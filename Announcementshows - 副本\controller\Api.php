<?php

namespace plugin\Announcementshows\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use app\common\library\Auth;

class Api extends BasePlugin {

    protected $scene = ['admin'];
    protected $noNeedLogin = ['fetchData'];


    public function index() {
        return View::fetch();
    }

    // 获取公告配置
    public function fetchData() {
        try {
            // 检查插件是否启用
            $status = plugconf('Announcementshows.status');
            if (!$status) {
                return json(['code' => 200, 'data' => ['status' => 0]]);
            }

            // 从设置中获取公告配置
            $config = [
                'status' => plugconf('Announcementshows.status'),
                'display_mode' => intval(plugconf('Announcementshows.display_mode') ?? 0),
                'height' => intval(plugconf('Announcementshows.height') ?? 60),
                'padding' => intval(plugconf('Announcementshows.padding') ?? 8),
                'background' => plugconf('Announcementshows.background') ?? '#f5f7fa',
                'text_color' => plugconf('Announcementshows.text_color') ?? '#333333',
                'allow_merchant_edit' => intval(plugconf('Announcementshows.allow_merchant_edit') ?? 1),
                'empty_ad_text' => plugconf('Announcementshows.empty_ad_text') ?? '广告位{position}空闲中',
                'empty_ad_image' => plugconf('Announcementshows.empty_ad_image') ?? '/static/plugins/Announcementshows/img/empty.png',
                'month_price' => floatval(plugconf('Announcementshows.month_price') ?? 19.9),
                'quarter_price' => floatval(plugconf('Announcementshows.quarter_price') ?? 49.9),
                'halfyear_price' => floatval(plugconf('Announcementshows.halfyear_price') ?? 89.9),
                'year_price' => floatval(plugconf('Announcementshows.year_price') ?? 169.9),
                'renew_month_price' => floatval(plugconf('Announcementshows.renew_month_price') ?? 17.9),
                'renew_quarter_price' => floatval(plugconf('Announcementshows.renew_quarter_price') ?? 44.9),
                'renew_halfyear_price' => floatval(plugconf('Announcementshows.renew_halfyear_price') ?? 79.9),
                'renew_year_price' => floatval(plugconf('Announcementshows.renew_year_price') ?? 149.9),
                'rent_link' => plugconf('Announcementshows.rent_link') ?? '',
                'auto_scroll' => intval(plugconf('Announcementshows.auto_scroll') ?? 1),
                'scroll_interval' => intval(plugconf('Announcementshows.scroll_interval') ?? 5000),
                'wrap' => intval(plugconf('Announcementshows.wrap') ?? 0),
                // 新增手机端配置
                'mobile_enabled' => intval(plugconf('Announcementshows.mobile_enabled') ?? 1),
                'mobile_display_mode' => intval(plugconf('Announcementshows.mobile_display_mode') ?? 0),
                'mobile_template1_enabled' => intval(plugconf('Announcementshows.mobile_template1_enabled') ?? 1),
                'mobile_template2_enabled' => intval(plugconf('Announcementshows.mobile_template2_enabled') ?? 1),
                'mobile_template3_enabled' => intval(plugconf('Announcementshows.mobile_template3_enabled') ?? 1),
                'mobile_current_template' => intval(plugconf('Announcementshows.mobile_current_template') ?? 1),
                'mobile_height' => intval(plugconf('Announcementshows.mobile_height') ?? 60),
                'mobile_padding' => intval(plugconf('Announcementshows.mobile_padding') ?? 10),
                'mobile_background' => plugconf('Announcementshows.mobile_background') ?? '#FFFFFF',
                'mobile_text_color' => plugconf('Announcementshows.mobile_text_color') ?? '#333333',
                'mobile_template1_style' => plugconf('Announcementshows.mobile_template1_style') ?? 'banner',
                'mobile_template2_style' => plugconf('Announcementshows.mobile_template2_style') ?? 'marquee',
                'mobile_template3_style' => plugconf('Announcementshows.mobile_template3_style') ?? 'color_cards',
                'gradient_cards' => intval(plugconf('Announcementshows.gradient_cards') ?? 1),
                // 自动删除过期公告配置
                'auto_delete_enabled' => intval(plugconf('Announcementshows.auto_delete_enabled') ?? 0),
                'grace_period_days' => intval(plugconf('Announcementshows.grace_period_days') ?? 3),
                'last_cleanup_time' => intval(plugconf('Announcementshows.last_cleanup_time') ?? 0),
                // 通知配置
                'notification_enabled' => intval(plugconf('Announcementshows.notification_enabled') ?? 0),
                'notification_type' => plugconf('Announcementshows.notification_type') ?? 'email', // email 或 sms
                'notification_days' => intval(plugconf('Announcementshows.notification_days') ?? 1), // 提前几天通知
                'sms_event_code' => plugconf('Announcementshows.sms_event_code') ?? 'ad_expire', // 短信场景编码
            ];

            // 获取公告内容
            $announcements = json_decode(plugconf('Announcementshows.announcements') ?? '[]', true);
            
            // 确保announcements是一个数组
            if (!is_array($announcements)) {
                $announcements = [];
            }
            
            // 处理每个公告项
            foreach ($announcements as $key => $item) {
                // 为每个广告位设置用户名
                if (!empty($item['user_id']) && empty($item['username'])) {
                    $user = \think\facade\Db::name('user')
                        ->where('id', $item['user_id'])
                        ->field('id, username, nickname, create_time')
                        ->find();
                    
                    if ($user) {
                        $announcements[$key]['username'] = $user['username'];
                        $announcements[$key]['nickname'] = $user['nickname'] ?? '';
                    }
                }
                
                // 确保图片模式下的图片URL正确
                if ($config['display_mode'] == 1 && !empty($item['content'])) {
                    // 添加域名前缀确保完整URL
                    $content = $item['content'];
                    // 如果不是以http开头也不是以/开头，则添加/前缀
                    if (!preg_match('/^(https?:\/\/|\/)/i', $content)) {
                        $content = '/' . $content;
                    }
                    $announcements[$key]['content'] = $content;
                    
                    // 同时保存一个image_url字段，作为备选
                    $announcements[$key]['image_url'] = $content;
                }
                
                // 确保链接字段存在
                if (!isset($item['link']) && isset($item['link_url'])) {
                    $announcements[$key]['link'] = $item['link_url'];
                } else if (!isset($item['link_url']) && isset($item['link'])) {
                    $announcements[$key]['link_url'] = $item['link'];
                }
                
                // 确保颜色字段存在
                if (!isset($item['textColor'])) {
                    $announcements[$key]['textColor'] = $config['text_color'];
                }
                if (!isset($item['backgroundColor'])) {
                    $announcements[$key]['backgroundColor'] = $config['background'];
                }
                
                // 处理渐变色背景相关字段
                $announcements[$key]['useGradient'] = isset($item['useGradient']) ? (bool)$item['useGradient'] : false;
                if (isset($item['gradientColor']) && !empty($item['gradientColor'])) {
                    $announcements[$key]['gradientColor'] = xss_safe($item['gradientColor']);
                } else {
                    $announcements[$key]['gradientColor'] = '';
                }

                // 处理文字渐变相关字段
                $announcements[$key]['useTextGradient'] = isset($item['useTextGradient']) ? (bool)$item['useTextGradient'] : false;
                if (isset($item['textGradientColor']) && !empty($item['textGradientColor'])) {
                    $announcements[$key]['textGradientColor'] = xss_safe($item['textGradientColor']);
                } else {
                    $announcements[$key]['textGradientColor'] = '';
                }
            }
            
            $config['announcements'] = json_encode($announcements);
            
            return json(['code' => 200, 'data' => $config]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '获取数据失败: ' . $e->getMessage(), 'type' => 'error']);
        }
    }

    // 保存公告配置
    public function save() {
        if (!$this->request->isPost()) {
            return json(['code' => 405, 'msg' => '请求方法不允许']);
        }

        try {
            // 获取并验证基本参数
            $status = $this->request->post('status/d', 1);
            $display_mode = $this->request->post('display_mode/d', 0);
            $background = $this->request->post('background', '#fff', 'trim');
            $text_color = $this->request->post('text_color', '#333', 'trim');
            $height = $this->request->post('height/d', 60);
            if ($height < 30 || $height > 500) { // 设置合理的高度范围
                return json(['code' => 400, 'msg' => '高度必须在30-500像素之间']);
            }
            $padding = $this->request->post('padding/d', 15);
            $wrap = $this->request->post('wrap/d', 0);
            $announcements = $this->request->post('announcements', '[]', 'trim');
            
            // 获取开通价格和续费价格
            $month_price = $this->request->post('month_price/f', 100);
            $quarter_price = $this->request->post('quarter_price/f', 270);
            $halfyear_price = $this->request->post('halfyear_price/f', 540);
            $year_price = $this->request->post('year_price/f', 1000);
            $renew_month_price = $this->request->post('renew_month_price/f', 80);
            $renew_quarter_price = $this->request->post('renew_quarter_price/f', 216);
            $renew_halfyear_price = $this->request->post('renew_halfyear_price/f', 432);
            $renew_year_price = $this->request->post('renew_year_price/f', 800);
            
            // 获取手机端广告模板相关配置
            $mobile_enabled = $this->request->post('mobile_enabled/d', 1);
            $mobile_display_mode = $this->request->post('mobile_display_mode/d', 0);
            $mobile_template1_enabled = $this->request->post('mobile_template1_enabled/d', 1);
            $mobile_template2_enabled = $this->request->post('mobile_template2_enabled/d', 1);
            $mobile_template3_enabled = $this->request->post('mobile_template3_enabled/d', 1);
            $mobile_current_template = $this->request->post('mobile_current_template/d', 1);
            $mobile_height = $this->request->post('mobile_height/d', 60);
            $mobile_padding = $this->request->post('mobile_padding/d', 10);
            $mobile_background = $this->request->post('mobile_background', '#FFFFFF', 'trim');
            $mobile_text_color = $this->request->post('mobile_text_color', '#333333', 'trim');
            $mobile_template1_style = $this->request->post('mobile_template1_style', 'banner', 'trim');
            $mobile_template2_style = $this->request->post('mobile_template2_style', 'marquee', 'trim');
            $mobile_template3_style = $this->request->post('mobile_template3_style', 'color_cards', 'trim');
            $gradient_cards = $this->request->post('gradient_cards/d', 1);

            // 获取自动删除配置
            $auto_delete_enabled = $this->request->post('auto_delete_enabled/d', 0);
            $grace_period_days = $this->request->post('grace_period_days/d', 3);

            // 获取通知配置
            $notification_enabled = $this->request->post('notification_enabled/d', 0);
            $notification_type = $this->request->post('notification_type', 'email', 'trim');
            $notification_days = $this->request->post('notification_days/d', 1);
            $sms_event_code = $this->request->post('sms_event_code', 'ad_expire', 'trim');
            
            // 验证手机端广告模板参数
            if ($mobile_height < 30 || $mobile_height > 500) {
                return json(['code' => 400, 'msg' => '手机端广告高度必须在30-500像素之间']);
            }
            
            if (!in_array($mobile_template1_style, ['banner', 'card', 'simple'])) {
                $mobile_template1_style = 'banner';
            }
            
            if (!in_array($mobile_template2_style, ['marquee', 'fade', 'slide'])) {
                $mobile_template2_style = 'marquee';
            }
            
            // 检查模板3样式
            if (!in_array($mobile_template3_style, ['color_cards', 'dark_mode', 'light_mode'])) {
                $mobile_template3_style = 'color_cards';
            }

            // 验证自动删除配置
            if ($grace_period_days < 0 || $grace_period_days > 365) {
                return json(['code' => 400, 'msg' => '宽限期天数必须在0-365天之间']);
            }

            // 验证通知配置
            if ($notification_days < 1 || $notification_days > 30) {
                return json(['code' => 400, 'msg' => '通知提前天数必须在1-30天之间']);
            }

            if (!in_array($notification_type, ['email', 'sms'])) {
                return json(['code' => 400, 'msg' => '通知类型只能是邮件或短信']);
            }
            
            // 验证价格
            if ($month_price < 0 || $quarter_price < 0 || $halfyear_price < 0 || $year_price < 0 ||
                $renew_month_price < 0 || $renew_quarter_price < 0 || $renew_halfyear_price < 0 || $renew_year_price < 0) {
                return json(['code' => 400, 'msg' => '价格不能为负数']);
            }
            
            // 验证公告数据格式
            $announcementData = json_decode($announcements, true);
            if (!is_array($announcementData)) {
                return json(['code' => 400, 'msg' => '公告数据格式不正确']);
            }

            // 验证图片模式下的数据
            if ($display_mode === 1) {
                foreach ($announcementData as $item) {
                    if (!empty($item['content'])) {
                        // 允许相对路径和绝对路径的URL
                        if (!preg_match('/^(https?:\/\/|\/)[^\s]*$/i', $item['content'])) {
                            return json(['code' => 400, 'msg' => '无效的图片URL格式']);
                        }
                    }
                }
            }

            // 验证文字模式下的数据
            if ($display_mode === 0) {
                foreach ($announcementData as $item) {
                    if (empty($item['content']) || !is_string($item['content'])) {
                        return json(['code' => 400, 'msg' => '广告内容不能为空']);
                    }
                }
            }

            // 确保每个广告位都有颜色设置和渐变设置
            foreach ($announcementData as &$item) {
                if (!isset($item['textColor'])) {
                    $item['textColor'] = $text_color;
                }
                if (!isset($item['backgroundColor'])) {
                    $item['backgroundColor'] = $background;
                }

                // 确保背景渐变字段存在
                if (!isset($item['useGradient'])) {
                    $item['useGradient'] = false;
                }
                if (!isset($item['gradientColor'])) {
                    $item['gradientColor'] = '';
                }

                // 确保文字渐变字段存在
                if (!isset($item['useTextGradient'])) {
                    $item['useTextGradient'] = false;
                }
                if (!isset($item['textGradientColor'])) {
                    $item['textGradientColor'] = '';
                }
            }
            unset($item);

            $announcements = json_encode($announcementData);

            // 严格限制广告位数量为20个
            if (count($announcementData) > 20) {
                return json(['code' => 400, 'msg' => '最多只能添加20个广告位']);
            }

            // 确保至少有一个广告位
            if (count($announcementData) === 0) {
                return json(['code' => 400, 'msg' => '至少需要一个广告位']);
            }

            // 验证每条公告的结束时间
            foreach ($announcementData as $item) {
                if (!empty($item['end_time']) && !strtotime($item['end_time'])) {
                    return json(['code' => 400, 'msg' => '结束时间格式不正确']);
                }
            }

            // 保存所有配置
            plugconf("Announcementshows.status", $status);
            plugconf("Announcementshows.display_mode", $display_mode);
            plugconf("Announcementshows.background", $background);
            plugconf("Announcementshows.text_color", $text_color);
            plugconf("Announcementshows.height", $height);
            plugconf("Announcementshows.padding", $padding);
            plugconf("Announcementshows.wrap", $wrap);
            plugconf("Announcementshows.announcements", $announcements);
            plugconf("Announcementshows.month_price", $month_price);
            plugconf("Announcementshows.quarter_price", $quarter_price);
            plugconf("Announcementshows.halfyear_price", $halfyear_price);
            plugconf("Announcementshows.year_price", $year_price);
            plugconf("Announcementshows.renew_month_price", $renew_month_price);
            plugconf("Announcementshows.renew_quarter_price", $renew_quarter_price);
            plugconf("Announcementshows.renew_halfyear_price", $renew_halfyear_price);
            plugconf("Announcementshows.renew_year_price", $renew_year_price);

            $auto_scroll = $this->request->post('auto_scroll/d', 1);
            plugconf("Announcementshows.auto_scroll", $auto_scroll);

            $scroll_interval = $this->request->post('scroll_interval/d', 5000);
            // 验证间隔时间范围（1-30秒）
            if ($scroll_interval < 1000 || $scroll_interval > 30000) {
                return json(['code' => 400, 'msg' => '轮播间隔时间必须在1-30秒之间']);
            }
            plugconf("Announcementshows.scroll_interval", $scroll_interval);

            // 保存租用链接
            $rent_link = $this->request->post('rent_link', '', 'trim');
            plugconf("Announcementshows.rent_link", $rent_link);

            // 保存是否允许商户自定义广告内容
            $allow_merchant_edit = $this->request->post('allow_merchant_edit/d', 1);
            plugconf("Announcementshows.allow_merchant_edit", $allow_merchant_edit);
            
            // 同步广告内容到各个用户的配置中
            try {
                // 获取所有广告位涉及的用户ID
                $userIds = [];
                foreach ($announcementData as $ad) {
                    if (!empty($ad['user_id'])) {
                        $userIds[] = $ad['user_id'];
                    }
                }
                
                // 为每个用户同步配置
                if (!empty($userIds)) {
                    $userIds = array_unique($userIds);
                    foreach ($userIds as $userId) {
                        // 获取用户相关的广告位
                        $userAds = [];
                        foreach ($announcementData as $index => $ad) {
                            if (isset($ad['user_id']) && $ad['user_id'] == $userId) {
                                $userAds[$index] = $ad;
                            }
                        }
                        
                        if (!empty($userAds)) {
                            // 更新用户配置
                            merchant_plugconf($userId, "Announcementshows.announcements", $announcements);
                        }
                    }
                }
            } catch (\Exception $syncError) {
                // 不中断保存过程，只静默处理异常
            }

            // 保存手机端配置
            plugconf('Announcementshows.mobile_enabled', $mobile_enabled);
            plugconf('Announcementshows.mobile_display_mode', $mobile_display_mode);
            plugconf('Announcementshows.mobile_template1_enabled', $mobile_template1_enabled);
            plugconf('Announcementshows.mobile_template2_enabled', $mobile_template2_enabled);
            plugconf('Announcementshows.mobile_template3_enabled', $mobile_template3_enabled);
            plugconf('Announcementshows.mobile_current_template', $mobile_current_template);
            plugconf('Announcementshows.mobile_height', $mobile_height);
            plugconf('Announcementshows.mobile_padding', $mobile_padding);
            plugconf('Announcementshows.mobile_background', $mobile_background);
            plugconf('Announcementshows.mobile_text_color', $mobile_text_color);
            plugconf('Announcementshows.mobile_template1_style', $mobile_template1_style);
            plugconf('Announcementshows.mobile_template2_style', $mobile_template2_style);
            plugconf('Announcementshows.mobile_template3_style', $mobile_template3_style);
            plugconf('Announcementshows.gradient_cards', $gradient_cards);

            // 保存自动删除配置
            plugconf('Announcementshows.auto_delete_enabled', $auto_delete_enabled);
            plugconf('Announcementshows.grace_period_days', $grace_period_days);

            // 保存通知配置
            plugconf('Announcementshows.notification_enabled', $notification_enabled);
            plugconf('Announcementshows.notification_type', $notification_type);
            plugconf('Announcementshows.notification_days', $notification_days);
            plugconf('Announcementshows.sms_event_code', $sms_event_code);

            return json(['code' => 200, 'msg' => '保存成功', 'data' => ['reload' => true]]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '保存失败: ' . $e->getMessage(), 'type' => 'error']);
        }
    }

    /**
     * 手动清理过期公告
     */
    public function cleanupExpiredAnnouncements() {
        if (!$this->request->isPost()) {
            return json(['code' => 405, 'msg' => '请求方法不允许']);
        }

        try {
            // 获取是否强制清理参数
            $forceCleanup = $this->request->post('force_cleanup/d', 0);

            // 获取宽限期天数配置
            $gracePeriodDays = intval(plugconf("Announcementshows.grace_period_days") ?? 3);
            if ($gracePeriodDays < 0) {
                $gracePeriodDays = 3; // 默认3天
            }

            // 获取当前公告配置
            $announcements = json_decode(plugconf("Announcementshows.announcements") ?? '[]', true);
            if (!is_array($announcements) || empty($announcements)) {
                return json(['code' => 200, 'msg' => '没有需要清理的公告', 'data' => ['deleted_count' => 0]]);
            }

            $currentTime = time();
            $hasChanges = false;
            $deletedCount = 0;
            $deletedList = [];
            $debugInfo = [];

            // 检查每个公告是否需要删除
            foreach ($announcements as $index => &$announcement) {
                // 只处理有到期时间的公告
                if (empty($announcement['end_time'])) {
                    $debugInfo[] = "位置" . ($index + 1) . ": 无到期时间，跳过";
                    continue;
                }

                $endTime = strtotime($announcement['end_time']);
                if (!$endTime) {
                    $debugInfo[] = "位置" . ($index + 1) . ": 到期时间格式错误，跳过";
                    continue;
                }

                // 计算宽限期结束时间
                $graceEndTime = $endTime + ($gracePeriodDays * 86400); // 86400秒 = 1天

                // 添加调试信息
                $debugInfo[] = "位置" . ($index + 1) . ": 到期时间=" . date('Y-m-d H:i:s', $endTime) .
                              ", 宽限期结束=" . date('Y-m-d H:i:s', $graceEndTime) .
                              ", 当前时间=" . date('Y-m-d H:i:s', $currentTime);

                // 判断是否需要删除：强制清理模式或者超过宽限期
                $shouldDelete = $forceCleanup || ($currentTime > $graceEndTime);

                if ($shouldDelete) {
                    $username = $announcement['username'] ?? '未知用户';
                    $position = $index + 1;

                    $deletedList[] = [
                        'position' => $position,
                        'username' => $username,
                        'end_time' => $announcement['end_time'],
                        'content' => mb_substr($announcement['content'] ?? '', 0, 50) . '...'
                    ];

                    // 清空公告内容，但保留位置结构
                    $announcement['content'] = '';
                    $announcement['link'] = '';
                    $announcement['user_id'] = '';
                    $announcement['username'] = '';
                    $announcement['nickname'] = '';
                    $announcement['end_time'] = '';
                    $announcement['backgroundColor'] = plugconf('Announcementshows.background') ?? '#f5f7fa';
                    $announcement['textColor'] = plugconf('Announcementshows.text_color') ?? '#333333';
                    $announcement['useGradient'] = false;
                    $announcement['gradientColor'] = '';
                    $announcement['useTextGradient'] = false;
                    $announcement['textGradientColor'] = '';
                    $announcement['image_url'] = '';

                    $hasChanges = true;
                    $deletedCount++;
                }
            }
            unset($announcement);

            // 如果有变更，保存配置
            if ($hasChanges) {
                plugconf("Announcementshows.announcements", json_encode($announcements));

                // 记录操作日志
                error_log("Announcementshows: 手动清理过期公告完成，共删除 {$deletedCount} 个过期公告");
            }

            // 更新最后清理时间
            plugconf("Announcementshows.last_cleanup_time", $currentTime);

            return json([
                'code' => 200,
                'msg' => $deletedCount > 0 ? "成功清理 {$deletedCount} 个过期公告" : "没有需要清理的过期公告",
                'data' => [
                    'deleted_count' => $deletedCount,
                    'deleted_list' => $deletedList,
                    'grace_period_days' => $gracePeriodDays,
                    'force_cleanup' => $forceCleanup,
                    'debug_info' => $debugInfo,
                    'current_time' => date('Y-m-d H:i:s', $currentTime)
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '清理失败: ' . $e->getMessage(), 'type' => 'error']);
        }
    }
}