/**
 * 广告位管理客户端脚本
 * 提供广告位租用、编辑、续费等功能
 */

(function() {
    // 优先定义公共方法以确保早期可用
    window.announcementShowsRentDialog = function(position, isRenewal = false) {
        // 如果配置还没有加载，先初始化
        if (!config.initialized) {
            init();
        }
        createRentDialog(position, isRenewal);
    };

    window.announcementShowsEditPage = function(position) {
        // 如果配置还没有加载，先初始化
        if (!config.initialized) {
            init();
        }
        
        if (position) {
            // 在显示编辑界面前，先检查用户是否已经租用了该广告位
            checkAdOwnership(position).then(owned => {
                if (owned) {
                    showEditIframe(position);
                } else {
                    // 如果未租用，提示用户需要先租用广告位
                    showRentRequiredDialog(position);
                }
            }).catch(() => {
                // 发生错误时，默认显示租用对话框
                createRentDialog(position, false);
            });
        } else {
            openEditPage();
        }
    };

    // 配置信息
    const config = {
        apiBase: '/plugin/Announcementshows/User/',
        editPage: '/plugin/Announcementshows/User/edit',
        dialogZIndex: 9999,
        priceConfig: null, // 添加价格配置存储
        initialized: false  // 标记是否已初始化
    };

    // 对话框样式
    const dialogStyles = `
        .announcement-dialog-mask {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: ${config.dialogZIndex};
            display: flex;
            align-items: center;
            justify-content: center;
            animation: fadeIn 0.2s ease-in-out;
        }
        .announcement-dialog {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            width: 90%;
            max-width: 460px;
            padding: 0;
            overflow: hidden;
            animation: scaleIn 0.2s ease-in-out;
        }
        .announcement-dialog-header {
            background: #f5f7fa;
            padding: 15px 20px;
            border-bottom: 1px solid #ebeef5;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .announcement-dialog-title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin: 0;
        }
        .announcement-dialog-close {
            cursor: pointer;
            font-size: 18px;
            color: #909399;
            background: none;
            border: none;
            padding: 0;
        }
        .announcement-dialog-body {
            padding: 20px;
            max-height: 70vh;
            overflow-y: auto;
        }
        .announcement-dialog-footer {
            padding: 10px 20px 20px;
            text-align: right;
            border-top: 1px solid #ebeef5;
        }
        .announcement-btn {
            padding: 8px 15px;
            border-radius: 4px;
            border: 1px solid #dcdfe6;
            background: #fff;
            cursor: pointer;
            outline: none;
            margin-left: 10px;
            transition: all 0.3s;
        }
        .announcement-btn-default {
            color: #606266;
        }
        .announcement-btn-default:hover {
            color: #409eff;
            border-color: #c6e2ff;
            background-color: #ecf5ff;
        }
        .announcement-btn-primary {
            color: #fff;
            background-color: #409eff;
            border-color: #409eff;
        }
        .announcement-btn-primary:hover {
            background: #66b1ff;
            border-color: #66b1ff;
        }
        .announcement-form-item {
            margin-bottom: 20px;
        }
        .announcement-form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: #606266;
        }
        .announcement-form-select {
            width: 100%;
            padding: 8px 10px;
            border-radius: 4px;
            border: 1px solid #dcdfe6;
            background-color: #fff;
            line-height: 1.5;
        }
        .announcement-form-price {
            font-size: 18px;
            color: #f56c6c;
            margin-top: 10px;
            text-align: center;
            font-weight: bold;
        }
        .announcement-form-summary {
            margin-top: 15px;
            padding: 10px;
            background: #f8f8f8;
            border-radius: 4px;
            font-size: 14px;
            color: #606266;
        }
        .announcement-form-balance {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
            font-size: 14px;
            color: #606266;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        @keyframes scaleIn {
            from { transform: scale(0.9); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
        }
    `;

    // 注入样式
    function injectStyles() {
        const styleEl = document.createElement('style');
        styleEl.type = 'text/css';
        styleEl.innerHTML = dialogStyles;
        document.head.appendChild(styleEl);
    }

    // 创建租用对话框
    function createRentDialog(position, isRenewal = false) {
        // 创建遮罩层
        const mask = document.createElement('div');
        mask.className = 'announcement-dialog-mask';
        
        // 创建对话框
        const dialog = document.createElement('div');
        dialog.className = 'announcement-dialog';
        
        // 对话框头部
        const header = document.createElement('div');
        header.className = 'announcement-dialog-header';
        
        const title = document.createElement('h3');
        title.className = 'announcement-dialog-title';
        title.textContent = isRenewal ? '续费广告位' : '租用广告位';
        
        const closeBtn = document.createElement('button');
        closeBtn.className = 'announcement-dialog-close';
        closeBtn.textContent = '×';
        closeBtn.onclick = () => mask.remove();
        
        header.appendChild(title);
        header.appendChild(closeBtn);
        
        // 对话框内容
        const body = document.createElement('div');
        body.className = 'announcement-dialog-body';
        
        // 表单内容
        const formHTML = `
            <div class="announcement-form-item">
                <label class="announcement-form-label">选择${isRenewal ? '续费' : '租用'}时长:</label>
                <select id="announcement-duration" class="announcement-form-select">
                    <option value="30">包月（30天）</option>
                    <option value="90">包季（90天）</option>
                    <option value="180">半年（180天）</option>
                    <option value="365">包年（365天）</option>
                </select>
            </div>
            <div class="announcement-form-price">
                ￥<span id="announcement-price">0.00</span>
            </div>
            <div class="announcement-form-summary">
                <div>广告位: #<span id="announcement-position">${position}</span></div>
                <div>时长: <span id="announcement-days">30</span>天</div>
                <div class="announcement-form-balance">
                    <span>账户余额:</span>
                    <span id="announcement-balance">加载中...</span>
                </div>
            </div>
        `;
        
        body.innerHTML = formHTML;
        
        // 对话框底部
        const footer = document.createElement('div');
        footer.className = 'announcement-dialog-footer';
        
        const cancelBtn = document.createElement('button');
        cancelBtn.className = 'announcement-btn announcement-btn-default';
        cancelBtn.textContent = '取消';
        cancelBtn.onclick = () => {
            handleCancelRent(position);
            mask.remove();
        };
        
        const confirmBtn = document.createElement('button');
        confirmBtn.className = 'announcement-btn announcement-btn-primary';
        confirmBtn.textContent = '确认支付';
        confirmBtn.onclick = () => handleRentConfirm(position, isRenewal);
        
        footer.appendChild(cancelBtn);
        footer.appendChild(confirmBtn);
        
        // 组装对话框
        dialog.appendChild(header);
        dialog.appendChild(body);
        dialog.appendChild(footer);
        
        mask.appendChild(dialog);
        document.body.appendChild(mask);
        
        // 加载价格和余额信息
        loadPriceInfo(position, isRenewal);
        
        // 监听时长变化更新价格
        const durationSelect = document.getElementById('announcement-duration');
        durationSelect.addEventListener('change', () => {
            updatePrice(position, durationSelect.value, isRenewal);
        });
    }

    // 加载价格和余额信息
    async function loadPriceInfo(position = null, isRenewal = false) {
        try {
            // 获取价格和余额数据
            const response = await fetch(config.apiBase + 'getPriceConfig', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                credentials: 'same-origin'
            });
            
            const result = await response.json();
            
            if (result.code === 200 && result.data) {
                // 保存价格配置到config对象
                config.priceConfig = result.data;
                
                // 如果提供了position参数，更新UI
                if (position !== null) {
                    const durationSelect = document.getElementById('announcement-duration');
                    if (durationSelect) {
                        updatePrice(position, durationSelect.value, isRenewal);
                    }
                    
                    // 显示用户余额
                    const balanceElement = document.getElementById('announcement-balance');
                    if (balanceElement && result.data.user_money !== undefined) {
                        balanceElement.textContent = '￥' + parseFloat(result.data.user_money).toFixed(2);
                    }
                }
                
                return result.data;
            } else {
                // 如果获取失败但提供了position参数，显示提示信息
                if (position !== null) {
                    const balanceElement = document.getElementById('announcement-balance');
                    if (balanceElement) {
                        balanceElement.textContent = '获取失败';
                    }
                }
                throw new Error(result.msg || '获取价格配置失败');
            }
        } catch (error) {
            const balanceElement = document.getElementById('announcement-balance');
            if (balanceElement) {
                balanceElement.textContent = '获取失败';
            }
            throw error;
        }
    }

    // 更新价格显示
    function updatePrice(position, duration, isRenewal) {
        let price = 0;
        
        // 如果已加载价格配置，则使用配置中的价格
        if (config.priceConfig) {
            switch (parseInt(duration)) {
                case 30:
                    price = isRenewal ? config.priceConfig.renew_month_price : config.priceConfig.month_price;
                    break;
                case 90:
                    price = isRenewal ? config.priceConfig.renew_quarter_price : config.priceConfig.quarter_price;
                    break;
                case 180:
                    price = isRenewal ? config.priceConfig.renew_halfyear_price : config.priceConfig.halfyear_price;
                    break;
                case 365:
                    price = isRenewal ? config.priceConfig.renew_year_price : config.priceConfig.year_price;
                    break;
            }
        } else {
            // 如果未加载价格配置（作为临时备用），使用预设值
            const priceMap = {
                regular: {
                    30: 19.9,
                    90: 49.9,
                    180: 89.9,
                    365: 169.9
                },
                renewal: {
                    30: 17.9,
                    90: 44.9,
                    180: 79.9,
                    365: 149.9
                }
            };
            
            const prices = isRenewal ? priceMap.renewal : priceMap.regular;
            price = prices[duration] || 0;
        }
        
        const priceElement = document.getElementById('announcement-price');
        const daysElement = document.getElementById('announcement-days');
        
        priceElement.textContent = parseFloat(price).toFixed(2);
        daysElement.textContent = duration;
    }

    // 处理租用确认
    async function handleRentConfirm(position, isRenewal) {
        try {
            const durationSelect = document.getElementById('announcement-duration');
            const duration = durationSelect.value;
            const price = document.getElementById('announcement-price').textContent;
            
            // 调用API进行实际租用
            const response = await fetch(config.apiBase + 'openAdSpace', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    position: position,
                    duration: duration,
                    price: price,
                    is_renew: isRenewal ? 1 : 0
                })
            });
            
            const result = await response.json();
            
            if (result.code === 200) {
                // 替换原来的alert为自定义美观的成功提示框
                showSuccessDialog(isRenewal ? '续费成功！' : '租用成功！', position);
                // 成功后关闭对话框
                const mask = document.querySelector('.announcement-dialog-mask');
                if (mask) {
                    mask.remove();
                }
                
                // 发送消息通知父窗口刷新数据
                if (window.parent !== window) {
                    window.parent.postMessage({
                        type: 'REFRESH_ADS_DATA',
                        position: position
                    }, '*');
                }
                
                // 如果在编辑页面，刷新数据
                if (window.location.href.includes('/edit') && typeof fetchMyAds === 'function') {
                    try {
                        fetchMyAds();
                    } catch (e) {
                        console.log('非编辑页面环境，将打开编辑界面');
                    }
                }
            } else {
                // 显示错误提示
                showErrorDialog('操作失败', result.msg || '未知错误');
            }
        } catch (error) {
            console.error('租用/续费操作失败:', error);
            showErrorDialog('操作失败', '系统错误，请稍后重试');
        }
    }
    
    // 显示成功提示对话框
    function showSuccessDialog(message, position) {
        // 创建遮罩层
        const mask = document.createElement('div');
        mask.className = 'announcement-success-mask';
        
        // 创建对话框
        const dialog = document.createElement('div');
        dialog.className = 'announcement-success-dialog';
        
        // 添加内容
        const iconContainer = document.createElement('div');
        iconContainer.className = 'announcement-success-icon';
        iconContainer.innerHTML = '<svg viewBox="0 0 52 52" xmlns="http://www.w3.org/2000/svg"><path d="M26,0C11.664,0,0,11.663,0,26s11.664,26,26,26s26-11.663,26-26S40.336,0,26,0z M40.495,17.329l-16,18 c-0.347,0.391-0.841,0.614-1.36,0.614c-0.001,0-0.002,0-0.003,0c-0.521-0.001-1.016-0.226-1.362-0.621l-8-9 c-0.695-0.781-0.624-1.976,0.157-2.671c0.782-0.695,1.976-0.624,2.672,0.157l6.498,7.31l14.63-16.458 c0.678-0.766,1.846-0.837,2.612-0.159C41.102,15.396,41.174,16.564,40.495,17.329z" fill="#67C23A"/></svg>';
        
        const textContent = document.createElement('div');
        textContent.className = 'announcement-success-content';
        textContent.innerHTML = `
            <h3>${message}</h3>
            <p>您可以立即开始编辑广告内容。</p>
        `;
        
        const buttonContainer = document.createElement('div');
        buttonContainer.className = 'announcement-success-buttons';
        
        const confirmBtn = document.createElement('button');
        confirmBtn.className = 'announcement-success-btn';
        confirmBtn.textContent = '确定';
        confirmBtn.onclick = () => {
            mask.remove();
            
            // 尝试刷新编辑页面数据（如果在编辑页面）
            if (window.location.href.includes('/edit') && typeof fetchMyAds === 'function') {
                try {
                    fetchMyAds();
                } catch (e) {
                    console.log('非编辑页面环境，将打开编辑界面');
                }
            }
            
            // 显示编辑界面
            showEditIframe(position);
        };
        
        // 组装对话框
        buttonContainer.appendChild(confirmBtn);
        dialog.appendChild(iconContainer);
        dialog.appendChild(textContent);
        dialog.appendChild(buttonContainer);
        mask.appendChild(dialog);
        
        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .announcement-success-mask {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                animation: announcement-fade-in 0.3s ease forwards;
            }
            .announcement-success-dialog {
                background: #fff;
                border-radius: 12px;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
                width: 90%;
                max-width: 400px;
                padding: 25px;
                text-align: center;
                animation: announcement-pop-in 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
                transform: scale(0.9);
                opacity: 0;
            }
            .announcement-success-icon {
                width: 70px;
                height: 70px;
                margin: 0 auto 15px;
                animation: announcement-success-scale 0.5s cubic-bezier(0.54, 1.5, 0.38, 1.11) 0.2s forwards;
                transform: scale(0);
            }
            .announcement-success-content {
                margin-bottom: 20px;
            }
            .announcement-success-content h3 {
                font-size: 22px;
                color: #303133;
                margin: 0 0 8px;
            }
            .announcement-success-content p {
                font-size: 16px;
                color: #606266;
                margin: 0;
            }
            .announcement-success-buttons {
                display: flex;
                justify-content: center;
            }
            .announcement-success-btn {
                min-width: 120px;
                padding: 10px 20px;
                background: linear-gradient(to right, #67C23A, #85ce61);
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 16px;
                cursor: pointer;
                transition: all 0.3s;
                box-shadow: 0 2px 6px rgba(103, 194, 58, 0.3);
            }
            .announcement-success-btn:hover {
                background: linear-gradient(to right, #85ce61, #67C23A);
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(103, 194, 58, 0.4);
            }
            .announcement-success-btn:active {
                transform: translateY(1px);
            }
            @keyframes announcement-fade-in {
                from { opacity: 0; }
                to { opacity: 1; }
            }
            @keyframes announcement-pop-in {
                from { transform: scale(0.9); opacity: 0; }
                to { transform: scale(1); opacity: 1; }
            }
            @keyframes announcement-success-scale {
                from { transform: scale(0); }
                to { transform: scale(1); }
            }
        `;
        
        document.head.appendChild(style);
        document.body.appendChild(mask);
        
        // 添加点击背景关闭
        mask.addEventListener('click', (e) => {
            if (e.target === mask) {
                mask.remove();
                // 不应该自动显示编辑界面，这会导致未购买就显示编辑界面
                // showEditIframe(position);
            }
        });
    }
    
    // 显示错误提示对话框
    function showErrorDialog(title, message) {
        // 创建遮罩层
        const mask = document.createElement('div');
        mask.className = 'announcement-error-mask';
        
        // 创建对话框
        const dialog = document.createElement('div');
        dialog.className = 'announcement-error-dialog';
        
        // 添加内容
        const iconContainer = document.createElement('div');
        iconContainer.className = 'announcement-error-icon';
        iconContainer.innerHTML = '<svg viewBox="0 0 52 52" xmlns="http://www.w3.org/2000/svg"><path d="M26,0C11.664,0,0,11.663,0,26s11.664,26,26,26s26-11.663,26-26S40.336,0,26,0z M35.707,33.293 c0.391,0.391,0.391,1.023,0,1.414C35.512,34.902,35.256,35,35,35s-0.512-0.098-0.707-0.293L26,26.414l-8.293,8.293 C17.512,34.902,17.256,35,17,35s-0.512-0.098-0.707-0.293c-0.391-0.391-0.391-1.023,0-1.414L24.586,25l-8.293-8.293 c-0.391-0.391-0.391-1.023,0-1.414s1.023-0.391,1.414,0L26,23.586l8.293-8.293c0.391-0.391,1.023-0.391,1.414,0 s0.391,1.023,0,1.414L27.414,25L35.707,33.293z" fill="#F56C6C"/></svg>';
        
        const textContent = document.createElement('div');
        textContent.className = 'announcement-error-content';
        textContent.innerHTML = `
            <h3>${title}</h3>
            <p>${message}</p>
        `;
        
        const buttonContainer = document.createElement('div');
        buttonContainer.className = 'announcement-error-buttons';
        
        const confirmBtn = document.createElement('button');
        confirmBtn.className = 'announcement-error-btn';
        confirmBtn.textContent = '确定';
        confirmBtn.onclick = () => mask.remove();
        
        // 组装对话框
        buttonContainer.appendChild(confirmBtn);
        dialog.appendChild(iconContainer);
        dialog.appendChild(textContent);
        dialog.appendChild(buttonContainer);
        mask.appendChild(dialog);
        
        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .announcement-error-mask {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                animation: announcement-fade-in 0.3s ease forwards;
            }
            .announcement-error-dialog {
                background: #fff;
                border-radius: 12px;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
                width: 90%;
                max-width: 400px;
                padding: 25px;
                text-align: center;
                animation: announcement-shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
            }
            .announcement-error-icon {
                width: 70px;
                height: 70px;
                margin: 0 auto 15px;
            }
            .announcement-error-content {
                margin-bottom: 20px;
            }
            .announcement-error-content h3 {
                font-size: 22px;
                color: #F56C6C;
                margin: 0 0 8px;
            }
            .announcement-error-content p {
                font-size: 16px;
                color: #606266;
                margin: 0;
            }
            .announcement-error-buttons {
                display: flex;
                justify-content: center;
            }
            .announcement-error-btn {
                min-width: 120px;
                padding: 10px 20px;
                background: #F56C6C;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 16px;
                cursor: pointer;
                transition: all 0.3s;
                box-shadow: 0 2px 6px rgba(245, 108, 108, 0.3);
            }
            .announcement-error-btn:hover {
                background: #f78989;
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(245, 108, 108, 0.4);
            }
            .announcement-error-btn:active {
                transform: translateY(1px);
            }
            @keyframes announcement-shake {
                10%, 90% { transform: translate3d(-1px, 0, 0); }
                20%, 80% { transform: translate3d(2px, 0, 0); }
                30%, 50%, 70% { transform: translate3d(-4px, 0, 0); }
                40%, 60% { transform: translate3d(4px, 0, 0); }
            }
        `;
        
        document.head.appendChild(style);
        document.body.appendChild(mask);
        
        // 添加点击背景关闭
        mask.addEventListener('click', (e) => {
            if (e.target === mask) {
                mask.remove();
            }
        });
    }

    // 新增函数：显示编辑iframe弹窗
    function showEditIframe(position) {
        // 创建弹窗容器
        const modalContainer = document.createElement('div');
        modalContainer.className = 'announcement-iframe-modal';
        
        // 创建弹窗内容
        const modalContent = document.createElement('div');
        modalContent.className = 'announcement-iframe-content';
        
        // 创建标题栏
        const titleBar = document.createElement('div');
        titleBar.className = 'announcement-iframe-title';
        
        const title = document.createElement('h3');
        title.textContent = '编辑广告内容';
        title.className = 'announcement-iframe-title-text';
        
        const closeBtn = document.createElement('button');
        closeBtn.className = 'announcement-iframe-close';
        closeBtn.innerHTML = '&times;';
        closeBtn.onclick = () => {
            modalContainer.classList.add('announcement-fade-out');
            setTimeout(() => modalContainer.remove(), 300);
        };
        closeBtn.title = '关闭窗口';
        
        titleBar.appendChild(title);
        titleBar.appendChild(closeBtn);
        
        // 创建iframe
        const iframe = document.createElement('iframe');
        iframe.src = `/plugin/Announcementshows/User/edit?position=${position}&t=${new Date().getTime()}`;
        iframe.className = 'announcement-iframe';
        iframe.frameBorder = '0';
        
        // 添加加载指示器
        const loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'announcement-loading';
        loadingIndicator.innerHTML = '<div class="announcement-spinner"></div><p>加载中...</p>';
        modalContent.appendChild(loadingIndicator);
        
        // iframe加载完成后隐藏加载指示器
        iframe.onload = () => {
            loadingIndicator.style.display = 'none';
            
            // 尝试向iframe发送刷新消息
            try {
                iframe.contentWindow.postMessage({
                    type: 'REFRESH_ADS_DATA',
                    position: position
                }, '*');
            } catch (e) {
                console.log('无法向iframe发送刷新消息', e);
            }
        };
        
        // 组装弹窗
        modalContent.appendChild(titleBar);
        modalContent.appendChild(iframe);
        modalContainer.appendChild(modalContent);
        
        // 添加弹窗样式
        const style = document.createElement('style');
        style.textContent = `
            .announcement-iframe-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.6);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 9999;
                opacity: 0;
                animation: announcement-fade-in 0.3s ease forwards;
            }
            @keyframes announcement-fade-in {
                from { opacity: 0; }
                to { opacity: 1; }
            }
            .announcement-fade-out {
                animation: announcement-fade-out 0.3s ease forwards;
            }
            @keyframes announcement-fade-out {
                from { opacity: 1; }
                to { opacity: 0; }
            }
            .announcement-iframe-content {
                background: #fff;
                border-radius: 12px;
                width: 92%;
                height: 92%;
                max-width: 1300px;
                display: flex;
                flex-direction: column;
                overflow: hidden;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
                position: relative;
                transform: scale(0.95);
                animation: announcement-scale-in 0.3s ease forwards;
            }
            @keyframes announcement-scale-in {
                from { transform: scale(0.95); }
                to { transform: scale(1); }
            }
            .announcement-iframe-title {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 16px 24px;
                border-bottom: 1px solid #ebeef5;
                background: linear-gradient(to right, #f5f7fa, #e4e7ed);
            }
            .announcement-iframe-title-text {
                margin: 0;
                font-size: 18px;
                color: #303133;
                font-weight: 600;
            }
            .announcement-iframe-close {
                background: none;
                border: none;
                font-size: 28px;
                color: #909399;
                cursor: pointer;
                width: 36px;
                height: 36px;
                line-height: 32px;
                text-align: center;
                border-radius: 50%;
                transition: all 0.3s;
                background-color: rgba(245, 245, 245, 0.8);
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
            }
            .announcement-iframe-close:hover {
                background-color: #f56c6c;
                color: #fff;
                transform: rotate(90deg);
            }
            .announcement-iframe {
                flex: 1;
                width: 100%;
                height: 100%;
                border: none;
            }
            .announcement-loading {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                background-color: rgba(255, 255, 255, 0.9);
                z-index: 1;
            }
            .announcement-spinner {
                width: 50px;
                height: 50px;
                border: 5px solid #f3f3f3;
                border-top: 5px solid #409eff;
                border-radius: 50%;
                animation: announcement-spin 1s linear infinite;
                margin-bottom: 15px;
            }
            @keyframes announcement-spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            .announcement-loading p {
                font-size: 16px;
                color: #606266;
                margin: 0;
            }
        `;
        document.head.appendChild(style);
        
        // 添加到页面
        document.body.appendChild(modalContainer);
        
        // 添加点击背景关闭功能
        modalContainer.addEventListener('click', (e) => {
            if (e.target === modalContainer) {
                modalContainer.classList.add('announcement-fade-out');
                setTimeout(() => modalContainer.remove(), 300);
            }
        });
        
        // 添加ESC键关闭弹窗
        const handleEscKey = (e) => {
            if (e.key === 'Escape') {
                modalContainer.classList.add('announcement-fade-out');
                setTimeout(() => {
                    modalContainer.remove();
                    document.removeEventListener('keydown', handleEscKey);
                }, 300);
            }
        };
        document.addEventListener('keydown', handleEscKey);
    }

    // 修改打开编辑页面的函数
    function openEditPage() {
        // 如果有position参数，则使用弹窗显示
        const urlParams = new URLSearchParams(window.location.search);
        const position = urlParams.get('position');
        
        if (position) {
            showEditIframe(position);
        } else {
            window.location.href = config.editPage;
        }
    }

    // 初始化
    function init() {
        injectStyles();
        config.initialized = true;
        // 自动尝试加载价格信息
        loadPriceInfo().catch(error => {
            console.error('自动加载价格信息失败:', error);
        });
    }

    // 在DOM加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // 定期检查广告内容更新
    function setupAdRefresher() {
        // 每60秒检查一次广告内容更新
        function checkForUpdates() {
            try {
                // 获取当前时间
                const now = Math.floor(Date.now() / 1000);
                
                // 如果广告内容刚刚加载，不需要刷新
                if (window.lastAdLoadTime && now - window.lastAdLoadTime < 5) {
                    return;
                }
                
                // 请求新的广告数据
                fast.fetch({
                    url: '/addons/Announcementshows/api/fetchData',
                    type: 'get',
                    dataType: 'json',
                    success: function(res) {
                        if (res.code === 200 && res.data) {
                            // 检查数据是否有变化
                            const currentData = JSON.stringify(res.data);
                            if (window.lastAdData && window.lastAdData === currentData) {
                                return; // 数据没有变化，不需要刷新
                            }
                            
                            // 保存新数据
                            window.lastAdData = currentData;
                            window.lastAdLoadTime = now;
                            
                            // 找到公告区域并刷新
                            const announcementEl = document.getElementById('announcement-area');
                            if (announcementEl) {
                                // 移除现有公告
                                while (announcementEl.firstChild) {
                                    announcementEl.removeChild(announcementEl.firstChild);
                                }
                                
                                // 重新创建公告
                                if (typeof createAnnouncement === 'function') {
                                    createAnnouncement(res.data);
                                } else if (typeof initAnnouncement === 'function') {
                                    initAnnouncement();
                                }
                                
                                console.log('广告内容已更新');
                            }
                        }
                    },
                    error: function(xhr) {
                        console.error('获取广告更新失败', xhr);
                    }
                });
            } catch (e) {
                console.error('检查广告更新时出错:', e);
            }
        }
        
        // 初始运行一次
        checkForUpdates();
        
        // 设置定时器，每分钟检查一次
        setInterval(checkForUpdates, 60000);
    }

    // 处理用户取消租用/不购买
    async function handleCancelRent(position) {
        try {
            // 调用API记录用户取消操作
            const response = await fetch(config.apiBase + 'cancelAdRent', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    position: position,
                    action: 'cancel'
                })
            });
            
            // 可以不需要等待响应，让用户体验更好
            console.log('用户取消租用广告位:', position);
        } catch (error) {
            console.error('记录取消操作失败:', error);
        }
    }

    // 新增：检查用户是否拥有指定广告位
    async function checkAdOwnership(position) {
        try {
            const response = await fetch(config.apiBase + 'checkAdOwnership', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({ position: position })
            });
            
            const result = await response.json();
            return result.code === 200 && result.data.owned === true;
        } catch (error) {
            console.error('检查广告位所有权失败:', error);
            return false;
        }
    }
    
    // 新增：显示需要先租用广告位的提示对话框
    function showRentRequiredDialog(position) {
        // 创建遮罩层
        const mask = document.createElement('div');
        mask.className = 'announcement-dialog-mask';
        
        // 创建对话框
        const dialog = document.createElement('div');
        dialog.className = 'announcement-dialog';
        
        // 对话框头部
        const header = document.createElement('div');
        header.className = 'announcement-dialog-header';
        
        const title = document.createElement('h3');
        title.className = 'announcement-dialog-title';
        title.textContent = '提示';
        
        const closeBtn = document.createElement('button');
        closeBtn.className = 'announcement-dialog-close';
        closeBtn.textContent = '×';
        closeBtn.onclick = () => mask.remove();
        
        header.appendChild(title);
        header.appendChild(closeBtn);
        
        // 对话框内容
        const body = document.createElement('div');
        body.className = 'announcement-dialog-body';
        body.innerHTML = `
            <div style="text-align: center; padding: 20px 0;">
                <div style="font-size: 64px; color: #e6a23c; margin-bottom: 20px;">
                    <i class="announcement-info-icon">!</i>
                </div>
                <p style="font-size: 16px; margin-bottom: 15px;">您尚未租用此广告位</p>
                <p style="color: #606266; margin-bottom: 20px;">请先租用广告位后再进行编辑操作</p>
            </div>
        `;
        
        // 对话框底部
        const footer = document.createElement('div');
        footer.className = 'announcement-dialog-footer';
        
        const cancelBtn = document.createElement('button');
        cancelBtn.className = 'announcement-btn announcement-btn-default';
        cancelBtn.textContent = '取消';
        cancelBtn.onclick = () => mask.remove();
        
        const rentBtn = document.createElement('button');
        rentBtn.className = 'announcement-btn announcement-btn-primary';
        rentBtn.textContent = '租用此广告位';
        rentBtn.onclick = () => {
            mask.remove();
            createRentDialog(position, false);
        };
        
        footer.appendChild(cancelBtn);
        footer.appendChild(rentBtn);
        
        // 组装对话框
        dialog.appendChild(header);
        dialog.appendChild(body);
        dialog.appendChild(footer);
        
        mask.appendChild(dialog);
        document.body.appendChild(mask);
        
        // 添加图标样式
        const style = document.createElement('style');
        style.textContent = `
            .announcement-info-icon {
                display: inline-block;
                width: 64px;
                height: 64px;
                line-height: 64px;
                text-align: center;
                border-radius: 50%;
                background-color: #fdf6ec;
                border: 2px solid #e6a23c;
                font-style: normal;
                font-weight: bold;
            }
        `;
        document.head.appendChild(style);
    }
})(); 