<?php

declare(strict_types=1);

namespace plugin\JingfakaMigrate\command;

use app\common\command\Command;
use think\console\Input;
use think\console\Output;
use app\common\model\SystemQueue as SystemQueueModel;
use think\facade\Db;
use think\facade\Config;
use app\common\library\RandomExtend;
use app\common\model\User as UserModel;
use app\common\model\UserCollect as UserCollectModel;
use app\common\model\UserAuth as UserAuthModel;
use app\common\model\GoodsCategory as GoodsCategoryModel;
use app\common\model\Goods as GoodsModel;
use app\common\model\GoodsCard as GoodsCardModel;
use app\common\model\GoodsCardStorage as GoodsCardStorageModel;
use app\common\service\StorageService;
use app\common\model\Order as OrderModel;
use app\common\model\OrderCardLog as OrderCardLogModel;
use app\common\model\AgentUser as AgentUserModel;
use app\common\model\Cash as CashModel;

class JingfakaMigrate extends Command {

    private $params = [];

    protected function configure() {
        // 指令配置
        $this->setName('JingfakaMigrate')->setDescription('鲸发卡迁移');
    }

    protected function execute(Input $input, Output $output) {
        $this->params = $this->queue->data;
        $this->setQueueProgress(">>> 迁移开始");
        $this->doMigrate();
        $this->setQueueSuccess("★☆★☆ 迁移成功  ★☆★☆");
    }

    private function checkStatus() {
        $queue = SystemQueueModel::where(['command' => 'JingfakaMigrate'])->find();
        if (!$queue || $queue->status != 2) {
            $this->setQueueSuccess("任务终止");
        }
    }

    private $userMap = [];
    private $goodsCategoryMap = [];
    private $goodsMap = [];
    private $configMap = [];
    private $img_host_fail = [];

    private function doMigrate() {

        $db_host = $this->params['hostname'] ?? '';
        $db_name = $this->params['database'] ?? '';
        $db_user = $this->params['username'] ?? '';
        $db_pwd = $this->params['password'] ?? '';
        $db_port = $this->params['hostport'] ?? 3306;
        if ($db_host == "" || $db_name == "" || $db_user == "" || $db_port == "") {
            $this->setQueueError("任务终止，请先配置插件后继续！");
        }

        Config::set([
            'connections' => [
                "dborigin" => [
                    'type' => 'mysql',
                    'hostname' => $db_host,
                    'database' => $db_name,
                    'username' => $db_user,
                    'password' => $db_pwd,
                    'hostport' => $db_port,
                    'params' => [],
                    'charset' => 'utf8',
                    'prefix' => '',
                ],
            ],
                ], 'database');

        try {
            Db::connect("dborigin")->query("select 1");
        } catch (\PDOException $e) {
            $this->setQueueError("任务终止，连接源数据库失败！" . $e->getMessage());
        } catch (\Exception $e) {
            $this->setQueueError("任务终止，连接源数据库失败！" . $e->getMessage());
        }
        $this->setQueueProgress(">>> 连接源数据库成功");

        // 获取配置
        $this->getOriginConfig();

        if ($this->params['datas']['user']) {
            // 迁移用户
            $this->migrateUser();
        }
        if ($this->params['datas']['goods_category']) {
            // 迁移分类
            $this->migrateGoodsCategory();
        }

        if ($this->params['datas']['goods']) {
            // 迁移商品
            $this->migrateGoods();
        }

        if ($this->params['datas']['user']) {
            // 迁移代理关系
            $this->migrateUserProxy();
        }

        if ($this->params['datas']['order']) {
            // 迁移订单
            $this->migrateOrder();
        }

        if ($this->params['datas']['auto_unfreeze']) {
            // 冻结表
            $this->migrateAutoUnfreeze();
        }

        if ($this->params['datas']['cash']) {
            // 提现单
            $this->migrateCash();
        }
    }

    private function getOriginConfig() {
        $this->configMap = Db::connect("dborigin")->name("system_config")->select()->toArray();
    }

    private function getOriginConfigOne($name) {
        $result = array_filter($this->configMap, function ($obj) use ($name) {
            return $obj['name'] === $name;
        });
        return $result['value'] ?? '';
    }

    private function migrateUser() {
        Db::connect("dborigin")->name("user")->chunk(200, function ($origin_users) {
            foreach ($origin_users as $origin_user) {

                $this->checkStatus();

                $exist = UserModel::where(['username' => $origin_user['username']])->find();

                $user = new UserModel();
                $user->username = $origin_user['username'];
                if ($exist) {
                    $user->username = $origin_user['username'] . "_2";
                }
                $user->password = $origin_user['password'];
                $user->mobile = $origin_user['mobile'];
                $user->nickname = $origin_user['shop_name'] ?: "商家" . substr($origin_user['mobile'], -4);
                $user->create_time = $origin_user['create_at'];
                $user->create_ip = $origin_user['ip'];
                $user->token = strtoupper(RandomExtend::random(8, 3));
                $user->shop_newbie = 1;
                $user->contact_qq = $origin_user['qq'];
                $user->platform_money = $origin_user['money'];
                $user->agent_key = $origin_user['agent_key'];
                $user->agent_status = 1;
                $user->agent_upgrade2_money = 10000;
                $user->agent_upgrade3_money = 20000;

                $user->save();

                $this->userMap[$origin_user['id']] = $user->id;

                // 设置标签
                $user->setRules(1, "JingfakaMigrate");
                if ($origin_user['status'] == 0) {
                    $user->setRules(1, "CloseUser");
                }
                // 设置收款信息
                $origin_collect = Db::connect("dborigin")->name("user_collect")->where(['user_id' => $origin_user['id']])->where('type', 'in', [1, 2.3])->find();
                if (!empty($origin_collect)) {
                    try {
                        $origin_collect_info = json_decode($origin_collect['info'], true);
                        $collect_info = [
                            'realname' => $origin_collect_info['realname'],
                            'realcard' => $origin_collect_info['idcard_number'],
                        ];

                        try {
                            if (!empty($origin_collect['collect_img'])) {
                                $parsedUrl = parse_url($origin_collect['collect_img']);
                                $domain = $parsedUrl['host'];

                                if (($origin_collect['collect_img'] ?? "" !== "") && !in_array($domain, $this->img_host_fail)) {
                                    $imgcontent = @file_get_contents($origin_collect['collect_img'] ?? "", false, stream_context_create([
                                                'http' => ['timeout' => 15]
                                    ]));
                                    if ($imgcontent === false) {
                                        $this->img_host_fail[] = $domain;
                                        throw new \Exception("远程获取收款二维码失败，跳过{$domain}");
                                    }
                                    $img_name = StorageService::name($origin_collect['collect_img'] ?? "", 'png', '', 'md5');

                                    $collect_img_info = StorageService::instance(strtolower(sysconf('storage.type')))->set($img_name, $imgcontent, false);
                                }
                            }
                        } catch (\Exception $exc) {
                            $this->setQueueProgress(">>> " . $exc->getMessage());
                        }

                        if ($origin_collect['type'] == 1) {
                            $collect_info['alipay_loginid'] = $origin_collect_info['account'];
                            $collect_info['alipay_qrcode'] = $collect_img_info['url'] ?? '';
                        }
                        if ($origin_collect['type'] == 2) {
                            $collect_info['weixin_id'] = $origin_collect_info['account'];
                            $collect_info['weixin_qrcode'] = $collect_img_info['url'] ?? '';
                        }
                        if ($origin_collect['type'] == 3) {
                            $collect_info['bank_name'] = $origin_collect_info['bank_name'];
                            $collect_info['card_number'] = $origin_collect_info['bank_card'];
                        }

                        if (isset($collect_img_info)) {
                            unset($collect_img_info);
                        }


                        $user_collect = new UserCollectModel();
                        $user_collect->user_id = $user->id;
                        $user_collect->collect_type = $origin_collect['type'];
                        $user_collect->collect_info = $collect_info;
                        $user_collect->save();
                    } catch (\Exception $exc) {
                        $this->setQueueProgress(">>> " . $exc->getMessage());
                    }
                }

                // 设置实名信息
                $origin_auth = Db::connect("dborigin")->name("plugin_merchantauth")->where(['user_id' => $origin_user['id']])->find();
                if (!empty($origin_auth)) {
                    $user_auth = new UserAuthModel();
                    $user_auth->user_id = $user->id;
                    $user_auth->params = [
                        'auth_option' => $origin_auth['auth_type'] == 3 ? 2 : 1,
                        'realname' => $origin_auth['card_name'] ?? '',
                        'realcard' => $origin_auth['card_number'] ?? '',
                        'mobile' => $origin_auth['mobile'] ?? '',
                    ];
                    $user_auth->auth_status = 2;
                    $user_auth->refuse_message = '';

                    $user_auth->save();
                }

                $this->setQueueProgress(">>> 用户【{$user->username}】迁移成功");
            }
        });

        Db::connect("dborigin")->name("user")->where("parent_id", ">", 0)->chunk(200, function ($origin_users) {

            $this->checkStatus();

            $this->setQueueProgress(">>> 分批处理用户邀请关系");

            foreach ($origin_users as $origin_user) {
                $user_id = $this->userMap[$origin_user['id']];
                $user = UserModel::where(['id' => $user_id])->find();
                if ($user) {
                    $user->parent_id = $this->userMap[$origin_user['parent_id']] ?? 0;
                    $user->save();
                }
            }
        });
    }

    private function migrateGoodsCategory() {
        Db::connect("dborigin")->name("goods_category")->chunk(200, function ($origin_categorys) {
            $this->setQueueProgress(">>> 分批迁移商品分类进行中 " . date('H:i:s', time()));

            foreach ($origin_categorys as $origin_category) {
                $this->checkStatus();

                $category = new GoodsCategoryModel();
                $category->user_id = $this->userMap[$origin_category['user_id']] ?? 0;
                $category->name = mb_substr($origin_category['name'], 0, 50, 'UTF-8');
                $category->sort = $origin_category['sort'];
                $category->status = $origin_category['status'];
                $category->create_time = $origin_category['create_at'];
                $category->goods_type = 'card';
                $category->save();

                $this->goodsCategoryMap[$origin_category['id']] = $category->id;
            }
        });
    }

    private function migrateGoods() {
        Db::connect("dborigin")->name("goods")->where(['delete_at' => null])->order("can_proxy desc")->chunk(200, function ($origin_goods) {
            foreach ($origin_goods as $origin_good) {
                $this->checkStatus();

                $origin_user = Db::connect("dborigin")->name("user")->where(['id' => $origin_good['user_id']])->find();
                if (empty($origin_user)) {
                    continue;
                }

                $goods = new GoodsModel();
                $goods->user_id = $this->userMap[$origin_good['user_id']] ?? 0;
                $goods->goods_type = "card";
                $goods->goods_key = random_key("goods", "goods_key", 6);
                $goods->verify = 1;
                $goods->name = mb_substr($origin_good['name'], 0, 60, 'UTF-8');
                $goods->category_id = $this->goodsCategoryMap[$origin_good['cate_id']] ?? 0;
                $goods->image = '';
                $goods->price = $origin_good['price'];
                $goods->market_price = 0;
                $goods->description = xss_safe(htmlspecialchars_decode($origin_good['content']));
                $goods->sort = $origin_good['sort'];
                $goods->status = $origin_good['status'];
                $goods->fee_payer = ($origin_user['fee_payer'] == 0 || $origin_user['fee_payer'] == 1) ? 0 : 1;
                $goods->show = 1;
                // 注意parent_id
                $goods->parent_id = $this->goodsMap[$origin_good['proxy_id']] ?? 0;
                $goods->agent_status = $origin_good['can_proxy'];
                $goods->agent_price1 = $origin_good['proxy_price'];
                $goods->agent_price2 = $origin_good['proxy_price'];
                $goods->agent_price3 = $origin_good['proxy_price'];

                // 对接的商品
                if ($origin_good['proxy_id'] > 0) {
                    if ($origin_good['proxy_price'] > 0) {
                        $goods->add_rate = round((($origin_good['price'] - $origin_good['proxy_price']) / $origin_good['proxy_price']) * 100, 1);
                    } else {
                        $goods->add_rate = 100;
                    }
                } else {
                    $goods->add_rate = 0;
                }

                $goods->create_time = $origin_good['create_at'];
                $goods->save();

                $goods->extend = new GoodsCardModel;
                $goods->extend->goods_id = $goods->id;
                $goods->extend->instructions = xss_safe(htmlspecialchars_decode($origin_good['remark']));
                $goods->extend->stock_notice = $origin_good['inventory_notify'];
                $goods->extend->lock_card = 0;
                $goods->extend->save();

                $this->goodsMap[$origin_good['id']] = $goods->id;

                $this->setQueueProgress(">>> 商品【{$goods->name}】迁移成功");

                $this->migrateCards($goods, $origin_good);
            }
        });
    }

    private function migrateCards($goods, $origin_good) {
        Db::connect("dborigin")->name("goods_card")->where(['goods_id' => $origin_good['id'], 'status' => 1, 'delete_at' => null])->chunk(600, function ($origin_cards) use ($goods, $origin_good) {
            $this->setQueueProgress(">>> 分批迁移库存【{$goods->name}】进行中 " . date('H:i:s', time()));
            $this->checkStatus();

            try {
                $cards = [];
                foreach ($origin_cards as $origin_card) {
                    $cards[] = [
                        'user_id' => $goods->user_id,
                        'goods_id' => $goods->id,
                        'secret' => $origin_card['secret'] == "" ? $origin_card['number'] : "卡号：" . $origin_card['number'] . "    卡密：" . $origin_card['secret'],
                        'status' => 0,
                        'first' => $origin_card['is_first'],
                        'create_time' => $origin_card['create_at'],
                    ];
                }

                GoodsCardStorageModel::checkCreateTable($goods->id);

                $model = new GoodsCardStorageModel();
                $model->setSuffix(goods_card_storage_suffix($goods->id));
                $model->saveAll($cards);
            } catch (\Exception $exc) {
                $this->setQueueProgress($exc->getMessage());
            }
        });
    }

    private function migrateUserProxy() {
        try {
            Db::connect("dborigin")->name("user_proxy")->chunk(200, function ($origin_user_proxys) {
                $this->setQueueProgress(">>> 分批迁移代理关系进行中 " . date('H:i:s', time()));

                foreach ($origin_user_proxys as $origin_user_proxy) {
                    $this->checkStatus();
                    $agent_user = new AgentUserModel();
                    $agent_user->user_id = $this->userMap[$origin_user_proxy['user_id']] ?? 0;
                    $agent_user->agent_id = $this->userMap[$origin_user_proxy['child_user_id']] ?? 0;

                    if ($agent_user->user_id == $agent_user->agent_id || $agent_user->user_id == 0 || $agent_user->agent_id == 0) {
                        continue;
                    }

                    $agent_user->create_time = time();
                    $agent_user->status = ($origin_user_proxy['status'] == 0 || $origin_user_proxy['status'] == -1) ? 0 : 1;
                    $agent_user->level_id = 1;
                    $agent_user->save();
                }
            });
        } catch (\Exception $th) {
            $this->setQueueProgress($th->getMessage());
        }
    }

    private function migrateOrder() {
        Db::connect("dborigin")->name("order")->where("create_at", ">", time() - 30 * 86400)->chunk(500, function ($origin_orders) {
            $this->setQueueProgress(">>> 分批迁移订单进行中 " . date('H:i:s', time()));

            foreach ($origin_orders as $origin_order) {
                $order = new OrderModel();

                if ($this->getOriginConfigOne('order_trade_no_type') == 1) {
                    if (strpos($origin_order['trade_no'], $this->getOriginConfigOne('order_trade_no_profix')) === 0) {
                        $newTradeNo = substr_replace($origin_order['trade_no'], sysconf('order.trade_no_profix'), 0, strlen($this->getOriginConfigOne('order_trade_no_profix')));
                    } else {
                        $newTradeNo = $origin_order['trade_no'];
                    }
                } else {
                    if (strpos($origin_order['trade_no'], "T") === 0) {
                        $newTradeNo = substr_replace($origin_order['trade_no'], sysconf('order.trade_no_profix'), 0, strlen("T"));
                    } else {
                        $newTradeNo = $origin_order['trade_no'];
                    }
                }

                ////////////////// 基础项 //////////////////
                $data['user_id'] = $this->userMap[$origin_order['user_id']] ?? 0;
                $data['contact'] = trim($origin_order['contact']);
                $data['trade_no'] = $newTradeNo;
                $data['create_time'] = $origin_order['create_at'];
                $data['create_ip'] = $origin_order['create_ip'];
                // 商品ID
                $data['goods_id'] = $this->goodsMap[$origin_order['goods_id']] ?? 0;
                // 商品名
                $data['goods_name'] = $origin_order['goods_name'];
                // 商品单价
                $data['goods_price'] = $origin_order['goods_price'];
                // 商品数量
                $data['quantity'] = $origin_order['quantity'];
                // 订单状态
                $data['status'] = $origin_order['status'];

                // 优惠券
                $data['use_coupon'] = $origin_order['coupon_type'];
                if ($data['use_coupon'] == 1) {
                    $data['coupon_code'] = Db::connect("dborigin")->name("goods_coupon")->where(['id' => $origin_order['coupon_id']])->value('code') ?? '';
                    $data['coupon_price'] = $origin_order['coupon_price'];
                } else {
                    $data['coupon_code'] = '';
                    $data['coupon_price'] = 0;
                }

                // 商品总原价（单价*数量）
                $data['original_amount'] = $origin_order['total_product_price'];
                // 实际支付金额
                $data['total_amount'] = $origin_order['total_price'];

                $data['channel_id'] = 0;
                $data['channel_account_id'] = 0;
                $data['transaction_id'] = $origin_order['transaction_id'];
                $data['success_time'] = $origin_order['success_at'];
                $data['sendout'] = $origin_order['sendout'];

                ////////////////// 费率结算项 //////////////////

                $data['rate'] = $origin_order['rate'];
                $data['fee_payer'] = ($origin_order['fee_payer'] == 0 || $origin_order['fee_payer'] == 1) ? 0 : 1;
                $data['fee'] = $origin_order['fee'];

                ////////////////// 代理相关 //////////////////
                if ($origin_order['proxy_id'] > 0) {
                    $data['parent_goods_id'] = $this->goodsMap[$origin_order['proxy_id']] ?? 0;
                    $data['parent_id'] = $this->userMap[$origin_order['proxy_parent_user_id']] ?? 0;
                    $data['parent_amount'] = $origin_order['proxy_finally_money'];
                } else {
                    $data['parent_goods_id'] = 0;
                    $data['parent_id'] = 0;
                    $data['parent_amount'] = 0;
                }

                try {
                    $order = OrderModel::create($data);
                } catch (\Exception $exc) {
                    continue;
                }

                $order_cards = Db::connect("dborigin")->name("order_card")->where(['order_id' => $origin_order['id']])->select();

                $result_cards = [];
                foreach ($order_cards as $o) {
                    $result_cards[] = $o['secret'] == "" ? $o['number'] : "卡号：" . $o['number'] . "    卡密：" . $o['secret'];
                }

                $orderCardLog = new OrderCardLogModel();
                $orderCardLog->order_id = $order->id;
                $orderCardLog->goods_id = $order->goods_id;
                $orderCardLog->cards = json_encode($result_cards);
                $orderCardLog->quantity = count($result_cards);
                $orderCardLog->save();
            }
        });
    }

    private function migrateAutoUnfreeze() {
        Db::connect("dborigin")->name("auto_unfreeze")->chunk(500, function ($origin_auto_unfreezes) {
            $this->setQueueProgress(">>> 分批处理冻结资金进行中 " . date('H:i:s', time()));
            foreach ($origin_auto_unfreezes as $origin_auto_unfreeze) {
                Db::startTrans();
                try {
                    $user = UserModel::lock(true)->where(['id' => $this->userMap[$origin_auto_unfreeze['user_id']] ?? 0])->find();
                    if (!$user) {
                        throw new \Exception("未找到用户");
                    }
                    $user->platform_money = Db::raw('platform_money+' . $origin_auto_unfreeze['money']);
                    $user->save();
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                }
            }
        });
    }

    private function migrateCash() {
        Db::connect("dborigin")->name("cash")->chunk(500, function ($origin_cashs) {
            $this->setQueueProgress(">>> 分批处理提现表进行中 " . date('H:i:s', time()));
            foreach ($origin_cashs as $origin_cash) {
                try {
                    $cash = new CashModel();
                    $cash->user_id = $this->userMap[$origin_cash['user_id']] ?? 0;
                    $cash->collect_type = $origin_cash['type'];

                    $collect_info = [
                        'realname' => $origin_cash['realname'],
                        'realcard' => $origin_cash['idcard_number'],
                    ];
                    try {
                        if (!empty($origin_cash['collect_img'])) {
                            $parsedUrl = parse_url($origin_cash['collect_img']);
                            $domain = $parsedUrl['host'];

                            if (($origin_cash['collect_img'] ?? "" !== "") && !in_array($domain, $this->img_host_fail)) {
                                $imgcontent = @file_get_contents($origin_cash['collect_img'] ?? "", false, stream_context_create([
                                            'http' => ['timeout' => 15,]
                                ]));
                                if ($imgcontent === false) {
                                    $this->img_host_fail[] = $domain;
                                    throw new \Exception("远程获取收款二维码失败，跳过{$domain}");
                                }
                                $img_name = StorageService::name($origin_cash['collect_img'] ?? "", 'png', '', 'md5');

                                $collect_img_info = StorageService::instance(strtolower(sysconf('storage.type')))->set($img_name, $imgcontent, false);
                            }
                        }
                    } catch (\Exception $exc) {
                        $this->setQueueProgress(">>> " . $exc->getMessage());
                    }
                    if ($origin_cash['type'] == 1) {
                        $collect_info['alipay_loginid'] = $origin_cash['bank_card'];
                        $collect_info['alipay_qrcode'] = $collect_img_info['url'] ?? '';
                    }
                    if ($origin_cash['type'] == 2) {
                        $collect_info['weixin_id'] = $origin_cash['bank_card'];
                        $collect_info['weixin_qrcode'] = $collect_img_info['url'] ?? '';
                    }
                    if ($origin_cash['type'] == 3) {
                        $collect_info['bank_name'] = $origin_cash['bank_name'];
                        $collect_info['card_number'] = $origin_cash['bank_card'];
                    }

                    if (isset($collect_img_info)) {
                        unset($collect_img_info);
                    }


                    $cash->collect_info = $collect_info;

                    $cash->money = $origin_cash['money'];
                    $cash->fee = $origin_cash['fee'];
                    $cash->actual_money = $origin_cash['actual_money'];
                    $cash->status = $origin_cash['status'] == 2 ? -1 : $origin_cash['status'];
                    $cash->create_time = $origin_cash['create_at'];
                    $cash->success_time = $origin_cash['complete_at'];
                    $cash->trade_no = $this->generate_trade_no('C', $origin_cash['create_at']);
                    $cash->reason = '';
                    $cash->daifu_status = $origin_cash['daifu_status'];
                    if ($cash->daifu_status == 1) {
                        $cash->daifu_success_time = $origin_cash['complete_at'];
                    }

                    $cash->save();
                } catch (\Exception $exc) {
                    $this->setQueueProgress($exc->getMessage());
                }
            }
        });
    }

    private function generate_trade_no($per, $time) {
        $chars = "abcdefghijklmnopqrstuvwxyz0123456789";
        $str = "";
        for ($i = 0; $i < 6; $i++) {
            $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
        }

        $trade_no = $per . date('ymd', $time) . strtoupper($str);
        return $trade_no;
    }
}
