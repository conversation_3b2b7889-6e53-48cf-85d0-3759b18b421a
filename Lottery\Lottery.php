<?php

namespace plugin\Lottery;

use app\common\library\Plugin;
use think\facade\Db;
use think\facade\Env;
use think\facade\Log;

class Lottery extends Plugin {

    public function install() {
        // 使用Env获取数据库前缀
        $prefix = Env::get('DB_PREFIX', 'pt_');

        echo "开始安装抽奖插件...\n";
        echo "正在创建数据表...\n";
        echo "数据库前缀: [" . $prefix . "]\n";

        try {
            // 定义所有表名
            $tables = [
                'config' => $prefix . 'plugin_lottery_config',
                'prizes' => $prefix . 'plugin_lottery_prizes',
                'records' => $prefix . 'plugin_lottery_records',
                'merchant_limits' => $prefix . 'plugin_lottery_merchant_limits',
                'turnover_rules' => $prefix . 'plugin_lottery_turnover_rules',
                'turnover_claims' => $prefix . 'plugin_lottery_turnover_claims',
                'prize_types' => $prefix . 'plugin_lottery_prize_types',
                'commission_draws' => $prefix . 'plugin_lottery_commission_draws',
                'invitations' => $prefix . 'plugin_lottery_invitations',
                'invitation_rewards' => $prefix . 'plugin_lottery_invitation_rewards'
            ];

            // 创建各个表
            $this->createConfigTable($tables['config']);
            $this->createPrizesTable($tables['prizes']);
            $this->createRecordsTable($tables['records']);
            $this->createMerchantLimitsTable($tables['merchant_limits']);
            $this->createTurnoverRulesTable($tables['turnover_rules']);
            $this->createTurnoverClaimsTable($tables['turnover_claims']);
            $this->createPrizeTypesTable($tables['prize_types']);
            $this->createCommissionDrawsTable($tables['commission_draws']);
            $this->createInvitationsTable($tables['invitations']);
            $this->createInvitationRewardsTable($tables['invitation_rewards']);

            // 插入初始化数据
            $this->insertInitialData($tables);

            echo "抽奖插件安装成功！\n";
            return true;
        } catch (\Exception $e) {
            echo "安装失败: " . $e->getMessage() . "\n";
            Log::error('抽奖插件安装失败: ' . $e->getMessage());
            // 记录错误日志到文件
            file_put_contents(__DIR__ . '/install_error.log', date('Y-m-d H:i:s') . " - 安装失败: " . $e->getMessage() . "\n", FILE_APPEND);
            return false;
        }
    }

    /**
     * 创建抽奖配置表
     */
    private function createConfigTable($tableName) {
        $hasTable = Db::query("SHOW TABLES LIKE '{$tableName}'");
        if (!empty($hasTable)) {
            echo "表 {$tableName} 已存在，跳过创建！\n";
            return;
        }

        $sql = <<<SQL
CREATE TABLE IF NOT EXISTS `{$tableName}` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `config_key` varchar(50) NOT NULL COMMENT '配置键',
    `config_value` text COMMENT '配置值',
    `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='抽奖配置表';
SQL;

        try {
            Db::execute($sql);
            echo "配置表创建完成！\n";
        } catch (\Exception $e) {
            echo "创建配置表失败: " . $e->getMessage() . "\n";
            file_put_contents(__DIR__ . '/install_error.log', date('Y-m-d H:i:s') . " - 创建配置表失败: " . $e->getMessage() . "\n", FILE_APPEND);
            throw $e;
        }
    }

    /**
     * 创建奖品表
     */
    private function createPrizesTable($tableName) {
        $hasTable = Db::query("SHOW TABLES LIKE '{$tableName}'");
        if (!empty($hasTable)) {
            echo "表 {$tableName} 已存在，跳过创建！\n";
            return;
        }

        $sql = <<<SQL
CREATE TABLE IF NOT EXISTS `{$tableName}` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL COMMENT '奖品名称',
    `type` varchar(20) NOT NULL COMMENT '奖品类型',
    `probability` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '中奖概率',
    `stock` int(11) NOT NULL DEFAULT '0' COMMENT '库存数量',
    `balance_amount` decimal(10,2) DEFAULT '0.00' COMMENT '余额金额',
    `description` text COMMENT '奖品描述',
    `image` varchar(255) DEFAULT NULL COMMENT '奖品图片',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 1启用 0禁用',
    `sort` int(11) DEFAULT '0' COMMENT '排序',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `status` (`status`),
    KEY `type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='抽奖奖品表';
SQL;

        try {
            Db::execute($sql);
            echo "奖品表创建完成！\n";
        } catch (\Exception $e) {
            echo "创建奖品表失败: " . $e->getMessage() . "\n";
            file_put_contents(__DIR__ . '/install_error.log', date('Y-m-d H:i:s') . " - 创建奖品表失败: " . $e->getMessage() . "\n", FILE_APPEND);
            throw $e;
        }
    }

    /**
     * 创建抽奖记录表
     */
    private function createRecordsTable($tableName) {
        $hasTable = Db::query("SHOW TABLES LIKE '{$tableName}'");
        if (!empty($hasTable)) {
            echo "表 {$tableName} 已存在，跳过创建！\n";
            return;
        }

        $sql = <<<SQL
CREATE TABLE IF NOT EXISTS `{$tableName}` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` bigint(20) NOT NULL COMMENT '用户ID',
    `merchant_id` bigint(20) NOT NULL COMMENT '商户ID',
    `prize_id` int(11) NOT NULL COMMENT '奖品ID',
    `prize_name` varchar(100) NOT NULL COMMENT '奖品名称',
    `prize_type` varchar(20) NOT NULL COMMENT '奖品类型',
    `balance_amount` decimal(10,2) DEFAULT '0.00' COMMENT '余额金额',
    `shipped` tinyint(1) DEFAULT '0' COMMENT '是否已发货',
    `ship_time` datetime DEFAULT NULL COMMENT '发货时间',
    `balance_sent` tinyint(1) DEFAULT '0' COMMENT '余额是否已发放',
    `auto_sent` tinyint(1) DEFAULT '0' COMMENT '是否自动发放',
    `is_virtual` tinyint(1) DEFAULT '0' COMMENT '是否虚拟记录',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `user_id` (`user_id`),
    KEY `merchant_id` (`merchant_id`),
    KEY `prize_id` (`prize_id`),
    KEY `create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='抽奖记录表';
SQL;

        try {
            Db::execute($sql);
            echo "抽奖记录表创建完成！\n";
        } catch (\Exception $e) {
            echo "创建抽奖记录表失败: " . $e->getMessage() . "\n";
            file_put_contents(__DIR__ . '/install_error.log', date('Y-m-d H:i:s') . " - 创建抽奖记录表失败: " . $e->getMessage() . "\n", FILE_APPEND);
            throw $e;
        }
    }

    /**
     * 创建商户抽奖限制表
     */
    private function createMerchantLimitsTable($tableName) {
        $hasTable = Db::query("SHOW TABLES LIKE '{$tableName}'");
        if (!empty($hasTable)) {
            echo "表 {$tableName} 已存在，跳过创建！\n";
            return;
        }

        $sql = <<<SQL
CREATE TABLE IF NOT EXISTS `{$tableName}` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `merchant_id` bigint(20) NOT NULL COMMENT '商户ID',
    `daily_limit` int(11) NOT NULL DEFAULT '3' COMMENT '每日抽奖次数限制',
    `used_count` int(11) NOT NULL DEFAULT '0' COMMENT '已使用次数',
    `last_reset_date` date NOT NULL COMMENT '最后重置日期',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `merchant_id` (`merchant_id`),
    KEY `last_reset_date` (`last_reset_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商户抽奖限制表';
SQL;

        try {
            Db::execute($sql);
            echo "商户抽奖限制表创建完成！\n";
        } catch (\Exception $e) {
            echo "创建商户抽奖限制表失败: " . $e->getMessage() . "\n";
            file_put_contents(__DIR__ . '/install_error.log', date('Y-m-d H:i:s') . " - 创建商户抽奖限制表失败: " . $e->getMessage() . "\n", FILE_APPEND);
            throw $e;
        }
    }

    /**
     * 创建流水规则表
     */
    private function createTurnoverRulesTable($tableName) {
        $hasTable = Db::query("SHOW TABLES LIKE '{$tableName}'");
        if (!empty($hasTable)) {
            echo "表 {$tableName} 已存在，跳过创建！\n";
            return;
        }

        $sql = <<<SQL
CREATE TABLE IF NOT EXISTS `{$tableName}` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `turnover_amount` decimal(10,2) NOT NULL COMMENT '流水金额',
    `draw_times` int(11) NOT NULL COMMENT '获得抽奖次数',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 1启用 0禁用',
    `sort` int(11) DEFAULT '0' COMMENT '排序',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `status` (`status`),
    KEY `turnover_amount` (`turnover_amount`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流水规则表';
SQL;

        try {
            Db::execute($sql);
            echo "流水规则表创建完成！\n";
        } catch (\Exception $e) {
            echo "创建流水规则表失败: " . $e->getMessage() . "\n";
            file_put_contents(__DIR__ . '/install_error.log', date('Y-m-d H:i:s') . " - 创建流水规则表失败: " . $e->getMessage() . "\n", FILE_APPEND);
            throw $e;
        }
    }

    /**
     * 创建流水领取记录表
     */
    private function createTurnoverClaimsTable($tableName) {
        $hasTable = Db::query("SHOW TABLES LIKE '{$tableName}'");
        if (!empty($hasTable)) {
            echo "表 {$tableName} 已存在，跳过创建！\n";
            return;
        }

        $sql = <<<SQL
CREATE TABLE IF NOT EXISTS `{$tableName}` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `merchant_id` bigint(20) NOT NULL COMMENT '商户ID',
    `rule_id` int(11) NOT NULL COMMENT '规则ID',
    `claim_date` date NOT NULL COMMENT '领取日期',
    `draw_times_used` int(11) DEFAULT '0' COMMENT '已使用的抽奖次数',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `merchant_rule_date` (`merchant_id`, `rule_id`, `claim_date`),
    KEY `merchant_id` (`merchant_id`),
    KEY `claim_date` (`claim_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流水领取记录表';
SQL;

        try {
            Db::execute($sql);
            echo "流水领取记录表创建完成！\n";
        } catch (\Exception $e) {
            echo "创建流水领取记录表失败: " . $e->getMessage() . "\n";
            file_put_contents(__DIR__ . '/install_error.log', date('Y-m-d H:i:s') . " - 创建流水领取记录表失败: " . $e->getMessage() . "\n", FILE_APPEND);
            throw $e;
        }
    }

    /**
     * 创建奖品类型表
     */
    private function createPrizeTypesTable($tableName) {
        $hasTable = Db::query("SHOW TABLES LIKE '{$tableName}'");
        if (!empty($hasTable)) {
            echo "表 {$tableName} 已存在，跳过创建！\n";
            return;
        }

        $sql = <<<SQL
CREATE TABLE IF NOT EXISTS `{$tableName}` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `value` varchar(20) NOT NULL COMMENT '类型值',
    `label` varchar(50) NOT NULL COMMENT '类型标签',
    `tag_type` varchar(20) DEFAULT 'info' COMMENT '标签类型',
    `tag_color` varchar(20) DEFAULT NULL COMMENT '自定义标签颜色',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 1启用 0禁用',
    `sort` int(11) DEFAULT '0' COMMENT '排序',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `value` (`value`),
    KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='奖品类型表';
SQL;

        try {
            Db::execute($sql);
            echo "奖品类型表创建完成！\n";
        } catch (\Exception $e) {
            echo "创建奖品类型表失败: " . $e->getMessage() . "\n";
            file_put_contents(__DIR__ . '/install_error.log', date('Y-m-d H:i:s') . " - 创建奖品类型表失败: " . $e->getMessage() . "\n", FILE_APPEND);
            throw $e;
        }
    }

    /**
     * 创建分佣抽奖次数使用记录表
     */
    private function createCommissionDrawsTable($tableName) {
        $hasTable = Db::query("SHOW TABLES LIKE '{$tableName}'");
        if (!empty($hasTable)) {
            echo "表 {$tableName} 已存在，跳过创建！\n";
            return;
        }

        $sql = <<<SQL
CREATE TABLE IF NOT EXISTS `{$tableName}` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `merchant_id` bigint(20) NOT NULL COMMENT '商户ID',
    `draw_date` date NOT NULL COMMENT '抽奖日期',
    `used_count` int(11) NOT NULL DEFAULT '0' COMMENT '已使用的分佣抽奖次数',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `merchant_date` (`merchant_id`, `draw_date`),
    KEY `merchant_id` (`merchant_id`),
    KEY `draw_date` (`draw_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分佣抽奖次数使用记录表';
SQL;

        try {
            Db::execute($sql);
            echo "分佣抽奖次数使用记录表创建完成！\n";
        } catch (\Exception $e) {
            echo "创建分佣抽奖次数使用记录表失败: " . $e->getMessage() . "\n";
            file_put_contents(__DIR__ . '/install_error.log', date('Y-m-d H:i:s') . " - 创建分佣抽奖次数使用记录表失败: " . $e->getMessage() . "\n", FILE_APPEND);
            throw $e;
        }
    }

    /**
     * 创建邀请关系表
     */
    private function createInvitationsTable($tableName) {
        $hasTable = Db::query("SHOW TABLES LIKE '{$tableName}'");
        if (!empty($hasTable)) {
            echo "表 {$tableName} 已存在，跳过创建！\n";
            return;
        }

        $sql = <<<SQL
CREATE TABLE IF NOT EXISTS `{$tableName}` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `inviter_id` bigint(20) NOT NULL COMMENT '邀请人ID',
    `inviter_name` varchar(50) NOT NULL COMMENT '邀请人用户名',
    `invitee_id` bigint(20) NOT NULL COMMENT '被邀请人ID',
    `invitee_name` varchar(50) NOT NULL COMMENT '被邀请人用户名',
    `invitation_code` varchar(100) DEFAULT NULL COMMENT '邀请码',
    `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0=待确认，1=已确认',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
    `confirm_time` datetime DEFAULT NULL COMMENT '确认时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `invitee_id` (`invitee_id`),
    KEY `inviter_id` (`inviter_id`),
    KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邀请关系表';
SQL;

        try {
            Db::execute($sql);
            echo "邀请关系表创建完成！\n";
        } catch (\Exception $e) {
            echo "创建邀请关系表失败: " . $e->getMessage() . "\n";
            file_put_contents(__DIR__ . '/install_error.log', date('Y-m-d H:i:s') . " - 创建邀请关系表失败: " . $e->getMessage() . "\n", FILE_APPEND);
            throw $e;
        }
    }

    /**
     * 创建邀请奖励记录表
     */
    private function createInvitationRewardsTable($tableName) {
        $hasTable = Db::query("SHOW TABLES LIKE '{$tableName}'");
        if (!empty($hasTable)) {
            echo "表 {$tableName} 已存在，跳过创建！\n";
            return;
        }

        $sql = <<<SQL
CREATE TABLE IF NOT EXISTS `{$tableName}` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `invitation_id` int(11) NOT NULL COMMENT '邀请关系ID',
    `inviter_id` bigint(20) NOT NULL COMMENT '邀请人ID',
    `inviter_name` varchar(50) NOT NULL COMMENT '邀请人用户名',
    `invitee_id` bigint(20) NOT NULL COMMENT '被邀请人ID',
    `invitee_name` varchar(50) NOT NULL COMMENT '被邀请人用户名',
    `reward_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '奖励金额',
    `reward_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '奖励状态：0=未发放，1=已发放',
    `send_time` datetime DEFAULT NULL COMMENT '发放时间',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `invitation_id` (`invitation_id`),
    KEY `inviter_id` (`inviter_id`),
    KEY `reward_status` (`reward_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邀请奖励记录表';
SQL;

        try {
            Db::execute($sql);
            echo "邀请奖励记录表创建完成！\n";
        } catch (\Exception $e) {
            echo "创建邀请奖励记录表失败: " . $e->getMessage() . "\n";
            file_put_contents(__DIR__ . '/install_error.log', date('Y-m-d H:i:s') . " - 创建邀请奖励记录表失败: " . $e->getMessage() . "\n", FILE_APPEND);
            throw $e;
        }
    }

    /**
     * 插入初始化数据
     */
    private function insertInitialData($tables) {
        try {
            // 插入基础配置
            $configData = [
                ['config_key' => 'status', 'config_value' => '1', 'description' => '抽奖状态：1=开启，0=关闭'],
                ['config_key' => 'daily_limit', 'config_value' => '3', 'description' => '每日抽奖次数限制'],
                ['config_key' => 'start_hour', 'config_value' => '00:00', 'description' => '抽奖开始时间'],
                ['config_key' => 'end_hour', 'config_value' => '23:59', 'description' => '抽奖结束时间'],
                ['config_key' => 'show_probability', 'config_value' => '0', 'description' => '是否显示中奖概率：1=显示，0=不显示'],
                ['config_key' => 'auto_send_balance', 'config_value' => '1', 'description' => '是否自动发放余额：1=自动，0=手动'],
                ['config_key' => 'commission_draw_enabled', 'config_value' => '0', 'description' => '是否启用下级商家销售分佣获取抽奖次数'],
                ['config_key' => 'commission_draw_amount', 'config_value' => '1.00', 'description' => '每多少分佣金额获得1次抽奖'],
                ['config_key' => 'commission_draw_max', 'config_value' => '10', 'description' => '每日通过分佣最多获得抽奖次数'],
                ['config_key' => 'turnover_reset_period', 'config_value' => 'daily', 'description' => '流水统计重置周期：daily=每日，weekly=每周，monthly=每月'],
                ['config_key' => 'invitation_enabled', 'config_value' => '0', 'description' => '是否启用邀请奖励功能'],
                ['config_key' => 'invitation_reward_amount', 'config_value' => '10.00', 'description' => '邀请奖励金额'],
                ['config_key' => 'invitation_auto_send', 'config_value' => '1', 'description' => '是否自动发放邀请奖励'],
                ['config_key' => 'hidden_prizes', 'config_value' => '[]', 'description' => '隐藏奖品列表'],
                ['config_key' => 'auto_update_status', 'config_value' => '0', 'description' => 'Hook自动更新状态：1=开启，0=关闭'],
                ['config_key' => 'update_interval', 'config_value' => '3600', 'description' => 'Hook更新间隔（秒）'],
                ['config_key' => 'last_update', 'config_value' => '0', 'description' => '最后更新时间戳']
            ];

            foreach ($configData as $config) {
                $exists = Db::name('plugin_lottery_config')
                    ->where('config_key', $config['config_key'])
                    ->count();
                if (!$exists) {
                    Db::name('plugin_lottery_config')->insert($config);
                }
            }

            // 插入默认奖品类型
            $prizeTypesData = [
                ['value' => 'physical', 'label' => '实物奖品', 'tag_type' => 'warning', 'tag_color' => null, 'status' => 1, 'sort' => 1],
                ['value' => 'virtual', 'label' => '虚拟奖品', 'tag_type' => 'success', 'tag_color' => null, 'status' => 1, 'sort' => 2],
                ['value' => 'cash', 'label' => '现金奖励', 'tag_type' => 'danger', 'tag_color' => null, 'status' => 1, 'sort' => 3],
                ['value' => 'coupon', 'label' => '优惠券', 'tag_type' => 'info', 'tag_color' => null, 'status' => 1, 'sort' => 4],
                ['value' => 'points', 'label' => '积分奖励', 'tag_type' => 'primary', 'tag_color' => null, 'status' => 1, 'sort' => 5],
                ['value' => 'thanks', 'label' => '谢谢参与', 'tag_type' => 'info', 'tag_color' => '#909399', 'status' => 1, 'sort' => 99]
            ];

            foreach ($prizeTypesData as $prizeType) {
                $exists = Db::name('plugin_lottery_prize_types')
                    ->where('value', $prizeType['value'])
                    ->count();
                if (!$exists) {
                    Db::name('plugin_lottery_prize_types')->insert($prizeType);
                }
            }

            // 插入示例奖品
            $prizesData = [
                ['name' => '谢谢参与', 'type' => 'thanks', 'probability' => 50.00, 'stock' => 999999, 'balance_amount' => 0.00, 'description' => '感谢您的参与！', 'status' => 1, 'sort' => 99],
                ['name' => '1元现金', 'type' => 'cash', 'probability' => 20.00, 'stock' => 100, 'balance_amount' => 1.00, 'description' => '获得1元现金奖励', 'status' => 1, 'sort' => 1],
                ['name' => '5元现金', 'type' => 'cash', 'probability' => 15.00, 'stock' => 50, 'balance_amount' => 5.00, 'description' => '获得5元现金奖励', 'status' => 1, 'sort' => 2],
                ['name' => '10元现金', 'type' => 'cash', 'probability' => 10.00, 'stock' => 20, 'balance_amount' => 10.00, 'description' => '获得10元现金奖励', 'status' => 1, 'sort' => 3],
                ['name' => '优惠券', 'type' => 'coupon', 'probability' => 3.00, 'stock' => 30, 'balance_amount' => 0.00, 'description' => '获得优惠券一张', 'status' => 1, 'sort' => 4],
                ['name' => '积分奖励', 'type' => 'points', 'probability' => 2.00, 'stock' => 50, 'balance_amount' => 0.00, 'description' => '获得100积分', 'status' => 1, 'sort' => 5]
            ];

            foreach ($prizesData as $prize) {
                $exists = Db::name('plugin_lottery_prizes')
                    ->where('name', $prize['name'])
                    ->where('type', $prize['type'])
                    ->count();
                if (!$exists) {
                    Db::name('plugin_lottery_prizes')->insert($prize);
                }
            }

            // 插入示例流水规则
            $turnoverRulesData = [
                ['turnover_amount' => 100.00, 'draw_times' => 1, 'status' => 1, 'sort' => 1],
                ['turnover_amount' => 500.00, 'draw_times' => 3, 'status' => 1, 'sort' => 2],
                ['turnover_amount' => 1000.00, 'draw_times' => 5, 'status' => 1, 'sort' => 3],
                ['turnover_amount' => 2000.00, 'draw_times' => 2, 'status' => 1, 'sort' => 4],
                ['turnover_amount' => 5000.00, 'draw_times' => 5, 'status' => 1, 'sort' => 5],
                ['turnover_amount' => 10000.00, 'draw_times' => 10, 'status' => 1, 'sort' => 6]
            ];

            foreach ($turnoverRulesData as $rule) {
                $exists = Db::name('plugin_lottery_turnover_rules')
                    ->where('turnover_amount', $rule['turnover_amount'])
                    ->count();
                if (!$exists) {
                    Db::name('plugin_lottery_turnover_rules')->insert($rule);
                }
            }

            echo "初始化数据插入完成！\n";
        } catch (\Exception $e) {
            echo "插入初始化数据失败: " . $e->getMessage() . "\n";
            file_put_contents(__DIR__ . '/install_error.log', date('Y-m-d H:i:s') . " - 插入初始化数据失败: " . $e->getMessage() . "\n", FILE_APPEND);
            throw $e;
        }
    }

    public function uninstall() {
        echo "开始卸载抽奖插件...\n";

        try {
            // 获取数据库前缀
            $prefix = Env::get('DB_PREFIX', 'pt_');

            // 删除所有相关表
            $tables = [
                $prefix . 'plugin_lottery_config',
                $prefix . 'plugin_lottery_prizes',
                $prefix . 'plugin_lottery_records',
                $prefix . 'plugin_lottery_merchant_limits',
                $prefix . 'plugin_lottery_turnover_rules',
                $prefix . 'plugin_lottery_turnover_claims',
                $prefix . 'plugin_lottery_prize_types',
                $prefix . 'plugin_lottery_commission_draws',
                $prefix . 'plugin_lottery_invitations',
                $prefix . 'plugin_lottery_invitation_rewards'
            ];

            foreach ($tables as $table) {
                try {
                    Db::execute("DROP TABLE IF EXISTS `{$table}`");
                    echo "表 {$table} 删除成功\n";
                } catch (\Exception $e) {
                    echo "删除表 {$table} 失败: " . $e->getMessage() . "\n";
                    // 继续删除其他表
                }
            }

            echo "抽奖插件卸载成功！\n";
            return true;
        } catch (\Exception $e) {
            echo "卸载失败: " . $e->getMessage() . "\n";
            Log::error('抽奖插件卸载失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * 升级方法
     */
    public function upgrade() {
        try {
            // 使用Env获取数据库前缀
            $prefix = Env::get('DB_PREFIX', 'pt_');

            echo "开始升级抽奖插件...\n";

            // 定义所有表名
            $tables = [
                'config' => $prefix . 'plugin_lottery_config',
                'prizes' => $prefix . 'plugin_lottery_prizes',
                'records' => $prefix . 'plugin_lottery_records',
                'merchant_limits' => $prefix . 'plugin_lottery_merchant_limits',
                'turnover_rules' => $prefix . 'plugin_lottery_turnover_rules',
                'turnover_claims' => $prefix . 'plugin_lottery_turnover_claims',
                'prize_types' => $prefix . 'plugin_lottery_prize_types',
                'commission_draws' => $prefix . 'plugin_lottery_commission_draws',
                'invitations' => $prefix . 'plugin_lottery_invitations',
                'invitation_rewards' => $prefix . 'plugin_lottery_invitation_rewards'
            ];

            // 检查并创建缺失的表
            foreach ($tables as $key => $tableName) {
                $tableExists = Db::query("SHOW TABLES LIKE '{$tableName}'");
                if (empty($tableExists)) {
                    echo "表 {$tableName} 不存在，正在创建...\n";
                    switch ($key) {
                        case 'config':
                            $this->createConfigTable($tableName);
                            break;
                        case 'prizes':
                            $this->createPrizesTable($tableName);
                            break;
                        case 'records':
                            $this->createRecordsTable($tableName);
                            break;
                        case 'merchant_limits':
                            $this->createMerchantLimitsTable($tableName);
                            break;
                        case 'turnover_rules':
                            $this->createTurnoverRulesTable($tableName);
                            break;
                        case 'turnover_claims':
                            $this->createTurnoverClaimsTable($tableName);
                            break;
                        case 'prize_types':
                            $this->createPrizeTypesTable($tableName);
                            break;
                        case 'commission_draws':
                            $this->createCommissionDrawsTable($tableName);
                            break;
                        case 'invitations':
                            $this->createInvitationsTable($tableName);
                            break;
                        case 'invitation_rewards':
                            $this->createInvitationRewardsTable($tableName);
                            break;
                    }
                }
            }

            // 检查并添加缺失的字段
            $this->upgradeTableFields($tables);

            // 插入缺失的初始化数据
            $this->insertInitialData($tables);

            echo "抽奖插件升级成功！\n";
            return true;
        } catch (\Exception $e) {
            echo "升级失败: " . $e->getMessage() . "\n";
            Log::error('抽奖插件升级失败：' . $e->getMessage());
            file_put_contents(__DIR__ . '/install_error.log', date('Y-m-d H:i:s') . " - 升级失败: " . $e->getMessage() . "\n", FILE_APPEND);
            return false;
        }
    }

    /**
     * 升级表字段
     */
    private function upgradeTableFields($tables) {
        // 这里可以添加字段升级逻辑
        // 例如：检查某个表是否缺少某个字段，如果缺少则添加

        // 1. 检查抽奖记录表是否有 is_virtual 字段
        $recordsTable = $tables['records'];
        $columns = Db::query("SHOW COLUMNS FROM `{$recordsTable}`");
        $hasVirtualField = false;
        foreach ($columns as $column) {
            if ($column['Field'] == 'is_virtual') {
                $hasVirtualField = true;
                break;
            }
        }

        if (!$hasVirtualField) {
            Db::execute("ALTER TABLE `{$recordsTable}` ADD COLUMN `is_virtual` tinyint(1) DEFAULT '0' COMMENT '是否虚拟记录' AFTER `auto_sent`");
            echo "添加 is_virtual 字段成功\n";
        }

        // 2. 升级 merchant_id 相关字段从 int(11) 到 bigint(20)
        $this->upgradeMerchantIdFields($tables);
    }

    /**
     * 升级商户ID字段类型
     */
    private function upgradeMerchantIdFields($tables) {
        try {
            echo "开始升级商户ID字段类型...\n";

            // 需要升级的表和字段映射
            $fieldsToUpgrade = [
                'records' => ['user_id', 'merchant_id'],
                'merchant_limits' => ['merchant_id'],
                'turnover_claims' => ['merchant_id'],
                'commission_draws' => ['merchant_id'],
                'invitations' => ['inviter_id', 'invitee_id'],
                'invitation_rewards' => ['inviter_id', 'invitee_id']
            ];

            foreach ($fieldsToUpgrade as $tableKey => $fields) {
                if (!isset($tables[$tableKey])) {
                    continue;
                }

                $tableName = $tables[$tableKey];

                // 检查表是否存在
                $tableExists = Db::query("SHOW TABLES LIKE '{$tableName}'");
                if (empty($tableExists)) {
                    continue;
                }

                foreach ($fields as $fieldName) {
                    // 检查字段当前类型
                    $columns = Db::query("SHOW COLUMNS FROM `{$tableName}` WHERE Field = '{$fieldName}'");
                    if (!empty($columns)) {
                        $column = $columns[0];
                        $currentType = strtolower($column['Type']);

                        // 如果不是 bigint 类型，则升级
                        if (strpos($currentType, 'bigint') === false) {
                            $comment = isset($column['Comment']) ? $column['Comment'] : '';
                            $nullable = $column['Null'] === 'YES' ? '' : 'NOT NULL';

                            $sql = "ALTER TABLE `{$tableName}` MODIFY COLUMN `{$fieldName}` bigint(20) {$nullable}";
                            if ($comment) {
                                $sql .= " COMMENT '{$comment}'";
                            }

                            Db::execute($sql);
                            echo "升级 {$tableName}.{$fieldName} 字段类型成功\n";
                        }
                    }
                }
            }

            echo "商户ID字段类型升级完成\n";
        } catch (\Exception $e) {
            echo "升级商户ID字段类型失败: " . $e->getMessage() . "\n";
            Log::error('升级商户ID字段类型失败：' . $e->getMessage());
        }
    }
}
