<?php

namespace plugin\Wzsjd\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use app\common\library\Auth;

class Api extends BasePlugin
{
    protected $scene = ['admin'];
    protected $noNeedLogin = ['fetchData', 'fetchStrategyData'];

    public function index()
    {
        return View::fetch();
    }

    // 获取策略模块数据
    public function fetchStrategyData()
    {
        try {
            // 检查插件是否启用
            $status = plugconf('Wzsjd.status');
            if (!$status) {
                return json(['code' => 200, 'data' => ['status' => 0]]);
            }

            // 从设置中获取策略模块配置
            $config = [
                'status' => plugconf('Wzsjd.status'),
                'title' => plugconf('Wzsjd.strategy_title') ?? '营销策略',
                'items' => plugconf('Wzsjd.strategy_items') ?? '[]'
            ];

            return json(['code' => 200, 'data' => $config]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '获取策略模块数据失败: ' . $e->getMessage()]);
        }
    }

    // 保存策略模块配置
    public function saveStrategy()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 405, 'msg' => '请求方法不允许']);
        }

        try {
            // 获取并验证参数
            $title = $this->request->post('title', '营销策略', 'trim');
            $items = $this->request->post('items', '[]', 'trim');
            
            // 验证items数据格式
            $itemsData = json_decode($items, true);
            if (!is_array($itemsData)) {
                return json(['code' => 400, 'msg' => '策略项数据格式不正确']);
            }

            // 保存配置
            plugconf("Wzsjd.strategy_title", $title);
            plugconf("Wzsjd.strategy_items", $items);

            return json(['code' => 200, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '保存失败: ' . $e->getMessage()]);
        }
    }

    // 获取配置
    public function fetchData()
    {
        try {
            // 检查插件是否启用
            $status = plugconf('Wzsjd.status');
            
            // 确保status值为有效整数
            if ($status === null) {
                $status = 1; // 默认值为开启
            } else {
                // 确保转为整数，1表示开启，0表示关闭
                $status = intval($status); 
                $status = $status ? 1 : 0; // 规范化为1或0
            }
            
            // 从设置中获取配置（无论状态如何，都返回完整配置）
            $config = [
                // UI中显示的配置项
                'status' => $status,
                'nav_title' => plugconf('Wzsjd.nav_title') ?? '快捷导航',
                'icons' => plugconf('Wzsjd.icons') ?? '[]',
                
                // 固定默认值（UI中已不显示）
                'position' => 'bottom',
                'max_icons' => 10,
                'z_index' => 1000,
                
                // 保留这些字段以维持兼容性，但在UI中不再显示
                'background' => plugconf('Wzsjd.background') ?? '#FFFFFF',
                'text_color' => plugconf('Wzsjd.text_color') ?? '#333333',
                'height' => intval(plugconf('Wzsjd.height') ?? 80),
                'padding' => intval(plugconf('Wzsjd.padding') ?? 15),
                'border_radius' => intval(plugconf('Wzsjd.border_radius') ?? 8),
                'box_shadow' => plugconf('Wzsjd.box_shadow') ?? '0 2px 8px rgba(0, 0, 0, 0.1)',
                'icon_size' => intval(plugconf('Wzsjd.icon_size') ?? 30),
                'display_style' => plugconf('Wzsjd.display_style') ?? 'grid',
            ];

            // 确保icons是有效的JSON字符串
            if (isset($config['icons']) && !empty($config['icons'])) {
                // 验证是否为有效的JSON
                json_decode($config['icons']);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    // 如果不是有效的JSON，则设置为空数组
                    $config['icons'] = '[]';
                }
            }

            return json(['code' => 200, 'data' => $config]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '获取数据失败: ' . $e->getMessage(), 'type' => 'error']);
        }
    }

    // 保存配置
    public function save()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 405, 'msg' => '请求方法不允许']);
        }

        try {
            // 获取并验证基本参数
            $status = $this->request->post('status/d', 1);
            // 规范化状态值
            $status = $status ? 1 : 0;
            $nav_title = $this->request->post('nav_title', '快捷导航', 'trim');
            $icons = $this->request->post('icons', '[]', 'trim');
            
            // 使用默认固定值，不再从请求中获取
            $position = 'bottom';  // 默认底部显示
            $max_icons = 10;       // 默认最多10个图标
            $z_index = 1000;       // 默认堆叠层级1000
            
            // 验证图标数据格式
            $iconsData = json_decode($icons, true);
            if (!is_array($iconsData)) {
                return json(['code' => 400, 'msg' => '图标数据格式不正确']);
            }
            
            // 验证图标数量是否超过最大限制
            if (count($iconsData) > $max_icons) {
                return json(['code' => 400, 'msg' => "最多只能添加{$max_icons}个图标"]);
            }
            
            // 验证每个图标的必要属性
            foreach ($iconsData as $index => $item) {
                if (empty($item['name']) || empty($item['icon']) || empty($item['link'])) {
                    return json(['code' => 400, 'msg' => "第" . ($index + 1) . "个图标的名称、图标或链接不能为空"]);
                }
            }

            // 保存基本配置
            plugconf("Wzsjd.status", $status);
            plugconf("Wzsjd.nav_title", $nav_title);
            plugconf("Wzsjd.icons", $icons);
            
            // 保存固定默认值
            plugconf("Wzsjd.position", $position);
            plugconf("Wzsjd.max_icons", $max_icons);
            plugconf("Wzsjd.z_index", $z_index);
            
            // 保持默认样式值以维持兼容性
            plugconf("Wzsjd.background", '#FFFFFF');
            plugconf("Wzsjd.text_color", '#333333');
            plugconf("Wzsjd.height", 80);
            plugconf("Wzsjd.padding", 15);
            plugconf("Wzsjd.border_radius", 8);
            plugconf("Wzsjd.box_shadow", '0 2px 8px rgba(0, 0, 0, 0.1)');
            plugconf("Wzsjd.icon_size", 30);
            plugconf("Wzsjd.display_style", 'grid');

            return json(['code' => 200, 'msg' => '保存成功', 'data' => ['reload' => true]]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '保存失败: ' . $e->getMessage(), 'type' => 'error']);
        }
    }
} 