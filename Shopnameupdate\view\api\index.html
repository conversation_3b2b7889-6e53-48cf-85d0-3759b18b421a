<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <meta http-equiv="Cache-Control" content="max-age=31536000">
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>同步对接商品名称</title>
    <style>
        /* 加载动画样式 */
        #loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #fff;
            z-index: 9999;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(0, 0, 0, 0.1);
            border-top-color: #3498db;
            border-radius: 50%;
            animation: spin 1s ease infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    </style>
</head>

<body>
<!-- 加载动画容器 -->
<div id="loading">
    <div class="spinner"></div>
</div>

<!-- Vue 应用挂载点 -->
<div id="app" style="display: none">
    <el-card shadow="never">
        <el-form :model="form" label-width="auto">
            <el-form-item label="同步功能：">
                <el-switch
                    v-model="form.status"
                    :loading="isLoading"
                    @change="saveStatus"
                    :active-value="1"
                    :inactive-value="0"
                />
                <span style="margin-left: 10px; color: #909399; font-size: 12px;">
                    开启后可以同步商品名称
                </span>
            </el-form-item>
            <el-form-item label="自动同步：">
                <el-switch
                    v-model="form.auto_sync"
                    :loading="isLoading"
                    @change="saveAutoSync"
                    :active-value="1"
                    :inactive-value="0"
                />
                <span style="margin-left: 10px; color: #909399; font-size: 12px;">
                    开启后将自动同步商品名称
                </span>
            </el-form-item>
            <el-form-item>
                <el-button 
                    type="primary" 
                    :loading="isLoading" 
                    @click="syncGoods"
                    :disabled="!form.status"
                    style="margin-right: 10px;"
                >
                    一键同步下级商品名称
                </el-button>
                <el-button 
                    type="success" 
                    :loading="isLoading" 
                    @click="syncAllGoods"
                    :disabled="!form.status"
                >
                    一键同步所有商品名称
                </el-button>
            </el-form-item>
        </el-form>
    </el-card>
</div>

<script src="/static/others/vue/vue.global.prod.js"></script>
<script src="/static/others/element-plus/index.full.min.js"></script>
<script src="/static/others/axios/axios.min.js"></script>

<script type="module">
    const { ref, reactive } = Vue;
    const { ElMessage } = ElementPlus;

    const isLoading = ref(false);
    const form = reactive({
        status: 0,
        auto_sync: 0
    });

    // 获取配置
    const fetchData = async () => {
        try {
            const res = await axios.post("/plugin/Shopnameupdate/api/fetchData");
            if (res.data?.code == 200) {
                form.status = res.data?.data?.status ?? 0;
                form.auto_sync = res.data?.data?.auto_sync ?? 0;
            }
        } catch (error) {
            ElMessage.error(error.message || '获取配置失败');
        }
    };

    // 保存状态
    const saveStatus = async () => {
        isLoading.value = true;
        try {
            const res = await axios.post("/plugin/Shopnameupdate/api/save", form);
            isLoading.value = false;
            if (res.data?.code == 200) {
                ElMessage.success('保存成功');
            } else {
                ElMessage.error(res.data?.msg);
                form.status = !form.status;
            }
        } catch (error) {
            isLoading.value = false;
            ElMessage.error(error.message || '保存失败');
            form.status = !form.status;
        }
    };

    // 保存自动同步状态
    const saveAutoSync = async () => {
        isLoading.value = true;
        try {
            const res = await axios.post("/plugin/Shopnameupdate/api/saveAutoSync", form);
            isLoading.value = false;
            if (res.data?.code == 200) {
                ElMessage.success('自动同步状态保存成功');
            } else {
                ElMessage.error(res.data?.msg);
                form.auto_sync = !form.auto_sync;
            }
        } catch (error) {
            isLoading.value = false;
            ElMessage.error(error.message || '保存失败');
            form.auto_sync = !form.auto_sync;
        }
    };

    const syncGoods = async () => {
        if (!form.status) {
            ElMessage.warning('请先启用同步功能');
            return;
        }
        
        isLoading.value = true;
        try {
            const res = await axios.post("/plugin/Shopnameupdate/api/syncGoods");
            isLoading.value = false;
            if (res.data?.code == 200) {
                ElMessage.success(res.data?.msg || '同步成功');
            } else {
                ElMessage.error(res.data?.msg);
            }
        } catch (error) {
            isLoading.value = false;
            ElMessage.error(error.message || '同步失败');
        }
    };

    const syncAllGoods = async () => {
        isLoading.value = true;
        try {
            const res = await axios.post("/plugin/Shopnameupdate/api/syncAllGoods");
            isLoading.value = false;
            if (res.data?.code == 200) {
                ElMessage.success(res.data?.msg || '同步成功');
            } else {
                ElMessage.error(res.data?.msg);
            }
        } catch (error) {
            isLoading.value = false;
            ElMessage.error(error.message || '同步失败');
        }
    };

    // 页面加载时获取配置
    fetchData();

    const app = Vue.createApp({
        setup() {
            return {
                isLoading,
                form,
                syncGoods,
                syncAllGoods,
                saveStatus,
                saveAutoSync
            };
        },
    });

    app.use(ElementPlus);
    app.mount("#app");

    window.onload = () => {
        document.getElementById("loading").style.display = "none";
        document.getElementById("app").style.display = "block";
    };
</script>
</body>
</html>
