import { ComponentPublicInstance, ComponentInternalInstance } from 'vue';
export declare function emitted<T = unknown>(vm: ComponentPublicInstance, eventName?: string): undefined | T[] | Record<string, T[]>;
export declare const attachEmitListener: () => void;
export declare const recordEvent: (vm: ComponentInternalInstance, event: string, args: unknown[]) => void;
export declare const removeEventHistory: (vm: ComponentPublicInstance) => void;
