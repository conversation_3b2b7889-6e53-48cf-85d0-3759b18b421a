<?php

namespace plugin\Zeroallsum\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;
use think\facade\Log;

class Api extends BasePlugin {

    protected $scene = [
        'admin'
    ];
    protected $noNeedLogin = [];

    public function index() {
        return View::fetch();
    }

    // 获取配置数据
    public function fetchData() {
        $page = max(1, intval($this->request->post('page/d', 1)));
        $pageSize = 10;
        
        // 获取新增的筛选参数
        $startDate = $this->request->post('startDate', '');
        $endDate = $this->request->post('endDate', '');
        $merchantId = $this->request->post('merchantId', '');

        // 默认的动态表名关键字
        $tableKeyword = 'user_analysis';

        try {
            // 获取所有表名
            $tables = Db::query("SHOW TABLES");

            // 检查查询是否成功
            if (!$tables || empty($tables)) {
                throw new \Exception("没有返回表信息，检查数据库连接是否正常");
            }

            $targetTable = null;

            // 遍历表名，寻找符合条件的表
            foreach ($tables as $table) {
                $tableName = array_values($table)[0]; // 获取表名
                if (strpos($tableName, $tableKeyword) !== false) {
                    $targetTable = $tableName;
                    break;
                }
            }

            // 如果未找到符合条件的表
            if (!$targetTable) {
                throw new \Exception("未找到包含关键字 '{$tableKeyword}' 的表");
            }

            // 查询目标表的数据
            $query = Db::table($targetTable);
            
            // 添加筛选条件
            if ($startDate) {
                $query->where('date', '>=', $startDate);
            }
            if ($endDate) {
                $query->where('date', '<=', $endDate . ' 23:59:59');
            }
            if ($merchantId) {
                $query->where('user_id', '=', $merchantId);
            }

            $total = $query->count();
            $tableData = $query->page($page, $pageSize)->select()->toArray();

            return json([
                'code' => 200,
                'msg' => 'success',
                'data' => [
                    'tableData' => $tableData,
                    'pagination' => [
                        'total' => $total,
                        'page' => $page,
                        'pageSize' => $pageSize,
                        'totalPages' => ceil($total / $pageSize),
                    ],
                ],
            ]);
        } catch (\Exception $e) {
            \think\facade\Log::error('数据库查询失败：' . $e->getMessage());
            return json([
                'code' => 500,
                'msg' => '数据库查询失败：' . $e->getMessage(),
            ]);
        }
    }

    // 清空大屏数据
    public function clearData() {
        try {
            // 获取筛选参数
            $startDate = $this->request->post('startDate', '');
            $endDate = $this->request->post('endDate', '');
            $merchantId = $this->request->post('merchantId', '');

            // 默认的动态表名关键字
            $tableKeyword = 'user_analysis';

            // 获取所有表名
            $tables = Db::query("SHOW TABLES");
            
            // 检查查询是否成功
            if (!$tables || empty($tables)) {
                throw new \Exception("没有返回表信息，检查数据库连接是否正常");
            }

            $targetTable = null;

            // 遍历表名，寻找符合条件的表
            foreach ($tables as $table) {
                $tableName = array_values($table)[0]; // 获取表名
                if (strpos($tableName, $tableKeyword) !== false) {
                    $targetTable = $tableName;
                    break;
                }
            }

            // 如果未找到符合条件的表
            if (!$targetTable) {
                throw new \Exception("未找到包含关键字 '{$tableKeyword}' 的表");
            }

            // 检查是否有筛选条件
            if ($startDate || $endDate || $merchantId) {
                // 有筛选条件时，按条件删除
                $query = Db::table($targetTable);
                if ($startDate) {
                    $query->where('date', '>=', $startDate);
                }
                if ($endDate) {
                    $query->where('date', '<=', $endDate . ' 23:59:59');
                }
                if ($merchantId) {
                    $query->where('user_id', '=', $merchantId);
                }
                $query->delete();
            } else {
                // 没有筛选条件时，直接清空整个表
                Db::execute("TRUNCATE TABLE {$targetTable}");
            }
            
            return json([
                'code' => 200,
                'msg' => '数据已清空',
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '清空失败: ' . $e->getMessage(),
            ]);
        }
    }

    // 获取自动清除配置
    public function getAutoConfig() {
        try {
            $config = [
                'auto_clear' => intval(plugconf("Zeroallsum.auto_clear") ?? 0),
                'clear_type' => plugconf("Zeroallsum.clear_type") ?? 'daily',
                'clear_time' => plugconf("Zeroallsum.clear_time") ?? '00:00',
                'clear_day' => intval(plugconf("Zeroallsum.clear_day") ?? 1),
                'clear_month' => intval(plugconf("Zeroallsum.clear_month") ?? 1),
                'last_clear_time' => plugconf("Zeroallsum.last_clear_time") ?? ''
            ];
            
            return json([
                'code' => 1,
                'msg' => 'success',
                'data' => $config
            ]);
        } catch (\Exception $e) {
            Log::error('获取配置失败：' . $e->getMessage());
            return json([
                'code' => 0,
                'msg' => '获取配置失败：' . $e->getMessage()
            ]);
        }
    }

    // 保存自动清除配置
    public function save() {
        try {
            $autoClear = $this->request->post('auto_clear/d', 0);
            $clearType = $this->request->post('clear_type/s', 'daily');
            $clearTime = $this->request->post('clear_time/s', '00:00');
            $clearDay = $this->request->post('clear_day/d', 1);
            $clearMonth = $this->request->post('clear_month/d', 1);
            
            // 验证时间格式（仅当类型为daily时验证）
            if ($clearType === 'daily' && !preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $clearTime)) {
                return json([
                    'code' => 0,
                    'msg' => '时间格式不正确'
                ]);
            }
            
            // 验证日期范围
            if ($clearType === 'monthly' && ($clearDay < 1 || $clearDay > 31)) {
                return json([
                    'code' => 0,
                    'msg' => '日期必须在1-31之间'
                ]);
            }
            
            // 验证月份范围
            if (in_array($clearType, ['quarterly', 'yearly']) && ($clearMonth < 1 || $clearMonth > 12)) {
                return json([
                    'code' => 0,
                    'msg' => '月份必须在1-12之间'
                ]);
            }
            
            // 保存所有配置项
            plugconf("Zeroallsum.auto_clear", $autoClear);
            plugconf("Zeroallsum.clear_type", $clearType);
            plugconf("Zeroallsum.clear_time", $clearTime);
            plugconf("Zeroallsum.clear_day", $clearDay);
            plugconf("Zeroallsum.clear_month", $clearMonth);
            
            // 如果关闭自动清除，清空最后清除时间
            if ($autoClear === 0) {
                plugconf("Zeroallsum.last_clear_time", '');
            }
            
            return json([
                'code' => 1,
                'msg' => '保存成功'
            ]);
        } catch (\Exception $e) {
            Log::error('保存配置失败：' . $e->getMessage());
            return json([
                'code' => 0,
                'msg' => '保存失败：' . $e->getMessage()
            ]);
        }
    }
}
