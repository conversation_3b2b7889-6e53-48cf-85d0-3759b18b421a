<?php

namespace plugin\Waterchallenge\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;
use think\facade\Log;

class User extends BasePlugin {
    
    protected $scene = ['user'];  // 只允许普通用户访问


    public function index() {
        return View::fetch('user/index');  // 修改这里，指定正确的模板路径
    }
    
    // 获取挑战排行榜数据
    public function getRankingList() {
        try {
            // 从数据库中获取已完成的挑战记录，按照完成时间排序
            $rankings = Db::name('plugin_waterchallenge_user')
                ->where('status', 'completed') // 只查询已完成的
                ->order([
                    'current_amount' => 'desc', // 首先按流水金额排序
                    'complete_time' => 'asc'    // 其次按完成时间排序（越早完成排名越高）
                ])
                ->limit(10) // 只取前10名
                ->select()
                ->toArray();
                
            // 获取用户店铺名称
            if (!empty($rankings)) {
                $userIds = array_column($rankings, 'user_id');
                $userMap = Db::name('user')
                    ->whereIn('id', $userIds)
                    ->column('nickname', 'id');  // 改为获取nickname字段作为店铺名
                    
                foreach ($rankings as &$rank) {
                    // 添加店铺名称，并隐藏部分店铺名
                    $shopname = $userMap[$rank['user_id']] ?? '未知店铺';
                    // 只显示店铺名的前两个和最后一个字符，中间用*替代
                    if (mb_strlen($shopname) > 3) {
                        $rank['shopname'] = mb_substr($shopname, 0, 2) . str_repeat('*', mb_strlen($shopname) - 3) . mb_substr($shopname, -1);
                    } else {
                        $rank['shopname'] = $shopname;
                    }
                    
                    // 确保数值字段为数字类型
                    $rank['target_amount'] = floatval($rank['target_amount']);
                    $rank['current_amount'] = floatval($rank['current_amount']);
                    $rank['reward_amount'] = floatval($rank['reward_amount']);
                    
                    // 计算完成用时（开始时间到完成时间的天数）
                    $startTime = strtotime($rank['start_time']);
                    $completeTime = strtotime($rank['complete_time']);
                    $rank['days_used'] = ceil(($completeTime - $startTime) / 86400); // 向上取整，至少1天
                }
            }
            
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $rankings
            ]);
            
        } catch (\Exception $e) {
            Log::error('获取排行榜数据失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取排行榜数据失败：' . $e->getMessage()]);
        }
    }
    
    // 获取可参与的挑战列表
    public function getChallenges() {
        try {
            $merchantId = $this->user->id;
            
            // 从数据库获取有效的挑战规则
            $validRules = Db::name('plugin_waterchallenge_rules')
                ->where('status', 1)
                ->select()
                ->toArray();
                
            // 筛选在有效期内的规则
            $now = time();
            $validRules = array_filter($validRules, function($rule) use ($now) {
                // 计算规则结束时间
                $createTime = strtotime($rule['create_time']);
                $endTime = $createTime + ($rule['valid_days'] * 86400); // 86400秒 = 1天
                
                // 如果当前时间小于结束时间，规则仍有效
                return $now < $endTime;
            });
            
            // 重置数组索引
            $validRules = array_values($validRules);

            // 获取用户参与每个规则的历史次数
            $ruleIds = array_column($validRules, 'rule_id');
            $participationCounts = [];

            if (!empty($ruleIds)) {
                // 查询用户参与每个规则的次数
                $participationRecords = Db::name('plugin_waterchallenge_user')
                    ->where([
                        ['user_id', '=', $merchantId],
                        ['rule_id', 'in', $ruleIds]
                    ])
                    ->field('rule_id, COUNT(*) as count')
                    ->group('rule_id')
                    ->select()
                    ->toArray();

                foreach ($participationRecords as $record) {
                    $participationCounts[$record['rule_id']] = $record['count'];
                }
            }
                
            // 确保规则中的数值字段是数字类型
            foreach ($validRules as &$rule) {
                $rule['turnover_amount'] = floatval($rule['turnover_amount']);
                $rule['reward_amount'] = floatval($rule['reward_amount']);
                $rule['valid_days'] = intval($rule['valid_days']);
                $rule['challenge_duration'] = intval($rule['challenge_duration']);
                $rule['max_attempts'] = isset($rule['max_attempts']) ? intval($rule['max_attempts']) : 0;
                
                // 添加规则剩余有效天数
                $createTime = strtotime($rule['create_time']);
                $endTime = $createTime + ($rule['valid_days'] * 86400);
                $remainingDays = ceil(($endTime - $now) / 86400);
                $rule['remaining_days'] = $remainingDays;
                
                // 添加用户已参与该规则的次数
                $rule['participated_count'] = isset($participationCounts[$rule['rule_id']]) ? $participationCounts[$rule['rule_id']] : 0;
                
                // 判断用户是否还能参与该规则（0表示无限制）
                $rule['can_participate'] = ($rule['max_attempts'] == 0 || $rule['participated_count'] < $rule['max_attempts']);
                
                // 如果有限制，计算剩余可参与次数
                if ($rule['max_attempts'] > 0) {
                    $rule['remaining_attempts'] = max(0, $rule['max_attempts'] - $rule['participated_count']);
                } else {
                    $rule['remaining_attempts'] = '不限';
                }
            }
            
            // 从数据库中获取当前进行中的挑战
            $currentChallenges = Db::name('plugin_waterchallenge_user')
                ->where([
                    ['user_id', '=', $merchantId],
                    ['status', '=', 'ongoing']
                ])
                ->select()
                ->toArray();
            
            // 从数据库中获取历史挑战记录
            $historyRecords = Db::name('plugin_waterchallenge_user')
                ->where([
                    ['user_id', '=', $merchantId],
                    ['status', '<>', 'ongoing']
                ])
                ->select()
                ->toArray();
            
            // 更新当前挑战的流水数据
            foreach ($currentChallenges as &$challenge) {
                // 确保数据是数字类型
                $challenge['target_amount'] = floatval($challenge['target_amount']);
                $challenge['current_amount'] = floatval($challenge['current_amount']);
                $challenge['reward_amount'] = floatval($challenge['reward_amount']);
                $challenge['base_amount'] = floatval($challenge['base_amount']);

                // 获取当前最新总流水
                $currentTotalAmount = floatval(Db::name('user_analysis')
                    ->where([
                        ['user_id', '=', $merchantId]
                    ])
                    ->sum('total_amount')) ?: 0;

                // 计算挑战流水（当前总流水 - 接受挑战时的基准流水）并保留3位小数
                $baseAmount = $challenge['base_amount'] ?? 0;
                $challenge['current_amount'] = round(max(0, $currentTotalAmount - $baseAmount), 3);

                // 检查是否完成挑战
                $updated = false;
                $statusChanged = false;
                if ($challenge['current_amount'] >= $challenge['target_amount']) {
                    $challenge['status'] = 'completed';
                    $challenge['complete_time'] = date('Y-m-d H:i:s');
                    $updated = true;
                    $statusChanged = true;
                }
                // 检查是否已过期
                elseif (strtotime($challenge['end_time']) < time()) {
                    $challenge['status'] = 'failed';
                    $challenge['complete_time'] = date('Y-m-d H:i:s');
                    $updated = true;
                    $statusChanged = true;
                }

                // 始终更新数据库中的current_amount，确保后端管理界面显示正确
                $updateData = [
                    'current_amount' => $challenge['current_amount'],
                    'update_time' => date('Y-m-d H:i:s')
                ];

                // 如果状态发生变化，同时更新状态和完成时间
                if ($statusChanged) {
                    $updateData['status'] = $challenge['status'];
                    $updateData['complete_time'] = $challenge['complete_time'];
                }

                Db::name('plugin_waterchallenge_user')
                    ->where('id', $challenge['id'])
                    ->update($updateData);
            }
            
            // 检查是否需要自动发放余额
            foreach ($currentChallenges as &$challenge) {
                if ($challenge['status'] === 'completed' && empty($challenge['reward_sent'])) {
                    // 开启事务
                    Db::startTrans();
                    try {
                        // 获取用户当前余额
                        $user = Db::name('user')->where('id', $merchantId)->find();
                        if (!$user) {
                            throw new \Exception('用户不存在');
                        }
                        
                        // 计算新余额
                        $beforeBalance = $user['platform_money'];
                        $afterBalance = bcadd($beforeBalance, $challenge['reward_amount'], 2);
                        
                        // 更新用户余额
                        $result = Db::name('user')
                            ->where('id', $merchantId)
                            ->update(['platform_money' => $afterBalance]);
                            
                        if (!$result) {
                            throw new \Exception('更新用户余额失败');
                        }
                        
                        // 添加余额变更记录
                        $result = Db::name('user_money_log')->insert([
                            'user_id' => $merchantId,
                            'change' => $challenge['reward_amount'],
                            'reason' => '完成流水挑战奖励',
                            'create_time' => time(),
                            'source' => 'Platform'
                        ]);
                        
                        if (!$result) {
                            throw new \Exception('添加余额记录失败');
                        }
                        
                        // 更新挑战记录状态
                        $result = Db::name('plugin_waterchallenge_user')
                            ->where('id', $challenge['id'])
                            ->update([
                                'reward_sent' => 1,
                                'reward_time' => date('Y-m-d H:i:s'),
                                'update_time' => date('Y-m-d H:i:s')
                            ]);
                        
                        if (!$result) {
                            throw new \Exception('更新挑战奖励状态失败');
                        }
                        
                        // 更新内存中的状态
                        $challenge['reward_sent'] = 1;
                        $challenge['reward_time'] = date('Y-m-d H:i:s');
                        
                        Db::commit();
                    } catch (\Exception $e) {
                        Db::rollback();
                        Log::error('自动发放余额失败：' . $e->getMessage());
                    }
                }
            }
            
            // 确保历史记录中的数据也是数字类型
            foreach ($historyRecords as &$record) {
                $record['target_amount'] = floatval($record['target_amount']);
                $record['current_amount'] = floatval($record['current_amount']);
                $record['reward_amount'] = floatval($record['reward_amount']);
                $record['base_amount'] = floatval($record['base_amount']);
            }
            
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => [
                    'rules' => $validRules,
                    'current_challenges' => array_values($currentChallenges),
                    'history' => array_values($historyRecords)
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('获取挑战列表失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }
    
    // 接受挑战
    public function acceptChallenge() {
        try {
            $merchantId = $this->user->id;
            $ruleId = input('post.rule_id/d', '');
            
            if (empty($ruleId)) {
                return json(['code' => 400, 'msg' => '请选择要参与的挑战']);
            }
            
            // 获取当前的总流水作为基准值（保留3位小数）
            $baseAmount = round(floatval(Db::name('user_analysis')
                ->where([
                    ['user_id', '=', $merchantId]
                ])
                ->sum('total_amount')) ?: 0, 3);
            
            // 从数据库中获取规则
            $rule = Db::name('plugin_waterchallenge_rules')
                ->where([
                    ['id', '=', $ruleId],
                    ['status', '=', 1]
                ])
                ->find();
            
            if (!$rule) {
                return json(['code' => 404, 'msg' => '挑战规则不存在或已禁用']);
            }
            
            // 检查规则是否在有效期内
            $now = time();
            $createTime = strtotime($rule['create_time']);
            $endTime = $createTime + ($rule['valid_days'] * 86400); // 86400秒 = 1天
            
            if ($now >= $endTime) {
                return json(['code' => 400, 'msg' => '该挑战已过期，无法参与']);
            }
            
            // 检查用户是否达到参与次数上限
            if (!empty($rule['max_attempts']) && $rule['max_attempts'] > 0) {
                // 查询用户参与该规则的次数
                $participationCount = Db::name('plugin_waterchallenge_user')
                    ->where([
                        ['user_id', '=', $merchantId],
                        ['rule_id', '=', $rule['rule_id']]
                    ])
                    ->count();
                
                if ($participationCount >= $rule['max_attempts']) {
                    return json([
                        'code' => 400, 
                        'msg' => '您已达到该挑战的最大参与次数（' . $rule['max_attempts'] . '次）'
                    ]);
                }
            }
            
            // 检查是否已有进行中的挑战
            $hasOngoing = Db::name('plugin_waterchallenge_user')
                ->where([
                    ['user_id', '=', $merchantId],
                    ['status', '=', 'ongoing']
                ])
                ->count();
            
            if ($hasOngoing) {
                return json(['code' => 400, 'msg' => '您已有进行中的挑战，请完成后再参与新的挑战']);
            }
            
            // 创建新的挑战记录
            $challengeId = Db::name('plugin_waterchallenge_user')->insertGetId([
                'user_id' => $merchantId,
                'challenge_id' => uniqid(),
                'rule_id' => $rule['rule_id'],
                'rule_name' => $rule['name'],
                'current_amount' => 0.000,  // 初始值设为0.000
                'base_amount' => $baseAmount,
                'target_amount' => round($rule['turnover_amount'], 3),
                'status' => 'ongoing',
                'start_time' => date('Y-m-d H:i:s'),
                'end_time' => date('Y-m-d H:i:s', strtotime("+{$rule['challenge_duration']} days")),
                'reward_amount' => round($rule['reward_amount'], 2),
                'reward_sent' => 0,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ]);
            
            // 获取新创建的记录
            $newRecord = Db::name('plugin_waterchallenge_user')
                ->where('id', $challengeId)
                ->find();
            
            return json(['code' => 200, 'msg' => '接受挑战成功', 'data' => $newRecord]);
            
        } catch (\Exception $e) {
            Log::error('接受挑战失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '接受失败：' . $e->getMessage()]);
        }
    }

    // 随机接取挑战
    public function randomAcceptChallenge() {
        try {
            $merchantId = $this->user->id;

            // 检查是否已有进行中的挑战
            $hasOngoing = Db::name('plugin_waterchallenge_user')
                ->where([
                    ['user_id', '=', $merchantId],
                    ['status', '=', 'ongoing']
                ])
                ->count();

            if ($hasOngoing) {
                return json(['code' => 400, 'msg' => '您已有进行中的挑战，请完成后再参与新的挑战']);
            }

            // 从数据库中获取有效的挑战规则
            $validRules = Db::name('plugin_waterchallenge_rules')
                ->where('status', 1)
                ->select()
                ->toArray();

            if (empty($validRules)) {
                return json(['code' => 404, 'msg' => '暂无可参与的挑战规则']);
            }

            // 筛选在有效期内的规则
            $now = time();
            $availableRules = [];

            foreach ($validRules as $rule) {
                // 计算规则结束时间
                $createTime = strtotime($rule['create_time']);
                $endTime = $createTime + ($rule['valid_days'] * 86400);

                // 如果当前时间小于结束时间，规则仍有效
                if ($now < $endTime) {
                    // 检查用户是否达到参与次数上限
                    $canParticipate = true;
                    if (!empty($rule['max_attempts']) && $rule['max_attempts'] > 0) {
                        $participationCount = Db::name('plugin_waterchallenge_user')
                            ->where([
                                ['user_id', '=', $merchantId],
                                ['rule_id', '=', $rule['rule_id']]
                            ])
                            ->count();

                        if ($participationCount >= $rule['max_attempts']) {
                            $canParticipate = false;
                        }
                    }

                    if ($canParticipate) {
                        $availableRules[] = $rule;
                    }
                }
            }

            if (empty($availableRules)) {
                return json(['code' => 404, 'msg' => '暂无可参与的挑战规则']);
            }

            // 随机选择一个规则
            $randomIndex = array_rand($availableRules);
            $selectedRule = $availableRules[$randomIndex];

            // 获取当前的总流水作为基准值（保留3位小数）
            $baseAmount = round(floatval(Db::name('user_analysis')
                ->where([
                    ['user_id', '=', $merchantId]
                ])
                ->sum('total_amount')) ?: 0, 3);

            // 创建新的挑战记录
            $challengeId = Db::name('plugin_waterchallenge_user')->insertGetId([
                'user_id' => $merchantId,
                'challenge_id' => uniqid(),
                'rule_id' => $selectedRule['rule_id'],
                'rule_name' => $selectedRule['name'],
                'current_amount' => 0.000,
                'base_amount' => $baseAmount,
                'target_amount' => round($selectedRule['turnover_amount'], 3),
                'status' => 'ongoing',
                'start_time' => date('Y-m-d H:i:s'),
                'end_time' => date('Y-m-d H:i:s', strtotime("+{$selectedRule['challenge_duration']} days")),
                'reward_amount' => round($selectedRule['reward_amount'], 2),
                'reward_sent' => 0,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ]);

            // 获取新创建的记录
            $newRecord = Db::name('plugin_waterchallenge_user')
                ->where('id', $challengeId)
                ->find();

            return json([
                'code' => 200,
                'msg' => '随机接取挑战成功！挑战名称：' . $selectedRule['name'],
                'data' => $newRecord
            ]);

        } catch (\Exception $e) {
            Log::error('随机接取挑战失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '随机接取失败：' . $e->getMessage()]);
        }
    }
}