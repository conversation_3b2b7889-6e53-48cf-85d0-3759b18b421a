<?php

namespace app\payApi\controller;

use app\common\controller\BasePay;

class Xunhupay extends BasePay {

    public static $config = [
        "account_fields" => [
            [
                'id' => 'appid',
                'name' => 'APPID',
                'placeholder' => '请输入虎皮椒APPID',
                'required' => true,
                'type' => 'input',
            ],
            [
                'id' => 'appsecret',
                'name' => '密钥',
                'placeholder' => '请输入虎皮椒密钥',
                'required' => true,
                'type' => 'input',
            ],
            [
                'id' => 'gateway',
                'name' => '支付网关',
                'placeholder' => '请输入支付网关',
                'required' => true,
                'type' => 'input',
                'default' => 'https://api.xunhupay.com/payment/do.html'
            ],
            [
                'id' => 'type',
                'name' => '支付类型',
                'placeholder' => '请选择支付类型',
                'required' => true,
                'type' => 'radio',
                'data' => [
                    [
                        'name' => '支付宝',
                        'value' => 'alipay',
                    ],
                    [
                        'name' => '微信H5支付',
                        'value' => 'WAP',
                    ],
                    [
                        'name' => '微信小程序支付',
                        'value' => 'JSAPI',
                    ],
                ],
            ],
        ],
        "remark" => "虎皮椒支付接口",
    ];

    /**
     * 支付入口
     * @param string $trade_no 系统单号
     */
    public function pay($trade_no) {
        $order = $this->loadOrder($trade_no);
        
        // 获取配置参数
        $appid = $order->channelAccount->params->appid;
        $appsecret = $order->channelAccount->params->appsecret;
        $gateway = $order->channelAccount->params->gateway;
        $pay_type = $order->channelAccount->params->type;

        // 构建请求参数
        $params = array(
            'version' => '1.1',
            'appid' => $appid,
            'trade_order_id' => $trade_no,
            'total_fee' => $order->total_amount,
            'title' => $order->title,
            'time' => time(),
            'notify_url' => (string)url("payApi/Xunhupay/notify", [], false, true),
            'return_url' => (string)url("payApi/Xunhupay/callback", [], false, true),
            'callback_url' => $order->callbackUrl(),
            'nonce_str' => $this->createNonceStr(),
            'type' => $pay_type,
            'wap_url' => request()->domain(),
            'wap_name' => sysconf('website.title'),
            'redirect' => 'Y'
        );

        // 生成签名
        ksort($params);
        $arg = "";
        foreach ($params as $key => $val) {
            if (is_null($val) || $val === '') continue;
            $arg .= "{$key}={$val}&";
        }
        $arg = trim($arg, '&');
        $params['hash'] = md5($arg . $appsecret);

        // 构建支付URL
        $payUrl = $gateway . '?' . http_build_query($params);

        // 根据设备类型处理
        if ($this->isMobile()) {
            // 移动端直接跳转
            header("location: " . $payUrl);
            exit;
        } else {
            // PC端显示移动支付URL的二维码
            return $this->qrcode($order, $payUrl, [
                'title' => '请使用手机扫码支付',
                'amount' => $order->total_amount
            ]);
        }
    }

    /**
     * 判断是否为移动端访问
     * @return boolean
     */
    private function isMobile() {
        if (isset($_SERVER['HTTP_USER_AGENT'])) {
            $userAgent = strtolower($_SERVER['HTTP_USER_AGENT']);
            $mobileKeywords = [
                'mobile', 'android', 'iphone', 'ipod', 'ipad', 'windows phone',
                'webos', 'blackberry', 'iemobile', 'opera mini', 'mobi'
            ];
            
            foreach ($mobileKeywords as $keyword) {
                if (strpos($userAgent, $keyword) !== false) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 发送POST请求
     * @param string $url
     * @param array $data
     * @return string
     */
    private function httpPost($url, $data) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        $response = curl_exec($ch);
        curl_close($ch);
        return $response;
    }

    /**
     * 同步回调
     */
    public function callback() {
        $trade_order_id = input('trade_order_id/s', '');
        if (empty($trade_order_id)) {
            $this->error('订单号不存在');
        }

        $order = $this->loadOrder($trade_order_id);
        if (!$order) {
            $this->error('订单不存在');
        }

        // 等待异步通知处理完成（最多等待3秒）
        $retry = 0;
        while ($retry < 3) {
            if ($order->status === 'paid') {
                break;
            }
            sleep(1);
            $order = $this->loadOrder($trade_order_id); // 重新加载订单状态
            $retry++;
        }

        // 检查订单状态
        if ($order->status === 'paid') {
            // 支付成功，跳转到订单结果页面
            return redirect('/order/result/' . $trade_order_id);
        } else {
            // 主动查询订单状态
            $result = $this->queryOrder($order);
            if ($result && $result['status'] === 'OD') {
                // 如果查询到支付成功，更新订单状态
                $order->transaction_id = $result['transaction_id'];
                $order->completeOrder();
                return redirect('/order/result/' . $trade_order_id);
            }
        }

        $this->error('支付结果确认中，请稍后刷新页面');
    }

    /**
     * 主动查询订单状态
     */
    private function queryOrder($order) {
        $params = [
            'appid' => $order->channelAccount->params->appid,
            'trade_order_id' => $order->trade_no,
            'nonce_str' => $this->createNonceStr(),
            'time' => time()
        ];

        // 生成签名
        ksort($params);
        $arg = "";
        foreach ($params as $key => $val) {
            if (is_null($val) || $val === '') continue;
            $arg .= "{$key}={$val}&";
        }
        $arg = trim($arg, '&');
        $params['hash'] = md5($arg . $order->channelAccount->params->appsecret);

        // 发起查询请求
        $gateway = str_replace('/payment/do.html', '/payment/query.html', $order->channelAccount->params->gateway);
        $response = $this->httpPost($gateway, $params);
        $result = json_decode($response, true);

        if (!empty($result) && isset($result['status'])) {
            return $result;
        }
        return false;
    }

/**
 * 异步通知
 */
public function notify() {
    $params = input('post.');
    if (empty($params)) {
        \think\facade\Log::error('回调参数为空');
        echo 'error';
        exit;
    }

    // 记录原始回调数据
    \think\facade\Log::info('虎皮椒支付原始回调数据：'.json_encode($params));

    // 加载订单
    if (empty($params['trade_order_id'])) {
        \think\facade\Log::error('缺少订单号参数');
        echo 'error';
        exit;
    }
    
    $order = $this->loadOrder($params['trade_order_id']);
    if (empty($order)) {
        \think\facade\Log::error('订单不存在：'.$params['trade_order_id']);
        echo 'error';
        exit;
    }

    // 订单已支付直接返回成功
    if ($order->status === 'paid') {
        \think\facade\Log::info('订单已处理：'.$params['trade_order_id']);
        echo 'success';
        exit;
    }

    // 参数过滤（保留空值参数）
    $verify_params = $this->paraFilter($params);
    
    // 生成待签名字符串
    $prestr = $this->createLinkstring($verify_params);
    
    // 调试日志
    \think\facade\Log::info('待签名字符串：'.$prestr);
    \think\facade\Log::info('使用的密钥：'.$order->channelAccount->params->appsecret);
    \think\facade\Log::info('生成签名：'.md5($prestr . $order->channelAccount->params->appsecret));
    \think\facade\Log::info('收到签名：'.$params['hash']);

    // 验证签名
    $isValidSign = $this->md5Verify($prestr, $params['hash'], $order->channelAccount->params->appsecret);
    
    if (!$isValidSign) {
        \think\facade\Log::error('签名验证失败：'.$params['trade_order_id']);
        echo 'error';
        exit;
    }

    // 验证支付状态
    if ($params['status'] === 'OD') { // 确认虎皮椒成功状态码
        // 更新订单状态
        $order->transaction_id = $params['transaction_id'] ?? '';
        if ($order->completeOrder()) {
            \think\facade\Log::info('订单支付成功：'.$params['trade_order_id']);
            echo 'success';
        } else {
            \think\facade\Log::error('订单状态更新失败：'.$params['trade_order_id']);
            echo 'error';
        }
    } else {
        \think\facade\Log::error('支付未成功，状态：'.$params['status']);
        echo 'error';
    }
    exit;
}

/**
 * 参数过滤（保留空值参数）
 */
private function paraFilter($para) {
    $para_filter = array();
    foreach ($para as $key => $val) {
        if ($key === 'hash') continue; // 仅排除hash参数
        $para_filter[$key] = $val;
    }
    return $para_filter;
}
    /**
     * 创建链接字符串
     */
    private function createLinkstring($para) {
        ksort($para);
        $arg = "";
        foreach ($para as $key => $val) {
            $arg .= "{$key}={$val}&";
        }
        return trim($arg, '&');
    }

    /**
     * MD5验证
     */
    private function md5Verify($prestr, $sign, $key) {
        $mysign = md5($prestr . $key);
        return $mysign === $sign;
    }

    /**
     * 生成随机字符串
     */
    private function createNonceStr($length = 32) {
        $chars = "abcdefghijklmnopqrstuvwxyz0123456789";
        $str = "";
        for ($i = 0; $i < $length; $i++) {
            $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
        }
        return $str;
    }

    /**
     * 退款接口
     * @param object $order 订单对象
     * @param float $amount 退款金额
     * @return boolean
     */
    public function refund($order, $amount) {
        // 虎皮椒支付暂不支持退款接口
        $this->setError('当前支付渠道不支持退款');
        return false;
    }

    /**
     * 添加URL参数
     */
    private function appendParam($url, $params) {
        if (strpos($url, '?') !== false) {
            $url .= '&';
        } else {
            $url .= '?';
        }
        return $url . http_build_query($params);
    }
} 