#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
批量自动化注册器
使用Selenium进行批量用户注册
"""

import time
import random
import threading
from typing import List, Dict
import sys
import os
# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from browser_automation.selenium_register import SeleniumRegister
from config.config_manager import get_config
from core.sms_api import YeziCloudAPI

class BatchRegister:
    """批量注册器"""
    
    def __init__(self):
        """初始化批量注册器"""
        self.config = get_config()
        self.selenium_config = self.config.get_selenium_config()
        self.register_config = self.config.get_register_config_enhanced()
        self.yezi_config = self.config.get_yezi_cloud_config()
        
        self.results = []
        self.lock = threading.Lock()
        self.yezi_api = None
        
        # 初始化椰子云API
        if self.config.is_yezi_cloud_configured():
            self.setup_yezi_cloud()
    
    def setup_yezi_cloud(self) -> bool:
        """设置椰子云API"""
        try:
            self.yezi_api = YeziCloudAPI(
                username=self.yezi_config['username'],
                password=self.yezi_config['password']
            )
            
            login_result = self.yezi_api.login()
            if login_result['success']:
                print(f"[成功] 椰子云登录成功，余额: {login_result.get('balance', '0')}")
                return True
            else:
                print(f"[失败] 椰子云登录失败: {login_result['error']}")
                return False
                
        except Exception as e:
            print(f"[失败] 椰子云设置失败: {e}")
            return False
    
    def generate_user_data(self, count: int) -> List[Dict[str, str]]:
        """
        生成用户数据
        
        Args:
            count: 生成数量
            
        Returns:
            用户数据列表
        """
        users = []
        basic_config = self.config.get_section('BASIC')
        
        username_length = int(basic_config.get('username_length', '8'))
        password_length = int(basic_config.get('password_length', '8'))
        username_prefix = basic_config.get('username_prefix', '')
        
        for i in range(count):
            # 生成用户名
            if username_prefix:
                username = username_prefix + ''.join(random.choices('0123456789', k=username_length-len(username_prefix)))
            else:
                username = ''.join(random.choices('0123456789', k=username_length))
            
            # 生成密码（使用相同的用户名）
            password = username
            
            users.append({
                'username': username,
                'password': password,
                'index': i + 1
            })
        
        return users
    
    def register_single_user(self, user_data: Dict[str, str], thread_id: int):
        """
        注册单个用户
        
        Args:
            user_data: 用户数据
            thread_id: 线程ID
        """
        register = None
        try:
            print(f"[启动] 线程{thread_id} - 开始注册用户 {user_data['index']}: {user_data['username']}")
            
            # 创建Selenium注册器
            register = SeleniumRegister(
                headless=self.selenium_config['headless'],
                use_yezi_cloud=bool(self.yezi_api)
            )
            
            # 如果有椰子云API，设置项目ID
            if self.yezi_api:
                register.yezi_api = self.yezi_api
                register.project_id = self.yezi_config['project_id']
            
            # 执行注册
            result = register.register_user(
                username=user_data['username'],
                password=user_data['password']
            )
            
            # 记录结果
            with self.lock:
                result['thread_id'] = thread_id
                result['user_index'] = user_data['index']
                self.results.append(result)
            
            if result['success']:
                print(f"[成功] 线程{thread_id} - 用户 {user_data['index']} 注册成功: {user_data['username']}")
            else:
                print(f"[失败] 线程{thread_id} - 用户 {user_data['index']} 注册失败: {result['error']}")
            
            # 添加延迟
            delay = random.uniform(
                self.register_config['delay_min'],
                self.register_config['delay_max']
            )
            time.sleep(delay)
            
        except Exception as e:
            print(f"[失败] 线程{thread_id} - 用户 {user_data['index']} 注册异常: {e}")
            
            with self.lock:
                self.results.append({
                    'success': False,
                    'error': f'注册异常: {str(e)}',
                    'thread_id': thread_id,
                    'user_index': user_data['index'],
                    'username': user_data['username']
                })
        finally:
            if register:
                register.close()
    
    def batch_register(self, count: int) -> List[Dict]:
        """
        批量注册用户
        
        Args:
            count: 注册数量
            
        Returns:
            注册结果列表
        """
        print(f"[启动] 开始批量注册 {count} 个用户")
        print("=" * 60)
        
        # 生成用户数据
        users = self.generate_user_data(count)
        
        # 清空结果
        self.results = []
        
        # 创建线程池
        max_threads = min(self.register_config['max_threads'], count)
        threads = []
        
        print(f"[进度] 配置信息:")
        print(f"   最大线程数: {max_threads}")
        print(f"   延迟范围: {self.register_config['delay_min']}-{self.register_config['delay_max']}秒")
        print(f"   椰子云: {'[成功] 已配置' if self.yezi_api else '[失败] 未配置'}")
        print(f"   无头模式: {'[成功] 开启' if self.selenium_config['headless'] else '[失败] 关闭'}")
        print()
        
        # 分配用户到线程
        users_per_thread = len(users) // max_threads
        remaining_users = len(users) % max_threads
        
        start_index = 0
        for thread_id in range(max_threads):
            # 计算当前线程处理的用户数量
            current_count = users_per_thread + (1 if thread_id < remaining_users else 0)
            thread_users = users[start_index:start_index + current_count]
            start_index += current_count
            
            if thread_users:
                # 为每个线程创建独立的处理函数
                def create_thread_func(tid, tusers):
                    def thread_func():
                        for user in tusers:
                            self.register_single_user(user, tid)
                    return thread_func
                
                thread = threading.Thread(
                    target=create_thread_func(thread_id + 1, thread_users),
                    name=f"RegisterThread-{thread_id + 1}"
                )
                threads.append(thread)
        
        # 启动所有线程
        start_time = time.time()
        for thread in threads:
            thread.start()
            time.sleep(0.5)  # 错开启动时间
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 统计结果
        success_count = sum(1 for r in self.results if r['success'])
        fail_count = len(self.results) - success_count
        
        print(f"\n[进度] 批量注册完成")
        print("=" * 60)
        print(f"总用户数: {count}")
        print(f"成功注册: {success_count}")
        print(f"注册失败: {fail_count}")
        print(f"成功率: {success_count/count*100:.1f}%")
        print(f"总耗时: {total_time:.1f}秒")
        print(f"平均耗时: {total_time/count:.1f}秒/用户")
        
        return self.results
    
    def save_results(self, filename: str = None):
        """
        保存注册结果到文件
        
        Args:
            filename: 文件名，默认使用时间戳
        """
        if not filename:
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"register_results_{timestamp}.txt"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("970faka.com 批量注册结果\n")
                f.write("=" * 50 + "\n\n")
                
                success_users = [r for r in self.results if r['success']]
                fail_users = [r for r in self.results if not r['success']]
                
                f.write(f"成功注册用户 ({len(success_users)}个):\n")
                f.write("-" * 30 + "\n")
                for result in success_users:
                    f.write(f"用户名: {result.get('username', 'N/A')}\n")
                    f.write(f"密码: {result.get('password', 'N/A')}\n")
                    f.write(f"手机号: {result.get('mobile', 'N/A')}\n")
                    f.write(f"线程: {result.get('thread_id', 'N/A')}\n")
                    f.write("-" * 20 + "\n")
                
                f.write(f"\n失败用户 ({len(fail_users)}个):\n")
                f.write("-" * 30 + "\n")
                for result in fail_users:
                    f.write(f"用户名: {result.get('username', 'N/A')}\n")
                    f.write(f"错误: {result.get('error', 'N/A')}\n")
                    f.write(f"步骤: {result.get('step', 'N/A')}\n")
                    f.write(f"线程: {result.get('thread_id', 'N/A')}\n")
                    f.write("-" * 20 + "\n")
            
            print(f"[成功] 结果已保存到: {filename}")
            
        except Exception as e:
            print(f"[失败] 保存结果失败: {e}")

def main():
    """主函数"""
    print("🤖 970faka.com 批量自动化注册器")
    print("=" * 60)
    
    # 检查配置
    config = get_config()
    if not config.is_yezi_cloud_configured():
        print("[警告] 椰子云未配置，请先在config.ini中配置椰子云信息")
        print("   username = 你的椰子云用户名")
        print("   password = 你的椰子云密码")
        print("   project_id = 你的椰子云项目ID")
        
        use_manual = input("\n是否继续使用手动输入短信验证码模式？(y/n): ").strip().lower()
        if use_manual != 'y':
            return
    
    # 获取注册数量
    try:
        count = int(input("请输入要注册的用户数量: ").strip())
        if count <= 0:
            print("[失败] 数量必须大于0")
            return
    except ValueError:
        print("[失败] 请输入有效的数字")
        return
    
    # 创建批量注册器
    batch_register = BatchRegister()
    
    # 执行批量注册
    results = batch_register.batch_register(count)
    
    # 保存结果
    save_file = input("\n是否保存结果到文件？(y/n): ").strip().lower()
    if save_file == 'y':
        batch_register.save_results()

if __name__ == "__main__":
    main()
