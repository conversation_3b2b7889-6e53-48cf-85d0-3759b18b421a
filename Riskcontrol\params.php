<?php

return [
    // 基础配置
    "status" => 0, // 总开关
    "check_interval" => 30, // 检查间隔(分钟)
    
    // 阿里云配置
    "aliyun_status" => 0,
    "aliyun_access_key" => "",
    "aliyun_access_secret" => "",
    "aliyun_region" => "cn-shanghai",
    
    // 腾讯云配置  
    "tencent_status" => 0,
    "tencent_secret_id" => "",
    "tencent_secret_key" => "",
    "tencent_region" => "ap-guangzhou",
    
    // 百度云配置
    "baidu_status" => 0,
    "baidu_app_id" => "",
    "baidu_api_key" => "",
    "baidu_secret_key" => "",
    
    // 处理配置
    "action_type" => "mark", // mark=标记/delete=删除
    "notify_admin" => 1, // 通知管理员
    "notify_content" => "商品ID:{id} 标题或描述存在违规内容,请及时处理"
]; 