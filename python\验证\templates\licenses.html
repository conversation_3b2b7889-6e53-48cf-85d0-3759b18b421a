<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡密验证系统 - 卡密管理</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background: linear-gradient(180deg, #4a00e0 0%, #8e2de2 100%);
            min-height: 100vh;
            padding-top: 20px;
            box-shadow: 4px 0 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            position: fixed;
            width: inherit;
            max-width: inherit;
        }
        .sidebar-header {
            padding: 0 15px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }
        .sidebar a {
            color: rgba(255, 255, 255, 0.8);
            display: block;
            padding: 12px 20px;
            text-decoration: none;
            transition: all 0.3s;
            margin-bottom: 5px;
            border-left: 3px solid transparent;
        }
        .sidebar a:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: #fff;
            border-left: 3px solid #fff;
        }
        .sidebar a.active {
            background-color: rgba(255, 255, 255, 0.2);
            color: #fff;
            border-left: 3px solid #fff;
        }
        .sidebar i {
            margin-right: 10px;
        }
        .main-content {
            padding: 25px;
            margin-left: 225px;
            transition: margin-left 0.3s;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 25px;
            border: none;
            transition: transform 0.3s;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card-title {
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
        }
        .stats-card {
            padding: 20px;
            border-left: 4px solid;
            display: flex;
            align-items: center;
            height: 100%;
            background: white;
        }
        .stats-icon {
            background-color: rgba(74, 0, 224, 0.1);
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .stats-icon i {
            font-size: 20px;
        }
        .stats-info {
            flex-grow: 1;
        }
        .stats-info h5 {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 5px;
        }
        .stats-info h3 {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 0;
        }
        .all-licenses { border-left-color: #4a00e0; }
        .all-licenses .stats-icon i { color: #4a00e0; }
        
        .activated-licenses { border-left-color: #00c851; }
        .activated-licenses .stats-icon i { color: #00c851; }
        
        .expired-licenses { border-left-color: #ff4444; }
        .expired-licenses .stats-icon i { color: #ff4444; }
        
        .banned-licenses { border-left-color: #ff8800; }
        .banned-licenses .stats-icon i { color: #ff8800; }
        .search-container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }
        .table-container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }
        .license-key {
            font-family: SFMono-Regular, Menlo, Monaco, Consolas, monospace;
            background-color: #f5f5f5;
            padding: 3px 6px;
            border-radius: 4px;
            font-size: 14px;
        }
        .pagination {
            margin-top: 20px;
            justify-content: center;
        }
        .pagination .page-item .page-link {
            color: #4a00e0;
            border-radius: 5px;
            margin: 0 3px;
        }
        .pagination .page-item.active .page-link {
            background-color: #4a00e0;
            border-color: #4a00e0;
            color: white;
        }
        .pagination .page-item .page-link:hover {
            background-color: #f0e6ff;
        }
        .empty-state {
            text-align: center;
            padding: 40px 0;
        }
        .empty-state i {
            font-size: 48px;
            color: #d1d1d1;
            margin-bottom: 10px;
        }
        .empty-state h4 {
            font-weight: 500;
            color: #6c757d;
            margin-bottom: 15px;
        }
        .badge-count {
            background-color: #f0e6ff;
            color: #4a00e0;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-left: 5px;
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
            color: #495057;
        }
        .action-btn {
            width: 32px;
            height: 32px;
            padding: 0;
            line-height: 32px;
            text-align: center;
            margin-right: 5px;
        }
        .badge-status {
            padding: 5px 10px;
            border-radius: 20px;
            font-weight: 500;
            font-size: 12px;
        }
        .status-active {
            background-color: #e3f2fd;
            color: #0d6efd;
        }
        .status-inactive {
            background-color: #f8f9fa;
            color: #6c757d;
        }
        .status-expired {
            background-color: #feecf0;
            color: #dc3545;
        }
        .status-banned {
            background-color: #fff8e1;
            color: #fd7e14;
        }
        .select-all-container {
            margin-bottom: 10px;
        }
        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
        }
        .table {
            margin-bottom: 0;
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
            color: #495057;
            white-space: nowrap;
        }
        .btn-group-sm .action-btn {
            width: 28px;
            height: 28px;
            line-height: 28px;
            font-size: 12px;
        }
        
        /* 移动设备适配 */
        @media (max-width: 991.98px) {
            .mobile-header {
                display: flex;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                height: 60px;
                background: white;
                z-index: 1040;
                align-items: center;
                justify-content: center;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            }
            
            .mobile-header h4 {
                margin: 0;
                font-weight: 600;
                color: #4a00e0;
            }
            
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s;
                width: 250px;
                z-index: 1100;
            }
            
            .sidebar.show {
                transform: translateX(0);
                box-shadow: 5px 0 15px rgba(0, 0, 0, 0.2);
            }
            
            .main-content {
                margin-left: 0;
                padding: 15px;
                padding-top: 60px;  /* 为顶部菜单按钮留出空间 */
            }
            
            .toggle-sidebar {
                position: fixed;
                top: 15px;
                left: 15px;
                z-index: 1050;
                border-radius: 50%;
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #4a00e0;
                color: white;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
                border: none;
            }
            
            .table-responsive {
                margin: 0 -15px;
            }
            
            /* 表格调整 */
            .table th, .table td {
                white-space: nowrap;
            }
            
            .license-key {
                max-width: 100px;
                overflow: hidden;
                text-overflow: ellipsis;
                display: inline-block;
            }
            
            /* 用于页面加载时防止闪烁 */
            body.loading .sidebar {
                display: none;
            }
            
            .mobile-overlay {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 1080;
            }
            
            .mobile-overlay.show {
                display: block;
            }
        }
    </style>
</head>
<body class="loading">
<!-- 移动设备遮罩层 -->
<div class="mobile-overlay" id="mobileOverlay"></div>

<!-- 移动设备顶部标题 -->
<div class="mobile-header d-lg-none">
    <h4>卡密验证系统</h4>
</div>

<div class="container-fluid">
    <div class="row">
        <!-- 移动设备菜单按钮 -->
        <button class="toggle-sidebar d-lg-none" id="toggleSidebar">
            <i class="fas fa-bars"></i>
        </button>
        
        <!-- 侧边栏 -->
        <div class="col-lg-2 col-md-3 px-0">
            <div class="sidebar" id="sidebar">
                <div class="sidebar-header">
                    <h4 class="text-white mb-0">卡密验证系统</h4>
                    <p class="text-white-50 mb-0">管理面板</p>
                </div>
                <a href="/dashboard"><i class="fas fa-tachometer-alt"></i> 仪表盘</a>
                <a href="/licenses" class="active"><i class="fas fa-key"></i> 卡密管理</a>
                <a href="/access_logs"><i class="fas fa-history"></i> 访问日志</a>
                <a href="/logout"><i class="fas fa-sign-out-alt"></i> 退出登录</a>
            </div>
        </div>
        
        <!-- 主内容区 -->
        <div class="col-lg-10 col-md-9 main-content">
            <h2 class="mb-4">卡密管理</h2>
            
            <div class="row mb-4">
                <!-- 统计卡片 -->
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card stats-card all-licenses">
                        <div class="stats-icon">
                            <i class="fas fa-key"></i>
                        </div>
                        <div class="stats-info">
                            <h5>卡密总数</h5>
                            <h3>{{ total_licenses }}</h3>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card stats-card activated-licenses">
                        <div class="stats-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stats-info">
                            <h5>已激活</h5>
                            <h3>{{ activated_licenses }}</h3>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card stats-card expired-licenses">
                        <div class="stats-icon">
                            <i class="fas fa-calendar-times"></i>
                        </div>
                        <div class="stats-info">
                            <h5>已过期</h5>
                            <h3>{{ expired_licenses }}</h3>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card stats-card banned-licenses">
                        <div class="stats-icon">
                            <i class="fas fa-ban"></i>
                        </div>
                        <div class="stats-info">
                            <h5>已封禁</h5>
                            <h3>{{ banned_licenses }}</h3>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 搜索栏 -->
            <div class="search-container">
                <form method="get" action="{{ url_for('licenses') }}" class="row g-3">
                    <div class="col-lg-5 col-md-12 mb-2 mb-lg-0">
                        <div class="input-group">
                            <input type="text" class="form-control" name="search" value="{{ search }}" placeholder="搜索卡密...">
                            <select class="form-select" name="filter_type" style="max-width: 150px;">
                                <option value="key" {% if filter_type == 'key' %}selected{% endif %}>卡密</option>
                                <option value="software_id" {% if filter_type == 'software_id' %}selected{% endif %}>软件ID</option>
                                <option value="user_id" {% if filter_type == 'user_id' %}selected{% endif %}>用户ID</option>
                                <option value="device_id" {% if filter_type == 'device_id' %}selected{% endif %}>设备ID</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-2 mb-lg-0">
                        <select class="form-select" name="status_filter">
                            <option value="all" {% if status_filter == 'all' %}selected{% endif %}>所有状态</option>
                            <option value="active" {% if status_filter == 'active' %}selected{% endif %}>已激活</option>
                            <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>未激活</option>
                            <option value="expired" {% if status_filter == 'expired' %}selected{% endif %}>已过期</option>
                            <option value="banned" {% if status_filter == 'banned' %}selected{% endif %}>已封禁</option>
                        </select>
                    </div>
                    <div class="col-lg-2 col-md-6 mb-2 mb-lg-0">
                        <button class="btn btn-primary w-100" type="submit">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                    </div>
                    <div class="col-lg-2 col-md-12">
                        <a href="{{ url_for('create_license') }}" class="btn btn-success w-100">
                            <i class="fas fa-plus"></i> 创建卡密
                        </a>
                    </div>
                </form>
            </div>
            
            <!-- 表格内容 -->
            <div class="table-container">
                <form id="batch-action-form" method="post">
                    <div class="select-all-container d-flex align-items-center mb-3">
                        <div class="form-check me-2">
                            <input class="form-check-input" type="checkbox" id="selectAll">
                            <label class="form-check-label" for="selectAll">全选</label>
                        </div>
                        <input type="hidden" id="selectedIds" name="selectedIds" value="">
                        <input type="hidden" id="batchAction" name="batchAction" value="">
                        <span class="ms-2 text-muted" id="selectedCount"></span>
                    </div>
                    
                    <div class="btn-toolbar mb-3">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-danger" id="batchDeleteBtn" disabled>
                                <i class="fas fa-trash-alt"></i> 批量删除
                            </button>
                        </div>
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-warning" id="batchBanBtn" disabled>
                                <i class="fas fa-ban"></i> 批量封禁
                            </button>
                        </div>
                        <div class="btn-group">
                            <button type="button" class="btn btn-info" id="batchUnbanBtn" disabled>
                                <i class="fas fa-unlock"></i> 批量解封
                            </button>
                        </div>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead>
                                <tr>
                                    <th width="40px" class="text-center">
                                        <input class="form-check-input" type="checkbox" id="selectAll">
                                    </th>
                                    <th>卡密</th>
                                    <th>软件ID</th>
                                    <th>用户ID</th>
                                    <th>状态</th>
                                    <th class="d-none d-md-table-cell">创建日期</th>
                                    <th class="d-none d-md-table-cell">有效期至</th>
                                    <th class="text-end">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if licenses %}
                                    {% for license in licenses %}
                                    <tr>
                                        <td class="text-center">
                                            <input class="form-check-input license-checkbox" type="checkbox" name="license_ids" value="{{ license.id }}">
                                        </td>
                                        <td><span class="license-key">{{ license.key }}</span></td>
                                        <td>{{ license.software_id }}</td>
                                        <td>{{ license.user_id or '无' }}</td>
                                        <td>
                                            {% if license.is_banned %}
                                            <span class="badge badge-status status-banned">已封禁</span>
                                            {% elif license.valid_until < current_date %}
                                            <span class="badge badge-status status-expired">已过期</span>
                                            {% elif license.activated %}
                                            <span class="badge badge-status status-active">已激活</span>
                                            {% else %}
                                            <span class="badge badge-status status-inactive">未激活</span>
                                            {% endif %}
                                        </td>
                                        <td class="d-none d-md-table-cell">{{ license.created_at }}</td>
                                        <td class="d-none d-md-table-cell">{{ license.valid_until or '永久' }}</td>
                                        <td class="text-end">
                                            <div class="btn-group btn-group-sm">
                                                <a href="{{ url_for('view_license', license_id=license.id) }}" class="btn btn-primary action-btn" title="查看">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ url_for('edit_license', license_id=license.id) }}" class="btn btn-info action-btn" title="编辑">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-danger action-btn" 
                                                        title="删除" 
                                                        data-bs-toggle="modal" 
                                                        data-bs-target="#deleteModal" 
                                                        data-id="{{ license.id }}" 
                                                        data-key="{{ license.key }}">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="8" class="empty-state">
                                            <i class="fas fa-key"></i>
                                            <h4>暂无卡密数据</h4>
                                            <p class="text-muted mb-4">您可以创建新的卡密来开始管理</p>
                                            <a href="{{ url_for('create_license') }}" class="btn btn-primary">
                                                <i class="fas fa-plus"></i> 创建第一个卡密
                                            </a>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                    
                    <input type="hidden" name="selected_ids" id="selectedIds">
                    <input type="hidden" name="action" id="batchAction">
                </form>
                
                <!-- 分页 -->
                <nav>
                    <ul class="pagination">
                        {% if page > 1 %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('licenses', page=page-1, search=search, filter_type=filter_type, status_filter=status_filter) }}">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                        {% endif %}
                        
                        {% for p in range(1, total_pages + 1) %}
                            {% if p == page %}
                            <li class="page-item active">
                                <span class="page-link">{{ p }}</span>
                            </li>
                            {% elif p <= 3 or p >= total_pages - 2 or (p >= page - 1 and p <= page + 1) %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('licenses', page=p, search=search, filter_type=filter_type, status_filter=status_filter) }}">{{ p }}</a>
                            </li>
                            {% elif p == 4 and page > 4 or p == total_pages - 3 and page < total_pages - 3 %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page < total_pages %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('licenses', page=page+1, search=search, filter_type=filter_type, status_filter=status_filter) }}">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- 删除卡密确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                您确定要删除卡密 <span id="licenseKeyToDelete" class="fw-bold"></span> 吗？此操作不可撤销。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <a href="#" id="confirmDelete" class="btn btn-danger">删除</a>
            </div>
        </div>
    </div>
</div>

<!-- 批量删除确认模态框 -->
<div class="modal fade" id="batchDeleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认批量删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                您确定要删除所选的 <span id="deleteCount" class="fw-bold"></span> 个卡密吗？此操作不可撤销。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmBatchDelete">删除</button>
            </div>
        </div>
    </div>
</div>

<!-- 批量封禁确认模态框 -->
<div class="modal fade" id="batchBanModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认批量封禁</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>您确定要封禁所选的 <span id="banCount" class="fw-bold"></span> 个卡密吗？</p>
                <div class="mb-3">
                    <label for="banReason" class="form-label">封禁原因</label>
                    <input type="text" class="form-control" id="banReason" name="ban_reason" placeholder="请输入封禁原因">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-warning" id="confirmBatchBan">封禁</button>
            </div>
        </div>
    </div>
</div>

<!-- 批量解封确认模态框 -->
<div class="modal fade" id="batchUnbanModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认批量解封</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                您确定要解封所选的 <span id="unbanCount" class="fw-bold"></span> 个卡密吗？
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-info" id="confirmBatchUnban">解封</button>
            </div>
        </div>
    </div>
</div>

<script>
    // 防止页面加载时闪烁
    window.addEventListener('DOMContentLoaded', function() {
        document.body.classList.remove('loading');
    });

    // 导航栏切换
    document.getElementById('toggleSidebar').addEventListener('click', function() {
        document.getElementById('sidebar').classList.toggle('show');
        document.getElementById('mobileOverlay').classList.toggle('show');
    });
    
    // 点击遮罩层关闭侧边栏
    document.getElementById('mobileOverlay').addEventListener('click', function() {
        document.getElementById('sidebar').classList.remove('show');
        this.classList.remove('show');
    });
    
    // 点击内容区域关闭侧边栏（移动设备）
    document.querySelector('.main-content').addEventListener('click', function(e) {
        if (window.innerWidth < 992) {
            document.getElementById('sidebar').classList.remove('show');
            document.getElementById('mobileOverlay').classList.remove('show');
        }
    });
    
    // 删除卡密确认模态框
    document.getElementById('deleteModal').addEventListener('show.bs.modal', function (event) {
        var button = event.relatedTarget;
        var id = button.getAttribute('data-id');
        var key = button.getAttribute('data-key');
        document.getElementById('licenseKeyToDelete').textContent = key;
        document.getElementById('confirmDelete').href = '/delete_license/' + id;
    });
    
    // 全选/取消全选
    document.getElementById('selectAll').addEventListener('change', function() {
        var checkboxes = document.querySelectorAll('.license-checkbox');
        for (var i = 0; i < checkboxes.length; i++) {
            checkboxes[i].checked = this.checked;
        }
        updateBatchButtons();
    });
    
    // 单个复选框变化时更新按钮状态
    var checkboxes = document.querySelectorAll('.license-checkbox');
    for (var i = 0; i < checkboxes.length; i++) {
        checkboxes[i].addEventListener('change', updateBatchButtons);
    }
    
    // 更新批量操作按钮状态
    function updateBatchButtons() {
        var checked = document.querySelectorAll('.license-checkbox:checked');
        var hasChecked = checked.length > 0;
        
        document.getElementById('batchDeleteBtn').disabled = !hasChecked;
        document.getElementById('batchBanBtn').disabled = !hasChecked;
        document.getElementById('batchUnbanBtn').disabled = !hasChecked;
        
        // 更新选中计数
        var selectedCount = document.getElementById('selectedCount');
        if (hasChecked) {
            selectedCount.textContent = `已选择 ${checked.length} 个卡密`;
        } else {
            selectedCount.textContent = '';
        }
    }
    
    // 批量删除按钮点击事件
    document.getElementById('batchDeleteBtn').addEventListener('click', function() {
        var checked = document.querySelectorAll('.license-checkbox:checked');
        document.getElementById('deleteCount').textContent = checked.length;
        var modal = new bootstrap.Modal(document.getElementById('batchDeleteModal'));
        modal.show();
    });
    
    // 确认批量删除
    document.getElementById('confirmBatchDelete').addEventListener('click', function() {
        var form = document.getElementById('batch-action-form');
        var selectedIds = getSelectedIds();
        document.getElementById('selectedIds').value = selectedIds;
        document.getElementById('batchAction').value = 'delete';
        form.action = '{{ url_for("delete_licenses") }}';
        form.submit();
    });
    
    // 批量封禁按钮点击事件
    document.getElementById('batchBanBtn').addEventListener('click', function() {
        var checked = document.querySelectorAll('.license-checkbox:checked');
        document.getElementById('banCount').textContent = checked.length;
        var modal = new bootstrap.Modal(document.getElementById('batchBanModal'));
        modal.show();
    });
    
    // 确认批量封禁
    document.getElementById('confirmBatchBan').addEventListener('click', function() {
        var form = document.getElementById('batch-action-form');
        var selectedIds = getSelectedIds();
        document.getElementById('selectedIds').value = selectedIds;
        document.getElementById('batchAction').value = 'ban';
        
        // 获取封禁原因
        var banReason = document.getElementById('banReason').value;
        
        // 创建一个隐藏的输入元素来存储封禁原因
        var banReasonInput = document.createElement('input');
        banReasonInput.type = 'hidden';
        banReasonInput.name = 'ban_reason';
        banReasonInput.value = banReason;
        form.appendChild(banReasonInput);
        
        form.action = '{{ url_for("batch_ban_licenses") }}';
        form.submit();
    });
    
    // 批量解封按钮点击事件
    document.getElementById('batchUnbanBtn').addEventListener('click', function() {
        var checked = document.querySelectorAll('.license-checkbox:checked');
        document.getElementById('unbanCount').textContent = checked.length;
        var modal = new bootstrap.Modal(document.getElementById('batchUnbanModal'));
        modal.show();
    });
    
    // 确认批量解封
    document.getElementById('confirmBatchUnban').addEventListener('click', function() {
        var form = document.getElementById('batch-action-form');
        var selectedIds = getSelectedIds();
        document.getElementById('selectedIds').value = selectedIds;
        document.getElementById('batchAction').value = 'unban';
        form.action = '{{ url_for("unban_licenses") }}';
        form.submit();
    });
    
    // 获取选中的ID列表
    function getSelectedIds() {
        var checked = document.querySelectorAll('.license-checkbox:checked');
        var ids = [];
        for (var i = 0; i < checked.length; i++) {
            ids.push(checked[i].value);
        }
        return ids.join(',');
    }
</script>

</body>
</html>
    