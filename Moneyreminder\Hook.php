<?php

namespace plugin\Moneyreminder;

use think\facade\Db;
use app\common\service\EmailService;
use app\common\service\QqService;
use app\common\service\WxpusherService;

class Hook
{
    public function handle()
    {
        try {
            // 检查是否启用监控
            if (intval(plugconf("Moneyreminder.monitor_status") ?? 0) !== 1) {
                error_log("Moneyreminder: 监控未启用");
                return true; // 返回 true 表示任务完成
            }

            $result = $this->checkAccounts();
            
            // 记录执行日志
            error_log("Moneyreminder Hook executed at: " . date('Y-m-d H:i:s'));
            
            return true; // 返回 true 表示任务完成
            
        } catch (\Exception $e) {
            error_log("Moneyreminder Hook error: " . $e->getMessage());
            return true; // 即使发生异常也返回 true，避免任务挂起
        }
    }

    private function checkAccounts()
    {
        try {
            // 获取配置
            $depositThreshold = floatval(plugconf("Moneyreminder.deposit_threshold") ?? 1000);
            $operateThreshold = floatval(plugconf("Moneyreminder.operate_threshold") ?? 500);
            $notifyType = intval(plugconf("Moneyreminder.notify_type") ?? 1);
            $email = plugconf("Moneyreminder.notify_email") ?? '';
            $qq = plugconf("Moneyreminder.notify_qq") ?? '';
            $wxpusher = plugconf("Moneyreminder.notify_wxpusher") ?? '';

            // 验证通知配置
            if ($notifyType === 3 && empty($email)) {
                error_log("Moneyreminder: 请先配置通知邮箱");
                throw new \Exception("请先配置通知邮箱");
            }
            if ($notifyType === 2 && empty($qq)) {
                error_log("Moneyreminder: 请先配置通知QQ");
                throw new \Exception("请先配置通知QQ");
            }
            if ($notifyType === 4 && empty($wxpusher)) {
                error_log("Moneyreminder: 请先配置wxpusher");
                throw new \Exception("请先配置wxpusher");
            }

            // 查询所有用户
            $users = Db::name('user')
                ->field(['id', 'username', 'email', 'deposit_money', 'deposit_limit', 'operate_money'])
                ->select()
                ->toArray();

            if (empty($users)) {
                error_log("Moneyreminder: 没有找到用户");
                throw new \Exception("没有找到用户");
            }

            $checkedCount = 0;
            $notifiedDepositCount = 0;
            $notifiedOperateCount = 0;

            foreach ($users as $user) {
                try {
                    $checkedCount++;
                    $needNotify = false;
                    $userMessage = "";
                    $adminMessage = "";
                    
                    // 检查保证金余额
                    if ($user['deposit_money'] < $depositThreshold) {
                        $needNotify = true;
                        $userMessage .= "您的保证金余额不足{$user['deposit_money']}元，请您及时登录后台充值。\n";
                        $adminMessage .= "{$user['username']}用户保证金余额不足{$user['deposit_money']}元，请注意提醒用户充值。\n";
                        $notifiedDepositCount++;
                        
                        // 记录保证金余额变动日志
                        Db::name('user_money_log')->insert([
                            'user_id' => $user['id'],
                            'username' => $user['username'],
                            'money' => $user['deposit_money'],
                            'before' => $user['deposit_money'],
                            'after' => $user['deposit_money'],
                            'memo' => "保证金余额不足{$depositThreshold}元提醒",
                            'create_time' => time(),
                            'type' => 'deposit_warning'
                        ]);
                    }
                    
                    // 检查运营账户余额
                    if ($user['operate_money'] < $operateThreshold) {
                        $needNotify = true;
                        $userMessage .= "您的运营钱包余额不足{$user['operate_money']}元，请您及时登录后台充值。\n";
                        $adminMessage .= "{$user['username']}用户运营钱包余额不足{$user['operate_money']}元，请注意提醒用户充值。\n";
                        $notifiedOperateCount++;
                        
                        // 记录运营账户余额变动日志
                        Db::name('user_money_log')->insert([
                            'user_id' => $user['id'],
                            'username' => $user['username'],
                            'money' => $user['operate_money'],
                            'before' => $user['operate_money'],
                            'after' => $user['operate_money'],
                            'memo' => "运营钱包余额不足{$operateThreshold}元提醒",
                            'create_time' => time(),
                            'type' => 'operate_warning'
                        ]);
                    }
                    
                    // 如果需要发送通知
                    if ($needNotify) {
                        $this->sendNotification($notifyType, $user, trim($userMessage), trim($adminMessage), $email, $qq, $wxpusher);
                    }
                    
                } catch (\Throwable $e) {
                    error_log("Moneyreminder: 处理用户 {$user['id']} 失败: " . $e->getMessage());
                    // 不抛出异常，继续处理下一个用户
                }
            }

            error_log("Moneyreminder: 检查完成，共检查 {$checkedCount} 个用户，保证金不足 {$notifiedDepositCount} 个，运营账户不足 {$notifiedOperateCount} 个");
            
            // 发送统计信息
            $this->sendStatistics($notifyType, $this->getTotalDepositSum(), $this->getTotalDepositLimit(), $this->getTotalOperateSum(), $email, $qq, $wxpusher);
            
            return [
                'success' => true,
                'message' => "检查完成：共检查 {$checkedCount} 个用户，保证金不足 {$notifiedDepositCount} 个，运营账户不足 {$notifiedOperateCount} 个"
            ];

        } catch (\Exception $e) {
            error_log("Moneyreminder: 检查账户失败: " . $e->getMessage());
            return [
                'success' => false,
                'message' => "检查失败：" . $e->getMessage()
            ];
        }
    }

    private function getTotalDepositSum()
    {
        $sum = Db::name('user')
            ->sum('deposit_money');
            
        return $sum ?: 0;
    }
    
    private function getTotalDepositLimit()
    {
        $sum = Db::name('user')
            ->sum('deposit_limit');
            
        return $sum ?: 0;
    }
    
    private function getTotalOperateSum()
    {
        $sum = Db::name('user')
            ->sum('operate_money');
            
        return $sum ?: 0;
    }

    private function sendDepositNotification($notifyType, $user, $threshold, $adminEmail, $adminQQ, $adminWxpusher)
    {
        $userMessage = "您的保证金余额不足{$user['deposit_money']}元，请您及时登录后台充值。";
        $adminMessage = "{$user['username']}用户保证金余额不足{$user['deposit_money']}元，请注意提醒用户充值。";
        
        // 记录用户余额变动日志
        Db::name('user_money_log')->insert([
            'user_id' => $user['id'],
            'username' => $user['username'],
            'money' => $user['deposit_money'],
            'before' => $user['deposit_money'],
            'after' => $user['deposit_money'],
            'memo' => "保证金余额不足{$threshold}元提醒",
            'create_time' => time(),
            'type' => 'deposit_warning'
        ]);
        
        $this->sendNotification($notifyType, $user, $userMessage, $adminMessage, $adminEmail, $adminQQ, $adminWxpusher);
    }
    
    private function sendOperateNotification($notifyType, $user, $threshold, $adminEmail, $adminQQ, $adminWxpusher)
    {
        $userMessage = "您的运营钱包余额不足{$user['operate_money']}元，请您及时登录后台充值。";
        $adminMessage = "{$user['username']}用户运营钱包余额不足{$user['operate_money']}元，请注意提醒用户充值。";
        
        // 记录用户余额变动日志
        Db::name('user_money_log')->insert([
            'user_id' => $user['id'],
            'username' => $user['username'],
            'money' => $user['operate_money'],
            'before' => $user['operate_money'],
            'after' => $user['operate_money'],
            'memo' => "运营钱包余额不足{$threshold}元提醒",
            'create_time' => time(),
            'type' => 'operate_warning'
        ]);
        
        $this->sendNotification($notifyType, $user, $userMessage, $adminMessage, $adminEmail, $adminQQ, $adminWxpusher);
    }
    
    private function sendStatistics($notifyType, $totalDepositSum, $totalDepositLimit, $totalOperateSum, $adminEmail, $adminQQ, $adminWxpusher)
    {
        $adminMessage = "保证金限额总和: {$totalDepositLimit}元\n保证金总金额: {$totalDepositSum}元\n运营钱包总余额: {$totalOperateSum}元";
        
        try {
            switch ($notifyType) {
                case 3: // 邮箱通知
                    if (!empty($adminEmail)) {
                        $service = new \app\common\service\EmailService();
                        $res = $service->subject('账户余额统计')
                               ->message($adminMessage)
                               ->to($adminEmail)
                               ->send();
                        
                        if ($res) {
                            error_log("Moneyreminder: 统计邮件通知发送成功");
                        } else {
                            error_log("Moneyreminder: 统计邮件发送失败: " . $service->getError());
                        }
                    }
                    break;
                    
                case 2: // QQ通知
                    if (!empty($adminQQ)) {
                        $service = new QqService();
                        $service->sendMessage($adminQQ, $adminMessage);
                        error_log("Moneyreminder: 统计QQ通知已发送");
                    }
                    break;
                    
                case 4: // wxpusher通知
                    if (!empty($adminWxpusher)) {
                        $service = new WxpusherService();
                        $service->sendMessage($adminWxpusher, '账户余额统计', $adminMessage);
                        error_log("Moneyreminder: 统计wxpusher通知已发送");
                    }
                    break;
                    
                case 5: // 企业微信群通知
                    // 实现企业微信群通知
                    error_log("Moneyreminder: 统计企业微信群通知已发送");
                    break;
                    
                case 6: // 机器人通知
                    // 实现机器人通知
                    error_log("Moneyreminder: 统计机器人通知已发送");
                    break;
            }
        } catch (\Exception $e) {
            error_log("Moneyreminder: 发送统计通知失败: " . $e->getMessage());
        }
    }

    private function sendNotification($notifyType, $user, $userMessage, $adminMessage, $adminEmail, $adminQQ, $adminWxpusher)
    {
        try {
            // 默认发送商户端通知
            Db::name('notification')->insert([
                'user_id' => $user['id'],
                'message' => $userMessage,
                'create_time' => time(),
                'status' => 0, // 未读
            ]);
            error_log("Moneyreminder: 商户端通知已发送");

            // 根据通知类型发送额外的通知
            switch ($notifyType) {
                case 2: // QQ通知
                    if (!empty($user['qq']) && !empty($adminQQ)) {
                        $service = new QqService();
                        $service->sendMessage($user['qq'], $userMessage);
                        $service->sendMessage($adminQQ, $adminMessage);
                        error_log("Moneyreminder: QQ通知已发送");
                    }
                    break;
                    
                case 3: // 邮箱通知（需要手动开启）
                    if (!empty($user['email']) && !empty($adminEmail)) {
                        // 使用提供的EmailService代码发送邮件
                        $service = new \app\common\service\EmailService();
                        
                        // 发送给用户
                        $res = $service->subject('账户余额提醒')
                               ->message($userMessage)
                               ->to($user['email'])
                               ->send();
                        
                        if ($res) {
                            error_log("Moneyreminder: 用户邮件通知发送成功");
                        } else {
                            error_log("Moneyreminder: 用户邮件发送失败: " . $service->getError());
                        }
                        
                        // 发送给管理员
                        $service = new \app\common\service\EmailService();
                        $res = $service->subject('账户余额提醒')
                               ->message($adminMessage)
                               ->to($adminEmail)
                               ->send();
                        
                        if ($res) {
                            error_log("Moneyreminder: 管理员邮件通知发送成功");
                        } else {
                            error_log("Moneyreminder: 管理员邮件发送失败: " . $service->getError());
                        }
                    }
                    break;
                    
                case 4: // wxpusher通知
                    if (!empty($adminWxpusher)) {
                        $service = new WxpusherService();
                        // 假设user配置了wxpusher
                        if (!empty($user['wxpusher_uid'])) {
                            $service->sendMessage($user['wxpusher_uid'], '账户余额提醒', $userMessage);
                        }
                        $service->sendMessage($adminWxpusher, '账户余额提醒', $adminMessage);
                        error_log("Moneyreminder: wxpusher通知已发送");
                    }
                    break;
                    
                case 5: // 企业微信群通知
                    // 实现企业微信群通知
                    error_log("Moneyreminder: 企业微信群通知已发送");
                    break;
                    
                case 6: // 机器人通知
                    // 实现机器人通知
                    error_log("Moneyreminder: 机器人通知已发送");
                    break;
            }
        } catch (\Exception $e) {
            error_log("Moneyreminder: 发送通知失败: " . $e->getMessage());
            // 通知发送失败不影响主流程
        }
    }

    /**
     * 发送测试通知
     * @param array $user 测试用户数据
     * @return bool|string 成功返回true，失败返回错误信息
     */
    public function sendTestNotification($user)
    {
        try {
            // 获取通知配置
            $notifyType = intval(plugconf('Moneyreminder.notify_type'));
            $email = plugconf('Moneyreminder.notify_email');
            $qq = plugconf('Moneyreminder.notify_qq');
            $wxpusher = plugconf('Moneyreminder.notify_wxpusher');
            
            // 记录测试信息
            \think\facade\Log::info("发送测试通知 - 配置信息:", [
                '通知类型' => $notifyType,
                '邮箱' => $email,
                'QQ' => $qq,
                'WxPusher' => $wxpusher
            ]);
            
            // 获取阈值配置
            $depositThreshold = floatval(plugconf('Moneyreminder.deposit_threshold'));
            $operateThreshold = floatval(plugconf('Moneyreminder.operate_threshold'));
            
            \think\facade\Log::info("阈值配置:", [
                '保证金阈值' => $depositThreshold,
                '运营钱包阈值' => $operateThreshold
            ]);
            
            // 测试消息内容 - 使用简单的消息以降低出错可能性
            $userMessage = "这是一条测试通知，当前配置：\n";
            $userMessage .= "保证金阈值：{$depositThreshold}元\n";
            $userMessage .= "运营账户阈值：{$operateThreshold}元\n";
            $userMessage .= "测试时间：" . date('Y-m-d H:i:s');
            
            $adminMessage = "这是一条测试通知，当前配置：\n";
            $adminMessage .= "保证金阈值：{$depositThreshold}元\n";
            $adminMessage .= "运营账户阈值：{$operateThreshold}元\n";
            $adminMessage .= "测试时间：" . date('Y-m-d H:i:s');
            
            \think\facade\Log::info("准备发送消息内容:", [
                '用户消息' => $userMessage,
                '管理员消息' => $adminMessage
            ]);
            
            // 默认商户通知 - 总是发送
            try {
                \think\facade\Log::info("开始保存商户端通知到数据库");
                
                // 检查notification表是否存在已在Api.php中完成
                
                // 写入通知记录
                Db::name('notification')->insert([
                    'user_id' => 0, // 测试时使用 0 作为虚拟用户ID
                    'message' => $userMessage,
                    'create_time' => time(),
                    'status' => 0 // 未读状态
                ]);
                
                \think\facade\Log::info("商户端通知已保存到数据库");
            } catch (\Exception $e) {
                \think\facade\Log::error("保存商户端通知失败:" . $e->getMessage());
                \think\facade\Log::error($e->getTraceAsString());
                throw new \Exception("保存商户端通知失败: " . $e->getMessage());
            }
            
            // 如果只选择商户端通知，直接返回成功
            if ($notifyType == 1) {
                \think\facade\Log::info("仅选择商户端通知，测试完成");
                return true;
            }
            
            // 额外通知 - 根据选择的通知类型
            \think\facade\Log::info("开始发送额外通知，类型: {$notifyType}");
            
            try {
                switch ($notifyType) {
                    case 2: // QQ通知
                        \think\facade\Log::info("尝试发送QQ通知");
                        if (empty($qq)) {
                            throw new \Exception('QQ号码未配置');
                        }
                        
                        // 检查QQ服务是否存在
                        if (!class_exists('\\app\\common\\service\\QqService')) {
                            throw new \Exception('QQ服务类不存在，请先安装相应插件');
                        }
                        
                        $service = new \app\common\service\QqService();
                        // 确保方法存在
                        if (!method_exists($service, 'sendMessage')) {
                            throw new \Exception('QQ服务缺少sendMessage方法');
                        }
                        
                        $service->sendMessage($qq, $userMessage);
                        \think\facade\Log::info("QQ通知已发送成功");
                        break;
                        
                    case 3: // 邮件通知
                        \think\facade\Log::info("尝试发送邮件通知");
                        if (empty($email)) {
                            throw new \Exception('邮箱地址未配置');
                        }
                        
                        // 检查邮件服务是否存在
                        if (!class_exists('\\app\\common\\service\\EmailService')) {
                            throw new \Exception('邮件服务类不存在，请先安装相应插件');
                        }
                        
                        $service = new \app\common\service\EmailService();
                        // 确保所需方法存在
                        if (!method_exists($service, 'subject') || !method_exists($service, 'message') || 
                            !method_exists($service, 'to') || !method_exists($service, 'send')) {
                            throw new \Exception('邮件服务缺少必要的方法');
                        }
                        
                        $res = $service->subject('余额监控测试通知')
                               ->message($userMessage)
                               ->to($email)
                               ->send();
                               
                        if (!$res) {
                            throw new \Exception('邮件发送失败: ' . $service->getError());
                        }
                        
                        \think\facade\Log::info("邮件通知已发送成功");
                        break;
                        
                    case 4: // WxPusher通知
                        \think\facade\Log::info("尝试发送WxPusher通知");
                        if (empty($wxpusher)) {
                            throw new \Exception('WxPusher UID未配置');
                        }
                        
                        // 检查WxPusher服务是否存在
                        if (!class_exists('\\app\\common\\service\\WxpusherService')) {
                            throw new \Exception('WxPusher服务类不存在，请先安装相应插件');
                        }
                        
                        $service = new \app\common\service\WxpusherService();
                        // 确保方法存在
                        if (!method_exists($service, 'sendMessage')) {
                            throw new \Exception('WxPusher服务缺少sendMessage方法');
                        }
                        
                        $service->sendMessage($wxpusher, '余额监控测试通知', $userMessage);
                        \think\facade\Log::info("WxPusher通知已发送成功");
                        break;
                        
                    default:
                        throw new \Exception("不支持的通知类型: {$notifyType}");
                }
            } catch (\Exception $e) {
                \think\facade\Log::error("额外通知发送失败: " . $e->getMessage());
                \think\facade\Log::error($e->getTraceAsString());
                return "额外通知发送失败: " . $e->getMessage() . "，但商户端通知已保存";
            }
            
            \think\facade\Log::info("测试通知发送完成，所有通知方式均成功");
            return true;
            
        } catch (\Throwable $e) {
            \think\facade\Log::error("测试通知发送失败: " . $e->getMessage());
            \think\facade\Log::error($e->getTraceAsString());
            return $e->getMessage();
        }
    }

    /**
     * 发送商户通知
     * @param string $userMsg 用户消息
     * @param string $adminMsg 管理员消息
     */
    private function sendMerchantNotification($userMsg, $adminMsg)
    {
        // 获取商户通知URL
        $notifyUrl = plugconf('Moneyreminder.notify_url');
        if (empty($notifyUrl)) {
            throw new \Exception('商户通知URL未配置');
        }

        // 发送通知
        $data = [
            'user_msg' => $userMsg,
            'admin_msg' => $adminMsg
        ];
        
        $response = \think\facade\Http::post($notifyUrl, $data);
        if ($response->getStatusCode() != 200) {
            throw new \Exception('商户通知发送失败：HTTP ' . $response->getStatusCode());
        }
        
        $result = $response->getBody()->getContents();
        if ($result !== 'success') {
            throw new \Exception('商户通知发送失败：' . $result);
        }
    }

    /**
     * 发送QQ通知
     * @param string $qq QQ号码
     * @param string $message 消息内容
     */
    private function sendQQNotification($qq, $message)
    {
        // 实现QQ通知逻辑
        // TODO: 根据实际QQ通知API实现
        throw new \Exception('QQ通知功能尚未实现');
    }

    /**
     * 发送邮件通知
     * @param string $email 邮箱地址
     * @param string $message 消息内容
     */
    private function sendEmailNotification($email, $message)
    {
        // 获取邮件配置
        $config = [
            'host' => plugconf('Moneyreminder.smtp_host'),
            'port' => plugconf('Moneyreminder.smtp_port'),
            'user' => plugconf('Moneyreminder.smtp_user'),
            'pass' => plugconf('Moneyreminder.smtp_pass'),
            'from' => plugconf('Moneyreminder.smtp_from'),
            'name' => plugconf('Moneyreminder.smtp_name')
        ];
        
        // 验证配置
        foreach ($config as $key => $value) {
            if (empty($value)) {
                throw new \Exception("邮件{$key}未配置");
            }
        }
        
        // 发送邮件
        $mail = new \PHPMailer\PHPMailer\PHPMailer(true);
        try {
            $mail->isSMTP();
            $mail->Host = $config['host'];
            $mail->SMTPAuth = true;
            $mail->Username = $config['user'];
            $mail->Password = $config['pass'];
            $mail->SMTPSecure = \PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS;
            $mail->Port = $config['port'];
            
            $mail->setFrom($config['from'], $config['name']);
            $mail->addAddress($email);
            
            $mail->isHTML(true);
            $mail->Subject = '余额监控测试通知';
            $mail->Body = $message;
            
            $mail->send();
        } catch (\Exception $e) {
            throw new \Exception('邮件发送失败：' . $mail->ErrorInfo);
        }
    }

    /**
     * 发送WxPusher通知
     * @param string $uid WxPusher UID
     * @param string $message 消息内容
     */
    private function sendWxPusherNotification($uid, $message)
    {
        // 获取WxPusher配置
        $appToken = plugconf('Moneyreminder.wxpusher_token');
        if (empty($appToken)) {
            throw new \Exception('WxPusher Token未配置');
        }
        
        // 发送通知
        $url = 'http://wxpusher.zjiecode.com/api/send/message';
        $data = [
            'appToken' => $appToken,
            'content' => $message,
            'contentType' => 1,
            'uids' => [$uid]
        ];
        
        $response = \think\facade\Http::json($data)->post($url);
        if ($response->getStatusCode() != 200) {
            throw new \Exception('WxPusher通知发送失败：HTTP ' . $response->getStatusCode());
        }
        
        $result = $response->json();
        if (!$result['success']) {
            throw new \Exception('WxPusher通知发送失败：' . $result['msg']);
        }
    }
} 