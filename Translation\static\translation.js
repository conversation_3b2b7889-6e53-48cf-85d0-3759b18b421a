(function() {
    // 创建翻译容器
    var translate_wrapper = document.createElement('div');
    translate_wrapper.id = 'translate';
    translate_wrapper.style.position = 'fixed';
    translate_wrapper.style.left = '20px';
    translate_wrapper.style.bottom = '20px';
    translate_wrapper.style.zIndex = '999999';
    document.body.appendChild(translate_wrapper);

    // 加载translate.js
    const script = document.createElement('script');
    script.src = 'https://cdn.staticfile.net/translate.js/3.12.0/translate.js';
    script.onload = function() {
        // 配置translate.js，添加德语到支持的语言列表
        translate.selectLanguageTag.languages = 'english,french,russian,chinese_simplified,hindi,japanese,chinese_traditional,portuguese,spanish,italian,korean,german';
        translate.selectLanguageTag.documentId = 'translate';
        translate.language.setLocal('german');

        // 直接显示选择框
        translate.execute();

        // 自定义选择框样式（可选）
        const selectLanguage = document.getElementById(translate.selectLanguageTag.documentId + 'SelectLanguage');
        if (selectLanguage) {
            // 设置选择框样式
            selectLanguage.style.padding = '5px';
            selectLanguage.style.borderRadius = '4px';
            
            // 监听选择框的变化
            selectLanguage.addEventListener('change', function(event) {
                var language = event.target.value;
                translate.changeLanguage(language);
            });
        }

        // 监听DOM变化，预先翻译隐藏的弹窗内容
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.addedNodes.length) {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) {
                            // 立即翻译新添加的节点内容
                            var selectedLanguage = translate.to || translate.language.getLocal();
                            translate.execute([node]);
                        }
                    });
                }
            });
        });

        // 开始观察整个文档
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            characterData: true
        });
    };
    document.head.appendChild(script);
})(); 
