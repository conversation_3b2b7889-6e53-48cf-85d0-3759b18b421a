<?php

namespace plugin\Waterchallenge;

use app\common\library\Plugin;
use think\facade\Db;
use think\facade\Env;
use think\facade\Log;

class Waterchallenge extends Plugin {

    public function install() {
        // 使用Env获取数据库前缀
        $prefix = Env::get('DB_PREFIX');
        $tableRules = $prefix . 'plugin_waterchallenge_rules';
        $tableUser = $prefix . 'plugin_waterchallenge_user';
        
        echo "开始安装流水挑战插件...\n";
        echo "正在创建数据表...\n";
        echo "数据库前缀: [" . $prefix . "]\n";
        echo "创建表: {$tableRules}, {$tableUser}\n";
        
        // 使用heredoc语法定义SQL - 流水挑战规则表
        $sqlRules = <<<SQL
CREATE TABLE IF NOT EXISTS `{$tableRules}` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `rule_id` varchar(32) NOT NULL COMMENT '规则ID',
  `name` varchar(255) NOT NULL COMMENT '规则名称',
  `turnover_amount` decimal(10,3) NOT NULL DEFAULT '0.000' COMMENT '要求流水',
  `reward_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '奖励金额',
  `challenge_duration` int(11) NOT NULL DEFAULT '7' COMMENT '挑战持续天数',
  `valid_days` int(11) NOT NULL DEFAULT '30' COMMENT '规则有效天数',
  `max_attempts` int(11) NOT NULL DEFAULT '0' COMMENT '最大参与次数，0表示无限制',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '规则状态：0禁用1启用',
  `start_time` datetime DEFAULT NULL COMMENT '生效开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '生效结束时间',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_rule_id` (`rule_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流水挑战规则';
SQL;

        // 使用heredoc语法定义SQL - 流水挑战用户记录表
        $sqlUser = <<<SQL
CREATE TABLE IF NOT EXISTS `{$tableUser}` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `challenge_id` varchar(32) NOT NULL COMMENT '挑战记录ID',
  `rule_id` varchar(32) NOT NULL COMMENT '规则ID',
  `rule_name` varchar(255) NOT NULL COMMENT '规则名称',
  `current_amount` decimal(10,3) NOT NULL DEFAULT '0.000' COMMENT '当前流水',
  `base_amount` decimal(10,3) NOT NULL DEFAULT '0.000' COMMENT '基准流水',
  `target_amount` decimal(10,3) NOT NULL DEFAULT '0.000' COMMENT '目标流水',
  `status` varchar(20) NOT NULL DEFAULT 'ongoing' COMMENT '状态：ongoing|completed|failed',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `reward_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '奖励金额',
  `reward_sent` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已发放奖励：0否1是',
  `reward_time` datetime DEFAULT NULL COMMENT '奖励发放时间',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_challenge_id` (`challenge_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流水挑战用户记录';
SQL;

        // 检查规则表是否存在
        $has_tables = Db::query("SHOW TABLES LIKE '{$tableRules}'");
        
        // 规则表已经存在
        if (!empty($has_tables)) {
            echo "表 {$tableRules} 已存在，跳过创建！\n";
        } else {
            // 表不存在，创建表
            try {
                Db::execute($sqlRules);
                echo "规则表创建完成！\n";
            } catch (\Exception $e) {
                echo "创建规则表失败: " . $e->getMessage() . "\n";
                // 记录错误日志
                file_put_contents(__DIR__ . '/install_error.log', date('Y-m-d H:i:s') . " - 创建规则表失败: " . $e->getMessage() . "\n", FILE_APPEND);
                return false;
            }
        }
        
        // 检查用户表是否存在
        $has_tables = Db::query("SHOW TABLES LIKE '{$tableUser}'");
        
        // 用户表已经存在
        if (!empty($has_tables)) {
            echo "表 {$tableUser} 已存在，跳过创建！\n";
        } else {
            // 表不存在，创建表
            try {
                Db::execute($sqlUser);
                echo "用户表创建完成！\n";
            } catch (\Exception $e) {
                echo "创建用户表失败: " . $e->getMessage() . "\n";
                // 记录错误日志
                file_put_contents(__DIR__ . '/install_error.log', date('Y-m-d H:i:s') . " - 创建用户表失败: " . $e->getMessage() . "\n", FILE_APPEND);
                return false;
            }
        }
        
        echo "流水挑战插件安装成功！\n";
        return true;
    }

    public function uninstall() {
        // 使用Env获取数据库前缀
        $prefix = Env::get('DB_PREFIX');
        $tableRules = $prefix . 'plugin_waterchallenge_rules';
        $tableUser = $prefix . 'plugin_waterchallenge_user';
        
        try {
            // 删除流水挑战规则表
            Db::execute("DROP TABLE IF EXISTS `{$tableRules}`");
            echo "表 {$tableRules} 删除成功\n";
            
            // 删除流水挑战用户表
            Db::execute("DROP TABLE IF EXISTS `{$tableUser}`");
            echo "表 {$tableUser} 删除成功\n";
        } catch (\Exception $e) {
            echo "卸载失败: " . $e->getMessage() . "\n";
            Log::error('Waterchallenge插件卸载失败：' . $e->getMessage());
            return false;
        }
        
        return true;
    }
    
    public function upgrade() {
        try {
            // 使用Env获取数据库前缀
            $prefix = Env::get('DB_PREFIX');
            $tableUser = $prefix . 'plugin_waterchallenge_user';
            $tableRules = $prefix . 'plugin_waterchallenge_rules';
            
            // 检查用户表是否存在
            $tableExists = Db::query("SHOW TABLES LIKE '{$tableUser}'");
            if (!empty($tableExists)) {
                // 检查字段是否存在
                $columnExists = false;
                $columns = Db::query("SHOW COLUMNS FROM `{$tableUser}`");
                foreach ($columns as $column) {
                    if ($column['Field'] == 'base_amount') {
                        $columnExists = true;
                        break;
                    }
                }
                
                // 如果字段不存在，添加字段
                if (!$columnExists) {
                    Db::execute("ALTER TABLE `{$tableUser}` ADD COLUMN `base_amount` decimal(10,3) NOT NULL DEFAULT '0.000' COMMENT '基准流水' AFTER `current_amount`");
                }
                
                // 将配置文件中的挑战记录迁移到数据库
                $this->migrateRecordsToDatabase();
            } else {
                // 如果表不存在，重新安装
                $this->install();
                $this->migrateRecordsToDatabase();
            }
            
            // 检查规则表中是否存在max_attempts字段
            $tableExistsRules = Db::query("SHOW TABLES LIKE '{$tableRules}'");
            if (!empty($tableExistsRules)) {
                // 检查max_attempts字段是否存在
                $maxAttemptsExists = false;
                $columnsRules = Db::query("SHOW COLUMNS FROM `{$tableRules}`");
                foreach ($columnsRules as $column) {
                    if ($column['Field'] == 'max_attempts') {
                        $maxAttemptsExists = true;
                        break;
                    }
                }
                
                // 如果max_attempts字段不存在，添加它
                if (!$maxAttemptsExists) {
                    Db::execute("ALTER TABLE `{$tableRules}` ADD COLUMN `max_attempts` int(11) NOT NULL DEFAULT '0' COMMENT '最大参与次数，0表示无限制' AFTER `valid_days`");
                    echo "添加max_attempts字段成功\n";
                    Log::info('添加max_attempts字段成功');
                }
            }
            
            return true;
        } catch (\Exception $e) {
            Log::error('Waterchallenge插件升级失败：' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 将配置文件中的挑战记录迁移到数据库
     */
    protected function migrateRecordsToDatabase() {
        try {
            // 使用Env获取数据库前缀
            $prefix = Env::get('DB_PREFIX');
            $tableName = $prefix . 'plugin_waterchallenge_user';
            
            // 读取配置文件
            $configFile = app()->getRootPath() . 'plugin/Waterchallenge/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            // 检查是否有挑战记录需要迁移
            if (!empty($config['challenge_records']) && is_array($config['challenge_records'])) {
                foreach ($config['challenge_records'] as $record) {
                    // 检查记录是否已存在
                    $exists = Db::name('plugin_waterchallenge_user')
                        ->where('challenge_id', $record['id'])
                        ->count();
                        
                    if (!$exists) {
                        // 插入到数据库
                        Db::name('plugin_waterchallenge_user')->insert([
                            'user_id' => $record['merchant_id'],
                            'challenge_id' => $record['id'],
                            'rule_id' => $record['rule_id'],
                            'rule_name' => $record['rule_name'],
                            'current_amount' => $record['current_amount'],
                            'base_amount' => $record['base_amount'] ?? 0,
                            'target_amount' => $record['target_amount'],
                            'status' => $record['status'],
                            'start_time' => $record['start_time'],
                            'end_time' => $record['end_time'],
                            'complete_time' => $record['complete_time'] ?? null,
                            'reward_amount' => $record['reward_amount'],
                            'reward_sent' => $record['reward_sent'] ? 1 : 0,
                            'reward_time' => $record['reward_time'] ?? null,
                            'create_time' => $record['start_time'],
                            'update_time' => date('Y-m-d H:i:s')
                        ]);
                    }
                }
                
                // 迁移完成后，从配置文件中移除挑战记录
                unset($config['challenge_records']);
                file_put_contents($configFile, "<?php\nreturn " . var_export($config, true) . ";\n");
            }
        } catch (\Exception $e) {
            Log::error('迁移挑战记录失败：' . $e->getMessage());
        }
    }
} 