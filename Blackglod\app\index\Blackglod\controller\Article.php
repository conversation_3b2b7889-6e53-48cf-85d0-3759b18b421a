<?php

namespace app\index\Blackglod\controller;

use app\common\controller\BaseIndex;
use app\common\model\Article as ArticleModel;
use think\facade\Db;

class Article extends BaseIndex {

    public function index() {
        // 获取公告数据
        $notice = ArticleModel::hasWhere("category", ['alias' => 'single'])
            ->where(['Article.status' => 1])
            ->where(['Article.id' => 1])
            ->order("sort desc,id desc")
            ->limit(5)
            ->select();

        // 获取所有启用的菜单项并按sort降序排序
        $allMenuItems = Db::name('nav')
            ->where('status', 1)
            ->order('sort desc, id asc')  // 先按sort降序，相同时按id升序
            ->select()
            ->toArray();
            
        // 找出顶级菜单
        $navItems = array_filter($allMenuItems, function($item) {
            return $item['pid'] == 0;
        });
        
        // 为每个顶级菜单添加子菜单
        foreach ($navItems as &$item) {
            $item['children'] = array_filter($allMenuItems, function($child) use ($item) {
                return $child['pid'] == $item['id'];
            });
        }
        
        // 将导航数组重置索引
        $navItems = array_values($navItems);

        // 获取网站配置信息
        $logo = $this->formatImageUrl(sysconf('website.logo'));
        $siteName = sysconf('website.app_name');
        $favicon = $this->formatImageUrl(sysconf('website.favicon'));
        $icpNumber = sysconf('website.icp_number');
        $gaNumber = sysconf('website.ga_number');
        $icpCert = sysconf('website.icp_cert');

        // 获取页脚配置
        $params = get_template_params();

        return view('', [
            'title' => sysconf("website.title"),
            'notice' => $notice,
            'logo' => $logo,
            'siteName' => $siteName,
            'favicon' => $favicon,
            'navItems' => $navItems,
            'icpNumber' => $icpNumber,
            'gaNumber' => $gaNumber,
            'icpCert' => $icpCert,
            // 服务中心配置
            'footer_service_show' => $params->footer_service_show ?? 1,
            'footer_service_1' => $params->footer_service_1 ?? '卡密查询',
            'footer_service_1_link' => $params->footer_service_1_link ?? '#',
            'footer_service_2' => $params->footer_service_2 ?? '投诉中心',
            'footer_service_2_link' => $params->footer_service_2_link ?? '#',
            'footer_service_3' => $params->footer_service_3 ?? '卡密工具',
            'footer_service_3_link' => $params->footer_service_3_link ?? '#',
            'footer_service_4' => $params->footer_service_4 ?? '商户入驻',
            'footer_service_4_link' => $params->footer_service_4_link ?? '#',
            
            // 帮助中心配置
            'footer_help_show' => $params->footer_help_show ?? 1,
            'footer_help_1' => $params->footer_help_1 ?? '常见问题',
            'footer_help_1_link' => $params->footer_help_1_link ?? '#',
            'footer_help_2' => $params->footer_help_2 ?? '系统公告',
            'footer_help_2_link' => $params->footer_help_2_link ?? '#',
            'footer_help_3' => $params->footer_help_3 ?? '结算公告',
            'footer_help_3_link' => $params->footer_help_3_link ?? '#',
            'footer_help_4' => $params->footer_help_4 ?? '新闻动态',
            'footer_help_4_link' => $params->footer_help_4_link ?? '#',
            
            // 法律责任配置
            'footer_legal_show' => $params->footer_legal_show ?? 1,
            'footer_legal_1' => $params->footer_legal_1 ?? '免责声明',
            'footer_legal_1_link' => $params->footer_legal_1_link ?? '#',
            'footer_legal_2' => $params->footer_legal_2 ?? '禁售商品',
            'footer_legal_2_link' => $params->footer_legal_2_link ?? '#',
            'footer_legal_3' => $params->footer_legal_3 ?? '服务协议',
            'footer_legal_3_link' => $params->footer_legal_3_link ?? '#',
            'footer_legal_4' => $params->footer_legal_4 ?? '隐私政策',
            'footer_legal_4_link' => $params->footer_legal_4_link ?? '#',
            
            // 友情链接配置
            'footer_links_show' => $params->footer_links_show ?? 1,
            'footer_links_1' => $params->footer_links_1 ?? '一意支付',
            'footer_links_1_link' => $params->footer_links_1_link ?? '#',
            'footer_links_2' => $params->footer_links_2 ?? '支付宝',
            'footer_links_2_link' => $params->footer_links_2_link ?? '#',
            'footer_links_3' => $params->footer_links_3 ?? '微信支付',
            'footer_links_3_link' => $params->footer_links_3_link ?? '#',
            'footer_links_4' => $params->footer_links_4 ?? 'QQ钱包',
            'footer_links_4_link' => $params->footer_links_4_link ?? '#'
        ]);
    }

    public function faq() {
        // 获取FAQ分类的文章列表
        $faqList = ArticleModel::hasWhere("category", ['alias' => 'faq'])
            ->where(['Article.status' => 1])
            ->order("sort desc,id desc")
            ->select();

        // 获取所有启用的菜单项并按sort降序排序
        $allMenuItems = Db::name('nav')
            ->where('status', 1)
            ->order('sort desc, id asc')  // 先按sort降序，相同时按id升序
            ->select()
            ->toArray();
            
        // 找出顶级菜单
        $navItems = array_filter($allMenuItems, function($item) {
            return $item['pid'] == 0;
        });
        
        // 为每个顶级菜单添加子菜单
        foreach ($navItems as &$item) {
            $item['children'] = array_filter($allMenuItems, function($child) use ($item) {
                return $child['pid'] == $item['id'];
            });
        }
        
        // 将导航数组重置索引
        $navItems = array_values($navItems);

        // 获取网站配置信息
        $logo = $this->formatImageUrl(sysconf('website.logo'));
        $siteName = sysconf('website.app_name');
        $favicon = $this->formatImageUrl(sysconf('website.favicon'));
        $icpNumber = sysconf('website.icp_number');
        $gaNumber = sysconf('website.ga_number');
        $icpCert = sysconf('website.icp_cert');

        // 获取页脚配置
        $params = get_template_params();

        return view('', [
            'title' => '常见问题 - ' . $siteName,
            'faqList' => $faqList,
            'logo' => $logo,
            'siteName' => $siteName,
            'favicon' => $favicon,
            'navItems' => $navItems,
            'icpNumber' => $icpNumber,
            'gaNumber' => $gaNumber,
            'icpCert' => $icpCert,
            // 服务中心配置
            'footer_service_show' => $params->footer_service_show ?? 1,
            'footer_service_1' => $params->footer_service_1 ?? '卡密查询',
            'footer_service_1_link' => $params->footer_service_1_link ?? '#',
            'footer_service_2' => $params->footer_service_2 ?? '投诉中心',
            'footer_service_2_link' => $params->footer_service_2_link ?? '#',
            'footer_service_3' => $params->footer_service_3 ?? '卡密工具',
            'footer_service_3_link' => $params->footer_service_3_link ?? '#',
            'footer_service_4' => $params->footer_service_4 ?? '商户入驻',
            'footer_service_4_link' => $params->footer_service_4_link ?? '#',
            
            // 帮助中心配置
            'footer_help_show' => $params->footer_help_show ?? 1,
            'footer_help_1' => $params->footer_help_1 ?? '常见问题',
            'footer_help_1_link' => $params->footer_help_1_link ?? '#',
            'footer_help_2' => $params->footer_help_2 ?? '系统公告',
            'footer_help_2_link' => $params->footer_help_2_link ?? '#',
            'footer_help_3' => $params->footer_help_3 ?? '结算公告',
            'footer_help_3_link' => $params->footer_help_3_link ?? '#',
            'footer_help_4' => $params->footer_help_4 ?? '新闻动态',
            'footer_help_4_link' => $params->footer_help_4_link ?? '#',
            
            // 法律责任配置
            'footer_legal_show' => $params->footer_legal_show ?? 1,
            'footer_legal_1' => $params->footer_legal_1 ?? '免责声明',
            'footer_legal_1_link' => $params->footer_legal_1_link ?? '#',
            'footer_legal_2' => $params->footer_legal_2 ?? '禁售商品',
            'footer_legal_2_link' => $params->footer_legal_2_link ?? '#',
            'footer_legal_3' => $params->footer_legal_3 ?? '服务协议',
            'footer_legal_3_link' => $params->footer_legal_3_link ?? '#',
            'footer_legal_4' => $params->footer_legal_4 ?? '隐私政策',
            'footer_legal_4_link' => $params->footer_legal_4_link ?? '#',
            
            // 友情链接配置
            'footer_links_show' => $params->footer_links_show ?? 1,
            'footer_links_1' => $params->footer_links_1 ?? '一意支付',
            'footer_links_1_link' => $params->footer_links_1_link ?? '#',
            'footer_links_2' => $params->footer_links_2 ?? '支付宝',
            'footer_links_2_link' => $params->footer_links_2_link ?? '#',
            'footer_links_3' => $params->footer_links_3 ?? '微信支付',
            'footer_links_3_link' => $params->footer_links_3_link ?? '#',
            'footer_links_4' => $params->footer_links_4 ?? 'QQ钱包',
            'footer_links_4_link' => $params->footer_links_4_link ?? '#'
        ]);
    }

    /**
     * 格式化图片URL
     */
    private function formatImageUrl($url) {
        if (empty($url)) {
            return '';
        }

        // 如果是完整的URL，直接返回
        if (preg_match('/^https?:\/\/(oss\.|cos\.|[-\w]+\.qiniucdn\.|[-\w]+)/i', $url)) {
            return $url;
        }

        // 如果是uploads目录下的文件
        if (strpos($url, 'uploads/') === 0) {
            return request()->domain() . '/' . $url;
        }

        // 其他情况，确保以/开头
        return request()->domain() . '/' . ltrim($url, '/');
    }
}
