<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>抽奖大转盘</title>
    <style>
        /* 修改背景为红色渐变 */
        body {
            background: linear-gradient(to bottom, #ff4d4d, #ff1a1a);
            overflow-x: hidden;
            min-height: 450px;
        }

        .sakura {
            position: fixed;
            pointer-events: none;
            z-index: 0;
        }

        /* 修改卡片容器样式 */
        .lottery-container {
            background-color: transparent;
            box-shadow: none;
            max-width: 100%;
            padding: 10px 5px;  /* 减少左右内边距 */
            margin: 0;
        }

        /* 修改转盘容器样式 */
        .turntable {
            position: relative;
            width: 400px;
            height: 400px;
            margin: 50px auto 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 50%;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.8);
            padding: 0;
            transition: transform 0.3s ease;
        }

        /* 修改转盘画布样式 */
        .turntable-canvas {
            position: relative;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            transition: none;
            z-index: 1;
        }

        .turntable-canvas.rotating {
            transition: transform 4s cubic-bezier(0.32, 0.96, 0.28, 1);
        }

        /* 修改指针容器和指针样式 */
        .pointer-container {
            position: absolute;
            top: -20px; /* 减小上边距 */
            left: 50%;
            transform: translateX(-50%);
            z-index: 3;
            width: 40px; /* 减小宽度 */
            height: 50px; /* 减小高度 */
        }

        /* 指针主体 */
        .pointer {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 40px; /* 减小宽度 */
            height: 50px; /* 减小高度 */
            background: linear-gradient(135deg, #FF6B6B, #FF4D4D);
            clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
            box-shadow: 0 4px 15px rgba(255, 77, 77, 0.3);
            opacity: 0.9; /* 添加透明度 */
        }

        /* 指针顶部装饰 */
        .pointer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 30px;
            background: linear-gradient(135deg, #FFD700, #FFA500);
            border-radius: 50%;
            box-shadow: 
                0 2px 4px rgba(0, 0, 0, 0.2),
                inset -2px -2px 4px rgba(0, 0, 0, 0.1),
                inset 2px 2px 4px rgba(255, 255, 255, 0.5);
        }

        /* 指针中心圆点 */
        .pointer::after {
            content: '';
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 12px;
            height: 12px;
            background: #fff;
            border-radius: 50%;
            box-shadow: 
                0 2px 4px rgba(0, 0, 0, 0.2),
                inset 0 0 2px rgba(0, 0, 0, 0.1);
        }

        /* 指针光泽效果 */
        .pointer-shine {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 100%;
            background: linear-gradient(
                90deg,
                transparent 0%,
                rgba(255, 255, 255, 0.2) 50%,
                transparent 100%
            );
        }

        /* 指针悬停效果 */
        .pointer:hover {
            filter: brightness(1.1);
        }

        /* 指针点击效果 */
        .pointer:active {
            transform: translateX(-50%) scale(0.98);
        }

        /* 修改结果显示样式 */
        .result-display {
            position: absolute;
            top: 50px;  /* 改为固定位置 */
            left: 50px; /* 改为固定位置 */
            transform: none; /* 移除原有的居中变换 */
            background: rgba(255, 255, 255, 0.95);
            color: #FF4D4D;
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.8);
            opacity: 0;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            z-index: 10; /* 确保显示在最上层 */
        }

        .result-display.show {
            opacity: 1;
            transform: translateY(0); /* 修改动画效果 */
        }

        /* 修改信息展示区域样式 */
        .lottery-info {
            text-align: center;
            color: #fff;
            margin-bottom: 20px;
        }

        .lottery-info > div {
            margin: 5px 0;
            font-size: 16px;
        }

        .time-message {
            color: #ffeb3b;
        }

        .end-time {
            margin-left: 10px;
        }

        /* 修改按钮样式 */
        .lottery-btn .el-button--primary {
            background: linear-gradient(to right, #FFD700, #FFA500);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-size: 16px;
            color: #FF4D4D;
            box-shadow: 0 4px 12px rgba(255, 77, 77, 0.3);
        }

        /* 修改标签页样式 */
        .lottery-tabs .el-tabs__nav-wrap::after {
            display: none;
        }

        .lottery-tabs .el-tabs__item {
            color: rgba(255, 255, 255, 0.8);
        }

        .lottery-tabs .el-tabs__item.is-active {
            color: #fff;
        }

        .lottery-tabs .el-tabs__active-bar {
            background-color: #fff;
        }

        /* 修改奖品列表和记录样式 */
        .prize-list, .lottery-records {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            margin-top: 15px;
        }

        /* 信息卡片样式 */
        .lottery-info {
            margin-bottom: 20px;
            text-align: center;
        }

        /* 按钮容器样式 */
        .lottery-btn {
            display: none;
        }

        /* 奖品列表样式 */
        .prize-list {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .prize-list h3 {
            color: #333;
            font-size: 18px;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .prize-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            margin: 8px 0;
            background: #fff;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .prize-item:hover {
            background: #f5f7fa;
        }

        .prize-name {
            color: #333;
            font-size: 16px;
        }

        .prize-probability {
            color: #909399;
            font-size: 14px;
        }

        /* 中奖记录样式 */
        .lottery-records {
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            margin-top: 20px;
            padding: 20px;
        }

        .records-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .record-item {
            padding: 15px;
            border-bottom: 1px solid #ebeef5;
        }

        .record-item:last-child {
            border-bottom: none;
        }

        .record-prize {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .prize-name {
            margin-right: 8px;
        }

        .record-time {
            font-size: 12px;
            color: #909399;
        }

        .no-records {
            text-align: center;
            color: #909399;
            padding: 20px;
        }

        /* 表单项提示信息 */
        .el-form-item-msg {
            margin-top: 8px;
            color: #666;
        }

        /* 导航菜单样式 */
        .lottery-tabs {
            margin-top: 20px;
        }

        .lottery-tabs .el-tabs__content {
            padding: 20px 0;
        }

        /* 调整奖品列表和记录的间距 */
        .prize-list,
        .lottery-records {
            margin-top: 0;
        }

        /* 适配不同尺寸的移动端屏幕 */
        @media screen and (max-width: 768px) {
            .turntable {
                width: 240px;  /* 减小默认移动端尺寸 */
                height: 240px;
                margin: 10px auto;
            }

            /* 调整中心按钮大小 */
            .turntable-center {
                width: 50px;
                height: 50px;
            }

            .turntable-center-text {
                font-size: 14px;
            }

            /* 调整指针大小 */
            .pointer-container {
                top: -18px;
                width: 36px;
                height: 45px;
            }

            .pointer {
                width: 36px;
                height: 45px;
            }

            /* 调整结果显示位置 */
            .result-display {
                top: 10px;
                left: 10px;
                font-size: 12px;
                padding: 5px 14px;
            }
        }

        /* 针对中等尺寸手机屏幕 */
        @media screen and (min-width: 375px) and (max-width: 767px) {
            .turntable {
                width: 260px;
                height: 260px;
            }
        }

        /* 针对大尺寸手机屏幕 */
        @media screen and (min-width: 414px) and (max-width: 767px) {
            .turntable {
                width: 280px;
                height: 280px;
            }
        }

        /* 针对特小屏幕设备 */
        @media screen and (max-width: 320px) {
            .turntable {
                width: 220px;
                height: 220px;
            }

            .turntable-center {
                width: 45px;
                height: 45px;
            }

            .turntable-center-text {
                font-size: 12px;
            }
        }

        /* 优化樱花动画在移动端的表现 */
        @media screen and (max-width: 768px) {
            .sakura {
                opacity: 0.6;  /* 降低樱花透明度 */
            }
        }

        /* 修改中心按钮样式 */
        .turntable-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 90px;
            height: 90px;
            background: linear-gradient(135deg, #FF6B6B, #FF4D4D);
            border-radius: 50%;
            z-index: 2;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(255, 77, 77, 0.3);
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border: 3px solid #FFF;
        }

        .turntable-center:hover {
            transform: translate(-50%, -50%) scale(1.05);
            box-shadow: 0 6px 20px rgba(255, 77, 77, 0.4);
        }

        .turntable-center:active {
            transform: translate(-50%, -50%);
        }

        .turntable-center-text {
            color: #FFF;
            font-size: 20px;
            font-weight: bold;
            text-align: center;
            line-height: 1.2;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        /* 添加禁用状态样式 */
        .turntable-center.disabled {
            opacity: 0.6;
            cursor: not-allowed;
            background: #ccc;
        }

        .turntable-center.disabled:hover {
            transform: translate(-50%, -50%);
            box-shadow: 0 4px 8px rgba(255, 77, 77, 0.3);
        }

        .turntable-center.disabled:active {
            transform: translate(-50%, -50%);
        }

        /* 流水统计样式 */
        .turnover-stats {
            padding: 10px;
        }

        .stat-card {
            background: linear-gradient(135deg, #ff6b6b, #ff4d4d);
            color: #fff;
            border: none;
        }

        .stat-card .el-card__header {
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .stat-content {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            padding: 20px 0;
        }

        .stat-item {
            text-align: center;
        }

        .stat-label {
            font-size: 14px;
            opacity: 0.8;
            margin-bottom: 8px;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
        }

        /* 流水规则卡片样式 */
        .rules-card {
            margin-top: 20px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
        }

        .rules-card .el-card__header {
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .rules-content {
            padding: 10px 0;
        }

        /* 表格样式美化 */
        .rules-content .el-table {
            background: transparent;
        }

        .rules-content .el-table th {
            background: #f5f7fa;
            color: #606266;
        }

        .rules-content .el-table td {
            color: #606266;
        }

        /* 添加分页样式 */
        .pagination {
            margin-top: 20px;
            text-align: center;
        }

        .pagination .el-pagination {
            justify-content: center;
        }

        .pagination .el-pagination.is-background .el-pager li:not(.disabled).active {
            background-color: #FF4D4D;
        }

        /* 添加或修改样式 */
        .pagination-container {
            margin-top: 20px;
            display: flex;
            justify-content: center;
        }

        .el-pagination.is-background .el-pager li:not(.disabled).active {
            background-color: #ff4d4d;
            color: #fff;
        }

        .el-pagination.is-background .el-pager li:not(.disabled):hover {
            color: #ff4d4d;
        }

        .records-list {
            margin-bottom: 20px;
        }

        .record-item {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 10px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .record-prize {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .prize-name {
            font-weight: bold;
            color: #333;
        }

        .record-time {
            font-size: 12px;
            color: #999;
            margin-top: 8px;
        }

        /* 中奖用户列表样式 */
        .winners-list {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 20px;
            margin-top: 15px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .winners-list .record-item {
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 8px;
            background: #fff;
            transition: all 0.3s ease;
        }

        .winners-list .record-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .winners-list .merchant-name {
            color: #409EFF;
            font-weight: bold;
            margin-right: 8px;
        }

        .winners-list .prize-name {
            color: #FF4D4D;
            font-weight: 500;
        }

        .winners-list .record-time {
            color: #909399;
            font-size: 12px;
            margin-top: 8px;
        }

        .balance-amount {
            color: #F56C6C;
            font-weight: bold;
            margin-left: 8px;
            font-size: 14px;
        }

        .prize-info {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 8px;
        }

        /* 中奖记录表格头部样式 */
        .winners-header {
            background: #f5f7fa;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 15px;
            font-weight: bold;
            color: #606266;
        }

        .winners-header .el-row {
            align-items: center;
        }

        /* 记录内容样式 */
        .record-content {
            display: flex;
            align-items: center;
            padding: 12px;
        }

        .winners-list .record-item {
            margin-bottom: 10px;
            padding: 0;
            border-radius: 8px;
            background: #fff;
            transition: all 0.3s ease;
        }

        .winners-list .record-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .winners-list .merchant-name {
            color: #409EFF;
            font-weight: bold;
        }

        .winners-list .prize-name {
            color: #FF4D4D;
            font-weight: 500;
        }

        .winners-list .balance-amount {
            color: #F56C6C;
            font-weight: bold;
            font-size: 14px;
        }

        .winners-list .record-time {
            color: #909399;
            font-size: 13px;
        }

        /* 确保列对齐 */
        .el-row {
            width: 100%;
            margin: 0 !important;
        }

        .el-col {
            display: flex;
            align-items: center;
        }

        /* 添加或修改样式 */
        .winners-list .merchant-name {
            font-weight: 500;
            display: inline-block;
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* 中奖用户列表的移动端适配 */
        @media screen and (max-width: 768px) {
            .winners-list {
                padding: 10px;
                
                .winners-header {
                    display: none; /* 在移动端隐藏表头 */
                }
                
                .record-item {
                    margin-bottom: 10px;
                    background: #fff;
                    border-radius: 8px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                }
                
                .record-content {
                    padding: 12px;
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 8px;
                }
                
                /* 重置列宽度为100% */
                .el-col {
                    width: 100% !important;
                    max-width: 100%;
                    margin-left: 0 !important;
                    margin-right: 0 !important;
                    padding: 4px 0;
                }
                
                /* 添加标签说明 */
                .el-col:before {
                    content: attr(data-label);
                    font-size: 12px;
                    color: #909399;
                    margin-right: 8px;
                }
                
                /* 商家名称样式 */
                .el-col:nth-child(1):before {
                    content: "商家：";
                }
                
                /* 奖品名称样式 */
                .el-col:nth-child(2):before {
                    content: "奖品：";
                }
                
                /* 奖品类型样式 */
                .el-col:nth-child(3):before {
                    content: "类型：";
                }
                
                /* 奖金样式 */
                .el-col:nth-child(4):before {
                    content: "奖金：";
                }
                
                /* 修改时间显示样式 */
                .el-col:nth-child(5) {
                    font-size: 13px; /* 减小字体大小 */
                    
                    &:before {
                        content: "时间：";
                        font-size: 12px;
                        color: #909399;
                        white-space: nowrap; /* 防止标签换行 */
                    }
                    
                    span {
                        white-space: nowrap; /* 防止时间换行 */
                        font-size: 13px; /* 统一字体大小 */
                    }
                }
                
                /* 调整时间行的内边距 */
                .el-col:last-child {
                    padding: 2px 0; /* 减少上下内边距 */
                    display: flex;
                    align-items: center;
                    flex-wrap: nowrap; /* 防止内容换行 */
                }
                
                .merchant-name {
                    display: inline;
                    word-break: break-all;
                    line-height: 1.4;
                }
                
                .prize-name {
                    display: inline;
                }
                
                /* 调整分页器样式 */
                .pagination-container {
                    margin-top: 15px;
                    
                    .el-pagination {
                        justify-content: center;
                        flex-wrap: wrap;
                        padding: 0 5px;
                    }
                    
                    .el-pagination .el-select {
                        margin-bottom: 8px;
                    }
                }
            }
        }

        /* 针对超小屏幕的额外优化 */
        @media screen and (max-width: 320px) {
            .winners-list {
                padding: 8px;
                
                .record-item {
                    margin-bottom: 8px;
                }
                
                .record-content {
                    padding: 8px;
                }
                
                .el-tag {
                    margin: 2px 0;
                }
            }
        }
    </style>
</head>
<body>
    <!-- 添加樱花容器 -->
    <div id="sakura-container"></div>
    <div id="app">
        <el-card shadow="never" class="lottery-container">
            <!-- 信息展示区域 -->
            <div class="lottery-info">
                <div>今日基础抽奖次数: {{ remainingBaseDraws }}/{{ config?.daily_limit || 0 }}</div>
                <div>流水获得抽奖次数: {{ turnoverDraws }}/{{ turnoverStats.today_draws || 0 }}</div>
                <div>今日已抽奖次数: {{ totalDraws }}</div>
                <div class="time-message">{{ timeMessage }}</div>
                <div v-if="drawMessage" style="color: #ff4d4d;">{{ drawMessage }}</div>
            </div>

            <!-- 添加导航菜单 -->
            <el-tabs v-model="activeTab" class="lottery-tabs">
                <el-tab-pane label="抽奖大转盘" name="lottery">
                    <!-- 转盘区域 -->
                    <div class="turntable">
                        <canvas ref="turntableCanvas" class="turntable-canvas" :class="{ rotating: isRotating }" width="300" height="300"></canvas>
                        <div class="turntable-center" @click="startLottery" :class="{ 'disabled': !canDraw || isRotating }">
                            <div class="turntable-center-text">点击</div>
                            <div class="turntable-center-text">抽奖</div>
                        </div>
                        <div class="pointer-container">
                            <div class="pointer">
                                <div class="pointer-shine"></div>
                            </div>
                            <div class="result-display" :class="{ show: showResult }">
                                {{ currentPrize }}
                            </div>
                        </div>
                    </div>
                </el-tab-pane>

                <el-tab-pane label="奖品信息" name="info">
                    <!-- 奖品列表 -->
                    <div class="prize-list">
                        <h3>奖品列表</h3>
                        <div class="prize-item" v-for="prize in prizes" :key="prize.id">
                            <span class="prize-name">{{ prize.name }}</span>
                            <span v-if="config && config.show_probability" class="prize-probability">
                                概率: {{ prize.probability }}%
                            </span>
                        </div>
                    </div>

                    <!-- 中奖记录 -->
                    <div class="lottery-records">
                        <h3>我的中奖记录</h3>
                        <div class="records-list">
                            <div v-if="records.length === 0" class="no-records">
                                暂无中奖记录
                            </div>
                            <div v-else v-for="record in records" :key="record.id" class="record-item">
                                <div class="record-prize">
                                    <span class="prize-name">{{ record.prize_name }}</span>
                                    <el-tag 
                                        :type="record.prize_type_style.type"
                                        :style="record.prize_type_style.style"
                                        size="small"
                                    >
                                        {{ record.prize_type_text }}
                                    </el-tag>
                                    <span v-if="record.balance_amount > 0" class="balance-amount">
                                        {{ record.balance_amount }}元
                                    </span>
                                    <el-tag 
                                        size="small" 
                                        :type="record.shipped ? 'success' : 'warning'"
                                        style="margin-left: 8px"
                                    >
                                        {{ record.shipped ? '已发货' : '未发货' }}
                                    </el-tag>
                                </div>
                                <div class="record-time">{{ record.create_time }}</div>
                            </div>
                        </div>
                        
                        <!-- 修改分页组件 -->
                        <div class="pagination-container" v-if="total > 0">
                            <el-pagination
                                v-model:current-page="currentPage"
                                v-model:page-size="pageSize"
                                :page-sizes="[10, 20, 50]"
                                :total="total"
                                @size-change="handleSizeChange"
                                @current-change="handleCurrentChange"
                                layout="total, sizes, prev, pager, next"
                                background
                            />
                        </div>
                    </div>
                </el-tab-pane>

                <!-- 添加流水统计标签页 -->
                <el-tab-pane label="流水统计" name="turnover">
                    <div class="turnover-stats">
                        <el-card class="stat-card">
                            <template #header>
                                <div class="card-header">
                                    <span>流水统计</span>
                                    <el-button type="primary" link @click="refreshTurnoverStats">
                                        <el-icon><Refresh /></el-icon>
                                        刷新
                                    </el-button>
                                </div>
                            </template>
                            <div class="stat-content">
                                <div class="stat-item">
                                    <div class="stat-label">今日流水</div>
                                    <div class="stat-value">{{ turnoverStats.today_amount || 0 }}元</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">本月流水</div>
                                    <div class="stat-value">{{ turnoverStats.month_amount || 0 }}元</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">已获得抽奖次数</div>
                                    <div class="stat-value">{{ turnoverStats.today_draws || 0 }}</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">下次抽奖需要</div>
                                    <div class="stat-value">{{ turnoverStats.next_required || 0 }}元</div>
                                </div>
                            </div>
                        </el-card>

                        <!-- 流水规则说明 -->
                        <el-card class="rules-card" style="margin-top: 20px;">
                            <template #header>
                                <div class="card-header">
                                    <span>流水规则说明</span>
                                </div>
                            </template>
                            <div class="rules-content">
                                <el-table :data="turnoverRules" style="width: 100%">
                                    <el-table-column prop="turnover_amount" label="流水金额">
                                        <template #default="scope">
                                            {{ scope.row.turnover_amount }}元
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="draw_times" label="获得抽奖次数">
                                        <template #default="scope">
                                            {{ scope.row.draw_times }}次
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="状态">
                                        <template #default="scope">
                                            <el-tag v-if="!scope.row.status" type="info">
                                                未开启
                                            </el-tag>
                                            <el-tag v-else :type="scope.row.claimed ? 'success' : 'warning'">
                                                {{ scope.row.claimed ? '已领取' : '未领取' }}
                                            </el-tag>
                                        </template>
                                    </el-table-column>
                                </el-table>
                                <el-empty v-if="!turnoverRules || turnoverRules.length === 0" description="暂无流水规则" />
                            </div>
                        </el-card>
                    </div>
                </el-tab-pane>

                <!-- 中奖用户标签页 -->
                <el-tab-pane label="中奖用户" name="winners">
                    <div class="winners-list">
                        <!-- 添加表头 -->
                        <div class="winners-header">
                            <el-row :gutter="20">
                                <el-col :span="4">商家</el-col>
                                <el-col :span="4">奖品名称</el-col>
                                <el-col :span="4">奖品类型</el-col>
                                <el-col :span="4">奖金</el-col>
                                <el-col :span="8">时间</el-col>
                            </el-row>
                        </div>

                        <div v-if="allRecords.length === 0" class="no-records">
                            暂无中奖记录
                        </div>
                        <div v-else v-for="record in allRecords" :key="record.id" class="record-item">
                            <el-row :gutter="20" class="record-content">
                                <el-col :span="4">
                                    <span class="merchant-name" :style="record.merchant_name.startsWith('VIP') ? 'color: #67C23A;' : ''">
                                        {{ record.merchant_name || record.merchant_id }}
                                    </span>
                                </el-col>
                                <el-col :span="4">
                                    <span class="prize-name">{{ record.prize_name }}</span>
                                </el-col>
                                <el-col :span="4">
                                    <el-tag 
                                        :type="record.prize_type_style.type"
                                        :style="record.prize_type_style.style"
                                    >
                                        {{ record.prize_type_text }}
                                    </el-tag>
                                </el-col>
                                <el-col :span="4">
                                    <span v-if="record.balance_amount > 0">¥{{ record.balance_amount }}</span>
                                    <span v-else>-</span>
                                </el-col>
                                <el-col :span="8">
                                    <span>{{ record.create_time }}</span>
                                </el-col>
                            </el-row>
                        </div>
                        
                        <!-- 分页组件 -->
                        <div class="pagination-container" v-if="winnersTotal > 0">
                            <el-pagination
                                v-model:current-page="winnersCurrentPage"
                                v-model:page-size="winnersPageSize"
                                :page-sizes="[10, 20, 50]"
                                :total="winnersTotal"
                                @size-change="handleWinnersSizeChange"
                                @current-change="handleWinnersCurrentChange"
                                layout="total, sizes, prev, pager, next"
                                background
                            />
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </el-card>
    </div>

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>

    <script>
    const { ref, reactive, onMounted, computed, watch, nextTick } = Vue;
    const { ElMessage } = ElementPlus;

    const app = Vue.createApp({
        setup() {
            const turntableCanvas = ref(null);
            const isRotating = ref(false);
            const prizes = ref([]);
            const remainingBaseDraws = ref(0);
            const turnoverDraws = ref(0);
            const totalDraws = ref(0); // 已使用的总抽奖次数
            const config = ref({
                status: 1,
                daily_limit: 0,
                start_hour: '00:00',
                end_hour: '23:59',
                show_probability: 0
            });
            const myRecords = ref([]);
            const showRecords = ref(true);

            // 添加导航菜单激活状态
            const activeTab = ref('lottery');

            // 检查是否在抽奖时间范围内
            const isInDrawTime = computed(() => {
                if (!config.value) return false;
                const now = new Date();
                const currentTime = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;
                return currentTime >= config.value.start_hour && currentTime <= config.value.end_hour;
            });

            // 显示时间消息
            const timeMessage = computed(() => {
                if (!config.value) return '';
                return `抽奖时间：${config.value.start_hour} - ${config.value.end_hour}`;
            });

            // 检查是否可以抽奖
            const canDraw = computed(() => {
                if (!config.value.status) return false;
                if (remainingBaseDraws.value <= 0 && turnoverDraws.value <= 0) return false;
                
                const now = new Date();
                const currentTime = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;
                
                return currentTime >= config.value.start_hour && currentTime <= config.value.end_hour;
            });

            // 抽奖提示信息
            const drawMessage = computed(() => {
                if (!config.value.status) return '抽奖活动已关闭';
                if (remainingBaseDraws.value <= 0 && turnoverDraws.value <= 0) return '今日抽奖次数已用完';
                
                const now = new Date();
                const currentTime = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;
                
                if (currentTime < config.value.start_hour) {
                    return `抽奖未开始，开始时间：${config.value.start_hour}`;
                } else if (currentTime > config.value.end_hour) {
                    return `抽奖已结束，结束时间：${config.value.end_hour}`;
                }
                return '';
            });

            // 在 data 中添加新的响应式数据
            const currentPrize = ref('');
            const showResult = ref(false);

            // 添加一个存储转盘奖品位置信息的数组
            const prizePositions = ref([]);

            // 添加 merchantLimit 响应式变量
            const merchantLimit = ref({
                daily_limit: 0,
                used_count: 0,
                update_time: 0,
                last_reset_date: ''
            });

            // 修改初始化转盘函数
            const initTurntable = () => {
                const canvas = turntableCanvas.value;
                if (!canvas || !prizes.value || prizes.value.length === 0) return;
                
                const ctx = canvas.getContext('2d');
                const width = canvas.width;
                const height = canvas.height;
                const centerX = width / 2;
                const centerY = height / 2;
                const radius = Math.min(width, height) / 2 - 20;
                
                ctx.clearRect(0, 0, width, height);
                
                const activePrizes = prizes.value.filter(prize => prize.status === 1 && prize.stock > 0);
                if (activePrizes.length === 0) return;
                
                const sliceAngle = (Math.PI * 2) / activePrizes.length;
                
                // 清空奖品位置数组
                prizePositions.value = [];
                
                activePrizes.forEach((prize, index) => {
                    // 调整起始角度，使第一个奖品位于12点钟方向
                    const startAngle = index * sliceAngle - Math.PI / 2;
                    const endAngle = startAngle + sliceAngle;
                    
                    // 存储奖品位置信息
                    prizePositions.value.push({
                        index,
                        id: prize.id,
                        name: prize.name,
                        startAngle,
                        endAngle,
                        // 转换为度数并确保在0-360范围内
                        startDegree: ((startAngle * 180 / Math.PI + 360) % 360),
                        endDegree: ((endAngle * 180 / Math.PI + 360) % 360)
                    });
                    
                    // 绘制扇形
                    ctx.beginPath();
                    ctx.moveTo(centerX, centerY);
                    ctx.arc(centerX, centerY, radius, startAngle, endAngle);
                    ctx.closePath();
                    
                    // 使用金色背景
                    ctx.fillStyle = '#FFF3D4';
                    ctx.fill();
                    
                    // 绘制分割线
                    ctx.beginPath();
                    ctx.moveTo(centerX, centerY);
                    ctx.lineTo(
                        centerX + Math.cos(startAngle) * radius,
                        centerY + Math.sin(startAngle) * radius
                    );
                    ctx.strokeStyle = '#FF4D4D';
                    ctx.lineWidth = 2;
                    ctx.stroke();
                    
                    // 绘制奖品文字和图标
                    ctx.save();
                    ctx.translate(centerX, centerY);
                    ctx.rotate(startAngle + sliceAngle / 2);
                    
                    // 绘制奖品名称
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    ctx.fillStyle = '#FF4D4D';
                    ctx.font = 'bold 16px Arial';
                    
                    // 计算文字位置
                    const textRadius = radius * 0.65;
                    
                    // 如果是现金奖励，添加红包图标
                    if (prize.type === 'cash') {
                        // 绘制红包背景
                        ctx.save();
                        ctx.translate(textRadius - 30, 0);
                        ctx.fillStyle = '#FF4D4D';
                        ctx.fillRect(-20, -15, 40, 30);
                        ctx.fillStyle = '#FFD700';
                        ctx.font = 'bold 14px Arial';
                        ctx.fillText('¥' + prize.amount, 0, 0);
                        ctx.restore();
                    }
                    
                    // 绘制奖品名称
                    ctx.save();
                    ctx.translate(textRadius, 0);
                    ctx.rotate(Math.PI / 2);
                    ctx.fillStyle = '#FF4D4D';
                    ctx.fillText(prize.name, 0, 0);
                    ctx.restore();
                    
                    ctx.restore();
                });
                
                // 绘制中心圆（修改这部分，不再绘制文字）
                ctx.beginPath();
                ctx.arc(centerX, centerY, radius * 0.2, 0, Math.PI * 2);
                ctx.fillStyle = '#FFF';
                ctx.fill();
                ctx.strokeStyle = '#FF4D4D';
                ctx.lineWidth = 2;
                ctx.stroke();
            };

            // 修改获取数据的方法
            const fetchData = async () => {
                try {
                    const res = await axios.get('/plugin/Lottery/user/getLotteryConfig');
                    if (res.data.code === 200) {
                        config.value = res.data.data.config;
                        remainingBaseDraws.value = res.data.data.remainingBaseDraws;
                        turnoverDraws.value = res.data.data.turnoverDraws;
                        totalDraws.value = res.data.data.totalDraws; // 这里获取的是已使用的总次数
                        prizes.value = res.data.data.prizes;
                        merchantLimit.value = res.data.data.merchantLimit || {
                            daily_limit: config.value.daily_limit,
                            used_count: 0,
                            update_time: 0,
                            last_reset_date: ''
                        };
                        
                        // 同时更新流水统计
                        await fetchTurnoverStats();
                        
                        nextTick(() => {
                            initTurntable();
                        });
                    }
                } catch (error) {
                    console.error('获取配置失败:', error);
                    ElMessage.error('获取配置失败：' + (error.response?.data?.msg || error.message));
                }
            };

            // 修改获取当前指向奖品的方法
            const getCurrentPrize = () => {
                if (!prizePositions.value.length) return '';
                
                const canvas = turntableCanvas.value;
                if (!canvas) return '';

                // 获取当前转盘旋转角度
                const style = window.getComputedStyle(canvas);
                const matrix = new WebKitCSSMatrix(style.transform);
                let angle = Math.round(Math.atan2(matrix.m12, matrix.m11) * (180/Math.PI));
                
                // 将角度转换为 0-360 范围内的正值
                angle = ((angle % 360) + 360) % 360;
                
                // 计算指针实际指向的角度（12点方向为0度）
                const pointerAngle = (360 - angle + 270) % 360;
                
                // 查找当前指向的奖品
                const currentPrize = prizePositions.value.find(prize => {
                    let start = prize.startDegree;
                    let end = prize.endDegree;
                    
                    // 处理跨越360度的情况
                    if (end < start) {
                        end += 360;
                    }
                    
                    let adjustedPointerAngle = pointerAngle;
                    if (pointerAngle < start) {
                        adjustedPointerAngle += 360;
                    }
                    
                    return adjustedPointerAngle >= start && adjustedPointerAngle < end;
                });

                return currentPrize?.name || '';
            };

            // 添加动画帧更新方法
            const updateCurrentPrize = () => {
                if (isRotating.value) {
                    currentPrize.value = getCurrentPrize();
                    requestAnimationFrame(updateCurrentPrize);
                }
            };

            // 修改抽奖函数
            const startLottery = async () => {
                if (!canDraw.value || isRotating.value) return;
                
                try {
                    isRotating.value = true;
                    showResult.value = false;
                    
                    const res = await axios.post("/plugin/Lottery/user/doLottery");
                    
                    if (res.data?.code === 200) {
                        const { prize, angle, remainingBaseDraws: newBaseDraws, turnoverDraws: newTurnoverDraws, totalDraws: newTotalDraws } = res.data.data;
                        
                        // 应用旋转动画
                        const canvas = turntableCanvas.value;
                        canvas.style.transition = 'none';
                        canvas.style.transform = 'rotate(0deg)';
                        canvas.offsetHeight; // 强制重排
                        
                        // 设置动画
                        canvas.style.transition = 'transform 4s cubic-bezier(0.25, 0.1, 0.25, 1)';
                        canvas.style.transform = `rotate(${angle}deg)`;
                        
                        // 显示结果
                        setTimeout(async () => {
                            currentPrize.value = prize.name;
                            showResult.value = true;
                            
                            // 更新剩余次数
                            remainingBaseDraws.value = newBaseDraws;
                            turnoverDraws.value = newTurnoverDraws;
                            totalDraws.value = newTotalDraws;
                            
                            // 重新获取流水统计
                            await fetchTurnoverStats();
                            
                            ElMessage({
                                message: `恭喜获得：${prize.name}`,
                                type: 'success',
                                duration: 3000
                            });
                            
                            isRotating.value = false;
                        }, 4000);
                        
                    } else {
                        ElMessage.error(res.data?.msg || '抽奖失败');
                        isRotating.value = false;
                    }
                } catch (error) {
                    console.error('抽奖失败:', error);
                    ElMessage.error('抽奖失败：' + (error.response?.data?.msg || error.message));
                    isRotating.value = false;
                }
            };

            // 获取当前转盘旋转角度的辅助函数
            const getCurrentRotation = (element) => {
                const style = window.getComputedStyle(element);
                const matrix = new WebKitCSSMatrix(style.transform);
                const angle = Math.round(Math.atan2(matrix.m12, matrix.m11) * (180/Math.PI));
                return ((angle % 360) + 360) % 360; // 确保返回0-360之间的值
            };

            // 监听奖品数组变化，重新初始化转盘
            watch(() => prizes.value, () => {
                nextTick(() => {
                    initTurntable();
                });
            }, { deep: true });

            // 添加分页相关的响应式数据
            const currentPage = ref(1);
            const pageSize = ref(10);
            const total = ref(0);
            const records = ref([]);

            // 获取中奖记录
            const fetchLotteryRecords = async () => {
                try {
                    const params = new URLSearchParams();
                    params.append('page', currentPage.value);
                    params.append('pageSize', pageSize.value);
                    
                    const res = await axios.post("/plugin/Lottery/user/getLotteryRecords", params);
                    
                    if (res.data?.code === 200) {
                        records.value = res.data.data.list;
                        total.value = res.data.data.total;
                    } else {
                        ElMessage.error(res.data?.msg || '获取记录失败');
                    }
                } catch (error) {
                    console.error('获取中奖记录失败:', error);
                    ElMessage.error('获取记录失败');
                }
            };

            // 处理页码变化
            const handleCurrentChange = (val) => {
                currentPage.value = val;
                fetchLotteryRecords();
            };

            // 获取奖品类型对应的标签类型
            const getPrizeTypeTag = (type) => {
                switch (type) {
                    case 'cash':
                        return 'danger';
                    case 'virtual':
                        return 'success';
                    case 'physical':
                        return 'warning';
                    default:
                        return 'info';
                }
            };

            // 检查凌晨重置
            const checkReset = () => {
                const now = new Date();
                const tomorrow = new Date(now);
                tomorrow.setDate(tomorrow.getDate() + 1);
                tomorrow.setHours(0, 0, 0, 0);
                
                const timeToReset = tomorrow - now;
                
                setTimeout(() => {
                    fetchData(); // 重新获取配置和次数
                    checkReset(); // 设置下一天的重置
                }, timeToReset);
            };

            // 添加一个用于调试的方法（可选）
            const debugPrizePosition = () => {
                const currentRotation = getCurrentRotation(turntableCanvas.value);
                console.log('当前转盘旋转角度:', currentRotation);
                console.log('当前指向奖品:', getCurrentPrize());
                console.log('所有奖品位置:', prizePositions.value);
            };

            // 添加定时刷新
            const refreshConfig = () => {
                Promise.all([
                    fetchData(),
                    fetchTurnoverStats()
                ]).catch(error => {
                    console.error('刷新数据失败:', error);
                });
                setTimeout(refreshConfig, 60000); // 每分钟刷新一次
            };

            // 流水统计数据
            const turnoverStats = ref({
                today_amount: 0,
                today_draws: 0,
                next_required: 0
            });

            const turnoverRules = ref([]);

            // 获取流水统计
            const fetchTurnoverStats = async () => {
                try {
                    const res = await axios.post("/plugin/Lottery/user/getTurnoverStats");
                    if (res.data?.code === 200) {
                        turnoverStats.value = res.data.data.stats;
                        turnoverRules.value = res.data.data.rules;
                        
                        // 如果配置已更新，重新获取抽奖配置
                        if (res.data.data.config_updated) {
                            await fetchData();
                            ElMessage.success('已获得新的抽奖次数！');
                        }
                    }
                } catch (error) {
                    console.error('获取流水统计失败:', error);
                    ElMessage.error('获取流水统计失败');
                }
            };

            // 刷新流水统计
            const refreshTurnoverStats = () => {
                fetchTurnoverStats();
                ElMessage.success('刷新成功');
            };

            // 监听标签页切换
            watch(activeTab, (newVal) => {
                if (newVal === 'turnover') {
                    fetchTurnoverStats();
                }
            });

            // 修改配置监听
            watch(() => config.value, (newConfig) => {
                if (newConfig && newConfig.daily_limit) {
                    remainingBaseDraws.value = Math.max(0, newConfig.daily_limit - (merchantLimit.value?.used_count || 0));
                }
            }, { deep: true });

            // 在 setup 函数中添加 handleSizeChange 方法
            const handleSizeChange = (val) => {
                pageSize.value = val;
                currentPage.value = 1; // 切换每页显示数量时重置为第一页
                fetchLotteryRecords();
            };

            // 添加中奖用户相关的响应式数据
            const allRecords = ref([]);
            const winnersCurrentPage = ref(1);
            const winnersPageSize = ref(10);
            const winnersTotal = ref(0);

            // 获取所有中奖记录
            const fetchAllWinners = async () => {
                try {
                    const res = await axios.get('/plugin/Lottery/user/getLotteryRecords', {
                        params: {
                            page: winnersCurrentPage.value,
                            pageSize: winnersPageSize.value,
                            type: 'winners' // 添加type参数，用于标识是获取中奖用户列表
                        }
                    });
                    
                    if (res.data.code === 200) {
                        allRecords.value = res.data.data.list;
                        winnersTotal.value = res.data.data.total;
                    }
                } catch (error) {
                    console.error('获取中奖记录失败:', error);
                    ElMessage.error('获取中奖记录失败');
                }
            };

            // 处理中奖用户分页大小变化
            const handleWinnersSizeChange = (val) => {
                winnersPageSize.value = val;
                winnersCurrentPage.value = 1;
                fetchAllWinners();
            };

            // 处理中奖用户页码变化
            const handleWinnersCurrentChange = (val) => {
                winnersCurrentPage.value = val;
                fetchAllWinners();
            };

            // 监听标签页切换
            watch(activeTab, (newVal) => {
                if (newVal === 'winners') {
                    fetchAllWinners();
                }
            });

            // 添加奖品类型样式函数
            const getPrizeTypeStyle = (type) => {
                switch (type) {
                    case 'physical':
                        return 'warning';
                    case 'virtual':
                        return 'success';
                    case 'cash':
                        return 'danger';
                    default:
                        return 'info';
                }
            };

            // 添加奖品类型文本转换函数
            const getPrizeTypeText = (type) => {
                switch (type) {
                    case 'physical':
                        return '实物';
                    case 'virtual':
                        return '虚拟';
                    case 'cash':
                        return '现金';
                    default:
                        return '未知';
                }
            };

            onMounted(() => {
                // 设置canvas尺寸
                const canvas = turntableCanvas.value;
                const dpr = window.devicePixelRatio || 1;
                const rect = canvas.getBoundingClientRect();
                canvas.width = rect.width * dpr;
                canvas.height = rect.height * dpr;
                
                // 获取数据并初始化转盘
                fetchData();
                fetchLotteryRecords();
                checkReset(); // 添加重置检查
                refreshConfig();
                if (activeTab.value === 'turnover') {
                    fetchTurnoverStats();
                }
            });

            return {
                turntableCanvas,
                isRotating,
                remainingBaseDraws,
                turnoverDraws,
                totalDraws,
                canDraw,
                drawMessage,
                startLottery,
                prizes,
                myRecords,
                showRecords,
                getPrizeTypeTag,
                activeTab,
                isInDrawTime,
                timeMessage,
                records,
                currentPage,
                pageSize,
                total,
                handleCurrentChange,
                currentPrize,
                showResult,
                turnoverStats,
                turnoverRules,
                refreshTurnoverStats,
                merchantLimit,
                handleSizeChange,
                config,
                allRecords,
                winnersCurrentPage,
                winnersPageSize,
                winnersTotal,
                handleWinnersSizeChange,
                handleWinnersCurrentChange,
                getPrizeTypeStyle,
                getPrizeTypeText
            };
        }
    });

    app.use(ElementPlus, {
        locale: ElementPlusLocaleZhCn
    });

    app.mount("#app");

    // 添加樱花动画相关代码
    function createSakura() {
        const sakuraContainer = document.getElementById('sakura-container');
        const sakura = document.createElement('span');
        const size = Math.random() * 20 + 10;
        
        sakura.className = 'sakura';
        sakura.style.width = `${size}px`;
        sakura.style.height = `${size}px`;
        sakura.style.left = Math.random() * window.innerWidth + 'px';
        sakura.style.top = '-50px';
        sakura.style.background = `rgba(255, ${Math.random() * 100 + 155}, ${Math.random() * 100 + 155}, 0.8)`;
        sakura.style.borderRadius = '50%';
        sakura.style.filter = 'blur(1px)';
        
        const animation = sakura.animate([
            {
                transform: `translate(0, 0) rotate(0deg)`,
                opacity: 1
            },
            {
                transform: `translate(${Math.random() * 200 - 100}px, ${window.innerHeight + 50}px) rotate(${Math.random() * 720}deg)`,
                opacity: 0
            }
        ], {
            duration: Math.random() * 4000 + 5000,
            easing: 'linear'
        });
        
        sakuraContainer.appendChild(sakura);
        
        animation.onfinish = () => {
            sakura.remove();
        };
    }

    // 定期创建樱花
    function startSakuraAnimation() {
        setInterval(createSakura, 300);
    }

    // 在页面加载完成后启动樱花动画
    window.addEventListener('load', () => {
        startSakuraAnimation();
    });
    </script>
</body>
</html>
