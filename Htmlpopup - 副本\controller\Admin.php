<?php

namespace plugin\Htmlpopup\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;

class Admin extends BasePlugin {

    // 指定只有管理员可以访问
    protected $scene = ['admin'];

    // 指定不需要登录验证的方法
    protected $noNeedLogin = [];

    public function index() {
        return View::fetch();
    }

    // 获取内置模板列表
    public function getTemplates() {
        try {
            // 获取内置模板配置
            $templates = $this->getBuiltinTemplates();

            return json(['code' => 200, 'data' => $templates]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    // 保存内置模板
    public function saveTemplate() {
        try {
            // 使用原始POST数据获取内容，避免ThinkPHP的过滤
            $postData = json_decode(file_get_contents('php://input'), true);

            $templateKey = $postData['templateKey'] ?? input('templateKey');
            $templateName = $postData['templateName'] ?? input('templateName');
            $templateContent = $postData['templateContent'] ?? input('templateContent', '', null); // 不进行任何过滤

            if (empty($templateKey) || empty($templateName) || empty($templateContent)) {
                return json(['code' => 0, 'msg' => '参数不完整']);
            }

            // 验证模板标识格式
            if (!preg_match('/^[a-zA-Z0-9_]+$/', $templateKey)) {
                return json(['code' => 0, 'msg' => '模板标识只能包含字母、数字和下划线']);
            }

            // 获取现有模板配置
            $templates = $this->getBuiltinTemplates();

            // 更新或添加模板
            $templates[$templateKey] = [
                'name' => trim($templateName),
                'content' => $templateContent // 保持原始HTML内容，不进行任何转义或过滤
            ];

            // 保存到配置，使用JSON_UNESCAPED_UNICODE确保中文正确保存
            $jsonData = json_encode($templates, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            if ($jsonData === false) {
                return json(['code' => 0, 'msg' => '模板数据编码失败']);
            }

            plugconf('Htmlpopup.builtin_templates', $jsonData);

            // 验证保存是否成功 - 重新读取数据进行验证
            $savedTemplates = $this->getBuiltinTemplates();
            $savedContent = $savedTemplates[$templateKey]['content'] ?? '';



            return json(['code' => 200, 'msg' => '保存成功', 'data' => $savedTemplates]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    // 删除内置模板
    public function deleteTemplate() {
        try {
            $templateKey = input('templateKey');

            if (empty($templateKey)) {
                return json(['code' => 0, 'msg' => '模板标识不能为空']);
            }

            // 获取现有模板配置
            $templates = $this->getBuiltinTemplates();

            if (!isset($templates[$templateKey])) {
                return json(['code' => 0, 'msg' => '模板不存在']);
            }

            // 删除模板
            unset($templates[$templateKey]);

            // 保存到配置
            plugconf('Htmlpopup.builtin_templates', json_encode($templates));

            return json(['code' => 200, 'msg' => '删除成功', 'data' => $templates]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }

    // 获取内置模板配置
    private function getBuiltinTemplates() {
        $templatesJson = plugconf('Htmlpopup.builtin_templates');

        if (empty($templatesJson)) {
            // 返回默认模板
            return [
                'purchase_agreement' => [
                    'name' => '购买协议模板',
                    'content' => $this->getDefaultPurchaseAgreement()
                ],
                'platform_rules' => [
                    'name' => '平台规则模板',
                    'content' => $this->getDefaultPlatformRules()
                ],
                'modern_card' => [
                    'name' => '现代卡片风格模板',
                    'content' => $this->getModernCardTemplate()
                ],
                'gradient_notice' => [
                    'name' => '渐变通知模板',
                    'content' => $this->getGradientNoticeTemplate()
                ],
                'simple_announcement' => [
                    'name' => '简洁公告模板',
                    'content' => $this->getSimpleAnnouncementTemplate()
                ],
                'feature_showcase' => [
                    'name' => '功能展示模板',
                    'content' => $this->getFeatureShowcaseTemplate()
                ]
            ];
        }

        // 解码JSON数据
        $templates = json_decode($templatesJson, true);

        // 检查JSON解码是否成功
        if (json_last_error() !== JSON_ERROR_NONE) {
            // JSON解码失败，返回默认模板
            return [
                'purchase_agreement' => [
                    'name' => '购买协议模板',
                    'content' => $this->getDefaultPurchaseAgreement()
                ],
                'platform_rules' => [
                    'name' => '平台规则模板',
                    'content' => $this->getDefaultPlatformRules()
                ]
            ];
        }

        // 确保每个模板都有name和content字段
        foreach ($templates as $key => $template) {
            if (!isset($template['name']) || !isset($template['content'])) {
                unset($templates[$key]);
            }
        }

        return $templates ?: [];
    }

    // 获取默认购买协议模板
    private function getDefaultPurchaseAgreement() {
        return '<div id="buy-protocol">
    <p><strong><span style="color:#303133;"><span style="font-size:18px;">小火羊云寄售官方频道@xhyfkw</span></span></strong></p>
    <p><strong><span style="color:#ff6600;"><span style="font-size:18px;">温馨提示：本站不提供任何担保、私下交易被骗一律与本站无关。</span></span></strong></p>
    <p><span style="color:#ff00ff;">1、本平台仅提供自动发卡、寄售服务，本平台非售卖销售商，有问题联系店铺客服或者平台投诉订单处理，非商品卡密问题本站不予受理售后争议</span></p>
    <p><span style="color:#27ae60;">2、平台提供卡密查询时间为1个月（购买后请自行保存）如遇假卡/欺诈/涉黄赌/等违规商品请当天23点50前去平台投诉订单处理，若是超过24点后，平台放款给商家，不再受理任何问题。</span></p>
    <p><span style="color:#ae2760;">3、为了方便售后建议您的每次购物都录个视频，一直到商品正常使用没问题在取消录制哦！</span></p>
    <hr style="border: 1px dashed #ccc; margin: 20px 0;">
    <div style="text-align: center;">
        <p><strong>请仔细阅读以上协议，点击同意即表示您已完全理解并接受所有条款</strong></p>
    </div>
</div>';
    }

    // 获取默认平台规则模板
    private function getDefaultPlatformRules() {
        return '<div id="platform-rules">
    <h3 style="color: #409eff; text-align: center;">平台使用规则</h3>
    <div style="background: #f0f9eb; padding: 15px; border-radius: 8px; border-left: 4px solid #67c23a;">
        <h4 style="color: #67c23a;">✅ 允许的行为</h4>
        <ul>
            <li>正常浏览和购买商品</li>
            <li>合理使用平台功能</li>
            <li>遵守法律法规</li>
        </ul>
    </div>
    <div style="background: #fef0f0; padding: 15px; border-radius: 8px; border-left: 4px solid #f56c6c; margin-top: 15px;">
        <h4 style="color: #f56c6c;">❌ 禁止的行为</h4>
        <ul>
            <li>恶意刷单或虚假交易</li>
            <li>发布违法违规内容</li>
            <li>恶意攻击平台系统</li>
        </ul>
    </div>
    <div style="text-align: center; margin-top: 20px;">
        <p><strong>违反规则将面临账号封禁等处罚</strong></p>
    </div>
</div>';
    }

    // 现代卡片风格模板
    private function getModernCardTemplate() {
        return '<div style="max-width: 500px; margin: 0 auto; font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif;">
    <!-- 头部区域 -->
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px 25px; border-radius: 12px 12px 0 0; text-align: center;">
        <div style="font-size: 24px; font-weight: 600; margin-bottom: 8px;">🎉 欢迎使用我们的服务</div>
        <div style="font-size: 14px; opacity: 0.9;">感谢您的信任与支持</div>
    </div>

    <!-- 内容区域 -->
    <div style="background: white; padding: 25px; border-radius: 0 0 12px 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
        <div style="margin-bottom: 20px;">
            <h3 style="color: #2c3e50; margin: 0 0 15px 0; font-size: 18px;">📋 重要提醒</h3>
            <p style="color: #5a6c7d; line-height: 1.6; margin: 0;">请仔细阅读以下内容，确保您了解我们的服务条款和使用规则。</p>
        </div>

        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff;">
            <div style="color: #007bff; font-weight: 600; margin-bottom: 10px;">💡 温馨提示</div>
            <ul style="color: #495057; margin: 0; padding-left: 20px; line-height: 1.8;">
                <li>请保管好您的账户信息</li>
                <li>如有疑问请及时联系客服</li>
                <li>遵守平台使用规范</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 25px; padding-top: 20px; border-top: 1px solid #e9ecef;">
            <div style="color: #6c757d; font-size: 13px;">点击确认即表示您已阅读并同意相关条款</div>
        </div>
    </div>
</div>';
    }

    // 渐变通知模板
    private function getGradientNoticeTemplate() {
        return '<div style="max-width: 450px; margin: 0 auto; background: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3); padding: 3px; border-radius: 15px;">
    <div style="background: white; border-radius: 12px; padding: 25px; text-align: center;">
        <div style="font-size: 48px; margin-bottom: 15px;">🔔</div>
        <h2 style="background: linear-gradient(45deg, #ff6b6b, #4ecdc4); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin: 0 0 15px 0; font-size: 22px; font-weight: 700;">
            系统通知
        </h2>
        <div style="color: #2c3e50; line-height: 1.6; margin-bottom: 20px;">
            <p style="margin: 0 0 10px 0;">亲爱的用户，您好！</p>
            <p style="margin: 0;">我们有重要信息需要告知您，请仔细阅读以下内容。</p>
        </div>

        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px; border-radius: 8px; margin: 15px 0;">
            <div style="font-weight: 600; margin-bottom: 8px;">📢 重要更新</div>
            <div style="font-size: 14px; opacity: 0.95;">
                • 新功能已上线，欢迎体验<br>
                • 系统维护时间：每日凌晨2-4点<br>
                • 如有问题请联系在线客服
            </div>
        </div>

        <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #e9ecef; color: #6c757d; font-size: 12px;">
            感谢您的理解与支持 ❤️
        </div>
    </div>
</div>';
    }

    // 简洁公告模板
    private function getSimpleAnnouncementTemplate() {
        return '<div style="max-width: 400px; margin: 0 auto; background: #ffffff; border: 2px solid #e3f2fd; border-radius: 10px; overflow: hidden; font-family: \'Helvetica Neue\', Arial, sans-serif;">
    <!-- 标题栏 -->
    <div style="background: #2196f3; color: white; padding: 15px 20px; text-align: center;">
        <h3 style="margin: 0; font-size: 18px; font-weight: 500;">📋 平台公告</h3>
    </div>

    <!-- 内容区域 -->
    <div style="padding: 25px 20px;">
        <div style="text-align: center; margin-bottom: 20px;">
            <div style="display: inline-block; background: #fff3cd; color: #856404; padding: 8px 16px; border-radius: 20px; font-size: 14px; border: 1px solid #ffeaa7;">
                ⚠️ 重要提醒
            </div>
        </div>

        <div style="color: #333; line-height: 1.7; margin-bottom: 20px;">
            <p style="margin: 0 0 12px 0;">尊敬的用户：</p>
            <p style="margin: 0 0 12px 0;">为了给您提供更好的服务体验，我们将进行系统升级。</p>
            <p style="margin: 0;">升级期间可能会影响部分功能的正常使用，敬请谅解。</p>
        </div>

        <!-- 信息卡片 -->
        <div style="background: #f8f9fa; border-left: 4px solid #28a745; padding: 15px; border-radius: 0 6px 6px 0; margin: 15px 0;">
            <div style="color: #28a745; font-weight: 600; margin-bottom: 8px;">✅ 升级内容</div>
            <div style="color: #495057; font-size: 14px;">
                • 优化系统性能<br>
                • 修复已知问题<br>
                • 新增便民功能
            </div>
        </div>

        <div style="text-align: center; margin-top: 20px; color: #6c757d; font-size: 13px;">
            如有疑问，请联系客服：400-123-4567
        </div>
    </div>
</div>';
    }

    // 功能展示模板
    private function getFeatureShowcaseTemplate() {
        return '<div style="max-width: 480px; margin: 0 auto; background: #fff; border-radius: 16px; box-shadow: 0 8px 32px rgba(0,0,0,0.12); overflow: hidden; font-family: -apple-system, BlinkMacSystemFont, sans-serif;">
    <!-- 头部横幅 -->
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px 25px; text-align: center; position: relative;">
        <div style="position: absolute; top: 10px; right: 15px; background: rgba(255,255,255,0.2); color: white; padding: 4px 8px; border-radius: 12px; font-size: 11px;">
            NEW
        </div>
        <div style="color: white; font-size: 28px; margin-bottom: 8px;">🚀</div>
        <h2 style="color: white; margin: 0 0 8px 0; font-size: 20px; font-weight: 600;">全新功能上线</h2>
        <p style="color: rgba(255,255,255,0.9); margin: 0; font-size: 14px;">体验更强大的服务功能</p>
    </div>

    <!-- 功能列表 -->
    <div style="padding: 25px;">
        <div style="margin-bottom: 20px;">
            <div style="display: flex; align-items: center; margin-bottom: 15px;">
                <div style="width: 40px; height: 40px; background: #e3f2fd; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px;">
                    <span style="font-size: 18px;">⚡</span>
                </div>
                <div>
                    <div style="font-weight: 600; color: #2c3e50; margin-bottom: 4px;">极速处理</div>
                    <div style="color: #6c757d; font-size: 13px;">处理速度提升300%</div>
                </div>
            </div>

            <div style="display: flex; align-items: center; margin-bottom: 15px;">
                <div style="width: 40px; height: 40px; background: #e8f5e8; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px;">
                    <span style="font-size: 18px;">🔒</span>
                </div>
                <div>
                    <div style="font-weight: 600; color: #2c3e50; margin-bottom: 4px;">安全保障</div>
                    <div style="color: #6c757d; font-size: 13px;">银行级安全加密</div>
                </div>
            </div>

            <div style="display: flex; align-items: center;">
                <div style="width: 40px; height: 40px; background: #fff3cd; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px;">
                    <span style="font-size: 18px;">🎯</span>
                </div>
                <div>
                    <div style="font-weight: 600; color: #2c3e50; margin-bottom: 4px;">精准服务</div>
                    <div style="color: #6c757d; font-size: 13px;">个性化推荐算法</div>
                </div>
            </div>
        </div>

        <!-- 底部提示 -->
        <div style="background: linear-gradient(90deg, #f093fb 0%, #f5576c 100%); color: white; padding: 15px; border-radius: 10px; text-align: center;">
            <div style="font-weight: 600; margin-bottom: 5px;">🎉 限时体验</div>
            <div style="font-size: 13px; opacity: 0.95;">立即体验新功能，享受专属优惠</div>
        </div>
    </div>
</div>';
    }

    // 重置模板数据（调试用）
    public function resetTemplates() {
        try {
            // 清除现有配置
            plugconf('Htmlpopup.builtin_templates', '');

            // 获取默认模板（这会重新生成默认模板）
            $templates = $this->getBuiltinTemplates();

            return json(['code' => 200, 'msg' => '模板数据已重置', 'data' => $templates]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '重置失败：' . $e->getMessage()]);
        }
    }



    // 获取全局设置
    public function getGlobalSettings() {
        try {
            $settings = [
                'popupEnabled' => (bool)(plugconf('Htmlpopup.global_popup_enabled') ?? true)
            ];

            return json(['code' => 200, 'data' => $settings]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取全局设置失败：' . $e->getMessage()]);
        }
    }

    // 保存全局设置
    public function saveGlobalSettings() {
        try {
            $data = input('post.');

            // 保存弹窗总开关
            if (isset($data['popupEnabled'])) {
                plugconf('Htmlpopup.global_popup_enabled', (bool)$data['popupEnabled']);
            }



            return json(['code' => 200, 'msg' => '全局设置保存成功']);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '保存全局设置失败：' . $e->getMessage()]);
        }
    }

    // 获取弹窗配置
    public function getPopupConfig() {
        try {
            $config = [
                'frequency' => plugconf('Htmlpopup.global_frequency') ?? 'once',
                'title' => plugconf('Htmlpopup.global_title') ?? 'HTML弹窗',
                'button_color' => plugconf('Htmlpopup.global_button_color') ?? 'blue',
                'reject_action' => plugconf('Htmlpopup.global_reject_action') ?? 'none',
                'reject_url' => plugconf('Htmlpopup.global_reject_url') ?? '',
                'close_confirm' => (bool)(plugconf('Htmlpopup.global_close_confirm') ?? false),
                'default_template' => plugconf('Htmlpopup.global_default_template') ?? 'purchase_agreement'
            ];

            return json(['code' => 200, 'data' => $config]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取弹窗配置失败：' . $e->getMessage()]);
        }
    }

    // 保存弹窗配置
    public function savePopupConfig() {
        try {
            $data = input('post.');

            // 保存弹出频率
            if (isset($data['frequency'])) {
                plugconf('Htmlpopup.global_frequency', $data['frequency']);
            }

            // 保存弹窗标题
            if (isset($data['title'])) {
                plugconf('Htmlpopup.global_title', $data['title']);
            }

            // 保存按钮颜色
            if (isset($data['button_color'])) {
                plugconf('Htmlpopup.global_button_color', $data['button_color']);
            }

            // 保存拒绝后操作
            if (isset($data['reject_action'])) {
                plugconf('Htmlpopup.global_reject_action', $data['reject_action']);
            }

            // 保存跳转地址
            if (isset($data['reject_url'])) {
                plugconf('Htmlpopup.global_reject_url', $data['reject_url']);
            }

            // 保存关闭确认
            if (isset($data['close_confirm'])) {
                plugconf('Htmlpopup.global_close_confirm', (bool)$data['close_confirm']);
            }

            // 保存默认模板
            if (isset($data['default_template'])) {
                plugconf('Htmlpopup.global_default_template', $data['default_template']);
            }

            return json(['code' => 200, 'msg' => '弹窗配置保存成功']);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '保存弹窗配置失败：' . $e->getMessage()]);
        }
    }
}