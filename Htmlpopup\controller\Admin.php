<?php

namespace plugin\Htmlpopup\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;

class Admin extends BasePlugin {

    // 指定只有管理员可以访问
    protected $scene = ['admin'];

    // 指定不需要登录验证的方法
    protected $noNeedLogin = [];

    public function index() {
        return View::fetch();
    }

    // 获取内置模板列表
    public function getTemplates() {
        try {
            // 获取内置模板配置
            $templates = $this->getBuiltinTemplates();

            return json(['code' => 200, 'data' => $templates]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取模板文本配置
     */
    public function getTemplateTexts() {
        try {
            $texts = plugconf('Htmlpopup.template_texts') ?: $this->getDefaultTemplateTexts();
            return json(['code' => 200, 'data' => $texts]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取模板文本失败']);
        }
    }

    /**
     * 保存模板文本配置
     */
    public function saveTemplateTexts() {
        try {
            // 直接获取POST数据，不需要嵌套在texts字段中
            $texts = input('post.');



            // 验证必要字段
            $requiredFields = [
                'logoUrl', 'platformName', 'platformChannel', 'warmTip',
                'shopLink', 'shopButtonText', 'groupLink', 'groupButtonText',
                'serviceLink', 'serviceButtonText', 'customerServiceNotice',
                'notice1', 'notice2', 'notice3', 'warningTitle',
                'warning1', 'warning2', 'warning3', 'warning4', 'warning5', 'footerText'
            ];

            foreach ($requiredFields as $field) {
                if (!isset($texts[$field])) {
                    return json(['code' => 0, 'msg' => "缺少必要字段: {$field}"]);
                }
            }

            // 处理特殊字符，防止编码问题
            foreach ($texts as $key => $value) {
                if (is_string($value)) {
                    // 1. 解码HTML实体，确保引号正常显示
                    $value = html_entity_decode($value, ENT_QUOTES | ENT_HTML5, 'UTF-8');

                    // 2. 处理常见的特殊字符编码问题
                    $value = str_replace([
                        '&quot;',      // HTML实体的双引号
                        '&#39;',       // HTML实体的单引号
                        '&amp;',       // HTML实体的&符号
                        '&lt;',        // HTML实体的<符号
                        '&gt;',        // HTML实体的>符号
                        '&nbsp;',      // HTML实体的空格
                    ], [
                        '"',           // 正常的双引号
                        "'",           // 正常的单引号
                        '&',           // 正常的&符号
                        '<',           // 正常的<符号
                        '>',           // 正常的>符号
                        ' ',           // 正常的空格
                    ], $value);

                    // 3. 确保字符串是UTF-8编码
                    $texts[$key] = mb_convert_encoding($value, 'UTF-8', 'UTF-8');
                }
            }

            // 保存到配置
            $result = plugconf('Htmlpopup.template_texts', $texts);

            return json(['code' => 200, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }



    /**
     * 获取所有配置数据（用于前端统一加载）
     */
    public function getConfig() {
        try {
            // 获取各项配置
            $globalSettings = [
                'popupEnabled' => (bool)(plugconf('Htmlpopup.global_popup_enabled') ?? true)
            ];

            $popupConfig = [
                'frequency' => plugconf('Htmlpopup.global_frequency') ?? 'once',
                'title' => plugconf('Htmlpopup.global_title') ?? 'HTML弹窗',
                'enable_scrollbar' => (bool)(plugconf('Htmlpopup.enable_scrollbar') ?? false),
                'enable_agree_delay' => (bool)(plugconf('Htmlpopup.enable_agree_delay') ?? false),
                'agree_delay_seconds' => (int)(plugconf('Htmlpopup.agree_delay_seconds') ?? 5)
            ];

            $templateTexts = plugconf('Htmlpopup.template_texts') ?: $this->getDefaultTemplateTexts();

            $config = [
                'globalSettings' => $globalSettings,
                'popupConfig' => $popupConfig,
                'templateTexts' => $templateTexts
            ];

            return json(['code' => 200, 'data' => $config]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取配置失败：' . $e->getMessage()]);
        }
    }





    // 获取内置模板配置
    private function getBuiltinTemplates() {
        // 只保留购买协议模板
        $defaultTemplates = [
            'purchase_agreement' => [
                'name' => '购买协议模板',
                'content' => $this->getDefaultPurchaseAgreement()
            ]
        ];

        // 获取已保存的模板配置
        $templatesData = plugconf('Htmlpopup.builtin_templates');

        if (empty($templatesData)) {
            // 如果没有保存的配置，返回默认模板
            return $defaultTemplates;
        }

        // 处理不同的数据类型
        $savedTemplates = null;
        if (is_string($templatesData)) {
            // 如果是字符串，尝试JSON解码
            $savedTemplates = json_decode($templatesData, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return $defaultTemplates;
            }
        } elseif (is_array($templatesData)) {
            // 如果已经是数组，直接使用
            $savedTemplates = $templatesData;
        } else {
            // 其他类型，使用默认模板
            return $defaultTemplates;
        }

        // 合并保存的模板和默认模板
        $finalTemplates = $defaultTemplates;

        if (is_array($savedTemplates)) {
            foreach ($savedTemplates as $key => $template) {
                // 验证模板数据完整性
                if (isset($template['name']) && isset($template['content']) &&
                    !empty($template['content']) && isset($finalTemplates[$key])) {
                    // 使用保存的内容覆盖默认内容
                    $finalTemplates[$key]['content'] = $template['content'];
                }
            }
        }

        return $finalTemplates;
    }

    // 获取默认购买协议模板
    private function getDefaultPurchaseAgreement() {
        return '<style>
    body {
       font-family: "Microsoft YaHei", sans-serif; /* 修改字体 */
         margin: 0;
        padding: 0;
    }
    .card {
        max-width: 600px;
        margin: 0 auto;
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 6px 18px rgba(0, 0, 0, 0.08);
    }
    .header {
        background: linear-gradient(135deg, #6e8efb, #a777e3);
        color: white;
        padding: 20px;
        text-align: center;
    }
    .header h3 {
        margin: 0;
        font-size: 22px;
        font-weight: 600;
        letter-spacing: 0.5px;
    }
    .content {
        padding: 20px;
    }
    .alert {
        background-color: #fff8e1;
        border-left: 4px solid #ffc107;
        padding: 12px;
        margin-bottom: 20px;
        border-radius: 0 4px 4px 0;
        font-weight: 500;
        color: #e65100;
    }
    .btn {
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #6e8efb, #a777e3);
        color: white;
        border: none;
        padding: 12px;
        margin: 12px 0;
        border-radius: 8px;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
    .btn img {
        width: 20px;
        height: 20px;
        margin-right: 10px;
    }
    .notice {
        background-color: #f3e5f5;
        border-left: 4px solid #9c27b0;
        padding: 12px;
        margin: 15px 0;
        border-radius: 0 4px 4px 0;
        font-size: 15px;
        line-height: 1.5;
    }
    .warning-box {
        background-color: #ffebee;
        border-radius: 8px;
        padding: 15px;
        margin-top: 20px;
    }
    .warning-title {
        color: #d32f2f;
        font-weight: 600;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
    }
    .warning-title:before {
        content: "⚠️";
        margin-right: 8px;
    }
    .warning-item {
        margin-bottom: 10px;
        font-size: 14px;
        line-height: 1.5;
        position: relative;
        padding-left: 20px;
    }
    .warning-item:before {
        content: "•";
        color: #d32f2f;
        font-weight: bold;
        position: absolute;
        left: 0;
    }
    .footer {
        background: #f5f5f5;
        padding: 15px;
        text-align: center;
        font-size: 12px;
        color: #666;
    }
</style>
<body>
<div class="card">
  <div class="header">
    <img src="https://shop.xhyfaka.com/xhylogo.gif" alt="本地图片" style="width: 180px; height: 60px;">
  <!--  <h3>小火羊云寄售温馨提示</h3> -->
</div>

    <div class="content">
        <div class="alert">
            本站不提供任何担保，私下交易被骗一律与本站无关 频道：@xhyfkw
        </div>

<a href="https://shop.xhyfaka.com/merchant/" class="btn">
<svg t="1744595050613" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1316" width="24" height="24"><path d="M670.6176 715.2128m-179.0976 0a179.0976 179.0976 0 1 0 358.1952 0 179.0976 179.0976 0 1 0-358.1952 0Z" fill="#FFBE0A" p-id="1317"></path><path d="M695.1424 311.7056a219.7504 219.7504 0 1 0-356.1472 172.0832 362.8032 362.8032 0 0 0-232.8064 338.2272 75.9808 75.9808 0 0 0 75.9296 75.9296h260.5568a35.84 35.84 0 0 0 0-71.68H182.1184a4.2496 4.2496 0 0 1-4.2496-4.2496 290.9184 290.9184 0 0 1 290.56-290.56h13.9776a29.8496 29.8496 0 0 0 4.3008-0.3072 220.16 220.16 0 0 0 208.4352-219.4432zM475.4432 459.776a148.0704 148.0704 0 1 1 148.0192-148.0704A148.224 148.224 0 0 1 475.4432 459.776zM714.2912 833.2288a35.84 35.84 0 0 1-24.5248-9.728L603.4944 742.4a35.84 35.84 0 1 1 49.1008-52.224l58.1632 54.6304L845.312 578.56a35.84 35.84 0 0 1 55.808 45.312l-158.72 196.096a35.84 35.84 0 0 1-25.6 13.1584 19.712 19.712 0 0 1-2.5088 0.1024z" fill="#34332E" p-id="1318"></path></svg>
    注册商家同款店铺
</a>

<a href="https://shop.xhyfaka.com/35c378d8-590b-45a1-8767-18e34b083da5.png" class="btn">
<svg t="1744595015666" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1163" width="24" height="24"><path d="M608.256 565.3504m-179.0976 0a179.0976 179.0976 0 1 0 358.1952 0 179.0976 179.0976 0 1 0-358.1952 0Z" fill="#FFBE0A" p-id="1164"></path><path d="M635.5968 921.6a132.5568 132.5568 0 0 1-117.4016-69.9392l-116.3264-210.4832-231.1168-103.1168a119.6544 119.6544 0 0 1 7.1168-221.44l552.96-205.3632a135.3216 135.3216 0 0 1 178.176 160.5632l-141.1584 547.84a132.8128 132.8128 0 0 1-113.9712 100.4544 144.6912 144.6912 0 0 1-18.2784 1.4848zM202.8544 384a47.9744 47.9744 0 0 0-2.8672 88.7808l242.0736 108.032a35.84 35.84 0 0 1 16.7424 15.36l122.112 220.8768a63.6928 63.6928 0 0 0 117.3504-14.9504l141.1072-547.84a63.6416 63.6416 0 0 0-83.8144-75.52L202.8032 384z" fill="#34332E" p-id="1165"></path><path d="M532.48 529.6128a35.84 35.84 0 0 1-25.6-60.9792l152.064-154.4704a35.84 35.84 0 1 1 51.2 50.2784L558.08 518.912a35.84 35.84 0 0 1-25.6 10.7008z" fill="#34332E" p-id="1166"></path></svg>
    本平台商家通知群
</a>

<a href="https://shop.xhyfaka.com/kf.html" class="btn">
<svg t="1744594983943" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1010" width="24" height="24"><path d="M512 533.1968m-184.2176 0a184.2176 184.2176 0 1 0 368.4352 0 184.2176 184.2176 0 1 0-368.4352 0Z" fill="#FFBE0A" p-id="1011"></path><path d="M914.1248 754.0736A249.2928 249.2928 0 0 0 832.4608 445.44a34.048 34.048 0 0 0-2.8672-1.8432 366.6944 366.6944 0 0 0-377.088-333.8752 366.7456 366.7456 0 0 0-317.44 528.0256c-14.3872 53.4528-30.2592 106.5984-41.4208 142.9504a67.328 67.328 0 0 0 83.3024 84.224l156.5696-46.08a367.0016 367.0016 0 0 0 192.1536 18.8416A249.0368 249.0368 0 0 0 773.12 883.2l79.5648 23.3984a66.56 66.56 0 0 0 18.6368 2.7136 65.9968 65.9968 0 0 0 62.976-84.992c-7.5776-25.1392-14.336-48.7424-20.1728-70.2464z m-443.904 17.1008a290.6624 290.6624 0 0 1-120.832-23.1936 36.1472 36.1472 0 0 0-24.1152-1.3824L164.6592 793.6c12.1344-39.68 28.7232-95.8464 43.264-151.04a35.84 35.84 0 0 0-3.1232-26.2144 295.168 295.168 0 0 1 249.9584-435.2 295.0144 295.0144 0 1 1 15.36 589.824z m374.2208-38.0928a35.84 35.84 0 0 0-3.1232 26.2144c6.0416 22.9376 13.2096 48.2816 21.2992 75.5712l-81.2544-23.8592a35.84 35.84 0 0 0-24.1152 1.3824 176.9984 176.9984 0 0 1-142.4896-1.9456A369.3056 369.3056 0 0 0 721.92 736.8704a365.4144 365.4144 0 0 0 104.3456-199.68 176.9984 176.9984 0 0 1 18.432 196.1472z" fill="#34332E" p-id="1012"></path><path d="M331.1616 512.7168a35.84 35.84 0 0 1-35.84-35.84v-42.1376a35.84 35.84 0 1 1 71.68 0v42.1376a35.84 35.84 0 0 1-35.84 35.84zM569.7536 512.7168a35.84 35.84 0 0 1-35.84-35.84v-42.1376a35.84 35.84 0 0 1 71.68 0v42.1376a35.84 35.84 0 0 1-35.84 35.84z" fill="#34332E" p-id="1013"></path></svg>
    联系平台在线客服
</a>
     <div class="notice" style="border-left-color: #f44336; background-color: #ffebee;">
           注意：平台客服不等于商家客服，有任何问题请优先联系商家客服
        </div>
        <div class="notice">
            1. 本平台仅提供发卡服务，本平台非销售商，非卡密问题本站不予受理售后争议。
        </div>

        <div class="notice">
            2. 平台提供卡密查询时间为10天（购买后请自行保存），如遇违反中华人民共和国相关法律的违规商品，请当天24点前与我们平台客服联系举报。
        </div>

        <div class="notice">
            3. 退货退款：虚拟产品具有可复制性，付款后不能正常使用时,不支持退货退款。您可与商家进行咨询
        </div>

        <div class="warning-box">
            <div class="warning-title">防骗提醒</div>
            <div class="warning-item">警惕脱离平台交易：遇到商品提示 "联系QQ取卡""TG群拿货" 等脱离正规交易平台的信息，切勿轻信。</div>
            <div class="warning-item">留意发货拖延借口：若卖家以各种理由推脱，声称要到第二天发货，需提高警惕。</div>
            <div class="warning-item">关注售后保障问题：当商品出现问题，卖家却拒绝提供售后服务，不处理退换货等事宜，此情况可疑。</div>
            <div class="warning-item">小心充值返现陷阱：对于承诺充值返现的情况，不要被高额返利诱惑。</div>
            <div class="warning-item">核实实物快递情况：购买实物商品时，若未按正常流程安排快递发货，应及时联系平台客服投诉。</div>
        </div>
    </div>

    <div class="footer">
        请谨慎交易，保护好自己的财产安全
    </div>
</div>
</body>';
    }











    // 获取全局设置
    public function getGlobalSettings() {
        try {
            $settings = [
                'popupEnabled' => (bool)(plugconf('Htmlpopup.global_popup_enabled') ?? true)
            ];

            return json(['code' => 200, 'data' => $settings]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取全局设置失败：' . $e->getMessage()]);
        }
    }

    // 保存全局设置
    public function saveGlobalSettings() {
        try {
            $data = input('post.');

            // 保存弹窗总开关
            if (isset($data['popupEnabled'])) {
                plugconf('Htmlpopup.global_popup_enabled', (bool)$data['popupEnabled']);
            }



            return json(['code' => 200, 'msg' => '全局设置保存成功']);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '保存全局设置失败：' . $e->getMessage()]);
        }
    }

    // 获取弹窗配置
    public function getPopupConfig() {
        try {
            $config = [
                'frequency' => plugconf('Htmlpopup.global_frequency') ?? 'once',
                'title' => plugconf('Htmlpopup.global_title') ?? 'HTML弹窗',
                'button_color' => plugconf('Htmlpopup.global_button_color') ?? 'blue',
                'reject_action' => plugconf('Htmlpopup.global_reject_action') ?? 'none',
                'reject_url' => plugconf('Htmlpopup.global_reject_url') ?? '',
                'close_confirm' => (bool)(plugconf('Htmlpopup.global_close_confirm') ?? false),
                'default_template' => plugconf('Htmlpopup.global_default_template') ?? 'purchase_agreement',
                'enable_scrollbar' => (bool)(plugconf('Htmlpopup.enable_scrollbar') ?? false)
            ];

            return json(['code' => 200, 'data' => $config]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取弹窗配置失败：' . $e->getMessage()]);
        }
    }

    // 保存弹窗配置
    public function savePopupConfig() {
        try {
            $data = input('post.');

            // 保存弹出频率
            if (isset($data['frequency'])) {
                plugconf('Htmlpopup.global_frequency', $data['frequency']);
            }

            // 保存弹窗标题
            if (isset($data['title'])) {
                plugconf('Htmlpopup.global_title', $data['title']);
            }

            // 保存按钮颜色
            if (isset($data['button_color'])) {
                plugconf('Htmlpopup.global_button_color', $data['button_color']);
            }

            // 保存拒绝后操作
            if (isset($data['reject_action'])) {
                plugconf('Htmlpopup.global_reject_action', $data['reject_action']);
            }

            // 保存跳转地址
            if (isset($data['reject_url'])) {
                plugconf('Htmlpopup.global_reject_url', $data['reject_url']);
            }

            // 保存关闭确认
            if (isset($data['close_confirm'])) {
                plugconf('Htmlpopup.global_close_confirm', (bool)$data['close_confirm']);
            }

            // 保存默认模板
            if (isset($data['default_template'])) {
                plugconf('Htmlpopup.global_default_template', $data['default_template']);
            }

            // 保存滚动条设置
            if (isset($data['enable_scrollbar'])) {
                plugconf('Htmlpopup.enable_scrollbar', (bool)$data['enable_scrollbar']);
            }

            // 保存同意按钮延迟设置
            if (isset($data['enable_agree_delay'])) {
                plugconf('Htmlpopup.enable_agree_delay', (bool)$data['enable_agree_delay']);
            }

            // 保存同意按钮延迟秒数
            if (isset($data['agree_delay_seconds'])) {
                $seconds = (int)$data['agree_delay_seconds'];
                // 限制范围在1-60秒之间
                $seconds = max(1, min(60, $seconds));
                plugconf('Htmlpopup.agree_delay_seconds', $seconds);
            }

            return json(['code' => 200, 'msg' => '弹窗配置保存成功']);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '保存弹窗配置失败：' . $e->getMessage()]);
        }
    }

    // 保存编辑的模板内容
    public function saveEditedContent() {
        try {
            $data = input('post.');

            if (!isset($data['content']) || empty($data['content'])) {
                return json(['code' => 0, 'msg' => '内容不能为空']);
            }

            $templateKey = $data['template_key'] ?? 'purchase_agreement';
            $content = $data['content'];

            // 清理旧的 edited_template_contents 配置（如果存在）
            $this->cleanupOldEditedContents();

            // 获取现有的内置模板配置（使用统一的方法）
            $templates = $this->getBuiltinTemplates();

            // 确保模板键存在
            if (!isset($templates[$templateKey])) {
                return json(['code' => 0, 'msg' => '模板不存在']);
            }

            // 直接保存内容，保持原始格式
            $templates[$templateKey]['content'] = $content;

            // 手动转换为JSON字符串，确保格式一致
            $jsonString = json_encode($templates, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

            plugconf('Htmlpopup.builtin_templates', $jsonString);

            return json([
                'code' => 200,
                'msg' => '模板内容保存成功'
            ]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '保存失败：' . $e->getMessage()]);
        } catch (\Throwable $e) {
            return json(['code' => 0, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    /**
     * 清理旧的 edited_template_contents 配置
     */
    private function cleanupOldEditedContents() {
        try {
            // 检查是否存在旧的配置
            $oldEditedContents = plugconf('Htmlpopup.edited_template_contents');
            if (!empty($oldEditedContents)) {
                // 删除旧的配置
                plugconf('Htmlpopup.edited_template_contents', '');
            }
        } catch (\Exception $e) {
            // 静默处理异常
        }
    }

    /**
     * 确保内容格式与params.php中的JSON转义格式一致
     */
    private function ensureJsonEscapeFormat($content) {
        // 如果内容已经包含JSON转义字符，说明格式正确，直接返回
        if (strpos($content, '\\n') !== false || strpos($content, '\\"') !== false) {
            return $content;
        }

        // 如果内容是普通HTML（包含实际换行符和引号），需要转换为JSON转义格式
        if (strpos($content, "\n") !== false || strpos($content, '"') !== false) {
            // 转义特殊字符为JSON格式
            $content = addslashes($content); // 转义引号和反斜杠
            $content = str_replace("\n", "\\n", $content); // 转义换行符
            $content = str_replace("\r", "\\r", $content); // 转义回车符
            $content = str_replace("\t", "\\t", $content); // 转义制表符
        }

        return $content;
    }



    /**
     * 获取默认模板文本配置
     */
    private function getDefaultTemplateTexts() {
        return [
            'logoUrl' => 'https://shop.xhyfaka.com/xhylogo.gif',
            'platformName' => '小火羊云寄售官方频道',
            'platformChannel' => '@xhyfkw',
            'warmTip' => '本站不提供任何担保，私下交易被骗一律与本站无关',
            'shopLink' => 'https://shop.xhyfaka.com/merchant/',
            'shopButtonText' => '注册商家同款店铺',
            'groupLink' => 'https://shop.xhyfaka.com/35c378d8-590b-45a1-8767-18e34b083da5.png',
            'groupButtonText' => '本平台商家通知群',
            'serviceLink' => 'https://shop.xhyfaka.com/kf.html',
            'serviceButtonText' => '联系平台在线客服',
            'customerServiceNotice' => '注意：平台客服不等于商家客服，有任何问题请优先联系商家客服',
            'notice1' => '1. 本平台仅提供发卡服务，本平台非销售商，非卡密问题本站不予受理售后争议。',
            'notice2' => '2. 平台提供卡密查询时间为10天（购买后请自行保存），如遇违反中华人民共和国相关法律的违规商品，请当天24点前与我们平台客服联系举报。',
            'notice3' => '3. 退货退款：虚拟产品具有可复制性，付款后不能正常使用时,不支持退货退款。您可与商家进行咨询',
            'warningTitle' => '防骗提醒',
            'warning1' => '警惕脱离平台交易：遇到商品提示"联系QQ取卡"、"TG群拿货"等脱离正规交易平台的信息，切勿轻信。',
            'warning2' => '留意发货拖延借口：若卖家以各种理由推脱，声称要到第二天发货，需提高警惕。',
            'warning3' => '关注售后保障问题：当商品出现问题，卖家却拒绝提供售后服务，不处理退换货等事宜，此情况可疑。',
            'warning4' => '小心充值返现陷阱：对于承诺充值返现的情况，不要被高额返利诱惑。',
            'warning5' => '核实实物快递情况：购买实物商品时，若未按正常流程安排快递发货，应及时联系平台客服投诉。',
            'footerText' => '请谨慎交易，保护好自己的财产安全'
        ];
    }

    /**
     * 生成带有文本配置的HTML模板
     */
    public function generateTemplateWithTexts() {
        try {
            $texts = plugconf('Htmlpopup.template_texts') ?: $this->getDefaultTemplateTexts();
            $html = $this->buildTemplateHtml($texts);

            return json(['code' => 200, 'data' => ['html' => $html, 'texts' => $texts]]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '生成模板失败']);
        }
    }

    /**
     * 构建HTML模板（样式固定，内容可配置）
     */
    private function buildTemplateHtml($texts) {
        return '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: "Microsoft YaHei", sans-serif;
            margin: 0;
            padding: 0;
        }
        .card {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 6px 18px rgba(0, 0, 0, 0.08);
        }
        .header {
            background: linear-gradient(135deg, #6e8efb, #a777e3);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .header h3 {
            margin: 0;
            font-size: 22px;
            font-weight: 600;
            letter-spacing: 0.5px;
        }
        .content {
            padding: 20px;
        }
        .alert {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 12px;
            margin-bottom: 20px;
            border-radius: 0 4px 4px 0;
            font-weight: 500;
            color: #e65100;
        }
        .btn {
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #6e8efb, #a777e3);
            color: white;
            border: none;
            padding: 12px;
            margin: 12px 0;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .btn img {
            width: 20px;
            height: 20px;
            margin-right: 10px;
        }
        .notice {
            background-color: #f3e5f5;
            border-left: 4px solid #9c27b0;
            padding: 12px;
            margin: 15px 0;
            border-radius: 0 4px 4px 0;
            font-size: 15px;
            line-height: 1.5;
        }
        .warning-box {
            background-color: #ffebee;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }
        .warning-title {
            color: #d32f2f;
            font-weight: 600;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        .warning-title:before {
            content: "⚠️";
            margin-right: 8px;
        }
        .warning-item {
            margin-bottom: 10px;
            font-size: 14px;
            line-height: 1.5;
            position: relative;
            padding-left: 20px;
        }
        .warning-item:before {
            content: "•";
            color: #d32f2f;
            font-weight: bold;
            position: absolute;
            left: 0;
        }
        .footer {
            background: #f5f5f5;
            padding: 15px;
            text-align: center;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
<div class="card">
    <div class="header">
        <img src="' . htmlspecialchars($texts['logoUrl']) . '" alt="平台Logo" style="width: 180px; height: 60px;">
    </div>

    <div class="content">
        <div class="alert">
            ' . htmlspecialchars($texts['warmTip']) . ' 频道：' . htmlspecialchars($texts['platformChannel']) . '
        </div>

        <a href="' . htmlspecialchars($texts['shopLink']) . '" class="btn">
            <svg t="1744595050613" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1316" width="24" height="24"><path d="M670.6176 715.2128m-179.0976 0a179.0976 179.0976 0 1 0 358.1952 0 179.0976 179.0976 0 1 0-358.1952 0Z" fill="#FFBE0A" p-id="1317"></path><path d="M695.1424 311.7056a219.7504 219.7504 0 1 0-356.1472 172.0832 362.8032 362.8032 0 0 0-232.8064 338.2272 75.9808 75.9808 0 0 0 75.9296 75.9296h260.5568a35.84 35.84 0 0 0 0-71.68H182.1184a4.2496 4.2496 0 0 1-4.2496-4.2496 290.9184 290.9184 0 0 1 290.56-290.56h13.9776a29.8496 29.8496 0 0 0 4.3008-0.3072 220.16 220.16 0 0 0 208.4352-219.4432zM475.4432 459.776a148.0704 148.0704 0 1 1 148.0192-148.0704A148.224 148.224 0 0 1 475.4432 459.776zM714.2912 833.2288a35.84 35.84 0 0 1-24.5248-9.728L603.4944 742.4a35.84 35.84 0 1 1 49.1008-52.224l58.1632 54.6304L845.312 578.56a35.84 35.84 0 1 1 55.808 45.312l-158.72 196.096a35.84 35.84 0 0 1-25.6 13.1584 19.712 19.712 0 0 1-2.5088 0.1024z" fill="#34332E" p-id="1318"></path></svg>
            ' . htmlspecialchars($texts['shopButtonText']) . '
        </a>

        <a href="' . htmlspecialchars($texts['groupLink']) . '" class="btn">
            <svg t="1744595015666" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1163" width="24" height="24"><path d="M608.256 565.3504m-179.0976 0a179.0976 179.0976 0 1 0 358.1952 0 179.0976 179.0976 0 1 0-358.1952 0Z" fill="#FFBE0A" p-id="1164"></path><path d="M635.5968 921.6a132.5568 132.5568 0 0 1-117.4016-69.9392l-116.3264-210.4832-231.1168-103.1168a119.6544 119.6544 0 0 1 7.1168-221.44l552.96-205.3632a135.3216 135.3216 0 0 1 178.176 160.5632l-141.1584 547.84a132.8128 132.8128 0 0 1-113.9712 100.4544 144.6912 144.6912 0 0 1-18.2784 1.4848zM202.8544 384a47.9744 47.9744 0 0 0-2.8672 88.7808l242.0736 108.032a35.84 35.84 0 0 1 16.7424 15.36l122.112 220.8768a63.6928 63.6928 0 0 0 117.3504-14.9504l141.1072-547.84a63.6416 63.6416 0 0 0-83.8144-75.52L202.8032 384z" fill="#34332E" p-id="1165"></path><path d="M532.48 529.6128a35.84 35.84 0 0 1-25.6-60.9792l152.064-154.4704a35.84 35.84 0 1 1 51.2 50.2784L558.08 518.912a35.84 35.84 0 0 1-25.6 10.7008z" fill="#34332E" p-id="1166"></path></svg>
            ' . htmlspecialchars($texts['groupButtonText']) . '
        </a>

        <a href="' . htmlspecialchars($texts['serviceLink']) . '" class="btn">
            <svg t="1744594983943" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1010" width="24" height="24"><path d="M512 533.1968m-184.2176 0a184.2176 184.2176 0 1 0 368.4352 0 184.2176 184.2176 0 1 0-368.4352 0Z" fill="#FFBE0A" p-id="1011"></path><path d="M914.1248 754.0736A249.2928 249.2928 0 0 0 832.4608 445.44a34.048 34.048 0 0 0-2.8672-1.8432 366.6944 366.6944 0 0 0-377.088-333.8752 366.7456 366.7456 0 0 0-317.44 528.0256c-14.3872 53.4528-30.2592 106.5984-41.4208 142.9504a67.328 67.328 0 0 0 83.3024 84.224l156.5696-46.08a367.0016 367.0016 0 0 0 192.1536 18.8416A249.0368 249.0368 0 0 0 773.12 883.2l79.5648 23.3984a66.56 66.56 0 0 0 18.6368 2.7136 65.9968 65.9968 0 0 0 62.976-84.992c-7.5776-25.1392-14.336-48.7424-20.1728-70.2464z m-443.904 17.1008a290.6624 290.6624 0 0 1-120.832-23.1936 36.1472 36.1472 0 0 0-24.1152-1.3824L164.6592 793.6c12.1344-39.68 28.7232-95.8464 43.264-151.04a35.84 35.84 0 0 0-3.1232-26.2144 295.168 295.168 0 0 1 249.9584-435.2 295.0144 295.0144 0 1 1 15.36 589.824z m374.2208-38.0928a35.84 35.84 0 0 0-3.1232 26.2144c6.0416 22.9376 13.2096 48.2816 21.2992 75.5712l-81.2544-23.8592a35.84 35.84 0 0 0-24.1152 1.3824 176.9984 176.9984 0 0 1-142.4896-1.9456A369.3056 369.3056 0 0 0 721.92 736.8704a365.4144 365.4144 0 0 0 104.3456-199.68 176.9984 176.9984 0 0 1 18.432 196.1472z" fill="#34332E" p-id="1012"></path><path d="M331.1616 512.7168a35.84 35.84 0 0 1-35.84-35.84v-42.1376a35.84 35.84 0 1 1 71.68 0v42.1376a35.84 35.84 0 0 1-35.84 35.84zM569.7536 512.7168a35.84 35.84 0 0 1-35.84-35.84v-42.1376a35.84 35.84 0 0 1 71.68 0v42.1376a35.84 35.84 0 0 1-35.84 35.84z" fill="#34332E" p-id="1013"></path></svg>
            ' . htmlspecialchars($texts['serviceButtonText']) . '
        </a>

        <div class="notice" style="border-left-color: #f44336; background-color: #ffebee;">
            ' . htmlspecialchars($texts['customerServiceNotice']) . '
        </div>

        <div class="notice">
            ' . htmlspecialchars($texts['notice1']) . '
        </div>

        <div class="notice">
            ' . htmlspecialchars($texts['notice2']) . '
        </div>

        <div class="notice">
            ' . htmlspecialchars($texts['notice3']) . '
        </div>

        <div class="warning-box">
            <div class="warning-title">' . htmlspecialchars($texts['warningTitle']) . '</div>
            <div class="warning-item">' . htmlspecialchars($texts['warning1']) . '</div>
            <div class="warning-item">' . htmlspecialchars($texts['warning2']) . '</div>
            <div class="warning-item">' . htmlspecialchars($texts['warning3']) . '</div>
            <div class="warning-item">' . htmlspecialchars($texts['warning4']) . '</div>
            <div class="warning-item">' . htmlspecialchars($texts['warning5']) . '</div>
        </div>
    </div>

    <div class="footer">
        ' . htmlspecialchars($texts['footerText']) . '
    </div>
</div>
</body>
</html>';
    }
}