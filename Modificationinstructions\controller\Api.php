<?php

namespace plugin\Modificationinstructions\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Log;
use think\facade\Db;

class Api extends BasePlugin {

    protected $scene = ['admin'];  // 只允许管理员访问
    
    protected $noNeedLogin = [];

    // 管理面板首页
    public function index() {
        return View::fetch();
    }

    // 获取配置
    public function getConfig() {
        try {
            $configFile = app()->getRootPath() . 'plugin/Modificationinstructions/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            // 确保返回必要的配置项
            if (!isset($config['instructions_config'])) {
                $config['instructions_config'] = [
                    'status' => true,
                    'allow_html' => false, // 是否允许HTML
                    'need_approval' => false, // 是否需要审核
                    'max_length' => 2000 // 最大字符长度
                ];
            }

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $config['instructions_config']
            ]);
        } catch (\Exception $e) {
            Log::error('获取配置失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取配置失败：' . $e->getMessage()]);
        }
    }

    // 保存配置
    public function saveConfig() {
        try {
            $data = [
                'status' => input('status/b', true),
                'allow_html' => input('allow_html/b', false),
                'need_approval' => input('need_approval/b', false),
                'max_length' => input('max_length/d', 2000)
            ];
            
            // 读取配置文件
            $configFile = app()->getRootPath() . 'plugin/Modificationinstructions/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            // 更新配置
            $config['instructions_config'] = $data;
            
            // 保存配置
            if (!file_put_contents($configFile, "<?php\nreturn " . var_export($config, true) . ";\n")) {
                throw new \Exception('保存配置失败');
            }
            
            return json(['code' => 200, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            Log::error('保存配置失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    // 获取待审核的说明修改
    public function getPendingApprovals() {
        try {
            $page = input('page/d', 1);
            $limit = input('limit/d', 10);
            $offset = ($page - 1) * $limit;
            
            // 获取所有待审核的修改记录
            $pendingApprovals = Db::name('instructions_change_log')
                ->alias('l')
                ->join('goods g', 'l.goods_id = g.id')
                ->join('user u', 'l.user_id = u.id')
                ->where('l.status', 0) // 0表示待审核
                ->field([
                    'l.*',
                    'g.name as goods_name',
                    'u.username',
                    'u.nickname'
                ])
                ->order('l.create_time', 'desc')
                ->limit($offset, $limit)
                ->select()
                ->toArray();
            
            $total = Db::name('instructions_change_log')
                ->where('status', 0)
                ->count();
            
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $pendingApprovals,
                'total' => $total
            ]);
        } catch (\Exception $e) {
            Log::error('获取待审核数据失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取数据失败：' . $e->getMessage()]);
        }
    }
    
    // 审批使用说明修改
    public function approveChange() {
        try {
            $logId = input('log_id/d', 0);
            $action = input('action/s', 'approve'); // approve或reject
            $rejectReason = input('reject_reason/s', '');
            
            if ($logId <= 0) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }
            
            // 获取修改记录
            $changeLog = Db::name('instructions_change_log')->where('id', $logId)->find();
            if (!$changeLog) {
                return json(['code' => 404, 'msg' => '修改记录不存在']);
            }
            
            if ($changeLog['status'] != 0) {
                return json(['code' => 400, 'msg' => '该记录已处理']);
            }
            
            Db::startTrans();
            
            if ($action == 'approve') {
                // 更新goods_card表中的instructions
                Db::name('goods_card')
                    ->where('goods_id', $changeLog['goods_id'])
                    ->update(['instructions' => $changeLog['new_instructions']]);
                
                // 更新修改记录状态为已通过
                Db::name('instructions_change_log')
                    ->where('id', $logId)
                    ->update([
                        'status' => 1,
                        'approve_time' => time(),
                        'approve_user_id' => $this->user->id
                    ]);
            } else {
                // 更新修改记录状态为已拒绝
                Db::name('instructions_change_log')
                    ->where('id', $logId)
                    ->update([
                        'status' => 2,
                        'approve_time' => time(),
                        'approve_user_id' => $this->user->id,
                        'reject_reason' => $rejectReason
                    ]);
            }
            
            Db::commit();
            
            return json(['code' => 200, 'msg' => $action == 'approve' ? '审批通过成功' : '拒绝成功']);
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('审批失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '审批失败：' . $e->getMessage()]);
        }
    }
    
    // 获取商品使用说明 - 用于前端JS调用
    public function getGoodsInstructions() {
        try {
            $goodsId = input('goods_id/d', 0);
            
            if ($goodsId <= 0) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }
            
            // 获取当前商品信息
            $goods = Db::name('goods')
                ->where('id', $goodsId)
                ->where('delete_time', null)
                ->field(['id', 'name', 'parent_id', 'user_id'])
                ->find();
                
            if (!$goods) {
                return json(['code' => 404, 'msg' => '商品不存在']);
            }
            
            // 如果商品没有上级，返回空
            if ($goods['parent_id'] <= 0) {
                return json(['code' => 200, 'msg' => '获取成功', 'data' => [
                    'instructions' => ''
                ]]);
            }
            
            // 获取上级商品的使用说明
            $parentInstructions = Db::name('goods_card')
                ->where('goods_id', $goods['parent_id'])
                ->value('instructions');
                
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => [
                    'goods_id' => $goodsId,
                    'parent_id' => $goods['parent_id'],
                    'instructions' => $parentInstructions ?? ''
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('获取商品使用说明失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取数据失败：' . $e->getMessage()]);
        }
    }
} 