
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>卡密验证系统 - 生成卡密</title>
        <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        <style>
            body {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            }
            .sidebar {
                background: linear-gradient(180deg, #4a00e0 0%, #8e2de2 100%);
                min-height: 100vh;
                padding-top: 20px;
                box-shadow: 4px 0 10px rgba(0, 0, 0, 0.1);
                z-index: 1000;
                position: fixed;
                width: inherit;
                max-width: inherit;
            }
            .sidebar-header {
                padding: 0 15px 20px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                margin-bottom: 20px;
            }
            .sidebar a {
                color: rgba(255, 255, 255, 0.8);
                display: block;
                padding: 12px 20px;
                text-decoration: none;
                transition: all 0.3s;
                margin-bottom: 5px;
                border-left: 3px solid transparent;
            }
            .sidebar a:hover {
                background-color: rgba(255, 255, 255, 0.1);
                color: #fff;
                border-left: 3px solid #fff;
            }
            .sidebar a.active {
                background-color: rgba(255, 255, 255, 0.2);
                color: #fff;
                border-left: 3px solid #fff;
            }
            .sidebar i {
                margin-right: 10px;
                width: 20px;
                text-align: center;
            }
            .main-content {
                margin-left: 16.666667%;
                padding: 30px;
            }
            .page-header {
                margin-bottom: 30px;
                border-bottom: 1px solid #dee2e6;
                padding-bottom: 15px;
            }
            .dashboard-card {
                background: #fff;
                border-radius: 15px;
                overflow: hidden;
                box-shadow: 0 10px 20px rgba(0,0,0,0.05);
                margin-bottom: 30px;
                transition: all 0.3s;
            }
            .dashboard-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 15px 30px rgba(0,0,0,0.1);
            }
            .card-header {
                padding: 20px;
                border-bottom: 1px solid #f1f1f1;
                font-weight: 600;
                background-color: #fff;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }
            .card-header .title {
                font-size: 18px;
                color: #333;
            }
            .card-body {
                padding: 20px;
            }
            .btn-primary {
                background: linear-gradient(45deg, #8e2de2, #4a00e0);
                border: none;
                box-shadow: 0 5px 15px rgba(142, 45, 226, 0.3);
                transition: all 0.3s;
            }
            .btn-primary:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 20px rgba(142, 45, 226, 0.4);
            }
            .form-control, .form-select {
                padding: 10px 15px;
                border-radius: 8px;
                border: 1px solid #e0e0e0;
            }
            .form-control:focus, .form-select:focus {
                border-color: #8e2de2;
                box-shadow: 0 0 0 3px rgba(142, 45, 226, 0.1);
            }
            .alert {
                border-radius: 10px;
                border: none;
                box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            }
            .license-keys {
                background-color: #f8f9fa;
                border-radius: 10px;
                padding: 15px;
                max-height: 300px;
                overflow-y: auto;
                margin-top: 20px;
            }
            .license-key-item {
                font-family: monospace;
                background-color: #fff;
                padding: 12px 15px;
                margin-bottom: 10px;
                border-radius: 8px;
                border-left: 3px solid #8e2de2;
                display: flex;
                justify-content: space-between;
                align-items: center;
                transition: all 0.3s;
            }
            .license-key-item:hover {
                background-color: #f8f9fa;
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            }
            .copy-btn {
                background: transparent;
                border: none;
                color: #8e2de2;
                cursor: pointer;
                padding: 5px;
                font-size: 14px;
                transition: all 0.3s;
                opacity: 0.6;
            }
            .license-key-item:hover .copy-btn {
                opacity: 1;
            }
            .card-type-selector {
                margin-top: 10px;
                display: flex;
                flex-wrap: wrap;
                gap: 10px;
            }
            .card-type-option {
                cursor: pointer;
                padding: 8px 15px;
                border-radius: 30px;
                font-size: 0.85rem;
                transition: all 0.3s;
                background-color: #f1f1f1;
                user-select: none;
            }
            .card-type-option:hover {
                background-color: #e9ecef;
            }
            .card-type-option.selected {
                background: linear-gradient(45deg, #8e2de2, #4a00e0);
                color: white;
            }
            .valid-method-selector {
                margin-bottom: 15px;
            }
            .form-check-input:checked {
                background-color: #8e2de2;
                border-color: #8e2de2;
            }
            .batch-settings {
                background-color: #f8f9fa;
                border-radius: 10px;
                padding: 15px;
                margin-top: 15px;
                border: 1px dashed #dee2e6;
            }
            .input-group-text {
                background-color: #f8f9fa;
                border-color: #e0e0e0;
            }
        </style>
    </head>
    <body>
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-2">
                    <div class="sidebar">
                        <div class="sidebar-header">
                            <h4 class="text-center text-white fw-bold mb-0">卡密验证系统</h4>
                            <p class="text-center text-white-50 mb-0">管理面板</p>
                        </div>
                        <a href="/dashboard"><i class="fas fa-tachometer-alt"></i> 仪表盘</a>
                        <a href="/licenses"><i class="fas fa-key"></i> 卡密管理</a>
                        <a href="/create_license" class="active"><i class="fas fa-plus-circle"></i> 生成卡密</a>
                        <a href="/access_logs"><i class="fas fa-history"></i> 访问日志</a>
                        <a href="/logout"><i class="fas fa-sign-out-alt"></i> 退出登录</a>
                    </div>
                </div>
                <div class="col-md-10 main-content">
                    <div class="page-header">
                        <h2 class="mb-0">生成卡密</h2>
                    </div>
                    
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}
                    
                    <div class="row">
                        <div class="col-md-7">
                            <div class="dashboard-card">
                                <div class="card-header">
                                    <div class="title">
                                        <i class="fas fa-plus-circle me-2"></i>创建新卡密
                                    </div>
                                </div>
                                <div class="card-body">
                                    <form action="/create_license" method="post">
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="software_id" class="form-label">软件ID <span class="text-danger">*</span></label>
                                                <div class="input-group">
                                                    <select class="form-select" id="software_id" name="software_id" required>
                                                        {% for sw_id in software_ids %}
                                                        <option value="{{ sw_id }}">{{ sw_id }}</option>
                                                        {% endfor %}
                                                    </select>
                                                    <button class="btn btn-outline-secondary" type="button" data-bs-toggle="modal" data-bs-target="#newSoftwareModal">
                                                        <i class="fas fa-plus"></i>
                                                    </button>
                                                </div>
                                                <div class="form-text">
                                                    选择现有软件ID或添加新的
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="user_id" class="form-label">用户ID <span class="text-muted">(可选)</span></label>
                                                <input type="text" class="form-control" id="user_id" name="user_id" placeholder="填写用户ID或留空">
                                                <div class="form-text">
                                                    为特定用户生成卡密时填写
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="prefix" class="form-label">卡密前缀 <span class="text-muted">(可选)</span></label>
                                            <input type="text" class="form-control" id="prefix" name="prefix" placeholder="例如: VIP- 或 ABC_">
                                            <div class="form-text">
                                                添加前缀使卡密更易辨识，如 VIP-123456...
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">有效期设置</label>
                                            <div class="valid-method-selector">
                                                <div class="form-check form-check-inline">
                                                    <input class="form-check-input" type="radio" name="valid_type" id="valid_type_days" value="days" checked>
                                                    <label class="form-check-label" for="valid_type_days">按天数</label>
                                                </div>
                                                <div class="form-check form-check-inline">
                                                    <input class="form-check-input" type="radio" name="valid_type" id="valid_type_date" value="date">
                                                    <label class="form-check-label" for="valid_type_date">按日期</label>
                                                </div>
                                            </div>
                                            
                                            <div id="valid_days_container">
                                                <div class="card-type-selector mb-2">
                                                    <div class="card-type-option selected" data-days="1" onclick="selectCardType(this, 1)">日卡</div>
                                                    <div class="card-type-option" data-days="7" onclick="selectCardType(this, 7)">周卡</div>
                                                    <div class="card-type-option" data-days="30" onclick="selectCardType(this, 30)">月卡</div>
                                                    <div class="card-type-option" data-days="90" onclick="selectCardType(this, 90)">季卡</div>
                                                    <div class="card-type-option" data-days="180" onclick="selectCardType(this, 180)">半年卡</div>
                                                    <div class="card-type-option" data-days="365" onclick="selectCardType(this, 365)">年卡</div>
                                                    <div class="card-type-option" data-days="0" onclick="selectCardType(this, 0)">永久卡</div>
                                                </div>
                                                <div class="input-group">
                                                    <input type="number" class="form-control" id="valid_days" name="valid_days" value="1" min="0">
                                                    <span class="input-group-text">天</span>
                                                </div>
                                                <div class="form-text">
                                                    设置为0表示永久有效
                                                </div>
                                            </div>
                                            
                                            <div id="valid_date_container" style="display: none;">
                                                <input type="date" class="form-control" id="valid_until" name="valid_until" 
                                                    value="{{ default_expiry_date }}">
                                                <div class="form-text">
                                                    留空表示永久有效
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">批量生成设置</label>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="enable_batch" onchange="toggleBatchSettings()">
                                                <label class="form-check-label" for="enable_batch">
                                                    启用批量生成
                                                </label>
                                            </div>
                                            
                                            <div id="batch_settings" class="batch-settings" style="display: none;">
                                                <div class="mb-3">
                                                    <label for="count" class="form-label">生成数量</label>
                                                    <input type="number" class="form-control" id="count" name="count" value="10" min="1" max="500">
                                                    <div class="form-text">
                                                        一次最多可生成500个卡密
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="d-grid gap-2">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-key me-2"></i>生成卡密
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-5">
                            <div class="dashboard-card">
                                <div class="card-header">
                                    <div class="title">
                                        <i class="fas fa-info-circle me-2"></i>卡密使用说明
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="mb-4">
                                        <h5>什么是卡密？</h5>
                                        <p>卡密是用于激活和验证软件的唯一密钥，用户可以通过卡密来授权使用软件的功能。</p>
                                    </div>
                                    
                                    <div class="mb-4">
                                        <h5>卡密类型</h5>
                                        <ul>
                                            <li><strong>时效卡</strong> - 有固定使用期限的卡密，到期后需要重新激活</li>
                                            <li><strong>永久卡</strong> - 永久有效的卡密，一次激活即可永久使用</li>
                                        </ul>
                                    </div>
                                    
                                    <div class="mb-0">
                                        <h5>卡密状态说明</h5>
                                        <ul>
                                            <li><span class="badge bg-secondary">未激活</span> - 卡密尚未被任何设备使用</li>
                                            <li><span class="badge bg-success">已激活</span> - 卡密已被设备绑定使用</li>
                                            <li><span class="badge bg-danger">已过期</span> - 卡密使用期限已过期</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            {% if keys %}
                            <div class="dashboard-card mt-4">
                                <div class="card-header">
                                    <div class="title">
                                        <i class="fas fa-list-alt me-2"></i>最近生成的卡密
                                    </div>
                                    {% if keys|length > 1 %}
                                    <button class="btn btn-sm btn-primary" onclick="copyAllKeys()">
                                        复制全部
                                    </button>
                                    {% endif %}
                                </div>
                                <div class="card-body p-0">
                                    <div class="license-keys">
                                        {% for key in keys %}
                                        <div class="license-key-item">
                                            <span class="key-text">{{ key }}</span>
                                            <button class="copy-btn" onclick="copyKey('{{ key }}')">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 新增软件ID的模态框 -->
        <div class="modal fade" id="newSoftwareModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">添加新软件ID</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="new_software_id" class="form-label">新软件ID</label>
                            <input type="text" class="form-control" id="new_software_id">
                            <div class="form-text">
                                添加后会立即更新到软件ID下拉列表中
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="addSoftwareId()">添加</button>
                    </div>
                </div>
            </div>
        </div>
        
        <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
        <script>
            // 有效期类型切换
            document.querySelectorAll('input[name="valid_type"]').forEach(radio => {
                radio.addEventListener('change', function() {
                    if (this.value === 'days') {
                        document.getElementById('valid_days_container').style.display = 'block';
                        document.getElementById('valid_date_container').style.display = 'none';
                    } else {
                        document.getElementById('valid_days_container').style.display = 'none';
                        document.getElementById('valid_date_container').style.display = 'block';
                    }
                });
            });
            
            // 选择卡密类型
            function selectCardType(element, days) {
                // 移除所有选项的选中状态
                document.querySelectorAll('.card-type-option').forEach(option => {
                    option.classList.remove('selected');
                });
                
                // 添加当前选项的选中状态
                element.classList.add('selected');
                
                // 更新天数输入框
                document.getElementById('valid_days').value = days;
            }
            
            // 批量生成设置切换
            function toggleBatchSettings() {
                const batchSettings = document.getElementById('batch_settings');
                const enableBatch = document.getElementById('enable_batch');
                
                if (enableBatch.checked) {
                    batchSettings.style.display = 'block';
                    document.getElementById('count').value = 10;
                } else {
                    batchSettings.style.display = 'none';
                    document.getElementById('count').value = 1;
                }
            }
            
            // 添加新的软件ID
            function addSoftwareId() {
                const newSoftwareId = document.getElementById('new_software_id').value.trim();
                if (!newSoftwareId) return;
                
                const select = document.getElementById('software_id');
                
                // 检查是否已存在
                let exists = false;
                for (let i = 0; i < select.options.length; i++) {
                    if (select.options[i].value === newSoftwareId) {
                        exists = true;
                        break;
                    }
                }
                
                if (!exists) {
                    const option = document.createElement('option');
                    option.value = newSoftwareId;
                    option.text = newSoftwareId;
                    select.add(option);
                    
                    // 选中新添加的选项
                    select.value = newSoftwareId;
                }
                
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('newSoftwareModal'));
                modal.hide();
                
                // 清空输入框
                document.getElementById('new_software_id').value = '';
            }
            
            // 复制单个卡密
            function copyKey(key) {
                navigator.clipboard.writeText(key).then(() => {
                    alert('卡密已复制到剪贴板');
                }).catch(err => {
                    console.error('复制失败:', err);
                });
            }
            
            // 复制所有卡密
            function copyAllKeys() {
                const keys = Array.from(document.querySelectorAll('.key-text')).map(el => el.textContent.trim()).join('\n');
                navigator.clipboard.writeText(keys).then(() => {
                    alert('所有卡密已复制到剪贴板');
                }).catch(err => {
                    console.error('复制失败:', err);
                });
            }
        </script>
    </body>
    </html>
    