<?php

namespace plugin\Tderciyuan\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;

/**
 * 二次元Live2D管理后台控制器
 */
class Admin extends BasePlugin
{
    // 指定只有管理员可以访问
    protected $scene = ['admin'];

    // 指定不需要登录验证的方法
    protected $noNeedLogin = [];

    /**
     * 管理面板首页
     */
    public function index()
    {
        return View::fetch();
    }

    // 获取默认配置
    public function getConfig()
    {
        try {
            $data = [
                'status' => intval(plugconf("Tderciyuan.status") ?? 1),
                'model_type' => (string)(plugconf("Tderciyuan.model_type") ?? 'auto'),
                'custom_model_path' => (string)(plugconf("Tderciyuan.custom_model_path") ?? ''),
                'position' => (string)(plugconf("Tderciyuan.position") ?? 'right'),
                'width' => intval(plugconf("Tderciyuan.width") ?? 200),
                'height' => intval(plugconf("Tderciyuan.height") ?? 300),
                'display_mode' => (string)(plugconf("Tderciyuan.display_mode") ?? 'normal'),
                'merchant_can_edit' => intval(plugconf("Tderciyuan.merchant_can_edit") ?? 1)
            ];

            return json(['code' => 200, 'msg' => '获取成功', 'data' => $data]);
        } catch (\Exception $e) {
            \think\facade\Log::error('GetConfig error: ' . $e->getMessage());
            return json(['code' => 0, 'msg' => '获取数据失败：' . $e->getMessage()]);
        }
    }

    // 保存默认配置
    public function saveConfig()
    {
        try {
            // 获取表单数据并确保类型正确
            $status = intval($this->request->post('status') ?? 1);
            $model_type = trim((string)$this->request->post('model_type', 'auto'));
            $custom_model_path = trim((string)$this->request->post('custom_model_path', ''));
            $position = trim((string)$this->request->post('position', 'right'));
            $display_mode = trim((string)$this->request->post('display_mode', 'normal'));
            
            // 确保merchant_can_edit值为0或1
            $merchant_can_edit = $this->request->post('merchant_can_edit');
            $merchant_can_edit = $merchant_can_edit === null ? 1 : intval($merchant_can_edit);
            $merchant_can_edit = $merchant_can_edit == 1 ? 1 : 0; // 强制转为0或1
            
            // 获取并验证尺寸数据
            $width = intval($this->request->post('width') ?? 200);
            $height = intval($this->request->post('height') ?? 300);
            
            // 验证必填项
            if (empty($model_type)) {
                return json(['code' => 0, 'msg' => '请选择模型类型']);
            }

            // 如果是自定义模型，验证模型路径
            if ($model_type === 'custom' && empty($custom_model_path)) {
                return json(['code' => 0, 'msg' => '请输入自定义模型路径']);
            }

            // 保存所有配置
            plugconf("Tderciyuan.status", $status);
            plugconf("Tderciyuan.model_type", $model_type);
            plugconf("Tderciyuan.custom_model_path", $custom_model_path);
            plugconf("Tderciyuan.position", $position);
            plugconf("Tderciyuan.width", $width);
            plugconf("Tderciyuan.height", $height);
            plugconf("Tderciyuan.display_mode", $display_mode);
            plugconf("Tderciyuan.merchant_can_edit", $merchant_can_edit);

            // 返回完整的配置数据
            return json([
                'code' => 200, 
                'msg' => '保存成功',
                'data' => [
                    'status' => $status,
                    'model_type' => $model_type,
                    'custom_model_path' => $custom_model_path,
                    'position' => $position,
                    'width' => $width,
                    'height' => $height,
                    'display_mode' => $display_mode,
                    'merchant_can_edit' => $merchant_can_edit
                ]
            ]);
        } catch (\Exception $e) {
            \think\facade\Log::error('SaveConfig error: ' . $e->getMessage());
            return json(['code' => 0, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }
    
    // 重置配置
    public function resetConfig()
    {
        try {
            // 重置为默认值
            plugconf("Tderciyuan.status", 1);
            plugconf("Tderciyuan.model_type", 'auto');
            plugconf("Tderciyuan.custom_model_path", '');
            plugconf("Tderciyuan.position", 'right');
            plugconf("Tderciyuan.width", 200);
            plugconf("Tderciyuan.height", 300);
            plugconf("Tderciyuan.display_mode", 'normal');
            plugconf("Tderciyuan.merchant_can_edit", 1);

            return json(['code' => 200, 'msg' => '重置成功']);
        } catch (\Exception $e) {
            \think\facade\Log::error('ResetConfig error: ' . $e->getMessage());
            return json(['code' => 0, 'msg' => '重置失败：' . $e->getMessage()]);
        }
    }
} 