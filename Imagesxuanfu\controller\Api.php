<?php

namespace plugin\Imagesxuanfu\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;

class Api extends BasePlugin {

    protected $noNeedLogin = ['fetchData'];
    
    // 添加场景配置
    protected $scene = [
        'user'
    ];

    public function index() {
        // 传递当前登录用户信息到模板
        View::assign([
            'merchant_id' => $this->user->id ?? 0,
            'shop_name' => $this->user->nickname ?? ''
        ]);
        return View::fetch();
    }

    public function fetchData() {
        try {
            // 获取商家ID
            $merchant_id = $this->request->param('merchant_id', 0, 'intval');
            $shop_name = $this->request->param('shop_name', '', 'trim');
            
            // 如果没有merchant_id但有shop_name，通过shop_name查找merchant_id
            if (!$merchant_id && $shop_name) {
                $user = \think\facade\Db::name('user')
                    ->where('nickname', $shop_name)
                    ->field('id')
                    ->find();
                
                if ($user) {
                    $merchant_id = $user['id'];
                }
            }

            if (!$merchant_id) {
                return json([
                    'code' => 0,
                    'msg' => '未找到商家信息'
                ]);
            }

            // 获取商家配置
            $merchant_status = merchant_plugconf($merchant_id, "Imagesxuanfu.status");
            
            // 获取商家权限设置 - 获取原始值然后明确转为整数
            $merchant_can_edit_raw = plugconf("Imagesxuanfu.merchant_can_edit");
            $merchant_can_edit = $merchant_can_edit_raw === null ? 1 : intval($merchant_can_edit_raw);
            
            // 记录日志
            \think\facade\Log::info("商家权限原始值: " . var_export($merchant_can_edit_raw, true));
            \think\facade\Log::info("商家权限处理后值: " . $merchant_can_edit);
            
            // 如果商家未开启或未设置，则获取后台默认配置
            if ($merchant_status === null || intval($merchant_status) !== 1) {
                $data = [
                    'status' => intval(plugconf("Imagesxuanfu.default_status") ?? 0),
                    'image_url' => (string)(plugconf("Imagesxuanfu.default_image_url") ?? ''),
                    'popup_image_url' => (string)(plugconf("Imagesxuanfu.default_popup_image_url") ?? ''),
                    'link_url' => (string)(plugconf("Imagesxuanfu.default_link_url") ?? ''),
                    'width' => intval(plugconf("Imagesxuanfu.default_width") ?? 200),
                    'height' => intval(plugconf("Imagesxuanfu.default_height") ?? 200),
                    'bottom' => intval(plugconf("Imagesxuanfu.default_bottom") ?? 20),
                    'right' => intval(plugconf("Imagesxuanfu.default_right") ?? 20),
                    'min_size' => intval(plugconf("Imagesxuanfu.default_min_size") ?? 200),
                    'max_size' => intval(plugconf("Imagesxuanfu.default_max_size") ?? 500),
                    'popup_title' => (string)(plugconf("Imagesxuanfu.default_popup_title") ?? '扫描二维码'),
                    'popup_footer' => (string)(plugconf("Imagesxuanfu.default_popup_footer") ?? '请使用扫码软件扫描'),
                    'merchant_can_edit' => $merchant_can_edit
                ];
            } else {
                // 使用商家自己的配置
                $data = [
                    'status' => intval($merchant_status),
                    'image_url' => (string)(merchant_plugconf($merchant_id, "Imagesxuanfu.image_url") ?? ''),
                    'popup_image_url' => (string)(merchant_plugconf($merchant_id, "Imagesxuanfu.popup_image_url") ?? ''),
                    'link_url' => (string)(merchant_plugconf($merchant_id, "Imagesxuanfu.link_url") ?? ''),
                    'width' => intval(merchant_plugconf($merchant_id, "Imagesxuanfu.width") ?? 200),
                    'height' => intval(merchant_plugconf($merchant_id, "Imagesxuanfu.height") ?? 200),
                    'bottom' => intval(merchant_plugconf($merchant_id, "Imagesxuanfu.bottom") ?? 20),
                    'right' => intval(merchant_plugconf($merchant_id, "Imagesxuanfu.right") ?? 20),
                    'min_size' => intval(merchant_plugconf($merchant_id, "Imagesxuanfu.min_size") ?? 200),
                    'max_size' => intval(merchant_plugconf($merchant_id, "Imagesxuanfu.max_size") ?? 500),
                    'popup_title' => (string)(merchant_plugconf($merchant_id, "Imagesxuanfu.popup_title") ?? '扫描二维码'),
                    'popup_footer' => (string)(merchant_plugconf($merchant_id, "Imagesxuanfu.popup_footer") ?? '请使用扫码软件扫描'),
                    'merchant_can_edit' => $merchant_can_edit
                ];
            }

            // 如果没有设置弹窗图片，默认使用悬浮框图片
            if (empty($data['popup_image_url'])) {
                $data['popup_image_url'] = $data['image_url'];
            }

            return json([
                'code' => 200,
                'msg' => 'success',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            \think\facade\Log::error('FetchData error: ' . $e->getMessage());
            return json(['code' => 0, 'msg' => '获取数据失败：' . $e->getMessage()]);
        }
    }

    public function save() {
        try {
            // 判断是否开启了商家修改权限
            $merchantCanEdit = plugconf("Imagesxuanfu.merchant_can_edit");
            $merchantCanEdit = $merchantCanEdit === null ? 1 : intval($merchantCanEdit);
            
            if ($merchantCanEdit !== 1) {
                return json(['code' => 403, 'msg' => '管理员已禁止商家修改飘窗设置']);
            }
            
            if (!$this->user || !$this->user->id) {
                return json(['code' => 403, 'msg' => '请先登录']);
            }

            $merchant_id = $this->user->id;
            
            // 获取并验证基础参数
            $params = $this->validateParams();
            if (!$params['success']) {
                return json(['code' => 0, 'msg' => $params['message']]);
            }

            // 保存配置
            $this->saveConfigurations($merchant_id, $params['data']);

            return json(['code' => 200, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            \think\facade\Log::error('Save error: ' . $e->getMessage());
            return json(['code' => 0, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    /**
     * 验证参数
     * @return array
     */
    private function validateParams() {
        // 判断是否开启了商家修改权限
        $merchantCanEdit = plugconf("Imagesxuanfu.merchant_can_edit");
        $merchantCanEdit = $merchantCanEdit === null ? 1 : intval($merchantCanEdit);
        
        if ($merchantCanEdit !== 1) {
            return ['success' => false, 'message' => '管理员已禁止商家修改飘窗设置'];
        }

        $params = [
            'status' => $this->request->post('status/d', 0),
            'image_url' => $this->request->post('image_url', '', 'trim'),
            'popup_image_url' => $this->request->post('popup_image_url', '', 'trim'),
            'link_url' => $this->request->post('link_url', '', 'trim'),
            'width' => $this->request->post('width/d', 300),
            'height' => $this->request->post('height/d', 300),
            'bottom' => $this->request->post('bottom/d', 20),
            'right' => $this->request->post('right/d', 20),
            'min_size' => $this->request->post('min_size/d', 200),
            'max_size' => $this->request->post('max_size/d', 500),
            'popup_title' => $this->request->post('popup_title', '扫描二维码', 'trim'),
            'popup_footer' => $this->request->post('popup_footer', '请使用扫码软件扫描', 'trim')
        ];

        // 验证必填项
        if (empty($params['image_url']) && $params['status'] == 1) {
            return ['success' => false, 'message' => '请先上传悬浮框图片'];
        }

        // 如果弹窗图片为空，使用悬浮框图片
        if (empty($params['popup_image_url'])) {
            $params['popup_image_url'] = $params['image_url'];
        }

        // 验证数值范围
        if (!$this->validateSizeRanges($params)) {
            return ['success' => false, 'message' => '参数范围验证失败'];
        }

        return ['success' => true, 'data' => $params];
    }

    /**
     * 验证尺寸范围
     * @param array $params
     * @return bool
     */
    private function validateSizeRanges($params) {
        $validations = [
            ['value' => $params['width'], 'min' => 50, 'max' => 1200, 'name' => '图片宽度'],
            ['value' => $params['height'], 'min' => 50, 'max' => 1200, 'name' => '图片高度'],
            ['value' => $params['bottom'], 'min' => 0, 'max' => 1000, 'name' => '底部距离'],
            ['value' => $params['right'], 'min' => 0, 'max' => 1000, 'name' => '右侧距离'],
            ['value' => $params['min_size'], 'min' => 50, 'max' => 300, 'name' => '最小尺寸'],
            ['value' => $params['max_size'], 'min' => 300, 'max' => 800, 'name' => '最大尺寸']
        ];

        foreach ($validations as $validation) {
            if ($validation['value'] < $validation['min'] || $validation['value'] > $validation['max']) {
                return false;
            }
        }

        return $params['max_size'] > $params['min_size'];
    }

    /**
     * 保存配置
     * @param int $merchant_id
     * @param array $data
     */
    private function saveConfigurations($merchant_id, $data) {
        merchant_plugconf($merchant_id, "Imagesxuanfu.status", $data['status']);
        merchant_plugconf($merchant_id, "Imagesxuanfu.image_url", $data['image_url']);
        merchant_plugconf($merchant_id, "Imagesxuanfu.popup_image_url", $data['popup_image_url']);
        merchant_plugconf($merchant_id, "Imagesxuanfu.link_url", $data['link_url']);
        merchant_plugconf($merchant_id, "Imagesxuanfu.width", $data['width']);
        merchant_plugconf($merchant_id, "Imagesxuanfu.height", $data['height']);
        merchant_plugconf($merchant_id, "Imagesxuanfu.bottom", $data['bottom']);
        merchant_plugconf($merchant_id, "Imagesxuanfu.right", $data['right']);
        merchant_plugconf($merchant_id, "Imagesxuanfu.min_size", $data['min_size']);
        merchant_plugconf($merchant_id, "Imagesxuanfu.max_size", $data['max_size']);
        merchant_plugconf($merchant_id, "Imagesxuanfu.popup_title", $data['popup_title']);
        merchant_plugconf($merchant_id, "Imagesxuanfu.popup_footer", $data['popup_footer']);
    }

    /**
     * 删除图片文件
     * @param string $imageUrl 图片URL
     * @return bool
     */
    private function deleteImageFile($imageUrl) {
        try {
            if (empty($imageUrl)) {
                return false;
            }

            // 将URL转换为服务器路径
            $filePath = public_path() . ltrim($imageUrl, '/');
            
            // 检查文件是否存在且在允许的目录内
            if (strpos($filePath, public_path() . 'upload/imagesxuanfu/') !== 0) {
                \think\facade\Log::error('Attempted to delete file outside of allowed directory: ' . $filePath);
                return false;
            }

            if (file_exists($filePath)) {
                if (@unlink($filePath)) {
                    \think\facade\Log::info('Image file deleted successfully: ' . $filePath);
                    
                    // 尝试删除空文件夹
                    $dir = dirname($filePath);
                    if (is_dir($dir) && count(scandir($dir)) <= 2) { // 只有 . 和 .. 时删除目录
                        @rmdir($dir);
                        \think\facade\Log::info('Empty directory removed: ' . $dir);
                    }
                    
                    return true;
                } else {
                    \think\facade\Log::error('Failed to delete image file: ' . $filePath);
                    return false;
                }
            }
            
            return false;
        } catch (\Exception $e) {
            \think\facade\Log::error('Delete image file error: ' . $e->getMessage());
            return false;
        }
    }

    public function upload() {
        try {
            // 检查用户登录状态
            if (!$this->user || !$this->user->id) {
                return json(['code' => 403, 'msg' => '请先登录']);
            }

            $file = $this->request->file('file');
            if (!$file) {
                return json(['code' => 0, 'msg' => '请选择要上传的图片']);
            }

            // 验证文件
            validate([
                'file' => [
                    'fileSize' => 2097152,
                    'fileExt' => 'jpg,jpeg,png,gif'
                ]
            ])->check(['file' => $file]);

            // 直接转发到商户端统一上传接口
            $response = $this->forwardToUploadApi($file);
            return json($response);

        } catch (\think\exception\ValidateException $e) {
            return json(['code' => 0, 'msg' => $e->getMessage()]);
        } catch (\Exception $e) {
            \think\facade\Log::error('Upload error: ' . $e->getMessage());
            return json(['code' => 0, 'msg' => '上传失败：' . $e->getMessage()]);
        }
    }

} 