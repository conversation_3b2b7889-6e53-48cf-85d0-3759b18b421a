<?php
namespace plugin\Complaintrate\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;

class Api extends BasePlugin
{
    protected $scene = ['admin'];
    protected $noNeedLogin = ['merchant', 'getMerchantData', 'channelGroups', 'upgradeLevel'];

    public function index()
    {
        return View::fetch();
    }

    // 获取设置
    public function getSettings()
    {
        try {
            $settings = [
                'update_interval' => intval(plugconf('Complaintrate.update_interval') ?? 5),
                'auto_update_status' => intval(plugconf('Complaintrate.auto_update_status') ?? 0),
                'update_cycle' => plugconf('Complaintrate.update_cycle') ?? 'monthly',
                'calc_mode' => plugconf('Complaintrate.calc_mode') ?? 'realtime'
            ];
            
            return json(['code' => 200, 'msg' => '获取成功', 'data' => $settings]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    // 保存设置
    public function saveSettings()
    {
        try {
            $data = $this->request->post();
            
            // 验证最小值
            if ($data['update_interval'] < 5) {
                return json(['code' => 400, 'msg' => '更新间隔不能小于5秒']);
            }
            
            plugconf('Complaintrate.update_interval', intval($data['update_interval']));
            plugconf('Complaintrate.auto_update_status', intval($data['auto_update_status']));
            plugconf('Complaintrate.update_cycle', $data['update_cycle']);
            plugconf('Complaintrate.calc_mode', $data['calc_mode']);

            return json(['code' => 200, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    // 管理端获取渠道组列表
    public function adminChannelGroups()
    {
        try {
            // 获取渠道组基本信息
            $groups = Db::name('channel_group')
                ->alias('g')
                ->whereNull('g.delete_time')
                ->field([
                    'g.id',
                    'g.name'
                ])
                ->select()
                ->toArray();

            // 获取投诉率和投诉订单数配置
            $complaintRates = plugconf('Complaintrate.complaint_rates') ? json_decode(plugconf('Complaintrate.complaint_rates'), true) : [];
            $complaintCounts = plugconf('Complaintrate.complaint_counts') ? json_decode(plugconf('Complaintrate.complaint_counts'), true) : [];
            
            // 获取价格配置
            $prices = plugconf('Complaintrate.prices') ? json_decode(plugconf('Complaintrate.prices'), true) : [];
            $quarterlyPrices = plugconf('Complaintrate.quarterly_prices') ? json_decode(plugconf('Complaintrate.quarterly_prices'), true) : [];
            $yearlyPrices = plugconf('Complaintrate.yearly_prices') ? json_decode(plugconf('Complaintrate.yearly_prices'), true) : [];

            foreach ($groups as &$group) {
                $group['complaint_rate'] = $complaintRates[$group['id']] ?? 0;
                $group['complaint_count'] = $complaintCounts[$group['id']] ?? 0;
                $group['price'] = $prices[$group['id']] ?? 299.00;  // 默认299元
                $group['quarterly_price'] = $quarterlyPrices[$group['id']] ?? 0;
                $group['yearly_price'] = $yearlyPrices[$group['id']] ?? 0;
                $group['can_upgrade'] = intval(plugconf("Complaintrate.can_upgrade.{$group['id']}") ?? 1);
                $group['disabled_message'] = plugconf("Complaintrate.disabled_message.{$group['id']}") ?? '该等级暂不开放';
            }

            return json(['code' => 200, 'msg' => '获取成功', 'data' => $groups]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '获取失败: ' . $e->getMessage()]);
        }
    }

    // 获取费率组列表
    public function getRateGroups()
    {
        try {
            $groups = Db::name('channel_group')
                ->whereNull('delete_time')
                ->field([
                    'id',
                    'name'
                ])
                ->select()
                ->toArray();

            return json(['code' => 200, 'msg' => '获取成功', 'data' => $groups]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '获取失败: ' . $e->getMessage()]);
        }
    }

    // 修改保存渠道组方法
    public function saveChannelGroup()
    {
        try {
            $data = input('post.');
            
            Db::startTrans();
            try {
                $groupId = $data['id'];
                if (empty($groupId)) {
                    return json(['code' => 400, 'msg' => '参数错误']);
                }

                // 保存配置
                $complaintRates = plugconf('Complaintrate.complaint_rates') ? json_decode(plugconf('Complaintrate.complaint_rates'), true) : [];
                $complaintCounts = plugconf('Complaintrate.complaint_counts') ? json_decode(plugconf('Complaintrate.complaint_counts'), true) : [];
                
                $complaintRates[$groupId] = floatval($data['complaint_rate']);
                $complaintCounts[$groupId] = intval($data['complaint_count']);

                plugconf('Complaintrate.complaint_rates', json_encode($complaintRates));
                plugconf('Complaintrate.complaint_counts', json_encode($complaintCounts));
                plugconf("Complaintrate.can_upgrade.{$groupId}", $data['can_upgrade'] ? '1' : '0');
                plugconf("Complaintrate.disabled_message.{$groupId}", $data['disabled_message'] ?? '该等级暂不开放');

                Db::commit();
                return json(['code' => 200, 'msg' => '保存成功']);
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    // 获取投诉率列表
    public function getComplaintsList()
    {
        try {
            $page = input('page/d', 1);
            $limit = input('limit/d', 10);
            $sortType = input('sort_type', 'latest_time');
            $offset = ($page - 1) * $limit;

            // 构建子查询来获取投诉数和订单数
            $subQuery = Db::name('user')
                ->alias('u')
                ->leftJoin('complaint c', 'u.id = c.user_id')
                ->leftJoin('order o', 'u.id = o.user_id AND o.status = 1')
                ->field([
                    'u.id',
                    'u.username',
                    'u.nickname',
                    'u.avatar',
                    'COUNT(DISTINCT c.id) as complaint_count',
                    'COUNT(DISTINCT o.id) as total_orders',
                    'MAX(c.create_time) as latest_complaint_time'
                ])
                ->group('u.id')
                ->having('complaint_count > 0')
                ->buildSql();

            // 主查询
            $query = Db::table($subQuery . ' a')
                ->field([
                    'id',
                    'username',
                    'nickname',
                    'avatar',
                    'complaint_count',
                    'total_orders',
                    'latest_complaint_time',
                    'ROUND(complaint_count / total_orders * 100, 2) as complaint_rate'
                ]);

            // 排序
            switch ($sortType) {
                case 'complaint_rate':
                    $query->order('complaint_rate DESC');
                    break;
                case 'complaint_count':
                    $query->order('complaint_count DESC');
                    break;
                case 'latest_time':
                default:
                    $query->order('latest_complaint_time DESC');
            }

            $total = $query->count();
            $list = $query->limit($offset, $limit)->select()->toArray();

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => [
                    'total' => $total,
                    'list' => $list
                ]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取数据失败：' . $e->getMessage()]);
        }
    }

    // 手动计算
    public function manualCalc()
    {
        try {
            $hook = new \plugin\Complaintrate\Hook();
            $hook->handle();
            return json(['code' => 200, 'msg' => '计算完成']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '计算失败：' . $e->getMessage()]);
        }
    }

    // 保存价格
    public function savePrice()
    {
        try {
            $data = input('post.');
            
            if (empty($data['id']) || empty($data['type']) || !isset($data['value'])) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }

            $groupId = $data['id'];
            $type = $data['type'];
            $value = floatval($data['value']);

            // 验证价格
            if ($value < 0) {
                return json(['code' => 400, 'msg' => '价格不能小于0']);
            }

            // 获取现有配置
            $configKey = '';
            switch ($type) {
                case 'price':
                    $configKey = 'Complaintrate.prices';
                    break;
                case 'quarterly_price':
                    $configKey = 'Complaintrate.quarterly_prices';
                    break;
                case 'yearly_price':
                    $configKey = 'Complaintrate.yearly_prices';
                    break;
                default:
                    return json(['code' => 400, 'msg' => '无效的价格类型']);
            }

            $prices = plugconf($configKey) ? json_decode(plugconf($configKey), true) : [];
            $prices[$groupId] = $value;
            
            // 保存配置
            plugconf($configKey, json_encode($prices));

            return json(['code' => 200, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    // 添加其他必要的方法...
} 