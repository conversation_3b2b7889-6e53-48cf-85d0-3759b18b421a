<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>商家消息接收系统</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>
    <style>
        [v-cloak] { display: none; }
        body { 
            font-family: "Microsoft YaHei", sans-serif; 
            margin: 0; 
            padding: 0; 
            background-color: #f5f5f5;
        }
        /* 统一美化按钮样式 */
        .btn {
            padding: 9px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            white-space: nowrap;
            position: relative;
            overflow: hidden;
        }

        .btn::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 5px;
            height: 5px;
            background: rgba(255, 255, 255, 0.5);
            opacity: 0;
            border-radius: 100%;
            transform: scale(1, 1) translate(-50%);
            transform-origin: 50% 50%;
        }

        .btn:active::after {
            animation: ripple 0.6s ease-out;
        }

        @keyframes ripple {
            0% {
                transform: scale(0, 0);
                opacity: 0.5;
            }
            20% {
                transform: scale(25, 25);
                opacity: 0.3;
            }
            100% {
                opacity: 0;
                transform: scale(40, 40);
            }
        }

        .btn i {
            margin-right: 5px;
            font-size: 16px;
        }

        .btn-primary {
            background-color: #409eff;
            color: white;
            box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
            border-bottom: 2px solid rgba(0,0,0,0.1);
        }

        .btn-primary:hover {
            background-color: #66b1ff;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(64, 158, 255, 0.3);
        }

        .btn-secondary {
            background-color: #f2f3f5;
            color: #606266;
        }

        .btn-secondary:hover {
            background-color: #e6e8eb;
        }

        .btn-success {
            background-color: #67c23a;
            color: white;
            box-shadow: 0 2px 6px rgba(103, 194, 58, 0.2);
        }

        .btn-success:hover {
            background-color: #85ce61;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(103, 194, 58, 0.3);
        }

        .btn-danger {
            background-color: #f56c6c;
            color: white;
        }

        .btn-danger:hover {
            background-color: #f78989;
        }

        .btn-sm {
            padding: 5px 10px;
            font-size: 12px;
            border-radius: 4px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            filter: brightness(1.05);
        }

        .btn:active {
            transform: translateY(0);
            filter: brightness(0.95);
            transition: all 0.1s;
        }

        .btn-info {
            background-color: #6777ef;
            color: white;
        }

        .btn-info:hover {
            background-color: #7b88f1;
        }

        .btn-warning {
            background-color: #e6a23c;
            color: white;
        }

        .btn-warning:hover {
            background-color: #ebb563;
        }

        /* 聊天头部标题样式 */
        .chat-header-back {
            display: inline-flex;
            align-items: center;
            color: #333;
            font-size: 16px;
            font-weight: 500;
        }

        .chat-header-back .back-icon {
            font-size: 20px;
            margin-right: 8px;
            font-weight: bold;
        }

        .chat-header-back .session-title {
            font-size: 16px;
            font-weight: bold;
            color: inherit;
        }

        /* 移动端样式调整 */
        @media (max-width: 768px) {
            .chat-header-back {
                font-size: 14px;
            }

            .chat-header-back .back-icon {
                font-size: 18px;
                margin-right: 6px;
            }

            .chat-header-back .session-title {
                font-size: 14px;
            }
        }

        /* 优化设置按钮 */
        .settings-button {
            position: fixed;
            bottom: 15px;
            right: 15px;
            z-index: 1000;
            background-color: #67c23a;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            transition: all 0.3s;
            font-weight: 500;
        }

        .settings-button:hover {
            background-color: #85ce61;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.25);
        }

        .settings-button:active {
            transform: translateY(0);
        }

        /* 美化聊天输入区域和按钮 */
        .chat-input-container {
            background-color: #fff;
            padding: 15px;
            border-top: 1px solid #e0e0e0;
            display: flex;
            flex-direction: column;
            box-shadow: 0 -1px 5px rgba(0,0,0,0.05);
        }

        .message-textarea {
            resize: none;
            border: 1px solid #dcdfe6;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 12px;
            min-height: 80px;
            max-height: 150px;
            overflow-y: auto;
            transition: all 0.3s;
            font-size: 14px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05) inset;
        }

        .message-textarea:focus {
            border: 1px solid #409eff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(64,158,255,0.1), 0 1px 3px rgba(0,0,0,0.05) inset;
        }

        .send-btn-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        /* 按钮分组样式 */
        .button-group {
            display: flex;
            gap: 8px;
            align-items: center;
            flex-wrap: wrap;
            position: relative;
        }

        .button-group + .button-group::before {
            content: '';
            position: absolute;
            left: -10px;
            top: 50%;
            transform: translateY(-50%);
            height: 24px;
            width: 1px;
            background-color: #e0e0e0;
            display: block;
        }

        @media (max-width: 768px) {
            .button-group + .button-group::before {
                display: none;
            }
            
            /* 在移动端为按钮组添加上下分隔 */
            .button-group {
                padding: 8px 0;
                border-bottom: 1px solid #f0f0f0;
            }
            
            .button-group:last-child {
                border-bottom: none;
            }
        }

        /* 美化发送按钮和其他按钮 */
        .send-btn {
            background-color: #409eff;
            color: white;
            border: none;
            padding: 8px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 5px rgba(64,158,255,0.3);
            font-weight: 500;
            height: 38px;
            margin-left: auto;
        }

        .send-btn i {
            margin-right: 5px;
            font-size: 16px;
        }

        .send-btn:hover {
            background-color: #66b1ff;
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 4px 8px rgba(64,158,255,0.4);
        }

        .send-btn:active {
            transform: translateY(0);
        }

        .send-btn:disabled {
            background-color: #a0cfff;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .upload-btn {
            background-color: #6777ef;
            color: white;
            display: flex;
            align-items: center;
            box-shadow: 0 2px 5px rgba(103,119,239,0.3);
        }

        .upload-btn:hover {
            background-color: #7b88f1;
            box-shadow: 0 4px 8px rgba(103,119,239,0.4);
        }

        .join-requests-btn {
            background-color: #409eff;
            box-shadow: 0 2px 5px rgba(64,158,255,0.3);
        }

        .join-requests-btn:hover {
            background-color: #66b1ff;
            box-shadow: 0 4px 8px rgba(64,158,255,0.4);
        }

        .settings-btn {
            background-color: #67c23a;
            box-shadow: 0 2px 5px rgba(103,194,58,0.3);
        }

        .settings-btn:hover {
            background-color: #85ce61;
            box-shadow: 0 4px 8px rgba(103,194,58,0.4);
        }

        .invite-merchant-btn {
            background-color: #e6a23c;
            color: white;
            display: flex;
            align-items: center;
            box-shadow: 0 2px 5px rgba(230,162,60,0.3);
        }

        .invite-merchant-btn:hover {
            background-color: #ebb563;
            box-shadow: 0 4px 8px rgba(230,162,60,0.4);
        }

        /* 美化图片上传区域 */
        .image-upload-box {
            border: 2px dashed #dcdfe6;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin-bottom: 15px;
            transition: all 0.3s;
            cursor: pointer;
            background-color: #f9f9f9;
        }

        .image-upload-box:hover {
            border-color: #409eff;
            background-color: #f5f7fa;
        }

        .image-upload-box i {
            font-size: 40px;
            color: #909399;
            margin-bottom: 10px;
        }

        .image-upload-box p {
            color: #606266;
            margin: 5px 0 0;
        }

        /* 美化图片预览 */
        .message-image-container {
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            max-width: 100%;
            min-height: 60px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .message-image {
            max-width: 250px;
            max-height: 200px;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.3s;
            display: block;
            object-fit: contain;
        }

        .message-image:hover {
            transform: scale(1.02);
        }

        /* 图片预览手机端适配 */
        @media (max-width: 768px) {
            .message-image {
                max-width: 200px;
                max-height: 150px;
            }

            .message-image-container {
                margin-bottom: 10px;
            }
        }

        /* 手机端汉堡菜单样式 */
        .mobile-menu-toggle {
            display: none;
            background: #409eff;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .mobile-menu-toggle:hover {
            background: #66b1ff;
        }

        .mobile-menu-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .mobile-menu {
            position: fixed;
            top: 0;
            right: -300px;
            width: 280px;
            height: 100%;
            background: white;
            box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
            z-index: 1001;
            transition: right 0.3s ease;
            overflow-y: auto;
        }

        .mobile-menu.active {
            right: 0;
        }

        .mobile-menu-header {
            padding: 20px;
            background: #409eff;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .mobile-menu-close {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
        }

        .mobile-menu-content {
            padding: 20px;
        }

        .mobile-menu-item {
            display: block;
            width: 100%;
            padding: 12px 16px;
            margin-bottom: 8px;
            border: none;
            border-radius: 6px;
            background: #f8f9fa;
            color: #333;
            text-align: left;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .mobile-menu-item:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }

        .mobile-menu-item .icon-svg {
            margin-right: 8px;
            width: 16px;
            height: 16px;
        }

        /* 确保移动端菜单在小屏幕上正确显示 */
        @media (max-width: 480px) {
            .mobile-menu {
                width: 100%;
                right: -100%;
            }

            .mobile-menu-content {
                padding: 15px;
            }

            .mobile-menu-item {
                padding: 15px;
                font-size: 16px;
            }
        }

        /* 在移动端调整按钮样式 */
        @media (max-width: 768px) {
            .mobile-menu-toggle {
                display: inline-flex;
                align-items: center;
                gap: 5px;
                padding: 10px 15px;
                font-size: 14px;
            }

            .mobile-menu-overlay.active {
                display: block;
            }

            .send-btn-container {
                justify-content: space-between;
                align-items: center;
                flex-direction: row;
                gap: 10px;
                padding: 10px 15px;
            }

            .button-group {
                display: none; /* 隐藏桌面端的按钮组 */
            }

            .send-btn {
                margin-left: 0;
                flex: 1;
                max-width: none;
                padding: 12px 20px;
                font-size: 16px;
                border-radius: 8px;
            }

            .btn {
                padding: 10px 15px;
                font-size: 14px;
                border-radius: 6px;
                min-height: 44px; /* 确保触摸友好的最小高度 */
            }

            .btn i, .btn .icon-svg {
                font-size: 16px;
            }

            /* 手机端菜单项优化 */
            .mobile-menu-item {
                padding: 15px 20px;
                font-size: 16px;
                min-height: 50px;
                display: flex;
                align-items: center;
                gap: 12px;
            }

            .mobile-menu-item .icon-svg {
                width: 20px;
                height: 20px;
            }
        }
        
        /* 设置按钮样式 */
        .settings-panel {
            position: fixed;
            top: 0;
            right: 0;
            width: 400px;
            height: 100%;
            background-color: #fff;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
            z-index: 1001;
            padding: 20px;
            overflow-y: auto;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }
        .settings-panel.active {
            transform: translateX(0);
        }
        .settings-panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        .settings-panel-title {
            font-size: 18px;
            font-weight: bold;
            color: #303133;
        }
        .settings-panel-close {
            cursor: pointer;
            font-size: 20px;
            color: #909399;
        }
        .settings-panel-close:hover {
            color: #409eff;
        }
        .settings-section {
            margin-bottom: 25px;
        }
        .settings-section-title {
            font-size: 16px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 15px;
            padding-left: 10px;
            border-left: 3px solid #409eff;
        }
        .settings-form-item {
            margin-bottom: 15px;
        }
        .settings-form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: #606266;
        }
        .settings-form-hint {
            display: block;
            margin-top: 5px;
            font-size: 12px;
            color: #909399;
        }
        .settings-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0,0,0,0.5);
            z-index: 1000;
            display: none;
        }
        .settings-overlay.active {
            display: block;
        }
        
        @media (max-width: 768px) {
            .settings-panel {
                width: 100%;
                left: 0;
                right: 0;
            }
        }
        .chat-container { 
            display: flex; 
            height: 100vh; /* 使用视口高度 */
            min-height: 600px; /* 最小高度确保在小屏幕上也有足够空间 */
            max-height: 100vh; /* 最大高度确保在大屏幕上不会过大 */
            overflow: hidden; 
        }
        .sidebar { 
            width: 280px; 
            background-color: #fff; 
            border-right: 1px solid #e0e0e0; 
            display: flex; 
            flex-direction: column;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        .main-content { 
            flex: 1; 
            display: flex; 
            flex-direction: column;
            background-color: #f9f9f9;
        }
        .session-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
            display: flex;
            align-items: flex-start;
        }
        .session-item:hover {
            background-color: #f5f7fa;
        }
        .session-item.active {
            background-color: #ecf5ff;
            border-left: 3px solid #409eff;
        }
        .session-item-avatar {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background-image: linear-gradient(135deg, #409eff, #95c7f3);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 18px;
            flex-shrink: 0;
            margin-right: 10px;
        }
        .session-item-content {
            flex: 1;
            min-width: 0;
        }
        .session-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }
        .session-item-title {
            font-weight: bold;
            font-size: 14px;
            color: #333;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .session-item-time {
            font-size: 12px;
            color: #999;
            white-space: nowrap;
        }
        .session-item-message {
            color: #666;
            font-size: 13px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-bottom: 5px;
        }
        .session-item-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .input-tips {
            text-align: right;
            font-size: 12px;
            color: #999;
            margin-bottom: 8px;
        }
        .status-bar {
            background-color: #ecf8ff;
            padding: 12px 15px;
            text-align: center;
            color: #409eff;
            margin-bottom: 10px;
            font-size: 14px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .status-bar i {
            margin-right: 5px;
            font-size: 16px;
        }

        .status-bar span {
            transition: color 0.3s;
        }

        .status-bar span:hover {
            color: #409eff;
        }

        /* 状态栏布局优化 */
        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-bar-left {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-bar-right {
            display: flex;
            align-items: center;
        }

        /* 移动端头部菜单按钮 */
        .mobile-header-menu-btn {
            display: flex;
            align-items: center;
            gap: 6px;
            background: linear-gradient(135deg, #409eff, #66b1ff);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .mobile-header-menu-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .mobile-header-menu-btn:hover::before {
            left: 100%;
        }

        .mobile-header-menu-btn:hover {
            background: linear-gradient(135deg, #66b1ff, #409eff);
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 4px 16px rgba(64, 158, 255, 0.4);
        }

        .mobile-header-menu-btn:active {
            transform: translateY(0) scale(0.98);
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
        }

        .mobile-header-menu-btn .menu-text {
            font-size: 13px;
            font-weight: 700;
            letter-spacing: 0.5px;
        }

        .mobile-header-menu-btn i {
            font-size: 16px;
            transition: transform 0.3s ease;
        }

        .mobile-header-menu-btn:hover i {
            transform: rotate(90deg);
        }

        /* 移动端头部菜单面板样式 */
        .mobile-header-menu-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1500;
            backdrop-filter: blur(4px);
        }

        .mobile-header-menu-overlay.active {
            display: block;
        }

        .mobile-header-menu {
            position: fixed;
            top: 0;
            right: -350px;
            width: 320px;
            height: 100%;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            box-shadow: -8px 0 32px rgba(0, 0, 0, 0.15);
            z-index: 1501;
            transition: right 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            overflow-y: auto;
            border-radius: 24px 0 0 24px;
        }

        .mobile-header-menu.active {
            right: 0;
        }

        .mobile-header-menu-header {
            padding: 24px 20px;
            background: linear-gradient(135deg, #409eff, #66b1ff);
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .mobile-header-menu-header::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }

        .mobile-header-menu-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 700;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 1;
        }

        .mobile-header-menu-close {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            position: relative;
            z-index: 1;
        }

        .mobile-header-menu-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: rotate(90deg) scale(1.1);
        }

        .mobile-header-menu-content {
            padding: 24px 20px;
        }

        .mobile-header-menu-item {
            display: flex;
            align-items: center;
            gap: 16px;
            width: 100%;
            padding: 16px 20px;
            margin-bottom: 12px;
            border: none;
            border-radius: 16px;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            color: #374151;
            cursor: pointer;
            font-size: 15px;
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }

        .mobile-header-menu-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(64, 158, 255, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .mobile-header-menu-item:hover::before {
            left: 100%;
        }

        .mobile-header-menu-item:hover {
            background: linear-gradient(135deg, #e0f2fe 0%, #e1f5fe 100%);
            color: #409eff;
            transform: translateX(8px) scale(1.02);
            box-shadow: 0 6px 20px rgba(64, 158, 255, 0.15);
        }

        .mobile-header-menu-item:active {
            transform: translateX(4px) scale(0.98);
        }

        .mobile-header-menu-item .icon-svg {
            width: 20px;
            height: 20px;
            fill: currentColor;
            transition: all 0.3s ease;
        }

        .mobile-header-menu-item:hover .icon-svg {
            transform: scale(1.1);
            fill: #409eff;
        }

        /* 移动端头部菜单响应式优化 */
        @media (max-width: 480px) {
            .mobile-header-menu {
                width: 100%;
                right: -100%;
                border-radius: 0;
            }

            .mobile-header-menu-content {
                padding: 20px 16px;
            }

            .mobile-header-menu-item {
                padding: 18px 20px;
                font-size: 16px;
                margin-bottom: 16px;
            }

            .mobile-header-menu-item .icon-svg {
                width: 24px;
                height: 24px;
            }
        }
        .chat-header { 
            padding: 15px; 
            background-color: #fff; 
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        .header-info {
            display: flex;
            flex-direction: column;
        }
        .session-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
        .session-id {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }
        .session-status {
            display: flex;
            align-items: center;
        }
        .chat-messages { 
            flex: 1; 
            padding: 15px;
            overflow-y: auto;
            background-color: #f5f5f5;
        }
        .message {
            margin-bottom: 20px;
        }
        .message-content {
            flex: 1;
            max-width: 100%;
            display: flex;
            flex-direction: column;
        }
        .message-customer {
            display: flex;
            justify-content: flex-start;
            margin-right: 15%;
            position: relative;
        }
        .message-staff {
            display: flex;
            justify-content: flex-end;
            margin-left: 15%;
            position: relative;
        }
        .message-customer .message-content {
            align-items: flex-start;
        }
        .message-staff .message-content {
            align-items: flex-end;
        }
        .message-bubble {
            padding: 12px 16px;
            border-radius: 18px;
            position: relative;
            word-break: break-word;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            max-width: 100%;
            display: inline-block;
        }
        .message-customer .message-bubble {
            background-color: #ffffff;
            border-top-left-radius: 4px;
        }
        .message-staff .message-bubble {
            background-color: #95ec69;
            border-top-right-radius: 4px;
        }
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin: 0 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 16px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            flex-shrink: 0;
        }
        .customer-avatar {
            background-color: #67c23a;
            background-image: linear-gradient(135deg, #67c23a, #95ec69);
        }
        .staff-avatar {
            background-color: #409eff;
            background-image: linear-gradient(135deg, #409eff, #95c7f3);
        }
        .message-sender-name {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
            font-weight: 500;
        }
        .message-customer .message-sender-name {
            text-align: left;
        }
        .message-staff .message-sender-name {
            text-align: right;
        }
        .message-time {
            font-size: 12px;
            color: #999;
            margin-top: 5px;
        }
        .message-customer .message-time {
            text-align: left;
        }
        .message-staff .message-time {
            text-align: right;
        }
        .message-text {
            white-space: pre-wrap;
            line-height: 1.5;
        }
        .message-image-container {
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            max-width: 100%;
        }
        .message-image {
            max-width: 250px;
            max-height: 200px;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.3s;
            display: block;
            object-fit: contain;
        }
        .avatar-text {
            font-size: 18px;
        }
        .time-divider {
            text-align: center;
            color: #909399;
            font-size: 12px;
            margin: 15px 0;
            position: relative;
        }
        .time-divider:before, .time-divider:after {
            content: '';
            position: absolute;
            top: 50%;
            width: 40%;
            height: 1px;
            background-color: rgba(144, 147, 153, 0.2);
        }
        .time-divider:before {
            left: 0;
        }
        .time-divider:after {
            right: 0;
        }
        .time-divider-content {
            background-color: rgba(255,255,255,0.8);
            padding: 0 10px;
            border-radius: 10px;
            position: relative;
            z-index: 1;
        }
        .notification-badge {
            position: absolute;
            top: 10px;
            right: 20px;
            background-color: #f56c6c;
            color: white;
            padding: 5px 10px;
            font-size: 14px;
            border-radius: 15px;
            animation: pulse 1.5s infinite;
            box-shadow: 0 2px 6px rgba(245,108,108,0.4);
        }
        @keyframes pulse {
            0% {transform: scale(1);}
            50% {transform: scale(1.1);}
            100% {transform: scale(1);}
        }
        .chat-input-container {
            background-color: #fff;
            padding: 15px;
            border-top: 1px solid #e0e0e0;
            display: flex;
            flex-direction: column;
            box-shadow: 0 -1px 5px rgba(0,0,0,0.05);
        }
        .input-toolbar {
            display: flex;
            margin-bottom: 10px;
        }
        .input-toolbar .toolbar-btn {
            margin-right: 15px;
            font-size: 20px;
            color: #606266;
            cursor: pointer;
            transition: all 0.3s;
        }
        .input-toolbar .toolbar-btn:hover {
            color: #409eff;
            transform: scale(1.1);
        }
        .message-textarea {
            resize: none;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 10px;
            min-height: 80px;
            max-height: 150px;
            overflow-y: auto;
            transition: border 0.3s;
        }
        .message-textarea:focus {
            border: 1px solid #409eff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(64,158,255,0.1);
        }
        .send-btn-container {
            display: flex;
            justify-content: flex-end;
        }
        .send-btn {
            background-color: #409eff;
            color: white;
            border: none;
            padding: 8px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .send-btn:hover {
            background-color: #66b1ff;
        }
        .send-btn:disabled {
            background-color: #a0cfff;
            cursor: not-allowed;
        }
        @media (max-width: 768px) {
            .chat-container {
                flex-direction: column;
                height: 100vh;
                overflow: hidden;
                position: relative;
            }
            .sidebar {
                width: 100%;
                height: 100%;
                overflow-y: auto;
                position: absolute;
                top: 0;
                left: 0;
                z-index: 10;
                background-color: #fff;
                transition: transform 0.3s ease;
            }
            .sidebar.hidden {
                transform: translateX(-100%);
            }
            .main-content {
                width: 100%;
                height: 100%;
                display: flex;
                flex-direction: column;
                position: absolute;
                top: 0;
                left: 0;
                z-index: 5;
                transition: transform 0.3s ease;
                background-color: #f9f9f9;
            }
            .chat-header {
                position: relative;
                padding: 12px 15px;
                min-height: 60px;
                display: flex;
                align-items: center;
            }
            .back-button {
                position: absolute;
                left: 15px;
                top: 50%;
                transform: translateY(-50%);
                background: transparent;
                border: none;
                color: #409eff;
                font-size: 20px;
                cursor: pointer;
                z-index: 15;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 32px;
                height: 32px;
            }
            .chat-messages {
                flex: 1;
                padding: 10px;
            }
            .message-bubble {
                max-width: 85%;
                padding: 12px 15px;
                font-size: 15px;
                line-height: 1.5;
                border-radius: 16px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }

            .message-customer .message-bubble {
                border-top-left-radius: 6px;
            }

            .message-staff .message-bubble {
                border-top-right-radius: 6px;
            }
            .avatar {
                width: 32px;
                height: 32px;
                font-size: 14px;
            }
            .chat-input-container {
                padding: 15px 10px;
                border-top: 2px solid #e0e0e0;
            }
            .message-textarea {
                min-height: 80px;
                font-size: 16px;
                padding: 15px;
                border-radius: 10px;
                line-height: 1.5;
            }
            .send-btn, .upload-btn {
                padding: 6px 12px;
                font-size: 13px;
            }
            .welcome-container {
                width: 90%;
                padding: 20px;
            }
            .welcome-features {
                gap: 15px;
            }
            .feature-item i {
                font-size: 22px;
            }
            .message-image {
                max-width: 200px;
                max-height: 160px;
            }
            .session-item {
                padding: 15px 12px;
                border-bottom: 1px solid #f0f0f0;
                min-height: 70px;
            }
            .session-item-avatar {
                width: 45px;
                height: 45px;
                font-size: 18px;
                margin-right: 12px;
            }
            .session-item-title {
                font-size: 16px;
                font-weight: 600;
                margin-bottom: 4px;
            }
            .session-item-message {
                font-size: 14px;
                line-height: 1.4;
                margin-bottom: 8px;
            }
            .session-item-time {
                font-size: 12px;
            }
            .session-item-footer {
                margin-top: 8px;
            }
            .session-item-footer .el-tag {
                font-size: 11px;
                padding: 2px 6px;
            }
            /* 添加一些过渡效果，使UI更流畅 */
            .chat-container * {
                transition: all 0.2s ease;
            }
            /* 优化移动端的长按体验 */
            .message-bubble {
                -webkit-touch-callout: none; /* 禁止iOS长按呼出菜单 */
                -webkit-user-select: none; /* 禁止选择文本 */
                -webkit-tap-highlight-color: transparent; /* 去除点击高亮 */
            }
            /* 优化移动端的滚动体验 */
            .chat-messages {
                -webkit-overflow-scrolling: touch; /* 平滑滚动 */
                scroll-behavior: smooth;
            }
            /* 增加移动端的按钮触摸区域 */
            .send-btn, .upload-btn, .back-button {
                min-height: 44px;
                min-width: 44px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 8px;
            }

            .back-button {
                background-color: rgba(64, 158, 255, 0.1);
                color: #409eff;
                border: 1px solid rgba(64, 158, 255, 0.2);
                transition: all 0.3s;
            }

            .back-button:hover {
                background-color: rgba(64, 158, 255, 0.2);
            }

            .back-button i {
                font-size: 18px;
            }
            /* 优化移动端键盘弹出时的体验 */
            .message-textarea:focus {
                position: relative;
                z-index: 100;
            }

            /* 整体移动端优化 */
            body {
                -webkit-overflow-scrolling: touch;
                -webkit-tap-highlight-color: transparent;
            }

            /* 优化移动端触摸体验 */
            .btn, .mobile-menu-item, .session-item, .preset-reply-chip {
                -webkit-tap-highlight-color: transparent;
                touch-action: manipulation;
            }

            /* 移动端安全区域适配 */
            .chat-container {
                padding-bottom: env(safe-area-inset-bottom);
            }

            /* 移动端状态栏优化 */
            .status-bar {
                padding: 12px 16px;
                background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
                border-bottom: 1px solid #e5e7eb;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            }

            .status-bar-left span {
                font-size: 16px;
                font-weight: 600;
                color: #374151;
            }

            .mobile-header-menu-btn {
                padding: 6px 12px;
                font-size: 12px;
            }

            .mobile-header-menu-btn .menu-text {
                font-size: 12px;
            }

            .mobile-header-menu-btn i {
                font-size: 14px;
            }

            .chat-input-container {
                padding-bottom: calc(15px + env(safe-area-inset-bottom));
            }
        }
        .welcome-container {
            text-align: center;
            padding: 30px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            max-width: 500px;
        }
        .welcome-icon {
            font-size: 60px;
            color: #409eff;
            margin-bottom: 20px;
        }
        .welcome-title {
            color: #333;
            margin-bottom: 10px;
            font-size: 24px;
        }
        .welcome-subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }
        .welcome-features {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 20px;
        }
        .feature-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #606266;
        }
        .feature-item i {
            font-size: 28px;
            margin-bottom: 10px;
            color: #409eff;
        }
        .system-message {
            text-align: center;
            color: #909399;
            font-size: 12px;
            margin: 10px 0;
            padding: 5px 10px;
            background-color: rgba(255,255,255,0.8);
            border-radius: 15px;
            display: inline-block;
            margin-left: auto;
            margin-right: auto;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        .message-file {
            display: flex;
            align-items: center;
            background: #f0f0f0;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
        }
        .message-file:hover {
            background: #e8e8e8;
        }
        /* 添加悬浮回到底部按钮 */
        .scroll-to-bottom {
            position: fixed;
            right: 20px;
            bottom: 80px;
            width: 40px;
            height: 40px;
            background-color: rgba(64, 158, 255, 0.8);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            z-index: 100;
            transition: all 0.3s;
            opacity: 0;
            transform: translateY(20px);
            cursor: pointer;
        }
        .scroll-to-bottom.visible {
            opacity: 1;
            transform: translateY(0);
        }
        @media (max-width: 768px) {
            .scroll-to-bottom {
                right: 15px;
                bottom: 90px;
                width: 44px;
                height: 44px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            }

            .scroll-to-bottom i {
                font-size: 18px;
            }
        }
        
        /* 根据不同屏幕高度调整聊天容器 */
        @media (max-height: 768px) {
            .chat-container {
                height: 95vh;
                min-height: 500px;
            }
        }
        
        @media (max-height: 600px) {
            .chat-container {
                height: 98vh;
                min-height: 450px;
            }
        }
        
        /* 预设问题样式 */
        .preset-questions {
            background: #f8fafc;
            border-radius: 8px;
            padding: 12px 15px;
            margin: 10px 0;
            border: 1px solid #e2e8f0;
        }
        
        .preset-question-item {
            display: block;
            padding: 8px 12px;
            margin: 5px 0;
            background: #fff;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .preset-question-item:hover {
            background: #f0f9ff;
            border-color: #bae6fd;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        
        /* 确保预设问题中的文字是深色的 */
        .message-staff .message-content .preset-questions,
        .message-staff .message-content .preset-question-item {
            color: #333;
        }
        
        /* 添加右键菜单样式 */
        .message-context-menu {
            position: fixed;
            background: white;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            padding: 5px 0;
            z-index: 1000;
        }
        
        .menu-item {
            padding: 8px 15px;
            cursor: pointer;
            transition: all 0.2s;
            white-space: nowrap;
            font-size: 14px;
            color: #333;
        }
        
        .menu-item:hover {
            background: #f2f6fc;
        }
        
        .menu-item.danger {
            color: #f56c6c;
        }
        
        .menu-item.disabled {
            color: #c0c4cc;
            cursor: not-allowed;
            background: #f5f7fa;
        }

        /* 右键菜单手机端适配 */
        @media (max-width: 768px) {
            .message-context-menu {
                min-width: 150px;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            }

            .menu-item {
                padding: 12px 20px;
                font-size: 16px;
                min-height: 48px;
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .menu-item .icon-svg {
                width: 18px;
                height: 18px;
            }
        }
        
        .revoked-message {
            color: #909399;
            font-style: italic;
            padding: 8px 12px;
            background-color: #f8f8f8;
            border-radius: 8px;
            display: inline-block;
        }
        
        /* 添加图片加载失败样式 */
        .image-error-text {
            background-color: #f8f8f8;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            color: #909399;
            font-size: 13px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .image-error-text i {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .found-merchants {
            margin-top: 15px;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }

        .found-merchants p {
            font-weight: bold;
            margin-bottom: 10px;
            color: #409EFF;
        }

        .el-radio-group {
            display: flex;
            flex-direction: column;
        }

        .el-radio {
            margin-bottom: 8px;
        }
        
        /* 添加消息底部样式 */
        .message-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            margin-top: 4px;
        }
        
        /* 已读/未读状态样式 */
        .message-read-status {
            font-size: 11px;
            margin-left: 6px;
        }
        
        .read-status {
            padding: 1px 4px;
            border-radius: 8px;
        }
        
        .read-status.all_read {
            color: #67c23a;
        }

        .read-status.customer_read {
            color: #409eff;
        }

        .read-status.merchant_read {
            color: #e6a23c;
        }

        .read-status.staff_read {
            color: #909399;
        }

        .read-status.partial_read {
            color: #e6a23c;
        }

        .read-status.unread {
            color: #f56c6c;
        }

        .read-status.read {
            color: #67c23a;
        }
        
        /* 针对右侧消息样式调整 */
        .message-staff .message-footer {
            flex-direction: row-reverse;
        }
        
        .message-staff .message-read-status {
            margin-left: 0;
            margin-right: 6px;
        }

        /* SVG图标样式 */
        .icon-svg {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 1em;
            height: 1em;
            vertical-align: -0.15em;
            fill: currentColor;
            overflow: hidden;
            margin-right: 5px;
        }

        .icon-svg.no-margin {
            margin-right: 0;
        }

        .icon-svg svg {
            width: 100%;
            height: 100%;
        }

        .btn .icon-svg {
            font-size: 1.2em;
        }

        /* 调整按钮内的图标样式 */
        .btn i, .btn .icon-svg {
            margin-right: 5px;
        }

        /* 在移动端调整按钮样式 */
        @media (max-width: 768px) {
            .btn .icon-svg {
                font-size: 1.1em;
            }
        }

        .image-upload-box .icon-svg {
            font-size: 40px;
            color: #909399;
            margin-bottom: 10px;
            display: block;
            margin: 0 auto 10px;
        }

        /* 角色标签样式改进 - 参考截图样式 */
        .message-sender-role {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 13px;
            font-weight: 500;
            line-height: 1.4;
        }

        .message-sender-role i {
            margin-right: 4px;
        }

        .customer-tag {
            background-color: #e6f4ff;
            color: #0958d9;
        }

        .merchant-tag {
            background-color: #fff7e6;
            color: #d46b08;
        }

        .staff-tag {
            background-color: #fff1f0;
            color: #cf1322;
        }
        
        .supplier-tag {
            background-color: #f6ffed;
            color: #52c41a;
        }

        /* Element Plus 对话框手机端适配 */
        @media (max-width: 768px) {
            .el-dialog {
                width: 95% !important;
                margin: 10px auto !important;
                max-height: 90vh !important;
            }

            .el-dialog__header {
                padding: 12px 16px !important;
            }

            .el-dialog__body {
                padding: 16px !important;
                max-height: 60vh;
                overflow-y: auto;
            }

            .el-dialog__footer {
                padding: 12px 16px !important;
            }

            .el-dialog .el-button {
                width: 100%;
                margin: 4px 0 !important;
            }

            .el-tabs__content {
                max-height: 50vh;
                overflow-y: auto;
            }
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .message-sender-role {
                font-size: 12px;
                padding: 2px 6px;
            }
        }

        /* 为不同角色添加不同的样式 */
        .supplier-tag {
            background-color: #8BC34A; /* 绿色背景 */
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
        }

        .merchant-tag {
            background-color: #03A9F4; /* 蓝色背景 */
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        /* 预设回复样式 */
        .preset-replies-panel {
            position: fixed;
            top: 0;
            right: 0;
            width: 400px;
            height: 100%;
            background-color: #fff;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
            z-index: 1001;
            padding: 20px;
            overflow-y: auto;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }
        .preset-replies-panel.active {
            transform: translateX(0);
        }

        @media (max-width: 768px) {
            .preset-replies-panel {
                width: 100%;
                left: 0;
                right: 0;
            }
        }
        .preset-replies-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        .preset-replies-title {
            font-size: 18px;
            font-weight: bold;
            color: #303133;
        }
        .preset-replies-close {
            cursor: pointer;
            font-size: 20px;
            color: #909399;
        }
        .preset-replies-close:hover {
            color: #409eff;
        }
        .preset-reply-item {
            padding: 12px 15px;
            background-color: #f5f7fa;
            border-radius: 4px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s;
            border-left: 3px solid #dcdfe6;
        }
        .preset-reply-item:hover {
            background-color: #ecf5ff;
            border-left-color: #409eff;
            transform: translateX(5px);
        }
        .preset-reply-content {
            color: #606266;
            font-size: 14px;
            margin-bottom: 5px;
            white-space: pre-wrap;
        }
        .preset-reply-actions {
            display: flex;
            justify-content: flex-end;
            margin-top: 8px;
        }
        .preset-reply-actions button {
            padding: 4px 8px;
            font-size: 12px;
            margin-left: 5px;
        }

        /* 预设回复手机端适配 */
        @media (max-width: 768px) {
            .preset-reply-item {
                margin-bottom: 15px;
                padding: 12px;
            }

            .preset-reply-title {
                font-size: 14px;
                margin-bottom: 8px;
            }

            .preset-reply-content {
                font-size: 13px;
                margin-bottom: 10px;
            }

            .preset-reply-actions {
                flex-wrap: wrap;
                gap: 6px;
                justify-content: flex-start;
            }

            .preset-reply-actions .el-button {
                margin: 0;
                padding: 8px 12px;
                font-size: 12px;
                flex: 1;
                min-width: calc(50% - 3px);
            }

            .preset-replies-header {
                padding: 12px 16px;
            }

            .preset-replies-title {
                font-size: 16px;
            }
        }

        /* 欢迎界面按钮手机端适配 */
        @media (max-width: 768px) {
            .welcome-buttons {
                flex-direction: column !important;
                gap: 15px !important;
                padding: 0 20px;
            }

            .welcome-buttons .btn {
                width: 100%;
                padding: 15px 20px;
                font-size: 16px;
                justify-content: center;
                min-height: 50px;
            }

            .welcome-buttons .btn .icon-svg {
                width: 20px;
                height: 20px;
                margin-right: 10px;
            }
        }
        .preset-reply-tag {
            display: inline-block;
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 2px;
            background-color: #f0f9ff;
            color: #409eff;
            margin-right: 5px;
        }
        .preset-reply-search {
            margin-bottom: 15px;
        }
        .preset-reply-quickbar {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-bottom: 10px;
            padding: 8px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }
        .preset-reply-chip {
            padding: 4px 8px;
            background-color: #ecf5ff;
            border: 1px solid #d9ecff;
            border-radius: 4px;
            color: #409eff;
            font-size: 12px;
            cursor: pointer;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100px;
            transition: all 0.2s;
        }
        .preset-reply-chip:hover {
            background-color: #dbecff;
            transform: translateY(-2px);
        }

        /* 预设回复芯片手机端适配 */
        @media (max-width: 768px) {
            .preset-reply-chip {
                padding: 8px 12px;
                font-size: 14px;
                margin: 4px;
                max-width: none;
                min-height: 36px;
                display: flex;
                align-items: center;
                justify-content: center;
                text-align: center;
            }

            .quick-replies {
                flex-wrap: wrap;
                gap: 8px;
                margin-bottom: 15px;
            }
        }
        
        /* 操作按钮组样式 */
        .action-buttons {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-left: 10px;
        }
        
        .action-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s;
            color: #666;
            background-color: #f5f7fa;
        }
        
        .action-btn:hover {
            background-color: #e6f1fc;
            color: #409eff;
        }
        
        .preset-btn {
            background-color: #f0f9eb;
            color: #67c23a;
        }
        
        .preset-btn:hover {
            background-color: #e1f3d8;
            color: #67c23a;
        }
        
        .upload-btn {
            background-color: #f4f4f5;
            color: #909399;
        }
        
        .upload-btn:hover {
            background-color: #e9e9eb;
            color: #606266;
        }
        
        .send-btn {
            background-color: #ecf5ff;
            color: #409eff;
        }
        
        .send-btn:hover {
            background-color: #d9ecff;
            color: #409eff;
        }

        /* 设置对话框样式 */
        .settings-dialog {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .settings-dialog-backdrop {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(2px);
        }

        .settings-dialog-content {
            position: relative;
            width: 550px;
            max-width: 90%;
            max-height: 85vh;
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        @media (max-width: 768px) {
            .settings-dialog-content {
                width: 95%;
                max-width: 95%;
                max-height: 90vh;
                border-radius: 8px;
                margin: 10px;
            }

            .settings-dialog-header {
                padding: 12px 16px;
            }

            .settings-dialog-body {
                padding: 16px;
            }

            .settings-dialog-footer {
                padding: 12px 16px;
                flex-direction: column;
                gap: 8px;
            }

            .settings-dialog-footer .btn {
                width: 100%;
                margin: 0;
            }
        }
        .settings-dialog-content {
            animation: dialogFadeIn 0.3s ease;
        }

        @keyframes dialogFadeIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .settings-dialog-header {
            padding: 18px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #f9fafc;
        }

        .settings-dialog-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #303133;
            position: relative;
            padding-left: 12px;
        }

        .settings-dialog-header h3::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 18px;
            background: linear-gradient(to bottom, #409eff, #67c23a);
            border-radius: 2px;
        }

        .settings-dialog-header .close-btn {
            background: none;
            border: none;
            cursor: pointer;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
            color: #909399;
        }

        .settings-dialog-header .close-btn:hover {
            background-color: #f2f6fc;
            color: #409eff;
        }

        .settings-dialog-body {
            padding: 20px;
            overflow-y: auto;
            flex: 1;
        }

        .settings-dialog-footer {
            padding: 16px 20px;
            border-top: 1px solid #eee;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            background-color: #f9fafc;
        }

        .action-btn:hover {
            background-color: #e9e9eb;
        }

        .form-group {
            margin-bottom: 20px;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #606266;
        }

        .form-control {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #dcdfe6;
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.3s;
            box-sizing: border-box;
        }

        .form-control:focus {
            border-color: #409eff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(64,158,255,0.2);
        }

        .question-list {
            border: 1px solid #ebeef5;
            border-radius: 8px;
            padding: 8px;
            background-color: #f9fafc;
            margin-top: 10px;
        }

        .question-item {
            border: 1px solid #ebeef5;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            background-color: #fff;
            transition: all 0.3s;
            position: relative;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
        }

        .question-item:last-child {
            margin-bottom: 0;
        }

        .question-item:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            border-color: #d9ecff;
        }

        .question-item .item-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        .question-item .item-number {
            background-color: #409eff;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            margin-right: 10px;
            flex-shrink: 0;
        }

        .question-actions {
            position: absolute;
            top: 12px;
            right: 12px;
            display: flex;
            gap: 8px;
        }

        .input-group {
            margin-bottom: 12px;
        }

        .input-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: #606266;
            font-size: 13px;
        }

        .help-text {
            font-size: 12px;
            color: #909399;
            margin-top: 6px;
            line-height: 1.4;
        }

        .switch-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 16px;
            background-color: #ecf5ff;
            border-radius: 6px;
            margin-bottom: 12px;
            border-left: 3px solid #409eff;
        }

        .switch-label span {
            font-weight: 500;
            color: #409eff;
        }

        .text-center {
            text-align: center;
        }

        /* 添加图标SVG样式 */
        .icon-svg {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 1em;
            height: 1em;
            font-size: inherit;
        }
        
        .icon-svg svg {
            width: 1em;
            height: 1em;
            fill: currentColor;
        }
        
        /* 添加删除图标的SVG样式 */
        .delete-icon-svg {
            display: none;
        }
        
        /* 用于替换el-icon的全局规则 */
        i.el-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        /* 预设问题回复类型选择器样式 */
        .reply-type-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .reply-type {
            display: inline-flex;
            align-items: center;
            padding: 5px 12px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .reply-type.active {
            border-color: #409eff;
            background-color: #ecf5ff;
            color: #409eff;
        }

        .reply-type input {
            margin-right: 4px;
            position: absolute;
            opacity: 0;
        }

        /* 内容区域样式 */
        .content-section {
            margin-bottom: 15px;
            padding: 12px;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            background: #f8f9fa;
        }

        .content-label {
            display: block;
            font-weight: 500;
            margin-bottom: 8px;
            color: #495057;
            font-size: 14px;
        }

        .media-upload-container {
            width: 100%;
            min-height: 80px;
            position: relative;
        }

        .upload-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100px;
            border: 1px dashed #dcdfe6;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .upload-placeholder:hover {
            border-color: #409eff;
            color: #409eff;
        }

        .upload-placeholder i {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .image-preview {
            position: relative;
            max-width: 100%;
        }

        .preview-image {
            max-width: 100%;
            max-height: 200px;
            border-radius: 4px;
        }

        .file-preview {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px;
            background: #f5f7fa;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .remove-media {
            position: absolute;
            right: 5px;
            top: 5px;
            background: rgba(0,0,0,0.5);
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .file-preview .remove-media {
            position: static;
            background: none;
            color: #f56c6c;
        }
    </style>
</head>
<body>
    <div id="app" v-cloak>

        
        <!-- 移动端头部菜单面板 -->
        <div class="mobile-header-menu-overlay" :class="{ active: showMobileHeaderMenu }" @click="closeMobileHeaderMenu"></div>
        <div class="mobile-header-menu" :class="{ active: showMobileHeaderMenu }">
            <div class="mobile-header-menu-header">
                <h3>功能菜单</h3>
                <button class="mobile-header-menu-close" @click="closeMobileHeaderMenu">×</button>
            </div>
            <div class="mobile-header-menu-content">
                <!-- 供货商申请 -->
                <button class="mobile-header-menu-item" @click="goToJoinRequests(); closeMobileHeaderMenu()">
                    <span class="icon-svg">
                        <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                            <path d="M832 512a192 192 0 0 0-384 0c0 35.2 10.24 67.84 26.88 96H448a192 192 0 0 0-384 0c0 35.2 10.24 67.84 26.88 96h-18.56C32.96 704 0 736.96 0 776.32V816c0 39.36 32.96 72.32 72.32 72.32h879.36c39.36 0 72.32-32.96 72.32-72.32v-39.68c0-39.36-32.96-72.32-72.32-72.32h-18.56C949.76 675.84 960 643.2 960 608a192 192 0 0 0-128-180.48V300.8c0-26.24-21.12-47.36-47.36-47.36H239.36c-26.24 0-47.36 21.12-47.36 47.36v126.72A192 192 0 0 0 64 608c0 35.2 10.24 67.84 26.88 96H72.32a24.32 24.32 0 0 0-24.32 24.32V816c0 13.44 10.88 24.32 24.32 24.32h879.36A24.32 24.32 0 0 0 976 816v-39.68a24.32 24.32 0 0 0-24.32-24.32h-26.88c16.64-28.16 26.88-60.8 26.88-96 0-35.2-10.24-67.84-26.88-96zM640 512c0-70.4 57.6-128 128-128s128 57.6 128 128-57.6 128-128 128-128-57.6-128-128zm-512 0c0-70.4 57.6-128 128-128s128 57.6 128 128-57.6 128-128 128-128-57.6-128-128zm368 128c35.2 0 64 28.8 64 64H240c0-35.2 28.8-64 64-64h192zm256 0c35.2 0 64 28.8 64 64H624c0-35.2 28.8-64 64-64h64zM240 300.8c0-8.96 7.04-16 16-16h545.28c8.96 0 16 7.04 16 16V375.68a191.36 191.36 0 0 0-40.96-4.8c-34.56 0-66.88 9.6-95.36 26.24A192 192 0 0 0 576 300.8a193.28 193.28 0 0 0-142.08 64A193.28 193.28 0 0 0 288 300.8c-12.8 0-24.96 1.92-36.48 4.8h-.64v-4.8z"/>
                        </svg>
                    </span>
                    供货商申请
                </button>
            </div>
        </div>

        <!-- 设置面板 -->
        <div class="settings-overlay" :class="{ active: showSettings }" @click="closeSettings"></div>
        <div class="settings-panel" :class="{ active: showSettings }">
            <div class="settings-panel-header">
                <div class="settings-panel-title">商家消息系统设置</div>
                <div class="settings-panel-close" @click="closeSettings">
                    <span class="icon-svg">
                        <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                            <path d="M563.8 512l262.5-312.9c4.4-5.2.7-13.1-6.1-13.1h-79.8c-4.7 0-9.2 2.1-12.3 5.7L511.6 449.8 295.1 191.7c-3-3.6-7.5-5.7-12.3-5.7H203c-6.8 0-10.5 7.9-6.1 13.1L459.4 512 196.9 824.9c-4.4 5.2-.7 13.1 6.1 13.1h79.8c4.7 0 9.2-2.1 12.3-5.7l216.5-258.1 216.5 258.1c3 3.6 7.5 5.7 12.3 5.7h79.8c6.8 0 10.5-7.9 6.1-13.1L563.8 512z"/>
                        </svg>
                    </span>
                </div>
            </div>
            
            <div class="settings-section">
                <div class="settings-section-title">供货商设置</div>
                <div class="settings-form-item">
                    <el-switch
                        v-model="settings.allowSupplier"
                        active-text="允许供货商加入"
                        inactive-text="禁止供货商加入">
                    </el-switch>
                    <div class="settings-form-hint">开启后，供货商可以加入消息系统</div>
                </div>
            </div>
            
            <div class="settings-section">
                <div class="settings-section-title">订单消息设置</div>
                <div class="settings-form-item">
                    <el-switch
                        v-model="settings.enableOrderQuery"
                        active-text="启用订单查询"
                        inactive-text="禁用订单查询">
                    </el-switch>
                    <div class="settings-form-hint">启用后，客户可以通过输入订单号查询订单信息</div>
                </div>
                
                <div class="settings-form-item" v-if="settings.enableOrderQuery">
                    <div class="settings-form-label">订单查询提示语</div>
                    <el-input
                        v-model="settings.orderQueryPrompt"
                        type="textarea"
                        :rows="2"
                        placeholder="请输入提示语，例如：请输入您的订单号，查询订单状态">
                    </el-input>
                    <div class="settings-form-hint">当客户联系时，系统将自动发送此提示语</div>
                </div>
                
                <div class="settings-form-item" v-if="settings.enableOrderQuery">
                    <div class="settings-form-label">订单查询模板</div>
                    <el-input
                        v-model="settings.orderQueryTemplate"
                        type="textarea"
                        :rows="4"
                        placeholder="订单信息展示模板，支持变量：{订单号}、{商品名称}、{订单金额}、{下单时间}等">
                    </el-input>
                    <div class="settings-form-hint">系统将根据此模板展示订单信息，可使用变量：{订单号}、{商品名称}、{订单金额}、{商品数量}、{下单时间}、{订单状态}</div>
                </div>
            </div>
            
            <div class="settings-section">
                <div class="settings-section-title">自动回复设置</div>
                <div class="settings-form-item">
                    <el-switch
                        v-model="settings.enableAutoReply"
                        active-text="启用自动回复"
                        inactive-text="禁用自动回复">
                    </el-switch>
                    <div class="settings-form-hint">启用后，系统将自动回复客户消息</div>
                </div>
                
                <div class="settings-form-item" v-if="settings.enableAutoReply">
                    <div class="settings-form-label">欢迎语</div>
                    <el-input
                        v-model="settings.welcomeMessage"
                        type="textarea"
                        :rows="2"
                        placeholder="请输入欢迎语，例如：您好，欢迎咨询，请问有什么可以帮到您？">
                    </el-input>
                    <div class="settings-form-hint">客户首次联系时，系统将自动发送此欢迎语</div>
                </div>
            </div>
            
            <div style="margin-top: 30px; text-align: center;">
                <el-button type="primary" @click="saveSettings" class="btn btn-primary">保存设置</el-button>
                <el-button @click="closeSettings" class="btn">取消</el-button>
            </div>
        </div>
        
        <div class="chat-container">
            <!-- 左侧会话列表 -->
            <div class="sidebar" :class="{ 'hidden': isMobile && currentSession }">
                <div class="status-bar">
                    <div class="status-bar-left">
                        <i class="el-icon-chat-line-round"></i>
                        <span @click="handleTitleClick" style="cursor: pointer;">< 商家消息管理系统</span>
                    </div>
                    <div class="status-bar-right" v-if="isMobile">
                        <button class="mobile-header-menu-btn" @click="toggleMobileHeaderMenu">
                            <span class="menu-text">菜单</span>
                            <i class="el-icon-s-grid"></i>
                        </button>
                    </div>
                </div>
                <div style="overflow-y: auto; flex: 1;">
                    <div v-if="sessions.length === 0" style="text-align: center; padding: 20px; color: #909399;">
                        <i class="el-icon-chat-dot-square" style="font-size: 24px; margin-bottom: 10px;"></i>
                        <p>暂无会话记录</p>
                    </div>
                    <div v-for="session in sessions" :key="session.id" 
                         :class="['session-item', { active: currentSession && currentSession.id === session.id }]"
                         @click="openSession(session.id)">
                        <div class="session-item-avatar">
                            <span>{{ session.title.substr(0, 1) }}</span>
                        </div>
                        <div class="session-item-content">
                            <div class="session-item-header">
                                <div class="session-item-title">{{ session.title }}</div>
                                <div class="session-item-time">{{ formatDate(session.last_time * 1000) }}</div>
                            </div>
                            <div class="session-item-message">{{ session.last_message }}</div>
                            <div class="session-item-footer">
                                <el-tag v-if="session.status === 'open'" size="small" type="success">进行中</el-tag>
                                <el-tag v-else size="small" type="info">已关闭</el-tag>
                                <el-badge v-if="session.unread_count > 0" :value="session.unread_count" type="danger"></el-badge>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧消息区域 -->
            <div class="main-content" v-if="currentSession">
                <div class="chat-header">
                    <button v-if="isMobile" class="back-button" @click="backToSessionList">
                        <i class="el-icon-arrow-left"></i>
                    </button>
                    <div class="header-info">
                        <div style="display: flex; align-items: center;">
                            <div class="chat-header-back">
                                <span class="back-icon">&lt;</span>
                                <span class="session-title">{{ currentSession.title }}</span>
                            </div>
                        </div>
                        <span class="session-id">ID: {{ currentSession.id }}</span>
                    </div>
                    <div class="session-status">
                        <el-tag :type="currentSession.status === 'open' ? 'success' : 'info'">
                            {{ currentSession.status === 'open' ? '进行中' : '已关闭' }}
                        </el-tag>
                    </div>
                </div>

                <div class="chat-messages" ref="messageContainer" @scroll="checkScrollPosition" @contextmenu.prevent="handleContextMenu">
                    <div v-for="(message, index) in messages" :key="index">
                        <!-- 时间分隔线 -->
                        <div v-if="showTimeDivider(message, index)" class="time-divider">
                            <span class="time-divider-content">{{ formatDateTime(message.create_time * 1000) }}</span>
                        </div>

                        <!-- 系统消息 -->
                        <div v-if="message.is_system" style="text-align: center; margin: 10px 0;">
                            <span class="system-message">{{ message.message }}</span>
                        </div>

                        <!-- 普通消息 -->
                        <div v-else :class="['message', isCustomerMessage(message) ? 'message-customer' : 'message-staff']" 
                             @contextmenu.prevent.stop="openContextMenu($event, message, index)">
                            <div v-if="isCustomerMessage(message)" class="avatar customer-avatar">
                                <span class="avatar-text">客</span>
                            </div>
                            <div class="message-content">
                                <!-- 添加发送者角色名称 - 美化后的显著样式 -->
                                <div class="message-sender-role" 
                                     :class="{
                                         'customer-tag': message.role_type === 'customer',
                                         'merchant-tag': message.role_type === 'merchant',
                                         'staff-tag': message.role_type === 'staff',
                                         'supplier-tag': message.role_type === 'supplier'
                                     }" 
                                     style="display: inline-block; padding: 3px 8px; border-radius: 4px; margin-bottom: 8px; font-weight: 500; font-size: 13px;">
                                     <i :class="{
                                         'el-icon-user': message.role_type === 'customer',
                                         'el-icon-shop': message.role_type === 'merchant',
                                         'el-icon-service': message.role_type === 'staff',
                                         'el-icon-shopping-cart-full': message.role_type === 'supplier'
                                     }"></i>
                                    {{ message.role_type === 'customer' ? '客户' : 
                                      (message.role_type === 'merchant' ? '商家' : 
                                       (message.role_type === 'supplier' ? '供货商' : '平台客服')) }}
                                </div>
                                
                                <div class="message-bubble">
                                    <!-- 文本消息 -->
                                    <div v-if="(message.message_type === 'text' || message.type === 'text') && !message.is_recalled" 
                                         class="message-text" 
                                         v-html="formatMessage(message.message || message.content)"
                                         @click="handlePresetQuestionClick($event, message)"></div>
                                    
                                    <!-- 图片消息 -->
                                    <div v-else-if="(message.message_type === 'image' || message.type === 'image') && !message.is_recalled" class="message-image-container">
                                        <img :src="message.file_url || message.content || message.message" 
                                             class="message-image" 
                                             @click="previewImage(message.file_url || message.content || message.message)" 
                                             @error="handleImageError($event, message)"
                                             @load="message.imageLoaded = true" 
                                             alt="图片消息"
                                             :style="{ display: message.imageError ? 'none' : 'block' }">
                                        <div v-if="message.imageError" class="image-error-text">
                                            <i class="el-icon-picture-outline"></i>
                                            图片加载失败
                                        </div>
                                    </div>
                                    
                                    <!-- 文件消息 -->
                                    <div v-else-if="(message.message_type === 'file' || message.type === 'file') && !message.is_recalled" class="message-file" @click="downloadFile(message.file_url || message.content)">
                                        <i class="el-icon-document" style="margin-right: 8px;"></i>
                                        <span>文件下载</span>
                                    </div>
                                    
                                    <!-- 撤回的消息 -->
                                    <div v-else-if="message.is_recalled === 1" class="message-text revoked-message">
                                        [该消息已被撤回]
                                    </div>
                                    
                                    <div class="message-footer">
                                        <div class="message-time">
                                            {{ formatTime(message.create_time * 1000) }}
                                        </div>
                                        <div v-if="!isCustomerMessage(message)" class="message-read-status">
                                            <span class="read-status" :class="getReadStatus(message)">
                                                {{ getReadStatusText(message) }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-if="!isCustomerMessage(message)" class="avatar staff-avatar">
                                <span class="avatar-text" v-if="message.role_type === 'staff'">服</span>
                                <span class="avatar-text" v-else>商</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 消息输入区域 -->
                <div class="chat-input-container" v-if="currentSession.status === 'open'">
                    <!-- 在输入框上方添加可拖放的图片上传区域 -->
                    <div class="image-upload-box" v-if="showUploadBox" @click="openImageUploadMerchant" @dragover.prevent @drop.prevent="handleImageDrop">
                        <span class="icon-svg">
                            <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                                <path d="M544 864V672h128L512 480 352 672h128v192H320v-1.6c-5.376.32-10.496 1.6-16 1.6A240 240 0 0 1 64 624c0-123.136 93.12-223.488 212.608-237.248A239.808 239.808 0 0 1 512 192a239.872 239.872 0 0 1 235.456 194.752c119.488 13.76 212.48 114.112 212.48 237.248a240 240 0 0 1-240 240c-5.376 0-10.56-1.28-16-1.6v1.6H544z"/>
                            </svg>
                        </span>
                        <p>点击上传或拖放图片到此处</p>
                    </div>

                    <!-- 添加预设回复快捷栏 -->
                    <div class="preset-reply-quickbar" v-if="quickReplies.length > 0">
                        <div 
                            v-for="(reply, index) in quickReplies" 
                            :key="index" 
                            class="preset-reply-chip"
                            @click="usePresetReply(reply)"
                            :title="reply.content">
                            {{ reply.title || reply.content.substr(0, 10) + '...' }}
                        </div>
                        <div class="preset-reply-chip" style="background-color:#f0f9eb;border-color:#e1f3d8;color:#67c23a" @click="openPresetReplies">
                            更多回复 <i class="el-icon-plus"></i>
                        </div>
                    </div>
                    
                    <textarea 
                        class="message-textarea" 
                        v-model="messageContent" 
                        placeholder="请输入消息内容... 支持粘贴图片自动发送" 
                        @keydown.enter.exact="sendMessage"
                        @paste="handlePaste"></textarea>
                    <div class="input-tips">按 Enter 发送 | 右键消息可撤回</div>
                    <div class="send-btn-container">
                        <!-- 手机端汉堡菜单按钮 -->
                        <button class="mobile-menu-toggle" @click="toggleMobileMenu">
                            <span class="icon-svg">
                                <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M904 160H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0 624H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0-312H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8z"/>
                                </svg>
                            </span>
                            菜单
                        </button>

                        <!-- 桌面端操作按钮分组 -->
                        <div class="button-group">
                            <!-- 会话接入申请按钮 -->
                            <button class="btn btn-primary join-requests-btn" @click="goToJoinRequests">
                                <span class="icon-svg">
                                    <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M832 512a192 192 0 0 0-384 0c0 35.2 10.24 67.84 26.88 96H448a192 192 0 0 0-384 0c0 35.2 10.24 67.84 26.88 96h-18.56C32.96 704 0 736.96 0 776.32V816c0 39.36 32.96 72.32 72.32 72.32h879.36c39.36 0 72.32-32.96 72.32-72.32v-39.68c0-39.36-32.96-72.32-72.32-72.32h-18.56C949.76 675.84 960 643.2 960 608a192 192 0 0 0-128-180.48V300.8c0-26.24-21.12-47.36-47.36-47.36H239.36c-26.24 0-47.36 21.12-47.36 47.36v126.72A192 192 0 0 0 64 608c0 35.2 10.24 67.84 26.88 96H72.32a24.32 24.32 0 0 0-24.32 24.32V816c0 13.44 10.88 24.32 24.32 24.32h879.36A24.32 24.32 0 0 0 976 816v-39.68a24.32 24.32 0 0 0-24.32-24.32h-26.88c16.64-28.16 26.88-60.8 26.88-96 0-35.2-10.24-67.84-26.88-96zM640 512c0-70.4 57.6-128 128-128s128 57.6 128 128-57.6 128-128 128-128-57.6-128-128zm-512 0c0-70.4 57.6-128 128-128s128 57.6 128 128-57.6 128-128 128-128-57.6-128-128zm368 128c35.2 0 64 28.8 64 64H240c0-35.2 28.8-64 64-64h192zm256 0c35.2 0 64 28.8 64 64H624c0-35.2 28.8-64 64-64h64zM240 300.8c0-8.96 7.04-16 16-16h545.28c8.96 0 16 7.04 16 16V375.68a191.36 191.36 0 0 0-40.96-4.8c-34.56 0-66.88 9.6-95.36 26.24A192 192 0 0 0 576 300.8a193.28 193.28 0 0 0-142.08 64A193.28 193.28 0 0 0 288 300.8c-12.8 0-24.96 1.92-36.48 4.8h-.64v-4.8z"/>
                                    </svg>
                                </span>
                                供货商申请
                            </button>

                            <!-- 上传图片按钮 -->
                            <button class="btn btn-info upload-btn" @click="toggleUploadBox">
                                <span class="icon-svg">
                                    <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 632H136v-39.9l138.5-164.3 150.1 178L658.1 489 888 761.6V792zm0-129.8L664.2 396.8c-3.2-3.8-9-3.8-12.2 0L424.6 666.4l-144-170.7c-3.2-3.8-9-3.8-12.2 0L136 652.7V232h752v430.2zM304 456a88 88 0 1 0 0-176 88 88 0 1 0 0 176zm0-116c15.5 0 28 12.5 28 28s-12.5 28-28 28-28-12.5-28-28 12.5-28 28-28z"/>
                                    </svg>
                                </span>
                                上传图片
                            </button>
                        </div>
                        
                        <!-- 功能按钮分组 -->
                        <div class="button-group">
                            <!-- 快捷回复按钮 -->
                            <button class="btn btn-success" @click="openPresetReplies">
                                <span class="icon-svg">
                                    <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M280 752h80c4.4 0 8-3.6 8-8V280c0-4.4-3.6-8-8-8h-80c-4.4 0-8 3.6-8 8v464c0 4.4 3.6 8 8 8zM468 752h80c4.4 0 8-3.6 8-8V280c0-4.4-3.6-8-8-8h-80c-4.4 0-8 3.6-8 8v464c0 4.4 3.6 8 8 8zM820 752h60c4.4 0 8-3.6 8-8V280c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v464c0 4.4 3.6 8 8 8zM656 752h80c4.4 0 8-3.6 8-8V280c0-4.4-3.6-8-8-8h-80c-4.4 0-8 3.6-8 8v464c0 4.4 3.6 8 8 8z"/>
                                    </svg>
                                </span>
                                快捷回复
                            </button>
                            
                            <!-- 预设问题按钮 -->
                            <button class="btn btn-success" @click="sendPresetQuestions" style="background-color: #67c23a; border-color: #67c23a;">
                                <span class="icon-svg">
                                    <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M912 192H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM104 228a56 56 0 1 0 112 0 56 56 0 1 0-112 0zm0 284a56 56 0 1 0 112 0 56 56 0 1 0-112 0zm0 284a56 56 0 1 0 112 0 56 56 0 1 0-112 0z"/>
                                    </svg>
                                </span>
                                预设问题
                            </button>
                            
                            <!-- 邮箱验证按钮 -->
                            <button class="btn btn-primary" @click="sendEmailVerification" style="background-color: #409eff; border-color: #409eff;">
                                <span class="icon-svg">
                                    <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232L512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6c20.2 15.7 48.5 15.7 68.7 0L888 271.2l27.6-21.5-39.3-50.5-42.7 32.8z"/>
                                    </svg>
                                </span>
                                发送邮箱通知客户
                            </button>
                            
                            <!-- 预设问题设置按钮 -->
                            <button class="btn btn-info" @click="openSettingsDialog">
                                <span class="icon-svg">
                                    <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"></path>
                                        <path d="M623.6 316.7C593.6 290.4 554 276 512 276s-81.6 14.5-111.6 40.7C369.2 344 352 380.7 352 420v7.6c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V420c0-44.1 43.1-80 96-80s96 35.9 96 80c0 31.1-22 59.6-56.1 72.7-21.2 8.1-39.2 22.3-52.1 40.9-13.1 19-19.9 41.8-19.9 64.9V620c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8v-22.7a48.3 48.3 0 0130.9-44.8c59-22.7 97.1-74.7 97.1-132.5.1-39.3-17.1-76-48.3-103.3zM472 732a40 40 0 1080 0 40 40 0 10-80 0z"></path>
                                    </svg>
                                </span>
                                问题设置
                            </button>
                            
                            <!-- 邀请供货商按钮 -->
                            <button class="btn btn-warning invite-merchant-btn" @click="openInviteSupplierDialog">
                                <span class="icon-svg">
                                    <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"/>
                                        <path d="M176 474h672q8 0 8 8v60q0 8-8 8H176q-8 0-8-8v-60q0-8 8-8z"/>
                                    </svg>
                                </span>
                                邀请供货商
                            </button>
                        </div>
                        
                        <!-- 发送按钮 -->
                        <button class="btn btn-primary send-btn" :disabled="!messageContent.trim() || uploadingFile" @click="sendMessage">
                            <span class="icon-svg">
                                <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M931.4 498.9L94.9 79.5c-3.4-1.7-7.3-2.1-11-1.2-8.5 2.1-13.8 10.7-11.7 19.3l86.2 352.2c1.3 5.3 5.2 9.6 10.4 11.3l147.7 50.7-147.6 50.7c-5.2 1.8-9.1 6-10.3 11.3L72.2 926.5c-.9 3.7-.5 7.6 1.2 10.9 3.9 7.9 13.5 11.1 21.5 7.2l836.5-417c3.1-1.5 5.6-4.1 7.2-7.1 3.9-8 .7-17.6-7.2-21.6zM170.8 826.3l50.3-205.6 295.2-101.3c2.3-.8 4.2-2.6 5-5 1.4-4.2-.8-8.7-5-10.2L221.1 403 171 198.2l628 314.9-628.2 313.2z"/>
                                </svg>
                            </span>
                            {{ uploadingFile ? '发送中...' : '发送' }}
                        </button>
                    </div>

                    <!-- 手机端菜单遮罩层 -->
                    <div class="mobile-menu-overlay" :class="{ active: showMobileMenu }" @click="closeMobileMenu"></div>

                    <!-- 手机端菜单 -->
                    <div class="mobile-menu" :class="{ active: showMobileMenu }">
                        <div class="mobile-menu-header">
                            <h3>功能菜单</h3>
                            <button class="mobile-menu-close" @click="closeMobileMenu">×</button>
                        </div>
                        <div class="mobile-menu-content">
                            <!-- 供货商申请 -->
                            <button class="mobile-menu-item" @click="goToJoinRequests(); closeMobileMenu()">
                                <span class="icon-svg">
                                    <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M832 512a192 192 0 0 0-384 0c0 35.2 10.24 67.84 26.88 96H448a192 192 0 0 0-384 0c0 35.2 10.24 67.84 26.88 96h-18.56C32.96 704 0 736.96 0 776.32V816c0 39.36 32.96 72.32 72.32 72.32h879.36c39.36 0 72.32-32.96 72.32-72.32v-39.68c0-39.36-32.96-72.32-72.32-72.32h-18.56C949.76 675.84 960 643.2 960 608a192 192 0 0 0-128-180.48V300.8c0-26.24-21.12-47.36-47.36-47.36H239.36c-26.24 0-47.36 21.12-47.36 47.36v126.72A192 192 0 0 0 64 608c0 35.2 10.24 67.84 26.88 96H72.32a24.32 24.32 0 0 0-24.32 24.32V816c0 13.44 10.88 24.32 24.32 24.32h879.36A24.32 24.32 0 0 0 976 816v-39.68a24.32 24.32 0 0 0-24.32-24.32h-26.88c16.64-28.16 26.88-60.8 26.88-96 0-35.2-10.24-67.84-26.88-96zM640 512c0-70.4 57.6-128 128-128s128 57.6 128 128-57.6 128-128 128-128-57.6-128-128zm-512 0c0-70.4 57.6-128 128-128s128 57.6 128 128-57.6 128-128 128-128-57.6-128-128zm368 128c35.2 0 64 28.8 64 64H240c0-35.2 28.8-64 64-64h192zm256 0c35.2 0 64 28.8 64 64H624c0-35.2 28.8-64 64-64h64zM240 300.8c0-8.96 7.04-16 16-16h545.28c8.96 0 16 7.04 16 16V375.68a191.36 191.36 0 0 0-40.96-4.8c-34.56 0-66.88 9.6-95.36 26.24A192 192 0 0 0 576 300.8a193.28 193.28 0 0 0-142.08 64A193.28 193.28 0 0 0 288 300.8c-12.8 0-24.96 1.92-36.48 4.8h-.64v-4.8z"/>
                                    </svg>
                                </span>
                                供货商申请
                            </button>

                            <!-- 上传图片 -->
                            <button class="mobile-menu-item" @click="toggleUploadBox(); closeMobileMenu()">
                                <span class="icon-svg">
                                    <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 632H136v-39.9l138.5-164.3 150.1 178L658.1 489 888 761.6V792zm0-129.8L664.2 396.8c-3.2-3.8-9-3.8-12.2 0L424.6 666.4l-144-170.7c-3.2-3.8-9-3.8-12.2 0L136 652.7V232h752v430.2zM304 456a88 88 0 1 0 0-176 88 88 0 1 0 0 176zm0-116c15.5 0 28 12.5 28 28s-12.5 28-28 28-28-12.5-28-28 12.5-28 28-28z"/>
                                    </svg>
                                </span>
                                上传图片
                            </button>

                            <!-- 快捷回复 -->
                            <button class="mobile-menu-item" @click="openPresetReplies(); closeMobileMenu()">
                                <span class="icon-svg">
                                    <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M280 752h80c4.4 0 8-3.6 8-8V280c0-4.4-3.6-8-8-8h-80c-4.4 0-8 3.6-8 8v464c0 4.4 3.6 8 8 8zM468 752h80c4.4 0 8-3.6 8-8V280c0-4.4-3.6-8-8-8h-80c-4.4 0-8 3.6-8 8v464c0 4.4 3.6 8 8 8zM820 752h60c4.4 0 8-3.6 8-8V280c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v464c0 4.4 3.6 8 8 8zM656 752h80c4.4 0 8-3.6 8-8V280c0-4.4-3.6-8-8-8h-80c-4.4 0-8 3.6-8 8v464c0 4.4 3.6 8 8 8z"/>
                                    </svg>
                                </span>
                                快捷回复
                            </button>

                            <!-- 预设问题 -->
                            <button class="mobile-menu-item" @click="sendPresetQuestions(); closeMobileMenu()">
                                <span class="icon-svg">
                                    <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M912 192H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM104 228a56 56 0 1 0 112 0 56 56 0 1 0-112 0zm0 284a56 56 0 1 0 112 0 56 56 0 1 0-112 0zm0 284a56 56 0 1 0 112 0 56 56 0 1 0-112 0z"/>
                                    </svg>
                                </span>
                                预设问题
                            </button>

                            <!-- 邮箱通知 -->
                            <button class="mobile-menu-item" @click="sendEmailVerification(); closeMobileMenu()">
                                <span class="icon-svg">
                                    <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232L512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6c20.2 15.7 48.5 15.7 68.7 0L888 271.2l27.6-21.5-39.3-50.5-42.7 32.8z"/>
                                    </svg>
                                </span>
                                发送邮箱通知客户
                            </button>

                            <!-- 问题设置 -->
                            <button class="mobile-menu-item" @click="openSettingsDialog(); closeMobileMenu()">
                                <span class="icon-svg">
                                    <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"></path>
                                        <path d="M623.6 316.7C593.6 290.4 554 276 512 276s-81.6 14.5-111.6 40.7C369.2 344 352 380.7 352 420v7.6c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V420c0-44.1 43.1-80 96-80s96 35.9 96 80c0 31.1-22 59.6-56.1 72.7-21.2 8.1-39.2 22.3-52.1 40.9-13.1 19-19.9 41.8-19.9 64.9V620c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8v-22.7a48.3 48.3 0 0130.9-44.8c59-22.7 97.1-74.7 97.1-132.5.1-39.3-17.1-76-48.3-103.3zM472 732a40 40 0 1080 0 40 40 0 10-80 0z"></path>
                                    </svg>
                                </span>
                                问题设置
                            </button>

                            <!-- 邀请供货商 -->
                            <button class="mobile-menu-item" @click="openInviteSupplierDialog(); closeMobileMenu()">
                                <span class="icon-svg">
                                    <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"/>
                                        <path d="M176 474h672q8 0 8 8v60q0 8-8 8H176q-8 0-8-8v-60q0-8 8-8z"/>
                                    </svg>
                                </span>
                                邀请供货商
                            </button>
                        </div>
                    </div>
                    <input type="file" ref="imageInput" style="display: none;" accept="image/*" @change="uploadImage">
                    <input type="file" ref="imageInputMerchant" style="display: none;" accept="image/*" @change="uploadImageMerchantApi">
                    <input type="file" ref="fileInput" style="display: none;" @change="uploadFile">
                </div>
                <div class="chat-input-container" v-else>
                    <div class="status-bar">
                        <i class="el-icon-warning"></i> 
                        该会话已关闭，无法发送消息
                    </div>
                </div>
            </div>

            <div class="main-content" v-else style="display: flex; justify-content: center; align-items: center; background-color: #f9f9f9;">
                <div class="welcome-container">
                    <div class="welcome-icon">
                        <span class="icon-svg" style="font-size: 60px; color: #409eff;">
                            <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                                <path d="M464 512a48 48 0 1 0 96 0 48 48 0 1 0-96 0zm200 0a48 48 0 1 0 96 0 48 48 0 1 0-96 0zm-400 0a48 48 0 1 0 96 0 48 48 0 1 0-96 0zm661.2-173.6c-22.6-53.7-55-101.9-96.3-143.3a444.35 444.35 0 0 0-143.3-96.3C630.6 75.7 572.2 64 512 64h-2c-60.6.3-119.3 12.3-174.5 35.9a445.35 445.35 0 0 0-142 96.5c-40.9 41.3-73 89.3-95.2 142.8-23 55.4-34.6 114.3-34.3 174.9A449.4 449.4 0 0 0 112 714v152a46 46 0 0 0 46 46h152.1A449.4 449.4 0 0 0 510 960h2.1c59.9 0 118-11.6 172.7-34.3a444.48 444.48 0 0 0 142.8-95.2c41.3-40.9 73.8-88.7 96.5-142 23.6-55.2 35.6-113.9 35.9-174.5.3-60.9-11.5-120-34.8-175.6zm-151.1 438C704 845.8 611 884 512 884h-1.7c-60.3-.3-120.2-15.3-173.1-43.5l-8.4-4.5H188V695.2l-4.5-8.4C155.3 633.9 140.3 574 140 513.7c-.4-99.7 37.7-193.3 107.6-263.8 69.8-70.5 163.1-109.5 262.8-109.9h1.7c50 0 98.5 9.7 144.2 28.9 44.6 18.7 84.6 45.6 119 80 34.3 34.3 61.3 74.4 80 119 19.4 46.2 29.1 95.2 28.9 145.8-.6 99.6-39.7 192.9-110.1 262.7z"/>
                            </svg>
                        </span>
                    </div>
                    <h2 class="welcome-title">欢迎使用商家消息管理系统</h2>
                    <p class="welcome-subtitle">请从左侧列表选择一个会话开始沟通</p>
                    <div class="welcome-features">
                        <div class="feature-item">
                            <span class="icon-svg" style="font-size: 28px; color: #409eff; margin-bottom: 10px;">
                                <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M924.3 338.4a447.57 447.57 0 0 0-96.1-143.3 443.09 443.09 0 0 0-143-96.3A443.91 443.91 0 0 0 512 64h-2c-60.5.3-119 12.3-174.1 35.9a444.08 444.08 0 0 0-141.7 96.5 445 445 0 0 0-95 142.8A449.89 449.89 0 0 0 65 514c.3 69.4 16.9 138.3 47.6 199.9v152c0 25.4 20.6 46 45.9 46h151.8a447.72 447.72 0 0 0 199.5 48h2.1c59.8 0 117.7-11.6 172.3-34.3A443.2 443.2 0 0 0 827 830.5c41.2-40.9 73.6-88.7 96.3-142 23.5-55.2 35.5-113.9 35.8-174.5.2-60.9-11.6-120-34.8-175.6zM312.4 560c-26.4 0-47.9-21.5-47.9-48s21.5-48 47.9-48 47.9 21.5 47.9 48-21.4 48-47.9 48zm199.6 0c-26.4 0-47.9-21.5-47.9-48s21.5-48 47.9-48 47.9 21.5 47.9 48-21.5 48-47.9 48zm199.6 0c-26.4 0-47.9-21.5-47.9-48s21.5-48 47.9-48 47.9 21.5 47.9 48-21.5 48-47.9 48z"/>
                                </svg>
                            </span>
                            <span>实时沟通</span>
                        </div>
                        <div class="feature-item">
                            <span class="icon-svg" style="font-size: 28px; color: #409eff; margin-bottom: 10px;">
                                <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 632H136v-39.9l138.5-164.3 150.1 178L658.1 489 888 761.6V792zm0-129.8L664.2 396.8c-3.2-3.8-9-3.8-12.2 0L424.6 666.4l-144-170.7c-3.2-3.8-9-3.8-12.2 0L136 652.7V232h752v430.2zM304 456a88 88 0 1 0 0-176 88 88 0 1 0 0 176zm0-116c15.5 0 28 12.5 28 28s-12.5 28-28 28-28-12.5-28-28 12.5-28 28-28z"/>
                                </svg>
                            </span>
                            <span>图片传输</span>
                        </div>
                        <div class="feature-item">
                            <span class="icon-svg" style="font-size: 28px; color: #409eff; margin-bottom: 10px;">
                                <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0 0 42 42h216v494zM504 618H320c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h184c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zM312 490v48c0 4.4 3.6 8 8 8h384c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H320c-4.4 0-8 3.6-8 8z"/>
                                </svg>
                            </span>
                            <span>文件共享</span>
                        </div>
                    </div>
                    
                    <!-- 添加供货商申请按钮到欢迎界面 -->
                    <div class="welcome-buttons" style="margin-top: 30px; display: flex; justify-content: center; flex-wrap: wrap; gap: 10px;">
                        <button class="btn btn-primary" style="display: inline-flex;" @click="goToJoinRequests">
                            <span class="icon-svg">
                                <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M832 512a192 192 0 0 0-384 0c0 35.2 10.24 67.84 26.88 96H448a192 192 0 0 0-384 0c0 35.2 10.24 67.84 26.88 96h-18.56C32.96 704 0 736.96 0 776.32V816c0 39.36 32.96 72.32 72.32 72.32h879.36c39.36 0 72.32-32.96 72.32-72.32v-39.68c0-39.36-32.96-72.32-72.32-72.32h-18.56C949.76 675.84 960 643.2 960 608a192 192 0 0 0-128-180.48V300.8c0-26.24-21.12-47.36-47.36-47.36H239.36c-26.24 0-47.36 21.12-47.36 47.36v126.72A192 192 0 0 0 64 608c0 35.2 10.24 67.84 26.88 96H72.32a24.32 24.32 0 0 0-24.32 24.32V816c0 13.44 10.88 24.32 24.32 24.32h879.36A24.32 24.32 0 0 0 976 816v-39.68a24.32 24.32 0 0 0-24.32-24.32h-26.88c16.64-28.16 26.88-60.8 26.88-96 0-35.2-10.24-67.84-26.88-96zM640 512c0-70.4 57.6-128 128-128s128 57.6 128 128-57.6 128-128 128-128-57.6-128-128zm-512 0c0-70.4 57.6-128 128-128s128 57.6 128 128-57.6 128-128 128-128-57.6-128-128zm368 128c35.2 0 64 28.8 64 64H240c0-35.2 28.8-64 64-64h192zm256 0c35.2 0 64 28.8 64 64H624c0-35.2 28.8-64 64-64h64zM240 300.8c0-8.96 7.04-16 16-16h545.28c8.96 0 16 7.04 16 16V375.68a191.36 191.36 0 0 0-40.96-4.8c-34.56 0-66.88 9.6-95.36 26.24A192 192 0 0 0 576 300.8a193.28 193.28 0 0 0-142.08 64A193.28 193.28 0 0 0 288 300.8c-12.8 0-24.96 1.92-36.48 4.8h-.64v-4.8z"/>
                                </svg>
                            </span>
                            供货商申请
                        </button>
                        
                        <!-- 新增设置按钮 -->
                        <button class="btn btn-success" style="display: inline-flex;" @click="openSettings">
                            <span class="icon-svg">
                                <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56a32.03 32.03 0 0 0 9.3-35.2l-.9-2.6a443.74 443.74 0 0 0-79.7-137.9l-1.8-2.1a32.12 32.12 0 0 0-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85a32.05 32.05 0 0 0-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5a32.05 32.05 0 0 0-25.8 25.7l-15.8 85.4a351.86 351.86 0 0 0-99 57.4l-81.9-29.1a32 32 0 0 0-35.1 9.5l-1.8 2.1a446.02 446.02 0 0 0-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5a32.03 32.03 0 0 0-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1a32.12 32.12 0 0 0 35.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4a32.05 32.05 0 0 0 25.8 25.7l2.7.5a448.27 448.27 0 0 0 159 0l2.7-.5a32.05 32.05 0 0 0 25.8-25.7l15.7-85a350 350 0 0 0 99.7-57.6l81.3 29.1a32 32 0 0 0 35.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM788.3 465.9c2.5 15.1 3.8 30.6 3.8 46.1s-1.3 31-3.8 46.1l-6.6 40.1 74.7 63.9a370.03 370.03 0 0 1-42.6 73.6L721 702.8l-31.4 25.8c-23.9 19.6-50.5 35-79.3 45.8l-38.1 14.3-17.9 97a377.5 377.5 0 0 1-85 0l-17.9-97.2-37.8-14.5c-28.5-10.8-55-26.2-78.7-45.7l-31.4-25.9-93.4 33.2c-17-22.9-31.2-47.6-42.6-73.6l75.5-64.5-6.5-40c-2.4-14.9-3.7-30.3-3.7-45.5 0-15.3 1.2-30.6 3.7-45.5l6.5-40-75.5-64.5c11.3-26.1 25.6-50.7 42.6-73.6l93.4 33.2 31.4-25.9c23.7-19.5 50.2-34.9 78.7-45.7l37.9-14.3 17.9-97.2c28.1-3.2 56.8-3.2 85 0l17.9 97 38.1 14.3c28.7 10.8 55.4 26.2 79.3 45.8l31.4 25.8 92.8-32.9c17 22.9 31.2 47.6 42.6 73.6L781.8 426l6.5 39.9zM512 326c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm79.2 255.2A111.6 111.6 0 0 1 512 614c-29.9 0-58-11.7-79.2-32.8A111.6 111.6 0 0 1 400 502c0-29.9 11.7-58 32.8-79.2C454 401.6 482.1 390 512 390c29.9 0 58 11.6 79.2 32.8A111.6 111.6 0 0 1 624 502c0 29.9-11.7 58-32.8 79.2z"/>
                                </svg>
                            </span>
                            设置
                        </button>
                        
                        <!-- 新增预设问题按钮 -->
                        <button class="btn btn-success" style="display: inline-flex;" @click="sendPresetQuestions">
                            <span class="icon-svg">
                                <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M912 192H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM104 228a56 56 0 1 0 112 0 56 56 0 1 0-112 0zm0 284a56 56 0 1 0 112 0 56 56 0 1 0-112 0zm0 284a56 56 0 1 0 112 0 56 56 0 1 0-112 0z"/>
                                </svg>
                            </span>
                            预设问题
                        </button>
                        
                        <!-- 新增问题设置按钮 -->
                        <button class="btn btn-info" style="display: inline-flex;" @click="openSettingsDialog">
                            <span class="icon-svg">
                                <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"></path>
                                    <path d="M623.6 316.7C593.6 290.4 554 276 512 276s-81.6 14.5-111.6 40.7C369.2 344 352 380.7 352 420v7.6c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V420c0-44.1 43.1-80 96-80s96 35.9 96 80c0 31.1-22 59.6-56.1 72.7-21.2 8.1-39.2 22.3-52.1 40.9-13.1 19-19.9 41.8-19.9 64.9V620c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8v-22.7a48.3 48.3 0 0130.9-44.8c59-22.7 97.1-74.7 97.1-132.5.1-39.3-17.1-76-48.3-103.3zM472 732a40 40 0 1080 0 40 40 0 10-80 0z"></path>
                                </svg>
                            </span>
                            问题设置
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图片预览 -->
        <el-image-viewer v-if="showImageViewer" :url-list="[previewUrl]" @close="showImageViewer = false"></el-image-viewer>
        
        <!-- 全局通知徽章 -->
        <div v-if="totalUnreadCount > 0" class="notification-badge">
            有 {{ totalUnreadCount }} 条未读消息
        </div>
        
        <!-- 回到底部悬浮按钮 -->
        <div v-if="showScrollButton" class="scroll-to-bottom visible" @click="scrollToBottom">
            <i class="el-icon-arrow-down"></i>
        </div>
        
        <!-- 右键菜单 -->
        <div v-if="showContextMenu" class="message-context-menu" :style="contextMenuStyle">
            <div class="menu-item danger" @click="handleRevokeMessage" v-if="canRevokeMessage">
                <span class="icon-svg">
                    <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                        <path d="M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"/>
                    </svg>
                </span>
                撤回消息
            </div>
            <div class="menu-item disabled" v-else>
                <span class="icon-svg">
                    <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                        <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"></path>
                        <path d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"/>
                    </svg>
                </span>
                消息已超过2分钟，无法撤回
            </div>
            <div class="menu-item" @click="copyMessageText">
                <span class="icon-svg">
                    <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                        <path d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z"/>
                    </svg>
                </span>
                复制内容
            </div>
            <div class="menu-item" @click="closeContextMenu">
                <span class="icon-svg">
                    <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                        <path d="M563.8 512l262.5-312.9c4.4-5.2.7-13.1-6.1-13.1h-79.8c-4.7 0-9.2 2.1-12.3 5.7L511.6 449.8 295.1 191.7c-3-3.6-7.5-5.7-12.3-5.7H203c-6.8 0-10.5 7.9-6.1 13.1L459.4 512 196.9 824.9c-4.4 5.2-.7 13.1 6.1 13.1h79.8c4.7 0 9.2-2.1 12.3-5.7l216.5-258.1 216.5 258.1c3 3.6 7.5 5.7 12.3 5.7h79.8c6.8 0 10.5-7.9 6.1-13.1L563.8 512z"/>
                    </svg>
                </span>
                取消
            </div>
        </div>

        <el-dialog title="邀请供货商" v-model="inviteDialogVisible">
            <el-tabs v-model="inviteTabActive">
                <el-tab-pane label="按供货商查找" name="merchant">
                    <el-select 
                        v-model="selectedSupplier" 
                        placeholder="选择供货商" 
                        style="width: 100%;"
                        filterable
                        remote
                        reserve-keyword
                        :remote-method="remoteSearchSupplier"
                        :loading="supplierSelectLoading">
                        <el-option v-for="supplier in filteredSuppliers" :key="supplier.id" :label="supplier.name" :value="supplier.id"></el-option>
                    </el-select>
                </el-tab-pane>
                <el-tab-pane label="按商品查找" name="goods">
                    <div style="margin-bottom: 15px;">
                        <el-input v-model="goodsNameInput" placeholder="输入商品名称" style="width: calc(100% - 90px); margin-right: 10px;"></el-input>
                        <el-button type="primary" @click="searchSupplierByGoods">搜索</el-button>
                    </div>
                    <div class="found-merchants" v-if="foundSuppliers.length > 0">
                        <p>找到以下相关供货商：</p>
                        <el-radio-group v-model="selectedFoundSupplier">
                            <el-radio v-for="supplier in foundSuppliers" :key="supplier.id" :label="supplier.id">
                                {{ supplier.name }} - {{ supplier.goods.length }}个相关商品
                            </el-radio>
                        </el-radio-group>
                    </div>
                </el-tab-pane>
            </el-tabs>
            <div class="dialog-footer" style="margin-top: 20px; text-align: right;">
                <el-button @click="inviteDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="inviteSupplier">邀请</el-button>
            </div>
        </el-dialog>

        <!-- 预设回复面板 -->
        <div class="settings-overlay" :class="{ active: showPresetReplies }" @click="closePresetReplies"></div>
        <div class="preset-replies-panel" :class="{ active: showPresetReplies }">
            <div class="preset-replies-header">
                <div class="preset-replies-title">预设回复管理</div>
                <div class="preset-replies-close" @click="closePresetReplies">
                    <span class="icon-svg">
                        <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                            <path d="M563.8 512l262.5-312.9c4.4-5.2.7-13.1-6.1-13.1h-79.8c-4.7 0-9.2 2.1-12.3 5.7L511.6 449.8 295.1 191.7c-3-3.6-7.5-5.7-12.3-5.7H203c-6.8 0-10.5 7.9-6.1 13.1L459.4 512 196.9 824.9c-4.4 5.2-.7 13.1 6.1 13.1h79.8c4.7 0 9.2-2.1 12.3-5.7l216.5-258.1 216.5 258.1c3 3.6 7.5 5.7 12.3 5.7h79.8c6.8 0 10.5-7.9 6.1-13.1L563.8 512z"/>
                        </svg>
                    </span>
                </div>
            </div>
            
            <div class="preset-reply-search">
                <el-input v-model="presetReplySearch" placeholder="搜索预设回复" prefix-icon="el-icon-search" clearable></el-input>
            </div>
            
            <div style="margin-bottom: 20px;">
                <el-button type="primary" size="small" @click="showAddPresetDialog = true">
                    <i class="el-icon-plus"></i> 添加新回复
                </el-button>
                <el-button type="success" size="small" @click="loadPresetReplies">
                    <i class="el-icon-refresh"></i> 刷新
                </el-button>
            </div>
            
            <div v-if="filteredPresetReplies.length === 0" style="text-align: center; padding: 20px; color: #909399;">
                <i class="el-icon-chat-dot-square" style="font-size: 24px; margin-bottom: 10px;"></i>
                <p>暂无预设回复</p>
            </div>
            
            <div v-for="reply in filteredPresetReplies" :key="reply.id" class="preset-reply-item">
                <span class="preset-reply-tag" v-if="reply.tags && reply.tags.length > 0">
                    {{ reply.tags.join(', ') }}
                </span>
                <span class="preset-reply-tag" v-if="reply.is_quick" style="background-color: #f0f9eb; color: #67c23a;">
                    快捷回复
                </span>
                <div class="preset-reply-title" v-if="reply.title">{{ reply.title }}</div>
                <div class="preset-reply-content">{{ reply.content }}</div>
                <div class="preset-reply-actions">
                    <el-button size="mini" type="primary" @click="usePresetReply(reply)">使用</el-button>
                    <el-button size="mini" type="warning" @click="editPresetReply(reply)">编辑</el-button>
                    <el-button size="mini" type="success" @click="toggleQuickReply(reply)">
                        {{ reply.is_quick ? '取消快捷' : '设为快捷' }}
                    </el-button>
                    <el-button size="mini" type="danger" @click="deletePresetReply(reply.id)">删除</el-button>
                </div>
            </div>
        </div>

        <!-- 添加预设回复对话框 -->
        <el-dialog title="添加预设回复" v-model="showAddPresetDialog" width="500px">
            <el-form :model="newPresetReply" label-width="100px">
                <el-form-item label="标题">
                    <el-input v-model="newPresetReply.title" placeholder="选填，用于快速识别此回复"></el-input>
                </el-form-item>
                <el-form-item label="回复内容">
                    <el-input type="textarea" v-model="newPresetReply.content" :rows="5" placeholder="请输入回复内容"></el-input>
                </el-form-item>
                <el-form-item label="标签">
                    <el-select v-model="newPresetReply.tags" multiple filterable allow-create default-first-option placeholder="可选，用于分类">
                        <el-option v-for="tag in availableTags" :key="tag" :label="tag" :value="tag"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="设为快捷回复">
                    <el-switch v-model="newPresetReply.is_quick"></el-switch>
                    <div style="font-size: 12px; color: #909399; margin-top: 5px;">
                        快捷回复将显示在输入框上方供快速选择
                    </div>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="showAddPresetDialog = false">取消</el-button>
                <el-button type="primary" @click="addPresetReply">确认添加</el-button>
            </template>
        </el-dialog>

        <!-- 编辑预设回复对话框 -->
        <el-dialog title="编辑预设回复" v-model="showEditPresetDialog" width="500px">
            <el-form :model="editingPresetReply" label-width="100px">
                <el-form-item label="标题">
                    <el-input v-model="editingPresetReply.title" placeholder="选填，用于快速识别此回复"></el-input>
                </el-form-item>
                <el-form-item label="回复内容">
                    <el-input type="textarea" v-model="editingPresetReply.content" :rows="5" placeholder="请输入回复内容"></el-input>
                </el-form-item>
                <el-form-item label="标签">
                    <el-select v-model="editingPresetReply.tags" multiple filterable allow-create default-first-option placeholder="可选，用于分类">
                        <el-option v-for="tag in availableTags" :key="tag" :label="tag" :value="tag"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="设为快捷回复">
                    <el-switch v-model="editingPresetReply.is_quick"></el-switch>
                    <div style="font-size: 12px; color: #909399; margin-top: 5px;">
                        快捷回复将显示在输入框上方供快速选择
                    </div>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="showEditPresetDialog = false">取消</el-button>
                <el-button type="primary" @click="updatePresetReply">保存修改</el-button>
            </template>
        </el-dialog>

        <!-- 添加预设问题设置对话框 -->
        <div class="settings-dialog" v-if="showSettingsDialog">
            <div class="settings-dialog-backdrop" @click="showSettingsDialog = false"></div>
            <div class="settings-dialog-content">
                <div class="settings-dialog-header">
                    <h3>预设问题设置</h3>
                    <button class="close-btn" @click="showSettingsDialog = false">
                        <i class="el-icon"><el-icon><Close /></el-icon></i>
                    </button>
                </div>
                <div class="settings-dialog-body">
                    <div class="form-group">
                        <label class="switch-label">
                            <span>自动发送预设问题</span>
                            <el-switch 
                                v-model="presetConfig.autoSendPresetQuestions"
                                :active-value="true"
                                :inactive-value="false">
                            </el-switch>
                        </label>
                        <div class="help-text">当客户连接商家且之前没有发送过预设问题时，自动发送预设问题</div>
                    </div>
                    
                    <div class="form-group">
                        <div class="input-group">
                            <label>标题</label>
                            <input type="text" v-model="presetConfig.presetQuestions.title" class="form-control" placeholder="输入预设问题标题">
                            <div class="help-text">显示在预设问题的顶部</div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <div class="input-group">
                            <label>描述</label>
                            <textarea v-model="presetConfig.presetQuestions.description" class="form-control" rows="3" placeholder="输入预设问题描述"></textarea>
                            <div class="help-text">向客户解释预设问题的用途</div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>预设问题列表</label>
                        <div class="question-list">
                            <div v-for="(item, index) in presetConfig.presetQuestions.questions" :key="index" class="question-item">
                                <div class="item-header">
                                    <div class="item-number">{{ index + 1 }}</div>
                                    <label>问题 #{{ index + 1 }}</label>
                                </div>
                                
                                <div class="question-actions">
                                    <button class="btn btn-sm btn-danger" @click="removeQuestion(index)" title="删除问题">
                                        <span class="icon-svg">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                                                <path fill="currentColor" d="M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"></path>
                                            </svg>
                                        </span>
                                        删除
                                    </button>
                                </div>
                                
                                <div class="input-group">
                                    <label>问题内容</label>
                                    <input type="text" v-model="item.question" class="form-control" placeholder="输入预设问题">
                                </div>
                                
                                <div class="input-group" style="margin-top: 12px;">
                                    <label>回复内容</label>
                                    <div class="reply-type-selector">
                                        <label class="reply-type" :class="{'active': item.enable_text !== false}">
                                            <input type="checkbox" v-model="item.enable_text" :value="true" checked>
                                            <span>文本</span>
                                        </label>
                                        <label class="reply-type" :class="{'active': item.enable_image}">
                                            <input type="checkbox" v-model="item.enable_image" :value="true">
                                            <span>图片</span>
                                        </label>
                                        <label class="reply-type" :class="{'active': item.enable_file}">
                                            <input type="checkbox" v-model="item.enable_file" :value="true">
                                            <span>文件</span>
                                        </label>
                                    </div>

                                    <!-- 文本类型输入框 -->
                                    <div v-if="item.enable_text !== false" class="content-section">
                                        <label class="content-label">文本内容</label>
                                        <textarea v-model="item.answer" class="form-control" rows="2" placeholder="输入当客户点击该问题时自动发送的文本内容"></textarea>
                                    </div>

                                    <!-- 图片类型上传 -->
                                    <div v-if="item.enable_image" class="content-section">
                                        <label class="content-label">图片内容</label>
                                        <div class="media-upload-container">
                                            <div v-if="item.image_url" class="image-preview">
                                                <img :src="item.image_url" class="preview-image" />
                                                <button type="button" class="remove-media" @click="item.image_url = ''">
                                                    <i class="el-icon"><el-icon><Delete /></el-icon></i>
                                                </button>
                                            </div>
                                            <div v-else class="upload-placeholder" @click="openMediaUpload(index, 'image')">
                                                <i class="el-icon"><el-icon><Upload /></el-icon></i>
                                                <span>点击上传图片</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 文件类型上传 -->
                                    <div v-if="item.enable_file" class="content-section">
                                        <label class="content-label">文件内容</label>
                                        <div class="media-upload-container">
                                            <div v-if="item.file_url" class="file-preview">
                                                <div class="file-info">
                                                    <i class="el-icon"><el-icon><Document /></el-icon></i>
                                                    <span>{{ item.file_name || '已上传文件' }}</span>
                                                </div>
                                                <button type="button" class="remove-media" @click="item.file_url = ''; item.file_name = ''">
                                                    <i class="el-icon"><el-icon><Delete /></el-icon></i>
                                                </button>
                                            </div>
                                            <div v-else class="upload-placeholder" @click="openMediaUpload(index, 'file')">
                                                <i class="el-icon"><el-icon><Upload /></el-icon></i>
                                                <span>点击上传文件</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-center" style="margin-top: 16px;">
                            <button class="btn btn-success" @click="addQuestion">
                                <span class="icon-svg">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                                        <path fill="currentColor" d="M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 0 1 0 64H544v352a32 32 0 0 1-64 0V544H128a32 32 0 0 1 0-64h352z"></path>
                                    </svg>
                                </span>
                                添加问题
                            </button>
                        </div>
                    </div>
                </div>
                <div class="settings-dialog-footer">
                    <button class="btn btn-secondary" @click="showSettingsDialog = false">取消</button>
                    <button class="btn btn-primary" @click="savePresetQuestionsConfig">保存设置</button>
                </div>
            </div>
        </div>

        <!-- 隐藏的文件上传输入 -->
        <input type="file" id="media-upload-input" ref="mediaUploadInput" style="display: none;" @change="handleMediaUpload" />
    </div>

    <script>
        const app = Vue.createApp({
            data() {
                return {
                    sessions: [],
                    currentSession: null,
                    messages: [],
                    pollingInterval: null,
                    lastMessageId: 0,
                    showImageViewer: false,
                    previewUrl: '',
                    totalUnreadCount: 0,
                    messageContent: '',
                    uploadingFile: false,
                    userId: 0,
                    isCurrentUserMerchant: false, // 添加该变量：当前用户是否为商家
                    tableStructureChecked: false,

                    isMobile: false, // 添加移动端判断
                    showMobileMenu: false, // 移动端菜单显示状态
                    showMobileHeaderMenu: false, // 移动端头部菜单显示状态
                    showScrollButton: false,
                    // 右键菜单相关数据
                    showContextMenu: false,
                    contextMenuStyle: {
                        top: '0px',
                        left: '0px'
                    },
                    selectedMessage: null,
                    selectedMessageIndex: -1,
                    canRevokeMessage: false,
                    // 保存消息ID，避免引用问题
                    currentMessageId: null,
                    // 设置相关
                    showSettings: false,
                    showUploadBox: false, // 新增: 是否显示上传区域
                    settings: {
                        allowSupplier: false,
                        enableOrderQuery: false,
                        orderQueryPrompt: '请输入您的订单号，我们将为您查询订单信息',
                        orderQueryTemplate: '订单号：{订单号}\n商品名称：{商品名称}\n商品数量：{商品数量}\n订单金额：{订单金额}\n下单时间：{下单时间}\n订单状态：{订单状态}',
                        enableAutoReply: false,
                        welcomeMessage: '您好，欢迎咨询，请问有什么可以帮到您？'
                    },
                    // 记录已响应的订单消息ID
                    respondedOrderIds: [],
                    // 标记当前是否在处理订单回复
                    isProcessingOrderReply: false,
                    // 添加标志，防止循环发送
                    orderQueryResponseLock: false,
                    // 新增：记录最近查询过的订单号，避免重复查询
                    recentQueriedOrders: {},
                    pendingJoinRequests: 0,
                    isSupplier: false,
                    inviteDialogVisible: false,
                    selectedSupplier: null,
                    availableSuppliers: [],
                    // 会话参与者已移除
                
                    inviteTabActive: 'merchant',
                    goodsNameInput: '',
                    foundSuppliers: [],
                    selectedFoundSupplier: null,
                    supplierSelectLoading: false,
                    filteredSuppliers: [],
                    
                    // 预设回复相关数据
                    showPresetReplies: false,
                    presetReplies: [],
                    presetReplySearch: '',
                    showAddPresetDialog: false,
                    showEditPresetDialog: false, // 新增：编辑对话框显示状态
                    editingPresetReply: { // 新增：当前正在编辑的预设回复
                        id: null,
                        title: '',
                        content: '',
                        tags: [],
                        is_quick: false
                    },
                    newPresetReply: {
                        title: '',
                        content: '',
                        tags: [],
                        is_quick: false
                    },
                    availableTags: ['订单', '发货', '售后', '投诉', '退款', '咨询'],
                    quickReplies: [],
                    showSettingsDialog: false,
                    presetConfig: {
                        autoSendPresetQuestions: true,
                        presetQuestions: {
                            title: 'Spikees云寄售',
                            description: 'Spikees云寄售欢迎您~~\n有什么问题您下方留言\n如果下面有您想问的问题点击即可解答！',
                            questions: [
                                {
                                    question: '1. 平台安全吗',
                                    answer: '是的，我们平台非常安全，所有交易都有平台担保，请放心使用。',
                                    enable_text: true,
                                    enable_image: false,
                                    enable_file: false,
                                    image_url: '',
                                    file_url: '',
                                    file_name: ''
                                },
                                {
                                    question: '2. 如何注册商家？',
                                    answer: '商家注册需要完成实名认证，然后在用户中心点击"成为商家"，按步骤填写资料并等待审核。',
                                    enable_text: true,
                                    enable_image: false,
                                    enable_file: false,
                                    image_url: '',
                                    file_url: '',
                                    file_name: ''
                                },
                                {
                                    question: '3. 投诉订单什么时候到账？',
                                    answer: '投诉受理后，订单资金会在3-7个工作日内退回到您的账户。',
                                    enable_text: true,
                                    enable_image: false,
                                    enable_file: false,
                                    image_url: '',
                                    file_url: '',
                                    file_name: ''
                                }
                            ]
                        }
                    },
                    // 用于媒体上传的临时变量
                    currentUploadingIndex: -1,
                    currentUploadingType: 'text',
                };
            },
            mounted() {

                
                // 尝试获取当前用户ID
                if (window.userInfo && window.userInfo.id) {
                    this.userId = window.userInfo.id;
                } else {
                    // 尝试从页面中获取userId
                    try {
                        const userInfoData = document.getElementById('userInfoData');
                        if (userInfoData) {
                            const userInfo = JSON.parse(userInfoData.textContent);
                            if (userInfo && userInfo.id) {
                                this.userId = userInfo.id;
                            }
                        }
                    } catch (e) {

                    }
                }

                
                // 检查当前用户是否为商家
                this.checkCurrentUserIsMerchant();
                
                // 检查是否从申请页面跳转过来
                const fromJoinRequest = localStorage.getItem('fromJoinRequest');
                if (fromJoinRequest === 'true') {
                    // 清除标记
                    localStorage.removeItem('fromJoinRequest');
                    
                    // 立即加载会话列表并选中新加入的会话
                    this.loadSessions(true);
                } else {
                    this.loadSessions();
                }
                
                // 加载设置
                this.loadSettings();

                // 每15秒刷新会话列表
                setInterval(() => {
                    this.loadSessions();
                }, 15000);
                
                // 添加浏览器通知权限请求
                this.requestNotificationPermission();
                
                // 添加标题闪烁提醒
                this.originalTitle = document.title;

                axios.defaults.baseURL = window.location.origin;
                
                // 检查表结构
                this.checkTableStructure();

                // 添加移动端判断
                this.isMobile = window.innerWidth <= 768;
                
                // 监听窗口大小变化
                window.addEventListener('resize', this.handleResize);
                
                // 移动端添加滑动手势支持
                if (this.isMobile) {
                    this.setupTouchEvents();
                }
                
                // 添加全局点击事件，用于关闭右键菜单和头部菜单
                document.addEventListener('click', this.closeContextMenu);
                document.addEventListener('click', (e) => {
                    // 如果点击的不是头部菜单按钮或菜单内容，则关闭头部菜单
                    if (!e.target.closest('.mobile-header-menu-btn') &&
                        !e.target.closest('.mobile-header-menu') &&
                        this.showMobileHeaderMenu) {
                        this.closeMobileHeaderMenu();
                    }
                });
                
                // 添加键盘事件监听
                document.addEventListener('keydown', this.handleKeyDown);

                // 检查当前用户是否是供应商，并加载待处理的接入申请数量
                this.checkSupplierStatus();

                // 加载预设回复
                this.loadPresetReplies();
            },
            beforeUnmount() {
                // 清理事件监听
                window.removeEventListener('resize', this.handleResize);
                document.removeEventListener('click', this.closeContextMenu);
                document.removeEventListener('keydown', this.handleKeyDown);
            },
            computed: {
                // 过滤预设回复列表
                filteredPresetReplies() {
                    if (!this.presetReplySearch) {
                        return this.presetReplies;
                    }
                    const search = this.presetReplySearch.toLowerCase();
                    return this.presetReplies.filter(reply => {
                        // 根据标题、内容和标签搜索
                        const matchTitle = reply.title && reply.title.toLowerCase().includes(search);
                        const matchContent = reply.content && reply.content.toLowerCase().includes(search);
                        const matchTags = reply.tags && Array.isArray(reply.tags) && 
                            reply.tags.some(tag => tag.toLowerCase().includes(search));
                        return matchTitle || matchContent || matchTags;
                    });
                }
            },
            methods: {
                // 移动端菜单控制方法
                toggleMobileMenu() {
                    this.showMobileMenu = !this.showMobileMenu;
                },
                closeMobileMenu() {
                    this.showMobileMenu = false;
                },

                // 移动端头部菜单控制方法
                toggleMobileHeaderMenu() {
                    this.showMobileHeaderMenu = !this.showMobileHeaderMenu;
                },
                closeMobileHeaderMenu() {
                    this.showMobileHeaderMenu = false;
                },

                // 头部菜单导航方法
                goToWorkplace() {
                    window.location.href = '/merchant/dashboard/workplace';
                },
                goToSettings() {
                    window.location.href = '/merchant/dashboard/settings';
                },
                goToOrders() {
                    window.location.href = '/merchant/dashboard/orders';
                },
                goToProducts() {
                    window.location.href = '/merchant/dashboard/products';
                },

                // 处理标题点击事件 - 根据设备类型跳转不同页面
                handleTitleClick() {
                    if (this.isMobile) {
                        // 手机端跳转到 /merchant/dashboard
                        window.location.href = '/merchant/dashboard';
                    } else {
                        // 电脑端跳转到 /merchant/dashboard/workplace
                        window.location.href = '/merchant/dashboard/workplace';
                    }
                },



                loadSessions(fromJoinRequest = false) {

                    const url = '/plugin/Customersystem/user/getSessionList';

                    axios.get(url)
                        .then(response => {

                            if (response.data.code === 200) {
                                // 兼容处理不同的数据结构
                                if (response.data.data && response.data.data.data) {
                                    this.sessions = response.data.data.data;
                                } else if (Array.isArray(response.data.data)) {
                                    this.sessions = response.data.data;
                                } else if (response.data.data) {
                                    this.sessions = [response.data.data];
                                } else {
                                    this.sessions = [];
                                }
                                
                                // 确保会话列表中的每个会话都有必要的属性
                                this.sessions = this.sessions.map(session => {
                                    return {
                                        id: session.id,
                                        title: session.title || '未命名会话',
                                        last_message: session.last_message || '',
                                        last_time: session.last_time || new Date().getTime() / 1000,
                                        unread_count: session.unread_count || 0,
                                        status: session.status || 'open'
                                    };
                                });
                                

                                
                                // 计算总未读消息数
                                this.totalUnreadCount = this.sessions.reduce((sum, session) => {
                                    return sum + (session.unread_count || 0);
                                }, 0);
                                
                                // 如果有未读消息，启动标题闪烁
                                if (this.totalUnreadCount > 0) {
                                    this.startTitleFlashing();
                                } else {
                                    this.stopTitleFlashing();
                                }
                                
                                // 如果当前有打开的会话，更新它的状态
                                if (this.currentSession) {
                                    const updatedSession = this.sessions.find(s => s.id === this.currentSession.id);
                                    if (updatedSession) {
                                        this.currentSession = updatedSession;
                                    }
                                }
                                
                                // 如果是从申请页面跳转过来，尝试打开新加入的会话
                                if (fromJoinRequest) {
                                    const joinedSessionId = localStorage.getItem('joinedSessionId');
                                    localStorage.removeItem('joinedSessionId');
                                    
                                    // 获取URL参数
                                    const urlParams = new URLSearchParams(window.location.search);
                                    const from = urlParams.get('from');
                                    
                                    // 如果确实是从加入申请页面过来的
                                    if (from === 'joinRequests') {
                                        // 延迟一点再打开会话，确保列表已渲染
                                        setTimeout(() => {
                                            // 如果有特定的会话ID，打开它
                                            if (joinedSessionId) {
                                                this.openSession(parseInt(joinedSessionId));
                                            } 
                                            // 否则打开最新的会话
                                            else if (this.sessions.length > 0) {
                                                // 按最后活动时间排序会话
                                                const sortedSessions = [...this.sessions].sort((a, b) => b.last_time - a.last_time);
                                                this.openSession(sortedSessions[0].id);
                                            }
                                        }, 300);
                                    }
                                }
                            } else {
                                ElementPlus.ElMessage.error(response.data.msg || '获取会话列表失败');

                            }
                        })
                        .catch(error => {
                            ElementPlus.ElMessage.error('网络错误，请稍后重试');

                        });
                },
                openSession(sessionId) {

                    // 清除现有的轮询
                    if (this.pollingInterval) {
                        clearInterval(this.pollingInterval);
                    }

                    axios.get('/plugin/Customersystem/user/getSessionDetail', {
                        params: { id: sessionId }
                    })
                    .then(response => {

                        if (response.data.code === 200) {
                            // 确保会话对象有必要的属性
                            const session = response.data.data.session || {};
                            this.currentSession = {
                                id: session.id,
                                title: session.title || '未命名会话',
                                status: session.status || 'open',
                                last_message: session.last_message || '',
                                last_time: session.last_time || new Date().getTime() / 1000,
                                unread_count: session.unread_count || 0
                            };
                            
                            // 确保消息列表格式正确
                            const messages = response.data.data.messages || [];
                            this.messages = messages.map(msg => {
                                // 对于已撤回的消息，清除所有可能的图片URL
                                if (msg.is_recalled === 1) {
                                    msg.file_url = '';
                                    if (msg.hasOwnProperty('content')) {
                                        msg.content = '';
                                    }
                                    // 保留message字段作为撤回提示
                                    if (msg.message !== '[该消息已被撤回]') {
                                        msg.message = '[该消息已被撤回]';
                                    }
                                }
                                
                                // 创建一个标准消息对象，确保所有属性都存在
                                const standardMessage = {
                                    id: msg.id,
                                    session_id: msg.session_id,
                                    sender_type: msg.sender_type || 'system',
                                    sender_id: msg.sender_id || 0,
                                    role_type: msg.role_type || (msg.sender_type === 'customer' ? 'customer' : 'merchant'),
                                    message_type: msg.message_type || msg.type || 'text',
                                    message: msg.message || msg.content || '',
                                    file_url: msg.file_url || '',
                                    file_name: msg.file_name || '',
                                    is_read: msg.is_read || 0,
                                    is_system: msg.is_system || 0,
                                    create_time: msg.create_time || new Date().getTime() / 1000,
                                    imageLoaded: false,
                                    imageError: false,
                                    is_recalled: msg.is_recalled || 0
                                };
                                
                                return standardMessage;
                            });
                            

                            
                            // 更新会话已读状态
                            this.updateSessionUnreadCount(sessionId);
                            
                            if (this.messages.length > 0) {
                                this.lastMessageId = Math.max(...this.messages.map(m => m.id));
                            } else {
                                this.lastMessageId = 0;
                            }
                            
                            this.$nextTick(() => {
                                this.scrollToBottom();
                                // 自动标记客户消息为已读
                                this.handleMessagesRead();
                            });
                            
                            // 开始轮询新消息
                            this.startPolling(sessionId);
                            
                            // 移动端处理：在选择会话后隐藏会话列表
                            if (this.isMobile) {
                                document.querySelector('.sidebar').classList.add('hidden');
                            }
                            
                            // 检查是否需要自动发送订单查询提示或欢迎语
                            this.checkAutoSendMessages(this.messages);
                        } else {
                            ElementPlus.ElMessage.error(response.data.msg || '获取会话详情失败');

                        }
                    })
                    .catch(error => {
                        ElementPlus.ElMessage.error('网络错误，请稍后重试');

                    });
                },
                startPolling(sessionId) {
                    this.pollingInterval = setInterval(() => {
                        // 同时检查新消息和已读状态变化
                        axios.get('/plugin/Customersystem/user/getSessionDetail', {
                            params: {
                                id: sessionId
                            }
                        })
                        .then(response => {
                            if (response.data.code === 200) {
                                const allMessages = response.data.data.messages || [];
                                const currentMessageCount = this.messages.length;

                                // 检查是否有新消息或已读状态变化
                                let hasUpdates = false;
                                let hasNewMessages = false;

                                // 检查新消息
                                if (allMessages.length > currentMessageCount) {
                                    hasUpdates = true;
                                    hasNewMessages = true;
                                } else {
                                    // 检查已读状态是否有变化
                                    for (let i = 0; i < this.messages.length; i++) {
                                        const currentMsg = this.messages[i];
                                        const serverMsg = allMessages.find(msg => msg.id === currentMsg.id);

                                        if (serverMsg) {
                                            // 检查已读状态字段是否有变化
                                            if (currentMsg.customer_read !== serverMsg.customer_read ||
                                                currentMsg.merchant_read !== serverMsg.merchant_read ||
                                                currentMsg.staff_read !== serverMsg.staff_read ||
                                                currentMsg.is_read !== serverMsg.is_read) {
                                                hasUpdates = true;
                                                break;
                                            }
                                        }
                                    }
                                }

                                if (hasUpdates) {
                                    // 获取新消息（如果有的话）
                                    const newMessages = allMessages.slice(currentMessageCount);

                                    // 更新整个消息列表以反映已读状态变化
                                    this.messages = allMessages.map(msg => {
                                        // 对于已撤回的消息，清除所有可能的图片URL
                                        if (msg.is_recalled === 1) {
                                            msg.file_url = '';
                                            if (msg.hasOwnProperty('content')) {
                                                msg.content = '';
                                            }
                                            // 保留message字段作为撤回提示
                                            if (msg.message !== '[该消息已被撤回]') {
                                                msg.message = '[该消息已被撤回]';
                                            }
                                        }
                                        
                                        // 创建一个标准消息对象，确保所有属性都存在
                                        const standardMessage = {
                                            id: msg.id,
                                            session_id: msg.session_id,
                                            sender_type: msg.sender_type || 'system',
                                            sender_id: msg.sender_id || 0,
                                            role_type: msg.role_type || (msg.sender_type === 'customer' ? 'customer' : 'merchant'),
                                            message_type: msg.message_type || msg.type || 'text',
                                            message: msg.message || msg.content || '',
                                            file_url: msg.file_url || '',
                                            file_name: msg.file_name || '',
                                            is_read: msg.is_read || 0,
                                            is_system: msg.is_system || 0,
                                            create_time: msg.create_time || new Date().getTime() / 1000,
                                            imageLoaded: false,
                                            imageError: false,
                                            is_recalled: msg.is_recalled || 0,
                                            // 添加已读状态字段
                                            customer_read: msg.customer_read || 0,
                                            merchant_read: msg.merchant_read || 0,
                                            staff_read: msg.staff_read || 0
                                        };

                                        return standardMessage;
                                    });

                                    // 更新最后消息ID
                                    if (allMessages.length > 0) {
                                        this.lastMessageId = Math.max(...allMessages.map(m => m.id));
                                    }

                                    // 如果有新消息才滚动到底部
                                    if (hasNewMessages) {
                                        this.$nextTick(() => {
                                            this.scrollToBottom();
                                        });

                                        // 如果有新的客户消息，播放提示音并立即检查订单号
                                        const customerTextMessages = newMessages.filter(msg =>
                                            msg.sender_type === 'customer' &&
                                            (msg.message_type === 'text' || msg.type === 'text') &&
                                            !msg.is_recalled);

                                        if (customerTextMessages.length > 0) {
                                            this.playNotificationSound();
                                            this.showBrowserNotification("新消息", "您收到了新的客户消息");

                                            // 自动标记新收到的客户消息为已读
                                            this.handleNewMessagesRead(customerTextMessages);

                                            // 检查客户消息是否包含订单号，自动处理（增强这部分逻辑）
                                            if (this.settings.enableOrderQuery && !this.orderQueryResponseLock) {
                                                // 立即处理可能的订单查询，不再等待
                                                this.processOrderQuery(customerTextMessages);
                                            }
                                        }
                                    }
                                }
                                
                                // 检查现有消息是否有被撤回的
                                if (response.data.data.recalled_messages && response.data.data.recalled_messages.length > 0) {
                                    response.data.data.recalled_messages.forEach(recalledId => {
                                        const index = this.messages.findIndex(m => m.id === recalledId);
                                        if (index !== -1) {
                                            // 更新消息为已撤回状态
                                            this.messages[index].is_recalled = 1;
                                            this.messages[index].message = '[该消息已被撤回]';
                                            this.messages[index].file_url = '';
                                            this.messages[index].content = '';
                                            
                                            // 确保不会尝试加载图片
                                            if ((this.messages[index].hasOwnProperty('message_type') && this.messages[index].message_type === 'image') || 
                                                (this.messages[index].hasOwnProperty('type') && this.messages[index].type === 'image')) {
                                                this.messages[index].imageLoaded = true;
                                                this.messages[index].imageError = false;
                                            }
                                        }
                                    });
                                }
                                
                                // 更新会话状态
                                if (response.data.data.session_status) {
                                    this.currentSession.status = response.data.data.session_status;
                                }
                            }
                        })
                        .catch(error => {

                        });
                    }, 2000); // 减少轮询间隔，从3秒改为2秒
                },
                
                // 新增方法：专门处理订单查询逻辑
                processOrderQuery(customerMessages) {
                    // 遍历所有客户消息，检查是否包含订单号格式
                    for (const msg of customerMessages) {
                        // 获取消息内容
                        const content = msg.message || msg.content || '';
                        if (!content) continue;
                        
                        // 如果已经响应过该消息，跳过
                        if (this.respondedOrderIds && this.respondedOrderIds.includes(msg.id)) {

                            continue;
                        }
                        

                        
                        // 先尝试直接匹配"订单号:xxx"格式
                        let orderNumber = null;
                        let shouldRespond = false;
                        
                        // 1. 尝试匹配标准的"订单号:xxx"格式
                        const orderNoPattern = /订单号[:|：]\s*([a-zA-Z0-9-]{4,32})/i;
                        const match = content.match(orderNoPattern);
                        
                        if (match && match[1]) {
                            orderNumber = match[1];
                            shouldRespond = true;

                        }
                        // 仅支持严格的"订单号:xxx"格式，不再支持其他匹配方式
                        
                        // 如果识别到订单号，自动查询并回复
                        if (shouldRespond && orderNumber) {
                            // 检查是否最近查询过此订单号（5分钟内）
                            const now = Date.now();
                            if (this.recentQueriedOrders[orderNumber] && 
                                (now - this.recentQueriedOrders[orderNumber]) < 300000) {

                                continue;
                            }
                            
                            // 记录此订单号查询时间
                            this.recentQueriedOrders[orderNumber] = now;
                            
                            // 设置锁，防止重复和循环处理
                            this.orderQueryResponseLock = true;
                            
                            // 记录已响应的消息ID
                            if (!this.respondedOrderIds) {
                                this.respondedOrderIds = [];
                            }
                            this.respondedOrderIds.push(msg.id);
                            

                            
                            // 直接调用后端API查询订单 - 移除不必要的延迟
                            axios.post('/plugin/Customersystem/user/queryOrder', {
                                trade_no: orderNumber
                            })
                            .then(response => {
                                if (response.data.code === 200 && response.data.data) {
                                    // 订单信息
                                    const orderInfo = response.data.data;
                                    
                                    // 使用模板生成回复内容
                                    let replyMessage = this.settings.orderQueryTemplate;
                                    
                                    // 定义字段映射和默认值
                                    const fields = {
                                        '订单号': orderInfo.trade_no || orderNumber,
                                        '商品名称': orderInfo.goods_name || '商品详情',
                                        '商品数量': orderInfo.quantity || '1',
                                        '订单金额': orderInfo.total_amount || '0.00',
                                        '下单时间': '',
                                        '订单状态': orderInfo.status || '处理中'
                                    };
                                    
                                    // 格式化订单时间
                                    if (orderInfo.create_time) {
                                        const date = new Date(orderInfo.create_time * 1000);
                                        fields['下单时间'] = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
                                    } else {
                                        fields['下单时间'] = '未知';
                                    }
                                    
                                    // 订单状态转换
                                    let statusText = '未知';
                                    if (typeof orderInfo.status === 'string' && 
                                        ['未支付', '已支付', '已关闭', '已退款', '处理中'].includes(orderInfo.status)) {
                                        // 如果status已经是有效的状态文本，直接使用
                                        statusText = orderInfo.status;
                                    } else {
                                        // 尝试将status解析为数字并转换
                                        const status = parseInt(orderInfo.status);
                                        if (!isNaN(status)) {
                                            switch (status) {
                                                case 0: statusText = '未支付'; break;
                                                case 1: statusText = '已支付'; break;
                                                case 2: statusText = '已关闭'; break;
                                                case 3: statusText = '已退款'; break;
                                                default: statusText = '处理中'; break;
                                            }
                                        } else if (orderInfo.status_text) {
                                            // 如果提供了status_text，使用它
                                            statusText = orderInfo.status_text;
                                        } else {
                                            // 默认显示为处理中
                                            statusText = '处理中';
                                        }
                                    }
                                    fields['订单状态'] = statusText;
                                    
                                    // 替换所有变量
                                    for (const [key, value] of Object.entries(fields)) {
                                        const pattern = new RegExp(`{${key}}`, 'g');
                                        replyMessage = replyMessage.replace(pattern, value);
                                    }
                                    
                                    // 处理未替换的变量
                                    const unreplacedVars = replyMessage.match(/{[^{}]+}/g);
                                    if (unreplacedVars) {
                                        unreplacedVars.forEach(variable => {
                                            replyMessage = replyMessage.replace(new RegExp(variable, 'g'), '');
                                        });
                                    }
                                    

                                    
                                    // 自动发送订单查询结果 - 直接使用sendMessageToServer
                                    this.sendMessageToServer(replyMessage);
                                    
                                    // 检查是否开启供货商功能并存在供货商信息
                                    if (orderInfo.supplier_info && this.settings.allowSupplier) {
                                        // 延迟1秒，在订单结果后询问用户是否联系供货商
                                        setTimeout(() => {
                                            const supplierInfo = orderInfo.supplier_info;
                                            const supplierMessage = `系统检测到该商品的供货商是"${supplierInfo.nickname || supplierInfo.username}"，您需要联系这位供货商吗？`;
                                            
                                            // 发送供货商询问消息
                                            this.sendMessageToServer(supplierMessage);
                                            
                                            // 添加"是"和"否"的预设回复选项
                                            setTimeout(() => {
                                                // 构建预设问题HTML
                                                const presetHtml = `
                                                    <div class="preset-questions">
                                                        <div class="preset-question-item" data-action="contact-supplier" data-supplier-id="${supplierInfo.user_id}">是的，请联系供货商</div>
                                                        <div class="preset-question-item" data-action="no-contact">不需要，谢谢</div>
                                                    </div>
                                                `;
                                                
                                                // 发送预设问题
                                                this.sendMessageToServer(presetHtml);
                                            }, 500);
                                        }, 1000);
                                    }
                                } else {
                                    // 查询失败时发送失败提示
                                    const errorMsg = response.data.msg || '未找到该订单信息';
                                    this.sendMessageToServer(errorMsg + "\n\n请确认订单号格式是否正确");
                                }
                                
                                // 释放锁 - 减少延迟时间
                                setTimeout(() => {
                                    this.orderQueryResponseLock = false;
                                }, 500); // 从1000毫秒减少到500毫秒
                            })
                            .catch(error => {

                                this.sendMessageToServer('查询订单时发生错误，请稍后重试');
                                
                                // 释放锁
                                setTimeout(() => {
                                    this.orderQueryResponseLock = false;
                                }, 500); // 从1000毫秒减少到500毫秒
                            });
                            
                            // 只处理一条含有订单号的消息
                            break;
                        }
                    }
                },
                updateSessionUnreadCount(sessionId) {
                    // 找到当前会话在列表中的索引
                    const sessionIndex = this.sessions.findIndex(s => s.id === sessionId);
                    if (sessionIndex !== -1) {
                        // 更新总未读消息数
                        this.totalUnreadCount -= this.sessions[sessionIndex].unread_count || 0;
                        // 设置该会话未读消息数为0
                        this.sessions[sessionIndex].unread_count = 0;
                        
                        // 如果没有未读消息了，停止标题闪烁
                        if (this.totalUnreadCount <= 0) {
                            this.stopTitleFlashing();
                        }
                    }
                },
                scrollToBottom() {
                    if (this.$refs.messageContainer) {
                        this.$refs.messageContainer.scrollTop = this.$refs.messageContainer.scrollHeight;
                    }
                },
                
                // 发送邮箱验证
                sendEmailVerification() {
                    ElementPlus.ElMessageBox.confirm(
                        '系统将查找所有匹配的联系人，并向其发送邮箱验证邮件。确定要继续吗？',
                        '邮箱验证确认',
                        {
                            confirmButtonText: '确定发送',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    ).then(() => {
                        // 显示加载中提示
                        const loading = ElementPlus.ElLoading.service({
                            lock: true,
                            text: '正在发送邮箱验证...',
                            background: 'rgba(0, 0, 0, 0.7)'
                        });
                        
                        // 发起请求
                        axios.post('/plugin/Customersystem/user/sendEmailVerification')
                            .then(response => {
                                loading.close();
                                if (response.data.code === 200) {
                                    // 成功
                                    ElementPlus.ElMessage.success(response.data.msg);
                                    // 显示详细结果
                                    if (response.data.data) {
                                        const data = response.data.data;
                                        let message = `成功: ${data.success_count} 封`;
                                        if (data.fail_count > 0) {
                                            message += `，失败: ${data.fail_count} 封`;
                                        }
                                        if (data.matched_contacts && data.matched_contacts.length > 0) {
                                            message += `\n已向以下联系人发送: \n`;
                                            data.matched_contacts.forEach(contact => {
                                                message += `- ${contact.name} (${contact.email})\n`;
                                            });
                                        }
                                        ElementPlus.ElNotification({
                                            title: '邮箱验证结果',
                                            message: message,
                                            type: 'success',
                                            duration: 10000
                                        });
                                    }
                                } else {
                                    // 失败
                                    ElementPlus.ElMessage.error(response.data.msg);
                                    // 显示详细错误
                                    if (response.data.data && response.data.data.error_messages) {
                                        ElementPlus.ElNotification({
                                            title: '邮箱验证失败',
                                            message: response.data.data.error_messages.join('\n'),
                                            type: 'error',
                                            duration: 10000
                                        });
                                    }
                                }
                            })
                            .catch(error => {
                                loading.close();
                                ElementPlus.ElMessage.error('发送邮箱验证请求失败: ' + error.message);
                            });
                    }).catch(() => {
                        ElementPlus.ElMessage.info('已取消发送邮箱验证');
                    });
                },
                
                checkScrollPosition() {
                    if (!this.$refs.messageContainer) return;
                    
                        const container = this.$refs.messageContainer;
                    const scrollBottom = container.scrollHeight - container.scrollTop - container.clientHeight;
                    
                    // 如果滚动条离底部超过100px，显示回到底部按钮
                    this.showScrollButton = scrollBottom > 100;
                },
                sendMessage() {
                    if (!this.currentSession) {
                        ElementPlus.ElMessage.warning('请先选择会话');
                        return;
                    }
                    
                    // 检查会话是否已关闭
                    if (this.currentSession.status === 'closed') {
                        ElementPlus.ElMessage.warning('该会话已关闭，无法发送消息');
                        return;
                    }
                    
                    const content = this.messageContent.trim();
                    if (!content) {
                        return;
                    }
                    
                    // 如果正在处理订单回复，直接发送消息不做处理
                    if (this.isProcessingOrderReply || this.orderQueryResponseLock) {
                        this.sendMessageToServer(content);
                        // 清空输入框
                        this.messageContent = '';
                        return;
                    }
                    
                    // 检查是否是订单号查询 - 只识别"订单号:xxx"格式
                    if (this.settings.enableOrderQuery) {
                        // 尝试匹配"订单号:xxx"格式
                        const orderNoPattern = /订单号[:：]?\s*([a-zA-Z0-9-]{4,32})/i;
                        const match = content.match(orderNoPattern);
                        
                        if (match && match[1]) {
                            // 记录当前处理的订单号，防止重复处理
                            const orderNumber = match[1];
                            
                            // 设置锁防止循环和重复发送
                            this.orderQueryResponseLock = true;
                            this.isProcessingOrderReply = true;
                            
                            // 先发送用户消息
                            this.sendMessageToServer(content);
                            
                            // 清空输入框 - 必须在发送后立即清空
                            this.messageContent = '';
                            
                            // 直接调用queryOrderInfo而不是自己构建响应
                            setTimeout(() => {
                                // 直接使用原始的content，保留完整的"订单号:xxx"格式
                                this.queryOrderInfo(content);
                                
                                // 延迟释放锁
                                setTimeout(() => {
                                    this.isProcessingOrderReply = false;
                                    this.orderQueryResponseLock = false;
                                }, 1000);
                            }, 500);
                            
                            return;
                        }
                    }
                    
                    // 普通消息发送
                    this.sendMessageToServer(content);
                    
                    // 清空输入框 - 确保在任何情况下都清空输入框
                    this.messageContent = '';
                },
                
                // 实际发送消息到服务器
                sendMessageToServer(content) {
                    // 防重发机制：检查是否在短时间内发送了相同内容的消息
                    // 创建一个记录最近发送消息的属性（如果不存在）
                    if (!this.recentSentMessages) {
                        this.recentSentMessages = [];
                    }
                    
                    // 检查是否在最近3秒内发送过完全相同的消息
                    const now = Date.now();
                    const isDuplicate = this.recentSentMessages.some(item => {
                        return item.content === content && (now - item.time < 3000);
                    });
                    
                    if (isDuplicate) {

                        return;
                    }
                    
                    // 记录本次发送的消息
                    this.recentSentMessages.push({
                        content: content,
                        time: now
                    });
                    
                    // 只保留最近10条消息记录
                    if (this.recentSentMessages.length > 10) {
                        this.recentSentMessages.shift();
                    }
                    
                    // 发送消息
                    axios.post('/plugin/Customersystem/user/sendMessage', {
                        session_id: this.currentSession.id,
                        content: content,
                        type: 'text'
                    })
                    .then(response => {
                        if (response.data.code === 200) {
                            // 添加到本地消息列表
                            const msg = response.data.data;
                            const newMessage = {
                                id: msg.id,
                                session_id: msg.session_id,
                                sender_type: msg.sender_type || 'staff',
                                sender_id: msg.sender_id || this.userId,
                                role_type: msg.role_type || 'merchant',
                                message_type: msg.message_type || msg.type || 'text',
                                message: msg.message || msg.content || content,
                                is_read: msg.is_read || 0,
                                is_system: msg.is_system || 0,
                                create_time: msg.create_time || new Date().getTime() / 1000
                            };
                            
                            this.messages.push(newMessage);
                            this.lastMessageId = newMessage.id;
                            
                            // 检查消息内容，如果是订单号格式，自动查询订单
                            // 注意：这里是检查已发送的用户消息内容是否是订单号
                            if (this.settings.enableOrderQuery && !this.isProcessingOrderReply && !this.orderQueryResponseLock) {
                                // 检查是否使用了"订单号:xxx"格式
                                const trimmedContent = content.trim();
                                let orderNumber = null;
                                
                                // 如果内容包含多行并且明显是订单回复格式，跳过查询
                                if (trimmedContent.includes('订单号：') && 
                                    (trimmedContent.includes('商品名称：') || 
                                     trimmedContent.includes('订单金额：') || 
                                     trimmedContent.includes('订单状态：'))) {

                                    // 滚动到底部并返回
                                    this.$nextTick(() => {
                                        this.scrollToBottom();
                                    });
                                    return;
                                }
                                
                                // 只检查严格的"订单号:xxx"格式，不再支持其他格式
                                const orderNoPattern = /订单号[:：]?\s*([a-zA-Z0-9-]{4,32})/i;
                                const match = trimmedContent.match(orderNoPattern);

                                if (match && match[1]) {
                                    orderNumber = match[1];

                                    
                                    // 设置锁防止重复处理
                                    this.orderQueryResponseLock = true;
                                    
                                    // 延迟查询，避免立即触发
                                    setTimeout(() => {
                                        this.queryOrderInfo(orderNumber);
                                    }, 500);
                                }
                            }
                        }
                        
                        // 滚动到底部
                        this.$nextTick(() => {
                            this.scrollToBottom();
                        });
                    })
                    .catch(error => {
                        ElementPlus.ElMessage.error('网络错误，请稍后重试');
                    });
                },
                openImageUpload() {
                    this.$refs.imageInput.click();
                },
                openImageUploadMerchant() {
                    this.$refs.imageInputMerchant.click();
                },
                openFileUpload() {
                    this.$refs.fileInput.click();
                },
                uploadImageMerchantApi(event) {
                    if (!event.target.files || !event.target.files[0]) return;
                    
                    const file = event.target.files[0];
                    if (file.size > 10 * 1024 * 1024) {
                        ElementPlus.ElMessage.error('图片大小不能超过10MB');
                        return;
                    }
                    
                    const formData = new FormData();
                    formData.append('file', file);
                    
                    this.uploadingFile = true;
                    // 移除上传提示
                    // ElementPlus.ElMessage.info('图片上传中，请稍候...');
                    
                    // 使用商户API上传图片
                    axios.post('/merchantApi/Upload/file', formData, {
                        headers: {
                            'Content-Type': 'multipart/form-data'
                        }
                    })
                    .then(response => {
                        this.uploadingFile = false;

                        
                        if (response.data.code === 200 || response.data.code === 1 || response.data.code === 1000) {
                            // 获取上传后的URL，增强兼容性处理
                            let fileUrl = '';
                            
                            // 处理不同的返回数据结构
                            if (response.data.data && response.data.data.url) {
                                fileUrl = response.data.data.url;
                            } else if (response.data.data && response.data.data.file_url) {
                                fileUrl = response.data.data.file_url;
                            } else if (response.data.url) {
                                fileUrl = response.data.url;
                            } else if (response.data.data && typeof response.data.data === 'string') {
                                fileUrl = response.data.data;
                            } else if (typeof response.data === 'string') {
                                fileUrl = response.data;
                            }
                            
                            // 确保URL是完整的
                            if (fileUrl && !fileUrl.startsWith('http')) {
                                if (fileUrl.startsWith('/')) {
                                    fileUrl = window.location.origin + fileUrl;
                                } else {
                                    fileUrl = window.location.origin + '/' + fileUrl;
                                }
                            }
                            

                            
                            if (!fileUrl) {
                                ElementPlus.ElMessage.error('无法获取上传文件的URL');
                                return;
                            }
                            
                            // 先测试图片URL是否可访问
                            const testImg = new Image();
                            testImg.onload = () => {

                                this.sendImageMessage(fileUrl, file.name);
                            };
                            testImg.onerror = () => {

                                // 添加时间戳防止缓存问题
                                const timestampedUrl = fileUrl + (fileUrl.includes('?') ? '&' : '?') + '_t=' + new Date().getTime();
                                this.sendImageMessage(timestampedUrl, file.name);
                            };
                            testImg.src = fileUrl;
                        } else {
                            ElementPlus.ElMessage.error(response.data.msg || '商户API上传图片失败');
                        }
                    })
                    .catch(error => {
                        this.uploadingFile = false;

                        ElementPlus.ElMessage.error('网络错误，请稍后重试');
                    });
                    
                    // 清空文件输入框，以便可以再次选择同一文件
                    event.target.value = '';
                },
                
                // 新增方法，专门处理发送图片消息
                sendImageMessage(fileUrl, fileName) {
                    // 先添加一个本地消息预览
                    const localMessage = {
                        id: 'temp-' + Date.now(),
                        session_id: this.currentSession.id,
                        sender_type: 'staff',
                        sender_id: this.userId,
                        role_type: 'merchant',
                        message_type: 'image',
                        message: fileUrl, // 将URL同时存入message字段
                        file_url: fileUrl,
                        file_name: fileName || '',
                        is_read: 1,
                        is_system: 0,
                        create_time: Math.floor(Date.now() / 1000),
                        imageLoaded: false,
                        imageError: false
                    };
                    
                    this.messages.push(localMessage);
                    
                    // 滚动到底部
                    this.$nextTick(() => {
                        this.scrollToBottom();
                    });
                    
                    // 然后发送图片消息
                    axios.post('/plugin/Customersystem/user/sendMessage', {
                        session_id: this.currentSession.id,
                        content: fileUrl,
                        type: 'image'
                    })
                    .then(msgResponse => {
                        if (msgResponse.data.code === 200) {
                            // 更新消息ID
                            const msg = msgResponse.data.data;
                            // 移除临时消息
                            this.messages = this.messages.filter(m => m.id !== localMessage.id);
                            
                            // 添加服务器返回的消息
                            const newMessage = {
                                id: msg.id,
                                session_id: msg.session_id,
                                sender_type: msg.sender_type || 'staff',
                                sender_id: msg.sender_id || this.userId,
                                role_type: msg.role_type || 'merchant',
                                message_type: 'image',
                                message: fileUrl, // 确保URL也保存在message字段
                                file_url: fileUrl,
                                file_name: fileName || '',
                                is_read: msg.is_read || 0,
                                is_system: msg.is_system || 0,
                                create_time: msg.create_time || Math.floor(Date.now() / 1000),
                                imageLoaded: false,
                                imageError: false
                            };
                            

                            this.messages.push(newMessage);
                            this.lastMessageId = newMessage.id;
                            
                            ElementPlus.ElMessage.success('图片发送成功');
                        } else {
                            ElementPlus.ElMessage.error(msgResponse.data.msg || '发送图片失败');
                        }
                    })
                    .catch(error => {

                        ElementPlus.ElMessage.error('发送图片消息失败，但图片已上传');
                    });
                },
                uploadFile(event) {
                    if (!event.target.files || !event.target.files[0]) return;
                    
                    const file = event.target.files[0];
                    if (file.size > 20 * 1024 * 1024) {
                        ElementPlus.ElMessage.error('文件大小不能超过20MB');
                        return;
                    }
                    
                    const formData = new FormData();
                    formData.append('file', file);
                    formData.append('session_id', this.currentSession.id);
                    formData.append('type', 'file');
                    
                    this.uploadingFile = true;
                    
                    axios.post('/plugin/Customersystem/user/upload', formData, {
                        headers: {
                            'Content-Type': 'multipart/form-data'
                        }
                    })
                    .then(response => {
                        this.uploadingFile = false;
                        
                        if (response.data.code === 200) {
                            // 发送文件消息
                            axios.post('/plugin/Customersystem/user/sendMessage', {
                                session_id: this.currentSession.id,
                                content: response.data.data.url,
                                type: 'file'
                            })
                            .then(msgResponse => {
                                if (msgResponse.data.code === 200) {
                                    // 添加到本地消息列表
                                    const msg = msgResponse.data.data;
                                    const newMessage = {
                                        id: msg.id,
                                        session_id: msg.session_id,
                                        sender_type: msg.sender_type || 'staff',
                                        sender_id: msg.sender_id || this.userId,
                                        role_type: msg.role_type || 'merchant',
                                        message_type: 'file',
                                        message: '',
                                        file_url: response.data.data.url,
                                        file_name: file.name,
                                        is_read: msg.is_read || 0,
                                        is_system: msg.is_system || 0,
                                        create_time: msg.create_time || new Date().getTime() / 1000
                                    };
                                    
                                    this.messages.push(newMessage);
                                    this.lastMessageId = newMessage.id;
                                    
                                    // 滚动到底部
                                    this.$nextTick(() => {
                                        this.scrollToBottom();
                                    });
                                } else {
                                    ElementPlus.ElMessage.error(msgResponse.data.msg || '发送文件失败');
                                }
                            });
                        } else {
                            ElementPlus.ElMessage.error(response.data.msg || '上传文件失败');
                        }
                    })
                    .catch(error => {
                        this.uploadingFile = false;
                        ElementPlus.ElMessage.error('网络错误，请稍后重试');
                    });
                    
                    // 清空文件输入框，以便可以再次选择同一文件
                    event.target.value = '';
                },
                formatTime(timestamp) {
                    const date = new Date(timestamp);
                    return `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
                },
                formatDate(timestamp) {
                    const date = new Date(timestamp);
                    const now = new Date();
                    const yesterday = new Date(now);
                    yesterday.setDate(now.getDate() - 1);
                    
                    if (date.toDateString() === now.toDateString()) {
                        return `今天 ${this.formatTime(timestamp)}`;
                    } else if (date.toDateString() === yesterday.toDateString()) {
                        return `昨天 ${this.formatTime(timestamp)}`;
                    } else {
                        return `${date.getMonth() + 1}-${date.getDate()} ${this.formatTime(timestamp)}`;
                    }
                },
                formatDateTime(timestamp) {
                    const date = new Date(timestamp);
                    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${this.formatTime(timestamp)}`;
                },
                showTimeDivider(message, index) {
                    if (index === 0) return true;
                    
                    const prevTime = new Date(this.messages[index - 1].create_time * 1000);
                    const currTime = new Date(message.create_time * 1000);
                    
                    // 如果两条消息间隔超过5分钟，显示时间分隔线
                    return (currTime - prevTime) > 5 * 60 * 1000;
                },
                playNotificationSound() {
                    // 使用Web Audio API创建提示音，避免404错误
                    try {
                        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                        const oscillator = audioContext.createOscillator();
                        const gainNode = audioContext.createGain();

                        // 连接音频节点
                        oscillator.connect(gainNode);
                        gainNode.connect(audioContext.destination);

                        // 设置音频参数
                        oscillator.frequency.setValueAtTime(800, audioContext.currentTime); // 800Hz频率
                        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime); // 音量30%

                        // 播放短促的提示音
                        oscillator.start(audioContext.currentTime);
                        oscillator.stop(audioContext.currentTime + 0.2); // 播放0.2秒

                    } catch (e) {
                        // 如果Web Audio API不支持，静默处理
                        console.log('音频提示不可用');
                    }
                },
                previewImage(url) {
                    this.previewUrl = url;
                    this.showImageViewer = true;
                },
                downloadFile(url) {
                    window.open(url, '_blank');
                },
                requestNotificationPermission() {
                    if (Notification && Notification.permission !== "granted") {
                        Notification.requestPermission();
                    }
                },
                showBrowserNotification(title, body) {
                    if (Notification && Notification.permission === "granted") {
                        const notification = new Notification(title, {
                            body: body,
                            icon: '/static/common/images/logo.png'
                        });
                        
                        notification.onclick = function() {
                            window.focus();
                            this.close();
                        };
                    }
                },
                startTitleFlashing() {
                    if (this.titleInterval) return;
                    
                    this.originalTitle = document.title;
                    let flag = false;
                    
                    this.titleInterval = setInterval(() => {
                        document.title = flag ? 
                            `【新消息】${this.totalUnreadCount}条未读` : 
                            this.originalTitle;
                        flag = !flag;
                    }, 1000);
                },
                stopTitleFlashing() {
                    if (this.titleInterval) {
                        clearInterval(this.titleInterval);
                        this.titleInterval = null;
                        document.title = this.originalTitle;
                    }
                },

                

                runDiagnose() {

                    axios.get('/plugin/Customersystem/user/diagnose')
                        .then(response => {

                            if (response.data.code === 200) {
                                // 显示诊断信息
                                ElementPlus.ElMessageBox.alert(
                                    `<pre>${JSON.stringify(response.data.data, null, 2)}</pre>`, 
                                    '系统诊断结果', 
                                    {
                                        dangerouslyUseHTMLString: true
                                    }
                                );
                                ElementPlus.ElMessage.success('诊断完成');
                            } else {
                                ElementPlus.ElMessage.error(response.data.msg || '诊断失败');
                            }
                        })
                        .catch(error => {

                            ElementPlus.ElMessage.error('诊断请求失败');
                        });
                },
                // 检查表结构
                checkTableStructure() {
                    axios.get('/plugin/Customersystem/user/diagnose')
                        .then(response => {
                            if (response.data.code === 200) {
                                if (response.data.data && response.data.data.tables) {
                                    this.tableStructureChecked = true;
                                }
                            }
                        })
                        .catch(error => {

                        });
                },
                isCustomerMessage(message) {
                    // 优先判断role_type字段 - 商家角色的消息应该显示在右侧
                    if (message.role_type === 'merchant') {
                        // 如果当前登录用户是商家，并且消息是商家发送的，应该显示在右侧
                        if (this.isCurrentUserMerchant) {
                            // 检查是否是当前用户发送的消息
                            return message.sender_id != this.userId;
                        }
                        return false; // 对于客户用户，商家消息显示在右侧
                    }
                    
                    // 增加对客服角色类型的处理
                    if (message.role_type === 'staff') {
                        // 客服消息应该显示在右侧
                        return false;
                    }
                    
                    if (message.role_type === 'customer') {
                        // 如果当前登录用户是商家，客户消息应该显示在左侧
                        if (this.isCurrentUserMerchant) {
                            return true;
                        }
                        // 如果当前登录用户是客户，自己的消息应该显示在右侧
                        return message.sender_id != this.userId;
                    }
                    
                    // 如果没有role_type或role_type不明确，根据sender_type判断
                    if (message.sender_type === 'staff') {
                        // 如果当前登录用户是商家，并且消息是自己发送的，应该显示在右侧
                        if (this.isCurrentUserMerchant) {
                            return message.sender_id != this.userId;
                        }
                        return false; // 对于客户用户，staff消息显示在右侧
                    }
                    
                    // 检查是否是当前用户发送的消息
                    if (message.sender_id == this.userId) {
                        return false; // 自己发送的消息显示在右侧
                    }
                    
                    return true; // 默认其他情况显示在左侧
                },
                handleImageError(event, message) {

                    
                    // 标记图片加载失败
                    message.imageError = true;
                    
                    // 尝试从其他字段获取图片URL
                    if (message.message && message.message.startsWith('http') && event.target.src !== message.message) {

                        event.target.src = message.message;
                    } else if (message.content && message.content.startsWith('http') && event.target.src !== message.content) {

                        event.target.src = message.content;
                    }
                    // 强制Vue更新视图
                    this.$forceUpdate();
                },
                backToSessionList() {
                    // 实现返回会话列表的逻辑（只在移动端有效）
                    if (this.isMobile) {
                        // 不清除当前会话，只是返回会话列表视图
                        document.querySelector('.sidebar').classList.remove('hidden');
                    }
                },
                handleResize() {
                    // 处理窗口大小变化的逻辑
                    this.isMobile = window.innerWidth <= 768;
                },
                setupTouchEvents() {
                    // 实现触摸滑动返回的逻辑
                    let startX, startY;
                    const mainContent = document.querySelector('.main-content');
                    const sidebar = document.querySelector('.sidebar');
                    
                    if (!mainContent || !sidebar) return;
                    
                    // 监听触摸开始事件
                    document.addEventListener('touchstart', (e) => {
                        startX = e.touches[0].clientX;
                        startY = e.touches[0].clientY;
                    }, { passive: true });
                    
                    // 监听触摸移动事件
                    document.addEventListener('touchmove', (e) => {
                        if (!startX || !this.currentSession) return;
                        
                        const currentX = e.touches[0].clientX;
                        const currentY = e.touches[0].clientY;
                        
                        // 计算水平和垂直滑动距离
                        const diffX = currentX - startX;
                        const diffY = currentY - startY;
                        
                        // 如果是从左往右滑动，并且水平滑动距离大于垂直滑动距离
                        if (diffX > 50 && Math.abs(diffX) > Math.abs(diffY)) {
                            // 从左向右滑动时显示会话列表
                            this.backToSessionList();
                            startX = null; // 重置开始位置
                        }
                    }, { passive: true });
                    
                    // 监听触摸结束事件
                    document.addEventListener('touchend', () => {
                        startX = null;
                    }, { passive: true });
                },
                formatMessage(message) {
                    if (!message) return '';
                    
                    // 检查是否为图片链接
                    if (typeof message === 'string' && message.match(/\.(jpg|jpeg|png|gif|bmp|webp)(\?.*)?$/i)) {
                        // 返回图片标签
                        return `<img src="${message}" class="inline-message-image" onclick="this.classList.toggle('enlarged')" style="max-width: 100%; cursor: pointer; border-radius: 4px;" />`;
                    }
                    
                    // 检查是否包含预设问题的HTML
                    if (typeof message === 'string' && message.includes('<div class="preset-questions">')) {
                        // 如果是预设问题HTML，直接返回不做处理
                        return message;
                    }
                    
                    // 处理换行符
                    let formattedMessage = message.replace(/\n/g, '<br>');
                    
                    // 处理链接
                    formattedMessage = formattedMessage.replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank">$1</a>');
                    
                    return formattedMessage;
                },
                handlePresetQuestionClick(event, message) {
                    // 查找预设问题元素
                    const targetItem = event.target.closest('.preset-question-item');
                    if (!targetItem) return;
                    
                    const action = targetItem.getAttribute('data-action');

                    
                    // 处理联系供货商
                    if (action === 'contact-supplier') {
                        const supplierId = targetItem.getAttribute('data-supplier-id');
                        if (supplierId) {

                            this.contactSupplier(supplierId);
                        }
                    } else if (action === 'no-contact') {

                        this.sendMessageToServer('好的，如有其他问题可以随时咨询。');
                    }
                    // 如果有回复内容，优先使用回复内容
                    else {
                        // 获取多种类型的数据
                        const textContent = targetItem.getAttribute('data-text-content');
                        const imageUrl = targetItem.getAttribute('data-image-url');
                        const fileUrl = targetItem.getAttribute('data-file-url');
                        const fileName = targetItem.getAttribute('data-file-name') || '预设问题文件';

                        let hasSentContent = false;

                        // 发送文本内容
                        if (textContent && textContent.trim()) {
                            this.messageContent = textContent;
                            this.sendMessage();
                            hasSentContent = true;
                        }

                        // 发送图片内容
                        if (imageUrl && imageUrl.trim()) {
                            setTimeout(() => {
                                this.sendImageMessage(imageUrl, '预设问题图片');
                            }, hasSentContent ? 500 : 0);
                            hasSentContent = true;
                        }

                        // 发送文件内容
                        if (fileUrl && fileUrl.trim()) {
                            setTimeout(() => {
                                axios.post('/plugin/Customersystem/user/sendMessage', {
                                    session_id: this.currentSession.id,
                                    content: fileUrl,
                                    type: 'file'
                                })
                                .then(msgResponse => {
                                    if (msgResponse.data.code === 200) {
                                        // 添加到本地消息列表
                                        const msg = msgResponse.data.data;
                                        const newMessage = {
                                            id: msg.id,
                                            session_id: msg.session_id,
                                            sender_type: msg.sender_type || 'staff',
                                            sender_id: msg.sender_id || this.userId,
                                            role_type: msg.role_type || 'merchant',
                                            message_type: 'file',
                                            message: '',
                                            file_url: fileUrl,
                                            file_name: fileName,
                                            is_read: msg.is_read || 0,
                                            is_system: msg.is_system || 0,
                                            create_time: msg.create_time || new Date().getTime() / 1000
                                        };

                                        this.messages.push(newMessage);
                                        this.lastMessageId = newMessage.id;

                                        // 滚动到底部
                                        this.$nextTick(() => {
                                            this.scrollToBottom();
                                        });
                                    } else {
                                        ElementPlus.ElMessage.error(msgResponse.data.msg || '发送文件失败');
                                    }
                                })
                                .catch(error => {
                                    ElementPlus.ElMessage.error('发送文件失败，请稍后重试');
                                });
                            }, hasSentContent ? 1000 : 0);
                            hasSentContent = true;
                        }

                        // 如果没有任何内容，则使用问题文本作为消息发送
                        if (!hasSentContent) {
                            const questionText = targetItem.textContent.trim();
                            this.messageContent = questionText;
                            this.sendMessage();
                        }
                    }
                },
                
                // 新增：发送预设问题给客户
                sendPresetQuestions() {
                    if (!this.currentSession) return;
                    
                    // 先尝试加载预设问题配置
                    axios.get('/plugin/Customersystem/user/getPresetQuestionsConfig')
                    .then(configResponse => {
                        let presetConfig = {};
                        
                        if (configResponse.data.code === 200) {
                            presetConfig = configResponse.data.data;
                            
                            // 如果问题是字符串形式，转换为对象形式
                            if (presetConfig.presetQuestions && presetConfig.presetQuestions.questions) {
                                presetConfig.presetQuestions.questions = presetConfig.presetQuestions.questions.map(q => {
                                    if (typeof q === 'string') {
                                        return { question: q, answer: '' };
                                    }
                                    return q;
                                });
                            }
                        } else {
                            // 使用默认配置
                            presetConfig = {
                                autoSendPresetQuestions: true,
                                presetQuestions: {
                                    title: 'Spikees云寄售',
                                    description: 'Spikees云寄售欢迎您~~\n有什么问题您下方留言\n如果下面有您想问的问题点击即可解答！',
                                    questions: [
                                        {question: '1. 平台安全吗', answer: '是的，我们平台非常安全，所有交易都有平台担保，请放心使用。'},
                                        {question: '2. 如何注册商家？', answer: '商家注册需要完成实名认证，然后在用户中心点击"成为商家"，按步骤填写资料并等待审核。'},
                                        {question: '3. 投诉订单什么时候到账？', answer: '投诉受理后，订单资金会在3-7个工作日内退回到您的账户。'}
                                    ]
                                }
                            };
                        }
                        
                        // 发送到服务器
                        axios.post('/plugin/Customersystem/user/sendPresetQuestions', {
                            session_id: this.currentSession.id,
                            title: presetConfig.presetQuestions.title,
                            description: presetConfig.presetQuestions.description,
                            questions: presetConfig.presetQuestions.questions
                        })
                        .then(response => {
                            if (response.data.code === 200) {

                                // 如果返回了消息，添加到消息列表
                                if (response.data.data) {
                                    this.messages.push(response.data.data);
                                    this.$nextTick(() => {
                                        this.scrollToBottom();
                                    });
                                }
                            } else {
                                ElementPlus.ElMessage.error(response.data.msg || '发送预设问题失败');
                            }
                        })
                        .catch(error => {

                            ElementPlus.ElMessage.error('网络错误，请稍后重试');
                        });
                    })
                    .catch(error => {

                        // 使用默认预设问题
                        this.sendDefaultPresetQuestions();
                    });
                },
                
                // 发送默认预设问题（作为备用方案）
                sendDefaultPresetQuestions() {
                    if (!this.currentSession) return;
                    
                    // 默认预设问题
                    const defaultQuestions = [
                        {
                            question: '1. 平台安全吗',
                            answer: '是的，我们平台非常安全，所有交易都有平台担保，请放心使用。',
                            enable_text: true,
                            enable_image: false,
                            enable_file: false,
                            image_url: '',
                            file_url: '',
                            file_name: ''
                        },
                        {
                            question: '2. 如何注册商家？',
                            answer: '商家注册需要完成实名认证，然后在用户中心点击"成为商家"，按步骤填写资料并等待审核。',
                            enable_text: true,
                            enable_image: false,
                            enable_file: false,
                            image_url: '',
                            file_url: '',
                            file_name: ''
                        },
                        {
                            question: '3. 投诉订单什么时候到账？',
                            answer: '投诉受理后，订单资金会在3-7个工作日内退回到您的账户。',
                            enable_text: true,
                            enable_image: false,
                            enable_file: false,
                            image_url: '',
                            file_url: '',
                            file_name: ''
                        }
                    ];
                    
                    // 发送到服务器
                    axios.post('/plugin/Customersystem/user/sendPresetQuestions', {
                        session_id: this.currentSession.id,
                        title: 'Spikees云寄售',
                        description: 'Spikees云寄售欢迎您~~\n有什么问题您下方留言\n如果下面有您想问的问题点击即可解答！',
                        questions: defaultQuestions
                    })
                    .then(response => {
                        if (response.data.code === 200) {

                            // 如果返回了消息，添加到消息列表
                            if (response.data.data) {
                                this.messages.push(response.data.data);
                                this.$nextTick(() => {
                                    this.scrollToBottom();
                                });
                            }
                        } else {
                            ElementPlus.ElMessage.error(response.data.msg || '发送预设问题失败');
                        }
                    })
                    .catch(error => {

                        ElementPlus.ElMessage.error('网络错误，请稍后重试');
                    });
                },
                
                // 联系供货商
                contactSupplier(supplierId) {
                    // 设置加载状态
                    this.sendMessageToServer('正在为您联系供货商，请稍候...');
                    
                    // 创建与供货商的新会话
                    axios.post('/plugin/Customersystem/user/createSession', {
                        target_user_id: supplierId,
                        title: '商品咨询',
                        message: '您好，我想咨询您的商品相关事宜。'
                    })
                    .then(response => {
                        if (response.data.code === 200) {
                            const sessionData = response.data.data;
                            
                            // 发送会话创建成功的消息
                            this.sendMessageToServer(`已成功创建与供货商的会话，您可以通过会话ID: ${sessionData.id} 查询详情。`);
                            
                            // 刷新会话列表
                            this.loadSessions();
                        } else {
                            // 发送失败提示
                            this.sendMessageToServer('联系供货商失败: ' + (response.data.msg || '未知错误'));
                        }
                    })
                    .catch(error => {

                        this.sendMessageToServer('系统错误，无法联系供货商，请稍后重试。');
                    });
                },
                // 右键菜单相关方法
                handleContextMenu(event) {
                    // 阻止默认的右键菜单
                    event.preventDefault();
                    // 关闭已打开的菜单
                    this.closeContextMenu();
                },
                
                openContextMenu(event, message, index) {
                    if (!message) {

                        return;
                    }
                    
                    // 存储消息ID而不是整个对象
                    this.currentMessageId = message.id; 
                    this.selectedMessageIndex = index;
                    

                    
                    // 检查消息是否可撤回（2分钟内的消息）
                    const currentTime = Math.floor(Date.now() / 1000);
                    const messageTime = message.create_time;
                    this.canRevokeMessage = (currentTime - messageTime) <= 120;
                    
                    // 显示右键菜单
                    this.showContextMenu = true;
                    
                    // 设置菜单位置
                    this.$nextTick(() => {
                        const menu = document.querySelector('.message-context-menu');
                        if (!menu) return;
                        
                        const menuWidth = menu.offsetWidth;
                        const menuHeight = menu.offsetHeight;
                        
                        // 计算菜单位置，避免超出视口
                        let left = event.clientX;
                        let top = event.clientY;
                        
                        // 检查是否会超出右边界
                        if (left + menuWidth > window.innerWidth) {
                            left = window.innerWidth - menuWidth - 10;
                        }
                        
                        // 检查是否会超出下边界
                        if (top + menuHeight > window.innerHeight) {
                            top = window.innerHeight - menuHeight - 10;
                        }
                        
                        this.contextMenuStyle = {
                            top: `${top}px`,
                            left: `${left}px`
                        };
                    });
                },
                
                closeContextMenu() {
                    this.showContextMenu = false;
                },
                
                // 新方法，完全替代旧的revokeMessage方法
                handleRevokeMessage() {
                    // 首先关闭菜单，避免引用问题
                    this.closeContextMenu();
                    
                    if (!this.currentMessageId) {

                        ElementPlus.ElMessage.error('无法撤回消息：消息ID无效');
                        return;
                    }
                    

                    
                        // 显示加载中提示
                    const loadingInstance = ElementPlus.ElLoading.service({
                        text: '正在撤回消息...',
                        background: 'rgba(0, 0, 0, 0.7)'
                    });
                    
                    // 调用API撤回消息
                    axios.post('/plugin/Customersystem/user/revokeMessage', {
                        message_id: this.currentMessageId
                    })
                    .then(response => {
                        // 关闭加载提示
                        loadingInstance.close();
                        
                        if (response.data.code === 200) {
                            // 更新本地消息数据
                            if (this.selectedMessageIndex >= 0 && this.selectedMessageIndex < this.messages.length) {
                                // 清除图片和文件的URL
                                const message = this.messages[this.selectedMessageIndex];
                                message.message = '[该消息已被撤回]';
                                message.is_recalled = 1;
                                
                                // 安全地清除可能存在的URL字段
                                if (message.hasOwnProperty('file_url')) {
                                    message.file_url = '';
                                }
                                
                                if (message.hasOwnProperty('content')) {
                                    message.content = '';
                                }
                                
                                // 确保不会尝试加载图片
                                if ((message.hasOwnProperty('message_type') && message.message_type === 'image') || 
                                    (message.hasOwnProperty('type') && message.type === 'image')) {
                                    message.imageLoaded = true;
                                    message.imageError = false;
                                }
                            }
                            
                            ElementPlus.ElMessage.success('消息已撤回');
                        } else {
                            // 显示详细的错误信息
                            const errorMsg = response.data.msg || '撤回消息失败';

                            ElementPlus.ElMessage.error(errorMsg);
                        }
                    })
                    .catch(error => {
                        // 关闭加载提示
                        loadingInstance.close();
                        

                        let errorMsg = '网络错误，请稍后重试';
                        
                        // 尝试提取更详细的错误信息
                        if (error.response && error.response.data) {
                            errorMsg = error.response.data.msg || errorMsg;
                        }
                        
                        ElementPlus.ElMessage.error(errorMsg);
                    })
                    .finally(() => {
                        // 请求完成后清除当前消息ID
                        this.currentMessageId = null;
                    });
                },
                
                copyMessageText() {
                    if (this.selectedMessageIndex < 0 || this.selectedMessageIndex >= this.messages.length) {
                        this.closeContextMenu();
                        return;
                    }
                    
                    const message = this.messages[this.selectedMessageIndex];
                    
                    // 获取消息文本
                    let text = message.message || message.content || '';
                    
                    // 如果是HTML内容，提取纯文本
                    if (text.includes('<') && text.includes('>')) {
                        const temp = document.createElement('div');
                        temp.innerHTML = text;
                        text = temp.textContent || temp.innerText || '';
                    }
                    
                    // 使用Clipboard API复制文本
                    navigator.clipboard.writeText(text)
                        .then(() => {
                            ElementPlus.ElMessage.success('已复制到剪贴板');
                            this.closeContextMenu();
                        })
                        .catch(err => {

                            ElementPlus.ElMessage.error('复制失败');
                        });
                },
                
                handleKeyDown(event) {
                    // 按ESC键关闭右键菜单
                    if (event.key === 'Escape' && this.showContextMenu) {
                        this.closeContextMenu();
                    }
                },
                
                // 处理粘贴事件，支持粘贴图片自动发送
                handlePaste(event) {
                    // 获取剪贴板数据
                    const clipboardData = event.clipboardData || window.clipboardData;
                    
                    // 检查是否包含图片
                    const items = clipboardData.items;
                    let imageFile = null;
                    
                    if (items) {
                        // 遍历剪贴板项目
                        for (let i = 0; i < items.length; i++) {
                            if (items[i].type.indexOf('image') !== -1) {
                                // 获取图片文件
                                imageFile = items[i].getAsFile();
                                break;
                            }
                        }
                    }
                    
                    // 如果找到图片文件，上传并发送
                    if (imageFile) {
                        // 阻止默认粘贴行为
                        event.preventDefault();
                        
                        // 上传图片时不显示提示
                        // ElementPlus.ElMessage.info('正在上传粘贴的图片...');
                        
                        // 创建FormData对象
                        const formData = new FormData();
                        formData.append('file', imageFile);
                        
                        this.uploadingFile = true;
                        
                        // 使用商户API上传图片
                        axios.post('/merchantApi/Upload/file', formData, {
                            headers: {
                                'Content-Type': 'multipart/form-data'
                            }
                        })
                        .then(response => {
                            this.uploadingFile = false;

                            
                            if (response.data.code === 200 || response.data.code === 1 || response.data.code === 1000) {
                                // 获取上传后的URL
                                let fileUrl = '';
                                
                                // 处理不同的返回数据结构
                                if (response.data.data && response.data.data.url) {
                                    fileUrl = response.data.data.url;
                                } else if (response.data.data && response.data.data.file_url) {
                                    fileUrl = response.data.data.file_url;
                                } else if (response.data.url) {
                                    fileUrl = response.data.url;
                                } else if (response.data.data && typeof response.data.data === 'string') {
                                    fileUrl = response.data.data;
                                } else if (typeof response.data === 'string') {
                                    fileUrl = response.data;
                                }
                                
                                // 确保URL是完整的
                                if (fileUrl && !fileUrl.startsWith('http')) {
                                    if (fileUrl.startsWith('/')) {
                                        fileUrl = window.location.origin + fileUrl;
                                    } else {
                                        fileUrl = window.location.origin + '/' + fileUrl;
                                    }
                                }
                                
                                if (!fileUrl) {
                                    ElementPlus.ElMessage.error('无法获取上传文件的URL');
                                    return;
                                }
                                
                                // 添加临时消息
                                const localMessage = {
                                    id: 'temp-' + Date.now(),
                                    session_id: this.currentSession.id,
                                    sender_type: 'staff',
                                    sender_id: this.userId,
                                    role_type: 'merchant',
                                    message_type: 'image',
                                    message: fileUrl,
                                    file_url: fileUrl,
                                    file_name: '粘贴的图片.png',
                                    is_read: 1,
                                    is_system: 0,
                                    create_time: Math.floor(Date.now() / 1000)
                                };
                                
                                this.messages.push(localMessage);
                                
                                // 滚动到底部
                                this.$nextTick(() => {
                                    this.scrollToBottom();
                                });
                                
                                // 发送图片消息
                                axios.post('/plugin/Customersystem/user/sendMessage', {
                                    session_id: this.currentSession.id,
                                    content: fileUrl,
                                    type: 'image'
                                })
                                .then(msgResponse => {
                                    if (msgResponse.data.code === 200) {
                                        // 移除临时消息
                                        this.messages = this.messages.filter(m => m.id !== localMessage.id);
                                        
                                        // 添加服务器返回的消息
                                        const msg = msgResponse.data.data;
                                        const newMessage = {
                                            id: msg.id,
                                            session_id: msg.session_id,
                                            sender_type: msg.sender_type || 'staff',
                                            sender_id: msg.sender_id || this.userId,
                                            role_type: msg.role_type || 'merchant',
                                            message_type: 'image',
                                            message: fileUrl,
                                            file_url: fileUrl,
                                            file_name: '粘贴的图片.png',
                                            is_read: msg.is_read || 0,
                                            is_system: msg.is_system || 0,
                                            create_time: msg.create_time || Math.floor(Date.now() / 1000)
                                        };
                                        
                                        this.messages.push(newMessage);
                                        this.lastMessageId = newMessage.id;
                                        
                                        ElementPlus.ElMessage.success('图片发送成功');
                                    } else {
                                        ElementPlus.ElMessage.error(msgResponse.data.msg || '发送图片失败');
                                    }
                                })
                                .catch(error => {

                                    ElementPlus.ElMessage.error('发送图片消息失败');
                                });
                            } else {
                                ElementPlus.ElMessage.error(response.data.msg || '上传图片失败');
                            }
                        })
                        .catch(error => {
                            this.uploadingFile = false;

                            ElementPlus.ElMessage.error('网络错误，请稍后重试');
                        });
                    }
                    // 如果是文本，让默认粘贴行为继续
                },
                openSettings() {
                    this.showSettings = true;
                },
                closeSettings() {
                    this.showSettings = false;
                },
                saveSettings() {
                    // 在这里添加保存设置的逻辑

                    
                    // 创建一个加载提示
                        const loading = ElementPlus.ElLoading.service({
                            lock: true,
                        text: '正在保存设置...',
                            background: 'rgba(0, 0, 0, 0.7)'
                        });
                        
                    // 保存设置到后端
                    axios.post('/plugin/Customersystem/user/saveSettings', this.settings)
                            .then(response => {
                            loading.close(); // 关闭加载提示
                            
                                if (response.data.code === 200) {

                                ElementPlus.ElMessage.success('设置保存成功');
                                // 立即重新加载设置，确保数据一致
                                this.loadSettings();
                                this.closeSettings();
                            } else {

                                ElementPlus.ElMessage.error(response.data.msg || '保存设置失败');
                            }
                        })
                        .catch(error => {
                            loading.close(); // 关闭加载提示
                            

                            let errorMsg = '网络错误，请稍后重试';
                            
                            // 尝试提取更详细的错误信息
                            if (error.response && error.response.data) {
                                errorMsg = error.response.data.msg || errorMsg;
                            }
                            
                            ElementPlus.ElMessage.error(errorMsg);
                        });
                },
                // 加载设置
                loadSettings() {
                    axios.get('/plugin/Customersystem/user/getSettings')
                        .then(response => {

                            
                            if (response.data.code === 200 && response.data.data) {
                                const loadedSettings = response.data.data;
                                
                                // 防止覆盖默认设置中不存在的字段
                                const mergedSettings = {...this.settings};
                                
                                // 特殊处理布尔值字段
                                if ('allowSupplier' in loadedSettings) {
                                    mergedSettings.allowSupplier = Boolean(loadedSettings.allowSupplier);
                                }
                                
                                if ('enableOrderQuery' in loadedSettings) {
                                    mergedSettings.enableOrderQuery = Boolean(loadedSettings.enableOrderQuery);
                                }
                                
                                if ('enableAutoReply' in loadedSettings) {
                                    mergedSettings.enableAutoReply = Boolean(loadedSettings.enableAutoReply);
                                }
                                
                                // 文本字段直接赋值
                                if ('orderQueryPrompt' in loadedSettings && loadedSettings.orderQueryPrompt) {
                                    mergedSettings.orderQueryPrompt = loadedSettings.orderQueryPrompt;
                                }
                                
                                if ('orderQueryTemplate' in loadedSettings && loadedSettings.orderQueryTemplate) {
                                    mergedSettings.orderQueryTemplate = loadedSettings.orderQueryTemplate;
                                }
                                
                                if ('welcomeMessage' in loadedSettings && loadedSettings.welcomeMessage) {
                                    mergedSettings.welcomeMessage = loadedSettings.welcomeMessage;
                                }
                                
                                this.settings = mergedSettings;

                                
                                // 如果开启了供货商功能，检查一下是否需要加载供货商相关设置
                                if (this.settings.allowSupplier) {

                                    // 手动设置为true用于测试，或者从其他地方获取实际的供应商状态
                                    this.isSupplier = true;
                                    // 同时刷新待处理的申请数量
                                    this.loadPendingJoinRequests();
                                } else {
                                    this.isSupplier = false;
                                }
                            } else {

                            }
                        })
                        .catch(error => {

                        });
                },
                // 检查订单号格式
                isOrderNumber(text) {
                    if (!this.settings.enableOrderQuery) return false;
                    
                    const trimmedText = text.trim();
                    
                    // 如果消息包含多行且明显是订单回复格式，不要将其识别为订单号
                    if (trimmedText.includes('订单号：') && 
                        (trimmedText.includes('商品名称：') || 
                         trimmedText.includes('订单金额：') || 
                         trimmedText.includes('订单状态：'))) {

                        return false;
                    }
                    
                    // 只检查带前缀的格式，不接受纯订单号格式
                    
                    // 1. 直接提取"订单号:xxxx"或"订单号：xxxx"格式
                    const orderNoPattern = /订单号[:：]?\s*([a-zA-Z0-9-]{4,32})/i;
                    const match = trimmedText.match(orderNoPattern);

                    if (match && match[1]) {

                        
                        // 移除检查是否最近查询过此订单号的代码
                        
                        return true;
                    }
                    

                    return false;
                },
                // 查询订单信息
                queryOrderInfo(orderNumber) {
                    if (!orderNumber || !this.settings.enableOrderQuery) return;
                    

                    
                    // 检查是否是标准的"订单号:xxx"格式
                    const initialOrderNoPattern = /订单号[:：]?\s*([a-zA-Z0-9-]{4,32})/i;
                    const initialMatch = orderNumber.match(initialOrderNoPattern);
                    

                    
                    if (!initialMatch || !initialMatch[1]) {
                        // 不是标准格式，显示错误提示
                        this.sendMessageToServer("请使用标准格式：订单号:xxxxxx 进行查询");
                        return;
                    }
                    
                    // 如果已有锁，直接返回，防止循环调用
                    if (this.orderQueryResponseLock && !this.isProcessingOrderReply) {

                        return;
                    }
                    
                    // 设置锁
                    this.orderQueryResponseLock = true;
                    this.isProcessingOrderReply = true;
                    

                    
                    // 添加正在查询的提示消息 - 直接使用sendMessageToServer替代修改messageContent和调用sendMessage
                    this.sendMessageToServer("正在查询订单信息，请稍候...");
                    
                    // 直接使用原始输入 - 完整保留"订单号:xxx"格式
                    // 不对原始输入做任何清理或修改，确保精确传递到后端
                    const rawInput = orderNumber;
                    

                    
                    axios.post('/plugin/Customersystem/user/queryOrder', {
                        trade_no: rawInput
                    })
                        .then(response => {

                            
                            if (response.data.code === 200 && response.data.data) {
                                // 订单信息
                                const orderInfo = response.data.data;
                                
                                // 使用模板生成回复内容
                                let replyMessage = this.settings.orderQueryTemplate;
                                
                                // 定义字段映射和默认值
                                const fields = {
                                    '订单号': orderInfo.trade_no || rawInput,
                                    '商品名称': orderInfo.goods_name || '商品详情',
                                    '商品数量': orderInfo.quantity || '1',
                                    '订单金额': orderInfo.total_amount || '0.00',
                                    '下单时间': '',
                                    '订单状态': orderInfo.status || '处理中'
                                };
                                
                                // 格式化订单时间
                                if (orderInfo.create_time) {
                                    const date = new Date(orderInfo.create_time * 1000);
                                    fields['下单时间'] = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
                                } else {
                                    fields['下单时间'] = '未知';
                                }
                                
                                // 替换所有变量
                                for (const [key, value] of Object.entries(fields)) {
                                    const pattern = new RegExp(`{${key}}`, 'g');
                                    replyMessage = replyMessage.replace(pattern, value);
                                }
                                
                                // 处理模板中还存在但未替换的变量
                                const unreplacedVars = replyMessage.match(/{[^{}]+}/g);
                                if (unreplacedVars) {
                                    unreplacedVars.forEach(variable => {
                                        // 替换为合适的默认值或空字符串
                                        replyMessage = replyMessage.replace(new RegExp(variable, 'g'), '');
                                    });
                                }
                                

                                
                                // 直接发送订单信息，不再修改messageContent和调用sendMessage
                                this.sendMessageToServer(replyMessage);
                                
                                // 查询成功后解除锁定
                                setTimeout(() => {
                                    this.isProcessingOrderReply = false;
                                    this.orderQueryResponseLock = false;
                                }, 500);
                            } else {
                                // 订单不存在或查询失败
                                const errorMsg = response.data.msg || '未找到该订单信息';

                                
                                // 直接发送错误消息
                                this.sendMessageToServer(errorMsg + "\n\n请确认订单号格式是否正确。您只能查询您自己的订单信息。");
                                
                                // 解除锁定
                                setTimeout(() => {
                                    this.isProcessingOrderReply = false;
                                    this.orderQueryResponseLock = false;
                                }, 500);
                                }
                            })
                            .catch(error => {

                            
                            // 显示更友好的错误消息
                            let errorMessage = '查询订单时发生错误，请稍后重试';
                            if (error.response && error.response.data && error.response.data.msg) {
                                errorMessage = error.response.data.msg;
                            }
                            
                            // 直接发送错误消息
                            this.sendMessageToServer(errorMessage);
                            
                            // 解除锁定
                            setTimeout(() => {
                                this.isProcessingOrderReply = false;
                                this.orderQueryResponseLock = false;
                            }, 500);
                        });
                },
                // 检查是否需要自动发送消息
                checkAutoSendMessages(messages) {
                    // 如果没有开启功能，直接返回
                    if (!this.settings.enableOrderQuery && !this.settings.enableAutoReply) {
                        return;
                    }
                    
                    // 如果会话已关闭，不自动发送
                    if (this.currentSession.status === 'closed') {
                        return;
                    }
                    
                    // 检查消息数量，如果是新会话（只有系统消息或无消息）或其他合适条件
                    const nonSystemMessages = messages.filter(m => !m.is_system);
                    
                    // 获取商家发送的消息和客户发送的消息
                    const merchantMessages = nonSystemMessages.filter(m => 
                        m.sender_type === 'staff' || m.role_type === 'merchant');
                    const customerMessages = nonSystemMessages.filter(m => 
                        m.sender_type === 'customer' || m.role_type === 'customer');
                    
                    // 检查最新的客户消息，看是否有订单号格式
                    if (this.settings.enableOrderQuery && customerMessages.length > 0 && !this.orderQueryResponseLock) {
                        // 获取最新的客户消息
                        const latestCustomerMessage = customerMessages[customerMessages.length - 1];
                        
                        // 只检查文本类型的消息
                        if ((latestCustomerMessage.message_type === 'text' || latestCustomerMessage.type === 'text') && 
                            !latestCustomerMessage.is_recalled) {
                            // 获取消息内容
                            const content = latestCustomerMessage.message || latestCustomerMessage.content || '';
                            if (!content) return;
                            
                            // 如果已经响应过该消息，跳过
                            if (this.respondedOrderIds && this.respondedOrderIds.includes(latestCustomerMessage.id)) {

                                return;
                            }
                            
                            const trimmedContent = content.trim();
                            
                            // 只检查"订单号:xxx"格式，不再检查纯数字或标准订单号格式
                            const orderNoPattern = /订单号[:：]?\s*([a-zA-Z0-9-]{4,32})/i;
                            const match = trimmedContent.match(orderNoPattern);

                            if (match && match[1]) {

                                // 记录已响应
                                if (!this.respondedOrderIds) this.respondedOrderIds = [];
                                this.respondedOrderIds.push(latestCustomerMessage.id);
                                
                                // 设置锁，防止重复处理
                                this.orderQueryResponseLock = true;
                                
                                setTimeout(() => {
                                    this.queryOrderInfo(trimmedContent);
                                }, 800);
                                return;
                            }
                        }
                    }
                    
                    // 无论是否有客户消息，只要商家没有发过消息，且会话开始，就发送提示
                    if (merchantMessages.length === 0) {
                        // 设置锁，防止重复发送
                        if (this.orderQueryResponseLock) return;
                        this.orderQueryResponseLock = true;
                        
                        // 先发送欢迎语 - 如果启用了自动回复
                        if (this.settings.enableAutoReply && this.settings.welcomeMessage) {
                            setTimeout(() => {
                                this.sendMessageToServer(this.settings.welcomeMessage);
                                
                                // 然后发送订单查询提示 - 优先级更高
                                if (this.settings.enableOrderQuery && this.settings.orderQueryPrompt) {
                                    setTimeout(() => {
                                        this.sendMessageToServer(this.settings.orderQueryPrompt);
                                        
                                        // 发送后聚焦到输入框，方便用户立即输入订单号
                                        setTimeout(() => {
                                            // 聚焦输入框并滚动到底部
                                            const textarea = document.querySelector('.message-textarea');
                                            if (textarea) {
                                                textarea.focus();
                                            }
                                            this.scrollToBottom();
                                            
                                            // 解除锁定
                                            this.orderQueryResponseLock = false;
                                        }, 200);
                                    }, 1500); // 延迟1.5秒，在欢迎语之后发送
                                } else {
                                    // 如果不需要发送订单查询提示，直接解锁
                                    this.orderQueryResponseLock = false;
                                }
                            }, 500);
                        } else if (this.settings.enableOrderQuery && this.settings.orderQueryPrompt) {
                            // 如果没有欢迎语，直接发送订单查询提示
                            setTimeout(() => {
                                this.sendMessageToServer(this.settings.orderQueryPrompt);
                                
                                // 发送后聚焦到输入框，方便用户立即输入订单号
                                setTimeout(() => {
                                    // 聚焦输入框并滚动到底部
                                    const textarea = document.querySelector('.message-textarea');
                                    if (textarea) {
                                        textarea.focus();
                                    }
                                    this.scrollToBottom();
                                    
                                    // 解除锁定
                                    this.orderQueryResponseLock = false;
                                }, 200);
                            }, 500);
                        } else {
                            // 如果两者都没启用，直接解锁
                            this.orderQueryResponseLock = false;
                        }
                    }
                },
                goToJoinRequests() {
                    window.location.href = '/plugin/Customersystem/user/joinRequestsPage';
                },
                checkSupplierStatus() {
                    axios.get('/plugin/Customersystem/user/checkSupplierStatus')
                        .then(response => {
                            if (response.data.code === 200) {
                                this.isSupplier = response.data.data.is_supplier || false;
                                
                                if (this.isSupplier) {
                                    this.loadPendingJoinRequests();
                                }
                            }
                        })
                        .catch(error => {

                    });
                },
                loadPendingJoinRequests() {
                    axios.get('/plugin/Customersystem/user/getPendingJoinRequestsCount')
                        .then(response => {
                            if (response.data.code === 200) {
                                this.pendingJoinRequests = response.data.data || 0;
                            }
                        })
                        .catch(error => {

                        });
                },
                openInviteSupplierDialog() {
                    // 加载可邀请的供货商列表
                    axios.get('/plugin/Customersystem/user/getAvailableSuppliers', {
                        params: {
                            session_id: this.currentSession.id
                        }
                    }).then(response => {
                        if(response.data.code === 200) {
                            // 从user表中获取供货商信息
                            this.availableSuppliers = response.data.data.map(supplier => {
                                return {
                                    id: supplier.id,
                                    name: supplier.nickname || supplier.username
                                };
                            });
                            // 初始化时设置过滤后的供货商列表等于所有供货商
                            this.filteredSuppliers = this.availableSuppliers;
                            this.inviteDialogVisible = true;
                        }
                    }).catch(error => {

                    });
                },
                inviteSupplier() {
                    const supplierId = this.inviteTabActive === 'merchant' 
                        ? this.selectedSupplier 
                        : this.selectedFoundSupplier;
                        
                    if (!supplierId) {
                        this.$message.warning('请选择要邀请的供货商');
                        return;
                    }
                    

                    
                    // 使用最简单的方式传递参数
                    const params = new URLSearchParams();
                    params.append('session_id', this.currentSession.id);
                    params.append('supplier_ids[]', supplierId);
                    params.append('reason', "商品咨询需要您的协助");
                    

                    
                    // 发送请求
                    axios.post('/plugin/Customersystem/user/inviteSuppliers', params)
                        .then(response => {

                            if(response.data.code === 200) {
                                this.$message.success('邀请已发送');
                                this.inviteDialogVisible = false;
                                // 重置数据
                                this.goodsNameInput = '';
                                this.foundSuppliers = [];
                                this.selectedSupplier = null;
                                this.selectedFoundSupplier = null;
                                
                                // 已移除会话参与者显示
                            } else {
                                this.$message.error(response.data.msg || '邀请失败');
                            }
                        })
                        .catch(error => {

                            if (error.response && error.response.data) {

                            }
                            this.$message.error('邀请失败，请稍后重试');
                        });
                },
                getSenderName(senderId) {
                    // 不再使用会话参与者列表获取名称
                    return '商家';
                    // 如果在参与者列表中找不到，则尝试根据消息类型判断
                    const message = this.messages.find(m => m.sender_id == senderId);
                    if (message) {
                        if (message.sender_type === 'customer' || message.role_type === 'customer') {
                            return '客户';
                        } else if (message.sender_type === 'staff' || message.role_type === 'staff') {
                            return '客服';
                        }
                    }
                    return '商家';
                },
                searchSupplierByGoods() {
                    if (!this.goodsNameInput.trim()) {
                        this.$message.warning('请输入商品名称');
                        return;
                    }
                    
                    axios.get('/plugin/Customersystem/user/getSuppliersByGoods', {
                        params: {
                            goods_name: this.goodsNameInput,
                            session_id: this.currentSession.id
                        }
                    }).then(response => {
                        if (response.data.code === 200) {
                            this.foundSuppliers = response.data.data || [];
                            if (this.foundSuppliers.length === 0) {
                                this.$message.info('未找到相关供货商');
                            }
                        } else {
                            this.$message.error(response.data.msg || '查询失败');
                        }
                    }).catch(error => {

                        this.$message.error('查询失败，请稍后重试');
                    });
                },
                selectSession(session) {

                    if (this.pollingInterval) {
                        clearInterval(this.pollingInterval);
                    }
                    
                    this.currentSession = session;
                    this.messages = [];
                    this.lastMessageId = 0;
                    
                    // 已移除会话参与者显示
                    
                    // 标记会话为已读
                    if (session.unread_count > 0) {
                        this.markSessionRead(session.id);
                    }
                    
                    // 加载会话消息
                    axios.get('/plugin/Customersystem/user/getSessionDetail', {
                        params: {
                            session_id: session.id
                        }
                    })
                    .then(response => {

                        
                        if (response.data.code === 200) {
                            // 兼容处理不同的数据结构
                            if (response.data.data && response.data.data.messages) {
                                this.messages = response.data.data.messages;
                            } else if (Array.isArray(response.data.data)) {
                                this.messages = response.data.data;
                            } else {
                                this.messages = [];
                            }
                            
                            // 更新最后一条消息ID，用于后续实时获取新消息
                            if (this.messages.length > 0) {
                                this.lastMessageId = Math.max(...this.messages.map(m => m.id));
                            }
                            
                            // 会话加载完成后滚动到底部
                            this.$nextTick(() => {
                                this.scrollToBottom();
                                
                                // 获取商家消息
                                const merchantMessages = this.messages.filter(m => 
                                    (m.role_type === 'merchant' || m.sender_type === 'staff') && !m.is_system);
                                
                                // 获取客户消息
                                const customerMessages = this.messages.filter(m => 
                                    (m.role_type === 'customer' || m.sender_type === 'customer') && !m.is_system);
                                
                                // 前端不再检查和自动发送预设问题，由后端自动处理
                                // if (merchantMessages.length === 0 && customerMessages.length > 0) {
                                //     this.checkAutoSendPresetQuestions();
                                // }
                                
                                // 标记消息已读
                                this.markMessagesRead(sessionId);
                            });
                            
                            // 启动轮询获取新消息
                            this.startMessagePolling();
                            
                            // 处理可能的订单查询等自动响应
                            this.handleAutoResponses(this.messages);
                        } else {
                            ElementPlus.ElMessage.error(response.data.msg || '加载会话失败');
                        }
                    })
                    .catch(error => {
                        ElementPlus.ElMessage.error('网络错误，请稍后重试');

                    });
                },
                
                // 会话参与者功能已移除
                
                // 检查当前用户是否为商家
                checkCurrentUserIsMerchant() {
                    if (!this.userId) return;
                    
                    // 使用异步请求检查用户是否为商家
                    axios.get('/plugin/Customersystem/user/checkSupplierStatus')
                        .then(response => {
                            if (response.data.code === 200 && response.data.data) {
                                this.isCurrentUserMerchant = response.data.data.is_supplier || false;

                            }
                        })
                        .catch(error => {

                        });
                },
                // 标记消息为已读
                markMessageRead(messageId) {
                    if (!this.currentSession) return;
                    
                    axios.post('/plugin/Customersystem/api/markMessageRead', {
                        message_id: messageId,
                        session_id: this.currentSession.id,
                        role_type: 'merchant'
                    })
                    .then(response => {
                        if (response.data.code === 200) {
                            // 更新本地消息状态
                            const index = this.messages.findIndex(msg => msg.id === messageId);
                            if (index !== -1) {
                                this.messages[index].is_read = 1;
                                this.messages[index].read_time = Math.floor(Date.now() / 1000);
                                // 更新商家特定已读状态
                                this.messages[index].merchant_read = 1;
                                this.messages[index].merchant_read_time = Math.floor(Date.now() / 1000);
                            }

                        }
                    })
                    .catch(error => {

                    });
                },
                
                // 批量标记会话消息为已读
                markAllMessagesRead() {
                    if (!this.currentSession) return;
                    
                    axios.post('/plugin/Customersystem/api/markSessionMessagesRead', {
                        session_id: this.currentSession.id,
                        role_type: 'merchant'
                    })
                    .then(response => {
                        if (response.data.code === 200) {
                            // 更新所有客户消息的已读状态
                            this.messages.forEach(msg => {
                                if ((msg.sender_type === 'customer' || msg.role_type === 'customer') && !msg.is_read) {
                                    msg.is_read = 1;
                                    msg.read_time = Math.floor(Date.now() / 1000);
                                    // 更新商家特定已读状态
                                    msg.merchant_read = 1;
                                    msg.merchant_read_time = Math.floor(Date.now() / 1000);
                                }
                            });

                            // 静默标记已读，无需提示
                        }
                    })
                    .catch(error => {

                    });
                },
                
                // 打开会话时自动标记客户消息为已读
                handleMessagesRead() {
                    if (!this.currentSession) return;
                    
                    // 找出需要标记为已读的消息
                    const unreadMessages = this.messages.filter(msg => 
                        !msg.is_read && (msg.sender_type === 'customer' || msg.role_type === 'customer')
                    );
                    
                    if (unreadMessages.length > 0) {
                        this.markAllMessagesRead();
                    }
                },
                // 新增方法：处理新消息的已读状态
                handleNewMessagesRead(newMessages) {
                    if (!newMessages || newMessages.length === 0) return;
                    
                    // 获取所有未读的客户消息ID
                    const customerMessageIds = newMessages
                        .filter(msg => (msg.sender_type === 'customer' || msg.role_type === 'customer') && !msg.is_read)
                        .map(msg => msg.id);
                        
                    if (customerMessageIds.length > 0) {
                        // 批量标记这些消息为已读
                        this.markAllMessagesRead();
                    }
                },
                // 处理图片拖放上传
                handleImageDrop(event) {
                    const files = event.dataTransfer.files;
                    if (files.length > 0 && files[0].type.startsWith('image/')) {
                        // 模拟选择文件
                        const fileInput = this.$refs.imageInputMerchant;
                        fileInput.files = files;
                        this.uploadImageMerchantApi({target: fileInput});
                    } else {
                        ElementPlus.ElMessage.warning('只能上传图片文件');
                    }
                },
                // 切换图片上传区域显示
                toggleUploadBox() {
                    this.showUploadBox = !this.showUploadBox;
                },
                remoteSearchMerchant(query) {
                    if (query !== '') {
                        this.merchantSelectLoading = true;
                        setTimeout(() => {
                            this.merchantSelectLoading = false;
                            this.filteredMerchants = this.availableMerchants.filter(merchant => {
                                return merchant.name.toLowerCase().includes(query.toLowerCase());
                            });
                        }, 200);
                    } else {
                        this.filteredMerchants = this.availableMerchants;
                    }
                },
                // 统一的已读状态判断方法
                getReadStatus(message) {
                    const currentRole = 'merchant'; // 商家端固定为merchant

                    if (message.role_type === currentRole || message.sender_type === currentRole) {
                        // 自己发送的消息，检查其他角色是否已读
                        const customerRead = message.customer_read === 1;
                        const staffRead = message.staff_read === 1;

                        if (customerRead && staffRead) {
                            return 'all_read'; // 全部已读
                        } else if (customerRead && !staffRead) {
                            return 'customer_read'; // 客户已读
                        } else if (!customerRead && staffRead) {
                            return 'staff_read'; // 客服已读
                        } else {
                            return 'unread'; // 全部未读
                        }
                    } else {
                        // 收到的消息，检查自己是否已读
                        return message.merchant_read === 1 ? 'read' : 'unread';
                    }
                },

                // 获取已读状态显示文本
                getReadStatusText(message) {
                    // 只为自己发送的消息显示已读状态
                    if (message.role_type !== 'merchant' && message.sender_type !== 'merchant') {
                        return '';
                    }

                    // 检查客户和客服是否已读
                    const customerRead = message.customer_read === 1;
                    const staffRead = message.staff_read === 1;

                    if (customerRead && staffRead) {
                        return '已读';
                    } else if (customerRead && !staffRead) {
                        return '客户已读';
                    } else if (!customerRead && staffRead) {
                        return '客服已读';
                    } else {
                        return '未读';
                    }
                },
                // 已移除会话参与者标签类型方法
                
                
                // 预设回复相关方法
                openPresetReplies() {
                    this.showPresetReplies = true;
                },
                
                closePresetReplies() {
                    this.showPresetReplies = false;
                },
                
                loadPresetReplies() {
                    axios.get('/plugin/Customersystem/user/getPresetReplies')
                        .then(response => {
                            if (response.data.code === 200) {
                                this.presetReplies = response.data.data || [];
                                // 获取所有快捷回复
                                this.quickReplies = this.presetReplies.filter(reply => reply.is_quick);
                                
                                // 提取所有标签
                                const tags = [];
                                this.presetReplies.forEach(reply => {
                                    if (reply.tags && Array.isArray(reply.tags)) {
                                        reply.tags.forEach(tag => {
                                            if (!tags.includes(tag)) {
                                                tags.push(tag);
                                            }
                                        });
                                    }
                                });
                                
                                // 合并标签
                                this.availableTags = [...new Set([...this.availableTags, ...tags])];
                            } else {
                                ElementPlus.ElMessage.error(response.data.msg || '获取预设回复失败');
                            }
                        })
                        .catch(error => {
                            ElementPlus.ElMessage.error('网络错误，请稍后重试');
                        });
                },
                
                addPresetReply() {
                    if (!this.newPresetReply.content) {
                        ElementPlus.ElMessage.warning('请输入回复内容');
                        return;
                    }
                    
                    axios.post('/plugin/Customersystem/user/savePresetReply', this.newPresetReply)
                        .then(response => {
                            if (response.data.code === 200) {
                                ElementPlus.ElMessage.success('添加成功');
                                this.showAddPresetDialog = false;
                                this.loadPresetReplies();
                                
                                // 重置表单
                                this.newPresetReply = {
                                    title: '',
                                    content: '',
                                    tags: [],
                                    is_quick: false
                                };
                            } else {
                                ElementPlus.ElMessage.error(response.data.msg || '添加失败');
                            }
                        })
                        .catch(error => {
                            ElementPlus.ElMessage.error('网络错误，请稍后重试');
                        });
                },
                
                usePresetReply(reply) {
                    this.messageContent = reply.content;
                    this.closePresetReplies();
                    
                    // 聚焦到输入框
                    this.$nextTick(() => {
                        const textarea = document.querySelector('.message-textarea');
                        if (textarea) {
                            textarea.focus();
                        }
                    });
                },
                
                deletePresetReply(replyId) {
                    ElementPlus.ElMessageBox.confirm('确认删除此预设回复？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        axios.post('/plugin/Customersystem/user/deletePresetReply', {
                            id: replyId
                        })
                        .then(response => {
                            if (response.data.code === 200) {
                                ElementPlus.ElMessage.success('删除成功');
                                this.loadPresetReplies();
                            } else {
                                ElementPlus.ElMessage.error(response.data.msg || '删除失败');
                            }
                        })
                        .catch(error => {
                            ElementPlus.ElMessage.error('网络错误，请稍后重试');
                        });
                    }).catch(() => {});
                },
                
                toggleQuickReply(reply) {
                    axios.post('/plugin/Customersystem/user/toggleQuickReply', {
                        id: reply.id,
                        is_quick: !reply.is_quick
                    })
                    .then(response => {
                        if (response.data.code === 200) {
                            ElementPlus.ElMessage.success(reply.is_quick ? '已取消快捷回复' : '已设为快捷回复');
                            this.loadPresetReplies();
                        } else {
                            ElementPlus.ElMessage.error(response.data.msg || '操作失败');
                        }
                    })
                    .catch(error => {
                        ElementPlus.ElMessage.error('网络错误，请稍后重试');
                    });
                },
                
                getSupplierByGoodsName(goodsName) {
                    // ... existing code ...
                },
                loadSessionMessages(sessionId) {

                    if (!sessionId) return;
                    
                    const url = '/plugin/Customersystem/user/getSessionDetail';
                    
                    axios.get(url, {
                        params: { id: sessionId }
                    })
                    .then(response => {
                        if (response.data.code === 200) {

                            
                            // 确保会话对象有必要的属性
                            const session = response.data.data.session || {};
                            this.currentSession = {
                                id: session.id,
                                title: session.title || '未命名会话',
                                status: session.status || 'open',
                                last_message: session.last_message || '',
                                last_time: session.last_time || new Date().getTime() / 1000,
                                unread_count: session.unread_count || 0,
                                source: session.source || 'merchant', // 添加source属性
                            };
                            
                            // 确保消息列表格式正确
                            const messages = response.data.data.messages || [];
                            this.messages = messages.map(msg => {
                                return this.standardizeMessage(msg);
                            });
                            
                            if (this.messages.length > 0) {
                                this.lastMessageId = Math.max(...this.messages.map(m => m.id));
                            } else {
                                this.lastMessageId = 0;
                            }
                            
                            this.$nextTick(() => {
                                this.scrollToBottom();
                                
                                // 获取商家消息
                                const merchantMessages = this.messages.filter(m => 
                                    (m.role_type === 'merchant' || m.sender_type === 'staff') && !m.is_system);
                                
                                // 获取客户消息
                                const customerMessages = this.messages.filter(m => 
                                    (m.role_type === 'customer' || m.sender_type === 'customer') && !m.is_system);
                                
                                // 前端不再检查和自动发送预设问题，由后端自动处理
                                // if (merchantMessages.length === 0 && customerMessages.length > 0) {
                                //     this.checkAutoSendPresetQuestions();
                                // }
                                
                                // 标记消息已读
                                this.markMessagesRead(sessionId);
                            });
                            
                            // 开始轮询新消息
                            this.startPolling(sessionId);
                            
                            // 加载会话参与者
                            this.loadSessionParticipants();
                        } else {
                            ElementPlus.ElMessage.error(response.data.msg || '获取会话详情失败');
                        }
                    })
                    .catch(error => {
                        ElementPlus.ElMessage.error('网络错误，请稍后重试');
                    });
                },
                
                // 标准化消息对象，确保所有字段都存在
                standardizeMessage(msg) {
                    return {
                        id: msg.id,
                        session_id: msg.session_id,
                        sender_type: msg.sender_type || 'system',
                        sender_id: msg.sender_id || 0,
                        role_type: msg.role_type || (msg.sender_type === 'customer' ? 'customer' : 'merchant'),
                        message_type: msg.message_type || msg.type || 'text',
                        message: msg.message || msg.content || '',
                        file_url: msg.file_url || '',
                        file_name: msg.file_name || '',
                        is_read: msg.is_read || 0,
                        is_system: msg.is_system || 0,
                        create_time: msg.create_time || new Date().getTime() / 1000,
                        imageLoaded: false,
                        imageError: false,
                        is_recalled: msg.is_recalled || 0
                    };
                },
                
                // 检查是否需要自动发送预设问题
                checkAutoSendPresetQuestions() {
                    // 获取设置，判断是否启用了自动发送预设问题
                    axios.get('/plugin/Customersystem/user/getPresetQuestionsConfig')
                    .then(response => {
                        if (response.data.code === 200) {
                            const config = response.data.data;
                            
                            // 如果设置为自动发送 - 注释掉自动发送的部分，因为后端会处理
                            if (config.autoSendPresetQuestions) {
                                // 检查是否已经发送过预设问题（查找消息中是否包含预设问题的HTML结构）
                                const hasPresetQuestions = this.messages.some(m => 
                                    m.message && m.message.includes('class="preset-questions"') &&
                                    (m.role_type === 'merchant' || m.sender_type === 'staff')
                                );
                                
                                // 前端不再主动发送预设问题，由后端自动处理
                                // if (!hasPresetQuestions) {
                                //     // 发送预设问题
                                //     setTimeout(() => {
                                //         this.sendPresetQuestions();
                                //     }, 1000); // 延迟1秒发送，避免连续多条消息
                                // }
                            }
                        }
                    })
                    .catch(error => {

                    });
                },
                showSettingsDialog() {
                    this.showSettingsDialog = !this.showSettingsDialog;
                },
                savePresetQuestionsConfig() {
                    // 保存预设问题配置到服务器
                    axios.post('/plugin/Customersystem/user/savePresetQuestionsConfig', {
                        autoSendPresetQuestions: this.presetConfig.autoSendPresetQuestions,
                        title: this.presetConfig.presetQuestions.title,
                        description: this.presetConfig.presetQuestions.description,
                        questions: this.presetConfig.presetQuestions.questions
                    })
                    .then(response => {
                        if (response.data.code === 200) {
                            ElementPlus.ElMessage.success('设置保存成功');
                            this.showSettingsDialog = false;
                        } else {
                            ElementPlus.ElMessage.error(response.data.msg || '保存设置失败');
                        }
                    })
                    .catch(error => {

                        ElementPlus.ElMessage.error('网络错误，请稍后重试');
                    });
                },
                removeQuestion(index) {
                    this.presetConfig.presetQuestions.questions.splice(index, 1);
                },
                addQuestion() {
                    this.presetConfig.presetQuestions.questions.push({
                        question: '',
                        answer: '',
                        enable_text: true,
                        enable_image: false,
                        enable_file: false,
                        image_url: '',
                        file_url: '',
                        file_name: ''
                    });
                },
                openSettingsDialog() {
                    // 先加载现有配置
                    axios.get('/plugin/Customersystem/user/getPresetQuestionsConfig')
                    .then(response => {
                        if (response.data.code === 200) {
                            const config = response.data.data;

                            // 兼容旧数据格式，转换为新格式
                            if (config.presetQuestions && config.presetQuestions.questions) {
                                config.presetQuestions.questions = config.presetQuestions.questions.map(item => {
                                    // 如果是旧格式（只有question和answer字段）
                                    if (typeof item === 'object' && !item.hasOwnProperty('enable_text')) {
                                        return {
                                            question: item.question || '',
                                            answer: item.answer || '',
                                            enable_text: true,
                                            enable_image: false,
                                            enable_file: false,
                                            image_url: '',
                                            file_url: '',
                                            file_name: ''
                                        };
                                    }
                                    // 如果是字符串格式
                                    else if (typeof item === 'string') {
                                        return {
                                            question: item,
                                            answer: '',
                                            enable_text: true,
                                            enable_image: false,
                                            enable_file: false,
                                            image_url: '',
                                            file_url: '',
                                            file_name: ''
                                        };
                                    }
                                    // 新格式，直接返回
                                    return item;
                                });
                            }

                            this.presetConfig = config;
                        }
                        this.showSettingsDialog = true;
                    })
                    .catch(error => {

                        ElementPlus.ElMessage.error('获取配置失败，请重试');
                    });
                },
                editPresetReply(reply) {
                    // 打开编辑对话框
                    this.editingPresetReply = {
                        id: reply.id,
                        title: reply.title || '',
                        content: reply.content || '',
                        tags: Array.isArray(reply.tags) ? [...reply.tags] : [],
                        is_quick: reply.is_quick || false
                    };
                    this.showEditPresetDialog = true;
                },
                
                // 添加更新预设回复方法
                updatePresetReply() {
                    if (!this.editingPresetReply.content) {
                        ElementPlus.ElMessage.warning('请输入回复内容');
                        return;
                    }
                    
                    axios.post('/plugin/Customersystem/user/updatePresetReply', this.editingPresetReply)
                        .then(response => {
                            if (response.data.code === 200) {
                                ElementPlus.ElMessage.success('更新成功');
                                this.showEditPresetDialog = false;
                                this.loadPresetReplies();
                            } else {
                                ElementPlus.ElMessage.error(response.data.msg || '更新失败');
                            }
                        })
                        .catch(error => {
                            ElementPlus.ElMessage.error('网络错误，请稍后重试');
                        });
                },
                // 添加媒体上传处理方法
                openMediaUpload(index, type) {
                    this.currentUploadingIndex = index;
                    this.currentUploadingType = type;
                    
                    // 重置input，确保change事件能够被触发
                    if (this.$refs.mediaUploadInput) {
                        this.$refs.mediaUploadInput.value = '';
                    }
                    
                    // 设置文件接受类型
                    if (type === 'image') {
                        this.$refs.mediaUploadInput.accept = 'image/*';
                    } else {
                        this.$refs.mediaUploadInput.accept = '*/*';
                    }
                    
                    // 触发文件选择
                    this.$refs.mediaUploadInput.click();
                },
                handleMediaUpload(event) {
                    if (!event.target.files || !event.target.files[0]) return;
                    
                    const file = event.target.files[0];
                    if (file.size > 20 * 1024 * 1024) {
                        ElementPlus.ElMessage.error('文件大小不能超过20MB');
                        return;
                    }
                    
                    const formData = new FormData();
                    formData.append('file', file); // 使用'file'作为文件名称
                    
                    // 显示上传中提示
                    const loadingInstance = ElementPlus.ElLoading.service({
                        text: '上传中...'
                    });
                    
                    // 使用官方商户端上传API
                    axios.post('/merchantApi/Upload/file', formData, {
                        headers: {
                            'Content-Type': 'multipart/form-data'
                        }
                    })
                    .then(response => {
                        loadingInstance.close();
                        
                        if (response.data.code === 200 || response.data.code === 1) {
                            // 设置上传后的URL到对应的问题项
                            if (this.currentUploadingIndex !== -1 && this.presetConfig.presetQuestions.questions[this.currentUploadingIndex]) {
                                const question = this.presetConfig.presetQuestions.questions[this.currentUploadingIndex];

                                // 获取返回的URL路径
                                const fileUrl = response.data.data.url || response.data.data;

                                // 根据类型设置不同的字段
                                if (this.currentUploadingType === 'image') {
                                    question.image_url = fileUrl;
                                } else if (this.currentUploadingType === 'file') {
                                    question.file_url = fileUrl;
                                    question.file_name = file.name;
                                }

                                ElementPlus.ElMessage.success('上传成功');
                            }
                        } else {
                            ElementPlus.ElMessage.error(response.data.msg || '上传失败');
                        }
                    })
                    .catch(error => {
                        loadingInstance.close();
                        ElementPlus.ElMessage.error('上传失败，请稍后重试');

                    });
                },
            }
        });

        app.use(ElementPlus, {
            locale: ElementPlusLocaleZhCn
        });
        
        app.mount('#app');
    </script>
</body>
</html>