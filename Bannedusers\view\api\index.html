<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>禁用用户列表</title>
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <link rel="icon" href="<?php echo $favicon; ?>" type="image/x-icon">
    <script>
        // 禁用右键菜单
        document.oncontextmenu = function(e) {
            e.preventDefault();
            return false;
        };

        // 禁用 F12、Ctrl+Shift+I、Ctrl+Shift+J、Ctrl+Shift+C
        document.onkeydown = function(e) {
            if (
                e.keyCode === 123 || // F12
                (e.ctrlKey && e.shiftKey && e.keyCode === 73) || // Ctrl+Shift+I
                (e.ctrlKey && e.shiftKey && e.keyCode === 74) || // Ctrl+Shift+J
                (e.ctrl<PERSON><PERSON> && e.shift<PERSON>ey && e.keyCode === 67) // Ctrl+Shift+C
            ) {
                e.preventDefault();
                return false;
            }
        };

        // 检测开发者工具状态
        (function() {
            let devtools = {
                open: false,
                orientation: null
            };
            
            const threshold = 160;
            const emitEvent = (isOpen, orientation) => {
                devtools.open = isOpen;
                devtools.orientation = orientation;
            };

            setInterval(function() {
                const widthThreshold = window.outerWidth - window.innerWidth > threshold;
                const heightThreshold = window.outerHeight - window.innerHeight > threshold;
                const orientation = widthThreshold ? 'vertical' : 'horizontal';

                if (
                    !(heightThreshold && widthThreshold) &&
                    ((window.Firebug && window.Firebug.chrome && window.Firebug.chrome.isInitialized) || widthThreshold || heightThreshold)
                ) {
                    emitEvent(true, orientation);
                } else {
                    emitEvent(false, null);
                }
            }, 500);

            setInterval(function() {
                devtools.opened = false;
                console.clear();
                if(devtools.open) {
                    window.location.href = '/';
                }
            }, 1000);

            console.log = console.warn = console.error = function() {};
        })();

        // 防止通过 debug 功能调试
        setInterval(function() {
            debugger;
        }, 100);
    </script>
    <style>
        /* 将 v-cloak 样式移到最前面并确保它具有足够的优先级 */
        [v-cloak] {
            display: none !important;
        }

        /* 添加页面加载时的loading样式 */
        .page-loading {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: #f5f7fa;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        /* 确保在Vue加载完成前显示loading */
        .init-loading {
            display: flex;
            min-height: 100vh;
            align-items: center;
            justify-content: center;
            background: #f5f7fa;
        }
        
        /* 全局样式 */
        body {
            margin: 0;
            background-color: #f5f7fa;
        }

        /* 头部导航样式 */
        .header {
            background: #fff;
            box-shadow: 0 1px 4px rgba(0,0,0,0.05);
            padding: 16px 24px;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .nav-left {
            display: flex;
            align-items: center;
            gap: 40px;
        }

        .logo-container {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
        }

        .logo-img {
            height: 28px;
            width: auto;
            transition: transform 0.3s ease;
        }

        .logo-container:hover .logo-img {
            transform: scale(1.05);
        }

        .nav-menu {
            display: flex;
            gap: 32px;
        }

        .nav-popover {
            padding: 0;
            min-width: 120px;
        }

        .nav-submenu {
            display: flex;
            flex-direction: column;
            background: white;
        }

        .nav-subitem {
            padding: 8px 16px;
            color: #606266;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s;
        }

        .nav-subitem:hover {
            background-color: #f5f7fa;
            color: var(--el-color-primary);
        }

        .nav-subitem.active {
            color: var(--el-color-primary);
            background-color: #ecf5ff;
        }

        .nav-item {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            color: #606266;
            text-decoration: none;
            font-size: 14px;
            position: relative;
            padding: 4px 0;
            transition: color 0.3s ease;
        }

        .nav-item .el-icon {
            color: #303133;
            font-size: 12px;
            margin-top: 1px;
            margin-left: 4px;
            transition: transform 0.3s;
        }

        .nav-item:hover {
            color: var(--el-color-primary);
        }

        .nav-item.active {
            color: var(--el-color-primary);
            font-weight: 500;
        }

        .nav-item.active::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 2px;
            background: var(--el-color-primary);
        }

        /* 主要内容区域 */
        .main-container {
            flex: 1;
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
            padding: 24px;
            box-sizing: border-box; /* 确保padding计入宽度 */
        }

        .el-card {
            margin-bottom: 24px;
        }

        /* 用户列表样式 */
        .user-list {
            padding: 8px;
        }

        .user-card {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 12px;
            width: 100%;
        }

        .user-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            object-fit: cover;
        }

        .user-info {
            flex: 1;
        }

        .user-name {
            font-weight: 500;
            color: #303133;
            margin-bottom: 4px;
        }

        .user-nickname {
            color: #909399;
            font-size: 13px;
        }

        .risk-badge {
            font-size: 12px;
            padding: 0 8px;
            height: 24px;
            line-height: 24px;
        }

        .risk-detail {
            padding: 16px 24px;
            background: #fafafa;
            border-radius: 4px;
            margin: 8px 12px;
        }

        .risk-content {
            color: #f56c6c;
        }

        .risk-title {
            font-weight: 500;
            margin-bottom: 8px;
            color: #303133;
        }

        .risk-text {
            line-height: 1.6;
            font-size: 14px;
            white-space: pre-wrap;
        }

        .no-risk {
            color: #909399;
            font-size: 14px;
            text-align: center;
            padding: 16px;
        }

        /* 空状态样式 */
        .empty-state {
            text-align: center;
            padding: 40px;
            color: #909399;
        }

        /* 响应式处理 */
        @media (max-width: 768px) {
            .nav-container {
                flex-direction: column;
                gap: 16px;
            }

            .nav-left {
                width: 100%;
                justify-content: space-between;
            }

            .nav-menu {
                gap: 24px;
            }
        }

        /* Element Plus 折叠面板样式优化 */
        :deep(.el-collapse-item__header) {
            padding: 0;
            border: none;
        }

        :deep(.el-collapse-item__content) {
            padding-bottom: 16px;
        }

        :deep(.el-collapse-item__wrap) {
            border: none;
        }

        :deep(.el-collapse) {
            border: none;
        }

        :deep(.el-collapse-item) {
            background: #fff;
            border-radius: 8px;
            margin-bottom: 12px;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
        }

        /* 表格样式优化 */
        .user-info-cell {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .username {
            font-size: 14px;
            color: #303133;
        }

        .risk-info-cell {
            padding: 4px 0;
        }

        .el-table {
            --el-table-border-color: #EBEEF5;
            --el-table-header-bg-color: #F5F7FA;
        }

        /* 确保表格内容垂直居中 */
        .el-table .cell {
            display: flex;
            align-items: center;
        }

        /* 标签样式优化 */
        .el-tag {
            max-width: 100%;
            white-space: normal;
            height: auto;
            padding: 4px 8px;
            line-height: 1.4;
        }

        .violation-record {
            padding: 8px 0;
            border-bottom: 1px solid #EBEEF5;
        }

        .violation-record:last-child {
            border-bottom: none;
        }

        .violation-time {
            font-size: 12px;
            color: #909399;
            margin-bottom: 4px;
        }

        .violation-content {
            color: #303133;
            font-size: 14px;
            line-height: 1.4;
        }

        .user-detail {
            padding: 20px;
        }

        .pagination-container {
            margin-top: 20px;
            display: flex;
            justify-content: flex-end;
            padding: 10px 0;
        }

        /* 添加过渡动画效果 */
        .user-list {
            transition: all 0.3s ease;
        }
        
        .el-table__row {
            transition: all 0.3s ease;
        }

        /* 警告图标动画样式 */
        .warning-icon-container {
            position: fixed;
            left: 24px;
            bottom: 24px;
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(255, 247, 237, 0.9);
            padding: 12px 16px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid #ffd591;
            animation: slideIn 0.5s ease-out, pulse 2s infinite;
            z-index: 99;
        }

        .warning-icon {
            color: #ff9800;
            font-size: 20px;
            animation: rotate 2s infinite;
        }

        .warning-text {
            color: #d48806;
            font-size: 14px;
        }

        @keyframes slideIn {
            from {
                transform: translateX(-100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
            100% {
                transform: scale(1);
            }
        }

        @keyframes rotate {
            0% {
                transform: rotate(0deg);
            }
            25% {
                transform: rotate(-15deg);
            }
            75% {
                transform: rotate(15deg);
            }
            100% {
                transform: rotate(0deg);
            }
        }

        /* 添加响应式样式 */
        @media screen and (max-width: 768px) {
            /* 导航栏响应式 */
            .nav-container {
                flex-direction: column;
                padding: 12px;
            }

            .nav-left {
                width: 100%;
                flex-direction: column;
                gap: 16px;
            }

            .nav-menu {
                width: 100%;
                flex-wrap: wrap;
                justify-content: center;
                gap: 16px;
            }

            /* 主容器响应式 */
            .main-container {
                padding: 12px;
            }

            /* 表格响应式 */
            .el-table {
                width: 100%;
                font-size: 13px;
            }

            /* 分页组件响应式 */
            .pagination-container {
                justify-content: center;
            }

            .el-pagination {
                flex-wrap: wrap;
                justify-content: center;
            }

            /* 警告图标容器响应式 */
            .warning-icon-container {
                left: 12px;
                right: 12px;
                bottom: 12px;
                width: auto;
            }

            /* 对话框响应式 */
            .el-dialog {
                width: 90% !important;
                margin: 0 auto !important;
            }

            /* 用户信息单元格响应式 */
            .user-info-cell {
                flex-direction: column;
                gap: 8px;
                align-items: flex-start;
            }

            .user-avatar {
                margin: 0 auto;
            }
        }

        /* 针对更小屏幕的优化 */
        @media screen and (max-width: 480px) {
            .nav-menu {
                gap: 12px;
            }

            .el-table {
                font-size: 12px;
            }

            .el-button {
                padding: 8px 12px;
            }

            .warning-icon-container {
                font-size: 12px;
                padding: 8px 12px;
            }

            .el-descriptions-item {
                margin-bottom: 8px;
            }

            .el-tag {
                display: inline-block;
                margin: 4px 0;
            }
        }

        /* 添加到现有样式中 */
        .nav-item .el-icon {
            color: #303133; /* Element Plus 的默认黑色 */
        }

        /* 当菜单展开时旋转倒三角 */
        .el-popover__reference-wrapper:hover .el-icon {
            transform: rotate(180deg);
        }

        /* 确保倒三角和文字在同一行 */
        .nav-item {
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        /* 添加新的样式 */
        .risk-info-cell {
            padding: 4px 0;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .risk-reason {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .punishment-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .punishment-detail {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;
        }

        .ml-2 {
            margin-left: 8px;
        }

        /* 响应式调整 */
        @media screen and (max-width: 768px) {
            .risk-info-cell {
                gap: 4px;
            }

            .punishment-detail {
                flex-direction: column;
                align-items: flex-start;
                gap: 4px;
            }

            .ml-2 {
                margin-left: 0;
                margin-top: 4px;
            }
        }

        .risk-info-cell {
            padding: 4px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* 调整表格列宽度 */
        .el-table .cell {
            white-space: normal;
            word-break: break-word;
            line-height: 1.5;
        }

        /* 响应式调整 */
        @media screen and (max-width: 768px) {
            .risk-info-cell {
                flex-wrap: wrap;
            }
        }

        /* 主容器布局优化 */
        #app {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            overflow-x: hidden; /* 防止水平滚动条 */
        }

        /* 页脚样式优化 */
        .risk-footer-container {
            width: 100%;
            background: #f5f7fa;
            border-top: 1px solid #e4e7ed;
            margin-top: auto;  /* 将页脚推到底部 */
            padding: 20px 0;
            box-sizing: border-box;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
            box-sizing: border-box;
            width: 100%;
        }

        .footer-main {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
            gap: 12px;
            color: #606266;
            font-size: 14px;
        }

        .site-name {
            color: #409EFF;
            font-weight: 500;
        }

        .footer-link {
            color: #606266;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-link:hover {
            color: #409EFF;
        }

        .divider {
            color: #dcdfe6;
        }

        .icp-cert {
            color: #606266;
        }

        /* 移动端适配 */
        @media screen and (max-width: 768px) {
            .risk-footer-container {
                padding: 16px;
            }
            
            .footer-main {
                flex-direction: column;
                gap: 8px;
            }
            
            .divider {
                display: none;
            }
        }
    </style>
</head>

<body>
    <!-- 添加初始loading -->
    <div class="init-loading" id="init-loading">
        <div class="el-loading-spinner">
            <svg class="circular" viewBox="25 25 50 50">
                <circle class="path" cx="50" cy="50" r="20" fill="none"/>
            </svg>
            <p class="el-loading-text">页面加载中...</p>
        </div>
    </div>

    <div id="app" v-cloak>
        <!-- 头部导航 -->
        <div class="header">
            <div class="nav-container">
                <div class="nav-left">
                    <div class="logo-container">
                        <img :src="logo" class="logo-img" alt="Logo">
                    </div>
                    <div class="nav-menu">
                        <template v-for="(item, index) in navItems" :key="index">
                            <!-- 有子菜单的导航项目 -->
                            <template v-if="item.subMenus && item.subMenus.length > 0">
                                <el-popover
                                    placement="bottom"
                                    :width="120"
                                    trigger="hover"
                                    popper-class="nav-popover"
                                >
                                    <template #reference>
                                        <span class="nav-item" :class="{ active: isActiveParent(item) }">
                                            {{ item.name }}
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" style="fill: currentColor;">
                                                <path d="M12 15.375l-6.375-6.375h12.75z"/>
                                            </svg>
                                        </span>
                                    </template>
                                    <div class="nav-submenu">
                                        <!-- 子菜单已经按sort排序，直接遍历显示 -->
                                        <template v-for="subItem in item.subMenus" :key="subItem.href">
                                            <a :href="subItem.href" 
                                               class="nav-subitem"
                                               :class="{ active: isCurrentPage(subItem.href) }"
                                               v-if="menuStatus[getMenuKey(subItem.href)]">
                                                {{ subItem.name }}
                                            </a>
                                        </template>
                                    </div>
                                </el-popover>
                            </template>
                            <!-- 兼容旧代码：特定的"黑名单查询"菜单 -->
                            <template v-else-if="item.name === '黑名单查询' && item.subMenus">
                                <el-popover
                                    placement="bottom"
                                    :width="120"
                                    trigger="hover"
                                    popper-class="nav-popover"
                                >
                                    <template #reference>
                                        <span class="nav-item" :class="{ active: isActiveParent(item) }">
                                            {{ item.name }}
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" style="fill: currentColor;">
                                                <path d="M12 15.375l-6.375-6.375h12.75z"/>
                                            </svg>
                                        </span>
                                    </template>
                                    <div class="nav-submenu">
                                        <!-- 子菜单已经按sort排序，直接遍历显示 -->
                                        <template v-for="subItem in item.subMenus" :key="subItem.href">
                                            <a :href="subItem.href" 
                                               class="nav-subitem"
                                               :class="{ active: isCurrentPage(subItem.href) }"
                                               v-if="menuStatus[getMenuKey(subItem.href)]">
                                                {{ subItem.name }}
                                            </a>
                                        </template>
                                    </div>
                                </el-popover>
                            </template>
                            <!-- 其他顶级菜单项 -->
                            <template v-else>
                                <a :href="item.href" 
                                   class="nav-item" 
                                   :class="{ active: isCurrentPage(item.href) }">
                                    {{ item.name }}
                                </a>
                            </template>
                        </template>
                    </div>
                </div>
                <el-button type="primary" @click="goToMerchant">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" style="fill: currentColor; margin-right: 4px;">
                        <path d="M12 4a4 4 0 1 0 0 8 4 4 0 0 0 0-8zM6 8a6 6 0 1 1 12 0A6 6 0 0 1 6 8zm2 10a3 3 0 0 0-3 3 1 1 0 1 1-2 0 5 5 0 0 1 5-5h8a5 5 0 0 1 5 5 1 1 0 1 1-2 0 3 3 0 0 0-3-3H8z"/>
                    </svg>
                    商家中心
                </el-button>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-container">
            <el-card shadow="never">
                <template #header>
                    <div class="card-header">
                        <span>风控记录</span>
                    </div>
                </template>

                <div v-if="users.length > 0" class="user-list">
                    <el-table 
                        :data="users" 
                        border 
                        style="width: 100%" 
                        v-loading="loading"
                        :size="isMobile ? 'small' : 'default'"
                        :cell-style="{ padding: isMobile ? '4px' : '8px' }">
                        <el-table-column label="排名" width="70" align="center">
                            <template #default="scope">
                                <span :style="getRankStyle(scope.$index + 1)">
                                    {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
                                </span>
                            </template>
                        </el-table-column>

                        <el-table-column label="商家账户" min-width="180">
                            <template #default="scope">
                                <div class="user-info-cell">
                                    <el-avatar 
                                        :size="32" 
                                        :src="scope.row.avatar || '/static/images/avatar.png'"
                                        class="user-avatar">
                                    </el-avatar>
                                    <div style="display: flex; flex-direction: column;">
                                        <span class="username">{{ maskString(scope.row.username) }}</span>
                                        <span v-if="scope.row.nickname" style="font-size: 12px; color: #909399;">
                                            {{ maskString(scope.row.nickname) }}
                                        </span>
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                        
                        <el-table-column label="违规原因" min-width="200">
                            <template #default="scope">
                                <div class="risk-info-cell">
                                    <el-tag 
                                        v-if="scope.row.risk_content" 
                                        type="danger" 
                                        effect="light">
                                        {{ scope.row.risk_content }}
                                    </el-tag>
                                    <el-tag v-else type="info" effect="light">暂未说明违规理由</el-tag>
                                </div>
                            </template>
                        </el-table-column>

                        <el-table-column label="处罚情况" min-width="100">
                            <template #default="scope">
                                <div class="risk-info-cell">
                                    <el-tag 
                                        :type="scope.row.risk_type === 1 ? 'warning' : 'error'"
                                        effect="dark"
                                        size="small">
                                        {{ scope.row.risk_type === 1 ? '关闭交易' : '封禁店铺' }}
                                    </el-tag>
                                </div>
                            </template>
                        </el-table-column>
                        
                        <el-table-column label="操作" width="120" align="center">
                            <template #default="scope">
                                <el-button 
                                    type="primary" 
                                    link
                                    size="small"
                                    @click="viewDetails(scope.row)">
                                    查看详情 <el-icon><ArrowRight /></el-icon>
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    
                    <!-- 添加分页组件 -->
                    <div class="pagination-container">
                        <el-pagination
                            v-model:current-page="currentPage"
                            v-model:page-size="pageSize"
                            :page-sizes="[10, 20, 30, 50]"
                            :total="total"
                            layout="total, sizes, prev, pager, next, jumper"
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                        />
                    </div>
                </div>
                <div v-else class="empty-state">
                    暂无禁用商户
                </div>
            </el-card>

            <!-- 添加用户详情对话框 -->
            <el-dialog
                v-model="dialogVisible"
                :title="'商家违禁详情'"
                :width="isMobile ? '95%' : '50%'"
                :fullscreen="isMobile"
                :close-on-click-modal="false">
                <div v-if="currentUser" class="user-detail">
                    <el-descriptions :column="1" border>
                        <el-descriptions-item label="商家账户">
                            {{ maskString(currentUser.username) }}
                            <el-tag v-if="currentUser.nickname" size="small" type="info" class="ml-2">
                                店铺名称：{{ maskString(currentUser.nickname) }}
                            </el-tag>
                        </el-descriptions-item>
                        <el-descriptions-item label="注册时间">
                            {{ currentUser.register_time }}
                        </el-descriptions-item>
                        <el-descriptions-item label="禁用时间">
                            {{ currentUser.banned_time }}
                        </el-descriptions-item>
                        <el-descriptions-item label="违规原因">
                            <el-tag type="danger" effect="light">
                                {{ currentUser.risk_content || '暂未说明违规理由' }}
                            </el-tag>
                        </el-descriptions-item>
                        <el-descriptions-item label="处罚情况">
                            <el-tag 
                                :type="currentUser.risk_type === 1 ? 'warning' : 'error'"
                                effect="dark"
                                size="small">
                                {{ currentUser.risk_type === 1 ? '关闭交易' : '封禁店铺' }}
                            </el-tag>
                        </el-descriptions-item>
                    </el-descriptions>
                </div>
            </el-dialog>

            <!-- 在 main-container 内添加警告图标组件 -->
            <div class="warning-icon-container">
                <el-icon class="warning-icon">
                    <WarningFilled />
                </el-icon>
                <span class="warning-text">当前共有 {{ total }} 个违规商家</span>
            </div>
        </div>

        <!-- 添加页脚 -->
        <footer class="risk-footer-container">
            <div class="footer-content">
                <div class="footer-main">
                    <span class="site-name">Powered by {{ siteName }}</span>
                    <span class="divider">|</span>
                    <span class="icp-cert">{{ icpNumber }}</span>
                    <span class="divider">|</span>
                    <a :href="'https://beian.miit.gov.cn'" target="_blank" class="footer-link">增值电信经营许可证(ICP/EDI):{{ icpCert }}</a>
                    <span class="divider">|</span>
                    <a :href="'http://www.beian.gov.cn'" target="_blank" class="footer-link">
                        <img src="/assets/template/default/assets/wangan.a20583c8.png" alt="公安备案图标" style="vertical-align: -2px; margin-right: 3px; width: 16px; height: 16px;">
                        {{ gaNumber }}
                    </a>
                </div>
            </div>
        </footer>
    </div>

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>

    <script>
        // 在Vue实例创建之前添加
        window.addEventListener('load', function() {
            const initLoading = document.getElementById('init-loading');
            if (initLoading) {
                initLoading.style.display = 'none';
            }
        });

        const { createApp, ref, onMounted, onUnmounted } = Vue;

        createApp({
            setup() {
                const users = ref([]);
                const currentPage = ref(1);
                const pageSize = ref(10);
                const total = ref(0);
                const loading = ref(false);
                const logo = ref('<?php echo addslashes($logo); ?>');
                const siteName = ref('<?php echo addslashes($siteName); ?>');
                const icpNumber = ref('<?php echo addslashes($icpNumber); ?>');
                const gaNumber = ref('<?php echo addslashes($gaNumber); ?>');
                const icpCert = ref('<?php echo addslashes($icpCert); ?>');
                let timer = null;
                const dialogVisible = ref(false);
                const currentUser = ref(null);
                const isMobile = ref(window.innerWidth <= 768);
                const templateType = ref('default');  // 添加模板类型响应式变量
                const menuStatus = ref({
                    index: true,
                    complaints: true,
                    bannedRecords: true
                });
                
                // 加载用户数据
                const loadUsers = async () => {
                    if (loading.value) return; // 防止重复加载
                    loading.value = true;
                    
                    try {
                        const response = await axios.get('/plugin/Bannedusers/Api/getBannedUsers', {
                            params: {
                                page: currentPage.value,
                                limit: pageSize.value
                            }
                        });
                        
                        if (response.data.items) {
                            // 使用 Vue 的响应式更新方法
                            users.value = [...response.data.items];
                            total.value = response.data.total;
                        }
                    } catch (error) {
                        console.error('获取用户列表失败:', error);
                        ElMessage.error('获取数据失败，请稍后重试');
                    } finally {
                        loading.value = false;
                    }
                };
                
                const goToMerchant = () => {
                    window.location.href = '/merchant/login';
                };
                
                const formatDate = (timestamp) => {
                    if (!timestamp) return '未知';
                    // 如果已经是格式化的时间字符串，直接返回
                    if (typeof timestamp === 'string') return timestamp;
                    // 如果是时间戳，则进行格式化
                    const date = new Date(timestamp * 1000);
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    const hours = String(date.getHours()).padStart(2, '0');
                    const minutes = String(date.getMinutes()).padStart(2, '0');
                    const seconds = String(date.getSeconds()).padStart(2, '0');
                    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
                };
                
                const viewDetails = (user) => {
                    currentUser.value = user;
                    dialogVisible.value = true;
                };
                
                // 处理每页显示数量变化
                const handleSizeChange = (val) => {
                    pageSize.value = val;
                    currentPage.value = 1; // 重置到第一页
                    loadUsers();
                };
                
                // 处理页码变化
                const handleCurrentChange = (val) => {
                    currentPage.value = val;
                    loadUsers();
                };
                
                // 修改状态检查逻辑
                let statusCheckTimer = null;
                const checkUserStatus = async (userId) => {
                    try {
                        const response = await axios.get('/plugin/Bannedusers/Api/checkUserStatus', {
                            params: { userId }
                        });
                        
                        if (response.data.code === 1) {
                            const shouldRemove = !response.data.isBanned && !response.data.isTradeDisabled;
                            
                            if (shouldRemove) {
                                const userIndex = users.value.findIndex(user => user.id === userId);
                                if (userIndex !== -1) {
                                    // 使用 Vue 的响应式数组方法
                                    users.value = users.value.filter(user => user.id !== userId);
                                    if (total.value > 0) total.value--;
                                    
                                    // 如果当前页没有数据了，重新加载
                                    if (users.value.length === 0) {
                                        if (currentPage.value > 1) {
                                            currentPage.value--;
                                        }
                                        await loadUsers();
                                    }
                                }
                            }
                        }
                    } catch (error) {
                        console.error('检查用户状态失败:', error);
                    }
                };

                // 修改定时器逻辑
                const startStatusCheck = () => {
                    if (statusCheckTimer) {
                        clearInterval(statusCheckTimer);
                    }
                    
                    // 延长检查间隔到30秒
                    statusCheckTimer = setInterval(() => {
                        if (users.value && users.value.length > 0) {
                            users.value.forEach(user => {
                                checkUserStatus(user.id);
                            });
                        }
                    }, 30000);
                };

                const handleResize = () => {
                    isMobile.value = window.innerWidth <= 768;
                };

                // 组件挂载时的处理
                onMounted(() => {
                    // 确保Vue加载完成后移除loading
                    const initLoading = document.getElementById('init-loading');
                    if (initLoading) {
                        initLoading.style.display = 'none';
                    }
                    
                    loadUsers();
                    startStatusCheck();
                    window.addEventListener('resize', handleResize);
                    getMenuStatus();
                });

                // 组件卸载时的清理
                onUnmounted(() => {
                    if (statusCheckTimer) {
                        clearInterval(statusCheckTimer);
                    }
                    window.removeEventListener('resize', handleResize);
                });
                
                // 添加脱敏方法
                const maskString = (str) => {
                    if (!str) return '';
                    if (str.length <= 2) return '*'.repeat(str.length);
                    const len = str.length;
                    const middle = Math.floor(len / 2);
                    const maskLen = Math.max(1, middle);
                    return str.substring(0, Math.floor((len - maskLen) / 2)) 
                        + '*'.repeat(maskLen) 
                        + str.substring(Math.ceil((len + maskLen) / 2));
                };

                // 添加导航相关的数据和方法
                const navItems = ref(JSON.parse('<?php echo $navItemsJson; ?>'));
                
                const isCurrentPage = (href) => {
                    return window.location.pathname === href;
                };

                const isActiveParent = (item) => {
                    // 检查是否存在子菜单
                    if (item.subMenus && item.subMenus.length > 0) {
                        // 获取子菜单的href数组
                        const subPaths = item.subMenus.map(sub => sub.href);
                        // 检查当前页面路径是否在子菜单中
                        return subPaths.includes(window.location.pathname);
                    }
                    
                    // 兼容旧代码，如果是名称为"黑名单查询"的菜单
                    if (item.name === '黑名单查询') {
                        const paths = [
                            '/plugin/Bannedusers/Api/index',
                            '/plugin/Bannedusers/Api/riskControl',
                            '/plugin/Bannedusers/Api/complaints',
                            '/plugin/Bannedusers/Api/bannedRecords'
                        ];
                        return paths.includes(window.location.pathname);
                    }
                    
                    return isCurrentPage(item.href);
                };

                // 添加排名样式方法
                const getRankStyle = (rank) => {
                    if (rank <= 3) {
                        const colors = {
                            1: '#FF4E50',  // 第一名 - 红色
                            2: '#FF8C00',  // 第二名 - 橙色
                            3: '#FFD700'   // 第三名 - 金色
                        };
                        return {
                            color: colors[rank],
                            fontWeight: 'bold',
                            fontSize: '16px'
                        };
                    }
                    return {
                        color: '#606266'  // 默认颜色
                    };
                };

                // 获取当前模板类型
                const getTemplateType = async () => {
                    try {
                        const response = await axios.get('/plugin/Bannedusers/Api/getTemplate');
                        if (response.data.code === 1) {
                            templateType.value = response.data.type;
                        }
                    } catch (error) {
                        console.error('获取模板类型失败:', error);
                    }
                };

                // 根据当前模板类型返回对应的路径
                const getMenuPath = (type) => {
                    if (type === 'index') {
                        return templateType.value === 'risk' 
                            ? '/plugin/Bannedusers/Api/riskControl'
                            : '/plugin/Bannedusers/Api/index';
                    }
                    return `/plugin/Bannedusers/Api/${type}`;
                };

                // 获取菜单key
                const getMenuKey = (href) => {
                    // 处理插件路径
                    if (href && href.startsWith('/plugin/Bannedusers/Api/')) {
                        const path = href.split('/').pop();
                        // 特殊处理 index 和 riskControl
                        if (path === 'index' || path === 'riskControl') {
                            return 'index';
                        }
                        return path;
                    }
                    
                    // 兼容旧代码的固定路径映射
                    const pathMap = {
                        '/plugin/Bannedusers/Api/riskControl': 'index',
                        '/plugin/Bannedusers/Api/index': 'index',
                        '/plugin/Bannedusers/Api/complaints': 'complaints',
                        '/plugin/Bannedusers/Api/bannedRecords': 'bannedRecords'
                    };
                    return pathMap[href] || '';
                };

                // 修改获取菜单状态的方法
                const getMenuStatus = async () => {
                    try {
                        const response = await axios.get('/plugin/Bannedusers/Api/checkNav');
                        if (response.data.code === 1) {
                            menuStatus.value = response.data.status;
                            
                            // 更新导航菜单数据，包含已排序的菜单
                            if (response.data.menus) {
                                navItems.value = response.data.menus;
                            }
                            
                            // 更新父菜单及其子菜单
                            if (response.data.parentMenu) {
                                // 查找是否已经存在该菜单
                                const parentIndex = navItems.value.findIndex(
                                    item => item.id === response.data.parentMenu.id
                                );
                                
                                if (parentIndex !== -1) {
                                    // 如果找到，直接更新子菜单
                                    navItems.value[parentIndex].subMenus = response.data.parentMenu.subMenus;
                                } else {
                                    // 如果没找到，查找名称匹配的菜单
                                    const nameIndex = navItems.value.findIndex(
                                        item => item.name === response.data.parentMenu.name
                                    );
                                    
                                    if (nameIndex !== -1) {
                                        navItems.value[nameIndex].subMenus = response.data.parentMenu.subMenus;
                                    } else {
                                        // 如果仍然找不到，则寻找没有 href 的菜单项(通常是 javascript:;)
                                        for (let i = 0; i < navItems.value.length; i++) {
                                            if (navItems.value[i].href === 'javascript:;' || !navItems.value[i].href) {
                                                navItems.value[i].subMenus = response.data.parentMenu.subMenus;
                                                break;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    } catch (error) {
                        console.error('获取菜单状态失败:', error);
                    }
                };

                return {
                    users,
                    logo,
                    siteName,
                    icpNumber,
                    gaNumber,
                    icpCert,
                    goToMerchant,
                    dialogVisible,
                    currentUser,
                    viewDetails,
                    formatDate,
                    currentPage,
                    pageSize,
                    total,
                    loading,
                    handleSizeChange,
                    handleCurrentChange,
                    maskString,
                    navItems,
                    isCurrentPage,
                    isActiveParent,
                    getRankStyle,
                    isMobile,
                    getMenuPath,
                    menuStatus,
                    getMenuKey,
                };
            }
        }).use(ElementPlus, {
            locale: ElementPlusLocaleZhCn
        }).mount("#app");
    </script>
</body>
</html>