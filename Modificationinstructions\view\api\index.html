<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上级商品使用说明修改设置</title>
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <style>
        /* 基础样式 */
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #f5f7fa;
        }

        .container {
            padding: 20px;
        }

        .config-card {
            margin-bottom: 20px;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .el-form-item {
            margin-bottom: 22px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <el-card class="config-card">
                <template #header>
                    <div class="card-header">
                        <span>功能设置</span>
                    </div>
                </template>
                
                <el-form :model="config" label-width="120px" v-loading="loading">
                    <el-form-item label="功能开关">
                        <el-switch v-model="config.status" />
                        <div class="el-form-item-tip" style="font-size: 12px; color: #909399; margin-top: 5px;">
                            开启后，用户可以修改对接上级商品的使用说明
                        </div>
                    </el-form-item>
                    
                    <el-form-item label="允许HTML内容">
                        <el-switch v-model="config.allow_html" />
                        <div class="el-form-item-tip" style="font-size: 12px; color: #909399; margin-top: 5px;">
                            开启后，用户可以在使用说明中使用HTML标签（可能存在安全风险）
                        </div>
                    </el-form-item>
                    
                    <el-form-item label="需要管理员审核">
                        <el-switch v-model="config.need_approval" />
                        <div class="el-form-item-tip" style="font-size: 12px; color: #909399; margin-top: 5px;">
                            开启后，用户修改的内容需要管理员审核后才能生效
                        </div>
                    </el-form-item>
                    
                    <el-form-item label="最大字符长度">
                        <el-input-number v-model="config.max_length" :min="100" :max="10000" />
                        <div class="el-form-item-tip" style="font-size: 12px; color: #909399; margin-top: 5px;">
                            使用说明的最大字符长度限制
                        </div>
                    </el-form-item>
                    
                    <el-form-item>
                        <el-button type="primary" @click="saveConfig" :loading="saving">保存设置</el-button>
                    </el-form-item>
                </el-form>
            </el-card>
            
            <el-card class="config-card" v-if="config.need_approval">
                <template #header>
                    <div class="card-header">
                        <span>审核管理</span>
                    </div>
                </template>
                
                <el-table :data="pendingList" v-loading="loadingApproval" style="width: 100%">
                    <el-table-column prop="id" label="ID" width="60" />
                    <el-table-column prop="goods_name" label="商品名称" min-width="180" />
                    <el-table-column prop="username" label="用户名" width="120" />
                    <el-table-column prop="nickname" label="用户昵称" width="120" />
                    <el-table-column label="提交时间" width="160">
                        <template #default="scope">
                            {{ formatTime(scope.row.create_time) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="240" align="center">
                        <template #default="scope">
                            <el-button type="primary" size="small" @click="viewContent(scope.row)">查看内容</el-button>
                            <el-button type="success" size="small" @click="handleApprove(scope.row)">通过</el-button>
                            <el-button type="danger" size="small" @click="handleReject(scope.row)">拒绝</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                
                <div style="margin-top: 15px; text-align: right;">
                    <el-pagination
                        v-if="totalPending > 0"
                        background
                        layout="prev, pager, next"
                        :total="totalPending"
                        :page-size="10"
                        @current-change="handlePageChange"
                    />
                </div>
            </el-card>
        </div>
        
        <!-- 查看内容弹窗 -->
        <el-dialog v-model="viewDialogVisible" title="查看使用说明内容" width="700px">
            <el-form label-width="100px">
                <el-form-item label="用户">
                    <el-input v-model="currentApproval.userInfo" readonly />
                </el-form-item>
                <el-form-item label="商品">
                    <el-input v-model="currentApproval.goodsName" readonly />
                </el-form-item>
                <el-form-item label="提交时间">
                    <el-input v-model="currentApproval.submitTime" readonly />
                </el-form-item>
                <el-form-item label="使用说明内容">
                    <el-input type="textarea" v-model="currentApproval.content" rows="10" readonly />
                </el-form-item>
            </el-form>
        </el-dialog>
        
        <!-- 拒绝理由弹窗 -->
        <el-dialog v-model="rejectDialogVisible" title="拒绝修改请求" width="500px">
            <el-form>
                <el-form-item label="拒绝理由" label-width="100px">
                    <el-input type="textarea" v-model="rejectReason" rows="4" placeholder="请输入拒绝理由" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="rejectDialogVisible = false">取消</el-button>
                <el-button type="danger" @click="confirmReject">确认拒绝</el-button>
            </template>
        </el-dialog>
    </div>

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>

    <script>
        const app = Vue.createApp({
            setup() {
                const config = Vue.ref({
                    status: true,
                    allow_html: false,
                    need_approval: false,
                    max_length: 2000
                });
                const loading = Vue.ref(false);
                const saving = Vue.ref(false);
                const loadingApproval = Vue.ref(false);
                const pendingList = Vue.ref([]);
                const totalPending = Vue.ref(0);
                const currentPage = Vue.ref(1);
                
                const viewDialogVisible = Vue.ref(false);
                const rejectDialogVisible = Vue.ref(false);
                const rejectReason = Vue.ref('');
                const currentApproval = Vue.ref({
                    id: 0,
                    userInfo: '',
                    goodsName: '',
                    submitTime: '',
                    content: ''
                });

                // 获取配置
                const getConfig = async () => {
                    loading.value = true;
                    try {
                        const res = await axios.get('/plugin/Modificationinstructions/api/getConfig');
                        if (res.data.code === 200) {
                            Object.assign(config.value, res.data.data);
                            if (config.value.need_approval) {
                                getPendingApprovals();
                            }
                        } else {
                            ElMessage.error(res.data.msg || '获取配置失败');
                        }
                    } catch (error) {
                        console.error('获取配置失败:', error);
                        ElMessage.error('获取配置失败：' + error.message);
                    } finally {
                        loading.value = false;
                    }
                };

                // 保存配置
                const saveConfig = async () => {
                    saving.value = true;
                    try {
                        const res = await axios.post('/plugin/Modificationinstructions/api/saveConfig', config.value);
                        if (res.data.code === 200) {
                            ElMessage.success('保存成功');
                            if (config.value.need_approval) {
                                getPendingApprovals();
                            }
                        } else {
                            ElMessage.error(res.data.msg || '保存失败');
                        }
                    } catch (error) {
                        console.error('保存配置失败:', error);
                        ElMessage.error('保存配置失败：' + error.message);
                    } finally {
                        saving.value = false;
                    }
                };
                
                // 获取待审核列表
                const getPendingApprovals = async () => {
                    loadingApproval.value = true;
                    try {
                        const res = await axios.get('/plugin/Modificationinstructions/api/getPendingApprovals', {
                            params: {
                                page: currentPage.value,
                                limit: 10
                            }
                        });
                        if (res.data.code === 200) {
                            pendingList.value = res.data.data;
                            totalPending.value = res.data.total;
                        } else {
                            ElMessage.error(res.data.msg || '获取待审核列表失败');
                        }
                    } catch (error) {
                        console.error('获取待审核列表失败:', error);
                        ElMessage.error('获取待审核列表失败：' + error.message);
                    } finally {
                        loadingApproval.value = false;
                    }
                };
                
                // 查看使用说明内容
                const viewContent = (row) => {
                    currentApproval.value = {
                        id: row.id,
                        userInfo: `${row.username} (${row.nickname})`,
                        goodsName: row.goods_name,
                        submitTime: formatTime(row.create_time),
                        content: row.new_instructions
                    };
                    viewDialogVisible.value = true;
                };
                
                // 处理审核通过
                const handleApprove = (row) => {
                    ElMessageBox.confirm('确定要通过该修改请求吗？', '确认', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        approveChange(row.id, 'approve');
                    }).catch(() => {});
                };
                
                // 处理拒绝
                const handleReject = (row) => {
                    currentApproval.value.id = row.id;
                    rejectReason.value = '';
                    rejectDialogVisible.value = true;
                };
                
                // 确认拒绝
                const confirmReject = () => {
                    if (!rejectReason.value) {
                        ElMessage.warning('请输入拒绝理由');
                        return;
                    }
                    
                    approveChange(currentApproval.value.id, 'reject', rejectReason.value);
                    rejectDialogVisible.value = false;
                };
                
                // 审批变更
                const approveChange = async (logId, action, reason = '') => {
                    try {
                        const data = {
                            log_id: logId,
                            action: action
                        };
                        
                        if (action === 'reject') {
                            data.reject_reason = reason;
                        }
                        
                        const res = await axios.post('/plugin/Modificationinstructions/api/approveChange', data);
                        if (res.data.code === 200) {
                            ElMessage.success(res.data.msg);
                            getPendingApprovals();
                        } else {
                            ElMessage.error(res.data.msg || '操作失败');
                        }
                    } catch (error) {
                        console.error('审批失败:', error);
                        ElMessage.error('审批失败：' + error.message);
                    }
                };
                
                // 分页处理
                const handlePageChange = (page) => {
                    currentPage.value = page;
                    getPendingApprovals();
                };
                
                // 格式化时间
                const formatTime = (timestamp) => {
                    if (!timestamp) return '-';
                    return new Date(timestamp * 1000).toLocaleString();
                };

                // 页面加载时获取配置
                Vue.onMounted(() => {
                    getConfig();
                });

                return {
                    config,
                    loading,
                    saving,
                    loadingApproval,
                    pendingList,
                    totalPending,
                    viewDialogVisible,
                    rejectDialogVisible,
                    rejectReason,
                    currentApproval,
                    saveConfig,
                    viewContent,
                    handleApprove,
                    handleReject,
                    confirmReject,
                    handlePageChange,
                    formatTime
                };
            }
        });

        app.use(ElementPlus, {
            locale: ElementPlusLocaleZhCn
        });
        
        app.mount('#app');
    </script>
</body>
</html> 