<?php

namespace plugin\Goandpay\controller;

use app\common\controller\BasePlugin;
use think\facade\View;

class Api extends BasePlugin {

    protected $scene = ['admin', 'merchant'];
    protected $noNeedLogin = [
        'fetchData',
    ];

    public function index() {
        // 添加编辑器配置
        $editorConfig = [
            'toolbarKeys' => [
                [
                    'key' => 'headerSelect',
                    'title' => '正文'
                ],
                'quote',
                'bold',
                'underline',
                'italic',
                'through',
                [
                    'key' => 'fontSize',
                    'title' => '默认字号'
                ],
                [
                    'key' => 'fontFamily',
                    'title' => '默认字体'
                ],
                [
                    'key' => 'lineHeight',
                    'title' => '默认行高'
                ],
                'bulletedList',
                'numberedList',
                'todo',
                [
                    'key' => 'group-justify',
                    'title' => '对齐',
                    'menuKeys' => ['justifyLeft', 'justifyCenter', 'justifyRight']
                ]
            ],
            'editorConfig' => [
                'placeholder' => '请输入内容...',
                'autoFocus' => false,
                'scroll' => true,
                'height' => 300,
                'minHeight' => 150,
                'maxHeight' => 500,
                'fontSizes' => [
                    '12px', '13px', '14px', '15px', '16px', '19px', '22px', '24px', '29px', '32px', '40px'
                ],
                'fontFamilies' => [
                    ['name' => '宋体', 'value' => 'SimSun'],
                    ['name' => '黑体', 'value' => 'SimHei'],
                    ['name' => '微软雅黑', 'value' => 'Microsoft YaHei'],
                    ['name' => '楷体', 'value' => 'KaiTi'],
                    ['name' => '仿宋', 'value' => 'FangSong'],
                    ['name' => 'Arial', 'value' => 'Arial, Helvetica, sans-serif']
                ],
                'lineHeights' => ['1', '1.15', '1.6', '2', '2.5', '3']
            ],
            'uploadConfig' => [
                'server' => '/ajax/upload',
                'fieldName' => 'file'
            ]
        ];
        
        View::assign('editorConfig', json_encode($editorConfig));
        return View::fetch();
    }

    // 获取去支付配置
    public function fetchData() {
        // 添加防止缓存的响应头
        header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
        header('Cache-Control: post-check=0, pre-check=0', false);
        header('Pragma: no-cache');
        
        // 如果是商家端访问，只返回启用状态的配置
        if ($this->scene == 'merchant') {
            $status = intval(plugconf("Goandpay.status") ?? 1);
            if ($status != 1) {
                $this->success('success', ['status' => 0, 'payment_notice' => '']);
                return;
            }
        }

        $params = [
            'status' => intval(plugconf("Goandpay.status") ?? 1),
            'payment_notice' => plugconf("Goandpay.payment_notice") ?? '',
            'frequency' => plugconf("Goandpay.frequency") ?? 'once',
            'read_enabled' => intval(plugconf("Goandpay.read_enabled") ?? 1),
            'close_delay' => intval(plugconf("Goandpay.close_delay") ?? 0)
        ];

        $this->success('success', $params);
    }

    // 保存去支付配置
    public function save() {
        $status = $this->request->post('status/d', 1);
        $payment_notice = $this->request->post('payment_notice', '', 'trim');
        $frequency = $this->request->post('frequency', 'once', 'trim');
        $read_enabled = $this->request->post('read_enabled/d', 1);
        $close_delay = $this->request->post('close_delay/d', 0);
        
        // 验证 read_enabled 值
        if (!in_array($read_enabled, [0, 1])) {
            $read_enabled = 1; // 默认开启
        }
        
        // 验证frequency值
        if (!in_array($frequency, ['once', 'login', 'daily', 'weekly'])) {
            $this->error('弹出频率设置不正确');
        }
        
        // 验证倒计时值
        if ($close_delay < 0 || $close_delay > 60) {
            $this->error('倒计时设置不正确');
        }
        
        // 内容安全检查
        if ($payment_notice !== '' && !is_string($payment_notice)) {
            $this->error('支付提示内容格式不正确');
        }

        // 保存配置
        plugconf("Goandpay.status", $status);
        plugconf("Goandpay.payment_notice", $payment_notice);
        plugconf("Goandpay.frequency", $frequency);
        plugconf("Goandpay.read_enabled", intval($read_enabled));
        plugconf("Goandpay.close_delay", $close_delay);

        $this->success('保存成功');
    }
}
