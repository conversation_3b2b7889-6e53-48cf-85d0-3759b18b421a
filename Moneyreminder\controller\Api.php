<?php

namespace plugin\Moneyreminder\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;

class Api extends BasePlugin
{
    protected $scene = ['admin'];
    protected $noNeedLogin = [];

    public function index()
    {
        // 获取配置参数
        $params = [
            'monitor_status' => (bool)plugconf('Moneyreminder.monitor_status'),
            'deposit_threshold' => (float)plugconf('Moneyreminder.deposit_threshold'),
            'operate_threshold' => (float)plugconf('Moneyreminder.operate_threshold'),
            'check_interval' => (int)plugconf('Moneyreminder.check_interval'),
            'notify_type' => (string)plugconf('Moneyreminder.notify_type'),
            'notify_email' => (string)plugconf('Moneyreminder.notify_email'),
            'notify_qq' => (string)plugconf('Moneyreminder.notify_qq'),
            'notify_wxpusher' => (string)plugconf('Moneyreminder.notify_wxpusher'),
        ];

        // 设置默认值（如果配置为空）
        if (!isset($params['monitor_status'])) $params['monitor_status'] = false;
        if (!isset($params['deposit_threshold']) || $params['deposit_threshold'] <= 0) $params['deposit_threshold'] = 1000.00;
        if (!isset($params['operate_threshold']) || $params['operate_threshold'] <= 0) $params['operate_threshold'] = 500.00;
        if (!isset($params['check_interval']) || $params['check_interval'] <= 0) $params['check_interval'] = 60;
        if (!isset($params['notify_type']) || empty($params['notify_type'])) $params['notify_type'] = '1';
        
        // 记录读取的配置
        error_log("Reading config params: " . json_encode($params));
        
        View::assign('params', $params);
        
        return View::fetch(__DIR__ . '/../view/index.html');
    }
    
    // 保存配置
    public function save()
    {
        try {
            $param = $this->request->post();
            
            // 记录原始数据
            error_log("Raw post data: " . json_encode($param));
            
            // 类型转换和验证
            $param = [
                'monitor_status' => isset($param['monitor_status']) ? (int)$param['monitor_status'] : 0,
                'deposit_threshold' => isset($param['deposit_threshold']) ? (float)$param['deposit_threshold'] : 1000.00,
                'operate_threshold' => isset($param['operate_threshold']) ? (float)$param['operate_threshold'] : 500.00,
                'check_interval' => isset($param['check_interval']) ? (int)$param['check_interval'] : 60,
                'notify_type' => isset($param['notify_type']) ? (string)$param['notify_type'] : '1',
                'notify_email' => isset($param['notify_email']) ? trim((string)$param['notify_email']) : '',
                'notify_qq' => isset($param['notify_qq']) ? trim((string)$param['notify_qq']) : '',
                'notify_wxpusher' => isset($param['notify_wxpusher']) ? trim((string)$param['notify_wxpusher']) : ''
            ];
            
            // 验证参数
            $rule = [
                'monitor_status' => 'require|in:0,1',
                'deposit_threshold' => 'require|float|egt:0',
                'operate_threshold' => 'require|float|egt:0',
                'check_interval' => 'require|integer|egt:1',
                'notify_type' => 'require|in:1,2,3,4,5,6',
            ];
            
            $validate = \think\facade\Validate::rule($rule);
            if (!$validate->check($param)) {
                error_log("Validation error: " . $validate->getError());
                return json([
                    'code' => 0,
                    'msg' => $validate->getError()
                ]);
            }
            
            // 根据通知类型校验额外参数
            switch ($param['notify_type']) {
                case '3': // 邮箱通知需要验证配置
                    if (empty($param['notify_email'])) {
                        return json([
                            'code' => 0,
                            'msg' => '请填写通知邮箱'
                        ]);
                    }
                    break;
                // 其他通知方式为可选配置，不强制验证
                case '2': // QQ
                case '4': // wxpusher
                case '5': // 企业微信群
                case '6': // 机器人
                    // 不强制验证配置
                    break;
            }
            
            // 记录处理后的数据
            error_log("Processed config params: " . json_encode($param));
            
            // 保存配置
            foreach ($param as $key => $value) {
                plugconf("Moneyreminder.{$key}", $value);
            }
            
            // 获取保存后的配置进行验证
            $savedConfig = [
                'monitor_status' => (bool)plugconf('Moneyreminder.monitor_status'),
                'deposit_threshold' => (float)plugconf('Moneyreminder.deposit_threshold'),
                'operate_threshold' => (float)plugconf('Moneyreminder.operate_threshold'),
                'check_interval' => (int)plugconf('Moneyreminder.check_interval'),
                'notify_type' => (string)plugconf('Moneyreminder.notify_type'),
                'notify_email' => (string)plugconf('Moneyreminder.notify_email'),
                'notify_qq' => (string)plugconf('Moneyreminder.notify_qq'),
                'notify_wxpusher' => (string)plugconf('Moneyreminder.notify_wxpusher')
            ];
            
            error_log("Saved config: " . json_encode($savedConfig));
            
            return json([
                'code' => 1,
                'msg' => '保存成功',
                'data' => $savedConfig
            ]);
        } catch (\Exception $e) {
            error_log("Moneyreminder save config error: " . $e->getMessage());
            return json([
                'code' => 0,
                'msg' => '保存失败：' . $e->getMessage()
            ]);
        }
    }
    
    // 测试通知
    public function testNotify()
    {
        try {
            // 记录开始测试
            \think\facade\Log::info('开始发送测试通知');
            
            // 检查监控状态
            $monitorStatus = plugconf('Moneyreminder.monitor_status');
            \think\facade\Log::info('当前监控状态: ' . ($monitorStatus ? '启用' : '禁用'));
            
            if (!$monitorStatus) {
                throw new \Exception('请先启用监控状态');
            }

            // 获取当前通知配置
            $notifyType = plugconf('Moneyreminder.notify_type');
            $notifyEmail = plugconf('Moneyreminder.notify_email');
            $notifyQQ = plugconf('Moneyreminder.notify_qq');
            $notifyWxpusher = plugconf('Moneyreminder.notify_wxpusher');
            
            \think\facade\Log::info("当前通知配置 - 类型: {$notifyType}, 邮箱: {$notifyEmail}, QQ: {$notifyQQ}, WxPusher: {$notifyWxpusher}");

            // 验证配置
            if ($notifyType == '3' && empty($notifyEmail)) {
                throw new \Exception('请先配置通知邮箱');
            }
            if ($notifyType == '2' && empty($notifyQQ)) {
                throw new \Exception('请先配置通知QQ');
            }
            if ($notifyType == '4' && empty($notifyWxpusher)) {
                throw new \Exception('请先配置WxPusher');
            }

            // 检查数据库notification表是否存在
            $tableExists = false;
            try {
                $tables = Db::query('SHOW TABLES LIKE "pt_notification"');
                $tableExists = !empty($tables);
                \think\facade\Log::info("notification表检查结果: " . ($tableExists ? "存在" : "不存在"));
            } catch (\Exception $e) {
                \think\facade\Log::error("检查数据库表失败: " . $e->getMessage());
            }

            if (!$tableExists) {
                throw new \Exception("notification表不存在，请先创建表");
            }

            // 创建测试用户数据
            $testUser = [
                'id' => 0,
                'username' => '测试用户',
                'email' => $notifyEmail ?: '',
                'qq' => $notifyQQ ?: '',
                'wxpusher_uid' => $notifyWxpusher ?: '',
                'deposit_money' => 100,
                'operate_money' => 100
            ];

            \think\facade\Log::info('测试用户数据: ' . json_encode($testUser, JSON_UNESCAPED_UNICODE));

            try {
                // 创建Hook实例
                $hook = new \plugin\Moneyreminder\Hook();
                
                // 调用公共方法发送通知
                \think\facade\Log::info('开始调用Hook::sendTestNotification方法');
                $result = $hook->sendTestNotification($testUser);
                \think\facade\Log::info('测试通知发送结果: ' . ($result === true ? '成功' : $result));
                
                if ($result === true) {
                    return json([
                        'code' => 1,
                        'msg' => '测试通知发送成功，请查看通知'
                    ]);
                } else {
                    throw new \Exception($result);
                }
            } catch (\Error $e) {
                \think\facade\Log::error('Hook类错误: ' . $e->getMessage());
                \think\facade\Log::error($e->getTraceAsString());
                throw new \Exception('Hook类错误：' . $e->getMessage());
            }
            
        } catch (\Throwable $e) {
            \think\facade\Log::error('测试通知发送失败：' . $e->getMessage());
            \think\facade\Log::error($e->getTraceAsString());
            return json([
                'code' => 0,
                'msg' => '发送测试通知失败',
                'data' => [
                    'error' => $e->getMessage()
                ]
            ]);
        }
    }
} 