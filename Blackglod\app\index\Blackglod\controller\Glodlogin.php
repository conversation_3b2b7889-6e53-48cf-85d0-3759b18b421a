<?php

namespace app\index\Blackglod\controller;

use app\common\controller\BaseIndex;
use think\facade\Db;

class Glodlogin extends BaseIndex {

    public function index() {
        // 获取所有启用的菜单项并按sort降序排序
        $allMenuItems = Db::name('nav')
            ->where('status', 1)
            ->order('sort desc, id asc')  // 先按sort降序，相同时按id升序
            ->select()
            ->toArray();
            
        // 找出顶级菜单
        $navItems = array_filter($allMenuItems, function($item) {
            return $item['pid'] == 0;
        });
        
        // 为每个顶级菜单添加子菜单
        foreach ($navItems as &$item) {
            $item['children'] = array_filter($allMenuItems, function($child) use ($item) {
                return $child['pid'] == $item['id'];
            });
        }
        
        // 将导航数组重置索引
        $navItems = array_values($navItems);

        // 获取网站配置信息
        $logo = $this->formatImageUrl(sysconf('website.logo'));
        $siteName = sysconf('website.app_name');
        $favicon = $this->formatImageUrl(sysconf('website.favicon'));
        $icpNumber = sysconf('website.icp_number');
        $gaNumber = sysconf('website.ga_number');
        $icpCert = sysconf('website.icp_cert');

        return view('', [
            'title' => '商户登录 - ' . $siteName,
            'logo' => $logo,
            'siteName' => $siteName,
            'favicon' => $favicon,
            'navItems' => $navItems,
            'icpNumber' => $icpNumber,
            'gaNumber' => $gaNumber,
            'icpCert' => $icpCert,
        ]);
    }

    /**
     * 格式化图片URL
     */
    private function formatImageUrl($url) {
        if (empty($url)) {
            return '';
        }
        if (preg_match('/^https?:\/\/(oss\.|cos\.|[-\w]+\.qiniucdn\.|[-\w]+)/i', $url)) {
            return $url;
        }
        if (strpos($url, 'uploads/') === 0) {
            return request()->domain() . '/' . $url;
        }
        return request()->domain() . '/' . ltrim($url, '/');
    }

    public function login()
    {
        // 确保是AJAX请求
        if (!request()->isAjax() || !request()->isPost()) {
            return json([
                'code' => 0,
                'msg' => '非法请求',
                'time' => time()
            ]);
        }

        $username = input('post.username');
        $password = input('post.password');
        $remember = input('post.remember/d', 0);

        if (empty($username) || empty($password)) {
            return json([
                'code' => 0,
                'msg' => '请填写完整的登录信息',
                'time' => time()
            ]);
        }

        try {
            // 验证用户信息
            $user = Db::name('user')
                ->where('username', $username)
                ->find();

            if (!$user || !password_verify($password, $user['password'])) {
                return json([
                    'code' => 0,
                    'msg' => '账号或密码错误',
                    'time' => time()
                ]);
            }

            // 生成token
            $merchant_token = sprintf(
                '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
                mt_rand(0, 0xffff), mt_rand(0, 0xffff),
                mt_rand(0, 0xffff),
                mt_rand(0, 0x0fff) | 0x4000,
                mt_rand(0, 0x3fff) | 0x8000,
                mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
            );

            // 更新用户token
            Db::name('user')->where('id', $user['id'])->update([
                'merchant_token' => $merchant_token,
                'last_login_time' => time(),
                'last_login_ip' => request()->ip()
            ]);

            // 设置session
            session('user', [
                'id' => $user['id'],
                'username' => $user['username'],
                'merchant_token' => $merchant_token
            ]);

            // 设置cookie
            if ($remember) {
                cookie('merchant_token', $merchant_token, 7 * 24 * 3600);
            }

            // 返回JSON响应
            return json([
                'code' => 1,
                'msg' => '登录成功',
                'time' => time(),
                'data' => [
                    'url' => '/merchant/dashboard/workplace',
                    'merchant_token' => $merchant_token
                ]
            ])->header([
                'Content-Type' => 'application/json; charset=utf-8'
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '登录失败，请稍后重试',
                'time' => time()
            ]);
        }
    }

    /**
     * 获取用户详细信息
     */
    private function getUserInfo($userId)
    {
        try {
            // 调用用户信息接口
            $response = $this->httpRequest('/merchantApi/user/userinfo', [
                'user_id' => $userId
            ], 'GET');

            if ($response && isset($response['code']) && $response['code'] === 1) {
                return $response['data'];
            }

            return null;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 发送HTTP请求
     */
    private function httpRequest($url, $params = [], $method = 'GET')
    {
        $ch = curl_init();
        
        if ($method === 'GET' && !empty($params)) {
            $url .= '?' . http_build_query($params);
        }
        
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HEADER, false);
        
        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
        }
        
        $response = curl_exec($ch);
        curl_close($ch);
        
        return json_decode($response, true);
    }
} 