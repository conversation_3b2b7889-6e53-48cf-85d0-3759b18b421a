<?php
namespace plugin\Complaintrate;

use think\facade\Db;

class Hook
{
    public function handle()
    {
        try {
            // 检查是否开启自动更新
            if (intval(plugconf('Complaintrate.auto_update_status') ?? 0) !== 1) {
                error_log("Complaintrate: 自动更新未开启");
                return;
            }

            // 获取计算模式
            $calcMode = plugconf('Complaintrate.calc_mode') ?? 'realtime';
            
            // 如果是实时模式，则每次都计算
            if ($calcMode === 'realtime') {
                $this->calculateAll();
                return;
            }

            // 定时模式下检查更新间隔
            $lastUpdateTime = intval(plugconf('Complaintrate.last_update_time') ?? 0);
            $updateInterval = max(5, intval(plugconf('Complaintrate.update_interval') ?? 5));
            if (time() - $lastUpdateTime < $updateInterval) {
                return;
            }

            // 执行计算
            $this->calculateAll();
        } catch (\Exception $e) {
            error_log("Complaintrate: 自动更新失败: " . $e->getMessage());
        }
    }

    private function calculateAll()
    {
        try {
            // 获取所有商户
            $merchants = Db::name('user')
                ->field(['id', 'channel_group_id'])
                ->select()
                ->toArray();

            foreach ($merchants as $merchant) {
                try {
                    // 计算投诉率
                    $complaintRate = $this->calculateComplaintRate($merchant['id']);
                    
                    // 根据投诉率确定目标等级
                    $targetGroupId = $this->determineTargetGroup($complaintRate);
                    
                    if ($targetGroupId && $targetGroupId != $merchant['channel_group_id']) {
                        // 更新商户等级
                        $this->updateMerchantGroup(
                            $merchant['id'], 
                            $merchant['channel_group_id'],
                            $targetGroupId,
                            "投诉率: {$complaintRate}%, 系统调整等级"
                        );
                    }
                } catch (\Exception $e) {
                    error_log("Complaintrate: 处理商户 {$merchant['id']} 失败: " . $e->getMessage());
                    continue;
                }
            }

            // 更新最后执行时间
            plugconf('Complaintrate.last_update_time', time());
        } catch (\Exception $e) {
            error_log("Complaintrate: 批量计算失败: " . $e->getMessage());
            throw $e;
        }
    }

    private function calculateComplaintRate($merchantId)
    {
        // 获取更新周期
        $updateCycle = plugconf('Complaintrate.update_cycle') ?? 'monthly';
        
        // 计算时间范围
        $endTime = time();
        switch ($updateCycle) {
            case 'monthly':
                $startTime = strtotime('-30 days');
                break;
            case 'quarterly':
                $startTime = strtotime('-90 days');
                break;
            case 'yearly':
                $startTime = strtotime('-365 days');
                break;
            default:
                $startTime = strtotime('-30 days');
        }

        // 获取投诉数和订单数
        $complaints = Db::name('complaints')
            ->where('merchant_id', $merchantId)
            ->where('create_time', 'between', [$startTime, $endTime])
            ->count();

        $orders = Db::name('orders')
            ->where('merchant_id', $merchantId)
            ->where('create_time', 'between', [$startTime, $endTime])
            ->count();

        // 计算投诉率
        return $orders > 0 ? ($complaints / $orders) * 100 : 0;
    }
} 