<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$title}</title>
    <!-- Font Awesome 图标库 -->
    <link rel="stylesheet" href="/assets/plugin/Bluelunbo/plugin/Bluelunbo/css/all.min.css">
    <style>
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
        }
        
        :root {
            --primary: #4169e1;
            --primary-light: #6a8bef;
            --primary-dark: #3a5ecc;
            --primary-bg: #f5f9ff;
            --secondary-bg: #f0f7ff;
            --card-bg: #f8faff;
            --text-primary: #333;
            --text-secondary: #666;
            --text-light: #999;
            --border-radius-sm: 8px;
            --border-radius: 15px;
            --border-radius-lg: 20px;
            --border-radius-xl: 30px;
            --shadow-sm: 0 4px 8px rgba(0, 0, 0, 0.05);
            --shadow: 0 10px 30px rgba(65, 105, 225, 0.1);
            --shadow-lg: 0 15px 40px rgba(65, 105, 225, 0.15);
            --transition: all 0.3s ease;
            
            /* 新增加的变量 */
            --gradient-primary: linear-gradient(135deg, var(--primary-light), var(--primary), var(--primary-dark));
            --gradient-overlay: linear-gradient(rgba(65, 105, 225, 0.05), rgba(65, 105, 225, 0.1));
            --glass-effect: rgba(255, 255, 255, 0.8);
            --box-shadow-hover: 0 15px 35px rgba(65, 105, 225, 0.2);
            --accent-color: #ff7b54;
            --success-color: #4CAF50;
            --warning-color: #FFC107;
            --error-color: #FF5252;
        }
        
        body {
            background-color: var(--primary-bg);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden; /* 防止水平滚动条 */
            font-size: 16px;
            background-image: var(--gradient-overlay);
            background-attachment: fixed;
        }
        
        a {
            text-decoration: none;
            color: var(--text-primary);
            transition: var(--transition);
        }
        
        ul {
            list-style: none;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            position: relative;
            z-index: 1;
            text-align: center; /* 添加文本居中 */
        }
        
        /* 波浪形分隔线 */
        .wave-divider {
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            overflow: hidden;
            line-height: 0;
            z-index: 1;
        }
        
        .wave-divider svg {
            position: relative;
            display: block;
            width: calc(100% + 1.3px);
            height: 70px;
        }
        
        .wave-divider .shape-fill {
            fill: #FFFFFF;
        }
        
        .wave-divider-inverted {
            position: absolute;
            top: -2px;
            left: 0;
            width: 100%;
            overflow: hidden;
            line-height: 0;
            transform: rotate(180deg);
            z-index: 1;
        }
        
        .wave-divider-inverted svg {
            position: relative;
            display: block;
            width: calc(100% + 1.3px);
            height: 70px;
        }
        
        .wave-divider-inverted .shape-fill {
            fill: #FFFFFF;
        }
        
        /* 动画效果 */
        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }
        
        @keyframes slideInLeft {
            from {
                transform: translateX(-50px);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        @keyframes slideInRight {
            from {
                transform: translateX(50px);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        @keyframes slideInUp {
            from {
                transform: translateY(50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
        
        @keyframes zoomIn {
            from {
                transform: scale(0.8);
                opacity: 0;
            }
            to {
                transform: scale(1);
                opacity: 1;
            }
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-20px);
            }
            60% {
                transform: translateY(-10px);
            }
        }
        
        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
            100% {
                transform: scale(1);
            }
        }
        
        @keyframes rotateIn {
            from {
                transform: rotate(-10deg);
                opacity: 0;
            }
            to {
                transform: rotate(0);
                opacity: 1;
            }
        }
        
        @keyframes flipIn {
            from {
                transform: perspective(400px) rotateY(90deg);
                opacity: 0;
            }
            to {
                transform: perspective(400px) rotateY(0);
                opacity: 1;
            }
        }
        
        @keyframes floatAnimation {
            0% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(-10px);
            }
            100% {
                transform: translateY(0);
            }
        }
        
        /* 滚动触发动画的基础类 */
        .animate {
            opacity: 0;
            transition: all 0.8s ease;
        }
        
        .animate.active {
            opacity: 1;
        }
        
        .fade-in {
            opacity: 0;
        }
        
        .fade-in.active {
            animation: fadeIn 1s forwards;
        }
        
        .slide-left {
            opacity: 0;
            transform: translateX(-50px);
        }
        
        .slide-left.active {
            animation: slideInLeft 1s forwards;
        }
        
        .slide-right {
            opacity: 0;
            transform: translateX(50px);
        }
        
        .slide-right.active {
            animation: slideInRight 1s forwards;
        }
        
        .slide-up {
            opacity: 0;
            transform: translateY(50px);
        }
        
        .slide-up.active {
            animation: slideInUp 1s forwards;
        }
        
        .zoom-in {
            opacity: 0;
            transform: scale(0.8);
        }
        
        .zoom-in.active {
            animation: zoomIn 1s forwards;
        }
        
        .bounce {
            opacity: 0;
        }
        
        .bounce.active {
            opacity: 1;
            animation: bounce 1s forwards;
        }
        
        .pulse {
            opacity: 0;
        }
        
        .pulse.active {
            opacity: 1;
            animation: pulse 1.5s infinite;
        }
        
        .rotate-in {
            opacity: 0;
            transform: rotate(-10deg);
        }
        
        .rotate-in.active {
            animation: rotateIn 1s forwards;
        }
        
        .flip-in {
            opacity: 0;
            transform: perspective(400px) rotateY(90deg);
        }
        
        .flip-in.active {
            animation: flipIn 1s forwards;
        }
        
        .float {
            animation: floatAnimation 3s ease-in-out infinite;
        }
        
        /* 延迟动画 */
        .delay-100 {
            animation-delay: 0.1s;
        }
        
        .delay-200 {
            animation-delay: 0.2s;
        }
        
        .delay-300 {
            animation-delay: 0.3s;
        }
        
        .delay-400 {
            animation-delay: 0.4s;
        }
        
        .delay-500 {
            animation-delay: 0.5s;
        }
        
        .delay-600 {
            animation-delay: 0.6s;
        }
        
        .delay-700 {
            animation-delay: 0.7s;
        }
        
        .delay-800 {
            animation-delay: 0.8s;
        }
        
        /* 头部导航增强 */
        header {
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 15px 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            transition: all 0.3s ease;
            height: 70px; /* 固定高度 */
            display: flex;
            align-items: center;
            will-change: transform; /* 优化性能 */
        }
        
        /* 滚动后导航栏变小 */
        header.scrolled {
            padding: 10px 0;
            background-color: rgba(255, 255, 255, 0.98);
            box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
            height: 60px; /* 固定滚动后高度 */
        }
        
        header.scrolled .logo img {
            height: 35px;
        }
        
        header.scrolled .logo-text {
            font-size: 20px;
        }
        
        header.scrolled .nav-links li a {
            font-size: 14px;
        }
        
        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            position: relative; /* 确保子元素相对于它定位 */
        }
        
        .logo {
            display: flex;
            align-items: center;
            animation: slideInLeft 1s;
            transition: all 0.3s ease;
            height: 40px; /* 固定高度 */
        }
        
        .logo img {
            height: 40px;
            margin-right: 10px;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
            transition: all 0.3s ease;
            vertical-align: middle; /* 确保垂直居中 */
        }
        
        .logo-text {
            font-size: 22px;
            font-weight: bold;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }
        
        .nav-links li a {
            color: var(--text-primary);
            font-weight: 500;
            transition: var(--transition);
            white-space: nowrap;
            position: relative;
            padding: 8px 0;
            display: inline-block;
        }
        
        .nav-links li a::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: 0;
            left: 0;
            background: var(--gradient-primary);
            border-radius: 2px;
            transition: width 0.3s ease;
        }
        
        .nav-links li a:hover::after {
            width: 100%;
        }
        
        /* 下拉箭头样式 */
        .dropdown-arrow {
            font-size: 8px; /* 箭头字体大小 */
            margin-left: 5px; /* 与文字的间距 */
            color: var(--primary); /* 箭头颜色 */
            display: inline-block;
            vertical-align: middle;
            transform: translateY(-1px); /* 微调箭头位置 */
        }
        
        .nav-links li.active::after {
            content: "";
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 100%;
            height: 2px;
            background: var(--gradient-primary);
        }
        
        .nav-main {
            display: flex;
            align-items: center;
            animation: slideInRight 1s;
            margin-left: 20px;
            height: 100%; /* 确保高度一致 */
        }
        
        .nav-links {
            display: flex;
            margin-right: 30px;
            align-items: center; /* 确保垂直居中 */
            height: 100%; /* 确保高度一致 */
        }
        
        .nav-links li {
            display: inline-block;
            margin: 0 10px;
            position: relative;
            height: 100%; /* 确保高度一致 */
            display: flex;
            align-items: center;
        }
        
        /* 根据导航项数量自动调整字体大小 */
        .nav-links li {
            display: inline-block;
            margin: 0 10px;
            position: relative;
        }
        
        /* 当导航项超过5个时缩小字体和间距 */
        @media (min-width: 768px) {
            .nav-links li:nth-child(n+6) ~ li {
                margin: 0 8px;
            }
            
            .nav-links:has(li:nth-child(6)) li a {
                font-size: 14px;
            }
            
            /* 当导航项超过7个时进一步缩小 */
            .nav-links:has(li:nth-child(8)) li a {
                font-size: 13px;
                padding: 8px 0;
            }
        }
        
        .nav-links li a {
            color: var(--text-primary);
            font-weight: 500;
            transition: var(--transition);
            white-space: nowrap;
        }
        
        .nav-links li a:hover {
            color: var(--primary);
        }
        
        .nav-links li.active a {
            color: var(--primary);
        }
        
        .nav-links li.active::after {
            content: "";
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: var(--primary);
        }
        
        /* 子菜单样式 */
        .submenu {
            position: absolute;
            top: 100%;
            left: 50%;  /* 改为50%使其可以水平居中 */
            width: 150px; /* 从200px减小到150px */
            background-color: #fff;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-radius: var(--border-radius-sm);
            padding: 10px 0;
            z-index: 100;
            opacity: 0;
            visibility: hidden;
            transform: translateX(-50%) translateY(10px); /* 添加translateX(-50%)实现水平居中 */
            transition: all 0.3s ease;
        }
        
        .nav-links li:hover .submenu {
            opacity: 1;
            visibility: visible;
            transform: translateX(-50%) translateY(0); /* 更新transform，保持水平居中 */
        }
        
        .submenu li {
            display: block;
            margin: 0;
            padding: 0;
            text-align: center; /* 添加文本居中对齐 */
        }
        
        .submenu li a {
            display: block;
            padding: 8px 15px;
            font-size: 14px;
            color: var(--text-secondary);
            text-align: center; /* 添加文本居中对齐 */
        }
        
        .submenu li a:hover {
            background-color: var(--primary-bg);
        }
        
        .auth-buttons {
            display: flex;
            gap: 10px;
            transition: all 0.3s ease;
            height: 40px; /* 固定高度 */
            align-items: center;
        }
        
        .auth-buttons .btn {
            padding: 8px 16px;
            transition: all 0.3s ease;
            height: 36px; /* 固定高度 */
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        
        header.scrolled .auth-buttons .btn {
            padding: 6px 14px;
            font-size: 13px;
            height: 32px; /* 滚动后的固定高度 */
        }
        
        .btn {
            padding: 10px 24px;
            border-radius: 50px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
            outline: none;
            letter-spacing: 0.5px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
        }
        
        .btn::before {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: all 0.4s;
        }
        
        .btn:hover::before {
            left: 100%;
        }
        
        .btn-outline {
            border: 1.5px solid var(--primary);
            color: var(--primary);
            background-color: transparent;
            margin-right: 10px;
        }
        
        .btn-outline:hover {
            background-color: rgba(65, 105, 225, 0.05);
            transform: translateY(-2px);
            box-shadow: 0 6px 10px rgba(0, 0, 0, 0.08);
        }
        
        .btn-primary {
            background: var(--gradient-primary);
            background-size: 200% auto;
            color: white;
            display: flex;
            align-items: center;
        }
        
        .btn-primary:hover {
            background-position: right center;
            transform: translateY(-2px);
            box-shadow: 0 6px 10px rgba(65, 105, 225, 0.2);
        }
        
        /* 公告区域 */
        .announcement-section {
            background-color: #fff;
            padding: 40px 0;
            margin-bottom: 0;
            position: relative;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.03);
        }
        
        .announcement-container {
            display: flex;
            justify-content: space-between;
        }
        
        .announcement-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 25px 20px;
            border-radius: var(--border-radius);
            transition: transform 0.3s, box-shadow 0.3s;
            text-align: center;
            background-color: var(--card-bg);
            margin: 0 10px;
            box-shadow: var(--shadow-sm);
        }
        
        .announcement-item:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow);
        }
        
        .announcement-icon {
            width: 70px;
            height: 70px;
            background-color: rgba(65, 105, 225, 0.1);
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            transition: transform 0.3s;
        }
        
        .announcement-item:hover .announcement-icon {
            transform: scale(1.1);
            background-color: rgba(65, 105, 225, 0.15);
        }
        
        .announcement-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: var(--text-primary);
        }
        
        .announcement-desc {
            font-size: 14px;
            color: var(--text-secondary);
            line-height: 1.5;
        }
        
        /* 自动滚动标签导航 */
        .tab-container {
            position: relative;
            display: flex;
            gap: 30px;
            margin-bottom: 30px;
            justify-content: center;
            background-color: rgba(255, 255, 255, 0.5);
            padding: 10px 20px;
            border-radius: 50px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.8);
        }
        
        .tab-item {
            padding: 12px 20px;
            cursor: pointer;
            font-size: 16px;
            color: rgba(0, 0, 0, 0.6);
            transition: color 0.3s;
            position: relative;
            font-weight: 500;
            z-index: 2;
            overflow: hidden; /* 添加overflow:hidden以确保闪光效果在元素内 */
        }
        
        /* 添加白光闪过效果 */
        .tab-item::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: rgba(255, 255, 255, 0.7);
            transform: rotate(45deg);
            pointer-events: none;
            z-index: 3;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .tab-item:hover::before {
            animation: whiteFlash 1.2s ease-out;
        }
        
        .tab-item.active::before {
            animation: whiteFlash 1.5s ease-out 0.1s;
        }
        
        @keyframes whiteFlash {
            0% {
                left: -150%;
                opacity: 0;
            }
            20% {
                opacity: 0.5;
            }
            50% {
                opacity: 0.6;
            }
            100% {
                left: 150%;
                opacity: 0;
            }
        }

        .tab-item.active {
            color: var(--primary);
        }
        
        .tab-underline {
            position: absolute;
            bottom: 0;
            height: 40px;
            background-color: white;
            transition: left 0.3s ease, width 0.3s ease;
            border-radius: 50px;
            z-index: 1;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        
        .tab-content {
            margin-top: 20px;
        }
        
        .tab-panel {
            display: none;
            opacity: 0;
            transition: opacity 0.3s;
            position: absolute; /* 使用绝对定位防止内容变化导致布局变化 */
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none; /* 默认禁用交互 */
        }
        
        .tab-panel.active {
            display: block;
            opacity: 1;
            animation: fadeIn 0.5s;
            pointer-events: auto; /* 启用交互 */
            z-index: 1; /* 确保活动面板在顶层 */
        }
        
        /* 主要内容区域 */
        .section {
            padding: 80px 0;
            position: relative;
            overflow: hidden;
            text-align: center; /* 添加文本居中 */
        }
        
        .section-title {
            text-align: center;
            margin-bottom: 50px;
            font-size: 36px;
            font-weight: bold;
            color: var(--text-primary);
            position: relative;
            display: block; /* 改为block以确保占据整行 */
            margin-left: auto; /* 使用margin auto居中 */
            margin-right: auto;
            width: 100%; /* 确保宽度100% */
            left: auto; /* 移除left */
            transform: none; /* 移除transform */
            letter-spacing: -0.5px;
            line-height: 1.2;
        }
        
        .section-title::after {
            content: "";
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: var(--gradient-primary);
            border-radius: 2px;
        }
        
        .section-subtitle {
            text-align: center;
            margin-bottom: 50px;
            font-size: 18px;
            color: var(--text-secondary);
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.7;
        }
        
        /* 背景装饰 */
        .bg-decoration {
            position: absolute;
            width: 300px;
            height: 300px;
            background: linear-gradient(135deg, rgba(65, 105, 225, 0.1), rgba(65, 105, 225, 0.05));
            border-radius: 50%;
            z-index: 0;
        }
        
        .bg-decoration-1 {
            top: -150px;
            right: -150px;
        }
        
        .bg-decoration-2 {
            bottom: -150px;
            left: -150px;
        }
        
        /* 解决方案区域 */
        .hero-section {
            padding: 60px 0 120px;
            background: linear-gradient(180deg, var(--primary-bg) 0%, #ffffff 100%);
            position: relative;
            overflow: hidden;
        }
        
        .hero-container {
            display: flex;
            align-items: center;
            gap: 50px;
            position: relative;
            z-index: 2;
            text-align: left; /* 添加文本居中 */
            min-height: 500px; /* 添加最小高度，确保内容区域高度稳定 */
        }
        
        .hero-content-wrapper {
            flex: 1;
            display: flex;
            flex-direction: column;
            position: relative;
            min-height: 380px; /* 确保内容区域有固定的最小高度 */
        }
        
        .hero-content {
            flex: 1;
            position: relative;
        }
        
        .hero-title {
            font-size: 42px;
            font-weight: bold;
            margin-bottom: 25px;
            text-align: left;
            color: var(--text-primary);
            line-height: 1.3;
            letter-spacing: -0.5px;
        }
        
        .hero-subtitle {
            font-size: 18px;
            color: var(--text-secondary);
            margin-bottom: 40px;
            max-width: 800px;
            text-align: left;
            line-height: 1.7;
        }
        
        /* 图片轮播区域 */
        .carousel-container {
            flex: 1;
            position: relative;
            height: 440px; /* 调整高度，使其与上方标签对齐 */
            overflow: hidden;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-lg);
            background-color: #fff;
            border: 1px solid rgba(65, 105, 225, 0.1);
            transition: all 0.5s ease;
            position: sticky; /* 使用sticky定位保持轮播图位置固定 */
            top: 60px; /* 调整顶部位置，与标签对齐 */
            max-width: 50%; /* 限制最大宽度 */
            margin-top: 15px; /* 添加顶部外边距以对齐标签 */
        }
        
        /* 添加渐变边框效果 */
        .carousel-container::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, var(--primary-light), var(--primary), var(--primary-dark), var(--primary-light));
            z-index: -1;
            border-radius: calc(var(--border-radius-lg) + 2px);
            opacity: 0.7;
            animation: borderGlow 3s infinite alternate;
        }
        
        /* 添加装饰元素 */
        .carousel-container::after {
            content: '';
            position: absolute;
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(65, 105, 225, 0.1) 0%, rgba(65, 105, 225, 0) 70%);
            top: -50px;
            right: -50px;
            z-index: 0;
        }
        
        /* 添加边框动画 */
        @keyframes borderGlow {
            0% {
                opacity: 0.3;
                filter: blur(5px);
            }
            100% {
                opacity: 0.7;
                filter: blur(3px);
            }
        }
        
        /* 轮播容器悬停效果 */
        .carousel-container:hover {
            box-shadow: 0 20px 50px rgba(65, 105, 225, 0.2);
            transform: translateY(-5px);
        }
        
        /* 添加浮动小圆点装饰 */
        .floating-grid::before {
            content: '';
            position: absolute;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            background-color: var(--primary-light);
            opacity: 0.7;
            top: 20px;
            left: 20px;
            z-index: 10;
            filter: blur(1px);
            animation: pulse 3s infinite alternate;
        }
        
        .floating-grid::after {
            content: '';
            position: absolute;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: var(--primary);
            opacity: 0.7;
            bottom: 20px;
            right: 20px;
            z-index: 10;
            filter: blur(1px);
            animation: pulse 2s infinite alternate-reverse;
        }
        
        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 0.7;
            }
            100% {
                transform: scale(1.3);
                opacity: 0.4;
            }
        }
        
        .carousel-layer {
            position: absolute;
            width: 100%;
            height: 130px;
            display: flex;
            overflow: hidden;
        }
        
        .carousel-layer:nth-child(1) {
            top: 10px;
        }
        
        .carousel-layer:nth-child(2) {
            top: 150px;
        }
        
        .carousel-layer:nth-child(3) {
            top: 290px;
        }
        
        .carousel-track {
            display: flex;
            animation-duration: 25s;
            animation-timing-function: linear;
            animation-iteration-count: infinite;
            width: fit-content;
        }
        
        .carousel-layer:nth-child(1) .carousel-track {
            animation-name: scrollRight;
        }
        
        .carousel-layer:nth-child(2) .carousel-track {
            animation-name: scrollLeft;
        }
        
        .carousel-layer:nth-child(3) .carousel-track {
            animation-name: scrollRight;
        }
        
        .carousel-item {
            flex: 0 0 auto;
            height: 110px;
            margin: 10px;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            transition: transform 0.3s, box-shadow 0.3s;
            position: relative;
            background-color: white;
        }
        
        .carousel-item:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: var(--shadow);
        }
        
        .carousel-item img {
            width: 190px;
            height: 110px;
            object-fit: contain;
            background-color: var(--secondary-bg);
            padding: 15px;
            transition: transform 0.3s;
        }
        
        .carousel-item:hover img {
            transform: scale(1.05);
        }
        
        /* 添加被挡住效果 */
        .carousel-mask {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, rgba(255,255,255,0) 70%, rgba(255,255,255,0.8) 100%);
            z-index: 2;
            pointer-events: none;
        }
        
        .carousel-layer:nth-child(1)::before,
        .carousel-layer:nth-child(3)::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 60px;
            height: 100%;
            background: linear-gradient(90deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0) 100%);
            z-index: 3;
            pointer-events: none;
        }
        
        .carousel-layer:nth-child(1)::after,
        .carousel-layer:nth-child(3)::after {
            content: "";
            position: absolute;
            top: 0;
            right: 0;
            width: 60px;
            height: 100%;
            background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.9) 100%);
            z-index: 3;
            pointer-events: none;
        }
        
        .carousel-layer:nth-child(2)::before {
            content: "";
            position: absolute;
            top: 0;
            right: 0;
            width: 60px;
            height: 100%;
            background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.9) 100%);
            z-index: 3;
            pointer-events: none;
        }
        
        .carousel-layer:nth-child(2)::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 60px;
            height: 100%;
            background: linear-gradient(90deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0) 100%);
            z-index: 3;
            pointer-events: none;
        }
        
        @keyframes scrollRight {
            0% {
                transform: translateX(0);
            }
            100% {
                transform: translateX(-50%);
            }
        }
        
        @keyframes scrollLeft {
            0% {
                transform: translateX(-50%);
            }
            100% {
                transform: translateX(0);
            }
        }
        
        /* 功能区域 */
        .features-section {
            background-color: #fff;
            position: relative;
            z-index: 1;
            text-align: center; /* 确保居中 */
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 30px;
            margin-top: 60px;
            text-align: center; /* 确保内容居中 */
        }
        
        .feature-card {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            padding: 40px 30px;
            transition: transform 0.3s, box-shadow 0.3s;
            height: 100%;
            border: 1px solid rgba(65, 105, 225, 0.05);
            box-shadow: var(--shadow-sm);
            position: relative;
            overflow: hidden;
            text-align: center; /* 确保内容居中 */
        }
        
        .feature-card::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(90deg, var(--primary-light), var(--primary));
            transform: scaleX(0);
            transform-origin: left;
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover::before {
            transform: scaleX(1);
        }
        
        .feature-card:hover {
            transform: translateY(-15px);
            box-shadow: var(--shadow);
        }
        
        .feature-icon {
            width: 80px;
            height: 80px;
            margin-bottom: 25px;
            transition: transform 0.3s;
        }
        
        .feature-card:hover .feature-icon {
            transform: scale(1.1);
        }
        
        .feature-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
            color: var(--text-primary);
            text-align: center; /* 确保标题居中 */
        }
        
        .feature-desc {
            font-size: 16px;
            color: var(--text-secondary);
            margin-bottom: 25px;
            line-height: 1.7;
            text-align: center; /* 确保描述文本居中 */
        }
        
        /* 添加更多样式确保内容居中 */
        .feature-icon-wrapper {
            margin-left: auto;
            margin-right: auto;
        }
        
        .feature-tag {
            display: inline-block;
            padding: 8px 18px;
            background-color: rgba(65, 105, 225, 0.1);
            color: var(--primary);
            border-radius: 50px;
            font-size: 14px;
            margin-top: 15px;
            font-weight: 500;
            transition: background-color 0.3s;
        }
        
        .feature-card:hover .feature-tag {
            background-color: rgba(65, 105, 225, 0.15);
        }
        
        /* 需求分析区域 */
        .analysis-section {
            background-color: #fff;
            position: relative;
        }
        
        .analysis-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 60px;
        }
        
        .analysis-content {
            flex: 1;
            text-align: center; /* 添加居中对齐 */
        }
        
        .analysis-title {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 25px;
            color: var(--text-primary);
            position: relative;
            display: inline-block;
            text-align: center; /* 添加居中对齐 */
            left: 50%; /* 添加居中定位 */
            transform: translateX(-50%); /* 添加居中定位 */
        }
        
        .analysis-title::after {
            content: "";
            position: absolute;
            bottom: -10px;
            left: 50%; /* 修改为居中 */
            transform: translateX(-50%); /* 添加居中定位 */
            width: 60px;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-light), var(--primary));
            border-radius: 2px;
        }
        
        .analysis-desc {
            font-size: 18px;
            color: var(--text-secondary);
            margin-bottom: 35px;
            line-height: 1.7;
            text-align: center; /* 添加居中对齐 */
        }
        
        .analysis-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 25px;
            justify-content: center; /* 添加居中对齐 */
        }
        
        .analysis-tag {
            padding: 10px 25px;
            background-color: rgba(65, 105, 225, 0.08);
            border-radius: 50px;
            font-size: 15px;
            color: var(--primary);
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
            border: 1px solid rgba(65, 105, 225, 0.1);
        }
        
        .analysis-tag:hover {
            background-color: rgba(65, 105, 225, 0.12);
            transform: translateY(-3px);
        }
        
        .analysis-tag.active {
            background-color: var(--primary);
            color: white;
            box-shadow: 0 5px 15px rgba(65, 105, 225, 0.3);
        }
        
        .analysis-image {
            flex: 1;
            display: flex;
            justify-content: center;
        }
        
        .analysis-image img {
            max-width: none;
            width: 100%;
            height: auto;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            transition: transform 0.3s, box-shadow 0.3s;
            object-fit: contain;  /* 改为contain以保持图片比例不变形 */
            image-rendering: -webkit-optimize-contrast;  /* 提高图片清晰度 */
            image-rendering: crisp-edges;  /* 使边缘更清晰 */
        }
        
        .analysis-image img:hover {
            transform: scale(1.03);
            box-shadow: var(--shadow-lg);
        }
        
        /* 功能定制区域 */
        .customization-section {
            background-color: var(--secondary-bg);
            position: relative;
        }
        
        .customization-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 60px;
        }
        
        .customization-content {
            flex: 1;
            text-align: center; /* 添加居中对齐 */
        }
        
        .customization-title {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 25px;
            color: var(--text-primary);
            position: relative;
            display: inline-block;
            text-align: center; /* 添加居中对齐 */
            left: 50%; /* 添加居中定位 */
            transform: translateX(-50%); /* 添加居中定位 */
        }
        
        .customization-title::after {
            content: "";
            position: absolute;
            bottom: -10px;
            left: 50%; /* 修改为居中 */
            transform: translateX(-50%); /* 添加居中定位 */
            width: 60px;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-light), var(--primary));
            border-radius: 2px;
        }
        
        .customization-desc {
            font-size: 18px;
            color: var(--text-secondary);
            margin-bottom: 35px;
            line-height: 1.7;
            text-align: center; /* 添加居中对齐 */
        }
        
        .customization-features {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-top: 25px;
        }
        
        .customization-feature {
            padding: 20px 15px;
            background-color: white;
            border-radius: var(--border-radius);
            text-align: center;
            transition: all 0.3s;
            cursor: pointer;
            font-weight: 500;
            box-shadow: var(--shadow-sm);
            border: 1px solid rgba(65, 105, 225, 0.05);
        }
        
        .customization-feature:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow);
            color: var(--primary);
        }
        
        .customization-image {
            flex: 1;
            display: flex;
            justify-content: center;
        }
        
        .customization-image img {
            max-width: 100%;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .customization-image img:hover {
            transform: scale(1.03);
            box-shadow: var(--shadow-lg);
        }
        
        /* 资金安全区域 */
        .security-section {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            text-align: center;
            color: white;
            position: relative;
            overflow: hidden;
        }
        
        .security-section::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: radial-gradient(circle, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
            background-size: 20px 20px;
            opacity: 0.5;
        }
        
        .security-title {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 25px;
            position: relative;
            z-index: 2;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .security-desc {
            font-size: 18px;
            max-width: 800px;
            margin: 0 auto 40px;
            line-height: 1.7;
            position: relative;
            z-index: 2;
            opacity: 0.9;
        }
        
        /* 支付方式区域 */
        .payment-section {
            background-color: #e8f4ff;
            position: relative;
        }
        
        .payment-container {
            display: flex;
            align-items: center;
            gap: 60px;
        }
        
        .payment-content {
            flex: 1;
            text-align: center; /* 添加居中对齐 */
        }
        
        .payment-title {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 25px;
            color: var(--text-primary);
            position: relative;
            display: inline-block;
            text-align: center; /* 添加居中对齐 */
            left: 50%; /* 添加居中定位 */
            transform: translateX(-50%); /* 添加居中定位 */
        }
        
        .payment-title::after {
            content: "";
            position: absolute;
            bottom: -10px;
            left: 50%; /* 修改为居中 */
            transform: translateX(-50%); /* 添加居中定位 */
            width: 60px;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-light), var(--primary));
            border-radius: 2px;
        }
        
        .payment-desc {
            font-size: 18px;
            color: var(--text-secondary);
            margin-bottom: 35px;
            line-height: 1.7;
            text-align: center; /* 添加居中对齐 */
        }
        
        .payment-methods {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 25px;
            justify-content: center; /* 添加居中对齐 */
        }
        
        .payment-method {
            padding: 12px 25px;
            background-color: rgba(65, 105, 225, 0.08);
            border-radius: 50px;
            font-size: 15px;
            color: var(--primary);
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
            border: 1px solid rgba(65, 105, 225, 0.1);
        }
        
        .payment-method:hover {
            background-color: rgba(65, 105, 225, 0.12);
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(65, 105, 225, 0.1);
        }
        
        .payment-image {
            flex: 1;
            display: flex;
            justify-content: center;
        }
        
        .payment-image img {
            max-width: 100%;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .payment-image img:hover {
            transform: scale(1.03);
            box-shadow: var(--shadow-lg);
        }
        
        /* 账户隔离区域 */
        .account-section {
            background-color: #e8f4ff;
            position: relative;
        }
        
        .account-container {
            display: flex;
            align-items: center;
            gap: 60px;
        }
        
        .account-content {
            flex: 1;
            text-align: center; /* 添加居中对齐 */
        }
        
        .account-title {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 25px;
            color: var(--text-primary);
            position: relative;
            display: inline-block;
            text-align: center; /* 添加居中对齐 */
            left: 50%; /* 添加居中定位 */
            transform: translateX(-50%); /* 添加居中定位 */
        }
        
        .account-title::after {
            content: "";
            position: absolute;
            bottom: -10px;
            left: 50%; /* 修改为居中 */
            transform: translateX(-50%); /* 添加居中定位 */
            width: 60px;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-light), var(--primary));
            border-radius: 2px;
        }
        
        .account-desc {
            font-size: 18px;
            color: var(--text-secondary);
            margin-bottom: 35px;
            line-height: 1.7;
            text-align: center; /* 添加居中对齐 */
        }
        
        .account-features {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 25px;
            justify-content: center; /* 添加居中对齐 */
        }
        
        .account-feature {
            padding: 12px 25px;
            background-color: white;
            border-radius: 50px;
            font-size: 15px;
            color: var(--primary);
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
            box-shadow: var(--shadow-sm);
            border: 1px solid rgba(65, 105, 225, 0.05);
        }
        
        .account-feature:hover {
            background-color: rgba(65, 105, 225, 0.05);
            transform: translateY(-3px);
            box-shadow: var(--shadow);
        }
        
        .account-image {
            flex: 1;
            display: flex;
            justify-content: center;
        }
        
        .account-image img {
            max-width: 100%;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .account-image img:hover {
            transform: scale(1.03);
            box-shadow: var(--shadow-lg);
        }
        
        /* 行业适用区域 */
        .industry-section {
            background-color: #f9f9f9;
            padding: 80px 0;
            position: relative;
            overflow: hidden;
            min-height: 800px; /* 确保整个区域高度一致 */
        }
        
        .industry-title {
            text-align: center;
            margin-bottom: 40px;
            font-size: 28px;
            font-weight: 600;
            color: var(--text-dark);
        }
        
        .industry-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 50px;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .industry-tab {
            padding: 12px 25px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 1px solid #eee;
        }
        
        .industry-tab:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .industry-tab.active {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        
        /* 行业内容面板样式 */
        .industry-panel {
            display: none;
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.5s ease, transform 0.5s ease;
            position: relative;
            /* 移除固定最小高度，使面板高度根据内容自适应 */
            width: 100%;
            height: 0; /* 添加初始高度为0，防止空白区域 */
            overflow: hidden; /* 隐藏溢出内容 */
        }
        
        .industry-panel.active {
            display: grid;
            opacity: 1;
            transform: translateY(0);
            height: auto; /* 激活时恢复自动高度 */
            overflow: visible; /* 激活时显示内容 */
        }
        
        .features-grid-alt {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 25px;
            position: relative;
            z-index: 2;
            transition: opacity 0.3s ease;
            /* 移除固定行模板，让高度自适应 */
            max-width: 1200px; /* 控制最大宽度 */
            margin: 0 auto; /* 居中显示 */
        }
        
        .feature-card-alt {
            background-color: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: var(--border-radius);
            padding: 35px 25px;
            text-align: center;
            transition: transform 0.3s, box-shadow 0.3s;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            height: 100%; /* 确保所有卡片高度一致 */
            display: flex; /* 使用弹性布局 */
            flex-direction: column; /* 垂直排列 */
            justify-content: flex-start; /* 从顶部开始布局 */
            min-height: 280px; /* 增加最小高度，确保所有卡片有足够空间 */
            position: relative; /* 添加相对定位 */
            overflow: hidden; /* 防止内容溢出 */
        }
        
        /* 确保所有面板卡片的内容一致性 */
        .industry-panel .feature-card-alt {
            min-height: 280px; /* 统一最小高度 */
            padding-bottom: 70px; /* 为底部标签预留空间 */
        }
        
        .feature-card-alt:hover {
            transform: translateY(-15px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
        }
        
        .feature-title-alt {
            font-size: 22px;
            font-weight: bold;
            margin-bottom: 15px;
            color: var(--text-primary);
        }
        
        .feature-desc-alt {
            font-size: 16px;
            color: var(--text-secondary);
            margin-bottom: 25px;
            line-height: 1.7;
            flex-grow: 1; /* 让描述部分自适应高度 */
        }
        
        .feature-tag-alt {
            display: inline-block;
            padding: 8px 20px;
            background-color: rgba(65, 105, 225, 0.1);
            color: var(--primary);
            border-radius: 50px;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.3s;
            position: absolute; /* 绝对定位 */
            bottom: 25px; /* 距离底部的距离 */
            left: 50%; /* 水平居中 */
            transform: translateX(-50%); /* 水平居中 */
            width: auto; /* 根据内容调整宽度 */
            min-width: 120px; /* 最小宽度 */
        }
        
        .feature-card-alt:hover .feature-tag-alt {
            background-color: rgba(65, 105, 225, 0.15);
        }
        
        /* 快速开店区域 */
        .quick-start-section {
            background-color: var(--primary-bg);
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .quick-start-title {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-primary);
        }
        
        .quick-start-title img {
            height: 50px;
            margin-right: 20px;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }
        
        .quick-start-cards {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 25px;
            margin-top: 60px;
        }
        
        .quick-start-card {
            background-color: white;
            border-radius: var(--border-radius);
            padding: 40px 25px;
            text-align: center;
            transition: transform 0.3s, box-shadow 0.3s;
            box-shadow: var(--shadow-sm);
            border: 1px solid rgba(65, 105, 225, 0.05);
            position: relative;
            overflow: hidden;
        }
        
        .quick-start-card::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(90deg, var(--primary-light), var(--primary));
            transform: scaleX(0);
            transform-origin: left;
            transition: transform 0.3s ease;
        }
        
        .quick-start-card:hover::before {
            transform: scaleX(1);
        }
        
        .quick-start-card:hover {
            transform: translateY(-15px);
            box-shadow: var(--shadow);
        }
        
        .quick-start-card-title {
            font-size: 22px;
            font-weight: bold;
            margin-bottom: 15px;
            color: var(--text-primary);
        }
        
        .quick-start-card-desc {
            font-size: 16px;
            color: var(--text-secondary);
            line-height: 1.7;
        }
        
        /* 侧边栏 */
        .sidebar {
            position: fixed;
            right: 30px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            z-index: 99;
        }
        
        .sidebar-item {
            width: 60px;
            height: 60px;
            background-color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.3s;
            font-size: 24px;
            border: 1px solid rgba(65, 105, 225, 0.1);
        }
        
        .sidebar-item:hover {
            transform: scale(1.1) translateY(-5px);
            box-shadow: 0 10px 20px rgba(65, 105, 225, 0.15);
            color: var(--primary);
        }
        
        /* 3D特性卡片网格 */
        .feature-3d-section {
            background: linear-gradient(180deg, var(--secondary-bg) 0%, #ffffff 100%);
            padding: 80px 0;
            position: relative;
            overflow: hidden;
        }
        
        .feature-3d-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin-top: 60px;
        }
        
        .feature-3d-card {
            background-color: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border-radius: var(--border-radius);
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
            box-shadow: var(--shadow-sm);
            border: 1px solid rgba(65, 105, 225, 0.05);
            opacity: 1 !important; /* 强制可见 */
            visibility: visible !important; /* 强制可见 */
        }
        
        .feature-3d-card:hover {
            transform: translateY(-15px) rotateY(5deg);
            box-shadow: var(--shadow-lg);
        }
        
        .feature-3d-card.animate {
            opacity: 1 !important; /* 覆盖animate类的透明度设置 */
            visibility: visible !important; /* 覆盖animate类的可见性设置 */
        }
        
        .feature-3d-image {
            height: 200px;
            background-color: var(--secondary-bg);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 30px;
            position: relative;
            overflow: hidden;
            opacity: 1; /* 确保默认可见 */
            visibility: visible; /* 确保默认可见 */
        }
        
        .feature-3d-image::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(65, 105, 225, 0.05) 0%, rgba(65, 105, 225, 0) 100%);
            z-index: 1;
        }
        
        .feature-3d-image img, .feature-3d-image i {
            max-width: 100%;
            max-height: 100%;
            transition: transform 0.5s;
            position: relative;
            z-index: 2;
            opacity: 1; /* 确保默认可见 */
            visibility: visible; /* 确保默认可见 */
        }
        
        .feature-3d-card:hover .feature-3d-image img {
            transform: scale(1.1) translateY(-5px);
        }
        
        .feature-3d-content {
            padding: 30px 25px;
            background-color: white;
            /* 移除固定高度，让内容自然展开 */
            min-height: 130px; /* 改为最小高度而不是固定高度 */
            position: relative; /* 添加相对定位 */
            /* 移除overflow:hidden，让内容完全显示 */
            display: flex; /* 使用flex布局 */
            flex-direction: column; /* 垂直排列 */
        }
        
        .feature-3d-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 12px;
            color: var(--text-primary);
            transition: color 0.3s;
        }
        
        .feature-3d-card:hover .feature-3d-title {
            color: var(--primary);
        }
        
        .feature-3d-subtitle {
            font-size: 16px;
            color: var(--text-secondary);
            line-height: 1.6;
            /* 移除最大高度限制 */
            /* 移除溢出隐藏和省略号 */
            /* display: -webkit-box; */
        }
        
        /* 页脚区域 */
        .footer {
            padding-top: 50px;
            padding-bottom: 20px;
            background: linear-gradient(to bottom, #2c3e50, #1a252f);
            color: #fff;
            position: relative;
            box-shadow: 0 -5px 15px rgba(0, 0, 0, 0.05);
        }
        
        .footer::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5z-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.03' fill-rule='evenodd'/%3E%3C/svg%3E");
            opacity: 0.3;
        }
        
        .footer-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            text-align: left;
            gap: 40px;
            position: relative;
            z-index: 1;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .footer-column {
            flex: 1;
            min-width: 230px;
            transition: all 0.3s ease;
        }
        
        .footer-column:hover {
            transform: translateY(-5px);
        }
        
        .footer-logo {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .footer-logo img {
            height: 42px;
            margin-right: 12px;
            filter: brightness(0) invert(1);
            transition: transform 0.3s ease;
        }
        
        .footer-logo:hover img {
            transform: scale(1.1);
        }
        
        .footer-description {
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.6;
            margin-bottom: 22px;
            font-size: 14px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }
        
        .footer-social {
            display: flex;
            gap: 12px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .footer-social-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 38px;
            height: 38px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transition: all 0.3s ease;
            font-size: 16px;
            backdrop-filter: blur(3px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .footer-social-icon:hover {
            background: var(--primary);
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            color: white;
        }
        
        .footer-social-icon i {
            transition: transform 0.3s ease;
        }
        
        .footer-social-icon:hover i {
            transform: scale(1.15);
        }
        
        .footer-title {
            color: white;
            font-size: 19px;
            font-weight: 600;
            margin-bottom: 18px;
            position: relative;
            padding-bottom: 10px;
            letter-spacing: 0.2px;
        }
        
        .footer-title::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-light), var(--primary));
            border-radius: 2px;
            transition: width 0.3s ease;
        }
        
        .footer-column:hover .footer-title::after {
            width: 70px;
        }
        
        .footer-links {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        
        .footer-links a {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
            position: relative;
            transition: all 0.3s ease;
            padding-left: 0;
            display: inline-block;
        }
        
        .footer-links a::before {
            content: "›";
            position: absolute;
            left: -15px;
            opacity: 0;
            transition: all 0.3s ease;
        }
        
        .footer-links a:hover {
            color: white;
            padding-left: 5px;
            transform: translateX(3px);
        }
        
        .footer-links a:hover::before {
            opacity: 1;
            left: -10px;
        }
        
        .footer-contact {
            display: flex;
            align-items: flex-start;
            margin-bottom: 14px;
            transition: all 0.3s ease;
        }
        
        .footer-contact:hover {
            transform: translateX(3px);
        }
        
        .footer-contact-icon {
            width: 24px;
            margin-right: 12px;
            margin-top: 3px;
            color: var(--primary-light);
            transition: transform 0.3s ease;
        }
        
        .footer-contact:hover .footer-contact-icon {
            transform: scale(1.2);
            color: white;
        }
        
        .footer-contact-text {
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.5;
            font-size: 14px;
            flex: 1;
            transition: color 0.3s ease;
        }
        
        .footer-contact:hover .footer-contact-text {
            color: white;
        }
        
        .footer-bottom {
            text-align: center;
            margin-top: 50px;
            padding-top: 25px;
            padding-bottom: 15px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            z-index: 1;
        }
        
        .footer-bottom::before {
            content: "";
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-light), var(--primary));
            border-radius: 3px;
        }
        
        .footer-bottom p {
            color: rgba(255, 255, 255, 0.6);
            font-size: 13px;
        }
        
        /* 版权区域样式优化 */
        .copyright {
            width: 100%;
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .copyright-content {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .copyright-text {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 7px;
            transition: color 0.3s ease;
        }
        
        .copyright-text:hover {
            color: white;
        }
        
        .copyright-text i {
            color: var(--primary-light);
            font-size: 16px;
            transition: transform 0.3s ease;
        }
        
        .beian-info {
            display: flex;
            align-items: center;
            gap: 20px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .beian-link {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 7px;
            transition: all 0.3s ease;
            position: relative;
            padding: 5px 10px;
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.05);
        }
        
        .beian-link::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 0;
            background-color: var(--primary);
            opacity: 0.2;
            transition: height 0.3s ease;
            z-index: -1;
            border-radius: 4px;
        }
        
        .beian-link:hover {
            color: #fff;
            transform: translateY(-2px);
        }
        
        .beian-link:hover::after {
            height: 100%;
        }
        
        .beian-link i {
            font-size: 16px;
            color: var(--primary-light);
            transition: transform 0.3s ease;
        }
        
        .beian-link:hover i {
            transform: scale(1.2);
        }
        
        /* 响应式设计 */
        @media (max-width: 1024px) {
            .features-grid, .features-grid-alt, .feature-3d-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .quick-start-cards {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .analysis-container, .customization-container, .payment-container, .account-container {
                flex-direction: column;
            }
            
            /* 响应式时保持标题居中 */
            .analysis-content, .customization-content, .payment-content, .account-content {
                text-align: center;
            }
            
            .analysis-title, .customization-title, .payment-title, .account-title {
                text-align: center;
            }
            
            .customization-features {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .hero-container {
                flex-direction: column;
            }
            
            .carousel-container {
                width: 100%;
            }
            
            .footer-container {
                gap: 30px;
            }
            
            .footer-column {
                min-width: 200px;
                flex-basis: calc(50% - 30px);
            }
            
            .footer-bottom {
                margin-top: 30px;
            }
            
            .copyright-content {
                flex-direction: column;
                gap: 15px;
            }
            
            .section {
                padding: 60px 0;
            }
            
            .hero-title {
                font-size: 36px;
            }
            
            .section-title {
                font-size: 32px;
            }
        }
        
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }
            
            .features-grid, .features-grid-alt, .feature-3d-grid {
                grid-template-columns: 1fr;
            }
            
            .quick-start-cards {
                grid-template-columns: 1fr;
            }
            
            .customization-features {
                grid-template-columns: 1fr;
            }
            
            .sidebar {
                display: none;
            }
            
            .announcement-container {
                flex-wrap: wrap;
            }
            
            .footer-column {
                flex-basis: 100%;
            }
            
            .footer-bottom {
                margin-top: 20px;
                padding-top: 20px;
            }
            
            .beian-info {
                gap: 10px;
            }
        }

        /* 添加Font Awesome图标样式 */
        .announcement-icon i {
            color: var(--primary);
            font-size: 32px;
        }

        .announcement-icon {
            width: 70px;
            height: 70px;
            background-color: rgba(65, 105, 225, 0.1);
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            transition: transform 0.3s;
        }

        /* 特性卡片图标样式 */
        .feature-3d-image {
            text-align: center;
            margin-bottom: 20px;
            min-height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 1; /* 确保默认可见 */
            transition: all 0.5s ease;
        }
        
        .feature-3d-image i {
            font-size: 60px;
            color: var(--primary);
            filter: drop-shadow(0 5px 15px rgba(65, 105, 225, 0.3));
            transition: all 0.5s ease;
            opacity: 1; /* 确保图标默认可见 */
            visibility: visible; /* 确保图标默认可见 */
            transform: scale(1); /* 初始尺寸 */
        }
        
        .feature-3d-card:hover .feature-3d-image i {
            transform: scale(1.1) rotate(5deg);
            color: var(--primary-light);
        }

        /* 页脚社交图标样式 */
        .footer-social-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background-color: rgba(65, 105, 225, 0.1);
            border-radius: 50%;
            margin-right: 10px;
            transition: all 0.3s;
        }
        
        .footer-social-icon i {
            font-size: 18px;
            color: var(--primary);
        }
        
        .footer-social-icon:hover {
            background-color: var(--primary);
            transform: translateY(-5px);
        }
        
        .footer-social-icon:hover i {
            color: white;
        }

        /* 特性卡片图标样式 */
        .feature-icon-wrapper {
            width: 80px;
            height: 80px;
            background-color: rgba(65, 105, 225, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            transition: all 0.3s ease;
        }
        
        .feature-icon-wrapper i {
            font-size: 36px;
            color: var(--primary);
            transition: all 0.3s ease;
        }
        
        .feature-card:hover .feature-icon-wrapper {
            background-color: var(--primary);
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(65, 105, 225, 0.2);
        }
        
        .feature-card:hover .feature-icon-wrapper i {
            color: white;
        }
        
        /* 添加渐变文本效果 */
        .gradient-text {
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
            background-size: 200% auto;
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            color: transparent;
            animation: gradientFlow 5s linear infinite;
        }
        
        @keyframes gradientFlow {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        /* 增强标签区域 */
        .tab-container {
            display: flex;
            margin-bottom: 40px;
            position: relative;
            background: rgba(255, 255, 255, 0.8);
            padding: 12px;
            border-radius: var(--border-radius);
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            justify-content: center;
            border: 1px solid rgba(65, 105, 225, 0.1);
            max-width: fit-content;
            margin-left: auto;
            margin-right: auto;
        }
        
        .tab-item {
            cursor: pointer;
            padding: 10px 20px;
            border-radius: var(--border-radius-sm);
            font-weight: 500;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: 8px;
            position: relative;
            z-index: 1;
            transition: all 0.3s ease;
        }
        
        .tab-item i {
            font-size: 14px;
            opacity: 0.8;
        }
        
        .tab-item.active {
            color: var(--primary);
            font-weight: 600;
        }
        
        .tab-item:hover:not(.active) {
            color: var(--text-primary);
            background-color: rgba(65, 105, 225, 0.05);
        }
        
        .tab-underline {
            position: absolute;
            bottom: 0;
            height: 3px;
            background: var(--primary);
            border-radius: 3px;
            transition: left 0.3s ease, width 0.3s ease;
            z-index: 1;
            box-shadow: 0 0 6px rgba(65, 105, 225, 0.4);
            opacity: 0.85;
            width: 0; /* 初始宽度为0 */
        }
        
        /* 标签内容样式 */
        .tab-content {
            position: relative;
        }
        
        .tab-panel {
            display: none;
            animation: fadeIn 0.5s ease forwards;
        }
        
        .tab-panel.active {
            display: block;
        }
        
        /* 标签 */
        .tag {
            display: inline-block;
            padding: 5px 12px;
            background: rgba(65, 105, 225, 0.1);
            color: var(--primary);
            border-radius: 30px;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 15px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .tag::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                90deg,
                transparent 0%,
                rgba(255, 255, 255, 0.8) 50%,
                transparent 100%
            );
            z-index: 1;
            animation: tagShimmer 3s infinite;
        }
        
        @keyframes tagShimmer {
            0% {
                left: -100%;
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            50% {
                opacity: 1;
            }
            100% {
                left: 200%;
                opacity: 0;
            }
        }
        
        .tag:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(65, 105, 225, 0.15);
        }
        
        .tag:hover::after {
            animation: tagShimmerFast 1.2s ease-out;
        }
        
        @keyframes tagShimmerFast {
            0% {
                left: -100%;
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            50% {
                opacity: 0.9;
            }
            100% {
                left: 200%;
                opacity: 0;
            }
        }
        
        /* 脉冲按钮动画 */
        .pulse-animation {
            position: relative;
            display: flex;
            align-items: center;
            gap: 8px;
            overflow: hidden;
        }
        
        .pulse-animation i {
            transition: transform 0.3s ease;
        }
        
        .pulse-animation:hover i {
            transform: translateX(4px);
        }
        
        .pulse-animation::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.6s ease;
        }
        
        .pulse-animation:active::after {
            width: 300px;
            height: 300px;
            opacity: 0;
        }
        
        /* 轮播指示器 */
        .carousel-controls {
            position: absolute;
            bottom: 20px;
            left: 0;
            width: 100%;
            display: flex;
            justify-content: center;
            z-index: 10;
        }
        
        .carousel-indicators {
            display: flex;
            gap: 8px;
        }
        
        .indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .indicator.active {
            background-color: #fff;
            transform: scale(1.2);
        }
        
        /* 增强动画 */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* 响应式调整 */
        @media (max-width: 991px) {
            .hero-container {
                flex-direction: column;
                min-height: auto; /* 移动端不需要固定最小高度 */
            }
            
            .hero-content-wrapper {
                margin-right: 0;
                margin-bottom: 40px;
                width: 100%;
                min-height: 300px; /* 移动端调整最小高度 */
            }
            
            .tab-container {
                width: 100%;
                overflow-x: auto;
                justify-content: flex-start;
                padding: 8px 15px; /* 调整内边距 */
            }
            
            .carousel-container {
                width: 100%;
                position: relative; /* 移动端使用相对定位 */
                top: 0;
                max-width: 100%;
                height: 350px; /* 调整移动端高度 */
            }
            
            .tab-panel {
                position: relative; /* 移动端使用相对定位 */
                height: auto;
            }
            
            .floating-row {
                height: calc(100% / 3 - 10px); /* 调整移动端高度 */
                margin-bottom: 15px; /* 减小行间距 */
            }
            
            .floating-card {
                width: 160px; /* 移动端调整卡片宽度 */
            }
        }
        
        @media (max-width: 768px) {
            .hero-content-wrapper {
                min-height: 250px; /* 较小屏幕调整最小高度 */
            }
            
            .carousel-container {
                height: 320px; /* 较小屏幕调整高度 */
            }
            
            .floating-card {
                width: 140px; /* 较小屏幕调整卡片宽度 */
            }
            
            .tab-item {
                padding: 6px 12px;
                font-size: 14px;
            }
            
            .tab-underline {
                height: calc(100% - 8px);
                top: 4px;
            }
        }
        
        /* 图片网格布局样式 */
        .image-grid {
            display: flex;
            flex-direction: column;
            gap: 15px;
            height: 100%;
            position: relative;
            z-index: 1;
        }
        
        .grid-row {
            display: flex;
            gap: 15px;
            height: calc(100% / 3);
        }
        
        .grid-item {
            flex: 1;
            min-width: 0;
            position: relative;
        }
        
        .image-card {
            height: 100%;
            width: 100%;
            border-radius: 15px;
            overflow: hidden;
            position: relative;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
            transform: translateZ(0);
            background: linear-gradient(135deg, #f5f7ff 0%, #e6ebff 100%);
        }
        
        .image-card img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: all 0.5s ease;
            filter: brightness(0.85) saturate(1.2);
        }
        
        .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                to bottom,
                rgba(30, 65, 180, 0.05) 0%,
                rgba(30, 65, 180, 0.4) 100%
            );
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: all 0.4s ease;
        }
        
        .overlay-content {
            text-align: center;
            color: white;
            transform: translateY(20px);
            transition: all 0.4s ease;
        }
        
        .overlay-content i {
            font-size: 28px;
            margin-bottom: 10px;
            display: block;
            filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.3));
        }
        
        .overlay-content span {
            display: block;
            font-weight: 500;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }
        
        /* 悬停效果 */
        .image-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 15px 30px rgba(30, 65, 180, 0.15), 
                        0 5px 15px rgba(0, 0, 0, 0.08);
            z-index: 2;
        }
        
        .image-card:hover img {
            transform: scale(1.08);
            filter: brightness(0.7) saturate(1.3);
        }
        
        .image-card:hover .image-overlay {
            opacity: 1;
        }
        
        .image-card:hover .overlay-content {
            transform: translateY(0);
        }
        
        /* 高亮卡片效果 */
        .image-card.highlight {
            box-shadow: 0 10px 25px rgba(65, 105, 225, 0.25);
            border: 2px solid rgba(65, 105, 225, 0.2);
        }
        
        .image-card.highlight:hover {
            box-shadow: 0 15px 35px rgba(65, 105, 225, 0.3);
            border-color: rgba(65, 105, 225, 0.4);
        }
        
        /* 添加3D效果 */
        .image-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                45deg,
                rgba(255, 255, 255, 0.2) 0%,
                rgba(255, 255, 255, 0) 60%
            );
            z-index: 2;
            opacity: 0;
            transition: opacity 0.4s ease;
        }
        
        .image-card:hover::before {
            opacity: 1;
        }
        
        /* 网格动画效果 */
        .grid-item:nth-child(1) .image-card {
            animation-delay: 0.1s;
        }
        
        .grid-item:nth-child(2) .image-card {
            animation-delay: 0.2s;
        }
        
        .grid-item:nth-child(3) .image-card {
            animation-delay: 0.3s;
        }
        
        /* 响应式调整 */
        @media (max-width: 768px) {
            .grid-row {
                flex-direction: column;
                height: auto;
            }
            
            .grid-item {
                height: 120px;
            }
            
            .image-card {
                height: 100%;
            }
        }
        
        /* 浮动网格布局及滚动效果 */
        .floating-grid {
            position: relative;
            width: 100%;
            height: 100%;
            overflow: hidden;
            border-radius: 16px;
            box-shadow: 0 15px 40px rgba(30, 65, 180, 0.18);
            background: linear-gradient(145deg, #f1f5ff, #e8f0ff);
            perspective: 1000px;
            transform-style: preserve-3d;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            transform: translateZ(0); /* 强制启用硬件加速 */
            padding: 10px 0; /* 添加内边距，优化内部元素位置 */
        }
        
        .floating-row {
            display: flex;
            gap: 24px; /* 增加间距使布局更加均匀 */
            height: calc(100% / 3 - 16px); /* 调整高度计算方式使三行均匀分布 */
            margin-bottom: 24px; /* 增加行间距 */
            position: relative;
            transform-style: preserve-3d;
            width: max-content;
            align-items: center; /* 确保卡片垂直居中 */
            will-change: transform; /* 优化动画性能 */
        }
        
        /* 为每行创建克隆元素使循环无缝 */
        .floating-row::after {
            content: "";
            display: block;
            flex: 0 0 24px; /* 与gap相同，确保无缝连接 */
        }
        
        /* 第一行向右滚动，调整速度 */
        .floating-row:nth-child(1) {
            animation: scrollRight 65s linear infinite;
            padding-top: 5px; /* 调整第一行顶部边距 */
        }
        
        /* 第二行向左滚动，调整速度 */
        .floating-row:nth-child(2) {
            animation: scrollLeft 70s linear infinite;
            margin-top: 5px; /* 调整第二行上边距，使其与下方文字对齐 */
            margin-bottom: 28px; /* 增加底部边距 */
        }
        
        /* 第三行向右滚动，调整速度 */
        .floating-row:nth-child(3) {
            animation: scrollRight 75s linear infinite;
            margin-bottom: 0;
        }
        
        /* 优化滚动动画，更加平滑 */
        @keyframes scrollRight {
            0% {
                transform: translateX(0);
            }
            100% {
                transform: translateX(calc(-50%)); /* 移动一半的距离，确保无缝 */
            }
        }
        
        @keyframes scrollLeft {
            0% {
                transform: translateX(calc(-50%));
            }
            100% {
                transform: translateX(0);
            }
        }
        
        .floating-card {
            flex-shrink: 0;
            width: 210px; /* 统一卡片尺寸 */
            height: 100%;
            border-radius: 16px;
            overflow: hidden;
            position: relative;
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.12), 0 5px 10px rgba(0, 0, 0, 0.05);
            transition: all 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
            transform: translateZ(0) scale(0.98);
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid rgba(255, 255, 255, 0.15);
            cursor: pointer;
            will-change: transform, box-shadow;
            aspect-ratio: 4/3; /* 统一卡片比例 */
        }
        
        .floating-card img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 1;
            transition: all 0.7s cubic-bezier(0.19, 1, 0.22, 1);
            filter: brightness(0.95) saturate(1.25) contrast(1.05);
            will-change: transform, filter;
        }
        
        /* 优化卡片覆盖层 */
        .card-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, 
                rgba(30, 65, 180, 0.1) 0%, 
                rgba(30, 65, 180, 0.85) 100%);
            z-index: 3;
            opacity: 0;
            transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
            backdrop-filter: blur(0px);
            will-change: opacity, backdrop-filter;
        }
        
        .card-icon {
            display: none; /* 隐藏图标 */
        }
        
        .card-content {
            text-align: center;
            transform: translateY(15px);
            transition: all 0.5s ease;
            color: white;
            opacity: 0;
        }
        
        .card-content h4 {
            font-size: 22px; /* 增大标题字号 */
            font-weight: 600;
            margin-bottom: 12px;
            text-shadow: 0 2px 5px rgba(0, 0, 0, 0.25);
        }
        
        .card-content p {
            font-size: 16px; /* 增大描述文字字号 */
            opacity: 0.9;
            margin: 0;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.35);
        }
        
        /* 增强悬停效果 */
        .floating-card:hover {
            transform: translateY(-15px) scale(1.08);
            box-shadow: 0 20px 40px rgba(30, 65, 180, 0.25), 0 12px 10px rgba(30, 65, 180, 0.1);
            z-index: 10;
        }
        
        .floating-card:hover img {
            transform: scale(1.12);
            filter: brightness(0.65) saturate(1.3) contrast(1.1);
        }
        
        .floating-card:hover .card-overlay {
            opacity: 1;
            backdrop-filter: blur(3px);
        }
        
        .floating-card:hover .card-content {
            transform: translateY(0);
            opacity: 1;
        }
        
        /* 优化行悬停效果 */
        .floating-row {
            transition: animation-play-state 0.3s ease;
        }
        
        .floating-row:hover {
            animation-play-state: paused;
        }
        
        /* 添加移动指示器 */
        .floating-grid::before, 
        .floating-grid::after {
            transition: transform 0.3s ease;
        }
        
        .floating-grid:hover::before {
            transform: scale(1.5) translateX(10px);
        }
        
        .floating-grid:hover::after {
            transform: scale(1.5) translateX(-10px);
        }
        
        @media (max-width: 991px) {
            .floating-card {
                width: 180px;
            }
        }
        
        @media (max-width: 768px) {
            .floating-row {
                height: 150px;
            }
            
            .floating-card {
                width: 150px;
            }
            
            .card-icon {
                width: 40px;
                height: 40px;
                margin-bottom: 10px;
            }
            
            .card-icon i {
                font-size: 18px;
            }
            
            .card-content h4 {
                font-size: 16px;
                margin-bottom: 4px;
            }
            
            .card-content p {
                font-size: 12px;
            }
        }
        
        @media (max-width: 991px) {
            .footer-container {
                gap: 20px;
            }
        }
        
        @media (max-width: 768px) {
            .footer {
                padding-top: 40px;
            }
            .footer-column {
                min-width: 100%;
            }
            .footer-bottom {
                margin-top: 30px;
            }
            .footer-social {
                justify-content: center;
            }
            .footer-title {
                text-align: center;
            }
            .footer-title::after {
                left: 50%;
                transform: translateX(-50%);
            }
            .footer-links {
                align-items: center;
            }
            .footer-contact {
                justify-content: center;
            }
            .footer-contact-text {
                max-width: 70%;
            }
        }

        @media (max-width: 991px) {
            .footer-container {
                gap: 30px;
            }
            
            .footer-column {
                min-width: 45%;
            }
        }
        
        @media (max-width: 768px) {
            .footer {
                padding-top: 40px;
            }
            
            .footer-column {
                min-width: 100%;
                margin-bottom: 10px;
            }
            
            .footer-column:hover {
                transform: none;
            }
            
            .footer-bottom {
                margin-top: 30px;
            }
            
            .footer-social {
                justify-content: center;
            }
            
            .footer-title {
                text-align: center;
                margin-top: 15px;
                margin-bottom: 15px;
            }
            
            .footer-title::after {
                left: 50%;
                transform: translateX(-50%);
            }
            
            .footer-links {
                align-items: center;
            }
            
            .footer-links a:hover {
                transform: none;
            }
            
            .footer-contact {
                justify-content: center;
                margin-bottom: 10px;
            }
            
            .footer-contact:hover {
                transform: none;
            }
            
            .footer-contact-text {
                max-width: 70%;
            }
            
            .copyright-content {
                flex-direction: column;
                gap: 15px;
            }
            
            .beian-info {
                justify-content: center;
                gap: 12px;
            }
            
            .copyright-text:hover i {
                transform: none;
            }
            
            .beian-link:hover i {
                transform: none;
            }
        }
        
        @media (max-width: 480px) {
            .footer-container {
                padding: 0 15px;
            }
            
            .footer-contact-text {
                max-width: 85%;
                font-size: 13px;
            }
            
            .copyright-text, .beian-link {
                font-size: 12px;
            }
        }

        /* 优化左侧内容区域布局 */
        .hero-content-wrapper {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            margin-right: 30px;
        }

        /* 优化标签导航位置 */
        .tab-container {
            display: flex;
            background: rgba(255, 255, 255, 0.7);
            border-radius: var(--border-radius-lg);
            padding: 10px 15px;
            position: relative;
            margin-bottom: 25px;
            box-shadow: 0 4px 12px rgba(65, 105, 225, 0.08);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(65, 105, 225, 0.08);
            align-self: center;
            z-index: 10;
            transition: all 0.4s ease;
            height: 60px; /* 固定高度 */
            width: auto; /* 宽度适应内容 */
            justify-content: center; /* 居中对齐 */
            max-width: 480px; /* 最大宽度 */
            margin-left: auto; /* 水平居中 */
            margin-right: auto;
        }

        .tab-container:hover {
            box-shadow: 0 8px 25px rgba(65, 105, 225, 0.15);
            transform: translateY(-2px);
        }

        .tab-item {
            padding: 12px 15px;
            cursor: pointer;
            position: relative;
            z-index: 2;
            border-radius: var(--border-radius);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: 8px;
            overflow: hidden;
            flex: 0 1 auto; /* 不强制平均分配 */
            justify-content: center; /* 内容居中 */
            min-width: auto; /* 移除最小宽度 */
            text-align: center; /* 文本居中 */
            height: 40px; /* 固定高度 */
            margin: 0 8px; /* 增加间距 */
            white-space: nowrap; /* 防止文字换行 */
        }
        
        /* 修复标签下划线样式 */
        .tab-underline {
            position: absolute;
            bottom: 0;
            height: 3px;
            background: var(--primary);
            border-radius: 3px;
            transition: left 0.3s ease, width 0.3s ease;
            z-index: 1;
            box-shadow: 0 0 6px rgba(65, 105, 225, 0.4);
        }

        .tab-item::before {
            content: '';
            position: absolute;
            top: 100%;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(65, 105, 225, 0.1) 0%, rgba(65, 105, 225, 0.05) 100%);
            opacity: 0;
            transition: transform 0.4s ease, opacity 0.3s ease;
            z-index: -1;
            transform: translateY(0);
            border-radius: var(--border-radius);
        }

        .tab-item::after {
            content: '';
            position: absolute;
            width: 30px;
            height: 120%;
            background: linear-gradient(90deg, 
                rgba(255, 255, 255, 0) 0%, 
                rgba(255, 255, 255, 0.5) 50%, 
                rgba(255, 255, 255, 0) 100%);
            top: -10%;
            left: -100px;
            transform: skewX(-15deg);
            transition: all 0.6s ease;
            opacity: 0;
        }

        .tab-item i {
            font-size: 18px;
            transition: all 0.4s ease;
            position: relative;
            transform-origin: center;
            margin-right: 4px; /* 图标和文字间距 */
        }

        .tab-item span {
            position: relative;
            transition: all 0.3s ease;
            font-weight: 500; /* 稍微加粗 */
        }

        .tab-item.active {
            color: var(--primary);
            background: rgba(255, 255, 255, 0.85);
            transform: translateY(-2px); /* 减小上移距离 */
            box-shadow: 0 3px 10px rgba(65, 105, 225, 0.08); /* 减小阴影 */
            font-weight: bold; /* 激活状态字体加粗 */
        }

        .tab-item.active i {
            transform: scale(1.2);
            color: var(--primary);
        }

        .tab-item.active::before {
            opacity: 1;
            transform: translateY(-100%);
        }

        .tab-item:hover:not(.active) {
            color: var(--primary-light);
            background: rgba(255, 255, 255, 0.7);
            transform: translateY(-2px) scale(1.05);
        }

        .tab-item:hover:not(.active) i {
            transform: rotate(10deg) scale(1.1);
        }

        .tab-item:hover::after {
            left: 150%;
            opacity: 1;
        }

        /* 特定颜色标签 */
        .tab-item[data-tab="compliance"] i {
            color: #4169e1; /* 蓝色 - 合规 */
        }

        .tab-item[data-tab="marketing"] i {
            color: #ff6b7a; /* 红色 - 营销 */
        }

        .tab-item[data-tab="payment"] i {
            color: #2ecc71; /* 绿色 - 支付 */
        }

        .tab-item[data-tab="distribution"] i {
            color: #f1c40f; /* 黄色 - 分销 */
        }

        /* 添加标签下划线样式并固定位置 */
        .tab-underline {
            position: absolute;
            bottom: 10px;
            height: 3px;
            background: var(--primary);
            border-radius: 3px;
            transition: left 0.3s ease, width 0.3s ease;
            z-index: 1;
            box-shadow: 0 0 8px rgba(65, 105, 225, 0.5);
            /* 确保宽度基于内容计算 */
            width: 80px; /* 默认宽度，将由JS动态调整 */
        }

        /* 调整文本区域样式 */
        .hero-content {
            max-width: 100%;
        }

        /* 响应式调整 */
        @media (max-width: 991px) {
            .hero-container {
                flex-direction: column;
            }
            
            .hero-content-wrapper {
                margin-right: 0;
                margin-bottom: 40px;
                width: 100%;
            }
            
            .tab-container {
                width: 100%;
                overflow-x: auto;
                justify-content: flex-start;
            }
            
            .carousel-container {
                width: 100%;
            }
        }

        /* 标签点击动画 */
        @keyframes tab-pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(65, 105, 225, 0.3);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(65, 105, 225, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(65, 105, 225, 0);
            }
        }

        @keyframes tab-glow {
            0% {
                opacity: 0.7;
                text-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
            }
            50% {
                opacity: 1;
                text-shadow: 0 0 15px var(--primary), 0 0 20px rgba(255, 255, 255, 0.7);
            }
            100% {
                opacity: 0.7;
                text-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
            }
        }

        .tab-item.just-clicked {
            animation: tab-pulse 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }

        .tab-item.just-clicked i {
            animation: tab-glow 0.8s ease;
        }

        .tab-item.just-clicked span {
            animation: tab-glow 1s ease;
        }

        /* 特定颜色的点击动画 */
        .tab-item[data-tab="compliance"].just-clicked {
            animation: tab-pulse-blue 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }

        .tab-item[data-tab="marketing"].just-clicked {
            animation: tab-pulse-red 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }

        .tab-item[data-tab="payment"].just-clicked {
            animation: tab-pulse-green 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }

        .tab-item[data-tab="distribution"].just-clicked {
            animation: tab-pulse-yellow 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }

        @keyframes tab-pulse-blue {
            0% { box-shadow: 0 0 0 0 rgba(65, 105, 225, 0.5); }
            70% { box-shadow: 0 0 0 10px rgba(65, 105, 225, 0); }
            100% { box-shadow: 0 0 0 0 rgba(65, 105, 225, 0); }
        }

        @keyframes tab-pulse-red {
            0% { box-shadow: 0 0 0 0 rgba(255, 107, 122, 0.5); }
            70% { box-shadow: 0 0 0 10px rgba(255, 107, 122, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 107, 122, 0); }
        }

        @keyframes tab-pulse-green {
            0% { box-shadow: 0 0 0 0 rgba(46, 204, 113, 0.5); }
            70% { box-shadow: 0 0 0 10px rgba(46, 204, 113, 0); }
            100% { box-shadow: 0 0 0 0 rgba(46, 204, 113, 0); }
        }

        @keyframes tab-pulse-yellow {
            0% { box-shadow: 0 0 0 0 rgba(241, 196, 15, 0.5); }
            70% { box-shadow: 0 0 0 10px rgba(241, 196, 15, 0); }
            100% { box-shadow: 0 0 0 0 rgba(241, 196, 15, 0); }
        }

        /* 标签背景变化效果 */
        .tab-panel {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: absolute;
            visibility: hidden;
        }

        .tab-panel.active {
            opacity: 1;
            transform: translateY(0);
            position: relative;
            visibility: visible;
        }

        /* 优化卡片内容布局和效果 */
        .card-icon {
            width: 55px;
            height: 55px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.95);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
            transform: translateY(-15px) scale(0.8);
            transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
            position: relative;
            overflow: hidden;
        }
        
        .card-icon::after {
            content: '';
            position: absolute;
            width: 140%;
            height: 140%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0) 70%);
            top: -20%;
            left: -20%;
            opacity: 0;
            transition: opacity 0.5s ease;
            transform: scale(0.5);
        }
        
        .card-icon i {
            font-size: 24px;
            color: var(--primary);
            transition: all 0.3s ease;
            position: relative;
            z-index: 2;
        }
        
        .card-content {
            text-align: center;
            transform: translateY(15px);
            transition: all 0.5s ease;
            color: white;
            opacity: 0;
            width: 100%;
        }
        
        .card-content h4 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 6px;
            text-shadow: 0 2px 5px rgba(0, 0, 0, 0.25);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .card-content p {
            font-size: 13px;
            opacity: 0.9;
            margin: 0;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.35);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        /* 统一悬停效果 */
        .floating-card:hover {
            transform: translateY(-12px) scale(1.05);
            box-shadow: 0 18px 40px rgba(30, 65, 180, 0.25), 0 12px 10px rgba(30, 65, 180, 0.1);
            z-index: 10;
        }
        
        .floating-card:hover img {
            transform: scale(1.1);
            filter: brightness(0.7) saturate(1.2) contrast(1.08);
        }
        
        .floating-card:hover .card-overlay {
            opacity: 1;
            backdrop-filter: blur(2px);
        }
        
        .floating-card:hover .card-icon {
            transform: translateY(0) scale(1);
        }
        
        .floating-card:hover .card-icon::after {
            opacity: 0.8;
            transform: scale(1);
        }
        
        .floating-card:hover .card-icon i {
            transform: scale(1.1);
            animation: slight-bounce 1s ease-in-out;
        }
        
        .floating-card:hover .card-content {
            transform: translateY(0);
            opacity: 1;
        }
        
        /* 添加轻微弹跳动画 */
        @keyframes slight-bounce {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.2); }
        }
        
        /* 为不同的卡片添加特定颜色 */
        .floating-card:nth-child(4n+1) .card-icon i {
            color: #4169e1; /* 蓝色 */
        }
        
        .floating-card:nth-child(4n+2) .card-icon i {
            color: #ff6b7a; /* 红色 */
        }
        
        .floating-card:nth-child(4n+3) .card-icon i {
            color: #2ecc71; /* 绿色 */
        }
        
        .floating-card:nth-child(4n+4) .card-icon i {
            color: #f1c40f; /* 黄色 */
        }
        
        /* 预加载图片 */
        .carousel-container {
            position: relative;
        }
        
        .carousel-container::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(65, 105, 225, 0.03) 0%, rgba(65, 105, 225, 0) 100%);
            border-radius: 16px;
            pointer-events: none;
            z-index: 2;
        }
        
        /* 添加更多现代动画效果 */
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        @keyframes float3D {
            0% { transform: translateY(0) translateZ(0) rotateX(0) rotateY(0); }
            25% { transform: translateY(-5px) translateZ(5px) rotateX(2deg) rotateY(2deg); }
            50% { transform: translateY(0) translateZ(10px) rotateX(0) rotateY(5deg); }
            75% { transform: translateY(5px) translateZ(5px) rotateX(-2deg) rotateY(2deg); }
            100% { transform: translateY(0) translateZ(0) rotateX(0) rotateY(0); }
        }
        
        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }
        
        /* 增强悬停效果 */
        .feature-card, .announcement-item, .feature-3d-card {
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }
        
        .feature-card:hover, .announcement-item:hover, .feature-3d-card:hover {
            transform: translateY(-10px) scale(1.03);
            box-shadow: var(--box-shadow-hover);
        }
        
        /* 添加视差滚动效果 */
        .parallax-bg {
            background-attachment: fixed;
            background-position: center;
            background-repeat: no-repeat;
            background-size: cover;
        }
        
        /* 玻璃态效果 */
        .glass-effect {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        /* 渐变边框效果 */
        .gradient-border {
            position: relative;
            background: white;
            z-index: 1;
        }
        
        .gradient-border::before {
            content: '';
            position: absolute;
            top: -2px; right: -2px; bottom: -2px; left: -2px;
            background: var(--gradient-primary);
            z-index: -1;
            border-radius: inherit;
            animation: gradientShift 3s ease infinite;
            background-size: 200% 200%;
        }
        
        /* 更平滑的滚动 */
        html {
            scroll-behavior: smooth;
        }
        
        /* 添加动画延迟类 */
        .delay-900 {
            animation-delay: 0.9s;
        }
        
        .delay-1000 {
            animation-delay: 1s;
        }
        
        /* 3D悬停效果 */
        .hover-3d {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            transform-style: preserve-3d;
            perspective: 1000px;
        }
        
        .hover-3d:hover {
            transform: translateY(-5px) translateZ(10px) rotateX(2deg) rotateY(2deg);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }
        
        /* 卡片样式改进 */
        .card-common {
            border-radius: var(--border-radius);
            background-color: white;
            box-shadow: var(--shadow-sm);
            transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
            overflow: hidden;
            position: relative;
        }
        
        .card-common:hover {
            transform: translateY(-8px);
            box-shadow: var(--box-shadow-hover);
        }
        
        .card-common::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.2));
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }
        
        .card-common:hover::after {
            opacity: 1;
        }
        
        /* 应用到已有卡片类 */
        .feature-card, .announcement-item, .feature-3d-card {
            border-radius: var(--border-radius);
            background-color: white;
            box-shadow: var(--shadow-sm);
            overflow: hidden;
        }
        
        /* 增强卡片动画效果 */
        .feature-card:hover .feature-icon-wrapper,
        .announcement-item:hover .announcement-icon,
        .feature-3d-card:hover .feature-3d-image {
            transform: scale(1.15) rotate(5deg);
            background-color: rgba(65, 105, 225, 0.15);
        }
        
        /* 添加卡片闪光效果 */
        .feature-card::before,
        .announcement-item::before,
        .feature-3d-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -75%;
            width: 50%;
            height: 100%;
            background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.4), transparent);
            transform: skewX(-25deg);
            transition: all 0.75s;
        }
        
        .feature-card:hover::before,
        .announcement-item:hover::before,
        .feature-3d-card:hover::before {
            animation: shimmer 0.75s forwards;
        }
        
        /* 响应式设计优化 */
        @media (max-width: 992px) {
            .section {
                padding: 60px 0;
            }
            
            .section-title {
                font-size: 30px;
            }
            
            .hero-title {
                font-size: 34px;
            }
            
            .hero-container {
                flex-direction: column;
                gap: 30px;
                text-align: center;
            }
            
            .hero-content {
                order: 2;
            }
            
            .carousel-container {
                order: 1;
                height: 360px;
            }
            
            .hero-title, .hero-subtitle {
                text-align: center;
            }
            
            .features-grid, .feature-3d-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 20px;
            }
            
            .tab-container {
                overflow-x: auto;
                justify-content: flex-start;
                padding: 10px;
            }
            
            .announcement-container {
                flex-wrap: wrap;
            }
            
            .announcement-item {
                flex: 0 0 calc(50% - 20px);
                margin-bottom: 20px;
            }
        }
        
        @media (max-width: 768px) {
            .section-title {
                font-size: 28px;
            }
            
            .carousel-container {
                height: 320px;
            }
            
            .features-grid, .feature-3d-grid {
                grid-template-columns: 1fr;
            }
            
            .announcement-item {
                flex: 0 0 100%;
            }
            
            .btn {
                padding: 8px 18px;
                font-size: 13px;
            }
        }
        
        /* 改进页面过渡效果 */
        .page-transition {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.3s ease;
        }
        
        .page-transition.active {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* 平滑滚动增强 */
        @media (prefers-reduced-motion: no-preference) {
            html {
                scroll-behavior: smooth;
                scroll-padding-top: 70px; /* 为固定导航留出空间 */
            }
        }
        
        /* 加载动画优化 */
        .loading-effect {
            position: relative;
            overflow: hidden;
        }
        
        .loading-effect::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transform: translateX(-100%);
            animation: loading 1.5s infinite;
        }
        
        @keyframes loading {
            100% {
                transform: translateX(100%);
            }
        }

        /* 添加移动设备的响应式样式 */
        @media (max-width: 768px) {
            .tab-container {
                max-width: 360px;
                padding: 8px 10px;
                height: 55px;
            }
            
            .tab-item {
                padding: 10px 12px;
                margin: 0 5px;
                font-size: 14px;
            }
            
            .tab-item i {
                font-size: 16px;
            }
        }

        @keyframes spinner {
            to { transform: rotate(360deg); }
        }
        
        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.1); opacity: 0.8; }
            100% { transform: scale(1); opacity: 1; }
        }

        #page-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.98);
            z-index: 9999;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: all 0.5s ease;
        }

        .loader-content {
            text-align: center;
            animation: pulse 2s infinite ease-in-out;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 3px solid transparent;
            border-radius: 50%;
            border-top: 3px solid var(--primary);
            border-right: 3px solid var(--primary);
            border-bottom: 3px solid rgba(65, 105, 225, 0.2);
            border-left: 3px solid rgba(65, 105, 225, 0.2);
            animation: spinner 1s linear infinite;
            box-shadow: 0 0 20px rgba(65, 105, 225, 0.1);
        }

        .loader-text {
            margin-top: 20px;
            color: var(--primary);
            font-weight: 500;
            font-size: 16px;
            letter-spacing: 1px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        body.loaded #page-loader {
            opacity: 0;
            visibility: hidden;
        }
        
        body > *:not(#page-loader) {
            opacity: 0;
            transition: opacity 0.5s ease;
        }
        
        body.loaded > *:not(#page-loader) {
            opacity: 1;
        }

        /* 新增高级动画效果 */
        @keyframes floatUpDown {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-15px); }
        }

        @keyframes scaleAndFade {
            0% {
                opacity: 0;
                transform: scale(0.9);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes slideAndRotate {
            0% {
                opacity: 0;
                transform: translateX(-30px) rotate(-10deg);
            }
            100% {
                opacity: 1;
                transform: translateX(0) rotate(0);
            }
        }

        /* 优化现有动画 */
        .animate {
            opacity: 0;
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            will-change: transform, opacity;
        }

        .animate.active {
            opacity: 1;
        }

        .fade-in {
            animation: scaleAndFade 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        }

        .slide-left {
            animation: slideAndRotate 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        }

        .slide-right {
            animation: slideAndRotate 0.8s cubic-bezier(0.4, 0, 0.2, 1) reverse forwards;
        }

        .zoom-in {
            animation: scaleAndFade 0.8s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
        }

        /* 新增动画类 */
        .float-animation {
            animation: floatUpDown 3s ease-in-out infinite;
        }

        /* 优化延迟时间 */
        .delay-100 { animation-delay: 0.1s; }
        .delay-200 { animation-delay: 0.2s; }
        .delay-300 { animation-delay: 0.3s; }
        .delay-400 { animation-delay: 0.4s; }
        .delay-500 { animation-delay: 0.5s; }

        /* 添加动画持续时间类 */
        .duration-500 { animation-duration: 0.5s; }
        .duration-800 { animation-duration: 0.8s; }
        .duration-1000 { animation-duration: 1s; }

        /* 添加交错动画效果 */
        .stagger-item:nth-child(1) { animation-delay: 0.1s; }
        .stagger-item:nth-child(2) { animation-delay: 0.2s; }
        .stagger-item:nth-child(3) { animation-delay: 0.3s; }
        .stagger-item:nth-child(4) { animation-delay: 0.4s; }
        .stagger-item:nth-child(5) { animation-delay: 0.5s; }

        /* 优化滚动触发动画 */
        .scroll-trigger {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .scroll-trigger.active {
            opacity: 1;
            transform: translateY(0);
        }

        /* 添加悬停动画效果 */
        .hover-float:hover {
            animation: floatUpDown 1.5s ease-in-out infinite;
        }

        .hover-scale:hover {
            transform: scale(1.05);
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .hover-rotate:hover {
            transform: rotate(5deg);
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* 需求分析面板样式 */
        .analysis-panels {
            margin-top: 30px;
            position: relative;
            min-height: 200px;
        }
        
        .analysis-panel {
            display: none;
            opacity: 0;
            transform: translateY(10px);
            transition: opacity 0.3s ease, transform 0.3s ease;
            position: absolute;
            width: 100%;
            left: 0;
        }
        
        .analysis-panel.active {
            display: block;
            opacity: 1;
            transform: translateY(0);
            position: relative;
        }
        
        .analysis-panel-content {
            background-color: white;
            border-radius: var(--border-radius);
            padding: 25px;
            box-shadow: var(--shadow);
            text-align: left;
        }
        
        .panel-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            color: var(--primary);
        }
        
        .panel-desc {
            font-size: 15px;
            line-height: 1.6;
            margin-bottom: 15px;
            color: var(--text-secondary);
        }
        
        .panel-features {
            margin: 0;
            padding-left: 20px;
        }
        
        .panel-features li {
            margin-bottom: 8px;
            position: relative;
            color: var(--text-primary);
        }
        
        .panel-features li::before {
            content: "•";
            color: var(--primary);
            font-weight: bold;
            display: inline-block;
            width: 1em;
            margin-left: -1em;
        }
        
        .analysis-image {
            flex: 1;
            display: flex;
            justify-content: center;
        }
    </style>

    <div id="page-loader">
        <div class="loader-content">
            <div class="loading-spinner"></div>
            <p class="loader-text">{$siteName}</p>
        </div>
    </div>
</head>
<body>
    <!-- 添加页面加载效果 -->
    <div id="page-loader" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: white; z-index: 9999; display: flex; justify-content: center; align-items: center; transition: opacity 0.5s ease, visibility 0.5s ease;">
        <div style="text-align: center;">
            <div class="loading-spinner" style="width: 50px; height: 50px; border: 3px solid rgba(65, 105, 225, 0.1); border-top-color: var(--primary); border-radius: 50%; animation: spinner 1s linear infinite;"></div>
            <p style="margin-top: 15px; color: var(--primary); font-weight: 500;">{$siteName}</p>
        </div>
    </div>
    
    <style>
        @keyframes spinner {
            to { transform: rotate(360deg); }
        }
        
        /* 页面内容初始隐藏，加载后显示 */
        body > *:not(#page-loader) {
            opacity: 0;
            transition: opacity 0.5s ease;
        }
        
        body.loaded > *:not(#page-loader) {
            opacity: 1;
        }
        
        body.loaded #page-loader {
            opacity: 0;
            visibility: hidden;
        }
    </style>
    
    <script>
        // 页面加载完成后隐藏加载动画
        window.addEventListener('load', function() {
            setTimeout(function() {
                document.body.classList.add('loaded');
            }, 300);
        });
    </script>
    
    <!-- 头部导航 -->
    <header>
        <div class="container header-container">
            <div class="logo">
                <img src="{$logo}" alt="{$siteName}Logo">
            </div>
            <div class="nav-main">
                <ul class="nav-links">
                    {foreach $navItems as $nav}
                    <li class="{if $nav.href == '/' || $nav.href == '' || $nav.href == '#'}active{/if}">
                        <a href="{$nav.href}" {if $nav.target=='_blank'}target="_blank"{/if}>
                            {$nav.name}
                            {if !empty($nav.children)}
                            <span class="dropdown-arrow">▼</span>
                            {/if}
                        </a>
                        {if !empty($nav.children)}
                        <ul class="submenu">
                            {foreach $nav.children as $child}
                            <li><a href="{$child.href}" {if $child.target=='_blank'}target="_blank"{/if}>{$child.name}</a></li>
                            {/foreach}
                        </ul>
                        {/if}
                    </li>
                    {/foreach}
                </ul>
                <div class="auth-buttons">
                    <a href="/merchant/login" class="btn btn-outline"><i class="fas fa-user"></i> 商家登录</a>
                    <a href="/merchant/register" class="btn btn-primary"><i class="fas fa-user-plus"></i>&nbsp;&nbsp;商家注册</a>
                </div>
            </div>
        </div>
    </header>

    <!-- 自动滚动标签导航区域 -->
    <section class="hero-section">
        <!-- 背景装饰元素 -->
        <div class="bg-decoration bg-decoration-1"></div>
        <div class="bg-decoration bg-decoration-2"></div>
        
        <div class="container">
            <div class="hero-container">
                <!-- 左侧内容区域 -->
                <div class="hero-content-wrapper animate slide-left">
                    <!-- 标签导航系统 -->
                    <div class="tab-container animate fade-in">
                        <div class="tab-item active" data-tab="compliance">
                            <i class="fas fa-shield-alt"></i>
                            <span>合规</span>
                        </div>
                        <div class="tab-item" data-tab="marketing">
                            <i class="fas fa-bullhorn"></i>
                            <span>营销</span>
                        </div>
                        <div class="tab-item" data-tab="payment">
                            <i class="fas fa-credit-card"></i>
                            <span>支付</span>
                        </div>
                        <div class="tab-item" data-tab="distribution">
                            <i class="fas fa-network-wired"></i>
                            <span>分销</span>
                        </div>
                        <div class="tab-underline"></div>
                    </div>
                    
                    <!-- 文字内容部分 -->
                    <div class="hero-content">
                        <div class="tab-content">
                            <div class="tab-panel active" data-tab="compliance">
                                <span class="tag">安全可靠</span>
                                <h1 class="hero-title"><span class="logo-text gradient-text">{$siteName}</span><br>资金安全有保障</h1>
                                <p class="hero-subtitle">证照齐全、合规经营为商家提供安全、便捷、稳定的虚拟商品自动交易服务</p>
                                <button class="btn btn-primary pulse-animation" style="margin-top: 20px;">
                                    <span>立即入驻</span>
                                    <i class="fas fa-arrow-right"></i>
                                </button>
                            </div>
                            <div class="tab-panel" data-tab="marketing">
                                <span class="tag">高效推广</span>
                                <h1 class="hero-title">智能营销系统<br><span class="gradient-text">助力业务增长</span></h1>
                                <p class="hero-subtitle">多样化营销工具，精准触达用户，提升转化率</p>
                                <button class="btn btn-primary pulse-animation" style="margin-top: 20px;">
                                    <span>了解更多</span>
                                    <i class="fas fa-arrow-right"></i>
                                </button>
                            </div>
                            <div class="tab-panel" data-tab="payment">
                                <span class="tag">便捷支付</span>
                                <h1 class="hero-title">多种支付方式<br><span class="gradient-text">安全快捷支付</span></h1>
                                <p class="hero-subtitle">支持支付宝、微信、银联等多种支付方式，资金安全有保障</p>
                                <button class="btn btn-primary pulse-animation" style="margin-top: 20px;">
                                    <span>立即体验</span>
                                    <i class="fas fa-arrow-right"></i>
                                </button>
                            </div>
                            <div class="tab-panel" data-tab="distribution">
                                <span class="tag">拓展渠道</span>
                                <h1 class="hero-title">分销系统升级<br><span class="gradient-text">轻松开展业务</span></h1>
                                <p class="hero-subtitle">完善的分销体系，帮助商家快速建立销售网络</p>
                                <button class="btn btn-primary pulse-animation" style="margin-top: 20px;">
                                    <span>开始使用</span>
                                    <i class="fas fa-arrow-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 3D滚动图片展示区域放在右侧 -->
                <div class="carousel-container animate slide-right">
                    <div class="floating-grid">
                        <!-- 第一行图片 -->
                        <div class="floating-row">
                            <!-- 原始卡片 -->
                            <div class="floating-card">
                                <img src="/assets/plugin/Bluelunbo/plugin/Bluelunbo/img/home_sroll_img1.jpg" alt="合规系统">
                                <div class="card-overlay">
                                    <div class="card-icon">
                                        <i class="fas fa-shield-alt"></i>
                                    </div>
                                    <div class="card-content">
                                        <h4>安全合规</h4>
                                        <p>保障交易安全</p>
                                    </div>
                                </div>
                            </div>
                            <div class="floating-card">
                                <img src="/assets/plugin/Bluelunbo/plugin/Bluelunbo/img/home_sroll_img2.jpg" alt="营销系统">
                                <div class="card-overlay">
                                    <div class="card-icon">
                                        <i class="fas fa-chart-line"></i>
                                    </div>
                                    <div class="card-content">
                                        <h4>数据分析</h4>
                                        <p>实时监控业务</p>
                                    </div>
                                </div>
                            </div>
                            <div class="floating-card">
                                <img src="/assets/plugin/Bluelunbo/plugin/Bluelunbo/img/home_sroll_img3.jpg" alt="支付系统">
                                <div class="card-overlay">
                                    <div class="card-icon">
                                        <i class="fas fa-credit-card"></i>
                                    </div>
                                    <div class="card-content">
                                        <h4>支付系统</h4>
                                        <p>多种支付方式</p>
                                    </div>
                                </div>
                            </div>
                            <div class="floating-card">
                                <img src="/assets/plugin/Bluelunbo/plugin/Bluelunbo/img/home_sroll_img4.jpg" alt="用户管理">
                                <div class="card-overlay">
                                    <div class="card-icon">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <div class="card-content">
                                        <h4>用户管理</h4>
                                        <p>精细客户管理</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 重复卡片，确保无缝循环 -->
                            <div class="floating-card">
                                <img src="/assets/plugin/Bluelunbo/plugin/Bluelunbo/img/home_sroll_img1.jpg" alt="合规系统">
                                <div class="card-overlay">
                                    <div class="card-icon">
                                        <i class="fas fa-shield-alt"></i>
                                    </div>
                                    <div class="card-content">
                                        <h4>安全合规</h4>
                                        <p>保障交易安全</p>
                                    </div>
                                </div>
                            </div>
                            <div class="floating-card">
                                <img src="/assets/plugin/Bluelunbo/plugin/Bluelunbo/img/home_sroll_img2.jpg" alt="营销系统">
                                <div class="card-overlay">
                                    <div class="card-icon">
                                        <i class="fas fa-chart-line"></i>
                                    </div>
                                    <div class="card-content">
                                        <h4>数据分析</h4>
                                        <p>实时监控业务</p>
                                    </div>
                                </div>
                            </div>
                            <div class="floating-card">
                                <img src="/assets/plugin/Bluelunbo/plugin/Bluelunbo/img/home_sroll_img3.jpg" alt="支付系统">
                                <div class="card-overlay">
                                    <div class="card-icon">
                                        <i class="fas fa-credit-card"></i>
                                    </div>
                                    <div class="card-content">
                                        <h4>支付系统</h4>
                                        <p>多种支付方式</p>
                                    </div>
                                </div>
                            </div>
                            <div class="floating-card">
                                <img src="/assets/plugin/Bluelunbo/plugin/Bluelunbo/img/home_sroll_img4.jpg" alt="用户管理">
                                <div class="card-overlay">
                                    <div class="card-icon">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <div class="card-content">
                                        <h4>用户管理</h4>
                                        <p>精细客户管理</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 第二行图片 -->
                        <div class="floating-row">
                            <!-- 原始卡片 -->
                            <div class="floating-card">
                                <img src="/assets/plugin/Bluelunbo/plugin/Bluelunbo/img/home_sroll_img5.jpg" alt="营销系统">
                                <div class="card-overlay">
                                    <div class="card-icon">
                                        <i class="fas fa-bullhorn"></i>
                                    </div>
                                    <div class="card-content">
                                        <h4>营销系统</h4>
                                        <p>提升转化率</p>
                                    </div>
                                </div>
                            </div>
                            <div class="floating-card">
                                <img src="/assets/plugin/Bluelunbo/plugin/Bluelunbo/img/home_sroll_img6.jpg" alt="系统设置">
                                <div class="card-overlay">
                                    <div class="card-icon">
                                        <i class="fas fa-cogs"></i>
                                    </div>
                                    <div class="card-content">
                                        <h4>系统设置</h4>
                                        <p>灵活配置功能</p>
                                    </div>
                                </div>
                            </div>
                            <div class="floating-card">
                                <img src="/assets/plugin/Bluelunbo/plugin/Bluelunbo/img/home_sroll_img7.jpg" alt="数据分析">
                                <div class="card-overlay">
                                    <div class="card-icon">
                                        <i class="fas fa-database"></i>
                                    </div>
                                    <div class="card-content">
                                        <h4>数据中心</h4>
                                        <p>业务数据分析</p>
                                    </div>
                                </div>
                            </div>
                            <div class="floating-card">
                                <img src="/assets/plugin/Bluelunbo/plugin/Bluelunbo/img/home_sroll_img8.jpg" alt="分销系统">
                                <div class="card-overlay">
                                    <div class="card-icon">
                                        <i class="fas fa-network-wired"></i>
                                    </div>
                                    <div class="card-content">
                                        <h4>分销系统</h4>
                                        <p>拓展销售渠道</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 重复卡片，确保无缝循环 -->
                            <div class="floating-card">
                                <img src="/assets/plugin/Bluelunbo/plugin/Bluelunbo/img/home_sroll_img5.jpg" alt="营销系统">
                                <div class="card-overlay">
                                    <div class="card-icon">
                                        <i class="fas fa-bullhorn"></i>
                                    </div>
                                    <div class="card-content">
                                        <h4>营销系统</h4>
                                        <p>提升转化率</p>
                                    </div>
                                </div>
                            </div>
                            <div class="floating-card">
                                <img src="/assets/plugin/Bluelunbo/plugin/Bluelunbo/img/home_sroll_img6.jpg" alt="系统设置">
                                <div class="card-overlay">
                                    <div class="card-icon">
                                        <i class="fas fa-cogs"></i>
                                    </div>
                                    <div class="card-content">
                                        <h4>系统设置</h4>
                                        <p>灵活配置功能</p>
                                    </div>
                                </div>
                            </div>
                            <div class="floating-card">
                                <img src="/assets/plugin/Bluelunbo/plugin/Bluelunbo/img/home_sroll_img7.jpg" alt="数据分析">
                                <div class="card-overlay">
                                    <div class="card-icon">
                                        <i class="fas fa-database"></i>
                                    </div>
                                    <div class="card-content">
                                        <h4>数据中心</h4>
                                        <p>业务数据分析</p>
                                    </div>
                                </div>
                            </div>
                            <div class="floating-card">
                                <img src="/assets/plugin/Bluelunbo/plugin/Bluelunbo/img/home_sroll_img8.jpg" alt="分销系统">
                                <div class="card-overlay">
                                    <div class="card-icon">
                                        <i class="fas fa-network-wired"></i>
                                    </div>
                                    <div class="card-content">
                                        <h4>分销系统</h4>
                                        <p>拓展销售渠道</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 第三行图片 -->
                        <div class="floating-row">
                            <!-- 原始卡片 -->
                            <div class="floating-card">
                                <img src="/assets/plugin/Bluelunbo/plugin/Bluelunbo/img/home_sroll_img3.jpg" alt="支付系统">
                                <div class="card-overlay">
                                    <div class="card-icon">
                                        <i class="fas fa-shopping-cart"></i>
                                    </div>
                                    <div class="card-content">
                                        <h4>商品管理</h4>
                                        <p>灵活商品配置</p>
                                    </div>
                                </div>
                            </div>
                            <div class="floating-card">
                                <img src="/assets/plugin/Bluelunbo/plugin/Bluelunbo/img/home_sroll_img7.jpg" alt="数据分析">
                                <div class="card-overlay">
                                    <div class="card-icon">
                                        <i class="fas fa-chart-pie"></i>
                                    </div>
                                    <div class="card-content">
                                        <h4>统计报表</h4>
                                        <p>多维度数据统计</p>
                                    </div>
                                </div>
                            </div>
                            <div class="floating-card">
                                <img src="/assets/plugin/Bluelunbo/plugin/Bluelunbo/img/home_sroll_img5.jpg" alt="营销系统">
                                <div class="card-overlay">
                                    <div class="card-icon">
                                        <i class="fas fa-comments"></i>
                                    </div>
                                    <div class="card-content">
                                        <h4>在线客服</h4>
                                        <p>实时客户沟通</p>
                                    </div>
                                </div>
                            </div>
                            <div class="floating-card">
                                <img src="/assets/plugin/Bluelunbo/plugin/Bluelunbo/img/home_sroll_img2.jpg" alt="营销系统">
                                <div class="card-overlay">
                                    <div class="card-icon">
                                        <i class="fas fa-tasks"></i>
                                    </div>
                                    <div class="card-content">
                                        <h4>订单管理</h4>
                                        <p>高效处理订单</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 重复卡片，确保无缝循环 -->
                            <div class="floating-card">
                                <img src="/assets/plugin/Bluelunbo/plugin/Bluelunbo/img/home_sroll_img3.jpg" alt="支付系统">
                                <div class="card-overlay">
                                    <div class="card-icon">
                                        <i class="fas fa-shopping-cart"></i>
                                    </div>
                                    <div class="card-content">
                                        <h4>商品管理</h4>
                                        <p>灵活商品配置</p>
                                    </div>
                                </div>
                            </div>
                            <div class="floating-card">
                                <img src="/assets/plugin/Bluelunbo/plugin/Bluelunbo/img/home_sroll_img7.jpg" alt="数据分析">
                                <div class="card-overlay">
                                    <div class="card-icon">
                                        <i class="fas fa-chart-pie"></i>
                                    </div>
                                    <div class="card-content">
                                        <h4>统计报表</h4>
                                        <p>多维度数据统计</p>
                                    </div>
                                </div>
                            </div>
                            <div class="floating-card">
                                <img src="/assets/plugin/Bluelunbo/plugin/Bluelunbo/img/home_sroll_img5.jpg" alt="营销系统">
                                <div class="card-overlay">
                                    <div class="card-icon">
                                        <i class="fas fa-comments"></i>
                                    </div>
                                    <div class="card-content">
                                        <h4>在线客服</h4>
                                        <p>实时客户沟通</p>
                                    </div>
                                </div>
                            </div>
                            <div class="floating-card">
                                <img src="/assets/plugin/Bluelunbo/plugin/Bluelunbo/img/home_sroll_img2.jpg" alt="营销系统">
                                <div class="card-overlay">
                                    <div class="card-icon">
                                        <i class="fas fa-tasks"></i>
                                    </div>
                                    <div class="card-content">
                                        <h4>订单管理</h4>
                                        <p>高效处理订单</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 美化波浪分隔线 -->
        <div class="wave-divider">
            <svg data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
                <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z" class="shape-fill"></path>
            </svg>
        </div>
        
        <!-- 添加内联脚本修复轮播图位置问题 -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // 获取轮播图容器
                const carousel = document.querySelector('.carousel-container');
                if (!carousel) return;
                
                // 设置初始状态
                carousel.style.transform = 'translateZ(0)';
                carousel.style.willChange = 'transform';
                
                // 获取初始位置
                const initialRect = carousel.getBoundingClientRect();
                carousel.setAttribute('data-initial-top', initialRect.top.toString());
                
                // 监听标签点击
                const tabItems = document.querySelectorAll('.tab-item');
                tabItems.forEach(item => {
                    item.addEventListener('click', function() {
                        // 保存点击前的位置
                        const beforeClickRect = carousel.getBoundingClientRect();
                        
                        // 在下一帧检查位置变化
                        requestAnimationFrame(() => {
                            const afterClickRect = carousel.getBoundingClientRect();
                            
                            // 如果位置发生变化，应用修正
                            if (Math.abs(afterClickRect.top - beforeClickRect.top) > 2) {
                                // 立即修正位置
                                carousel.style.transform = `translateY(${beforeClickRect.top - afterClickRect.top}px)`;
                                
                                // 平滑过渡到原始位置
                                setTimeout(() => {
                                    carousel.style.transition = 'transform 0.3s ease';
                                    carousel.style.transform = 'translateZ(0)';
                                    setTimeout(() => {
                                        carousel.style.transition = '';
                                    }, 300);
                                }, 100);
                            }
                        });
                    });
                });
                
                // 确保浮动行动画流畅
                const floatingRows = document.querySelectorAll('.floating-row');
                floatingRows.forEach(row => {
                    row.style.willChange = 'transform';
                    row.style.transform = 'translateZ(0)';
                });
            });
        </script>
    </section>
    
    <!-- 公告区域 -->
    <section class="announcement-section">
        <div class="container">
            <div class="announcement-container">
                <div class="announcement-item animate zoom-in delay-100">
                    <div class="announcement-icon">
                        <i class="fas fa-bullhorn"></i>
                    </div>
                    <h3 class="announcement-title">平台公告</h3>
                    <p class="announcement-desc">了解最新平台动态和重要通知</p>
                </div>
                <div class="announcement-item animate zoom-in delay-200">
                    <div class="announcement-icon">
                        <i class="fas fa-newspaper"></i>
                    </div>
                    <h3 class="announcement-title">行业新闻</h3>
                    <p class="announcement-desc">掌握行业最新趋势和市场动态</p>
                </div>
                <div class="announcement-item animate zoom-in delay-300">
                    <div class="announcement-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <h3 class="announcement-title">商品问题</h3>
                    <p class="announcement-desc">解决商品相关疑问和常见问题</p>
                </div>
                <div class="announcement-item animate zoom-in delay-400">
                    <div class="announcement-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <h3 class="announcement-title">售后相关</h3>
                    <p class="announcement-desc">获取专业的售后支持和服务</p>
                </div>
                <div class="announcement-item animate zoom-in delay-500">
                    <div class="announcement-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="announcement-title">账户安全</h3>
                    <p class="announcement-desc">保障账户安全和资金安全</p>
                </div>
            </div>
        </div>
        <div class="wave-divider-inverted">
            <svg data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
                <path d="M985.66,92.83C906.67,72,823.78,31,743.84,14.19c-82.26-17.34-168.06-16.33-250.45.39-57.84,11.73-114,31.07-172,41.86A600.21,600.21,0,0,1,0,27.35V120H1200V95.8C1132.19,118.92,1055.71,111.31,985.66,92.83Z" class="shape-fill"></path>
            </svg>
        </div>
    </section>

    <!-- 3D特性卡片网格 -->
    <section class="feature-3d-section">
        <div class="bg-decoration bg-decoration-1"></div>
        <div class="bg-decoration bg-decoration-2"></div>
        <div class="container">
            <h2 class="section-title animate fade-in">我们的核心优势</h2>
            <div class="feature-3d-grid">
                <div class="feature-3d-card animate flip-in delay-100">
                    <div class="feature-3d-image">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <div class="feature-3d-content">
                        <h3 class="feature-3d-title">支付渠道</h3>
                        <p class="feature-3d-subtitle">多渠道，空中分账，满足各类支付需求</p>
                    </div>
                </div>
                <div class="feature-3d-card animate flip-in delay-200">
                    <div class="feature-3d-image">
                        <i class="fas fa-headset"></i>
                    </div>
                    <div class="feature-3d-content">
                        <h3 class="feature-3d-title">专业客服</h3>
                        <p class="feature-3d-subtitle">专业的客服团队，解答您的各类疑难问题</p>
                    </div>
                </div>
                <div class="feature-3d-card animate flip-in delay-300">
                    <div class="feature-3d-image">
                        <i class="fas fa-wallet"></i>
                    </div>
                    <div class="feature-3d-content">
                        <h3 class="feature-3d-title">自动提现无手续费</h3>
                        <p class="feature-3d-subtitle">工作日自动提现，提现秒到账，无需手续费</p>
                    </div>
                </div>
                <div class="feature-3d-card animate flip-in delay-400">
                    <div class="feature-3d-image">
                        <i class="fas fa-code"></i>
                    </div>
                    <div class="feature-3d-content">
                        <h3 class="feature-3d-title">专业开发团队打造</h3>
                        <p class="feature-3d-subtitle">大厂研发团队开发及运营，品质保障</p>
                    </div>
                </div>
                <div class="feature-3d-card animate flip-in delay-500">
                    <div class="feature-3d-image">
                        <i class="fas fa-sitemap"></i>
                    </div>
                    <div class="feature-3d-content">
                        <h3 class="feature-3d-title">下级代理收入裂变</h3>
                        <p class="feature-3d-subtitle">代理商帮你卖，解脱自身，收入倍增</p>
                    </div>
                </div>
                <div class="feature-3d-card animate flip-in delay-600">
                    <div class="feature-3d-image">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="feature-3d-content">
                        <h3 class="feature-3d-title">高稳定支付渠道</h3>
                        <p class="feature-3d-subtitle">高可用性支付系统，及时感知讯息</p>
                    </div>
                </div>
                <div class="feature-3d-card animate flip-in delay-700">
                    <div class="feature-3d-image">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="feature-3d-content">
                        <h3 class="feature-3d-title">法助增销量</h3>
                        <p class="feature-3d-subtitle">多种营销模式供您选择，助力销量提升</p>
                    </div>
                </div>
                <div class="feature-3d-card animate flip-in delay-800">
                    <div class="feature-3d-image">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="feature-3d-content">
                        <h3 class="feature-3d-title">灵活选择费率承担方</h3>
                        <p class="feature-3d-subtitle">商家或买家均可承担费率，灵活选择</p>
                    </div>
                </div>
                <div class="feature-3d-card animate flip-in delay-900">
                    <div class="feature-3d-image">
                        <i class="fas fa-server"></i>
                    </div>
                    <div class="feature-3d-content">
                        <h3 class="feature-3d-title">微服务分布式架构</h3>
                        <p class="feature-3d-subtitle">先进的微服务分布式架构设计，高效稳定</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 解决方案区域 -->
    <section class="section features-section">
        <div class="bg-decoration bg-decoration-1"></div>
        <div class="container" style="text-align: center;">
            <h2 class="section-title animate fade-in">营销、支付、服务全流程解决方案</h2>
            <p class="section-subtitle animate fade-in delay-200">大数据、人工智能等技术赋能，促进行业智能化，提升服务体验，升级客户忠诚度</p>
            
            <div class="features-grid">
                <div class="feature-card animate slide-up delay-100">
                    <div class="feature-icon-wrapper">
                        <i class="fas fa-tools"></i>
                    </div>
                    <h3 class="feature-title">定制开发</h3>
                    <p class="feature-desc">根据您的业务需求，提供专业的定制开发服务，打造专属解决方案</p>
                    <div class="feature-tag">个性化定制</div>
                </div>
                <div class="feature-card animate slide-up delay-200">
                    <div class="feature-icon-wrapper">
                        <i class="fas fa-money-check-alt"></i>
                    </div>
                    <h3 class="feature-title">支付优势</h3>
                    <p class="feature-desc">多种支付方式，安全快捷，满足各类交易需求，提高转化率</p>
                    <div class="feature-tag">安全可靠</div>
                </div>
                <div class="feature-card animate slide-up delay-300">
                    <div class="feature-icon-wrapper">
                        <i class="fas fa-shopping-bag"></i>
                    </div>
                    <h3 class="feature-title">便捷购买</h3>
                    <p class="feature-desc">简化购买流程，提升用户体验，增加转化率，促进销售增长</p>
                    <div class="feature-tag">用户体验</div>
                </div>
                <div class="feature-card animate slide-up delay-400">
                    <div class="feature-icon-wrapper">
                        <i class="fas fa-boxes"></i>
                    </div>
                    <h3 class="feature-title">货源代理</h3>
                    <p class="feature-desc">丰富的货源渠道，稳定可靠，满足各类需求，扩大业务范围</p>
                    <div class="feature-tag">资源丰富</div>
                </div>
                <div class="feature-card animate slide-up delay-500">
                    <div class="feature-icon-wrapper">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="feature-title">安全防护</h3>
                    <p class="feature-desc">多重安全保障，保护您的资金和数据安全，让您无忧经营</p>
                    <div class="feature-tag">全面保障</div>
                </div>
                <div class="feature-card animate slide-up delay-600">
                    <div class="feature-icon-wrapper">
                        <i class="fas fa-store"></i>
                    </div>
                    <h3 class="feature-title">电商经营</h3>
                    <p class="feature-desc">一站式电商解决方案，助力您的业务快速发展，实现业绩增长</p>
                    <div class="feature-tag">一站式服务</div>
                </div>
            </div>
        </div>
    </section>

    <!-- 需求分析区域 -->
    <section class="section analysis-section">
        <div class="bg-decoration bg-decoration-2"></div>
        <div class="container">
            <div class="analysis-container">
                <div class="analysis-content animate slide-left">
                    <div class="analysis-tags">
                        <div class="analysis-tag active" data-tab="basic">基本需求</div>
                        <div class="analysis-tag" data-tab="function">功能需求</div>
                        <div class="analysis-tag" data-tab="custom">个性化需求</div>
                    </div>
                    
                    <!-- 需求内容面板 -->
                    <div class="analysis-panels">
                        <!-- 基本需求面板 -->
                        <div class="analysis-panel active" data-tab="basic">
                            <div class="analysis-panel-content">
                                <h3 class="panel-title">基本需求解决方案</h3>
                                <p class="panel-desc">提供标准化的商城建设解决方案，包含基础的商品管理、订单处理、会员系统等功能，满足电商运营的基本需求。</p>
                                <ul class="panel-features">
                                    <li>商品管理与展示</li>
                                    <li>订单处理与物流对接</li>
                                    <li>会员注册与管理</li>
                                    <li>基础数据统计分析</li>
                                </ul>
                            </div>
                        </div>
                        
                        <!-- 功能需求面板 -->
                        <div class="analysis-panel" data-tab="function">
                            <div class="analysis-panel-content">
                                <h3 class="panel-title">功能需求解决方案</h3>
                                <p class="panel-desc">根据业务场景扩展功能模块，提供多样化的营销工具和支付方式，增强用户体验和运营效率。</p>
                                <ul class="panel-features">
                                    <li>多样化营销活动工具</li>
                                    <li>多渠道支付系统集成</li>
                                    <li>分销体系与佣金设置</li>
                                    <li>数据分析与业务决策支持</li>
                                </ul>
                            </div>
                        </div>
                        
                        <!-- 个性化需求面板 -->
                        <div class="analysis-panel" data-tab="custom">
                            <div class="analysis-panel-content">
                                <h3 class="panel-title">个性化需求解决方案</h3>
                                <p class="panel-desc">针对特定行业或企业的独特需求，提供定制化开发服务，打造专属商业解决方案。</p>
                                <ul class="panel-features">
                                    <li>企业专属UI设计</li>
                                    <li>特定行业功能定制</li>
                                    <li>系统集成与API对接</li>
                                    <li>个性化业务流程设计</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="analysis-image animate slide-right">
                    <img src="/assets/plugin/Bluelunbo/plugin/Bluelunbo/img/business-team-discussing-ideas-startup_74855-4380.jpg" alt="需求分析图示">
                </div>
            </div>
        </div>
    </section>

    <!-- 资金安全区域 -->
    <section class="section security-section">
        <div class="container">
            <h2 class="security-title animate fade-in">资金安全全面保障</h2>
            <p class="security-desc animate fade-in delay-200">提供网银、快捷、手机PAY、扫码等多种支付方式，为您提供安全、便捷、稳定的支付服务</p>
        </div>
    </section>

    <!-- 支付方式区域 -->
    <section class="section payment-section">
        <div class="wave-divider-inverted">
            <svg data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
                <path d="M985.66,92.83C906.67,72,823.78,31,743.84,14.19c-82.26-17.34-168.06-16.33-250.45.39-57.84,11.73-114,31.07-172,41.86A600.21,600.21,0,0,1,0,27.35V120H1200V95.8C1132.19,118.92,1055.71,111.31,985.66,92.83Z" class="shape-fill"></path>
            </svg>
        </div>
        <div class="container">
            <div class="payment-container">
                <div class="payment-content animate slide-left">
                    <h2 class="payment-title">多种支付方式</h2>
                    <p class="payment-desc">提供一站式支付解决方案，聚合多个支付渠道，支付宝、微信支付、银联等聚合多种支付，规避平台二清风险</p>
                    <div class="payment-methods">
                        <div class="payment-method animate zoom-in delay-100">微信支付</div>
                        <div class="payment-method animate zoom-in delay-200">支付宝支付</div>
                        <div class="payment-method animate zoom-in delay-300">银联支付</div>
                        <div class="payment-method animate zoom-in delay-400">连连支付</div>
                    </div>
                </div>
                <div class="payment-image animate slide-right">
                    <img src="/assets/plugin/Bluelunbo/plugin/Bluelunbo/img/sj.gif" alt="支付方式图示">
                </div>
            </div>
        </div>
        <div class="wave-divider">
            <svg data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
                <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z" class="shape-fill"></path>
            </svg>
        </div>
    </section>

    <!-- 账户隔离区域 -->
    <section class="section account-section">
        <div class="container">
            <div class="account-container">
                <div class="account-content animate slide-left">
                    <h2 class="account-title">账户隔离</h2>
                    <p class="account-desc">您可以将资金存入第三方监管账户，由第三方支付机构进行监管，可以开设独立的银行账户，用于收取交易资金，不直接于预收的账户资金</p>
                    <div class="account-features">
                        <div class="account-feature animate zoom-in delay-100">第三方支付机构资金存管</div>
                        <div class="account-feature animate zoom-in delay-200">银行账户隔离</div>
                    </div>
                </div>
                <div class="account-image animate slide-right">
                    <img src="/assets/plugin/Bluelunbo/plugin/Bluelunbo/img/safe.gif" alt="账户隔离图示">
                </div>
            </div>
        </div>
    </section>

    <!-- 行业适用区域 -->
    <section class="section industry-section">
        <div class="container">
            <h2 class="industry-title animate fade-in">智能化 / 轻松扩展下级代理 / 协助销量增长</h2>
            <div class="industry-tabs animate fade-in delay-200">
                <div class="industry-tab active" data-tab="platform">平台优势</div>
                <div class="industry-tab" data-tab="multi-entry">多端入口</div>
                <div class="industry-tab" data-tab="security">系统安全</div>
                <div class="industry-tab" data-tab="big-data">大数据</div>
            </div>
            
            <!-- 平台优势面板 -->
            <div class="features-grid-alt industry-panel active" data-tab="platform">
                <div class="feature-card-alt animate zoom-in delay-100">
                    <h3 class="feature-title-alt">消息队列</h3>
                    <p class="feature-desc-alt">高并发，低延迟，全场景通用<br>使用无卡顿</p>
                    <div class="feature-tag-alt">拓展性强</div>
                </div>
                <div class="feature-card-alt animate zoom-in delay-200">
                    <h3 class="feature-title-alt">独特营销</h3>
                    <p class="feature-desc-alt">高并发，低延迟，全场景通用<br>使用无卡顿</p>
                    <div class="feature-tag-alt">稳定性高</div>
                </div>
                <div class="feature-card-alt animate zoom-in delay-300">
                    <h3 class="feature-title-alt">安全保障</h3>
                    <p class="feature-desc-alt">高并发，低延迟，全场景通用<br>使用无卡顿</p>
                    <div class="feature-tag-alt">一站式服务</div>
                </div>
                <div class="feature-card-alt animate zoom-in delay-400">
                    <h3 class="feature-title-alt">超高并发</h3>
                    <p class="feature-desc-alt">高并发，低延迟，全场景通用<br>使用无卡顿</p>
                    <div class="feature-tag-alt">高效运作</div>
                </div>
                <div class="feature-card-alt animate zoom-in delay-500">
                    <h3 class="feature-title-alt">持续升级</h3>
                    <p class="feature-desc-alt">高并发，低延迟，全场景通用<br>使用无卡顿</p>
                    <div class="feature-tag-alt">安全可靠</div>
                </div>
                <div class="feature-card-alt animate zoom-in delay-600">
                    <h3 class="feature-title-alt">数据保护</h3>
                    <p class="feature-desc-alt">高并发，低延迟，全场景通用<br>使用无卡顿</p>
                    <div class="feature-tag-alt">全面防御</div>
                </div>
            </div>
            
            <!-- 多端入口面板 -->
            <div class="features-grid-alt industry-panel" data-tab="multi-entry">
                <div class="feature-card-alt animate zoom-in delay-100">
                    <h3 class="feature-title-alt">H5商城</h3>
                    <p class="feature-desc-alt">无需安装，随时随地访问<br>一键分享链接即可购买<br>覆盖所有移动设备用户</p>
                    <div class="feature-tag-alt">全平台兼容</div>
                </div>
                <div class="feature-card-alt animate zoom-in delay-200">
                    <h3 class="feature-title-alt">微信小程序</h3>
                    <p class="feature-desc-alt">轻便快捷，即用即走<br>完美融入微信生态系统<br>用户转化率高达85%</p>
                    <div class="feature-tag-alt">微信生态</div>
                </div>
                <div class="feature-card-alt animate zoom-in delay-300">
                    <h3 class="feature-title-alt">APP应用</h3>
                    <p class="feature-desc-alt">功能完整，体验流畅<br>支持离线使用和推送<br>打造专属品牌形象</p>
                    <div class="feature-tag-alt">品牌专属</div>
                </div>
                <div class="feature-card-alt animate zoom-in delay-400">
                    <h3 class="feature-title-alt">PC管理端</h3>
                    <p class="feature-desc-alt">功能强大，操作便捷<br>专业后台管理系统<br>数据统计一目了然</p>
                    <div class="feature-tag-alt">专业管理</div>
                </div>
                <div class="feature-card-alt animate zoom-in delay-500">
                    <h3 class="feature-title-alt">推广分享</h3>
                    <p class="feature-desc-alt">智能海报生成系统<br>一键分享至各社交平台<br>裂变营销效果显著</p>
                    <div class="feature-tag-alt">营销助力</div>
                </div>
                <div class="feature-card-alt animate zoom-in delay-600">
                    <h3 class="feature-title-alt">平台互通</h3>
                    <p class="feature-desc-alt">数据实时互通，账户同步<br>多端操作无缝切换<br>提供统一用户体验</p>
                    <div class="feature-tag-alt">无缝切换</div>
                </div>
            </div>
            
            <!-- 系统安全面板 -->
            <div class="features-grid-alt industry-panel" data-tab="security">
                <div class="feature-card-alt animate zoom-in delay-100">
                    <h3 class="feature-title-alt">HTTPS加密</h3>
                    <p class="feature-desc-alt">SSL安全传输协议<br>数据传输加密</p>
                    <div class="feature-tag-alt">通信安全</div>
                </div>
                <div class="feature-card-alt animate zoom-in delay-200">
                    <h3 class="feature-title-alt">防SQL注入</h3>
                    <p class="feature-desc-alt">参数验证过滤<br>防止恶意攻击</p>
                    <div class="feature-tag-alt">代码安全</div>
                </div>
                <div class="feature-card-alt animate zoom-in delay-300">
                    <h3 class="feature-title-alt">数据备份</h3>
                    <p class="feature-desc-alt">定时备份，多重存储<br>数据永不丢失</p>
                    <div class="feature-tag-alt">容灾恢复</div>
                </div>
                <div class="feature-card-alt animate zoom-in delay-400">
                    <h3 class="feature-title-alt">权限管理</h3>
                    <p class="feature-desc-alt">角色分级，权限精细<br>安全可控操作</p>
                    <div class="feature-tag-alt">访问控制</div>
                </div>
                <div class="feature-card-alt animate zoom-in delay-500">
                    <h3 class="feature-title-alt">日志审计</h3>
                    <p class="feature-desc-alt">操作记录，行为追踪<br>异常及时发现</p>
                    <div class="feature-tag-alt">追踪监控</div>
                </div>
                <div class="feature-card-alt animate zoom-in delay-600">
                    <h3 class="feature-title-alt">防火墙</h3>
                    <p class="feature-desc-alt">CC防护，DDoS防御<br>高效安全屏障</p>
                    <div class="feature-tag-alt">攻击防御</div>
                </div>
            </div>
            
            <!-- 大数据面板 -->
            <div class="features-grid-alt industry-panel" data-tab="big-data">
                <div class="feature-card-alt animate zoom-in delay-100">
                    <h3 class="feature-title-alt">用户画像</h3>
                    <p class="feature-desc-alt">精准分析用户特征<br>个性化推荐产品</p>
                    <div class="feature-tag-alt">精准营销</div>
                </div>
                <div class="feature-card-alt animate zoom-in delay-200">
                    <h3 class="feature-title-alt">销售趋势</h3>
                    <p class="feature-desc-alt">实时监控销售情况<br>预测未来走势</p>
                    <div class="feature-tag-alt">业绩预测</div>
                </div>
                <div class="feature-card-alt animate zoom-in delay-300">
                    <h3 class="feature-title-alt">热点分析</h3>
                    <p class="feature-desc-alt">热门商品识别<br>及时调整库存</p>
                    <div class="feature-tag-alt">库存优化</div>
                </div>
                <div class="feature-card-alt animate zoom-in delay-400">
                    <h3 class="feature-title-alt">效果分析</h3>
                    <p class="feature-desc-alt">活动效果评估<br>营销策略优化</p>
                    <div class="feature-tag-alt">投资回报</div>
                </div>
                <div class="feature-card-alt animate zoom-in delay-500">
                    <h3 class="feature-title-alt">区域分析</h3>
                    <p class="feature-desc-alt">地域销售分布<br>区域市场拓展</p>
                    <div class="feature-tag-alt">区域洞察</div>
                </div>
                <div class="feature-card-alt animate zoom-in delay-600">
                    <h3 class="feature-title-alt">竞品分析</h3>
                    <p class="feature-desc-alt">市场竞争监控<br>差异化战略制定</p>
                    <div class="feature-tag-alt">战略优势</div>
                </div>
            </div>
        </div>
    </section>

    <!-- 快速开店区域 -->
    <section class="section quick-start-section">
        <div class="wave-divider-inverted">
            <svg data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
                <path d="M985.66,92.83C906.67,72,823.78,31,743.84,14.19c-82.26-17.34-168.06-16.33-250.45.39-57.84,11.73-114,31.07-172,41.86A600.21,600.21,0,0,1,0,27.35V120H1200V95.8C1132.19,118.92,1055.71,111.31,985.66,92.83Z" class="shape-fill"></path>
            </svg>
        </div>
        <div class="bg-decoration bg-decoration-1"></div>
        <div class="bg-decoration bg-decoration-2"></div>
        <div class="container">
            <h2 class="quick-start-title animate fade-in">
                <i class="fas fa-rocket" style="color: var(--primary); margin-right: 15px;"></i>
                极速实现您的开店梦想
            </h2>
            
            <div class="quick-start-cards">
                <div class="quick-start-card animate slide-up delay-100">
                    <h3 class="quick-start-card-title">轻松入驻，程序智能化</h3>
                    <p class="quick-start-card-desc">一分钟极速入驻，迅速卖货，无需复杂操作</p>
                </div>
                <div class="quick-start-card animate slide-up delay-200">
                    <h3 class="quick-start-card-title">高效稳定，技术保障平台</h3>
                    <p class="quick-start-card-desc">专业技术保证，高效稳定的服务，确保业务连续性</p>
                </div>
                <div class="quick-start-card animate slide-up delay-300">
                    <h3 class="quick-start-card-title">持续更新，保持服务</h3>
                    <p class="quick-start-card-desc">不间断更新，持续为店铺赋能，紧跟市场变化</p>
                </div>
                <div class="quick-start-card animate slide-up delay-400">
                    <h3 class="quick-start-card-title">多场景适用，兼容性强</h3>
                    <p class="quick-start-card-desc">支持所有购买场景，让客户轻松购，提高转化率</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚区域 -->
    <footer class="footer">
        <div class="container footer-container">
            <div class="footer-column animate slide-up">
                <div class="footer-logo">
                    <img src="{$logo}" alt="{$siteName}Logo">
                    <div class="footer-logo-text">{$siteName}</div>
                </div>
                <p class="footer-description">
                    {$footer_description}
                </p>
                <div class="footer-social">
                    {if !empty($footer_contact_phone)}
                    <a href="tel:{$footer_contact_phone}" class="footer-social-icon" title="电话联系"><i class="fas fa-mobile-alt"></i></a>
                    {/if}
                    {if !empty($contact_qq)}
                    <a href="http://wpa.qq.com/msgrd?v=3&uin={$contact_qq}&site=qq&menu=yes" class="footer-social-icon" title="QQ联系" target="_blank"><i class="fab fa-qq"></i></a>
                    {/if}
                    {if !empty($footer_contact_email)}
                    <a href="mailto:{$footer_contact_email}" class="footer-social-icon" title="邮件联系"><i class="fas fa-envelope"></i></a>
                    {/if}
                    {if !empty($website_url)}
                    <a href="{$website_url}" class="footer-social-icon" title="访问官网"><i class="fas fa-globe"></i></a>
                    {/if}
                    {if !empty($contact_wx)}
                    <a href="javascript:;" class="footer-social-icon" title="微信联系"><i class="fab fa-weixin"></i></a>
                    {/if}
                </div>
            </div>
            
            <div class="footer-column animate slide-up delay-200">
                <h3 class="footer-title">{$footer_quicklinks_title}</h3>
                <div class="footer-links">
                    {foreach $footer_quicklinks as $link}
                    <a href="{$link.href}">{$link.name}</a>
                    {/foreach}
                </div>
            </div>
            
            <div class="footer-column animate slide-up delay-300">
                <h3 class="footer-title">{$footer_products_title}</h3>
                <div class="footer-links">
                    {foreach $footer_products as $product}
                    <a href="{$product.href}">{$product.name}</a>
                    {/foreach}
                </div>
            </div>
            
            <div class="footer-column animate slide-up delay-400">
                <h3 class="footer-title">{$footer_contact_title}</h3>
                {if !empty($footer_contact_address)}
                <div class="footer-contact">
                    <div class="footer-contact-icon"><i class="fas fa-map-marker-alt"></i></div>
                    <div class="footer-contact-text">{$footer_contact_address}</div>
                </div>
                {/if}
                {if !empty($footer_contact_phone)}
                <div class="footer-contact">
                    <div class="footer-contact-icon"><i class="fas fa-phone-alt"></i></div>
                    <div class="footer-contact-text">{$footer_contact_phone}</div>
                </div>
                {/if}
                {if !empty($footer_contact_email)}
                <div class="footer-contact">
                    <div class="footer-contact-icon"><i class="fas fa-envelope"></i></div>
                    <div class="footer-contact-text">{$footer_contact_email}</div>
                </div>
                {/if}
                {if !empty($footer_contact_hours)}
                <div class="footer-contact">
                    <div class="footer-contact-icon"><i class="fas fa-clock"></i></div>
                    <div class="footer-contact-text">{$footer_contact_hours}</div>
                </div>
                {/if}
                {if !empty($contact_qq)}
                <div class="footer-contact">
                    <div class="footer-contact-icon"><i class="fab fa-qq"></i></div>
                    <div class="footer-contact-text">QQ: {$contact_qq}</div>
                </div>
                {/if}
                {if !empty($contact_wx)}
                <div class="footer-contact">
                    <div class="footer-contact-icon"><i class="fab fa-weixin"></i></div>
                    <div class="footer-contact-text">微信: {$contact_wx}</div>
                </div>
                {/if}
            </div>
        </div>
        
        <div class="container footer-bottom">
            <div class="copyright">
                <div class="copyright-content">
                    <span class="copyright-text">
                        <i class="far fa-copyright"></i>
                        {$siteName|default='Bluelunbo'} - 版权所有 © {$year|default='2023'}-至今
                    </span>
                    <div class="beian-info">
                        {if !empty($icpNumber)}
                        <a href="https://beian.miit.gov.cn/" target="_blank" class="beian-link">
                            <i class="fas fa-shield-alt"></i>
                            <span>{$icpNumber}</span>
                        </a>
                        {/if}
                        {if !empty($gaNumber)}
                        <a href="{$icpCert}" target="_blank" class="beian-link">
                            <i class="fas fa-badge-check"></i>
                            <span>{$gaNumber}</span>
                        </a>
                        {/if}
                    </div>
                </div>
            </div>
        </div>
    </footer>
    <!-- JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 标签导航
            const tabContainer = document.querySelector('.tab-container');
            const tabs = document.querySelectorAll('.tab-item');
            const panels = document.querySelectorAll('.tab-panel');
            const underline = document.querySelector('.tab-underline');
            let currentIndex = 0;
            
            // 固定轮播图位置
            const fixCarouselPosition = () => {
                const carousel = document.querySelector('.carousel-container');
                if (!carousel) return;
                
                // 标记为位置固定
                carousel.setAttribute('data-fixed-position', 'true');
                carousel.style.transform = 'translateZ(0)';
                
                // 确保浮动行动画平滑
                const floatingRows = document.querySelectorAll('.floating-row');
                floatingRows.forEach(row => {
                    row.style.willChange = 'transform';
                    row.style.transform = 'translateZ(0)';
                });
            };
            
            // 在页面加载和窗口调整大小时固定轮播图位置
            fixCarouselPosition();
            window.addEventListener('resize', fixCarouselPosition);
            
            // 添加窗口大小变化时更新标签尺寸数据的函数
            function updateTabDimensions() {
                if (!tabs || !tabs.length) return;
                
                // 重新计算并保存所有标签的宽度和位置信息
                tabs.forEach((tab, idx) => {
                    tab.setAttribute('data-width', tab.offsetWidth);
                    tab.setAttribute('data-left', tab.offsetLeft);
                });
                
                // 更新当前激活标签的下划线位置
                const activeTab = document.querySelector('.tab-item.active');
                if (activeTab && underline) {
                    const tabWidth = activeTab.getAttribute('data-width') || activeTab.offsetWidth;
                    const tabLeft = activeTab.getAttribute('data-left') || activeTab.offsetLeft;
                    
                    underline.style.width = `${tabWidth}px`;
                    underline.style.left = `${tabLeft}px`;
                }
            }
            
            // 监听窗口大小变化事件
            window.addEventListener('resize', updateTabDimensions);
            
            // 初始化下划线位置和宽度
            function initUnderline() {
                if (!tabs[0] || !underline) return;
                
                const activeTab = tabs[0];
                
                // 确保下划线与标签宽度匹配
                const tabWidth = activeTab.getBoundingClientRect().width;
                
                // 先隐藏下划线，设置正确位置后再显示，避免初始闪烁
                underline.style.opacity = '0';
                underline.style.width = `${tabWidth}px`;
                underline.style.left = `${activeTab.offsetLeft}px`;
                
                // 设置颜色
                underline.style.backgroundColor = 'var(--primary)';
                
                // 将首个标签设为激活状态
                tabs[0].classList.add('active');
                panels[0].classList.add('active');
                
                // 保存所有标签项的宽度和位置信息
                updateTabDimensions();
                
                // 确保轮播图位置固定
                fixCarouselPosition();
                
                // 短暂延迟后显示下划线，确保平滑过渡
                setTimeout(() => {
                    underline.style.opacity = '0.85';
                }, 200);
            }

            // 切换标签
            function switchTab(index) {
                if (!tabs[index] || !panels[index] || !underline) return;
                
                // 获取当前内容容器的高度，用于稳定布局
                const tabContent = document.querySelector('.tab-content');
                const originalHeight = tabContent ? tabContent.offsetHeight : 0;
                if (tabContent) {
                    tabContent.style.minHeight = originalHeight + 'px';
                }
                
                // 移除所有active类
                tabs.forEach(tab => {
                    tab.classList.remove('active');
                    tab.classList.remove('just-clicked');
                });
                panels.forEach(panel => panel.classList.remove('active'));

                // 添加新的active类和点击效果类
                tabs[index].classList.add('active');
                tabs[index].classList.add('just-clicked');
                panels[index].classList.add('active');

                // 计算正确的下划线位置
                const activeTab = tabs[index];
                const tabWidth = activeTab.getBoundingClientRect().width;
                
                // 移动下划线，使用流畅的动画
                underline.style.width = `${tabWidth}px`;
                underline.style.left = `${activeTab.offsetLeft}px`;
                
                // 添加特定颜色的光效
                let color;
                let bgColor;
                switch(tabs[index].getAttribute('data-tab')) {
                    case 'compliance':
                        color = 'rgba(65, 105, 225, 0.2)'; // 蓝色 - 合规
                        bgColor = 'var(--primary)';
                        break;
                    case 'marketing':
                        color = 'rgba(255, 107, 122, 0.2)'; // 红色 - 营销
                        bgColor = '#ff6b7a';
                        break;
                    case 'payment':
                        color = 'rgba(46, 204, 113, 0.2)'; // 绿色 - 支付
                        bgColor = '#2ecc71';
                        break;
                    case 'distribution':
                        color = 'rgba(241, 196, 15, 0.2)'; // 黄色 - 分销
                        bgColor = '#f1c40f';
                        break;
                    default:
                        color = 'rgba(65, 105, 225, 0.2)';
                        bgColor = 'var(--primary)';
                }
                
                // 更新下划线颜色
                underline.style.backgroundColor = bgColor;
                
                // 创建一个短暂的背景动画
                underline.style.boxShadow = `0 5px 20px ${color}`;
                setTimeout(() => {
                    underline.style.boxShadow = '0 5px 15px rgba(65, 105, 225, 0.08)';
                }, 500);
                
                // 设置超时移除点击效果类，保持动画持续时间一致
                setTimeout(() => {
                    tabs[index].classList.remove('just-clicked');
                }, 400);
                
                // 确保切换标签后轮播图位置不变
                if (carousel && carouselRect) {
                    // 使用 requestAnimationFrame 确保在下一帧重新定位
                    requestAnimationFrame(() => {
                        const newRect = carousel.getBoundingClientRect();
                        // 如果位置有变化，应用修正
                        if (Math.abs(newRect.top - carouselRect.top) > 1) {
                            carousel.style.transform = `translateY(${carouselRect.top - newRect.top}px)`;
                            // 缓慢恢复正常位置（可选）
                            setTimeout(() => {
                                carousel.style.transition = 'transform 0.2s ease';
                                carousel.style.transform = 'translateY(0)';
                                setTimeout(() => {
                                    carousel.style.transition = '';
                                }, 200);
                            }, 50);
                        }
                    });
                }
                
                // 切换完成后恢复内容区高度
                setTimeout(() => {
                    if (tabContent) {
                        tabContent.style.minHeight = '';
                    }
                    // 确保body高度是auto
                    document.body.style.height = 'auto';
                }, 300);
            }
            
            // 初始化完成后确保body高度是自动的
            setTimeout(() => {
                if (document.body.style.height && document.body.style.height !== 'auto') {
                    document.body.style.height = 'auto';
                }
            }, 1000);
            
            // 监听窗口大小变化，确保body高度是自动的
            window.addEventListener('resize', function() {
                if (document.body.style.height && document.body.style.height !== 'auto') {
                    document.body.style.height = 'auto';
                }
            });
            
            // 监听滚动事件，如果body高度异常，则重置
            window.addEventListener('scroll', function() {
                if (parseFloat(document.body.style.height) > window.innerHeight * 2) {
                    document.body.style.height = 'auto';
                }
            }, { passive: true });

            // 初始化
            initUnderline();

            // 点击标签切换
            tabs.forEach((tab, index) => {
                tab.addEventListener('click', () => {
                    // 保存轮播图的位置信息
                    const carousel = document.querySelector('.carousel-container');
                    let carouselRect = null;
                    if (carousel) {
                        carouselRect = carousel.getBoundingClientRect();
                    }
                    
                    // 切换标签
                    currentIndex = index;
                    switchTab(index);
                    
                    // 确保轮播图不会因布局变化而移动
                    if (carousel && carouselRect) {
                        requestAnimationFrame(() => {
                            const newRect = carousel.getBoundingClientRect();
                            // 如果位置发生变化，应用一个反向变换以保持视觉上的稳定
                            if (newRect.top !== carouselRect.top) {
                                carousel.style.transform = `translateY(${carouselRect.top - newRect.top}px)`;
                                // 平滑恢复原始位置
                                setTimeout(() => {
                                    carousel.style.transition = 'transform 0.3s ease';
                                    carousel.style.transform = 'translateY(0)';
                                    // 移除过渡效果
                                    setTimeout(() => {
                                        carousel.style.transition = '';
                                    }, 300);
                                }, 10);
                            }
                        });
                    }
                });
            });

            // 窗口大小改变时重新计算下划线位置
            window.addEventListener('resize', () => {
                switchTab(currentIndex);
            });
            
            // 滚动触发动画
            const animateElements = document.querySelectorAll('.animate');
            
            function checkScroll() {
                const triggerBottom = window.innerHeight * 0.9; // 提高触发阈值
                
                animateElements.forEach(element => {
                    const elementTop = element.getBoundingClientRect().top;
                    const elementBottom = element.getBoundingClientRect().bottom;
                    
                    // 判断元素是否在视口中或接近视口
                    if (elementTop < triggerBottom && elementBottom > 0) {
                        element.classList.add('active');
                        
                        // 特别处理feature-3d-card
                        if (element.classList.contains('feature-3d-card')) {
                            // 确保动画样式被应用
                            element.style.opacity = '1';
                            element.style.visibility = 'visible';
                        }
                    }
                });
            }
            
            // 初始检查
            checkScroll();
            // 再次检查，确保所有元素都被激活
            setTimeout(checkScroll, 500);
            
            // 滚动时检查
            let scrollTimeout;
            window.addEventListener('scroll', function() {
                clearTimeout(scrollTimeout);
                scrollTimeout = setTimeout(checkScroll, 50);
            });
            
            // 确保当前可见的行业面板内的动画元素立即显示
            function activateVisiblePanelAnimations() {
                const visiblePanel = document.querySelector('.industry-panel.active');
                if (visiblePanel) {
                    const animations = visiblePanel.querySelectorAll('.animate');
                    animations.forEach(element => {
                        element.classList.add('active');
                    });
                }
            }
            
            // 初始激活当前面板的动画
            activateVisiblePanelAnimations();

            // 添加键盘导航支持
            tabContainer.addEventListener('keydown', (e) => {
                if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {
                    currentIndex = (currentIndex + 1) % tabs.length;
                    switchTab(currentIndex);
                    e.preventDefault();
                } else if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
                    currentIndex = (currentIndex - 1 + tabs.length) % tabs.length;
                    switchTab(currentIndex);
                    e.preventDefault();
                }
            });

            // 为了改善无障碍性，添加适当的ARIA属性
            tabs.forEach((tab, index) => {
                tab.setAttribute('role', 'tab');
                tab.setAttribute('aria-selected', index === 0 ? 'true' : 'false');
                tab.setAttribute('tabindex', index === 0 ? '0' : '-1');
                
                tab.addEventListener('click', () => {
                    currentIndex = index;
                    switchTab(index);
                    
                    // 更新ARIA属性
                    tabs.forEach((t, i) => {
                        t.setAttribute('aria-selected', i === index ? 'true' : 'false');
                        t.setAttribute('tabindex', i === index ? '0' : '-1');
                    });
                });
            });
        });

        // 添加标签切换功能
        document.addEventListener('DOMContentLoaded', function() {
            const tabItems = document.querySelectorAll('.tab-item');
            const tabPanels = document.querySelectorAll('.tab-panel');
            const tabUnderline = document.querySelector('.tab-underline');
            
            // 初始化下划线位置
            const activeTab = document.querySelector('.tab-item.active');
            if (activeTab) {
                updateUnderline(activeTab);
            }
            
            // 标签切换
            tabItems.forEach(tab => {
                tab.addEventListener('click', function() {
                    // 移除所有活动状态
                    tabItems.forEach(item => item.classList.remove('active'));
                    tabPanels.forEach(panel => panel.classList.remove('active'));
                    
                    // 添加活动状态
                    this.classList.add('active');
                    const tabId = this.getAttribute('data-tab');
                    const activePanel = document.querySelector(`.tab-panel[data-tab="${tabId}"]`);
                    activePanel.classList.add('active');
                    
                    // 更新下划线位置 
                    updateUnderline(this);
                    
                    // 防止轮播图位置变化
                    const carouselContainer = document.querySelector('.carousel-container');
                    if (carouselContainer) {
                        // 保存原始位置状态
                        const rect = carouselContainer.getBoundingClientRect();
                        // 在下一帧恢复位置
                        requestAnimationFrame(() => {
                            carouselContainer.style.transform = 'translateZ(0)'; // 强制硬件加速
                        });
                    }
                });
            });
            
            function updateUnderline(tab) {
                tabUnderline.style.width = `${tab.offsetWidth}px`;
                tabUnderline.style.left = `${tab.offsetLeft}px`;
            }
            
            // 轮播指示器
            const indicators = document.querySelectorAll('.indicator');
            indicators.forEach((indicator, index) => {
                indicator.addEventListener('click', function() {
                    indicators.forEach(ind => ind.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 这里可以添加实际轮播切换逻辑
                });
            });
            
            // 窗口调整大小时更新下划线位置
            window.addEventListener('resize', function() {
                const activeTab = document.querySelector('.tab-item.active');
                if (activeTab) {
                    updateUnderline(activeTab);
                }
            });
            
            // 行业标签切换功能
            const industryTabs = document.querySelectorAll('.industry-tab');
            const industryPanels = document.querySelectorAll('.industry-panel');
            
            // 预加载所有面板，确保图片和资源都已加载，防止切换时跳动
            industryPanels.forEach(panel => {
                // 强制布局计算以确保所有面板都有正确的高度
                void panel.offsetHeight;
                
                // 确保非活动面板完全隐藏
                if (!panel.classList.contains('active')) {
                    panel.style.height = '0';
                    panel.style.overflow = 'hidden';
                } else {
                    // 活动面板正常显示
                    panel.style.height = 'auto';
                    panel.style.overflow = 'visible';
                }
            });
            
            // 定义一个函数来计算和应用所有卡片的高度
            function recalculateCardHeights() {
                // 首先计算所有面板中卡片的最大高度
                let globalMaxHeight = 0;
                industryPanels.forEach(panel => {
                    const cards = panel.querySelectorAll('.feature-card-alt');
                    
                    // 首先移除所有内联高度样式，回到CSS控制的状态
                    cards.forEach(card => {
                        card.style.height = '';
                    });
                    
                    // 临时使面板可见以获取正确的高度
                    const originalStyle = {
                        visibility: panel.style.visibility,
                        display: panel.style.display,
                        height: panel.style.height,
                        overflow: panel.style.overflow,
                        opacity: panel.style.opacity,
                        position: panel.style.position,
                        zIndex: panel.style.zIndex
                    };
                    
                    panel.style.visibility = 'hidden';
                    panel.style.display = 'grid';
                    panel.style.height = 'auto';
                    panel.style.overflow = 'visible';
                    panel.style.opacity = '0';
                    panel.style.position = 'absolute';
                    panel.style.zIndex = '-1';
                    
                    // 计算这个面板下所有卡片的最大高度
                    cards.forEach(card => {
                        const height = card.scrollHeight;
                        if (height > globalMaxHeight) {
                            globalMaxHeight = height;
                        }
                    });
                    
                    // 恢复原始状态
                    panel.style.visibility = originalStyle.visibility;
                    panel.style.display = originalStyle.display;
                    panel.style.height = originalStyle.height;
                    panel.style.overflow = originalStyle.overflow;
                    panel.style.opacity = originalStyle.opacity;
                    panel.style.position = originalStyle.position;
                    panel.style.zIndex = originalStyle.zIndex;
                });
                
                // 应用全局最大高度到所有面板的所有卡片
                industryPanels.forEach(panel => {
                    const cards = panel.querySelectorAll('.feature-card-alt');
                    cards.forEach(card => {
                        card.style.height = `${globalMaxHeight}px`;
                    });
                });
            }
            
            // 防抖函数
            function debounce(func, wait) {
                let timeout;
                return function() {
                    const context = this;
                    const args = arguments;
                    clearTimeout(timeout);
                    timeout = setTimeout(() => {
                        func.apply(context, args);
                    }, wait);
                };
            }
            
            // 初始计算所有卡片高度
            recalculateCardHeights();
            
            // 在窗口大小改变时重新计算卡片高度
            window.addEventListener('resize', debounce(recalculateCardHeights, 250));
            
            // 为所有行业标签添加点击事件监听
            industryTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // 如果已经是当前活动标签，不做任何操作
                    if (this.classList.contains('active')) {
                        return;
                    }
                    
                    // 获取当前激活的面板
                    const currentPanel = document.querySelector('.industry-panel.active');
                    
                    // 移除所有标签的活动状态
                    industryTabs.forEach(item => item.classList.remove('active'));
                    
                    // 添加活动状态到当前标签
                    this.classList.add('active');
                    const tabId = this.getAttribute('data-tab');
                    const targetPanel = document.querySelector(`.industry-panel[data-tab="${tabId}"]`);
                    
                    // 使用淡出淡入效果平滑切换面板
                    if (currentPanel) {
                        // 淡出当前面板
                        currentPanel.style.opacity = '0';
                        currentPanel.style.transform = 'translateY(20px)';
                        
                        // 缩短切换等待时间，减少空白期
                        setTimeout(() => {
                            // 隐藏当前面板
                            currentPanel.classList.remove('active');
                            currentPanel.style.display = 'none';
                            currentPanel.style.height = '0';           // 设置高度为0
                            currentPanel.style.overflow = 'hidden';    // 隐藏溢出内容
                            
                            // 显示目标面板并触发淡入动画
                            targetPanel.style.display = 'grid';
                            targetPanel.classList.add('active');
                            targetPanel.style.height = 'auto';         // 设置目标面板高度为自动
                            targetPanel.style.overflow = 'visible';    // 显示内容
                            
                            // 添加更平滑的显示过渡
                            requestAnimationFrame(() => {
                                targetPanel.style.opacity = '0';
                                targetPanel.style.transform = 'translateY(20px)';
                                
                                // 强制重排
                                void targetPanel.offsetWidth;
                                
                                // 应用过渡
                                targetPanel.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                                
                                // 重新计算所有卡片高度，确保一致性
                                recalculateCardHeights();
                                
                                // 淡入效果
                                requestAnimationFrame(() => {
                                    targetPanel.style.opacity = '1';
                                    targetPanel.style.transform = 'translateY(0)';
                                });
                            });
                            
                            // 重新激活目标面板中的动画，带有级联效果
                            const animations = targetPanel.querySelectorAll('.animate');
                            animations.forEach((element, index) => {
                                element.classList.remove('active');
                                
                                // 使用setTimeout创建级联动画效果
                                setTimeout(() => {
                                    void element.offsetWidth;              // 触发重排
                                    element.classList.add('active');
                                }, 100 + (index * 50));                    // 级联延迟
                            });
                        }, 120);                                       // 将等待时间从150ms减少到120ms
                    } else {
                        // 直接显示目标面板
                        targetPanel.classList.add('active');
                        targetPanel.style.display = 'grid';
                        targetPanel.style.height = 'auto';             // 设置高度为自动
                        targetPanel.style.overflow = 'visible';        // 显示内容
                        
                        // 重新计算所有卡片高度，确保一致性
                        recalculateCardHeights();
                    }
                });
            });
        });

        // 页面加载完成后
        document.addEventListener('DOMContentLoaded', function() {
            // 滚动导航栏效果 - 使用节流函数优化
            const header = document.querySelector('header');
            let lastScrollTop = 0;
            let ticking = false;
            
            // 节流函数
            function throttle(func, delay) {
                let inThrottle;
                return function() {
                    const context = this;
                    const args = arguments;
                    if (!inThrottle) {
                        func.apply(context, args);
                        inThrottle = true;
                        setTimeout(() => inThrottle = false, delay);
                    }
                };
            }
            
            // 处理滚动事件的函数
            function handleScroll() {
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                
                // 使用transform来代替class切换，减少回流
                if (scrollTop > 50) {
                    if (!header.classList.contains('scrolled')) {
                        requestAnimationFrame(() => {
                            header.classList.add('scrolled');
                        });
                    }
                } else {
                    if (header.classList.contains('scrolled')) {
                        requestAnimationFrame(() => {
                            header.classList.remove('scrolled');
                        });
                    }
                }
                
                lastScrollTop = scrollTop;
                ticking = false;
            }
            
            // 优化的滚动事件监听
            window.addEventListener('scroll', throttle(function() {
                if (!ticking) {
                    requestAnimationFrame(handleScroll);
                    ticking = true;
                }
            }, 100));
            
            // 页面过渡动画
            const pageTransitions = document.querySelectorAll('.page-transition');
            pageTransitions.forEach(element => {
                setTimeout(() => {
                    element.classList.add('active');
                }, 300);
            });
            
            // 记录选项卡面板状态，优化切换体验
            const tabPanels = document.querySelectorAll('.tab-panel');
            const tabItems = document.querySelectorAll('.tab-item');
            
            tabItems.forEach((item, index) => {
                item.addEventListener('click', function() {
                    // 已被缓存的面板无需重新加载动画
                    const isFirstLoad = !tabPanels[index].classList.contains('loaded');
                    
                    // 标记面板已加载过
                    tabPanels[index].classList.add('loaded');
                    
                    // 如果是首次加载，应用更精细的动画
                    if (isFirstLoad) {
                        const animations = tabPanels[index].querySelectorAll('.animate');
                        animations.forEach((element, animIndex) => {
                            element.style.transitionDelay = `${0.1 + (animIndex * 0.05)}s`;
                        });
                    }
                });
            });
            
            // 为所有卡片添加悬停3D效果类
            const cards = document.querySelectorAll('.feature-card, .announcement-item, .feature-3d-card');
            cards.forEach(card => {
                card.classList.add('hover-3d');
            });
        });
        
        // 添加视差滚动效果
        window.addEventListener('scroll', function() {
            const parallaxElements = document.querySelectorAll('.parallax-bg');
            parallaxElements.forEach(element => {
                const scrollPosition = window.pageYOffset;
                const speed = element.getAttribute('data-speed') || 0.5;
                element.style.transform = `translateY(${scrollPosition * speed}px)`;
            });
        });

        // 确保当前可见的行业面板内的动画元素立即显示
        function activateVisiblePanelAnimations() {
            const visiblePanel = document.querySelector('.industry-panel.active');
            if (visiblePanel) {
                const animations = visiblePanel.querySelectorAll('.animate');
                animations.forEach(element => {
                    element.classList.add('active');
                });
            }
        }
        
        // 初始激活当前面板的动画
        activateVisiblePanelAnimations();
        
        
        // 确保当滚动到核心优势部分时触发显示
        function checkFeature3DSection() {
            const feature3dSection = document.querySelector('.feature-3d-section');
            if (feature3dSection) {
                const sectionTop = feature3dSection.getBoundingClientRect().top;
                const triggerBottom = window.innerHeight * 0.7;
                
                if (sectionTop < triggerBottom) {
                    activateFeature3DCards();
                    // 移除此监听器，因为已经触发了
                    window.removeEventListener('scroll', checkFeature3DSection);
                }
            }
        }
        
        // 监听滚动以检查核心优势部分
        window.addEventListener('scroll', checkFeature3DSection);
        // 初始检查
        checkFeature3DSection();
    </script>
    <!-- 添加视差滚动装饰元素 -->
    <div class="parallax-decorations">
        <div class="parallax-element parallax-bg" data-speed="0.2" style="position: absolute; top: 150px; left: 5%; width: 50px; height: 50px; background: rgba(65, 105, 225, 0.1); border-radius: 50%;"></div>
        <div class="parallax-element parallax-bg" data-speed="0.3" style="position: absolute; top: 300px; right: 10%; width: 100px; height: 100px; background: rgba(65, 105, 225, 0.05); border-radius: 50%;"></div>
        <div class="parallax-element parallax-bg" data-speed="0.15" style="position: absolute; top: 600px; left: 15%; width: 70px; height: 70px; background: rgba(65, 105, 225, 0.07); border-radius: 50%;"></div>
        <div class="parallax-element parallax-bg" data-speed="0.25" style="position: absolute; top: 1200px; right: 5%; width: 120px; height: 120px; background: rgba(65, 105, 225, 0.03); border-radius: 50%;"></div>
    </div>

    <!-- 在页面加载完成时立即执行 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 立即强制激活所有核心优势卡片
            const advantageCards = document.querySelectorAll('.feature-3d-card');
            if (advantageCards.length > 0) {
                advantageCards.forEach(card => {
                    // 强制显示卡片
                    card.style.opacity = '1';
                    card.style.visibility = 'visible';
                    card.classList.add('active');
                    
                    // 强制显示图标
                    const icon = card.querySelector('.feature-3d-image i');
                    if (icon) {
                        icon.style.opacity = '1';
                        icon.style.visibility = 'visible';
                    }
                    
                    // 强制显示内容
                    const content = card.querySelector('.feature-3d-content');
                    if (content) {
                        content.style.opacity = '1';
                        content.style.visibility = 'visible';
                    }
                });
            }
        });
    </script>

    <!-- 添加新的脚本来均衡卡片高度 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 均衡所有卡片内容区域的高度
            function equalizeCardHeights() {
                const cards = document.querySelectorAll('.feature-3d-card');
                let maxHeight = 0;
                
                // 找出最高的内容区域
                cards.forEach(card => {
                    const content = card.querySelector('.feature-3d-content');
                    if (content) {
                        // 设置为auto以计算实际内容高度
                        content.style.height = 'auto';
                        
                        const height = content.scrollHeight;
                        maxHeight = Math.max(maxHeight, height);
                    }
                });
                
                // 应用最大高度到所有卡片
                if (maxHeight > 0) {
                    cards.forEach(card => {
                        const content = card.querySelector('.feature-3d-content');
                        if (content) {
                            content.style.height = maxHeight + 'px';
                            // 确保内容可见
                            content.style.overflow = 'visible';
                        }
                    });
                }
            }
            
            // 页面加载后执行
            equalizeCardHeights();
            
            // 窗口大小改变时重新计算
            window.addEventListener('resize', equalizeCardHeights);
            
            // 需求分析标签切换功能
            const analysisTabs = document.querySelectorAll('.analysis-tag');
            const analysisPanels = document.querySelectorAll('.analysis-panel');
            
            // 为所有需求分析标签添加点击事件监听
            analysisTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // 如果已经是当前活动标签，不做任何操作
                    if (this.classList.contains('active')) {
                        return;
                    }
                    
                    // 获取当前激活的面板
                    const currentPanel = document.querySelector('.analysis-panel.active');
                    
                    // 移除所有标签的活动状态
                    analysisTabs.forEach(item => item.classList.remove('active'));
                    
                    // 添加活动状态到当前标签
                    this.classList.add('active');
                    const tabId = this.getAttribute('data-tab');
                    const targetPanel = document.querySelector(`.analysis-panel[data-tab="${tabId}"]`);
                    
                    // 使用淡出淡入效果平滑切换面板
                    if (currentPanel) {
                        // 淡出当前面板
                        currentPanel.style.opacity = '0';
                        currentPanel.style.transform = 'translateY(10px)';
                        
                        // 切换面板
                        setTimeout(() => {
                            currentPanel.classList.remove('active');
                            targetPanel.classList.add('active');
                            
                            // 淡入新面板
                            requestAnimationFrame(() => {
                                targetPanel.style.opacity = '1';
                                targetPanel.style.transform = 'translateY(0)';
                            });
                        }, 300);
                    }
                });
            });
        });
    </script>

    <!-- 添加3D卡片激活功能 -->
    <script>
        function activateFeature3DCards() {
            const cards = document.querySelectorAll('.feature-3d-card');
            if (!cards.length) return;
            
            cards.forEach(card => {
                card.addEventListener('mousemove', function(e) {
                    const rect = card.getBoundingClientRect();
                    const x = e.clientX - rect.left; 
                    const y = e.clientY - rect.top;
                    
                    // 计算旋转角度，最大旋转±15度
                    const rotateY = ((x / rect.width) * 30) - 15; 
                    const rotateX = ((y / rect.height) * -30) + 15;
                    
                    // 应用变换
                    card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
                });
                
                card.addEventListener('mouseleave', function() {
                    // 鼠标离开时恢复原始状态
                    card.style.transform = 'perspective(1000px) rotateX(0) rotateY(0)';
                });
            });
        }

        // 定义全局carousel变量
        let carousel = null;

        // 更新切换标签页函数
        function switchTab(index) {
            const tabs = document.querySelectorAll('.tab-link');
            const tabContents = document.querySelectorAll('.tab-content');
            
            tabs.forEach(tab => tab.classList.remove('active'));
            tabs[index].classList.add('active');
            
            tabContents.forEach(content => content.style.display = 'none');
            tabContents[index].style.display = 'block';
            
            // 检查并初始化轮播图
            if (carousel && typeof carousel.goTo === 'function') {
                carousel.goTo(0); // 重置轮播到第一张
            }
        }
    </script>

    <!-- 立即修复body高度问题 -->
    <script>
        (function fixBodyHeight() {
            // 检查body是否有固定高度
            if (document.body && document.body.style.height) {
                // 立即重置body高度
                document.body.style.height = 'auto';
                console.log('Body高度已重置为auto');
            }
            
            // 添加DOMContentLoaded事件监听，确保在页面加载后也执行检查
            document.addEventListener('DOMContentLoaded', function() {
                if (document.body.style.height && document.body.style.height !== 'auto') {
                    document.body.style.height = 'auto';
                    console.log('DOMContentLoaded: Body高度已重置为auto');
                }
            });
        })();
    </script>
</body>
</html>