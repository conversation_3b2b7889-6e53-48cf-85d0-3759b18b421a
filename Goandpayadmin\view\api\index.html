<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>去支付弹窗提示配置</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background-color: #f5f5f5;
        }
        .app-container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #ffffff;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        }
        .header {
            margin-bottom: 24px;
            border-bottom: 1px solid #f0f0f0;
            padding-bottom: 16px;
        }
        .header-title {
            font-size: 20px;
            font-weight: 500;
            color: #1d2129;
            margin: 0;
        }
        .form-section {
            margin-bottom: 20px;
        }
        .action-bar {
            margin-top: 40px;
            display: flex;
            justify-content: flex-end;
        }
        .action-bar .el-button + .el-button {
            margin-left: 16px;
        }
        .frequency-tips {
            color: #86909c;
            margin-top: 8px;
            font-size: 13px;
        }
        .mobile-preview {
            background: #f7f8fa;
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 24px;
            border: 1px solid #e5e6eb;
        }
        
        .editor-container {
            border: 1px solid #dcdfe6;
            margin-bottom: 10px;
        }
        .w-e-toolbar {
            border-bottom: 1px solid #dcdfe6 !important;
        }
        .w-e-text-container {
            height: 400px !important;
        }
        /* 编辑器内容样式 */
        .w-e-text-container [contenteditable=true] {
            color: #4a5568;
            line-height: 1.6;
            font-size: 15px;
        }
        .w-e-text-container [contenteditable=true] p {
            margin: 10px 0;
        }
        .w-e-text-container [contenteditable=true] img {
            max-width: 100%;
            height: auto;
        }
        .w-e-text-container [contenteditable=true] table {
            border-collapse: collapse;
        }
        .w-e-text-container [contenteditable=true] td,
        .w-e-text-container [contenteditable=true] th {
            border: 1px solid #ddd;
            padding: 8px;
        }
        
        /* 响应式样式调整 */
        @media screen and (max-width: 768px) {
            .el-form-item {
                margin-bottom: 15px;
            }
            
            .el-form {
                padding: 10px;
            }
            
            .el-form-item__label {
                width: auto !important;
                text-align: left;
                float: none;
                display: block;
                margin-bottom: 8px;
                padding: 0;
            }
            
            .el-form-item__content {
                margin-left: 0 !important;
            }
            
            .el-radio-group {
                display: flex;
                flex-direction: column;
                gap: 10px;
            }
            
            .el-radio {
                margin-right: 0;
                margin-bottom: 5px;
            }
            
            .w-e-toolbar {
                flex-wrap: wrap;
            }
            
            .w-e-text-container {
                height: 300px !important;
            }
            
            .editor-container {
                margin: 0 -10px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <el-card shadow="never">
            <template #header>
                <div class="card-header">
                    <span>去支付弹窗提示配置</span>
                </div>
            </template>
            <el-form :model="form" label-width="120px">
                <el-form-item label="弹窗开关：">
                    <el-switch v-model="form.status" :active-value="1" :inactive-value="0" />
                </el-form-item>

                <el-form-item label="弹出频率：">
                    <el-radio-group v-model="form.frequency">
                        <el-radio label="once">仅弹一次</el-radio>
                        <el-radio label="login">每次访问</el-radio>
                        <el-radio label="daily">每天一次</el-radio>
                        <el-radio label="weekly">每周一次</el-radio>
                    </el-radio-group>
                </el-form-item>

                <el-form-item label="支付提示内容：">
                    <div class="editor-container">
                        <div id="editor-toolbar" style="border-bottom: 1px solid #ccc;"></div>
                        <div id="editor-text-area" style="height: 300px"></div>
                    </div>
                    <div class="el-form-item-msg" style="margin-top: 8px; color: #666;">
                        提示：支持HTML标签和CSS样式，可以设置文字颜色、大小、添加图片等
                    </div>
                </el-form-item>
                
                <el-form-item label="启用语音朗读：">
                    <el-switch v-model="form.readEnabled" :active-value="1" :inactive-value="0" />
                    <div class="el-form-item-msg" style="margin-top: 8px; color: #666;">
                        开启后，弹窗内容将会被自动朗读给用户
                    </div>
                </el-form-item>
                
                <el-form-item label="确认按钮倒计时：">
                    <el-input-number v-model="form.closeDelay" :min="0" :max="60" :precision="0"></el-input-number>
                    <span class="el-input-group__append">秒</span>
                    <div class="el-form-item-msg" style="margin-top: 8px; color: #666;">
                        设置后，确认按钮将在倒计时结束后才可点击，设为0表示无需等待
                    </div>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" :loading="loading" @click="save">保存设置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
    </div>

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/dist/index.min.js"></script>
    <link href="/static/others/dist/css/style.css" rel="stylesheet">

    <script>
        const { createApp, ref, reactive, onMounted } = Vue;
        const { ElMessage } = ElementPlus;

        createApp({
            setup() {
                const loading = ref(false);
                const form = reactive({
                    status: 1,
                    frequency: 'once',
                    readEnabled: 1,
                    closeDelay: 0
                });
                let editor = null;

                onMounted(() => {
                    if (window.wangEditor) {
                        const E = window.wangEditor;
                        editor = E.createEditor({
                            selector: '#editor-text-area',
                            html: '',
                            config: {
                                placeholder: '请输入支付提示内容...',
                                html: true,
                                MENU_CONF: {
                                    uploadImage: {
                                        server: '/merchantApi/Upload/file',
                                        fieldName: 'file',
                                        maxFileSize: 2 * 1024 * 1024,
                                        maxNumberOfFiles: 10,
                                        allowedFileTypes: ['image/*'],
                                        meta: {
                                            // 可以添加自定义参数
                                        },
                                        headers: {
                                            // 可以添加自定义请求头
                                        },
                                        onBeforeUpload(file) {
                                            console.log('准备上传图片', file)
                                            return file // 返回 false 则终止上传
                                        },
                                        onProgress(progress) {
                                            console.log('上传进度', progress)
                                        },
                                        onSuccess(file, res) {
                                            console.log('上传成功', file, res)
                                        },
                                        onFailed(file, res) {
                                            console.log('上传失败', file, res)
                                            ElMessage.error('图片上传失败：' + (res?.msg || '未知错误'))
                                        },
                                        onError(file, err, res) {
                                            console.log('上传错误', file, err, res)
                                            ElMessage.error('图片上传失败：' + (res?.msg || err.message || '未知错误'))
                                        },
                                        customInsert(res, insertFn) {
                                            if (res.code === 1) {
                                                insertFn(res.data.url, res.data.original_name || '', '')
                                            } else {
                                                ElMessage.error(res.msg || '上传失败')
                                            }
                                        }
                                    }
                                }
                            }
                        });

                        const toolbar = E.createToolbar({
                            editor,
                            selector: '#editor-toolbar',
                            config: {
                                excludeKeys: [],
                                toolbarKeys: [
                                    'headerSelect',
                                    'blockquote',
                                    '|',
                                    'bold',
                                    'underline',
                                    'italic',
                                    {
                                        key: 'group-more-style',
                                        title: '更多',
                                        menuKeys: ['through', 'code', 'sup', 'sub', 'clearStyle'],
                                    },
                                    '|',
                                    'color',
                                    'bgColor',
                                    '|',
                                    'fontSize',
                                    'fontFamily',
                                    'lineHeight',
                                    '|',
                                    'bulletedList',
                                    'numberedList',
                                    'todo',
                                    {
                                        key: 'group-justify',
                                        title: '对齐',
                                        menuKeys: ['justifyLeft', 'justifyCenter', 'justifyRight', 'justifyJustify'],
                                    },
                                    '|',
                                    'insertImage',
                                    '|',
                                    'insertTable',
                                    'undo',
                                    'redo',
                                    '|',
                                    'fullScreen'
                                ]
                            }
                        });
                    } else {
                        ElMessage.error('富文本编辑器加载失败，请刷新页面重试');
                    }

                    fetchData();
                });

                // 获取数据
                const fetchData = async () => {
                    try {
                        const res = await axios.post("/plugin/Goandpay/api/fetchData");
                        if (res.data?.code === 200) {
                            const data = res.data.data;
                            form.status = parseInt(data.status);
                            form.frequency = data.frequency;
                            form.readEnabled = parseInt(data.read_enabled);
                            form.closeDelay = parseInt(data.close_delay) || 0;
                            if (editor) {
                                editor.setHtml(data.payment_notice || '');
                            }
                        } else {
                            ElMessage.error(res.data?.msg || '获取数据失败');
                        }
                    } catch (error) {
                        console.error('获取数据失败:', error);
                        ElMessage.error('获取数据失败');
                    }
                };

                // 保存数据
                const save = async () => {
                    if (!editor) {
                        ElMessage.error('编辑器未初始化');
                        return;
                    }

                    const content = editor.getHtml();
                    if (!content.trim()) {
                        ElMessage.warning('请输入支付提示内容');
                        return;
                    }

                    loading.value = true;
                    try {
                        const formData = new FormData();
                        formData.append('status', form.status);
                        formData.append('frequency', form.frequency);
                        formData.append('read_enabled', form.readEnabled);
                        formData.append('close_delay', form.closeDelay);
                        formData.append('payment_notice', content);
                        
                        const res = await axios.post("/plugin/Goandpay/api/save", formData);
                        if (res.data?.code === 200) {
                            ElMessage.success('保存成功');
                            await fetchData();
                        } else {
                            ElMessage.error(res.data?.msg || '保存失败');
                        }
                    } catch (error) {
                        console.error('保存失败:', error);
                        ElMessage.error('保存失败');
                    } finally {
                        loading.value = false;
                    }
                };

                return {
                    loading,
                    form,
                    save
                };
            }
        })
        .use(ElementPlus)
        .mount('#app');
    </script>
</body>
</html> 