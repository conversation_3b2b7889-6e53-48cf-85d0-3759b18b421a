"use strict";

/**
 * This file contains all implemented properties that are not a part of any
 * current specifications or drafts, but are handled by browsers nevertheless.
 */

module.exports = [
  "background-composite",
  "border-after",
  "border-after-color",
  "border-after-style",
  "border-after-width",
  "border-before",
  "border-before-color",
  "border-before-style",
  "border-before-width",
  "border-end",
  "border-end-color",
  "border-end-style",
  "border-end-width",
  "border-fit",
  "border-horizontal-spacing",
  "border-start",
  "border-start-color",
  "border-start-style",
  "border-start-width",
  "border-vertical-spacing",
  "color-correction",
  "column-axis",
  "column-break-after",
  "column-break-before",
  "column-break-inside",
  "column-rule-color",
  "flex-align",
  "flex-item-align",
  "flex-line-pack",
  "flex-order",
  "flex-pack",
  "flex-wrap",
  "font-size-delta",
  "font-smoothing",
  "highlight",
  "hyphenate-limit-after",
  "hyphenate-limit-before",
  "locale",
  "logical-height",
  "logical-width",
  "margin-after",
  "margin-after-collapse",
  "margin-before",
  "margin-before-collapse",
  "margin-bottom-collapse",
  "margin-collapse",
  "margin-end",
  "margin-start",
  "margin-top-collapse",
  "marquee",
  "marquee-direction",
  "marquee-increment",
  "marquee-repetition",
  "marquee-speed",
  "marquee-style",
  "mask-attachment",
  "mask-box-image-outset",
  "mask-box-image-repeat",
  "mask-box-image-slice",
  "mask-box-image-source",
  "mask-box-image-width",
  "mask-position-x",
  "mask-position-y",
  "mask-repeat-x",
  "mask-repeat-y",
  "match-nearest-mail-blockquote-color",
  "max-logical-height",
  "max-logical-width",
  "min-logical-height",
  "min-logical-width",
  "nbsp-mode",
  "overflow-scrolling",
  "padding-after",
  "padding-before",
  "padding-end",
  "padding-start",
  "perspective-origin-x",
  "perspective-origin-y",
  "region-break-after",
  "region-break-before",
  "region-break-inside",
  "region-overflow",
  "rtl-ordering",
  "svg-shadow",
  "tap-highlight-color",
  "text-decorations-in-effect",
  "text-emphasis-color",
  "text-fill-color",
  "text-security",
  "text-size-adjust",
  "text-stroke",
  "text-stroke-color",
  "text-stroke-width",
  "transform",
  "transform-origin-x",
  "transform-origin-y",
  "transform-origin-z",
  "user-drag",
  "user-modify",
  "wrap",
  "wrap-margin",
  "wrap-padding",
  "wrap-shape-inside",
  "wrap-shape-outside",
  "zoom"
].map((prop) => `-webkit-${prop}`);
