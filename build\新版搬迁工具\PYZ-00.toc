('D:\\编程\\插件开发\\plugin\\build\\新版搬迁工具\\PYZ-00.pyz',
 [('OpenSSL',
   'D:\\pyfile\\py\\Lib\\site-packages\\OpenSSL\\__init__.py',
   'PYMODULE'),
  ('OpenSSL.SSL',
   'D:\\pyfile\\py\\Lib\\site-packages\\OpenSSL\\SSL.py',
   'PYMODULE'),
  ('OpenSSL._util',
   'D:\\pyfile\\py\\Lib\\site-packages\\OpenSSL\\_util.py',
   'PYMODULE'),
  ('OpenSSL.crypto',
   'D:\\pyfile\\py\\Lib\\site-packages\\OpenSSL\\crypto.py',
   'PYMODULE'),
  ('OpenSSL.version',
   'D:\\pyfile\\py\\Lib\\site-packages\\OpenSSL\\version.py',
   'PYMODULE'),
  ('PyQt5',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('__future__', 'D:\\pyfile\\py\\Lib\\__future__.py', 'PYMODULE'),
  ('_compat_pickle', 'D:\\pyfile\\py\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'D:\\pyfile\\py\\Lib\\_compression.py', 'PYMODULE'),
  ('_py_abc', 'D:\\pyfile\\py\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\pyfile\\py\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_strptime', 'D:\\pyfile\\py\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'D:\\pyfile\\py\\Lib\\_threading_local.py', 'PYMODULE'),
  ('argparse', 'D:\\pyfile\\py\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\pyfile\\py\\Lib\\ast.py', 'PYMODULE'),
  ('base64', 'D:\\pyfile\\py\\Lib\\base64.py', 'PYMODULE'),
  ('bcrypt',
   'D:\\pyfile\\py\\Lib\\site-packages\\bcrypt\\__init__.py',
   'PYMODULE'),
  ('bisect', 'D:\\pyfile\\py\\Lib\\bisect.py', 'PYMODULE'),
  ('brotli', 'D:\\pyfile\\py\\Lib\\site-packages\\brotli.py', 'PYMODULE'),
  ('bz2', 'D:\\pyfile\\py\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\pyfile\\py\\Lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'D:\\pyfile\\py\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\pyfile\\py\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('chardet',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.enums',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.escprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.johabfreq',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\johabfreq.py',
   'PYMODULE'),
  ('chardet.johabprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\johabprober.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langrussianmodel',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\langrussianmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.utf1632prober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\utf1632prober.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.version',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\pyfile\\py\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\pyfile\\py\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.assets',
   'D:\\pyfile\\py\\Lib\\site-packages\\charset_normalizer\\assets\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\pyfile\\py\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\pyfile\\py\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\pyfile\\py\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.md',
   'D:\\pyfile\\py\\Lib\\site-packages\\charset_normalizer\\md.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\pyfile\\py\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\pyfile\\py\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('contextlib', 'D:\\pyfile\\py\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'D:\\pyfile\\py\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'D:\\pyfile\\py\\Lib\\copy.py', 'PYMODULE'),
  ('cryptography',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.aead',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ciphers',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ciphers.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.cmac',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\cmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.decode_asn1',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\decode_asn1.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ec',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.rsa',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.pkcs12',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\pkcs12.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('csv', 'D:\\pyfile\\py\\Lib\\csv.py', 'PYMODULE'),
  ('dataclasses', 'D:\\pyfile\\py\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'D:\\pyfile\\py\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'D:\\pyfile\\py\\Lib\\decimal.py', 'PYMODULE'),
  ('dis', 'D:\\pyfile\\py\\Lib\\dis.py', 'PYMODULE'),
  ('email', 'D:\\pyfile\\py\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\pyfile\\py\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\pyfile\\py\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'D:\\pyfile\\py\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase',
   'D:\\pyfile\\py\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime', 'D:\\pyfile\\py\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'D:\\pyfile\\py\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\pyfile\\py\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'D:\\pyfile\\py\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'D:\\pyfile\\py\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\pyfile\\py\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'D:\\pyfile\\py\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'D:\\pyfile\\py\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\pyfile\\py\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'D:\\pyfile\\py\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'D:\\pyfile\\py\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'D:\\pyfile\\py\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'D:\\pyfile\\py\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'D:\\pyfile\\py\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'D:\\pyfile\\py\\Lib\\email\\utils.py', 'PYMODULE'),
  ('fnmatch', 'D:\\pyfile\\py\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\pyfile\\py\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'D:\\pyfile\\py\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\pyfile\\py\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\pyfile\\py\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\pyfile\\py\\Lib\\gettext.py', 'PYMODULE'),
  ('gzip', 'D:\\pyfile\\py\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\pyfile\\py\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\pyfile\\py\\Lib\\hmac.py', 'PYMODULE'),
  ('http', 'D:\\pyfile\\py\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'D:\\pyfile\\py\\Lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'D:\\pyfile\\py\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http.cookies', 'D:\\pyfile\\py\\Lib\\http\\cookies.py', 'PYMODULE'),
  ('idna', 'D:\\pyfile\\py\\Lib\\site-packages\\idna\\__init__.py', 'PYMODULE'),
  ('idna.core',
   'D:\\pyfile\\py\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'D:\\pyfile\\py\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\pyfile\\py\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'D:\\pyfile\\py\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'D:\\pyfile\\py\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib', 'D:\\pyfile\\py\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'D:\\pyfile\\py\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\pyfile\\py\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\pyfile\\py\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'D:\\pyfile\\py\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\pyfile\\py\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\pyfile\\py\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\pyfile\\py\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\pyfile\\py\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\pyfile\\py\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\pyfile\\py\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\pyfile\\py\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\pyfile\\py\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\pyfile\\py\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\pyfile\\py\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\pyfile\\py\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\pyfile\\py\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\pyfile\\py\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\pyfile\\py\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\pyfile\\py\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\pyfile\\py\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util', 'D:\\pyfile\\py\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'D:\\pyfile\\py\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\pyfile\\py\\Lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'D:\\pyfile\\py\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'D:\\pyfile\\py\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'D:\\pyfile\\py\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\pyfile\\py\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'D:\\pyfile\\py\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'D:\\pyfile\\py\\Lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'D:\\pyfile\\py\\Lib\\mimetypes.py', 'PYMODULE'),
  ('netrc', 'D:\\pyfile\\py\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'D:\\pyfile\\py\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'D:\\pyfile\\py\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'D:\\pyfile\\py\\Lib\\opcode.py', 'PYMODULE'),
  ('pathlib', 'D:\\pyfile\\py\\Lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'D:\\pyfile\\py\\Lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'D:\\pyfile\\py\\Lib\\pkgutil.py', 'PYMODULE'),
  ('pprint', 'D:\\pyfile\\py\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'D:\\pyfile\\py\\Lib\\py_compile.py', 'PYMODULE'),
  ('queue', 'D:\\pyfile\\py\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\pyfile\\py\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\pyfile\\py\\Lib\\random.py', 'PYMODULE'),
  ('requests',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('selectors', 'D:\\pyfile\\py\\Lib\\selectors.py', 'PYMODULE'),
  ('shlex', 'D:\\pyfile\\py\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\pyfile\\py\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\pyfile\\py\\Lib\\signal.py', 'PYMODULE'),
  ('socket', 'D:\\pyfile\\py\\Lib\\socket.py', 'PYMODULE'),
  ('socks', 'D:\\pyfile\\py\\Lib\\site-packages\\socks.py', 'PYMODULE'),
  ('ssl', 'D:\\pyfile\\py\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'D:\\pyfile\\py\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\pyfile\\py\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\pyfile\\py\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\pyfile\\py\\Lib\\subprocess.py', 'PYMODULE'),
  ('tarfile', 'D:\\pyfile\\py\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\pyfile\\py\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\pyfile\\py\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\pyfile\\py\\Lib\\threading.py', 'PYMODULE'),
  ('token', 'D:\\pyfile\\py\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\pyfile\\py\\Lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\pyfile\\py\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('typing', 'D:\\pyfile\\py\\Lib\\typing.py', 'PYMODULE'),
  ('urllib', 'D:\\pyfile\\py\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'D:\\pyfile\\py\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\pyfile\\py\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request', 'D:\\pyfile\\py\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('urllib.response', 'D:\\pyfile\\py\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib3',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib._appengine_environ',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\contrib\\_appengine_environ.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.packages',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\packages\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.backports',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\packages\\backports\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.backports.makefile',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\packages\\backports\\makefile.py',
   'PYMODULE'),
  ('urllib3.packages.six',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\packages\\six.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.request',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\request.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.queue',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\queue.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('zipfile', 'D:\\pyfile\\py\\Lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'D:\\pyfile\\py\\Lib\\zipimport.py', 'PYMODULE')])
