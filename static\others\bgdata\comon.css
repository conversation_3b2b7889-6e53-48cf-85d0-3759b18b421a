@charset "utf-8";*{
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box
}
*,body{
    padding:0px;
    margin:0px;
    color: #222;
    font-family: "微软雅黑";
}
body{
    color:#666;
    font-size: 16px;
}
li{
    list-style-type:none;
}
table{
}
i{
    margin:0px;
    padding:0px;
    text-indent:0px;
}
img{
    border:none;
    max-width: 100%;
}
a{
    text-decoration:none;
    color:#399bff;
}
a.active,a:focus{
    outline:none!important;
    text-decoration:none;
}
ol,ul,p,h1,h2,h3,h4,h5,h6{
    padding:0;
    margin:0
}
a:hover{
    color:#06c;
    text-decoration: none!important
}
.clearfix:after, .clearfix:before {
    display: table;
    content: " "
}
.clearfix:after {
    clear: both
}
.pulll_left{
    float:left;
}
.pulll_right{
    float:right;
}
.canvas{
    position:fixed;
    width:100%;
    left: 0;
    top: 0;
    height:100%;
    z-index: 1;
}
.loading{
    position:fixed;
    left:0;
    top:0;
    font-size:16px;
    z-index:100000000;
    width:100%;
    height:100%;
    background:#1a1a1c;
    text-align:center;
}
.loadbox{
    position:absolute;
    width:160px;
    height:150px;
    color:rgba(255,255,255,.6);
    left:50%;
    top:50%;
    margin-top:-100px;
    margin-left:-75px;
}
.loadbox img{
    margin:10px auto;
    display:block;
    width:40px;
}
.head{
    height:105px;
    background: url(../images/head_bg.png) no-repeat center center;
    position: relative
}
h1{
    color:#fff;
    text-align: left;
    font-size:40px;
    line-height:64px;
    text-align: center;
}
.weather{
    position:absolute;
    right:20px;
    top:0;
    line-height: 70px;
}
.weather span{
    color:rgba(255,255,255,.7)!important;
    font-size: 18px;
}
.mainbox{
    padding:20px 20px 0px 20px;
}
.mainbox>ul{
    margin-left:-.4rem;
    margin-right:-.4rem;
}
.mainbox>ul>li{
    float: left;
    padding: 0 .4rem
}
.mainbox>ul>li{
    width: 24%
}
.mainbox>ul>li:nth-child(2){
    width: 52%
}
.boxall{
    padding:15px;
    background: rgba(0,0,0,.2);
    position: relative;
    margin-bottom:15px;
    z-index: 10;
    border-radius: 12px;
}
.alltitle{
    font-size:18px;
    color:#fff;
    position: relative;
    margin-bottom: 10px;
}
.alltitle:before{
    width: 90%;
    height: 1px;
    top:12px;
    position: absolute;
    content: "";
    border-bottom: dashed #fff 1px;
    right: 0;
    opacity: .2;
}
.alltitle:after{
    width: 8px;
    height:10px;
    top:8px;
    position: absolute;
    content: "";
    border-radius:20px;
    right: 0;
    background: #0c51f9;
}
.navboxall{
    height: calc(100% - 30px);
}
.num,.zhibiao{
    height: 100%;
    width: 50%;
}
.zb1,.zb2,.zb3{
    float: left;
    width: 33.3333%;
    height: 100%;
}
#zb1,#zb2,#zb3{
    height: calc(100% - 30px);
}
.zhibiao span{
    padding-top: 20px;
    display: block;
    text-align: center;
    color: #fff;
    font-size: 16px;
}
.num{
    padding-right: 20px;
}
.numbt{
    font-size: 24px;
    color: #fff;
    padding-top:14px;
}
.numbt span{
    font-size: 18px;
    padding-left: 10px;
    color: #fff;
}
.numtxt{
    color: #fef000;
    font-size: 80px;
    font-family: arial;
    border-top: 1px solid rgba(255,255,255,.1);
    border-bottom: 1px solid rgba(255,255,255,.1);
    padding: 10px 0;
    margin: 18px 0;
    font-weight: bold;
    letter-spacing: 2px;
}
.table1 th{
    border-bottom: 1px solid rgba(255,255,255,.2);
    font-size: 16px;
    color:rgba(255,255,255,.6);
    font-weight: normal;
    padding:10px 0 10px 0;
}
.table1 td{
    font-size: 16px;
    color:rgba(255,255,255,.4);
    padding: 15px 0 0 0;
}
.table1 span{
    width: 24px;
    height: 24px;
    border-radius: 53px;
    display: block;
    background: #878787;
    color: #fff;
    line-height: 24px;
    text-align: center;
}
.table1 tr:nth-child(2) span{
    background: #ed405d
}
.table1 tr:nth-child(3) span{
    background: #f78c44
}
.table1 tr:nth-child(4) span{
    background: #49bcf7
}/*Plugin CSS*/
.sycm ul{
}
.sycm li{
    text-align: center;
    padding: 10px 0;
    position: relative;
    float: left;
    width:50%;
    padding: 20px 0;
}
.sycm ul li:nth-child(odd):before{
    position:absolute;
    content: "";
    height:30%;
    width: 1px;
    background: rgba(255,255,255,.2);
    right: 0;
    top: 30%;
}
.sycm li h2{
    font-size:24px;
    color: #c5ccff;
}
.sycm li span{
    font-size:18px;
    color: #fff;
    opacity: .5;
}
.sycm p{
    border: 1px solid rgba(255,255,255,.3);
    color: rgba(255,255,255,.6);
    text-align: center;
    margin: 10px;
    border-radius: 5px;
    padding: 8px ;
    position: relative;
}
.sycm p:before{
    content: "";
    position: absolute;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid rgba(255,255,255,.3);
    bottom: -8px;
    left: 49%
}
.bg_content{
    margin:0;
    padding:0px;
    font-family:microsoft yahei;
    background:#003bc9;
    background: linear-gradient(to bottom right, #003bc9, #023ac2 , #021341);
    height: 100%;
    width: 100%;
}
.map1,
.map2,
.map3 {
    position:absolute;
    opacity: .5
}
.map1 {
    z-index: 2;
    animation: myfirst2 15s infinite linear;
    width: 500px;
    top: 45px;
    left: 50%;
    margin-left: -250px;
}
.map2 {
    z-index: 3;
    opacity: 0.2;
    animation: myfirst 10s infinite linear;
    width: 435px;
    top: 80px;
    left: 50%;
    margin-left: -217.5px;
}
.map3 {
    z-index: 1;
    width: 400px;
    top: 100px;
    left: 50%;
    margin-left: -200px;
}
@keyframes myfirst2 {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(359deg);
    }
}
@keyframes myfirst {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(-359deg);
    }
}
#echart3{
    z-index: 4;
}