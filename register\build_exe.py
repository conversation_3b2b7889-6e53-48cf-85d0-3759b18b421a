#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PyInstaller打包脚本
用于生成exe可执行文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_dependencies():
    """检查打包依赖"""
    print("[检查] 检查打包依赖...")
    
    try:
        import PyInstaller
        print(f"[成功] PyInstaller 版本: {PyInstaller.__version__}")
    except ImportError:
        print("[失败] PyInstaller 未安装")
        print("请安装: pip install pyinstaller")
        return False
    
    # 检查其他关键依赖
    dependencies = [
        ('PyQt6', 'PyQt6'),
        ('selenium', 'selenium'),
        ('requests', 'requests'),
        ('ddddocr', 'ddddocr'),
        ('PIL', 'Pillow')
    ]
    
    for import_name, display_name in dependencies:
        try:
            __import__(import_name)
            print(f"[成功] {display_name} 已安装")
        except ImportError:
            print(f"[失败] {display_name} 未安装")
            return False
    
    return True

def clean_build_dirs():
    """清理构建目录"""
    print("[清理] 清理构建目录...")
    
    dirs_to_clean = ['build', 'dist_new', '__pycache__']
    files_to_clean = ['*.spec']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"[成功] 已删除 {dir_name}")
    
    # 清理spec文件
    for spec_file in Path('.').glob('*.spec'):
        spec_file.unlink()
        print(f"[成功] 已删除 {spec_file}")

def create_pyinstaller_spec():
    """创建PyInstaller配置文件"""
    print("[编辑] 创建PyInstaller配置文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import sys
import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# 收集数据文件
datas = []

# 添加配置文件
datas += [('config/config_example.ini', 'config/')]

# 添加文档
datas += [('docs/', 'docs/')]

# 收集ddddocr数据文件
try:
    datas += collect_data_files('ddddocr')
except:
    pass

# 收集PyQt6数据文件
try:
    datas += collect_data_files('PyQt6')
except:
    pass

# 隐藏导入
hiddenimports = []
hiddenimports += collect_submodules('ddddocr')
hiddenimports += collect_submodules('selenium')
hiddenimports += collect_submodules('PyQt6')
hiddenimports += [
    'core.register',
    'core.sms_api',
    'core.captcha_handler',
    'core.batch_register',
    'gui.register_gui',
    'browser_automation.selenium_register',
    'browser_automation.element_helper',
    'browser_automation.xpath_config',
    'config.config_manager',
    'utils.fix_chromedriver'
]

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['PyQt5', 'PyQt5.QtCore', 'PyQt5.QtGui', 'PyQt5.QtWidgets', 'tkinter'],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='用户注册工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    with open('register_tool.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("[成功] 配置文件已创建: register_tool.spec")

def build_exe():
    """构建exe文件"""
    print("[构建] 开始构建exe文件...")
    
    # 使用简化的命令行方式，避免Qt冲突
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--onefile',
        '--console',
        '--clean',
        '--noconfirm',
        '--name=用户注册工具',
        '--add-data=config;config',
        '--add-data=docs;docs',
        '--exclude-module=PyQt5',
        '--exclude-module=PyQt5.QtCore',
        '--exclude-module=PyQt5.QtGui',
        '--exclude-module=PyQt5.QtWidgets',
        '--exclude-module=tkinter',
        '--hidden-import=core.register',
        '--hidden-import=core.sms_api',
        '--hidden-import=core.captcha_handler',
        '--hidden-import=core.batch_register',
        '--hidden-import=gui.register_gui',
        '--hidden-import=browser_automation.selenium_register',
        '--hidden-import=browser_automation.element_helper',
        '--hidden-import=browser_automation.xpath_config',
        '--hidden-import=config.config_manager',
        '--hidden-import=utils.fix_chromedriver',
        '--hidden-import=selenium',
        '--hidden-import=requests',
        '--hidden-import=ddddocr',
        '--hidden-import=PyQt6',
        'main.py'
    ]
    
    print(f"[启动] 执行命令: pyinstaller [参数...]")

    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("[成功] 构建成功!")
        return True
    except subprocess.CalledProcessError as e:
        print("[失败] 构建失败!")
        print(f"错误输出: {e.stderr}")
        return False

def copy_additional_files():
    """复制额外文件到dist目录"""
    print("[文件] 复制额外文件...")
    
    dist_dir = Path('dist')
    if not dist_dir.exists():
        print("[失败] dist目录不存在")
        return False
    
    # 要复制的文件和目录
    files_to_copy = [
        ('config/config_example.ini', 'config/config.ini'),
        ('README.md', 'README.md'),
        ('requirements.txt', 'requirements.txt')
    ]
    
    for src, dst in files_to_copy:
        src_path = Path(src)
        dst_path = dist_dir / dst
        
        if src_path.exists():
            # 确保目标目录存在
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            
            if src_path.is_file():
                shutil.copy2(src_path, dst_path)
                print(f"[成功] 已复制: {src} -> {dst}")
            else:
                shutil.copytree(src_path, dst_path, dirs_exist_ok=True)
                print(f"[成功] 已复制目录: {src} -> {dst}")
        else:
            print(f"[警告] 文件不存在: {src}")
    
    return True

def create_installer_script():
    """创建安装脚本"""
    print("[脚本] 创建安装脚本...")
    
    install_script = '''@echo off
chcp 65001 >nul
echo [启动] 970faka.com 注册工具安装程序
echo ================================

echo.
echo [信息] 检查系统要求...

:: 检查Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [失败] 未检测到Python，请先安装Python 3.7+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo [成功] Python 已安装

:: 检查Chrome浏览器
reg query "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\App Paths\\chrome.exe" >nul 2>&1
if %errorlevel% neq 0 (
    echo [失败] 未检测到Chrome浏览器，请先安装Chrome
    echo 下载地址: https://www.google.com/chrome/
    pause
    exit /b 1
)

echo [成功] Chrome浏览器 已安装

echo.
echo [完成] 系统检查通过！
echo.
echo [说明] 使用说明:
echo 1. 双击 "用户注册工具.exe" 启动程序
echo 2. 首次使用建议先配置椰子云信息
echo 3. 详细说明请查看 README.md
echo.
echo [成功] 安装完成！
pause
'''
    
    with open('dist/install.bat', 'w', encoding='gbk') as f:
        f.write(install_script)
    
    print("[成功] 安装脚本已创建: dist/install.bat")

def create_usage_guide():
    """创建使用说明"""
    print("[文档] 创建使用说明...")
    
    usage_text = '''970faka.com 用户注册工具 使用说明
=====================================

[启动] 快速开始
----------
1. 双击 "用户注册工具.exe" 启动程序
2. 选择启动方式：
   - 输入 1：启动图形界面（推荐新手）
   - 输入 2：启动Selenium自动化
   - 输入 3：启动批量注册
   - 输入 4：配置管理

[配置] 首次配置
-----------
1. 启动程序后选择 "4. 配置管理"
2. 选择 "1. 配置椰子云"
3. 输入椰子云用户名、密码和项目ID
4. 测试连接确保配置正确

[手机] 椰子云配置
-----------
- 用户名：椰子云账号用户名
- 密码：椰子云账号密码  
- 项目ID：椰子云项目编号
- 配置后可实现完全自动化注册

[功能] 功能说明
-----------
1. GUI界面：提供可视化操作界面
2. Selenium自动化：浏览器自动化注册
3. 批量注册：支持多线程批量注册
4. 验证码识别：自动识别图形验证码
5. 短信验证：自动获取短信验证码

[设置] 故障排除
-----------
1. 如果程序无法启动：
   - 检查是否安装了Chrome浏览器
   - 运行 install.bat 检查系统要求

2. 如果验证码识别失败：
   - 程序会自动重试
   - 可切换到手动输入模式

3. 如果椰子云连接失败：
   - 检查用户名密码是否正确
   - 检查项目ID是否有效
   - 检查网络连接

[支持] 技术支持
-----------
如有问题，请查看 README.md 获取详细文档。

版本信息：v1.0
更新日期：2025-07-10
'''
    
    with open('dist/使用说明.txt', 'w', encoding='gbk') as f:
        f.write(usage_text)
    
    print("[成功] 使用说明已创建: dist/使用说明.txt")

def main():
    """主函数"""
    print("[构建] 970faka.com 注册工具 - exe打包程序")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        print("[失败] 依赖检查失败，请安装所需依赖")
        return False
    
    # 清理构建目录
    clean_build_dirs()
    
    # 创建配置文件
    create_pyinstaller_spec()
    
    # 构建exe
    if not build_exe():
        print("[失败] 构建失败")
        return False
    
    # 复制额外文件
    copy_additional_files()
    
    # 创建安装脚本和说明
    create_installer_script()
    create_usage_guide()
    
    print("\n[完成] 打包完成!")
    print("=" * 50)
    print("[文件] 输出目录: dist/")
    print("[启动] 可执行文件: dist/用户注册工具.exe")
    print("[脚本] 安装脚本: dist/install.bat")
    print("[文档] 使用说明: dist/使用说明.txt")
    print("\n[提示] 建议:")
    print("1. 测试exe文件是否正常运行")
    print("2. 将整个dist目录打包分发")
    print("3. 用户首次使用前运行install.bat检查环境")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n[中断] 用户中断打包过程")
        sys.exit(1)
    except Exception as e:
        print(f"\n[失败] 打包异常: {e}")
        sys.exit(1)
