<?php

namespace plugin\ZeroSource\controller;

use app\common\controller\BasePlugin;
use think\facade\View;

class Api extends BasePlugin {

    protected $scene = [
        'admin'
    ];
    protected $noNeedLogin = [];

    public function index() {
        return View::fetch();
    }

    // 获取配置数据
    public function fetchData() {
        $params = [
            'status' => intval(plugconf("ZeroSource.status") ?? 0),
            'hideTime' => plugconf("ZeroSource.hideTime") ?? '',
            'disableStatus' => intval(plugconf("ZeroSource.disableStatus") ?? 0),
        ];

        $this->success('success', $params);
    }

    // 保存配置数据
    public function save() {
        $status = $this->request->post('status/d', 0);
        $time = $this->request->post('hideTime/s', '');
        $disableStatus = $this->request->post('disableStatus/d', 0);

        plugconf("ZeroSource.status", $status);
        plugconf("ZeroSource.hideTime", $time);
        plugconf("ZeroSource.disableStatus", $disableStatus);

        $this->success('success');
    }
}
