<?php

namespace plugin\Fengzeroemail\Hook;

use think\Db;
use think\facade\Log;

class DeleteUserCardsHook {

    public function handle($userId) {
        $status = intval(plugconf("Fengzeroemail.status") ?? 0);
        if ($status == 1) {
            $prefix = Db::connect()->getConfig('prefix');
            $goodsIds = $this->getAllGoodsIds($userId);
            
            foreach ($goodsIds as $goodsId) {
                $tableSuffix = goods_card_storage_suffix($goodsId);
                $tableName = "{$prefix}goods_card_storage" . $tableSuffix;
                
                $result = Db::name($tableName)->where('user_id', $userId)->delete();
                
                if ($result) {
                    Log::info("Deleted all cards for user ID: " . $userId . " in table: " . $tableName);
                } else {
                    Log::error("Failed to delete cards for user ID: " . $userId . " in table: " . $tableName);
                }
            }
        }
    }

    private function getAllGoodsIds($userId) {
        return [1, 2, 3];
    }
}