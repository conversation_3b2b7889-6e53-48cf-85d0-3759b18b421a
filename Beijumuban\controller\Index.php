<?php

namespace app\index\iframe\controller;

use app\common\controller\BaseIndex;

class Index extends BaseIndex {

    public function index() {
        $params = get_template_params();

        if (($params->url ?? '') == '') {
            $this->error('请先在后台配置主题');
        }

        $html = \app\common\service\HttpService::get($params->url);

        return view('', [
            'title' => $this->getWebsiteTitle($html),
            'favicon' => $this->getWebsiteFavicon($params->url, $html),
            'params' => $params
        ]);
    }

    private function getWebsiteTitle($html) {
        if (preg_match("/<title>(.*?)<\/title>/i", $html, $matches)) {
            return $matches[1];
        } else {
            return "";
        }
    }

    private function getWebsiteFavicon($url, $html) {
        $favicon = "/favicon.ico";  // 默认路径
        if (preg_match('/<link[^>]*rel=["\'](?:shortcut\s+icon|icon)["\'][^>]*href=["\'](.*?)["\']/i', $html, $matches)) {
            $favicon = $matches[1];
            if (strpos($favicon, "//") === 0) {
                $favicon = "https:" . $favicon;
            } elseif (strpos($favicon, "/") === 0) {
                $parsedUrl = parse_url($url);
                $favicon = $parsedUrl["scheme"] . "://" . $parsedUrl["host"] . $favicon;
            } elseif (!preg_match('/^https?:\/\//i', $favicon)) {
                $favicon = rtrim($url, '/') . '/' . $favicon;
            }
        } else {
            // 如果没找到 <link> 标签，尝试直接拼接 URL
            $parsedUrl = parse_url($url);
            $favicon = $parsedUrl["scheme"] . "://" . $parsedUrl["host"] . "/favicon.ico";
        }
        return $favicon;
    }
}
