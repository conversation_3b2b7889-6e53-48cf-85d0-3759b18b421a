<?php

return [
    'version'            => '1.0.0',
    'min_system_version' => '1.0.0', // 根据系统最低要求填写
    'category_name'      => '功能插件',   // 插件分类
    'logo' => 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM0MDlFRkYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIj48cmVjdCB4PSIyIiB5PSIzIiB3aWR0aD0iMjAiIGhlaWdodD0iMTQiIHJ4PSIyIiByeT0iMiIgZmlsbD0iI0VDRjVGRiIvPjxsaW5lIHgxPSI4IiB5MT0iMjEiIHgyPSIxNiIgeTI9IjIxIi8+PGxpbmUgeDE9IjEyIiB5MT0iMTciIHgyPSIxMiIgeTI9IjIxIi8+PHBhdGggZD0iTTcgOGwzIDNMNyAxNCIvPjxwYXRoIGQ9Ik0xNyA4bC0zIDMgMyAzIi8+PC9zdmc+',     // 插件LOGO
    'name'               => '调试禁用插件', // 插件名称
    'description'        => '为购买页面添加JavaScript调试禁用功能', // 插件描述
    'menu'               => [],
    'hook'               => [
        'ShopJs' => 'plugin\\Tddisbug\\Hook', // 使用 ShopJs 钩子
    ],
    'form_fields'        => [
        [
            'id' => 'status',
            'name' => '开启状态',
            'required' => true,
            'type' => 'radio',
            'data' => [
                [
                    'name' => '关闭',
                    'value' => 0,
                ],
                [
                    'name' => '开启',
                    'value' => 1,
                ]
            ],
        ],
        [
            'id' => 'debug_level',
            'name' => '调试禁用级别',
            'required' => true,
            'type' => 'select',
            'data' => [
                [
                    'name' => '禁用控制台',
                    'value' => 'console',
                ],
                [
                    'name' => '禁用右键菜单',
                    'value' => 'rightclick',
                ],
                [
                    'name' => '全部禁用',
                    'value' => 'all',
                ]
            ],
        ],
    ],
]; 