(function() {
    // 初始化聊天窗口
    function initMerchantChat() {
        const baseUrl = window.location.protocol + '//' + window.location.host;
        
        // 获取当前页面的商家信息
        let shopName = '';
        let merchantId = '';
        
        // 1. 从 nickname 元素获取商家名称（新增，优先级最高）
        const nicknameElement = document.querySelector('.nickname');
        if (nicknameElement) {
            shopName = nicknameElement.textContent.trim();
        }

        // 2. 从页面标题获取商家名称
        if (!shopName) {
            const pageTitle = document.title.trim();
            if (pageTitle) {
                const index = pageTitle.indexOf('的小店');
                if (index !== -1) {
                    shopName = pageTitle.substring(0, index);
                }
            }
        }

        // 3. 从页面元素中获取商家信息（备用方式）
        if (!shopName) {
            const shopInfoScript = document.querySelector('script[data-shop-info]');
            if (shopInfoScript) {
                try {
                    const shopInfo = JSON.parse(shopInfoScript.textContent);
                    shopName = shopInfo.shopName;
                    merchantId = shopInfo.merchantId;
                } catch (e) {
                    // 静默处理错误
                }
            }
        }
        
        // 4. 从 meta 标签获取（备用方式）
        if (!shopName) {
            const metaShopName = document.querySelector('meta[name="shop-name"]');
            if (metaShopName) {
                shopName = metaShopName.getAttribute('content');
            }
        }
        
        // 5. 从 URL 参数获取（备用方式）
        if (!shopName) {
            const urlParams = new URLSearchParams(window.location.search);
            shopName = urlParams.get('shop_name') || '';
        }

        // 如果获取不到商家信息，则不显示聊天窗口
        if (!shopName && !merchantId) {
            return;
        }

        // 构建请求参数
        const params = new URLSearchParams();
        if (shopName) params.append('shop_name', shopName);
        if (merchantId) params.append('merchant_id', merchantId);

        const requestUrl = baseUrl + '/plugin/MerchantChat/api/fetchData?' + params.toString();

        // 发送请求获取聊天配置
        fetch(requestUrl, {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200 && data.data && data.data.status === 1 && data.data.business_id && data.data.token) {
                loadChatScript(data.data);
            }
        })
        .catch(error => {
            // 静默处理错误
        });
    }

    // 加载聊天脚本
    function loadChatScript(config) {
        // 创建聊天配置对象
        const chatConfig = document.createElement('script');
        chatConfig.textContent = `var ymwl={visiter_id:"",visiter_name:"",avatar:"",business_id:${config.business_id},groupid:0,token:"${config.token}"}`;
        document.head.appendChild(chatConfig);

        // 创建聊天脚本
        const scriptUrl = config.script_url || 'https://kf.y1yun.top/assets/front/ymwl_online.js';
        const chatScript = document.createElement('script');

        // 使用配置的版本号或生成时间戳
        let versionParam = '';
        if (config.script_version && config.script_version.trim() !== '') {
            versionParam = config.script_version.trim();
        } else {
            versionParam = new Date().getTime();
        }

        chatScript.src = `${scriptUrl}?v=${versionParam}`;
        document.head.appendChild(chatScript);

        // 调整聊天窗口位置（如果配置了）
        if (config.show_position) {
            // 添加CSS以修改聊天窗口位置
            const style = document.createElement('style');
            if (config.show_position === 'left') {
                style.textContent = `
                    .ymwl-container { left: 20px !important; right: auto !important; }
                    .ymwl-float-item { left: 20px !important; right: auto !important; }
                `;
            }
            document.head.appendChild(style);

            // 监听iframe加载，以便修改其中的样式和添加滑动箭头功能
            chatScript.onload = function() {
                setTimeout(function() {
                    const chatIframes = document.querySelectorAll('iframe[id^="ymwl-iframe"]');
                    chatIframes.forEach(iframe => {
                        try {
                            if (config.show_position === 'left') {
                                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                                const style = iframeDoc.createElement('style');
                                style.textContent = `
                                    .chatBox { left: 20px !important; right: auto !important; }
                                    .backTop { left: 20px !important; right: auto !important; }
                                `;
                                iframeDoc.head.appendChild(style);
                            }
                        } catch (e) {
                            // 静默处理错误
                        }
                    });
                }, 1000);

                // 添加滑动箭头功能
                setTimeout(() => {
                    addChatButtonSlideFeature();
                }, 2000);
            };
        } else {
            // 如果没有配置位置，直接添加滑动箭头功能
            chatScript.onload = function() {
                setTimeout(() => {
                    addChatButtonSlideFeature();
                }, 2000);
            };
        }
    }

    // 添加客服按钮滑动箭头功能
    function addChatButtonSlideFeature() {
        // 查找客服按钮元素
        function findChatButton() {
            // 查找包含"在线咨询"和"客服交谈"的客服按钮
            const chatButtons = document.querySelectorAll('a');
            for (let button of chatButtons) {
                const iconText = button.querySelector('.icon-text');
                const btnText = button.querySelector('.btn-text');
                if (iconText && btnText &&
                    iconText.textContent.includes('在线咨询') &&
                    btnText.textContent.includes('客服交谈')) {
                    return button;
                }
            }

            // 备用查找方式：查找包含chat1.png图片的按钮
            const imgElements = document.querySelectorAll('img[src*="chat1.png"]');
            for (let img of imgElements) {
                const parentLink = img.closest('a');
                if (parentLink) {
                    return parentLink;
                }
            }

            return null;
        }

        // 等待客服按钮出现
        let retryCount = 0;
        const maxRetries = 10;

        function tryAddSlideFeature() {
            const chatButton = findChatButton();

            if (chatButton) {
                createSlideArrow(chatButton);
            } else if (retryCount < maxRetries) {
                retryCount++;
                setTimeout(tryAddSlideFeature, 1000);
            }
        }

        tryAddSlideFeature();
    }

    // 创建滑动箭头
    function createSlideArrow(chatButton) {
        // 检查是否已经添加过箭头
        if (chatButton.parentElement.querySelector('.chat-slide-arrow')) {
            return;
        }

        // 创建容器包装客服按钮
        const container = document.createElement('div');
        container.className = 'chat-button-container';
        container.style.cssText = `
            position: fixed;
            z-index: 9999;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            border-radius: 50px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
        `;

        // 获取客服按钮的位置信息
        const buttonRect = chatButton.getBoundingClientRect();
        const isRightSide = buttonRect.right > window.innerWidth / 2;

        // 设置容器位置
        if (isRightSide) {
            container.style.right = '20px';
        } else {
            container.style.left = '20px';
        }
        container.style.bottom = '20px';

        // 将客服按钮移动到容器中
        chatButton.parentNode.insertBefore(container, chatButton);
        container.appendChild(chatButton);

        // 创建箭头按钮
        const arrow = document.createElement('div');
        arrow.className = 'chat-slide-arrow';
        // 初始状态：显示时箭头向左，表示点击后收起
        arrow.innerHTML = '◀';
        arrow.style.cssText = `
            position: absolute;
            top: 50%;
            ${isRightSide ? 'left: -35px;' : 'right: -35px;'}
            transform: translateY(-50%);
            width: 30px;
            height: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 12px;
            box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
            user-select: none;
            z-index: 10000;
        `;

        container.appendChild(arrow);

        // 从localStorage读取状态
        const storageKey = 'chatButtonHidden';
        let isHidden = localStorage.getItem(storageKey) === 'true';

        // 应用初始状态
        if (isHidden) {
            if (isRightSide) {
                container.style.transform = 'translateX(calc(100% + 10px))';
            } else {
                container.style.transform = 'translateX(calc(-100% - 10px))';
            }
            // 隐藏时箭头向右，表示点击后展开
            arrow.innerHTML = '▶';
        }

        // 箭头点击事件
        arrow.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            isHidden = !isHidden;
            localStorage.setItem(storageKey, isHidden.toString());

            if (isHidden) {
                // 隐藏客服按钮
                if (isRightSide) {
                    container.style.transform = 'translateX(calc(100% + 10px))';
                } else {
                    container.style.transform = 'translateX(calc(-100% - 10px))';
                }
                // 隐藏时箭头向右，表示点击后展开
                arrow.innerHTML = '▶';
            } else {
                // 显示客服按钮
                container.style.transform = 'translateX(0)';
                // 显示时箭头向左，表示点击后收起
                arrow.innerHTML = '◀';
            }
        });

        // 箭头悬停效果
        arrow.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-50%) scale(1.1)';
            this.style.boxShadow = '0 4px 15px rgba(102, 126, 234, 0.5)';
        });

        arrow.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(-50%) scale(1)';
            this.style.boxShadow = '0 2px 10px rgba(102, 126, 234, 0.3)';
        });

        // 移动端适配
        if (window.innerWidth <= 768) {
            container.style.bottom = '15px';
            if (isRightSide) {
                container.style.right = '15px';
            } else {
                container.style.left = '15px';
            }
            arrow.style.width = '28px';
            arrow.style.height = '28px';
            arrow.style.fontSize = '11px';
        }


    }

    // 页面加载完成后执行初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initMerchantChat);
    } else {
        initMerchantChat();
    }
})();