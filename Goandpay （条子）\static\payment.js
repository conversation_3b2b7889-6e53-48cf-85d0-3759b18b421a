(function () {
    // 获取店铺信息
    function getShopInfo() {
        let shopName = '';
        let merchantId = '';
        
        // 从用户模型获取店铺信息（通过ShopSettingBefore和ShopSettingAfter钩子函数提供）
        const shopInfoScript = document.querySelector('script[data-shop-info]');
        if (shopInfoScript) {
            try {
                const shopInfo = JSON.parse(shopInfoScript.textContent);
                if (shopInfo.shopName) {
                    shopName = shopInfo.shopName;
                }
                if (shopInfo.merchantId) {
                    merchantId = shopInfo.merchantId;
                }
            } catch (e) {
                // 解析商家信息失败
            }
        }
        
        // 如果无法通过钩子函数获取，则尝试其他方式
        if (!shopName) {
            // 1. 从 nickname 元素获取商家名称
            const nicknameElement = document.querySelector('.nickname');
            if (nicknameElement) {
                shopName = nicknameElement.textContent.trim();
            }
        }

        // 2. 从带有店铺名称class的元素获取
        if (!shopName) {
            const shopNameElements = document.querySelectorAll('.shop-name, .store-name, .merchant-name');
            for (const element of shopNameElements) {
                const text = element.textContent.trim();
                if (text) {
                    shopName = text;
                    break;
                }
            }
        }

        // 3. 从页面标题获取商家名称
        if (!shopName) {
            const pageTitle = document.title.trim();
            if (pageTitle) {
                const index = pageTitle.indexOf('的小店');
                if (index !== -1) {
                    shopName = pageTitle.substring(0, index);
                }
            }
        }

        // 4. 从商家ID属性元素获取
        if (!merchantId) {
            const merchantIdElement = document.querySelector('[data-merchant-id]');
            if (merchantIdElement) {
                merchantId = merchantIdElement.getAttribute('data-merchant-id');
            }
        }
        
        // 5. 从 meta 标签获取
        if (!shopName) {
            const metaTags = [
                document.querySelector('meta[name="shop-name"]'),
                document.querySelector('meta[property="og:site_name"]'),
                document.querySelector('meta[name="author"]')
            ];
            
            for (const tag of metaTags) {
                if (tag && tag.getAttribute('content')) {
                    shopName = tag.getAttribute('content');
                    break;
                }
            }
        }
        
        if (!merchantId) {
            const merchantIdMeta = document.querySelector('meta[name="merchant-id"]');
            if (merchantIdMeta) {
                merchantId = merchantIdMeta.getAttribute('content');
            }
        }
        
        // 6. 从 URL 参数获取（备用方式）
        if (!shopName || !merchantId) {
            const urlParams = new URLSearchParams(window.location.search);
            if (!shopName) {
                const urlShopName = urlParams.get('shop_name');
                if (urlShopName) shopName = urlShopName;
            }
            if (!merchantId) {
                const urlMerchantId = urlParams.get('merchant_id');
                if (urlMerchantId) merchantId = urlMerchantId;
            }
        }

        return { shopName, merchantId };
    }

    // 查找支付按钮（分别查找"去支付"和"立即购买"按钮）
    function findPayButtons() {
        // 获取所有按钮
        const allButtons = document.querySelectorAll('button, .arco-btn, .el-button, .van-button, .btn');
        
        let goPayButton = null;
        let buyNowButton = null;
        
        // 查找"去支付"按钮
        for (const button of allButtons) {
            if (button.textContent && button.textContent.trim().includes('去支付')) {
                goPayButton = button;
                break;
            }
        }
        
        // 查找"立即购买"按钮
        for (const button of allButtons) {
            const buttonClass = button.className || '';
            const buttonText = button.textContent || '';
            
            if ((buttonClass.includes('van-button') && buttonClass.includes('buy-this')) || 
                (buttonText.trim().includes('立即购买'))) {
                buyNowButton = button;
                break;
            }
        }
        
        return { goPayButton, buyNowButton };
    }

    // 检查联系方式是否已填写
    function checkContactInfo() {
        // 检查可能的联系方式输入框
        const contactInputs = [
            // 手机号输入框
            document.querySelector('input[placeholder*="手机"]'),
            document.querySelector('input[placeholder*="电话"]'),
            document.querySelector('input[placeholder*="联系方式"]'),
            document.querySelector('input[name="mobile"]'),
            document.querySelector('input[name="phone"]'),
            document.querySelector('input[name="contact"]'),
            
            // 邮箱输入框
            document.querySelector('input[placeholder*="邮箱"]'),
            document.querySelector('input[name="email"]'),
            
            // 微信输入框
            document.querySelector('input[placeholder*="微信"]'),
            document.querySelector('input[name="wechat"]'),
            
            // 通用联系方式
            document.querySelector('input[placeholder*="联系"]'),
            document.querySelector('textarea[placeholder*="联系"]'),
            
            // 添加对特定UI框架的支持
            document.querySelector('.arco-input[placeholder*="联系方式"]'),
            document.querySelector('.contact_box .arco-input'),
            document.querySelector('.contact_box input'),
            document.querySelector('.arco-input-wrapper input')
        ];
        
        // 检查所有可能的联系方式输入框
        for (const input of contactInputs) {
            if (input && input.value.trim()) {
                return true; // 找到有内容的联系方式
            }
        }
        
        return false; // 未找到有内容的联系方式
    }
    
    // 显示联系方式提示
    function showContactPrompt() {
        // 如果页面上已经有提示，不再显示
        if (document.querySelector('.goandpay-contact-prompt')) {
            return;
        }
        
        // 创建提示元素
        const promptElement = document.createElement('div');
        promptElement.className = 'goandpay-contact-prompt';
        
        // 设置提示内容，使用带图片的提示
        promptElement.innerHTML = `
            <div class="goandpay-contact-prompt-container">
                <div class="goandpay-contact-prompt-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 1024 1024" fill="none" stroke="#e74c3c" stroke-width="2">
                        <path fill="#e74c3c" d="M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z"></path>
                    </svg>
                </div>
                <div class="goandpay-contact-prompt-text">请输入联系方式</div>
                <button class="goandpay-contact-prompt-close">×</button>
            </div>
        `;
        
        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .goandpay-contact-prompt {
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                z-index: 100000;
                max-width: 90%;
                animation: goandpay-contact-slide-down 0.3s ease;
            }
            
            @keyframes goandpay-contact-slide-down {
                from { transform: translate(-50%, -20px); opacity: 0; }
                to { transform: translate(-50%, 0); opacity: 1; }
            }
            
            .goandpay-contact-prompt-container {
                background-color: #fff;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                display: flex;
                align-items: center;
                padding: 12px 16px;
                border-left: 4px solid #e74c3c;
            }
            
            .goandpay-contact-prompt-icon {
                margin-right: 10px;
                flex-shrink: 0;
            }
            
            .goandpay-contact-prompt-text {
                font-size: 14px;
                color: #333;
                margin-right: 12px;
                font-weight: 500;
            }
            
            .goandpay-contact-prompt-close {
                background: none;
                border: none;
                color: #999;
                font-size: 20px;
                cursor: pointer;
                padding: 0;
                margin-left: auto;
                line-height: 1;
                width: 24px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .goandpay-contact-prompt-close:hover {
                color: #666;
            }
            
            /* 为空输入框添加图标提示 */
            .arco-input[placeholder*="联系方式"]:placeholder-shown {
                background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='18' height='18' viewBox='0 0 1024 1024'%3E%3Cpath fill='%23e74c3c' d='M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z'%3E%3C/path%3E%3C/svg%3E");
                background-repeat: no-repeat;
                background-position: right 8px center;
                padding-right: 30px;
            }
            
            /* 添加额外的选择器以确保覆盖更多情况 */
            .contact_box .arco-input:placeholder-shown,
            input[placeholder*="联系方式"]:placeholder-shown {
                background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='18' height='18' viewBox='0 0 1024 1024'%3E%3Cpath fill='%23e74c3c' d='M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.21334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z'%3E%3C/path%3E%3C/svg%3E");
                background-repeat: no-repeat;
                background-position: right 8px center;
                padding-right: 30px;
            }
        `;
        
        document.head.appendChild(style);
        document.body.appendChild(promptElement);
        
        // 添加关闭按钮事件
        const closeButton = promptElement.querySelector('.goandpay-contact-prompt-close');
        if (closeButton) {
            closeButton.addEventListener('click', function() {
                promptElement.remove();
            });
        }
        
        // 5秒后自动关闭
        setTimeout(() => {
            if (document.body.contains(promptElement)) {
                promptElement.remove();
            }
        }, 5000);
        
        // 为所有联系方式输入框添加样式和监听事件
        const contactInputs = document.querySelectorAll('input[placeholder*="联系方式"], .contact_box .arco-input, .arco-input[placeholder*="联系方式"]');
        
        contactInputs.forEach(input => {
            // 添加输入事件监听，当输入内容时移除图标
            input.addEventListener('input', function() {
                if (this.value.trim() !== '') {
                    this.style.backgroundImage = 'none';
                } else {
                    this.style.backgroundImage = 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'18\' height=\'18\' viewBox=\'0 0 1024 1024\'%3E%3Cpath fill=\'%23e74c3c\' d=\'M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\'%3E%3C/path%3E%3C/svg%3E")';
                }
            });
            
            // 初始时应用样式
            if (!input.value.trim()) {
                input.style.backgroundImage = 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'18\' height=\'18\' viewBox=\'0 0 1024 1024\'%3E%3Cpath fill=\'%23e74c3c\' d=\'M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z\'%3E%3C/path%3E%3C/svg%3E")';
                input.style.backgroundRepeat = 'no-repeat';
                input.style.backgroundPosition = 'right 8px center';
                input.style.paddingRight = '30px';
            }
        });
    }

    // 是否应该显示弹窗（根据频率设置）
    function shouldShowNotice(frequency) {
        // 获取本地存储
        const storageKey = 'goandpay_last_shown';
        const lastShownData = localStorage.getItem(storageKey);
        const now = new Date();
        
        // 如果之前没有显示过，则显示
        if (!lastShownData) {
            // 保存当前时间
            saveLastShownTime();
            return true;
        }
        
        // 解析上次显示时间
        try {
            const lastShown = JSON.parse(lastShownData);
            const lastDate = new Date(lastShown.time);
            
            switch (frequency) {
                case 'once': // 仅显示一次
                    return false;
                
                case 'login': // 每次访问都显示
                    saveLastShownTime();
                    return true;
                
                case 'daily': // 每天显示一次
                    if (now.getDate() !== lastDate.getDate() || 
                        now.getMonth() !== lastDate.getMonth() || 
                        now.getFullYear() !== lastDate.getFullYear()) {
                        saveLastShownTime();
                        return true;
                    }
                    return false;
                
                case 'weekly': // 每周显示一次
                    // 计算上次显示时间到现在的天数
                    const dayDiff = Math.floor((now - lastDate) / (1000 * 60 * 60 * 24));
                    if (dayDiff >= 7) {
                        saveLastShownTime();
                        return true;
                    }
                    return false;
                
                default:
                    saveLastShownTime();
                    return true;
            }
        } catch (e) {
            // 解析上次显示时间失败
            saveLastShownTime();
            return true;
        }
    }
    
    // 保存最后显示时间
    function saveLastShownTime() {
        const storageKey = 'goandpay_last_shown';
        localStorage.setItem(storageKey, JSON.stringify({ 
            time: new Date().toISOString() 
        }));
    }

    // 添加新的支付按钮（适配PC端和移动端）
    function addNewPayButton(originalButton, config) {
        if (!originalButton || !config || !config.payment_notice) return;
        
        // 检查是否已经添加了新按钮
        if (document.querySelector('.goandpay-new-button')) {
            return;
        }
        
        // 根据频率决定是否显示
        if (!shouldShowNotice(config.frequency)) {
            return;
        }
        
        // 创建新按钮，与原按钮样式相同
        const newButton = document.createElement('button');
        
        // 判断是否为移动端按钮
        const isMobileButton = originalButton.className && 
                               (originalButton.className.includes('van-button') || 
                                originalButton.textContent.trim().includes('立即购买'));
        
        // 设置按钮文本
        newButton.textContent = isMobileButton ? '立即购买' : '去支付';
        
        // 设置类名，保留原按钮的类名
        newButton.className = originalButton.className + ' goandpay-new-button';
        
        // 复制原按钮的样式
        const computedStyle = window.getComputedStyle(originalButton);
        newButton.style.cssText = computedStyle.cssText;
        
        // 如果是移动端按钮，还需要复制内部结构（vant按钮有特殊结构）
        if (isMobileButton) {
            const originalHTML = originalButton.innerHTML;
            if (originalHTML.includes('van-button__content')) {
                // 使用类似的HTML结构
                newButton.innerHTML = `<div class="van-button__content"><span class="van-button__text">立即购买</span></div>`;
            }
        } else {
            // PC端按钮样式调整
            newButton.style.backgroundColor = '#4080ff';
            newButton.style.color = 'white';
            newButton.style.border = 'none';
            newButton.style.borderRadius = '6px';
            newButton.style.padding = '8px 16px';
            newButton.style.cursor = 'pointer';
            newButton.style.fontSize = '14px';
            newButton.style.margin = '0 0 10px 0';
            newButton.style.width = computedStyle.width;
            newButton.style.height = computedStyle.height;
        }
        
        // 添加点击事件
        newButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // 检查联系方式是否已填写
            if (!checkContactInfo()) {
                showContactPrompt();
                return; // 如果联系方式未填写，不继续执行支付流程
            }
            
            // 显示配置的内容（如果有）
            if (config.payment_notice) {
                // 创建内容弹窗
                const notification = document.createElement('div');
                notification.className = 'goandpay-notification';
                
                // 获取倒计时时间
                let countdown = parseInt(config.close_delay) || 0;
                
                notification.innerHTML = `
                    <div class="goandpay-notice-container">
                        <div class="goandpay-notice-header">
                            <span class="goandpay-notice-title">${config.notice_title || '支付前提示'}</span>
                            <div class="goandpay-notice-line"></div>
                        </div>
                        <div class="goandpay-notice-content">${config.payment_notice}</div>
                        <div class="goandpay-notice-footer">
                            <div class="goandpay-countdown-area">
                                ${countdown > 0 ? 
                                `<div class="goandpay-countdown"><i class="goandpay-timer-icon"></i>${countdown}秒后可勾选</div>` : 
                                `<div class="goandpay-checkbox-container">
                                    <input type="checkbox" id="goandpay-agree-checkbox" class="goandpay-agree-checkbox">
                                    <label for="goandpay-agree-checkbox">我已阅读并同意上述内容</label>
                                </div>`}
                            </div>
                            <button class="goandpay-submit-btn" disabled>同意支付</button>
                        </div>
                    </div>
                `;
                
                // 添加样式，确保在移动端也有良好的显示效果
                const style = document.createElement('style');
                style.textContent = `
                    .goandpay-notification {
                        position: fixed;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background-color: rgba(0, 0, 0, 0.5);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        z-index: 99999;
                        animation: goandpay-fade-in 0.3s ease;
                    }
                    @keyframes goandpay-fade-in {
                        from { opacity: 0; }
                        to { opacity: 1; }
                    }
                    .goandpay-notice-container {
                        background: white;
                        border-radius: 12px;
                        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
                        width: 85%;
                        max-width: 420px;
                        overflow: hidden;
                        display: flex;
                        flex-direction: column;
                        transform: translateY(0);
                        animation: goandpay-slide-up 0.3s ease;
                    }
                    @keyframes goandpay-slide-up {
                        from { transform: translateY(30px); opacity: 0.8; }
                        to { transform: translateY(0); opacity: 1; }
                    }
                    .goandpay-notice-header {
                        padding: 20px 24px 12px;
                        position: relative;
                    }
                    .goandpay-notice-title {
                        font-size: 18px;
                        font-weight: 600;
                        color: #333;
                        display: block;
                    }
                    .goandpay-notice-line {
                        height: 3px;
                        width: 40px;
                        background: linear-gradient(90deg, #4080ff, #6095ff);
                        border-radius: 3px;
                        margin-top: 8px;
                    }
                    .goandpay-notice-content {
                        padding: 16px 24px;
                        color: #333;
                        line-height: 1.6;
                        min-height: 60px;
                        max-height: 60vh;
                        overflow-y: auto;
                        flex: 1;
                        font-size: 14px;
                        border-top: 1px solid #f5f5f5;
                        border-bottom: 1px solid #f5f5f5;
                    }
                    /* 富文本内容样式 - 与编辑器样式一致 */
                    .goandpay-notice-content p {
                        margin: 8px 0;
                        line-height: 1.5;
                        font-size: 14px;
                    }
                    .goandpay-notice-content img {
                        max-width: 100%;
                        height: auto;
                        border-radius: 6px;
                    }
                    .goandpay-notice-content a {
                        color: #4080ff;
                        text-decoration: none;
                    }
                    .goandpay-notice-content a:hover {
                        text-decoration: underline;
                    }
                    .goandpay-notice-content blockquote {
                        padding: 12px 16px;
                        margin: 12px 0;
                        border-left: 4px solid #e2e2e2;
                        background-color: #f9f9f9;
                        border-radius: 0 6px 6px 0;
                    }
                    .goandpay-notice-content h1, 
                    .goandpay-notice-content h2, 
                    .goandpay-notice-content h3, 
                    .goandpay-notice-content h4, 
                    .goandpay-notice-content h5, 
                    .goandpay-notice-content h6 {
                        margin: 16px 0 10px;
                        font-weight: bold;
                        line-height: 1.3;
                    }
                    .goandpay-notice-content h1 {
                        font-size: 18px;
                    }
                    .goandpay-notice-content h2 {
                        font-size: 16px;
                    }
                    .goandpay-notice-content h3 {
                        font-size: 15px;
                    }
                    .goandpay-notice-content table {
                        border-collapse: collapse;
                        margin: 12px 0;
                        width: 100%;
                        border-radius: 6px;
                        overflow: hidden;
                    }
                    .goandpay-notice-content table td,
                    .goandpay-notice-content table th {
                        border: 1px solid #eee;
                        padding: 10px;
                    }
                    .goandpay-notice-content table th {
                        background-color: #f5f7fa;
                    }
                    .goandpay-notice-content ul, 
                    .goandpay-notice-content ol {
                        padding-left: 20px;
                        margin: 10px 0;
                    }
                    .goandpay-notice-content code {
                        background-color: #f0f0f0;
                        padding: 2px 6px;
                        border-radius: 4px;
                        font-family: monospace;
                        font-size: 13px;
                    }
                    .goandpay-notice-footer {
                        padding: 16px 24px 20px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        flex-wrap: wrap;
                    }
                    /* 移动端适配 */
                    @media screen and (max-width: 480px) {
                        .goandpay-notice-container {
                            width: 90%;
                            max-width: 360px;
                        }
                        .goandpay-notice-content {
                            font-size: 13px; /* 移动端字体更小 */
                            line-height: 1.4;
                            padding: 15px;
                            word-break: break-word;
                            text-align: left;
                            text-justify: inter-word;
                        }
                        .goandpay-notice-content p {
                            margin: 6px 0;
                            line-height: 1.4;
                            font-size: 13px;
                        }
                        .goandpay-notice-content h1 {
                            font-size: 16px;
                        }
                        .goandpay-notice-content h2 {
                            font-size: 15px;
                        }
                        .goandpay-notice-content h3 {
                            font-size: 14px;
                        }
                        .goandpay-notice-header {
                            padding: 16px 15px 10px;
                        }
                        .goandpay-notice-title {
                            font-size: 16px;
                        }
                        .goandpay-notice-footer {
                            flex-direction: column;
                            gap: 10px;
                            align-items: flex-start;
                            padding: 12px 15px 16px;
                        }
                        .goandpay-submit-btn {
                            width: 100%;
                            margin-top: 10px;
                            height: 40px;
                            font-size: 15px;
                        }
                        .goandpay-checkbox-container {
                            margin-bottom: 5px;
                        }
                        .goandpay-agree-checkbox + label {
                            font-size: 13px;
                        }
                    }
                    .goandpay-countdown {
                        color: #666;
                        font-size: 14px;
                        display: flex;
                        align-items: center;
                    }
                    .goandpay-timer-icon {
                        display: inline-block;
                        width: 16px;
                        height: 16px;
                        margin-right: 6px;
                        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666'%3E%3Cpath d='M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10 10-4.5 10-10S17.5 2 12 2zm0 18c-4.4 0-8-3.6-8-8s3.6-8 8-8 8 3.6 8 8-3.6 8-8 8zm.5-13H11v6l5.2 3.2.8-1.3-4.5-2.7V7z'/%3E%3C/svg%3E") no-repeat center center;
                        background-size: contain;
                    }
                    .goandpay-submit-btn {
                        background: linear-gradient(135deg, #4080ff, #3070ee);
                        color: white;
                        border: none;
                        border-radius: 6px;
                        padding: 10px 20px;
                        cursor: pointer;
                        font-size: 15px;
                        transition: all 0.2s ease;
                        font-weight: 500;
                        box-shadow: 0 2px 8px rgba(64, 128, 255, 0.3);
                    }
                    .goandpay-submit-btn:not([disabled]):hover {
                        background: linear-gradient(135deg, #3070ee, #2060dd);
                        transform: translateY(-1px);
                        box-shadow: 0 4px 12px rgba(64, 128, 255, 0.4);
                    }
                    .goandpay-submit-btn[disabled] {
                        background: linear-gradient(135deg, #a0bfff, #94b5f8);
                        cursor: not-allowed;
                        box-shadow: none;
                    }
                    .goandpay-checkbox-container {
                        display: flex;
                        align-items: center;
                        cursor: pointer;
                    }
                    .goandpay-agree-checkbox {
                        appearance: none;
                        -webkit-appearance: none;
                        width: 18px;
                        height: 18px;
                        border: 2px solid #ddd;
                        border-radius: 4px;
                        margin-right: 8px;
                        position: relative;
                        cursor: pointer;
                        transition: all 0.2s;
                    }
                    .goandpay-agree-checkbox:checked {
                        background-color: #4080ff;
                        border-color: #4080ff;
                    }
                    .goandpay-agree-checkbox:checked:after {
                        content: '';
                        position: absolute;
                        left: 5px;
                        top: 2px;
                        width: 5px;
                        height: 10px;
                        border: solid white;
                        border-width: 0 2px 2px 0;
                        transform: rotate(45deg);
                    }
                    .goandpay-agree-checkbox:focus {
                        outline: none;
                        box-shadow: 0 0 0 2px rgba(64, 128, 255, 0.2);
                    }
                    .goandpay-agree-checkbox + label {
                        font-size: 14px;
                        color: #333;
                        cursor: pointer;
                    }
                `;
                document.head.appendChild(style);
                document.body.appendChild(notification);
                
                // 获取立即前往支付按钮
                const submitBtn = notification.querySelector('.goandpay-submit-btn');
                
                // 如果倒计时大于0，则禁用按钮并开始倒计时
                if (countdown > 0) {
                    const countdownInterval = setInterval(() => {
                        countdown--;
                        const countdownEl = notification.querySelector('.goandpay-countdown');
                        if (countdownEl) {
                            countdownEl.textContent = `${countdown}秒后可勾选`;
                        }
                        
                        if (countdown <= 0) {
                            clearInterval(countdownInterval);
                            // 显示勾选框
                            const countdownArea = notification.querySelector('.goandpay-countdown-area');
                            if (countdownArea) {
                                countdownArea.innerHTML = `
                                <div class="goandpay-checkbox-container">
                                    <input type="checkbox" id="goandpay-agree-checkbox" class="goandpay-agree-checkbox">
                                    <label for="goandpay-agree-checkbox">我已阅读并同意上述内容</label>
                                </div>`;
                                
                                // 添加勾选框事件
                                const checkbox = notification.querySelector('#goandpay-agree-checkbox');
                                if (checkbox) {
                                    checkbox.addEventListener('change', function() {
                                        submitBtn.disabled = !this.checked;
                                    });
                                }
                            }
                        }
                    }, 1000);
                } else {
                    // 如果没有倒计时，直接添加勾选框事件
                    const checkbox = notification.querySelector('#goandpay-agree-checkbox');
                    if (checkbox) {
                        checkbox.addEventListener('change', function() {
                            submitBtn.disabled = !this.checked;
                        });
                    }
                }
                
                // 添加按钮点击事件
                if (submitBtn) {
                    submitBtn.addEventListener('click', () => {
                        notification.remove();
                        newButton.remove();
                        originalButton.click();
                    });
                }
                
                // 如果启用了语音朗读
                if (config.read_enabled && parseInt(config.read_enabled) === 1) {
                    try {
                        // 提取纯文本
                        const tempDiv = document.createElement('div');
                        tempDiv.innerHTML = config.payment_notice;
                        const plainText = tempDiv.textContent || tempDiv.innerText || '';
                        
                        if (plainText && window.speechSynthesis) {
                            const utterance = new SpeechSynthesisUtterance(plainText);
                            // 设置中文语音
                            utterance.lang = 'zh-CN';
                            utterance.rate = 1.0; // 语速
                            window.speechSynthesis.speak(utterance);
                            
                            // 弹窗关闭时停止语音
                            const stopSpeech = () => {
                                window.speechSynthesis.cancel();
                            };
                            submitBtn.addEventListener('click', stopSpeech);
                        }
                    } catch (e) {
                        // 语音朗读失败
                    }
                }
            } else {
                // 如果没有配置内容，直接触发原始按钮
                newButton.remove();
                originalButton.click();
            }
        });
        
        // 将新按钮添加到原按钮之前
        originalButton.parentNode.insertBefore(newButton, originalButton);
        
        // 隐藏原始按钮
        originalButton.style.visibility = 'hidden';
        originalButton.style.position = 'absolute';
        originalButton.style.pointerEvents = 'none';
    }

    // 获取弹窗配置
    function fetchPaymentNoticeConfig() {
        const { shopName, merchantId } = getShopInfo();
        const timestamp = new Date().getTime(); // 防止缓存
        
        // 构建API请求URL
        let apiUrl = '/plugin/Goandpay/Api/fetchData';
        
        // 添加参数
        const params = new URLSearchParams();
        if (shopName) params.append('shop_name', shopName);
        if (merchantId) params.append('merchant_id', merchantId);
        params.append('t', timestamp);
        
        // 组合最终URL
        apiUrl = apiUrl + '?' + params.toString();
        
        // 发送请求
        return fetch(apiUrl)
            .then(response => response.json())
            .then(data => {
                if (data.code === 200 && data.data) {
                    // 保存配置到全局变量
                    window.paymentNoticeConfig = data.data;
                    return data.data;
                }
                return null;
            })
            .catch(error => {
                return null;
            });
    }

    // 判断当前是否在订单确认页或支付页面
    function isInOrderConfirmPage() {
        // 检查URL是否包含订单相关关键词
        const url = window.location.href.toLowerCase();
        if (url.includes('order') || url.includes('checkout') || url.includes('payment') || 
            url.includes('confirm') || url.includes('pay')) {
            return true;
        }
        
        // 检查页面标题是否包含订单相关关键词
        const title = document.title.toLowerCase();
        if (title.includes('订单') || title.includes('确认') || title.includes('支付') || 
            title.includes('结算') || title.includes('付款')) {
            return true;
        }
        
        // 检查是否有订单确认页面常见元素
        const orderElements = [
            document.querySelector('.order-confirm'),
            document.querySelector('.checkout'),
            document.querySelector('.payment'),
            document.querySelector('.order-summary'),
            document.querySelector('[data-role="order"]'),
            document.querySelector('.order-details'),
            document.querySelector('.order-info'),
            document.querySelector('订单确认')
        ];
        
        for (const el of orderElements) {
            if (el) return true;
        }
        
        // 检查是否有微信支付或支付宝支付按钮
        const paymentMethods = document.querySelectorAll('button, .button, a');
        for (const method of paymentMethods) {
            const text = method.textContent || '';
            if (text.includes('微信支付') || text.includes('支付宝') || 
                text.includes('支付方式') || text.includes('付款方式')) {
                return true;
            }
        }
        
        return false;
    }

    // 初始化函数
    function init() {
        // 获取配置
        fetchPaymentNoticeConfig().then(config => {
            if (!config || !config.status) return;
            
            // 查找页面中的支付按钮
            const { goPayButton, buyNowButton } = findPayButtons();
            const isConfirmPage = isInOrderConfirmPage();
            
            // 判断处理逻辑:
            // 1. 如果在订单确认页面且有"去支付"按钮，优先处理"去支付"按钮
            // 2. 如果在订单确认页面但没有"去支付"按钮，才处理"立即购买"按钮
            // 3. 如果不在订单确认页面，判断是否为移动端，移动端需要处理"立即购买"按钮
            
            const isMobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            
            if (goPayButton) {
                // 有"去支付"按钮时，直接处理它
                addNewPayButton(goPayButton, config);
            } else if ((isConfirmPage || isMobileDevice) && buyNowButton) {
                // 在订单确认页面或移动端设备上，处理"立即购买"按钮
                addNewPayButton(buyNowButton, config);
            }
            
            // 监听DOM变化，处理动态加载的按钮
            const observer = new MutationObserver((mutations) => {
                for (const mutation of mutations) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        // 如果页面中新增了节点，尝试再次查找支付按钮
                        if (!document.querySelector('.goandpay-new-button')) {
                            const { goPayButton, buyNowButton } = findPayButtons();
                            const currentIsConfirmPage = isInOrderConfirmPage();
                            
                            // 使用与上面相同的处理逻辑
                            if (goPayButton) {
                                addNewPayButton(goPayButton, config);
                            } else if ((currentIsConfirmPage || isMobileDevice) && buyNowButton) {
                                addNewPayButton(buyNowButton, config);
                            }
                        }
                    }
                }
            });
            
            // 开始观察整个文档的变化
            observer.observe(document.body, { childList: true, subtree: true });
        });
    }

    // 当DOM加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
})(); 