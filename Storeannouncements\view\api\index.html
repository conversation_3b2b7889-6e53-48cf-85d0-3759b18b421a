<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>店铺公告设置</title>
    <style>
        .editor-container {
            border: 1px solid #dcdfe6;
            margin-bottom: 10px;
        }
        .w-e-toolbar {
            border-bottom: 1px solid #dcdfe6 !important;
        }
        .w-e-text-container {
            height: 400px !important;
        }
        /* 编辑器内容样式 */
        .w-e-text-container [contenteditable=true] {
            color: #4a5568;
            line-height: 1.6;
            font-size: 15px;
        }
        .w-e-text-container [contenteditable=true] p {
            margin: 10px 0;
        }
        .w-e-text-container [contenteditable=true] img {
            max-width: 100%;
            height: auto;
        }
        .w-e-text-container [contenteditable=true] table {
            border-collapse: collapse;
        }
        .w-e-text-container [contenteditable=true] td,
        .w-e-text-container [contenteditable=true] th {
            border: 1px solid #ddd;
            padding: 8px;
        }
        /* 颜色块样式 */
        .color-block {
            display: inline-block;
            width: 18px;
            height: 18px;
            border-radius: 4px;
            margin-right: 6px;
            vertical-align: middle;
            border: 1px solid #dcdfe6;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        /* 响应式样式调整 */
        @media screen and (max-width: 768px) {
            .el-form-item {
                margin-bottom: 15px;
            }
            
            .el-form {
                padding: 10px;
            }
            
            .el-form-item__label {
                width: auto !important;
                text-align: left;
                float: none;
                display: block;
                margin-bottom: 8px;
                padding: 0;
            }
            
            .el-form-item__content {
                margin-left: 0 !important;
            }
            
            .el-radio-group {
                display: flex;
                flex-direction: column;
                gap: 10px;
            }
            
            .el-radio {
                margin-right: 0;
                margin-bottom: 5px;
            }
            
            .w-e-toolbar {
                flex-wrap: wrap;
            }
            
            .w-e-text-container {
                height: 300px !important;
            }
            
            .editor-container {
                margin: 0 -10px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <el-card shadow="never">
            <template #header>
                <div class="card-header">
                    <span>店铺公告设置（<span style="color: #f56c6c;">提示：店铺名称不能默认不然用不了商家8888不可行</span>）</span>
                </div>
            </template>
            <el-form :model="form" label-width="120px">
                <el-form-item label="公告开关：">
                    <el-switch v-model="form.status" :active-value="1" :inactive-value="0" />
                </el-form-item>

                <el-form-item label="公告标题：">
                    <el-input v-model="form.title" placeholder="自定义公告标题，默认为'店铺公告'" maxlength="20" show-word-limit style="max-width: 400px;" />
                </el-form-item>

                <el-form-item label="弹出频率：">
                    <el-radio-group v-model="form.frequency">
                        <el-radio label="once">仅弹一次</el-radio>
                        <el-radio label="login">每次访问</el-radio>
                        <el-radio label="daily">每天一次</el-radio>
                        <el-radio label="weekly">每周一次</el-radio>
                    </el-radio-group>
                </el-form-item>

                <el-form-item label="默认按钮颜色：">
                    <el-color-picker v-model="form.buttonColor" show-alpha></el-color-picker>
                </el-form-item>

                <el-form-item label="公告内容：">
                    <div class="editor-container">
                        <div id="editor-toolbar" style="border-bottom: 1px solid #ccc;"></div>
                        <div id="editor-text-area" style="height: 300px"></div>
                    </div>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" :loading="loading" @click="save">保存设置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
    </div>

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/dist/index.min.js"></script>
    <link href="/static/others/dist/css/style.css" rel="stylesheet">

    <script>
        const { createApp, ref, reactive, onMounted } = Vue;
        const { ElMessage } = ElementPlus;

        createApp({
            setup() {
                const loading = ref(false);
                const form = reactive({
                    status: 1,
                    frequency: 'once',
                    readEnabled: 1,
                    closeDelay: 0,
                    title: '店铺公告',
                    buttonColor: '#409eff'
                });
                let editor = null;

                onMounted(() => {
                    if (window.wangEditor) {
                        const E = window.wangEditor;
                        editor = E.createEditor({
                            selector: '#editor-text-area',
                            html: '',
                            config: {
                                placeholder: '请输入公告内容...',
                                html: true,
                                MENU_CONF: {
                                    uploadImage: {
                                        server: '/merchantApi/Upload/file',
                                        fieldName: 'file',
                                        maxFileSize: 2 * 1024 * 1024,
                                        maxNumberOfFiles: 10,
                                        allowedFileTypes: ['image/*'],
                                        meta: {
                                            // 可以添加自定义参数
                                        },
                                        headers: {
                                            // 可以添加自定义请求头
                                        },
                                        onBeforeUpload(file) {
                                            return file // 返回 false 则终止上传
                                        },
                                        onProgress(progress) {
                                            // 上传进度回调
                                        },
                                        onSuccess(file, res) {
                                            // 上传成功回调
                                        },
                                        onFailed(file, res) {
                                            ElMessage.error('图片上传失败：' + (res?.msg || '未知错误'))
                                        },
                                        onError(file, err, res) {
                                            ElMessage.error('图片上传失败：' + (res?.msg || err.message || '未知错误'))
                                        },
                                        customInsert(res, insertFn) {
                                            if (res.code === 1) {
                                                insertFn(res.data.url, res.data.original_name || '', '')
                                            } else {
                                                ElMessage.error(res.msg || '上传失败')
                                            }
                                        }
                                    }
                                }
                            }
                        });

                        const toolbar = E.createToolbar({
                            editor,
                            selector: '#editor-toolbar',
                            config: {
                                excludeKeys: [],
                                toolbarKeys: [
                                    'headerSelect',
                                    'blockquote',
                                    '|',
                                    'bold',
                                    'underline',
                                    'italic',
                                    {
                                        key: 'group-more-style',
                                        title: '更多',
                                        menuKeys: ['through', 'code', 'sup', 'sub', 'clearStyle'],
                                    },
                                    '|',
                                    'color',
                                    'bgColor',
                                    '|',
                                    'fontSize',
                                    'fontFamily',
                                    'lineHeight',
                                    '|',
                                    'bulletedList',
                                    'numberedList',
                                    'todo',
                                    {
                                        key: 'group-justify',
                                        title: '对齐',
                                        menuKeys: ['justifyLeft', 'justifyCenter', 'justifyRight', 'justifyJustify'],
                                    },
                                    '|',
                                    'insertImage',
                                    '|',
                                    'insertTable',
                                    'undo',
                                    'redo',
                                    '|',
                                    'fullScreen'
                                ]
                            }
                        });
                    } else {
                        ElMessage.error('富文本编辑器加载失败，请刷新页面重试');
                    }

                    fetchData();
                });

                // 获取数据
                const fetchData = async () => {
                    try {
                        const res = await axios.post("/plugin/Storeannouncements/api/fetchData");
                        if (res.data?.code === 200) {
                            const data = res.data.data;
                            form.status = parseInt(data.status);
                            form.frequency = data.frequency;
                            form.readEnabled = parseInt(data.read_enabled);
                            form.closeDelay = parseInt(data.close_delay) || 0;
                            form.title = data.title || '店铺公告';
                            // 将预设颜色名转换为对应的十六进制值
                            const colorMap = {
                                'blue': '#409eff',
                                'red': '#F56C6C',
                                'green': '#67C23A',
                                'orange': '#E6A23C',
                                'teal': '#009688',
                                'purple': '#9C27B0',
                                'gray': '#909399'
                            };
                            // 如果是颜色名则转换，否则直接使用（可能是十六进制或rgba）
                            const buttonColor = data.button_color || 'blue';
                            form.buttonColor = colorMap[buttonColor] || buttonColor;
                            if (editor) {
                                editor.setHtml(data.announcement || '');
                            }
                        } else {
                            ElMessage.error(res.data?.msg || '获取数据失败');
                        }
                    } catch (error) {
                        ElMessage.error('获取数据失败');
                    }
                };

                // 保存数据
                const save = async () => {
                    if (!editor) {
                        ElMessage.error('编辑器未初始化');
                        return;
                    }

                    const content = editor.getHtml();
                    if (!content.trim()) {
                        ElMessage.warning('请输入公告内容');
                        return;
                    }

                    loading.value = true;
                    try {
                        const formData = new FormData();
                        formData.append('status', form.status);
                        formData.append('frequency', form.frequency);
                        formData.append('read_enabled', form.readEnabled);
                        formData.append('close_delay', form.closeDelay);
                        formData.append('title', form.title);
                        formData.append('button_color', form.buttonColor);
                        formData.append('announcement', content);
                        
                        const res = await axios.post("/plugin/Storeannouncements/api/save", formData);
                        if (res.data?.code === 200) {
                            ElMessage.success('保存成功');
                            await fetchData();
                        } else {
                            ElMessage.error(res.data?.msg || '保存失败');
                        }
                    } catch (error) {
                        ElMessage.error('保存失败');
                    } finally {
                        loading.value = false;
                    }
                };

                return {
                    loading,
                    form,
                    save
                };
            }
        })
        .use(ElementPlus)
        .mount('#app');
    </script>
</body>
</html> 