from flask import Flask, request, jsonify
import secrets
import sqlite3
import os
import time
import hashlib
from datetime import datetime, timedelta

app = Flask(__name__)

@app.route('/')
def index():
    """服务器状态检查"""
    return jsonify({
        "status": "online", 
        "message": "许可证验证服务器正在运行",
        "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    })

# 创建数据库
def init_db():
    if not os.path.exists('licenses.db'):
        conn = sqlite3.connect('licenses.db')
        c = conn.cursor()
        c.execute('''
        CREATE TABLE licenses
        (id INTEGER PRIMARY KEY AUTOINCREMENT,
        license_key TEXT UNIQUE, 
        software_id TEXT, 
        user_id TEXT,
        device_id TEXT,
        valid_until TEXT,
        activated INTEGER DEFAULT 0,
        created_at TEXT)
        ''')
        
        c.execute('''
        CREATE TABLE access_logs
        (id INTEGER PRIMARY KEY AUTOINCREMENT,
        license_key TEXT,
        device_id TEXT,
        ip_address TEXT,
        action TEXT,
        timestamp TEXT,
        success INTEGER,
        message TEXT)
        ''')
        
        c.execute('''
        CREATE TABLE admins
        (id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE,
        password TEXT,
        email TEXT,
        created_at TEXT)
        ''')
        
        # 创建默认管理员账户 admin/admin123
        c.execute('INSERT INTO admins (username, password, email, created_at) VALUES (?, ?, ?, ?)',
                 ('admin', hashlib.sha256('admin123'.encode()).hexdigest(), '<EMAIL>', datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
        
        conn.commit()
        conn.close()

# 生成许可证密钥
def generate_license_key():
    return secrets.token_hex(16)

# 验证许可证
def verify_license(license_key, software_id, device_id):
    conn = sqlite3.connect('licenses.db')
    c = conn.cursor()
    c.execute("SELECT * FROM licenses WHERE license_key = ? AND software_id = ?", 
              (license_key, software_id))
    result = c.fetchone()
    
    if not result:
        conn.close()
        return False, "无效的许可证"
    
    # 获取字段索引
    columns = [description[0] for description in c.description]
    
    # 构建许可证数据字典
    license_data = {}
    for i, column in enumerate(columns):
        license_data[column] = result[i]
    
    # 检查许可证是否被封禁
    if license_data.get("is_banned", 0):
        ban_reason = license_data.get("ban_reason", "未知原因")
        conn.close()
        return False, f"卡密已被封禁: {ban_reason}"
    
    # 检查许可证是否已激活
    if not license_data.get("activated", 0):
        # 如果未激活且没有绑定设备，则自动激活并绑定设备
        if not license_data.get("device_id"):
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            c.execute("UPDATE licenses SET activated = 1, device_id = ?, activated_at = ? WHERE license_key = ?", 
                      (device_id, current_time, license_key))
            conn.commit()
            conn.close()
            
            # 记录激活日志
            log_access(license_key, device_id, "", "activate", True, "首次激活成功")
            
            return True, "卡密首次激活成功，已自动绑定到当前设备"
    
    # 检查许可证是否过期
    valid_until = license_data.get("valid_until")
    if valid_until:
        try:
            expiry_date = datetime.strptime(valid_until, "%Y-%m-%d")
            if expiry_date < datetime.now():
                conn.close()
                return False, "卡密已过期"
        except:
            pass  # 日期格式错误则忽略过期检查
    
    # 检查设备ID是否匹配（如果已经绑定）
    if license_data.get("device_id") and license_data.get("device_id") != device_id:
        conn.close()
        return False, "设备ID不匹配，卡密已绑定到其他设备"
    
    conn.close()
    return True, "验证成功"

# 记录访问日志
def log_access(license_key, device_id, ip_address, action, success, message=""):
    conn = sqlite3.connect('licenses.db')
    c = conn.cursor()
    c.execute("INSERT INTO access_logs (license_key, device_id, ip_address, action, timestamp, success, message) VALUES (?, ?, ?, ?, ?, ?, ?)",
              (license_key, device_id, ip_address, action, datetime.now().strftime("%Y-%m-%d %H:%M:%S"), 1 if success else 0, message))
    conn.commit()
    conn.close()

@app.route('/api/verify', methods=['POST'])
def verify():
    data = request.json
    license_key = data.get('license_key')
    software_id = data.get('software_id')
    device_id = data.get('hwid')  # 保持兼容性
    
    if not all([license_key, software_id, device_id]):
        log_access(license_key or "", device_id or "", request.remote_addr, "verify", False, "缺少必要参数")
        return jsonify({"status": "error", "message": "缺少必要参数"}), 400
    
    is_valid, message = verify_license(license_key, software_id, device_id)
    log_access(license_key, device_id, request.remote_addr, "verify", is_valid, message)
    
    return jsonify({
        "status": "success" if is_valid else "error",
        "message": message,
        "valid": is_valid
    })

@app.route('/api/register', methods=['POST'])
def register_license():
    # 这里需要管理员身份验证，简化起见省略
    data = request.json
    software_id = data.get('software_id')
    user_id = data.get('user_id')
    valid_days = data.get('valid_days', 30)
    
    if not all([software_id, user_id]):
        return jsonify({"status": "error", "message": "缺少必要参数"}), 400
    
    license_key = generate_license_key()
    valid_until = (datetime.now() + timedelta(days=valid_days)).strftime("%Y-%m-%d")
    
    conn = sqlite3.connect('licenses.db')
    c = conn.cursor()
    c.execute("INSERT INTO licenses (license_key, software_id, user_id, device_id, valid_until, activated, created_at) VALUES (?, ?, ?, ?, ?, ?, ?)",
              (license_key, software_id, user_id, "", valid_until, 0, datetime.now().strftime("%Y-%m-%d")))
    conn.commit()
    conn.close()
    
    # 记录注册日志
    log_access(license_key, "", "", "register", True, f"通过API创建，有效期{valid_days}天")
    
    return jsonify({
        "status": "success",
        "license_key": license_key,
        "valid_until": valid_until
    })

if __name__ == '__main__':
    init_db()
    app.run(host='0.0.0.0', port=5000) 