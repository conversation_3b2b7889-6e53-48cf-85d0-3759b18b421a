#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ChromeDriver问题修复工具
自动检测Chrome版本并下载对应的ChromeDriver
"""

import os
import sys
import subprocess
import requests
import zipfile
import platform
from pathlib import Path

def get_chrome_version():
    """获取Chrome浏览器版本"""
    try:
        if platform.system() == "Windows":
            # Windows系统
            import winreg
            try:
                # 尝试从注册表获取版本
                key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\Google\Chrome\BLBeacon")
                version, _ = winreg.QueryValueEx(key, "version")
                winreg.CloseKey(key)
                return version
            except:
                # 尝试从程序文件获取版本
                chrome_path = r"C:\Program Files\Google\Chrome\Application\chrome.exe"
                if os.path.exists(chrome_path):
                    result = subprocess.run([chrome_path, "--version"], capture_output=True, text=True)
                    if result.returncode == 0:
                        version = result.stdout.strip().split()[-1]
                        return version
        
        elif platform.system() == "Darwin":
            # macOS系统
            result = subprocess.run(["/Applications/Google Chrome.app/Contents/MacOS/Google Chrome", "--version"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                version = result.stdout.strip().split()[-1]
                return version
        
        elif platform.system() == "Linux":
            # Linux系统
            result = subprocess.run(["google-chrome", "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                version = result.stdout.strip().split()[-1]
                return version
        
        return None
    except Exception as e:
        print(f"[失败] 获取Chrome版本失败: {e}")
        return None

def get_chromedriver_download_url(chrome_version):
    """获取ChromeDriver下载URL"""
    try:
        # 获取主版本号
        major_version = chrome_version.split('.')[0]
        
        # Chrome 115+使用新的API
        if int(major_version) >= 115:
            api_url = "https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json"
            response = requests.get(api_url, timeout=30)
            data = response.json()
            
            # 查找匹配的版本
            for version_info in data['versions']:
                if version_info['version'].startswith(major_version):
                    downloads = version_info.get('downloads', {})
                    chromedriver_downloads = downloads.get('chromedriver', [])
                    
                    # 根据平台选择下载链接
                    platform_name = get_platform_name()
                    for download in chromedriver_downloads:
                        if download['platform'] == platform_name:
                            return download['url'], version_info['version']
        
        # 旧版本API（Chrome < 115）
        else:
            api_url = f"https://chromedriver.storage.googleapis.com/LATEST_RELEASE_{major_version}"
            response = requests.get(api_url, timeout=30)
            driver_version = response.text.strip()
            
            platform_name = get_platform_name_old()
            download_url = f"https://chromedriver.storage.googleapis.com/{driver_version}/chromedriver_{platform_name}.zip"
            return download_url, driver_version
        
        return None, None
    except Exception as e:
        print(f"[失败] 获取ChromeDriver下载链接失败: {e}")
        return None, None

def get_platform_name():
    """获取平台名称（新API格式）"""
    system = platform.system()
    machine = platform.machine().lower()
    
    if system == "Windows":
        if "64" in machine or machine == "amd64":
            return "win64"
        else:
            return "win32"
    elif system == "Darwin":
        if "arm" in machine or "m1" in machine:
            return "mac-arm64"
        else:
            return "mac-x64"
    elif system == "Linux":
        return "linux64"
    else:
        return "linux64"

def get_platform_name_old():
    """获取平台名称（旧API格式）"""
    system = platform.system()
    machine = platform.machine().lower()
    
    if system == "Windows":
        if "64" in machine or machine == "amd64":
            return "win32"  # 注意：旧API中64位也是win32
        else:
            return "win32"
    elif system == "Darwin":
        return "mac64"
    elif system == "Linux":
        return "linux64"
    else:
        return "linux64"

def download_chromedriver(download_url, version):
    """下载ChromeDriver"""
    try:
        print(f"[下载] 正在下载ChromeDriver {version}...")
        
        response = requests.get(download_url, timeout=300)
        response.raise_for_status()
        
        # 创建临时目录
        temp_dir = Path("temp_chromedriver")
        temp_dir.mkdir(exist_ok=True)
        
        # 保存zip文件
        zip_path = temp_dir / "chromedriver.zip"
        with open(zip_path, 'wb') as f:
            f.write(response.content)
        
        print(f"[成功] ChromeDriver下载完成")
        
        # 解压文件
        print("📦 正在解压ChromeDriver...")
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(temp_dir)
        
        # 查找chromedriver可执行文件
        chromedriver_exe = None
        for root, dirs, files in os.walk(temp_dir):
            for file in files:
                if file.startswith("chromedriver") and (file.endswith(".exe") or platform.system() != "Windows"):
                    chromedriver_exe = Path(root) / file
                    break
            if chromedriver_exe:
                break
        
        if not chromedriver_exe:
            raise Exception("未找到chromedriver可执行文件")
        
        # 移动到当前目录
        target_path = Path("chromedriver.exe" if platform.system() == "Windows" else "chromedriver")
        if target_path.exists():
            target_path.unlink()
        
        chromedriver_exe.rename(target_path)
        
        # 设置执行权限（Linux/macOS）
        if platform.system() != "Windows":
            os.chmod(target_path, 0o755)
        
        # 清理临时文件
        import shutil
        shutil.rmtree(temp_dir)
        
        print(f"[成功] ChromeDriver安装完成: {target_path.absolute()}")
        return str(target_path.absolute())
        
    except Exception as e:
        print(f"[失败] 下载ChromeDriver失败: {e}")
        return None

def install_webdriver_manager():
    """安装webdriver_manager"""
    try:
        print("📦 正在安装webdriver-manager...")
        subprocess.run([sys.executable, "-m", "pip", "install", "webdriver-manager"], check=True)
        print("[成功] webdriver-manager安装成功")
        return True
    except Exception as e:
        print(f"[失败] 安装webdriver-manager失败: {e}")
        return False

def test_chromedriver():
    """测试ChromeDriver是否工作"""
    try:
        print("🧪 测试ChromeDriver...")
        
        # 尝试使用webdriver_manager
        try:
            from webdriver_manager.chrome import ChromeDriverManager
            from selenium import webdriver
            from selenium.webdriver.chrome.service import Service
            from selenium.webdriver.chrome.options import Options
            
            options = Options()
            options.add_argument('--headless')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=options)
            
            driver.get("https://www.google.com")
            title = driver.title
            driver.quit()
            
            print(f"[成功] ChromeDriver测试成功！页面标题: {title}")
            return True
            
        except ImportError:
            print("[警告] webdriver_manager未安装，尝试使用本地ChromeDriver...")
            
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            
            options = Options()
            options.add_argument('--headless')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            
            driver = webdriver.Chrome(options=options)
            driver.get("https://www.google.com")
            title = driver.title
            driver.quit()
            
            print(f"[成功] ChromeDriver测试成功！页面标题: {title}")
            return True
            
    except Exception as e:
        print(f"[失败] ChromeDriver测试失败: {e}")
        return False

def main():
    """主函数"""
    print("[设置] ChromeDriver问题修复工具")
    print("=" * 50)
    
    # 检查Chrome版本
    print("[检查] 检查Chrome浏览器版本...")
    chrome_version = get_chrome_version()
    
    if not chrome_version:
        print("[失败] 未找到Chrome浏览器，请先安装Chrome")
        return
    
    print(f"[成功] 检测到Chrome版本: {chrome_version}")
    
    # 尝试安装webdriver_manager
    print("\n📦 安装webdriver_manager...")
    if install_webdriver_manager():
        # 测试webdriver_manager
        if test_chromedriver():
            print("\n[完成] ChromeDriver问题已解决！")
            print("[提示] 建议：使用webdriver_manager可以自动管理ChromeDriver版本")
            return
    
    # 如果webdriver_manager失败，手动下载
    print("\n[重试] webdriver_manager失败，尝试手动下载ChromeDriver...")
    
    download_url, driver_version = get_chromedriver_download_url(chrome_version)
    
    if not download_url:
        print("[失败] 无法获取ChromeDriver下载链接")
        print("[提示] 请手动访问 https://chromedriver.chromium.org/ 下载对应版本")
        return
    
    print(f"🔗 找到ChromeDriver版本: {driver_version}")
    
    # 下载ChromeDriver
    chromedriver_path = download_chromedriver(download_url, driver_version)
    
    if chromedriver_path:
        # 测试ChromeDriver
        if test_chromedriver():
            print("\n[完成] ChromeDriver问题已解决！")
        else:
            print("\n[失败] ChromeDriver仍然无法正常工作")
    else:
        print("\n[失败] ChromeDriver下载失败")
    
    print("\n[提示] 如果问题仍然存在，请尝试：")
    print("1. 更新Chrome浏览器到最新版本")
    print("2. 重启计算机")
    print("3. 检查防火墙和代理设置")
    print("4. 手动下载ChromeDriver并放到PATH中")

if __name__ == "__main__":
    main()
