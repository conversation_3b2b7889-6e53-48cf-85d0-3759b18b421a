<?php

namespace plugin\Lottery\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Log;
use think\facade\Db;

class Api extends BasePlugin {

    protected $scene = ['admin'];  // 只允许管理员访问
    
    // 管理面板首页
    public function index() {
        // 检查数据库表是否存在
        try {
            $tables = [
                'plugin_lottery_config',
                'plugin_lottery_prizes',
                'plugin_lottery_records',
                'plugin_lottery_merchant_limits',
                'plugin_lottery_turnover_rules',
                'plugin_lottery_turnover_claims',
                'plugin_lottery_prize_types'
            ];

            foreach ($tables as $table) {
                $exists = Db::query("SHOW TABLES LIKE 'pt_{$table}'");
                if (empty($exists)) {
                    Log::error("数据库表 pt_{$table} 不存在");
                }
            }
        } catch (\Exception $e) {
            Log::error('检查数据库表失败：' . $e->getMessage());
        }

        return View::fetch();
    }
    
    /**
     * 获取奖品列表
     */
    public function getPrizes() {
        try {
            $page = input('page/d', 1);
            $pageSize = input('pageSize/d', 20);
            
            $query = Db::name('plugin_lottery_prizes');
            
            $total = $query->count();
            $prizes = $query->order('sort asc, id asc')
                ->page($page, $pageSize)
                ->select()
                ->toArray();
            
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => [
                    'list' => $prizes,
                    'total' => $total
                ]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 添加奖品
     */
    public function addPrize() {
        try {
            $data = input('post.');
            
            // 验证必填字段
            if (empty($data['name']) || empty($data['type'])) {
                throw new \Exception('奖品名称和类型不能为空');
            }
            
            $prizeData = [
                'name' => $data['name'],
                'type' => $data['type'],
                'probability' => floatval($data['probability'] ?? 0),
                'stock' => intval($data['stock'] ?? 0),
                'balance_amount' => floatval($data['balance_amount'] ?? 0),
                'description' => $data['description'] ?? '',
                'image' => $data['image'] ?? '',
                'status' => intval($data['status'] ?? 1),
                'sort' => intval($data['sort'] ?? 0)
            ];
            
            $id = Db::name('plugin_lottery_prizes')->insertGetId($prizeData);
            
            return json([
                'code' => 200,
                'msg' => '添加成功',
                'data' => ['id' => $id]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 更新奖品
     */
    public function updatePrize() {
        try {
            $id = input('id/d');
            $data = input('post.');
            
            if (!$id) {
                throw new \Exception('奖品ID不能为空');
            }
            
            $prizeData = [
                'name' => $data['name'],
                'type' => $data['type'],
                'probability' => floatval($data['probability'] ?? 0),
                'stock' => intval($data['stock'] ?? 0),
                'balance_amount' => floatval($data['balance_amount'] ?? 0),
                'description' => $data['description'] ?? '',
                'image' => $data['image'] ?? '',
                'status' => intval($data['status'] ?? 1),
                'sort' => intval($data['sort'] ?? 0),
                'update_time' => date('Y-m-d H:i:s')
            ];
            
            Db::name('plugin_lottery_prizes')->where('id', $id)->update($prizeData);
            
            return json([
                'code' => 200,
                'msg' => '更新成功'
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 删除奖品
     */
    public function deletePrize() {
        try {
            $id = input('id/d');
            
            if (!$id) {
                throw new \Exception('奖品ID不能为空');
            }
            
            Db::name('plugin_lottery_prizes')->where('id', $id)->delete();
            
            return json([
                'code' => 200,
                'msg' => '删除成功'
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 更新奖品状态
     */
    public function updatePrizeStatus() {
        try {
            $id = input('id/d');
            $status = input('status/d');
            
            if (!$id) {
                throw new \Exception('奖品ID不能为空');
            }
            
            Db::name('plugin_lottery_prizes')->where('id', $id)->update([
                'status' => $status,
                'update_time' => date('Y-m-d H:i:s')
            ]);
            
            return json([
                'code' => 200,
                'msg' => '更新成功'
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 获取抽奖记录
     */
    public function getLotteryRecords() {
        try {
            $page = input('page/d', 1);
            $pageSize = input('pageSize/d', 20);
            $merchantId = input('merchantId/s', '');
            $prizeName = input('prizeName/s', '');
            $prizeType = input('prizeType/s', '');
            
            $query = Db::name('plugin_lottery_records')
                ->alias('r')
                ->leftJoin('plugin_lottery_prizes p', 'r.prize_id = p.id')
                ->field('r.*, p.name as prize_name, p.type as prize_type');
            
            // 添加搜索条件
            if (!empty($merchantId)) {
                $query->where('r.merchant_id', $merchantId);
            }
            if (!empty($prizeName)) {
                $query->where('p.name', 'like', '%' . $prizeName . '%');
            }
            if (!empty($prizeType)) {
                $query->where('p.type', $prizeType);
            }
            
            $total = $query->count();
            $records = $query->order('r.create_time desc')
                ->page($page, $pageSize)
                ->select()
                ->toArray();
            
            // 获取奖品类型列表
            $prizeTypes = Db::name('plugin_lottery_prize_types')
                ->field('value, label, tag_type, tag_color')
                ->select()
                ->toArray();
            
            // 处理记录数据
            foreach ($records as &$record) {
                // 获取商户信息
                if (is_numeric($record['merchant_id'])) {
                    $merchant = Db::name('user')->where('id', $record['merchant_id'])->find();
                    $record['merchant_name'] = $merchant ? $merchant['nickname'] : '未知用户';
                } else {
                    $record['merchant_name'] = $record['merchant_id'];
                }
                
                // 设置奖品类型样式
                $record['prize_type_text'] = $this->getPrizeTypeText($record['prize_type'], $prizeTypes);
                $record['prize_type_style'] = $this->getPrizeTypeStyle($record['prize_type'], $prizeTypes);
            }
            
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => [
                    'records' => $records,
                    'pagination' => [
                        'total' => $total,
                        'current' => $page,
                        'pageSize' => $pageSize
                    ],
                    'prizeTypes' => $prizeTypes
                ]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 获取奖品类型文本
     */
    private function getPrizeTypeText($type, $prizeTypes = []) {
        foreach ($prizeTypes as $prizeType) {
            if ($prizeType['value'] === $type) {
                return $prizeType['label'];
            }
        }
        return $type;
    }

    /**
     * 获取奖品类型样式
     */
    private function getPrizeTypeStyle($type, $prizeTypes = []) {
        foreach ($prizeTypes as $prizeType) {
            if ($prizeType['value'] === $type) {
                return [
                    'type' => $prizeType['tag_type'] === 'custom' ? '' : $prizeType['tag_type'],
                    'color' => $prizeType['tag_type'] === 'custom' ? $prizeType['tag_color'] : null
                ];
            }
        }
        return ['type' => 'info', 'color' => null];
    }

    /**
     * 获取抽奖配置
     */
    public function getLotteryConfig() {
        try {
            $config = Db::name('plugin_lottery_config')->select()->toArray();

            // 设置默认配置
            $defaultConfig = [
                'status' => 1,
                'daily_limit' => 3,
                'start_hour' => '00:00',
                'end_hour' => '23:59',
                'show_probability' => 0
            ];

            $configData = $defaultConfig;

            // 覆盖数据库中的配置
            foreach ($config as $item) {
                $key = $item['config_key'];
                $value = $item['config_value'];

                // 根据配置键进行类型转换
                switch ($key) {
                    case 'status':
                    case 'daily_limit':
                    case 'show_probability':
                        $configData[$key] = intval($value);
                        break;
                    case 'start_hour':
                    case 'end_hour':
                        $configData[$key] = $value;
                        break;
                    default:
                        $configData[$key] = $value;
                        break;
                }
            }

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $configData
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 保存抽奖配置
     */
    public function saveLotteryConfig() {
        try {
            $data = input('post.');

            foreach ($data as $key => $value) {
                Db::name('plugin_lottery_config')->replace()->save([
                    'config_key' => $key,
                    'config_value' => $value,
                    'update_time' => date('Y-m-d H:i:s')
                ]);
            }

            return json([
                'code' => 200,
                'msg' => '保存成功'
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 获取奖品类型
     */
    public function getPrizeTypes() {
        try {
            $types = Db::name('plugin_lottery_prize_types')
                ->order('sort asc, id asc')
                ->select()
                ->toArray();

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $types
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 添加奖品类型
     */
    public function addPrizeType() {
        try {
            $data = input('post.');

            if (empty($data['label']) || empty($data['value'])) {
                throw new \Exception('类型名称和标识不能为空');
            }

            // 检查标识是否已存在
            $exists = Db::name('plugin_lottery_prize_types')
                ->where('value', $data['value'])
                ->find();

            if ($exists) {
                throw new \Exception('类型标识已存在');
            }

            $typeData = [
                'label' => $data['label'],
                'value' => $data['value'],
                'tag_type' => $data['tag_type'] ?? 'info',
                'tag_color' => $data['tag_color'] ?? '',
                'sort' => intval($data['sort'] ?? 0),
                'status' => 1,
                'create_time' => date('Y-m-d H:i:s')
            ];

            $id = Db::name('plugin_lottery_prize_types')->insertGetId($typeData);

            return json([
                'code' => 200,
                'msg' => '添加成功',
                'data' => ['id' => $id]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 更新奖品类型
     */
    public function updatePrizeType() {
        try {
            $id = input('id/d');
            $data = input('post.');

            if (!$id) {
                throw new \Exception('类型ID不能为空');
            }

            // 检查类型是否存在
            $type = Db::name('plugin_lottery_prize_types')->where('id', $id)->find();
            if (!$type) {
                throw new \Exception('类型不存在');
            }

            $typeData = [
                'label' => $data['label'],
                'tag_type' => $data['tag_type'] ?? 'info',
                'tag_color' => $data['tag_color'] ?? '',
                'sort' => intval($data['sort'] ?? 0),
                'update_time' => date('Y-m-d H:i:s')
            ];

            Db::name('plugin_lottery_prize_types')->where('id', $id)->update($typeData);

            return json([
                'code' => 200,
                'msg' => '更新成功'
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 删除奖品类型
     */
    public function deletePrizeType() {
        try {
            $id = input('id/d');

            if (!$id) {
                throw new \Exception('类型ID不能为空');
            }

            // 检查类型是否存在
            $type = Db::name('plugin_lottery_prize_types')->where('id', $id)->find();
            if (!$type) {
                throw new \Exception('类型不存在');
            }

            // 检查是否有奖品使用此类型
            $prizeCount = Db::name('plugin_lottery_prizes')
                ->where('type', $type['value'])
                ->count();

            if ($prizeCount > 0) {
                throw new \Exception('该类型下还有奖品，无法删除');
            }

            Db::name('plugin_lottery_prize_types')->where('id', $id)->delete();

            return json([
                'code' => 200,
                'msg' => '删除成功'
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 获取流水规则
     */
    public function getTurnoverRules() {
        try {
            $page = input('page/d', 1);
            $pageSize = input('pageSize/d', 20);

            $query = Db::name('plugin_lottery_turnover_rules');

            $total = $query->count();
            $rules = $query->order('sort asc, id asc')
                ->page($page, $pageSize)
                ->select()
                ->toArray();

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => [
                    'list' => $rules,
                    'total' => $total
                ]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 添加流水规则
     */
    public function addTurnoverRule() {
        try {
            $data = input('post.');

            if (empty($data['turnover_amount']) || empty($data['draw_times'])) {
                throw new \Exception('流水金额和抽奖次数不能为空');
            }

            $ruleData = [
                'turnover_amount' => floatval($data['turnover_amount']),
                'draw_times' => intval($data['draw_times']),
                'status' => intval($data['status'] ?? 1),
                'sort' => intval($data['sort'] ?? 0),
                'create_time' => date('Y-m-d H:i:s')
            ];

            $id = Db::name('plugin_lottery_turnover_rules')->insertGetId($ruleData);

            return json([
                'code' => 200,
                'msg' => '添加成功',
                'data' => ['id' => $id]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 更新流水规则
     */
    public function updateTurnoverRule() {
        try {
            $id = input('id/d');
            $data = input('post.');

            if (!$id) {
                throw new \Exception('规则ID不能为空');
            }

            $ruleData = [
                'turnover_amount' => floatval($data['turnover_amount']),
                'draw_times' => intval($data['draw_times']),
                'status' => intval($data['status'] ?? 1),
                'sort' => intval($data['sort'] ?? 0),
                'update_time' => date('Y-m-d H:i:s')
            ];

            Db::name('plugin_lottery_turnover_rules')->where('id', $id)->update($ruleData);

            return json([
                'code' => 200,
                'msg' => '更新成功'
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 删除流水规则
     */
    public function deleteTurnoverRule() {
        try {
            $id = input('id/d');

            if (!$id) {
                throw new \Exception('规则ID不能为空');
            }

            Db::name('plugin_lottery_turnover_rules')->where('id', $id)->delete();

            return json([
                'code' => 200,
                'msg' => '删除成功'
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 更新流水规则状态
     */
    public function updateTurnoverRuleStatus() {
        try {
            $id = input('id/d');
            $status = input('status/d');

            if (!$id) {
                throw new \Exception('规则ID不能为空');
            }

            Db::name('plugin_lottery_turnover_rules')->where('id', $id)->update([
                'status' => $status,
                'update_time' => date('Y-m-d H:i:s')
            ]);

            return json([
                'code' => 200,
                'msg' => '更新成功'
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 清空抽奖记录
     */
    public function clearRecords() {
        try {
            Db::name('plugin_lottery_records')->delete(true);

            return json([
                'code' => 200,
                'msg' => '清空成功'
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 批量删除记录
     */
    public function batchDeleteRecords() {
        try {
            $ids = input('ids/a', []);

            if (empty($ids)) {
                throw new \Exception('请选择要删除的记录');
            }

            Db::name('plugin_lottery_records')->whereIn('id', $ids)->delete();

            return json([
                'code' => 200,
                'msg' => '删除成功'
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 保存奖品
     */
    public function savePrize() {
        try {
            $data = input('post.');

            // 验证必填字段
            if (empty($data['name']) || empty($data['type'])) {
                throw new \Exception('奖品名称和类型不能为空');
            }

            $prizeData = [
                'name' => $data['name'],
                'type' => $data['type'],
                'probability' => floatval($data['probability'] ?? 0),
                'stock' => intval($data['stock'] ?? 0),
                'balance_amount' => floatval($data['balance_amount'] ?? 0),
                'description' => $data['description'] ?? '',
                'image' => $data['image'] ?? '',
                'status' => intval($data['status'] ?? 1),
                'sort' => intval($data['sort'] ?? 0)
            ];

            if (!empty($data['id'])) {
                // 更新奖品
                $prizeData['update_time'] = date('Y-m-d H:i:s');
                Db::name('plugin_lottery_prizes')->where('id', $data['id'])->update($prizeData);
                $message = '更新成功';
            } else {
                // 添加奖品
                $prizeData['create_time'] = date('Y-m-d H:i:s');
                Db::name('plugin_lottery_prizes')->insert($prizeData);
                $message = '添加成功';
            }

            return json([
                'code' => 200,
                'msg' => $message
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 保存奖品类型
     */
    public function savePrizeType() {
        try {
            $data = input('post.');

            if (empty($data['label']) || empty($data['value'])) {
                throw new \Exception('类型名称和标识不能为空');
            }

            $typeData = [
                'label' => $data['label'],
                'value' => $data['value'],
                'tag_type' => $data['tag_type'] ?? 'info',
                'tag_color' => $data['tag_color'] ?? '',
                'sort' => intval($data['sort'] ?? 0),
                'status' => 1
            ];

            if (!empty($data['id'])) {
                // 更新类型 - 检查标识是否与其他记录重复
                $exists = Db::name('plugin_lottery_prize_types')
                    ->where('value', $data['value'])
                    ->where('id', '<>', $data['id'])
                    ->find();

                if ($exists) {
                    throw new \Exception('类型标识已存在');
                }

                $typeData['update_time'] = date('Y-m-d H:i:s');
                Db::name('plugin_lottery_prize_types')->where('id', $data['id'])->update($typeData);
                $message = '更新成功';
            } else {
                // 检查标识是否已存在
                $exists = Db::name('plugin_lottery_prize_types')
                    ->where('value', $data['value'])
                    ->find();

                if ($exists) {
                    throw new \Exception('类型标识已存在');
                }

                // 添加类型
                $typeData['create_time'] = date('Y-m-d H:i:s');
                Db::name('plugin_lottery_prize_types')->insert($typeData);
                $message = '添加成功';
            }

            return json([
                'code' => 200,
                'msg' => $message
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 保存流水规则
     */
    public function saveTurnoverRule() {
        try {
            $data = input('post.');

            if (empty($data['turnover_amount']) || empty($data['draw_times'])) {
                throw new \Exception('流水金额和抽奖次数不能为空');
            }

            $ruleData = [
                'turnover_amount' => floatval($data['turnover_amount']),
                'draw_times' => intval($data['draw_times']),
                'status' => intval($data['status'] ?? 1),
                'sort' => intval($data['sort'] ?? 0)
            ];

            if (!empty($data['id'])) {
                // 更新规则
                $ruleData['update_time'] = date('Y-m-d H:i:s');
                Db::name('plugin_lottery_turnover_rules')->where('id', $data['id'])->update($ruleData);
                $message = '更新成功';
            } else {
                // 添加规则
                $ruleData['create_time'] = date('Y-m-d H:i:s');
                Db::name('plugin_lottery_turnover_rules')->insert($ruleData);
                $message = '添加成功';
            }

            return json([
                'code' => 200,
                'msg' => $message
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 获取流水配置
     */
    public function getTurnoverConfig() {
        try {
            // 返回空配置，因为这个功能可能不需要特殊配置
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => []
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 重置流水
     */
    public function resetTurnover() {
        try {
            // 清空流水领取记录
            Db::name('plugin_lottery_turnover_claims')->delete(true);

            return json([
                'code' => 200,
                'msg' => '重置成功'
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 获取Hook配置
     */
    public function getHookConfig() {
        try {
            // 返回默认Hook配置
            $config = [
                'auto_update_status' => 0,
                'update_interval' => 300,
                'last_update_time' => ''
            ];

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $config
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 触发更新
     */
    public function triggerUpdate() {
        try {
            // 这里可以添加具体的更新逻辑
            return json([
                'code' => 200,
                'msg' => '更新成功'
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 发送余额
     */
    public function sendBalance() {
        try {
            $merchantId = input('merchant_id/d');
            $amount = input('amount/f');
            $recordId = input('record_id/d');

            if (!$merchantId || !$amount || !$recordId) {
                throw new \Exception('参数不完整');
            }

            // 这里应该添加实际的余额发放逻辑
            // 更新记录状态
            Db::name('plugin_lottery_records')->where('id', $recordId)->update([
                'balance_sent' => 1,
                'update_time' => date('Y-m-d H:i:s')
            ]);

            return json([
                'code' => 200,
                'msg' => '发放成功'
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 获取自动发送配置
     */
    public function getAutoSendConfig() {
        try {
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => [
                    'auto_send' => false
                ]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 生成虚拟记录
     */
    public function generateFakeRecords() {
        try {
            $count = input('count/d', 1);
            $prizeId = input('prize_id/d');
            $timeRange = input('timeRange/a', []);
            $merchantPrefix = input('merchant_prefix/s', '');

            if ($count <= 0 || $count > 1000) {
                throw new \Exception('生成数量必须在1-1000之间');
            }

            // 获取奖品信息
            $prizes = Db::name('plugin_lottery_prizes')->select()->toArray();
            if (empty($prizes)) {
                throw new \Exception('没有可用的奖品');
            }

            $records = [];
            for ($i = 0; $i < $count; $i++) {
                // 选择奖品
                $prize = $prizeId ?
                    array_filter($prizes, function($p) use ($prizeId) { return $p['id'] == $prizeId; }) :
                    $prizes[array_rand($prizes)];

                if (is_array($prize)) {
                    $prize = reset($prize);
                }

                // 生成商户ID
                $merchantId = $merchantPrefix ?
                    $merchantPrefix . ($i + 1) :
                    '商家' . str_pad($i + 1, 4, '0', STR_PAD_LEFT);

                // 生成时间
                $createTime = !empty($timeRange) && count($timeRange) >= 2 ?
                    date('Y-m-d H:i:s', rand(strtotime($timeRange[0]), strtotime($timeRange[1]))) :
                    date('Y-m-d H:i:s', time() - rand(0, 86400 * 7)); // 最近7天内随机时间

                $records[] = [
                    'user_id' => 0,
                    'merchant_id' => $merchantId,
                    'prize_id' => $prize['id'],
                    'prize_name' => $prize['name'],
                    'prize_type' => $prize['type'],
                    'balance_amount' => $prize['balance_amount'],
                    'shipped' => 0,
                    'balance_sent' => 0,
                    'auto_sent' => 0,
                    'is_virtual' => 1,
                    'create_time' => $createTime,
                    'update_time' => $createTime
                ];
            }

            // 批量插入记录
            Db::name('plugin_lottery_records')->insertAll($records);

            return json([
                'code' => 200,
                'msg' => '生成成功'
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 安装数据库表
     */
    public function install() {
        try {
            // 执行安装脚本
            $installSql = file_get_contents(__DIR__ . '/../install.sql');
            if (!$installSql) {
                throw new \Exception('安装脚本文件不存在');
            }

            // 分割SQL语句
            $statements = array_filter(
                array_map('trim', explode(';', $installSql)),
                function($statement) {
                    return !empty($statement) && !preg_match('/^\s*--/', $statement);
                }
            );

            // 执行每个SQL语句
            foreach ($statements as $statement) {
                if (!empty($statement)) {
                    try {
                        Db::execute($statement);
                    } catch (\Exception $e) {
                        // 如果是表已存在的错误，忽略它
                        if (strpos($e->getMessage(), 'already exists') === false) {
                            Log::error('执行SQL失败：' . $e->getMessage() . ' SQL: ' . $statement);
                        }
                    }
                }
            }

            // 执行数据脚本
            $dataSql = file_get_contents(__DIR__ . '/../data.sql');
            if ($dataSql) {
                $dataStatements = array_filter(
                    array_map('trim', explode(';', $dataSql)),
                    function($statement) {
                        return !empty($statement) && !preg_match('/^\s*--/', $statement);
                    }
                );

                foreach ($dataStatements as $statement) {
                    if (!empty($statement)) {
                        try {
                            Db::execute($statement);
                        } catch (\Exception $e) {
                            // 如果是重复数据错误，忽略它
                            if (strpos($e->getMessage(), 'Duplicate entry') === false) {
                                Log::error('执行数据SQL失败：' . $e->getMessage() . ' SQL: ' . $statement);
                            }
                        }
                    }
                }
            }

            return json([
                'code' => 200,
                'msg' => '安装成功'
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '安装失败：' . $e->getMessage()]);
        }
    }
}
