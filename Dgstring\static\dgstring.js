// 创建容器div
var container = document.createElement('div');
container.id = 'xznkfxt'; // 设置ID
container.style.position = 'fixed';
container.style.right = '20px';
container.style.bottom = '20px';
container.style.zIndex = '9999';
container.style.width = 'auto';      // 改为自适应宽度
container.style.height = 'auto';     // 改为自适应高度
container.style.pointerEvents = 'none'; 

// 创建一个内部容器来包装脚本
var innerContainer = document.createElement('div');
innerContainer.style.pointerEvents = 'auto';  // 内部容器保持可点击
innerContainer.style.width = '150px';         // 将固定宽度移到内部容器
innerContainer.style.height = '150px';        // 将固定高度移到内部容器

// 添加外部脚本
var script = document.createElement('script');
script.src = 'https://w.xiaozhiniao.com.cn/index/gtgj?js=1&appid=17698&yzm=a2t3tva1f8&zy=1';

// 添加脚本加载完成后的处理
script.onload = function() {
    // 重写音频自动播放函数
    window.addEventListener('DOMContentLoaded', function() {
        // 等待用户第一次点击页面
        document.addEventListener('click', function initAudio() {
            // 创建一个静音的音频上下文来初始化音频系统
            var audioContext = new (window.AudioContext || window.webkitAudioContext)();
            document.removeEventListener('click', initAudio);
        }, { once: true });
    });
};

// 将脚本添加到内部容器，再将内部容器添加到主容器中
innerContainer.appendChild(script);
container.appendChild(innerContainer);

// 将容器添加到页面
document.body.appendChild(container);

// 添加MutationObserver来监听动态添加的元素
var observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.addedNodes.length) {
            mutation.addedNodes.forEach(function(node) {
                if (node.style) {
                    // 确保动态添加的元素不会影响整个页面的点击
                    node.style.pointerEvents = 'auto';
                    if (node.style.position === 'fixed') {
                        node.style.width = 'auto';
                        node.style.height = 'auto';
                    }
                }
                // 处理可能的音频元素
                if (node.tagName === 'AUDIO' || node.tagName === 'VIDEO') {
                    node.muted = true; // 默认静音
                }
            });
        }
    });
});

// 开始观察container的变化
observer.observe(container, {
    childList: true,
    subtree: true
});
