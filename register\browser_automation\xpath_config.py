#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
XPath配置文件
存储970faka.com注册页面的所有元素定位信息
"""

class XPathConfig:
    """XPath配置类"""
    
    # 注册表单元素
    FORM_ELEMENTS = {
        # 用户名输入框
        'username': "//*[@id='username']/div[1]/div/span/input",
        
        # 密码输入框
        'password': "//*[@id='password']/div/div/span/input",
        
        # 确认密码输入框
        'repeat_password': "//*[@id='repeat_password']/div/div/span/input",
        
        # 手机号输入框
        'mobile': "//*[@id='mobile']/div/div/span/input",
        
        # 短信验证码输入框
        'mobile_code': "//*[@id='mobile_code']/div/div/span/input",
    }
    
    # 按钮元素
    BUTTON_ELEMENTS = {
        # 获取短信验证码按钮（用户提供的准确XPath）
        'get_sms_code': "//*[@id='mobile_code']/div/div/span/span[2]/button",

        # 初始弹窗关闭按钮（用户新提供的）
        'initial_popup_close': "/html/body/div[2]/div/div[2]/div/div[3]/button",

        # 验证码弹窗确认按钮（用户提供的准确XPath）
        'captcha_confirm': "/html/body/div[2]/div[2]/div/div[3]/button[2]",

        # 提交按钮（多种可能的选择器）
        'submit_buttons': [
            "//button[contains(text(), '立即注册')]",  # 优先匹配"立即注册"文本
            "//button[@type='submit']",
            "//button[contains(text(), '注册')]",
            "//button[contains(text(), '提交')]",
            "//input[@type='submit']",
            "//button[contains(@class, 'submit')]",
            "//button[contains(@class, 'arco-btn-primary')]",  # 蓝色主要按钮
            "//button[contains(@class, 'register')]"  # 注册相关按钮
        ],

        # 协议复选框（已阅读并同意）
        'agreement_checkbox_exact': "//*[@id=\"app\"]/div/div[2]/div[1]/div/div[3]/div[2]/div/div/div/form/div[6]/div[1]/div/div",
        'agreement_checkbox': "//div[@class='arco-checkbox-icon']",
        'agreement_checkbox_alt': "//div[contains(@class, 'arco-checkbox-icon')]",
        'agreement_checkbox_container': "//label[contains(@class, 'arco-checkbox')]",
        'agreement_checkbox_precise': "//div[@class='register-form-agreement-actions']//div[@class='arco-checkbox-icon']",
        'agreement_checkbox_input': "//div[@class='register-form-agreement-actions']//input[@type='checkbox']"
    }

    # 弹窗元素
    POPUP_ELEMENTS = {
        # 初始弹窗容器（用户提供的检测元素）
        'initial_popup': "/html/body/div[2]/div/div[2]/div",

        # 初始弹窗关闭按钮
        'initial_popup_close': "/html/body/div[2]/div/div[2]/div/div[3]/button"
    }
    
    # 验证码相关元素
    CAPTCHA_ELEMENTS = {
        # 图形验证码图片
        'captcha_image': "/html/body/div[2]/div[2]/div/div[2]/div/div[1]/div/img",
        
        # 图形验证码输入框
        'captcha_input': "/html/body/div[2]/div[2]/div/div[2]/div/div[2]/span/input",
        
        # 验证码刷新按钮（如果有）
        'captcha_refresh': "//img[contains(@src, 'refresh') or contains(@alt, '刷新')]"
    }
    
    # 错误和成功提示元素
    MESSAGE_ELEMENTS = {
        # 错误提示
        'error_messages': [
            "//*[contains(@class, 'error')]",
            "//*[contains(@class, 'danger')]",
            "//*[contains(text(), '错误')]",
            "//*[contains(text(), '失败')]",
            "//*[contains(text(), '已存在')]"
        ],
        
        # 成功提示
        'success_messages': [
            "//*[contains(@class, 'success')]",
            "//*[contains(text(), '成功')]",
            "//*[contains(text(), '注册完成')]"
        ]
    }
    
    # 页面检测元素
    PAGE_ELEMENTS = {
        # 注册页面标识
        'register_page_indicators': [
            "//title[contains(text(), '注册')]",
            "//*[contains(text(), '用户注册')]",
            "//*[contains(text(), '商户注册')]"
        ],
        
        # 登录成功页面标识
        'login_success_indicators': [
            "//title[contains(text(), 'dashboard')]",
            "//*[contains(text(), '控制台')]",
            "//*[contains(text(), '管理后台')]"
        ]
    }
    
    @classmethod
    def get_form_element(cls, element_name: str) -> str:
        """获取表单元素XPath"""
        return cls.FORM_ELEMENTS.get(element_name, "")
    
    @classmethod
    def get_button_element(cls, button_name: str) -> str:
        """获取按钮元素XPath"""
        return cls.BUTTON_ELEMENTS.get(button_name, "")
    
    @classmethod
    def get_captcha_element(cls, element_name: str) -> str:
        """获取验证码元素XPath"""
        return cls.CAPTCHA_ELEMENTS.get(element_name, "")

    @classmethod
    def get_popup_element(cls, element_name: str) -> str:
        """获取弹窗元素XPath"""
        return cls.POPUP_ELEMENTS.get(element_name, "")
    
    @classmethod
    def get_submit_buttons(cls) -> list:
        """获取所有可能的提交按钮XPath"""
        return cls.BUTTON_ELEMENTS.get('submit_buttons', [])
    
    @classmethod
    def get_error_selectors(cls) -> list:
        """获取错误提示选择器"""
        return cls.MESSAGE_ELEMENTS.get('error_messages', [])
    
    @classmethod
    def get_success_selectors(cls) -> list:
        """获取成功提示选择器"""
        return cls.MESSAGE_ELEMENTS.get('success_messages', [])

    @classmethod
    def get_agreement_checkbox_selectors(cls) -> list:
        """获取协议复选框选择器"""
        return [
            cls.BUTTON_ELEMENTS.get('agreement_checkbox_exact', ''),    # 用户提供的精确XPath
            cls.BUTTON_ELEMENTS.get('agreement_checkbox_precise', ''),  # 基于HTML结构的精确选择器
            cls.BUTTON_ELEMENTS.get('agreement_checkbox', ''),
            cls.BUTTON_ELEMENTS.get('agreement_checkbox_alt', ''),
            cls.BUTTON_ELEMENTS.get('agreement_checkbox_container', ''),
            cls.BUTTON_ELEMENTS.get('agreement_checkbox_input', '')
        ]
    
    @classmethod
    def validate_xpath(cls, driver, xpath: str) -> bool:
        """验证XPath是否有效"""
        try:
            elements = driver.find_elements("xpath", xpath)
            return len(elements) > 0
        except:
            return False
    
    @classmethod
    def find_working_submit_button(cls, driver) -> str:
        """查找可用的提交按钮"""
        for xpath in cls.get_submit_buttons():
            if cls.validate_xpath(driver, xpath):
                return xpath
        return ""
    
    @classmethod
    def check_page_errors(cls, driver) -> list:
        """检查页面错误信息"""
        errors = []
        for xpath in cls.get_error_selectors():
            try:
                elements = driver.find_elements("xpath", xpath)
                for element in elements:
                    text = element.text.strip()
                    if text and text not in errors:
                        errors.append(text)
            except:
                continue
        return errors
    
    @classmethod
    def check_page_success(cls, driver) -> list:
        """检查页面成功信息"""
        success_messages = []
        for xpath in cls.get_success_selectors():
            try:
                elements = driver.find_elements("xpath", xpath)
                for element in elements:
                    text = element.text.strip()
                    if text and text not in success_messages:
                        success_messages.append(text)
            except:
                continue
        return success_messages

# 使用示例
def example_usage():
    """使用示例"""
    from selenium import webdriver
    
    # 创建driver
    driver = webdriver.Chrome()
    
    try:
        # 访问注册页面
        driver.get("https://970faka.com/merchant/register")
        
        # 获取用户名输入框XPath
        username_xpath = XPathConfig.get_form_element('username')
        print(f"用户名输入框XPath: {username_xpath}")
        
        # 验证XPath是否有效
        is_valid = XPathConfig.validate_xpath(driver, username_xpath)
        print(f"XPath有效性: {is_valid}")
        
        # 查找可用的提交按钮
        submit_xpath = XPathConfig.find_working_submit_button(driver)
        print(f"可用的提交按钮: {submit_xpath}")
        
        # 检查页面错误
        errors = XPathConfig.check_page_errors(driver)
        print(f"页面错误: {errors}")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    example_usage()
