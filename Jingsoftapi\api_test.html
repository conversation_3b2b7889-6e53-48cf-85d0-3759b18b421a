<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API接口测试工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .config-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #007cba;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, textarea {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 6px;
            box-sizing: border-box;
            font-size: 14px;
        }
        input:focus, textarea:focus {
            border-color: #007cba;
            outline: none;
        }
        button {
            background: linear-gradient(135deg, #007cba 0%, #005a87 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin: 5px;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        .result {
            margin-top: 20px;
            padding: 20px;
            border-radius: 8px;
            white-space: pre-wrap;
            font-family: 'Consolas', monospace;
            font-size: 13px;
            max-height: 500px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border: 2px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 2px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 2px solid #bee5eb;
            color: #0c5460;
        }
        .loading {
            background: #fff3cd;
            border: 2px solid #ffeaa7;
            color: #856404;
        }
        .button-group {
            text-align: center;
            margin: 20px 0;
        }
        .field-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 API接口测试工具</h1>
        
        <div class="config-section">
            <h3>🔧 测试配置</h3>
            <div class="form-group">
                <label for="apiUrl">API地址:</label>
                <input type="text" id="apiUrl" value="https://spikeesoft.com/plugin/Jingsoftapi/api/get_user_info" placeholder="请输入API地址">
            </div>
            <div class="form-group">
                <label for="apiKey">API Key:</label>
                <input type="text" id="apiKey" value="ep_get_user_info_29043b27f3cad751" placeholder="请输入API Key（支持全局密钥或接口独立密钥）">
            </div>
            <div class="form-group">
                <label for="apiSecret">API Secret:</label>
                <input type="text" id="apiSecret" value="es_get_user_info_e5ac3999962fec35743cb06" placeholder="请输入API Secret">
            </div>
        </div>

        <div class="field-info">
            <h4>📋 返回字段说明</h4>
            <p><strong>mobile</strong> - 手机号 | <strong>username</strong> - 用户名 | <strong>create_time</strong> - 注册时间</p>
            <p><strong>contact_qq</strong> - 联系QQ | <strong>contact_mobile</strong> - 联系手机 | <strong>platform_money</strong> - 平台余额</p>
            <p style="color: #666; font-size: 12px; margin-top: 10px;">
                💡 <strong>密钥类型</strong>：支持全局密钥（ak_xxx）或接口独立密钥（ep_xxx）
            </p>
        </div>

        <div class="button-group">
            <button onclick="testAPI()" id="testBtn">🚀 测试API接口</button>
            <button onclick="clearResult()">🗑️ 清空结果</button>
            <button onclick="copyResult()">📋 复制结果</button>
        </div>
        
        <div id="result"></div>
    </div>

    <script>
        async function testAPI() {
            const apiUrl = document.getElementById('apiUrl').value.trim();
            const apiKey = document.getElementById('apiKey').value.trim();
            const apiSecret = document.getElementById('apiSecret').value.trim();
            const resultDiv = document.getElementById('result');
            const testBtn = document.getElementById('testBtn');
            
            // 验证输入
            if (!apiUrl || !apiKey || !apiSecret) {
                showResult('❌ 请填写完整的API配置信息', 'error');
                return;
            }
            
            // 禁用按钮并显示加载状态
            testBtn.disabled = true;
            testBtn.textContent = '🔄 测试中...';
            
            showResult('🔄 正在发送API请求...', 'loading');
            
            try {
                const startTime = Date.now();
                
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': apiKey,
                        'X-API-Secret': apiSecret
                    },
                    body: JSON.stringify({})
                });
                
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                const responseText = await response.text();
                let responseData;
                
                try {
                    responseData = JSON.parse(responseText);
                } catch (e) {
                    responseData = null;
                }
                
                let result = `=== 🧪 API测试结果 ===\n`;
                result += `🌐 请求URL: ${apiUrl}\n`;
                result += `📊 HTTP状态码: ${response.status}\n`;
                result += `⏱️ 响应时间: ${responseTime}ms\n`;
                result += `🕒 测试时间: ${new Date().toLocaleString()}\n`;
                result += `🔑 API Key: ${apiKey}\n`;
                result += `🔐 API Secret: ${apiSecret.substring(0, 10)}...\n\n`;
                
                if (response.ok && responseData && responseData.code === 200) {
                    result += `✅ 测试成功！\n\n`;
                    
                    if (responseData.data) {
                        const data = responseData.data;
                        result += `📊 用户数据:\n`;
                        result += `━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n`;
                        result += `📱 mobile: ${data.mobile || 'N/A'}\n`;
                        result += `👤 username: ${data.username || 'N/A'}\n`;
                        result += `📅 create_time: ${data.create_time || 'N/A'}\n`;
                        result += `💬 contact_qq: ${data.contact_qq || 'N/A'}\n`;
                        result += `📞 contact_mobile: ${data.contact_mobile || 'N/A'}\n`;
                        result += `💰 platform_money: ${data.platform_money || '0.00'}\n`;
                        result += `━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n`;
                    }
                    
                    result += `\n📋 完整API响应:\n`;
                    result += JSON.stringify(responseData, null, 2);
                    
                    showResult(result, 'success');
                } else {
                    result += `❌ 测试失败！\n\n`;
                    result += `📄 原始响应:\n${responseText}\n\n`;
                    
                    if (responseData && responseData.msg) {
                        result += `💬 错误信息: ${responseData.msg}\n\n`;
                    }
                    
                    result += `🔧 可能的问题:\n`;
                    result += `1. API密钥不正确\n`;
                    result += `2. 接口未启用\n`;
                    result += `3. 用户不存在\n`;
                    result += `4. 服务器错误\n`;
                    
                    showResult(result, 'error');
                }
                
            } catch (error) {
                let result = `❌ 网络请求失败！\n\n`;
                result += `💬 错误信息: ${error.message}\n\n`;
                result += `🔧 可能的问题:\n`;
                result += `1. 网络连接问题\n`;
                result += `2. 服务器无法访问\n`;
                result += `3. CORS跨域限制\n`;
                result += `4. API地址错误\n`;
                
                showResult(result, 'error');
            } finally {
                // 恢复按钮状态
                testBtn.disabled = false;
                testBtn.textContent = '🚀 测试API接口';
            }
        }
        
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
        }
        
        function clearResult() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '';
            resultDiv.className = '';
        }
        
        function copyResult() {
            const resultDiv = document.getElementById('result');
            if (resultDiv.textContent) {
                navigator.clipboard.writeText(resultDiv.textContent).then(() => {
                    alert('结果已复制到剪贴板！');
                }).catch(() => {
                    alert('复制失败，请手动复制');
                });
            } else {
                alert('没有可复制的内容');
            }
        }
        
        // 页面加载完成后的提示
        window.onload = function() {
            showResult('🧪 API测试工具已准备就绪！\n\n📝 使用说明:\n1. 修改上方的API配置信息\n2. 点击"测试API接口"按钮\n3. 查看测试结果\n\n🔧 密钥类型说明:\n• 全局密钥：ak_xxx（可调用所有启用的接口）\n• 接口独立密钥：ep_xxx（只能调用对应的接口）\n\n💡 当前已配置接口独立密钥，可直接测试！', 'info');
        };
        
        // 回车键快捷测试
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && e.ctrlKey) {
                testAPI();
            }
        });
    </script>
</body>
</html>
