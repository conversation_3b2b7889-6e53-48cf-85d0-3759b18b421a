<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单导出</title>
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            min-height: 100vh;
            background: linear-gradient(135deg, #f5f7fa, #e4e7eb);
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }

        .container {
            width: 700px;
            padding: 30px;
        }

        .el-card {
            background: rgba(255, 255, 255, 0.98);
            border: 1px solid rgba(0, 0, 0, 0.05);
            border-radius: 15px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
        }

        .el-card__body {
            padding: 30px;
        }

        h2 {
            color: #2c3e50;
            font-size: 24px;
            margin-bottom: 30px;
            text-align: center;
            font-weight: 600;
            position: relative;
            padding-bottom: 15px;
        }

        h2:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #2563eb, #1d4ed8);
        }

        .el-form-item {
            margin-bottom: 25px;
        }

        .el-input {
            --el-input-border-radius: 8px;
        }

        .el-date-editor {
            width: 100% !important;
            --el-date-editor-border-radius: 8px;
        }

        .button-group {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 30px;
        }

        .el-button {
            padding: 12px 25px;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .el-button--primary {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            border: none;
            color: white;
        }

        .el-button--success {
            background: linear-gradient(135deg, #059669, #047857);
            border: none;
            color: white;
        }

        .el-button--primary:hover,
        .el-button--success:hover {
            transform: translateY(-2px);
            opacity: 0.9;
        }

        .el-button:disabled {
            opacity: 0.6;
            transform: none;
        }

        .el-tag {
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: 14px;
            margin-left: 15px;
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
        }

        .el-tag.el-tag--info {
            background: linear-gradient(135deg, #9ca3af, #6b7280);
            opacity: 0.8;
        }

        .data-count {
            text-align: center;
            margin-top: 20px;
        }

        .pulse {
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .el-input:focus-within,
        .el-date-editor:focus-within {
            box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
        }
    </style>
</head>

<body>
    <div id="app" class="container">
        <el-card>
            <h2>订单信息导出工具</h2>
            <el-form :model="form" label-width="120px">
                <el-form-item label="用户ID" :rules="[{ required: true, message: '请输入用户ID' }]">
                    <el-input 
                        v-model="form.userId" 
                        placeholder="请输入用户ID"
                        clearable>
                    </el-input>
                </el-form-item>
                
                <el-form-item label="上级ID">
                    <el-input 
                        v-model="form.parentId" 
                        placeholder="可选，输入上级ID筛选相关订单"
                        clearable>
                    </el-input>
                    <div style="color: #dc2626; font-size: 12px; margin-top: 5px;">
                        如果填上级ID，则只筛选和这个上级有关的订单，不填则查询全部
                    </div>
                </el-form-item>

                <el-form-item label="商品关键词">
                    <el-input 
                        v-model="form.keyword" 
                        placeholder="可选，输入商品名称关键词筛选"
                        clearable>
                    </el-input>
                </el-form-item>

                <el-form-item label="时间范围">
                    <el-date-picker
                        v-model="form.dateRange"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="YYYY-MM-DD"
                        :shortcuts="dateShortcuts"
                        :locale="zhCn"
                        size="default"
                        style="width: 100%">
                    </el-date-picker>
                </el-form-item>

                <div class="button-group">
                    <el-button 
                        type="primary" 
                        :loading="loading" 
                        @click="queryData">
                        查询数据
                    </el-button>
                    <el-button 
                        type="success" 
                        @click="exportData"
                        :disabled="!count">
                        导出数据
                    </el-button>
                </div>

                <div class="data-count" v-if="count !== null">
                    <el-tag :type="count > 0 ? 'success' : 'info'" class="pulse">
                        查询到 {{ count }} 条数据
                        <template v-if="count > 0">
                            &nbsp;商品总金额 {{ goodsAmount }} 元
                            &nbsp;实收金额 {{ actualAmount }} 元
                            <span v-if="hasFee">(含手续费)</span>
                            &nbsp;商家实收 {{ merchantAmount }} 元
                        </template>
                    </el-tag>
                </div>
            </el-form>
        </el-card>
    </div>

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script>
        const { createApp, ref } = Vue;
        const { ElMessage } = ElementPlus;

        const zhCn = {
            name: 'zh-cn',
            el: {
                datepicker: {
                    now: '此刻',
                    today: '今天',
                    cancel: '取消',
                    clear: '清除',
                    confirm: '确定',
                    selectDate: '选择日期',
                    selectTime: '选择时间',
                    startDate: '开始日期',
                    startTime: '开始时间',
                    endDate: '结束日期',
                    endTime: '结束时间',
                    prevYear: '前一年',
                    nextYear: '后一年',
                    prevMonth: '上个月',
                    nextMonth: '下个月',
                    year: '年',
                    month1: '1月',
                    month2: '2月',
                    month3: '3月',
                    month4: '4月',
                    month5: '5月',
                    month6: '6月',
                    month7: '7月',
                    month8: '8月',
                    month9: '9月',
                    month10: '10月',
                    month11: '11月',
                    month12: '12月',
                    weeks: {
                        sun: '日',
                        mon: '一',
                        tue: '二',
                        wed: '三',
                        thu: '四',
                        fri: '五',
                        sat: '六'
                    }
                }
            }
        };

        const app = createApp({
            setup() {
                const form = ref({
                    userId: '',
                    parentId: '',
                    dateRange: '',
                    keyword: ''
                });
                const count = ref(null);
                const goodsAmount = ref(null);
                const actualAmount = ref(null);
                const merchantAmount = ref(null);
                const loading = ref(false);
                const hasFee = ref(false);

                // 日期快捷选项
                const dateShortcuts = [
                    {
                        text: '今天',
                        value: () => {
                            const today = new Date();
                            return [today, today];
                        }
                    },
                    {
                        text: '昨天',
                        value: () => {
                            const yesterday = new Date();
                            yesterday.setTime(yesterday.getTime() - 3600 * 1000 * 24);
                            return [yesterday, yesterday];
                        }
                    },
                    {
                        text: '最近一周',
                        value: () => {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                            return [start, end];
                        }
                    },
                    {
                        text: '最近一个月',
                        value: () => {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                            return [start, end];
                        }
                    },
                    {
                        text: '最近三个月',
                        value: () => {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                            return [start, end];
                        }
                    }
                ];

                const queryData = async () => {
                    if (!form.value.userId) {
                        ElMessage.warning('请输入用户ID');
                        return;
                    }

                    loading.value = true;
                    try {
                        const res = await axios.post('/plugin/Fengzeroexport/api/query', {
                            user_id: form.value.userId,
                            parent_id: form.value.parentId,
                            date_range: form.value.dateRange ? form.value.dateRange.join(' - ') : '',
                            keyword: form.value.keyword
                        });
                        
                        if (res.data.code === 200) {
                            count.value = res.data.data.count;
                            goodsAmount.value = res.data.data.goodsAmount;
                            actualAmount.value = res.data.data.actualAmount;
                            merchantAmount.value = res.data.data.merchantAmount;
                            hasFee.value = res.data.data.hasFee;
                            ElMessage.success('查询成功');
                        } else {
                            count.value = 0;
                            goodsAmount.value = null;
                            actualAmount.value = null;
                            merchantAmount.value = null;
                            hasFee.value = false;
                            ElMessage.error(res.data.msg || '查询失败');
                        }
                    } catch (error) {
                        count.value = 0;
                        goodsAmount.value = null;
                        actualAmount.value = null;
                        merchantAmount.value = null;
                        hasFee.value = false;
                        ElMessage.error('查询失败：' + error.message);
                    } finally {
                        loading.value = false;
                    }
                };

                const exportData = async () => {
                    if (!form.value.userId) {
                        ElMessage.warning('请输入用户ID');
                        return;
                    }
                    try {
                        const res = await axios.post('/plugin/Fengzeroexport/api/export', {
                            user_id: form.value.userId,
                            parent_id: form.value.parentId,
                            date_range: form.value.dateRange ? form.value.dateRange.join(' - ') : '',
                            keyword: form.value.keyword
                        }, {
                            responseType: 'blob'
                        });

                        const blob = new Blob([res.data], { 
                            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
                        });
                        const url = window.URL.createObjectURL(blob);
                        const link = document.createElement('a');
                        link.href = url;
                        link.download = `orders_${new Date().getTime()}.xlsx`;
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                        window.URL.revokeObjectURL(url);
                        
                        ElMessage.success('导出成功');
                    } catch (error) {
                        ElMessage.error('导出失败：' + error.message);
                    }
                };

                return {
                    form,
                    count,
                    goodsAmount,
                    actualAmount,
                    merchantAmount,
                    loading,
                    dateShortcuts,
                    queryData,
                    exportData,
                    hasFee,
                    zhCn
                };
            }
        });

        app.use(ElementPlus, {
            locale: zhCn
        });
        app.mount('#app');
    </script>
</body>

</html>