<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$siteName} - 客服系统</title>
    <link rel="shortcut icon" href="{$favicon}" type="image/x-icon">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css">
    <style>
        :root {
            --primary-color: #4c84ff;
            --primary-hover: #3a70e3;
            --text-primary: #303133;
            --text-secondary: #606266;
            --text-light: #909399;
            --bg-color: #f9fafc;
            --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            --border-radius: 12px;
            --transition: all 0.3s ease;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
            font-size: 14px;
            color: var(--text-primary);
            background-color: var(--bg-color);
            -webkit-font-smoothing: antialiased;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            line-height: 1.6;
        }
        
        /* 导航栏样式 */
        .header {
            background-color: #fff;
            box-shadow: var(--box-shadow);
            position: relative;
            z-index: 1000;
        }
        
        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            height: 70px;
        }
        
        .nav-toggle {
            display: none;
            flex-direction: column;
            justify-content: space-between;
            width: 24px;
            height: 18px;
            cursor: pointer;
            z-index: 1001;
        }
        
        .nav-toggle span {
            display: block;
            height: 2px;
            width: 100%;
            background-color: var(--text-primary);
            transition: var(--transition);
        }
        
        .nav-left {
            display: flex;
            align-items: center;
            flex: 1;
        }
        
        .logo-container {
            display: flex;
            align-items: center;
            cursor: pointer;
            margin-right: 40px;
        }
        
        .logo-img {
            height: 40px;
            border-radius: 6px;
            object-fit: contain;
        }
        
        .nav-menu {
            display: flex;
            align-items: center;
        }
        
        .nav-item {
            position: relative;
            color: var(--text-primary);
            text-decoration: none;
            padding: 8px 16px;
            margin: 0 3px;
            font-size: 15px;
            border-radius: 4px;
            transition: var(--transition);
        }
        
        .nav-item:hover, .nav-item.active {
            color: var(--primary-color);
            background-color: rgba(76, 132, 255, 0.08);
        }
        
        .nav-dropdown {
            position: relative;
        }
        
        .nav-dropdown-content {
            position: absolute;
            top: 100%;
            left: 0;
            background-color: #fff;
            min-width: 160px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
            border-radius: 6px;
            padding: 8px 0;
            opacity: 0;
            visibility: hidden;
            transform: translateY(10px);
            transition: opacity 0.3s ease, transform 0.3s ease, visibility 0.3s;
            z-index: 100;
        }
        
        .nav-dropdown:hover .nav-dropdown-content {
            opacity: 1;
            visibility: visible;
            transform: translateY(5px);
        }
        
        .dropdown-item {
            display: block;
            padding: 8px 16px;
            color: var(--text-primary);
            text-decoration: none;
            transition: var(--transition);
            font-size: 14px;
        }
        
        .dropdown-item:hover, .dropdown-item.active {
            background-color: rgba(76, 132, 255, 0.08);
            color: var(--primary-color);
        }
        
        .merchant-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: 14px;
            cursor: pointer;
            transition: var(--transition);
            outline: none;
            font-weight: 500;
        }
        
        .merchant-btn svg {
            margin-right: 6px;
            fill: currentColor;
        }
        
        .merchant-btn:hover {
            background-color: var(--primary-hover);
            transform: translateY(-2px);
        }
        
        .menu-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: none;
        }
        
        /* 原有样式继续保留 */
        .main {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 60px 20px;
            background: linear-gradient(135deg, rgba(245,247,250,0.8) 0%, rgba(240,242,245,0.9) 100%);
        }
        
        .closed-container {
            background: #fff;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            width: 100%;
            max-width: 550px;
            padding: 50px;
            text-align: center;
            transform: translateY(0);
            transition: transform 0.5s ease;
            animation: fadeIn 0.6s ease-out;
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .closed-icon {
            width: 110px;
            height: 110px;
            margin: 0 auto 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 45px;
            color: var(--primary-color);
            border-radius: 50%;
            background: linear-gradient(145deg, #f0f4ff, #eef3ff);
            box-shadow: 0 10px 25px rgba(76, 132, 255, 0.15);
            transition: var(--transition);
            animation: pulse 2s infinite ease-in-out;
        }
        
        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
            100% {
                transform: scale(1);
            }
        }
        
        .closed-title {
            font-size: 28px;
            color: var(--text-primary);
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .closed-message {
            font-size: 16px;
            color: var(--text-secondary);
            line-height: 1.8;
            margin-bottom: 35px;
        }
        
        .contact-options {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 20px;
            margin-bottom: 35px;
        }
        
        .contact-option {
            display: flex;
            align-items: center;
            background: #f9f9f9;
            padding: 12px 20px;
            border-radius: 8px;
            transition: var(--transition);
        }
        
        .contact-option:hover {
            background: #f0f4ff;
            transform: translateY(-3px);
        }
        
        .contact-option i {
            font-size: 20px;
            color: var(--primary-color);
            margin-right: 10px;
        }
        
        .btn-home {
            display: inline-block;
            line-height: 1;
            white-space: nowrap;
            cursor: pointer;
            background: var(--primary-color);
            border: 1px solid var(--primary-color);
            color: #fff;
            text-align: center;
            box-sizing: border-box;
            outline: none;
            margin: 0;
            transition: var(--transition);
            font-weight: 500;
            padding: 14px 28px;
            font-size: 15px;
            border-radius: 50px;
            text-decoration: none;
            box-shadow: 0 6px 16px rgba(76, 132, 255, 0.2);
        }
        
        .btn-home:hover {
            background: var(--primary-hover);
            border-color: var(--primary-hover);
            color: #fff;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(76, 132, 255, 0.3);
        }
        
        .btn-home i {
            margin-right: 8px;
        }
        
        .footer {
            text-align: center;
            padding: 25px;
            color: var(--text-light);
            font-size: 13px;
            background: #fff;
            box-shadow: 0 -2px 12px 0 rgba(0, 0, 0, 0.05);
        }
        
        .footer a {
            color: var(--text-light);
            text-decoration: none;
            transition: var(--transition);
        }
        
        .footer a:hover {
            color: var(--primary-color);
        }
        
        /* 响应式样式 */
        @media (max-width: 768px) {
            .nav-toggle {
                display: flex;
            }
            
            .nav-menu {
                position: fixed;
                top: 0;
                left: -280px;
                width: 280px;
                height: 100vh;
                background-color: #fff;
                flex-direction: column;
                align-items: flex-start;
                padding: 70px 20px 20px;
                transition: var(--transition);
                box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
                z-index: 1000;
                overflow-y: auto;
            }
            
            .nav-menu.active {
                left: 0;
            }
            
            .nav-item {
                margin: 8px 0;
                width: 100%;
            }
            
            .nav-dropdown {
                width: 100%;
            }
            
            .nav-dropdown-content {
                position: static;
                box-shadow: none;
                opacity: 1;
                visibility: visible;
                transform: none;
                margin-left: 16px;
                display: none;
                padding: 5px 0;
            }
            
            .nav-dropdown.active .nav-dropdown-content {
                display: block;
            }
            
            .closed-container {
                padding: 35px 25px;
                max-width: 90%;
            }
            
            .closed-icon {
                width: 90px;
                height: 90px;
                margin-bottom: 25px;
            }
            
            .closed-title {
                font-size: 22px;
                margin-bottom: 15px;
            }
            
            .closed-message {
                font-size: 14px;
                margin-bottom: 25px;
            }
            
            .contact-options {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>

<body>
    <!-- 新的导航栏结构 -->
    <div class="header">
        <div class="nav-container">
            <div class="nav-toggle" id="navToggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
            <div class="nav-left">
                <div class="logo-container" onclick="window.location.href='/'">
                    <img src="{$logo}" class="logo-img" alt="{$siteName}">
                </div>
                <div class="nav-menu" id="navMenu">
                    <!-- 使用ThinkPHP模板语法遍历导航菜单 -->
                    {volist name="navItems" id="item"}
                        <!-- 判断是否有子菜单 -->
                        {if condition="empty($item.subMenus)"}
                            <!-- 没有子菜单的普通菜单项 -->
                            <a href="{$item.href}" target="{$item.target == '_blank' ? '_blank' : '_self'}" 
                               class="nav-item {$item.active ? 'active' : ''}">
                                {$item.name}
                            </a>
                        {else/}
                            <!-- 有子菜单的下拉菜单项 -->
                            <div class="nav-dropdown">
                                <a href="javascript:void(0);" class="nav-item dropdown-trigger {$item.active ? 'active' : ''}">
                                    {$item.name} <span style="font-size:12px; margin-left:3px;">▼</span>
                                </a>
                                <div class="nav-dropdown-content">
                                    {volist name="item.subMenus" id="subItem"}
                                        <a href="{$subItem.href}" target="{$subItem.target == '_blank' ? '_blank' : '_self'}" 
                                           class="dropdown-item {if condition="request()->url(true) == $subItem.href"}active{/if}">
                                            {$subItem.name}
                                        </a>
                                    {/volist}
                                </div>
                            </div>
                        {/if}
                    {/volist}
                </div>
            </div>
            <button class="merchant-btn" onclick="window.location.href='/merchant/login'">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16">
                    <path d="M12 4a4 4 0 1 0 0 8 4 4 0 0 0 0-8zM6 8a6 6 0 1 1 12 0A6 6 0 0 1 6 8zm2 10a3 3 0 0 0-3 3 1 1 0 1 1-2 0 5 5 0 0 1 5-5h8a5 5 0 0 1 5 5 1 1 0 1 1-2 0 3 3 0 0 0-3-3H8z"/>
                </svg>
                商家中心
            </button>
        </div>
        <div class="menu-backdrop" id="menuBackdrop"></div>
    </div>

    <div class="main">
        <div class="closed-container">
            <div class="closed-icon">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="60" height="60" fill="currentColor">
                    <path d="M21.5,6h-19C1.673,6,1,6.673,1,7.5v13C1,21.327,1.673,22,2.5,22h19c0.827,0,1.5-0.673,1.5-1.5v-13C23,6.673,22.327,6,21.5,6z M7,20H4v-2h3V20z M7,16H4v-2h3V16z M7,12H4v-2h3V12z M14,20H8v-2h6V20z M14,16H8v-2h6V16z M14,12H8v-2h6V12z M20,20h-5v-2h5V20z M20,16h-5v-2h5V16z M20,12h-5v-2h5V12z M2.5,2c-0.827,0-1.5,0.673-1.5,1.5S1.673,5,2.5,5h19C22.327,5,23,4.327,23,3.5S22.327,2,21.5,2H2.5z"/>
                </svg>
            </div>
            <h2 class="closed-title">客服功能暂未开放</h2>
            <p class="closed-message">
                很抱歉，当前客服功能暂未开放，请稍后再试或通过以下方式与我们联系。<br>
                我们将尽快回复您的问题，感谢您的耐心等待！
            </p>
            
            <div class="contact-options">
                <div class="contact-option">
                    <i class="ri-mail-line"></i>
                    <span>发送邮件至客服邮箱</span>
                </div>
                <div class="contact-option">
                    <i class="ri-customer-service-2-line"></i>
                    <span>致电客服热线</span>
                </div>
                <div class="contact-option">
                    <i class="ri-question-answer-line"></i>
                    <span>查看常见问题解答</span>
                </div>
            </div>
            
            <a href="/" class="btn-home"><i class="ri-home-4-line"></i>返回首页</a>
        </div>
    </div>

    <div class="footer">
        <p>Copyright © {$siteName}. All Rights Reserved.</p>
        <p v-if="'{$icpNumber}'">
            <a href="https://beian.miit.gov.cn/" target="_blank">{$icpNumber}</a>
            <span v-if="'{$gaNumber}'"> | {$gaNumber}</span>
        </p>
    </div>

    <!-- 添加移动端导航菜单的JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const navToggle = document.getElementById('navToggle');
            const navMenu = document.getElementById('navMenu');
            const menuBackdrop = document.getElementById('menuBackdrop');
            const dropdownTriggers = document.querySelectorAll('.dropdown-trigger');
            
            // 切换导航菜单
            navToggle.addEventListener('click', function() {
                navMenu.classList.toggle('active');
                menuBackdrop.style.display = navMenu.classList.contains('active') ? 'block' : 'none';
                
                // 切换汉堡按钮样式
                const spans = navToggle.querySelectorAll('span');
                if (navMenu.classList.contains('active')) {
                    spans[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
                    spans[1].style.opacity = '0';
                    spans[2].style.transform = 'rotate(-45deg) translate(5px, -5px)';
                } else {
                    spans[0].style.transform = 'none';
                    spans[1].style.opacity = '1';
                    spans[2].style.transform = 'none';
                }
            });
            
            // 点击背景关闭菜单
            menuBackdrop.addEventListener('click', function() {
                navMenu.classList.remove('active');
                menuBackdrop.style.display = 'none';
                
                // 恢复汉堡按钮样式
                const spans = navToggle.querySelectorAll('span');
                spans[0].style.transform = 'none';
                spans[1].style.opacity = '1';
                spans[2].style.transform = 'none';
            });
            
            // 移动端下拉菜单处理
            if (window.innerWidth <= 768) {
                dropdownTriggers.forEach(trigger => {
                    trigger.addEventListener('click', function(e) {
                        e.preventDefault();
                        const parent = this.parentElement;
                        parent.classList.toggle('active');
                    });
                });
            }
        });
    </script>
</body>
</html> 