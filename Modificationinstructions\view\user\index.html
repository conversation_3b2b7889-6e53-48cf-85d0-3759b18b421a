<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上级商品使用说明修改</title>
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <style>
        /* 基础样式 */
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #f5f7fa;
            height: 100vh;
        }

        .container {
            padding: 20px;
            height: 100%;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .card {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .card-header {
            background-color: #f5f7fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e4e7ed;
            font-weight: bold;
            color: #303133;
            display: flex;
            align-items: center;
        }

        .card-body {
            padding: 20px;
            min-height: 300px;
        }

        .parent-info {
            margin-bottom: 15px;
            padding: 12px;
            background-color: #f8f8f8;
            border-radius: 4px;
        }

        /* 表格样式 */
        .el-table {
            --el-table-row-height: 55px;
            font-size: 14px;
        }

        .el-table th {
            background-color: #f5f7fa !important;
            font-weight: bold;
            color: #606266;
        }

        /* 美化滚动条 */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        ::-webkit-scrollbar-thumb {
            border-radius: 3px;
            background: #dcdfe6;
        }

        ::-webkit-scrollbar-track {
            border-radius: 3px;
            background: #f5f7fa;
        }

        /* 表单样式 */
        .el-form-item {
            margin-bottom: 20px;
        }

        .form-actions {
            margin-top: 20px;
            text-align: right;
        }

        /* 输入框 */
        .el-textarea__inner {
            min-height: 150px !important;
        }

        /* 字数统计 */
        .word-counter {
            text-align: right;
            color: #909399;
            font-size: 12px;
            margin-top: 5px;
        }

        .word-counter.exceeded {
            color: #F56C6C;
        }

        /* 弹窗 */
        .el-dialog {
            border-radius: 8px;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <div class="main-content">
                <div class="card">
                    <div class="card-header">
                        <i class="el-icon-edit"></i> 上级商品使用说明修改
                    </div>
                    <div class="card-body">
                        <!-- 搜索区域 -->
                        <el-row :gutter="20" style="margin-bottom: 20px;">
                            <el-col :span="8">
                                <el-input
                                    v-model="searchKey"
                                    placeholder="搜索商品名称"
                                    clearable
                                    @keyup.enter="handleSearch"
                                    @clear="handleSearch">
                                    <template #append>
                                        <el-button @click="handleSearch">
                                            <i class="el-icon-search"></i>
                                        </el-button>
                                    </template>
                                </el-input>
                            </el-col>
                        </el-row>
                        
                        <!-- 商品列表表格 -->
                        <el-table
                            :data="goodsList"
                            v-loading="loading"
                            style="width: 100%">
                            <el-table-column prop="id" label="ID" width="60" sortable></el-table-column>
                            <el-table-column prop="name" label="商品名称" min-width="180"></el-table-column>
                            <el-table-column prop="parent_name" label="上级商品" min-width="180"></el-table-column>
                            <el-table-column prop="parent_nickname" label="上级商家" width="150"></el-table-column>
                            <el-table-column label="操作" width="180" align="center">
                                <template #default="scope">
                                    <el-button type="primary" size="small" @click="editInstructions(scope.row.id)">
                                        编辑使用说明
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                        
                        <!-- 分页 -->
                        <div style="margin-top: 20px; text-align: center;">
                            <el-pagination
                                v-model:current-page="currentPage"
                                v-model:page-size="pageSize"
                                :page-sizes="[10, 20, 50]"
                                layout="total, sizes, prev, pager, next, jumper"
                                :total="total"
                                @size-change="handleSizeChange"
                                @current-change="handleCurrentChange">
                            </el-pagination>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 编辑使用说明弹窗 -->
        <el-dialog
            v-model="editDialogVisible"
            title="修改使用说明"
            width="700px"
            :close-on-click-modal="false"
            :destroy-on-close="true">
            <div v-loading="editLoading">
                <!-- 商品信息 -->
                <div class="parent-info">
                    <p><b>商品名称:</b> {{ currentGoods.goods_name }}</p>
                    <p><b>上级商品:</b> {{ currentGoods.parent_name }} (商家: {{ currentGoods.parent_nickname }})</p>
                </div>
                
                <!-- 表单 -->
                <el-form label-width="140px">
                    <el-form-item label="上级商品使用说明">
                        <el-input
                            type="textarea"
                            v-model="currentGoods.parent_instructions"
                            readonly>
                        </el-input>
                        <div style="text-align: right; margin-top: 5px;">
                            <el-button
                                type="text"
                                @click="copyParentInstructions">
                                一键复制上级内容
                            </el-button>
                        </div>
                    </el-form-item>
                    
                    <el-form-item label="当前使用说明">
                        <el-input
                            type="textarea"
                            v-model="currentGoods.my_instructions">
                        </el-input>
                        <div :class="['word-counter', { 'exceeded': isLengthExceeded }]">
                            {{ currentGoods.my_instructions.length }}/{{ maxLength }}
                        </div>
                    </el-form-item>
                </el-form>
            </div>
            
            <template #footer>
                <span class="dialog-footer">
                    <el-button 
                        type="primary"
                        @click="submitChange"
                        :disabled="isLengthExceeded">
                        提交修改
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>
    
    <script>
        const { createApp, ref, computed, onMounted } = Vue;
        
        const app = createApp({
            setup() {
                // 列表数据
                const goodsList = ref([]);
                const currentPage = ref(1);
                const pageSize = ref(10);
                const total = ref(0);
                const loading = ref(false);
                const searchKey = ref('');
                
                // 编辑弹窗
                const editDialogVisible = ref(false);
                const editLoading = ref(false);
                const currentGoods = ref({
                    goods_name: '',
                    parent_name: '',
                    parent_nickname: '',
                    parent_instructions: '',
                    my_instructions: ''
                });
                const currentGoodsId = ref(0);
                const maxLength = ref(2000);
                
                // 系统配置
                const sysConfig = ref({
                    max_length: 2000
                });
                
                // 计算属性
                const isLengthExceeded = computed(() => {
                    return currentGoods.value.my_instructions.length > maxLength.value;
                });
                
                // 获取配置
                const getConfig = async () => {
                    try {
                        const res = await axios.get("/plugin/Modificationinstructions/user/getConfig");
                        if (res.data.code === 200) {
                            sysConfig.value = res.data.data;
                            maxLength.value = sysConfig.value.max_length || 2000;
                        }
                    } catch (err) {
                        console.error('获取配置失败:', err);
                        ElementPlus.ElMessage.error('获取配置失败');
                    }
                };
                
                // 加载商品列表
                const loadGoodsList = async () => {
                    try {
                        loading.value = true;
                        const res = await axios.get("/plugin/Modificationinstructions/user/getParentGoodsList", {
                            params: {
                                page: currentPage.value,
                                limit: pageSize.value,
                                search: searchKey.value
                            }
                        });
                        
                        if (res.data.code === 200) {
                            goodsList.value = res.data.data;
                            total.value = res.data.count || res.data.total;
                        } else {
                            ElementPlus.ElMessage.error(res.data.msg || '获取商品列表失败');
                        }
                    } catch (err) {
                        console.error('加载商品列表失败:', err);
                        ElementPlus.ElMessage.error('加载商品列表失败');
                    } finally {
                        loading.value = false;
                    }
                };
                
                // 搜索处理
                const handleSearch = () => {
                    currentPage.value = 1;
                    loadGoodsList();
                };
                
                // 分页处理
                const handleSizeChange = (val) => {
                    pageSize.value = val;
                    loadGoodsList();
                };
                
                const handleCurrentChange = (val) => {
                    currentPage.value = val;
                    loadGoodsList();
                };
                
                // 打开编辑弹窗
                const editInstructions = async (goodsId) => {
                    try {
                        currentGoodsId.value = goodsId;
                        editDialogVisible.value = true;
                        editLoading.value = true;
                        
                        const res = await axios.get("/plugin/Modificationinstructions/user/getParentInstructions", {
                            params: {
                                goods_id: goodsId
                            }
                        });
                        
                        if (res.data.code === 200) {
                            currentGoods.value = res.data.data;
                        } else {
                            ElementPlus.ElMessage.error(res.data.msg || '获取商品信息失败');
                            editDialogVisible.value = false;
                        }
                    } catch (err) {
                        console.error('获取商品信息失败:', err);
                        ElementPlus.ElMessage.error('获取商品信息失败');
                        editDialogVisible.value = false;
                    } finally {
                        editLoading.value = false;
                    }
                };
                
                // 复制上级使用说明
                const copyParentInstructions = () => {
                    currentGoods.value.my_instructions = currentGoods.value.parent_instructions;
                };
                
                // 提交修改
                const submitChange = async () => {
                    if (currentGoods.value.my_instructions.length > maxLength.value) {
                        ElementPlus.ElMessage.warning('内容超过最大长度限制');
                        return;
                    }
                    
                    try {
                        await ElementPlus.ElMessageBox.confirm('确定要提交修改吗？', '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        });
                        
                        const res = await axios.post("/plugin/Modificationinstructions/user/updateInstructions", {
                            goods_id: currentGoodsId.value,
                            instructions: currentGoods.value.my_instructions
                        });
                        
                        if (res.data.code === 200) {
                            ElementPlus.ElMessage.success(res.data.msg);
                            editDialogVisible.value = false;
                            loadGoodsList();
                        } else {
                            ElementPlus.ElMessage.error(res.data.msg);
                        }
                    } catch (err) {
                        if (err !== 'cancel') {
                            console.error('提交修改失败:', err);
                            ElementPlus.ElMessage.error('提交修改失败');
                        }
                    }
                };
                
                // 格式化时间
                const formatTime = (timestamp) => {
                    if (!timestamp) return '-';
                    return new Date(timestamp * 1000).toLocaleString();
                };
                
                // 初始化
                onMounted(() => {
                    getConfig();
                    loadGoodsList();
                });
                
                return {
                    // 列表数据
                    goodsList,
                    currentPage,
                    pageSize,
                    total,
                    loading,
                    searchKey,
                    
                    // 编辑相关
                    editDialogVisible,
                    editLoading,
                    currentGoods,
                    currentGoodsId,
                    maxLength,
                    isLengthExceeded,
                    
                    // 方法
                    handleSearch,
                    handleSizeChange,
                    handleCurrentChange,
                    editInstructions,
                    copyParentInstructions,
                    submitChange,
                    formatTime
                };
            }
        });
        
        app.use(ElementPlus, {
            locale: ElementPlusLocaleZhCn
        }).mount('#app');
    </script>
</body>
</html> 