<?php
// 自动上货插件参数配置

return [
    // 插件基本信息
    'version' => '1.0.0',
    'author' => '系统开发组',
    'description' => '自动上货模块，用于批量导入账号卡密',
    
    // 自动上货功能配置
    'autoload_config' => [
        'status' => true,  // 功能开关
        'match_patterns' => [
            '角色昵称' => '角色昵称[:：]\\s*([^|\\n]+)',
            '等级' => '等级[:：]\\s*([0-9]+)',
            '仓库价值' => '仓库价值v?[:：]\\s*([0-9\\.]+[MmKkGg]?)',
            '哈夫币' => '哈夫币[:：]\\s*([0-9]+)',
            '道具价值' => '道具价值[:：]\\s*([0-9]+)',
            '在线状态' => '在线状态[:：]\\s*([^|\\n]+)',
            '今日登录' => '今日登录[:：]\\s*([^|\\n]+)',
            '封号' => '封号[:：]\\s*([^|\\n]+)',
            '最后登录时间' => '最后登录时间[:：]\\s*([^|\\n]+)',
            '最后退出间' => '最后退出间[:：]\\s*([^|\\n]+)'
        ],
        // 这里可以添加其他配置
    ],
]; 