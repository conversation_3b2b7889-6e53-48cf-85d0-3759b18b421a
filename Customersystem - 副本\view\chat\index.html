﻿<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$siteName} - 在线客服</title>
    <link rel="shortcut icon" href="{$favicon}" type="image/x-icon">
    <!-- 引入Element UI -->
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <style>
        :root {
            --primary-color: #4e6ef2;
            --secondary-color: #5E81AC;
            --bg-color: #f8fafc;
            --text-color: #1e293b;
            --border-color: #e0e7ee;
            --shadow-color: rgba(0, 0, 0, 0.08);
            --transition-time: 0.3s;
            --button-primary: #4e6ef2; 
            --button-hover: #4050d2;
            --card-bg: #ffffff;
            --message-staff-bg: #eef2ff;
            --message-customer-bg: #f8fafc;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
        }

        /* 添加订单卡片样式 */
        .order-card {
            background: #f8f9ff;
            border: 1px solid #e0e7ee;
            border-left: 4px solid var(--primary-color);
            border-radius: 8px;
            padding: 12px 15px;
            margin: 5px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        
        .order-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            transform: translateY(-2px);
        }
        
        .order-card .order-header {
            border-bottom: 1px solid #eaecf3;
            margin-bottom: 10px;
            padding-bottom: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .order-card .order-header .merchant-name {
            font-weight: 600;
            color: #333;
            font-size: 15px;
        }
        
        .order-card .order-header .order-badge {
            background: var(--primary-color);
            color: white;
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 12px;
        }
        
        .order-card .order-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 6px 0;
        }
        
        .order-card .order-key {
            color: #666;
            font-weight: 500;
        }
        
        .order-card .order-value {
            color: #333;
        }
        
        .order-card .order-value.order-id {
            color: #1e293b;
            font-weight: 500;
            font-family: monospace;
        }
        
        .order-card .order-value.order-amount {
            color: #e53e3e;
            font-weight: 600;
        }
        
        .order-card .order-value.order-status-success {
            color: var(--success-color);
            font-weight: 600;
        }
        
        .order-card .order-value.order-status-pending {
            color: var(--warning-color);
            font-weight: 600;
        }
        
        .order-card .order-value.order-status-failed {
            color: var(--error-color);
            font-weight: 600;
        }
        
        .order-card .order-value.order-time {
            color: #6b7280;
            font-weight: 500;
        }
        
        /* 移动设备上的订单卡片样式调整 */
        @media (max-width: 768px) {
            .order-card {
                padding: 10px 12px;
            }
            
            .order-card .order-header {
                padding-bottom: 6px;
                margin-bottom: 8px;
            }
            
            .order-card .order-header .merchant-name {
                font-size: 14px;
            }
            
            .order-card .order-header .order-badge {
                font-size: 11px;
                padding: 1px 6px;
            }
            
            .order-card .order-item {
                margin: 4px 0;
                font-size: 13px;
            }
        }
        
        /* 加载动画样式 */
        #loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: rgba(255, 255, 255, 0.95);
            z-index: 9999;
            opacity: 1;
            transition: opacity 0.5s ease-out;
        }

        #loading.fade-out {
            opacity: 0;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(78, 110, 242, 0.1);
            border-top-color: var(--button-primary);
            border-radius: 50%;
            animation: spin 1s cubic-bezier(0.4, 0, 0.2, 1) infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 确保内容在加载时隐藏 */
        #app {
            visibility: hidden;
            opacity: 0;
            transition: opacity 0.5s ease-in;
            min-height: calc(100vh - 100px); /* 确保足够的高度 */
        }

        #app.loaded {
            visibility: visible;
            opacity: 1;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
            font-size: 15px;
            color: var(--text-color);
            background-color: var(--bg-color);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            line-height: 1.6;
            letter-spacing: 0.01em;
        }
        
        /* 添加自定义元素样式，覆盖Element UI的默认负边距 */
        .el-row {
            margin-left: 0 !important;
            margin-right: 0 !important;
        }
        
        .container {
            max-width: 1240px;
            margin: 0 auto;
            padding: 15px 25px; /* 调整垂直内边距 */
            overflow: hidden; /* 防止溢出 */
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px; /* 移动端减少内边距 */
            }
        }
        
        /* 导航栏样式 */
        .header {
            background: #fff;
            box-shadow: 0 1px 5px rgba(0,0,0,0.03);
            padding: 0;
            position: sticky;
            top: 0;
            z-index: 100;
            border-bottom: 1px solid #f0f0f0;
        }

        .nav-container {
            max-width: 1260px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 60px;
        }

        .nav-left {
            display: flex;
            align-items: center;
            gap: 50px;
        }

        .logo-container {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            margin-left: 20px;
        }

        .logo-img {
            height: 42px;
            width: auto;
            transition: transform 0.35s ease;
        }

        .nav-menu {
            display: flex;
            gap: 32px; /* 调整菜单项之间的间距 */
            height: 100%;
            align-items: center;
        }

        .nav-item {
            display: inline-flex;
            align-items: center;
            height: 60px;
            cursor: pointer;
            color: #333;
            text-decoration: none;
            font-size: 15px;
            position: relative;
            padding: 0;
            transition: all 0.35s ease;
            font-weight: 400;
        }

        .nav-item:hover {
            color: var(--button-primary);
        }

        .nav-item.active {
            color: var(--button-primary);
            font-weight: 400; /* 活动项也不加粗 */
        }

        .nav-item.active::after {
            content: '';
            position: absolute;
            bottom: 0; /* 修改活动项底部线条位置 */
            left: 0;
            width: 100%;
            height: 2px;
            background: var(--button-primary);
            border-radius: 1px;
        }

        /* 下拉菜单项 */
        .nav-dropdown {
            position: relative;
            height: 100%;
            display: flex;
            align-items: center;
        }

        .dropdown-trigger {
            cursor: pointer;
        }

        .nav-dropdown-content {
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            min-width: 160px;
            background-color: #fff;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            padding: 5px 0;
            z-index: 1000; /* 确保下拉菜单在其他元素之上 */
            display: none;
            margin-top: 0; /* 调整下拉菜单与导航栏的距离 */
            border: 1px solid #eaeaea;
        }

        .nav-dropdown:hover .nav-dropdown-content {
            display: block;
        }

        .dropdown-item {
            display: block;
            padding: 12px 20px;
            text-decoration: none;
            color: #333;
            font-size: 15px; /* 调整下拉菜单项字体大小 */
            transition: all 0.2s;
            text-align: center;
            white-space: nowrap;
        }

        .dropdown-item:hover {
            background-color: #f0f5ff;
            color: var(--button-primary);
        }
        
        /* 商家中心按钮样式 */
        .merchant-btn {
            background-color: #4e6ef2;
            color: white;
            border: none;
            padding: 0 24px;
            border-radius: 6px;
            font-size: 15px;
            font-weight: 400;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.35s ease;
            line-height: 38px;
            height: 38px;
            margin-right: 24px;
        }
        
        .merchant-btn:hover {
            background-color: #4458d9;
            box-shadow: 0 4px 12px rgba(78, 110, 242, 0.25);
            transform: translateY(-2px);
        }
        
        .merchant-btn svg {
            width: 14px;
            height: 14px;
            fill: white;
        }
        
        .main {
            display: flex;
            flex-direction: column;
            min-height: calc(100vh - 120px); /* 确保足够的高度减去header高度和padding */
        }
        
        /* 页脚简约风格 */
        .footer {
            text-align: center;
            padding: 20px 0;
            background: #f9fafb;
            border-top: 1px solid #edf2f7;
            font-size: 13px;
            color: #6b7280;
            margin-top: 40px;
        }

        .footer-content {
            max-width: 1240px;
            margin: 0 auto;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }

        .footer-item {
            display: inline-flex;
            align-items: center;
        }

        .footer-item a {
            color: #6b7280;
            text-decoration: none;
            transition: color 0.25s;
        }

        .footer-item a:hover {
            color: #4e6ef2;
        }

        .footer-divider {
            color: #ddd;
            margin: 0 10px;
        }

        .beian-icon {
            height: 14px;
            margin-right: 6px;
        }

        @media (max-width: 768px) {
            .footer-content {
                flex-direction: column;
                gap: 10px;
            }
            
            .footer-divider {
                display: none;
            }
        }
        
        /* 修改聊天容器样式 */
        .chat-container {
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 20px var(--shadow-color);
            overflow: hidden;
            display: flex;
            flex-direction: column;
            height: 100%;
            min-height: 600px;
            transition: all var(--transition-time);
            border: 1px solid var(--border-color);
            margin-bottom: 0; /* 移除底部间距 */
            margin-top: 0; /* 确保无顶部间距 */
        }
        
        .chat-header {
            padding: 18px 24px;
            background: var(--primary-color);
            color: #fff;
            font-size: 17px;
            font-weight: 500;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: none;
            box-shadow: 0 2px 10px rgba(78, 110, 242, 0.2);
            height: 60px; /* 添加固定高度 */
            box-sizing: border-box; /* 确保padding不会增加元素高度 */
            position: sticky; /* 使其粘性定位 */
            top: 0; /* 置顶 */
            z-index: 10; /* 确保在层级上覆盖其他元素 */
        }
        
        .chat-body {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background-color: var(--bg-color);
            scroll-behavior: smooth;
            background-image: linear-gradient(rgba(78, 110, 242, 0.02) 1px, transparent 1px), 
                              linear-gradient(90deg, rgba(78, 110, 242, 0.02) 1px, transparent 1px);
            background-size: 20px 20px;
        }
        
        .chat-body::-webkit-scrollbar {
            width: 6px;
        }
        
        .chat-body::-webkit-scrollbar-thumb {
            background: rgba(78, 110, 242, 0.15);
        }
        
        .chat-body::-webkit-scrollbar-thumb:hover {
            background: rgba(78, 110, 242, 0.25);
        }
        
        .chat-footer {
            padding: 18px 24px;
            border-top: 1px solid var(--border-color);
            background: #fff;
        }
        
        .message {
            margin-bottom: 20px;
            display: flex;
            opacity: 0;
            transform: translateY(20px);
            animation: messageIn 0.3s ease forwards;
        }
        
        @keyframes messageIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .message.staff {
            flex-direction: row;
        }
        
        .message.customer {
            flex-direction: row-reverse;
        }
        
        .message.merchant {
            flex-direction: row;
        }
        
        .message-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 16px;
            margin: 0 10px;
            flex-shrink: 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            font-weight: bold;
        }
        
        .message.staff .message-avatar {
            background: var(--primary-color);
        }
        
        .message.customer .message-avatar {
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        }
        
        .message.merchant .message-avatar {
            background: linear-gradient(135deg, #f59e0b, #ea580c);
        }
        
        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 12px;
            position: relative;
            word-break: break-word;
            line-height: 1.5;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        
        .message.staff .message-content {
            background: var(--message-staff-bg);
            color: #333;
            margin-right: 50px;
            border-top-left-radius: 4px;
        }
        
        .message.customer .message-content {
            background: var(--message-customer-bg);
            color: #333;
            margin-left: 50px;
            border-top-right-radius: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
        }
        
        .message.merchant .message-content {
            background: #fff8e1;
            color: #333;
            margin-right: 50px;
            border-top-left-radius: 4px;
        }
        
        .message-time-small {
            font-size: 12px;
            color: #94a3b8;
            margin-top: 6px;
        }
        
        .message-time {
            font-size: 12px;
            color: #94a3b8;
            text-align: center;
            margin: 16px 0;
            position: relative;
        }
        
        .message-time:before,
        .message-time:after {
            content: '';
            position: absolute;
            top: 50%;
            width: 80px;
            height: 1px;
            background: #e5e7eb;
        }
        
        .message-time:before {
            left: 20%;
        }
        
        .message-time:after {
            right: 20%;
        }
        
        /* 修改系统消息样式 */
        .message.system .message-content {
            background: #f8fafc;
            color: #64748b;
            margin: 10px auto;
            text-align: center;
            max-width: 90%;
            border: 1px solid #e5e7eb;
            box-shadow: none;
            font-size: 13px;
            border-radius: 8px;
        }
        
        /* 进一步简化气泡样式 */
        .message-image {
            max-width: 100%;
            max-height: 320px;
            border-radius: 8px;
            cursor: pointer;
            border: 1px solid #e0e7ee;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            display: block;
            margin: 5px auto;
        }
        
        .message-image:hover {
            transform: scale(1.02);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12);
        }
        
        /* 添加图片加载动画 */
        @keyframes imageFadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .message-image {
            animation: imageFadeIn 0.3s ease forwards;
        }
        
        /* 美化消息入场动画 */
        @keyframes messageIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .message {
            animation: messageIn 0.3s ease forwards;
            margin-bottom: 24px;
            opacity: 0;
        }
        
        .message-file {
            display: flex;
            align-items: center;
            background: #f5f7fa;
            padding: 10px 14px;
            border-radius: 8px;
            cursor: pointer;
            border: 1px solid #e0e7ee;
            transition: all 0.25s ease;
            margin-top: 5px;
        }
        
        .message-file:hover {
            background: #eef2ff;
            border-color: #d0d7f7;
        }
        
        .message-file i {
            font-size: 20px;
            margin-right: 12px;
            color: var(--primary-color);
        }
        
        /* 对方正在输入的提示 - 已废弃，使用独立的typing-indicator替代 */
        /* 
        .message.staff .message-content span {
            display: inline-block;
            animation: typing 1.5s infinite;
        }
        */
        
        @keyframes typing {
            0% { opacity: 0.3; }
            50% { opacity: 1; }
            100% { opacity: 0.3; }
        }
        
        /* 新增：独立的"正在输入"提示样式 */
        .typing-indicator {
            text-align: center;
            padding: 8px 12px;
            margin: 10px 0;
            background-color: rgba(255,255,255,0.9);
            border-radius: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: inline-block;
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 10;
            font-size: 14px;
            color: #64748b;
            animation: typing 1.5s infinite;
        }
        
        .user-form {
            background: #fff;
            padding: 35px;
            border-radius: 12px;
            box-shadow: 0 4px 24px 0 var(--shadow-color);
            margin-bottom: 25px;
            transition: all 0.4s ease;
        }
        
        .user-form:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.07);
        }
        
        .emoji-picker {
            margin-right: 14px;
        }
        
        .emoji-item {
            font-size: 20px;
            cursor: pointer;
            margin: 5px;
            display: inline-block;
        }
        
        .emoji-item:hover {
            transform: scale(1.15);
        }
        
        /* 上传预览优化 */
        .upload-preview {
            margin: 10px 0 15px;
            position: relative;
            display: inline-block;
            background: #f8fafc;
            padding: 10px;
            border: 1px solid #e0e7ee;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
        }
        
        .upload-preview:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .upload-preview img {
            max-height: 160px;
            max-width: 100%;
            border-radius: 6px;
            transition: transform 0.3s ease;
            display: block;
            margin: 0 auto;
        }
        
        .upload-preview img:hover {
            transform: scale(1.03);
        }
        
        .upload-preview .file-name {
            font-size: 13px;
            color: #64748b;
            margin-top: 6px;
            text-align: center;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .upload-preview .el-icon-close {
            position: absolute;
            top: -10px;
            right: -10px;
            background: #ef4444;
            color: #fff;
            border-radius: 50%;
            padding: 7px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.25s ease;
            box-shadow: 0 2px 5px rgba(239, 68, 68, 0.3);
        }
        
        .upload-preview .el-icon-close:hover {
            transform: scale(1.1);
            background: #dc2626;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 12px;
            }
            
            .chat-container {
                height: calc(100vh - 120px) !important;
                border-radius: 0;
                margin-bottom: 0;
            }
            
            .message-content {
                max-width: 85%;
            }
            
            .message-time:before,
            .message-time:after {
                width: 60px;
            }
            
            .user-form {
                padding: 25px;
            }
            
            /* 移动设备上的typing-indicator样式调整 */
            .typing-indicator {
                bottom: 5px;
                padding: 6px 10px;
                font-size: 12px;
                max-width: 90%;
            }
        }
        
        /* 添加过渡动画 */
        .fade-enter-active,
        .fade-leave-active {
            transition: opacity 0.4s ease;
        }
        
        .fade-enter-from,
        .fade-leave-to {
            opacity: 0;
        }
        
        /* 优化按钮样式 */
        .el-button {
            transition: all 0.3s ease;
            border-radius: 8px;
            font-weight: 500;
        }
        
        .el-button--primary {
            background-color: var(--primary-color) !important;
            border-color: var(--primary-color) !important;
            border-radius: 8px !important;
            padding: 12px 24px !important;
            font-size: 15px !important;
            font-weight: 500 !important;
            letter-spacing: 0.01em !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 2px 5px rgba(78, 110, 242, 0.25) !important;
        }
        
        .el-button--primary:hover, 
        .el-button--primary:focus {
            background-color: var(--button-hover) !important;
            border-color: var(--button-hover) !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 12px rgba(78, 110, 242, 0.3) !important;
        }
        
        .el-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px var(--shadow-color);
        }
        
        /* 优化输入框样式 */
        .el-input__inner,
        .el-textarea__inner {
            transition: all 0.3s ease;
            border-radius: 8px;
            border-color: #e2e8f0;
        }
        
        .el-input__inner:focus,
        .el-textarea__inner:focus {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px var(--shadow-color);
            border-color: var(--button-primary);
        }
        
        /* 添加上传按钮样式 */
        .upload-button {
            display: inline-flex;
            align-items: center;
            padding: 8px 12px;
            background: #f7f7f7;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #666;
            font-size: 14px;
            margin-right: 12px;
        }
        
        .upload-button:hover {
            background: #f0f0f0;
        }
        
        .upload-button i {
            margin-right: 8px;
            font-size: 18px;
            color: var(--button-primary);
        }
        
        .upload-button span {
            line-height: 1;
        }
        
        .el-upload__input {
            display: none;
        }

        /* FAQ容器样式 */
        .faq-container {
            margin-top: 35px;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
            padding: 30px;
            transition: all 0.4s ease;
        }

        .faq-container:hover {
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.06);
        }

        .faq-container h3 {
            margin-bottom: 25px;
            font-size: 20px;
            color: #1e293b;
            position: relative;
            padding-left: 16px;
            font-weight: 600;
        }

        .faq-container h3::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 22px;
            background: var(--button-primary);
            border-radius: 2px;
        }

        .el-collapse {
            border: none;
        }

        .el-collapse-item__header {
            font-size: 16px;
            font-weight: 500;
            color: #334155;
            padding: 14px 16px;
            border-radius: 8px;
            background: #f8fafc;
            margin-bottom: 12px;
            border: none;
            transition: all 0.3s;
        }

        .el-collapse-item__header:hover {
            background: #f0f5ff;
            color: var(--button-primary);
        }

        .el-collapse-item__wrap {
            border: none;
            padding: 0 15px;
        }

        .el-collapse-item__content {
            padding-bottom: 18px;
        }

        .faq-item {
            padding: 16px;
            border-radius: 10px;
            margin-bottom: 14px;
            background: #f8fafc;
            cursor: pointer;
            transition: all 0.35s;
            border-left: 3px solid transparent;
        }

        .faq-item:hover {
            background: #f0f5ff;
            transform: translateX(5px);
            border-left: 3px solid var(--button-primary);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.04);
        }

        .faq-item p {
            margin: 0;
            line-height: 1.7;
        }

        .faq-item p:first-child {
            margin-bottom: 10px;
            color: #334155;
        }

        .faq-item .question-icon {
            color: var(--button-primary);
            margin-right: 8px;
            font-weight: bold;
        }

        .faq-item .answer-icon {
            color: var(--secondary-color);
            margin-right: 8px;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .faq-container {
                padding: 20px;
            }
            
            .faq-item {
                padding: 14px;
            }
        }

        /* 历史会话侧边栏 */
        .history-sidebar {
            height: calc(100vh - 110px);
        }

        .user-form.compact {
            font-size: 0.95em;
        }

        .user-form.compact h2 {
            font-size: 1.5em;
        }

        /* 历史会话卡片 */
        .el-card {
            border-radius: 10px;
            overflow: hidden;
            transition: all 0.3s ease;
            border: 1px solid #e2e8f0;
        }

        .el-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.05);
            border-color: #cbd5e1;
        }

        .has-unread {
            border-left: 3px solid var(--error-color);
        }

        .session-badge {
            margin-left: 8px;
        }

        .el-card__header {
            background: #f8fafc;
            padding: 14px 16px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        /* 会话标题样式 */
        .session-title {
            margin-bottom: 10px; 
            font-weight: 500; 
            text-align: center; 
            background: #f0f7ff; 
            padding: 8px 12px; 
            border-radius: 6px;
            border: 1px solid #e0eaff;
            color: #3b82f6;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
            position: relative;
        }
        
        /* 切换按钮样式 */
        .switch-btn {
            transition: all 0.3s ease;
        }
        
        .switch-btn:hover {
            background: #e0eaff !important;
            transform: scale(1.05);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .switch-btn:active {
            transform: scale(0.98);
        }
        
        .session-title:before {
            content: '';
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 12px;
            background: #3b82f6;
            border-radius: 2px;
        }

        @media (max-width: 992px) {
            .history-sidebar {
                margin-top: 25px;
            }
        }

        /* 添加收起/展开箭头的样式 */
        .toggle-history-btn {
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .toggle-history-btn:hover {
            background: #4050d2 !important;
        }

        .toggle-history-btn.collapsed {
            right: -40px !important;
        }

        .toggle-history-btn:not(.collapsed) {
            right: -40px !important;
        }

        /* 添加竖排文字样式 */
        .vertical-text {
            writing-mode: vertical-lr;
            text-orientation: upright;
            font-size: 12px;
            letter-spacing: 2px;
            font-weight: 500;
            text-align: center;
            line-height: 1.2;
        }

        /* 移动端历史对话按钮样式 */
        @media (max-width: 768px) {
            .toggle-history-btn {
                width: 140px !important;
                height: 40px !important;
                right: 15px !important;
                top: auto !important;
                bottom: 15px !important;
                border-radius: 20px !important;
                flex-direction: row !important;
                justify-content: center !important;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
                padding: 0 15px !important;
            }

            .toggle-history-btn .vertical-text {
                writing-mode: horizontal-tb !important;
                text-orientation: mixed !important;
                font-size: 14px !important;
                letter-spacing: normal !important;
                margin-left: 8px !important;
                white-space: nowrap !important;
            }
            
            .toggle-history-btn.collapsed, 
            .toggle-history-btn:not(.collapsed) {
                right: 15px !important;
            }
        }

        /* 自定义消息提示样式 */
        .custom-message {
            background-color: rgba(78, 110, 242, 0.95) !important;
            color: white !important;
            border-radius: 10px !important;
            padding: 14px 20px !important;
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12) !important;
            font-weight: 500 !important;
        }
        
        /* 自定义加载遮罩 */
        .el-loading-mask {
            background-color: rgba(255, 255, 255, 0.92) !important;
            backdrop-filter: blur(3px);
        }
        
        .el-loading-spinner .path {
            stroke: var(--primary-color) !important;
        }
        
        .el-loading-spinner .el-loading-text {
            color: var(--primary-color) !important;
            font-size: 15px !important;
            margin-top: 10px !important;
            font-weight: 500;
        }
        
        /* 未读消息动画 */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.03); }
            100% { transform: scale(1); }
        }
        
        .has-unread {
            animation: pulse 2s infinite;
        }
        
        /* 圆形按钮动画 */
        .el-button.is-circle:hover {
            transform: rotate(15deg) scale(1.1);
        }
        
        /* 增强表单样式 */
        .el-form-item__label {
            font-weight: 500;
            color: #475569;
        }
        
        .el-input__inner {
            border-radius: 8px !important;
            padding: 0 16px !important;
            height: 40px !important;
            transition: all 0.3s ease;
            border-color: #e2e8f0 !important;
        }
        
        .el-input__inner:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(78, 110, 242, 0.1) !important;
        }
        
        /* 历史会话增强 */
        .history-sessions {
            padding: 0 5px;
        }

        /* 美化输入区域和按钮 */
        .el-textarea__inner {
            border-radius: 8px !important;
            border-color: #e2e8f0 !important; 
            padding: 12px 16px !important;
            font-size: 15px !important;
            resize: none !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02) !important;
            background-color: #f8fafc !important;
        }
        
        .el-textarea__inner:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(78, 110, 242, 0.1) !important;
            background-color: #fff !important;
        }
        
        .el-button--primary {
            background-color: var(--primary-color) !important;
            border-color: var(--primary-color) !important;
            border-radius: 8px !important;
            padding: 12px 24px !important;
            font-size: 15px !important;
            font-weight: 500 !important;
            letter-spacing: 0.01em !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 2px 5px rgba(78, 110, 242, 0.25) !important;
        }
        
        .el-button--primary:hover, 
        .el-button--primary:focus {
            background-color: var(--button-hover) !important;
            border-color: var(--button-hover) !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 12px rgba(78, 110, 242, 0.3) !important;
        }

        /* 美化滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.02);
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 4px;
            transition: all 0.2s ease;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 0, 0, 0.2);
        }
        
        .chat-body::-webkit-scrollbar {
            width: 6px;
        }
        
        .chat-body::-webkit-scrollbar-thumb {
            background: rgba(78, 110, 242, 0.15);
        }
        
        .chat-body::-webkit-scrollbar-thumb:hover {
            background: rgba(78, 110, 242, 0.25);
        }

        /* 添加移动端导航菜单样式 */
        .nav-toggle {
            display: none;
            flex-direction: column;
            justify-content: space-between;
            width: 24px;
            height: 20px;
            margin-left: 15px;
            cursor: pointer;
            z-index: 101;
            position: relative;
            padding: 10px;
            margin: -10px 5px -10px -5px;
        }

        .nav-toggle span {
            display: block;
            height: 3px;
            width: 100%;
            background-color: #333;
            border-radius: 3px;
            transition: all 0.3s ease;
            transform-origin: center;
        }

        /* 汉堡菜单动画 */
        .nav-toggle.active span:nth-child(1) {
            transform: translateY(8.5px) rotate(45deg);
            background-color: var(--primary-color);
        }

        .nav-toggle.active span:nth-child(2) {
            opacity: 0;
        }

        .nav-toggle.active span:nth-child(3) {
            transform: translateY(-8.5px) rotate(-45deg);
            background-color: var(--primary-color);
        }

        /* 移动端菜单样式 */
        @media (max-width: 768px) {
            .nav-container {
                height: 60px;
            }
            
            .nav-toggle {
                display: flex;
            }
            
            .nav-left {
                width: 100%;
                gap: 10px;
            }
            
            .logo-img {
                height: 35px;
            }
            
            .nav-menu {
                position: fixed;
                top: 0;
                left: -100%;
                width: 80%;
                max-width: 300px;
                height: 100vh;
                background-color: #fff;
                flex-direction: column;
                gap: 0;
                align-items: flex-start;
                padding-top: 70px;
                box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
                transition: all 0.35s ease;
                z-index: 100;
                overflow-y: auto;
            }
            
            .nav-menu.active {
                left: 0;
            }
            
            .nav-item {
                width: 100%;
                height: auto;
                padding: 15px 20px;
                border-bottom: 1px solid #f0f0f0;
            }
            
            .nav-item.active::after {
                display: none;
            }
            
            .nav-item.active {
                background-color: #f8fafc;
                color: var(--primary-color);
                font-weight: 500;
            }
            
            .nav-dropdown {
                width: 100%;
                height: auto;
                border-bottom: 1px solid #f0f0f0;
            }
            
            .nav-dropdown-content {
                position: static;
                display: none;
                transform: none;
                box-shadow: none;
                width: 100%;
                margin-top: 5px;
                background-color: #f8fafc;
                border-radius: 0;
                border: none;
                padding: 5px 0;
                transition: all 0.3s ease;
            }
            
            .nav-dropdown.open .nav-dropdown-content {
                display: block;
                animation: fadeDown 0.3s ease;
            }
            
            @keyframes fadeDown {
                from {
                    opacity: 0;
                    transform: translateY(-10px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            
            .dropdown-item {
                padding: 12px 20px 12px 40px;
                text-align: left;
                border-bottom: 1px solid #f0f0f0;
            }
            
            .dropdown-item:last-child {
                border-bottom: none;
            }
            
            .dropdown-trigger {
                display: flex;
                justify-content: space-between;
                align-items: center;
                width: 100%;
                padding: 15px 20px;
            }
            
            .dropdown-trigger span {
                transition: transform 0.3s ease;
            }
            
            .nav-dropdown.open .dropdown-trigger span {
                transform: rotate(180deg);
            }
            
            .merchant-btn {
                position: absolute;
                right: 15px;
                padding: 0 15px;
                height: 34px;
                font-size: 13px;
            }
            
            /* 添加背景遮罩 */
            .menu-backdrop {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 99;
                opacity: 0;
                transition: opacity 0.3s ease;
                backdrop-filter: blur(3px);
            }
            
            .menu-backdrop.active {
                display: block;
                opacity: 1;
            }
            
            /* 修复其他元素在移动端的样式 */
            .container {
                padding: 12px;
            }
            
            /* 移动端历史对话侧边栏样式 */
            .history-sidebar-container {
                position: relative;
                z-index: 99;
            }
            
            .history-sidebar-container > div {
                position: fixed !important;
                right: 0 !important;
                top: auto !important;
                bottom: 0 !important;
                width: 100% !important;
                max-width: 100% !important;
                z-index: 98 !important;
            }
            
            .toggle-history-btn {
                width: 50px !important;
                height: 40px !important;
                right: 15px !important;
                top: auto !important;
                bottom: 15px !important;
                border-radius: 20px !important;
                flex-direction: row !important;
                justify-content: center !important;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
            }
            
            .toggle-history-btn .vertical-text {
                writing-mode: horizontal-tb !important;
                text-orientation: mixed !important;
                font-size: 13px !important;
                letter-spacing: normal !important;
                margin-left: 5px !important;
            }
            
            .toggle-history-btn i {
                margin-bottom: 0 !important;
                transform: rotate(90deg);
            }
            
            .toggle-history-btn.collapsed i {
                transform: rotate(-90deg);
            }
            
            .history-sidebar-container .user-form {
                position: fixed !important;
                bottom: 0 !important;
                left: 0 !important;
                right: 0 !important;
                width: 100% !important;
                max-height: 80vh !important;
                overflow-y: auto !important;
                border-radius: 15px 15px 0 0 !important;
                padding: 20px 15px 25px !important;
                transform: translateY(100%) !important;
                transition: transform 0.35s ease !important;
                z-index: 99;
                box-shadow: 0 -5px 25px rgba(0, 0, 0, 0.15) !important;
                display: none;
            }
            
            .history-sidebar-container .user-form.active {
                transform: translateY(0) !important;
                display: block !important;
            }
            
            /* 添加顶部拖动条 */
            .history-sidebar-container .user-form::before {
                content: '';
                display: block;
                width: 40px;
                height: 5px;
                background: #e0e0e0;
                border-radius: 3px;
                margin: 0 auto 15px;
                cursor: grab;
            }
            
            .history-sidebar-container .user-form:active::before {
                cursor: grabbing;
            }
            
            .history-sidebar-container .user-form:not([style*="display: none"]) {
                transform: translateY(0) !important;
            }
            
            /* 添加历史对话背景遮罩 */
            .history-backdrop {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 97;
                opacity: 0;
                transition: opacity 0.3s ease;
                backdrop-filter: blur(3px);
            }
            
            .history-backdrop.active {
                display: block;
                opacity: 1;
            }
        }

        .system-message {
            font-style: italic;
            color: #888;
            text-align: center;
            padding: 5px 10px;
            margin: 10px 0;
            background-color: #f8f9fa;
            border-radius: 5px;
            font-size: 13px;
        }
        
        .merchant-badge {
            background-color: #4e6ef2;
            color: white;
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 4px;
            margin-left: 5px;
        }
        
        .preset-questions {
            background: #f8fafc;
            border-radius: 8px;
            padding: 12px 15px;
            margin: 10px 0;
            border: 1px solid #e2e8f0;
        }
        
        .preset-questions br {
            margin-bottom: 8px;
        }
        
        /* 预设问题项样式 */
        .preset-question-item {
            display: block;
            padding: 8px 12px;
            margin: 5px 0;
            background: #fff;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .preset-question-item:hover {
            background: #f0f9ff;
            border-color: #bae6fd;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        
        /* 移除不正确的span选择器，它会干扰预设问题的正确渲染 */
        .preset-questions span {
            display: inline;
        }
        
        .message.staff .message-content:after,
        .message.customer .message-content:after,
        .message.merchant .message-content:after {
            display: none;
        }
        
        .preset-reply {
            background: #fff;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e7ee;
            position: relative;
            overflow: hidden;
        }
        
        .preset-reply:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--primary-color);
        }
        
        .preset-reply p {
            margin: 0;
            line-height: 1.6;
            color: #333;
        }
        
        .preset-reply img {
            max-width: 100%;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .preset-reply a {
            color: var(--primary-color);
            text-decoration: none;
        }
        
        .preset-reply a:hover {
            text-decoration: underline;
        }
        
        /* 消息操作菜单样式 */
        .message-actions {
            opacity: 0;
            transition: opacity 0.2s ease;
        }
        
        .message:hover .message-actions {
            opacity: 1;
        }
        
        .message-actions-trigger {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }
        
        .message-actions-trigger:hover {
            background: rgba(255, 255, 255, 1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            transform: scale(1.1);
        }
        
        /* 已撤回消息样式 */
        .recalled-message {
            font-style: italic;
            color: #94a3b8;
            font-size: 13px;
            background: #f8fafc;
            border: 1px dashed #e2e8f0;
            border-radius: 8px;
            padding: 10px;
            text-align: center;
            box-shadow: none;
        }
        
        /* 会话列表项悬停样式 */
        .session-item:hover {
            background-color: #f9fafb;
        }
        
        /* 联系人会话对话框样式 */
        .contact-sessions-dialog .el-message-box__content {
            padding: 20px !important;
        }
        
        .contact-sessions-dialog .el-message-box {
            width: 500px;
            max-width: 90vw;
        }
        
        .contact-sessions-dialog .el-message-box__header {
            padding: 15px 20px;
            background: #f0f7ff;
        }

        .session-participants {
            background-color: #f8f8f8;
            border-bottom: 1px solid #e0e0e0;
            padding: 8px 15px;
            margin-bottom: 10px;
        }

        .participants-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
            font-size: 13px;
            color: #606266;
        }

        .participants-list {
            display: flex;
            flex-wrap: wrap;
        }

        /* 添加角色标签样式 */
        .message-sender-role {
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            font-size: 13px;
            transition: all 0.2s ease;
        }
        
        .message-sender-role:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
        }
        
        .customer-tag {
            background-color: #e0f2fe;
            color: #0369a1;
            border-left: 3px solid #0ea5e9;
        }
        
        .merchant-tag {
            background-color: #fef3c7;
            color: #9a3412;
            border-left: 3px solid #f59e0b;
        }
        
        .staff-tag {
            background-color: #e0e7ff;
            color: #4338ca;
            border-left: 3px solid #6366f1;
        }
        
        /* 移动端适配 */
        @media (max-width: 768px) {
            .message-sender-role {
                font-size: 12px;
                padding: 2px 6px;
            }
        }

        /* 角色标签样式改进 - 参考截图样式 */
        .message-sender-role {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: 500;
            line-height: 1.4;
        }
        
        .message-sender-role i {
            font-size: 11px;
            margin-right: 3px;
        }
        
        .customer-tag {
            background-color: #e6f4ff;
            color: #0958d9;
        }
        
        .merchant-tag {
            background-color: #fff7e6;
            color: #d46b08;
        }
        
        .staff-tag {
            background-color: #fff1f0;
            color: #cf1322;
        }
        
        /* 移动端适配 */
        @media (max-width: 768px) {
            .message-sender-role {
                font-size: 11px;
                padding: 1px 5px;
            }
            
            /* 按钮容器和按钮的手机端样式 */
            .button-container {
                flex-direction: column;
                width: 100%;
                max-width: 100%;
                gap: 10px;
            }
            
            .button-container .el-button {
                width: 100%;
                margin: 0 0 5px !important;
                padding: 12px 0 !important;
                height: auto !important;
                font-size: 15px !important;
                border-radius: 6px !important;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            
            .button-container .action-button.primary-button {
                margin-bottom: 8px !important;
            }
            
            .button-container .action-button.secondary-button {
                margin-bottom: 0 !important;
            }
        }
        
        /* 移动端聊天类型选择器和表单优化 */
        @media (max-width: 768px) {
            /* 优化聊天类型选择器 */
            .chat-type-selector {
                padding: 15px !important;
                margin: 0 auto 25px !important;
                max-width: 100% !important;
            }
            
            .el-radio-group[style*="display: flex; justify-content: center; gap: 40px"] {
                flex-direction: column !important;
                gap: 15px !important;
            }
            
            .el-radio[style*="padding: 14px 22px; border: 1px solid rgb(237, 242, 247); border-radius: 12px"] {
                min-width: 100% !important;
                margin-right: 0 !important;
            }
            
            /* 优化表单布局 */
            .el-form[style*="padding: 0px 10px"] {
                padding: 0 !important;
            }
            
            .el-form-item__label[style*="width: 120px"] {
                width: 90px !important;
                padding-right: 5px !important;
                text-align: left !important;
            }
            
            .el-form-item__content {
                margin-left: 90px !important;
            }
            
            /* 网格布局改为单列 */
            div[style*="display: grid; grid-template-columns: 1fr 1fr; gap: 20px 30px"] {
                grid-template-columns: 1fr !important;
                gap: 10px !important;
            }
            
            div[style*="display: grid; grid-template-columns: 1fr 1fr; gap: 20px 30px"] .el-form-item {
                grid-column: 1 !important;
            }
            
            /* 商家搜索框响应式调整 */
            div[style*="display: flex; align-items: center; box-shadow"] {
                flex-direction: column !important;
                align-items: stretch !important;
            }
            
            div[style*="display: flex; align-items: center; box-shadow"] .el-input {
                margin-right: 0 !important;
                margin-bottom: 10px !important;
            }
            
            /* 按钮样式调整 */
            .el-form-item[style*="text-align: center"] .el-button {
                padding: 12px 20px !important;
            }
            
            .el-form-item[style*="text-align: center"] .el-button+.el-button {
                margin-left: 10px !important;
            }
        }
        
        /* 统一的已读状态样式 */
        .read-status {
            font-size: 12px;
            color: #999;
            position: absolute;
            right: 8px;
            bottom: 4px;
        }

        .message-read-status.all_read {
            color: #67c23a;
        }

        .message-read-status.customer_read {
            color: #409eff;
        }

        .message-read-status.merchant_read {
            color: #e6a23c;
        }

        .message-read-status.staff_read {
            color: #909399;
        }

        .message-read-status.partial_read {
            color: #e6a23c;
        }

        .message-read-status.unread {
            color: #f56c6c;
        }
        
        /* 为了适应已读状态显示，调整消息气泡内边距 */
        .message-content {
            padding-bottom: 18px; /* 给已读状态留出空间 */
        }
        
        /* 消息气泡相对定位，便于放置已读状态 */
        .message-item {
            position: relative;
        }
    </style>
</head>

<body>
    <!-- 加载动画容器 -->
    <div id="loading">
        <div class="spinner"></div>
    </div>

    <!-- 新的导航栏结构 -->
    <div class="header">
        <div class="nav-container">
            <div class="nav-toggle" id="navToggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
            <div class="nav-left">
                <div class="logo-container" onclick="window.location.href='/'">
                    <img src="{$logo}" class="logo-img" alt="{$siteName}">
                </div>
                <div class="nav-menu" id="navMenu">
                    <!-- 使用ThinkPHP模板语法遍历导航菜单 -->
                    {volist name="navItems" id="item"}
                        <!-- 判断是否有子菜单 -->
                        {if condition="empty($item.subMenus)"}
                            <!-- 没有子菜单的普通菜单项 -->
                            <a href="{$item.href}" target="{$item.target == '_blank' ? '_blank' : '_self'}" 
                               class="nav-item {$item.active ? 'active' : ''}">
                                {$item.name}
                            </a>
                        {else/}
                            <!-- 有子菜单的下拉菜单项 -->
                            <div class="nav-dropdown">
                                <a href="javascript:void(0);" class="nav-item dropdown-trigger {$item.active ? 'active' : ''}">
                                    {$item.name} <span style="font-size:12px; margin-left:3px;">▼</span>
                                </a>
                                <div class="nav-dropdown-content">
                                    {volist name="item.subMenus" id="subItem"}
                                        <a href="{$subItem.href}" target="{$subItem.target == '_blank' ? '_blank' : '_self'}" 
                                           class="dropdown-item {if condition="request()->url(true) == $subItem.href"}active{/if}">
                                            {$subItem.name}
                                        </a>
                                    {/volist}
                                </div>
                            </div>
                        {/if}
                    {/volist}
                </div>
            </div>
            <button class="merchant-btn" onclick="window.location.href='/merchant/login'">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16">
                    <path d="M12 4a4 4 0 1 0 0 8 4 4 0 0 0 0-8zM6 8a6 6 0 1 1 12 0A6 6 0 0 1 6 8zm2 10a3 3 0 0 0-3 3 1 1 0 1 1-2 0 5 5 0 0 1 5-5h8a5 5 0 0 1 5 5 1 1 0 1 1-2 0 3 3 0 0 0-3-3H8z"/>
                </svg>
                商家中心
            </button>
        </div>
        <div class="menu-backdrop" id="menuBackdrop"></div>
    </div>

    <div class="container">
        <div class="main" id="app">
            <el-row :gutter="20" style="margin-left: 0; margin-right: 0;">
                <!-- 主要聊天区域，调整宽度比例 -->
                <el-col :md="sessionStarted ? 24 : 24" :sm="historyMode && !sessionStarted ? 0 : 24" :xs="historyMode && !sessionStarted ? 0 : 24">
                    <div v-if="!sessionStarted" class="user-form" style="box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08); border-radius: 16px; overflow: hidden; border: 1px solid #f0f5ff; background: linear-gradient(180deg, #ffffff, #fcfdff); max-width: 100%; margin: 0 auto;">
                        <!-- 表单标题区域 - 更现代的渐变效果 -->
                        <div style="padding: 20px 25px; background: linear-gradient(135deg, #4e6ef2, #5981d8); margin: -35px -35px 25px -35px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1); position: relative;">
                            <div style="position: absolute; top: 0; right: 0; bottom: 0; left: 0; background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB4PSIwIiB5PSIwIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiIHBhdHRlcm5UcmFuc2Zvcm09InJvdGF0ZSgzMCkiPjxyZWN0IHg9IjAiIHk9IjAiIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgZmlsbD0icmdiYSgyNTUsMjU1LDI1NSwwLjAzKSI+PC9yZWN0PjwvcGF0dGVybj48L2RlZnM+PHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0idXJsKCNwYXR0ZXJuKSI+PC9yZWN0Pjwvc3ZnPg=='); opacity: 0.5;"></div>
                            <div style="position: relative;">
                                <h2 style="margin: 0; font-size: 22px; color: #ffffff; font-weight: 600; letter-spacing: 0.5px;">开始咨询</h2>
                                <p style="margin: 10px 0 0 0; color: rgba(255, 255, 255, 0.9); font-size: 14px; max-width: 100%;">请填写下方信息，我们将尽快为您提供专业帮助</p>
                            </div>
                        </div>
                        
                        <!-- 聊天类型选择器 - 更加突出的选择区域 -->
                        <div class="chat-type-selector" style="margin: 0 auto 25px; text-align: center; padding: 10px; max-width: 100%;">
                            <div style="margin-bottom: 15px; font-size: 16px; color: #3a4a5e; font-weight: 500; text-align: center;">
                                <i class="el-icon-chat-dot-round" style="margin-right: 8px; color: #4e6ef2; font-size: 18px;"></i>请选择您想要联系的对象
                            </div>
                            
                            <div style="width: 100%; max-width: 800px; margin: 0 auto;">
                                <el-radio-group v-model="chatType" @change="handleChatTypeChange" style="display: flex; flex-direction: row; width: 100%; gap: 20px; flex-wrap: wrap;" class="chat-type-radio-group">
                                    <el-radio :label="'customer'" style="margin: 0; padding: 0; height: auto; display: block; width: calc(50% - 10px); min-width: 260px; flex: 1;" class="chat-type-radio">
                                        <div style="padding: 15px; background-color: #f8fafc; border-radius: 12px; border-left: 4px solid transparent; transition: all 0.3s; height: 100%;" :style="{ 'border-left-color': chatType === 'customer' ? '#4e6ef2' : 'transparent', 'background-color': chatType === 'customer' ? 'rgba(78, 110, 242, 0.08)' : '#f8fafc' }" class="chat-type-card">
                                            <div style="display: flex; align-items: center; gap: 10px; justify-content: center; margin: 0 auto;" class="chat-type-title">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#4e6ef2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <circle cx="12" cy="8" r="4"></circle>
                                                    <path d="M18 21a8 8 0 1 0-12 0"></path>
                                                    <path d="M9 17v-2h6v2"></path>
                                                </svg>
                                                <span style="font-size: 15px; font-weight: 500; color: #334155;">{$siteName}平台客服</span>
                                            </div>
                                            <div style="font-size: 12px; color: #64748b; margin-top: 6px; line-height: 1.5; word-break: break-all; white-space: normal; width: 100%; text-align: center;" class="chat-type-desc">
                                                【{$siteName}的平台客服，一般解决交易诈骗等纠纷】
                                            </div>
                                        </div>
                                    </el-radio>
                                    
                                    <el-radio :label="'merchant'" style="margin: 0; padding: 0; height: auto; display: block; width: calc(50% - 10px); min-width: 260px; flex: 1;" class="chat-type-radio">
                                        <div style="padding: 15px; background-color: #f8fafc; border-radius: 12px; border-left: 4px solid transparent; transition: all 0.3s; height: 100%;" :style="{ 'border-left-color': chatType === 'merchant' ? '#4e6ef2' : 'transparent', 'background-color': chatType === 'merchant' ? 'rgba(78, 110, 242, 0.08)' : '#f8fafc' }" class="chat-type-card">
                                            <div style="display: flex; align-items: center; gap: 10px; justify-content: center; margin: 0 auto;" class="chat-type-title">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#4e6ef2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M3 3h18v2H3z"></path>
                                                    <path d="M19 6v14H5V6"></path>
                                                    <path d="M9 6v14"></path>
                                                    <path d="M15 6v14"></path>
                                                    <path d="M12 11c1.5 0 2.5-1 2.5-2.5V6"></path>
                                                    <path d="M9.5 8.5c0 1.5 1 2.5 2.5 2.5"></path>
                                                </svg>
                                                <span style="font-size: 15px; font-weight: 500; color: #334155;">咨询店铺商家</span>
                                            </div>
                                            <div style="font-size: 12px; color: #64748b; margin-top: 6px; line-height: 1.5; word-break: break-all; white-space: normal; width: 100%; text-align: center;" class="chat-type-desc">
                                                【您所选的商铺的商家客服】
                                            </div>
                                        </div>
                                    </el-radio>
                                </el-radio-group>
                            </div>
                        </div>
                        
                        <el-form :model="userInfo" :rules="rules" ref="userForm" label-width="90px" label-position="top" style="padding: 0 10px;">
                            <style>
                                .el-form-item__label {
                                    text-align: left !important;
                                    white-space: nowrap !important;
                                    font-weight: 500 !important;
                                    color: #374151 !important;
                                    font-size: 15px !important;
                                    letter-spacing: 0.3px !important;
                                    padding-bottom: 5px !important;
                                }
                                
                                .el-input__inner, .el-textarea__inner {
                                    border-radius: 10px !important;
                                    transition: all 0.3s !important;
                                    padding: 10px 12px !important;
                                    height: auto !important;
                                    line-height: 1.5 !important;
                                    font-size: 14px !important;
                                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02) !important;
                                }
                                
                                .el-input__inner:focus, .el-textarea__inner:focus {
                                    border-color: #4e6ef2 !important;
                                    box-shadow: 0 0 0 3px rgba(78, 110, 242, 0.15) !important;
                                }
                                
                                .el-radio.is-checked .el-radio__label {
                                    color: #4e6ef2 !important;
                                    font-weight: 500 !important;
                                }
                                
                                .el-radio.is-checked {
                                    background-color: rgba(78, 110, 242, 0.1) !important;
                                    border-color: transparent !important;
                                    transform: translateY(-2px);
                                    box-shadow: 0 3px 8px rgba(78, 110, 242, 0.15) !important;
                                }
                                
                                .el-radio__input {
                                    vertical-align: middle !important;
                                }
                                
                                .el-radio {
                                    box-shadow: none !important;
                                    display: flex !important;
                                    flex-direction: column !important;
                                    align-items: flex-start !important;
                                }
                                
                                .el-radio__input {
                                    position: absolute !important;
                                    top: 16px !important;
                                    left: 16px !important;
                                    opacity: 0 !important; /* 隐藏单选按钮 */
                                    pointer-events: none !important; /* 禁用鼠标事件 */
                                }
                                
                                /* 隐藏单选按钮的圆形指示器和相关元素 */
                                .el-radio__inner,
                                .el-radio__input.is-checked .el-radio__inner,
                                .el-radio__input.is-checked::after {
                                    opacity: 0 !important;
                                    display: none !important;
                                }
                                
                                /* 防止选择时出现额外间距 */
                                .el-radio__original {
                                    display: none !important;
                                }
                                
                                .el-radio__label {
                                    padding-left: 30px !important;
                                    width: 100% !important;
                                }
                                
                                .el-form-item {
                                    margin-bottom: 20px !important;
                                }
                                
                                /* 响应式样式优化 */
                                @media (max-width: 768px) {
                                    .el-form-item {
                                        margin-bottom: 15px !important;
                                    }
                                    
                                    .user-form {
                                        padding: 15px !important;
                                        margin: 10px !important;
                                    }
                                    
                                    .el-form-item__label {
                                        font-size: 14px !important;
                                        padding-bottom: 3px !important;
                                    }
                                    
                                    .el-input__inner, .el-textarea__inner {
                                        font-size: 14px !important;
                                        padding: 8px 10px !important;
                                    }
                                    
                                    .chat-type-selector {
                                        padding: 5px !important;
                                        margin-bottom: 15px !important;
                                    }
                                    
                                    .chat-type-radio-group {
                                        flex-direction: column !important;
                                        gap: 10px !important;
                                    }
                                    
                                    .chat-type-radio {
                                        width: 100% !important;
                                        min-width: 100% !important;
                                    }
                                    
                                    .chat-type-title {
                                        margin-left: 20px !important;
                                        gap: 8px !important;
                                    }
                                    
                                    .chat-type-title i {
                                        font-size: 18px !important;
                                    }
                                    
                                    .chat-type-title span {
                                        font-size: 14px !important;
                                    }
                                    
                                    .chat-type-desc {
                                        font-size: 11px !important;
                                        margin-top: 4px !important;
                                        margin-left: 46px !important;
                                        white-space: normal !important;
                                        word-break: break-word !important;
                                        width: calc(100% - 60px) !important;
                                        overflow: visible !important;
                                    }
                                    
                                    .el-radio__input {
                                        top: 14px !important;
                                        left: 12px !important;
                                    }
                                }
                                
                                /* 小型手机屏幕 */
                                @media (max-width: 375px) {
                                    .chat-type-card {
                                        padding: 10px 8px !important;
                                    }
                                    
                                    .chat-type-title {
                                        margin-left: 18px !important;
                                    }
                                    
                                    .chat-type-desc {
                                        margin-left: 38px !important;
                                    }
                                    
                                    .el-radio__input {
                                        top: 12px !important;
                                        left: 10px !important;
                                    }
                                }
                            </style>
                            
                            <!-- 商家选择区域 - 更加直观的商家搜索体验 -->
                            <el-form-item v-if="chatType === 'merchant'" label="店铺尾号" style="margin-bottom: 20px;">
                                <div style="display: flex; flex-direction: column; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04); border-radius: 10px; padding: 2px; background: #fcfcfc;">
                                    <div style="display: flex; flex-wrap: wrap; gap: 8px;">
                                    <el-input 
                                        v-model="merchantToken" 
                                        placeholder="例如：765U90CA" 
                                            style="flex: 1; min-width: 150px;"
                                        @blur="searchMerchantByToken"
                                    >
                                        <template #prefix>
                                            <i class="el-icon-search" style="color: #4e6ef2; margin-right: 5px;"></i>
                                        </template>
                                    </el-input>
                                        <el-button type="primary" @click="searchMerchantByToken" size="default" style="border-radius: 8px; font-weight: 500; padding: 8px 15px;">
                                        <i class="el-icon-search" style="margin-right: 5px;"></i>查找
                                    </el-button>
                                    </div>
                                </div>
                                <div style="display: flex; justify-content: flex-end; margin-top: 6px;">
                                    <el-tooltip content="商家店铺尾号是系统分配给商家的唯一标识，可向商家索取或从店铺网址获取" placement="top">
                                        <span style="color: #7e8ba3; font-size: 12px; cursor: pointer; display: flex; align-items: center;">
                                            <i class="el-icon-question" style="margin-right: 4px; color: #4e6ef2;"></i>
                                            什么是店铺尾号？
                                        </span>
                                    </el-tooltip>
                                </div>
                                
                                <!-- 已选择商家展示 - 更加突出的商家信息展示 -->
                                <div v-if="selectedMerchant" style="margin-top: 15px; display: flex; align-items: center; padding: 12px; background: #f0f7ff; border-radius: 10px; border: 1px solid #cee0fe; box-shadow: 0 2px 10px rgba(78, 110, 242, 0.08);">
                                    <div style="display: flex; align-items: center; gap: 10px; width: 100%; flex-wrap: wrap;">
                                        <img v-if="merchantInfo.avatar" :src="merchantInfo.avatar" style="width: 36px; height: 36px; border-radius: 50%; object-fit: cover; border: 2px solid #e0e7ff; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
                                        <div v-else style="width: 36px; height: 36px; border-radius: 50%; background-color: #4e6ef2; color: white; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 16px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
                                            {{ merchantInfo.nickname ? merchantInfo.nickname.charAt(0).toUpperCase() : 'M' }}
                                        </div>
                                        <div style="flex: 1;">
                                            <div style="font-weight: 600; color: #334155; font-size: 15px; display: flex; align-items: center; flex-wrap: wrap; gap: 5px;">
                                                已选择商家:
                                                <span style="color: #4e6ef2;">{{ merchantInfo.nickname }}</span>
                                                <span style="background: #e0e7ff; color: #4e6ef2; font-size: 11px; padding: 1px 6px; border-radius: 10px; font-weight: normal;">已验证</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 错误信息 - 更加醒目的错误提示 -->
                                <div v-if="merchantSearchError" style="color: #F56C6C; font-size: 13px; margin-top: 10px; padding: 10px; background: #FEF2F2; border-radius: 8px; border-left: 3px solid #F56C6C; display: flex; align-items: flex-start;">
                                    <i class="el-icon-warning" style="margin-right: 6px; margin-top: 2px; font-size: 15px;"></i>
                                    <div>{{ merchantSearchError }}</div>
                                </div>
                            </el-form-item>
                            
                            <!-- 分割线 -->
                            <div v-if="chatType === 'merchant' && selectedMerchant" style="height: 1px; background: linear-gradient(to right, transparent, #e5e7eb, transparent); margin: 5px 0 20px;"></div>
                            
                            <!-- 联系人信息表单 - 更优雅的字段布局 -->
                            <h3 style="font-size: 16px; color: #4b5563; margin-bottom: 15px; padding-bottom: 8px; border-bottom: 1px solid #f0f0f0;">
                                <i class="el-icon-user" style="margin-right: 6px; color: #4e6ef2;"></i>联系人信息
                            </h3>
                            
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                                <el-form-item label="姓名" prop="name" style="margin-bottom: 15px;">
                                    <el-input 
                                        v-model="userInfo.name" 
                                        placeholder="请输入您的姓名"
                                        prefix-icon="el-icon-user"
                                    ></el-input>
                                </el-form-item>
                                
                                <!-- 联系方式输入框 - 根据设置显示电话/QQ/微信其中一种 -->
                                <el-form-item v-if="!params.settings || !params.settings.contact_field_type || params.settings.contact_field_type === 'phone'" label="电话" prop="phone" style="margin-bottom: 15px;">
                                    <el-input 
                                        v-model="userInfo.phone" 
                                        placeholder="请输入您的电话号码"
                                        prefix-icon="el-icon-phone"
                                    ></el-input>
                                </el-form-item>
                                
                                <el-form-item v-if="params.settings && params.settings.contact_field_type === 'qq'" label="QQ" prop="qq" style="margin-bottom: 15px;">
                                    <el-input 
                                        v-model="userInfo.qq" 
                                        placeholder="请输入您的QQ号码"
                                        prefix-icon="el-icon-chat-dot-square"
                                    ></el-input>
                                </el-form-item>
                                
                                <el-form-item v-if="params.settings && params.settings.contact_field_type === 'wechat'" label="微信" prop="wechat" style="margin-bottom: 15px;">
                                    <el-input 
                                        v-model="userInfo.wechat" 
                                        placeholder="请输入您的微信号"
                                        prefix-icon="el-icon-chat-dot-square"
                                    ></el-input>
                                </el-form-item>
                            </div>
                            
                            <el-form-item label="邮箱" prop="email" style="margin-bottom: 15px;">
                                <el-input 
                                    v-model="userInfo.email" 
                                    placeholder="请输入您的联系邮箱"
                                    prefix-icon="el-icon-message"
                                ></el-input>
                            </el-form-item>
                            
                            <el-form-item label="问题描述" prop="message" style="margin-bottom: 25px;">
                                <el-input 
                                    type="textarea" 
                                    v-model="userInfo.message" 
                                    rows="4" 
                                    placeholder="请详细描述您遇到的问题，以便我们更好地为您提供帮助"
                                    style="box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);"
                                ></el-input>
                                <div style="display: flex; justify-content: space-between; margin-top: 8px; flex-wrap: wrap; gap: 5px;">
                                    <span style="color: #94a3b8; font-size: 12px;">
                                        <i class="el-icon-info" style="margin-right: 4px;"></i>详细的描述有助于我们更快解决问题
                                    </span>
                                    <span style="color: #94a3b8; font-size: 12px;">{{ userInfo.message.length }}/500</span>
                                </div>
                            </el-form-item>
                            
                            <!-- 分割线 -->
                            <div style="height: 1px; background: linear-gradient(to right, transparent, #e5e7eb, transparent); margin: 10px 0 30px;"></div>
                            
                            <!-- 操作按钮区 - 更现代的按钮设计 -->
                            <el-form-item style="margin-bottom: 15px; text-align: center;">
                                <div class="button-container" style="display: flex; flex-wrap: wrap; justify-content: center; gap: 15px; width: 100%; max-width: 480px; margin: 0 auto;">
                                    <el-button 
                                        type="primary" 
                                        @click="startChat" 
                                        size="large" 
                                        class="action-button primary-button"
                                        style="padding: 12px 24px; font-weight: 500; letter-spacing: 0.5px; border-radius: 8px; box-shadow: 0 4px 12px rgba(78, 110, 242, 0.25); transition: all 0.3s; margin: 0; flex: 1;"
                                    >
                                        <i class="el-icon-chat-dot-round" style="margin-right: 8px;"></i>开始咨询
                                    </el-button>
                                    <el-button 
                                        @click="checkHistory" 
                                        size="large" 
                                        class="action-button secondary-button"
                                        style="padding: 12px 24px; font-weight: 500; border-radius: 8px; transition: all 0.3s; margin: 0; flex: 1;"
                                    >
                                        <i class="el-icon-time" style="margin-right: 8px;"></i>查看历史会话
                                    </el-button>
                                </div>
                            </el-form-item>
                            
                            <!-- 说明文本 - 更加醒目的提示信息 -->
                            <div style="font-size: 14px; color: #64748b; margin-top: 20px; background: #f8fafc; padding: 15px; border-radius: 10px; border-left: 4px solid #94a3b8; display: flex; align-items: flex-start;">
                                <i class="el-icon-info-circle" style="margin-right: 10px; margin-top: 2px; font-size: 18px; color: #4e6ef2;"></i>
                                <div>
                                    注意：每个联系人(邮箱/电话)最多可同时进行 
                                    <strong style="color: #4b5563; background: rgba(78, 110, 242, 0.1); padding: 2px 6px; border-radius: 4px;">
                                        {{ params.settings && params.settings.max_active_sessions ? params.settings.max_active_sessions : 2 }}
                                    </strong> 
                                    个会话，请合理管理您的会话
                                </div>
                            </div>
                        </el-form>
                    </div>

                    <div v-if="sessionStarted" class="chat-container" style="height: calc(100vh - 100px); margin-top: 0;">
                        <div class="chat-header" style="display: flex; justify-content: space-between; align-items: center;">
                            <div style="display: flex; align-items: center;">
                                {{ params.ui_config.header_text || '在线客服' }}
                                <span v-if="getSessionClosed()"
                                      style="color: #ffffff; background-color: #ef4444; border-radius: 4px; padding: 2px 8px; margin-left: 10px; font-size: 12px;">
                                    会话已关闭
                                </span>
                            </div>
                            <div style="display: flex; align-items: center;">
                                <el-button size="small" type="text" style="color: #fff; font-size: 14px;" @click="backToStartConsult">
                                    <i class="el-icon-back" style="margin-right: 3px;"></i>返回开始咨询
                                </el-button>
                            </div>
                        </div>
                        <div class="chat-body" ref="chatBody">
                            <div class="message-time">{{ formatDate(new Date()) }}</div>
                            
                            <!-- 对消息按照创建时间排序 -->
                            <div v-for="(msg, index) in sortedMessages" :key="index" 
                                 :class="['message', msg.role_type === 'customer' ? 'customer' : (msg.role_type === 'merchant' ? 'merchant' : 'staff')]"
                                 :data-message-id="msg.id"
                                 :data-message-role="msg.role_type">
                                <div class="message-avatar">
                                    <span>{{ msg.role_type === 'merchant' ? 'M' : (msg.role_type === 'staff' ? 'S' : 'C') }}</span>
                                </div>
                                <div class="message-content">
                                    <!-- 添加发送者角色名称 - 美化后的显著样式 -->
                                    <div class="message-sender-role" 
                                         :class="{
                                             'customer-tag': msg.role_type === 'customer',
                                             'merchant-tag': msg.role_type === 'merchant',
                                             'staff-tag': msg.role_type === 'staff'
                                         }" 
                                         style="display: inline-block; padding: 2px 8px; border-radius: 3px; margin-bottom: 8px; font-weight: 500; font-size: 12px;">
                                         <i :class="{
                                             'el-icon-user': msg.role_type === 'customer',
                                             'el-icon-shop': msg.role_type === 'merchant',
                                             'el-icon-service': msg.role_type === 'staff'
                                         }" style="margin-right: 4px;"></i>
                                        {{ msg.role_type === 'customer' ? '客户' : (msg.role_type === 'merchant' ? '商家' : '平台客服') }}
                                    </div>
                                    
                                    <!-- 消息操作菜单，只在自己发送的消息上显示，且只在未撤回的消息上显示 -->
                                    <div v-if="msg.role_type === 'customer' && msg.is_recalled !== 1" class="message-actions" style="position: absolute; top: 8px; right: 8px;">
                                        <el-dropdown trigger="click" size="small" @command="handleMessageAction($event, msg)">
                                            <span class="message-actions-trigger" style="cursor:pointer; color:#94a3b8; padding:4px;">
                                                <i class="el-icon-more"></i>
                                            </span>
                                            <template #dropdown>
                                                <el-dropdown-menu>
                                                    <el-dropdown-item command="recall" 
                                                        :disabled="(Date.now()/1000 - msg.create_time) > 120">
                                                        <i class="el-icon-delete" style="margin-right:5px;"></i>撤回消息
                                                    </el-dropdown-item>
                                                </el-dropdown-menu>
                                            </template>
                                        </el-dropdown>
                                    </div>
                                    
                                    <!-- 已撤回的消息特殊处理 -->
                                    <div v-if="msg.is_recalled === 1" class="recalled-message" style="padding: 10px; background: #f8f8f8; border: 1px dashed #ddd; border-radius: 4px; color: #999; text-align: center;">
                                        {{ msg.message }}
                                    </div>
                                    
                                    <!-- 文本消息 -->
                                    <div v-else-if="msg.message_type === 'text'" v-html="formatMessage(msg.message)" @click="handlePresetQuestionClick($event, msg)"></div>
                                    
                                    <!-- 图片消息 -->
                                    <div v-else-if="msg.message_type === 'image'">
                                        <!-- 当消息内容包含HTML标签时直接使用v-html渲染 -->
                                        <div v-if="msg.message && typeof msg.message === 'string' && msg.message.includes('<div') && msg.message.includes('<img')" v-html="msg.message"></div>
                                        <!-- 否则检查是否为图片URL -->
                                        <div v-else-if="isImageUrl(msg.message)" class="image-container" style="position: relative; overflow: hidden; width: 100%; margin: 0 auto;">
                                            <img :src="msg.message" class="message-image" @click="previewImage(msg.message)" @error="handleImageError">
                                            <div class="image-overlay" style="position: absolute; bottom: 10px; right: 10px; background: rgba(0,0,0,0.5); color: white; border-radius: 4px; padding: 3px 6px; font-size: 12px; opacity: 0.8;">
                                                <i class="el-icon-zoom-in" style="margin-right: 3px;"></i>点击查看
                                            </div>
                                        </div>
                                        <!-- 常规图片显示方式 -->
                                        <div v-else class="image-container" style="position: relative; overflow: hidden; width: 100%; margin: 0 auto;">
                                            <img :src="msg.file_url || msg.message" class="message-image" @click="previewImage(msg.file_url || msg.message)" @error="handleImageError">
                                            <div class="image-overlay" style="position: absolute; bottom: 10px; right: 10px; background: rgba(0,0,0,0.5); color: white; border-radius: 4px; padding: 3px 6px; font-size: 12px; opacity: 0.8;">
                                                <i class="el-icon-zoom-in" style="margin-right: 3px;"></i>点击查看
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 文件消息 -->
                                    <div v-else-if="msg.message_type === 'file'" class="message-file" @click="downloadFile(msg.file_url || msg.message)">
                                        <i class="el-icon-document"></i>
                                        <span>文件下载</span>
                                    </div>
                                    
                                    <div class="message-time-small">
                                        {{ formatTime(msg.create_time) }}
                                        <!-- 统一的已读状态显示 -->
                                        <span v-if="msg.role_type === 'customer' || msg.sender_type === 'customer'"
                                              class="message-read-status"
                                              :class="getReadStatus(msg)"
                                              style="margin-left: 8px; font-size: 12px;">
                                            {{ getReadStatusText(msg) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 将"对方正在输入..."提示移到单独的区域，不再作为消息气泡显示 -->
                            <div v-if="isTyping" class="typing-indicator">
                                <span>对方正在输入...</span>
                            </div>
                        </div>
                        <div class="chat-footer">
                            <div style="display: flex; margin-bottom: 12px;">
                                <div class="emoji-picker">
                                    <el-popover placement="top" width="320" trigger="click" v-model="emojiVisible">
                                        <div style="max-height: 220px; overflow-y: auto; padding: 8px;">
                                            <span v-for="emoji in emojis" :key="emoji" class="emoji-item" @click="insertEmoji(emoji)">{{ emoji }}</span>
                                        </div>
                                        <el-button slot="reference" icon="el-icon-smile" circle></el-button>
                                    </el-popover>
                                </div>
                                <input 
                                    type="file" 
                                    ref="fileInput" 
                                    style="display: none;" 
                                    @change="handleManualFileChange" 
                                    accept="image/*"
                                />
                            </div>
                            
                            <div v-if="uploadFile" class="upload-preview">
                                <template v-if="uploadFile.type.includes('image')">
                                    <img :src="uploadPreview">
                                    <div class="file-name">{{ uploadFile.name }}</div>
                                </template>
                                <div v-else class="message-file">
                                    <i class="el-icon-document"></i>
                                    <span>{{ uploadFile.name }}</span>
                                </div>
                                <i class="el-icon-close" @click="cancelUpload"></i>
                            </div>
                            
                            <el-input
                                v-model="messageInput"
                                type="textarea"
                                :rows="3"
                                :placeholder="params.ui_config.placeholder_text || '请输入您的问题...'"
                                @keyup.enter.native="sendMessage"
                                @paste.native="handlePaste"
                            ></el-input>
                            <div class="paste-tip" style="font-size: 12px; color: #909399; margin-top: 5px; text-align: left;">
                                <i class="el-icon-info-circle"></i> 提示：您可以直接粘贴(Ctrl+V)剪贴板中的图片
                            </div>
                            <div style="margin-top: 14px; text-align: right; display: flex; justify-content: flex-end; align-items: center;">
                                <el-button @click="triggerFileUpload" type="primary" size="large" style="margin-right: 10px;">
                                    <i class="el-icon-upload"></i>
                                    上传图片
                                </el-button>
                                <el-button type="primary" @click.native="sendMessage" size="large">
                                    {{ params.ui_config.send_button_text || '发送' }}
                                </el-button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- FAQ部分，仅在未开始会话且不在历史模式时显示 -->
                    <div v-if="!sessionStarted && !historyMode && params.faq_config && params.faq_config.enabled" class="faq-container">
                        <h3>常见问题</h3>
                        <el-collapse v-model="activeFaq">
                            <el-collapse-item v-for="(category, index) in params.faq_config.categories" :key="index" :title="category.name" :name="category.id">
                                <div v-for="(item, qIndex) in category.questions" :key="qIndex" class="faq-item" @click="useFaqQuestion(item)">
                                    <p><strong><span class="question-icon">Q:</span> {{ item.question }}</strong></p>
                                    <p style="color: #64748b"><span class="answer-icon">A:</span> {{ item.answer }}</p>
                                </div>
                            </el-collapse-item>
                        </el-collapse>
                    </div>
                </el-col>
                
                <!-- 历史查询部分，移除栅格系统 -->
            </el-row>
            
            <!-- 历史查询部分，移到栅格系统外部 -->
            <div class="history-sidebar-container" v-show="sessionStarted || (historyMode && !sessionStarted)" v-bind:style="historyCollapsed ? 'width: 0; overflow: visible;' : ''">
                <div style="position: fixed; right: 25px; top: 110px; width: 22%; max-width: 350px; z-index: 10;">
                    <!-- 添加收起/展开箭头 -->
                    <div @click="toggleHistorySidebar" class="toggle-history-btn" :class="{'collapsed': historyCollapsed}" style="position: absolute; right: -40px; top: 20px; width: 40px; height: 160px; background: #4e6ef2; border-radius: 0 4px 4px 0; cursor: pointer; display: flex; flex-direction: column; align-items: center; justify-content: center; color: white; box-shadow: 2px 0 8px rgba(0,0,0,0.1); transition: all 0.3s ease; z-index: 11;">
                        <i :class="historyCollapsed ? 'el-icon-arrow-right' : 'el-icon-arrow-left'" style="font-size: 18px; margin-bottom: 8px;"></i>
                        <div class="vertical-text">查询历史对话</div>
                    </div>
                    <div :class="['user-form', {'compact': sessionStarted}]" style="box-shadow: 0 2px 20px rgba(0,0,0,0.08); border-radius: 12px; padding: 25px 20px; transition: all 0.3s ease; position: absolute; width: 100%;" :style="historyCollapsed ? 'transform: translateX(1000px); display: none;' : 'transform: translateX(0); display: block;'">
                        <h2 style="margin-bottom: 15px; font-size: 18px; color: #1e293b; text-align: center; position: relative;">
                            查询历史对话
                            <span style="height: 3px; width: 40px; background: #4e6ef2; position: absolute; bottom: -8px; left: 50%; transform: translateX(-50%); border-radius: 2px;"></span>
                        </h2>
                        <el-form :model="historyQuery" :rules="historyRules" ref="historyForm" label-width="60px" style="margin-top: 20px;">
                            <el-form-item label="邮箱">
                                <el-input v-model="historyQuery.email" placeholder="请输入您的联系邮箱" @change="autoQueryHistory" size="small"></el-input>
                            </el-form-item>
                            <el-form-item label="电话">
                                <el-input v-model="historyQuery.phone" placeholder="请输入您的联系电话" @change="autoQueryHistory" size="small"></el-input>
                            </el-form-item>
                            <!-- 添加搜索框 -->
                            <el-form-item label="搜索">
                                <el-input v-model="historyQuery.searchText" placeholder="按名称或内容筛选" size="small" clearable @input="filterHistorySessions"></el-input>
                            </el-form-item>
                            <div style="font-size: 12px; color: #909399; margin: -5px 0 10px 60px;">邮箱或电话至少填写一项</div>
                            <el-form-item style="text-align: center; margin-bottom: 10px;">
                                <el-button type="primary" @click="queryHistory" size="small" style="width: 90px;">查询</el-button>
                                <el-button @click="backToChat" v-if="!sessionStarted" size="small" style="width: 90px;">返回</el-button>
                            </el-form-item>
                        </el-form>
                        
                        <div v-if="historySessions.length > 0" class="history-sessions" style="max-height: calc(100vh - 270px); overflow-y: auto; margin-top: 15px;">
                            <h3 style="margin: 15px 0 12px; font-size: 16px; color: #334155; padding-left: 10px; border-left: 3px solid #4e6ef2;">
                                历史会话 <span style="font-size: 12px; color: #64748b;">(共 {{ filteredSessions.length }}/{{ historySessions.length }} 个)</span>
                            </h3>
                            
                            <!-- 如果按联系人分组 -->
                            <template v-if="isGroupByEmail">
                                <div v-for="(group, contactKey) in groupedSessions" :key="contactKey" style="margin-bottom: 20px;">
                                    <!-- 联系人分组头部 -->
                                    <div style="display: flex; align-items: center; margin-bottom: 10px; background: #f0f7ff; padding: 8px 12px; border-radius: 6px; border-left: 3px solid #3b82f6;">
                                        <i class="el-icon-message" style="margin-right: 8px; color: #3b82f6;"></i>
                                        <span style="font-weight: 500; color: #334155;">{{ contactKey }}</span>
                                        <span style="margin-left: 10px; font-size: 12px; color: #64748b;">({{ group.length }}个会话)</span>
                                    </div>
                                    
                                    <!-- 显示每个会话及其对应的联系人信息 -->
                                    <el-card v-for="session in group" :key="session.id" style="margin-bottom: 12px; border-radius: 8px;" :class="{'has-unread': session.has_unread}">
                                        <div slot="header" style="display: flex; align-items: center; justify-content: space-between; padding: 8px 0; font-size: 13px;">
                                            <span>
                                                {{ formatTime(session.last_time) }}
                                                <el-badge v-if="session.unread_staff_count > 0" :value="session.unread_staff_count" class="session-badge" type="danger"></el-badge>
                                            </span>
                                            <el-button style="padding: 4px 10px; font-size: 12px; color: #3b82f6; background: #f0f7ff; border-radius: 4px; border: 1px solid #e0eaff;" 
                                                    type="text" 
                                                    @click="viewSessionDetail(session)"
                                                    class="switch-btn">
                                                <i class="el-icon-refresh-right" style="margin-right: 3px;"></i>切换
                                            </el-button>
                                        </div>
                                        <div style="font-size: 13px;">
                                            <!-- 联系人信息显示 -->
                                            <div v-if="session.contact" style="margin-bottom: 8px; display: flex; align-items: center; background: #f5f9ff; padding: 6px 10px; border-radius: 6px; font-size: 12px;">
                                                <!-- 显示联系人名称首字母 -->
                                                <div style="width: 22px; height: 22px; border-radius: 50%; background: #4e6ef2; color: white; display: flex; align-items: center; justify-content: center; margin-right: 8px; font-weight: bold;">
                                                    {{ session.contact.name ? session.contact.name.substring(0, 1).toUpperCase() : 'U' }}
                                                </div>
                                                <div>
                                                    <!-- 显示联系人名称和ID -->
                                                    <div style="font-weight: 500;">{{ session.contact.name }} <span style="font-size: 11px; color: #94a3b8;">(ID: {{ session.contact.id }})</span></div>
                                                    <!-- 显示联系人邮箱 -->
                                                    <div style="color: #64748b; font-size: 11px;">
                                                        <a href="javascript:void(0);" @click="searchByEmail(session.contact.email)" style="color: #4e6ef2; text-decoration: none;">
                                                            {{ session.contact.email }}
                                                            <i class="el-icon-search" style="font-size: 10px; margin-left: 2px;"></i>
                                                        </a>
                                                        <!-- 添加查看该联系人所有会话的按钮 -->
                                                        <a href="javascript:void(0);" @click="viewAllSessionsByContactId(session.contact.id)" style="color: #10b981; text-decoration: none; margin-left: 8px;">
                                                            <i class="el-icon-chat-dot-round" style="font-size: 10px; margin-right: 2px;"></i>所有会话
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- 会话标题和状态 -->
                                            <p class="session-title">
                                                <el-tooltip :content="session.title || '未命名会话'" placement="top">
                                                    <span>{{ session.title && session.title.length > 18 ? session.title.substring(0, 18) + '...' : (session.title || '未命名会话') }}</span>
                                                </el-tooltip>
                                                <span style="font-size: 11px; color: #94a3b8; margin-left: 5px;">#{{ session.id }}</span>
                                            </p>
                                            <p style="margin-bottom: 6px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;"><strong>最近消息：</strong>{{ session.last_message }}</p>
                                            <p style="display: flex; justify-content: space-between; align-items: center;">
                                                <strong>状态：</strong>
                                                <span v-if="session.status === 'open'" style="color: #10b981; font-weight: 500; background: rgba(16,185,129,0.1); padding: 2px 8px; border-radius: 4px; font-size: 12px;">进行中</span>
                                                <span v-else style="color: #ef4444; font-weight: 500; background: rgba(239,68,68,0.1); padding: 2px 8px; border-radius: 4px; font-size: 12px;">已关闭</span>
                                            </p>
                                        </div>
                                    </el-card>
                                </div>
                            </template>
                            
                            <!-- 添加非分组显示方式，确保所有会话都显示 -->
                            <template v-else>
                                <el-card v-for="session in filteredSessions" :key="session.id" style="margin-bottom: 12px; border-radius: 8px;" :class="{'has-unread': session.has_unread}">
                                    <div slot="header" style="display: flex; align-items: center; justify-content: space-between; padding: 8px 0; font-size: 13px;">
                                        <span>
                                            {{ formatTime(session.last_time) }}
                                            <el-badge v-if="session.unread_staff_count > 0" :value="session.unread_staff_count" class="session-badge" type="danger"></el-badge>
                                        </span>
                                        <el-button style="padding: 4px 10px; font-size: 12px; color: #3b82f6; background: #f0f7ff; border-radius: 4px; border: 1px solid #e0eaff;" 
                                                type="text" 
                                                @click="viewSessionDetail(session)"
                                                class="switch-btn">
                                            <i class="el-icon-refresh-right" style="margin-right: 3px;"></i>切换
                                        </el-button>
                                    </div>
                                    <div style="font-size: 13px;">
                                        <!-- 联系人信息显示 -->
                                        <div v-if="session.contact" style="margin-bottom: 8px; display: flex; align-items: center; background: #f5f9ff; padding: 6px 10px; border-radius: 6px; font-size: 12px;">
                                            <!-- 显示联系人名称首字母 -->
                                            <div style="width: 22px; height: 22px; border-radius: 50%; background: #4e6ef2; color: white; display: flex; align-items: center; justify-content: center; margin-right: 8px; font-weight: bold;">
                                                {{ session.contact.name ? session.contact.name.substring(0, 1).toUpperCase() : 'U' }}
                                            </div>
                                            <div>
                                                <!-- 显示联系人名称和ID -->
                                                <div style="font-weight: 500;">{{ session.contact.name }} <span style="font-size: 11px; color: #94a3b8;">(ID: {{ session.contact.id }})</span></div>
                                                <!-- 显示联系人邮箱 -->
                                                <div style="color: #64748b; font-size: 11px;">
                                                    <a href="javascript:void(0);" @click="searchByEmail(session.contact.email)" style="color: #4e6ef2; text-decoration: none;">
                                                        {{ session.contact.email }}
                                                        <i class="el-icon-search" style="font-size: 10px; margin-left: 2px;"></i>
                                                    </a>
                                                    <!-- 添加查看该联系人所有会话的按钮 -->
                                                    <a href="javascript:void(0);" @click="viewAllSessionsByContactId(session.contact.id)" style="color: #10b981; text-decoration: none; margin-left: 8px;">
                                                        <i class="el-icon-chat-dot-round" style="font-size: 10px; margin-right: 2px;"></i>所有会话
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- 会话标题和状态 -->
                                        <p class="session-title">
                                            <el-tooltip :content="session.title || '未命名会话'" placement="top">
                                                <span>{{ session.title && session.title.length > 18 ? session.title.substring(0, 18) + '...' : (session.title || '未命名会话') }}</span>
                                            </el-tooltip>
                                            <span style="font-size: 11px; color: #94a3b8; margin-left: 5px;">#{{ session.id }}</span>
                                        </p>
                                        <p style="margin-bottom: 6px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;"><strong>最近消息：</strong>{{ session.last_message }}</p>
                                        <p style="display: flex; justify-content: space-between; align-items: center;">
                                            <strong>状态：</strong>
                                            <span v-if="session.status === 'open'" style="color: #10b981; font-weight: 500; background: rgba(16,185,129,0.1); padding: 2px 8px; border-radius: 4px; font-size: 12px;">进行中</span>
                                            <span v-else style="color: #ef4444; font-weight: 500; background: rgba(239,68,68,0.1); padding: 2px 8px; border-radius: 4px; font-size: 12px;">已关闭</span>
                                        </p>
                                    </div>
                                </el-card>
                            </template>
                            
                            <!-- 添加分组/取消分组按钮 -->
                            <div style="text-align: center; margin-top: 15px;">
                                <el-button size="small" type="text" @click="toggleGroupByEmail">
                                    <i :class="isGroupByEmail ? 'el-icon-s-unfold' : 'el-icon-s-fold'" style="margin-right: 4px;"></i>
                                    {{ isGroupByEmail ? '取消按邮箱分组' : '按邮箱分组显示' }}
                                </el-button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="footer">
        <div class="footer-content">
            <div class="footer-item">
                <a href="{$domain}" target="_blank">Powered by {$siteName}</a>
            </div>
            
            {if condition="!empty($icpNumber)"}
            <div class="footer-item">
                <a href="https://beian.miit.gov.cn/" target="_blank">{$icpNumber}</a>
            </div>
            {/if}
            
            {if condition="!empty($gaNumber)"}
            <div class="footer-item">
                <a href="http://www.beian.gov.cn/" target="_blank">
                    <img src="/static/images/beian.png" alt="备案图标" class="beian-icon">{$gaNumber}
                </a>
            </div>
            {/if}
        </div>
    </div>

    <!-- 添加历史对话遮罩 -->
    <div class="history-backdrop" id="historyBackdrop"></div>

    <!-- 更新Vue的引入方式，确保正确加载Vue 3 -->
    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>
    <script src="/assets/plugin/Customersystem/qiantai.js"></script>

    <script>
        // 确保Vue 3正确加载
        if (typeof Vue === 'undefined') {

        } else if (typeof Vue.createApp !== 'function') {

        } else if (typeof ElementPlus === 'undefined') {

        } else {
            // 设置Element Plus
            const app = Vue.createApp({
                data() {
                    return {
                        sessionStarted: false,
                        historyMode: false,
                        viewingHistory: false,
                        sessionId: null,
                        contactId: null,
                        userInfo: {
                            name: '',
                            email: '',
                            phone: '',
                            qq: '',
                            wechat: '',
                            message: ''
                        },
                        rules: {
                            name: [
                                { required: true, message: '请输入您的姓名', trigger: 'blur' }
                            ],
                            email: [
                                { required: true, message: '请输入您的邮箱', trigger: 'blur' },
                                { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
                            ],
                            message: [
                                { required: true, message: '请输入您的问题', trigger: 'blur' }
                            ]
                        },
                        historyQuery: {
                            email: '',
                            phone: '',
                            searchText: ''
                        },
                        historyRules: {
                            email: [
                                { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
                            ]
                        },
                        historySessions: [],
                        messages: [],
                        messageInput: '',
                        isTyping: false,
                        activeFaq: [],
                        pollingTimer: null,
                        historyPollingTimer: null,
                        pollingInterval: 5000, // 5秒一次轮询
                        readStatusPollingTimer: null, // 添加已读状态轮询定时器
                        readStatusPollingInterval: 10000, // 10秒检查一次已读状态
                        emojiVisible: false,
                        uploadFile: null,
                        uploadPreview: '',
                        hasUnreadMessage: false,
                        unreadCount: 0,
                        originalTitle: document.title,
                        titleBlinkTimer: null,
                        emojis: [
                            '😊', '😃', '😁', '😅', '😂', '🤣', '😉', '😍', '😘', '🥰', 
                            '😋', '😎', '😇', '🤔', '🙄', '😐', '😒', '😔', '😪', '😴', 
                            '🤗', '🤭', '🥳', '😌', '😏', '👍', '👌', '👏', '🙏', '🤝',
                            '👋', '🎉', '✅', '❓', '⭐', '🔥', '🎁', '💯'
                        ],
                        notificationSound: null,
                        isPageActive: document.hasFocus(),
                        params: {},
                        historyCollapsed: localStorage.getItem('historyCollapsed') === 'true',
                        chatType: 'merchant', // 'customer' 或 'merchant'
                        merchantToken: '',
                        selectedMerchant: null,
                        merchantInfo: {},
                        merchantSearchError: '',
                        autoReplyTimer: null, // 添加自动回复定时器变量
                        currentContact: null, // 添加当前联系人信息变量
                        isGroupByEmail: false, // 添加按邮箱分组状态
                        groupedSessions: {}, // 添加分组数据
                        readStatusMap: {}, // 存储消息已读状态
                        readStatusTimer: null, // 已读状态轮询定时器
                        
                        // 滚动节流计时器
                        scrollThrottleTimer: null,
                    };
                },
                created() {
                    this.getParams();
                    
                    this.originalTitle = document.title; // 保存页面原始标题
                    this.startPolling();
                    
                    // 注册页面可见性变化事件
                    document.addEventListener('visibilitychange', this.handleVisibilityChange);
                    
                    // 注册窗口焦点事件
                    window.addEventListener('focus', this.handleFocus);
                    window.addEventListener('blur', this.handleBlur);
                    
                    // 启动已读状态轮询
                    this.startReadStatusPolling();
                },
                beforeDestroy() {
                    this.stopPolling();
                    this.stopTitleBlinking();
                    this.stopReadStatusPolling(); // 停止已读状态轮询
                    
                    document.removeEventListener('visibilitychange', this.handleVisibilityChange);
                    window.removeEventListener('focus', this.handleFocus);
                    window.removeEventListener('blur', this.handleBlur);
                },
                computed: {
                    sortedMessages() {
                        return [...this.messages].sort((a, b) => a.create_time - b.create_time);
                    },
                    filteredSessions() {
                        if (!this.historySessions || this.historySessions.length === 0) {
                            return [];
                        }
                        
                        const searchText = (this.historyQuery.searchText || '').toLowerCase().trim();
                        
                        // 如果没有搜索词，返回所有会话
                        if (!searchText) {
                            return this.historySessions;
                        }
                        
                        return this.historySessions.filter(session => {
                            // 检查会话联系人信息
                            const contact = session.contact || {};
                            const contactName = (contact.name || '').toLowerCase();
                            const contactEmail = (contact.email || '').toLowerCase();
                            
                            // 检查会话标题和最后消息
                            const title = (session.title || '').toLowerCase();
                            const lastMessage = (session.last_message || '').toLowerCase();
                            
                            // 搜索条件：匹配联系人名称、邮箱、会话标题或最后消息
                            return contactName.includes(searchText) || 
                                   contactEmail.includes(searchText) || 
                                   title.includes(searchText) || 
                                   lastMessage.includes(searchText);
                        });
                    },
                    groupedSessions() {
                        // 将历史会话按联系人信息分组（邮箱优先，没有邮箱则用手机号）
                        return this.filteredSessions.reduce((acc, session) => {
                            if (!session.contact) {
                                return acc; // 跳过没有联系人信息的会话
                            }

                            // 优先使用邮箱，如果没有邮箱则使用手机号作为分组键
                            const groupKey = session.contact.email || session.contact.phone || '未知联系人';

                            if (!acc[groupKey]) {
                                acc[groupKey] = [];
                            }
                            acc[groupKey].push(session);
                            return acc;
                        }, {});
                    }
                },
                methods: {
                    getParams() {
                        axios.post('/plugin/Customersystem/api/getParams')
                            .then(response => {
                                if (response.data.code === 200) {
                                    let data = response.data.data;
                                    
                                    // 确保配置中的布尔值被正确解析
                                    if (data.settings) {
                                        for (let key in data.settings) {
                                            if (data.settings[key] === 'true') {
                                                data.settings[key] = true;
                                            } else if (data.settings[key] === 'false') {
                                                data.settings[key] = false;
                                            }
                                        }
                                    }
                                    
                                    this.params = data;
                                    
                                } else {
                                    this.$message.error(response.data.msg || '获取参数失败');
                                }
                            })
                            .catch(error => {
                                
                                this.$message.error('获取参数失败，请刷新页面重试');
                            });
                    },
                    startPolling() {
                        // 清除任何现有的轮询定时器
                        if (this.pollingTimer) {
                            clearInterval(this.pollingTimer);
                            this.pollingTimer = null;
                        }
                        
                        // 首先立即获取一次最新消息和会话状态
                        this.pollNewMessages();
                        
                        // 然后设置定时轮询
                        this.pollingTimer = setInterval(() => {
                            this.pollNewMessages();
                        }, this.pollingInterval);
                        
                        
                    },
                    
                    stopPolling() {
                        if (this.pollingTimer) {
                            clearInterval(this.pollingTimer);
                            this.pollingTimer = null;
                        }
                    },
                    
                    pollNewMessages() {
                        
                        
                        // 如果会话ID为null或undefined，则不发送API请求
                        if (!this.sessionId) {
                            return; // 直接返回，不执行后续API调用
                        }
                        
                        axios.post('/plugin/Customersystem/api/getRealtimeMessages', {
                            session_id: this.sessionId,
                            last_message_id: this.getLastMessageId(),
                            check_session_status: true,
                            check_recalled: true,  // 添加这个参数
                            check_read_status: true,  // 检查是否有消息已读状态变更
                            sender_type: 'customer'  // 指明当前用户为客户，服务端会据此决定哪些消息需要标记为已读
                        })
                        .then(response => {
                            if (response.data.code === 200) {
                                // 从返回数据中获取消息和会话状态
                                const responseData = response.data.data;
                                const messages = responseData.messages || [];
                                const readStatusChangedMessages = responseData.read_status_changed || []; // 获取已读状态变更的消息
                                const recalledMessages = responseData.recalled_messages || []; // 获取已撤回的消息
                                const sessionStatus = responseData.session_status;
                                const isClosed = responseData.is_closed === true;
                                
                                // 如果返回了会话状态，更新本地缓存的状态
                                if (sessionStatus) {
                                    this.updateSessionStatus(this.sessionId, sessionStatus);
                                }
                                
                                // 如果会话已经关闭，更新UI状态
                                if (isClosed || sessionStatus === 'closed') {
                                    this.disableInputAndUpdate();
                                    
                                }
                                
                                // 处理新消息
                                if (messages && messages.length > 0) {
                                    let hasNewMessages = false;
                                    let newStaffMessages = [];
                                    let sessionClosed = false;
                                    
                                    messages.forEach(message => {
                                        // 确保消息ID是整数
                                        if (message.id) {
                                            message.id = parseInt(message.id);
                                        }
                                        // 确保时间戳是整数
                                        if (message.create_time) {
                                            message.create_time = parseInt(message.create_time);
                                        }
                                        
                                        // 检查是否有会话关闭的系统消息
                                        if (message.sender_type === 'staff' && message.sender_id === 0 && 
                                            message.message && message.message.includes('会话已关闭')) {
                                            sessionClosed = true;
                                            
                                            // 更新本地历史会话的状态
                                            this.updateSessionStatus(this.sessionId, 'closed');
                                            // 确保禁用输入框和发送按钮
                                            this.disableInputAndUpdate();
                                        }
                                        
                                        const exists = this.messages.some(msg => msg.id === message.id);
                                        if (!exists) {
                                            this.messages.push(message);
                                            // 添加新消息
                                            hasNewMessages = true;
                                            
                                            // 如果是客服消息，添加到通知列表
                                            if (message.sender_type === 'staff' || message.role_type === 'staff' || 
                                                message.sender_type === 'merchant' || message.role_type === 'merchant') {
                                                newStaffMessages.push(message);
                                            }
                                            
                                            // 如果当前页面处于活跃状态，并且是客服或商家的消息，自动标记为已读
                                            if ((message.sender_type === 'staff' || message.role_type === 'staff' || 
                                                 message.sender_type === 'merchant' || message.role_type === 'merchant') && 
                                                (document.hasFocus() && document.visibilityState === 'visible')) {
                                                this.markMessageAsRead(message.id);
                                            }
                                        } else {
                                            // 如果消息已存在，检查是否需要更新（比如被撤回或已读状态）
                                            const index = this.messages.findIndex(msg => msg.id === message.id);
                                            if (index !== -1) {
                                                // 更新消息对象，保留原有属性的同时更新变更的部分
                                                this.messages[index] = {...this.messages[index], ...message};
                                            }
                                        }
                                    });
                                    
                                    if (hasNewMessages) {
                                        this.$nextTick(() => {
                                            this.scrollToBottom();
                                            
                                            if (newStaffMessages.length > 0) {
                                                // 收到新的客服消息
                                                
                                                // 更新未读消息数量
                                                if (!document.hasFocus() || document.visibilityState !== 'visible') {
                                                    this.unreadCount += newStaffMessages.length;
                                                    this.hasUnreadMessage = true;
                                                    this.startTitleBlinking();
                                                    this.playNotificationSound();
                                                } else {
                                                    // 如果页面处于活跃状态，自动标记新消息为已读
                                                    newStaffMessages.forEach(message => {
                                                        this.markMessageAsRead(message.id);
                                                    });
                                                }
                                            }
                                        });
                                    }
                                }
                                
                                // 处理已读状态变更的消息
                                if (readStatusChangedMessages && readStatusChangedMessages.length > 0) {
                                    readStatusChangedMessages.forEach(message => {
                                        const index = this.messages.findIndex(msg => msg.id === message.id);
                                        if (index !== -1) {
                                            // 只更新已读状态相关字段
                                            this.messages[index].is_read = message.is_read;
                                            this.messages[index].read_time = message.read_time;
                                        }
                                    });
                                    

                                }
                                
                                // 处理已撤回的消息
                                if (recalledMessages && recalledMessages.length > 0) {
                                    recalledMessages.forEach(recalledMsg => {
                                        // 更新本地消息列表中对应的消息
                                        const index = this.messages.findIndex(m => m.id === recalledMsg.id);
                                        if (index !== -1) {
                                            // 替换为撤回后的消息
                                            this.messages[index] = recalledMsg;
                                        }
                                    });
                                }
                            } else {
                                // 获取消息失败
                            }
                        })
                        .catch(error => {
                            // 获取实时消息失败
                        });
                    },
                    
                    getLastMessageId() {
                        if (this.messages.length === 0) {
                            return 0;
                        }

                        const maxId = Math.max(...this.messages.map(msg => parseInt(msg.id || 0)));
                        return maxId;
                    },
                    // 获取用户最后一条消息
                    getLastCustomerMessage() {
                        if (this.messages.length === 0) {
                            return null;
                        }
                        
                        // 按时间排序消息，获取最新的客户消息
                        const sortedMessages = [...this.messages].sort((a, b) => b.create_time - a.create_time);
                        for (let i = 0; i < sortedMessages.length; i++) {
                            if (sortedMessages[i].role_type === 'customer' || 
                                sortedMessages[i].sender_type === 'customer') {
                                return sortedMessages[i];
                            }
                        }
                        return null;
                    },
                    startChat() {
                        this.$refs.userForm.validate(valid => {
                            if (valid) {
                                // 检查是否选择了商家
                                if (this.chatType === 'merchant' && !this.selectedMerchant) {
                                    this.$message.warning('请先查找并选择要联系的商家');
                                    return;
                                }
                                
                                // 显示加载状态
                                const loading = this.$loading({
                                    lock: true,
                                    text: '创建会话中...',
                                    spinner: 'el-icon-loading',
                                    background: 'rgba(0, 0, 0, 0.7)'
                                });
                                
                                // 准备请求参数
                                const postData = {...this.userInfo};
                                
                                // 如果是与商家聊天，添加商家信息到消息内容
                                if (this.chatType === 'merchant' && this.selectedMerchant) {
                                    postData.merchant_id = this.selectedMerchant;
                                    
                                    // 默认使用ID作为显示值
                                    let merchantDisplay = this.selectedMerchant;
                                    
                                    // 如果已经有商家名称信息，直接使用
                                    if (this.merchantNames && this.merchantNames[this.selectedMerchant]) {
                                        merchantDisplay = this.merchantNames[this.selectedMerchant];
                                    } 
                                    // 如果没有商家名称信息，异步获取
                                    else if (!this.merchantNames) {
                                        this.merchantNames = {};
                                        // 异步获取商家名称，但不等待结果
                                        axios.post(this.apiUrl + '/getMerchantByToken', {
                                            merchant_id: this.selectedMerchant
                                        }).then(response => {
                                            if (response.data.code === 200 && response.data.data) {
                                                this.merchantNames[this.selectedMerchant] = response.data.data.nickname || this.selectedMerchant;
                                            }
                                        }).catch(() => {});
                                    }
                                    
                                    postData.message = (postData.message || '') + 
                                        (postData.message ? '\n\n' : '') + 
                                        `[与商家ID:${this.selectedMerchant} 的对话]`;
                                }
                                
                                axios.post('/plugin/Customersystem/api/createSession', postData)
                                    .then(response => {
                                        if (response.data.code === 200) {
                                            this.sessionStarted = true;
                                            this.sessionId = response.data.data.session_id;
                                            this.contactId = response.data.data.contact_id;
                                            this.messages = response.data.data.messages;
                                            
                                            this.$nextTick(() => {
                                                this.scrollToBottom();
                                            });
                                            
                                            // 启动所有轮询
                                            this.startAllPolling();
                                        } else {
                                            // 使用自定义的提示样式，让错误更加明显
                                            this.$message({
                                                message: response.data.msg || '创建会话失败',
                                                type: 'error',
                                                customClass: 'custom-message',
                                                duration: 5000 // 显示时间延长到5秒
                                            });
                                            
                                            // 如果是会话限制的问题，添加额外提示
                                            if (response.data.msg && response.data.msg.includes('已有') && response.data.msg.includes('个进行中的会话')) {
                                                this.$confirm(response.data.msg + '。您是否要查看现有会话？', '会话数量已达上限', {
                                                    confirmButtonText: '查看现有会话',
                                                    cancelButtonText: '取消',
                                                    type: 'warning'
                                                }).then(() => {
                                                    // 用户选择查看现有会话
                                                    this.historyQuery.email = this.userInfo.email;
                                                    this.historyQuery.phone = this.userInfo.phone;
                                                    this.checkHistory();
                                                    this.queryHistory();
                                                }).catch(() => {
                                                    // 用户取消，不做任何操作
                                                });
                                            }
                                        }
                                    })
                                    .catch(error => {
                                        // 创建会话失败
                                        this.$message.error('网络错误，请稍后重试');
                                    })
                                    .finally(() => {
                                        loading.close();
                                    });
                            }
                        });
                    },
                    sendMessage(fromPresetClick = false) {
                        if ((!this.messageInput || this.messageInput.trim() === '') && !this.uploadFile) {
                            this.$message({
                                message: '请输入消息或选择文件',
                                type: 'warning',
                                customClass: 'custom-message'
                            });
                            return;
                        }
                        
                        // 检查会话状态，如果已关闭则不允许发送
                        if (this.getSessionClosed()) {
                            this.$message({
                                message: '会话已关闭，无法发送消息',
                                type: 'warning',
                                customClass: 'custom-message'
                            });
                            return;
                        }
                        
                        // 检查会话ID是否存在
                        if (!this.sessionId) {
                            this.$message({
                                message: '会话不存在，无法发送消息',
                                type: 'warning',
                                customClass: 'custom-message'
                            });
                            return;
                        }
                        
                        // 禁用发送按钮，防止重复发送
                        const sendBtn = document.querySelector('.el-button--primary');
                        if (sendBtn) sendBtn.disabled = true;
                        
                        if (this.uploadFile) {
                            // 如果已经有预览URL，说明文件已经上传过（如剪贴板图片）
                            if (this.uploadPreview && this.uploadFile.fromClipboard) {
                                // 直接发送消息，不需要再次上传
                                this.sendFileMessage(this.uploadPreview, 'image');
                            } else {
                                const formData = new FormData();
                                formData.append('file', this.uploadFile);
                                
                                // 显示上传进度
                                const loading = this.$loading({
                                    lock: true,
                                    text: '正在上传文件...',
                                    spinner: 'el-icon-upload',
                                    background: 'rgba(0, 0, 0, 0.7)'
                                });
                                
                                // 使用官方上传接口
                                axios.post('/shopApi/Upload/file', formData, {
                                    headers: {
                                        'Content-Type': 'multipart/form-data'
                                    }
                                })
                                    .then(response => {
                                        // 涓婁紶鎴愬姛;
                                        if (response.data.code === 1 || response.data.code === 200) {
                                            // 获取上传后的URL
                                            const fileUrl = response.data.data.url || response.data.data;
                                            const fileType = this.uploadFile.type.includes('image') ? 'image' : 'file';
                                            this.sendFileMessage(fileUrl, fileType);
                                        } else {
                                            this.$message.error(response.data.msg || '上传文件失败');
                                            if (sendBtn) sendBtn.disabled = false;
                                        }
                                    })
                                    .catch(error => {

                                        let errorMsg = '上传文件失败，请稍后重试';
                                        try {
                                            if (error && error.response && error.response.data && error.response.data.msg) {
                                                errorMsg = error.response.data.msg;
                                            }
                                        } catch (e) {
                                            // 如果嵌套访问时出错，使用默认错误消息
                                        }
                                        this.$message.error(errorMsg);
                                        if (sendBtn) sendBtn.disabled = false;
                                    })
                                    .finally(() => {
                                        loading.close(); // 关闭加载状态
                                    });
                            }
                        } else {
                            // 确保消息不是纯空白字符
                            const messageText = this.messageInput.trim();
                            if (!messageText) {
                                this.$message({
                                    message: '消息内容不能为空',
                                    type: 'warning',
                                    customClass: 'custom-message'
                                });
                                if (sendBtn) sendBtn.disabled = false;
                                return;
                            }
                            
                            const data = {
                                session_id: this.sessionId,
                                message: messageText,
                                sender_type: 'customer'
                            };
                            
                            // 如果有当前联系人ID，添加到请求中
                            if (this.contactId) {
                                data.sender_id = this.contactId;
                            }
                            
                            axios.post('/plugin/Customersystem/api/sendMessage', data)
                                .then(response => {
                                    if (response.data.code === 200) {
                                        // 如果返回的数据是数组（表示有多条消息，如预设问题匹配），则全部添加到消息列表
                                        if (Array.isArray(response.data.data)) {
                                            response.data.data.forEach(msg => {
                                                // 为发送的客户消息初始化已读状态
                                                if (msg.role_type === 'customer') {
                                                    msg.is_read = 0;  // 初始状态为未读
                                                    msg.read_time = 0;
                                                }
                                                this.messages.push(msg);
                                            });
                                        } else {
                                            // 为发送的客户消息初始化已读状态
                                            if (response.data.data.role_type === 'customer') {
                                                response.data.data.is_read = 0;  // 初始状态为未读
                                                response.data.data.read_time = 0;
                                            }
                                            this.messages.push(response.data.data);
                                        }
                                        this.messageInput = '';
                                        
                                        this.$nextTick(() => {
                                            this.scrollToBottom();
                                            
                                            // 如果不是从预设问题点击触发，则隐藏所有预设问题元素
                                            if (!fromPresetClick) {
                                                const presetElements = document.querySelectorAll('.preset-questions');
                                                presetElements.forEach(el => {
                                                    el.style.display = 'none';
                                                });
                                            }
                                            
                                            // 因为我们发送了新消息，标记所有客服/商家消息为已读
                                            this.markAllOtherMessagesAsRead();
                                        });
                                        
                                        // 检查是否有实际内容的消息
                                        const lastSentMessage = this.messages[this.messages.length - 1];
                                        const hasValidContent = lastSentMessage && 
                                                              lastSentMessage.message && 
                                                              lastSentMessage.message.trim() !== '';
                                        
                                        // 只有当消息有实际内容时才触发自动回复
                                        if (hasValidContent) {
                                            // 移除自动回复调用
                                        }
                                    } else {
                                        this.$message.error(response.data.msg || '发送消息失败');
                                    }
                                })
                                .catch(error => {

                                    let errorMsg = '发送消息失败，请稍后重试';
                                    try {
                                        if (error && error.response && error.response.data && error.response.data.msg) {
                                            errorMsg = error.response.data.msg;
                                        }
                                    } catch (e) {
                                        // 如果嵌套访问时出错，使用默认错误消息
                                    }
                                    this.$message.error(errorMsg);
                                })
                                .finally(() => {
                                    if (sendBtn) sendBtn.disabled = false;
                                    
                                    // 发送消息后立即刷新一次消息列表，确保状态同步
                                    this.pollNewMessages();
                                });
                        }
                    },
                    sendFileMessage(fileUrl, fileType) {
                        // 检查会话状态，如果已关闭则不允许发送
                        if (this.getSessionClosed()) {
                            this.$message({
                                message: '会话已关闭，无法发送消息',
                                type: 'warning',
                                customClass: 'custom-message'
                            });
                            return;
                        }
                        
                        // 检查会话ID是否存在
                        if (!this.sessionId) {
                            this.$message({
                                message: '会话不存在，无法发送消息',
                                type: 'warning',
                                customClass: 'custom-message'
                            });
                            return;
                        }
                        
                        // 禁用发送按钮，防止重复发送
                        const sendBtn = document.querySelector('.el-button--primary');
                        if (sendBtn) sendBtn.disabled = true;
                        
                        const data = {
                            session_id: this.sessionId,
                            message_type: fileType || 'file',
                            file_url: fileUrl,
                            sender_type: 'customer'
                        };
                        
                        // 如果是图片消息，直接创建HTML格式的图片标签
                        if (fileType === 'image') {
                            data.message = '<div class="message-image-container"><img src="' + fileUrl + '" class="message-image" alt="图片消息" style="display: block;"><!----></div>';
                        }
                        
                        // 如果有当前联系人ID，添加到请求中
                        if (this.contactId) {
                            data.sender_id = this.contactId;
                        }
                        
                        // 检查是否有文本消息，如果有则添加到数据中
                        // 只在非图片消息类型时添加文本
                        const hasTextMessage = this.messageInput && this.messageInput.trim() !== '';
                        if (hasTextMessage && fileType !== 'image') {
                            data.message = this.messageInput.trim();
                        }
                        
                        axios.post('/plugin/Customersystem/api/sendMessage', data)
                            .then(response => {
                                if (response.data.code === 200) {
                                    // 如果返回的数据是数组（表示有多条消息，如预设问题匹配），则全部添加到消息列表
                                    if (Array.isArray(response.data.data)) {
                                        response.data.data.forEach(msg => {
                                            // 为发送的客户消息初始化已读状态
                                            if (msg.role_type === 'customer') {
                                                msg.is_read = 0;  // 初始状态为未读
                                                msg.read_time = 0;
                                            }
                                            this.messages.push(msg);
                                        });
                                    } else {
                                        // 为发送的客户消息初始化已读状态
                                        if (response.data.data.role_type === 'customer') {
                                            response.data.data.is_read = 0;  // 初始状态为未读
                                            response.data.data.read_time = 0;
                                        }
                                        this.messages.push(response.data.data);
                                    }
                                    this.messageInput = '';
                                    this.uploadFile = null;
                                    this.uploadPreview = '';
                                    
                                    this.$nextTick(() => {
                                        this.scrollToBottom();
                                        
                                        // 标记所有客服/商家消息为已读
                                        this.markAllOtherMessagesAsRead();
                                    });
                                } else {
                                    this.$message.error(response.data.msg || '发送消息失败');
                                }
                            })
                            .catch(error => {

                                let errorMsg = '发送消息失败，请稍后重试';
                                try {
                                    if (error && error.response && error.response.data && error.response.data.msg) {
                                        errorMsg = error.response.data.msg;
                                    }
                                } catch (e) {
                                    // 如果嵌套访问时出错，使用默认错误消息
                                }
                                this.$message.error(errorMsg);
                            })
                            .finally(() => {
                                // 启用发送按钮
                                if (sendBtn) sendBtn.disabled = false;
                                
                                // 发送消息后立即刷新一次消息列表，确保状态同步
                                this.pollNewMessages();
                            });
                    },
                    scrollToBottom() {
                        if (this.$refs.chatBody) {
                            const chatBody = this.$refs.chatBody;
                            const scrollHeight = chatBody.scrollHeight;
                            
                            // 使用动画效果滚动到底部
                            const startPosition = chatBody.scrollTop;
                            const change = scrollHeight - startPosition;
                            const duration = 300; // 滚动动画持续时间（毫秒）
                            let startTime = null;
                            
                            const animateScroll = (timestamp) => {
                                if (!startTime) startTime = timestamp;
                                const elapsed = timestamp - startTime;
                                
                                // 使用easeOutQuad缓动函数
                                const progress = Math.min(elapsed / duration, 1);
                                const easeProgress = 1 - (1 - progress) * (1 - progress);
                                
                                chatBody.scrollTop = startPosition + change * easeProgress;
                                
                                if (elapsed < duration) {
                                    window.requestAnimationFrame(animateScroll);
                                }
                            };
                            
                            window.requestAnimationFrame(animateScroll);
                        }
                    },
                    simulateStaffResponse() {
                        // 此功能已禁用
                        return;
                    },
                    getAutoReply() {
                        // 此功能已禁用
                        return '您好，请问有什么可以帮助您的吗？';
                    },
                    formatTime(timestamp) {
                        if (!timestamp) return '';
                        const date = new Date(timestamp * 1000);
                        const now = new Date();
                        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                        const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
                        const messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

                        const hours = date.getHours().toString().padStart(2, '0');
                        const minutes = date.getMinutes().toString().padStart(2, '0');
                        const timeStr = `${hours}:${minutes}`;

                        // 如果是今天，只显示时间
                        if (messageDate.getTime() === today.getTime()) {
                            return timeStr;
                        }
                        // 如果是昨天，显示"昨天 时间"
                        else if (messageDate.getTime() === yesterday.getTime()) {
                            return `昨天 ${timeStr}`;
                        }
                        // 如果是今年，显示"月-日 时间"
                        else if (date.getFullYear() === now.getFullYear()) {
                            const month = (date.getMonth() + 1).toString().padStart(2, '0');
                            const day = date.getDate().toString().padStart(2, '0');
                            return `${month}-${day} ${timeStr}`;
                        }
                        // 如果是其他年份，显示完整日期时间
                        else {
                            const year = date.getFullYear();
                            const month = (date.getMonth() + 1).toString().padStart(2, '0');
                            const day = date.getDate().toString().padStart(2, '0');
                            return `${year}-${month}-${day} ${timeStr}`;
                        }
                    },
                    formatDate(date) {
                        const year = date.getFullYear();
                        const month = (date.getMonth() + 1).toString().padStart(2, '0');
                        const day = date.getDate().toString().padStart(2, '0');
                        return `${year}-${month}-${day}`;
                    },
                    formatMessage(message) {
                        if (!message) return ''; // 如果消息为null或undefined，返回空字符串
                        
                        // 检查是否为HTML图片标签格式，使用更简单的检测方式
                        if (message.includes('<div') && message.includes('<img') && message.includes('message-image')) {
                            // 这是一个已格式化的图片消息，直接返回
                            return message;
                        }
                        
                        // 检查是否包含预设问题HTML结构，直接返回原始HTML
                        if (message.includes('<div class="preset-questions"')) {
                            return message;
                        }
                        
                        // 检查是否为图片URL格式
                        const urlPattern = /^https?:\/\/[^\s]+\.(jpg|jpeg|png|gif|bmp|webp)(\?[^\s]*)?$/i;
                        if (urlPattern.test(message)) {
                            // 构建图片HTML
                            return `<div class="message-image-container"><img src="${message}" class="message-image" alt="图片消息" style="display: block;"></div>`;
                        }
                        
                        // 处理普通文本：转义HTML特殊字符并保留换行
                        return message
                            .replace(/&/g, '&amp;')
                            .replace(/</g, '&lt;')
                            .replace(/>/g, '&gt;')
                            .replace(/"/g, '&quot;')
                            .replace(/'/g, '&#039;')
                            .replace(/\n/g, '<br>');
                    },
                    insertEmoji(emoji) {
                        this.messageInput += emoji;
                        this.emojiVisible = false;
                    },
                    triggerFileUpload() {
                        // 通过点击隐藏的文件输入框来触发文件选择
                        this.$refs.fileInput.click();
                    },
                    handleManualFileChange(event) {
                        const file = event.target.files[0];
                        if (!file) return;
                        
                        // Check if there's already an uploaded file from clipboard paste
                        if (this.uploadFile && (this.uploadFile.fromClipboard || this.uploadFile.name === 'clipboard_image.png')) {
                            // File was already uploaded via paste, don't auto-send
                            // Just clear the file input to ensure next selection works
                            event.target.value = '';
                            return;
                        }
                        
                        this.uploadFile = file;
                        
                        if (file.type.includes('image')) {
                            const reader = new FileReader();
                            reader.onload = e => {
                                this.uploadPreview = e.target.result;
                            };
                            reader.readAsDataURL(file);
                        }
                        
                        // 清除文件输入，确保下次选择同一文件时也能触发change事件
                        event.target.value = '';
                        
                        // 自动发送图片 - 这里不再提前上传，而是在sendMessage中处理
                        this.sendMessage();
                    },
                    handleFileChange(file) {
                        this.uploadFile = file.raw;
                        
                        if (file.raw.type.includes('image')) {
                            const reader = new FileReader();
                            reader.onload = e => {
                                this.uploadPreview = e.target.result;
                            };
                            reader.readAsDataURL(file.raw);
                        }
                    },
                    beforeUpload(file) {
                        // 此方法会在文件选择后立即调用
                        // 返回false可以阻止自动上传
                        return false;
                    },
                    cancelUpload() {
                        this.uploadFile = null;
                        this.uploadPreview = '';
                    },
                    previewImage(url) {
                        // 使用自定义的图片预览方法
                        previewImageElement(url);
                    },
                    downloadFile(url) {
                        window.open(url, '_blank');
                    },
                    handleImageError(event) {
                        // 当图片加载失败时，显示文本提示而不是尝试加载另一个图片

                        
                        // 创建一个文本提示元素替代图片
                        const parent = event.target.parentNode;
                        const textDiv = document.createElement('div');
                        textDiv.className = 'image-error-text';
                        textDiv.innerHTML = '图片已被撤回或不存在';
                        textDiv.style.padding = '10px';
                        textDiv.style.background = '#f8f8f8';
                        textDiv.style.border = '1px dashed #ddd';
                        textDiv.style.borderRadius = '4px';
                        textDiv.style.color = '#999';
                        textDiv.style.textAlign = 'center';
                        
                        // 替换图片元素
                        parent.replaceChild(textDiv, event.target);
                        
                        // 移除图片覆盖层
                        const overlay = parent.querySelector('.image-overlay');
                        if (overlay) {
                            overlay.style.display = 'none';
                        }
                    },
                    checkHistory() {
                        this.historyMode = true;
                        this.sessionStarted = false;
                        
                        // 如果URL中包含邮箱参数，自动填入并查询
                        const urlParams = new URLSearchParams(window.location.search);
                        const email = urlParams.get('email');
                        if (email) {
                            this.historyQuery.email = email;
                            // 延迟一下确保表单已经加载
                            setTimeout(() => {
                                this.queryHistory();
                            }, 300);
                        } else if (localStorage.getItem('lastQueryEmail')) {
                            // 如果localStorage中有上次查询的邮箱，自动填入
                            this.historyQuery.email = localStorage.getItem('lastQueryEmail');
                            // 延迟一下确保表单已经加载
                            setTimeout(() => {
                                this.queryHistory();
                            }, 300);
                        }
                    },
                    backToChat() {
                        this.historyMode = false;
                        this.viewingHistory = false;
                        this.historySessions = [];
                        
                        // 移动端关闭历史对话底部抽屉
                        if (window.innerWidth <= 768) {
                            this.hideHistoryPanel();
                        }
                        
                        if (this.sessionStarted && this.sessionId) {
                            this.startPolling();
                        }
                    },
                    queryHistory() {
                        if (!this.historyQuery.email && !this.historyQuery.phone) {
                            this.$message.warning('请至少填写邮箱或电话其中一项');
                            return;
                        }
                        
                        // 显示加载状态
                        const loading = this.$loading({
                            lock: true,
                            text: '查询历史会话中...',
                            spinner: 'el-icon-loading',
                            background: 'rgba(0, 0, 0, 0.7)'
                        });
                        
                        axios.post('/plugin/Customersystem/api/queryHistory', {
                            email: this.historyQuery.email,
                            phone: this.historyQuery.phone,
                            silent: 0  // 非静默查询
                        })
                        .then(response => {
                            if (response.data.code === 200) {
                                // 更新联系人和会话数据
                                const data = response.data.data;
                                
                                // 保存联系人列表
                                const contactsList = data.contacts || [];

                                
                                // 获取会话列表并进行处理，确保会话ID唯一
                                const rawSessions = data.sessions || [];
                                const uniqueSessions = [];
                                const seenSessionIds = new Set();

                                rawSessions.forEach(session => {
                                    if (!seenSessionIds.has(session.id)) {
                                        uniqueSessions.push(session);
                                        seenSessionIds.add(session.id);
                                    }
                                });

                                this.historySessions = uniqueSessions;

                                // 清空搜索文本，默认不分组显示所有会话
                                this.historyQuery.searchText = '';
                                this.isGroupByEmail = false; // 禁用分组，确保所有会话都能显示

                                if (this.historySessions.length > 0) {
                                    // 遍历每个会话，检查联系人信息和状态
                                    this.historySessions.forEach(session => {
                                        // 确保每个会话有联系人信息
                                        if (!session.contact) {
                                            // 如果会话没有关联联系人，从联系人列表中查找
                                            const contact = contactsList.find(c => c.id === session.contact_id);
                                            if (contact) {
                                                session.contact = contact;
                                            }
                                        }
                                        
                                        // 更新会话状态
                                        session.isClosed = session.status === 'closed';
                                    });
                                }
                                
                                if (this.historySessions.length === 0) {
                                    this.$message({
                                        message: '未找到历史会话',
                                        type: 'info',
                                        customClass: 'custom-message'
                                    });
                                }
                            } else {
                                this.$message.error(response.data.msg || '查询失败');
                            }
                        })
                        .catch(error => {

                            this.$message.error('网络错误，请稍后重试');
                        })
                        .finally(() => {
                            loading.close();
                            
                            // 如果查询成功且有结果，标记为历史模式
                            if (this.historySessions.length > 0) {
                                this.historyMode = true;
                            }
                        });
                    },
                    
                    // 邮箱输入后自动查询
                    autoQueryHistory() {
                        // 如果有邮箱或电话，自动查询
                        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                        if ((this.historyQuery.email && emailRegex.test(this.historyQuery.email)) || 
                            (this.historyQuery.phone && this.historyQuery.phone.length >= 5)) {
                            this.queryHistory();
                        }
                    },
                    viewSessionDetail(session) {
                        this.sessionId = session.id;
                        this.sessionStarted = true;
                        this.messages = session.messages || [];
                        this.viewingHistory = false; // 设为false以确保显示聊天界面而非历史记录
                        this.historyMode = false; // 退出历史模式
                        this.contactId = session.contact_id; // 确保使用当前会话的联系人ID
                        
                        // 查找并保存当前会话的联系人信息
                        if (session.contact) {
                            // 如果会话中已包含联系人信息，直接使用
                            this.currentContact = session.contact;
                        } else {
                            // 如果会话不包含联系人信息，则从联系人列表中查找
                            const contactFound = this.historySessions.find(s => s.id === session.id && s.contact)?.contact;
                            if (contactFound) {
                                this.currentContact = contactFound;
                            }
                        }
                        
                        // 默认先启用输入框
                        this.resetInputStatus();
                        
                        // 设置历史会话状态
                        if (session.status === 'closed' || session.isClosed) {
                            this.updateSessionStatus(session.id, 'closed');
                            
                            // 在nextTick中执行界面更新，确保DOM已更新
                            this.$nextTick(() => {
                                // 禁用输入框和发送按钮
                                this.disableInputAndUpdate();
                            });
                        } else {
                            this.updateSessionStatus(session.id, 'open');
                            // 确保输入框和发送按钮可用
                            this.$nextTick(() => {
                                this.enableInputAndUpdate();
                            });
                        }
                        
                        this.$nextTick(() => {
                            this.scrollToBottom();
                            
                            // 如果是历史会话，标记所有消息为已读
                            if (session.messages && session.has_unread) {
                                session.has_unread = false;
                                session.unread_staff_count = 0;
                            }
                            
                            // 启动轮询获取新消息
                            this.startPolling();
                        });
                        
                        // 强制切换到聊天模式
                        this.showHistory = false;
                        this.showChatView = true;
                    },
                    
                    // 重置输入框状态
                    resetInputStatus() {
                        // 移除禁用状态
                        this.inputDisabled = false;
                        this.sendDisabled = false;
                        
                        // 重置UI元素
                        this.$nextTick(() => {
                            const messageInput = document.getElementById('messageInput');
                            const sendButton = document.querySelector('.send-button');
                            
                            if (messageInput) {
                                messageInput.disabled = false;
                                messageInput.placeholder = "请输入消息...";
                                messageInput.style.backgroundColor = '#fff';
                                messageInput.style.cursor = 'text';
                            }
                            
                            if (sendButton) {
                                sendButton.disabled = false;
                                sendButton.style.opacity = '1';
                                sendButton.style.cursor = 'pointer';
                            }
                        });
                    },
                    
                    // 启用输入框和发送按钮
                    enableInputAndUpdate() {
                        this.inputDisabled = false;
                        this.sendDisabled = false;
                        
                        this.$nextTick(() => {
                            const messageInput = document.getElementById('messageInput');
                            const sendButton = document.querySelector('.send-button');
                            
                            if (messageInput) {
                                messageInput.disabled = false;
                                messageInput.placeholder = "请输入消息...";
                                messageInput.style.backgroundColor = '#fff';
                                messageInput.style.cursor = 'text';
                            }
                            
                            if (sendButton) {
                                sendButton.disabled = false;
                                sendButton.style.opacity = '1';
                                sendButton.style.cursor = 'pointer';
                            }
                        });
                    },
                    
                    // 为历史页面专门开启轮询
                    startPollingHistory() {
                        // 开始轮询之前先停止任何现有的轮询
                        if (this.pollingTimer) {
                            clearInterval(this.pollingTimer);
                            this.pollingTimer = null;
                        }
                        
                        
                        
                        // 设置定时轮询
                        this.pollingTimer = setInterval(() => {
                            this.pollHistoryMessages();
                        }, 1000); // 每秒轮询一次以确保实时性
                    },
                    
                    // 轮询历史会话的新消息
                    pollHistoryMessages() {
                        if (!this.sessionId) return;
                        
                        
                        
                        axios.post('/plugin/Customersystem/api/getRealtimeMessages', {
                            session_id: this.sessionId,
                            last_message_id: this.getLastMessageId(),
                            check_session_status: true,  // 获取会话状态
                            check_recalled: true  // 检查撤回状态
                        })
                        .then(response => {
                            if (response.data.code === 200) {
                                // 从返回数据中获取消息和会话状态
                                const responseData = response.data.data;
                                const messages = responseData.messages || [];
                                const sessionStatus = responseData.session_status;
                                const isClosed = responseData.is_closed === true;
                                
                                // 如果返回了会话状态，更新本地缓存的状态
                                if (sessionStatus) {
                                    this.updateSessionStatus(this.sessionId, sessionStatus);
                                }
                                
                                // 如果会话已经关闭，更新UI状态
                                if (isClosed) {
                                    this.disableInputAndUpdate();
                                }
                                
                                if (messages && messages.length > 0) {

                                    
                                    let hasNewMessages = false;
                                    messages.forEach(message => {
                                        // 确保消息ID是整数
                                        if (message.id) {
                                            message.id = parseInt(message.id);
                                        }
                                        // 确保时间戳是整数
                                        if (message.create_time) {
                                            message.create_time = parseInt(message.create_time);
                                        }
                                        
                                        const exists = this.messages.some(msg => msg.id === message.id);
                                        if (!exists) {
                                            this.messages.push(message);

                                            hasNewMessages = true;
                                        } else {
                                            // 如果消息已存在，检查是否需要更新（比如被撤回）
                                            const index = this.messages.findIndex(msg => msg.id === message.id);
                                            if (index !== -1) {
                                                // 检查消息是否被撤回
                                                if (message.is_recalled === 1 && this.messages[index].is_recalled !== 1) {
                                                    // 更新被撤回的消息
                                                    // 更新消息状态
                                                    this.messages[index] = message;
                                                }
                                            }
                                        }
                                    });
                                    
                                    if (hasNewMessages) {
                                        this.$nextTick(() => {
                                            this.scrollToBottom();
                                            
                                            // 播放通知提示音
                                            this.playNotificationSound();
                                        });
                                    }
                                }
                                
                                // 处理已撤回的消息
                                const recalledMessages = responseData.recalled_messages || [];
                                if (recalledMessages && recalledMessages.length > 0) {
                                    recalledMessages.forEach(recalledMsg => {
                                        // 更新本地消息列表中对应的消息
                                        const index = this.messages.findIndex(m => m.id === recalledMsg.id);
                                        if (index !== -1) {
                                            // 替换为撤回后的消息
                                            this.messages[index] = recalledMsg;
                                        }
                                    });
                                }
                            }
                        })
                        .catch(error => {

                        });
                    },
                    
                    backToStartConsult() {
                        // 关闭当前会话并返回到开始咨询页面
                        this.sessionStarted = false;
                        this.historyMode = false;
                        this.viewingHistory = false;
                        this.sessionId = null;
                        this.contactId = null;
                        this.currentContact = null;
                        this.messages = [];
                        this.messageInput = '';
                        this.uploadFile = null;
                        this.uploadPreview = '';
                        
                        // 停止轮询
                        this.stopPolling();
                        
                        // 如果需要，可以重置用户表单
                        this.$nextTick(() => {
                            if (this.$refs.userForm) {
                                this.$refs.userForm.resetFields();
                            }
                        });
                    },
                    useFaqQuestion(item) {
                        this.userInfo.message = item.question;
                    },
                    handleVisibilityChange() {
                        if (document.visibilityState === 'visible') {
                            this.isPageActive = true;
                            // 页面变为可见时重置标题
                            this.stopTitleBlinking();
                            this.hasUnreadMessage = false;
                            this.unreadCount = 0;
                            
                            // 页面变为可见时，标记所有消息为已读
                            if (this.sessionStarted && this.sessionId) {
                                // 添加短暂延迟，确保用户真正看到了页面内容
                                setTimeout(() => {
                                    this.markAllOtherMessagesAsRead(); // 调用标记已读方法，传递页面可见标记
                                }, 1000);
                            }
                            
                            // 页面变为可见时立即轮询一次消息
                            if (this.sessionStarted && this.sessionId && !this.viewingHistory) {
                                this.pollNewMessages();
                            }
                            // 重新以更短的间隔启动轮询
                            this.stopPolling();
                            this.startPolling();
                        } else {
                            this.isPageActive = false;
                            // 页面不可见时以较长的间隔轮询
                            this.stopPolling();
                            this.startPolling();
                        }
                    },
                    handleFocus() {
                        this.isPageActive = true;
                        // 重置标题
                        this.stopTitleBlinking();
                        this.hasUnreadMessage = false;
                        this.unreadCount = 0;
                        
                        // 页面获得焦点时，标记所有消息为已读
                        if (this.sessionStarted && this.sessionId) {
                            this.markAllOtherMessagesAsRead();
                        }
                        
                        // 重启轮询
                        this.stopPolling();
                        this.startPolling();
                        // 立即轮询一次
                        if (this.sessionStarted && this.sessionId && !this.viewingHistory) {
                            this.pollNewMessages();
                        }
                    },
                    handleBlur() {
                        this.isPageActive = false;
                        this.stopPolling();
                        this.startPolling();
                    },
                    toggleHistorySidebar() {
                        // 检查是否为移动端
                        if (window.innerWidth <= 768) {
                            // 移动端特殊处理，显示/隐藏历史对话底部抽屉
                            const historyBackdrop = document.getElementById('historyBackdrop');
                            const historyForm = document.querySelector('.history-sidebar-container .user-form');
                            
                            if (historyForm) {
                                if (historyForm.classList.contains('active')) {
                                    // 已显示，需要隐藏
                                    this.hideHistoryPanel(historyForm, historyBackdrop);
                                } else {
                                    // 未显示，需要显示
                                    historyForm.style.display = 'block';
                                    // 使用setTimeout确保先显示再添加active类
                                    setTimeout(() => {
                                        historyForm.classList.add('active');
                                        if (historyBackdrop) historyBackdrop.classList.add('active');
                                    }, 10);
                                }
                            }
                        } else {
                            // 桌面端正常行为
                            this.historyCollapsed = !this.historyCollapsed;
                            // 保存状态到本地存储，以便页面刷新后保持状态
                            localStorage.setItem('historyCollapsed', this.historyCollapsed);
                        }
                        
                        // 强制重绘确保侧边栏完全隐藏
                        this.$nextTick(() => {
                            window.dispatchEvent(new Event('resize'));
                        });
                    },
                    
                    // 隐藏历史面板的公共方法
                    hideHistoryPanel(historyForm, historyBackdrop) {
                        if (!historyForm) {
                            historyForm = document.querySelector('.history-sidebar-container .user-form');
                        }
                        if (!historyBackdrop) {
                            historyBackdrop = document.getElementById('historyBackdrop');
                        }
                        
                        if (historyForm) {
                            historyForm.classList.remove('active');
                            if (historyBackdrop) historyBackdrop.classList.remove('active');
                            
                            // 延迟后才真正隐藏，保证动画效果
                            setTimeout(() => {
                                historyForm.style.display = 'none';
                            }, 350);
                        }
                    },
                    playNotificationSound() {
                        try {
                            // 首先尝试使用生成的提示音
                            if (window.playCustomerSystemNotification && window.playCustomerSystemNotification()) {
                                return; // 如果成功播放，直接返回
                            }

                            // 使用Web Audio API创建提示音，避免404错误
                            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                            const oscillator = audioContext.createOscillator();
                            const gainNode = audioContext.createGain();

                            // 连接音频节点
                            oscillator.connect(gainNode);
                            gainNode.connect(audioContext.destination);

                            // 设置音频参数
                            oscillator.frequency.setValueAtTime(800, audioContext.currentTime); // 800Hz频率
                            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime); // 音量30%

                            // 播放短促的提示音
                            oscillator.start(audioContext.currentTime);
                            oscillator.stop(audioContext.currentTime + 0.2); // 播放0.2秒

                        } catch (e) {
                            // 如果Web Audio API不支持，静默处理
                            console.log('音频提示不可用');
                        }
                    },
                    // 开始标题闪烁
                    startTitleBlinking() {
                        if (this.titleBlinkTimer) {
                            return; // 如果已经在闪烁，则不再启动
                        }
                        
                        let isOriginal = false;
                        this.titleBlinkTimer = setInterval(() => {
                            if (isOriginal) {
                                document.title = this.originalTitle;
                            } else {
                                document.title = `(${this.unreadCount}) 新消息 - ${this.originalTitle}`;
                            }
                            isOriginal = !isOriginal;
                        }, 1000); // 每1秒切换一次
                    },
                    
                    // 停止标题闪烁
                    stopTitleBlinking() {
                        if (this.titleBlinkTimer) {
                            clearInterval(this.titleBlinkTimer);
                            this.titleBlinkTimer = null;
                            document.title = this.originalTitle;
                        }
                    },
                    // 更新会话状态
                    updateSessionStatus(sessionId, status) {
                        // 更新当前会话的状态
                        const sessionIndex = this.historySessions.findIndex(s => s.id === sessionId);
                        if (sessionIndex !== -1) {
                            const oldStatus = this.historySessions[sessionIndex].status;
                            this.historySessions[sessionIndex].status = status;
                            this.historySessions[sessionIndex].isClosed = status === 'closed';
                            
                            // 如果状态从open变为closed，显示提示并禁用输入
                            if (oldStatus === 'open' && status === 'closed') {
                                // 显示提示
                                this.$message({
                                    message: '会话已被关闭',
                                    type: 'warning',
                                    customClass: 'custom-message'
                                });
                                
                                // 禁用输入
                                this.disableInputAndUpdate();
                            }
                        }
                    },
                    // 获取当前会话的关闭状态
                    getSessionClosed() {
                        if (!this.sessionId) return false;
                        
                        // 先查找匹配的会话
                        const currentSession = this.historySessions.find(s => s.id === this.sessionId);
                        if (currentSession) {
                            return currentSession.status === 'closed' || currentSession.isClosed === true;
                        }
                        
                        return false;
                    },
                    disableInputAndUpdate() {
                        // 禁用输入框和发送按钮
                        this.$nextTick(() => {
                            const textarea = document.querySelector('.el-textarea__inner');
                            if (textarea) {
                                textarea.setAttribute('placeholder', '会话已关闭，无法发送消息');
                                textarea.style.backgroundColor = '#f5f5f5';
                                textarea.style.cursor = 'not-allowed';
                                textarea.setAttribute('readonly', 'readonly'); // 添加只读属性
                            }
                            
                            const sendBtn = document.querySelector('.chat-footer .el-button--primary');
                            if (sendBtn) {
                                sendBtn.disabled = true;
                                sendBtn.style.opacity = '0.5';
                                sendBtn.style.cursor = 'not-allowed';
                            }
                            
                            // 在消息区域顶部添加会话已关闭提示
                            const chatHeader = document.querySelector('.chat-header');
                            if (chatHeader) {
                                // 检查是否已存在关闭提示，避免重复添加
                                const existingAlert = chatHeader.querySelector('.session-closed-alert');
                                if (!existingAlert) {
                                    const sessionAlert = document.createElement('div');
                                    sessionAlert.className = 'session-closed-alert';
                                    sessionAlert.style.color = '#ffffff';
                                    sessionAlert.style.backgroundColor = '#ef4444';
                                    sessionAlert.style.borderRadius = '4px';
                                    sessionAlert.style.padding = '2px 8px';
                                    sessionAlert.style.marginRight = '8px';
                                    sessionAlert.style.fontSize = '12px';
                                    sessionAlert.style.display = 'inline-block';
                                    sessionAlert.innerHTML = '会话已关闭';
                                    
                                    // 插入到第一个子元素前
                                    if (chatHeader.firstChild) {
                                        chatHeader.insertBefore(sessionAlert, chatHeader.firstChild.nextSibling);
                                    } else {
                                        chatHeader.appendChild(sessionAlert);
                                    }
                                }
                            }
                        });
                    },
                    // 加载商家列表
                    loadMerchantList() {
                        axios.get('/plugin/Customersystem/api/getMerchantList')
                            .then(response => {
                                if (response.data.code === 200) {
                                    this.merchantList = response.data.data.merchants || [];
                                } else {
                                    this.$message.error(response.data.msg || '获取商家列表失败');
                                }
                            })
                            .catch(error => {

                            });
                    },
                    // 切换聊天类型
                    handleChatTypeChange(value) {
                        this.chatType = value;
                        // 重置商家相关信息
                        if (value === 'merchant') {
                            this.merchantToken = '';
                            this.selectedMerchant = '';
                            this.merchantInfo = {};
                            this.merchantSearchError = '';
                        }
                    },
                    searchMerchantByToken() {
                        if (!this.merchantToken || this.merchantToken.trim() === '') {
                            this.merchantSearchError = '请输入XH1ASH6I:如何获取店铺路径shop后面就是例如https://xxx/shop/XH1ASH6I';
                            return;
                        }
                        
                        this.merchantSearchError = '';
                        this.selectedMerchant = '';
                        this.merchantInfo = {};
                        
                        // 去除网址前缀，只保留token部分
                        let token = this.merchantToken.trim();
                        if (token.includes('/shop/')) {
                            token = token.split('/shop/')[1];
                        }
                        
                        // 发送请求获取商家信息
                        axios.get(`/plugin/Customersystem/api/getMerchantByToken?token=${token}`)
                            .then(response => {
                                if (response.data.code === 200) {
                                    this.merchantInfo = response.data.data.merchant;
                                    this.selectedMerchant = this.merchantInfo.id;
                                    this.merchantSearchError = '';
                                } else {
                                    this.merchantSearchError = response.data.msg || '未找到对应的商家';
                                    this.selectedMerchant = '';
                                    this.merchantInfo = {};
                                }
                            })
                            .catch(error => {

                                this.merchantSearchError = '网络错误，请稍后重试';
                                this.selectedMerchant = '';
                                this.merchantInfo = {};
                            });
                    },
                    handlePresetQuestionClick(event, msg) {
                        // 检查是否是预设问题消息
                        if (msg.sender_type === 'staff' && msg.message.includes('preset-questions')) {
                            // 检查点击的是否是问题项
                            const clickedElement = event.target;
                            const presetItemElement = clickedElement.classList.contains('preset-question-item') ? 
                                clickedElement : clickedElement.closest('.preset-question-item');
                            
                            if (!presetItemElement) return;
                            
                            // 获取问题文本
                            const questionText = presetItemElement.textContent.trim();
                            
                            // 获取回答内容和类型（使用新的数据属性名）
                            const answer = presetItemElement.getAttribute('data-preset-answer') || 
                                          presetItemElement.getAttribute('data-answer'); // 兼容旧版
                            
                            const answerType = presetItemElement.getAttribute('data-preset-type') || 
                                              presetItemElement.getAttribute('data-answer-type'); // 兼容旧版
                            
                            // 如果会话处于活跃状态，处理回答
                            if (this.sessionStarted && !this.getSessionClosed()) {
                                // 先发送用户提问
                                this.messageInput = questionText;
                                this.$nextTick(() => {
                                    this.sendMessage(true); // 传入true表示是从预设问题点击触发的
                                    
                                    // 如果有回答内容，延迟发送回答
                                    if (answer && answer.trim()) {
                                        setTimeout(() => {
                                            switch (answerType) {
                                                case 'text':
                                                    // 发送文本回复
                                                    this.sendStaffMessage({
                                                        message: answer,
                                                        message_type: 'text'
                                                    });
                                                    break;
                                                    
                                                case 'image':
                                                    // 发送图片回复，确保图片正确显示
                                                    this.sendStaffMessage({
                                                        message: '<div class="message-image-container"><img src="' + answer + '" class="message-image" alt="图片消息" style="display: block;"><!----></div>',
                                                        message_type: 'image',
                                                        file_url: answer
                                                    });
                                                    break;
                                                    
                                                case 'file':
                                                    // 发送文件回复
                                                    this.sendStaffMessage({
                                                        message: answer,
                                                        message_type: 'file'
                                                    });
                                                    break;
                                                    
                                                default:
                                                    // 默认作为文本处理
                                                    this.sendStaffMessage({
                                                        message: answer,
                                                        message_type: 'text'
                                                    });
                                            }
                                        }, 500);
                                    }
                                });
                            }
                        }
                    },
                    // 发送商家/客服回复消息
                    sendStaffMessage(messageData) {
                        if (!this.currentSession) return;
                        
                        const { message, message_type = 'text', file_name = '' } = messageData;
                        
                        // 构建消息对象
                        const msg = {
                            session_id: this.currentSession.id,
                            sender_type: 'staff',
                            sender_id: this.currentSession.merchant_id || 0,
                            role_type: 'merchant',
                            message: message,
                            message_type: message_type,
                            is_read: 1,
                            create_time: Math.floor(Date.now() / 1000),
                            update_time: Math.floor(Date.now() / 1000),
                        };
                        
                        // 特定类型的额外字段
                        if (message_type === 'image') {
                            msg.file_url = message;
                            msg.message = ''; // 清空文本内容，否则客户端可能展示URL而不是图片
                        } else if (message_type === 'file') {
                            msg.file_url = message;
                            msg.message = ''; // 清空文本内容，避免显示URL
                            msg.file_name = file_name || message.split('/').pop() || '文件';
                        }
                        
                        // 添加到消息列表
                        this.messages.push(msg);
                        
                        // 滚动到底部
                        this.$nextTick(() => {
                            this.scrollToBottom();
                        });
                        
                        // 更新会话的最后消息
                        if (this.currentSession) {
                            this.currentSession.last_message = message_type === 'text' ? message : `[${message_type}]`;
                            this.currentSession.last_time = msg.create_time;
                            this.currentSession.update_time = msg.create_time;
                        }
                    },
                    // 处理粘贴事件
                    handlePaste(e) {
                        // 判断当前会话状态
                        if (this.getSessionClosed()) {
                            return; // 如果会话已关闭，则不处理粘贴事件
                        }
                        
                        const items = e.clipboardData && e.clipboardData.items;
                        let file = null;
                        
                        if (items && items.length) {
                            // 遍历剪贴板中的内容，查找图片
                            for (let i = 0; i < items.length; i++) {
                                if (items[i].type.indexOf('image') !== -1) {
                                    file = items[i].getAsFile();
                                    // 鍙戠幇鍓创鏉垮浘鐗?
                                    break;
                                }
                            }
                        }
                        
                        // 如果找到图片文件，则上传
                        if (file) {
                            // 阻止默认粘贴行为
                            e.preventDefault();
                            
                            // 检查文件大小
                            const maxSize = 5 * 1024 * 1024; // 5MB
                            if (file.size > maxSize) {
                                this.$message.error('图片大小不能超过5MB');
                                return;
                            }
                            
                            // 创建FormData对象
                            const formData = new FormData();
                            formData.append('file', file, 'clipboard_image.png');
                            
                            // 显示上传进度
                            const loading = this.$loading({
                                lock: true,
                                text: '正在上传剪贴板图片...',
                                spinner: 'el-icon-loading',
                                background: 'rgba(0, 0, 0, 0.7)'
                            });
                            
                            // 使用官方上传接口
                            axios.post('/shopApi/Upload/file', formData, {
                                headers: {
                                    'Content-Type': 'multipart/form-data'
                                }
                            })
                                .then(response => {
                                    // 涓婁紶鎴愬姛;
                                    if (response.data.code === 1 || response.data.code === 200) {
                                        // 如果上传成功，设置上传文件信息
                                        this.uploadFile = {
                                            name: 'clipboard_image.png',
                                            type: 'image/png',
                                            size: file.size,
                                            fromClipboard: true  // 添加标记，表示来自剪贴板
                                        };
                                        
                                        // 设置预览图片，使用返回的URL
                                        this.uploadPreview = response.data.data.url || response.data.data;
                                        
                                        // 不自动发送，让用户点击发送按钮
                                        this.$message.success('剪贴板图片已上传，点击发送按钮发送');
                                    } else {
                                        this.$message.error(response.data.msg || '上传失败');
                                    }
                                })
                                .catch(error => {

                                    if (error.response) {
                                        // 閿欒鍝嶅簲;
                                        this.$message.error('上传失败: ' + (error.response.data.msg || error.message));
                                    } else {
                                        this.$message.error('上传剪贴板图片失败: ' + error.message);
                                    }
                                })
                                .finally(() => {
                                    loading.close();
                                });
                        }
                    },
                    
                    // 处理消息操作
                    handleMessageAction(command, message) {
                        if (command === 'recall') {
                            this.recallMessage(message);
                        }
                    },
                    
                    // 撤回消息
                    recallMessage(message) {
                        // 检查消息发送时间是否超过2分钟
                        const now = Math.floor(Date.now() / 1000);
                        const messageTime = message.create_time;
                        const timeLimit = 120; // 2分钟
                        
                        if ((now - messageTime) > timeLimit) {
                            this.$message({
                                message: '消息发送已超过2分钟，无法撤回',
                                type: 'warning',
                                customClass: 'custom-message'
                            });
                            return;
                        }
                        
                        // 显示确认对话框
                        this.$confirm('确定要撤回此消息吗？', '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }).then(() => {
                            // 显示加载状态
                            const loading = this.$loading({
                                lock: true,
                                text: '正在撤回消息...',
                                spinner: 'el-icon-loading',
                                background: 'rgba(0, 0, 0, 0.7)'
                            });
                            
                            // 发送撤回请求
                            axios.post('/plugin/Customersystem/api/recallMessage', {
                                message_id: message.id,
                                session_id: this.sessionId
                            })
                            .then(response => {
                                if (response.data.code === 200) {
                                    // 更新本地消息
                                    const index = this.messages.findIndex(msg => msg.id === message.id);
                                    if (index !== -1) {
                                        // 更新消息状态为已撤回 - 使用直接赋值而不是$set
                                        this.messages[index].is_recalled = 1;
                                        this.messages[index].message = '[该消息已撤回]';
                                        
                                        // 如果是图片或文件类型，清空文件URL并修改消息类型
                                        if (this.messages[index].message_type === 'image' || 
                                            this.messages[index].message_type === 'file') {
                                            this.messages[index].file_url = '';
                                            this.messages[index].message_type = 'text';
                                        }
                                    }
                                    
                                    this.$message.success('消息已撤回');
                                } else {
                                    this.$message.error(response.data.msg || '撤回消息失败');
                                }
                            })
                            .catch(error => {

                                this.$message.error('撤回消息失败，请稍后重试');
                            })
                            .finally(() => {
                                loading.close();
                                
                                // 消息撤回后立即刷新一次消息列表，确保状态同步
                                this.pollNewMessages();
                            });
                        }).catch(() => {
                            // 用户取消撤回操作
                        });
                    },
                    filterHistorySessions() {
                        // 此方法仅触发计算属性的更新，不直接修改historySessions数组
                        // 实际过滤逻辑已移至计算属性filteredSessions中

                    },
                    searchByEmail(email) {
                        if (!email) return;
                        
                        // 如果已经在查询同一个邮箱，不需要重新查询
                        if (this.historyQuery.email === email && this.historySessions.length > 0) {
                            // 只更新搜索框，清空其他过滤条件
                            this.historyQuery.searchText = '';
                            return;
                        }
                        
                        // 设置邮箱并清空其他查询条件
                        this.historyQuery.email = email;
                        this.historyQuery.phone = '';
                        this.historyQuery.searchText = '';
                        
                        // 显示加载状态
                        const loading = this.$loading({
                            lock: true,
                            text: '正在查询邮箱相关会话...',
                            spinner: 'el-icon-loading',
                            background: 'rgba(0, 0, 0, 0.7)'
                        });
                        
                        // 调用查询API
                        axios.post('/plugin/Customersystem/api/queryHistory', {
                            email: email,
                            silent: 0
                        })
                        .then(response => {
                            if (response.data.code === 200) {
                                const data = response.data.data;
                                // 更新会话列表
                                this.historySessions = data.sessions || [];
                                
                                // 如果没有会话，显示提示
                                if (this.historySessions.length === 0) {
                                    this.$message({
                                        message: '未找到与该邮箱关联的历史会话',
                                        type: 'info'
                                    });
                                } else {
                                    this.$message({
                                        message: `找到 ${this.historySessions.length} 个相关会话`,
                                        type: 'success'
                                    });
                                }
                            } else {
                                this.$message.error(response.data.msg || '查询失败');
                            }
                        })
                        .catch(error => {

                            this.$message.error('网络错误，请稍后重试');
                        })
                        .finally(() => {
                            loading.close();
                        });
                    },
                    toggleGroupByEmail() {
                        this.isGroupByEmail = !this.isGroupByEmail;
                    },
                    viewAllSessionsByContactId(contactId) {
                        // 显示加载中
                        const loading = this.$loading({
                            lock: true,
                            text: '正在加载会话列表...',
                            spinner: 'el-icon-loading',
                            background: 'rgba(255, 255, 255, 0.7)'
                        });
                        
                        // 调用API获取该联系人的所有会话
                        axios.post('/plugin/Customersystem/api/getSessionsByContactId', {
                            contact_id: contactId
                        })
                        .then(response => {
                            loading.close();
                            
                            if (response.data.code === 200) {
                                const result = response.data.data;
                                const contact = result.contact;
                                const sessions = result.sessions;
                                
                                // 显示对话框
                                this.$confirm(
                                    this.renderContactSessionsHtml(contact, sessions),
                                    '联系人所有会话',
                                    {
                                        dangerouslyUseHTMLString: true,
                                        confirmButtonText: '关闭',
                                        cancelButtonText: '取消',
                                        showCancelButton: false,
                                        type: 'info',
                                        customClass: 'contact-sessions-dialog'
                                    }
                                ).catch(() => {});
                            } else {
                                this.$message.error(response.data.msg || '获取会话失败');
                            }
                        })
                        .catch(error => {
                            loading.close();

                            this.$message.error('网络错误，请稍后重试');
                        });
                    },
                    
                    // 渲染联系人的会话列表HTML
                    renderContactSessionsHtml(contact, sessions) {
                        if (!sessions || sessions.length === 0) {
                            return `<div>
                                <h3 style="margin-bottom: 10px;">联系人: ${contact.name} (ID: ${contact.id})</h3>
                                <p>没有找到相关会话</p>
                            </div>`;
                        }
                        
                        // 构建会话列表HTML
                        let sessionsHtml = sessions.map(session => {
                            const statusText = session.status === 'open' ? 
                                '<span style="color: #10b981; padding: 2px 6px; background: rgba(16,185,129,0.1); border-radius: 4px;">进行中</span>' : 
                                '<span style="color: #ef4444; padding: 2px 6px; background: rgba(239,68,68,0.1); border-radius: 4px;">已关闭</span>';
                            
                            const time = this.formatTime(session.last_time);
                            
                            return `<div class="session-item" style="border-bottom: 1px solid #f0f0f0; padding: 10px 0; margin-bottom: 8px;">
                                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                    <span style="font-weight: 500;">${session.title}</span>
                                    <span style="font-size: 12px; color: #64748b;">${time}</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div style="font-size: 12px; color: #64748b; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 250px;">
                                        ${session.last_message || '无消息'}
                                    </div>
                                    <div style="display: flex; align-items: center;">
                                        ${statusText}
                                        <button onclick="window.viewSession(${session.id})" 
                                            style="border: none; background: #4e6ef2; color: white; border-radius: 4px; padding: 3px 8px; margin-left: 8px; cursor: pointer;">
                                            切换
                                        </button>
                                    </div>
                                </div>
                            </div>`;
                        }).join('');
                        
                        // 添加全局函数以便按钮可以调用Vue实例方法
                        window.viewSession = (sessionId) => {
                            const session = sessions.find(s => s.id == sessionId);
                            if (session) {
                                // 将联系人信息添加到会话中
                                session.contact = contact;
                                // 查询会话消息并切换
                                this.getSessionMessages(session);
                                // 立即关闭对话框
                                document.querySelector('.el-message-box__close').click();
                            }
                        };
                        
                        return `<div style="max-height: 400px; overflow-y: auto;">
                            <h3 style="margin-bottom: 15px;">联系人: ${contact.name} (ID: ${contact.id})</h3>
                            <p style="margin-bottom: 10px;">邮箱: ${contact.email}</p>
                            <div class="sessions-list">
                                ${sessionsHtml}
                            </div>
                        </div>`;
                    },
                    
                    // 获取会话消息并切换到该会话
                    getSessionMessages(session) {
                        // 显示加载中
                        const loading = this.$loading({
                            lock: true,
                            text: '正在加载会话消息...',
                            spinner: 'el-icon-loading',
                            background: 'rgba(255, 255, 255, 0.7)'
                        });
                        
                        axios.post('/plugin/Customersystem/api/getDetailedHistory', {
                            session_id: session.id
                        })
                        .then(response => {
                            loading.close();
                            
                            if (response.data.code === 200) {
                                const result = response.data.data;
                                
                                // 设置会话消息
                                session.messages = result.messages;
                                
                                // 切换到该会话
                                this.viewSessionDetail(session);
                                
                                // 显示成功提示
                                this.$message({
                                    message: '已切换到会话',
                                    type: 'success',
                                    duration: 1500
                                });
                                
                                // 切换到聊天页面
                                this.historyMode = false;
                                this.viewingHistory = false;
                                this.sessionStarted = true;
                            } else {
                                this.$message.error(response.data.msg || '获取会话消息失败');
                            }
                        })
                        .catch(error => {
                            loading.close();

                            this.$message.error('网络错误，请稍后重试');
                        });
                    },
                    // 标记单条消息为已读
                    markMessageAsRead(messageId) {
                        if (!messageId || !this.sessionId) return;
                        
                        // 获取当前用户角色
                        let roleType = 'customer';
                        
                        // 调用统一API标记消息为已读
                        axios.post('/plugin/Customersystem/api/markMessageRead', {
                            message_id: messageId,
                            session_id: this.sessionId,
                            role_type: 'customer'
                        })
                        .then(response => {
                            if (response.data.code === 200) {
                                // 更新本地消息的已读状态
                                const index = this.messages.findIndex(msg => msg.id === messageId);
                                if (index !== -1) {
                                    this.messages[index].is_read = 1;
                                    this.messages[index].read_time = response.data.data.read_time || Math.floor(Date.now() / 1000);
                                    this.messages[index].customer_read = 1;
                                    this.messages[index].customer_read_time = response.data.data.customer_read_time || Math.floor(Date.now() / 1000);
                                }
                            }
                        })
                        .catch(error => {

                        });
                    },
                    
                    // 标记所有非客户发送的消息为已读
                    markAllOtherMessagesAsRead() {
                        if (!this.sessionId) return;
                        
                        // 获取当前用户角色
                        let roleType = 'customer';
                        
                        // 调用API批量标记消息已读
                        axios.post('/plugin/Customersystem/api/markSessionMessagesRead', {
                            session_id: this.sessionId,
                            role_type: roleType
                        })
                        .then(response => {
                            if (response.data.code === 200) {
                                // 更新本地所有非客户消息的已读状态
                                this.messages.forEach(msg => {
                                    if (msg.sender_type !== 'customer' && msg.role_type !== 'customer') {
                                        msg.is_read = 1;
                                        msg.read_time = Math.floor(Date.now() / 1000);
                                        msg.customer_read = 1;
                                        msg.customer_read_time = Math.floor(Date.now() / 1000);
                                    }
                                });
                            }
                        })
                        .catch(error => {

                        });
                    },
                    
                    // 开始轮询查询已读状态
                    startReadStatusPolling() {
                        // 清除之前的轮询
                        this.stopReadStatusPolling();
                        
                        // 设置新的轮询，每2秒查询一次已读状态
                        this.readStatusTimer = setInterval(() => {
                            this.pollReadStatus();
                        }, 2000);
                    },
                    
                    // 停止已读状态轮询
                    stopReadStatusPolling() {
                        if (this.readStatusTimer) {
                            clearInterval(this.readStatusTimer);
                            this.readStatusTimer = null;
                        }
                    },
                    
                    // 轮询消息已读状态
                    pollReadStatus() {
                        if (!this.sessionId) return;
                        
                        // 获取所有未读的客户消息ID
                        const unreadCustomerMessages = this.messages
                            .filter(msg => (msg.sender_type === 'customer' || msg.role_type === 'customer') && 
                                   (!msg.staff_read || msg.staff_read === 0 || !msg.merchant_read || msg.merchant_read === 0))
                            .map(msg => msg.id);
                            
                        if (unreadCustomerMessages.length === 0) return;
                        
                        // 查询这些消息的已读状态
                        axios.post('/plugin/Customersystem/api/getRealtimeMessages', {
                            session_id: this.sessionId,
                            message_ids: unreadCustomerMessages,
                            check_read_status: true
                        })
                        .then(response => {
                            if (response.data.code === 200) {
                                const readStatusChangedMessages = response.data.data.read_status_changed || [];
                                
                                if (readStatusChangedMessages.length > 0) {
                                    // 更新本地消息的已读状态
                                    readStatusChangedMessages.forEach(updatedMsg => {
                                        const index = this.messages.findIndex(msg => msg.id === updatedMsg.id);
                                        if (index !== -1) {
                                            // 更新所有已读状态相关字段
                                            this.messages[index].is_read = updatedMsg.is_read || this.messages[index].is_read;
                                            this.messages[index].read_time = updatedMsg.read_time || this.messages[index].read_time;
                                            this.messages[index].staff_read = updatedMsg.staff_read || this.messages[index].staff_read;
                                            this.messages[index].staff_read_time = updatedMsg.staff_read_time || this.messages[index].staff_read_time;
                                            this.messages[index].merchant_read = updatedMsg.merchant_read || this.messages[index].merchant_read;
                                            this.messages[index].merchant_read_time = updatedMsg.merchant_read_time || this.messages[index].merchant_read_time;
                                        }
                                    });
                                }
                            }
                        })
                        .catch(error => {

                        });
                    },
                    
                    // 启动所有轮询
                    startAllPolling() {
                        // 启动消息轮询
                        this.startPolling();
                        // 启动已读状态轮询
                        this.startReadStatusPolling();
                    },
                    // 使用Intersection Observer监测消息元素是否进入视口
                    setupMessageObserver() {
                      // 检查浏览器是否支持IntersectionObserver
                      if (!('IntersectionObserver' in window)) {

                        return;
                      }
                      
                      // 检查聊天容器是否存在
                      if (!this.$refs.chatBody) {

                        return;
                      }
                      
                      const options = {
                        root: this.$refs.chatBody,
                        threshold: 0.7 // 提高阈值，当消息显示70%以上时才触发
                      };
                      
                      if (this.observer) {
                        // 如果已存在observer，先断开所有观察
                        this.observer.disconnect();
                      }
                      
                      // 创建一个变量跟踪用户是否实际在查看聊天
                      const isActivelyViewing = () => {
                        return document.visibilityState === 'visible' && document.hasFocus();
                      };
                      
                      this.observer = new IntersectionObserver((entries) => {
                        // 只有当页面处于活跃状态才执行标记
                        if (!isActivelyViewing()) {
                          return;
                        }
                        
                        // 收集需要标记的消息ID
                        const messagesToMark = [];
                        
                        entries.forEach(entry => {
                          if (entry.isIntersecting) {
                            // 消息已在视口中
                            const messageElement = entry.target.closest('.message');
                            if (messageElement) {
                              const messageId = messageElement.dataset.messageId;
                              const messageRole = messageElement.dataset.messageRole;
                              
                              // 只有当对方发送的消息进入视口时才标记已读
                              // 由于这是客户端界面，所以当前用户角色固定为customer
                              if ((messageRole === 'staff' || messageRole === 'merchant') && messageId) {
                                messagesToMark.push(parseInt(messageId));
                                
                                // 预先在本地标记为已读，避免重复标记
                                const messageIndex = this.messages.findIndex(msg => msg.id === parseInt(messageId));
                                if (messageIndex !== -1 && !this.messages[messageIndex].is_read) {
                                  this.messages[messageIndex].is_read = 1;
                                  this.messages[messageIndex].read_time = Math.floor(Date.now() / 1000);
                                }
                              }
                              
                              // 标记后停止观察此元素
                              this.observer.unobserve(entry.target);
                            }
                          }
                        });
                        
                        // 如果有消息需要标记，批量标记
                        if (messagesToMark.length > 0) {

                          
                          // 使用单独的标记方法，每条消息都发送请求
                          // 虽然效率较低，但确保每条消息的已读状态都准确记录
                          messagesToMark.forEach(messageId => {
                            this.markMessageAsRead(messageId);
                          });
                        }
                      }, options);
                      
                      // 开始观察所有消息元素
                      this.$nextTick(() => {
                        const messageBubbles = document.querySelectorAll('.message');
                        if (messageBubbles && messageBubbles.length > 0) {

                          messageBubbles.forEach(el => {
                            // 只观察未读的消息
                            const messageId = el.dataset.messageId;
                            const messageRole = el.dataset.messageRole;
                            
                            if (messageId && (messageRole === 'staff' || messageRole === 'merchant')) {
                              // 检查消息是否未读
                              const message = this.messages.find(msg => msg.id === parseInt(messageId));
                              if (message && message.is_read === 0) {
                                this.observer.observe(el);
                              }
                            }
                          });
                        }
                      });
                    },
                    
                    // 单独的标记已读方法
                    markMessageAsRead(messageId) {
                      axios.post('/plugin/Customersystem/api/markMessageRead', {
                        message_id: messageId,
                        session_id: this.sessionId,
                        role_type: this.currentUserRole
                      });
                    },
                    // 批量标记消息为已读 - 根据角色确定哪些消息应该被标记
                    markMessagesAsRead() {
                      if (!this.sessionId) return;
                      
                      // 设置当前用户为客户（因为这是客户端界面）
                      const currentUserRole = 'customer';
                      let targetRoleType = '';
                      
                      // 客户查看时标记商家和客服消息
                      targetRoleType = 'customer'; // 参数值为当前查看者的角色，API会处理相应逻辑
                      
                      axios.post('/plugin/Customersystem/api/markSessionMessagesRead', {
                        session_id: this.sessionId,
                        role_type: targetRoleType,
                        viewer_role: currentUserRole
                      })
                      .then(response => {
                        if (response.data.code === 200) {
                          // 更新本地消息状态
                          this.messages.forEach(msg => {
                            if ((msg.role_type === 'staff' || msg.role_type === 'merchant') && !msg.is_read) {
                              msg.is_read = 1;
                              msg.read_time = Math.floor(Date.now() / 1000);
                            }
                          });
                        }
                      })
                      .catch(error => {
                        // 静默处理错误
                      });
                    },
                    
                    // 更新本地消息已读状态
                    updateLocalMessagesReadStatus(targetRoleTypes) {
                      const roleTypes = targetRoleTypes.split(',');
                      
                      this.messages.forEach(msg => {
                        if (roleTypes.includes(msg.role_type) && !msg.is_read) {
                          msg.is_read = 1;
                          msg.read_time = Math.floor(Date.now() / 1000);
                        }
                      });
                    },
                    
                    // 页面获得焦点或重新可见时处理
                    handlePageFocus() {
                      // 仅当页面处于活跃状态且用户已查看消息区域时标记为已读
                      if (document.visibilityState === 'visible' && this.userHasViewedMessages) {
                        this.markMessagesAsRead();
                      }
                    },
                    
                    // 滚动处理函数
                    handleScroll(e) {
                        const chatBody = e.target;
                        const scrollPosition = chatBody.scrollTop + chatBody.clientHeight;
                        const scrollHeight = chatBody.scrollHeight;
                        
                        if (scrollHeight - scrollPosition < 100) {
                            this.userHasViewedMessages = true;
                            this.markMessagesAsRead();
                        }
                    },
                    // 统一的已读状态判断方法
                    getReadStatus(message) {
                        const currentRole = 'customer'; // 客户端固定为customer

                        if (message.role_type === currentRole || message.sender_type === currentRole) {
                            // 自己发送的消息，检查其他角色是否已读
                            const merchantRead = message.merchant_read === 1;
                            const staffRead = message.staff_read === 1;

                            if (merchantRead && staffRead) {
                                return 'all_read'; // 全部已读
                            } else if (merchantRead && !staffRead) {
                                return 'merchant_read'; // 商家已读
                            } else if (!merchantRead && staffRead) {
                                return 'staff_read'; // 客服已读
                            } else {
                                return 'unread'; // 全部未读
                            }
                        } else {
                            // 收到的消息，检查自己是否已读
                            return message.customer_read === 1 ? 'read' : 'unread';
                        }
                    },

                    // 获取已读状态显示文本
                    getReadStatusText(message) {
                        // 只为自己发送的消息显示已读状态
                        if (message.role_type !== 'customer' && message.sender_type !== 'customer') {
                            return '';
                        }

                        // 检查商家和客服是否已读
                        const merchantRead = message.merchant_read === 1;
                        const staffRead = message.staff_read === 1;

                        if (merchantRead && staffRead) {
                            return '已读';
                        } else if (merchantRead && !staffRead) {
                            return '商家已读';
                        } else if (!merchantRead && staffRead) {
                            return '客服已读';
                        } else {
                            return '未读';
                        }
                    },
                    // 处理消息已读状态
                    processMessageReadStatus(message) {
                        // 根据当前用户角色确定关注哪个已读字段
                        const userRole = this.isCurrentUserMerchant ? 'merchant' : 'customer';
                        const fieldToCheck = userRole + '_read';
                        
                        // 根据发送者确定消息已读状态
                        if (message.sender_type === userRole) {
                            // 自己发送的消息
                            // 检查对方是否已读
                            const otherRoleRead = message.staff_read === 1;
                            return otherRoleRead ? '已读' : '未读';
                        } else {
                            // 收到的消息默认不显示已读状态
                            return '';
                        }
                    },
                    
                    // 标记会话中所有消息为已读
                    markAllMessagesAsRead() {
                        if (!this.currentSession) return;
                        
                        const userRole = this.isCurrentUserMerchant ? 'merchant' : 'customer';
                        
                        axios.post('/plugin/Customersystem/api/markSessionMessagesRead', {
                            session_id: this.currentSession.id,
                            role_type: userRole,
                            is_visible: true
                        }).then(res => {
                            if (res.data.code === 200) {

                                // 刷新消息状态
                                this.refreshMessages();
                            }
                        }).catch(err => {

                        });
                    },
                    
                    // 更新消息已读状态显示
                    updateMessageReadStatus(message) {
                        // 处理消息已读状态，存储到readStatusMap
                        const status = this.processMessageReadStatus(message);
                        if (status) {
                            this.$set(this.readStatusMap, message.id, status);
                        }
                    },
                    
                    // 在获取实时消息成功后调用此方法
                    handleNewMessages(newMessages) {
                        // 处理新消息...
                        
                        // 更新已读状态
                        newMessages.forEach(message => {
                            this.updateMessageReadStatus(message);
                        });
                    },
                    continueSession(session) {
                        // 继续当前会话
                        this.sessionStarted = true;
                        this.sessionId = session.id;
                        this.messages = session.messages || [];
                        this.contactId = session.contact_id;
                        
                        // 设置历史会话状态
                        if (session.status === 'closed' || session.isClosed) {
                            this.updateSessionStatus(session.id, 'closed');
                            
                            // 在nextTick中执行界面更新，确保DOM已更新
                            this.$nextTick(() => {
                                // 禁用输入框和发送按钮
                                this.disableInputAndUpdate();
                            });
                        } else {
                            this.updateSessionStatus(session.id, 'open');
                            // 确保输入框和发送按钮可用
                            this.$nextTick(() => {
                                this.enableInputAndUpdate();
                            });
                        }
                        
                        this.$nextTick(() => {
                            this.scrollToBottom();
                            
                            // 如果是历史会话，标记所有消息为已读
                            if (session.messages && session.has_unread) {
                                session.has_unread = false;
                                session.unread_staff_count = 0;
                                
                                // 标记会话中所有消息为已读
                                this.markAllOtherMessagesAsRead();
                            }
                            
                            // 启动轮询获取新消息和检查已读状态
                            this.startAllPolling();
                        });
                    },
                    
                    /**
                     * 标记会话中的所有消息为已读
                     */
                    markAllSessionMessagesAsRead() {
                        if (!this.currentSession || !this.currentSession.id) return;
                        
                        // 向服务器发送请求，标记当前会话的所有消息为已读
                        axios.post('/plugin/Customersystem/api/markSessionMessagesRead', {
                            session_id: this.currentSession.id
                        })
                        .then(response => {
                            if (response.data.code === 200) {
                                // 更新本地消息的已读状态
                                const updatedCount = this.messages.filter(msg => 
                                    msg.role_type !== 'customer' && !msg.is_read
                                ).length;
                                
                                this.messages.forEach(msg => {
                                    if (msg.role_type !== 'customer') {
                                        msg.is_read = 1;
                                        msg.read_time = Math.floor(Date.now() / 1000);
                                    }
                                });
                                
                                if (updatedCount > 0) {

                                }
                            }
                        })
                        .catch(error => {

                        });
                    },
                    
                    /**
                     * 检查并更新消息的已读状态
                     */
                    checkAndUpdateReadStatus() {
                        if (!this.currentSession) return;
                        
                        // 找出当前可见的非客户发送的未读消息
                        const visibleMessages = this.getVisibleMessages();
                        const unreadMessages = visibleMessages.filter(msg => 
                            msg.role_type !== 'customer' && !msg.is_read
                        );
                        
                        if (unreadMessages.length === 0) return;
                        
                        // 标记这些消息为已读
                        const messageIds = unreadMessages.map(msg => msg.id);
                        
                        axios.post('/plugin/Customersystem/api/markMessageRead', {
                            message_ids: messageIds
                        })
                        .then(response => {
                            if (response.data.code === 200) {
                                // 更新本地消息的已读状态
                                unreadMessages.forEach(msg => {
                                    msg.is_read = 1;
                                    msg.read_time = Math.floor(Date.now() / 1000);
                                });
                                
                                if (messageIds.length > 0) {

                                }
                            }
                        })
                        .catch(error => {

                        });
                    },
                    
                    /**
                     * 获取当前可见的消息
                     */
                    getVisibleMessages() {
                        if (!this.$refs.chatBody) return [];
                        
                        const chatBody = this.$refs.chatBody;
                        const messageElements = chatBody.querySelectorAll('[data-message-id]');
                        const visibleMessages = [];
                        
                        // 检查每个消息元素是否在可视区域内
                        messageElements.forEach(el => {
                            const rect = el.getBoundingClientRect();
                            const isVisible = (
                                rect.top >= 0 &&
                                rect.left >= 0 &&
                                rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                                rect.right <= (window.innerWidth || document.documentElement.clientWidth)
                            );
                            
                            if (isVisible) {
                                const messageId = parseInt(el.getAttribute('data-message-id'), 10);
                                if (!isNaN(messageId)) {
                                    const message = this.messages.find(msg => msg.id === messageId);
                                    if (message) {
                                        visibleMessages.push(message);
                                    }
                                }
                            }
                        });
                        
                        return visibleMessages;
                    },
                    
                    /**
                     * 处理滚动事件，检查并更新消息已读状态
                     */
                    handleScroll() {
                        // 使用节流函数，避免频繁触发
                        if (this.scrollThrottleTimer) return;
                        
                        this.scrollThrottleTimer = setTimeout(() => {
                            this.checkAndUpdateReadStatus();
                            this.scrollThrottleTimer = null;
                        }, 300);
                    },
                    
                    /**
                     * 标记所有非客户发送的消息为已读
                     */
                    markAllOtherMessagesAsRead() {
                        // 找出所有非客户发送的未读消息
                        const unreadMessages = this.messages.filter(msg => 
                            msg.role_type !== 'customer' && !msg.is_read
                        );
                        
                        if (unreadMessages.length === 0) return;
                        
                        // 标记这些消息为已读
                        const messageIds = unreadMessages.map(msg => msg.id);
                        
                        axios.post('/plugin/Customersystem/api/markMessageRead', {
                            message_ids: messageIds
                        })
                        .then(response => {
                            if (response.data.code === 200) {
                                // 更新本地消息的已读状态
                                unreadMessages.forEach(msg => {
                                    msg.is_read = 1;
                                    msg.read_time = Math.floor(Date.now() / 1000);
                                });
                            }
                        })
                        .catch(error => {

                        });
                    },
                    
                    /**
                     * 处理页面焦点变化事件
                     */
                    handlePageFocus() {
                        // 当页面获得焦点时
                        if (!document.hidden && this.sessionStarted) {
                            // 标记所有消息为已读
                            this.markAllSessionMessagesAsRead();
                            
                            setTimeout(() => {
                                // 当页面重新变为可见状态时，检查已读状态更新
                                this.checkAndUpdateReadStatus();
                            }, 500);
                        }
                    },
                    isImageUrl(url) {
                        if (!url || typeof url !== 'string') return false;
                        
                        // 检查是否为图片URL
                        const imageUrlPattern = /^https?:\/\/[^\s]+\.(jpg|jpeg|png|gif|bmp|webp)(\?[^\s]*)?$/i;
                        return imageUrlPattern.test(url);
                    },
                },
                mounted() {
                    // 在Vue应用挂载完成后添加平滑的加载动画
                    this.$nextTick(() => {
                        const loading = document.getElementById('loading');
                        if (loading) {
                            // 延迟一点以确保页面已经准备好
                            setTimeout(() => {
                                loading.classList.add('fade-out');
                                setTimeout(() => {
                                    loading.style.display = 'none';
                                    const app = document.getElementById('app');
                                    app.classList.add('loaded');
                                    
                                    // 检查URL参数，如果有email参数，自动进入历史查询模式
                                    const urlParams = new URLSearchParams(window.location.search);
                                    if (urlParams.has('email')) {
                                        this.checkHistory();
                                    }
                                    
                                    // 从本地存储恢复历史侧边栏状态
                                    if (localStorage.getItem('historyCollapsed') === 'true') {
                                        this.historyCollapsed = true;
                                    }
                                    
                                    // 初始化移动端历史面板
                                    if (window.innerWidth <= 768) {
                                        // 确保历史对话面板初始状态正确
                                        const historyForm = document.querySelector('.history-sidebar-container .user-form');
                                        if (historyForm) {
                                            historyForm.style.display = 'none';
                                            historyForm.classList.remove('active');
                                        }
                                    }
                                }, 500);
                            }, 300);
                        }
                        
                        // 获取参数配置
                        this.getParams();
                    });
                    
                    // 监听可见性变化
                    document.addEventListener('visibilitychange', this.handleVisibilityChange);
                    window.addEventListener('focus', this.handleFocus);
                    window.addEventListener('blur', this.handleBlur);
                    
                    // 在页面获得焦点时标记所有消息为已读 - 使用正确的处理方式
                    const self = this; // 保存Vue实例的引用
                    window.addEventListener('focus', function() {
                        if (self.sessionStarted && self.sessionId) {
                            setTimeout(function() {
                                if (typeof self.markAllSessionMessagesAsRead === 'function') {
                                    self.markAllSessionMessagesAsRead();
                                }
                                // 同时检查客户消息是否有已读状态更新
                                if (typeof self.checkAndUpdateReadStatus === 'function') {
                                    self.checkAndUpdateReadStatus();
                                }
                            }, 1000);
                        }
                    });
                    
                    // 添加页面可见性变化处理 - 使用正确的处理方式
                    document.addEventListener('visibilitychange', function() {
                        if (document.visibilityState === 'visible' && self.sessionStarted && self.sessionId) {
                            setTimeout(function() {
                                // 当页面重新变为可见状态时，检查已读状态更新
                                if (typeof self.checkAndUpdateReadStatus === 'function') {
                                    self.checkAndUpdateReadStatus();
                                }
                            }, 500);
                        }
                    });
                    
                    // 只有当聊天容器存在时才设置消息观察器
                    if (this.$refs.chatBody) {
                        this.setupMessageObserver();
                        
                        // 当滚动到底部时，认为用户已查看消息
                        this.$refs.chatBody.addEventListener('scroll', this.handleScroll);
                    }
                    
                    // 监听页面可见性变化 - 使用方法引用
                    document.addEventListener('visibilitychange', this.handlePageFocus.bind(this));
                },
                beforeDestroy() {
                  document.removeEventListener('visibilitychange', this.handlePageFocus);
                  
                  // 移除滚动事件监听器
                  if (this.$refs.chatBody) {
                      this.$refs.chatBody.removeEventListener('scroll', this.handleScroll);
                  }
                },
                updated() {
                  // 当DOM更新后，如果聊天容器存在，再次设置消息观察器
                  if (this.$refs.chatBody && this.sessionStarted) {
                      this.setupMessageObserver();
                  }
                }
            });
            
            app.use(ElementPlus, {
                locale: ElementPlusLocaleZhCn
            });
            
            app.mount('#app');
        }
    </script>

    <script>
        // 预加载关键资源
        document.addEventListener('DOMContentLoaded', function() {
            // 预加载关键资源
            const preloadLinks = [
                '/static/others/element-plus/index.css',
                '/static/others/vue/vue.global.prod.js',
                '/static/others/element-plus/index.full.min.js'
            ];
            
            preloadLinks.forEach(link => {
                const preload = document.createElement('link');
                preload.rel = 'preload';
                preload.as = link.endsWith('.css') ? 'style' : 'script';
                preload.href = link;
                document.head.appendChild(preload);
            });
            
            // 添加自定义样式类
            const style = document.createElement('style');
            style.textContent = `
                .custom-message {
                    background-color: rgba(78, 110, 242, 0.9) !important;
                    color: white !important;
                    border-radius: 8px !important;
                    padding: 12px 16px !important;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
                }
                
                .el-loading-mask {
                    background-color: rgba(255, 255, 255, 0.9) !important;
                }
                
                .el-loading-spinner .path {
                    stroke: var(--button-primary) !important;
                }
                
                .el-loading-spinner .el-loading-text {
                    color: var(--button-primary) !important;
                    font-size: 14px !important;
                    margin-top: 8px !important;
                }
                
                @keyframes pulse {
                    0% { transform: scale(1); }
                    50% { transform: scale(1.05); }
                    100% { transform: scale(1); }
                }
                
                .has-unread {
                    animation: pulse 2s infinite;
                }
            `;
            document.head.appendChild(style);
        });
    </script>

    <script>
        // 增强下拉菜单功能，特别是在移动设备上
        document.addEventListener('DOMContentLoaded', function() {
            // 获取所有下拉菜单触发器
            var dropdownTriggers = document.querySelectorAll('.dropdown-trigger');
            
            // 为每个触发器添加点击事件
            dropdownTriggers.forEach(function(trigger) {
                trigger.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // 查找相关的下拉内容
                    var parent = this.closest('.nav-dropdown');
                    var content = parent.querySelector('.nav-dropdown-content');
                    
                    // 切换显示状态
                    if (window.getComputedStyle(content).display === 'none') {
                        content.style.display = 'block';
                    } else {
                        content.style.display = 'none';
                    }
                });
            });
            
            // 点击文档其他位置关闭所有下拉菜单
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.nav-dropdown')) {
                    document.querySelectorAll('.nav-dropdown-content').forEach(function(content) {
                        content.style.display = 'none';
                    });
                }
            });
            
            // 确保下拉项可以正常点击
            document.querySelectorAll('.dropdown-item').forEach(function(item) {
                item.addEventListener('click', function(e) {
                    e.stopPropagation();
                    window.location.href = this.getAttribute('href');
                });
            });
        });
    </script>

    <script>
        // 图片预览相关方法
        function previewImageElement(url) {
            // 创建预览容器
            const previewContainer = document.createElement('div');
            previewContainer.style.position = 'fixed';
            previewContainer.style.top = '0';
            previewContainer.style.left = '0';
            previewContainer.style.width = '100%';
            previewContainer.style.height = '100%';
            previewContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.85)';
            previewContainer.style.zIndex = '10000';
            previewContainer.style.display = 'flex';
            previewContainer.style.justifyContent = 'center';
            previewContainer.style.alignItems = 'center';
            previewContainer.style.cursor = 'zoom-out';
            previewContainer.style.opacity = '0';
            previewContainer.style.transition = 'opacity 0.3s ease';
            
            // 创建图片元素
            const img = document.createElement('img');
            img.src = url;
            img.style.maxWidth = '90%';
            img.style.maxHeight = '90%';
            img.style.borderRadius = '8px';
            img.style.boxShadow = '0 5px 25px rgba(0, 0, 0, 0.2)';
            img.style.transform = 'scale(0.9)';
            img.style.transition = 'transform 0.3s ease';
            img.style.objectFit = 'contain';
            
            // 添加关闭按钮
            const closeBtn = document.createElement('div');
            closeBtn.innerHTML = '×';
            closeBtn.style.position = 'absolute';
            closeBtn.style.top = '20px';
            closeBtn.style.right = '20px';
            closeBtn.style.color = '#fff';
            closeBtn.style.fontSize = '30px';
            closeBtn.style.fontWeight = 'bold';
            closeBtn.style.cursor = 'pointer';
            closeBtn.style.width = '40px';
            closeBtn.style.height = '40px';
            closeBtn.style.display = 'flex';
            closeBtn.style.justifyContent = 'center';
            closeBtn.style.alignItems = 'center';
            closeBtn.style.borderRadius = '50%';
            closeBtn.style.background = 'rgba(0, 0, 0, 0.3)';
            closeBtn.style.transition = 'background 0.2s';
            
            closeBtn.onmouseover = () => {
                closeBtn.style.background = 'rgba(0, 0, 0, 0.6)';
            };
            
            closeBtn.onmouseout = () => {
                closeBtn.style.background = 'rgba(0, 0, 0, 0.3)';
            };
            
            // 添加元素到容器
            previewContainer.appendChild(img);
            previewContainer.appendChild(closeBtn);
            document.body.appendChild(previewContainer);
            
            // 显示动画
            setTimeout(() => {
                previewContainer.style.opacity = '1';
                img.style.transform = 'scale(1)';
            }, 10);
            
            // 点击关闭
            const closePreview = () => {
                img.style.transform = 'scale(0.9)';
                previewContainer.style.opacity = '0';
                setTimeout(() => {
                    document.body.removeChild(previewContainer);
                }, 300);
            };
            
            previewContainer.addEventListener('click', closePreview);
            closeBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                closePreview();
            });
            
            // 阻止滚动
            document.body.style.overflow = 'hidden';
            previewContainer.addEventListener('click', () => {
                document.body.style.overflow = '';
            });
            
            // ESC键关闭
            const handleEsc = (e) => {
                if (e.key === 'Escape') {
                    closePreview();
                    document.removeEventListener('keydown', handleEsc);
                }
            };
            document.addEventListener('keydown', handleEsc);
        }
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 获取导航菜单元素
            const navToggle = document.getElementById('navToggle');
            const navMenu = document.getElementById('navMenu');
            const menuBackdrop = document.getElementById('menuBackdrop');
            const dropdowns = document.querySelectorAll('.nav-dropdown');
            const historyForm = document.querySelector('.history-sidebar-container .user-form');
            const historyBackdrop = document.getElementById('historyBackdrop');
            const historyToggle = document.querySelector('.toggle-history-btn');
            
            // 汉堡菜单点击事件
            navToggle.addEventListener('click', function() {
                toggleMenu();
            });
            
            // 点击背景遮罩关闭菜单
            menuBackdrop.addEventListener('click', function() {
                closeMenu();
            });
            
            // 历史对话背景遮罩点击事件
            if (historyBackdrop) {
                historyBackdrop.addEventListener('click', function() {
                    if (historyForm && historyForm.classList.contains('active')) {
                        hideHistoryForm();
                    }
                });
            }
            
            // 菜单开关函数
            function toggleMenu() {
                navMenu.classList.toggle('active');
                menuBackdrop.classList.toggle('active');
                navToggle.classList.toggle('active');
                document.body.style.overflow = navMenu.classList.contains('active') ? 'hidden' : '';
            }
            
            // 关闭菜单函数
            function closeMenu() {
                navMenu.classList.remove('active');
                menuBackdrop.classList.remove('active');
                navToggle.classList.remove('active');
                document.body.style.overflow = '';
            }
            
            // 隐藏历史表单函数
            function hideHistoryForm() {
                historyForm.classList.remove('active');
                historyBackdrop.classList.remove('active');
                setTimeout(() => {
                    historyForm.style.display = 'none';
                }, 350);
            }
            
            // 添加历史表单的滑动关闭功能
            if (historyForm) {
                let startY = 0;
                let currentY = 0;
                
                // 监听触摸开始事件
                historyForm.addEventListener('touchstart', function(e) {
                    // 只有在用户点击拖动条或表单上半部分区域时才激活
                    if (e.target.closest('.user-form::before') || e.touches[0].clientY < (window.innerHeight - historyForm.offsetHeight/2)) {
                        startY = e.touches[0].clientY;
                        historyForm.style.transition = 'none';
                    }
                }, { passive: true });
                
                // 监听触摸移动事件
                historyForm.addEventListener('touchmove', function(e) {
                    if (startY > 0) {
                        currentY = e.touches[0].clientY;
                        let translateY = currentY - startY;
                        
                        // 只允许向下滑动
                        if (translateY > 0) {
                            historyForm.style.transform = `translateY(${translateY}px)`;
                        }
                    }
                }, { passive: true });
                
                // 监听触摸结束事件
                historyForm.addEventListener('touchend', function(e) {
                    if (startY > 0) {
                        historyForm.style.transition = 'transform 0.35s ease';
                        
                        // 如果滑动距离大于100px，则关闭表单
                        if (currentY - startY > 100) {
                            hideHistoryForm();
                        } else {
                            historyForm.style.transform = 'translateY(0)';
                        }
                        
                        startY = 0;
                        currentY = 0;
                    }
                }, { passive: true });
            }
            
            // 移动端下拉菜单点击事件
            if (window.innerWidth <= 768) {
                dropdowns.forEach(function(dropdown) {
                    const trigger = dropdown.querySelector('.dropdown-trigger');
                    
                    trigger.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation(); // 防止冒泡触发关闭整个菜单
                        
                        // 检查是否有其他展开的下拉菜单
                        const wasOpen = dropdown.classList.contains('open');
                        
                        // 关闭其他打开的下拉菜单
                        dropdowns.forEach(function(item) {
                            if (item !== dropdown) {
                                item.classList.remove('open');
                            }
                        });
                        
                        // 切换当前下拉菜单
                        if (wasOpen) {
                            dropdown.classList.remove('open');
                        } else {
                            dropdown.classList.add('open');
                        }
                    });
                });
            }
            
            // 添加触摸滑动手势支持
            let touchStartX = 0;
            let touchEndX = 0;
            
            // 监听触摸开始事件
            document.addEventListener('touchstart', function(e) {
                touchStartX = e.changedTouches[0].screenX;
            }, false);
            
            // 监听触摸结束事件
            document.addEventListener('touchend', function(e) {
                touchEndX = e.changedTouches[0].screenX;
                handleSwipe();
            }, false);
            
            // 处理滑动手势
            function handleSwipe() {
                const swipeDistance = touchEndX - touchStartX;
                const swipeThreshold = 70; // 滑动阈值
                
                // 从左向右滑动，打开菜单
                if (swipeDistance > swipeThreshold && touchStartX < 50) {
                    navMenu.classList.add('active');
                    menuBackdrop.classList.add('active');
                    navToggle.classList.add('active');
                    document.body.style.overflow = 'hidden';
                }
                
                // 从右向左滑动，关闭菜单
                if (swipeDistance < -swipeThreshold && navMenu.classList.contains('active')) {
                    closeMenu();
                }
            }
            
            // ESC键关闭菜单
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    if (navMenu.classList.contains('active')) {
                        closeMenu();
                    }
                    if (historyForm && historyForm.classList.contains('active')) {
                        hideHistoryForm();
                    }
                }
            });
            
            // 窗口大小变化时重置菜单状态
            window.addEventListener('resize', function() {
                if (window.innerWidth > 768) {
                    closeMenu();
                    
                    // 重置下拉菜单
                    dropdowns.forEach(function(dropdown) {
                        dropdown.classList.remove('open');
                    });
                    
                    // 如果有历史表单打开，需要重置样式
                    if (historyForm && historyForm.classList.contains('active')) {
                        historyForm.classList.remove('active');
                        historyBackdrop.classList.remove('active');
                        historyForm.style.display = 'none';
                        historyForm.style.transform = '';
                    }
                }
            });
            
            // 点击导航菜单内的链接后关闭菜单（针对非下拉菜单项）
            const menuLinks = navMenu.querySelectorAll('.nav-item:not(.dropdown-trigger)');
            menuLinks.forEach(function(link) {
                link.addEventListener('click', function() {
                    if (window.innerWidth <= 768) {
                        closeMenu();
                    }
                });
            });
            
            // 点击下拉菜单中的链接后关闭整个菜单
            const dropdownLinks = navMenu.querySelectorAll('.dropdown-item');
            dropdownLinks.forEach(function(link) {
                link.addEventListener('click', function() {
                    if (window.innerWidth <= 768) {
                        closeMenu();
                    }
                });
            });
        });
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 添加历史对话的移动端适配
            const historyToggle = document.querySelector('.toggle-history-btn');
            const historyForm = document.querySelector('.history-sidebar-container .user-form');
            const historyBackdrop = document.getElementById('historyBackdrop');
            
            if (historyToggle && historyForm && historyBackdrop) {
                historyToggle.addEventListener('click', function() {
                    if (window.innerWidth <= 768) {
                        // 显示/隐藏历史对话表单和背景遮罩
                        const isVisible = historyForm.style.display !== 'none';
                        if (isVisible) {
                            historyForm.style.transform = 'translateY(100%)';
                            historyBackdrop.classList.remove('active');
                            setTimeout(() => {
                                historyForm.style.display = 'none';
                            }, 350);
                        } else {
                            historyForm.style.display = 'block';
                            setTimeout(() => {
                                historyForm.style.transform = 'translateY(0)';
                            }, 10);
                            historyBackdrop.classList.add('active');
                        }
                    }
                });
                
                // 点击背景关闭历史对话
                historyBackdrop.addEventListener('click', function() {
                    historyForm.style.transform = 'translateY(100%)';
                    historyBackdrop.classList.remove('active');
                    setTimeout(() => {
                        historyForm.style.display = 'none';
                    }, 350);
                });
            }
        });
    </script>
</body>
</html> 
