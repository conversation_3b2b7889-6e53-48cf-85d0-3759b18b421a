<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商家投诉率统计</title>
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <link rel="icon" href="<?php echo $favicon; ?>" type="image/x-icon">
    <style>
        /* 全局样式 */
        body {
            margin: 0;
            background-color: #f5f7fa;
        }

        /* 头部导航样式 */
        .header {
            background: #fff;
            box-shadow: 0 1px 4px rgba(0,0,0,0.05);
            padding: 16px 24px;  /* 修改padding */
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .nav-left {
            display: flex;
            align-items: center;
            gap: 40px;
        }

        .logo-container {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .logo-img {
            height: 28px;  /* 修改logo高度 */
        }

        /* 主要内容区域 */
        .main-container {
            max-width: 1200px;
            margin: 24px auto;
            padding: 0 24px;  /* 添加左右内边距 */
        }

        /* 将内容放入卡片中 */
        .el-card {
            margin-bottom: 24px;
            box-shadow: none;  /* 移除阴影 */
        }

        .page-title {
            margin: 0;  /* 重置标题margin */
            font-size: 16px;
            font-weight: normal;
        }

        /* 分页容器样式 */
        .pagination-container {
            margin-top: 20px;
            display: flex;
            justify-content: flex-end;
            padding: 10px 0;
        }
    </style>
</head>

<body>
    <div id="app">
        <!-- 头部导航 -->
        <div class="header">
            <div class="nav-container">
                <div class="nav-left">
                    <div class="logo-container">
                        <img :src="logo" class="logo-img" alt="Logo">
                    </div>
                    <div class="nav-menu">
                        <template v-for="item in navItems" :key="item.href">
                            <a :href="item.href" 
                               class="nav-item" 
                               :class="{ active: isCurrentPage(item.href) }">
                                {{ item.name }}
                            </a>
                        </template>
                    </div>
                </div>
                <el-button type="primary" @click="goToMerchant">商家中心</el-button>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="main-container">
            <el-card shadow="never">
                <template #header>
                    <div class="card-header">
                        <span>商家投诉率统计</span>
                    </div>
                </template>

                <el-table :data="users" border v-loading="loading">
                    <el-table-column prop="username" label="商家账户" min-width="200">
                        <template #default="scope">
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <el-avatar 
                                    :size="32" 
                                    :src="scope.row.avatar || '/static/images/avatar.png'"
                                    style="flex-shrink: 0">
                                </el-avatar>
                                <div style="display: flex; flex-direction: column;">
                                    <span>{{ scope.row.username }}</span>
                                    <span style="font-size: 12px; color: #909399;" v-if="scope.row.nickname">
                                        {{ scope.row.nickname }}
                                    </span>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="投诉率" width="120" align="center">
                        <template #default="scope">
                            <el-tag :type="getComplaintRateType(scope.row.complaint_rate)" effect="light">
                                {{ scope.row.complaint_rate }}%
                            </el-tag>
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="投诉订单" width="120" align="center">
                        <template #default="scope">
                            <span>{{ scope.row.complaint_orders }}</span>
                        </template>
                    </el-table-column>

                    <el-table-column label="成交订单" width="120" align="center">
                        <template #default="scope">
                            <span>{{ scope.row.total_orders }}</span>
                        </template>
                    </el-table-column>

                    <el-table-column label="最近投诉时间" width="180" align="center">
                        <template #default="scope">
                            <span>{{ formatDate(scope.row.latest_complaint_time) }}</span>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页组件 -->
                <div class="pagination-container" v-if="users.length > 0">
                    <el-pagination
                        v-model:current-page="currentPage"
                        v-model:page-size="pageSize"
                        :page-sizes="[10, 20, 30, 50]"
                        :total="total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                    />
                </div>
            </el-card>
        </div>
    </div>

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>

    <script>
        const { createApp, ref, onMounted } = Vue;

        createApp({
            setup() {
                const users = ref([]);
                const currentPage = ref(1);
                const pageSize = ref(10);
                const total = ref(0);
                const loading = ref(false);
                const logo = ref('<?php echo $logo; ?>');
                const siteName = ref('<?php echo $siteName; ?>');
                const navItems = ref(JSON.parse('<?php echo $navItems; ?>'));

                const getComplaintRateType = (rate) => {
                    if (rate >= 20) return 'danger';
                    if (rate >= 10) return 'warning';
                    if (rate >= 5) return 'info';
                    return 'success';
                };

                const formatDate = (timestamp) => {
                    if (!timestamp) return '-';
                    const date = new Date(timestamp * 1000);
                    return date.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    }).replace(/\//g, '-');
                };

                const goToMerchant = () => {
                    window.location.href = '/merchant';
                };

                const loadData = async () => {
                    loading.value = true;
                    try {
                        const response = await axios.get('/plugin/Complaintsbusiness/Api/getComplaintsList', {
                            params: {
                                page: currentPage.value,
                                limit: pageSize.value
                            }
                        });
                        if (response.data.code === 200) {
                            users.value = response.data.items;
                            total.value = response.data.total;
                        } else {
                            ElMessage.error(response.data.msg || '获取数据失败');
                        }
                    } catch (error) {
                        ElMessage.error('获取商家列表失败');
                    } finally {
                        loading.value = false;
                    }
                };

                const handleSizeChange = (val) => {
                    pageSize.value = val;
                    currentPage.value = 1;
                    loadData();
                };

                const handleCurrentChange = (val) => {
                    currentPage.value = val;
                    loadData();
                };

                const isCurrentPage = (href) => {
                    return href === '/plugin/Complaintsbusiness/Api/index';
                };

                onMounted(() => {
                    loadData();
                });

                return {
                    users,
                    currentPage,
                    pageSize,
                    total,
                    loading,
                    logo,
                    siteName,
                    handleSizeChange,
                    handleCurrentChange,
                    getComplaintRateType,
                    formatDate,
                    goToMerchant,
                    navItems,
                    isCurrentPage
                };
            }
        }).use(ElementPlus).mount("#app");
    </script>
</body>
</html>