import os
import shutil
from datetime import datetime

# 输入和输出文件
input_file = 'stove_accounts_auto.txt'
backup_file = f'stove_accounts_auto_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.txt'
output_file = 'stove_accounts_auto_unique.txt'

# 首先备份原文件
shutil.copy(input_file, backup_file)
print(f'已备份原文件为: {backup_file}')

# 读取全部账号
with open(input_file, 'r', encoding='utf-8') as f:
    lines = [line.strip() for line in f if line.strip()]

print(f'原始文件共有 {len(lines)} 行')

# 使用字典来记录每个用户ID的第一个完整账号行
unique_accounts = {}

# 遍历所有行，保留每个用户ID的第一次出现
for line in lines:
    parts = line.split('----')
    if len(parts) >= 1:
        user_id = parts[0]
        if user_id not in unique_accounts:
            unique_accounts[user_id] = line

# 获取去重后的账号列表
unique_lines = list(unique_accounts.values())
print(f'去重后共有 {len(unique_lines)} 行')
print(f'删除了 {len(lines) - len(unique_lines)} 个重复账号')

# 直接将去重后的内容写回原文件
with open(input_file, 'w', encoding='utf-8') as f:
    for line in unique_lines:
        f.write(line + '\n')

print(f'已更新原文件，删除了所有重复账号: {input_file}') 