<?php
namespace plugin\Bannedusers\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;
use think\facade\Cache;

class Api extends BasePlugin {

    // 需要登录的方法
    protected $scene = [
        'admin'  // 表示后台需要登录
    ];
    
    // 无需登录的方法
    protected $noNeedLogin = [
        'index',
        'getBannedUsers',
        'checkUserStatus',
        'complaints',
        'getComplaintsList',
        'checkNav',
        'toggleNav',
        'clearCache',
        'bannedRecords',
        'getBannedRecords',
        'riskControl',
        'getRiskStats',
        'getRiskUsers',
        'getRiskNotice',
        'getTemplate',
        'getNoticeContent',
        'getNotice',
        'getRiskLevels',
    ];

/**
 * 插件配置页面
 */
public function chick()
{
    try {
        // 获取当前模板类型
        $templateType = plugconf('Bannedusers.template_type') ?: 'default';
        $indexPath = $templateType === 'risk' 
            ? '/plugin/Bannedusers/Api/riskControl'
            : '/plugin/Bannedusers/Api/index';
            
        // 检查父级菜单是否存在
        $parentMenu = $this->getPluginParentMenu();
            
        // 检查子菜单是否存在
        $navExists = false;
        if ($parentMenu) {
            $subMenu = Db::name('nav')
                ->where('pid', $parentMenu['id'])
                ->where('href', $indexPath)
                ->find();
            $navExists = !empty($subMenu);
        }
            
        View::assign([
            'navExists' => $navExists
        ]);
        
        return View::fetch();
    } catch (\Exception $e) {
        return $this->error($e->getMessage());
    }
}

/**
 * 切换导航菜单状态
 */
public function toggleNav()
{
    try {
        $templateType = plugconf('Bannedusers.template_type') ?: 'default';
        $indexPath = $templateType === 'risk' 
            ? '/plugin/Bannedusers/Api/riskControl'
            : '/plugin/Bannedusers/Api/index';

        // 需要检查的所有路径
        $pathList = [
            $indexPath,
            '/plugin/Bannedusers/Api/complaints',
            '/plugin/Bannedusers/Api/bannedRecords'
        ];

        // 查找是否已存在子菜单
        $childMenus = Db::name('nav')
            ->where('href', 'in', $pathList)
            ->select()
            ->toArray();

        $parentId = null;

        // 如果找到了子菜单，检查它们是否有共同的父ID
        if (!empty($childMenus)) {
            foreach ($childMenus as $menu) {
                if ($menu['pid'] > 0) {
                    if ($parentId === null) {
                        $parentId = $menu['pid'];
                    } elseif ($parentId !== $menu['pid']) {
                        // 如果子菜单有不同的父ID，使用最后找到的一个
                        $parentId = $menu['pid'];
                    }
                }
            }

            // 如果找到了父ID，获取父菜单信息
            if ($parentId !== null) {
                $parentExists = Db::name('nav')
                    ->where('id', $parentId)
                    ->find();
                
                // 删除所有子菜单和父菜单
                if ($parentExists) {
                    Db::name('nav')->where('pid', $parentExists['id'])->delete();
                    Db::name('nav')->where('id', $parentExists['id'])->delete();
                    return json(['code' => 1, 'msg' => '已从导航栏移除', 'exists' => false]);
                }
            }
        }

        // 如果没有找到父菜单或已删除，创建新的导航菜单结构
        // 定义顶级菜单
        $parentMenu = [
            'name' => '黑名单查询',
            'href' => 'javascript:;',
            'type' => 'M'  // M 表示菜单
        ];

        // 定义子菜单（按指定顺序排列）
        $childMenus = [
            [
                'href' => $indexPath,  // 使用动态路径
                'name' => '风控名单',
                'type' => 'C',
                'sort' => 2  // 第一个显示
            ],
            [
                'href' => '/plugin/Bannedusers/Api/complaints',
                'name' => '商家投诉率',
                'type' => 'C',
                'sort' => 1  // 第二个显示
            ],
            [
                'href' => '/plugin/Bannedusers/Api/bannedRecords',
                'name' => '买家黑名单',
                'type' => 'C',
                'sort' => 0  // 第三个显示
            ]
        ];
        
        // 获取最大的ID
        $maxId = Db::name('nav')->max('id');
        $parentId = $maxId + 1;
        
        // 添加顶级菜单
        Db::name('nav')->insert([
            'id' => $parentId,
            'pid' => 0,
            'type' => $parentMenu['type'],
            'name' => $parentMenu['name'],
            'href' => $parentMenu['href'],
            'sort' => 0,
            'target' => 0,
            'status' => 1,
            'create_time' => time(),
            'update_time' => time()
        ]);
        
        // 添加子菜单
        foreach ($childMenus as $index => $menu) {
            Db::name('nav')->insert([
                'id' => $parentId + $index + 1,
                'pid' => $parentId,  // 设置父级ID
                'type' => $menu['type'],
                'name' => $menu['name'],
                'href' => $menu['href'],
                'sort' => $menu['sort'],  // 使用预定义的排序值
                'target' => 0,
                'status' => 1,
                'create_time' => time(),
                'update_time' => time()
            ]);
        }
        
        // 更新菜单状态缓存
        $status = [
            'index' => true,        // 根据实际情况设置
            'complaints' => true,    // 根据实际情况设置
            'bannedRecords' => true,  // 根据实际情况设置
            'riskControl' => true     // 根据实际情况设置
        ];
        cache('bannedusers_menu_status', $status);
        
        return json(['code' => 1, 'msg' => '已添加到导航栏', 'exists' => true]);
    } catch (\Exception $e) {
        return json(['code' => 0, 'msg' => '操作失败：' . $e->getMessage()]);
    }
}

/**
 * 检查导航菜单状态
 */
public function checkNav()
{
    try {
        // 获取当前模板类型
        $templateType = plugconf('Bannedusers.template_type') ?: 'default';
        $riskPath = $templateType === 'risk' 
            ? '/plugin/Bannedusers/Api/riskControl'
            : '/plugin/Bannedusers/Api/index';

        // 需要检查的所有路径
        $pathConfigs = [
            [
                'path' => $riskPath,
                'key' => 'index',
                'name' => '风控名单'
            ],
            [
                'path' => '/plugin/Bannedusers/Api/complaints',
                'key' => 'complaints',
                'name' => '商家投诉率'
            ],
            [
                'path' => '/plugin/Bannedusers/Api/bannedRecords',
                'key' => 'bannedRecords',
                'name' => '买家黑名单'
            ]
        ];

        // 获取所有启用的菜单项并按sort降序排序
        $allMenuItems = Db::name('nav')
            ->where('status', 1)
            ->order('sort desc, id asc')  // 先按sort降序，相同时按id升序
            ->select()
            ->toArray();

        // 首先尝试通过子菜单的href找到对应的菜单项
        $childMenus = [];
        $parentId = null;
        $parentMenu = null;

        foreach ($pathConfigs as $config) {
            $menuItem = array_filter($allMenuItems, function($item) use ($config) {
                return $item['href'] === $config['path'];
            });
            
            $menuItem = reset($menuItem); // 获取第一个匹配项
            
            if ($menuItem && $menuItem['pid'] > 0) {
                $childMenus[] = $menuItem;
                
                // 如果还没有确定父ID，使用第一个找到的子菜单的pid
                if ($parentId === null) {
                    $parentId = $menuItem['pid'];
                }
            }
        }

        // 如果找到了子菜单，通过parentId找到父菜单
        if ($parentId !== null) {
            $parentMenu = array_filter($allMenuItems, function($item) use ($parentId) {
                return $item['id'] === $parentId;
            });
            
            $parentMenu = reset($parentMenu); // 获取第一个匹配项
        }

        // 如果通过子菜单找不到父菜单，尝试通过名称查找
        if (!$parentMenu) {
            $parentMenu = array_filter($allMenuItems, function($item) {
                return $item['name'] === '黑名单查询' && $item['type'] === 'M';
            });
            
            $parentMenu = reset($parentMenu); // 获取第一个匹配项
        }

        // 构建排序后的菜单数组
        $sortedMenus = [];
        $menuStatus = [];
        
        foreach ($allMenuItems as $menuItem) {
            if ($menuItem['pid'] == 0) {
                // 顶级菜单
                $sortedMenus[] = [
                    'name' => $menuItem['name'],
                    'href' => $menuItem['href'],
                    'sort' => $menuItem['sort']
                ];
            }
        }

        // 处理子菜单状态
        foreach ($pathConfigs as $config) {
            $menuItem = array_filter($allMenuItems, function($item) use ($config) {
                return $item['href'] === $config['path'];
            });
            
            $menuItem = reset($menuItem); // 获取第一个匹配项

            if ($menuItem) {
                // 如果是风控相关页面(index或riskControl)
                if (in_array($config['path'], [
                    '/plugin/Bannedusers/Api/index',
                    '/plugin/Bannedusers/Api/riskControl'
                ])) {
                    $menuStatus[$config['key']] = !($menuItem['pid'] == 0);
                } else {
                    if ($menuItem['pid'] == 0) {
                        $menuStatus[$config['key']] = false;
                    } else if ($parentMenu && $menuItem['pid'] == $parentMenu['id']) {
                        $menuStatus[$config['key']] = true;
                    } else {
                        $menuStatus[$config['key']] = false;
                    }
                }
            } else {
                $menuStatus[$config['key']] = false;
            }
        }

        // 如果存在父菜单，获取其排序后的子菜单
        $sortedSubMenus = [];
        if ($parentMenu) {
            $subMenus = array_filter($allMenuItems, function($item) use ($parentMenu) {
                return $item['pid'] == $parentMenu['id'];
            });
            
            // 按sort降序排序子菜单
            usort($subMenus, function($a, $b) {
                return $b['sort'] - $a['sort'];
            });
            
            foreach ($subMenus as $subMenu) {
                $sortedSubMenus[] = [
                    'name' => $subMenu['name'],
                    'href' => $subMenu['href'],
                    'sort' => $subMenu['sort']
                ];
            }
        }

        return json([
            'code' => 1,
            'status' => $menuStatus,
            'menus' => $sortedMenus,
            'parentMenu' => $parentMenu ? [
                'id' => $parentMenu['id'],
                'name' => $parentMenu['name'],
                'subMenus' => $sortedSubMenus
            ] : null
        ]);
    } catch (\Exception $e) {
        return json(['code' => 0, 'msg' => $e->getMessage()]);
    }
}

/**
 * 清除缓存
 */
public function clearCache()
{
    try {
        Cache::delete('banned_users_list');
        return json(['code' => 1, 'msg' => '缓存已清除']);
    } catch (\Exception $e) {
        return json(['code' => 0, 'msg' => '清除缓存失败：' . $e->getMessage()]);
    }
}

    public function index() {
        // 检查访问权限
        if (!$this->checkPageAccess('/plugin/Bannedusers/Api/index')) {
            return $this->error('未授权访问');
        }
        
        $bannedUsers = Cache::get('banned_users_list', []);
        if (empty($bannedUsers)) {
            $bannedUsers = $this->updateBannedUsers();
        }
        
        // 从system_config表读取logo、网站名称和favicon
        $logo = Db::name('system_config')
            ->where('type', 'website')
            ->where('name', 'logo')
            ->value('value');
        
        // 修改logo URL处理逻辑
        if (preg_match('/^https?:\/\/(oss\.|cos\.|[-\w]+\.qiniucdn\.|[-\w]+)/i', $logo)) {
            // 如果是完整URL(包括云存储和其他域名)，直接使用数据库中的完整路径
            $logo = $logo;
        } else if (strpos($logo, 'uploads/') === 0) {
            // 如果是uploads目录下的文件，添加域名
            $logo = request()->domain() . '/' . $logo;
        } else {
            // 其他情况(本地文件)，确保以/开头
            $logo = request()->domain() . '/' . ltrim($logo, '/');
        }
        
        $siteName = Db::name('system_config')
            ->where('type', 'website')
            ->where('name', 'app_name')
            ->value('value');
        
        $favicon = Db::name('system_config')
            ->where('type', 'website')
            ->where('name', 'favicon')
            ->value('value');
        
        // 对favicon使用相同的处理逻辑
        if (preg_match('/^https?:\/\/(oss\.|cos\.|[-\w]+\.qiniucdn\.|[-\w]+)/i', $favicon)) {
            // 如果是完整URL，直接使用
            $favicon = $favicon;
        } else if (strpos($favicon, 'uploads/') === 0) {
            $favicon = request()->domain() . '/' . $favicon;
        } else {
            $favicon = request()->domain() . '/' . ltrim($favicon, '/');
        }
        
        // 获取导航菜单数据并格式化为前端需要的格式
        $navItems = Db::name('nav')
            ->where('status', 1)
            ->where('pid', 0)
            ->order('sort asc')
            ->field(['name', 'href', 'target'])
            ->select()
            ->toArray();
        
        // 将导航数据转换为 JSON 字符串，以便在 Vue 中使用
        $navItemsJson = json_encode($navItems, JSON_UNESCAPED_UNICODE);
        
        // 获取网站配置信息
        $icpNumber = Db::name('system_config')
            ->where('type', 'website')
            ->where('name', 'icp_number')
            ->value('value');
        
        $gaNumber = Db::name('system_config')
            ->where('type', 'website')
            ->where('name', 'ga_number')
            ->value('value');
        
        $icpCert = Db::name('system_config')
            ->where('type', 'website')
            ->where('name', 'icp_cert')
            ->value('value');
        
        View::assign([
            'bannedUsers' => $bannedUsers,
            'logo' => $logo,
            'siteName' => $siteName,
            'favicon' => $favicon,
            'navItemsJson' => $navItemsJson,
            'icpNumber' => $icpNumber,
            'gaNumber' => $gaNumber,
            'icpCert' => $icpCert,
        ]);
        
        return View::fetch();
    }

    private function updateBannedUsers()
    {
        // 获取总记录数
        $total = Db::name('user')
            ->alias('u')
            ->join('user_risk r', 'u.id = r.user_id')
            ->where('r.risk_type', 'in', [1, 2])  // 修改：包含风控类型1和2
            ->count();

        // 获取分页数据
        $page = input('page/d', 1);
        $limit = input('limit/d', 10);
        $offset = ($page - 1) * $limit;

        $bannedUsers = Db::name('user')
            ->alias('u')
            ->join('user_risk r', 'u.id = r.user_id')
            ->where('r.risk_type', 'in', [1, 2])  // 修改：包含风控类型1和2
            ->field([
                'u.id',
                'u.username',
                'u.nickname',
                'u.avatar',
                'u.create_time as register_time',
                'r.content as risk_content',
                'r.create_time as banned_time',
                'r.risk_type'  // 添加：返回风控类型
            ])
            ->order('r.create_time desc')
            ->limit($offset, $limit)
            ->select()
            ->toArray();

        return [
            'total' => $total,
            'items' => $bannedUsers,
            'page' => $page,
            'limit' => $limit
        ];
    }

    public function getBannedUsers()
    {
        try {
            $page = input('page/d', 1);
            $limit = input('limit/d', 10);
            
            // 使用更短的缓存时间，避免数据不同步
            $cacheKey = 'banned_users_list_' . $page . '_' . $limit;
            $bannedUsers = Cache::get($cacheKey);
            
            if (empty($bannedUsers)) {
                $bannedUsers = $this->updateBannedUsers();
                // 缓存时间改为3秒，减少数据不同步的可能性
                Cache::set($cacheKey, $bannedUsers, 3);
            }
            
            // 确保数据格式化
            if (!empty($bannedUsers['items'])) {
                foreach ($bannedUsers['items'] as &$user) {
                    if (!empty($user['register_time']) && is_numeric($user['register_time'])) {
                        $user['register_time'] = date('Y-m-d H:i:s', $user['register_time']);
                    }
                    if (!empty($user['banned_time']) && is_numeric($user['banned_time'])) {
                        $user['banned_time'] = date('Y-m-d H:i:s', $user['banned_time']);
                    }
                }
            }
            
            return json($bannedUsers);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取数据失败：' . $e->getMessage()]);
        }
    }

    /**
     * 检查用户状态
     */
    public function checkUserStatus()
    {
        try {
            $userId = input('userId');
            if (empty($userId)) {
                return json(['code' => 0, 'msg' => '参数错误']);
            }

            // 检查用户风控状态
            $user = Db::name('user')
                ->alias('u')
                ->join('user_risk r', 'u.id = r.user_id')
                ->where('u.id', $userId)
                ->where('r.risk_type', 'in', [1, 2])  // 只检查风控类型1和2
                ->field(['u.rules', 'r.risk_type'])
                ->find();

            return json([
                'code' => 1,
                'isBanned' => !empty($user) && ($user['rules'] === '["CloseUser"]' || $user['risk_type'] == 2),
                'isTradeDisabled' => !empty($user) && $user['risk_type'] == 1
            ]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '检查失败：' . $e->getMessage()]);
        }
    }

    // 添加获取投诉列表的方法
    public function getComplaintsList()
    {
        try {
            $page = input('page/d', 1);
            $limit = input('limit/d', 10);
            $sortType = input('sort_type', 'latest_time');
            $offset = ($page - 1) * $limit;

            // 构建子查询来获取投诉数和订单数
            $subQuery = Db::name('user')
                ->alias('u')
                ->leftJoin('complaint c', 'u.id = c.user_id')
                ->leftJoin('order o', 'u.id = o.user_id AND o.status = 1')
                ->field([
                    'u.id',
                    'u.username',
                    'u.nickname',
                    'u.avatar',
                    'COUNT(DISTINCT c.id) as complaint_count',
                    'COUNT(DISTINCT o.id) as total_orders',
                    'MAX(c.create_time) as latest_complaint_time'
                ])
                ->group('u.id')
                ->having('complaint_count > 0')
                ->buildSql();

            // 主查询
            $query = Db::table($subQuery . ' a');

            // 根据排序类型设置排序方式
            switch ($sortType) {
                case 'complaint_rate':
                    $query->orderRaw('(complaint_count / IF(total_orders = 0, 1, total_orders) * 100) DESC, complaint_count DESC');
                    break;
                case 'complaint_orders':
                    $query->order('complaint_count DESC, latest_complaint_time DESC');
                    break;
                case 'latest_time':
                default:
                    $query->order('latest_complaint_time DESC, complaint_count DESC');
                    break;
            }

            // 获取总记录数
            $total = $query->count();

            // 获取分页数据
            $users = $query->limit($offset, $limit)
                ->select()
                ->each(function($item) {
                    // 计算投诉率
                    $totalOrders = intval($item['total_orders']);
                    $complaintOrders = intval($item['complaint_count']);
                    
                    // 重命名字段以匹配前端期望的字段名
                    $item['complaint_orders'] = $complaintOrders;
                    $item['total_orders'] = $totalOrders;
                    $item['complaint_rate'] = $totalOrders > 0 
                        ? round(($complaintOrders / $totalOrders) * 100, 2) 
                        : 0;
                    
                    // 用户名脱敏处理
                    $item['username'] = $this->maskString($item['username']);
                    if ($item['nickname']) {
                        $item['nickname'] = $this->maskString($item['nickname']);
                    }
                    
                    // 格式化时间
                    if ($item['latest_complaint_time']) {
                        $item['latest_complaint_time'] = intval($item['latest_complaint_time']);
                    }
                    
                    return $item;
                })
                ->toArray();

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'total' => $total,
                'items' => $users
            ]);
        } catch (\Exception $e) {
            error_log("获取投诉列表失败: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return json(['code' => 0, 'msg' => '获取数据失败：' . $e->getMessage()]);
        }
    }

    public function complaints()
    {
        // 检查访问权限
        if (!$this->checkPageAccess('/plugin/Bannedusers/Api/complaints')) {
            return $this->error('未授权访问');
        }
        
        try {
            // 获取并处理logo URL
            $logo = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'logo')
                ->value('value');
            
            // 处理logo URL
            if (preg_match('/^https?:\/\/(oss\.|cos\.|[-\w]+\.qiniucdn\.|[-\w]+)/i', $logo)) {
                // 如果是完整URL(包括云存储和其他域名)，直接使用数据库中的完整路径
                $logo = $logo;
            } else if (strpos($logo, 'uploads/') === 0) {
                // 如果是uploads目录下的文件，添加域名
                $logo = request()->domain() . '/' . $logo;
            } else {
                // 其他情况(本地文件)，确保以/开头
                $logo = request()->domain() . '/' . ltrim($logo, '/');
            }
            
            $siteName = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'app_name')
                ->value('value');
            
            $favicon = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'favicon')
                ->value('value');
            
            // 对favicon使用相同的处理逻辑
            if (preg_match('/^https?:\/\/(oss\.|cos\.|[-\w]+\.qiniucdn\.|[-\w]+)/i', $favicon)) {
                // 如果是完整URL，直接使用
                $favicon = $favicon;
            } else if (strpos($favicon, 'uploads/') === 0) {
                $favicon = request()->domain() . '/' . $favicon;
            } else {
                $favicon = request()->domain() . '/' . ltrim($favicon, '/');
            }
            
            // 获取导航菜单数据
            $navItems = Db::name('nav')
                ->where('status', 1)
                ->where('pid', 0)
                ->field(['name', 'href', 'target'])
                ->order('sort asc')
                ->select()
                ->toArray();

            // 获取网站配置信息
            $icpNumber = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'icp_number')
                ->value('value');
            
            $gaNumber = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'ga_number')
                ->value('value');
            
            $icpCert = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'icp_cert')
                ->value('value');

            View::assign([
                'logo' => $logo,
                'siteName' => $siteName,
                'favicon' => $favicon,
                'navItems' => json_encode($navItems, JSON_UNESCAPED_UNICODE),
                'icpNumber' => $icpNumber,
                'gaNumber' => $gaNumber,
                'icpCert' => $icpCert,
            ]);
            
            return View::fetch('complaints');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 字符串脱敏处理
     * @param string $string 需要脱敏的字符串
     * @return string
     */
    private function maskString($string) {
        if (!$string) return '';
        $length = mb_strlen($string);
        if ($length <= 2) {
            return mb_substr($string, 0, 1) . '*';
        } else {
            $firstChar = mb_substr($string, 0, 1);
            $lastChar = mb_substr($string, -1);
            $stars = str_repeat('*', min($length - 2, 3));
            return $firstChar . $stars . $lastChar;
        }
    }

    /**
     * 显示黑名单页面
     */
    public function bannedRecords()
    {
        // 检查访问权限，使用当前页面的实际路径
        if (!$this->checkPageAccess('/plugin/Bannedusers/Api/bannedRecords')) {
            return $this->error('未授权访问');
        }
        
        try {
            // 获取当前模板类型（用于导航菜单）
            $templateType = plugconf('Bannedusers.template_type') ?: 'default';
            
            // 获取网站配置
            $logo = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'logo')
                ->value('value');
            
            // 处理logo URL
            if (preg_match('/^https?:\/\/(oss\.|cos\.|[-\w]+\.qiniucdn\.|[-\w]+)/i', $logo)) {
                // 如果是完整URL(包括云存储和其他域名)，直接使用数据库中的完整路径
                $logo = $logo;
            } else if (strpos($logo, 'uploads/') === 0) {
                // 如果是uploads目录下的文件，添加域名
                $logo = request()->domain() . '/' . $logo;
            } else {
                // 其他情况(本地文件)，确保以/开头
                $logo = request()->domain() . '/' . ltrim($logo, '/');
            }
            
            $siteName = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'app_name')
                ->value('value');
            
            $favicon = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'favicon')
                ->value('value');
            
            // 处理 favicon URL
            if (preg_match('/^https?:\/\/(oss\.|cos\.|[-\w]+\.qiniucdn\.|[-\w]+)/i', $favicon)) {
                // 如果是完整URL，直接使用
                $favicon = $favicon;
            } else if (strpos($favicon, 'uploads/') === 0) {
                $favicon = request()->domain() . '/' . $favicon;
            } else {
                $favicon = request()->domain() . '/' . ltrim($favicon, '/');
            }
            
            // 获取导航菜单
            $navItems = Db::name('nav')
                ->where('status', 1)
                ->where('pid', 0)
                ->field(['name', 'href', 'target'])
                ->order('sort asc')
                ->select()
                ->toArray();

            // 获取网站配置信息
            $icpNumber = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'icp_number')
                ->value('value');
            
            $gaNumber = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'ga_number')
                ->value('value');
            
            $icpCert = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'icp_cert')
                ->value('value');

            View::assign([
                'logo' => $logo,
                'siteName' => $siteName,
                'favicon' => $favicon,
                'navItems' => json_encode($navItems, JSON_UNESCAPED_UNICODE),
                'icpNumber' => $icpNumber,
                'gaNumber' => $gaNumber,
                'icpCert' => $icpCert,
            ]);
            
            return View::fetch('bannedRecords');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取黑名单列表
     */
    public function getBannedRecords()
    {
        try {
            $page = input('page/d', 1);
            $limit = input('limit/d', 10);
            $offset = ($page - 1) * $limit;

            // 查询 order_buyer_black 表
            $query = Db::name('order_buyer_black')
                ->field([
                    'id',
                    '`key` as buyer_ip',      // IP地址
                    'meta as order_no',        // 订单号
                    'create_time',            // 拉黑时间
                    'count',                  // 次数
                    'types'                    // 黑名单类型
                ]);

            $total = $query->count();

            $records = $query->order('create_time desc')
                ->limit($offset, $limit)
                ->select()
                ->each(function ($item) {
                    // 格式化创建时间
                    $item['create_time'] = date('Y-m-d H:i:s', $item['create_time']);
                    
                    // 根据类型进行不同程度的脱敏
                    // 1. IP地址脱敏 - 增强版 - 无论是什么类型都脱敏处理
                    if (!empty($item['buyer_ip'])) {
                        // 先检查是否为标准IPv4地址
                        $ip_parts = explode('.', $item['buyer_ip']);
                        if (count($ip_parts) === 4) {
                            // 对IPv4进行脱敏，只保留第一段，其余全部用*代替
                            $item['buyer_ip'] = $ip_parts[0] . '.*.*.*';
                        } else {
                            // 非标准IP格式（可能是IPv6或其他格式），使用通用脱敏方法
                            $item['buyer_ip'] = $this->maskString($item['buyer_ip']);
                        }
                    }
                    
                    // 2. 订单号脱敏 - 增强版 - 无论是什么类型都脱敏处理
                    if (!empty($item['order_no'])) {
                        // 清理可能的JSON字符
                        $order_no = str_replace(['[', ']', '"'], '', $item['order_no']);
                        $length = strlen($order_no);
                        
                        if ($length > 8) {
                            // 只保留前2位和后2位，中间全部用*代替
                            $item['order_no'] = substr($order_no, 0, 2) . str_repeat('*', $length - 4) . substr($order_no, -2);
                        } else if ($length > 0) {
                            // 短订单号使用通用脱敏方法
                            $item['order_no'] = $this->maskString($order_no);
                        }
                    }
                    
                    // 3. 对其他可能的敏感字段进行脱敏
                    foreach ($item as $key => $value) {
                        // 跳过已处理的字段和不需要脱敏的字段
                        if (in_array($key, ['buyer_ip', 'order_no', 'create_time', 'id', 'count', 'types', 'types'])) {
                            continue;
                        }
                        
                        // 对其他字段进行通用脱敏处理
                        if (is_string($value) && !empty($value)) {
                            $item[$key] = $this->maskString($value);
                        }
                    }
                    
                    // 4. 设置类型显示名称
                    $type = '';
                    if (!empty($item['types'])) {
                        // 根据types字段值设置显示名称
                        switch (strtolower($item['types'])) {
                            case 'ip':
                                $type = 'IP';
                                break;
                            case 'juuid':
                                $type = '指纹JUUID';
                                break;
                            default:
                                $type = ucfirst($item['types']);
                                break;
                        }
                    } else {
                        // 如果types为空，尝试通过数据特征推断类型
                        $originalKey = $item['buyer_ip'];
                        if (filter_var($originalKey, FILTER_VALIDATE_IP)) {
                            $type = 'IP';
                        } else if (preg_match('/^r[A-Za-z0-9]{15,}$/', $originalKey)) {
                            $type = '指纹JUUID';
                        } else if (strlen($originalKey) > 30) {
                            $type = '指纹';
                        } else {
                            $type = '其他';
                        }
                    }
                    $item['type'] = $type;

                    return $item;
                })
                ->toArray();

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'total' => $total,
                'items' => $records,
                'page' => $page,
                'limit' => $limit
            ]);
        } catch (\Exception $e) {
            error_log("获取黑名单数据失败: " . $e->getMessage());
            return json(['code' => 0, 'msg' => '获取数据失败：' . $e->getMessage()]);
        }
    }

    /**
     * 格式化图片URL
     */
    private function formatImageUrl($url)
    {
        if (empty($url)) {
            return '';
        }

        // 如果是完整的URL，直接返回
        if (preg_match('/^https?:\/\//', $url)) {
            return $url;
        }

        // 如果是相对路径，确保以/开头
        return '/' . ltrim($url, '/');
    }
    
    /**
     * 检查页面访问权限
     */
    private function checkPageAccess($path)
    {
        try {
            // 首先直接检查是否存在对应的菜单(包括顶级菜单和子菜单)
            $menu = Db::name('nav')
                ->where('href', $path)
                ->where('status', 1)  // 确保菜单是启用状态
                ->find();
            
            // 如果找到启用的菜单项就允许访问
            if (!empty($menu)) {
                return true;
            }
            
            // 如果没找到，检查是否是我们的插件特定路径
            $templateType = plugconf('Bannedusers.template_type') ?: 'default';
            $riskPath = $templateType === 'risk' 
                ? '/plugin/Bannedusers/Api/riskControl'
                : '/plugin/Bannedusers/Api/index';
            
            $ourPaths = [
                $riskPath,
                '/plugin/Bannedusers/Api/complaints',
                '/plugin/Bannedusers/Api/bannedRecords'
            ];
            
            if (!in_array($path, $ourPaths)) {
                return false;
            }
            
            // 如果是我们的路径，检查其他路径是否存在于菜单中
            foreach ($ourPaths as $ourPath) {
                if ($ourPath == $path) {
                    continue;
                }
                
                $otherMenu = Db::name('nav')
                    ->where('href', $ourPath)
                    ->where('status', 1)
                    ->find();
                    
                if (!empty($otherMenu) && $otherMenu['pid'] > 0) {
                    // 如果找到其他路径对应的菜单，并且有父级菜单，则允许访问
                    return true;
                }
            }
            
            return false;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 风控页面
     */
    public function riskControl()
    {
        // 检查访问权限
        if (!$this->checkPageAccess('/plugin/Bannedusers/Api/riskControl')) {
            return $this->error('未授权访问');
        }
        
        try {
            // 获取网站配置
            $logo = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'logo')
                ->value('value');
            
            // 处理logo URL
            if (preg_match('/^https?:\/\/(oss\.|cos\.|[-\w]+\.qiniucdn\.|[-\w]+)/i', $logo)) {
                // 如果是完整URL(包括云存储和其他域名)，直接使用数据库中的完整路径
                $logo = $logo;
            } else if (strpos($logo, 'uploads/') === 0) {
                // 如果是uploads目录下的文件，添加域名
                $logo = request()->domain() . '/' . $logo;
            } else {
                // 其他情况(本地文件)，确保以/开头
                $logo = request()->domain() . '/' . ltrim($logo, '/');
            }
            
            $siteName = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'app_name')
                ->value('value');
            
            $favicon = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'favicon')
                ->value('value');
            
            // 处理 favicon URL
            if (preg_match('/^https?:\/\/(oss\.|cos\.|[-\w]+\.qiniucdn\.|[-\w]+)/i', $favicon)) {
                // 如果是完整URL，直接使用
                $favicon = $favicon;
            } else if (strpos($favicon, 'uploads/') === 0) {
                $favicon = request()->domain() . '/' . $favicon;
            } else {
                $favicon = request()->domain() . '/' . ltrim($favicon, '/');
            }
            
            // 获取网站配置信息
            $icpNumber = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'icp_number')
                ->value('value');
            
            $gaNumber = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'ga_number')
                ->value('value');
            
            $icpCert = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'icp_cert')
                ->value('value');

            // 获取导航菜单
            $navItems = Db::name('nav')
                ->where('status', 1)
                ->where('pid', 0)
                ->field(['name', 'href', 'target'])
                ->order('sort asc')
                ->select()
                ->toArray();
            
            View::assign([
                'logo' => $logo,
                'siteName' => $siteName,
                'favicon' => $favicon,
                'icpNumber' => $icpNumber,
                'gaNumber' => $gaNumber,
                'icpCert' => $icpCert,
                'navItemsJson' => json_encode($navItems, JSON_UNESCAPED_UNICODE)
            ]);
            
            return View::fetch('riskControl');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取风控用户列表
     */
    public function getRiskUsers()
    {
        try {
            $page = input('page/d', 1);
            $limit = input('limit/d', 10);
            $timeRange = input('timeRange', 'all');
            $offset = ($page - 1) * $limit;

            // 基础查询
            $query = Db::name('user')
                ->alias('u')
                ->join('user_risk r', 'u.id = r.user_id')
                ->where(function ($query) {
                    $query->whereOr([
                        ['u.rules', 'like', '%CloseUser%'],  // 店铺封禁
                        ['r.risk_type', '=', 1]  // 关闭交易
                    ]);
                })
                ->where('r.risk_type', 'in', [1, 2]);  // 确保只包含这两种类型

            // 根据时间范围添加条件
            switch ($timeRange) {
                case 'today':
                    $query->whereTime('r.create_time', 'today');
                    break;
                case 'week':
                    $query->whereTime('r.create_time', 'week');
                    break;
                case 'month':
                    $query->whereTime('r.create_time', 'month');
                    break;
                case 'all':
                default:
                    // 不添加时间限制
                    break;
            }

            // 获取总数
            $total = $query->count();

            // 获取分页数据
            $users = $query->order('r.create_time desc')
                ->limit($offset, $limit)
                ->field([
                    'u.id',
                    'u.username',
                    'u.nickname',
                    'u.avatar',
                    'u.create_time as register_time',
                    'r.content as risk_content',
                    'r.create_time as banned_time',
                    'r.risk_type'
                ])
                ->select()
                ->each(function ($item) {
                    // 处理头像URL
                    if ($item['avatar']) {
                        $item['avatar'] = $this->formatImageUrl($item['avatar']);
                    }
                    
                    // 用户名脱敏
                    $item['username'] = $this->maskString($item['username']);
                    if ($item['nickname']) {
                        $item['nickname'] = $this->maskString($item['nickname']);
                    }
                    
                    // 确保风控内容不为空
                    if (empty($item['risk_content'])) {
                        $item['risk_content'] = '未说明违规理由';
                    }
                    
                    // 时间格式化
                    if ($item['register_time']) {
                        $item['register_time'] = date('Y-m-d H:i:s', $item['register_time']);
                    }
                    if ($item['banned_time']) {
                        $item['banned_time'] = date('Y-m-d H:i:s', $item['banned_time']);
                    }
                    
                    // 根据风控内容匹配风控等级
                    $item['risk_level'] = $this->matchRiskLevel($item['risk_content']);
                    
                    // 确保 risk_type 是整数类型
                    $item['risk_type'] = intval($item['risk_type']);
                    
                    return $item;
                })
                ->toArray();

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'total' => $total,
                'items' => $users
            ]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取数据失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取风控统计数据
     */
    public function getRiskStats()
    {
        try {
            // 获取累计风控数据
            $totalRisk = Db::name('user')
                ->alias('u')
                ->join('user_risk r', 'u.id = r.user_id')
                ->where(function ($query) {
                    $query->whereOr([
                        ['u.rules', 'like', '%CloseUser%'],  // 店铺封禁
                        ['r.risk_type', '=', 1]  // 关闭交易
                    ]);
                })
                ->count();
            
            // 获取今日风控数据
            $todayRisk = Db::name('user')
                ->alias('u')
                ->join('user_risk r', 'u.id = r.user_id')
                ->where(function ($query) {
                    $query->whereOr([
                        ['u.rules', 'like', '%CloseUser%'],
                        ['r.risk_type', '=', 1]
                    ]);
                })
                ->whereTime('r.create_time', 'today')
                ->count();
            
            // 获取昨日风控数据
            $yesterdayRisk = Db::name('user')
                ->alias('u')
                ->join('user_risk r', 'u.id = r.user_id')
                ->where(function ($query) {
                    $query->whereOr([
                        ['u.rules', 'like', '%CloseUser%'],
                        ['r.risk_type', '=', 1]
                    ]);
                })
                ->whereTime('r.create_time', 'yesterday')
                ->count();
            
            // 计算今日环比（与昨日相比）
            $todayGrowth = $yesterdayRisk > 0 ? round((($todayRisk - $yesterdayRisk) / $yesterdayRisk) * 100) : 0;

            // 获取本周和上周数据
            $weekRisk = Db::name('user')
                ->alias('u')
                ->join('user_risk r', 'u.id = r.user_id')
                ->where(function ($query) {
                    $query->whereOr([
                        ['u.rules', 'like', '%CloseUser%'],
                        ['r.risk_type', '=', 1]
                    ]);
                })
                ->whereTime('r.create_time', 'week')
                ->count();

            $lastWeekRisk = Db::name('user')
                ->alias('u')
                ->join('user_risk r', 'u.id = r.user_id')
                ->where(function ($query) {
                    $query->whereOr([
                        ['u.rules', 'like', '%CloseUser%'],
                        ['r.risk_type', '=', 1]
                    ]);
                })
                ->whereTime('r.create_time', 'last week')
                ->count();

            // 计算周环比
            $weekGrowth = $lastWeekRisk > 0 ? round((($weekRisk - $lastWeekRisk) / $lastWeekRisk) * 100) : 0;

            // 获取本月和上月数据
            $monthRisk = Db::name('user')
                ->alias('u')
                ->join('user_risk r', 'u.id = r.user_id')
                ->where(function ($query) {
                    $query->whereOr([
                        ['u.rules', 'like', '%CloseUser%'],
                        ['r.risk_type', '=', 1]
                    ]);
                })
                ->whereTime('r.create_time', 'month')
                ->count();

            $lastMonthRisk = Db::name('user')
                ->alias('u')
                ->join('user_risk r', 'u.id = r.user_id')
                ->where(function ($query) {
                    $query->whereOr([
                        ['u.rules', 'like', '%CloseUser%'],
                        ['r.risk_type', '=', 1]
                    ]);
                })
                ->whereTime('r.create_time', 'last month')
                ->count();

            // 计算月环比
            $monthGrowth = $lastMonthRisk > 0 ? round((($monthRisk - $lastMonthRisk) / $lastMonthRisk) * 100) : 0;

            // 累计数据不计算环比
            $totalGrowth = 0;

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => [
                    'total' => [
                        'value' => $totalRisk,
                        'growth' => $totalGrowth,
                        'trend' => $totalGrowth >= 0 ? 'up' : 'down'
                    ],
                    'month' => [
                        'value' => $monthRisk,
                        'growth' => $monthGrowth,
                        'trend' => $monthGrowth >= 0 ? 'up' : 'down'
                    ],
                    'week' => [
                        'value' => $weekRisk,
                        'growth' => $weekGrowth,
                        'trend' => $weekGrowth >= 0 ? 'up' : 'down'
                    ],
                    'today' => [
                        'value' => $todayRisk,
                        'growth' => $todayGrowth,
                        'trend' => $todayGrowth >= 0 ? 'up' : 'down'
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取统计数据失败：' . $e->getMessage()]);
        }
    }

    /**
     * 保存风控公示内容
     */
    public function saveNotice()
    {
        try {
            $postData = json_decode(file_get_contents('php://input'), true);
            $content = $postData['content'] ?? '';
            
            if (empty($content)) {
                return json(['code' => 0, 'msg' => '公示内容不能为空']);
            }
            
            // 处理链接格式
            $content = preg_replace_callback(
                '/<a\s+(?:[^>]*?\s+)?href=(["\'])(.*?)\1(?:\s+[^>]*)?>/i',
                function($matches) {
                    $url = $matches[2];
                    $style = 'style="color: #409EFF; text-decoration: none;"';
                    
                    // 如果是外部链接
                    if (preg_match('/^https?:\/\//i', $url)) {
                        return '<a href="' . $url . '" target="_blank" rel="noopener noreferrer" ' . $style . '>';
                    }
                    
                    // 如果是内部链接
                    return '<a href="' . $url . '" target="_blank" ' . $style . '>';
                },
                $content
            );
            
            // 移除多余的引号转义
            $content = str_replace('\"', '"', $content);
            
            // 保存到插件配置中
            if (plugconf('Bannedusers.notice_content', $content)) {
                $savedContent = plugconf('Bannedusers.notice_content');
                if ($savedContent === false || empty($savedContent)) {
                    return json(['code' => 0, 'msg' => '保存失败：内容未正确保存']);
                }
                return json(['code' => 1, 'msg' => '保存成功']);
            }
            
            return json(['code' => 0, 'msg' => '保存失败']);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取风控公示内容
     */
    public function getNotice()
    {
        try {
            $content = plugconf('Bannedusers.notice_content');
            if (empty($content)) {
                // 默认的公示内容
                $content = '快发卡倡导绿色合规交易，保障买卖绿色、便捷、合法一直是我们的初衷，为积极响应落实《<a href="http://www.gov.cn/xinwen/2016-11/07/content_5129723.htm" target="_blank" style="color: #409EFF; text-decoration: none;">中华人民共和国网络安全法</a>》，快发卡将依据《<a href="/static/docs/platform_agreement.html" target="_blank" style="color: #409EFF; text-decoration: none;">平台服务协议</a>》对用户行为进行规范管理，同时为保障风控数据的公正、公开、透明，我们特此将平台风控数据作出公示。';
                // 保存默认内容
                plugconf('Bannedusers.notice_content', $content);
            }
            return json([
                'code' => 1, 
                'content' => $content
            ]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取模板设置
     */
    public function getTemplate()
    {
        try {
            $type = plugconf('Bannedusers.template_type') ?: 'default';
            return json(['code' => 1, 'type' => $type]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    /**
     * 保存模板设置
     */
    public function saveTemplate()
    {
        try {
            $type = input('type');
            if (!in_array($type, ['default', 'risk'])) {
                return json(['code' => 0, 'msg' => '无效的模板类型']);
            }

            plugconf('Bannedusers.template_type', $type);
            return json(['code' => 1, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取风控等级配置
     */
    public function getRiskLevels()
    {
        try {
            $levels = plugconf('Bannedusers.risk_levels');
            if (empty($levels)) {
                // 默认配置，使用分号分隔多个原由，增加处罚依据
                $levels = [
                    [
                        'name' => '高风险',
                        'color' => '#F56C6C',
                        'content' => '存在严重违规行为，已被系统永久封禁;发布违禁商品，已被系统永久封禁;存在欺诈行为，已被系统永久封禁',
                        'punishment' => '依据《平台服务协议》第X条，【严重违规行为】相关规定，予以永久封禁处理'
                    ],
                    [
                        'name' => '中风险',
                        'color' => '#E6A23C',
                        'content' => '存在违规行为，需要进行整改;发布不合规商品，需要整改;商品描述不实，需要整改',
                        'punishment' => '依据《平台服务协议》第X条，【一般违规行为】相关规定，暂停交易权限，整改后可恢复'
                    ],
                    [
                        'name' => '低风险',
                        'color' => '#67C23A',
                        'content' => '系统检测到轻微违规行为;商品信息不完整;商品价格异常',
                        'punishment' => '依据《平台服务协议》第X条，【轻微违规行为】相关规定，限制部分功能，整改后解除限制'
                    ]
                ];
                // 保存默认配置
                plugconf('Bannedusers.risk_levels', json_encode($levels, JSON_UNESCAPED_UNICODE));
            } else {
                $levels = json_decode($levels, true);
                // 检查每个等级的必要字段，如果为空则使用默认值
                foreach ($levels as &$level) {
                    if (empty($level['punishment'])) {
                        $defaultPunishment = '';
                        switch ($level['name']) {
                            case '高风险':
                                $defaultPunishment = '依据《平台服务协议》第X条，【严重违规行为】相关规定，予以永久封禁处理';
                                break;
                            case '中风险':
                                $defaultPunishment = '依据《平台服务协议》第X条，【一般违规行为】相关规定，暂停交易权限，整改后可恢复';
                                break;
                            case '低风险':
                                $defaultPunishment = '依据《平台服务协议》第X条，【轻微违规行为】相关规定，限制部分功能，整改后解除限制';
                                break;
                        }
                        $level['punishment'] = $defaultPunishment;
                    }
                }
            }
            return json(['code' => 1, 'levels' => $levels]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    /**
     * 保存风控等级配置
     */
    public function saveRiskLevels()
    {
        try {
            $levels = input('levels/a');
            if (empty($levels)) {
                return json(['code' => 0, 'msg' => '风控等级不能为空']);
            }

            // 验证数据
            foreach ($levels as $level) {
                if (empty($level['name'])) {
                    return json(['code' => 0, 'msg' => '等级名称不能为空']);
                }
                if (empty($level['color'])) {
                    return json(['code' => 0, 'msg' => '请选择标签颜色']);
                }
                if (empty($level['content'])) {
                    return json(['code' => 0, 'msg' => '请输入违规内容']);
                }
            }

            // 保存配置
            plugconf('Bannedusers.risk_levels', json_encode($levels, JSON_UNESCAPED_UNICODE));
            return json(['code' => 1, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    /**
     * 根据风控内容匹配风控等级
     */
    private function matchRiskLevel($content)
    {
        try {
            $levels = json_decode(plugconf('Bannedusers.risk_levels') ?: '[]', true);
            if (empty($levels)) {
                return null;
            }

            // 遍历所有风控等级
            foreach ($levels as $level) {
                if (empty($level['content'])) {
                    continue;
                }
                
                // 将内容按分号分割成多个原由
                $reasons = array_map('trim', explode(';', $level['content']));
                
                // 检查每个原由是否匹配
                foreach ($reasons as $reason) {
                    if (!empty($reason) && stripos($content, $reason) !== false) {
                        // 返回匹配的等级，但使用实际匹配到的原由
                        return array_merge($level, ['matched_content' => $reason]);
                    }
                }
            }

            // 如果没有匹配到，返回第一个等级（通常是最高风险等级）
            return $levels[0] ?? null;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 检查导航菜单状态
     */
    public function checkNavStatus()
    {
        try {
            // 获取当前模板类型
            $templateType = plugconf('Bannedusers.template_type') ?: 'default';
            $indexPath = $templateType === 'risk' 
                ? '/plugin/Bannedusers/Api/riskControl'
                : '/plugin/Bannedusers/Api/index';

            // 检查父级菜单
            $parentMenu = $this->getPluginParentMenu();

            if (!$parentMenu) {
                return json(['code' => 1, 'exists' => false]);
            }

            // 需要检查的所有子菜单路径
            $requiredPaths = [
                $indexPath,  // 风控名单/违禁商家（根据模板类型动态确定）
                '/plugin/Bannedusers/Api/complaints',  // 商家投诉率
                '/plugin/Bannedusers/Api/bannedRecords'  // 买家黑名单
            ];

            // 检查所有必需的子菜单是否都存在
            $existingMenus = Db::name('nav')
                ->where('pid', $parentMenu['id'])
                ->where('href', 'in', $requiredPaths)
                ->column('href');

            // 只有当所有必需的菜单都存在时，才返回 true
            $allExists = count(array_intersect($requiredPaths, $existingMenus)) === count($requiredPaths);

            return json([
                'code' => 1, 
                'exists' => $allExists
            ]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '检查失败：' . $e->getMessage()]);
        }
    }

    /**
     * 安装浮动菜单
     */
    public function installFloatMenu()
    {
        try {
            // 修正源文件路径，使用插件根目录
            $sourceFile = root_path() . 'plugin/Bannedusers/assets/openai.js';
            $targetDir = root_path() . 'public/assets/template/defaulth5/assets/';
            $targetFile = $targetDir . 'openai.js';
            $indexFile = root_path() . 'public/assets/template/defaulth5/index.html';

            // 检查源文件是否存在
            if (!file_exists($sourceFile)) {
                throw new \Exception('源文件不存在：' . $sourceFile);
            }

            // 检查目标目录是否存在，不存在则创建
            if (!is_dir($targetDir)) {
                mkdir($targetDir, 0755, true);
            }

            // 检查目标文件是否已存在
            if (file_exists($targetFile)) {
                // 如果文件已存在，检查内容是否相同
                if (md5_file($sourceFile) === md5_file($targetFile)) {
                    // 检查 index.html 是否已包含脚本标签
                    $indexContent = file_get_contents($indexFile);
                    if (strpos($indexContent, 'openai.js') !== false) {
                        return json(['code' => 0, 'msg' => '浮动菜单已安装']);
                    }
                }
            }

            // 复制文件
            if (!copy($sourceFile, $targetFile)) {
                throw new \Exception('复制文件失败');
            }

            // 修改 index.html
            if (file_exists($indexFile)) {
                $indexContent = file_get_contents($indexFile);
                
                // 检查是否已包含脚本标签
                if (strpos($indexContent, 'openai.js') === false) {
                    // 在 </body> 标签前插入脚本
                    $scriptTag = "\n<script src=\"/assets/template/defaulth5/assets/openai.js\"></script>\n";
                    $indexContent = str_replace('</body>', $scriptTag . '</body>', $indexContent);
                    
                    // 保存修改后的文件
                    if (!file_put_contents($indexFile, $indexContent)) {
                        throw new \Exception('修改 index.html 失败');
                    }
                }
            } else {
                throw new \Exception('index.html 文件不存在');
            }

            return json(['code' => 1, 'msg' => '浮动菜单安装成功']);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '安装失败：' . $e->getMessage()]);
        }
    }

    /**
     * 卸载浮动菜单
     */
    public function uninstallFloatMenu()
    {
        try {
            $targetFile = root_path() . 'public/assets/template/defaulth5/assets/openai.js';
            $indexFile = root_path() . 'public/assets/template/defaulth5/index.html';

            // 删除 js 文件
            if (file_exists($targetFile)) {
                if (!unlink($targetFile)) {
                    throw new \Exception('删除 openai.js 文件失败');
                }
            }

            // 从 index.html 中移除脚本标签
            if (file_exists($indexFile)) {
                $indexContent = file_get_contents($indexFile);
                $scriptTag = "\n<script src=\"/assets/template/defaulth5/assets/openai.js\"></script>\n";
                
                // 移除脚本标签
                $indexContent = str_replace($scriptTag, '', $indexContent);
                
                // 保存修改后的文件
                if (!file_put_contents($indexFile, $indexContent)) {
                    throw new \Exception('修改 index.html 失败');
                }
            }

            return json(['code' => 1, 'msg' => '浮动菜单卸载成功']);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '卸载失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取插件的父级菜单
     * 通过子菜单的href查找pid，再通过pid找到父级菜单
     * 
     * @return array|null 父级菜单信息，未找到返回null
     */
    private function getPluginParentMenu()
    {
        try {
            // 获取当前模板类型
            $templateType = plugconf('Bannedusers.template_type') ?: 'default';
            $indexPath = $templateType === 'risk' 
                ? '/plugin/Bannedusers/Api/riskControl'
                : '/plugin/Bannedusers/Api/index';
            
            // 需要检查的所有路径
            $pathList = [
                $indexPath,
                '/plugin/Bannedusers/Api/complaints',
                '/plugin/Bannedusers/Api/bannedRecords'
            ];
            
            // 查找子菜单
            $childMenus = Db::name('nav')
                ->where('href', 'in', $pathList)
                ->select()
                ->toArray();
            
            if (empty($childMenus)) {
                return null;
            }
            
            // 查找共同的父ID
            $parentIds = [];
            foreach ($childMenus as $menu) {
                if ($menu['pid'] > 0) {
                    $parentIds[] = $menu['pid'];
                }
            }
            
            if (empty($parentIds)) {
                return null;
            }
            
            // 获取出现次数最多的父ID
            $parentIdCounts = array_count_values($parentIds);
            arsort($parentIdCounts);
            $mostCommonParentId = key($parentIdCounts);
            
            // 获取父菜单信息
            $parentMenu = Db::name('nav')
                ->where('id', $mostCommonParentId)
                ->find();
            
            return $parentMenu ?: null;
        } catch (\Exception $e) {
            return null;
        }
    }
}