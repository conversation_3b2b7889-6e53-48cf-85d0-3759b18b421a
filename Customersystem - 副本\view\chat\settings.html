<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服系统设置</title>
    <!-- 引入Element Plus -->
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <!-- 添加补充样式，修复switch组件样式问题 -->
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%),
                linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #2d3748;
            height: 100vh;
            overflow: hidden;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.03)"/><circle cx="10" cy="50" r="0.5" fill="rgba(255,255,255,0.03)"/><circle cx="90" cy="30" r="0.5" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
            z-index: 0;
        }

        body::after {
            content: '';
            position: fixed;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background:
                conic-gradient(from 0deg at 50% 50%,
                    rgba(102, 126, 234, 0.1) 0deg,
                    rgba(118, 75, 162, 0.1) 120deg,
                    rgba(255, 119, 198, 0.1) 240deg,
                    rgba(102, 126, 234, 0.1) 360deg);
            animation: rotate 60s linear infinite;
            pointer-events: none;
            z-index: 0;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .container {
            padding: 8px 16px;
            margin: 0 auto;
            height: calc(100vh - 64px); /* 减去header高度 */
            overflow: hidden;
            display: flex;
            flex-direction: column;
            background: transparent;
            max-width: 1600px; /* 增加最大宽度 */
            width: 100%;
            position: relative;
            z-index: 1;
        }
        
        .el-header {
            background: rgba(255, 255, 255, 0.85);
            backdrop-filter: blur(30px) saturate(1.2);
            color: #2d3748;
            line-height: 64px;
            box-shadow:
                0 4px 20px rgba(0, 0, 0, 0.12),
                0 1px 4px rgba(0, 0, 0, 0.08),
                inset 0 -1px 0 rgba(255, 255, 255, 0.3);
            position: sticky;
            top: 0;
            z-index: 1000;
            transition: all 0.3s ease;
            flex-shrink: 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.3);
            position: relative;
        }

        .el-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(102, 126, 234, 0.3) 50%,
                transparent 100%);
        }

        .el-header:hover {
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .el-header-inner {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 32px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title {
            font-size: 24px;
            font-weight: 700;
            color: #1a202c;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header-back {
            color: #667eea;
            text-decoration: none;
            display: flex;
            align-items: center;
            padding: 8px 16px;
            border-radius: 8px;
            background: rgba(102, 126, 234, 0.1);
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .header-back:hover {
            color: #5a67d8;
            background: rgba(102, 126, 234, 0.2);
            transform: translateY(-1px);
        }
        
        .settings-card {
            margin-bottom: 12px;
            border-radius: 12px;
            overflow: visible;
            box-shadow:
                0 6px 24px rgba(0, 0, 0, 0.1),
                0 2px 6px rgba(0, 0, 0, 0.06),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.3);
            background: rgba(255, 255, 255, 0.85);
            backdrop-filter: blur(30px) saturate(1.2);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .settings-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(255, 255, 255, 0.6) 50%,
                transparent 100%);
        }

        .settings-card:hover {
            transform: translateY(-2px) scale(1.005);
            box-shadow:
                0 12px 40px rgba(0, 0, 0, 0.12),
                0 8px 24px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
            border-color: rgba(255, 255, 255, 0.4);
        }

        .settings-header {
            border-bottom: 1px solid rgba(226, 232, 240, 0.5);
            padding-bottom: 12px;
            margin-bottom: 18px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
            margin: -20px -20px 18px -20px;
            padding: 16px 20px 12px 20px;
        }

        .settings-header h2 {
            font-size: 18px;
            font-weight: 700;
            margin: 0;
            color: #1a202c;
            position: relative;
            padding-left: 16px;
        }

        .settings-header h2::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 5px;
            height: 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 3px;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }
        
        .header-actions {
            display: flex;
            gap: 8px;
        }
        
        .action-buttons {
            position: fixed;
            left: 50%;
            transform: translateX(-50%);
            bottom: 24px;
            width: 90%;
            max-width: 600px;
            margin: 0 auto;
            border-radius: 20px;
            padding: 20px 24px;
            background: rgba(255, 255, 255, 0.85);
            backdrop-filter: blur(30px) saturate(1.2);
            box-shadow:
                0 12px 48px rgba(0, 0, 0, 0.15),
                0 4px 16px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
            z-index: 1000;
            text-align: right;
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .action-buttons::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(255, 255, 255, 0.8) 50%,
                transparent 100%);
        }

        .action-buttons:hover {
            transform: translateX(-50%) translateY(-2px);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
        }

        .action-buttons .el-button {
            margin-left: 8px;
            padding: 10px 20px;
            font-size: 13px;
            min-width: 80px;
        }

        .action-buttons .el-button:first-child {
            margin-left: 0;
        }
        
        .color-block {
            width: 24px;
            height: 24px;
            display: inline-block;
            margin-right: 12px;
            border: 1px solid #e2e8f0;
            vertical-align: middle;
            border-radius: 4px;
        }
        
        /* 表单样式优化 */
        .el-form-item {
            margin-bottom: 16px;
            transition: all 0.3s ease;
        }

        .el-form-item:hover {
            transform: translateY(-1px);
        }

        .el-form-item__label {
            font-weight: 600;
            color: #2d3748;
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 6px;
        }

        .el-input, .el-textarea {
            --el-input-border-radius: 8px;
            --el-input-border-color: rgba(226, 232, 240, 0.8);
            --el-input-focus-border-color: #667eea;
            --el-input-hover-border-color: #a0aec0;
        }

        .el-input__wrapper {
            border-radius: 8px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
            transition: all 0.3s ease;
        }

        .el-input__wrapper:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        .el-input__wrapper.is-focus {
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .el-button {
            border-radius: 6px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
            padding: 8px 16px;
        }

        .el-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .el-button--primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }

        .el-button--primary:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        }
        
        /* 优化switch组件样式 */
        .el-switch {
            --el-switch-on-color: #667eea;
            --el-switch-off-color: #cbd5e1;
            margin-right: 8px;
        }

        .el-switch__core {
            margin: 0;
            display: inline-flex;
            position: relative;
            align-items: center;
            height: 20px;
            min-width: 40px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            background-color: var(--el-switch-off-color);
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .el-switch.is-checked .el-switch__core {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 1px 4px rgba(102, 126, 234, 0.3);
        }

        .el-switch__core .el-switch__action {
            position: absolute;
            left: 2px;
            border-radius: 50%;
            transition: all 0.3s ease;
            width: 16px;
            height: 16px;
            background-color: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        .el-switch.is-checked .el-switch__core .el-switch__action {
            left: calc(100% - 18px);
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
        }

        .el-switch + span {
            color: #4a5568;
            font-weight: 500;
            font-size: 14px;
        }

        /* 邮箱输入容器样式 */
        .email-input-container {
            width: 100%;
        }

        .form-tip {
            margin-top: 6px;
            padding: 6px 10px;
            background: rgba(102, 126, 234, 0.05);
            border-left: 2px solid #667eea;
            border-radius: 4px;
            color: #4a5568;
            font-size: 12px;
            line-height: 1.3;
        }

        /* 联系字段选择器样式 */
        .contact-field-selector {
            background: rgba(248, 250, 252, 0.8);
            border: 1px solid rgba(226, 232, 240, 0.8);
            border-radius: 8px;
            padding: 16px;
            backdrop-filter: blur(10px);
        }

        .selector-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid rgba(226, 232, 240, 0.5);
            color: #4a5568;
            font-weight: 600;
            font-size: 13px;
        }

        .contact-radio-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .radio-option {
            background: white;
            border: 1px solid rgba(226, 232, 240, 0.8);
            border-radius: 6px;
            padding: 12px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .radio-option:hover {
            border-color: rgba(102, 126, 234, 0.3);
            background: rgba(102, 126, 234, 0.02);
            transform: translateY(-1px);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
        }

        .radio-option:has(.el-radio.is-checked) {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
        }

        .radio-content {
            display: flex;
            align-items: center;
        }

        .radio-icon {
            color: #667eea;
            font-size: 16px;
            margin-right: 12px;
        }

        .radio-text {
            color: #2d3748;
            font-weight: 500;
            font-size: 14px;
        }
        
        /* 预设回复相关样式 */
        .preset-reply-item {
            margin-bottom: 12px;
            padding: 12px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background-color: #f8fafc;
            transition: all 0.3s;
        }

        .preset-reply-item:hover {
            border-color: #cbd5e1;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .preset-reply-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-30px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(30px); }
            to { opacity: 1; transform: translateX(0); }
        }

        .settings-card {
            animation: fadeIn 0.6s ease-out;
        }

        .settings-menu {
            animation: slideInLeft 0.5s ease-out;
        }

        .settings-content {
            animation: slideInRight 0.5s ease-out 0.1s both;
        }

        .el-form-item {
            animation: fadeIn 0.4s ease-out;
        }

        .el-form-item:nth-child(even) {
            animation-delay: 0.1s;
        }

        .el-form-item:nth-child(odd) {
            animation-delay: 0.05s;
        }
        
        /* 左侧导航菜单样式 */
        .settings-container {
            display: flex;
            width: 100%;
            height: 100%; /* 使用父容器的完整高度 */
            position: relative;
            overflow: hidden;
            flex: 1;
        }

        .settings-menu {
            width: 280px; /* 增加菜单宽度 */
            background: rgba(255, 255, 255, 0.85);
            backdrop-filter: blur(30px) saturate(1.2);
            border-right: 1px solid rgba(255, 255, 255, 0.3);
            padding: 16px 0;
            height: 100%;
            overflow-y: auto;
            box-shadow:
                0 6px 24px rgba(0, 0, 0, 0.1),
                0 2px 6px rgba(0, 0, 0, 0.06),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            border-radius: 16px;
            flex-shrink: 0;
            margin-right: 10px; /* 调整间距 */
            position: relative;
        }

        .settings-menu::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(255, 255, 255, 0.6) 50%,
                transparent 100%);
        }
        
        .settings-content {
            flex: 1;
            padding: 0 24px 0 24px; /* 底部间距设为0 */
            overflow-y: auto;
            overflow-x: hidden;
            height: 100%;
            position: relative;
            min-width: 0; /* 确保内容区域能够正确收缩 */
            /* 优化滚动性能 */
            -webkit-overflow-scrolling: touch;
            scrollbar-width: thin;
        }
        
        .el-menu {
            border-right: none;
            background: transparent;
        }

        .el-menu-item {
            margin: 8px 16px;
            border-radius: 12px;
            transition: all 0.3s ease;
            color: #4a5568;
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }

        .el-menu-item:hover {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            color: #667eea;
            transform: translateX(4px);
        }

        .el-menu-item.is-active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .el-menu-item.is-active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 4px;
            height: 100%;
            background: rgba(255, 255, 255, 0.3);
        }

        .el-menu-item i {
            margin-right: 12px;
            font-size: 16px;
        }
        
        .el-menu-item.is-active {
            background-color: #eef2ff;
            color: #3b82f6;
            font-weight: 600;
        }
        
        .el-menu-item {
            padding-left: 20px !important;
            margin: 5px 0;
            border-radius: 0 24px 24px 0;
        }
        
        .el-menu-item:hover {
            background-color: #f1f5f9;
        }
        
        /* 响应式设计 */

        /* 大屏幕桌面端优化 (1440px+) */
        @media (min-width: 1440px) {
            .container {
                max-width: 1400px;
                padding: 32px;
            }

            .settings-menu {
                width: 320px;
            }

            .settings-content {
                padding: 0 40px 0 40px;
            }

            .el-form-item__label {
                width: 140px !important;
            }
        }

        /* 标准桌面端 (1200px - 1439px) */
        @media (min-width: 1200px) and (max-width: 1439px) {
            .container {
                max-width: 1200px;
                padding: 28px;
            }

            .settings-menu {
                width: 300px;
            }

            .settings-content {
                padding: 0 32px 0 32px;
            }
        }

        /* 中等桌面端 (1024px - 1199px) */
        @media (min-width: 1024px) and (max-width: 1199px) {
            .settings-menu {
                width: 260px;
            }

            .el-form-item__label {
                width: 130px !important;
            }

            .settings-content {
                padding: 0 28px 0 28px;
            }
        }

        /* 平板端横屏 (768px - 1023px) */
        @media (min-width: 768px) and (max-width: 1023px) {
            .container {
                padding: 20px;
            }

            .settings-menu {
                width: 240px;
            }

            .el-form-item__label {
                width: 120px !important;
                font-size: 14px;
            }

            .settings-content {
                padding: 0 24px 0 24px;
            }

            .el-form-item {
                margin-bottom: 18px;
            }

            .settings-card {
                margin-bottom: 16px;
            }
        }

        /* 手机端竖屏 (最大宽度 767px) */
        @media (max-width: 767px) {
            body {
                height: 100vh;
                overflow: hidden;
                background:
                    radial-gradient(circle at 30% 70%, rgba(120, 119, 198, 0.4) 0%, transparent 50%),
                    radial-gradient(circle at 70% 30%, rgba(255, 119, 198, 0.4) 0%, transparent 50%),
                    linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            }

            body::after {
                animation-duration: 40s;
            }

            .container {
                padding: 8px 12px; /* 手机端适中内边距 */
                height: calc(100vh - 64px);
                max-width: 100%;
            }

            .settings-container {
                flex-direction: column;
                height: 100%;
                overflow: hidden;
                gap: 0;
            }

            .settings-menu {
                width: 100%;
                height: 55px; /* 稍微增加高度以便更好的触摸体验 */
                margin-right: 0;
                margin-bottom: 15px;
                border-right: none;
                border-bottom: 1px solid rgba(255, 255, 255, 0.3);
                overflow-x: auto;
                overflow-y: hidden;
                padding: 8px 0;
                border-radius: 12px;
                flex-shrink: 0;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            }

            .el-menu--horizontal {
                display: flex;
                width: 100%;
                border-bottom: none;
                background: transparent;
            }

            .el-menu--horizontal > .el-menu-item {
                height: 40px; /* 增加触摸区域 */
                line-height: 40px;
                margin: 0 6px;
                border-bottom: none;
                flex-shrink: 0;
                font-size: 13px;
                padding: 0 12px;
                border-radius: 8px;
                transition: all 0.3s ease;
                white-space: nowrap;
                min-width: auto;
            }

            .el-menu--horizontal > .el-menu-item:hover {
                background: rgba(78, 110, 242, 0.1);
                color: #4e6ef2;
            }

            .el-menu--horizontal > .el-menu-item.is-active {
                background: #4e6ef2;
                color: white;
                font-weight: 500;
            }

            .settings-content {
                flex: 1;
                padding: 0 16px 0 16px;
                overflow-y: auto;
                overflow-x: hidden;
                height: calc(100% - 70px); /* 调整减去菜单高度 */
                background: rgba(255, 255, 255, 0.9);
                border-radius: 12px;
                backdrop-filter: blur(20px);
            }

            .el-form-item__label {
                width: 90px !important;
                font-size: 13px;
                min-width: 90px;
                text-align: left;
                line-height: 1.4;
                padding-right: 8px;
            }

            /* 移动端表单元素优化 */
            .el-input, .el-textarea, .el-select {
                max-width: 100%;
                font-size: 14px;
            }

            .el-input__inner, .el-textarea__inner {
                font-size: 14px;
                padding: 10px 12px;
                border-radius: 8px;
                border: 1px solid #e2e8f0;
                transition: all 0.3s ease;
            }

            .el-input__inner:focus, .el-textarea__inner:focus {
                border-color: #4e6ef2;
                box-shadow: 0 0 0 2px rgba(78, 110, 242, 0.1);
            }

            .el-form-item {
                margin-bottom: 18px;
            }

            .el-form-item__content {
                line-height: 1.5;
            }

            .email-input-container .el-input {
                max-width: 100%;
            }

            .contact-field-selector {
                padding: 14px;
                border-radius: 10px;
                background: #f8fafc;
            }

            .radio-option {
                padding: 12px;
                border-radius: 8px;
                margin-bottom: 8px;
            }

            .action-buttons {
                width: 100%;
                padding: 16px;
                border-radius: 12px;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(10px);
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            }

            /* 移动端卡片样式优化 */
            .settings-card {
                margin-bottom: 16px;
                border-radius: 12px;
                box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
                border: 1px solid rgba(255, 255, 255, 0.2);
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(10px);
            }

            .settings-card .el-card__header {
                padding: 16px;
                background: rgba(78, 110, 242, 0.05);
                border-bottom: 1px solid rgba(78, 110, 242, 0.1);
            }

            .settings-card .el-card__body {
                padding: 16px;
            }

            /* 移动端预设回复项优化 */
            .preset-reply-item {
                margin-bottom: 12px;
                padding: 14px;
                border-radius: 10px;
                background: #f8fafc;
                border: 1px solid #e2e8f0;
            }

            /* 移动端按钮优化 */
            .el-button {
                padding: 10px 16px;
                font-size: 14px;
                border-radius: 8px;
                min-height: 40px; /* 确保足够的触摸区域 */
            }

            .el-button--small {
                padding: 8px 12px;
                font-size: 13px;
                min-height: 36px;
            }

            /* 移动端开关控件优化 */
            .el-switch {
                transform: scale(1.1); /* 稍微放大以便操作 */
            }

            /* 移动端头部操作按钮 */
            .header-actions {
                display: flex;
                gap: 8px;
                flex-wrap: wrap;
            }

            .header-actions .el-button {
                flex: 1;
                min-width: 80px;
            }
        }

        /* 小屏手机端优化 (最大宽度 480px) */
        @media (max-width: 480px) {
            .container {
                padding: 6px 8px;
            }

            .settings-menu {
                height: 50px;
                padding: 6px 0;
                margin-bottom: 12px;
            }

            .el-menu--horizontal > .el-menu-item {
                height: 36px;
                line-height: 36px;
                font-size: 12px;
                padding: 0 8px;
                margin: 0 4px;
            }

            .settings-content {
                padding: 0 12px 0 12px;
                height: calc(100% - 62px);
            }

            .el-form-item__label {
                width: 80px !important;
                font-size: 12px;
                min-width: 80px;
            }

            .settings-card .el-card__header {
                padding: 12px;
            }

            .settings-card .el-card__body {
                padding: 12px;
            }

            .el-form-item {
                margin-bottom: 16px;
            }

            .header-actions {
                flex-direction: column;
                gap: 6px;
            }

            .header-actions .el-button {
                width: 100%;
                margin: 0;
            }

            .preset-reply-item {
                padding: 12px;
                margin-bottom: 10px;
            }

            .el-input__inner, .el-textarea__inner {
                font-size: 13px;
                padding: 8px 10px;
            }
        }

        /* 手机端横屏优化 */
        @media (max-width: 767px) and (orientation: landscape) {
            .container {
                padding: 8px 16px;
            }

            .settings-container {
                flex-direction: row;
                gap: 16px;
            }

            .settings-menu {
                width: 200px;
                height: auto;
                flex-direction: column;
                margin-bottom: 0;
                margin-right: 16px;
                border-bottom: none;
                border-right: 1px solid rgba(255, 255, 255, 0.3);
                overflow-y: auto;
                overflow-x: hidden;
                padding: 12px 0;
            }

            .el-menu--horizontal {
                flex-direction: column;
            }

            .el-menu--horizontal > .el-menu-item {
                width: 100%;
                height: 44px;
                line-height: 44px;
                margin: 0 0 4px 0;
                text-align: left;
                padding: 0 16px;
            }

            .settings-content {
                flex: 1;
                height: calc(100vh - 80px);
                padding: 0 20px 0 20px;
            }
        }

        /* 修复移动端滚动问题 */
        @media (max-width: 767px) {
            .el-main {
                height: calc(100vh - 64px);
                overflow: hidden;
            }

            .action-buttons {
                position: fixed;
                left: 50%;
                transform: translateX(-50%);
                bottom: 16px;
                width: 90%;
                max-width: 500px;
                border-radius: 8px;
                margin: 0;
                padding: 12px 16px;
                background: rgba(255, 255, 255, 0.98);
                box-shadow: 0 2px 15px rgba(0, 0, 0, 0.15);
                z-index: 1001; /* 确保按钮在最上层 */
            }
            
            /* 优化菜单在移动端的显示 */
            .el-menu--horizontal.settings-menu-vertical {
                padding: 5px;
                border-radius: 8px;
                background: #fff;
            }
            
            /* 修复设置卡片在移动端的显示 */
            .settings-card {
                margin-bottom: 10px;
                transform: none !important;
            }
            
            /* 确保下拉菜单正确显示 */
            .el-select-dropdown {
                max-width: 90vw;
            }
        }
        
        /* 添加主容器样式 */
        .el-main {
            padding: 0;
            overflow: hidden;
            height: calc(100vh - 64px);
            flex: 1;
        }
        
        /* 修正表单在内容区的表现 */
        .settings-content .el-form {
            height: auto;
            overflow: visible;
            padding: 0; /* 底部间距设为0 */
        }

        /* 设置每个标签页内容的样式 */
        .settings-content .el-form > div {
            height: auto;
            overflow: visible;
        }

        /* 确保card内容不会溢出 */
        .settings-card {
            overflow: visible;
            margin-bottom: 16px;
        }

        /* 确保元素过多时可以滚动 */
        .preset-reply-item, .faq-category-item {
            max-width: 100%;
            overflow-x: hidden;
            word-wrap: break-word;
        }
        
        /* 美化滚动条 */
        .settings-content::-webkit-scrollbar {
            width: 8px;
        }

        .settings-content::-webkit-scrollbar-track {
            background: rgba(241, 245, 249, 0.3);
            border-radius: 4px;
        }

        .settings-content::-webkit-scrollbar-thumb {
            background: rgba(203, 213, 225, 0.6);
            border-radius: 4px;
        }

        .settings-content::-webkit-scrollbar-thumb:hover {
            background: rgba(148, 163, 184, 0.8);
        }

        .settings-menu::-webkit-scrollbar {
            height: 6px;
            width: 6px;
        }

        .settings-menu::-webkit-scrollbar-track {
            background: rgba(241, 245, 249, 0.3);
            border-radius: 3px;
        }

        .settings-menu::-webkit-scrollbar-thumb {
            background: rgba(203, 213, 225, 0.6);
            border-radius: 3px;
        }

        .settings-menu::-webkit-scrollbar-thumb:hover {
            background: rgba(148, 163, 184, 0.8);
        }

        /* 优化表单项间距，防止内容过于紧密 */
        .el-form-item {
            margin-bottom: 18px;
        }

        /* 确保预设回复项目有足够的空间 */
        .preset-reply-item {
            margin-bottom: 12px;
        }

        /* 优化FAQ分类项的显示 */
        .faq-category-item {
            margin-bottom: 16px;
        }

        /* 确保页面高度正确 */
        html, body {
            height: 100%;
            overflow: hidden;
        }

        #app {
            height: 100%;
        }

        .el-container {
            height: 100%;
        }

        .el-main {
            padding: 0;
            height: calc(100% - 64px);
            overflow: hidden;
        }

        /* 确保所有内容都能正确显示 */
        .settings-card .el-card__body {
            padding: 20px;
            overflow: visible;
        }

        /* 优化textarea和input的显示 */
        .el-textarea__inner {
            resize: vertical;
            min-height: 60px;
        }

        /* 确保长内容能够正确换行 */
        .el-input__inner, .el-textarea__inner {
            word-wrap: break-word;
            word-break: break-all;
        }

        /* 优化移动端的输入框 */
        @media (max-width: 768px) {
            .el-textarea__inner {
                min-height: 60px;
            }

            .el-input__inner {
                font-size: 16px; /* 防止iOS缩放 */
            }
        }

        /* 确保页面内容不会被底部按钮遮挡 */
        body {
            padding-bottom: 0;
            margin-bottom: 0;
        }

        /* 底部间距设为0 */
        .settings-content::after {
            content: '';
            display: block;
            height: 0px;
            width: 100%;
        }

        /* 优化内容区域的最大高度和滚动 */
        .settings-content {
            max-height: calc(100vh - 64px - 32px); /* 减去header和padding */
        }

        /* 确保表单内容不会超出视窗 */
        .el-form {
            max-height: none;
        }

        /* 优化大屏幕显示 */
        @media (min-width: 1200px) {
            .container {
                max-width: 1800px; /* 大屏幕使用更大宽度 */
            }

            .settings-menu {
                width: 320px; /* 大屏幕增加菜单宽度 */
            }

            .settings-content {
                padding: 0 32px; /* 大屏幕增加内边距 */
            }
        }

        /* 超大屏幕优化 */
        @media (min-width: 1600px) {
            .container {
                max-width: 2000px;
            }

            .settings-menu {
                width: 360px;
            }

            .settings-content {
                padding: 0 40px;
            }
        }

        /* 优化卡片内部的间距 */
        .el-card__header {
            padding: 16px 20px;
        }

        .el-card__body {
            padding: 20px;
        }

        /* 优化表单元素宽度利用 */
        .el-form-item {
            margin-bottom: 20px;
        }

        .el-form-item__content {
            max-width: 100%;
        }

        /* 优化输入框宽度 */
        .el-input, .el-textarea, .el-select {
            width: 100%;
            max-width: 600px; /* 限制最大宽度避免过宽 */
        }

        /* 优化开关和按钮的布局 */
        .el-switch {
            margin-right: 12px;
        }

        /* 优化表单标签宽度 */
        .el-form-item__label {
            min-width: 120px;
            text-align: right;
            padding-right: 12px;
        }

        /* 优化按钮组的间距 */
        .header-actions .el-button {
            margin-left: 8px;
        }

        /* 优化特定表单元素 */
        .email-input-container {
            width: 100%;
            max-width: 600px;
        }

        /* 优化文本域 */
        .el-textarea__inner {
            min-height: 100px;
            resize: vertical;
        }

        /* 优化数字输入框 */
        .el-input-number {
            width: 200px;
        }

        /* 优化选择器 */
        .el-select .el-input {
            width: 100%;
        }

        /* 优化表单布局在大屏幕上的表现 */
        @media (min-width: 1200px) {
            .el-form-item__label {
                min-width: 140px;
            }

            .el-input, .el-textarea, .el-select {
                max-width: 800px;
            }
        }

        /* 确保内容区域充分利用空间 */
        .settings-container {
            width: 100%;
            max-width: 100%;
        }

        /* 优化卡片在大屏幕上的显示 */
        .el-card {
            margin-bottom: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        /* 优化表单项的间距和布局 */
        .el-form-item__content > * {
            width: 100%;
        }

        /* 确保按钮不会过宽 */
        .el-button {
            max-width: 200px;
        }

        /* 优化开关控件的显示 */
        .el-form-item .el-switch {
            margin-top: 4px;
        }

        .header-actions .el-button:first-child {
            margin-left: 0;
        }

        /* 通用响应式优化 */

        /* 触摸设备优化 */
        @media (hover: none) and (pointer: coarse) {
            .el-button, .el-menu-item, .el-switch {
                min-height: 44px; /* iOS推荐的最小触摸区域 */
            }

            .el-input__inner, .el-textarea__inner {
                font-size: 16px; /* 防止iOS缩放 */
            }

            .settings-menu .el-menu-item {
                padding: 12px 16px;
                line-height: 1.4;
            }
        }

        /* 高分辨率屏幕优化 */
        @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
            .settings-card {
                border-width: 0.5px;
            }

            .el-input__inner, .el-textarea__inner {
                border-width: 0.5px;
            }
        }

        /* 减少动画以提升性能 */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* 暗色模式支持 */
        @media (prefers-color-scheme: dark) {
            .settings-card {
                background: rgba(30, 41, 59, 0.95);
                border-color: rgba(255, 255, 255, 0.1);
                color: #e2e8f0;
            }

            .el-input__inner, .el-textarea__inner {
                background: rgba(51, 65, 85, 0.8);
                border-color: rgba(255, 255, 255, 0.2);
                color: #e2e8f0;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <el-container>
            <el-header height="64px">
                <div class="el-header-inner">
                    <div class="header-title">客服系统设置</div>
                    <div>
                        <a href="javascript:void(0)" class="header-back" @click="handleGoBack">
                            返回
                        </a>
                    </div>
                </div>
            </el-header>
            
            <el-main>
                <div class="container">
                    <!-- 设置容器 -->
                    <div class="settings-container">
                        <!-- 左侧导航菜单 -->
                        <div class="settings-menu">
                            <el-menu
                                :default-active="activeMenu"
                                @select="handleMenuSelect"
                                class="settings-menu-vertical">
                                <el-menu-item index="basic">
                                    <i class="el-icon-setting"></i>
                                    <span>基本设置</span>
                                </el-menu-item>
                                <el-menu-item index="preset">
                                    <i class="el-icon-chat-dot-square"></i>
                                    <span>预设回复设置</span>
                                </el-menu-item>
                                <el-menu-item index="quick">
                                    <i class="el-icon-chat-line-square"></i>
                                    <span>快速回复设置</span>
                                </el-menu-item>
                                <el-menu-item index="qa">
                                    <i class="el-icon-chat-round"></i>
                                    <span>问答回复设置</span>
                                </el-menu-item>
                                <el-menu-item index="faq">
                                    <i class="el-icon-question"></i>
                                    <span>常见问题配置</span>
                                </el-menu-item>
                            </el-menu>
                        </div>
                        
                        <!-- 右侧内容区域 -->
                        <div class="settings-content">
                            <el-form :model="params" label-width="120px" size="default" v-loading="loading">
                                <!-- 基本设置 -->
                                <div v-show="activeMenu === 'basic'">
                                    <el-card class="settings-card">
                                        <template #header>
                                            <div class="settings-header">
                                                <h2>基本设置</h2>
                                                <div class="header-actions">
                                                    <el-button size="small" @click="resetSettings" :loading="loading">重置设置</el-button>
                                                    <el-button size="small" type="primary" @click="saveSettings" :loading="saving">保存设置</el-button>
                                                </div>
                                            </div>
                                        </template>
                                        
                                        <el-form-item label="欢迎消息">
                                            <el-input type="textarea" v-model="params.chat_description" :rows="3" placeholder="客户进入聊天时显示的欢迎消息"></el-input>
                                        </el-form-item>
                                        
                                        <el-form-item label="功能开关">
                                            <el-switch v-model="params.settings.chat_enabled" :true-value="true" :false-value="false"></el-switch>
                                            <span style="margin-left: 8px">启用客服聊天</span>
                                        </el-form-item>
                                        
                                        <el-form-item label="文件上传">
                                            <el-switch v-model="params.settings.file_upload_enabled" :true-value="true" :false-value="false"></el-switch>
                                            <span style="margin-left: 8px">允许文件上传</span>
                                        </el-form-item>
                                        
                                        <el-form-item label="消息通知">
                                            <el-switch v-model="params.settings.notification_enabled" :true-value="true" :false-value="false"></el-switch>
                                            <span style="margin-left: 8px">启用消息通知</span>
                                        </el-form-item>

                                        <el-form-item label="平台客服邮箱">
                                            <div class="email-input-container">
                                                <el-input
                                                    v-model="params.notification_config.platform_service_email"
                                                    placeholder="请输入平台客服邮箱地址"
                                                    style="width: 100%; max-width: 450px;">
                                                    <template #prefix>
                                                        <i class="el-icon-message" style="color: #667eea;"></i>
                                                    </template>
                                                </el-input>
                                                <div class="form-tip">
                                                    <i class="el-icon-info" style="color: #667eea; margin-right: 6px;"></i>
                                                    当用户选择"平台客服"咨询时，系统会向此邮箱发送通知
                                                </div>
                                            </div>
                                        </el-form-item>

                                        <el-form-item label="离线留言">
                                            <el-switch v-model="params.settings.offline_message_enabled" :true-value="true" :false-value="false"></el-switch>
                                            <span style="margin-left: 8px">启用离线留言</span>
                                        </el-form-item>
                                        
                                        <el-form-item label="联系字段显示">
                                            <div class="contact-field-selector">
                                                <div class="selector-header">
                                                    <i class="el-icon-info" style="color: #667eea; margin-right: 8px;"></i>
                                                    <span>请选择一种联系方式显示在聊天表单中</span>
                                                </div>
                                                <el-radio-group v-model="params.settings.contact_field_type" class="contact-radio-group">
                                                    <div class="radio-option">
                                                        <el-radio label="phone">
                                                            <div class="radio-content">
                                                                <i class="el-icon-phone radio-icon"></i>
                                                                <span class="radio-text">显示电话输入框</span>
                                                            </div>
                                                        </el-radio>
                                                    </div>
                                                    <div class="radio-option">
                                                        <el-radio label="qq">
                                                            <div class="radio-content">
                                                                <i class="el-icon-chat-dot-square radio-icon"></i>
                                                                <span class="radio-text">显示QQ输入框</span>
                                                            </div>
                                                        </el-radio>
                                                    </div>
                                                    <div class="radio-option">
                                                        <el-radio label="wechat">
                                                            <div class="radio-content">
                                                                <i class="el-icon-chat-line-square radio-icon"></i>
                                                                <span class="radio-text">显示微信输入框</span>
                                                            </div>
                                                        </el-radio>
                                                    </div>
                                                </el-radio-group>
                                            </div>
                                        </el-form-item>
                                        
                                        <el-form-item label="会话数量限制">
                                            <el-input-number v-model="params.settings.max_active_sessions" :min="1" :max="10" :step="1" size="small" style="width: 120px;"></el-input-number>
                                            <span style="margin-left: 8px">每个联系人(邮箱/电话)最多可创建的活跃会话数</span>
                                        </el-form-item>
                                        
                                        <el-form-item label="前台JS">
                                            <div style="display: flex; align-items: center;">
                                                <el-switch v-model="params.settings.qiantai_js_enabled" :true-value="true" :false-value="false" v-if="params.settings"></el-switch>
                                                <el-switch v-model="tempSwitchState" disabled v-else></el-switch>
                                                <span style="margin-left: 8px">启用前台JS脚本</span>
                                                <el-button type="primary" size="small" style="margin-left: 16px" @click="toggleQiantaiJs">
                                                    {{ params.settings && params.settings.qiantai_js_enabled ? '禁用前台JS' : '启用前台JS' }}
                                                </el-button>
                                                <el-tooltip content="启用时将复制static/qiantai.js到/assets/plugin/Customersystem/目录，并在chat/index.html中引入" placement="top">
                                                    <i class="el-icon-question" style="margin-left: 8px; color: #909399;"></i>
                                                </el-tooltip>
                                            </div>
                                        </el-form-item>
                                    </el-card>
                                </div>
                                
                                <!-- 预设回复设置 -->
                                <div v-show="activeMenu === 'preset'">
                                    <el-card class="settings-card">
                                        <template #header>
                                            <div class="settings-header">
                                                <h2>预设回复设置</h2>
                                                <div class="header-actions">
                                                    <el-button size="small" @click="resetSettings" :loading="loading">重置设置</el-button>
                                                    <el-button size="small" type="primary" @click="saveSettings" :loading="saving">保存设置</el-button>
                                                </div>
                                            </div>
                                        </template>
                                        
                                        <el-form-item label="启用预设回复">
                                            <el-switch v-model="params.preset_replies.enabled" :true-value="true" :false-value="false"></el-switch>
                                            <span style="margin-left: 8px">显示预设回复按钮</span>
                                        </el-form-item>
                                        
                                        <el-form-item label="预设回复标题">
                                            <el-input v-model="params.preset_replies.title" placeholder="例如：以下是常见问题，您看下是否可以帮助到您呢？"></el-input>
                                        </el-form-item>

                                        <el-form-item label="预设问题标题">
                                            <el-input v-model="params.preset_questions.title" placeholder="例如：常见问题"></el-input>
                                        </el-form-item>
                                        
                                        <el-form-item label="预设问题描述">
                                            <el-input type="textarea" v-model="params.preset_questions.description" :rows="2" placeholder="例如：以下是用户经常咨询的问题，点击即可直接提问"></el-input>
                                        </el-form-item>
                                        
                                        <el-form-item label="预设回复内容">
                                            <div v-for="(reply, index) in params.preset_replies.items" :key="index" class="preset-reply-item">
                                                <div class="preset-reply-header">
                                                    <el-input v-model="reply.label" placeholder="按钮标签文字" style="width: 200px;"></el-input>
                                                    <el-button type="danger" size="small" @click="removePresetReply(index)">
                                                        删除
                                                    </el-button>
                                                </div>
                                                <el-input v-model="reply.content" type="textarea" placeholder="回复内容" :rows="3"></el-input>
                                            </div>
                                            
                                            <div style="text-align: center; margin-top: 20px;">
                                                <el-button type="primary" @click="addPresetReply">
                                                    添加预设回复
                                                </el-button>
                                            </div>
                                        </el-form-item>
                                        

                                    </el-card>
                                </div>
                                
                                <!-- 快速回复设置 -->
                                <div v-show="activeMenu === 'quick'">
                                    <el-card class="settings-card">
                                        <template #header>
                                            <div class="settings-header">
                                                <h2>快速回复设置</h2>
                                                <div class="header-actions">
                                                    <el-button size="small" @click="resetSettings" :loading="loading">重置设置</el-button>
                                                    <el-button size="small" type="primary" @click="saveSettings" :loading="saving">保存设置</el-button>
                                                </div>
                                            </div>
                                        </template>
                                        
                                        <el-form-item label="启用快速回复">
                                            <el-switch v-model="params.quick_replies.enabled" :true-value="true" :false-value="false"></el-switch>
                                            <span style="margin-left: 8px">显示快速回复按钮</span>
                                        </el-form-item>
                                        
                                        <el-form-item label="快速回复内容">
                                            <div v-for="(reply, index) in params.quick_replies.items" :key="index" class="preset-reply-item">
                                                <div class="preset-reply-header">
                                                    <el-input v-model="reply.label" placeholder="按钮标签文字" style="width: 200px;"></el-input>
                                                    <el-button type="danger" size="small" @click="removeQuickReply(index)">
                                                        删除
                                                    </el-button>
                                                </div>
                                                <el-input v-model="reply.content" type="textarea" placeholder="回复内容" :rows="3"></el-input>
                                            </div>
                                            
                                            <div style="text-align: center; margin-top: 20px;">
                                                <el-button type="primary" @click="addQuickReply">
                                                    添加快速回复
                                                </el-button>
                                            </div>
                                        </el-form-item>
                                    </el-card>
                                </div>
                                
                                <!-- 问答回复设置 -->
                                <div v-show="activeMenu === 'qa'">
                                    <el-card class="settings-card">
                                        <template #header>
                                            <div class="settings-header">
                                                <h2>问答回复设置</h2>
                                                <div class="header-actions">
                                                    <el-button size="small" @click="resetSettings" :loading="loading">重置设置</el-button>
                                                    <el-button size="small" type="primary" @click="saveSettings" :loading="saving">保存设置</el-button>
                                                </div>
                                            </div>
                                        </template>
                                        
                                        <el-form-item label="启用问答回复">
                                            <el-switch v-model="params.qa_replies.enabled" :true-value="true" :false-value="false"></el-switch>
                                            <span style="margin-left: 8px">显示问答回复功能</span>
                                        </el-form-item>
                                        
                                        <el-form-item label="问答标题">
                                            <el-input v-model="params.qa_replies.title" placeholder="例如：智能问答"></el-input>
                                        </el-form-item>
                                        
                                        <el-form-item label="问答说明">
                                            <el-input type="textarea" v-model="params.qa_replies.description" :rows="2" placeholder="例如：输入您的问题，系统会智能匹配回复"></el-input>
                                        </el-form-item>
                                        
                                        <el-form-item label="问答内容配置">
                                            <div style="background-color: #f0f9eb; border: 1px solid #e1f3d8; border-radius: 8px; padding: 12px; margin-bottom: 15px; box-shadow: 0 2px 6px rgba(0,0,0,0.05);">
                                                <p style="color: #529b2e; margin: 0; font-size: 14px; font-weight: 600; display: flex; align-items: center;">
                                                    <i class="el-icon-info-circle" style="margin-right: 8px; font-size: 18px;"></i>
                                                    问答回复功能会根据用户输入的问题，自动匹配最相似的问题并返回对应答案。
                                                </p>
                                            </div>
                                            
                                            <div v-for="(qa, index) in params.qa_replies.items" :key="index" class="preset-reply-item">
                                                <div class="preset-reply-header">
                                                    <el-input v-model="qa.question" placeholder="问题" style="width: 200px;"></el-input>
                                                    <el-button type="danger" size="small" @click="removeQaReply(index)">
                                                        删除
                                                    </el-button>
                                                </div>
                                                <el-input v-model="qa.answer" type="textarea" placeholder="回答内容" :rows="3"></el-input>
                                                <div style="margin-top: 8px;">
                                                    <el-input v-model="qa.keywords" placeholder="关键词（多个关键词用逗号分隔，用于提高匹配精度）"></el-input>
                                                </div>
                                            </div>
                                            
                                            <div style="text-align: center; margin-top: 20px;">
                                                <el-button type="primary" @click="addQaReply">
                                                    添加问答对
                                                </el-button>
                                            </div>
                                        </el-form-item>
                                        
                                        <el-form-item label="相似度阈值">
                                            <el-slider v-model="params.qa_replies.threshold" :step="1" :min="0" :max="100">
                                                <template #default="{value}">
                                                    <span style="color: #3b82f6;">{{ value }}%</span>
                                                </template>
                                            </el-slider>
                                            <div style="color: #64748b; font-size: 12px; margin-top: 5px;">
                                                设置问题匹配的相似度阈值，建议设置为75-85，值越高匹配越精准但可能导致部分问题无法匹配
                                            </div>
                                        </el-form-item>
                                    </el-card>
                                </div>
                                
                                <!-- 常见问题配置 -->
                                <div v-show="activeMenu === 'faq'">
                                    <el-card class="settings-card">
                                        <template #header>
                                            <div class="settings-header">
                                                <h2>常见问题配置</h2>
                                                <div class="header-actions">
                                                    <el-button size="small" @click="resetSettings" :loading="loading">重置设置</el-button>
                                                    <el-button size="small" type="primary" @click="saveSettings" :loading="saving">保存设置</el-button>
                                                </div>
                                            </div>
                                        </template>
                                        
                                        <el-form-item label="启用FAQ">
                                            <el-switch v-model="params.faq_config.enabled"></el-switch>
                                            <span style="margin-left: 8px">显示常见问题</span>
                                        </el-form-item>
                                        
                                        <el-form-item label="FAQ分类">
                                            <div v-for="(category, index) in params.faq_config.categories" :key="index" style="margin-bottom: 16px; padding: 12px; border: 1px solid #e2e8f0; border-radius: 8px; background-color: #f8fafc;">
                                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px; border-bottom: 1px dashed #e2e8f0; padding-bottom: 8px;">
                                                    <el-input v-model="category.name" placeholder="分类名称" style="width: 200px;"></el-input>
                                                    <el-button type="danger" size="small" @click="removeCategory(index)">
                                                        删除分类
                                                    </el-button>
                                                </div>

                                                <div v-for="(question, qIndex) in category.questions" :key="qIndex" style="margin-bottom: 12px; background: #ffffff; padding: 12px; border-radius: 6px; border: 1px solid #e2e8f0;">
                                                    <el-input v-model="question.question" placeholder="问题" style="margin-bottom: 8px;"></el-input>
                                                    <el-input v-model="question.answer" type="textarea" placeholder="回答" :rows="2"></el-input>
                                                    <div style="text-align: right; margin-top: 6px;">
                                                        <el-button type="danger" size="small" @click="removeQuestion(category, qIndex)">
                                                            删除问题
                                                        </el-button>
                                                    </div>
                                                </div>

                                                <div style="text-align: center; margin-top: 12px;">
                                                    <el-button type="primary" size="small" @click="addQuestion(category)">
                                                        添加问题
                                                    </el-button>
                                                </div>
                                            </div>

                                            <div style="text-align: center; margin-top: 16px;">
                                                <el-button type="primary" @click="addCategory">
                                                    添加分类
                                                </el-button>
                                            </div>
                                        </el-form-item>
                                    </el-card>
                                </div>
                            </el-form>
                        </div>
                    </div>
                </div>
            </el-main>
        </el-container>
    </div>
    
    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>
    
    <script>
        const { createApp, ref, watch, onMounted } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;
        
        document.documentElement.style.setProperty('--el-transition-duration', '0.3s');
        document.documentElement.style.setProperty('--el-border-color', '#dcdfe6');
        document.documentElement.style.setProperty('--el-color-white', '#ffffff');
        
        const App = {
            setup() {
                const params = ref({
                    chat_description: '',
                    settings: {
                        chat_enabled: true,
                        file_upload_enabled: true,
                        notification_enabled: true,
                        offline_message_enabled: true,
                        max_active_sessions: 2,
                        qiantai_js_enabled: false
                    },
                    emoji_enabled: true,
                    upload_config: {
                        max_file_size: 5242880,
                        allowed_extensions: ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx'],
                        upload_api: '/shopApi/Upload/file'
                    },
                    staff_config: {
                        max_concurrent_chats: 5,
                        auto_allocation: true,
                        chat_timeout: 1800
                    },
                    session_config: {
                        save_history: true,
                        history_days: 30
                    },
                    visitor_info: {
                        name_required: true,
                        email_required: true,
                        phone_required: false,
                        question_required: true
                    },
                    ui_config: {
                        theme_color: '#41b883',
                        secondary_color: '#348ceb',
                        chat_position: 'right',
                        window_height: '500px',
                        window_width: '360px',
                        button_text: '联系客服',
                        header_text: '在线客服',
                        placeholder_text: '请输入您的问题...',
                        send_button_text: '发送',
                        timestamp_format: 'Y-m-d H:i:s'
                    },
                    notification_config: {
                        sound_enabled: true,
                        browser_notification: true,
                        email_notification: true,
                        email_template: '您有一条新的客服消息，请登录系统查看。',
                        new_message_sound: '/static/plugins/Customersystem/sounds/notification.mp3',
                        platform_service_email: ''
                    },
                    analytics_config: {
                        track_chat_duration: true,
                        track_response_time: true,
                        track_satisfaction: true,
                        satisfaction_options: [
                            '非常满意',
                            '满意',
                            '一般',
                            '不满意',
                            '非常不满意'
                        ]
                    },
                    preset_replies: {
                        enabled: true,
                        title: '以下是常见问题，您看下是否可以帮助到您呢？',
                        items: []
                    },
                    faq_config: {
                        enabled: true,
                        categories: []
                    },
                    quick_replies: {
                        enabled: true,
                        items: []
                    },
                    preset_questions: {
                        title: '',
                        description: '以下是用户经常咨询的问题，点击即可直接提问'
                    },
                    qa_replies: {
                        enabled: true,
                        title: '智能问答',
                        description: '输入您的问题，系统会智能匹配回复',
                        threshold: 75,
                        items: []
                    },
                    features: {
                        chat_enabled: true,
                        file_upload_enabled: true,
                        emoji_enabled: true,
                        faq_enabled: true,
                        rating_enabled: true,
                        captcha_enabled: false
                    }
                });
                
                const originalParams = ref(null);
                const loading = ref(false);
                const saving = ref(false);
                const uploadMaxSize = ref(5);
                const tempSwitchState = ref(false);
                
                // 添加导航菜单相关状态
                const activeMenu = ref('basic');
                
                // 菜单选择处理函数
                const handleMenuSelect = (index) => {
                    activeMenu.value = index;
                };
                
                // 监听文件大小变化
                watch(uploadMaxSize, (newVal) => {
                    // 将 MB 转换为字节
                    params.value.upload_config.max_file_size = newVal * 1024 * 1024;
                });
                
                // 获取参数
                const getParams = () => {
                    loading.value = true;
                    axios.post('/plugin/Customersystem/api/getParams')
                        .then(response => {
                            if (response.data.code === 200) {
                                const data = response.data.data;
                                
                                // 确保settings对象存在
                                if (!data.settings) {
                                    data.settings = {};
                                }
                                
                                // 确保qiantai_js_enabled有默认值
                                if (typeof data.settings.qiantai_js_enabled === 'undefined') {
                                    data.settings.qiantai_js_enabled = false;
                                }
                                
                                // 确保quick_replies对象存在
                                if (!data.quick_replies) {
                                    data.quick_replies = {
                                        enabled: true,
                                        items: []
                                    };
                                } else if (!data.quick_replies.items) {
                                    data.quick_replies.items = [];
                                }
                                
                                // 确保preset_questions对象存在
                                if (!data.preset_questions) {
                                    data.preset_questions = {
                                        title: '常见问题',
                                        description: '以下是用户经常咨询的问题，点击即可直接提问'
                                    };
                                }
                                
                                // 确保所有开关都是布尔值
                                const booleanKeys = [
                                    'chat_enabled',
                                    'file_upload_enabled',
                                    'notification_enabled',
                                    'offline_message_enabled',
                                    'qiantai_js_enabled'
                                ];
                                
                                booleanKeys.forEach(key => {
                                    if (typeof data.settings[key] !== 'undefined') {
                                        data.settings[key] = Boolean(data.settings[key]);
                                    }
                                });
                                
                                params.value = data;
                                
                                // 将文件大小 (字节 -> MB)
                                uploadMaxSize.value = Math.round(params.value.upload_config.max_file_size / 1024 / 1024);
                                
                                // 保存原始参数用于重置
                                originalParams.value = JSON.parse(JSON.stringify(params.value));
                            } else {
                                ElMessage.error(response.data.msg || '获取参数失败');
                            }
                        })
                        .catch(error => {

                            ElMessage.error('获取参数失败，请稍后重试');
                        })
                        .finally(() => {
                            loading.value = false;
                        });
                };
                
                // 保存设置
                const saveSettings = () => {
                    saving.value = true;
                    
                    // 创建副本以避免修改原始数据
                    const saveData = JSON.parse(JSON.stringify(params.value));
                    
                    axios.post('/plugin/Customersystem/api/saveParams', saveData)
                        .then(response => {
                            if (response.data.code === 200) {
                                ElMessage.success('设置保存成功');
                                // 更新原始参数
                                originalParams.value = JSON.parse(JSON.stringify(params.value));
                            } else {
                                ElMessage.error(response.data.msg || '保存设置失败');
                            }
                        })
                        .catch(error => {

                            ElMessage.error('保存设置失败，请稍后重试');
                        })
                        .finally(() => {
                            saving.value = false;
                        });
                };
                
                // 重置设置
                const resetSettings = () => {
                    ElMessageBox.confirm('确定要重置所有设置吗？这将恢复到最后保存的状态。', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        if (originalParams.value) {
                            params.value = JSON.parse(JSON.stringify(originalParams.value));
                            
                            // 确保重置后preset_questions对象存在
                            if (!params.value.preset_questions) {
                                params.value.preset_questions = {
                                    title: '常见问题',
                                    description: '以下是用户经常咨询的问题，点击即可直接提问'
                                };
                            }
                            
                            uploadMaxSize.value = Math.round(params.value.upload_config.max_file_size / 1024 / 1024);
                            ElMessage.success('设置已重置');
                        } else {
                            getParams();
                        }
                    }).catch(() => {});
                };
                
                // 添加预设回复
                const addPresetReply = () => {
                    if (!params.value.preset_replies) {
                        params.value.preset_replies = {
                            enabled: true,
                            title: '以下是常见问题，您看下是否可以帮助到您呢？',
                            items: []
                        };
                    }
                    
                    if (!params.value.preset_replies.items) {
                        params.value.preset_replies.items = [];
                    }
                    
                    params.value.preset_replies.items.push({
                        label: '',
                        content: ''
                    });
                };
                
                // 删除预设回复
                const removePresetReply = (index) => {
                    ElMessageBox.confirm('确定要删除这个预设回复吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        params.value.preset_replies.items.splice(index, 1);
                        ElMessage.success('预设回复已删除');
                    }).catch(() => {});
                };
                
                // 添加快速回复
                const addQuickReply = () => {
                    if (!params.value.quick_replies) {
                        params.value.quick_replies = {
                            enabled: true,
                            items: []
                        };
                    }
                    
                    if (!params.value.quick_replies.items) {
                        params.value.quick_replies.items = [];
                    }
                    
                    params.value.quick_replies.items.push({
                        label: '',
                        content: ''
                    });
                };
                
                // 删除快速回复
                const removeQuickReply = (index) => {
                    ElMessageBox.confirm('确定要删除这个快速回复吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        params.value.quick_replies.items.splice(index, 1);
                        ElMessage.success('快速回复已删除');
                    }).catch(() => {});
                };
                
                // 添加分类
                const addCategory = () => {
                    if (!params.value.faq_config.categories) {
                        params.value.faq_config.categories = [];
                    }
                    
                    params.value.faq_config.categories.push({
                        id: 'category_' + Date.now(),
                        name: '新分类',
                        questions: []
                    });
                };
                
                // 删除分类
                const removeCategory = (index) => {
                    ElMessageBox.confirm('确定要删除这个分类吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        params.value.faq_config.categories.splice(index, 1);
                    }).catch(() => {});
                };
                
                // 添加问题
                const addQuestion = (category) => {
                    if (!category.questions) {
                        category.questions = [];
                    }
                    
                    category.questions.push({
                        question: '',
                        answer: ''
                    });
                };
                
                // 删除问题
                const removeQuestion = (category, index) => {
                    category.questions.splice(index, 1);
                };
                
                // 添加问答回复
                const addQaReply = () => {
                    if (!params.value.qa_replies) {
                        params.value.qa_replies = {
                            enabled: true,
                            title: '智能问答',
                            description: '输入您的问题，系统会智能匹配回复',
                            threshold: 75,
                            items: []
                        };
                    }
                    
                    if (!params.value.qa_replies.items) {
                        params.value.qa_replies.items = [];
                    }
                    
                    params.value.qa_replies.items.push({
                        question: '',
                        answer: '',
                        keywords: ''
                    });
                };
                
                // 删除问答回复
                const removeQaReply = (index) => {
                    ElMessageBox.confirm('确定要删除这个问答对吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        params.value.qa_replies.items.splice(index, 1);
                        ElMessage.success('问答对已删除');
                    }).catch(() => {});
                };
                
                // 返回上一页
                const handleGoBack = () => {
                    // 清理可能影响index.html的临时数据
                    try {
                        // 清除可能影响对话框全屏模式的相关类和状态
                        document.querySelectorAll('.el-dialog').forEach(dialog => {
                            dialog.classList.remove('is-fullscreen');
                        });
                        
                        // 清除本地存储中可能的高度设置
                        localStorage.removeItem('chat_container_height');
                        localStorage.removeItem('dialog_fullscreen_state');
                    } catch (e) {

                    }
                    
                    // 设置一个标记，表示是从设置页面返回的
                    sessionStorage.setItem('returning_from_settings', 'true');
                    
                    // 返回上一页
                    history.back();
                };
                
                // 切换前台JS功能
                const toggleQiantaiJs = () => {
                    // 确保settings对象存在
                    if (!params.value.settings) {
                        params.value.settings = {};
                    }
                    
                    // 确保qiantai_js_enabled存在，初始为false
                    if (typeof params.value.settings.qiantai_js_enabled === 'undefined') {
                        params.value.settings.qiantai_js_enabled = false;
                    }
                    
                    const enabled = !params.value.settings.qiantai_js_enabled;
                    
                    // 设置loading状态
                    loading.value = true;
                    
                    // 调用后端API
                    axios.post('/plugin/Customersystem/api/toggleQiantaiJs', { enabled })
                        .then(response => {
                            if (response.data.code === 200) {
                                // 更新状态
                                params.value.settings.qiantai_js_enabled = enabled;
                                
                                // 显示成功提示
                                ElMessage.success(enabled ? 'JS脚本已启用' : 'JS脚本已禁用');
                            } else {
                                ElMessage.error(response.data.msg || '操作失败');
                            }
                        })
                        .catch(error => {

                            ElMessage.error('操作失败，请稍后重试');
                        })
                        .finally(() => {
                            loading.value = false;
                        });
                };
                
                onMounted(() => {
                    // 确保params.value已初始化
                    if (!params.value) {
                        params.value = {
                            settings: {
                                chat_enabled: true,
                                file_upload_enabled: true,
                                notification_enabled: true,
                                offline_message_enabled: true,
                                max_active_sessions: 2,
                                qiantai_js_enabled: false
                            },
                            preset_replies: { enabled: true, items: [] },
                            quick_replies: { enabled: true, items: [] },
                            faq_config: { enabled: true, categories: [] },
                            qa_replies: { enabled: true, items: [] }
                        };
                    } else {
                        // 在获取参数前先初始化可能的数组，以避免length错误
                        if (!params.value.preset_replies) {
                            params.value.preset_replies = { enabled: true, items: [] };
                        } else if (!params.value.preset_replies.items) {
                            params.value.preset_replies.items = [];
                        }
                        
                        if (!params.value.quick_replies) {
                            params.value.quick_replies = { enabled: true, items: [] };
                        } else if (!params.value.quick_replies.items) {
                            params.value.quick_replies.items = [];
                        }
                        
                        if (!params.value.faq_config) {
                            params.value.faq_config = { enabled: true, categories: [] };
                        } else if (!params.value.faq_config.categories) {
                            params.value.faq_config.categories = [];
                        }
                        
                        if (!params.value.qa_replies) {
                            params.value.qa_replies = { enabled: true, items: [] };
                        } else if (!params.value.qa_replies.items) {
                            params.value.qa_replies.items = [];
                        }
                    }
                    
                    getParams();
                });
                
                return {
                    params,
                    loading,
                    saving,
                    uploadMaxSize,
                    tempSwitchState,
                    activeMenu,
                    handleMenuSelect,
                    getParams,
                    saveSettings,
                    resetSettings,
                    addPresetReply,
                    removePresetReply,
                    addQuickReply,
                    removeQuickReply,
                    addQaReply,
                    removeQaReply,
                    addCategory,
                    removeCategory,
                    addQuestion,
                    removeQuestion,
                    handleGoBack,
                    toggleQiantaiJs
                };
            }
        };
        
        const app = createApp(App);
        app.use(ElementPlus, {
            locale: ElementPlusLocaleZhCn
        });
        app.mount('#app');
    </script>
</body>
</html> 