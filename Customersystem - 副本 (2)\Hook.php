<?php

namespace plugin\Customersystem;

use think\facade\Db;
use think\facade\Log;
use think\facade\Cache;

class Hook
{
    /**
     * 主要Hook处理方法
     * 用于将JS文件加入页面并实现通话实时显示
     * 
     * @param array $js JS文件数组
     * @return void
     */
    public function handle(&$array)
    {
        $user = $array[0];   
        $array[1][] = plugstatic("Customersystem", 'qiantai.js');
    }
    
    /**
     * 缓存活跃会话数据
     * 减少频繁查询数据库的需求
     * 
     * @return void
     */
    protected function cacheActiveSessions()
    {
        try {
            // 获取所有活跃会话
            $activeSessions = Db::name('plugin_chat_sessions')
                ->alias('s')
                ->join('plugin_chat_contacts c', 'c.id = s.contact_id')
                ->field('s.id, s.title, s.status, s.last_message, s.last_time, s.unread_count, s.staff_id, c.name as contact_name, c.avatar')
                ->where('s.status', 1)
                ->order('s.last_time', 'desc')
                ->select()
                ->toArray();
            
            if (!empty($activeSessions)) {
                // 缓存活跃会话数据，设置较短的过期时间以保证数据相对实时
                // 使用会话数量作为缓存键的一部分，便于判断是否有新会话
                $sessionCount = count($activeSessions);
                Cache::set('customersystem_active_sessions', $activeSessions, 2); // 缓存2秒
                Cache::set('customersystem_session_count', $sessionCount, 2);
                
                // 为每个活跃会话缓存最近的消息
                foreach ($activeSessions as $session) {
                    $this->cacheSessionMessages($session['id']);
                }
                
                Log::info("Customersystem实时缓存：已更新" . count($activeSessions) . "个活跃会话");
            }
        } catch (\Exception $e) {
            Log::error("Customersystem实时缓存失败：" . $e->getMessage());
        }
    }
    
    /**
     * 缓存会话消息，确保已读状态正确处理
     * 
     * @param int $sessionId 会话ID
     * @return void
     */
    protected function cacheSessionMessages($sessionId)
    {
        try {
            // 获取会话最近的30条消息，增加数量以确保更多历史记录可用
            $messages = Db::name('plugin_chat_messages')
                ->where('session_id', $sessionId)
                ->order('create_time', 'desc')
                ->limit(30)
                ->select()
                ->toArray();
            
            if (!empty($messages)) {
                // 反转消息顺序，使其按时间顺序排列
                $messages = array_reverse($messages);
                
                // 记录消息发送者类型统计
                $staffCount = 0;
                $customerCount = 0;
                $readCount = 0;
                $unreadCount = 0;
                
                // 将消息ID转为整数以确保正确比较
                foreach ($messages as &$message) {
                    if (isset($message['id'])) {
                        $message['id'] = intval($message['id']);
                    }
                    // 确保时间戳是整数
                    if (isset($message['create_time'])) {
                        $message['create_time'] = intval($message['create_time']);
                    }
                    if (isset($message['update_time'])) {
                        $message['update_time'] = intval($message['update_time']);
                    }
                    
                    // 确保已读状态和时间字段存在
                    if (!isset($message['is_read'])) {
                        $message['is_read'] = 0;
                    } else {
                        $message['is_read'] = intval($message['is_read']);
                    }
                    
                    if (!isset($message['read_time'])) {
                        $message['read_time'] = 0;
                    } else {
                        $message['read_time'] = intval($message['read_time']);
                    }
                    
                    // 统计已读/未读消息数量
                    if ($message['is_read'] == 1) {
                        $readCount++;
                    } else {
                        $unreadCount++;
                    }
                    
                    // 确保消息类型存在，默认为文本类型
                    if (empty($message['message_type'])) {
                        $message['message_type'] = 'text';
                    }
                    
                    // 统计消息类型
                    if (isset($message['sender_type'])) {
                        if ($message['sender_type'] == 'staff') {
                            $staffCount++;
                        } else {
                            $customerCount++;
                        }
                    }
                }
                
                // 记录消息类型统计
                Log::info("Customersystem缓存会话消息统计 [会话ID: {$sessionId}]：总计 " . count($messages) . " 条, 客服消息 {$staffCount} 条, 客户消息 {$customerCount} 条, 已读 {$readCount} 条, 未读 {$unreadCount} 条");
                
                // 缓存会话消息，缩短缓存时间以保证实时性
                Cache::set('customersystem_session_' . $sessionId . '_messages', $messages, 2); // 缓存2秒
                
                // 添加最后更新时间缓存，用于判断缓存是否需要更新
                Cache::set('customersystem_session_' . $sessionId . '_last_update', time(), 2);
            }
        } catch (\Exception $e) {
            Log::error("Customersystem缓存会话消息失败 [会话ID: {$sessionId}]：" . $e->getMessage());
        }
    }
    
    /**
     * 获取活跃会话数据
     * 供前端调用的接口方法
     * 
     * @return array 活跃会话数据
     */
    public function getActiveSessions()
    {
        // 尝试从缓存获取数据
        $sessions = Cache::get('customersystem_active_sessions');
        
        // 缓存未命中时，重新加载并缓存
        if (empty($sessions)) {
            $this->cacheActiveSessions();
            $sessions = Cache::get('customersystem_active_sessions') ?: [];
        }
        
        return $sessions;
    }
    
    /**
     * 获取指定会话的消息
     * 供前端调用的接口方法
     * 
     * @param int $sessionId 会话ID
     * @return array 会话消息
     */
    public function getSessionMessages($sessionId)
    {
        // 始终先检查数据库是否有新消息
        $lastUpdate = Cache::get('customersystem_session_' . $sessionId . '_last_update', 0);
        $currentTime = time();
        
        // 如果上次更新时间超过3秒，则强制更新缓存
        if ($currentTime - $lastUpdate > 3) {
            $this->cacheSessionMessages($sessionId);
        }
        
        // 从缓存获取数据
        $messages = Cache::get('customersystem_session_' . $sessionId . '_messages');
        
        // 缓存未命中时，重新加载该会话消息并缓存
        if (empty($messages)) {
            $this->cacheSessionMessages($sessionId);
            $messages = Cache::get('customersystem_session_' . $sessionId . '_messages') ?: [];
        }
        
        // 如果消息确实是最新的，从数据库获取最新的几条消息并与缓存合并
        $latestMessages = [];
        if (!empty($messages)) {
            $lastMessageId = max(array_column($messages, 'id'));
            
            // 获取比缓存中最新消息ID更大的消息
            $latestMessages = Db::name('plugin_chat_messages')
                ->where('session_id', $sessionId)
                ->where('id', '>', $lastMessageId)
                ->order('create_time', 'asc')
                ->select()
                ->toArray();
                
            // 确保消息ID和时间戳是整数
            foreach ($latestMessages as &$message) {
                if (isset($message['id'])) {
                    $message['id'] = intval($message['id']);
                }
                if (isset($message['create_time'])) {
                    $message['create_time'] = intval($message['create_time']);
                }
                if (isset($message['update_time'])) {
                    $message['update_time'] = intval($message['update_time']);
                }
            }
            
            // 如果有新消息，更新缓存
            if (!empty($latestMessages)) {
                $messages = array_merge($messages, $latestMessages);
                Cache::set('customersystem_session_' . $sessionId . '_messages', $messages, 5);
                Cache::set('customersystem_session_' . $sessionId . '_last_update', time(), 5);
            }
        }
        
        return $messages;
    }
    
    /**
     * 消息发送后回调
     * 在消息发送后立即更新缓存以确保实时性
     * 
     * @param int $sessionId 会话ID
     * @param array $message 新消息数据
     * @return void
     */
    public function afterMessageSent($sessionId, $message = null)
    {
        try {
            // 立即清除缓存，强制下次读取从数据库获取
            $this->clearSessionCache($sessionId);
            
            // 如果提供了消息数据，将其直接添加到缓存
            if ($message && is_array($message)) {
                $cachedMessages = Cache::get('customersystem_session_' . $sessionId . '_messages', []);
                
                // 确保消息ID是整数
                if (isset($message['id'])) {
                    $message['id'] = intval($message['id']);
                }
                
                // 确保时间戳是整数
                if (isset($message['create_time'])) {
                    $message['create_time'] = intval($message['create_time']);
                }
                if (isset($message['update_time'])) {
                    $message['update_time'] = intval($message['update_time']);
                }
                
                // 确保消息类型存在，默认为文本类型
                if (empty($message['message_type'])) {
                    $message['message_type'] = 'text';
                }
                
                // 检查消息是否已存在
                $exists = false;
                foreach ($cachedMessages as $cachedMsg) {
                    if (isset($cachedMsg['id']) && isset($message['id']) && $cachedMsg['id'] === $message['id']) {
                        $exists = true;
                        break;
                    }
                }
                
                // 如果消息不存在，添加到缓存
                if (!$exists) {
                    $cachedMessages[] = $message;
                    
                    // 按时间排序
                    usort($cachedMessages, function($a, $b) {
                        return $a['create_time'] - $b['create_time'];
                    });
                    
                    // 更新缓存
                    Cache::set('customersystem_session_' . $sessionId . '_messages', $cachedMessages, 5);
                    Cache::set('customersystem_session_' . $sessionId . '_last_update', time(), 5);
                }
            }
            
            // 立即更新该会话的缓存（从数据库重新加载）
            $this->cacheSessionMessages($sessionId);
            
            // 同时更新会话信息
            $this->cacheActiveSessions();
            
            Log::info("Customersystem消息发送后缓存更新: 会话ID={$sessionId}");
        } catch (\Exception $e) {
            Log::error("Customersystem消息发送后缓存更新失败: " . $e->getMessage());
        }
        
        return true;
    }
    
    /**
     * 消息标记为已读后回调
     * 在消息状态变更后立即更新缓存以确保实时性
     * 
     * @param int $sessionId 会话ID
     * @param int $messageId 消息ID
     * @return void
     */
    public function afterMessageRead($sessionId, $messageId = null)
    {
        try {
            // 立即清除缓存，强制下次读取从数据库获取
            $this->clearSessionCache($sessionId);
            
            // 如果指定了消息ID，直接更新缓存中该消息的已读状态
            if ($messageId) {
                $cachedMessages = Cache::get('customersystem_session_' . $sessionId . '_messages', []);
                $currentTime = time();
                
                $updated = false;
                
                foreach ($cachedMessages as &$message) {
                    if (isset($message['id']) && $message['id'] == $messageId) {
                        $message['is_read'] = 1;
                        $message['read_time'] = $currentTime;
                        $updated = true;
                    }
                }
                
                // 如果找到并更新了消息，更新缓存
                if ($updated) {
                    Cache::set('customersystem_session_' . $sessionId . '_messages', $cachedMessages, 5);
                    Log::info("Customersystem已读状态缓存更新: 会话ID={$sessionId}, 消息ID={$messageId}");
                }
            }
            
            // 立即更新该会话的缓存（从数据库重新加载）
            $this->cacheSessionMessages($sessionId);
            
            Log::info("Customersystem已读状态更新后缓存更新: 会话ID={$sessionId}");
        } catch (\Exception $e) {
            Log::error("Customersystem已读状态更新后缓存更新失败: " . $e->getMessage());
        }
        
        return true;
    }
    
    /**
     * 批量标记消息为已读后回调
     * 
     * @param int $sessionId 会话ID
     * @param string $roleType 角色类型
     * @return void
     */
    public function afterBatchMessageRead($sessionId, $roleType)
    {
        try {
            // 直接清除缓存，强制下次读取从数据库获取最新的已读状态
            $this->clearSessionCache($sessionId);
            
            // 立即更新该会话的缓存
            $this->cacheSessionMessages($sessionId);
            
            Log::info("Customersystem批量已读状态更新: 会话ID={$sessionId}, 角色类型={$roleType}");
        } catch (\Exception $e) {
            Log::error("Customersystem批量已读状态更新失败: " . $e->getMessage());
        }
        
        return true;
    }
    
    /**
     * 清除指定会话的缓存
     * 用于强制前端重新获取最新数据
     * 
     * @param int $sessionId 会话ID
     * @return void
     */
    public function clearSessionCache($sessionId)
    {
        try {
            Cache::delete('customersystem_session_' . $sessionId . '_messages');
            Cache::delete('customersystem_session_' . $sessionId . '_last_update');
            
            // 同时也清除活跃会话缓存
            Cache::delete('customersystem_active_sessions');
            Cache::delete('customersystem_session_count');
            
            Log::info("Customersystem清除会话缓存: 会话ID={$sessionId}");
        } catch (\Exception $e) {
            Log::error("Customersystem清除会话缓存失败: " . $e->getMessage());
        }
    }
} 