<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>操作面板</title>
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html,
        body {
            height: 100%;
            margin: 0;
            padding: 0;
        }

       .container {
            height: 100%;
            width: 100%;
            max-width: none;
            padding: 30px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

       .el-form-item {
            margin-bottom: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

       .el-button {
            margin: 5px;
            padding: 10px 20px;
            color: white;
            background-color: blue;
        }

       .el-tag {
            margin-left: 10px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
        }

       .el-input__inner {
            color: black;
        }

       .loading {
            margin-top: 25px;
        }

       .title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
        }
    </style>
</head>

<body>
    <div id="app" class="container">
        <h1 class="title">域名查询面板</h1>
        <span>注：后面有时间会搞自定义时长持续监测，目前不打算。</span>
        <el-card shadow="never">
            <el-form :model="form" label-width="auto">
                <el-form-item label="邮箱接收地址：">
                    <el-input v-model="form.recipientEmail" placeholder="请输入邮箱接收地址" />
                </el-form-item>
                <el-form-item label="要检测的域名：">
                    <el-input v-model="form.url" placeholder="请输入要检测的域名" />
                </el-form-item>
                <el-form-item>
                    <el-button @click="queryDomainStatus">查询域名状态</el-button>
                    <el-tag v-if="monitoringStatus!== null" :type="monitoringStatus === '正常'? 'success' : 'danger'">{{ monitoringStatus }}</el-tag>
                </el-form-item>
                <div v-if="isLoading" class="loading">
                    <el-spinner></el-spinner>
                </div>
            </el-form>
        </el-card>
    </div>
    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script type="module">
        const { ref, reactive } = Vue;
        const { ElMessage } = ElementPlus;

        const form = reactive({
            recipientEmail: '',
            url: '',
        });

        const monitoringStatus = ref(null);
        const isLoading = ref(false);

        const queryDomainStatus = async () => {
            isLoading.value = true;
            try {
                const url = form.url.trim();
                if (!url) {
                    ElMessage.error('请输入要检测的域名');
                    return;
                }

                const res = await axios.post("/plugin/Fengzeroemail/api/queryDomainStatus", {
                    url: url,
                });

                if (res.data.code === 200) {
                    monitoringStatus.value = res.data.data.status;
                    ElMessage.success('查询成功');
                    sendEmail(url, res.data.data.status, form.recipientEmail);
                } else {
                    monitoringStatus.value = null;
                    ElMessage.error(res.data.msg || '查询失败');
                }
            } catch (error) {
                monitoringStatus.value = null;
                ElMessage.error('查询失败：' + error.message);
            } finally {
                isLoading.value = false;
            }
        };

        const sendEmail = async (url, status, recipientEmail) => {
            try {
                const res = await axios.post("/plugin/Fengzeroemail/api/sendEmail", {
                    url: url,
                    status: status,
                    recipient_email: recipientEmail,
                });

                if (res.data.code === 200) {
                    ElMessage.success('邮件发送成功');
                } else {
                    ElMessage.error(res.data.msg || '邮件发送失败');
                }
            } catch (error) {
                ElMessage.error('邮件发送失败：' + error.message);
            }
        };

        const app = Vue.createApp({
            setup() {
                return {
                    form,
                    monitoringStatus,
                    isLoading,
                    queryDomainStatus,
                };
            }
        });

        app.use(ElementPlus);
        app.mount("#app");
    </script>
</body>

</html>