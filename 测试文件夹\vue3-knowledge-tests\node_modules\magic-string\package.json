{"name": "magic-string", "version": "0.30.17", "description": "Modify strings, generate sourcemaps", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "repository": "https://github.com/rich-harris/magic-string", "license": "MIT", "author": "<PERSON>", "main": "./dist/magic-string.cjs.js", "module": "./dist/magic-string.es.mjs", "sideEffects": false, "jsnext:main": "./dist/magic-string.es.mjs", "types": "./dist/magic-string.cjs.d.ts", "exports": {"./package.json": "./package.json", ".": {"import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js"}}, "files": ["dist/*", "index.d.ts", "README.md"], "devDependencies": {"@eslint/js": "^9.16.0", "@rollup/plugin-node-resolve": "^15.3.0", "@rollup/plugin-replace": "^5.0.7", "benchmark": "^2.1.4", "bumpp": "^9.9.1", "conventional-changelog-cli": "^3.0.0", "eslint": "^9.16.0", "prettier": "^3.4.2", "publint": "^0.2.12", "rollup": "^3.29.5", "source-map-js": "^1.2.1", "source-map-support": "^0.5.21", "vitest": "^2.1.8"}, "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0"}, "scripts": {"build": "rollup -c", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "format": "prettier --single-quote --print-width 100 --use-tabs --write .", "lint": "eslint src test && publint", "lint:fix": "eslint src test --fix", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "pretest": "npm run build", "test": "vitest run", "test:dev": "vitest", "bench": "npm run build && node benchmark/index.mjs", "watch": "rollup -cw"}}