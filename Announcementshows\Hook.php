<?php

namespace plugin\Announcementshows;

class Hook
{
    public function handle(&$js)
    {
        // 执行自动清理过期公告
        $this->autoCleanupExpiredAnnouncements();

        // 基础公告展示脚本
        $js[] = plugstatic("Announcementshows", 'announcement.js');

        // 广告位管理客户端脚本，提供租用和编辑功能
        $js[] = plugstatic("Announcementshows", 'user-ads.js');
    }

    // 管理员脚本
    public function adminHandle(&$js)
    {
        // 执行自动清理过期公告
        $this->autoCleanupExpiredAnnouncements();

        // 管理员只需要基础脚本，不需要用户广告管理脚本
        $js[] = plugstatic("Announcementshows", 'announcement.js');
    }

    /**
     * 自动清理过期公告
     */
    private function autoCleanupExpiredAnnouncements()
    {
        try {
            // 检查是否启用自动删除功能
            $autoDeleteEnabled = intval(plugconf("Announcementshows.auto_delete_enabled") ?? 0);
            if (!$autoDeleteEnabled) {
                return;
            }

            // 获取宽限期天数配置
            $gracePeriodDays = intval(plugconf("Announcementshows.grace_period_days") ?? 3);
            if ($gracePeriodDays < 0) {
                $gracePeriodDays = 3; // 默认3天
            }

            // 检查上次清理时间，避免频繁执行（每小时最多执行一次）
            $lastCleanupTime = intval(plugconf("Announcementshows.last_cleanup_time") ?? 0);
            $currentTime = time();
            if ($currentTime - $lastCleanupTime < 3600) { // 1小时 = 3600秒
                return;
            }

            // 先执行通知检查
            $this->checkAndSendNotifications($currentTime, $gracePeriodDays);

            // 获取当前公告配置
            $announcements = json_decode(plugconf("Announcementshows.announcements") ?? '[]', true);
            if (!is_array($announcements) || empty($announcements)) {
                return;
            }

            $hasChanges = false;
            $deletedCount = 0;

            // 检查每个公告是否需要删除
            foreach ($announcements as $index => &$announcement) {
                // 只处理有到期时间的公告
                if (empty($announcement['end_time'])) {
                    continue;
                }

                $endTime = strtotime($announcement['end_time']);
                if (!$endTime) {
                    continue;
                }

                // 计算宽限期结束时间
                $graceEndTime = $endTime + ($gracePeriodDays * 86400); // 86400秒 = 1天

                // 如果当前时间超过宽限期结束时间，则清空公告内容
                if ($currentTime > $graceEndTime) {
                    // 记录删除日志
                    $username = $announcement['username'] ?? '未知用户';
                    $position = $index + 1;
                    error_log("Announcementshows: 自动删除过期公告 - 位置:{$position}, 用户:{$username}, 到期时间:{$announcement['end_time']}, 宽限期:{$gracePeriodDays}天");

                    // 清空公告内容，但保留位置结构
                    $announcement['content'] = '';
                    $announcement['link'] = '';
                    $announcement['user_id'] = '';
                    $announcement['username'] = '';
                    $announcement['nickname'] = '';
                    $announcement['end_time'] = '';
                    $announcement['backgroundColor'] = plugconf('Announcementshows.background') ?? '#f5f7fa';
                    $announcement['textColor'] = plugconf('Announcementshows.text_color') ?? '#333333';
                    $announcement['useGradient'] = false;
                    $announcement['gradientColor'] = '';
                    $announcement['useTextGradient'] = false;
                    $announcement['textGradientColor'] = '';
                    $announcement['image_url'] = '';

                    $hasChanges = true;
                    $deletedCount++;
                }
            }
            unset($announcement);

            // 如果有变更，保存配置
            if ($hasChanges) {
                plugconf("Announcementshows.announcements", json_encode($announcements));
                error_log("Announcementshows: 自动清理完成，共删除 {$deletedCount} 个过期公告");
            }

            // 更新最后清理时间
            plugconf("Announcementshows.last_cleanup_time", $currentTime);

        } catch (\Exception $e) {
            error_log("Announcementshows: 自动清理过期公告失败 - " . $e->getMessage());
        }
    }

    /**
     * 检查并发送到期通知
     */
    private function checkAndSendNotifications($currentTime, $gracePeriodDays)
    {
        try {
            // 检查是否启用通知功能
            $notificationEnabled = intval(plugconf("Announcementshows.notification_enabled") ?? 0);
            if (!$notificationEnabled) {
                return;
            }

            // 获取通知配置
            $notificationType = plugconf("Announcementshows.notification_type") ?? 'email';
            $notificationDays = intval(plugconf("Announcementshows.notification_days") ?? 1);
            $smsEventCode = plugconf("Announcementshows.sms_event_code") ?? 'ad_expire';

            // 获取上次通知时间，避免重复发送（每天最多发送一次）
            $lastNotificationTime = intval(plugconf("Announcementshows.last_notification_time") ?? 0);
            if ($currentTime - $lastNotificationTime < 86400) { // 24小时 = 86400秒
                return;
            }

            // 获取当前公告配置
            $announcements = json_decode(plugconf("Announcementshows.announcements") ?? '[]', true);
            if (!is_array($announcements) || empty($announcements)) {
                return;
            }

            $notificationSent = false;

            // 检查每个公告是否需要发送通知
            foreach ($announcements as $index => $announcement) {
                // 只处理有到期时间的公告
                if (empty($announcement['end_time'])) {
                    continue;
                }

                // 确保公告有用户信息(用户ID或用户名)
                if (empty($announcement['user_id']) && empty($announcement['username'])) {
                    continue;
                }

                $endTime = strtotime($announcement['end_time']);
                if (!$endTime) {
                    continue;
                }

                // 计算通知时间：到期时间 - 通知提前天数
                $notificationTime = $endTime - ($notificationDays * 86400);

                // 如果当前时间已经到了通知时间，但还没到清理时间
                $graceEndTime = $endTime + ($gracePeriodDays * 86400);
                if ($currentTime >= $notificationTime && $currentTime < $graceEndTime) {
                    // 获取用户信息，优先使用user_id，如果没有则使用username
                    $userId = $announcement['user_id'] ?? null;
                    $username = $announcement['username'] ?? null;
                    
                    $user = null;
                    if (!empty($userId)) {
                        $user = $this->getUserInfo($userId);
                    } elseif (!empty($username)) {
                        $user = $this->getUserInfo($username);
                    }
                    
                    if (!$user) {
                        continue;
                    }

                    // 计算剩余天数
                    $remainingDays = ceil(($graceEndTime - $currentTime) / 86400);

                    // 发送通知
                    $this->sendExpirationNotification($user, $announcement, $remainingDays, $notificationType, $smsEventCode, $index + 1);
                    $notificationSent = true;
                }
            }

            // 如果发送了通知，更新最后通知时间
            if ($notificationSent) {
                plugconf("Announcementshows.last_notification_time", $currentTime);
            }

        } catch (\Exception $e) {
            error_log("Announcementshows: 发送到期通知失败 - " . $e->getMessage());
        }
    }

    /**
     * 获取用户信息
     * 如果提供的是用户ID，直接通过ID查询；如果提供的是用户名，则通过用户名查询
     * @param mixed $userIdOrName 用户ID或用户名
     * @return array|null 用户信息
     */
    private function getUserInfo($userIdOrName)
    {
        try {
            $user = null;
            
            // 判断传入的是ID还是用户名
            if (is_numeric($userIdOrName)) {
                // 通过ID查询
                $user = \think\facade\Db::name('user')
                    ->where('id', $userIdOrName)
                    ->field('id, username, nickname, mobile')
                    ->find();
            } else if (is_string($userIdOrName) && !empty($userIdOrName)) {
                // 通过用户名查询
                $user = \think\facade\Db::name('user')
                    ->where('username', $userIdOrName)
                    ->field('id, username, nickname, mobile')
                    ->find();
            }
            
            // 如果找到用户，尝试获取邮箱
            if ($user && !empty($user['id'])) {
                // 从user_email_push表获取邮箱
                $emailInfo = \think\facade\Db::name('user_email_push')
                    ->where('user_id', $user['id'])
                    ->field('email')
                    ->find();
                    
                if ($emailInfo && !empty($emailInfo['email'])) {
                    $user['email'] = $emailInfo['email'];
                }
            }
            
            return $user;
        } catch (\Exception $e) {
            error_log("Announcementshows: 获取用户信息失败 - " . $e->getMessage());
            return null;
        }
    }

    /**
     * 发送到期通知
     */
    private function sendExpirationNotification($user, $announcement, $remainingDays, $notificationType, $smsEventCode, $position)
    {
        try {
            $username = $user['username'] ?? $user['nickname'] ?? '用户';
            $content = $announcement['content'] ?? '';
            $contentPreview = mb_substr($content, 0, 20) . (mb_strlen($content) > 20 ? '...' : '');

            if ($notificationType === 'email' && !empty($user['email'])) {
                // 发送邮件通知
                $this->sendEmailNotification($user['email'], $username, $position, $contentPreview, $remainingDays);
            } elseif ($notificationType === 'sms' && !empty($user['mobile'])) {
                // 发送短信通知
                $this->sendSmsNotification($user['mobile'], $smsEventCode, $username, $position, $remainingDays);
            }

        } catch (\Exception $e) {
            error_log("Announcementshows: 发送通知失败 - " . $e->getMessage());
        }
    }

    /**
     * 发送邮件通知
     */
    private function sendEmailNotification($email, $username, $position, $contentPreview, $remainingDays)
    {
        try {
            $service = new \app\common\service\EmailService();

            $subject = '广告位到期提醒';
            $message = "亲爱的 {$username}：\n\n";
            $message .= "您在第 {$position} 号广告位的内容即将到期。\n";
            $message .= "广告内容：{$contentPreview}\n";
            $message .= "剩余时间：{$remainingDays} 天\n\n";
            $message .= "为避免广告内容被自动清除，请及时续费。\n\n";
            $message .= "感谢您的使用！";

            $res = $service->subject($subject)->message($message)->to($email)->send();

            if ($res) {
                error_log("Announcementshows: 邮件通知发送成功 - 用户:{$username}, 邮箱:{$email}, 位置:{$position}");
            } else {
                error_log("Announcementshows: 邮件通知发送失败 - " . $service->getError());
            }

        } catch (\Exception $e) {
            error_log("Announcementshows: 邮件通知异常 - " . $e->getMessage());
        }
    }

    /**
     * 发送短信通知
     */
    private function sendSmsNotification($mobile, $eventCode, $username, $position, $remainingDays)
    {
        try {
            $service = new \app\common\service\SmsService();

            // 短信模板变量 - 使用${变量名}格式
            $params = [
                'username' => $username,
                'position' => $position,
                'days' => $remainingDays
            ];

            $ret = $service->sendCode($mobile, $eventCode, $params);

            if ($ret) {
                error_log("Announcementshows: 短信通知发送成功 - 用户:{$username}, 手机:{$mobile}, 位置:{$position}");
            } else {
                error_log("Announcementshows: 短信通知发送失败 - " . $service->getError());
            }

        } catch (\Exception $e) {
            error_log("Announcementshows: 短信通知异常 - " . $e->getMessage());
        }
    }
}