<?php

namespace plugin\Announcementshows\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;

class User extends BasePlugin {
    protected $scene = ['user'];  // 用户场景
    protected $noNeedLogin = [];  // 所有方法都需要登录

    // 显示广告编辑页面
    public function edit() {
        // 获取要聚焦的广告位ID
        $position = $this->request->param('position/d', 0);
        
        // 传递到视图
        return View::fetch('', [
            'focusPosition' => $position
        ]);
    }

    // 获取当前用户的广告位
    public function getMyAds() {
        try {
            // 从全局配置获取公告配置
            $config = [
                'display_mode' => intval(plugconf('Announcementshows.display_mode') ?? 0),
                'height' => intval(plugconf('Announcementshows.height') ?? 60),
                'padding' => intval(plugconf('Announcementshows.padding') ?? 8),
                'background' => plugconf('Announcementshows.background') ?? '#f5f7fa',
                'text_color' => plugconf('Announcementshows.text_color') ?? '#333333',
                'auto_scroll' => intval(plugconf('Announcementshows.auto_scroll') ?? 1),
                'scroll_interval' => intval(plugconf('Announcementshows.scroll_interval') ?? 5000),
            ];

            // 优先获取用户自己的广告配置
            $userAnnouncements = json_decode(merchant_plugconf($this->user->id, "Announcementshows.announcements") ?? '[]', true);
            
            // 如果用户配置为空，则获取全局配置
            if (empty($userAnnouncements)) {
                $userAnnouncements = json_decode(plugconf("Announcementshows.announcements") ?? '[]', true);
            }
            
            // 提取当前用户拥有的广告位
            $myAds = [];
            foreach ($userAnnouncements as $index => $announcement) {
                if (isset($announcement['user_id']) && $announcement['user_id'] === $this->user->id) {
                    $ad = $announcement;
                    $ad['position'] = $index + 1; // 广告位编号（从1开始）
                    if (!isset($ad['backgroundColor'])) {
                        $ad['backgroundColor'] = $config['background'];
                    }
                    if (!isset($ad['textColor'])) {
                        $ad['textColor'] = $config['text_color'];
                    }
                    // 确保link字段存在
                    if (!isset($ad['link'])) {
                        $ad['link'] = $ad['link_url'] ?? '';
                    }
                    // 确保content字段存在
                    if (!isset($ad['content'])) {
                        $ad['content'] = '';
                    }
                    $myAds[] = $ad;
                }
            }
            
            // 确保全局配置和用户配置同步
            if (!empty($myAds) && json_encode($userAnnouncements) !== plugconf("Announcementshows.announcements")) {
                merchant_plugconf($this->user->id, "Announcementshows.announcements", plugconf("Announcementshows.announcements"));
            }

            return json(['code' => 200, 'data' => [
                'ads' => $myAds,
                'config' => $config
            ]]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '获取数据失败: ' . $e->getMessage(), 'type' => 'error']);
        }
    }

    // 编辑广告内容
    public function editAdContent() {
        if ($this->request->isPost()) {
            try {
                $position = $this->request->post('position/d', 0);
                $content = $this->request->post('content/s', NULL);
                $isImage = $this->request->post('is_image/b', false);
                $textColor = $this->request->post('textColor/s', '#333333');
                $backgroundColor = $this->request->post('backgroundColor/s', '#FFFFFF');
                $useGradient = $this->request->post('useGradient/b', false);
                $gradientStartColor = $this->request->post('gradientStartColor/s', '#409eff');
                $gradientEndColor = $this->request->post('gradientEndColor/s', '#67c23a');
                $gradientDirection = $this->request->post('gradientDirection/s', 'to right');
                $useTextGradient = $this->request->post('useTextGradient/b', false);
                $textGradientStartColor = $this->request->post('textGradientStartColor/s', '#f56c6c');
                $textGradientEndColor = $this->request->post('textGradientEndColor/s', '#e6a23c');
                $textGradientDirection = $this->request->post('textGradientDirection/s', 'to right');
                $link = $this->request->post('link/s', '');

                if (!$position) {
                    return json(['code' => 400, 'msg' => '请选择有效的广告位置', 'type' => 'warning']);
                }

                // 检查是否允许商户自定义广告内容
                $allowMerchantEdit = intval(plugconf("Announcementshows.allow_merchant_edit") ?? 1);
                if (!$allowMerchantEdit) {
                    return json(['code' => 403, 'msg' => '管理员已禁止商户自定义广告内容', 'type' => 'error']);
                }

                // 获取全局广告配置
                $announcements = json_decode(plugconf("Announcementshows.announcements") ?? '[]', true);
                
                // 检查广告位是否存在且属于当前用户
                if (!isset($announcements[$position - 1]) || 
                    !isset($announcements[$position - 1]['user_id']) || 
                    $announcements[$position - 1]['user_id'] !== $this->user->id) {
                    return json(['code' => 403, 'msg' => '您暂无此广告位的编辑权限', 'type' => 'error']);
                }

                // 检查广告位是否过期
                if (isset($announcements[$position - 1]['end_time'])) {
                    $endTime = strtotime($announcements[$position - 1]['end_time']);
                    if ($endTime < time()) {
                        return json(['code' => 400, 'msg' => '此广告位已过期，无法编辑', 'type' => 'warning']);
                    }
                }

                // 根据不同模式处理内容
                if ($isImage) {
                    // 图片模式
                    $announcements[$position - 1]['content'] = xss_safe($content);
                    $announcements[$position - 1]['link'] = xss_safe($link); // 保存链接
                    $announcements[$position - 1]['link_url'] = xss_safe($link); // 同时也保存为link_url确保一致性
                    
                    // 添加image_url字段
                    if (!empty($content)) {
                        if (!preg_match('/^(https?:\/\/|\/)/i', $content)) {
                            $imageUrl = '/' . $content; // 修正路径
                        } else {
                            $imageUrl = $content;
                        }
                        $announcements[$position - 1]['image_url'] = xss_safe($imageUrl);
                    }
                } else {
                    // 文字模式
                    $announcements[$position - 1]['content'] = xss_safe($content, ['style', 'span', 'p', 'br', 'div']);
                    $announcements[$position - 1]['link'] = xss_safe($link); // 保存链接
                    $announcements[$position - 1]['link_url'] = xss_safe($link); // 同时也保存为link_url确保一致性
                    $announcements[$position - 1]['textColor'] = xss_safe($textColor);
                    $announcements[$position - 1]['backgroundColor'] = xss_safe($backgroundColor);

                    // 保存背景渐变相关数据
                    $announcements[$position - 1]['useGradient'] = $useGradient;
                    $announcements[$position - 1]['gradientStartColor'] = xss_safe($gradientStartColor);
                    $announcements[$position - 1]['gradientEndColor'] = xss_safe($gradientEndColor);
                    $announcements[$position - 1]['gradientDirection'] = xss_safe($gradientDirection);

                    // 保存文字渐变相关数据
                    $announcements[$position - 1]['useTextGradient'] = $useTextGradient;
                    $announcements[$position - 1]['textGradientStartColor'] = xss_safe($textGradientStartColor);
                    $announcements[$position - 1]['textGradientEndColor'] = xss_safe($textGradientEndColor);
                    $announcements[$position - 1]['textGradientDirection'] = xss_safe($textGradientDirection);

                    // 如果使用背景渐变，生成渐变CSS字符串
                    if ($useGradient) {
                        $gradientCSS = "linear-gradient({$gradientDirection}, {$gradientStartColor}, {$gradientEndColor})";
                        $announcements[$position - 1]['gradientColor'] = $gradientCSS;
                    } else {
                        // 如果不使用渐变，清除渐变CSS
                        unset($announcements[$position - 1]['gradientColor']);
                    }

                    // 如果使用文字渐变，生成文字渐变CSS字符串
                    if ($useTextGradient) {
                        $textGradientCSS = "linear-gradient({$textGradientDirection}, {$textGradientStartColor}, {$textGradientEndColor})";
                        $announcements[$position - 1]['textGradientColor'] = $textGradientCSS;
                    } else {
                        // 如果不使用文字渐变，清除文字渐变CSS
                        unset($announcements[$position - 1]['textGradientColor']);
                    }
                }
                
                // 保存到全局配置中
                plugconf("Announcementshows.announcements", json_encode($announcements));
                
                // 确保同步到商家配置
                merchant_plugconf($this->user->id, "Announcementshows.announcements", json_encode($announcements));

                // 发送刷新信号
                try {
                    $refreshMessage = [
                        'type' => 'REFRESH_ANNOUNCEMENTS',
                        'timestamp' => time(),
                        'position' => $position,
                        'user_id' => $this->user->id
                    ];
                } catch (\Exception $e) {
                    // 静默处理异常
                }

                return json(['code' => 200, 'msg' => '广告内容更新成功', 'type' => 'success']);
            } catch (\Exception $e) {
                return json(['code' => 500, 'msg' => '系统处理异常，请稍后重试', 'type' => 'error']);
            }
        }
        return json(['code' => 405, 'msg' => '不支持的请求方法', 'type' => 'error']);
    }

    // 开通广告位
    public function openAdSpace() {
        if ($this->request->isPost()) {
            try {
                $duration = $this->request->post('duration/d', 0);
                $price = $this->request->post('price/f', 0);
                $position = $this->request->post('position/d', 0);
                $isRenew = $this->request->post('is_renew/b', false);

                if (!$duration || !$price || !$position) {
                    return json(['code' => 400, 'msg' => '请填写完整的开通信息', 'type' => 'warning']);
                }

                // 检查余额
                $user = Db::name('user')->where('id', $this->user->id)->find();
                if (!$user) {
                    return json(['code' => 400, 'msg' => '用户信息获取失败，请重新登录', 'type' => 'error']);
                }

                if ($user['operate_money'] < $price) {
                    return json(['code' => 400, 'msg' => '账户余额不足，请先充值', 'type' => 'warning']);
                }

                // 获取全局广告位配置
                $announcements = json_decode(plugconf("Announcementshows.announcements") ?? '[]', true);
                
                // 确保位置有效
                if ($position < 1 || $position > count($announcements)) {
                    return json(['code' => 400, 'msg' => '无效的广告位置', 'type' => 'error']);
                }
                
                // 检查广告位是否已被租用
                if (!empty($announcements[$position - 1]['user_id']) && 
                    $announcements[$position - 1]['user_id'] !== $this->user->id) {
                    return json(['code' => 400, 'msg' => '该广告位已被其他用户租用', 'type' => 'warning']);
                }

                // 检查广告位是否过期
                if (!empty($announcements[$position - 1]['end_time'])) {
                    $endTime = strtotime($announcements[$position - 1]['end_time']);
                    // 如果广告位未过期且不是当前用户的
                    if ($endTime > time() && 
                        (!empty($announcements[$position - 1]['user_id']) && 
                         $announcements[$position - 1]['user_id'] !== $this->user->id)) {
                        return json(['code' => 400, 'msg' => '该广告位尚未到期', 'type' => 'warning']);
                    }
                }

                // 如果是续费，需要检查是否是当前用户的广告位
                if ($isRenew) {
                    if (!isset($announcements[$position - 1]['user_id']) || 
                        $announcements[$position - 1]['user_id'] !== $this->user->id) {
                        return json(['code' => 403, 'msg' => '您暂无此广告位的续费权限', 'type' => 'error']);
                    }
                }

                // 开启事务
                Db::startTrans();
                try {
                    // 扣除用户余额
                    $updateResult = Db::name('user')
                        ->where('id', $this->user->id)
                        ->dec('operate_money', $price)
                        ->update();
        
                    // 记录资金变动
                    $moneyLogResult = Db::name('user_money_log')->insert([
                        'user_id' => $this->user->id,
                        'reason' => '开通广告位 ' . $duration . '天',
                        'create_time' => time(),
                        'change' => -$price,
                        'source' => 'Operate'
                    ]);

                    // 更新广告位状态
                    if ($isRenew && isset($announcements[$position - 1]['end_time'])) {
                        // 续费：在原到期时间基础上增加时间
                        $currentEndTime = strtotime($announcements[$position - 1]['end_time']);
                        $newEndTime = date('Y-m-d H:i:s', strtotime("+{$duration} day", max($currentEndTime, time())));
                        
                        // 保留原有内容，只更新到期时间
                        $announcements[$position - 1]['end_time'] = $newEndTime;
                    } else {
                        // 新开通：完全更新配置
                        $endTime = date('Y-m-d H:i:s', strtotime("+{$duration} day"));
                        
                        // 保留原有内容如果是当前用户的广告位
                        $content = '<div style="text-align: center;">广告位已被租用</div>';
                        $backgroundColor = '#f5f7fa';
                        $textColor = '#333333';
                        $link = '';
                        
                        // 如果是同一用户，保留原有内容
                        if (isset($announcements[$position - 1]['user_id']) && 
                            $announcements[$position - 1]['user_id'] === $this->user->id) {
                            $content = $announcements[$position - 1]['content'] ?? $content;
                            $backgroundColor = $announcements[$position - 1]['backgroundColor'] ?? $backgroundColor;
                            $textColor = $announcements[$position - 1]['textColor'] ?? $textColor;
                            $link = $announcements[$position - 1]['link'] ?? '';
                        }
                        
                        $announcements[$position - 1] = [
                            'content' => xss_safe($content, ['style', 'div']),
                            'backgroundColor' => $backgroundColor,
                            'textColor' => $textColor,
                            'link' => $link,
                            'link_url' => $link,
                            'end_time' => $endTime,
                            'user_id' => $this->user->id
                        ];
                    }
                    
                    // 保存更新后的配置到全局
                    plugconf("Announcementshows.announcements", json_encode($announcements));
                    
                    // 同时保存到商家配置中
                    merchant_plugconf($this->user->id, "Announcementshows.announcements", json_encode($announcements));

                    Db::commit();
                    return json(['code' => 200, 'msg' => '广告位开通成功', 'type' => 'success']);
                } catch (\Exception $e) {
                    Db::rollback();
                    return json(['code' => 500, 'msg' => '系统处理异常，请稍后重试', 'type' => 'error']);
                }
            } catch (\Exception $e) {
                return json(['code' => 500, 'msg' => '系统错误：' . $e->getMessage(), 'type' => 'error']);
            }
        }
        return json(['code' => 405, 'msg' => '不支持的请求方法', 'type' => 'error']);
    }

    // 获取价格配置和用户余额
    public function getPriceConfig() {
        try {
            // 获取价格配置
            $priceConfig = [
                'month_price' => floatval(plugconf('Announcementshows.month_price') ?? 19.9),
                'quarter_price' => floatval(plugconf('Announcementshows.quarter_price') ?? 49.9),
                'halfyear_price' => floatval(plugconf('Announcementshows.halfyear_price') ?? 89.9),
                'year_price' => floatval(plugconf('Announcementshows.year_price') ?? 169.9),
                'renew_month_price' => floatval(plugconf('Announcementshows.renew_month_price') ?? 17.9),
                'renew_quarter_price' => floatval(plugconf('Announcementshows.renew_quarter_price') ?? 44.9),
                'renew_halfyear_price' => floatval(plugconf('Announcementshows.renew_halfyear_price') ?? 79.9),
                'renew_year_price' => floatval(plugconf('Announcementshows.renew_year_price') ?? 149.9),
            ];
            
            // 获取用户余额
            $user = Db::name('user')->where('id', $this->user->id)->find();
            if ($user) {
                $priceConfig['user_money'] = $user['operate_money'];
            }
            
            return json(['code' => 200, 'data' => $priceConfig]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '获取价格配置失败: ' . $e->getMessage()]);
        }
    }

    // 处理用户取消租用广告位
    public function cancelAdRent() {
        if (!$this->request->isPost()) {
            return json(['code' => 405, 'msg' => '请求方法不正确', 'type' => 'error']);
        }
        
        try {
            $position = $this->request->post('position/d', 0);
            $action = $this->request->post('action/s', '');
            
            if (!$position || $action !== 'cancel') {
                return json(['code' => 400, 'msg' => '参数错误', 'type' => 'error']);
            }
            
            // 记录用户取消操作
            Db::name('user_action_log')->insert([
                'user_id' => $this->user->id,
                'action' => 'cancel_ad_rent',
                'details' => json_encode([
                    'position' => $position,
                    'timestamp' => time()
                ]),
                'ip' => $this->request->ip(),
                'create_time' => time()
            ]);
            
            return json(['code' => 200, 'msg' => '操作已记录', 'type' => 'success']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '系统异常', 'type' => 'error']);
        }
    }

    // 检查用户是否拥有指定广告位
    public function checkAdOwnership() {
        if (!$this->request->isPost()) {
            return json(['code' => 405, 'msg' => '请求方法不正确', 'type' => 'error']);
        }
        
        try {
            $position = $this->request->post('position/d', 0);
            
            if (!$position) {
                return json(['code' => 400, 'msg' => '参数错误', 'type' => 'error']);
            }
            
            // 获取全局广告位配置
            $announcements = json_decode(plugconf("Announcementshows.announcements") ?? '[]', true);
            
            // 检查广告位是否存在且属于当前用户
            $owned = false;
            $expired = false;
            $endTime = null;

            if (isset($announcements[$position - 1]) &&
                isset($announcements[$position - 1]['user_id']) &&
                $announcements[$position - 1]['user_id'] === $this->user->id) {

                // 检查广告位是否已过期
                if (isset($announcements[$position - 1]['end_time']) && !empty($announcements[$position - 1]['end_time'])) {
                    $endTime = strtotime($announcements[$position - 1]['end_time']);
                    $owned = $endTime > time(); // 只有未过期的广告位才算拥有
                    $expired = $endTime <= time(); // 检查是否已过期
                } else {
                    $owned = true; // 无结束时间视为永久拥有
                    $expired = false;
                }
            }

            return json([
                'code' => 200,
                'data' => [
                    'owned' => $owned,
                    'expired' => $expired,
                    'position' => $position,
                    'end_time' => $endTime ? date('Y-m-d H:i:s', $endTime) : null
                ]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '系统异常', 'type' => 'error']);
        }
    }
}
