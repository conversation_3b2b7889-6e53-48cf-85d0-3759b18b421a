<?php

namespace plugin\KsushouClone\controller;

use app\common\controller\BasePlugin;
use think\facade\View;

class Api extends BasePlugin {

    protected $scene = [
        'admin'
    ];
    protected $noNeedLogin = [];

    public function index() {
        return View::fetch();
    }

    public function fetchData() {
        $params = [
            'app_id' => base64_decode(plugconf("KsushouClone.base64_app_id") ?? ''),
            'app_secret' => base64_decode(plugconf("KsushouClone.base64_app_secret") ?? ''),
        ];
        
        $this->success('success', $params);
    }

    public function save() {
        $app_id = $this->request->post('app_id/s', '');
        $app_secret = $this->request->post('app_secret/s', '');

        plugconf("KsushouClone.base64_app_id", base64_encode($app_id));
        plugconf("KsushouClone.base64_app_secret", base64_encode($app_secret));

        $this->success('success');
    }
}
