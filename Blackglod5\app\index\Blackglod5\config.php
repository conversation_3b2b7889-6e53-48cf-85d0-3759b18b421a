<?php

# 配置文件

return [
    // 服务中心
    [
        'id' => 'footer_service_show',
        'name' => '是否显示服务中心',
        'required' => true,
        'type' => 'radio',
        'data' => [
            [
                'name' => '关闭',
                'value' => 0
            ],
            [
                'name' => '开启',
                'value' => 1
            ]
        ]
    ],
    [
        'id' => 'footer_service_1',
        'name' => '服务中心链接1',
        'placeholder' => '卡密查询',
        'type' => 'input',
    ],
    [
        'id' => 'footer_service_1_link',
        'name' => '服务中心链接1地址',
        'placeholder' => '#',
        'type' => 'input',
    ],
    [
        'id' => 'footer_service_2',
        'name' => '服务中心链接2',
        'placeholder' => '投诉中心',
        'type' => 'input',
    ],
    [
        'id' => 'footer_service_2_link',
        'name' => '服务中心链接2地址',
        'placeholder' => '#',
        'type' => 'input',
    ],
    [
        'id' => 'footer_service_3',
        'name' => '服务中心链接3',
        'placeholder' => '卡密工具',
        'type' => 'input',
    ],
    [
        'id' => 'footer_service_3_link',
        'name' => '服务中心链接3地址',
        'placeholder' => '#',
        'type' => 'input',
    ],
    [
        'id' => 'footer_service_4',
        'name' => '服务中心链接4',
        'placeholder' => '商户入驻',
        'type' => 'input',
    ],
    [
        'id' => 'footer_service_4_link',
        'name' => '服务中心链接4地址',
        'placeholder' => '#',
        'type' => 'input',
    ],

    // 帮助中心
    [
        'id' => 'footer_help_show',
        'name' => '是否显示帮助中心',
        'required' => true,
        'type' => 'radio',
        'data' => [
            [
                'name' => '关闭',
                'value' => 0
            ],
            [
                'name' => '开启',
                'value' => 1
            ]
        ]
    ],
    [
        'id' => 'footer_help_1',
        'name' => '帮助中心链接1',
        'placeholder' => '常见问题',
        'type' => 'input',
    ],
    [
        'id' => 'footer_help_1_link',
        'name' => '帮助中心链接1地址',
        'placeholder' => '#',
        'type' => 'input',
    ],
    [
        'id' => 'footer_help_2',
        'name' => '帮助中心链接2',
        'placeholder' => '系统公告',
        'type' => 'input',
    ],
    [
        'id' => 'footer_help_2_link',
        'name' => '帮助中心链接2地址',
        'placeholder' => '#',
        'type' => 'input',
    ],
    [
        'id' => 'footer_help_3',
        'name' => '帮助中心链接3',
        'placeholder' => '结算公告',
        'type' => 'input',
    ],
    [
        'id' => 'footer_help_3_link',
        'name' => '帮助中心链接3地址',
        'placeholder' => '#',
        'type' => 'input',
    ],
    [
        'id' => 'footer_help_4',
        'name' => '帮助中心链接4',
        'placeholder' => '新闻动态',
        'type' => 'input',
    ],
    [
        'id' => 'footer_help_4_link',
        'name' => '帮助中心链接4地址',
        'placeholder' => '#',
        'type' => 'input',
    ],

    // 法律责任
    [
        'id' => 'footer_legal_show',
        'name' => '是否显示法律责任',
        'required' => true,
        'type' => 'radio',
        'data' => [
            [
                'name' => '关闭',
                'value' => 0
            ],
            [
                'name' => '开启',
                'value' => 1
            ]
        ]
    ],
    [
        'id' => 'footer_legal_1',
        'name' => '法律责任链接1',
        'placeholder' => '免责声明',
        'type' => 'input',
    ],
    [
        'id' => 'footer_legal_1_link',
        'name' => '法律责任链接1地址',
        'placeholder' => '#',
        'type' => 'input',
    ],
    [
        'id' => 'footer_legal_2',
        'name' => '法律责任链接2',
        'placeholder' => '禁售商品',
        'type' => 'input',
    ],
    [
        'id' => 'footer_legal_2_link',
        'name' => '法律责任链接2地址',
        'placeholder' => '#',
        'type' => 'input',
    ],
    [
        'id' => 'footer_legal_3',
        'name' => '法律责任链接3',
        'placeholder' => '服务协议',
        'type' => 'input',
    ],
    [
        'id' => 'footer_legal_3_link',
        'name' => '法律责任链接3地址',
        'placeholder' => '#',
        'type' => 'input',
    ],
    [
        'id' => 'footer_legal_4',
        'name' => '法律责任链接4',
        'placeholder' => '隐私政策',
        'type' => 'input',
    ],
    [
        'id' => 'footer_legal_4_link',
        'name' => '法律责任链接4地址',
        'placeholder' => '#',
        'type' => 'input',
    ],

    // 友情链接
    [
        'id' => 'footer_links_show',
        'name' => '是否显示友情链接',
        'required' => true,
        'type' => 'radio',
        'data' => [
            [
                'name' => '关闭',
                'value' => 0
            ],
            [
                'name' => '开启',
                'value' => 1
            ]
        ]
    ],
    [
        'id' => 'footer_links_1',
        'name' => '友情链接1',
        'placeholder' => '一意支付',
        'type' => 'input',
    ],
    [
        'id' => 'footer_links_1_link',
        'name' => '友情链接1地址',
        'placeholder' => '#',
        'type' => 'input',
    ],
    [
        'id' => 'footer_links_2',
        'name' => '友情链接2',
        'placeholder' => '支付宝',
        'type' => 'input',
    ],
    [
        'id' => 'footer_links_2_link',
        'name' => '友情链接2地址',
        'placeholder' => '#',
        'type' => 'input',
    ],
    [
        'id' => 'footer_links_3',
        'name' => '友情链接3',
        'placeholder' => '微信支付',
        'type' => 'input',
    ],
    [
        'id' => 'footer_links_3_link',
        'name' => '友情链接3地址',
        'placeholder' => '#',
        'type' => 'input',
    ],
    [
        'id' => 'footer_links_4',
        'name' => '友情链接4',
        'placeholder' => 'QQ钱包',
        'type' => 'input',
    ],
    [
        'id' => 'footer_links_4_link',
        'name' => '友情链接4地址',
        'placeholder' => '#',
        'type' => 'input',
    ],

    // 联系方式
    [
        'id' => 'contact_email',
        'name' => '联系邮箱',
        'placeholder' => '<EMAIL>',
        'type' => 'input',
    ],
    [
        'id' => 'contact_qq',
        'name' => 'QQ客服',
        'placeholder' => '123456789',
        'type' => 'input',
    ],
    [
        'id' => 'contact_wx',
        'name' => '微信客服',
        'placeholder' => 'service_wx',
        'type' => 'input',
    ],

    // 首页统计数据配置
    [
        'id' => 'stats_orders',
        'name' => '完成订单数',
        'placeholder' => '12452177',
        'type' => 'input',
        'required' => true,
    ],
    [
        'id' => 'stats_cards',
        'name' => '发卡次数',
        'placeholder' => '2565245',
        'type' => 'input',
        'required' => true,
    ],
    [
        'id' => 'stats_merchants',
        'name' => '商户累计',
        'placeholder' => '1526988',
        'type' => 'input',
        'required' => true,
    ],
    [
        'id' => 'stats_amount',
        'name' => '交易金额',
        'placeholder' => '84768.25',
        'type' => 'input',
        'required' => true,
    ],
];
