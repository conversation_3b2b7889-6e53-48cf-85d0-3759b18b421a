<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>富文本视频清理工具</title>
    <style>
        .page-container {
            display: flex;
            min-height: 100vh;
        }
        .sidebar {
            width: 160px;
            border-right: 1px solid #dcdfe6;
            padding: 20px 0;
            background-color: #fff;
        }
        .main-content {
            flex: 1;
            padding: 20px 30px;
        }
        .suggestion-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 0;
        }
        .stats-info {
            margin-top: 10px;
        }
        .button-group {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .el-form-item-description {
            color: #606266;
            font-size: 12px;
            line-height: 1.5;
        }
        .el-form-item-description ul {
            margin: 5px 0;
        }
        .el-form-item-description li {
            margin: 3px 0;
        }
        .el-menu {
            border-right: none !important;
        }
        .el-menu-item {
            height: 50px;
            line-height: 50px;
            padding: 0 15px !important;
        }
        .el-menu-item .el-icon {
            margin-right: 8px;
            width: 20px;
            text-align: center;
            font-size: 16px;
            vertical-align: middle;
        }
        .el-menu-item span {
            font-size: 13px;
            color: #303133;
        }
        .el-menu-item.is-active {
            background-color: #ecf5ff;
        }
        .el-menu-item.is-active span {
            color: #409eff;
        }
        .el-menu-item:hover {
            background-color: #f5f7fa;
        }
        .upload-button {
            display: inline-block;
        }
        .upload-button .el-button {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .upload-button .el-icon {
            margin-right: 2px;
        }
        .backup-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .backup-actions {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .backup-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            gap: 20px;
            padding-right: 20px;
        }
        .backup-info .filename {
            flex: 1;
            font-size: 14px;
            color: #303133;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            min-width: 0;
        }
        .backup-info .file-meta {
            flex-shrink: 0;
            display: flex;
            align-items: center;
            gap: 15px;
            color: #909399;
            font-size: 13px;
        }
        .backup-info .file-size {
            white-space: nowrap;
        }
        .backup-info .file-time {
            white-space: nowrap;
        }
        .el-table {
            --el-table-border-color: #EBEEF5;
            --el-table-header-background-color: #F5F7FA;
        }
        .el-table th {
            background-color: var(--el-table-header-background-color);
            color: #606266;
            font-weight: 500;
        }
        .el-table td {
            padding: 12px 0;
        }
        .el-table .el-button--link {
            padding: 4px 8px;
            font-size: 13px;
        }
        .el-tag {
            margin: 0 4px;
        }
        .el-table tbody tr:hover td {
            background-color: #F5F7FA;
        }
        .el-popover {
            max-width: 300px;
            font-size: 13px;
            line-height: 1.5;
            color: #606266;
        }
        .el-message-box {
            width: 400px;
        }
        .el-message {
            width: auto;
            min-width: 300px;
            max-width: 500px;
        }
        .upload-button .icon {
            margin-right: 4px;
            vertical-align: middle;
        }
        .upload-button .el-button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }
        .el-button svg {
            margin-top: -2px;
        }
        .el-form-item-description {
            font-size: 12px;
            color: #909399;
            line-height: 1.5;
            margin-top: 4px;
            padding-left: 1px;
            display: block !important;
        }
        .progress-container {
            width: 100%;
        }
        .progress-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .progress-status p {
            margin: 0;
        }
        .el-progress {
            width: 100%;
        }
        /* 表格样式优化 */
        .el-table .cell {
            word-break: break-all;
            line-height: 1.4;
        }
        .el-table__body-wrapper {
            overflow-x: auto;
        }
        .el-table .el-table__cell {
            padding: 8px 0;
        }
        /* 文件路径列样式 */
        .file-path-cell {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #606266;
            word-break: break-all;
            white-space: normal;
        }
        /* 文件大小列样式 */
        .file-size-cell {
            font-weight: 500;
            color: #409eff;
        }
        /* 上传时间列样式 */
        .upload-time-cell {
            font-family: 'Courier New', monospace;
            font-size: 13px;
            color: #606266;
            font-weight: 500;
        }
        /* 表格排序样式 */
        .el-table th.is-sortable .cell {
            cursor: pointer;
        }
    </style>
</head>

<body>
<div id="app">
    <div class="page-container">
        <!-- 左侧导航栏 -->
        <div class="sidebar">
            <el-menu
                :default-active="activeMenu"
                @select="handleMenuSelect"
                style="border-right: none">
                <el-menu-item index="manual">
                    <el-icon>
                        <svg viewBox="0 0 1024 1024" width="24" height="24">
                            <path d="M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 632H136V232h752v560z" fill="currentColor"/>
                            <path d="M424 352c48.6 0 88 39.4 88 88s-39.4 88-88 88-88-39.4-88-88 39.4-88 88-88zm224 400H376c-4.4 0-8-3.6-8-8v-48c0-4.4 3.6-8 8-8h272c4.4 0 8 3.6 8 8v48c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V391zm-88 236c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v128c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V627zm264-236c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v364c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V391z" fill="currentColor"/>
                        </svg>
                    </el-icon>
                    <span>手动清理</span>
                </el-menu-item>
                <el-menu-item index="auto">
                    <el-icon>
                        <svg viewBox="0 0 1024 1024" width="24" height="24">
                            <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm47.7-420.7c-25.7-4.9-45.8-9.1-45.8-22.3 0-12.1 15.4-20 37.7-20 24.3 0 41.1 7.9 47.7 21.7 1.8 3.7 5.5 6 9.6 6h32.2c6.6 0 11.5-6.3 9.7-12.6-7.8-26.7-33.8-45.1-74.2-45.1v-32c0-4.4-3.6-8-8-8h-32c-4.4 0-8 3.6-8 8v32c-46.9 0-81.4 23.7-81.4 59.3 0 36.1 29.5 47.8 89.1 57.3 35.7 5.7 45.8 12.2 45.8 24.7 0 13.6-17.3 22.3-43.1 22.3-28.7 0-47.9-9.7-54.3-25.3-1.6-3.9-5.4-6.4-9.6-6.4h-33.2c-6.6 0-11.5 6.4-9.6 12.7 9.5 29.9 39.6 48.9 82.7 48.9V704c0 4.4 3.6 8 8 8h32c4.4 0 8-3.6 8-8v-32.2c50.8 0 83.2-24.8 83.2-63.3 0-37.3-29.6-49.2-85.5-58.2z" fill="currentColor"/>
                        </svg>
                    </el-icon>
                    <span>自动清理</span>
                </el-menu-item>
                <el-menu-item index="stats">
                    <el-icon>
                        <svg viewBox="0 0 1024 1024" width="24" height="24">
                            <path d="M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-40 824H232V136h560v752zM484 391c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v364c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V391zm-88 236c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v128c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V627zm264-236c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v364c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V391z" fill="currentColor"/>
                        </svg>
                    </el-icon>
                    <span>统计信息</span>
                </el-menu-item>
            </el-menu>
        </div>

        <!-- 主要内容区 -->
        <div class="main-content">
            <!-- 手动清理 -->
            <el-card v-show="activeMenu === 'manual'" shadow="never">
                <template #header>
                    <div class="card-header">
                        <h3>手动清理商品描述视频</h3>
                    </div>
                </template>

                <el-form :model="manualForm" label-width="120px">
                    <el-form-item label="用户ID：">
                        <el-autocomplete
                            v-model="manualForm.user_id"
                            :fetch-suggestions="searchUsers"
                            placeholder="请输入用户ID（不输入则清理所有）"
                            style="width: 100%"
                            clearable
                            @select="handleUserSelect"
                            @clear="handleUserClear">
                            <template #default="{ item }">
                                <div>{{ item.label }}</div>
                            </template>
                        </el-autocomplete>
                    </el-form-item>

                    <el-form-item label="日期范围：">
                        <el-date-picker
                            v-model="manualForm.dateRange"
                            type="datetimerange"
                            range-separator="至"
                            start-placeholder="开始日期时间"
                            end-placeholder="结束日期时间"
                            value-format="YYYY-MM-DD HH:mm:ss"
                            format="YYYY-MM-DD HH:mm:ss"
                            :clearable="true"
                            @change="handleDateRangeChange"
                            style="width: 100%">
                        </el-date-picker>
                        <div class="el-form-item-description">
                            可选择精确到秒的日期时间范围，不选择则清理所有时间范围的商品视频
                        </div>
                    </el-form-item>

                    <el-form-item label="视频扩展名：">
                        <el-input
                            v-model="manualForm.extensions"
                            placeholder="mp4,avi,mov,wmv,flv,mkv,webm,m4v"
                            style="width: 100%"
                        />
                        <div class="el-form-item-description">
                            多个扩展名用逗号分隔，留空使用默认配置
                        </div>
                    </el-form-item>

                    <el-form-item label="文件大小：">
                        <el-row :gutter="10">
                            <el-col :span="11">
                                <el-input
                                    v-model="manualForm.min_size"
                                    placeholder="最小大小(MB)"
                                    type="number"
                                    min="0"
                                />
                            </el-col>
                            <el-col :span="2" style="text-align: center;">
                                <span>至</span>
                            </el-col>
                            <el-col :span="11">
                                <el-input
                                    v-model="manualForm.max_size"
                                    placeholder="最大大小(MB)"
                                    type="number"
                                    min="0"
                                />
                            </el-col>
                        </el-row>
                        <div class="el-form-item-description">
                            设置为0表示不限制，可用于过滤特定大小范围的视频文件
                        </div>
                    </el-form-item>

                    <el-form-item>
                        <el-button
                            type="primary"
                            @click="searchVideoStats"
                            :loading="isSearching">
                            查询统计
                        </el-button>
                    </el-form-item>

                    <!-- 显示统计结果 -->
                    <div v-if="videoStats && hasSearched" class="stats-info">
                        <el-alert
                            :title="videoStats.msg"
                            type="info"
                            :closable="false">
                        </el-alert>
                    </div>

                    <el-divider>手动清理</el-divider>
                    <el-form-item>
                        <el-button
                            type="danger"
                            @click="handleClearVideos"
                            :loading="isLoading">
                            清理商品描述视频文件
                        </el-button>
                    </el-form-item>
                </el-form>
            </el-card>

            <!-- 自动清理配置 -->
            <el-card v-show="activeMenu === 'auto'" shadow="never">
                <template #header>
                    <div class="card-header">
                        <h3>自动清理配置</h3>
                    </div>
                </template>

                <el-form :model="autoForm" label-width="120px">
                    <el-form-item label="启用状态：">
                        <el-switch
                            v-model="autoForm.video_status"
                            :active-value="1"
                            :inactive-value="0"
                        />
                        <div class="el-form-item-description">
                            启用后将按照设定的时间自动清理商品描述中的视频文件
                        </div>
                    </el-form-item>

                    <el-form-item label="清理天数：">
                        <el-input-number
                            v-model="autoForm.video_days"
                            :min="1"
                            :max="365"
                            style="width: 100%"
                        />
                        <div class="el-form-item-description">
                            清理多少天前的视频文件（1-365天）
                        </div>
                    </el-form-item>

                    <el-form-item label="执行时间：">
                        <el-time-picker
                            v-model="autoForm.video_execute_time"
                            format="HH:mm"
                            value-format="HH:mm"
                            placeholder="选择执行时间"
                            style="width: 100%"
                        />
                        <div class="el-form-item-description">
                            每天执行清理的时间点
                        </div>
                    </el-form-item>

                    <el-form-item label="执行间隔：">
                        <el-input-number
                            v-model="autoForm.video_interval"
                            :min="0"
                            :max="30"
                            style="width: 100%"
                        />
                        <div class="el-form-item-description">
                            执行间隔天数，0表示每天执行（0-30天）
                        </div>
                    </el-form-item>

                    <el-form-item label="视频扩展名：">
                        <el-input
                            v-model="autoForm.video_extensions"
                            placeholder="mp4,avi,mov,wmv,flv,mkv,webm,m4v"
                            style="width: 100%"
                        />
                        <div class="el-form-item-description">
                            要清理的视频文件扩展名，多个用逗号分隔
                        </div>
                    </el-form-item>

                    <el-form-item label="文件大小：">
                        <el-row :gutter="10">
                            <el-col :span="11">
                                <el-input-number
                                    v-model="autoForm.video_min_size"
                                    :min="0"
                                    :max="10240"
                                    :precision="2"
                                    placeholder="最小大小(MB)"
                                    style="width: 100%"
                                />
                            </el-col>
                            <el-col :span="2" style="text-align: center;">
                                <span>至</span>
                            </el-col>
                            <el-col :span="11">
                                <el-input-number
                                    v-model="autoForm.video_max_size"
                                    :min="0"
                                    :max="10240"
                                    :precision="2"
                                    placeholder="最大大小(MB)"
                                    style="width: 100%"
                                />
                            </el-col>
                        </el-row>
                        <div class="el-form-item-description">
                            只清理指定大小范围内的视频文件，0表示不限制
                        </div>
                    </el-form-item>

                    <el-form-item>
                        <el-button
                            type="primary"
                            @click="saveAutoConfig"
                            :loading="isLoading">
                            保存配置
                        </el-button>
                    </el-form-item>
                </el-form>
            </el-card>

            <!-- 统计信息 -->
            <el-card v-show="activeMenu === 'stats'" shadow="never">
                <template #header>
                    <div class="card-header">
                        <h3>商品描述视频统计</h3>
                    </div>
                </template>

                <el-form :model="statsForm" label-width="120px">
                    <el-form-item label="用户ID：">
                        <el-autocomplete
                            v-model="statsForm.user_id"
                            :fetch-suggestions="searchUsers"
                            placeholder="请输入用户ID（不输入则统计所有）"
                            style="width: 100%"
                            clearable
                            @select="handleStatsUserSelect"
                            @clear="handleStatsUserClear">
                            <template #default="{ item }">
                                <div>{{ item.label }}</div>
                            </template>
                        </el-autocomplete>
                    </el-form-item>

                    <el-form-item label="日期范围：">
                        <el-date-picker
                            v-model="statsForm.dateRange"
                            type="datetimerange"
                            range-separator="至"
                            start-placeholder="开始日期时间"
                            end-placeholder="结束日期时间"
                            value-format="YYYY-MM-DD HH:mm:ss"
                            format="YYYY-MM-DD HH:mm:ss"
                            :clearable="true"
                            @change="handleStatsDateRangeChange"
                            style="width: 100%">
                        </el-date-picker>
                        <div class="el-form-item-description">
                            可选择精确到秒的日期时间范围，不选择则统计所有时间范围的商品视频
                        </div>
                    </el-form-item>

                    <el-form-item label="视频扩展名：">
                        <el-input
                            v-model="statsForm.extensions"
                            placeholder="mp4,avi,mov,wmv,flv,mkv,webm,m4v"
                            style="width: 100%"
                        />
                        <div class="el-form-item-description">
                            多个扩展名用逗号分隔，留空使用默认配置
                        </div>
                    </el-form-item>

                    <el-form-item>
                        <el-button
                            type="primary"
                            @click="getVideoStats"
                            :loading="isSearching">
                            查询统计
                        </el-button>
                    </el-form-item>

                    <!-- 显示统计结果 -->
                    <div v-if="statsInfo && hasStatsSearched" class="stats-info">
                        <el-alert
                            :title="statsInfo.msg"
                            type="info"
                            :closable="false">
                        </el-alert>

                        <div v-if="statsInfo.data && statsInfo.data.files && statsInfo.data.files.length > 0" style="margin-top: 20px;">
                            <el-table
                                :data="statsInfo.data.files"
                                style="width: 100%"
                                max-height="400"
                                stripe
                                border>
                                <el-table-column
                                    prop="path"
                                    label="文件路径"
                                    min-width="300"
                                    show-overflow-tooltip>
                                    <template #default="scope">
                                        <div class="file-path-cell" :title="scope.row.path">
                                            {{ scope.row.path }}
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                    prop="upload_time_formatted"
                                    label="上传时间"
                                    width="160"
                                    align="center"
                                    sortable>
                                    <template #default="scope">
                                        <div class="upload-time-cell">
                                            {{ scope.row.upload_time_formatted || '未知' }}
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                    prop="formatted_size"
                                    label="文件大小"
                                    width="120"
                                    align="right">
                                    <template #default="scope">
                                        <div class="file-size-cell">
                                            {{ scope.row.formatted_size }}
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>

                        <div v-else-if="statsInfo.data && (!statsInfo.data.files || statsInfo.data.files.length === 0)" style="margin-top: 20px;">
                            <el-empty description="没有找到符合条件的视频文件" />
                        </div>
                    </div>
                </el-form>
            </el-card>
        </div>
    </div>
</div>

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script>
        // Ensure all libraries are loaded
        window.addEventListener('load', function() {
            if (typeof Vue === 'undefined' || typeof ElementPlus === 'undefined') {
                console.error('Required libraries not loaded');
                return;
            }

            const { createApp, ref, reactive, onMounted } = Vue;
            const { ElMessage, ElMessageBox } = ElementPlus;

            // 获取中文语言包
            let zhCn = null;

            // 尝试多种可能的引用方式
            if (typeof ElementPlusLocaleZhCn !== 'undefined') {
                zhCn = ElementPlusLocaleZhCn.default || ElementPlusLocaleZhCn;
            } else if (window.ElementPlusLocaleZhCn) {
                zhCn = window.ElementPlusLocaleZhCn.default || window.ElementPlusLocaleZhCn;
            } else if (window.ElementPlusLocale && window.ElementPlusLocale.zhCn) {
                zhCn = window.ElementPlusLocale.zhCn;
            }

            // 如果还是没有找到，尝试手动创建中文配置
            if (!zhCn) {
                zhCn = {
                    name: 'zh-cn',
                    el: {
                        colorpicker: {
                            confirm: '确定',
                            clear: '清空'
                        },
                        datepicker: {
                            now: '此刻',
                            today: '今天',
                            cancel: '取消',
                            clear: '清空',
                            confirm: '确定',
                            selectDate: '选择日期',
                            selectTime: '选择时间',
                            startDate: '开始日期',
                            startTime: '开始时间',
                            endDate: '结束日期',
                            endTime: '结束时间',
                            prevYear: '前一年',
                            nextYear: '后一年',
                            prevMonth: '上个月',
                            nextMonth: '下个月',
                            year: '年',
                            month1: '1 月',
                            month2: '2 月',
                            month3: '3 月',
                            month4: '4 月',
                            month5: '5 月',
                            month6: '6 月',
                            month7: '7 月',
                            month8: '8 月',
                            month9: '9 月',
                            month10: '10 月',
                            month11: '11 月',
                            month12: '12 月',
                            weeks: {
                                sun: '日',
                                mon: '一',
                                tue: '二',
                                wed: '三',
                                thu: '四',
                                fri: '五',
                                sat: '六'
                            },
                            months: {
                                jan: '一月',
                                feb: '二月',
                                mar: '三月',
                                apr: '四月',
                                may: '五月',
                                jun: '六月',
                                jul: '七月',
                                aug: '八月',
                                sep: '九月',
                                oct: '十月',
                                nov: '十一月',
                                dec: '十二月'
                            }
                        },
                        select: {
                            loading: '加载中',
                            noMatch: '无匹配数据',
                            noData: '无数据',
                            placeholder: '请选择'
                        },
                        cascader: {
                            noMatch: '无匹配数据',
                            loading: '加载中',
                            placeholder: '请选择',
                            noData: '暂无数据'
                        },
                        pagination: {
                            goto: '前往',
                            pagesize: '条/页',
                            total: '共 {total} 条',
                            pageClassifier: '页',
                            page: '页',
                            prev: '上一页',
                            next: '下一页',
                            currentPage: '第 {pager} 页',
                            prevPages: '向前 {pager} 页',
                            nextPages: '向后 {pager} 页'
                        },
                        messagebox: {
                            title: '提示',
                            confirm: '确定',
                            cancel: '取消',
                            error: '输入的数据不合法!'
                        },
                        upload: {
                            deleteTip: '按 delete 键可删除',
                            delete: '删除',
                            preview: '查看图片',
                            continue: '继续上传'
                        },
                        table: {
                            emptyText: '暂无数据',
                            confirmFilter: '筛选',
                            resetFilter: '重置',
                            clearFilter: '全部',
                            sumText: '合计'
                        },
                        tree: {
                            emptyText: '暂无数据'
                        },
                        transfer: {
                            noMatch: '无匹配数据',
                            noData: '无数据',
                            titles: ['列表 1', '列表 2'],
                            filterPlaceholder: '请输入搜索内容',
                            noCheckedFormat: '共 {total} 项',
                            hasCheckedFormat: '已选 {checked}/{total} 项'
                        },
                        image: {
                            error: '加载失败'
                        },
                        pageHeader: {
                            title: '返回'
                        },
                        popconfirm: {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消'
                        },
                        empty: {
                            description: '暂无数据'
                        }
                    }
                };
            }

            console.log('中文语言包状态:', zhCn ? '已加载' : '未加载');

            const app = createApp({
            setup() {
                // 响应式数据
                const activeMenu = ref('manual');
                const isLoading = ref(false);
                const isSearching = ref(false);
                const hasSearched = ref(false);
                const hasStatsSearched = ref(false);

                // 表单数据
                const manualForm = reactive({
                    user_id: '',
                    dateRange: null,
                    extensions: '',
                    min_size: 0,
                    max_size: 0
                });

                const autoForm = reactive({
                    video_status: 0,
                    video_days: 30,
                    video_execute_time: '00:00',
                    video_interval: 1,
                    video_extensions: 'mp4,avi,mov,wmv,flv,mkv,webm,m4v',
                    video_max_size: 100,
                    video_min_size: 0
                });

                const statsForm = reactive({
                    user_id: '',
                    dateRange: null,
                    extensions: ''
                });

                // 统计数据
                const videoStats = ref(null);
                const statsInfo = ref(null);

                // 菜单选择
                const handleMenuSelect = (key) => {
                    activeMenu.value = key;
                    if (key === 'auto') {
                        fetchAutoConfig();
                    }
                };

                // 用户搜索
                const searchUsers = async (queryString, cb) => {
                    if (!queryString || !queryString.trim()) {
                        cb([]);
                        return;
                    }

                    try {
                        const response = await axios.post('./searchUsers', {
                            keyword: queryString.trim()
                        });

                        if (response.data?.code === 200) {
                            cb(response.data.data || []);
                        } else {
                            cb([]);
                        }
                    } catch (error) {
                        console.error('搜索用户失败:', error);
                        cb([]);
                    }
                };

                // 用户选择处理
                const handleUserSelect = (item) => {
                    manualForm.user_id = item.value;
                };

                const handleUserClear = () => {
                    manualForm.user_id = '';
                };

                const handleStatsUserSelect = (item) => {
                    statsForm.user_id = item.value;
                };

                const handleStatsUserClear = () => {
                    statsForm.user_id = '';
                };

                // 日期范围处理
                const handleDateRangeChange = (value) => {
                    manualForm.dateRange = value;
                };

                const handleStatsDateRangeChange = (value) => {
                    statsForm.dateRange = value;
                };

                // 查询视频统计
                const searchVideoStats = async () => {
                    try {
                        isSearching.value = true;
                        hasSearched.value = true;

                        let userId = 0;
                        if (manualForm.user_id) {
                            // 清理输入值，移除空格
                            const cleanUserId = String(manualForm.user_id).trim();
                            if (cleanUserId) {
                                userId = parseInt(cleanUserId, 10);
                                if (isNaN(userId) || userId <= 0) {
                                    ElMessage.warning('请输入有效的数字ID');
                                    hasSearched.value = false;
                                    isSearching.value = false;
                                    return;
                                }
                            }
                        }

                        const res = await axios.post("./getGoodsDescriptionVideoStats", {
                            user_id: userId,
                            start_date: manualForm.dateRange ? manualForm.dateRange[0] : '',
                            end_date: manualForm.dateRange ? manualForm.dateRange[1] : '',
                            extensions: manualForm.extensions
                        });

                        if (res.data?.code === 200) {
                            videoStats.value = res.data;
                        } else {
                            ElMessage.error(res.data?.msg || '查询失败');
                        }
                    } catch (error) {
                        ElMessage.error('查询统计失败');
                    } finally {
                        isSearching.value = false;
                    }
                };

                // 清理视频文件
                const handleClearVideos = async () => {
                    try {
                        // 验证用户ID格式（如果提供了的话）
                        if (manualForm.user_id) {
                            const cleanUserId = String(manualForm.user_id).trim();
                            if (cleanUserId) {
                                const userId = parseInt(cleanUserId, 10);
                                if (isNaN(userId) || userId <= 0) {
                                    ElMessage.warning('请输入有效的数字ID');
                                    return;
                                }
                            }
                        }

                        const confirmMessage = manualForm.user_id
                            ? `确定要清理用户ID ${manualForm.user_id} 的商品描述视频文件吗？`
                            : '确定要清理所有用户的商品描述视频文件吗？此操作不可恢复！';

                        await ElMessageBox.confirm(
                            confirmMessage,
                            '警告',
                            {
                                confirmButtonText: '确定清理',
                                cancelButtonText: '取消',
                                type: 'warning'
                            }
                        );

                        isLoading.value = true;
                        const params = {
                            user_id: manualForm.user_id || 0,
                            start_date: manualForm.dateRange ? manualForm.dateRange[0] : '',
                            end_date: manualForm.dateRange ? manualForm.dateRange[1] : '',
                            extensions: manualForm.extensions,
                            min_size: manualForm.min_size || 0,
                            max_size: manualForm.max_size || 0
                        };

                        const res = await axios.post("./clearGoodsDescriptionVideos", params);

                        if (res.data?.code === 200) {
                            ElMessage.success(res.data.msg);
                            // 清理成功后重置表单
                            manualForm.user_id = '';
                            manualForm.dateRange = null;
                            videoStats.value = null;
                            hasSearched.value = false;
                        } else {
                            ElMessage.error(res.data?.msg || '清理失败');
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            ElMessage.error(error.response?.data?.msg || '操作失败');
                        }
                    } finally {
                        isLoading.value = false;
                    }
                };

                // 获取自动配置
                const fetchAutoConfig = async () => {
                    try {
                        const response = await axios.post('./fetchData');
                        if (response.data?.code === 1) {
                            Object.assign(autoForm, response.data.data);
                        }
                    } catch (error) {
                        console.error('获取配置失败:', error);
                    }
                };

                // 保存自动配置
                const saveAutoConfig = async () => {
                    try {
                        isLoading.value = true;

                        const response = await axios.post('./saveVideoConfig', {
                            status: autoForm.video_status,
                            days: autoForm.video_days,
                            executeTime: autoForm.video_execute_time,
                            interval: autoForm.video_interval,
                            extensions: autoForm.video_extensions,
                            minSize: autoForm.video_min_size,
                            maxSize: autoForm.video_max_size
                        });

                        if (response.data?.code === 1) {
                            ElMessage.success(response.data.msg);
                        } else {
                            ElMessage.error(response.data?.msg || '保存失败');
                        }
                    } catch (error) {
                        ElMessage.error('保存配置失败');
                    } finally {
                        isLoading.value = false;
                    }
                };

                // 获取统计信息
                const getVideoStats = async () => {
                    try {
                        isSearching.value = true;
                        hasStatsSearched.value = true;

                        let userId = 0;
                        if (statsForm.user_id) {
                            // 清理输入值，移除空格
                            const cleanUserId = String(statsForm.user_id).trim();
                            if (cleanUserId) {
                                userId = parseInt(cleanUserId, 10);
                                if (isNaN(userId) || userId <= 0) {
                                    ElMessage.warning('请输入有效的数字ID');
                                    hasStatsSearched.value = false;
                                    isSearching.value = false;
                                    return;
                                }
                            }
                        }

                        const res = await axios.post("./getGoodsDescriptionVideoStats", {
                            user_id: userId,
                            start_date: statsForm.dateRange ? statsForm.dateRange[0] : '',
                            end_date: statsForm.dateRange ? statsForm.dateRange[1] : '',
                            extensions: statsForm.extensions
                        });

                        if (res.data?.code === 200) {
                            statsInfo.value = res.data;
                        } else {
                            ElMessage.error(res.data?.msg || '查询失败');
                            hasStatsSearched.value = false;
                        }
                    } catch (error) {
                        console.error('查询统计失败:', error);
                        ElMessage.error('查询统计失败');
                        hasStatsSearched.value = false;
                    } finally {
                        isSearching.value = false;
                    }
                };

                // 组件挂载时获取配置
                onMounted(() => {
                    fetchAutoConfig();
                });

                // 返回所有需要的数据和方法
                return {
                    activeMenu,
                    isLoading,
                    isSearching,
                    hasSearched,
                    hasStatsSearched,
                    manualForm,
                    autoForm,
                    statsForm,
                    videoStats,
                    statsInfo,
                    handleMenuSelect,
                    searchUsers,
                    handleUserSelect,
                    handleUserClear,
                    handleStatsUserSelect,
                    handleStatsUserClear,
                    handleDateRangeChange,
                    handleStatsDateRangeChange,
                    searchVideoStats,
                    handleClearVideos,
                    fetchAutoConfig,
                    saveAutoConfig,
                    getVideoStats
                };
            }
        });

        // 配置Element Plus使用中文
        if (zhCn) {
            console.log('使用中文语言包配置Element Plus');
            app.use(ElementPlus, {
                locale: zhCn,
            });
        } else {
            console.warn('中文语言包未加载，使用默认语言');
            app.use(ElementPlus);
        }
        app.mount("#app");
        });
    </script>
</body>
</html>
