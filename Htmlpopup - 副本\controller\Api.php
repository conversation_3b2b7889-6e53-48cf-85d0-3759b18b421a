<?php

namespace plugin\Htmlpopup\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;

class Api extends BasePlugin {

    protected $noNeedLogin = ['fetchData'];

    // 添加场景配置
    protected $scene = [
        'user'
    ];

    public function index() {
        // 删除所有编辑器配置，直接返回视图
        return View::fetch();
    }

    public function fetchData() {
        try {
            // 添加防止缓存的响应头
            header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
            header('Cache-Control: post-check=0, pre-check=0', false);
            header('Pragma: no-cache');

            // 获取请求参数中的商家信息
            $shop_name = $this->request->param('shop_name', '', 'trim');
            $request_merchant_id = $this->request->param('merchant_id', 0, 'intval');
            $merchant_id = 0;

            // 优先使用URL请求参数中的商家ID
            if ($request_merchant_id > 0) {
                $merchant_id = $request_merchant_id;
            }
            // 其次通过店铺名称查找商家ID
            else if (!empty($shop_name)) {
                $merchant_id = $this->getMerchantIdByShopName($shop_name);
            }
            // 最后才使用当前登录用户的ID
            else if ($this->user && $this->user->id) {
                $merchant_id = $this->user->id;
            }

            if (!$merchant_id) {
                return json(['code' => 0, 'msg' => '未找到商家信息']);
            }

            // 检查全局弹窗开关
            $globalPopupEnabled = (bool)(plugconf('Htmlpopup.global_popup_enabled') ?? true);
            if (!$globalPopupEnabled) {
                return json(['code' => 200, 'msg' => 'success', 'data' => ['status' => 0]]);
            }

            // 获取商家的内容配置
            $contentType = merchant_plugconf($merchant_id, "Htmlpopup.content_type") ?? 'custom';
            $builtinTemplate = merchant_plugconf($merchant_id, "Htmlpopup.builtin_template");

            // 如果商家没有选择模板，使用管理员设置的默认模板
            if (empty($builtinTemplate)) {
                $builtinTemplate = plugconf('Htmlpopup.global_default_template') ?? 'purchase_agreement';
            }

            // 强制使用内置模板模式
            $contentType = 'builtin';

            // 获取商家的自定义内容
            $customContent = merchant_plugconf($merchant_id, "Htmlpopup.content") ?? '';

            // 如果商家没有自定义内容且没有明确选择内容类型，自动使用内置模板
            if (empty($customContent) && !merchant_plugconf($merchant_id, "Htmlpopup.content_type")) {
                $contentType = 'builtin';
            }

            // 从全局配置获取弹窗设置
            $params = [
                'status' => 1, // 状态由全局开关控制，这里固定为1
                'content' => $customContent,
                'frequency' => plugconf('Htmlpopup.global_frequency') ?? 'once',
                'title' => plugconf('Htmlpopup.global_title') ?? 'HTML弹窗',
                'button_color' => plugconf('Htmlpopup.global_button_color') ?? 'blue',
                'reject_action' => plugconf('Htmlpopup.global_reject_action') ?? 'none',
                'reject_url' => plugconf('Htmlpopup.global_reject_url') ?? '',
                'close_confirm' => intval(plugconf('Htmlpopup.global_close_confirm') ?? 0),
                'content_type' => $contentType,
                'builtin_template' => $builtinTemplate,
                'template_fields' => merchant_plugconf($merchant_id, "Htmlpopup.template_fields") ?? ''
            ];

            // 如果是内置模板且不是预设的可编辑模板，获取管理员自定义模板内容
            if ($contentType === 'builtin' && !in_array($builtinTemplate, ['purchase_agreement', 'platform_rules'])) {
                $templates = $this->getBuiltinTemplates();
                if (isset($templates[$builtinTemplate])) {
                    $params['builtin_template_content'] = $templates[$builtinTemplate]['content'];
                }
            }

            return json(['code' => 200, 'msg' => 'success', 'data' => $params]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取数据失败']);
        }
    }

    // 添加通过店铺名称查找商家ID的方法
    protected function getMerchantIdByShopName($shop_name) {
        if (empty($shop_name)) {
            return 0;
        }

        try {
            // 使用与 Storeannouncements 相同的查询逻辑
            $merchant = \think\facade\Db::name('user')
                ->where('nickname', $shop_name)
                ->value('id');

            return intval($merchant);
        } catch (\Exception $e) {
            return 0;
        }
    }

    public function save() {
        try {
            if (!$this->user || !$this->user->id) {
                return json(['code' => 403, 'msg' => '请先登录']);
            }

            // 获取要保存配置的商家ID
            $merchant_id = $this->request->post('merchant_id', 0, 'intval');

            // 如果没有指定商家ID，使用当前登录用户的ID
            if (!$merchant_id) {
                $merchant_id = $this->user->id;
            }

            // 权限检查：只允许用户修改自己的设置，管理员除外
            if ($merchant_id != $this->user->id && !$this->user->isAdmin) {
                return json(['code' => 403, 'msg' => '您无权修改其他商家的设置']);
            }

            // 只获取商家可以配置的字段
            $content = $this->request->post('content', '', 'trim');
            $content_type = $this->request->post('content_type', 'custom', 'trim');
            $builtin_template = $this->request->post('builtin_template', 'purchase_agreement', 'trim');
            $template_fields = $this->request->post('template_fields', '', 'trim');



            // 验证内容类型
            if (!in_array($content_type, ['custom', 'builtin'])) {
                $content_type = 'custom';
            }

            // 验证内置模板
            $availableTemplates = array_keys($this->getBuiltinTemplates());
            if (!in_array($builtin_template, $availableTemplates)) {
                $builtin_template = $availableTemplates[0] ?? 'purchase_agreement';
            }

            // 处理内容
            if ($content_type === 'builtin') {
                // 内置模板模式：不保存生成的HTML内容到content字段
                // 内容将在前端通过getBuiltinTemplate()动态生成
                $content = '';
            } else {
                // 自定义内容安全检查
                if ($content !== '' && !is_string($content)) {
                    return json(['code' => 0, 'msg' => '弹窗内容格式不正确']);
                }

                // 对于HTML弹窗内容，我们需要保留所有样式，只过滤危险的脚本
                if ($content) {
                    // 只移除script标签和javascript:协议，保留所有其他HTML和CSS
                    $content = $this->safeHtmlFilter($content);
                }
            }

            // 保存商家配置（只保存内容相关的配置）
            merchant_plugconf($merchant_id, "Htmlpopup.content", $content);
            merchant_plugconf($merchant_id, "Htmlpopup.content_type", $content_type);
            merchant_plugconf($merchant_id, "Htmlpopup.builtin_template", $builtin_template);
            merchant_plugconf($merchant_id, "Htmlpopup.template_fields", $template_fields);

            return json(['code' => 200, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '保存失败']);
        }
    }

    /**
     * 宽松的HTML过滤函数，支持复杂HTML布局，只移除最危险的JavaScript内容
     */
    private function safeHtmlFilter($html) {
        // 移除script标签
        $html = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $html);

        // 移除javascript:协议
        $html = preg_replace('/javascript:/i', '', $html);

        // 移除vbscript:协议
        $html = preg_replace('/vbscript:/i', '', $html);

        // 只移除明显的事件属性，保留其他所有属性
        $html = preg_replace('/\s*on(click|load|mouseover|mouseout|focus|blur|change|submit)\s*=\s*["\'][^"\']*["\']/i', '', $html);

        // 移除style属性中的expression (IE特有的危险功能)
        $html = preg_replace('/expression\s*\([^)]*\)/i', '', $html);

        // 保留所有其他HTML标签、属性和CSS样式，包括：
        // - 所有HTML标签 (div, span, p, a, img, table等)
        // - 所有CSS属性 (style, class, id等)
        // - 所有布局属性 (width, height, margin, padding等)
        // - 所有颜色和字体属性
        return $html;
    }

    /**
     * 获取内置模板内容
     */
    private function getBuiltinTemplateContent($template, $template_fields = '') {
        // 解析用户自定义字段
        $fields = [];
        if (!empty($template_fields)) {
            $fields = json_decode($template_fields, true) ?: [];
        }

        // 设置默认字段值
        $defaultFields = [
            // 购买协议模板字段
            'platformName' => '小火羊云寄售官方频道',
            'platformChannel' => '@xhyfkw',
            'warmTip' => '本站不提供任何担保、私下交易被骗一律与本站无关。',
            'shopLink' => 'https://www.zzrongtong.cn/merchant/',
            'serviceLink' => 'https://work.weixin.qq.com/kfid/kfcf72f7282e80c5ad2',
            'shopButtonText' => '开店成为商家赚米',
            'serviceButtonText' => '联系平台微信客服',
            // 平台规则模板字段
            'rulesTitle' => '平台使用规则',
            'userNotice' => "1. 请遵守平台相关规定，文明使用平台服务\n2. 禁止发布违法违规内容，维护良好的平台环境\n3. 保护个人隐私信息，谨防诈骗",
            'tradeRules' => "1. 所有交易请通过平台正规渠道进行\n2. 如遇问题请及时联系客服处理\n3. 平台将保障用户合法权益",
            'serviceNotice' => '如有疑问，请联系平台客服'
        ];

        // 合并用户字段和默认字段
        $fields = array_merge($defaultFields, $fields);

        // 获取管理员配置的模板
        $adminTemplates = $this->getBuiltinTemplates();

        // 如果是管理员自定义的模板，直接返回其内容（不支持字段替换）
        if (isset($adminTemplates[$template]) && !in_array($template, ['purchase_agreement', 'platform_rules'])) {
            return $adminTemplates[$template]['content'];
        }

        // 根据模板类型生成内容（仅对内置的两个模板支持字段替换）
        if ($template === 'purchase_agreement') {
            return '
                <div id="buy-protocol">
                    <p><strong><span style="color:#303133;"><span style="font-size:18px;">' . htmlspecialchars($fields['platformName']) . htmlspecialchars($fields['platformChannel']) . '</span></span></strong></p>
                    <p><strong><span style="color:#ff6600;"><span style="font-size:18px;">温馨提示：' . htmlspecialchars($fields['warmTip']) . '</span></span></strong></p>
                    <p><span style="color:#ff00ff;">1、本平台仅提供自动发卡、寄售服务，本平台非售卖销售商，有问题联系店铺客服或者平台投诉订单处理，非商品卡密问题本站不予受理售后争议</span></p>
                    <p><span style="color:#27ae60;">2、平台提供卡密查询时间为1个月（购买后请自行保存）如遇假卡/欺诈/涉黄赌/等违规商品请当天23点50前去平台投诉订单处理，若是超过24点后，平台放款给商家，不再受理任何问题。</span></p>
                    <p><span style="color:#ae2760;">3、为了方便售后建议您的每次购物都录个视频，一直到商品正常使用没问题在取消录制哦！</span></p>
                    <p><span style="color:#ff0000;">若遇到任何无法处理的问题，请当天在平台投诉订单，平台官方客服介入处理</span></p>
                    <div style="text-align: center; white-space: nowrap;">
                        <a href="' . htmlspecialchars($fields['shopLink']) . '" style="display: inline-block; width: 45%; background-color: #6600ff; color: white; border: none; padding: 10px; margin: 0 2.5%; border-radius: 5px; text-decoration: none; cursor: pointer;">' . htmlspecialchars($fields['shopButtonText']) . '</a>
                        <a href="' . htmlspecialchars($fields['serviceLink']) . '" style="display: inline-block; width: 45%; background-color: #6600ff; color: white; border: none; padding: 10px; margin: 0 2.5%; border-radius: 5px; text-decoration: none; cursor: pointer;">' . htmlspecialchars($fields['serviceButtonText']) . '</a>
                    </div>
                    <hr style="border: none; border-top: 1px dashed #000; margin: 20px 0;">
                    <p><span style="font-size:16px;"><strong><span style="color:#ff0000;">防骗提醒</span></strong><span style="color:#ff0000;">：</span></span><span style="color:#6600ff;">1.卡密内容为"联系QQ取卡或者QQ群拿货" 2.以各种理由推脱到第二天发货 3.商品有问题，卖家不售后 4.承诺充值返现 5.购买的商品为实物，需要快递发货的。请及时联系我们平台客服</span></p>
                </div>
            ';
        } else if ($template === 'platform_rules') {
            // 处理用户须知和交易规则的换行
            $userNoticeLines = explode("\n", $fields['userNotice']);
            $userNoticeHtml = '';
            foreach ($userNoticeLines as $index => $line) {
                $line = trim($line);
                if (!empty($line)) {
                    $line = preg_replace('/^\d+\.\s*/', '', $line); // 移除开头的数字
                    $userNoticeHtml .= '<p>' . ($index + 1) . '. ' . htmlspecialchars($line) . '</p>';
                }
            }

            $tradeRulesLines = explode("\n", $fields['tradeRules']);
            $tradeRulesHtml = '';
            foreach ($tradeRulesLines as $index => $line) {
                $line = trim($line);
                if (!empty($line)) {
                    $line = preg_replace('/^\d+\.\s*/', '', $line); // 移除开头的数字
                    $tradeRulesHtml .= '<p>' . ($index + 1) . '. ' . htmlspecialchars($line) . '</p>';
                }
            }

            return '
                <div style="padding: 20px; line-height: 1.6;">
                    <h3 style="color: #333; text-align: center; margin-bottom: 20px;">' . htmlspecialchars($fields['rulesTitle']) . '</h3>
                    <div style="background: #f0f8ff; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                        <h4 style="color: #1e90ff; margin-top: 0;">用户须知</h4>
                        ' . $userNoticeHtml . '
                    </div>
                    <div style="background: #fff5ee; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                        <h4 style="color: #ff6347; margin-top: 0;">交易规则</h4>
                        ' . $tradeRulesHtml . '
                    </div>
                    <div style="text-align: center; margin-top: 20px;">
                        <p style="color: #666;">' . htmlspecialchars($fields['serviceNotice']) . '</p>
                    </div>
                </div>
            ';
        }

        // 如果没有找到指定模板，返回默认模板
        $defaultTemplates = $this->getDefaultTemplates();
        return $defaultTemplates[$template]['content'] ?? $defaultTemplates['purchase_agreement']['content'];
    }

    /**
     * 获取管理员配置的内置模板
     */
    private function getBuiltinTemplates() {
        $templatesJson = plugconf('Htmlpopup.builtin_templates');

        if (empty($templatesJson)) {
            // 返回默认模板
            return $this->getDefaultTemplates();
        }

        // 解码JSON数据
        $templates = json_decode($templatesJson, true);

        // 检查JSON解码是否成功
        if (json_last_error() !== JSON_ERROR_NONE) {
            // JSON解码失败，返回默认模板
            return $this->getDefaultTemplates();
        }

        // 确保返回的是数组
        if (!is_array($templates)) {
            return $this->getDefaultTemplates();
        }

        return $templates;
    }

    /**
     * 获取默认模板（用于初始化或备用）
     */
    private function getDefaultTemplates() {
        return [
            'purchase_agreement' => [
                'name' => '购买协议模板',
                'content' => '
                    <div id="buy-protocol">
                        <p><strong><span style="color:#303133;"><span style="font-size:18px;">小火羊云寄售官方频道@xhyfkw</span></span></strong></p>
                        <p><strong><span style="color:#ff6600;"><span style="font-size:18px;">温馨提示：本站不提供任何担保、私下交易被骗一律与本站无关。</span></span></strong></p>
                        <p><span style="color:#ff00ff;">1、本平台仅提供自动发卡、寄售服务，本平台非售卖销售商，有问题联系店铺客服或者平台投诉订单处理，非商品卡密问题本站不予受理售后争议</span></p>
                        <p><span style="color:#27ae60;">2、平台提供卡密查询时间为1个月（购买后请自行保存）如遇假卡/欺诈/涉黄赌/等违规商品请当天23点50前去平台投诉订单处理，若是超过24点后，平台放款给商家，不再受理任何问题。</span></p>
                        <p><span style="color:#ae2760;">3、为了方便售后建议您的每次购物都录个视频，一直到商品正常使用没问题在取消录制哦！</span></p>
                        <p><span style="color:#ff0000;">若遇到任何无法处理的问题，请当天在平台投诉订单，平台官方客服介入处理</span></p>
                        <div style="text-align: center; white-space: nowrap;">
                            <a href="https://www.zzrongtong.cn/merchant/" style="display: inline-block; width: 45%; background-color: #6600ff; color: white; border: none; padding: 10px; margin: 0 2.5%; border-radius: 5px; text-decoration: none; cursor: pointer;">开店成为商家赚米</a>
                            <a href="https://work.weixin.qq.com/kfid/kfcf72f7282e80c5ad2" style="display: inline-block; width: 45%; background-color: #6600ff; color: white; border: none; padding: 10px; margin: 0 2.5%; border-radius: 5px; text-decoration: none; cursor: pointer;">联系平台微信客服</a>
                        </div>
                        <hr style="border: none; border-top: 1px dashed #000; margin: 20px 0;">
                        <p><span style="font-size:16px;"><strong><span style="color:#ff0000;">防骗提醒</span></strong><span style="color:#ff0000;">：</span></span><span style="color:#6600ff;">1.卡密内容为"联系QQ取卡或者QQ群拿货" 2.以各种理由推脱到第二天发货 3.商品有问题，卖家不售后 4.承诺充值返现 5.购买的商品为实物，需要快递发货的。请及时联系我们平台客服</span></p>
                    </div>
                '
            ],
            'platform_rules' => [
                'name' => '平台规则模板',
                'content' => '
                    <div style="padding: 20px; line-height: 1.6;">
                        <h3 style="color: #333; text-align: center; margin-bottom: 20px;">平台使用规则</h3>
                        <div style="background: #f0f8ff; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                            <h4 style="color: #1e90ff; margin-top: 0;">用户须知</h4>
                            <p>1. 请遵守平台相关规定，文明使用平台服务</p>
                            <p>2. 禁止发布违法违规内容，维护良好的平台环境</p>
                            <p>3. 保护个人隐私信息，谨防诈骗</p>
                        </div>
                        <div style="background: #fff5ee; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                            <h4 style="color: #ff6347; margin-top: 0;">交易规则</h4>
                            <p>1. 所有交易请通过平台正规渠道进行</p>
                            <p>2. 如遇问题请及时联系客服处理</p>
                            <p>3. 平台将保障用户合法权益</p>
                        </div>
                        <div style="text-align: center; margin-top: 20px;">
                            <p style="color: #666;">如有疑问，请联系平台客服</p>
                        </div>
                    </div>
                '
            ]
        ];
    }

    /**
     * 获取可用的内置模板列表（供前端选择使用）
     */
    public function getAvailableTemplates() {
        try {
            $templates = $this->getBuiltinTemplates();



            // 只返回模板的基本信息，不包含完整内容
            $templateList = [];
            foreach ($templates as $key => $template) {
                $templateList[] = [
                    'value' => $key,
                    'label' => $template['name']
                ];
            }

            return json(['code' => 200, 'data' => $templateList]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取模板列表失败']);
        }
    }

    /**
     * 获取特定内置模板的内容（API接口）
     */
    public function getTemplateContent() {
        try {
            $templateKey = input('templateKey');
            if (empty($templateKey)) {
                return json(['code' => 0, 'msg' => '模板标识不能为空']);
            }

            $templates = $this->getBuiltinTemplates();

            if (!isset($templates[$templateKey])) {
                return json(['code' => 0, 'msg' => '模板不存在']);
            }

            return json([
                'code' => 200,
                'data' => [
                    'key' => $templateKey,
                    'name' => $templates[$templateKey]['name'],
                    'content' => $templates[$templateKey]['content']
                ]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取模板内容失败']);
        }
    }
}
