<?php
namespace plugin\Shopnameupdate;

use think\facade\Db;
use think\facade\Cache;

class Hook
{
    public function handle()
    {
        // 检查全局自动同步功能状态
        $autoSyncStatus = intval(merchant_plugconf(0, "Shopnameupdate.auto_sync") ?? 0);
        if ($autoSyncStatus !== 1) {
            return; // 如果自动同步功能关闭，则不执行同步
        }

        $this->autoSync();
    }

    private function autoSync()
    {
        $prefix = config('database.connections.mysql.prefix');
        $goodsTable = $prefix . 'goods';

        // 查询所有父商品
        $parentGoods = Db::table($goodsTable)
            ->where('parent_id', 0)
            ->field('id, user_id, name, update_time')
            ->select()
            ->toArray();

        foreach ($parentGoods as $parent) {
            // 检查商家是否启用了同步功能
            $merchantStatus = intval(merchant_plugconf($parent['user_id'], "Shopnameupdate.status") ?? 0);
            if ($merchantStatus !== 1) {
                continue; // 如果商家未启用同步功能，则跳过
            }

            // 获取缓存的商品信息
            $cacheKey = "shopnameupdate_goods_{$parent['id']}";
            $cachedInfo = Cache::get($cacheKey);

            // 如果缓存不存在或更新时间不同，则需要同步
            if (!$cachedInfo || $cachedInfo['update_time'] != $parent['update_time']) {
                // 更新子商品
                $affected = Db::table($goodsTable)
                    ->where([
                        ['parent_id', '=', $parent['id']],
                        ['name', '<>', $parent['name']]
                    ])
                    ->update([
                        'name' => $parent['name'],
                        'update_time' => $parent['update_time']
                    ]);

                // 更新缓存
                Cache::set($cacheKey, [
                    'name' => $parent['name'],
                    'update_time' => $parent['update_time']
                ]);
            }
        }
    }
}