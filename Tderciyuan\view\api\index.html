<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>二次元看板娘设置</title>
    <script>
        // 这里可以从后端模板变量中获取
        window.merchant_id = '{$merchant_id|default=0}'; // ThinkPHP 模板语法
        window.shop_name = '{$shop_name|default=""}';
    </script>
    <style>
        /* 基础样式 */
        body {
            background: #f5f7fa;
            margin: 0;
            padding: 16px;
            min-height: 500px;
            box-sizing: border-box;
        }

        .el-card {
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05) !important;
            margin: 0 auto;
            max-width: 900px;
        }

        /* 响应式布局 */
        @media screen and (max-width: 768px) {
            body {
                padding: 10px;
            }

            .el-card {
                margin: 0;
                border-radius: 8px;
            }

            .el-form {
                padding: 10px;
            }

            .el-form-item {
                margin-bottom: 24px;
            }

            /* 调整标签宽度 */
            .el-form {
                --el-form-label-width: 90px !important;
            }

            .el-form-item__label {
                float: none;
                display: block;
                text-align: left;
                padding: 0 0 8px 0;
                line-height: 1.4;
                white-space: normal;
            }

            .el-form-item__content {
                margin-left: 0 !important;
            }

            /* 调整数字输入框组的布局 */
            div[style*="display: flex; align-items: center;"] {
                flex-wrap: wrap;
                gap: 8px;
            }

            div[style*="display: flex; align-items: center;"] > span {
                min-width: 60px;
            }

            .el-input-number {
                width: calc(50% - 50px) !important;
                margin: 0 !important;
            }
        }

        /* 超小屏幕优化 */
        @media screen and (max-width: 375px) {
            body {
                padding: 8px;
            }
            .el-form-item__label {
                font-size: 14px;
            }
            .el-form-item-msg {
                font-size: 11px;
            }
            div[style*="display: flex; align-items: center;"] {
                flex-direction: column;
                align-items: flex-start !important;
            }
            .el-input-number {
                width: 100% !important;
            }
        }

        /* 暗黑模式支持 */
        @media (prefers-color-scheme: dark) {
            body {
                background: #1a1a1a;
            }
            .el-card {
                background: #2b2b2b;
            }
        }

        /* 触摸优化 */
        @media (hover: none) {
            .el-button,
            .el-switch,
            .action-icon {
                min-height: 44px;
            }
        }

        /* 自定义模型输入框 */
        .custom-model-input {
            margin-top: 10px;
            padding: 10px;
            background: #f0f9eb;
            border-radius: 4px;
            border-left: 3px solid #67c23a;
            margin-bottom: 10px;
        }

        /* 尺寸设置 */
        .size-inputs {
            display: flex !important;
            flex-wrap: wrap;
            gap: 8px;
            align-items: center !important;
            width: 100%;
        }
        .size-input-group {
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;
            min-width: 200px;
        }
        .size-input-group span {
            min-width: 45px;
        }
        .size-input-group .el-input-number {
            flex: 1;
            max-width: 160px;
        }

        /* 表单提示信息 */
        .el-form-item-msg {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }



        /* 表单控件样式 */
        .el-form-item:last-child {
            margin-bottom: 0;
        }
        
        /* 输入框样式优化 */
        .el-input__inner {
            text-align: left;
        }
        
        /* 表单项间距 */
        .el-form-item {
            margin-bottom: 22px;
        }
    </style>
</head>

<body>
    <div id="app">
        <el-card shadow="never">
            <el-form :model="form" label-width="120px">
                <!-- 看板娘开关 -->
                <el-form-item label="看板娘开关：">
                    <div style="display: flex; align-items: center;">
                        <el-switch v-model="form.status" :active-value="1" :inactive-value="0" :disabled="!canEdit" />
                    </div>
                    <div class="el-form-item-msg">开启/关闭网站上的二次元看板娘显示</div>
                </el-form-item>

                <!-- 模型选择 -->
                <el-form-item label="模型选择：">
                    <el-radio-group v-model="form.model_type" @change="handleModelChange" :disabled="!canEdit">
                        <el-radio label="auto">自动加载（推荐）</el-radio>
                        <el-radio label="koharu">Koharu (默认)</el-radio>
                        <el-radio label="shizuku">Shizuku</el-radio>
                        <el-radio label="custom">自定义模型</el-radio>
                    </el-radio-group>

                    <!-- 模型说明 -->
                    <div style="margin-top: 8px; font-size: 12px; color: #909399;">
                        <div v-if="form.model_type === 'auto'">
                            <i class="el-icon-info"></i>
                            使用 live2d-widgets 自动加载，支持多种随机模型，如果加载失败会自动降级到自定义看板娘
                        </div>
                        <div v-else-if="form.model_type === 'koharu'">
                            <i class="el-icon-info"></i>
                            粉色主题的可爱看板娘
                        </div>
                        <div v-else-if="form.model_type === 'shizuku'">
                            <i class="el-icon-info"></i>
                            蓝绿主题的清新看板娘
                        </div>
                        <div v-else-if="form.model_type === 'custom'">
                            <i class="el-icon-info"></i>
                            使用您指定的自定义模型路径
                        </div>
                    </div>
                    
                    <!-- 自定义模型输入框 -->
                    <div v-if="form.model_type === 'custom'" class="custom-model-input">
                        <el-input 
                            v-model="form.custom_model_path" 
                            placeholder="请输入自定义Live2D模型的JSON文件URL"
                            clearable
                            :disabled="!canEdit"
                        />
                        <div class="el-form-item-msg">例如: https://example.com/live2d-models/mymodel/mymodel.model.json</div>
                    </div>
                </el-form-item>

                <!-- 显示位置 -->
                <el-form-item label="显示位置：">
                    <el-radio-group v-model="form.position" :disabled="!canEdit">
                        <el-radio label="left">左侧</el-radio>
                        <el-radio label="right">右侧</el-radio>
                    </el-radio-group>
                </el-form-item>

                <!-- 尺寸设置 -->
                <el-form-item label="尺寸设置：">
                    <div class="size-inputs">
                        <div class="size-input-group">
                            <span>宽度：</span>
                            <el-input-number 
                                v-model="form.width" 
                                :min="100" 
                                :max="400" 
                                :step="10"
                                controls-position="right"
                                :disabled="!canEdit"
                            />
                        </div>
                        <div class="size-input-group">
                            <span>高度：</span>
                            <el-input-number 
                                v-model="form.height" 
                                :min="100" 
                                :max="600" 
                                :step="10"
                                controls-position="right"
                                :disabled="!canEdit"
                            />
                        </div>
                    </div>
                    <div class="el-form-item-msg">模型展示区域大小，调整后会影响看板娘显示的大小</div>
                </el-form-item>

                <!-- 显示模式 -->
                <el-form-item label="显示模式：">
                    <el-radio-group v-model="form.display_mode" :disabled="!canEdit">
                        <el-radio label="normal">标准模式</el-radio>
                        <el-radio label="pure">纯净模式</el-radio>
                    </el-radio-group>

                    <!-- 模式说明 -->
                    <div style="margin-top: 8px; font-size: 12px; color: #909399;">
                        <div v-if="form.display_mode === 'normal'">
                            <i class="el-icon-info"></i>
                            显示看板娘和控制按钮，用户可以关闭和互动
                        </div>
                        <div v-else-if="form.display_mode === 'pure'">
                            <i class="el-icon-info"></i>
                            只显示看板娘模型，无控制按钮，纯净展示
                        </div>
                    </div>
                </el-form-item>

                <!-- 保存按钮 -->
                <el-form-item>
                    <el-button 
                        type="primary" 
                        :loading="loading" 
                        @click="save"
                        :disabled="!canEdit"
                    >
                        保存设置
                    </el-button>
                    <div v-if="!canEdit" class="el-form-item-msg" style="color: #f56c6c; margin-top: 10px;">
                        管理员已禁止商家修改看板娘设置，如需更改请联系管理员
                    </div>
                </el-form-item>
            </el-form>
        </el-card>
    </div>

    <!-- 引入依赖库 -->
    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>

    <script>
        // 全局错误处理
        window.addEventListener('unhandledrejection', function(event) {
            console.error('未处理的Promise拒绝:', event.reason);
            event.preventDefault(); // 阻止默认的错误处理
        });

        const { createApp, ref, reactive, onMounted, watch, nextTick } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        // 配置 axios
        axios.defaults.withCredentials = true;
        axios.defaults.validateStatus = function (status) {
            return status >= 200 && status < 300; // 默认值
        };

        createApp({
            setup() {
                // 状态定义
                const loading = ref(false);
                const canEdit = ref(true); // 是否允许商家编辑
                
                const form = reactive({
                    status: 1,
                    model_type: 'auto',
                    custom_model_path: '',
                    position: 'right',
                    width: 200,
                    height: 300,
                    display_mode: 'normal'
                });



                onMounted(() => {
                    let retryCount = 0;
                    const maxRetries = 3;
                    
                    const tryFetchData = async () => {
                        try {
                            await fetchData();
                        } catch (error) {
                            if (retryCount < maxRetries) {
                                retryCount++;
                                console.log(`获取数据失败，第 ${retryCount} 次重试...`);
                                setTimeout(tryFetchData, 1000 * retryCount); // 递增重试延迟
                            } else {
                                console.error('多次重试后仍然失败');
                                ElMessage.error('加载数据失败，请刷新页面重试');
                            }
                        }
                    };

                    tryFetchData();
                });
                
                const fetchData = async () => {
                    try {
                        // 获取当前登录用户的数据
                        const res = await axios.post("/plugin/Tderciyuan/api/fetchData", {
                            merchant_id: window.merchant_id, // 如果页面中有商户ID
                            shop_name: window.shop_name // 如果页面中有商家名称
                        });
                        
                        if (res.data?.code === 200 && res.data.data) {
                            const data = res.data.data;
                            // 使用解构赋值并设置默认值
                            const {
                                status = 1,
                                model_type = 'auto',
                                custom_model_path = '',
                                position = 'right',
                                width = 200,
                                height = 300,
                                display_mode = 'normal',
                                merchant_can_edit = 1
                            } = data;

                            // 设置是否可编辑
                            canEdit.value = parseInt(merchant_can_edit) === 1;
                            
                            // 禁用编辑时给用户一个提示
                            if (!canEdit.value) {
                                ElMessage.warning('管理员已禁止商家修改看板娘设置');
                                
                                // 禁用所有输入元素
                                nextTick(() => {
                                    const inputs = document.querySelectorAll('input, .el-input-number, .el-switch');
                                    inputs.forEach(input => {
                                        input.setAttribute('disabled', 'disabled');
                                    });
                                });
                            }

                            // 更新表单数据
                            Object.assign(form, {
                                status: parseInt(status),
                                model_type,
                                custom_model_path,
                                position,
                                width: parseInt(width),
                                height: parseInt(height),
                                display_mode
                            });


                        } else if (res.data?.code === 403) {
                            ElMessage.error('请先登录');
                            // 可以在这里添加重定向到登录页面的逻辑
                            // window.location.href = '/login';
                        } else {
                            ElMessage.error(res.data?.msg || '获取数据失败');
                            console.error('获取数据失败:', res.data);
                        }
                    } catch (error) {
                        console.error('获取数据失败:', error);
                        ElMessage.error('获取数据失败，请检查网络连接');
                        throw error;
                    }
                };

                const save = async () => {
                    // 检查是否有权限保存
                    if (!canEdit.value) {
                        ElMessage.error('管理员已禁止商家修改看板娘设置');
                        return;
                    }
                    
                    // 如果是自定义模型，验证模型路径
                    if (form.model_type === 'custom' && !form.custom_model_path) {
                        ElMessage.error('请输入自定义模型路径');
                        return;
                    }

                    try {
                        loading.value = true;
                        
                        const res = await axios.post("/plugin/Tderciyuan/api/save", form);
                        if (res.data?.code === 200) {
                            ElMessage.success('保存成功');
                            await fetchData();
                        } else {
                            ElMessage.error(res.data?.msg || '保存失败');
                        }
                    } catch (error) {
                        console.error('保存失败:', error);
                        ElMessage.error('保存失败，请稍后重试');
                    } finally {
                        loading.value = false;
                    }
                };
                
                // 处理模型变更
                const handleModelChange = () => {
                    // 如果切换到非自定义模型，清空自定义模型路径
                    if (form.model_type !== 'custom') {
                        form.custom_model_path = '';
                    }
                };



                // 返回数据和方法
                return {
                    loading,
                    form,
                    canEdit,
                    handleModelChange,
                    save
                };
            }
        })
        .use(ElementPlus)
        .mount('#app');
    </script>
</body>
</html> 