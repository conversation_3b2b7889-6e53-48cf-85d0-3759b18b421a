<?php

namespace plugin\Lottery\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Log;
use think\facade\Db;
use plugin\Lottery\model\LotteryModel;

class User extends BasePlugin {

    protected $scene = ['user'];  // 只允许普通用户访问
    
    protected $noNeedLogin = [
    ];

    // 抽奖页面
    public function index() {
        return View::fetch();
    }

    // 执行抽奖
    public function doLottery() {
        try {
            // 获取商户ID
            $merchantId = $this->user->id;

            // 验证商户ID是否有效
            if (!is_numeric($merchantId) || $merchantId <= 0) {
                throw new \Exception('无效的用户ID');
            }

            // 获取配置
            $config = LotteryModel::getConfig();

            // 检查活动状态
            if (!isset($config['status']) || !$config['status']) {
                throw new \Exception('抽奖活动已关闭');
            }

            // 检查时间限制
            $currentTime = date('H:i');
            if ($currentTime < $config['start_hour'] || $currentTime > $config['end_hour']) {
                throw new \Exception('不在抽奖时间范围内');
            }
            
            // 获取商户限制
            $merchantLimit = LotteryModel::getMerchantLimit($merchantId);

            // 获取流水规则获得的抽奖次数
            $turnoverDraws = LotteryModel::calculateTurnoverDraws($merchantId);

            // 获取分佣获得的抽奖次数
            $commissionDraws = LotteryModel::calculateCommissionDraws($merchantId);

            // 检查抽奖次数
            $useBaseDraw = false;
            $useCommissionDraw = false;

            if ($merchantLimit['used_count'] >= $merchantLimit['daily_limit']) {
                // 基础次数已用完，尝试使用其他次数
                if ($turnoverDraws > 0) {
                    // 优先使用流水获得的抽奖次数
                    if (!LotteryModel::useTurnoverDraw($merchantId)) {
                        throw new \Exception('流水抽奖次数使用失败');
                    }
                } elseif ($commissionDraws > 0) {
                    // 使用分佣获得的抽奖次数
                    if (!LotteryModel::useCommissionDraw($merchantId)) {
                        throw new \Exception('分佣抽奖次数使用失败');
                    }
                    $useCommissionDraw = true;
                } else {
                    throw new \Exception('今日抽奖次数已用完');
                }
            } else {
                // 使用基础抽奖次数
                LotteryModel::updateMerchantUsedCount($merchantId);
                $useBaseDraw = true;
            }
            
            // 获取可用奖品
            $availablePrizes = LotteryModel::getAvailablePrizes();

            if (empty($availablePrizes)) {
                throw new \Exception('暂无可用奖品');
            }
            
            // 抽奖逻辑
            $totalProbability = array_sum(array_column($availablePrizes, 'probability'));
            $randomNum = mt_rand(1, $totalProbability * 100) / 100;
            
            $currentSum = 0;
            $winPrize = null;
            foreach ($availablePrizes as $prize) {
                $currentSum += $prize['probability'];
                if ($randomNum <= $currentSum) {
                    $winPrize = $prize;
                    break;
                }
            }
            
            if (!$winPrize) {
                throw new \Exception('抽奖失败');
            }
            
            // 记录中奖信息
            $record = [
                'user_id' => $this->user->id,
                'merchant_id' => $merchantId,
                'prize_id' => $winPrize['id'],
                'prize_name' => $winPrize['name'],
                'prize_type' => $winPrize['type'],
                'balance_amount' => isset($winPrize['balance_amount']) ? $winPrize['balance_amount'] : 0,
                'shipped' => 0,
                'balance_sent' => 0,
                'auto_sent' => 0,
                'is_virtual' => 0
            ];

            LotteryModel::addRecord($record);

            // 更新奖品库存
            LotteryModel::decreasePrizeStock($winPrize['id']);
            
            // 修改角度计算逻辑
            $prizeCount = count($availablePrizes);
            $baseAngle = 360 / $prizeCount;
            $prizeIndex = array_search($winPrize, array_values($availablePrizes));
            
            // 计算指针指向奖品中心的角度
            // 由于转盘是顺时针旋转，而指针在12点位置，需要调整角度计算
            $targetAngle = 360 - ($prizeIndex * $baseAngle + $baseAngle / 2);
            // 添加额外的旋转圈数（4圈）
            $finalAngle = 1440 + $targetAngle;
            
            // 重新获取更新后的数据
            $newMerchantLimit = LotteryModel::getMerchantLimit($merchantId);
            $newTurnoverDraws = LotteryModel::calculateTurnoverDraws($merchantId);
            $newCommissionDraws = LotteryModel::calculateCommissionDraws($merchantId);

            return json([
                'code' => 200,
                'msg' => '抽奖成功',
                'data' => [
                    'prize' => $winPrize,
                    'angle' => $finalAngle,
                    'remainingBaseDraws' => max(0, $newMerchantLimit['daily_limit'] - $newMerchantLimit['used_count']),
                    'turnoverDraws' => $newTurnoverDraws,
                    'commissionDraws' => $newCommissionDraws,
                    'totalDraws' => $this->getTotalDrawsCount()
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('抽奖失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '抽奖失败：' . $e->getMessage()]);
        }
    }

    // 计算目标角度
    private function calculateTargetAngle($winPrize, $availablePrizes) {
        $prizeCount = count($availablePrizes);
        $baseAngle = 360 / $prizeCount;
        $prizeIndex = array_search($winPrize, $availablePrizes);
        return 1440 + ($baseAngle * $prizeIndex); // 多转4圈后停止
    }

    // 获取抽奖记录
    public function getLotteryRecords() {
        try {
            // 支持GET和POST请求
            $page = input('page/d', 1);
            $pageSize = input('pageSize/d', 20);
            $all = input('type/s', '') === 'winners';  // 是否获取所有用户记录

            // 构建查询条件
            $where = [];
            if (!$all) {
                // 只获取当前用户的记录
                $where[] = ['user_id', '=', $this->user->id];
            }

            // 获取记录，如果是获取所有用户记录（中奖用户列表），则隐藏指定奖品
            $hideHiddenPrizes = $all; // 只在中奖用户列表中隐藏
            $result = LotteryModel::getRecords($where, $page, $pageSize, $hideHiddenPrizes);
            $records = $result['list'];
            $total = $result['total'];
            
            // 处理记录信息
            foreach ($records as &$record) {
                // 添加奖品类型文本和样式
                $record['prize_type_text'] = $this->getPrizeTypeText($record['prize_type']);
                $record['prize_type_style'] = $this->getPrizeTypeStyle($record['prize_type']);

                // 确保余额金额为数字
                $record['balance_amount'] = isset($record['balance_amount']) ? floatval($record['balance_amount']) : 0;

                // 修改商户名称获取逻辑
                if (isset($record['is_virtual']) && $record['is_virtual']) {
                    // 如果是虚拟记录，直接显示商家ID
                    $record['merchant_name'] = $record['merchant_id'];
                } else if (is_numeric($record['merchant_id'])) {
                    // 如果是真实用户ID，从数据库获取用户名
                    $merchant = Db::name('user')->where('id', $record['merchant_id'])->find();
                    $record['merchant_name'] = $merchant ? $merchant['username'] : '未知用户';
                } else {
                    // 其他情况显示商家ID
                    $record['merchant_name'] = $record['merchant_id'];
                }
            }
            
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => [
                    'list' => $records,
                    'total' => $total
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取中奖记录失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取记录失败：' . $e->getMessage()]);
        }
    }

    // 获取奖品类型对应的标签类型和样式
    private function getPrizeTypeStyle($type) {
        $prizeType = LotteryModel::getPrizeTypeByValue($type);

        if ($prizeType) {
            if ($prizeType['tag_type'] === 'custom') {
                return ['style' => 'background-color: ' . $prizeType['tag_color']];
            }
            return ['type' => $prizeType['tag_type'] ?: 'info'];
        }

        // 如果找不到对应的类型定义，返回默认样式
        return ['type' => 'info'];
    }

    // 获取奖品类型文本
    private function getPrizeTypeText($type) {
        $prizeType = LotteryModel::getPrizeTypeByValue($type);

        if ($prizeType) {
            return $prizeType['label'];
        }

        // 如果找不到对应的类型定义，返回未知类型
        return '未知类型';
    }

    // 获取抽奖配置
    public function getLotteryConfig() {
        try {
            // 获取配置
            $config = LotteryModel::getConfig();

            // 获取商户ID
            $merchantId = $this->user->id;

            // 获取商户限制
            $merchantLimit = LotteryModel::getMerchantLimit($merchantId);
            
            // 获取流水规则获得的抽奖次数
            $turnoverDraws = LotteryModel::calculateTurnoverDraws($merchantId);

            // 获取分佣获得的抽奖次数
            $commissionDraws = LotteryModel::calculateCommissionDraws($merchantId);

            // 计算剩余基础抽奖次数
            $remainingBaseDraws = max(0, $merchantLimit['daily_limit'] - $merchantLimit['used_count']);

            // 获取可用奖品列表
            $prizes = LotteryModel::getAvailablePrizes();

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => [
                    'config' => $config,
                    'remainingBaseDraws' => $remainingBaseDraws,
                    'turnoverDraws' => $turnoverDraws,
                    'commissionDraws' => $commissionDraws,
                    'totalDraws' => $this->getTotalDrawsCount(),
                    'prizes' => array_values($prizes),
                    'merchantLimit' => $merchantLimit
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取抽奖配置失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取配置失败：' . $e->getMessage()]);
        }
    }

    // 获取今日抽奖次数（已废弃，使用数据库）
    protected function getTodayLotteryCount()
    {
        try {
            // 获取商户ID
            $merchantId = $this->user->id;

            // 从数据库获取商户限制
            $merchantLimit = LotteryModel::getMerchantLimit($merchantId);

            return $merchantLimit['used_count'] ?? 0;
        } catch (\Exception $e) {
            Log::error('获取今日抽奖次数失败：' . $e->getMessage());
            return 0;
        }
    }

    // 获取总抽奖次数
    protected function getTotalDrawsCount()
    {
        try {
            $userId = $this->user->id;

            // 从数据库统计当前用户的抽奖记录数量
            $result = LotteryModel::getRecords([['user_id', '=', $userId]]);

            return $result['total'] ?? 0;
        } catch (\Exception $e) {
            Log::error('获取总抽奖次数失败：' . $e->getMessage());
            return 0;
        }
    }

    // 获取通知列表（暂未实现通知功能）
    public function getNotifications() {
        try {
            // 暂时返回空数组，后续可扩展通知功能
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => []
            ]);
        } catch (\Exception $e) {
            Log::error('获取通知列表失败：' . $e->getMessage());
            return json([
                'code' => 500,
                'msg' => '获取通知失败：' . $e->getMessage()
            ]);
        }
    }

    // 获取奖品类型列表
    public function getPrizeTypes() {
        try {
            $types = LotteryModel::getPrizeTypes();

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $types
            ]);
        } catch (\Exception $e) {
            Log::error('获取奖品类型列表失败：' . $e->getMessage());
            return json([
                'code' => 500,
                'msg' => '获取奖品类型失败：' . $e->getMessage()
            ]);
        }
    }

    // 获取抽奖数据
    public function fetchData() {
        try {
            // 从数据库获取奖品列表
            $prizes = LotteryModel::getPrizes();

            // 转换为前端需要的格式
            $tableData = [];
            foreach ($prizes as $prize) {
                $tableData[] = [
                    'id' => $prize['id'],
                    'name' => $prize['name'],
                    'type' => $prize['type'],
                    'probability' => $prize['probability'],
                    'stock' => $prize['stock'],
                    'status' => $prize['status']
                ];
            }

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => [
                    'tableData' => $tableData
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取抽奖数据失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取数据失败：' . $e->getMessage()]);
        }
    }

    // 获取流水统计
    public function getTurnoverStats()
    {
        try {
            // 获取商户ID
            $merchantId = $this->user->id;

            // 获取流水规则
            $turnoverRules = LotteryModel::getTurnoverRules();

            // 获取今日和本月流水（保持兼容性）
            $todayAmount = LotteryModel::getTodayTurnover($merchantId);
            $monthAmount = LotteryModel::getMonthTurnover($merchantId);

            // 计算已获得的抽奖次数
            $todayDraws = LotteryModel::calculateTurnoverDraws($merchantId);
            $nextRequired = 0;
            $configUpdated = false;

            // 获取当前重置周期的流水金额
            $currentAmount = LotteryModel::getCurrentTurnover($merchantId);

            // 获取已领取的记录
            $claims = LotteryModel::getTurnoverClaims($merchantId);
            $claimedRuleIds = array_column($claims, 'rule_id');

            // 处理规则数据
            $processedRules = [];
            foreach ($turnoverRules as $rule) {
                $claimed = in_array($rule['id'], $claimedRuleIds) && $currentAmount >= $rule['turnover_amount'];
                $canClaim = !$claimed && $currentAmount >= $rule['turnover_amount'] && $rule['status'] == 1;

                $processedRules[] = [
                    'id' => $rule['id'],
                    'turnover_amount' => number_format($rule['turnover_amount'], 2, '.', ''),
                    'draw_times' => intval($rule['draw_times']),
                    'current_amount' => number_format($currentAmount, 2, '.', ''),
                    'claimed' => $claimed,
                    'can_claim' => $canClaim,
                    'status' => intval($rule['status']),
                    'claiming' => false
                ];

                // 计算下一个目标（基于当前重置周期的流水）
                if ($nextRequired == 0 && $currentAmount < $rule['turnover_amount']) {
                    $nextRequired = $rule['turnover_amount'] - $currentAmount;
                }
            }

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => [
                    'stats' => [
                        'today_amount' => number_format($todayAmount, 2, '.', ''),
                        'month_amount' => number_format($monthAmount, 2, '.', ''),
                        'today_draws' => $todayDraws,
                        'next_required' => number_format($nextRequired, 2, '.', '')
                    ],
                    'rules' => $processedRules,
                    'config_updated' => $configUpdated
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取流水统计失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取统计失败：' . $e->getMessage()]);
        }
    }



    // 获取奖品列表
    public function getPrizes() {
        try {
            // 从数据库获取可用奖品
            $prizes = LotteryModel::getAvailablePrizes();

            // 转换为前端需要的格式
            $prizeList = [];
            foreach ($prizes as $prize) {
                $prizeList[] = [
                    'id' => $prize['id'],
                    'name' => $prize['name'],
                    'type' => $prize['type'],
                    'probability' => $prize['probability']
                ];
            }

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $prizeList
            ]);
        } catch (\Exception $e) {
            Log::error('获取奖品列表失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取奖品列表失败：' . $e->getMessage()]);
        }
    }

    // 手动领取流水奖励
    public function claimTurnoverReward() {
        try {
            // 获取商户ID
            $merchantId = $this->user->id;

            // 获取请求参数
            $ruleIndex = input('rule_index');
            if ($ruleIndex === null) {
                throw new \Exception('缺少规则索引参数');
            }

            // 获取流水规则
            $turnoverRules = LotteryModel::getTurnoverRules();

            if (!isset($turnoverRules[$ruleIndex])) {
                throw new \Exception('规则不存在');
            }

            $rule = $turnoverRules[$ruleIndex];

            // 检查规则状态
            if (!$rule['status']) {
                throw new \Exception('该规则未启用');
            }

            // 获取当前重置周期的流水金额
            $currentAmount = LotteryModel::getCurrentTurnover($merchantId);

            // 获取配置的重置周期
            $config = LotteryModel::getConfig();
            $resetPeriod = $config['turnover_reset_period'] ?? 'daily';

            // 检查是否达到流水要求
            if ($currentAmount < $rule['turnover_amount']) {
                $periodText = [
                    'daily' => '今日',
                    'weekly' => '本周',
                    'monthly' => '本月'
                ][$resetPeriod] ?? '今日';

                throw new \Exception($periodText . '流水金额不足，还需要 ' . number_format($rule['turnover_amount'] - $currentAmount, 2) . ' 元');
            }

            // 检查是否已经领取过
            $claims = LotteryModel::getTurnoverClaims($merchantId);
            $claimedRuleIds = array_column($claims, 'rule_id');

            if (in_array($rule['id'], $claimedRuleIds)) {
                throw new \Exception('该奖励已经领取过了');
            }

            // 添加领取记录
            $claimData = [
                'merchant_id' => $merchantId,
                'rule_id' => $rule['id'],
                'claim_date' => date('Y-m-d'),
                'draw_times_used' => 0,
                'create_time' => date('Y-m-d H:i:s')
            ];

            $result = Db::name('plugin_lottery_turnover_claims')->insert($claimData);

            if (!$result) {
                throw new \Exception('领取失败，请重试');
            }

            return json([
                'code' => 200,
                'msg' => '领取成功',
                'data' => [
                    'draw_times' => $rule['draw_times'],
                    'turnover_amount' => $rule['turnover_amount']
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('领取流水奖励失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 邀请奖励页面
     */
    public function invitation() {
        return view('invitation');
    }

    /**
     * 获取邀请统计
     */
    public function getInvitationStats() {
        try {
            $merchantId = session('merchant_id') ?? 'DEMO001';

            // 模拟数据，实际应该从数据库获取
            $stats = [
                'totalInvitations' => 25,
                'todayInvitations' => 3,
                'totalRewards' => 250.00,
                'pendingRewards' => 30.00
            ];

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 获取邀请记录
     */
    public function getInvitationRecords() {
        try {
            $merchantId = $this->user->id;
            $page = input('page/d', 1);
            $pageSize = input('pageSize/d', 20);

            // 获取该用户作为邀请人的记录
            $query = Db::name('plugin_lottery_invitation_rewards')
                ->where('inviter_id', $merchantId)
                ->order('create_time desc');

            $total = $query->count();
            $records = $query->page($page, $pageSize)->select()->toArray();

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => [
                    'list' => $records,
                    'total' => $total
                ]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 获取邀请链接
     */
    public function getInvitationLink() {
        try {
            $merchantId = session('merchant_id') ?? 'DEMO001';

            // 生成邀请链接
            $baseUrl = request()->domain();
            $invitationCode = base64_encode($merchantId . '_' . time());
            $link = $baseUrl . '/plugin/Lottery/user/register?invite=' . $invitationCode;

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => [
                    'link' => $link,
                    'code' => $invitationCode
                ]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 邀请注册页面
     */
    public function register() {
        $inviteCode = input('invite', '');

        // 这里可以处理邀请注册逻辑
        // 1. 解析邀请码
        // 2. 记录邀请关系
        // 3. 跳转到注册页面或显示注册表单

        return view('register', ['invite_code' => $inviteCode]);
    }

    /**
     * 处理邀请注册
     */
    public function processInvitation() {
        try {
            $inviteCode = input('invite_code/s', '');
            $newUserId = input('new_user_id/d');

            if (empty($inviteCode) || !$newUserId) {
                throw new \Exception('邀请码或用户ID不能为空');
            }

            // 解析邀请码获取邀请人ID
            $decoded = base64_decode($inviteCode);
            $parts = explode('_', $decoded);

            if (count($parts) < 2) {
                throw new \Exception('无效的邀请码');
            }

            $inviterId = $parts[0];

            // 创建邀请关系
            $result = LotteryModel::createInvitation($inviterId, $newUserId, $inviteCode);

            if ($result) {
                return json([
                    'code' => 200,
                    'msg' => '邀请关系建立成功',
                    'data' => ['invitation_id' => $result]
                ]);
            } else {
                throw new \Exception('邀请关系建立失败');
            }
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }
}