<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡密验证系统 - 卡密详情</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background: linear-gradient(180deg, #4a00e0 0%, #8e2de2 100%);
            min-height: 100vh;
            padding-top: 20px;
            box-shadow: 4px 0 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            position: fixed;
            width: inherit;
            max-width: inherit;
        }
        .sidebar-header {
            padding: 0 15px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }
        .sidebar a {
            color: rgba(255, 255, 255, 0.8);
            display: block;
            padding: 12px 20px;
            text-decoration: none;
            transition: all 0.3s;
            margin-bottom: 5px;
            border-left: 3px solid transparent;
        }
        .sidebar a:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: #fff;
            border-left: 3px solid #fff;
        }
        .sidebar a.active {
            background-color: rgba(255, 255, 255, 0.2);
            color: #fff;
            border-left: 3px solid #fff;
        }
        .sidebar i {
            margin-right: 10px;
        }
        .main-content {
            padding: 25px;
            margin-left: 225px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 25px;
            border: none;
        }
        .card-title {
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .license-key {
            font-family: SFMono-Regular, Menlo, Monaco, Consolas, monospace;
            background-color: #f5f5f5;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 14px;
            position: relative;
        }
        .info-list {
            list-style: none;
            padding: 0;
        }
        .info-list li {
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
        }
        .info-list li:last-child {
            border-bottom: none;
        }
        .info-list .label {
            font-weight: 500;
            color: #6c757d;
            width: 150px;
            min-width: 150px;
        }
        .badge-status {
            padding: 5px 10px;
            border-radius: 20px;
            font-weight: 500;
            font-size: 12px;
        }
        .status-active {
            background-color: #e3f2fd;
            color: #0d6efd;
        }
        .status-inactive {
            background-color: #f8f9fa;
            color: #6c757d;
        }
        .status-expired {
            background-color: #feecf0;
            color: #dc3545;
        }
        .status-banned {
            background-color: #fff8e1;
            color: #fd7e14;
        }
        .timeline {
            position: relative;
            padding-left: 40px;
            margin-top: 20px;
        }
        .timeline::before {
            content: '';
            position: absolute;
            left: 10px;
            top: 0;
            bottom: 0;
            width: 2px;
            background-color: #e9ecef;
        }
        .timeline-item {
            position: relative;
            margin-bottom: 25px;
        }
        .timeline-item:last-child {
            margin-bottom: 0;
        }
        .timeline-dot {
            position: absolute;
            left: -40px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: #f8f9fa;
            border: 2px solid #dee2e6;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 10px;
        }
        .timeline-content {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
        }
        .timeline-date {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 5px;
        }
        .timeline-text {
            margin-bottom: 0;
        }
        .action-buttons .btn {
            margin-right: 10px;
        }
        .action-buttons .btn:last-child {
            margin-right: 0;
        }
        .ban-badge {
            margin-left: 10px;
            padding: 5px 10px;
            background-color: #fff8e1;
            color: #fd7e14;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        .ban-info {
            margin-top: 10px;
            padding: 10px;
            background-color: #fff8e1;
            border-radius: 5px;
            border-left: 3px solid #fd7e14;
        }
    </style>
</head>
<body>

<div class="container-fluid">
    <div class="row">
        <!-- 侧边栏 -->
        <div class="col-lg-2 col-md-3 px-0">
            <div class="sidebar">
                <div class="sidebar-header">
                    <h4 class="text-white mb-0">卡密验证系统</h4>
                    <p class="text-white-50 mb-0">管理面板</p>
                </div>
                <a href="/dashboard"><i class="fas fa-tachometer-alt"></i> 仪表盘</a>
                <a href="/licenses" class="active"><i class="fas fa-key"></i> 卡密管理</a>
                <a href="/access_logs"><i class="fas fa-history"></i> 访问日志</a>
                <a href="/logout"><i class="fas fa-sign-out-alt"></i> 退出登录</a>
            </div>
        </div>
        
        <!-- 主内容区 -->
        <div class="col-lg-10 col-md-9 main-content">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">卡密详情</h2>
                <a href="/licenses" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left"></i> 返回列表
                </a>
            </div>
            
            <div class="card">
                <div class="card-body">
                    <div class="card-title">
                        <div>
                            <span class="license-key" id="licenseKey">{{ license.key }}</span>
                            {% if license.is_banned %}
                            <span class="ban-badge"><i class="fas fa-ban"></i> 已封禁</span>
                            {% endif %}
                        </div>
                        <div class="action-buttons">
                            {% if license.is_banned %}
                            <button class="btn btn-info" data-bs-toggle="modal" data-bs-target="#unbanModal">
                                <i class="fas fa-unlock"></i> 解除封禁
                            </button>
                            {% else %}
                            <button class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#banModal">
                                <i class="fas fa-ban"></i> 封禁卡密
                            </button>
                            {% endif %}
                            <a href="{{ url_for('edit_license', license_id=license.id) }}" class="btn btn-primary">
                                <i class="fas fa-edit"></i> 编辑卡密
                            </a>
                            <button class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                                <i class="fas fa-trash-alt"></i> 删除卡密
                            </button>
                        </div>
                    </div>

                    {% if license.is_banned %}
                    <div class="ban-info">
                        <strong>封禁原因:</strong> {{ license.ban_reason or '无' }}
                        <br>
                        <strong>封禁时间:</strong> {{ license.banned_at }}
                    </div>
                    {% endif %}
                    
                    <ul class="info-list mt-4">
                        <li>
                            <span class="label">状态</span>
                            {% if license.is_banned %}
                            <span class="badge badge-status status-banned">已封禁</span>
                            {% elif license.valid_until < current_date %}
                            <span class="badge badge-status status-expired">已过期</span>
                            {% elif license.activated %}
                            <span class="badge badge-status status-active">已激活</span>
                            {% else %}
                            <span class="badge badge-status status-inactive">未激活</span>
                            {% endif %}
                        </li>
                        <li>
                            <span class="label">软件ID</span>
                            <span>{{ license.software_id }}</span>
                        </li>
                        <li>
                            <span class="label">用户ID</span>
                            <span>{{ license.user_id or '未设置' }}</span>
                        </li>
                        <li>
                            <span class="label">设备ID</span>
                            <span>{{ license.device_id or '未绑定' }}</span>
                        </li>
                        <li>
                            <span class="label">创建日期</span>
                            <span>{{ license.created_at }}</span>
                        </li>
                        <li>
                            <span class="label">有效期至</span>
                            <span>{{ license.valid_until }}</span>
                        </li>
                        <li>
                            <span class="label">激活日期</span>
                            <span>{{ license.activated_at or '未激活' }}</span>
                        </li>
                        <li>
                            <span class="label">上次访问</span>
                            <span>{{ license.last_accessed or '从未' }}</span>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- 访问日志卡片 -->
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">访问日志</h5>
                    
                    {% if logs %}
                    <div class="timeline">
                        {% for log in logs %}
                        <div class="timeline-item">
                            <div class="timeline-dot">
                                <i class="fas fa-circle"></i>
                            </div>
                            <div class="timeline-content">
                                <div class="timeline-date">
                                    {{ log.access_time }}
                                </div>
                                <p class="timeline-text">
                                    <strong>操作:</strong> {{ log.action }}
                                    {% if log.ip_address %}
                                    <br><strong>IP地址:</strong> {{ log.ip_address }}
                                    {% endif %}
                                    {% if log.device_id %}
                                    <br><strong>设备ID:</strong> {{ log.device_id }}
                                    {% endif %}
                                    {% if log.user_agent %}
                                    <br><strong>User Agent:</strong> {{ log.user_agent }}
                                    {% endif %}
                                    {% if log.status %}
                                    <br><strong>状态:</strong> 
                                    {% if log.status == 'success' %}
                                    <span class="text-success">成功</span>
                                    {% else %}
                                    <span class="text-danger">失败</span>
                                    {% endif %}
                                    {% endif %}
                                    {% if log.message %}
                                    <br><strong>消息:</strong> {{ log.message }}
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        此卡密暂无访问记录
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除卡密确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                您确定要删除此卡密吗？此操作不可撤销。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form method="post" action="{{ url_for('delete_license', license_id=license.id) }}">
                    <button type="submit" class="btn btn-danger">删除</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 封禁卡密确认模态框 -->
<div class="modal fade" id="banModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">封禁卡密</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>您确定要封禁此卡密吗？封禁后该卡密将无法使用。</p>
                <form id="banForm" method="post" action="{{ url_for('ban_license', license_id=license.id) }}">
                    <div class="mb-3">
                        <label for="banReason" class="form-label">封禁原因</label>
                        <input type="text" class="form-control" id="banReason" name="ban_reason" placeholder="请输入封禁原因">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="submit" form="banForm" class="btn btn-warning">确认封禁</button>
            </div>
        </div>
    </div>
</div>

<!-- 解除封禁确认模态框 -->
<div class="modal fade" id="unbanModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">解除封禁</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                您确定要解除此卡密的封禁状态吗？解除后卡密将恢复正常使用。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form method="post" action="{{ url_for('unban_license', license_id=license.id) }}">
                    <button type="submit" class="btn btn-info">确认解除</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    // 复制卡密到剪贴板
    document.getElementById('licenseKey').addEventListener('click', function() {
        navigator.clipboard.writeText(this.textContent.trim())
            .then(() => {
                // 显示复制成功提示
                const tooltip = document.createElement('div');
                tooltip.textContent = '已复制!';
                tooltip.style.position = 'absolute';
                tooltip.style.left = '50%';
                tooltip.style.transform = 'translateX(-50%)';
                tooltip.style.top = '-25px';
                tooltip.style.backgroundColor = 'rgba(0,0,0,0.7)';
                tooltip.style.color = 'white';
                tooltip.style.padding = '3px 8px';
                tooltip.style.borderRadius = '3px';
                tooltip.style.fontSize = '12px';
                tooltip.style.pointerEvents = 'none';
                
                this.style.position = 'relative';
                this.appendChild(tooltip);
                
                setTimeout(() => {
                    this.removeChild(tooltip);
                }, 1500);
            })
            .catch(err => {
                console.error('复制失败:', err);
            });
    });
</script>

</body>
</html>
    