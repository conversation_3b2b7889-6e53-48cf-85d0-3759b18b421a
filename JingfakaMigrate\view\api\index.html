<html>
 
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1.0" />
  <meta http-equiv="Cache-Control" content="max-age=31536000">

  <link rel="stylesheet"
    href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-plus/2.0.4/theme-chalk/index.css">

  <title>数据迁移</title>
  <style>
    /* 加载动画样式 */
    #loading {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #fff;
      z-index: 9999;
    }

    .spinner {
      width: 50px;
      height: 50px;
      border: 5px solid rgba(0, 0, 0, 0.1);
      border-top-color: #3498db;
      border-radius: 50%;
      animation: spin 1s ease infinite;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }
  </style>
  <style>
    .progress-panel {
      background: #393d49;
      position: relative;
      min-height: 100px;
      line-height: 20px;
      resize: none;
      overflow: hidden;
      font-size: 12px;
      padding: 6px 10px;
      color: #fff;
      border-radius: 4px;
    }

    .title {
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .actions {
      margin-top: 16px;
    }

    .tips {
      font-size: 12px;
      color: #888;
    }
  </style>
</head>

<body>
  <!-- 加载动画容器 -->
  <div id="loading">
    <div class="spinner"></div>
  </div>

  <!-- Vue 应用挂载点 -->
  <div id="app" style="display: none">

    <el-card v-if="step==1&&!queueData" shadow="never">
      <div class="title">
        <el-tag>配置源鲸发卡平台数据库</el-tag>
      </div>

      <el-form :model="form" label-width="auto">
        <el-form-item label="数据库服务器地址：">
          <el-input v-model="form.hostname" placeholder="请输入数据库服务器地址" />
        </el-form-item>
        <el-form-item label="数据库名：">
          <el-input v-model="form.database" placeholder="请输入数据库名" />
        </el-form-item>
        <el-form-item label="数据库用户名：">
          <el-input v-model="form.username" placeholder="请输入数据库用户名" />
        </el-form-item>
        <el-form-item label="数据库密码：">
          <el-input v-model="form.password" placeholder="请输入数据库密码" />
        </el-form-item>
        <el-form-item label="端口号：">
          <el-input v-model="form.hostport" placeholder="默认3306，如不明白保持默认" />
        </el-form-item>

        <el-form-item label="">
          <el-button type="primary" :loading="isLoading" @click="connectDatebase">
            下一步
          </el-button>
        </el-form-item>
      </el-form>

    </el-card>


    <el-card v-if="step==2&&!queueData" shadow="never">
      <div class="title">
        <el-tag>选择要迁移的数据</el-tag>
      </div>


      <el-form :model="form" label-width="auto">
        <el-form-item label="" prop="type">
          <el-checkbox v-model="form.datas.user" name="type">
            商户表 ---- {{statistics?.user}}条
          </el-checkbox>
        </el-form-item>

        <el-form-item label="" prop="type">
          <el-checkbox v-model="form.datas.goods_category" name="type" :disabled="!form.datas.user">
            商品分类表 ---- {{statistics?.goods_category}}条
          </el-checkbox>
        </el-form-item>

        <el-form-item label="" prop="type">
          <el-checkbox v-model="form.datas.goods" name="type" :disabled="!form.datas.user||!form.datas.goods_category">
            商品表【包含未售卡密】---- {{statistics?.goods}}条
          </el-checkbox>
        </el-form-item>

        <el-form-item label="" prop="type">
          <el-checkbox v-model="form.datas.order" name="type"
            :disabled="!form.datas.user||!form.datas.goods_category||!form.datas.goods">
            订单表 ---- {{statistics?.order}}条
          </el-checkbox>
        </el-form-item>

        <el-form-item label="" prop="type">
          <el-checkbox v-model="form.datas.auto_unfreeze" name="type" :disabled="!form.datas.user">
            待解冻资金表【迁移后将全部自动解冻到新平台余额】---- {{statistics?.auto_unfreeze}}条
          </el-checkbox>
        </el-form-item>

        <el-form-item label="" prop="type">
          <el-checkbox v-model="form.datas.cash" name="type" :disabled="!form.datas.user">
            提现记录表 ---- {{statistics?.cash}}条
          </el-checkbox>
        </el-form-item>

        <el-form-item label="">
          <el-button type="primary" :loading="isLoading" @click="queueStart">
            开始迁移
          </el-button>
        </el-form-item>
      </el-form>

    </el-card>


    <el-card v-if="step==3||queueData" shadow="never">
      <div class="title">
        <el-space>
          <el-link type="primary" @click="queueReset">
            &lt; 重新配置 </el-link>

          迁移日志
          <i v-if="isLoading||queueData?.status==2" class="el-icon is-loading">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
              <path fill="currentColor"
                d="M512 64a32 32 0 0 1 32 32v192a32 32 0 0 1-64 0V96a32 32 0 0 1 32-32m0 640a32 32 0 0 1 32 32v192a32 32 0 1 1-64 0V736a32 32 0 0 1 32-32m448-192a32 32 0 0 1-32 32H736a32 32 0 1 1 0-64h192a32 32 0 0 1 32 32m-640 0a32 32 0 0 1-32 32H96a32 32 0 0 1 0-64h192a32 32 0 0 1 32 32M195.2 195.2a32 32 0 0 1 45.248 0L376.32 331.008a32 32 0 0 1-45.248 45.248L195.2 240.448a32 32 0 0 1 0-45.248zm452.544 452.544a32 32 0 0 1 45.248 0L828.8 783.552a32 32 0 0 1-45.248 45.248L647.744 692.992a32 32 0 0 1 0-45.248zM828.8 195.264a32 32 0 0 1 0 45.184L692.992 376.32a32 32 0 0 1-45.248-45.248l135.808-135.808a32 32 0 0 1 45.248 0m-452.544 452.48a32 32 0 0 1 0 45.248L240.448 828.8a32 32 0 0 1-45.248-45.248l135.808-135.808a32 32 0 0 1 45.248 0z">
              </path>
            </svg>
          </i>
        </el-space>
      </div>

      <div class="progress-panel">
        <div v-if="!queueData">没有启动</div>
        <template v-else>
          <div v-for="item in queueData?.history" :key="item">
            {{ item.message }}
          </div>
        </template>
      </div>
      <div class="actions">
        <!-- 任务状态(1新任务,2处理中,3成功,4失败) -->
        <el-button :disabled="!queueData||queueData?.status!=2" type="danger" plain @click="queueStop">
          停止迁移
        </el-button>
      </div>
      <p v-if="queueData?.status==2" class="tips">
        任务执行中，您可以关闭当前页面，系统将后台自动运行
      </p>
    </el-card>
  </div>

  <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/vue/3.2.31/vue.global.min.js"></script>
  <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-plus/2.0.4/index.full.min.js"></script>
  <script src="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/axios/0.26.0/axios.min.js"></script>

  <script type="module">
    const { ref, reactive, watch } = Vue;
    const { ElMessage, ElMessageBox } = ElementPlus;
    const { Loading } = ElementPlus;

    const step = ref(1);
    const statistics = ref({
      user: 0,
      goods_category: 0,
      goods: 0,
      order: 0,
      auto_unfreeze: 0,
      cash: 0,
    });

    const isLoading = ref(false);
    const queueData = ref(null);

    const form = reactive({
      hostname: '127.0.0.1',
      database: '',
      username: '',
      password: '',
      hostport: 3306,
      datas: {
        user: true, goods_category: true, goods: true, order: true, auto_unfreeze: true, cash: true,
      },
    });

    watch(form.datas, (New, Old) => {
      if (New?.user == false) {
        form.datas.goods_category = false;
        form.datas.goods = false;
        form.datas.order = false;
        form.datas.auto_unfreeze = false;
        form.datas.cash = false;
      }

      if (New?.goods_category == false) {
        form.datas.goods = false;
        form.datas.order = false;
      }

      if (New?.goods == false) {
        form.datas.order = false;
      }


    })

    const connectDatebase = async () => {

      ElMessageBox.confirm('1.请确保新平台数据库已备份（在宝塔点击数据库备份），方便迁移出错恢复！<br>2.请确保老平台已处理完全部投诉订单', '提示', {
        distinguishCancelAndClose: true,
        confirmButtonText: '确定',
        cancelButtonText: '关闭',
        dangerouslyUseHTMLString: true
      })
        .then(async () => {
          isLoading.value = true;
          const res = await axios.post(
            "/plugin/JingfakaMigrate/api/connectDatebase",
            form
          );
          isLoading.value = false;
          if (res.data?.code == 200) {
            ElMessage.success('连接成功');
            statistics.value = res.data?.data?.statistics;

            step.value = 2;
          } else {
            ElMessage.error(res.data?.msg);
          }

        })
        .catch(() => {
          // catch error
        })


    }


    const queueExecute = async () => {
      try {
        isLoading.value = true;
        const res = await axios.post(
          "/plugin/JingfakaMigrate/api/queueProgress"
        );
        isLoading.value = false;
        queueData.value = res.data?.data;

        if (res.data?.data?.status == 1 || res.data?.data?.status == 2) {
          setTimeout(() => {
            queueExecute();
          }, 3000);
        }
      } catch (error) { }
    };

    const queueStart = async () => {

      try {
        const res = await axios.post(
          "/plugin/JingfakaMigrate/api/queueStart"
          ,
          form
        );
        if (res.data?.code == 200) {
          await queueExecute();
          ElMessage.success("启动成功，请等待自动执行");
        } else {
          ElMessage.error(res.data?.msg);
        }
      } catch (error) { }


    };

    const queueStop = async () => {
      try {
        const res = await axios.post("/plugin/JingfakaMigrate/api/queueStop");
        if (res.data?.code == 200) {
          ElMessage.success("停止成功");
          await queueExecute();
          queueData.value = null;
        } else {
          ElMessage.error(res.data?.msg);
        }
      } catch (error) { }
    };

    queueExecute();

    const queueReset = () => {


      ElMessageBox.confirm('此操作将会停止进行中的任务，确定要重置任务?', '提示', {
        distinguishCancelAndClose: true,
        confirmButtonText: '确定',
        cancelButtonText: '关闭',
      })
        .then(() => {
          queueStop();
          step.value = 1;

        })
        .catch(() => {
          // catch error
        })
    }
    // Vue 应用
    const app = Vue.createApp({
      setup() {
        return {
          isLoading,
          queueStart,
          queueStop,
          queueData,
          step,
          form,
          statistics,
          connectDatebase,
          queueReset,
        };
      },
    });

    app.use(ElementPlus);



    app.mount("#app");

    // 移除加载动画
    window.onload = () => {
      document.getElementById("loading").style.display = "none"; // 隐藏加载动画
      document.getElementById("app").style.display = "block"; // 显示 Vue 应用
    };
  </script>
</body>

</html>