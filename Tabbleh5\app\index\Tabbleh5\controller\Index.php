<?php

namespace app\index\Tabbleh5\controller;

use app\common\controller\BaseIndex;
use think\facade\View;
use think\facade\Db;

class Index extends BaseIndex {

    public function index() {
        // 获取导航菜单数据(包含子菜单)
        $navItems = Db::name('nav')
            ->where('status', 1)
            ->where('pid', 0)
            ->field(['id', 'name', 'href', 'target'])
            ->order('sort asc')
            ->select()
            ->each(function($item) {
                // 获取子菜单
                $item['children'] = Db::name('nav')
                    ->where('status', 1)
                    ->where('pid', $item['id'])
                    ->field(['name', 'href', 'target'])
                    ->order('sort asc')
                    ->select()
                    ->toArray();
                return $item;
            })
            ->toArray();

        // 获取网站配置信息
        $logo = $this->formatImageUrl(sysconf('website.logo'));
        $siteName = sysconf('website.app_name');
        $siteSubtitle = sysconf('website.subtitle') ?: '全能产品和服务查询平台';
        $favicon = $this->formatImageUrl(sysconf('website.favicon'));
        $icpNumber = sysconf('website.icp_number');
        $gaNumber = sysconf('website.ga_number');
        $icpCert = sysconf('website.icp_cert');

        // 获取页脚配置
        $params = get_template_params();

        return view('', [
            'title' => sysconf("website.title"),
            'logo' => $logo,
            'siteName' => $siteName,
            'siteSubtitle' => $siteSubtitle,
            'favicon' => $favicon,
            'navItems' => $navItems,
            'icpNumber' => $icpNumber,
            'gaNumber' => $gaNumber,
            'icpCert' => $icpCert,
            // 服务中心配置
            'footer_service_show' => $params->footer_service_show ?? 1,
            'footer_service_1' => $params->footer_service_1 ?? '卡密查询',
            'footer_service_1_link' => $params->footer_service_1_link ?? '#',
            'footer_service_2' => $params->footer_service_2 ?? '投诉中心',
            'footer_service_2_link' => $params->footer_service_2_link ?? '#',
            'footer_service_3' => $params->footer_service_3 ?? '卡密工具',
            'footer_service_3_link' => $params->footer_service_3_link ?? '#',
            'footer_service_4' => $params->footer_service_4 ?? '商户入驻',
            'footer_service_4_link' => $params->footer_service_4_link ?? '#',
            
            // 帮助中心配置
            'footer_help_show' => $params->footer_help_show ?? 1,
            'footer_help_1' => $params->footer_help_1 ?? '常见问题',
            'footer_help_1_link' => $params->footer_help_1_link ?? '#',
            'footer_help_2' => $params->footer_help_2 ?? '系统公告',
            'footer_help_2_link' => $params->footer_help_2_link ?? '#',
            'footer_help_3' => $params->footer_help_3 ?? '结算公告',
            'footer_help_3_link' => $params->footer_help_3_link ?? '#',
            'footer_help_4' => $params->footer_help_4 ?? '新闻动态',
            'footer_help_4_link' => $params->footer_help_4_link ?? '#',
            
            // 法律责任配置
            'footer_legal_show' => $params->footer_legal_show ?? 1,
            'footer_legal_1' => $params->footer_legal_1 ?? '免责声明',
            'footer_legal_1_link' => $params->footer_legal_1_link ?? '#',
            'footer_legal_2' => $params->footer_legal_2 ?? '禁售商品',
            'footer_legal_2_link' => $params->footer_legal_2_link ?? '#',
            'footer_legal_3' => $params->footer_legal_3 ?? '服务协议',
            'footer_legal_3_link' => $params->footer_legal_3_link ?? '#',
            'footer_legal_4' => $params->footer_legal_4 ?? '隐私政策',
            'footer_legal_4_link' => $params->footer_legal_4_link ?? '#',
            
            // 友情链接配置
            'footer_links_show' => $params->footer_links_show ?? 1,
            'footer_links_1' => $params->footer_links_1 ?? '一意支付',
            'footer_links_1_link' => $params->footer_links_1_link ?? '#',
            'footer_links_2' => $params->footer_links_2 ?? '支付宝',
            'footer_links_2_link' => $params->footer_links_2_link ?? '#',
            'footer_links_3' => $params->footer_links_3 ?? '微信支付',
            'footer_links_3_link' => $params->footer_links_3_link ?? '#',
            'footer_links_4' => $params->footer_links_4 ?? 'QQ钱包',
            'footer_links_4_link' => $params->footer_links_4_link ?? '#',
            
            // 联系方式配置
            'contact_email' => $params->contact_email ?? '<EMAIL>',
            'contact_qq' => $params->contact_qq ?? '123456789',
            'contact_wx' => $params->contact_wx ?? 'service_wx',
            
            // 统计数据配置
            'stats_orders' => $params->stats_orders ?? '12452177',
            'stats_cards' => $params->stats_cards ?? '2565245',
            'stats_merchants' => $params->stats_merchants ?? '1526988',
            'stats_amount' => $params->stats_amount ?? '84768.25',
            
            // 页脚描述信息
            'footer_description' => $params->footer_description ?? '鲸商pro是一家专业的电商支付解决方案提供商，致力于为商家提供安全、便捷、稳定的虚拟商品自动交易服务。',
            
            // 页脚快速链接
            'footer_quicklinks_title' => $params->footer_quicklinks_title ?? '快速链接',
            'footer_quicklinks' => [
                ['name' => $params->footer_quicklink_1 ?? '首页', 'href' => $params->footer_quicklink_1_link ?? '#'],
                ['name' => $params->footer_quicklink_2 ?? '平台优势', 'href' => $params->footer_quicklink_2_link ?? '#'],
                ['name' => $params->footer_quicklink_3 ?? '解决方案', 'href' => $params->footer_quicklink_3_link ?? '#'],
                ['name' => $params->footer_quicklink_4 ?? '帮助中心', 'href' => $params->footer_quicklink_4_link ?? '#'],
                ['name' => $params->footer_quicklink_5 ?? '服务协议', 'href' => $params->footer_quicklink_5_link ?? '#'],
            ],
            
            // 页脚产品服务
            'footer_products_title' => $params->footer_products_title ?? '产品服务',
            'footer_products' => [
                ['name' => $params->footer_product_1 ?? '支付渠道', 'href' => $params->footer_product_1_link ?? '#'],
                ['name' => $params->footer_product_2 ?? '商品管理', 'href' => $params->footer_product_2_link ?? '#'],
                ['name' => $params->footer_product_3 ?? '订单管理', 'href' => $params->footer_product_3_link ?? '#'],
                ['name' => $params->footer_product_4 ?? '用户管理', 'href' => $params->footer_product_4_link ?? '#'],
                ['name' => $params->footer_product_5 ?? '分销系统', 'href' => $params->footer_product_5_link ?? '#'],
            ],
            
            // 页脚联系信息
            'footer_contact_title' => $params->footer_contact_title ?? '联系我们',
            'footer_contact_address' => $params->footer_contact_address ?? '北京市朝阳区科技园区88号',
            'footer_contact_phone' => $params->footer_contact_phone ?? '************',
            'footer_contact_email' => $params->footer_contact_email ?? '<EMAIL>',
            'footer_contact_hours' => $params->footer_contact_hours ?? '周一至周日 9:00-18:00',
            
            // 页脚版权信息
            'footer_copyright' => isset($params->footer_copyright) ? $params->footer_copyright : '© 2023 鲸商pro 版权所有 | 京ICP备12345678号-1 | 京公网安备11010502030123号',
            'year' => date('Y'),
            'icpNumber' => $icpNumber,
            'gaNumber' => $gaNumber,
            'icpCert' => $icpCert,
        ]);
    }

    /**
     * 格式化图片URL
     */
    private function formatImageUrl($url) {
        if (empty($url)) {
            return '';
        }

        // 如果是完整的URL，直接返回
        if (preg_match('/^https?:\/\/(oss\.|cos\.|[-\w]+\.qiniucdn\.|[-\w]+)/i', $url)) {
            return $url;
        }

        // 如果是uploads目录下的文件
        if (strpos($url, 'uploads/') === 0) {
            return request()->domain() . '/' . $url;
        }

        // 其他情况，确保以/开头
        return request()->domain() . '/' . ltrim($url, '/');
    }
}
