import os
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session
import sqlite3
from datetime import datetime, timedelta, date
import secrets
import hashlib
import functools
import random
import string
from math import ceil

app = Flask(__name__)
app.secret_key = secrets.token_hex(16)

# 创建模板目录
templates_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'templates')
os.makedirs(templates_dir, exist_ok=True)

# 设置模板目录
app.template_folder = templates_dir

# 初始化数据库
def init_db():
    conn = sqlite3.connect('licenses.db')
    c = conn.cursor()
    
    # 创建licenses表
    c.execute('''
    CREATE TABLE IF NOT EXISTS licenses
    (id INTEGER PRIMARY KEY AUTOINCREMENT,
    license_key TEXT UNIQUE, 
    software_id TEXT, 
    user_id TEXT,
    device_id TEXT,
    valid_until TEXT,
    activated INTEGER DEFAULT 0,
    created_at TEXT,
    activated_at TEXT,
    is_banned INTEGER DEFAULT 0,
    ban_reason TEXT,
    banned_at TEXT)
    ''')
    
    # 检查是否需要添加新字段
    c.execute("PRAGMA table_info(licenses)")
    columns = [info[1] for info in c.fetchall()]
    
    # 添加激活时间字段（如果不存在）
    if 'activated_at' not in columns:
        c.execute('ALTER TABLE licenses ADD COLUMN activated_at TEXT')
    
    # 添加封禁相关字段（如果不存在）
    if 'is_banned' not in columns:
        c.execute('ALTER TABLE licenses ADD COLUMN is_banned INTEGER DEFAULT 0')
    if 'ban_reason' not in columns:
        c.execute('ALTER TABLE licenses ADD COLUMN ban_reason TEXT')
    if 'banned_at' not in columns:
        c.execute('ALTER TABLE licenses ADD COLUMN banned_at TEXT')
    
    # 创建访问日志表
    c.execute('''
    CREATE TABLE IF NOT EXISTS access_logs
    (id INTEGER PRIMARY KEY AUTOINCREMENT,
    license_key TEXT,
    device_id TEXT,
    ip_address TEXT,
    action TEXT,
    timestamp TEXT,
    success INTEGER,
    message TEXT)
    ''')
    
    # 创建管理员账户表
    c.execute('''
    CREATE TABLE IF NOT EXISTS admins
    (id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE,
    password TEXT,
    email TEXT,
    created_at TEXT)
    ''')
    
    # 检查是否已有管理员账户，如果没有则创建默认账户
    c.execute('SELECT COUNT(*) FROM admins')
    if c.fetchone()[0] == 0:
        # 创建默认管理员账户 admin/admin123
        c.execute('INSERT INTO admins (username, password, email, created_at) VALUES (?, ?, ?, ?)',
                 ('admin', hashlib.sha256('admin123'.encode()).hexdigest(), '<EMAIL>', datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
    
    conn.commit()
    conn.close()

# 确保数据库存在
init_db()

# 生成卡密
def generate_license_key(prefix=''):
    """生成一个随机的卡密，可选添加前缀"""
    # 用secrets模块生成16字节的随机十六进制字符串
    random_part = secrets.token_hex(16)
    if prefix:
        return f"{prefix}{random_part}"
    return random_part

# 卡密类型预设
CARD_TYPES = {
    "daily": {"name": "日卡", "days": 1},
    "weekly": {"name": "周卡", "days": 7},
    "monthly": {"name": "月卡", "days": 30},
    "quarterly": {"name": "季卡", "days": 90},
    "half_yearly": {"name": "半年卡", "days": 180},
    "yearly": {"name": "年卡", "days": 365},
    "permanent": {"name": "永久卡", "days": 3650}  # 约10年
}

# 验证登录状态的装饰器
def login_required(func):
    @functools.wraps(func)
    def secure_function(*args, **kwargs):
        if not session.get('logged_in'):
            flash('请先登录')
            return redirect(url_for('index'))
        return func(*args, **kwargs)
    return secure_function

# 数据库连接
def get_db_connection():
    conn = sqlite3.connect('licenses.db')
    conn.row_factory = sqlite3.Row
    return conn

# 管理员登录页
@app.route('/')
def index():
    # 如果已经登录，直接跳转到仪表盘
    if session.get('logged_in'):
        return redirect(url_for('dashboard'))
        
    # 创建登录页模板
    login_template = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>卡密验证系统 - 登录</title>
        <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        <style>
            body { 
                background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
            }
            .login-card { 
                background-color: rgba(255, 255, 255, 0.9);
                border-radius: 15px;
                box-shadow: 0 15px 35px rgba(0,0,0,0.2);
                backdrop-filter: blur(10px);
                overflow: hidden;
            }
            .card-header { 
                background: linear-gradient(to right, #4a00e0, #8e2de2);
                color: white;
                padding: 25px 20px;
                text-align: center;
                border-bottom: none;
            }
            .card-body {
                padding: 40px 30px;
            }
            .btn-primary {
                background: linear-gradient(to right, #4a00e0, #8e2de2);
                border: none;
                transition: all 0.3s;
                padding: 12px;
                font-weight: 600;
                letter-spacing: 0.5px;
            }
            .btn-primary:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 20px rgba(74, 0, 224, 0.3);
            }
            .form-control {
                border-radius: 10px;
                padding: 12px 15px;
                background: rgba(255, 255, 255, 0.9);
                border: 1px solid #e0e0e0;
                font-size: 15px;
            }
            .form-control:focus {
                box-shadow: 0 0 0 3px rgba(78, 0, 224, 0.2);
                border-color: #4a00e0;
            }
            .login-icon-container {
                text-align: center;
                margin-bottom: 30px;
            }
            .login-icon {
                font-size: 48px;
                width: 100px;
                height: 100px;
                line-height: 100px;
                border-radius: 50%;
                background: linear-gradient(135deg, #4a00e0, #8e2de2);
                color: white;
                text-align: center;
                margin: 0 auto;
                box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            }
            .form-label {
                font-weight: 600;
                color: #333;
                margin-bottom: 8px;
            }
            .alert {
                border-radius: 10px;
                padding: 15px;
                margin-bottom: 25px;
            }
            .form-floating label {
                padding: 12px 15px;
            }
            .form-floating>.form-control {
                padding: 12px 15px;
                height: calc(3.5rem + 2px);
            }
            .input-group {
                margin-bottom: 20px;
            }
            .input-group-text {
                background-color: white;
                border-right: none;
                border-radius: 10px 0 0 10px;
            }
            .input-group .form-control {
                border-left: none;
                border-radius: 0 10px 10px 0;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-5">
                    <div class="text-center mb-4">
                        <h2 class="text-white fw-bold mb-1">卡密验证系统</h2>
                        <p class="text-white mb-0">管理员控制面板</p>
                    </div>
                    <div class="card login-card">
                        <div class="card-header">
                            <h3 class="mb-0">管理员登录</h3>
                        </div>
                        <div class="card-body">
                            <div class="login-icon-container">
                                <div class="login-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                            </div>
                            
                            {% with messages = get_flashed_messages(with_categories=true) %}
                                {% if messages %}
                                    {% for category, message in messages %}
                                        <div class="alert alert-{{ category if category != 'message' else 'warning' }} alert-dismissible fade show" role="alert">
                                            {{ message }}
                                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                        </div>
                                    {% endfor %}
                                {% endif %}
                            {% endwith %}
                            
                            <form method="post" action="/login">
                                <div class="input-group mb-3">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                    <input type="text" class="form-control" id="username" name="username" placeholder="用户名" required autofocus>
                                </div>
                                <div class="input-group mb-4">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input type="password" class="form-control" id="password" name="password" placeholder="密码" required>
                                </div>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-sign-in-alt me-2"></i>登录
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="text-center mt-4">
                        <p class="text-white">© 2023 卡密验证系统 - 版权所有</p>
                    </div>
                </div>
            </div>
        </div>
        
        <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    """
    
    # 写入模板文件
    with open(os.path.join(templates_dir, 'login.html'), 'w', encoding='utf-8') as f:
        f.write(login_template)
    
    return render_template('login.html')

# 登录处理
@app.route('/login', methods=['POST'])
def login():
    username = request.form['username']
    password = request.form['password']
    
    # 验证用户名和密码
    conn = get_db_connection()
    user = conn.execute('SELECT * FROM admins WHERE username = ?', (username,)).fetchone()
    conn.close()
    
    if user and user['password'] == hashlib.sha256(password.encode()).hexdigest():
        session['logged_in'] = True
        session['user_id'] = user['id']
        session['username'] = user['username']
        return redirect(url_for('dashboard'))
    else:
        flash('用户名或密码错误', 'danger')
        return redirect(url_for('index'))

# 退出登录
@app.route('/logout')
def logout():
    session.pop('logged_in', None)
    session.pop('user_id', None)
    session.pop('username', None)
    flash('您已成功退出登录', 'success')
    return redirect(url_for('index'))

# 管理员仪表板
@app.route('/dashboard')
@login_required
def dashboard():
    conn = get_db_connection()
    
    # 统计数据
    total_licenses = conn.execute('SELECT COUNT(*) FROM licenses').fetchone()[0]
    active_licenses = conn.execute('SELECT COUNT(*) FROM licenses WHERE activated = 1').fetchone()[0]
    expired_licenses = conn.execute(
        'SELECT COUNT(*) FROM licenses WHERE valid_until IS NOT NULL AND valid_until < date("now")'
    ).fetchone()[0]
    
    # 获取最近10个卡密
    recent_licenses = conn.execute(
        'SELECT * FROM licenses ORDER BY created_at DESC LIMIT 10'
    ).fetchall()
    
    # 获取最近10条激活记录
    recent_activations = conn.execute(
        'SELECT * FROM access_logs WHERE action = "activate" ORDER BY timestamp DESC LIMIT 10'
    ).fetchall()
    
    # 获取验证数据统计
    verification_stats = {}
    
    # 过去30天验证总数
    verification_stats['total'] = conn.execute(
        'SELECT COUNT(*) FROM access_logs WHERE action = "verify" AND timestamp > date("now", "-30 days")'
    ).fetchone()[0]
    
    # 今日验证数
    verification_stats['today'] = conn.execute(
        'SELECT COUNT(*) FROM access_logs WHERE action = "verify" AND date(timestamp) = date("now")'
    ).fetchone()[0]
    
    # 昨日验证数
    verification_stats['yesterday'] = conn.execute(
        'SELECT COUNT(*) FROM access_logs WHERE action = "verify" AND date(timestamp) = date("now", "-1 day")'
    ).fetchone()[0]
    
    # 本周验证数
    verification_stats['this_week'] = conn.execute(
        'SELECT COUNT(*) FROM access_logs WHERE action = "verify" AND timestamp > date("now", "weekday 0", "-7 days")'
    ).fetchone()[0]
    
    conn.close()
    
    # 生成 dashboard.html 模板
    dashboard_template = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>卡密验证系统 - 仪表盘</title>
        <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        <script src="https://cdn.bootcdn.net/ajax/libs/Chart.js/3.7.1/chart.min.js"></script>
        <style>
            body {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            }
            .sidebar {
                background: linear-gradient(180deg, #4a00e0 0%, #8e2de2 100%);
                min-height: 100vh;
                padding-top: 20px;
                box-shadow: 4px 0 10px rgba(0, 0, 0, 0.1);
                z-index: 1000;
                position: fixed;
                width: inherit;
                max-width: inherit;
            }
            .sidebar-header {
                padding: 0 15px 20px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                margin-bottom: 20px;
            }
            .sidebar a {
                color: rgba(255, 255, 255, 0.8);
                display: block;
                padding: 12px 20px;
                text-decoration: none;
                transition: all 0.3s;
                margin-bottom: 5px;
                border-left: 3px solid transparent;
            }
            .sidebar a:hover {
                background-color: rgba(255, 255, 255, 0.1);
                color: #fff;
                border-left: 3px solid #fff;
            }
            .sidebar a.active {
                background-color: rgba(255, 255, 255, 0.2);
                color: #fff;
                border-left: 3px solid #fff;
            }
            .sidebar i {
                margin-right: 10px;
                width: 20px;
                text-align: center;
            }
            .main-content {
                margin-left: 16.666667%;
                padding: 30px;
            }
            .page-header {
                margin-bottom: 30px;
                border-bottom: 1px solid #dee2e6;
                padding-bottom: 15px;
            }
            .dashboard-card {
                background: #fff;
                border-radius: 15px;
                overflow: hidden;
                box-shadow: 0 10px 20px rgba(0,0,0,0.05);
                margin-bottom: 30px;
                transition: all 0.3s;
            }
            .dashboard-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 15px 30px rgba(0,0,0,0.1);
            }
            .card-header {
                padding: 20px;
                border-bottom: 1px solid #f1f1f1;
                font-weight: 600;
                background-color: #fff;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }
            .card-header .title {
                font-size: 18px;
                color: #333;
            }
            .card-body {
                padding: 20px;
            }
            .stat-card {
                background: linear-gradient(45deg, #8e2de2, #4a00e0);
                border-radius: 15px;
                padding: 25px 20px;
                box-shadow: 0 10px 20px rgba(142, 45, 226, 0.2);
                color: white;
                display: flex;
                align-items: center;
                transition: all 0.3s;
                margin-bottom: 30px;
            }
            .stat-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 15px 30px rgba(142, 45, 226, 0.3);
            }
            .stat-card .icon {
                background: rgba(255, 255, 255, 0.2);
                width: 65px;
                height: 65px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                margin-right: 20px;
                font-size: 24px;
            }
            .stat-card .stat-content h2 {
                font-size: 28px;
                font-weight: 700;
                margin-bottom: 5px;
                line-height: 1;
            }
            .stat-card .stat-content p {
                margin-bottom: 0;
                font-size: 14px;
                opacity: 0.8;
            }
            .stat-card.blue {
                background: linear-gradient(45deg, #4e54c8, #8f94fb);
            }
            .stat-card.green {
                background: linear-gradient(45deg, #11998e, #38ef7d);
            }
            .stat-card.orange {
                background: linear-gradient(45deg, #f46b45, #eea849);
            }
            .verification-stat {
                text-align: center;
                padding: 20px 15px;
                border-radius: 10px;
                background-color: #f8f9fa;
                transition: all 0.3s;
                margin-bottom: 20px;
            }
            .verification-stat:hover {
                background-color: #e9ecef;
                transform: translateY(-3px);
            }
            .verification-stat h3 {
                font-size: 24px;
                font-weight: 700;
                margin-bottom: 5px;
                color: #4a00e0;
            }
            .verification-stat p {
                margin-bottom: 0;
                color: #6c757d;
                font-size: 14px;
            }
            .table {
                margin-bottom: 0;
            }
            .table thead th {
                font-weight: 600;
                font-size: 14px;
                border-top: none;
                padding: 15px 12px;
            }
            .table tbody td {
                vertical-align: middle;
                padding: 12px;
                font-size: 14px;
            }
            .license-key {
                font-family: monospace;
                font-size: 13px;
                background-color: #f8f9fa;
                padding: 5px 8px;
                border-radius: 5px;
                cursor: pointer;
            }
            .license-key:hover {
                background-color: #e9ecef;
            }
            .card-status {
                font-weight: 500;
                padding: 5px 10px;
                border-radius: 20px;
                font-size: 12px;
                display: inline-block;
            }
            .status-active {
                background-color: rgba(25, 135, 84, 0.1);
                color: #198754;
            }
            .status-inactive {
                background-color: rgba(108, 117, 125, 0.1);
                color: #6c757d;
            }
            .status-expired {
                background-color: rgba(220, 53, 69, 0.1);
                color: #dc3545;
            }
            .empty-data {
                text-align: center;
                padding: 40px 20px;
                color: #6c757d;
            }
            .empty-data i {
                font-size: 48px;
                margin-bottom: 20px;
                opacity: 0.2;
            }
            .empty-data p {
                margin-bottom: 0;
                font-size: 16px;
            }
            .chart-container {
                height: 330px;
                width: 100%;
                margin-top: 10px;
            }
        </style>
    </head>
    <body>
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-2">
                    <div class="sidebar">
                        <div class="sidebar-header">
                            <h4 class="text-center text-white fw-bold mb-0">卡密验证系统</h4>
                            <p class="text-center text-white-50 mb-0">管理面板</p>
                        </div>
                        <a href="/dashboard" class="active"><i class="fas fa-tachometer-alt"></i> 仪表盘</a>
                        <a href="/licenses"><i class="fas fa-key"></i> 卡密管理</a>
                        <a href="/create_license"><i class="fas fa-plus-circle"></i> 生成卡密</a>
                        <a href="/access_logs"><i class="fas fa-history"></i> 访问日志</a>
                        <a href="/logout"><i class="fas fa-sign-out-alt"></i> 退出登录</a>
                    </div>
                </div>
                <div class="col-md-10 main-content">
                    <div class="page-header d-flex align-items-center justify-content-between">
                        <h2 class="mb-0">系统仪表盘</h2>
                        <div class="date">{{ current_date }}</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="stat-card blue">
                                <div class="icon">
                                    <i class="fas fa-key"></i>
                                </div>
                                <div class="stat-content">
                                    <h2>{{ total_licenses }}</h2>
                                    <p>卡密总数</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stat-card green">
                                <div class="icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="stat-content">
                                    <h2>{{ active_licenses }}</h2>
                                    <p>已激活卡密</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stat-card orange">
                                <div class="icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="stat-content">
                                    <h2>{{ expired_licenses }}</h2>
                                    <p>已过期卡密</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="dashboard-card">
                                <div class="card-header">
                                    <div class="title">
                                        <i class="fas fa-chart-line me-2"></i>验证趋势
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container">
                                        <canvas id="verificationsChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="dashboard-card">
                                <div class="card-header">
                                    <div class="title">
                                        <i class="fas fa-chart-bar me-2"></i>验证统计
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="verification-stat">
                                                <h3>{{ verification_stats.total }}</h3>
                                                <p>30天总验证</p>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="verification-stat">
                                                <h3>{{ verification_stats.today }}</h3>
                                                <p>今日验证</p>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="verification-stat">
                                                <h3>{{ verification_stats.yesterday }}</h3>
                                                <p>昨日验证</p>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="verification-stat">
                                                <h3>{{ verification_stats.this_week }}</h3>
                                                <p>本周验证</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="dashboard-card">
                                <div class="card-header">
                                    <div class="title">
                                        <i class="fas fa-key me-2"></i>最近创建的卡密
                                    </div>
                                    <a href="/licenses" class="btn btn-sm btn-outline-primary">查看全部</a>
                                </div>
                                <div class="card-body p-0">
                                    {% if recent_licenses %}
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>卡密</th>
                                                    <th>软件ID</th>
                                                    <th>创建日期</th>
                                                    <th>状态</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for license in recent_licenses %}
                                                <tr>
                                                    <td><span class="license-key" onclick="copyToClipboard('{{ license.license_key }}')">{{ license.license_key }}</span></td>
                                                    <td>{{ license.software_id }}</td>
                                                    <td>{{ license.created_at }}</td>
                                                    <td>
                                                        {% if license.valid_until and license.valid_until < current_date %}
                                                            <span class="card-status status-expired">已过期</span>
                                                        {% elif license.activated %}
                                                            <span class="card-status status-active">已激活</span>
                                                        {% else %}
                                                            <span class="card-status status-inactive">未激活</span>
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    {% else %}
                                    <div class="empty-data">
                                        <i class="fas fa-key"></i>
                                        <p>尚未创建任何卡密</p>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="dashboard-card">
                                <div class="card-header">
                                    <div class="title">
                                        <i class="fas fa-history me-2"></i>最近激活记录
                                    </div>
                                    <a href="/access_logs" class="btn btn-sm btn-outline-primary">查看全部日志</a>
                                </div>
                                <div class="card-body p-0">
                                    {% if recent_activations %}
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>卡密</th>
                                                    <th>设备ID</th>
                                                    <th>时间</th>
                                                    <th>状态</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for log in recent_activations %}
                                                <tr>
                                                    <td><span class="license-key">{{ log.license_key }}</span></td>
                                                    <td>{{ log.device_id }}</td>
                                                    <td>{{ log.timestamp }}</td>
                                                    <td>
                                                        <span class="card-status status-active">成功</span>
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    {% else %}
                                    <div class="empty-data">
                                        <i class="fas fa-history"></i>
                                        <p>尚无卡密激活记录</p>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <script>
            // 模拟过去30天的验证数据
            const labels = Array.from({length: 30}, (_, i) => {
                const date = new Date();
                date.setDate(date.getDate() - 29 + i);
                return `${date.getMonth()+1}/${date.getDate()}`;
            });
            
            // 随机生成样本数据
            const generateData = () => {
                return Array.from({length: 30}, () => Math.floor(Math.random() * 50));
            };
            
            const ctx = document.getElementById('verificationsChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '验证次数',
                        data: generateData(),
                        borderColor: '#8e2de2',
                        backgroundColor: 'rgba(142, 45, 226, 0.1)',
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: '#8e2de2',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 4,
                        pointHoverRadius: 6
                    }]
                },
                options: {
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                            backgroundColor: 'rgba(0, 0, 0, 0.7)',
                            titleColor: '#fff',
                            bodyColor: '#fff',
                            borderColor: '#8e2de2',
                            borderWidth: 1,
                            padding: 10,
                            displayColors: false
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        }
                    }
                }
            });
            
            function copyToClipboard(text) {
                navigator.clipboard.writeText(text).then(() => {
                    alert('卡密已复制到剪贴板');
                }).catch(err => {
                    console.error('复制失败:', err);
                });
            }
        </script>
    </body>
    </html>
    """
    
    # 写入模板文件
    with open(os.path.join(templates_dir, 'dashboard.html'), 'w', encoding='utf-8') as f:
        f.write(dashboard_template)
    
    # 获取当前日期用于比较过期状态和显示
    current_date = datetime.now().strftime('%Y-%m-%d')
    
    return render_template('dashboard.html', 
                          total_licenses=total_licenses,
                          active_licenses=active_licenses,
                          expired_licenses=expired_licenses,
                          recent_licenses=recent_licenses,
                          recent_activations=recent_activations,
                          verification_stats=verification_stats,
                          current_date=current_date)

# 卡密列表
@app.route('/licenses')
@login_required
def licenses():
    # 获取查询参数
    search = request.args.get('search', '')
    filter_type = request.args.get('filter_type', '')
    status_filter = request.args.get('status_filter', 'all')
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    # 计算分页偏移量
    offset = (page - 1) * per_page
    
    # 构建基础查询
    query = """
        SELECT 
            id, 
            license_key as `key`, 
            software_id, 
            user_id, 
            device_id, 
            valid_until, 
            activated, 
            activated_at, 
            created_at, 
            is_banned, 
            ban_reason, 
            banned_at 
        FROM licenses
    """
    count_query = "SELECT COUNT(*) FROM licenses"
    where_clauses = []
    
    # 应用搜索和过滤
    if search:
        search_fields = []
        if not filter_type or filter_type == 'key':
            search_fields.append("license_key LIKE ?")
        if not filter_type or filter_type == 'software_id':
            search_fields.append("software_id LIKE ?")
        if not filter_type or filter_type == 'user_id':
            search_fields.append("user_id LIKE ?")
        if not filter_type or filter_type == 'device_id':
            search_fields.append("device_id LIKE ?")
        
        if search_fields:
            search_clause = " OR ".join(search_fields)
            where_clauses.append(f"({search_clause})")
            # 为每个搜索字段添加一个参数
            search_params = [f"%{search}%"] * len(search_fields)
    else:
        search_params = []
    
    # 状态过滤
    if status_filter == 'active':
        where_clauses.append("activated = 1 AND (valid_until IS NULL OR valid_until >= DATE('now')) AND is_banned = 0")
    elif status_filter == 'inactive':
        where_clauses.append("activated = 0 AND is_banned = 0")
    elif status_filter == 'expired':
        where_clauses.append("valid_until < DATE('now') AND is_banned = 0")
    elif status_filter == 'banned':
        where_clauses.append("is_banned = 1")
    
    # 添加where子句
    if where_clauses:
        query += " WHERE " + " AND ".join(where_clauses)
        count_query += " WHERE " + " AND ".join(where_clauses)
    
    # 添加排序和分页
    query += " ORDER BY created_at DESC LIMIT ? OFFSET ?"
    
    # 执行查询
    conn = get_db_connection()
    params = search_params + [per_page, offset]
    licenses = conn.execute(query, params).fetchall()
    
    # 获取总记录数
    count_params = search_params
    total_count = conn.execute(count_query, count_params).fetchone()[0]
    
    # 计算总页数
    total_pages = ceil(total_count / per_page)
    
    # 获取统计信息
    total_licenses = conn.execute("SELECT COUNT(*) FROM licenses").fetchone()[0]
    activated_licenses = conn.execute("SELECT COUNT(*) FROM licenses WHERE activated = 1").fetchone()[0]
    expired_licenses = conn.execute("SELECT COUNT(*) FROM licenses WHERE valid_until < DATE('now')").fetchone()[0]
    banned_licenses = conn.execute("SELECT COUNT(*) FROM licenses WHERE is_banned = 1").fetchone()[0]
    
    conn.close()
    
    # 当前日期用于判断是否过期
    current_date = datetime.now().strftime('%Y-%m-%d')
    
    return render_template('licenses.html', 
                          licenses=licenses,
                          search=search,
                          filter_type=filter_type,
                          status_filter=status_filter,
                          page=page,
                          total_pages=total_pages,
                          total_licenses=total_licenses,
                          activated_licenses=activated_licenses,
                          expired_licenses=expired_licenses,
                          banned_licenses=banned_licenses,
                          current_date=current_date)

# 激活许可证（增加这个功能）
@app.route('/activate_license/<license_key>', methods=['POST'])
@login_required
def activate_license(license_key):
    conn = get_db_connection()
    conn.execute('UPDATE licenses SET is_active = 1 WHERE license_key = ?', (license_key,))
    conn.commit()
    conn.close()
    
    flash(f'卡密已激活: {license_key}')
    return redirect('/licenses')

# 获取所有不同的软件ID
def get_software_ids():
    conn = get_db_connection()
    try:
        software_ids = conn.execute('SELECT DISTINCT software_id FROM licenses ORDER BY software_id').fetchall()
        return [row['software_id'] for row in software_ids]
    except Exception as e:
        print(f"获取软件ID列表出错: {str(e)}")
        return []
    finally:
        conn.close()

# 创建许可证表单页
@app.route('/create_license', methods=['GET', 'POST'])
@login_required
def create_license():
    if request.method == 'POST':
        software_id = request.form['software_id']
        user_id = request.form['user_id']
        count = int(request.form.get('count', 1))
        prefix = request.form.get('prefix', '').strip()  # 获取前缀参数
        
        # 处理有效期设置
        valid_type = request.form.get('valid_type', 'date')
        if valid_type == 'date':
            valid_until = request.form['valid_until'] if request.form['valid_until'] else None
        else:  # valid_type == 'days'
            days = int(request.form.get('valid_days', 0))
            if days > 0:
                valid_until = (datetime.now() + timedelta(days=days)).strftime('%Y-%m-%d')
            else:
                valid_until = None
        
        conn = get_db_connection()
        keys = []
        
        for _ in range(count):
            license_key = generate_license_key(prefix)  # 使用前缀生成卡密
            keys.append(license_key)
            
            conn.execute('INSERT INTO licenses (license_key, software_id, user_id, valid_until, created_at) VALUES (?, ?, ?, ?, ?)',
                        (license_key, software_id, user_id, valid_until, datetime.now().strftime('%Y-%m-%d')))
        
        conn.commit()
        conn.close()
        
        if count == 1:
            flash(f'卡密创建成功: {keys[0]}', 'success')
        else:
            flash(f'成功创建 {count} 个卡密', 'success')
        
        return redirect(url_for('create_license'))
    
    # 获取已有的软件ID列表
    software_ids = get_software_ids()
    
    # 如果没有任何软件ID，添加一个默认值
    if not software_ids:
        software_ids = ['default']
    
    # 生成创建卡密的HTML模板
    create_license_template = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>卡密验证系统 - 生成卡密</title>
        <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        <style>
            body {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            }
            .sidebar {
                background: linear-gradient(180deg, #4a00e0 0%, #8e2de2 100%);
                min-height: 100vh;
                padding-top: 20px;
                box-shadow: 4px 0 10px rgba(0, 0, 0, 0.1);
                z-index: 1000;
                position: fixed;
                width: inherit;
                max-width: inherit;
            }
            .sidebar-header {
                padding: 0 15px 20px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                margin-bottom: 20px;
            }
            .sidebar a {
                color: rgba(255, 255, 255, 0.8);
                display: block;
                padding: 12px 20px;
                text-decoration: none;
                transition: all 0.3s;
                margin-bottom: 5px;
                border-left: 3px solid transparent;
            }
            .sidebar a:hover {
                background-color: rgba(255, 255, 255, 0.1);
                color: #fff;
                border-left: 3px solid #fff;
            }
            .sidebar a.active {
                background-color: rgba(255, 255, 255, 0.2);
                color: #fff;
                border-left: 3px solid #fff;
            }
            .sidebar i {
                margin-right: 10px;
                width: 20px;
                text-align: center;
            }
            .main-content {
                margin-left: 16.666667%;
                padding: 30px;
            }
            .page-header {
                margin-bottom: 30px;
                border-bottom: 1px solid #dee2e6;
                padding-bottom: 15px;
            }
            .dashboard-card {
                background: #fff;
                border-radius: 15px;
                overflow: hidden;
                box-shadow: 0 10px 20px rgba(0,0,0,0.05);
                margin-bottom: 30px;
                transition: all 0.3s;
            }
            .dashboard-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 15px 30px rgba(0,0,0,0.1);
            }
            .card-header {
                padding: 20px;
                border-bottom: 1px solid #f1f1f1;
                font-weight: 600;
                background-color: #fff;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }
            .card-header .title {
                font-size: 18px;
                color: #333;
            }
            .card-body {
                padding: 20px;
            }
            .btn-primary {
                background: linear-gradient(45deg, #8e2de2, #4a00e0);
                border: none;
                box-shadow: 0 5px 15px rgba(142, 45, 226, 0.3);
                transition: all 0.3s;
            }
            .btn-primary:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 20px rgba(142, 45, 226, 0.4);
            }
            .form-control, .form-select {
                padding: 10px 15px;
                border-radius: 8px;
                border: 1px solid #e0e0e0;
            }
            .form-control:focus, .form-select:focus {
                border-color: #8e2de2;
                box-shadow: 0 0 0 3px rgba(142, 45, 226, 0.1);
            }
            .alert {
                border-radius: 10px;
                border: none;
                box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            }
            .license-keys {
                background-color: #f8f9fa;
                border-radius: 10px;
                padding: 15px;
                max-height: 300px;
                overflow-y: auto;
                margin-top: 20px;
            }
            .license-key-item {
                font-family: monospace;
                background-color: #fff;
                padding: 12px 15px;
                margin-bottom: 10px;
                border-radius: 8px;
                border-left: 3px solid #8e2de2;
                display: flex;
                justify-content: space-between;
                align-items: center;
                transition: all 0.3s;
            }
            .license-key-item:hover {
                background-color: #f8f9fa;
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            }
            .copy-btn {
                background: transparent;
                border: none;
                color: #8e2de2;
                cursor: pointer;
                padding: 5px;
                font-size: 14px;
                transition: all 0.3s;
                opacity: 0.6;
            }
            .license-key-item:hover .copy-btn {
                opacity: 1;
            }
            .card-type-selector {
                margin-top: 10px;
                display: flex;
                flex-wrap: wrap;
                gap: 10px;
            }
            .card-type-option {
                cursor: pointer;
                padding: 8px 15px;
                border-radius: 30px;
                font-size: 0.85rem;
                transition: all 0.3s;
                background-color: #f1f1f1;
                user-select: none;
            }
            .card-type-option:hover {
                background-color: #e9ecef;
            }
            .card-type-option.selected {
                background: linear-gradient(45deg, #8e2de2, #4a00e0);
                color: white;
            }
            .valid-method-selector {
                margin-bottom: 15px;
            }
            .form-check-input:checked {
                background-color: #8e2de2;
                border-color: #8e2de2;
            }
            .batch-settings {
                background-color: #f8f9fa;
                border-radius: 10px;
                padding: 15px;
                margin-top: 15px;
                border: 1px dashed #dee2e6;
            }
            .input-group-text {
                background-color: #f8f9fa;
                border-color: #e0e0e0;
            }
        </style>
    </head>
    <body>
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-2">
                    <div class="sidebar">
                        <div class="sidebar-header">
                            <h4 class="text-center text-white fw-bold mb-0">卡密验证系统</h4>
                            <p class="text-center text-white-50 mb-0">管理面板</p>
                        </div>
                        <a href="/dashboard"><i class="fas fa-tachometer-alt"></i> 仪表盘</a>
                        <a href="/licenses"><i class="fas fa-key"></i> 卡密管理</a>
                        <a href="/create_license" class="active"><i class="fas fa-plus-circle"></i> 生成卡密</a>
                        <a href="/access_logs"><i class="fas fa-history"></i> 访问日志</a>
                        <a href="/logout"><i class="fas fa-sign-out-alt"></i> 退出登录</a>
                    </div>
                </div>
                <div class="col-md-10 main-content">
                    <div class="page-header">
                        <h2 class="mb-0">生成卡密</h2>
                    </div>
                    
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}
                    
                    <div class="row">
                        <div class="col-md-7">
                            <div class="dashboard-card">
                                <div class="card-header">
                                    <div class="title">
                                        <i class="fas fa-plus-circle me-2"></i>创建新卡密
                                    </div>
                                </div>
                                <div class="card-body">
                                    <form action="/create_license" method="post">
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="software_id" class="form-label">软件ID <span class="text-danger">*</span></label>
                                                <div class="input-group">
                                                    <select class="form-select" id="software_id" name="software_id" required>
                                                        {% for sw_id in software_ids %}
                                                        <option value="{{ sw_id }}">{{ sw_id }}</option>
                                                        {% endfor %}
                                                    </select>
                                                    <button class="btn btn-outline-secondary" type="button" data-bs-toggle="modal" data-bs-target="#newSoftwareModal">
                                                        <i class="fas fa-plus"></i>
                                                    </button>
                                                </div>
                                                <div class="form-text">
                                                    选择现有软件ID或添加新的
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="user_id" class="form-label">用户ID <span class="text-muted">(可选)</span></label>
                                                <input type="text" class="form-control" id="user_id" name="user_id" placeholder="填写用户ID或留空">
                                                <div class="form-text">
                                                    为特定用户生成卡密时填写
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="prefix" class="form-label">卡密前缀 <span class="text-muted">(可选)</span></label>
                                            <input type="text" class="form-control" id="prefix" name="prefix" placeholder="例如: VIP- 或 ABC_">
                                            <div class="form-text">
                                                添加前缀使卡密更易辨识，如 VIP-123456...
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">有效期设置</label>
                                            <div class="valid-method-selector">
                                                <div class="form-check form-check-inline">
                                                    <input class="form-check-input" type="radio" name="valid_type" id="valid_type_days" value="days" checked>
                                                    <label class="form-check-label" for="valid_type_days">按天数</label>
                                                </div>
                                                <div class="form-check form-check-inline">
                                                    <input class="form-check-input" type="radio" name="valid_type" id="valid_type_date" value="date">
                                                    <label class="form-check-label" for="valid_type_date">按日期</label>
                                                </div>
                                            </div>
                                            
                                            <div id="valid_days_container">
                                                <div class="card-type-selector mb-2">
                                                    <div class="card-type-option selected" data-days="1" onclick="selectCardType(this, 1)">日卡</div>
                                                    <div class="card-type-option" data-days="7" onclick="selectCardType(this, 7)">周卡</div>
                                                    <div class="card-type-option" data-days="30" onclick="selectCardType(this, 30)">月卡</div>
                                                    <div class="card-type-option" data-days="90" onclick="selectCardType(this, 90)">季卡</div>
                                                    <div class="card-type-option" data-days="180" onclick="selectCardType(this, 180)">半年卡</div>
                                                    <div class="card-type-option" data-days="365" onclick="selectCardType(this, 365)">年卡</div>
                                                    <div class="card-type-option" data-days="0" onclick="selectCardType(this, 0)">永久卡</div>
                                                </div>
                                                <div class="input-group">
                                                    <input type="number" class="form-control" id="valid_days" name="valid_days" value="1" min="0">
                                                    <span class="input-group-text">天</span>
                                                </div>
                                                <div class="form-text">
                                                    设置为0表示永久有效
                                                </div>
                                            </div>
                                            
                                            <div id="valid_date_container" style="display: none;">
                                                <input type="date" class="form-control" id="valid_until" name="valid_until" 
                                                    value="{{ default_expiry_date }}">
                                                <div class="form-text">
                                                    留空表示永久有效
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">批量生成设置</label>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="enable_batch" onchange="toggleBatchSettings()">
                                                <label class="form-check-label" for="enable_batch">
                                                    启用批量生成
                                                </label>
                                            </div>
                                            
                                            <div id="batch_settings" class="batch-settings" style="display: none;">
                                                <div class="mb-3">
                                                    <label for="count" class="form-label">生成数量</label>
                                                    <input type="number" class="form-control" id="count" name="count" value="10" min="1" max="500">
                                                    <div class="form-text">
                                                        一次最多可生成500个卡密
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="d-grid gap-2">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-key me-2"></i>生成卡密
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-5">
                            <div class="dashboard-card">
                                <div class="card-header">
                                    <div class="title">
                                        <i class="fas fa-info-circle me-2"></i>卡密使用说明
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="mb-4">
                                        <h5>什么是卡密？</h5>
                                        <p>卡密是用于激活和验证软件的唯一密钥，用户可以通过卡密来授权使用软件的功能。</p>
                                    </div>
                                    
                                    <div class="mb-4">
                                        <h5>卡密类型</h5>
                                        <ul>
                                            <li><strong>时效卡</strong> - 有固定使用期限的卡密，到期后需要重新激活</li>
                                            <li><strong>永久卡</strong> - 永久有效的卡密，一次激活即可永久使用</li>
                                        </ul>
                                    </div>
                                    
                                    <div class="mb-0">
                                        <h5>卡密状态说明</h5>
                                        <ul>
                                            <li><span class="badge bg-secondary">未激活</span> - 卡密尚未被任何设备使用</li>
                                            <li><span class="badge bg-success">已激活</span> - 卡密已被设备绑定使用</li>
                                            <li><span class="badge bg-danger">已过期</span> - 卡密使用期限已过期</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            {% if keys %}
                            <div class="dashboard-card mt-4">
                                <div class="card-header">
                                    <div class="title">
                                        <i class="fas fa-list-alt me-2"></i>最近生成的卡密
                                    </div>
                                    {% if keys|length > 1 %}
                                    <button class="btn btn-sm btn-primary" onclick="copyAllKeys()">
                                        复制全部
                                    </button>
                                    {% endif %}
                                </div>
                                <div class="card-body p-0">
                                    <div class="license-keys">
                                        {% for key in keys %}
                                        <div class="license-key-item">
                                            <span class="key-text">{{ key }}</span>
                                            <button class="copy-btn" onclick="copyKey('{{ key }}')">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 新增软件ID的模态框 -->
        <div class="modal fade" id="newSoftwareModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">添加新软件ID</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="new_software_id" class="form-label">新软件ID</label>
                            <input type="text" class="form-control" id="new_software_id">
                            <div class="form-text">
                                添加后会立即更新到软件ID下拉列表中
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="addSoftwareId()">添加</button>
                    </div>
                </div>
            </div>
        </div>
        
        <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
        <script>
            // 有效期类型切换
            document.querySelectorAll('input[name="valid_type"]').forEach(radio => {
                radio.addEventListener('change', function() {
                    if (this.value === 'days') {
                        document.getElementById('valid_days_container').style.display = 'block';
                        document.getElementById('valid_date_container').style.display = 'none';
                    } else {
                        document.getElementById('valid_days_container').style.display = 'none';
                        document.getElementById('valid_date_container').style.display = 'block';
                    }
                });
            });
            
            // 选择卡密类型
            function selectCardType(element, days) {
                // 移除所有选项的选中状态
                document.querySelectorAll('.card-type-option').forEach(option => {
                    option.classList.remove('selected');
                });
                
                // 添加当前选项的选中状态
                element.classList.add('selected');
                
                // 更新天数输入框
                document.getElementById('valid_days').value = days;
            }
            
            // 批量生成设置切换
            function toggleBatchSettings() {
                const batchSettings = document.getElementById('batch_settings');
                const enableBatch = document.getElementById('enable_batch');
                
                if (enableBatch.checked) {
                    batchSettings.style.display = 'block';
                    document.getElementById('count').value = 10;
                } else {
                    batchSettings.style.display = 'none';
                    document.getElementById('count').value = 1;
                }
            }
            
            // 添加新的软件ID
            function addSoftwareId() {
                const newSoftwareId = document.getElementById('new_software_id').value.trim();
                if (!newSoftwareId) return;
                
                const select = document.getElementById('software_id');
                
                // 检查是否已存在
                let exists = false;
                for (let i = 0; i < select.options.length; i++) {
                    if (select.options[i].value === newSoftwareId) {
                        exists = true;
                        break;
                    }
                }
                
                if (!exists) {
                    const option = document.createElement('option');
                    option.value = newSoftwareId;
                    option.text = newSoftwareId;
                    select.add(option);
                    
                    // 选中新添加的选项
                    select.value = newSoftwareId;
                }
                
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('newSoftwareModal'));
                modal.hide();
                
                // 清空输入框
                document.getElementById('new_software_id').value = '';
            }
            
            // 复制单个卡密
            function copyKey(key) {
                navigator.clipboard.writeText(key).then(() => {
                    alert('卡密已复制到剪贴板');
                }).catch(err => {
                    console.error('复制失败:', err);
                });
            }
            
            // 复制所有卡密
            function copyAllKeys() {
                const keys = Array.from(document.querySelectorAll('.key-text')).map(el => el.textContent.trim()).join('\\n');
                navigator.clipboard.writeText(keys).then(() => {
                    alert('所有卡密已复制到剪贴板');
                }).catch(err => {
                    console.error('复制失败:', err);
                });
            }
        </script>
    </body>
    </html>
    """
    
    # 写入模板文件
    with open(os.path.join(templates_dir, 'create_license.html'), 'w', encoding='utf-8') as f:
        f.write(create_license_template)
    
    # 计算默认过期日期 (30天后)
    default_expiry_date = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')
    
    return render_template('create_license.html', 
                          software_ids=software_ids, 
                          keys=session.pop('generated_keys', None),
                          now=datetime.now(),
                          default_expiry_date=default_expiry_date,
                          timedelta=timedelta)

# 记录类型枚举值和对应的中文名
LOG_ACTIONS = {
    'verify': '验证',
    'activate': '激活',
    'register': '注册'
}

@app.route('/access_logs')
@login_required
def access_logs():
    page = request.args.get('page', 1, type=int)
    per_page = 20
    offset = (page - 1) * per_page
    
    search_term = request.args.get('search', '')
    filter_action = request.args.get('action', '')
    
    conn = get_db_connection()
    
    # 构建查询
    query = "SELECT * FROM access_logs"
    count_query = "SELECT COUNT(*) FROM access_logs"
    params = []
    where_clauses = []
    
    if search_term:
        where_clauses.append("(license_key LIKE ? OR device_id LIKE ? OR ip_address LIKE ?)")
        params.extend(['%' + search_term + '%'] * 3)
    
    if filter_action:
        where_clauses.append("action = ?")
        params.append(filter_action)
    
    if where_clauses:
        query += " WHERE " + " AND ".join(where_clauses)
        count_query += " WHERE " + " AND ".join(where_clauses)
    
    # 添加排序和分页
    query += " ORDER BY timestamp DESC LIMIT ? OFFSET ?"
    params.extend([per_page, offset])
    
    # 执行查询
    logs = conn.execute(query, params).fetchall()
    total = conn.execute(count_query, params[:-2] if params else []).fetchone()[0]
    
    # 获取验证、激活和失败的日志数量
    verify_count = conn.execute("SELECT COUNT(*) FROM access_logs WHERE action = 'verify'").fetchone()[0]
    activate_count = conn.execute("SELECT COUNT(*) FROM access_logs WHERE action = 'activate'").fetchone()[0]
    failed_count = conn.execute("SELECT COUNT(*) FROM access_logs WHERE success = 0").fetchone()[0]
    
    conn.close()
    
    # 生成访问日志HTML模板
    access_logs_template = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>卡密验证系统 - 访问日志</title>
        <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        <style>
            body {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            }
            .sidebar {
                background: linear-gradient(180deg, #4a00e0 0%, #8e2de2 100%);
                min-height: 100vh;
                padding-top: 20px;
                box-shadow: 4px 0 10px rgba(0, 0, 0, 0.1);
                z-index: 1000;
                position: fixed;
                width: inherit;
                max-width: inherit;
            }
            .sidebar-header {
                padding: 0 15px 20px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                margin-bottom: 20px;
            }
            .sidebar a {
                color: rgba(255, 255, 255, 0.8);
                display: block;
                padding: 12px 20px;
                text-decoration: none;
                transition: all 0.3s;
                margin-bottom: 5px;
                border-left: 3px solid transparent;
            }
            .sidebar a:hover {
                background-color: rgba(255, 255, 255, 0.1);
                color: #fff;
                border-left: 3px solid #fff;
            }
            .sidebar a.active {
                background-color: rgba(255, 255, 255, 0.2);
                color: #fff;
                border-left: 3px solid #fff;
            }
            .sidebar i {
                margin-right: 10px;
                width: 20px;
                text-align: center;
            }
            .main-content {
                margin-left: 16.666667%;
                padding: 30px;
            }
            .page-header {
                margin-bottom: 30px;
                border-bottom: 1px solid #dee2e6;
                padding-bottom: 15px;
            }
            .dashboard-card {
                background: #fff;
                border-radius: 15px;
                overflow: hidden;
                box-shadow: 0 10px 20px rgba(0,0,0,0.05);
                margin-bottom: 30px;
                transition: all 0.3s;
            }
            .dashboard-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 15px 30px rgba(0,0,0,0.1);
            }
            .card-header {
                padding: 20px;
                border-bottom: 1px solid #f1f1f1;
                font-weight: 600;
                background-color: #fff;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }
            .card-header .title {
                font-size: 18px;
                color: #333;
            }
            .card-body {
                padding: 20px;
            }
            .status-card {
                border-radius: 15px;
                padding: 20px;
                box-shadow: 0 10px 20px rgba(0,0,0,0.05);
                transition: all 0.3s;
                display: flex;
                align-items: center;
                margin-bottom: 30px;
            }
            .status-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            }
            .status-card .icon {
                width: 60px;
                height: 60px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 10px;
                font-size: 24px;
                margin-right: 20px;
                color: white;
            }
            .stats-blue {
                background: linear-gradient(45deg, #4e54c8, #8f94fb);
            }
            .stats-green {
                background: linear-gradient(45deg, #11998e, #38ef7d);
            }
            .stats-red {
                background: linear-gradient(45deg, #ff416c, #ff4b2b);
            }
            .stats-info {
                flex-grow: 1;
            }
            .stats-info h3 {
                font-size: 24px;
                font-weight: 700;
                margin-bottom: 0;
            }
            .stats-info p {
                color: #6c757d;
                margin-bottom: 0;
            }
            .search-bar {
                padding: 15px 20px;
                margin-bottom: 20px;
                border-radius: 10px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.05);
                background-color: #fff;
            }
            .table-container {
                background-color: #fff;
                border-radius: 10px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.05);
                overflow: hidden;
            }
            .table {
                margin-bottom: 0;
            }
            .table th {
                background-color: #f8f9fa;
                border-top: none;
                font-weight: 600;
                font-size: 14px;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }
            .table td {
                vertical-align: middle;
                padding: 15px 12px;
                font-size: 14px;
            }
            .card-status {
                font-weight: 500;
                padding: 5px 10px;
                border-radius: 20px;
                font-size: 12px;
                display: inline-block;
            }
            .status-success {
                background-color: rgba(25, 135, 84, 0.1);
                color: #198754;
            }
            .status-danger {
                background-color: rgba(220, 53, 69, 0.1);
                color: #dc3545;
            }
            .action-badge {
                padding: 5px 10px;
                border-radius: 20px;
                font-size: 12px;
                font-weight: 500;
            }
            .action-verify {
                background-color: rgba(13, 110, 253, 0.1);
                color: #0d6efd;
            }
            .action-activate {
                background-color: rgba(25, 135, 84, 0.1);
                color: #198754;
            }
            .action-register {
                background-color: rgba(102, 16, 242, 0.1);
                color: #6610f2;
            }
            .pagination {
                margin-top: 20px;
                justify-content: center;
            }
            .pagination .page-item .page-link {
                color: #6c757d;
                border: none;
                margin: 0 5px;
                border-radius: 5px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            }
            .pagination .page-item.active .page-link {
                background: linear-gradient(45deg, #8e2de2, #4a00e0);
                color: white;
                box-shadow: 0 5px 10px rgba(142, 45, 226, 0.3);
            }
            .device-id {
                font-family: monospace;
                font-size: 13px;
                max-width: 150px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            .license-key {
                font-family: monospace;
                font-size: 13px;
                background-color: #f8f9fa;
                padding: 5px 10px;
                border-radius: 5px;
                cursor: pointer;
            }
            .license-key:hover {
                background-color: #e9ecef;
            }
            .empty-data {
                text-align: center;
                padding: 60px 20px;
                color: #6c757d;
            }
            .empty-data i {
                font-size: 48px;
                margin-bottom: 20px;
                opacity: 0.2;
            }
            .empty-data p {
                margin-bottom: 0;
                font-size: 16px;
            }
        </style>
    </head>
    <body>
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-2">
                    <div class="sidebar">
                        <div class="sidebar-header">
                            <h4 class="text-center text-white fw-bold mb-0">卡密验证系统</h4>
                            <p class="text-center text-white-50 mb-0">管理面板</p>
                        </div>
                        <a href="/dashboard"><i class="fas fa-tachometer-alt"></i> 仪表盘</a>
                        <a href="/licenses"><i class="fas fa-key"></i> 卡密管理</a>
                        <a href="/create_license"><i class="fas fa-plus-circle"></i> 生成卡密</a>
                        <a href="/access_logs" class="active"><i class="fas fa-history"></i> 访问日志</a>
                        <a href="/logout"><i class="fas fa-sign-out-alt"></i> 退出登录</a>
                    </div>
                </div>
                <div class="col-md-10 main-content">
                    <div class="page-header">
                        <h2 class="mb-0">访问日志</h2>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="status-card">
                                <div class="icon stats-blue">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div class="stats-info">
                                    <h3>{{ verify_count }}</h3>
                                    <p>验证请求</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="status-card">
                                <div class="icon stats-green">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="stats-info">
                                    <h3>{{ activate_count }}</h3>
                                    <p>激活请求</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="status-card">
                                <div class="icon stats-red">
                                    <i class="fas fa-times-circle"></i>
                                </div>
                                <div class="stats-info">
                                    <h3>{{ failed_count }}</h3>
                                    <p>失败请求</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="search-bar">
                        <form method="get" action="/access_logs" class="row">
                            <div class="col-md-6">
                                <input type="text" name="search" class="form-control" placeholder="搜索卡密、设备ID或IP地址..." value="{{ search_term }}">
                            </div>
                            <div class="col-md-4">
                                <select name="action" class="form-select">
                                    <option value="" {% if not filter_action %}selected{% endif %}>所有类型</option>
                                    <option value="verify" {% if filter_action == 'verify' %}selected{% endif %}>验证</option>
                                    <option value="activate" {% if filter_action == 'activate' %}selected{% endif %}>激活</option>
                                    <option value="register" {% if filter_action == 'register' %}selected{% endif %}>注册</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-2"></i>搜索
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <div class="table-container">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>卡密</th>
                                    <th>设备ID</th>
                                    <th>IP地址</th>
                                    <th>类型</th>
                                    <th>时间</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in logs %}
                                <tr>
                                    <td>{{ log.id }}</td>
                                    <td><span class="license-key" onclick="copyToClipboard('{{ log.license_key }}')">{{ log.license_key }}</span></td>
                                    <td><span class="device-id" title="{{ log.device_id }}">{{ log.device_id }}</span></td>
                                    <td>{{ log.ip_address }}</td>
                                    <td>
                                        {% if log.action == 'verify' %}
                                            <span class="action-badge action-verify">验证</span>
                                        {% elif log.action == 'activate' %}
                                            <span class="action-badge action-activate">激活</span>
                                        {% elif log.action == 'register' %}
                                            <span class="action-badge action-register">注册</span>
                                        {% else %}
                                            {{ log.action }}
                                        {% endif %}
                                    </td>
                                    <td>{{ log.timestamp }}</td>
                                    <td>
                                        {% if log.success %}
                                            <span class="card-status status-success">成功</span>
                                        {% else %}
                                            <span class="card-status status-danger">失败</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                                
                                {% if not logs %}
                                <tr>
                                    <td colspan="7">
                                        <div class="empty-data">
                                            <i class="fas fa-history"></i>
                                            <p>
                                                {% if search_term or filter_action %}
                                                    没有找到符合条件的访问记录
                                                {% else %}
                                                    尚未记录任何访问日志
                                                {% endif %}
                                            </p>
                                        </div>
                                    </td>
                                </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页控件 -->
                    {% if total > per_page %}
                    <nav>
                        <ul class="pagination">
                            {% for i in range(1, (total // per_page) + (1 if total % per_page > 0 else 0) + 1) %}
                            <li class="page-item {% if i == page %}active{% endif %}">
                                <a class="page-link" href="/access_logs?page={{ i }}{% if search_term %}&search={{ search_term }}{% endif %}{% if filter_action %}&action={{ filter_action }}{% endif %}">{{ i }}</a>
                            </li>
                            {% endfor %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <script>
            function copyToClipboard(text) {
                navigator.clipboard.writeText(text).then(() => {
                    alert('卡密已复制到剪贴板');
                }).catch(err => {
                    console.error('复制失败:', err);
                });
            }
        </script>
    </body>
    </html>
    """
    
    # 写入模板文件
    with open(os.path.join(templates_dir, 'access_logs.html'), 'w', encoding='utf-8') as f:
        f.write(access_logs_template)
    
    return render_template('access_logs.html', 
                          logs=logs, 
                          verify_count=verify_count,
                          activate_count=activate_count,
                          failed_count=failed_count,
                          total=total,
                          page=page,
                          per_page=per_page,
                          search_term=search_term,
                          filter_action=filter_action)

@app.route('/delete_license/<int:license_id>', methods=['GET', 'POST'])
@login_required
def delete_license(license_id):
    conn = get_db_connection()
    
    # 先获取卡密信息
    license_info = conn.execute('SELECT license_key FROM licenses WHERE id = ?', (license_id,)).fetchone()
    
    if not license_info:
        conn.close()
        flash('卡密不存在', 'danger')
        return redirect(url_for('licenses'))
    
    # 执行删除
    conn.execute('DELETE FROM licenses WHERE id = ?', (license_id,))
    conn.commit()
    conn.close()
    
    flash(f'卡密 {license_info["license_key"]} 已成功删除', 'success')
    return redirect(url_for('licenses'))

@app.route('/delete_licenses', methods=['POST'])
@login_required
def delete_licenses():
    conn = get_db_connection()
    
    action = request.form.get('action', '')
    
    if action == 'delete_all':
        # 清空所有卡密
        conn.execute('DELETE FROM licenses')
        conn.commit()
        conn.close()
        flash('所有卡密已成功删除', 'success')
    
    elif action == 'delete_expired':
        # 删除过期卡密
        current_date = datetime.now().strftime('%Y-%m-%d')
        conn.execute("DELETE FROM licenses WHERE valid_until < ?", (current_date,))
        deleted_count = conn.total_changes
        conn.commit()
        conn.close()
        flash(f'{deleted_count} 个过期卡密已成功删除', 'success')
    
    elif action == 'delete_selected':
        # 删除选中的卡密
        selected_ids = request.form.get('selected_ids', '')
        if selected_ids:
            id_list = selected_ids.split(',')
            placeholders = ', '.join(['?'] * len(id_list))
            conn.execute(f'DELETE FROM licenses WHERE id IN ({placeholders})', id_list)
            conn.commit()
            conn.close()
            flash(f'{len(id_list)} 个卡密已成功删除', 'success')
        else:
            conn.close()
            flash('未选择任何卡密', 'warning')
    
    return redirect(url_for('licenses'))

# 批量解封卡密
@app.route('/unban_licenses', methods=['POST'])
@login_required
def unban_licenses():
    selected_ids = request.form.get('selected_ids', '')
    
    if not selected_ids:
        flash('没有选择要解封的卡密', 'error')
        return redirect(url_for('licenses'))
    
    id_list = selected_ids.split(',')
    
    conn = get_db_connection()
    admin_username = session.get('username')
    
    # 获取所有卡密的密钥用于日志
    keys_query = f"SELECT id, license_key FROM licenses WHERE id IN ({', '.join(['?'] * len(id_list))})"
    license_keys = {str(row['id']): row['license_key'] for row in conn.execute(keys_query, id_list).fetchall()}
    
    # 批量更新
    placeholders = ', '.join(['?'] * len(id_list))
    update_query = f"""
        UPDATE licenses 
        SET is_banned = 0, 
            ban_reason = NULL, 
            banned_at = NULL 
        WHERE id IN ({placeholders})
    """
    
    conn.execute(update_query, id_list)
    conn.commit()
    
    # 记录日志
    log_entry = f"管理员 {admin_username} 批量解封了 {len(id_list)} 个卡密"
    
    # 详细的卡密列表
    keys_detail = "，".join([f"{license_keys.get(id_str, '未知')}(ID:{id_str})" for id_str in id_list])
    detailed_log = f"{log_entry}。卡密列表: {keys_detail}"
    
    conn.execute("""
        INSERT INTO admin_logs (admin_username, action, details, timestamp)
        VALUES (?, 'unban_licenses', ?, DATETIME('now'))
    """, (admin_username, detailed_log))
    conn.commit()
    conn.close()
    
    flash(f'已成功解封 {len(id_list)} 个卡密', 'success')
    return redirect(url_for('licenses'))

@app.route('/view_license/<int:license_id>')
@login_required
def view_license(license_id):
    conn = get_db_connection()
    
    # 获取卡密详细信息
    license = conn.execute('''
        SELECT * FROM licenses WHERE id = ?
    ''', (license_id,)).fetchone()
    
    if not license:
        flash('卡密不存在', 'error')
        conn.close()
        return redirect(url_for('licenses'))
    
    if request.method == 'POST':
        software_id = request.form.get('software_id')
        user_id = request.form.get('user_id') or None
        valid_until = request.form.get('valid_until') or None
        
        conn.execute(
            'UPDATE licenses SET software_id = ?, user_id = ?, valid_until = ? WHERE id = ?',
            (software_id, user_id, valid_until, license_id)
        )
        
        # 记录管理员操作
        conn.execute(
            'INSERT INTO admin_logs (admin_username, action, details, created_at) VALUES (?, ?, ?, datetime("now"))',
            (
                session.get('username'), 
                'edit_license', 
                f'编辑了卡密 ID: {license_id}, 卡密: {license["key"]}'
            )
        )
        
        conn.commit()
        flash('卡密已更新', 'success')
        return redirect(url_for('view_license', license_id=license_id))
    
    # 获取可用的软件IDs作为建议
    software_ids = conn.execute('SELECT DISTINCT software_id FROM licenses').fetchall()
    software_ids = [row[0] for row in software_ids if row[0]]
    
    conn.close()
    
    return render_template(
        'edit_license.html', 
        license=license, 
        software_ids=software_ids,
        current_date=datetime.now().strftime('%Y-%m-%d')
    )

@app.route('/edit_license/<int:license_id>')
@login_required
def edit_license(license_id):
    """重定向到view_license以保持兼容性"""
    return redirect(url_for('view_license', license_id=license_id))

@app.route('/batch_ban_licenses', methods=['POST'])
@login_required
def batch_ban_licenses():
    selected_ids = request.form.get('selected_ids', '')
    ban_reason = request.form.get('ban_reason', '管理员封禁')
    
    if not selected_ids:
        flash('没有选择要封禁的卡密', 'error')
        return redirect(url_for('licenses'))
    
    id_list = selected_ids.split(',')
    
    conn = get_db_connection()
    admin_username = session.get('username')
    
    # 获取所有卡密的密钥用于日志
    keys_query = f"SELECT id, license_key FROM licenses WHERE id IN ({', '.join(['?'] * len(id_list))})"
    license_keys = {str(row['id']): row['license_key'] for row in conn.execute(keys_query, id_list).fetchall()}
    
    # 批量更新
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    placeholders = ', '.join(['?'] * len(id_list))
    update_query = f"""
        UPDATE licenses 
        SET is_banned = 1, 
            ban_reason = ?, 
            banned_at = ? 
        WHERE id IN ({placeholders})
    """
    
    params = [ban_reason, current_time] + id_list
    conn.execute(update_query, params)
    conn.commit()
    
    # 记录日志
    log_entry = f"管理员 {admin_username} 批量封禁了 {len(id_list)} 个卡密"
    
    # 详细的卡密列表
    keys_detail = "，".join([f"{license_keys.get(id_str, '未知')}(ID:{id_str})" for id_str in id_list])
    detailed_log = f"{log_entry}。封禁原因: {ban_reason}。卡密列表: {keys_detail}"
    
    conn.execute("""
        INSERT INTO admin_logs (admin_username, action, details, timestamp)
        VALUES (?, 'batch_ban_licenses', ?, DATETIME('now'))
    """, (admin_username, detailed_log))
    conn.commit()
    conn.close()
    
    flash(f'已成功封禁 {len(id_list)} 个卡密', 'success')
    return redirect(url_for('licenses'))

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5001, debug=True) 