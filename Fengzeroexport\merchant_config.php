<?php
return [
    'category_name' => '功能插件',
    'name' => '商家订单导出工具',
    'description' => '商家端订单和卡密导出工具，只能导出自己账户的订单信息，包含订单号、商品名、卡密、购买时间、商品金额等信息',
    'version' => '1.0.0',
    'logo' => 'data:image/svg+xml;base64,'.base64_encode('<?xml version="1.0" encoding="UTF-8"?>
<svg width="128" height="128" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="48" height="48" rx="8" fill="#f0f5ff"/>
    <path d="M8 12L24 6L40 12V28L24 34L8 28V12Z" fill="#e6f4ff" stroke="#2f54eb" stroke-width="2"/>
    <path d="M24 6V34" stroke="#2f54eb" stroke-width="2"/>
    <path d="M8 12L40 28" stroke="#2f54eb" stroke-width="2"/>
    <path d="M40 12L8 28" stroke="#2f54eb" stroke-width="2"/>
    <rect x="18" y="18" width="12" height="8" fill="#2f54eb" rx="2"/>
    <path d="M20 20H26M20 22H26M20 24H24" stroke="white" stroke-width="1" stroke-linecap="round"/>
    <circle cx="32" cy="16" r="3" fill="#52c41a"/>
    <path d="M30 16L31.5 17.5L34 15" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>'),
    'menu' => [
        [
            'tag' => 'iframe',
            'name' => '订单导出',
            'src' => (string)plugin_url("Fengzeroexport/User/merchant", [], false, true),
        ]
    ]
]; 
