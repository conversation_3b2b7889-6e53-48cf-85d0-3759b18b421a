<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>批量注册</title>
    <style>
    .el-card {
        margin: 20px;
    }
    .upload-tip {
        color: #666;
        font-size: 14px;
        margin-top: 10px;
    }
    .result-number {
        font-size: 24px;
        font-weight: bold;
        text-align: center;
    }
    .result-number.success {
        color: #67C23A;
    }
    .result-number.error {
        color: #F56C6C;
    }
    </style>
</head>
<body>
<div id="app">
    <el-card shadow="never">
        <template #header>
            <div class="card-header">
                <span>批量注册用户</span>
            </div>
        </template>

        <el-alert
            title="文件格式说明"
            type="info"
            description="1. 文件必须是txt格式&#10;2. 每行一条数据，格式：账号----密码----手机号&#10;3. 手机号必须是11位有效号码"
            :closable="false"
            show-icon
            style="margin-bottom: 20px;">
        </el-alert>

        <el-form-item label="手动输入">
            <el-input
                v-model="inputText"
                type="textarea"
                :rows="5"
                placeholder="每行一条数据，格式：&#10;user001----password123----13800138000"
            ></el-input>
        </el-form-item>

        <el-divider>或者</el-divider>

        <el-upload
            action="#"
            accept=".txt"
            :auto-upload="false"
            :on-change="handleFileChange"
            :show-file-list="false">
            <el-button type="primary">选择TXT文件</el-button>
            <div class="upload-tip">
                示例：user001----password123----13800138000
            </div>
        </el-upload>

        <el-button 
            type="success" 
            style="margin-top: 20px;"
            :loading="isLoading"
            @click="handleUpload">
            开始注册
        </el-button>

        <el-table 
            v-if="previewData.length" 
            :data="previewData" 
            style="margin-top: 20px;"
            border>
            <el-table-column type="index" label="序号" width="80"></el-table-column>
            <el-table-column prop="username" label="账号"></el-table-column>
            <el-table-column prop="password" label="密码"></el-table-column>
            <el-table-column prop="mobile" label="手机号"></el-table-column>
            <el-table-column prop="status" label="状态" width="120">
                <template #default="scope">
                    <el-tag :type="getStatusType(scope.row.status)">
                        {{ scope.row.status || '待处理' }}
                    </el-tag>
                </template>
            </el-table-column>
        </el-table>

        <div v-if="registrationResults" style="margin-top: 20px;">
            <el-divider>注册结果</el-divider>
            
            <el-row :gutter="20">
                <el-col :span="8">
                    <el-card>
                        <template #header>总数</template>
                        <div class="result-number">{{ registrationResults.total }}</div>
                    </el-card>
                </el-col>
                <el-col :span="8">
                    <el-card>
                        <template #header>成功</template>
                        <div class="result-number success">{{ registrationResults.success }}</div>
                    </el-card>
                </el-col>
                <el-col :span="8">
                    <el-card>
                        <template #header>失败</template>
                        <div class="result-number error">{{ registrationResults.skipped }}</div>
                    </el-card>
                </el-col>
            </el-row>

            <el-table
                v-if="registrationResults.skipped_users.length"
                :data="registrationResults.skipped_users"
                style="margin-top: 20px;"
                border>
                <el-table-column type="index" label="序号" width="80"></el-table-column>
                <el-table-column prop="username" label="账号"></el-table-column>
                <el-table-column prop="reason" label="失败原因"></el-table-column>
            </el-table>
        </div>
    </el-card>
</div>

<script src="/static/others/vue/vue.global.prod.js"></script>
<script src="/static/others/element-plus/index.full.min.js"></script>
<script src="/static/others/axios/axios.min.js"></script>

<script>
const { ref, reactive } = Vue;
const app = Vue.createApp({
    setup() {
        const isLoading = ref(false);
        const previewData = ref([]);
        const registrationResults = ref(null);
        const manualFormRef = ref(null);
        const manualLoading = ref(false);
        const manualForm = reactive({
            username: '',
            password: '',
            mobile: ''
        });
        const inputText = ref('');

        const rules = {
            username: [
                { required: true, message: '请输入账号', trigger: 'blur' },
                { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
            ],
            password: [
                { required: true, message: '请输入密码', trigger: 'blur' },
                { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
            ],
            mobile: [
                { required: true, message: '请输入手机号', trigger: 'blur' },
                { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
            ]
        };

        const getStatusType = (status) => {
            switch(status) {
                case '注册成功': return 'success';
                case '已存在': return 'warning';
                default: return 'info';
            }
        };

        const handleFileChange = (file) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                const content = e.target.result;
                const lines = content.split('\n');
                previewData.value = lines
                    .filter(line => line.trim())
                    .map(line => {
                        const [username, password, mobile] = line.split('----').map(item => item.trim());
                        return { 
                            username: username || '',
                            password: password || '',
                            mobile: mobile || '',
                            status: '待处理'
                        };
                    });
                registrationResults.value = null;
            };
            reader.readAsText(file.raw);
        };

        const handleUpload = async () => {
            let usersToRegister = [];
            
            if (inputText.value.trim()) {
                const lines = inputText.value.split('\n').filter(line => line.trim());
                usersToRegister = lines.map(line => {
                    const [username, password, mobile] = line.split('----').map(item => item.trim());
                    return { username, password, mobile };
                });
            } 
            else if (previewData.value.length) {
                usersToRegister = previewData.value;
            } else {
                ElementPlus.ElMessage.warning('请输入数据或选择文件');
                return;
            }

            for (let i = 0; i < usersToRegister.length; i++) {
                const user = usersToRegister[i];
                if (!user.username || !user.password || !user.mobile) {
                    ElementPlus.ElMessage.error(`第${i + 1}行数据不完整`);
                    return;
                }
                if (!/^1[3-9]\d{9}$/.test(user.mobile)) {
                    ElementPlus.ElMessage.error(`第${i + 1}行手机号格式不正确`);
                    return;
                }
            }

            isLoading.value = true;
            try {
                const response = await axios.post('register', {
                    users: usersToRegister
                });
                
                if (response.data.code === 200) {
                    registrationResults.value = response.data.data;
                    ElementPlus.ElMessage.success(response.data.msg);
                    inputText.value = '';
                    previewData.value = [];
                } else {
                    throw new Error(response.data.msg);
                }
            } catch (error) {
                console.error('上传错误：', error);
                const errorMsg = error.response?.data?.msg || error.message || '注册失败';
                ElementPlus.ElMessage.error(errorMsg);
            } finally {
                isLoading.value = false;
            }
        };

        const handleManualRegister = async () => {
            if (!manualFormRef.value) return;
            
            try {
                await manualFormRef.value.validate();
                manualLoading.value = true;
                
                const response = await axios.post('register', {
                    users: [manualForm]
                });
                
                if (response.data.code === 200) {
                    ElementPlus.ElMessage.success('注册成功');
                    manualFormRef.value.resetFields();
                } else {
                    throw new Error(response.data.msg);
                }
            } catch (error) {
                const errorMsg = error.response?.data?.msg || error.message || '注册失败';
                ElementPlus.ElMessage.error(errorMsg);
            } finally {
                manualLoading.value = false;
            }
        };

        return {
            isLoading,
            previewData,
            registrationResults,
            handleFileChange,
            handleUpload,
            getStatusType,
            manualForm,
            manualFormRef,
            manualLoading,
            rules,
            handleManualRegister,
            inputText,
        };
    }
});

app.use(ElementPlus);
app.mount("#app");
</script>
</body>
</html>
