<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>手机端导航设置</title>
    <style>
        .el-form-item-tip {
            font-size: 12px;
            color: #909399;
            margin-top: 5px;
        }
        .icon-item {
            border: 1px solid #EBEEF5;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
            background: #FAFAFA;
        }
        .icon-header {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .icon-title {
            font-size: 14px;
            font-weight: 500;
            color: #606266;
        }
        .icon-actions {
            margin-top: 10px;
            display: flex;
            gap: 15px;
        }
        .el-input-number.is-controls-right .el-input__wrapper {
            padding-left: 15px;
            padding-right: 50px;
        }
        .el-input-number.is-controls-right .el-input-number__decrease, 
        .el-input-number.is-controls-right .el-input-number__increase {
            height: 50%;
            line-height: 15px;
        }
        
        /* 优化表单布局 */
        .form-section {
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #ebeef5;
        }

        .form-section-title {
            font-weight: 500;
            margin-bottom: 15px;
            color: #409EFF;
            font-size: 16px;
        }
        
        .mobile-form-items {
            opacity: 0.6;
            pointer-events: none;
        }
        
        .mobile-form-items.enabled {
            opacity: 1;
            pointer-events: auto;
        }
        
        /* 禁用状态样式 */
        .disabled-form {
            opacity: 0.6;
            pointer-events: none;
            filter: grayscale(30%);
        }
    </style>
</head>

<body>
    <div id="app">
        <el-card shadow="never">
            <el-form :model="form" label-width="120px">
                <!-- 基本设置 -->
                <div class="form-section">
                    <div class="form-section-title">基本设置</div>
                    
                    <el-form-item label="导航栏开关：">
                        <el-switch 
                            v-model="form.status" 
                            :active-value="1" 
                            :inactive-value="0" 
                            @change="updateStatus"
                            active-text="开启"
                            inactive-text="关闭"
                        />
                        <div class="el-form-item-tip">{{form.status === 1 ? '当前状态：开启' : '当前状态：关闭'}}</div>
                    </el-form-item>
                    
                    <div :class="{'disabled-form': form.status === 0}" style="transition: all 0.3s ease;">
                        <el-form-item label="导航栏标题：">
                            <el-input 
                                v-model="form.nav_title" 
                                placeholder="请输入导航栏标题文本"
                                maxlength="20"
                                show-word-limit
                            />
                            <div class="el-form-item-tip">导航栏显示的标题文本，默认为"快捷导航"</div>
                        </el-form-item>
                        
                        <!-- 已移除显示位置、最大图标数和堆叠层级设置项 -->
                    </div>
                </div>

                <!-- 图标设置 -->
                <div class="form-section">
                    <div class="form-section-title">图标设置</div>
                    
                    <el-form-item label="导航图标：">
                        <div v-for="(item, index) in iconItems" :key="index" class="icon-item">
                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <div class="icon-header">
                                        <span class="icon-title">图标 {{ index + 1 }}</span>
                                    </div>
                                </el-col>
                                <el-col :span="24" style="margin-top: 10px;">
                                    <el-form-item label="图标名称:" label-width="80px">
                                        <el-input v-model="item.name" placeholder="请输入图标显示名称" />
                                        <div class="el-form-item-tip">提示：使用 | 符号可以实现换行显示，如"商品|管理"</div>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="24">
                                    <el-form-item label="SVG图标:" label-width="80px">
                                        <el-input
                                            v-model="item.icon"
                                            type="textarea"
                                            :rows="4"
                                            placeholder="请输入SVG图标代码"
                                        ></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="24">
                                    <el-form-item label="链接地址:" label-width="80px">
                                        <el-input v-model="item.link" placeholder="请输入点击跳转链接" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <div class="icon-actions">
                                <el-button 
                                    v-if="iconItems.length > 1" 
                                    type="danger" 
                                    link 
                                    @click="removeIconItem(index)"
                                >
                                    删除此图标
                                </el-button>
                            </div>
                        </div>
                        <el-button type="primary" plain @click="addIconItem" style="margin-top: 15px;">
                            添加图标
                        </el-button>
                        <div class="el-form-item-tip" style="margin-top: 8px;">
                            提示：图标采用SVG格式，可以从图标库中获取SVG代码。
                        </div>
                    </el-form-item>
                </div>

                <el-form-item>
                    <el-button type="primary" :loading="loading" @click="save">保存设置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
    </div>

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>

    <script>
        const { createApp, ref, reactive, computed } = Vue;
        const { ElMessage } = ElementPlus;

        // 将setup函数提取为独立的组合式函数
        const useIconNavigation = () => {
            const loading = ref(false);
            const iconItems = ref([
                {
                    name: '首页',
                    icon: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#409EFF" width="24" height="24"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8h5z"/></svg>',
                    link: '/'
                }
            ]);
            
            // 预览数据
            const form = reactive({
                status: 1,
                nav_title: '快捷导航',
                position: 'bottom',  // 保留默认值，但不在UI中显示
                max_icons: 10,       // 保留默认值，但不在UI中显示
                z_index: 1000,       // 保留默认值，但不在UI中显示
            });

            const updatePreview = () => {
                // 简化函数，仅更新预览相关逻辑
            };

            const addIconItem = () => {
                if (iconItems.value.length >= form.max_icons) {
                    ElMessage.warning(`最多只能添加${form.max_icons}个图标`);
                    return;
                }
                
                const newItem = {
                    name: '',
                    icon: '',
                    link: ''
                };
                
                iconItems.value.push(newItem);
            };

            const removeIconItem = (index) => {
                if (iconItems.value.length <= 1) {
                    ElMessage.warning('至少需要保留一个图标');
                    return;
                }
                iconItems.value.splice(index, 1);
            };

            const updateStatus = (val) => {
                form.status = val;
                // 当状态变化时，显示对应提示
                if (val === 0) {
                    ElMessage.warning('导航栏已关闭，前台将不显示导航图标');
                } else {
                    ElMessage.success('导航栏已开启，前台将显示导航图标');
                }
            };

            const fetchData = async () => {
                loading.value = true;
                try {
                    const { data: response } = await axios.post("/plugin/Wzsjd/Api/fetchData");
                    if (response?.code === 200) {
                        const { data } = response;
                        
                        // 确保status值正确解析为数字
                        const statusValue = data.status !== undefined ? parseInt(data.status) : 1;
                        
                        Object.assign(form, {
                            status: statusValue,
                            nav_title: data.nav_title || '快捷导航',
                            position: data.position || 'bottom',    // 保留但不显示
                            max_icons: parseInt(data.max_icons || 10),  // 保留但不显示
                            z_index: parseInt(data.z_index || 1000),   // 保留但不显示
                        });
                        
                        // 如果状态为关闭，静默处理，不显示提示
                        // 防止每次加载页面都弹出提示消息
                        
                        if (data.icons) {
                            try {
                                let parsedIcons;
                                // 检查icons是否已经是对象数组
                                if (Array.isArray(data.icons)) {
                                    parsedIcons = data.icons;
                                } else {
                                    // 尝试解析字符串
                                    parsedIcons = JSON.parse(data.icons);
                                }
                                
                                // 只有当parsedIcons有效且不为空时才更新图标数据
                                if (Array.isArray(parsedIcons) && parsedIcons.length > 0) {
                                    iconItems.value = parsedIcons;
                                } else if (iconItems.value.length === 0) {
                                    // 仅当当前没有图标数据时才设置默认图标
                                    iconItems.value = [{
                                        name: '首页',
                                        icon: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#409EFF" width="24" height="24"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8h5z"/></svg>',
                                        link: '/'
                                    }];
                                }
                            } catch (e) {
                                // 静默处理错误
                                // 解析失败时，仅当当前没有图标数据时才设置默认图标
                                if (iconItems.value.length === 0) {
                                    iconItems.value = [{
                                        name: '首页',
                                        icon: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#409EFF" width="24" height="24"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8h5z"/></svg>',
                                        link: '/'
                                    }];
                                }
                            }
                        }
                    }
                } catch (error) {
                    ElMessage.error('获取数据失败');
                } finally {
                    loading.value = false;
                }
            };

            const save = async () => {
                loading.value = true;
                try {
                    // 验证图标数据
                    if (iconItems.value.length === 0) {
                        ElMessage.error('请至少添加一个图标');
                        loading.value = false;
                        return;
                    }
                    
                    for (let i = 0; i < iconItems.value.length; i++) {
                        const item = iconItems.value[i];
                        if (!item.name || !item.name.trim()) {
                            ElMessage.error(`第${i+1}个图标名称不能为空`);
                            loading.value = false;
                            return;
                        }
                        
                        if (!item.icon || !item.icon.trim()) {
                            ElMessage.error(`第${i+1}个SVG图标不能为空`);
                            loading.value = false;
                            return;
                        }
                        
                        if (!item.link || !item.link.trim()) {
                            ElMessage.error(`第${i+1}个链接地址不能为空`);
                            loading.value = false;
                            return;
                        }
                    }

                    const formData = new FormData();
                    Object.entries(form).forEach(([key, value]) => {
                        formData.append(key, value);
                    });
                    
                    // 添加图标数据（无论导航栏状态如何，都保存图标数据）
                    const iconsJson = JSON.stringify(iconItems.value);
                    formData.append('icons', iconsJson);
                    
                    const { data: response } = await axios.post("/plugin/Wzsjd/Api/save", formData);
                    if (response?.code === 200) {
                        ElMessage.success('保存成功');
                    } else {
                        ElMessage.error(response?.msg || '保存失败');
                    }
                } catch (error) {
                    ElMessage.error('保存失败');
                } finally {
                    loading.value = false;
                }
            };

            // 初始化数据
            fetchData();

            return {
                loading,
                form,
                iconItems,
                addIconItem,
                removeIconItem,
                save,
            };
        };

        createApp({
            setup() {
                return useIconNavigation();
            }
        })
        .use(ElementPlus, {
            locale: ElementPlusLocaleZhCn
        })
        .mount('#app');
    </script>
</body>
</html> 