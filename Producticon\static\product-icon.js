(function() {
    function getShopInfo() {
        let shopName = '';
        let merchantId = '';
        
        const urlParams = new URLSearchParams(window.location.search);
        merchantId = urlParams.get('merchant_id') || '';
        
        const selectors = [
            '.nickname',
            '.shop-name',
            '[data-shop-name]',
            '#shop-name',
            '.merchant-info .name'
        ];
        
        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element) {
                shopName = element.textContent.trim() || element.dataset.shopName;
                if (shopName) break;
            }
        }
        
        if (!shopName) {
            const pageTitle = document.title.trim();
            const patterns = [
                /^(.*?)的小店/,
                /^(.*?)店铺/,
                /商家：(.*?)$/,
                /店铺：(.*?)$/
            ];
            
            for (const pattern of patterns) {
                const match = pageTitle.match(pattern);
                if (match && match[1]) {
                    shopName = match[1];
                    break;
                }
            }
        }
        
        if (!shopName) {
            const metaTags = [
                'meta[name="shop-name"]',
                'meta[property="og:site_name"]',
                'meta[name="merchant-name"]'
            ];
            
            for (const selector of metaTags) {
                const meta = document.querySelector(selector);
                if (meta) {
                    shopName = meta.getAttribute('content');
                    if (shopName) break;
                }
            }
        }

        return { shopName, merchantId };
    }

    function addIconToProduct(productElement, config) {
        const existingIcon = productElement.querySelector('.product-custom-icon');
        if (existingIcon) return;

        const iconElement = document.createElement('div');
        iconElement.className = 'product-custom-icon';
        iconElement.innerHTML = `<img src="${config.icon_url}" alt="icon">`;

        const style = document.createElement('style');
        style.textContent = `
            .product-custom-icon {
                position: absolute;
                z-index: 99;
                pointer-events: none;
            }
            .product-custom-icon img {
                width: ${config.size}px;
                height: ${config.size}px;
                object-fit: contain;
            }
            @media screen and (max-width: 768px) {
                .product-custom-icon {
                    z-index: 9;
                }
                .product-custom-icon img {
                    width: ${Math.max(20, Math.floor(config.size * 0.7))}px;
                    height: ${Math.max(20, Math.floor(config.size * 0.7))}px;
                }
                [data-v-ba6673da] .product-custom-icon {
                    position: absolute;
                    transform: translateY(-50%);
                }
                [data-v-ba6673da] .info .product-custom-icon {
                    top: 20px !important;
                }
            }
        `;
        document.head.appendChild(style);

        const isMobile = window.innerWidth <= 768;
        const positionStyle = {
            'right-top': isMobile ? 
                { top: '20px', right: '5px' } : 
                { top: '5px', right: '5px' },
            'left-top': isMobile ? 
                { top: '20px', left: '5px' } : 
                { top: '5px', left: '5px' },
            'right-bottom': { bottom: '5px', right: '5px' },
            'left-bottom': { bottom: '5px', left: '5px' }
        }[config.position];

        Object.assign(iconElement.style, {
            position: 'absolute',
            ...positionStyle
        });

        const elementPosition = window.getComputedStyle(productElement).position;
        if (elementPosition === 'static') {
            productElement.style.position = 'relative';
        }

        productElement.appendChild(iconElement);
    }

    function checkProductMatch(productName, keywords) {
        if (!keywords || !productName) return false;
        
        const keywordList = keywords.split(',')
            .map(k => k.trim())
            .filter(k => k.length > 0);
            
        if (keywordList.length === 0) return false;
        
        return keywordList.some(keyword => {
            return productName.toLowerCase().includes(keyword.toLowerCase());
        });
    }

    function processProducts(config) {
        if (!config || !config.status || !Array.isArray(config.icons)) return;
        
        const products = document.querySelectorAll('.goods_item, [data-v-ba6673da].info, .goods-group-item');
        
        products.forEach(product => {
            const nameElement = product.querySelector('.name, .goods-name, .goods-item-info-title');
            if (!nameElement) return;
            
            const productName = nameElement.textContent.trim();
            if (!productName) return;
            
            config.icons.forEach(iconConfig => {
                if (checkProductMatch(productName, iconConfig.keywords)) {
                    addIconToProduct(product, iconConfig);
                }
            });
        });
    }

    function init() {
        const { shopName, merchantId } = getShopInfo();
        
        const params = new URLSearchParams();
        if (shopName) params.append('shop_name', shopName);
        if (merchantId) params.append('merchant_id', merchantId);

        fetch(window.location.protocol + '//' + window.location.host + '/plugin/Producticon/api/fetchConfig?' + params.toString(), {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200 && data.data) {
                const config = data.data;
                if (config.status === 1 && Array.isArray(config.icons) && config.icons.length > 0) {
                    processProducts(config);
                    
                    const observer = new MutationObserver(() => {
                        processProducts(config);
                    });
                    
                    observer.observe(document.body, {
                        childList: true,
                        subtree: true
                    });
                }
            }
        });
    }

    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
})();