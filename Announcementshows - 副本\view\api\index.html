<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>公告显示设置</title>
    <style>
        
        /* 修改日期选择器的样式 */
        .el-date-picker {
            --el-text-color-regular: #606266;
        }
        .el-date-picker__header-label,
        .el-date-table th,
        .el-date-table td {
            color: #606266;
        }
        /* 添加日期数字的样式 */
        .el-date-table td .el-date-table-cell__text {
            color: #606266;
        }
        .el-date-table td.next-month .el-date-table-cell__text,
        .el-date-table td.prev-month .el-date-table-cell__text {
            color: #C0C4CC;
        }
        .el-date-table td.today .el-date-table-cell__text {
            color: #409EFF;
        }
        .el-date-table td.current .el-date-table-cell__text {
            color: #fff;
            background-color: #409EFF;
        }
        .announcement-item {
            border: 1px solid #EBEEF5;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
            background: #FAFAFA;
        }
        .announcement-header {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .announcement-title {
            font-size: 14px;
            font-weight: 500;
            color: #606266;
        }
        .announcement-actions {
            margin-top: 15px;
            display: flex;
            gap: 10px;
            justify-content: flex-start;
        }
        .el-input-number.is-controls-right .el-input__wrapper {
            padding-left: 15px;
            padding-right: 50px;
        }
        .el-input-number.is-controls-right .el-input-number__decrease, 
        .el-input-number.is-controls-right .el-input-number__increase {
            height: 50%;
            line-height: 15px;
        }
        .announcement-upload {
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            text-align: center;
            padding: 20px;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 120px;
        }
        .announcement-upload:hover {
            border-color: #409EFF;
            background-color: rgba(64, 158, 255, 0.05);
        }
        .uploaded-image {
            max-width: 100%;
            max-height: 300px;
            object-fit: contain;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
        }
        
        .el-form-item-tip {
            font-size: 12px;
            color: #909399;
            margin-top: 5px;
        }
        .empty-ad-upload {
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            text-align: center;
            padding: 20px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 120px;
        }
        .empty-ad-upload:hover {
            border-color: #409EFF;
            background-color: rgba(64, 158, 255, 0.05);
        }
        .price-card {
            margin-bottom: 20px;
        }
        .price-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .price-header span {
            font-size: 14px;
            font-weight: 500;
        }
        .el-input-number.is-controls-right {
            width: 200px;
        }

        /* 添加轮播设置相关的样式 */
        .interval-select .el-input__wrapper {
            width: 180px;
        }

        .disabled-form-item .el-form-item__label {
            color: #a8abb2;
        }

        /* 优化表单布局 */
        .form-section {
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #ebeef5;
        }

        .form-section-title {
            font-weight: 500;
            margin-bottom: 15px;
            color: #409EFF;
            font-size: 16px;
        }

        /* 新增图片控制样式 */
        .image-controls {
            margin-top: 10px;
            display: flex;
            gap: 10px;
            justify-content: center;
        }
        .image-preview-container {
            position: relative;
            width: 100%;
            margin-bottom: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .image-actions {
            position: absolute;
            top: 5px;
            right: 5px;
            display: flex;
            gap: 5px;
            z-index: 2;
        }
        .image-actions .el-button {
            background: rgba(255, 255, 255, 0.8);
            border: none;
        }

        /* 图片预览对话框样式 */
        .image-preview-dialog-content {
            padding: 0 !important;
            overflow: hidden !important;
        }
        
        /* 添加加载指示器样式 */
        .upload-loading {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            background: rgba(255, 255, 255, 0.7);
            z-index: 10;
        }

        /* 改进图片预览样式 */
        .el-message-box.image-preview-dialog {
            width: auto !important;
            max-width: 90vw !important;
        }

        /* 颜色设置样式 */
        .color-settings {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 20px;
        }
        .color-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            white-space: nowrap;
        }
        .color-label {
            margin-right: 10px;
            font-size: 14px;
            color: #606266;
        }
        .gradient-option {
            margin-left: 10px;
            margin-top: 5px;
            flex-basis: 100%;
        }

        /* 导航菜单样式 */
        .nav-container {
            margin-bottom: 20px;
        }

        .nav-tabs {
            border-bottom: 2px solid #e4e7ed;
            margin-bottom: 20px;
            display: flex;
            flex-wrap: nowrap;
            overflow-x: auto;
            white-space: nowrap;
        }

        .nav-tab {
            display: inline-block;
            padding: 12px 16px;
            margin-right: 2px;
            background: #f5f7fa;
            border: 1px solid #e4e7ed;
            border-bottom: none;
            cursor: pointer;
            color: #606266;
            font-size: 14px;
            transition: all 0.3s;
            border-radius: 4px 4px 0 0;
            flex: 0 0 auto;
        }

        .nav-tab:hover {
            background: #ecf5ff;
            color: #409eff;
        }

        .nav-tab.active {
            background: #fff;
            color: #409eff;
            border-color: #409eff;
            border-bottom: 2px solid #fff;
            margin-bottom: -2px;
            font-weight: 500;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .save-button-container {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e4e7ed;
            text-align: center;
        }
    </style>
</head>

<body>
    <div id="app">
        <el-card shadow="never">
            <!-- 导航菜单 -->
            <div class="nav-container">
                <div class="nav-tabs">
                    <div class="nav-tab" :class="{ active: activeTab === 'basic' }" @click="activeTab = 'basic'">
                        基本设置
                    </div>
                    <div class="nav-tab" :class="{ active: activeTab === 'mobile' }" @click="activeTab = 'mobile'">
                        手机端设置
                    </div>
                    <div class="nav-tab" :class="{ active: activeTab === 'price' }" @click="activeTab = 'price'">
                        价格设置
                    </div>
                    <div class="nav-tab" :class="{ active: activeTab === 'auto_delete' }" @click="activeTab = 'auto_delete'">
                        自动删除设置
                    </div>
                    <div class="nav-tab" :class="{ active: activeTab === 'display' }" @click="activeTab = 'display'">
                        展示设置
                    </div>
                    <div class="nav-tab" :class="{ active: activeTab === 'content' }" @click="activeTab = 'content'">
                        广告内容设置
                    </div>
                </div>
            </div>

            <el-form :model="form" label-width="120px">
                <!-- 基本设置 -->
                <div class="tab-content" :class="{ active: activeTab === 'basic' }">
                    
                    <el-form-item label="公告开关：">
                        <el-switch v-model="form.status" :active-value="1" :inactive-value="0" />
                    </el-form-item>

                    <el-form-item label="颜色设置：">
                        <div class="color-settings">
                            <div class="color-item">
                                <span class="color-label">文字颜色：</span>
                                <el-color-picker v-model="form.text_color" />
                            </div>
                            <div class="color-item">
                                <span class="color-label">背景颜色：</span>
                                <el-color-picker v-model="form.background" />
                            </div>
                            <div class="gradient-option">
                                <el-checkbox v-model="form.use_gradient">使用渐变背景</el-checkbox>
                            </div>
                        </div>
                    </el-form-item>

                    <el-form-item label="高度(px)：">
                        <el-input-number 
                            v-model="form.height" 
                            :min="30" 
                            :max="500" 
                            :step="10"
                            style="width: 150px;"
                        />
                        <div class="el-form-item-tip">可设置30-500px之间的高度</div>
                    </el-form-item>

                    <el-form-item label="内边距(px)：">
                        <el-input-number 
                            v-model="form.padding" 
                            :min="0" 
                            :max="50" 
                            :step="1"
                        />
                    </el-form-item>

                    <el-form-item label="内容换行：">
                        <el-switch v-model="form.wrap" :active-value="1" :inactive-value="0" />
                    </el-form-item>
                </div>

                <!-- 手机端设置 -->
                <div class="tab-content" :class="{ active: activeTab === 'mobile' }">
                    
                    <el-form-item label="手机端开关：">
                        <el-switch v-model="form.mobile_enabled" :active-value="1" :inactive-value="0" />
                        <div class="el-form-item-tip">开启后移动设备访问时会显示手机端广告模板</div>
                    </el-form-item>

                    <el-form-item label="当前模板：">
                        <el-radio-group v-model="form.mobile_current_template" :disabled="!form.mobile_enabled">
                            <el-radio :label="1">模板1</el-radio>
                            <el-radio :label="2">模板2</el-radio>
                            <el-radio :label="3">模板3</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    
                    <el-form-item label="模板1开关：">
                        <el-switch v-model="form.mobile_template1_enabled" :active-value="1" :inactive-value="0" :disabled="!form.mobile_enabled" />
                        <div class="el-form-item-tip">Banner轮播广告样式</div>
                    </el-form-item>
                    
                    <el-form-item label="模板1样式：">
                        <el-select v-model="form.mobile_template1_style" :disabled="!form.mobile_enabled || !form.mobile_template1_enabled" style="width: 200px;">
                            <el-option label="Banner样式" value="banner"></el-option>
                            <el-option label="卡片样式" value="card"></el-option>
                            <el-option label="简约样式" value="simple"></el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="模板2开关：">
                        <el-switch v-model="form.mobile_template2_enabled" :active-value="1" :inactive-value="0" :disabled="!form.mobile_enabled" />
                        <div class="el-form-item-tip">走马灯滚动广告样式</div>
                    </el-form-item>
                    
                    <el-form-item label="模板2样式：">
                        <el-select v-model="form.mobile_template2_style" :disabled="!form.mobile_enabled || !form.mobile_template2_enabled" style="width: 200px;">
                            <el-option label="走马灯效果" value="marquee"></el-option>
                            <el-option label="淡入淡出效果" value="fade"></el-option>
                            <el-option label="滑动效果" value="slide"></el-option>
                        </el-select>
                    </el-form-item>
                    
                    <el-form-item label="模板3开关：">
                        <el-switch v-model="form.mobile_template3_enabled" :active-value="1" :inactive-value="0" :disabled="!form.mobile_enabled" />
                        <div class="el-form-item-tip">彩色卡片广告样式</div>
                    </el-form-item>
                    
                    <el-form-item label="模板3样式：">
                        <el-select v-model="form.mobile_template3_style" :disabled="!form.mobile_enabled || !form.mobile_template3_enabled" style="width: 200px;">
                            <el-option label="彩色卡片" value="color_cards"></el-option>
                            <el-option label="暗色模式" value="dark_mode"></el-option>
                            <el-option label="亮色模式" value="light_mode"></el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="展示模式：">
                        <el-radio-group v-model="form.mobile_display_mode" :disabled="!form.mobile_enabled">
                            <el-radio :label="0">文字模式</el-radio>
                            <el-radio :label="1">图片模式</el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <el-form-item label="手机端高度：">
                        <el-input-number 
                            v-model="form.mobile_height" 
                            :min="30" 
                            :max="300" 
                            :step="5"
                            :disabled="!form.mobile_enabled"
                            style="width: 150px;"
                        />
                        <div class="el-form-item-tip">推荐设置为30-100px之间的高度</div>
                    </el-form-item>

                    <el-form-item label="手机端内边距：">
                        <el-input-number 
                            v-model="form.mobile_padding" 
                            :min="0" 
                            :max="30" 
                            :step="1"
                            :disabled="!form.mobile_enabled"
                        />
                    </el-form-item>

                    <el-form-item label="手机端背景色：">
                        <el-color-picker v-model="form.mobile_background" :disabled="!form.mobile_enabled" />
                    </el-form-item>

                    <el-form-item label="手机端文字色：">
                        <el-color-picker v-model="form.mobile_text_color" :disabled="!form.mobile_enabled" />
                    </el-form-item>
                </div>

                <!-- 价格设置 -->
                <div class="tab-content" :class="{ active: activeTab === 'price' }">
                    <el-form-item label="价格设置：">
                        <el-row :gutter="20">
                            <el-col :span="24">
                                <el-card shadow="never" class="price-card">
                                    <template #header>
                                        <div class="price-header">
                                            <span>开通价格</span>
                                        </div>
                                    </template>
                                    <el-form-item label="月费：">
                                        <el-input-number v-model="form.month_price" :min="0" :precision="2" :step="10">
                                            <template #prefix>￥</template>
                                        </el-input-number>
                                    </el-form-item>
                                    <el-form-item label="季费：">
                                        <el-input-number v-model="form.quarter_price" :min="0" :precision="2" :step="10">
                                            <template #prefix>￥</template>
                                        </el-input-number>
                                    </el-form-item>
                                    <el-form-item label="半年费：">
                                        <el-input-number v-model="form.halfyear_price" :min="0" :precision="2" :step="10">
                                            <template #prefix>￥</template>
                                        </el-input-number>
                                    </el-form-item>
                                    <el-form-item label="年费：">
                                        <el-input-number v-model="form.year_price" :min="0" :precision="2" :step="10">
                                            <template #prefix>￥</template>
                                        </el-input-number>
                                    </el-form-item>
                                </el-card>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20" style="margin-top: 20px;">
                            <el-col :span="24">
                                <el-card shadow="never" class="price-card">
                                    <template #header>
                                        <div class="price-header">
                                            <span>续费价格</span>
                                        </div>
                                    </template>
                                    <el-form-item label="月费：">
                                        <el-input-number v-model="form.renew_month_price" :min="0" :precision="2" :step="10">
                                            <template #prefix>￥</template>
                                        </el-input-number>
                                    </el-form-item>
                                    <el-form-item label="季费：">
                                        <el-input-number v-model="form.renew_quarter_price" :min="0" :precision="2" :step="10">
                                            <template #prefix>￥</template>
                                        </el-input-number>
                                    </el-form-item>
                                    <el-form-item label="半年费：">
                                        <el-input-number v-model="form.renew_halfyear_price" :min="0" :precision="2" :step="10">
                                            <template #prefix>￥</template>
                                        </el-input-number>
                                    </el-form-item>
                                    <el-form-item label="年费：">
                                        <el-input-number v-model="form.renew_year_price" :min="0" :precision="2" :step="10">
                                            <template #prefix>￥</template>
                                        </el-input-number>
                                    </el-form-item>
                                </el-card>
                            </el-col>
                        </el-row>
                    </el-form-item>
                </div>

                <!-- 自动删除设置 -->
                <div class="tab-content" :class="{ active: activeTab === 'auto_delete' }">

                    <el-form-item label="自动删除开关：">
                        <el-switch v-model="form.auto_delete_enabled" :active-value="1" :inactive-value="0" />
                        <div class="el-form-item-tip">开启后，过期公告将在宽限期结束后自动删除内容</div>
                    </el-form-item>

                    <el-form-item label="宽限期天数：">
                        <el-input-number
                            v-model="form.grace_period_days"
                            :min="0"
                            :max="365"
                            :step="1"
                            :disabled="!form.auto_delete_enabled"
                            style="width: 150px;"
                        />
                        <div class="el-form-item-tip">公告到期后的宽限期，超过此期间未续费将自动删除内容（0-365天）</div>
                    </el-form-item>

                    <el-form-item label="到期通知：">
                        <el-switch v-model="form.notification_enabled" :active-value="1" :inactive-value="0" :disabled="!form.auto_delete_enabled" />
                        <div class="el-form-item-tip">开启后，在广告即将到期时发送通知给用户</div>
                    </el-form-item>

                    <el-form-item label="通知方式：" v-if="form.notification_enabled">
                        <el-radio-group v-model="form.notification_type" :disabled="!form.auto_delete_enabled || !form.notification_enabled">
                            <el-radio label="email">邮件通知</el-radio>
                            <el-radio label="sms">短信通知</el-radio>
                        </el-radio-group>
                        <div class="el-form-item-tip">选择通知方式：邮件或短信（两者只能选一个）</div>
                    </el-form-item>

                    <el-form-item label="提前通知天数：" v-if="form.notification_enabled">
                        <el-input-number
                            v-model="form.notification_days"
                            :min="1"
                            :max="30"
                            :step="1"
                            :disabled="!form.auto_delete_enabled || !form.notification_enabled"
                            style="width: 150px;"
                        />
                        <div class="el-form-item-tip">在广告到期前几天发送通知（1-30天）</div>
                    </el-form-item>

                    <el-form-item label="短信场景编码：" v-if="form.notification_enabled && form.notification_type === 'sms'">
                        <el-input
                            v-model="form.sms_event_code"
                            placeholder="请输入短信场景编码"
                            :disabled="!form.auto_delete_enabled || !form.notification_enabled"
                            style="width: 200px;"
                        />
                        <div class="el-form-item-tip">短信模板的场景编码，需要在后台短信配置中设置对应模板</div>
                    </el-form-item>

                    <el-form-item label="手动清理：">
                        <div style="display: flex; gap: 10px; align-items: center;">
                            <el-button
                                type="warning"
                                :loading="cleanupLoading"
                                @click="manualCleanup(false)"
                                :disabled="!form.auto_delete_enabled"
                            >
                                立即清理过期公告
                            </el-button>
                            <el-button
                                type="danger"
                                :loading="cleanupLoading"
                                @click="manualCleanup(true)"
                                :disabled="!form.auto_delete_enabled"
                            >
                                强制清理所有公告
                            </el-button>
                        </div>
                        <div class="el-form-item-tip">
                            立即清理：只清理超过宽限期的公告<br>
                            强制清理：清理所有有到期时间的公告（用于测试）
                        </div>
                    </el-form-item>

                    <el-form-item label="上次清理时间：" v-if="form.last_cleanup_time > 0">
                        <span style="color: #909399;">{{ formatTime(form.last_cleanup_time) }}</span>
                    </el-form-item>
                </div>

                <!-- 展示设置 -->
                <div class="tab-content" :class="{ active: activeTab === 'display' }">
                    <div class="form-section-title">展示设置</div>
                    
                    <el-form-item label="展示模式" prop="display_mode">
                        <el-radio-group v-model="form.display_mode" @change="changeDisplayMode">
                            <el-radio :label="0">文字模式</el-radio>
                            <el-radio :label="1">图片模式</el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <el-form-item label="未租用提示：">
                        <el-input
                            v-model="form.empty_ad_text"
                            placeholder="请输入未租用广告位的显示文本"
                            :maxlength="50"
                            show-word-limit
                        >
                            <template #append>
                                <el-tooltip content="可使用 {position} 作为广告位序号占位符" placement="top">
                                    <span style="margin: 0 5px;">?</span>
                                </el-tooltip>
                            </template>
                        </el-input>
                        <div class="el-form-item-tip">
                            设置未租用广告位的显示文本，使用 {position} 可显示广告位序号。
                        </div>
                    </el-form-item>

                    <el-form-item label="未租用图片：" v-if="form.display_mode === 1">
                        <div class="image-preview-container">
                            <el-upload
                                class="empty-ad-upload"
                                action="/adminApi/Upload/file"
                                :on-success="handleEmptyAdImageSuccess"
                                :on-error="handleUploadError"
                                :before-upload="beforeUpload"
                                accept="image/*"
                                :show-file-list="false"
                            >
                                <template v-if="form.empty_ad_image">
                                    <img 
                                        :src="form.empty_ad_image" 
                                        class="uploaded-image"
                                        :style="{ maxHeight: form.height + 'px' }"
                                    >
                                    <div class="image-actions">
                                        <el-button type="primary" size="small" icon="el-icon-edit" circle title="更换图片"></el-button>
                                    </div>
                                </template>
                                <template v-else>
                                    <div style="font-size: 28px; color: #8c939d; margin-bottom: 8px;">+</div>
                                    <div class="el-upload__text">点击上传未租用图片</div>
                                    <div style="font-size: 12px;color: #999; margin-top: 8px;">推荐尺寸: 宽度不限 x {{form.height}}px</div>
                                </template>
                            </el-upload>
                            <div class="image-controls" v-if="form.empty_ad_image">
                                <el-button type="danger" size="small" @click="form.empty_ad_image = ''">删除图片</el-button>
                            </div>
                        </div>
                        <div class="el-form-item-tip">当广告位设为未租用状态时显示此图片</div>
                    </el-form-item>

                    <el-form-item label="图片切换：" v-if="form.display_mode === 1">
                        <el-row :gutter="20">
                            <el-col :span="24" style="margin-bottom: 15px;">
                                <el-radio-group v-model="form.auto_scroll">
                                    <el-radio :label="1">自动轮播</el-radio>
                                    <el-radio :label="0">手动点击</el-radio>
                                </el-radio-group>
                            </el-col>
                            
                            <!-- 轮播间隔设置 -->
                            <el-col :span="24">
                                <el-form-item label="轮播间隔时间" :class="{'disabled-form-item': !form.auto_scroll}">
                                    <el-select 
                                        v-model="form.scroll_interval" 
                                        :disabled="!form.auto_scroll"
                                        placeholder="请选择轮播间隔时间"
                                        style="width: 180px;"
                                    >
                                        <el-option :value="1000" label="1秒"></el-option>
                                        <el-option :value="2000" label="2秒"></el-option>
                                        <el-option :value="3000" label="3秒"></el-option>
                                        <el-option :value="5000" label="5秒"></el-option>
                                        <el-option :value="8000" label="8秒"></el-option>
                                        <el-option :value="10000" label="10秒"></el-option>
                                        <el-option :value="15000" label="15秒"></el-option>
                                        <el-option :value="20000" label="20秒"></el-option>
                                        <el-option :value="30000" label="30秒"></el-option>
                                    </el-select>
                                    <div class="el-form-item-tip" v-if="form.auto_scroll">选择图片自动轮播的间隔时间</div>
                                </el-form-item>
                            </el-col>

                            <el-col :span="24" style="margin-top: 15px;">
                                <el-form-item label="跳转链接">
                                    <el-input 
                                        v-model="form.rent_link" 
                                        placeholder="未购买广告位点击跳转链接（若为空则弹出购买对话框）"
                                    ></el-input>
                                    <div class="el-form-item-tip">当点击未购买的广告位时，跳转到此链接。留空则显示购买对话框</div>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form-item>

                    <el-form-item label="商户权限">
                        <el-switch
                            v-model="form.allow_merchant_edit" 
                            :active-value="1" 
                            :inactive-value="0" 
                            active-text="允许商户自定义广告内容" 
                            inactive-text="禁止商户自定义广告内容"
                        ></el-switch>
                        <div class="el-form-item-tip">开启后，商户可以自行编辑已购买广告位的内容和链接</div>
                    </el-form-item>
                </div>

                <!-- 广告内容 -->
                <div class="tab-content" :class="{ active: activeTab === 'content' }">
                    <div class="form-section-title">广告内容设置</div>
                    
                    <el-form-item label="公告内容：">
                        <div v-for="(item, index) in announcementItems" :key="index" class="announcement-item">
                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <div class="announcement-header">
                                        <span class="announcement-title">第 {{ index + 1 }} 号广告位</span>
                                        <el-tag 
                                            v-if="item.content === getEmptyAdText(index + 1)" 
                                            type="info" 
                                            size="small"
                                        >
                                            未租用
                                        </el-tag>
                                        <el-tag 
                                            v-else-if="item.user_id" 
                                            type="success" 
                                            size="small"
                                        >
                                            已租用：{{ item.username || '未知用户' }}
                                        </el-tag>
                                    </div>
                                </el-col>
                                <el-col :span="24" style="margin-top: 10px;">
                                    <template v-if="form.display_mode === 0">
                                        <el-input
                                            v-model="item.content"
                                            type="textarea"
                                            :rows="2"
                                            :placeholder="'请输入第' + (index + 1) + '条公告内容'"
                                            :maxlength="500"
                                            show-word-limit
                                        ></el-input>
                                        <el-input
                                            v-model="item.link_url"
                                            placeholder="请输入点击跳转链接（选填，完整URL，如http://example.com）"
                                            style="margin-top: 10px;"
                                        ></el-input>
                                    </template>
                                    <template v-else>
                                        <div class="image-preview-container">
                                            <el-upload
                                                class="announcement-upload"
                                                action="/adminApi/Upload/file"
                                                :on-success="(res) => handleUploadSuccess(res, index)"
                                                :on-error="handleUploadError"
                                                :before-upload="beforeUpload"
                                                accept="image/*"
                                                :show-file-list="false"
                                            >
                                                <template v-if="item.content && item.content.startsWith('http')">
                                                    <img 
                                                        :src="item.content" 
                                                        class="uploaded-image"
                                                        :style="{ maxHeight: form.height + 'px' }"
                                                    >
                                                    <div class="image-actions">
                                                        <el-button type="primary" size="small" icon="el-icon-edit" circle title="更换图片"></el-button>
                                                    </div>
                                                </template>
                                                                                <template v-else>
                                    <div style="font-size: 28px; color: #8c939d; margin-bottom: 8px;">+</div>
                                    <div class="el-upload__text">点击上传广告图片</div>
                                    <div style="font-size: 12px;color: #999; margin-top: 8px;">推荐尺寸: 宽度不限 x {{form.height}}px</div>
                                </template>
                                            </el-upload>
                                            <div class="image-controls" v-if="item.content && item.content.startsWith('http')">
                                                <el-button type="primary" size="small" @click="previewImage(item.content)">预览大图</el-button>
                                                <el-button type="danger" size="small" @click="item.content = ''">删除图片</el-button>
                                            </div>
                                        </div>
                                        <el-input
                                            v-model="item.link_url"
                                            placeholder="请输入图片点击跳转链接（选填）"
                                            style="margin-top: 10px;"
                                        >
                                            <template #prepend>
                                                <span style="white-space: nowrap;">链接:</span>
                                            </template>
                                        </el-input>
                                    </template>
                                </el-col>
                                <el-col :span="24" style="margin-top: 10px;">
                                    <el-date-picker
                                        v-model="item.end_time"
                                        type="datetime"
                                        :placeholder="'选择第' + (index + 1) + '条公告的结束时间'"
                                        format="YYYY-MM-DD HH:mm:ss"
                                        value-format="YYYY-MM-DD HH:mm:ss"
                                        :shortcuts="shortcuts"
                                        :clearable="true"
                                        :disabled-date="disabledDate"
                                        style="width: 100%;"
                                    />
                                </el-col>
                                <el-col :span="24" style="margin-top: 15px;">
                                    <el-form-item label="颜色设置：">
                                        <div class="color-settings">
                                            <div class="color-item">
                                                <span class="color-label">文字颜色：</span>
                                                <el-color-picker v-model="item.textColor" :disabled="item.useTextGradient" />
                                            </div>
                                            <div class="color-item">
                                                <span class="color-label">背景颜色：</span>
                                                <el-color-picker v-model="item.backgroundColor" :disabled="item.useGradient" />
                                            </div>
                                            <div class="gradient-option">
                                                <el-checkbox v-model="item.useGradient">使用渐变背景</el-checkbox>
                                            </div>
                                            <div class="gradient-option">
                                                <el-checkbox v-model="item.useTextGradient">使用渐变文字</el-checkbox>
                                            </div>
                                        </div>
                                        
                                        <div v-if="item.useGradient">
                                            <div style="margin-top: 10px; font-size: 13px; color: #606266; margin-bottom: 5px;">背景渐变设置:</div>
                                            <el-input
                                                v-model="item.gradientColor"
                                                placeholder="输入CSS渐变色，如：linear-gradient(135deg, #ff7b25, #ff5e62)"
                                                style="margin-top: 5px;"
                                            ></el-input>
                                            <div class="color-preview" :style="{
                                                background: item.gradientColor || 'linear-gradient(135deg, #ff7b25, #ff5e62)',
                                                height: '30px',
                                                borderRadius: '4px',
                                                marginTop: '10px'
                                            }"></div>

                                            <div class="gradient-presets" style="margin-top: 10px;">
                                                <div class="preset-title" style="margin-bottom: 5px; font-size: 13px; color: #606266;">背景渐变预设:</div>
                                                <div class="preset-list" style="display: flex; flex-wrap: wrap; gap: 8px;">
                                                    <div
                                                        v-for="(preset, i) in gradientPresets"
                                                        :key="i"
                                                        class="preset-item"
                                                        :style="{
                                                            background: preset.value,
                                                            width: '30px',
                                                            height: '30px',
                                                            borderRadius: '4px',
                                                            cursor: 'pointer',
                                                            border: item.gradientColor === preset.value ? '2px solid #409EFF' : '1px solid #DCDFE6'
                                                        }"
                                                        @click="item.gradientColor = preset.value"
                                                        :title="preset.name"
                                                    ></div>
                                                </div>
                                            </div>
                                        </div>

                                        <div v-if="item.useTextGradient">
                                            <div style="margin-top: 15px; font-size: 13px; color: #606266; margin-bottom: 5px;">文字渐变设置:</div>
                                            <el-input
                                                v-model="item.textGradientColor"
                                                placeholder="输入CSS渐变色，如：linear-gradient(to right, #f56c6c, #e6a23c)"
                                                style="margin-top: 5px;"
                                            ></el-input>
                                            <div class="text-gradient-preview" :style="{
                                                background: item.textGradientColor || 'linear-gradient(to right, #f56c6c, #e6a23c)',
                                                WebkitBackgroundClip: 'text',
                                                WebkitTextFillColor: 'transparent',
                                                backgroundClip: 'text',
                                                color: 'transparent',
                                                fontSize: '16px',
                                                fontWeight: 'bold',
                                                textAlign: 'center',
                                                padding: '10px',
                                                marginTop: '10px',
                                                border: '1px solid #DCDFE6',
                                                borderRadius: '4px'
                                            }">
                                                文字渐变预览效果
                                            </div>

                                            <div class="text-gradient-presets" style="margin-top: 10px;">
                                                <div class="preset-title" style="margin-bottom: 5px; font-size: 13px; color: #606266;">文字渐变预设:</div>
                                                <div class="preset-list" style="display: flex; flex-wrap: wrap; gap: 8px;">
                                                    <div
                                                        v-for="(preset, i) in textGradientPresets"
                                                        :key="i"
                                                        class="preset-item"
                                                        :style="{
                                                            background: preset.value,
                                                            WebkitBackgroundClip: 'text',
                                                            WebkitTextFillColor: 'transparent',
                                                            backgroundClip: 'text',
                                                            color: 'transparent',
                                                            width: '60px',
                                                            height: '30px',
                                                            borderRadius: '4px',
                                                            cursor: 'pointer',
                                                            border: item.textGradientColor === preset.value ? '2px solid #409EFF' : '1px solid #DCDFE6',
                                                            fontSize: '12px',
                                                            fontWeight: 'bold',
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            justifyContent: 'center'
                                                        }"
                                                        @click="item.textGradientColor = preset.value"
                                                        :title="preset.name"
                                                    >
                                                        文字
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <div class="announcement-actions">
                                <el-row :gutter="20">
                                    <el-col :span="24">
                                        <el-button 
                                            v-if="announcementItems.length > 1" 
                                            type="danger" 
                                            size="small"
                                            @click="removeAnnouncementItem(index)"
                                        >
                                            删除此条公告
                                        </el-button>
                                        <el-button 
                                            type="primary" 
                                            size="small"
                                            @click="setEmptyAd(index)"
                                        >
                                            设为未租用状态
                                        </el-button>
                                    </el-col>
                                </el-row>
                            </div>
                        </div>
                        <el-button type="primary" plain @click="addAnnouncementItem" style="margin-top: 15px;">
                            添加公告
                        </el-button>
                        <div class="el-form-item-tip" style="margin-top: 8px;">
                            提示：每条公告可以单独设置结束时间，到期后该条公告将自动隐藏。不设置则一直显示。
                        </div>
                    </el-form-item>
                </div>

                

                <el-form-item>
                    <el-button type="primary" :loading="loading" @click="save">保存设置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
    </div>

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>

    <script>
        const { createApp, ref, reactive, computed } = Vue;
        const { ElMessage } = ElementPlus;

        // 将setup函数提取为独立的组合式函数
        const useAnnouncement = () => {
            const loading = ref(false);
            const cleanupLoading = ref(false);
            const activeTab = ref('basic'); // Initialize activeTab
            const announcementItems = ref([
                {
                    content: '',
                    end_time: ''
                }
            ]);

            // 添加背景渐变色预设
            const gradientPresets = [
                { name: '橙红渐变', value: 'linear-gradient(135deg, #ff7b25, #ff5e62)' },
                { name: '蓝紫渐变', value: 'linear-gradient(135deg, #667eea, #764ba2)' },
                { name: '绿松石', value: 'linear-gradient(135deg, #00cdac, #02aab0)' },
                { name: '紫红渐变', value: 'linear-gradient(135deg, #bc4e9c, #f80759)' },
                { name: '阳光黄', value: 'linear-gradient(135deg, #fcb045, #fd1d1d)' },
                { name: '天空蓝', value: 'linear-gradient(135deg, #56ccf2, #2f80ed)' },
                { name: '薄荷绿', value: 'linear-gradient(135deg, #11998e, #38ef7d)' },
                { name: '深海蓝', value: 'linear-gradient(135deg, #4b6cb7, #182848)' },
                { name: '金色渐变', value: 'linear-gradient(135deg, #FFD700, #FFA500)' }
            ];

            // 添加文字渐变色预设
            const textGradientPresets = [
                { name: '红橙渐变', value: 'linear-gradient(to right, #f56c6c, #e6a23c)' },
                { name: '蓝绿渐变', value: 'linear-gradient(to right, #409eff, #67c23a)' },
                { name: '紫粉渐变', value: 'linear-gradient(to right, #909399, #606266)' },
                { name: '彩虹渐变', value: 'linear-gradient(to right, #ff0000, #ff7f00, #ffff00, #00ff00, #0000ff, #4b0082, #9400d3)' },
                { name: '金银渐变', value: 'linear-gradient(to right, #FFD700, #C0C0C0)' },
                { name: '火焰渐变', value: 'linear-gradient(to right, #ff4757, #ff6348)' },
                { name: '海洋渐变', value: 'linear-gradient(to right, #3742fa, #2f3542)' },
                { name: '森林渐变', value: 'linear-gradient(to right, #2ed573, #1e3799)' },
                { name: '日落渐变', value: 'linear-gradient(to right, #ffa726, #ff7043)' }
            ];

            const form = reactive({
                status: 1,
                background: '#ffffff',
                text_color: '#333333',
                height: 60,
                padding: 15,
                wrap: 0,
                use_gradient: false,
                month_price: 19.9,
                quarter_price: 49.9,
                halfyear_price: 89.9,
                year_price: 169.9,
                renew_month_price: 17.9,
                renew_quarter_price: 44.9,
                renew_halfyear_price: 79.9,
                renew_year_price: 149.9,
                empty_ad_text: '广告位{position}还没出租，点击立即租用', // 默认未租用文本
                display_mode: 0,
                auto_scroll: 1,
                scroll_interval: 5000, // 默认5秒
                empty_ad_image: '', // 添加未租用图片字段
                rent_link: '', // 添加跳转链接字段
                allow_merchant_edit: 1, // 添加商户权限字段
                mobile_enabled: 0,
                mobile_current_template: 1,
                mobile_template1_enabled: 0,
                mobile_template1_style: 'banner',
                mobile_template2_enabled: 0,
                mobile_template2_style: 'marquee',
                mobile_template3_enabled: 0,
                mobile_template3_style: 'color_cards',
                gradient_cards: 1,
                mobile_display_mode: 0,
                mobile_height: 30,
                mobile_padding: 0,
                mobile_background: '#ffffff',
                mobile_text_color: '#333333',
                // 自动删除配置
                auto_delete_enabled: 0,
                grace_period_days: 3,
                last_cleanup_time: 0,
                // 通知配置
                notification_enabled: 0,
                notification_type: 'email',
                notification_days: 1,
                sms_event_code: 'ad_expire',
            });

            

            const addAnnouncementItem = () => {
                if (announcementItems.value.length >= 20) {
                    ElMessage.warning('最多只能添加20个广告位');
                    return;
                }
                
                const newItem = {
                    content: form.display_mode === 1 ? '' : getEmptyAdText(announcementItems.value.length + 1),
                    end_time: '',
                    image_url: '',
                    link_url: form.rent_link || '', // 使用设置的租用链接，如果没有则使用空字符串
                    textColor: form.text_color,
                    backgroundColor: form.background,
                    useGradient: false,
                    gradientColor: 'linear-gradient(135deg, #ff7b25, #ff5e62)',
                    useTextGradient: false,
                    textGradientColor: 'linear-gradient(to right, #f56c6c, #e6a23c)'
                };
                
                announcementItems.value.push(newItem);
            };

            const removeAnnouncementItem = (index) => {
                if (announcementItems.value.length <= 1) {
                    ElMessage.warning('至少需要保留一个广告位');
                    return;
                }
                announcementItems.value.splice(index, 1);
                announcementItems.value.forEach((item, idx) => {
                    if (!item.content || item.content === getEmptyAdText(idx + 2)) {
                        item.content = getEmptyAdText(idx + 1);
                    }
                });
            };

            const getEmptyAdText = (position) => {
                return form.empty_ad_text.replace('{position}', position);
            };

            const setEmptyAd = (index) => {
                announcementItems.value[index] = {
                    content: form.display_mode === 1 ? form.empty_ad_image : getEmptyAdText(index + 1),
                    end_time: '',
                    image_url: form.display_mode === 1 ? form.empty_ad_image : '',
                    link_url: form.rent_link || '', // 使用设置的租用链接，如果没有则使用空字符串
                    textColor: form.text_color,
                    backgroundColor: form.background,
                    useGradient: false,
                    gradientColor: 'linear-gradient(135deg, #ff7b25, #ff5e62)',
                    useTextGradient: false,
                    textGradientColor: 'linear-gradient(to right, #f56c6c, #e6a23c)'
                };
            };

            // 添加图片上传相关的处理函数
            const handleUploadSuccess = (response, index) => {
                if (response.code === 200) {
                    if (form.display_mode === 1) {
                        // 图片模式
                        announcementItems.value[index].content = response.data.url;
                        announcementItems.value[index].image_url = response.data.url;
                    } else {
                        // 文字模式
                        announcementItems.value[index].content = response.data.url;
                    }
                    ElMessage.success('图片上传成功');
                } else {
                    ElMessage.error(response.msg || '图片上传失败');
                }
            };

            // 添加图片预览功能
            const previewImage = (imageUrl) => {
                if (!imageUrl) return;
                
                // 使用ElementPlus的对话框来显示大图
                ElementPlus.ElMessageBox.alert(
                    `<div style="text-align: center;"><img src="${imageUrl}" style="max-width: 100%; max-height: 70vh;"></div>`, 
                    '图片预览', 
                    {
                        dangerouslyUseHTMLString: true,
                        showCancelButton: false,
                        confirmButtonText: '关闭',
                        customClass: {
                            content: 'image-preview-dialog-content'
                        }
                    }
                );
            };

            const handleUploadError = (error) => {
                ElMessage.error('图片上传失败');
            };

            const beforeUpload = (file) => {
                // 验证文件类型
                const isImage = file.type.startsWith('image/');
                if (!isImage) {
                    ElMessage.error('只能上传图片文件！');
                    return false;
                }

                // 验证文件大小（限制为2MB）
                const isLt2M = file.size / 1024 / 1024 < 2;
                if (!isLt2M) {
                    ElMessage.error('图片大小不能超过2MB！');
                    return false;
                }

                return true;
            };

            // 添加未租用图片上传成功处理函数
            const handleEmptyAdImageSuccess = (response) => {
                if (response.code === 200) {
                    form.empty_ad_image = response.data.url;
                    ElMessage.success('未租用图片上传成功');
                } else {
                    ElMessage.error(response.msg || '图片上传失败');
                }
            };

            const fetchData = async () => {
                loading.value = true;
                try {
                    const { data: response } = await axios.post("/plugin/Announcementshows/Api/fetchData");
                    if (response?.code === 200) {
                        const { data } = response;
                        Object.assign(form, {
                            status: parseInt(data.status),
                            background: data.background,
                            text_color: data.text_color,
                            height: parseInt(data.height),
                            padding: parseInt(data.padding),
                            wrap: parseInt(data.wrap),
                            use_gradient: data.use_gradient === '1',
                            month_price: parseFloat(data.month_price),
                            quarter_price: parseFloat(data.quarter_price),
                            halfyear_price: parseFloat(data.halfyear_price),
                            year_price: parseFloat(data.year_price),
                            renew_month_price: parseFloat(data.renew_month_price),
                            renew_quarter_price: parseFloat(data.renew_quarter_price),
                            renew_halfyear_price: parseFloat(data.renew_halfyear_price),
                            renew_year_price: parseFloat(data.renew_year_price),
                            empty_ad_text: data.empty_ad_text || '广告位{position}还没出租，点击立即租用',
                            display_mode: parseInt(data.display_mode),
                            auto_scroll: parseInt(data.auto_scroll),
                            scroll_interval: parseInt(data.scroll_interval),
                            empty_ad_image: data.empty_ad_image || '',
                            rent_link: data.rent_link || '',
                            allow_merchant_edit: parseInt(data.allow_merchant_edit),
                            mobile_enabled: parseInt(data.mobile_enabled),
                            mobile_current_template: parseInt(data.mobile_current_template),
                            mobile_template1_enabled: parseInt(data.mobile_template1_enabled),
                            mobile_template1_style: data.mobile_template1_style || 'banner',
                            mobile_template2_enabled: parseInt(data.mobile_template2_enabled),
                            mobile_template2_style: data.mobile_template2_style || 'marquee',
                            mobile_template3_enabled: parseInt(data.mobile_template3_enabled),
                            mobile_template3_style: data.mobile_template3_style || 'color_cards',
                            gradient_cards: parseInt(data.gradient_cards || 1),
                            mobile_display_mode: parseInt(data.mobile_display_mode),
                            mobile_height: parseInt(data.mobile_height),
                            mobile_padding: parseInt(data.mobile_padding),
                            mobile_background: data.mobile_background || '#ffffff',
                            mobile_text_color: data.mobile_text_color || '#333333',
                            // 自动删除配置
                            auto_delete_enabled: parseInt(data.auto_delete_enabled || 0),
                            grace_period_days: parseInt(data.grace_period_days || 3),
                            last_cleanup_time: parseInt(data.last_cleanup_time || 0),
                            // 通知配置
                            notification_enabled: parseInt(data.notification_enabled || 0),
                            notification_type: data.notification_type || 'email',
                            notification_days: parseInt(data.notification_days || 1),
                            sms_event_code: data.sms_event_code || 'ad_expire',
                        });
                        
                        if (data.announcements) {
                            const announcements = JSON.parse(data.announcements);
                            if (announcements.length > 0) {
                                announcementItems.value = announcements.map(item => ({
                                    ...item,
                                    image_url: form.display_mode === 1 ? item.content : '', // 图片模式下，content字段存储图片URL
                                    link_url: item.link_url || '',
                                    textColor: item.textColor || data.text_color,
                                    backgroundColor: item.backgroundColor || data.background,
                                    useGradient: item.useGradient || false,
                                    gradientColor: item.gradientColor || 'linear-gradient(135deg, #ff7b25, #ff5e62)',
                                    useTextGradient: item.useTextGradient || false,
                                    textGradientColor: item.textGradientColor || 'linear-gradient(to right, #f56c6c, #e6a23c)'
                                }));
                            } else {
                                announcementItems.value = [{
                                    content: getEmptyAdText(1),
                                    end_time: '',
                                    image_url: '',
                                    link_url: data.rent_link || '',
                                    textColor: data.text_color,
                                    backgroundColor: data.background
                                }];
                            }
                        }
                    }
                } catch (error) {
                    ElMessage.error('获取数据失败');
                } finally {
                    loading.value = false;
                }
            };

            const save = async () => {
                loading.value = true;
                try {
                    const formData = new FormData();
                    
                    // 确保价格字段为数字
                    const priceFields = [
                        'month_price', 'quarter_price', 'halfyear_price', 'year_price',
                        'renew_month_price', 'renew_quarter_price', 'renew_halfyear_price', 'renew_year_price'
                    ];
                    
                    // 确保数字字段为整数
                    const intFields = [
                        'status', 'display_mode', 'height', 'padding', 'wrap', 'auto_scroll', 'scroll_interval',
                        'allow_merchant_edit', 'mobile_enabled', 'mobile_current_template', 'mobile_template1_enabled',
                        'mobile_template2_enabled', 'mobile_template3_enabled', 'mobile_display_mode', 'mobile_height', 'mobile_padding',
                        'gradient_cards'
                    ];
                    
                    Object.entries(form).forEach(([key, value]) => {
                        if (priceFields.includes(key)) {
                            formData.append(key, parseFloat(value));
                        } else if (intFields.includes(key)) {
                            formData.append(key, parseInt(value));
                        } else {
                            formData.append(key, value);
                        }
                    });
                    
                    const announcements = announcementItems.value
                        .filter(item => {
                            if (form.display_mode === 1) {
                                // 图片模式下检查content（已上传的图片URL）
                                return item.content && typeof item.content === 'string' && 
                                       item.content.trim() && item.content.startsWith('http');
                            }
                            // 文字模式下检查content
                            return item.content && item.content.trim();
                        })
                        .map(item => {
                            const announcement = {
                                content: item.content.trim(),
                                end_time: item.end_time,
                                textColor: item.textColor || form.text_color,
                                backgroundColor: item.useGradient && item.gradientColor ? 
                                                 item.gradientColor : 
                                                 (item.backgroundColor || form.background),
                                useGradient: item.useGradient || false,
                                gradientColor: item.gradientColor || '',
                                user_id: item.user_id || null, // 确保保留用户ID
                                username: item.username || null, // 确保保留用户名
                                link_url: item.link_url || '',
                                nickname: item.nickname || '', // 添加nickname字段
                            };
                            
                            if (item.link_url) {
                                announcement.link_url = item.link_url.trim();
                            }
                            
                            // 添加其他用户相关字段
                            if (item.nickname) {
                                announcement.nickname = item.nickname;
                            }
                            
                            return announcement;
                        });

                        // 再次验证过滤后的数组长度
                        if (announcements.length === 0) {
                            ElMessage.error(form.display_mode === 1 ? '至少需要一个有效的图片广告' : '至少需要一个有效的文字广告');
                            return;
                        }

                        formData.append('announcements', JSON.stringify(announcements));
                        
                        const { data: response } = await axios.post("/plugin/Announcementshows/Api/save", formData);
                        if (response?.code === 200) {
                            ElMessage.success('保存成功');
                            
                            // 确保更新生效，更新展示页面的广告数据
                            setTimeout(() => {
                                // 发送刷新信号，通知前端页面刷新广告数据
                                try {
                                    const message = {
                                        type: 'REFRESH_ANNOUNCEMENTS',
                                        timestamp: new Date().getTime()
                                    };
                                    localStorage.setItem('announcement_refresh', JSON.stringify(message));
                                } catch (e) {
                                    // 静默处理
                                }
                            }, 1000);
                        } else {
                            ElMessage.error(response?.msg || '保存失败');
                        }
                } catch (error) {
                    ElMessage.error('保存失败');
                } finally {
                    loading.value = false;
                }
            };

            // 格式化时间戳
            const formatTime = (timestamp) => {
                if (!timestamp) return '从未执行';
                const date = new Date(timestamp * 1000);
                return date.toLocaleString('zh-CN');
            };

            // 手动清理过期公告
            const manualCleanup = async (forceCleanup = false) => {
                cleanupLoading.value = true;
                try {
                    const formData = new FormData();
                    if (forceCleanup) {
                        formData.append('force_cleanup', '1');
                    }

                    const { data: response } = await axios.post("/plugin/Announcementshows/Api/cleanupExpiredAnnouncements", formData);
                    if (response?.code === 200) {
                        const { deleted_count, deleted_list, grace_period_days, debug_info, current_time } = response.data;

                        if (deleted_count > 0) {
                            let message = `成功清理 ${deleted_count} 个${forceCleanup ? '强制' : '过期'}公告`;
                            if (deleted_list && deleted_list.length > 0) {
                                message += '\n清理详情：';
                                deleted_list.forEach(item => {
                                    message += `\n位置${item.position}: ${item.username} (到期时间: ${item.end_time})`;
                                });
                            }
                            ElMessage.success(message);

                            // 刷新数据
                            await fetchData();
                        } else {
                            let message = `没有需要清理的${forceCleanup ? '' : '过期'}公告`;
                            if (!forceCleanup) {
                                message += `（宽限期：${grace_period_days}天）`;
                            }

                            // 显示调试信息
                            if (debug_info && debug_info.length > 0) {
                                message += '\n\n调试信息：';
                                message += `\n当前服务器时间: ${current_time}`;
                                debug_info.forEach(info => {
                                    message += `\n${info}`;
                                });
                            }

                            ElMessage.info(message);
                        }

                        // 更新最后清理时间
                        form.last_cleanup_time = Math.floor(Date.now() / 1000);
                    } else {
                        ElMessage.error(response?.msg || '清理失败');
                    }
                } catch (error) {
                    ElMessage.error('清理失败');
                } finally {
                    cleanupLoading.value = false;
                }
            };

            // 添加快捷选项
            const shortcuts = [
                {
                    text: '今天',
                    value: new Date(),
                },
                {
                    text: '明天',
                    value: () => {
                        const date = new Date();
                        date.setTime(date.getTime() + 3600 * 1000 * 24);
                        return date;
                    },
                },
                {
                    text: '一周后',
                    value: () => {
                        const date = new Date();
                        date.setTime(date.getTime() + 3600 * 1000 * 24 * 7);
                        return date;
                    },
                },
                {
                    text: '一个月后',
                    value: () => {
                        const date = new Date();
                        date.setMonth(date.getMonth() + 1);
                        return date;
                    },
                }
            ];

            // 禁用过去的日期
            const disabledDate = (time) => {
                return time.getTime() < Date.now() - 8.64e7; // 禁用今天之前的日期
            };

            

            // 切换显示模式
            const changeDisplayMode = (val) => {
                // 更新表单
                form.display_mode = val;
                
                // 如果是图片模式，检查并更新内容
                if (val === 1) {
                    announcementItems.value.forEach(item => {
                        if (!item.content || !item.content.match(/^(https?:\/\/|\/)[^\s]*$/i)) {
                            item.content = form.empty_ad_image || '';
                        }
                    });
                }
            };
                        

            // 初始化数据
            fetchData();

            return {
                loading,
                form,
                save,
                announcementItems,
                addAnnouncementItem,
                removeAnnouncementItem,
                gradientPresets,
                textGradientPresets,
                shortcuts,
                disabledDate,
                getEmptyAdText,
                setEmptyAd,
                handleUploadSuccess,
                handleUploadError,
                beforeUpload,
                handleEmptyAdImageSuccess,
                changeDisplayMode,
                previewImage,
                cleanupLoading,
                formatTime,
                manualCleanup,
                activeTab
            };
        };

        createApp({
            setup() {
                return useAnnouncement();
            }
        })
        .use(ElementPlus, {
            locale: ElementPlusLocaleZhCn
        })
        .mount('#app');
    </script>
</body>
</html> 