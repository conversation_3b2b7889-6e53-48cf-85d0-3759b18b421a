(function() {
    // 配置常量
    const CONFIG = {
        ANIMATION_DURATION: 300,
        DEBOUNCE_DELAY: 300,
        CARD_BACKGROUNDS: {
            0: '#FFF7E6', // NO.1
            1: '#F0F7FF', // NO.2
            2: '#F0FFF7'  // NO.3
        },
        CARD_BORDERS: {
            0: '#FFB020', // NO.1
            1: '#FF7452', // NO.2
            2: '#40B883'  // NO.3
        },
        DEFAULT_ICONS: {
            rank1: 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMjAgMTIwIiB3aWR0aD0iMjQiIGhlaWdodD0iMjQiPjxwYXRoIGQ9Ik02MCAxMEwxMDUgMzVWODVMNjAgMTEwTDE1IDg1VjM1TDYwIDEwWiIgZmlsbD0iI0ZGQjAyMCIgc3Ryb2tlPSIjRkY5NTAwIiBzdHJva2Utd2lkdGg9IjIiLz48Y2lyY2xlIGN4PSI2MCIgY3k9IjYwIiByPSIyMCIgZmlsbD0iI0ZGRDcwMCIgc3Ryb2tlPSIjRkZDNzAwIiBzdHJva2Utd2lkdGg9IjIiLz48cGF0aCBkPSJNNjAgNDVMNjQgNTVINzVMNjYgNjJMNzAgNzJMNjAgNjVMNTAgNzJMNTQgNjJMNDUgNTVINTZMNjAgNDVaIiBmaWxsPSIjRkZGRkZGIi8+PHBhdGggZD0iTTM1IDUwQzI1IDU1IDIwIDY1IDI1IDcwQzM1IDY1IDQwIDU1IDM1IDUwWiIgZmlsbD0iIzQxNjlFMSIgb3BhY2l0eT0iMC45Ii8+PHBhdGggZD0iTTg1IDUwQzk1IDU1IDEwMCA2NSA5NSA3MEM4NSA2NSA4MCA1NSA4NSA1MFoiIGZpbGw9IiM0MTY5RTEiIG9wYWNpdHk9IjAuOSIvPjx0ZXh0IHg9IjYwIiB5PSI4NSIgZm9udC1mYW1pbHk9IkFyaWFsIEJsYWNrLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjI0IiBmb250LXdlaWdodD0iYm9sZCIgZmlsbD0iI0ZGRkZGRiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+Tk8uMTwvdGV4dD48L3N2Zz4=',
            rank2: 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMjAgMTIwIiB3aWR0aD0iMjQiIGhlaWdodD0iMjQiPjxwYXRoIGQ9Ik02MCAxMEwxMDUgMzVWODVMNjAgMTEwTDE1IDg1VjM1TDYwIDEwWiIgZmlsbD0iI0ZGNzQ1MiIgc3Ryb2tlPSIjRkY1QzNEIiBzdHJva2Utd2lkdGg9IjIiLz48Y2lyY2xlIGN4PSI2MCIgY3k9IjYwIiByPSIyMCIgZmlsbD0iI0ZGRDcwMCIgc3Ryb2tlPSIjRkZDNzAwIiBzdHJva2Utd2lkdGg9IjIiLz48cGF0aCBkPSJNNjAgNDVMNjQgNTVINzVMNjYgNjJMNzAgNzJMNjAgNjVMNTAgNzJMNTQgNjJMNDUgNTVINTZMNjAgNDVaIiBmaWxsPSIjRkZGRkZGIi8+PHBhdGggZD0iTTM1IDUwQzI1IDU1IDIwIDY1IDI1IDcwQzM1IDY1IDQwIDU1IDM1IDUwWiIgZmlsbD0iIzQxNjlFMSIgb3BhY2l0eT0iMC45Ii8+PHBhdGggZD0iTTg1IDUwQzk1IDU1IDEwMCA2NSA5NSA3MEM4NSA2NSA4MCA1NSA4NSA1MFoiIGZpbGw9IiM0MTY5RTEiIG9wYWNpdHk9IjAuOSIvPjx0ZXh0IHg9IjYwIiB5PSI4NSIgZm9udC1mYW1pbHk9IkFyaWFsIEJsYWNrLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjI0IiBmb250LXdlaWdodD0iYm9sZCIgZmlsbD0iI0ZGRkZGRiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+Tk8uMjwvdGV4dD48L3N2Zz4=',
            rank3: 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMjAgMTIwIiB3aWR0aD0iMjQiIGhlaWdodD0iMjQiPjxwYXRoIGQ9Ik02MCAxMEwxMDUgMzVWODVMNjAgMTEwTDE1IDg1VjM1TDYwIDEwWiIgZmlsbD0iIzQwQjg4MyIgc3Ryb2tlPSIjMzY5QjZGIiBzdHJva2Utd2lkdGg9IjIiLz48Y2lyY2xlIGN4PSI2MCIgY3k9IjYwIiByPSIyMCIgZmlsbD0iI0ZGRDcwMCIgc3Ryb2tlPSIjRkZDNzAwIiBzdHJva2Utd2lkdGg9IjIiLz48cGF0aCBkPSJNNjAgNDVMNjQgNTVINzVMNjYgNjJMNzAgNzJMNjAgNjVMNTAgNzJMNTQgNjJMNDUgNTVINTZMNjAgNDVaIiBmaWxsPSIjRkZGRkZGIi8+PHBhdGggZD0iTTM1IDUwQzI1IDU1IDIwIDY1IDI1IDcwQzM1IDY1IDQwIDU1IDM1IDUwWiIgZmlsbD0iIzQxNjlFMSIgb3BhY2l0eT0iMC45Ii8+PHBhdGggZD0iTTg1IDUwQzk1IDU1IDEwMCA2NSA5NSA3MEM4NSA2NSA4MCA1NSA4NSA1MFoiIGZpbGw9IiM0MTY5RTEiIG9wYWNpdHk9IjAuOSIvPjx0ZXh0IHg9IjYwIiB5PSI4NSIgZm9udC1mYW1pbHk9IkFyaWFsIEJsYWNrLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjI0IiBmb250LXdlaWdodD0iYm9sZCIgZmlsbD0iI0ZGRkZGRiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+Tk8uMzwvdGV4dD48L3N2Zz4='
        },
        MOBILE_BREAKPOINT: 768,
        // 特效配置
        EFFECTS: {
            PARTICLES: true,    // 粒子特效
            GRADIENT: true,     // 渐变特效
            GLOW: true          // 光晕特效
        }
    };

    /**
     * 初始化货源排行显示
     */
    function initSourceDisplay() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setupObserver();
                createSourceDisplay();
            });
        } else {
            setupObserver();
            createSourceDisplay();
        }
    }

    /**
     * 设置DOM变化观察器以响应页面变化
     */
    function setupObserver() {
        // 创建一个观察器实例来监听路由变化
        const observer = new MutationObserver((mutations) => {
            // 使用防抖来避免频繁触发
            if (window.sourceDisplayTimer) {
                clearTimeout(window.sourceDisplayTimer);
            }
            
            window.sourceDisplayTimer = setTimeout(() => {
                const targetElement = findElement();
                const existingContainer = document.querySelector('.top-sources-container');
                
                if (targetElement && !existingContainer) {
                    createSourceDisplay();
                }
            }, CONFIG.DEBOUNCE_DELAY);
        });

        // 配置观察器选项
        const config = {
            childList: true,
            subtree: true,
            attributes: true
        };

        // 观察整个文档
        observer.observe(document.body, config);
    }

    /**
     * 创建并显示货源排行卡片
     */
    async function createSourceDisplay() {
        try {
            // 检查是否已存在显示容器
            const existingContainer = document.querySelector('.top-sources-container');
            if (existingContainer) {
                return;
            }

            // 检查当前是否在正确的标签页
            if (isMobile()) {
                const activeTab = document.querySelector('.arco-tabs-tab.arco-tabs-tab-active .arco-tabs-tab-title');
                if (!activeTab || activeTab.textContent.trim() !== '货源广场') {
                    // 如果不是货源广场标签，移除已存在的容器（如果有的话）
                    const container = document.querySelector('.top-sources-container');
                    if (container) {
                        container.remove();
                    }
                    return;
                }
            }

            // 确保目标元素存在
            const targetElement = findElement();
            if (!targetElement) {
                return;
            }

            // 获取数据
            const response = await fetch('/plugin/Sourcedisplay/api/fetchData', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            const result = await response.json();
            if (result.code !== 200 || !result.data) {
                return;
            }

            const { sources, enabled, title_wrap, rank_icons } = result.data;
            
            // 修改点击链接的处理
            sources.forEach(source => {
                if (source.url) {
                    // 将原有的路径替换为新的包含 agent_key 的路径
                    source.url = source.url.replace(
                        '/merchant/my_parent/source_square',
                        `/merchant/my_parent/parent_goods?agent_key=${source.agent_key || ''}`
                    );
                }
            });

            // 保存配置到全局变量，包括图标配置
            window.sourceDisplayConfig = { 
                title_wrap,
                rank_icons: rank_icons || {}
            };
            
            // 检查插件是否启用
            if (!enabled) {
                return;
            }

            // 创建显示容器
            const container = createContainer(sources);
            if (container) {
                // 根据设备类型选择不同的插入位置
                if (isMobile()) {
                    const targetElement = findElement();
                    if (targetElement) {
                        // 在移动端，将容器插入到导航栏后面
                        targetElement.parentNode.insertBefore(container, targetElement.nextSibling);
                        
                        // 添加渐入动画
                        setTimeout(() => {
                            container.style.opacity = '1';
                        }, 50);
                    }
                } else {
                    // 桌面端保持原有的插入逻辑
                    const targetElement = findElement();
                    if (targetElement) {
                        targetElement.parentNode.insertBefore(container, targetElement.nextSibling);
                        
                        // 添加渐入动画
                        setTimeout(() => {
                            container.style.opacity = '1';
                        }, 50);
                    }
                }
            }

        } catch (error) {
        }
    }

    /**
     * 寻找页面中的目标元素
     * @returns {Element|null} 找到的目标元素
     */
    function findElement() {
        // 首先尝试查找电脑端的货源广场元素
        const desktopElements = document.querySelectorAll('.card-title');
        for (const element of desktopElements) {
            if (element.textContent.trim().includes('货源广场') && 
                element.getAttribute('data-v-208f240f') !== null) {
                return element;
            }
        }

        // 修改手机端查找逻辑
        const tabElements = document.querySelectorAll('.arco-tabs-tab');
        for (const tab of tabElements) {
            const titleSpan = tab.querySelector('.arco-tabs-tab-title');
            if (titleSpan && titleSpan.textContent.trim() === '货源广场') {
                // 找到"货源广场"标签所在的导航栏
                const tabNav = tab.closest('.arco-tabs-nav');
                if (tabNav) {
                    return tabNav;
                }
            }
        }
        
        return null;
    }

    /**
     * 创建货源卡片容器
     * @param {Array} sources 货源数据数组
     * @returns {HTMLElement|null} 创建的容器元素
     */
    function createContainer(sources) {
        if (!Array.isArray(sources) || sources.length === 0) {
            return null;
        }

        const container = document.createElement('div');
        container.className = 'top-sources-container';

        // 移动端特殊样式
        if (isMobile()) {
            container.style.cssText = `
                display: flex;
                flex-direction: column;
                gap: 16px;
                margin: 12px;
                padding: 18px;
                background: #fff;
                border-radius: 16px;
                box-shadow: 0 6px 25px rgba(0,0,0,0.08);
                transition: all 0.3s ease;
                opacity: 0;
            `;
        } else {
            // 改进的桌面端样式 - 根据数据数量调整布局
            const justifyContent = sources.length === 1 ? 'center' : 'flex-start';
            container.style.cssText = `
                display: flex;
                gap: 18px;
                margin-top: 18px;
                padding: 20px;
                background: #fff;
                border-radius: 12px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.05);
                position: sticky;
                top: 0;
                z-index: 100;
                flex-wrap: wrap;
                transition: all 0.3s ease;
                opacity: 0;
                justify-content: ${justifyContent};
            `;
        }

        // 根据数据数量决定显示逻辑
        if (sources.length === 1) {
            // 只有一个数据时，直接显示在中间
            const sourceCard = createSourceCard(sources[0], 0, true); // 传递 isSingle 参数
            // 单个卡片居中显示时的特殊样式
            sourceCard.style.transform = 'scale(1.05)';
            sourceCard.style.zIndex = '1';
            sourceCard.style.boxShadow = '0 6px 25px rgba(0,0,0,0.08)';
            container.appendChild(sourceCard);
        } else {
            // 多个数据时使用原有的排序逻辑：NO.2 -> NO.1 -> NO.3
            const orderedSources = [
                sources[1], // NO.2 (左边)
                sources[0], // NO.1 (中间)
                sources[2]  // NO.3 (右边)
            ];

            orderedSources.forEach((source, index) => {
                if (!source) return; // 跳过空数据

                const sourceCard = createSourceCard(source, getCardIndex(index), false); // 传递 isSingle 参数
                // 中间卡片(NO.1)放大和提升
                if (index === 1) {
                    sourceCard.style.transform = 'scale(1.05)';
                    sourceCard.style.zIndex = '1';
                    sourceCard.style.boxShadow = '0 6px 25px rgba(0,0,0,0.08)';
                }
                container.appendChild(sourceCard);
            });
        }

        return container;
    }

    /**
     * 获取实际的排名索引
     * @param {number} displayIndex 显示顺序索引
     * @returns {number} 实际排名索引
     */
    function getCardIndex(displayIndex) {
        // 转换显示顺序到实际排名
        const indexMap = {
            0: 1,  // 左边显示为 NO.2
            1: 0,  // 中间显示为 NO.1
            2: 2   // 右边显示为 NO.3
        };
        return indexMap[displayIndex];
    }

    /**
     * 创建单个货源卡片
     * @param {Object} source 货源数据
     * @param {number} rankIndex 排名索引
     * @param {boolean} isSingle 是否为单个卡片显示
     * @returns {HTMLElement} 卡片元素
     */
    function createSourceCard(source, rankIndex, isSingle = false) {
        const sourceCard = document.createElement('div');
        sourceCard.className = 'source-card';
        
        // 移动端特殊样式
        if (isMobile()) {
            sourceCard.style.cssText = `
                padding: 18px;
                background: ${CONFIG.CARD_BACKGROUNDS[rankIndex]};
                border: 1px solid ${CONFIG.CARD_BORDERS[rankIndex]};
                border-radius: 12px;
                position: relative;
                display: flex;
                gap: 14px;
                min-height: 120px;
                box-sizing: border-box;
                width: 100%;
                cursor: pointer;
                transition: all 0.3s ease;
                box-shadow: 0 3px 15px rgba(0,0,0,0.08);
                overflow: hidden;
            `;
        } else {
            // 改进的桌面端样式 - 根据是否为单个卡片调整样式
            sourceCard.style.cssText = `
                flex: ${isSingle ? '0 0 auto' : '1'};
                padding: 20px;
                background: ${CONFIG.CARD_BACKGROUNDS[rankIndex]};
                border: 1px solid ${CONFIG.CARD_BORDERS[rankIndex]};
                border-radius: 12px;
                position: relative;
                transition: all 0.3s ease;
                display: flex;
                gap: 16px;
                min-height: 140px;
                box-sizing: border-box;
                width: ${isSingle ? 'min(450px, 90vw)' : 'calc(33.33% - 12px)'};
                max-width: ${isSingle ? '450px' : 'none'};
                min-width: ${isSingle ? '350px' : '320px'};
                height: auto;
                cursor: pointer;
                box-shadow: 0 3px 15px rgba(0,0,0,0.08);
                overflow: hidden;
            `;
        }

        // 添加鼠标悬停效果
        sourceCard.addEventListener('mouseenter', () => {
            sourceCard.style.transform = sourceCard.style.transform === 'scale(1.05)' 
                ? 'scale(1.08)' 
                : 'scale(1.03)';
            sourceCard.style.boxShadow = '0 6px 25px rgba(0,0,0,0.08)';
            
            // 启动粒子动画
            if (sourceCard.querySelector('.particles-container')) {
                const particles = sourceCard.querySelectorAll('.particle');
                particles.forEach(p => {
                    p.style.animationPlayState = 'running';
                });
            }
        });
        
        sourceCard.addEventListener('mouseleave', () => {
            sourceCard.style.transform = sourceCard.style.transform === 'scale(1.08)' 
                ? 'scale(1.05)' 
                : 'scale(1)';
            sourceCard.style.boxShadow = sourceCard.style.transform === 'scale(1.05)'
                ? '0 6px 25px rgba(0,0,0,0.08)'
                : '0 2px 10px rgba(0,0,0,0.03)';
            
            // 暂停粒子动画
            if (sourceCard.querySelector('.particles-container')) {
                const particles = sourceCard.querySelectorAll('.particle');
                particles.forEach(p => {
                    p.style.animationPlayState = 'paused';
                });
            }
        });

        // 添加点击效果
        sourceCard.addEventListener('mousedown', () => {
            sourceCard.style.transform = sourceCard.style.transform === 'scale(1.05)' 
                ? 'scale(1.03)' 
                : 'scale(0.98)';
        });

        sourceCard.addEventListener('mouseup', () => {
            sourceCard.style.transform = sourceCard.style.transform === 'scale(1.03)' 
                ? 'scale(1.05)' 
                : 'scale(1)';
        });

        // 添加点击事件
        sourceCard.addEventListener('click', () => {
            if (source.agent_key) {
                const url = `/merchant/my_parent/parent_goods?agent_key=${source.agent_key}`;
                window.location.href = url;
            }
        });

        // 创建背景特效
        if (CONFIG.EFFECTS.PARTICLES) {
            addParticlesEffect(sourceCard, rankIndex);
        }

        if (CONFIG.EFFECTS.GRADIENT) {
            addGradientEffect(sourceCard, rankIndex);
        }

        if (CONFIG.EFFECTS.GLOW) {
            addGlowEffect(sourceCard, rankIndex);
        }

        // 创建图标容器
        const iconContainer = document.createElement('div');
        iconContainer.style.cssText = `
            flex-shrink: 0;
            width: 72px;
            height: 72px;
            display: flex;
            align-items: flex-start;
            justify-content: center;
            position: relative;
            z-index: 2;
            padding-top: 4px;
        `;

        // 修改图标获取逻辑
        const icon = document.createElement('img');
        const rankKey = `rank${rankIndex + 1}`;
        const configuredIcon = window.sourceDisplayConfig?.rank_icons?.[rankKey];
        
        // 只有当配置的图标存在且不为空字符串时才使用它
        icon.src = (configuredIcon && configuredIcon.trim())
            ? configuredIcon
            : CONFIG.DEFAULT_ICONS[rankKey];

        icon.style.cssText = `
            width: 100%;
            height: 100%;
            object-fit: contain;
            transition: all 0.3s ease;
            filter: drop-shadow(0 3px 8px rgba(0,0,0,0.15));
            border-radius: 8px;
        `;
        
        // 图标悬停效果
        iconContainer.addEventListener('mouseenter', () => {
            icon.style.transform = 'scale(1.1) rotate(3deg)';
            icon.style.filter = 'drop-shadow(0 4px 12px rgba(0,0,0,0.2))';
        });

        iconContainer.addEventListener('mouseleave', () => {
            icon.style.transform = 'scale(1) rotate(0)';
            icon.style.filter = 'drop-shadow(0 3px 8px rgba(0,0,0,0.15))';
        });
        
        iconContainer.appendChild(icon);

        // 创建内容容器
        const contentWrapper = document.createElement('div');
        contentWrapper.style.cssText = `
            flex: 1;
            min-width: 0;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            overflow: hidden;
            padding-right: 10px;
            position: relative;
            z-index: 2;
            height: 100%;
        `;

        // 添加内容
        const content = createCardContent(source);
        contentWrapper.appendChild(content);

        // 将图标和内容添加到卡片
        sourceCard.appendChild(iconContainer);
        sourceCard.appendChild(contentWrapper);

        return sourceCard;
    }

    /**
     * 添加粒子效果
     * @param {HTMLElement} card 卡片元素
     * @param {number} rankIndex 排名索引
     */
    function addParticlesEffect(card, rankIndex) {
        const particlesContainer = document.createElement('div');
        particlesContainer.className = 'particles-container';
        particlesContainer.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
            z-index: 1;
        `;

        // 基于排名获取粒子颜色
        const colors = getParticleColors(rankIndex);
        
        // 创建粒子
        const particleCount = isMobile() ? 8 : 15;
        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            
            const size = Math.random() * 5 + 2;
            const color = colors[Math.floor(Math.random() * colors.length)];
            const left = Math.random() * 100;
            const top = Math.random() * 100;
            const duration = Math.random() * 15 + 10;
            const delay = Math.random() * 5;

            particle.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                background-color: ${color};
                border-radius: 50%;
                left: ${left}%;
                top: ${top}%;
                opacity: 0.6;
                filter: blur(1px);
                animation: float-particle ${duration}s ease-in-out ${delay}s infinite alternate;
                animation-play-state: paused;
            `;
            
            particlesContainer.appendChild(particle);
        }
        
        // 添加关键帧动画到文档
        if (!document.getElementById('particle-animations')) {
            const style = document.createElement('style');
            style.id = 'particle-animations';
            style.textContent = `
                @keyframes float-particle {
                    0% {
                        transform: translate(0, 0) scale(1);
                        opacity: 0.6;
                    }
                    50% {
                        opacity: 0.8;
                    }
                    100% {
                        transform: translate(${Math.random() > 0.5 ? '+' : '-'}${Math.random() * 30 + 10}px, 
                                           ${Math.random() > 0.5 ? '+' : '-'}${Math.random() * 30 + 10}px) 
                                  scale(${Math.random() * 0.5 + 0.8});
                        opacity: 0.3;
                    }
                }
            `;
            document.head.appendChild(style);
        }

        card.insertBefore(particlesContainer, card.firstChild);
    }

    /**
     * 添加渐变效果
     * @param {HTMLElement} card 卡片元素
     * @param {number} rankIndex 排名索引
     */
    function addGradientEffect(card, rankIndex) {
        const gradientOverlay = document.createElement('div');
        gradientOverlay.className = 'gradient-overlay';
        
        // 获取适合不同排名的渐变
        const gradient = getGradientForRank(rankIndex);
        
        gradientOverlay.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: ${gradient};
            opacity: 0.4;
            mix-blend-mode: soft-light;
            pointer-events: none;
            z-index: 1;
            animation: gradient-shift 8s ease-in-out infinite alternate;
        `;
        
        // 添加关键帧动画到文档
        if (!document.getElementById('gradient-animations')) {
            const style = document.createElement('style');
            style.id = 'gradient-animations';
            style.textContent = `
                @keyframes gradient-shift {
                    0% {
                        background-position: 0% 50%;
                    }
                    50% {
                        background-position: 100% 50%;
                    }
                    100% {
                        background-position: 0% 50%;
                    }
                }
            `;
            document.head.appendChild(style);
        }
        
        card.insertBefore(gradientOverlay, card.firstChild);
    }

    /**
     * 添加光晕效果
     * @param {HTMLElement} card 卡片元素
     * @param {number} rankIndex 排名索引
     */
    function addGlowEffect(card, rankIndex) {
        const glowElement = document.createElement('div');
        glowElement.className = 'glow-effect';
        
        // 基于排名获取光晕颜色
        const color = getGlowColor(rankIndex);
        
        glowElement.style.cssText = `
            position: absolute;
            top: -30%;
            right: -30%;
            width: 60%;
            height: 60%;
            background: radial-gradient(circle, ${color} 0%, transparent 70%);
            opacity: 0.3;
            pointer-events: none;
            z-index: 1;
            animation: pulse-glow 5s ease-in-out infinite alternate;
        `;
        
        // 添加关键帧动画到文档
        if (!document.getElementById('glow-animations')) {
            const style = document.createElement('style');
            style.id = 'glow-animations';
            style.textContent = `
                @keyframes pulse-glow {
                    0% {
                        opacity: 0.2;
                        transform: scale(1);
                    }
                    50% {
                        opacity: 0.3;
                    }
                    100% {
                        opacity: 0.4;
                        transform: scale(1.2);
                    }
                }
            `;
            document.head.appendChild(style);
        }
        
        card.insertBefore(glowElement, card.firstChild);
    }

    /**
     * 获取粒子颜色
     * @param {number} rankIndex 排名索引
     * @returns {Array} 颜色数组
     */
    function getParticleColors(rankIndex) {
        const colorSets = {
            0: ['#FFD700', '#FFC700', '#FFB020', '#FFAA00', '#FF9500'], // NO.1 金色主题
            1: ['#FF7452', '#FF5C3D', '#FF4D4D', '#FF6347', '#FF8066'], // NO.2 红色主题
            2: ['#40B883', '#369B6F', '#34D399', '#2DD4BF', '#10B981']  // NO.3 绿色主题
        };
        
        return colorSets[rankIndex] || ['#A1A1AA', '#D4D4D8', '#E4E4E7', '#F4F4F5'];
    }

    /**
     * 获取渐变背景
     * @param {number} rankIndex 排名索引
     * @returns {string} 渐变CSS
     */
    function getGradientForRank(rankIndex) {
        const gradients = {
            0: 'linear-gradient(135deg, rgba(255, 215, 0, 0.2) 0%, rgba(255, 165, 0, 0.1) 100%)',  // NO.1
            1: 'linear-gradient(135deg, rgba(255, 116, 82, 0.2) 0%, rgba(255, 92, 61, 0.1) 100%)', // NO.2
            2: 'linear-gradient(135deg, rgba(64, 184, 131, 0.2) 0%, rgba(54, 155, 111, 0.1) 100%)' // NO.3
        };
        
        return gradients[rankIndex] || 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(240, 240, 240, 0.05) 100%)';
    }

    /**
     * 获取光晕颜色
     * @param {number} rankIndex 排名索引
     * @returns {string} 颜色
     */
    function getGlowColor(rankIndex) {
        const colors = {
            0: 'rgba(255, 215, 0, 0.8)',  // NO.1 金色
            1: 'rgba(255, 116, 82, 0.8)', // NO.2 红色
            2: 'rgba(64, 184, 131, 0.8)'  // NO.3 绿色
        };
        
        return colors[rankIndex] || 'rgba(200, 200, 200, 0.5)';
    }

    /**
     * 获取卡片背景颜色
     * @param {number} rankIndex 排名索引
     * @returns {string} 背景颜色
     */
    function getCardBackground(rankIndex) {
        return CONFIG.CARD_BACKGROUNDS[rankIndex] || '#fff';
    }

    /**
     * 获取卡片边框颜色
     * @param {number} rankIndex 排名索引 
     * @returns {string} 边框颜色
     */
    function getCardBorder(rankIndex) {
        return CONFIG.CARD_BORDERS[rankIndex] || '#e5e7eb';
    }

    /**
     * 创建卡片内容
     * @param {Object} source 货源数据
     * @returns {HTMLElement} 内容元素
     */
    function createCardContent(source) {
        const content = document.createElement('div');
        content.style.cssText = `
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        `;

        // 安全处理数据
        const nickname = source.nickname ? escapeHtml(source.nickname) : '未知用户';
        let title = source.title ? escapeHtml(source.title) : '无标题';
        const time = source.refresh_time ? formatTime(source.refresh_time) : '未知时间';

        // 创建用户信息区域
        const userSection = document.createElement('div');
        userSection.style.cssText = `
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid rgba(0,0,0,0.06);
        `;

        userSection.innerHTML = `
            <div style="
                width: 32px;
                height: 32px;
                border-radius: 50%;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 10px;
                flex-shrink: 0;
                box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
            ">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                </svg>
            </div>
            <div style="
                flex: 1;
                min-width: 0;
            ">
                <div style="
                    font-weight: 600;
                    font-size: 15px;
                    color: #2c3e50;
                    margin-bottom: 2px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    letter-spacing: 0.3px;
                ">${nickname}</div>
                <div style="
                    font-size: 11px;
                    color: #7f8c8d;
                    font-weight: 500;
                ">商家用户</div>
            </div>
        `;

        // 创建标题区域
        const titleSection = document.createElement('div');
        titleSection.style.cssText = `
            flex: 1;
            margin-bottom: 12px;
        `;

        titleSection.innerHTML = `
            <div style="
                color: #34495e;
                font-size: 13px;
                line-height: 1.6;
                font-weight: 500;
                word-break: break-word;
                display: -webkit-box;
                -webkit-line-clamp: 3;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
                max-height: 62px;
                position: relative;
            ">${title}</div>
        `;

        // 创建时间和状态区域
        const timeSection = document.createElement('div');
        timeSection.style.cssText = `
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-top: 8px;
            border-top: 1px solid rgba(0,0,0,0.06);
        `;

        timeSection.innerHTML = `
            <div style="
                display: flex;
                align-items: center;
                color: #95a5a6;
                font-size: 11px;
                font-weight: 500;
            ">
                <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-right: 4px;">
                    <circle cx="12" cy="12" r="10"></circle>
                    <polyline points="12 6 12 12 16 14"></polyline>
                </svg>
                ${time}
            </div>
            <div style="
                display: flex;
                align-items: center;
                gap: 4px;
            ">
                <div style="
                    width: 6px;
                    height: 6px;
                    border-radius: 50%;
                    background: #27ae60;
                    animation: pulse-dot 2s ease-in-out infinite;
                "></div>
                <span style="
                    font-size: 10px;
                    color: #27ae60;
                    font-weight: 600;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                ">活跃</span>
            </div>
        `;

        // 添加脉动动画样式
        if (!document.getElementById('pulse-dot-animation')) {
            const style = document.createElement('style');
            style.id = 'pulse-dot-animation';
            style.textContent = `
                @keyframes pulse-dot {
                    0%, 100% {
                        opacity: 1;
                        transform: scale(1);
                    }
                    50% {
                        opacity: 0.5;
                        transform: scale(1.2);
                    }
                }
            `;
            document.head.appendChild(style);
        }

        // 组装内容
        content.appendChild(userSection);
        content.appendChild(titleSection);
        content.appendChild(timeSection);

        return content;
    }

    /**
     * 格式化时间戳为可读时间
     * @param {number} timestamp 时间戳
     * @returns {string} 格式化的时间字符串
     */
    function formatTime(timestamp) {
        try {
            const date = new Date(timestamp * 1000);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch (error) {

            return '时间格式错误';
        }
    }

    /**
     * 格式化标签
     * @param {string} tags 标签JSON字符串
     * @returns {string} 格式化的HTML标签字符串
     */
    function formatTags(tags) {
        try {
            if (!tags) return '';
            const tagArray = JSON.parse(tags);
            if (!Array.isArray(tagArray)) return '';
            
            return tagArray.map(tag => `
                <span style="
                    display: inline-block;
                    padding: 3px 10px;
                    background: #F5F7FA;
                    color: #666;
                    border-radius: 20px;
                    font-size: 12px;
                    margin-right: 6px;
                    margin-bottom: 6px;
                    transition: all 0.3s ease;
                    border: 1px solid #E5E7EB;
                ">${escapeHtml(tag)}</span>
            `).join('');
        } catch (error) {
            return '';
        }
    }

    /**
     * HTML转义
     * @param {string} unsafe 不安全的字符串
     * @returns {string} 转义后的安全字符串
     */
    function escapeHtml(unsafe) {
        return unsafe
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }

    /**
     * 检测是否为移动设备
     * @returns {boolean} 是否为移动设备
     */
    function isMobile() {
        return window.innerWidth <= CONFIG.MOBILE_BREAKPOINT;
    }

    // 初始化
    initSourceDisplay();
})(); 