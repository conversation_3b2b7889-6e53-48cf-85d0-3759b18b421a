<?php

namespace plugin\Lottery\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Log;
use think\facade\Db;

class User extends BasePlugin {

    protected $scene = ['user'];  // 只允许普通用户访问
    
    protected $noNeedLogin = [
    ];

    // 抽奖页面
    public function index() {
        return View::fetch();
    }

    // 执行抽奖
    public function doLottery() {
        try {
            // 获取商户ID
            $merchantId = $this->user->id;
            
            // 读取配置文件
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = include $configFile;
            
            // 检查活动状态
            if (!isset($config['lottery_config']['status']) || !$config['lottery_config']['status']) {
                throw new \Exception('抽奖活动已关闭');
            }
            
            // 检查时间限制
            $currentTime = date('H:i');
            if ($currentTime < $config['lottery_config']['start_hour'] || $currentTime > $config['lottery_config']['end_hour']) {
                throw new \Exception('不在抽奖时间范围内');
            }
            
            // 检查抽奖次数
            $merchantLimit = isset($config['merchant_limits'][$merchantId]) ? 
                $config['merchant_limits'][$merchantId] : 
                ['daily_limit' => $config['lottery_config']['daily_limit'], 'used_count' => 0];
                
            // 获取流水规则获得的抽奖次数
            $turnoverDraws = 0;
            if (isset($config['turnover_claims'][$merchantId]) && 
                $config['turnover_claims'][$merchantId]['date'] === date('Y-m-d')) {
                // 计算总的流水抽奖次数
                foreach ($config['turnover_claims'][$merchantId]['claimed_levels'] as $level) {
                    if (isset($config['turnover_rules'][$level]['draw_times'])) {
                        $turnoverDraws += $config['turnover_rules'][$level]['draw_times'];
                    }
                }
                // 减去已使用的次数
                $turnoverDraws -= ($config['turnover_claims'][$merchantId]['draw_times_used'] ?? 0);
            }
            
            // 确保不会出现负数
            $turnoverDraws = max(0, $turnoverDraws);
            
            // 如果基础次数已用完，检查是否还有流水获得的次数
            if ($merchantLimit['used_count'] >= $merchantLimit['daily_limit']) {
                if ($turnoverDraws <= 0) {
                    throw new \Exception('今日抽奖次数已用完');
                }
                // 使用流水获得的抽奖次数
                $config['turnover_claims'][$merchantId]['draw_times_used'] = 
                    ($config['turnover_claims'][$merchantId]['draw_times_used'] ?? 0) + 1;
            } else {
                // 使用基础抽奖次数
                $merchantLimit['used_count']++;
                $config['merchant_limits'][$merchantId] = $merchantLimit;
            }
            
            // 获取可用奖品
            $availablePrizes = array_filter($config['prizes'] ?? [], function($prize) {
                return $prize['status'] == 1 && $prize['stock'] > 0;
            });
            
            if (empty($availablePrizes)) {
                throw new \Exception('暂无可用奖品');
            }
            
            // 抽奖逻辑
            $totalProbability = array_sum(array_column($availablePrizes, 'probability'));
            $randomNum = mt_rand(1, $totalProbability * 100) / 100;
            
            $currentSum = 0;
            $winPrize = null;
            foreach ($availablePrizes as $prize) {
                $currentSum += $prize['probability'];
                if ($randomNum <= $currentSum) {
                    $winPrize = $prize;
                    break;
                }
            }
            
            if (!$winPrize) {
                throw new \Exception('抽奖失败');
            }
            
            // 记录中奖信息
            if (!isset($config['lottery_records'])) {
                $config['lottery_records'] = [];
            }
            
            $record = [
                'id' => uniqid(),
                'user_id' => $this->user->id,
                'merchant_id' => $merchantId,
                'prize_id' => $winPrize['id'],
                'prize_name' => $winPrize['name'],
                'prize_type' => $winPrize['type'],
                'balance_amount' => isset($winPrize['balance_amount']) ? $winPrize['balance_amount'] : 0,
                'create_time' => date('Y-m-d H:i:s'),
                'shipped' => false,
                'ship_time' => null,
                'balance_sent' => false,
                'auto_sent' => false
            ];
            
            $config['lottery_records'][] = $record;
            
            // 更新奖品库存
            foreach ($config['prizes'] as &$prize) {
                if ($prize['id'] == $winPrize['id']) {
                    $prize['stock']--;
                    break;
                }
            }
            
            // 保存配置
            if (!file_put_contents($configFile, "<?php\nreturn " . var_export($config, true) . ";\n")) {
                throw new \Exception('保存配置失败');
            }
            
            // 修改角度计算逻辑
            $prizeCount = count($availablePrizes);
            $baseAngle = 360 / $prizeCount;
            $prizeIndex = array_search($winPrize, array_values($availablePrizes));
            
            // 计算指针指向奖品中心的角度
            // 由于转盘是顺时针旋转，而指针在12点位置，需要调整角度计算
            $targetAngle = 360 - ($prizeIndex * $baseAngle + $baseAngle / 2);
            // 添加额外的旋转圈数（4圈）
            $finalAngle = 1440 + $targetAngle;
            
            // 计算新的总抽奖次数(累计)
            $totalDraws = ($merchantLimit['used_count'] ?? 0);
            if (isset($config['turnover_claims'][$merchantId]) && 
                $config['turnover_claims'][$merchantId]['date'] === date('Y-m-d')) {
                $totalDraws += ($config['turnover_claims'][$merchantId]['draw_times_used'] ?? 0);
            }
            
            return json([
                'code' => 200,
                'msg' => '抽奖成功',
                'data' => [
                    'prize' => $winPrize,
                    'angle' => $finalAngle,
                    'remainingBaseDraws' => max(0, $merchantLimit['daily_limit'] - $merchantLimit['used_count']),
                    'turnoverDraws' => max(0, $turnoverDraws - 1),
                    'totalDraws' => ($merchantLimit['used_count'] ?? 0) + 
                        ($config['turnover_claims'][$merchantId]['draw_times_used'] ?? 0)
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('抽奖失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '抽奖失败：' . $e->getMessage()]);
        }
    }

    // 计算目标角度
    private function calculateTargetAngle($winPrize, $availablePrizes) {
        $prizeCount = count($availablePrizes);
        $baseAngle = 360 / $prizeCount;
        $prizeIndex = array_search($winPrize, $availablePrizes);
        return 1440 + ($baseAngle * $prizeIndex); // 多转4圈后停止
    }

    // 获取抽奖记录
    public function getLotteryRecords() {
        try {
            $page = input('page/d', 1);
            $pageSize = input('pageSize/d', 20);
            $all = input('type/s', '') === 'winners';  // 是否获取所有用户记录
            
            // 读取配置文件
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            // 获取真实中奖记录
            $records = isset($config['lottery_records']) ? $config['lottery_records'] : [];
            
            // 获取隐藏奖品配置
            $hiddenPrizes = isset($config['hidden_prizes']) ? $config['hidden_prizes'] : [];
            
            // 修改过滤逻辑
            $records = array_filter($records, function($record) use ($hiddenPrizes, $all) {
                // 如果是获取所有记录（中奖用户列表）
                if ($all) {
                    // 显示虚拟记录
                    if (is_string($record['merchant_id']) && strpos($record['merchant_id'], '商家') === 0) {
                        return true;
                    }
                    
                    // 对于真实用户记录，如果奖品在隐藏列表中则不显示
                    if (in_array($record['prize_id'], $hiddenPrizes)) {
                        return false;
                    }
                }
                // 如果是获取个人记录，显示所有记录（包括隐藏奖品）
                return true;
            });
            
            // 如果不是获取所有记录，只返回当前用户的记录
            if (!$all) {
                $records = array_filter($records, function($record) {
                    return $record['merchant_id'] == $this->user->id;
                });
            }
            
            // 按时间倒序排序
            usort($records, function($a, $b) {
                return strtotime($b['create_time']) - strtotime($a['create_time']);
            });
            
            // 处理记录信息
            foreach ($records as &$record) {
                // 添加奖品类型文本和样式
                $record['prize_type_text'] = $this->getPrizeTypeText($record['prize_type']);
                $record['prize_type_style'] = $this->getPrizeTypeStyle($record['prize_type']);
                
                // 确保余额金额为数字
                $record['balance_amount'] = isset($record['balance_amount']) ? floatval($record['balance_amount']) : 0;
                
                // 修改商户名称获取逻辑
                if (isset($record['is_virtual']) && $record['is_virtual']) {
                    // 如果是虚拟记录，直接显示商家ID
                    $record['merchant_name'] = $record['merchant_id'];
                } else if (is_numeric($record['merchant_id'])) {
                    // 如果是真实用户ID，从数据库获取昵称
                    $merchant = Db::name('user')->where('id', $record['merchant_id'])->find();
                    $record['merchant_name'] = $merchant ? $merchant['nickname'] : '未知用户';
                } else {
                    // 其他情况显示商家ID
                    $record['merchant_name'] = $record['merchant_id'];
                }
            }
            
            // 分页处理
            $total = count($records);
            $records = array_slice($records, ($page - 1) * $pageSize, $pageSize);
            
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => [
                    'list' => $records,
                    'total' => $total
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取中奖记录失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取记录失败：' . $e->getMessage()]);
        }
    }

    // 获取奖品类型对应的标签类型和样式
    private function getPrizeTypeStyle($type) {
        // 读取配置文件获取奖品类型定义
        $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
        $config = file_exists($configFile) ? include $configFile : [];
        
        // 从配置中获取奖品类型定义
        $prizeTypes = isset($config['prize_types']) ? $config['prize_types'] : [];
        
        // 查找对应的类型定义
        foreach ($prizeTypes as $prizeType) {
            if ($prizeType['value'] === $type) {
                if ($prizeType['tagType'] === 'custom') {
                    return ['style' => 'background-color: ' . $prizeType['tagColor']];
                }
                return ['type' => $prizeType['tagType'] ?: 'info'];
            }
        }
        
        // 如果找不到对应的类型定义，返回默认样式
        return ['type' => 'info'];
    }

    // 获取奖品类型文本
    private function getPrizeTypeText($type) {
        // 读取配置文件获取奖品类型定义
        $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
        $config = file_exists($configFile) ? include $configFile : [];
        
        // 从配置中获取奖品类型定义
        $prizeTypes = isset($config['prize_types']) ? $config['prize_types'] : [];
        
        // 查找对应的类型定义
        foreach ($prizeTypes as $prizeType) {
            if ($prizeType['value'] === $type) {
                return $prizeType['label'];
            }
        }
        
        // 如果找不到对应的类型定义，返回未知类型
        return '未知类型';
    }

    // 获取抽奖配置
    public function getLotteryConfig() {
        try {
            // 读取配置文件
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            // 获取基础配置
            $lotteryConfig = isset($config['lottery_config']) ? $config['lottery_config'] : [
                'status' => 1,
                'daily_limit' => 3,
                'start_hour' => '00:00',
                'end_hour' => '23:59'
            ];
            
            // 获取商户ID
            $merchantId = $this->user->id;
            
            // 获取商户限制(基础每日抽奖次数)
            $merchantLimit = isset($config['merchant_limits'][$merchantId]) ? 
                $config['merchant_limits'][$merchantId] : [
                    'daily_limit' => $lotteryConfig['daily_limit'],
                    'used_count' => 0,
                    'update_time' => time(),
                    'last_reset_date' => date('Y-m-d')
                ];
            
            // 修改商户限制的重置逻辑
            if (!isset($merchantLimit['last_reset_date']) || $merchantLimit['last_reset_date'] !== date('Y-m-d')) {
                $merchantLimit = [
                    'daily_limit' => $lotteryConfig['daily_limit'],
                    'used_count' => 0,
                    'update_time' => time(),
                    'last_reset_date' => date('Y-m-d')
                ];
                $config['merchant_limits'][$merchantId] = $merchantLimit;
                
                // 保存更新后的配置
                if (!file_put_contents($configFile, "<?php\nreturn " . var_export($config, true) . ";\n")) {
                    throw new \Exception('保存配置失败');
                }
            }
            
            // 获取流水规则获得的抽奖次数
            $turnoverDraws = 0;
            if (isset($config['turnover_claims'][$merchantId]) && 
                $config['turnover_claims'][$merchantId]['date'] === date('Y-m-d')) {
                foreach ($config['turnover_claims'][$merchantId]['claimed_levels'] as $level) {
                    if (isset($config['turnover_rules'][$level]['draw_times'])) {
                        $turnoverDraws += $config['turnover_rules'][$level]['draw_times'];
                    }
                }
                // 减去已使用的次数
                $turnoverDraws -= ($config['turnover_claims'][$merchantId]['draw_times_used'] ?? 0);
            }
            
            // 确保不会出现负数
            $turnoverDraws = max(0, $turnoverDraws);
            
            // 计算剩余基础抽奖次数
            $remainingBaseDraws = max(0, $merchantLimit['daily_limit'] - $merchantLimit['used_count']);
            
            // 计算总抽奖次数(累计)
            $totalDraws = ($merchantLimit['used_count'] ?? 0);
            if (isset($config['turnover_claims'][$merchantId]) && 
                $config['turnover_claims'][$merchantId]['date'] === date('Y-m-d')) {
                $totalDraws += ($config['turnover_claims'][$merchantId]['draw_times_used'] ?? 0);
            }
            
            // 获取可用奖品列表
            $prizes = array_filter($config['prizes'] ?? [], function($prize) {
                return $prize['status'] == 1 && $prize['stock'] > 0;
            });
            
            // 修改返回数据，只返回已使用的次数总和
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => [
                    'config' => $lotteryConfig,
                    'remainingBaseDraws' => $remainingBaseDraws,
                    'turnoverDraws' => $turnoverDraws,
                    'totalDraws' => ($merchantLimit['used_count'] ?? 0) + 
                        ($config['turnover_claims'][$merchantId]['draw_times_used'] ?? 0), // 只计算已使用次数的总和
                    'prizes' => array_values($prizes),
                    'merchantLimit' => $merchantLimit
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取抽奖配置失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取配置失败：' . $e->getMessage()]);
        }
    }

    // 获取今日抽奖次数
    protected function getTodayLotteryCount() 
    {
        try {
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = include $configFile;
            
            // 获取商户ID
            $merchantId = $this->user->merchant_id;
            
            // 检查是否存在商户限制配置
            if (isset($config['merchant_limits'][$merchantId])) {
                // 直接使用商户限制中记录的已使用次数
                return isset($config['merchant_limits'][$merchantId]['used_count']) ? 
                    $config['merchant_limits'][$merchantId]['used_count'] : 0;
            }
            
            // 如果没有商户限制配置，则使用日志统计（兼容旧数据）
            $logs = isset($config['lottery_logs']) ? 
                (is_string($config['lottery_logs'], true) ? json_decode($config['lottery_logs'], true) : $config['lottery_logs']) : 
                [];
            
            if (!is_array($logs)) {
                return 0;
            }

            $todayStart = strtotime(date('Y-m-d'));
            $userId = $this->user->id;
            
            return count(array_filter($logs, function($log) use ($todayStart, $userId) {
                return $log['user_id'] == $userId && 
                    strtotime($log['create_time']) >= $todayStart;
            }));
        } catch (\Exception $e) {
            Log::error('获取今日抽奖次数失败：' . $e->getMessage());
            return 0;
        }
    }

    // 获取总抽奖次数
    protected function getTotalDrawsCount()
    {
        try {
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = include $configFile;
            
            // 使用 lottery_records 来统计
            $records = isset($config['lottery_records']) ? $config['lottery_records'] : [];
            
            if (!is_array($records)) {
                return 0;
            }

            $userId = $this->user->id;
            
            // 统计当前用户的抽奖记录数量
            return count(array_filter($records, function($record) use ($userId) {
                return $record['user_id'] == $userId;
            }));
        } catch (\Exception $e) {
            Log::error('获取总抽奖次数失败：' . $e->getMessage());
            return 0;
        }
    }

    // 获取通知列表
    public function getNotifications() {
        try {
            $notifications = Db::name('lottery_notifications')
                ->where('status', 1)
                ->order('create_time', 'desc')
                ->select()
                ->toArray();

            return json([
                'code' => 0,
                'msg' => '获取成功',
                'data' => $notifications
            ]);
        } catch (\Exception $e) {
            Log::error('获取通知列表失败：' . $e->getMessage());
            return json([
                'code' => 500,
                'msg' => '获取通知失败：' . $e->getMessage()
            ]);
        }
    }

    // 获取奖品类型列表
    public function getPrizeTypes() {
        try {
            $types = Db::name('lottery_prize_types')
                ->where('status', 1)
                ->select()
                ->toArray();

            return json([
                'code' => 0,
                'msg' => '获取成功',
                'data' => $types
            ]);
        } catch (\Exception $e) {
            Log::error('获取奖品类型列表失败：' . $e->getMessage());
            return json([
                'code' => 500,
                'msg' => '获取奖品类型失败：' . $e->getMessage()
            ]);
        }
    }

    // 获取抽奖数据
    public function fetchData() {
        try {
            // 读取配置文件
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = include $configFile;
            
            // 获取奖品列表
            $prizes = isset($config['prizes']) ? $config['prizes'] : [];
            
            // 转换为前端需要的格式
            $tableData = [];
            foreach ($prizes as $id => $prize) {
                $tableData[] = [
                    'id' => $id,
                    'name' => $prize['name'],
                    'type' => $prize['type'],
                    'probability' => $prize['probability'],
                    'stock' => $prize['stock'],
                    'status' => $prize['status']
                ];
            }

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => [
                    'tableData' => $tableData
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取抽奖数据失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取数据失败：' . $e->getMessage()]);
        }
    }

    // 获取流水统计
    public function getTurnoverStats()
    {
        try {
            // 读取配置文件获取流水规则
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = include $configFile;
            
            // 获取商户ID
            $merchantId = $this->user->id;
            
            // 获取流水规则
            $turnoverRules = isset($config['turnover_rules']) ? $config['turnover_rules'] : [];
            
            // 初始化今日领取记录
            if (!isset($config['turnover_claims'][$merchantId])) {
                $config['turnover_claims'][$merchantId] = [
                    'date' => date('Y-m-d'),
                    'claimed_levels' => []
                ];
            }
            
            // 检查是否需要重置领取记录（新的一天）
            if ($config['turnover_claims'][$merchantId]['date'] !== date('Y-m-d')) {
                $config['turnover_claims'][$merchantId] = [
                    'date' => date('Y-m-d'),
                    'claimed_levels' => []
                ];
            }

            // 按流水金额排序
            usort($turnoverRules, function($a, $b) {
                return $a['turnover_amount'] - $b['turnover_amount'];
            });

            // 获取今日和本月流水
            $today = date('Y-m-d');
            $monthStart = date('Y-m-01');
            
            // 获取今日流水
            $todayAmount = Db::name('user_analysis')
                ->where([
                    ['user_id', '=', $merchantId],
                    ['date', '=', $today]
                ])
                ->sum('total_amount');
                
            // 获取本月流水    
            $monthAmount = Db::name('user_analysis')
                ->where([
                    ['user_id', '=', $merchantId],
                    ['date', '>=', $monthStart],
                    ['date', '<=', $today]
                ])
                ->sum('total_amount');
            
            // 计算已获得的抽奖次数和下一个目标
            $todayDraws = 0;
            $nextRequired = null;
            $todayAmount = floatval($todayAmount);
            $configUpdated = false;
            
            // 获取已领取的等级列表
            $claimedLevels = $config['turnover_claims'][$merchantId]['claimed_levels'] ?? [];

            // 计算已获得的抽奖次数（仅统计已领取的）
            foreach ($turnoverRules as $index => $rule) {
                // 只处理状态为启用的规则
                if (!isset($rule['status']) || $rule['status'] != 1) {
                    continue;
                }

                if ($todayAmount >= $rule['turnover_amount'] && in_array($index, $claimedLevels)) {
                    $todayDraws += $rule['draw_times'];
                }

                // 计算下一个目标
                if ($todayAmount < $rule['turnover_amount'] && $nextRequired === null) {
                    $nextRequired = $rule['turnover_amount'] - $todayAmount;
                }
            }
            
            // 如果已达到最高等级，将下一个目标设为0
            if ($nextRequired === null) {
                $nextRequired = 0;
            }

            // 获取已领取的等级列表
            $claimedLevels = $config['turnover_claims'][$merchantId]['claimed_levels'] ?? [];
            
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => [
                    'stats' => [
                        'today_amount' => number_format($todayAmount, 2, '.', ''),
                        'month_amount' => number_format($monthAmount, 2, '.', ''),
                        'today_draws' => $todayDraws,
                        'next_required' => number_format($nextRequired, 2, '.', '')
                    ],
                    'rules' => array_map(function($rule, $index) use ($claimedLevels, $todayAmount) {
                        $claimed = in_array($index, $claimedLevels);
                        $canClaim = !$claimed && $todayAmount >= $rule['turnover_amount'] && (isset($rule['status']) ? $rule['status'] == 1 : true);

                        return [
                            'turnover_amount' => number_format($rule['turnover_amount'], 2, '.', ''),
                            'draw_times' => intval($rule['draw_times']),
                            'claimed' => $claimed,
                            'can_claim' => $canClaim,
                            'status' => isset($rule['status']) ? intval($rule['status']) : 1,
                            'claiming' => false
                        ];
                    }, $turnoverRules, array_keys($turnoverRules)),
                    'config_updated' => $configUpdated
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取流水统计失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取统计失败：' . $e->getMessage()]);
        }
    }

    // 计算商户流水
    private function calculateMerchantTurnover($merchantId) {
        // 计算当日流水
        $today = date('Y-m-d');
        return Db::name('user_analysis')
            ->where([
                ['user_id', '=', $merchantId],
                ['date', '=', $today]
            ])
            ->sum('total_amount');
    }

    // 获取奖品列表
    public function getPrizes() {
        try {
            // 读取配置文件
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = include $configFile;

            // 获取可用奖品
            $prizes = isset($config['prizes']) ? $config['prizes'] : [];

            // 转换为前端需要的格式
            $prizeList = [];
            foreach ($prizes as $prize) {
                if ($prize['status'] == 1) { // 只返回启用的奖品
                    $prizeList[] = [
                        'id' => $prize['id'],
                        'name' => $prize['name'],
                        'type' => $prize['type'],
                        'probability' => $prize['probability']
                    ];
                }
            }

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $prizeList
            ]);
        } catch (\Exception $e) {
            Log::error('获取奖品列表失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取奖品列表失败：' . $e->getMessage()]);
        }
    }

    // 手动领取流水奖励
    public function claimTurnoverReward() {
        try {
            // 获取商户ID
            $merchantId = $this->user->id;

            // 获取请求参数
            $ruleIndex = input('rule_index');
            if ($ruleIndex === null) {
                throw new \Exception('缺少规则索引参数');
            }

            // 读取配置文件
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = include $configFile;

            // 获取流水规则
            $turnoverRules = isset($config['turnover_rules']) ? $config['turnover_rules'] : [];

            if (!isset($turnoverRules[$ruleIndex])) {
                throw new \Exception('规则不存在');
            }

            $rule = $turnoverRules[$ruleIndex];

            // 检查规则状态
            if (!isset($rule['status']) || $rule['status'] != 1) {
                throw new \Exception('该规则未启用');
            }

            // 初始化今日领取记录
            if (!isset($config['turnover_claims'][$merchantId])) {
                $config['turnover_claims'][$merchantId] = [
                    'date' => date('Y-m-d'),
                    'claimed_levels' => []
                ];
            }

            // 检查是否需要重置领取记录（新的一天）
            if ($config['turnover_claims'][$merchantId]['date'] !== date('Y-m-d')) {
                $config['turnover_claims'][$merchantId] = [
                    'date' => date('Y-m-d'),
                    'claimed_levels' => []
                ];
            }

            $claimedLevels = $config['turnover_claims'][$merchantId]['claimed_levels'] ?? [];

            // 检查是否已经领取过
            if (in_array($ruleIndex, $claimedLevels)) {
                throw new \Exception('该奖励已经领取过了');
            }

            // 计算当日流水
            $todayAmount = $this->calculateMerchantTurnover($merchantId);

            // 检查是否达到流水要求
            if ($todayAmount < $rule['turnover_amount']) {
                throw new \Exception('流水金额不足，还需要 ' . number_format($rule['turnover_amount'] - $todayAmount, 2) . ' 元');
            }

            // 添加到已领取列表
            $config['turnover_claims'][$merchantId]['claimed_levels'][] = $ruleIndex;

            // 保存配置
            file_put_contents($configFile, "<?php\nreturn " . var_export($config, true) . ";\n");

            return json([
                'code' => 200,
                'msg' => '领取成功',
                'data' => [
                    'draw_times' => $rule['draw_times'],
                    'turnover_amount' => $rule['turnover_amount']
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('领取流水奖励失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }
}