<?php

namespace plugin\Suolink;
 
use app\common\library\Plugin;
use app\common\model\DwzApi as DwzApiModel;
use app\common\service\HttpService;

class Suolink extends Plugin {

    /**
     * 插件安装方法
     * @return bool
     */
    public function install() {
        $model = DwzApiModel::where(['code' => 'Suolink'])->find();
        if (!$model) {
            $model = new DwzApiModel();
        }
        $model->code = 'Suolink';
        $model->name = 'Suolink短链接';
        $model->tips = '';
        $model->website = 'http://api.suolink.cn/';
        $model->save();

        return true;
    }

    /**
     * 插件卸载方法
     * @return bool
     */
    public function uninstall() {
        $model = DwzApiModel::where(['code' => 'Suolink'])->find();
        if ($model) {
            $model->delete();
        }
        return true;
    }

    public function create($url) {
        $key = plugconf('Suolink.key');
        $domain = plugconf('Suolink.domain');
        $expireDate = plugconf('Suolink.expireDate');

        // 处理多个域名的情况，随机选择一个域名
        if (strpos($domain, ',') !== false) {
            $domains = explode(',', $domain);
            // 过滤掉空白域名
            $domains = array_filter($domains, function($d) {
                return trim($d) !== '';
            });
            // 如果有多个有效域名，随机选择一个
            if (count($domains) > 0) {
                $domain = trim($domains[array_rand($domains)]);
            }
        }

        // 构建API请求URL
        $apiUrl = "http://api.suolink.cn/api?";
        $apiUrl .= "url=" . urlencode($url);
        $apiUrl .= "&key=" . $key;
        $apiUrl .= "&expireDate=" . $expireDate;
        $apiUrl .= "&domain=" . $domain;
        $apiUrl .= "&format=json";

        $res = HttpService::get($apiUrl);
        
        if ($res === false) {
            record_system_log("Suolink短链接生成失败：请求失败");
            return false;
        }
        
        $json = json_decode($res);
        if (!$json) {
            record_system_log("Suolink短链接生成失败：" . $res);
            return false;
        }
        
        if (!empty($json->err)) {
            record_system_log("Suolink短链接生成失败：" . $json->err);
            return false;
        }
        
        return $json->url;
    }
} 