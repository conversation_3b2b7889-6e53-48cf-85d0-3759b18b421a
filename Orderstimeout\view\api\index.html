<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>订单管理</title>
    <style>
        #app {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #ffffff;
        }

        .progress-panel {
            background: #ffffff;
            min-height: 400px;
            padding: 16px;
            color: #000;
            border-radius: 4px;
            width: 800px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        .title {
            margin-bottom: 16px;
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }

        .actions {
            margin-top: 16px;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        .tips {
            font-size: 14px;
            color: #ff0000;
            font-weight: bold;
            margin-top: 8px;
        }
    </style>
</head>
<body>

<div id="app">
    <el-card class="progress-panel">
        <template #header>
            <div class="title">订单管理</div>
        </template>

        <div class="actions">
            <el-form :model="form" label-width="auto">
                <el-form-item label="关闭过期订单开关：">
                    <el-radio-group v-model="form.status" @change="handleToggleStatus">
                        <el-radio :value="0">关闭</el-radio>
                        <el-radio :value="1">开启</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <div class="tips">修改完订单状态之后必须关闭不然任务计划会不执行</div>
            <div class="tips" style="margin-top: 4px;">订单状态说明：0未支付、1已支付、2已关闭、3已退款</div>
        </div>

        <el-input v-model="searchKeyword" placeholder="请输入完整的订单号" clearable></el-input>
        <el-button type="primary" @click="searchOrders">搜索</el-button>

        <el-table v-if="orders && orders.length > 0" :data="orders" style="width: 100%; margin-top: 20px;">
            <el-table-column prop="id" label="ID" width="50"></el-table-column>
            <el-table-column prop="trade_no" label="订单号"></el-table-column>
            <el-table-column prop="goods_name" label="商品名称"></el-table-column>
            <el-table-column prop="status" label="状态"></el-table-column>
            <el-table-column label="操作" width="150">
                <template #default="scope">
                    <el-button size="mini" @click="editOrder(scope.row)">未付款</el-button>
                </template>
            </el-table-column>
        </el-table>

        <div v-else-if="searchKeyword" class="no-data" style="text-align: center; margin-top: 20px; color: #909399;">
            未找到相关订单信息
        </div>

        <div class="tips">提示：操作不可撤销，请谨慎操作。</div>

        <el-dialog :visible.sync="editDialogVisible" title="编辑订单状态">
            <el-form :model="currentOrder">
                <el-form-item label="状态">
                    <el-input v-model="currentOrder.status"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="editDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="saveOrder">保存</el-button>
            </div>
        </el-dialog>
    </el-card>
</div>

<script src="/static/others/vue/vue.global.prod.js"></script>
<script src="/static/others/element-plus/index.full.min.js"></script>
<script src="/static/others/axios/axios.min.js"></script>

<script type="module">
    const { ref, reactive, onMounted } = Vue;
    const { ElMessage } = ElementPlus;

    const app = Vue.createApp({
        setup() {
            const form = reactive({
                status: 0
            });
            
            const fetchStatus = async () => {
                try {
                    const res = await axios.get("/plugin/Orderstimeout/Api/fetchData");
                    if (res.data.code === 200) {
                        form.status = res.data.data.status;
                    }
                } catch (error) {
                    ElMessage.error('获取状态失败: ' + error.message);
                }
            };

            const handleToggleStatus = async (status) => {
                try {
                    const saveRes = await axios.post("/plugin/Orderstimeout/Api/save", {
                        status: status
                    });
                    
                    if (saveRes.data.code === 1) {
                        const resetRes = await axios.post("/plugin/Orderstimeout/Api/resetCustomStatus", {
                            status: status
                        });
                        
                        if (resetRes.data.code === 200) {
                            ElMessage.success(status === 1 ? '状态已开启' : '状态已关闭');
                        } else {
                            ElMessage.warning('状态已保存，但队列更新失败');
                        }
                    } else {
                        ElMessage.error(saveRes.data.msg || '保存失败');
                        form.status = status === 1 ? 0 : 1; // 回滚状态
                    }
                } catch (error) {
                    ElMessage.error('操作失败: ' + error.message);
                    form.status = status === 1 ? 0 : 1; // 回滚状态
                }
            };

            onMounted(() => {
                fetchStatus();
            });

            const searchKeyword = ref('');
            const orders = ref([]);
            const editDialogVisible = ref(false);
            const currentOrder = ref({});

            const searchOrders = async () => {
                if (!searchKeyword.value) {
                    ElMessage.warning('请输入完整的订单号进行搜索');
                    return;
                }
                try {
                    const res = await axios.get("/plugin/Orderstimeout/Api/searchOrders", {
                        params: { trade_no: searchKeyword.value }
                    });
                    
                    if (res.data.code === 200) {
                        orders.value = res.data.data;
                    } else if (res.data.code === 404) {
                        orders.value = [];
                        ElMessage.warning('未找到该订单号');
                    } else {
                        ElMessage.error(res.data.msg);
                    }
                } catch (error) {
                    orders.value = [];
                    ElMessage.error('获取订单失败: ' + error.message);
                }
            };

            const editOrder = async (order) => {
                try {
                    const res = await axios.post("/plugin/Orderstimeout/Api/saveOrder", {
                        id: order.id,
                        status: 0  // 直接设置状态为0
                    });
                    
                    if (res.data.code === 200) {
                        ElMessage.success('订单状态已修改为0');
                        // 刷新订单列表
                        searchOrders();
                    } else {
                        ElMessage.error(res.data.msg || '修改失败');
                    }
                } catch (error) {
                    ElMessage.error('修改失败: ' + error.message);
                }
            };

            const saveOrder = async () => {
                try {
                    const res = await axios.post("/plugin/Orderstimeout/Api/saveOrder", currentOrder.value);
                    if (res.data.code === 200) {
                        ElMessage.success('订单状态保存成功');
                        searchOrders();
                        editDialogVisible.value = false;
                    } else {
                        ElMessage.error(res.data.msg);
                    }
                } catch (error) {
                    ElMessage.error('保存订单状态失败: ' + error.message);
                }
            };

            return { 
                form,
                handleToggleStatus,
                searchKeyword,
                orders,
                editDialogVisible,
                currentOrder,
                searchOrders,
                editOrder,
                saveOrder
            };
        }
    });

    app.use(ElementPlus);
    app.mount("#app");
</script>
</body>
</html>
