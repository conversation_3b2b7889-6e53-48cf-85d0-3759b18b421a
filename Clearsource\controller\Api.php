<?php
namespace plugin\Clearsource\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;
use think\facade\Request;
use think\response\Json;

class Api extends BasePlugin
{
    protected $scene = ['admin']; // 仅管理员可访问

    protected $noNeedLogin = ['index']; // index方法无需鉴权

    // 显示 API 页面
    public function index()
    {
       return View::fetch();
    }

    // 自动匹配包含“goods_pool”关键字的表名
    protected function getTableName(): ?string
    {
        $tables = Db::query("SHOW TABLES"); // 获取所有表
        $targetTable = null;
        
        foreach ($tables as $table) {
            $tableName = array_values($table)[0]; // 获取表名
            if (strpos($tableName, 'goods_pool') !== false) {
                $targetTable = $tableName;
                break;
            }
        }
        
        return $targetTable;
    }

    // 清除货源标签或匹配 title 字段关键字
    public function clearSourceData(): Json
    {
        if (!Request::isPost()) {
            return json(['code' => 405, 'msg' => '请求方式无效']);
        }

        try {
            // 动态获取表名
            $tableName = $this->getTableName();

            if (!$tableName) {
                return json(['code' => 404, 'msg' => '未找到符合条件的表']);
            }

            // 获取前端传入的 keyword 参数
            $keyword = Request::param('keyword');

            if ($keyword) {
                // 如果提供了 keyword，则按 title 字段匹配删除
                $deletedRows = Db::table($tableName)
                    ->whereLike('title', "%{$keyword}%")
                    ->delete();

                if ($deletedRows > 0) {
                    return json(['code' => 200, 'msg' => "包含 '{$keyword}' 的货源标签清空成功"]);
                } else {
                    return json(['code' => 204, 'msg' => "未找到包含 '{$keyword}' 的记录"]);
                }
            } else {
                // 如果未提供 keyword，则清空表中所有数据
                $deletedRows = Db::table($tableName)->delete(true);

                if ($deletedRows > 0) {
                    return json(['code' => 200, 'msg' => '所有货源标签清空成功']);
                } else {
                    return json(['code' => 204, 'msg' => '没有货源标签被清空']);
                }
            }
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '错误: ' . $e->getMessage()]);
        }
    }
}
