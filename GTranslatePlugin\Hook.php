<?php

namespace plugin\GTranslatePlugin;

class Hook
{
    public function handle(&$array)
    {
        // $array[0] 是 $user，$array[1] 是 $js
        $status = intval(plugconf("Customname.status") ?? 0);  // 读取状态配置

        // 当status=1时候引入js
        if ($status == 1) {
            // 使用 plugstatic 函数获取静态资源的 URL
            $js_url = plugstatic('GTranslatePlugin', 'init.js');

            // 将 JS 文件的 URL 添加到 $array[1] 中
            $array[1][] = $js_url;
        }
    }
}
