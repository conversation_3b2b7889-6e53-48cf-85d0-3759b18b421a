// 简化版反调试保护
(function() {
    'use strict';
    
    // 获取调试级别配置
    const debugLevel = window.debugProtectionLevel || 'all';

    // 检测是否在iframe中
    if (window.top !== window.self) {
        window.top.location = window.self.location;
    }

    // 禁用右键菜单
    document.oncontextmenu = function(e) {
        e.preventDefault();
        return false;
    };

    // 禁用 F12、Ctrl+Shift+I、Ctrl+Shift+J、Ctrl+Shift+C
    document.onkeydown = function(e) {
        if (
            e.keyCode === 123 || // F12
            (e.ctrlKey && e.shiftKey && e.keyCode === 73) || // Ctrl+Shift+I
            (e.ctrlKey && e.shiftKey && e.keyCode === 74) || // Ctrl+Shift+J
            (e.ctrlKey && e.shiftKey && e.keyCode === 67) || // Ctrl+Shift+C
            // 附加禁用其他常见开发快捷键
            (e.ctrl<PERSON>ey && e.keyCode === 85) || // Ctrl+U (查看源代码)
            (e.ctrlKey && e.keyCode === 83) || // Ctrl+S (保存)
            (e.keyCode >= 112 && e.keyCode <= 123) // F1-F12
        ) {
            e.preventDefault();
            return false;
        }
    };

    // 禁用开发者工具
    (function() {
        // 检测开发者工具的打开
        let devtools = function() {};
        devtools.toString = function() {
            this.opened = true;
        }

        // 定期检查是否打开了开发者工具
        setInterval(function() {
            devtools.opened = false;
            console.log(devtools);
            console.clear();
            if(devtools.opened) {
                // 检测到开发者工具打开时执行的操作
                handleDevToolsOpen();
            }
            
            // 同时检测窗口尺寸差异，这是一种更可靠的检测方法
            const widthDiff = window.outerWidth - window.innerWidth;
            const heightDiff = window.outerHeight - window.innerHeight;
            if (widthDiff > 160 || heightDiff > 160) {
                handleDevToolsOpen();
            }
        }, 1000);

        // 禁用控制台输出
        console.log = console.warn = console.error = console.debug = console.info = function() {};
    })();

    // 防止通过 debug 功能调试
    setInterval(function() {
        debugger;
    }, 100);
    
    // 处理检测到开发者工具时的操作
    function handleDevToolsOpen() {
        // 尝试关闭页面
        try { window.close(); } catch(e) {}
        
        // 尝试跳转
        try { window.location.href = 'about:blank'; } catch(e) {}
        
        // 显示警告
        try {
            document.documentElement.innerHTML = '<html><head><title>页面保护</title></head><body style="background:#000;color:#fff;text-align:center;padding-top:200px;"><h1>页面已被保护</h1><p>检测到调试工具，访问被拒绝</p></body></html>';
        } catch(e) {}
    }
    
    // 根据配置级别启用额外保护
    if (debugLevel === 'all') {
        // 禁用选择、复制等
        ['selectstart', 'copy', 'cut', 'paste', 'dragstart'].forEach(event => {
            document.addEventListener(event, function(e) {
                e.preventDefault();
                e.stopPropagation();
                return false;
            }, true);
        });
        
        // 添加保护样式
        const style = document.createElement('style');
        style.textContent = `
            * {
                -webkit-user-select: none !important;
                -moz-user-select: none !important;
                -ms-user-select: none !important;
                user-select: none !important;
            }
            input, textarea {
                -webkit-user-select: text !important;
                -moz-user-select: text !important;
                -ms-user-select: text !important;
                user-select: text !important;
            }
        `;
        
        try {
            document.head.appendChild(style);
        } catch(e) {
            document.documentElement.appendChild(style);
        }
    }
})();
