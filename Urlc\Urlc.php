<?php

namespace plugin\Urlc;
 
use app\common\library\Plugin;
use app\common\model\DwzApi as DwzApiModel;
use app\common\service\HttpService;

class Urlc extends Plugin {

    /**
     * 插件安装方法
     * @return bool
     */
    public function install() {
        $model = DwzApiModel::where(['code' => 'Urlc'])->find();
        if (!$model) {
            $model = new DwzApiModel();
        }
        $model->code = 'Urlc';
        $model->name = 'Urlc短连接';
        $model->tips = '';
        $model->website = 'https://www.urlc.cn/';
        $model->save();

        return true;
    }

    /**
     * 插件卸载方法
     * @return bool
     */
    public function uninstall() {
        $model = DwzApiModel::where(['code' => 'Urlc'])->find();
        if ($model) {
            $model->delete();
        }
        return true;
    }

    public function create($url) {
        $key = plugconf('Urlc.key');

        $res = HttpService::post(
                "https://www.urlc.cn/api/url/add",
                ['url' => $url],
                ['headers' => ['Authorization: Token ' . $key, "Content-Type: application/json",]]);
        if ($res === false) {
            record_system_log("Urlc短链接生成失败：" . $res);
            return false;
        }
        $json = json_decode($res);
        if (!$json) {
            record_system_log("Urlc短链接生成失败：" . $res);
            return false;
        }
        if ($json->error == 1) {
            record_system_log("Urlc短链接生成失败：" . $res);
            return false;
        }
        return $json->short;
    }
}
