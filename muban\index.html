<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CyberCard - 虚拟卡密交易平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(180deg, #0a1428 0%, #1a2f5a 50%, #2d4a7a 100%);
            color: #ffffff;
            overflow-x: hidden;
        }

        /* 波浪海背景 */
        .wave-ocean {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        /* 波浪层 */
        .wave {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 200%;
            height: 100px;
            background: linear-gradient(90deg, 
                rgba(0, 212, 255, 0.1) 0%, 
                rgba(0, 150, 255, 0.15) 25%, 
                rgba(0, 212, 255, 0.1) 50%, 
                rgba(0, 100, 200, 0.2) 75%, 
                rgba(0, 212, 255, 0.1) 100%);
            border-radius: 1000px 1000px 0 0;
            animation: wave-animation 15s ease-in-out infinite;
        }

        .wave:nth-child(1) {
            bottom: 0;
            height: 120px;
            opacity: 0.8;
            animation-delay: 0s;
            background: linear-gradient(90deg, 
                rgba(0, 212, 255, 0.15) 0%, 
                rgba(0, 180, 255, 0.2) 50%, 
                rgba(0, 212, 255, 0.15) 100%);
        }

        .wave:nth-child(2) {
            bottom: 10px;
            height: 100px;
            opacity: 0.6;
            animation-delay: -5s;
            animation-duration: 18s;
            background: linear-gradient(90deg, 
                rgba(255, 107, 157, 0.1) 0%, 
                rgba(255, 107, 157, 0.15) 50%, 
                rgba(255, 107, 157, 0.1) 100%);
        }

        .wave:nth-child(3) {
            bottom: 20px;
            height: 80px;
            opacity: 0.4;
            animation-delay: -10s;
            animation-duration: 20s;
            background: linear-gradient(90deg, 
                rgba(0, 255, 200, 0.08) 0%, 
                rgba(0, 255, 200, 0.12) 50%, 
                rgba(0, 255, 200, 0.08) 100%);
        }

        .wave:nth-child(4) {
            bottom: 30px;
            height: 60px;
            opacity: 0.3;
            animation-delay: -2s;
            animation-duration: 22s;
            background: linear-gradient(90deg, 
                rgba(255, 215, 61, 0.06) 0%, 
                rgba(255, 215, 61, 0.1) 50%, 
                rgba(255, 215, 61, 0.06) 100%);
        }

        @keyframes wave-animation {
            0% { transform: translateX(0) translateY(0); }
            25% { transform: translateX(-25%) translateY(-10px); }
            50% { transform: translateX(-50%) translateY(0); }
            75% { transform: translateX(-75%) translateY(-5px); }
            100% { transform: translateX(-100%) translateY(0); }
        }

        /* 海洋泡泡效果 */
        .bubble {
            position: absolute;
            border-radius: 50%;
            background: radial-gradient(circle at 30% 30%, 
                rgba(255, 255, 255, 0.8), 
                rgba(0, 212, 255, 0.3), 
                rgba(0, 212, 255, 0.1));
            animation: bubble-float 8s infinite ease-in-out;
            opacity: 0;
        }

        .bubble:nth-child(odd) {
            background: radial-gradient(circle at 30% 30%, 
                rgba(255, 255, 255, 0.6), 
                rgba(255, 107, 157, 0.3), 
                rgba(255, 107, 157, 0.1));
        }

        @keyframes bubble-float {
            0% { 
                transform: translateY(100vh) scale(0);
                opacity: 0;
            }
            10% { 
                opacity: 1;
                transform: translateY(90vh) scale(1);
            }
            90% { 
                opacity: 1;
                transform: translateY(-10vh) scale(1);
            }
            100% { 
                transform: translateY(-20vh) scale(0);
                opacity: 0;
            }
        }

        /* 海洋光线效果 */
        .ocean-light {
            position: absolute;
            width: 2px;
            height: 100%;
            background: linear-gradient(180deg, 
                transparent 0%, 
                rgba(0, 212, 255, 0.3) 20%, 
                rgba(0, 212, 255, 0.1) 40%, 
                transparent 60%);
            animation: light-sway 12s ease-in-out infinite;
        }

        .light-1 {
            left: 20%;
            animation-delay: 0s;
        }

        .light-2 {
            left: 50%;
            animation-delay: -4s;
            background: linear-gradient(180deg, 
                transparent 0%, 
                rgba(255, 107, 157, 0.2) 20%, 
                rgba(255, 107, 157, 0.1) 40%, 
                transparent 60%);
        }

        .light-3 {
            right: 25%;
            animation-delay: -8s;
            background: linear-gradient(180deg, 
                transparent 0%, 
                rgba(0, 255, 200, 0.2) 20%, 
                rgba(0, 255, 200, 0.1) 40%, 
                transparent 60%);
        }

        @keyframes light-sway {
            0%, 100% { 
                transform: translateX(0) rotate(0deg);
                opacity: 0.3;
            }
            25% { 
                transform: translateX(10px) rotate(1deg);
                opacity: 0.6;
            }
            50% { 
                transform: translateX(-5px) rotate(-0.5deg);
                opacity: 0.4;
            }
            75% { 
                transform: translateX(8px) rotate(0.8deg);
                opacity: 0.5;
            }
        }

        /* 浮动几何元素（保留部分） */
        .floating-element {
            position: absolute;
            border-radius: 50%;
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.2);
            animation: float-gentle 20s infinite ease-in-out;
        }

        .element-1 {
            width: 60px;
            height: 60px;
            top: 15%;
            left: 15%;
            animation-delay: 0s;
        }

        .element-2 {
            width: 40px;
            height: 40px;
            top: 70%;
            right: 20%;
            animation-delay: -7s;
            background: rgba(255, 107, 157, 0.1);
            border-color: rgba(255, 107, 157, 0.2);
        }

        @keyframes float-gentle {
            0%, 100% { 
                transform: translateY(0px) translateX(0px);
                opacity: 0.3;
            }
            25% { 
                transform: translateY(-20px) translateX(10px);
                opacity: 0.6;
            }
            50% { 
                transform: translateY(-5px) translateX(-8px);
                opacity: 0.4;
            }
            75% { 
                transform: translateY(15px) translateX(5px);
                opacity: 0.5;
            }
        }

        /* 导航栏 */
        .navbar {
            position: fixed;
            top: 0;
            width: 100%;
            background: rgba(10, 20, 40, 0.9);
            backdrop-filter: blur(15px);
            padding: 1rem 2rem;
            z-index: 1000;
            border-bottom: 1px solid rgba(0, 212, 255, 0.2);
        }

        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: bold;
            color: #00d4ff;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            color: #ffffff;
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-links a:hover {
            color: #00d4ff;
            text-shadow: 0 0 8px rgba(0, 212, 255, 0.6);
        }

        .nav-links a::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, #00d4ff, #ff6b9d);
            transition: width 0.3s ease;
        }

        .nav-links a:hover::after {
            width: 100%;
        }

        /* 英雄区域 */
        .hero {
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            position: relative;
        }

        .hero-content h1 {
            font-size: 4rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #00d4ff, #ff6b9d, #00ffc8);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: ocean-glow 4s ease-in-out infinite alternate;
        }

        @keyframes ocean-glow {
            from { 
                filter: drop-shadow(0 0 15px rgba(0, 212, 255, 0.4));
                transform: scale(1);
            }
            to { 
                filter: drop-shadow(0 0 25px rgba(0, 255, 200, 0.4));
                transform: scale(1.02);
            }
        }

        .hero-content p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            color: #e8f4f8;
        }

        .cta-button {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(45deg, #00d4ff, #ff6b9d);
            color: #fff;
            text-decoration: none;
            border-radius: 30px;
            font-weight: bold;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 212, 255, 0.3);
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 30px rgba(0, 212, 255, 0.4);
        }

        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s;
        }

        .cta-button:hover::before {
            left: 100%;
        }

        /* 产品区域 */
        .products {
            padding: 100px 2rem;
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
        }

        .section-title {
            text-align: center;
            font-size: 3rem;
            margin-bottom: 3rem;
            color: #00d4ff;
            text-shadow: 0 0 15px rgba(0, 212, 255, 0.4);
        }

        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .product-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .product-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, rgba(0, 212, 255, 0.08), rgba(255, 107, 157, 0.08));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .product-card:hover::before {
            opacity: 1;
        }

        .product-card:hover {
            transform: translateY(-10px);
            border-color: #00d4ff;
            box-shadow: 0 20px 40px rgba(0, 212, 255, 0.2);
        }

        .product-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #ff6b9d;
        }

        .product-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #00d4ff;
        }

        .product-card p {
            opacity: 0.8;
            line-height: 1.6;
            color: #e8f4f8;
        }

        .price {
            font-size: 1.5rem;
            font-weight: bold;
            color: #00ffc8;
            margin-top: 1rem;
        }

        /* 功能区域 */
        .features {
            padding: 100px 2rem;
            background: rgba(0, 0, 0, 0.2);
            position: relative;
        }

        .features-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .feature-item {
            text-align: center;
            padding: 2rem;
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 1rem;
            background: linear-gradient(45deg, #00d4ff, #ff6b9d);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: #fff;
        }

        .feature-item h3 {
            font-size: 1.3rem;
            margin-bottom: 1rem;
            color: #00d4ff;
        }

        .feature-item p {
            color: #e8f4f8;
            opacity: 0.9;
        }

        /* 联系区域 */
        .contact {
            padding: 100px 2rem;
            text-align: center;
            position: relative;
        }

        .contact-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .contact-form {
            display: grid;
            gap: 1rem;
            margin-top: 2rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group input,
        .form-group textarea {
            padding: 15px;
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 10px;
            color: #fff;
            font-size: 1rem;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #00d4ff;
            box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
        }

        .submit-btn {
            padding: 15px 30px;
            background: linear-gradient(45deg, #00d4ff, #ff6b9d);
            color: #fff;
            border: none;
            border-radius: 30px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .submit-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 212, 255, 0.4);
        }

        /* 页脚 */
        .footer {
            background: rgba(10, 20, 40, 0.9);
            padding: 2rem;
            text-align: center;
            border-top: 1px solid rgba(0, 212, 255, 0.2);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .hero-content h1 {
                font-size: 2.5rem;
            }
            
            .nav-links {
                display: none;
            }
            
            .section-title {
                font-size: 2rem;
            }
            
            .product-grid {
                grid-template-columns: 1fr;
            }

            .wave {
                height: 60px;
            }

            .wave:nth-child(1) {
                height: 80px;
            }
        }

        /* 滚动动画 */
        .fade-in {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }
    </style>
</head>
<body>
    <!-- 波浪海背景 -->
    <div class="wave-ocean">
        <!-- 多层波浪 -->
        <div class="wave"></div>
        <div class="wave"></div>
        <div class="wave"></div>
        <div class="wave"></div>
        
        <!-- 海洋光线 -->
        <div class="ocean-light light-1"></div>
        <div class="ocean-light light-2"></div>
        <div class="ocean-light light-3"></div>
        
        <!-- 浮动元素 -->
        <div class="floating-element element-1"></div>
        <div class="floating-element element-2"></div>
    </div>

    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="logo">CyberCard</div>
            <ul class="nav-links">
                <li><a href="#home">首页</a></li>
                <li><a href="#products">产品</a></li>
                <li><a href="#features">功能</a></li>
                <li><a href="#contact">联系</a></li>
            </ul>
        </div>
    </nav>

    <!-- 英雄区域 -->
    <section id="home" class="hero">
        <div class="hero-content">
            <h1>CyberCard</h1>
            <p>专业的虚拟卡密交易平台</p>
            <p>安全 · 快速 · 可靠</p>
            <a href="#products" class="cta-button">立即体验</a>
        </div>
    </section>

    <!-- 产品区域 -->
    <section id="products" class="products">
        <h2 class="section-title fade-in">热门产品</h2>
        <div class="product-grid">
            <div class="product-card fade-in">
                <div class="product-icon">🎮</div>
                <h3>游戏卡密</h3>
                <p>Steam、Epic、Origin等平台游戏激活码，正版保障，即买即用</p>
                <div class="price">¥29.99起</div>
            </div>
            <div class="product-card fade-in">
                <div class="product-icon">💳</div>
                <h3>充值卡密</h3>
                <p>各大平台充值卡，支持微信、支付宝、QQ等多种充值方式</p>
                <div class="price">¥9.99起</div>
            </div>
            <div class="product-card fade-in">
                <div class="product-icon">🎵</div>
                <h3>会员卡密</h3>
                <p>音乐、视频、软件会员激活码，享受VIP特权服务</p>
                <div class="price">¥19.99起</div>
            </div>
            <div class="product-card fade-in">
                <div class="product-icon">🛡️</div>
                <h3>安全软件</h3>
                <p>杀毒软件、VPN服务等安全工具激活码，保护您的数字生活</p>
                <div class="price">¥39.99起</div>
            </div>
            <div class="product-card fade-in">
                <div class="product-icon">☁️</div>
                <h3>云服务</h3>
                <p>云存储、云计算服务激活码，扩展您的数字空间</p>
                <div class="price">¥49.99起</div>
            </div>
            <div class="product-card fade-in">
                <div class="product-icon">🎨</div>
                <h3>创意软件</h3>
                <p>Adobe、Office等专业软件激活码，释放您的创造力</p>
                <div class="price">¥99.99起</div>
            </div>
        </div>
    </section>

    <!-- 功能区域 -->
    <section id="features" class="features">
        <div class="features-container">
            <h2 class="section-title fade-in">平台优势</h2>
            <div class="features-grid">
                <div class="feature-item fade-in">
                    <div class="feature-icon">⚡</div>
                    <h3>极速交付</h3>
                    <p>自动化系统，支付成功后秒级发货，无需等待</p>
                </div>
                <div class="feature-item fade-in">
                    <div class="feature-icon">🔒</div>
                    <h3>安全保障</h3>
                    <p>银行级加密技术，多重安全验证，保护您的交易安全</p>
                </div>
                <div class="feature-item fade-in">
                    <div class="feature-icon">💎</div>
                    <h3>正版保证</h3>
                    <p>所有卡密均为正版授权，假一赔十，品质有保障</p>
                </div>
                <div class="feature-item fade-in">
                    <div class="feature-icon">🎯</div>
                    <h3>精准匹配</h3>
                    <p>智能推荐系统，根据您的需求推荐最适合的产品</p>
                </div>
                <div class="feature-item fade-in">
                    <div class="feature-icon">🌍</div>
                    <h3>全球服务</h3>
                    <p>支持全球多地区卡密，满足不同地区用户需求</p>
                </div>
                <div class="feature-item fade-in">
                    <div class="feature-icon">📞</div>
                    <h3>24/7客服</h3>
                    <p>专业客服团队全天候在线，随时为您解决问题</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 联系区域 -->
    <section id="contact" class="contact">
        <div class="contact-container">
            <h2 class="section-title fade-in">联系我们</h2>
            <p class="fade-in">有任何问题或建议？我们很乐意听到您的声音</p>
            <form class="contact-form fade-in">
                <div class="form-group">
                    <input type="text" placeholder="您的姓名" required>
                </div>
                <div class="form-group">
                    <input type="email" placeholder="您的邮箱" required>
                </div>
                <div class="form-group">
                    <input type="text" placeholder="主题" required>
                </div>
                <div class="form-group">
                    <textarea rows="5" placeholder="您的消息" required></textarea>
                </div>
                <button type="submit" class="submit-btn">发送消息</button>
            </form>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer">
        <p>&copy; 2024 CyberCard. 保留所有权利。</p>
        <p>安全 · 可靠 · 专业的虚拟卡密交易平台</p>
    </footer>

    <script>
        // 动态创建海洋泡泡
        function createBubbles() {
            const ocean = document.querySelector('.wave-ocean');
            
            for (let i = 0; i < 20; i++) {
                const bubble = document.createElement('div');
                bubble.className = 'bubble';
                
                const size = Math.random() * 15 + 5;
                bubble.style.width = size + 'px';
                bubble.style.height = size + 'px';
                bubble.style.left = Math.random() * 100 + '%';
                bubble.style.animationDelay = Math.random() * 8 + 's';
                bubble.style.animationDuration = (Math.random() * 4 + 6) + 's';
                
                ocean.appendChild(bubble);
            }
        }

        // 滚动动画
        function animateOnScroll() {
            const elements = document.querySelectorAll('.fade-in');
            elements.forEach(element => {
                const elementTop = element.getBoundingClientRect().top;
                const elementVisible = 150;
                
                if (elementTop < window.innerHeight - elementVisible) {
                    element.classList.add('visible');
                }
            });
        }

        // 初始化
        window.addEventListener('load', () => {
            createBubbles();
            animateOnScroll();
        });

        window.addEventListener('scroll', animateOnScroll);

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 表单提交
        document.querySelector('.contact-form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('感谢您的消息！我们会尽快回复您。');
            this.reset();
        });

        // 鼠标波纹效果
        document.addEventListener('click', function(e) {
            const ripple = document.createElement('div');
            ripple.style.cssText = `
                position: fixed;
                width: 20px;
                height: 20px;
                background: radial-gradient(circle, rgba(0, 212, 255, 0.6), transparent);
                border-radius: 50%;
                pointer-events: none;
                z-index: 9999;
                left: ${e.clientX - 10}px;
                top: ${e.clientY - 10}px;
                animation: ripple-effect 1s ease-out forwards;
            `;
            document.body.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 1000);
        });

        // 添加波纹动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple-effect {
                0% { 
                    opacity: 0.8; 
                    transform: scale(1); 
                }
                100% { 
                    opacity: 0; 
                    transform: scale(8); 
                }
            }
        `;
        document.head.appendChild(style);

        // 定期清理泡泡，避免内存泄漏
        setInterval(() => {
            const bubbles = document.querySelectorAll('.bubble');
            if (bubbles.length > 30) {
                for (let i = 0; i < 10; i++) {
                    if (bubbles[i]) {
                        bubbles[i].remove();
                    }
                }
            }
        }, 10000);
    </script>
</body>
</html>