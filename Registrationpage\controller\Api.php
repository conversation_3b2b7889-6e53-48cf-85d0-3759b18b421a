<?php

namespace plugin\Registrationpage\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;

class Api extends BasePlugin {

    protected $scene = ['admin'];
    protected $noNeedLogin = ['getOptions','saveAnswer'];

    public function index() {
        return View::fetch();
    }

    /**
     * 安全过滤XSS，同时保留Markdown语法
     * @param string $text 需要过滤的文本
     * @return string 过滤后的安全文本
     */
    protected function xss_safe($text) {
        // 允许的HTML标签和属性
        $allowed_tags = [
            'h1' => ['style'], 
            'h2' => ['style'], 
            'h3' => ['style'], 
            'h4' => ['style'], 
            'h5' => ['style'], 
            'h6' => ['style'],
            'p' => ['style'], 
            'div' => ['style'], 
            'span' => ['style'], 
            'a' => ['href', 'title', 'target', 'rel', 'style'],
            'img' => ['src', 'alt', 'title', 'width', 'height', 'style'],
            'hr' => [],
            'br' => [],
            'ul' => ['style'], 
            'ol' => ['style'], 
            'li' => ['style'],
            'strong' => [], 
            'em' => [],
            'u' => [],
            'code' => ['class'],
            'pre' => ['class'],
            'blockquote' => ['style'],
            'table' => ['width', 'border', 'style'],
            'thead' => ['style'],
            'tbody' => ['style'],
            'tr' => ['style'],
            'td' => ['style', 'colspan', 'rowspan'],
            'th' => ['style', 'colspan', 'rowspan']
        ];
        
        // 使用 HTML Purifier 或类似库进行安全过滤
        // 这里简化处理，仅作示例，生产环境建议使用专业的XSS过滤库
        $dom = new \DOMDocument();
        @$dom->loadHTML('<div>'.$text.'</div>', LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        
        // 移除不安全的标签和属性
        $this->cleanupNodes($dom, $allowed_tags);
        
        $html = $dom->saveHTML();
        // 修复loadHTML可能添加的额外标签
        $html = preg_replace('/<\/?html>|<\/?body>|<!DOCTYPE.*?>/', '', $html);
        $html = preg_replace('/<div>(.*)<\/div>/s', '$1', $html);
        
        return trim($html);
    }
    
    /**
     * 递归清理DOM节点
     */
    private function cleanupNodes($dom, $allowed_tags, $node = null) {
        if ($node === null) {
            $node = $dom;
        }
        
        $nodesToRemove = [];
        
        foreach ($node->childNodes as $child) {
            if ($child->nodeType === XML_ELEMENT_NODE) {
                $tag = strtolower($child->nodeName);
                
                // 检查标签是否允许
                if (!isset($allowed_tags[$tag])) {
                    $nodesToRemove[] = $child;
                    continue;
                }
                
                // 检查属性是否允许
                if (!empty($allowed_tags[$tag])) {
                    $attributes = [];
                    foreach ($child->attributes as $attr) {
                        if (!in_array($attr->nodeName, $allowed_tags[$tag])) {
                            $attributes[] = $attr->nodeName;
                        }
                    }
                    
                    foreach ($attributes as $attr) {
                        $child->removeAttribute($attr);
                    }
                }
                
                // 递归处理子节点
                $this->cleanupNodes($dom, $allowed_tags, $child);
            }
        }
        
        // 移除不安全的节点
        foreach ($nodesToRemove as $nodeToRemove) {
            $node->removeChild($nodeToRemove);
        }
    }

    // 获取配置数据
    public function fetchData() {
        try {
            $data = [
                'survey_status' => intval(plugconf("Registrationpage.survey_status") ?? 0),
                'show_close_button' => intval(plugconf("Registrationpage.show_close_button") ?? 1),
                'survey_options' => json_decode(plugconf("Registrationpage.survey_options") ?? '[]', true),
                'survey_title' => plugconf("Registrationpage.survey_title") ?? '嗨，您为什么要注册发卡网ID:',
                'button_text' => plugconf("Registrationpage.button_text") ?? 'OK 我已选好',
                'custom_messages' => json_decode(plugconf("Registrationpage.custom_messages") ?? '[]', true)
            ];
            
            return json(['code' => 1, 'msg' => '获取成功', 'data' => $data]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    // 保存配置
    public function saveConfig() {
        try {
            // 直接获取整个请求体
            $postData = $this->request->post();
            
            // 记录原始数据
            error_log("接收到的原始数据：" . json_encode($postData, JSON_UNESCAPED_UNICODE));
            
            // 解析数据
            $status = isset($postData['status']) ? intval($postData['status']) : 0;
            $showCloseButton = isset($postData['showCloseButton']) ? intval($postData['showCloseButton']) : 1;
            $options = isset($postData['options']) ? $postData['options'] : [];
            $title = isset($postData['title']) ? $postData['title'] : '';
            $buttonText = isset($postData['buttonText']) ? $postData['buttonText'] : '';
            $customMessages = isset($postData['customMessages']) ? $postData['customMessages'] : [];
            
            // 验证数据
            if (empty($options)) {
                return json(['code' => 0, 'msg' => '至少需要添加一个选项']);
            }

            if (empty($title)) {
                return json(['code' => 0, 'msg' => '问卷标题不能为空']);
            }

            if (empty($buttonText)) {
                return json(['code' => 0, 'msg' => '按钮文字不能为空']);
            }
            
            // 处理所有协议内容，应用XSS过滤
            foreach ($customMessages as $index => &$message) {
                if (isset($message['agreement'])) {
                    // 确保协议内容是UTF-8编码的纯文本，不是HTML实体
                    $agreement = $message['agreement'];
                    
                    // 记录原始协议内容（用于调试）
                    error_log("保存前的原始协议内容 [选项 $index] 前40字符: " . substr($agreement, 0, 40));
                    
                    // 首先解码任何HTML实体，确保我们有原始文本
                    $agreement = html_entity_decode($agreement, ENT_QUOTES | ENT_HTML5, 'UTF-8');
                    
                    // 检测并修复编码问题
                    if (!mb_check_encoding($agreement, 'UTF-8')) {
                        // 尝试检测实际编码
                        $detectedEncoding = mb_detect_encoding($agreement, ['ASCII', 'UTF-8', 'GB2312', 'GBK', 'BIG5'], true);
                        if ($detectedEncoding) {
                            error_log("检测到协议内容编码为: " . $detectedEncoding);
                            $agreement = mb_convert_encoding($agreement, 'UTF-8', $detectedEncoding);
                        } else {
                            // 无法检测编码时的兜底处理
                            error_log("无法检测协议内容编码，尝试强制转换");
                            $agreement = mb_convert_encoding($agreement, 'UTF-8', 'auto');
                        }
                    }
                    
                    // 标准化换行符
                    $agreement = str_replace(["\r\n", "\r"], "\n", $agreement);
                    
                    // 使用更简单的方法过滤危险标签，保留基本Markdown格式
                    $message['agreement'] = $this->simple_markdown_safe($agreement);
                    
                    // 记录处理后的协议内容（用于调试）
                    error_log("保存后的处理协议内容 [选项 $index] 前40字符: " . substr($message['agreement'], 0, 40));
                }
            }

            // 保存配置 - 使用JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES确保中文和斜杠正常存储
            plugconf("Registrationpage.survey_status", $status);
            plugconf("Registrationpage.show_close_button", $showCloseButton);
            plugconf("Registrationpage.survey_options", json_encode($options, JSON_UNESCAPED_UNICODE));
            plugconf("Registrationpage.survey_title", $title);
            plugconf("Registrationpage.button_text", $buttonText);
            plugconf("Registrationpage.custom_messages", json_encode($customMessages, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
            
            // 返回统一格式
            return json(['code' => 1, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            // 记录详细错误
            error_log("保存配置失败：" . $e->getMessage() . "\n" . $e->getTraceAsString());
            return json(['code' => 0, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 简单的Markdown安全处理，专为协议内容设计
     * 允许基本的Markdown语法，过滤危险HTML标签
     */
    protected function simple_markdown_safe($text) {
        // 先移除所有HTML标签，只保留纯文本
        $text = strip_tags($text);
        
        // 移除可能的XSS脚本标记
        $text = str_replace(['javascript:', 'onclick=', 'onerror=', '<script', '</script>'], '', $text);
        
        // 仅编码 < 和 > 字符，保留其他字符原样
        $text = str_replace(['<', '>'], ['&lt;', '&gt;'], $text);
        
        return $text;
    }

    // 获取选项（供前端调用）
    public function getOptions() {
        try {
            $options = json_decode(plugconf("Registrationpage.survey_options") ?? '[]', true);
            $title = plugconf("Registrationpage.survey_title") ?? '嗨，您为什么要注册发卡网ID:';
            $buttonText = plugconf("Registrationpage.button_text") ?? 'OK 我已选好';
            $customMessages = json_decode(plugconf("Registrationpage.custom_messages") ?? '[]', true);
            $showCloseButton = intval(plugconf("Registrationpage.show_close_button") ?? 1);
            
            // 处理所有协议内容
            foreach ($customMessages as $index => &$message) {
                if (isset($message['agreement'])) {
                    $agreement = $message['agreement'];
                    
                    // 记录原始协议内容
                    error_log("读取的原始协议内容 [选项 $index] 前40字符: " . substr($agreement, 0, 40));
                    
                    // 解码任何HTML实体，确保是原始文本
                    if (strpos($agreement, '&') !== false && strpos($agreement, ';') !== false) {
                        $decoded = html_entity_decode($agreement, ENT_QUOTES | ENT_HTML5, 'UTF-8');
                        // 仅当解码结果不同时才使用解码结果
                        if ($decoded !== $agreement) {
                            error_log("协议内容包含HTML实体，已解码");
                            $agreement = $decoded;
                        }
                    }
                    
                    // 检测和修复编码问题
                    if (!mb_check_encoding($agreement, 'UTF-8')) {
                        $detectedEncoding = mb_detect_encoding($agreement, ['ASCII', 'UTF-8', 'GB2312', 'GBK', 'BIG5'], true);
                        if ($detectedEncoding) {
                            error_log("检测到协议内容编码为: " . $detectedEncoding);
                            $agreement = mb_convert_encoding($agreement, 'UTF-8', $detectedEncoding);
                        } else {
                            $agreement = mb_convert_encoding($agreement, 'UTF-8', 'auto');
                        }
                    }
                    
                    // 标准化换行符和清理多余空行
                    $agreement = str_replace(["\r\n", "\r"], "\n", $agreement);
                    $agreement = preg_replace('/\n{3,}/', "\n\n", $agreement);
                    
                    // 如果是HTML实体编码的文本，尝试解码
                    if (preg_match('/&[a-zA-Z0-9#]+;/', $agreement)) {
                        error_log("尝试解码HTML实体");
                        $decoded = html_entity_decode($agreement, ENT_QUOTES | ENT_HTML5, 'UTF-8');
                        
                        // 检查解码后是否更接近中文原文
                        $chineseCount = preg_match_all('/[\x{4e00}-\x{9fa5}]/u', $decoded, $matches);
                        $originalChineseCount = preg_match_all('/[\x{4e00}-\x{9fa5}]/u', $agreement, $originalMatches);
                        
                        if ($chineseCount > $originalChineseCount) {
                            $agreement = $decoded;
                            error_log("HTML实体解码成功，发现更多中文字符");
                        }
                    }
                    
                    // 处理乱码特征字符序列
                    if (preg_match('/(\&\#[0-9]+\;)|(\&[a-z]+\;)/', $agreement) || 
                        strpos($agreement, 'ç') !== false || 
                        strpos($agreement, 'æ') !== false || 
                        strpos($agreement, 'å') !== false) {
                        
                        error_log("检测到协议内容可能包含乱码，尝试修复");
                        
                        // 尝试多种解码方法
                        $tests = [
                            // 方法1: 标准HTML实体解码
                            html_entity_decode($agreement, ENT_QUOTES | ENT_HTML5, 'UTF-8'),
                            
                            // 方法2: 通过Latin1中转
                            mb_convert_encoding(mb_convert_encoding($agreement, 'ISO-8859-1', 'UTF-8'), 'UTF-8', 'ISO-8859-1'),
                            
                            // 方法3: 强制双重UTF-8解码
                            mb_convert_encoding($agreement, 'UTF-8', 'UTF-8'),
                            
                            // 方法4: URL解码+实体解码 (处理可能的双重编码)
                            html_entity_decode(urldecode($agreement), ENT_QUOTES | ENT_HTML5, 'UTF-8'),
                        ];
                        
                        // 选择含有最多中文字符的结果
                        $bestIndex = 0;
                        $maxChineseCount = 0;
                        
                        foreach($tests as $i => $testText) {
                            $chineseCount = preg_match_all('/[\x{4e00}-\x{9fa5}]/u', $testText, $matches);
                            if ($chineseCount > $maxChineseCount) {
                                $maxChineseCount = $chineseCount;
                                $bestIndex = $i;
                            }
                        }
                        
                        // 如果有更好的结果，使用它
                        if ($maxChineseCount > preg_match_all('/[\x{4e00}-\x{9fa5}]/u', $agreement, $matches)) {
                            $agreement = $tests[$bestIndex];
                            error_log("使用修复方法 #" . ($bestIndex + 1) . " 成功修复了乱码");
                        }
                    }
                    
                    $message['agreement'] = $agreement;
                    
                    error_log("最终处理后的协议内容 [选项 $index] 前40字符: " . substr($agreement, 0, 40));
                }
            }
            
            return json([
                'code' => 200, 
                'msg' => '获取成功', 
                'data' => [
                    'options' => $options,
                    'title' => $title,
                    'buttonText' => $buttonText,
                    'customMessages' => $customMessages,
                    'showCloseButton' => $showCloseButton
                ]
            ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT);
        } catch (\Exception $e) {
            error_log("获取选项失败: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return json(['code' => 500, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    // 添加/编辑选项
    public function saveOption() {
        try {
            $data = input('post.');
            $id = isset($data['id']) ? intval($data['id']) : 0;
            
            $saveData = [
                'option_text' => $data['option_text'],
                'sort' => intval($data['sort'] ?? 0),
                'update_time' => time()
            ];
            
            if ($id) {
                Db::name('registration_options')->where('id', $id)->update($saveData);
            } else {
                $saveData['create_time'] = time();
                Db::name('registration_options')->insert($saveData);
            }
            
            return json(['code' => 200, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    // 删除选项
    public function deleteOption() {
        try {
            $id = input('id/d', 0);
            if ($id) {
                Db::name('registration_options')->where('id', $id)->delete();
            }
            return json(['code' => 200, 'msg' => '删除成功']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }

    // 保存问卷答案
    public function saveAnswer() {
        try {
            $data = input('post.');
            $option = isset($data['option']) ? $data['option'] : '';
            $optionIndex = isset($data['optionIndex']) ? intval($data['optionIndex']) : -1;
            
            // 记录用户选择
            error_log("用户选择了选项: " . $option . ", 索引: " . $optionIndex);
            
            // 读取自定义消息
            $customMessages = json_decode(plugconf("Registrationpage.custom_messages") ?? '[]', true);
            $options = json_decode(plugconf("Registrationpage.survey_options") ?? '[]', true);
            
            // 返回对应的消息和行为
            $response = [
                'code' => 200,
                'msg' => '提交成功',
                'data' => [
                    'option' => $option,
                    'optionIndex' => $optionIndex
                ]
            ];
            
            // 如果有对应的自定义消息，添加到响应中
            if (isset($customMessages[$optionIndex])) {
                // 检查当前选项是消息类型还是协议类型
                if (isset($customMessages[$optionIndex]['message'])) {
                    // 消息类型
                    $response['data']['message'] = $customMessages[$optionIndex]['message'] ?? '';
                    $response['data']['buttonText'] = $customMessages[$optionIndex]['buttonText'] ?? '我知道了';
                    $response['data']['redirectUrl'] = $customMessages[$optionIndex]['redirectUrl'] ?? '';
                    $response['data']['action'] = $customMessages[$optionIndex]['action'] ?? '';
                    
                    // 处理额外的按钮
                    if (isset($customMessages[$optionIndex]['extraButtons']) && 
                        is_array($customMessages[$optionIndex]['extraButtons']) && 
                        !empty($customMessages[$optionIndex]['extraButtons'])) {
                        
                        // 确保只传递必要的属性并过滤可能的安全问题
                        $extraButtons = [];
                        foreach ($customMessages[$optionIndex]['extraButtons'] as $btn) {
                            $extraButtons[] = [
                                'text' => $btn['text'] ?? '按钮',
                                'type' => in_array($btn['type'], ['default', 'primary', 'success', 'warning', 'danger']) ? $btn['type'] : 'default',
                                'url' => $btn['url'] ?? ''
                            ];
                        }
                        
                        $response['data']['extraButtons'] = $extraButtons;
                    }
                } else if (isset($customMessages[$optionIndex]['agreement'])) {
                    // 协议类型 - 不仅限于optionIndex=3
                    // 确保协议内容是有效的UTF-8编码
                    $agreement = $customMessages[$optionIndex]['agreement'];
                    
                    // 更强健的编码检测和修复
                    if (!mb_check_encoding($agreement, 'UTF-8')) {
                        // 尝试检测实际编码并转换
                        $detectedEncoding = mb_detect_encoding($agreement, ['ASCII', 'UTF-8', 'GB2312', 'GBK', 'BIG5'], true);
                        if ($detectedEncoding) {
                            $agreement = mb_convert_encoding($agreement, 'UTF-8', $detectedEncoding);
                        } else {
                            // 如果无法检测，强制转换
                            $agreement = mb_convert_encoding($agreement, 'UTF-8', 'auto');
                        }
                    }
                    
                    // 清理可能导致显示问题的字符
                    $agreement = str_replace(["\r\n", "\r"], "\n", $agreement);
                    $agreement = preg_replace('/\n{3,}/', "\n\n", $agreement);
                    
                    // 处理Unicode转义序列（如\uXXXX）
                    $agreement = preg_replace_callback('/\\\\u([0-9a-fA-F]{4})/', function ($matches) {
                        return json_decode('"' . $matches[0] . '"');
                    }, $agreement);
                    
                    $response['data']['agreement'] = $agreement;
                    
                    // 记录处理后的协议内容以便调试
                    error_log("处理后的协议内容（前20字符）: " . substr($agreement, 0, 20));
                }
            }
            
            // 确保JSON响应使用UTF-8编码，不转义Unicode字符
            return json($response, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        } catch (\Exception $e) {
            error_log("提交问卷答案失败: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return json(['code' => 500, 'msg' => '提交失败：' . $e->getMessage()]);
        }
    }
} 