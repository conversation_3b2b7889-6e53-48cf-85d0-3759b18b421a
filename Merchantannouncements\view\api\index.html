<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <link href="https://unpkg.com/@wangeditor/editor@latest/dist/css/style.css" rel="stylesheet">
    <title>商家登录公告设置</title>
    <style>
        .merchant-announcement-box {
            max-width: 600px !important;
        }
        .merchant-announcement-box .el-message-box__content {
            max-height: 400px;
            overflow-y: auto;
        }
        .editor-container {
            border: 1px solid #dcdfe6;
            margin-bottom: 10px;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        .w-e-toolbar {
            border-bottom: 1px solid #dcdfe6 !important;
            background: #fafafa !important;
            padding: 8px 12px !important;
        }
        .w-e-text-container {
            height: 350px !important;
            background: #fff !important;
        }
        .w-e-text-placeholder {
            color: #c0c4cc !important;
            font-style: italic !important;
        }
        /* 富文本编辑器工具栏按钮样式优化 */
        .w-e-toolbar .w-e-bar-item button {
            border-radius: 4px !important;
            margin: 0 2px !important;
            transition: all 0.2s ease !important;
        }
        .w-e-toolbar .w-e-bar-item button:hover {
            background: #e6f7ff !important;
            color: #1890ff !important;
        }
        .w-e-toolbar .w-e-bar-item button.w-e-active {
            background: #1890ff !important;
            color: white !important;
        }
        /* 编辑器内容区域样式 */
        .w-e-text-container .w-e-scroll {
            padding: 15px !important;
            line-height: 1.6 !important;
            font-size: 14px !important;
        }
        /* 编辑器焦点状态 */
        .editor-container:focus-within {
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
        }
    </style>
    <script>
        // 防止重复加载
        window.announcementLoaded = false;
    </script>
</head>

<body>
    <div id="app">
        <el-card shadow="never">
            <el-form :model="form" label-width="120px">
                <el-form-item label="公告开关：">
                    <el-switch v-model="form.status" :active-value="1" :inactive-value="0" />
                </el-form-item>

                <el-form-item label="弹出频率：">
                    <el-radio-group v-model="form.frequency">
                        <el-radio label="once">仅弹一次</el-radio>
                        <el-radio label="login">每次登录</el-radio>
                        <el-radio label="daily">每天一次</el-radio>
                        <el-radio label="weekly">每周一次</el-radio>
                    </el-radio-group>
                </el-form-item>

                <el-form-item label="朗读开关：">
                    <el-switch 
                        v-model="form.readEnabled"
                        :active-value="1"
                        :inactive-value="0"
                    />
                </el-form-item>

                <el-form-item label="关闭倒计时：">
                    <el-input-number 
                        v-model="form.closeDelay" 
                        :min="0" 
                        :max="60" 
                        :step="1"
                    />
                    <div class="el-form-item-msg" style="margin-top: 8px; color: #666;">
                        设置"我知道了"按钮显示前的倒计时秒数，设置为0则不启用倒计时
                    </div>
                </el-form-item>

                <el-form-item label="编辑模式：">
                    <el-radio-group v-model="form.editMode" @change="handleEditModeChange">
                        <el-radio label="rich">富文本编辑器</el-radio>
                        <el-radio label="html">HTML代码编辑</el-radio>
                    </el-radio-group>
                    <div class="el-form-item-msg" style="margin-top: 8px; color: #666;">
                        富文本编辑器：可视化编辑，适合一般用户；HTML编辑：直接编辑HTML代码，适合高级用户
                    </div>
                </el-form-item>

                <el-form-item label="公告内容：">
                    <!-- 富文本编辑器 -->
                    <div v-if="form.editMode === 'rich'">
                        <div class="editor-container">
                            <div id="editor-toolbar"></div>
                            <div id="editor-text-area"></div>
                        </div>
                        <!-- 富文本编辑器快捷操作 -->
                        <div style="margin-top: 8px; display: flex; gap: 8px; flex-wrap: wrap;">
                            <el-button size="small" type="primary" @click="insertRichTemplate('announcement')">
                                📢 插入公告模板
                            </el-button>
                            <el-button size="small" type="success" @click="insertRichTemplate('notice')">
                                📋 插入通知模板
                            </el-button>
                            <el-button size="small" type="warning" @click="insertRichTemplate('urgent')">
                                🚨 插入紧急通知
                            </el-button>
                            <el-button size="small" type="info" @click="clearRichContent">
                                🗑️ 清空内容
                            </el-button>
                        </div>
                        <div style="margin-top: 4px; color: #909399; font-size: 12px; display: flex; justify-content: space-between; align-items: center;">
                            <span>💡 提示：可使用快捷键 Ctrl+B(粗体)、Ctrl+I(斜体)、Ctrl+U(下划线)、Ctrl+Z(撤销)、Ctrl+Y(重做)</span>
                            <span style="color: #666;">字数统计: {{ richTextLength }}/50000</span>
                        </div>
                    </div>

                    <!-- HTML代码编辑器 -->
                    <div v-if="form.editMode === 'html'">
                        <div style="display: flex; gap: 15px;">
                            <!-- 左侧：HTML编辑器 -->
                            <div style="flex: 1;">
                                <div style="margin-bottom: 8px; font-weight: 500; color: #606266;">HTML代码编辑器</div>
                                <el-input
                                    v-model="form.htmlContent"
                                    type="textarea"
                                    :rows="20"
                                    placeholder="请输入HTML内容，支持所有HTML标签和CSS样式..."
                                    style="font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 14px; line-height: 1.5;"
                                    @input="updateHtmlPreview"
                                />
                                <div style="margin-top: 8px; color: #909399; font-size: 12px; display: flex; justify-content: space-between; align-items: center;">
                                    <span><i class="el-icon-info"></i> 支持完整HTML代码，包括复杂布局、样式和结构，但不支持JavaScript</span>
                                    <span style="color: #666;">字符数: {{ form.htmlContent.length }}</span>
                                </div>
                                <div style="margin-top: 4px; color: #67c23a; font-size: 11px;">
                                    💡 提示：可以直接粘贴完整的HTML代码，支持嵌套标签、内联样式等
                                </div>
                                <div style="margin-top: 8px; display: flex; gap: 8px; flex-wrap: wrap;">
                                    <el-button size="small" type="primary" @click="showPreview = !showPreview">
                                        {{ showPreview ? '隐藏预览' : '显示预览' }}
                                    </el-button>
                                    <el-button size="small" @click="insertSampleHtml">插入示例HTML</el-button>
                                    <el-button size="small" type="info" @click="insertAdvancedSample">插入高级样式</el-button>
                                    <el-button size="small" type="warning" @click="formatHtmlCode">格式化代码</el-button>
                                    <el-button size="small" type="danger" @click="clearHtmlContent">清空内容</el-button>
                                </div>
                            </div>

                            <!-- 右侧：实时预览 -->
                            <div v-if="showPreview" style="flex: 1;">
                                <div style="margin-bottom: 8px; font-weight: 500; color: #606266; display: flex; align-items: center; justify-content: space-between;">
                                    <span>实时预览效果</span>
                                    <el-button size="mini" type="text" @click="refreshPreview">
                                        🔄 刷新预览
                                    </el-button>
                                </div>
                                <div style="border: 1px solid #dcdfe6; border-radius: 6px; background: #f9f9f9; height: 520px; overflow: hidden; display: flex; flex-direction: column;">
                                    <div style="background: #fff; padding: 8px 12px; border-bottom: 1px solid #dcdfe6; font-size: 12px; color: #909399;">
                                        预览窗口 - 实际效果可能因环境而异
                                    </div>
                                    <div style="flex: 1; overflow-y: auto; background: white; padding: 15px;">
                                        <div v-html="htmlPreviewContent" style="min-height: 100px; word-wrap: break-word;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 移动端预览（当隐藏右侧预览时显示） -->
                        <div v-if="!showPreview && form.htmlContent.trim()" style="margin-top: 15px;">
                            <div style="margin-bottom: 8px; font-weight: 500; color: #606266;">快速预览</div>
                            <div style="border: 1px solid #dcdfe6; border-radius: 4px; padding: 15px; background: #f9f9f9; max-height: 300px; overflow-y: auto;">
                                <div v-html="form.htmlContent" style="border: 1px solid #e4e7ed; background: white; padding: 10px; border-radius: 4px; min-height: 100px;"></div>
                            </div>
                        </div>
                    </div>

                    <div class="el-form-item-msg" style="margin-top: 8px; color: #666;">
                        <span v-if="form.editMode === 'rich'">
                            <strong>富文本编辑器功能：</strong>
                            <br>• 支持文字格式化（粗体、斜体、下划线、删除线）
                            <br>• 支持字体、字号、颜色、背景色设置
                            <br>• 支持标题、列表、对齐方式、引用等
                            <br>• 支持插入图片、视频、链接、表格
                            <br>• 支持表情符号和分割线
                        </span>
                        <span v-else>
                            <strong>HTML编辑模式：</strong>直接编辑HTML代码，支持所有HTML标签和CSS样式，但不支持JavaScript
                        </span>
                    </div>
                </el-form-item>



                <el-form-item>
                    <el-button type="primary" :loading="loading" @click="save">保存设置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
    </div>

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="https://unpkg.com/@wangeditor/editor@latest/dist/index.js"></script>

    <script>
        const { createApp, ref, reactive, onMounted, nextTick } = Vue;
        const { ElMessage } = ElementPlus;

        createApp({
            setup() {
                const loading = ref(false);
                const form = reactive({
                    status: 1,
                    frequency: 'once',
                    readEnabled: 1,
                    announcement: '',
                    closeDelay: 0,
                    editMode: 'rich',
                    htmlContent: ''
                });
                let editor = null;
                const showPreview = ref(false);
                const richTextLength = ref(0);
                const htmlPreviewContent = ref('');

                onMounted(() => {
                    fetchData();
                });

                // 获取配置数据
                const fetchData = async () => {
                    try {
                        const res = await axios.post("/plugin/Merchantannouncements/api/fetchData");
                        if (res.data?.code == 200) {
                            const data = res.data.data;
                            form.status = parseInt(data.status);
                            form.frequency = data.frequency;
                            form.readEnabled = parseInt(data.read_enabled);
                            form.closeDelay = parseInt(data.close_delay) || 0;
                            form.editMode = data.edit_mode || 'rich';

                            const announcement = data.announcement || '';
                            form.htmlContent = announcement;

                            // 如果是富文本模式，创建编辑器
                            if (form.editMode === 'rich') {
                                createRichEditor();
                            } else if (form.editMode === 'html') {
                                // 初始化HTML预览
                                htmlPreviewContent.value = form.htmlContent;
                            }
                        }
                    } catch (error) {
                        ElMessage.error('获取数据失败');
                    }
                };

                // 保存配置
                const save = async () => {
                    loading.value = true;
                    try {
                        let htmlContent = '';

                        if (form.editMode === 'rich') {
                            if (!editor) {
                                ElMessage.error('富文本编辑器未初始化');
                                loading.value = false;
                                return;
                            }
                            htmlContent = editor.getHtml();
                        } else {
                            htmlContent = form.htmlContent;
                        }

                        const formData = new FormData();
                        formData.append('status', form.status);
                        formData.append('frequency', form.frequency);
                        formData.append('read_enabled', form.readEnabled);
                        formData.append('close_delay', form.closeDelay);
                        formData.append('edit_mode', form.editMode);
                        formData.append('announcement', htmlContent);

                        const res = await axios.post("/plugin/Merchantannouncements/api/save", formData);
                        if (res.data?.code == 200) {
                            ElMessage.success('保存成功');
                            await fetchData();
                        } else {
                            ElMessage.error(res.data?.msg || '保存失败');
                        }
                    } catch (error) {
                        console.error('保存失败:', error);
                        ElMessage.error('保存失败');
                    }
                    loading.value = false;
                };

                // 创建富文本编辑器
                const createRichEditor = () => {
                    if (editor) {
                        editor.destroy();
                        editor = null;
                    }

                    // 等待DOM更新
                    nextTick(() => {
                        const E = window.wangEditor;
                        editor = E.createEditor({
                            selector: '#editor-text-area',
                            html: form.htmlContent,
                            config: {
                                placeholder: '请输入公告内容，支持富文本格式、图片、表格等...',
                                html: true,
                                preserveContent: true,
                                autoFocus: false,
                                scroll: true,
                                maxLength: 50000,
                                MENU_CONF: {
                                    fontSize: {
                                        fontSizeList: [
                                            '10px', '12px', '14px', '16px', '18px', '20px', '22px', '24px',
                                            '26px', '28px', '30px', '32px', '36px', '40px', '48px'
                                        ]
                                    },
                                    fontFamily: {
                                        fontFamilyList: [
                                            '宋体', '黑体', '楷体', '仿宋', '微软雅黑', 'Arial', 'Tahoma',
                                            'Verdana', 'Times New Roman', 'Courier New', 'Georgia', 'Palatino'
                                        ]
                                    },
                                    lineHeight: {
                                        lineHeightList: ['1', '1.15', '1.2', '1.4', '1.5', '1.6', '1.8', '2', '2.5', '3']
                                    },
                                    color: {
                                        colors: [
                                            '#000000', '#333333', '#666666', '#999999', '#cccccc', '#ffffff',
                                            '#ff0000', '#ff6600', '#ffcc00', '#33cc33', '#0099cc', '#6633cc',
                                            '#cc0066', '#ff3366', '#ff9900', '#99cc00', '#00cccc', '#9966cc',
                                            '#990000', '#cc3300', '#cc9900', '#669900', '#006699', '#663399'
                                        ]
                                    },
                                    bgColor: {
                                        colors: [
                                            '#ffffff', '#f8f9fa', '#e9ecef', '#dee2e6', '#ced4da', '#adb5bd',
                                            '#ffebee', '#fff3e0', '#f3e5f5', '#e8f5e8', '#e3f2fd', '#fce4ec',
                                            '#ffcdd2', '#ffe0b2', '#d1c4e9', '#c8e6c8', '#bbdefb', '#f8bbd9'
                                        ]
                                    },
                                    uploadImage: {
                                        server: '/ajax/upload',
                                        fieldName: 'file',
                                        maxFileSize: 5 * 1024 * 1024,
                                        allowedFileTypes: ['image/*'],
                                        timeout: 30 * 1000,
                                        withCredentials: true,
                                        customInsert(res, insertFn) {
                                            if (res.code === 1 && res.data && res.data.url) {
                                                insertFn(res.data.url, res.data.alt || '图片', res.data.href || '');
                                            } else {
                                                alert('图片上传失败：' + (res.msg || '未知错误'));
                                            }
                                        }
                                    },
                                    insertLink: {
                                        checkLink: (linkText, linkUrl) => {
                                            if (!linkUrl) return '请输入链接地址';
                                            if (!linkUrl.startsWith('http://') && !linkUrl.startsWith('https://') && !linkUrl.startsWith('/')) {
                                                return '请输入有效的链接地址';
                                            }
                                            return true;
                                        }
                                    },
                                    insertTable: {
                                        showTableBorder: true
                                    }
                                }
                            }
                        });

                        const toolbar = E.createToolbar({
                            editor,
                            selector: '#editor-toolbar',
                            config: {
                                toolbarKeys: [
                                    'headerSelect', 'blockquote', '|',
                                    'bold', 'underline', 'italic',
                                    {
                                        key: 'group-more-style',
                                        title: '更多样式',
                                        menuKeys: ['through', 'code', 'sup', 'sub', 'clearStyle'],
                                    },
                                    '|',
                                    'color', 'bgColor', '|',
                                    'fontSize', 'fontFamily', 'lineHeight', '|',
                                    'bulletedList', 'numberedList', 'todo',
                                    {
                                        key: 'group-justify',
                                        title: '对齐方式',
                                        menuKeys: ['justifyLeft', 'justifyCenter', 'justifyRight', 'justifyJustify'],
                                    },
                                    '|',
                                    'insertLink', 'uploadImage', 'insertVideo', 'insertTable', 'divider', '|',
                                    'undo', 'redo', '|', 'fullScreen'
                                ],
                                excludeKeys: []
                            }
                        });

                        // 编辑器内容变化监听
                        editor.on('change', () => {
                            if (form.editMode === 'rich') {
                                form.htmlContent = editor.getHtml();
                                const textContent = editor.getText();
                                richTextLength.value = textContent.length;
                            }
                        });

                        // 初始化字数统计
                        if (form.htmlContent) {
                            const textContent = editor.getText();
                            richTextLength.value = textContent.length;
                        }
                    });
                };

                // 编辑模式切换
                const handleEditModeChange = (mode) => {
                    if (mode === 'html' && editor) {
                        // 从富文本切换到HTML，获取富文本内容
                        form.htmlContent = editor.getHtml();
                        editor.destroy();
                        editor = null;
                    } else if (mode === 'rich') {
                        // 切换到富文本模式，重新创建编辑器
                        createRichEditor();
                    }
                };

                // 更新HTML预览
                const updateHtmlPreview = () => {
                    htmlPreviewContent.value = form.htmlContent;
                };

                // 刷新预览
                const refreshPreview = () => {
                    htmlPreviewContent.value = form.htmlContent;
                    ElMessage.success('预览已刷新');
                };

                // 格式化HTML代码
                const formatHtmlCode = () => {
                    if (!form.htmlContent.trim()) {
                        ElMessage.warning('请先输入HTML内容');
                        return;
                    }

                    try {
                        // 简单的HTML格式化
                        let formatted = form.htmlContent
                            .replace(/></g, '>\n<')
                            .replace(/^\s+|\s+$/gm, '')
                            .split('\n')
                            .map((line, index, array) => {
                                let indent = 0;
                                for (let i = 0; i < index; i++) {
                                    if (array[i].includes('<') && !array[i].includes('</') && !array[i].includes('/>')) {
                                        indent++;
                                    }
                                    if (array[i].includes('</')) {
                                        indent--;
                                    }
                                }
                                if (line.includes('</')) {
                                    indent--;
                                }
                                return '  '.repeat(Math.max(0, indent)) + line.trim();
                            })
                            .join('\n');

                        form.htmlContent = formatted;
                        updateHtmlPreview();
                        ElMessage.success('代码格式化完成');
                    } catch (error) {
                        ElMessage.error('格式化失败，请检查HTML语法');
                    }
                };

                // 清空HTML内容
                const clearHtmlContent = () => {
                    ElMessageBox.confirm('确定要清空所有HTML内容吗？此操作不可撤销。', '确认清空', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        form.htmlContent = '';
                        htmlPreviewContent.value = '';
                        ElMessage.success('内容已清空');
                    }).catch(() => {
                        // 用户取消
                    });
                };

                // 插入示例HTML
                const insertSampleHtml = () => {
                    const sampleHtml = `<div style="padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 10px; color: white; text-align: center;">
    <h2 style="margin: 0 0 15px 0; color: #fff;">🎉 重要公告 🎉</h2>
    <p style="font-size: 16px; line-height: 1.6; margin: 10px 0;">
        欢迎使用我们的平台！为了给您提供更好的服务体验，我们进行了系统升级。
    </p>
    <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; margin: 15px 0;">
        <h3 style="margin: 0 0 10px 0; color: #fff;">✨ 新功能亮点</h3>
        <ul style="text-align: left; margin: 0; padding-left: 20px;">
            <li>界面更加美观易用</li>
            <li>功能更加完善稳定</li>
            <li>性能大幅提升</li>
        </ul>
    </div>
    <p style="font-size: 14px; margin: 15px 0 0 0; opacity: 0.9;">
        如有任何问题，请联系客服。感谢您的支持！
    </p>
</div>`;
                    form.htmlContent = sampleHtml;
                    updateHtmlPreview();
                    ElMessage.success('示例HTML已插入');
                };

                // 插入富文本模板
                const insertRichTemplate = (type) => {
                    if (!editor) {
                        ElMessage.warning('富文本编辑器未初始化');
                        return;
                    }

                    let templateHtml = '';
                    switch (type) {
                        case 'announcement':
                            templateHtml = `
                                <div style="text-align: center; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 10px; margin: 10px 0;">
                                    <h2 style="margin: 0 0 15px 0; color: #fff;">📢 重要公告</h2>
                                    <p style="font-size: 16px; line-height: 1.6; margin: 10px 0;">
                                        尊敬的用户，我们有重要事项需要通知您：
                                    </p>
                                    <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; margin: 15px 0; text-align: left;">
                                        <ul style="margin: 0; padding-left: 20px;">
                                            <li>请在此处添加具体的公告内容</li>
                                            <li>可以添加多个要点说明</li>
                                            <li>感谢您的关注和支持</li>
                                        </ul>
                                    </div>
                                    <p style="font-size: 14px; margin: 15px 0 0 0; opacity: 0.9;">
                                        如有疑问，请联系客服。
                                    </p>
                                </div>
                            `;
                            break;
                        case 'notice':
                            templateHtml = `
                                <div style="border-left: 4px solid #409eff; background: #f0f9ff; padding: 20px; margin: 10px 0; border-radius: 0 8px 8px 0;">
                                    <h3 style="color: #409eff; margin: 0 0 15px 0; font-size: 18px;">
                                        📋 系统通知
                                    </h3>
                                    <p style="margin: 0 0 15px 0; line-height: 1.6; color: #333;">
                                        <strong>通知时间：</strong>${new Date().toLocaleString()}
                                    </p>
                                    <p style="margin: 0 0 15px 0; line-height: 1.6; color: #333;">
                                        <strong>通知内容：</strong>请在此处填写具体的通知内容，可以包含详细的说明和操作指引。
                                    </p>
                                    <div style="background: white; padding: 15px; border-radius: 6px; margin-top: 15px;">
                                        <p style="margin: 0; color: #666; font-size: 14px;">
                                            💡 温馨提示：请仔细阅读以上内容，如有不明之处请及时联系我们。
                                        </p>
                                    </div>
                                </div>
                            `;
                            break;
                        case 'urgent':
                            templateHtml = `
                                <div style="border: 2px solid #ff4757; background: #fff5f5; padding: 20px; margin: 10px 0; border-radius: 8px; position: relative;">
                                    <div style="position: absolute; top: -1px; left: -1px; background: #ff4757; color: white; padding: 4px 12px; font-size: 12px; font-weight: bold; border-radius: 6px 0 6px 0;">
                                        紧急
                                    </div>
                                    <h3 style="color: #ff4757; margin: 20px 0 15px 0; font-size: 20px;">
                                        🚨 紧急通知
                                    </h3>
                                    <div style="background: #ff4757; color: white; padding: 15px; border-radius: 6px; margin: 15px 0;">
                                        <p style="margin: 0; font-weight: bold; font-size: 16px;">
                                            ⚠️ 重要提醒：请立即关注以下内容
                                        </p>
                                    </div>
                                    <p style="margin: 15px 0; line-height: 1.8; color: #333; font-size: 15px;">
                                        请在此处填写紧急通知的具体内容。建议包含：
                                    </p>
                                    <ol style="margin: 15px 0; padding-left: 20px; color: #333;">
                                        <li style="margin-bottom: 8px;">紧急事件的具体情况</li>
                                        <li style="margin-bottom: 8px;">需要用户采取的行动</li>
                                        <li style="margin-bottom: 8px;">联系方式和处理时限</li>
                                    </ol>
                                    <div style="background: #fff2cc; border: 1px solid #ffd666; padding: 12px; border-radius: 6px; margin-top: 15px;">
                                        <p style="margin: 0; color: #b8860b; font-size: 14px;">
                                            📞 紧急联系方式：请拨打客服热线或在线咨询
                                        </p>
                                    </div>
                                </div>
                            `;
                            break;
                    }

                    if (templateHtml) {
                        editor.dangerouslyInsertHtml(templateHtml);
                        ElMessage.success('模板已插入，请根据需要修改内容');
                    }
                };

                // 清空富文本内容
                const clearRichContent = () => {
                    if (!editor) {
                        ElMessage.warning('富文本编辑器未初始化');
                        return;
                    }

                    ElMessageBox.confirm('确定要清空所有内容吗？此操作不可撤销。', '确认清空', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        editor.clear();
                        form.htmlContent = '';
                        ElMessage.success('内容已清空');
                    }).catch(() => {
                        // 用户取消
                    });
                };

                // 插入高级样式示例
                const insertAdvancedSample = () => {
                    const advancedHtml = `<div style="max-width: 600px; margin: 0 auto; font-family: 'Microsoft YaHei', Arial, sans-serif;">
    <!-- 头部横幅 -->
    <div style="background: linear-gradient(45deg, #ff6b6b, #4ecdc4); padding: 30px; text-align: center; border-radius: 15px 15px 0 0; position: relative; overflow: hidden;">
        <div style="position: absolute; top: -50%; left: -50%; width: 200%; height: 200%; background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><circle cx=\"50\" cy=\"50\" r=\"2\" fill=\"rgba(255,255,255,0.1)\"/></svg>') repeat; animation: float 20s infinite linear;"></div>
        <h1 style="color: white; margin: 0; font-size: 28px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); position: relative; z-index: 1;">
            🚀 系统重要通知
        </h1>
        <p style="color: rgba(255,255,255,0.9); margin: 10px 0 0 0; font-size: 16px; position: relative; z-index: 1;">
            Platform Important Announcement
        </p>
    </div>

    <!-- 主要内容区域 -->
    <div style="background: white; padding: 30px; box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
        <!-- 紧急通知标签 -->
        <div style="display: inline-block; background: #ff4757; color: white; padding: 8px 16px; border-radius: 20px; font-size: 12px; font-weight: bold; margin-bottom: 20px; animation: pulse 2s infinite;">
            🔥 紧急通知
        </div>

        <div style="line-height: 1.8; color: #333;">
            <p style="font-size: 18px; color: #2c3e50; margin-bottom: 20px;">
                尊敬的用户，我们即将进行系统维护升级，请注意以下重要事项：
            </p>

            <!-- 信息卡片 -->
            <div style="display: flex; gap: 15px; margin: 25px 0; flex-wrap: wrap;">
                <div style="flex: 1; min-width: 200px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 12px; text-align: center;">
                    <div style="font-size: 24px; margin-bottom: 8px;">⏰</div>
                    <div style="font-weight: bold; margin-bottom: 5px;">维护时间</div>
                    <div style="font-size: 14px; opacity: 0.9;">今晚 23:00-01:00</div>
                </div>
                <div style="flex: 1; min-width: 200px; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 20px; border-radius: 12px; text-align: center;">
                    <div style="font-size: 24px; margin-bottom: 8px;">🔧</div>
                    <div style="font-weight: bold; margin-bottom: 5px;">影响范围</div>
                    <div style="font-size: 14px; opacity: 0.9;">全部功能暂停</div>
                </div>
            </div>

            <!-- 重要提醒列表 -->
            <div style="background: #f8f9fa; border-left: 4px solid #007bff; padding: 20px; margin: 20px 0; border-radius: 0 8px 8px 0;">
                <h3 style="color: #007bff; margin: 0 0 15px 0; font-size: 16px;">
                    📋 重要提醒事项
                </h3>
                <ul style="margin: 0; padding-left: 20px; color: #495057;">
                    <li style="margin-bottom: 8px;">请提前保存您的工作内容</li>
                    <li style="margin-bottom: 8px;">维护期间无法访问系统功能</li>
                    <li style="margin-bottom: 8px;">如有紧急问题请联系客服热线</li>
                    <li style="margin-bottom: 8px;">维护完成后系统性能将大幅提升</li>
                </ul>
            </div>

            <!-- 联系方式 -->
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 12px; text-align: center; margin-top: 25px;">
                <h3 style="margin: 0 0 15px 0; font-size: 18px;">📞 需要帮助？</h3>
                <div style="display: flex; justify-content: center; gap: 15px; flex-wrap: wrap;">
                    <a href="#" style="display: inline-block; background: rgba(255,255,255,0.2); color: white; padding: 10px 20px; text-decoration: none; border-radius: 25px; transition: all 0.3s; border: 1px solid rgba(255,255,255,0.3);">
                        💬 在线客服
                    </a>
                    <a href="#" style="display: inline-block; background: rgba(255,255,255,0.2); color: white; padding: 10px 20px; text-decoration: none; border-radius: 25px; transition: all 0.3s; border: 1px solid rgba(255,255,255,0.3);">
                        📧 邮件支持
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部 -->
    <div style="background: #2c3e50; color: white; padding: 20px; text-align: center; border-radius: 0 0 15px 15px;">
        <p style="margin: 0; font-size: 14px; opacity: 0.8;">
            感谢您的理解与支持 | 技术团队
        </p>
        <p style="margin: 5px 0 0 0; font-size: 12px; opacity: 0.6;">
            发布时间：${new Date().toLocaleString()}
        </p>
    </div>
</div>

<style>
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}
@keyframes float {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}
</style>`;
                    form.htmlContent = advancedHtml;
                    updateHtmlPreview();
                    ElMessage.success('高级样式示例已插入');
                };

                return {
                    loading,
                    form,
                    showPreview,
                    richTextLength,
                    htmlPreviewContent,
                    save,
                    createRichEditor,
                    handleEditModeChange,
                    insertSampleHtml,
                    insertAdvancedSample,
                    insertRichTemplate,
                    clearRichContent,
                    updateHtmlPreview,
                    refreshPreview,
                    formatHtmlCode,
                    clearHtmlContent
                };
            }
        })
        .use(ElementPlus)
        .mount('#app');
    </script>
</body>
</html>
