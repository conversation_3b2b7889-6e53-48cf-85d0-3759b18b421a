<?php

namespace plugin\Translation;

class Hook
{
    public function handle(&$js)
    {
        // 读取状态配置
        $status = intval(plugconf("Translation.status") ?? 0);
        
        // 当status=1时才加载js
        if ($status == 1) {
            $js[1][] = plugstatic("Translation", 'translation.js');
        }
    }
    
    public function adminjs(&$js)
    {
        // 读取状态配置
        $status = intval(plugconf("Translation.status") ?? 0);
        
        // 当status=1时才加载js
        if ($status == 1) {
            $js[] = plugstatic("Translation", 'translation.js');
        }
    }
    
    public function merchant(&$js)
    {
        // 读取状态配置
        $status = intval(plugconf("Translation.status") ?? 0);
        
        // 当status=1时才加载js
        if ($status == 1) {
            $js[] = plugstatic("Translation", 'translation.js');
        }
    }
} 