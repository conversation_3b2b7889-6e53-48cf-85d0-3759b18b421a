<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>注册问卷管理</title>
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f5f7fa;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            min-height: 100%;
        }
        /* 加载动画样式 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            backdrop-filter: blur(3px);
        }
        .loading-spinner {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .loading-circle {
            width: 50px;
            height: 50px;
            animation: loading-rotate 2s linear infinite;
        }
        .loading-circle circle {
            stroke: #409EFF;
            stroke-linecap: round;
            animation: loading-dash 1.5s ease-in-out infinite;
        }
        .loading-text {
            margin-top: 15px;
            font-size: 16px;
            color: #409EFF;
            font-weight: 500;
        }
        @keyframes loading-rotate {
            100% {
                transform: rotate(360deg);
            }
        }
        @keyframes loading-dash {
            0% {
                stroke-dasharray: 1, 200;
                stroke-dashoffset: 0;
            }
            50% {
                stroke-dasharray: 90, 150;
                stroke-dashoffset: -40;
            }
            100% {
                stroke-dasharray: 90, 150;
                stroke-dashoffset: -120;
            }
        }
        .container {
            background: #fff;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
            max-width: 1200px;
            margin: 0 auto;
            height: auto;
        }
        .header {
            margin-bottom: 30px;
        }
        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 0 20px 0;
            border-bottom: 2px solid #f0f2f5;
            margin-bottom: 24px;
        }
        .card-title {
            font-size: 22px;
            font-weight: 600;
            color: #1a1a1a;
            position: relative;
            padding-left: 12px;
        }
        .card-title:before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 18px;
            background: #409EFF;
            border-radius: 2px;
        }
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin: 32px 0 24px 0;
            padding-bottom: 12px;
            border-bottom: 1px solid #ebeef5;
            position: relative;
        }
        .section-title:after {
            content: '';
            position: absolute;
            left: 0;
            bottom: -1px;
            width: 60px;
            height: 2px;
            background: #409EFF;
        }
        .basic-settings {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
            background: #fafafa;
            padding: 24px;
            border-radius: 8px;
        }
        .options-list {
            margin-top: 24px;
        }
        .option-item {
            display: flex;
            align-items: flex-start;
            flex-wrap: wrap;
            margin-bottom: 24px;
            padding: 24px;
            border: 1px solid #e4e7ed;
            border-radius: 12px;
            background-color: #fff;
            position: relative;
            transition: all 0.3s ease;
            height: auto;
        }
        .option-item:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            border-color: #409EFF;
        }
        .option-header {
            display: flex;
            width: 100%;
            align-items: center;
            margin-bottom: 24px;
            gap: 16px;
        }
        .option-number {
            background-color: #409EFF;
            color: white;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: bold;
            flex-shrink: 0;
        }
        .option-content {
            display: flex;
            align-items: center;
            gap: 16px;
            flex-grow: 1;
            padding-right: 40px;
        }
        .option-input {
            flex-grow: 1;
            max-width: calc(100% - 160px);
        }
        .option-sort {
            width: 120px !important;
            flex-shrink: 0;
            margin-left: auto;
        }
        .option-message {
            width: 100%;
            margin-top: 20px;
            padding: 20px;
            background-color: #f8faff;
            border-radius: 8px;
            border: 1px dashed #c0d3ff;
            height: auto;
        }
        .option-message-title {
            font-weight: 600;
            margin-bottom: 20px;
            color: #409EFF;
            display: flex;
            align-items: center;
            font-size: 15px;
            padding-bottom: 12px;
            border-bottom: 1px solid #e8f0ff;
        }
        .option-message-content {
            display: grid;
            gap: 20px;
        }
        .option-message-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .option-form-item {
            margin-bottom: 16px;
            height: auto;
        }
        .option-form-item:last-child {
            margin-bottom: 0;
        }
        .option-form-label {
            font-size: 14px;
            color: #606266;
            margin-bottom: 8px;
            display: block;
        }
        .delete-option-btn {
            position: absolute;
            top: 24px;
            right: 24px;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 2;
        }
        .option-item:hover .delete-option-btn {
            opacity: 1;
        }
        .preview-dialog .el-dialog__body {
            max-height: 80vh;
            overflow-y: auto;
        }
        .actions-bar {
            margin-top: 40px;
            display: flex;
            justify-content: flex-end;
            gap: 16px;
            padding-top: 20px;
            border-top: 1px solid #ebeef5;
        }
        .hint-text {
            margin-left: 12px;
            color: #909399;
            font-size: 13px;
            opacity: 0.8;
        }
        .empty-options {
            text-align: center;
            padding: 40px;
            color: #909399;
            background-color: #fafafa;
            border-radius: 8px;
            margin-bottom: 24px;
            border: 2px dashed #e4e7ed;
        }
        .el-form-item {
            margin-bottom: 20px;
        }
        .el-form-item__label {
            font-weight: 500;
            color: #2c3e50;
        }
        .el-input__inner {
            border-radius: 6px;
        }
        .el-button {
            border-radius: 6px;
            font-weight: 500;
        }
        .el-button--primary {
            box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
        }
        .svg-icon {
            width: 16px;
            height: 16px;
            margin-right: 4px;
            vertical-align: -0.15em;
            fill: currentColor;
            overflow: hidden;
        }
        
        .delete-icon {
            width: 14px;
            height: 14px;
        }
        
        .setting-icon {
            width: 16px;
            height: 16px;
            color: #409EFF;
        }
        
        .document-icon {
            width: 16px;
            height: 16px;
            color: #409EFF;
        }
        .el-textarea__inner {
            min-height: 80px;
            height: auto;
            resize: vertical;
        }
        .el-button .svg-icon {
            margin-right: 4px;
        }
        .el-button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        .markdown-tips {
            background: #f9f9f9;
            border-left: 4px solid #67C23A;
            padding: 12px;
            margin-top: 8px;
            font-size: 13px;
            color: #606266;
            border-radius: 4px;
        }
        .markdown-tips code {
            background: #eee;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
            margin: 0 2px;
        }
        .markdown-tips h4 {
            margin: 0 0 8px 0;
            color: #409EFF;
        }
        .preview-option {
            color: #333;
            background-color: transparent;
        }
        .preview-option.selected {
            background-color: #4080ff !important;
            color: white !important;
        }
        
        /* Markdown 内容样式 */
        .markdown-content {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
        }
        .markdown-content h1, .markdown-content h2, .markdown-content h3, 
        .markdown-content h4, .markdown-content h5, .markdown-content h6 {
            margin-top: 1.5em;
            margin-bottom: 0.5em;
            font-weight: 600;
            color: #1a1a1a;
        }
        .markdown-content h1 { font-size: 1.8em; }
        .markdown-content h2 { font-size: 1.6em; }
        .markdown-content h3 { font-size: 1.4em; }
        .markdown-content h4 { font-size: 1.2em; }
        .markdown-content p { margin: 0.8em 0; }
        .markdown-content ul, .markdown-content ol { padding-left: 2em; margin: 0.8em 0; }
        .markdown-content li { margin: 0.3em 0; }
        .markdown-content blockquote {
            margin: 1em 0;
            padding: 0.5em 1em;
            border-left: 4px solid #ddd;
            background: #f8f8f8;
            color: #666;
        }
        .markdown-content code {
            background: #f0f0f0;
            padding: 0.2em 0.4em;
            border-radius: 3px;
            font-family: monospace;
            font-size: 0.9em;
        }
        .markdown-content pre {
            background: #f0f0f0;
            padding: 1em;
            border-radius: 5px;
            overflow-x: auto;
        }
        .markdown-content pre code {
            background: transparent;
            padding: 0;
        }
        .markdown-content a {
            color: #0366d6;
            text-decoration: none;
        }
        .markdown-content a:hover {
            text-decoration: underline;
        }
        /* 初始加载动画样式 */
        #initial-loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #f5f7fa;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        .initial-loading-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #409EFF;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .initial-loading-text {
            position: absolute;
            margin-top: 80px;
            color: #409EFF;
            font-size: 16px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- 初始加载动画 -->
    <div id="initial-loading">
        <div class="initial-loading-spinner"></div>
        <div class="initial-loading-text">加载中...</div>
    </div>

    <div id="app" style="display: none;">
        <!-- 全局加载动画 -->
        <div class="loading-overlay" v-if="loading">
            <div class="loading-spinner">
                <svg viewBox="25 25 50 50" class="loading-circle">
                    <circle cx="50" cy="50" r="20" fill="none" stroke-width="3" stroke-miterlimit="10" />
                </svg>
                <p class="loading-text">{{loadingText}}</p>
            </div>
        </div>

        <div class="container">
            <div class="header">
                <el-card>
                    <template #header>
                        <div class="card-header">
                            <span class="card-title">注册问卷管理</span>
                        </div>
                    </template>
                    
                    <el-form :model="form" label-width="120px" label-position="top">
                        <div class="section-title">基本设置</div>
                        <div class="basic-settings">
                            <el-form-item label="启用状态">
                                <el-switch v-model="form.status" />
                                <span class="hint-text">开启后访问注册页面将自动弹出问卷</span>
                            </el-form-item>

                            <el-form-item label="显示关闭按钮">
                                <el-switch v-model="form.showCloseButton" />
                                <span class="hint-text">开启后问卷右上角会显示关闭按钮(X)</span>
                            </el-form-item>

                            <el-form-item label="问卷标题">
                                <el-input v-model="form.title" placeholder="请输入问卷标题"></el-input>
                            </el-form-item>

                            <el-form-item label="提交按钮文字">
                                <el-input v-model="form.buttonText" placeholder="请输入按钮文字"></el-input>
                            </el-form-item>
                        </div>

                        <div class="section-title">选项设置</div>
                        <p v-if="form.options.length === 0" class="empty-options">
                            暂无选项，请点击下方按钮添加
                        </p>
                        <div class="options-list">
                            <div v-for="(option, index) in form.options" :key="index" class="option-item">
                                <div class="option-header">
                                    <div class="option-number">{{ index + 1 }}</div>
                                    <div class="option-content">
                                        <el-input 
                                            v-model="option.text" 
                                            placeholder="选项内容" 
                                            class="option-input">
                                        </el-input>
                                        <el-input-number 
                                            v-model="option.sort" 
                                            :min="1" 
                                            class="option-sort"
                                            controls-position="right" 
                                            placeholder="排序">
                                        </el-input-number>
                                    </div>
                                </div>
                                
                                <el-button 
                                    type="danger" 
                                    size="small" 
                                    class="delete-option-btn" 
                                    circle 
                                    @click="removeOption(index)">
                                    <svg class="svg-icon delete-icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                                        <path fill="currentColor" d="M160 256H96a32 32 0 0 1 0-64h256V95.936a32 32 0 0 1 32-32h256a32 32 0 0 1 32 32V192h256a32 32 0 1 1 0 64h-64v672a32 32 0 0 1-32 32H192a32 32 0 0 1-32-32V256zm448-64v-64H416v64h192zM224 896h576V256H224v640zm192-128a32 32 0 0 1-32-32V416a32 32 0 0 1 64 0v320a32 32 0 0 1-32 32zm192 0a32 32 0 0 1-32-32V416a32 32 0 0 1 64 0v320a32 32 0 0 1-32 32z"/>
                                    </svg>
                                </el-button>
                                
                                <div class="option-message">
                                    <!-- 对于任何需要选择类型的选项（新添加的选项）显示类型选择器 -->
                                    <template v-if="form.customMessages[index] && form.customMessages[index]._needTypeSelect">
                                        <div class="option-message-title">
                                            <svg class="svg-icon setting-icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                                                <path fill="currentColor" d="M600.704 64a32 32 0 0 1 30.464 22.208l35.2 109.376c14.784 7.232 28.928 15.36 42.432 24.512l112.384-24.192a32 32 0 0 1 34.432 15.36L944.32 364.8a32 32 0 0 1-4.032 37.504l-77.12 85.12a357.12 357.12 0 0 1 0 49.024l77.12 85.248a32 32 0 0 1 4.032 37.504l-88.704 153.6a32 32 0 0 1-34.432 15.296L708.8 803.904c-13.504 9.152-27.648 17.28-42.432 24.512l-35.2 109.376A32 32 0 0 1 600.704 960H423.296a32 32 0 0 1-30.464-22.208L357.696 828.48a351.616 351.616 0 0 1-42.432-24.512l-112.384 24.192a32 32 0 0 1-34.432-15.36L79.744 659.2a32 32 0 0 1 4.032-37.504l77.12-85.248a357.12 357.12 0 0 1 0-49.024l-77.12-85.12a32 32 0 0 1-4.032-37.504l88.704-153.6a32 32 0 0 1 34.432-15.296l112.32 24.256c13.504-9.152 27.648-17.28 42.432-24.512l35.2-109.376A32 32 0 0 1 423.296 64H600.704zm-23.296 64H446.592l-36.8 114.432a32 32 0 0 1-19.712 20.992 288 288 0 0 0-46.784 26.88 32 32 0 0 1-28.032 4.608l-117.888-25.344-65.92 114.432 80.64 88.96a32 32 0 0 1 8.96 27.776 290.688 290.688 0 0 0 0 54.272 32 32 0 0 1-8.96 27.776l-80.64 88.96 65.92 114.432 117.888-25.344a32 32 0 0 1 28.032 4.608 288 288 0 0 0 46.784 26.88 32 32 0 0 1 19.712 20.992l36.8 114.432h130.816l36.8-114.432a32 32 0 0 1 19.712-20.992 288 288 0 0 0 46.784-26.88 32 32 0 0 1 28.032-4.608l117.888 25.344 65.92-114.432-80.64-88.96a32 32 0 0 1-8.96-27.776 290.688 290.688 0 0 0 0-54.272 32 32 0 0 1 8.96-27.776l80.64-88.96-65.92-114.432-117.888 25.344a32 32 0 0 1-28.032-4.608 288 288 0 0 0-46.784-26.88 32 32 0 0 1-19.712-20.992l-36.8-114.432zM512 320a192 192 0 1 1 0 384 192 192 0 0 1 0-384zm0 64a128 128 0 1 0 0 256 128 128 0 0 0 0-256z"/>
                                            </svg>
                                            <span style="margin-left: 8px;">请选择响应类型</span>
                                        </div>
                                        <div class="option-message-content">
                                            <div style="margin-bottom: 20px;">
                                                <el-alert
                                                    title="请为此选项选择一个响应类型"
                                                    type="info"
                                                    description="选择'显示提示信息'将显示一个简单的提示框；选择'显示协议内容'将显示一个带有同意/不同意按钮的协议框。"
                                                    show-icon
                                                    :closable="false">
                                                </el-alert>
                                            </div>
                                            <el-radio-group v-model="optionTypes[index]" @change="changeOptionType(index)">
                                                <el-radio :label="'message'" style="margin-right: 20px;">显示提示信息</el-radio>
                                                <el-radio :label="'agreement'">显示协议内容</el-radio>
                                            </el-radio-group>
                                        </div>
                                    </template>
                                    
                                    <!-- 已经是消息类型的选项 -->
                                    <template v-else-if="form.customMessages[index] && form.customMessages[index].message !== undefined">
                                        <div class="option-message-title">
                                            <svg class="svg-icon setting-icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                                                <path fill="currentColor" d="M600.704 64a32 32 0 0 1 30.464 22.208l35.2 109.376c14.784 7.232 28.928 15.36 42.432 24.512l112.384-24.192a32 32 0 0 1 34.432 15.36L944.32 364.8a32 32 0 0 1-4.032 37.504l-77.12 85.12a357.12 357.12 0 0 1 0 49.024l77.12 85.248a32 32 0 0 1 4.032 37.504l-88.704 153.6a32 32 0 0 1-34.432 15.296L708.8 803.904c-13.504 9.152-27.648 17.28-42.432 24.512l-35.2 109.376A32 32 0 0 1 600.704 960H423.296a32 32 0 0 1-30.464-22.208L357.696 828.48a351.616 351.616 0 0 1-42.432-24.512l-112.384 24.192a32 32 0 0 1-34.432-15.36L79.744 659.2a32 32 0 0 1 4.032-37.504l77.12-85.248a357.12 357.12 0 0 1 0-49.024l-77.12-85.12a32 32 0 0 1-4.032-37.504l88.704-153.6a32 32 0 0 1 34.432-15.296l112.32 24.256c13.504-9.152 27.648-17.28 42.432-24.512l35.2-109.376A32 32 0 0 1 423.296 64H600.704zm-23.296 64H446.592l-36.8 114.432a32 32 0 0 1-19.712 20.992 288 288 0 0 0-46.784 26.88 32 32 0 0 1-28.032 4.608l-117.888-25.344-65.92 114.432 80.64 88.96a32 32 0 0 1 8.96 27.776 290.688 290.688 0 0 0 0 54.272 32 32 0 0 1-8.96 27.776l-80.64 88.96 65.92 114.432 117.888-25.344a32 32 0 0 1 28.032 4.608 288 288 0 0 0 46.784 26.88 32 32 0 0 1 19.712 20.992l36.8 114.432h130.816l36.8-114.432a32 32 0 0 1 19.712-20.992 288 288 0 0 0 46.784-26.88 32 32 0 0 1 28.032-4.608l117.888 25.344 65.92-114.432-80.64-88.96a32 32 0 0 1-8.96-27.776 290.688 290.688 0 0 0 0-54.272 32 32 0 0 1 8.96-27.776l80.64-88.96-65.92-114.432-117.888 25.344a32 32 0 0 1-28.032-4.608 288 288 0 0 0-46.784-26.88 32 32 0 0 1-19.712-20.992l-36.8-114.432zM512 320a192 192 0 1 1 0 384 192 192 0 0 1 0-384zm0 64a128 128 0 1 0 0 256 128 128 0 0 0 0-256z"/>
                                            </svg>
                                            <span style="margin-left: 8px;">选项响应设置</span>
                                            <el-button 
                                                type="text" 
                                                size="small" 
                                                style="margin-left: auto;"
                                                @click="optionTypes[index] = 'agreement'; changeOptionType(index);">
                                                切换为协议设置 <el-tooltip content="您输入的信息内容将会被保留" placement="top"><i class="el-icon-info" style="font-size: 14px;"></i></el-tooltip>
                                            </el-button>
                                        </div>
                                        <div class="option-message-content">
                                            <div class="option-form-item">
                                                <label class="option-form-label">提示信息</label>
                                                <el-input 
                                                    type="textarea" 
                                                    :rows="3" 
                                                    v-model="form.customMessages[index].message" 
                                                    placeholder="选择此选项后显示的提示信息">
                                                </el-input>
                                            </div>
                                            
                                            <!-- 按钮设置区域 -->
                                            <div class="option-form-item">
                                                <label class="option-form-label">
                                                    按钮设置
                                                    <el-button 
                                                        type="primary" 
                                                        size="small" 
                                                        style="margin-left: 8px;" 
                                                        @click="addButton(index)">
                                                        添加按钮
                                                    </el-button>
                                                </label>
                                                
                                                <!-- 主按钮设置 -->
                                                <div class="option-button-item" style="margin-bottom: 15px; padding: 15px; border: 1px solid #ebeef5; border-radius: 6px; background-color: #fafafa;">
                                                    <div style="font-weight: 500; margin-bottom: 10px; color: #409EFF; display: flex; align-items: center;">
                                                        <svg class="svg-icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" style="width: 16px; height: 16px; margin-right: 5px;">
                                                            <path fill="currentColor" d="M831.872 340.864 512 652.672 192.128 340.864a30.592 30.592 0 0 0-42.752 0 29.12 29.12 0 0 0 0 41.6L489.664 714.24a32 32 0 0 0 44.672 0l340.288-331.712a29.12 29.12 0 0 0 0-41.728 30.592 30.592 0 0 0-42.752 0z"></path>
                                                        </svg>
                                                        主按钮
                                                    </div>
                                                    <div class="option-message-row">
                                                        <div class="option-form-item" style="margin-bottom: 0;">
                                                            <label class="option-form-label">按钮文字</label>
                                                            <el-input 
                                                                v-model="form.customMessages[index].buttonText" 
                                                                placeholder="按钮显示文字，默认为：我知道了">
                                                            </el-input>
                                                        </div>
                                                        <div class="option-form-item" style="margin-bottom: 0;">
                                                            <label class="option-form-label">跳转地址</label>
                                                            <el-input 
                                                                v-model="form.customMessages[index].redirectUrl" 
                                                                placeholder="点击按钮后跳转的地址，不填则关闭弹窗">
                                                            </el-input>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <!-- 附加按钮 -->
                                                <div v-if="form.customMessages[index].extraButtons && form.customMessages[index].extraButtons.length > 0">
                                                    <div 
                                                        v-for="(btn, btnIndex) in form.customMessages[index].extraButtons" 
                                                        :key="btnIndex"
                                                        class="option-button-item" 
                                                        style="margin-bottom: 15px; padding: 15px; border: 1px solid #ebeef5; border-radius: 6px; background-color: #fafafa; position: relative;">
                                                        
                                                        <el-button 
                                                            type="danger" 
                                                            size="mini" 
                                                            circle
                                                            style="position: absolute; top: 10px; right: 10px;" 
                                                            @click="removeButton(index, btnIndex)">
                                                            <svg class="svg-icon delete-icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                                                                <path fill="currentColor" d="M160 256H96a32 32 0 0 1 0-64h256V95.936a32 32 0 0 1 32-32h256a32 32 0 0 1 32 32V192h256a32 32 0 1 1 0 64h-64v672a32 32 0 0 1-32 32H192a32 32 0 0 1-32-32V256zm448-64v-64H416v64h192zM224 896h576V256H224v640zm192-128a32 32 0 0 1-32-32V416a32 32 0 0 1 64 0v320a32 32 0 0 1-32 32zm192 0a32 32 0 0 1-32-32V416a32 32 0 0 1 64 0v320a32 32 0 0 1-32 32z"/>
                                                            </svg>
                                                        </el-button>
                                                        
                                                        <div style="font-weight: 500; margin-bottom: 10px; color: #409EFF; display: flex; align-items: center;">
                                                            <svg class="svg-icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" style="width: 16px; height: 16px; margin-right: 5px;">
                                                                <path fill="currentColor" d="M831.872 340.864 512 652.672 192.128 340.864a30.592 30.592 0 0 0-42.752 0 29.12 29.12 0 0 0 0 41.6L489.664 714.24a32 32 0 0 0 44.672 0l340.288-331.712a29.12 29.12 0 0 0 0-41.728 30.592 30.592 0 0 0-42.752 0z"></path>
                                                            </svg>
                                                            附加按钮 {{btnIndex + 1}}
                                                        </div>
                                                        
                                                        <div class="option-message-row">
                                                            <div class="option-form-item" style="margin-bottom: 10px;">
                                                                <label class="option-form-label">按钮文字</label>
                                                                <el-input 
                                                                    v-model="btn.text" 
                                                                    placeholder="按钮显示文字">
                                                                </el-input>
                                                            </div>
                                                            <div class="option-form-item" style="margin-bottom: 10px;">
                                                                <label class="option-form-label">按钮类型</label>
                                                                <el-select v-model="btn.type" placeholder="选择按钮类型" style="width: 100%;">
                                                                    <el-option label="默认按钮" value="default"></el-option>
                                                                    <el-option label="主要按钮" value="primary"></el-option>
                                                                    <el-option label="成功按钮" value="success"></el-option>
                                                                    <el-option label="警告按钮" value="warning"></el-option>
                                                                    <el-option label="危险按钮" value="danger"></el-option>
                                                                </el-select>
                                                            </div>
                                                        </div>
                                                        <div class="option-form-item">
                                                            <label class="option-form-label">跳转地址</label>
                                                            <el-input 
                                                                v-model="btn.url" 
                                                                placeholder="点击按钮后跳转的地址，不填则关闭弹窗">
                                                            </el-input>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                    
                                    <!-- 已经是协议类型的选项 -->
                                    <template v-else-if="form.customMessages[index] && form.customMessages[index].agreement !== undefined">
                                        <div class="option-message-title">
                                            <svg class="svg-icon document-icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                                                <path fill="currentColor" d="M832 384H576V128H192v768h640V384zm-26.496-64L640 154.496V320h165.504zM192 64h384l256 256v608a32 32 0 0 1-32 32H192a32 32 0 0 1-32-32V96a32 32 0 0 1 32-32zm0 768v-64h640v64H192z m0-128v-64h640v64H192z m0-128v-64h640v64H192z"/>
                                            </svg>
                                            <span style="margin-left: 8px;">协议设置</span>
                                            <el-button 
                                                type="text" 
                                                size="small" 
                                                style="margin-left: auto;"
                                                @click="optionTypes[index] = 'message'; changeOptionType(index);">
                                                切换为提示信息 <el-tooltip content="您输入的协议内容将会被保留" placement="top"><i class="el-icon-info" style="font-size: 14px;"></i></el-tooltip>
                                            </el-button>
                                        </div>
                                        <div class="option-message-content">
                                            <div class="option-form-item">
                                                <label class="option-form-label">协议内容（支持Markdown语法）</label>
                                                <el-input 
                                                    type="textarea" 
                                                    :rows="8" 
                                                    v-model="form.customMessages[index].agreement" 
                                                    placeholder="用户协议和隐私政策内容，支持Markdown语法">
                                                </el-input>
                                                <div class="markdown-tips">
                                                    <h4>Markdown语法提示：</h4>
                                                    <p>
                                                        <code># 标题1</code> <code>## 标题2</code> - 使用#号创建标题<br>
                                                        <code>**粗体**</code> <code>*斜体*</code> - 使用星号标记文本样式<br>
                                                        <code>[链接文字](https://example.com)</code> - 创建超链接<br>
                                                        <code>1. 有序列表</code> <code>- 无序列表</code> - 创建列表<br>
                                                        <code>---</code> - 创建水平分割线<br>
                                                        <code>```代码块```</code> - 代码块
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </div>
                        <el-button type="primary" plain @click="addOption" style="margin-top: 10px;">
                            <svg class="svg-icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                                <path fill="currentColor" d="M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64h352z"/>
                            </svg>
                            添加选项
                        </el-button>

                        <div class="actions-bar">
                            <el-button @click="previewDialog = true">
                                <svg class="svg-icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                                    <path fill="currentColor" d="M512 160c320 0 512 352 512 352S832 864 512 864 0 512 0 512s192-352 512-352zm0 64c-225.28 0-384.128 208.064-436.8 288 52.608 79.872 211.456 288 436.8 288 225.28 0 384.128-208.064 436.8-288-52.608-79.872-211.456-288-436.8-288zm0 64a224 224 0 1 1 0 448 224 224 0 0 1 0-448zm0 64a160.192 160.192 0 0 0-160 160c0 88.192 71.744 160 160 160s160-71.808 160-160-71.744-160-160-160z"/>
                                </svg>
                                预览效果
                            </el-button>
                            <el-button type="primary" @click="saveConfig">
                                <svg class="svg-icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                                    <path fill="currentColor" d="M406.656 706.944 195.84 496.256a32 32 0 1 0-45.248 45.248l256 256 512-512a32 32 0 0 0-45.248-45.248L406.592 706.944z"/>
                                </svg>
                                保存配置
                            </el-button>
                        </div>
                    </el-form>
                </el-card>
            </div>
        </div>

        <!-- 预览弹窗 -->
        <el-dialog v-model="previewDialog" title="预览效果" width="450px" class="preview-dialog">
            <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);">
                <h3 style="margin-top: 0; margin-bottom: 20px; font-size: 18px; font-weight: normal;">{{ form.title }}</h3>
                
                <!-- 选项列表 - 使用div替代radio，与a.js保持一致 -->
                <div class="preview-options">
                    <div 
                        v-for="option in sortedOptions" 
                        :key="option.sort" 
                        class="preview-option"
                        :class="{ 'selected': previewSelected === option.text }"
                        @click="selectOption(option)"
                        style="padding: 12px 10px; margin-bottom: 8px; border-radius: 4px; cursor: pointer; transition: all 0.3s;">
                        {{ option.text }}
                    </div>
                </div>
                
                <!-- 提交按钮 -->
                <div style="text-align: center; margin-top: 20px;">
                    <button 
                        style="background-color: #4080ff; color: white; border: none; padding: 10px 15px; border-radius: 4px; cursor: pointer; width: 100%; font-size: 16px;"
                        @click="handleSubmitButtonClick">
                        {{ form.buttonText }}
                    </button>
                </div>
                
                <div v-if="form.showCloseButton" style="position: absolute; top: 10px; right: 40px; font-size: 24px; cursor: pointer; color: #333;">&times;</div>
            </div>
            
            <!-- 添加协议对话框预览 -->
            <div v-if="showAgreementPreview" style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 10000; display: flex; align-items: center; justify-content: center;">
                <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); max-width: 600px; width: 90%; max-height: 80vh; overflow-y: auto; position: relative;">
                    <!-- 添加关闭按钮 -->
                    <div style="position: absolute; top: 10px; right: 15px; font-size: 24px; cursor: pointer; color: #333;" @click="closeAgreementPreview">&times;</div>
                    
                    <div style="margin-bottom: 20px; line-height: 1.6; color: #333;">
                        <div v-if="previewAgreementOption">
                            <div v-html="formatMarkdown(previewAgreementOption.agreement || '')" class="markdown-content"></div>
                        </div>
                        <div v-else>
                            <p>选择一个协议类型的选项来预览协议内容</p>
                        </div>
                    </div>
                    
                    <div style="display: flex; justify-content: space-between;">
                        <button 
                            style="background-color: #f56c6c; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer; width: 48%;"
                            @click="closeAgreementPreview">
                            不同意
                        </button>
                        <button 
                            style="background-color: #67c23a; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer; width: 48%;"
                            @click="closeAgreementPreview">
                            我已阅读，同意协议
                        </button>
                    </div>
                </div>
            </div>
            
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="previewDialog = false">关闭预览</el-button>
                    <el-button type="primary" @click="previewAgreement" v-if="hasAgreementOption">预览协议对话框</el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 添加消息提示框预览 -->
        <div v-if="showMessagePreview" style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 10000; display: flex; align-items: center; justify-content: center;">
            <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); max-width: 450px; width: 90%; position: relative;">
                <!-- 添加关闭按钮 -->
                <div style="position: absolute; top: 10px; right: 15px; font-size: 24px; cursor: pointer; color: #333;" @click="closeMessagePreview">&times;</div>
                
                <div style="margin: 10px 0 25px 0; line-height: 1.6; color: #333;">
                    {{ previewMessageOption ? previewMessageOption.message : '' }}
                </div>
                
                <!-- 主按钮 -->
                <div style="text-align: center; margin-bottom: 10px;">
                    <button 
                        style="background-color: #4080ff; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer; width: 100%;"
                        @click="handleMessageButtonClick">
                        {{ previewMessageOption ? (previewMessageOption.buttonText || '我知道了') : '我知道了' }}
                    </button>
                </div>
                
                <!-- 附加按钮组 -->
                <div v-if="previewMessageOption && previewMessageOption.extraButtons && previewMessageOption.extraButtons.length > 0"
                     style="display: flex; flex-wrap: wrap; gap: 10px; margin-top: 10px;">
                    <button 
                        v-for="(btn, index) in previewMessageOption.extraButtons" 
                        :key="index"
                        :style="getButtonStyle(btn.type)"
                        style="flex-grow: 1; min-width: 120px; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer;"
                        @click="handleExtraButtonClick(btn)">
                        {{ btn.text }}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script>
        const { createApp, ref, computed } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        // 等待所有资源加载完成
        window.addEventListener('load', function() {
            // 创建Vue应用
            const app = createApp({
                setup() {
                    // 添加加载状态变量
                    const loading = ref(false);
                    const loadingText = ref('加载中...');
                    
                    const form = ref({
                        status: false,
                        showCloseButton: true,
                        title: '',
                        buttonText: '',
                        options: [],
                        customMessages: [
                            { 
                                message: '亲，买家无需注册哦，找商家要购买地址！没有商家联系方式，买不了哦！',
                                buttonText: '我知道了',
                                redirectUrl: ''
                            },
                            { 
                                message: '亲，通过查询订单，输入订单号或下单联系方式，可以查看商家联系方式和卡密信息',
                                buttonText: '去查询',
                                redirectUrl: '/query'
                            },
                            { 
                                message: '亲，通过查询订单，输入订单号或下单联系方式，可以查看商家联系方式和卡密信息',
                                buttonText: '去查询',
                                redirectUrl: '/query'
                            },
                            { 
                                agreement: `用户注册协议和隐私政策\n\n
## 【审慎阅读】
您在申请注册流程中点击同意前，应当认真阅读以下协议。请您务必审慎阅读、充分理解协议中相关条款内容，其中包括：

1. 与您约定免除或限制责任的条款；
2. 与您约定法律适用和管辖的条款；
3. 其他以粗体下划线标识的重要条款。

如您对协议有任何疑问，可向平台客服咨询。当您按照注册页面提示填写信息、阅读并同意协议且完成全部注册程序后，即表示您已充分阅读、理解并接受协议的全部内容。如您因平台服务与发生争议的，适用《平台服务协议》处理。如您在使用平台服务过程中与其他用户发生争议的，依您与其他用户达成的协议处理。

### 《嗨发卡-全网最好用的虚拟卡密自动发货平台平台使用协议》

### 《法律声明及隐私权政策》

### 《平台禁售商品目录》`
                            }
                        ]
                    });

                    const previewDialog = ref(false);
                    const previewSelected = ref('');
                    const showAgreementPreview = ref(false);
                    const previewAgreementOption = ref(null);
                    const showMessagePreview = ref(false);
                    const previewMessageOption = ref(null);
                    
                    // 排序后的选项
                    const sortedOptions = computed(() => {
                        // 添加防护代码，确保 form.value.options 存在
                        if (!form.value || !form.value.options) {
                            return [];
                        }
                        return [...form.value.options].sort((a, b) => a.sort - b.sort);
                    });

                    // 获取配置
                    const fetchData = async () => {
                        loading.value = true;
                        loadingText.value = '正在加载配置...';
                        try {
                            const res = await axios.get('/plugin/Registrationpage/api/fetchData');
                            console.log('接收到的配置数据:', res.data);
                            
                            if (res.data.code === 1) {
                                form.value.status = res.data.data.survey_status === 1;
                                form.value.showCloseButton = res.data.data.show_close_button !== 0;
                                form.value.options = res.data.data.survey_options || [];
                                form.value.title = res.data.data.survey_title;
                                form.value.buttonText = res.data.data.button_text;
                                
                                // 加载自定义消息
                                if (res.data.data.custom_messages && res.data.data.custom_messages.length > 0) {
                                    form.value.customMessages = res.data.data.custom_messages;
                                }
                                
                                // 确保customMessages数组至少有4个元素
                                while (form.value.customMessages.length < 4) {
                                    if (form.value.customMessages.length === 0) {
                                        form.value.customMessages.push({ 
                                            message: '亲，买家无需注册哦，找商家要购买地址！没有商家联系方式，买不了哦！',
                                            buttonText: '我知道了',
                                            redirectUrl: ''
                                        });
                                    } else if (form.value.customMessages.length === 1 || form.value.customMessages.length === 2) {
                                        form.value.customMessages.push({ 
                                            message: '亲，通过查询订单，输入订单号或下单联系方式，可以查看商家联系方式和卡密信息',
                                            buttonText: '去查询',
                                            redirectUrl: '/query'
                                        });
                                    } else if (form.value.customMessages.length === 3) {
                                        form.value.customMessages.push({ 
                                            agreement: `用户注册协议和隐私政策\n\n
## 【审慎阅读】
您在申请注册流程中点击同意前，应当认真阅读以下协议。请您务必审慎阅读、充分理解协议中相关条款内容，其中包括：

1. 与您约定免除或限制责任的条款；
2. 与您约定法律适用和管辖的条款；
3. 其他以粗体下划线标识的重要条款。

如您对协议有任何疑问，可向平台客服咨询。当您按照注册页面提示填写信息、阅读并同意协议且完成全部注册程序后，即表示您已充分阅读、理解并接受协议的全部内容。如您因平台服务与发生争议的，适用《平台服务协议》处理。如您在使用平台服务过程中与其他用户发生争议的，依您与其他用户达成的协议处理。

### 《嗨发卡-全网最好用的虚拟卡密自动发货平台平台使用协议》

### 《法律声明及隐私权政策》

### 《平台禁售商品目录》`
                                        });
                                    }
                                }
                                
                                // 检查并修复可能的乱码
                                if (form.value.customMessages[3] && form.value.customMessages[3].agreement) {
                                    const agreement = form.value.customMessages[3].agreement;
                                    
                                    // 检测明显的乱码特征
                                    if (agreement.includes('ç') || agreement.includes('æ') || agreement.includes('å') || 
                                        agreement.includes('&#') || (agreement.includes('&') && agreement.includes(';'))) {
                                        
                                        console.warn("检测到协议内容可能包含乱码，尝试修复");
                                        
                                        // 尝试多种修复方法
                                        try {
                                            // 方法1: 使用decodeURIComponent
                                            form.value.customMessages[3].agreement = decodeURIComponent(escape(agreement));
                                            console.log("乱码修复成功");
                                        } catch (e) {
                                            console.error("修复乱码失败:", e);
                                            
                                            // 方法2: 使用HTML解码
                                            try {
                                                const div = document.createElement('div');
                                                div.innerHTML = agreement;
                                                form.value.customMessages[3].agreement = div.textContent;
                                                console.log("使用HTML解码修复乱码");
                                            } catch (e2) {
                                                console.error("HTML解码失败:", e2);
                                            }
                                        }
                                    }
                                }
                                
                                // 数据加载完成后隐藏初始加载动画
                                hideInitialLoading();
                            } else {
                                ElMessage.error(res.data.msg || '获取配置失败');
                            }
                        } catch (error) {
                            console.error('获取配置出错:', error);
                            ElMessage.error('获取配置失败: ' + (error.response?.data?.msg || error.message));
                        } finally {
                            loading.value = false;
                        }
                    };

                    // 添加选项
                    const addOption = () => {
                        const newIndex = form.value.options.length;
                        
                        // 添加选项基本信息
                        form.value.options.push({
                            text: '',
                            sort: newIndex + 1
                        });
                        
                        // 同时添加空的自定义消息对象（不预设任何类型）
                        form.value.customMessages.push({
                            _needTypeSelect: true // 添加标记，表示需要选择类型
                        });
                    };

                    // 选项类型选择
                    const optionTypes = ref({});
                    
                    // 更改选项类型
                    const changeOptionType = (index) => {
                        // 保存当前消息的内容
                        const currentMessage = form.value.customMessages[index];
                        const type = optionTypes.value[index];
                        
                        // 保存可能的额外按钮
                        const extraButtons = currentMessage && currentMessage.extraButtons ? 
                            [...currentMessage.extraButtons] : [];
                        
                        // 保存主按钮信息
                        const buttonText = currentMessage && currentMessage.buttonText || '我知道了';
                        const redirectUrl = currentMessage && currentMessage.redirectUrl || '';
                        
                        if (type === 'message') {
                            // 如果是从协议类型切换到消息类型
                            if (currentMessage && currentMessage.agreement) {
                                // 保存协议内容作为消息内容
                                const savedContent = currentMessage.agreement || '';
                                form.value.customMessages[index] = { 
                                    message: savedContent,
                                    buttonText: buttonText,
                                    redirectUrl: redirectUrl
                                };
                                
                                // 保留额外按钮（如果有）
                                if (extraButtons.length > 0) {
                                    form.value.customMessages[index].extraButtons = extraButtons;
                                }
                                
                                // 显示切换成功提示
                                ElMessage.success('已切换为提示信息类型，原协议内容已保留');
                            } else {
                                // 新建消息或从其他类型切换
                                form.value.customMessages[index] = { 
                                    message: '',
                                    buttonText: '我知道了',
                                    redirectUrl: ''
                                };
                            }
                        } else if (type === 'agreement') {
                            // 如果是从消息类型切换到协议类型
                            if (currentMessage && currentMessage.message) {
                                // 保存消息内容作为协议内容
                                const savedContent = currentMessage.message || '';
                                form.value.customMessages[index] = { 
                                    agreement: savedContent || `用户注册协议和隐私政策\n\n
## 【审慎阅读】
您在申请注册流程中点击同意前，应当认真阅读以下协议。请您务必审慎阅读、充分理解协议中相关条款内容，其中包括：

1. 与您约定免除或限制责任的条款；
2. 与您约定法律适用和管辖的条款；
3. 其他以粗体下划线标识的重要条款。`
                                };
                                
                                // 显示切换成功提示
                                ElMessage.success('已切换为协议类型，原提示信息内容已保留');
                            } else {
                                // 新建协议或从其他类型切换
                                form.value.customMessages[index] = { 
                                    agreement: `用户注册协议和隐私政策\n\n
## 【审慎阅读】
您在申请注册流程中点击同意前，应当认真阅读以下协议。请您务必审慎阅读、充分理解协议中相关条款内容，其中包括：

1. 与您约定免除或限制责任的条款；
2. 与您约定法律适用和管辖的条款；
3. 其他以粗体下划线标识的重要条款。`
                                };
                            }
                        }
                        
                        // 如果是从未选择类型状态切换，移除标记
                        if (form.value.customMessages[index]._needTypeSelect) {
                            delete form.value.customMessages[index]._needTypeSelect;
                        }
                    };

                    // 删除选项
                    const removeOption = (index) => {
                        form.value.options.splice(index, 1);
                        form.value.customMessages.splice(index, 1);
                    };

                    // 保存配置前的处理
                    const prepareDataForSave = () => {
                        // 移除所有临时标记
                        form.value.customMessages.forEach(msg => {
                            if (msg._needTypeSelect) {
                                delete msg._needTypeSelect;
                            }
                        });
                    };
                    
                    // 保存配置
                    const saveConfig = async () => {
                        try {
                            // 验证数据
                            if (!form.value.title) {
                                return ElMessage.error('请输入问卷标题');
                            }
                            if (!form.value.buttonText) {
                                return ElMessage.error('请输入按钮文字');
                            }
                            if (form.value.options.length === 0) {
                                return ElMessage.error('请至少添加一个选项');
                            }
                            if (form.value.options.some(opt => !opt.text)) {
                                return ElMessage.error('选项内容不能为空');
                            }
                            
                            // 检查是否有未选择类型的选项
                            for (let i = 0; i < form.value.customMessages.length; i++) {
                                if (form.value.customMessages[i]._needTypeSelect) {
                                    return ElMessage.error(`第 ${i+1} 个选项未选择响应类型，请选择类型后再保存`);
                                }
                            }

                            // 准备要发送的数据
                            prepareDataForSave();
                            
                            const postData = {
                                status: form.value.status ? 1 : 0,
                                showCloseButton: form.value.showCloseButton ? 1 : 0,
                                options: form.value.options,
                                title: form.value.title,
                                buttonText: form.value.buttonText,
                                customMessages: form.value.customMessages
                            };
                            
                            // 确保协议内容是有效的UTF-8编码
                            if (postData.customMessages[3] && postData.customMessages[3].agreement) {
                                // 标准化换行符
                                postData.customMessages[3].agreement = postData.customMessages[3].agreement
                                    .replace(/\r\n/g, '\n')
                                    .replace(/\r/g, '\n');
                                    
                                // 删除多余的空行
                                postData.customMessages[3].agreement = postData.customMessages[3].agreement
                                    .replace(/\n{3,}/g, '\n\n');
                                    
                                // 记录保存内容以便调试
                                console.log('协议内容前40字符:', postData.customMessages[3].agreement.substring(0, 40));
                            }
                            
                            console.log('发送的数据:', postData);
                            
                            // 显示加载状态
                            loading.value = true;
                            loadingText.value = '正在保存配置...';
                            
                            const res = await axios.post('/plugin/Registrationpage/api/saveConfig', postData, {
                                headers: {
                                    'Content-Type': 'application/json;charset=utf-8'
                                }
                            });
                            
                            console.log('保存结果:', res.data);

                            if (res.data.code === 1) {
                                ElMessage.success('保存成功');
                            } else {
                                ElMessage.error(res.data.msg || '保存失败');
                            }
                        } catch (error) {
                            console.error('保存出错:', error);
                            if (error.response) {
                                console.error('错误响应:', error.response.data);
                                ElMessage.error('保存失败: ' + (error.response.data.msg || '服务器错误'));
                            } else {
                                ElMessage.error('保存失败: ' + error.message);
                            }
                        } finally {
                            // 隐藏加载状态
                            loading.value = false;
                        }
                    };

                    // 检查是否有协议类型的选项
                    const hasAgreementOption = computed(() => {
                        return form.value.customMessages.some(msg => msg && msg.agreement !== undefined);
                    });
                    
                    // 预览协议对话框
                    const previewAgreement = () => {
                        // 查找第一个协议类型的选项
                        const agreementOption = form.value.customMessages.find(msg => msg && msg.agreement !== undefined);
                        if (agreementOption) {
                            // 尝试处理可能的乱码问题
                            try {
                                let agreement = agreementOption.agreement;
                                console.log("原始协议内容前40字符:", agreement.substring(0, 40));
                                
                                // 检测是否含有常见的乱码特征
                                if (agreement.includes('ç') || agreement.includes('æ') || 
                                    agreement.includes('&#') || (agreement.includes('&') && agreement.includes(';'))) {
                                    
                                    console.log("检测到可能的乱码，尝试修复");
                                    
                                    // 尝试HTML实体解码
                                    let tempDiv = document.createElement('div');
                                    tempDiv.innerHTML = agreement;
                                    let decodedText = tempDiv.textContent;
                                    
                                    if (decodedText && decodedText.length > 0) {
                                        agreement = decodedText;
                                        console.log("HTML实体解码后前40字符:", agreement.substring(0, 40));
                                    }
                                    
                                    // 如果仍有问题，尝试UTF-8解码
                                    if (agreement.includes('ç') || agreement.includes('æ')) {
                                        try {
                                            agreement = decodeURIComponent(escape(agreement));
                                            console.log("UTF-8解码后前40字符:", agreement.substring(0, 40));
                                        } catch (e) {
                                            console.error("UTF-8解码失败:", e);
                                        }
                                    }
                                }
                                
                                // 标准化换行符
                                agreement = agreement.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
                                
                                // 删除多余的空行
                                agreement = agreement.replace(/\n{3,}/g, '\n\n');
                                
                                // 更新协议内容
                                const previewObj = { ...agreementOption, agreement };
                                previewAgreementOption.value = previewObj;
                            } catch (e) {
                                console.error("处理协议内容时出错:", e);
                                previewAgreementOption.value = agreementOption;
                            }
                            
                            showAgreementPreview.value = true;
                        } else {
                            ElMessage.info('没有找到协议类型的选项');
                        }
                    };
                    
                    // 添加选择选项的处理函数
                    const selectOption = (option) => {
                        previewSelected.value = option.text;
                        
                        // 找到选项对应的下标
                        const index = form.value.options.findIndex(opt => opt.text === option.text);
                        if (index === -1) return;
                        
                        // 找到对应的消息配置
                        const messageConfig = form.value.customMessages[index];
                        if (!messageConfig) return;
                        
                        // 根据消息类型显示不同的效果
                        if (messageConfig.agreement !== undefined) {
                            // 如果是协议类型，显示协议预览
                            try {
                                let agreement = messageConfig.agreement;
                                
                                // 标准化换行符和清理空行
                                agreement = agreement.replace(/\r\n/g, '\n').replace(/\r/g, '\n').replace(/\n{3,}/g, '\n\n');
                                
                                const previewObj = { ...messageConfig, agreement };
                                previewAgreementOption.value = previewObj;
                                showAgreementPreview.value = true;
                            } catch (e) {
                                console.error("处理协议内容时出错:", e);
                                previewAgreementOption.value = messageConfig;
                                showAgreementPreview.value = true;
                            }
                        } else if (messageConfig.message !== undefined) {
                            // 如果是消息类型，显示自定义消息预览弹窗
                            previewMessageOption.value = messageConfig;
                            showMessagePreview.value = true;
                        }
                    };
                    
                    // 关闭协议预览
                    const closeAgreementPreview = () => {
                        showAgreementPreview.value = false;
                    };
                    
                    // 简单的Markdown格式转换
                    const formatMarkdown = (text) => {
                        if (!text) return '';
                        
                        // 基本安全处理
                        text = text.replace(/</g, '&lt;').replace(/>/g, '&gt;');
                        
                        // 替换标题
                        text = text.replace(/^### (.*$)/gim, '<h3>$1</h3>');
                        text = text.replace(/^## (.*$)/gim, '<h2>$1</h2>');
                        text = text.replace(/^# (.*$)/gim, '<h1>$1</h1>');
                        
                        // 替换粗体和斜体
                        text = text.replace(/\*\*(.*?)\*\*/gm, '<strong>$1</strong>');
                        text = text.replace(/\*(.*?)\*/gm, '<em>$1</em>');
                        
                        // 替换链接
                        text = text.replace(/\[(.*?)\]\((.*?)\)/gm, '<a href="$2" target="_blank">$1</a>');
                        
                        // 替换列表
                        let lines = text.split('\n');
                        let inList = false;
                        let listType = '';
                        
                        for (let i = 0; i < lines.length; i++) {
                            // 有序列表项
                            if (lines[i].match(/^\d+\.\s+(.*)$/)) {
                                if (!inList || listType !== 'ol') {
                                    lines[i] = '<ol><li>' + lines[i].replace(/^\d+\.\s+(.*)$/, '$1') + '</li>';
                                    inList = true;
                                    listType = 'ol';
                                } else {
                                    lines[i] = '<li>' + lines[i].replace(/^\d+\.\s+(.*)$/, '$1') + '</li>';
                                }
                            }
                            // 无序列表项
                            else if (lines[i].match(/^-\s+(.*)$/)) {
                                if (!inList || listType !== 'ul') {
                                    lines[i] = '<ul><li>' + lines[i].replace(/^-\s+(.*)$/, '$1') + '</li>';
                                    inList = true;
                                    listType = 'ul';
                                } else {
                                    lines[i] = '<li>' + lines[i].replace(/^-\s+(.*)$/, '$1') + '</li>';
                                }
                            }
                            // 不是列表项，结束之前的列表
                            else if (inList) {
                                lines[i-1] += (listType === 'ol') ? '</ol>' : '</ul>';
                                inList = false;
                            }
                        }
                        
                        // 如果文档结束但列表还没结束
                        if (inList) {
                            lines[lines.length-1] += (listType === 'ol') ? '</ol>' : '</ul>';
                        }
                        
                        text = lines.join('\n');
                        
                        // 替换水平线
                        text = text.replace(/^---$/gim, '<hr>');
                        
                        // 替换换行，但保留段落结构
                        text = text.replace(/\n\n/gm, '</p><p>');
                        text = text.replace(/\n/gm, '<br>');
                        
                        // 确保文本被段落包裹
                        if (!text.startsWith('<h') && !text.startsWith('<p>')) {
                            text = '<p>' + text;
                        }
                        if (!text.endsWith('</p>') && !text.endsWith('</h1>') && !text.endsWith('</h2>') && !text.endsWith('</h3>')) {
                            text = text + '</p>';
                        }
                        
                        return text;
                    };

                    // 初始化
                    fetchData();

                    // 如果一开始没有选项，添加一个默认选项
                    if (form.value.options.length === 0) {
                        addOption();
                    }

                    // 关闭消息预览
                    const closeMessagePreview = () => {
                        showMessagePreview.value = false;
                    };
                    
                    // 处理消息按钮点击
                    const handleMessageButtonClick = () => {
                        const messageConfig = previewMessageOption.value;
                        // 关闭消息预览
                        showMessagePreview.value = false;
                        
                        // 如果有跳转地址，模拟跳转
                        if (messageConfig && messageConfig.redirectUrl) {
                            console.log(`预览模式 - 模拟跳转到: ${messageConfig.redirectUrl}`);
                            ElMessage.info(`预览模式 - 将跳转到: ${messageConfig.redirectUrl}`);
                        }
                    };
                    
                    // 处理附加按钮点击
                    const handleExtraButtonClick = (btn) => {
                        // 关闭消息预览
                        showMessagePreview.value = false;
                        
                        // 如果有跳转地址，模拟跳转
                        if (btn && btn.url) {
                            console.log(`预览模式 - 模拟跳转到: ${btn.url}`);
                            ElMessage.info(`预览模式 - 将跳转到: ${btn.url}`);
                        }
                    };
                    
                    // 获取按钮样式
                    const getButtonStyle = (type) => {
                        const styles = {
                            default: {
                                backgroundColor: '#ffffff',
                                color: '#606266',
                                border: '1px solid #dcdfe6'
                            },
                            primary: {
                                backgroundColor: '#409EFF',
                                color: '#ffffff',
                                border: 'none'
                            },
                            success: {
                                backgroundColor: '#67C23A',
                                color: '#ffffff',
                                border: 'none'
                            },
                            warning: {
                                backgroundColor: '#E6A23C',
                                color: '#ffffff',
                                border: 'none'
                            },
                            danger: {
                                backgroundColor: '#F56C6C',
                                color: '#ffffff',
                                border: 'none'
                            }
                        };
                        
                        return styles[type] || styles.default;
                    };
                    
                    // 处理提交按钮点击
                    const handleSubmitButtonClick = () => {
                        // 如果未选择选项，提示用户
                        if (!previewSelected.value) {
                            ElMessage.warning('请先选择一个选项');
                            return;
                        }
                        
                        // 找到选中选项对应的索引
                        const index = form.value.options.findIndex(opt => opt.text === previewSelected.value);
                        if (index === -1) return;
                        
                        // 直接触发该选项的点击事件
                        const option = form.value.options.find(opt => opt.text === previewSelected.value);
                        if (option) {
                            selectOption(option);
                        }
                    };
                    
                    // 添加按钮
                    const addButton = (index) => {
                        // 确保customMessages[index]存在
                        if (!form.value.customMessages[index]) return;
                        
                        // 如果没有extraButtons数组，创建一个
                        if (!form.value.customMessages[index].extraButtons) {
                            form.value.customMessages[index].extraButtons = [];
                        }
                        
                        // 添加一个新按钮
                        form.value.customMessages[index].extraButtons.push({
                            text: '附加按钮',
                            type: 'default',
                            url: ''
                        });
                    };
                    
                    // 删除按钮
                    const removeButton = (index, btnIndex) => {
                        // 确保customMessages[index]存在且extraButtons数组存在
                        if (!form.value.customMessages[index] || !form.value.customMessages[index].extraButtons) return;
                        
                        // 确保btnIndex有效
                        if (btnIndex < 0 || btnIndex >= form.value.customMessages[index].extraButtons.length) return;
                        
                        // 从数组中删除指定索引的按钮
                        form.value.customMessages[index].extraButtons.splice(btnIndex, 1);
                    };

                    // 在初始化时隐藏加载动画
                    const hideInitialLoading = () => {
                        const initialLoading = document.getElementById('initial-loading');
                        const appContainer = document.getElementById('app');
                        if (initialLoading && appContainer) {
                            initialLoading.style.opacity = '0';
                            appContainer.style.display = 'block';
                            setTimeout(() => {
                                initialLoading.style.display = 'none';
                            }, 300);
                        }
                    };

                    return {
                        form,
                        previewDialog,
                        previewSelected,
                        sortedOptions,
                        addOption,
                        removeOption,
                        saveConfig,
                        optionTypes,
                        changeOptionType,
                        showAgreementPreview,
                        previewAgreementOption,
                        previewAgreement,
                        closeAgreementPreview,
                        formatMarkdown,
                        hasAgreementOption,
                        selectOption,
                        showMessagePreview,
                        previewMessageOption,
                        closeMessagePreview,
                        handleMessageButtonClick,
                        handleSubmitButtonClick,
                        addButton,
                        removeButton,
                        handleExtraButtonClick,
                        getButtonStyle,
                        hideInitialLoading
                    };
                }
            }).use(ElementPlus).mount('#app');

            // 如果数据加载失败，也要隐藏初始加载动画
            setTimeout(hideInitialLoading, 5000);
        });
    </script>
</body>
</html> 