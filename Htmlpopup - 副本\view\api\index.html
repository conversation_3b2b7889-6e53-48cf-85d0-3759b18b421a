<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>HTML弹窗设置</title>
    <style>
        /* 颜色块样式 */
        .color-block {
            display: inline-block;
            width: 18px;
            height: 18px;
            border-radius: 4px;
            margin-right: 6px;
            vertical-align: middle;
            border: 1px solid #dcdfe6;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        /* 响应式样式调整 */
        @media screen and (max-width: 768px) {
            .el-form-item {
                margin-bottom: 15px;
            }

            .el-form {
                padding: 10px;
            }

            .el-form-item__label {
                width: auto !important;
                text-align: left;
                float: none;
                display: block;
                margin-bottom: 8px;
                padding: 0;
            }

            .el-form-item__content {
                margin-left: 0 !important;
            }

            .el-radio-group {
                display: flex;
                flex-direction: column;
                gap: 10px;
            }

            .el-radio {
                margin-right: 0;
                margin-bottom: 5px;
            }

            .w-e-toolbar {
                flex-wrap: wrap;
            }

            .w-e-text-container {
                height: 300px !important;
            }

            .editor-container {
                margin: 0 -10px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <el-card shadow="never">
            <template #header>
                <div class="card-header">
                    <span>HTML弹窗设置（<span style="color: #f56c6c;">提示：店铺名称不能默认不然用不了商家8888不可行</span>）</span>
                </div>
            </template>
            <el-form :model="form" label-width="120px">




                <el-form-item label="内容类型：">
                    <el-radio-group v-model="form.contentType">
                        <el-radio label="builtin">内置模板</el-radio>
                    </el-radio-group>
                    <div style="margin-top: 8px; color: #909399; font-size: 12px;">
                        <i class="el-icon-info"></i> 使用预设的内置模板，支持自定义文字内容和链接配置
                    </div>
                </el-form-item>

                <el-form-item label="内置模板：" v-if="form.contentType === 'builtin'">
                    <el-select v-model="form.builtinTemplate" placeholder="请选择内置模板" style="width: 300px;" @change="onTemplateChange">
                        <el-option
                            v-for="template in availableTemplates"
                            :key="template.value"
                            :label="template.label"
                            :value="template.value"
                        ></el-option>
                    </el-select>
                    <div style="margin-top: 8px; color: #67c23a; font-size: 12px;">
                        💡 内置模板样式已预设，您只能修改文字内容和按钮跳转链接
                    </div>
                    <div style="margin-top: 4px; color: #e6a23c; font-size: 11px;">
                        🔒 模板的HTML结构、CSS样式、布局等均不可修改，仅开放文本和链接编辑权限
                    </div>
                    <div v-if="availableTemplates.length === 0" style="margin-top: 8px; color: #f56c6c; font-size: 12px;">
                        ⚠️ 暂无可用的内置模板，请联系管理员添加
                    </div>
                </el-form-item>

                <!-- 内置模板可编辑字段 -->
                <div v-if="form.contentType === 'builtin' && form.builtinTemplate">
                    <el-divider content-position="left">
                        <span style="color: #409eff; font-weight: bold;">📝 可编辑内容（仅限文字和链接）</span>
                    </el-divider>
                    <el-alert
                        title="编辑说明"
                        type="info"
                        :closable="false"
                        style="margin-bottom: 15px;"
                    >
                        <template #default>
                            <p style="margin: 0; font-size: 13px;">
                                🔒 <strong>样式保护：</strong>内置模板的HTML结构、CSS样式、颜色、字体、布局等均已锁定，无法修改<br>
                                ✏️ <strong>可编辑项：</strong>仅允许修改文字内容和按钮跳转链接，确保模板的视觉一致性
                            </p>
                        </template>
                    </el-alert>

                    <!-- 购买协议模板的可编辑字段 -->
                    <div v-if="form.builtinTemplate === 'purchase_agreement'">
                        <div style="background: #f0f9ff; border: 1px solid #bae6fd; border-radius: 6px; padding: 12px; margin-bottom: 15px;">
                            <div style="color: #0369a1; font-weight: bold; margin-bottom: 8px;">🛡️ 购买协议模板 - 文字内容编辑</div>
                            <div style="color: #64748b; font-size: 12px;">此模板专为电商平台设计，样式和布局已优化，您只需填写平台相关信息即可</div>
                        </div>

                        <el-form-item label="平台名称：">
                            <el-input v-model="form.templateFields.platformName" placeholder="请输入平台名称" maxlength="50" show-word-limit style="max-width: 400px;" />
                            <div style="margin-top: 4px; color: #909399; font-size: 11px;">📍 将显示在协议标题中，建议包含平台全称</div>
                        </el-form-item>

                        <el-form-item label="平台频道：">
                            <el-input v-model="form.templateFields.platformChannel" placeholder="请输入平台频道信息" maxlength="100" show-word-limit style="max-width: 400px;" />
                            <div style="margin-top: 4px; color: #909399; font-size: 11px;">📢 如：@xhyfkw，用于标识平台官方频道</div>
                        </el-form-item>

                        <el-form-item label="温馨提示：">
                            <el-input v-model="form.templateFields.warmTip" type="textarea" :rows="2" placeholder="请输入温馨提示内容" maxlength="200" show-word-limit />
                            <div style="margin-top: 4px; color: #909399; font-size: 11px;">⚠️ 重要提醒信息，将以醒目样式显示</div>
                        </el-form-item>

                        <el-divider content-position="left">
                            <span style="color: #f59e0b; font-weight: bold;">🔗 按钮链接配置</span>
                        </el-divider>

                        <el-form-item label="开店链接：">
                            <el-input v-model="form.templateFields.shopLink" placeholder="请输入开店链接" style="max-width: 500px;" />
                            <div style="margin-top: 4px; color: #909399; font-size: 11px;">🏪 "开店成为商家"按钮的跳转链接</div>
                        </el-form-item>

                        <el-form-item label="客服链接：">
                            <el-input v-model="form.templateFields.serviceLink" placeholder="请输入客服链接" style="max-width: 500px;" />
                            <div style="margin-top: 4px; color: #909399; font-size: 11px;">💬 "联系客服"按钮的跳转链接</div>
                        </el-form-item>

                        <el-divider content-position="left">
                            <span style="color: #8b5cf6; font-weight: bold;">✏️ 按钮文字自定义</span>
                        </el-divider>

                        <el-form-item label="开店按钮文字：">
                            <el-input v-model="form.templateFields.shopButtonText" placeholder="开店成为商家赚米" maxlength="20" show-word-limit style="max-width: 300px;" />
                            <div style="margin-top: 4px; color: #909399; font-size: 11px;">🎯 按钮显示的文字内容，建议简洁有吸引力</div>
                        </el-form-item>

                        <el-form-item label="客服按钮文字：">
                            <el-input v-model="form.templateFields.serviceButtonText" placeholder="联系平台微信客服" maxlength="20" show-word-limit style="max-width: 300px;" />
                            <div style="margin-top: 4px; color: #909399; font-size: 11px;">🎯 按钮显示的文字内容，建议明确表达联系方式</div>
                        </el-form-item>
                    </div>

                    <!-- 平台规则模板的可编辑字段 -->
                    <div v-if="form.builtinTemplate === 'platform_rules'">
                        <div style="background: #f0fdf4; border: 1px solid #bbf7d0; border-radius: 6px; padding: 12px; margin-bottom: 15px;">
                            <div style="color: #15803d; font-weight: bold; margin-bottom: 8px;">📋 平台规则模板 - 文字内容编辑</div>
                            <div style="color: #64748b; font-size: 12px;">此模板用于展示平台使用规则，样式已预设，您只需填写具体规则内容</div>
                        </div>

                        <el-form-item label="规则标题：">
                            <el-input v-model="form.templateFields.rulesTitle" placeholder="平台使用规则" maxlength="30" show-word-limit style="max-width: 400px;" />
                            <div style="margin-top: 4px; color: #909399; font-size: 11px;">📝 规则页面的主标题</div>
                        </el-form-item>

                        <el-form-item label="用户须知内容：">
                            <el-input v-model="form.templateFields.userNotice" type="textarea" :rows="4" placeholder="请输入用户须知内容，每行一条规则" />
                            <div style="margin-top: 4px; color: #909399; font-size: 11px;">📌 每行一条须知，系统会自动添加序号</div>
                        </el-form-item>

                        <el-form-item label="交易规则内容：">
                            <el-input v-model="form.templateFields.tradeRules" type="textarea" :rows="4" placeholder="请输入交易规则内容，每行一条规则" />
                            <div style="margin-top: 4px; color: #909399; font-size: 11px;">📌 每行一条规则，系统会自动添加序号</div>
                        </el-form-item>

                        <el-form-item label="客服提示：">
                            <el-input v-model="form.templateFields.serviceNotice" placeholder="如有疑问，请联系平台客服" maxlength="100" show-word-limit style="max-width: 400px;" />
                            <div style="margin-top: 4px; color: #909399; font-size: 11px;">💬 底部客服联系提示信息</div>
                        </el-form-item>
                    </div>

                    <!-- 其他自定义模板的通用编辑提示 -->
                    <div v-if="form.builtinTemplate !== 'purchase_agreement' && form.builtinTemplate !== 'platform_rules'">
                        <el-alert
                            title="🔒 管理员自定义模板"
                            type="warning"
                            :closable="false"
                            style="margin-bottom: 20px;"
                        >
                            <template #default>
                                <p style="margin: 0; line-height: 1.6;">
                                    <strong>模板锁定：</strong>此模板为管理员在后台预设的自定义模板，所有内容（包括文字、样式、链接）均已锁定。<br>
                                    <strong>编辑权限：</strong>商家用户无法修改此模板的任何内容。<br>
                                    <strong>如需修改：</strong>请联系管理员在后台管理界面进行编辑，或选择其他可编辑的模板。
                                </p>
                            </template>
                        </el-alert>
                    </div>
                </div>



                <el-form-item label="模板预览：" v-if="form.contentType === 'builtin'">
                    <div style="border: 1px solid #dcdfe6; border-radius: 4px; padding: 15px; background: #f9f9f9; max-height: 400px; overflow-y: auto;">
                        <div style="margin-bottom: 8px; font-weight: bold; color: #606266;">
                            🔍 内置模板实时预览
                            <span style="font-weight: normal; color: #909399; font-size: 12px; margin-left: 10px;">
                                （样式已锁定，仅显示您填写的内容效果）
                            </span>
                        </div>
                        <div v-html="getBuiltinTemplate()" style="border: 1px solid #e4e7ed; background: white; padding: 10px; border-radius: 4px; min-height: 100px;"></div>
                        <div style="margin-top: 8px; color: #67c23a; font-size: 11px;">
                            💡 预览会实时反映您的文字和链接修改，但模板的整体样式和布局保持不变
                        </div>
                    </div>
                </el-form-item>



                <el-form-item>
                    <el-button type="primary" :loading="loading" @click="save">保存设置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
    </div>

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>

    <script>
        const { createApp, ref, reactive, onMounted, watch, nextTick } = Vue;
        const { ElMessage } = ElementPlus;

        createApp({
            setup() {
                const loading = ref(false);
                const form = reactive({
                    contentType: 'builtin',
                    builtinTemplate: 'purchase_agreement',
                    templateFields: {
                        // 购买协议模板字段
                        platformName: '小火羊云寄售官方频道',
                        platformChannel: '@xhyfkw',
                        warmTip: '本站不提供任何担保、私下交易被骗一律与本站无关。',
                        shopLink: 'https://www.zzrongtong.cn/merchant/',
                        serviceLink: 'https://work.weixin.qq.com/kfid/kfcf72f7282e80c5ad2',
                        shopButtonText: '开店成为商家赚米',
                        serviceButtonText: '联系平台微信客服',
                        // 平台规则模板字段
                        rulesTitle: '平台使用规则',
                        userNotice: '1. 请遵守平台相关规定，文明使用平台服务\n2. 禁止发布违法违规内容，维护良好的平台环境\n3. 保护个人隐私信息，谨防诈骗',
                        tradeRules: '1. 所有交易请通过平台正规渠道进行\n2. 如遇问题请及时联系客服处理\n3. 平台将保障用户合法权益',
                        serviceNotice: '如有疑问，请联系平台客服'
                    }
                });

                const showPreview = ref(false);
                const availableTemplates = ref([]);
                const customTemplateContent = ref(''); // 存储管理员自定义模板的内容

                // 获取内置模板内容
                const getBuiltinTemplate = () => {
                    const fields = form.templateFields;

                    // 首先检查是否是管理员自定义模板
                    const selectedTemplate = availableTemplates.value.find(t => t.value === form.builtinTemplate);

                    // 如果是管理员自定义模板（不是内置的两个可编辑模板），则显示实际内容
                    if (selectedTemplate && !['purchase_agreement', 'platform_rules'].includes(form.builtinTemplate)) {
                        // 如果有缓存的内容，直接返回
                        if (customTemplateContent.value) {
                            return customTemplateContent.value;
                        }

                        // 否则显示加载提示
                        return `
                            <div style="padding: 20px; text-align: center; background: #f8f9fa; border: 2px dashed #dee2e6; border-radius: 8px;">
                                <div style="color: #6c757d; font-size: 16px; margin-bottom: 10px;">
                                    🔒 管理员自定义模板
                                </div>
                                <div style="color: #495057; font-size: 14px; line-height: 1.6;">
                                    <strong>模板名称：</strong>${selectedTemplate.label}<br>
                                    <strong>模板说明：</strong>此模板由管理员预设，内容已锁定<br>
                                    <strong>编辑权限：</strong>商家用户无法修改任何内容<br>
                                    <strong>实际效果：</strong>正在加载模板内容...
                                </div>
                                <div style="margin-top: 15px; padding: 10px; background: #e9ecef; border-radius: 4px; font-size: 12px; color: #6c757d;">
                                    💡 如需修改此模板，请联系管理员在后台管理界面进行编辑
                                </div>
                            </div>
                        `;
                    }

                    if (form.builtinTemplate === 'purchase_agreement') {
                        return `
                            <div id="buy-protocol">
                                <p><strong><span style="color:#303133;"><span style="font-size:18px;">${fields.platformName}${fields.platformChannel}</span></span></strong></p>
                                <p><strong><span style="color:#ff6600;"><span style="font-size:18px;">温馨提示：${fields.warmTip}</span></span></strong></p>
                                <p><span style="color:#ff00ff;">1、本平台仅提供自动发卡、寄售服务，本平台非售卖销售商，有问题联系店铺客服或者平台投诉订单处理，非商品卡密问题本站不予受理售后争议</span></p>
                                <p><span style="color:#27ae60;">2、平台提供卡密查询时间为1个月（购买后请自行保存）如遇假卡/欺诈/涉黄赌/等违规商品请当天23点50前去平台投诉订单处理，若是超过24点后，平台放款给商家，不再受理任何问题。</span></p>
                                <p><span style="color:#ae2760;">3、为了方便售后建议您的每次购物都录个视频，一直到商品正常使用没问题在取消录制哦！</span></p>
                                <p><span style="color:#ff0000;">若遇到任何无法处理的问题，请当天在平台投诉订单，平台官方客服介入处理</span></p>
                                <div style="text-align: center; white-space: nowrap; width: 100%; box-sizing: border-box; margin: 10px 0;">
                                    <a href="${fields.shopLink}" style="display: inline-block; width: calc(50% - 5px); background-color: #6600ff; color: white; border: none; padding: 8px 4px; margin-right: 10px; border-radius: 5px; text-decoration: none; cursor: pointer; box-sizing: border-box; text-align: center; font-size: 12px;">${fields.shopButtonText}</a>
                                    <a href="${fields.serviceLink}" style="display: inline-block; width: calc(50% - 5px); background-color: #6600ff; color: white; border: none; padding: 8px 4px; border-radius: 5px; text-decoration: none; cursor: pointer; box-sizing: border-box; text-align: center; font-size: 12px;">${fields.serviceButtonText}</a>
                                </div>
                                <hr style="border: none; border-top: 1px dashed #000; margin: 20px 0;">
                                <p><span style="font-size:16px;"><strong><span style="color:#ff0000;">防骗提醒</span></strong><span style="color:#ff0000;">：</span></span><span style="color:#6600ff;">1.卡密内容为"联系QQ取卡或者QQ群拿货" 2.以各种理由推脱到第二天发货 3.商品有问题，卖家不售后 4.承诺充值返现 5.购买的商品为实物，需要快递发货的。请及时联系我们平台客服</span></p>
                            </div>
                        `;
                    } else if (form.builtinTemplate === 'platform_rules') {
                        // 处理用户须知和交易规则的换行
                        const userNoticeLines = fields.userNotice.split('\n').map((line, index) =>
                            `<p>${index + 1}. ${line.replace(/^\d+\.\s*/, '')}</p>`
                        ).join('');

                        const tradeRulesLines = fields.tradeRules.split('\n').map((line, index) =>
                            `<p>${index + 1}. ${line.replace(/^\d+\.\s*/, '')}</p>`
                        ).join('');

                        return `
                            <div style="padding: 20px; line-height: 1.6;">
                                <h3 style="color: #333; text-align: center; margin-bottom: 20px;">${fields.rulesTitle}</h3>
                                <div style="background: #f0f8ff; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                                    <h4 style="color: #1e90ff; margin-top: 0;">用户须知</h4>
                                    ${userNoticeLines}
                                </div>
                                <div style="background: #fff5ee; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                                    <h4 style="color: #ff6347; margin-top: 0;">交易规则</h4>
                                    ${tradeRulesLines}
                                </div>
                                <div style="text-align: center; margin-top: 20px;">
                                    <p style="color: #666;">${fields.serviceNotice}</p>
                                </div>
                            </div>
                        `;
                    }

                    // 默认返回购买协议模板的默认内容
                    return `
                        <div id="buy-protocol">
                            <p><strong><span style="color:#303133;"><span style="font-size:18px;">小火羊云寄售官方频道@xhyfkw</span></span></strong></p>
                            <p><strong><span style="color:#ff6600;"><span style="font-size:18px;">温馨提示：本站不提供任何担保、私下交易被骗一律与本站无关。</span></span></strong></p>
                            <p><span style="color:#ff00ff;">1、本平台仅提供自动发卡、寄售服务，本平台非售卖销售商，有问题联系店铺客服或者平台投诉订单处理，非商品卡密问题本站不予受理售后争议</span></p>
                            <p><span style="color:#27ae60;">2、平台提供卡密查询时间为1个月（购买后请自行保存）如遇假卡/欺诈/涉黄赌/等违规商品请当天23点50前去平台投诉订单处理，若是超过24点后，平台放款给商家，不再受理任何问题。</span></p>
                            <p><span style="color:#ae2760;">3、为了方便售后建议您的每次购物都录个视频，一直到商品正常使用没问题在取消录制哦！</span></p>
                            <p><span style="color:#ff0000;">若遇到任何无法处理的问题，请当天在平台投诉订单，平台官方客服介入处理</span></p>
                            <div style="text-align: center; white-space: nowrap; width: 100%; box-sizing: border-box; margin: 10px 0;">
                                <a href="https://www.zzrongtong.cn/merchant/" style="display: inline-block; width: calc(50% - 5px); background-color: #6600ff; color: white; border: none; padding: 8px 4px; margin-right: 10px; border-radius: 5px; text-decoration: none; cursor: pointer; box-sizing: border-box; text-align: center; font-size: 12px;">开店成为商家赚米</a>
                                <a href="https://work.weixin.qq.com/kfid/kfcf72f7282e80c5ad2" style="display: inline-block; width: calc(50% - 5px); background-color: #6600ff; color: white; border: none; padding: 8px 4px; border-radius: 5px; text-decoration: none; cursor: pointer; box-sizing: border-box; text-align: center; font-size: 12px;">联系平台微信客服</a>
                            </div>
                            <hr style="border: none; border-top: 1px dashed #000; margin: 20px 0;">
                            <p><span style="font-size:16px;"><strong><span style="color:#ff0000;">防骗提醒</span></strong><span style="color:#ff0000;">：</span></span><span style="color:#6600ff;">1.卡密内容为"联系QQ取卡或者QQ群拿货" 2.以各种理由推脱到第二天发货 3.商品有问题，卖家不售后 4.承诺充值返现 5.购买的商品为实物，需要快递发货的。请及时联系我们平台客服</span></p>
                        </div>
                    `;
                };







                // 模板切换处理
                const onTemplateChange = async (templateValue) => {
                    // 提示用户模板切换会重置内容
                    ElMessage.info('已切换模板，内容已重置为默认值');

                    // 如果是管理员自定义模板，获取其内容
                    if (templateValue && !['purchase_agreement', 'platform_rules'].includes(templateValue)) {
                        await fetchCustomTemplateContent(templateValue);
                    } else {
                        // 清空自定义模板内容缓存
                        customTemplateContent.value = '';
                    }

                    // 根据选择的模板重置字段为默认值
                    if (templateValue === 'purchase_agreement') {
                        form.templateFields.platformName = '小火羊云寄售官方频道';
                        form.templateFields.platformChannel = '@xhyfkw';
                        form.templateFields.warmTip = '本站不提供任何担保、私下交易被骗一律与本站无关。';
                        form.templateFields.shopLink = 'https://www.zzrongtong.cn/merchant/';
                        form.templateFields.serviceLink = 'https://work.weixin.qq.com/kfid/kfcf72f7282e80c5ad2';
                        form.templateFields.shopButtonText = '开店成为商家赚米';
                        form.templateFields.serviceButtonText = '联系平台微信客服';
                    } else if (templateValue === 'platform_rules') {
                        form.templateFields.rulesTitle = '平台使用规则';
                        form.templateFields.userNotice = '1. 请遵守平台相关规定，文明使用平台服务\n2. 禁止发布违法违规内容，维护良好的平台环境\n3. 保护个人隐私信息，谨防诈骗';
                        form.templateFields.tradeRules = '1. 所有交易请通过平台正规渠道进行\n2. 如遇问题请及时联系客服处理\n3. 平台将保障用户合法权益';
                        form.templateFields.serviceNotice = '如有疑问，请联系平台客服';
                    }
                };

                // 获取管理员自定义模板的内容
                const fetchCustomTemplateContent = async (templateKey) => {
                    try {
                        const res = await axios.post("/plugin/Htmlpopup/api/getTemplateContent", {
                            templateKey: templateKey
                        });
                        if (res.data?.code === 200) {
                            customTemplateContent.value = res.data.data.content;
                        } else {
                            customTemplateContent.value = `
                                <div style="padding: 20px; text-align: center; color: #f56c6c;">
                                    ⚠️ 模板内容加载失败：${res.data?.msg || '未知错误'}
                                </div>
                            `;
                        }
                    } catch (error) {
                        customTemplateContent.value = `
                            <div style="padding: 20px; text-align: center; color: #f56c6c;">
                                ⚠️ 模板内容加载失败，请刷新页面重试
                            </div>
                        `;
                    }
                };

                onMounted(() => {
                    fetchAvailableTemplates();
                    fetchData();
                });

                // 获取可用的内置模板列表
                const fetchAvailableTemplates = async () => {
                    try {
                        const res = await axios.post("/plugin/Htmlpopup/api/getAvailableTemplates");
                        if (res.data?.code === 200) {
                            availableTemplates.value = res.data.data || [];
                        } else {
                            // 使用默认模板列表作为备用
                            availableTemplates.value = [
                                { value: 'purchase_agreement', label: '购买协议模板' },
                                { value: 'platform_rules', label: '平台规则模板' }
                            ];
                        }
                    } catch (error) {
                        // 使用默认模板列表作为备用
                        availableTemplates.value = [
                            { value: 'purchase_agreement', label: '购买协议模板' },
                            { value: 'platform_rules', label: '平台规则模板' }
                        ];
                    }
                };

                // 获取数据
                const fetchData = async () => {
                    try {
                        const res = await axios.post("/plugin/Htmlpopup/api/fetchData");
                        if (res.data?.code === 200) {
                            const data = res.data.data;
                            // 移除status处理，由全局开关控制
                            // 强制使用内置模板模式
                            form.contentType = 'builtin';


                            // contentType已在上面处理，这里删除重复设置
                            form.builtinTemplate = data.builtin_template || 'purchase_agreement';



                            // 加载模板字段数据
                            if (data.template_fields) {
                                try {
                                    const templateFields = typeof data.template_fields === 'string'
                                        ? JSON.parse(data.template_fields)
                                        : data.template_fields;
                                    Object.assign(form.templateFields, templateFields);
                                } catch (e) {
                                    // 解析模板字段数据失败，使用默认值
                                }
                            }

                            // 如果是管理员自定义模板，获取其内容
                            if (form.contentType === 'builtin' && form.builtinTemplate &&
                                !['purchase_agreement', 'platform_rules'].includes(form.builtinTemplate)) {
                                await fetchCustomTemplateContent(form.builtinTemplate);
                            }
                        } else {
                            ElMessage.error(res.data?.msg || '获取数据失败');
                        }
                    } catch (error) {
                        ElMessage.error('获取数据失败');
                    }
                };

                // 保存数据
                const save = async () => {
                    // 验证内容
                    if (form.contentType === 'builtin' && !form.builtinTemplate) {
                        ElMessage.warning('请选择内置模板');
                        return;
                    }

                    // 验证内置模板的必填字段
                    if (form.contentType === 'builtin') {
                        if (form.builtinTemplate === 'purchase_agreement') {
                            if (!form.templateFields.platformName.trim()) {
                                ElMessage.warning('请输入平台名称');
                                return;
                            }
                            if (!form.templateFields.shopLink.trim()) {
                                ElMessage.warning('请输入开店链接');
                                return;
                            }
                            if (!form.templateFields.serviceLink.trim()) {
                                ElMessage.warning('请输入客服链接');
                                return;
                            }
                            // 验证链接格式
                            const urlPattern = /^https?:\/\/.+/;
                            if (!urlPattern.test(form.templateFields.shopLink)) {
                                ElMessage.warning('开店链接格式不正确，请输入完整的URL（以http://或https://开头）');
                                return;
                            }
                            if (!urlPattern.test(form.templateFields.serviceLink)) {
                                ElMessage.warning('客服链接格式不正确，请输入完整的URL（以http://或https://开头）');
                                return;
                            }
                        } else if (form.builtinTemplate === 'platform_rules') {
                            if (!form.templateFields.rulesTitle.trim()) {
                                ElMessage.warning('请输入规则标题');
                                return;
                            }
                            if (!form.templateFields.userNotice.trim()) {
                                ElMessage.warning('请输入用户须知内容');
                                return;
                            }
                            if (!form.templateFields.tradeRules.trim()) {
                                ElMessage.warning('请输入交易规则内容');
                                return;
                            }
                        }
                    }

                    loading.value = true;
                    try {
                        const formData = new FormData();
                        // 内置模板模式保存空字符串
                        formData.append('content', '');
                        formData.append('content_type', form.contentType);
                        formData.append('builtin_template', form.builtinTemplate);
                        formData.append('template_fields', JSON.stringify(form.templateFields));

                        const res = await axios.post("/plugin/Htmlpopup/api/save", formData);
                        if (res.data?.code === 200) {
                            ElMessage.success('保存成功');
                            await fetchData();
                        } else {
                            ElMessage.error(res.data?.msg || '保存失败');
                        }
                    } catch (error) {
                        ElMessage.error('保存失败');
                    } finally {
                        loading.value = false;
                    }
                };



                return {
                    loading,
                    form,
                    showPreview,
                    availableTemplates,
                    customTemplateContent,
                    save,
                    getBuiltinTemplate,
                    onTemplateChange,
                    fetchCustomTemplateContent
                };
            }
        })
        .use(ElementPlus)
        .mount('#app');
    </script>
</body>
</html>
