<?php

namespace plugin\Fengzeroexport\controller;

use app\common\controller\BasePlugin;
use think\facade\Db;
use think\facade\View;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Cell\DataType;

class User extends BasePlugin
{
    protected $scene = ['user'];
    protected $noNeedLogin = [];

    // 商家端页面
    public function merchant()
    {
        return View::fetch('merchant');
    }

    // 商家端导出功能
    public function merchantExport()
    {
        // 验证商家用户权限
        if (!$this->user || !$this->user->id) {
            return json(['code' => 403, 'msg' => '无权限访问']);
        }

        $dateRange = $this->request->post('date_range', '');
        $keyword = $this->request->post('keyword', '');
        $orderStatus = $this->request->post('order_status', 'paid'); // all, paid, unpaid
        $exportFormat = $this->request->post('export_format', 'excel'); // excel, txt
        
        try {
            // 使用JOIN查询，关联商品表，过滤已删除的商品
            $query = Db::name('order')
                ->alias('o')
                ->join('goods g', 'o.goods_id = g.id')
                ->where('o.user_id', $this->user->id)
                ->whereNull('g.delete_time'); // 只查询未删除商品的订单
            
            // 添加关键词搜索
            if (!empty($keyword)) {
                $query->where('o.goods_name', 'like', "%{$keyword}%");
            }

            // 根据订单状态筛选
            if ($orderStatus === 'paid') {
                $query->where('o.status', 1); // 只查询已支付的订单
            } elseif ($orderStatus === 'unpaid') {
                $query->where('o.status', 0); // 只查询未支付的订单
            }
            // orderStatus === 'all' 时不添加状态条件，查询全部
            
            // 添加时间范围查询条件
            if (!empty($dateRange)) {
                list($start, $end) = explode(' - ', $dateRange);
                $startTime = strtotime($start);
                $endTime = strtotime($end) + 86399; // 23:59:59
                $query->whereBetween('o.create_time', [$startTime, $endTime]);
            }
            
            // 获取数据，同时获取商品表中的最新商品名称
            $data = $query->field([
                'o.id',
                'o.trade_no',
                'o.goods_name as order_goods_name', // 订单中的商品名称
                'g.name as current_goods_name',     // 商品表中的当前名称
                'o.goods_id',
                'o.create_time',
                'o.goods_price',
                'o.total_amount',
                'o.fee',
                'o.status'
            ])->order('o.create_time', 'desc')->select();

            if (empty($data)) {
                return json(['code' => 0, 'msg' => '没有查询到数据']);
            }

            // 获取订单对应的卡密信息
            $orderData = $data->toArray();
            $orderIds = array_column($orderData, 'id');

            // 获取订单卡密日志 - cards字段直接存储卡密内容
            $cardLogs = Db::name('order_card_log')
                ->whereIn('order_id', $orderIds)
                ->field(['order_id', 'cards'])
                ->select()
                ->toArray();

            // 重新组织卡密数据
            $cardData = [];
            foreach ($cardLogs as $log) {
                if (!empty($log['cards'])) {
                    // cards字段直接存储卡密内容，可能是JSON格式或纯文本
                    $cards = $log['cards'];

                    // 尝试解析JSON格式
                    $decodedCards = json_decode($cards, true);
                    if (is_array($decodedCards)) {
                        // 如果是数组，合并为字符串
                        $cardData[$log['order_id']] = implode(', ', $decodedCards);
                    } else {
                        // 如果不是JSON，直接使用原始内容
                        $cardData[$log['order_id']] = $cards;
                    }
                } else {
                    $cardData[$log['order_id']] = '无卡密信息';
                }
            }

            // 根据导出格式选择不同的处理方式
            if ($exportFormat === 'txt') {
                // 生成TXT格式
                $this->exportOrdersAsTxt($data, $cardData);
            } else {
                // 生成Excel格式
                $this->exportOrdersAsExcel($data, $cardData);
            }

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '导出失败：' . $e->getMessage()]);
        }
    }

    // 导出订单为Excel格式
    private function exportOrdersAsExcel($data, $cardData)
    {
        // 创建Excel对象
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // 设置表头
        $sheet->setCellValue('A1', '订单号');
        $sheet->setCellValue('B1', '商品名称');
        $sheet->setCellValue('C1', '卡密信息');
        $sheet->setCellValue('D1', '购买时间');
        $sheet->setCellValue('E1', '商品金额');
        $sheet->setCellValue('F1', '实付金额');
        $sheet->setCellValue('G1', '手续费');
        $sheet->setCellValue('H1', '状态');

        // 写入数据
        $row = 2;
        foreach ($data as $item) {
            // 获取卡密信息
            $cardInfo = isset($cardData[$item['id']]) ? $cardData[$item['id']] : '无卡密信息';

            // 如果卡密信息太长，截取前500个字符（增加显示长度）
            if (strlen($cardInfo) > 500) {
                $cardInfo = substr($cardInfo, 0, 500) . '...';
            }

            // 优先使用商品表中的当前名称，如果为空则使用订单中的名称
            $displayGoodsName = !empty($item['current_goods_name']) ? $item['current_goods_name'] : $item['order_goods_name'];

            // 将订单号设置为文本格式，避免Excel自动转换为科学计数法
            $sheet->setCellValueExplicit('A' . $row, $item['trade_no'], DataType::TYPE_STRING);
            $sheet->setCellValue('B' . $row, $displayGoodsName);

            // 将卡密信息设置为文本格式，避免Excel自动转换为科学计数法
            $sheet->setCellValueExplicit('C' . $row, $cardInfo, DataType::TYPE_STRING);

            $sheet->setCellValue('D' . $row, date('Y-m-d H:i:s', $item['create_time']));
            $sheet->setCellValue('E' . $row, $item['goods_price']);
            $sheet->setCellValue('F' . $row, $item['total_amount']);
            $sheet->setCellValue('G' . $row, $item['fee']);

            // 根据订单状态显示不同文本
            $statusText = $item['status'] == 1 ? '已支付' : '未支付';
            $sheet->setCellValue('H' . $row, $statusText);
            $row++;
        }

        // 设置列宽
        $sheet->getColumnDimension('A')->setWidth(20);
        $sheet->getColumnDimension('B')->setWidth(40);
        $sheet->getColumnDimension('C')->setWidth(30);
        $sheet->getColumnDimension('D')->setWidth(20);
        $sheet->getColumnDimension('E')->setWidth(15);
        $sheet->getColumnDimension('F')->setWidth(15);
        $sheet->getColumnDimension('G')->setWidth(15);
        $sheet->getColumnDimension('H')->setWidth(15);

        // 设置表头样式
        $sheet->getStyle('A1:H1')->getFont()->setBold(true);
        $sheet->getStyle('A1:H1')->getFill()
            ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
            ->getStartColor()->setRGB('CCCCCC');

        // 修改输出部分
        ob_end_clean(); // 清除缓冲区
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="merchant_orders_' . date('YmdHis') . '.xlsx"');
        header('Cache-Control: max-age=0');
        header('Cache-Control: max-age=1');
        header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
        header('Cache-Control: cache, must-revalidate');
        header('Pragma: public');

        $writer = new Xlsx($spreadsheet);
        $writer->save('php://output');
        exit;
    }

    // 导出订单为TXT格式
    private function exportOrdersAsTxt($data, $cardData)
    {
        // 设置输出头
        ob_end_clean(); // 清除缓冲区
        header('Content-Type: text/plain; charset=utf-8');
        header('Content-Disposition: attachment;filename="merchant_orders_' . date('YmdHis') . '.txt"');
        header('Cache-Control: max-age=0');

        // 输出BOM以确保UTF-8编码正确显示
        echo "\xEF\xBB\xBF";

        // 输出表头
        echo "订单号\t商品名称\t卡密信息\t购买时间\t商品金额\t实付金额\t手续费\t状态\n";
        echo str_repeat("=", 100) . "\n";

        // 输出数据
        foreach ($data as $item) {
            // 获取卡密信息
            $cardInfo = isset($cardData[$item['id']]) ? $cardData[$item['id']] : '无卡密信息';

            // 如果卡密信息太长，截取前1000个字符（TXT格式可以显示更多）
            if (strlen($cardInfo) > 1000) {
                $cardInfo = substr($cardInfo, 0, 1000) . '...';
            }

            // 优先使用商品表中的当前名称，如果为空则使用订单中的名称
            $displayGoodsName = !empty($item['current_goods_name']) ? $item['current_goods_name'] : $item['order_goods_name'];

            // 根据订单状态显示不同文本
            $statusText = $item['status'] == 1 ? '已支付' : '未支付';

            // 替换卡密信息中的换行符为空格，避免破坏表格格式
            $cardInfo = str_replace(["\r\n", "\r", "\n"], " ", $cardInfo);

            // 输出数据行
            echo $item['trade_no'] . "\t" .
                 $displayGoodsName . "\t" .
                 $cardInfo . "\t" .
                 date('Y-m-d H:i:s', $item['create_time']) . "\t" .
                 $item['goods_price'] . "\t" .
                 $item['total_amount'] . "\t" .
                 $item['fee'] . "\t" .
                 $statusText . "\n";
        }

        exit;
    }

    // 商家端查询订单数量
    public function merchantQuery()
    {
        // 验证商家用户权限
        if (!$this->user || !$this->user->id) {
            return json(['code' => 403, 'msg' => '无权限访问']);
        }

        $dateRange = $this->request->post('date_range', '');
        $keyword = $this->request->post('keyword', '');
        $orderStatus = $this->request->post('order_status', 'paid'); // all, paid, unpaid
        
        try {
            // 使用JOIN查询，关联商品表，过滤已删除的商品
            $query = Db::name('order')
                ->alias('o')
                ->join('goods g', 'o.goods_id = g.id')
                ->where('o.user_id', $this->user->id)
                ->whereNull('g.delete_time'); // 只查询未删除商品的订单
            
            // 添加关键词搜索
            if (!empty($keyword)) {
                $query->where('o.goods_name', 'like', "%{$keyword}%");
            }

            // 根据订单状态筛选
            if ($orderStatus === 'paid') {
                $query->where('o.status', 1); // 只统计已支付的订单
            } elseif ($orderStatus === 'unpaid') {
                $query->where('o.status', 0); // 只统计未支付的订单
            }
            // orderStatus === 'all' 时不添加状态条件，统计全部

            if (!empty($dateRange)) {
                list($start, $end) = explode(' - ', $dateRange);
                $startTime = strtotime($start);
                $endTime = strtotime($end) + 86399; // 23:59:59
                $query->whereBetween('o.create_time', [$startTime, $endTime]);
            }

            // 使用单次查询获取所有金额数据
            $orders = $query->field([
                'count(*) as count',
                'sum(o.goods_price) as goods_amount',
                'sum(case when o.fee_payer = 0 then o.total_amount else 0 end) as no_fee_amount',
                'sum(case when o.fee_payer = 1 then o.total_amount else 0 end) as fee_amount',
                'sum(case
                    when o.fee_payer = 0 then o.total_amount - o.fee
                    when o.fee_payer = 1 then o.total_amount - o.fee
                    else 0
                end) as merchant_amount'
            ])->find();

            if ($orders['count'] > 0) {
                // 根据查询状态生成不同的提示信息
                $statusText = '';
                if ($orderStatus === 'paid') {
                    $statusText = '(已支付)';
                } elseif ($orderStatus === 'unpaid') {
                    $statusText = '(未支付)';
                } else {
                    $statusText = '(全部状态)';
                }

                return json([
                    'code' => 200,
                    'msg' => '查询成功',
                    'data' => [
                        'count' => $orders['count'],
                        'goodsAmount' => number_format($orders['goods_amount'], 2, '.', ''),
                        'actualAmount' => number_format($orders['no_fee_amount'] + $orders['fee_amount'], 2, '.', ''),
                        'merchantAmount' => number_format($orders['merchant_amount'], 2, '.', ''),
                        'hasFee' => $orders['fee_amount'] > 0 ? '(含手续费)' : '',
                        'statusText' => $statusText
                    ]
                ]);
            } else {
                return json(['code' => 0, 'msg' => '没有查询到数据']);
            }

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '查询失败：' . $e->getMessage()]);
        }
    }

    // 获取卡密表后缀的辅助函数
    private function goods_card_storage_suffix($goods_id) {
        return "_" . (intval($goods_id / 4000));
    }

    // 商家端删除未售出卡密功能
    public function merchantDeleteCards()
    {
        // 验证商家用户权限
        if (!$this->user || !$this->user->id) {
            return json(['code' => 403, 'msg' => '无权限访问']);
        }

        $keyword = $this->request->post('keyword', '');
        $action = $this->request->post('action', 'query'); // query 或 delete

        try {
            // 查询商品信息 - 只查询商家自己的未删除商品
            $goodsQuery = Db::name('goods')
                ->where('user_id', $this->user->id)
                ->whereNull('delete_time'); // 只查询未删除的商品

            if (!empty($keyword)) {
                $goodsQuery->where('name', 'like', "%{$keyword}%");
            }

            $goods = $goodsQuery->field(['id', 'name', 'user_id'])->select()->toArray();

            if (empty($goods)) {
                return json(['code' => 0, 'msg' => '没有查询到商品数据']);
            }

            // 按商品ID分组，因为不同商品可能在不同的卡密表中
            $goodsByTable = [];
            foreach ($goods as $good) {
                $suffix = $this->goods_card_storage_suffix($good['id']);
                $goodsByTable[$suffix][] = $good;
            }

            $totalUnusedCards = 0;
            $goodsCardCounts = []; // 存储每个商品的未售出卡密数量

            // 分别查询不同的卡密表
            foreach ($goodsByTable as $suffix => $goodsGroup) {
                $goodsIds = array_column($goodsGroup, 'id');
                $tableName = 'goods_card_storage' . $suffix;

                try {
                    // 查询未售出的卡密数量
                    $cardCounts = Db::name($tableName)
                        ->whereIn('goods_id', $goodsIds)
                        ->where('status', 0) // 0表示未售出
                        ->whereNull('delete_time') // 未被软删除
                        ->field(['goods_id', 'COUNT(*) as count'])
                        ->group('goods_id')
                        ->select()
                        ->toArray();

                    foreach ($cardCounts as $cardCount) {
                        $goodsCardCounts[$cardCount['goods_id']] = $cardCount['count'];
                        $totalUnusedCards += $cardCount['count'];
                    }

                    // 如果是删除操作
                    if ($action === 'delete') {
                        $deleteResult = Db::name($tableName)
                            ->whereIn('goods_id', $goodsIds)
                            ->where('status', 0) // 只删除未售出的
                            ->whereNull('delete_time') // 未被软删除的
                            ->update(['delete_time' => time()]); // 软删除
                    }

                } catch (\Exception $e) {
                    // 如果表不存在，跳过
                    continue;
                }
            }

            if ($action === 'query') {
                // 构建商品列表信息
                $goodsList = [];
                foreach ($goods as $good) {
                    $unusedCount = isset($goodsCardCounts[$good['id']]) ? $goodsCardCounts[$good['id']] : 0;
                    if ($unusedCount > 0) { // 只显示有未售出卡密的商品
                        $goodsList[] = [
                            'goods_id' => $good['id'],
                            'goods_name' => $good['name'],
                            'unused_count' => $unusedCount
                        ];
                    }
                }

                return json([
                    'code' => 200,
                    'msg' => '查询成功',
                    'data' => [
                        'total_unused' => $totalUnusedCards,
                        'goods_list' => $goodsList
                    ]
                ]);
            } else {
                // 删除操作
                return json([
                    'code' => 200,
                    'msg' => "成功删除 {$totalUnusedCards} 条未售出卡密"
                ]);
            }

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '操作失败：' . $e->getMessage()]);
        }
    }

    // 商家端卡密导出功能
    public function merchantCardExport()
    {
        // 验证商家用户权限
        if (!$this->user || !$this->user->id) {
            return json(['code' => 403, 'msg' => '无权限访问']);
        }

        $dateRange = $this->request->post('date_range', '');
        $keyword = $this->request->post('keyword', '');
        $status = $this->request->post('status', 'all'); // all, used, unused
        $exportFormat = $this->request->post('export_format', 'excel'); // excel, txt

        try {
            // 查询商品信息 - 只查询商家自己的未删除商品
            $goodsQuery = Db::name('goods')
                ->where('user_id', $this->user->id)
                ->whereNull('delete_time'); // 只查询未删除的商品

            if (!empty($keyword)) {
                $goodsQuery->where('name', 'like', "%{$keyword}%");
            }

            $goods = $goodsQuery->field(['id', 'name', 'user_id'])->select()->toArray();

            if (empty($goods)) {
                return json(['code' => 0, 'msg' => '没有查询到商品数据']);
            }

            // 按商品ID分组，因为不同商品可能在不同的卡密表中
            $goodsByTable = [];
            foreach ($goods as $good) {
                $suffix = $this->goods_card_storage_suffix($good['id']);
                $goodsByTable[$suffix][] = $good;
            }

            $allCards = [];

            // 分别查询不同的卡密表
            foreach ($goodsByTable as $suffix => $goodsGroup) {
                $goodsIds = array_column($goodsGroup, 'id');
                $tableName = 'goods_card_storage' . $suffix;

                try {
                    $cardQuery = Db::name($tableName)
                        ->whereIn('goods_id', $goodsIds);

                    if (!empty($dateRange)) {
                        list($start, $end) = explode(' - ', $dateRange);
                        $startTime = strtotime($start);
                        $endTime = strtotime($end) + 86399;
                        $cardQuery->whereBetween('create_time', [$startTime, $endTime]);
                    }

                    // 根据状态筛选
                    if ($status === 'used') {
                        $cardQuery->where('status', 1);
                    } elseif ($status === 'unused') {
                        $cardQuery->where('status', 0);
                    }

                    $cards = $cardQuery->field([
                        'id', 'goods_id', 'secret', 'status', 'create_time', 'sell_time'
                    ])->order('create_time', 'desc')->select()->toArray();

                    $allCards = array_merge($allCards, $cards);
                } catch (\Exception $e) {
                    // 如果表不存在，跳过
                    continue;
                }
            }

            if (empty($allCards)) {
                return json(['code' => 0, 'msg' => '没有查询到卡密数据']);
            }

            // 创建商品ID到名称的映射
            $goodsMap = [];
            foreach ($goods as $good) {
                $goodsMap[$good['id']] = $good['name'];
            }

            // 根据导出格式选择不同的处理方式
            if ($exportFormat === 'txt') {
                // 生成TXT格式
                $this->exportCardsAsTxt($allCards, $goodsMap);
            } else {
                // 生成Excel格式
                $this->exportCardsAsExcel($allCards, $goodsMap);
            }

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '导出失败：' . $e->getMessage()]);
        }
    }

    // 导出卡密为Excel格式
    private function exportCardsAsExcel($allCards, $goodsMap)
    {
        // 创建Excel对象
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // 设置表头
        $sheet->setCellValue('A1', '商品名称');
        $sheet->setCellValue('B1', '卡密');
        $sheet->setCellValue('C1', '状态');
        $sheet->setCellValue('D1', '创建时间');
        $sheet->setCellValue('E1', '售出时间');

        // 写入数据
        $row = 2;
        foreach ($allCards as $card) {
            $goodsName = isset($goodsMap[$card['goods_id']]) ? $goodsMap[$card['goods_id']] : '未知商品';
            $cardStatus = $card['status'] == 1 ? '已售出' : '未售出';
            $sellTime = $card['sell_time'] ? date('Y-m-d H:i:s', $card['sell_time']) : '-';

            $sheet->setCellValue('A' . $row, $goodsName);

            // 将卡密内容设置为文本格式，避免Excel自动转换为科学计数法
            $sheet->setCellValueExplicit('B' . $row, $card['secret'], DataType::TYPE_STRING);

            $sheet->setCellValue('C' . $row, $cardStatus);
            $sheet->setCellValue('D' . $row, date('Y-m-d H:i:s', $card['create_time']));
            $sheet->setCellValue('E' . $row, $sellTime);
            $row++;
        }

        // 设置列宽
        $sheet->getColumnDimension('A')->setWidth(40);
        $sheet->getColumnDimension('B')->setWidth(30);
        $sheet->getColumnDimension('C')->setWidth(15);
        $sheet->getColumnDimension('D')->setWidth(20);
        $sheet->getColumnDimension('E')->setWidth(20);

        // 设置表头样式
        $sheet->getStyle('A1:E1')->getFont()->setBold(true);
        $sheet->getStyle('A1:E1')->getFill()
            ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
            ->getStartColor()->setRGB('CCCCCC');

        // 输出文件
        ob_end_clean();
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="merchant_cards_' . date('YmdHis') . '.xlsx"');
        header('Cache-Control: max-age=0');
        header('Cache-Control: max-age=1');
        header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
        header('Cache-Control: cache, must-revalidate');
        header('Pragma: public');

        $writer = new Xlsx($spreadsheet);
        $writer->save('php://output');
        exit;
    }

    // 导出卡密为TXT格式
    private function exportCardsAsTxt($allCards, $goodsMap)
    {
        // 设置输出头
        ob_end_clean(); // 清除缓冲区
        header('Content-Type: text/plain; charset=utf-8');
        header('Content-Disposition: attachment;filename="merchant_cards_' . date('YmdHis') . '.txt"');
        header('Cache-Control: max-age=0');

        // 输出BOM以确保UTF-8编码正确显示
        echo "\xEF\xBB\xBF";

        // 输出表头
        echo "商品名称\t卡密\t状态\t创建时间\t售出时间\n";
        echo str_repeat("=", 100) . "\n";

        // 输出数据
        foreach ($allCards as $card) {
            $goodsName = isset($goodsMap[$card['goods_id']]) ? $goodsMap[$card['goods_id']] : '未知商品';
            $cardStatus = $card['status'] == 1 ? '已售出' : '未售出';
            $sellTime = $card['sell_time'] ? date('Y-m-d H:i:s', $card['sell_time']) : '-';

            // 替换卡密中的换行符为空格，避免破坏表格格式
            $cardSecret = str_replace(["\r\n", "\r", "\n"], " ", $card['secret']);

            // 输出数据行
            echo $goodsName . "\t" .
                 $cardSecret . "\t" .
                 $cardStatus . "\t" .
                 date('Y-m-d H:i:s', $card['create_time']) . "\t" .
                 $sellTime . "\n";
        }

        exit;
    }

    // 商家端卡密查询
    public function merchantCardQuery()
    {
        // 验证商家用户权限
        if (!$this->user || !$this->user->id) {
            return json(['code' => 403, 'msg' => '无权限访问']);
        }

        $dateRange = $this->request->post('date_range', '');
        $keyword = $this->request->post('keyword', '');
        $status = $this->request->post('status', 'all');

        try {
            // 查询商品信息 - 只查询商家自己的未删除商品
            $goodsQuery = Db::name('goods')
                ->where('user_id', $this->user->id)
                ->whereNull('delete_time'); // 只查询未删除的商品

            if (!empty($keyword)) {
                $goodsQuery->where('name', 'like', "%{$keyword}%");
            }

            $goods = $goodsQuery->field(['id', 'name'])->select()->toArray();

            if (empty($goods)) {
                return json(['code' => 0, 'msg' => '没有查询到商品数据']);
            }

            // 按商品ID分组，因为不同商品可能在不同的卡密表中
            $goodsByTable = [];
            foreach ($goods as $good) {
                $suffix = $this->goods_card_storage_suffix($good['id']);
                $goodsByTable[$suffix][] = $good;
            }

            $totalCount = 0;
            $usedCount = 0;
            $unusedCount = 0;

            // 分别查询不同的卡密表
            foreach ($goodsByTable as $suffix => $goodsGroup) {
                $goodsIds = array_column($goodsGroup, 'id');
                $tableName = 'goods_card_storage' . $suffix;

                try {
                    $cardQuery = Db::name($tableName)
                        ->whereIn('goods_id', $goodsIds);

                    if (!empty($dateRange)) {
                        list($start, $end) = explode(' - ', $dateRange);
                        $startTime = strtotime($start);
                        $endTime = strtotime($end) + 86399;
                        $cardQuery->whereBetween('create_time', [$startTime, $endTime]);
                    }

                    // 根据状态筛选并统计
                    if ($status === 'used') {
                        $count = $cardQuery->where('status', 1)->count();
                        $totalCount += $count;
                        $usedCount += $count;
                    } elseif ($status === 'unused') {
                        $count = $cardQuery->where('status', 0)->count();
                        $totalCount += $count;
                        $unusedCount += $count;
                    } else {
                        // 统计全部
                        $total = $cardQuery->count();
                        $used = $cardQuery->where('status', 1)->count();
                        $unused = $cardQuery->where('status', 0)->count();

                        $totalCount += $total;
                        $usedCount += $used;
                        $unusedCount += $unused;
                    }
                } catch (\Exception $e) {
                    // 如果表不存在，跳过
                    continue;
                }
            }

            return json([
                'code' => 200,
                'msg' => '查询成功',
                'data' => [
                    'total' => $totalCount,
                    'used' => $usedCount,
                    'unused' => $unusedCount
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '查询失败：' . $e->getMessage()]);
        }
    }
}
