#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Selenium元素操作助手
提供通用的元素检测、点击、输入等功能
"""

import time
from typing import List, Dict, Optional, Union
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.common.action_chains import ActionChains

class ElementHelper:
    """Selenium元素操作助手类"""
    
    def __init__(self, driver, default_timeout: int = 10):
        """
        初始化元素助手
        
        Args:
            driver: Selenium WebDriver实例
            default_timeout: 默认等待超时时间（秒）
        """
        self.driver = driver
        self.default_timeout = default_timeout
        self.wait = WebDriverWait(driver, default_timeout)
    
    def check_element_exists(self, locator: Union[str, tuple], timeout: int = None) -> bool:
        """
        检查元素是否存在
        
        Args:
            locator: 元素定位器（XPath字符串或(By.XXX, value)元组）
            timeout: 等待超时时间
            
        Returns:
            bool: 元素是否存在
        """
        timeout = timeout or self.default_timeout
        
        try:
            if isinstance(locator, str):
                locator = (By.XPATH, locator)
            
            WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located(locator)
            )
            return True
        except TimeoutException:
            return False
        except Exception:
            return False
    
    def check_element_clickable(self, locator: Union[str, tuple], timeout: int = None) -> bool:
        """
        检查元素是否可点击
        
        Args:
            locator: 元素定位器
            timeout: 等待超时时间
            
        Returns:
            bool: 元素是否可点击
        """
        timeout = timeout or self.default_timeout
        
        try:
            if isinstance(locator, str):
                locator = (By.XPATH, locator)
            
            WebDriverWait(self.driver, timeout).until(
                EC.element_to_be_clickable(locator)
            )
            return True
        except TimeoutException:
            return False
        except Exception:
            return False
    
    def safe_click(self, locator: Union[str, tuple], description: str = "元素", 
                   timeout: int = None, scroll_to_element: bool = True) -> bool:
        """
        安全点击元素（先检查是否存在和可点击）
        
        Args:
            locator: 元素定位器
            description: 元素描述（用于日志）
            timeout: 等待超时时间
            scroll_to_element: 是否滚动到元素位置
            
        Returns:
            bool: 是否成功点击
        """
        timeout = timeout or self.default_timeout
        
        try:
            if isinstance(locator, str):
                locator = (By.XPATH, locator)
            
            print(f"[检查] 检查{description}: {locator[1]}")
            
            # 等待元素可点击
            element = WebDriverWait(self.driver, timeout).until(
                EC.element_to_be_clickable(locator)
            )
            
            print(f"[成功] 找到{description}，准备点击")
            
            # 滚动到元素位置
            if scroll_to_element:
                self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                time.sleep(0.5)
            
            # 点击元素
            element.click()
            print(f"[成功] 成功点击{description}")
            
            return True
            
        except TimeoutException:
            print(f"[警告] {description}不存在或不可点击（超时{timeout}秒）")
            return False
        except Exception as e:
            print(f"[失败] 点击{description}失败: {e}")
            return False
    
    def safe_input(self, locator: Union[str, tuple], text: str, description: str = "输入框",
                   timeout: int = None, clear_first: bool = True) -> bool:
        """
        安全输入文本
        
        Args:
            locator: 元素定位器
            text: 要输入的文本
            description: 元素描述
            timeout: 等待超时时间
            clear_first: 是否先清空输入框
            
        Returns:
            bool: 是否成功输入
        """
        timeout = timeout or self.default_timeout
        
        try:
            if isinstance(locator, str):
                locator = (By.XPATH, locator)
            
            print(f"[编辑] 向{description}输入文本: {text}")
            
            # 等待元素可见
            element = WebDriverWait(self.driver, timeout).until(
                EC.visibility_of_element_located(locator)
            )
            
            # 滚动到元素位置
            self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
            time.sleep(0.3)
            
            # 清空并输入
            if clear_first:
                element.clear()
            element.send_keys(text)
            
            print(f"[成功] 成功向{description}输入文本")
            return True
            
        except TimeoutException:
            print(f"[警告] {description}不存在或不可见（超时{timeout}秒）")
            return False
        except Exception as e:
            print(f"[失败] 向{description}输入文本失败: {e}")
            return False
    
    def get_element_text(self, locator: Union[str, tuple], timeout: int = None) -> Optional[str]:
        """
        获取元素文本
        
        Args:
            locator: 元素定位器
            timeout: 等待超时时间
            
        Returns:
            str: 元素文本，失败返回None
        """
        timeout = timeout or self.default_timeout
        
        try:
            if isinstance(locator, str):
                locator = (By.XPATH, locator)
            
            element = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located(locator)
            )
            
            return element.text.strip()
            
        except Exception:
            return None
    
    def wait_for_element_disappear(self, locator: Union[str, tuple], timeout: int = None) -> bool:
        """
        等待元素消失
        
        Args:
            locator: 元素定位器
            timeout: 等待超时时间
            
        Returns:
            bool: 元素是否消失
        """
        timeout = timeout or self.default_timeout
        
        try:
            if isinstance(locator, str):
                locator = (By.XPATH, locator)
            
            WebDriverWait(self.driver, timeout).until_not(
                EC.presence_of_element_located(locator)
            )
            return True
        except TimeoutException:
            return False
        except Exception:
            return False
    
    def find_all_buttons(self) -> List[Dict]:
        """
        查找页面上所有按钮
        
        Returns:
            List[Dict]: 按钮信息列表
        """
        buttons_info = []
        
        try:
            buttons = self.driver.find_elements(By.TAG_NAME, "button")
            
            for i, button in enumerate(buttons):
                try:
                    info = {
                        'index': i + 1,
                        'text': button.text.strip(),
                        'tag_name': button.tag_name,
                        'is_displayed': button.is_displayed(),
                        'is_enabled': button.is_enabled(),
                        'element': button
                    }
                    
                    # 尝试获取常用属性
                    try:
                        info['id'] = button.get_attribute('id') or ''
                        info['class'] = button.get_attribute('class') or ''
                        info['type'] = button.get_attribute('type') or ''
                    except:
                        pass
                    
                    buttons_info.append(info)
                    
                except Exception as e:
                    buttons_info.append({
                        'index': i + 1,
                        'error': str(e),
                        'element': button
                    })
        
        except Exception as e:
            print(f"[失败] 查找按钮失败: {e}")
        
        return buttons_info
    
    def click_button_by_text(self, text: str, partial_match: bool = True, timeout: int = None) -> bool:
        """
        根据文本点击按钮
        
        Args:
            text: 按钮文本
            partial_match: 是否部分匹配
            timeout: 等待超时时间
            
        Returns:
            bool: 是否成功点击
        """
        timeout = timeout or self.default_timeout
        
        try:
            if partial_match:
                xpath = f"//button[contains(text(), '{text}')]"
            else:
                xpath = f"//button[text()='{text}']"
            
            return self.safe_click(xpath, f"文本为'{text}'的按钮", timeout)
            
        except Exception as e:
            print(f"[失败] 根据文本点击按钮失败: {e}")
            return False
    
    def handle_initial_popup(self, popup_xpath: str, close_button_xpath: str,
                            description: str = "初始弹窗", timeout: int = 5) -> bool:
        """
        处理初始弹窗（先检查是否存在，存在则点击关闭按钮）

        Args:
            popup_xpath: 弹窗容器的XPath
            close_button_xpath: 关闭按钮的XPath
            description: 弹窗描述
            timeout: 等待超时时间

        Returns:
            bool: 是否处理了弹窗（True=处理了弹窗，False=没有弹窗或处理失败）
        """
        try:
            print(f"[检查] 检查{description}: {popup_xpath}")

            # 检查弹窗是否存在
            if self.check_element_exists(popup_xpath, timeout=timeout):
                print(f"[成功] 发现{description}，尝试关闭")

                # 点击关闭按钮
                if self.safe_click(close_button_xpath, f"{description}关闭按钮", timeout=timeout):
                    print(f"[成功] {description}已关闭")
                    time.sleep(1)  # 等待弹窗关闭动画
                    return True
                else:
                    print(f"[失败] {description}关闭失败")
                    return False
            else:
                print(f"[信息] 未发现{description}，跳过处理")
                return False

        except Exception as e:
            print(f"[失败] 处理{description}异常: {e}")
            return False

    def highlight_element(self, locator: Union[str, tuple], duration: float = 2.0):
        """
        高亮显示元素（用于调试）

        Args:
            locator: 元素定位器
            duration: 高亮持续时间（秒）
        """
        try:
            if isinstance(locator, str):
                locator = (By.XPATH, locator)

            element = self.driver.find_element(*locator)

            # 保存原始样式
            original_style = element.get_attribute('style')

            # 添加高亮样式
            self.driver.execute_script(
                "arguments[0].style.border='3px solid red'; arguments[0].style.backgroundColor='yellow';",
                element
            )

            time.sleep(duration)

            # 恢复原始样式
            self.driver.execute_script(
                f"arguments[0].style='{original_style or ''}';",
                element
            )

        except Exception as e:
            print(f"[失败] 高亮元素失败: {e}")

# 使用示例
def example_usage():
    """使用示例"""
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    
    # 创建driver
    options = Options()
    options.add_argument('--headless')
    driver = webdriver.Chrome(options=options)
    
    try:
        # 创建元素助手
        helper = ElementHelper(driver)
        
        # 访问页面
        driver.get("https://970faka.com/merchant/register")
        
        # 检查特定按钮
        button_xpath = "/html/body/div[2]/div/div[2]/div/div[3]/button"
        
        if helper.check_element_exists(button_xpath):
            print("[成功] 按钮存在")
            
            if helper.safe_click(button_xpath, "目标按钮"):
                print("[成功] 按钮点击成功")
        
        # 查找所有按钮
        buttons = helper.find_all_buttons()
        print(f"[进度] 找到 {len(buttons)} 个按钮")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    example_usage()
