<!doctype html>
<html lang="zh">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>{$title}</title>
        
        <link rel="shortcut icon" href="{$favicon}" type="image/x-icon">
        <link rel="stylesheet" href="/assets/plugin/Blackglod/plugin/Blackglod/css/all.min.css">
        <link rel="stylesheet" href="/assets/plugin/Blackglod/plugin/Blackglod/css/all.min1.css">
        
        <style>
            /* 全局样式 */
            :root {
                --primary-gold: #B39B77;     /* 更深沉的金色 */
                --dark-gold: #8B7355;        /* 暗金色 */
                --light-gold: #D4B78F;       /* 浅金色 */
                --bg-dark: #151515;          /* 更深的背景色 */
                --bg-darker: #0A0A0A;        /* 最深的背景色 */
                --text-gold: #B39B77;        /* 文字金色 */
                --text-light: #E5E5E5;       /* 浅色文字 */
            }

            /* 背景样式优化 */
            body {
                background: linear-gradient(135deg, 
                    var(--bg-darker) 0%, 
                    var(--bg-dark) 50%,
                    var(--bg-darker) 100%
                );
                color: var(--text-light);
                margin: 0;
                font-family: 'Arial', sans-serif;
                min-height: 100vh;
                position: relative;
                overflow-x: hidden;
            }

            /* 背景装饰效果 */
            body::before {
                content: '';
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: 
                    radial-gradient(circle at 20% 30%, 
                        rgba(179, 155, 119, 0.05) 0%, 
                        transparent 50%),
                    radial-gradient(circle at 80% 70%, 
                        rgba(179, 155, 119, 0.05) 0%, 
                        transparent 50%);
                pointer-events: none;
                z-index: 0;
            }

            /* 动态光效 */
            .light-effect {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: 
                    linear-gradient(45deg, 
                        transparent 0%, 
                        rgba(179, 155, 119, 0.02) 50%, 
                        transparent 100%);
                animation: lightMove 8s ease-in-out infinite;
                pointer-events: none;
                z-index: 1;
            }

            /* 网格背景 */
            .grid-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-image: 
                    linear-gradient(rgba(179, 155, 119, 0.05) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(179, 155, 119, 0.05) 1px, transparent 1px);
                background-size: 50px 50px;
                pointer-events: none;
                z-index: 1;
                opacity: 0.3;
            }

            /* 装饰线条 */
            .decorative-lines {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: 
                    linear-gradient(45deg, transparent 48%, 
                        rgba(179, 155, 119, 0.1) 49%, 
                        rgba(179, 155, 119, 0.1) 51%, 
                        transparent 52%);
                background-size: 100px 100px;
                pointer-events: none;
                z-index: 1;
                opacity: 0.2;
            }

            @keyframes lightMove {
                0% {
                    transform: translateX(-100%) rotate(-45deg);
                }
                50% {
                    transform: translateX(100%) rotate(-45deg);
                }
                100% {
                    transform: translateX(-100%) rotate(-45deg);
                }
            }

            /* 确保内容在装饰层之上 */
            .header,
            .article-section,
            .hex-decoration {
                position: relative;
                z-index: 2;
            }

            /* 调整文章容器背景 */
            .article-container {
                background: rgba(0, 0, 0, 0.4);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(179, 155, 119, 0.1);
                box-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
            }

            .notice-item {
                background: rgba(255, 255, 255, 0.03);
                backdrop-filter: blur(5px);
                border: 1px solid rgba(179, 155, 119, 0.1);
                transition: all 0.3s ease;
            }

            .notice-item:hover {
                transform: translateY(-2px);
                box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
                border-color: rgba(179, 155, 119, 0.2);
            }

            /* 导航栏样式优化 */
            .header {
                background: rgba(10, 10, 10, 0.95);
                border-bottom: 1px solid rgba(179, 155, 119, 0.1);
                backdrop-filter: blur(10px);
                padding: 0;
                position: fixed;
                width: 100%;
                top: 0;
                z-index: 1000;
                height: 70px;
            }

            .nav-container {
                display: flex;
                justify-content: space-between;
                align-items: center;
                max-width: 1400px;
                margin: 0 auto;
                padding: 0 2rem;
                height: 100%;
            }

            .logo {
                display: flex;
                align-items: center;
                gap: 1rem;
                text-decoration: none;
            }

            .logo-img {
                height: 35px;
                transition: transform 0.3s;
            }

            .nav-links {
                display: flex;
                align-items: center;
                gap: 2.5rem;
                margin: 0;
                padding: 0;
                list-style: none;
            }

            .nav-item {
                position: relative;
            }
            
            .nav-link {
                color: #999;
                text-decoration: none;
                font-size: 0.95rem;
                padding: 0.5rem 0;
                transition: all 0.3s ease;
                position: relative;
                display: flex;
                align-items: center;
            }
            
            .nav-link .dropdown-icon {
                margin-left: 5px;
                font-size: 0.7rem;
                transition: transform 0.3s ease;
            }
            
            .nav-item:hover .dropdown-icon {
                transform: rotate(180deg);
            }

            .nav-link::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 50%;
                width: 0;
                height: 2px;
                background: linear-gradient(90deg, var(--primary-gold), var(--light-gold));
                transition: all 0.3s ease;
                transform: translateX(-50%);
            }

            .nav-link:hover {
                color: var(--primary-gold);
            }

            .nav-link:hover::after {
                width: 100%;
            }
            
            /* 子菜单样式 */
            .submenu {
                position: absolute;
                top: 100%;
                left: 50%;
                transform: translateX(-50%) translateY(10px);
                min-width: 180px;
                background: rgba(15, 15, 15, 0.95);
                border: 1px solid rgba(179, 155, 119, 0.15);
                border-radius: 4px;
                padding: 0.5rem 0;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
                z-index: 1001;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
                backdrop-filter: blur(10px);
            }
            
            .nav-item:hover .submenu {
                opacity: 1;
                visibility: visible;
                transform: translateX(-50%) translateY(0);
            }
            
            .submenu::before {
                content: '';
                position: absolute;
                top: -6px;
                left: 50%;
                transform: translateX(-50%);
                width: 12px;
                height: 12px;
                background: rgba(15, 15, 15, 0.95);
                border-left: 1px solid rgba(179, 155, 119, 0.15);
                border-top: 1px solid rgba(179, 155, 119, 0.15);
                transform: translateX(-50%) rotate(45deg);
            }
            
            .submenu-item {
                display: block;
                padding: 0.6rem 1.2rem;
                color: #999;
                text-decoration: none;
                font-size: 0.9rem;
                transition: all 0.2s ease;
                position: relative;
                white-space: nowrap;
            }
            
            .submenu-item:hover {
                color: var(--primary-gold);
                background: rgba(179, 155, 119, 0.05);
                padding-left: 1.5rem;
            }
            
            .submenu-item::before {
                content: '';
                position: absolute;
                left: 0.8rem;
                top: 50%;
                width: 0;
                height: 1px;
                background: var(--primary-gold);
                transition: all 0.2s ease;
                transform: translateY(-50%);
            }
            
            .submenu-item:hover::before {
                width: 0.4rem;
            }

            /* 按钮样式优化 */
            .auth-buttons {
                display: flex;
                gap: 1rem;
            }

            .btn-login, .btn-register {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                font-size: 0.9rem;
                transition: all 0.3s;
                text-decoration: none;
            }

            .btn-login {
                background: transparent;
                border: 1px solid var(--primary-gold);
                color: var(--primary-gold);
                padding: 0.6rem 1.5rem;
                border-radius: 4px;
            }

            .btn-register {
                background: linear-gradient(135deg, var(--primary-gold), var(--dark-gold));
                border: none;
                color: #000;
                padding: 0.6rem 1.5rem;
                border-radius: 4px;
                font-weight: 500;
            }

            .btn-login:hover, .btn-register:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(179, 155, 119, 0.2);
            }

            /* 响应式设计 */
            @media (max-width: 768px) {
                .nav-container {
                    padding: 0 1rem;
                }

                .nav-links {
                    gap: 1rem;
                }

                .auth-buttons {
                    gap: 0.5rem;
                }

                .btn-login, .btn-register {
                    padding: 0.4rem 0.8rem;
                    font-size: 0.85rem;
                }
                
                /* 移动设备子菜单样式 */
                .submenu {
                    position: absolute;
                    left: 0;
                    top: 100%;
                    width: 100%;
                    transform: translateY(10px);
                    min-width: auto;
                    z-index: 1001;
                }
                
                .nav-item:hover .submenu {
                    transform: translateY(0);
                }
                
                .submenu::before {
                    left: 1rem;
                    transform: rotate(45deg);
                }
            }
            
            /* 移动设备菜单按钮 */
            .mobile-menu-toggle {
                display: none;
                cursor: pointer;
                background: none;
                border: none;
                font-size: 1.5rem;
                color: var(--primary-gold);
                padding: 0;
            }
            
            @media (max-width: 576px) {
                .mobile-menu-toggle {
                    display: block;
                    margin-right: 10px;
                }
                
                .auth-buttons {
                    justify-content: flex-end;
                    flex: 1;
                }
                
                .btn-login {
                    display: none;
                }
                
                .btn-register {
                    padding: 0.4rem 0.8rem;
                    font-size: 0.85rem;
                }
                
                .nav-links {
                    position: fixed;
                    top: 70px;
                    left: 0;
                    width: 100%;
                    flex-direction: column;
                    background: rgba(10, 10, 10, 0.98);
                    padding: 1rem 0;
                    transform: translateY(-150%);
                    opacity: 0;
                    visibility: hidden;
                    transition: all 0.3s ease;
                    z-index: 1000;
                    border-bottom: 1px solid rgba(179, 155, 119, 0.1);
                    backdrop-filter: blur(10px);
                    align-items: flex-start;
                    gap: 0;
                    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
                }
                
                .nav-links.active {
                    transform: translateY(0);
                    opacity: 1;
                    visibility: visible;
                }
                
                .nav-item {
                    width: 100%;
                    border-bottom: 1px solid rgba(179, 155, 119, 0.05);
                }
                
                .nav-item:last-child {
                    border-bottom: none;
                }
                
                .nav-link {
                    padding: 0.8rem 1.5rem;
                    width: 100%;
                    justify-content: space-between;
                }
                
                .nav-link::after {
                    display: none;
                }
                
                .submenu {
                    position: static;
                    background: rgba(20, 20, 20, 0.95);
                    transform: none;
                    box-shadow: none;
                    border-radius: 0;
                    border: none;
                    border-left: 2px solid var(--primary-gold);
                    max-height: 0;
                    overflow: hidden;
                    opacity: 1;
                    visibility: visible;
                    margin-left: 1.5rem;
                    width: calc(100% - 3rem);
                    transition: max-height 0.3s ease, padding 0.3s ease;
                }
                
                .nav-item.open .submenu {
                    max-height: 500px;
                    padding: 0.5rem 0;
                }
                
                .submenu::before {
                    display: none;
                }
                
                .submenu-item {
                    padding: 0.6rem 1rem;
                    border-bottom: 1px solid rgba(179, 155, 119, 0.05);
                }
                
                .submenu-item:last-child {
                    border-bottom: none;
                }
                
                /* 移动端登录/注册按钮 */
                .mobile-auth {
                    display: block;
                    width: 100%;
                    padding: 1rem 1.5rem;
                    background: rgba(179, 155, 119, 0.05);
                    margin-top: 0.5rem;
                }
                
                .mobile-auth .btn-login {
                    display: inline-block;
                    margin-right: 1rem;
                }
            }

            /* 文章列表样式优化 */
            .article-section {
                padding-top: 100px;
                position: relative;
                z-index: 2;
            }

            .article-container {
                max-width: 1200px;
                margin: 0 auto;
                padding: 2rem;
                background: rgba(0, 0, 0, 0.3);
                border-radius: 20px;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(179, 155, 119, 0.1);
            }

            .notice-item {
                background: rgba(255, 255, 255, 0.03);
                border: 1px solid rgba(200, 166, 117, 0.1);
                border-radius: 15px;
                padding: 2rem;
                margin-bottom: 1.5rem;
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
            }

            .notice-item:hover {
                transform: translateY(-3px);
                border-color: rgba(200, 166, 117, 0.3);
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            }

            .notice-item::before {
                content: '';
                position: absolute;
                top: -50%;
                left: -50%;
                width: 200%;
                height: 200%;
                background: linear-gradient(
                    45deg,
                    transparent,
                    rgba(200,166,117,0.1),
                    transparent
                );
                transform: rotate(45deg);
                animation: shimmer 3s infinite linear;
                pointer-events: none;
            }

            .notice-title {
                font-size: 1.5rem;
                color: var(--primary-gold);
                margin-bottom: 1rem;
                position: relative;
                display: inline-block;
            }

            .notice-content {
                color: var(--text-light);
                line-height: 1.8;
                margin-bottom: 1.5rem;
            }

            .notice-meta {
                display: flex;
                align-items: center;
                gap: 20px;
                margin-top: 15px;
                font-size: 14px;
                color: #666;
            }

            .notice-meta span {
                display: flex;
                align-items: center;
                gap: 5px;
            }

            .notice-meta i {
                font-size: 16px;
                color: #888;
            }

            /* 浏览次数图标特殊样式 */
            .view-count {
                opacity: 0.7;
            }

            .view-count i {
                color: #999;
            }

            /* 动画效果 */
            @keyframes shimmer {
                0% {
                    transform: translateX(-100%) rotate(45deg);
                }
                100% {
                    transform: translateX(100%) rotate(45deg);
                }
            }

            /* 响应式设计 */
            @media (max-width: 768px) {
                .container {
                    padding: 0 1rem;
                }

                .article-container {
                    padding: 1rem;
                    margin: 0 1rem;
                }

                .notice-item {
                    padding: 1.5rem;
                }

                .notice-title {
                    font-size: 1.3rem;
                }
            }

            /* 页脚样式 */
            .footer {
                background: rgba(10, 10, 10, 0.95);
                border-top: 1px solid rgba(179, 155, 119, 0.1);
                padding: 4rem 0 2rem;
                margin-top: 4rem;
            }

            .footer-container {
                max-width: 1200px;
                margin: 0 auto;
                padding: 0 2rem;
                display: grid;
                grid-template-columns: repeat(4, 1fr);
                gap: 3rem;
            }

            .footer-section h3 {
                color: var(--primary-gold);
                font-size: 1.2rem;
                margin-bottom: 1.5rem;
            }

            .footer-section ul {
                list-style: none;
                padding: 0;
                margin: 0;
            }

            .footer-section li {
                margin-bottom: 0.8rem;
            }

            .footer-section a {
                color: var(--text-light);
                text-decoration: none;
                transition: color 0.3s;
            }

            .footer-section a:hover {
                color: var(--primary-gold);
            }

            .footer-bottom {
                margin-top: 3rem;
                padding-top: 2rem;
                border-top: 1px solid rgba(179, 155, 119, 0.1);
                text-align: center;
                color: #666;
            }

            .footer-bottom a {
                color: #888;
                text-decoration: none;
            }

            .footer-bottom a:hover {
                color: var(--primary-gold);
            }

            /* 3D 蜂巢装饰样式 */
            .hex-decoration {
                position: fixed;
                top: 50%;
                transform: translateY(-50%);
                width: 300px;
                height: 600px;
                pointer-events: none;
                z-index: 0;
            }

            .hex-left {
                left: 0;
            }

            .hex-right {
                right: 0;
            }

            .hex-container {
                position: relative;
                width: 100%;
                height: 100%;
                display: grid;
                grid-template-columns: repeat(4, 1fr);
                gap: 10px;
                padding: 20px;
            }

            .hex {
                width: 60px;
                height: 60px;
                background: rgba(179, 155, 119, 0.1);
                clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
                position: relative;
                transition: all 0.3s ease;
                transform-style: preserve-3d;
                transform: perspective(1000px) rotateX(30deg) rotateY(0deg);
                box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            }

            .hex::before {
                content: '';
                position: absolute;
                width: 100%;
                height: 100%;
                background: rgba(179, 155, 119, 0.05);
                clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
                transform: translateZ(-10px);
            }

            .hex::after {
                content: '';
                position: absolute;
                width: 100%;
                height: 100%;
                background: rgba(179, 155, 119, 0.1);
                transform: translateZ(10px);
                clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
            }

            .hex:nth-child(even) {
                transform: perspective(1000px) rotateX(30deg) rotateY(0deg) translateY(30px);
            }

            /* 动画效果 */
            @keyframes hexFloat {
                0% {
                    transform: translateY(0) rotateX(30deg) rotateY(0deg);
                }
                50% {
                    transform: translateY(-10px) rotateX(35deg) rotateY(5deg);
                }
                100% {
                    transform: translateY(0) rotateX(30deg) rotateY(0deg);
                }
            }

            .hex {
                animation: hexFloat 4s ease-in-out infinite;
                animation-delay: calc(var(--delay) * 0.2s);
            }

            /* 响应式调整 */
            @media (max-width: 1600px) {
                .hex-decoration {
                    width: 200px;
                }
                .hex {
                    width: 40px;
                    height: 40px;
                }
            }

            @media (max-width: 1200px) {
                .hex-decoration {
                    width: 150px;
                }
                .hex {
                    width: 30px;
                    height: 30px;
                }
            }

            @media (max-width: 768px) {
                .hex-decoration {
                    display: none;
                }
            }
        </style>
    </head>
    <body>
        <!-- 背景效果层 -->
        <div class="light-effect"></div>
        <div class="grid-overlay"></div>
        <div class="decorative-lines"></div>

        <!-- 左侧蜂巢装饰 -->
        <div class="hex-decoration hex-left">
            <div class="hex-container">
                {for start="1" end="17"}
                <div class="hex" style="--delay: {$i}"></div>
                {/for}
            </div>
        </div>

        <!-- 右侧蜂巢装饰 -->
        <div class="hex-decoration hex-right">
            <div class="hex-container">
                {for start="1" end="17"}
                <div class="hex" style="--delay: {$i}"></div>
                {/for}
            </div>
        </div>

        <header class="header">
            <div class="nav-container">
                <a href="/" class="logo">
                    {if !empty($logo)}
                    <img src="{$logo}" alt="{$siteName}" class="logo-img">
                    {else}
                    <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                        <path d="M16 4L28 12V24L16 32L4 24V12L16 4Z" fill="#D4B78F"/>
                    </svg>
                    {/if}
                </a>
                
                <button class="mobile-menu-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                
                <nav class="nav-links">
                    {foreach $navItems as $nav}
                    <div class="nav-item">
                        <a href="{$nav.href}" class="nav-link" {if $nav.target eq '_blank'}target="_blank"{/if}>
                            {$nav.name}
                            {if !empty($nav.children)}
                            <i class="fas fa-chevron-down dropdown-icon"></i>
                            {/if}
                        </a>
                        {if !empty($nav.children)}
                        <div class="submenu">
                            {foreach $nav.children as $child}
                            <a href="{$child.href}" class="submenu-item" {if $child.target eq '_blank'}target="_blank"{/if}>
                                {$child.name}
                            </a>
                            {/foreach}
                        </div>
                        {/if}
                    </div>
                    {/foreach}
                </nav>
                
                <div class="auth-buttons">
                    <a href="/merchant/login" class="btn-login">
                        <i class="fas fa-sign-in-alt"></i>
                        商户登录
                    </a>
                    <a href="/merchant/register" class="btn-register">
                        <i class="fas fa-user-plus"></i>
                        商户注册
                    </a>
                </div>
            </div>
        </header>

        <main class="article-section">
            <div class="article-container">
                {foreach $notice as $article}
                <article class="notice-item" data-article-id="{$article.id}">
                    <h2 class="notice-title">{$article.title}</h2>
                    <div class="notice-content">
                        {$article.content|raw}
                    </div>
                    <div class="notice-meta">
                        <span><i class="far fa-clock"></i> {$article.create_at}</span>
                        <span class="view-count">
                            <i class="far fa-eye"></i> 
                            {$article.views ?? '0'} 次浏览
                        </span>
                    </div>
                </article>
                {/foreach}
            </div>
        </main>

        <footer class="footer">
            <div class="footer-container">
                {if $footer_service_show}
                <div class="footer-section">
                    <h3>服务中心</h3>
                    <ul>
                        <li><a href="{$footer_service_1_link}">{$footer_service_1}</a></li>
                        <li><a href="{$footer_service_2_link}">{$footer_service_2}</a></li>
                        <li><a href="{$footer_service_3_link}">{$footer_service_3}</a></li>
                        <li><a href="{$footer_service_4_link}">{$footer_service_4}</a></li>
                    </ul>
                </div>
                {/if}

                {if $footer_help_show}
                <div class="footer-section">
                    <h3>帮助中心</h3>
                    <ul>
                        <li><a href="{$footer_help_1_link}">{$footer_help_1}</a></li>
                        <li><a href="{$footer_help_2_link}">{$footer_help_2}</a></li>
                        <li><a href="{$footer_help_3_link}">{$footer_help_3}</a></li>
                        <li><a href="{$footer_help_4_link}">{$footer_help_4}</a></li>
                    </ul>
                </div>
                {/if}

                {if $footer_legal_show}
                <div class="footer-section">
                    <h3>法律责任</h3>
                    <ul>
                        <li><a href="{$footer_legal_1_link}">{$footer_legal_1}</a></li>
                        <li><a href="{$footer_legal_2_link}">{$footer_legal_2}</a></li>
                        <li><a href="{$footer_legal_3_link}">{$footer_legal_3}</a></li>
                        <li><a href="{$footer_legal_4_link}">{$footer_legal_4}</a></li>
                    </ul>
                </div>
                {/if}

                {if $footer_links_show}
                <div class="footer-section">
                    <h3>友情链接</h3>
                    <ul>
                        <li><a href="{$footer_links_1_link}">{$footer_links_1}</a></li>
                        <li><a href="{$footer_links_2_link}">{$footer_links_2}</a></li>
                        <li><a href="{$footer_links_3_link}">{$footer_links_3}</a></li>
                        <li><a href="{$footer_links_4_link}">{$footer_links_4}</a></li>
                    </ul>
                </div>
                {/if}
            </div>

            <div class="footer-bottom">
                {if !empty($icpNumber)}
                <a href="https://beian.miit.gov.cn/" target="_blank">{$icpNumber}</a>
                {/if}
                {if !empty($gaNumber)}
                <a href="{$icpCert}" target="_blank">{$gaNumber}</a>
                {/if}
            </div>
        </footer>

        <script>
            // 添加3D效果和交互
            document.querySelectorAll('.hex').forEach((hex, index) => {
                hex.style.setProperty('--delay', index);
                
                hex.addEventListener('mouseover', () => {
                    hex.style.transform = 'perspective(1000px) rotateX(40deg) rotateY(10deg) translateZ(20px)';
                });
                
                hex.addEventListener('mouseout', () => {
                    hex.style.transform = '';
                });
            });

            document.addEventListener('DOMContentLoaded', function() {
                const articles = document.querySelectorAll('.notice-item');
                articles.forEach(article => {
                    const articleId = article.getAttribute('data-article-id');
                    if (articleId) {
                        // 发送异步请求更新访问量
                        fetch(`/article/updateViews?id=${articleId}`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.code === 1) {
                                const viewCountElement = article.querySelector('.view-count');
                                if (viewCountElement) {
                                    viewCountElement.innerHTML = `<i class="far fa-eye"></i> ${data.views} 次浏览`;
                                }
                            }
                        })
                        .catch(error => console.error('Error:', error));
                    }
                });
                
                // 移动端菜单交互
                const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
                const navLinks = document.querySelector('.nav-links');
                const navItems = document.querySelectorAll('.nav-item');
                
                if (mobileMenuToggle) {
                    mobileMenuToggle.addEventListener('click', function() {
                        navLinks.classList.toggle('active');
                        this.innerHTML = navLinks.classList.contains('active') 
                            ? '<i class="fas fa-times"></i>' 
                            : '<i class="fas fa-bars"></i>';
                            
                        // 添加移动端登录/注册按钮
                        if (navLinks.classList.contains('active') && !document.querySelector('.mobile-auth')) {
                            const mobileAuth = document.createElement('div');
                            mobileAuth.className = 'mobile-auth';
                            mobileAuth.innerHTML = `
                                <a href="/merchant/login" class="btn-login">
                                    <i class="fas fa-sign-in-alt"></i> 商户登录
                                </a>
                                <a href="/merchant/register" class="btn-register">
                                    <i class="fas fa-user-plus"></i> 商户注册
                                </a>
                            `;
                            navLinks.appendChild(mobileAuth);
                        }
                    });
                }
                
                // 移动端子菜单交互
                const handleSubMenu = () => {
                    if (window.innerWidth <= 576) {
                        navItems.forEach(item => {
                            const link = item.querySelector('.nav-link');
                            const subMenu = item.querySelector('.submenu');
                            
                            if (subMenu) {
                                link.addEventListener('click', function(e) {
                                    e.preventDefault();
                                    
                                    // 关闭其他打开的子菜单
                                    navItems.forEach(otherItem => {
                                        if (otherItem !== item && otherItem.classList.contains('open')) {
                                            otherItem.classList.remove('open');
                                            const otherIcon = otherItem.querySelector('.dropdown-icon');
                                            if (otherIcon) {
                                                otherIcon.style.transform = 'rotate(0)';
                                            }
                                        }
                                    });
                                    
                                    // 切换当前子菜单
                                    item.classList.toggle('open');
                                    const dropdownIcon = link.querySelector('.dropdown-icon');
                                    if (dropdownIcon) {
                                        dropdownIcon.style.transform = item.classList.contains('open') 
                                            ? 'rotate(180deg)' 
                                            : 'rotate(0)';
                                    }
                                });
                            }
                        });
                    }
                };
                
                handleSubMenu();
                
                // 窗口大小变化时重新处理
                window.addEventListener('resize', function() {
                    if (window.innerWidth > 576 && navLinks.classList.contains('active')) {
                        navLinks.classList.remove('active');
                        mobileMenuToggle.innerHTML = '<i class="fas fa-bars"></i>';
                        const mobileAuth = document.querySelector('.mobile-auth');
                        if (mobileAuth) {
                            mobileAuth.remove();
                        }
                    }
                });
            });
        </script>
    </body>
</html>
