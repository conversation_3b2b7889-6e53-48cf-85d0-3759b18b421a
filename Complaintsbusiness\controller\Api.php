<?php
namespace plugin\Complaintsbusiness\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;
use think\facade\Cache;

class Api extends BasePlugin {

    // 需要登录的方法
    protected $scene = [
        'admin',  // 表示后台需要登录
        'chick'   // 配置页面需要登录
    ];
    
    // 无需登录的方法
    protected $noNeedLogin = [
        'index',
        'getComplaintsList'
    ];

    /**
     * 首页
     */
    public function index()
    {
        try {
            // 获取基础配置
            $logo = '/'.ltrim(Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'logo')
                ->value('value'), '/');
            
            $siteName = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'app_name')
                ->value('value');
            
            $favicon = '/'.ltrim(Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'favicon')
                ->value('value'), '/');

            // 获取导航菜单数据
            $navItems = Db::name('nav')
                ->where('status', 1)  // 只获取启用的导航
                ->where('pid', 0)     // 只获取一级导航
                ->field(['name', 'href'])
                ->order('sort', 'asc') // 按sort字段升序排序
                ->select()
                ->toArray();

            View::assign([
                'logo' => $logo,
                'siteName' => $siteName,
                'favicon' => $favicon,
                'navItems' => json_encode($navItems) // 转为JSON传递给前端
            ]);
            
            return View::fetch();
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 插件配置页面
     */
    public function chick()
    {
        return View::fetch('chick');  // 明确指定模板文件
    }

    /**
     * 切换导航菜单状态
     */
    public function toggleNav()
    {
        try {
            // 检查是否已存在该菜单
            $exists = Db::name('nav')->where('href', '/plugin/Complaintsbusiness/Api/index')->find();
            
            if ($exists) {
                // 如果存在则删除
                Db::name('nav')->where('href', '/plugin/Complaintsbusiness/Api/index')->delete();
                return json(['code' => 1, 'msg' => '已从导航栏移除']);
            } else {
                // 如果不存在则添加
                $maxId = Db::name('nav')->max('id') + 1;
                
                // 插入导航菜单
                Db::name('nav')->insert([
                    'id' => $maxId,
                    'pid' => 0,
                    'type' => 'C',
                    'name' => '商家投诉率',
                    'href' => '/plugin/Complaintsbusiness/Api/index',
                    'sort' => 0,
                    'target' => 0,
                    'status' => 1,
                    'create_time' => time(),
                    'update_time' => time()
                ]);
                return json(['code' => 1, 'msg' => '已添加到导航栏']);
            }
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '操作失败：' . $e->getMessage()]);
        }
    }

    /**
     * 检查导航菜单状态
     */
    public function checkNav()
    {
        $exists = Db::name('nav')->where('href', '/plugin/Complaintsbusiness/Api/index')->find();
        return json(['exists' => !empty($exists)]);
    }

    /**
     * 清除缓存
     */
    public function clearCache()
    {
        try {
            Cache::delete('complaints_list');
            return json(['code' => 1, 'msg' => '缓存已清除']);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '清除缓存失败']);
        }
    }

    /**
     * 获取投诉商家列表
     */
    public function getComplaintsList()
    {
        try {
            $page = input('page/d', 1);
            $limit = input('limit/d', 10);
            $offset = ($page - 1) * $limit;

            // 获取商家列表及其投诉信息
            $users = Db::name('user')
                ->alias('u')
                ->leftJoin('complaint c', 'u.id = c.user_id')
                ->leftJoin('order o', 'u.id = o.user_id AND o.status = 1') // 只关联成交的订单
                ->field([
                    'u.id',
                    'u.username',
                    'u.nickname',
                    'u.avatar',
                    'COUNT(DISTINCT c.order_id) as complaint_orders', // 投诉订单数
                    'COUNT(DISTINCT o.id) as total_orders',           // 成交订单总数
                    'MAX(c.create_time) as latest_complaint_time'
                ])
                ->group('u.id')
                ->having('complaint_orders > 0')
                ->order('complaint_orders', 'desc')
                ->limit($offset, $limit)
                ->select()
                ->each(function($item) {
                    // 用户名脱敏处理
                    $item['username'] = $this->maskString($item['username']);
                    // 店铺名称脱敏处理
                    if ($item['nickname']) {
                        $item['nickname'] = $this->maskString($item['nickname']);
                    }
                    // 计算投诉率：投诉订单数/成交订单总数
                    $item['complaint_rate'] = $item['total_orders'] > 0 
                        ? round(($item['complaint_orders'] / $item['total_orders']) * 100, 2) 
                        : 0;
                    return $item;
                })
                ->toArray();

            // 获取总记录数
            $total = Db::name('user')
                ->alias('u')
                ->join('complaint c', 'u.id = c.user_id')
                ->group('u.id')
                ->count();

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'total' => $total,
                'items' => $users,
                'page' => $page,
                'limit' => $limit
            ]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取数据失败：' . $e->getMessage()]);
        }
    }

    /**
     * 字符串脱敏处理
     * @param string $string 需要脱敏的字符串
     * @return string
     */
    private function maskString($string) {
        $length = mb_strlen($string);
        if ($length <= 2) {
            return mb_substr($string, 0, 1) . '*';
        } else {
            $firstChar = mb_substr($string, 0, 1);
            $lastChar = mb_substr($string, -1);
            $stars = str_repeat('*', min($length - 2, 3));
            return $firstChar . $stars . $lastChar;
        }
    }
} 