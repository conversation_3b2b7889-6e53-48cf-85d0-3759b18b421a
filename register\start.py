#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
970faka.com 注册工具启动器
提供多种启动方式
"""

import sys
import os
from config.config_manager import get_config

def show_menu():
    """显示主菜单"""
    print("[启动] 970faka.com 注册工具")
    print("=" * 50)
    print("请选择启动方式:")
    print()
    print("1. [显示]  图形界面 (GUI)")
    print("2. 🤖 Selenium自动化注册")
    print("3. 📦 批量自动化注册")
    print("4. ⚙️  配置管理")
    print("5. [信息] 查看当前配置")
    print("6. [设置] 修复ChromeDriver")
    print("7. ❓ 帮助信息")
    print("0. 🚪 退出")
    print()

def start_gui():
    """启动图形界面"""
    try:
        print("[显示] 启动图形界面...")
        from gui.register_gui import main as gui_main
        gui_main()
    except ImportError as e:
        print(f"[失败] 启动GUI失败，缺少依赖: {e}")
        print("请安装PyQt6: pip install PyQt6")
    except Exception as e:
        print(f"[失败] 启动GUI失败: {e}")

def start_selenium():
    """启动Selenium自动化注册"""
    try:
        print("🤖 启动Selenium自动化注册...")
        from browser_automation.selenium_register import main as selenium_main
        selenium_main()
    except ImportError as e:
        print(f"[失败] 启动Selenium失败，缺少依赖: {e}")
        print("请安装Selenium: pip install selenium")
        print("并确保已安装Chrome浏览器和ChromeDriver")
    except Exception as e:
        print(f"[失败] 启动Selenium失败: {e}")

def start_batch():
    """启动批量注册"""
    try:
        print("📦 启动批量自动化注册...")
        from core.batch_register import main as batch_main
        batch_main()
    except ImportError as e:
        print(f"[失败] 启动批量注册失败，缺少依赖: {e}")
        print("请安装所需依赖")
    except Exception as e:
        print(f"[失败] 启动批量注册失败: {e}")

def config_management():
    """配置管理"""
    print("⚙️ 配置管理")
    print("-" * 30)
    
    config = get_config()
    
    print("请选择操作:")
    print("1. 配置椰子云")
    print("2. 配置Selenium")
    print("3. 重置配置")
    print("0. 返回主菜单")
    
    choice = input("\n请输入选择: ").strip()
    
    if choice == "1":
        configure_yezi_cloud(config)
    elif choice == "2":
        configure_selenium(config)
    elif choice == "3":
        reset_config(config)
    elif choice == "0":
        return
    else:
        print("[失败] 无效选择")

def configure_yezi_cloud(config):
    """配置椰子云"""
    print("\n[手机] 配置椰子云")
    print("-" * 20)
    
    current_config = config.get_yezi_cloud_config()
    
    print(f"当前配置:")
    print(f"  用户名: {current_config.get('username', '未配置')}")
    print(f"  密码: {'*' * len(current_config.get('password', '')) if current_config.get('password') else '未配置'}")
    print(f"  项目ID: {current_config.get('project_id', '未配置')}")
    
    print("\n请输入新配置 (直接回车保持原值):")
    
    username = input(f"椰子云用户名 [{current_config.get('username', '')}]: ").strip()
    if not username:
        username = current_config.get('username', '')
    
    password = input(f"椰子云密码 [{'*' * 8 if current_config.get('password') else ''}]: ").strip()
    if not password:
        password = current_config.get('password', '')
    
    project_id = input(f"椰子云项目ID [{current_config.get('project_id', '')}]: ").strip()
    if not project_id:
        project_id = current_config.get('project_id', '')
    
    if username and password and project_id:
        config.update_yezi_cloud_config(username, password, project_id)
        print("[成功] 椰子云配置已保存")
        
        # 测试连接
        test_connection = input("是否测试连接？(y/n): ").strip().lower()
        if test_connection == 'y':
            test_yezi_cloud_connection(username, password)
    else:
        print("[失败] 配置不完整，未保存")

def test_yezi_cloud_connection(username, password):
    """测试椰子云连接"""
    try:
        print("[检查] 测试椰子云连接...")
        from core.sms_api import YeziCloudAPI
        
        api = YeziCloudAPI(username=username, password=password)
        result = api.login()
        
        if result['success']:
            print(f"[成功] 连接成功！余额: {result.get('balance', '0')}")
        else:
            print(f"[失败] 连接失败: {result['error']}")
    except Exception as e:
        print(f"[失败] 测试连接异常: {e}")

def configure_selenium(config):
    """配置Selenium"""
    print("\n🤖 配置Selenium")
    print("-" * 20)
    
    selenium_config = config.get_selenium_config()
    
    print(f"当前配置:")
    print(f"  无头模式: {selenium_config['headless']}")
    print(f"  窗口宽度: {selenium_config['window_width']}")
    print(f"  窗口高度: {selenium_config['window_height']}")
    print(f"  等待超时: {selenium_config['wait_timeout']}秒")
    print(f"  短信等待超时: {selenium_config['sms_wait_timeout']}秒")
    
    print("\n请输入新配置 (直接回车保持原值):")
    
    headless = input(f"无头模式 (True/False) [{selenium_config['headless']}]: ").strip()
    if headless.lower() in ['true', 'false']:
        config.set('SELENIUM', 'headless', headless.lower() == 'true')
    
    width = input(f"窗口宽度 [{selenium_config['window_width']}]: ").strip()
    if width.isdigit():
        config.set('SELENIUM', 'window_width', width)
    
    height = input(f"窗口高度 [{selenium_config['window_height']}]: ").strip()
    if height.isdigit():
        config.set('SELENIUM', 'window_height', height)
    
    timeout = input(f"等待超时(秒) [{selenium_config['wait_timeout']}]: ").strip()
    if timeout.isdigit():
        config.set('SELENIUM', 'wait_timeout', timeout)
    
    sms_timeout = input(f"短信等待超时(秒) [{selenium_config['sms_wait_timeout']}]: ").strip()
    if sms_timeout.isdigit():
        config.set('SELENIUM', 'sms_wait_timeout', sms_timeout)
    
    config.save_config()
    print("[成功] Selenium配置已保存")

def reset_config(config):
    """重置配置"""
    print("\n[重试] 重置配置")
    print("-" * 20)
    
    confirm = input("确定要重置所有配置吗？(y/n): ").strip().lower()
    if confirm == 'y':
        # 备份当前配置
        config.backup_config()
        
        # 重新创建默认配置
        config.create_default_config()
        print("[成功] 配置已重置为默认值")
        print("💾 原配置已备份")
    else:
        print("[失败] 取消重置")

def show_current_config():
    """显示当前配置"""
    print("[信息] 当前配置")
    print("=" * 50)
    
    config = get_config()
    
    # 基本配置
    basic_config = config.get_section('BASIC')
    print("\n[基本配置]")
    for key, value in basic_config.items():
        print(f"  {key}: {value}")
    
    # 注册配置
    register_config = config.get_register_config_enhanced()
    print("\n[注册配置]")
    for key, value in register_config.items():
        print(f"  {key}: {value}")
    
    # Selenium配置
    selenium_config = config.get_selenium_config()
    print("\n[Selenium配置]")
    for key, value in selenium_config.items():
        print(f"  {key}: {value}")
    
    # 椰子云配置
    yezi_config = config.get_yezi_cloud_config()
    print("\n[椰子云配置]")
    print(f"  username: {yezi_config.get('username', '未配置')}")
    print(f"  password: {'*' * len(yezi_config.get('password', '')) if yezi_config.get('password') else '未配置'}")
    print(f"  project_id: {yezi_config.get('project_id', '未配置')}")
    print(f"  配置状态: {'[成功] 已配置' if config.is_yezi_cloud_configured() else '[失败] 未配置'}")

def fix_chromedriver():
    """修复ChromeDriver问题"""
    try:
        print("[设置] 启动ChromeDriver修复工具...")
        from utils.fix_chromedriver import main as fix_main
        fix_main()
    except ImportError as e:
        print(f"[失败] 启动修复工具失败，缺少依赖: {e}")
        print("请安装所需依赖: pip install requests")
    except Exception as e:
        print(f"[失败] 修复工具异常: {e}")

def show_help():
    """显示帮助信息"""
    print("❓ 帮助信息")
    print("=" * 50)
    print()
    print("📖 使用说明:")
    print("1. 首次使用请先配置椰子云信息")
    print("2. 推荐使用Selenium自动化模式")
    print("3. 批量注册适合大量用户注册")
    print("4. GUI界面提供完整的可视化操作")
    print()
    print("[设置] 依赖要求:")
    print("- Python 3.7+")
    print("- selenium (pip install selenium)")
    print("- PyQt6 (pip install PyQt6) - GUI模式需要")
    print("- Chrome浏览器 + ChromeDriver")
    print()
    print("[手机] 椰子云配置:")
    print("- 需要椰子云账号和项目ID")
    print("- 用于自动获取手机号和短信验证码")
    print("- 不配置则需要手动输入")
    print()
    print("🤖 Selenium模式:")
    print("- 完全自动化的浏览器操作")
    print("- 支持图形验证码自动处理")
    print("- 推荐用于单个或少量用户注册")
    print()
    print("📦 批量模式:")
    print("- 支持多线程并发注册")
    print("- 适合大量用户注册")
    print("- 自动生成用户数据")

def main():
    """主函数"""
    while True:
        try:
            show_menu()
            choice = input("请输入选择: ").strip()
            
            if choice == "1":
                start_gui()
            elif choice == "2":
                start_selenium()
            elif choice == "3":
                start_batch()
            elif choice == "4":
                config_management()
            elif choice == "5":
                show_current_config()
            elif choice == "6":
                fix_chromedriver()
            elif choice == "7":
                show_help()
            elif choice == "0":
                print("👋 再见！")
                break
            else:
                print("[失败] 无效选择，请重新输入")
            
            if choice != "0":
                input("\n按回车键继续...")
                print("\n" + "=" * 60 + "\n")
        
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，再见！")
            break
        except Exception as e:
            print(f"\n[失败] 程序异常: {e}")
            input("按回车键继续...")

if __name__ == "__main__":
    main()
