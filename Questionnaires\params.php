<?php

return [
    // 默认问卷说明
    'questionnaire_description' => '请您根据举报页面提示，提交证明材料并填写举报说明，平台将会在7个工作日内处理您的举报信息。',
    
    // 默认问题类型
    'question_types' => array (
  0 => 
  array (
    'id' => 'shop_name',
    'name' => '商家店铺昵称',
    'icon' => 'Question',
    'component' => 'input',
    'required' => true,
    'placeholder' => '请输入商家店铺昵称',
  ),
  1 => 
  array (
    'id' => 'shop_link',
    'name' => '违规店铺链接',
    'icon' => 'Link',
    'component' => 'input',
    'required' => true,
    'placeholder' => '请输入违规店铺链接',
  ),
  2 => 
  array (
    'id' => 'product_link',
    'name' => '违规商品链接',
    'icon' => 'Link',
    'component' => 'input',
    'required' => true,
    'placeholder' => '请输入违规商品链接',
  ),
  3 => 
  array (
    'id' => 'violation_type',
    'name' => '违规类型',
    'icon' => 'Warning',
    'component' => 'radio',
    'required' => true,
    'options' => 
    array (
      0 => '商品违规',
      1 => '店铺违规',
      2 => '色情违规',
      3 => '各种违规',
    ),
  ),
  4 => 
  array (
    'id' => 'proofs',
    'name' => '违规证明材料',
    'icon' => 'Upload',
    'component' => 'upload',
    'required' => true,
    'placeholder' => '请上传违规证明材料，最多上传5张图片',
    'maxFiles' => '5',
    'fileTypes' => 
    array (
      0 => 'image',
    ),
  ),
  5 => 
  array (
    'id' => 'email',
    'name' => '联系邮箱',
    'icon' => 'Message',
    'component' => 'input',
    'required' => true,
    'placeholder' => '请输入您的联系邮箱，用于接收处理结果',
  ),
  6 => 
  array (
    'id' => 'remark',
    'name' => '补充说明',
    'icon' => 'Document',
    'component' => 'textarea',
    'required' => false,
    'placeholder' => '请输入补充说明信息（选填）',
  ),
),
    
    // 字段映射
    'field_mappings' => array (
  'shop_name' => 
  array (
    'id' => 'shop_name',
    'name' => '商家店铺昵称',
    'dataType' => 'string',
    'defaultValue' => '',
  ),
  'shop_link' => 
  array (
    'id' => 'shop_link',
    'name' => '违规店铺链接',
    'dataType' => 'string',
    'defaultValue' => '',
  ),
  'product_link' => 
  array (
    'id' => 'product_link',
    'name' => '违规商品链接',
    'dataType' => 'string',
    'defaultValue' => '',
  ),
  'violation_type' => 
  array (
    'id' => 'violation_type',
    'name' => '违规类型',
    'dataType' => 'string',
    'defaultValue' => '',
  ),
  'proofs' => 
  array (
    'id' => 'proofs',
    'name' => '违规证明材料',
    'dataType' => 'string',
    'defaultValue' => '',
  ),
  'email' => 
  array (
    'id' => 'email',
    'name' => '联系邮箱',
    'dataType' => 'string',
    'defaultValue' => '',
  ),
  'remark' => 
  array (
    'id' => 'remark',
    'name' => '补充说明',
    'dataType' => 'string',
    'defaultValue' => '',
  ),
),
    
    // 文件上传配置
    'upload_api' => '/shopApi/Upload/file',
    
    // 默认模板类型
    'template_type' => 'default',
    
    // 邮箱提交次数限制，默认3次
    'email_submit_limit' => 3,
    
    // IP提交邮箱数量限制，默认5个
    'ip_email_limit' => 5,
    
    // 功能开关配置
    'features' => array (
  'report_enabled' => false,
  'questionnaire_enabled' => false,
  'email_verification' => false,
  'captcha_enabled' => false,
)
];
