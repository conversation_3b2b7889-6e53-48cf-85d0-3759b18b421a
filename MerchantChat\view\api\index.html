<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>商家聊天配置</title>
    <script>
        // 这里可以从后端模板变量中获取
        window.merchant_id = '{$merchant_id|default=0}'; // ThinkPHP 模板语法
        window.shop_name = '{$shop_name|default=""}';
    </script>
    <style>
        /* 基础样式 */
        body {
            background: #f5f7fa;
            margin: 0;
            padding: 16px;
            min-height: 500px;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }

        .el-card {
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05) !important;
            margin: 0 auto;
            max-width: 900px;
        }
        
        .card-header {
            display: flex;
            align-items: center;
        }
        
        .card-header span {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
        }

        /* 问号图标样式优化 */
        .question-icon {
            display: inline-block;
            width: 16px;
            height: 16px;
            line-height: 16px;
            text-align: center;
            background-color: #909399;
            color: white;
            border-radius: 50%;
            font-size: 12px;
            margin-left: 5px;
            cursor: pointer;
        }

        /* 响应式布局 */
        @media screen and (max-width: 768px) {
            body {
                padding: 10px;
            }

            .el-card {
                margin: 0;
                border-radius: 8px;
            }

            .el-form {
                padding: 10px;
            }

            .el-form-item {
                margin-bottom: 24px;
            }

            /* 调整标签宽度 */
            .el-form {
                --el-form-label-width: 90px !important;
            }

            .el-form-item__label {
                float: none;
                display: block;
                text-align: left;
                padding: 0 0 8px 0;
                line-height: 1.4;
                white-space: normal;
            }

            .el-form-item__content {
                margin-left: 0 !important;
            }

            /* 调整数字输入框组的布局 */
            div[style*="display: flex; align-items: center;"] {
                flex-wrap: wrap;
                gap: 8px;
            }

            div[style*="display: flex; align-items: center;"] > span {
                min-width: 60px;
            }

            /* 优化输入框 */
            .el-input {
                width: 100%;
            }

            /* 保存按钮 */
            .el-button {
                width: 100%;
                margin-top: 20px;
                height: 44px;
            }
        }

        /* 超小屏幕优化 */
        @media screen and (max-width: 375px) {
            body {
                padding: 8px;
            }
            .el-form-item__label {
                font-size: 14px;
            }
            .el-form-item-msg {
                font-size: 11px;
            }
        }

        /* 暗黑模式支持 */
        @media (prefers-color-scheme: dark) {
            body {
                background: #1a1a1a;
            }
            .el-card {
                background: #2b2b2b;
            }
        }

        /* 触摸优化 */
        @media (hover: none) {
            .el-button,
            .el-switch {
                min-height: 44px;
            }
        }

        /* 横屏模式优化 */
        @media screen and (max-width: 768px) and (orientation: landscape) {
            .el-form {
                max-width: 600px;
                margin: 0 auto;
            }
        }

        /* 高分辨率屏幕优化 */
        @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
            .el-button,
            .el-input,
            .el-switch {
                transform: translateZ(0);
            }
        }

        /* 表单提示信息 */
        .el-form-item-msg {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        
        /* 表单控件样式 */
        .el-form {
            max-width: 800px;
            margin: 0 auto;
            padding-top: 10px;
        }
        
        .el-form-item:last-child {
            margin-bottom: 0;
        }
        
        /* 输入框样式优化 */
        .el-input__inner {
            text-align: left;
        }
        
        /* 表单项间距 */
        .el-form-item {
            margin-bottom: 22px;
            position: relative;
        }
        
        /* 链接输入框宽度 */
        .el-form-item__content .el-input {
            max-width: 500px;
        }

        /* 预览框样式 */
        .preview-box {
            border: 1px dashed #ccc;
            padding: 20px;
            margin-top: 20px;
            border-radius: 5px;
            background-color: #f8f8f8;
        }
        
        .preview-box pre {
            background-color: #fff;
            border: 1px solid #eee;
            padding: 10px;
            border-radius: 3px;
            word-break: break-all;
            white-space: pre-wrap;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            font-size: 13px;
            line-height: 1.5;
            overflow-x: auto;
        }
        
        /* 问号提示的自定义样式 */
        .el-tooltip__trigger {
            display: inline-flex;
            align-items: center;
        }
    </style>
</head>

<body>
    <div id="app">
        <el-card shadow="never">
            <template #header>
                <div class="card-header">
                    <span>商家聊天配置</span>
                </div>
            </template>
            
            <el-form :model="form" label-width="90px">
                <!-- 聊天开关 -->
                <el-form-item label="聊天开关">
                    <div style="display: flex; align-items: center;">
                        <el-switch 
                            v-model="form.status" 
                            :active-value="1" 
                            :inactive-value="0"
                            :disabled="!canEdit"
                        />
                    </div>
                    <div class="el-form-item-msg">开启后将在网站中显示聊天窗口</div>
                </el-form-item>

                <!-- 业务ID -->
                <el-form-item label="business_id">
                    <template #label>
                        business_id
                        <el-tooltip content="此处设置的business_id将显示在嵌入到网站的脚本中，如:<script>var ymwl={business_id:193,...}</script>" placement="top">
                            <span style="display:inline-block;width:16px;height:16px;line-height:16px;text-align:center;background-color:#909399;color:white;border-radius:50%;font-size:12px;margin-left:5px;cursor:pointer;">?</span>
                        </el-tooltip>
                    </template>
                    <el-input 
                        v-model="form.business_id" 
                        placeholder="请输入business_id:后面的值，例如: 151" 
                        :disabled="!canEdit"
                    />
                    <div class="el-form-item-msg">聊天脚本中的 business_id 参数值</div>
                </el-form-item>

                <!-- Token -->
                <el-form-item label="Token">
                    <template #label>
                        Token
                        <el-tooltip content="此处设置的Token将显示在嵌入到网站的脚本中，如:<script>var ymwl={token:'afd4d8896d3bd733a2ebd12d788c62d0',...}</script>" placement="top">
                            <span style="display:inline-block;width:16px;height:16px;line-height:16px;text-align:center;background-color:#909399;color:white;border-radius:50%;font-size:12px;margin-left:5px;cursor:pointer;">?</span>
                        </el-tooltip>
                    </template>
                    <el-input 
                        v-model="form.token" 
                        placeholder="请输入Token，例如: afd4d8896d3bd733a2ebd12d788c62d0" 
                        :disabled="!canEdit"
                    />
                    <div class="el-form-item-msg">聊天脚本中的 token 参数值</div>
                </el-form-item>

                <!-- 脚本URL -->
                <el-form-item label="脚本URL">
                    <template #label>
                        脚本URL
                        <el-tooltip content="此URL将作为脚本src的值在网站中引用，如:<script src='https://kf.y1yun.top/assets/front/ymwl_online.js?v=1749626195'></script>" placement="top">
                            <span style="display:inline-block;width:16px;height:16px;line-height:16px;text-align:center;background-color:#909399;color:white;border-radius:50%;font-size:12px;margin-left:5px;cursor:pointer;">?</span>
                        </el-tooltip>
                    </template>
                    <el-input 
                        v-model="form.script_url" 
                        placeholder="请输入聊天脚本URL，默认为kf.y1yun.top开头的地址" 
                        :disabled="!canEdit"
                    />
                    <div class="el-form-item-msg">聊天脚本URL，默认为: https://kf.y1yun.top/assets/front/ymwl_online.js</div>
                </el-form-item>

                <!-- 版本号字段 -->
                <el-form-item label="版本号">
                    <template #label>
                        版本号
                        <el-tooltip content="此版本号将作为脚本src的URL参数v的值，如:ymwl_online.js?v=1749626195" placement="top">
                            <span style="display:inline-block;width:16px;height:16px;line-height:16px;text-align:center;background-color:#909399;color:white;border-radius:50%;font-size:12px;margin-left:5px;cursor:pointer;">?</span>
                        </el-tooltip>
                    </template>
                    <el-input 
                        v-model="form.script_version" 
                        placeholder="请输入版本号，例如: 1749606431，留空则自动生成" 
                        :disabled="!canEdit"
                    />
                    <div class="el-form-item-msg">脚本URL中的v参数值，不填则自动生成时间戳</div>
                </el-form-item>

                <!-- 预览代码 -->
                <el-form-item label="预览代码">
                    <div class="preview-box">
                        <p>您的聊天代码将如下所示:</p>
                        <pre id="code-preview">{{ previewCode }}</pre>
                    </div>
                </el-form-item>

                <!-- 保存按钮 -->
                <el-form-item>
                    <el-button 
                        type="primary" 
                        :loading="loading" 
                        @click="save"
                        :disabled="!canEdit"
                    >
                        保存设置
                    </el-button>
                    <div v-if="!canEdit" class="el-form-item-msg" style="color: #f56c6c; margin-top: 10px;">
                        管理员已禁止商家修改聊天配置，如需更改请联系管理员
                    </div>
                </el-form-item>
            </el-form>
        </el-card>
    </div>

    <!-- 引入依赖库 -->
    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>

    <script>
        const { createApp, ref, reactive, onMounted, computed, watch } = Vue;
        const { ElMessage } = ElementPlus;

        // 配置 axios
        axios.defaults.withCredentials = true;
        axios.defaults.validateStatus = function (status) {
            return status >= 200 && status < 300; // 默认值
        };

        createApp({
            setup() {
                // 状态定义
                const loading = ref(false);
                const canEdit = ref(true); // 是否允许商家编辑
                
                const form = reactive({
                    status: 0,
                    business_id: '',
                    token: '',
                    script_url: 'https://kf.y1yun.top/assets/front/ymwl_online.js',
                    script_version: ''
                });

                // 计算预览代码
                const previewCode = computed(() => {
                    const businessId = form.business_id || '';
                    const token = form.token || '';
                    const scriptUrl = form.script_url || 'https://kf.y1yun.top/assets/front/ymwl_online.js';
                    
                    const scriptTag1 = '<script>var ymwl={visiter_id:"",visiter_name:"",avatar:"",business_id:' + businessId + ',groupid:0,token:"' + token + '"}<\/script>';
                    const scriptTag2 = '<script src="' + scriptUrl + '?v=' + (form.script_version || new Date().getTime()) + '" charset="UTF-8"><\/script>';
                    
                    return scriptTag1 + '\n' + scriptTag2;
                });

                onMounted(() => {
                    let retryCount = 0;
                    const maxRetries = 3;
                    
                    const tryFetchData = async () => {
                        try {
                            await fetchData();
                        } catch (error) {
                            if (retryCount < maxRetries) {
                                retryCount++;
                                console.log(`获取数据失败，第 ${retryCount} 次重试...`);
                                setTimeout(tryFetchData, 1000 * retryCount); // 递增重试延迟
                            } else {
                                console.error('多次重试后仍然失败');
                                ElMessage.error('加载数据失败，请刷新页面重试');
                            }
                        }
                    };

                    tryFetchData();
                });

                axios.defaults.withCredentials = true;
                
                const fetchData = async () => {
                    try {
                        // 获取当前登录用户的数据
                        const res = await axios.post("/plugin/MerchantChat/api/fetchData", {
                            merchant_id: window.merchant_id, // 如果页面中有商户ID
                            shop_name: window.shop_name // 如果页面中有商家名称
                        });
                        
                        if (res.data?.code === 200 && res.data.data) {
                            const data = res.data.data;
                            // 使用解构赋值并设置默认值
                            const {
                                status = 0,
                                business_id = '',
                                token = '',
                                script_url = 'https://kf.y1yun.top/assets/front/ymwl_online.js',
                                script_version = '',
                                merchant_can_edit = 1
                            } = data;

                            // 设置是否可编辑
                            canEdit.value = parseInt(merchant_can_edit) === 1;
                            
                            // 禁用编辑时给用户一个提示
                            if (!canEdit.value) {
                                ElMessage.warning('管理员已禁止商家修改聊天配置');
                            }

                            // 更新表单数据
                            Object.assign(form, {
                                status: parseInt(status),
                                business_id,
                                token,
                                script_url,
                                script_version
                            });
                        } else if (res.data?.code === 403) {
                            ElMessage.error('请先登录');
                            // 可以在这里添加重定向到登录页面的逻辑
                            // window.location.href = '/login';
                        } else {
                            ElMessage.error(res.data?.msg || '获取数据失败');
                            console.error('获取数据失败:', res.data);
                        }
                    } catch (error) {
                        console.error('获取数据失败:', error);
                        ElMessage.error('获取数据失败，请检查网络连接');
                        throw error;
                    }
                };

                const save = async () => {
                    // 检查是否有权限保存
                    if (!canEdit.value) {
                        ElMessage.error('管理员已禁止商家修改聊天配置');
                        return;
                    }

                    // 验证表单
                    if (form.status === 1) {
                        if (!form.business_id) {
                            ElMessage.error('请输入业务ID');
                            return;
                        }
                        if (!form.token) {
                            ElMessage.error('请输入Token');
                            return;
                        }
                        if (!form.script_url) {
                            ElMessage.error('请输入脚本URL');
                            return;
                        }
                    }
                    
                    try {
                        loading.value = true;
                        
                        const res = await axios.post("/plugin/MerchantChat/api/save", form);
                        if (res.data?.code === 200) {
                            ElMessage.success('保存成功');
                            await fetchData();
                        } else {
                            ElMessage.error(res.data?.msg || '保存失败');
                        }
                    } catch (error) {
                        console.error('保存失败:', error);
                        ElMessage.error('保存失败，请稍后重试');
                    } finally {
                        loading.value = false;
                    }
                };

                // 监听业务ID、Token和脚本URL的变化，更新预览代码
                watch([() => form.business_id, () => form.token, () => form.script_url], () => {
                    // 预览代码会自动更新，因为它是一个计算属性
                }, { deep: true });

                // 返回数据和方法
                return {
                    loading,
                    form,
                    canEdit,
                    save,
                    previewCode
                };
            }
        })
        .use(ElementPlus)
        .mount('#app');
    </script>
</body>
</html> 