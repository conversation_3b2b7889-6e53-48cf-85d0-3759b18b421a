<?php

namespace plugin\StorageHostReplace\controller;

use app\common\controller\BasePlugin;
use think\facade\View;

class Api extends BasePlugin {

    protected $scene = [
        'admin'
    ];
    protected $noNeedLogin = [];

    public function index() {
        return View::fetch();
    }

    private $array = [];

    public function searchHost() {
        \app\common\model\Cash::chunk(100, function ($cashs) {
            foreach ($cashs as $cash) {
                $collect_info = $cash->collect_info;

                if ($cash->collect_type == 1) {
                    // 支付宝
                    if (($collect_info->alipay_qrcode ?? '') != '') {
                        $this->handle($collect_info->alipay_qrcode);
                    }
                } elseif ($cash->collect_type == 2) {
                    // 微信
                    if (($collect_info->weixin_qrcode ?? '') != '') {
                        $this->handle($collect_info->weixin_qrcode);
                    }
                }
            }
        });

        \app\common\model\UserCollect::chunk(100, function ($collects) {
            foreach ($collects as $collect) {
                $collect_info = $collect->collect_info;
                if ($collect->collect_type == 1) {
                    // 支付宝
                    if (($collect_info->alipay_qrcode ?? '') != '') {
                        $this->handle($collect_info->alipay_qrcode);
                    }
                } elseif ($collect->collect_type == 2) {
                    // 微信
                    if (($collect_info->weixin_qrcode ?? '') != '') {
                        $this->handle($collect_info->weixin_qrcode);
                    }
                }
            }
        });

        $this->handle(sysconf('website.logo'));
        $this->handle(sysconf('website.favicon'));
        $this->handle(sysconf('kefu.qrcode'));

        \app\common\model\Article::chunk(100, function ($articles) {
            foreach ($articles as $article) {
                $pattern = '/<img[^>]+src=["\']([^"\']+)["\']|<source[^>]+src=["\']([^"\']+)["\']/i';
                preg_match_all($pattern, $article->content, $matches);
                $sources = $matches[1];
                foreach ($sources as $source) {
                    $this->handle($source);
                }
            }
        });

        \app\common\model\Complaint::chunk(100, function ($complaints) {
            foreach ($complaints as $complaint) {
                $images = (array) $complaint->images;
                foreach ($images as $image) {
                    $this->handle($image);
                }
            }
        });

        \app\common\model\ComplaintMessage::chunk(100, function ($messages) {
            foreach ($messages as $message) {
                if ($message->content_type == 1) {
                    $this->handle($message->content);
                }
            }
        });

        \app\common\model\Goods::chunk(100, function ($goods) {
            foreach ($goods as $good) {
                $pattern = '/<img[^>]+src=["\']([^"\']+)["\']|<source[^>]+src=["\']([^"\']+)["\']/i';
                preg_match_all($pattern, $good->description, $matches);
                $sources = $matches[1];
                foreach ($sources as $source) {
                    $this->handle($source);
                }

                $this->handle($good->image);
            }
        });

        \app\common\model\GoodsArticle::chunk(100, function ($goods) {
            foreach ($goods as $good) {
                $pattern = '/<img[^>]+src=["\']([^"\']+)["\']|<source[^>]+src=["\']([^"\']+)["\']/i';
                preg_match_all($pattern, $good->paid_content, $matches);
                $sources = $matches[1];
                foreach ($sources as $source) {
                    $this->handle($source);
                }
            }
        });

        \app\common\model\GoodsArticlePart::chunk(100, function ($goods) {
            foreach ($goods as $good) {
                $pattern = '/<img[^>]+src=["\']([^"\']+)["\']|<source[^>]+src=["\']([^"\']+)["\']/i';
                preg_match_all($pattern, $good->part_content, $matches);
                $sources = $matches[1];
                foreach ($sources as $source) {
                    $this->handle($source);
                }
            }
        });

        \app\common\model\GoodsCard::chunk(100, function ($goods) {
            foreach ($goods as $good) {
                $pattern = '/<img[^>]+src=["\']([^"\']+)["\']|<source[^>]+src=["\']([^"\']+)["\']/i';
                preg_match_all($pattern, $good->instructions, $matches);
                $sources = $matches[1];
                foreach ($sources as $source) {
                    $this->handle($source);
                }
            }
        });

        \app\common\model\GoodsCategory::chunk(100, function ($goods) {
            foreach ($goods as $good) {
                $this->handle($good->image);
            }
        });

        \app\common\model\GoodsResource::chunk(100, function ($goods) {
            foreach ($goods as $good) {
                $pattern = '/<img[^>]+src=["\']([^"\']+)["\']|<source[^>]+src=["\']([^"\']+)["\']/i';
                preg_match_all($pattern, $good->download_content, $matches);
                $sources = $matches[1];
                foreach ($sources as $source) {
                    $this->handle($source);
                }
            }
        });

        \app\common\model\User::chunk(100, function ($users) {
            foreach ($users as $user) {
                $this->handle($user->avatar);
            }
        });

        \app\common\model\UserAuth::chunk(100, function ($users) {
            foreach ($users as $user) {
                foreach ($user->scene_images as $image) {
                    $this->handle($image);
                }
            }
        });

        $this->success('success', $this->array);
    }

    private function handle($url) {
        if ($url == "") {
            return false;
        }

        $parse_url = parse_url($url);
        if (!isset($parse_url['scheme'])) {
            return false;
        }
        if (!isset($parse_url['host'])) {
            return false;
        }
        $url = ($parse_url['scheme'] ?? '') . "://" . ($parse_url['host'] ?? '');

        // 跳过云存储域名
        $host1 = strtolower(sysconf('storage.qcloud')['domain'] ?? '');
        $host2 = strtolower(sysconf('storage.qiniu')['domain'] ?? '');
        $host3 = strtolower(sysconf('storage.aliyun')['domain'] ?? '');

        if (in_array($url, [$host1, $host2, $host3])) {
            return false;
        }

        if (!in_array($url, array_column($this->array, 'host'))) {
            $this->array[] = [
                'host' => $url,
                'count' => 1,
            ];
        } else {
            $index = array_search($url, array_column($this->array, 'host'));
            if ($index !== false) {
                $this->array[$index]['count'] += 1;
            }
        }

        return true;
    }

    public function startReplace() {
        $host = $this->request->post("host/s", '');
        $target = $this->request->post("target/a", []);

        if ($host == "") {
            $this->error('域名不能为空');
        }

        \app\common\model\Cash::chunk(100, function ($cashs)use ($host, $target) {
            foreach ($cashs as $cash) {
                $collect_info = $cash->collect_info;
                if ($cash->collect_type == 1) {
                    // 支付宝
                    if (($collect_info->alipay_qrcode ?? '') != '') {
                        $url = $this->parse($collect_info->alipay_qrcode);
                        if (in_array($url, $target)) {
                            $collect_info->alipay_qrcode = str_replace($url, $host, $collect_info->alipay_qrcode);
                            $cash->collect_info = $collect_info;
                            $cash->save();
                        }
                    }
                } elseif ($cash->collect_type == 2) {
                    // 微信
                    if (($collect_info->weixin_qrcode ?? '') != '') {
                        $url = $this->parse($collect_info->weixin_qrcode);
                        if (in_array($url, $target)) {
                            $collect_info->weixin_qrcode = str_replace($url, $host, $collect_info->weixin_qrcode);
                            $cash->collect_info = $collect_info;
                            $cash->save();
                        }
                    }
                }
            }
        });

        \app\common\model\UserCollect::chunk(100, function ($collects) use ($host, $target) {
            foreach ($collects as $collect) {
                $collect_info = $collect->collect_info;
                if ($collect->collect_type == 1) {
                    // 支付宝
                    if (($collect_info->alipay_qrcode ?? '') != '') {
                        $url = $this->parse($collect_info->alipay_qrcode);
                        if (in_array($url, $target)) {
                            $collect_info->alipay_qrcode = str_replace($url, $host, $collect_info->alipay_qrcode);
                            $collect->collect_info = $collect_info;
                            $collect->save();
                        }
                    }
                } elseif ($collect->collect_type == 2) {
                    // 微信
                    if (($collect_info->weixin_qrcode ?? '') != '') {
                        $url = $this->parse($collect_info->weixin_qrcode);
                        if (in_array($url, $target)) {
                            $collect_info->weixin_qrcode = str_replace($url, $host, $collect_info->weixin_qrcode);
                            $collect->collect_info = $collect_info;
                            $collect->save();
                        }
                    }
                }
            }
        });

        sysconf('website.logo', str_replace($this->parse(sysconf('website.logo')), $host, sysconf('website.logo')));
        sysconf('website.favicon', str_replace($this->parse(sysconf('website.favicon')), $host, sysconf('website.favicon')));
        sysconf('kefu.qrcode', str_replace($this->parse(sysconf('kefu.qrcode')), $host, sysconf('kefu.qrcode')));

        \app\common\model\Article::chunk(100, function ($articles)use ($host, $target) {
            foreach ($articles as $article) {
                $pattern = '/<img[^>]+src=["\']([^"\']+)["\']|<source[^>]+src=["\']([^"\']+)["\']/i';
                preg_match_all($pattern, $article->content, $matches);
                $sources = $matches[1];
                foreach ($sources as $source) {
                    $url = $this->parse($source);
                    if (in_array($url, $target)) {
                        $article->content = str_replace($url, $host, $article->content);
                        $article->save();
                    }
                }
            }
        });

        \app\common\model\Complaint::chunk(100, function ($complaints)use ($host, $target) {
            foreach ($complaints as $complaint) {
                $images = (array) $complaint->images;
                foreach ($images as &$image) {
                    $url = $this->parse($image);
                    if (in_array($url, $target)) {
                        $image = str_replace($url, $host, $image);
                    }
                }
                $complaint->images = $images;
                $complaint->save();
            }
        });

        \app\common\model\ComplaintMessage::chunk(100, function ($messages)use ($host, $target) {
            foreach ($messages as $message) {
                if ($message->content_type == 1) {
                    $url = $this->parse($message->content);
                    if (in_array($url, $target)) {
                        $message->content = str_replace($url, $host, $message->content);
                        $message->save();
                    }
                }
            }
        });

        \app\common\model\Goods::chunk(100, function ($goods) use ($host, $target) {
            foreach ($goods as $good) {
                $pattern = '/<img[^>]+src=["\']([^"\']+)["\']|<source[^>]+src=["\']([^"\']+)["\']/i';
                preg_match_all($pattern, $good->description, $matches);
                $sources = $matches[1];
                foreach ($sources as $source) {
                    $url = $this->parse($source);
                    if (in_array($url, $target)) {
                        $good->description = str_replace($url, $host, $good->description);
                        $good->save();
                    }
                }


                $url = $this->parse($good->image);
                if (in_array($url, $target)) {
                    $good->image = str_replace($url, $host, $good->image);
                    $good->save();
                }
            }
        });

        \app\common\model\GoodsArticle::chunk(100, function ($goods) use ($host, $target) {
            foreach ($goods as $good) {
                $pattern = '/<img[^>]+src=["\']([^"\']+)["\']|<source[^>]+src=["\']([^"\']+)["\']/i';
                preg_match_all($pattern, $good->paid_content, $matches);
                $sources = $matches[1];
                foreach ($sources as $source) {
                    $url = $this->parse($source);
                    if (in_array($url, $target)) {
                        $good->paid_content = str_replace($url, $host, $good->paid_content);
                        $good->save();
                    }
                }
            }
        });

        \app\common\model\GoodsArticlePart::chunk(100, function ($goods) use ($host, $target) {
            foreach ($goods as $good) {
                $pattern = '/<img[^>]+src=["\']([^"\']+)["\']|<source[^>]+src=["\']([^"\']+)["\']/i';
                preg_match_all($pattern, $good->part_content, $matches);
                $sources = $matches[1];
                foreach ($sources as $source) {
                    $url = $this->parse($source);
                    if (in_array($url, $target)) {
                        $good->part_content = str_replace($url, $host, $good->part_content);
                        $good->save();
                    }
                }
            }
        });

        \app\common\model\GoodsCard::chunk(100, function ($goods) use ($host, $target) {
            foreach ($goods as $good) {
                $pattern = '/<img[^>]+src=["\']([^"\']+)["\']|<source[^>]+src=["\']([^"\']+)["\']/i';
                preg_match_all($pattern, $good->instructions, $matches);
                $sources = $matches[1];
                foreach ($sources as $source) {
                    $url = $this->parse($source);
                    if (in_array($url, $target)) {
                        $good->instructions = str_replace($url, $host, $good->instructions);
                        $good->save();
                    }
                }
            }
        });

        \app\common\model\GoodsCategory::chunk(100, function ($goods) use ($host, $target) {
            foreach ($goods as $good) {
                $url = $this->parse($good->image);
                if (in_array($url, $target)) {
                    $good->image = str_replace($url, $host, $good->image);
                    $good->save();
                }
            }
        });

        \app\common\model\GoodsResource::chunk(100, function ($goods)use ($host, $target) {
            foreach ($goods as $good) {
                $pattern = '/<img[^>]+src=["\']([^"\']+)["\']|<source[^>]+src=["\']([^"\']+)["\']/i';
                preg_match_all($pattern, $good->download_content, $matches);
                $sources = $matches[1];
                foreach ($sources as $source) {
                    $url = $this->parse($source);
                    if (in_array($url, $target)) {
                        $good->download_content = str_replace($url, $host, $good->download_content);
                        $good->save();
                    }
                }
            }
        });

        \app\common\model\User::chunk(100, function ($users) use ($host, $target) {
            foreach ($users as $user) {
                $url = $this->parse($user->avatar);
                if (in_array($url, $target)) {
                    $user->avatar = str_replace($url, $host, $user->avatar);
                    $user->save();
                }
            }
        });

        \app\common\model\UserAuth::chunk(100, function ($users) use ($host, $target) {
            foreach ($users as $user) {
                $images = (array) $user->scene_images;
                foreach ($images as &$image) {
                    $url = $this->parse($image);
                    if (in_array($url, $target)) {
                        $image = str_replace($url, $host, $image);
                    }
                }
                $user->scene_images = $images;
                $user->save();
            }
        });

        $this->success('success');
    }

    private function parse($src) {
        if ($src == "") {
            return "";
        }
        $parse_url = parse_url($src);
        $url = ($parse_url['scheme'] ?? "") . "://" . ($parse_url['host'] ?? "");
        return $url;
    }
}
