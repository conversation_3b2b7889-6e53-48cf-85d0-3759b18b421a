<?php

namespace plugin\Autoban;

use think\facade\Db;

class Hook
{
    public function handle()
    {
        try {
            // 检查投诉率封禁
            if (intval(plugconf("Autoban.complaint_status") ?? 0) === 1) {
                $this->checkAndBanHighComplaintUsers();
            }

            // 检查无流水封禁
            if (intval(plugconf("Autoban.order_status") ?? 0) === 1) {
                $this->checkAndBanNoOrderUsers();
            }

            // 检查未实名用户封禁
            if (intval(plugconf("Autoban.unauth_status") ?? 0) === 1) {
                $this->checkAndBanUnauthUsers();
            }

            // 检查并删除超时封禁用户
            if (intval(plugconf("Autoban.timeout_status") ?? 0) === 1) {
                $this->checkAndDeleteTimeoutUsers();
            }

            // 添加场景检查
            if (intval(plugconf("Autoban.scene_status") ?? 0) === 1) {
                $this->checkAndBanSceneUsers();
            }

            // 记录执行日志
            error_log("Autoban Hook executed at: " . date('Y-m-d H:i:s'));
        } catch (\Exception $e) {
            error_log("Autoban Hook error: " . $e->getMessage());
        }
    }

    private function isInWhitelist($userId)
    {
        try {
            $whitelist = plugconf("Autoban.whitelist") ? json_decode(plugconf("Autoban.whitelist"), true) : [];
            if (empty($whitelist)) return false;
            
            foreach ($whitelist as $item) {
                if ($item['user_id'] == $userId && $item['expire_time'] > time()) {
                    error_log("Autoban: 用户 ID {$userId} 在白名单中，跳过操作");
                    return true;
                }
            }
            return false;
        } catch (\Exception $e) {
            error_log("Autoban: 检查白名单异常: " . $e->getMessage());
            return false;
        }
    }

    private function checkAndBanHighComplaintUsers()
    {
        try {
            $type = plugconf("Autoban.complaint_type") ?? 'rate';
            
            // 获取默认封禁时间
            $expireTime = intval(plugconf("Autoban.default_ban_expire_time") ?? 4102415999);
            
            // 如果设置了投诉率特定的封禁时间，则使用投诉率封禁时间
            $complaintExpireTime = intval(plugconf("Autoban.complaint_expire_time") ?? 0);
            if ($complaintExpireTime > 0) {
                $expireTime = $complaintExpireTime;
            }
            
            // 构建基础查询
            $query = Db::name('user')
                ->alias('u')
                ->leftJoin('complaint c', 'u.id = c.user_id')
                ->where('u.rules', 'not like', '%CloseUser%')
                ->group('u.id');

            if ($type === 'rate') {
                // 按投诉率查询
                $threshold = floatval(plugconf("Autoban.complaint_rate_threshold") ?? 50);
                $minOrders = intval(plugconf("Autoban.complaint_min_orders") ?? 5);
                $banContent = plugconf("Autoban.complaint_rate_content") ?? '因投诉率超过{threshold}%，系统自动封禁';
                
                $users = $query->leftJoin('order o', 'u.id = o.user_id AND o.status = 1')
                    ->field([
                        'u.id',
                        'u.username',
                        'COUNT(DISTINCT c.id) as complaint_count',
                        'COUNT(DISTINCT o.id) as total_orders',
                        'ROUND((COUNT(DISTINCT c.id) / COUNT(DISTINCT o.id) * 100), 2) as complaint_rate'
                    ])
                    ->having('total_orders >= ' . $minOrders)
                    ->having('complaint_rate >= ' . $threshold)
                    ->select()
                    ->toArray();

                // 记录详细日志
                foreach ($users as $user) {
                    error_log("Autoban: 用户 {$user['username']} 订单数 {$user['total_orders']}，投诉率为 {$user['complaint_rate']}%，超过阈值 {$threshold}%");
                }

                // 替换内容占位符
                $banContent = str_replace('{threshold}', $threshold, $banContent);
            } else {
                // 按投诉数量查询
                $threshold = intval(plugconf("Autoban.complaint_count_threshold") ?? 5);
                $banContent = plugconf("Autoban.complaint_count_content") ?? '因投诉次数超过{threshold}次，系统自动封禁';
                
                $users = $query->field([
                    'u.id',
                    'u.username',
                    'COUNT(DISTINCT c.id) as complaint_count'
                ])
                ->having('complaint_count >= ' . $threshold)
                ->select()
                ->toArray();

                // 记录详细日志
                foreach ($users as $user) {
                    error_log("Autoban: 用户 {$user['username']} 投诉次数为 {$user['complaint_count']}次，超过阈值 {$threshold}次");
                }

                // 替换内容占位符
                $banContent = str_replace('{threshold}', $threshold, $banContent);
            }

            if (empty($users)) {
                error_log("Autoban: 没有需要封禁的高投诉用户");
                return;
            }

            // 在执行封禁前过滤白名单用户
            $toBanUsers = array_filter($users, function($userId) {
                return !$this->isInWhitelist($userId);
            });

            if (!empty($toBanUsers)) {
                $this->banUsers($toBanUsers, $type === 'rate' ? "投诉率超标" : "投诉次数超标", $banContent, time(), $expireTime);
                error_log("Autoban: 已封禁高投诉用户数量: " . count($toBanUsers));
            }

        } catch (\Exception $e) {
            error_log("Autoban: 处理高投诉用户失败: " . $e->getMessage());
        }
    }

    public function checkAndBanNoOrderUsers()
    {
        try {
            // 获取配置
            $banTypes = explode(',', plugconf("Autoban.order_ban_types") ?? '');
            if (empty($banTypes)) {
                error_log("Autoban: 未设置封禁类型");
                return;
            }

            $days = intval(plugconf("Autoban.order_days") ?? 30);
            $startTime = plugconf("Autoban.order_start_time");
            $endTime = plugconf("Autoban.order_end_time");
            $banContent = plugconf("Autoban.order_ban_content") ?? '因{days}天内无有效订单，系统自动封禁';
            
            // 获取默认封禁时间
            $expireTime = intval(plugconf("Autoban.default_ban_expire_time") ?? 4102415999);
            
            // 如果设置了无流水特定的封禁时间，则使用无流水封禁时间
            $orderExpireTime = intval(plugconf("Autoban.order_expire_time") ?? 0);
            if ($orderExpireTime > 0) {
                $expireTime = $orderExpireTime;
            }
            
            // 修改：从配置中读取排除新用户天数
            $excludeDays = intval(plugconf("Autoban.exclude_new_user_days") ?? 7);
            $excludeTime = time() - ($excludeDays * 86400);

            // 确定时间范围
            if (!empty($startTime) && !empty($endTime)) {
                $startTimestamp = strtotime($startTime . ' 00:00:00');
                $endTimestamp = strtotime($endTime . ' 23:59:59');
                
                // 验证时间范围
                if ($startTimestamp === false || $endTimestamp === false) {
                    error_log("Autoban: 无效的时间格式 - startTime: {$startTime}, endTime: {$endTime}");
                    return;
                }
                
                if ($startTimestamp > $endTimestamp) {
                    error_log("Autoban: 开始时间不能大于结束时间");
                    return;
                }
                
                $timeDesc = date('Y-m-d', $startTimestamp) . ' 至 ' . date('Y-m-d', $endTimestamp);
                // 使用新的占位符
                $banContent = str_replace(['{days}天内', '{days}', '{time_range}'], $timeDesc, $banContent);
            } else {
                if ($days <= 0 || $days > 365) {
                    error_log("Autoban: 无效的天数设置: {$days}");
                    return;
                }
                
                $endTimestamp = time();
                $startTimestamp = $endTimestamp - ($days * 86400);
                $banContent = str_replace(['{days}天内', '{days}', '{time_range}'], $days, $banContent);
            }

            error_log("Autoban: 检查时间范围 " . date('Y-m-d H:i:s', $startTimestamp) . " 至 " . date('Y-m-d H:i:s', $endTimestamp));

            $currentTime = time();
            $totalBanned = 0;

            // 处理完全无订单的用户
            if (in_array('no_order', $banTypes)) {
                $noOrderUsers = Db::name('user')
                    ->alias('u')
                    ->where('u.rules', 'not like', '%CloseUser%')
                    ->where('u.create_time', '<=', $startTimestamp)
                    ->where('u.create_time', '<', $excludeTime)
                    ->whereNotExists(function ($query) use ($startTimestamp, $endTimestamp) {
                        $query->name('order')
                            ->where('user_id = u.id')
                            ->whereBetween('create_time', [$startTimestamp, $endTimestamp]);
                    })
                    ->column('u.id');

                // 在执行封禁前过滤白名单用户
                $noOrderUsers = array_filter($noOrderUsers, function($userId) {
                    return !$this->isInWhitelist($userId);
                });

                if (!empty($noOrderUsers)) {
                    $this->banUsers($noOrderUsers, '完全无订单', $banContent, $currentTime, $expireTime);
                    error_log("Autoban: 已封禁无订单用户数量: " . count($noOrderUsers));
                }

                $totalBanned += count($noOrderUsers);
            }

            // 处理有订单但无成功订单的用户
            if (in_array('no_success', $banTypes)) {
                $noSuccessUsers = Db::name('user')
                    ->alias('u')
                    ->where('u.rules', 'not like', '%CloseUser%')
                    ->where('u.create_time', '<=', $startTimestamp)
                    ->where('u.create_time', '<', $excludeTime)
                    ->whereExists(function ($query) use ($startTimestamp, $endTimestamp) {
                        $query->name('order')
                            ->where('user_id = u.id')
                            ->whereBetween('create_time', [$startTimestamp, $endTimestamp]);
                    })
                    ->whereNotExists(function ($query) use ($startTimestamp, $endTimestamp) {
                        $query->name('order')
                            ->where('user_id = u.id')
                            ->where('status', 1)
                            ->whereBetween('create_time', [$startTimestamp, $endTimestamp]);
                    })
                    ->column('u.id');

                // 同样在执行封禁前过滤白名单用户
                $noSuccessUsers = array_filter($noSuccessUsers, function($userId) {
                    return !$this->isInWhitelist($userId);
                });

                if (!empty($noSuccessUsers)) {
                    $this->banUsers($noSuccessUsers, '无成功订单', $banContent, $currentTime, $expireTime);
                    error_log("Autoban: 已封禁无成功订单用户数量: " . count($noSuccessUsers));
                }

                $totalBanned += count($noSuccessUsers);
            }

            error_log("Autoban: 本次封禁完成，总共封禁 {$totalBanned} 个用户");

        } catch (\Exception $e) {
            error_log("Autoban: 处理无流水用户失败: " . $e->getMessage());
        }
    }

    protected function banUsers($userIds, $reason, $banContent, $currentTime, $expireTime = 4102415999)
    {
        if (empty($userIds)) {
            error_log("Autoban: 没有需要封禁的用户");
            return;
        }

        $bannedCount = 0;
        foreach ($userIds as $userId) {
            try {
                // 检查是否已被封禁
                $existingBan = Db::name('user_risk')
                    ->where('user_id', $userId)
                    ->where('risk_type', 2)  // 使用风险类型 2
                    ->find();

                if (!$existingBan) {
                    // 添加封禁记录到 user_risk 表
                    Db::name('user_risk')->insert([
                        'user_id' => $userId,
                        'risk_type' => 2,     // 风险类型 2
                        'user_read' => 0,     // 用户未读
                        'content' => $banContent . "（{$reason}）",
                        'create_time' => $currentTime,
                        'expire_time' => $expireTime  // 添加封禁到期时间
                    ]);

                    // 更新用户状态为封禁
                    Db::name('user')
                        ->where('id', $userId)
                        ->update([
                            'rules' => json_encode(['CloseUser']),
                            'update_time' => $currentTime
                        ]);

                    $bannedCount++;
                }
            } catch (\Exception $e) {
                error_log("Autoban: 封禁用户 {$userId} 失败: " . $e->getMessage());
            }
        }

        error_log("Autoban: {$reason}封禁完成，成功封禁 {$bannedCount} 个用户");
    }

    public function manualBanNoOrderUsers()
    {
        try {
            // 获取配置
            $banContent = plugconf("Autoban.order_ban_content") ?? '因无订单，系统自动封禁';
            $currentTime = time();
            
            // 获取默认封禁时间
            $expireTime = intval(plugconf("Autoban.default_ban_expire_time") ?? 4102415999);

            // 记录SQL查询
            error_log("开始查询无订单用户...");
            
            // 查找无订单用户
            $query = Db::name('user')
                ->alias('u')
                ->where('u.rules', 'not like', '%CloseUser%')
                ->whereNotExists(function ($query) {
                    $query->name('order')
                        ->where('user_id = u.id');
                });
            
            // 记录生成的SQL
            error_log("生成的SQL: " . $query->fetchSql(true)->select());
            
            $noOrderUsers = $query->column('u.id');

            error_log("查询到的用户数量: " . count($noOrderUsers));

            if (empty($noOrderUsers)) {
                return ['code' => 1, 'count' => 0, 'message' => '没有需要封禁的用户'];
            }

            try {
                // 执行封禁
                $this->banUsers($noOrderUsers, '手动封禁-无订单', $banContent, $currentTime, $expireTime);
                
                // 添加详细日志
                error_log("手动封禁执行结果：用户IDs=" . json_encode($noOrderUsers));

                return ['code' => 1, 'count' => count($noOrderUsers), 'message' => '封禁成功'];
            } catch (\Exception $e) {
                error_log("封禁过程中出错: " . $e->getMessage());
                throw $e;
            }
        } catch (\Exception $e) {
            // 添加详细错误日志
            $errorMessage = sprintf(
                "手动封禁失败：\n消息：%s\n文件：%s\n行号：%d\n堆栈：\n%s",
                $e->getMessage(),
                $e->getFile(),
                $e->getLine(),
                $e->getTraceAsString()
            );
            error_log($errorMessage);
            return ['code' => 0, 'message' => $e->getMessage()];
        }
    }

    // 添加自动删除超时用户的方法
    private function checkAndDeleteTimeoutUsers()
    {
        try {
            $days = intval(plugconf("Autoban.timeout_days") ?? 30);
            $timeLimit = time() - ($days * 86400);
            
            // 获取批量处理相关配置
            $batchProcessing = intval(plugconf("Autoban.timeout_batch_processing") ?? 0);
            $batchSize = intval(plugconf("Autoban.timeout_batch_size") ?? 100);
            
            // 验证批量处理参数
            if ($batchProcessing && ($batchSize < 5 || $batchSize > 500)) {
                error_log("Autoban: 配置错误 - 每批处理数量必须在5-500之间，当前值: {$batchSize}");
                $batchProcessing = 0; // 禁用批量处理
            }

            // 查找超时的封禁用户
            $timeoutUsers = Db::name('user_risk')
                ->alias('ur')
                ->join('user u', 'ur.user_id = u.id')
                ->where('ur.risk_type', 2)  // 确保是封禁记录
                ->where('ur.create_time', '<', $timeLimit)
                ->field(['u.id', 'u.username', 'ur.user_id'])
                ->select()
                ->toArray();

            if (empty($timeoutUsers)) {
                error_log("Autoban: 没有需要删除的超时用户");
                return;
            }

            // 过滤白名单用户
            $timeoutUsers = array_filter($timeoutUsers, function($user) {
                return !$this->isInWhitelist($user['user_id']);
            });

            if (empty($timeoutUsers)) {
                error_log("Autoban: 没有需要删除的超时用户（已排除白名单用户）");
                return;
            }

            $userIds = array_column($timeoutUsers, 'user_id');
            $totalCount = count($userIds);
            
            error_log("Autoban: 开始处理 {$totalCount} 个超时封禁用户");
            
            // 是否需要批量处理
            if ($batchProcessing && $totalCount > $batchSize) {
                // 创建批次任务
                $batches = array_chunk($userIds, $batchSize);
                $batchCount = count($batches);
                $totalDeleted = 0;
                $deletedCounts = [];
                
                error_log("Autoban: 批量处理 {$totalCount} 个超时用户，共 {$batchCount} 个批次，每批 {$batchSize} 个用户");
                
                foreach ($batches as $index => $batchUserIds) {
                    try {
                        Db::startTrans();
                        
                        // 删除与用户相关的所有数据
                        $batchDeletedCounts = $this->deleteUserRelatedData($batchUserIds);
                        
                        Db::commit();
                        
                        // 合并统计数据
                        foreach ($batchDeletedCounts as $table => $count) {
                            if (!isset($deletedCounts[$table])) {
                                $deletedCounts[$table] = 0;
                            }
                            $deletedCounts[$table] += $count;
                        }
                        
                        $batchSize = count($batchUserIds);
                        $totalDeleted += $batchSize;
                        $progressPercent = round(($index + 1) / $batchCount * 100, 2);
                        
                        error_log("Autoban: 已完成第 " . ($index + 1) . " 批，总进度 {$progressPercent}%");
                        
                        // 休眠一小段时间，避免服务器过载
                        usleep(200000); // 休眠0.2秒
                    } catch (\Exception $e) {
                        Db::rollback();
                        error_log("Autoban: 批量删除第 " . ($index + 1) . " 批用户失败: " . $e->getMessage());
                        // 继续处理下一批
                    }
                }
                
                // 记录删除总结果
                $deletedTablesInfo = [];
                foreach ($deletedCounts as $table => $count) {
                    if ($count > 0) {
                        $deletedTablesInfo[] = "{$table}表: {$count}条";
                    }
                }
                $deletedSummary = implode(', ', $deletedTablesInfo);
                
                error_log("Autoban: 批量删除完成，总共成功删除 {$totalDeleted} 个用户，删除数据: {$deletedSummary}");
            } else {
                try {
                    Db::startTrans();
                    
                    // 删除与用户相关的所有数据
                    $deletedCounts = $this->deleteUserRelatedData($userIds);
                    
                    Db::commit();
                    
                    // 记录操作日志
                    $usernames = implode(',', array_column($timeoutUsers, 'username'));
                    error_log("Autoban: 自动删除超时封禁用户成功：用户名={$usernames}, 删除用户数=" . count($userIds));
                    
                } catch (\Exception $e) {
                    Db::rollback();
                    error_log("Autoban: 自动删除超时封禁用户事务失败：" . $e->getMessage() . "\n" . $e->getTraceAsString());
                    throw $e;
                }
            }
        } catch (\Exception $e) {
            error_log("Autoban: 删除超时用户失败: " . $e->getMessage());
        }
    }

    // 修改为公共方法以便在 Api 中调用
    public function deleteUserRelatedData($userIds)
    {
        if (empty($userIds)) {
            return [];
        }
        
        // 记录删除的数据量
        $deletedCounts = [];
        
        try {
            // 1. 获取当前数据库中的所有表
            $tables = Db::query('SHOW TABLES');
            $prefix = config('database.connections.mysql.prefix');
            $tableList = [];
            
            // 提取表名
            foreach ($tables as $table) {
                $tableName = current($table);
                // 去掉前缀以获得实际表名
                $realTableName = str_replace($prefix, '', $tableName);
                $tableList[] = $realTableName;
            }
            
            error_log("Autoban: 数据库共有 " . count($tableList) . " 张表");
            
            // 2. 遍历所有表，查找包含 user_id 字段的表
            foreach ($tableList as $table) {
                try {
                    $fields = Db::name($table)->getTableFields();
                    
                    // 检查表是否包含 user_id 字段
                    if (in_array('user_id', $fields)) {
                        // 删除该表中与指定用户关联的记录
                        $count = Db::name($table)
                            ->whereIn('user_id', $userIds)
                            ->delete();
                        
                        $deletedCounts[$table] = $count;
                        error_log("Autoban: 从表 {$table} 中删除了 {$count} 条记录");
                    }
                    
                    // 特殊处理 user 表，因为它可能使用 id 而不是 user_id
                    if ($table === 'user' && in_array('id', $fields)) {
                        $count = Db::name($table)
                            ->whereIn('id', $userIds)
                            ->delete();
                        
                        $deletedCounts[$table] = $count;
                        error_log("Autoban: 从表 {$table} 中删除了 {$count} 条用户记录");
                    }
                } catch (\Exception $e) {
                    error_log("Autoban: 处理表 {$table} 失败: " . $e->getMessage());
                    // 继续处理下一个表
                    continue;
                }
            }
            
            error_log("Autoban: 删除用户关联数据完成: " . json_encode($deletedCounts, JSON_UNESCAPED_UNICODE));
            return $deletedCounts;
            
        } catch (\Exception $e) {
            error_log("Autoban: 删除用户关联数据失败: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return $deletedCounts;
        }
    }

    // 添加未实名用户检测方法
    private function checkAndBanUnauthUsers()
    {
        try {
            // 获取未实名封禁提示内容和天数
            $banContent = plugconf("Autoban.unauth_ban_content") ?? '您的账号未完成实名认证，已被系统自动封禁。';
            $days = intval(plugconf("Autoban.unauth_days") ?? 7); // 确保读取正确的天数配置
            $timeLimit = time() - ($days * 86400);
            
            // 获取默认封禁时间
            $expireTime = intval(plugconf("Autoban.default_ban_expire_time") ?? 4102415999);
            
            // 如果设置了未实名特定的封禁时间，则使用未实名封禁时间
            $unauthExpireTime = intval(plugconf("Autoban.unauth_expire_time") ?? 0);
            if ($unauthExpireTime > 0) {
                $expireTime = $unauthExpireTime;
            }
            
            // 记录配置参数，便于调试
            error_log("Autoban: 未实名检查 - 天数={$days}, 封禁时长={$expireTime}");
            
            // 查找未实名且超过指定天数无交易的用户
            $unauthUsers = Db::name('user')
                ->alias('u')
                ->where('u.rules', 'not like', '%CloseUser%')
                ->where('u.create_time', '<', $timeLimit)
                ->where(function ($query) {
                    $query->whereNotExists(function ($query) {
                        $query->name('user_auth')
                            ->whereColumn('user_id', 'u.id')
                            ->where('params', 'not null');
                    });
                })
                ->where(function ($query) use ($timeLimit) {
                    $query->whereNotExists(function ($query) use ($timeLimit) {
                        $query->name('order')
                            ->whereColumn('user_id', 'u.id')
                            ->where('create_time', '>', $timeLimit);
                    });
                })
                ->field(['u.id', 'u.username'])
                ->select()
                ->toArray();

            if (empty($unauthUsers)) {
                error_log("Autoban: 没有发现符合条件的未实名用户");
                return;
            }

            // 过滤白名单用户
            $unauthUsers = array_filter($unauthUsers, function($user) {
                return !$this->isInWhitelist($user['id']);
            });

            if (empty($unauthUsers)) {
                error_log("Autoban: 没有需要处理的未实名用户（已排除白名单用户）");
                return;
            }

            // 执行封禁
            $userIds = array_column($unauthUsers, 'id');
            $this->banUsers($userIds, "未实名且{$days}天无交易", $banContent, time(), $expireTime);
            error_log("Autoban: 已封禁未实名无交易用户数量: " . count($userIds));

        } catch (\Exception $e) {
            error_log("Autoban: 处理未实名用户失败: " . $e->getMessage() . "\n" . $e->getTraceAsString());
        }
    }

    // 修改场景检查方法
    private function checkAndBanSceneUsers()
    {
        try {
            // 检查插件配置中的场景状态
            $sceneStatus = intval(plugconf("Autoban.scene_status") ?? 0);
            if ($sceneStatus != 1) {
                error_log("Autoban: 场景检查 - 功能未启用");
                return;
            }

            // 获取配置
            $banStatus = intval(plugconf("Autoban.scene_ban_status") ?? 0);
            $banMinutes = intval(plugconf("Autoban.scene_ban_minutes") ?? 30);  // 获取封禁时间
            $deleteStatus = intval(plugconf("Autoban.scene_delete_status") ?? 0);
            $deleteMinutes = intval(plugconf("Autoban.scene_delete_minutes") ?? 30);
            
            // 如果总开关关闭，强制关闭封禁和删除功能
            if ($sceneStatus == 0) {
                $banStatus = 0;
                $deleteStatus = 0;
            }
            
            // 如果封禁和删除都未开启，则退出
            if (!$banStatus && !$deleteStatus) {
                error_log("Autoban: 场景检查 - 封禁和删除功能未开启");
                return;
            }

            $sceneContent = plugconf("Autoban.scene_content");
            $sceneWebsite = plugconf("Autoban.scene_website");
            $banContent = plugconf("Autoban.scene_ban_content") ?? '您的账号因场景内容违规，已被系统自动封禁。';

            // 获取所有用户ID
            $allUserIds = Db::name('user')
                ->where('rules', 'not like', '%CloseUser%')
                ->column('id');

            // 获取已认证的用户ID
            $authUserIds = Db::name('user_auth')
                ->where('params', 'not null')
                ->column('user_id');

            // 找出未认证的用户ID
            $unauthUserIds = array_diff($allUserIds, $authUserIds);

            if (empty($unauthUserIds)) {
                return;
            }

            // 检查删除开关状态 - 只针对未认证用户
            if ($deleteStatus === 1) {
                // 执行删除逻辑
                $deleteTime = time() - ($deleteMinutes * 86400); // 将分钟改为天,86400为一天的秒数
                $timeoutUsers = Db::name('user')
                    ->whereIn('id', $unauthUserIds)
                    ->where('create_time', '<', $deleteTime)
                    ->field(['id', 'username', 'create_time'])
                    ->select()
                    ->toArray();

                if (!empty($timeoutUsers)) {
                    // 过滤白名单用户
                    $timeoutUsers = array_filter($timeoutUsers, function($user) {
                        return !$this->isInWhitelist($user['id']);
                    });

                    if (!empty($timeoutUsers)) {
                        $deleteIds = array_column($timeoutUsers, 'id');
                        
                        try {
                            Db::startTrans();
                            
                            // 删除用户相关数据
                            $this->deleteUserRelatedData($deleteIds);
                            // 删除用户记录
                            $userDeleted = Db::name('user')
                                ->whereIn('id', $deleteIds)
                                ->delete();
                            
                            Db::commit();
                            
                            // 记录删除日志
                            $usernames = implode(',', array_column($timeoutUsers, 'username'));
                            error_log("Autoban: 自动删除超时未认证用户成功：用户名={$usernames}, 删除用户数={$userDeleted}");
                            
                        } catch (\Exception $e) {
                            Db::rollback();
                            error_log("Autoban: 删除超时未认证用户失败：" . $e->getMessage());
                        }
                    }
                }
            } else if ($banStatus === 1) {
                $banTime = time() - ($banMinutes * 86400); // 将分钟改为天
                $timeoutUsers = Db::name('user')
                    ->whereIn('id', $unauthUserIds)
                    ->where('create_time', '<', $banTime)
                    ->field(['id', 'username', 'create_time'])
                    ->select()
                    ->toArray();

                if (!empty($timeoutUsers)) {
                    // 过滤白名单用户
                    $timeoutUsers = array_filter($timeoutUsers, function($user) {
                        return !$this->isInWhitelist($user['id']);
                    });

                    if (!empty($timeoutUsers)) {
                        $banIds = array_column($timeoutUsers, 'id');
                        
                        // 获取默认封禁时间
                        $expireTime = intval(plugconf("Autoban.default_ban_expire_time") ?? 4102415999);
                        
                        // 如果设置了场景特定的封禁时间，则使用场景封禁时间
                        $sceneBanExpireTime = intval(plugconf("Autoban.scene_ban_expire_time") ?? 0);
                        if ($sceneBanExpireTime > 0) {
                            $expireTime = $sceneBanExpireTime;
                        }
                        
                        $this->banUsers($banIds, "超时未认证", $banContent, time(), $expireTime);
                        
                        // 记录封禁日志
                        $usernames = implode(',', array_column($timeoutUsers, 'username'));
                        error_log("Autoban: 自动封禁超时未认证用户成功：用户名={$usernames}, 封禁用户数=" . count($banIds));
                    }
                }
            } else {
                error_log("Autoban: 场景检查 - 封禁和删除功能未开启，跳过操作");
            }

            // 继续执行原有的场景检查逻辑
            if (empty($sceneContent) && empty($sceneWebsite)) {
                error_log("Autoban: 场景检查 - 未配置任何匹配规则");
                return;
            }

            $matchedUsers = [];
            
            // 检查场景内容
            if (!empty($sceneContent)) {
                $contentPatterns = explode('|', $sceneContent);
                foreach ($contentPatterns as $pattern) {
                    if (empty($pattern)) continue;
                    
                    $users = Db::name('user_auth')
                        ->alias('ua')
                        ->join('user u', 'ua.user_id = u.id')
                        ->where('u.rules', 'not like', '%CloseUser%')
                        ->where('ua.scene_content', 'like', "%{$pattern}%")
                        ->field(['u.id', 'u.username', 'ua.scene_content'])
                        ->select()
                        ->toArray();
                    
                    foreach ($users as $user) {
                        if (!isset($matchedUsers[$user['id']])) {
                            $matchedUsers[$user['id']] = [
                                'username' => $user['username'],
                                'reason' => "场景内容包含：{$pattern}",
                                'content' => $user['scene_content']
                            ];
                        }
                    }
                }
            }

            // 检查场景网站
            if (!empty($sceneWebsite)) {
                $websitePatterns = explode('|', $sceneWebsite);
                foreach ($websitePatterns as $pattern) {
                    if (empty($pattern)) continue;
                    
                    $users = Db::name('user_auth')
                        ->alias('ua')
                        ->join('user u', 'ua.user_id = u.id')
                        ->where('u.rules', 'not like', '%CloseUser%')
                        ->where('ua.scene_website', 'like', "%{$pattern}%")
                        ->field(['u.id', 'u.username', 'ua.scene_website'])
                        ->select()
                        ->toArray();
                    
                    foreach ($users as $user) {
                        if (!isset($matchedUsers[$user['id']])) {
                            $matchedUsers[$user['id']] = [
                                'username' => $user['username'],
                                'reason' => "场景网站包含：{$pattern}",
                                'content' => $user['scene_website']
                            ];
                        }
                    }
                }
            }

            if (empty($matchedUsers)) {
                error_log("Autoban: 场景检查 - 未发现违规场景用户");
                return;
            }

            // 过滤白名单用户
            $matchedUsers = array_filter($matchedUsers, function($user, $userId) {
                return !$this->isInWhitelist($userId);
            }, ARRAY_FILTER_USE_BOTH);

            if (empty($matchedUsers)) {
                error_log("Autoban: 场景检查 - 所有匹配用户都在白名单中");
                return;
            }

            // 执行封禁
            $bannedCount = 0;
            foreach ($matchedUsers as $userId => $user) {
                try {
                    // 检查是否已被封禁
                    $existingBan = Db::name('user_risk')
                        ->where('user_id', $userId)
                        ->where('risk_type', 2)
                        ->find();

                    if (!$existingBan) {
                        // 获取默认封禁时间
                        $expireTime = intval(plugconf("Autoban.default_ban_expire_time") ?? 4102415999);
                        
                        // 如果设置了场景特定的封禁时间，则使用场景封禁时间
                        $sceneBanExpireTime = intval(plugconf("Autoban.scene_ban_expire_time") ?? 0);
                        if ($sceneBanExpireTime > 0) {
                            $expireTime = $sceneBanExpireTime;
                        }
                        
                        // 添加封禁记录到 user_risk 表
                        Db::name('user_risk')->insert([
                            'user_id' => $userId,
                            'risk_type' => 2,
                            'user_read' => 0,
                            'content' => $banContent, // 直接使用匹配到的内容
                            'create_time' => time(),
                            'expire_time' => $expireTime // 添加封禁到期时间
                        ]);

                        // 更新用户状态
                        Db::name('user')
                            ->where('id', $userId)
                            ->update([
                                'rules' => json_encode(['CloseUser']),
                                'update_time' => time()
                            ]);

                        $bannedCount++;
                        error_log("Autoban: 场景检查 - 已封禁用户 {$user['username']} - {$user['reason']}");
                    }
                } catch (\Exception $e) {
                    error_log("Autoban: 封禁场景违规用户 {$userId} 失败: " . $e->getMessage());
                }
            }

            error_log("Autoban: 场景检查完成，共封禁 {$bannedCount} 个用户");

        } catch (\Exception $e) {
            error_log("Autoban: 处理场景检查失败: " . $e->getMessage() . "\n" . $e->getTraceAsString());
        }
    }
} 