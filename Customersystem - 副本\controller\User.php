<?php
namespace plugin\Customersystem\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;
use think\facade\Log;
use think\facade\Session;
use plugin\Customersystem\Hook;

class User extends BasePlugin
{
    protected $scene = [
        'user',
    ];
    protected $noNeedLogin = ['captcha'];

    /**
     * 初始化方法
     */
    protected function initialize()
    {
        parent::initialize();
    }

    /**
     * 商家消息接收面板主页
     */
    public function index()
    {
        try {
            // 获取网站配置
            $logo = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'logo')
                ->value('value');
            
            // 处理logo URL
            if (preg_match('/^https?:\/\/(oss\.|cos\.|[-\w]+\.qiniucdn\.|[-\w]+)/i', $logo)) {
                $logo = $logo;
            } else if (strpos($logo, 'uploads/') === 0) {
                $logo = request()->domain() . '/' . $logo;
            } else {
                $logo = request()->domain() . '/' . ltrim($logo, '/');
            }
            
            $siteName = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'app_name')
                ->value('value');
            
            $favicon = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'favicon')
                ->value('value');
            
            // 处理 favicon URL
            if (preg_match('/^https?:\/\/(oss\.|cos\.|[-\w]+\.qiniucdn\.|[-\w]+)/i', $favicon)) {
                $favicon = $favicon;
            } else if (strpos($favicon, 'uploads/') === 0) {
                $favicon = request()->domain() . '/' . $favicon;
            } else {
                $favicon = request()->domain() . '/' . ltrim($favicon, '/');
            }
            
            // 获取网站配置信息
            $icpNumber = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'icp_number')
                ->value('value');
            
            $gaNumber = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'ga_number')
                ->value('value');
            
            $icpCert = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'icp_cert')
                ->value('value');

            // 渲染视图，商家视图
            View::assign([
                'logo' => $logo,
                'siteName' => $siteName,
                'favicon' => $favicon,
                'icpNumber' => $icpNumber,
                'gaNumber' => $gaNumber,
                'icpCert' => $icpCert,
                'userInfo' => $this->user,
            ]);
            
            return View::fetch('user/index');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取会话列表 (商家)
     * 只返回属于当前商家的会话
     */
    public function getSessionList()
    {
        try {
            // 确保用户已登录
            if (empty($this->user)) {
                Log::error('获取会话列表失败: 用户未登录');
                return $this->error('请先登录');
            }
            
            $page = $this->request->param('page/d', 1);
            $limit = $this->request->param('limit/d', 20);
            $status = $this->request->param('status', '');
            
            Log::info('获取会话列表请求参数: 用户ID=' . $this->user->id . ', 页码=' . $page . ', 每页数量=' . $limit);
            
            // 根据plugin_chat_tables.sql中定义的表结构
            Log::info('使用表中实际存在的字段查询会话列表');
            $sessionList = [];
            
            // 只获取当前商家ID对应的会话
            // 修改查询逻辑，确保只有商家ID匹配的会话才会被获取
            
            // 使用contact_id查询 - 确保只查询与当前商家相关的会话
            $list1 = Db::name('plugin_chat_sessions')
                ->where('contact_id', $this->user->id)
                ->where('merchant_id', $this->user->id) // 只查询明确指定给当前商家的会话
                ->order('last_time desc')
                ->select()
                ->toArray();
                
            if (!empty($list1)) {
                Log::info('通过contact_id找到' . count($list1) . '条会话数据');
                $sessionList = array_merge($sessionList, $list1);
            }
            
            // 使用staff_id查询 - 确保只查询与当前商家相关的会话
            $list2 = Db::name('plugin_chat_sessions')
                ->where('staff_id', $this->user->id)
                ->where('merchant_id', $this->user->id) // 只查询明确指定给当前商家的会话
                ->order('last_time desc')
                ->select()
                ->toArray();
                
            if (!empty($list2)) {
                Log::info('通过staff_id找到' . count($list2) . '条会话数据');
                $sessionList = array_merge($sessionList, $list2);
            }
            
            // 如果没有找到任何会话，尝试查找与该用户相关的消息，并确保商家ID匹配
            if (empty($sessionList)) {
                Log::info('尝试通过消息表关联查询 - 用户ID: ' . $this->user->id);
                $list3 = Db::name('plugin_chat_sessions')
                    ->where('id', 'in', function($query) {
                        $query->name('plugin_chat_messages')
                              ->where('sender_id', $this->user->id)
                              ->field('session_id')
                              ->distinct(true);
                    })
                    ->where('merchant_id', $this->user->id) // 只查询明确指定给当前商家的会话
                    ->order('last_time desc')
                    ->select()
                    ->toArray();
                    
                if (!empty($list3)) {
                    Log::info('通过消息表关联找到' . count($list3) . '条会话数据');
                    $sessionList = array_merge($sessionList, $list3);
                }
            }
            
            // 新增处理：查找source为'customer'的会话（客户选择"联系客服"）
            // 只有当用户是商家时才能看到这些会话，且必须是明确指定给该商家的
            $isShop = Db::name('user')
                ->where('id', $this->user->id)
                ->where('custom_status', 1)  // custom_status=1表示正常商户
                ->count() > 0;
                
            if ($isShop) {
                Log::info('商家身份，尝试查找客服会话 - 用户ID: ' . $this->user->id);
                // 修改查询条件，只查询当前商家相关的会话
                $customerSessions = Db::name('plugin_chat_sessions')
                    ->where(function ($query) {
                        // 只查询明确指定了当前商家ID的会话
                        $query->where('merchant_id', $this->user->id);
                    })
                    ->where(function ($query) {
                        // 方式1：查询source为customer且指定了当前商家ID的会话
                        $query->where('source', 'customer')
                              ->whereOr('source', 'merchant');
                    })
                    ->order('last_time desc')
                    ->select()
                    ->toArray();
                    
                if (!empty($customerSessions)) {
                    Log::info('找到' . count($customerSessions) . '条相关商家会话数据');
                    $sessionList = array_merge($sessionList, $customerSessions);
                }
                
                // 查询当前用户作为参与者的会话（被邀请的商家）
                Log::info('查询当前用户作为参与者的会话 - 用户ID: ' . $this->user->id);
                $participantSessions = Db::name('plugin_chat_sessions')
                    ->alias('s')
                    ->join('plugin_chat_session_participants p', 'p.session_id = s.id')
                    ->where('p.user_id', $this->user->id)
                    ->where('p.user_type', 'supplier')
                    ->where('p.is_active', 1)
                    ->field('s.*')
                    ->order('s.last_time desc')
                    ->select()
                    ->toArray();
                    
                if (!empty($participantSessions)) {
                    Log::info('找到' . count($participantSessions) . '条当前用户参与的会话数据');
                    $sessionList = array_merge($sessionList, $participantSessions);
                }
            }
            
            // 防止重复会话，根据ID去重
            $uniqueSessions = [];
            foreach ($sessionList as $session) {
                $uniqueSessions[$session['id']] = $session;
            }
            $sessionList = array_values($uniqueSessions);
            
            // 过滤状态
            if ($status !== '' && !empty($sessionList)) {
                $sessionList = array_filter($sessionList, function($session) use ($status) {
                    return $session['status'] === $status;
                });
                $sessionList = array_values($sessionList); // 重新索引数组
            }
            
            // 按最后活动时间排序
            usort($sessionList, function($a, $b) {
                return $b['last_time'] - $a['last_time'];
            });
            
            // 构建分页数据
            $total = count($sessionList);
            $offset = ($page - 1) * $limit;
            $sessionList = array_slice($sessionList, $offset, $limit);
            
            // 增加会话参与者信息，方便前端显示
            foreach ($sessionList as &$session) {
                // 查询会话的参与者信息
                $participants = Db::name('plugin_chat_session_participants')
                    ->alias('p')
                    ->join('user u', 'p.user_id = u.id', 'LEFT')
                    ->where('p.session_id', $session['id'])
                    ->where('p.is_active', 1)
                    ->field('p.user_id, p.user_type, u.nickname, u.username')
                    ->select()
                    ->toArray();
                
                $participantInfo = [];
                foreach ($participants as $participant) {
                    $participantInfo[] = [
                        'user_id' => $participant['user_id'],
                        'user_type' => $participant['user_type'],
                        'name' => $participant['nickname'] ?: $participant['username'] ?: ('用户' . $participant['user_id'])
                    ];
                }
                
                $session['participants'] = $participantInfo;
            }
            
            Log::info('获取会话列表结果: 总数=' . $total . ', 当前页数据数量=' . count($sessionList));
            
            // 构造分页结构
            $result = [
                'data' => $sessionList,
                'total' => $total,
                'per_page' => $limit,
                'current_page' => $page,
                'last_page' => ceil($total / $limit),
            ];
            
            return $this->success('获取成功', $result);
        } catch (\Exception $e) {
            Log::error('获取会话列表异常: ' . $e->getMessage());
            return $this->error('获取会话列表失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取会话详情 (商家)
     * 只能查看属于当前商家的会话
     */
    public function getSessionDetail()
    {
        try {
            // 确保用户已登录
            if (empty($this->user)) {
                return $this->error('请先登录');
            }
            
            $sessionId = $this->request->param('id/d');
            
            if (empty($sessionId)) {
                return $this->error('会话ID不能为空');
            }
            
            // 查询会话
            $session = Db::name('plugin_chat_sessions')
                ->where('id', $sessionId)
                ->find();
                
            if (empty($session)) {
                return $this->error('会话不存在');
            }
            
            // 获取表结构，确定有哪些字段
            $tableInfo = [];
            try {
                $columns = Db::query("SHOW COLUMNS FROM plugin_chat_sessions");
                $tableInfo = array_column($columns, 'Field');
            } catch (\Exception $e) {
                Log::error('获取表结构失败: ' . $e->getMessage());
            }
            
            // 验证权限 - 检查表中存在的身份字段
            $hasPermission = false;
            
            // 首先检查商家ID是否匹配
            if (in_array('merchant_id', $tableInfo) && isset($session['merchant_id'])) {
                if ($session['merchant_id'] == $this->user->id) {
                    $hasPermission = true;
                    Log::info('用户权限通过merchant_id匹配');
                } else if ($session['merchant_id'] != 0 && $session['merchant_id'] != null) {
                    // 如果有指定merchant_id但不匹配当前用户，则拒绝访问
                    Log::info('用户无权限：merchant_id不匹配 - 会话merchant_id=' . $session['merchant_id'] . ', 用户ID=' . $this->user->id);
                    return $this->error('您无权查看此会话');
                }
            }
            
            // 如果没有通过merchant_id匹配，再检查其他字段
            if (!$hasPermission) {
                // 检查可能的商家ID字段
                $merchantIdFields = ['contact_id', 'staff_id'];
                foreach ($merchantIdFields as $field) {
                    if (in_array($field, $tableInfo) && isset($session[$field]) && $session[$field] == $this->user->id) {
                        $hasPermission = true;
                        Log::info('用户权限通过字段 ' . $field);
                        break;
                    }
                }
            }
            
            // 如果没有找到直接关联，尝试查找消息关联
            if (!$hasPermission) {
                // 查询该用户是否在此会话中发送过消息
                $hasSentMessage = Db::name('plugin_chat_messages')
                    ->where('session_id', $sessionId)
                    ->where('sender_id', $this->user->id)
                    ->count() > 0;
                    
                if ($hasSentMessage) {
                    $hasPermission = true;
                }
            }
            
            // 检查用户是否为会话参与者（被邀请的商家）
            if (!$hasPermission) {
                $isParticipant = Db::name('plugin_chat_session_participants')
                    ->where('session_id', $sessionId)
                    ->where('user_id', $this->user->id)
                    ->where('user_type', 'supplier')
                    ->where('is_active', 1)
                    ->count() > 0;
                    
                if ($isParticipant) {
                    $hasPermission = true;
                    Log::info('用户权限通过会话参与者关系匹配: 用户ID=' . $this->user->id);
                }
            }
            
            // 新增处理：检查用户是否为商家角色，如果是则直接授权
            if (!$hasPermission) {
                // 尝试从用户表检查商家身份
                $isShop = Db::name('user')
                    ->where('id', $this->user->id)
                    ->where('custom_status', 1)  // custom_status=1表示正常商户
                    ->count() > 0;
                    
                if ($isShop) {
                    // 如果是商家，检查会话类型
                    if (isset($session['source']) && $session['source'] == 'customer') {
                        // 如果是"联系客服"类型的会话，商家可以查看
                        $hasPermission = true;
                        Log::info('商家身份授权查看客服会话: 用户ID=' . $this->user->id);
                    } else if (isset($session['merchant_id']) && $session['merchant_id'] == $this->user->id) {
                        // 如果是指定给该商家的会话，商家可以查看
                        $hasPermission = true;
                        Log::info('商家身份授权查看指定商家会话: 用户ID=' . $this->user->id);
                    } else {
                        // 其他情况下通用商家权限
                        $hasPermission = true;
                        Log::info('商家身份授权查看会话: 用户ID=' . $this->user->id);
                    }
                }
            }
            
            if (!$hasPermission) {
                return $this->error('您无权查看此会话');
            }
            
            // 获取联系人信息
            $contact = null;
            
            // 按优先级尝试获取联系人信息
            if (in_array('contact_id', $tableInfo) && isset($session['contact_id']) && $session['contact_id'] > 0) {
                $contact = Db::name('plugin_chat_contacts')
                    ->where('id', $session['contact_id'])
                    ->find();
            } elseif (in_array('customer_id', $tableInfo) && isset($session['customer_id']) && $session['customer_id'] > 0) {
                $contact = Db::name('plugin_chat_contacts')
                    ->where('id', $session['customer_id'])
                    ->find();
            } elseif (in_array('user_id', $tableInfo) && isset($session['user_id']) && $session['user_id'] > 0 && $session['user_id'] != $this->user->id) {
                // 尝试从users表获取
                $contact = Db::name('user')
                    ->where('id', $session['user_id'])
                    ->find();
            }
                
            // 获取消息列表
            $messages = Db::name('plugin_chat_messages')
                ->where('session_id', $sessionId)
                ->order('create_time asc')
                ->select()
                ->toArray();
                
            // 如果有权限查看会话，且会话与当前商家相关，检查是否需要自动发送预设问题
            if ($hasPermission) {
                // 检查是否需要自动发送预设问题
                $customerHasMessages = false;
                $merchantHasMessages = false;
                
                // 检查会话中是否有客户消息
                foreach ($messages as $message) {
                    if ($message['role_type'] === 'customer' || $message['sender_type'] === 'customer') {
                        $customerHasMessages = true;
                    }
                    if (($message['role_type'] === 'merchant' || $message['sender_type'] === 'merchant') && 
                        $message['sender_id'] == $this->user->id && 
                        stripos($message['message'], 'class="preset-questions"') !== false) {
                        $merchantHasMessages = true;
                    }
                }
                
                // 如果有客户消息但没有商家的预设问题消息，自动发送预设问题
                if ($customerHasMessages && !$merchantHasMessages) {
                    $this->checkAndSendPresetQuestions($sessionId);
                    
                    // 重新获取消息列表，包含新发送的预设问题
                    $messages = Db::name('plugin_chat_messages')
                        ->where('session_id', $sessionId)
                        ->order('create_time asc')
                        ->select()
                        ->toArray();
                }
            }
                
            // 更新已读状态
            Db::name('plugin_chat_sessions')
                ->where('id', $sessionId)
                ->update([
                    'unread_count' => 0,
                    'update_time' => time()
                ]);
                
            Db::name('plugin_chat_messages')
                ->where('session_id', $sessionId)
                ->where('is_read', 0)
                ->update([
                    'is_read' => 1,
                    'update_time' => time()
                ]);
                
            // 返回结果
            return $this->success('获取成功', [
                'session' => $session,
                'messages' => $messages,
            ]);
        } catch (\Exception $e) {
            Log::error('获取会话详情失败: ' . $e->getMessage());
            return $this->error('获取会话详情失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取实时消息
     */
    public function getRealtimeMessages()
    {
        try {
            // 确保用户已登录
            if (empty($this->user)) {
                return $this->error('请先登录');
            }
            
            $sessionId = $this->request->param('session_id/d', 0);
            $lastMessageId = $this->request->param('last_message_id/d', 0);
            
            if (empty($sessionId)) {
                return $this->error('会话ID不能为空');
            }
            
            // 检查会话是否存在
            $session = Db::name('plugin_chat_sessions')
                ->where('id', $sessionId)
                ->find();
                
            if (empty($session)) {
                return $this->error('会话不存在');
            }
            
            // 获取表结构，确定有哪些字段
            $tableInfo = [];
            try {
                $columns = Db::query("SHOW COLUMNS FROM plugin_chat_sessions");
                $tableInfo = array_column($columns, 'Field');
                Log::info('会话表字段: ' . json_encode($tableInfo));
            } catch (\Exception $e) {
                Log::error('获取表结构失败: ' . $e->getMessage());
            }
            
            // 验证权限 - 检查表中存在的身份字段
            $hasPermission = false;
            
            // 首先检查商家ID是否匹配
            if (in_array('merchant_id', $tableInfo) && isset($session['merchant_id'])) {
                if ($session['merchant_id'] == $this->user->id) {
                    $hasPermission = true;
                    Log::info('用户权限通过merchant_id匹配');
                } else if ($session['merchant_id'] != 0 && $session['merchant_id'] != null) {
                    // 如果有指定merchant_id但不匹配当前用户，则拒绝访问
                    Log::info('用户无权限：merchant_id不匹配 - 会话merchant_id=' . $session['merchant_id'] . ', 用户ID=' . $this->user->id);
                    return $this->error('您无权查看此会话');
                }
            }
            
            // 如果没有通过merchant_id匹配，再检查其他字段
            if (!$hasPermission) {
                // 检查可能的商家ID字段
                $merchantIdFields = ['contact_id', 'staff_id'];
                foreach ($merchantIdFields as $field) {
                    if (in_array($field, $tableInfo) && isset($session[$field]) && $session[$field] == $this->user->id) {
                        $hasPermission = true;
                        Log::info('用户权限通过字段 ' . $field);
                        break;
                    }
                }
            }
            
            // 如果没有找到直接关联，尝试查找消息关联
            if (!$hasPermission) {
                // 查询该用户是否在此会话中发送过消息
                $hasSentMessage = Db::name('plugin_chat_messages')
                    ->where('session_id', $sessionId)
                    ->where('sender_id', $this->user->id)
                    ->count() > 0;
                    
                if ($hasSentMessage) {
                    $hasPermission = true;
                }
            }
            
            // 检查用户是否为会话参与者（被邀请的商家）
            if (!$hasPermission) {
                $isParticipant = Db::name('plugin_chat_session_participants')
                    ->where('session_id', $sessionId)
                    ->where('user_id', $this->user->id)
                    ->where('user_type', 'supplier')
                    ->where('is_active', 1)
                    ->count() > 0;
                    
                if ($isParticipant) {
                    $hasPermission = true;
                    Log::info('用户权限通过会话参与者关系匹配: 用户ID=' . $this->user->id);
                }
            }
            
            // 新增处理：检查用户是否为商家角色，如果是则直接授权
            if (!$hasPermission) {
                // 尝试从用户表检查商家身份
                $isShop = Db::name('user')
                    ->where('id', $this->user->id)
                    ->where('custom_status', 1)  // custom_status=1表示正常商户
                    ->count() > 0;
                    
                if ($isShop) {
                    // 如果是商家，检查会话类型
                    if (isset($session['source']) && $session['source'] == 'customer') {
                        // 如果是"联系客服"类型的会话，商家可以查看
                        $hasPermission = true;
                        Log::info('商家身份授权获取客服会话实时消息: 用户ID=' . $this->user->id);
                    } else if (isset($session['merchant_id']) && $session['merchant_id'] == $this->user->id) {
                        // 如果是指定给该商家的会话，商家可以查看
                        $hasPermission = true;
                        Log::info('商家身份授权获取指定商家会话实时消息: 用户ID=' . $this->user->id);
                    } else {
                        // 其他情况下通用商家权限
                        $hasPermission = true;
                        Log::info('商家身份授权获取实时消息: 用户ID=' . $this->user->id);
                    }
                }
            }
            
            if (!$hasPermission) {
                return $this->error('您无权查看此会话');
            }
            
            // 获取新消息
            $newMessages = Db::name('plugin_chat_messages')
                ->where('session_id', $sessionId)
                ->where('id', '>', $lastMessageId)
                ->order('create_time asc')
                ->select()
                ->toArray();
                
            // 获取最近被撤回的消息ID列表（包括已经加载的消息中被撤回的）
            $recalledMessages = Db::name('plugin_chat_messages')
                ->where('session_id', $sessionId)
                ->where('is_recalled', 1)
                ->where('id', '<=', $lastMessageId) // 只获取已加载的消息
                ->where('update_time', '>', time() - 300) // 只获取最近5分钟内撤回的消息
                ->column('id');
                
            // 将新消息标记为已读
            if (!empty($newMessages)) {
                $messageIds = array_column($newMessages, 'id');
                
                Db::name('plugin_chat_messages')
                    ->whereIn('id', $messageIds)
                    ->update([
                        'is_read' => 1,
                        'update_time' => time()
                    ]);
                    
                // 重新计算未读消息数（只统计客户发送的消息）
                $unreadCount = Db::name('plugin_chat_messages')
                    ->where('session_id', $sessionId)
                    ->where('is_read', 0)
                    ->where('sender_type', 'customer') // 只统计客户消息
                    ->count();
                    
                // 更新会话的未读消息数
                Db::name('plugin_chat_sessions')
                    ->where('id', $sessionId)
                    ->update([
                        'unread_count' => $unreadCount,
                        'update_time' => time()
                    ]);
            }
                
            return $this->success('获取成功', [
                'messages' => $newMessages,
                'session_status' => $session['status'],
                'is_closed' => $session['status'] === 'closed',
                'recalled_messages' => $recalledMessages  // 添加已撤回消息ID列表
            ]);
        } catch (\Exception $e) {
            Log::error('获取实时消息失败: ' . $e->getMessage());
            return $this->error('获取实时消息失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 发送消息
     */
    public function sendMessage()
    {
        try {
            // 确保用户已登录
            if (empty($this->user)) {
                return $this->error('请先登录');
            }
            
            $sessionId = $this->request->param('session_id/d');
            $content = $this->request->param('content');
            $message = $this->request->param('message'); // 兼容旧版前端
            $type = $this->request->param('type', 'text');
            $messageType = $this->request->param('message_type'); // 兼容旧版前端
            
            // 内容兼容处理
            if (empty($content) && !empty($message)) {
                $content = $message;
            }
            
            // 类型兼容处理
            if (empty($type) && !empty($messageType)) {
                $type = $messageType;
            }
            
            if (empty($sessionId)) {
                return $this->error('会话ID不能为空');
            }
            
            // 检查会话是否存在
            $session = Db::name('plugin_chat_sessions')
                ->where('id', $sessionId)
                ->find();
                
            if (empty($session)) {
                return $this->error('会话不存在');
            }
            
            // 验证权限 - 根据实际表结构检查权限
            $hasPermission = false;
            $senderType = 'customer'; // 默认为客户
            
            // 检查contact_id权限
            if (isset($session['contact_id']) && $session['contact_id'] == $this->user->id) {
                $hasPermission = true;
                $senderType = 'customer';
                Log::info('用户发送消息权限通过contact_id');
            }
            
            // 检查staff_id权限
            if (!$hasPermission && isset($session['staff_id']) && $session['staff_id'] == $this->user->id) {
                $hasPermission = true;
                $senderType = 'staff';
                Log::info('用户发送消息权限通过staff_id');
            }
            
            // 如果没有找到直接关联，尝试查找消息关联
            if (!$hasPermission) {
                // 查询该用户是否在此会话中发送过消息
                $previousMessage = Db::name('plugin_chat_messages')
                    ->where('session_id', $sessionId)
                    ->where('sender_id', $this->user->id)
                    ->order('id desc')
                    ->find();
                    
                if ($previousMessage) {
                    $hasPermission = true;
                    $senderType = $previousMessage['sender_type'];
                    Log::info('用户发送消息权限通过发送消息记录');
                }
            }
            
            // 检查用户是否为会话参与者（被邀请的商家）
            if (!$hasPermission) {
                $isParticipant = Db::name('plugin_chat_session_participants')
                    ->where('session_id', $sessionId)
                    ->where('user_id', $this->user->id)
                    ->where('user_type', 'supplier')
                    ->where('is_active', 1)
                    ->count() > 0;
                    
                if ($isParticipant) {
                    $hasPermission = true;
                    $senderType = 'staff'; // 被邀请的商家以商家身份(staff)发送消息
                    Log::info('用户发送消息权限通过会话参与者关系匹配: 用户ID=' . $this->user->id);
                }
            }
            
            // 新增处理：检查用户是否为商家角色，如果是则直接授权
            if (!$hasPermission) {
                // 尝试从用户表检查商家身份
                $isShop = Db::name('user')
                    ->where('id', $this->user->id)
                    ->where('custom_status', 1)  // custom_status=1表示正常商户
                    ->count() > 0;
                    
                if ($isShop) {
                    // 如果是商家，检查会话类型
                    if (isset($session['source']) && $session['source'] == 'customer') {
                        // 如果是"联系客服"类型的会话，商家可以发送消息
                        $hasPermission = true;
                        $senderType = 'staff'; // 商家回复客服会话，设置为客服类型
                        Log::info('商家身份授权发送客服会话消息: 用户ID=' . $this->user->id);
                    } else if (isset($session['merchant_id']) && $session['merchant_id'] == $this->user->id) {
                        // 如果是指定给该商家的会话，商家可以发送消息
                        $hasPermission = true;
                        $senderType = 'staff'; // 商家回复指定商家会话，设置为客服类型
                        Log::info('商家身份授权发送指定商家会话消息: 用户ID=' . $this->user->id);
                    } else {
                        // 其他情况下通用商家权限
                        $hasPermission = true;
                        $senderType = 'staff'; // 商家发送消息时，设置为客服类型
                        Log::info('商家身份授权发送消息: 用户ID=' . $this->user->id);
                    }
                }
            }
            
            if (!$hasPermission) {
                Log::error('用户无权发送消息: 用户ID=' . $this->user->id . ', 会话ID=' . $sessionId);
                return $this->error('您无权在此会话中发送消息');
            }
            
            // 检查会话是否已关闭
            if ($session['status'] === 'closed') {
                return $this->error('会话已关闭，无法发送消息');
            }
            
            // 验证消息内容
            if ($type === 'text' && empty($content)) {
                return $this->error('消息内容不能为空');
            }
            
            if (in_array($type, ['image', 'file']) && empty($content)) {
                return $this->error('文件链接不能为空');
            }
            
            // 新增处理：检查是否是回复"是"，并且最近有订单查询记录
            if ($type === 'text' && $senderType === 'customer' && in_array(trim($content), ['是', '是的', '对', '对的', '好', '好的', 'yes', 'OK', 'ok', '可以'])) {
                // 商家也可以触发供货商连接
                $this->processSupplierConnectionRequest($sessionId, $this->user->id);
            }
            
            // 获取消息表结构
            $tableColumns = [];
            try {
                $columns = Db::query("SHOW COLUMNS FROM plugin_chat_messages");
                $tableColumns = array_column($columns, 'Field');
                Log::info('消息表字段: ' . json_encode($tableColumns));
            } catch (\Exception $e) {
                Log::error('获取消息表结构失败: ' . $e->getMessage());
            }
            
            // 检查是否为客户发送的纯订单号格式 - 增强对订单号的自动识别
            if ($type === 'text' && $senderType === 'customer') {
                $trimmedContent = trim($content);
                
                // 检查是否使用了"订单号:xxx"格式 - 只识别严格的"订单号:"格式
                if (preg_match('/订单号[:：]?\s*([a-zA-Z0-9-]{4,32})/ui', $trimmedContent, $matches)) {
                    Log::info('检测到客户发送的标准订单号: ' . $matches[1]);
                    // 标记为订单号查询，在响应中心自动处理
                    $session['is_order_query'] = true;
                    $session['query_order_no'] = $matches[1];
                }
                // 不再支持其他格式
            }
            
            // 准备消息数据
            $messageData = [
                'session_id' => $sessionId,
                'sender_type' => $senderType,
                'sender_id' => $this->user->id,
                'role_type' => 'customer', // 默认为客户角色
                'create_time' => time(),
                'update_time' => time(),
                'is_read' => 0,
                'message_type' => $type
            ];
            
            // 处理图片消息
            if ($type === 'image') {
                // 将图片URL包装成HTML格式
                $messageData['message'] = '<div class="message-image-container"><img src="' . $content . '" class="message-image" alt="图片消息" style="display: block;"></div>';
                $messageData['file_url'] = $content; // 同时保存原始URL到file_url字段
            } else {
                // 文本或其他类型消息
                $messageData['message'] = $content;
            }
            
            // 自动检测文本消息是否为图片链接
            if ($type === 'text' && preg_match('/\.(jpg|jpeg|png|gif|bmp|webp)(\?.*)?$/i', $content)) {
                // 将消息类型修改为图片
                $messageData['message_type'] = 'image';
                $messageData['file_url'] = $content; // 保存图片URL到file_url字段
                $messageData['message'] = '<div class="message-image-container"><img src="' . $content . '" class="message-image" alt="图片消息" style="display: block;"></div>';
                Log::info('检测到图片链接，自动设置消息类型为image: ' . $content);
            }
            
            // 根据用户身份设置角色类型
            // 检查用户是否为商家
            $isShop = Db::name('user')
                ->where('id', $this->user->id)
                ->where('custom_status', 1)  // custom_status=1表示正常商户
                ->count() > 0;
                
            // 检查用户是否为会话参与者（被邀请的商家）
            $isParticipant = Db::name('plugin_chat_session_participants')
                ->where('session_id', $sessionId)
                ->where('user_id', $this->user->id)
                ->where('user_type', 'supplier')
                ->where('is_active', 1)
                ->count() > 0;

            // 通过plugin_chat_join_requests表来正确判断用户角色
            $correctRoleType = $this->getUserRoleTypeFromJoinRequests($sessionId, $this->user->id);

            if ($correctRoleType) {
                $messageData['role_type'] = $correctRoleType;
                Log::info('通过join_requests表设置消息发送者角色: ' . $correctRoleType . ', 用户ID=' . $this->user->id);
            } else if ($isShop || $isParticipant) {
                // 如果join_requests表中没有找到，使用原有逻辑
                $messageData['role_type'] = 'merchant';
                Log::info('消息发送者角色设置为商家: 用户ID=' . $this->user->id);
            } else if ($senderType === 'staff') {
                // 如果是客服人员，需要明确设置角色类型为客服
                $messageData['role_type'] = 'staff';
                Log::info('消息发送者角色设置为客服: 用户ID=' . $this->user->id);
            }
            
            // 日志记录准备发送的数据
            Log::info('准备发送消息: ' . json_encode($messageData));
            
            // 插入消息记录
            $messageId = Db::name('plugin_chat_messages')->insertGetId($messageData);
            
            if (!$messageId) {
                return $this->error('发送失败，请重试');
            }
            
            // 更新会话最后消息和时间
            $updateData = [
                'last_message' => $type === 'text' ? $content : '[' . $type . ']',
                'last_time' => time(),
                'update_time' => time()
            ];
            
            // 如果当前用户是联系人（客户），增加未读数
            if ($senderType === 'customer' && isset($session['staff_id']) && $session['staff_id'] > 0) {
                $updateData['unread_count'] = Db::raw('unread_count + 1');
            }
            
            // 如果当前用户是商家/客服，确保设置正确的sender_type
            if ($senderType === 'staff' && isset($session['contact_id']) && $session['contact_id'] > 0) {
                // 确保消息类型正确标记为staff，但前端会根据发送者ID和角色区分显示
                Db::name('plugin_chat_messages')
                    ->where('id', $messageId)
                    ->update(['sender_type' => 'staff']);
                
                // 重置会话未读数
                $updateData['unread_count'] = 0;
            }
            
            Db::name('plugin_chat_sessions')
                ->where('id', $sessionId)
                ->update($updateData);
                
            // 获取完整的消息数据
            $message = Db::name('plugin_chat_messages')
                ->where('id', $messageId)
                ->find();
                
            return $this->success('发送成功', $message);
        } catch (\Exception $e) {
            Log::error('发送消息失败: ' . $e->getMessage());
            return $this->error('发送消息失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 处理用户回复"是"的供货商连接请求
     * 
     * @param int $sessionId 当前会话ID
     * @param int $userId 用户ID
     * @return bool 处理结果
     */
    protected function processSupplierConnectionRequest($sessionId, $userId)
    {
        try {
            // 1. 获取会话中最近的订单查询记录
            $recentOrderQueryTime = time() - 300; // 只查询5分钟内的记录
            
            $recentOrderQuery = Db::name('plugin_chat_messages')
                ->where('session_id', $sessionId)
                ->where('sender_type', 'staff')
                ->where('create_time', '>', $recentOrderQueryTime)
                ->where('message', 'like', '%是否需要联系供货商%')
                ->order('id desc')
                ->find();
                
            if (!$recentOrderQuery) {
                Log::info('未找到最近的供货商询问消息');
                return false;
            }
            
            // 2. 查找最近的订单查询结果，里面包含供货商信息
            $recentOrderResult = Db::name('plugin_chat_messages')
                ->where('session_id', $sessionId)
                ->where('sender_type', 'staff')
                ->where('create_time', '>', $recentOrderQueryTime)
                ->where('message', 'like', '%订单号%')
                ->where('message', 'like', '%商品名称%')
                ->order('id desc')
                ->find();
                
            if (!$recentOrderResult) {
                Log::info('未找到最近的订单查询结果');
                return false;
            }
            
            // 3. 从订单查询会话或缓存中获取供货商信息
            $supplierInfo = null;
            
            // 先检查会话属性中是否有缓存的供货商信息
            $sessionInfo = Db::name('plugin_chat_sessions')
                ->where('id', $sessionId)
                ->find();
                
            if (!empty($sessionInfo) && !empty($sessionInfo['last_query_result'])) {
                try {
                    $queryResult = json_decode($sessionInfo['last_query_result'], true);
                    if (!empty($queryResult) && !empty($queryResult['supplier_info'])) {
                        $supplierInfo = $queryResult['supplier_info'];
                        Log::info('从会话属性中找到供货商信息');
                    }
                } catch (\Exception $e) {
                    Log::error('解析会话属性中的供货商信息失败: ' . $e->getMessage());
                }
            }
            
            // 如果会话属性中没有，尝试从系统缓存中获取
            if (empty($supplierInfo)) {
                // 从查询结果消息中提取商品名称
                if (preg_match('/商品名称[:：]\s*([^\n]+)/u', $recentOrderResult['message'], $matches)) {
                    $goodsName = trim($matches[1]);
                    if (!empty($goodsName)) {
                        $supplierInfo = $this->getSupplierByGoodsName($goodsName);
                        Log::info('通过商品名称查询到供货商信息: ' . json_encode($supplierInfo, JSON_UNESCAPED_UNICODE));
                    }
                }
            }
            
            // 如果仍然没找到供货商信息，退出
            if (empty($supplierInfo) || empty($supplierInfo['user_id'])) {
                Log::info('未找到有效的供货商信息');
                
                // 发送提示消息
                $responseMessage = [
                    'session_id' => $sessionId,
                    'sender_type' => 'staff',
                    'sender_id' => 0, // 系统消息
                    'role_type' => 'merchant',
                    'create_time' => time(),
                    'update_time' => time(),
                    'is_read' => 0,
                    'message' => '抱歉，无法连接到供货商，未找到供货商信息或供货商不在线。',
                    'message_type' => 'text'
                ];
                
                Db::name('plugin_chat_messages')->insert($responseMessage);
                
                // 更新会话
                Db::name('plugin_chat_sessions')
                    ->where('id', $sessionId)
                    ->update([
                        'last_message' => '抱歉，无法连接到供货商，未找到供货商信息或供货商不在线。',
                        'last_time' => time(),
                        'update_time' => time()
                    ]);
                
                return false;
            }
            
            // 4. 创建与供货商的新会话
            $supplierUserId = $supplierInfo['user_id'];
            
            // 检查是否已存在会话
            $existSession = Db::name('plugin_chat_sessions')
                ->where('user_id', $userId)
                ->where('contact_id', $supplierUserId)
                ->whereOr(function ($query) use ($supplierUserId, $userId) {
                    $query->where('user_id', $supplierUserId)
                          ->where('contact_id', $userId);
                })
                ->find();
                
            if ($existSession) {
                // 如果已存在会话，发送提示消息
                $responseMessage = [
                    'session_id' => $sessionId,
                    'sender_type' => 'staff',
                    'sender_id' => 0, // 系统消息
                    'role_type' => 'merchant',
                    'create_time' => time(),
                    'update_time' => time(),
                    'is_read' => 0,
                    'message' => '您已经与该供货商有聊天会话，请在会话列表中查看。会话ID: ' . $existSession['id'],
                    'message_type' => 'text'
                ];
                
                Db::name('plugin_chat_messages')->insert($responseMessage);
                
                // 更新会话
                Db::name('plugin_chat_sessions')
                    ->where('id', $sessionId)
                    ->update([
                        'last_message' => '您已经与该供货商有聊天会话，请在会话列表中查看。',
                        'last_time' => time(),
                        'update_time' => time()
                    ]);
                
                return true;
            }
            
            // 创建新会话
            $supplierName = !empty($supplierInfo['nickname']) ? $supplierInfo['nickname'] : '供货商';
            $sessionData = [
                'user_id' => $userId,
                'contact_id' => $supplierUserId,
                'merchant_id' => $supplierUserId, // 修改为供货商ID，确保供货商也能看到会话
                'title' => '与' . $supplierName . '的商品咨询',
                'source' => 'supplier', // 标记为供货商会话
                'status' => 'open',
                'last_message' => '您好，我想咨询您的商品相关事宜。',
                'last_time' => time(),
                'create_time' => time(),
                'update_time' => time(),
                'source_order' => $cleanTradeNo, // 记录来源订单号
                'source_session' => $sessionId,  // 记录来源会话
            ];
            
            $newSessionId = Db::name('plugin_chat_sessions')->insertGetId($sessionData);
            
            if (!$newSessionId) {
                Log::error('创建供货商会话失败');
                return false;
            }
            
            // 添加第一条消息
            $messageData = [
                'session_id' => $newSessionId,
                'sender_id' => $userId,
                'sender_type' => 'customer',
                'role_type' => 'customer',
                'message' => '您好，我想咨询关于您的商品相关事宜。',
                'message_type' => 'text',
                'is_read' => 0,
                'create_time' => time(),
                'update_time' => time()
            ];
            
            Db::name('plugin_chat_messages')->insert($messageData);
            
            // 添加系统消息，告知供货商有客户咨询
            $supplierMessageData = [
                'session_id' => $newSessionId,
                'sender_id' => 0, // 系统消息
                'sender_type' => 'staff',
                'role_type' => 'merchant',
                'message' => '有客户对您的商品发起了咨询，请及时回复。',
                'message_type' => 'text',
                'is_read' => 0,
                'is_system' => 1,
                'create_time' => time(),
                'update_time' => time()
            ];
            
            Db::name('plugin_chat_messages')->insert($supplierMessageData);
            
            // 5. 在原会话中发送提示消息
            $responseMessage = [
                'session_id' => $sessionId,
                'sender_type' => 'staff',
                'sender_id' => 0, // 系统消息
                'role_type' => 'merchant',
                'create_time' => time(),
                'update_time' => time(),
                'is_read' => 0,
                'message' => '已为您创建与供货商"' . $supplierName . '"的会话，您可以在会话列表中找到并开始咨询。',
                'message_type' => 'text'
            ];
            
            Db::name('plugin_chat_messages')->insert($responseMessage);
            
            // 更新原会话
            Db::name('plugin_chat_sessions')
                ->where('id', $sessionId)
                ->update([
                    'last_message' => '已为您创建与供货商的会话，您可以在会话列表中找到。',
                    'last_time' => time(),
                    'update_time' => time()
                ]);
                
            Log::info('成功创建供货商会话并发送提示消息, 新会话ID=' . $newSessionId);
            
            return true;
        } catch (\Exception $e) {
            Log::error('处理供货商连接请求失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 上传文件 (商家)
     */
    public function upload()
    {
        try {
            // 确保用户已登录
            if (empty($this->user)) {
                return $this->error('请先登录');
            }
            
            $sessionId = $this->request->post('session_id/d');
            $type = $this->request->post('type', 'file'); // image 或 file
            
            if (empty($sessionId)) {
                return $this->error('会话ID不能为空');
            }
            
            // 检查会话
            $session = Db::name('plugin_chat_sessions')
                ->where('id', $sessionId)
                ->find();
                
            if (empty($session)) {
                return $this->error('会话不存在');
            }
            
            // 获取表结构，确定有哪些字段
            $tableInfo = [];
            try {
                $columns = Db::query("SHOW COLUMNS FROM plugin_chat_sessions");
                $tableInfo = array_column($columns, 'Field');
            } catch (\Exception $e) {
                Log::error('获取表结构失败: ' . $e->getMessage());
            }
            
            // 验证权限 - 检查表中存在的身份字段
            $hasPermission = false;
            
            // 检查可能的商家ID字段
            $merchantIdFields = ['contact_id', 'merchant_id', 'staff_id'];
            foreach ($merchantIdFields as $field) {
                if (in_array($field, $tableInfo) && isset($session[$field]) && $session[$field] == $this->user->id) {
                    $hasPermission = true;
                    break;
                }
            }
            
            // 特殊情况处理
            if (!$hasPermission && in_array('user_id', $tableInfo) && isset($session['user_id']) && $session['user_id'] == $this->user->id) {
                if (in_array('is_merchant', $tableInfo) && isset($session['is_merchant']) && $session['is_merchant'] == 1) {
                    $hasPermission = true;
                }
            }
            
            // 如果没有找到直接关联，尝试查找消息关联
            if (!$hasPermission) {
                // 查询该用户是否在此会话中发送过消息
                $hasSentMessage = Db::name('plugin_chat_messages')
                    ->where('session_id', $sessionId)
                    ->where('user_id', $this->user->id)
                    ->count() > 0;
                    
                if ($hasSentMessage) {
                    $hasPermission = true;
                }
            }
            
            // 检查用户是否为会话参与者（被邀请的商家）
            if (!$hasPermission) {
                $isParticipant = Db::name('plugin_chat_session_participants')
                    ->where('session_id', $sessionId)
                    ->where('user_id', $this->user->id)
                    ->where('user_type', 'supplier')
                    ->where('is_active', 1)
                    ->count() > 0;
                    
                if ($isParticipant) {
                    $hasPermission = true;
                    Log::info('用户权限通过会话参与者关系匹配: 用户ID=' . $this->user->id);
                }
            }
            
            // 新增处理：检查用户是否为商家角色，如果是则直接授权
            if (!$hasPermission) {
                // 尝试从用户表检查商家身份
                $isShop = Db::name('user')
                    ->where('id', $this->user->id)
                    ->where('custom_status', 1)  // custom_status=1表示正常商户
                    ->count() > 0;
                    
                if ($isShop) {
                    // 如果是商家，检查会话类型
                    if (isset($session['source']) && $session['source'] == 'customer') {
                        // 如果是"联系客服"类型的会话，商家可以上传文件
                        $hasPermission = true;
                        Log::info('商家身份授权上传文件到客服会话: 用户ID=' . $this->user->id);
                    } else if (isset($session['merchant_id']) && $session['merchant_id'] == $this->user->id) {
                        // 如果是指定给该商家的会话，商家可以上传文件
                        $hasPermission = true;
                        Log::info('商家身份授权上传文件到指定商家会话: 用户ID=' . $this->user->id);
                    } else {
                        // 其他情况下通用商家权限
                        $hasPermission = true;
                        Log::info('商家身份授权上传文件: 用户ID=' . $this->user->id);
                    }
                }
            }
            
            if (!$hasPermission) {
                return $this->error('您无权在此会话中上传文件');
            }
            
            // 检查会话是否已关闭
            if ($session['status'] === 'closed') {
                return $this->error('该会话已关闭，无法上传文件');
            }
            
            // 获取上传文件
            $file = $this->request->file('file');
            
            if (empty($file)) {
                return $this->error('请选择要上传的文件');
            }
            
            // 根据类型设置验证规则
            if ($type === 'image') {
                $validate = validate([
                    'file' => [
                        'fileSize' => 10 * 1024 * 1024, // 10MB
                        'fileExt' => 'jpg,jpeg,png,gif,webp,bmp',
                        'fileMime' => 'image/jpeg,image/png,image/gif,image/webp,image/bmp'
                    ]
                ]);
            } else {
                $validate = validate([
                    'file' => [
                        'fileSize' => 20 * 1024 * 1024, // 20MB
                        'fileExt' => 'jpg,jpeg,png,gif,webp,doc,docx,xls,xlsx,ppt,pptx,pdf,txt,zip,rar'
                    ]
                ]);
            }
            
            // 验证文件
            if (!$validate->check(['file' => $file])) {
                return $this->error($validate->getError());
            }
            
            // 文件存储路径
            $savePath = 'uploads/chat/' . date('Ymd');
            
            try {
                // 上传文件
                $info = \think\facade\Filesystem::disk('public')->putFile($savePath, $file);
                
                if (!$info) {
                    return $this->error('文件上传失败');
                }
                
                // 获取文件URL
                $fileUrl = request()->domain() . '/storage/' . $info;
                
                return $this->success('上传成功', [
                    'url' => $fileUrl,
                    'name' => $file->getOriginalName()
                ]);
            } catch (\Exception $e) {
                Log::error('文件上传失败: ' . $e->getMessage());
                return $this->error('文件上传失败: ' . $e->getMessage());
            }
        } catch (\Exception $e) {
            Log::error('文件上传失败: ' . $e->getMessage());
            return $this->error('文件上传失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 标准响应：成功
     */
    public function success($msg = '', $data = null, $code = 200, $type = null, array $header = [])
    {
        return json([
            'code' => $code,
            'msg'  => $msg,
            'time' => time(),
            'data' => $data,
        ])->header($header);
    }

    /**
     * 标准响应：错误
     */
    public function error($msg = '', $data = null, $code = 0, $type = null, array $header = [])
    {
        return json([
            'code' => $code,
            'msg'  => $msg,
            'time' => time(),
            'data' => $data,
        ])->header($header);
    }

    /**
     * 测试API - 返回测试数据
     */
    public function test()
    {
        // 创建一个测试会话数据
        $session = [
            'id' => 1,
            'title' => '测试会话',
            'contact_id' => $this->user ? $this->user->id : 0,
            'customer_id' => 1,
            'status' => 'open',
            'last_message' => '这是一条测试消息',
            'last_time' => time(),
            'unread_count' => 1,
            'create_time' => time(),
            'update_time' => time()
        ];
        
        // 创建测试消息
        $messages = [
            [
                'id' => 1,
                'session_id' => 1,
                'user_id' => 1,
                'sender_type' => 'customer',
                'message_type' => 'text',
                'message' => '您好，这是客户发送的消息',
                'file_url' => '',
                'file_name' => '',
                'is_read' => 1,
                'is_system' => 0,
                'create_time' => time() - 3600,
                'update_time' => time() - 3600
            ],
            [
                'id' => 2,
                'session_id' => 1,
                'user_id' => $this->user ? $this->user->id : 0,
                'sender_type' => 'staff',
                'message_type' => 'text',
                'message' => '您好，这是系统回复的消息',
                'file_url' => '',
                'file_name' => '',
                'is_read' => 1,
                'is_system' => 0,
                'create_time' => time() - 1800,
                'update_time' => time() - 1800
            ],
            [
                'id' => 3,
                'session_id' => 1,
                'user_id' => 1,
                'sender_type' => 'customer',
                'message_type' => 'text',
                'message' => '这是最新的一条客户消息',
                'file_url' => '',
                'file_name' => '',
                'is_read' => 0,
                'is_system' => 0,
                'create_time' => time() - 600,
                'update_time' => time() - 600
            ]
        ];
        
        Log::info('测试API被调用');
        
        return $this->success('测试成功', [
            'session' => $session,
            'messages' => $messages
        ]);
    }

    /**
     * 测试会话列表
     */
    public function testSessions()
    {
        // 创建几个测试会话
        $sessions = [
            [
                'id' => 1,
                'title' => '测试会话1',
                'contact_id' => $this->user ? $this->user->id : 0,
                'customer_id' => 1,
                'status' => 'open',
                'last_message' => '这是测试会话1的最新消息',
                'last_time' => time(),
                'unread_count' => 2,
                'create_time' => time() - 86400,
                'update_time' => time()
            ],
            [
                'id' => 2,
                'title' => '测试会话2',
                'contact_id' => $this->user ? $this->user->id : 0,
                'customer_id' => 2,
                'status' => 'open',
                'last_message' => '这是测试会话2的最新消息',
                'last_time' => time() - 3600,
                'unread_count' => 0,
                'create_time' => time() - 172800,
                'update_time' => time() - 3600
            ],
            [
                'id' => 3,
                'title' => '测试会话3',
                'contact_id' => $this->user ? $this->user->id : 0,
                'customer_id' => 3,
                'status' => 'closed',
                'last_message' => '这是一个已关闭的测试会话',
                'last_time' => time() - 86400,
                'unread_count' => 0,
                'create_time' => time() - 259200,
                'update_time' => time() - 86400
            ]
        ];
        
        Log::info('测试会话列表API被调用');
        
        // 返回测试数据
        $data = [
            'data' => $sessions,
            'total' => count($sessions),
            'per_page' => 20,
            'current_page' => 1,
            'last_page' => 1
        ];
        
        return $this->success('测试会话列表', $data);
    }

    /**
     * 数据库诊断方法
     */
    public function diagnose()
    {
        try {
            $result = [];
            
            // 检查用户信息
            $result['user'] = !empty($this->user) ? [
                'id' => $this->user->id,
                'username' => $this->user->username
            ] : null;
            
            // 检查数据表是否存在
            $tables = [
                'plugin_chat_sessions',
                'plugin_chat_messages',
                'plugin_chat_contacts'
            ];
            
            $tableStatus = [];
            foreach ($tables as $table) {
                $exists = Db::query("SHOW TABLES LIKE '{$table}'");
                $tableStatus[$table] = [
                    'exists' => !empty($exists),
                ];
                
                if (!empty($exists)) {
                    // 获取表结构
                    $columns = Db::query("SHOW COLUMNS FROM {$table}");
                    $tableStatus[$table]['columns'] = array_column($columns, 'Field');
                    
                    // 获取记录数
                    $count = Db::table($table)->count();
                    $tableStatus[$table]['record_count'] = $count;
                    
                    // 如果有记录，获取一条示例数据
                    if ($count > 0) {
                        $sample = Db::table($table)->limit(1)->select()->toArray();
                        $tableStatus[$table]['sample'] = $sample;
                    }
                }
            }
            
            $result['tables'] = $tableStatus;
            
            // 尝试获取一条会话记录
            $session = Db::name('plugin_chat_sessions')
                ->where('contact_id', $this->user ? $this->user->id : 0)
                ->find();
                
            $result['sample_session'] = $session;
            
            if ($session) {
                // 尝试获取这个会话的消息
                $messages = Db::name('plugin_chat_messages')
                    ->where('session_id', $session['id'])
                    ->limit(5)
                    ->select()
                    ->toArray();
                    
                $result['sample_messages'] = $messages;
            }
            
            return $this->success('诊断信息', $result);
        } catch (\Exception $e) {
            return $this->error('诊断失败: ' . $e->getMessage());
        }
    }

    /**
     * 撤回消息
     */
    public function revokeMessage()
    {
        try {
            // 确保用户已登录
            if (empty($this->user)) {
                Log::error('撤回消息失败: 用户未登录');
                return $this->error('请先登录');
            }
            
            $messageId = $this->request->param('message_id/d');
            
            if (empty($messageId)) {
                Log::error('撤回消息失败: 消息ID为空');
                return $this->error('消息ID不能为空');
            }
            
            // 记录用户信息和消息ID
            Log::info('用户尝试撤回消息: 用户ID=' . $this->user->id . ', 消息ID=' . $messageId);
            
            // 检查plugin_chat_messages表结构
            try {
                // 注意：根据SQL文件，字段名应该是is_recalled而不是is_revoked
                $columns = Db::query("SHOW COLUMNS FROM plugin_chat_messages LIKE 'is_recalled'");
                if (empty($columns)) {
                    // 添加is_recalled字段（如果不存在）
                    Db::execute("ALTER TABLE plugin_chat_messages ADD COLUMN is_recalled tinyint(1) DEFAULT 0 COMMENT '是否已撤回 0否 1是'");
                    Log::info('为plugin_chat_messages表添加is_recalled字段');
                }
            } catch (\Exception $e) {
                Log::error('检查或添加字段失败: ' . $e->getMessage());
                // 继续执行，使用update时如果字段不存在会忽略该字段
            }
            
            // 查询消息
            $message = Db::name('plugin_chat_messages')
                ->where('id', $messageId)
                ->find();
                
            if (empty($message)) {
                Log::error('撤回消息失败: 消息不存在, 消息ID=' . $messageId);
                return $this->error('消息不存在');
            }
            
            // 记录消息信息
            Log::info('待撤回消息信息: ' . json_encode($message, JSON_UNESCAPED_UNICODE));
            
            $currentTime = time();
            $messageTime = $message['create_time'];
            
            // 检查消息发送时间，超过2分钟不允许撤回
            if (($currentTime - $messageTime) > 120) {
                Log::error('消息发送超过2分钟: 消息时间=' . date('Y-m-d H:i:s', $messageTime) . ', 当前时间=' . date('Y-m-d H:i:s', $currentTime));
                return $this->error('消息发送超过2分钟，无法撤回');
            }
            
            // 检查是否是图片或文件消息，如果是则清除相关URL
            $messageType = isset($message['message_type']) ? $message['message_type'] : '';
            $typeField = isset($message['type']) ? $message['type'] : '';
            
            // 撤回消息（将消息内容替换为撤回提示，清除所有文件URL）
            $updateData = [
                'message' => '[该消息已被撤回]',
                'is_recalled' => 1, // 使用is_recalled字段而不是is_revoked
                'update_time' => time()
            ];
            
            if ($messageType === 'image' || $typeField === 'image' || 
                $messageType === 'file' || $typeField === 'file') {
                // 获取表结构，确保只更新存在的字段
                try {
                    $columns = Db::query("SHOW COLUMNS FROM plugin_chat_messages");
                    $tableFields = array_column($columns, 'Field');
                    
                    // 只有字段存在时才添加到更新数据中
                    if (in_array('file_url', $tableFields)) {
                        $updateData['file_url'] = '';
                    }
                    
                    if (in_array('content', $tableFields)) {
                        $updateData['content'] = '';
                    }
                    
                    // 保留消息类型，但清除内容
                    $logType = $messageType ?: $typeField;
                    Log::info('撤回图片或文件消息，清除URL: 消息ID=' . $messageId . ', 类型=' . $logType . ', 可用字段=' . implode(',', $tableFields));
                } catch (\Exception $e) {
                    Log::error('获取表结构失败，仅更新消息内容: ' . $e->getMessage());
                    // 如果获取表结构失败，至少还是更新message字段
                }
            }
            
            $result = Db::name('plugin_chat_messages')
                ->where('id', $messageId)
                ->update($updateData);
                
            if (!$result) {
                Log::error('消息撤回失败: 数据库更新失败, 消息ID=' . $messageId);
                return $this->error('撤回失败，请重试');
            }
            
            Log::info('消息撤回成功: 消息ID=' . $messageId);
            
            // 更新会话的最后消息，如果最后消息是被撤回的消息
            try {
                $session = Db::name('plugin_chat_sessions')
                    ->where('id', $message['session_id'])
                    ->find();
                    
                if ($session && isset($session['last_message']) && isset($message['message']) && $session['last_message'] == $message['message']) {
                    Db::name('plugin_chat_sessions')
                        ->where('id', $message['session_id'])
                        ->update([
                            'last_message' => '[该消息已被撤回]',
                            'update_time' => time()
                        ]);
                    Log::info('更新会话最后消息成功: 会话ID=' . $message['session_id']);
                }
            } catch (\Exception $e) {
                Log::error('更新会话最后消息失败: ' . $e->getMessage());
                // 撤回消息成功，更新会话最后消息失败不影响结果
            }
            
            // 返回更新后的消息数据
            try {
                $updatedMessage = Db::name('plugin_chat_messages')
                    ->where('id', $messageId)
                    ->find();
                    
                return $this->success('消息已撤回', $updatedMessage);
            } catch (\Exception $e) {
                Log::error('获取更新后消息失败: ' . $e->getMessage());
                // 返回基本成功信息
                return $this->success('消息已撤回');
            }
        } catch (\Exception $e) {
            Log::error('撤回消息失败: ' . $e->getMessage());
            return $this->error('撤回消息失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取商家设置
     */
    public function getSettings()
    {
        try {
            // 确保用户已登录
            if (empty($this->user)) {
                return $this->error('请先登录');
            }
            
            // 查询商家的设置记录
            $settings = Db::name('plugin_chat_settings')
                ->where('user_id', $this->user->id)
                ->find();
                
            if (empty($settings)) {
                // 如果没有设置记录，返回默认设置
                Log::info('用户没有设置记录，返回默认设置: 用户ID=' . $this->user->id);
                return $this->success('获取成功', [
                    'allowSupplier' => false,
                    'enableOrderQuery' => false,
                    'orderQueryPrompt' => '请输入您的订单号，我们将为您查询订单信息',
                    'orderQueryTemplate' => "订单号：{订单号}\n商品名称：{商品名称}\n商品数量：{商品数量}\n订单金额：{订单金额}\n下单时间：{下单时间}\n订单状态：{订单状态}",
                    'enableAutoReply' => false,
                    'welcomeMessage' => '您好，欢迎咨询，请问有什么可以帮到您？'
                ]);
            }
            
            // 返回存储的设置
            // 解析JSON字段并转换为前端需要的格式
            $settingsData = [];
            if (isset($settings['settings']) && !empty($settings['settings'])) {
                $settingsData = json_decode($settings['settings'], true) ?: [];
                
                // 确保布尔值正确转换
                $booleanFields = ['allowSupplier', 'enableOrderQuery', 'enableAutoReply'];
                foreach ($booleanFields as $field) {
                    if (isset($settingsData[$field])) {
                        // 将存储的各种布尔值表示形式转换为实际的布尔值
                        $settingsData[$field] = filter_var($settingsData[$field], FILTER_VALIDATE_BOOLEAN);
                    } else {
                        // 如果字段不存在，设置默认值为false
                        $settingsData[$field] = false;
                    }
                }
                
                // 确保其他字段存在默认值
                if (!isset($settingsData['orderQueryPrompt'])) {
                    $settingsData['orderQueryPrompt'] = '请输入您的订单号，我们将为您查询订单信息';
                }
                if (!isset($settingsData['orderQueryTemplate'])) {
                    $settingsData['orderQueryTemplate'] = "订单号：{订单号}\n商品名称：{商品名称}\n商品数量：{商品数量}\n订单金额：{订单金额}\n下单时间：{下单时间}\n订单状态：{订单状态}";
                }
                if (!isset($settingsData['welcomeMessage'])) {
                    $settingsData['welcomeMessage'] = '您好，欢迎咨询，请问有什么可以帮到您？';
                }
            }
            
            Log::info('获取用户设置成功: 用户ID=' . $this->user->id . ', 设置数据=' . json_encode($settingsData, JSON_UNESCAPED_UNICODE));
            return $this->success('获取成功', $settingsData);
        } catch (\Exception $e) {
            Log::error('获取商家设置失败: ' . $e->getMessage());
            return $this->error('获取商家设置失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 保存商家设置
     */
    public function saveSettings()
    {
        try {
            // 确保用户已登录
            if (empty($this->user)) {
                return $this->error('请先登录');
            }
            
            // 获取设置数据
            $settingsData = $this->request->post();
            
            // 如果没有设置数据，返回错误
            if (empty($settingsData)) {
                return $this->error('设置数据不能为空');
            }
            
            // 检查数据格式并转换布尔值
            $validKeys = [
                'allowSupplier', 'enableOrderQuery', 'orderQueryPrompt', 
                'orderQueryTemplate', 'enableAutoReply', 'welcomeMessage'
            ];
            
            $filteredData = [];
            foreach ($validKeys as $key) {
                if (isset($settingsData[$key])) {
                    // 处理布尔值设置项
                    if (in_array($key, ['allowSupplier', 'enableOrderQuery', 'enableAutoReply'])) {
                        // 转换JavaScript的true/false到PHP的布尔值
                        if ($settingsData[$key] === true || $settingsData[$key] === 'true' || $settingsData[$key] === 1 || $settingsData[$key] === '1') {
                            $filteredData[$key] = true;
                        } else {
                            $filteredData[$key] = false;
                        }
                    } else {
                        $filteredData[$key] = $settingsData[$key];
                    }
                }
            }
            
            // 记录保存的设置内容
            Log::info('保存商家设置: 用户ID=' . $this->user->id . ', 设置数据=' . json_encode($filteredData, JSON_UNESCAPED_UNICODE));
            
            // 查询是否已有设置记录
            $existingSettings = Db::name('plugin_chat_settings')
                ->where('user_id', $this->user->id)
                ->find();
                
            // 准备保存数据
            $saveData = [
                'user_id' => $this->user->id,
                'settings' => json_encode($filteredData, JSON_UNESCAPED_UNICODE),
                'update_time' => time()
            ];
            
            // 检查表是否存在，不存在则创建
            try {
                $tableExists = Db::query("SHOW TABLES LIKE 'plugin_chat_settings'");
                if (empty($tableExists)) {
                    // 创建设置表
                    Db::execute("CREATE TABLE `plugin_chat_settings` (
                        `id` int(11) NOT NULL AUTO_INCREMENT,
                        `user_id` int(11) NOT NULL COMMENT '商家ID',
                        `settings` text COLLATE utf8mb4_unicode_ci COMMENT '设置JSON',
                        `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
                        `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
                        PRIMARY KEY (`id`),
                        UNIQUE KEY `user_id` (`user_id`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商家消息设置表';");
                    
                    Log::info('创建plugin_chat_settings表成功');
                }
            } catch (\Exception $e) {
                Log::error('检查或创建settings表失败: ' . $e->getMessage());
                return $this->error('保存设置失败，数据表错误: ' . $e->getMessage());
            }
            
            if ($existingSettings) {
                // 更新现有记录
                Db::name('plugin_chat_settings')
                    ->where('id', $existingSettings['id'])
                    ->update($saveData);
                
                Log::info('更新设置成功: 记录ID=' . $existingSettings['id']);
            } else {
                // 创建新记录
                $saveData['create_time'] = time();
                $id = Db::name('plugin_chat_settings')->insertGetId($saveData);
                Log::info('创建设置成功: 新记录ID=' . $id);
            }
            
            return $this->success('保存成功');
        } catch (\Exception $e) {
            Log::error('保存商家设置失败: ' . $e->getMessage());
            return $this->error('保存商家设置失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 查询订单信息
     */
    public function queryOrder()
    {
        try {
            // 确保用户已登录
            if (empty($this->user)) {
                return $this->error('请先登录');
            }
            
            // 获取订单号
            $tradeNo = $this->request->post('trade_no');
            if (empty($tradeNo)) {
                return $this->error('订单号不能为空');
            }
            
            // 添加调试日志
            Log::info('收到订单查询请求原始输入: [' . $tradeNo . ']');
            
            // 清理订单号 - 使用更严格的处理
            $cleanTradeNo = null;
            
            // 调试每个字符的编码
            $charInfo = [];
            for ($i = 0; $i < mb_strlen($tradeNo, 'UTF-8'); $i++) {
                $char = mb_substr($tradeNo, $i, 1, 'UTF-8');
                $charInfo[] = $char . ' (' . ord($char) . ')';
            }
            Log::info('输入字符详情: ' . implode(', ', $charInfo));
            
            // 尝试不同的匹配方式
            $patterns = [
                '/订单号[:：]?\s*([a-zA-Z0-9-]{4,32})/ui',   // 标准格式，冒号可选
                '/^\s*([a-zA-Z0-9-]{4,32})\s*$/ui'          // 直接输入订单号
            ];
            
            foreach ($patterns as $index => $pattern) {
                if (preg_match($pattern, $tradeNo, $matches) && !empty($matches[1])) {
                    $cleanTradeNo = $matches[1];
                    Log::info('使用模式' . $index . '成功匹配: ' . $cleanTradeNo . ' (匹配数组: ' . json_encode($matches) . ')');
                    break;
                }
            }
            
            if ($cleanTradeNo === null) {
                // 如果所有匹配都失败，尝试更简单的方法
                if (preg_match('/([a-zA-Z0-9-]{4,32})/ui', $tradeNo, $matches)) {
                    $cleanTradeNo = $matches[1];
                    Log::info('使用简单模式匹配: ' . $cleanTradeNo);
                }
            }
            
            // 如果仍然未能匹配，返回错误
            if ($cleanTradeNo === null) {
                Log::warning('订单号格式不匹配: [' . $tradeNo . ']');
                return $this->error('请使用标准格式：订单号:xxxxxx 进行查询');
            }
            
            // 查询订单信息
            try {
                // 加入更多日志，确保可以追踪查询过程
                Log::info('准备查询订单，清理后的订单号: ' . $cleanTradeNo . ', 用户ID: ' . $this->user->id);
                
                // 添加索引字段，优化查询速度
                $order = Db::name('order')
                    ->where('trade_no', $cleanTradeNo)
                    ->where('user_id', $this->user->id)
                    ->field('trade_no, goods_name, quantity, total_amount, create_time, status, goods_id')  // 只查询需要的字段
                    ->find();
                
                Log::info('订单查询结果: ' . ($order ? json_encode($order) : '未找到订单'));
                
                if ($order) {
                    // 格式化订单状态
                    $statusText = '未知';
                    switch ((int)$order['status']) {
                        case 0: $statusText = '未支付'; break;
                        case 1: $statusText = '已支付'; break;
                        case 2: $statusText = '已关闭'; break;
                        case 3: $statusText = '已退款'; break;
                        default: $statusText = '处理中'; break;
                    }
                    
                    // 查询供货商信息
                    $supplierInfo = null;
                    if (!empty($order['goods_name'])) {
                        $supplierInfo = $this->getSupplierByGoodsName($order['goods_name']);
                    }
                    
                    // 返回查询到的订单信息
                    $orderData = [
                        'trade_no' => $order['trade_no'],
                        'goods_name' => $order['goods_name'],
                        'quantity' => $order['quantity'],
                        'total_amount' => $order['total_amount'],
                        'create_time' => $order['create_time'],
                        'status' => $statusText,
                        'is_system_query_result' => true,
                        'query_time' => time(),
                        'supplier_info' => $supplierInfo // 添加供货商信息
                    ];
                    
                    return $this->success('查询成功', $orderData);
                }
            } catch (\Exception $e) {
                Log::error('查询订单时数据库错误: ' . $e->getMessage());
                // 继续执行，返回模拟数据
            }
            
            // 如果数据库查询失败或未找到订单，返回模拟数据
            $order = [
                'trade_no' => $cleanTradeNo,
                'goods_name' => '商品详情',
                'quantity' => 1,
                'total_amount' => '待确认',
                'create_time' => time(),
                'status' => '处理中',
                'is_system_query_result' => true,
                'query_time' => time(),
                'supplier_info' => null
            ];
            
            Log::info('返回模拟订单信息: ' . json_encode($order, JSON_UNESCAPED_UNICODE));
            
            // 返回订单信息
            return $this->success('查询成功', $order);
        } catch (\Exception $e) {
            Log::error('查询订单信息失败: ' . $e->getMessage());
            return $this->error('查询订单信息失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 根据商品名称查询供货商信息
     * 
     * @param string $goodsName 商品名称
     * @return array|null 供货商信息
     */
    protected function getSupplierByGoodsName($goodsName)
    {
        try {
            Log::info('查询供货商，商品名称: ' . $goodsName);
            
            // 根据商品名称查询商品
            $goods = Db::name('goods')
                ->where('name', $goodsName)
                ->select()
                ->toArray();
                
            if (empty($goods)) {
                Log::info('未找到对应商品: ' . $goodsName);
                return null;
            }
            
            Log::info('找到相关商品数量: ' . count($goods));
            
            // 查找parent_id为0的商品
            $supplierGoods = null;
            foreach ($goods as $item) {
                if (isset($item['parent_id']) && $item['parent_id'] == 0) {
                    $supplierGoods = $item;
                    break;
                }
            }
            
            if (!$supplierGoods) {
                Log::info('未找到供货商商品(parent_id=0)');
                return null;
            }
            
            Log::info('找到供货商商品: ' . json_encode($supplierGoods, JSON_UNESCAPED_UNICODE));
            
            // 获取供货商用户信息
            if (isset($supplierGoods['user_id']) && $supplierGoods['user_id'] > 0) {
                $supplier = Db::name('user')
                    ->where('id', $supplierGoods['user_id'])
                    ->field('id, username, nickname, mobile')  // 只查询必要字段
                    ->find();
                    
                if ($supplier) {
                    Log::info('找到供货商: ' . json_encode($supplier, JSON_UNESCAPED_UNICODE));
                    
                    return [
                        'user_id' => $supplier['id'],
                        'username' => $supplier['username'],
                        'nickname' => $supplier['nickname'] ?? $supplier['username'],
                        'mobile' => $supplier['mobile'] ?? '',
                        'goods_id' => $supplierGoods['id']
                    ];
                }
            }
            
            return null;
        } catch (\Exception $e) {
            Log::error('查询供货商信息异常: ' . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 检查设置是否允许供货商
     */
    public function checkSupplierAllowed()
    {
        try {
            // 确保用户已登录
            if (empty($this->user)) {
                return $this->error('请先登录');
            }
            
            // 查询商家的设置记录
            $settings = Db::name('plugin_chat_settings')
                ->where('user_id', $this->user->id)
                ->find();
                
            if (empty($settings) || empty($settings['settings'])) {
                return $this->success('查询成功', ['allowSupplier' => false]);
            }
            
            $settingsData = json_decode($settings['settings'], true) ?: [];
            $allowSupplier = isset($settingsData['allowSupplier']) ? 
                filter_var($settingsData['allowSupplier'], FILTER_VALIDATE_BOOLEAN) : false;
                
            return $this->success('查询成功', ['allowSupplier' => $allowSupplier]);
        } catch (\Exception $e) {
            Log::error('检查供货商设置失败: ' . $e->getMessage());
            return $this->error('检查供货商设置失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 创建与供货商的新会话
     */
    public function createSession()
    {
        try {
            // 确保用户已登录
            if (empty($this->user)) {
                return $this->error('请先登录');
            }
            
            // 获取参数
            $targetUserId = $this->request->post('target_user_id/d');
            $title = $this->request->post('title/s', '商品咨询');
            $message = $this->request->post('message/s', '您好，我想咨询您的商品相关事宜。');
            
            if (empty($targetUserId)) {
                return $this->error('供货商ID不能为空');
            }
            
            // 查询供货商是否存在
            $supplier = Db::name('user')
                ->where('id', $targetUserId)
                ->find();
                
            if (empty($supplier)) {
                return $this->error('供货商不存在');
            }
            
            // 检查是否已存在会话
            $existSession = Db::name('plugin_chat_sessions')
                ->where('user_id', $this->user->id)
                ->where('contact_id', $targetUserId)
                ->whereOr(function ($query) use ($targetUserId) {
                    $query->where('user_id', $targetUserId)
                          ->where('contact_id', $this->user->id);
                })
                ->find();
                
            if ($existSession) {
                // 如果已存在会话，直接返回
                return $this->success('会话已存在', $existSession);
            }
            
            // 创建新会话
            $sessionData = [
                'user_id' => $this->user->id,
                'contact_id' => $targetUserId,
                'merchant_id' => $targetUserId, // 修改为供货商ID，确保供货商也能看到会话
                'title' => $title,
                'source' => 'supplier', // 标记为供货商会话
                'status' => 'open',
                'last_message' => $message,
                'last_time' => time(),
                'create_time' => time(),
                'update_time' => time(),
                'source_order' => $cleanTradeNo, // 记录来源订单号
                'source_session' => $sessionId,  // 记录来源会话
            ];
            
            $sessionId = Db::name('plugin_chat_sessions')->insertGetId($sessionData);
            
            if (!$sessionId) {
                return $this->error('创建会话失败');
            }
            
            // 添加第一条消息
            $messageData = [
                'session_id' => $sessionId,
                'sender_id' => $this->user->id,
                'sender_type' => 'customer',
                'role_type' => 'customer',
                'message' => $message,
                'message_type' => 'text',
                'is_read' => 0,
                'create_time' => time(),
                'update_time' => time()
            ];
            
            Db::name('plugin_chat_messages')->insert($messageData);
            
            // 添加系统消息，告知供货商有客户咨询
            $supplierMessageData = [
                'session_id' => $sessionId,
                'sender_id' => 0, // 系统消息
                'sender_type' => 'staff',
                'role_type' => 'merchant',
                'message' => '有客户对您的商品发起了咨询，请及时回复。',
                'message_type' => 'text',
                'is_read' => 0,
                'is_system' => 1,
                'create_time' => time(),
                'update_time' => time()
            ];
            
            Db::name('plugin_chat_messages')->insert($supplierMessageData);
            
            // 更新会话数据
            $sessionData['id'] = $sessionId;
            
            return $this->success('创建会话成功', $sessionData);
        } catch (\Exception $e) {
            Log::error('创建供货商会话失败: ' . $e->getMessage());
            return $this->error('创建会话失败: ' . $e->getMessage());
        }
    }

    /**
     * 商家申请加入其他会话
     * @return \think\response\Json
     */
    public function requestJoinSession()
    {
        try {
            $sessionId = $this->request->param('session_id', 0, 'intval');
            $reason = $this->request->param('reason', '', 'trim');
            
            if (empty($sessionId)) {
                return $this->error('会话ID不能为空');
            }
            
            // 获取当前商家信息
            $supplierInfo = $this->getCurrentSupplier();
            if (!$supplierInfo) {
                return $this->error('获取商家信息失败');
            }
            
            // 检查会话是否存在
            $session = Db::name('plugin_chat_sessions')->where('id', $sessionId)->find();
            if (!$session) {
                return $this->error('会话不存在');
            }
            
            // 检查该商家是否已经是会话参与者
            $isParticipant = Db::name('plugin_chat_session_participants')
                ->where('session_id', $sessionId)
                ->where('user_id', $supplierInfo['id'])
                ->where('user_type', 'supplier')
                ->find();
                
            if ($isParticipant) {
                return $this->error('您已经是该会话的参与者');
            }
            
            // 获取原会话商家信息
            $originalSupplier = Db::name('plugin_chat_session_participants')
                ->alias('p')
                ->join('plugin_suppliers s', 'p.user_id = s.id')
                ->where('p.session_id', $sessionId)
                ->where('p.user_type', 'supplier')
                ->field('s.id, s.name')
                ->find();
                
            if (!$originalSupplier) {
                return $this->error('原会话商家信息获取失败');
            }
            
            // 判断请求方的实际类型
            $requesterType = $this->getUserActualType($supplierInfo['id']);
            $targetType = $this->getUserActualType($originalSupplier['id']);

            // 创建加入申请
            $joinRequest = [
                'session_id' => $sessionId,
                'requester_id' => $supplierInfo['id'],
                'requester_type' => $requesterType,
                'requester_name' => $supplierInfo['name'],
                'target_id' => $originalSupplier['id'],
                'target_type' => $targetType,
                'reason' => $reason,
                'status' => 'pending',
                'create_time' => time(),
                'update_time' => time()
            ];

            Log::info("创建加入申请: 请求方类型={$requesterType}, 目标方类型={$targetType}");
            
            Db::name('plugin_chat_join_requests')->insert($joinRequest);
            
            // 创建系统通知消息，通知原商家有人申请加入
            $notificationMsg = [
                'session_id' => $sessionId,
                'sender_id' => 0,
                'sender_type' => 'staff', // 使用staff类型代替system
                'message' => "商家 【{$supplierInfo['name']}】 申请加入此会话" . ($reason ? "，原因：{$reason}" : ""),
                'message_type' => 'notification',
                'is_read' => 0,
                'is_system' => 1, // 标记为系统消息
                'visible_to' => json_encode(['supplier_'.$originalSupplier['id']]),
                'create_time' => time(),
                'update_time' => time()
            ];
            
            Db::name('plugin_chat_messages')->insert($notificationMsg);
            
            return $this->success('申请已发送，等待对方商家审核');
        } catch (\Exception $e) {
            Log::error('申请加入会话失败: ' . $e->getMessage());
            return $this->error('申请加入会话失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 处理商家加入会话申请
     * @return \think\response\Json
     */
    public function handleJoinRequest()
    {
        try {
            // 获取POST提交的数据
            $postData = $this->request->post();
            
            // 记录接收到的原始请求数据
            Log::info('处理加入申请请求接收数据: ' . json_encode($postData));
            
            // 从POST数据中获取请求ID和操作类型
            $requestId = isset($postData['request_id']) ? intval($postData['request_id']) : 0;
            $action = isset($postData['action']) ? trim($postData['action']) : '';
            
            if (empty($requestId) || empty($action)) {
                Log::error('处理加入申请参数不完整: request_id=' . $requestId . ', action=' . $action);
                return $this->error('参数不完整');
            }
            
            // 获取当前商家信息
            $supplierInfo = $this->getCurrentSupplier();
            if (!$supplierInfo) {
                Log::error('处理加入申请获取商家信息失败');
                return $this->error('获取商家信息失败');
            }
            
            Log::info('处理加入申请的商家信息: ' . json_encode($supplierInfo));
            
            // 获取加入申请详情
            $joinRequest = Db::name('plugin_chat_join_requests')
                ->where('id', $requestId)
                ->where('target_id', $supplierInfo['id'])
                ->where('target_type', 'supplier')
                ->where('status', 'pending')
                ->find();
                
            if (!$joinRequest) {
                Log::error('处理加入申请: 申请不存在或已处理 request_id=' . $requestId);
                return $this->error('申请不存在或已处理');
            }
            
            Log::info('找到待处理的申请: ' . json_encode($joinRequest));
            
            // 更新申请状态
            $status = $action === 'approve' ? 'approved' : 'rejected';
            $updateResult = Db::name('plugin_chat_join_requests')
                ->where('id', $requestId)
                ->update([
                    'status' => $status,
                    'update_time' => time()
                ]);
                
            Log::info('申请状态更新结果: ' . ($updateResult ? '成功' : '失败') . ', 新状态: ' . $status);
                
            // 如果是同意申请
            if ($action === 'approve') {
                // 获取会话详情，以便向申请者展示
                $sessionInfo = Db::name('plugin_chat_sessions')
                    ->alias('s')
                    ->where('s.id', $joinRequest['session_id'])
                    ->find();
                
                if (!$sessionInfo) {
                    Log::error('获取会话详情失败: session_id=' . $joinRequest['session_id']);
                    return $this->error('获取会话详情失败');
                }
                
                Log::info('获取到会话详情: ' . json_encode($sessionInfo));
                
                // 检查用户是否已经是会话参与者
                $existingParticipant = Db::name('plugin_chat_session_participants')
                    ->where('session_id', $joinRequest['session_id'])
                    ->where('user_id', $joinRequest['requester_id'])
                    ->where('user_type', $joinRequest['requester_type'])
                    ->find();
                
                if ($existingParticipant) {
                    Log::info('用户已经是会话参与者，无需重复添加: ' . json_encode($existingParticipant));
                    
                    // 如果参与者记录存在但未激活，将其激活
                    if ($existingParticipant['is_active'] != 1) {
                        Db::name('plugin_chat_session_participants')
                            ->where('id', $existingParticipant['id'])
                            ->update([
                                'is_active' => 1,
                                'update_time' => time()
                            ]);
                        Log::info('已激活现有参与者: ' . json_encode($existingParticipant));
                    }
                } else {
                    // 添加新商家为会话参与者
                    $participantData = [
                        'session_id' => $joinRequest['session_id'],
                        'user_id' => $joinRequest['requester_id'],
                        'user_type' => $joinRequest['requester_type'],
                        'join_time' => time(),
                        'is_active' => 1
                    ];
                    
                    try {
                        $insertResult = Db::name('plugin_chat_session_participants')->insert($participantData);
                        Log::info('添加会话参与者结果: ' . ($insertResult ? '成功' : '失败') . ', 数据: ' . json_encode($participantData));
                        
                        // 在会话中添加系统消息，通知所有参与者有新的供货商加入
                        $systemMessage = [
                            'session_id' => $joinRequest['session_id'],
                            'sender_id' => 0,
                            'sender_type' => 'system',
                            'message' => "商家 【{$joinRequest['requester_name']}】 已作为供货商加入此会话",
                            'message_type' => 'text',
                            'is_read' => 0,
                            'is_system' => 1,
                            'create_time' => time(),
                            'update_time' => time()
                        ];
                        
                        Db::name('plugin_chat_messages')->insert($systemMessage);
                        Log::info('添加系统通知消息成功: ' . json_encode($systemMessage));
                    } catch (\Exception $e) {
                        Log::error('添加供货商参与者失败: ' . $e->getMessage());
                        // 继续处理，邀请记录仍会创建
                    }
                }
                
                // 给会话添加系统消息 - 告知所有人
                $messageData = [
                    'session_id' => $joinRequest['session_id'],
                    'sender_id' => 0,
                    'sender_type' => 'staff', // 使用staff类型而不是system
                    'message' => "商家 【{$joinRequest['requester_name']}】 已加入此会话",
                    'message_type' => 'notification',
                    'is_read' => 0,
                    'is_system' => 1, // 标记为系统消息
                    'create_time' => time(),
                    'update_time' => time()
                ];
                
                try {
                    $msgResult = Db::name('plugin_chat_messages')->insert($messageData);
                    Log::info('添加系统通知消息结果: ' . ($msgResult ? '成功' : '失败'));
                } catch (\Exception $e) {
                    Log::error('添加系统通知消息失败: ' . $e->getMessage());
                }
                
                // 更新会话最后消息记录
                Db::name('plugin_chat_sessions')
                    ->where('id', $joinRequest['session_id'])
                    ->update([
                        'last_time' => time(),
                        'last_message' => "商家 【{$joinRequest['requester_name']}】 已加入此会话"
                    ]);
            }
            
            // 通知申请方结果
            $notificationToRequester = [
                'session_id' => $joinRequest['session_id'],
                'sender_id' => 0,
                'sender_type' => 'staff', // 使用staff类型而不是system
                'message' => "您申请加入会话的请求已" . ($action === 'approve' ? '通过' : '被拒绝'),
                'message_type' => 'notification',
                'is_read' => 0,
                'is_system' => 1, // 标记为系统消息
                'visible_to' => json_encode(['supplier_'.$joinRequest['requester_id']]),
                'create_time' => time(),
                'update_time' => time()
            ];
            
            try {
                $notifyResult = Db::name('plugin_chat_messages')->insert($notificationToRequester);
                Log::info('添加申请者通知消息结果: ' . ($notifyResult ? '成功' : '失败'));
            } catch (\Exception $e) {
                Log::error('添加申请者通知消息失败: ' . $e->getMessage());
            }
            
            Log::info('处理加入申请完成: request_id=' . $requestId . ', action=' . $action);
            if ($action === 'approve') {
                return $this->success('已同意加入申请', ['session_id' => $joinRequest['session_id']]);
            } else {
                return $this->success('已拒绝加入申请');
            }
        } catch (\Exception $e) {
            Log::error('处理加入会话申请失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return $this->error('处理申请失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取当前登录的商家信息
     * @return array|false
     */
    /**
     * 关闭超时的邀请请求
     * @param bool $force 是否强制检查所有邀请，不管是否过期
     * @return bool 是否存在并处理了超时邀请
     */
    protected function closeExpiredInvitations($force = false)
    {
        try {
            // 记录开始处理时间
            $startTime = microtime(true);
            Log::info('开始检查过期邀请请求，强制模式: ' . ($force ? '是' : '否'));
            
            // 默认设置：48小时
            $expiryValue = 48;
            $expiryUnit = 'hour';
            
            // 获取过期值
            $valueSetting = Db::name('plugin_chat_customer_settings')
                ->where('key', 'invitation_expiry_value')
                ->find();
                
            if ($valueSetting && isset($valueSetting['value'])) {
                $expiryValue = intval($valueSetting['value']);
                if ($expiryValue <= 0) {
                    $expiryValue = 48; // 如果设置值无效，使用默认值
                }
            }
            
            // 获取过期单位
            $unitSetting = Db::name('plugin_chat_customer_settings')
                ->where('key', 'invitation_expiry_unit')
                ->find();
                
            if ($unitSetting && isset($unitSetting['value'])) {
                $expiryUnit = $unitSetting['value'];
                // 确保单位有效
                if (!in_array($expiryUnit, ['minute', 'hour', 'day'])) {
                    $expiryUnit = 'hour'; // 默认使用小时
                }
            }
            
            // 根据单位计算秒数
            $expirySeconds = 0;
            switch ($expiryUnit) {
                case 'minute':
                    $expirySeconds = $expiryValue * 60;
                    break;
                case 'hour':
                    $expirySeconds = $expiryValue * 3600;
                    break;
                case 'day':
                    $expirySeconds = $expiryValue * 86400;
                    break;
                default:
                    $expirySeconds = $expiryValue * 3600; // 默认小时
                    break;
            }
            
            // 计算过期时间点
            $expiryTimestamp = time() - $expirySeconds;
            
            // 构建查询条件
            $where = [
                ['status', '=', 'pending'],
                ['is_invitation', '=', 1], // 只处理邀请类型，不处理申请类型
            ];
            
            // 非强制模式下，只检查已过期的邀请
            if (!$force) {
                $where[] = ['create_time', '<', $expiryTimestamp];
            }
            
            // 查找过期的邀请
            $expiredInvitations = Db::name('plugin_chat_join_requests')
                ->where($where)
                ->select()
                ->toArray();
                
            if (empty($expiredInvitations)) {
                Log::info('没有找到需要处理的过期邀请请求');
                return false; // 没有过期邀请
            }
            
            // 对邀请进行分类处理
            $toExpire = [];
            foreach ($expiredInvitations as $invitation) {
                // 检查是否真的过期了
                if (($invitation['create_time'] + $expirySeconds) < time()) {
                    $toExpire[] = $invitation;
                }
            }
            
            if (empty($toExpire)) {
                Log::info('没有真正过期的邀请请求');
                return false;
            }
            
            $expiredInvitations = $toExpire;
            
            Log::info('发现过期邀请请求 ' . count($expiredInvitations) . ' 条，超时时间设置值: ' . $expiryValue . '，单位: ' . $expiryUnit);
            
            // 获取待更新的ID列表
            $expiredIds = array_column($expiredInvitations, 'id');
            
            // 批量更新状态为拒绝
            $updateResult = Db::name('plugin_chat_join_requests')
                ->where('id', 'in', $expiredIds)
                ->update([
                    'status' => 'rejected',
                    'update_time' => time()
                ]);
                
            Log::info('已自动关闭 ' . $updateResult . ' 条超时邀请请求');
            
            // 为每个超时邀请添加系统消息通知
            $currentTime = time();
            $notificationMessages = [];
            
            foreach ($expiredInvitations as $invitation) {
                // 通知申请方(邀请发起方)
                $notificationMessages[] = [
                    'session_id' => $invitation['session_id'],
                    'sender_id' => 0,
                    'sender_type' => 'staff',
                    'message' => "您发起的邀请请求已超时自动关闭，邀请对象：{$invitation['target_type']} {$invitation['target_id']}",
                    'message_type' => 'notification',
                    'is_read' => 0,
                    'is_system' => 1,
                    'visible_to' => json_encode(['supplier_'.$invitation['requester_id']]),
                    'create_time' => $currentTime,
                    'update_time' => $currentTime
                ];
                
                // 通知目标方(被邀请方)
                $notificationMessages[] = [
                    'session_id' => $invitation['session_id'],
                    'sender_id' => 0,
                    'sender_type' => 'staff',
                    'message' => "来自 {$invitation['requester_name']} 的邀请请求已超时自动关闭",
                    'message_type' => 'notification',
                    'is_read' => 0,
                    'is_system' => 1,
                    'visible_to' => json_encode(['supplier_'.$invitation['target_id']]),
                    'create_time' => $currentTime,
                    'update_time' => $currentTime
                ];
            }
            
            // 批量插入通知消息
            if (!empty($notificationMessages)) {
                Db::name('plugin_chat_messages')->insertAll($notificationMessages);
                Log::info('已添加 ' . count($notificationMessages) . ' 条超时邀请请求通知消息');
            }
            
            // 记录执行时间
            $executionTime = round(microtime(true) - $startTime, 4);
            Log::info('过期邀请请求检查完成，耗时: ' . $executionTime . ' 秒');
            
            return $updateResult > 0;
        } catch (\Exception $e) {
            Log::error('处理超时邀请请求失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return false;
        }
    }
    
    protected function getCurrentSupplier()
    {
        $userId = $this->user->id;
        if (!$userId) {
            Log::error('获取商家信息失败: 用户ID为空');
            return false;
        }
        
        Log::info('尝试获取商家信息，用户ID: ' . $userId);
        
        try {
            // 首先检查plugin_suppliers表是否存在 - 使用不带前缀的表名
            $tableExists = Db::query("SHOW TABLES LIKE '%plugin_suppliers'");
            if (empty($tableExists)) {
                Log::error('获取商家信息失败: plugin_suppliers表不存在');
                // 如果表不存在，创建一个临时的供应商记录返回，以便功能可以正常工作
                return [
                    'id' => $userId, 
                    'user_id' => $userId,
                    'name' => $this->user->nickname ?: ('用户' . $userId)
                ];
            }
            
            $supplier = Db::name('plugin_suppliers')
                ->where('user_id', $userId)
                ->find();
                
            if (!$supplier) {
                Log::error('获取商家信息失败: 未找到用户ID为' . $userId . '的供应商记录');
                // 如果找不到记录，创建一个临时的供应商记录返回
                return [
                    'id' => $userId, 
                    'user_id' => $userId,
                    'name' => $this->user->nickname ?: ('用户' . $userId)
                ];
            }
            
            Log::info('成功获取商家信息: ' . json_encode($supplier));
            return $supplier;
        } catch (\Exception $e) {
            Log::error('获取商家信息异常: ' . $e->getMessage());
            // 返回一个临时的供应商记录
            return [
                'id' => $userId, 
                'user_id' => $userId,
                'name' => $this->user->nickname ?: ('用户' . $userId)
            ];
        }
    }
    
    /**
     * 获取待处理的加入会话申请列表
     * @return \think\response\Json
     */
    public function getPendingJoinRequests()
    {
        try {
            // 先处理过期的邀请
            $this->closeExpiredInvitations();
            
            $page = $this->request->param('page', 1, 'intval');
            $limit = $this->request->param('limit', 10, 'intval');
            
            // 获取当前商家信息
            $supplierInfo = $this->getCurrentSupplier();
            if (!$supplierInfo) {
                return $this->error('获取商家信息失败');
            }
            
            // 查询待处理申请 - 支持merchant和supplier类型
            $requests = Db::name('plugin_chat_join_requests')
                ->alias('jr')
                ->join('plugin_chat_sessions s', 'jr.session_id = s.id')
                ->where('jr.target_id', $supplierInfo['id'])
                ->where('jr.target_type', 'in', ['supplier', 'merchant'])
                ->where('jr.status', 'pending')
                ->field('jr.*, s.title as session_title')
                ->page($page, $limit)
                ->order('jr.create_time DESC')
                ->select()
                ->toArray();

            $total = Db::name('plugin_chat_join_requests')
                ->where('target_id', $supplierInfo['id'])
                ->where('target_type', 'in', ['supplier', 'merchant'])
                ->where('status', 'pending')
                ->count();
                
            return $this->success('获取成功', ['data' => $requests, 'total' => $total]);
        } catch (\Exception $e) {
            Log::error('获取申请列表失败: ' . $e->getMessage());
            return $this->error('获取申请列表失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取已处理的加入会话邀请列表
     * @return \think\response\Json
     */
    public function getProcessedJoinRequests()
    {
        try {
            $page = $this->request->param('page', 1, 'intval');
            $limit = $this->request->param('limit', 10, 'intval');
            
            // 获取当前商家信息
            $supplierInfo = $this->getCurrentSupplier();
            if (!$supplierInfo) {
                return $this->error('获取商家信息失败');
            }
            
            // 查询已处理邀请（已同意或已拒绝）- 支持merchant和supplier类型
            $requests = Db::name('plugin_chat_join_requests')
                ->alias('jr')
                ->join('plugin_chat_sessions s', 'jr.session_id = s.id', 'LEFT')
                ->where('jr.target_id', $supplierInfo['id'])
                ->where('jr.target_type', 'in', ['supplier', 'merchant'])
                ->where('jr.status', 'in', ['approved', 'rejected'])
                ->field('jr.*, s.title as session_title')
                ->page($page, $limit)
                ->order('jr.update_time DESC')
                ->select()
                ->toArray();

            $total = Db::name('plugin_chat_join_requests')
                ->where('target_id', $supplierInfo['id'])
                ->where('target_type', 'in', ['supplier', 'merchant'])
                ->where('status', 'in', ['approved', 'rejected'])
                ->count();
                
            return $this->success('获取成功', ['data' => $requests, 'total' => $total]);
        } catch (\Exception $e) {
            Log::error('获取已处理邀请列表失败: ' . $e->getMessage());
            return $this->error('获取已处理邀请列表失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取可申请接入的会话列表
     * @return \think\response\Json
     */
    public function getAvailableSessions()
    {
        try {
            $page = $this->request->param('page', 1, 'intval');
            $limit = $this->request->param('limit', 10, 'intval');
            $keyword = $this->request->param('keyword', '', 'trim');
            
            // 获取当前商家信息
            $supplierInfo = $this->getCurrentSupplier();
            if (!$supplierInfo) {
                return $this->error('获取商家信息失败');
            }
            
            // 查询条件：会话必须存在且当前商家不是会话的参与者
            $where = [
                ['s.status', 'in', ['active', 'pending']] // 会话状态为活跃或待处理
            ];
            
            // 排除当前商家已经是参与者的会话
            $participatedSessionIds = Db::name('plugin_chat_session_participants')
                ->where('user_id', $supplierInfo['id'])
                ->where('user_type', 'supplier')
                ->column('session_id');
                
            if (!empty($participatedSessionIds)) {
                $where[] = ['s.id', 'not in', $participatedSessionIds];
            }
            
            // 排除已经申请加入的会话
            $requestedSessionIds = Db::name('plugin_chat_join_requests')
                ->where('requester_id', $supplierInfo['id'])
                ->where('requester_type', 'supplier')
                ->where('status', 'in', ['pending', 'approved'])
                ->column('session_id');
                
            if (!empty($requestedSessionIds)) {
                $where[] = ['s.id', 'not in', $requestedSessionIds];
            }
            
            // 关键字搜索
            if (!empty($keyword)) {
                $where[] = ['s.title|c.nickname|c.username|su.name', 'like', "%{$keyword}%"];
            }
            
            // 查询会话列表
            $query = Db::name('plugin_chat_sessions')
                ->alias('s')
                ->join('user c', 's.contact_id = c.id', 'LEFT')
                ->join('plugin_chat_session_participants p', 'p.session_id = s.id AND p.user_type = "supplier"', 'LEFT')
                ->join('plugin_suppliers su', 'p.user_id = su.id', 'LEFT')
                ->where($where)
                ->field('s.id, s.title, s.create_time, s.update_time, 
                         c.id as customer_id, c.nickname as customer_name, 
                         su.id as supplier_id, su.name as supplier_name')
                ->group('s.id') // 确保每个会话只返回一次
                ->order('s.update_time DESC');
                
            $total = $query->count();
            $sessions = $query->page($page, $limit)->select()->toArray();
            
            // 获取每个会话的最后一条消息
            foreach ($sessions as &$session) {
                $lastMessage = Db::name('plugin_chat_messages')
                    ->where('session_id', $session['id'])
                    ->where('message_type', '<>', 'notification') // 排除系统通知类消息
                    ->order('create_time DESC')
                    ->field('content')
                    ->find();
                    
                $session['last_message'] = $lastMessage ? (mb_strlen($lastMessage['content']) > 30 ? mb_substr($lastMessage['content'], 0, 30) . '...' : $lastMessage['content']) : '';
                // 使用客户昵称或用户名
                $session['customer_name'] = $session['customer_name'] ?: '未知客户';
            }
            
            return $this->success('获取成功', ['data' => $sessions, 'total' => $total]);
        } catch (\Exception $e) {
            Log::error('获取可接入会话列表失败: ' . $e->getMessage());
            return $this->error('获取可接入会话列表失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取待处理的接入申请数量
     * @return \think\response\Json
     */
    public function getPendingJoinRequestsCount()
    {
        try {
            // 先处理过期的邀请
            $this->closeExpiredInvitations();
            
            // 获取当前商家信息
            $supplierInfo = $this->getCurrentSupplier();
            if (!$supplierInfo) {
                return $this->success('获取成功', 0);
            }
            
            // 查询待处理申请数量 - 支持merchant和supplier类型
            $count = Db::name('plugin_chat_join_requests')
                ->where('target_id', $supplierInfo['id'])
                ->where('target_type', 'in', ['supplier', 'merchant'])
                ->where('status', 'pending')
                ->count();
                
            return $this->success('获取成功', $count);
        } catch (\Exception $e) {
            Log::error('获取待处理申请数量失败: ' . $e->getMessage());
            return $this->success('获取成功', 0);
        }
    }
    
    /**
     * 检查当前用户是否是供应商
     * @return \think\response\Json
     */
    public function checkSupplierStatus()
    {
        try {
            $supplierInfo = $this->getCurrentSupplier();
            $isSupplier = !empty($supplierInfo);
            
            return $this->success('获取成功', ['is_supplier' => $isSupplier]);
        } catch (\Exception $e) {
            Log::error('检查供应商状态失败: ' . $e->getMessage());
            return $this->success('获取成功', ['is_supplier' => false]);
        }
    }
    
    /**
     * 接入申请页面
     * @return \think\response\View
     */
    public function joinRequestsPage()
    {
        // 检查用户是否已登录
        if (empty($this->user)) {
            $this->redirect('index/user/login');
        }
        
        // 检查是否是供应商
        $supplierInfo = $this->getCurrentSupplier();
        if (!$supplierInfo) {
            $this->error('您不是供应商，无法访问此页面');
        }
        
        return $this->view->fetch('join_requests');
    }
    
    /**
     * 获取会话的参与者列表
     * @return \think\response\Json
     */
    public function getSessionParticipants()
    {
        try {
            $sessionId = $this->request->param('session_id', 0, 'intval');
            
            if (empty($sessionId)) {
                return $this->error('会话ID不能为空');
            }
            
            // 获取当前用户ID
            $userId = $this->user->id;
            if (!$userId) {
                return $this->error('获取用户信息失败');
            }
            
            // 检查该用户是否是会话参与者
            $isParticipant = Db::name('plugin_chat_session_participants')
                ->where('session_id', $sessionId)
                ->where('user_id', $userId)
                ->find();
                
            if (!$isParticipant) {
                return $this->error('您不是该会话的参与者');
            }
            
            // 获取会话的所有参与者
            $participants = Db::name('plugin_chat_session_participants')
                ->alias('p')
                ->join('user u', 'p.user_id = u.id')
                ->where('p.session_id', $sessionId)
                ->where('p.is_active', 1)
                ->field('p.id as participant_id, p.user_id, p.user_type, p.join_time, 
                         u.id, u.username, u.nickname, u.avatar')
                ->select()
                ->toArray();
            
            // 对参与者进行去重，以user_id为唯一标识
            $uniqueParticipants = [];
            $userIds = [];
            
            foreach ($participants as $participant) {
                // 如果这个user_id已经存在，跳过这条记录
                if (in_array($participant['user_id'], $userIds)) {
                    continue;
                }
                
                // 标记这个user_id已处理
                $userIds[] = $participant['user_id'];
                
                // 为每个参与者添加是否是供货商的标识
                $participant['is_supplier'] = ($participant['user_type'] === 'supplier');
                
                // 检查商家是否被邀请为供货商
                if (!$participant['is_supplier']) {
                    $isInvitedAsSupplier = Db::name('plugin_chat_session_participants')
                        ->where('session_id', $sessionId)
                        ->where('user_id', $participant['user_id'])
                        ->where('user_type', 'supplier')
                        ->find();
                    
                    if ($isInvitedAsSupplier) {
                        $participant['is_supplier'] = true;
                        $participant['user_type'] = 'supplier'; // 修正角色类型
                    }
                }
                
                // 添加到去重后的数组
                $uniqueParticipants[] = $participant;
            }
                
            // 检查是否有邀请其他人的权限
            $session = Db::name('plugin_chat_sessions')->where('id', $sessionId)->find();
            $canInvite = false;
            
            if ($session) {
                // 任何参与者都可以邀请其他人
                $canInvite = true;
            }
            
            return $this->success('获取成功', [
                'participants' => $uniqueParticipants,
                'current_user_id' => $userId,
                'can_invite' => $canInvite
            ]);
        } catch (\Exception $e) {
            Log::error('获取会话参与者失败: ' . $e->getMessage());
            return $this->error('获取会话参与者失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取可以邀请的商家列表
     * @return \think\response\Json
     */
    public function getAvailableSuppliers()
    {
        try {
            $sessionId = $this->request->param('session_id', 0, 'intval');
            
            if (empty($sessionId)) {
                return $this->error('会话ID不能为空');
            }
            
            // 获取当前商家信息
            $supplierInfo = $this->getCurrentSupplier();
            if (!$supplierInfo) {
                return $this->error('获取商家信息失败');
            }
            
            // 获取已经是会话参与者的商家ID
            $participantIds = Db::name('plugin_chat_session_participants')
                ->where('session_id', $sessionId)
                ->where('user_type', 'supplier')
                ->column('user_id');
                
            // 查询可邀请的商家 - 从user表获取
            $merchants = Db::name('user')
                ->where('custom_status', 1) // 正常商户状态
                ->where('id', '<>', $this->user->id) // 排除当前商家
                ->whereNotIn('id', $participantIds) // 排除已参与的商家
                ->field('id, username, nickname, avatar')
                ->select()
                ->toArray();
                
            return $this->success('获取成功', $merchants);
        } catch (\Exception $e) {
            Log::error('获取可邀请商家失败: ' . $e->getMessage());
            return $this->error('获取可邀请商家失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 邀请其他商家加入会话
     * @return \think\response\Json
     */
    public function inviteSuppliers()
    {
        try {
            // 记录原始请求数据，用于调试
            $rawData = $this->request->param();
            Log::info('邀请商家请求参数: ' . json_encode($rawData));
            
            // 尝试多种方式获取参数
            $sessionId = isset($rawData['session_id']) ? intval($rawData['session_id']) : 0;
            // 兼容不同格式的supplier_ids参数
            $supplierIds = [];
            if (isset($rawData['supplier_ids'])) {
                if (is_array($rawData['supplier_ids'])) {
                    $supplierIds = $rawData['supplier_ids'];
                } elseif (is_string($rawData['supplier_ids'])) {
                    // 尝试解析JSON字符串
                    $supplierIds = json_decode($rawData['supplier_ids'], true) ?: [$rawData['supplier_ids']];
                }
            }
            // 尝试从supplier_ids_json参数获取
            elseif (isset($rawData['supplier_ids_json'])) {
                $json = json_decode($rawData['supplier_ids_json'], true);
                if (is_array($json)) {
                    $supplierIds = $json;
                }
            }
            
            $reason = isset($rawData['reason']) ? trim($rawData['reason']) : '';
            
            Log::info('处理后的参数: sessionId=' . $sessionId . ', supplierIds=' . json_encode($supplierIds) . ', reason=' . $reason);
            
            if (empty($sessionId) || empty($supplierIds)) {
                Log::error('邀请商家参数不完整: sessionId=' . $sessionId . ', supplierIds=' . json_encode($supplierIds));
                return $this->error('参数不完整');
            }
            
            // 检查当前用户是否登录
            if (!$this->user || !$this->user->id) {
                Log::error('邀请商家失败: 用户未登录');
                return $this->error('请先登录');
            }
            
            Log::info('当前用户ID: ' . $this->user->id);
            
            // 检查会话是否存在
            $session = Db::name('plugin_chat_sessions')->where('id', $sessionId)->find();
            if (!$session) {
                Log::error('邀请商家失败: 会话不存在，sessionId=' . $sessionId);
                return $this->error('会话不存在');
            }
            
            Log::info('找到会话: ' . json_encode($session));
            
            // 获取已经接受邀请的商家ID列表
            $acceptedSupplierIds = Db::name('plugin_chat_join_requests')
                ->where('session_id', $sessionId)
                ->where('target_type', 'supplier')
                ->where('status', 'approved')
                ->column('target_id');
                
            Log::info('已接受邀请的商家IDs: ' . json_encode($acceptedSupplierIds));
                
            // 过滤掉已经接受邀请的商家ID
            $validSupplierIds = array_filter($supplierIds, function($supplierId) use ($acceptedSupplierIds) {
                return !in_array($supplierId, $acceptedSupplierIds);
            });
            
            if (empty($validSupplierIds)) {
                Log::error('邀请失败: 所选商家都已接受过邀请');
                return $this->error('所选商家都已接受过邀请，请选择其他商家');
            }
            
            // 更新supplierIds为有效的IDs
            $supplierIds = $validSupplierIds;
            Log::info('有效的商家IDs: ' . json_encode($supplierIds));
            
            // 完全开放邀请功能，允许任何已登录用户邀请商家
            // 只要用户已登录，且会话存在，即可邀请
            Log::info('用户已登录且会话存在，允许邀请操作');
            
            // 批量创建邀请记录
            $inviteData = [];
            $notificationData = [];
            $currentTime = time();
            $processedSuppliers = 0;
            
            foreach ($supplierIds as $supplierId) {
                // 确保是整数ID
                $supplierId = intval($supplierId);
                if (!$supplierId) continue;
                
                Log::info('处理供应商ID: ' . $supplierId);
                
                // 检查供应商是否已经是该会话的参与者
                $alreadyParticipant = Db::name('plugin_chat_session_participants')
                    ->where('session_id', $sessionId)
                    ->where('user_id', $supplierId)
                    ->find();
                
                if ($alreadyParticipant) {
                    Log::info('供应商已是会话参与者，跳过: ' . $supplierId);
                    
                    // 如果存在但不是供货商角色，则更新为供货商角色
                    if ($alreadyParticipant['user_type'] !== 'supplier') {
                        Db::name('plugin_chat_session_participants')
                            ->where('id', $alreadyParticipant['id'])
                            ->update([
                                'user_type' => 'supplier',
                                'update_time' => time()
                            ]);
                        Log::info('已将现有参与者更新为供货商角色: ' . json_encode($alreadyParticipant));
                        
                        // 添加一条系统消息通知更新
                        $systemMessage = [
                            'session_id' => $sessionId,
                            'sender_id' => 0,
                            'sender_type' => 'system',
                            'message' => "商家 【{$targetName}】 角色已更新为供货商",
                            'message_type' => 'text',
                            'is_read' => 0,
                            'is_system' => 1,
                            'create_time' => $currentTime,
                            'update_time' => $currentTime
                        ];
                        
                        try {
                            Db::name('plugin_chat_messages')->insert($systemMessage);
                            Log::info('添加角色更新系统消息成功: ' . json_encode($systemMessage));
                        } catch (\Exception $e) {
                            Log::error('添加角色更新系统消息失败: ' . $e->getMessage());
                        }
                    }
                    
                    continue; // 已经是参与者，跳过处理
                }
                
                // 获取目标商家信息
                $targetUser = Db::name('user')
                    ->where('id', $supplierId)
                    ->field('id, username, nickname')
                    ->find();
                
                if (!$targetUser) {
                    Log::error('目标商家不存在: ' . $supplierId);
                    continue; // 目标商家不存在，跳过处理
                }
                
                Log::info('找到目标商家: ' . json_encode($targetUser));
                
                $targetName = $targetUser['nickname'] ?: $targetUser['username'];
                $requesterName = $this->user->nickname ?: $this->user->username;
                
                // 创建邀请记录
                $inviteRecord = [
                    'session_id' => $sessionId,
                    'requester_id' => $this->user->id,
                    'requester_type' => 'merchant',
                    'requester_name' => $requesterName,
                    'target_id' => $supplierId,
                    'target_type' => 'supplier',
                    'reason' => $reason,
                    'status' => 'pending',
                    'is_invitation' => 1, // 标记为邀请类型
                    'create_time' => $currentTime,
                    'update_time' => $currentTime
                ];
                
                Log::info('创建邀请记录: ' . json_encode($inviteRecord));
                $inviteData[] = $inviteRecord;
                
                // 移除直接添加参与者的代码，需要等待商家B在join页面同意后才添加
                
                // 创建通知消息
                $notificationRecord = [
                    'session_id' => 0, // 0表示系统消息，不属于具体会话
                    'sender_id' => $this->user->id,
                    'sender_type' => 'supplier',
                    'content' => "商家 【{$requesterName}】 邀请您加入会话 #{$sessionId}" . ($reason ? "，原因：{$reason}" : ""),
                    'message_type' => 'invitation',
                    'is_read' => 0,
                    'is_system' => 1,
                    'visible_to' => json_encode(['supplier_'.$supplierId]),
                    'extra_data' => json_encode([
                        'session_id' => $sessionId,
                        'session_title' => $session['title'],
                        'inviter_id' => $this->user->id,
                        'inviter_name' => $requesterName
                    ]),
                    'create_time' => $currentTime,
                    'update_time' => $currentTime
                ];
                
                Log::info('创建通知消息: ' . json_encode($notificationRecord));
                $notificationData[] = $notificationRecord;
                
                $processedSuppliers++;
            }
            
            Log::info('共处理供应商数量: ' . $processedSuppliers);
            
            if (empty($inviteData)) {
                Log::error('邀请商家失败: 没有有效的邀请数据');
                return $this->error('没有有效的邀请对象');
            }
            
            // 插入邀请记录
            try {
                $insertResult = Db::name('plugin_chat_join_requests')->insertAll($inviteData);
                Log::info('插入邀请记录成功: ' . $insertResult);
            } catch (\Exception $e) {
                Log::error('插入邀请记录失败: ' . $e->getMessage());
                return $this->error('保存邀请记录失败: ' . $e->getMessage());
            }
            
            // 插入通知消息
            if (!empty($notificationData)) {
                try {
                    $notifyResult = Db::name('plugin_chat_messages')->insertAll($notificationData);
                    Log::info('插入通知消息成功: ' . $notifyResult);
                } catch (\Exception $e) {
                    Log::error('插入通知消息失败: ' . $e->getMessage());
                    // 这里不返回错误，因为邀请记录已经成功保存
                }
            }
            
            return $this->success('邀请已发送，等待对方接受');
        } catch (\Exception $e) {
            Log::error('邀请商家失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return $this->error('邀请商家失败: ' . $e->getMessage());
        }
    }

    /**
     * 根据商品名称查找商家
     * @return \think\response\Json
     */
    public function getMerchantsByGoods()
    {
        try {
            $goodsName = $this->request->param('goods_name', '', 'trim');
            $sessionId = $this->request->param('session_id', 0, 'intval');
            
            if (empty($goodsName)) {
                return $this->error('商品名称不能为空');
            }
            
            if (empty($sessionId)) {
                return $this->error('会话ID不能为空');
            }
            
            // 获取当前商家信息
            $supplierInfo = $this->getCurrentSupplier();
            if (!$supplierInfo) {
                return $this->error('获取商家信息失败');
            }
            
            // 获取已经是会话参与者的商家ID
            $participantIds = Db::name('plugin_chat_session_participants')
                ->where('session_id', $sessionId)
                ->where('user_type', 'supplier')
                ->column('user_id');
            
            // 查询符合条件的商品
            $goods = Db::name('goods')
                ->where('name', 'like', "%{$goodsName}%")
                ->where('parent_id', 0)  // parent_id为0
                ->where('user_id', '<>', $this->user->id)  // 排除当前商家
                ->whereNotIn('user_id', $participantIds)  // 排除已参与的商家
                ->field('id, name, user_id')
                ->select()
                ->toArray();
                
            if (empty($goods)) {
                return $this->success('未找到相关商品', []);
            }
            
            // 获取这些商品关联的商家信息
            $userIds = array_unique(array_column($goods, 'user_id'));
            
            $merchants = Db::name('user')
                ->whereIn('id', $userIds)
                ->where('custom_status', 1)  // 正常商户状态
                ->field('id, username, nickname, avatar')
                ->select()
                ->toArray();
                
            // 格式化返回数据
            $result = [];
            foreach ($merchants as $merchant) {
                $result[] = [
                    'id' => $merchant['id'],
                    'name' => $merchant['nickname'] ?: $merchant['username'],
                    'goods' => array_filter($goods, function($item) use ($merchant) {
                        return $item['user_id'] == $merchant['id'];
                    })
                ];
            }
            
            return $this->success('获取成功', $result);
        } catch (\Exception $e) {
            Log::error('根据商品名称查找商家失败: ' . $e->getMessage());
            return $this->error('查询失败: ' . $e->getMessage());
        }
    }

    /**
     * 根据商品名称查找供货商
     * @return \think\response\Json
     */
    public function getSuppliersByGoods()
    {
        try {
            $goodsName = $this->request->param('goods_name', '', 'trim');
            $sessionId = $this->request->param('session_id', 0, 'intval');
            
            if (empty($goodsName)) {
                return $this->error('商品名称不能为空');
            }
            
            if (empty($sessionId)) {
                return $this->error('会话ID不能为空');
            }
            
            // 获取当前商家信息
            $supplierInfo = $this->getCurrentSupplier();
            if (!$supplierInfo) {
                return $this->error('获取商家信息失败');
            }
            
            // 获取已经是会话参与者的供货商ID
            $participantIds = Db::name('plugin_chat_session_participants')
                ->where('session_id', $sessionId)
                ->where('user_type', 'supplier')
                ->column('user_id');
            
            // 查询符合条件的商品
            $goods = Db::name('goods')
                ->where('name', 'like', "%{$goodsName}%")
                ->where('parent_id', 0)  // parent_id为0
                ->where('user_id', '<>', $this->user->id)  // 排除当前商家
                ->whereNotIn('user_id', $participantIds)  // 排除已参与的供货商
                ->field('id, name, user_id')
                ->select()
                ->toArray();
                
            if (empty($goods)) {
                return $this->success('未找到相关商品', []);
            }
            
            // 获取这些商品关联的供货商信息
            $userIds = array_unique(array_column($goods, 'user_id'));
            
            $suppliers = Db::name('user')
                ->whereIn('id', $userIds)
                ->where('custom_status', 1)  // 正常用户状态
                ->field('id, username, nickname, avatar')
                ->select()
                ->toArray();
                
            // 格式化返回数据
            $result = [];
            foreach ($suppliers as $supplier) {
                $result[] = [
                    'id' => $supplier['id'],
                    'name' => $supplier['nickname'] ?: $supplier['username'],
                    'goods' => array_filter($goods, function($item) use ($supplier) {
                        return $item['user_id'] == $supplier['id'];
                    })
                ];
            }
            
            return $this->success('获取成功', $result);
        } catch (\Exception $e) {
            Log::error('根据商品名称查找供货商失败: ' . $e->getMessage());
            return $this->error('查询失败: ' . $e->getMessage());
        }
    }

    /**
     * 标记消息为已读
     */
    public function markMessageRead()
    {
        try {
            // 确保用户已登录
            if (empty($this->user)) {
                return $this->error('请先登录');
            }
            
            $messageId = $this->request->post('message_id/d');
            $sessionId = $this->request->post('session_id/d');
            
            if (empty($messageId) || empty($sessionId)) {
                return $this->error('参数错误');
            }
            
            // 检查会话是否存在
            $session = Db::name('plugin_chat_sessions')
                ->where('id', $sessionId)
                ->find();
                
            if (empty($session)) {
                return $this->error('会话不存在');
            }
            
            // 检查消息是否存在
            $message = Db::name('plugin_chat_messages')
                ->where('id', $messageId)
                ->where('session_id', $sessionId)
                ->find();
                
            if (empty($message)) {
                return $this->error('消息不存在');
            }
            
            // 已经是商家已读状态，直接返回成功
            if (!empty($message['merchant_read']) && $message['merchant_read'] == 1) {
                return $this->success('消息已读');
            }
            
            // 更新消息为商家已读状态（保留原有is_read字段更新，但确保只有merchant_read被设置）
            $currentTime = time();
            Db::name('plugin_chat_messages')
                ->where('id', $messageId)
                ->update([
                    'is_read' => 1, // 保留向后兼容
                    'read_time' => $currentTime,
                    'merchant_read' => 1,
                    'merchant_read_time' => $currentTime,
                    'update_time' => $currentTime
                ]);

            // 调用Hook清理缓存
            $hook = new \plugin\Customersystem\Hook();
            $hook->afterMessageRead($sessionId, $messageId);

            return $this->success('标记成功', [
                'message_id' => $messageId,
                'is_read' => 1,
                'merchant_read' => 1,
                'read_time' => $currentTime
            ]);
        } catch (\Exception $e) {
            Log::error('标记消息已读失败: ' . $e->getMessage());
            return $this->error('标记消息已读失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量标记会话消息为已读
     */
    public function markSessionMessagesRead()
    {
        $request = $this->request;
        $sessionId = $request->post('session_id/d', 0);
        $roleType = $request->post('role_type', ''); // 表示当前用户角色：customer, staff, merchant
        $viewerRole = $request->post('viewer_role', ''); // 查看者的角色
        $isVisible = $request->post('is_visible/d', 0); // 添加页面可见性参数，0=不可见，1=可见
        $autoMarkRead = $request->post('auto_mark_read/d', 0); // 是否自动标记已读，0=不自动标记，1=自动标记
        
        // 验证参数
        if (!$sessionId) {
            return $this->error('会话ID不能为空');
        }
        
        if (!$roleType) {
            return $this->error('角色类型不能为空');
        }
        
        // 检查是否禁用了自动标记已读功能
        if ($autoMarkRead == 0) {
            // 系统设置为禁用自动标记已读
            return $this->success('已禁用自动标记已读功能', [
                'status' => 'disabled',
                'reason' => 'auto_mark_read_disabled'
            ]);
        }
        
        // 如果页面不可见，不标记消息已读（除非强制标记）
        $forceMarkRead = $request->post('force_mark_read/d', 0);
        if (!$isVisible && !$forceMarkRead) {
            return $this->success('页面不可见，暂不标记消息已读', [
                'status' => 'skipped',
                'reason' => 'page_not_visible'
            ]);
        }
        
        // 获取会话信息
        $session = \plugin\Customersystem\model\Session::where('id', $sessionId)->find();
        if (!$session) {
            return $this->error('会话不存在');
        }
        
        // 准备查询条件
        $where = [];
        $where[] = ['session_id', '=', $sessionId];
        
        // 根据角色类型确定要更新的消息
        switch ($viewerRole) {
            case 'customer':
                // 客户查看时，标记来自客服和商家的消息为已读
                $where[] = ['role_type', 'in', ['staff', 'merchant']];
                $where[] = ['customer_read', '=', 0]; // 只更新未读的消息
                $updateData = ['customer_read' => 1, 'customer_read_time' => time()];
                if (!isset($session['customer_last_read_time']) || $session['customer_last_read_time'] < time()) {
                    // 更新会话中客户最后读取时间
                    $session->customer_last_read_time = time();
                    $session->save();
                }
                break;
                
            case 'merchant':
                // 商家查看时，标记客户和客服的消息为已读
                $where[] = ['role_type', 'in', ['customer', 'staff']];
                $where[] = ['merchant_read', '=', 0]; // 只更新未读的消息
                $updateData = ['merchant_read' => 1, 'merchant_read_time' => time()];
                if (!isset($session['merchant_last_read_time']) || $session['merchant_last_read_time'] < time()) {
                    // 更新会话中商家最后读取时间
                    $session->merchant_last_read_time = time();
                    $session->save();
                }
                break;
                
            case 'staff':
                // 客服查看时，标记客户和商家的消息为已读
                $where[] = ['role_type', 'in', ['customer', 'merchant']];
                $where[] = ['staff_read', '=', 0]; // 只更新未读的消息
                $updateData = ['staff_read' => 1, 'staff_read_time' => time()];
                if (!isset($session['staff_last_read_time']) || $session['staff_last_read_time'] < time()) {
                    // 更新会话中客服最后读取时间
                    $session->staff_last_read_time = time();
                    $session->save();
                }
                break;
                
            default:
                return $this->error('未知的查看者角色');
        }
        
        // 不再更新通用的is_read字段，确保角色读取状态完全独立
        // $updateData['is_read'] = 1; 
        // $updateData['read_time'] = time();
        
        // 执行更新
        $affectedRows = \plugin\Customersystem\model\Message::where($where)->update($updateData);

        // 更新会话表中对应角色的未读计数
        $this->updateSessionUnreadCount($sessionId, $viewerRole);

        // 调用Hook清理缓存
        $hook = new \plugin\Customersystem\Hook();
        $hook->afterBatchMessageRead($sessionId, $viewerRole);

        return $this->success('标记消息已读成功', [
            'affected_rows' => $affectedRows,
            'session_id' => $sessionId,
            'viewer_role' => $viewerRole
        ]);
    }
    
    // 新增方法：更新会话表中对应角色的未读计数
    protected function updateSessionUnreadCount($sessionId, $viewerRole)
    {
        $session = \plugin\Customersystem\model\Session::where('id', $sessionId)->find();
        if (!$session) {
            return false;
        }
        
        $updateData = [];
        
        switch ($viewerRole) {
            case 'staff':
                // 更新客服未读计数
                $unreadCount = \plugin\Customersystem\model\Message::where([
                    ['session_id', '=', $sessionId],
                    ['role_type', 'in', ['customer', 'merchant']],
                    ['staff_read', '=', 0],
                    ['is_recalled', '=', 0]
                ])->count();
                $updateData['staff_unread_count'] = $unreadCount;
                break;
                
            case 'merchant':
                // 更新商家未读计数 (暂不处理，可根据实际表结构添加)
                // 如果有merchant_unread_count字段，可以添加
                break;
                
            case 'customer':
                // 更新客户未读计数 (暂不处理，通常只记录服务方的未读)
                break;
        }
        
        if (!empty($updateData)) {
            $session->save($updateData);
        }
        
        return true;
    }

    /**
     * 检查用户是否是供货商（通过已处理邀请记录）
     * @return \think\response\Json
     */
    public function checkSupplierByProcessedInvites()
    {
        try {
            // 确保用户已登录
            if (empty($this->user)) {
                return $this->error('请先登录');
            }
            
            $userId = $this->user->id;
            
            // 尝试通过增强的供货商检查方法获取结果
            $supplierInfo = $this->getSupplierWithInvites();
            
            // 如果能获取到供货商信息，说明用户是供货商
            $isSupplier = $supplierInfo !== false;
            
            $supplierData = [
                'is_supplier' => $isSupplier,
                'user_id' => $userId
            ];
            
            if ($isSupplier) {
                $supplierData['supplier_info'] = $supplierInfo;
            }
            
            return $this->success('检查完成', $supplierData);
        } catch (\Exception $e) {
            Log::error('检查供货商状态失败: ' . $e->getMessage());
            return $this->error('检查失败: ' . $e->getMessage());
        }
    }

    /**
     * 清除已处理的邀请请求记录
     * @return \think\response\Json
     */
    public function clearProcessedJoinRequests()
    {
        try {
            // 确保用户已登录
            if (empty($this->user)) {
                return $this->error('请先登录');
            }
            
            $userId = $this->user->id;
            
            // 获取当前商家信息
            $supplierInfo = $this->getCurrentSupplier();
            if (!$supplierInfo) {
                return $this->error('获取商家信息失败');
            }
            
            // 获取用户已处理的邀请请求
            $result = Db::name('plugin_chat_join_requests')
                ->where('target_id', $supplierInfo['id'])
                ->where('target_type', 'supplier')
                ->where('status', 'in', ['approved', 'rejected'])
                ->delete();
                
            Log::info('已清除用户ID='.$userId.'的已处理邀请请求记录, 受影响行数: '.$result);
            
            return $this->success('已清除所有已处理的邀请记录');
        } catch (\Exception $e) {
            Log::error('清除已处理邀请请求记录失败: ' . $e->getMessage());
            return $this->error('清除失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 批量删除会话
     * @return \think\response\Json
     */
    public function batchDeleteSessions()
    {
        try {
            // 确保用户已登录
            if (empty($this->user)) {
                return $this->error('请先登录');
            }
            
            $sessionIds = $this->request->post('session_ids/a');
            if (empty($sessionIds)) {
                return $this->error('请选择要删除的会话');
            }
            
            Log::info('批量删除会话, 用户ID='.$this->user->id.', 会话IDs='.json_encode($sessionIds));
            
            // 验证用户对这些会话的权限
            $validSessions = Db::name('plugin_chat_sessions')
                ->alias('s')
                ->join('plugin_chat_session_participants p', 'p.session_id = s.id', 'LEFT')
                ->where(function($query) {
                    $query->where('s.merchant_id', $this->user->id)
                          ->whereOr('s.contact_id', $this->user->id)
                          ->whereOr('s.staff_id', $this->user->id)
                          ->whereOr(function($q) {
                              $q->where('p.user_id', $this->user->id)
                                ->where('p.user_type', 'supplier')
                                ->where('p.is_active', 1);
                          });
                })
                ->whereIn('s.id', $sessionIds)
                ->column('s.id');
                
            if (empty($validSessions)) {
                return $this->error('没有找到有效的会话或您没有权限删除这些会话');
            }
            
            // 开启事务
            Db::startTrans();
            try {
                // 删除会话关联的参与者记录
                Db::name('plugin_chat_session_participants')
                    ->whereIn('session_id', $validSessions)
                    ->where('user_id', $this->user->id)
                    ->delete();
                    
                // 如果用户是会话的创建者，则删除整个会话及相关消息
                $ownedSessionIds = Db::name('plugin_chat_sessions')
                    ->where(function($query) {
                        $query->where('merchant_id', $this->user->id)
                              ->whereOr('contact_id', $this->user->id);
                    })
                    ->whereIn('id', $validSessions)
                    ->column('id');
                    
                if (!empty($ownedSessionIds)) {
                    // 删除会话消息
                    Db::name('plugin_chat_messages')
                        ->whereIn('session_id', $ownedSessionIds)
                        ->delete();
                    
                    // 删除会话参与者
                    Db::name('plugin_chat_session_participants')
                        ->whereIn('session_id', $ownedSessionIds)
                        ->delete();
                    
                    // 删除会话
                    Db::name('plugin_chat_sessions')
                        ->whereIn('id', $ownedSessionIds)
                        ->delete();
                }
                
                Db::commit();
                return $this->success('删除成功', ['deleted_count' => count($validSessions)]);
            } catch (\Exception $e) {
                Db::rollback();
                Log::error('批量删除会话失败: ' . $e->getMessage());
                return $this->error('删除失败: ' . $e->getMessage());
            }
        } catch (\Exception $e) {
            Log::error('批量删除会话失败: ' . $e->getMessage());
            return $this->error('删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量删除已处理的邀请请求记录
     * @return \think\response\Json
     */
    public function batchDeleteProcessedJoinRequests()
    {
        try {
            // 确保用户已登录
            if (empty($this->user)) {
                return $this->error('请先登录');
            }
            
            $requestIds = $this->request->post('request_ids/a');
            if (empty($requestIds)) {
                return $this->error('请选择要删除的记录');
            }
            
            Log::info('批量删除已处理邀请请求, 用户ID='.$this->user->id.', 请求IDs='.json_encode($requestIds));
            
            // 获取当前商家信息
            $supplierInfo = $this->getCurrentSupplier();
            if (!$supplierInfo) {
                return $this->error('获取商家信息失败');
            }
            
            // 验证记录是否属于当前用户
            $validRequestIds = Db::name('plugin_chat_join_requests')
                ->where('target_id', $supplierInfo['id'])
                ->where('target_type', 'supplier')
                ->where('status', 'in', ['approved', 'rejected'])
                ->whereIn('id', $requestIds)
                ->column('id');
                
            if (empty($validRequestIds)) {
                return $this->error('没有找到有效的记录或您没有权限删除这些记录');
            }
            
            // 删除选中的已处理邀请记录
            $result = Db::name('plugin_chat_join_requests')
                ->whereIn('id', $validRequestIds)
                ->delete();
                
            Log::info('已删除用户ID='.$this->user->id.'的已处理邀请请求记录, 受影响行数: '.$result);
            
            return $this->success('已成功删除选中的记录', ['deleted_count' => $result]);
        } catch (\Exception $e) {
            Log::error('批量删除已处理邀请请求记录失败: ' . $e->getMessage());
            return $this->error('删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 判断用户的实际类型（merchant或supplier）
     * @param int $userId 用户ID
     * @return string 返回用户类型 merchant/supplier
     */
    private function getUserActualType($userId)
    {
        try {
            // 检查用户是否为商家（基本方式）
            $isShop = Db::name('user')
                ->where('id', $userId)
                ->where('custom_status', 1)  // custom_status=1表示正常商户
                ->count() > 0;

            if ($isShop) {
                return 'merchant';
            }

            // 检查是否在plugin_suppliers表中
            $tableExists = Db::query("SHOW TABLES LIKE '%plugin_suppliers'");
            if (!empty($tableExists)) {
                $supplier = Db::name('plugin_suppliers')
                    ->where('user_id', $userId)
                    ->find();
                if ($supplier) {
                    return 'supplier';
                }
            }

            // 检查是否有已通过的邀请记录作为supplier
            $hasApprovedInvite = Db::name('plugin_chat_join_requests')
                ->where('target_id', $userId)
                ->where('target_type', 'supplier')
                ->where('status', 'approved')
                ->count() > 0;

            if ($hasApprovedInvite) {
                return 'supplier';
            }

            // 检查是否是会话参与者
            $isSessionParticipant = Db::name('plugin_chat_session_participants')
                ->where('user_id', $userId)
                ->where('user_type', 'supplier')
                ->where('is_active', 1)
                ->count() > 0;

            if ($isSessionParticipant) {
                return 'supplier';
            }

            // 默认返回merchant
            return 'merchant';

        } catch (\Exception $e) {
            Log::error('判断用户类型失败: ' . $e->getMessage());
            return 'merchant'; // 默认返回merchant
        }
    }

    /**
     * 通过plugin_chat_join_requests表判断用户在特定会话中的正确角色类型
     * @param int $sessionId 会话ID
     * @param int $userId 用户ID
     * @return string|null 返回正确的角色类型(merchant/supplier)，如果无法确定则返回null
     */
    private function getUserRoleTypeFromJoinRequests($sessionId, $userId)
    {
        try {
            // 查找该用户在此会话中的加入请求记录
            $joinRequest = Db::name('plugin_chat_join_requests')
                ->where('session_id', $sessionId)
                ->where(function($query) use ($userId) {
                    $query->whereOr([
                        ['requester_id', '=', $userId],
                        ['target_id', '=', $userId]
                    ]);
                })
                ->where('status', 'approved')
                ->order('update_time DESC')
                ->find();

            if ($joinRequest) {
                // 如果用户是请求方
                if ($joinRequest['requester_id'] == $userId) {
                    // 根据请求方类型设置角色
                    if ($joinRequest['requester_type'] == 'merchant') {
                        return 'merchant';
                    } else if ($joinRequest['requester_type'] == 'supplier') {
                        return 'supplier';
                    }
                }
                // 如果用户是目标方
                else if ($joinRequest['target_id'] == $userId) {
                    // 根据目标方类型设置角色
                    if ($joinRequest['target_type'] == 'merchant') {
                        return 'merchant';
                    } else if ($joinRequest['target_type'] == 'supplier') {
                        return 'supplier';
                    }
                }
            }

            Log::info("未在join_requests表中找到用户{$userId}在会话{$sessionId}中的角色信息");
            return null;

        } catch (\Exception $e) {
            Log::error('通过join_requests表判断用户角色失败: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 增强版的获取供货商信息方法，包含邀请记录判断
     * @return array|bool 供货商信息或false
     */
    protected function getSupplierWithInvites()
    {
        $userId = $this->user->id;
        if (!$userId) {
            Log::error('获取商家信息失败: 用户ID为空');
            return false;
        }
        
        Log::info('尝试获取商家信息，用户ID: ' . $userId);
        
        try {
            // 首先检查plugin_suppliers表是否存在 - 使用不带前缀的表名
            $tableExists = Db::query("SHOW TABLES LIKE '%plugin_suppliers'");
            if (empty($tableExists)) {
                Log::info('plugin_suppliers表不存在，尝试通过其他方式判断商家身份');
                
                // 检查用户是否为商家（基本方式）
                $isShop = Db::name('user')
                    ->where('id', $userId)
                    ->where('custom_status', 1)  // custom_status=1表示正常商户
                    ->count() > 0;
                    
                // 检查用户是否有已处理且已通过的邀请记录
                $hasApprovedInvite = Db::name('plugin_chat_join_requests')
                    ->where('target_id', $userId)
                    ->where('target_type', 'supplier')
                    ->where('status', 'approved')
                    ->count() > 0;
                    
                // 检查用户是否是会话参与者
                $isSessionParticipant = Db::name('plugin_chat_session_participants')
                    ->where('user_id', $userId)
                    ->where('user_type', 'supplier')
                    ->where('is_active', 1)
                    ->count() > 0;
                
                // 如果用户是商家或有已通过的邀请或是会话参与者，则创建临时供应商记录
                if ($isShop || $hasApprovedInvite || $isSessionParticipant) {
                    Log::info('用户被识别为商家/供货商: 用户ID=' . $userId . 
                            ', 是商家=' . ($isShop ? '是' : '否') . 
                            ', 有已通过邀请=' . ($hasApprovedInvite ? '是' : '否') . 
                            ', 是会话参与者=' . ($isSessionParticipant ? '是' : '否'));
                    
                    return [
                        'id' => $userId, 
                        'user_id' => $userId,
                        'name' => $this->user->nickname ?: ('用户' . $userId)
                    ];
                } else {
                    Log::error('用户不是商家/供货商: 用户ID=' . $userId);
                    return false;
                }
            }
            
            $supplier = Db::name('plugin_suppliers')
                ->where('user_id', $userId)
                ->find();
                
            if (!$supplier) {
                Log::info('在plugin_suppliers表中未找到记录，尝试通过其他方式判断商家身份');
                
                // 检查用户是否为商家（基本方式）
                $isShop = Db::name('user')
                    ->where('id', $userId)
                    ->where('custom_status', 1)  // custom_status=1表示正常商户
                    ->count() > 0;
                    
                // 检查用户是否有已处理且已通过的邀请记录
                $hasApprovedInvite = Db::name('plugin_chat_join_requests')
                    ->where('target_id', $userId)
                    ->where('target_type', 'supplier')
                    ->where('status', 'approved')
                    ->count() > 0;
                    
                // 检查用户是否是会话参与者
                $isSessionParticipant = Db::name('plugin_chat_session_participants')
                    ->where('user_id', $userId)
                    ->where('user_type', 'supplier')
                    ->where('is_active', 1)
                    ->count() > 0;
                
                // 如果用户是商家或有已通过的邀请或是会话参与者，则创建临时供应商记录
                if ($isShop || $hasApprovedInvite || $isSessionParticipant) {
                    Log::info('用户被识别为商家/供货商: 用户ID=' . $userId . 
                            ', 是商家=' . ($isShop ? '是' : '否') . 
                            ', 有已通过邀请=' . ($hasApprovedInvite ? '是' : '否') . 
                            ', 是会话参与者=' . ($isSessionParticipant ? '是' : '否'));
                    
                    return [
                        'id' => $userId, 
                        'user_id' => $userId,
                        'name' => $this->user->nickname ?: ('用户' . $userId)
                    ];
                } else {
                    Log::error('用户不是商家/供货商: 用户ID=' . $userId);
                    return false;
                }
            }
            
            Log::info('成功获取商家信息: ' . json_encode($supplier));
            return $supplier;
        } catch (\Exception $e) {
            Log::error('获取商家信息异常: ' . $e->getMessage());
            // 返回一个临时的供应商记录
            return [
                'id' => $userId, 
                'user_id' => $userId,
                'name' => $this->user->nickname ?: ('用户' . $userId)
            ];
        }
    }

    /**
     * 获取所有预设回复
     * @return \think\response\Json
     */
    public function getPresetReplies()
    {
        // 获取当前商家ID
        $merchantId = $this->getCurrentUserId();
        if (!$merchantId) {
            return $this->error('未登录或权限不足');
        }

        // 从数据库中获取预设回复
        $presetReplies = Db::name('plugin_chat_preset_replies')
            ->where('merchant_id', $merchantId)
            ->order('create_time', 'desc')
            ->select()
            ->toArray();

        // 处理标签字段，转为数组
        foreach ($presetReplies as &$reply) {
            if (!empty($reply['tags'])) {
                $reply['tags'] = explode(',', $reply['tags']);
            } else {
                $reply['tags'] = [];
            }
        }

        return $this->success('获取成功', $presetReplies);
    }

    /**
     * 保存预设回复
     * @return \think\response\Json
     */
    public function savePresetReply()
    {
        // 获取当前商家ID
        $merchantId = $this->getCurrentUserId();
        if (!$merchantId) {
            return $this->error('未登录或权限不足');
        }

        // 获取请求参数
        $title = $this->request->post('title', '');
        $content = $this->request->post('content', '');
        $tags = $this->request->post('tags/a', []);
        $isQuick = $this->request->post('is_quick', 0);

        // 验证内容是否为空
        if (empty($content)) {
            return $this->error('回复内容不能为空');
        }

        // 处理标签，转为字符串保存
        $tagsStr = '';
        if (!empty($tags) && is_array($tags)) {
            $tagsStr = implode(',', $tags);
        }

        // 保存到数据库
        $data = [
            'merchant_id' => $merchantId,
            'title' => $title,
            'content' => $content,
            'tags' => $tagsStr,
            'is_quick' => $isQuick ? 1 : 0,
            'create_time' => time(),
            'update_time' => time()
        ];

        // 插入数据
        $result = Db::name('plugin_chat_preset_replies')->insert($data);
        if (!$result) {
            return $this->error('保存失败');
        }

        return $this->success('保存成功');
    }

    /**
     * 更新预设回复
     * @return \think\response\Json
     */
    public function updatePresetReply()
    {
        // 获取当前商家ID
        $merchantId = $this->getCurrentUserId();
        if (!$merchantId) {
            return $this->error('未登录或权限不足');
        }

        // 获取请求参数
        $id = $this->request->post('id', 0);
        $title = $this->request->post('title', '');
        $content = $this->request->post('content', '');
        $tags = $this->request->post('tags/a', []);
        $isQuick = $this->request->post('is_quick', 0);

        // 验证参数
        if (empty($id)) {
            return $this->error('参数错误');
        }

        if (empty($content)) {
            return $this->error('回复内容不能为空');
        }

        // 检查记录是否存在且属于当前商家
        $exists = Db::name('plugin_chat_preset_replies')
            ->where('id', $id)
            ->where('merchant_id', $merchantId)
            ->find();

        if (!$exists) {
            return $this->error('记录不存在或无权限修改');
        }

        // 处理标签，转为字符串保存
        $tagsStr = '';
        if (!empty($tags) && is_array($tags)) {
            $tagsStr = implode(',', $tags);
        }

        // 更新数据
        $data = [
            'title' => $title,
            'content' => $content,
            'tags' => $tagsStr,
            'is_quick' => $isQuick ? 1 : 0,
            'update_time' => time()
        ];

        // 执行更新
        $result = Db::name('plugin_chat_preset_replies')
            ->where('id', $id)
            ->where('merchant_id', $merchantId)
            ->update($data);

        if ($result === false) {
            return $this->error('更新失败');
        }

        return $this->success('更新成功');
    }

    /**
     * 删除预设回复
     * @return \think\response\Json
     */
    public function deletePresetReply()
    {
        // 获取当前商家ID
        $merchantId = $this->getCurrentUserId();
        if (!$merchantId) {
            return $this->error('未登录或权限不足');
        }

        // 获取预设回复ID
        $replyId = $this->request->post('id', 0);
        if (!$replyId) {
            return $this->error('参数错误');
        }

        // 确保只能删除自己的预设回复
        $exists = Db::name('plugin_chat_preset_replies')
            ->where('id', $replyId)
            ->where('merchant_id', $merchantId)
            ->find();

        if (!$exists) {
            return $this->error('预设回复不存在或无权限删除');
        }

        // 执行删除
        $result = Db::name('plugin_chat_preset_replies')
            ->where('id', $replyId)
            ->where('merchant_id', $merchantId)
            ->delete();

        if (!$result) {
            return $this->error('删除失败');
        }

        return $this->success('删除成功');
    }

    /**
     * 切换快捷回复状态
     * @return \think\response\Json
     */
    public function toggleQuickReply()
    {
        // 获取当前商家ID
        $merchantId = $this->getCurrentUserId();
        if (!$merchantId) {
            return $this->error('未登录或权限不足');
        }

        // 获取预设回复ID和状态
        $replyId = $this->request->post('id', 0);
        $isQuick = $this->request->post('is_quick', 0);

        if (!$replyId) {
            return $this->error('参数错误');
        }

        // 确保只能修改自己的预设回复
        $exists = Db::name('plugin_chat_preset_replies')
            ->where('id', $replyId)
            ->where('merchant_id', $merchantId)
            ->find();

        if (!$exists) {
            return $this->error('预设回复不存在或无权限修改');
        }

        // 执行修改
        $result = Db::name('plugin_chat_preset_replies')
            ->where('id', $replyId)
            ->where('merchant_id', $merchantId)
            ->update([
                'is_quick' => $isQuick ? 1 : 0,
                'update_time' => time()
            ]);

        if (!$result) {
            return $this->error('更新失败');
        }

        return $this->success('更新成功');
    }

    /**
     * 辅助方法：获取当前用户ID
     * @return int|null
     */
    protected function getCurrentUserId()
    {
        if (empty($this->user)) {
            return null;
        }
        return $this->user->id;
    }

    /**
     * 获取预设问题配置
     * @return \think\response\Json
     */
    public function getPresetQuestionsConfig()
    {
        try {
            // 确保用户已登录
            if (empty($this->user)) {
                return $this->error('请先登录');
            }
            
            // 查询商家的设置记录
            $settings = Db::name('plugin_chat_settings')
                ->where('user_id', $this->user->id)
                ->find();
                
            if (empty($settings) || empty($settings['settings'])) {
                // 返回默认设置
                return $this->success('获取成功', [
                    'autoSendPresetQuestions' => true,
                    'presetQuestions' => [
                        'title' => '', // 空标题，让用户自定义
                        'description' => '', // 空描述，让用户自定义
                        'questions' => [
                            ['question' => '请输入您的问题1', 'answer' => '这是问题1的回复内容'],
                            ['question' => '请输入您的问题2', 'answer' => '这是问题2的回复内容'],
                            ['question' => '请输入您的问题3', 'answer' => '这是问题3的回复内容']
                        ]
                    ]
                ]);
            }
            
            // 解析JSON设置
            $settingsData = json_decode($settings['settings'], true) ?: [];
            
            // 获取预设问题配置或使用默认值
            $presetConfig = [
                'autoSendPresetQuestions' => $settingsData['autoSendPresetQuestions'] ?? true,
                'presetQuestions' => $settingsData['presetQuestions'] ?? [
                    'title' => '', // 空标题，让用户自定义
                    'description' => '', // 空描述，让用户自定义
                    'questions' => [
                        ['question' => '请输入您的问题1', 'answer' => '这是问题1的回复内容'],
                        ['question' => '请输入您的问题2', 'answer' => '这是问题2的回复内容'],
                        ['question' => '请输入您的问题3', 'answer' => '这是问题3的回复内容']
                    ]
                ]
            ];
            
            // 兼容旧格式数据 - 如果questions是字符串数组而不是对象数组
            if (isset($presetConfig['presetQuestions']['questions']) && 
                !empty($presetConfig['presetQuestions']['questions']) && 
                is_array($presetConfig['presetQuestions']['questions']) && 
                !isset($presetConfig['presetQuestions']['questions'][0]['question'])) {
                
                $oldQuestions = $presetConfig['presetQuestions']['questions'];
                $newQuestions = [];
                
                foreach ($oldQuestions as $question) {
                    $newQuestions[] = [
                        'question' => $question,
                        'answer' => ''
                    ];
                }
                
                $presetConfig['presetQuestions']['questions'] = $newQuestions;
            }
            
            return $this->success('获取成功', $presetConfig);
        } catch (\Exception $e) {
            Log::error('获取预设问题配置失败: ' . $e->getMessage());
            return $this->error('获取预设问题配置失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 保存预设问题配置
     * @return \think\response\Json
     */
    public function savePresetQuestionsConfig()
    {
        try {
            // 确保用户已登录
            if (empty($this->user)) {
                return $this->error('请先登录');
            }
            
            // 获取请求参数
            $autoSend = $this->request->post('autoSendPresetQuestions', true);
            $title = $this->request->post('title', '');
            $description = $this->request->post('description', '');
            $questions = $this->request->post('questions/a', []);
            
            if (empty($questions)) {
                $questions = [
                    ['question' => '请输入您的问题1', 'answer' => '这是问题1的回复内容'],
                    ['question' => '请输入您的问题2', 'answer' => '这是问题2的回复内容'],
                    ['question' => '请输入您的问题3', 'answer' => '这是问题3的回复内容']
                ];
            }
            
            // 查询商家的设置记录
            $settings = Db::name('plugin_chat_settings')
                ->where('user_id', $this->user->id)
                ->find();
                
            // 构建预设问题配置
            $presetConfig = [
                'autoSendPresetQuestions' => $autoSend ? true : false,
                'presetQuestions' => [
                    'title' => $title,
                    'description' => $description,
                    'questions' => $questions
                ]
            ];
            
            if (empty($settings)) {
                // 创建新设置记录
                $saveData = [
                    'user_id' => $this->user->id,
                    'settings' => json_encode([
                        'autoSendPresetQuestions' => $presetConfig['autoSendPresetQuestions'],
                        'presetQuestions' => $presetConfig['presetQuestions']
                    ], JSON_UNESCAPED_UNICODE),
                    'create_time' => time(),
                    'update_time' => time()
                ];
                
                // 检查表是否存在
                try {
                    $tableExists = Db::query("SHOW TABLES LIKE 'plugin_chat_settings'");
                    if (empty($tableExists)) {
                        // 创建表
                        Db::execute("CREATE TABLE `plugin_chat_settings` (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
                            `user_id` int(11) NOT NULL COMMENT '商家ID',
                            `settings` text COLLATE utf8mb4_unicode_ci COMMENT '设置JSON',
                            `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
                            `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
                            PRIMARY KEY (`id`),
                            UNIQUE KEY `user_id` (`user_id`)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商家消息设置表';");
                    }
                } catch (\Exception $e) {
                    Log::error('检查或创建设置表失败: ' . $e->getMessage());
                }
                
                Db::name('plugin_chat_settings')->insert($saveData);
            } else {
                // 更新设置
                $settingsData = json_decode($settings['settings'], true) ?: [];
                
                // 合并新设置
                $settingsData['autoSendPresetQuestions'] = $presetConfig['autoSendPresetQuestions'];
                $settingsData['presetQuestions'] = $presetConfig['presetQuestions'];
                
                // 保存
                Db::name('plugin_chat_settings')
                    ->where('id', $settings['id'])
                    ->update([
                        'settings' => json_encode($settingsData, JSON_UNESCAPED_UNICODE),
                        'update_time' => time()
                    ]);
            }
            
            return $this->success('保存成功', $presetConfig);
        } catch (\Exception $e) {
            Log::error('保存预设问题配置失败: ' . $e->getMessage());
            return $this->error('保存预设问题配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 发送预设问题列表
     * @return \think\response\Json
     */
    public function sendPresetQuestions()
    {
        try {
            // 确保用户已登录
            if (empty($this->user)) {
                return $this->error('请先登录');
            }
            
            // 获取参数
            $sessionId = $this->request->post('session_id/d', 0);
            $title = $this->request->post('title', null);
            $description = $this->request->post('description', null);
            $questions = $this->request->post('questions/a', []);
            
            if (empty($sessionId)) {
                return $this->error('会话ID不能为空');
            }
            
            // 获取会话信息
            $session = Db::name('plugin_chat_sessions')->where('id', $sessionId)->find();
            if (!$session) {
                return $this->error('会话不存在');
            }
            
            // 根据会话source类型选择不同的预设问题内容
            if (isset($session['source']) && $session['source'] == 'customer') {
                // 平台客服会话，使用选中的内容（系统配置）
                $params = $this->getParamsData();
                
                if (isset($params['preset_replies']) && !empty($params['preset_replies']['items'])) {
                    // 使用系统配置中的标题和描述，如果没有则使用空字符串
                    $title = isset($params['preset_questions']['title']) && !empty($params['preset_questions']['title']) 
                        ? $params['preset_questions']['title'] 
                        : '';
                    
                    $description = isset($params['preset_questions']['description']) && !empty($params['preset_questions']['description']) 
                        ? $params['preset_questions']['description'] 
                        : '';
                    
                    $questions = [];
                    foreach ($params['preset_replies']['items'] as $index => $item) {
                        $number = $index + 1;
                        $questions[] = [
                            'question' => "{$number}. {$item['label']}",
                            'answer' => $item['content'] ?? ''
                        ];
                    }
                }
            } else {
                // 商家会话，使用user里面的预设问题
                // 如果没有提供参数，尝试从用户设置中获取
                if ($title === null || $description === null || empty($questions)) {
                    $settings = Db::name('plugin_chat_settings')
                        ->where('user_id', $this->user->id)
                        ->find();
                        
                    if (!empty($settings) && !empty($settings['settings'])) {
                        $settingsData = json_decode($settings['settings'], true) ?: [];
                        
                        if (!empty($settingsData['presetQuestions'])) {
                            $presetQuestions = $settingsData['presetQuestions'];
                            
                            if ($title === null) {
                                $title = $presetQuestions['title'] ?? '';
                            }
                            
                            if ($description === null) {
                                $description = $presetQuestions['description'] ?? '';
                            }
                            
                            if (empty($questions) && !empty($presetQuestions['questions'])) {
                                $questions = $presetQuestions['questions'];
                            }
                        }
                    }
                    
                    // 如果仍然没有值，使用默认空值或简单问题
                    if ($title === null) {
                        $title = '';
                    }
                    
                    if ($description === null) {
                        $description = '';
                    }
                    
                    if (empty($questions)) {
                        $questions = [
                            ['question' => '请输入您的问题1', 'answer' => '这是问题1的回复内容'],
                            ['question' => '请输入您的问题2', 'answer' => '这是问题2的回复内容'],
                            ['question' => '请输入您的问题3', 'answer' => '这是问题3的回复内容']
                        ];
                    }
                    
                    // 兼容旧格式数据 - 如果questions是字符串数组而不是对象数组
                    if (!empty($questions) && is_array($questions) && !isset($questions[0]['question'])) {
                        $oldQuestions = $questions;
                        $questions = [];
                        
                        foreach ($oldQuestions as $question) {
                            $questions[] = [
                                'question' => $question,
                                'answer' => ''
                            ];
                        }
                    }
                }
            }
            
            // 验证权限 - 判断当前用户是否有权在此会话发送消息
            $hasPermission = false;
            
            // 检查用户是否为商家
            $isShop = Db::name('user')
                ->where('id', $this->user->id)
                ->where('custom_status', 1)  // custom_status=1表示正常商户
                ->count() > 0;
                
            // 如果是商家，根据会话类型授权
            if ($isShop) {
                if (isset($session['source']) && $session['source'] == 'customer') {
                    // 平台客服会话
                    $hasPermission = true;
                    $senderType = 'staff';
                    $roleType = 'merchant';
                    Log::info('商家发送预设问题到客服会话，会话ID: ' . $sessionId);
                } else if (isset($session['source']) && $session['source'] == 'merchant') {
                    // 商家会话
                    $hasPermission = true;
                    $senderType = 'staff';
                    $roleType = 'merchant';
                    Log::info('商家发送预设问题到商家会话，会话ID: ' . $sessionId);
                } else if (isset($session['merchant_id']) && $session['merchant_id'] == $this->user->id) {
                    // 商家专属会话
                    $hasPermission = true;
                    $senderType = 'staff';
                    $roleType = 'merchant';
                    Log::info('商家发送预设问题到商家专属会话，会话ID: ' . $sessionId);
                }
            }
            
            // 还需要检查用户是否是会话参与者
            if (!$hasPermission) {
                $isParticipant = Db::name('plugin_chat_session_participants')
                    ->where('session_id', $sessionId)
                    ->where('user_id', $this->user->id)
                    ->where('is_active', 1)
                    ->count() > 0;
                    
                if ($isParticipant) {
                    $hasPermission = true;
                    $senderType = 'staff';
                    $roleType = 'merchant';
                    Log::info('会话参与者发送预设问题，会话ID: ' . $sessionId);
                }
            }
            
            if (!$hasPermission) {
                return $this->error('您无权在此会话中发送消息');
            }
            
            // 构建预设问题HTML
            $titleHtml = !empty($title) ? '<h4 class="preset-title">' . htmlspecialchars($title) . '</h4>' : '';
            $descHtml = !empty($description) ? '<p class="preset-description">' . nl2br(htmlspecialchars($description)) . '</p>' : '';
            
            $questionList = '';
            foreach ($questions as $item) {
                $question = is_array($item) ? ($item['question'] ?? '') : $item;
                $answer = is_array($item) ? ($item['answer'] ?? '') : '';
                $imageUrl = is_array($item) ? ($item['image_url'] ?? '') : '';
                $fileUrl = is_array($item) ? ($item['file_url'] ?? '') : '';
                $fileName = is_array($item) ? ($item['file_name'] ?? '') : '';

                // 构建数据属性
                $dataAttrs = '';
                if (!empty($answer)) {
                    $dataAttrs .= ' data-text-content="' . htmlspecialchars($answer, ENT_QUOTES) . '"';
                }
                if (!empty($imageUrl)) {
                    $dataAttrs .= ' data-image-url="' . htmlspecialchars($imageUrl, ENT_QUOTES) . '"';
                }
                if (!empty($fileUrl)) {
                    $dataAttrs .= ' data-file-url="' . htmlspecialchars($fileUrl, ENT_QUOTES) . '"';
                    $dataAttrs .= ' data-file-name="' . htmlspecialchars($fileName, ENT_QUOTES) . '"';
                }

                // 检查问题是否为图片URL
                $urlPattern = '/^https?:\/\/[^\s]+\.(jpg|jpeg|png|gif|bmp|webp)(\?.*)?$/i';
                if (preg_match($urlPattern, $question)) {
                    // 如果问题是图片URL，将其转换为图片HTML，不显示URL文本
                    $questionList .= '<div class="preset-question-item preset-question-image"' . $dataAttrs . '>
                        <img src="' . htmlspecialchars($question, ENT_QUOTES) . '" class="preset-image" alt="预设图片">
                        </div>';
                } else {
                    // 正常文本问题
                    $questionList .= '<div class="preset-question-item"' . $dataAttrs . '>'
                        . htmlspecialchars($question) . '</div>';
                }
            }
            
            // 发送预设问题列表，包含标题和描述
            $messageId = Db::name('plugin_chat_messages')->insertGetId([
                'session_id' => $sessionId,
                'sender_type' => $senderType,
                'sender_id' => $this->user->id, // 使用当前商家ID
                'message' => '<div class="preset-questions">' . $titleHtml . $descHtml . $questionList . '</div>',
                'message_type' => 'text',
                'role_type' => $roleType,
                'is_read' => 0,
                'create_time' => time(),
                'update_time' => time(),
            ]);
            
            if (!$messageId) {
                return $this->error('发送预设问题失败');
            }
            
            // 更新会话的最后消息
            Db::name('plugin_chat_sessions')
                ->where('id', $sessionId)
                ->update([
                    'last_message' => '[预设问题列表]',
                    'last_time' => time(),
                    'update_time' => time(),
                    'unread_count' => Db::raw('unread_count + 1')
                ]);
                
            // 获取完整消息
            $message = Db::name('plugin_chat_messages')
                ->where('id', $messageId)
                ->find();
                
            return $this->success('发送成功', $message);
        } catch (\Exception $e) {
            Log::error('发送预设问题失败: ' . $e->getMessage());
            return $this->error('发送预设问题失败: ' . $e->getMessage());
        }
    }

    /**
     * 检查并自动发送预设问题
     * @param int $sessionId 会话ID
     * @return bool 是否发送了预设问题
     */
    public function checkAndSendPresetQuestions($sessionId)
    {
        try {
            // 确保用户已登录
            if (empty($this->user)) {
                return false;
            }
            
            // 首先检查会话是否存在
            $session = Db::name('plugin_chat_sessions')
                ->where('id', $sessionId)
                ->find();
                
            if (!$session) {
                return false;
            }
            
            // 检查该会话是否已经发送过预设问题
            $hasPresetQuestions = Db::name('plugin_chat_messages')
                ->where('session_id', $sessionId)
                ->where('sender_id', $this->user->id)
                ->where('message', 'like', '%class="preset-questions"%')
                ->find();
                
            if ($hasPresetQuestions) {
                // 已经发送过预设问题，不再重复发送
                return false;
            }
            
            // 根据会话source类型选择不同的预设问题内容
            $title = '';
            $description = '';
            $questions = [];
            
            if (isset($session['source']) && $session['source'] == 'customer') {
                // 平台客服会话，使用选中的内容（系统配置）
                $params = $this->getParamsData();
                
                if (isset($params['preset_replies']) && !empty($params['preset_replies']['items'])) {
                    $title = isset($params['preset_questions']['title']) && !empty($params['preset_questions']['title']) 
                        ? $params['preset_questions']['title'] 
                        : '';
                    
                    $description = isset($params['preset_questions']['description']) && !empty($params['preset_questions']['description']) 
                        ? $params['preset_questions']['description'] 
                        : '';
                    
                    $questions = [];
                    foreach ($params['preset_replies']['items'] as $index => $item) {
                        $number = $index + 1;
                        $questions[] = [
                            'question' => "{$number}. {$item['label']}",
                            'answer' => $item['content'] ?? ''
                        ];
                    }
                    
                    if (empty($questions)) {
                        return false; // 如果没有预设问题，则不发送
                    }
                } else {
                    return false; // 没有配置预设回复，不发送
                }
            } else {
                // 商家会话，使用user里面的预设问题
                // 获取商家的预设问题设置
                $settings = Db::name('plugin_chat_settings')
                    ->where('user_id', $this->user->id)
                    ->find();
                    
                // 如果没有设置或未启用自动发送，则不发送
                if (empty($settings) || empty($settings['settings'])) {
                    $settingsData = [
                        'autoSendPresetQuestions' => true,
                        'presetQuestions' => [
                            'title' => '', // 空标题，让用户自定义
                            'description' => '', // 空描述，让用户自定义
                            'questions' => [
                                ['question' => '请输入您的问题1', 'answer' => '这是问题1的回复内容'],
                                ['question' => '请输入您的问题2', 'answer' => '这是问题2的回复内容'],
                                ['question' => '请输入您的问题3', 'answer' => '这是问题3的回复内容']
                            ]
                        ]
                    ];
                } else {
                    $settingsData = json_decode($settings['settings'], true) ?: [];
                }
                
                // 检查是否启用了自动发送预设问题
                if (!isset($settingsData['autoSendPresetQuestions']) || $settingsData['autoSendPresetQuestions'] !== true) {
                    return false;
                }
                
                // 准备预设问题数据
                $presetQuestions = $settingsData['presetQuestions'] ?? [
                    'title' => '', // 空标题，让用户自定义
                    'description' => '', // 空描述，让用户自定义
                    'questions' => [
                        ['question' => '请输入您的问题1', 'answer' => '这是问题1的回复内容'],
                        ['question' => '请输入您的问题2', 'answer' => '这是问题2的回复内容'],
                        ['question' => '请输入您的问题3', 'answer' => '这是问题3的回复内容']
                    ]
                ];
                
                $title = $presetQuestions['title'] ?? '';
                $description = $presetQuestions['description'] ?? '';
                $questions = $presetQuestions['questions'] ?? [
                    ['question' => '请输入您的问题1', 'answer' => '这是问题1的回复内容'],
                    ['question' => '请输入您的问题2', 'answer' => '这是问题2的回复内容'],
                    ['question' => '请输入您的问题3', 'answer' => '这是问题3的回复内容']
                ];
                
                // 兼容旧格式数据 - 如果questions是字符串数组而不是对象数组
                if (!empty($questions) && is_array($questions) && !isset($questions[0]['question'])) {
                    $oldQuestions = $questions;
                    $questions = [];
                    
                    foreach ($oldQuestions as $question) {
                        $questions[] = [
                            'question' => $question,
                            'answer' => ''
                        ];
                    }
                }
            }
            
            // 确定发送者类型和角色
            $senderType = 'staff'; // 必须使用'staff'，因为数据库sender_type字段只接受'staff'和'customer'
            $roleType = 'merchant';
            
            // 构建预设问题HTML
            $titleHtml = !empty($title) ? '<h4 class="preset-title">' . htmlspecialchars($title) . '</h4>' : '';
            $descHtml = !empty($description) ? '<p class="preset-description">' . nl2br(htmlspecialchars($description)) . '</p>' : '';
            
            $questionList = '';
            foreach ($questions as $item) {
                $question = is_array($item) ? ($item['question'] ?? '') : $item;
                $answer = is_array($item) ? ($item['answer'] ?? '') : '';
                $imageUrl = is_array($item) ? ($item['image_url'] ?? '') : '';
                $fileUrl = is_array($item) ? ($item['file_url'] ?? '') : '';
                $fileName = is_array($item) ? ($item['file_name'] ?? '') : '';

                // 构建数据属性
                $dataAttrs = '';
                if (!empty($answer)) {
                    $dataAttrs .= ' data-text-content="' . htmlspecialchars($answer, ENT_QUOTES) . '"';
                }
                if (!empty($imageUrl)) {
                    $dataAttrs .= ' data-image-url="' . htmlspecialchars($imageUrl, ENT_QUOTES) . '"';
                }
                if (!empty($fileUrl)) {
                    $dataAttrs .= ' data-file-url="' . htmlspecialchars($fileUrl, ENT_QUOTES) . '"';
                    $dataAttrs .= ' data-file-name="' . htmlspecialchars($fileName, ENT_QUOTES) . '"';
                }

                // 对不同类型的回复内容进行不同处理
                $questionList .= '<div class="preset-question-item"' . $dataAttrs . '>'
                    . htmlspecialchars($question) . '</div>';
            }
            
            // 发送预设问题列表，包含标题和描述
            $messageId = Db::name('plugin_chat_messages')->insertGetId([
                'session_id' => $sessionId,
                'sender_type' => $senderType,
                'sender_id' => $this->user->id,
                'message' => '<div class="preset-questions">' . $titleHtml . $descHtml . $questionList . '</div>',
                'message_type' => 'text',
                'role_type' => $roleType,
                'is_read' => 0,
                'create_time' => time(),
                'update_time' => time(),
            ]);
            
            if (!$messageId) {
                return false;
            }
            
            // 更新会话的最后消息
            Db::name('plugin_chat_sessions')
                ->where('id', $sessionId)
                ->update([
                    'last_message' => '[预设问题列表]',
                    'last_time' => time(),
                    'update_time' => time(),
                    'unread_count' => Db::raw('unread_count + 1')
                ]);
                
            return true;
        } catch (\Exception $e) {
            Log::error('自动发送预设问题失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取参数配置数据
     * @return array
     */
    protected function getParamsData()
    {
        try {
            // 从数据库中获取配置参数
            $params = Db::name('plugin_chat_params')->where('id', 1)->value('params');
            if ($params) {
                return json_decode($params, true) ?: [];
            }
            return [];
        } catch (\Exception $e) {
            Log::error('获取参数配置失败: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取客户设置
     * @return \think\response\Json
     */
    public function getCustomerSettings()
    {
        try {
            $key = $this->request->param('key', '', 'trim');
            
            if (empty($key)) {
                return $this->error('参数错误：缺少key');
            }
            
            $setting = Db::name('plugin_chat_customer_settings')
                ->where('key', $key)
                ->find();
                
            if (!$setting) {
                return $this->error('设置不存在');
            }
            
            return $this->success('获取成功', $setting);
        } catch (\Exception $e) {
            Log::error('获取客户设置失败: ' . $e->getMessage());
            return $this->error('获取客户设置失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取邀请过期时间设置
     * @return \think\response\Json
     */
    public function getInvitationSettings()
    {
        try {
            // 获取过期值
            $valueSetting = Db::name('plugin_chat_customer_settings')
                ->where('key', 'invitation_expiry_value')
                ->find();
                
            // 获取过期单位
            $unitSetting = Db::name('plugin_chat_customer_settings')
                ->where('key', 'invitation_expiry_unit')
                ->find();
                
            // 默认设置
            $settings = [
                'value' => 48,
                'unit' => 'hour'
            ];
            
            // 设置值
            if ($valueSetting && isset($valueSetting['value'])) {
                $settings['value'] = intval($valueSetting['value']);
            }
            
            // 设置单位
            if ($unitSetting && isset($unitSetting['value'])) {
                $settings['unit'] = $unitSetting['value'];
            }
            
            // 确保单位有效
            if (!in_array($settings['unit'], ['minute', 'hour', 'day'])) {
                $settings['unit'] = 'hour';
            }
            
            return $this->success('获取成功', $settings);
        } catch (\Exception $e) {
            Log::error('获取邀请设置失败: ' . $e->getMessage());
            return $this->error('获取邀请设置失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 保存邀请过期时间设置
     * @return \think\response\Json
     */
    public function saveInvitationSettings()
    {
        try {
            // 获取设置数据
            $value = $this->request->post('value', 48, 'intval');
            $unit = $this->request->post('unit', 'hour', 'trim');
            
            // 验证数据
            if ($value <= 0) {
                return $this->error('过期时间值必须大于0');
            }
            
            if (!in_array($unit, ['minute', 'hour', 'day'])) {
                return $this->error('时间单位无效，必须是minute、hour或day');
            }
            
            // 更新或插入设置
            $currentTime = time();
            
            // 更新值设置
            $valueExists = Db::name('plugin_chat_customer_settings')
                ->where('key', 'invitation_expiry_value')
                ->find();
                
            if ($valueExists) {
                Db::name('plugin_chat_customer_settings')
                    ->where('key', 'invitation_expiry_value')
                    ->update([
                        'value' => $value,
                        'update_time' => $currentTime
                    ]);
            } else {
                Db::name('plugin_chat_customer_settings')
                    ->insert([
                        'key' => 'invitation_expiry_value',
                        'value' => $value,
                        'type' => 'integer',
                        'description' => '邀请过期时间值',
                        'create_time' => $currentTime,
                        'update_time' => $currentTime
                    ]);
            }
            
            // 更新单位设置
            $unitExists = Db::name('plugin_chat_customer_settings')
                ->where('key', 'invitation_expiry_unit')
                ->find();
                
            if ($unitExists) {
                Db::name('plugin_chat_customer_settings')
                    ->where('key', 'invitation_expiry_unit')
                    ->update([
                        'value' => $unit,
                        'update_time' => $currentTime
                    ]);
            } else {
                Db::name('plugin_chat_customer_settings')
                    ->insert([
                        'key' => 'invitation_expiry_unit',
                        'value' => $unit,
                        'type' => 'string',
                        'description' => '邀请过期时间单位(minute/hour/day)',
                        'create_time' => $currentTime,
                        'update_time' => $currentTime
                    ]);
            }
            
            // 更新兼容性设置 invitation_expiry_hours
            // 根据单位转换为小时
            $hoursValue = $value;
            switch ($unit) {
                case 'minute':
                    $hoursValue = $value / 60;
                    break;
                case 'day':
                    $hoursValue = $value * 24;
                    break;
            }
            
            $hoursExists = Db::name('plugin_chat_customer_settings')
                ->where('key', 'invitation_expiry_hours')
                ->find();
                
            if ($hoursExists) {
                Db::name('plugin_chat_customer_settings')
                    ->where('key', 'invitation_expiry_hours')
                    ->update([
                        'value' => $hoursValue,
                        'update_time' => $currentTime
                    ]);
            } else {
                Db::name('plugin_chat_customer_settings')
                    ->insert([
                        'key' => 'invitation_expiry_hours',
                        'value' => $hoursValue,
                        'type' => 'integer',
                        'description' => '商家邀请超时时间(小时)',
                        'create_time' => $currentTime,
                        'update_time' => $currentTime
                    ]);
            }
            
            return $this->success('保存成功');
        } catch (\Exception $e) {
            Log::error('保存邀请设置失败: ' . $e->getMessage());
            return $this->error('保存邀请设置失败: ' . $e->getMessage());
        }
    }

    /**
     * 主动检查并处理过期的邀请请求
     * @return \think\response\Json
     */
    public function checkExpiredInvitations()
    {
        try {
            // 强制检查所有邀请请求
            $result = $this->closeExpiredInvitations(true);
            
            if ($result) {
                return $this->success('已成功处理过期邀请请求');
            } else {
                return $this->success('没有发现过期邀请请求');
            }
        } catch (\Exception $e) {
            Log::error('主动检查过期邀请请求失败: ' . $e->getMessage());
            return $this->error('检查过期邀请请求失败: ' . $e->getMessage());
        }
    }

    /**
     * 根据会话标题匹配联系人并发送邮箱验证
     * @return \think\response\Json
     */
    public function sendEmailVerification()
    {
        try {
            // 确保用户已登录
            if (empty($this->user)) {
                return $this->error('请先登录');
            }
            
            // 获取商家ID
            $merchantId = $this->user->id;
            
            // 查询与当前商家相关的所有会话
            $sessions = Db::name('plugin_chat_sessions')
                ->where('merchant_id', $merchantId)
                ->select()
                ->toArray();
                
            if (empty($sessions)) {
                return $this->error('未找到与您相关的会话');
            }
            
            $matchedContacts = [];
            
            // 遍历会话，查找包含"客户咨询:"的标题
            foreach ($sessions as $session) {
                // 记录当前处理的会话信息，方便调试
                Log::info('处理会话: ID=' . $session['id'] . ', 标题=' . $session['title']);
                
                // 检查标题是否包含"客户咨询:"
                if (strpos($session['title'], '客户咨询:') !== false) {
                    // 提取标题中的名字部分
                    $titleParts = explode('客户咨询:', $session['title']);
                    if (isset($titleParts[1])) {
                        // 从后半部分提取名字，可能包含空格和其他字符
                        $namePart = trim($titleParts[1]);
                        Log::info('原始名称部分: ' . $namePart);
                        
                        // 先处理可能的联系商家部分，去掉(联系商家:xxx)
                        if (preg_match('/\(联系商家:.+\)/', $namePart, $matches)) {
                            $namePart = trim(str_replace($matches[0], '', $namePart));
                            Log::info('去除联系商家后: ' . $namePart);
                        }
                        
                        // 如果包含【，截取到【前的部分作为名字
                        if (strpos($namePart, '【') !== false) {
                            $namePart = trim(explode('【', $namePart)[0]);
                            Log::info('去除【】后: ' . $namePart);
                        }
                        
                        // 如果包含[，截取到[前的部分作为名字
                        if (strpos($namePart, '[') !== false) {
                            $namePart = trim(explode('[', $namePart)[0]);
                            Log::info('去除[]后: ' . $namePart);
                        }
                        
                        // 如果包含(，截取到(前的部分作为名字
                        if (strpos($namePart, '(') !== false) {
                            $namePart = trim(explode('(', $namePart)[0]);
                            Log::info('去除()后: ' . $namePart);
                        }
                        
                        // 最后再按空格分割，但只有当空格不是第一个字符时才分割
                        if (strpos(trim($namePart), ' ') > 0) {
                            $nameParts = explode(' ', trim($namePart));
                            $nameInTitle = trim($nameParts[0]);
                        } else {
                            $nameInTitle = trim($namePart);
                        }
                        
                        Log::info('最终提取的名字: ' . $nameInTitle);
                        
                        // 查找对应的联系人
                        $contact = Db::name('plugin_chat_contacts')
                            ->where('id', $session['contact_id'])
                            ->find();
                        
                        if ($contact) {
                            Log::info('找到联系人: ID=' . $contact['id'] . ', 名字=' . $contact['name'] . ', 邮箱=' . ($contact['email'] ?? '无'));
                            
                            // 使用更宽松的匹配方式 - 名字是否相等或包含关系
                            $nameMatched = false;
                            
                            // 完全相等
                            if (trim($contact['name']) === $nameInTitle) {
                                $nameMatched = true;
                                Log::info('名字完全匹配');
                            } 
                            // 联系人名字包含标题名字
                            else if (strpos(trim(strtolower($contact['name'])), strtolower($nameInTitle)) !== false) {
                                $nameMatched = true;
                                Log::info('联系人名字包含标题名字');
                            } 
                            // 标题名字包含联系人名字
                            else if (strpos(strtolower($nameInTitle), strtolower(trim($contact['name']))) !== false) {
                                $nameMatched = true;
                                Log::info('标题名字包含联系人名字');
                            }
                            // 额外处理：如果名字很短（少于等于2个字符），需要完全匹配
                            else if (mb_strlen($nameInTitle, 'UTF-8') <= 2 && mb_strlen(trim($contact['name']), 'UTF-8') <= 2) {
                                $nameMatched = ($nameInTitle === trim($contact['name']));
                                Log::info('短名字比较: ' . ($nameMatched ? '匹配' : '不匹配'));
                            }
                            
                            // 如果找到联系人并且名字匹配，并且有邮箱
                            if ($nameMatched && !empty($contact['email'])) {
                                Log::info('名字匹配成功，添加到匹配列表');
                                $matchedContacts[] = [
                                    'session_id' => $session['id'],
                                    'contact_id' => $contact['id'],
                                    'name' => $contact['name'],
                                    'email' => $contact['email'],
                                    'title' => $session['title']
                                ];
                            } else {
                                if (!$nameMatched) {
                                    Log::info('名字不匹配: 标题中名字=' . $nameInTitle . ', 联系人名字=' . $contact['name']);
                                }
                                if (empty($contact['email'])) {
                                    Log::info('联系人没有邮箱');
                                }
                            }
                        } else {
                            Log::info('未找到联系人记录，会话ID=' . $session['id'] . ', 联系人ID=' . $session['contact_id']);
                        }
                    }
                } else {
                    Log::info('标题不包含"客户咨询:"');
                }
            }
            
            if (empty($matchedContacts)) {
                // 添加更多诊断信息
                Log::warning('未找到匹配的联系人或邮箱。处理了 ' . count($sessions) . ' 个会话。');
                
                return $this->error('未找到匹配的联系人或邮箱');
            }
            

            
            // 发送邮箱验证
            $successCount = 0;
            $failCount = 0;
            $errorMessages = [];
            
            Log::info('开始发送邮箱验证邮件，共有 ' . count($matchedContacts) . ' 个联系人...');
            
            foreach ($matchedContacts as $contact) {
                try {
                    Log::info("正在处理联系人: {$contact['name']} ({$contact['email']})");
                    
                    // 设置邮件标题和内容
                    $subject = '邮箱验证通知';
                    $content = "尊敬的{$contact['name']}用户，您好！\n\n你回复的消息，我们已经收到，请您查看客服系统去查看\n\n如有任何问题，请随时联系我们。\n\n此致\n商家";
                    
                    Log::info("准备发送邮件: 收件人 = {$contact['email']}, 主题 = {$subject}");
                    
                    // 使用官方的EmailService发送邮件
                    $service = new \app\common\service\EmailService();
                    $res = $service->subject($subject)
                        ->message($content)
                        ->to($contact['email'])
                        ->send();
                    
                    if ($res) {
                        Log::info("邮件发送成功: {$contact['email']}");
                        $successCount++;
                        
                        // 添加系统消息记录邮件发送
                        Db::name('plugin_chat_messages')->insert([
                            'session_id' => $contact['session_id'],
                            'sender_type' => 'staff',
                            'sender_id' => $merchantId,
                            'role_type' => 'merchant',
                            'message' => "已向用户 {$contact['name']} 发送邮箱验证通知",
                            'message_type' => 'text',
                            'is_read' => 0,
                            'create_time' => time(),
                            'update_time' => time(),
                        ]);
                    } else {
                        $error = $service->getError() ?: '未知错误';
                        Log::error("邮件发送失败: {$contact['email']}, 错误: {$error}");
                        $failCount++;
                        $errorMessages[] = "发送给 {$contact['name']} ({$contact['email']}) 失败: " . $error;
                    }
                } catch (\Exception $e) {
                    Log::error("邮件发送异常: {$contact['email']}, 错误: " . $e->getMessage());
                    $failCount++;
                    $errorMessages[] = "发送给 {$contact['name']} ({$contact['email']}) 失败: " . $e->getMessage();
                }
            }
            
            Log::info("邮件发送完成: 成功 {$successCount} 封, 失败 {$failCount} 封");
            
            if ($successCount > 0) {
                return $this->success("成功发送 {$successCount} 封邮箱验证邮件" . ($failCount > 0 ? "，失败 {$failCount} 封" : ""), [
                    'success_count' => $successCount,
                    'fail_count' => $failCount,
                    'error_messages' => $errorMessages,
                    'matched_contacts' => $matchedContacts
                ]);
            } else {
                return $this->error("邮箱验证邮件发送失败", [
                    'error_messages' => $errorMessages,
                    'matched_contacts' => $matchedContacts
                ]);
            }
        } catch (\Exception $e) {
            Log::error('发送邮箱验证失败: ' . $e->getMessage());
            return $this->error('发送邮箱验证失败: ' . $e->getMessage());
        }
    }
}
