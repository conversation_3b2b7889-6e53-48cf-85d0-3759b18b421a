# 注册工具文件说明

## 📁 项目结构

```
register/
├── gui/                    # 🖥️ GUI界面模块
│   ├── __init__.py
│   ├── register_gui.py     # 主GUI界面类
│   └── run_gui.py          # GUI启动入口
├── core/                   # 🔧 核心功能模块
│   ├── __init__.py
│   ├── register.py         # 注册功能核心类（API方式）
│   ├── sms_api.py          # 椰子云短信API接口
│   ├── captcha_handler.py  # 验证码处理模块
│   └── batch_register.py   # 批量注册功能
├── browser_automation/     # 🤖 浏览器自动化模块
│   ├── __init__.py
│   ├── selenium_register.py # Selenium自动化注册核心类
│   ├── element_helper.py   # 页面元素操作辅助类
│   └── xpath_config.py     # XPath配置管理
├── config/                 # ⚙️ 配置管理模块
│   ├── __init__.py
│   ├── config_manager.py   # 配置文件管理
│   ├── config.ini          # 主配置文件
│   └── config_example.ini  # 配置文件示例
├── utils/                  # 🛠️ 工具模块
│   ├── __init__.py
│   └── fix_chromedriver.py # ChromeDriver修复工具
├── docs/                   # 📚 文档模块
│   └── 文件说明.md         # 项目文档
├── dist/                   # 📦 发布版本
│   ├── 用户注册工具.exe    # 打包后的可执行程序
│   ├── config.ini          # 发布版配置文件
│   ├── README.md           # 使用说明
│   ├── install.bat         # 安装脚本
│   └── 使用说明.txt        # 详细使用说明
├── start.py                # 🚀 主启动入口
└── requirements.txt        # 📋 Python依赖包列表
```

## 模块说明

### 🖥️ GUI界面模块 (gui/)
- `register_gui.py` - 主GUI界面类，提供完整的可视化操作界面
- `run_gui.py` - GUI启动入口，负责启动图形界面

### 🔧 核心功能模块 (core/)
- `register.py` - 注册功能核心类，提供API方式的注册功能
- `sms_api.py` - 椰子云短信API接口，处理短信验证码
- `captcha_handler.py` - 验证码处理模块，支持自动识别和手动输入
- `batch_register.py` - 批量注册功能，支持多线程并发注册

### 🤖 浏览器自动化模块 (browser_automation/)
- `selenium_register.py` - Selenium自动化注册核心类
- `element_helper.py` - 页面元素操作辅助类
- `xpath_config.py` - XPath配置管理

### ⚙️ 配置管理模块 (config/)
- `config_manager.py` - 配置文件管理类
- `config.ini` - 主配置文件
- `config_example.ini` - 配置文件示例

### 🛠️ 工具模块 (utils/)
- `fix_chromedriver.py` - ChromeDriver修复工具

### 📦 发布版本
- `dist/` - 打包后的可执行文件目录
  - `用户注册工具.exe` - 打包后的可执行程序
  - `config.ini` - 发布版配置文件
  - `README.md` - 使用说明
  - `install.bat` - 安装脚本
  - `使用说明.txt` - 详细使用说明

### 🗂️ 缓存文件
- `__pycache__/` - Python字节码缓存目录

## 使用方式

### 1. GUI方式启动
```bash
python run_gui.py
```

### 2. 命令行方式启动
```bash
python start.py
```

### 3. 直接运行可执行文件
```bash
cd dist
用户注册工具.exe
```

## 主要功能

1. **用户注册**
   - 支持970faka.com等网站注册
   - 自动填写用户信息
   - 自动处理验证码

2. **短信验证**
   - 集成椰子云短信API
   - 自动获取和填写短信验证码

3. **批量注册**
   - 支持批量生成用户
   - 多线程并发注册
   - 进度实时显示

4. **验证码处理**
   - ddddocr自动识别
   - 手动输入备选
   - 智能重试机制

## 配置说明

主要配置项在 `config.ini` 中：

- `[BASIC]` - 基本设置（域名、用户名长度等）
- `[REGISTER]` - 注册设置（重试次数、线程数等）
- `[SELENIUM]` - 浏览器设置（无头模式、窗口大小等）
- `[YEZI_CLOUD]` - 椰子云API设置
- `[UI]` - 界面设置

## 依赖要求

主要Python包（见requirements.txt）：
- PyQt6 - GUI界面
- selenium - 浏览器自动化
- requests - HTTP请求
- ddddocr - 验证码识别
- Pillow - 图像处理

## 注意事项

1. 首次使用需要安装ChromeDriver
2. 椰子云功能需要配置API密钥
3. 建议在虚拟环境中运行
4. 使用前请阅读相关网站的服务条款
