// 创建翻译容器
var gtranslate_wrapper = document.createElement('div');
gtranslate_wrapper.className = 'gtranslate_wrapper';
document.body.appendChild(gtranslate_wrapper);

// 配置 GTranslate 设置
window.gtranslateSettings = {
    "default_language": "zh-CN",
    "native_language_names": true,
    "detect_browser_language": true,
    "languages": ["zh-CN", "zh-TW", "en", "vi", "th", "ml"],
    "wrapper_selector": ".gtranslate_wrapper",
    "alt_flags": {"en": "usa"},
    "edge_browser_fix": true
};

// 动态加载 GTranslate 脚本
var script = document.createElement('script');
script.src = 'https://cdn.gtranslate.net/widgets/latest/float.js';
script.defer = true;
document.body.appendChild(script);