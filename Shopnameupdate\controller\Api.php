<?php

namespace plugin\Shopnameupdate\controller;

use app\common\controller\BasePlugin;
use think\facade\Db;
use think\facade\View;
use think\facade\Cache;

class Api extends BasePlugin {

    protected $scene = [
        'merchant',
        'user'  // 添加 user 场景以获取商家ID
    ];
    
    protected $noNeedLogin = [];

    public function index() {
        return View::fetch();
    }

    /**
     * 同步商品名称
     */
    public function syncGoods()
    {
        // 获取当前商家ID
        $merchantId = $this->user->id;
        
        // 检查商家是否启用了此功能
        $status = intval(merchant_plugconf($merchantId, "Shopnameupdate.status") ?? 0);
        if ($status !== 1) {
            return $this->error('请先启用同步功能');
        }

        $prefix = config('database.connections.mysql.prefix');
        $goodsTable = $prefix . 'goods';

        // 查询当前商家的父商品
        $parentGoods = Db::table($goodsTable)
            ->where([
                ['user_id', '=', $merchantId],
                ['parent_id', '=', 0]
            ])
            ->field('id, name, update_time')
            ->select()
            ->toArray();

        if (empty($parentGoods)) {
            return $this->success('没有需要同步的商品');
        }

        $updateCount = 0;
        foreach ($parentGoods as $parent) {
            // 获取缓存的商品信息
            $cacheKey = "shopnameupdate_goods_{$parent['id']}";
            $cachedInfo = Cache::get($cacheKey);
            
            // 如果缓存不存在或更新时间不同，则需要同步
            if (!$cachedInfo || $cachedInfo['update_time'] != $parent['update_time']) {
                // 更新子商品
                $affected = Db::table($goodsTable)
                    ->where([
                        ['parent_id', '=', $parent['id']],
                        ['name', '<>', $parent['name']]
                    ])
                    ->update([
                        'name' => $parent['name'],
                        'update_time' => time()
                    ]);
                
                $updateCount += $affected;
                
                // 更新缓存
                Cache::set($cacheKey, [
                    'name' => $parent['name'],
                    'update_time' => $parent['update_time']
                ]);
            }
        }

        if ($updateCount > 0) {
            return $this->success("同步完成，更新了{$updateCount}个商品名称");
        } else {
            return $this->success('所有商品名称已是最新');
        }
    }

    /**
     * 同步所有关联商品名称
     */
    public function syncAllGoods()
    {
        $prefix = config('database.connections.mysql.prefix');
        $goodsTable = $prefix . 'goods';

        // 查询所有父商品(parent_id = 0)
        $parentGoods = Db::table($goodsTable)
            ->where('parent_id', 0)
            ->field('id, name')
            ->select()
            ->toArray();

        if (empty($parentGoods)) {
            $this->success('没有需要同步的商品');
        }

        $updateCount = 0;
        foreach ($parentGoods as $parent) {
            // 更新所有关联此父商品的商品名称
            $affected = Db::table($goodsTable)
                ->where([
                    ['parent_id', '=', $parent['id']],
                    ['name', '<>', $parent['name']]  // 只更新名称不同的商品
                ])
                ->update(['name' => $parent['name']]);
            
            $updateCount += $affected;
        }

        if ($updateCount > 0) {
            $this->success("同步完成，更新了{$updateCount}个商品名称");
        } else {
            $this->success('所有商品名称已是最新');
        }
    }

    /**
     * 获取配置数据
     */
    public function fetchData() {
        $params = [
            'status' => intval(merchant_plugconf($this->user->id, "Shopnameupdate.status") ?? 0),
            'auto_sync' => intval(merchant_plugconf(0, "Shopnameupdate.auto_sync") ?? 0)
        ];
        
        $this->success('success', $params);
    }

    /**
     * 保存配置数据
     */
    public function save() {
        $status = $this->request->post('status/d', 0);
        merchant_plugconf($this->user->id, "Shopnameupdate.status", $status);
        
        $this->success('保存成功');
    }

    /**
     * 保存自动同步状态
     */
    public function saveAutoSync() {
        $auto_sync = $this->request->post('auto_sync/d', 0);
        merchant_plugconf(0, "Shopnameupdate.auto_sync", $auto_sync);
        
        $this->success('保存成功');
    }

    /**
     * 同步上级商品名称
     */
    public function syncParentGoods()
    {
        // 获取当前商家ID
        $merchantId = $this->user->id;
        
        // 检查商家是否启用了此功能
        $status = intval(merchant_plugconf($merchantId, "Shopnameupdate.status") ?? 0);
        if ($status !== 1) {
            $this->error('请先启用同步功能');
        }

        $prefix = config('database.connections.mysql.prefix');
        $goodsTable = $prefix . 'goods';

        // 查询当前商家的子商品（parent_id > 0）
        $childGoods = Db::table($goodsTable)
            ->where([
                ['user_id', '=', $merchantId],
                ['parent_id', '>', 0]  // 只查找子商品
            ])
            ->field('id, parent_id')
            ->select()
            ->toArray();

        if (empty($childGoods)) {
            $this->success('没有需要同步的商品');
        }

        // 获取所有父商品ID
        $parentIds = array_unique(array_column($childGoods, 'parent_id'));
        
        // 获取父商品信息
        $parentGoods = Db::table($goodsTable)
            ->whereIn('id', $parentIds)
            ->field('id, name')
            ->select()
            ->toArray();

        if (empty($parentGoods)) {
            $this->success('没有找到上级商品');
        }

        $updateCount = 0;
        foreach ($parentGoods as $parent) {
            // 更新关联到此父商品的所有子商品
            $affected = Db::table($goodsTable)
                ->where([
                    ['parent_id', '=', $parent['id']],
                    ['user_id', '=', $merchantId],
                    ['name', '<>', $parent['name']]  // 只更新名称不同的商品
                ])
                ->update(['name' => $parent['name']]);
            
            $updateCount += $affected;
        }

        if ($updateCount > 0) {
            $this->success("同步完成，更新了{$updateCount}个商品名称");
        } else {
            $this->success('所有商品名称已是最新');
        }
    }
}
