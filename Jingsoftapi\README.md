# 鲸商城API接口管理插件

## 功能概述

本插件为鲸商城系统提供API接口管理功能，支持：

1. **全局访问密钥管理**
   - 自动生成API密钥
   - 重置密钥功能
   - 密钥复制功能

2. **接口独立密钥管理**
   - 每个API接口可生成独立密钥
   - 支持接口级别的访问控制
   - 密钥显示/隐藏切换

3. **API接口控制**
   - 接口开关控制
   - API文档生成
   - 接口参数验证

## 接口列表

### 获取用户信息 (get_user_info)
- **功能**: 根据用户ID获取用户详细信息
- **支持密钥**: 全局密钥 + 接口独立密钥
- **请求方式**: POST
- **参数**: user_id (必填)

## 使用说明

### 1. 密钥管理

#### 全局密钥
- 页面加载时自动生成
- 点击"重置密钥"生成新的全局密钥
- 全局密钥可访问所有启用的接口

#### 接口独立密钥
- 点击"生成接口密钥"为特定接口生成独立密钥
- 接口密钥只能访问对应的接口
- 优先级高于全局密钥

### 2. 接口调用

```bash
# 使用全局密钥调用
curl -X POST "http://your-domain.com/plugin/Jingsoftapi/api/get_user_info" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: ak_global_key_here" \
  -H "X-API-Secret: sk_global_secret_here" \
  -d '{"user_id": 1}'

# 使用接口独立密钥调用
curl -X POST "http://your-domain.com/plugin/Jingsoftapi/api/get_user_info" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: ep_get_user_info_key_here" \
  -H "X-API-Secret: es_get_user_info_secret_here" \
  -d '{"user_id": 1}'
```

### 3. 响应格式

```json
{
  "code": 200,
  "msg": "获取用户信息成功",
  "data": {
    "user_id": 1,
    "mobile": "13800138000",
    "username": "testuser",
    "email": "<EMAIL>",
    "nickname": "测试用户",
    "avatar": "http://example.com/avatar.jpg",
    "description": "用户描述",
    "contact_info": {
      "qq": "123456789",
      "mobile": "13800138000",
      "wechat": "wechat_id"
    },
    "create_time": "2024-01-01 12:00:00",
    "create_ip": "127.0.0.1",
    "login_province": "北京",
    "sell_count": 100,
    "platform_money": 1000.50,
    "deposit_money": 500.00,
    "status": {
      "custom_status": 1,
      "custom_status_msg": ""
    },
    "social_links": {
      "weibo": "http://weibo.com/user",
      "website": "http://user-website.com"
    }
  }
}
```

## 安全特性

1. **双重密钥验证**: API Key + API Secret
2. **接口级别控制**: 可单独启用/禁用接口
3. **独立密钥支持**: 接口可使用独立密钥
4. **商家隔离**: 每个商家有独立的密钥空间

## 错误码

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | API认证失败 |
| 404 | 用户不存在 |
| 500 | 服务器内部错误 |

## 注意事项

1. 请妥善保管API密钥，避免泄露
2. 接口密钥优先级高于全局密钥
3. 建议在生产环境中使用HTTPS协议
4. API调用频率限制：每秒1次请求(QPS)，每日1000次请求
