<!doctype html>
<html lang="zh">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>{$title}</title>
        
        <link rel="shortcut icon" href="{$favicon}" type="image/x-icon">
        <link rel="stylesheet" href="/assets/template/default/assets/styles.css">
        <style>
            @font-face {
    font-family: 'Noto Sans SC';
    src: url('/assets/plugin/Blackglod/plugin/Blackglod/fonts/k3kCo84MPvpLmixcA63oeAL7Iqp5IZJF9bmaG-3FnYw.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
}
@font-face {
    font-family: 'Noto Sans SC';
    src: url('/assets/plugin/Blackglod/plugin/Blackglod/fonts/k3kCo84MPvpLmixcA63oeAL7Iqp5IZJF9bmaG9_FnYw.ttf') format('truetype');
    font-weight: 500;
    font-style: normal;
}
@font-face {
    font-family: 'Noto Sans SC';
    src: url('/assets/plugin/Blackglod/plugin/Blackglod/fonts/k3kCo84MPvpLmixcA63oeAL7Iqp5IZJF9bmaGzjCnYw.ttf') format('truetype');
    font-weight: 700;
    font-style: normal;
}

:root {
    --primary: #C8A675;
    --primary-dark: #a88a5c;
    --primary-light: #e0c4a3;
    --dark: #000000;
    --light: #ffffff;
    --gray: #666666;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Noto Sans SC', sans-serif;
    background-color: var(--dark);
    color: var(--light);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
}

/* 增强的背景效果 */
.background-animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    opacity: 0.1;
    background: 
        radial-gradient(circle at 20% 20%, var(--primary) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, var(--primary-dark) 0%, transparent 50%);
    filter: blur(100px);
    animation: backgroundMove 20s ease-in-out infinite;
    will-change: transform;
    transform: translateZ(0);
    contain: paint;
}

@keyframes backgroundMove {
    0%, 100% { transform: translate(0, 0); }
    25% { transform: translate(5%, 5%); }
    50% { transform: translate(-5%, 5%); }
    75% { transform: translate(-5%, -5%); }
}

/* 增强的头部导航 */
.header {
    padding: 1rem 0;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    transition: var(--transition);
    will-change: transform, opacity;
    transform: translateZ(0);
}

.header.scrolled {
    padding: 0.5rem 0;
    background: rgba(0, 0, 0, 0.95);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
    color: var(--light);
    text-decoration: none;
    min-width: 200px;
}

.logo-img {
    height: 36px;
    width: auto;
}

.logo span {
    font-size: 1.4rem;
    font-weight: 500;
    background: linear-gradient(135deg, var(--light), var(--primary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.nav-links {
    display: flex;
    gap: 1rem;
    margin-left: auto;
    flex-wrap: nowrap;
    align-items: center;
}

.nav-item {
    position: relative;
    white-space: nowrap;
}

.nav-item > a {
    color: var(--light);
    text-decoration: none;
    font-size: 0.85rem;
    padding: 0.4rem 0.6rem;
    border-radius: 4px;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 2px;
}

.nav-item > a:hover {
    color: var(--primary);
    background: rgba(200, 166, 117, 0.1);
}

.dropdown-arrow {
    transition: var(--transition);
    margin-left: 2px;
}

.nav-item:hover .dropdown-arrow {
    transform: translateY(2px);
}

.submenu {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translate3d(-50%, 10px, 0);
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(10px);
    min-width: 160px;
    border-radius: 8px;
    padding: 0.5rem;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(200, 166, 117, 0.1);
    will-change: transform, opacity;
}

.nav-item:hover .submenu {
    opacity: 1;
    visibility: visible;
    transform: translate3d(-50%, 0, 0);
}

.submenu a {
    color: var(--light);
    text-decoration: none;
    padding: 0.6rem 1rem;
    display: block;
    font-size: 0.9rem;
    border-radius: 4px;
    transition: var(--transition);
}

.submenu a:hover {
    background: rgba(200, 166, 117, 0.1);
    color: var(--primary);
}

.auth-buttons {
    display: flex;
    gap: 1rem;
    margin-left: 2rem;
}

.auth-buttons .btn {
    padding: 0.5rem 1.2rem;
    border-radius: 4px;
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 6px;
}

.auth-icon {
    transition: var(--transition);
}

.btn-login {
    color: var(--primary);
    border: 1px solid var(--primary);
    background: transparent;
}

.btn-login:hover {
    background: rgba(200, 166, 117, 0.1);
}

.btn-login:hover .auth-icon {
    transform: scale(1.1);
}

.btn-register {
    color: var(--dark);
    background: var(--primary);
    border: 1px solid var(--primary);
}

.btn-register .auth-icon {
    color: var(--dark);
}

.btn-register:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-register:hover .auth-icon {
    transform: scale(1.1);
}

@media (max-width: 768px) {
    .nav-links, .auth-buttons {
        display: none;
    }
}

/* 增强的英雄区域 */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    padding: 6rem 0;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        linear-gradient(45deg, rgba(0,0,0,0.7), transparent),
        radial-gradient(circle at center, transparent, rgba(0,0,0,0.8));
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    animation: fadeInUp 1s ease-out;
}

.hero h1 {
    font-size: 4rem;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--light), var(--primary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradientFlow 8s linear infinite;
}

@keyframes gradientFlow {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.hero h2 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    background: linear-gradient(135deg, var(--light) 10%, var(--primary) 50%, var(--light) 90%);
    background-size: 200% auto;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: shine 3s linear infinite;
    text-shadow: 0 0 10px rgba(200, 166, 117, 0.3);
    position: relative;
}

@keyframes shine {
    0% {
        background-position: 200% center;
    }
    100% {
        background-position: -200% center;
    }
}

.hero h2::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
    animation: lightPass 2s ease-in-out infinite;
    transform: skewX(-20deg);
}

@keyframes lightPass {
    0% {
        transform: translateX(-100%) skewX(-20deg);
    }
    100% {
        transform: translateX(200%) skewX(-20deg);
    }
}

/* 增强的统计数字 */
.stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 4rem;
}

.stat-item {
    background: rgba(0, 0, 0, 0.4);
    border: 1px solid rgba(200, 166, 117, 0.2);
    border-radius: 20px;
    padding: 2.5rem 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
    backdrop-filter: blur(10px);
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(200, 166, 117, 0.2),
        transparent
    );
    transition: 0.5s;
}

.stat-item:hover::before {
    left: 100%;
}

.stat-item:hover {
    transform: translateY(-10px);
    border-color: var(--primary);
    box-shadow: 0 10px 30px rgba(200, 166, 117, 0.1);
}

.stat-number {
    font-size: 2.8rem;
    font-weight: 700;
    background: linear-gradient(135deg, #ffd700, #c8a675);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 1rem;
    font-family: 'Arial', sans-serif;
    position: relative;
    display: inline-block;
}

.stat-number::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 2px;
    background: var(--primary);
    border-radius: 2px;
}

.stat-item div:last-child {
    color: #999;
    font-size: 1.1rem;
    margin-top: 0.5rem;
    letter-spacing: 1px;
}

/* 数字滚动动画 */
@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stat-number.visible {
    animation: countUp 1.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* 增强的特性区域 */
.features {
    padding: 8rem 0;
    background: linear-gradient(to bottom, transparent, rgba(0,0,0,0.8));
    position: relative;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-item {
    padding: 2.5rem;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(200, 166, 117, 0.1);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.feature-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, transparent, rgba(200, 166, 117, 0.1));
    opacity: 0;
    transition: var(--transition);
}

.feature-item:hover {
    transform: translateY(-10px);
    border-color: var(--primary);
}

.feature-item:hover::before {
    opacity: 1;
}

.feature-icon {
    font-size: 3rem;
    color: var(--primary);
    margin-bottom: 1.5rem;
    transition: var(--transition);
}

.feature-item:hover .feature-icon {
    transform: scale(1.2) rotate(10deg);
}

/* 增强的步骤区域 */
.steps {
    padding: 8rem 0;
    background: linear-gradient(to top, transparent, rgba(0,0,0,0.8));
    position: relative;
    overflow: hidden;
}

/* 添加六边形网格背景 */
.steps::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(30deg, #000000 12%, transparent 12.5%, transparent 87%, #000000 87.5%, #000000),
        linear-gradient(150deg, #000000 12%, transparent 12.5%, transparent 87%, #000000 87.5%, #000000),
        linear-gradient(30deg, #000000 12%, transparent 12.5%, transparent 87%, #000000 87.5%, #000000),
        linear-gradient(150deg, #000000 12%, transparent 12.5%, transparent 87%, #000000 87.5%, #000000),
        linear-gradient(60deg, rgba(200,166,117,0.1) 25%, transparent 25.5%, transparent 75%, rgba(200,166,117,0.1) 75%, rgba(200,166,117,0.1)),
        linear-gradient(60deg, rgba(200,166,117,0.1) 25%, transparent 25.5%, transparent 75%, rgba(200,166,117,0.1) 75%, rgba(200,166,117,0.1));
    background-size: 40px 70px;
    background-position: 0 0, 0 0, 20px 35px, 20px 35px, 0 0, 20px 35px;
    opacity: 0.1;
    z-index: 0;
}

/* 添加发光动画效果 */
.steps::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 50% 50%, 
        rgba(200,166,117,0.1),
        transparent 60%);
    animation: pulseGlow 4s ease-in-out infinite;
    z-index: 1;
}

@keyframes pulseGlow {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 0.5;
        transform: scale(1.2);
    }
}

/* 确保内容在背景之上 */
.steps .container {
    position: relative;
    z-index: 2;
}

.steps .section-title {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--primary-light), var(--primary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.steps .section-subtitle {
    text-align: center;
    color: var(--gray);
    margin-bottom: 4rem;
    font-size: 1.1rem;
}

.steps-container {
    display: flex;
    justify-content: center;
    gap: 4rem;
    position: relative;
}

.steps-container::before {
    content: '';
    position: absolute;
    top: 60px;
    left: calc(16.666% + 60px);
    right: calc(16.666% + 60px);
    height: 2px;
    background: linear-gradient(90deg,
        transparent,
        var(--primary) 20%,
        var(--primary) 80%,
        transparent
    );
    opacity: 0.3;
}

.step {
    flex: 1;
    max-width: 280px;
    text-align: center;
    position: relative;
}

.step-circle {
    width: 120px;
    height: 120px;
    margin: 0 auto 2rem;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.step-number {
    position: absolute;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary);
    z-index: 2;
    transition: var(--transition);
    opacity: 1;
}

.step-icon {
    position: absolute;
    font-size: 2.5rem;
    color: var(--primary);
    z-index: 2;
    opacity: 0;
    transform: scale(0.5);
    transition: var(--transition);
}

.progress-ring {
    position: absolute;
    transform: rotate(-90deg);
}

.progress-ring-circle {
    fill: transparent;
    stroke: var(--primary);
    stroke-width: 2;
    stroke-dasharray: 339.292;
    stroke-dashoffset: 339.292;
    transition: var(--transition);
}

.step:hover .progress-ring-circle {
    stroke-dashoffset: 0;
}

.step:hover .step-number {
    opacity: 0;
    transform: scale(0.5);
}

.step:hover .step-icon {
    opacity: 1;
    transform: scale(1);
}

.step h3 {
    color: var(--primary);
    font-size: 1.3rem;
    margin-bottom: 1rem;
    transition: var(--transition);
}

.step p {
    color: var(--gray);
    font-size: 1rem;
    line-height: 1.6;
    transition: var(--transition);
}

.step:hover h3 {
    transform: translateY(-5px);
    color: var(--primary-light);
}

.step:hover p {
    color: var(--light);
}

@media (max-width: 768px) {
    .steps {
        padding: 4rem 0;
    }

    .steps-container {
        flex-direction: column;
        align-items: center;
        gap: 3rem;
    }

    .steps-container::before {
        display: none;
    }

    .step {
        max-width: 100%;
    }

    .steps .section-title {
        font-size: 2rem;
    }

    .steps .section-subtitle {
        font-size: 1rem;
        margin-bottom: 3rem;
    }
}

/* 增强的页脚 */
.footer {
    padding: 4rem 0;
    background: rgba(0, 0, 0, 0.95);
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(to right, 
        transparent, 
        var(--primary),
        transparent
    );
}

.footer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-column h3 {
    color: var(--primary);
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
}

.footer-column a {
    color: var(--light);
    text-decoration: none;
    display: block;
    margin-bottom: 0.8rem;
    transition: var(--transition);
    position: relative;
    padding-left: 1.5rem;
}

.footer-column a::before {
    content: '→';
    position: absolute;
    left: 0;
    opacity: 0;
    transform: translateX(-10px);
    transition: var(--transition);
}

.footer-column a:hover {
    color: var(--primary);
    padding-left: 2rem;
}

.footer-column a:hover::before {
    opacity: 1;
    transform: translateX(0);
}

.copyright {
    text-align: center;
    color: var(--gray);
    padding-top: 2rem;
    border-top: 1px solid rgba(200, 166, 117, 0.1);
    font-size: 0.9rem;
}

.copyright a {
    color: var(--primary);
    text-decoration: none;
    transition: var(--transition);
    margin: 0 0.5rem;
}

.copyright a:hover {
    color: var(--primary-light);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-links {
        display: none;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .hero h2 {
        font-size: 2rem;
    }

    .steps-container {
        flex-direction: column;
        gap: 2rem;
    }

    .steps-container::before {
        display: none;
    }

    .auth-buttons {
        display: none;
    }

    .mobile-menu-btn {
        display: block;
    }
}

/* 移动端菜单按钮 */
.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    color: var(--light);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
}

@media (max-width: 768px) {
    .mobile-menu-btn {
        display: block;
    }
}

/* 星星背景容器 */
.stars-container {
position: fixed;
top: 0;
left: 0;
width: 100%;
height: 100%;
overflow: hidden;
z-index: 0;
pointer-events: none;
background: radial-gradient(circle at center, rgba(0,0,0,0.8), rgba(0,0,0,0.95)); /* 添加暗色渐变背景 */
}

/* 星星样式 */
.star {
position: absolute;
background: linear-gradient(135deg, #ffd700, #ffa500);  /* 更亮的金色渐变 */
border-radius: 50%;
filter: blur(1px);
box-shadow: 
    0 0 4px #ffd700,
    0 0 8px #ffd700,
    0 0 12px #c8a675;  /* 三层光晕效果 */
opacity: 0;
animation: twinkle var(--duration) ease-in-out infinite;
animation-delay: var(--delay);
will-change: transform, opacity;
transform: translateZ(0);
transform: translate3d(0, 0, 0);
}

/* 星星闪烁动画 */
@keyframes twinkle {
0%, 100% {
    opacity: 0;
    transform: scale(0.3);
    filter: blur(1px);
}
50% {
    opacity: var(--opacity);
    transform: scale(1.2);  /* 更大的缩放效果 */
    filter: blur(0.5px);
}
}

/* 流星效果 */
.shooting-star {
position: absolute;
width: 200px;  /* 更长的流星 */
height: 3px;   /* 更粗的流星 */
background: linear-gradient(90deg, 
    rgba(255, 215, 0, 1), 
    rgba(255, 215, 0, 0.8),
    rgba(200, 166, 117, 0.4), 
    transparent
);
opacity: 0;
filter: blur(0.5px);
transform: rotate(-45deg);
box-shadow: 
    0 0 20px rgba(255, 215, 0, 0.8),
    0 0 30px rgba(255, 215, 0, 0.6);  /* 更强的光晕效果 */
animation: shoot 3s ease-in infinite;
animation-delay: var(--delay);
will-change: transform, opacity;
}

@keyframes shoot {
0% {
    opacity: 0;
    transform: translateX(-100%) translateY(0) rotate(-45deg);
}
10% {
    opacity: 1;
}
20%, 100% {
    opacity: 0;
    transform: translateX(100vw) translateY(100vh) rotate(-45deg);
}
}

/* 金额数字的响应式字体大小 */
.stat-number.amount {
font-size: 2.8rem; /* 默认大小 */
}

.stat-number.amount.length-9 {
font-size: 2.6rem;
}

.stat-number.amount.length-10 {
font-size: 2.4rem;
}

.stat-number.amount.length-11 {
font-size: 2.2rem;
}

.stat-number.amount.length-12 {
font-size: 2rem;
}

/* 移动端适配 */
@media (max-width: 768px) {
.stat-number.amount {
    font-size: 2.2rem;
}

.stat-number.amount.length-9 {
    font-size: 2rem;
}

.stat-number.amount.length-10 {
    font-size: 1.8rem;
}

.stat-number.amount.length-11 {
    font-size: 1.6rem;
}

.stat-number.amount.length-12 {
    font-size: 1.4rem;
}
}

.payment-icons {
padding: 6rem 0;
background: rgba(0, 0, 0, 0.3);
border-bottom: 1px solid rgba(200, 166, 117, 0.1);
position: relative;
overflow: hidden;
}

.payment-icons::before {
content: '';
position: absolute;
top: 0;
left: 0;
right: 0;
height: 1px;
background: linear-gradient(90deg, 
    transparent,
    var(--primary),
    transparent
);
}

.section-title {
text-align: center;
color: var(--primary);
font-size: 2.2rem;
margin-bottom: 1rem;
background: linear-gradient(135deg, var(--primary-light), var(--primary));
-webkit-background-clip: text;
-webkit-text-fill-color: transparent;
}

.section-subtitle {
text-align: center;
color: var(--gray);
margin-bottom: 3rem;
font-size: 1.1rem;
}

.icons-grid {
display: grid;
grid-template-columns: repeat(6, 1fr);
gap: 3rem;
align-items: center;
justify-items: center;
padding: 2rem 0;
}

.icon-item {
display: flex;
flex-direction: column;
align-items: center;
gap: 1.2rem;
position: relative;
}

.icon-wrapper {
position: relative;
padding: 1rem;
}

.payment-icon {
width: 70px;
height: 70px;
color: var(--primary);
transition: var(--transition);
position: relative;
z-index: 2;
}

.icon-glow {
position: absolute;
inset: 0;
background: radial-gradient(circle at center, 
    rgba(200, 166, 117, 0.2),
    transparent 70%
);
opacity: 0;
transition: var(--transition);
filter: blur(10px);
}

.icon-item:hover .icon-glow {
opacity: 1;
transform: scale(1.2);
}

.icon-item:hover .payment-icon {
transform: scale(1.1);
filter: drop-shadow(0 0 10px rgba(200, 166, 117, 0.3));
}

.icon-item span {
color: var(--gray);
font-size: 1rem;
font-weight: 500;
transition: var(--transition);
}

.icon-item:hover span {
color: var(--primary-light);
}

@media (max-width: 768px) {
.payment-icons {
    padding: 4rem 0;
}

.icons-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.payment-icon {
    width: 60px;
    height: 60px;
}

.section-title {
    font-size: 1.8rem;
}

.section-subtitle {
    font-size: 1rem;
    margin-bottom: 2rem;
}
}

/* 减少不必要的动画 */
@media (prefers-reduced-motion: reduce) {
.background-animation,
.star,
.shooting-star {
    animation: none;
}
}

.nav-item > a.has-arrow {
display: flex;
align-items: center;
gap: 8px;
padding-right: 12px;
}

/* 确保箭头颜色为金色 */
.dropdown-arrow path {
stroke: var(--primary);
}

/* 悬停时箭头颜色加深 */
.nav-item:hover .dropdown-arrow path {
stroke: var(--primary-dark);
}

/* 添加FAQ样式 */
.article-section {
    min-height: 100vh;
    padding: 120px 0 60px;
    position: relative;
    z-index: 2;
    background: linear-gradient(to bottom, 
        rgba(0, 0, 0, 0.8),
        rgba(0, 0, 0, 0.6)
    );
}

.article-container {
    background: rgba(0, 0, 0, 0.4);
    border-radius: 24px;
    padding: 3rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(200, 166, 117, 0.1);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 900px;
    margin: 0 auto;
}

.page-title {
    text-align: center;
    margin-bottom: 4rem;
    position: relative;
}

.page-title h1 {
    color: var(--primary);
    font-size: 2.5rem;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--primary-light), var(--primary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
    display: inline-block;
}

.page-title h1::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(to right, var(--primary-light), var(--primary));
    border-radius: 3px;
}

.page-title p {
    color: var(--gray);
    font-size: 1.1rem;
    max-width: 600px;
    margin: 1rem auto 0;
}

.notice-box {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 20px;
    padding: 2rem;
    position: relative;
}

.notice-box::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(45deg, 
        rgba(200, 166, 117, 0.1),
        transparent,
        rgba(200, 166, 117, 0.1)
    );
    border-radius: 20px;
    z-index: -1;
}

.faq-item {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(200, 166, 117, 0.1);
    border-radius: 12px;
    margin-bottom: 1.5rem;
    overflow: hidden;
    transform: translateZ(0);
    will-change: transform;
    transition: transform 0.2s ease, border-color 0.2s ease;
}

.faq-item:hover {
    border-color: var(--primary);
    transform: translateY(-2px) translateZ(0);
    box-shadow: 0 10px 30px rgba(200, 166, 117, 0.1);
}

.faq-item h3 {
    padding: 1.5rem;
    color: var(--light);
    font-size: 1.1rem;
    margin: 0;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    transition: color 0.2s ease;
    will-change: transform;
    transform: translateZ(0);
}

.faq-item h3:hover {
    color: var(--primary-light);
}

.faq-item h3::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 3px;
    background: var(--primary);
    opacity: 0;
    transition: all 0.3s ease;
}

.faq-item.active h3::before {
    opacity: 1;
}

.faq-item h3::after {
    content: '+';
    font-size: 1.5rem;
    color: var(--primary);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 24px;
    text-align: center;
    transform-origin: center;
}

.faq-item.active h3::after {
    transform: rotate(45deg);
    color: var(--primary-light);
}

.faq-content {
    padding: 0 1.5rem;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: max-height, padding;
    transform: translateZ(0);
    opacity: 0;
    color: var(--gray);
    line-height: 1.8;
    font-size: 1.05rem;
}

.faq-item.active .faq-content {
    max-height: 1000px;
    padding: 0 1.5rem 1.5rem;
    opacity: 1;
}

.faq-content p {
    margin-bottom: 1rem;
    position: relative;
    padding-left: 1.2rem;
}

.faq-content p::before {
    content: '•';
    position: absolute;
    left: 0;
    color: var(--primary);
}

.faq-content a {
    color: var(--primary);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    padding-bottom: 2px;
}

.faq-content a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: var(--primary);
    transform: scaleX(0);
    transition: transform 0.2s ease;
    transform-origin: right;
}

.faq-content a:hover {
    color: var(--primary-light);
}

.faq-content a:hover::after {
    transform: scaleX(1);
    transform-origin: left;
}

/* 添加渐入动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px) translateZ(0);
    }
    to {
        opacity: 1;
        transform: translateY(0) translateZ(0);
    }
}

.faq-item {
    animation: fadeIn 0.3s ease-out forwards;
}

.faq-item:nth-child(2) { animation-delay: 0.1s; }
.faq-item:nth-child(3) { animation-delay: 0.2s; }
.faq-item:nth-child(4) { animation-delay: 0.3s; }
.faq-item:nth-child(5) { animation-delay: 0.4s; }

/* 响应式优化 */
@media (max-width: 768px) {
    .article-section {
        padding: 100px 1rem 40px;
    }
    
    .article-container {
        padding: 2rem 1.5rem;
    }
    
    .page-title h1 {
        font-size: 2rem;
    }
    
    .page-title p {
        font-size: 1rem;
    }
    
    .faq-item h3 {
        font-size: 1rem;
        padding: 1.2rem;
    }
    
    .faq-content {
        font-size: 0.95rem;
    }
    
    .notice-box {
        padding: 1.5rem;
    }
}

/* 当导航项超过一定数量时进行响应式调整 */
@media (max-width: 1200px) {
    .nav-item > a {
        font-size: 0.8rem;
        padding: 0.3rem 0.5rem;
    }
    
    .nav-links {
        gap: 0.5rem;
    }
}

/* 添加导航栏滚动功能 */
@media (max-width: 1400px) {
    .nav-links {
        overflow-x: auto;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
        padding-bottom: 5px; /* 为滚动条预留空间 */
    }
    
    .nav-links::-webkit-scrollbar {
        display: none; /* Chrome, Safari and Opera */
    }
}

/* 行星装饰 */
.planet {
    position: fixed;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    pointer-events: none;
    z-index: 0;
}

.planet-left {
    left: -150px;
    top: 20%;
    background: radial-gradient(circle at 70% 50%,
        rgba(200, 166, 117, 0.15),
        rgba(200, 166, 117, 0.1),
        rgba(0, 0, 0, 0)
    );
    box-shadow: 
        inset -20px -20px 50px rgba(200, 166, 117, 0.2),
        0 0 50px rgba(200, 166, 117, 0.1);
    animation: planetPulseLeft 8s ease-in-out infinite;
}

.planet-right {
    right: -150px;
    bottom: 20%;
    background: radial-gradient(circle at 30% 50%,
        rgba(200, 166, 117, 0.15),
        rgba(200, 166, 117, 0.1),
        rgba(0, 0, 0, 0)
    );
    box-shadow: 
        inset 20px -20px 50px rgba(200, 166, 117, 0.2),
        0 0 50px rgba(200, 166, 117, 0.1);
    animation: planetPulseRight 8s ease-in-out infinite;
}

/* 行星环 */
.planet-ring {
    position: absolute;
    width: 400px;
    height: 80px;
    border-radius: 50%;
    border: 2px solid rgba(200, 166, 117, 0.1);
    transform: rotate(-20deg);
}

.planet-left .planet-ring {
    top: 110px;
    left: -50px;
}

.planet-right .planet-ring {
    top: 110px;
    left: -50px;
    transform: rotate(20deg);
}

/* 行星表面纹理 */
.planet-texture {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: repeating-linear-gradient(
        45deg,
        rgba(200, 166, 117, 0.05) 0px,
        transparent 5px,
        transparent 10px
    );
    animation: textureRotate 20s linear infinite;
}

/* 行星光晕动画 */
@keyframes planetPulseLeft {
    0%, 100% {
        transform: scale(1) translateX(0);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.1) translateX(20px);
        opacity: 1;
    }
}

@keyframes planetPulseRight {
    0%, 100% {
        transform: scale(1) translateX(0);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.1) translateX(-20px);
        opacity: 1;
    }
}

@keyframes textureRotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 响应式调整 */
@media (max-width: 1400px) {
    .planet {
        width: 200px;
        height: 200px;
    }
    .planet-left {
        left: -100px;
    }
    .planet-right {
        right: -100px;
    }
    .planet-ring {
        width: 300px;
        height: 60px;
        top: 70px;
        left: -50px;
    }
}

@media (max-width: 768px) {
    .planet {
        display: none;
    }
}
        </style>
    </head>
    <body>
        <div class="stars-container"></div>
        <div class="background-animation"></div>
        
        <!-- 添加行星元素 -->
        <div class="planet planet-left">
            <div class="planet-texture"></div>
            <div class="planet-ring"></div>
        </div>
        <div class="planet planet-right">
            <div class="planet-texture"></div>
            <div class="planet-ring"></div>
        </div>

        <!-- 导航栏 -->
        <header class="header">
            <div class="container">
                <a href="/" class="logo">
                    {if !empty($logo)}
                    <img src="{$logo}" alt="{$siteName}" class="logo-img">
                    {else}
                    <i class="fas fa-credit-card"></i>
                    {/if}
                </a>
                
                <nav class="nav-links">
                    {foreach $navItems as $nav}
                    <div class="nav-item">
                        <a href="{$nav.href}" class="nav-link {if !empty($nav.children)}has-arrow{/if}">
                            {$nav.name}
                            {if !empty($nav.children)}
                            <svg class="dropdown-arrow" width="8" height="6" viewBox="0 0 8 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M4 6L0 0L8 0L4 6Z" fill="var(--primary)"/>
                            </svg>
                            {/if}
                        </a>
                        {if !empty($nav.children)}
                        <div class="submenu">
                            {foreach $nav.children as $child}
                            <a href="{$child.href}">{$child.name}</a>
                            {/foreach}
                        </div>
                        {/if}
                    </div>
                    {/foreach}
                </nav>
                <div class="auth-buttons">
                    <a href="/merchant/login" class="btn btn-login">
                        <svg class="auth-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M20 21V19C20 16.7909 18.2091 15 16 15H8C5.79086 15 4 16.7909 4 19V21" stroke="var(--primary)" stroke-width="2" stroke-linecap="round"/>
                            <circle cx="12" cy="7" r="4" stroke="var(--primary)" stroke-width="2"/>
                        </svg>
                        商户登录
                    </a>
                    <a href="/merchant/register" class="btn btn-register">
                        <svg class="auth-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M16 21V19C16 16.7909 14.2091 15 12 15H8C5.79086 15 4 16.7909 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            <circle cx="10" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                            <path d="M20 8V14M17 11H23" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                        商户注册
                    </a>
                </div>
                <button class="mobile-menu-btn">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </header>

        <!-- FAQ列表部分 -->
        <section class="article-section" style="padding-top: 120px;">
            <div class="container article-container">
                <div class="page-title" style="text-align: center; margin-bottom: 3rem;">
                    <h1 style="color: var(--primary); font-size: 2.5rem; margin-bottom: 1rem;">常见问题</h1>
                    <p style="color: var(--gray);">为您解答使用过程中的常见问题</p>
                </div>
                
                <div class="notice-box" style="background: rgba(0, 0, 0, 0.3); border-radius: 20px; padding: 2rem;">
                    {foreach $faqList as $item}
                    <div class="faq-item" data-aos="fade-up">
                        <h3 style="cursor: pointer;">{$item.title}</h3>
                        <div class="faq-content">
                            {$item.content|raw}
                        </div>
                    </div>
                    {/foreach}
                </div>
            </div>
        </section>

        <!-- 页脚 -->
        <footer class="footer">
            <div class="container">
                <div class="footer-grid">
                    {if $footer_service_show == 1}
                    <div class="footer-column">
                        <h3>服务中心</h3>
                        <a href="{$footer_service_1_link|default='#'}">{$footer_service_1|default='卡密查询'}</a>
                        <a href="{$footer_service_2_link|default='#'}">{$footer_service_2|default='投诉中心'}</a>
                        <a href="{$footer_service_3_link|default='#'}">{$footer_service_3|default='卡密工具'}</a>
                        <a href="{$footer_service_4_link|default='#'}">{$footer_service_4|default='商户入驻'}</a>
                    </div>
                    {/if}
                    
                    {if $footer_help_show == 1}
                    <div class="footer-column">
                        <h3>帮助中心</h3>
                        <a href="{$footer_help_1_link|default='#'}">{$footer_help_1|default='常见问题'}</a>
                        <a href="{$footer_help_2_link|default='#'}">{$footer_help_2|default='系统公告'}</a>
                        <a href="{$footer_help_3_link|default='#'}">{$footer_help_3|default='结算公告'}</a>
                        <a href="{$footer_help_4_link|default='#'}">{$footer_help_4|default='新闻动态'}</a>
                    </div>
                    {/if}
                    
                    {if $footer_legal_show == 1}
                    <div class="footer-column">
                        <h3>法律责任</h3>
                        <a href="{$footer_legal_1_link|default='#'}">{$footer_legal_1|default='免责声明'}</a>
                        <a href="{$footer_legal_2_link|default='#'}">{$footer_legal_2|default='禁售商品'}</a>
                        <a href="{$footer_legal_3_link|default='#'}">{$footer_legal_3|default='服务协议'}</a>
                        <a href="{$footer_legal_4_link|default='#'}">{$footer_legal_4|default='隐私政策'}</a>
                    </div>
                    {/if}
                    
                    {if $footer_links_show == 1}
                    <div class="footer-column">
                        <h3>友情链接</h3>
                        <a href="{$footer_links_1_link|default='#'}">{$footer_links_1|default='一意支付'}</a>
                        <a href="{$footer_links_2_link|default='#'}">{$footer_links_2|default='支付宝'}</a>
                        <a href="{$footer_links_3_link|default='#'}">{$footer_links_3|default='微信支付'}</a>
                        <a href="{$footer_links_4_link|default='#'}">{$footer_links_4|default='QQ钱包'}</a>
                    </div>
                    {/if}
                </div>
                <div class="copyright">
                    <span>{$siteName|default='Blackglod'} - 版权所有 © {$year|default='2022'}-至今</span>
                    {if !empty($icpNumber)}
                    <a href="https://beian.miit.gov.cn/" target="_blank">{$icpNumber}</a>
                    {/if}
                    {if !empty($gaNumber)}
                    <a href="{$icpCert}" target="_blank">{$gaNumber}</a>
                    {/if}
                </div>
            </div>
        </footer>

        <script>
            // 更新FAQ交互脚本
            document.addEventListener('DOMContentLoaded', function() {
                const faqItems = document.querySelectorAll('.faq-item');
                
                faqItems.forEach(item => {
                    const title = item.querySelector('h3');
                    const content = item.querySelector('.faq-content');
                    
                    title.addEventListener('click', () => {
                        const isActive = item.classList.contains('active');
                        
                        // 只切换当前点击的FAQ项的状态
                        item.classList.toggle('active');
                        
                        if (!isActive) {
                            content.style.maxHeight = content.scrollHeight + 'px';
                        } else {
                            content.style.maxHeight = '0';
                        }
                    });
                });
                
                // 添加滚动动画
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.classList.add('fade-in');
                            observer.unobserve(entry.target);
                        }
                    });
                }, {
                    threshold: 0.1
                });
                
                faqItems.forEach(item => {
                    observer.observe(item);
                });
            });

            // 创建星星
            function createStars(count) {
                const starsContainer = document.querySelector('.stars-container');
                const fragment = document.createDocumentFragment();
                
                for (let i = 0; i < count; i++) {
                    const star = document.createElement('div');
                    star.className = 'star';
                    
                    // 随机位置
                    const x = Math.random() * 100;
                    const y = Math.random() * 100;
                    
                    // 随机大小
                    const size = Math.random() * 3 + 1;
                    
                    // 随机动画持续时间和延迟
                    const duration = Math.random() * 3 + 2;
                    const delay = Math.random() * 3;
                    const opacity = Math.random() * 0.5 + 0.3;
                    
                    star.style.cssText = `
                        left: ${x}%;
                        top: ${y}%;
                        width: ${size}px;
                        height: ${size}px;
                        --duration: ${duration}s;
                        --delay: ${delay}s;
                        --opacity: ${opacity};
                    `;
                    
                    fragment.appendChild(star);
                }
                
                starsContainer.appendChild(fragment);
            }
            
            // 创建流星
            function createShootingStar() {
                const starsContainer = document.querySelector('.stars-container');
                const star = document.createElement('div');
                star.className = 'shooting-star';
                
                // 随机位置和延迟
                const delay = Math.random() * 15;
                const top = Math.random() * 50;
                
                star.style.cssText = `
                    top: ${top}%;
                    --delay: ${delay}s;
                `;
                
                starsContainer.appendChild(star);
                
                // 动画结束后移除元素
                star.addEventListener('animationend', () => {
                    star.remove();
                });
            }
            
            // 初始化星星
            createStars(150);
            
            // 定期创建流星
            setInterval(createShootingStar, 8000);
        </script>
    </body>
</html> 