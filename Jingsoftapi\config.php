<?php


return [
    "version" => "1.0.0",
    "min_system_version" => "2.12.0",
    "category_name" => "功能插件",
    "logo" => "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0ibHVjaWRlIGx1Y2lkZS1hcGkiPjxwYXRoIGQ9Im0xMiAzLTEuOTEyIDUuODEzYTIgMiAwIDAgMS0xLjI3NSAxLjI3NUwzIDEybDUuODEzIDEuOTEyYTIgMiAwIDAgMSAxLjI3NSAxLjI3NUwxMiAyMWwxLjkxMi01LjgxM2EyIDIgMCAwIDEgMS4yNzUtMS4yNzVMMjEgMTJsLTUuODEzLTEuOTEyYTIgMiAwIDAgMS0xLjI3NS0xLjI3NUwxMiAzWiIvPjxwYXRoIGQ9Im0yIDJoMnYySDJWMnoiLz48L3N2Zz4=",
    "name" => "API接口管理",
    "description" => "提供API接口管理功能，支持接口开关控制和访问密钥管理",
    "menu" => [
        [
            'tag' => 'iframe',
            'name' => 'API管理',
            'src' => (string)plugin_url("Jingsoftapi/Api/index", [], false, true),
        ]
    ],
    "hook" => [
        'SimpleCommand' => 'plugin\Jingsoftapi\Hook::handle',
    ],
];