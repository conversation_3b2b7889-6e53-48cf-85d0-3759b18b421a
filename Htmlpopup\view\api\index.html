<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>HTML弹窗预览</title>
    <style>
        .preview-container {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
        }

        .preview-card {
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            background: #fff;
        }

        .preview-title {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e4e7ed;
        }

        .preview-content {
            min-height: 200px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            padding: 15px;
            background: #fafafa;
        }

        .info-box {
            background: #f0f9ff;
            border: 1px solid #bfdbfe;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 20px;
            color: #1e40af;
        }

        .warning-box {
            background: #fef3cd;
            border: 1px solid #fde68a;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 20px;
            color: #92400e;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="preview-container">
            <div class="info-box">
                <strong>📋 弹窗预览</strong><br>
                此页面仅用于预览管理员配置的弹窗模板，无法进行编辑。如需修改弹窗内容，请联系管理员在后台进行配置。
            </div>

            <div class="warning-box" v-if="!currentTemplate">
                <strong>⚠️ 提示</strong><br>
                管理员尚未配置弹窗模板，或当前模板不可用。请联系管理员进行配置。
            </div>

            <div class="preview-card" v-if="currentTemplate">
                <div class="preview-title">
                    {{ currentTemplate.name || '弹窗预览' }}
                </div>
                <div class="preview-content"
                     :style="enableScrollbar ? 'max-height: 400px; overflow-y: auto; border: 1px solid #dcdfe6;' : 'height: auto; overflow: visible; border: 1px solid #dcdfe6;'"
                     v-html="currentTemplate.content">
                </div>
            </div>

            <div class="info-box" v-if="currentTemplate">
                <strong>ℹ️ 说明</strong><br>
                • 此预览展示的是管理员配置的弹窗内容<br>
                • 滚动条设置：{{ enableScrollbar ? '已开启（内容超出时显示滚动条）' : '已关闭（显示全部内容）' }}<br>
                • 实际弹窗效果可能因浏览器环境而略有差异<br>
                • 如需修改内容，请联系管理员操作
            </div>
        </div>

        <!-- Vue.js 脚本 -->
        <script src="/static/others/vue/vue.global.prod.js"></script>
        <script src="/static/others/element-plus/index.full.min.js"></script>
        <script>
            // 检查依赖是否加载成功
            if (typeof Vue === 'undefined') {
                document.body.innerHTML = '<div style="text-align:center;padding:50px;color:red;">Vue.js 加载失败，请检查网络连接</div>';
            } else if (typeof ElementPlus === 'undefined') {
                document.body.innerHTML = '<div style="text-align:center;padding:50px;color:red;">Element Plus 加载失败，请检查网络连接</div>';
            } else {
                const { createApp } = Vue;

                createApp({
                    data() {
                        return {
                            currentTemplate: null,
                            loading: false,
                            enableScrollbar: false
                        };
                    },
                    mounted() {
                        this.loadTemplate();
                    },
                    methods: {
                        async loadTemplate() {
                            this.loading = true;
                            try {
                                const response = await fetch('/plugin/Htmlpopup/api/fetchData', {
                                    method: 'GET',
                                    credentials: 'same-origin',
                                    headers: {
                                        'X-Requested-With': 'XMLHttpRequest'
                                    }
                                });

                                if (response.ok) {
                                    const result = await response.json();
                                    if (result.code === 200 && result.data) {
                                        this.currentTemplate = {
                                            name: '当前弹窗模板',
                                            content: result.data.content || '暂无内容'
                                        };
                                        this.enableScrollbar = result.data.enable_scrollbar || false;
                                    } else {
                                        this.currentTemplate = null;
                                    }
                                } else {
                                    this.currentTemplate = null;
                                }
                            } catch (error) {
                                this.currentTemplate = null;
                            } finally {
                                this.loading = false;
                            }
                        }
                    }
                }).use(ElementPlus).mount('#app');
            }
        </script>
</body>
</html>
