var O=Object.create;var f=Object.defineProperty;var h=Object.getOwnPropertyDescriptor;var C=Object.getOwnPropertyNames;var g=Object.getPrototypeOf,_=Object.prototype.hasOwnProperty;var I=(e,t)=>{for(var i in t)f(e,i,{get:t[i],enumerable:!0})},b=(e,t,i,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let l of C(t))!_.call(e,l)&&l!==i&&f(e,l,{get:()=>t[l],enumerable:!(o=h(t,l))||o.enumerable});return e};var w=(e,t,i)=>(i=e!=null?O(g(e)):{},b(t||!e||!e.__esModule?f(i,"default",{value:e,enumerable:!0}):i,e)),M=e=>b(f({},"__esModule",{value:!0}),e);var A={};I(A,{getDirectionality:()=>N,getSlottedTextContent:()=>c,isContentEditable:()=>D,isInShadowTree:()=>x,isInclusive:()=>v,isNamespaceDeclared:()=>k,isPreceding:()=>P,selectorToNodeProps:()=>S});module.exports=M(A);var E=w(require("bidi-js"),1),r=require("./constant.js");const x=(e={})=>{let t;if(e.nodeType===r.ELEMENT_NODE||e.nodeType===r.DOCUMENT_FRAGMENT_NODE){let i=e;for(;i;){const{host:o,mode:l,nodeType:n,parentNode:p}=i;if(o&&l&&n===r.DOCUMENT_FRAGMENT_NODE&&r.REG_SHADOW_MODE.test(l)){t=!0;break}i=p}}return!!t},c=(e={})=>{let t;if(e.localName==="slot"&&x(e)){const i=e.assignedNodes();if(i.length){for(const o of i)if(t=o.textContent.trim(),t)break}else t=e.textContent.trim()}return t??null},N=(e={})=>{let t;if(e.nodeType===r.ELEMENT_NODE){const{dir:i,localName:o,parentNode:l}=e,{getEmbeddingLevels:n}=(0,E.default)(),p=/^(?:ltr|rtl)$/;if(p.test(i))t=i;else if(i==="auto"){let s;switch(o){case"input":{(!e.type||/^(?:(?:butto|hidde)n|(?:emai|te|ur)l|(?:rese|submi|tex)t|password|search)$/.test(e.type))&&(s=e.value);break}case"slot":{s=c(e);break}case"textarea":{s=e.value;break}default:{const a=[].slice.call(e.childNodes);for(const u of a){const{dir:m,localName:T,nodeType:d,textContent:y}=u;if(d===r.TEXT_NODE?s=y.trim():d===r.ELEMENT_NODE&&!/^(?:bdi|s(?:cript|tyle)|textarea)$/.test(T)&&(!m||!p.test(m))&&(T==="slot"?s=c(u):s=y.trim()),s)break}}}if(s){const{paragraphs:[{level:a}]}=n(s);a%2===1?t="rtl":t="ltr"}if(!t)if(l){const{nodeType:a}=l;a===r.ELEMENT_NODE?t=N(l):(a===r.DOCUMENT_NODE||a===r.DOCUMENT_FRAGMENT_NODE)&&(t="ltr")}else t="ltr"}else if(o==="bdi"){const s=e.textContent.trim();if(s){const{paragraphs:[{level:a}]}=n(s);a%2===1?t="rtl":t="ltr"}t||l||(t="ltr")}else if(o==="input"&&e.type==="tel")t="ltr";else if(l){if(o==="slot"){const s=c(e);if(s){const{paragraphs:[{level:a}]}=n(s);a%2===1?t="rtl":t="ltr"}}if(!t){const{nodeType:s}=l;s===r.ELEMENT_NODE?t=N(l):(s===r.DOCUMENT_NODE||s===r.DOCUMENT_FRAGMENT_NODE)&&(t="ltr")}}else t="ltr"}return t??null},D=(e={})=>{let t;if(e.nodeType===r.ELEMENT_NODE){if(typeof e.isContentEditable=="boolean")t=e.isContentEditable;else if(e.ownerDocument.designMode==="on")t=!0;else if(e.hasAttribute("contenteditable")){const i=e.getAttribute("contenteditable");if(i===""||/^(?:plaintext-only|true)$/.test(i))t=!0;else if(i==="inherit"){let o=e.parentNode;for(;o;){if(D(o)){t=!0;break}o=o.parentNode}}}}return!!t},k=(e="",t={})=>{let i;if(e&&typeof e=="string"&&t.nodeType===r.ELEMENT_NODE){const o=`xmlns:${e}`,l=t.ownerDocument.documentElement;let n=t;for(;n;){if(typeof n.hasAttribute=="function"&&n.hasAttribute(o)){i=!0;break}else if(n===l)break;n=n.parentNode}}return!!i},v=(e={},t={})=>{let i;if(e.nodeType===r.ELEMENT_NODE&&t.nodeType===r.ELEMENT_NODE){const o=t.compareDocumentPosition(e);i=o&r.DOCUMENT_POSITION_CONTAINS||o&r.DOCUMENT_POSITION_CONTAINED_BY}return!!i},P=(e={},t={})=>{let i;if(e.nodeType===r.ELEMENT_NODE&&t.nodeType===r.ELEMENT_NODE){const o=t.compareDocumentPosition(e);i=o&r.DOCUMENT_POSITION_PRECEDING||o&r.DOCUMENT_POSITION_CONTAINS}return!!i},S=(e,t)=>{let i,o;if(e&&typeof e=="string")e.indexOf("|")>-1?[i,o]=e.split("|"):(i="*",o=e);else throw new DOMException(`Invalid selector ${e}`,r.SYNTAX_ERR);return{prefix:i,tagName:o}};0&&(module.exports={getDirectionality,getSlottedTextContent,isContentEditable,isInShadowTree,isInclusive,isNamespaceDeclared,isPreceding,selectorToNodeProps});
//# sourceMappingURL=dom-util.js.map
