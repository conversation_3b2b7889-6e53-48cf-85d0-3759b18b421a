<?php
namespace plugin\MerchantChat\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;

class Api extends BasePlugin
{
    protected $noNeedLogin = ['fetchData'];
    
    // 添加场景配置
    protected $scene = [
        'user'
    ];

    public function index()
    {
        // 传递当前登录用户信息到模板
        View::assign([
            'merchant_id' => $this->user->id ?? 0,
            'shop_name' => $this->user->nickname ?? ''
        ]);
        return View::fetch();
    }

    public function fetchData()
    {
        try {
            // 获取商家ID
            $merchant_id = $this->request->param('merchant_id', 0, 'intval');
            $shop_name = $this->request->param('shop_name', '', 'trim');
            
            // 如果没有merchant_id但有shop_name，通过shop_name查找merchant_id
            if (!$merchant_id && $shop_name) {
                $user = \think\facade\Db::name('user')
                    ->where('nickname', $shop_name)
                    ->field('id')
                    ->find();
                
                if ($user) {
                    $merchant_id = $user['id'];
                }
            }

            if (!$merchant_id) {
                return json([
                    'code' => 0,
                    'msg' => '未找到商家信息'
                ]);
            }

            // 获取商家配置
            $merchant_status = merchant_plugconf($merchant_id, "MerchantChat.status");
            
            // 获取商家权限设置 - 获取原始值然后明确转为整数
            $merchant_can_edit_raw = plugconf("MerchantChat.merchant_can_edit");
            $merchant_can_edit = $merchant_can_edit_raw === null ? 1 : intval($merchant_can_edit_raw);
            
            // 记录日志
            \think\facade\Log::info("商家权限原始值: " . var_export($merchant_can_edit_raw, true));
            \think\facade\Log::info("商家权限处理后值: " . $merchant_can_edit);
            
            // 如果商家未开启或未设置，则获取后台默认配置
            if ($merchant_status === null || intval($merchant_status) !== 1) {
                $data = [
                    'status' => intval(plugconf("MerchantChat.default_status") ?? 0),
                    'business_id' => (string)(plugconf("MerchantChat.default_business_id") ?? ''),
                    'token' => (string)(plugconf("MerchantChat.default_token") ?? ''),
                    'script_url' => (string)(plugconf("MerchantChat.default_script_url") ?? 'https://kf.y1yun.top/assets/front/ymwl_online.js'),
                    'script_version' => (string)(plugconf("MerchantChat.default_script_version") ?? ''),
                    'merchant_can_edit' => $merchant_can_edit
                ];
            } else {
                // 使用商家自己的配置
                $data = [
                    'status' => intval($merchant_status),
                    'business_id' => (string)(merchant_plugconf($merchant_id, "MerchantChat.business_id") ?? ''),
                    'token' => (string)(merchant_plugconf($merchant_id, "MerchantChat.token") ?? ''),
                    'script_url' => (string)(merchant_plugconf($merchant_id, "MerchantChat.script_url") ?? 'https://kf.y1yun.top/assets/front/ymwl_online.js'),
                    'script_version' => (string)(merchant_plugconf($merchant_id, "MerchantChat.script_version") ?? (string)(plugconf("MerchantChat.default_script_version") ?? '')),
                    'merchant_can_edit' => $merchant_can_edit
                ];
            }

            return json([
                'code' => 200,
                'msg' => 'success',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            \think\facade\Log::error('FetchData error: ' . $e->getMessage());
            return json(['code' => 0, 'msg' => '获取数据失败：' . $e->getMessage()]);
        }
    }

    public function save()
    {
        try {
            // 判断是否开启了商家修改权限
            $merchantCanEdit = plugconf("MerchantChat.merchant_can_edit");
            $merchantCanEdit = $merchantCanEdit === null ? 1 : intval($merchantCanEdit);
            
            if ($merchantCanEdit !== 1) {
                return json(['code' => 403, 'msg' => '管理员已禁止商家修改聊天配置']);
            }
            
            if (!$this->user || !$this->user->id) {
                return json(['code' => 403, 'msg' => '请先登录']);
            }

            $merchant_id = $this->user->id;
            
            // 获取并验证基础参数
            $params = $this->validateParams();
            if (!$params['success']) {
                return json(['code' => 0, 'msg' => $params['message']]);
            }

            // 保存配置
            $this->saveConfigurations($merchant_id, $params['data']);

            return json(['code' => 200, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            \think\facade\Log::error('Save error: ' . $e->getMessage());
            return json(['code' => 0, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    /**
     * 验证参数
     * @return array
     */
    private function validateParams() {
        // 判断是否开启了商家修改权限
        $merchantCanEdit = plugconf("MerchantChat.merchant_can_edit");
        $merchantCanEdit = $merchantCanEdit === null ? 1 : intval($merchantCanEdit);
        
        if ($merchantCanEdit !== 1) {
            return ['success' => false, 'message' => '管理员已禁止商家修改聊天配置'];
        }

        $params = [
            'status' => $this->request->post('status/d', 0),
            'business_id' => $this->request->post('business_id', '', 'trim'),
            'token' => $this->request->post('token', '', 'trim'),
            'script_url' => $this->request->post('script_url', 'https://kf.y1yun.top/assets/front/ymwl_online.js', 'trim'),
            'script_version' => $this->request->post('script_version', '', 'trim')
        ];

        // 验证必填项
        if (empty($params['business_id']) && $params['status'] == 1) {
            return ['success' => false, 'message' => '请输入业务ID'];
        }

        if (empty($params['token']) && $params['status'] == 1) {
            return ['success' => false, 'message' => '请输入Token'];
        }

        if (empty($params['script_url']) && $params['status'] == 1) {
            return ['success' => false, 'message' => '请输入脚本URL'];
        }

        return ['success' => true, 'data' => $params];
    }

    /**
     * 保存配置
     * @param int $merchant_id
     * @param array $data
     */
    private function saveConfigurations($merchant_id, $data) {
        merchant_plugconf($merchant_id, "MerchantChat.status", $data['status']);
        merchant_plugconf($merchant_id, "MerchantChat.business_id", $data['business_id']);
        merchant_plugconf($merchant_id, "MerchantChat.token", $data['token']);
        merchant_plugconf($merchant_id, "MerchantChat.script_url", $data['script_url']);
        merchant_plugconf($merchant_id, "MerchantChat.script_version", $data['script_version']);
    }
}