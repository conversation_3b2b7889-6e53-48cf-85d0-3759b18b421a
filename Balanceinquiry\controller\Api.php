<?php

namespace plugin\Balanceinquiry\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;

class Api extends BasePlugin {

    protected $scene = ['admin'];  // 只允许用户访问
    
    protected $noNeedLogin = [];

    // 查询页面
    public function index() {
        return View::fetch();
    }

    // 获取所有用户余额信息
    public function getBalance() {
        try {
            $search = input('search', '');
            $page = input('page/d', 1);
            $limit = input('limit/d', 10);
            
            // 构建查询
            $query = Db::name('user')
                ->field(['username', 'platform_money'])
                ->order('platform_money', 'desc');
            
            // 如果有搜索关键词，添加搜索条件
            if (!empty($search)) {
                $query->where('username', 'like', "%{$search}%");
            }
            
            // 获取总数
            $total = $query->count();
            
            // 执行分页查询
            $userList = $query->page($page, $limit)->select()->toArray();

            if (empty($userList)) {
                return json(['code' => 404, 'msg' => '没有用户数据']);
            }

            // 计算总余额
            $totalBalance = Db::name('user')->sum('platform_money');

            // 格式化数据
            $formattedList = array_map(function($user) {
                return [
                    'username' => $user['username'],
                    'balance' => number_format(floatval($user['platform_money']), 2)
                ];
            }, $userList);

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $formattedList,
                'total' => $total,
                'totalBalance' => number_format($totalBalance, 2)
            ]);
            
        } catch (\Exception $e) {
            // 记录错误日志
            \think\facade\Log::error('余额查询失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    // 获取用户资金明细
    public function getMoneyLog() {
        try {
            $username = input('username', '');
            $search = input('search', '');
            $page = input('page/d', 1);
            $limit = input('limit/d', 20);
            
            if (empty($username)) {
                return json(['code' => 400, 'msg' => '请提供用户名']);
            }

            // 先通过用户名查询用户ID
            $user = Db::name('user')
                ->where('username', $username)
                ->field(['id'])
                ->find();

            if (empty($user)) {
                return json(['code' => 404, 'msg' => '未找到该用户']);
            }

            $query = Db::name('user_money_log')
                ->where([
                    'user_id' => $user['id'],
                    'source' => 'Platform'
                ])
                ->field([
                    'id',
                    'user_id',
                    'reason',
                    'create_time',
                    'change',
                    'source'
                ]);

            // 添加搜索条件
            if (!empty($search)) {
                $query->where('reason', 'like', "%{$search}%");
            }

            // 获取总数
            $total = $query->count();

            // 执行分页查询
            $logs = $query->order('create_time', 'desc')
                ->page($page, $limit)
                ->select()
                ->toArray();

            if (empty($logs)) {
                return json(['code' => 404, 'msg' => '没有找到资金记录']);
            }

            // 格式化数据
            $formattedLogs = array_map(function($log) {
                return [
                    'reason' => $log['reason'],
                    'create_time' => date('Y-m-d H:i:s', $log['create_time']),
                    'change' => number_format(floatval($log['change']), 2),
                    'source' => $log['source']
                ];
            }, $logs);

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $formattedLogs,
                'total' => $total
            ]);

        } catch (\Exception $e) {
            \think\facade\Log::error('资金明细查询失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }
} 