"use strict";var e,o,a=require("@csstools/css-tokenizer"),n=require("@csstools/color-helpers"),t=require("@csstools/css-parser-algorithms"),r=require("@csstools/css-calc");function convertNaNToZero(e){return[Number.isNaN(e[0])?0:e[0],Number.isNaN(e[1])?0:e[1],Number.isNaN(e[2])?0:e[2]]}function colorData_to_XYZ_D50(e){switch(e.colorNotation){case exports.ColorNotation.HEX:case exports.ColorNotation.RGB:case exports.ColorNotation.sRGB:return{...e,colorNotation:exports.ColorNotation.XYZ_D50,channels:n.sRGB_to_XYZ_D50(convertNaNToZero(e.channels))};case exports.ColorNotation.Linear_sRGB:return{...e,colorNotation:exports.ColorNotation.XYZ_D50,channels:n.lin_sRGB_to_XYZ_D50(convertNaNToZero(e.channels))};case exports.ColorNotation.Display_P3:return{...e,colorNotation:exports.ColorNotation.XYZ_D50,channels:n.P3_to_XYZ_D50(convertNaNToZero(e.channels))};case exports.ColorNotation.Rec2020:return{...e,colorNotation:exports.ColorNotation.XYZ_D50,channels:n.rec_2020_to_XYZ_D50(convertNaNToZero(e.channels))};case exports.ColorNotation.A98_RGB:return{...e,colorNotation:exports.ColorNotation.XYZ_D50,channels:n.a98_RGB_to_XYZ_D50(convertNaNToZero(e.channels))};case exports.ColorNotation.ProPhoto_RGB:return{...e,colorNotation:exports.ColorNotation.XYZ_D50,channels:n.ProPhoto_RGB_to_XYZ_D50(convertNaNToZero(e.channels))};case exports.ColorNotation.HSL:return{...e,colorNotation:exports.ColorNotation.XYZ_D50,channels:n.HSL_to_XYZ_D50(convertNaNToZero(e.channels))};case exports.ColorNotation.HWB:return{...e,colorNotation:exports.ColorNotation.XYZ_D50,channels:n.HWB_to_XYZ_D50(convertNaNToZero(e.channels))};case exports.ColorNotation.Lab:return{...e,colorNotation:exports.ColorNotation.XYZ_D50,channels:n.Lab_to_XYZ_D50(convertNaNToZero(e.channels))};case exports.ColorNotation.OKLab:return{...e,colorNotation:exports.ColorNotation.XYZ_D50,channels:n.OKLab_to_XYZ_D50(convertNaNToZero(e.channels))};case exports.ColorNotation.LCH:return{...e,colorNotation:exports.ColorNotation.XYZ_D50,channels:n.LCH_to_XYZ_D50(convertNaNToZero(e.channels))};case exports.ColorNotation.OKLCH:return{...e,colorNotation:exports.ColorNotation.XYZ_D50,channels:n.OKLCH_to_XYZ_D50(convertNaNToZero(e.channels))};case exports.ColorNotation.XYZ_D50:return{...e,colorNotation:exports.ColorNotation.XYZ_D50,channels:n.XYZ_D50_to_XYZ_D50(convertNaNToZero(e.channels))};case exports.ColorNotation.XYZ_D65:return{...e,colorNotation:exports.ColorNotation.XYZ_D50,channels:n.XYZ_D65_to_XYZ_D50(convertNaNToZero(e.channels))};default:throw new Error("Unsupported color notation")}}exports.ColorNotation=void 0,(e=exports.ColorNotation||(exports.ColorNotation={})).A98_RGB="a98-rgb",e.Display_P3="display-p3",e.HEX="hex",e.HSL="hsl",e.HWB="hwb",e.LCH="lch",e.Lab="lab",e.Linear_sRGB="srgb-linear",e.OKLCH="oklch",e.OKLab="oklab",e.ProPhoto_RGB="prophoto-rgb",e.RGB="rgb",e.sRGB="srgb",e.Rec2020="rec2020",e.XYZ_D50="xyz-d50",e.XYZ_D65="xyz-d65",exports.SyntaxFlag=void 0,(o=exports.SyntaxFlag||(exports.SyntaxFlag={})).ColorKeyword="color-keyword",o.HasAlpha="has-alpha",o.HasDimensionValues="has-dimension-values",o.HasNoneKeywords="has-none-keywords",o.HasNumberValues="has-number-values",o.HasPercentageAlpha="has-percentage-alpha",o.HasPercentageValues="has-percentage-values",o.HasVariableAlpha="has-variable-alpha",o.Hex="hex",o.LegacyHSL="legacy-hsl",o.LegacyRGB="legacy-rgb",o.NamedColor="named-color",o.RelativeColorSyntax="relative-color-syntax",o.ColorMix="color-mix",o.ColorMixVariadic="color-mix-variadic",o.ContrastColor="contrast-color",o.Experimental="experimental";const s=new Set([exports.ColorNotation.A98_RGB,exports.ColorNotation.Display_P3,exports.ColorNotation.HEX,exports.ColorNotation.Linear_sRGB,exports.ColorNotation.ProPhoto_RGB,exports.ColorNotation.RGB,exports.ColorNotation.sRGB,exports.ColorNotation.Rec2020,exports.ColorNotation.XYZ_D50,exports.ColorNotation.XYZ_D65]);function colorDataTo(e,o){const a={...e};if(e.colorNotation!==o){const e=colorData_to_XYZ_D50(a);switch(o){case exports.ColorNotation.HEX:case exports.ColorNotation.RGB:a.colorNotation=exports.ColorNotation.RGB,a.channels=n.XYZ_D50_to_sRGB(e.channels);break;case exports.ColorNotation.sRGB:a.colorNotation=exports.ColorNotation.sRGB,a.channels=n.XYZ_D50_to_sRGB(e.channels);break;case exports.ColorNotation.Linear_sRGB:a.colorNotation=exports.ColorNotation.Linear_sRGB,a.channels=n.XYZ_D50_to_lin_sRGB(e.channels);break;case exports.ColorNotation.Display_P3:a.colorNotation=exports.ColorNotation.Display_P3,a.channels=n.XYZ_D50_to_P3(e.channels);break;case exports.ColorNotation.Rec2020:a.colorNotation=exports.ColorNotation.Rec2020,a.channels=n.XYZ_D50_to_rec_2020(e.channels);break;case exports.ColorNotation.ProPhoto_RGB:a.colorNotation=exports.ColorNotation.ProPhoto_RGB,a.channels=n.XYZ_D50_to_ProPhoto(e.channels);break;case exports.ColorNotation.A98_RGB:a.colorNotation=exports.ColorNotation.A98_RGB,a.channels=n.XYZ_D50_to_a98_RGB(e.channels);break;case exports.ColorNotation.HSL:a.colorNotation=exports.ColorNotation.HSL,a.channels=n.XYZ_D50_to_HSL(e.channels);break;case exports.ColorNotation.HWB:a.colorNotation=exports.ColorNotation.HWB,a.channels=n.XYZ_D50_to_HWB(e.channels);break;case exports.ColorNotation.Lab:a.colorNotation=exports.ColorNotation.Lab,a.channels=n.XYZ_D50_to_Lab(e.channels);break;case exports.ColorNotation.LCH:a.colorNotation=exports.ColorNotation.LCH,a.channels=n.XYZ_D50_to_LCH(e.channels);break;case exports.ColorNotation.OKLCH:a.colorNotation=exports.ColorNotation.OKLCH,a.channels=n.XYZ_D50_to_OKLCH(e.channels);break;case exports.ColorNotation.OKLab:a.colorNotation=exports.ColorNotation.OKLab,a.channels=n.XYZ_D50_to_OKLab(e.channels);break;case exports.ColorNotation.XYZ_D50:a.colorNotation=exports.ColorNotation.XYZ_D50,a.channels=n.XYZ_D50_to_XYZ_D50(e.channels);break;case exports.ColorNotation.XYZ_D65:a.colorNotation=exports.ColorNotation.XYZ_D65,a.channels=n.XYZ_D50_to_XYZ_D65(e.channels);break;default:throw new Error("Unsupported color notation")}}else a.channels=convertNaNToZero(e.channels);if(o===e.colorNotation)a.channels=carryForwardMissingComponents(e.channels,[0,1,2],a.channels,[0,1,2]);else if(s.has(o)&&s.has(e.colorNotation))a.channels=carryForwardMissingComponents(e.channels,[0,1,2],a.channels,[0,1,2]);else switch(o){case exports.ColorNotation.HSL:switch(e.colorNotation){case exports.ColorNotation.HWB:a.channels=carryForwardMissingComponents(e.channels,[0],a.channels,[0]);break;case exports.ColorNotation.Lab:case exports.ColorNotation.OKLab:a.channels=carryForwardMissingComponents(e.channels,[2],a.channels,[0]);break;case exports.ColorNotation.LCH:case exports.ColorNotation.OKLCH:a.channels=carryForwardMissingComponents(e.channels,[0,1,2],a.channels,[2,1,0])}break;case exports.ColorNotation.HWB:switch(e.colorNotation){case exports.ColorNotation.HSL:a.channels=carryForwardMissingComponents(e.channels,[0],a.channels,[0]);break;case exports.ColorNotation.LCH:case exports.ColorNotation.OKLCH:a.channels=carryForwardMissingComponents(e.channels,[0],a.channels,[2])}break;case exports.ColorNotation.Lab:case exports.ColorNotation.OKLab:switch(e.colorNotation){case exports.ColorNotation.HSL:a.channels=carryForwardMissingComponents(e.channels,[0],a.channels,[2]);break;case exports.ColorNotation.Lab:case exports.ColorNotation.OKLab:a.channels=carryForwardMissingComponents(e.channels,[0,1,2],a.channels,[0,1,2]);break;case exports.ColorNotation.LCH:case exports.ColorNotation.OKLCH:a.channels=carryForwardMissingComponents(e.channels,[0],a.channels,[0])}break;case exports.ColorNotation.LCH:case exports.ColorNotation.OKLCH:switch(e.colorNotation){case exports.ColorNotation.HSL:a.channels=carryForwardMissingComponents(e.channels,[0,1,2],a.channels,[2,1,0]);break;case exports.ColorNotation.HWB:a.channels=carryForwardMissingComponents(e.channels,[0],a.channels,[2]);break;case exports.ColorNotation.Lab:case exports.ColorNotation.OKLab:a.channels=carryForwardMissingComponents(e.channels,[0],a.channels,[0]);break;case exports.ColorNotation.LCH:case exports.ColorNotation.OKLCH:a.channels=carryForwardMissingComponents(e.channels,[0,1,2],a.channels,[0,1,2])}}return a.channels=convertPowerlessComponentsToMissingComponents(a.channels,o),a}function convertPowerlessComponentsToMissingComponents(e,o){const a=[...e];switch(o){case exports.ColorNotation.HSL:!Number.isNaN(a[1])&&reducePrecision(a[1],4)<=0&&(a[0]=Number.NaN);break;case exports.ColorNotation.HWB:Math.max(0,reducePrecision(a[1],4))+Math.max(0,reducePrecision(a[2],4))>=100&&(a[0]=Number.NaN);break;case exports.ColorNotation.LCH:!Number.isNaN(a[1])&&reducePrecision(a[1],4)<=0&&(a[2]=Number.NaN);break;case exports.ColorNotation.OKLCH:!Number.isNaN(a[1])&&reducePrecision(a[1],6)<=0&&(a[2]=Number.NaN)}return a}function convertPowerlessComponentsToZeroValuesForDisplay(e,o){const a=[...e];switch(o){case exports.ColorNotation.HSL:(reducePrecision(a[2])<=0||reducePrecision(a[2])>=100)&&(a[0]=Number.NaN,a[1]=Number.NaN),reducePrecision(a[1])<=0&&(a[0]=Number.NaN);break;case exports.ColorNotation.HWB:Math.max(0,reducePrecision(a[1]))+Math.max(0,reducePrecision(a[2]))>=100&&(a[0]=Number.NaN);break;case exports.ColorNotation.Lab:(reducePrecision(a[0])<=0||reducePrecision(a[0])>=100)&&(a[1]=Number.NaN,a[2]=Number.NaN);break;case exports.ColorNotation.LCH:reducePrecision(a[1])<=0&&(a[2]=Number.NaN),(reducePrecision(a[0])<=0||reducePrecision(a[0])>=100)&&(a[1]=Number.NaN,a[2]=Number.NaN);break;case exports.ColorNotation.OKLab:(reducePrecision(a[0])<=0||reducePrecision(a[0])>=1)&&(a[1]=Number.NaN,a[2]=Number.NaN);break;case exports.ColorNotation.OKLCH:reducePrecision(a[1])<=0&&(a[2]=Number.NaN),(reducePrecision(a[0])<=0||reducePrecision(a[0])>=1)&&(a[1]=Number.NaN,a[2]=Number.NaN)}return a}function carryForwardMissingComponents(e,o,a,n){const t=[...a];for(const a of o)Number.isNaN(e[o[a]])&&(t[n[a]]=Number.NaN);return t}function normalizeRelativeColorDataChannels(e){const o=new Map;switch(e.colorNotation){case exports.ColorNotation.RGB:case exports.ColorNotation.HEX:o.set("r",dummyNumberToken(255*e.channels[0])),o.set("g",dummyNumberToken(255*e.channels[1])),o.set("b",dummyNumberToken(255*e.channels[2])),"number"==typeof e.alpha&&o.set("alpha",dummyNumberToken(e.alpha));break;case exports.ColorNotation.HSL:o.set("h",dummyNumberToken(e.channels[0])),o.set("s",dummyNumberToken(e.channels[1])),o.set("l",dummyNumberToken(e.channels[2])),"number"==typeof e.alpha&&o.set("alpha",dummyNumberToken(e.alpha));break;case exports.ColorNotation.HWB:o.set("h",dummyNumberToken(e.channels[0])),o.set("w",dummyNumberToken(e.channels[1])),o.set("b",dummyNumberToken(e.channels[2])),"number"==typeof e.alpha&&o.set("alpha",dummyNumberToken(e.alpha));break;case exports.ColorNotation.Lab:case exports.ColorNotation.OKLab:o.set("l",dummyNumberToken(e.channels[0])),o.set("a",dummyNumberToken(e.channels[1])),o.set("b",dummyNumberToken(e.channels[2])),"number"==typeof e.alpha&&o.set("alpha",dummyNumberToken(e.alpha));break;case exports.ColorNotation.LCH:case exports.ColorNotation.OKLCH:o.set("l",dummyNumberToken(e.channels[0])),o.set("c",dummyNumberToken(e.channels[1])),o.set("h",dummyNumberToken(e.channels[2])),"number"==typeof e.alpha&&o.set("alpha",dummyNumberToken(e.alpha));break;case exports.ColorNotation.sRGB:case exports.ColorNotation.A98_RGB:case exports.ColorNotation.Display_P3:case exports.ColorNotation.Rec2020:case exports.ColorNotation.Linear_sRGB:case exports.ColorNotation.ProPhoto_RGB:o.set("r",dummyNumberToken(e.channels[0])),o.set("g",dummyNumberToken(e.channels[1])),o.set("b",dummyNumberToken(e.channels[2])),"number"==typeof e.alpha&&o.set("alpha",dummyNumberToken(e.alpha));break;case exports.ColorNotation.XYZ_D50:case exports.ColorNotation.XYZ_D65:o.set("x",dummyNumberToken(e.channels[0])),o.set("y",dummyNumberToken(e.channels[1])),o.set("z",dummyNumberToken(e.channels[2])),"number"==typeof e.alpha&&o.set("alpha",dummyNumberToken(e.alpha))}return o}function noneToZeroInRelativeColorDataChannels(e){const o=new Map(e);for(const[a,n]of e)Number.isNaN(n[4].value)&&o.set(a,dummyNumberToken(0));return o}function dummyNumberToken(e){return Number.isNaN(e)?[a.TokenType.Number,"none",-1,-1,{value:Number.NaN,type:a.NumberType.Number}]:[a.TokenType.Number,e.toString(),-1,-1,{value:e,type:a.NumberType.Number}]}function reducePrecision(e,o=7){if(Number.isNaN(e))return 0;const a=Math.pow(10,o);return Math.round(e*a)/a}function normalize(e,o,a,n){return Math.min(Math.max(e/o,a),n)}const l=/[A-Z]/g;function toLowerCaseAZ(e){return e.replace(l,(e=>String.fromCharCode(e.charCodeAt(0)+32)))}function normalize_Color_ChannelValues(e,o,n){if(a.isTokenIdent(e)&&"none"===toLowerCaseAZ(e[4].value))return n.syntaxFlags.add(exports.SyntaxFlag.HasNoneKeywords),[a.TokenType.Number,"none",e[2],e[3],{value:Number.NaN,type:a.NumberType.Number}];if(a.isTokenPercentage(e)){3!==o&&n.syntaxFlags.add(exports.SyntaxFlag.HasPercentageValues);let t=normalize(e[4].value,100,-2147483647,2147483647);return 3===o&&(t=normalize(e[4].value,100,0,1)),[a.TokenType.Number,t.toString(),e[2],e[3],{value:t,type:a.NumberType.Number}]}if(a.isTokenNumber(e)){3!==o&&n.syntaxFlags.add(exports.SyntaxFlag.HasNumberValues);let t=normalize(e[4].value,1,-2147483647,2147483647);return 3===o&&(t=normalize(e[4].value,1,0,1)),[a.TokenType.Number,t.toString(),e[2],e[3],{value:t,type:a.NumberType.Number}]}return!1}const i=new Set(["srgb","srgb-linear","display-p3","a98-rgb","prophoto-rgb","rec2020","xyz","xyz-d50","xyz-d65"]);function color$1(e,o){const n=[],s=[],l=[],u=[];let c,p,N=!1,m=!1;const h={colorNotation:exports.ColorNotation.sRGB,channels:[0,0,0],alpha:1,syntaxFlags:new Set([])};let x=n;for(let y=0;y<e.value.length;y++){let b=e.value[y];if(t.isWhitespaceNode(b)||t.isCommentNode(b))for(;t.isWhitespaceNode(e.value[y+1])||t.isCommentNode(e.value[y+1]);)y++;else if(x===n&&n.length&&(x=s),x===s&&s.length&&(x=l),t.isTokenNode(b)&&a.isTokenDelim(b.value)&&"/"===b.value[4].value){if(x===u)return!1;x=u}else{if(t.isFunctionNode(b)){if(x===u&&"var"===toLowerCaseAZ(b.getName())){h.syntaxFlags.add(exports.SyntaxFlag.HasVariableAlpha),x.push(b);continue}if(!r.mathFunctionNames.has(toLowerCaseAZ(b.getName())))return!1;const[[e]]=r.calcFromComponentValues([[b]],{censorIntoStandardRepresentableValues:!0,globals:p,precision:-1,toCanonicalUnits:!0,rawPercentages:!0});if(!e||!t.isTokenNode(e)||!a.isTokenNumeric(e.value))return!1;Number.isNaN(e.value[4].value)&&(e.value[4].value=0),b=e}if(x===n&&0===n.length&&t.isTokenNode(b)&&a.isTokenIdent(b.value)&&i.has(toLowerCaseAZ(b.value[4].value))){if(N)return!1;N=toLowerCaseAZ(b.value[4].value),h.colorNotation=colorSpaceNameToColorNotation(N),m&&(m.colorNotation!==h.colorNotation&&(m=colorDataTo(m,h.colorNotation)),c=normalizeRelativeColorDataChannels(m),p=noneToZeroInRelativeColorDataChannels(c))}else if(x===n&&0===n.length&&t.isTokenNode(b)&&a.isTokenIdent(b.value)&&"from"===toLowerCaseAZ(b.value[4].value)){if(m)return!1;if(N)return!1;for(;t.isWhitespaceNode(e.value[y+1])||t.isCommentNode(e.value[y+1]);)y++;if(y++,b=e.value[y],m=o(b),!1===m)return!1;m.syntaxFlags.has(exports.SyntaxFlag.Experimental)&&h.syntaxFlags.add(exports.SyntaxFlag.Experimental),h.syntaxFlags.add(exports.SyntaxFlag.RelativeColorSyntax)}else{if(!t.isTokenNode(b))return!1;if(a.isTokenIdent(b.value)&&c&&c.has(toLowerCaseAZ(b.value[4].value))){x.push(new t.TokenNode(c.get(toLowerCaseAZ(b.value[4].value))));continue}x.push(b)}}}if(!N)return!1;if(1!==x.length)return!1;if(1!==n.length||1!==s.length||1!==l.length)return!1;if(!t.isTokenNode(n[0])||!t.isTokenNode(s[0])||!t.isTokenNode(l[0]))return!1;if(c&&!c.has("alpha"))return!1;const y=normalize_Color_ChannelValues(n[0].value,0,h);if(!y||!a.isTokenNumber(y))return!1;const b=normalize_Color_ChannelValues(s[0].value,1,h);if(!b||!a.isTokenNumber(b))return!1;const C=normalize_Color_ChannelValues(l[0].value,2,h);if(!C||!a.isTokenNumber(C))return!1;const d=[y,b,C];if(1===u.length)if(h.syntaxFlags.add(exports.SyntaxFlag.HasAlpha),t.isTokenNode(u[0])){const e=normalize_Color_ChannelValues(u[0].value,3,h);if(!e||!a.isTokenNumber(e))return!1;d.push(e)}else h.alpha=u[0];else if(c&&c.has("alpha")){const e=normalize_Color_ChannelValues(c.get("alpha"),3,h);if(!e||!a.isTokenNumber(e))return!1;d.push(e)}return h.channels=[d[0][4].value,d[1][4].value,d[2][4].value],4===d.length&&(h.alpha=d[3][4].value),h}function colorSpaceNameToColorNotation(e){switch(e){case"srgb":return exports.ColorNotation.sRGB;case"srgb-linear":return exports.ColorNotation.Linear_sRGB;case"display-p3":return exports.ColorNotation.Display_P3;case"a98-rgb":return exports.ColorNotation.A98_RGB;case"prophoto-rgb":return exports.ColorNotation.ProPhoto_RGB;case"rec2020":return exports.ColorNotation.Rec2020;case"xyz":case"xyz-d65":return exports.ColorNotation.XYZ_D65;case"xyz-d50":return exports.ColorNotation.XYZ_D50;default:throw new Error("Unknown color space name: "+e)}}const u=new Set(["srgb","srgb-linear","display-p3","a98-rgb","prophoto-rgb","rec2020","lab","oklab","xyz","xyz-d50","xyz-d65"]),c=new Set(["hsl","hwb","lch","oklch"]),p=new Set(["shorter","longer","increasing","decreasing"]);function colorMix(e,o){let n=null,r=null,s=null,l=!1;for(let i=0;i<e.value.length;i++){const N=e.value[i];if(!t.isWhiteSpaceOrCommentNode(N)){if(t.isTokenNode(N)&&a.isTokenIdent(N.value)){if(!n&&"in"===toLowerCaseAZ(N.value[4].value)){n=N;continue}if(n&&!r){r=toLowerCaseAZ(N.value[4].value);continue}if(n&&r&&!s&&c.has(r)){s=toLowerCaseAZ(N.value[4].value);continue}if(n&&r&&s&&!l&&"hue"===toLowerCaseAZ(N.value[4].value)){l=!0;continue}return!1}return!(!t.isTokenNode(N)||!a.isTokenComma(N.value))&&(!!r&&(s||l?!!(r&&s&&l&&c.has(r)&&p.has(s))&&colorMixPolar(r,s,colorMixComponents(e.value.slice(i+1),o)):u.has(r)?colorMixRectangular(r,colorMixComponents(e.value.slice(i+1),o)):!!c.has(r)&&colorMixPolar(r,"shorter",colorMixComponents(e.value.slice(i+1),o))))}}return!1}function colorMixComponents(e,o){const n=[];let s=1,l=!1,i=!1;for(let s=0;s<e.length;s++){let u=e[s];if(!t.isWhiteSpaceOrCommentNode(u)){if(!t.isTokenNode(u)||!a.isTokenComma(u.value)){if(!l){const e=o(u);if(e){l=e;continue}}if(!i){if(t.isFunctionNode(u)&&r.mathFunctionNames.has(toLowerCaseAZ(u.getName()))){if([[u]]=r.calcFromComponentValues([[u]],{censorIntoStandardRepresentableValues:!0,precision:-1,toCanonicalUnits:!0,rawPercentages:!0}),!u||!t.isTokenNode(u)||!a.isTokenNumeric(u.value))return!1;Number.isNaN(u.value[4].value)&&(u.value[4].value=0)}if(t.isTokenNode(u)&&a.isTokenPercentage(u.value)&&u.value[4].value>=0){i=u.value[4].value;continue}}return!1}if(!l)return!1;n.push({color:l,percentage:i}),l=!1,i=!1}}l&&n.push({color:l,percentage:i});let u=0,c=0;for(let e=0;e<n.length;e++){const o=n[e].percentage;if(!1!==o){if(o<0||o>100)return!1;u+=o}else c++}const p=Math.max(0,100-u);u=0;for(let e=0;e<n.length;e++)!1===n[e].percentage&&(n[e].percentage=p/c),u+=n[e].percentage;if(0===u)return{colors:[{color:{channels:[0,0,0],colorNotation:exports.ColorNotation.sRGB,alpha:0,syntaxFlags:new Set},percentage:0}],alphaMultiplier:0};if(u>100)for(let e=0;e<n.length;e++){let o=n[e].percentage;o=o/u*100,n[e].percentage=o}if(u<100){s=u/100;for(let e=0;e<n.length;e++){let o=n[e].percentage;o=o/u*100,n[e].percentage=o}}return{colors:n,alphaMultiplier:s}}function colorMixRectangular(e,o){if(!o||!o.colors.length)return!1;const a=o.colors.slice();a.reverse();let n=exports.ColorNotation.RGB;switch(e){case"srgb":n=exports.ColorNotation.RGB;break;case"srgb-linear":n=exports.ColorNotation.Linear_sRGB;break;case"display-p3":n=exports.ColorNotation.Display_P3;break;case"a98-rgb":n=exports.ColorNotation.A98_RGB;break;case"prophoto-rgb":n=exports.ColorNotation.ProPhoto_RGB;break;case"rec2020":n=exports.ColorNotation.Rec2020;break;case"lab":n=exports.ColorNotation.Lab;break;case"oklab":n=exports.ColorNotation.OKLab;break;case"xyz-d50":n=exports.ColorNotation.XYZ_D50;break;case"xyz":case"xyz-d65":n=exports.ColorNotation.XYZ_D65;break;default:return!1}if(1===a.length){const e=colorDataTo(a[0].color,n);return e.colorNotation=n,e.syntaxFlags.add(exports.SyntaxFlag.ColorMixVariadic),"number"!=typeof e.alpha?!1:(e.alpha=e.alpha*o.alphaMultiplier,e)}for(;a.length>=2;){const e=a.pop(),o=a.pop();if(!e||!o)return!1;const t=colorMixRectangularPair(n,e.color,e.percentage,o.color,o.percentage);if(!t)return!1;a.push({color:t,percentage:e.percentage+o.percentage})}const t=a[0]?.color;return!!t&&(o.colors.some((e=>e.color.syntaxFlags.has(exports.SyntaxFlag.Experimental)))&&t.syntaxFlags.add(exports.SyntaxFlag.Experimental),"number"==typeof t.alpha&&(t.alpha=t.alpha*o.alphaMultiplier,2!==o.colors.length&&t.syntaxFlags.add(exports.SyntaxFlag.ColorMixVariadic),t))}function colorMixRectangularPair(e,o,a,n,t){const r=a/(a+t);let s=o.alpha;if("number"!=typeof s)return!1;let l=n.alpha;if("number"!=typeof l)return!1;s=Number.isNaN(s)?l:s,l=Number.isNaN(l)?s:l;const i=colorDataTo(o,e).channels,u=colorDataTo(n,e).channels;i[0]=fillInMissingComponent(i[0],u[0]),u[0]=fillInMissingComponent(u[0],i[0]),i[1]=fillInMissingComponent(i[1],u[1]),u[1]=fillInMissingComponent(u[1],i[1]),i[2]=fillInMissingComponent(i[2],u[2]),u[2]=fillInMissingComponent(u[2],i[2]),i[0]=premultiply(i[0],s),i[1]=premultiply(i[1],s),i[2]=premultiply(i[2],s),u[0]=premultiply(u[0],l),u[1]=premultiply(u[1],l),u[2]=premultiply(u[2],l);const c=interpolate(s,l,r);return{colorNotation:e,channels:[un_premultiply(interpolate(i[0],u[0],r),c),un_premultiply(interpolate(i[1],u[1],r),c),un_premultiply(interpolate(i[2],u[2],r),c)],alpha:c,syntaxFlags:new Set([exports.SyntaxFlag.ColorMix])}}function colorMixPolar(e,o,a){if(!a||!a.colors.length)return!1;const n=a.colors.slice();n.reverse();let t=exports.ColorNotation.HSL;switch(e){case"hsl":t=exports.ColorNotation.HSL;break;case"hwb":t=exports.ColorNotation.HWB;break;case"lch":t=exports.ColorNotation.LCH;break;case"oklch":t=exports.ColorNotation.OKLCH;break;default:return!1}if(1===n.length){const e=colorDataTo(n[0].color,t);return e.colorNotation=t,e.syntaxFlags.add(exports.SyntaxFlag.ColorMixVariadic),"number"!=typeof e.alpha?!1:(e.alpha=e.alpha*a.alphaMultiplier,e)}for(;n.length>=2;){const e=n.pop(),a=n.pop();if(!e||!a)return!1;const r=colorMixPolarPair(t,o,e.color,e.percentage,a.color,a.percentage);if(!r)return!1;n.push({color:r,percentage:e.percentage+a.percentage})}const r=n[0]?.color;return!!r&&(a.colors.some((e=>e.color.syntaxFlags.has(exports.SyntaxFlag.Experimental)))&&r.syntaxFlags.add(exports.SyntaxFlag.Experimental),"number"==typeof r.alpha&&(r.alpha=r.alpha*a.alphaMultiplier,2!==a.colors.length&&r.syntaxFlags.add(exports.SyntaxFlag.ColorMixVariadic),r))}function colorMixPolarPair(e,o,a,n,t,r){const s=n/(n+r);let l=0,i=0,u=0,c=0,p=0,N=0,m=a.alpha;if("number"!=typeof m)return!1;let h=t.alpha;if("number"!=typeof h)return!1;m=Number.isNaN(m)?h:m,h=Number.isNaN(h)?m:h;const x=colorDataTo(a,e).channels,y=colorDataTo(t,e).channels;switch(e){case exports.ColorNotation.HSL:case exports.ColorNotation.HWB:l=x[0],i=y[0],u=x[1],c=y[1],p=x[2],N=y[2];break;case exports.ColorNotation.LCH:case exports.ColorNotation.OKLCH:u=x[0],c=y[0],p=x[1],N=y[1],l=x[2],i=y[2]}l=fillInMissingComponent(l,i),Number.isNaN(l)&&(l=0),i=fillInMissingComponent(i,l),Number.isNaN(i)&&(i=0),u=fillInMissingComponent(u,c),c=fillInMissingComponent(c,u),p=fillInMissingComponent(p,N),N=fillInMissingComponent(N,p);const b=i-l;switch(o){case"shorter":b>180?l+=360:b<-180&&(i+=360);break;case"longer":-180<b&&b<180&&(b>0?l+=360:i+=360);break;case"increasing":b<0&&(i+=360);break;case"decreasing":b>0&&(l+=360);break;default:throw new Error("Unknown hue interpolation method")}u=premultiply(u,m),p=premultiply(p,m),c=premultiply(c,h),N=premultiply(N,h);let C=[0,0,0];const d=interpolate(m,h,s);switch(e){case exports.ColorNotation.HSL:case exports.ColorNotation.HWB:C=[interpolate(l,i,s),un_premultiply(interpolate(u,c,s),d),un_premultiply(interpolate(p,N,s),d)];break;case exports.ColorNotation.LCH:case exports.ColorNotation.OKLCH:C=[un_premultiply(interpolate(u,c,s),d),un_premultiply(interpolate(p,N,s),d),interpolate(l,i,s)]}return{colorNotation:e,channels:C,alpha:d,syntaxFlags:new Set([exports.SyntaxFlag.ColorMix])}}function fillInMissingComponent(e,o){return Number.isNaN(e)?o:e}function interpolate(e,o,a){return e*a+o*(1-a)}function premultiply(e,o){return Number.isNaN(o)?e:Number.isNaN(e)?Number.NaN:e*o}function un_premultiply(e,o){return 0===o||Number.isNaN(o)?e:Number.isNaN(e)?Number.NaN:e/o}function hex(e){const o=toLowerCaseAZ(e[4].value);if(o.match(/[^a-f0-9]/))return!1;const a={colorNotation:exports.ColorNotation.HEX,channels:[0,0,0],alpha:1,syntaxFlags:new Set([exports.SyntaxFlag.Hex])},n=o.length;if(3===n){const e=o[0],n=o[1],t=o[2];return a.channels=[parseInt(e+e,16)/255,parseInt(n+n,16)/255,parseInt(t+t,16)/255],a}if(6===n){const e=o[0]+o[1],n=o[2]+o[3],t=o[4]+o[5];return a.channels=[parseInt(e,16)/255,parseInt(n,16)/255,parseInt(t,16)/255],a}if(4===n){const e=o[0],n=o[1],t=o[2],r=o[3];return a.channels=[parseInt(e+e,16)/255,parseInt(n+n,16)/255,parseInt(t+t,16)/255],a.alpha=parseInt(r+r,16)/255,a.syntaxFlags.add(exports.SyntaxFlag.HasAlpha),a}if(8===n){const e=o[0]+o[1],n=o[2]+o[3],t=o[4]+o[5],r=o[6]+o[7];return a.channels=[parseInt(e,16)/255,parseInt(n,16)/255,parseInt(t,16)/255],a.alpha=parseInt(r,16)/255,a.syntaxFlags.add(exports.SyntaxFlag.HasAlpha),a}return!1}function normalizeHue(e){if(a.isTokenNumber(e))return e[4].value=e[4].value%360,e[1]=e[4].value.toString(),e;if(a.isTokenDimension(e)){let o=e[4].value;switch(toLowerCaseAZ(e[4].unit)){case"deg":break;case"rad":o=180*e[4].value/Math.PI;break;case"grad":o=.9*e[4].value;break;case"turn":o=360*e[4].value;break;default:return!1}return o%=360,[a.TokenType.Number,o.toString(),e[2],e[3],{value:o,type:a.NumberType.Number}]}return!1}function normalize_legacy_HSL_ChannelValues(e,o,n){if(0===o){const o=normalizeHue(e);return!1!==o&&(a.isTokenDimension(e)&&n.syntaxFlags.add(exports.SyntaxFlag.HasDimensionValues),o)}if(a.isTokenPercentage(e)){3===o?n.syntaxFlags.add(exports.SyntaxFlag.HasPercentageAlpha):n.syntaxFlags.add(exports.SyntaxFlag.HasPercentageValues);let t=normalize(e[4].value,1,0,100);return 3===o&&(t=normalize(e[4].value,100,0,1)),[a.TokenType.Number,t.toString(),e[2],e[3],{value:t,type:a.NumberType.Number}]}if(a.isTokenNumber(e)){if(3!==o)return!1;let n=normalize(e[4].value,1,0,100);return 3===o&&(n=normalize(e[4].value,1,0,1)),[a.TokenType.Number,n.toString(),e[2],e[3],{value:n,type:a.NumberType.Number}]}return!1}function normalize_modern_HSL_ChannelValues(e,o,n){if(a.isTokenIdent(e)&&"none"===toLowerCaseAZ(e[4].value))return n.syntaxFlags.add(exports.SyntaxFlag.HasNoneKeywords),[a.TokenType.Number,"none",e[2],e[3],{value:Number.NaN,type:a.NumberType.Number}];if(0===o){const o=normalizeHue(e);return!1!==o&&(a.isTokenDimension(e)&&n.syntaxFlags.add(exports.SyntaxFlag.HasDimensionValues),o)}if(a.isTokenPercentage(e)){3===o?n.syntaxFlags.add(exports.SyntaxFlag.HasPercentageAlpha):n.syntaxFlags.add(exports.SyntaxFlag.HasPercentageValues);let t=e[4].value;return 3===o?t=normalize(e[4].value,100,0,1):1===o&&(t=normalize(e[4].value,1,0,2147483647)),[a.TokenType.Number,t.toString(),e[2],e[3],{value:t,type:a.NumberType.Number}]}if(a.isTokenNumber(e)){3!==o&&n.syntaxFlags.add(exports.SyntaxFlag.HasNumberValues);let t=e[4].value;return 3===o?t=normalize(e[4].value,1,0,1):1===o&&(t=normalize(e[4].value,1,0,2147483647)),[a.TokenType.Number,t.toString(),e[2],e[3],{value:t,type:a.NumberType.Number}]}return!1}function threeChannelLegacySyntax(e,o,n,s){const l=[],i=[],u=[],c=[],p={colorNotation:n,channels:[0,0,0],alpha:1,syntaxFlags:new Set(s)};let N=l;for(let o=0;o<e.value.length;o++){let n=e.value[o];if(!t.isWhitespaceNode(n)&&!t.isCommentNode(n)){if(t.isTokenNode(n)&&a.isTokenComma(n.value)){if(N===l){N=i;continue}if(N===i){N=u;continue}if(N===u){N=c;continue}if(N===c)return!1}if(t.isFunctionNode(n)){if(N===c&&"var"===n.getName().toLowerCase()){p.syntaxFlags.add(exports.SyntaxFlag.HasVariableAlpha),N.push(n);continue}if(!r.mathFunctionNames.has(n.getName().toLowerCase()))return!1;const[[e]]=r.calcFromComponentValues([[n]],{censorIntoStandardRepresentableValues:!0,precision:-1,toCanonicalUnits:!0,rawPercentages:!0});if(!e||!t.isTokenNode(e)||!a.isTokenNumeric(e.value))return!1;Number.isNaN(e.value[4].value)&&(e.value[4].value=0),n=e}if(!t.isTokenNode(n))return!1;N.push(n)}}if(1!==N.length)return!1;if(1!==l.length||1!==i.length||1!==u.length)return!1;if(!t.isTokenNode(l[0])||!t.isTokenNode(i[0])||!t.isTokenNode(u[0]))return!1;const m=o(l[0].value,0,p);if(!m||!a.isTokenNumber(m))return!1;const h=o(i[0].value,1,p);if(!h||!a.isTokenNumber(h))return!1;const x=o(u[0].value,2,p);if(!x||!a.isTokenNumber(x))return!1;const y=[m,h,x];if(1===c.length)if(p.syntaxFlags.add(exports.SyntaxFlag.HasAlpha),t.isTokenNode(c[0])){const e=o(c[0].value,3,p);if(!e||!a.isTokenNumber(e))return!1;y.push(e)}else p.alpha=c[0];return p.channels=[y[0][4].value,y[1][4].value,y[2][4].value],4===y.length&&(p.alpha=y[3][4].value),p}function threeChannelSpaceSeparated(e,o,n,s,l){const i=[],u=[],c=[],p=[];let N,m,h=!1;const x={colorNotation:n,channels:[0,0,0],alpha:1,syntaxFlags:new Set(s)};let y=i;for(let o=0;o<e.value.length;o++){let s=e.value[o];if(t.isWhitespaceNode(s)||t.isCommentNode(s))for(;t.isWhitespaceNode(e.value[o+1])||t.isCommentNode(e.value[o+1]);)o++;else if(y===i&&i.length&&(y=u),y===u&&u.length&&(y=c),t.isTokenNode(s)&&a.isTokenDelim(s.value)&&"/"===s.value[4].value){if(y===p)return!1;y=p}else{if(t.isFunctionNode(s)){if(y===p&&"var"===s.getName().toLowerCase()){x.syntaxFlags.add(exports.SyntaxFlag.HasVariableAlpha),y.push(s);continue}if(!r.mathFunctionNames.has(s.getName().toLowerCase()))return!1;const[[e]]=r.calcFromComponentValues([[s]],{censorIntoStandardRepresentableValues:!0,globals:m,precision:-1,toCanonicalUnits:!0,rawPercentages:!0});if(!e||!t.isTokenNode(e)||!a.isTokenNumeric(e.value))return!1;Number.isNaN(e.value[4].value)&&(e.value[4].value=0),s=e}if(y===i&&0===i.length&&t.isTokenNode(s)&&a.isTokenIdent(s.value)&&"from"===s.value[4].value.toLowerCase()){if(h)return!1;for(;t.isWhitespaceNode(e.value[o+1])||t.isCommentNode(e.value[o+1]);)o++;if(o++,s=e.value[o],h=l(s),!1===h)return!1;h.syntaxFlags.has(exports.SyntaxFlag.Experimental)&&x.syntaxFlags.add(exports.SyntaxFlag.Experimental),x.syntaxFlags.add(exports.SyntaxFlag.RelativeColorSyntax),h.colorNotation!==n&&(h=colorDataTo(h,n)),N=normalizeRelativeColorDataChannels(h),m=noneToZeroInRelativeColorDataChannels(N)}else{if(!t.isTokenNode(s))return!1;if(a.isTokenIdent(s.value)&&N){const e=s.value[4].value.toLowerCase();if(N.has(e)){y.push(new t.TokenNode(N.get(e)));continue}}y.push(s)}}}if(1!==y.length)return!1;if(1!==i.length||1!==u.length||1!==c.length)return!1;if(!t.isTokenNode(i[0])||!t.isTokenNode(u[0])||!t.isTokenNode(c[0]))return!1;if(N&&!N.has("alpha"))return!1;const b=o(i[0].value,0,x);if(!b||!a.isTokenNumber(b))return!1;const C=o(u[0].value,1,x);if(!C||!a.isTokenNumber(C))return!1;const d=o(c[0].value,2,x);if(!d||!a.isTokenNumber(d))return!1;const g=[b,C,d];if(1===p.length)if(x.syntaxFlags.add(exports.SyntaxFlag.HasAlpha),t.isTokenNode(p[0])){const e=o(p[0].value,3,x);if(!e||!a.isTokenNumber(e))return!1;g.push(e)}else x.alpha=p[0];else if(N&&N.has("alpha")){const e=o(N.get("alpha"),3,x);if(!e||!a.isTokenNumber(e))return!1;g.push(e)}return x.channels=[g[0][4].value,g[1][4].value,g[2][4].value],4===g.length&&(x.alpha=g[3][4].value),x}function hsl(e,o){if(e.value.some((e=>t.isTokenNode(e)&&a.isTokenComma(e.value)))){const o=hslCommaSeparated(e);if(!1!==o)return o}{const a=hslSpaceSeparated(e,o);if(!1!==a)return a}return!1}function hslCommaSeparated(e){return threeChannelLegacySyntax(e,normalize_legacy_HSL_ChannelValues,exports.ColorNotation.HSL,[exports.SyntaxFlag.LegacyHSL])}function hslSpaceSeparated(e,o){return threeChannelSpaceSeparated(e,normalize_modern_HSL_ChannelValues,exports.ColorNotation.HSL,[],o)}function normalize_HWB_ChannelValues(e,o,n){if(a.isTokenIdent(e)&&"none"===toLowerCaseAZ(e[4].value))return n.syntaxFlags.add(exports.SyntaxFlag.HasNoneKeywords),[a.TokenType.Number,"none",e[2],e[3],{value:Number.NaN,type:a.NumberType.Number}];if(0===o){const o=normalizeHue(e);return!1!==o&&(a.isTokenDimension(e)&&n.syntaxFlags.add(exports.SyntaxFlag.HasDimensionValues),o)}if(a.isTokenPercentage(e)){3===o?n.syntaxFlags.add(exports.SyntaxFlag.HasPercentageAlpha):n.syntaxFlags.add(exports.SyntaxFlag.HasPercentageValues);let t=e[4].value;return 3===o&&(t=normalize(e[4].value,100,0,1)),[a.TokenType.Number,t.toString(),e[2],e[3],{value:t,type:a.NumberType.Number}]}if(a.isTokenNumber(e)){3!==o&&n.syntaxFlags.add(exports.SyntaxFlag.HasNumberValues);let t=e[4].value;return 3===o&&(t=normalize(e[4].value,1,0,1)),[a.TokenType.Number,t.toString(),e[2],e[3],{value:t,type:a.NumberType.Number}]}return!1}function normalize_Lab_ChannelValues(e,o,n){if(a.isTokenIdent(e)&&"none"===toLowerCaseAZ(e[4].value))return n.syntaxFlags.add(exports.SyntaxFlag.HasNoneKeywords),[a.TokenType.Number,"none",e[2],e[3],{value:Number.NaN,type:a.NumberType.Number}];if(a.isTokenPercentage(e)){3!==o&&n.syntaxFlags.add(exports.SyntaxFlag.HasPercentageValues);let t=normalize(e[4].value,1,0,100);return 1===o||2===o?t=normalize(e[4].value,.8,-2147483647,2147483647):3===o&&(t=normalize(e[4].value,100,0,1)),[a.TokenType.Number,t.toString(),e[2],e[3],{value:t,type:a.NumberType.Number}]}if(a.isTokenNumber(e)){3!==o&&n.syntaxFlags.add(exports.SyntaxFlag.HasNumberValues);let t=normalize(e[4].value,1,0,100);return 1===o||2===o?t=normalize(e[4].value,1,-2147483647,2147483647):3===o&&(t=normalize(e[4].value,1,0,1)),[a.TokenType.Number,t.toString(),e[2],e[3],{value:t,type:a.NumberType.Number}]}return!1}function lab(e,o){return threeChannelSpaceSeparated(e,normalize_Lab_ChannelValues,exports.ColorNotation.Lab,[],o)}function normalize_LCH_ChannelValues(e,o,n){if(a.isTokenIdent(e)&&"none"===toLowerCaseAZ(e[4].value))return n.syntaxFlags.add(exports.SyntaxFlag.HasNoneKeywords),[a.TokenType.Number,"none",e[2],e[3],{value:Number.NaN,type:a.NumberType.Number}];if(2===o){const o=normalizeHue(e);return!1!==o&&(a.isTokenDimension(e)&&n.syntaxFlags.add(exports.SyntaxFlag.HasDimensionValues),o)}if(a.isTokenPercentage(e)){3!==o&&n.syntaxFlags.add(exports.SyntaxFlag.HasPercentageValues);let t=normalize(e[4].value,1,0,100);return 1===o?t=normalize(e[4].value,100/150,0,2147483647):3===o&&(t=normalize(e[4].value,100,0,1)),[a.TokenType.Number,t.toString(),e[2],e[3],{value:t,type:a.NumberType.Number}]}if(a.isTokenNumber(e)){3!==o&&n.syntaxFlags.add(exports.SyntaxFlag.HasNumberValues);let t=normalize(e[4].value,1,0,100);return 1===o?t=normalize(e[4].value,1,0,2147483647):3===o&&(t=normalize(e[4].value,1,0,1)),[a.TokenType.Number,t.toString(),e[2],e[3],{value:t,type:a.NumberType.Number}]}return!1}function lch(e,o){return threeChannelSpaceSeparated(e,normalize_LCH_ChannelValues,exports.ColorNotation.LCH,[],o)}const N=new Map;for(const[e,o]of Object.entries(n.namedColors))N.set(e,o);function namedColor(e){const o=N.get(toLowerCaseAZ(e));return!!o&&{colorNotation:exports.ColorNotation.RGB,channels:[o[0]/255,o[1]/255,o[2]/255],alpha:1,syntaxFlags:new Set([exports.SyntaxFlag.ColorKeyword,exports.SyntaxFlag.NamedColor])}}function normalize_OKLab_ChannelValues(e,o,n){if(a.isTokenIdent(e)&&"none"===toLowerCaseAZ(e[4].value))return n.syntaxFlags.add(exports.SyntaxFlag.HasNoneKeywords),[a.TokenType.Number,"none",e[2],e[3],{value:Number.NaN,type:a.NumberType.Number}];if(a.isTokenPercentage(e)){3!==o&&n.syntaxFlags.add(exports.SyntaxFlag.HasPercentageValues);let t=normalize(e[4].value,100,0,1);return 1===o||2===o?t=normalize(e[4].value,250,-2147483647,2147483647):3===o&&(t=normalize(e[4].value,100,0,1)),[a.TokenType.Number,t.toString(),e[2],e[3],{value:t,type:a.NumberType.Number}]}if(a.isTokenNumber(e)){3!==o&&n.syntaxFlags.add(exports.SyntaxFlag.HasNumberValues);let t=normalize(e[4].value,1,0,1);return 1===o||2===o?t=normalize(e[4].value,1,-2147483647,2147483647):3===o&&(t=normalize(e[4].value,1,0,1)),[a.TokenType.Number,t.toString(),e[2],e[3],{value:t,type:a.NumberType.Number}]}return!1}function oklab(e,o){return threeChannelSpaceSeparated(e,normalize_OKLab_ChannelValues,exports.ColorNotation.OKLab,[],o)}function normalize_OKLCH_ChannelValues(e,o,n){if(a.isTokenIdent(e)&&"none"===toLowerCaseAZ(e[4].value))return n.syntaxFlags.add(exports.SyntaxFlag.HasNoneKeywords),[a.TokenType.Number,"none",e[2],e[3],{value:Number.NaN,type:a.NumberType.Number}];if(2===o){const o=normalizeHue(e);return!1!==o&&(a.isTokenDimension(e)&&n.syntaxFlags.add(exports.SyntaxFlag.HasDimensionValues),o)}if(a.isTokenPercentage(e)){3!==o&&n.syntaxFlags.add(exports.SyntaxFlag.HasPercentageValues);let t=normalize(e[4].value,100,0,1);return 1===o?t=normalize(e[4].value,250,0,2147483647):3===o&&(t=normalize(e[4].value,100,0,1)),[a.TokenType.Number,t.toString(),e[2],e[3],{value:t,type:a.NumberType.Number}]}if(a.isTokenNumber(e)){3!==o&&n.syntaxFlags.add(exports.SyntaxFlag.HasNumberValues);let t=normalize(e[4].value,1,0,1);return 1===o?t=normalize(e[4].value,1,0,2147483647):3===o&&(t=normalize(e[4].value,1,0,1)),[a.TokenType.Number,t.toString(),e[2],e[3],{value:t,type:a.NumberType.Number}]}return!1}function oklch(e,o){return threeChannelSpaceSeparated(e,normalize_OKLCH_ChannelValues,exports.ColorNotation.OKLCH,[],o)}function normalize_legacy_sRGB_ChannelValues(e,o,n){if(a.isTokenPercentage(e)){3===o?n.syntaxFlags.add(exports.SyntaxFlag.HasPercentageAlpha):n.syntaxFlags.add(exports.SyntaxFlag.HasPercentageValues);const t=normalize(e[4].value,100,0,1);return[a.TokenType.Number,t.toString(),e[2],e[3],{value:t,type:a.NumberType.Number}]}if(a.isTokenNumber(e)){3!==o&&n.syntaxFlags.add(exports.SyntaxFlag.HasNumberValues);let t=normalize(e[4].value,255,0,1);return 3===o&&(t=normalize(e[4].value,1,0,1)),[a.TokenType.Number,t.toString(),e[2],e[3],{value:t,type:a.NumberType.Number}]}return!1}function normalize_modern_sRGB_ChannelValues(e,o,n){if(a.isTokenIdent(e)&&"none"===e[4].value.toLowerCase())return n.syntaxFlags.add(exports.SyntaxFlag.HasNoneKeywords),[a.TokenType.Number,"none",e[2],e[3],{value:Number.NaN,type:a.NumberType.Number}];if(a.isTokenPercentage(e)){3!==o&&n.syntaxFlags.add(exports.SyntaxFlag.HasPercentageValues);let t=normalize(e[4].value,100,-2147483647,2147483647);return 3===o&&(t=normalize(e[4].value,100,0,1)),[a.TokenType.Number,t.toString(),e[2],e[3],{value:t,type:a.NumberType.Number}]}if(a.isTokenNumber(e)){3!==o&&n.syntaxFlags.add(exports.SyntaxFlag.HasNumberValues);let t=normalize(e[4].value,255,-2147483647,2147483647);return 3===o&&(t=normalize(e[4].value,1,0,1)),[a.TokenType.Number,t.toString(),e[2],e[3],{value:t,type:a.NumberType.Number}]}return!1}function rgb(e,o){if(e.value.some((e=>t.isTokenNode(e)&&a.isTokenComma(e.value)))){const o=rgbCommaSeparated(e);if(!1!==o)return(!o.syntaxFlags.has(exports.SyntaxFlag.HasNumberValues)||!o.syntaxFlags.has(exports.SyntaxFlag.HasPercentageValues))&&o}else{const a=rgbSpaceSeparated(e,o);if(!1!==a)return a}return!1}function rgbCommaSeparated(e){return threeChannelLegacySyntax(e,normalize_legacy_sRGB_ChannelValues,exports.ColorNotation.RGB,[exports.SyntaxFlag.LegacyRGB])}function rgbSpaceSeparated(e,o){return threeChannelSpaceSeparated(e,normalize_modern_sRGB_ChannelValues,exports.ColorNotation.RGB,[],o)}function XYZ_D50_to_sRGB_Gamut(e){const o=n.XYZ_D50_to_sRGB(e);if(n.inGamut(o))return n.clip(o);let a=e;return a=n.XYZ_D50_to_OKLCH(a),a[0]<1e-6&&(a=[0,0,0]),a[0]>.999999&&(a=[1,0,0]),n.gam_sRGB(n.mapGamutRayTrace(a,oklch_to_lin_srgb,lin_srgb_to_oklch))}function oklch_to_lin_srgb(e){return e=n.OKLCH_to_OKLab(e),e=n.OKLab_to_XYZ(e),n.XYZ_to_lin_sRGB(e)}function lin_srgb_to_oklch(e){return e=n.lin_sRGB_to_XYZ(e),e=n.XYZ_to_OKLab(e),n.OKLab_to_OKLCH(e)}function contrastColor(e,o){let a=!1;for(let n=0;n<e.value.length;n++){const r=e.value[n];if(!t.isWhitespaceNode(r)&&!t.isCommentNode(r)&&(a||(a=o(r),!a)))return!1}if(!a)return!1;a.channels=convertNaNToZero(a.channels),a.channels=XYZ_D50_to_sRGB_Gamut(colorData_to_XYZ_D50(a).channels),a.colorNotation=exports.ColorNotation.sRGB;const r={colorNotation:exports.ColorNotation.sRGB,channels:[0,0,0],alpha:1,syntaxFlags:new Set([exports.SyntaxFlag.ContrastColor,exports.SyntaxFlag.Experimental])},s=n.contrast_ratio_wcag_2_1(a.channels,[1,1,1]),l=n.contrast_ratio_wcag_2_1(a.channels,[0,0,0]);return r.channels=s>l?[1,1,1]:[0,0,0],r}function XYZ_D50_to_P3_Gamut(e){const o=n.XYZ_D50_to_P3(e);if(n.inGamut(o))return n.clip(o);let a=e;return a=n.XYZ_D50_to_OKLCH(a),a[0]<1e-6&&(a=[0,0,0]),a[0]>.999999&&(a=[1,0,0]),n.gam_P3(n.mapGamutRayTrace(a,oklch_to_lin_p3,lin_p3_to_oklch))}function oklch_to_lin_p3(e){return e=n.OKLCH_to_OKLab(e),e=n.OKLab_to_XYZ(e),n.XYZ_to_lin_P3(e)}function lin_p3_to_oklch(e){return e=n.lin_P3_to_XYZ(e),e=n.XYZ_to_OKLab(e),n.OKLab_to_OKLCH(e)}function toPrecision(e,o=7){e=+e,o=+o;const a=(Math.floor(Math.abs(e))+"").length;if(o>a)return+e.toFixed(o-a);{const n=10**(a-o);return Math.round(e/n)*n}}function serializeWithAlpha(e,o,n,r){const s=[a.TokenType.CloseParen,")",-1,-1,void 0];if("number"==typeof e.alpha){const l=Math.min(1,Math.max(0,toPrecision(Number.isNaN(e.alpha)?0:e.alpha)));return 1===toPrecision(l,4)?new t.FunctionNode(o,s,r):new t.FunctionNode(o,s,[...r,new t.WhitespaceNode([n]),new t.TokenNode([a.TokenType.Delim,"/",-1,-1,{value:"/"}]),new t.WhitespaceNode([n]),new t.TokenNode([a.TokenType.Number,toPrecision(l,4).toString(),-1,-1,{value:e.alpha,type:a.NumberType.Integer}])])}return new t.FunctionNode(o,s,[...r,new t.WhitespaceNode([n]),new t.TokenNode([a.TokenType.Delim,"/",-1,-1,{value:"/"}]),new t.WhitespaceNode([n]),e.alpha])}exports.color=function color(e){if(t.isFunctionNode(e)){switch(toLowerCaseAZ(e.getName())){case"rgb":case"rgba":return rgb(e,color);case"hsl":case"hsla":return hsl(e,color);case"hwb":return o=color,threeChannelSpaceSeparated(e,normalize_HWB_ChannelValues,exports.ColorNotation.HWB,[],o);case"lab":return lab(e,color);case"lch":return lch(e,color);case"oklab":return oklab(e,color);case"oklch":return oklch(e,color);case"color":return color$1(e,color);case"color-mix":return colorMix(e,color);case"contrast-color":return contrastColor(e,color)}}var o;if(t.isTokenNode(e)){if(a.isTokenHash(e.value))return hex(e.value);if(a.isTokenIdent(e.value)){const o=namedColor(e.value[4].value);return!1!==o?o:"transparent"===toLowerCaseAZ(e.value[4].value)&&{colorNotation:exports.ColorNotation.RGB,channels:[0,0,0],alpha:0,syntaxFlags:new Set([exports.SyntaxFlag.ColorKeyword])}}}return!1},exports.colorDataFitsDisplayP3_Gamut=function colorDataFitsDisplayP3_Gamut(e){const o={...e,channels:[...e.channels]};return o.channels=convertPowerlessComponentsToZeroValuesForDisplay(o.channels,o.colorNotation),!colorDataTo(o,exports.ColorNotation.Display_P3).channels.find((e=>e<-1e-5||e>1.00001))},exports.colorDataFitsRGB_Gamut=function colorDataFitsRGB_Gamut(e){const o={...e,channels:[...e.channels]};return o.channels=convertPowerlessComponentsToZeroValuesForDisplay(o.channels,o.colorNotation),!colorDataTo(o,exports.ColorNotation.RGB).channels.find((e=>e<-1e-5||e>1.00001))},exports.serializeHSL=function serializeHSL(e,o=!0){e.channels=convertPowerlessComponentsToZeroValuesForDisplay(e.channels,e.colorNotation);let r=e.channels.map((e=>Number.isNaN(e)?0:e));r=o?n.XYZ_D50_to_HSL(n.sRGB_to_XYZ_D50(XYZ_D50_to_sRGB_Gamut(colorData_to_XYZ_D50(e).channels))):n.XYZ_D50_to_HSL(colorData_to_XYZ_D50(e).channels),r=r.map((e=>Number.isNaN(e)?0:e));const s=Math.min(360,Math.max(0,Math.round(toPrecision(r[0])))),l=Math.min(100,Math.max(0,Math.round(toPrecision(r[1])))),i=Math.min(100,Math.max(0,Math.round(toPrecision(r[2])))),u=[a.TokenType.CloseParen,")",-1,-1,void 0],c=[a.TokenType.Whitespace," ",-1,-1,void 0],p=[a.TokenType.Comma,",",-1,-1,void 0],N=[new t.TokenNode([a.TokenType.Number,s.toString(),-1,-1,{value:r[0],type:a.NumberType.Integer}]),new t.TokenNode(p),new t.WhitespaceNode([c]),new t.TokenNode([a.TokenType.Percentage,l.toString()+"%",-1,-1,{value:r[1]}]),new t.TokenNode(p),new t.WhitespaceNode([c]),new t.TokenNode([a.TokenType.Percentage,i.toString()+"%",-1,-1,{value:r[2]}])];if("number"==typeof e.alpha){const o=Math.min(1,Math.max(0,toPrecision(Number.isNaN(e.alpha)?0:e.alpha)));return 1===toPrecision(o,4)?new t.FunctionNode([a.TokenType.Function,"hsl(",-1,-1,{value:"hsl"}],u,N):new t.FunctionNode([a.TokenType.Function,"hsla(",-1,-1,{value:"hsla"}],u,[...N,new t.TokenNode(p),new t.WhitespaceNode([c]),new t.TokenNode([a.TokenType.Number,toPrecision(o,4).toString(),-1,-1,{value:e.alpha,type:a.NumberType.Number}])])}return new t.FunctionNode([a.TokenType.Function,"hsla(",-1,-1,{value:"hsla"}],u,[...N,new t.TokenNode(p),new t.WhitespaceNode([c]),e.alpha])},exports.serializeOKLCH=function serializeOKLCH(e){e.channels=convertPowerlessComponentsToZeroValuesForDisplay(e.channels,e.colorNotation);let o=e.channels.map((e=>Number.isNaN(e)?0:e));e.colorNotation!==exports.ColorNotation.OKLCH&&(o=n.XYZ_D50_to_OKLCH(colorData_to_XYZ_D50(e).channels));const r=toPrecision(o[0],6),s=toPrecision(o[1],6),l=toPrecision(o[2],6),i=[a.TokenType.Function,"oklch(",-1,-1,{value:"oklch"}],u=[a.TokenType.Whitespace," ",-1,-1,void 0];return serializeWithAlpha(e,i,u,[new t.TokenNode([a.TokenType.Number,r.toString(),-1,-1,{value:o[0],type:a.NumberType.Number}]),new t.WhitespaceNode([u]),new t.TokenNode([a.TokenType.Number,s.toString(),-1,-1,{value:o[1],type:a.NumberType.Number}]),new t.WhitespaceNode([u]),new t.TokenNode([a.TokenType.Number,l.toString(),-1,-1,{value:o[2],type:a.NumberType.Number}])])},exports.serializeP3=function serializeP3(e,o=!0){e.channels=convertPowerlessComponentsToZeroValuesForDisplay(e.channels,e.colorNotation);let r=e.channels.map((e=>Number.isNaN(e)?0:e));o?r=XYZ_D50_to_P3_Gamut(colorData_to_XYZ_D50(e).channels):e.colorNotation!==exports.ColorNotation.Display_P3&&(r=n.XYZ_D50_to_P3(colorData_to_XYZ_D50(e).channels));const s=o?Math.min(1,Math.max(0,toPrecision(r[0],6))):toPrecision(r[0],6),l=o?Math.min(1,Math.max(0,toPrecision(r[1],6))):toPrecision(r[1],6),i=o?Math.min(1,Math.max(0,toPrecision(r[2],6))):toPrecision(r[2],6),u=[a.TokenType.Function,"color(",-1,-1,{value:"color"}],c=[a.TokenType.Whitespace," ",-1,-1,void 0];return serializeWithAlpha(e,u,c,[new t.TokenNode([a.TokenType.Ident,"display-p3",-1,-1,{value:"display-p3"}]),new t.WhitespaceNode([c]),new t.TokenNode([a.TokenType.Number,s.toString(),-1,-1,{value:r[0],type:a.NumberType.Number}]),new t.WhitespaceNode([c]),new t.TokenNode([a.TokenType.Number,l.toString(),-1,-1,{value:r[1],type:a.NumberType.Number}]),new t.WhitespaceNode([c]),new t.TokenNode([a.TokenType.Number,i.toString(),-1,-1,{value:r[2],type:a.NumberType.Number}])])},exports.serializeRGB=function serializeRGB(e,o=!0){e.channels=convertPowerlessComponentsToZeroValuesForDisplay(e.channels,e.colorNotation);let r=e.channels.map((e=>Number.isNaN(e)?0:e));r=o?XYZ_D50_to_sRGB_Gamut(colorData_to_XYZ_D50(e).channels):n.XYZ_D50_to_sRGB(colorData_to_XYZ_D50(e).channels);const s=Math.min(255,Math.max(0,Math.round(255*toPrecision(r[0])))),l=Math.min(255,Math.max(0,Math.round(255*toPrecision(r[1])))),i=Math.min(255,Math.max(0,Math.round(255*toPrecision(r[2])))),u=[a.TokenType.CloseParen,")",-1,-1,void 0],c=[a.TokenType.Whitespace," ",-1,-1,void 0],p=[a.TokenType.Comma,",",-1,-1,void 0],N=[new t.TokenNode([a.TokenType.Number,s.toString(),-1,-1,{value:Math.min(255,255*Math.max(0,r[0])),type:a.NumberType.Integer}]),new t.TokenNode(p),new t.WhitespaceNode([c]),new t.TokenNode([a.TokenType.Number,l.toString(),-1,-1,{value:Math.min(255,255*Math.max(0,r[1])),type:a.NumberType.Integer}]),new t.TokenNode(p),new t.WhitespaceNode([c]),new t.TokenNode([a.TokenType.Number,i.toString(),-1,-1,{value:Math.min(255,255*Math.max(0,r[2])),type:a.NumberType.Integer}])];if("number"==typeof e.alpha){const o=Math.min(1,Math.max(0,toPrecision(Number.isNaN(e.alpha)?0:e.alpha)));return 1===toPrecision(o,4)?new t.FunctionNode([a.TokenType.Function,"rgb(",-1,-1,{value:"rgb"}],u,N):new t.FunctionNode([a.TokenType.Function,"rgba(",-1,-1,{value:"rgba"}],u,[...N,new t.TokenNode(p),new t.WhitespaceNode([c]),new t.TokenNode([a.TokenType.Number,toPrecision(o,4).toString(),-1,-1,{value:e.alpha,type:a.NumberType.Number}])])}return new t.FunctionNode([a.TokenType.Function,"rgba(",-1,-1,{value:"rgba"}],u,[...N,new t.TokenNode(p),new t.WhitespaceNode([c]),e.alpha])};
