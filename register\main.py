#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
注册工具主启动文件
支持GUI和命令行两种启动方式
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def main():
    """主函数"""
    if len(sys.argv) > 1:
        # 命令行参数启动
        arg = sys.argv[1].lower()
        
        if arg == "gui":
            # 启动GUI
            from gui.run_gui import main as gui_main
            gui_main()
        elif arg == "selenium":
            # 启动Selenium模式
            from browser_automation.selenium_register import main as selenium_main
            selenium_main()
        elif arg == "batch":
            # 启动批量注册
            from core.batch_register import main as batch_main
            batch_main()
        elif arg == "config":
            # 配置管理
            from start import config_management
            config_management()
        else:
            print("[失败] 无效参数")
            show_usage()
    else:
        # 默认启动菜单模式
        from start import main as start_main
        start_main()

def show_usage():
    """显示使用说明"""
    print("📖 使用说明:")
    print("python main.py          # 启动菜单模式")
    print("python main.py gui      # 启动GUI界面")
    print("python main.py selenium # 启动Selenium自动化")
    print("python main.py batch    # 启动批量注册")
    print("python main.py config   # 配置管理")

if __name__ == "__main__":
    main()
