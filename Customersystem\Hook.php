<?php

namespace plugin\Customersystem;

use think\facade\Db;
use think\facade\Log;
use think\facade\Cache;

class Hook
{
    /**
     * 主要Hook处理方法
     * 用于将JS文件加入页面并实现通话实时显示
     *
     * @param array $js JS文件数组
     * @return void
     */
    public function handle(&$array)
    {
        $user = $array[0];
        $array[1][] = plugstatic("Customersystem", 'qiantai.js');
    }

    /**
     * 系统定时任务钩子 - 每5秒执行一次
     * 用于实时更新聊天消息缓存和推送通知
     *
     * @return void
     */
    public function SimpleCommand()
    {
        try {
            // 更新活跃会话缓存
            $this->cacheActiveSessions();

            // 检查是否有新消息需要推送
            $this->checkNewMessages();

            // 清理过期的缓存数据
            $this->cleanExpiredCache();

        } catch (\Exception $e) {
            Log::error("Customersystem定时任务执行失败: " . $e->getMessage());
        }
    }
    
    /**
     * 缓存活跃会话数据
     * 减少频繁查询数据库的需求
     * 
     * @return void
     */
    protected function cacheActiveSessions()
    {
        try {
            // 获取所有活跃会话
            $activeSessions = Db::name('plugin_chat_sessions')
                ->alias('s')
                ->join('plugin_chat_contacts c', 'c.id = s.contact_id')
                ->field('s.id, s.title, s.status, s.last_message, s.last_time, s.unread_count, s.staff_id, c.name as contact_name, c.avatar')
                ->where('s.status', 1)
                ->order('s.last_time', 'desc')
                ->select()
                ->toArray();
            
            if (!empty($activeSessions)) {
                // 缓存活跃会话数据，设置较短的过期时间以保证数据相对实时
                // 使用会话数量作为缓存键的一部分，便于判断是否有新会话
                $sessionCount = count($activeSessions);
                Cache::set('customersystem_active_sessions', $activeSessions, 2); // 缓存2秒
                Cache::set('customersystem_session_count', $sessionCount, 2);
                
                // 为每个活跃会话缓存最近的消息
                foreach ($activeSessions as $session) {
                    $this->cacheSessionMessages($session['id']);
                }
                

            }
        } catch (\Exception $e) {
            Log::error("Customersystem实时缓存失败：" . $e->getMessage());
        }
    }
    
    /**
     * 缓存会话消息，确保已读状态正确处理
     * 
     * @param int $sessionId 会话ID
     * @return void
     */
    protected function cacheSessionMessages($sessionId)
    {
        try {
            // 获取会话最近的30条消息，增加数量以确保更多历史记录可用
            $messages = Db::name('plugin_chat_messages')
                ->where('session_id', $sessionId)
                ->order('create_time', 'desc')
                ->limit(30)
                ->select()
                ->toArray();
            
            if (!empty($messages)) {
                // 反转消息顺序，使其按时间顺序排列
                $messages = array_reverse($messages);
                

                
                // 将消息ID转为整数以确保正确比较
                foreach ($messages as &$message) {
                    if (isset($message['id'])) {
                        $message['id'] = intval($message['id']);
                    }
                    // 确保时间戳是整数
                    if (isset($message['create_time'])) {
                        $message['create_time'] = intval($message['create_time']);
                    }
                    if (isset($message['update_time'])) {
                        $message['update_time'] = intval($message['update_time']);
                    }
                    
                    // 确保已读状态和时间字段存在
                    if (!isset($message['is_read'])) {
                        $message['is_read'] = 0;
                    } else {
                        $message['is_read'] = intval($message['is_read']);
                    }
                    
                    if (!isset($message['read_time'])) {
                        $message['read_time'] = 0;
                    } else {
                        $message['read_time'] = intval($message['read_time']);
                    }
                    

                    
                    // 确保消息类型存在，默认为文本类型
                    if (empty($message['message_type'])) {
                        $message['message_type'] = 'text';
                    }
                    

                }
                

                
                // 缓存会话消息，缩短缓存时间以保证实时性
                Cache::set('customersystem_session_' . $sessionId . '_messages', $messages, 2); // 缓存2秒
                
                // 添加最后更新时间缓存，用于判断缓存是否需要更新
                Cache::set('customersystem_session_' . $sessionId . '_last_update', time(), 2);
            }
        } catch (\Exception $e) {
            Log::error("Customersystem缓存会话消息失败 [会话ID: {$sessionId}]：" . $e->getMessage());
        }
    }
    
    /**
     * 获取活跃会话数据
     * 供前端调用的接口方法
     * 
     * @return array 活跃会话数据
     */
    public function getActiveSessions()
    {
        // 尝试从缓存获取数据
        $sessions = Cache::get('customersystem_active_sessions');
        
        // 缓存未命中时，重新加载并缓存
        if (empty($sessions)) {
            $this->cacheActiveSessions();
            $sessions = Cache::get('customersystem_active_sessions') ?: [];
        }
        
        return $sessions;
    }
    
    /**
     * 获取指定会话的消息
     * 供前端调用的接口方法
     * 
     * @param int $sessionId 会话ID
     * @return array 会话消息
     */
    public function getSessionMessages($sessionId)
    {
        // 始终先检查数据库是否有新消息
        $lastUpdate = Cache::get('customersystem_session_' . $sessionId . '_last_update', 0);
        $currentTime = time();
        
        // 如果上次更新时间超过3秒，则强制更新缓存
        if ($currentTime - $lastUpdate > 3) {
            $this->cacheSessionMessages($sessionId);
        }
        
        // 从缓存获取数据
        $messages = Cache::get('customersystem_session_' . $sessionId . '_messages');
        
        // 缓存未命中时，重新加载该会话消息并缓存
        if (empty($messages)) {
            $this->cacheSessionMessages($sessionId);
            $messages = Cache::get('customersystem_session_' . $sessionId . '_messages') ?: [];
        }
        
        // 如果消息确实是最新的，从数据库获取最新的几条消息并与缓存合并
        $latestMessages = [];
        if (!empty($messages)) {
            $lastMessageId = max(array_column($messages, 'id'));
            
            // 获取比缓存中最新消息ID更大的消息
            $latestMessages = Db::name('plugin_chat_messages')
                ->where('session_id', $sessionId)
                ->where('id', '>', $lastMessageId)
                ->order('create_time', 'asc')
                ->select()
                ->toArray();
                
            // 确保消息ID和时间戳是整数
            foreach ($latestMessages as &$message) {
                if (isset($message['id'])) {
                    $message['id'] = intval($message['id']);
                }
                if (isset($message['create_time'])) {
                    $message['create_time'] = intval($message['create_time']);
                }
                if (isset($message['update_time'])) {
                    $message['update_time'] = intval($message['update_time']);
                }
            }
            
            // 如果有新消息，更新缓存
            if (!empty($latestMessages)) {
                $messages = array_merge($messages, $latestMessages);
                Cache::set('customersystem_session_' . $sessionId . '_messages', $messages, 5);
                Cache::set('customersystem_session_' . $sessionId . '_last_update', time(), 5);
            }
        }
        
        return $messages;
    }
    
    /**
     * 消息发送后回调
     * 在消息发送后立即更新缓存以确保实时性
     * 
     * @param int $sessionId 会话ID
     * @param array $message 新消息数据
     * @return void
     */
    public function afterMessageSent($sessionId, $message = null)
    {
        try {
            // 立即清除缓存，强制下次读取从数据库获取
            $this->clearSessionCache($sessionId);
            
            // 如果提供了消息数据，将其直接添加到缓存
            if ($message && is_array($message)) {
                $cachedMessages = Cache::get('customersystem_session_' . $sessionId . '_messages', []);
                
                // 确保消息ID是整数
                if (isset($message['id'])) {
                    $message['id'] = intval($message['id']);
                }
                
                // 确保时间戳是整数
                if (isset($message['create_time'])) {
                    $message['create_time'] = intval($message['create_time']);
                }
                if (isset($message['update_time'])) {
                    $message['update_time'] = intval($message['update_time']);
                }
                
                // 确保消息类型存在，默认为文本类型
                if (empty($message['message_type'])) {
                    $message['message_type'] = 'text';
                }
                
                // 检查消息是否已存在
                $exists = false;
                foreach ($cachedMessages as $cachedMsg) {
                    if (isset($cachedMsg['id']) && isset($message['id']) && $cachedMsg['id'] === $message['id']) {
                        $exists = true;
                        break;
                    }
                }
                
                // 如果消息不存在，添加到缓存
                if (!$exists) {
                    $cachedMessages[] = $message;
                    
                    // 按时间排序
                    usort($cachedMessages, function($a, $b) {
                        return $a['create_time'] - $b['create_time'];
                    });
                    
                    // 更新缓存
                    Cache::set('customersystem_session_' . $sessionId . '_messages', $cachedMessages, 5);
                    Cache::set('customersystem_session_' . $sessionId . '_last_update', time(), 5);
                }
            }
            
            // 立即更新该会话的缓存（从数据库重新加载）
            $this->cacheSessionMessages($sessionId);
            
            // 同时更新会话信息
            $this->cacheActiveSessions();
            

        } catch (\Exception $e) {
            Log::error("Customersystem消息发送后缓存更新失败: " . $e->getMessage());
        }
        
        return true;
    }
    
    /**
     * 消息标记为已读后回调
     * 在消息状态变更后立即更新缓存以确保实时性
     * 
     * @param int $sessionId 会话ID
     * @param int $messageId 消息ID
     * @return void
     */
    public function afterMessageRead($sessionId, $messageId = null)
    {
        try {
            // 立即清除所有相关缓存，强制下次读取从数据库获取
            $this->clearAllSessionRelatedCache($sessionId);

            // 立即更新该会话的缓存（从数据库重新加载）
            $this->cacheSessionMessages($sessionId);

            Log::info("已读状态更新后缓存已清理，会话ID: {$sessionId}, 消息ID: {$messageId}");

        } catch (\Exception $e) {
            Log::error("Customersystem已读状态更新后缓存更新失败: " . $e->getMessage());
        }

        return true;
    }

    /**
     * 清除会话相关的所有缓存
     *
     * @param int $sessionId 会话ID
     * @return void
     */
    private function clearAllSessionRelatedCache($sessionId)
    {
        try {
            // 清除会话缓存
            Cache::delete('customersystem_session_' . $sessionId);
            Cache::delete('customersystem_session_' . $sessionId . '_messages');
            Cache::delete('customersystem_session_' . $sessionId . '_detail');

            // 清除未读计数缓存
            Cache::delete('customersystem_unread_' . $sessionId);
            Cache::delete('customersystem_unread_count_' . $sessionId);

            // 清除活跃会话缓存
            Cache::delete('customersystem_active_sessions');
            Cache::delete('customersystem_sessions_list');

            // 清除通知缓存
            Cache::delete('customersystem_new_message_' . $sessionId);

        } catch (\Exception $e) {
            Log::error("清除会话相关缓存失败: " . $e->getMessage());
        }
    }
    
    /**
     * 批量标记消息为已读后回调
     * 
     * @param int $sessionId 会话ID
     * @param string $roleType 角色类型
     * @return void
     */
    public function afterBatchMessageRead($sessionId, $roleType)
    {
        try {
            // 清除所有相关缓存，强制下次读取从数据库获取最新的已读状态
            $this->clearAllSessionRelatedCache($sessionId);

            // 立即更新该会话的缓存
            $this->cacheSessionMessages($sessionId);

            Log::info("批量已读状态更新后缓存已清理，会话ID: {$sessionId}, 角色: {$roleType}");

        } catch (\Exception $e) {
            Log::error("Customersystem批量已读状态更新失败: " . $e->getMessage());
        }

        return true;
    }
    
    /**
     * 清除指定会话的缓存
     * 用于强制前端重新获取最新数据
     * 
     * @param int $sessionId 会话ID
     * @return void
     */
    public function clearSessionCache($sessionId)
    {
        try {
            Cache::delete('customersystem_session_' . $sessionId . '_messages');
            Cache::delete('customersystem_session_' . $sessionId . '_last_update');
            
            // 同时也清除活跃会话缓存
            Cache::delete('customersystem_active_sessions');
            Cache::delete('customersystem_session_count');
            

        } catch (\Exception $e) {
            Log::error("Customersystem清除会话缓存失败: " . $e->getMessage());
        }
    }

    /**
     * 检查新消息并更新实时推送数据
     *
     * @return void
     */
    protected function checkNewMessages()
    {
        try {
            // 获取最近5分钟内的新消息
            $recentTime = time() - 300; // 5分钟前

            $newMessages = Db::name('plugin_chat_messages')
                ->alias('m')
                ->join('plugin_chat_sessions s', 's.id = m.session_id')
                ->join('plugin_chat_contacts c', 'c.id = s.contact_id')
                ->field('m.*, s.title as session_title, c.name as contact_name')
                ->where('m.create_time', '>', $recentTime)
                ->where('s.status', 1) // 只处理活跃会话
                ->order('m.create_time', 'desc')
                ->select()
                ->toArray();

            if (!empty($newMessages)) {
                // 按会话分组处理新消息
                $messagesBySession = [];
                foreach ($newMessages as $message) {
                    $sessionId = $message['session_id'];
                    if (!isset($messagesBySession[$sessionId])) {
                        $messagesBySession[$sessionId] = [];
                    }
                    $messagesBySession[$sessionId][] = $message;
                }

                // 为每个有新消息的会话更新缓存
                foreach ($messagesBySession as $sessionId => $messages) {
                    $this->cacheSessionMessages($sessionId);

                    // 设置新消息通知标记
                    $this->setNewMessageNotification($sessionId, count($messages));
                }

                // 更新全局新消息统计
                $this->updateGlobalMessageStats($newMessages);
            }

        } catch (\Exception $e) {
            Log::error("Customersystem检查新消息失败: " . $e->getMessage());
        }
    }

    /**
     * 设置新消息通知标记
     *
     * @param int $sessionId 会话ID
     * @param int $messageCount 新消息数量
     * @return void
     */
    protected function setNewMessageNotification($sessionId, $messageCount)
    {
        try {
            $notificationKey = 'customersystem_new_message_' . $sessionId;
            $currentNotification = Cache::get($notificationKey, 0);

            // 累加新消息数量
            $totalNewMessages = $currentNotification + $messageCount;

            // 缓存新消息通知，设置较长的过期时间
            Cache::set($notificationKey, $totalNewMessages, 300); // 5分钟

            // 设置全局新消息标记
            $globalKey = 'customersystem_has_new_messages';
            Cache::set($globalKey, time(), 300);

        } catch (\Exception $e) {
            Log::error("Customersystem设置新消息通知失败: " . $e->getMessage());
        }
    }

    /**
     * 更新全局消息统计
     *
     * @param array $newMessages 新消息数组
     * @return void
     */
    protected function updateGlobalMessageStats($newMessages)
    {
        try {
            $stats = [
                'total_new_messages' => count($newMessages),
                'last_update_time' => time(),
                'sessions_with_new_messages' => []
            ];

            // 统计每个会话的新消息数量
            foreach ($newMessages as $message) {
                $sessionId = $message['session_id'];
                if (!isset($stats['sessions_with_new_messages'][$sessionId])) {
                    $stats['sessions_with_new_messages'][$sessionId] = [
                        'count' => 0,
                        'latest_message' => $message['message'],
                        'latest_time' => $message['create_time'],
                        'contact_name' => $message['contact_name']
                    ];
                }
                $stats['sessions_with_new_messages'][$sessionId]['count']++;
            }

            // 缓存全局统计信息
            Cache::set('customersystem_message_stats', $stats, 300);

        } catch (\Exception $e) {
            Log::error("Customersystem更新全局消息统计失败: " . $e->getMessage());
        }
    }

    /**
     * 清理过期的缓存数据
     *
     * @return void
     */
    protected function cleanExpiredCache()
    {
        try {
            // 获取所有会话ID
            $sessionIds = Db::name('plugin_chat_sessions')
                ->where('status', 1)
                ->column('id');

            if (!empty($sessionIds)) {
                foreach ($sessionIds as $sessionId) {
                    // 检查会话缓存的最后更新时间
                    $lastUpdate = Cache::get('customersystem_session_' . $sessionId . '_last_update', 0);
                    $currentTime = time();

                    // 如果缓存超过10分钟未更新，清理它
                    if ($currentTime - $lastUpdate > 600) {
                        Cache::delete('customersystem_session_' . $sessionId . '_messages');
                        Cache::delete('customersystem_session_' . $sessionId . '_last_update');
                    }
                }
            }

            // 清理过期的通知缓存
            $this->cleanExpiredNotifications();

        } catch (\Exception $e) {
            Log::error("Customersystem清理过期缓存失败: " . $e->getMessage());
        }
    }

    /**
     * 清理过期的通知缓存
     *
     * @return void
     */
    protected function cleanExpiredNotifications()
    {
        try {
            // 这里可以添加清理过期通知的逻辑
            // 由于我们使用了TTL，大部分过期数据会自动清理
            // 这里主要是为了确保系统稳定性

            $currentTime = time();
            $expiredTime = $currentTime - 3600; // 1小时前

            // 可以在这里添加更多的清理逻辑

        } catch (\Exception $e) {
            Log::error("Customersystem清理过期通知失败: " . $e->getMessage());
        }
    }

    /**
     * 获取新消息通知数据
     * 供前端调用的接口方法
     *
     * @return array 新消息通知数据
     */
    public function getNewMessageNotifications()
    {
        try {
            $notifications = [];

            // 获取全局消息统计
            $globalStats = Cache::get('customersystem_message_stats', []);

            if (!empty($globalStats)) {
                $notifications['global'] = $globalStats;
            }

            // 获取各会话的新消息通知
            $sessionIds = Db::name('plugin_chat_sessions')
                ->where('status', 1)
                ->column('id');

            if (!empty($sessionIds)) {
                $sessionNotifications = [];
                foreach ($sessionIds as $sessionId) {
                    $notificationKey = 'customersystem_new_message_' . $sessionId;
                    $count = Cache::get($notificationKey, 0);
                    if ($count > 0) {
                        $sessionNotifications[$sessionId] = $count;
                    }
                }
                $notifications['sessions'] = $sessionNotifications;
            }

            return $notifications;

        } catch (\Exception $e) {
            Log::error("Customersystem获取新消息通知失败: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 清除指定会话的新消息通知
     *
     * @param int $sessionId 会话ID
     * @return void
     */
    public function clearNewMessageNotification($sessionId)
    {
        try {
            $notificationKey = 'customersystem_new_message_' . $sessionId;
            Cache::delete($notificationKey);

        } catch (\Exception $e) {
            Log::error("Customersystem清除新消息通知失败: " . $e->getMessage());
        }
    }

    /**
     * 清空消息后的Hook处理
     * 清理相关缓存数据
     *
     * @param int $sessionId 会话ID
     * @return void
     */
    public function afterClearMessages($sessionId)
    {
        try {
            // 清除会话相关的所有缓存
            $sessionCacheKey = 'customersystem_session_' . $sessionId;
            $messagesCacheKey = 'customersystem_messages_' . $sessionId;
            $unreadCacheKey = 'customersystem_unread_' . $sessionId;
            $notificationKey = 'customersystem_new_message_' . $sessionId;

            Cache::delete($sessionCacheKey);
            Cache::delete($messagesCacheKey);
            Cache::delete($unreadCacheKey);
            Cache::delete($notificationKey);

            // 清除活跃会话缓存，强制重新加载
            Cache::delete('customersystem_active_sessions');

            Log::info("会话 {$sessionId} 的聊天记录已清空，相关缓存已清理");

        } catch (\Exception $e) {
            Log::error("清空消息后Hook处理失败: " . $e->getMessage());
        }
    }
}