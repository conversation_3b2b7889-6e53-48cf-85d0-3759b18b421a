<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <meta http-equiv="Cache-Control" content="max-age=31536000">
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>文字风控设置</title>
    <style>
        #loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #fff;
            z-index: 9999;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(0, 0, 0, 0.1);
            border-top-color: #3498db;
            border-radius: 50%;
            animation: spin 1s ease infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .page-container {
            display: flex;
            min-height: 100vh;
        }
        .main-content {
            flex: 1;
            padding: 20px;
        }
    </style>
</head>

<body>
<div id="loading">
    <div class="spinner"></div>
</div>

<div id="app" style="display: none">
    <div class="page-container">
        <div class="main-content">
            <el-card shadow="never">
                <el-form :model="riskForm" label-width="120px">
                    <!-- 基础设置 -->
                    <el-form-item label="总开关：">
                        <el-radio-group v-model="riskForm.status">
                            <el-radio :label="0">关闭</el-radio>
                            <el-radio :label="1">开启</el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <!-- 阿里云设置 -->
                    <el-divider>阿里云文本检测</el-divider>
                    <div style="margin-bottom: 15px;">
                        <el-link 
                            type="primary" 
                            href="https://yundun.console.aliyun.com/?p=cts#/api/profile" 
                            target="_blank">
                            前往阿里云内容安全控制台
                        </el-link>
                    </div>
                    <el-form-item label="启用：">
                        <el-radio-group v-model="riskForm.aliyun_status">
                            <el-radio :label="0">关闭</el-radio>
                            <el-radio :label="1">开启</el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <el-form-item label="AccessKey：">
                        <el-input 
                            v-model="riskForm.aliyun_access_key"
                            placeholder="请输入阿里云AccessKey">
                        </el-input>
                    </el-form-item>

                    <el-form-item label="AccessSecret：">
                        <el-input 
                            v-model="riskForm.aliyun_access_secret"
                            type="password"
                            placeholder="请输入阿里云AccessSecret">
                        </el-input>
                    </el-form-item>

                    <el-form-item label="区域：">
                        <el-select v-model="riskForm.aliyun_region">
                            <el-option label="上海" value="cn-shanghai"></el-option>
                            <el-option label="北京" value="cn-beijing"></el-option>
                            <el-option label="杭州" value="cn-hangzhou"></el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" size="small" @click="testCheck('aliyun')">
                            测试阿里云检测
                        </el-button>
                    </el-form-item>

                    <!-- 腾讯云设置 -->
                    <el-divider>腾讯云文本检测</el-divider>
                    <div style="margin-bottom: 15px;">
                        <el-link 
                            type="primary" 
                            href="https://console.cloud.tencent.com/cms" 
                            target="_blank">
                            前往腾讯云内容安全控制台
                        </el-link>
                    </div>
                    <el-form-item label="启用：">
                        <el-radio-group v-model="riskForm.tencent_status">
                            <el-radio :label="0">关闭</el-radio>
                            <el-radio :label="1">开启</el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <el-form-item label="SecretId：">
                        <el-input 
                            v-model="riskForm.tencent_secret_id"
                            placeholder="请输入腾讯云SecretId">
                        </el-input>
                    </el-form-item>

                    <el-form-item label="SecretKey：">
                        <el-input 
                            v-model="riskForm.tencent_secret_key"
                            type="password"
                            placeholder="请输入腾讯云SecretKey">
                        </el-input>
                    </el-form-item>

                    <el-form-item label="区域：">
                        <el-select v-model="riskForm.tencent_region">
                            <el-option label="广州" value="ap-guangzhou"></el-option>
                            <el-option label="上海" value="ap-shanghai"></el-option>
                            <el-option label="北京" value="ap-beijing"></el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" size="small" @click="testCheck('tencent')">
                            测试腾讯云检测
                        </el-button>
                    </el-form-item>

                    <!-- 百度云设置 -->
                    <el-divider>百度云文本检测</el-divider>
                    <div style="margin-bottom: 15px;">
                        <el-link 
                            type="primary" 
                            href="https://console.bce.baidu.com/ai/#/ai/antiporn/overview/index" 
                            target="_blank">
                            前往百度云内容审核控制台
                        </el-link>
                    </div>
                    <el-form-item label="启用：">
                        <el-radio-group v-model="riskForm.baidu_status">
                            <el-radio :label="0">关闭</el-radio>
                            <el-radio :label="1">开启</el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <el-form-item label="AppID：">
                        <el-input 
                            v-model="riskForm.baidu_app_id"
                            placeholder="请输入百度云AppID">
                        </el-input>
                    </el-form-item>

                    <el-form-item label="ApiKey：">
                        <el-input 
                            v-model="riskForm.baidu_api_key"
                            placeholder="请输入百度云ApiKey">
                        </el-input>
                    </el-form-item>

                    <el-form-item label="SecretKey：">
                        <el-input 
                            v-model="riskForm.baidu_secret_key"
                            type="password"
                            placeholder="请输入百度云SecretKey">
                        </el-input>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" size="small" @click="testCheck('baidu')">
                            测试百度云检测
                        </el-button>
                    </el-form-item>

                    <!-- 处理设置 -->
                    <el-divider>处理设置</el-divider>
                    <el-form-item label="处理方式：">
                        <el-radio-group v-model="riskForm.action_type">
                            <el-radio label="mark">标记</el-radio>
                            <el-radio label="delete">删除</el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <el-form-item label="通知管理员：">
                        <el-radio-group v-model="riskForm.notify_admin">
                            <el-radio :label="0">关闭</el-radio>
                            <el-radio :label="1">开启</el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <el-form-item label="通知内容：">
                        <el-input 
                            v-model="riskForm.notify_content"
                            type="textarea"
                            :rows="3"
                            placeholder="支持变量: {id}">
                        </el-input>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="saveConfig" :loading="isLoading">
                            保存配置
                        </el-button>
                        <el-button type="danger" @click="handleManualCheck" :loading="isLoading">
                            立即检测
                        </el-button>
                    </el-form-item>
                </el-form>
            </el-card>
        </div>
    </div>
</div>

<!-- 测试对话框模板 -->
<script type="text/html" id="testDialog">
    <form class="layui-form" style="padding: 20px;">
        <div class="layui-form-item">
            <label class="layui-form-label">测试文本</label>
            <div class="layui-input-block">
                <textarea name="test_text" class="layui-textarea" placeholder="请输入要测试的文本内容"></textarea>
            </div>
        </div>
    </form>
</script>

<script src="/static/others/vue/vue.global.prod.js"></script>
<script src="/static/others/element-plus/index.full.min.js"></script>
<script src="/static/others/axios/axios.min.js"></script>

<script>
const { createApp, ref, reactive, onMounted } = Vue;
const { ElMessage, ElMessageBox } = ElementPlus;

const app = createApp({
    setup() {
        const isLoading = ref(false);
        
        const riskForm = reactive({
            status: 0,
            
            aliyun_status: 0,
            aliyun_access_key: '',
            aliyun_access_secret: '',
            aliyun_region: 'cn-shanghai',
            
            tencent_status: 0,
            tencent_secret_id: '',
            tencent_secret_key: '',
            tencent_region: 'ap-guangzhou',
            
            baidu_status: 0,
            baidu_app_id: '',
            baidu_api_key: '',
            baidu_secret_key: '',
            
            action_type: 'mark',
            notify_admin: 1,
            notify_content: ''
        });

        // 获取配置
        const fetchData = async () => {
            try {
                const res = await axios.post("/plugin/Riskcontrol/Api/fetchData");
                if (res.data?.code === 200) {
                    Object.assign(riskForm, res.data.data);
                } else {
                    ElMessage.error(res.data?.msg || '获取配置失败');
                }
            } catch (error) {
                ElMessage.error('获取配置失败');
            }
        };

        // 保存配置
        const saveConfig = async () => {
            try {
                isLoading.value = true;
                const res = await axios.post("/plugin/Riskcontrol/Api/saveConfig", riskForm);
                if (res.data?.code === 200) {
                    ElMessage.success('保存成功');
                } else {
                    ElMessage.error(res.data?.msg || '保存失败');
                }
            } catch (error) {
                ElMessage.error('保存失败');
            } finally {
                isLoading.value = false;
            }
        };

        // 测试文本检测
        const testCheck = async (platform) => {
            try {
                const { value: text } = await ElMessageBox.prompt('请输入要测试的文本', '测试文本检测', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    inputType: 'textarea',
                    inputPlaceholder: '请输入要检测的文本内容'
                });

                if (text) {
                    isLoading.value = true;
                    const res = await axios.post("/plugin/Riskcontrol/Api/testCheck", {
                        platform,
                        text
                    });

                    if (res.data?.code === 200) {
                        const result = res.data.data;
                        if (result.violation) {
                            ElMessage.warning(`检测结果：违规（${result.message}）`);
                        } else {
                            ElMessage.success('检测结果：正常');
                        }
                    } else {
                        ElMessage.error(res.data?.msg || '检测失败');
                    }
                }
            } catch (error) {
                if (error !== 'cancel') {
                    ElMessage.error('检测失败');
                }
            } finally {
                isLoading.value = false;
            }
        };

        // 手动检测
        const handleManualCheck = async () => {
            try {
                await ElMessageBox.confirm(
                    '确定要立即执行文本检测吗？',
                    '警告',
                    {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }
                );
                
                isLoading.value = true;
                const res = await axios.post("/plugin/Riskcontrol/Api/manualCheck");
                
                if (res.data?.code === 200) {
                    await ElMessageBox.alert(
                        res.data.msg.replace(/\n/g, '<br>'),
                        '执行结果',
                        {
                            dangerouslyUseHTMLString: true,
                            confirmButtonText: '确定'
                        }
                    );
                } else {
                    ElMessage.error(res.data?.msg || '检测失败');
                }
            } catch (error) {
                if (error !== 'cancel') {
                    ElMessage.error('操作失败');
                }
            } finally {
                isLoading.value = false;
            }
        };

        onMounted(() => {
            fetchData();
            // 隐藏加载动画
            document.getElementById('loading').style.display = 'none';
            document.getElementById('app').style.display = 'block';
        });

        return {
            isLoading,
            riskForm,
            saveConfig,
            testCheck,
            handleManualCheck
        };
    }
});

app.use(ElementPlus);
app.mount('#app');
</script>
</body>
</html> 