<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡密验证系统 - 编辑卡密</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background: linear-gradient(180deg, #4a00e0 0%, #8e2de2 100%);
            min-height: 100vh;
            padding-top: 20px;
            box-shadow: 4px 0 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            position: fixed;
            width: inherit;
            max-width: inherit;
        }
        .sidebar-header {
            padding: 0 15px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }
        .sidebar a {
            color: rgba(255, 255, 255, 0.8);
            display: block;
            padding: 12px 20px;
            text-decoration: none;
            transition: all 0.3s;
            margin-bottom: 5px;
            border-left: 3px solid transparent;
        }
        .sidebar a:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: #fff;
            border-left: 3px solid #fff;
        }
        .sidebar a.active {
            background-color: rgba(255, 255, 255, 0.2);
            color: #fff;
            border-left: 3px solid #fff;
        }
        .sidebar i {
            margin-right: 10px;
        }
        .main-content {
            padding: 25px;
            margin-left: 225px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 25px;
            border: none;
        }
        .card-title {
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-label {
            font-weight: 500;
            margin-bottom: 8px;
            color: #495057;
        }
        .form-control {
            border-radius: 5px;
            border: 1px solid #ced4da;
            padding: 10px 12px;
            transition: all 0.3s;
        }
        .form-control:focus {
            border-color: #8e2de2;
            box-shadow: 0 0 0 0.2rem rgba(142, 45, 226, 0.25);
        }
        .btn-primary {
            background: linear-gradient(45deg, #8e2de2, #4a00e0);
            border: none;
            padding: 10px 20px;
            box-shadow: 0 4px 6px rgba(142, 45, 226, 0.2);
            transition: all 0.3s;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(142, 45, 226, 0.3);
        }
        .btn-secondary {
            background-color: #f8f9fa;
            color: #495057;
            border: 1px solid #ced4da;
            padding: 10px 20px;
            transition: all 0.3s;
        }
        .btn-secondary:hover {
            background-color: #e9ecef;
        }
        .license-key {
            font-family: SFMono-Regular, Menlo, Monaco, Consolas, monospace;
            background-color: #f5f5f5;
            padding: 8px 12px;
            border-radius: 5px;
            font-size: 14px;
            margin-top: 5px;
            display: inline-block;
        }
        .badge-status {
            padding: 5px 10px;
            border-radius: 20px;
            font-weight: 500;
            font-size: 12px;
            margin-left: 10px;
            display: inline-block;
        }
        .status-active {
            background-color: #e3f2fd;
            color: #0d6efd;
        }
        .status-inactive {
            background-color: #f8f9fa;
            color: #6c757d;
        }
        .status-expired {
            background-color: #feecf0;
            color: #dc3545;
        }
        .status-banned {
            background-color: #fff8e1;
            color: #fd7e14;
        }
    </style>
</head>
<body>

<div class="container-fluid">
    <div class="row">
        <!-- 侧边栏 -->
        <div class="col-lg-2 col-md-3 px-0">
            <div class="sidebar">
                <div class="sidebar-header">
                    <h4 class="text-white mb-0">卡密验证系统</h4>
                    <p class="text-white-50 mb-0">管理面板</p>
                </div>
                <a href="/dashboard"><i class="fas fa-tachometer-alt"></i> 仪表盘</a>
                <a href="/licenses" class="active"><i class="fas fa-key"></i> 卡密管理</a>
                <a href="/access_logs"><i class="fas fa-history"></i> 访问日志</a>
                <a href="/logout"><i class="fas fa-sign-out-alt"></i> 退出登录</a>
            </div>
        </div>
        
        <!-- 主内容区 -->
        <div class="col-lg-10 col-md-9 main-content">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">编辑卡密</h2>
                <a href="{{ url_for('view_license', license_id=license.id) }}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left"></i> 返回详情
                </a>
            </div>
            
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">
                        卡密: <span class="license-key">{{ license.key }}</span>
                        {% if license.is_banned %}
                        <span class="badge badge-status status-banned">已封禁</span>
                        {% elif license.valid_until < current_date %}
                        <span class="badge badge-status status-expired">已过期</span>
                        {% elif license.activated %}
                        <span class="badge badge-status status-active">已激活</span>
                        {% else %}
                        <span class="badge badge-status status-inactive">未激活</span>
                        {% endif %}
                    </h5>
                    
                    <form method="post" action="{{ url_for('edit_license', license_id=license.id) }}">
                        <div class="form-group">
                            <label for="software_id" class="form-label">软件ID</label>
                            <input type="text" class="form-control" id="software_id" name="software_id" value="{{ license.software_id }}" required>
                            {% if software_ids %}
                            <div class="form-text">
                                可选值: 
                                {% for id in software_ids %}
                                <span class="badge bg-light text-dark me-1">{{ id }}</span>
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <label for="user_id" class="form-label">用户ID</label>
                            <input type="text" class="form-control" id="user_id" name="user_id" value="{{ license.user_id }}">
                            <div class="form-text">可选，留空表示不绑定用户</div>
                        </div>
                        
                        <div class="form-group">
                            <label for="valid_until" class="form-label">有效期至</label>
                            <input type="date" class="form-control" id="valid_until" name="valid_until" value="{{ license.valid_until }}">
                            <div class="form-text">留空表示永久有效</div>
                        </div>
                        
                        <div class="form-group">
                            <div class="mb-3">
                                <label class="form-label">设备ID</label>
                                <div class="form-text">{{ license.device_id or '未绑定' }}</div>
                                <div class="form-text">设备ID在激活卡密时自动绑定</div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <div class="mb-3">
                                <label class="form-label">激活状态</label>
                                <div class="form-text">{{ '已激活' if license.activated else '未激活' }}</div>
                                <div class="form-text">激活时间: {{ license.activated_at or '未激活' }}</div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <div class="mb-3">
                                <label class="form-label">创建日期</label>
                                <div class="form-text">{{ license.created_at }}</div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ url_for('view_license', license_id=license.id) }}" class="btn btn-secondary me-2">取消</a>
                            <button type="submit" class="btn btn-primary">保存更改</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html> 