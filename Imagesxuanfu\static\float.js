(function() {
    // 添加全局CSS样式规则
    const styleElement = document.createElement('style');
    styleElement.textContent = `
        .float-image-container {
            line-height: 0 !important;
            font-size: 0 !important;
        }
        .float-image-container img {
            display: block !important;
            vertical-align: bottom !important;
            margin: 0 !important;
            padding: 0 !important;
        }
        .float-image-popup img {
            display: block !important;
            vertical-align: bottom !important;
            margin: 0 auto !important;
            padding: 0 !important;
        }
    `;
    document.head.appendChild(styleElement);

    function initFloatImage() {
        const baseUrl = window.location.protocol + '//' + window.location.host;
        
        // 获取当前页面的商家信息
        let shopName = '';
        let merchantId = '';
        
        // 1. 从 nickname 元素获取商家名称（新增，优先级最高）
        const nicknameElement = document.querySelector('.nickname');
        if (nicknameElement) {
            shopName = nicknameElement.textContent.trim();
        }

        // 2. 从页面标题获取商家名称
        if (!shopName) {
            const pageTitle = document.title.trim();
            if (pageTitle) {
                const index = pageTitle.indexOf('的小店');
                if (index !== -1) {
                    shopName = pageTitle.substring(0, index);
                }
            }
        }

        // 3. 从页面元素中获取商家信息（备用方式）
        if (!shopName) {
            const shopInfoScript = document.querySelector('script[data-shop-info]');
            if (shopInfoScript) {
                try {
                    const shopInfo = JSON.parse(shopInfoScript.textContent);
                    shopName = shopInfo.shopName;
                    merchantId = shopInfo.merchantId;
                } catch (e) {
                    console.error('解析商家信息失败:', e);
                }
            }
        }
        
        // 4. 从 meta 标签获取（备用方式）
        if (!shopName) {
            const metaShopName = document.querySelector('meta[name="shop-name"]');
            if (metaShopName) {
                shopName = metaShopName.getAttribute('content');
            }
        }
        
        // 5. 从 URL 参数获取（备用方式）
        if (!shopName) {
            const urlParams = new URLSearchParams(window.location.search);
            shopName = urlParams.get('shop_name') || '';
        }

        // 如果获取不到商家信息，则不显示飘窗
        if (!shopName && !merchantId) {
            console.log('未找到商家信息，不显示飘窗');
            return;
        }

        // 构建请求参数
        const params = new URLSearchParams();
        if (shopName) params.append('shop_name', shopName);
        if (merchantId) params.append('merchant_id', merchantId);

        // 发送请求获取飘窗配置
        fetch(baseUrl + '/plugin/Imagesxuanfu/api/fetchData?' + params.toString(), {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200 && data.data && data.data.status === 1 && data.data.image_url) {
                createFloatImage(data.data);
            } else {
                console.log('未找到有效的飘窗配置');
            }
        })
        .catch(error => {
            console.error('获取飘窗配置失败:', error);
        });
    }

    function createFloatImage(config) {
        try {
            const existingFloat = document.querySelector('.float-image-container');
            if (existingFloat) {
                document.body.removeChild(existingFloat);
            }

            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            
            let { width, height, bottom, right, min_size = 200, max_size = 500 } = config;
            
            // 使用配置的宽度和高度，但确保在min_size和max_size范围内
            width = Math.min(Math.max(width, min_size), max_size);
            height = width; // 保持正方形
            
            // 获取屏幕尺寸
            const screenWidth = window.innerWidth;
            const screenHeight = window.innerHeight;
            
            // 移动端适配
            if (isMobile) {
                const mobileMaxWidth = screenWidth * 0.5;
                if (width > mobileMaxWidth) {
                    width = mobileMaxWidth;
                    height = width;
                }
                bottom = Math.max(20, bottom);
                right = Math.max(20, right);
            }

            const container = document.createElement('div');
            container.className = 'float-image-container';
            container.style.cssText = `
                position: fixed;
                bottom: ${bottom}px;
                right: ${right}px;
                z-index: 9999;
                cursor: pointer;
                transition: all 0.3s ease;
                width: ${width}px;
                height: ${height}px;
                overflow: hidden;
                border-radius: 8px;
                background-color: transparent;
                box-shadow: none;
                line-height: 0;
                font-size: 0;
            `;

            const image = document.createElement('img');
            image.onerror = function() {
                console.error('图片加载失败:', config.image_url);
                if (!this.src.startsWith(window.location.origin)) {
                    this.src = window.location.origin + this.src;
                }
            };
            image.src = config.image_url;
            image.style.cssText = `
                width: 100%;
                height: 100%;
                object-fit: cover;
                display: block;
                background-color: transparent;
                vertical-align: bottom;
                margin: 0;
                padding: 0;
            `;

            // 修改关闭按钮样式
            const closeBtn = document.createElement('div');
            closeBtn.innerHTML = '×';
            closeBtn.style.cssText = `
                position: absolute;
                top: 5px;
                right: 5px;
                width: ${isMobile ? '32px' : '28px'};
                height: ${isMobile ? '32px' : '28px'};
                background: rgba(0,0,0,0.6);
                color: white;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                font-size: ${isMobile ? '24px' : '20px'};
                z-index: 10000;
                opacity: 0;
                visibility: hidden;
                transition: opacity 0.2s ease, visibility 0.2s ease;
            `;

            container.appendChild(image);
            container.appendChild(closeBtn);
            document.body.appendChild(container);

            // 添加鼠标悬停事件，显示关闭按钮
            container.addEventListener('mouseenter', () => {
                closeBtn.style.opacity = '1';
                closeBtn.style.visibility = 'visible';
            });

            // 添加鼠标离开事件，隐藏关闭按钮
            container.addEventListener('mouseleave', () => {
                closeBtn.style.opacity = '0';
                closeBtn.style.visibility = 'hidden';
            });

            // 移动设备触摸事件支持
            if (isMobile) {
                // 触摸开始时显示关闭按钮
                container.addEventListener('touchstart', () => {
                    closeBtn.style.opacity = '1';
                    closeBtn.style.visibility = 'visible';
                    // 设置一个定时器，几秒后自动隐藏
                    setTimeout(() => {
                        closeBtn.style.opacity = '0';
                        closeBtn.style.visibility = 'hidden';
                    }, 3000); // 3秒后隐藏
                });
            }

            // 创建侧边弹出框
            const popupContainer = document.createElement('div');
            popupContainer.className = 'float-image-popup';
            popupContainer.style.cssText = `
                position: fixed;
                z-index: 9998;
                display: none;
                transition: all 0.3s ease;
                background: white;
                border-radius: ${isMobile ? '16px' : '20px'};
                box-shadow: 0 8px 32px rgba(0,0,0,0.12);
                width: ${isMobile ? '85%' : '320px'};
                max-width: ${isMobile ? '320px' : '320px'};
                opacity: 0;
                transform: scale(0.95);
                overflow: hidden;
                padding-bottom: ${isMobile ? '20px' : '15px'};
                border: 1px solid rgba(0,0,0,0.03);
                line-height: normal;
                font-size: 16px;
            `;

            const popupContent = document.createElement('div');
            popupContent.style.cssText = `
                position: relative;
                padding: ${isMobile ? '30px 15px 10px' : '25px 15px 10px'};
                line-height: normal;
                font-size: 16px;
            `;

            // 创建弹窗标题
            const popupTitle = document.createElement('div');
            popupTitle.style.cssText = `
                font-size: ${isMobile ? '18px' : '18px'};
                font-weight: 600;
                margin-bottom: ${isMobile ? '25px' : '20px'};
                color: #333;
                text-align: center;
                letter-spacing: 1px;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            `;
            popupTitle.textContent = config.popup_title || '扫描二维码';

            // 创建图片容器
            const imageContainer = document.createElement('div');
            const containerSize = isMobile ? Math.min(240, window.innerWidth * 0.6) : 290;
            imageContainer.style.cssText = `
                background: white;
                padding: 0;
                border-radius: 12px;
                margin: 0 auto;
                width: ${containerSize}px;
                height: ${containerSize}px;
                display: flex;
                align-items: center;
                justify-content: center;
                overflow: hidden;
                box-shadow: 0 4px 12px rgba(0,0,0,0.03);
                border: 1px solid rgba(0,0,0,0.05);
                line-height: 0;
                font-size: 0;
            `;

            const popupImage = document.createElement('img');
            popupImage.src = config.popup_image_url || config.image_url;
            popupImage.style.cssText = `
                width: 92%;
                height: 92%;
                object-fit: contain;
                display: block;
                border-radius: 6px;
                vertical-align: bottom;
                margin: 0;
                padding: 0;
            `;

            // 添加底部文字
            const popupFooter = document.createElement('div');
            popupFooter.style.cssText = `
                font-size: ${isMobile ? '13px' : '14px'};
                margin-top: ${isMobile ? '22px' : '18px'};
                color: #888;
                text-align: center;
                padding: 0 10px;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
                line-height: 1.5;
            `;
            popupFooter.textContent = config.popup_footer || '请使用扫码软件扫描';

            const popupCloseBtn = document.createElement('div');
            popupCloseBtn.innerHTML = '×';
            popupCloseBtn.style.cssText = `
                position: absolute;
                top: ${isMobile ? '10px' : '10px'};
                right: ${isMobile ? '10px' : '10px'};
                width: ${isMobile ? '28px' : '26px'};
                height: ${isMobile ? '28px' : '26px'};
                color: #666;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                font-size: ${isMobile ? '24px' : '22px'};
                z-index: 10002;
                background: rgba(0,0,0,0.05);
                border-radius: 50%;
                font-weight: 300;
                line-height: 1;
                transition: all 0.2s ease;
            `;
            
            // 添加关闭按钮悬停效果
            popupCloseBtn.onmouseover = () => {
                popupCloseBtn.style.background = 'rgba(0,0,0,0.1)';
                popupCloseBtn.style.color = '#444';
            };
            
            popupCloseBtn.onmouseout = () => {
                popupCloseBtn.style.background = 'rgba(0,0,0,0.05)';
                popupCloseBtn.style.color = '#666';
            };

            // 添加指向悬浮框的三角形
            const arrow = document.createElement('div');
            arrow.style.cssText = `
                position: absolute;
                width: 0; 
                height: 0;
                border-style: solid;
                border-width: 8px;
                z-index: 10002;
            `;

            imageContainer.appendChild(popupImage);
            popupContent.appendChild(popupTitle);
            popupContent.appendChild(imageContainer);
            popupContent.appendChild(popupFooter);
            popupContent.appendChild(popupCloseBtn);
            popupContainer.appendChild(popupContent);
            popupContainer.appendChild(arrow);
            document.body.appendChild(popupContainer);

            // 点击图片显示弹窗
            image.onclick = (e) => {
                e.stopPropagation();
                
                // 获取悬浮框位置
                const rect = container.getBoundingClientRect();
                const currentScreenWidth = window.innerWidth;
                const currentScreenHeight = window.innerHeight;
                
                // 计算弹窗尺寸
                const popupWidth = isMobile ? Math.min(currentScreenWidth * 0.9, 320) : 320;
                const popupHeight = isMobile ? 
                    (containerSize + 100) : // 移动端弹窗高度
                    380; // 桌面端弹窗高度
                
                // 计算弹窗位置
                let popupLeft, popupTop;
                
                if (isMobile) {
                    // 移动端始终居中显示
                    popupLeft = (currentScreenWidth - popupWidth) / 2;
                    popupTop = (currentScreenHeight - popupHeight) / 2;
                    
                    // 隐藏箭头
                    arrow.style.display = 'none';
                    
                    // 在移动端弹窗显示时隐藏悬浮框，避免遮挡
                    container.style.opacity = '0';
                    container.style.visibility = 'hidden';
                } else {
                    // 桌面端始终让弹窗显示在悬浮窗的左侧
                    popupLeft = rect.left - popupWidth - 15;
                    popupTop = rect.top + (rect.height - popupHeight) / 2;
                    
                    // 设置三角形指向右侧
                    arrow.style.display = 'block';
                    arrow.style.right = '-15px';
                    arrow.style.left = 'auto';
                    arrow.style.top = '50%';
                    arrow.style.bottom = 'auto';
                    arrow.style.transform = 'translateY(-50%)';
                    arrow.style.borderColor = 'transparent transparent transparent white';
                    
                    // 如果左侧空间不足，则切换到右侧显示
                    if (popupLeft < 10) {
                        popupLeft = rect.right + 15;
                        
                        // 设置三角形指向左侧
                        arrow.style.left = '-15px';
                        arrow.style.right = 'auto';
                        arrow.style.borderColor = 'transparent white transparent transparent';
                    }
                }
                
                // 确保弹窗不超出屏幕边界
                popupLeft = Math.max(10, Math.min(currentScreenWidth - popupWidth - 10, popupLeft));
                popupTop = Math.max(10, Math.min(currentScreenHeight - popupHeight - 10, popupTop));
                
                // 设置弹窗位置
                popupContainer.style.left = `${popupLeft}px`;
                popupContainer.style.top = `${popupTop}px`;
                popupContainer.style.transform = `scale(0.95)`;
                
                // 移动端添加背景遮罩
                if (isMobile) {
                    const overlay = document.createElement('div');
                    overlay.className = 'float-image-overlay';
                    overlay.style.cssText = `
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(0,0,0,0.4);
                        z-index: 9997;
                        opacity: 0;
                        transition: opacity 0.2s ease;
                    `;
                    document.body.appendChild(overlay);
                    
                    // 记录遮罩元素
                    popupContainer.overlay = overlay;
                    
                    // 点击遮罩关闭弹窗
                    overlay.onclick = () => {
                        closePopup();
                    };
                    
                    // 显示遮罩
                    setTimeout(() => {
                        overlay.style.opacity = '1';
                    }, 5);
                    
                    // 移动端设置弹窗样式
                    popupContainer.style.boxShadow = '0 4px 16px rgba(0,0,0,0.08)';
                    popupContainer.style.left = '50%';
                    popupContainer.style.top = '50%';
                    popupContainer.style.transform = 'translate(-50%, -50%) scale(0.95)';
                }
                
                // 显示弹窗
                popupContainer.style.display = 'block';
                
                // 触发重排后显示动画
                setTimeout(() => {
                    popupContainer.style.opacity = '1';
                    
                    // 根据设备类型使用不同的动画
                    if (isMobile) {
                        popupContainer.style.transform = 'translate(-50%, -50%) scale(1)';
                    } else {
                        popupContainer.style.transform = 'scale(1)';
                    }
                }, 10);
            };

            // 关闭弹窗
            popupCloseBtn.onclick = (e) => {
                e.stopPropagation();
                closePopup();
            };
            
            // 创建统一的关闭弹窗函数
            const closePopup = () => {
                // 根据设备类型使用不同的动画
                if (isMobile && popupContainer.overlay) {
                    popupContainer.style.transform = 'translate(-50%, -50%) scale(0.95)';
                } else {
                    popupContainer.style.transform = 'scale(0.95)';
                }
                
                popupContainer.style.opacity = '0';
                
                // 如果有遮罩，也淡出遮罩
                if (popupContainer.overlay) {
                    popupContainer.overlay.style.opacity = '0';
                }
                
                setTimeout(() => {
                    popupContainer.style.display = 'none';
                    
                    // 移除遮罩
                    if (popupContainer.overlay) {
                        document.body.removeChild(popupContainer.overlay);
                        popupContainer.overlay = null;
                    }
                    
                    // 关闭弹窗后，如果是移动端，恢复悬浮框显示
                    if (isMobile) {
                        container.style.opacity = '1';
                        container.style.visibility = 'visible';
                    }
                }, 200);
            };

            document.addEventListener('click', (e) => {
                // 点击外部时关闭弹窗
                if (!popupContainer.contains(e.target) && e.target !== image) {
                    closePopup();
                }
            });

            // 如果有链接，设置点击图片跳转
            if (config.link_url) {
                popupImage.style.cursor = 'pointer';
                popupImage.onclick = (e) => {
                    e.stopPropagation();
                    window.open(config.link_url, '_blank');
                };
            }

            closeBtn.onclick = (e) => {
                e.stopPropagation();
                container.style.opacity = '0';
                setTimeout(() => {
                    container.parentNode?.removeChild(container);
                    popupContainer.parentNode?.removeChild(popupContainer);
                }, 300);
            };

            container.style.opacity = '0';
            setTimeout(() => container.style.opacity = '1', 100);
        } catch (error) {
            console.error('创建悬浮窗失败:', error);
        }
    }

    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initFloatImage);
    } else {
        initFloatImage();
    }
})(); 