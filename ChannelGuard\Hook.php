<?php

namespace plugin\ChannelGuard;

use think\facade\Db;
use app\common\service\EmailService;
use app\common\service\SmsService;

class Hook
{
    public function handle()
    {
        try {
            // 检查是否启用监控
            if (intval(plugconf("ChannelGuard.monitor_status") ?? 0) !== 1) {
                error_log("ChannelGuard: 监控未启用");
                return true; // 返回 true 表示任务完成
            }

            $result = $this->checkAccounts();
            
            // 记录执行日志
            error_log("ChannelGuard Hook executed at: " . date('Y-m-d H:i:s'));
            
            return true; // 返回 true 表示任务完成
            
        } catch (\Exception $e) {
            error_log("ChannelGuard Hook error: " . $e->getMessage());
            return true; // 即使发生异常也返回 true，避免任务挂起
        }
    }

    private function checkAccounts()
    {
        try {
            // 获取配置
            $threshold = intval(plugconf("ChannelGuard.fail_threshold") ?? 6);
            $notifyType = intval(plugconf("ChannelGuard.notify_type") ?? 1);
            $email = plugconf("ChannelGuard.notify_email") ?? '';
            $mobile = plugconf("ChannelGuard.notify_mobile") ?? '';
            $checkTime = intval(plugconf("ChannelGuard.check_time") ?? 2);
            $checkSeconds = $checkTime * 60;

            // 验证通知配置
            if ($notifyType === 1 && empty($email)) {
                error_log("ChannelGuard: 请先配置通知邮箱");
                throw new \Exception("请先配置通知邮箱");
            }
            if ($notifyType === 2 && empty($mobile)) {
                error_log("ChannelGuard: 请先配置通知手机号");
                throw new \Exception("请先配置通知手机号");
            }

            // 查询所有启用的渠道账户
            $activeAccounts = Db::name('channel_account')
                ->where('status', 1)
                ->field(['id', 'name', 'params'])
                ->select()
                ->toArray();

            if (empty($activeAccounts)) {
                error_log("ChannelGuard: 没有找到启用的渠道账户");
                throw new \Exception("没有找到启用的渠道账户");
            }

            $checkedCount = 0;
            $closedCount = 0;
            $currentTime = time();
            $checkStartTime = $currentTime - $checkSeconds;

            foreach ($activeAccounts as $account) {
                try {
                    $checkedCount++;
                    error_log("ChannelGuard: 正在检查账户 {$account['name']} (ID: {$account['id']})");
                    
                    // 获取配置时间内的订单
                    $recentOrders = Db::name('order')
                        ->where('channel_account_id', $account['id'])
                        ->where('create_time', '<=', $checkStartTime)
                        ->where('create_time', '>', $checkStartTime - $checkSeconds)
                        ->order('create_time desc')
                        ->field(['id', 'status', 'create_time'])
                        ->select()
                        ->toArray();

                    // 检查连续未支付订单
                    $consecutiveFailures = 0;
                    $failureGroup = null;

                    // 从最新的订单开始检查
                    for ($i = 0; $i < count($recentOrders); $i++) {
                        if ($recentOrders[$i]['status'] === 0) {
                            $consecutiveFailures++;
                            
                            if ($consecutiveFailures === $threshold) {
                                $groupOrders = array_slice($recentOrders, $i - $threshold + 1, $threshold);
                                $firstOrderTime = end($groupOrders)['create_time'];
                                $lastOrderTime = reset($groupOrders)['create_time'];
                                
                                if (($lastOrderTime - $firstOrderTime) <= $checkSeconds) {
                                    $orderIds = array_column($groupOrders, 'id');
                                    $currentStatuses = Db::name('order')
                                        ->whereIn('id', $orderIds)
                                        ->where('status', 0)
                                        ->count();
                                    
                                    if ($currentStatuses === $threshold) {
                                        $failureGroup = $groupOrders;
                                        error_log("ChannelGuard: 账户 {$account['name']} 发现连续 {$threshold} 个未支付订单");
                                        break;
                                    }
                                }
                            }
                        } else {
                            $consecutiveFailures = 0;
                        }
                    }

                    // 如果找到了失败组，处理账户切换
                    if ($failureGroup) {
                        $params = json_decode($account['params'], true);
                        $currentPayType = $params['type'] ?? '';
                        
                        if (empty($currentPayType)) {
                            error_log("ChannelGuard: 账户 {$account['id']} 支付类型为空，跳过处理");
                            continue;
                        }

                        Db::startTrans();
                        try {
                            // 关闭当前账户
                            Db::name('channel_account')
                                ->where('id', $account['id'])
                                ->update([
                                    'status' => 0
                                ]);
                            error_log("ChannelGuard: 已关闭账户 {$account['name']} (ID: {$account['id']})");

                            // 查找替代账户
                            $alternativeAccount = $this->findAlternativeAccount($account['id'], $currentPayType);

                            if ($alternativeAccount) {
                                Db::name('channel_account')
                                    ->where('id', $alternativeAccount['id'])
                                    ->update([
                                        'status' => 1
                                    ]);
                                error_log("ChannelGuard: 已启用替代账户 ID: {$alternativeAccount['id']}");
                            } else {
                                error_log("ChannelGuard: 未找到合适的替代账户");
                            }

                            $closedCount++;
                            Db::commit();

                            // 发送通知
                            try {
                                $this->sendNotification($notifyType, $account, $alternativeAccount, $email, $mobile);
                            } catch (\Exception $e) {
                                // 通知发送失败不影响主流程
                                error_log("ChannelGuard: 发送通知失败，但不影响账户切换: " . $e->getMessage());
                            }

                        } catch (\Throwable $e) {
                            Db::rollback();
                            error_log("ChannelGuard: 处理账户 {$account['id']} 失败：" . $e->getMessage());
                            // 不抛出异常，继续处理下一个账户
                        }
                    }
                } catch (\Throwable $e) {
                    error_log("ChannelGuard: 处理账户 {$account['id']} 失败: " . $e->getMessage());
                    // 不抛出异常，继续处理下一个账户
                }
            }

            error_log("ChannelGuard: 检查完成，共检查 {$checkedCount} 个账户，关闭 {$closedCount} 个异常账户");
            return [
                'success' => true,
                'message' => "检查完成：共检查 {$checkedCount} 个账户" . 
                            ($closedCount > 0 ? "，已自动关闭 {$closedCount} 个异常账户" : "，未发现异常账户"),
                'data' => [
                    'checked' => $checkedCount,
                    'closed' => $closedCount
                ]
            ];

        } catch (\Exception $e) {
            error_log("ChannelGuard: 检查账户失败: " . $e->getMessage());
            return [
                'success' => false,
                'message' => "检查失败：" . $e->getMessage(),
                'data' => [
                    'checked' => $checkedCount ?? 0,
                    'closed' => $closedCount ?? 0
                ]
            ];
        }
    }

    private function findAlternativeAccount($currentId, $payType)
    {
        // 先查找ID更大的账号
        $alternativeAccount = Db::name('channel_account')
            ->where('status', 0)
            ->where('id', '>', $currentId)
            ->whereRaw("JSON_EXTRACT(params, '$.type') = ?", [$payType])
            ->order('id asc')
            ->find();

        // 如果没找到更大ID的账号，就从最小ID开始查找
        if (!$alternativeAccount) {
            $alternativeAccount = Db::name('channel_account')
                ->where('status', 0)
                ->where('id', '<', $currentId)
                ->whereRaw("JSON_EXTRACT(params, '$.type') = ?", [$payType])
                ->order('id asc')
                ->find();
        }

        return $alternativeAccount;
    }

    private function sendNotification($notifyType, $account, $alternativeAccount, $email, $mobile)
    {
        try {
            if ($notifyType === 1) {
                // 发送邮件通知
                $service = new EmailService();
                $message = "账号切换通知：{$account['name']} (ID: {$account['id']}) 已切换到新账号";
                if (isset($alternativeAccount)) {
                    $message .= " (ID: {$alternativeAccount['id']})";
                }
                
                $service->subject('账号切换通知')
                       ->message($message)
                       ->to($email)
                       ->send();
                error_log("ChannelGuard: 邮件通知已发送");
            } else if ($notifyType === 2) {
                // 发送短信通知
                $service = new SmsService();
                $params = [
                    'account' => $account['name'],
                    'old_id' => (string)$account['id'],
                    'new_id' => isset($alternativeAccount) ? (string)$alternativeAccount['id'] : '无'
                ];
                
                $testMessage = str_replace(
                    ['{account}', '{old_id}', '{new_id}'],
                    [$account['name'], $account['id'], isset($alternativeAccount) ? $alternativeAccount['id'] : '无'],
                    '渠道账户{account}(ID:{old_id})已切换到新账号(ID:{new_id})'
                );
                error_log("ChannelGuard: 短信内容测试: " . $testMessage);
                
                $ret = $service->sendCode($mobile, 'account_switch', $params);
                if (!$ret) {
                    $error = $service->getError();
                    error_log("ChannelGuard: 短信发送失败：" . $error);
                    
                    // 记录到 system_log 表
                    Db::name('system_log')->insert([
                        'content' => "渠道账户{$account['name']}(ID:{$account['id']})切换账号短信通知发送失败：{$error}",
                        'create_time' => time()
                    ]);
                    
                    throw new \Exception("短信发送失败：" . $error);
                }
                error_log("ChannelGuard: 短信通知已发送");
            }
        } catch (\Exception $e) {
            error_log("ChannelGuard: 发送通知失败: " . $e->getMessage());
            
            // 记录到 system_log 表
            $notifyType = $notifyType === 1 ? '邮件' : '短信';
            Db::name('system_log')->insert([
                'content' => "渠道账户{$account['name']}(ID:{$account['id']})切换账号{$notifyType}通知发送失败：" . $e->getMessage(),
                'create_time' => time()
            ]);
            
            throw $e;
        }
    }
} 