<?php

return [
    "status" => 0,
    // API密钥配置
    "api_key" => "",
    "api_secret" => "",
    // API接口开关配置
    "api_endpoints" => json_encode([
        "get_user_info" => [
            "enabled" => true,
            "name" => "获取用户信息",
            "description" => "根据用户ID获取用户详细信息，包括用户名、邮箱和注册时间"
        ],
        "create_order" => [
            "enabled" => false,
            "name" => "创建订单",
            "description" => "创建新订单，需要用户ID和商品信息"
        ],
        "update_profile" => [
            "enabled" => true,
            "name" => "更新资料",
            "description" => "更新用户个人信息，支持修改用户名、头像和联系方式"
        ]
    ]),
    // 访问限制配置
    "access_limits" => json_encode([
        "daily_limit" => 1000,
        "qps_limit" => 1
    ])
];