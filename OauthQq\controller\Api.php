<?php

namespace plugin\OauthQq\controller;

use app\common\controller\BasePlugin;
use app\common\service\HttpService;
use app\common\library\RandomExtend;
use app\common\model\User as UserModel;

class Api extends BasePlugin {

    protected $scene = [
        'user'
    ];
    protected $noNeedLogin = [
        'start',
        'login',
        'callback',
        'query',
    ];

    public function start() {
        if (plugconf("OauthQq.status") == 1) {
            $scene = input('scene/s', '');
            $is_mobile = request()->isMobile();
            $platform_code = RandomExtend::random(10, 3);

            $url = (string) plugin_url('OauthQq/Api/login', ['platform_code' => $platform_code, 'scene' => $scene, 'is_mobile' => $is_mobile ? 1 : 0], false, true);
            $this->success('success', [
                "redirect_url" => $url,
                "platform_code" => $platform_code,
                    ], 1);
        } else {
            return $this->error("抱歉！未开启商户QQ登录", null, 0);
        }
    }

    public function login() {
        $is_mobile = input('is_mobile/d', 0);
        $scene = input('scene/s', '');
        $platform_code = input('platform_code/s', '');

        $params = [
            'response_type' => 'code',
            'client_id' => plugconf("OauthQq.appId"),
            'redirect_uri' => plugin_url("OauthQq/Api/callback", ['platform_code' => $platform_code, 'scene' => $scene, 'is_mobile' => $is_mobile], false, true),
            'state' => 'login',
            'scope' => 'login',
        ];

        return redirect('https://graph.qq.com/oauth2.0/authorize?' . http_build_query($params));
    }

    public function callback() {
        $platform_code = input('platform_code/s', '');
        $is_mobile = input('is_mobile/d', 0);
        $scene = input('scene/s', '');
        $code = input('code/s', '');
        $accessToken = $this->getAccessToken(plugconf("OauthQq.appId"), plugconf("OauthQq.appKey"), plugin_url("OauthQq/Api/callback", ['platform_code' => $platform_code, 'scene' => $scene, 'is_mobile' => $is_mobile], false, true), $code);
        $openid = $this->getOpendId($accessToken);
        if ($openid == "") {
            $this->error("抱歉！获取openid失败！", null, 500, 'html');
        }
        cache("oauth_" . $platform_code, ['from' => 'qq', 'openid' => $openid], 600);

        if ($scene == "bind" && UserModel::where(['qq_openid' => $openid])->find()) {
            return $this->success("当前QQ已绑定其他账号", null, 500, 'html');
        }

        if ($scene == "login") {
            if ($is_mobile == 1) {
                return redirect("/merchant/login?from=qq&platform_code=" . $platform_code);
            } else {
                return $this->success("授权成功", null, 200, 'html');
            }
        } elseif ($scene == "bind") {
            if ($is_mobile == 1) {
                return redirect("/merchant/user/setting?from=qq&platform_code=" . $platform_code);
            } else {
                return $this->success("授权成功", null, 200, 'html');
            }
        }
    }

    public function query() {
        $platform_code = input('platform_code/s', '');
        $scene = input('scene/s', '');
        $cache = cache("oauth_" . $platform_code);
        if ($cache != null && isset($cache['openid'])) {
            if ($scene == "bind") {
                $user = $this->user->getUser();
                $user->qq_openid = $cache['openid'];
                $user->save();
                $this->success("绑定成功", null, 1);
            } elseif ($scene == "login") {
                $this->success("登录成功", null, 1);
            }
        } else {
            $this->error("授权失败", null, 0);
        }
    }

    public function unbind() {
        $user = $this->user->getUser();
        $user->qq_openid = "";
        $user->save();
        $this->success("解绑成功", null, 1);
    }

    private function getAccessToken($app_id, $appkey, $callback, $code) {
        $reqData = array(
            "grant_type" => "authorization_code",
            "client_id" => $app_id,
            "redirect_uri" => $callback,
            "client_secret" => $appkey,
            "code" => $code
        );
        $ret = HttpService::get('https://graph.qq.com/oauth2.0/token', $reqData);
        parse_str($ret, $parseData);
        if (!isset($parseData['access_token'])) {
            echo $ret;
            die;
        } else {
            return $parseData['access_token'];
        }
    }

    private function getOpendId($accessToken) {
        $reqData = [
            'access_token' => $accessToken,
            'fmt' => 'json'
        ];
        $ret = HttpService::get('https://graph.qq.com/oauth2.0/me', $reqData);
        $paserData = json_decode($ret, true);
        return $paserData['openid'];
    }
}
