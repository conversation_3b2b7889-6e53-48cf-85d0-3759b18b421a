<?php

return [
    "version" => "1.0.0",
    "min_system_version" => "2.9.0", 
    "category_name" => "短链接",
    "logo" => "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAANwSURBVHhe7Z1NaBNBGIY3/kJV/EVE8KCC4EEEQRT0pngQQfQgeBAP4kU8eBM8ehA8iQdBPIjgwYMHDx48iCAIoiAIoiB48OBBREREREQkIiIS+r7ZSWy7+WY3M7vJbvI+8EKzmZmd/Z6d7MzON9NoNBqNRvMvzWbz1Gq1eqdSqTxbXV29h8+3K5XKxc7xra2tE/F/10SFADkGvEZwZ4Ef+H4b34/h+FPgM45/A3pw7Bm+78Lx/ThvX3wZTVIQlEPA1SAQBPcbgnwBXz8joPtxrA5cwfGLwG0ce4GgHsP3GRy/Cjzw0oFgX8Zn7+Kz1/H9II6dwfkX4uvvLBAQBOUgcB0B6AXBD4L5GEG8hM+vAXeAx/jsCgJ+FucfQ1BmcO51nHsL17kfX1cTFwTgCBCNwFcE5QOCPI/vt3D8Eb4/wfFZfL8BzOP7XRw/D9zEuY9x3nWcfw7XOxpfXuMKjn8IQVlGgD8iuE/x/TGOPwC6OPYcx1/h2Dt8XsL3BXz/hHNe4LwbOP8gLqOJCwJwEIFYDgKCIC8iuHcQtLs4/gj4iO+LOD6Hzx/w+QWCPI/Pn+LYIs65gOtl8eU1rhCEowjIMoISBPwdgvcY+IDPn/H9K44t4bwveP8Cn7/h8zKOP8V5F3GtQ7iMxhUEYT8CshIEBIF+jWC+wvfvCPoKsIbPv/D5N/ATx37g+E98/4XPv3HuGq6Vx2U0rhCIHAKyigD9QZD/Inh/gyCvI9AbwAY+b+J48P4vzmni2hv47Dqut4nzm7iMxhUEIouArAUBQZB/I3ibQRCC90EQgyAHn4MgbwZBxrFNXK+Fa23gvJa+h7hCIDII0N8gIEEQg/dBkNeDQOP4Bs5p41pBkNs4bwvnteP/r3EFgdiNwGwEQUHQWsHnIMjBeyAIeBDkv/F/1cQFgdiFwGxuF2gEsb1dwNvxf9PEBcHYicD83S7oQbCD98H74H0Q9OB9EPzgvSYpCMoOBGZru8AjcEHQg/fBe01SEJydCFQQpH/eB++D98H7v/F/0SQFAdqFgG0XbE1SEKSdCNh2wdYkBYHaiYBtF2xNUhCsnQjYdsHWJAXB2omAbRdsTVIQsCAg/4KtSQoC5gdbkxQEzQ+2JikImh9sTVIQND/YmqQgaH6wNRqNRqOZajKZf4PFJWZXz8PKAAAAAElFTkSuQmCC",
    "name" => "博天通用短链接",
    "description" => "博天通用短链接",
    "menu" => [
        [
            'tag' => 'a',
            'name' => '官网',
            'href' => 'https://t.aojiasuq.top/',
        ],
    ],
    "form_fields" => [
        [
            'id' => 'domain',
            'name' => 'API域名',
            'placeholder' => '请输入API域名，例如：https://t.aojiasuq.top',
            'required' => true,
            'type' => 'input',
        ],
        [
            'id' => 'token',
            'name' => 'API Token',
            'placeholder' => '在平台获取的API Token，请确保输入正确',
            'required' => true,
            'type' => 'input',
        ],
        [
            'id' => 'type',
            'name' => '短网址类型',
            'placeholder' => '请输入正确的短网址类型，例如：suiji',
            'required' => true,
            'type' => 'input',
        ],
        [
            'id' => 'pattern',
            'name' => '短网址模式',
            'required' => true,
            'type' => 'radio',
            'data' => [
                [
                    'name' => '普通模式',
                    'value' => '1',
                ],
                [
                    'name' => '防红模式',
                    'value' => '2',
                ],
                [
                    'name' => '直链模式',
                    'value' => '3',
                ],
                [
                    'name' => '缩短模式',
                    'value' => '4',
                ]
            ],
        ],
    ]
]; 