<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商家订单导出</title>
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            min-height: 100vh;
            background: linear-gradient(135deg, #f5f7fa, #e4e7eb);
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }

        .container {
            width: 700px;
            padding: 30px;
        }

        .el-card {
            background: rgba(255, 255, 255, 0.98);
            border: 1px solid rgba(0, 0, 0, 0.05);
            border-radius: 15px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
        }

        .el-card__body {
            padding: 30px;
        }

        h2 {
            color: #2c3e50;
            font-size: 24px;
            margin-bottom: 30px;
            text-align: center;
            font-weight: 600;
            position: relative;
            padding-bottom: 15px;
        }

        h2:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #2563eb, #1d4ed8);
        }

        .el-form-item {
            margin-bottom: 25px;
        }

        .el-input {
            --el-input-border-radius: 8px;
        }

        .el-date-editor {
            width: 100% !important;
            --el-date-editor-border-radius: 8px;
        }

        .button-group {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 30px;
        }

        .el-button {
            padding: 12px 25px;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .el-button--primary {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            border: none;
            color: white;
        }

        .el-button--success {
            background: linear-gradient(135deg, #059669, #047857);
            border: none;
            color: white;
        }

        .el-button--primary:hover,
        .el-button--success:hover {
            transform: translateY(-2px);
            opacity: 0.9;
        }

        .el-button:disabled {
            opacity: 0.6;
            transform: none;
        }

        .el-tag {
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: 14px;
            margin-left: 15px;
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
        }

        .el-tag.el-tag--info {
            background: linear-gradient(135deg, #9ca3af, #6b7280);
            opacity: 0.8;
        }

        .data-count {
            text-align: center;
            margin-top: 20px;
        }

        .pulse {
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .el-input:focus-within,
        .el-date-editor:focus-within {
            box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
        }

        .merchant-info {
            background: linear-gradient(135deg, #e0f2fe, #b3e5fc);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
            color: #0277bd;
            font-weight: 500;
        }

        .tab-content {
            padding-top: 20px;
        }

        .el-tabs__item {
            font-weight: 500;
        }

        .el-tabs__item.is-active {
            color: #2563eb;
        }

        .el-tabs__active-bar {
            background-color: #2563eb;
        }
    </style>
</head>

<body>
    <div id="app" class="container">
        <el-card>
            <h2>商家订单导出工具</h2>
            <div class="merchant-info">
                <i class="el-icon-user"></i>
                只能导出您自己账户的订单和卡密信息
            </div>
            
            <el-tabs v-model="activeTab" type="card">
                <el-tab-pane label="订单导出" name="orders">
                    <div class="tab-content">
            <el-form :model="form" label-width="120px">
                <el-form-item label="商品关键词">
                    <el-input
                        v-model="form.keyword"
                        placeholder="可选，输入商品名称关键词筛选"
                        clearable>
                    </el-input>
                </el-form-item>

                <el-form-item label="订单状态">
                    <el-select v-model="form.orderStatus" placeholder="请选择订单状态" style="width: 100%">
                        <el-option label="全部订单" value="all"></el-option>
                        <el-option label="已支付" value="paid"></el-option>
                        <el-option label="未支付" value="unpaid"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="时间范围">
                    <el-date-picker
                        v-model="form.dateRange"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="YYYY-MM-DD"
                        :shortcuts="dateShortcuts"
                        :locale="zhCn"
                        size="default"
                        style="width: 100%">
                    </el-date-picker>
                </el-form-item>

                <el-form-item label="导出格式">
                    <el-radio-group v-model="form.exportFormat">
                        <el-radio label="excel">Excel格式 (.xlsx)</el-radio>
                        <el-radio label="txt">文本格式 (.txt)</el-radio>
                    </el-radio-group>
                </el-form-item>

                        <div class="button-group">
                            <el-button 
                                type="primary" 
                                :loading="loading" 
                                @click="queryData">
                                查询数据
                            </el-button>
                            <el-button 
                                type="success" 
                                @click="exportData"
                                :disabled="!count">
                                导出数据
                            </el-button>
                        </div>

                        <div class="data-count" v-if="count !== null">
                            <el-tag :type="count > 0 ? 'success' : 'info'" class="pulse">
                                查询到 {{ count }} 条订单{{ statusText }}
                                <template v-if="count > 0">
                                    &nbsp;商品总金额 {{ goodsAmount }} 元
                                    &nbsp;实收金额 {{ actualAmount }} 元
                                    <span v-if="hasFee">(含手续费)</span>
                                    &nbsp;商家实收 {{ merchantAmount }} 元
                                </template>
                            </el-tag>
                        </div>
                    </div>
                </el-tab-pane>
                
                <el-tab-pane label="卡密导出" name="cards">
                    <div class="tab-content">
                        <el-form-item label="商品关键词">
                            <el-input
                                v-model="cardForm.keyword"
                                placeholder="可选，输入商品名称关键词筛选"
                                clearable>
                            </el-input>
                        </el-form-item>

                        <el-form-item label="卡密状态">
                            <el-select v-model="cardForm.status" placeholder="请选择卡密状态" style="width: 100%">
                                <el-option label="全部卡密" value="all"></el-option>
                                <el-option label="已售出" value="used"></el-option>
                                <el-option label="未售出" value="unused"></el-option>
                            </el-select>
                        </el-form-item>

                        <el-form-item label="时间范围">
                            <el-date-picker
                                v-model="cardForm.dateRange"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                value-format="YYYY-MM-DD"
                                :shortcuts="dateShortcuts"
                                :locale="zhCn"
                                size="default"
                                style="width: 100%">
                            </el-date-picker>
                        </el-form-item>

                        <el-form-item label="导出格式">
                            <el-radio-group v-model="cardForm.exportFormat">
                                <el-radio label="excel">Excel格式 (.xlsx)</el-radio>
                                <el-radio label="txt">文本格式 (.txt)</el-radio>
                            </el-radio-group>
                        </el-form-item>

                        <div class="button-group">
                            <el-button
                                type="primary"
                                :loading="cardLoading"
                                @click="queryCardData">
                                查询卡密
                            </el-button>
                            <el-button
                                type="success"
                                @click="exportCardData"
                                :disabled="!cardCount">
                                导出卡密
                            </el-button>
                        </div>

                        <div class="data-count" v-if="cardCount !== null">
                            <el-tag :type="cardCount > 0 ? 'success' : 'info'" class="pulse">
                                查询到 {{ cardCount }} 条卡密
                                <template v-if="cardCount > 0">
                                    &nbsp;已售出 {{ usedCount }} 条
                                    &nbsp;未售出 {{ unusedCount }} 条
                                </template>
                            </el-tag>
                        </div>
                    </div>
                </el-tab-pane>

                <el-tab-pane label="删除卡密" name="delete">
                    <div class="tab-content">
                        <div class="merchant-info" style="background: linear-gradient(135deg, #fef2f2, #fecaca); color: #dc2626;">
                            <i class="el-icon-warning"></i>
                            危险操作：删除后的卡密无法恢复，请谨慎操作！
                        </div>

                        <el-form-item label="商品关键词">
                            <el-input
                                v-model="deleteForm.keyword"
                                placeholder="可选，输入商品名称关键词筛选要删除的商品"
                                clearable>
                            </el-input>
                        </el-form-item>

                        <div class="button-group">
                            <el-button
                                type="primary"
                                :loading="deleteLoading"
                                @click="queryDeleteData">
                                查询未售出卡密
                            </el-button>
                            <el-button
                                type="danger"
                                @click="confirmDeleteCards"
                                :disabled="!deleteCount || deleteLoading">
                                删除未售出卡密
                            </el-button>
                        </div>

                        <div class="data-count" v-if="deleteCount !== null">
                            <el-tag :type="deleteCount > 0 ? 'warning' : 'info'" class="pulse">
                                查询到 {{ deleteCount }} 条未售出卡密
                            </el-tag>
                        </div>

                        <!-- 商品列表 -->
                        <div v-if="goodsList && goodsList.length > 0" style="margin-top: 20px;">
                            <h4 style="margin-bottom: 15px; color: #2c3e50;">商品详情：</h4>
                            <el-table :data="goodsList" border style="width: 100%">
                                <el-table-column prop="goods_name" label="商品名称" width="300"></el-table-column>
                                <el-table-column prop="unused_count" label="未售出卡密数量" width="150" align="center">
                                    <template #default="scope">
                                        <el-tag type="warning">{{ scope.row.unused_count }}</el-tag>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>
            </el-form>
        </el-card>
    </div>

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script>
        const { createApp, ref } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        const zhCn = {
            name: 'zh-cn',
            el: {
                datepicker: {
                    now: '此刻',
                    today: '今天',
                    cancel: '取消',
                    clear: '清除',
                    confirm: '确定',
                    selectDate: '选择日期',
                    selectTime: '选择时间',
                    startDate: '开始日期',
                    startTime: '开始时间',
                    endDate: '结束日期',
                    endTime: '结束时间',
                    prevYear: '前一年',
                    nextYear: '后一年',
                    prevMonth: '上个月',
                    nextMonth: '下个月',
                    year: '年',
                    month1: '1月',
                    month2: '2月',
                    month3: '3月',
                    month4: '4月',
                    month5: '5月',
                    month6: '6月',
                    month7: '7月',
                    month8: '8月',
                    month9: '9月',
                    month10: '10月',
                    month11: '11月',
                    month12: '12月',
                    weeks: {
                        sun: '日',
                        mon: '一',
                        tue: '二',
                        wed: '三',
                        thu: '四',
                        fri: '五',
                        sat: '六'
                    }
                }
            }
        };

        const app = createApp({
            setup() {
                const activeTab = ref('orders');
                const form = ref({
                    dateRange: '',
                    keyword: '',
                    orderStatus: 'paid',
                    exportFormat: 'excel'
                });
                const cardForm = ref({
                    dateRange: '',
                    keyword: '',
                    status: 'all',
                    exportFormat: 'excel'
                });
                const deleteForm = ref({
                    keyword: ''
                });
                const count = ref(null);
                const goodsAmount = ref(null);
                const actualAmount = ref(null);
                const merchantAmount = ref(null);
                const loading = ref(false);
                const hasFee = ref(false);
                const statusText = ref('');

                // 卡密相关状态
                const cardCount = ref(null);
                const usedCount = ref(null);
                const unusedCount = ref(null);
                const cardLoading = ref(false);

                // 删除卡密相关状态
                const deleteCount = ref(null);
                const deleteLoading = ref(false);
                const goodsList = ref([]);

                // 日期快捷选项
                const dateShortcuts = [
                    {
                        text: '今天',
                        value: () => {
                            const today = new Date();
                            return [today, today];
                        }
                    },
                    {
                        text: '昨天',
                        value: () => {
                            const yesterday = new Date();
                            yesterday.setTime(yesterday.getTime() - 3600 * 1000 * 24);
                            return [yesterday, yesterday];
                        }
                    },
                    {
                        text: '最近一周',
                        value: () => {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                            return [start, end];
                        }
                    },
                    {
                        text: '最近一个月',
                        value: () => {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                            return [start, end];
                        }
                    },
                    {
                        text: '最近三个月',
                        value: () => {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                            return [start, end];
                        }
                    }
                ];

                const queryData = async () => {
                    loading.value = true;
                    try {
                        const res = await axios.post('/plugin/Fengzeroexport/user/merchantQuery', {
                            date_range: form.value.dateRange ? form.value.dateRange.join(' - ') : '',
                            keyword: form.value.keyword,
                            order_status: form.value.orderStatus
                        });

                        if (res.data.code === 200) {
                            count.value = res.data.data.count;
                            goodsAmount.value = res.data.data.goodsAmount;
                            actualAmount.value = res.data.data.actualAmount;
                            merchantAmount.value = res.data.data.merchantAmount;
                            hasFee.value = res.data.data.hasFee;
                            statusText.value = res.data.data.statusText;
                            ElMessage.success('查询成功');
                        } else {
                            count.value = 0;
                            goodsAmount.value = null;
                            actualAmount.value = null;
                            merchantAmount.value = null;
                            hasFee.value = false;
                            statusText.value = '';
                            ElMessage.error(res.data.msg || '查询失败');
                        }
                    } catch (error) {
                        count.value = 0;
                        goodsAmount.value = null;
                        actualAmount.value = null;
                        merchantAmount.value = null;
                        hasFee.value = false;
                        statusText.value = '';
                        ElMessage.error('查询失败：' + error.message);
                    } finally {
                        loading.value = false;
                    }
                };

                const exportData = async () => {
                    try {
                        const res = await axios.post('/plugin/Fengzeroexport/user/merchantExport', {
                            date_range: form.value.dateRange ? form.value.dateRange.join(' - ') : '',
                            keyword: form.value.keyword,
                            order_status: form.value.orderStatus,
                            export_format: form.value.exportFormat
                        }, {
                            responseType: 'blob'
                        });

                        // 根据导出格式设置文件类型和扩展名
                        let contentType, fileExtension;
                        if (form.value.exportFormat === 'txt') {
                            contentType = 'text/plain;charset=utf-8';
                            fileExtension = 'txt';
                        } else {
                            contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                            fileExtension = 'xlsx';
                        }

                        const blob = new Blob([res.data], { type: contentType });
                        const url = window.URL.createObjectURL(blob);
                        const link = document.createElement('a');
                        link.href = url;
                        link.download = `merchant_orders_${new Date().getTime()}.${fileExtension}`;
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                        window.URL.revokeObjectURL(url);

                        ElMessage.success('导出成功');
                    } catch (error) {
                        ElMessage.error('导出失败：' + error.message);
                    }
                };

                // 卡密查询功能
                const queryCardData = async () => {
                    cardLoading.value = true;
                    try {
                        const res = await axios.post('/plugin/Fengzeroexport/user/merchantCardQuery', {
                            date_range: cardForm.value.dateRange ? cardForm.value.dateRange.join(' - ') : '',
                            keyword: cardForm.value.keyword,
                            status: cardForm.value.status
                        });

                        if (res.data.code === 200) {
                            cardCount.value = res.data.data.total;
                            usedCount.value = res.data.data.used;
                            unusedCount.value = res.data.data.unused;
                            ElMessage.success('查询成功');
                        } else {
                            cardCount.value = 0;
                            usedCount.value = 0;
                            unusedCount.value = 0;
                            ElMessage.error(res.data.msg || '查询失败');
                        }
                    } catch (error) {
                        cardCount.value = 0;
                        usedCount.value = 0;
                        unusedCount.value = 0;
                        ElMessage.error('查询失败：' + error.message);
                    } finally {
                        cardLoading.value = false;
                    }
                };

                // 卡密导出功能
                const exportCardData = async () => {
                    try {
                        const res = await axios.post('/plugin/Fengzeroexport/user/merchantCardExport', {
                            date_range: cardForm.value.dateRange ? cardForm.value.dateRange.join(' - ') : '',
                            keyword: cardForm.value.keyword,
                            status: cardForm.value.status,
                            export_format: cardForm.value.exportFormat
                        }, {
                            responseType: 'blob'
                        });

                        // 根据导出格式设置文件类型和扩展名
                        let contentType, fileExtension;
                        if (cardForm.value.exportFormat === 'txt') {
                            contentType = 'text/plain;charset=utf-8';
                            fileExtension = 'txt';
                        } else {
                            contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                            fileExtension = 'xlsx';
                        }

                        const blob = new Blob([res.data], { type: contentType });
                        const url = window.URL.createObjectURL(blob);
                        const link = document.createElement('a');
                        link.href = url;
                        link.download = `merchant_cards_${new Date().getTime()}.${fileExtension}`;
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                        window.URL.revokeObjectURL(url);

                        ElMessage.success('导出成功');
                    } catch (error) {
                        ElMessage.error('导出失败：' + error.message);
                    }
                };

                // 查询删除数据
                const queryDeleteData = async () => {
                    deleteLoading.value = true;
                    try {
                        const res = await axios.post('/plugin/Fengzeroexport/user/merchantDeleteCards', {
                            keyword: deleteForm.value.keyword,
                            action: 'query'
                        });

                        if (res.data.code === 200) {
                            deleteCount.value = res.data.data.total_unused;
                            goodsList.value = res.data.data.goods_list;
                            ElMessage.success('查询成功');
                        } else {
                            deleteCount.value = 0;
                            goodsList.value = [];
                            ElMessage.error(res.data.msg || '查询失败');
                        }
                    } catch (error) {
                        deleteCount.value = 0;
                        goodsList.value = [];
                        ElMessage.error('查询失败：' + error.message);
                    } finally {
                        deleteLoading.value = false;
                    }
                };

                // 确认删除卡密
                const confirmDeleteCards = async () => {
                    if (deleteCount.value === 0) {
                        ElMessage.warning('没有可删除的未售出卡密');
                        return;
                    }

                    try {
                        await ElMessageBox.confirm(
                            `确定要删除 ${deleteCount.value} 条未售出卡密吗？删除后无法恢复！`,
                            '危险操作确认',
                            {
                                confirmButtonText: '确定删除',
                                cancelButtonText: '取消',
                                type: 'warning',
                                confirmButtonClass: 'el-button--danger'
                            }
                        );

                        deleteLoading.value = true;
                        const res = await axios.post('/plugin/Fengzeroexport/user/merchantDeleteCards', {
                            keyword: deleteForm.value.keyword,
                            action: 'delete'
                        });

                        if (res.data.code === 200) {
                            ElMessage.success(res.data.msg);
                            // 重新查询数据
                            queryDeleteData();
                        } else {
                            ElMessage.error(res.data.msg || '删除失败');
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            ElMessage.error('删除失败：' + error.message);
                        }
                    } finally {
                        deleteLoading.value = false;
                    }
                };

                return {
                    activeTab,
                    form,
                    cardForm,
                    deleteForm,
                    count,
                    goodsAmount,
                    actualAmount,
                    merchantAmount,
                    loading,
                    cardCount,
                    usedCount,
                    unusedCount,
                    cardLoading,
                    deleteCount,
                    deleteLoading,
                    goodsList,
                    dateShortcuts,
                    queryData,
                    exportData,
                    queryCardData,
                    exportCardData,
                    queryDeleteData,
                    confirmDeleteCards,
                    hasFee,
                    statusText,
                    zhCn
                };
            }
        });

        app.use(ElementPlus, {
            locale: zhCn
        });
        app.mount('#app');
    </script>
</body>

</html>
