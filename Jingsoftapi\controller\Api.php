<?php

namespace plugin\Jingsoftapi\controller;

use app\common\controller\BasePlugin;
use think\facade\Db;
use think\facade\View;
use think\facade\Cache;

class Api extends BasePlugin {

    protected $scene = [
        'merchant',
        'user'
    ];

    protected $noNeedLogin = [
        'get_user_info',
        'create_order',
        'update_profile'
    ];

    public function index() {
        return View::fetch();
    }

    /**
     * 获取配置数据
     */
    public function fetchData() {
        $merchantId = $this->user->id;

        // 获取API密钥配置，如果没有则自动生成
        $apiKey = merchant_plugconf($merchantId, "Jingsoftapi.api_key") ?? '';
        $apiSecret = merchant_plugconf($merchantId, "Jingsoftapi.api_secret") ?? '';

        // 如果密钥为空，自动生成新的密钥
        if (empty($apiKey) || empty($apiSecret)) {
            $apiKey = 'ak_' . substr(md5(uniqid() . time() . $merchantId), 0, 20);
            $apiSecret = 'sk_' . substr(md5(uniqid() . time() . rand() . $merchantId), 0, 32);

            // 保存新生成的密钥
            merchant_plugconf($merchantId, "Jingsoftapi.api_key", $apiKey);
            merchant_plugconf($merchantId, "Jingsoftapi.api_secret", $apiSecret);
        }

        // 获取API接口配置
        $apiEndpoints = json_decode(merchant_plugconf($merchantId, "Jingsoftapi.api_endpoints") ?? '{}', true);
        if (empty($apiEndpoints)) {
            // 如果没有配置，使用默认配置
            $apiEndpoints = [
                "get_user_info" => [
                    "enabled" => true,
                    "name" => "获取用户信息",
                    "description" => "根据用户ID获取用户详细信息，包括用户名、邮箱和注册时间"
                ]
            ];
        }

        // 获取访问限制配置
        $accessLimits = json_decode(merchant_plugconf($merchantId, "Jingsoftapi.access_limits") ?? '{}', true);
        if (empty($accessLimits)) {
            $accessLimits = [
                "daily_limit" => 1000,
                "qps_limit" => 1
            ];
        }

        $params = [
            'api_key' => $apiKey,
            'api_secret' => $apiSecret,
            'api_endpoints' => $apiEndpoints,
            'access_limits' => $accessLimits
        ];

        return $this->success('success', $params);
    }

    /**
     * 重置密钥 - 重新生成新的API密钥
     */
    public function resetApiKeys() {
        $merchantId = $this->user->id;

        // 自动生成新的API Key和Secret
        $apiKey = 'ak_' . substr(md5(uniqid() . time() . $merchantId), 0, 20);
        $apiSecret = 'sk_' . substr(md5(uniqid() . time() . rand() . $merchantId), 0, 32);

        // 保存到配置
        merchant_plugconf($merchantId, "Jingsoftapi.api_key", $apiKey);
        merchant_plugconf($merchantId, "Jingsoftapi.api_secret", $apiSecret);

        return $this->success('密钥重置成功', [
            'api_key' => $apiKey,
            'api_secret' => $apiSecret
        ]);
    }

    /**
     * 更新API接口状态
     */
    public function updateEndpoint() {
        $merchantId = $this->user->id;
        $endpoint = $this->request->post('endpoint', '');
        $enabled = $this->request->post('enabled/b', false);

        if (empty($endpoint)) {
            return $this->error('接口名称不能为空');
        }

        // 获取当前配置
        $apiEndpoints = json_decode(merchant_plugconf($merchantId, "Jingsoftapi.api_endpoints") ?? '{}', true);

        if (!isset($apiEndpoints[$endpoint])) {
            return $this->error('接口不存在');
        }

        // 更新状态
        $apiEndpoints[$endpoint]['enabled'] = $enabled;

        // 保存配置
        merchant_plugconf($merchantId, "Jingsoftapi.api_endpoints", json_encode($apiEndpoints));

        return $this->success('更新成功');
    }

    /**
     * 生成接口密钥
     */
    public function generateEndpointKey() {
        $merchantId = $this->user->id;
        $endpoint = $this->request->post('endpoint', '');

        if (empty($endpoint)) {
            return $this->error('接口名称不能为空');
        }

        // 获取接口配置
        $apiEndpoints = json_decode(merchant_plugconf($merchantId, "Jingsoftapi.api_endpoints") ?? '{}', true);

        if (!isset($apiEndpoints[$endpoint])) {
            return $this->error('接口不存在');
        }

        // 为该接口生成独立的密钥
        $endpointKey = 'ep_' . $endpoint . '_' . substr(md5(uniqid() . time() . $merchantId . $endpoint), 0, 16);
        $endpointSecret = 'es_' . $endpoint . '_' . substr(md5(uniqid() . time() . rand() . $merchantId . $endpoint), 0, 24);

        // 保存接口密钥
        $apiEndpoints[$endpoint]['api_key'] = $endpointKey;
        $apiEndpoints[$endpoint]['api_secret'] = $endpointSecret;
        $apiEndpoints[$endpoint]['key_generated_time'] = time();

        merchant_plugconf($merchantId, "Jingsoftapi.api_endpoints", json_encode($apiEndpoints));

        return $this->success('接口密钥生成成功', [
            'endpoint' => $endpoint,
            'api_key' => $endpointKey,
            'api_secret' => $endpointSecret
        ]);
    }

    /**
     * 生成API文档
     */
    public function generateDoc() {
        $merchantId = $this->user->id;
        $endpoint = $this->request->post('endpoint', '');

        if (empty($endpoint)) {
            return $this->error('接口名称不能为空');
        }

        // 获取接口配置
        $apiEndpoints = json_decode(merchant_plugconf($merchantId, "Jingsoftapi.api_endpoints") ?? '{}', true);

        if (!isset($apiEndpoints[$endpoint])) {
            return $this->error('接口不存在');
        }

        $endpointInfo = $apiEndpoints[$endpoint];

        // 使用接口独立密钥或全局密钥
        $apiKey = $endpointInfo['api_key'] ?? merchant_plugconf($merchantId, "Jingsoftapi.api_key") ?? '';
        $apiSecret = $endpointInfo['api_secret'] ?? merchant_plugconf($merchantId, "Jingsoftapi.api_secret") ?? '';

        // 生成文档内容
        $doc = [
            'endpoint' => $endpoint,
            'name' => $endpointInfo['name'],
            'description' => $endpointInfo['description'],
            'method' => 'POST',
            'url' => request()->domain() . '/plugin/Jingsoftapi/api/' . $endpoint,
            'headers' => [
                'Content-Type' => 'application/json',
                'X-API-Key' => $apiKey,
                'X-API-Secret' => $apiSecret
            ],
            'parameters' => $this->getEndpointParameters($endpoint),
            'response' => $this->getEndpointResponse($endpoint)
        ];

        return $this->success('生成成功', $doc);
    }

    /**
     * 获取接口参数定义
     */
    private function getEndpointParameters($endpoint) {
        return [];
    }

    /**
     * 获取接口响应示例
     */
    private function getEndpointResponse($endpoint) {
        return [
            'code' => 200,
            'msg' => '获取用户信息成功',
            'data' => [
                'mobile' => '13800138000',
                'username' => 'example_user',
                'create_time' => '2024-01-01 12:00:00',
                'contact_qq' => '123456789',
                'contact_mobile' => '13800138000',
                'platform_money' => 1000.50
            ],
            'time' => 1640995200
        ];
    }

    /**
     * API接口：获取用户信息
     * 根据用户ID获取用户详细信息，包括用户名、邮箱和注册时间
     */
    public function get_user_info() {
        // 验证API密钥并获取对应的用户ID
        $userId = $this->validateApiAccessAndGetMerchant('get_user_info');
        if (!$userId) {
            return json([
                'code' => 401,
                'msg' => 'API访问验证失败',
                'data' => [],
                'time' => time()
            ]);
        }

        try {
            // 查询用户表中的指定字段
            $prefix = config('database.connections.mysql.prefix');
            $userTable = $prefix . 'user';

            // 只获取需要的6个字段
            $userInfo = Db::table($userTable)
                ->where('id', $userId)
                ->field('mobile, username, create_time, contact_qq, contact_mobile, platform_money')
                ->find();

            if (!$userInfo) {
                return json([
                    'code' => 404,
                    'msg' => '用户信息不存在',
                    'data' => [],
                    'time' => time()
                ]);
            }

            // 格式化数据类型
            $responseData = [];
            foreach ($userInfo as $field => $value) {
                switch ($field) {
                    case 'platform_money':
                        $responseData[$field] = (float)$value;
                        break;
                    case 'create_time':
                        $responseData[$field] = is_numeric($value) ? date('Y-m-d H:i:s', $value) : $value;
                        break;
                    default:
                        $responseData[$field] = $value;
                }
            }

            return json([
                'code' => 200,
                'msg' => '获取用户信息成功',
                'data' => $responseData,
                'time' => time()
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '获取用户信息失败',
                'data' => [],
                'time' => time()
            ]);
        }
    }

    /**
     * 验证API访问权限并返回商家ID
     */
    private function validateApiAccessAndGetMerchant($endpoint) {
        // 获取API密钥
        $apiKey = $this->request->header('X-API-Key', '');
        $apiSecret = $this->request->header('X-API-Secret', '');

        if (empty($apiKey) || empty($apiSecret)) {
            return false;
        }

        // 通过API Key查找对应的商家
        $prefix = config('database.connections.mysql.prefix');
        $userTable = $prefix . 'user';

        try {
            $merchants = Db::table($userTable)->field('id')->select()->toArray();

            foreach ($merchants as $merchant) {
                $merchantId = $merchant['id'];

                // 获取接口配置
                $apiEndpoints = json_decode(merchant_plugconf($merchantId, "Jingsoftapi.api_endpoints") ?? '{}', true);

                // 如果接口配置为空，初始化默认配置
                if (empty($apiEndpoints)) {
                    $apiEndpoints = [
                        "get_user_info" => [
                            "enabled" => true,
                            "name" => "获取用户信息",
                            "description" => "根据用户ID获取用户详细信息，包括用户名、邮箱和注册时间"
                        ]
                    ];
                    merchant_plugconf($merchantId, "Jingsoftapi.api_endpoints", json_encode($apiEndpoints));
                }

                // 首先检查接口独立密钥
                if (isset($apiEndpoints[$endpoint]['api_key']) && isset($apiEndpoints[$endpoint]['api_secret'])) {
                    $endpointApiKey = $apiEndpoints[$endpoint]['api_key'];
                    $endpointApiSecret = $apiEndpoints[$endpoint]['api_secret'];

                    if ($apiKey === $endpointApiKey && $apiSecret === $endpointApiSecret) {
                        // 检查接口是否启用
                        if (!$apiEndpoints[$endpoint]['enabled']) {
                            return false;
                        }
                        return $merchantId;
                    }
                }

                // 然后检查全局密钥
                $globalApiKey = merchant_plugconf($merchantId, "Jingsoftapi.api_key") ?? '';
                $globalApiSecret = merchant_plugconf($merchantId, "Jingsoftapi.api_secret") ?? '';

                if ($apiKey === $globalApiKey && $apiSecret === $globalApiSecret) {
                    // 检查接口是否启用
                    if (!isset($apiEndpoints[$endpoint]) || !$apiEndpoints[$endpoint]['enabled']) {
                        return false;
                    }
                    return $merchantId;
                }
            }

            return false;

        } catch (\Exception $e) {
            return false;
        }
    }


}
