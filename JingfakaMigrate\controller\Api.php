<?php

namespace plugin\JingfakaMigrate\controller;

use app\common\controller\BasePlugin;
use app\common\model\SystemQueue as SystemQueueModel;
use app\common\service\QueueService;
use think\facade\View;
use think\facade\Config;
use think\facade\Db;

class Api extends BasePlugin {

    protected $scene = [
        'admin'
    ];
    protected $noNeedLogin = [];

    public function index() {
        return View::fetch();
    }

    public function connectDatebase() {
        $db_host = $this->request->post('hostname/s', '');
        $db_name = $this->request->post('database/s', '');
        $db_user = $this->request->post('username/s', '');
        $db_pwd = $this->request->post('password/s', '');
        $db_port = $this->request->post('hostport/d', 3306);

        Config::set([
            'connections' => [
                "dborigin" => [
                    'type' => 'mysql',
                    'hostname' => $db_host,
                    'database' => $db_name,
                    'username' => $db_user,
                    'password' => $db_pwd,
                    'hostport' => $db_port,
                    'params' => [],
                    'charset' => 'utf8',
                    'prefix' => '',
                ],
            ],
                ], 'database');

        try {
            Db::connect("dborigin")->query("select 1");
        } catch (\PDOException $e) {
            $this->error($e->getMessage());
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }


        $statistics = [
            'user' => Db::connect("dborigin")->name("user")->count(),
            'goods_category' => Db::connect("dborigin")->name("goods_category")->count(),
            'goods' => Db::connect("dborigin")->name("goods")->count(),
            'order' => Db::connect("dborigin")->name("order")->where("create_at", ">", time() - 30 * 86400)->count(),
            'auto_unfreeze' => Db::connect("dborigin")->name("auto_unfreeze")->count(),
            'cash' => Db::connect("dborigin")->name("cash")->count(),
        ];

        $this->success("连接成功", [
            'statistics' => $statistics
        ]);
    }

    public function queueStart() {
        $params = $this->request->post();

        $task = SystemQueueModel::where(['command' => 'JingfakaMigrate'])->find();
        if ($task) {
            $task->delete();
        }
        sysqueue("鲸发卡迁移", "JingfakaMigrate", 0, $params, 0, 0);

        $this->success('success');
    }

    public function queueStop() {

        $task = SystemQueueModel::where(['command' => 'JingfakaMigrate'])->find();
        if ($task) {
            $task->delete();
        }

        $this->success('success');
    }

    /**
     * 任务进度查询
     * @login true
     * @throws \think\admin\Exception
     */
    public function queueProgress() {
        $task_exist = SystemQueueModel::where(['command' => 'JingfakaMigrate'])->find();
        if ($task_exist) {
            $queue = QueueService::instance()->initialize($task_exist->code);
            $this->success('获取任务进度成功！', $queue->progress());
        } else {
            $this->error("任务不存在");
        }
    }
}
