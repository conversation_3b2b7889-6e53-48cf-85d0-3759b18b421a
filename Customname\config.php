<?php

return [
    "version" => "1.0.2",
    "min_system_version" => "2.12.0",
    "category_name" => "功能插件",
    "logo" => "data:image/png;base64,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",
    "name" => "店铺名自定义",
    "description" => "就是把商家888改为xxxxxx,开启不上就多点几次直到下次打开控制面板看到开启或关闭就行",
    "menu" => [
        [
            'tag' => 'iframe',
            'name' => '操作面板',
            'src' => (string) plugin_url("Customname/Api/index", [], false, true),

        ]
    ],
    "hook" => [
        'SimpleCommand' => 'plugin\Customname\Hook::handle',
    ],
];