<!doctype html>
<html lang="zh">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>{$title}</title>
        
        <link rel="shortcut icon" href="{$favicon}" type="image/x-icon">
        <link rel="stylesheet" href="/assets/plugin/Blackglod/plugin/Blackglod/css/all.min.css">
        <style>
                    @font-face {
            font-family: 'Noto Sans SC';
            src: url('/assets/plugin/Blackglod/plugin/Blackglod/fonts/k3kCo84MPvpLmixcA63oeAL7Iqp5IZJF9bmaG-3FnYw.ttf') format('truetype');
            font-weight: 400;
            font-style: normal;
        }
        @font-face {
            font-family: 'Noto Sans SC';
            src: url('/assets/plugin/Blackglod/plugin/Blackglod/fonts/k3kCo84MPvpLmixcA63oeAL7Iqp5IZJF9bmaG9_FnYw.ttf') format('truetype');
            font-weight: 500;
            font-style: normal;
        }
        @font-face {
            font-family: 'Noto Sans SC';
            src: url('/assets/plugin/Blackglod/plugin/Blackglod/fonts/k3kCo84MPvpLmixcA63oeAL7Iqp5IZJF9bmaGzjCnYw.ttf') format('truetype');
            font-weight: 700;
            font-style: normal;
        }

        :root {
            --primary: #C8A675;
            --primary-dark: #a88a5c;
            --primary-light: #e0c4a3;
            --dark: #000000;
            --light: #ffffff;
            --gray: #666666;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: var(--dark);
            color: var(--light);
            line-height: 1.6;
            overflow-x: hidden;
        }

        .container {
            width: 90%;
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
        }

        /* 增强的背景效果 */
        .background-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            opacity: 0.1;
            background: 
                radial-gradient(circle at 20% 20%, var(--primary) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, var(--primary-dark) 0%, transparent 50%);
            filter: blur(100px);
            animation: backgroundMove 20s ease-in-out infinite;
            will-change: transform;
            transform: translateZ(0);
            contain: paint;
        }

        @keyframes backgroundMove {
            0%, 100% { transform: translate(0, 0); }
            25% { transform: translate(5%, 5%); }
            50% { transform: translate(-5%, 5%); }
            75% { transform: translate(-5%, -5%); }
        }

        /* 增强的头部导航 */
        .header {
            padding: 1rem 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            transition: var(--transition);
            will-change: transform, opacity;
            transform: translateZ(0);
        }

        .header.scrolled {
            padding: 0.5rem 0;
            background: rgba(0, 0, 0, 0.95);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
        }

        .header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 2rem;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            color: var(--light);
            text-decoration: none;
            min-width: 200px;
        }

        .logo-img {
            height: 36px;
            width: auto;
        }

        .logo span {
            font-size: 1.4rem;
            font-weight: 500;
            background: linear-gradient(135deg, var(--light), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .nav-links {
            display: flex;
            gap: 1rem;
            margin-left: auto;
            flex-wrap: nowrap;
            align-items: center;
        }

        .nav-item {
            position: relative;
            white-space: nowrap;
        }

        .nav-item > a {
            color: var(--light);
            text-decoration: none;
            font-size: 0.85rem;
            padding: 0.4rem 0.6rem;
            border-radius: 4px;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 2px;
        }

        .nav-item > a:hover {
            color: var(--primary);
            background: rgba(200, 166, 117, 0.1);
        }

        .dropdown-arrow {
            transition: var(--transition);
            margin-left: 2px;
        }

        .nav-item:hover .dropdown-arrow {
            transform: translateY(2px);
        }

        .submenu {
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translate3d(-50%, 10px, 0);
            background: rgba(0, 0, 0, 0.95);
            backdrop-filter: blur(10px);
            min-width: 160px;
            border-radius: 8px;
            padding: 0.5rem;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(200, 166, 117, 0.1);
            will-change: transform, opacity;
        }

        .nav-item:hover .submenu {
            opacity: 1;
            visibility: visible;
            transform: translate3d(-50%, 0, 0);
        }

        .submenu a {
            color: var(--light);
            text-decoration: none;
            padding: 0.6rem 1rem;
            display: block;
            font-size: 0.9rem;
            border-radius: 4px;
            transition: var(--transition);
        }

        .submenu a:hover {
            background: rgba(200, 166, 117, 0.1);
            color: var(--primary);
        }

        .auth-buttons {
            display: flex;
            gap: 1rem;
            margin-left: 2rem;
        }

        .auth-buttons .btn {
            padding: 0.5rem 1.2rem;
            border-radius: 4px;
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .auth-icon {
            transition: var(--transition);
        }

        .btn-login {
            color: var(--primary);
            border: 1px solid var(--primary);
            background: transparent;
        }

        .btn-login:hover {
            background: rgba(200, 166, 117, 0.1);
        }

        .btn-login:hover .auth-icon {
            transform: scale(1.1);
        }

        .btn-register {
            color: var(--dark);
            background: var(--primary);
            border: 1px solid var(--primary);
        }

        .btn-register .auth-icon {
            color: var(--dark);
        }

        .btn-register:hover {
            background: var(--primary-dark);
            border-color: var(--primary-dark);
        }

        .btn-register:hover .auth-icon {
            transform: scale(1.1);
        }

        @media (max-width: 768px) {
            .nav-links, .auth-buttons {
                display: none;
            }
        }

        /* 增强的英雄区域 */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
            padding: 6rem 0;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                linear-gradient(45deg, rgba(0,0,0,0.7), transparent),
                radial-gradient(circle at center, transparent, rgba(0,0,0,0.8));
            z-index: 1;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            text-align: center;
            animation: fadeInUp 1s ease-out;
        }

        .hero h1 {
            font-size: 4rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--light), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: gradientFlow 8s linear infinite;
        }

        @keyframes gradientFlow {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .hero h2 {
            font-size: 2.5rem;
            margin-bottom: 2rem;
            background: linear-gradient(135deg, var(--light) 10%, var(--primary) 50%, var(--light) 90%);
            background-size: 200% auto;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: shine 3s linear infinite;
            text-shadow: 0 0 10px rgba(200, 166, 117, 0.3);
            position: relative;
        }

        @keyframes shine {
            0% {
                background-position: 200% center;
            }
            100% {
                background-position: -200% center;
            }
        }

        .hero h2::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, 
                transparent,
                rgba(255, 255, 255, 0.4),
                transparent
            );
            animation: lightPass 2s ease-in-out infinite;
            transform: skewX(-20deg);
        }

        @keyframes lightPass {
            0% {
                transform: translateX(-100%) skewX(-20deg);
            }
            100% {
                transform: translateX(200%) skewX(-20deg);
            }
        }

        /* 增强的统计数字 */
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 4rem;
        }

        .stat-item {
            background: rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(200, 166, 117, 0.2);
            border-radius: 20px;
            padding: 2.5rem 2rem;
            text-align: center;
            position: relative;
            overflow: hidden;
            transition: all 0.4s ease;
            backdrop-filter: blur(10px);
        }

        .stat-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                90deg,
                transparent,
                rgba(200, 166, 117, 0.2),
                transparent
            );
            transition: 0.5s;
        }

        .stat-item:hover::before {
            left: 100%;
        }

        .stat-item:hover {
            transform: translateY(-10px);
            border-color: var(--primary);
            box-shadow: 0 10px 30px rgba(200, 166, 117, 0.1);
        }

        .stat-number {
            font-size: 2.8rem;
            font-weight: 700;
            background: linear-gradient(135deg, #ffd700, #c8a675);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
            font-family: 'Arial', sans-serif;
            position: relative;
            display: inline-block;
        }

        .stat-number::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 2px;
            background: var(--primary);
            border-radius: 2px;
        }

        .stat-item div:last-child {
            color: #999;
            font-size: 1.1rem;
            margin-top: 0.5rem;
            letter-spacing: 1px;
        }

        /* 数字滚动动画 */
        @keyframes countUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .stat-number.visible {
            animation: countUp 1.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        }

        /* 增强的特性区域 */
        .features {
            padding: 8rem 0;
            background: linear-gradient(to bottom, transparent, rgba(0,0,0,0.8));
            position: relative;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .feature-item {
            padding: 2.5rem;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(200, 166, 117, 0.1);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .feature-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, transparent, rgba(200, 166, 117, 0.1));
            opacity: 0;
            transition: var(--transition);
        }

        .feature-item:hover {
            transform: translateY(-10px);
            border-color: var(--primary);
        }

        .feature-item:hover::before {
            opacity: 1;
        }

        .feature-icon {
            font-size: 3rem;
            color: var(--primary);
            margin-bottom: 1.5rem;
            transition: var(--transition);
        }

        .feature-item:hover .feature-icon {
            transform: scale(1.2) rotate(10deg);
        }

        /* 增强的步骤区域 */
        .steps {
            padding: 8rem 0;
            background: linear-gradient(to top, transparent, rgba(0,0,0,0.8));
            position: relative;
            overflow: hidden;
        }

        /* 添加六边形网格背景 */
        .steps::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(30deg, #000000 12%, transparent 12.5%, transparent 87%, #000000 87.5%, #000000),
                linear-gradient(150deg, #000000 12%, transparent 12.5%, transparent 87%, #000000 87.5%, #000000),
                linear-gradient(30deg, #000000 12%, transparent 12.5%, transparent 87%, #000000 87.5%, #000000),
                linear-gradient(150deg, #000000 12%, transparent 12.5%, transparent 87%, #000000 87.5%, #000000),
                linear-gradient(60deg, rgba(200,166,117,0.1) 25%, transparent 25.5%, transparent 75%, rgba(200,166,117,0.1) 75%, rgba(200,166,117,0.1)),
                linear-gradient(60deg, rgba(200,166,117,0.1) 25%, transparent 25.5%, transparent 75%, rgba(200,166,117,0.1) 75%, rgba(200,166,117,0.1));
            background-size: 40px 70px;
            background-position: 0 0, 0 0, 20px 35px, 20px 35px, 0 0, 20px 35px;
            opacity: 0.1;
            z-index: 0;
        }

        /* 添加发光动画效果 */
        .steps::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 50% 50%, 
                rgba(200,166,117,0.1),
                transparent 60%);
            animation: pulseGlow 4s ease-in-out infinite;
            z-index: 1;
        }

        @keyframes pulseGlow {
            0%, 100% {
                opacity: 0.3;
                transform: scale(1);
            }
            50% {
                opacity: 0.5;
                transform: scale(1.2);
            }
        }

        /* 确保内容在背景之上 */
        .steps .container {
            position: relative;
            z-index: 2;
        }

        .steps .section-title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--primary-light), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .steps .section-subtitle {
            text-align: center;
            color: var(--gray);
            margin-bottom: 4rem;
            font-size: 1.1rem;
        }

        .steps-container {
            display: flex;
            justify-content: center;
            gap: 4rem;
            position: relative;
        }

        .steps-container::before {
            content: '';
            position: absolute;
            top: 60px;
            left: calc(16.666% + 60px);
            right: calc(16.666% + 60px);
            height: 2px;
            background: linear-gradient(90deg,
                transparent,
                var(--primary) 20%,
                var(--primary) 80%,
                transparent
            );
            opacity: 0.3;
        }

        .step {
            flex: 1;
            max-width: 280px;
            text-align: center;
            position: relative;
        }

        .step-circle {
            width: 120px;
            height: 120px;
            margin: 0 auto 2rem;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .step-number {
            position: absolute;
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary);
            z-index: 2;
            transition: var(--transition);
            opacity: 1;
        }

        .step-icon {
            position: absolute;
            font-size: 2.5rem;
            color: var(--primary);
            z-index: 2;
            opacity: 0;
            transform: scale(0.5);
            transition: var(--transition);
        }

        .progress-ring {
            position: absolute;
            transform: rotate(-90deg);
        }

        .progress-ring-circle {
            fill: transparent;
            stroke: var(--primary);
            stroke-width: 2;
            stroke-dasharray: 339.292;
            stroke-dashoffset: 339.292;
            transition: var(--transition);
        }

        .step:hover .progress-ring-circle {
            stroke-dashoffset: 0;
        }

        .step:hover .step-number {
            opacity: 0;
            transform: scale(0.5);
        }

        .step:hover .step-icon {
            opacity: 1;
            transform: scale(1);
        }

        .step h3 {
            color: var(--primary);
            font-size: 1.3rem;
            margin-bottom: 1rem;
            transition: var(--transition);
        }

        .step p {
            color: var(--gray);
            font-size: 1rem;
            line-height: 1.6;
            transition: var(--transition);
        }

        .step:hover h3 {
            transform: translateY(-5px);
            color: var(--primary-light);
        }

        .step:hover p {
            color: var(--light);
        }

        @media (max-width: 768px) {
            .steps {
                padding: 4rem 0;
            }

            .steps-container {
                flex-direction: column;
                align-items: center;
                gap: 3rem;
            }

            .steps-container::before {
                display: none;
            }

            .step {
                max-width: 100%;
            }

            .steps .section-title {
                font-size: 2rem;
            }

            .steps .section-subtitle {
                font-size: 1rem;
                margin-bottom: 3rem;
            }
        }

        /* 修改页脚部分 */
        .footer {
            background: rgba(0, 0, 0, 0.3);
            padding: 3rem 0 1.5rem;
            margin-top: 4rem;
            border-top: 1px solid rgba(200, 166, 117, 0.1);
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr); /* 修改为两列 */
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-column {
            padding: 0 1rem;
        }

        .footer-column h3 {
            color: var(--primary);
            font-size: 1.1rem;
            font-weight: 500;
            margin-bottom: 1.2rem;
            padding-bottom: 0.8rem;
            border-bottom: 1px solid rgba(200, 166, 117, 0.1);
        }

        .footer-column a {
            display: block;
            color: var(--gray);
            text-decoration: none;
            padding: 0.5rem 0;
            transition: var(--transition);
        }

        .footer-column a:hover {
            color: var(--primary);
            transform: translateX(5px);
        }

        .copyright {
            text-align: center;
            padding-top: 1.5rem;
            border-top: 1px solid rgba(200, 166, 117, 0.1);
            color: var(--gray);
            font-size: 0.9rem;
        }

        .copyright a {
            color: var(--gray);
            text-decoration: none;
            margin-left: 1rem;
        }

        .copyright a:hover {
            color: var(--primary);
        }

        /* 移动端响应式调整 */
        @media (max-width: 768px) {
            .footer {
                padding: 2rem 0 1rem;
                margin-top: 2rem;
            }

            .footer-grid {
                grid-template-columns: repeat(2, 1fr); /* 保持两列 */
                gap: 1rem;
                padding: 0 0.5rem;
            }

            .footer-column {
                padding: 0;
            }

            .footer-column h3 {
                font-size: 1rem;
                margin-bottom: 0.8rem;
                padding-bottom: 0.5rem;
            }

            .footer-column a {
                font-size: 0.9rem;
                padding: 0.4rem 0;
            }

            .copyright {
                padding-top: 1rem;
                font-size: 0.8rem;
            }
        }

        /* 文章列表样式增强 */
        .article-section {
            min-height: auto;
            padding: 80px 0 40px;
            position: relative;
        }

        .article-container {
            position: relative;
            z-index: 2;
        }

        .page-title h1 {
            background: linear-gradient(135deg, var(--primary-light), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: titleGlow 3s ease-in-out infinite;
        }

        @keyframes titleGlow {
            0%, 100% {
                filter: drop-shadow(0 0 8px rgba(200, 166, 117, 0.3));
            }
            50% {
                filter: drop-shadow(0 0 15px rgba(200, 166, 117, 0.5));
            }
        }

        .notice-box {
            backdrop-filter: blur(10px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(200, 166, 117, 0.1);
        }

        .notice-item {
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .notice-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                90deg,
                transparent,
                rgba(200, 166, 117, 0.1),
                transparent
            );
            transition: 0.5s;
        }

        .notice-item:hover {
            transform: translateY(-5px);
            border-color: var(--primary);
            box-shadow: 0 5px 15px rgba(200, 166, 117, 0.1);
        }

        .notice-item:hover::before {
            left: 100%;
        }

        .notice-item h3 a {
            position: relative;
            display: inline-block;
        }

        .notice-item h3 a::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 1px;
            background: var(--primary);
            transition: var(--transition);
        }

        .notice-item:hover h3 a {
            color: var(--primary-light);
        }

        .notice-item:hover h3 a::after {
            width: 100%;
        }

        .notice-item p {
            position: relative;
            padding-left: 1rem;
            border-left: 2px solid rgba(200, 166, 117, 0.2);
            transition: var(--transition);
        }

        .notice-item:hover p {
            border-left-color: var(--primary);
            color: var(--light);
        }

        .date {
            display: inline-block;
            padding: 0.3rem 0.8rem;
            background: rgba(200, 166, 117, 0.1);
            border-radius: 20px;
            transition: var(--transition);
        }

        .notice-item:hover .date {
            background: rgba(200, 166, 117, 0.2);
            transform: scale(1.05);
        }

        /* 修改文章列表部分的响应式样式 */
        @media (max-width: 768px) {
            .article-section {
                padding: 60px 0 20px;
            }
            
            .page-title {
                margin-bottom: 1.5rem;
            }
            
            .page-title h1 {
                font-size: 1.8rem;
                margin-bottom: 0.5rem;
            }
            
            .page-title p {
                font-size: 0.9rem;
            }
            
            .notice-box {
                padding: 1rem;
            }
            
            .notice-item {
                padding: 1rem;
                margin-bottom: 0.8rem;
            }
            
            .notice-item h3 a {
                font-size: 1rem;
            }
            
            .notice-item p {
                margin: 0.5rem 0;
                font-size: 0.9rem;
            }
            
            .date {
                font-size: 0.8rem;
                padding: 0.2rem 0.6rem;
            }
        }

        /* 行星装饰 */
        .planet {
            position: fixed;
            width: 300px;
            height: 300px;
            border-radius: 50%;
            pointer-events: none;
            z-index: -1;
        }

        .planet-left {
            left: -150px;
            top: 30%;
            animation: planetPulseLeft 8s ease-in-out infinite;
        }

        .planet-right {
            right: -150px;
            top: 60%;
            animation: planetPulseRight 8s ease-in-out infinite;
        }

        .planet-texture {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: repeating-linear-gradient(
                45deg,
                rgba(200, 166, 117, 0.05) 0px,
                transparent 5px,
                transparent 10px
            );
            animation: textureRotate 20s linear infinite;
        }

        /* 行星光晕动画 */
        @keyframes planetPulseLeft {
            0%, 100% {
                transform: scale(1) translateX(0);
                opacity: 0.8;
            }
            50% {
                transform: scale(1.1) translateX(20px);
                opacity: 1;
            }
        }

        @keyframes planetPulseRight {
            0%, 100% {
                transform: scale(1) translateX(0);
                opacity: 0.8;
            }
            50% {
                transform: scale(1.1) translateX(-20px);
                opacity: 1;
            }
        }

        @keyframes textureRotate {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }

        /* 响应式调整 */
        @media (max-width: 1400px) {
            .planet {
                width: 200px;
                height: 200px;
            }
            .planet-left {
                left: -100px;
            }
            .planet-right {
                right: -100px;
            }
            .planet-ring {
                width: 300px;
                height: 60px;
                top: 70px;
                left: -50px;
            }
        }

        @media (max-width: 768px) {
            .planet {
                display: none;
            }
        }

        /* 修改移动端菜单样式 */
        .mobile-menu {
            position: fixed;
            top: 0;
            left: -100%;  /* 改为从左侧滑出 */
            width: 80%;
            height: 100vh;
            background: rgba(0, 0, 0, 0.95);
            backdrop-filter: blur(10px);
            z-index: 1000;
            transition: var(--transition);
            padding: 2rem 1rem;
            overflow-y: auto;
        }

        .mobile-menu.active {
            left: 0;  /* 改为从左侧滑出 */
        }

        /* 修改移动导航链接样式 */
        .mobile-nav-links {
            margin-top: 1rem;
        }

        .mobile-nav-item {
            margin-bottom: 0.5rem;
        }

        .mobile-nav-link {
            color: var(--light);
            text-decoration: none;
            font-size: 1rem;
            display: block;
            padding: 0.8rem 1rem;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 4px;
            transition: var(--transition);
        }

        .mobile-nav-link:hover {
            background: rgba(200, 166, 117, 0.1);
            color: var(--primary);
        }

        /* 修改移动端认证按钮样式 */
        .mobile-auth-buttons {
            margin-top: 1rem;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .mobile-auth-buttons .btn {
            width: 100%;
            padding: 0.8rem;
            text-align: center;
            font-size: 1rem;
        }

        .mobile-auth-buttons .btn-login {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid var(--primary);
        }

        .mobile-auth-buttons .btn-register {
            background: var(--primary);
            color: var(--dark);
        }

        /* 添加粒子相关样式 */
        .particles-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            background: var(--primary);
            border-radius: 50%;
            opacity: var(--particle-opacity);
            animation: particleFloat 4s ease-in-out infinite;
        }

        .particle-line {
            position: absolute;
            height: 1px;
            background: linear-gradient(90deg, var(--primary), transparent);
            opacity: 0.2;
            transform-origin: left center;
            animation: lineFloat 2s ease-out forwards;
        }

        @keyframes particleFloat {
            0% {
                transform: translate(0, 0) rotate(0) scale(1);
                opacity: var(--particle-opacity);
            }
            100% {
                transform: 
                    translate(
                        var(--particle-translateX),
                        var(--particle-translateY)
                    )
                    rotate(var(--particle-rotate))
                    scale(var(--particle-scale));
                opacity: 0;
            }
        }

        @keyframes lineFloat {
            0% {
                opacity: 0.2;
                transform: scaleX(0) rotate(var(--angle));
            }
            100% {
                opacity: 0;
                transform: scaleX(1) rotate(var(--angle));
            }
        }
        </style>
    </head>
    <body>
        <div class="stars-container"></div>
        <div class="background-animation"></div>
        <div class="particles-container"></div>

        <!-- 在 stars-container 后添加行星元素 -->
        <div class="planet planet-left">
            <div class="planet-texture"></div>
            <div class="planet-ring"></div>
        </div>
        <div class="planet planet-right">
            <div class="planet-texture"></div>
            <div class="planet-ring"></div>
        </div>

        <!-- 导航栏 -->
        <header class="header">
            <div class="container">
                <a href="/" class="logo">
                    {if !empty($logo)}
                    <img src="{$logo}" alt="{$siteName}" class="logo-img">
                    {else}
                    <i class="fas fa-credit-card"></i>
                    {/if}
                </a>
                
                <button class="mobile-menu-btn">
                    <i class="fas fa-bars"></i>
                </button>
                
                <nav class="nav-links">
                    {foreach $navItems as $nav}
                    <div class="nav-item">
                        <a href="{$nav.href}" class="nav-link {if !empty($nav.children)}has-arrow{/if}">
                            {$nav.name}
                            {if !empty($nav.children)}
                            <svg class="dropdown-arrow" width="8" height="6" viewBox="0 0 8 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M4 6L0 0L8 0L4 6Z" fill="var(--primary)"/>
                            </svg>
                            {/if}
                        </a>
                        {if !empty($nav.children)}
                        <div class="submenu">
                            {foreach $nav.children as $child}
                            <a href="{$child.href}">{$child.name}</a>
                            {/foreach}
                        </div>
                        {/if}
                    </div>
                    {/foreach}
                </nav>
                <div class="auth-buttons">
                    <a href="/merchant/login" class="btn btn-login">
                        <svg class="auth-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M20 21V19C20 16.7909 18.2091 15 16 15H8C5.79086 15 4 16.7909 4 19V21" stroke="var(--primary)" stroke-width="2" stroke-linecap="round"/>
                            <circle cx="12" cy="7" r="4" stroke="var(--primary)" stroke-width="2"/>
                        </svg>
                        商户登录
                    </a>
                    <a href="/merchant/register" class="btn btn-register">
                        <svg class="auth-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M16 21V19C16 16.7909 14.2091 15 12 15H8C5.79086 15 4 16.7909 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            <circle cx="10" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                            <path d="M20 8V14M17 11H23" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                        商户注册
                    </a>
                </div>
            </div>
        </header>

        <!-- 在 header 标签后添加 -->
        <div class="mobile-menu">
            <button class="mobile-menu-close">
                <i class="fas fa-times"></i>
            </button>
            
            <nav class="mobile-nav-links">
                {foreach $navItems as $nav}
                <div class="mobile-nav-item">
                    <a href="{$nav.href}" class="mobile-nav-link">
                        {$nav.name}
                        {if !empty($nav.children)}
                        <i class="fas fa-chevron-down"></i>
                        {/if}
                    </a>
                    {if !empty($nav.children)}
                    <div class="mobile-submenu">
                        {foreach $nav.children as $child}
                        <a href="{$child.href}" class="mobile-nav-link">{$child.name}</a>
                        {/foreach}
                    </div>
                    {/if}
                </div>
                {/foreach}
            </nav>
            
            <div class="mobile-auth-buttons">
                <a href="/merchant/login" class="btn btn-login">
                    <i class="fas fa-user"></i>
                    商户登录
                </a>
                <a href="/merchant/register" class="btn btn-register">
                    <i class="fas fa-user-plus"></i>
                    商户注册
                </a>
            </div>
        </div>

        <!-- 文章列表部分 -->
        <section class="article-section" style="padding-top: 80px;">
            <div class="container article-container">
                <div class="page-title" style="text-align: center; margin-bottom: 2rem;">
                    <h1 style="color: var(--primary); font-size: 2.5rem; margin-bottom: 0.8rem;">公告中心</h1>
                    <p style="color: var(--gray);">了解最新商城动态与重要通知</p>
                </div>
                
                <div class="notice-box" style="background: rgba(0, 0, 0, 0.3); border-radius: 20px; padding: 2rem;">
                    {foreach $notice as $item}
                    <div class="notice-item" data-aos="fade-up" style="background: rgba(255, 255, 255, 0.03); border: 1px solid rgba(200, 166, 117, 0.1); border-radius: 10px; padding: 1.5rem; margin-bottom: 1rem; transition: var(--transition);">
                        <h3><a href="{$item.url}" style="color: var(--primary); text-decoration: none; font-size: 1.2rem;">{$item.title}</a></h3>
                        <p style="color: var(--gray); margin: 1rem 0;">{$item.content|raw}</p>
                        <span class="date" style="color: var(--primary-light); font-size: 0.9rem;">{$item.create_time|date="Y-m-d H:i"}</span>
                    </div>
                    {/foreach}
                </div>
            </div>
        </section>

        <!-- 页脚 -->
        <footer class="footer">
            <div class="container">
                <div class="footer-grid">
                    <!-- 只保留服务中心和帮助中心两列 -->
                    {if $footer_service_show == 1}
                    <div class="footer-column">
                        <h3>服务中心</h3>
                        <a href="{$footer_service_1_link|default='#'}">{$footer_service_1|default='卡密查询'}</a>
                        <a href="{$footer_service_2_link|default='#'}">{$footer_service_2|default='投诉中心'}</a>
                        <a href="{$footer_service_3_link|default='#'}">{$footer_service_3|default='卡密工具'}</a>
                        <a href="{$footer_service_4_link|default='#'}">{$footer_service_4|default='商户入驻'}</a>
                    </div>
                    {/if}
                    
                    {if $footer_help_show == 1}
                    <div class="footer-column">
                        <h3>帮助中心</h3>
                        <a href="{$footer_help_1_link|default='#'}">{$footer_help_1|default='常见问题'}</a>
                        <a href="{$footer_help_2_link|default='#'}">{$footer_help_2|default='系统公告'}</a>
                        <a href="{$footer_help_3_link|default='#'}">{$footer_help_3|default='结算公告'}</a>
                        <a href="{$footer_help_4_link|default='#'}">{$footer_help_4|default='新闻动态'}</a>
                    </div>
                    {/if}
                    
                    {if $footer_legal_show == 1}
                    <div class="footer-column">
                        <h3>法律责任</h3>
                        <a href="{$footer_legal_1_link|default='#'}">{$footer_legal_1|default='免责声明'}</a>
                        <a href="{$footer_legal_2_link|default='#'}">{$footer_legal_2|default='禁售商品'}</a>
                        <a href="{$footer_legal_3_link|default='#'}">{$footer_legal_3|default='服务协议'}</a>
                        <a href="{$footer_legal_4_link|default='#'}">{$footer_legal_4|default='隐私政策'}</a>
                    </div>
                    {/if}
                    
                    {if $footer_links_show == 1}
                    <div class="footer-column">
                        <h3>友情链接</h3>
                        <a href="{$footer_links_1_link|default='#'}">{$footer_links_1|default='一意支付'}</a>
                        <a href="{$footer_links_2_link|default='#'}">{$footer_links_2|default='支付宝'}</a>
                        <a href="{$footer_links_3_link|default='#'}">{$footer_links_3|default='微信支付'}</a>
                        <a href="{$footer_links_4_link|default='#'}">{$footer_links_4|default='QQ钱包'}</a>
                    </div>
                    {/if}
                </div>
                <div class="copyright">
                    <span>{$siteName|default='Blackglod'} - 版权所有 © {$year|default='2022'}-至今</span>
                    {if !empty($icpNumber)}
                    <a href="https://beian.miit.gov.cn/" target="_blank">{$icpNumber}</a>
                    {/if}
                    {if !empty($gaNumber)}
                    <a href="{$icpCert}" target="_blank">{$gaNumber}</a>
                    {/if}
                </div>
            </div>
        </footer>
    </body>
</html>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否为移动设备
    const isMobile = window.innerWidth <= 768;
    
    if (!isMobile) {
        const starsContainer = document.querySelector('.stars-container');
        // 仅在非移动设备上创建星星
        createStars(150);
        // 仅在非移动设备上创建流星
        setInterval(createShootingStar, 8000);
    }
    
    // 移动端菜单
    const menuBtn = document.querySelector('.mobile-menu-btn');
    const closeBtn = document.querySelector('.mobile-menu-close');
    const mobileMenu = document.querySelector('.mobile-menu');
    const mobileNavItems = document.querySelectorAll('.mobile-nav-item');
    
    menuBtn.addEventListener('click', () => {
        mobileMenu.classList.add('active');
        document.body.style.overflow = 'hidden';
    });
    
    closeBtn.addEventListener('click', () => {
        mobileMenu.classList.remove('active');
        document.body.style.overflow = '';
    });
    
    // 子菜单切换
    mobileNavItems.forEach(item => {
        const link = item.querySelector('.mobile-nav-link');
        const submenu = item.querySelector('.mobile-submenu');
        
        if (submenu) {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                submenu.classList.toggle('active');
                link.querySelector('i').classList.toggle('fa-chevron-up');
                link.querySelector('i').classList.toggle('fa-chevron-down');
            });
        }
    });
    
    // 点击菜单外部关闭菜单
    document.addEventListener('click', (e) => {
        if (!mobileMenu.contains(e.target) && !menuBtn.contains(e.target)) {
            mobileMenu.classList.remove('active');
            document.body.style.overflow = '';
        }
    });
});

// 创建星星
function createStars() {
    const container = document.querySelector('.stars-container');
    const fragment = document.createDocumentFragment();
    const starCount = window.innerWidth > 768 ? 120 : 60;

    for (let i = 0; i < starCount; i++) {
        const star = document.createElement('div');
        star.className = 'star';
        
        const x = Math.random() * 100;
        const y = Math.random() * 100;
        const size = Math.random() * 6 + 2;
        const delay = Math.random() * 5;
        const duration = Math.random() * 3 + 2;
        const opacity = Math.random() * 0.8 + 0.4;

        star.style.cssText = `
            left: ${x}%;
            top: ${y}%;
            width: ${size}px;
            height: ${size}px;
            --delay: ${delay}s;
            --duration: ${duration}s;
            --opacity: ${opacity};
        `;
        
        fragment.appendChild(star);
    }
    
    container.appendChild(fragment);
}

// 页面加载完成后创建星星
document.addEventListener('DOMContentLoaded', createStars);

// 为星星添加视差效果
let mouseMoveTimeout;
document.addEventListener('mousemove', (e) => {
    if (!mouseMoveTimeout) {
        requestAnimationFrame(() => {
            const stars = document.querySelectorAll('.star');
            const mouseX = e.clientX / window.innerWidth - 0.5;
            const mouseY = e.clientY / window.innerHeight - 0.5;

            stars.forEach(star => {
                const depth = Math.random() * 5;
                star.style.transform = `translate3d(${mouseX * depth}px, ${mouseY * depth}px, 0)`;
            });
            mouseMoveTimeout = null;
        });
        mouseMoveTimeout = true;
    }
});

// 修改粒子创建函数
document.addEventListener('DOMContentLoaded', function() {
    const container = document.querySelector('.particles-container');
    if (!container) {
        console.warn('Particles container not found');
        return;
    }

    function createParticles() {
        const particleCount = 50;
        
        function createParticle() {
            const particle = document.createElement('div');
            particle.className = 'particle';
            
            const size = Math.random() * 4 + 2;
            const x = Math.random() * window.innerWidth;
            const y = Math.random() * window.innerHeight;
            const translateX = (Math.random() - 0.5) * 200;
            const translateY = (Math.random() - 0.5) * 200;
            const rotate = Math.random() * 360;
            const scale = Math.random() * 0.5 + 0.5;
            const opacity = Math.random() * 0.5 + 0.3;
            
            particle.style.cssText = `
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                --particle-translateX: ${translateX}px;
                --particle-translateY: ${translateY}px;
                --particle-rotate: ${rotate}deg;
                --particle-scale: ${scale};
                --particle-opacity: ${opacity};
            `;
            
            container.appendChild(particle);
            
            particle.addEventListener('animationend', () => {
                if (particle.parentNode === container) {
                    particle.remove();
                    createParticle();
                }
            });
        }
        
        for (let i = 0; i < particleCount; i++) {
            setTimeout(() => createParticle(), i * 100);
        }
    }

    // 初始化粒子
    try {
        createParticles();
    } catch (error) {
        console.warn('Failed to create particles:', error);
    }
});
</script>
