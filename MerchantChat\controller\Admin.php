<?php
namespace plugin\MerchantChat\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Filesystem;

class Admin extends BasePlugin
{
    // 指定只有管理员可以访问
    protected $scene = ['admin'];
    
    // 指定不需要登录验证的方法
    protected $noNeedLogin = ['getAdminConfig'];

    public function index()
    {
        return View::fetch();
    }

    // 获取默认配置
    public function getConfig()
    {
        try {
            $merchant_can_edit_raw = plugconf("MerchantChat.merchant_can_edit");
            $merchant_can_edit = $merchant_can_edit_raw === null ? 1 : intval($merchant_can_edit_raw);
            
            $data = [
                'global_enabled' => intval(plugconf("MerchantChat.global_enabled") ?? 0),
                'simple_enabled' => intval(plugconf("MerchantChat.simple_enabled") ?? 0),
                'defaulth5_enabled' => intval(plugconf("MerchantChat.defaulth5_enabled") ?? 0),
                'status' => intval(plugconf("MerchantChat.default_status") ?? 0),
                'business_id' => (string)(plugconf("MerchantChat.default_business_id") ?? ''),
                'token' => (string)(plugconf("MerchantChat.default_token") ?? ''),
                'script_url' => (string)(plugconf("MerchantChat.default_script_url") ?? 'https://kf.y1yun.top/assets/front/ymwl_online.js'),
                'script_version' => (string)(plugconf("MerchantChat.default_script_version") ?? ''),
                'merchant_can_edit' => $merchant_can_edit
            ];

            return json(['code' => 200, 'msg' => '获取成功', 'data' => $data]);
        } catch (\Exception $e) {
            \think\facade\Log::error('GetConfig error: ' . $e->getMessage());
            return json(['code' => 0, 'msg' => '获取数据失败：' . $e->getMessage()]);
        }
    }

    // 供前端聊天脚本获取管理员配置的API
    public function getAdminConfig()
    {
        try {
            $data = [
                'status' => intval(plugconf("MerchantChat.default_status") ?? 0),
                'business_id' => (string)(plugconf("MerchantChat.default_business_id") ?? ''),
                'token' => (string)(plugconf("MerchantChat.default_token") ?? ''),
                'script_url' => (string)(plugconf("MerchantChat.default_script_url") ?? 'https://kf.y1yun.top/assets/front/ymwl_online.js'),
                'script_version' => (string)(plugconf("MerchantChat.default_script_version") ?? '')
            ];

            // 检查配置是否有效
            if (empty($data['business_id']) || empty($data['token'])) {
                $data['status'] = 0; // 如果缺少必要配置，强制设置状态为禁用
            }

            return json(['code' => 200, 'msg' => '获取成功', 'data' => $data]);
        } catch (\Exception $e) {
            \think\facade\Log::error('GetAdminConfig error: ' . $e->getMessage());
            return json(['code' => 0, 'msg' => '获取数据失败：' . $e->getMessage()]);
        }
    }

    // 保存默认配置
    public function saveConfig()
    {
        try {
            // 获取表单数据并确保类型正确
            $global_enabled = intval($this->request->post('global_enabled') ?? 0);
            $simple_enabled = intval($this->request->post('simple_enabled') ?? 0);
            $defaulth5_enabled = intval($this->request->post('defaulth5_enabled') ?? 0);
            $status = intval($this->request->post('status') ?? 0);
            $business_id = trim((string)$this->request->post('business_id'));
            $token = trim((string)$this->request->post('token'));
            $script_url = trim((string)$this->request->post('script_url'));
            $script_version = trim((string)$this->request->post('script_version'));
            
            // 确保merchant_can_edit值为0或1
            $merchant_can_edit = $this->request->post('merchant_can_edit');
            $merchant_can_edit = $merchant_can_edit === null ? 1 : intval($merchant_can_edit);
            $merchant_can_edit = $merchant_can_edit == 1 ? 1 : 0; // 强制转为0或1

            // 验证必填项
            if ($status == 1) {
                if (empty($business_id)) {
                    return json(['code' => 0, 'msg' => '请输入默认业务ID']);
                }
                if (empty($token)) {
                    return json(['code' => 0, 'msg' => '请输入默认Token']);
                }
                if (empty($script_url)) {
                    return json(['code' => 0, 'msg' => '请输入默认脚本URL']);
                }
            }

            // 保存所有配置
            plugconf("MerchantChat.global_enabled", $global_enabled);
            plugconf("MerchantChat.simple_enabled", $simple_enabled);
            plugconf("MerchantChat.defaulth5_enabled", $defaulth5_enabled);
            plugconf("MerchantChat.default_status", $status);
            plugconf("MerchantChat.default_business_id", $business_id);
            plugconf("MerchantChat.default_token", $token);
            plugconf("MerchantChat.default_script_url", $script_url);
            plugconf("MerchantChat.default_script_version", $script_version);
            plugconf("MerchantChat.merchant_can_edit", $merchant_can_edit);
            
            // 处理全局部署功能
            $deployResults = [
                'default' => ['success' => true, 'message' => ''], 
                'simple' => ['success' => true, 'message' => ''],
                'defaulth5' => ['success' => true, 'message' => '']
            ];
            
            if ($global_enabled == 1) {
                $deployResults['default'] = $this->deployToTemplate('default');
            } else {
                $deployResults['default'] = $this->removeFromTemplate('default');
            }
            
            if ($simple_enabled == 1) {
                $deployResults['simple'] = $this->deployToTemplate('simple');
            } else {
                $deployResults['simple'] = $this->removeFromTemplate('simple');
            }
            
            if ($defaulth5_enabled == 1) {
                $deployResults['defaulth5'] = $this->deployToTemplate('defaulth5');
            } else {
                $deployResults['defaulth5'] = $this->removeFromTemplate('defaulth5');
            }
            
            // 若部署出现错误，记录日志
            $deployError = '';
            if (!$deployResults['default']['success']) {
                \think\facade\Log::error('Deploy error (default): ' . $deployResults['default']['message']);
                $deployError .= '默认模板部署失败：' . $deployResults['default']['message'] . '; ';
            }
            if (!$deployResults['simple']['success']) {
                \think\facade\Log::error('Deploy error (simple): ' . $deployResults['simple']['message']);
                $deployError .= '简约模板部署失败：' . $deployResults['simple']['message'] . '; ';
            }
            if (!$deployResults['defaulth5']['success']) {
                \think\facade\Log::error('Deploy error (defaulth5): ' . $deployResults['defaulth5']['message']);
                $deployError .= '默认手机模板部署失败：' . $deployResults['defaulth5']['message'];
            }

            // 返回完整的配置数据
            return json([
                'code' => 200, 
                'msg' => '保存成功' . ($deployError ? '，但部署出现错误：' . $deployError : ''),
                'data' => [
                    'global_enabled' => $global_enabled,
                    'simple_enabled' => $simple_enabled,
                    'defaulth5_enabled' => $defaulth5_enabled,
                    'status' => $status,
                    'business_id' => $business_id,
                    'token' => $token,
                    'script_url' => $script_url,
                    'script_version' => $script_version,
                    'merchant_can_edit' => $merchant_can_edit
                ]
            ]);
        } catch (\Exception $e) {
            \think\facade\Log::error('SaveConfig error: ' . $e->getMessage());
            return json(['code' => 0, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 部署聊天脚本到指定模板
     * @param string $template 模板名称，默认为'default'
     * @return array ['success' => bool, 'message' => string]
     */
    protected function deployToTemplate($template = 'default')
    {
        try {
            // 获取配置值
            $business_id = (string)(plugconf("MerchantChat.default_business_id") ?? '');
            $token = (string)(plugconf("MerchantChat.default_token") ?? '');
            $script_url = (string)(plugconf("MerchantChat.default_script_url") ?? 'https://kf.y1yun.top/assets/front/ymwl_online.js');
            $script_version = (string)(plugconf("MerchantChat.default_script_version") ?? '');
            
            // 如果版本号为空，使用时间戳
            $version = empty($script_version) ? time() : $script_version;
            
            // 在指定模板的 index.html 中插入脚本引用
            $indexFile = root_path() . 'public/assets/template/' . $template . '/index.html';
            
            if (!file_exists($indexFile)) {
                return ['success' => false, 'message' => '模板文件不存在: ' . $indexFile];
            }
            
            $content = file_get_contents($indexFile);
            
            // 检查是否已经包含了脚本引用(检查新旧两种可能的脚本引用)
            $oldScriptTag = '<script src="/assets/template/' . $template . '/assets/kfchat.js"></script>';
            $ymwlScriptPattern = '/<script>var\s+ymwl\s*=\s*\{.*?\};\s*<\/script>\s*<script\s+src=["\'].*?ymwl_online\.js.*?["\'].*?><\/script>/s';
            
            // 准备新的脚本标签
            $newScriptTags = <<<HTML
<script>var ymwl={visiter_id:"",visiter_name:"",avatar:"",business_id:$business_id,groupid:0,token:"$token"};</script>
<script src="$script_url?v=$version" charset="UTF-8"></script>
HTML;
            
            // 删除旧的脚本标签
            if (strpos($content, $oldScriptTag) !== false) {
                $content = str_replace($oldScriptTag, '', $content);
            }
            
            // 如果存在旧的ymwl脚本标签，替换它
            if (preg_match($ymwlScriptPattern, $content)) {
                $content = preg_replace($ymwlScriptPattern, $newScriptTags, $content);
            } else {
                // 如果不存在，在 </body> 标签前插入新的脚本标签
                $content = str_replace('</body>', "    $newScriptTags\n</body>", $content);
            }
            
            // 保存修改后的内容
            if (!file_put_contents($indexFile, $content)) {
                return ['success' => false, 'message' => '无法写入模板文件: ' . $indexFile];
            }
            
            return ['success' => true, 'message' => '部署成功'];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * 从指定模板移除聊天脚本
     * @param string $template 模板名称，默认为'default'
     * @return array ['success' => bool, 'message' => string]
     */
    protected function removeFromTemplate($template = 'default')
    {
        try {
            // 从指定模板的 index.html 中移除脚本引用
            $indexFile = root_path() . 'public/assets/template/' . $template . '/index.html';
            
            if (file_exists($indexFile)) {
                $content = file_get_contents($indexFile);
                
                // 移除旧的脚本标签
                $oldScriptTag = '<script src="/assets/template/' . $template . '/assets/kfchat.js"></script>';
                if (strpos($content, $oldScriptTag) !== false) {
                    $content = str_replace("    {$oldScriptTag}\n", '', $content);
                }
                
                // 移除ymwl脚本标签
                $ymwlScriptPattern = '/<script>var\s+ymwl\s*=\s*\{.*?\};\s*<\/script>\s*<script\s+src=["\'].*?ymwl_online\.js.*?["\'].*?><\/script>/s';
                if (preg_match($ymwlScriptPattern, $content)) {
                    $content = preg_replace($ymwlScriptPattern, '', $content);
                }
                
                // 保存修改后的内容
                if (!file_put_contents($indexFile, $content)) {
                    return ['success' => false, 'message' => '无法写入模板文件: ' . $indexFile];
                }
            }
            
            // 清理可能存在的旧版文件
            $targetFile = root_path() . 'public/assets/template/' . $template . '/assets/kfchat.js';
            if (file_exists($targetFile)) {
                @unlink($targetFile);
            }
            
            $oldTargetFile = root_path() . 'public/assets/template/' . $template . '/assets/chat.js';
            if (file_exists($oldTargetFile)) {
                @unlink($oldTargetFile);
            }
            
            return ['success' => true, 'message' => '移除成功'];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
} 