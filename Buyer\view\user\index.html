<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>买家消费排行榜</title>
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <style>
        body {
            margin: 0;
            background: #f5f7fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
            height: 100vh;
            overflow: hidden;
        }

        .main-container {
            height: 650px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
        }

        .buyers-table {
            background: #fff;
            border-radius: 8px;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        /*.table-container {*/
        /*    flex: 1;*/
        /*    overflow: hidden;*/
        /*}*/

        .el-table {
            --el-table-row-height: 55px;
            font-size: 14px;
            height: 100%;
        }

        /* 美化滚动条 */
        .el-table__body-wrapper::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        .el-table__body-wrapper::-webkit-scrollbar-thumb {
            border-radius: 3px;
            background: #dcdfe6;
        }

        .el-table__body-wrapper::-webkit-scrollbar-track {
            border-radius: 3px;
            background: #f5f7fa;
        }

        .el-table__body-wrapper {
            overflow-y: overlay;
        }

        .el-table th {
            background-color: #f5f7fa !important;
            font-weight: bold;
            color: #606266;
        }

        /*.pagination-container {*/
        /*    margin-top: 20px;*/
        /*    display: flex;*/
        /*    justify-content: center;*/
        /*    padding-bottom: 10px;*/
        /*}*/

        /* 密码验证框样式 */
        .password-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            padding: 20px;
            box-sizing: border-box;
        }

        .password-box {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
        }

        .password-title {
            text-align: center;
            margin-bottom: 20px;
            font-size: 18px;
            color: #303133;
        }

        .password-input {
            margin-bottom: 20px;
        }

        /* 错误结果页面样式 */
        .el-result {
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .filter-container {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .el-date-editor.el-input__wrapper {
            width: 100%;
        }

        .el-date-editor--daterange {
            --el-date-editor-width: 100%;
        }
        
        .filter-container {
            background: #fff;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .pagination-container {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            padding-bottom: 10px;
            text-align: center;
        }

        .el-pagination {
            justify-content: center !important;
        }

        /* Arco Design 风格的日期选择器样式 */
        .arco-date-picker .el-date-editor {
            width: 100%;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            transition: all 0.2s;
        }

        .arco-date-picker-dropdown {
            border: none !important;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1) !important;
            border-radius: 4px !important;
            padding: 8px !important;
        }

        .arco-date-picker-dropdown .el-picker-panel__content {
            margin: 0 !important;
        }

        .arco-date-picker-dropdown .el-date-table th {
            padding: 8px;
            color: #4E5969;
            font-weight: 400;
        }

        .arco-date-picker-dropdown .el-date-table td {
            padding: 4px 0;
        }

        .arco-date-picker-dropdown .el-date-table td .el-date-table-cell {
            height: 32px;
            padding: 0;
            border-radius: 4px;
        }

        .arco-date-picker-dropdown .el-date-table td.current:not(.disabled) .el-date-table-cell {
            background-color: #165DFF;
            color: #fff;
        }

        .arco-date-picker-dropdown .el-date-table td.today .el-date-table-cell::before {
            border: 1px solid #165DFF;
            border-radius: 4px;
        }

        .arco-date-picker-dropdown .el-picker-panel__icon-btn {
            color: #4E5969;
        }

        .arco-date-picker-dropdown .el-date-picker__header-label {
            font-size: 14px;
            color: #1D2129;
        }

        .arco-date-picker-dropdown .el-picker-panel__footer {
            padding: 8px;
            border-top: 1px solid #E5E6EB;
        }

        .arco-date-picker-dropdown .el-picker-panel__footer .el-button {
            min-width: 80px;
            height: 32px;
            padding: 0 16px;
            border-radius: 4px;
        }

        .arco-date-picker-dropdown .el-picker-panel__footer .el-button--default {
            border-color: #E5E6EB;
            color: #4E5969;
        }

        .arco-date-picker-dropdown .el-picker-panel__footer .el-button--primary {
            background-color: #165DFF;
            border-color: #165DFF;
        }

        /* 日期范围选择器的分隔符样式 */
        .el-date-editor .el-range-separator {
            color: #86909C;
        }

        /* 输入框样式 */
        .el-date-editor.el-input__wrapper {
            padding: 0 12px;
        }

        /* 日期图标样式 */
        .el-date-editor .el-input__icon {
            color: #86909C;
        }

        /* Arco Design 日期选择器样式 */
        .arco-trigger-popup {
            position: absolute;
            background: #fff;
            border-radius: 4px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            z-index: 1001;
        }

        .arco-picker-range-container {
            padding: 8px;
        }

        .arco-picker-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 8px;
            height: 40px;
        }

        .arco-picker-header-icon {
            cursor: pointer;
            padding: 0 4px;
            color: #4E5969;
        }

        .arco-picker-header-title {
            font-size: 14px;
            color: #1D2129;
        }

        .arco-picker-week-list {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            padding: 8px 0;
        }

        .arco-picker-week-list-item {
            text-align: center;
            color: #4E5969;
            font-size: 14px;
        }

        .arco-picker-body {
            padding: 0 8px;
        }

        .arco-picker-row {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
        }

        .arco-picker-cell {
            padding: 4px 0;
            text-align: center;
        }

        .arco-picker-date {
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            cursor: pointer;
        }

        .arco-picker-cell-in-view .arco-picker-date {
            color: #1D2129;
        }

        .arco-picker-cell-today .arco-picker-date {
            border: 1px solid #165DFF;
        }

        .arco-picker-cell-selected .arco-picker-date {
            background-color: #165DFF;
            color: #fff;
        }

        .arco-picker-date-value {
            font-size: 14px;
        }

        .arco-picker-cell:not(.arco-picker-cell-in-view) .arco-picker-date {
            color: #C9CDD4;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 错误提示 -->
        <el-result
            v-if="error"
            :icon="error.icon || 'Error'"
            :title="error.message"
            :sub-title="error.subTitle">
        </el-result>

        <!-- 密码验证界面 -->
        <div v-else-if="needPassword" class="password-container">
            <div class="password-box">
                <div class="password-title">请输入访问密码</div>
                <el-input 
                    v-model="password"
                    type="password"
                    placeholder="请输入访问密码"
                    class="password-input"
                    @keyup.enter="verifyPassword">
                </el-input>
                <el-button 
                    type="primary" 
                    :loading="verifying"
                    @click="verifyPassword"
                    style="width: 100%">
                    确认
                </el-button>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div v-else-if="isVerified" class="main-container">
            <div class="buyers-table">
                <!-- 搜索和筛选区域 -->
                <div class="filter-container">
                    <el-row :gutter="20" style="margin-bottom: 20px;">
                        <el-col :span="8">
                            <el-input
                                v-model="searchKey"
                                placeholder="搜索买家联系方式"
                                clearable
                                @keyup.enter="handleSearch"
                                @clear="handleSearch">
                                <template #append>
                                    <el-button @click="handleSearch">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" style="fill: currentColor;">
                                            <path d="M10.5 3a7.5 7.5 0 015.645 12.438l4.709 4.708a.502.502 0 01-.708.708l-4.708-4.709A7.5 7.5 0 1110.5 3zm0 1a6.5 6.5 0 100 13 6.5 6.5 0 000-13z"/>
                                        </svg>
                                    </el-button>
                                </template>
                            </el-input>
                        </el-col>
                        <el-col :span="12">
                            <el-date-picker
                                v-model="dateRange"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                format="YYYY-MM-DD"
                                value-format="YYYY-MM-DD"
                                :shortcuts="dateShortcuts"
                                @change="handleDateChange"
                                style="width: 100%">
                            </el-date-picker>
                        </el-col>
                        <el-col :span="4">
                            <el-button type="primary" @click="handleReset" style="margin-left: 5px;">重置</el-button>
                        </el-col>
                    </el-row>
                </div>

                <div class="table-container">
                    <el-table 
                        :data="buyers" 
                        style="width: 100%" 
                        v-loading="loading"
                        @sort-change="handleSortChange"
                        :default-sort="{ prop: 'total_amount', order: 'descending' }">
                        <el-table-column 
                            prop="contact" 
                            label="买家联系方式" 
                            min-width="120"
                            align="center"
                            header-align="center">
                        </el-table-column>
                        <el-table-column 
                            prop="total_orders" 
                            label="成交订单" 
                            sortable="custom" 
                            min-width="120"
                            align="center"
                            header-align="center">
                        </el-table-column>
                        <el-table-column 
                            prop="total_cards" 
                            label="卡密张数" 
                            sortable="custom" 
                            min-width="120"
                            align="center"
                            header-align="center">
                        </el-table-column>
                        <el-table-column 
                            prop="total_amount" 
                            label="成交总额" 
                            sortable="custom" 
                            min-width="120"
                            align="center"
                            header-align="center">
                            <template #default="scope">
                                ¥{{ formatAmount(scope.row.total_amount) }}
                            </template>
                        </el-table-column>
                        <el-table-column 
                            prop="self_amount" 
                            label="自营总额" 
                            sortable="custom" 
                            min-width="120"
                            align="center"
                            header-align="center">
                            <template #default="scope">
                                ¥{{ formatAmount(scope.row.self_amount) }}
                            </template>
                        </el-table-column>
                        <el-table-column 
                            prop="agent_amount" 
                            label="代理总额" 
                            sortable="custom" 
                            min-width="120"
                            align="center"
                            header-align="center">
                            <template #default="scope">
                                ¥{{ formatAmount(scope.row.agent_amount) }}
                            </template>
                        </el-table-column>
                        <el-table-column 
                            label="商家信息" 
                            min-width="150"
                            align="center"
                            header-align="center">
                            <template #default="scope">
                                <div>{{ scope.row.nickname || '未知' }}</div>
                            </template>
                        </el-table-column>
                        <el-table-column 
                            label="操作" 
                            min-width="100"
                            align="center"
                            header-align="center">
                            <template #default="scope">
                                <el-button type="primary" size="small" @click="showContactStores(scope.row.contact, scope.row.raw_contact)">查看店铺</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>

                <div class="pagination-container">
                    <el-pagination
                        v-model:current-page="currentPage"
                        v-model:page-size="pageSize"
                        :page-sizes="[10]"
                        layout="total, prev, pager, next"
                        :total="total"
                        @current-change="handleCurrentChange"
                        style="display: flex; justify-content: center; width: 100%;">
                    </el-pagination>
                </div>
            </div>
        </div>

        <!-- 加载状态 -->
        <div v-else class="loading-container">
            <el-empty description="正在加载..." v-loading="true"></el-empty>
        </div>

        <!-- 添加店铺记录弹窗 -->
        <el-dialog
            v-model="storeDialogVisible"
            title="买家购买店铺记录"
            width="800px"
            :close-on-click-modal="false"
            :destroy-on-close="true">
            <div v-loading="storeLoading">
                <el-empty v-if="storeRecords.length === 0" description="暂无数据"></el-empty>
                <div v-else>
                    <div style="margin-bottom: 15px;">
                        <span style="font-size: 16px; font-weight: bold;">买家联系方式：{{ currentContact }}</span>
                        <el-alert
                            style="margin-top: 10px;"
                            type="info"
                            :closable="false"
                            show-icon>
                            <template #title>
                                该联系方式在全平台共在 {{ storeRecords.length }} 家店铺购买过商品，标签说明：
                                <el-tag size="small" type="success" style="margin-left: 5px;">自营店铺</el-tag>
                                <el-tag size="small" type="warning" style="margin-left: 5px;">对接店铺</el-tag>
                                <el-tag size="small" type="danger" style="margin-left: 5px;">上游店铺</el-tag>
                                <el-tag size="small" type="primary" style="margin-left: 5px;">与您有关</el-tag>
                            </template>
                        </el-alert>
                    </div>
                    
                    <el-collapse accordion>
                        <el-collapse-item v-for="(store, index) in storeRecords" :key="index" :name="index">
                            <template #title>
                                <div style="display: flex; align-items: center; justify-content: space-between; width: 100%;">
                                    <div>
                                        <span style="margin-right: 15px;">{{ store.store_name }}</span>
                                        <el-tag size="small" type="success" v-if="!store.is_upstream && !store.has_parent">自营店铺</el-tag>
                                        <el-tag size="small" type="warning" v-if="!store.is_upstream && store.has_parent">对接店铺 (上游: {{ store.parent_name }})</el-tag>
                                        <el-tag size="small" type="danger" v-if="store.is_upstream">上游店铺</el-tag>
                                        <el-tag size="small" type="primary" v-if="store.is_related_to_user">与您有关</el-tag>
                                    </div>
                                    <div>
                                        <span style="margin-right: 15px;">订单数: {{ store.order_count }}</span>
                                        <span v-if="!store.is_upstream">总金额: ¥{{ formatAmount(store.total_amount) }}</span>
                                        <span v-else>
                                            销售金额: ¥{{ formatAmount(store.total_amount) }} / 
                                            成本金额: ¥{{ formatAmount(store.parent_amount) }}
                                        </span>
                                    </div>
                                </div>
                            </template>
                            
                            <div v-if="store.is_upstream" class="upstream-info" style="margin-bottom: 15px; padding: 8px; background-color: #f8f8f8; border-radius: 4px;">
                                <div style="font-weight: bold; margin-bottom: 5px;">作为上游店铺，共有{{ store.downstream_count }}个下游店铺使用了您的商品</div>
                            </div>
                            
                            <div v-if="!store.is_related_to_user" class="not-related-info" style="margin-bottom: 15px; padding: 8px; background-color: #fff8e6; border-radius: 4px; border-left: 3px solid #e6a23c;">
                                <div style="color: #e6a23c; font-weight: bold;">此店铺与您没有直接关联，但该买家在此店铺购买过商品</div>
                            </div>
                            
                            <el-table :data="store.orders" style="width: 100%">
                                <el-table-column prop="goods_name" label="商品名称" min-width="180">
                                    <template #default="scope">
                                        <div>
                                            {{ scope.row.goods_name }}
                                            <el-tag size="small" type="primary" v-if="scope.row.is_related_to_user">与您有关</el-tag>
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column label="数量/单价" min-width="120">
                                    <template #default="scope">
                                        {{ scope.row.quantity }}件 x 
                                        <span v-if="scope.row.price">¥{{ formatAmount(scope.row.price) }}</span>
                                        <span v-else>--</span>
                                    </template>
                                </el-table-column>
                                <el-table-column label="店铺信息" min-width="150">
                                    <template #default="scope">
                                        <div v-if="store.is_upstream">
                                            {{ scope.row.downstream_shop }} 
                                            <el-tag size="small" type="info">下游店铺</el-tag>
                                        </div>
                                        <div v-else>
                                            {{ scope.row.shop_name }}
                                            <div v-if="scope.row.has_parent">
                                                上游: {{ scope.row.parent_shop_name }}
                                            </div>
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column label="金额信息" min-width="150">
                                    <template #default="scope">
                                        <div v-if="scope.row.is_upstream_order">
                                            <div>销售额: ¥{{ formatAmount(scope.row.total_amount) }}</div>
                                            <div>成本额: ¥{{ formatAmount(scope.row.parent_amount) }}</div>
                                        </div>
                                        <div v-else>
                                            ¥{{ formatAmount(scope.row.total_amount) }}
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="create_time" label="下单时间" min-width="180"></el-table-column>
                            </el-table>
                        </el-collapse-item>
                    </el-collapse>
                </div>
            </div>
        </el-dialog>
    </div>

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>

    <script>
        const { createApp, ref, onMounted } = Vue;

        createApp({
            setup() {
                const buyers = ref([]);
                const currentPage = ref(1);
                const pageSize = ref(10);
                const total = ref(0);
                const loading = ref(false);
                const error = ref(null);
                const isVerified = ref(false);
                const needPassword = ref(false);
                const password = ref('');
                const verifying = ref(false);
                const sortConfig = ref({
                    sort: 'total_amount',
                    order: 'desc'
                });
                const searchKey = ref('');
                const dateRange = ref([]);
                
                // 店铺查看相关
                const storeDialogVisible = ref(false);
                const storeLoading = ref(false);
                const storeRecords = ref([]);
                const currentContact = ref('');
                
                // 日期快捷选项
                const dateShortcuts = [
                    {
                        text: '今天',
                        value: () => {
                            const date = new Date();
                            return [date, date];
                        },
                    },
                    {
                        text: '最近7天',
                        value: () => {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
                            return [start, end];
                        },
                    },
                    {
                        text: '最近30天',
                        value: () => {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
                            return [start, end];
                        },
                    },
                    {
                        text: '本月',
                        value: () => {
                            const now = new Date();
                            const start = new Date(now.getFullYear(), now.getMonth(), 1);
                            const end = new Date();
                            return [start, end];
                        },
                    }
                ];

                // 检查访问状态
                const checkAccess = async () => {
                    try {
                        const res = await axios.get('/plugin/Buyer/user/checkVerified');
                        
                        if (res.data.code === 200) {
                            isVerified.value = true;
                            needPassword.value = false;
                            loadData();
                        } else if (res.data.code === 401) {
                            isVerified.value = false;
                            needPassword.value = true;
                        } else if (res.data.code === 403) {
                            error.value = {
                                code: 403,
                                message: res.data.msg
                            };
                        }
                    } catch (err) {
                        console.error('检查访问状态失败:', err);
                        error.value = {
                            message: '检查访问状态失败'
                        };
                    }
                };

                // 验证密码
                const verifyPassword = async () => {
                    if (!password.value) {
                        ElMessage.warning('请输入密码');
                        return;
                    }

                    verifying.value = true;
                    try {
                        const res = await axios.post('/plugin/Buyer/user/verifyPassword', {
                            password: password.value
                        });
                        
                        if (res.data.code === 200) {
                            isVerified.value = true;
                            needPassword.value = false;
                            loadData();
                        } else {
                            ElMessage.error(res.data.msg);
                            password.value = '';
                        }
                    } catch (err) {
                        ElMessage.error('验证失败');
                        password.value = '';
                    } finally {
                        verifying.value = false;
                    }
                };

                // 加载数据
                const loadData = async () => {
                    if (!isVerified.value) {
                        return;
                    }

                    try {
                        loading.value = true;
                        const res = await axios.get('/plugin/Buyer/user/getBuyerList', {
                            params: {
                                page: currentPage.value,
                                limit: pageSize.value,
                                sort: sortConfig.value.sort,
                                order: sortConfig.value.order,
                                search: searchKey.value,
                                dateRange: dateRange.value || [] // 如果未选择日期，传空数组
                            }
                        });

                        if (res.data.code === 200) {
                            buyers.value = res.data.data;
                            total.value = res.data.total;
                            error.value = null;
                        } else if (res.data.code === 401) {
                            isVerified.value = false;
                            needPassword.value = true;
                        } else if (res.data.code === 403) {
                            error.value = {
                                code: 403,
                                message: res.data.msg
                            };
                        }
                    } catch (err) {
                        console.error('加载数据失败:', err);
                        error.value = {
                            icon: 'Error',
                            title: '加载失败',
                            message: '数据加载失败，请稍后重试'
                        };
                    } finally {
                        loading.value = false;
                    }
                };

                const handleSortChange = ({ prop, order }) => {
                    sortConfig.value.sort = prop;
                    sortConfig.value.order = order;
                    loadData();
                };

                const handleSizeChange = (val) => {
                    pageSize.value = val;
                    loadData();
                };

                const handleCurrentChange = (val) => {
                    currentPage.value = val;
                    loadData();
                };

                // 搜索处理
                const handleSearch = () => {
                    currentPage.value = 1;
                    loadData();
                };

                // 日期变化处理
                const handleDateChange = () => {
                    currentPage.value = 1;
                    loadData();
                };

                // 重置筛选
                const handleReset = () => {
                    searchKey.value = '';
                    dateRange.value = [];
                    currentPage.value = 1;
                    loadData();
                };

                const formatAmount = (amount) => {
                    const num = parseFloat(amount || 0);
                    return isNaN(num) ? '0.00' : num.toFixed(2);
                };

                const weeks = ['日', '一', '二', '三', '四', '五', '六'];
                const currentYear = ref(new Date().getFullYear());
                const currentMonth = ref(new Date().getMonth() + 1);

                const getDatesForRow = (row) => {
                    // 获取当前年月的第一天
                    const firstDay = new Date(currentYear.value, currentMonth.value - 1, 1);
                    // 获取第一天是星期几
                    const firstDayWeek = firstDay.getDay();
                    // 计算当前月份的天数
                    const daysInMonth = new Date(currentYear.value, currentMonth.value, 0).getDate();
                    // 计算上个月的天数
                    const daysInPrevMonth = new Date(currentYear.value, currentMonth.value - 1, 0).getDate();
                    
                    // 计算当前行的起始日期索引
                    const startIdx = (row - 1) * 7;
                    
                    const rowDates = [];
                    for (let i = 0; i < 7; i++) {
                        const idx = startIdx + i;
                        const dayOffset = idx - firstDayWeek;
                        const isCurrentMonth = dayOffset >= 0 && dayOffset < daysInMonth;
                        
                        let day, date, inCurrentMonth, isToday;
                        
                        if (dayOffset < 0) {
                            // 上个月的日期
                            day = daysInPrevMonth + dayOffset + 1;
                            date = new Date(currentYear.value, currentMonth.value - 2, day);
                            inCurrentMonth = false;
                            isToday = false;
                        } else if (dayOffset >= daysInMonth) {
                            // 下个月的日期
                            day = dayOffset - daysInMonth + 1;
                            date = new Date(currentYear.value, currentMonth.value, day);
                            inCurrentMonth = false;
                            isToday = false;
                        } else {
                            // 当前月的日期
                            day = dayOffset + 1;
                            date = new Date(currentYear.value, currentMonth.value - 1, day);
                            inCurrentMonth = true;
                            
                            // 检查是否是今天
                            const today = new Date();
                            isToday = date.getFullYear() === today.getFullYear() && 
                                    date.getMonth() === today.getMonth() && 
                                    date.getDate() === today.getDate();
                        }
                        
                        rowDates.push({
                            day,
                            date,
                            inCurrentMonth,
                            isToday
                        });
                    }
                    
                    return rowDates;
                };

                const isSelected = (date) => {
                    if (!dateRange.value || dateRange.value.length !== 2) return false;
                    
                    const startDate = new Date(dateRange.value[0]);
                    startDate.setHours(0, 0, 0, 0);
                    
                    const endDate = new Date(dateRange.value[1]);
                    endDate.setHours(23, 59, 59, 999);
                    
                    return date >= startDate && date <= endDate;
                };

                // 查看联系方式购买店铺记录
                const showContactStores = async (contact, rawContact) => {
                    console.log('点击查看店铺按钮，联系方式:', contact, '原始联系方式:', rawContact);
                    try {
                        currentContact.value = contact;
                        storeDialogVisible.value = true;
                        storeLoading.value = true;
                        storeRecords.value = [];
                        
                        console.log('发送API请求...');
                        const res = await axios.get('/plugin/Buyer/user/getContactStores', {
                            params: { 
                                contact: contact,
                                raw_contact: rawContact
                            }
                        });
                        
                        console.log('API响应:', res.data);
                        if (res.data.code === 200) {
                            storeRecords.value = res.data.data;
                            if (res.data.display_contact) {
                                currentContact.value = res.data.display_contact;
                            }
                        } else if (res.data.code === 401) {
                            isVerified.value = false;
                            needPassword.value = true;
                            storeDialogVisible.value = false;
                            ElMessage.error('会话已过期，请重新验证');
                        } else {
                            ElMessage.error(res.data.msg || '获取数据失败');
                        }
                    } catch (error) {
                        console.error('详细错误信息:', error);
                        ElMessage.error('获取数据失败');
                    } finally {
                        storeLoading.value = false;
                    }
                };

                onMounted(() => {
                    checkAccess();
                });

                return {
                    buyers,
                    currentPage,
                    pageSize,
                    total,
                    loading,
                    error,
                    isVerified,
                    needPassword,
                    password,
                    verifying,
                    sortConfig,
                    handleSortChange,
                    handleSizeChange,
                    handleCurrentChange,
                    verifyPassword,
                    searchKey,
                    dateRange,
                    dateShortcuts,
                    handleSearch,
                    handleDateChange,
                    handleReset,
                    formatAmount,
                    weeks,
                    currentYear,
                    currentMonth,
                    getDatesForRow,
                    isSelected,
                    storeDialogVisible,
                    storeLoading,
                    storeRecords,
                    currentContact,
                    showContactStores
                };
            }
        }).use(ElementPlus, {
            locale: ElementPlusLocaleZhCn
        }).mount('#app');
    </script>
</body>
</html> 