<?php

namespace plugin\Zeroallsum;

use think\facade\Db;
use think\facade\Log;

class Hook
{
    public function handle()
    {
        // 检查是否开启自动清除
        if (intval(plugconf("Zeroallsum.auto_clear") ?? 0) !== 1) {
            return;
        }

        $clearType = plugconf("Zeroallsum.clear_type") ?? 'daily';
        $now = new \DateTime();
        
        // 根据不同的清除类型检查是否需要执行清除
        $shouldClear = false;
        
        switch ($clearType) {
            case 'daily':
                $clearTime = plugconf("Zeroallsum.clear_time") ?? '00:00';
                $shouldClear = $now->format('H:i') === $clearTime;
                break;
                
            case 'weekly':
                $clearDay = intval(plugconf("Zeroallsum.clear_day") ?? 1);
                $clearTime = plugconf("Zeroallsum.clear_time") ?? '00:00';
                $shouldClear = $now->format('N') == $clearDay && $now->format('H:i') === $clearTime;
                break;
                
            case 'monthly':
                $clearDay = intval(plugconf("Zeroallsum.clear_day") ?? 1);
                $clearTime = plugconf("Zeroallsum.clear_time") ?? '00:00';
                $shouldClear = $now->format('d') == $clearDay && $now->format('H:i') === $clearTime;
                break;
                
            case 'quarterly':
                $clearMonth = intval(plugconf("Zeroallsum.clear_month") ?? 3);
                $clearDay = intval(plugconf("Zeroallsum.clear_day") ?? 1);
                $clearTime = plugconf("Zeroallsum.clear_time") ?? '00:00';
                $shouldClear = in_array($now->format('n'), [3, 6, 9, 12]) && 
                              $now->format('n') == $clearMonth && 
                              $now->format('d') == $clearDay && 
                              $now->format('H:i') === $clearTime;
                break;
                
            case 'yearly':
                $clearMonth = intval(plugconf("Zeroallsum.clear_month") ?? 1);
                $clearDay = intval(plugconf("Zeroallsum.clear_day") ?? 1);
                $clearTime = plugconf("Zeroallsum.clear_time") ?? '00:00';
                $shouldClear = $now->format('n') == $clearMonth && 
                              $now->format('d') == $clearDay && 
                              $now->format('H:i') === $clearTime;
                break;
        }

        if ($shouldClear) {
            $this->clearAllData();
            // 记录清除时间
            plugconf("Zeroallsum.last_clear_time", $now->format('Y-m-d H:i:s'));
        }
    }

    protected function clearAllData()
    {
        try {
            // 默认的动态表名关键字
            $tableKeyword = 'user_analysis';

            // 获取所有表名
            $tables = Db::query("SHOW TABLES");
            
            // 检查查询是否成功
            if (!$tables || empty($tables)) {
                throw new \Exception("没有返回表信息，检查数据库连接是否正常");
            }

            $targetTable = null;

            // 遍历表名，寻找符合条件的表
            foreach ($tables as $table) {
                $tableName = array_values($table)[0]; // 获取表名
                if (strpos($tableName, $tableKeyword) !== false) {
                    $targetTable = $tableName;
                    break;
                }
            }

            // 如果未找到符合条件的表
            if (!$targetTable) {
                throw new \Exception("未找到包含关键字 '{$tableKeyword}' 的表");
            }

            // 直接清空整个表
            Db::execute("TRUNCATE TABLE {$targetTable}");
            Log::info("Zeroallsum自动清除：{$targetTable} 表已清空");

        } catch (\Exception $e) {
            Log::error("Zeroallsum自动清除失败：" . $e->getMessage());
        }
    }
}
