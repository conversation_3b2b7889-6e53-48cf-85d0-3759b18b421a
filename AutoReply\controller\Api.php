<?php
namespace plugin\AutoReply\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;

class Api extends BasePlugin
{
    protected $scene = ['admin'];
    protected $noNeedLogin = [];

    public function index()
    {
        return View::fetch();
    }

    // 获取配置
    public function getConfig()
    {
        try {
            // 从数据库或配置中获取实际的商家邮件开关状态
            $allowMerchantEmail = intval(plugconf("AutoReply.reply_config.allow_merchant_email") ?? 0); // 默认值改为0
            
            $config = [
                'status' => intval(plugconf("AutoReply.reply_config.status") ?? 0),
                'check_interval' => intval(plugconf("AutoReply.reply_config.check_interval") ?? 60),
                'email_notify' => intval(plugconf("AutoReply.reply_config.email_notify") ?? 0),
                'notify_email' => strval(plugconf("AutoReply.reply_config.notify_email") ?? ''),
                'allow_merchant_email' => $allowMerchantEmail, // 使用实际存储的值
                'email_check_interval' => intval(plugconf("AutoReply.reply_config.email_check_interval") ?? 60), // 新增邮件检查间隔
                'keyword_rules' => []
            ];
            
            // 处理规则数据
            $rules = plugconf("AutoReply.keyword_rules") ?: [];
            foreach ($rules as $rule) {
                $config['keyword_rules'][] = [
                    'status' => isset($rule['status']) ? intval($rule['status']) : 1,
                    'keywords' => is_array($rule['keywords']) ? $rule['keywords'] : [],
                    'reply' => strval($rule['reply'] ?? '')
                ];
            }
            
            return json(['code' => 200, 'msg' => '获取成功', 'data' => $config]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '获取配置失败：' . $e->getMessage()]);
        }
    }

    // 保存配置
    public function saveConfig()
    {
        try {
            $status = intval($this->request->post('status/d', 0));
            $checkInterval = intval($this->request->post('check_interval/d', 60));
            $emailNotify = intval($this->request->post('email_notify/d', 0));
            $notifyEmail = trim($this->request->post('notify_email/s', ''));
            $allowMerchantEmail = intval($this->request->post('allow_merchant_email/d', 1));
            $emailCheckInterval = intval($this->request->post('email_check_interval/d', 60)); // 新增
            $rules = $this->request->post('keyword_rules/a', []);

            // 验证规则
            if (!empty($rules)) {
                foreach ($rules as $rule) {
                    if (empty($rule['reply'])) {
                        return json(['code' => 400, 'msg' => '请为每个规则填写回复内容']);
                    }
                }
            }

            // 验证邮箱
            if ($emailNotify && !filter_var($notifyEmail, FILTER_VALIDATE_EMAIL)) {
                return json(['code' => 400, 'msg' => '请输入有效的邮箱地址']);
            }

            // 验证邮件检查间隔
            if ($emailCheckInterval < 10 || $emailCheckInterval > 3600) {
                return json(['code' => 400, 'msg' => '邮件检查间隔必须在10-3600秒之间']);
            }

            // 处理规则数据
            $saveRules = [];
            foreach ($rules as $rule) {
                $saveRules[] = [
                    'status' => isset($rule['status']) ? intval($rule['status']) : 1,
                    'keywords' => array_values(array_filter($rule['keywords'] ?? [])),
                    'reply' => strval($rule['reply'])
                ];
            }

            // 保存配置
            try {
                // 先保存商家邮件开关，因为这个比较重要
                if (!plugconf("AutoReply.reply_config.allow_merchant_email", $allowMerchantEmail)) {
                    throw new \Exception('保存商家邮件开关失败');
                }

                // 如果关闭了商家邮件功能，清除所有商家的邮件配置
                if ($allowMerchantEmail === 0) {
                    // 获取所有商家ID，移除status条件
                    $merchants = Db::name('user')->column('id');
                    foreach ($merchants as $merchantId) {
                        plugconf("AutoReply.merchant_{$merchantId}.email_notify", 0);
                        plugconf("AutoReply.merchant_{$merchantId}.notify_email", '');
                    }
                }

                // 保存其他基本配置
                if (!plugconf("AutoReply.reply_config.status", $status)) {
                    throw new \Exception('保存功能状态失败');
                }
                if (!plugconf("AutoReply.reply_config.check_interval", $checkInterval)) {
                    throw new \Exception('保存检查间隔失败');
                }
                if (!plugconf("AutoReply.reply_config.email_notify", $emailNotify)) {
                    throw new \Exception('保存邮件通知状态失败');
                }
                if (!plugconf("AutoReply.reply_config.notify_email", $notifyEmail)) {
                    throw new \Exception('保存通知邮箱失败');
                }
                if (!plugconf("AutoReply.reply_config.email_check_interval", $emailCheckInterval)) {
                    throw new \Exception('保存邮件检查间隔失败');
                }
                if (!plugconf("AutoReply.keyword_rules", $saveRules)) {
                    throw new \Exception('保存关键词规则失败');
                }

                // 记录日志
                error_log("AutoReply: 保存配置成功 - 功能状态: {$status}, 商家邮件开关: {$allowMerchantEmail}");
                
                return json([
                    'code' => 200, 
                    'msg' => '保存成功',
                    'data' => [
                        'allow_merchant_email' => $allowMerchantEmail // 返回保存后的状态
                    ]
                ]);
            } catch (\Exception $e) {
                error_log("AutoReply: 保存配置失败 - " . $e->getMessage());
                return json(['code' => 500, 'msg' => '保存失败：' . $e->getMessage()]);
            }

        } catch (\Exception $e) {
            error_log("AutoReply: 保存配置异常 - " . $e->getMessage());
            return json(['code' => 500, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    // 测试规则
    public function testRule()
    {
        try {
            $content = $this->request->post('content/s', '');
            $complaintId = $this->request->post('complaint_id/d', 0);
            $rules = plugconf("AutoReply.keyword_rules") ?: [];
            $autoSend = $this->request->post('auto_send/b', false);

            foreach ($rules as $rule) {
                if (empty($rule['status'])) {
                    continue;
                }

                // 如果没有设置关键词，直接匹配
                if (empty($rule['keywords'])) {
                    // 处理自动回复逻辑
                    return $this->handleAutoReply($rule, $content, $complaintId, $autoSend);
                }

                // 有关键词时才进行关键词匹配
                foreach ($rule['keywords'] as $keyword) {
                    if (strpos($content, $keyword) !== false) {
                        return $this->handleAutoReply($rule, $content, $complaintId, $autoSend, $keyword);
                    }
                }
            }

            return json(['code' => 200, 'msg' => '未匹配到任何规则']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '测试失败：' . $e->getMessage()]);
        }
    }

    // 新增处理自动回复的方法
    private function handleAutoReply($rule, $content, $complaintId, $autoSend, $keyword = '')
    {
        if ($autoSend && $complaintId) {
            try {
                $currentTime = time();
                
                // 插入消息
                Db::name('complaint_message')->insert([
                    'complaint_id' => $complaintId,
                    'content_type' => 0,
                    'content' => $rule['reply'],
                    'identity' => 'platform',
                    'create_time' => $currentTime
                ]);

                // 添加邮件通知功能
                if (intval(plugconf("AutoReply.reply_config.email_notify") ?? 0) === 1) {
                    $notifyEmail = plugconf("AutoReply.reply_config.notify_email");
                    if ($notifyEmail) {
                        try {
                            // 检查邮件发送频率
                            $lastEmailTime = cache('auto_reply_last_email_time') ?: 0;
                            $currentTime = time();
                            
                            // 限制每60秒只能发送一次邮件
                            if ($currentTime - $lastEmailTime < 60) {
                                error_log("AutoReply Test: 邮件发送太频繁，请稍后再试");
                                return json([
                                    'code' => 200,
                                    'msg' => '匹配成功并已自动回复（邮件通知已跳过：发送太频繁）',
                                    'data' => [
                                        'keyword' => $keyword,
                                        'reply' => $rule['reply'],
                                        'sent' => true
                                    ]
                                ]);
                            }
                            
                            $service = new \app\common\service\EmailService();
                            $emailContent = "系统自动回复了一条投诉消息：\n\n";
                            $emailContent .= "回复时间：" . date('Y-m-d H:i:s', $currentTime) . "\n";
                            $emailContent .= "投诉ID：{$complaintId}\n";
                            $emailContent .= "买家消息：{$content}\n";
                            $emailContent .= "触发关键词：{$keyword}\n";
                            $emailContent .= "自动回复：{$rule['reply']}\n";
                            
                            $res = $service->subject('投诉自动回复通知')
                                         ->message($emailContent)
                                         ->to($notifyEmail)
                                         ->send();
                            
                            if ($res) {
                                // 更新最后发送时间
                                cache('auto_reply_last_email_time', $currentTime);
                                error_log("AutoReply Test: 邮件发送成功");
                            } else {
                                error_log("AutoReply Test: 邮件发送失败: " . $service->getError());
                            }
                        } catch (\Exception $e) {
                            error_log("AutoReply Test: 邮件发送异常: " . $e->getMessage());
                        }
                    }
                }
                
                return json([
                    'code' => 200,
                    'msg' => '匹配成功并已自动回复',
                    'data' => [
                        'keyword' => $keyword ?: '未设置关键词',
                        'reply' => $rule['reply'],
                        'sent' => true
                    ]
                ]);
            } catch (\Exception $e) {
                return json([
                    'code' => 500,
                    'msg' => '自动回复失败：' . $e->getMessage()
                ]);
            }
        }

        return json([
            'code' => 200,
            'msg' => '匹配成功',
            'data' => [
                'keyword' => $keyword ?: '未设置关键词',
                'reply' => $rule['reply'],
                'sent' => false
            ]
        ]);
    }
}
