<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流水挑战</title>
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>
    <style>
        [v-cloak] { display: none; }
        body { margin: 0; padding: 0; background: #f5f7fa; }
        .container { padding: 20px; }
        .challenge-card {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
        }
        .challenge-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .challenge-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
        }
        .info-item {
            text-align: center;
        }
        .info-label {
            color: #909399;
            font-size: 14px;
        }
        .info-value {
            color: #303133;
            font-size: 16px;
            margin-top: 5px;
        }
        .highlight {
            color: #409EFF;
            font-weight: bold;
        }
        .remaining-days {
            color: #ff9900;
            font-weight: bold;
        }
        /* 排行榜样式 */
        .ranking-list {
            margin-top: 20px;
        }
        .ranking-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .ranking-title {
            font-size: 18px;
            font-weight: bold;
        }
        .ranking-tabs {
            margin-bottom: 0;
        }
        .medal {
            display: inline-block;
            width: 24px;
            height: 24px;
            line-height: 24px;
            text-align: center;
            border-radius: 50%;
            color: #fff;
            font-weight: bold;
            margin-right: 8px;
        }
        .medal-1 {
            background: linear-gradient(135deg, #FFD700, #FFA500);
        }
        .medal-2 {
            background: linear-gradient(135deg, #C0C0C0, #A9A9A9);
        }
        .medal-3 {
            background: linear-gradient(135deg, #CD7F32, #8B4513);
        }
        .medal-other {
            background: #409EFF;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div id="app" v-cloak>
        <div class="container">
            <!-- 当前进行中的挑战 -->
            <div v-if="currentChallenge" class="challenge-card">
                <div class="challenge-title">{{ currentChallenge.rule_name }}</div>
                <div class="challenge-info">
                    <div class="info-item">
                        <div class="info-label">目标流水</div>
                        <div class="info-value">{{ formatNumber(currentChallenge.target_amount) }}元</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">挑战流水</div>
                        <div class="info-value highlight">{{ formatNumber(currentChallenge.current_amount) }}元</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">奖励金额</div>
                        <div class="info-value">{{ formatNumber(currentChallenge.reward_amount) }}元</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">剩余时间</div>
                        <div class="info-value">{{ getRemainingTime(currentChallenge.end_time) }}</div>
                    </div>
                </div>
                <el-progress 
                    :percentage="challengeProgress" 
                    :status="challengeStatus"
                    :format="progressFormat"
                    :stroke-width="20"
                />
            </div>

            <!-- 可参与的挑战列表 -->
            <el-card v-if="!currentChallenge">
                <template #header>
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span class="challenge-title">可参与的挑战</span>
                        <el-button
                            type="primary"
                            @click="randomAcceptChallenge"
                            :loading="randomLoading"
                            :disabled="availableChallenges.length === 0"
                        >
                            🎲 随机接取挑战
                        </el-button>
                    </div>
                </template>
                <el-table :data="availableChallenges" border style="width: 100%">
                    <el-table-column label="挑战名称">
                        <template #default="scope">
                            {{ scope.row.name }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="turnover_amount" label="目标流水">
                        <template #default="scope">
                            {{ formatNumber(scope.row.turnover_amount) }}元
                        </template>
                    </el-table-column>
                    <el-table-column prop="reward_amount" label="奖励金额">
                        <template #default="scope">
                            {{ formatNumber(scope.row.reward_amount) }}元
                        </template>
                    </el-table-column>
                    <el-table-column label="有效期">
                        <template #default="scope">
                            <div>挑战时长: {{ scope.row.challenge_duration }}天</div>
                            <div>
                                <span>剩余时间: </span>
                                <span class="remaining-days">{{ scope.row.remaining_days }}天</span>
                            </div>
                            <div v-if="scope.row.max_attempts > 0">
                                <span>参与次数: </span>
                                <span class="remaining-days">{{ scope.row.participated_count }}/{{ scope.row.max_attempts }}次</span>
                            </div>
                            <div v-else>
                                <span>参与次数: 不限制</span>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="120">
                        <template #default="scope">
                            <el-button 
                                type="primary" 
                                size="small" 
                                @click="acceptChallenge(scope.row.id)"
                                :disabled="!scope.row.can_participate"
                            >
                                {{ isResetChallenge(scope.row) ? '重新挑战' : '参与挑战' }}
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>

            <!-- 历史挑战记录 -->
            <el-card style="margin-top: 20px;">
                <template #header>
                    <div class="challenge-title">历史挑战</div>
                </template>
                <el-table :data="historyRecords" border style="width: 100%">
                    <el-table-column label="挑战名称">
                        <template #default="scope">
                            {{ scope.row.rule_name }}
                        </template>
                    </el-table-column>
                    <el-table-column label="挑战结果">
                        <template #default="scope">
                            <div>目标: {{ formatNumber(scope.row.target_amount) }}元</div>
                            <div>完成: {{ formatNumber(scope.row.current_amount) }}元</div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="reward_amount" label="奖励金额">
                        <template #default="scope">
                            {{ formatNumber(scope.row.reward_amount) }}元
                        </template>
                    </el-table-column>
                    <el-table-column prop="status" label="状态" width="100">
                        <template #default="scope">
                            <el-tag :type="getStatusType(scope.row.status)">
                                {{ getStatusText(scope.row.status) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="reward_sent" label="奖励状态" width="100">
                        <template #default="scope">
                            <el-tag :type="scope.row.reward_sent ? 'success' : 'info'">
                                {{ scope.row.reward_sent ? '已发放' : '未发放' }}
                            </el-tag>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>

            <!-- 流水挑战排行榜 -->
            <el-card style="margin-top: 20px;" class="ranking-list">
                <template #header>
                    <div class="ranking-header">
                        <span class="ranking-title">挑战排行榜</span>
                        <el-button type="primary" size="small" @click="loadRankingList">刷新排行</el-button>
                    </div>
                </template>
                <el-table :data="rankingList" border style="width: 100%" v-loading="rankingLoading">
                    <el-table-column label="排名" width="80" align="center">
                        <template #default="scope">
                            <div v-if="scope.$index === 0">
                                <span class="medal medal-1">1</span>
                            </div>
                            <div v-else-if="scope.$index === 1">
                                <span class="medal medal-2">2</span>
                            </div>
                            <div v-else-if="scope.$index === 2">
                                <span class="medal medal-3">3</span>
                            </div>
                            <div v-else>
                                <span class="medal medal-other">{{ scope.$index + 1 }}</span>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="店铺" prop="shopname" align="center">
                    </el-table-column>
                    <el-table-column label="挑战名称">
                        <template #default="scope">
                            {{ scope.row.rule_name }}
                        </template>
                    </el-table-column>
                    <el-table-column label="流水金额" align="center">
                        <template #default="scope">
                            <span class="highlight">{{ formatNumber(scope.row.current_amount) }}元</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="完成天数" align="center" width="100">
                        <template #default="scope">
                            {{ scope.row.days_used }}天
                        </template>
                    </el-table-column>
                    <el-table-column label="完成时间" align="center">
                        <template #default="scope">
                            {{ formatDate(scope.row.complete_time) }}
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </div>
    </div>

    <script>
    const { createApp, ref, computed, onMounted, onUnmounted } = Vue;
    const { ElMessage } = ElementPlus;

    createApp({
        setup() {
            const currentChallenge = ref(null);
            const availableChallenges = ref([]);
            const historyRecords = ref([]);
            const loading = ref(false);
            
            // 排行榜数据
            const rankingList = ref([]);
            const rankingLoading = ref(false);

            // 随机接取挑战加载状态
            const randomLoading = ref(false);

            // 加载数据
            const loadData = async () => {
                try {
                    loading.value = true;
                    const res = await axios.get('/plugin/Waterchallenge/user/getChallenges');
                    loading.value = false;
                    
                    if (res.data.code === 200) {
                        const data = res.data.data;
                        if (data.current_challenges && data.current_challenges.length > 0) {
                            currentChallenge.value = data.current_challenges[0];
                        } else {
                            currentChallenge.value = null;
                        }
                        availableChallenges.value = data.rules || [];
                        historyRecords.value = data.history || [];
                    }
                } catch (error) {
                    loading.value = false;
                    console.error('加载数据失败:', error);
                    ElMessage.error('加载数据失败');
                }
            };
            
            // 加载排行榜数据
            const loadRankingList = async () => {
                try {
                    rankingLoading.value = true;
                    const res = await axios.get('/plugin/Waterchallenge/user/getRankingList');
                    rankingLoading.value = false;
                    
                    if (res.data.code === 200) {
                        rankingList.value = res.data.data;
                    } else {
                        ElMessage.error(res.data.msg || '加载排行榜失败');
                    }
                } catch (error) {
                    rankingLoading.value = false;
                    console.error('加载排行榜失败:', error);
                    ElMessage.error('加载排行榜失败');
                }
            };

            // 计算挑战进度
            const challengeProgress = computed(() => {
                if (!currentChallenge.value) return 0;
                const progress = (currentChallenge.value.current_amount / currentChallenge.value.target_amount) * 100;
                return Math.min(Math.round(progress), 100);
            });

            // 计算挑战状态
            const challengeStatus = computed(() => {
                if (!currentChallenge.value) return '';
                return challengeProgress.value >= 100 ? 'success' : '';
            });

            // 进度条文字格式化
            const progressFormat = (percentage) => {
                return `完成进度 ${percentage}%`;
            };

            // 参与挑战
            const acceptChallenge = async (ruleId) => {
                try {
                    const res = await axios.post('/plugin/Waterchallenge/user/acceptChallenge', {
                        rule_id: ruleId
                    });
                    if (res.data.code === 200) {
                        ElMessage.success('参与成功');
                        loadData();
                    } else {
                        ElMessage.error(res.data.msg);
                    }
                } catch (error) {
                    ElMessage.error('参与失败');
                }
            };

            // 随机接取挑战
            const randomAcceptChallenge = async () => {
                try {
                    randomLoading.value = true;
                    const res = await axios.post('/plugin/Waterchallenge/user/randomAcceptChallenge');
                    randomLoading.value = false;

                    if (res.data.code === 200) {
                        ElMessage.success(res.data.msg);
                        loadData(); // 重新加载数据
                    } else {
                        ElMessage.error(res.data.msg);
                    }
                } catch (error) {
                    randomLoading.value = false;
                    console.error('随机接取挑战失败:', error);
                    ElMessage.error('随机接取挑战失败');
                }
            };

            // 获取状态样式
            const getStatusType = (status) => {
                const types = {
                    ongoing: 'primary',
                    completed: 'success',
                    failed: 'danger'
                };
                return types[status] || 'info';
            };

            // 获取状态文本
            const getStatusText = (status) => {
                const texts = {
                    ongoing: '进行中',
                    completed: '已完成',
                    failed: '已失败'
                };
                return texts[status] || status;
            };

            // 判断是否是重置挑战
            const isResetChallenge = (rule) => {
                return historyRecords.value.some(record => 
                    record.rule_id === rule.rule_id && 
                    record.status === 'failed'
                );
            };

            // 计算剩余时间
            const getRemainingTime = (endTime) => {
                const end = new Date(endTime).getTime();
                const now = new Date().getTime();
                const diff = end - now;

                if (diff <= 0) return '已结束';

                const days = Math.floor(diff / (1000 * 60 * 60 * 24));
                const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

                return `${days}天${hours}小时${minutes}分`;
            };

            // 格式化数字
            const formatNumber = (value) => {
                if (typeof value === 'number') {
                    return value.toFixed(2);
                } else {
                    return '0.00';
                }
            };
            
            // 格式化日期
            const formatDate = (dateString) => {
                const date = new Date(dateString);
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                return `${year}-${month}-${day}`;
            };

            // 定时刷新数据
            onMounted(() => {
                loadData();
                loadRankingList(); // 加载排行榜数据
                // 每分钟刷新一次数据
                const timer = setInterval(loadData, 60000);
                // 组件卸载时清除定时器
                onUnmounted(() => {
                    clearInterval(timer);
                });
            });

            return {
                currentChallenge,
                availableChallenges,
                historyRecords,
                loading,
                challengeProgress,
                challengeStatus,
                progressFormat,
                acceptChallenge,
                randomAcceptChallenge,
                randomLoading,
                getStatusType,
                getStatusText,
                isResetChallenge,
                getRemainingTime,
                formatNumber,
                rankingList,
                rankingLoading,
                loadRankingList,
                formatDate,
            };
        }
    }).use(ElementPlus).mount('#app');
    </script>
</body>
</html> 