<?php

namespace plugin\Producticon\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;

class Api extends BasePlugin {
    
    protected $noNeedLogin = ['fetchConfig'];

    // 添加场景配置
    protected $scene = [
        'user'
    ];

    public function index() {
        return View::fetch();
    }

    public function fetchConfig() {
        try {
            $merchant_id = $this->request->param('merchant_id', 0, 'intval');
            $shop_name = $this->request->param('shop_name', '', 'trim');
            
            // 如果是商家登录状态,使用商家ID
            if (!$merchant_id && $this->user) {
                $merchant_id = $this->user->id;
            }
            
            // 如果有店铺名称,尝试多种方式获取商家ID
            if (!$merchant_id && $shop_name) {
                $merchant_id = $this->getMerchantIdByMultipleWays($shop_name);
            }
            
            if (!$merchant_id) {
                return json(['code' => 0, 'msg' => '未找到商家信息']);
            }

            // 获取配置
            $icons = merchant_plugconf($merchant_id, "Producticon.icons");
            $status = merchant_plugconf($merchant_id, "Producticon.status");
            
            // 初始化默认配置
            if ($icons === null) {
                $icons = json_encode([
                    [
                        'icon_url' => '',
                        'keywords' => '',
                        'position' => 'right-top',
                        'size' => 30
                    ]
                ], JSON_UNESCAPED_UNICODE);
                merchant_plugconf($merchant_id, "Producticon.icons", $icons);
            }
            
            if ($status === null) {
                $status = 1;
                merchant_plugconf($merchant_id, "Producticon.status", $status);
            }

            $icons = json_decode($icons, true) ?: [];
            $status = intval($status);

            // 如果不是商家本人访问,且开关关闭,则不返回图标配置
            if (!$this->user || ($this->user->id !== $merchant_id && !$status)) {
                return json(['code' => 200, 'msg' => 'success', 'data' => [
                    'status' => 0,
                    'icons' => []
                ]]);
            }

            return json(['code' => 200, 'msg' => 'success', 'data' => [
                'status' => $status,
                'icons' => $icons
            ]]);
        } catch (\Exception $e) {
            \think\facade\Log::error('获取配置失败：' . $e->getMessage());
            return json(['code' => 0, 'msg' => '获取配置失败']);
        }
    }

    protected function getMerchantIdByMultipleWays($shop_name) {
        if (empty($shop_name)) {
            return 0;
        }
        
        try {
            // 1. 先通过昵称查找
            $merchant_id = \think\facade\Db::name('user')
                ->where('nickname', $shop_name)
                ->value('id');
                
            if ($merchant_id) {
                return $merchant_id;
            }
            
            // 2. 通过店铺名称查找
            $merchant_id = \think\facade\Db::name('user')
                ->where('shop_name', $shop_name)
                ->value('id');
                
            if ($merchant_id) {
                return $merchant_id;
            }
            
            // 3. 通过模糊匹配查找
            $merchant_id = \think\facade\Db::name('user')
                ->where('nickname|shop_name', 'like', "%{$shop_name}%")
                ->value('id');
                
            return $merchant_id ?: 0;
        } catch (\Exception $e) {
            \think\facade\Log::error('查找商家失败：' . $e->getMessage());
            return 0;
        }
    }

    public function save() {
        try {
            if (!$this->user || !$this->user->id) {
                return json(['code' => 403, 'msg' => '请先登录']);
            }

            $status = $this->request->post('status/d', 1);
            $icons = $this->request->post('icons/a', []);

            // 验证图标数据
            foreach ($icons as $icon) {
                if (empty($icon['icon_url'])) {
                    return json(['code' => 0, 'msg' => '请输入图标URL']);
                }
                if (empty($icon['keywords'])) {
                    return json(['code' => 0, 'msg' => '请输入匹配关键词']);
                }
                if (!in_array($icon['position'], ['right-top', 'left-top', 'right-bottom', 'left-bottom'])) {
                    return json(['code' => 0, 'msg' => '图标位置设置不正确']);
                }
                if ($icon['size'] < 20 || $icon['size'] > 100) {
                    return json(['code' => 0, 'msg' => '图标大小必须在20-100像素之间']);
                }
            }

            // 保存配置
            merchant_plugconf($this->user->id, "Producticon.status", $status);
            merchant_plugconf($this->user->id, "Producticon.icons", json_encode($icons, JSON_UNESCAPED_UNICODE));

            return json(['code' => 200, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            \think\facade\Log::error('保存配置失败：' . $e->getMessage());
            return json(['code' => 0, 'msg' => '保存失败']);
        }
    }

} 