#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import sys
import csv
import time
import random
import re
from typing import List, Dict, Optional

class UserRegister:
    def __init__(self, base_url: str = "https://spikeesoft.com",
                 register_endpoint: str = "/merchantApi/user/register",
                 min_username_length: int = 3,
                 max_username_length: int = 20,
                 min_password_length: int = 6,
                 max_password_length: int = 20,
                 use_yescaptcha: bool = False,
                 yescaptcha_key: str = ""):
        """
        初始化用户注册器

        Args:
            base_url: 基础域名
            register_endpoint: 注册接口路径
            min_username_length: 用户名最小长度
            max_username_length: 用户名最大长度
            min_password_length: 密码最小长度
            max_password_length: 密码最大长度
            use_yescaptcha: 是否使用YesCaptcha商业API
            yescaptcha_key: YesCaptcha API密钥
        """
        self.base_url = base_url.rstrip('/')
        self.register_endpoint = register_endpoint
        self.min_username_length = min_username_length
        self.max_username_length = max_username_length
        self.min_password_length = min_password_length
        self.max_password_length = max_password_length
        self.use_yescaptcha = use_yescaptcha
        self.yescaptcha_key = yescaptcha_key
        self.session = requests.Session()

        # 设置默认请求头
        self.headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Content-Type': 'application/json',
            'Origin': self.base_url,
            'Referer': f'{self.base_url}/merchant/register',
            'Sec-Ch-Ua': '"Chromium";v="131", "Not_A Brand";v="24"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"Windows"',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.6778.86 Safari/537.36',
            'Priority': 'u=1, i'
        }

        # 初始化会话，获取PHPSESSID
        self._init_session()

    def _init_session(self):
        """初始化会话，获取PHPSESSID"""
        try:
            print("🔗 初始化会话，获取PHPSESSID...")
            # 访问注册页面获取PHPSESSID
            response = self.session.get(
                url=f"{self.base_url}/merchant/register",
                headers={k: v for k, v in self.headers.items() if k != 'Content-Type'},
                timeout=30
            )
            response.raise_for_status()

            # 检查是否获得了PHPSESSID
            phpsessid = self.session.cookies.get('PHPSESSID')
            if phpsessid:
                print(f"[成功] 获得PHPSESSID: {phpsessid}")
            else:
                print("[警告] 未获得PHPSESSID，可能影响后续请求")

        except Exception as e:
            print(f"[警告] 初始化会话失败: {e}")

    def set_session_cookie(self, phpsessid: str):
        """设置会话Cookie"""
        self.session.cookies.set('PHPSESSID', phpsessid)
        print(f"🍪 设置PHPSESSID: {phpsessid}")

    def validate_params(self, username: str, password: str, mobile: str) -> Dict[str, str]:
        """
        验证注册参数

        Args:
            username: 用户名
            password: 密码
            mobile: 手机号

        Returns:
            dict: 验证结果，包含错误信息
        """
        errors = {}

        # 验证用户名长度
        if len(username) < self.min_username_length:
            errors['username'] = f'用户名长度不能少于{self.min_username_length}位'
        elif len(username) > self.max_username_length:
            errors['username'] = f'用户名长度不能超过{self.max_username_length}位'

        # 验证密码长度
        if len(password) < self.min_password_length:
            errors['password'] = f'密码长度不能少于{self.min_password_length}位'
        elif len(password) > self.max_password_length:
            errors['password'] = f'密码长度不能超过{self.max_password_length}位'

        # 验证手机号格式
        mobile_pattern = r'^1[3-9]\d{9}$'
        if not re.match(mobile_pattern, mobile):
            errors['mobile'] = '手机号格式不正确'

        return errors

    def get_captcha(self) -> Dict:
        """
        获取验证码信息和图片

        Returns:
            dict: 验证码信息，包含图片数据
        """
        url = f"{self.base_url}/merchantApi/Common/captchaStart"
        data = {"code": ""}

        try:
            response = self.session.post(
                url=url,
                headers=self.headers,
                json=data,
                timeout=30
            )

            response.raise_for_status()
            result = response.json()

            if result.get('code') == 1:
                img_url = result['data']['img_url']
                check_url = result['data']['check_url']

                # 立即获取验证码图片数据
                img_response = self.session.get(img_url, timeout=30)
                img_response.raise_for_status()

                return {
                    'success': True,
                    'img_url': img_url,
                    'check_url': check_url,
                    'img_data': img_response.content,  # 图片二进制数据
                    'ip': result['data'].get('ip', '')
                }
            else:
                return {
                    'success': False,
                    'error': result.get('msg', '获取验证码失败')
                }

        except Exception as e:
            return {
                'success': False,
                'error': f'获取验证码失败: {str(e)}'
            }

    def recognize_captcha_with_ddddocr(self, img_data: bytes) -> Dict:
        """
        使用ddddocr识别验证码

        Args:
            img_data: 验证码图片二进制数据

        Returns:
            dict: 识别结果
        """
        try:
            import ddddocr

            # 创建ddddocr实例
            ocr = ddddocr.DdddOcr(show_ad=False)

            # 识别验证码
            captcha_code = ocr.classification(img_data)

            # 清理识别结果（去除空格、特殊字符等）
            captcha_code = ''.join(filter(str.isalnum, captcha_code))

            if captcha_code:
                return {
                    'success': True,
                    'captcha_code': captcha_code,
                    'confidence': 'auto'  # ddddocr不提供置信度
                }
            else:
                return {
                    'success': False,
                    'error': '验证码识别结果为空'
                }

        except ImportError:
            return {
                'success': False,
                'error': 'ddddocr库未安装，请运行: pip install ddddocr'
            }
        except Exception as e:
            return {
                'success': False,
                'error': f'验证码识别失败: {str(e)}'
            }



    def get_captcha_start(self) -> Dict:
        """
        获取验证码 - 第一步

        Returns:
            dict: 验证码获取结果
        """
        try:
            # 发送POST请求获取验证码
            response = self.session.post(
                url=self.base_url + "/merchantApi/Common/captchaStart",
                headers=self.headers,
                json={"code": ""},
                timeout=30
            )

            response.raise_for_status()
            result = response.json()

            # 检查响应格式
            if result.get('code') == 1 and result.get('msg') == 'success':
                data = result.get('data', {})
                img_url = data.get('img_url', '')
                check_url = data.get('check_url', '')

                return {
                    'success': True,
                    'img_url': img_url,
                    'check_url': check_url,
                    'img_key': self.extract_key_from_url(img_url),
                    'check_key': self.extract_key_from_url(check_url),
                    'captcha_key': self.extract_key_from_url(check_url),  # 保持向后兼容
                    'data': result,
                    'status_code': response.status_code
                }
            else:
                return {
                    'success': False,
                    'error': f"获取验证码失败: {result.get('msg', '未知错误')}",
                    'status_code': response.status_code
                }

        except Exception as e:
            return {
                'success': False,
                'error': f'获取验证码失败: {str(e)}',
                'status_code': None
            }

    def extract_key_from_url(self, url: str) -> str:
        """
        从URL中提取key参数

        Args:
            url: 包含key参数的URL

        Returns:
            str: 提取的key值
        """
        try:
            from urllib.parse import urlparse, parse_qs
            parsed = urlparse(url)
            query_params = parse_qs(parsed.query)
            return query_params.get('key', [''])[0]
        except:
            return ""

    def download_and_recognize_captcha(self, img_url: str) -> Dict:
        """
        下载并识别验证码图片

        Args:
            img_url: 验证码图片URL

        Returns:
            dict: 识别结果
        """
        try:
            # 下载验证码图片
            response = self.session.get(img_url, headers=self.headers, timeout=30)
            response.raise_for_status()

            image_data = response.content
            print(f"[图像] 验证码图片下载成功，大小: {len(image_data)} bytes")

            # 尝试使用优化的验证码识别
            try:
                # 首先尝试使用优化的识别方法（包括YesCaptcha）
                try:
                    from captcha_optimizer import enhanced_captcha_recognition
                    captcha_code = enhanced_captcha_recognition(
                        image_data,
                        use_yescaptcha=self.use_yescaptcha,
                        yescaptcha_key=self.yescaptcha_key
                    )
                    print(f"[启动] 优化识别结果: '{captcha_code}'")
                except ImportError:
                    print("[警告] 优化识别器不可用，使用标准ddddocr")
                    # 回退到标准ddddocr
                    import ddddocr
                    ocr = ddddocr.DdddOcr(show_ad=False)
                    captcha_code = ocr.classification(image_data)
                    captcha_code = ''.join(filter(str.isalnum, captcha_code.strip()))
                    print(f"🤖 标准识别结果: '{captcha_code}'")

                # 验证识别结果
                if captcha_code and len(captcha_code) >= 3:  # 验证码通常至少3位
                    print(f"[成功] 验证码识别成功: '{captcha_code}'")
                    return {
                        'success': True,
                        'captcha_code': captcha_code,
                        'image_data': image_data,
                        'auto_recognized': True
                    }
                else:
                    print(f"[警告] 识别结果无效: '{captcha_code}' (长度: {len(captcha_code) if captcha_code else 0})")

                    # 如果优化识别失败，尝试标准识别
                    if 'enhanced_captcha_recognition' in locals():
                        print("[重试] 尝试标准ddddocr识别...")
                        import ddddocr
                        ocr = ddddocr.DdddOcr(show_ad=False)
                        fallback_code = ocr.classification(image_data)
                        fallback_code = ''.join(filter(str.isalnum, fallback_code.strip()))
                        print(f"[重试] 标准识别结果: '{fallback_code}'")

                        if fallback_code and len(fallback_code) >= 3:
                            return {
                                'success': True,
                                'captcha_code': fallback_code,
                                'image_data': image_data,
                                'auto_recognized': True
                            }

                    return {
                        'success': False,
                        'captcha_code': captcha_code or '',
                        'image_data': image_data,
                        'auto_recognized': False,
                        'error': f'识别结果无效: {captcha_code}'
                    }

            except ImportError:
                print("[失败] ddddocr未安装，无法自动识别")
                return {
                    'success': False,
                    'captcha_code': '',
                    'image_data': image_data,
                    'auto_recognized': False,
                    'error': 'ddddocr未安装，无法自动识别'
                }
            except Exception as e:
                print(f"[失败] ddddocr识别异常: {str(e)}")
                return {
                    'success': False,
                    'captcha_code': '',
                    'image_data': image_data,
                    'auto_recognized': False,
                    'error': f'自动识别失败: {str(e)}'
                }

        except Exception as e:
            print(f"[失败] 下载验证码图片失败: {str(e)}")
            return {
                'success': False,
                'error': f'下载验证码图片失败: {str(e)}'
            }

    def verify_captcha(self, captcha_code: str, check_url: str, img_key: str = "") -> Dict:
        """
        验证验证码 - 基于真实请求格式，支持多种sign策略

        Args:
            captcha_code: 验证码
            check_url: 验证URL（从captchaStart响应中获取）
            img_key: 图片URL的key（可选，用于生成sign）

        Returns:
            dict: 验证结果
        """
        try:
            print(f"[检查] 验证验证码: {captcha_code}")
            print(f"🔗 验证URL: {check_url}")

            # 从URL中提取key参数
            from urllib.parse import urlparse, parse_qs
            parsed_url = urlparse(check_url)
            query_params = parse_qs(parsed_url.query)
            check_key = query_params.get('key', [''])[0]

            print(f"[密钥] check_url Key: {check_key}")
            if img_key:
                print(f"[图像] img_url Key: {img_key}")

            # 尝试多种sign策略
            sign_strategies = [
                ("使用img_key", img_key if img_key else check_key),
                ("使用check_key", check_key),
                ("MD5(img_key)", self._md5_hash(img_key) if img_key else self._md5_hash(check_key)),
                ("MD5(check_key)", self._md5_hash(check_key)),
                ("MD5(captcha+img_key)", self._md5_hash(captcha_code + img_key) if img_key else self._md5_hash(captcha_code + check_key)),
                ("MD5(captcha+check_key)", self._md5_hash(captcha_code + check_key)),
            ]

            for strategy_name, sign_value in sign_strategies:
                print(f"\n🧪 尝试策略: {strategy_name}")
                print(f"   sign值: {sign_value}")

                # 构建验证数据
                data = {
                    "code": captcha_code,
                    "sign": sign_value
                }

                print(f"📤 验证请求数据: {data}")

                try:
                    # 发送POST请求验证验证码
                    response = self.session.post(
                        url=check_url,
                        headers=self.headers,
                        json=data,
                        timeout=30
                    )

                    response.raise_for_status()
                    result = response.json()

                    print(f"[编辑] 验证响应: {result}")

                    # 检查验证结果
                    if result.get('code') == 1 and result.get('msg') == 'success':
                        data_content = result.get('data', {})
                        ticket = data_content.get('ticket', '')
                        print(f"[成功] 验证码验证成功！策略: {strategy_name}")
                        print(f"🎫 获得ticket: {ticket}")

                        return {
                            'success': True,
                            'ticket': ticket,
                            'data': result,
                            'status_code': response.status_code,
                            'strategy': strategy_name,
                            'sign_value': sign_value
                        }
                    else:
                        error_msg = result.get('msg', '未知错误')
                        print(f"[失败] 策略失败: {error_msg}")

                except Exception as e:
                    print(f"[失败] 策略异常: {str(e)}")
                    continue

            # 所有策略都失败了
            print(f"\n[失败] 所有sign策略都失败了")
            return {
                'success': False,
                'error': "所有验证码验证策略都失败",
                'status_code': None,
                'captcha_code': captcha_code,
                'check_key': check_key,
                'img_key': img_key
            }

        except Exception as e:
            error_msg = f'验证验证码失败: {str(e)}'
            print(f"[失败] {error_msg}")
            return {
                'success': False,
                'error': error_msg,
                'status_code': None,
                'captcha_code': captcha_code
            }

    def _md5_hash(self, text: str) -> str:
        """计算MD5哈希值"""
        import hashlib
        return hashlib.md5(text.encode()).hexdigest()

    def send_sms(self, mobile: str, ticket: str) -> Dict:
        """
        发送短信验证码 - 基于真实请求格式

        Args:
            mobile: 手机号
            ticket: 验证票据

        Returns:
            dict: 短信发送结果
        """
        try:
            print(f"[手机] 发送短信验证码到: {mobile}")
            print(f"🎫 使用票据: {ticket}")

            # 构建短信发送数据（基于真实请求格式）
            # 真实请求：{"event":"register","mobile":"19239099746","ticket":"ypdqn9rnknq6reongcfwub4z9mq828xx"}
            data = {
                "event": "register",
                "mobile": mobile,
                "ticket": ticket
            }

            print(f"📤 短信请求数据: {data}")

            # 发送POST请求发送短信
            response = self.session.post(
                url=self.base_url + "/merchantApi/sms/send",
                headers=self.headers,
                json=data,
                timeout=30
            )

            response.raise_for_status()
            result = response.json()

            print(f"[编辑] 短信响应: {result}")

            # 检查发送结果
            if result.get('code') == 1:
                print(f"[成功] 短信发送成功")
                return {
                    'success': True,
                    'data': result,
                    'status_code': response.status_code
                }
            else:
                error_msg = result.get('msg', '未知错误')
                print(f"[失败] 短信发送失败: {error_msg}")
                return {
                    'success': False,
                    'error': f"发送短信失败: {error_msg}",
                    'status_code': response.status_code
                }

        except Exception as e:
            error_msg = f'发送短信失败: {str(e)}'
            print(f"[失败] {error_msg}")
            return {
                'success': False,
                'error': error_msg,
                'status_code': None
            }

    def register_user(self, username: str, password: str, mobile: str,
                     mobile_code: str = "", invite_code: str = "", invite_token: str = "",
                     captcha_code: str = "") -> Dict:
        """
        用户注册 - 完整流程

        Args:
            username: 用户名
            password: 密码
            mobile: 手机号
            mobile_code: 手机验证码
            invite_code: 邀请码，默认为空
            invite_token: 邀请令牌，默认为空
            captcha_code: 图形验证码，默认为空

        Returns:
            dict: 注册结果
        """

        # 验证参数
        validation_errors = self.validate_params(username, password, mobile)
        if validation_errors:
            return {
                'success': False,
                'error': '参数验证失败',
                'validation_errors': validation_errors,
                'status_code': None
            }

        print(f"[启动] 执行用户注册")
        print(f"[用户] 用户名: {username}")
        print(f"[手机] 手机号: {mobile}")
        print(f"[短信] 短信验证码: {mobile_code}")

        # 构建请求数据（基于真实请求格式）
        # 真实请求：{"username":"2337761309","password":"2337761309","repeat_password":"2337761309","mobile":"19239099746","mobile_code":"720719","invite_code":"","invite_token":""}
        data = {
            "username": username,
            "password": password,
            "repeat_password": password,  # 重复密码与密码相同
            "mobile": mobile,
            "mobile_code": mobile_code,
            "invite_code": invite_code,
            "invite_token": invite_token
        }

        print(f"📤 注册请求数据: {data}")

        try:
            # 发送POST请求
            response = self.session.post(
                url=self.base_url + self.register_endpoint,
                headers=self.headers,
                json=data,
                timeout=30
            )

            # 检查响应状态
            response.raise_for_status()

            # 解析JSON响应
            result = response.json()

            print(f"[编辑] 注册响应: {result}")

            # 检查注册结果
            if result.get('code') == 1:
                print(f"[完成] 注册成功！")
                return {
                    'success': True,
                    'status_code': response.status_code,
                    'data': result,
                    'username': username,
                    'mobile': mobile
                }
            else:
                error_msg = result.get('msg', '未知错误')
                print(f"[失败] 注册失败: {error_msg}")
                return {
                    'success': False,
                    'error': f"注册失败: {error_msg}",
                    'status_code': response.status_code,
                    'data': result,
                    'username': username,
                    'mobile': mobile
                }

        except requests.exceptions.RequestException as e:
            return {
                'success': False,
                'error': f'请求错误: {str(e)}',
                'status_code': getattr(e.response, 'status_code', None) if hasattr(e, 'response') else None,
                'username': username,
                'mobile': mobile
            }
        except json.JSONDecodeError as e:
            return {
                'success': False,
                'error': f'JSON解析错误: {str(e)}',
                'status_code': response.status_code if 'response' in locals() else None,
                'username': username,
                'mobile': mobile
            }
        except Exception as e:
            return {
                'success': False,
                'error': f'未知错误: {str(e)}',
                'status_code': None,
                'username': username,
                'mobile': mobile
            }

    def register_user_complete_flow(self, username: str, password: str, mobile: str = "",
                                   mobile_code: str = "", invite_code: str = "", invite_token: str = "",
                                   use_yezi_cloud: bool = False, yezi_api=None, project_id: str = "") -> Dict:
        """
        完整的用户注册流程（包含验证码和短信验证）

        Args:
            username: 用户名
            password: 密码
            mobile: 手机号（如果使用椰子云则可为空）
            mobile_code: 手机验证码
            invite_code: 邀请码，默认为空
            invite_token: 邀请令牌，默认为空
            use_yezi_cloud: 是否使用椰子云获取手机号和验证码
            yezi_api: 椰子云API实例
            project_id: 椰子云项目ID

        Returns:
            dict: 注册结果
        """
        try:
            # 如果使用椰子云，先获取手机号
            if use_yezi_cloud and yezi_api and project_id:
                mobile_result = yezi_api.get_mobile(project_id)
                if not mobile_result['success']:
                    return {
                        'success': False,
                        'error': f"椰子云获取手机号失败: {mobile_result['error']}",
                        'step': 'yezi_get_mobile'
                    }
                mobile = mobile_result['mobile']

            # 第一步：获取图形验证码
            captcha_result = self.get_captcha_start()
            if not captcha_result['success']:
                return {
                    'success': False,
                    'error': f"获取验证码失败: {captcha_result['error']}",
                    'step': 'captcha_start'
                }

            img_url = captcha_result['img_url']
            captcha_key = captcha_result['captcha_key']

            # 第二步：下载并识别验证码
            recognize_result = self.download_and_recognize_captcha(img_url)
            if not recognize_result['success']:
                return {
                    'success': False,
                    'error': f"识别验证码失败: {recognize_result['error']}",
                    'step': 'captcha_recognize'
                }

            captcha_code = recognize_result.get('captcha_code', '')
            if not captcha_code:
                return {
                    'success': False,
                    'error': "验证码识别失败，无法获取验证码",
                    'step': 'captcha_empty'
                }

            # 第三步：验证图形验证码
            verify_result = self.verify_captcha(captcha_code, captcha_key)
            if not verify_result['success']:
                return {
                    'success': False,
                    'error': f"验证码验证失败: {verify_result['error']}",
                    'step': 'captcha_verify'
                }

            ticket = verify_result['ticket']

            # 第四步：发送短信验证码
            sms_result = self.send_sms(mobile, ticket)
            if not sms_result['success']:
                return {
                    'success': False,
                    'error': f"发送短信失败: {sms_result['error']}",
                    'step': 'sms_send'
                }

            # 第五步：等待椰子云短信验证码（如果使用椰子云）
            if use_yezi_cloud and yezi_api:
                import time
                start_time = time.time()
                max_wait_time = 120
                check_interval = 3

                while time.time() - start_time < max_wait_time:
                    sms_check_result = yezi_api.get_sms(project_id, mobile)

                    if sms_check_result['success'] and sms_check_result.get('received'):
                        mobile_code = sms_check_result.get('code', '')
                        if mobile_code:
                            break

                    time.sleep(check_interval)
                else:
                    return {
                        'success': False,
                        'error': f'等待椰子云短信验证码超时（{max_wait_time}秒）',
                        'step': 'yezi_sms_timeout',
                        'mobile': mobile
                    }

            # 第六步：最终注册
            return self.register_user(username, password, mobile, mobile_code, invite_code, invite_token)

        except Exception as e:
            return {
                'success': False,
                'error': f'注册流程异常: {str(e)}',
                'step': 'exception'
            }

    def continue_registration_after_captcha(self, username: str, password: str, mobile: str,
                                          captcha_code: str, captcha_key: str,
                                          mobile_code: str = "", invite_code: str = "", invite_token: str = "") -> Dict:
        """
        验证码验证后继续注册流程

        Args:
            username: 用户名
            password: 密码
            mobile: 手机号
            captcha_code: 图形验证码
            captcha_key: 验证码key
            mobile_code: 手机验证码
            invite_code: 邀请码，默认为空
            invite_token: 邀请令牌，默认为空

        Returns:
            dict: 注册结果
        """
        try:
            # 第二步：验证图形验证码
            verify_result = self.verify_captcha(captcha_code, captcha_key)
            if not verify_result['success']:
                return {
                    'success': False,
                    'error': f"验证码验证失败: {verify_result['error']}",
                    'step': 'captcha_verify'
                }

            # 从验证结果中获取ticket
            verify_data = verify_result['data']
            ticket = verify_data.get('ticket', '')

            if not ticket:
                return {
                    'success': False,
                    'error': '验证码验证成功但未获取到ticket',
                    'step': 'ticket_missing'
                }

            # 第三步：发送短信验证码
            sms_result = self.send_sms(mobile, ticket)
            if not sms_result['success']:
                return {
                    'success': False,
                    'error': f"发送短信失败: {sms_result['error']}",
                    'step': 'sms_send'
                }

            # 如果没有提供手机验证码，返回等待状态
            if not mobile_code:
                return {
                    'success': False,
                    'error': '短信已发送，请输入手机验证码',
                    'step': 'sms_sent',
                    'next_step': 'final_register',
                    'ticket': ticket
                }

            # 第四步：最终注册
            return self.final_register(username, password, mobile, mobile_code, invite_code, invite_token)

        except Exception as e:
            return {
                'success': False,
                'error': f'注册流程异常: {str(e)}',
                'step': 'exception'
            }

    def final_register(self, username: str, password: str, mobile: str,
                      mobile_code: str, invite_code: str = "", invite_token: str = "") -> Dict:
        """
        最终注册步骤

        Args:
            username: 用户名
            password: 密码
            mobile: 手机号
            mobile_code: 手机验证码
            invite_code: 邀请码，默认为空
            invite_token: 邀请令牌，默认为空

        Returns:
            dict: 注册结果
        """
        return self.register_user(username, password, mobile, mobile_code, invite_code, invite_token)

    def register_with_sms_flow(self, username: str, password: str, mobile: str = "",
                              invite_code: str = "", invite_token: str = "",
                              use_yezi_cloud: bool = False, yezi_api=None, project_id: str = "") -> Dict:
        """
        带短信验证的完整注册流程（基于970faka.com成功案例）

        Args:
            username: 用户名
            password: 密码
            mobile: 手机号（如果使用椰子云则可为空）
            invite_code: 邀请码，默认为空
            invite_token: 邀请令牌，默认为空
            use_yezi_cloud: 是否使用椰子云获取手机号和验证码
            yezi_api: 椰子云API实例
            project_id: 椰子云项目ID

        Returns:
            dict: 注册结果
        """
        try:
            # 如果使用椰子云，先获取手机号
            if use_yezi_cloud and yezi_api and project_id:
                mobile_result = yezi_api.get_mobile(project_id)
                if not mobile_result['success']:
                    return {
                        'success': False,
                        'error': f"椰子云获取手机号失败: {mobile_result['error']}",
                        'step': 'yezi_get_mobile'
                    }
                mobile = mobile_result['mobile']

            # 第一步：获取图形验证码
            captcha_result = self.get_captcha_start()
            if not captcha_result['success']:
                return {
                    'success': False,
                    'error': f"获取验证码失败: {captcha_result['error']}",
                    'step': 'captcha_start'
                }

            img_url = captcha_result['img_url']
            captcha_key = captcha_result['captcha_key']

            # 第二步：下载并识别验证码
            recognize_result = self.download_and_recognize_captcha(img_url)
            if not recognize_result['success']:
                return {
                    'success': False,
                    'error': f"识别验证码失败: {recognize_result['error']}",
                    'step': 'captcha_recognize'
                }

            captcha_code = recognize_result.get('captcha_code', '')
            if not captcha_code:
                return {
                    'success': False,
                    'error': "验证码识别失败，无法获取验证码",
                    'step': 'captcha_empty'
                }

            # 第三步：验证图形验证码
            check_url = captcha_result['check_url']
            img_key = captcha_result.get('img_key', '')
            verify_result = self.verify_captcha(captcha_code, check_url, img_key)
            if not verify_result['success']:
                return {
                    'success': False,
                    'error': f"验证码验证失败: {verify_result['error']}",
                    'step': 'captcha_verify'
                }

            ticket = verify_result['ticket']

            # 第四步：发送短信验证码
            sms_result = self.send_sms(mobile, ticket)
            if not sms_result['success']:
                return {
                    'success': False,
                    'error': f"发送短信失败: {sms_result['error']}",
                    'step': 'sms_send'
                }

            # 第五步：等待椰子云短信验证码（如果使用椰子云）
            mobile_code = ""
            if use_yezi_cloud and yezi_api:
                import time
                start_time = time.time()
                max_wait_time = 120
                check_interval = 3

                while time.time() - start_time < max_wait_time:
                    sms_check_result = yezi_api.get_sms(project_id, mobile)

                    if sms_check_result['success'] and sms_check_result.get('received'):
                        mobile_code = sms_check_result.get('code', '')
                        if mobile_code:
                            break

                    time.sleep(check_interval)
                else:
                    return {
                        'success': False,
                        'error': f'等待椰子云短信验证码超时（{max_wait_time}秒）',
                        'step': 'yezi_sms_timeout',
                        'mobile': mobile
                    }

            # 第六步：最终注册
            register_result = self.register_user(username, password, mobile, mobile_code, invite_code, invite_token)

            # 如果使用椰子云，注册完成后释放手机号
            if use_yezi_cloud and yezi_api and mobile:
                try:
                    import time
                    time.sleep(2)  # 等待2秒再释放
                    yezi_api.release_mobile(mobile, project_id)
                except:
                    pass  # 释放失败不影响注册结果

            return register_result

        except Exception as e:
            return {
                'success': False,
                'error': f'注册流程异常: {str(e)}',
                'step': 'exception'
            }

    def load_users_from_csv(self, csv_file: str) -> List[Dict]:
        """
        从CSV文件加载用户数据

        CSV格式: username,password,mobile,invite_code

        Args:
            csv_file: CSV文件路径

        Returns:
            list: 用户数据列表
        """
        users = []
        try:
            with open(csv_file, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                for row in reader:
                    users.append({
                        'username': row.get('username', '').strip(),
                        'password': row.get('password', '').strip(),
                        'mobile': row.get('mobile', '').strip(),
                        'invite_code': row.get('invite_code', '').strip()
                    })
        except FileNotFoundError:
            print(f"[失败] 文件未找到: {csv_file}")
        except Exception as e:
            print(f"[失败] 读取CSV文件错误: {str(e)}")

        return users

    def batch_register(self, users: List[Dict], delay_range: tuple = (1, 3),
                      max_retries: int = 3) -> List[Dict]:
        """
        批量注册用户

        Args:
            users: 用户数据列表
            delay_range: 请求间隔时间范围（秒）
            max_retries: 最大重试次数

        Returns:
            list: 注册结果列表
        """
        results = []
        total = len(users)

        print(f"[启动] 开始批量注册，共 {total} 个用户")
        print("=" * 60)

        for i, user in enumerate(users, 1):
            print(f"[{i}/{total}] 正在注册用户: {user['username']}")

            # 重试机制
            for attempt in range(max_retries):
                result = self.register_user(
                    username=user['username'],
                    password=user['password'],
                    mobile=user['mobile'],
                    invite_code=user.get('invite_code', '')
                )

                if result['success']:
                    print(f"[成功] 注册成功: {user['username']}")
                    break
                else:
                    if attempt < max_retries - 1:
                        print(f"[警告]  注册失败，正在重试 ({attempt + 1}/{max_retries}): {result['error']}")
                        time.sleep(random.uniform(0.5, 1.5))
                    else:
                        print(f"[失败] 注册失败: {user['username']} - {result['error']}")

            results.append(result)

            # 随机延迟，避免请求过于频繁
            if i < total:
                delay = random.uniform(delay_range[0], delay_range[1])
                print(f"⏳ 等待 {delay:.1f} 秒...")
                time.sleep(delay)

        return results

    def save_results_to_csv(self, results: List[Dict], output_file: str = "register_results.csv"):
        """
        保存注册结果到CSV文件

        Args:
            results: 注册结果列表
            output_file: 输出文件路径
        """
        try:
            with open(output_file, 'w', newline='', encoding='utf-8') as file:
                fieldnames = ['username', 'mobile', 'success', 'error', 'status_code']
                writer = csv.DictWriter(file, fieldnames=fieldnames)

                writer.writeheader()
                for result in results:
                    writer.writerow({
                        'username': result.get('username', ''),
                        'mobile': result.get('mobile', ''),
                        'success': result.get('success', False),
                        'error': result.get('error', ''),
                        'status_code': result.get('status_code', '')
                    })

            print(f"[进度] 注册结果已保存到: {output_file}")
        except Exception as e:
            print(f"[失败] 保存结果文件错误: {str(e)}")

    def print_summary(self, results: List[Dict]):
        """
        打印注册结果摘要

        Args:
            results: 注册结果列表
        """
        total = len(results)
        success_count = sum(1 for r in results if r['success'])
        failed_count = total - success_count

        print("\n" + "=" * 60)
        print("📈 注册结果摘要")
        print("=" * 60)
        print(f"总计: {total} 个用户")
        print(f"成功: {success_count} 个用户 ({success_count/total*100:.1f}%)")
        print(f"失败: {failed_count} 个用户 ({failed_count/total*100:.1f}%)")

        if failed_count > 0:
            print("\n[失败] 失败详情:")
            for result in results:
                if not result['success']:
                    print(f"  - {result['username']}: {result['error']}")

        print("=" * 60)
