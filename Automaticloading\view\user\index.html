<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据自动匹配系统</title>
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <style>
        body {
            margin: 0;
            background: #f5f7fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
            height: 100vh;
            overflow: hidden;
        }

        .main-container {
            height: 650px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
        }

        .data-panel {
            background: #fff;
            border-radius: 8px;
            flex: 1;
            display: flex;
            flex-direction: column;
            margin-bottom: 20px;
        }

        .history-table {
            background: #fff;
            border-radius: 8px;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .pagination-container {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            padding-bottom: 10px;
        }
        
        .el-pagination {
            justify-content: center !important;
        }

        .upload-container {
            padding: 20px;
        }

        .history-header {
            padding: 15px 20px;
            border-bottom: 1px solid #ebeef5;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="main-container">
            <!-- 数据上传面板 -->
            <div class="data-panel">
                <div class="upload-container">
                    <el-card>
                        <template #header>
                            <div class="card-header">
                                <span>游戏数据自动处理</span>
                            </div>
                        </template>
                        
                        <el-tabs v-model="activeTab" @tab-click="handleTabChange">
                            <!-- 数据处理 -->
                            <el-tab-pane label="数据检测(自动识别等级)" name="confidential">
                                <el-form :model="uploadForm" label-position="top">
                                    <el-form-item label="请输入用户数据（系统将根据等级自动判断，12-29级为机密，30级以上为绝密，12级以下可选择手动指定商品）：">
                                        <el-input
                                            v-model="uploadForm.userData"
                                            type="textarea"
                                            :rows="8"
                                            placeholder="请粘贴用户数据，格式如：角色昵称：xxx|等级：xx|哈夫币：xxx|道具价值：xxx|...">
                                        </el-input>
                                    </el-form-item>
                                    
                                    <!-- 添加低等级角色商品选择 -->
                                    <el-form-item label="低等级角色选项（12级以下角色将使用此选项）：">
                                        <el-alert
                                            title="注意：系统检测到12级以下角色时，将使用此处选择的商品进行匹配"
                                            type="info"
                                            :closable="false"
                                            show-icon
                                            style="margin-bottom: 10px;">
                                        </el-alert>
                                        <div style="display: flex; align-items: center;">
                                            <el-checkbox v-model="uploadForm.enableLowLevelProcess">启用12级以下角色处理</el-checkbox>
                                            <el-tooltip content="启用后，12级以下角色将按照选择的商品进行处理" placement="top">
                                                <el-icon style="margin-left: 5px;"><question-filled /></el-icon>
                                            </el-tooltip>
                                        </div>
                                        <div v-if="uploadForm.enableLowLevelProcess" style="margin-top: 10px;">
                                            <el-select 
                                                v-model="uploadForm.lowLevelGoodsId" 
                                                placeholder="请选择商品" 
                                                filterable 
                                                style="width: 100%;">
                                                <el-option
                                                    v-for="item in goodsList"
                                                    :key="item.id"
                                                    :label="item.name"
                                                    :value="item.id">
                                                    <span>{{ item.name }}</span>
                                                    <span style="float: right; color: #8492a6; font-size: 13px">ID: {{ item.id }}</span>
                                                </el-option>
                                            </el-select>
                                        </div>
                                    </el-form-item>
                                    
                                    <!-- 添加仓库价值范围商品选择 -->
                                    <el-form-item label="仓库价值范围选项（0-1.9M的角色将使用此选项）：">
                                        <el-alert
                                            title="注意：系统检测到仓库价值在0-1.9M范围内的角色时，将使用此处选择的商品进行匹配"
                                            type="info"
                                            :closable="false"
                                            show-icon
                                            style="margin-bottom: 10px;">
                                        </el-alert>
                                        <div style="display: flex; align-items: center;">
                                            <el-checkbox v-model="uploadForm.enableWarehouseValueProcess">启用仓库价值范围处理</el-checkbox>
                                            <el-tooltip content="启用后，仓库价值在0-1.9M范围内的角色将按照选择的商品进行处理" placement="top">
                                                <el-icon style="margin-left: 5px;"><question-filled /></el-icon>
                                            </el-tooltip>
                                        </div>
                                        <div v-if="uploadForm.enableWarehouseValueProcess" style="margin-top: 10px;">
                                            <el-select 
                                                v-model="uploadForm.warehouseValueGoodsId" 
                                                placeholder="请选择商品" 
                                                filterable 
                                                style="width: 100%;">
                                                <el-option
                                                    v-for="item in goodsList"
                                                    :key="item.id"
                                                    :label="item.name"
                                                    :value="item.id">
                                                    <span>{{ item.name }}</span>
                                                    <span style="float: right; color: #8492a6; font-size: 13px">ID: {{ item.id }}</span>
                                                </el-option>
                                            </el-select>
                                        </div>
                                    </el-form-item>
                                    
                                    <el-form-item label="数据过滤设置（选中的字段将被过滤掉）：">
                                        <div style="margin-bottom: 10px;">
                                            <el-checkbox v-model="uploadForm.enableFilter">启用字段过滤</el-checkbox>
                                            <el-tooltip content="选中的字段将被从数据中删除" placement="top">
                                                <el-icon style="margin-left: 5px;"><question-filled /></el-icon>
                                            </el-tooltip>
                                        </div>
                                        <div v-if="uploadForm.enableFilter">
                                            <div style="display: flex; flex-wrap: wrap; gap: 10px;">
                                                <el-checkbox v-model="uploadForm.filterFields.loginToday" label="今日登录">今日登录</el-checkbox>
                                                <el-checkbox v-model="uploadForm.filterFields.banned" label="封号">封号</el-checkbox>
                                                <el-checkbox v-model="uploadForm.filterFields.muted" label="禁言">禁言</el-checkbox>
                                                <el-checkbox v-model="uploadForm.filterFields.lastLoginTime" label="最后登录时间">最后登录时间</el-checkbox>
                                                <el-checkbox v-model="uploadForm.filterFields.lastLogoutTime" label="最后退出间">最后退出间</el-checkbox>
                                                <el-checkbox v-model="uploadForm.filterFields.onlineStatus" label="在线状态">在线状态</el-checkbox>
                                            </div>
                                            <div style="margin-top: 10px;">
                                                <el-input
                                                    v-model="uploadForm.customFilterField"
                                                    placeholder="添加自定义过滤字段"
                                                    style="width: 200px; margin-right: 10px;">
                                                </el-input>
                                                <el-button size="small" @click="addCustomFilterField" :disabled="!uploadForm.customFilterField">添加</el-button>
                                            </div>
                                            <div style="margin-top: 10px;" v-if="uploadForm.customFilterFields.length > 0">
                                                <el-tag
                                                    v-for="(field, index) in uploadForm.customFilterFields"
                                                    :key="index"
                                                    closable
                                                    @close="removeCustomFilterField(index)"
                                                    style="margin-right: 5px; margin-bottom: 5px;">
                                                    {{ field }}
                                                </el-tag>
                                            </div>
                                        </div>
                                    </el-form-item>
                                    
                                    <!-- 添加过滤历史成功数据选项 -->
                                    <el-form-item>
                                        <div style="display: flex; align-items: center; margin-bottom: 15px;">
                                            <el-checkbox v-model="uploadForm.skipPreviousSuccess">自动过滤之前成功处理的数据</el-checkbox>
                                            <el-tooltip content="启用后，系统会永久记录并自动跳过所有历史上成功处理过的角色数据，无需担心数据过期" placement="top">
                                                <el-icon style="margin-left: 5px;"><question-filled /></el-icon>
                                            </el-tooltip>
                                        </div>
                                    </el-form-item>
                                    
                                    <el-form-item>
                                        <el-button 
                                            type="primary" 
                                            @click="handleUpload('confidential')" 
                                            :loading="uploadLoading"
                                            :disabled="isProcessButtonDisabled">
                                            处理数据
                                        </el-button>
                                        <el-button @click="clearForm">清空</el-button>
                                        <el-upload
                                            class="upload-demo"
                                            action="/merchantApi/Upload/file"
                                            :show-file-list="false"
                                            :on-success="(res) => handleFileUploadSuccess(res, 'confidential')"
                                            :on-error="handleFileUploadError"
                                            :before-upload="beforeFileUpload"
                                            :headers="uploadHeaders"
                                            name="file"
                                            style="display: inline-block; margin-left: 10px;">
                                            <el-button type="success" :disabled="uploadLoading">导入记事本</el-button>
                                        </el-upload>
                                    </el-form-item>
                                </el-form>
                            </el-tab-pane>
                        </el-tabs>
                    </el-card>
                </div>
            </div>
            
            <!-- 结果对话框 -->
            <el-dialog
                v-model="resultVisible"
                title="处理结果"
                width="70%">
                <div v-if="processResult">
                    <div v-if="processResult.details && processResult.details.length > 0">
                        <el-alert
                            :title="processResult.message"
                            :type="processResult.status === 'success' ? 'success' : (processResult.status === 'warning' ? 'warning' : 'error')"
                            :closable="false"
                            show-icon
                            style="margin-bottom: 15px;">
                        </el-alert>
                        
                        <!-- 显示重复数据警告 -->
                        <el-alert
                            v-if="processResult.details.some(item => item.status === 'warning')"
                            :title="processResult.is_batch_duplicate ? '您已重复提交相同批次数据' : '发现重复数据，系统已自动过滤'"
                            type="warning"
                            :closable="false"
                            show-icon
                            style="margin-bottom: 15px;">
                            <template v-if="processResult.is_batch_duplicate" #default>
                                <div>您已提交过相同批次数据。请开启"自动过滤之前成功处理的数据"选项以避免重复处理。</div>
                                <div>开启后系统将自动过滤已成功处理的数据，只处理未处理或失败的数据。</div>
                            </template>
                        </el-alert>
                        
                        <!-- 显示处理统计信息 -->
                        <div v-if="processResult.summary" style="margin-bottom: 15px; padding: 10px; background-color: #f8f8f8; border-radius: 4px;">
                            <h4 style="margin-top: 0;">处理统计</h4>
                            <div style="display: flex; flex-wrap: wrap; gap: 20px;">
                                <div>
                                    <el-tag type="success">成功处理: {{ processResult.summary.success }} 条</el-tag>
                                </div>
                                <div v-if="processResult.summary.duplicate > 0">
                                    <el-tag type="warning">已过滤重复: {{ processResult.summary.duplicate }} 条</el-tag>
                                </div>
                                <div v-if="processResult.summary.failed > 0">
                                    <el-tag type="danger">处理失败: {{ processResult.summary.failed }} 条</el-tag>
                                </div>
                                <div v-if="getPreviouslySucceededCount() > 0">
                                    <el-tag type="info">历史已处理: {{ getPreviouslySucceededCount() }} 条</el-tag>
                                </div>
                                <div>
                                    <el-tag type="info">总计: {{ processResult.summary.total }} 条</el-tag>
                                </div>
                            </div>
                            <!-- 添加导出失败数据按钮 -->
                            <div v-if="processResult.failed_data_id && processResult.summary.failed > 0" style="margin-top: 10px;">
                                <el-button type="warning" size="small" @click="exportFailedData">导出失败原数据</el-button>
                            </div>
                        </div>
                        
                        <el-table :data="processResult.details" style="width: 100%" border>
                            <el-table-column prop="nickname" label="角色昵称" min-width="150"></el-table-column>
                            <el-table-column label="等级" width="100">
                                <template #default="scope">
                                    {{ scope.row.level || '未知' }}
                                </template>
                            </el-table-column>
                            <el-table-column label="仓库价值" width="120">
                                <template #default="scope">
                                    {{ formatWarehouseValue(scope.row.warehouse_value) }}
                                </template>
                            </el-table-column>
                            <el-table-column prop="goods_id" label="商品ID" width="100" v-if="processResult.status === 'success'"></el-table-column>
                            <el-table-column prop="goods_name" label="商品名称" min-width="150" v-if="processResult.status === 'success'"></el-table-column>
                            <el-table-column prop="message" label="处理结果" min-width="200">
                                <template #default="scope">
                                    <div>
                                        <span :style="(scope.row.is_duplicate || scope.row.previous_success) ? 'color: #E6A23C; font-weight: bold;' : ''">{{ scope.row.message }}</span>
                                        <el-tooltip v-if="scope.row.is_duplicate" content="此数据因重复而未被写入数据库" placement="top">
                                            <el-icon style="margin-left: 5px;"><warning /></el-icon>
                                        </el-tooltip>
                                        <el-tooltip v-if="scope.row.previous_success" :content="'上次成功处理时间: ' + scope.row.previous_timestamp" placement="top">
                                            <el-icon style="margin-left: 5px;"><timer /></el-icon>
                                        </el-tooltip>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column label="状态" width="110">
                                <template #default="scope">
                                    <el-tag v-if="scope.row.status === 'success'" type="success">成功</el-tag>
                                    <el-tag v-else-if="scope.row.status === 'warning'" type="warning" effect="dark">
                                        {{ scope.row.is_duplicate ? '重复数据' : (scope.row.previous_success ? '已处理' : (scope.row.message.includes('完全相同') ? '完全相同' : '重复')) }}
                                    </el-tag>
                                    <el-tag v-else type="danger">失败</el-tag>
                                </template>
                            </el-table-column>
                            
                            <!-- 添加操作列 -->
                            <el-table-column label="操作" width="120" fixed="right">
                                <template #default="scope">
                                    <!-- 对于之前成功处理过的数据，提供清除缓存的按钮 -->
                                    <el-button 
                                        v-if="scope.row.previous_success" 
                                        size="small" 
                                        type="warning" 
                                        @click="clearRoleCache(scope.row.nickname)">
                                        清除缓存
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>

                        <!-- 添加全局操作按钮 -->
                        <div style="margin-top: 15px; text-align: right;">
                            <el-button type="danger" size="small" @click="clearAllRoleCache" v-if="uploadForm.skipPreviousSuccess">
                                清除所有角色缓存
                            </el-button>
                        </div>
                    </div>
                    <div v-else>
                        <el-result
                            :icon="processResult.status === 'success' ? 'success' : (processResult.status === 'warning' ? 'warning' : 'error')"
                            :title="processResult.status === 'success' ? '处理成功' : (processResult.status === 'warning' ? '重复数据' : '处理失败')"
                            :sub-title="processResult.message">
                            <template #extra v-if="processResult.status === 'success'">
                                <div>
                                    <p><strong>匹配商品ID:</strong> {{ processResult.goods_id }}</p>
                                    <p v-if="processResult.goods_name"><strong>商品名称:</strong> {{ processResult.goods_name }}</p>
                                    <p><strong>仓库价值:</strong> {{ processResult.warehouse_value }}</p>
                                </div>
                            </template>
                        </el-result>
                    </div>
                </div>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="resultVisible = false">关闭</el-button>
                        <el-button type="primary" @click="afterProcessSuccess">确定</el-button>
                    </span>
                </template>
            </el-dialog>
        </div>
    </div>

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>
    
    <script>
        const { createApp, ref, onMounted, reactive, computed } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        createApp({
            setup() {
                // 标签页状态
                const activeTab = ref('confidential'); // 默认选中机密数据标签页
                
                // 标签页切换处理
                const handleTabChange = () => {
                    // 清空表单数据
                    clearForm();
                };
                
                // 上传表单数据
                const uploadForm = reactive({
                    userData: '',
                    enableFilter: false,
                    filterFields: {
                        loginToday: false,
                        banned: false,
                        muted: false,
                        lastLoginTime: false,
                        lastLogoutTime: false,
                        onlineStatus: false
                    },
                    customFilterField: '',
                    customFilterFields: [],
                    dataType: '',
                    enableLowLevelProcess: false,
                    lowLevelGoodsId: '',
                    enableWarehouseValueProcess: false,
                    warehouseValueGoodsId: '',
                    // 添加防重复提交标记
                    lastSubmitData: '',
                    lastSubmitTime: 0,
                    skipPreviousSuccess: false
                });
                
                // 商品列表数据
                const goodsList = ref([]);
                
                // 加载商品列表
                const loadGoodsList = async () => {
                    try {
                        const res = await axios.get('/plugin/Automaticloading/user/getGoodsList');
                        if (res.data.code === 200 && res.data.data) {
                            goodsList.value = res.data.data;
                        } else {
                            console.error('获取商品列表失败:', res.data.msg);
                        }
                    } catch (error) {
                        console.error('获取商品列表失败:', error);
                    }
                };
                
                // 文件上传相关配置
                const uploadHeaders = {
                    'X-Requested-With': 'XMLHttpRequest'
                };

                // 上传配置
                const uploadConfig = ref({
                    allowed_extensions: ['txt'],
                    max_size: 2048, // 2MB in KB
                    mime_types: ['text/plain'],
                    loaded: false
                });
                
                // 加载上传配置
                const loadUploadConfig = async () => {
                    try {
                        const res = await axios.get('/plugin/Automaticloading/Api/getUploadConfig');
                        if (res.data.code === 200 && res.data.data) {
                            uploadConfig.value = {
                                ...res.data.data,
                                loaded: true
                            };
                        }
                    } catch (error) {
                        console.error('获取上传配置失败:', error);
                    }
                };
                
                // 上传相关状态
                const uploadLoading = ref(false);
                const processResult = ref(null);
                const resultVisible = ref(false);
                
                // 处理按钮禁用状态计算
                const isProcessButtonDisabled = computed(() => {
                    // 正在上传或处理中时禁用
                    if (uploadLoading.value) return true;
                    
                    // 无数据时禁用
                    if (!uploadForm.userData.trim()) return true;
                    
                    // 如果提交过相同数据且时间间隔小于3秒，禁用按钮
                    const currentTime = new Date().getTime();
                    const timeDiff = currentTime - uploadForm.lastSubmitTime;
                    if (uploadForm.userData.trim() === uploadForm.lastSubmitData.trim() && timeDiff < 3000) {
                        return true;
                    }
                    
                    return false;
                });
                
                // 文件上传前验证
                const beforeFileUpload = (file) => {
                    // 初始化加载上传配置
                    if (!uploadConfig.value.loaded) {
                        loadUploadConfig();
                    }

                    const fileExt = file.name.split('.').pop().toLowerCase();
                    // 检查文件类型
                    const isAllowedType = uploadConfig.value.allowed_extensions.includes(fileExt);
                    const isAllowedMime = uploadConfig.value.mime_types.includes(file.type);
                    const isLtMaxSize = file.size / 1024 <= uploadConfig.value.max_size;
                    
                    if (!isAllowedType) {
                        ElMessage.error(`只能上传以下类型文件: ${uploadConfig.value.allowed_extensions.join(', ')}`);
                        return false;
                    }
                    
                    if (!isAllowedMime) {
                        console.warn(`MIME类型检查: ${file.type} 不在允许列表中`);
                        // 仅警告，不阻止上传
                    }
                    
                    if (!isLtMaxSize) {
                        ElMessage.error(`文件大小不能超过 ${uploadConfig.value.max_size / 1024} MB!`);
                        return false;
                    }
                    
                    uploadLoading.value = true;
                    return true;
                };
                
                // 文件上传成功处理
                const handleFileUploadSuccess = (response, dataType) => {
                    uploadLoading.value = false;
                    if (response.code === 1 && response.data && response.data.url) {
                        // 从上传的文件获取内容
                        fetch(response.data.url)
                            .then(res => res.text())
                            .then(content => {
                                // 处理文本格式
                                content = formatImportedText(content);
                                
                                // 检查导入的内容是否与最近提交的数据相同
                                const trimmedContent = content.trim();
                                const currentTime = new Date().getTime();
                                const timeDiff = currentTime - uploadForm.lastSubmitTime;
                                
                                // 如果导入的内容与上次提交的内容相同，且时间间隔较短，提示用户
                                if (trimmedContent === uploadForm.lastSubmitData.trim() && timeDiff < 60000) { // 1分钟内
                                    ElMessage.warning('检测到您正在导入可能重复的数据，系统将自动过滤数据库中已有的记录');
                                }
                                
                                // 检查导入数据中是否有重复的角色昵称
                                const duplicateNicknames = checkDuplicateNicknames(content);
                                if (duplicateNicknames.length > 0) {
                                    ElMessage({
                                        message: `导入的数据中存在重复角色昵称: ${duplicateNicknames.join(', ')}，系统将自动处理`,
                                        type: 'warning',
                                        duration: 5000
                                    });
                                }
                                
                                // 设置表单数据
                                uploadForm.userData = content;
                                
                                // 检查数据是否包含有效角色信息
                                const hasValidData = checkImportedDataValidity(content);
                                if (!hasValidData) {
                                    ElMessage.warning('导入的数据可能缺少角色信息，请检查格式');
                                } else {
                                    ElMessage.success('导入成功，请点击处理数据按钮完成处理');
                                }
                            })
                            .catch(err => {
                                console.error('读取文件内容失败：', err);
                                ElMessage.error('读取文件内容失败');
                            });
                    } else {
                        ElMessage.error(response.msg || '文件上传失败');
                    }
                };
                
                // 检查导入数据的有效性
                const checkImportedDataValidity = (content) => {
                    if (!content || content.trim() === '') return false;
                    
                    // 检查是否包含角色昵称信息
                    return content.includes('角色昵称');
                };
                
                // 格式化导入的文本
                const formatImportedText = (text) => {
                    if (!text) return text;
                    
                    // 标准化换行符
                    text = text.replace(/\r\n/g, "\n").replace(/\r/g, "\n");
                    
                    // 处理以管道符分隔的格式
                    const lines = text.split("\n");
                    let formattedLines = [];
                    let currentLine = "";
                    
                    for (let i = 0; i < lines.length; i++) {
                        const line = lines[i].trim();
                        
                        // 处理空行
                        if (line === "") continue;
                        
                        // 检查是否是新角色的开始（包含"角色昵称"）
                        if (line.includes("角色昵称") && currentLine !== "") {
                            formattedLines.push(currentLine);
                            currentLine = line;
                        } 
                        // 检查是否是回调数据（_Callback部分）
                        else if (line.includes("_Callback")) {
                            // 确保_Callback直接跟在当前行后面，用空格连接
                            if (currentLine !== "") {
                                // 检查当前行最后是否有"M"但没有空格
                                if (/[0-9.]+[Mm]$/.test(currentLine)) {
                                    currentLine += " ";  // 在M后添加空格
                                }
                                currentLine += " " + line;
                            } else {
                                currentLine = line;
                            }
                        }
                        // 处理常规字段行
                        else {
                            // 检查是否应该用管道符连接
                            if (currentLine === "") {
                                currentLine = line;
                            } else if (line.includes(":") || line.includes("：")) {
                                // 如果是键值对格式，用管道符连接
                                currentLine += "|" + line;
                            } else {
                                // 如果不是键值对，可能是上一行的继续，直接连接
                                currentLine += " " + line;
                            }
                        }
                    }
                    
                    // 添加最后处理的行
                    if (currentLine !== "") {
                        formattedLines.push(currentLine);
                    }
                    
                    // 对每一行做最后的处理，确保"M"后面有空格
                    formattedLines = formattedLines.map(line => {
                        // 找到所有的"数字+M"模式，确保M后面有空格
                        return line.replace(/([0-9.]+[Mm])(?![\s|])/g, '$1 ');
                    });
                    
                    return formattedLines.join("\n");
                };
                
                // 文件上传错误处理
                const handleFileUploadError = (err) => {
                    uploadLoading.value = false;
                    console.error('文件上传失败：', err);
                    ElMessage.error('文件上传失败，请稍后重试');
                };
                
                // 上传处理数据
                const handleUpload = async (dataType) => {
                    // 检查表单数据是否为空
                    if (!uploadForm.userData.trim()) {
                        ElMessage.warning('请输入用户数据');
                        return;
                    }
                    
                    // 防止重复提交检查
                    const currentTime = new Date().getTime();
                    const timeDiff = currentTime - uploadForm.lastSubmitTime;
                    const dataToSubmit = uploadForm.userData.trim();
                    
                    // 检查是否有重复的角色昵称
                    const duplicateNicknames = checkDuplicateNicknames(dataToSubmit);
                    if (duplicateNicknames.length > 0) {
                        ElMessage({
                            message: `数据中存在重复角色昵称: ${duplicateNicknames.join(', ')}，系统将自动处理`,
                            type: 'warning',
                            duration: 5000
                        });
                    }
                    
                    // 如果数据相同且时间间隔小于3秒，阻止提交
                    if (dataToSubmit === uploadForm.lastSubmitData.trim() && timeDiff < 3000) {
                        ElMessage.warning('请勿重复提交相同数据，请稍等片刻');
                        return;
                    }
                    
                    // 处理上传数据
                    processUploadData(dataType, dataToSubmit, currentTime);
                };
                
                // 处理上传数据（提取公共代码）
                const processUploadData = async (dataType, dataToSubmit, currentTime) => {
                    try {
                        // 记录当前提交的数据和时间
                        uploadForm.lastSubmitData = dataToSubmit;
                        uploadForm.lastSubmitTime = currentTime;
                        
                        // 添加日志
                        console.log('提交时间:', new Date().toLocaleString());
                        console.log('数据长度:', dataToSubmit.length);
                        
                        // 设置上传状态
                        uploadLoading.value = true;
                        
                        // 设置请求头，确保正确的Content-Type
                        const config = {
                            headers: {
                                'Content-Type': 'application/json',
                                'X-Requested-With': 'XMLHttpRequest'
                            },
                            // 明确指定请求方法，防止被改变
                            method: 'POST',
                            // 添加错误处理
                            validateStatus: function (status) {
                                return status >= 200 && status < 600; // 允许处理500错误
                            }
                        };
                        
                        console.log('发送数据处理请求...');
                        
                        // 准备过滤字段
                        let filterFields = [];
                        if (uploadForm.enableFilter) {
                            // 添加预设的过滤字段
                            if (uploadForm.filterFields.loginToday) filterFields.push('今日登录');
                            if (uploadForm.filterFields.banned) filterFields.push('封号');
                            if (uploadForm.filterFields.muted) filterFields.push('禁言');
                            if (uploadForm.filterFields.lastLoginTime) filterFields.push('最后登录时间');
                            if (uploadForm.filterFields.lastLogoutTime) filterFields.push('最后退出间');
                            if (uploadForm.filterFields.onlineStatus) filterFields.push('在线状态');
                            
                            // 添加自定义过滤字段
                            if (uploadForm.customFilterFields.length > 0) {
                                filterFields = [...filterFields, ...uploadForm.customFilterFields];
                            }
                        }
                        
                        // 生成请求ID，用于标识本次请求
                        const requestId = 'req_' + new Date().getTime() + '_' + Math.floor(Math.random() * 1000);
                        
                        // 使用更直接的方式设置请求方法和头
                        const res = await axios({
                            method: 'POST',
                            url: '/plugin/Automaticloading/user/process_data',
                            data: { 
                                userData: dataToSubmit,
                                filterFields: filterFields,
                                enableFilter: uploadForm.enableFilter,
                                dataType: '', // 移除dataType参数，让后端自动判断
                                // 添加低等级角色处理相关参数
                                enableLowLevelProcess: uploadForm.enableLowLevelProcess,
                                lowLevelGoodsId: uploadForm.enableLowLevelProcess ? uploadForm.lowLevelGoodsId : '',
                                // 添加仓库价值范围处理相关参数
                                enableWarehouseValueProcess: uploadForm.enableWarehouseValueProcess,
                                warehouseValueGoodsId: uploadForm.enableWarehouseValueProcess ? uploadForm.warehouseValueGoodsId : '',
                                // 添加请求ID
                                requestId: requestId,
                                // 标记来源（手动输入或记事本导入）
                                source: dataType === 'import' ? 'notepad' : 'manual',
                                // 添加过滤历史成功数据选项
                                skipPreviousSuccess: uploadForm.skipPreviousSuccess
                            },
                            ...config
                        });
                        
                        console.log('处理响应:', res);
                        
                        // 检查响应状态
                        if (res.status === 500) {
                            // 处理500错误
                            const errorMsg = res.data.message || '服务器内部错误';
                            console.error('服务器错误:', res.data);
                            ElMessage.error(errorMsg);
                            return;
                        }
                        
                        // 处理所有类型的结果
                        processResult.value = res.data;
                        resultVisible.value = true;
                        
                        // 根据结果状态显示不同的消息
                        if (processResult.value.status === 'success') {
                            // 检查是否有重复数据
                            const hasDuplicates = processResult.value.details && 
                                processResult.value.details.some(item => item.status === 'warning');
                            
                            if (hasDuplicates) {
                                ElMessage({
                                    message: '处理完成，但存在重复数据',
                                    type: 'warning'
                                });
                            } else {
                                ElMessage.success(processResult.value.message);
                            }
                        } else if (processResult.value.status === 'warning') {
                            // 处理警告状态
                            ElMessage({
                                message: processResult.value.message || '检测到重复数据',
                                type: 'warning'
                            });
                        } else {
                            // 处理错误状态
                            ElMessage.error(processResult.value.message || '处理失败');
                        }
                    } catch (err) {
                        console.error('处理数据失败:', err);
                        let errorMsg = '处理数据失败，请稍后重试';
                        
                        // 尝试获取详细错误信息
                        if (err.response && err.response.data) {
                            console.error('错误详情:', err.response.data);
                            errorMsg = err.response.data.message || errorMsg;
                            
                            // 如果有详细的错误信息，显示在控制台
                            if (err.response.data.file) {
                                console.error('错误文件:', err.response.data.file);
                                console.error('错误行号:', err.response.data.line);
                                console.error('错误追踪:', err.response.data.trace);
                            }
                        }
                        
                        ElMessage.error(errorMsg);
                    } finally {
                        uploadLoading.value = false;
                    }
                };
                
                // 检查数据中是否有重复的角色昵称
                const checkDuplicateNicknames = (data) => {
                    if (!data) return [];
                    
                    const nicknames = [];
                    const duplicates = [];
                    
                    // 按行分割
                    const lines = data.split(/\n/);
                    for (let line of lines) {
                        if (!line.trim()) continue;
                        
                        // 尝试提取角色昵称
                        const nicknameMatch = line.match(/角色昵称[:|：]\s*([^|]*)/i);
                        if (nicknameMatch && nicknameMatch[1]) {
                            const nickname = nicknameMatch[1].trim();
                            if (!nickname) continue; // 空昵称跳过
                            
                            // 检查是否已存在相同昵称（不区分大小写）
                            const lowerNickname = nickname.toLowerCase();
                            let isDuplicate = false;
                            
                            for (let i = 0; i < nicknames.length; i++) {
                                if (nicknames[i].toLowerCase() === lowerNickname) {
                                    isDuplicate = true;
                                    // 只添加一次到重复列表
                                    if (!duplicates.some(dup => dup.toLowerCase() === lowerNickname)) {
                                        duplicates.push(nickname);
                                    }
                                    break;
                                }
                            }
                            
                            if (!isDuplicate) {
                                nicknames.push(nickname);
                            }
                        }
                    }
                    
                    return duplicates;
                };
                
                // 处理成功后的操作
                const afterProcessSuccess = () => {
                    // 关闭结果对话框
                    resultVisible.value = false;
                    // 清空表单
                    uploadForm.userData = '';
                    uploadForm.enableFilter = false;
                    uploadForm.filterFields = {
                        loginToday: false,
                        banned: false,
                        muted: false,
                        lastLoginTime: false,
                        lastLogoutTime: false,
                        onlineStatus: false
                    };
                    uploadForm.customFilterField = '';
                    uploadForm.customFilterFields = [];
                    uploadForm.enableLowLevelProcess = false;
                    uploadForm.lowLevelGoodsId = '';
                    uploadForm.enableWarehouseValueProcess = false;
                    uploadForm.warehouseValueGoodsId = '';
                    uploadForm.skipPreviousSuccess = false;
                };
                
                // 导出失败数据
                const exportFailedData = () => {
                    if (!processResult.value || !processResult.value.failed_data_id) {
                        ElMessage.error('导出失败：未找到失败数据ID');
                        return;
                    }
                    
                    const exportUrl = `/plugin/Automaticloading/user/exportFailedData?cache_id=${processResult.value.failed_data_id}`;
                    window.open(exportUrl, '_blank');
                };
                
                // 清除指定角色的缓存
                const clearRoleCache = async (nickname) => {
                    try {
                        if (!nickname) {
                            ElMessage.error('清除缓存失败：未提供角色昵称');
                            return;
                        }
                        
                        // 确认对话框
                        await ElMessageBox.confirm(
                            `确定要清除角色"${nickname}"的缓存记录吗？清除后再次提交此角色数据将被重新处理。`,
                            '确认操作',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning'
                            }
                        );
                        
                        // 获取商户ID - 多种方式尝试
                        let merchantId = '';
                        
                        // 1. 从URL参数获取
                        const urlParams = new URLSearchParams(window.location.search);
                        merchantId = urlParams.get('merchant_id');
                        
                        // 2. 如果URL中没有，则尝试从process_data提交过程中记录的用户ID获取
                        if (!merchantId && processResult.value && processResult.value.user_id) {
                            merchantId = processResult.value.user_id;
                        }
                        
                        // 3. 如果都没有，显示错误信息
                        if (!merchantId) {
                            ElMessage.error('清除缓存失败：无法获取商户ID，请刷新页面后重试');
                            return;
                        }
                        
                        // 构建请求URL
                        const url = `/plugin/Automaticloading/user/clearRoleCache?nickname=${encodeURIComponent(nickname)}&merchant_id=${merchantId}`;
                        
                        // 发送请求
                        const res = await axios.get(url);
                        
                        if (res.data.status === 'success') {
                            ElMessage.success(res.data.message);
                            // 刷新结果列表中的状态
                            if (processResult.value && processResult.value.details) {
                                processResult.value.details.forEach(item => {
                                    if (item.nickname === nickname) {
                                        item.previous_success = false;
                                        item.message = item.message.replace(/该角色已于.*成功处理，已自动跳过/, '已清除缓存记录');
                                    }
                                });
                            }
                        } else {
                            ElMessage.warning(res.data.message);
                        }
                    } catch (error) {
                        if (error === 'cancel') {
                            return;
                        }
                        console.error('清除角色缓存失败:', error);
                        ElMessage.error('清除角色缓存失败: ' + (error.response?.data?.message || error.message || '未知错误'));
                    }
                };
                
                // 清除所有角色缓存
                const clearAllRoleCache = async () => {
                    try {
                        // 确认对话框
                        await ElMessageBox.confirm(
                            '确定要清除所有角色的缓存记录吗？清除后所有角色数据都将被重新处理。',
                            '确认操作',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'danger'
                            }
                        );
                        
                        // 获取商户ID - 多种方式尝试
                        let merchantId = '';
                        
                        // 1. 从URL参数获取
                        const urlParams = new URLSearchParams(window.location.search);
                        merchantId = urlParams.get('merchant_id');
                        
                        // 2. 如果URL中没有，则尝试从process_data提交过程中记录的用户ID获取
                        if (!merchantId && processResult.value && processResult.value.user_id) {
                            merchantId = processResult.value.user_id;
                        }
                        
                        // 3. 如果都没有，显示错误信息
                        if (!merchantId) {
                            ElMessage.error('清除缓存失败：无法获取商户ID，请刷新页面后重试');
                            return;
                        }
                        
                        // 构建请求URL
                        const url = `/plugin/Automaticloading/user/clearAllRoleCache?merchant_id=${merchantId}`;
                        
                        // 发送请求
                        const res = await axios.get(url);
                        
                        if (res.data.status === 'success') {
                            ElMessage.success(res.data.message);
                            // 刷新结果列表中的状态
                            if (processResult.value && processResult.value.details) {
                                processResult.value.details.forEach(item => {
                                    if (item.previous_success) {
                                        item.previous_success = false;
                                        item.message = '已清除缓存记录';
                                    }
                                });
                            }
                        } else {
                            ElMessage.warning(res.data.message);
                        }
                    } catch (error) {
                        if (error === 'cancel') {
                            return;
                        }
                        console.error('清除所有角色缓存失败:', error);
                        ElMessage.error('清除所有角色缓存失败: ' + (error.response?.data?.message || error.message || '未知错误'));
                    }
                };
                
                // 清空表单
                const clearForm = () => {
                    uploadForm.userData = '';
                    uploadForm.enableFilter = false;
                    uploadForm.filterFields = {
                        loginToday: false,
                        banned: false,
                        muted: false,
                        lastLoginTime: false,
                        lastLogoutTime: false,
                        onlineStatus: false
                    };
                    uploadForm.customFilterField = '';
                    uploadForm.customFilterFields = [];
                    // 清空低等级角色处理相关字段
                    uploadForm.enableLowLevelProcess = false;
                    uploadForm.lowLevelGoodsId = '';
                    uploadForm.enableWarehouseValueProcess = false;
                    uploadForm.warehouseValueGoodsId = '';
                    uploadForm.skipPreviousSuccess = false;
                };
                
                // 格式化仓库价值显示
                const formatWarehouseValue = (value) => {
                    // 如果值不存在，返回未知
                    if (value === undefined || value === null) {
                        return '未知';
                    }
                    
                    // 检查是否已经是带M单位的字符串
                    if (typeof value === 'string' && value.toUpperCase().includes('M')) {
                        // 确保M后面有空格
                        if (/[0-9.]+M$/i.test(value)) {
                            return value + ' ';
                        } else if (/[0-9.]+M\s*$/i.test(value)) {
                            // 如果已经有M和空格，确保只有一个空格
                            return value.replace(/([0-9.]+M)\s*$/i, '$1 ');
                        }
                        return value; // 已经是其他M格式，直接返回
                    }
                    
                    // 尝试将值转换为数字
                    let numValue;
                    try {
                        numValue = parseFloat(value);
                    } catch (e) {
                        return value; // 转换失败，返回原值
                    }
                    
                    // 如果是有效数字且大于等于10万，转换为M格式
                    if (!isNaN(numValue) && numValue >= 100000) {
                        return (numValue / 1000000).toFixed(1) + 'M '; // 确保M后有空格
                    }
                    
                    // 返回原始值
                    return value;
                };
                
                // 添加自定义过滤字段
                const addCustomFilterField = () => {
                    if (uploadForm.customFilterField.trim()) {
                        uploadForm.customFilterFields.push(uploadForm.customFilterField);
                        uploadForm.customFilterField = '';
                    }
                };
                
                // 移除自定义过滤字段
                const removeCustomFilterField = (index) => {
                    uploadForm.customFilterFields.splice(index, 1);
                };
                
                // 获取之前成功处理的数据数量
                const getPreviouslySucceededCount = () => {
                    if (!processResult.value || !processResult.value.details) return 0;
                    return processResult.value.details.filter(item => item.previous_success === true).length;
                };
                
                // 页面加载时获取商品列表
                onMounted(() => {
                    loadUploadConfig();
                    loadGoodsList();
                });
                
                return {
                    // 表单数据
                    uploadForm,
                    // 标签页状态
                    activeTab,
                    // 标签页切换处理
                    handleTabChange,
                    // 商品列表
                    goodsList,
                    loadGoodsList,
                    // 上传处理
                    handleUpload,
                    handleFileUploadSuccess,
                    handleFileUploadError,
                    beforeFileUpload,
                    uploadHeaders,
                    uploadConfig,
                    loadUploadConfig,
                    uploadLoading,
                    // 结果处理
                    processResult,
                    resultVisible,
                    formatWarehouseValue,
                    // 按钮状态
                    isProcessButtonDisabled,
                    // 辅助方法
                    clearForm,
                    afterProcessSuccess,
                    // 自定义过滤字段
                    addCustomFilterField,
                    removeCustomFilterField,
                    // 获取历史成功处理数量
                    getPreviouslySucceededCount,
                    // 导出失败数据
                    exportFailedData,
                    // 清除缓存方法
                    clearRoleCache,
                    clearAllRoleCache
                };
            }
        }).use(ElementPlus, {
            locale: ElementPlusLocaleZhCn
        }).mount('#app');
    </script>
</body>
</html>
