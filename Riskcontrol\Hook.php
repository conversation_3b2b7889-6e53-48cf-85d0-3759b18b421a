<?php

namespace plugin\Riskcontrol;

use think\facade\Db;
use think\facade\Event;
use plugin\Riskcontrol\api\CloudApi;

class Hook
{
    public function handle()
    {
        try {
            // 检查是否启用
            if (intval(plugconf("Riskcontrol.status") ?? 0) !== 1) {
                return;
            }
            
            $this->checkGoods();
            
        } catch (\Exception $e) {
            error_log("Riskcontrol Hook error: " . $e->getMessage());
        }
    }
    
    public function checkGoods()
    {
        $checkedCount = 0;
        $violationCount = 0;
        $result = '';
        
        try {
            // 获取所有正常状态的商品
            $goods = Db::name('goods')
                ->where('verify', 1)  // 只检查正常状态的商品
                ->field(['id', 'name', 'description'])
                ->select();
                
            if ($goods->isEmpty()) {
                return "没有需要检测的商品";
            }
            
            foreach ($goods as $good) {
                $checkedCount++;
                $isViolation = false;
                $violationMsg = '';
                
                // 检测标题和描述
                if ($this->checkText($good['name'], $violationMsg) || 
                    $this->checkText($good['description'], $violationMsg)) {
                    $isViolation = true;
                    $violationCount++;
                    
                    // 更新商品状态
                    Db::name('goods')->where('id', $good['id'])->update([
                        'verify' => 3  // 违规时设置verify=3(下架)
                    ]);
                    
                    // 如果开启了管理员通知
                    if (intval(plugconf("Riskcontrol.notify_admin") ?? 0) === 1) {
                        $notifyContent = plugconf("Riskcontrol.notify_content") ?? '';
                        $notifyContent = str_replace('{id}', $good['id'], $notifyContent);
                        // TODO: 发送通知给管理员
                    }
                }
            }
            
            $result = "检测完成:\n";
            $result .= "共检测 {$checkedCount} 个商品\n";
            $result .= "发现 {$violationCount} 个违规商品";
            
            return $result;
            
        } catch (\Exception $e) {
            throw new \Exception("检测商品失败: " . $e->getMessage());
        }
    }
    
    // 检查文本内容
    private function checkText($text, &$violationMsg)
    {
        if (empty($text)) {
            return false;
        }
        
        try {
            // 处理HTML内容，去除HTML标签，只保留文本
            $text = strip_tags($text);
            // 转换HTML实体为普通字符
            $text = html_entity_decode($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');
            // 去除多余空白字符
            $text = preg_replace('/\s+/', ' ', trim($text));
            
            if (empty($text)) {
                return false;
            }
            
            // 阿里云检测
            if (intval(plugconf("Riskcontrol.aliyun_status")) === 1) {
                if (CloudApi::aliyunCheck($text, $msg)) {
                    $violationMsg = "阿里云检测: " . $msg;
                    return true;
                }
            }
            
            // 腾讯云检测
            if (intval(plugconf("Riskcontrol.tencent_status")) === 1) {
                if (CloudApi::tencentCheck($text, $msg)) {
                    $violationMsg = "腾讯云检测: " . $msg;
                    return true;
                }
            }
            
            // 百度云检测
            if (intval(plugconf("Riskcontrol.baidu_status")) === 1) {
                if (CloudApi::baiduCheck($text, $msg)) {
                    $violationMsg = "百度云检测: " . $msg;
                    return true;
                }
            }
            
            return false;
            
        } catch (\Exception $e) {
            error_log("Riskcontrol text check error: " . $e->getMessage());
            return false;
        }
    }

    // 商品更新前检查
    public function beforeGoodsUpdate($params)
    {
        try {
            // 检查是否启用
            if (intval(plugconf("Riskcontrol.status") ?? 0) !== 1) {
                return true;
            }

            $goodsData = $params['data'];
            $violationMsg = '';

            // 检查商品名称和描述
            if (!empty($goodsData['name']) && $this->checkText($goodsData['name'], $violationMsg)) {
                if (isset($goodsData['id'])) {
                    Db::name('goods')->where('id', $goodsData['id'])->update([
                        'verify' => 3  // 设置为下架状态
                    ]);
                }
                return [
                    'code' => 0,
                    'msg' => "商品名称存在违规内容: " . $violationMsg
                ];
            }

            if (!empty($goodsData['description']) && $this->checkText($goodsData['description'], $violationMsg)) {
                if (isset($goodsData['id'])) {
                    Db::name('goods')->where('id', $goodsData['id'])->update([
                        'verify' => 3  // 设置为下架状态
                    ]);
                }
                return [
                    'code' => 0,
                    'msg' => "商品描述存在违规内容: " . $violationMsg
                ];
            }

            return true;

        } catch (\Exception $e) {
            error_log("Riskcontrol beforeGoodsUpdate error: " . $e->getMessage());
            return true; // 发生错误时不阻止更新
        }
    }
} 