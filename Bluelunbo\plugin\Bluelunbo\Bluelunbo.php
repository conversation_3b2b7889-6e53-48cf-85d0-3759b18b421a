<?php

namespace plugin\Bluelunbo;

use app\common\library\Plugin;
use app\common\model\Template as TemplateModel;

class Bluelunbo extends Plugin {

    /**
     * 模板的相关的参数，必须设置
     */
    private $code = "Bluelunbo";
    private $platform = "pc";
    private $name = "蓝色轮播主题";

    /**
     * 插件安装方法，建议一模一样
     * @return bool
     */
    public function install() {
        $template = TemplateModel::where(['code' => $this->code])->find();
        if (!$template) {
            $template = new TemplateModel();
        }
        $template->platform = $this->platform;
        $template->code = $this->code;
        $template->name = $this->name;
        $template->save();

        return true;
    }

    /**
     * 插件卸载方法，建议一模一样
     * @return bool
     */
    public function uninstall() {
        $template = TemplateModel::where(['code' => $this->code])->find();
        if ($template) {
            $template->delete();
        }

        delDirs(root_path('app' . DIRECTORY_SEPARATOR . 'index' . DIRECTORY_SEPARATOR . $this->code));
        delDirs(root_path('public' . DIRECTORY_SEPARATOR . 'assets' . DIRECTORY_SEPARATOR . 'plugin' . DIRECTORY_SEPARATOR . $this->code));
        return true;
    }
}
