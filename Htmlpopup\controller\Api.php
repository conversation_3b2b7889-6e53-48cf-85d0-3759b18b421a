<?php

namespace plugin\Htmlpopup\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;

class Api extends BasePlugin {

    protected $noNeedLogin = ['fetchData'];

    // 添加场景配置
    protected $scene = [
        'user'
    ];

    public function index() {
        // 删除所有编辑器配置，直接返回视图
        return View::fetch();
    }

    public function fetchData() {
        try {
            // 添加防止缓存的响应头
            header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
            header('Cache-Control: post-check=0, pre-check=0', false);
            header('Pragma: no-cache');

            // 获取请求参数中的商家信息
            $shop_name = $this->request->param('shop_name', '', 'trim');
            $request_merchant_id = $this->request->param('merchant_id', 0, 'intval');
            $merchant_id = 0;

            // 优先使用URL请求参数中的商家ID
            if ($request_merchant_id > 0) {
                $merchant_id = $request_merchant_id;
            }
            // 其次通过店铺名称查找商家ID
            else if (!empty($shop_name)) {
                $merchant_id = $this->getMerchantIdByShopName($shop_name);
            }
            // 最后才使用当前登录用户的ID
            else if ($this->user && $this->user->id) {
                $merchant_id = $this->user->id;
            }

            if (!$merchant_id) {
                return json(['code' => 0, 'msg' => '未找到商家信息']);
            }

            // 检查全局弹窗开关
            $globalPopupEnabled = (bool)(plugconf('Htmlpopup.global_popup_enabled') ?? true);
            if (!$globalPopupEnabled) {
                return json(['code' => 200, 'msg' => 'success', 'data' => ['status' => 0]]);
            }

            // 获取商家的内容配置
            $contentType = merchant_plugconf($merchant_id, "Htmlpopup.content_type");
            $builtinTemplate = merchant_plugconf($merchant_id, "Htmlpopup.builtin_template");
            $customContent = merchant_plugconf($merchant_id, "Htmlpopup.content") ?? '';

            // 检查商家是否已经配置过弹窗内容
            $hasConfigured = !empty($contentType) || !empty($builtinTemplate) || !empty($customContent);

            if (!$hasConfigured) {
                // 商家没有配置任何内容，自动使用管理员设置的默认模板
                $contentType = 'builtin';
                $builtinTemplate = plugconf('Htmlpopup.global_default_template') ?? 'purchase_agreement';


            } else {
                // 商家已配置，但可能需要补充默认值
                if (empty($contentType)) {
                    $contentType = 'custom';
                }

                // 如果选择了内置模板模式但没有指定模板，使用默认模板
                if ($contentType === 'builtin' && empty($builtinTemplate)) {
                    $builtinTemplate = plugconf('Htmlpopup.global_default_template') ?? 'purchase_agreement';
                }
            }

            // 从全局配置获取弹窗设置
            $params = [
                'status' => 1, // 状态由全局开关控制，这里固定为1
                'content' => $customContent,
                'frequency' => plugconf('Htmlpopup.global_frequency') ?? 'once',
                'title' => plugconf('Htmlpopup.global_title') ?? 'HTML弹窗',
                'button_color' => plugconf('Htmlpopup.global_button_color') ?? 'blue',
                'reject_action' => plugconf('Htmlpopup.global_reject_action') ?? 'none',
                'reject_url' => plugconf('Htmlpopup.global_reject_url') ?? '',
                'close_confirm' => intval(plugconf('Htmlpopup.global_close_confirm') ?? 0),
                'content_type' => $contentType,
                'builtin_template' => $builtinTemplate,
                'template_fields' => merchant_plugconf($merchant_id, "Htmlpopup.template_fields") ?? '',
                'enable_scrollbar' => intval(plugconf('Htmlpopup.enable_scrollbar') ?? 0),
                'enable_agree_delay' => (bool)(plugconf('Htmlpopup.enable_agree_delay') ?? false),
                'agree_delay_seconds' => intval(plugconf('Htmlpopup.agree_delay_seconds') ?? 5)
            ];

            // 如果是内置模板，使用Admin配置的模板文本生成内容
            if ($contentType === 'builtin') {
                // 获取Admin界面保存的模板文本配置
                $templateTexts = plugconf('Htmlpopup.template_texts');

                if ($templateTexts && is_array($templateTexts)) {
                    // 使用Admin配置的文本生成模板内容
                    $params['content'] = $this->generateTemplateFromTexts($templateTexts);
                } else {
                    // 如果没有Admin配置，使用旧的内置模板逻辑
                    $templates = $this->getBuiltinTemplates();
                    if (isset($templates[$builtinTemplate])) {
                        $params['builtin_template_content'] = $templates[$builtinTemplate]['content'];
                    }
                }
            }

            return json(['code' => 200, 'msg' => 'success', 'data' => $params]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取数据失败']);
        }
    }

    // 添加通过店铺名称查找商家ID的方法
    protected function getMerchantIdByShopName($shop_name) {
        if (empty($shop_name)) {
            return 0;
        }

        try {
            // 使用与 Storeannouncements 相同的查询逻辑
            $merchant = \think\facade\Db::name('user')
                ->where('nickname', $shop_name)
                ->value('id');

            return intval($merchant);
        } catch (\Exception $e) {
            return 0;
        }
    }

    // 保存功能已移除 - API端只提供预览功能，所有配置请在管理后台进行



    /**
     * 获取内置模板内容
     */
    private function getBuiltinTemplateContent($template, $template_fields = '') {
        // 解析用户自定义字段
        $fields = [];
        if (!empty($template_fields)) {
            $fields = json_decode($template_fields, true) ?: [];
        }

        // 设置默认字段值
        $defaultFields = [
            // 购买协议模板字段
            'platformName' => '小火羊云寄售官方频道',
            'platformChannel' => '@xhyfkw',
            'warmTip' => '本站不提供任何担保、私下交易被骗一律与本站无关。',
            'shopLink' => 'https://www.zzrongtong.cn/merchant/',
            'serviceLink' => 'https://work.weixin.qq.com/kfid/kfcf72f7282e80c5ad2',
            'shopButtonText' => '开店成为商家赚米',
            'serviceButtonText' => '联系平台微信客服',
            // 平台规则模板字段
            'rulesTitle' => '平台使用规则',
            'userNotice' => "1. 请遵守平台相关规定，文明使用平台服务\n2. 禁止发布违法违规内容，维护良好的平台环境\n3. 保护个人隐私信息，谨防诈骗",
            'tradeRules' => "1. 所有交易请通过平台正规渠道进行\n2. 如遇问题请及时联系客服处理\n3. 平台将保障用户合法权益",
            'serviceNotice' => '如有疑问，请联系平台客服'
        ];

        // 合并用户字段和默认字段
        $fields = array_merge($defaultFields, $fields);

        // 获取管理员配置的模板
        $adminTemplates = $this->getBuiltinTemplates();

        // 如果是管理员自定义的模板，直接返回其内容（不支持字段替换）
        if (isset($adminTemplates[$template]) && !in_array($template, ['purchase_agreement', 'platform_rules'])) {
            return $adminTemplates[$template]['content'];
        }

        // 根据模板类型生成内容（仅对内置的两个模板支持字段替换）
        if ($template === 'purchase_agreement') {
            return '
                <div id="buy-protocol">
                    <p><strong><span style="color:#303133;"><span style="font-size:18px;">' . htmlspecialchars($fields['platformName']) . htmlspecialchars($fields['platformChannel']) . '</span></span></strong></p>
                    <p><strong><span style="color:#ff6600;"><span style="font-size:18px;">温馨提示：' . htmlspecialchars($fields['warmTip']) . '</span></span></strong></p>
                    <p><span style="color:#ff00ff;">1、本平台仅提供自动发卡、寄售服务，本平台非售卖销售商，有问题联系店铺客服或者平台投诉订单处理，非商品卡密问题本站不予受理售后争议</span></p>
                    <p><span style="color:#27ae60;">2、平台提供卡密查询时间为1个月（购买后请自行保存）如遇假卡/欺诈/涉黄赌/等违规商品请当天23点50前去平台投诉订单处理，若是超过24点后，平台放款给商家，不再受理任何问题。</span></p>
                    <p><span style="color:#ae2760;">3、为了方便售后建议您的每次购物都录个视频，一直到商品正常使用没问题在取消录制哦！</span></p>
                    <p><span style="color:#ff0000;">若遇到任何无法处理的问题，请当天在平台投诉订单，平台官方客服介入处理</span></p>
                    <div style="text-align: center; white-space: nowrap;">
                        <a href="' . htmlspecialchars($fields['shopLink']) . '" style="display: inline-block; width: 45%; background-color: #6600ff; color: white; border: none; padding: 10px; margin: 0 2.5%; border-radius: 5px; text-decoration: none; cursor: pointer;">' . htmlspecialchars($fields['shopButtonText']) . '</a>
                        <a href="' . htmlspecialchars($fields['serviceLink']) . '" style="display: inline-block; width: 45%; background-color: #6600ff; color: white; border: none; padding: 10px; margin: 0 2.5%; border-radius: 5px; text-decoration: none; cursor: pointer;">' . htmlspecialchars($fields['serviceButtonText']) . '</a>
                    </div>
                    <hr style="border: none; border-top: 1px dashed #000; margin: 20px 0;">
                    <p><span style="font-size:16px;"><strong><span style="color:#ff0000;">防骗提醒</span></strong><span style="color:#ff0000;">：</span></span><span style="color:#6600ff;">1.卡密内容为"联系QQ取卡或者QQ群拿货" 2.以各种理由推脱到第二天发货 3.商品有问题，卖家不售后 4.承诺充值返现 5.购买的商品为实物，需要快递发货的。请及时联系我们平台客服</span></p>
                </div>
            ';
        } else if ($template === 'platform_rules') {
            // 处理用户须知和交易规则的换行
            $userNoticeLines = explode("\n", $fields['userNotice']);
            $userNoticeHtml = '';
            foreach ($userNoticeLines as $index => $line) {
                $line = trim($line);
                if (!empty($line)) {
                    $line = preg_replace('/^\d+\.\s*/', '', $line); // 移除开头的数字
                    $userNoticeHtml .= '<p>' . ($index + 1) . '. ' . htmlspecialchars($line) . '</p>';
                }
            }

            $tradeRulesLines = explode("\n", $fields['tradeRules']);
            $tradeRulesHtml = '';
            foreach ($tradeRulesLines as $index => $line) {
                $line = trim($line);
                if (!empty($line)) {
                    $line = preg_replace('/^\d+\.\s*/', '', $line); // 移除开头的数字
                    $tradeRulesHtml .= '<p>' . ($index + 1) . '. ' . htmlspecialchars($line) . '</p>';
                }
            }

            return '
                <div style="padding: 20px; line-height: 1.6;">
                    <h3 style="color: #333; text-align: center; margin-bottom: 20px;">' . htmlspecialchars($fields['rulesTitle']) . '</h3>
                    <div style="background: #f0f8ff; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                        <h4 style="color: #1e90ff; margin-top: 0;">用户须知</h4>
                        ' . $userNoticeHtml . '
                    </div>
                    <div style="background: #fff5ee; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                        <h4 style="color: #ff6347; margin-top: 0;">交易规则</h4>
                        ' . $tradeRulesHtml . '
                    </div>
                    <div style="text-align: center; margin-top: 20px;">
                        <p style="color: #666;">' . htmlspecialchars($fields['serviceNotice']) . '</p>
                    </div>
                </div>
            ';
        }

        // 如果没有找到指定模板，返回默认模板
        $defaultTemplates = $this->getDefaultTemplates();
        return $defaultTemplates[$template]['content'] ?? $defaultTemplates['purchase_agreement']['content'];
    }

    /**
     * 获取管理员配置的内置模板
     */
    private function getBuiltinTemplates() {
        $templatesJson = plugconf('Htmlpopup.builtin_templates');

        if (empty($templatesJson)) {
            // 返回默认模板
            return $this->getDefaultTemplates();
        }

        // 解码JSON数据
        $templates = json_decode($templatesJson, true);

        // 检查JSON解码是否成功
        if (json_last_error() !== JSON_ERROR_NONE) {
            // JSON解码失败，返回默认模板
            return $this->getDefaultTemplates();
        }

        // 确保返回的是数组
        if (!is_array($templates)) {
            return $this->getDefaultTemplates();
        }
        
        // 确保每个模板的内容是正确的HTML
        foreach ($templates as $key => &$template) {
            if (isset($template['content']) && !empty($template['content'])) {
                // 确保内容是完整的HTML结构
                if (!$this->hasCompleteHtmlStructure($template['content'])) {
                    $template['content'] = $this->ensureCompleteHtmlStructure($template['content']);
                }
            } else {
                // 如果模板内容为空，使用默认模板内容
                $defaultTemplates = $this->getDefaultTemplates();
                if (isset($defaultTemplates[$key])) {
                    $template['content'] = $defaultTemplates[$key]['content'];
                }
            }
        }

        return $templates;
    }
    
    /**
     * 检查HTML内容是否包含完整的HTML结构
     */
    private function hasCompleteHtmlStructure($html) {
        // 检查是否包含<html>和<body>标签
        return (stripos($html, '<html') !== false || stripos($html, '<!doctype') !== false) && 
               stripos($html, '<body') !== false && 
               stripos($html, '<style') !== false;
    }
    
    /**
     * 确保HTML内容有完整的HTML结构
     */
    private function ensureCompleteHtmlStructure($content) {
        // 如果内容已经包含了完整的HTML结构，直接返回
        if ($this->hasCompleteHtmlStructure($content)) {
            return $content;
        }
        
        // 分离样式和内容
        $styleContent = '';
        $bodyContent = $content;
        
        // 提取<style>标签内容
        if (preg_match('/<style[^>]*>(.*?)<\/style>/is', $content, $matches)) {
            $styleContent = $matches[0]; // 完整的style标签
            $bodyContent = preg_replace('/<style[^>]*>.*?<\/style>/is', '', $content);
        }
        
        // 构建完整的HTML结构
        $completeHtml = "<!DOCTYPE html>\n<html>\n<head>\n";
        
        // 添加样式
        if (!empty($styleContent)) {
            $completeHtml .= $styleContent . "\n";
        } else {
            $completeHtml .= "<style>\n/* 默认样式 */\nbody { font-family: Arial, sans-serif; }\n</style>\n";
        }
        
        $completeHtml .= "</head>\n<body>\n";
        
        // 添加主体内容
        if (stripos($bodyContent, '<body') !== false) {
            // 如果内容中已有<body>标签，提取其中的内容
            if (preg_match('/<body[^>]*>(.*?)<\/body>/is', $bodyContent, $matches)) {
                $bodyContent = $matches[1];
            }
        }
        
        $completeHtml .= $bodyContent;
        
        // 关闭标签
        if (!preg_match('/<\/body>\s*$/i', $bodyContent)) {
            $completeHtml .= "\n</body>\n</html>";
        } else if (!preg_match('/<\/html>\s*$/i', $bodyContent)) {
            $completeHtml .= "\n</html>";
        }
        
        return $completeHtml;
    }

    /**
     * 获取默认模板（用于初始化或备用）
     */
    private function getDefaultTemplates() {
        return [
            'purchase_agreement' => [
                'name' => '购买协议模板',
                'content' => '
                    <div id="buy-protocol">
                        <p><strong><span style="color:#303133;"><span style="font-size:18px;">小火羊云寄售官方频道@xhyfkw</span></span></strong></p>
                        <p><strong><span style="color:#ff6600;"><span style="font-size:18px;">温馨提示：本站不提供任何担保、私下交易被骗一律与本站无关。</span></span></strong></p>
                        <p><span style="color:#ff00ff;">1、本平台仅提供自动发卡、寄售服务，本平台非售卖销售商，有问题联系店铺客服或者平台投诉订单处理，非商品卡密问题本站不予受理售后争议</span></p>
                        <p><span style="color:#27ae60;">2、平台提供卡密查询时间为1个月（购买后请自行保存）如遇假卡/欺诈/涉黄赌/等违规商品请当天23点50前去平台投诉订单处理，若是超过24点后，平台放款给商家，不再受理任何问题。</span></p>
                        <p><span style="color:#ae2760;">3、为了方便售后建议您的每次购物都录个视频，一直到商品正常使用没问题在取消录制哦！</span></p>
                        <p><span style="color:#ff0000;">若遇到任何无法处理的问题，请当天在平台投诉订单，平台官方客服介入处理</span></p>
                        <div style="text-align: center; white-space: nowrap;">
                            <a href="https://www.zzrongtong.cn/merchant/" style="display: inline-block; width: 45%; background-color: #6600ff; color: white; border: none; padding: 10px; margin: 0 2.5%; border-radius: 5px; text-decoration: none; cursor: pointer;">开店成为商家赚米</a>
                            <a href="https://work.weixin.qq.com/kfid/kfcf72f7282e80c5ad2" style="display: inline-block; width: 45%; background-color: #6600ff; color: white; border: none; padding: 10px; margin: 0 2.5%; border-radius: 5px; text-decoration: none; cursor: pointer;">联系平台微信客服</a>
                        </div>
                        <hr style="border: none; border-top: 1px dashed #000; margin: 20px 0;">
                        <p><span style="font-size:16px;"><strong><span style="color:#ff0000;">防骗提醒</span></strong><span style="color:#ff0000;">：</span></span><span style="color:#6600ff;">1.卡密内容为"联系QQ取卡或者QQ群拿货" 2.以各种理由推脱到第二天发货 3.商品有问题，卖家不售后 4.承诺充值返现 5.购买的商品为实物，需要快递发货的。请及时联系我们平台客服</span></p>
                    </div>
                '
            ]
        ];
    }

    /**
     * 获取可用的内置模板列表（供前端选择使用）
     */
    public function getAvailableTemplates() {
        try {
            $templates = $this->getBuiltinTemplates();



            // 只返回模板的基本信息，不包含完整内容
            $templateList = [];
            foreach ($templates as $key => $template) {
                $templateList[] = [
                    'value' => $key,
                    'label' => $template['name']
                ];
            }

            return json(['code' => 200, 'data' => $templateList]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取模板列表失败']);
        }
    }

    /**
     * 获取特定内置模板的内容（API接口）
     */
    public function getTemplateContent() {
        try {
            $templateKey = input('templateKey');
            if (empty($templateKey)) {
                return json(['code' => 0, 'msg' => '模板标识不能为空']);
            }

            $templates = $this->getBuiltinTemplates();

            if (!isset($templates[$templateKey])) {
                return json(['code' => 0, 'msg' => '模板不存在']);
            }

            return json([
                'code' => 200,
                'data' => [
                    'key' => $templateKey,
                    'name' => $templates[$templateKey]['name'],
                    'content' => $templates[$templateKey]['content']
                ]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取模板内容失败']);
        }
    }

    /**
     * 根据Admin配置的模板文本生成完整的HTML模板
     */
    private function generateTemplateFromTexts($templateTexts) {
        // 设置默认值
        $defaults = [
            'logoUrl' => 'https://shop.xhyfaka.com/xhylogo.gif',
            'platformName' => '小火羊云寄售官方频道',
            'platformChannel' => '@xhyfkw',
            'warmTip' => '本站不提供任何担保，私下交易被骗一律与本站无关',
            'shopLink' => 'https://shop.xhyfaka.com/merchant/',
            'shopButtonText' => '注册商家同款店铺',
            'groupLink' => 'https://shop.xhyfaka.com/35c378d8-590b-45a1-8767-18e34b083da5.png',
            'groupButtonText' => '本平台商家通知群',
            'serviceLink' => 'https://shop.xhyfaka.com/kf.html',
            'serviceButtonText' => '联系平台在线客服',
            'customerServiceNotice' => '注意：平台客服不等于商家客服，有任何问题请优先联系商家客服',
            'notice1' => '1. 本平台仅提供发卡服务，本平台非销售商，非卡密问题本站不予受理售后争议。',
            'notice2' => '2. 平台提供卡密查询时间为10天（购买后请自行保存），如遇违反中华人民共和国相关法律的违规商品，请当天24点前与我们平台客服联系举报。',
            'notice3' => '3. 退货退款：虚拟产品具有可复制性，付款后不能正常使用时,不支持退货退款。您可与商家进行咨询',
            'warningTitle' => '防骗提醒',
            'warning1' => '警惕脱离平台交易：遇到商品提示"联系QQ取卡"、"TG群拿货"等脱离正规交易平台的信息，切勿轻信。',
            'warning2' => '留意发货拖延借口：若卖家以各种理由推脱，声称要到第二天发货，需提高警惕。',
            'warning3' => '关注售后保障问题：当商品出现问题，卖家却拒绝提供售后服务，不处理退换货等事宜，此情况可疑。',
            'warning4' => '小心充值返现陷阱：对于承诺充值返现的情况，不要被高额返利诱惑。',
            'warning5' => '核实实物快递情况：购买实物商品时，若未按正常流程安排快递发货，应及时联系平台客服投诉。',
            'footerText' => '请谨慎交易，保护好自己的财产安全'
        ];

        // 合并默认值和用户配置
        $texts = array_merge($defaults, $templateTexts);

        // 生成完整的HTML模板
        $html = '<div style="font-family:Microsoft YaHei,sans-serif;max-width:600px;margin:0 auto;background:white;border-radius:12px;overflow:hidden;box-shadow:0 6px 18px rgba(0,0,0,0.08)">';

        // 头部区域
        $html .= '<div style="background:linear-gradient(135deg,#6e8efb,#a777e3);color:white;padding:20px;text-align:center">';
        $html .= '<img src="' . htmlspecialchars($texts['logoUrl'], ENT_NOQUOTES, 'UTF-8') . '" alt="平台Logo" style="width:180px;height:60px;">';
        $html .= '</div>';

        // 内容区域
        $html .= '<div style="padding:20px">';

        // 温馨提示
        $html .= '<div style="background-color:#fff8e1;border-left:4px solid #ffc107;padding:12px;margin-bottom:20px;border-radius:0 4px 4px 0;font-weight:500;color:#e65100">';
        $html .= htmlspecialchars($texts['warmTip'], ENT_NOQUOTES, 'UTF-8') . ' 频道：' . htmlspecialchars($texts['platformChannel'], ENT_NOQUOTES, 'UTF-8');
        $html .= '</div>';

       // 按钮区域
        $html .= '<a href="' . htmlspecialchars($texts['shopLink'], ENT_NOQUOTES, 'UTF-8') . '" style="display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,#6e8efb,#a777e3);color:white;border:none;padding:12px;margin:12px 0;border-radius:8px;text-decoration:none;font-weight:500;transition:all 0.3s ease;box-shadow:0 2px 5px rgba(0,0,0,0.1)">';
        $html .= '<svg t="1744595050613" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1316" width="24" height="24" style="margin-right:8px;"><path d="M670.6176 715.2128m-179.0976 0a179.0976 179.0976 0 1 0 358.1952 0 179.0976 179.0976 0 1 0-358.1952 0Z" fill="#FFBE0A" p-id="1317"></path><path d="M695.1424 311.7056a219.7504 219.7504 0 1 0-356.1472 172.0832 362.8032 362.8032 0 0 0-232.8064 338.2272 75.9808 75.9808 0 0 0 75.9296 75.9296h260.5568a35.84 35.84 0 0 0 0-71.68H182.1184a4.2496 4.2496 0 0 1-4.2496-4.2496 290.9184 290.9184 0 0 1 290.56-290.56h13.9776a29.8496 29.8496 0 0 0 4.3008-0.3072 220.16 220.16 0 0 0 208.4352-219.4432zM475.4432 459.776a148.0704 148.0704 0 1 1 148.0192-148.0704A148.224 148.224 0 0 1 475.4432 459.776zM714.2912 833.2288a35.84 35.84 0 0 1-24.5248-9.728L603.4944 742.4a35.84 35.84 0 1 1 49.1008-52.224l58.1632 54.6304L845.312 578.56a35.84 35.84 0 0 1 55.808 45.312l-158.72 196.096a35.84 35.84 0 0 1-25.6 13.1584 19.712 19.712 0 0 1-2.5088 0.1024z" fill="#34332E" p-id="1318"></path></svg>';
        $html .= htmlspecialchars($texts['shopButtonText'], ENT_NOQUOTES, 'UTF-8');
        $html .= '</a>';

        $html .= '<a href="' . htmlspecialchars($texts['groupLink'], ENT_NOQUOTES, 'UTF-8') . '" style="display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,#6e8efb,#a777e3);color:white;border:none;padding:12px;margin:12px 0;border-radius:8px;text-decoration:none;font-weight:500;transition:all 0.3s ease;box-shadow:0 2px 5px rgba(0,0,0,0.1)">';
        $html .= '<svg t="1744595015666" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1163" width="24" height="24" style="margin-right:8px;"><path d="M608.256 565.3504m-179.0976 0a179.0976 179.0976 0 1 0 358.1952 0 179.0976 179.0976 0 1 0-358.1952 0Z" fill="#FFBE0A" p-id="1164"></path><path d="M635.5968 921.6a132.5568 132.5568 0 0 1-117.4016-69.9392l-116.3264-210.4832-231.1168-103.1168a119.6544 119.6544 0 0 1 7.1168-221.44l552.96-205.3632a135.3216 135.3216 0 0 1 178.176 160.5632l-141.1584 547.84a132.8128 132.8128 0 0 1-113.9712 100.4544 144.6912 144.6912 0 0 1-18.2784 1.4848zM202.8544 384a47.9744 47.9744 0 0 0-2.8672 88.7808l242.0736 108.032a35.84 35.84 0 0 1 16.7424 15.36l122.112 220.8768a63.6928 63.6928 0 0 0 117.3504-14.9504l141.1072-547.84a63.6416 63.6416 0 0 0-83.8144-75.52L202.8032 384z" fill="#34332E" p-id="1165"></path><path d="M532.48 529.6128a35.84 35.84 0 0 1-25.6-60.9792l152.064-154.4704a35.84 35.84 0 1 1 51.2 50.2784L558.08 518.912a35.84 35.84 0 0 1-25.6 10.7008z" fill="#34332E" p-id="1166"></path></svg>';
        $html .= htmlspecialchars($texts['groupButtonText'], ENT_NOQUOTES, 'UTF-8');
        $html .= '</a>';

        $html .= '<a href="' . htmlspecialchars($texts['serviceLink'], ENT_NOQUOTES, 'UTF-8') . '" style="display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,#6e8efb,#a777e3);color:white;border:none;padding:12px;margin:12px 0;border-radius:8px;text-decoration:none;font-weight:500;transition:all 0.3s ease;box-shadow:0 2px 5px rgba(0,0,0,0.1)">';
        $html .= '<svg t="1744594983943" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1010" width="24" height="24" style="margin-right:8px;"><path d="M512 533.1968m-184.2176 0a184.2176 184.2176 0 1 0 368.4352 0 184.2176 184.2176 0 1 0-368.4352 0Z" fill="#FFBE0A" p-id="1011"></path><path d="M914.1248 754.0736A249.2928 249.2928 0 0 0 832.4608 445.44a34.048 34.048 0 0 0-2.8672-1.8432 366.6944 366.6944 0 0 0-377.088-333.8752 366.7456 366.7456 0 0 0-317.44 528.0256c-14.3872 53.4528-30.2592 106.5984-41.4208 142.9504a67.328 67.328 0 0 0 83.3024 84.224l156.5696-46.08a367.0016 367.0016 0 0 0 192.1536 18.8416A249.0368 249.0368 0 0 0 773.12 883.2l79.5648 23.3984a66.56 66.56 0 0 0 18.6368 2.7136 65.9968 65.9968 0 0 0 62.976-84.992c-7.5776-25.1392-14.336-48.7424-20.1728-70.2464z m-443.904 17.1008a290.6624 290.6624 0 0 1-120.832-23.1936 36.1472 36.1472 0 0 0-24.1152-1.3824L164.6592 793.6c12.1344-39.68 28.7232-95.8464 43.264-151.04a35.84 35.84 0 0 0-3.1232-26.2144 295.168 295.168 0 0 1 249.9584-435.2 295.0144 295.0144 0 1 1 15.36 589.824z m374.2208-38.0928a35.84 35.84 0 0 0-3.1232 26.2144c6.0416 22.9376 13.2096 48.2816 21.2992 75.5712l-81.2544-23.8592a35.84 35.84 0 0 0-24.1152 1.3824 176.9984 176.9984 0 0 1-142.4896-1.9456A369.3056 369.3056 0 0 0 721.92 736.8704a365.4144 365.4144 0 0 0 104.3456-199.68 176.9984 176.9984 0 0 1 18.432 196.1472z" fill="#34332E" p-id="1012"></path><path d="M331.1616 512.7168a35.84 35.84 0 0 1-35.84-35.84v-42.1376a35.84 35.84 0 1 1 71.68 0v42.1376a35.84 35.84 0 0 1-35.84 35.84zM569.7536 512.7168a35.84 35.84 0 0 1-35.84-35.84v-42.1376a35.84 35.84 0 0 1 71.68 0v42.1376a35.84 35.84 0 0 1-35.84 35.84z" fill="#34332E" p-id="1013"></path></svg>';
        $html .= htmlspecialchars($texts['serviceButtonText'], ENT_NOQUOTES, 'UTF-8');
        $html .= '</a>';

        // 客服提醒
        $html .= '<div style="background-color:#ffebee;border-left:4px solid #f44336;padding:12px;margin:15px 0;border-radius:0 4px 4px 0;font-size:15px;line-height:1.5">';
        $html .= htmlspecialchars($texts['customerServiceNotice'], ENT_NOQUOTES, 'UTF-8');
        $html .= '</div>';

        // 服务条款
        $html .= '<div style="background-color:#f3e5f5;border-left:4px solid #9c27b0;padding:12px;margin:15px 0;border-radius:0 4px 4px 0;font-size:15px;line-height:1.5">';
        $html .= htmlspecialchars($texts['notice1'], ENT_NOQUOTES, 'UTF-8');
        $html .= '</div>';

        $html .= '<div style="background-color:#f3e5f5;border-left:4px solid #9c27b0;padding:12px;margin:15px 0;border-radius:0 4px 4px 0;font-size:15px;line-height:1.5">';
        $html .= htmlspecialchars($texts['notice2'], ENT_NOQUOTES, 'UTF-8');
        $html .= '</div>';

        $html .= '<div style="background-color:#f3e5f5;border-left:4px solid #9c27b0;padding:12px;margin:15px 0;border-radius:0 4px 4px 0;font-size:15px;line-height:1.5">';
        $html .= htmlspecialchars($texts['notice3'], ENT_NOQUOTES, 'UTF-8');
        $html .= '</div>';

        // 防骗提醒区域
        $html .= '<div style="background-color:#ffebee;border-radius:8px;padding:15px;margin-top:20px">';
        $html .= '<div style="color:#d32f2f;font-weight:600;margin-bottom:10px;display:flex;align-items:center">';
        $html .= '⚠️ ' . htmlspecialchars($texts['warningTitle'], ENT_NOQUOTES, 'UTF-8');
        $html .= '</div>';

        $html .= '<div style="margin-bottom:10px;font-size:14px;line-height:1.5;position:relative;padding-left:20px">';
        $html .= '<span style="color:#d32f2f;font-weight:bold;position:absolute;left:0">•</span>';
        $html .= htmlspecialchars($texts['warning1'], ENT_NOQUOTES, 'UTF-8');
        $html .= '</div>';

        $html .= '<div style="margin-bottom:10px;font-size:14px;line-height:1.5;position:relative;padding-left:20px">';
        $html .= '<span style="color:#d32f2f;font-weight:bold;position:absolute;left:0">•</span>';
        $html .= htmlspecialchars($texts['warning2'], ENT_NOQUOTES, 'UTF-8');
        $html .= '</div>';

        $html .= '<div style="margin-bottom:10px;font-size:14px;line-height:1.5;position:relative;padding-left:20px">';
        $html .= '<span style="color:#d32f2f;font-weight:bold;position:absolute;left:0">•</span>';
        $html .= htmlspecialchars($texts['warning3'], ENT_NOQUOTES, 'UTF-8');
        $html .= '</div>';

        $html .= '<div style="margin-bottom:10px;font-size:14px;line-height:1.5;position:relative;padding-left:20px">';
        $html .= '<span style="color:#d32f2f;font-weight:bold;position:absolute;left:0">•</span>';
        $html .= htmlspecialchars($texts['warning4'], ENT_NOQUOTES, 'UTF-8');
        $html .= '</div>';

        $html .= '<div style="margin-bottom:10px;font-size:14px;line-height:1.5;position:relative;padding-left:20px">';
        $html .= '<span style="color:#d32f2f;font-weight:bold;position:absolute;left:0">•</span>';
        $html .= htmlspecialchars($texts['warning5'], ENT_NOQUOTES, 'UTF-8');
        $html .= '</div>';

        $html .= '</div>';

        $html .= '</div>';

        // 底部区域
        $html .= '<div style="background:#f5f5f5;padding:15px;text-align:center;font-size:12px;color:#666">';
        $html .= htmlspecialchars($texts['footerText'], ENT_NOQUOTES, 'UTF-8');
        $html .= '</div>';

        $html .= '</div>';

        return $html;
    }
}
