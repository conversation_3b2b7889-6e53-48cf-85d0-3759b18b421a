<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>手机号和邮箱导出</title>
    <style>
        .page-container {
            display: flex;
            min-height: 100vh;
        }
        .sidebar {
            width: 160px;
            border-right: 1px solid #dcdfe6;
            padding: 20px 0;
            background-color: #fff;
        }
        .main-content {
            flex: 1;
            padding: 20px 30px;
        }
        .button-group {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .el-form-item-description {
            color: #606266;
            font-size: 12px;
            line-height: 1.5;
            margin-top: 4px;
            padding-left: 1px;
            display: block !important;
        }
        .el-menu {
            border-right: none !important;
        }
        .el-menu-item {
            height: 50px;
            line-height: 50px;
            padding: 0 15px !important;
        }
        .el-menu-item .el-icon {
            margin-right: 8px;
            width: 20px;
            text-align: center;
            font-size: 16px;
            vertical-align: middle;
        }
        .el-menu-item span {
            font-size: 13px;
            color: #303133;
        }
        .el-menu-item.is-active {
            background-color: #ecf5ff;
        }
        .el-menu-item.is-active span {
            color: #409eff;
        }
        .el-menu-item:hover {
            background-color: #f5f7fa;
        }
        .result-area {
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            background: #f5f7fa;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #dcdfe6;
        }
        .result-item {
            margin: 3px 0;
            font-size: 13px;
            color: #303133;
            padding: 2px 0;
        }
        .stats {
            margin-bottom: 15px;
            font-size: 14px;
            color: #409eff;
            font-weight: 500;
        }
        .copy-btn {
            margin-top: 15px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .section-header {
            font-size: 14px;
            font-weight: bold;
            color: #409eff;
            margin: 10px 0 5px 0;
            padding-bottom: 5px;
            border-bottom: 1px solid #e4e7ed;
        }
    </style>
</head>
<body>
<div id="app">
    <div class="page-container">
        <!-- 左侧导航栏 -->
        <div class="sidebar">
            <el-menu
                :default-active="activeMenu"
                @select="handleMenuSelect"
                style="border-right: none">
                <el-menu-item index="mobile">
                    <el-icon>
                        <svg viewBox="0 0 1024 1024" width="24" height="24">
                            <path d="M744 62H280c-35.3 0-64 28.7-64 64v772c0 35.3 28.7 64 64 64h464c35.3 0 64-28.7 64-64V126c0-35.3-28.7-64-64-64zm-8 824H288V134h448v752zM472 784h80c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8h-80c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z" fill="currentColor"/>
                        </svg>
                    </el-icon>
                    <span>手机号导出</span>
                </el-menu-item>
                <el-menu-item index="email">
                    <el-icon>
                        <svg viewBox="0 0 1024 1024" width="24" height="24">
                            <path d="M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5z" fill="currentColor"/>
                            <path d="M833.6 232L512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0 0 68.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z" fill="currentColor"/>
                        </svg>
                    </el-icon>
                    <span>邮箱导出</span>
                </el-menu-item>
                <el-menu-item index="all">
                    <el-icon>
                        <svg viewBox="0 0 1024 1024" width="24" height="24">
                            <path d="M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-40 824H232V136h560v752z" fill="currentColor"/>
                            <path d="M304 230h416v60H304zm0 120h416v60H304zm0 120h416v60H304zm0 120h416v60H304zm0 120h416v60H304z" fill="currentColor"/>
                        </svg>
                    </el-icon>
                    <span>全部导出</span>
                </el-menu-item>
            </el-menu>
        </div>

        <!-- 主要内容区 -->
        <div class="main-content">
            <!-- 手机号导出 -->
            <el-card v-show="activeMenu === 'mobile'" shadow="never">
                <template #header>
                    <div class="card-header">
                        <h3>手机号导出</h3>
                    </div>
                </template>

                <el-form label-width="120px">
                    <el-form-item>
                        <el-button
                            type="primary"
                            @click="exportMobile"
                            :loading="mobileLoading">
                            导出手机号
                        </el-button>
                        <div class="el-form-item-description">
                            从用户表中导出所有非空的手机号数据，自动去重
                        </div>
                    </el-form-item>

                    <div v-if="exportResult.length > 0 && activeMenu === 'mobile'" class="result-area">
                        <div class="stats">
                            手机号总数: {{ mobileCount }} 个
                        </div>
                        <div v-for="(item, index) in exportResult" :key="index" class="result-item">
                            {{ item }}
                        </div>
                        <div class="copy-btn">
                            <el-button size="small" type="primary" @click="copyToClipboard">复制到剪贴板</el-button>
                            <el-button size="small" type="success" @click="downloadAsText">下载为txt文件</el-button>
                        </div>
                    </div>
                </el-form>
            </el-card>

            <!-- 邮箱导出 -->
            <el-card v-show="activeMenu === 'email'" shadow="never">
                <template #header>
                    <div class="card-header">
                        <h3>邮箱导出</h3>
                    </div>
                </template>

                <el-form label-width="120px">
                    <el-form-item>
                        <el-button
                            type="success"
                            @click="exportEmail"
                            :loading="emailLoading">
                            导出邮箱
                        </el-button>
                        <div class="el-form-item-description">
                            从用户邮箱推送表中导出所有非空的邮箱数据，自动去重
                        </div>
                    </el-form-item>

                    <div v-if="exportResult.length > 0 && activeMenu === 'email'" class="result-area">
                        <div class="stats">
                            邮箱总数: {{ emailCount }} 个
                        </div>
                        <div v-for="(item, index) in exportResult" :key="index" class="result-item">
                            {{ item }}
                        </div>
                        <div class="copy-btn">
                            <el-button size="small" type="primary" @click="copyToClipboard">复制到剪贴板</el-button>
                            <el-button size="small" type="success" @click="downloadAsText">下载为txt文件</el-button>
                        </div>
                    </div>
                </el-form>
            </el-card>

            <!-- 全部导出 -->
            <el-card v-show="activeMenu === 'all'" shadow="never">
                <template #header>
                    <div class="card-header">
                        <h3>全部数据导出</h3>
                    </div>
                </template>

                <el-form label-width="120px">
                    <el-form-item>
                        <el-button
                            type="warning"
                            @click="exportAll"
                            :loading="allLoading">
                            导出全部数据
                        </el-button>
                        <div class="el-form-item-description">
                            同时导出手机号和邮箱数据，分类显示，自动去重
                        </div>
                    </el-form-item>

                    <div v-if="exportResult.length > 0 && activeMenu === 'all'" class="result-area">
                        <div class="stats">
                            <span v-if="mobileCount > 0">手机号: {{ mobileCount }} 个</span>
                            <span v-if="emailCount > 0" style="margin-left: 20px;">邮箱: {{ emailCount }} 个</span>
                        </div>
                        <div v-for="(item, index) in exportResult" :key="index" class="result-item">
                            <span v-if="item.startsWith('===')" class="section-header">{{ item }}</span>
                            <span v-else>{{ item }}</span>
                        </div>
                        <div class="copy-btn">
                            <el-button size="small" type="primary" @click="copyToClipboard">复制到剪贴板</el-button>
                            <el-button size="small" type="success" @click="downloadAsText">下载为txt文件</el-button>
                        </div>
                    </div>
                </el-form>
            </el-card>
        </div>
    </div>
</div>

<script src="/static/others/vue/vue.global.prod.js"></script>
<script src="/static/others/element-plus/index.full.min.js"></script>
<script src="/static/others/axios/axios.min.js"></script>

<script type="module">
    const { ref } = Vue;
    const { ElMessage } = ElementPlus;

    const app = Vue.createApp({
        setup() {
            const activeMenu = ref('mobile');
            const mobileLoading = ref(false);
            const emailLoading = ref(false);
            const allLoading = ref(false);
            const exportResult = ref([]);
            const mobileCount = ref(0);
            const emailCount = ref(0);

            const handleMenuSelect = (key) => {
                activeMenu.value = key;
                // 切换菜单时清空结果
                exportResult.value = [];
                mobileCount.value = 0;
                emailCount.value = 0;
            };

            const exportMobile = async () => {
                mobileLoading.value = true;
                try {
                    const res = await axios.post("/plugin/Moblieandemail/Api/exportMobile");

                    if (res.data.code === 200) {
                        exportResult.value = res.data.data;
                        mobileCount.value = res.data.count;
                        emailCount.value = 0;
                        ElMessage.success(`成功导出 ${res.data.count} 个手机号`);
                    } else {
                        ElMessage.error(res.data.msg);
                        exportResult.value = [];
                        mobileCount.value = 0;
                        emailCount.value = 0;
                    }
                } catch (error) {
                    ElMessage.error('导出失败: ' + error.message);
                    exportResult.value = [];
                    mobileCount.value = 0;
                    emailCount.value = 0;
                } finally {
                    mobileLoading.value = false;
                }
            };

            const exportEmail = async () => {
                emailLoading.value = true;
                try {
                    const res = await axios.post("/plugin/Moblieandemail/Api/exportEmail");

                    if (res.data.code === 200) {
                        exportResult.value = res.data.data;
                        emailCount.value = res.data.count;
                        mobileCount.value = 0;
                        ElMessage.success(`成功导出 ${res.data.count} 个邮箱`);
                    } else {
                        ElMessage.error(res.data.msg);
                        exportResult.value = [];
                        mobileCount.value = 0;
                        emailCount.value = 0;
                    }
                } catch (error) {
                    ElMessage.error('导出失败: ' + error.message);
                    exportResult.value = [];
                    mobileCount.value = 0;
                    emailCount.value = 0;
                } finally {
                    emailLoading.value = false;
                }
            };

            const exportAll = async () => {
                allLoading.value = true;
                try {
                    const res = await axios.post("/plugin/Moblieandemail/Api/exportAll");

                    if (res.data.code === 200) {
                        const data = res.data.data;
                        const allData = [];

                        // 添加手机号
                        if (data.mobiles && data.mobiles.length > 0) {
                            allData.push('=== 手机号 ===');
                            allData.push(...data.mobiles);
                        }

                        // 添加邮箱
                        if (data.emails && data.emails.length > 0) {
                            if (allData.length > 0) allData.push('');
                            allData.push('=== 邮箱 ===');
                            allData.push(...data.emails);
                        }

                        exportResult.value = allData;
                        mobileCount.value = data.mobile_count;
                        emailCount.value = data.email_count;
                        ElMessage.success(`成功导出 ${data.mobile_count} 个手机号和 ${data.email_count} 个邮箱`);
                    } else {
                        ElMessage.error(res.data.msg);
                        exportResult.value = [];
                        mobileCount.value = 0;
                        emailCount.value = 0;
                    }
                } catch (error) {
                    ElMessage.error('导出失败: ' + error.message);
                    exportResult.value = [];
                    mobileCount.value = 0;
                    emailCount.value = 0;
                } finally {
                    allLoading.value = false;
                }
            };



            const copyToClipboard = async () => {
                try {
                    // 过滤掉分隔符行，只保留实际的手机号和邮箱数据
                    const filteredData = exportResult.value.filter(item => {
                        return item &&
                               !item.startsWith('===') &&
                               item.trim() !== '' &&
                               item !== '=== 手机号 ===' &&
                               item !== '=== 邮箱 ===';
                    });

                    // 每行一个数据，适合粘贴到记事本
                    const text = filteredData.join('\n');

                    await navigator.clipboard.writeText(text);
                    ElMessage.success(`已复制 ${filteredData.length} 条数据到剪贴板，可直接粘贴到记事本`);
                } catch (error) {
                    // 如果现代API失败，尝试传统方法
                    try {
                        const filteredData = exportResult.value.filter(item => {
                            return item &&
                                   !item.startsWith('===') &&
                                   item.trim() !== '';
                        });
                        const text = filteredData.join('\n');

                        // 创建临时文本区域
                        const textArea = document.createElement('textarea');
                        textArea.value = text;
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);

                        ElMessage.success(`已复制 ${filteredData.length} 条数据到剪贴板`);
                    } catch (fallbackError) {
                        ElMessage.error('复制失败，请手动选择数据复制');
                    }
                }
            };

            const downloadAsText = () => {
                try {
                    // 过滤掉分隔符行，只保留实际的手机号和邮箱数据
                    const filteredData = exportResult.value.filter(item => {
                        return item &&
                               !item.startsWith('===') &&
                               item.trim() !== '';
                    });

                    if (filteredData.length === 0) {
                        ElMessage.warning('没有数据可以下载');
                        return;
                    }

                    // 创建文件内容，每行一个数据
                    const content = filteredData.join('\n');

                    // 创建Blob对象
                    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });

                    // 创建下载链接
                    const url = window.URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;

                    // 根据当前菜单生成文件名
                    let filename = '';
                    if (activeMenu.value === 'mobile') {
                        filename = `手机号导出_${new Date().toISOString().slice(0, 10)}.txt`;
                    } else if (activeMenu.value === 'email') {
                        filename = `邮箱导出_${new Date().toISOString().slice(0, 10)}.txt`;
                    } else {
                        filename = `联系方式导出_${new Date().toISOString().slice(0, 10)}.txt`;
                    }

                    link.download = filename;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    window.URL.revokeObjectURL(url);

                    ElMessage.success(`已下载 ${filteredData.length} 条数据到 ${filename}`);
                } catch (error) {
                    ElMessage.error('下载失败: ' + error.message);
                }
            };

            return {
                activeMenu,
                mobileLoading,
                emailLoading,
                allLoading,
                exportResult,
                mobileCount,
                emailCount,
                handleMenuSelect,
                exportMobile,
                exportEmail,
                exportAll,
                copyToClipboard,
                downloadAsText
            };
        }
    });

    app.use(ElementPlus);
    app.mount("#app");
</script>
</body>
</html>
