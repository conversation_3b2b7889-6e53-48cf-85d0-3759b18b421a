<?php

return [
    "version" => "1.0.0",
    "min_system_version" => "2.9.0", 
    "category_name" => "短链接",
    "logo" => "https://cdn.xiaomark.com/portal/main/xmzy-logo_v1noHt4S.png",
    "name" => "小码至营短链接",
    "description" => "小码至营短链接API接口",
    "menu" => [
        [
            'tag' => 'a',
            'name' => '官网',
            'href' => 'https://xiaomark.com/',
        ],
    ],
    "form_fields" => [
        [
            'id' => 'apikey',
            'name' => 'API密钥',
            'placeholder' => '请输入小猫云API密钥',
            'required' => true,
            'type' => 'input',
        ],
        [
            'id' => 'group_id',
            'name' => '分组ID',
            'placeholder' => '请输入链接分组的ID，可在网页端API短链分组列表中查找',
            'required' => true,
            'type' => 'input',
        ],
        [
            'id' => 'domain',
            'name' => '自定义域名',
            'placeholder' => '可选，不填则默认使用sourl.cn',
            'required' => false,
            'type' => 'input',
        ],
        [
            'id' => 'webhook',
            'name' => 'Webhook推送',
            'required' => false,
            'type' => 'radio',
            'data' => [
                [
                    'name' => '开启',
                    'value' => '1',
                ],
                [
                    'name' => '关闭',
                    'value' => '0',
                ]
            ],
        ],
        [
            'id' => 'webhook_scene',
            'name' => 'Webhook场景值',
            'placeholder' => '可选，Webhook推送场景值，长度不超过128个字符',
            'required' => false,
            'type' => 'input',
        ],
    ]
]; 