{"name": "@one-ini/wasm", "collaborators": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>"], "description": "Parse EditorConfig-INI file contents into AST", "version": "0.1.1", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/one-ini/core"}, "files": ["one_ini_bg.wasm", "one_ini.js", "one_ini.d.ts"], "main": "one_ini.js", "types": "one_ini.d.ts", "keywords": ["editorconfig", "ini", "parser", "ast"]}