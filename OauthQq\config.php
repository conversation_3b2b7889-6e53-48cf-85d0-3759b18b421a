<?php

return [
    "version" => "1.0.0",
    "min_system_version" => "2.12.0",
    "category_name" => "功能插件",
    "logo" => "data:image/png;base64,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",
    "name" => "QQ登录",
    "description" => "QQ登录",
    "menu" => [
        [
            'tag' => 'a',
            'name' => 'QQ互联官网',
            'href' => 'https://connect.qq.com/',
        ],
    ],
    "form_fields" => [
        [
            'id' => 'status',
            'name' => '开启状态',
            'required' => true,
            'type' => 'radio',
            'data' => [
                [
                    'name' => '关闭',
                    'value' => 0,
                ],
                [
                    'name' => '开启',
                    'value' => 1,
                ]
            ],
        ],
        [
            'id' => 'appId',
            'name' => 'APP ID',
            'placeholder' => '请输入APP ID',
            'required' => true,
            'type' => 'input',
        ],
        [
            'id' => 'appKey',
            'name' => 'APP Key',
            'placeholder' => '请输入APP Key',
            'required' => true,
            'type' => 'input',
            'remark' => '网站回调域：http(s)://你的域名/plugin/OauthQq/Api/callback'
        ],
    ],
    "hook" => [
        'MerchantSystemConfigAfter' => 'plugin\OauthQq\Hook',
    ],
];
