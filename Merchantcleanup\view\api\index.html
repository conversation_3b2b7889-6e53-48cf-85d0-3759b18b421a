<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡密回收站设置</title>
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #f5f7fa;
        }

        .container {
            padding: 20px;
        }

        .config-card {
            margin-bottom: 20px;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .el-form-item {
            margin-bottom: 22px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <el-card class="config-card">
                <template #header>
                    <div class="card-header">
                        <span>回收站设置</span>
                    </div>
                </template>
                
                <el-form :model="config" label-width="120px" v-loading="loading">
                    <el-form-item label="启用回收站">
                        <el-switch v-model="config.status" />
                        <div class="el-form-item-tip" style="font-size: 12px; color: #909399; margin-top: 5px;">
                            关闭后用户将无法访问回收站功能
                        </div>
                    </el-form-item>
                    
                    <el-form-item label="自动清理">
                        <el-switch v-model="config.enable_auto_clean" />
                        <div class="el-form-item-tip" style="font-size: 12px; color: #909399; margin-top: 5px;">
                            开启后将自动清理指定天数前的回收站卡密
                        </div>
                    </el-form-item>
                    
                    <el-form-item label="清理天数" v-if="config.enable_auto_clean">
                        <el-input-number 
                            v-model="config.auto_clean_days" 
                            :min="1" 
                            :max="365"
                            placeholder="请输入自动清理天数" />
                        <div class="el-form-item-tip" style="font-size: 12px; color: #909399; margin-top: 5px;">
                            超过指定天数的回收站卡密将被自动清理
                        </div>
                    </el-form-item>
                    
                    <el-form-item>
                        <el-button type="primary" @click="saveConfig" :loading="saving">保存设置</el-button>
                    </el-form-item>
                </el-form>
            </el-card>
        </div>
    </div>

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>

    <script>
        const app = Vue.createApp({
            setup() {
                const config = Vue.ref({
                    status: true,
                    auto_clean_days: 30,
                    enable_auto_clean: false
                });
                const loading = Vue.ref(false);
                const saving = Vue.ref(false);

                // 获取配置
                const getConfig = async () => {
                    loading.value = true;
                    try {
                        const res = await axios.get('/plugin/Merchantcleanup/api/getConfig');
                        if (res.data.code === 200) {
                            Object.assign(config.value, res.data.data);
                        } else {
                            ElMessage.error(res.data.msg || '获取配置失败');
                        }
                    } catch (error) {
                        console.error('获取配置失败:', error);
                        ElMessage.error('获取配置失败：' + error.message);
                    } finally {
                        loading.value = false;
                    }
                };

                // 保存配置
                const saveConfig = async () => {
                    saving.value = true;
                    try {
                        const res = await axios.post('/plugin/Merchantcleanup/api/saveConfig', config.value);
                        if (res.data.code === 200) {
                            ElMessage.success('保存成功');
                        } else {
                            ElMessage.error(res.data.msg || '保存失败');
                        }
                    } catch (error) {
                        console.error('保存配置失败:', error);
                        ElMessage.error('保存配置失败：' + error.message);
                    } finally {
                        saving.value = false;
                    }
                };

                // 页面加载时获取配置
                Vue.onMounted(() => {
                    getConfig();
                });

                return {
                    config,
                    loading,
                    saving,
                    saveConfig
                };
            }
        });

        app.use(ElementPlus, {
            locale: ElementPlusLocaleZhCn
        });
        
        app.mount('#app');
    </script>
</body>
</html> 