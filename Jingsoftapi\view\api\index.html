<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <meta http-equiv="Cache-Control" content="max-age=31536000">
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>API接口管理</title>
    <style>
        /* 加载动画样式 */
        #loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #fff;
            z-index: 9999;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(0, 0, 0, 0.1);
            border-top-color: #3498db;
            border-radius: 50%;
            animation: spin 1s ease infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .api-section {
            margin-bottom: 20px;
        }

        .api-header {
            font-size: 16px;
            font-weight: bold;
            color: #409EFF;
            margin-bottom: 10px;
        }

        .api-description {
            color: #666;
            font-size: 12px;
            margin-bottom: 15px;
        }

        .api-endpoint-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .api-endpoint-item:last-child {
            border-bottom: none;
        }

        .endpoint-info {
            flex: 1;
        }

        .endpoint-name {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .endpoint-description {
            color: #666;
            font-size: 12px;
        }

        .endpoint-controls {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 8px;
        }

        .control-row {
            display: flex;
            align-items: center;
        }

        .button-row {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .copy-btn {
            margin-left: 10px;
        }

        .secret-input {
            position: relative;
            display: flex;
            align-items: center;
        }

        .secret-input .el-input {
            flex: 1;
        }

        .eye-btn {
            margin-left: 8px;
            padding: 8px 12px;
            background: #f5f7fa;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            cursor: pointer;
            color: #606266;
            font-size: 14px;
            transition: all 0.3s;
        }

        .eye-btn:hover {
            background: #ecf5ff;
            border-color: #b3d8ff;
            color: #409eff;
        }

        /* 接口文档样式 */
        .api-doc-content {
            font-size: 14px;
            line-height: 1.6;
        }

        .doc-section {
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #ebeef5;
        }

        .doc-section:last-child {
            border-bottom: none;
        }

        .doc-section h3 {
            margin: 0 0 16px 0;
            color: #303133;
            font-size: 16px;
            font-weight: 600;
        }

        .doc-item {
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .method-tag {
            background: #67c23a;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .url-code {
            background: #f5f7fa;
            padding: 4px 8px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            color: #e6a23c;
            border: 1px solid #ebeef5;
        }

        .status-enabled {
            color: #67c23a;
            font-weight: bold;
        }

        .status-disabled {
            color: #f56c6c;
            font-weight: bold;
        }

        .auth-headers {
            background: #f5f7fa;
            padding: 16px;
            border-radius: 6px;
            border: 1px solid #ebeef5;
        }

        .header-item {
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .header-item:last-child {
            margin-bottom: 0;
        }

        .header-item code {
            background: white;
            padding: 6px 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            color: #606266;
            border: 1px solid #dcdfe6;
            flex: 1;
        }

        .response-example {
            background: #f5f7fa;
            border: 1px solid #ebeef5;
            border-radius: 6px;
            overflow: hidden;
        }

        .response-example pre {
            margin: 0;
            padding: 16px;
            background: #2d3748;
            color: #e2e8f0;
            overflow-x: auto;
        }

        .response-example code {
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.5;
        }

        .example-tabs .el-tabs__content {
            padding-top: 16px;
        }

        .example-tabs pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 16px;
            border-radius: 6px;
            overflow-x: auto;
            margin-bottom: 12px;
        }

        .example-tabs code {
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.5;
        }

        .notice-list {
            margin: 0;
            padding-left: 20px;
        }

        .notice-list li {
            margin-bottom: 8px;
            color: #606266;
        }

        .el-input.is-disabled .el-input__inner,
        .el-input__inner[readonly] {
            background-color: #f5f7fa;
            border-color: #e4e7ed;
            color: #606266;
            cursor: not-allowed;
        }
    </style>
</head>

<body>
<!-- 加载动画容器 -->
<div id="loading">
    <div class="spinner"></div>
</div>

<!-- Vue 应用挂载点 -->
<div id="app" style="display: none">
    <!-- API接口管理说明 -->
    <el-card shadow="never" class="api-section">
        <div class="api-header">API接口管理</div>
        <div class="api-description">
            • 接口限制：每秒最多 {{ form.access_limits.qps_limit }} 次请求（1 QPS）<br>
            • 每个接口每日使用总数限制，或使用单独的接口限制进行覆盖（推荐）
        </div>
        <div style="color: #666; font-size: 12px;">
            总密钥可以制约所有API接口，单个密钥仅对对应接口使用
        </div>
    </el-card>

    <!-- 全局访问密钥 -->
    <el-card shadow="never" class="api-section">
        <div class="api-header">全局访问密钥</div>

        <el-form :model="form" label-width="100px">
            <el-form-item label="API Key">
                <el-input
                    v-model="form.api_key"
                    readonly
                    placeholder="系统自动生成"
                    style="width: 400px;"
                >
                    <template #append>
                        <el-button @click="copyToClipboard(form.api_key)">复制</el-button>
                    </template>
                </el-input>
            </el-form-item>

            <el-form-item label="API Secret">
                <div class="secret-input">
                    <el-input
                        v-model="form.api_secret"
                        :type="showSecret ? 'text' : 'password'"
                        readonly
                        placeholder="系统自动生成"
                        style="width: 400px;"
                    >
                        <template #append>
                            <el-button @click="copyToClipboard(form.api_secret)">复制</el-button>
                        </template>
                    </el-input>
                    <div class="eye-btn" @click="showSecret = !showSecret">
                        {{ showSecret ? '👁️ 隐藏' : '👁️ 显示' }}
                    </div>
                </div>
            </el-form-item>

            <el-form-item>
                <el-button
                    type="primary"
                    :loading="isLoading"
                    @click="resetApiKeys"
                >
                    重置密钥
                </el-button>
            </el-form-item>
        </el-form>
    </el-card>

    <!-- API接口管理 -->
    <el-card shadow="never" class="api-section">
        <div class="api-header">API接口管理</div>

        <div v-for="(endpoint, key) in form.api_endpoints" :key="key" class="api-endpoint-item">
            <div class="endpoint-info">
                <div class="endpoint-name">{{ endpoint.name }} ({{ key }})</div>
                <div class="endpoint-description">{{ endpoint.description }}</div>
                <div v-if="endpoint.api_key" style="margin-top: 8px; font-size: 12px; color: #666;">
                    <div>接口密钥: {{ endpoint.api_key }}</div>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <span>接口密码: {{ showEndpointSecrets[key] ? endpoint.api_secret : '••••••••••••••••' }}</span>
                        <span
                            style="cursor: pointer; color: #409eff; font-size: 10px;"
                            @click="toggleEndpointSecret(key)"
                        >
                            {{ showEndpointSecrets[key] ? '👁️ 隐藏' : '👁️ 显示' }}
                        </span>
                    </div>
                </div>
            </div>
            <div class="endpoint-controls">
                <div class="control-row">
                    <el-switch
                        v-model="endpoint.enabled"
                        :loading="isLoading"
                        @change="updateEndpoint(key, endpoint.enabled)"
                    />
                    <span style="margin-left: 8px; font-size: 12px; color: #666;">
                        {{ endpoint.enabled ? '已启用' : '已禁用' }}
                    </span>
                </div>
                <div class="button-row">
                    <el-button
                        type="primary"
                        size="small"
                        @click="showApiDoc(key)"
                    >
                        📖 文档
                    </el-button>
                    <el-button
                        type="text"
                        size="small"
                        @click="generateEndpointKey(key)"
                    >
                        生成密钥
                    </el-button>
                    <el-button
                        v-if="endpoint.api_key"
                        type="text"
                        size="small"
                        @click="copyToClipboard(endpoint.api_key)"
                    >
                        复制密钥
                    </el-button>
                </div>
            </div>
        </div>
    </el-card>

    <!-- 接口文档弹窗 -->
    <el-dialog
        v-model="docDialogVisible"
        title="📖 接口文档"
        width="60%"
        destroy-on-close
    >
        <div v-if="currentApiDoc" style="font-size: 13px;">
            <el-descriptions title="接口信息" :column="1" border size="small">
                <el-descriptions-item label="接口名称" label-style="font-size: 12px;" content-style="font-size: 12px;">
                    {{ currentApiDoc.name }}
                </el-descriptions-item>
                <el-descriptions-item label="接口描述" label-style="font-size: 12px;" content-style="font-size: 12px;">
                    {{ currentApiDoc.description }}
                </el-descriptions-item>
                <el-descriptions-item label="请求方式" label-style="font-size: 12px;" content-style="font-size: 12px;">
                    POST
                </el-descriptions-item>
                <el-descriptions-item label="接口地址" label-style="font-size: 12px;" content-style="font-size: 12px;">
                    <code style="background: #f5f7fa; padding: 1px 4px; border-radius: 2px; font-size: 11px;">{{ currentApiDoc.url }}</code>
                    <el-button size="small" @click="copyToClipboard(currentApiDoc.url)" style="margin-left: 6px; font-size: 11px;">复制</el-button>
                </el-descriptions-item>
                <el-descriptions-item label="接口状态" label-style="font-size: 12px;" content-style="font-size: 12px;">
                    <el-tag :type="currentApiDoc.enabled ? 'success' : 'danger'" size="small" style="font-size: 11px;">
                        {{ currentApiDoc.enabled ? '✅ 已启用' : '❌ 已禁用' }}
                    </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="API Key" label-style="font-size: 12px;" content-style="font-size: 12px;">
                    <code style="background: #f5f7fa; padding: 1px 4px; border-radius: 2px; font-size: 11px;">{{ currentApiDoc.apiKey || form.api_key }}</code>
                    <el-button size="small" @click="copyToClipboard(currentApiDoc.apiKey || form.api_key)" style="margin-left: 6px; font-size: 11px;">复制</el-button>
                </el-descriptions-item>
                <el-descriptions-item label="API Secret" label-style="font-size: 12px;" content-style="font-size: 12px;">
                    <code style="background: #f5f7fa; padding: 1px 4px; border-radius: 2px; font-size: 11px;">{{ currentApiDoc.apiSecret || form.api_secret }}</code>
                    <el-button size="small" @click="copyToClipboard(currentApiDoc.apiSecret || form.api_secret)" style="margin-left: 6px; font-size: 11px;">复制</el-button>
                </el-descriptions-item>
            </el-descriptions>

            <div style="margin-top: 15px;">
                <h4 style="font-size: 13px; margin-bottom: 8px;">📋 返回字段说明</h4>
                <ul style="font-size: 12px; line-height: 1.4; margin: 0; padding-left: 16px;">
                    <li style="margin-bottom: 3px;"><strong>mobile</strong> - 手机号</li>
                    <li style="margin-bottom: 3px;"><strong>username</strong> - 用户名</li>
                    <li style="margin-bottom: 3px;"><strong>create_time</strong> - 注册时间</li>
                    <li style="margin-bottom: 3px;"><strong>contact_qq</strong> - 联系QQ</li>
                    <li style="margin-bottom: 3px;"><strong>contact_mobile</strong> - 联系手机</li>
                    <li style="margin-bottom: 3px;"><strong>platform_money</strong> - 平台余额</li>
                </ul>
            </div>

            <div style="margin-top: 15px;">
                <h4 style="font-size: 13px; margin-bottom: 8px;">⚠️ 重要提醒</h4>
                <el-alert
                    :title="currentApiDoc.enabled ? '接口已启用，可以正常调用' : '接口已禁用，无法调用'"
                    :type="currentApiDoc.enabled ? 'success' : 'error'"
                    show-icon
                    :closable="false"
                    size="small"
                    style="font-size: 12px;"
                />
            </div>
        </div>
        <template #footer>
            <el-button @click="docDialogVisible = false" size="small">关闭</el-button>
        </template>
    </el-dialog>


</div>

<script src="/static/others/vue/vue.global.prod.js"></script>
<script src="/static/others/element-plus/index.full.min.js"></script>
<script src="/static/others/axios/axios.min.js"></script>

<script type="module">
    const { ref, reactive } = Vue;
    const { ElMessage } = ElementPlus;

    const isLoading = ref(false);
    const showSecret = ref(false);
    const showEndpointSecrets = ref({});
    const docDialogVisible = ref(false);

    const currentApiDoc = ref(null);
    const activeTab = ref('curl');
    const form = reactive({
        api_key: '',
        api_secret: '',
        api_endpoints: {},
        access_limits: {
            daily_limit: 1000,
            qps_limit: 1
        }
    });

    // 获取配置
    const fetchData = async () => {
        try {
            const res = await axios.post("/plugin/Jingsoftapi/api/fetchData");
            if (res.data?.code == 200) {
                const data = res.data.data;
                form.api_key = data.api_key || '';
                form.api_secret = data.api_secret || '';
                form.api_endpoints = data.api_endpoints || {};
                form.access_limits = data.access_limits || { daily_limit: 1000, qps_limit: 1 };
            }
        } catch (error) {
            ElMessage.error(error.message || '获取配置失败');
        }
    };

    // 重置API密钥
    const resetApiKeys = async () => {
        isLoading.value = true;
        try {
            const res = await axios.post("/plugin/Jingsoftapi/api/resetApiKeys");
            isLoading.value = false;
            if (res.data?.code == 200) {
                ElMessage.success(res.data?.msg || '密钥重置成功');
                // 更新表单数据
                if (res.data.data) {
                    form.api_key = res.data.data.api_key;
                    form.api_secret = res.data.data.api_secret;
                }
            } else {
                ElMessage.error(res.data?.msg || '重置失败');
            }
        } catch (error) {
            isLoading.value = false;
            ElMessage.error(error.message || '重置失败');
        }
    };

    // 更新接口状态
    const updateEndpoint = async (endpoint, enabled) => {
        isLoading.value = true;
        try {
            const res = await axios.post("/plugin/Jingsoftapi/api/updateEndpoint", {
                endpoint: endpoint,
                enabled: enabled
            });
            isLoading.value = false;
            if (res.data?.code == 200) {
                ElMessage.success('更新成功');
            } else {
                ElMessage.error(res.data?.msg || '更新失败');
                // 恢复原状态
                form.api_endpoints[endpoint].enabled = !enabled;
            }
        } catch (error) {
            isLoading.value = false;
            ElMessage.error(error.message || '更新失败');
            // 恢复原状态
            form.api_endpoints[endpoint].enabled = !enabled;
        }
    };

    // 生成接口密钥
    const generateEndpointKey = async (endpoint) => {
        isLoading.value = true;
        try {
            const res = await axios.post("/plugin/Jingsoftapi/api/generateEndpointKey", {
                endpoint: endpoint
            });
            isLoading.value = false;
            if (res.data?.code == 200) {
                ElMessage.success(res.data?.msg || '接口密钥生成成功');
                // 更新接口配置
                if (res.data.data) {
                    const endpointName = res.data.data.endpoint;
                    form.api_endpoints[endpointName].api_key = res.data.data.api_key;
                    form.api_endpoints[endpointName].api_secret = res.data.data.api_secret;
                }
            } else {
                ElMessage.error(res.data?.msg || '生成失败');
            }
        } catch (error) {
            isLoading.value = false;
            ElMessage.error(error.message || '生成失败');
        }
    };

    // 切换接口密码显示状态
    const toggleEndpointSecret = (endpoint) => {
        showEndpointSecrets.value[endpoint] = !showEndpointSecrets.value[endpoint];
    };

    // 显示接口文档
    const showApiDoc = (endpoint) => {
        console.log('显示接口文档:', endpoint); // 调试日志

        const endpointInfo = form.api_endpoints[endpoint];
        if (!endpointInfo) {
            ElMessage.error('接口信息不存在');
            return;
        }

        currentApiDoc.value = {
            name: endpointInfo.name,
            description: endpointInfo.description,
            enabled: endpointInfo.enabled,
            url: `${window.location.origin}/plugin/Jingsoftapi/api/${endpoint}`,
            apiKey: endpointInfo.api_key || null,
            apiSecret: endpointInfo.api_secret || null
        };

        console.log('设置文档数据:', currentApiDoc.value); // 调试日志
        docDialogVisible.value = true;
        console.log('弹窗状态:', docDialogVisible.value); // 调试日志
    };

    // 关闭文档弹窗
    const closeDocDialog = () => {
        docDialogVisible.value = false;
        currentApiDoc.value = null;
    };

    // 生成API文档
    const generateDoc = async (endpoint) => {
        isLoading.value = true;
        try {
            const res = await axios.post("/plugin/Jingsoftapi/api/generateDoc", {
                endpoint: endpoint
            });
            isLoading.value = false;
            if (res.data?.code == 200) {
                // 显示API文档
                const doc = res.data.data;
                let docContent = `接口名称: ${doc.name}\n`;
                docContent += `接口地址: ${doc.url}\n`;
                docContent += `请求方式: ${doc.method}\n`;
                docContent += `接口描述: ${doc.description}\n\n`;
                docContent += `请求头:\n`;
                Object.keys(doc.headers).forEach(key => {
                    docContent += `${key}: ${doc.headers[key]}\n`;
                });
                docContent += `\n请求参数:\n`;
                Object.keys(doc.parameters).forEach(key => {
                    const param = doc.parameters[key];
                    docContent += `${key} (${param.type}): ${param.description} ${param.required ? '[必填]' : '[可选]'}\n`;
                });
                docContent += `\n响应示例:\n${JSON.stringify(doc.response, null, 2)}`;

                ElMessage.success('API文档已生成');
                // 这里可以弹出对话框显示文档内容
                alert(docContent);
            } else {
                ElMessage.error(res.data?.msg || '生成失败');
            }
        } catch (error) {
            isLoading.value = false;
            ElMessage.error(error.message || '生成失败');
        }
    };

    // 复制到剪贴板
    const copyToClipboard = (text) => {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                ElMessage.success('复制成功');
            }).catch(() => {
                ElMessage.error('复制失败');
            });
        } else {
            // 兼容旧浏览器
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                ElMessage.success('复制成功');
            } catch (err) {
                ElMessage.error('复制失败');
            }
            document.body.removeChild(textArea);
        }
    };

    // 页面加载时获取配置
    fetchData();

    const app = Vue.createApp({
        setup() {
            return {
                isLoading,
                showSecret,
                showEndpointSecrets,
                docDialogVisible,
                currentApiDoc,
                form,
                resetApiKeys,
                updateEndpoint,
                generateEndpointKey,
                toggleEndpointSecret,
                showApiDoc,
                closeDocDialog,
                generateDoc,
                copyToClipboard
            };
        },
    });

    app.use(ElementPlus);
    app.mount("#app");

    window.onload = () => {
        document.getElementById("loading").style.display = "none";
        document.getElementById("app").style.display = "block";
    };
</script>
</body>
</html>
