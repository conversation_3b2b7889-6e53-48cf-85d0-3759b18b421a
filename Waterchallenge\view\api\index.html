<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流水挑战</title>
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>
    <style>
        [v-cloak] { display: none; }
        body { margin: 0; padding: 0; background: #f5f7fa; }
        .container { padding: 20px; }
        .header-operations { margin-bottom: 20px; }
        .el-card { margin-bottom: 20px; }
        .status-tag { margin-right: 5px; }
        .amount-input .el-input__inner { text-align: right; }
        .progress-column { width: 200px; }
        .el-progress-bar { margin-top: 8px; }
        .challenge-stats { 
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        .stat-card {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #409EFF;
        }
        .stat-label {
            color: #909399;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div id="app" v-cloak>
        <div class="container">
            <!-- 统计数据 -->
            <div class="challenge-stats">
                <div class="stat-card">
                    <div class="stat-value">{{ stats.totalChallenges }}</div>
                    <div class="stat-label">总挑战数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{{ stats.activeChallenges }}</div>
                    <div class="stat-label">进行中</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{{ stats.completedChallenges }}</div>
                    <div class="stat-label">已完成</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{{ stats.totalRewards }}</div>
                    <div class="stat-label">总奖励(元)</div>
                </div>
            </div>

            <!-- 规则管理 -->
            <el-card>
                <template #header>
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span>挑战规则管理</span>
                        <el-button type="primary" @click="showRuleDialog('add')">添加规则</el-button>
                    </div>
                </template>
                
                <el-table :data="rules" border style="width: 100%">
                    <el-table-column label="规则信息">
                        <template #default="scope">
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div>
                                    <span style="color: #909399; margin-right: 5px;">规则名称:</span>
                                    <span>{{ scope.row.name }}</span>
                                </div>
                                <div>
                                    <span style="color: #909399; margin-right: 5px;">目标流水:</span>
                                    <span>{{ scope.row.turnover_amount.toLocaleString() }}元</span>
                                </div>
                                <div>
                                    <span style="color: #909399; margin-right: 5px;">奖励金额:</span>
                                    <span>{{ scope.row.reward_amount.toLocaleString() }}元</span>
                                </div>
                                <div>
                                    <span style="color: #909399; margin-right: 5px;">有效天数:</span>
                                    <span>{{ scope.row.valid_days }}天</span>
                                </div>
                                <div>
                                    <span style="color: #909399; margin-right: 5px;">挑战时长:</span>
                                    <span>{{ scope.row.challenge_duration }}天</span>
                                </div>
                                <div>
                                    <span style="color: #909399; margin-right: 5px;">可参与次数:</span>
                                    <span>{{ scope.row.max_attempts > 0 ? scope.row.max_attempts + '次' : '不限' }}</span>
                                </div>
                                <div>
                                    <span style="color: #909399; margin-right: 5px;">状态:</span>
                                    <el-switch
                                        v-model="scope.row.status"
                                        :active-value="1"
                                        :inactive-value="0"
                                        @change="handleStatusChange(scope.row)"
                                    />
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="150" fixed="right">
                        <template #default="scope">
                            <el-button-group>
                                <el-button size="small" @click="showRuleDialog('edit', scope.row)">编辑</el-button>
                                <el-button size="small" type="danger" @click="deleteRule(scope.row)">删除</el-button>
                            </el-button-group>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>

            <!-- 挑战记录 -->
            <el-card>
                <template #header>
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span>挑战记录</span>
                        <div>
                            <el-input
                                v-model="searchForm.merchant"
                                placeholder="搜索商户"
                                style="width: 200px; margin-right: 10px;">
                            </el-input>
                            <el-select v-model="searchForm.status" placeholder="状态筛选" style="width: 120px; margin-right: 10px;" clearable>
                                <el-option
                                    v-for="item in statusOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                    <span style="float: left">{{ item.label }}</span>
                                    <span v-if="item.count !== undefined" style="float: right; color: #8492a6; font-size: 13px">
                                        ({{ item.count }})
                                    </span>
                                </el-option>
                            </el-select>
                            <el-button type="primary" @click="searchRecords">搜索</el-button>
                            <el-button @click="resetSearch">重置</el-button>
                            <el-button type="danger" @click="clearRecords">清空记录</el-button>
                        </div>
                    </div>
                </template>

                <el-table :data="records" border style="width: 100%" v-loading="loading"
                    :size="isMobile ? 'small' : 'default'"
                    :cell-style="{ padding: isMobile ? '4px' : '8px' }">
                    <el-table-column prop="merchant_name" label="商户" min-width="120" align="center">
                        <template #default="scope">
                            <el-tag type="info" effect="plain">{{ scope.row.merchant_name }}</el-tag>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="rule_name" label="挑战规则" min-width="120" align="center">
                        <template #default="scope">
                            <el-tag type="warning" effect="light">{{ scope.row.rule_name }}</el-tag>
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="当前流水" min-width="120" align="center">
                        <template #default="scope">
                            {{ Number(scope.row.current_amount).toLocaleString() }}
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="目标流水" min-width="120" align="center">
                        <template #default="scope">
                            {{ Number(scope.row.target_amount).toLocaleString() }}
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="进度" min-width="120" align="center">
                        <template #default="scope">
                            <div>
                                <el-progress 
                                    :percentage="scope.row.progress_percentage || ((scope.row.current_amount / scope.row.target_amount) * 100)" 
                                    :format="percent => percent.toFixed(1) + '%'"
                                    :status="scope.row.status === 'completed' ? 'success' : ''"
                                ></el-progress>
                            </div>
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="状态" min-width="100" align="center">
                        <template #default="scope">
                            <el-tag :type="getStatusType(scope.row.status)">
                                {{ getStatusText(scope.row.status) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="挑战时间" min-width="180" align="center">
                        <template #default="scope">
                            <div>开始：{{ scope.row.start_time }}</div>
                            <div>结束：{{ scope.row.end_time }}</div>
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="完成时间" min-width="180" align="center">
                        <template #default="scope">
                            {{ scope.row.complete_time || '-' }}
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="奖励状态" min-width="100" align="center">
                        <template #default="scope">
                            <el-tag :type="scope.row.reward_sent ? 'success' : 'info'">
                                {{ scope.row.reward_sent ? '已发放' : '未发放' }}
                            </el-tag>
                        </template>
                    </el-table-column>
                </el-table>

                <div style="margin-top: 20px; text-align: right;">
                    <el-pagination
                        v-model:current-page="pagination.current"
                        v-model:page-size="pagination.pageSize"
                        :page-sizes="[10, 20, 50, 100]"
                        :total="pagination.total"
                        layout="total, sizes, prev, pager, next"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        prev-text="上一页"
                        next-text="下一页"
                    />
                </div>
            </el-card>

            <!-- 规则表单对话框 -->
            <el-dialog 
                :title="dialogType === 'add' ? '添加规则' : '编辑规则'" 
                v-model="ruleDialog"
                width="500px"
            >
                <el-form 
                    ref="ruleFormRef"
                    :model="ruleForm"
                    :rules="ruleRules"
                    label-width="100px"
                >
                    <el-form-item label="规则名称" prop="name">
                        <el-input v-model="ruleForm.name" placeholder="请输入规则名称"></el-input>
                    </el-form-item>
                    <el-form-item label="目标流水" prop="turnover_amount">
                        <el-input-number 
                            v-model="ruleForm.turnover_amount"
                            :min="1"
                            :precision="2"
                            :step="1000"
                            style="width: 100%"
                            placeholder="请输入目标流水金额">
                        </el-input-number>
                    </el-form-item>
                    <el-form-item label="奖励金额" prop="reward_amount">
                        <el-input-number
                            v-model="ruleForm.reward_amount"
                            :min="0.01"
                            :precision="2"
                            :step="100"
                            style="width: 100%"
                            placeholder="请输入奖励金额">
                        </el-input-number>
                    </el-form-item>
                    <el-form-item label="有效天数" prop="valid_days">
                        <el-input-number
                            v-model="ruleForm.valid_days"
                            :min="1"
                            :max="365"
                            style="width: 160px"
                        />
                        <span style="margin-left: 10px">天</span>
                    </el-form-item>
                    <el-form-item label="挑战时长" prop="challenge_duration">
                        <el-input-number
                            v-model="ruleForm.challenge_duration"
                            :min="1"
                            :max="30"
                            style="width: 160px"
                        />
                        <span style="margin-left: 10px">天</span>
                    </el-form-item>
                    <el-form-item label="可参与次数" prop="max_attempts">
                        <el-input-number
                            v-model="ruleForm.max_attempts"
                            :min="0"
                            :max="100"
                            style="width: 160px"
                        />
                        <span style="margin-left: 10px">次 (0表示不限)</span>
                    </el-form-item>
                    <el-form-item label="状态" prop="status">
                        <el-switch
                            v-model="ruleForm.status"
                            :active-value="1"
                            :inactive-value="0"
                        />
                    </el-form-item>
                </el-form>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="ruleDialog = false">取消</el-button>
                        <el-button type="primary" @click="submitRule">确定</el-button>
                    </span>
                </template>
            </el-dialog>
        </div>
    </div>

    <script>
    const { createApp, ref, reactive, onMounted, onUnmounted } = Vue;
    const { ElMessage } = ElementPlus;

    const app = createApp({
        setup() {
            // 页面加载状态
            const loading = ref(false);
            const isMobile = window.innerWidth < 768;
            
            // 添加自动刷新定时器
            let autoRefreshTimer = null;

            // 统计数据 - 初始化所有属性为0
            const stats = reactive({
                totalChallenges: 0,
                activeChallenges: 0,
                completedChallenges: 0,
                totalRewards: 0
            });

            // 规则列表
            const rules = ref([]);

            // 挑战记录
            const records = ref([]);

            // 分页
            const pagination = reactive({
                current: 1,
                pageSize: 10,
                total: 0
            });

            // 搜索表单
            const searchForm = reactive({
                merchant: '',
                status: ''
            });

            // 规则表单
            const ruleFormRef = ref(null);
            const ruleDialog = ref(false);
            const dialogType = ref('add');
            const ruleForm = ref({
                id: '',
                name: '',
                turnover_amount: 0,
                reward_amount: 0,
                valid_days: 30, // 默认30天
                challenge_duration: 7, // 默认7天
                max_attempts: 0, // 默认不限制次数
                status: 1
            });

            // 表单验证规则
            const ruleRules = {
                name: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
                turnover_amount: [{ required: true, message: '请输入目标流水', trigger: 'blur' }],
                reward_amount: [{ required: true, message: '请输入奖励金额', trigger: 'blur' }],
                valid_days: [{ required: true, message: '请设置有效天数', trigger: 'blur' }],
                challenge_duration: [{ required: true, message: '请设置挑战时长', trigger: 'blur' }]
            };

            // 添加状态选项
            const statusOptions = ref([
                { label: '全部', value: '', count: 0 },
                { label: '进行中', value: 'ongoing', count: 0 },
                { label: '已完成', value: 'completed', count: 0 },
                { label: '已失败', value: 'failed', count: 0 }
            ]);

            // 更新状态计数的方法
            const updateStatusCounts = () => {
                const counts = {
                    ongoing: 0,
                    completed: 0,
                    failed: 0
                };
                
                records.value.forEach(record => {
                    if (counts[record.status] !== undefined) {
                        counts[record.status]++;
                    }
                });

                statusOptions.value.forEach(option => {
                    if (option.value) {
                        option.count = counts[option.value] || 0;
                    } else {
                        // 全部选项的计数
                        option.count = records.value.length;
                    }
                });
            };

            // 加载数据
            const loadData = async () => {
                try {
                    loading.value = true;
                    const params = {
                        merchant: searchForm.merchant,
                        status: searchForm.status,
                        page: pagination.current,
                        pageSize: pagination.pageSize
                    };
                    
                    const res = await axios.get('/plugin/Waterchallenge/api/getData', { params });
                    if (res.data.code === 200) {
                        const data = res.data.data;
                        rules.value = data.rules;
                        records.value = data.records;
                        pagination.total = data.total;
                        Object.assign(stats, data.stats);
                        updateStatusCounts();
                    }
                    loading.value = false;
                } catch (error) {
                    loading.value = false;
                    ElMessage.error('加载数据失败');
                }
            };

            // 显示规则对话框
            const showRuleDialog = (type, row) => {
                dialogType.value = type;
                if (type === 'edit' && row) {
                    Object.assign(ruleForm.value, row);
                } else {
                    Object.assign(ruleForm.value, {
                        id: '',
                        name: '',
                        turnover_amount: 0,
                        reward_amount: 0,
                        valid_days: 30, // 默认30天
                        challenge_duration: 7, // 默认7天
                        max_attempts: 0, // 默认不限制次数
                        status: 1
                    });
                }
                ruleDialog.value = true;
            };

            // 提交规则
            const submitRule = async () => {
                if (!ruleFormRef.value) return;
                
                await ruleFormRef.value.validate(async (valid) => {
                    if (valid) {
                        try {
                            const postData = {
                                ...ruleForm.value
                            };
                            
                            const res = await axios.post('/plugin/Waterchallenge/api/saveRule', postData);
                            if (res.data.code === 200) {
                                ElMessage.success('保存成功');
                                ruleDialog.value = false;
                                loadData();
                            } else {
                                ElMessage.error(res.data.msg);
                            }
                        } catch (error) {
                            ElMessage.error('保存失败');
                        }
                    }
                });
            };

            // 删除规则
            const deleteRule = async (row) => {
                try {
                    await ElementPlus.ElMessageBox.confirm('确定要删除该规则吗？', '提示');
                    const res = await axios.post('/plugin/Waterchallenge/api/deleteRule', { id: row.id });
                    if (res.data.code === 200) {
                        ElMessage.success('删除成功');
                        loadData();
                    } else {
                        ElMessage.error(res.data.msg);
                    }
                } catch (error) {
                    if (error !== 'cancel') {
                        ElMessage.error('删除失败');
                    }
                }
            };

            // 状态变更
            const handleStatusChange = async (row) => {
                try {
                    const res = await axios.post('/plugin/Waterchallenge/api/updateStatus', {
                        id: row.id,
                        status: row.status
                    });
                    if (res.data.code === 200) {
                        ElMessage.success('状态更新成功');
                    } else {
                        row.status = row.status === 1 ? 0 : 1;
                        ElMessage.error(res.data.msg);
                    }
                } catch (error) {
                    row.status = row.status === 1 ? 0 : 1;
                    ElMessage.error('状态更新失败');
                }
            };

            // 搜索记录
            const searchRecords = () => {
                pagination.current = 1; // 重置到第一页
                loadData();
            };

            // 重置搜索
            const resetSearch = () => {
                searchForm.merchant = '';
                searchForm.status = '';
                searchRecords();
            };

            // 分页处理
            const handleSizeChange = (val) => {
                pagination.pageSize = val;
                loadData();
            };

            const handleCurrentChange = (val) => {
                pagination.current = val;
                loadData();
            };

            // 获取状态样式
            const getStatusType = (status) => {
                const types = {
                    ongoing: 'primary',
                    completed: 'success',
                    failed: 'danger'
                };
                return types[status] || 'info';
            };

            // 获取状态文本
            const getStatusText = (status) => {
                const texts = {
                    ongoing: '进行中',
                    completed: '已完成',
                    failed: '已失败'
                };
                return texts[status] || status;
            };

            // 清空记录
            const clearRecords = async () => {
                try {
                    await ElementPlus.ElMessageBox.confirm(
                        '确定要清空所有挑战记录吗？此操作不可恢复！',
                        '警告',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    );
                    
                    const res = await axios.post('/plugin/Waterchallenge/api/clearRecords');
                    if (res.data.code === 200) {
                        ElMessage.success('清空记录成功');
                        loadData(); // 重新加载数据
                    } else {
                        ElMessage.error(res.data.msg);
                    }
                } catch (error) {
                    if (error !== 'cancel') {
                        ElMessage.error('清空记录失败');
                    }
                }
            };

            onMounted(() => {
                loadData();
                
                // 设置自动刷新定时器，每60秒刷新一次数据
                autoRefreshTimer = setInterval(() => {
                    loadData();
                }, 60000);
            });
            
            // 组件卸载时清除定时器
            onUnmounted(() => {
                if (autoRefreshTimer) {
                    clearInterval(autoRefreshTimer);
                }
            });

            return {
                loading,
                isMobile,
                stats,
                rules,
                records,
                pagination,
                searchForm,
                ruleDialog,
                dialogType,
                ruleForm,
                ruleFormRef,
                ruleRules,
                showRuleDialog,
                submitRule,
                deleteRule,
                handleStatusChange,
                searchRecords,
                resetSearch,
                handleSizeChange,
                handleCurrentChange,
                getStatusType,
                getStatusText,
                clearRecords,
                statusOptions
            };
        }
    });

    // 配置 Element Plus 中文语言包
    app.use(ElementPlus, {
        locale: ElementPlusLocaleZhCn
    });

    // 挂载应用
    app.mount('#app');
    </script>
</body>
</html> 