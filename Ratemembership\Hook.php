<?php
namespace plugin\Ratemembership;

use think\facade\Db;

class Hook
{
    private $platformChannels = [1, 2, 3, 4, 5, 6, 7, 8, 9, 15];
    private $directChannels = [10, 11];
    private $merchantChannels = [14, 12, 13, 16, 17, 18, 19, 20];

    public function handle()
    {
        try {
            // 检查是否开启自动更新
            if (intval(plugconf('Ratemembership.auto_update_status') ?? 0) !== 1) {
                error_log("Ratemembership: 自动更新未开启");
                return;
            }

            // 检查更新间隔
            $lastUpdate = intval(plugconf('Ratemembership.last_update') ?? 0);
            $updateInterval = intval(plugconf('Ratemembership.update_interval') ?? 3600);
            $currentTime = time();

            if ($currentTime - $lastUpdate < $updateInterval) {
                error_log("Ratemembership: 未到更新时间，跳过执行");
                return;
            }

            // 更新所有商户等级
            $this->checkAndUpdateAllMerchants();

            // 更新最后执行时间
            plugconf('Ratemembership.last_update', strval($currentTime));
            error_log("Ratemembership Hook executed at: " . date('Y-m-d H:i:s'));
        } catch (\Exception $e) {
            error_log("Ratemembership Hook error: " . $e->getMessage() . "\n" . $e->getTraceAsString());
        }
    }

    private function checkAndUpdateAllMerchants()
    {
        try {
            // 获取计算模式
            $calcMode = plugconf('Ratemembership.calc_mode') ?? 'realtime';

            // 获取所有商户
            $merchants = Db::name('user')
                ->field(['id', 'channel_group_id'])
                ->select()
                ->toArray();

            // 获取更新周期设置
            $updateCycle = plugconf('Ratemembership.update_cycle') ?? 'monthly';
            $updateCount = 0;
            $errors = [];

            // 获取到期时间配置
            $expireTimes = plugconf('Ratemembership.expire_times') ? json_decode(plugconf('Ratemembership.expire_times'), true) : [];

            // 获取默认等级ID
            $defaultGroupId = Db::name('channel_group')
                ->whereNull('delete_time')
                ->min('id');

            foreach ($merchants as $merchant) {
                try {
                    // 检查当前等级是否开放
                    $currentGroupId = $merchant['channel_group_id'];
                    
                    // 获取会员到期时间
                    $expireTimes = plugconf('Ratemembership.expire_times') ? json_decode(plugconf('Ratemembership.expire_times'), true) : [];
                    $expireTime = $expireTimes[$merchant['id']] ?? 0;
                    $currentTime = time();

                    // 如果是付费会员且未到期，跳过等级检查
                    if ($currentGroupId != $defaultGroupId && $expireTime > $currentTime) {
                        error_log("Ratemembership: 商户ID {$merchant['id']} 为付费会员且未到期，跳过等级检查");
                        continue;
                    }

                    // 如果是付费会员且已到期，直接降级到默认等级
                    if ($currentGroupId != $defaultGroupId && $expireTime <= $currentTime) {
                        error_log("Ratemembership: 商户ID {$merchant['id']} 会员已到期，直接降级到默认等级");
                        $this->updateMerchantGroup($merchant['id'], $defaultGroupId, $updateCycle, '会员已到期，自动降级到默认等级');
                        $updateCount++;
                        continue;
                    }

                    // 以下是流水升级逻辑，只对默认等级用户生效
                    if ($currentGroupId == $defaultGroupId) {
                        if ($calcMode === 'realtime') {
                            // 实时模式：达到流水就升级
                            $amount = $this->calculateMerchantTurnover($merchant['id'], $updateCycle);
                        } else {
                            // 周期模式：根据上个周期的统计数据更新
                            if (!$this->shouldUpdateInPeriodMode($updateCycle)) {
                                continue;
                            }
                            $amount = $this->calculateLastPeriodAmount($merchant['id'], $updateCycle);
                        }

                        error_log("Ratemembership: 商户ID {$merchant['id']} 当前流水: {$amount}");

                        // 获取目标等级
                        $targetGroupId = $this->determineTargetGroup($amount, $merchant['id']);
                        
                        if ($targetGroupId === null) {
                            error_log("Ratemembership: 商户ID {$merchant['id']} 未找到合适的等级");
                            continue;
                        }

                        // 执行升级
                        if ($targetGroupId != $currentGroupId) {
                            $this->updateMerchantGroup($merchant['id'], $targetGroupId, $updateCycle, '流水达标，自动升级');
                            $updateCount++;
                        }
                    }
                } catch (\Exception $e) {
                    $errors[] = "商户ID {$merchant['id']} 处理失败: " . $e->getMessage();
                }
            }

            error_log("Ratemembership: 更新完成，成功更新 {$updateCount} 个商户");
        } catch (\Exception $e) {
            error_log("Ratemembership: 批量更新商户失败: " . $e->getMessage());
        }
    }

    private function calculateMerchantTurnover($merchantId, $updateCycle)
    {
        try {
            // 获取计算周期的起止时间
            $startDate = date('Y-m-01'); // 本月1号
            $endDate = date('Y-m-t');    // 本月最后一天

            // 根据更新周期调整日期范围
            switch ($updateCycle) {
                case 'quarterly':
                    $month = date('n');
                    $quarter = ceil($month / 3);
                    $startMonth = ($quarter - 1) * 3 + 1;
                    $startDate = date('Y-' . str_pad($startMonth, 2, '0', STR_PAD_LEFT) . '-01');
                    $endDate = date('Y-m-t', strtotime($startDate . ' +2 month'));
                    break;
                case 'yearly':
                    $startDate = date('Y-01-01');
                    $endDate = date('Y-12-31');
                    break;
            }

            // 计算流水
            $amount = Db::name('user_analysis')
                ->where([
                    ['user_id', '=', $merchantId],
                    ['date', '>=', $startDate],
                    ['date', '<=', $endDate]
                ])
                ->sum('total_amount');

            error_log("Ratemembership: 商户 {$merchantId} 流水计算结果: {$amount}, 周期: {$startDate} - {$endDate}");
            return floatval($amount);
        } catch (\Exception $e) {
            error_log("Ratemembership: calculateMerchantTurnover error: " . $e->getMessage());
            return 0;
        }
    }

    private function calculateLastMonthAmount($merchantId)
    {
        $lastMonthStart = date('Y-m-01', strtotime('-1 month'));
        $lastMonthEnd = date('Y-m-t', strtotime('-1 month'));

        return Db::name('user_analysis')
            ->where([
                ['user_id', '=', $merchantId],
                ['date', '>=', $lastMonthStart],
                ['date', '<=', $lastMonthEnd]
            ])
            ->sum('total_amount');
    }

    private function determineTargetGroup($amount, $merchantId)
    {
        try {
            // 获取流水阈值配置
            $turnovers = plugconf('Ratemembership.turnovers') ? json_decode(plugconf('Ratemembership.turnovers'), true) : [];
            
            if (empty($turnovers)) {
                error_log("Ratemembership: 流水阈值配置为空");
                return null;
            }

            // 获取默认等级（最小ID）
            $defaultGroupId = min(array_keys($turnovers));

            // 检查会员是否在有效期内
            $expireTimes = plugconf('Ratemembership.expire_times') ? json_decode(plugconf('Ratemembership.expire_times'), true) : [];
            $currentTime = time();
            $expireTime = $expireTimes[$merchantId] ?? 0;

            // 如果会员还在有效期内，保持当前等级
            if ($expireTime > $currentTime) {
                $currentGroupId = Db::name('user')->where('id', $merchantId)->value('channel_group_id');
                error_log("Ratemembership: 商户 {$merchantId} 会员在有效期内，保持当前等级 {$currentGroupId}");
                return $currentGroupId;
            }

            // 按照流水阈值从小到大排序
            asort($turnovers);
            
            // 检查是否有任何可开通的等级
            $hasAvailableGroup = false;
            $targetGroupId = $defaultGroupId;

            // 遍历流水阈值，找到最高符合条件的等级
            foreach ($turnovers as $groupId => $threshold) {
                // 检查该等级是否可以开通
                $canUpgrade = intval(plugconf("Ratemembership.can_upgrade.{$groupId}") ?? 1);
                if ($canUpgrade) {
                    $hasAvailableGroup = true;
                    // 如果当前流水大于等于阈值，更新目标等级
                    if (floatval($amount) >= floatval($threshold)) {
                        $targetGroupId = $groupId;
                        error_log("Ratemembership: 找到符合条件的等级 {$groupId}, 流水要求: {$threshold}, 当前流水: {$amount}");
                    }
                }
            }

            // 如果没有任何可开通的等级，返回默认等级
            if (!$hasAvailableGroup) {
                error_log("Ratemembership: 所有等级都不可开通，自动降级到默认等级 {$defaultGroupId}");
                return $defaultGroupId;
            }

            error_log("Ratemembership: 最终确定目标等级: {$targetGroupId}, 当前流水: {$amount}");
            return $targetGroupId;
        } catch (\Exception $e) {
            error_log("Ratemembership: determineTargetGroup error: " . $e->getMessage());
            return null;
        }
    }

    private function updateMerchantGroup($merchantId, $targetGroupId, $updateCycle, $reason = '')
    {
        if ($targetGroupId === null) {
            error_log("Ratemembership: 不需要更新商户 {$merchantId} 的等级");
            return;
        }

        Db::startTrans();
        try {
            // 获取当前等级
            $currentGroupId = Db::name('user')
                ->where('id', $merchantId)
                ->value('channel_group_id');

            // 如果等级没有变化，不需要更新
            if ($currentGroupId == $targetGroupId) {
                Db::rollback();
                return;
            }

            // 更新商户等级
            $result = Db::name('user')
                ->where('id', $merchantId)
                ->update([
                    'channel_group_id' => $targetGroupId,
                    'update_time' => time()
                ]);

            if (!$result) {
                throw new \Exception("更新商户等级失败");
            }

            // 获取最低等级（默认等级）ID
            $turnovers = plugconf('Ratemembership.turnovers') ? json_decode(plugconf('Ratemembership.turnovers'), true) : [];
            $defaultGroupId = !empty($turnovers) ? min(array_keys($turnovers)) : null;

            // 设置到期时间
            $expireTimes = plugconf('Ratemembership.expire_times') ? json_decode(plugconf('Ratemembership.expire_times'), true) : [];
            
            // 根据流水自动调整等级时，不设置到期时间
            $expireTimeLog = "根据流水自动调整";

            // 记录等级变更
            $this->updateMerchantLevel($merchantId, $currentGroupId, $targetGroupId, $reason);

            // 清除商户数据缓存
            cache('merchant_data_' . $merchantId, null);

            Db::commit();
            error_log("Ratemembership: 商户 {$merchantId} 更新等级成功，从 {$currentGroupId} 变更为 {$targetGroupId}，{$expireTimeLog}");
        } catch (\Exception $e) {
            Db::rollback();
            error_log("Ratemembership: updateMerchantGroup error: " . $e->getMessage());
            throw $e;
        }
    }

    private function getStartTimeByUpdateCycle($updateCycle)
    {
        $now = time();
        switch ($updateCycle) {
            case 'monthly':
                // 本月1号0点
                return strtotime(date('Y-m-01 00:00:00', $now));
            case 'quarterly':
                // 本季度第一个月1号0点
                $month = date('n', $now);
                $quarter = ceil($month / 3);
                $startMonth = ($quarter - 1) * 3 + 1;
                return strtotime(date('Y-' . $startMonth . '-01 00:00:00', $now));
            case 'yearly':
                // 本年1月1号0点
                return strtotime(date('Y-01-01 00:00:00', $now));
            default:
                return strtotime(date('Y-m-01 00:00:00', $now));
        }
    }

    private function calculateExpireTime($updateCycle)
    {
        $currentTime = time();
        switch ($updateCycle) {
            case 'daily':
                return $currentTime + (24 * 3600); // 1天
            case 'quarterly':
                return $currentTime + (90 * 24 * 3600); // 90天
            case 'yearly':
                return $currentTime + (365 * 24 * 3600); // 365天
            case 'monthly':
            default:
                return $currentTime + (30 * 24 * 3600); // 30天
        }
    }

    /**
     * 更新商家等级
     */
    private function updateMerchantLevel($merchantId, $fromGroupId, $toGroupId, $reason)
    {
        try {
            // 更新用户等级
            Db::name('user')
                ->where('id', $merchantId)
                ->update([
                    'channel_group_id' => $toGroupId,
                    'update_time' => time()
                ]);

            // 获取新等级的费率信息
            $platformRate = Db::name('channel_group_rule')
                ->where([
                    'group_id' => $toGroupId,
                    'channel_id' => 1  // 平台渠道
                ])
                ->value('rate');

            $directRate = Db::name('channel_group_rule')
                ->where([
                    'group_id' => $toGroupId,
                    'channel_id' => 10  // 直清合规渠道
                ])
                ->value('rate');

            // 记录等级变更
            $merchantData = [
                'level_change' => [
                    'from' => $fromGroupId,
                    'to' => $toGroupId,
                    'change_time' => time(),
                    'reason' => $reason,
                    'platform_rate' => floatval($platformRate ?? 6.00),
                    'direct_rate' => floatval($directRate ?? $platformRate ?? 6.00)
                ]
            ];

            // 更新params.php中的商家数据
            $this->updateParamsFile([
                'merchant_data' => [$merchantId => $merchantData],
                'last_update' => time()
            ]);

            error_log("商家ID: {$merchantId} 更新渠道组从 {$fromGroupId} 到 {$toGroupId}, 原因: {$reason}");
        } catch (\Exception $e) {
            error_log("更新商家等级失败: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 手动计算商家流水
     */
    public function manualCalc()
    {
        try {
            $this->checkAndUpdateMembership();
            return ['code' => 1, 'message' => '计算成功'];
        } catch (\Exception $e) {
            error_log("Ratemembership: 手动计算失败: " . $e->getMessage());
            return ['code' => 0, 'message' => '计算失败: ' . $e->getMessage()];
        }
    }

    /**
     * 更新参数文件
     */
    private function updateParamsFile($params)
    {
        try {
            $configFile = app()->getRootPath() . 'plugin/Ratemembership/params.php';
            
            // 确保配置文件存在
            if (!file_exists($configFile)) {
                file_put_contents($configFile, "<?php\n\nreturn [];\n");
            }
            
            // 读取现有配置
            $config = include $configFile;
            if (!is_array($config)) {
                $config = [];
            }

            // 更新商户数据 - 使用覆盖而不是追加
            if (isset($params['merchant_data'])) {
                foreach ($params['merchant_data'] as $merchantId => $merchantData) {
                    // 直接覆盖该商户的数据
                    $config['merchant_data'][$merchantId] = $merchantData;
                }
            }

            // 更新其他配置
            foreach ($params as $key => $value) {
                if ($key !== 'merchant_data') {
                    $config[$key] = $value;
                }
            }

            // 清理无效的商户数据
            if (isset($config['merchant_data'])) {
                // 移除数字索引的数组项
                foreach ($config['merchant_data'] as $key => $value) {
                    if (is_numeric($key) && !is_string($key)) {
                        unset($config['merchant_data'][$key]);
                    }
                }
                // 如果商户数据为空，设置为空数组
                if (empty($config['merchant_data'])) {
                    $config['merchant_data'] = [];
                }
            }

            // 写入配置文件
            $content = "<?php\n\nreturn " . var_export($config, true) . ";\n";
            if (file_put_contents($configFile, $content) === false) {
                throw new \Exception('无法写入配置文件');
            }
            
            error_log("Ratemembership: 更新配置文件成功，商户数据: " . json_encode($config['merchant_data']));
            return true;
        } catch (\Exception $e) {
            error_log("Ratemembership: 更新配置文件失败: " . $e->getMessage());
            throw $e;
        }
    }

    // 获取周期名称
    private function getCycleName($cycle)
    {
        $cycleNames = [
            'monthly' => '月度',
            'quarterly' => '季度',
            'yearly' => '年度',
            'permanent' => '永久'
        ];
        return $cycleNames[$cycle] ?? '月度';
    }

    // 检查是否应该在周期模式下更新
    private function shouldUpdateInPeriodMode($updateCycle)
    {
        $currentDay = date('j');
        $currentMonth = date('n');
        
        switch ($updateCycle) {
            case 'daily':
                // 每天0点更新
                return date('H') === '00';
            
            case 'monthly':
                // 每月1号更新
                return $currentDay === 1;
            
            case 'quarterly':
                // 每季度第一个月1号更新
                return $currentDay === 1 && in_array($currentMonth, [1, 4, 7, 10]);
            
            case 'yearly':
                // 每年1月1号更新
                return $currentDay === 1 && $currentMonth === 1;
            
            default:
                return false;
        }
    }

    // 计算上个周期的流水
    private function calculateLastPeriodAmount($merchantId, $updateCycle)
    {
        $startDate = '';
        $endDate = '';
        
        switch ($updateCycle) {
            case 'daily':
                // 昨天
                $startDate = date('Y-m-d', strtotime('-1 day'));
                $endDate = $startDate;
                break;
            
            case 'monthly':
                // 上个月
                $startDate = date('Y-m-01', strtotime('-1 month'));
                $endDate = date('Y-m-t', strtotime('-1 month'));
                break;
            
            case 'quarterly':
                // 上个季度
                $currentMonth = date('n');
                $currentQuarter = ceil($currentMonth / 3);
                $lastQuarter = $currentQuarter - 1;
                if ($lastQuarter === 0) {
                    $lastQuarter = 4;
                    $year = date('Y') - 1;
                } else {
                    $year = date('Y');
                }
                $startMonth = ($lastQuarter - 1) * 3 + 1;
                $startDate = date("$year-" . str_pad($startMonth, 2, '0', STR_PAD_LEFT) . '-01');
                $endDate = date('Y-m-t', strtotime($startDate . ' +2 month'));
                break;
            
            case 'yearly':
                // 上一年
                $lastYear = date('Y') - 1;
                $startDate = "$lastYear-01-01";
                $endDate = "$lastYear-12-31";
                break;
        }

        return Db::name('user_analysis')
            ->where([
                ['user_id', '=', $merchantId],
                ['date', '>=', $startDate],
                ['date', '<=', $endDate]
            ])
            ->sum('total_amount');
    }
}
