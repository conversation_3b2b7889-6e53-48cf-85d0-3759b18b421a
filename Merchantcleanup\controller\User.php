<?php

namespace plugin\Merchantcleanup\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Log;
use think\facade\Db;

class User extends BasePlugin {

    protected $scene = ['user'];  // 只允许普通用户访问
    
    protected $noNeedLogin = [];

    // 回收站页面
    public function index() {
        $config = include app()->getRootPath() . 'plugin/Merchantcleanup/params.php';
        if (!isset($config['cleanup_config']['status']) || !$config['cleanup_config']['status']) {
            return $this->error('回收站功能已关闭');
        }
        return View::fetch();
    }

    // 获取回收站卡密列表
    public function getRecycledCards() {
        try {
            $page = input('page/d', 1);
            $limit = input('limit/d', 10);
            $offset = ($page - 1) * $limit;
            $search = xss_safe(input('search/s', ''));
            $dateRange = input('dateRange/a', []);

            // 收集所有分表的结果
            $allCards = [];
            $totalCount = 0;
            
            // 直接查询卡密表获取商品ID
            $goodsIds = Db::name('goods_card_storage_0')
                ->where('user_id', $this->user->id)
                ->whereNotNull('delete_time')
                ->group('goods_id')
                ->column('goods_id');
                
            // 如果没有商品ID，尝试从其他分表查询
            if (empty($goodsIds)) {
                // 这里可以查询其他已知存在的分表
                // 或者遍历一些可能的分表后缀
                for ($i = 1; $i <= 10; $i++) {
                    $tableName = 'goods_card_storage_' . $i;
                    if (Db::query("SHOW TABLES LIKE '".config('database.connections.mysql.prefix').$tableName."'")) {
                        $ids = Db::name($tableName)
                            ->where('user_id', $this->user->id)
                            ->whereNotNull('delete_time')
                            ->group('goods_id')
                            ->column('goods_id');
                        
                        if (!empty($ids)) {
                            $goodsIds = array_merge($goodsIds, $ids);
                        }
                    }
                }
            }
                
            // 得到所有需要查询的表
            $tablesToQuery = [];
            foreach ($goodsIds as $goodsId) {
                $suffix = $this->goods_card_storage_suffix($goodsId);
                $tableName = 'goods_card_storage' . $suffix;
                if (!in_array($tableName, $tablesToQuery)) {
                    $tablesToQuery[] = $tableName;
                }
            }
            
            // 如果没有找到表，添加默认表
            if (empty($tablesToQuery)) {
                $tablesToQuery[] = 'goods_card_storage_0';
            }
            
            // 对每个表进行查询并合并结果
            foreach ($tablesToQuery as $tableName) {
                // 构建基础查询
                $query = Db::name($tableName)
                    ->alias('c')
                    ->join('goods g', 'c.goods_id = g.id')
                    ->where('c.user_id', $this->user->id)
                    ->whereNotNull('c.delete_time');  // 只查询已删除的卡密

                // 添加时间范围筛选
                if (!empty($dateRange) && count($dateRange) == 2) {
                    $startDate = strtotime($dateRange[0] . ' 00:00:00');
                    $endDate = strtotime($dateRange[1] . ' 23:59:59');
                    $query->whereBetweenTime('c.delete_time', $startDate, $endDate);
                }

                // 添加卡密搜索
                if (!empty($search)) {
                    $query->where('c.secret', 'like', "%{$search}%");
                }

                // 先获取此表中的总记录数
                $tableTotal = $query->count();
                $totalCount += $tableTotal;
                
                // 如果此表中有数据，获取分页数据
                if ($tableTotal > 0) {
                    $tableCards = $query->field([
                            'c.id',
                            'g.name as goods_name',
                            'g.image as goods_image',
                            'c.secret as card_info',
                            'FROM_UNIXTIME(c.delete_time) as delete_time',
                            'c.status',
                            'c.goods_id'  // 添加商品ID以便后续处理
                        ])
                        ->select()
                        ->toArray();
                    
                    $allCards = array_merge($allCards, $tableCards);
                }
            }
            
            // 对合并后的结果进行排序（按删除时间降序）
            usort($allCards, function($a, $b) {
                return strtotime($b['delete_time']) - strtotime($a['delete_time']);
            });
            
            // 手动分页
            $cards = array_slice($allCards, $offset, $limit);

            // 处理状态显示和时间格式
            foreach ($cards as &$card) {
                $card['status_text'] = $card['status'] == 0 ? '未售出' : '已售出';
                // 格式化时间
                $card['delete_time'] = date('Y-m-d H:i:s', strtotime($card['delete_time']));
                // XSS防护
                $card['goods_name'] = xss_safe($card['goods_name']);
                $card['card_info'] = xss_safe($card['card_info']);
                // 添加表名信息（用于后续删除操作）
                $card['table_suffix'] = $this->goods_card_storage_suffix($card['goods_id']);
            }

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $cards,
                'total' => $totalCount
            ]);

        } catch (\Exception $e) {
            Log::error('获取回收站卡密失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取数据失败：' . $e->getMessage()]);
        }
    }

    // 删除单个卡密
    public function deleteCard() {
        try {
            $id = input('id/d');
            $goodsId = input('goods_id/d');
            
            // 如果没有提供商品ID，需要先查询
            if (!$goodsId) {
                // 直接遍历所有可能的分表查找此卡密
                $foundCard = null;
                
                // 遍历可能的表后缀
                for ($i = 0; $i <= 10; $i++) {
                    $tableName = 'goods_card_storage_' . $i;
                    
                    try {
                        $card = Db::name($tableName)
                            ->where('id', $id)
                            ->where('user_id', $this->user->id)
                            ->find();
                            
                        if ($card) {
                            $goodsId = $card['goods_id'];
                            break;
                        }
                    } catch (\Exception $e) {
                        // 表不存在，继续下一个
                        continue;
                    }
                }
                
                if (!$goodsId) {
                    return json(['code' => 400, 'msg' => '找不到要删除的卡密']);
                }
            }
            
            $suffix = $this->goods_card_storage_suffix($goodsId);
            $tableName = 'goods_card_storage' . $suffix;
            
            $affected = Db::name($tableName)
                ->where('id', $id)
                ->where('user_id', $this->user->id)
                ->whereNotNull('delete_time')
                ->delete();

            if ($affected) {
                return json(['code' => 200, 'msg' => '删除成功']);
            } else {
                return json(['code' => 400, 'msg' => '删除失败或卡密不存在']);
            }
        } catch (\Exception $e) {
            Log::error('删除卡密失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }

    // 清空回收站
    public function cleanupRecycled() {
        try {
            $totalAffected = 0;
            
            // 直接查询可能的分表
            $tablesToQuery = [];
            
            // 遍历可能的表后缀
            for ($i = 0; $i <= 10; $i++) {
                $tableName = 'goods_card_storage_' . $i;
                
                try {
                    // 检查表是否存在且有数据
                    $count = Db::name($tableName)
                        ->where('user_id', $this->user->id)
                        ->whereNotNull('delete_time')
                        ->count();
                        
                    if ($count > 0) {
                        $tablesToQuery[] = $tableName;
                    }
                } catch (\Exception $e) {
                    // 表不存在，继续下一个
                    continue;
                }
            }
            
            // 如果没有找到表，添加默认表
            if (empty($tablesToQuery)) {
                $tablesToQuery[] = 'goods_card_storage_0';
            }
            
            // 对每个表进行删除操作
            foreach ($tablesToQuery as $tableName) {
                try {
                    $affected = Db::name($tableName)
                        ->where('user_id', $this->user->id)
                        ->whereNotNull('delete_time')
                        ->delete();
                        
                    $totalAffected += $affected;
                } catch (\Exception $e) {
                    // 记录错误但继续处理其他表
                    Log::error('清空表 '.$tableName.' 失败：'.$e->getMessage());
                }
            }

            return json([
                'code' => 200,
                'msg' => '清空成功',
                'data' => [
                    'affected' => $totalAffected
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('清空回收站失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '清空失败：' . $e->getMessage()]);
        }
    }

    // 批量删除卡密
    public function batchDeleteCards() {
        try {
            $cardsData = input('cardsData/a', []);
            
            if (empty($cardsData)) {
                return json(['code' => 400, 'msg' => '请选择要删除的卡密']);
            }

            Db::startTrans();
            try {
                $totalAffected = 0;
                
                // 按表分组整理数据
                $cardsByTable = [];
                foreach ($cardsData as $card) {
                    $id = $card['id'];
                    $goodsId = $card['goods_id'];
                    $suffix = $this->goods_card_storage_suffix($goodsId);
                    $tableName = 'goods_card_storage' . $suffix;
                    
                    if (!isset($cardsByTable[$tableName])) {
                        $cardsByTable[$tableName] = [];
                    }
                    
                    $cardsByTable[$tableName][] = $id;
                }
                
                // 对每个表执行批量删除
                foreach ($cardsByTable as $tableName => $ids) {
                    $affected = Db::name($tableName)
                        ->whereIn('id', $ids)
                        ->where('user_id', $this->user->id)
                        ->whereNotNull('delete_time')
                        ->delete();
                        
                    $totalAffected += $affected;
                }

                if ($totalAffected > 0) {
                    Db::commit();
                    return json([
                        'code' => 200, 
                        'msg' => '成功删除 ' . $totalAffected . ' 个卡密'
                    ]);
                } else {
                    Db::rollback();
                    return json(['code' => 400, 'msg' => '删除失败或卡密不存在']);
                }
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('批量删除卡密失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }

    // 添加分表后缀计算函数
    protected function goods_card_storage_suffix($goods_id) {
        return "_" . (intval($goods_id / 4000));
    }
}
