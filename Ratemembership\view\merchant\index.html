<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>会员等级/费率公示</title>
    <!-- 使用本地资源 -->

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <style>
        /* 基础样式 */
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Aria<PERSON>, sans-serif;
            background-color: #f5f7fa;
        }

        .page-container {
            padding: 15px;
            max-width: 1200px;
            margin: 0 auto;
        }

        /* 响应式标题栏 */
        .page-header {
            margin-bottom: 20px;
            padding: 15px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
        }

        .page-title {
            font-size: 18px;
            color: #303133;
            margin: 0;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        /* 响应式表格 */
        .el-table {
            margin-bottom: 20px;
            --el-table-border-color: #EBEEF5;
            --el-table-header-background-color: #F5F7FA;
        }

        /* 信息卡片样式 */
        .info-card {
            background: #fff;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
        }

        .card-header {
            padding: 15px;
            border-bottom: 1px solid #EBEEF5;
        }

        /* 移动端优化 */
        @media screen and (max-width: 768px) {
            .page-container {
                padding: 10px;
            }

            .page-header {
                padding: 10px;
            }

            .page-title {
                font-size: 16px;
            }

            .wallet-tip {
                display: block;
                margin-top: 8px;
            }

            /* 移动端表格样式优化 */
            .el-table {
                font-size: 13px;
            }

            .el-table th {
                padding: 6px 0;
            }

            .el-table td {
                padding: 6px 0;
            }

            /* 移动端费率显示优化 */
            .rate-info {
                display: flex;
                flex-direction: column;
                gap: 4px;
            }

            .rate-item {
                white-space: nowrap;
            }

            /* 移动端按钮组样式 */
            .button-group {
                display: flex;
                flex-direction: column;
                gap: 8px;
            }

            .el-select {
                width: 100%;
                margin-bottom: 8px;
            }

            .el-button {
                width: 100%;
            }

            /* 移动端描述列表样式 */
            .el-descriptions__label {
                width: 100px;
            }

            .el-descriptions__content {
                padding: 8px 12px;
            }
        }

        /* 通用样式优化 */
        .disabled-text {
            color: #909399;
            font-size: 13px;
        }

        .wallet-tip {
            color: #F56C6C;
            font-size: 13px;
            font-weight: normal;
        }

        .wallet-link {
            color: #409EFF;
            text-decoration: none;
            cursor: pointer;
        }

        .expire-time {
            font-size: 13px;
            color: #606266;
            margin-bottom: 5px;
        }

        .amount-text {
            font-size: 15px;
            color: #303133;
            font-weight: 500;
        }

        /* 优化表格内容布局 */
        .rate-content {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        @media screen and (min-width: 769px) {
            .rate-content {
                flex-direction: row;
                gap: 10px;
            }
        }

        /* 文本样式 */
        .time-text {
            color: #666;
            font-size: 14px;
        }

        .level-text {
            color: #67C23A;
            font-weight: bold;
        }

        .next-level-text {
            color: #E6A23C;
            font-weight: bold;
        }

        .default-group-text {
            color: #909399;
            font-size: 12px;
        }

        /* 钱包相关 */
        .wallet-tip {
            color: #F56C6C;
            font-size: 13px;
            font-weight: normal;
        }

        .wallet-link:hover {
            color: #66b1ff;
        }

        /* 表格样式 */
        .el-table th {
            background-color: #f5f7fa;
            color: #606266;
            font-weight: 500;
            padding: 8px 0;
        }

        .el-table td {
            padding: 8px 0;
        }

        /* 描述列表样式 */
        .el-descriptions__label {
            width: 120px;
            color: #606266;
        }

        .el-descriptions__content {
            padding: 12px 16px;
        }

        /* 到期时间样式 */
        .expire-warning {
            color: #F56C6C;
            font-weight: bold;
        }

        /* 添加小字样式 */
        .small-text {
            font-size: 12px;
            color: #909399;
        }
        
        /* 链接样式 */
        .link-text {
            color: #409EFF;
            text-decoration: none;
            cursor: pointer;
        }
        
        .link-text:hover {
            color: #66b1ff;
        }

        .level-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .highest-level-text {
            color: #67C23A;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="page-container">
            <!-- 标题栏 -->
            <div class="page-header">
                <h2 class="page-title">
                    会员等级/费率公示
                    <span class="wallet-tip">
                        提前开通指定等级，您需要前往<a href="/merchant/user/wallet" class="wallet-link" target="_blank">【运营钱包】</a>充值对应金额
                    </span>
                </h2>
            </div>

            <!-- 等级列表 -->
            <el-table :data="channelGroups" border style="width: 100%">
                <el-table-column 
                    prop="name" 
                    label="会员等级" 
                    width="90">
                </el-table-column>
                <el-table-column 
                    label="费率" 
                    min-width="220">
                    <template #header>
                        <span>
                            费率
                            <span class="small-text">（详情前往</span>
                            <a href="/merchant/shop/payment" target="_blank" class="link-text">支付方式</a>
                            <span class="small-text">查看）</span>
                        </span>
                    </template>
                    <template #default="scope">
                        <div class="rate-content">
                            <div class="rate-item">平台：{{ scope.row.platform_rate }}%</div>
                            <div class="rate-item">直清合规：{{ scope.row.direct_rate }}%</div>
                            <div class="rate-item">商户自定义：{{ scope.row.merchant_rate }}%</div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column 
                    label="成交流水" 
                    width="100" 
                    align="right">
                    <template #default="scope">
                        {{ formatAmount(scope.row.turnover) }}
                    </template>
                </el-table-column>
                <el-table-column 
                    label="费用（元/月）" 
                    width="120" 
                    align="right">
                    <template #default="scope">
                        {{ formatAmount(scope.row.price) }}
                    </template>
                </el-table-column>
                <el-table-column 
                    label="到期/操作" 
                    width="180" 
                    align="center">
                    <template #default="scope">
                        <template v-if="scope.row.id !== defaultGroupId">
                            <template v-if="!scope.row.can_upgrade">
                                <el-tooltip
                                    effect="dark"
                                    :content="scope.row.disabled_message"
                                    placement="top">
                                    <span class="disabled-text">暂不开放</span>
                                </el-tooltip>
                            </template>
                            <template v-else>
                                <div class="button-group">
                                    <template v-if="scope.row.is_current">
                                        <div class="expire-time" :class="{ 'expire-warning': isExpireSoon(scope.row.expire_time) }">
                                            {{ formatDate(scope.row.expire_time) }}
                                        </div>
                                        <el-select v-model="scope.row.selected_cycle" size="small" placeholder="请选择周期">
                                            <el-option 
                                                label="包月" 
                                                value="monthly">
                                                <span>包月</span>
                                                <span style="float: right; color: #8492a6; font-size: 13px">
                                                    {{ formatAmount(scope.row.price) }}元/月
                                                </span>
                                            </el-option>
                                            <el-option 
                                                v-if="scope.row.quarterly_price > 0"
                                                label="包季" 
                                                value="quarterly">
                                                <span>包季</span>
                                                <span style="float: right; color: #8492a6; font-size: 13px">
                                                    {{ formatAmount(scope.row.quarterly_price) }}元/季
                                                </span>
                                            </el-option>
                                            <el-option 
                                                v-if="scope.row.yearly_price > 0"
                                                label="包年" 
                                                value="yearly">
                                                <span>包年</span>
                                                <span style="float: right; color: #8492a6; font-size: 13px">
                                                    {{ formatAmount(scope.row.yearly_price) }}元/年
                                                </span>
                                            </el-option>
                                        </el-select>
                                        <el-button 
                                            type="primary" 
                                            size="small" 
                                            @click="handleUpgrade(scope.row.id, scope.row.selected_cycle)"
                                            :disabled="!scope.row.can_pay">
                                            {{ getButtonText(scope.row) }}
                                        </el-button>
                                    </template>
                                    <template v-else>
                                        <el-select v-model="scope.row.selected_cycle" size="small" placeholder="请选择周期">
                                            <el-option 
                                                label="包月" 
                                                value="monthly">
                                                <span>包月</span>
                                                <span style="float: right; color: #8492a6; font-size: 13px">
                                                    {{ formatAmount(scope.row.price) }}元/月
                                                </span>
                                            </el-option>
                                            <el-option 
                                                v-if="scope.row.quarterly_price > 0"
                                                label="包季" 
                                                value="quarterly">
                                                <span>包季</span>
                                                <span style="float: right; color: #8492a6; font-size: 13px">
                                                    {{ formatAmount(scope.row.quarterly_price) }}元/季
                                                </span>
                                            </el-option>
                                            <el-option 
                                                v-if="scope.row.yearly_price > 0"
                                                label="包年" 
                                                value="yearly">
                                                <span>包年</span>
                                                <span style="float: right; color: #8492a6; font-size: 13px">
                                                    {{ formatAmount(scope.row.yearly_price) }}元/年
                                                </span>
                                            </el-option>
                                        </el-select>
                                        <el-button 
                                            type="primary" 
                                            size="small" 
                                            @click="handleUpgrade(scope.row.id, scope.row.selected_cycle)"
                                            :disabled="!scope.row.can_pay">
                                            {{ getButtonText(scope.row) }}
                                        </el-button>
                                    </template>
                                </div>
                            </template>
                        </template>
                        <span v-else class="default-group-text">默认等级</span>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 商家流水信息 -->
            <el-card class="info-card">
                <template #header>
                    <div class="card-header">
                        <span>我的流水信息</span>
                    </div>
                </template>
                <el-descriptions :column="1" border>
                    <el-descriptions-item label="本月流水">
                        <span class="amount-text">￥{{ formatAmount(merchantData.this_month_amount || 0) }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="上月流水">
                        <span class="amount-text">￥{{ formatAmount(merchantData.last_month_amount || 0) }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="当前等级">
                        <div class="level-info">
                            <span class="level-text">{{ merchantData.current_group }}</span>
                            <template v-if="merchantData.is_highest_level">
                                <span class="highest-level-text">（恭喜您，已经是最高等级了）</span>
                            </template>
                            <template v-else>
                                <span class="next-level-text">
                                    <template v-if="merchantData.calc_mode === 'period' && merchantData.next_level_amount < 0">
                                        （已达到升级条件，将在 {{ merchantData.next_update_time }} 更新等级）
                                    </template>
                                    <template v-else>
                                        {{ merchantData.next_level_amount === -5000 ? 
                                            '（暂未开放升级）' : 
                                            `（距离下一等级还需要流水：￥${formatAmount(merchantData.next_level_amount)}）` 
                                        }}
                                    </template>
                                </span>
                            </template>
                        </div>
                    </el-descriptions-item>
                    <el-descriptions-item label="本月统计周期">
                        {{ merchantData.calc_start_time }} 至 {{ merchantData.calc_end_time }}
                    </el-descriptions-item>
                    <el-descriptions-item label="上月统计周期">
                        {{ merchantData.last_month_start }} 至 {{ merchantData.last_month_end }}
                    </el-descriptions-item>
                    <el-descriptions-item label="更新时间">
                        {{ merchantData.last_update_time }}
                    </el-descriptions-item>
                </el-descriptions>
            </el-card>
        </div>
    </div>

    <script>
        const app = Vue.createApp({
            setup() {
                const { ref, onMounted, onUnmounted } = Vue;
                const merchantData = ref({});
                const channelGroups = ref([]);
                const platformMoney = ref(0);
                const defaultGroupId = ref(null);
                let dataRefreshTimer = null;
                let lastTurnoverHash = '';

                const formatAmount = (amount) => {
                    return Number(amount).toLocaleString('zh-CN', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    });
                };

                const formatDateWithDash = (dateStr) => {
                    if (!dateStr) return '';
                    return dateStr.replace(/\//g, '-');
                };

                const formatDate = (timestamp) => {
                    if (!timestamp) return '永久';
                    const date = new Date(timestamp * 1000);
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    const hour = String(date.getHours()).padStart(2, '0');
                    const minute = String(date.getMinutes()).padStart(2, '0');
                    const second = String(date.getSeconds()).padStart(2, '0');
                    return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
                };

                const isExpireSoon = (timestamp) => {
                    if (!timestamp) return false;
                    const now = Math.floor(Date.now() / 1000);
                    const sevenDays = 7 * 24 * 60 * 60;
                    return timestamp - now <= sevenDays;
                };

                // 修改获取数据的方法
                const getMerchantData = async (showError = true) => {
                    try {
                        const res = await axios.post('/plugin/Ratemembership/merchant/getMerchantData');
                        if (res.data.code === 200) {
                            // 检查流水或等级是否有变化
                            const currentData = res.data.data;
                            merchantData.value = currentData;
                            // 每次获取数据后都刷新等级列表
                            await getChannelGroups();
                        } else {
                            throw new Error(res.data.msg);
                        }
                    } catch (error) {
                        if (showError) {
                            ElementPlus.ElMessage.error('获取商户数据失败');
                        }
                        console.error('获取商户数据失败:', error);
                    }
                };

                const getButtonText = (row) => {
                    if (!row.can_upgrade) {
                        return row.disabled_message || '不可开通';
                    }
                    if (!row.can_pay) {
                        return '余额不足';
                    }
                    return row.is_current ? '续费' : '开通';
                };

                // 修改定时刷新函数
                const startDataRefresh = () => {
                    // 每2秒刷新一次数据
                    dataRefreshTimer = setInterval(async () => {
                        await getMerchantData(false);
                    }, 2000);
                };

                const handleUpgrade = async (groupId, cycle) => {
                    try {
                        // 获取当前选择的等级信息
                        const currentGroup = channelGroups.value.find(g => g.id === groupId);
                        if (!currentGroup) return;
                        
                        // 根据周期获取对应价格
                        let price = 0;
                        switch (cycle) {
                            case 'yearly':
                                price = currentGroup.yearly_price;
                                break;
                            case 'quarterly':
                                price = currentGroup.quarterly_price;
                                break;
                            default:
                                price = currentGroup.price;
                        }

                        // 如果是默认等级，显示确认弹窗
                        if (groupId === defaultGroupId.value) {
                            const confirmResult = await ElementPlus.ElMessageBox.confirm(
                                '切换到默认等级后，您将失去当前等级的所有权益，是否继续？',
                                '确认切换',
                                {
                                    confirmButtonText: '确认',
                                    cancelButtonText: '取消',
                                    type: 'warning',
                                }
                            );
                            
                            if (confirmResult !== 'confirm') {
                                return;
                            }
                        } else {
                            // 非默认等级，显示价格确认弹窗
                            const cycleName = getCycleName(cycle);
                            const confirmResult = await ElementPlus.ElMessageBox.confirm(
                                `确认${currentGroup.is_current ? '续费' : '开通'}${currentGroup.name}？<br>` +
                                `费用：${formatAmount(price)}元/${cycleName}`,
                                `确认${currentGroup.is_current ? '续费' : '开通'}`,
                                {
                                    confirmButtonText: '确认',
                                    cancelButtonText: '取消',
                                    dangerouslyUseHTMLString: true,
                                    type: 'info',
                                }
                            );
                            
                            if (confirmResult !== 'confirm') {
                                return;
                            }
                        }

                        const res = await axios.post('/plugin/Ratemembership/merchant/upgradeLevel', {
                            group_id: groupId,
                            cycle: cycle,
                            is_renew: currentGroup.is_current
                        });

                        if (res.data.code === 200) {
                            ElementPlus.ElMessage.success(res.data.msg);
                            // 立即刷新数据
                            await Promise.all([
                                getMerchantData(),
                                getChannelGroups()
                            ]);
                        } else {
                            throw new Error(res.data.msg);
                        }
                    } catch (error) {
                        if (error.toString().includes('cancel')) {
                            return;
                        }
                        ElementPlus.ElMessage.error('升级失败: ' + error.message);
                    }
                };

                const getCycleName = (cycle) => {
                    const cycleNames = {
                        'monthly': '包月',
                        'quarterly': '包季',
                        'yearly': '包年'
                    };
                    return cycleNames[cycle] || '包月';
                };

                const getChannelGroups = async () => {
                    try {
                        const res = await axios.post('/plugin/Ratemembership/merchant/channelGroups');
                        if (res.data.code === 200) {
                            channelGroups.value = res.data.data;
                            // 设置默认分组ID
                            defaultGroupId.value = res.data.default_group_id;
                        } else {
                            throw new Error(res.data.msg);
                        }
                    } catch (error) {
                        ElementPlus.ElMessage.error('获取等级列表失败');
                    }
                };

                onMounted(() => {
                    // 初始化数据
                    Promise.all([
                        getMerchantData(),
                        getChannelGroups()
                    ]);
                    // 启动定时刷新
                    startDataRefresh();
                });

                onUnmounted(() => {
                    if (dataRefreshTimer) {
                        clearInterval(dataRefreshTimer);
                    }
                });

                return {
                    merchantData,
                    channelGroups,
                    platformMoney,
                    formatAmount,
                    formatDateWithDash,
                    formatDate,
                    isExpireSoon,
                    handleUpgrade,
                    defaultGroupId,
                    getButtonText,
                    getCycleName,
                };
            }
        });

        app.use(ElementPlus);
        app.mount('#app');
    </script>
</body>
</html>