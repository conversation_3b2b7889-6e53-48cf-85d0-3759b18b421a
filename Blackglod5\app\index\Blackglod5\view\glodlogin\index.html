<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$title}</title>
    
    {if !empty($favicon)}
    <link rel="shortcut icon" href="{$favicon}" type="image/x-icon">
    {/if}
    <link rel="stylesheet" href="/assets/plugin/Blackglod/plugin/Blackglod/css/all.min.css">

    <style>
        /* 移除防复制的CSS */
        * {
            -webkit-touch-callout: initial;
            -webkit-user-select: initial;
            -khtml-user-select: initial;
            -moz-user-select: initial;
            -ms-user-select: initial;
            user-select: initial;
        }
        
        /* 允许输入框可选 */
        input, textarea {
            -webkit-user-select: text;
            -khtml-user-select: text;
            -moz-user-select: text;
            -ms-user-select: text;
            user-select: text;
        }

        /* 复用首页的基础样式 */
        @font-face {
            font-family: 'Noto Sans SC';
            src: url('/assets/plugin/Blackglod/plugin/Blackglod/fonts/k3kCo84MPvpLmixcA63oeAL7Iqp5IZJF9bmaG-3FnYw.ttf') format('truetype');
            font-weight: 400;
            font-style: normal;
        }

        :root {
            --primary: #C8A675;
            --primary-dark: #a88a5c;
            --primary-light: #e0c4a3;
            --dark: #000000;
            --light: #ffffff;
            --gray: #666666;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: var(--dark);
            color: var(--light);
            line-height: 1.6;
            overflow-x: hidden;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* 复用首页的头部导航样式 */
        .header {
            padding: 1rem 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            transition: var(--transition);
        }

        .container {
            width: 90%;
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
        }

        .header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 2rem;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            color: var(--light);
            text-decoration: none;
        }

        .logo-img {
            height: 40px;
            width: auto;
        }

        /* 登录表单容器 */
        .login-container {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            position: relative;
            z-index: 1;
            margin-top: 60px;
        }

        .login-box {
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(10px);
            padding: 3rem;
            border-radius: 15px;
            width: 100%;
            max-width: 400px;
            border: 1px solid rgba(200, 166, 117, 0.1);
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
        }

        .login-title {
            text-align: center;
            margin-bottom: 2rem;
            font-size: 1.8rem;
            background: linear-gradient(135deg, var(--primary-light), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--primary-light);
            font-size: 0.9rem;
        }

        .form-group input {
            width: 100%;
            padding: 0.8rem 1rem;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(200, 166, 117, 0.2);
            border-radius: 8px;
            color: var(--light);
            font-size: 1rem;
            transition: var(--transition);
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--primary);
            background: rgba(255, 255, 255, 0.1);
        }

        .remember-forgot {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .remember-me {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--gray);
        }

        .forgot-password {
            color: var(--primary);
            text-decoration: none;
            transition: var(--transition);
        }

        .login-btn {
            width: 100%;
            padding: 0.8rem;
            background: var(--primary);
            border: none;
            border-radius: 8px;
            color: var(--dark);
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
        }

        .login-btn:hover {
            background: var(--primary-light);
        }

        /* 复用首页的页脚样式 */
        .footer {
            padding: 4rem 0;
            background: rgba(0, 0, 0, 0.95);
            position: relative;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 3rem;
            margin-bottom: 3rem;
        }

        .copyright {
            text-align: center;
            color: var(--gray);
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* 星星背景 */
        .stars-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 0;
            pointer-events: none;
        }

        .star {
            position: absolute;
            background: var(--primary);
            border-radius: 50%;
            opacity: 0;
            animation: twinkle var(--duration) ease-in-out infinite;
            animation-delay: var(--delay);
        }

        @keyframes twinkle {
            0%, 100% { opacity: 0; }
            50% { opacity: 0.8; }
        }
    </style>
</head>
<body>
    <div class="stars-container"></div>
    <div class="background-animation"></div>

    <header class="header">
        <div class="container">
            <a href="/" class="logo">
                {if !empty($logo)}
                <img src="{$logo}" alt="{$siteName}" class="logo-img">
                {else}
                <i class="fas fa-credit-card"></i>
                {/if}
                <span>{$siteName}</span>
            </a>
        </div>
    </header>

    <div class="login-container">
        <div class="login-box">
            <h2 class="login-title">欢迎来到 九五云商</h2>
            <form id="loginForm" method="post">
                <div class="form-group">
                    <label for="username">账号</label>
                    <input type="text" id="username" name="username" placeholder="请输入用户名" required>
                </div>
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" name="password" placeholder="请输入密码" required>
                </div>
                <div class="remember-forgot">
                    <label class="remember-me">
                        <input type="checkbox" name="remember" id="remember" value="1">
                        <span>记住我</span>
                    </label>
                    <a href="/merchant/forgot-password" class="forgot-password">忘记密码？</a>
                </div>
                <button type="submit" class="login-btn">登 录</button>
            </form>
        </div>
    </div>

    <footer class="footer">
        <div class="container">
            <div class="copyright">
                {$siteName} - 版权所有 © 2022-至今 
                {if !empty($icpNumber)}
                <a href="https://beian.miit.gov.cn/" target="_blank">{$icpNumber}</a>
                {/if}
                {if !empty($gaNumber)}
                <a href="{$icpCert}" target="_blank">{$gaNumber}</a>
                {/if}
            </div>
        </div>
    </footer>

    <script>
        // 创建星星背景
        function createStars() {
            const starsContainer = document.querySelector('.stars-container');
            const starCount = 100;

            for (let i = 0; i < starCount; i++) {
                const star = document.createElement('div');
                star.className = 'star';
                
                star.style.left = `${Math.random() * 100}%`;
                star.style.top = `${Math.random() * 100}%`;
                
                const size = Math.random() * 3;
                star.style.width = `${size}px`;
                star.style.height = `${size}px`;
                
                star.style.setProperty('--delay', `${Math.random() * 5}s`);
                star.style.setProperty('--duration', `${Math.random() * 3 + 2}s`);
                
                starsContainer.appendChild(star);
            }
        }

        createStars();

        // 修改表单验证和提交逻辑
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault(); // 阻止表单默认提交
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            
            if (!username || !password) {
                alert('请填写完整的登录信息');
                return;
            }
            
            // 发送AJAX请求
            fetch('/merchant/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                },
                body: new URLSearchParams({
                    username: username,
                    password: password,
                    remember: document.getElementById('remember').checked ? 1 : 0
                }).toString()
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.code === 1) {
                    // 保存token到localStorage
                    localStorage.setItem('merchant_token', data.data.merchant_token);
                    // 登录成功后跳转
                    window.location.href = '/merchant/dashboard/workplace';
                } else {
                    alert(data.msg || '登录失败');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('登录请求失败，请稍后重试');
            });
        });
    </script>
</body>
</html> 