<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <meta http-equiv="Cache-Control" content="max-age=31536000">
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>定时和手动删除平台总收款</title>
    <style>
        /* 加载动画样式 */
        #loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #fff;
            z-index: 9999;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(0, 0, 0, 0.1);
            border-top-color: #3498db;
            border-radius: 50%;
            animation: spin 1s ease infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    </style>
</head>

<body>
<!-- 加载动画容器 -->
<div id="loading">
    <div class="spinner"></div>
</div>

<!-- Vue 应用挂载点 -->
<div id="app" style="display: none">

    <el-card shadow="never">

        <el-form :model="form" label-width="auto">
            <el-form-item label="隐藏0商品货源开关：">
                <el-radio-group v-model="form.status">
                    <el-radio :value="0">关闭</el-radio>
                    <el-radio :value="1">开启</el-radio>
                </el-radio-group>
            </el-form-item>

            <!-- 商品下架的开启状态 -->
            <el-form-item label="下架缺卡的商品开关：">
                <el-radio-group v-model="form.disableStatus">
                    <el-radio :value="0">关闭</el-radio>
                    <el-radio :value="1">开启</el-radio>
                </el-radio-group>
            </el-form-item>

            <el-form-item label="定时触发设置：">
                <el-time-select v-model="form.hideTime" start="00:00" step="00:01" end="23:59" placeholder="请选择隐藏时间" />
            </el-form-item>

        </el-form>

        <p style="text-align: center;margin: 0 auto;margin-top: 16px;">
            <el-button type="primary" :loading="isLoading" @click="save">
                保存
            </el-button>
        </p>
    </el-card>

</div>

<script src="/static/others/vue/vue.global.prod.js"></script>
<script src="/static/others/element-plus/index.full.min.js"></script>
<script src="/static/others/axios/axios.min.js"></script>

<script type="module">
    const { ref, reactive } = Vue;
    const { ElMessage } = ElementPlus;

    const isLoading = ref(false);

    const form = reactive({
        status: 0,
        hideTime: '',
        disableStatus: 0
    });

    const fetchData = async () => {
        isLoading.value = true;
        try {
            const res = await axios.post("/plugin/ZeroSource/api/fetchData");
            isLoading.value = false;
            if (res.data?.code == 200) {
                form.status = res.data?.data?.status ?? 0;
                form.hideTime = res.data?.data?.hideTime ?? '';
                form.disableStatus = res.data?.data?.disableStatus ?? 0;
            } else {
                ElMessage.error(res.data?.msg);
            }
        } catch (error) {
            isLoading.value = false;
            ElMessage.error(error.message || '请求失败');
        }
    };

    fetchData();

    const save = async () => {
        isLoading.value = true;
        try {
            const res = await axios.post("/plugin/ZeroSource/api/save", form);
            isLoading.value = false;
            if (res.data?.code == 200) {
                ElMessage.success('保存成功');
            } else {
                ElMessage.error(res.data?.msg);
            }
        } catch (error) {
            isLoading.value = false;
            ElMessage.error(error.message || '请求失败');
        }
    };

    const app = Vue.createApp({
        setup() {
            return {
                isLoading,
                form,
                save,
            };
        },
    });

    app.use(ElementPlus);
    app.mount("#app");

    window.onload = () => {
        document.getElementById("loading").style.display = "none";
        document.getElementById("app").style.display = "block";
    };
</script>
</body>
</html>
