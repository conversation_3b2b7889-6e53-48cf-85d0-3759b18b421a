#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证码处理模块
"""

import requests
import base64
from typing import Dict, Optional
from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton, QMessageBox
from PyQt6.QtGui import QPixmap
from PyQt6.QtCore import Qt

class CaptchaDialog(QDialog):
    """验证码输入对话框"""

    def __init__(self, img_data: bytes = None, img_url: str = "", auto_code: str = "", parent=None):
        super().__init__(parent)
        self.img_data = img_data
        self.img_url = img_url
        self.auto_code = auto_code
        self.captcha_code = ""
        self.init_ui()
        self.load_captcha_image()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("输入验证码")
        self.setModal(True)
        self.resize(300, 200)
        
        layout = QVBoxLayout(self)
        
        # 验证码图片
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_label.setMinimumHeight(80)
        self.image_label.setStyleSheet("border: 1px solid gray;")
        layout.addWidget(self.image_label)
        
        # 输入框
        input_layout = QHBoxLayout()
        input_layout.addWidget(QLabel("验证码:"))
        self.code_edit = QLineEdit()
        self.code_edit.setMaxLength(6)
        self.code_edit.returnPressed.connect(self.accept)

        # 如果有自动识别的验证码，预填充
        if self.auto_code:
            self.code_edit.setText(self.auto_code)
            self.code_edit.selectAll()  # 选中所有文本，方便修改

        input_layout.addWidget(self.code_edit)
        layout.addLayout(input_layout)

        # 自动识别提示
        if self.auto_code:
            hint_label = QLabel(f"🤖 自动识别: {self.auto_code} (可修改)")
            hint_label.setStyleSheet("color: green; font-size: 12px;")
            layout.addWidget(hint_label)
        
        # 按钮
        button_layout = QHBoxLayout()
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.refresh_captcha)
        self.ok_btn = QPushButton("确定")
        self.ok_btn.clicked.connect(self.accept)
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(self.refresh_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.ok_btn)
        button_layout.addWidget(self.cancel_btn)
        layout.addLayout(button_layout)
        
        # 设置焦点
        self.code_edit.setFocus()
    
    def load_captcha_image(self):
        """加载验证码图片"""
        try:
            pixmap = QPixmap()

            if self.img_data:
                # 使用已有的图片数据
                pixmap.loadFromData(self.img_data)
            elif self.img_url:
                # 从URL获取图片
                response = requests.get(self.img_url, timeout=10)
                response.raise_for_status()
                pixmap.loadFromData(response.content)

            if not pixmap.isNull():
                # 缩放图片适应标签大小
                scaled_pixmap = pixmap.scaled(
                    200, 80,  # 固定大小
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation
                )
                self.image_label.setPixmap(scaled_pixmap)
            else:
                self.image_label.setText("验证码图片加载失败")

        except Exception as e:
            self.image_label.setText(f"加载验证码失败: {str(e)}")
    
    def refresh_captcha(self):
        """刷新验证码"""
        self.load_captcha_image()
        self.code_edit.clear()
    
    def accept(self):
        """确定按钮处理"""
        self.captcha_code = self.code_edit.text().strip()
        if not self.captcha_code:
            QMessageBox.warning(self, "警告", "请输入验证码！")
            return
        super().accept()
    
    def get_captcha_code(self) -> str:
        """获取输入的验证码"""
        return self.captcha_code

class CaptchaHandler:
    """验证码处理器"""

    def __init__(self, register_instance):
        self.register_instance = register_instance
        self.ddddocr = None
        self._init_ddddocr()

    def _init_ddddocr(self):
        """初始化ddddocr"""
        try:
            import ddddocr
            self.ddddocr = ddddocr.DdddOcr()
        except ImportError:
            print("ddddocr未安装，无法使用自动识别功能")
            self.ddddocr = None

    def process_captcha_start_response(self, response_data: Dict) -> Dict:
        """
        处理验证码开始响应

        Args:
            response_data: 验证码API响应数据

        Returns:
            dict: 处理结果
        """
        try:
            # 提取验证码信息
            captcha_key = response_data.get('key', '')
            captcha_image = response_data.get('image', '')

            if not captcha_key or not captcha_image:
                return {
                    'success': False,
                    'error': '验证码响应数据不完整'
                }

            # 解码base64图片
            image_data = self.decode_base64_image(captcha_image)
            if not image_data:
                return {
                    'success': False,
                    'error': '验证码图片解码失败'
                }

            # 尝试自动识别
            auto_result = None
            if self.ddddocr:
                try:
                    auto_result = self.ddddocr.classification(image_data)
                except Exception as e:
                    print(f"自动识别失败: {e}")

            return {
                'success': True,
                'captcha_key': captcha_key,
                'captcha_image': captcha_image,
                'image_data': image_data,
                'auto_result': auto_result
            }

        except Exception as e:
            return {
                'success': False,
                'error': f'处理验证码响应失败: {str(e)}'
            }

    def decode_base64_image(self, base64_str: str) -> Optional[bytes]:
        """
        解码base64图片

        Args:
            base64_str: base64编码的图片字符串

        Returns:
            bytes: 图片字节数据
        """
        try:
            # 移除data:image前缀
            if base64_str.startswith('data:image'):
                base64_str = base64_str.split(',')[1]

            # 解码base64
            image_data = base64.b64decode(base64_str)
            return image_data
        except Exception as e:
            print(f"解码base64图片失败: {e}")
            return None
    
    def get_captcha_with_dialog(self, parent=None, auto_recognize=True) -> Dict:
        """
        通过对话框获取验证码（支持自动识别）

        Args:
            parent: 父窗口
            auto_recognize: 是否启用自动识别

        Returns:
            dict: 验证码结果
        """
        try:
            # 获取验证码信息和图片数据
            captcha_result = self.register_instance.get_captcha()
            if not captcha_result['success']:
                return {
                    'success': False,
                    'error': captcha_result['error']
                }

            auto_code = ""
            if auto_recognize:
                # 尝试自动识别验证码
                recognize_result = self.register_instance.recognize_captcha_with_ddddocr(
                    captcha_result['img_data']
                )
                if recognize_result['success']:
                    auto_code = recognize_result['captcha_code']

            # 显示验证码对话框
            dialog = CaptchaDialog(
                img_data=captcha_result['img_data'],
                img_url=captcha_result['img_url'],
                auto_code=auto_code,
                parent=parent
            )

            if dialog.exec() == QDialog.DialogCode.Accepted:
                captcha_code = dialog.get_captcha_code()

                # 验证验证码
                verify_result = self.register_instance.verify_captcha(
                    captcha_result['check_url'],
                    captcha_code
                )

                if verify_result['success']:
                    return {
                        'success': True,
                        'captcha_code': captcha_code,
                        'auto_recognized': bool(auto_code),
                        'message': '验证码验证成功'
                    }
                else:
                    return {
                        'success': False,
                        'error': verify_result.get('error', '验证码验证失败')
                    }
            else:
                return {
                    'success': False,
                    'error': '用户取消输入验证码'
                }

        except Exception as e:
            return {
                'success': False,
                'error': f'验证码处理异常: {str(e)}'
            }
    
    def auto_solve_captcha(self) -> Dict:
        """
        完全自动识别验证码（无人工干预）

        Returns:
            dict: 识别结果
        """
        try:
            # 获取验证码信息和图片数据
            captcha_result = self.register_instance.get_captcha()
            if not captcha_result['success']:
                return {
                    'success': False,
                    'error': captcha_result['error']
                }

            # 自动识别验证码
            recognize_result = self.register_instance.recognize_captcha_with_ddddocr(
                captcha_result['img_data']
            )

            if not recognize_result['success']:
                return {
                    'success': False,
                    'error': recognize_result['error']
                }

            captcha_code = recognize_result['captcha_code']

            # 验证验证码
            verify_result = self.register_instance.verify_captcha(
                captcha_result['check_url'],
                captcha_code
            )

            if verify_result['success']:
                return {
                    'success': True,
                    'captcha_code': captcha_code,
                    'auto_recognized': True,
                    'message': f'自动识别成功: {captcha_code}'
                }
            else:
                return {
                    'success': False,
                    'error': f'自动识别的验证码错误: {captcha_code}',
                    'recognized_code': captcha_code
                }

        except Exception as e:
            return {
                'success': False,
                'error': f'自动识别异常: {str(e)}'
            }
    
    def batch_captcha_handler(self, count: int, parent=None) -> Dict:
        """
        批量验证码处理
        
        Args:
            count: 需要处理的验证码数量
            parent: 父窗口
            
        Returns:
            dict: 处理结果
        """
        captcha_codes = []
        
        for i in range(count):
            result = self.get_captcha_with_dialog(parent)
            if result['success']:
                captcha_codes.append(result['captcha_code'])
            else:
                return {
                    'success': False,
                    'error': f'第{i+1}个验证码处理失败: {result["error"]}'
                }
        
        return {
            'success': True,
            'captcha_codes': captcha_codes,
            'count': len(captcha_codes)
        }
