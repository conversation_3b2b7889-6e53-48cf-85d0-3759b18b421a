<?php
namespace plugin\Orderstimeout\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;
use think\facade\Request;
use think\response\Json;

class Api extends BasePlugin
{
    protected $scene = ['admin']; // 仅管理员可访问

    protected $noNeedLogin = ['index', 'fetchData']; // 确保 fetchData 方法无需鉴权

    // 显示 API 页面
    public function index()
    {
       return View::fetch();
    }

    // 获取配置数据
    public function fetchData()
    {
        $params = [
            'status' => intval(plugconf("Orderstimeout.status") ?? 0),
        ];

        return json([
            'code' => 200,
            'msg' => 'success',
            'data' => $params
        ]);
    }

    // 保存配置数据
    public function save()
    {
        try {
            $status = Request::post('status/d', 0);
            
            // 确保状态值为整数
            $status = intval($status);
            
            // 构建配置数组
            $params = [
                'status' => $status
            ];
            
            // 获取插件目录中的 params.php 文件路径
            $filePath = app()->getRootPath() . 'plugin/Orderstimeout/params.php';
            
            // 生成 PHP 配置文件内容
            $content = "<?php\n\nreturn " . var_export($params, true) . ";\n";
            
            // 写入文件
            if (file_put_contents($filePath, $content)) {
                // 同时更新运行时配置
                plugconf("Orderstimeout.status", $status);
                
                return json([
                    'code' => 1,
                    'msg' => '保存成功',
                    'data' => ['status' => $status]
                ]);
            } else {
                throw new \Exception('无法写入配置文件');
            }
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '保存失败：' . $e->getMessage()
            ]);
        }
    }

    // 更新队列状态
    public function resetCustomStatus(): Json
    {
        try {
            $status = Request::param('status/d', 0);
            $customStatus = $status ? 0 : 1;
            
            // 只更新特定条件的记录
            $updatedRows = Db::name('system_queue')
                ->where([
                    ['title', '=', '关闭过期订单'],
                    ['custom_status', '<>', $customStatus]
                ])
                ->update(['custom_status' => $customStatus]);

            return json([
                'code' => 200,
                'msg' => $updatedRows > 0 ? '状态更新成功' : '没有需要更新的记录',
                'data' => ['updated' => $updatedRows]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '错误: ' . $e->getMessage()
            ]);
        }
    }

    // 获取所有订单
    public function fetchOrders(): Json
    {
        try {
            $orders = Db::name('order')->select()->toArray();
            return json(['code' => 200, 'data' => $orders]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '错误: ' . $e->getMessage()]);
        }
    }

    // 根据订单号搜索订单
    public function searchOrders(): Json
    {
        $tradeNo = Request::param('trade_no');
        if (empty($tradeNo)) {
            return json(['code' => 400, 'msg' => '订单号不能为空']);
        }

        try {
            $orders = Db::name('order')
                ->where('trade_no', $tradeNo)
                ->select()
                ->toArray();

            if (empty($orders)) {
                return json(['code' => 404, 'msg' => '未找到该订单号的订单信息']);
            }

            return json([
                'code' => 200, 
                'msg' => 'success',
                'data' => $orders
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500, 
                'msg' => '查询失败: ' . $e->getMessage()
            ]);
        }
    }

    // 保存订单状态
    public function saveOrder(): Json
    {
        $orderData = Request::post();
        try {
            Db::name('order')
                ->where('id', $orderData['id'])
                ->update(['status' => 0]);

            return json([
                'code' => 200, 
                'msg' => '订单状态已修改为0',
                'data' => ['status' => 0]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500, 
                'msg' => '修改订单状态失败: ' . $e->getMessage()
            ]);
        }
    }
}
