<?php

namespace plugin\Xiaomark;
 
use app\common\library\Plugin;
use app\common\model\DwzApi as DwzApiModel;
use app\common\service\HttpService;

class Xiaomark extends Plugin {

    /**
     * 插件安装方法
     * @return bool
     */
    public function install() {
        try {
            $model = DwzApiModel::where(['code' => 'Xiaomark'])->find();
            if (!$model) {
                $model = new DwzApiModel();
            }
            $model->code = 'Xiaomark';
            $model->name = '小猫云短链接';
            $model->tips = '';
            $model->website = 'https://xiaomark.com/';
            
            // 保存数据并记录结果
            $result = $model->save();
            
            // 检查保存结果
            if ($result) {
                return true;
            } else {
                return false;
            }
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 插件卸载方法
     * @return bool
     */
    public function uninstall() {
        try {
            $model = DwzApiModel::where(['code' => 'Xiaomark'])->find();
            if ($model) {
                $model->delete();
            }
            return true;
        } catch (\Exception $e) {
            return true;
        }
    }

    /**
     * 生成短链接
     * @param string $url 需要缩短的长网址
     * @return string|false
     */
    public function create($url) {
        // 获取参数
        $apikey = plugconf('Xiaomark.apikey');
        $group_id = plugconf('Xiaomark.group_id');
        $domain = plugconf('Xiaomark.domain');
        $webhook = plugconf('Xiaomark.webhook');
        
        if (empty($apikey) || empty($group_id)) {
            return false;
        }

        // 使用V1 API参数
        $params = [
            'apikey' => $apikey,
            'origin_url' => $url,
            'group_sid' => $group_id
        ];
        
        // 添加可选参数
        if (!empty($domain)) {
            $params['domain'] = $domain;
        }
        
        if (!empty($webhook) && $webhook == '1') {
            $params['webhook'] = true;
            $webhook_scene = plugconf('Xiaomark.webhook_scene');
            if (!empty($webhook_scene)) {
                $params['webhook_scene'] = $webhook_scene;
            }
        }

        // 使用v1 API接口
        $api_url = 'https://api.xiaomark.com/v1/link/create';
        
        try {
            $res = HttpService::post($api_url, json_encode($params), ['Content-Type: application/json']);
            
            if ($res === false) {
                return false;
            }
            
            $json = json_decode($res, true);
            
            if (!$json) {
                return false;
            }

            // V1 API返回格式处理
            if (isset($json['code']) && $json['code'] != 0) {
                return false;
            }

            // V1 API返回结果解析
            if (!isset($json['data']) || !isset($json['data']['link']) || !isset($json['data']['link']['url'])) {
                return false;
            }

            return $json['data']['link']['url'];
        } catch (\Exception $e) {
            return false;
        }
    }
} 