<?php

namespace app\index\Blackglod\controller;

use app\common\controller\BaseIndex;
use think\facade\View;
use think\facade\Db;

class Index extends BaseIndex {

    public function index() {
        // 获取所有启用的菜单项并按sort降序排序
        $allMenuItems = Db::name('nav')
            ->where('status', 1)
            ->order('sort desc, id asc')  // 先按sort降序，相同时按id升序
            ->select()
            ->toArray();
            
        // 找出顶级菜单
        $navItems = array_filter($allMenuItems, function($item) {
            return $item['pid'] == 0;
        });
        
        // 为每个顶级菜单添加子菜单
        foreach ($navItems as &$item) {
            $item['children'] = array_filter($allMenuItems, function($child) use ($item) {
                return $child['pid'] == $item['id'];
            });
        }
        
        // 将导航数组重置索引
        $navItems = array_values($navItems);

        // 获取网站配置信息
        $logo = $this->formatImageUrl(sysconf('website.logo'));
        $siteName = sysconf('website.app_name');
        $favicon = $this->formatImageUrl(sysconf('website.favicon'));
        $icpNumber = sysconf('website.icp_number');
        $gaNumber = sysconf('website.ga_number');
        $icpCert = sysconf('website.icp_cert');

        // 获取页脚配置
        $params = get_template_params();
        
        // 确保$params是一个对象
        if (!is_object($params)) {
            $params = new \stdClass();
        }

        return view('', [
            'title' => sysconf("website.title"),
            'logo' => $logo,
            'siteName' => $siteName,
            'favicon' => $favicon,
            'navItems' => $navItems,
            'icpNumber' => $icpNumber,
            'gaNumber' => $gaNumber,
            'icpCert' => $icpCert,
            // 服务中心配置
            'footer_service_show' => $params->footer_service_show ?? 1,
            'footer_service_1' => $params->footer_service_1 ?? '卡密查询',
            'footer_service_1_link' => $params->footer_service_1_link ?? '#',
            'footer_service_2' => $params->footer_service_2 ?? '投诉中心',
            'footer_service_2_link' => $params->footer_service_2_link ?? '#',
            'footer_service_3' => $params->footer_service_3 ?? '卡密工具',
            'footer_service_3_link' => $params->footer_service_3_link ?? '#',
            'footer_service_4' => $params->footer_service_4 ?? '商户入驻',
            'footer_service_4_link' => $params->footer_service_4_link ?? '#',
            
            // 帮助中心配置
            'footer_help_show' => $params->footer_help_show ?? 1,
            'footer_help_1' => $params->footer_help_1 ?? '常见问题',
            'footer_help_1_link' => $params->footer_help_1_link ?? '#',
            'footer_help_2' => $params->footer_help_2 ?? '系统公告',
            'footer_help_2_link' => $params->footer_help_2_link ?? '#',
            'footer_help_3' => $params->footer_help_3 ?? '结算公告',
            'footer_help_3_link' => $params->footer_help_3_link ?? '#',
            'footer_help_4' => $params->footer_help_4 ?? '新闻动态',
            'footer_help_4_link' => $params->footer_help_4_link ?? '#',
            
            // 法律责任配置
            'footer_legal_show' => $params->footer_legal_show ?? 1,
            'footer_legal_1' => $params->footer_legal_1 ?? '免责声明',
            'footer_legal_1_link' => $params->footer_legal_1_link ?? '#',
            'footer_legal_2' => $params->footer_legal_2 ?? '禁售商品',
            'footer_legal_2_link' => $params->footer_legal_2_link ?? '#',
            'footer_legal_3' => $params->footer_legal_3 ?? '服务协议',
            'footer_legal_3_link' => $params->footer_legal_3_link ?? '#',
            'footer_legal_4' => $params->footer_legal_4 ?? '隐私政策',
            'footer_legal_4_link' => $params->footer_legal_4_link ?? '#',
            
            // 友情链接配置
            'footer_links_show' => $params->footer_links_show ?? 1,
            'footer_links_1' => $params->footer_links_1 ?? '一意支付',
            'footer_links_1_link' => $params->footer_links_1_link ?? '#',
            'footer_links_2' => $params->footer_links_2 ?? '支付宝',
            'footer_links_2_link' => $params->footer_links_2_link ?? '#',
            'footer_links_3' => $params->footer_links_3 ?? '微信支付',
            'footer_links_3_link' => $params->footer_links_3_link ?? '#',
            'footer_links_4' => $params->footer_links_4 ?? 'QQ钱包',
            'footer_links_4_link' => $params->footer_links_4_link ?? '#',
            
            // 联系方式配置
            'contact_email' => $params->contact_email ?? '<EMAIL>',
            'contact_qq' => $params->contact_qq ?? '123456789',
            'contact_wx' => $params->contact_wx ?? 'service_wx',
            
            // 统计数据配置
            'stats_orders' => $params->stats_orders ?? '12452177',
            'stats_cards' => $params->stats_cards ?? '2565245',
            'stats_merchants' => $params->stats_merchants ?? '1526988',
            'stats_amount' => $params->stats_amount ?? '84768.25',
            
            // 新增页脚配置
            'footer_logo' => empty($logo) ? '/assets/plugin/Blackglod/plugin/Blackglod/images/logo.png' : $this->formatImageUrl($logo),
            'footer_slogan' => isset($params->footer_slogan) ? $params->footer_slogan : '专业靠谱 · 不将就',
            'footer_address' => isset($params->footer_address) ? $params->footer_address : '中国 · 广西',
            
            // 快速通道配置
            'footer_quicklink_title' => isset($params->footer_quicklink_title) ? $params->footer_quicklink_title : '快速通道',
            'footer_quicklink_1' => isset($params->footer_quicklink_1) ? $params->footer_quicklink_1 : '注册商户',
            'footer_quicklink_1_link' => isset($params->footer_quicklink_1_link) ? $params->footer_quicklink_1_link : '/shop/register',
            'footer_quicklink_2' => isset($params->footer_quicklink_2) ? $params->footer_quicklink_2 : '商户登录',
            'footer_quicklink_2_link' => isset($params->footer_quicklink_2_link) ? $params->footer_quicklink_2_link : '/shop/login',
            
            // 二维码图片路径处理
            'footer_wechat_qrcode' => $this->getQrcodePath(isset($params->footer_wechat_qrcode) ? $params->footer_wechat_qrcode : '', 'wechat'),
            'footer_service_qrcode' => $this->getQrcodePath(isset($params->footer_service_qrcode) ? $params->footer_service_qrcode : '', 'service'),
            'footer_wechat_title' => isset($params->footer_wechat_title) ? $params->footer_wechat_title : '官方公众号',
            'footer_service_title' => isset($params->footer_service_title) ? $params->footer_service_title : '官方客服',
            'footer_wechat_desc' => isset($params->footer_wechat_desc) ? $params->footer_wechat_desc : '扫码关注微信公众号<br>实时获取订单及优惠',
            'footer_service_desc' => isset($params->footer_service_desc) ? $params->footer_service_desc : '扫码联系客服团队<br>实时解决订单问题',
        ]);
    }

    /**
     * 格式化图片URL
     */
    private function formatImageUrl($url) {
        if (empty($url)) {
            return '';
        }

        // 如果是完整的URL，直接返回
        if (preg_match('/^https?:\/\/(oss\.|cos\.|[-\w]+\.qiniucdn\.|[-\w]+)/i', $url)) {
            return $url;
        }

        try {
            // 如果是uploads目录下的文件
            if (strpos($url, 'uploads/') === 0) {
                return request()->domain() . '/' . $url;
            }

            // 其他情况，确保以/开头
            return request()->domain() . '/' . ltrim($url, '/');
        } catch (\Exception $e) {
            // 如果有任何异常，返回一个空字符串，避免500错误
            return '';
        }
    }

    private function getQrcodePath($qrcode, $type) {
        // 如果二维码路径为空，返回空字符串
        if (empty($qrcode)) {
            return '';
        }

        try {
            // 如果是完整的URL（以http或https开头），直接返回
            if (preg_match('/^https?:\/\//i', $qrcode)) {
                return $qrcode;
            }
            
            // 如果是相对路径，确保以/开头
            return '/' . ltrim($qrcode, '/');
            
        } catch (\Exception $e) {
            // 如果有任何异常，返回空字符串
            return '';
        }
    }
}
