<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{$title}</title>
    
    <link rel="shortcut icon" href="{$favicon}" type="image/x-icon">
    
    <!-- CSS 文件 -->
    <link href="/static/yibazhan/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/yibazhan/css/font-awesome.min.css" rel="stylesheet">
    <link href="/static/yibazhan/css/aos.css" rel="stylesheet">
    <link href="/static/yibazhan/css/css2.css" rel="stylesheet">
    <link href="/static/yibazhan/css/all.min.css" rel="stylesheet">
    <link href="/static/yibazhan/css/style.min.css" rel="stylesheet">
    <link href="/static/yibazhan/css/owl.carousel.min.css" rel="stylesheet">
    <link href="/static/yibazhan/css/owl.theme.default.min.css" rel="stylesheet">
    <link href="/static/yibazhan/css/jquery-ui.css" rel="stylesheet">
    <link href="/static/yibazhan/css/jquery-ui1.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    
    <!-- 添加自定义样式 -->
    <style>
      .navbar-brand img {
        max-width: 150px;  /* 限制logo最大宽度 */
        height: auto;      /* 保持图片比例 */
      }
      
      /* 移动端适配 */
      @media (max-width: 768px) {
        .navbar-brand img {
          max-width: 120px;  /* 移动端稍微缩小logo */
        }
        
        /* 导航菜单样式 */
        .navbar-nav {
          margin-top: 1rem;
          text-align: center;
        }
        
        .nav-item {
          padding: 0.5rem 0;
        }
        
        /* banner区域样式 */
        .banner-text-home h1 {
          font-size: 2rem;
          text-align: center;
        }
        
        .banner-text-home p {
          text-align: center;
          padding: 0 1rem;
        }
        
        .btn-get-btn {
          display: block;
          margin: 0 auto;
          max-width: 200px;
          text-align: center;
        }
        
        /* 特色服务区域 */
        .comon-felature01 {
          padding: 1.5rem;
          margin-bottom: 1rem;
        }
        
        /* 商家展示区域 */
        .items-astro {
          margin-bottom: 2rem;
        }
        
        /* 底部样式 */
        footer {
          text-align: center;
        }
        
        .colm-footer {
          margin-top: 2rem;
        }
        
        .colm-footer ul {
          padding-left: 0;
        }
      }

      /* 替换中心logo部分 */
      .center-logo {
        position: absolute;
        width: 120px;
        height: 120px;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(255, 255, 255, 0.15);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(5px);
        box-shadow: 0 0 30px rgba(255, 255, 255, 0.2);
        border: 2px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
      }

      .center-logo:hover {
        background: rgba(255, 255, 255, 0.25);
        transform: translate(-50%, -50%) scale(1.1);
        box-shadow: 0 0 40px rgba(255, 255, 255, 0.3);
      }

      .center-logo svg {
        filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.5));
      }

      .dropdown-arrow {
        margin-left: 4px;
        transition: transform 0.3s ease;
      }
      
      .dropdown.show .dropdown-arrow {
        transform: rotate(180deg);
      }
      
      .nav-link {
        display: flex;
        align-items: center;
      }
      
      /* 移除默认的下拉箭头 */
      .dropdown-toggle::after {
        display: none !important;
      }
      
      @media (max-width: 768px) {
        .dropdown-arrow {
          margin-left: 8px;
        }
      }

      /* 添加样式 */
      .stats-box {
        padding: 2rem;
      }
      .stats-box span {
        font-size: 3rem;
        margin-left: 0.5rem;
      }
      .status-lights {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
      }
      .light {
        width: 30px;
        height: 30px;
        border-radius: 50%;
      }
      .yellow { background: #ffd700; }
      .green { background: #4caf50; }
      .red { background: #f44336; }

      /* 添加响应式布局样式 */
      .comin-divu-main {
        padding: 2rem;
        transition: all 0.3s ease;
      }

      /* PC端样式 (>= 1024px) */
      @media only screen and (min-width: 1024px) {
        .comin-divu-main {
          padding: 2rem;
        }
        
        .comin-divu-main figure {
          width: 80px;
          height: 80px;
        }
        
        .comin-divu-main svg {
          width: 80px;
          height: 80px;
        }
        
        .comin-divu-main h5 {
          font-size: 1.25rem;
        }
        
        .comin-divu-main p {
          font-size: 1rem;
        }
        
        .btn-more {
          padding: 0.75rem 1.5rem;
          font-size: 1rem;
        }
      }

      /* iPad端样式 (768px - 1023px) */
      @media only screen and (min-width: 768px) and (max-width: 1023px) {
        .comin-divu-main {
          padding: 1.5rem;
        }
        
        .comin-divu-main figure {
          width: 60px;
          height: 60px;
        }
        
        .comin-divu-main svg {
          width: 60px;
          height: 60px;
        }
        
        .comin-divu-main h5 {
          font-size: 1.1rem;
        }
        
        .comin-divu-main p {
          font-size: 0.9rem;
        }
        
        .btn-more {
          padding: 0.6rem 1.2rem;
          font-size: 0.9rem;
        }
      }

      /* 手机端样式 (<= 767px) */
      @media only screen and (max-width: 767px) {
        .comin-divu-main {
          padding: 1rem;
          text-align: center;
        }
        
        .comin-divu-main figure {
          width: 50px;
          height: 50px;
          margin: 0 auto 1rem;
        }
        
        .comin-divu-main svg {
          width: 50px;
          height: 50px;
        }
        
        .comin-divu-main h5 {
          font-size: 1rem;
          margin-top: 1rem;
        }
        
        .comin-divu-main p {
          font-size: 0.85rem;
        }
        
        .comin-divu-main .col-lg-2,
        .comin-divu-main .col-lg-10 {
          width: 100%;
        }
        
        .btn-more {
          padding: 0.5rem 1rem;
          font-size: 0.85rem;
          margin: 1rem auto;
          display: table;
        }
      }

      /* 添加平滑过渡效果 */
      .comin-divu-main,
      .comin-divu-main figure,
      .comin-divu-main svg,
      .comin-divu-main h5,
      .comin-divu-main p,
      .btn-more {
        transition: all 0.3s ease-in-out;
      }

      /* 基础卡片样式 */
      .experience-card,
      .laptop-animation,
      .status-card {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 15px;
        backdrop-filter: blur(10px);
        padding: 2rem;
        height: 300px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
      }

      /* 经验年限卡片 */
      .experience-card {
        position: relative;
        overflow: hidden;
      }

      .moon-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0.3;
        z-index: 1;
      }

      .moon-bg img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .experience-content {
        position: relative;
        z-index: 2;
        text-align: center;
        color: white;
      }

      .experience-content h2 {
        font-size: 3.5rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
      }

      .experience-content span {
        color: #4caf50;
      }

      /* 笔记本动画容器 */
      .laptop-animation {
        background: transparent;
      }

      .laptop-animation svg {
        max-width: 100%;
        height: auto;
      }

      /* 状态指示灯 */
      .status-lights {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
      }

      .light {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        transition: all 0.3s ease;
      }

      .yellow { background: #ffd700; }
      .green { background: #4caf50; }
      .red { background: #f44336; }

      /* 响应式调整 */
      @media (max-width: 991px) {
        .experience-card,
        .laptop-animation,
        .status-card {
          height: 250px;
        }
        
        .experience-content h2 {
          font-size: 2.5rem;
        }
        
        .light {
          width: 25px;
          height: 25px;
        }
      }

      @media (max-width: 767px) {
        .experience-card,
        .laptop-animation,
        .status-card {
          height: 200px;
        }
        
        .experience-content h2 {
          font-size: 2rem;
        }
        
        .light {
          width: 20px;
          height: 20px;
        }
      }

      /* 悬停效果 */
      .experience-card:hover,
      .laptop-animation:hover,
      .status-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
      }

      /* 添加动画效果 */
      @keyframes glow {
        0% { box-shadow: 0 0 5px rgba(255, 255, 255, 0.5); }
        50% { box-shadow: 0 0 20px rgba(255, 255, 255, 0.8); }
        100% { box-shadow: 0 0 5px rgba(255, 255, 255, 0.5); }
      }

      .light {
        animation: glow 2s infinite;
      }
    </style>
  </head>

<body>

  <header class="float-start w-100">
    <nav class="navbar navbar-expand-lg navbar-light">
      <div class="container">
        <a class="navbar-brand" href="/">
          <img alt="logo" src="{$logo}" class="img-fluid">
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="offcanvas" data-bs-target="#mobile-menu">
          <span> <i class="fas fa-bars"></i> </span>
        </button>
        <div class="collapse navbar-collapse" id="navbarSupportedContent">
          <ul class="navbar-nav mx-auto mb-2 mb-lg-0">
            {foreach $navItems as $item}
            <li class="nav-item {if !empty($item.children)}dropdown{/if}">
                <a class="nav-link {if !empty($item.children)}dropdown-toggle{/if}" 
                   href="{$item.href}" 
                   {if $item.target}target="_blank"{/if}
                   {if !empty($item.children)}data-bs-toggle="dropdown"{/if}>
                    {$item.name}
                    {if !empty($item.children)}
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor" class="dropdown-arrow" viewBox="0 0 16 16">
                        <path d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z"/>
                    </svg>
                    {/if}
                </a>
                {if !empty($item.children)}
                <ul class="dropdown-menu">
                    {foreach $item.children as $child}
                    <li>
                        <a class="dropdown-item" 
                           href="{$child.href}" 
                           {if $child.target}target="_blank"{/if}>
                            {$child.name}
                        </a>
                    </li>
                    {/foreach}
                </ul>
                {/if}
            </li>
            {/foreach}
          </ul>
        </div>
        <div class="d-none d-lg-block">
            <div class="right-menui">
              <ul>
                  
                  <li>
                    <a href="/merchant/login" class="btn consult-btn">商家中心</a>
                  </li>
              </ul>
            </div>
        </div>
      </div>
    </nav>
   </header>

 <section class="banner-section float-start w-100">
    <div class="img-main-abnner d-inline-block w-100">
       <img src="/static/yibazhan/images/bg-bannerpic.jpg" alt="banner">
    </div>
    <div class="main-bg-start"></div>
    <div class="container">
      <div class="banner-text-home">
          <div class="row row-cols-1 row-cols-lg-2 align-items-center g-lg-5">
              <div class="col">
                <span class="spm-smalll" data-aos="fade-up"> 合 规 营 销 支 付 分 销</span>
                <h1 class="text-white my-3" data-aos="fade-down">独特营销系统
                  <span class="d-block"> 协助让您销量暴增 </span>
                </h1>
                <p data-aos="fade-up"> 独特营销系统可以帮助您的店铺实现销量暴增 
                  使用最新的营销策略可以吸引更多潜在客户进入您的店铺进行购物</p>
                  <div class="mt-5">
                     <a href="index.html#" class="btn btn-get-btn" data-aos="fade-up"> 立即入驻，成为商户</a>
                  </div>
              </div>
              <div class="col">
                <div class="main d-none d-lg-block">

                  <div class="big-circle">
                    <div class="icon-block">
                      <!-- 购物车图标 -->
                      <svg width="40" height="40" viewBox="0 0 24 24" fill="#fff">
                        <path d="M7 18c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12.9-1.63h7.45c.75 0 1.41-.41 1.75-1.03l3.58-6.49c.08-.14.12-.31.12-.48 0-.55-.45-1-1-1H5.21l-.94-2H1zm16 16c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2z"/>
                      </svg>
                    </div>
                    <div class="icon-block">
                      <!-- 商品图标 -->
                      <svg width="40" height="40" viewBox="0 0 24 24" fill="#fff">
                        <path d="M19 5h-2V3H7v2H5c-1.1 0-2 .9-2 2v1c0 2.55 1.92 4.63 4.39 4.94.63 1.5 1.98 2.63 3.61 2.96V19H7v2h10v-2h-4v-3.1c1.63-.33 2.98-1.46 3.61-2.96C19.08 12.63 21 10.55 21 8V7c0-1.1-.9-2-2-2zM7 10.82C5.84 10.4 5 9.3 5 8V7h2v3.82zM19 8c0 1.3-.84 2.4-2 2.82V7h2v1z"/>
                      </svg>
                    </div>
                    <div class="icon-block">
                      <!-- 优惠券图标 -->
                      <svg width="40" height="40" viewBox="0 0 24 24" fill="#fff">
                        <path d="M20 6h-2.18c.11-.31.18-.65.18-1 0-1.66-1.34-3-3-3-1.05 0-1.96.54-2.5 1.35l-.5.67-.5-.68C10.96 2.54 10.05 2 9 2 7.34 2 6 3.34 6 5c0 .35.07.69.18 1H4c-1.11 0-1.99.89-1.99 2L2 19c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2zm-5-2c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zM9 4c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm11 15H4v-2h16v2zm0-5H4V8h5.08L7 10.83 8.62 12 11 8.76l1-1.36 1 1.36L15.38 12 17 10.83 14.92 8H20v6z"/>
                      </svg>
                    </div>
                    <div class="icon-block">
                      <!-- 订单图标 -->
                      <svg width="40" height="40" viewBox="0 0 24 24" fill="#fff">
                        <path d="M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm2 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                      </svg>
                    </div>
                    <div class="icon-block">
                      <!-- 会员图标 -->
                      <svg width="40" height="40" viewBox="0 0 24 24" fill="#fff">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z"/>
                      </svg>
                    </div>
                    <div class="icon-block">
                      <!-- 搜索图标 -->
                      <svg width="40" height="40" viewBox="0 0 24 24" fill="#fff">
                        <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                      </svg>
                    </div>
                  </div>
                  <div class="circle">
                    <div class="icon-block">
                      <!-- 支付图标 -->
                      <svg width="40" height="40" viewBox="0 0 24 24" fill="#fff">
                        <path d="M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4v-6h16v6zm0-10H4V6h16v2z"/>
                      </svg>
                    </div>
                    <div class="icon-block">
                      <!-- 物流图标 -->
                      <svg width="40" height="40" viewBox="0 0 24 24" fill="#fff">
                        <path d="M20 8h-3V4H3c-1.1 0-2 .9-2 2v11h2c0 1.66 1.34 3 3 3s3-1.34 3-3h6c0 1.66 1.34 3 3 3s3-1.34 3-3h2v-5l-3-4zM6 18.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zm13.5-9l1.96 2.5H17V9.5h2.5zm-1.5 9c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5z"/>
                        <!-- 添加路线指示线 -->
                        <path d="M4 12h8M4 9h12" stroke="#ffffff" stroke-width="0.5" stroke-dasharray="2,2"/>
                        <!-- 添加定位标记 -->
                        <circle cx="4" cy="9" r="1"/>
                        <circle cx="16" cy="9" r="1"/>
                      </svg>
                    </div>
                    <div class="icon-block">
                      <!-- 客服图标 -->
                      <svg width="40" height="40" viewBox="0 0 24 24" fill="#fff">
                        <path d="M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
                      </svg>
                    </div>
                    <div class="icon-block">
                      <!-- 收藏图标 -->
                      <svg width="40" height="40" viewBox="0 0 24 24" fill="#fff">
                        <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                      </svg>
                    </div>
                    <div class="icon-block">
                      <!-- 分享图标 -->
                      <svg width="40" height="40" viewBox="0 0 24 24" fill="#fff">
                        <path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92 1.61 0 2.92-1.31 2.92-2.92s-1.31-2.92-2.92-2.92z"/>
                      </svg>
                    </div>
                    <div class="icon-block">
                      <!-- 设置图标 -->
                      <svg width="40" height="40" viewBox="0 0 24 24" fill="#fff">
                        <path d="M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.07.62-.07.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6z"/>
                      </svg>
                    </div>
                  </div>
                  <div class="center-logo">
                    <svg width="60" height="60" viewBox="0 0 24 24" fill="#fff">
                      <!-- 购物袋图标 -->
                      <path d="M19 6h-2c0-2.76-2.24-5-5-5S7 3.24 7 6H5c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm-7-3c1.66 0 3 1.34 3 3H9c0-1.66 1.34-3 3-3zm7 17H5V8h14v12zm-7-8c-1.66 0-3-1.34-3-3H7c0 2.76 2.24 5 5 5s5-2.24 5-5h-2c0 1.66-1.34 3-3 3z"/>
                      
                      <!-- 添加一个小星形装饰 -->
                      <path d="M12 8l-1.5 1.5L9 8l1.5-1.5L12 8zm0 3l-1.5 1.5L9 11l1.5-1.5L12 11zm0 3l-1.5 1.5L9 14l1.5-1.5L12 14z"/>
                    </svg>
                  </div>

                </div>
              </div>
          </div>
      </div>
    </div>
 </section>
      <section class="our-astroly d-inline-block w-100">
         <div class="container">
          <div class="comon-heading text-center">
            <h2 class="text-white comon-heading mt-3 mb-3" data-aos="fade-down">我们的接口</h2>
            <h6 class="text-white" data-aos="fade-up">支持各种接口</h6>
          </div>
          <div class="astrolger owl-carousel owl-theme mt-5">
              <!-- 支付宝支付 -->
              <a href=" " class="items-astro d-lg-grid align-content-center w-100">
                  <div class="bg-round-im">
                      <img alt="hj" src="/static/yibazhan/images/author-bg.png">
                      <div class="img-user mx-auto">
                          <img alt="支付宝" src="/static/yibazhan/images/3.png">
                      </div>
                  </div>
                  <div class="asto-te-dl text-center">
                      <h5>支付宝支付</h5>
                      <p>支付方式</p>
                      <div class="d-flex align-items-center justify-content-center">
                          <span class="me-2">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-credit-card" viewBox="0 0 16 16">
                                  <path d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4zm2-1a1 1 0 0 0-1 1v1h14V4a1 1 0 0 0-1-1H2zm13 4H1v5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V7z"/>
                                  <path d="M2 10a1 1 0 0 1 1-1h1a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1v-1z"/>
                              </svg>
                          </span>
                          <ul class="d-flex align-content-center">
                              <li> 移动支付 , </li>
                              <li> 电脑支付 </li>
                          </ul>
                      </div>
                  </div>
              </a>

              <!-- 微信支付 -->
              <a href=" " class="items-astro d-lg-grid align-content-center w-100">
                  <div class="bg-round-im">
                      <img alt="hj" src="/static/yibazhan/images/author-bg.png">
                      <div class="img-user mx-auto">
                          <img alt="微信支付" src="/static/yibazhan/images/2.png">
                      </div>
                  </div>
                  <div class="asto-te-dl text-center">
                      <h5>微信支付</h5>
                      <p>支付方式</p>
                      <div class="d-flex align-items-center justify-content-center">
                          <span class="me-2">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-credit-card" viewBox="0 0 16 16">
                                  <path d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4zm2-1a1 1 0 0 0-1 1v1h14V4a1 1 0 0 0-1-1H2zm13 4H1v5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V7z"/>
                                  <path d="M2 10a1 1 0 0 1 1-1h1a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1v-1z"/>
                              </svg>
                          </span>
                          <ul class="d-flex align-content-center">
                              <li> 移动支付 , </li>
                              <li> 电脑支付 </li>
                          </ul>
                      </div>
                  </div>
              </a>

              <!-- 银联支付 -->
              <a href=" " class="items-astro d-lg-grid align-content-center w-100">
                  <div class="bg-round-im">
                      <img alt="hj" src="/static/yibazhan/images/author-bg.png">
                      <div class="img-user mx-auto">
                          <img alt="银联支付" src="/static/yibazhan/images/4.png">
                      </div>
                  </div>
                  <div class="asto-te-dl text-center">
                      <h5>银联支付</h5>
                      <p>银行卡支付</p>
                      <div class="d-flex align-items-center justify-content-center">
                          <span class="me-2">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-credit-card" viewBox="0 0 16 16">
                                  <path d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4zm2-1a1 1 0 0 0-1 1v1h14V4a1 1 0 0 0-1-1H2zm13 4H1v5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V7z"/>
                                  <path d="M2 10a1 1 0 0 1 1-1h1a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1v-1z"/>
                              </svg>
                          </span>
                          <ul class="d-flex align-content-center">
                              <li> 移动支付 , </li>
                              <li> 电脑支付 </li>
                          </ul>
                      </div>
                  </div>
              </a>

              <!-- QQ支付 -->
              <a href=" " class="items-astro d-lg-grid align-content-center w-100">
                  <div class="bg-round-im">
                      <img alt="hj" src="/static/yibazhan/images/author-bg.png">
                      <div class="img-user mx-auto">
                          <img alt="PayPal" src="/static/yibazhan/images/1.png">
                      </div>
                  </div>
                  <div class="asto-te-dl text-center">
                      <h5>QQ支付</h5>
                      <p>支付方式</p>
                      <div class="d-flex align-items-center justify-content-center">
                          <span class="me-2">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-credit-card" viewBox="0 0 16 16">
                                  <path d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4zm2-1a1 1 0 0 0-1 1v1h14V4a1 1 0 0 0-1-1H2zm13 4H1v5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V7z"/>
                                  <path d="M2 10a1 1 0 0 1 1-1h1a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1v-1z"/>
                              </svg>
                          </span>
                          <ul class="d-flex align-content-center">
                              <li> 移动支付 , </li>
                              <li> 电脑支付 </li>
                          </ul>
                      </div>
                  </div>
              </a>

              <!-- 银联钱包 -->
              <a href=" " class="items-astro d-lg-grid align-content-center w-100">
                  <div class="bg-round-im">
                      <img alt="hj" src="/static/yibazhan/images/author-bg.png">
                      <div class="img-user mx-auto">
                          <img alt="银联钱包" src="/static/yibazhan/images/5.png">
                      </div>
                  </div>
                  <div class="asto-te-dl text-center">
                      <h5>京东支付</h5>
                      <p>支付方式</p>
                      <div class="d-flex align-items-center justify-content-center">
                          <span class="me-2">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-credit-card" viewBox="0 0 16 16">
                                  <path d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4zm2-1a1 1 0 0 0-1 1v1h14V4a1 1 0 0 0-1-1H2zm13 4H1v5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V7z"/>
                                  <path d="M2 10a1 1 0 0 1 1-1h1a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1v-1z"/>
                              </svg>
                          </span>
                          <ul class="d-flex align-content-center">
                              <li> 移动支付 , </li>
                              <li> 电脑支付 </li>
                          </ul>
                      </div>
                  </div>
              </a>
          </div>
         </div>
     </section>
 <main class="float-start w-100 body-main">
     <section class="div01-top d-inline-block w-100">
         <div class="container">
             <div class="row row-cols-1 h-top row-cols-md-3 g-4 g-lg-5">
                <div class="col">
                   <a href="index.html#" class="comon-felature01 d-grid align-content-center w-100" data-aos="fade-up">
                       <div class="c-img mx-auto mb-2">
                         <!-- 修改商品管理图标为白色 SVG -->
                         <svg width="80" height="80" viewBox="0 0 24 24" fill="#ffffff">
                           <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
                         </svg>
                       </div>
                       <h5 class="text-white">商品管理</h5>
                       <p>轻松管理您的商品库存、价格和描述，支持批量操作和实时更新，让您的店铺管理更高效</p>
                   </a>
                </div>

                <div class="col">
                  <a href=" " class="comon-felature01 d-grid align-content-center w-100" data-aos="fade-down">
                      <div class="c-img mx-auto mb-2">
                        <!-- 订单处理图标 - base64编码的SVG -->
                        <img alt="订单处理" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0iI2ZmZmZmZiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTkgM2gtNC4xOEMxNC40IDEuODQgMTMuMyAxIDEyIDFjLTEuMyAwLTIuNC44NC0yLjgyIDJINWMtMS4xIDAtMiAuOS0yIDJ2MTRjMCAxLjEuOSAyIDIgMmgxNGMxLjEgMCAyLS45IDItMlY1YzAtMS4xLS45LTItMi0yem0tNyAwYy41NSAwIDEgLjQ1IDEgMXMtLjQ1IDEtMSAxLTEtLjQ1LTEtMSAuNDUtMSAxLTF6bTIgMTRIN3YtMmg3djJ6bTMtNEg3di0yaDEwdjJ6bTAtNEg3VjdoMTB2MnoiLz48L3N2Zz4=">
                      </div>
                      <h5 class="text-white">订单处理</h5>
                      <p>智能订单管理系统，自动处理订单状态，支持多渠道订单整合，提高订单处理效率</p>
                  </a>
                 </div>


                 <div class="col">
                  <a href=" " class="comon-felature01 d-grid align-content-center w-100" data-aos="fade-up">
                      <div class="c-img mx-auto mb-2">
                        <!-- 数据分析图标 - base64编码的SVG -->
                        <img alt="数据分析" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0iI2ZmZmZmZiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTkgM0g1Yy0xLjEgMC0yIC45LTIgMnYxNGMwIDEuMS45IDIgMiAyaDE0YzEuMSAwIDItLjkgMi0yVjVjMC0xLjEtLjktMi0yLTJ6TTkgMTdIN3YtN2gydjd6bTQgMGgtMlY3aDJ2MTB6bTQgMGgtMnYtNGgydjR6Ii8+PC9zdmc+">
                      </div>
                      <h5 class="text-white">数据分析</h5>
                      <p>专业的数据分析工具，帮助您了解销售趋势、客户行为和库存状况，做出明智的经营决策</p>
                  </a>
                 </div>

             </div>
         </div>
     </section>
     <section class="about-part-section d-inline-block w-100">
        <div class="container">
            <div class="row row-cols-1 row-cols-lg-2 gy-5 g-lg-5">
               <div class="col">
                  <div class="img-box01 d-inline-block w-100 position-relative">
                       <figure class="moon-img">
                         <img alt="moon" src="/static/yibazhan/images/moon.jpg">
                       </figure>
                       <figure class="big-imog aos-init aos-animate" data-aos="fade-down">
                         <!-- 调整SVG尺寸和viewBox -->
                         <svg width="600" height="500" viewBox="0 0 350 350" style="filter: drop-shadow(0 0 10px rgba(255,255,255,0.2));">
                             <!-- 定义渐变和滤镜保持不变 -->
                             <defs>
                                 <linearGradient id="deviceGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                     <stop offset="0%" style="stop-color:#4051b5"/>
                                     <stop offset="100%" style="stop-color:#7986cb"/>
                                 </linearGradient>
                                 <filter id="deviceGlow">
                                     <feGaussianBlur stdDeviation="2" result="glow"/>
                                     <feMerge>
                                         <feMergeNode in="glow"/>
                                         <feMergeNode in="SourceGraphic"/>
                                     </feMerge>
                                 </filter>
                             </defs>

                             <!-- 调整主要内容的位置和大小 -->
                             <g transform="translate(40, 40) scale(0.9)">
                                 <!-- 笔记本电脑 -->
                                 <path d="M50,100 h200 v120 h-200 z" fill="url(#deviceGradient)" filter="url(#deviceGlow)"/>
                                 <path d="M30,220 h240 l-20,20 h-200 l-20,-20" fill="#5c6bc0"/>
                                 
                                 <!-- 屏幕内容 - 调整间距 -->
                                 <rect x="60" y="110" width="180" height="100" fill="#c5cae9"/>
                                 
                                 <!-- 动态数据元素 - 调整间距 -->
                                 <g class="screen-content">
                                     <line x1="70" y1="125" x2="230" y2="125" stroke="#7986cb" stroke-width="2"/>
                                     <line x1="70" y1="145" x2="200" y2="145" stroke="#7986cb" stroke-width="2"/>
                                     <line x1="70" y1="165" x2="220" y2="165" stroke="#7986cb" stroke-width="2"/>
                                 </g>

                                 <!-- 悬浮图标 - 调整位置 -->
                                 <g class="floating-elements">
                                     <circle cx="260" cy="130" r="12" fill="#ffd700" opacity="0.8"/>
                                     <circle cx="275" cy="155" r="10" fill="#4caf50" opacity="0.8"/>
                                     <circle cx="260" cy="180" r="8" fill="#f44336" opacity="0.8"/>
                                 </g>

                                 <!-- 购物车图标 - 调整大小 -->
                                 <path d="M100,85 h25 l12,-25 h-50 z" fill="#fff" opacity="0.6"/>
                                 
                                 <!-- 数据流动效果 - 调整位置 -->
                                 <g class="data-stream">
                                     <circle cx="150" cy="65" r="2.5" fill="#fff" opacity="0.8"/>
                                     <circle cx="175" cy="45" r="2" fill="#fff" opacity="0.6"/>
                                     <circle cx="190" cy="70" r="2" fill="#fff" opacity="0.7"/>
                                 </g>
                             </g>

                             <!-- 动画样式保持不变 -->
                             <style>
                                 @keyframes float {
                                     0% { transform: translateY(0); }
                                     50% { transform: translateY(-5px); }
                                     100% { transform: translateY(0); }
                                 }
                                 @keyframes pulse {
                                     0% { opacity: 0.4; }
                                     50% { opacity: 0.8; }
                                     100% { opacity: 0.4; }
                                 }
                                 .floating-elements {
                                     animation: float 3s ease-in-out infinite;
                                 }
                                 .data-stream {
                                     animation: pulse 2s infinite;
                                 }
                                 .screen-content line {
                                     animation: pulse 1.5s infinite alternate;
                                 }
                             </style>
                         </svg>
                       </figure>
                        <div class="wt-yeras d-flex align-items-center text-center justify-content-center aos-init aos-animate" data-aos="fade-up">
                           <h4 class="text-center">16 +
                            <span class="d-lg-block">年经营经验</span>
                           </h4>
                        </div>
                  </div>
               </div>
               <div class="col">
                   <h5 data-aos="fade-down">关于我们</h5>
                   <h2 class="text-white my-2" data-aos="fade-down">专业的电商服务平台
                    <span class="d-lg-block" data-aos="fade-up">助您打造成功的网上商店</span>
                   </h2>
                   <p class="mt-3" data-aos="fade-up">我们提供全方位的电商解决方案，包括商品管理、订单处理、物流配送、营销推广等服务，帮助您轻松开展线上业务。专业的技术团队为您提供7*24小时支持，确保您的店铺稳定运营。</p>
                  <p class="mt-3">依托强大的平台优势和丰富的行业经验，我们致力于为商家提供更好的服务体验，助力您的业务快速增长。加入我们，开启您的电商成功之旅！</p>
                  
                  <a href="/merchant/login" class="read-btn btn mt-4" data-aos="fade-down"> <span> 立即登录 </span> </a>
               </div>
            </div>
        </div>
     </section>



     <section class="review-main-astroly d-inline-block w-100 mt-5">
       <div class="container">
           <div class="row row-cols-2 row-cols-lg-4 mb-5">
              <div class="col">
                 <div class="feate text-center" data-aos="fade-down">
                    <h2 class="text-white">24<span>小时</span></h2>
                    <p class="text-white"> 全天候客服在线<br>
                      为您服务</p>
                 </div>
              </div>
              <div class="col">
                <div class="feate text-center" data-aos="fade-up">
                   <h2 class="text-white">50 <span>+</span></h2>
                   <p class="text-white"> 商品种类<br>
                    应有尽有</p>
                </div>
              </div>

              <div class="col">
                <div class="feate text-center" data-aos="fade-down">
                   <h2 class="text-white">99 <span>+</span></h2>
                   <p class="text-white"> 优质商家<br>
                    入驻平台</p>
                </div>
              </div>
              <div class="col">
                <div class="feate text-center" data-aos="fade-up">
                   <h2 class="text-white">99 <span>+</span></h2>
                   <p class="text-white"> 成功交易<br>
                    用户好评</p>
                </div>
              </div>
           </div>
              
          </div>
           
       </div>
     </section>


 </main>

 <footer class="float-start w-100 pt-5">
    <div class="container">
       <div class="row row-cols-1 row-cols-sm-2 row-cols-lg-4 align-content-center">
          <div class="col">
             <a href="/">
               <img alt="logo" src="{$logo}">
             </a>
             <p class="text-white mt-3 col-lg-10">我们致力于为您提供优质的商品和服务,让您享受愉快的购物体验</p>
          </div>
          <div class="col">
             <div class="colm-footer">
                <h5>联系我们</h5>
                <ul>
                  <li> <i class="fab fa-whatsapp"></i> ************</li>
                  <li> <i class="fas fa-paper-plane"></i> <EMAIL></li>
                  <li> <i class="fas fa-phone-alt"></i> ************</li>
                </ul>
             </div>
          </div>
          <div class="col">
            <div class="colm-footer">
               <h5>我们的服务</h5>
               <ul>
                 <li> <a href="">商品分类</a></li>
                 <li> <a href="">促销活动</a></li>
                 <li> <a href="">会员服务</a></li>
               </ul>
            </div>
         </div>
         <div class="col">
          <div class="colm-footer">
             <h5>快速链接</h5>
             <ul>
               <li> <a href="">关于我们</a></li>
               <li> <a href="">商城资讯</a></li>
               <li> <a href="">联系我们</a></li>
             </ul>
          </div>
        </div>
       </div>
       <hr class="my-4">
       <div class="d-md-flex align-items-center justify-content-between">
          <p>Copyright &copy; {:date('Y')} {$siteName} All rights reserved.</p>
          {if !empty($icpNumber) || !empty($gaNumber)}
          <div class="beian-info">
              {if !empty($icpNumber)}
              <a href="https://beian.miit.gov.cn" target="_blank">{$icpNumber}</a>
              {/if}
              {if !empty($gaNumber)}
              <span class="mx-2">|</span>
              <a href="http://www.beian.gov.cn" target="_blank">
                  <img src="/assets/template/default/assets/wangan.a20583c8.png" alt="公安备案图标" style="vertical-align: -2px; margin-right: 3px; width: 16px; height: 16px;">
                  {$gaNumber}
              </a>
              {/if}
              {if !empty($icpCert)}
              <span class="mx-2">|</span>
              <span>{$icpCert}</span>
              {/if}
          </div>
          {/if}
       </div>
    </div>
 </footer>


<!-- mobile menu -->

<div class="offcanvas offcanvas-end mobile-menu-div" id="mobile-menu">
  <div class="offcanvas-header">
    
     <button type="button" class="close-menu" data-bs-dismiss="offcanvas">
      <span>关闭</span> <i class="fas fa-long-arrow-alt-right"></i>
     </button>
  </div>
  
      
    <div class="offcanvas-body">
      
      <div class="head-contact">
         <a href="/" class="logo-side">
         <img src="{$logo}" alt="logo">
         </a>
         
         <div class="mobile-menu-sec mt-5">
          <nav class="navbar navbar-expand navbar-light">
            <div class="collapse navbar-collapse">
              <ul class="navbar-nav">
                {foreach $navItems as $item}
                <li class="nav-item {if !empty($item.children)}dropdown{/if}">
                    <a class="nav-link {if !empty($item.children)}dropdown-toggle{/if}" 
                       href="{$item.href}"
                       {if $item.target}target="_blank"{/if}
                       {if !empty($item.children)}data-bs-toggle="dropdown"{/if}>
                        {$item.name}
                        {if !empty($item.children)}
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor" class="dropdown-arrow" viewBox="0 0 16 16">
                            <path d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z"/>
                        </svg>
                        {/if}
                    </a>
                    {if !empty($item.children)}
                    <ul class="dropdown-menu">
                        {foreach $item.children as $child}
                        <li>
                            <a class="dropdown-item" 
                               href="{$child.href}"
                               {if $child.target}target="_blank"{/if}>
                                {$child.name}
                            </a>
                        </li>
                        {/foreach}
                    </ul>
                    {/if}
                </li>
                {/foreach}
              </ul>
            </div>
          </nav>
         </div>
         
         <ul class="side-media list-unstyled">
            <li> <a href=""> <i class="fab fa-facebook-f"></i> </a> </li>
            <li> <a href=""> <i class="fab fa-twitter"></i> </a> </li>
            <li> <a href=""> <i class="fab fa-google-plus-g"></i> </a> </li>
            <li> <a href=""> <i class="fab fa-instagram"></i> </a> </li>
         </ul>
      </div>
    </div>
    
 
</div>

<script src="/static/yibazhan/js/bootstrap.bundle.min.js"></script>
<script src="/static/yibazhan/js/jquery.min.js"></script>
<script src="/static/yibazhan/js/custom.js"></script>
<script src="/static/yibazhan/js/aos.js"></script>
<script src="/static/yibazhan/js/owl.carousel.min.js"></script>
<script src="/static/yibazhan/js/jquery-ui.min.js"></script>
<script>
    AOS.init({
      offset: 100,
      easing: 'ease',
      delay: 0,
      once: true,
      duration: 800,
    });

    // 禁用右键菜单
    document.addEventListener('contextmenu', function(e) {
        e.preventDefault();
        return false;
    });

    // 禁用F12、Ctrl+Shift+I、Ctrl+Shift+J、Ctrl+Shift+C、Ctrl+U
    document.addEventListener('keydown', function(e) {
        if (
            e.keyCode === 123 || // F12
            (e.ctrlKey && e.shiftKey && e.keyCode === 73) || // Ctrl+Shift+I
            (e.ctrlKey && e.shiftKey && e.keyCode === 74) || // Ctrl+Shift+J
            (e.ctrlKey && e.shiftKey && e.keyCode === 67) || // Ctrl+Shift+C
            (e.ctrlKey && e.keyCode === 85) // Ctrl+U
        ) {
            e.preventDefault();
            return false;
        }
    });

    // 禁用开发者工具快捷键
    window.addEventListener('keydown', function(e) {
        if (e.defaultPrevented) {
            return;
        }
        if (e.key === 'F12' || 
            (e.ctrlKey && e.key === 'I') ||
            (e.ctrlKey && e.key === 'J') ||
            (e.ctrlKey && e.key === 'C') ||
            (e.ctrlKey && e.key === 'U')) {
            e.preventDefault();
        }
    });

    // 禁用控制台
    setInterval(function() {
        debugger;
    }, 100);
</script>

</body>
</html>
