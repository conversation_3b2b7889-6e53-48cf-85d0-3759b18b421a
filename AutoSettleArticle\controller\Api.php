<?php

namespace plugin\AutoSettleArticle\controller;

use app\common\controller\BasePlugin;
use think\facade\View;

class Api extends BasePlugin {

    protected $scene = [
        'admin'
    ];
    protected $noNeedLogin = [];

    public function index() {
        return View::fetch();
    }

    public function fetchData() {
        $params = [
            'status' => intval(plugconf("AutoSettleArticle.status") ?? 0),
            'title' => base64_decode(plugconf("AutoSettleArticle.base64_title") ?? ''),
            'content' => base64_decode(plugconf("AutoSettleArticle.base64_content") ?? ''),
            'time' => plugconf("AutoSettleArticle.time") ?? '',
        ];

        $this->success('success', $params);
    }

    public function save() {
        $status = $this->request->post('status/d', 0);
        $title = $this->request->post('title/s', '');
        $content = $this->request->post('content/s', '');
        $time = $this->request->post('time/s', '');

        plugconf("AutoSettleArticle.status", $status);
        plugconf("AutoSettleArticle.base64_title", base64_encode($title));
        plugconf("AutoSettleArticle.base64_content", base64_encode($content));
        plugconf("AutoSettleArticle.time", $time);

        $this->success('success');
    }
}
