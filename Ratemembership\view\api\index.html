<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>渠道组管理</title>
    <style>
        .page-container {
            padding: 20px;
            min-height: 800px;
        }
        .button-container {
            margin-bottom: 20px;
        }
        .el-table {
            margin-top: 20px;
        }
        .el-form-item-tip {
            color: #909399;
            font-size: 12px;
            margin-top: 5px;
        }
        
        .el-table {
            margin-bottom: 20px;
        }

        .search-form {
            margin-bottom: 20px;
        }
        
        .flex-row {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        /* 修复下拉框文字颜色问题 */
        .rate-select-dropdown .el-select-dropdown__item {
            color: #303133 !important;
        }
        
        .rate-select-dropdown .el-select-dropdown__item.selected {
            color: #409EFF !important;
            font-weight: bold;
        }
        
        .el-select-dropdown__item {
            color: #303133 !important;
        }
        
        .el-select-dropdown__item.selected {
            color: #409EFF !important;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="page-container">
            <el-tabs v-model="activeTab">
                <el-tab-pane label="等级管理" name="groups">
                    <div class="button-container">
                        <el-button type="primary" @click="handleAdd">添加渠道组</el-button>
                        <el-button type="primary" @click="showSettingDialog">时间设置</el-button>
                        <el-button type="success" @click="handleManualCalc">手动计算</el-button>
                    </div>

                    <el-table :data="channelGroups" border style="width: 100%">
                        <el-table-column prop="id" label="ID" width="60"></el-table-column>
                        <el-table-column prop="display_name" label="等级" min-width="120">
                            <template #default="scope">
                                <div>
                                    {{ scope.row.display_name }}
                                    <el-tag v-if="scope.row.is_hidden === true" size="small" type="info">隐藏</el-tag>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="费率" min-width="200">
                            <template #default="scope">
                                <div>
                                    平台：{{ scope.row.platform_rate }}%、
                                    直清合规：{{ scope.row.direct_rate }}%、
                                    商户自定义：{{ scope.row.merchant_rate }}%
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="费用" min-width="280">
                            <template #default="scope">
                                <div>
                                    月费：{{ scope.row.price }}元、
                                    季费：{{ scope.row.quarterly_price }}元、
                                    年费：{{ scope.row.yearly_price }}元
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="状态" width="100">
                            <template #default="scope">
                                <el-tag :type="scope.row.can_upgrade ? 'success' : 'info'">
                                    {{ scope.row.can_upgrade ? '可开通' : '不可开通' }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="150" align="center">
                            <template #default="scope">
                                <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
                                <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-tab-pane>
                
                <el-tab-pane label="会员管理" name="members">
                    <div class="button-container">
                        <el-button type="danger" @click="handleClearMembers">清空会员</el-button>
                        <el-button type="primary" @click="getMemberList(true)">
                            <i class="el-icon-refresh"></i> 刷新
                        </el-button>
                    </div>
                    
                    <el-table 
                        :data="memberList" 
                        border 
                        style="width: 100%"
                        v-loading="isLoadingMembers"
                        element-loading-text="加载中..."
                        :row-key="row => row.id">
                        <el-table-column prop="id" label="ID" width="80"></el-table-column>
                        <el-table-column prop="username" label="用户名"></el-table-column>
                        <el-table-column prop="group_name" label="当前等级"></el-table-column>
                        <el-table-column label="到期时间" width="180">
                            <template #default="scope">
                                {{ formatDate(scope.row.expire_time) }}
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="280">
                            <template #default="scope">
                                <div :id="'member-row-' + scope.row.id">
                                    <el-button type="primary" size="small" @click="handleEditExpireTime(scope.row)">
                                        修改到期时间
                                    </el-button>
                                    <el-button type="warning" size="small" @click="handleEditLevel(scope.row)">
                                        修改等级
                                    </el-button>
                                    <el-button type="danger" size="small" @click="handleDeleteMember(scope.row)">
                                        删除
                                    </el-button>
                                </div>
                            </template>
                        </el-table-column>
                        <template #empty>
                            <div style="text-align: center; padding: 20px 0;">
                                <p>暂无会员数据</p>
                            </div>
                        </template>
                    </el-table>
                    
                    <!-- 添加分页组件 -->
                    <div class="pagination-container" style="margin-top: 20px; text-align: right;">
                        <el-pagination
                            v-model:current-page="currentPage"
                            v-model:page-size="pageSize"
                            :page-sizes="[10, 20, 50, 100]"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="total"
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                        />
                    </div>
                </el-tab-pane>
            </el-tabs>

            <!-- 添加/编辑对话框 -->
            <el-dialog
                :title="dialogTitle"
                v-model="dialogVisible"
                width="500px">
                <el-form :model="form" label-width="120px">
                    <el-form-item label="等级名称">
                        <el-input v-model="form.name"></el-input>
                    </el-form-item>
                    <el-form-item label="显示名称">
                        <el-input v-model="form.level_name" placeholder="可选，用于显示在等级名称后"></el-input>
                        <div class="el-form-item-tip">例如：填写"白银会员"将显示为"默认分组 (白银会员)"</div>
                    </el-form-item>
                    
                    <!-- 统一费率设置 -->
                    <el-form-item label="统一平台费率">
                        <el-input v-model="form.unified_platform_rate" type="number">
                            <template #append>%</template>
                        </el-input>
                    </el-form-item>
                    <el-form-item label="统一直付通费率">
                        <el-input v-model="form.unified_zft_rate" type="number">
                            <template #append>%</template>
                        </el-input>
                    </el-form-item>
                    <el-form-item label="统一商户费率">
                        <el-input v-model="form.unified_merchant_rate" type="number">
                            <template #append>%</template>
                        </el-input>
                    </el-form-item>
                    
                    <!-- 单独费率设置开关 -->
                    <el-form-item label="自定义费率">
                        <el-switch v-model="form.use_custom_rates"></el-switch>
                    </el-form-item>
                    
                    <!-- 单独费率设置，仅在开启自定义费率时显示 -->
                    <template v-if="form.use_custom_rates">
                        <el-form-item label="平台渠道费率">
                            <div v-for="(rate, channel) in form.platform_rates" :key="'p'+channel" style="margin-bottom: 10px;">
                                <el-row :gutter="10">
                                    <el-col :span="12">
                                        <el-select v-model="rate.channel_id" placeholder="选择渠道">
                                            <el-option 
                                                v-for="ch in platformChannels" 
                                                :key="ch.id" 
                                                :label="ch.name" 
                                                :value="ch.id">
                                            </el-option>
                                        </el-select>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-input v-model="rate.rate" type="number" placeholder="费率">
                                            <template #append>%</template>
                                        </el-input>
                                    </el-col>
                                    <el-col :span="4">
                                        <el-button type="danger" @click="removePlatformRate(channel)">删除</el-button>
                                    </el-col>
                                </el-row>
                            </div>
                            <el-button type="primary" @click="addPlatformRate">添加平台渠道</el-button>
                        </el-form-item>

                        <el-form-item label="商户渠道费率">
                            <div v-for="(rate, channel) in form.merchant_rates" :key="'m'+channel" style="margin-bottom: 10px;">
                                <el-row :gutter="10">
                                    <el-col :span="12">
                                        <el-select v-model="rate.channel_id" placeholder="选择渠道">
                                            <el-option 
                                                v-for="ch in merchantChannels" 
                                                :key="ch.id" 
                                                :label="ch.name" 
                                                :value="ch.id">
                                            </el-option>
                                        </el-select>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-input v-model="rate.rate" type="number" placeholder="费率">
                                            <template #append>%</template>
                                        </el-input>
                                    </el-col>
                                    <el-col :span="4">
                                        <el-button type="danger" @click="removeMerchantRate(channel)">删除</el-button>
                                    </el-col>
                                </el-row>
                            </div>
                            <el-button type="primary" @click="addMerchantRate">添加商户渠道</el-button>
                        </el-form-item>
                    </template>

                    <el-form-item label="成交流水">
                        <el-input v-model="form.turnover" type="number"></el-input>
                    </el-form-item>
                    <el-form-item label="月费">
                        <el-input v-model="form.price" type="number">
                            <template #append>元</template>
                        </el-input>
                    </el-form-item>

                    <!-- 修改季费和年费设置 -->
                    <el-form-item label="季费">
                        <el-input v-model="form.quarterly_price" type="number">
                            <template #append>元</template>
                        </el-input>
                    </el-form-item>

                    <el-form-item label="年费">
                        <el-input v-model="form.yearly_price" type="number">
                            <template #append>元</template>
                        </el-input>
                    </el-form-item>

                    <el-form-item label="状态">
                        <el-switch v-model="form.can_upgrade"></el-switch>
                    </el-form-item>
                    <el-form-item label="隐藏等级">
                        <el-switch v-model="form.is_hidden"></el-switch>
                        <div class="el-form-item-tip">开启后，该等级在商户端不会显示，但可以手动设置给商户</div>
                    </el-form-item>
                    <el-form-item label="提示信息">
                        <el-input v-model="form.disabled_message" placeholder="等级不可用时的提示信息"></el-input>
                    </el-form-item>
                </el-form>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="dialogVisible = false">取消</el-button>
                        <el-button type="primary" @click="handleSubmit">确定</el-button>
                    </span>
                </template>
            </el-dialog>

            <!-- 时间设置对话框 -->
            <el-dialog
                title="时间设置"
                v-model="settingDialogVisible"
                width="500px">
                <el-form :model="settingForm" label-width="120px">
                    <el-form-item label="计算模式">
                        <el-select v-model="settingForm.calc_mode" placeholder="请选择计算模式" style="width: 100%;" popper-class="rate-select-dropdown">
                            <el-option label="实时模式" value="realtime">
                                <template #default>
                                    <div style="font-weight: 500; color: #303133; font-size: 14px;">实时模式</div>
                                    <small style="color: #909399; display: block; margin-top: 4px;">达到流水立即升级，开通会员也升级</small>
                                </template>
                            </el-option>
                            <el-option label="周期模式" value="period">
                                <template #default>
                                    <div style="font-weight: 500; color: #303133; font-size: 14px;">周期模式</div>
                                    <small style="color: #909399; display: block; margin-top: 4px;">根据上个周期流水，在指定时间统一更新</small>
                                </template>
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="检查间隔(秒)">
                        <el-input-number 
                            v-model="settingForm.update_interval" 
                            :min="60"
                            :max="86400"
                            :step="60"
                            :precision="0"
                            controls-position="right">
                        </el-input-number>
                        <div class="el-form-item-tip">可设置范围：60秒 ~ 86400秒（1天）</div>
                    </el-form-item>

                    <el-form-item label="等级更新周期">
                        <el-select v-model="settingForm.update_cycle" placeholder="请选择更新周期" style="width: 100%;" popper-class="rate-select-dropdown">
                            <el-option label="每天" value="daily">
                                <template #default>
                                    <div style="font-weight: 500; color: #303133; font-size: 14px;">每天</div>
                                    <small style="color: #909399; display: block; margin-top: 4px;" v-if="settingForm.calc_mode === 'period'">每天0点更新前一天数据</small>
                                </template>
                            </el-option>
                            <el-option label="每月" value="monthly">
                                <template #default>
                                    <div style="font-weight: 500; color: #303133; font-size: 14px;">每月</div>
                                    <small style="color: #909399; display: block; margin-top: 4px;" v-if="settingForm.calc_mode === 'period'">每月1号更新上月数据</small>
                                </template>
                            </el-option>
                            <el-option label="每季度" value="quarterly">
                                <template #default>
                                    <div style="font-weight: 500; color: #303133; font-size: 14px;">每季度</div>
                                    <small style="color: #909399; display: block; margin-top: 4px;" v-if="settingForm.calc_mode === 'period'">每季度第一个月1号更新上季度数据</small>
                                </template>
                            </el-option>
                            <el-option label="每年" value="yearly">
                                <template #default>
                                    <div style="font-weight: 500; color: #303133; font-size: 14px;">每年</div>
                                    <small style="color: #909399; display: block; margin-top: 4px;" v-if="settingForm.calc_mode === 'period'">每年1月1号更新上一年数据</small>
                                </template>
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="自动更新">
                        <el-switch 
                            v-model="settingForm.auto_update_status"
                            :active-value="1"
                            :inactive-value="0">
                        </el-switch>
                    </el-form-item>
                </el-form>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="settingDialogVisible = false">取消</el-button>
                        <el-button type="primary" @click="handleSettingSubmit">确定</el-button>
                    </span>
                </template>
            </el-dialog>

            <!-- 修改到期时间对话框 -->
            <el-dialog
                v-model="expireTimeDialogVisible"
                title="修改到期时间"
                width="400px">
                <el-form :model="expireTimeForm" label-width="100px">
                    <el-form-item label="到期时间">
                        <el-date-picker
                            v-model="expireTimeForm.expire_time"
                            type="datetime"
                            placeholder="选择到期时间"
                            format="YYYY-MM-DD HH:mm:ss"
                            value-format="X"
                            :clearable="true">
                        </el-date-picker>
                        <div class="tip-text" style="color: #909399; font-size: 12px; margin-top: 5px;">
                            清空日期表示永久有效
                        </div>
                    </el-form-item>
                </el-form>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="expireTimeDialogVisible = false">取消</el-button>
                        <el-button type="primary" @click="handleExpireTimeSubmit" class="expire-time-submit-btn">确定</el-button>
                    </span>
                </template>
            </el-dialog>

            <!-- 修改等级对话框 -->
            <el-dialog
                v-model="editLevelDialogVisible"
                title="修改等级"
                width="500px">
                <el-form :model="editLevelForm" label-width="100px">
                    <el-form-item label="用户名">
                        {{ editLevelForm.username }}
                    </el-form-item>
                    <el-form-item label="当前等级">
                        {{ editLevelForm.group_name }}
                    </el-form-item>
                    <el-form-item label="新等级">
                        <el-select v-model="editLevelForm.new_group_id" placeholder="请选择新等级">
                            <el-option
                                v-for="group in channelGroups"
                                :key="group.id"
                                :label="group.name"
                                :value="group.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
                <template #footer>
                    <el-button @click="editLevelDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="handleEditLevelSubmit" class="level-submit-btn">确定</el-button>
                </template>
            </el-dialog>

            <!-- 添加清空确认对话框 -->
            <el-dialog
                v-model="clearConfirmVisible"
                title="确认清空"
                width="400px">
                <div>确定要清空所有会员吗？此操作将重置所有会员等级为默认等级。</div>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="clearConfirmVisible = false">取消</el-button>
                        <el-button type="danger" @click="confirmClearMembers">确定</el-button>
                    </span>
                </template>
            </el-dialog>
        </div>
    </div>

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>

    <script>
        const { createApp, ref, onMounted } = Vue;
        const { ElMessage, ElMessageBox, ElLoading } = ElementPlus;

        const app = createApp({
            setup() {
                const channelGroups = ref([]);
                const dialogVisible = ref(false);
                const dialogTitle = ref('添加渠道组');
                const defaultGroupId = ref(null);
                const form = ref({
                    id: null,
                    name: '',
                    level_name: '',
                    turnover: '',
                    price: '',
                    quarterly_price: '',
                    yearly_price: '',
                    can_upgrade: true,
                    disabled_message: '该等级暂不开放',
                    platform_rates: [],
                    merchant_rates: [],
                    unified_platform_rate: '',
                    unified_zft_rate: '',
                    unified_merchant_rate: '',
                    use_custom_rates: false,
                    is_hidden: false,
                });
                const settingDialogVisible = ref(false);
                const settingForm = ref({
                    update_interval: 3600,
                    auto_update_status: 0,
                    update_cycle: 'monthly',
                    calc_mode: 'realtime'
                });
                const activeTab = ref('groups');
                const memberList = ref([]);
                const expireTimeDialogVisible = ref(false);
                const expireTimeForm = ref({
                    user_id: '',
                    expire_time: ''
                });
                const currentPage = ref(1);
                const pageSize = ref(10);
                const total = ref(0);
                const editLevelDialogVisible = ref(false);
                const editLevelForm = ref({
                    id: null,
                    username: '',
                    group_name: '',
                    new_group_id: null
                });
                const clearConfirmVisible = ref(false);

                // 添加渠道数据
                const platformChannels = ref([]);
                const merchantChannels = ref([]);

                // 获取会员列表
                const isLoadingMembers = ref(false);
                let getMemberListTimer = null;
                
                const getMemberList = async (forceRefresh = false) => {
                    // 防抖处理
                    if (getMemberListTimer) {
                        clearTimeout(getMemberListTimer);
                    }
                    
                    // 如果不是强制刷新且已经在加载中，则返回
                    if (!forceRefresh && isLoadingMembers.value) {
                        return;
                    }
                    
                    getMemberListTimer = setTimeout(async () => {
                        try {
                            isLoadingMembers.value = true;
                            const res = await axios.post('/plugin/Ratemembership/api/getMemberList', {
                                page: currentPage.value,
                                limit: pageSize.value
                            });
                            
                            if (res.data.code === 200) {
                                // 处理返回的数据，确保格式正确
                                memberList.value = res.data.data.list.map(item => ({
                                    ...item,
                                    group_name: item.group_name || '默认等级',
                                    expire_time: item.expire_time || 0
                                }));
                                total.value = res.data.data.total;
                            } else {
                                ElMessage.error(res.data.msg || '获取会员列表失败');
                            }
                        } catch (error) {
                            console.error('获取会员列表失败:', error);
                            ElMessage.error(error.response?.data?.msg || '网络错误，获取会员列表失败');
                        } finally {
                            isLoadingMembers.value = false;
                        }
                    }, 200); // 200ms防抖延迟
                };

                // 获取渠道列表
                const getChannels = async () => {
                    try {
                        const res = await axios.post('/plugin/Ratemembership/api/getChannels');
                        if (res.data.code === 200) {
                            platformChannels.value = res.data.data.platform_channels;
                            merchantChannels.value = res.data.data.merchant_channels;
                        }
                    } catch (error) {
                        ElMessage.error('获取渠道列表失败');
                    }
                };

                // 获取渠道组列表
                const getChannelGroups = async () => {
                    try {
                        const res = await axios.post('/plugin/Ratemembership/api/adminChannelGroups');
                        if (res.data.code === 200) {
                            channelGroups.value = res.data.data;
                            // 设置默认分组ID（最小ID）
                            defaultGroupId.value = Math.min(...channelGroups.value.map(g => g.id));
                        }
                    } catch (error) {
                        ElMessage.error('获取渠道组列表失败');
                    }
                };

                // 获取设置
                const getSettings = async () => {
                    try {
                        const res = await axios.post('/plugin/Ratemembership/api/getSettings');
                        if (res.data.code === 200) {
                            const data = res.data.data;
                            settingForm.value = {
                                update_interval: data.update_interval || 3600,
                                auto_update_status: data.auto_update_status || 0,
                                update_cycle: data.update_cycle || 'monthly',
                                calc_mode: data.calc_mode || 'realtime'
                            };
                            console.log('已获取设置:', settingForm.value);
                        } else {
                            throw new Error(res.data.msg);
                        }
                    } catch (error) {
                        console.error('获取设置出错:', error);
                        ElMessage.error('获取设置出错');
                    }
                };

                // 添加渠道组
                const handleAdd = () => {
                    form.value = { 
                        id: null, 
                        name: '', 
                        level_name: '',
                        turnover: '', 
                        price: '',
                        quarterly_price: '',
                        yearly_price: '',
                        can_upgrade: true, 
                        disabled_message: '该等级暂不开放', 
                        platform_rates: [], 
                        merchant_rates: [], 
                        unified_platform_rate: '', 
                        unified_zft_rate: '', 
                        unified_merchant_rate: '', 
                        use_custom_rates: false,
                        is_hidden: false,
                    };
                    dialogTitle.value = '添加渠道组';
                    dialogVisible.value = true;
                };

                // 编辑渠道组
                const handleEdit = (row) => {
                    form.value = {
                        id: row.id,
                        name: row.name,
                        level_name: row.display_name.match(/\((.*?)\)$/)?.[1] || '',
                        unified_platform_rate: parseFloat(row.platform_rate) || 0,
                        unified_zft_rate: parseFloat(row.direct_rate) || 0,
                        unified_merchant_rate: parseFloat(row.merchant_rate) || 0,
                        turnover: parseFloat(row.turnover) || 0,
                        price: parseFloat(row.price) || 0,
                        quarterly_price: parseFloat(row.quarterly_price) || 0,
                        yearly_price: parseFloat(row.yearly_price) || 0,
                        can_upgrade: row.can_upgrade === true || row.can_upgrade === 1 || row.can_upgrade === '1',
                        disabled_message: row.disabled_message || '该等级暂不开放',
                        use_custom_rates: false,
                        platform_rates: [],
                        merchant_rates: [],
                        is_hidden: row.is_hidden === true || row.is_hidden === 1 || row.is_hidden === '1',
                    };
                    dialogTitle.value = '编辑等级';
                    dialogVisible.value = true;
                };

                // 删除渠道组
                const handleDelete = async (row) => {
                    try {
                        // 检查是否是默认等级
                        if (row.id === defaultGroupId.value) {
                            ElMessage.warning('默认等级不能删除');
                            return;
                        }

                        await ElMessageBox.confirm('确定要删除该等级吗？删除后无法恢复。', '提示', {
                            type: 'warning',
                            confirmButtonText: '确定删除',
                            cancelButtonText: '取消',
                            confirmButtonClass: 'el-button--danger'
                        });
                        
                        const res = await axios.post('/plugin/Ratemembership/api/deleteChannelGroup', {
                            id: row.id
                        });

                        if (res.data.code === 200) {
                            ElMessage.success('删除成功');
                            getChannelGroups();
                        } else {
                            throw new Error(res.data.msg);
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            ElMessage.error(error.message || '删除失败');
                        }
                    }
                };

                // 显示设置对话框
                const showSettingDialog = () => {
                    getSettings();
                    settingDialogVisible.value = true;
                };

                // 提交表单
                const handleSubmit = async () => {
                    if (!form.value.name) {
                        ElMessage.warning('请输入等级名称');
                        return;
                    }

                    try {
                        const formData = {
                            id: form.value.id,
                            name: form.value.name,
                            level_name: form.value.level_name,
                            turnover: form.value.turnover,
                            price: form.value.price,
                            quarterly_price: form.value.quarterly_price,
                            yearly_price: form.value.yearly_price,
                            can_upgrade: form.value.can_upgrade,
                            disabled_message: form.value.disabled_message,
                            use_custom_rates: form.value.use_custom_rates,
                            is_hidden: form.value.is_hidden,
                        };

                        // 根据是否使用自定义费率添加不同的费率数据
                        if (form.value.use_custom_rates) {
                            formData.platform_rates = form.value.platform_rates;
                            formData.merchant_rates = form.value.merchant_rates;
                        } else {
                            formData.unified_platform_rate = form.value.unified_platform_rate;
                            formData.unified_zft_rate = form.value.unified_zft_rate;
                            formData.unified_merchant_rate = form.value.unified_merchant_rate;
                        }
                        
                        const url = '/plugin/Ratemembership/api/saveChannelGroup';
                        const res = await axios.post(url, formData);

                        if (res.data.code === 200) {
                            ElMessage.success(form.value.id ? '更新成功' : '添加成功');
                            dialogVisible.value = false;
                            getChannelGroups();
                        } else {
                            throw new Error(res.data.msg);
                        }
                    } catch (error) {
                        ElMessage.error(error.message || '操作失败');
                    }
                };

                // 提交设置
                const handleSettingSubmit = async () => {
                    try {
                        const res = await axios.post('/plugin/Ratemembership/api/saveSettings', {
                            update_interval: parseInt(settingForm.value.update_interval),
                            auto_update_status: parseInt(settingForm.value.auto_update_status),
                            update_cycle: settingForm.value.update_cycle,
                            calc_mode: settingForm.value.calc_mode
                        });
                        
                        if (res.data.code === 200) {
                            ElMessage.success('保存成功');
                            settingDialogVisible.value = false;
                        } else {
                            throw new Error(res.data.msg);
                        }
                    } catch (error) {
                        ElMessage.error(error.message || '保存失败');
                    }
                };

                // 手动计算
                const handleManualCalc = async () => {
                    try {
                        await ElMessageBox.confirm('确定要手动计算商家流水吗？', '提示', {
                            type: 'warning'
                        });
                        
                        const res = await axios.post('/plugin/Ratemembership/api/handleManualCalc');
                        if (res.data.code === 200) {
                            ElMessage.success(res.data.msg || '计算成功');
                            getMemberList();
                        } else {
                            throw new Error(res.data.msg);
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            ElMessage.error(error.message || '计算失败');
                        }
                    }
                };

                // 处理修改到期时间按钮点击
                const handleEditExpireTime = (row) => {
                    expireTimeForm.value = {
                        user_id: row.id,
                        expire_time: row.expire_time || ''
                    };
                    expireTimeDialogVisible.value = true;
                };

                // 处理到期时间提交
                const handleExpireTimeSubmit = async () => {
                    // 添加提交按钮禁用状态防止重复提交
                    const submitBtn = document.querySelector('.expire-time-submit-btn');
                    if (submitBtn) submitBtn.disabled = true;
                    
                    try {
                        const res = await axios.post('/plugin/Ratemembership/api/updateExpireTime', {
                            user_id: expireTimeForm.value.user_id,
                            expire_time: expireTimeForm.value.expire_time
                        });
                        
                        if (res.data.code === 200) {
                            ElMessage.success('更新成功');
                            expireTimeDialogVisible.value = false;
                            
                            // 仅刷新当前用户的数据，而不是整个表格
                            const index = memberList.value.findIndex(user => user.id === expireTimeForm.value.user_id);
                            if (index !== -1) {
                                // 使用Vue的响应式API更新数据，避免整体替换数组
                                memberList.value[index] = {
                                    ...memberList.value[index],
                                    expire_time: expireTimeForm.value.expire_time || 0
                                };
                            } else {
                                // 如果找不到该用户，才刷新整个列表
                                getMemberList();
                            }
                        } else {
                            ElMessage.error(res.data.msg || '更新失败');
                        }
                    } catch (error) {
                        console.error('更新到期时间失败:', error);
                        ElMessage.error(error.response?.data?.msg || '网络错误，更新失败');
                    } finally {
                        // 恢复按钮状态
                        if (submitBtn) submitBtn.disabled = false;
                    }
                };

                // 格式化日期显示
                const formatDate = (timestamp) => {
                    if (!timestamp) return '永久';
                    return new Date(timestamp * 1000).toLocaleString();
                };

                // 处理页码改变
                const handleCurrentChange = (val) => {
                    currentPage.value = val;
                    getMemberList();
                };

                // 处理每页条数改变
                const handleSizeChange = (val) => {
                    pageSize.value = val;
                    currentPage.value = 1;
                    getMemberList();
                };

                // 修改等级
                const handleEditLevel = (row) => {
                    editLevelForm.value = {
                        id: row.id,
                        username: row.username,
                        group_name: row.group_name,
                        new_group_id: row.channel_group_id
                    };
                    editLevelDialogVisible.value = true;
                };

                // 提交修改等级
                const handleEditLevelSubmit = async () => {
                    // 添加提交按钮禁用状态防止重复提交
                    const levelSubmitBtn = document.querySelector('.level-submit-btn');
                    if (levelSubmitBtn) levelSubmitBtn.disabled = true;
                    
                    try {
                        // 检查新选择的等级是否与当前等级相同
                        if (editLevelForm.value.new_group_id === memberList.value.find(
                            user => user.id === editLevelForm.value.id
                        )?.channel_group_id) {
                            ElMessage.info('没有修改等级，保持原等级不变');
                            editLevelDialogVisible.value = false;
                            return;
                        }
                        
                        const res = await axios.post('/plugin/Ratemembership/api/updateMemberLevel', {
                            user_id: editLevelForm.value.id,
                            group_id: editLevelForm.value.new_group_id
                        });

                        if (res.data.code === 200) {
                            ElMessage.success('修改成功');
                            editLevelDialogVisible.value = false;
                            
                            // 查找新等级名称
                            const newGroup = channelGroups.value.find(group => group.id === editLevelForm.value.new_group_id);
                            
                            // 仅更新当前用户的数据，而不是整个表格
                            const index = memberList.value.findIndex(user => user.id === editLevelForm.value.id);
                            if (index !== -1 && newGroup) {
                                // 使用Vue的响应式API更新数据，避免整体替换数组
                                memberList.value[index] = {
                                    ...memberList.value[index],
                                    channel_group_id: editLevelForm.value.new_group_id,
                                    group_name: newGroup.display_name || newGroup.name
                                };
                            } else {
                                // 如果找不到该用户或新等级，才刷新整个列表
                                getMemberList();
                            }
                        } else {
                            throw new Error(res.data.msg || '修改失败');
                        }
                    } catch (error) {
                        console.error('修改等级失败:', error);
                        ElMessage.error(error.message || error.response?.data?.msg || '网络错误，修改失败');
                    } finally {
                        // 恢复按钮状态
                        if (levelSubmitBtn) levelSubmitBtn.disabled = false;
                    }
                };

                // 添加平台渠道费率
                const addPlatformRate = () => {
                    form.value.platform_rates.push({
                        channel_id: '',
                        rate: ''
                    });
                };

                // 删除平台渠道费率
                const removePlatformRate = (index) => {
                    form.value.platform_rates.splice(index, 1);
                };

                // 添加商户渠道费率
                const addMerchantRate = () => {
                    form.value.merchant_rates.push({
                        channel_id: '',
                        rate: ''
                    });
                };

                // 删除商户渠道费率
                const removeMerchantRate = (index) => {
                    form.value.merchant_rates.splice(index, 1);
                };

                // 处理清空按钮点击
                const handleClearMembers = () => {
                    clearConfirmVisible.value = true;
                };
                
                // 确认清空会员
                const confirmClearMembers = async () => {
                    const loadingInstance = ElLoading.service({
                        text: '正在清空会员数据...',
                        background: 'rgba(255, 255, 255, 0.7)'
                    });
                    
                    try {
                        const res = await axios.post('/plugin/Ratemembership/api/clearMembers');
                        if (res.data.code === 200) {
                            ElMessage.success('清空成功');
                            clearConfirmVisible.value = false;
                            
                            // 直接清空当前列表数据，不需要网络请求
                            memberList.value = [];
                            total.value = 0;
                        } else {
                            throw new Error(res.data.msg || '清空失败');
                        }
                    } catch (error) {
                        console.error('清空会员失败:', error);
                        ElMessage.error(error.message || error.response?.data?.msg || '网络错误，清空失败');
                    } finally {
                        loadingInstance.close();
                    }
                };

                // 删除会员
                const handleDeleteMember = (row) => {
                    ElMessageBox.confirm(
                        '确定要删除该会员吗？此操作将重置该会员等级为默认等级。',
                        '提示',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    ).then(async () => {
                        try {
                            // 显示加载中状态
                            const loadingInstance = ElLoading.service({
                                target: document.querySelector(`#member-row-${row.id}`),
                                text: '处理中...',
                                background: 'rgba(255, 255, 255, 0.7)'
                            });
                            
                            const res = await axios.post('/plugin/Ratemembership/api/deleteMerchant', {
                                user_id: row.id
                            });
                            
                            if (res.data.code === 200) {
                                ElMessage.success('删除成功');
                                // 直接从列表中移除该会员，不刷新整个列表
                                const index = memberList.value.findIndex(item => item.id === row.id);
                                if (index !== -1) {
                                    memberList.value.splice(index, 1);
                                    // 更新总数
                                    if (total.value > 0) {
                                        total.value -= 1;
                                    }
                                } else {
                                    // 如果找不到该会员，才刷新整个列表
                                    getMemberList();
                                }
                            } else {
                                throw new Error(res.data.msg || '删除失败');
                            }
                            
                            loadingInstance.close();
                        } catch (error) {
                            console.error('删除会员失败:', error);
                            ElMessage.error(error.message || error.response?.data?.msg || '网络错误，删除失败');
                        }
                    }).catch(() => {
                        // 用户取消删除操作
                    });
                };

                onMounted(() => {
                    getChannelGroups();
                    getMemberList();
                    getChannels();
                });

                return {
                    channelGroups,
                    dialogVisible,
                    dialogTitle,
                    form,
                    handleAdd,
                    handleEdit,
                    handleDelete,
                    handleSubmit,
                    settingDialogVisible,
                    settingForm,
                    showSettingDialog,
                    handleSettingSubmit,
                    handleManualCalc,
                    defaultGroupId,
                    activeTab,
                    memberList,
                    expireTimeDialogVisible,
                    expireTimeForm,
                    handleEditExpireTime,
                    handleExpireTimeSubmit,
                    formatDate,
                    currentPage,
                    pageSize,
                    total,
                    handleCurrentChange,
                    handleSizeChange,
                    editLevelDialogVisible,
                    editLevelForm,
                    handleEditLevel,
                    handleEditLevelSubmit,
                    platformChannels,
                    merchantChannels,
                    addPlatformRate,
                    removePlatformRate,
                    addMerchantRate,
                    removeMerchantRate,
                    clearConfirmVisible,
                    handleClearMembers,
                    confirmClearMembers,
                    handleDeleteMember,
                    isLoadingMembers
                };
            }
        });

        app.use(ElementPlus, {
            locale: ElementPlusLocaleZhCn,
        });
        
        app.mount('#app');
    </script>
</body>
</html>
