<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>抽奖系统管理</title>
</head>
<body>
<!-- 加载动画 -->
<div id="loading">
    <div class="spinner"></div>
</div>

<div id="app" style="display: none">
    <el-container>
        <!-- 侧边栏 -->
        <el-aside width="200px">
            <el-menu
                :default-active="activeMenu"
                @select="handleMenuSelect"
                class="sidebar-menu">
                <el-menu-item index="prize-settings">
                    <svg class="icon" viewBox="0 0 1024 1024" width="16" height="16">
                        <path d="M832 192h-80.896l-35.52-71.04A32 32 0 0 0 687.328 96H336.672a32 32 0 0 0-28.256 24.96L272.896 192H192a32 32 0 0 0-32 32v640a32 32 0 0 0 32 32h640a32 32 0 0 0 32-32V224a32 32 0 0 0-32-32zM512 736a160 160 0 1 1 0-320 160 160 0 0 1 0 320z" fill="currentColor"/>
                    </svg>
                    <span>奖品设置</span>
                </el-menu-item>
                <el-menu-item index="lottery-records">
                    <svg class="icon" viewBox="0 0 1024 1024" width="16" height="16">
                        <path d="M832 128H192a32 32 0 0 0-32 32v704a32 32 0 0 0 32 32h640a32 32 0 0 0 32-32V160a32 32 0 0 0-32-32zM288 288h448v64H288v-64zm0 192h448v64H288v-64zm0 192h288v64H288v-64z" fill="currentColor"/>
                    </svg>
                    <span>获奖记录</span>
                </el-menu-item>
                <el-menu-item index="turnover-settings">
                    <svg class="icon" viewBox="0 0 1024 1024" width="16" height="16">
                        <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm144 328c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V308H432v84c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V276c0-4.4 3.6-8 8-8h288c4.4 0 8 3.6 8 8v116zm-144 276c-48.5 0-88-39.5-88-88s39.5-88 88-88 88 39.5 88 88-39.5 88-88 88z" fill="currentColor"/>
                    </svg>
                    <span>流水设置</span>
                </el-menu-item>
            </el-menu>
        </el-aside>

        <!-- 主内容区 -->
        <el-main>
            <!-- 奖品设置面板 -->
            <el-card v-show="activeMenu === 'prize-settings'" class="main-card">
                <template #header>
                    <div class="card-header">
                        <span>奖品管理</span>
                        <div class="header-btns">
                            <el-button type="primary" @click="handleAddPrize">
                                <svg class="icon" viewBox="0 0 1024 1024" width="16" height="16">
                                    <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm192 472H544v160c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V536H320c-4.4 0-8-3.6-8-8v-48c0-4.4 3.6-8 8-8h160V312c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v160h160c4.4 0 8 3.6 8 8v48c0 4.4-3.6 8-8 8z" fill="currentColor"/>
                                </svg>
                                添加奖品
                            </el-button>
                            <el-button type="success" @click="handleAddType">
                                <svg class="icon" viewBox="0 0 1024 1024" width="16" height="16">
                                    <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm192 472H544v160c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V536H320c-4.4 0-8-3.6-8-8v-48c0-4.4 3.6-8 8-8h160V312c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v160h160c4.4 0 8 3.6 8 8v48c0 4.4-3.6 8-8 8z" fill="currentColor"/>
                                </svg>
                                添加类型
                            </el-button>
                            <el-button type="danger" @click="showTypeManage">
                                <svg class="icon" viewBox="0 0 1024 1024" width="16" height="16">
                                    <path d="M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56c10.1-8.6 13.8-22.6 9.3-35.2l-0.9-2.6c-18.1-50.5-44.9-96.9-79.7-137.9l-1.8-2.1c-8.6-10.1-22.5-13.9-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85c-2.4-13.1-12.7-23.3-25.8-25.7l-2.7-0.5c-52.1-9.4-106.9-9.4-159 0l-2.7 0.5c-13.1 2.4-23.4 12.6-25.8 25.7l-15.8 85.4c-35.9 13.6-69.2 32.9-99 57.4l-81.9-29.1c-12.5-4.4-26.5-0.7-35.1 9.5l-1.8 2.1c-34.8 41.1-61.6 87.5-79.7 137.9l-0.9 2.6c-4.5 12.5-0.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5c-10.1 8.6-13.8 22.6-9.3 35.2l0.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1c8.6 10.1 22.5 13.9 35.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4c2.4 13.1 12.7 23.3 25.8 25.7l2.7 0.5c26.1 4.7 52.8 7.1 79.5 7.1 26.7 0 53.5-2.4 79.5-7.1l2.7-0.5c13.1-2.4 23.4-12.6 25.8-25.7l15.7-85c36.2-13.6 69.7-32.9 99.7-57.6l81.3 29.1c12.5 4.4 26.5 0.7 35.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l0.9-2.6c4.5-12.3 0.8-26.3-9.3-35zM512 682c-93.9 0-170-76.1-170-170s76.1-170 170-170 170 76.1 170 170-76.1 170-170 170z" fill="currentColor"/>
                                </svg>
                                管理类型
                            </el-button>
                            <el-button type="primary" @click="showLotterySettings">
                                <svg class="icon" viewBox="0 0 1024 1024" width="16" height="16">
                                    <path d="M832 192h-80.896l-35.52-71.04A32 32 0 0 0 687.328 96H336.672a32 32 0 0 0-28.256 24.96L272.896 192H192a32 32 0 0 0-32 32v640a32 32 0 0 0 32 32h640a32 32 0 0 0 32-32V224a32 32 0 0 0-32-32zM512 736a160 160 0 1 1 0-320 160 160 0 0 1 0 320z" fill="currentColor"/>
                                </svg>
                                抽奖设置
                            </el-button>
                            <el-button type="warning" @click="showResetDialog">
                                <svg class="icon" viewBox="0 0 1024 1024" width="16" height="16">
                                    <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z" fill="currentColor"/>
                                    <path d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z" fill="currentColor"/>
                                </svg>重置次数
                            </el-button>
                            <el-button type="warning" @click="showHiddenPrizeSettings">
                                <svg class="icon" viewBox="0 0 1024 1024" width="16" height="16">
                                    <path d="M942.2 486.2C847.4 286.5 704.1 186 512 186c-192.2 0-335.4 100.5-430.2 300.3a60.3 60.3 0 0 0 0 51.5C176.6 737.5 319.9 838 512 838c192.2 0 335.4-100.5 430.2-300.3 7.7-16.2 7.7-35 0-51.5zM512 766c-161.3 0-279.4-81.8-362.7-254C232.6 339.8 350.7 258 512 258c161.3 0 279.4 81.8 362.7 254C791.5 684.2 673.4 766 512 766zm-4-430c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z" fill="currentColor"/>
                                </svg>
                                隐藏奖品设置
                            </el-button>
                            <!--<el-switch-->
                            <!--    v-model="autoSendBalance"-->
                            <!--    active-text="自动发放余额"-->
                            <!--    @change="handleAutoSendChange">-->
                            <!--</el-switch>-->
                        </div>
                    </div>
                </template>

                <!-- 奖品列表 -->
                <el-table :data="tableData" v-loading="tableLoading" :height="tableHeight">
                    <el-table-column label="奖品信息">
                        <template #default="{ row }">
                            <div class="prize-row">
                                <div class="prize-info">
                                    <el-tag size="small" type="info">ID: {{ row.id }}</el-tag>
                                    <span class="prize-name">{{ row.name }}</span>
                                    <el-tag 
                                        size="small"
                                        :type="getPrizeTypeTag(row.type)"
                                        :style="getPrizeTypeStyle(row.type)">
                                        {{ getPrizeTypeText(row.type) }}
                                    </el-tag>
                                    <span>概率: {{ row.probability }}%</span>
                                    <span>库存: {{ row.stock }}</span>
                                    <span>余额: {{ row.balance_amount }} 元</span>
                                </div>
                                <div class="prize-actions">
                                    <el-switch
                                        v-model="row.status"
                                        :active-value="1"
                                        :inactive-value="0"
                                        @change="val => handleStatusChange(row, val)" />
                                    <el-button-group>
                                        <el-button type="primary" link @click="handleEdit(row)">
                                            <svg class="icon" viewBox="0 0 1024 1024" width="16" height="16">
                                                <path d="M880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32zm-622.3-84c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9c3.9-3.9 3.9-10.2 0-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2c-1.9 11.1 1.5 21.9 9.4 29.8 6.6 6.4 14.9 9.9 23.8 9.9z" fill="currentColor"/>
                                            </svg>
                                            编辑
                                        </el-button>
                                        <el-button type="danger" link @click="handleDelete(row)">
                                            <svg class="icon" viewBox="0 0 1024 1024" width="16" height="16">
                                                <path d="M864 256H736v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zm-200 0H360v-72h304v72z" fill="currentColor"/>
                                            </svg>
                                            删除
                                        </el-button>
                                    </el-button-group>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="180">
                        <template #default="scope">
                            <el-button-group>
                                <el-button 
                                    type="primary" 
                                    size="small" 
                                    @click="handleEdit(scope.row)">
                                    编辑
                                </el-button>
                                <el-button 
                                    type="danger" 
                                    size="small" 
                                    @click="handleDelete(scope.row)">
                                    删除
                                </el-button>
                            </el-button-group>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页 -->
                <el-pagination
                    v-if="pagination.total > 0"
                    class="pagination"
                    @current-change="handlePageChange"
                    :current-page="pagination.page"
                    :page-size="pagination.pageSize"
                    :total="pagination.total"
                    layout="total, prev, pager, next, sizes"
                    :page-sizes="[10, 20, 50, 100]" />
            </el-card>

            <!-- 获奖记录面板 -->
            <el-card v-show="activeMenu === 'lottery-records'" class="main-card">
                <template #header>
                    <div class="card-header">
                        <span>获奖记录</span>
                        <div class="header-btns">
                            <el-button 
                                type="primary" 
                                :disabled="!selectedRecords.length"
                                @click="handleBatchShipment">
                                批量发货
                            </el-button>
                            <!-- 添加批量删除按钮 -->
                            <el-button 
                                type="danger" 
                                :disabled="!selectedRecords.length"
                                @click="handleBatchDelete">
                                批量删除
                            </el-button>
                            <el-button type="success" @click="showGenerateDialog">
                                生成虚拟中奖记录
                            </el-button>
                            <el-button type="danger" @click="showClearDialog">清空记录</el-button>
                        </div>
                    </div>
                </template>

                <!-- 在记录列表上方添加搜索表单 -->
                <template v-if="activeMenu === 'lottery-records'">
                    <div class="search-form">
                        <el-form :inline="true" class="demo-form-inline">
                            <el-form-item label="商户ID">
                                <el-input
                                    v-model="searchForm.merchantId"
                                    placeholder="请输入商户ID"
                                    clearable
                                    @clear="handleSearch"
                                />
                            </el-form-item>
                            <el-form-item label="奖品名称">
                                <el-input
                                    v-model="searchForm.prizeName"
                                    placeholder="请输入奖品名称"
                                    clearable
                                    @clear="handleSearch"
                                />
                            </el-form-item>
                            <el-form-item label="奖品类型">
                                <el-select
                                    v-model="searchForm.prizeType"
                                    placeholder="请选择奖品类型"
                                    clearable
                                    @clear="handleSearch"
                                    style="width: 140px"
                                >
                                    <el-option
                                        v-for="type in prizeTypes"
                                        :key="type.value"
                                        :label="type.label"
                                        :value="type.value"
                                    >
                                        <el-tag
                                            :type="type.tagType"
                                            :style="type.tagType === 'custom' ? { backgroundColor: type.tagColor } : {}"
                                            size="small"
                                        >
                                            {{ type.label }}
                                        </el-tag>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" @click="handleSearch">搜索</el-button>
                            </el-form-item>
                        </el-form>
                    </div>
                </template>

                <el-table 
                    :data="lotteryRecords" 
                    v-loading="tableLoading"
                    :height="tableHeight"
                    @selection-change="handleSelectionChange">
                    <!-- 修改选择列的配置 -->
                    <el-table-column 
                        type="selection" 
                        width="55"
                        fixed="left"
                        align="center">
                    </el-table-column>
                    
                    <el-table-column label="获奖信息" min-width="300">
                        <template #default="{ row }">
                            <div class="record-row">
                                <div class="record-info">
                                    <div class="record-merchant">
                                        <el-tag size="small">商家ID: {{ row.merchant_id }}</el-tag>
                                        <span>{{ row.merchant_name }}</span>
                                    </div>
                                    <div class="record-prize">
                                        <span>{{ row.prize_name }}</span>
                                        <el-tag 
                                            size="small" 
                                            :type="row.prize_type_style.type"
                                            :style="row.prize_type_style.color ? { backgroundColor: row.prize_type_style.color } : {}"
                                        >
                                            {{ row.prize_type_text }}
                                        </el-tag>
                                        <el-tag size="small" :type="row.shipped ? 'success' : 'warning'">
                                            {{ row.shipped ? '已发货' : '未发货' }}
                                        </el-tag>
                                    </div>
                                    <span class="record-time">{{ row.create_time }}</span>
                                </div>
                                <div class="record-actions">
                                    <!-- 发货按钮 -->
                                    <el-button 
                                        v-if="!row.shipped"
                                        type="primary" 
                                        size="small" 
                                        @click="handleShipment(row)">
                                        发货
                                    </el-button>
                                    
                                    <!-- 发放余额按钮 - 只在未自动发放时显示 -->
                                    <el-button 
                                        v-if="!row.balance_sent && !row.auto_sent"
                                        type="success" 
                                        size="small" 
                                        @click="showBalanceDialog(row)">
                                        发放余额
                                    </el-button>
                                    
                                    <!-- 状态显示 -->
                                    <el-tag v-if="row.shipped" type="success" size="small">已发货</el-tag>
                                    <el-tag v-if="row.balance_sent" type="success" size="small">
                                        {{ row.auto_sent ? '已自动发放' : '已发放余额' }}
                                    </el-tag>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 添加分页组件 -->
                <el-pagination
                    v-if="total > 0"
                    class="pagination"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage"
                    :page-size="pageSize"
                    :total="total"
                    layout="total, prev, pager, next, jumper" />

                <el-empty v-if="lotteryRecords.length === 0" description="暂无抽奖记录" />
            </el-card>

            <!-- 流水规则管理面板 -->
            <el-card v-if="activeMenu === 'turnover-settings'" class="box-card">
                <template #header>
                    <div class="card-header">
                        <span>流水规则管理</span>
                        <el-button type="primary" @click="handleAddTurnoverRule">
                            <svg class="icon" viewBox="0 0 1024 1024" width="16" height="16">
                                <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm192 472H544v160c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V536H320c-4.4 0-8-3.6-8-8v-48c0-4.4 3.6-8 8-8h160V312c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v160h160c4.4 0 8 3.6 8 8v48c0 4.4-3.6 8-8 8z" fill="currentColor"/>
                            </svg>
                            添加规则
                        </el-button>
                    </div>
                </template>

                <!-- 修改流水规则表格的列宽和布局 -->
                <el-table :data="turnoverRules" :height="tableHeight" style="width: 100%" v-loading="tableLoading">
                    <el-table-column label="规则信息" min-width="200">
                        <template #default="{ row }">
                            <div style="display: flex; align-items: center;">
                                <span style="color: #909399; margin-right: 10px;">#{{ row.id }}</span>
                                <div>
                                    <div>流水金额: {{ row.turnover_amount }}元</div>
                                    <div>抽奖次数: {{ row.draw_times }}次</div>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="状态" width="100">
                        <template #default="scope">
                            <el-switch
                                v-model="scope.row.status"
                                :active-value="1"
                                :inactive-value="0"
                                @change="handleRuleStatusChange(scope.row)"
                            />
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="操作" width="150" fixed="right">
                        <template #default="{ row }">
                            <el-button-group>
                                <el-button type="primary" link @click="handleEditRule(row)">
                                    编辑
                                </el-button>
                                <el-button type="danger" link @click="handleDeleteRule(row)">
                                    删除
                                </el-button>
                            </el-button-group>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页组件 -->
                <div class="pagination-container">
                    <el-pagination
                        v-model:current-page="turnoverPagination.page"
                        :page-size="turnoverPagination.pageSize"
                        :total="turnoverPagination.total"
                        @current-change="handleTurnoverPageChange"
                        layout="total, prev, pager, next"
                        background
                    />
                </div>
            </el-card>

            <!-- 添加样式 -->
            <style>
            .pagination-container {
                margin-top: 16px;
                display: flex;
                justify-content: flex-end;
            }
            .rule-info {
                display: flex;
                gap: 16px;
            }
            </style>

            <!-- Hook设置面板 -->
            <el-card v-show="activeMenu === 'hook-settings'" class="main-card">
                <template #header>
                    <div class="card-header">
                        <span>Hook设置</span>
                    </div>
                </template>
                
                <div class="hook-settings">
                    <div class="hook-status">
                        <span>自动更新状态：</span>
                        <el-switch
                            v-model="hookConfig.auto_update_status"
                            :active-value="1"
                            :inactive-value="0"
                            @change="handleHookStatusChange"
                        />
                    </div>
                    
                    <div class="hook-interval">
                        <span>更新间隔：</span>
                        <el-input-number
                            v-model="hookConfig.update_interval"
                            :min="300"
                            :max="86400"
                            :step="300"
                            @change="handleIntervalChange"
                        />
                        <span class="unit">秒</span>
                        <span class="form-tip">(最小5分钟，最大24小时)</span>
                    </div>
                    
                    <div class="hook-info">
                        <p>最后更新时间：{{ formatLastUpdateTime }}</p>
                    </div>
                    
                    <div class="hook-actions">
                        <el-button
                            type="primary"
                            :loading="updating"
                            @click="handleManualUpdate"
                        >
                            手动更新
                        </el-button>
                    </div>
                </div>
            </el-card>
        </el-main>
    </el-container>

    <!-- 添加/编辑奖品对话框 -->
    <el-dialog
        v-model="dialogVisible"
        :title="dialogTitle"
        width="500px"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        class="prize-dialog">
        <el-form :model="prizeForm" label-width="100px" :rules="rules" ref="prizeFormRef">
            <el-form-item label="奖品名称" prop="name">
                <el-input v-model="prizeForm.name" placeholder="请输入奖品名称"></el-input>
            </el-form-item>
            <el-form-item label="奖品类型" prop="type">
                <el-select v-model="prizeForm.type" placeholder="请选择奖品类型" @change="handleTypeChange">
                    <el-option
                        v-for="type in prizeTypes"
                        :key="type.value"
                        :label="type.label"
                        :value="type.value">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="中奖概率" prop="probability">
                <el-input-number 
                    v-model="prizeForm.probability"
                    :min="0"
                    :max="100"
                    :precision="2"
                    :step="0.1"
                    style="width: 100%">
                </el-input-number>
            </el-form-item>
            <el-form-item label="库存数量" prop="stock">
                <el-input-number 
                    v-model="prizeForm.stock"
                    :min="0"
                    :precision="0"
                    :step="1"
                    style="width: 100%">
                </el-input-number>
            </el-form-item>
            <el-form-item label="余额金额">
                <el-input-number 
                    v-model="prizeForm.balance_amount"
                    :min="0"
                    :precision="2"
                    :step="0.1"
                    style="width: 100%">
                </el-input-number>
                <div class="form-tip">设置为0则不发放余额</div>
            </el-form-item>
            <el-form-item label="状态">
                <el-switch v-model="prizeForm.status"></el-switch>
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="savePrize">确定</el-button>
            </div>
        </template>
    </el-dialog>

    <!-- 添加抽奖设置对话框 -->
    <el-dialog
        title="抽奖设置"
        v-model="settingsVisible"
        width="500px">
        <el-form :model="config" label-width="120px">
            <el-form-item label="开始时间">
                <el-time-select
                    v-model="config.start_hour"
                    start="00:00"
                    step="00:30"
                    end="23:30"
                    placeholder="选择时间">
                </el-time-select>
            </el-form-item>
            <el-form-item label="结束时间">
                <el-time-select
                    v-model="config.end_hour"
                    start="00:00"
                    step="00:30"
                    end="23:30"
                    placeholder="选择时间"
                    :min-time="config.start_hour">
                </el-time-select>
            </el-form-item>
            <el-form-item label="每日抽奖次数">
                <el-input-number v-model="config.daily_limit" :min="0" :max="100"></el-input-number>
            </el-form-item>
            <el-form-item label="概率显示">
                <el-switch
                    v-model="config.show_probability"
                    :active-value="1"
                    :inactive-value="0"
                    active-text="显示"
                    inactive-text="隐藏">
                </el-switch>
                <div class="el-form-item-msg">开启后用户可以看到每个奖品的中奖概率</div>
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="settingsVisible = false">取消</el-button>
                <el-button type="primary" @click="saveLotteryConfig">确定</el-button>
            </div>
        </template>
    </el-dialog>

    <!-- 添加/修改奖品类型对话框 -->
    <el-dialog
        v-model="typeDialogVisible"
        :title="typeDialogTitle"
        width="500px"
        @close="handleDialogClose">
        <el-form
            ref="typeFormRef"
            :model="typeForm"
            :rules="typeRules"
            label-width="100px">
            <el-form-item label="类型名称" prop="label">
                <el-input v-model="typeForm.label" placeholder="请输入类型名称"></el-input>
            </el-form-item>
            <el-form-item label="类型标识" prop="value">
                <el-input 
                    v-model="typeForm.value" 
                    placeholder="请输入类型标识"
                    :disabled="typeForm.isEdit"
                    :readonly="typeForm.isEdit">
                </el-input>
                <div class="el-form-item-tip" style="color: #909399; font-size: 12px; margin-top: 4px;">
                    类型标识创建后不可修改
                </div>
            </el-form-item>
            <el-form-item label="标签样式">
                <el-radio-group v-model="typeForm.tagType">
                    <el-radio label="success">绿色</el-radio>
                    <el-radio label="warning">黄色</el-radio>
                    <el-radio label="danger">红色</el-radio>
                    <el-radio label="info">灰色</el-radio>
                    <el-radio label="custom">自定义</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item v-if="typeForm.tagType === 'custom'" label="标签颜色">
                <el-color-picker v-model="typeForm.tagColor"></el-color-picker>
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="typeDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="handleSaveType">确定</el-button>
            </span>
        </template>
    </el-dialog>

    <!-- 添加清空记录确认对话框 -->
    <el-dialog
        title="清空记录确认"
        v-model="clearDialogVisible"
        width="400px">
        <div class="dialog-content">
            <p>确定要清空所有抽奖记录吗？此操作不可恢复！</p>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="clearDialogVisible = false">取消</el-button>
                <el-button type="danger" @click="handleClearRecords">确定清空</el-button>
            </div>
        </template>
    </el-dialog>

    <!-- 修改重置次数对话框 -->
    <el-dialog
        v-model="resetDialogVisible"
        title="重置抽奖次数"
        width="400px">
        <el-form :model="resetForm" label-width="100px">
            <el-form-item label="商户ID">
                <el-input v-model="resetForm.merchantId" placeholder="请输入商户ID"></el-input>
            </el-form-item>
            <div class="form-tip">重置后将使用每日抽奖次数设置的值</div>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="resetDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="handleResetDraws">确定</el-button>
            </div>
        </template>
    </el-dialog>

    <!-- 修改发放余额对话框 -->
    <el-dialog
        v-model="balanceDialogVisible"
        title="发放余额"
        width="30%">
        <el-form :model="balanceForm" label-width="100px">
            <el-form-item label="发放金额">
                <el-input-number 
                    v-model="balanceForm.amount" 
                    :min="0.01"
                    :precision="2"
                    :step="1"
                    placeholder="请输入发放金额" />
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="balanceDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="handleSendBalance">确定</el-button>
            </span>
        </template>
    </el-dialog>

    <!-- 奖品类型管理对话框 -->
    <el-dialog
        v-model="typeManageVisible"
        title="奖品类型管理"
        width="800px">
        <el-table :data="prizeTypes" style="width: 100%">
            <el-table-column prop="label" label="类型名称" width="180"></el-table-column>
            <el-table-column prop="value" label="类型标识" width="180"></el-table-column>
            <el-table-column label="标签样式">
                <template #default="{ row }">
                    <el-tag 
                        :type="row.tagType"
                        :style="row.tagType === 'custom' ? { backgroundColor: row.tagColor } : {}"
                    >
                        {{ row.label }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
                <template #default="{ row }">
                    <el-button 
                        type="primary" 
                        size="small" 
                        @click="handleEditType(row)"
                        :disabled="row.is_system"
                    >
                        编辑
                    </el-button>
                    <el-button 
                        type="danger" 
                        size="small" 
                        @click="handleDeleteType(row)"
                        :disabled="row.is_system"
                    >
                        删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
    </el-dialog>

    <style>
    /* 添加或修改样式 */
    .type-manage-header {
        margin-bottom: 16px;
    }

    :deep(.el-table) {
        margin-bottom: 16px;
    }

    :deep(.el-table .el-button-group) {
        display: flex;
        gap: 8px;
    }

    :deep(.el-table .el-button--link) {
        height: 24px;
        line-height: 24px;
    }

    :deep(.el-tag) {
        margin: 0;
    }

    /* 确保表格内容垂直居中 */
    :deep(.el-table td) {
        padding: 8px 12px;
    }

    /* 调整表格行高 */
    :deep(.el-table .el-table__row) {
        height: 50px;
    }

    /* 调整表头样式 */
    :deep(.el-table th) {
        background-color: #f5f7fa;
        color: #606266;
        font-weight: 500;
        padding: 8px 12px;
    }

    /* 确保内容不会被截断 */
    :deep(.el-table .cell) {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* 按钮组样式 */
    .header-btns {
        display: flex;
        gap: 12px;
        margin-bottom: 16px;
    }

    /* 图标样式 */
    .icon {
        margin-right: 4px;
        vertical-align: -0.15em;
    }
    </style>

    <!-- 添加相关样式 -->
    <style>
    .search-form {
        margin-bottom: 20px;
        padding: 15px;
        background-color: #fff;
        border-radius: 4px;
    }
    </style>

    <!-- 添加隐藏奖品设置对话框 -->
    <el-dialog
        v-model="hiddenPrizeDialogVisible"
        title="隐藏奖品设置"
        width="500px"
        @open="loadHiddenPrizes">
        <div class="hidden-prize-settings">
            <el-checkbox-group v-model="hiddenPrizes">
                <el-checkbox
                    v-for="prize in tableData"
                    :key="prize.id"
                    :label="prize.id">
                    {{ prize.name }}
                </el-checkbox>
            </el-checkbox-group>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="hiddenPrizeDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="saveHiddenPrizes">确定</el-button>
            </div>
        </template>
    </el-dialog>

    <!-- 生成虚拟中奖记录对话框 -->
    <el-dialog
        v-model="generateDialogVisible"
        title="生成虚拟中奖记录"
        width="500px"
        @close="handleGenerateDialogClose">
        <el-form :model="generateForm" label-width="120px">
            <el-form-item label="生成数量">
                <el-input-number v-model="generateForm.count" :min="1" :max="1000" />
            </el-form-item>
            <el-form-item label="指定奖品">
                <el-select v-model="generateForm.prize_id" clearable placeholder="不指定则随机">
                    <el-option
                        v-for="prize in tableData"
                        :key="prize.id"
                        :label="prize.name"
                        :value="prize.id"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="时间范围">
                <el-date-picker
                    v-model="generateForm.timeRange"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    value-format="YYYY-MM-DD HH:mm:ss"
                />
            </el-form-item>
            <el-form-item label="商家ID列表">
                <el-input 
                    v-model="generateForm.merchant_prefix" 
                    placeholder="多个ID用英文逗号分隔，生成数量需等于ID数量" />
                <div class="el-form-item-tip">例如：VIP1,VIP2,VIP3 (将按顺序生成3条记录)</div>
            </el-form-item>
            <el-form-item label="商家ID范围">
                <el-row :gutter="10">
                    <el-col :span="11">
                        <el-input v-model="generateForm.merchant_id_start" placeholder="起始值" />
                    </el-col>
                    <el-col :span="2" class="text-center">
                        <span>-</span>
                    </el-col>
                    <el-col :span="11">
                        <el-input v-model="generateForm.merchant_id_end" placeholder="结束值" />
                    </el-col>
                </el-row>
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="generateDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="handleGenerate">确定</el-button>
            </span>
        </template>
    </el-dialog>

    <!-- 添加流水规则对话框 -->
    <el-dialog
        v-model="turnoverDialogVisible"
        :title="turnoverDialogTitle"
        width="500px"
        :close-on-click-modal="false"
        @close="handleDialogClose">
        <el-form :model="turnoverForm" label-width="120px" ref="turnoverFormRef">
            <el-form-item label="流水金额" prop="turnover_amount">
                <el-input-number 
                    v-model="turnoverForm.turnover_amount"
                    :min="0"
                    :precision="2"
                    :step="100"
                    style="width: 200px">
                </el-input-number>
                <span class="unit">元</span>
            </el-form-item>
            <el-form-item label="抽奖次数" prop="draw_times">
                <el-input-number
                    v-model="turnoverForm.draw_times"
                    :min="1"
                    :max="100"
                    :step="1"
                    style="width: 200px">
                </el-input-number>
                <span class="unit">次</span>
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="turnoverDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="saveTurnoverRule">确定</el-button>
            </span>
        </template>
    </el-dialog>
</div>

<!-- 引入相关JS -->
<script src="/static/others/vue/vue.global.prod.js"></script>
<script src="/static/others/element-plus/index.full.min.js"></script>
<script src="/static/others/axios/axios.min.js"></script>
<script src="/static/others/element-plus/locale/zh-cn.min.js"></script>

<style>
/* 基础样式 */
body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: #f5f7fa;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
}

/* 修改主容器样式 */
.el-container {
    height: 800px;
    min-height: 800px;
}

/* 修改主内容区样式 */
.el-main {
    padding: 20px;
    background: #f5f7fa;
    height: calc(100vh - 40px); /* 减去padding的高度 */
    overflow-y: auto;
}

/* 修改卡片样式 */
.main-card {
    height: calc(100vh - 80px); /* 减去padding和其他间距 */
    display: flex;
    flex-direction: column;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

/* 修改表格容器样式 */
.el-table {
    flex: 1;
    overflow-y: auto;
}

/* 确保分页在底部 */
.pagination {
    margin-top: 20px;
    padding: 10px 0;
}

/* 修改表格最大高度 */
.el-table__body-wrapper {
    max-height: calc(100vh - 200px) !important; /* 调整为合适的高度 */
}

.el-aside {
    background: #304156;
    height: 100vh;
}

.sidebar-menu {
    height: 100%;
    border-right: none;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-btns {
    display: flex;
    gap: 8px;
    align-items: center;
}

.header-btns .el-button-group {
    display: flex;
    gap: 0; /* 按钮组内部不需要间隔 */
}

.header-btns .icon {
    margin-right: 4px;
    vertical-align: middle;
}

/* 确保按钮组中的按钮紧贴在一起 */
.el-button-group > .el-button:not(:last-child) {
    margin-right: -1px;
}

/* 奖品行样式 */
.prize-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: #fafafa;
    border-radius: 6px;
    margin-bottom: 8px;
}

.prize-info {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
}

.prize-name {
    font-weight: 500;
}

.prize-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* 记录行样式 */
.record-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: #fafafa;
    border-radius: 6px;
    margin-bottom: 8px;
}

.record-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.record-merchant {
    display: flex;
    align-items: center;
    gap: 10px;
}

.record-prize {
    display: flex;
    align-items: center;
    gap: 10px;
}

.record-time {
    color: #909399;
    font-size: 13px;
}

/* 加载动画 */
#loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 对话框按钮样式 */
.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 20px;
    margin-top: 20px;
    border-top: 1px solid #dcdfe6;
}

/* 类型按钮行样式 */
.type-buttons-row {
    display: flex;
    gap: 12px;
    margin: 20px 0;
    padding: 0 0 0 100px; /* 对齐表单项 */
}

/* 图标全局样式 */
.icon {
    width: 16px;
    height: 16px;
    margin-right: 4px;
    vertical-align: -0.15em;
}

/* 对话框内容样式优化 */
.el-dialog__body {
    padding: 20px 30px;
}

.el-form-item:last-child {
    margin-bottom: 0;
}

/* 自定义颜色选择器容器样式 */
.color-picker-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* 对话框中的分隔线 */
.dialog-divider {
    margin: 20px 0;
    border-top: 1px solid #ebeef5;
}

/* 对话框中的警告按钮对齐 */
.dialog-warning-button {
    margin-left: auto;
}

/* 补充对话框响应式样式 */
@media screen and (max-width: 768px) {
    .el-dialog {
        width: 90% !important;
        margin: 20px auto !important;
    }
    
    .type-buttons-row {
        padding-left: 0;
        justify-content: flex-end;
    }
    
    .dialog-footer {
        flex-direction: column-reverse;
    }
    
    .dialog-footer .el-button {
        width: 100%;
    }
}

.unit {
    margin-left: 8px;
    color: #606266;
}

/* 确保按钮内容垂直居中 */
.el-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    padding: 8px 16px;
}

.el-button--small {
    padding: 6px 12px;
}

/* 添加样式 */
.form-tip {
    margin-left: 8px;
    color: #909399;
}

.radio-group-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.el-radio {
    margin-right: 0;
    margin-bottom: 10px;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* 添加新样式 */
.type-buttons {
    margin-top: 8px;
    display: flex;
    gap: 12px;
}

.type-buttons .el-button {
    padding: 4px 8px;
}

.type-buttons .icon {
    margin-right: 4px;
    vertical-align: -0.15em;
}

/* 确保表单项之间有足够间距 */
.el-form-item {
    margin-bottom: 22px;
}

.el-button.is-disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 添加图标样式 */
.el-dropdown-menu__item .el-icon {
    margin-right: 5px;
    vertical-align: middle;
}

.el-icon--right {
    margin-left: 5px;
}

/* 调整下拉菜单样式 */
.el-dropdown-menu {
    padding: 5px 0;
}

.el-dropdown-menu__item {
    padding: 8px 20px;
    font-size: 14px;
    line-height: 1.5;
}

.el-dropdown-menu__item--divided {
    margin-top: 6px;
    border-top: 1px solid var(--el-border-color-lighter);
}

.form-select {
    width: 200px;
    height: 32px;
    padding: 0 10px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    outline: none;
}
.form-select:focus {
    border-color: #409eff;
}

.settings-section {
    margin-bottom: 20px;
    padding: 20px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
}
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}
.section-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
}
.rule-info {
    display: flex;
    align-items: center;
    gap: 15px;
}
.unit {
    margin-left: 8px;
    color: #909399;
}

.reset-option {
    padding: 8px 0;
    display: flex;
    align-items: center;
}
.reset-option span {
    margin-left: 8px;
}
.el-select-dropdown__item.selected {
    color: #409EFF;
    font-weight: bold;
}

/* 添加Hook配置相关样式 */
.hook-settings {
    padding: 20px;
}

.hook-status, .hook-interval, .hook-info {
    margin-bottom: 20px;
}

.hook-interval .unit {
    margin-left: 8px;
    color: #606266;
}

.hook-info {
    color: #606266;
}

.hook-actions {
    margin-top: 20px;
}

/* 添加到现有样式中 */
.el-popper {
    outline: none;
}
.el-dropdown__popper {
    inset: auto !important;
}

/* 添加记录样式 */
.records-container {
    padding: 15px;
}

.records-list {
    max-height: 400px;
    overflow-y: auto;
}

.record-item {
    padding: 12px;
    border-bottom: 1px solid #ebeef5;
    transition: background-color 0.3s;
}

.record-item:hover {
    background-color: #f5f7fa;
}

.record-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.prize-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.prize-name {
    font-weight: bold;
    color: #303133;
}

.record-detail {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #909399;
    font-size: 13px;
}

.user-name {
    color: #606266;
}

.record-time {
    color: #909399;
}

.pagination {
    margin-top: 15px;
    text-align: center;
}

.no-records {
    text-align: center;
    color: #909399;
    padding: 30px;
}

.dialog-content {
    text-align: center;
    margin: 20px 0;
}

.dialog-content p {
    color: #f56c6c;
    font-size: 16px;
}

/* 添加奖品图片相关样式 */
.prize-image {
    width: 40px;
    height: 40px;
    border-radius: 4px;
    object-fit: cover;
    margin-right: 8px;
}

.image-error {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f7fa;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    color: #909399;
}

.record-prize {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* 调整记录行布局 */
.record-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
}

.record-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.record-merchant {
    display: flex;
    align-items: center;
    gap: 10px;
}

.record-time {
    color: #909399;
    font-size: 13px;
}

.record-actions {
    display: flex;
    gap: 8px;
}

.form-tip {
    margin-top: 4px;
    font-size: 12px;
    color: #909399;
}
.unit {
    margin-left: 8px;
    color: #909399;
}

.form-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
}

.type-manage-header {
    margin-bottom: 16px;
}

:deep(.el-table) {
    margin-bottom: 16px;
}

:deep(.el-table .el-button-group) {
    display: flex;
    gap: 8px;
}

:deep(.el-table .el-button--link) {
    padding: 0 8px;
    height: 24px;
    line-height: 24px;
}

:deep(.el-tag) {
    margin: 0;
    border-radius: 4px;
    padding: 0 8px;
    height: 24px;
    line-height: 22px;
}

/* 确保表格内容垂直居中 */
:deep(.el-table td) {
    padding: 8px 0;
}

/* 调整操作列的按钮间距 */
:deep(.el-button + .el-button) {
    margin-left: 8px;
}

/* 调整表格选择框样式 */
:deep(.el-table-column--selection .cell) {
    padding-left: 14px;
}

.el-form-item-tips {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.2;
}

.el-date-editor.el-input__wrapper {
    width: 100% !important;
}

/* 添加响应式布局样式 */
@media screen and (max-width: 768px) {
    /* 侧边栏样式 */
    .el-aside {
        width: 60px !important;
    }

    .el-menu {
        width: 60px !important;
    }

    /* 菜单项样式 */
    .el-menu-item span {
        display: none;
    }

    .el-menu-item .icon {
        margin-right: 0;
    }

    /* 卡片头部样式 */
    .card-header {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .header-btns {
        flex-wrap: wrap;
        gap: 8px;
    }

    .header-btns .el-button {
        flex: 1;
        min-width: calc(50% - 4px);
        padding: 8px;
        justify-content: center;
    }

    /* 表格样式调整 */
    .prize-row,
    .record-row {
        flex-direction: column;
        gap: 8px;
    }

    .prize-info,
    .record-info {
        flex-wrap: wrap;
        gap: 6px;
    }

    .prize-actions,
    .record-actions {
        width: 100%;
        justify-content: flex-end;
    }

    /* 对话框样式 */
    .el-dialog {
        width: 90% !important;
        margin: 10vh auto !important;
    }

    .el-dialog__body {
        padding: 15px !important;
    }

    /* 表单样式 */
    .el-form-item {
        margin-bottom: 15px;
    }

    .el-form-item__label {
        float: none;
        display: block;
        text-align: left;
        padding: 0 0 8px;
    }

    .el-form-item__content {
        margin-left: 0 !important;
    }

    /* 分页样式 */
    .el-pagination {
        justify-content: center;
        flex-wrap: wrap;
        gap: 8px;
    }
}

/* 通用间距优化 */
.mb-2 {
    margin-bottom: 8px;
}

.mt-2 {
    margin-top: 8px;
}

.mx-2 {
    margin-left: 8px;
    margin-right: 8px;
}

/* 文字样式优化 */
.text-secondary {
    color: #909399;
    font-size: 13px;
}

/* 状态标签颜色优化 */
.status-tag {
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
}

.status-tag--success {
    background: #f0f9eb;
    color: #67c23a;
}

.status-tag--warning {
    background: #fdf6ec;
    color: #e6a23c;
}

/* 移动端日期时间选择器优化 */
@media screen and (max-width: 768px) {
    /* 调整日期时间范围选择器宽度 */
    .el-date-editor.el-input, 
    .el-date-editor.el-input__wrapper,
    .el-date-editor--datetimerange {
        width: 100% !important;
        max-width: 100% !important;
    }

    /* 调整日期面板位置和大小 */
    .el-picker__popper {
        width: 90vw !important;
        max-width: 400px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
    }

    /* 调整日期范围选择器内部布局 */
    .el-date-range-picker {
        width: 100% !important;
    }

    .el-date-range-picker .el-picker-panel__body {
        min-width: unset !important;
    }

    /* 调整日期面板中的日历表格大小 */
    .el-date-range-picker__content {
        float: none !important;
        width: 100% !important;
        margin: 0 !important;
    }

    /* 确保时间选择器完全可见 */
    .el-date-range-picker__time-header {
        width: 100% !important;
        padding: 8px !important;
    }

    .el-date-range-picker__time-content {
        width: 100% !important;
    }
}

/* 移动端日期时间选择器滚动优化 */
@media screen and (max-width: 768px) {
    /* 调整弹出层容器 */
    .el-picker__popper {
        max-height: 65vh !important; /* 减小最大高度 */
        overflow-y: auto !important;
        -webkit-overflow-scrolling: touch !important;
    }

    /* 优化日期表格尺寸 */
    .el-date-table {
        height: auto !important;
        font-size: 13px !important; /* 稍微减小字体 */
    }

    /* 调整日期单元格大小 */
    .el-date-table td {
        padding: 8px 0 !important; /* 减小单元格高度 */
    }

    /* 调整月份显示区域 */
    .el-date-range-picker__content {
        padding: 8px !important;
    }

    /* 优化月份标题 */
    .el-date-range-picker__header {
        padding: 8px 0 !important;
        margin: 0 !important;
    }

    /* 调整时间选择器头部 */
    .el-date-range-picker__time-header {
        padding: 6px 8px !important;
        height: auto !important;
    }

    /* 优化日期范围选择器整体布局 */
    .el-date-range-picker {
        width: 100% !important;
        min-width: unset !important;
    }
}
</style>

    <script>
const { ref, reactive, computed, onMounted, onUnmounted, watch } = Vue;
const { ElMessage, ElMessageBox, ElLoading } = ElementPlus;

const app = Vue.createApp({
    setup() {
        const tableData = ref([]);
        const dialogVisible = ref(false);
        const dialogTitle = ref('添加奖品');
        
        const pagination = reactive({
            total: 0,
            page: 1,
            pageSize: 20
        });

        const config = reactive({
            status: 1,
            daily_limit: 3,
            start_hour: '00:00',
            end_hour: '23:59',
            show_probability: 0
        });

        const prizeForm = ref({
            id: 0,
            name: '',
            type: '',
            probability: null,
            stock: null,
            status: 1,
            balance_amount: 0  // 默认为0
        });

        // 新增抽奖记录相关
        const lotteryRecords = ref([]);
        const activeMenu = ref('prize-settings');
        const tableLoading = ref(false);
        const settingsVisible = ref(false);

        // 添加奖品类型相关数据
        const typeDialogVisible = ref(false);
        const typeDialogTitle = ref('添加类型');
        const typeForm = ref({
            label: '',
            value: '',
            tagType: 'success',
            tagColor: '',
            is_system: 0
        });
        const prizeTypes = ref([]);
        const prizeTemplates = ref([]);

        // 添加重置相关的响应式数据
        const resetDialogVisible = ref(false);

        // 添加记录分页数据
        const recordsPagination = ref({
            currentPage: 1,
            pageSize: 20,
            total: 0
        });

        // 添加响应式窗口尺寸
        const windowSize = reactive({
            width: window.innerWidth,
            height: window.innerHeight
        });

        // 计算表格高度
        const tableHeight = computed(() => {
            // 根据窗口高度动态计算表格高度
            const baseHeight = windowSize.height;
            // 减去其他元素的高度(头部、分页等)
            return baseHeight - 280; // 可以根据实际情况调整减去的值
        });

        // 监听窗口大小变化
        const handleResize = () => {
            windowSize.width = window.innerWidth;
            windowSize.height = window.innerHeight;
        };

        // 添加清空记录相关的响应式数据
        const clearDialogVisible = ref(false);
        const clearForm = ref({
            merchantId: ''
        });

        // 流水规则相关数据
        const turnoverRules = ref([]);
        const turnoverDialogVisible = ref(false);
        const turnoverDialogTitle = ref('添加流水规则');
        const turnoverForm = ref({
            id: 0,
            turnover_amount: 0,
            draw_times: 1
        });
        const turnoverFormRef = ref(null);

        // Hook配置
        const hookConfig = reactive({
            auto_update_status: 0,
            update_interval: 3600,
            last_update: 0
        });
        
        const updating = ref(false);

        // 格式化最后更新时间
        const formatLastUpdateTime = computed(() => {
            if (!hookConfig.last_update) return '暂无更新';
            return new Date(hookConfig.last_update * 1000).toLocaleString();
        });

        const turnoverConfig = ref({
            reset_type: 'daily',
            last_reset: 0
        });

        // 格式化重置时间
        const formatDate = (timestamp) => {
            return new Date(timestamp * 1000).toLocaleString();
        };

        // 处理重置类型变更
        const handleResetTypeChange = async (val) => {
            try {
                const res = await axios.post("/plugin/Lottery/api/saveTurnoverConfig", {
                    reset_type: val
                });
                
                if (res.data?.code === 200) {
                    ElMessage.success('设置成功');
                } else {
                    ElMessage.error(res.data?.msg || '设置失败');
                }
            } catch (error) {
                ElMessage.error('设置失败');
            }
        };

        // 手动重置
        const handleManualReset = async () => {
            try {
                const res = await axios.post("/plugin/Lottery/api/resetTurnover");
                
                if (res.data?.code === 200) {
                    ElMessage.success('重置成功');
                    // 刷新配置
                    await fetchTurnoverConfig();
                } else {
                    ElMessage.error(res.data?.msg || '重置失败');
                }
            } catch (error) {
                console.error('重置失败:', error);
                ElMessage.error('重置失败：' + (error.response?.data?.msg || error.message));
            }
        };

        // 获取流水配置
        const fetchTurnoverConfig = async () => {
            try {
                const res = await axios.post("/plugin/Lottery/api/getTurnoverConfig");
                if (res.data?.code === 200) {
                    Object.assign(turnoverConfig.value, res.data.data);
                }
            } catch (error) {
                console.error('获取流水配置失败:', error);
                ElMessage.error('获取流水配置失败');
            }
        };

        // 监听窗口大小变化
        onMounted(() => {
            fetchData();
            getLotteryConfig();
            fetchLotteryRecords();
            fetchPrizeTypes(); // 添加获取奖品类型
            // 隐藏加载动画，显示应用
            document.getElementById("loading").style.display = "none";
            document.getElementById("app").style.display = "block";
            
            // 添加防抖的窗口大小变化处理
            let resizeTimeout;
            window.addEventListener('resize', () => {
                if (resizeTimeout) clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(() => {
                    tableHeight.value = 'calc(100vh - 200px)';
                }, 100);
            });

            window.addEventListener('resize', handleResize);
            handleResize(); // 初始化尺寸

            if (activeMenu.value === 'turnover-settings') {
                fetchTurnoverRules();
            }

            // 在 onMounted 中加载Hook配置
            fetchHookConfig();
            fetchTurnoverConfig();
            fetchAutoSendConfig();
        });

        onUnmounted(() => {
            window.removeEventListener('resize', handleResize);
        });

        // 获取奖品列表
        const fetchData = async (page = 1) => {
            try {
            const params = {
                    page
                };
                
                const res = await axios.post("/plugin/Lottery/api/getPrizes", params);
                if (res.data?.code === 200) {
                    tableData.value = res.data.data.list;
                    Object.assign(pagination, {
                        total: res.data.data.total,
                        page: page,
                        pageSize: 20
                    });
                }
            } catch (error) {
                ElMessage.error('获取数据失败');
            }
        };

        // 获取抽奖配置
        const getLotteryConfig = async () => {
            try {
                const res = await axios.post("/plugin/Lottery/api/getLotteryConfig");
                if (res.data?.code === 200) {
                    Object.assign(config, res.data.data.config);
                    prizeTypes.value = res.data.data.prizeTypes || [];
                } else {
                    ElMessage.error(res.data?.msg || '获取配置失败');
                    console.error('获取配置失败:', res.data);
                }
            } catch (error) {
                console.error('获取配置失败:', error.response?.data || error);
                ElMessage.error(error.response?.data?.msg || '获取配置失败，请检查网络连接');
            }
        };

        // 保存抽奖配置
        const saveLotteryConfig = async () => {
            try {
                const configData = {
                    ...config,
                    prize_types: prizeTypes.value,
                    prize_templates: prizeTemplates.value
                };

                const res = await axios.post("/plugin/Lottery/api/saveLotteryConfig", configData);
                if (res.data?.code === 200) {
                    ElMessage.success('保存成功');
                    settingsVisible.value = false;
                } else {
                    ElMessage.error(res.data?.msg || '保存失败');
                }
            } catch (error) {
                ElMessage.error('保存失败：' + error.message);
            }
        };

        // 修改状态切换处理
        const handleStatusChange = async (row, status) => {
            try {
                const res = await axios.post("/plugin/Lottery/api/updatePrizeStatus", {
                    id: row.id,
                    status: status
                });
                
                if (res.data?.code === 200) {
                    ElMessage.success('状态更新成功');
                } else {
                    ElMessage.error(res.data?.msg || '更新失败');
                    row.status = !status; // 恢复状态
                }
            } catch (error) {
                ElMessage.error('更新失败');
                row.status = !status; // 恢复状态
            }
        };

        // 修改编辑处理
        const handleEdit = (prize) => {
            prizeForm.value = {
                id: prize.id,
                name: prize.name,
                type: prize.type,
                probability: prize.probability,
                stock: prize.stock,
                status: prize.status === 1, // 修改这里，确保转换为布尔值
                balance_amount: prize.balance_amount || 0
            };
            dialogVisible.value = true;
            dialogTitle.value = '编辑奖品';
        };

        // 修改删除处理
        const handleDelete = async (row) => {
            try {
                await ElMessageBox.confirm('确定要删除该奖品吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                });
                
                const res = await axios.post("/plugin/Lottery/api/deletePrize", {
                    id: row.id
                });
                
                if (res.data?.code === 200) {
                    ElMessage.success('删除成功');
                    // 重新获取奖品列表
                    await fetchData();
                } else {
                    ElMessage.error(res.data?.msg || '删除失败');
                }
            } catch (error) {
                if (error !== 'cancel') {
                    console.error('删除奖品失败:', error);
                    ElMessage.error(error.response?.data?.msg || '删除失败');
                }
            }
        };

        // 修改保存奖品处理
        const savePrize = async () => {
            try {
                if (!prizeFormRef.value) {
                    return;
                }
                await prizeFormRef.value.validate();
                
                // 在发送请求前转换状态值
                const formData = {
                    ...prizeForm.value,
                    status: prizeForm.value.status ? 1 : 0 // 确保转换为 0/1
                };
                
                // 发送请求保存奖品
                const res = await axios.post("/plugin/Lottery/api/savePrize", formData);
                
                if (res.data?.code === 200) {
                    ElMessage.success('保存成功');
                    dialogVisible.value = false;
                    // 重新获取奖品列表
                    await fetchData();
                } else {
                    ElMessage.error(res.data?.msg || '保存失败');
                }
            } catch (error) {
                if (error?.message) {
                    ElMessage.error(error.message);
                } else {
                    console.error('保存奖品失败:', error);
                    ElMessage.error('保存失败');
                }
            }
        };

        const handleAddPrize = () => {
            dialogTitle.value = '添加奖品';
            prizeForm.value = {
                id: 0,
                name: '',
                type: '',
                probability: 0,
                stock: 0,
                status: 1,
                balance_amount: 0
            };
            dialogVisible.value = true;
        };

        const handlePageChange = (page) => {
            fetchData(page);
        };

        const getPrizeTypeTag = (type) => {
            if (!type || !prizeTypes.value) return '';
            const prizeType = prizeTypes.value.find(t => t.value === type);
            return prizeType?.tagType === 'custom' ? '' : (prizeType?.tagType || '');
        };

        const getPrizeTypeText = (type) => {
            if (!type || !prizeTypes.value) return '';
            const prizeType = prizeTypes.value.find(t => t.value === type);
            return prizeType?.label || type;
        };

        // 修改获取抽奖记录方法
        const fetchLotteryRecords = async () => {
            try {
                const params = {
                    page: currentPage.value,
                    pageSize: pageSize.value,
                    merchantId: searchForm.value.merchantId,
                    prizeName: searchForm.value.prizeName,
                    prizeType: searchForm.value.prizeType
                };
                
                const res = await axios.post('/plugin/Lottery/api/getLotteryRecords', params);
                
                if (res.data?.code === 200 && res.data?.data) {
                    lotteryRecords.value = res.data.data.records || [];
                    total.value = res.data.data.pagination?.total || 0;
                    prizeTypes.value = res.data.data.prizeTypes || []; // 保存奖品类型列表
                } else {
                    ElMessage.error(res.data?.msg || '获取记录失败');
                }
            } catch (error) {
                console.error('获取记录失败:', error);
                ElMessage.error('获取记录失败：' + (error.response?.data?.msg || error.message));
            }
        };

        // 添加分页相关的响应式数据
        const currentPage = ref(1);
        const pageSize = ref(20);
        const total = ref(0);
        const records = ref([]);

        // 分页改变时重新获取数据
        const handleCurrentChange = (page) => {
            currentPage.value = page;
            fetchLotteryRecords();
        };

        // 修改补货处理方法
        const handleRestock = async (record) => {
            try {
                const res = await axios.post("/plugin/Lottery/api/handleShipment", {
                    record_id: record.id
                });
                
                if (res.data?.code === 200) {
                    ElMessage.success('发货成功111');
                    // 刷新记录列表
                    fetchLotteryRecords();
                } else {
                    ElMessage.error(res.data?.msg || '发货失败');
                }
            } catch (error) {
                console.error('发货记录:', error);
                ElMessage.error('发货失败: ' + error.message);
            }
        };

        // 修改记录分页切换方法
        const handleRecordPageChange = (page) => {
            recordsPagination.value.currentPage = page;
            fetchLotteryRecords();
        };

        // 根据奖品ID获取奖品名称
        const getPrizeName = (prizeId) => {
            const prize = tableData.value.find(p => p.id === prizeId);
            return prize ? prize.name : '未知奖品';
        };

        // 根据奖品ID获取奖品类型
        const getPrizeType = (prizeId) => {
            const prize = tableData.value.find(p => p.id === prizeId);
            return prize ? prize.type : '';
        };

        // 处理菜单选择
        const handleMenuSelect = (index) => {
            activeMenu.value = index;
            if (index === 'lottery-records') {
                fetchLotteryRecords();
            }
        };

        // 显示抽奖设置对话框
        const showLotterySettings = () => {
            settingsVisible.value = true;
        };

        // 获取奖品类型列表
        const fetchPrizeTypes = async () => {
            try {
                const res = await axios.get('/plugin/Lottery/api/getLotteryConfig');
                if (res.data.code === 200) {
                    prizeTypes.value = res.data.data.prizeTypes;
                } else {
                    ElMessage.error(res.data.msg || '获取奖品类型失败');
                }
            } catch (error) {
                console.error('获取奖品类型失败:', error);
                ElMessage.error('获取奖品类型失败');
            }
        };

        // 显示类型对话框
        const showTypeDialog = (mode, row) => {
            typeDialogTitle.value = mode;
            
            if (mode === 'edit') {
                if (!row || !row.type) {
                    ElMessage.warning('请先选择要修改的奖品类型');
                    return;
                }
                
                const currentType = prizeTypes.value.find(t => t.value === row.type);
                if (!currentType) {
                    ElMessage.warning('未找到当前奖品类型');
                    return;
                }
                
                Object.assign(typeForm.value, {
                    value: currentType.value,
                    label: currentType.label,
                    tagType: currentType.tagType || '',
                    tagColor: currentType.tagColor || '',
                    is_system: currentType.is_system || 0
                });
            } else {
                // 添加模式时重置表单
                Object.assign(typeForm.value, {
                    value: '',
                    label: '',
                    tagType: '',
                    tagColor: '',
                    is_system: 0
                });
            }
            
            typeDialogVisible.value = true;
        };

        // 保存类型
        const handleSaveType = async () => {
            if (!typeFormRef.value) return;
            
            try {
                await typeFormRef.value.validate();
                
                const loading = ElLoading.service({
                    lock: true,
                    text: '保存中...'
                });
                
                try {
                    const res = await axios.post('/plugin/Lottery/api/savePrizeType', typeForm.value);
                    if (res.data.code === 200) {
                        ElMessage.success(res.data.msg);
                        typeDialogVisible.value = false;
                        // 重新加载类型列表
                        await getLotteryConfig();
                    } else {
                        ElMessage.error(res.data.msg);
                    }
                } finally {
                    loading.close();
                }
            } catch (error) {
                console.error('表单验证失败:', error);
            }
        };

        // 显示重置对话框
        const showResetDialog = () => {
            resetForm.value = {
                merchantId: '',
                resetCount: 0
            };
            resetDialogVisible.value = true;
        };

        // 处理重置抽奖次数
        const handleResetDraws = async () => {
            try {
                if (!resetForm.value.merchantId) {
                    ElMessage.warning('请输入商户ID');
                    return;
                }

                const res = await axios.post("/plugin/Lottery/api/resetDraws", {
                    merchantId: resetForm.value.merchantId
                });

                if (res.data?.code === 200) {
                    ElMessage.success('重置成功');
                    resetDialogVisible.value = false;
                } else {
                    ElMessage.error(res.data?.msg || '重置失败');
                }
            } catch (error) {
                ElMessage.error('重置失败：' + error.message);
            }
        };

        // 显示清空对话框
        const showClearDialog = () => {
            clearDialogVisible.value = true;
        };

        // 处理清空记录
        const handleClearRecords = async () => {
            try {
                const res = await axios.post("/plugin/Lottery/api/clearRecords");
                
                if (res.data?.code === 200) {
                    ElMessage.success('清空成功');
                    clearDialogVisible.value = false;
                    // 刷新记录列表
                    await fetchLotteryRecords(1);
                } else {
                    ElMessage.error(res.data?.msg || '清空失败');
                }
            } catch (error) {
                console.error('清空记录失败:', error);
                ElMessage.error('清空失败：' + (error.response?.data?.msg || error.message));
            }
        };

        // 修改获取标签样式的方法
        const getPrizeTypeStyle = (type) => {
            if (!type || !prizeTypes.value) return {};
            const prizeType = prizeTypes.value.find(t => t.value === type);
            if (prizeType?.tagType === 'custom' && prizeType?.tagColor) {
                return {
                    backgroundColor: prizeType.tagColor,
                    borderColor: prizeType.tagColor,
                    color: '#ffffff'
                };
            }
            return {};
        };

        // 在 setup() 中添加分页相关的响应式数据
        const turnoverPagination = reactive({
            page: 1,
            pageSize: 12, // 设置每页显示12条
            total: 0
        });

        // 修改获取流水规则的方法
        const fetchTurnoverRules = async () => {
            try {
                const res = await axios.post("/plugin/Lottery/api/getTurnoverRules", {
                    page: turnoverPagination.page,
                    pageSize: turnoverPagination.pageSize
                });
                
                if (res.data?.code === 200) {
                    turnoverRules.value = res.data.data.list;
                    turnoverPagination.total = res.data.data.total;
                } else {
                    ElMessage.error(res.data?.msg || '获取规则失败');
                }
            } catch (error) {
                console.error('获取流水规则失败:', error);
                ElMessage.error('获取规则失败');
            }
        };

        // 添加分页切换处理方法
        const handleTurnoverPageChange = (page) => {
            turnoverPagination.page = page;
            fetchTurnoverRules();
        };

        // 添加规则
        const handleAddTurnoverRule = () => {
            turnoverForm.value = {
                id: 0,
                turnover_amount: 0,
                draw_times: 1
            };
            turnoverDialogTitle.value = '添加流水规则';
            turnoverDialogVisible.value = true;
        };

        // 编辑规则
        const handleEditRule = (rule) => {
            turnoverForm.value = {
                id: rule.id,
                turnover_amount: rule.turnover_amount,
                draw_times: rule.draw_times
            };
            turnoverDialogTitle.value = '编辑流水规则';
            turnoverDialogVisible.value = true;
        };

        // 修改删除规则方法
        const handleDeleteRule = async (rule) => {
            try {
                await ElMessageBox.confirm('确定要删除该规则吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                });
                
                const res = await axios.post('/plugin/Lottery/api/deleteTurnoverRule', {
                    id: parseInt(rule.id)
                });
                
                if (res.data?.code === 200) {
                    ElMessage.success('删除成功');
                    // 修改这里：使用 fetchTurnoverRules 而不是 loadTurnoverRules
                    await fetchTurnoverRules();
                } else {
                    ElMessage.error(res.data?.msg || '删除失败');
                }
            } catch (error) {
                // 修改错误处理逻辑
                if (error?.toString() !== 'Error: cancel') {
                    console.error('删除规则失败:', error);
                    ElMessage.error(error?.response?.data?.msg || '删除失败');
                }
            }
        };

        // 保存规则
        const saveTurnoverRule = async () => {
            try {
                const res = await axios.post('/plugin/Lottery/api/saveTurnoverRule', turnoverForm.value);
                if (res.data?.code === 200) {
                    ElMessage.success('保存成功');
                    turnoverDialogVisible.value = false;
                    // 更新规则列表
                    turnoverRules.value = res.data.data;
                } else {
                    ElMessage.error(res.data?.msg || '保存失败');
                }
            } catch (error) {
                console.error('保存规则失败:', error);
                ElMessage.error('保存失败');
            }
        };

        // 对话框关闭处理
        const handleDialogClose = () => {
            turnoverForm.value = {
                id: 0,
                turnover_amount: 0,
                draw_times: 1
            };
        };

        // 添加删除类型的方法
        const handleDeleteType = async (row) => {
            try {
                await ElMessageBox.confirm(
                    '确定要删除该奖品类型吗？删除后不可恢复，且不能删除正在使用中的类型。',
                    '提示',
                    {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }
                );
                
                const res = await axios.post('/plugin/Lottery/api/deletePrizeType', {
                    value: row.value
                });
                
                if (res.data.code === 200) {
                    ElMessage.success('删除成功');
                    typeDialogVisible.value = false; // 关闭对话框
                    await fetchPrizeTypes(); // 重新获取类型列表
                } else {
                    ElMessage.error(res.data.msg || '删除失败');
                }
            } catch (error) {
                if (error !== 'cancel') {
                    console.error('删除类型失败:', error);
                    ElMessage.error('删除失败');
                }
            }
        };

        // 获取Hook配置
        const fetchHookConfig = async () => {
            try {
                const res = await axios.post("/plugin/Lottery/api/getHookConfig");
                if (res.data?.code === 200) {
                    Object.assign(hookConfig, res.data.data);
                }
            } catch (error) {
                console.error('获取Hook配置失败:', error);
                ElMessage.error('获取配置失败');
            }
        };

        // 处理Hook状态变更
        const handleHookStatusChange = async (val) => {
            try {
                const res = await axios.post("/plugin/Lottery/api/saveHookConfig", {
                    auto_update_status: val,
                    update_interval: hookConfig.update_interval
                });
                
                if (res.data?.code === 200) {
                    ElMessage.success('设置成功');
                } else {
                    ElMessage.error(res.data?.msg || '设置失败');
                    hookConfig.auto_update_status = !val;
                }
            } catch (error) {
                ElMessage.error('设置失败');
                hookConfig.auto_update_status = !val;
            }
        };

        // 处理间隔时间变更
        const handleIntervalChange = async (val) => {
            try {
                const res = await axios.post("/plugin/Lottery/api/saveHookConfig", {
                    auto_update_status: hookConfig.auto_update_status,
                    update_interval: val
                });
                
                if (res.data?.code === 200) {
                    ElMessage.success('设置成功');
                } else {
                    ElMessage.error(res.data?.msg || '设置失败');
                    hookConfig.update_interval = val;
                }
            } catch (error) {
                ElMessage.error('设置失败');
            }
        };

        // 手动触发更新
        const handleManualUpdate = async () => {
            if (updating.value) return;
            updating.value = true;
            try {
                const res = await axios.post("/plugin/Lottery/api/triggerUpdate");
                if (res.data?.code === 200) {
                    ElMessage.success('更新成功');
                    fetchHookConfig(); // 刷新配置
                } else {
                    ElMessage.error(res.data?.msg || '更新失败');
                }
            } catch (error) {
                ElMessage.error('更新失败');
            } finally {
                updating.value = false;
            }
        };

        // 监听菜单切换
        watch(activeMenu, (newVal) => {
            if (newVal === 'turnover-settings') {
                fetchTurnoverRules();
            }
        });

        // 在 setup() 中添加响应式数据
        const balanceDialogVisible = ref(false);
        const balanceForm = ref({
            record_id: '',
            merchant_id: '',
            amount: 0
        });

        // 在 setup() 中添加方法
        const showBalanceDialog = (row) => {
            balanceForm.value = {
                record_id: row.id,
                merchant_id: row.merchant_id,
                amount: 0,
                prize_name: row.prize_name // 添加奖品名称
            };
            balanceDialogVisible.value = true;
        };

        // 修改处理余额发放方法
        const handleSendBalance = async () => {
            try {
                if (!balanceForm.value.amount || balanceForm.value.amount <= 0) {
                    ElMessage.warning('请输入正确的发放金额');
                    return;
                }

                const params = new URLSearchParams();
                params.append('record_id', balanceForm.value.record_id);
                params.append('merchant_id', balanceForm.value.merchant_id);
                params.append('amount', balanceForm.value.amount);
                params.append('prize_name', balanceForm.value.prize_name); // 添加奖品名称

                const res = await axios.post("/plugin/Lottery/api/sendBalance", params);
                
                if (res.data?.code === 200) {
                    ElMessage.success('发放成功');
                    balanceDialogVisible.value = false;
                    // 刷新记录列表
                    fetchLotteryRecords();
                } else {
                    ElMessage.error(res.data?.msg || '发放失败');
                }
            } catch (error) {
                console.error('发放余额失败:', error);
                ElMessage.error('发放失败: ' + (error.response?.data?.msg || error.message));
            }
        };

        const autoSendBalance = ref(false);
        
        // 获取自动发送配置
        const fetchAutoSendConfig = async () => {
            try {
                const res = await axios.post("/plugin/Lottery/api/getAutoSendConfig");
                if (res.data?.code === 200) {
                    autoSendBalance.value = res.data.data.auto_send;
                }
            } catch (error) {
                console.error('获取自动发送配置失败:', error);
                ElMessage.error('获取配置失败');
            }
        };
        
        // 处理自动发送开关变更
        const handleAutoSendChange = async (val) => {
            try {
                const res = await axios.post("/plugin/Lottery/api/saveAutoSendConfig", {
                    auto_send: val
                });
                
                if (res.data?.code === 200) {
                    ElMessage.success('设置成功');
                } else {
                    ElMessage.error(res.data?.msg || '设置失败');
                    autoSendBalance.value = !val;
                }
            } catch (error) {
                ElMessage.error('设置失败');
                autoSendBalance.value = !val;
            }
        };

        // 重置表单
        const resetForm = ref({
            merchantId: '',
            resetCount: 0
        });

        // 处理类型变更
        const handleTypeChange = () => {
            // 不再需要根据类型重置余额金额
        };

        // 表单验证规则
        const rules = {
            name: [{ required: true, message: '请输入奖品名称', trigger: 'blur' }],
            type: [{ required: true, message: '请选择奖品类型', trigger: 'change' }],
            probability: [
                { required: true, message: '请输入中奖概率', trigger: 'blur' },
                { type: 'number', min: 0, max: 100, message: '概率必须在0-100之间', trigger: 'blur' }
            ],
            stock: [
                { required: true, message: '请输入库存数量', trigger: 'blur' },
                { type: 'number', min: 0, message: '库存不能小于0', trigger: 'blur' }
            ]
        };

        // 添加表单引用
        const prizeFormRef = ref(null);

        // 在 setup() 中添加发货处理方法
        const handleShipment = async (record) => {
            try {
                const res = await axios.post("/plugin/Lottery/api/handleShipment", {
                    record_id: record.id
                });
                
                if (res.data?.code === 200) {
                    // 修改成功提示信息，添加余额显示
                    let successMsg = '发货成功';
                    if (record.balance_amount && record.balance_amount > 0) {
                        successMsg += `，余额:${record.balance_amount}元`;
                    }
                    ElMessage.success(successMsg);
                    
                    // 刷新记录列表
                    fetchLotteryRecords();
                } else {
                    ElMessage.error(res.data?.msg || '发货失败');
                }
            } catch (error) {
                console.error('发货失败:', error);
                ElMessage.error('发货失败: ' + (error.response?.data?.msg || error.message));
            }
        };

        // 添加响应式数据
        const typeManageVisible = ref(false);
        
        // 显示类型管理对话框
        const showTypeManage = () => {
            typeManageVisible.value = true;
            fetchPrizeTypes(); // 获取最新的类型列表
        };

        // 显示添加/编辑对话框
        const showDialog = (type, row = null) => {
            dialogVisible.value = true;
            dialogTitle.value = type === 'add' ? '添加奖品' : '编辑奖品';
            
            if (type === 'add') {
                prizeForm.value = {
                    name: '',
                    type: '',
                    probability: 0,
                    stock: 0,
                    balance_amount: 0,
                    status: true
                };
            } else {
                prizeForm.value = { ...row };
            }
        };

        // 添加类型按钮点击处理函数
        const handleAddType = () => {
            typeDialogTitle.value = '添加类型';
            typeForm.value = {
                label: '',
                value: '',
                tagType: 'success',
                tagColor: '',
                isEdit: false // 添加模式不禁用输入
            };
            typeDialogVisible.value = true;
        };

        const typeFormRef = ref(null); // 添加这行，确保typeFormRef被定义

        // 在 setup() 中添加 handleEditType 方法
        const handleEditType = (row) => {
            typeDialogTitle.value = '编辑类型';
            typeForm.value = {
                label: row.label,
                value: row.value,
                tagType: row.tagType || 'success',
                tagColor: row.tagColor || '',
                isEdit: true // 编辑模式禁用输入
            };
            typeDialogVisible.value = true;
        };

        // 添加搜索表单数据
        const searchForm = ref({
            merchantId: '',
            prizeName: '',
            prizeType: ''
        });
        
        // 搜索处理方法
        const handleSearch = () => {
            currentPage.value = 1; // 重置页码
            fetchLotteryRecords();
        };
        
        // 重置搜索
        const resetSearch = () => {
            searchForm.value = {
                merchantId: '',
                prizeName: '',
                prizeType: ''
            };
            handleSearch();
        };

        // 添加选中记录数组
        const selectedRecords = ref([]);
        
        // 处理表格选择变化
        const handleSelectionChange = (selection) => {
            selectedRecords.value = selection;
        };
        
        // 处理批量发货
        const handleBatchShipment = async () => {
            if (!selectedRecords.value.length) {
                ElMessage.warning('请选择要发货的记录');
                return;
            }
            
            try {
                await ElMessageBox.confirm(
                    `确定要对选中的 ${selectedRecords.value.length} 条记录进行发货操作吗？`,
                    '确认批量发货',
                    {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }
                );
                
                const loading = ElLoading.service({
                    lock: true,
                    text: '批量发货中...'
                });
                
                try {
                    const res = await axios.post("/plugin/Lottery/api/batchShipment", {
                        record_ids: selectedRecords.value.map(record => record.id)
                    });
                    
                    if (res.data?.code === 200) {
                        ElMessage.success(res.data.msg);
                        // 修改这里：使用正确的函数名
                        await fetchLotteryRecords();
                    } else {
                        ElMessage.error(res.data?.msg || '批量发货失败');
                    }
                } finally {
                    loading.close();
                }
            } catch (error) {
                if (error !== 'cancel') {
                    console.error('批量发货失败:', error);
                    ElMessage.error('批量发货失败');
                }
            }
        };

        // 添加隐藏奖品设置对话框
        const hiddenPrizeDialogVisible = ref(false);
        const hiddenPrizes = ref([]);

        // 保存隐藏奖品设置
        const saveHiddenPrizes = async () => {
            try {
                const res = await axios.post("/plugin/Lottery/api/setHiddenPrizes", {
                    hiddenPrizes: hiddenPrizes.value
                });
                
                if (res.data?.code === 200) {
                    ElMessage.success('隐藏奖品设置成功');
                    hiddenPrizeDialogVisible.value = false;
                    // 刷新数据
                    await fetchLotteryRecords();
                } else {
                    ElMessage.error(res.data?.msg || '设置失败');
                }
            } catch (error) {
                console.error('设置隐藏奖品失败:', error);
                ElMessage.error('设置失败：' + (error.response?.data?.msg || error.message));
            }
        };

        // 显示隐藏奖品设置对话框
        const showHiddenPrizeSettings = async () => {
            try {
                const res = await axios.get('/plugin/Lottery/api/getLotteryConfig');
                if (res.data?.code === 200) {
                    hiddenPrizes.value = res.data.data.hidden_prizes || [];
                }
                hiddenPrizeDialogVisible.value = true;
            } catch (error) {
                ElMessage.error('获取设置失败');
            }
        };

        // 添加生成虚拟中奖记录对话框
        const generateDialogVisible = ref(false);
        const generateForm = ref({
            count: 1,
            prize_id: '',
            timeRange: [],
            merchant_prefix: '',  // 添加商家ID前缀字段
            merchant_id_start: '', // 添加商家ID起始值
            merchant_id_end: ''   // 添加商家ID结束值
        });

        // 处理生成虚拟中奖记录
        const handleGenerate = async () => {
            try {
                const params = new URLSearchParams();
                params.append('count', generateForm.value.count);
                if (generateForm.value.prize_id) {
                    params.append('prize_id', generateForm.value.prize_id);
                }
                
                if (generateForm.value.timeRange && generateForm.value.timeRange.length === 2) {
                    params.append('start_time', generateForm.value.timeRange[0]);
                    params.append('end_time', generateForm.value.timeRange[1]);
                }

                if (generateForm.value.merchant_prefix) {
                    params.append('merchant_prefix', generateForm.value.merchant_prefix);
                }

                // 添加商家ID范围参数
                if (generateForm.value.merchant_id_start) {
                    params.append('merchant_id_start', generateForm.value.merchant_id_start);
                }
                if (generateForm.value.merchant_id_end) {
                    params.append('merchant_id_end', generateForm.value.merchant_id_end);
                }
                
                const res = await axios.post('/plugin/Lottery/api/generateFakeRecords', params);
                
                if (res.data.code === 200) {
                    ElMessage.success('生成成功');
                    generateDialogVisible.value = false;
                    // 刷新记录列表
                    fetchLotteryRecords();
                } else {
                    ElMessage.error(res.data.msg || '生成失败');
                }
            } catch (error) {
                console.error('生成虚拟记录失败:', error);
                ElMessage.error('生成失败：' + (error.response?.data?.msg || error.message));
            }
        };

        // 处理对话框关闭
        const handleGenerateDialogClose = () => {
            generateForm.value = {
                count: 1,
                prize_id: '',
                timeRange: [],
                merchant_prefix: '',
                merchant_id_start: '',
                merchant_id_end: ''
            };
        };

        // 显示生成对话框
        const showGenerateDialog = () => {
            generateDialogVisible.value = true;
            generateForm.value = {
                count: 1,
                prize_id: '',
                timeRange: [],
                merchant_prefix: '',
                merchant_id_start: '',
                merchant_id_end: ''
            };
        };

        // 在 setup 中添加相关方法
        const loadHiddenPrizes = async () => {
            try {
                const res = await axios.get('/plugin/Lottery/api/getLotteryConfig');
                if (res.data.code === 200) {
                    hiddenPrizes.value = res.data.data.hidden_prizes || [];
                }
            } catch (error) {
                ElMessage.error('获取隐藏奖品配置失败');
            }
        };

        // 批量删除方法
        const handleBatchDelete = async () => {
            if (!selectedRecords.value.length) {
                ElMessage.warning('请选择要删除的记录');
                return;
            }

            try {
                await ElMessageBox.confirm(
                    `确定要删除选中的 ${selectedRecords.value.length} 条记录吗？`,
                    '确认删除',
                    {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }
                );

                const recordIds = selectedRecords.value.map(record => record.id);
                const res = await axios.post('/plugin/Lottery/api/batchDeleteRecords', {
                    recordIds: recordIds
                });

                if (res.data?.code === 200) {
                    ElMessage.success(res.data.msg);
                    // 重新加载记录列表
                    fetchLotteryRecords();
                } else {
                    ElMessage.error(res.data?.msg || '批量删除失败');
                }
            } catch (error) {
                if (error !== 'cancel') {
                    console.error('批量删除失败:', error);
                    ElMessage.error('批量删除失败：' + error.message);
                }
            }
        };

        // 修改处理规则状态变更的方法
        const handleRuleStatusChange = async (rule) => {
            try {
                // 保存原始状态，以便失败时恢复
                const originalStatus = rule.status;
                
                const res = await axios.post('/plugin/Lottery/api/saveTurnoverRule', {
                    id: rule.id,
                    turnover_amount: rule.turnover_amount,
                    draw_times: rule.draw_times,
                    status: rule.status
                });
                
                if (res.data.code !== 200) {
                    // 如果更新失败，恢复原状态
                    rule.status = originalStatus;
                    ElMessage.error(res.data.msg || '更新失败');
                }
                // 成功时不显示消息提示，因为 el-switch 组件本身会有状态变化的视觉反馈
            } catch (error) {
                console.error('更新规则状态失败:', error);
                // 如果发生错误，恢复原状态
                rule.status = !rule.status;
                ElMessage.error('更新失败');
            }
        };

        return {
            tableData,
            pagination,
            config,
            dialogVisible,
            dialogTitle,
            prizeForm,
            handleAddPrize,
            handleEdit,
            handleDelete,
            savePrize,
            saveLotteryConfig,
            getPrizeTypeTag,
            getPrizeTypeText,
            handleStatusChange,
            lotteryRecords,
            getPrizeName,
            getPrizeType,
            activeMenu,
            tableLoading,
            handleMenuSelect,
            settingsVisible,
            showLotterySettings,
            typeDialogVisible,
            typeDialogTitle,
            typeForm,
            prizeTypes,
            showTypeDialog,
            handleSaveType,
            resetDialogVisible,
            resetForm,
            showResetDialog,
            handleResetDraws,
            recordsPagination,
            handleRecordPageChange,
            handleRestock,
            tableHeight,
            clearDialogVisible,
            clearForm,
            showClearDialog,
            handleClearRecords,
            getPrizeTypeStyle,
            turnoverRules,
            turnoverDialogVisible,
            turnoverDialogTitle,
            turnoverForm,
            handleAddTurnoverRule,
            handleEditRule,
            handleDeleteRule,
            saveTurnoverRule,
            handleDialogClose,
            handleDeleteType,
            hookConfig,
            updating,
            formatLastUpdateTime,
            handleHookStatusChange,
            handleIntervalChange,
            handleManualUpdate,
            turnoverConfig,
            formatDate,
            handleResetTypeChange,
            handleManualReset,
            fetchTurnoverConfig,
            loading,
            currentPage,
            pageSize,
            total,
            records,
            handleCurrentChange,
            balanceDialogVisible,
            balanceForm,
            showBalanceDialog,
            handleSendBalance,
            autoSendBalance,
            handleAutoSendChange,
            handleTypeChange,
            rules,
            getPrizeInfo: (row) => {
                let info = [
                    `ID: ${row.id}`,
                    row.name,
                    `概率: ${row.probability}%`,
                    `库存: ${row.stock}`,
                    // 始终显示余额金额，0表示不发放
                    `余额: ${row.balance_amount} 元`
                ];
                return info.join(' ');
            },
            prizeFormRef, // 添加表单引用到返回值
            handleShipment,
            typeManageVisible,
            showTypeManage,
            showDialog,
            handleAddType,
            typeFormRef,
            typeRules: {
                label: [{ required: true, message: '请输入类型名称', trigger: 'blur' }],
                value: [{ required: true, message: '请输入类型标识', trigger: 'blur' }]
            },
            handleDialogClose,
            handleEditType,
            searchForm,
            handleSearch,
            resetSearch,
            selectedRecords,
            handleSelectionChange,
            handleBatchShipment,
            hiddenPrizeDialogVisible,
            hiddenPrizes,
            saveHiddenPrizes,
            showHiddenPrizeSettings,
            generateDialogVisible,
            generateForm,
            handleGenerate,
            handleGenerateDialogClose,
            showGenerateDialog,
            turnoverPagination,
            handleTurnoverPageChange,
            turnoverFormRef,
            loadHiddenPrizes,
            handleBatchDelete,
            handleRuleStatusChange
        };
    }
});

app.use(ElementPlus, {
    locale: ElementPlusLocaleZhCn
});

app.mount("#app");

// 在app.mount("#app")之前添加
window.addEventListener('error', function(e) {
    if (e.message === 'ResizeObserver loop completed with undelivered notifications.') {
        e.stopPropagation();
    }
});
    </script>
</body>
</html>