<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服聊天</title>
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background: #f0f2f5;
            height: 100vh;
            overflow: hidden;
        }

        .chat-container {
            display: flex;
            width: 100%;
            height: 100vh;
            background: #f0f2f5;
            overflow: hidden;
            position: fixed;
            top: 0;
            left: 0;
        }



        /* 第二板块：客户接待中心 */
        .session-sidebar {
            width: 320px;
            background: #f5f5f5;
            display: flex;
            flex-direction: column;
            border-right: 1px solid #e0e0e0;
        }

        .sidebar-header {
            padding: 20px;
            background: #f5f5f5;
            border-bottom: none;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 600;
            color: #5cb85c;
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .search-box {
            width: 100%;
        }

        .session-list {
            flex: 1;
            overflow-y: auto;
            padding: 8px 0;
        }

        .session-list::-webkit-scrollbar {
            width: 6px;
        }

        .session-list::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .session-list::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .session-list::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .session-item {
            padding: 15px;
            cursor: pointer;
            transition: all 0.2s;
            position: relative;
            background: white;
            margin: 8px 12px;
            border-radius: 12px;
            border: 2px solid transparent;
            min-height: 85px;
            display: flex;
            align-items: flex-start;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }

        .session-item:hover {
            background-color: #f8f9fa;
            border-color: #e0e0e0;
            box-shadow: 0 4px 12px rgba(0,0,0,0.12);
        }

        .session-item.active {
            background-color: #f0f8ff;
            border-color: #5cb85c;
            box-shadow: 0 4px 16px rgba(92, 184, 92, 0.2);
        }

        .session-avatar {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            font-weight: 600;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .session-content {
            flex: 1;
            min-width: 0;
        }

        .session-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 4px;
        }

        .session-name {
            font-weight: 500;
            color: #333;
            font-size: 13px;
            line-height: 1.3;
            max-width: 160px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .session-time {
            font-size: 11px;
            color: #999;
            flex-shrink: 0;
            margin-left: 8px;
        }

        .session-preview {
            font-size: 12px;
            color: #666;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-top: 2px;
        }

        .unread-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            background: #ff4757;
            color: white;
            border-radius: 10px;
            padding: 2px 6px;
            font-size: 10px;
            font-weight: 600;
            min-width: 16px;
            text-align: center;
        }





        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        /* 第三板块：主聊天区域 */
        .chat-main {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #ffffff;
            position: relative;
        }

        /* 第四板块：右侧工具栏 */
        .right-toolbar {
            width: 60px;
            background: #f8f9fa;
            border-left: 1px solid #e0e0e0;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px 0;
        }

        .toolbar-item {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: transparent;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s;
            color: #666;
            border: none;
        }

        .toolbar-item:hover {
            background: #e9ecef;
            color: #5cb85c;
        }

        /* 清空按钮特殊样式 */
        .toolbar-item[title="清空聊天记录"]:hover {
            background: #f8d7da;
            color: #dc3545;
        }

        .toolbar-item.active {
            background: #5cb85c;
            color: white;
        }

        .toolbar-status {
            margin-top: auto;
            text-align: center;
            font-size: 12px;
            color: #5cb85c;
            font-weight: 600;
        }

        /* 快速回复面板 */
        .quick-reply-panel {
            position: fixed;
            top: 0;
            right: -350px;
            width: 350px;
            height: 100vh;
            background: white;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
            transition: right 0.3s ease;
            z-index: 1000;
            display: flex;
            flex-direction: column;
        }

        .quick-reply-panel.show {
            right: 0;
        }

        .quick-reply-header {
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: #f8f9fa;
        }

        .quick-reply-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .quick-reply-close {
            background: none;
            border: none;
            font-size: 20px;
            color: #999;
            cursor: pointer;
            padding: 5px;
        }

        .quick-reply-close:hover {
            color: #333;
        }

        .quick-reply-content {
            flex: 1;
            padding: 0;
            overflow-y: auto;
        }

        .quick-reply-section {
            padding: 20px;
            border-bottom: 1px solid #f0f0f0;
        }

        .quick-reply-section-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .quick-reply-section-title::before {
            content: '';
            width: 4px;
            height: 16px;
            background: #5cb85c;
            margin-right: 8px;
            border-radius: 2px;
        }

        .quick-reply-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .preset-questions-grid {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .preset-question-btn {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 12px 15px;
            font-size: 14px;
            color: #856404;
            cursor: pointer;
            transition: all 0.3s;
            text-align: left;
            line-height: 1.4;
        }

        .preset-question-btn:hover {
            background: #fff3cd;
            border-color: #ffc107;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(255, 193, 7, 0.2);
        }

        .preset-replies-grid {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .preset-reply-btn {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 12px 15px;
            font-size: 14px;
            color: #155724;
            cursor: pointer;
            transition: all 0.3s;
            text-align: left;
            font-weight: 500;
        }

        .preset-reply-btn:hover {
            background: #c3e6cb;
            border-color: #5cb85c;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(92, 184, 92, 0.2);
        }

        .chat-header {
            padding: 16px 20px;
            background: #ffffff;
            border-bottom: 1px solid #e8e8e8;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 1px 4px rgba(0,0,0,0.1);
        }

        .chat-user-info {
            display: flex;
            align-items: center;
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: #5cb85c;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 500;
            margin-right: 12px;
        }

        .session-title-info h3 {
            font-size: 16px;
            color: #333;
            margin-bottom: 4px;
            font-weight: 500;
        }

        .session-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 4px;
        }

        .session-id, .session-time, .session-ip, .session-location {
            font-size: 11px;
            color: #999;
            background: #f5f5f5;
            padding: 2px 6px;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .session-ip.ip-updated, .session-location.ip-updated {
            color: #409eff;
            background: rgba(64, 158, 255, 0.1);
            font-weight: bold;
            animation: ipUpdatePulse 2s ease-in-out;
        }

        @keyframes ipUpdatePulse {
            0% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 0 0 8px rgba(64, 158, 255, 0);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);
            }
        }

        .user-status {
            font-size: 12px;
            color: #666;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
        }

        .status-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #67c23a;
            margin-right: 4px;
            box-shadow: 0 0 4px rgba(103, 194, 58, 0.5);
        }

        .chat-actions {
            display: flex;
            gap: 8px;
        }

        /* 会话详情面板 */
        .session-details-panel {
            background: white;
            border-bottom: 1px solid #e4e7ed;
            padding: 16px 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .details-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .details-header h4 {
            margin: 0;
            color: #303133;
            font-size: 14px;
            font-weight: 600;
        }

        .details-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 8px;
        }

        .detail-item {
            display: flex;
            align-items: center;
            font-size: 12px;
        }

        .detail-item label {
            font-weight: 500;
            color: #606266;
            margin-right: 8px;
            min-width: 60px;
        }

        .detail-item span {
            color: #303133;
        }

        .status-open {
            color: #67c23a !important;
            font-weight: 500;
        }

        .status-closed {
            color: #909399 !important;
            font-weight: 500;
        }

        /* 聊天消息区域 */
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            overflow-x: hidden;
            background: #f0f2f5;
            position: relative;
            min-height: 0;
            word-wrap: break-word;
            word-break: break-all;
        }

        .chat-messages::-webkit-scrollbar {
            width: 6px;
        }

        .chat-messages::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .chat-messages::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .chat-messages::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .message-group {
            margin-bottom: 20px;
        }

        .message-date {
            text-align: center;
            margin: 20px 0;
        }

        .message-date span {
            background: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            color: #909399;
            border: 1px solid #e4e7ed;
        }

        .message {
            display: flex;
            flex-direction: column;
            margin-bottom: 12px;
            align-items: flex-start;
        }

        .message.sent {
            align-items: flex-end;
        }

        .message.received {
            align-items: flex-start;
        }

        .message-time {
            font-size: 11px;
            color: #999;
        }

        .message-bubble {
            max-width: 400px;
            padding: 10px 14px;
            border-radius: 8px;
            word-break: break-word;
            word-wrap: break-word;
            overflow-wrap: break-word;
            line-height: 1.4;
            position: relative;
            font-size: 14px;
            overflow: hidden;
            white-space: pre-wrap;
        }

        .message-bubble img {
            max-width: 100%;
            height: auto;
            display: block;
        }

        .message.received .message-bubble {
            background: white;
            border: 1px solid #e8e8e8;
            color: #333;
        }

        .message.sent .message-bubble {
            background: #5cb85c;
            color: white;
        }

        .message-status {
            font-size: 11px;
            color: #999;
        }

        /* 发送者标识样式 */
        .sender-label {
            font-size: 10px;
            font-weight: 500;
            padding: 2px 6px;
            border-radius: 8px;
            margin-bottom: 4px;
            display: inline-block;
            width: fit-content;
        }

        /* 商家标识 */
        .sender-label.merchant {
            background: #fff3e0;
            color: #f57c00;
            border: 1px solid #ffcc02;
        }

        /* 客户标识 */
        .sender-label.customer {
            background: #e8f4fd;
            color: #1565c0;
            border: 1px solid #bbdefb;
        }

        /* 平台客服标识 */
        .sender-label.staff {
            background: #f3e5f5;
            color: #6a1b9a;
            border: 1px solid #ce93d8;
        }

        /* 右边消息的发送者标识靠右对齐 */
        .message.sent .sender-label {
            text-align: right;
            margin-left: auto;
        }

        /* 左边消息的发送者标识靠左对齐 */
        .message.received .sender-label {
            text-align: left;
            margin-right: auto;
        }

        /* 消息信息容器 */
        .message-info {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 4px;
            font-size: 11px;
        }

        /* 发送消息的信息靠右对齐 */
        .message.sent .message-info {
            justify-content: flex-end;
        }

        /* 接收消息的信息靠左对齐 */
        .message.received .message-info {
            justify-content: flex-start;
        }

        /* 已读状态样式 */
        .message-status.all_read {
            color: #67c23a;
        }

        .message-status.customer_read {
            color: #409eff;
        }

        .message-status.merchant_read {
            color: #e6a23c;
        }

        .message-status.staff_read {
            color: #909399;
        }

        .message-status.partial_read {
            color: #e6a23c;
        }

        .message-status.unread {
            color: #f56c6c;
        }

        .message-status {
            font-size: 11px;
            color: #999;
            margin-left: 8px;
            align-self: flex-end;
            margin-bottom: 4px;
        }

        /* 输入区域 */
        .chat-input {
            padding: 16px 20px;
            border-top: 1px solid #e8e8e8;
            background: white;
        }

        .input-area {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .input-box {
            flex: 1;
        }

        .input-box textarea {
            width: 100%;
            border: 1px solid #dcdfe6;
            border-radius: 8px;
            padding: 12px 16px;
            resize: none;
            font-family: inherit;
            font-size: 14px;
            line-height: 1.4;
            height: 48px;
        }

        .input-box textarea:focus {
            outline: none;
            border-color: #5cb85c;
        }

        .send-btn {
            background: #5cb85c;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            cursor: pointer;
            font-size: 14px;
            height: 48px;
        }

        .send-btn:hover {
            background: #4cae4c;
        }

        .send-btn:disabled {
            background: #c0c4cc;
            cursor: not-allowed;
        }

        /* 工具栏样式 */
        .chat-toolbar {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-right: 8px;
        }

        .toolbar-btn {
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 6px;
            background: #f5f5f5;
            color: #666;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }

        .toolbar-btn:hover {
            background: #e8e8e8;
            color: #333;
        }

        .toolbar-btn:active {
            transform: scale(0.95);
        }

        /* 表情选择器 */
        .chat-toolbar {
            position: relative;
        }

        .emoji-picker {
            position: absolute;
            bottom: 45px;
            right: 0;
            width: 300px;
            height: 200px;
            background: white;
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1000;
            padding: 12px;
            overflow-y: auto;
        }

        .emoji-grid {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 4px;
        }

        .emoji-item {
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border-radius: 4px;
            font-size: 18px;
            transition: background 0.2s;
        }

        .emoji-item:hover {
            background: #f0f0f0;
        }

        /* 消息撤回按钮 */
        .message-actions {
            position: absolute;
            right: -60px;
            top: 50%;
            transform: translateY(-50%);
            opacity: 0;
            transition: opacity 0.2s;
        }

        .message:hover .message-actions {
            opacity: 1;
        }

        .recall-btn {
            background: #ff4757;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 2px 6px;
            font-size: 10px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .recall-btn:hover {
            background: #ff3838;
        }

        /* 右键菜单样式 */
        .context-menu {
            position: fixed;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            padding: 4px 0;
            z-index: 9999;
            min-width: 120px;
            font-size: 13px;
            user-select: none;
            animation: contextMenuFadeIn 0.15s ease-out;
        }

        @keyframes contextMenuFadeIn {
            from {
                opacity: 0;
                transform: scale(0.95);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .context-menu-item {
            padding: 8px 16px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            position: relative;
            white-space: nowrap;
        }

        .context-menu-item:hover {
            background-color: #f5f5f5;
        }

        .context-menu-item.danger {
            color: #ff4757;
        }

        .context-menu-item.danger:hover {
            background-color: #fff5f5;
        }

        .context-menu-item.disabled {
            color: #ccc;
            cursor: not-allowed;
        }

        .context-menu-item.disabled:hover {
            background-color: transparent;
        }

        .context-menu-divider {
            height: 1px;
            background-color: #e0e0e0;
            margin: 4px 0;
        }

        /* 右键点击高亮效果 */
        .message-bubble.context-highlight {
            background-color: rgba(64, 158, 255, 0.1) !important;
            border: 1px solid rgba(64, 158, 255, 0.3) !important;
            transition: all 0.2s ease;
        }

        /* 文件上传隐藏 */
        .file-input {
            display: none;
        }

        /* 右键菜单样式 */
        .context-menu {
            position: fixed;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 9999;
            min-width: 120px;
            padding: 4px 0;
            font-size: 14px;
        }

        .context-menu-item {
            padding: 8px 16px;
            cursor: pointer;
            transition: background-color 0.2s;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .context-menu-item:hover {
            background-color: #f5f5f5;
        }

        .context-menu-item.disabled {
            color: #ccc;
            cursor: not-allowed;
        }

        .context-menu-item.disabled:hover {
            background-color: transparent;
        }

        .context-menu-item.danger {
            color: #ff4757;
        }

        .context-menu-item.danger:hover {
            background-color: #fff5f5;
        }

        .context-menu-divider {
            height: 1px;
            background-color: #e0e0e0;
            margin: 4px 0;
        }

        /* 预设问题区域 */
        .preset-questions {
            padding: 16px;
            background: #fff3cd;
            border-top: 1px solid #e8e8e8;
            border-radius: 8px;
            margin: 12px 16px;
        }

        .preset-questions-title {
            font-size: 14px;
            font-weight: 600;
            color: #856404;
            margin-bottom: 8px;
        }

        .preset-questions-description {
            font-size: 12px;
            color: #856404;
            margin-bottom: 12px;
            opacity: 0.8;
        }

        .preset-questions-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .preset-question-item {
            background: #fff;
            color: #856404;
            padding: 8px 12px;
            border-radius: 16px;
            font-size: 13px;
            border: 1px solid #ffeaa7;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        /* 预设回复区域 */
        .preset-replies {
            padding: 16px;
            background: #d4edda;
            border-top: 1px solid #e8e8e8;
            border-radius: 8px;
            margin: 12px 16px;
        }

        .preset-replies-title {
            font-size: 14px;
            font-weight: 600;
            color: #155724;
            margin-bottom: 12px;
        }

        .preset-replies-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .preset-reply-item {
            background: #5cb85c;
            color: white;
            padding: 8px 16px;
            border-radius: 16px;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s;
            border: none;
            outline: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .preset-reply-item:hover {
            background: #4cae4c;
            transform: translateY(-1px);
            box-shadow: 0 3px 6px rgba(0,0,0,0.15);
        }

        .preset-reply-item:active {
            transform: translateY(0);
        }

        /* 空状态 */
        .empty-chat {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #909399;
        }

        .empty-chat i {
            font-size: 48px;
            margin-bottom: 16px;
        }

        /* 实时状态指示器 */
        .realtime-status {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            color: #67c23a;
            margin-left: 15px;
        }

        .realtime-status.inactive {
            color: #f56c6c;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #67c23a;
            animation: pulse 2s infinite;
        }

        .status-dot.inactive {
            background-color: #f56c6c;
            animation: none;
        }

        @keyframes pulse {
            0% {
                transform: scale(0.95);
                box-shadow: 0 0 0 0 rgba(103, 194, 58, 0.7);
            }

            70% {
                transform: scale(1);
                box-shadow: 0 0 0 10px rgba(103, 194, 58, 0);
            }

            100% {
                transform: scale(0.95);
                box-shadow: 0 0 0 0 rgba(103, 194, 58, 0);
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .session-sidebar {
                width: 100%;
                position: absolute;
                left: -100%;
                z-index: 1000;
                transition: left 0.3s;
            }

            .session-sidebar.show {
                left: 0;
            }

            .chat-main {
                width: 100%;
            }



            .message {
                max-width: 85%;
            }

            .message-bubble {
                max-width: 280px;
            }

            .realtime-status {
                margin-left: 8px;
                font-size: 11px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="chat-container">
            <!-- 第一板块：客户接待中心 -->
            <div class="session-sidebar">
                <div class="sidebar-header">
                    <div class="sidebar-title">客户接待中心</div>
                </div>
                
                <div class="session-list">
                    <div
                        v-for="session in filteredSessions"
                        :key="session.id"
                        :class="['session-item', { active: currentSessionId === session.id }]"
                        @click="selectSession(session)">
                        <div class="session-avatar">
                            {{ session.contact ? session.contact.name.charAt(0) : 'U' }}
                        </div>
                        <div class="session-content">
                            <div class="session-info">
                                <div class="session-name">{{ getSessionDisplayTitle(session) }}</div>
                                <div class="session-time">{{ formatTime(session.last_time) }}</div>
                            </div>
                            <div class="session-preview">{{ session.last_message || '暂无消息' }}</div>
                        </div>

                        <div v-if="session.unread_count > 0" class="unread-badge">{{ session.unread_count }}</div>
                    </div>
                </div>
            </div>

            <!-- 右侧聊天区域 -->
            <div class="chat-main">
                <div v-if="currentSession" class="chat-header">
                    <div class="chat-user-info">
                        <div class="user-avatar">
                            {{ currentSession.contact ? currentSession.contact.name.charAt(0) : 'U' }}
                        </div>
                        <div class="user-details">
                            <div class="session-title-info">
                                <h3>{{ getSessionDisplayTitle(currentSession) }}</h3>
                                <div class="session-meta">
                                    <span class="session-id">会话ID: {{ currentSession.id }}</span>
                                    <span class="session-time">{{ formatSessionTime(currentSession.create_time) }}</span>
                                    <span class="session-ip">IP: {{ currentSession.contact ? currentSession.contact.ip : '未知' }}</span>
                                    <span class="session-location">位置: {{ ipLocation || '解析中...' }}</span>
                                    <!-- 实时状态指示器 -->
                                    <div class="realtime-status" :class="{ inactive: !isPollingActive }">
                                        <div class="status-dot" :class="{ inactive: !isPollingActive }"></div>
                                        <span>{{ isPollingActive ? '实时同步中' : '同步已暂停' }}</span>
                                        <span v-if="hasNewMessages" style="color: #e6a23c; margin-left: 8px;">
                                            <i class="el-icon-bell"></i> 有新消息
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="user-status">
                                <span class="status-dot"></span>
                                {{ currentSession.status === 'open' ? '在线' : '离线' }}
                                <span style="margin-left: 8px; color: rgba(255,255,255,0.8);">
                                    {{ currentSession.contact ? currentSession.contact.email : '' }}
                                </span>
                                <span v-if="currentSession.contact && currentSession.contact.phone"
                                      style="margin-left: 8px; color: rgba(255,255,255,0.8);">
                                    {{ currentSession.contact.phone }}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="chat-actions">
                        <el-button size="small" type="info" @click="goBack" style="margin-right: 8px;">
                            <i class="el-icon-back"></i> 返回管理
                        </el-button>
                        <el-button size="small" type="primary" @click="toggleSessionDetails">
                            <i class="el-icon-info"></i> {{ showSessionDetails ? '隐藏详情' : '查看详情' }}
                        </el-button>
                        <el-button size="small" :type="currentSession.status === 'open' ? 'danger' : 'success'"
                                   @click="toggleSessionStatus">
                            {{ currentSession.status === 'open' ? '关闭会话' : '打开会话' }}
                        </el-button>
                    </div>
                </div>

                <!-- 会话详情面板 -->
                <div v-if="currentSession && showSessionDetails" class="session-details-panel">
                    <div class="details-header">
                        <h4>会话详情</h4>
                        <el-button size="mini" type="text" @click="showSessionDetails = false">
                            <i class="el-icon-close"></i>
                        </el-button>
                    </div>
                    <div class="details-content">
                        <div class="detail-item">
                            <label>会话标题:</label>
                            <span>{{ getSessionDisplayTitle(currentSession) }}</span>
                        </div>
                        <div class="detail-item">
                            <label>客户信息:</label>
                            <span>{{ currentSession.contact ? currentSession.contact.name : '未知' }}</span>
                        </div>
                        <div class="detail-item">
                            <label>联系方式:</label>
                            <span>{{ currentSession.contact ? currentSession.contact.email : '未知' }}</span>
                        </div>
                        <div class="detail-item">
                            <label>会话状态:</label>
                            <span :class="currentSession.status === 'open' ? 'status-open' : 'status-closed'">
                                {{ currentSession.status === 'open' ? '进行中' : '已关闭' }}
                            </span>
                        </div>
                        <div class="detail-item">
                            <label>创建时间:</label>
                            <span>{{ formatSessionTime(currentSession.create_time) }}</span>
                        </div>
                        <div class="detail-item">
                            <label>最后活动:</label>
                            <span>{{ formatTime(currentSession.last_time) }}</span>
                        </div>
                    </div>
                </div>

                <div v-if="currentSession" class="chat-messages" ref="messagesContainer">
                    <div v-for="(group, date) in groupedMessages" :key="date" class="message-group">
                        <div class="message-date">
                            <span>{{ date }}</span>
                        </div>
                        <div v-for="message in group" :key="message.id"
                             :class="['message', (message.role_type === 'staff' || message.sender_type === 'staff') ? 'sent' : 'received']">
                            <div class="message-bubble"
                                 @contextmenu.prevent="showContextMenu($event, message)"
                                 :data-message-id="message.id">
                                <!-- 发送者标识 - 显示所有消息的发送者 -->
                                <div class="sender-label"
                                     :class="getSenderType(message)">
                                    {{ getSenderName(message) }}
                                </div>
                                <div v-if="message.is_recalled" style="font-style: italic; color: #909399;">
                                    [该消息已被撤回]
                                </div>
                                <div v-else-if="message.message_type === 'image' || isImageUrl(message.message)">
                                    <img :src="message.file_url || message.message"
                                         style="max-width: 200px; max-height: 200px; border-radius: 4px; cursor: pointer; display: block; object-fit: cover;"
                                         @click="previewImage(message.file_url || message.message)">
                                </div>
                                <div v-else v-html="formatMessage(message.message)"></div>
                            </div>
                            <!-- 消息信息容器 -->
                            <div class="message-info">
                                <div class="message-time">{{ formatMessageTime(message.create_time) }}</div>
                                <!-- 已读状态显示 -->
                                <div v-if="(message.role_type === 'staff' || message.sender_type === 'staff')"
                                     class="message-status"
                                     :class="getReadStatusClass(message)">
                                    {{ getReadStatusText(message) }}
                                </div>
                                <!-- 撤回按钮 -->
                                <div v-if="(message.role_type === 'staff' || message.sender_type === 'staff') &&
                                          !message.is_recalled &&
                                          canRecallMessage(message.create_time)"
                                     class="message-actions">
                                    <button class="recall-btn" @click="recallMessage(message.id)">撤回</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div v-if="!currentSession" class="empty-chat">
                    <i class="el-icon-chat-dot-round"></i>
                    <p>请选择一个会话开始聊天</p>
                </div>



                <div v-if="currentSession && currentSession.status === 'open'" class="chat-input">
                    <div class="input-area">
                        <div class="input-box">
                            <textarea
                                ref="messageInput"
                                v-model="inputMessage"
                                placeholder="请输入内容..."
                                rows="1"
                                @keydown.enter.ctrl="sendMessage"
                                @keydown.enter.exact.prevent="sendMessage"
                                @paste="handlePaste"></textarea>
                        </div>
                        <div class="chat-toolbar">
                            <!-- 表情选择器 -->
                            <div v-if="showEmojiPicker" class="emoji-picker">
                                <div class="emoji-grid">
                                    <span v-for="emoji in emojiList" :key="emoji"
                                          class="emoji-item"
                                          @click="insertEmoji(emoji)">{{ emoji }}</span>
                                </div>
                            </div>

                            <button class="toolbar-btn" @click="toggleEmojiPicker" title="表情">
                                😊
                            </button>
                            <button class="toolbar-btn" @click="selectImage" title="发送图片">
                                📷
                            </button>
                            <input ref="fileInput" type="file" class="file-input"
                                   accept="image/*" @change="handleFileSelect">
                        </div>
                        <button class="send-btn" @click="sendMessage" :disabled="!inputMessage.trim()">
                            发送
                        </button>
                    </div>
                </div>
            </div>

            <!-- 第四板块：右侧工具栏 -->
            <div class="right-toolbar">
                <button class="toolbar-item active" title="聊天">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M20,2H4A2,2 0 0,0 2,4V22L6,18H20A2,2 0 0,0 22,16V4C22,2.89 21.1,2 20,2Z"/>
                    </svg>
                </button>

                <button class="toolbar-item" @click="toggleQuickReplyPanel" title="快速回复">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M9,22A1,1 0 0,1 8,21V18H4A2,2 0 0,1 2,16V4C2,2.89 2.9,2 4,2H20A2,2 0 0,1 22,4V16A2,2 0 0,1 20,18H13.9L10.2,21.71C10,21.9 9.75,22 9.5,22H9M10,16V19.08L13.08,16H20V4H4V16H10Z"/>
                    </svg>
                </button>

                <button class="toolbar-item" @click="clearChatContent" title="清空聊天记录">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
                    </svg>
                </button>

                <button class="toolbar-item" @click="openSettings" title="设置">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/>
                    </svg>
                </button>


            </div>
        </div>

        <!-- 快速回复面板 -->
        <div class="quick-reply-panel" :class="{ show: showQuickReplyPanel }">
            <div class="quick-reply-header">
                <h3 class="quick-reply-title">快速回复</h3>
                <button class="quick-reply-close" @click="closeQuickReplyPanel">×</button>
            </div>

            <div class="quick-reply-content">
                <!-- 常见问题区域 -->
                <div v-if="presetQuestions.length > 0" class="quick-reply-section">
                    <div class="quick-reply-section-title">常见问题</div>
                    <div class="quick-reply-description">以下是用户经常咨询的问题，点击即可直接提问：</div>
                    <div class="preset-questions-grid">
                        <button v-for="question in presetQuestions" :key="question.id"
                                class="preset-question-btn"
                                @click="usePresetQuestion(question.question)">
                            {{ question.question }}
                        </button>
                    </div>
                </div>

                <!-- 快速回复区域 -->
                <div v-if="presetReplies.length > 0" class="quick-reply-section">
                    <div class="quick-reply-section-title">快速回复</div>
                    <div class="quick-reply-description">选择常用回复内容，快速回复客户：</div>
                    <div class="preset-replies-grid">
                        <button v-for="reply in presetReplies" :key="reply.id"
                                class="preset-reply-btn"
                                @click="usePresetReply(reply.content)">
                            {{ reply.title }}
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图片预览 -->
        <el-dialog v-model="imagePreviewVisible" append-to-body>
            <img width="100%" :src="previewImageUrl" alt="预览图片" style="border-radius: 4px;">
        </el-dialog>

        <!-- 右键菜单 -->
        <div v-if="contextMenu.visible"
             class="context-menu"
             :style="{ left: contextMenu.x + 'px', top: contextMenu.y + 'px' }">
            <div class="context-menu-item" @click="copyMessage">
                <i class="el-icon-document-copy"></i>
                <span>复制</span>
                <span style="margin-left: auto; font-size: 11px; color: #999;">Ctrl+C</span>
            </div>
            <div v-if="shouldShowRecallOption(contextMenu.message)"
                 class="context-menu-item danger"
                 @click="confirmRecallMessage">
                <i class="el-icon-refresh-left"></i>
                撤回
            </div>

            <div class="context-menu-divider"></div>
            <div class="context-menu-item" @click="hideContextMenu">
                <i class="el-icon-close"></i>
                取消
            </div>
        </div>
    </div>

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script>
        const { createApp, ref, computed, onMounted, nextTick } = Vue;
        
        createApp({
            setup() {
                // 数据
                const sessions = ref([]);
                const currentSessionId = ref(null);
                const currentMessages = ref([]);
                const inputMessage = ref('');
                const searchKeyword = ref('');
                const imagePreviewVisible = ref(false);
                const previewImageUrl = ref('');
                const showSessionDetails = ref(false);
                const ipLocation = ref('未知地区');

                // 实时消息相关
                const realTimeUpdateTimer = ref(null);
                const lastMessageId = ref(0);
                const hasNewMessages = ref(false);
                const isPollingActive = ref(true);
                const lastCheckTime = ref(0);
                const showEmojiPicker = ref(false);
                const fileInput = ref(null);

                // 右键菜单相关
                const contextMenu = ref({
                    visible: false,
                    x: 0,
                    y: 0,
                    message: null
                });
                const messageInput = ref(null);
                const presetQuestions = ref([]);
                const presetReplies = ref([]);

                // 快速回复面板相关
                const showQuickReplyPanel = ref(false);

                // 表情列表
                const emojiList = ref([
                    '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣',
                    '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰',
                    '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜',
                    '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏',
                    '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
                    '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠',
                    '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨',
                    '😰', '😥', '😓', '🤗', '🤔', '🤭', '🤫', '🤥',
                    '😶', '😐', '😑', '😬', '🙄', '😯', '😦', '😧',
                    '😮', '😲', '🥱', '😴', '🤤', '😪', '😵', '🤐',
                    '🥴', '🤢', '🤮', '🤧', '😷', '🤒', '🤕', '🤑',
                    '🤠', '😈', '👿', '👹', '👺', '🤡', '💩', '👻',
                    '💀', '☠️', '👽', '👾', '🤖', '🎃', '😺', '😸',
                    '😹', '😻', '😼', '😽', '🙀', '😿', '😾', '👋',
                    '🤚', '🖐️', '✋', '🖖', '👌', '🤏', '✌️', '🤞',
                    '🤟', '🤘', '🤙', '👈', '👉', '👆', '🖕', '👇',
                    '☝️', '👍', '👎', '👊', '✊', '🤛', '🤜', '👏',
                    '🙌', '👐', '🤲', '🤝', '🙏', '✍️', '💅', '🤳',
                    '💪', '🦾', '🦿', '🦵', '🦶', '👂', '🦻', '👃',
                    '🧠', '🫀', '🫁', '🦷', '🦴', '👀', '👁️', '👅',
                    '👄', '💋', '🩸', '👶', '🧒', '👦', '👧', '🧑',
                    '👱', '👨', '🧔', '👩', '🧓', '👴', '👵', '🙍',
                    '🙎', '🙅', '🙆', '💁', '🙋', '🧏', '🙇', '🤦',
                    '🤷', '👮', '🕵️', '💂', '🥷', '👷', '🤴', '👸',
                    '👳', '👲', '🧕', '🤵', '👰', '🤰', '🤱', '👼',
                    '🎅', '🤶', '🦸', '🦹', '🧙', '🧚', '🧛', '🧜',
                    '🧝', '🧞', '🧟', '💆', '💇', '🚶', '🧍', '🧎',
                    '🏃', '💃', '🕺', '🕴️', '👯', '🧖', '🧗', '🤺',
                    '🏇', '⛷️', '🏂', '🏌️', '🏄', '🚣', '🏊', '⛹️',
                    '🏋️', '🚴', '🚵', '🤸', '🤼', '🤽', '🤾', '🤹',
                    '🧘', '🛀', '🛌', '👭', '👫', '👬', '💏', '💑',
                    '👪', '👨‍👩‍👧', '👨‍👩‍👧‍👦', '👨‍👩‍👦‍👦', '👨‍👩‍👧‍👧', '👨‍👨‍👦', '👨‍👨‍👧', '👨‍👨‍👧‍👦'
                ]);
                
                // 计算属性
                const currentSession = computed(() => {
                    const session = sessions.value.find(s => s.id === currentSessionId.value) || null;
                    if (session) {
                        return {
                            ...session,
                            ipLocation: ipLocation.value
                        };
                    }
                    return null;
                });
                
                const filteredSessions = computed(() => {
                    if (!searchKeyword.value) return sessions.value;
                    return sessions.value.filter(session => {
                        const name = session.contact ? session.contact.name : '';
                        const email = session.contact ? session.contact.email : '';
                        const keyword = searchKeyword.value.toLowerCase();
                        return name.toLowerCase().includes(keyword) || 
                               email.toLowerCase().includes(keyword) ||
                               session.last_message.toLowerCase().includes(keyword);
                    });
                });
                
                const groupedMessages = computed(() => {
                    const groups = {};
                    currentMessages.value.forEach(message => {
                        const date = formatMessageDate(message.create_time);
                        if (!groups[date]) groups[date] = [];
                        groups[date].push(message);
                    });
                    return groups;
                });
                
                // 方法
                const loadSessions = async () => {
                    try {
                        const response = await axios.get('/plugin/Customersystem/api/getSessionList?limit=100');
                        if (response.data.code === 200) {
                            const data = response.data.data;
                            sessions.value = data.data || [];
                        }
                    } catch (error) {

                        ElementPlus.ElMessage.error('加载会话列表失败');
                    }
                };
                
                const selectSession = async (session) => {
                    currentSessionId.value = session.id;

                    try {
                        // 获取会话详情
                        const response = await axios.get(`/plugin/Customersystem/api/getSessionDetail?id=${session.id}`);
                        if (response.data.code === 200) {
                            const data = response.data.data;

                            // 更新当前会话信息
                            const updatedSession = {
                                ...session,
                                ...data.session,
                                contact: data.contact
                            };

                            // 更新会话列表中的对应项
                            const index = sessions.value.findIndex(s => s.id === session.id);
                            if (index !== -1) {
                                sessions.value[index] = updatedSession;
                            }

                            // 设置消息列表
                            currentMessages.value = data.messages || [];

                            // 更新最后消息ID，用于实时轮询
                            if (data.messages && data.messages.length > 0) {
                                const maxId = Math.max(...data.messages.map(msg => msg.id || 0));
                                lastMessageId.value = maxId;
                            } else {
                                lastMessageId.value = 0;
                            }

                            // 解析IP地址
                            if (updatedSession.contact && updatedSession.contact.ip) {
                                ipLocation.value = '解析中...';
                                const location = await getLocationFromIP(updatedSession.contact.ip);
                                ipLocation.value = location;
                            } else {
                                ipLocation.value = '未知地区';
                            }

                            // 自动标记未读消息为已读
                            setTimeout(() => {
                                markAllMessagesAsRead();
                            }, 500);

                            scrollToBottom();
                        } else {
                            ElementPlus.ElMessage.error(response.data.msg || '获取会话详情失败');
                        }
                    } catch (error) {

                        ElementPlus.ElMessage.error('获取会话详情失败');
                    }
                };
                
                const loadMessages = async (sessionId) => {
                    try {
                        const response = await axios.get(`/plugin/Customersystem/api/getSessionDetail?id=${sessionId}`);
                        if (response.data.code === 200) {
                            const data = response.data.data;
                            currentMessages.value = data.messages || [];

                            // 更新最后消息ID
                            if (data.messages && data.messages.length > 0) {
                                const maxId = Math.max(...data.messages.map(msg => msg.id || 0));
                                lastMessageId.value = maxId;
                            }

                            scrollToBottom();
                        }
                    } catch (error) {

                    }
                };
                
                const sendMessage = async () => {
                    if (!inputMessage.value.trim() || !currentSession.value) return;

                    try {
                        const response = await axios.post('/plugin/Customersystem/api/sendMessage', {
                            session_id: currentSession.value.id,
                            message: inputMessage.value,
                            sender_type: 'staff',
                            message_type: 'text'
                        });

                        if (response.data.code === 200) {
                            inputMessage.value = '';
                            await loadMessages(currentSession.value.id);

                            // 更新最后消息ID
                            if (response.data.data && response.data.data.id) {
                                lastMessageId.value = Math.max(lastMessageId.value, response.data.data.id);
                            }

                            scrollToBottom();
                            ElementPlus.ElMessage.success('消息发送成功');
                        } else {
                            ElementPlus.ElMessage.error(response.data.msg || '发送消息失败');
                        }
                    } catch (error) {

                        ElementPlus.ElMessage.error('发送消息失败');
                    }
                };
                
                const scrollToBottom = () => {
                    nextTick(() => {
                        const container = document.querySelector('.chat-messages');
                        if (container) {
                            container.scrollTop = container.scrollHeight;
                        }
                    });
                };
                
                const formatTime = (timestamp) => {
                    if (!timestamp) return '';
                    const date = new Date(timestamp * 1000);
                    const now = new Date();
                    const diff = now - date;
                    
                    if (diff < 60000) return '刚刚';
                    if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前';
                    if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前';
                    return date.toLocaleDateString();
                };
                
                const formatMessageTime = (timestamp) => {
                    if (!timestamp) return '';
                    const date = new Date(timestamp * 1000);
                    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
                };
                
                const formatMessageDate = (timestamp) => {
                    if (!timestamp) return '';
                    const date = new Date(timestamp * 1000);
                    const today = new Date();
                    const yesterday = new Date(today);
                    yesterday.setDate(yesterday.getDate() - 1);

                    if (date.toDateString() === today.toDateString()) return '今天';
                    if (date.toDateString() === yesterday.toDateString()) return '昨天';
                    return date.toLocaleDateString('zh-CN');
                };

                const formatSessionTime = (timestamp) => {
                    if (!timestamp) return '未知时间';
                    const date = new Date(timestamp * 1000);
                    return date.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                };
                
                const formatMessage = (message) => {
                    if (!message) return '';
                    return message.replace(/\n/g, '<br>');
                };

                const getRoleDisplayName = (message) => {
                    const roleType = message.role_type || message.sender_type;
                    switch (roleType) {
                        case 'staff': return '客服';
                        case 'merchant': return '商家';
                        case 'customer': return '客户';
                        case 'supplier': return '供应商';
                        default: return '未知';
                    }
                };

                const getSessionDisplayTitle = (session) => {
                    if (session.title && session.title.trim()) {
                        return session.title;
                    }

                    // 如果没有标题，根据会话信息生成
                    let title = '';
                    if (session.contact) {
                        title = session.contact.name || '未知用户';
                        if (session.source === 'merchant') {
                            title += ' - 联系商家';
                        } else {
                            title += ' - 联系客服';
                        }
                    } else {
                        title = '未知会话';
                    }

                    return title;
                };
                
                const isImageUrl = (url) => {
                    if (!url) return false;
                    return /\.(jpg|jpeg|png|gif|webp)$/i.test(url);
                };
                
                const previewImage = (url) => {
                    previewImageUrl.value = url;
                    imagePreviewVisible.value = true;
                };

                // IP地址解析函数
                const getLocationFromIP = async (ip) => {
                    if (!ip || ip === '未知') return '未知地区';

                    try {
                        // 使用服务器端API查询IP位置
                        const response = await axios.post('/plugin/Customersystem/api/queryIPLocation', {
                            ip: ip
                        });

                        if (response.data.code === 200) {
                            const data = response.data.data;

                            // 返回组合的位置字符串
                            if (data.location_string && data.location_string !== '未知地区') {
                                return data.location_string;
                            }

                            // 如果没有location_string，手动组合
                            const parts = [];
                            if (data.country) parts.push(data.country);
                            if (data.province) parts.push(data.province);
                            if (data.city) parts.push(data.city);

                            return parts.length > 0 ? parts.join(' ') : '未知地区';
                        } else {
                            console.warn('IP位置查询失败:', response.data.msg);
                            return '未知地区';
                        }
                    } catch (error) {
                        console.error('IP位置查询出错:', error);
                        return '未知地区';
                    }
                };


                // 表情功能
                const toggleEmojiPicker = () => {
                    showEmojiPicker.value = !showEmojiPicker.value;
                };

                const insertEmoji = (emoji) => {
                    inputMessage.value += emoji;
                    showEmojiPicker.value = false;
                    messageInput.value?.focus();
                };

                // 图片上传功能
                const selectImage = () => {
                    fileInput.value?.click();
                };

                const handleFileSelect = (event) => {
                    const file = event.target.files[0];
                    if (file) {
                        uploadImage(file);
                    }
                };

                const uploadImage = async (file) => {
                    if (!currentSession.value) return;

                    const formData = new FormData();
                    formData.append('file', file);

                    try {
                        const response = await axios.post('/adminApi/Upload/file', formData, {
                            headers: {
                                'Content-Type': 'multipart/form-data'
                            }
                        });

                        if (response.data.code === 200) {
                            const imageUrl = response.data.data.url;

                            // 发送图片消息
                            const messageResponse = await axios.post('/plugin/Customersystem/api/sendMessage', {
                                session_id: currentSession.value.id,
                                message: imageUrl,
                                message_type: 'image',
                                sender_type: 'staff'
                            });

                            if (messageResponse.data.code === 200) {
                                // 添加到消息列表
                                currentMessages.value.push({
                                    id: Date.now(),
                                    message: imageUrl,
                                    message_type: 'image',
                                    sender_type: 'staff',
                                    create_time: new Date().toISOString()
                                });
                                scrollToBottom();
                            }
                        } else {
                            ElementPlus.ElMessage.error('图片上传失败');
                        }
                    } catch (error) {

                        ElementPlus.ElMessage.error('图片上传失败');
                    }
                };

                // 粘贴图片功能
                const handlePaste = async (event) => {
                    const items = event.clipboardData?.items;
                    if (!items) return;

                    for (let i = 0; i < items.length; i++) {
                        const item = items[i];
                        if (item.type.indexOf('image') !== -1) {
                            event.preventDefault();
                            const file = item.getAsFile();
                            if (file) {
                                await uploadImage(file);
                            }
                            break;
                        }
                    }
                };

                // 撤回功能
                const canRecallMessage = (createTime) => {
                    if (!createTime) return false;

                    const now = new Date();
                    let messageTime;

                    // 处理不同的时间格式
                    if (typeof createTime === 'number') {
                        // 如果是时间戳（秒或毫秒）
                        messageTime = createTime > 1000000000000 ? new Date(createTime) : new Date(createTime * 1000);
                    } else if (typeof createTime === 'string') {
                        // 如果是字符串格式
                        messageTime = new Date(createTime);
                    } else {
                        messageTime = new Date(createTime);
                    }

                    // 检查时间是否有效
                    if (isNaN(messageTime.getTime())) {
                        console.warn('Invalid message time:', createTime);
                        return false;
                    }

                    const diffMinutes = (now - messageTime) / (1000 * 60);

                    return diffMinutes <= 2; // 2分钟内可以撤回
                };

                // 判断是否显示撤回选项
                const shouldShowRecallOption = (message) => {
                    if (!message) return false;

                    // 检查是否是客服发送的消息
                    const isStaffMessage = message.role_type === 'staff' || message.sender_type === 'staff';
                    if (!isStaffMessage) return false;

                    // 检查是否已撤回
                    if (message.is_recalled) return false;

                    // 检查时间限制
                    return canRecallMessage(message.create_time);
                };

                const recallMessage = async (messageId) => {
                    try {
                        // 检查当前会话ID
                        if (!currentSessionId.value) {
                            ElementPlus.ElMessage.error('当前会话ID为空，无法撤回消息');
                            return;
                        }

                        const response = await axios.post('/plugin/Customersystem/api/recallMessage', {
                            message_id: messageId,
                            session_id: currentSessionId.value
                        });

                        if (response.data.code === 200) {
                            // 更新消息状态
                            const message = currentMessages.value.find(m => m.id === messageId);
                            if (message) {
                                message.is_recalled = true;
                                message.message = '[该消息已撤回]';
                                // 如果是图片消息，也要更新消息类型
                                if (message.message_type === 'image' || message.message_type === 'file') {
                                    message.message_type = 'text';
                                    message.file_url = '';
                                }
                            }
                            ElementPlus.ElMessage.success('消息已撤回');
                        } else {
                            ElementPlus.ElMessage.error(response.data.msg || '撤回失败');
                        }
                    } catch (error) {

                        ElementPlus.ElMessage.error('撤回失败');
                    }
                };

                // 右键菜单功能
                const showContextMenu = (event, message) => {
                    event.preventDefault();

                    // 获取消息气泡的位置信息
                    const messageBubble = event.currentTarget;
                    const bubbleRect = messageBubble.getBoundingClientRect();
                    const messageElement = messageBubble.closest('.message');
                    const isStaffMessage = messageElement.classList.contains('sent');

                    // 计算菜单位置
                    const menuWidth = 120;
                    const menuHeight = 120;
                    let x, y;

                    if (isStaffMessage) {
                        // 客服消息在右边，菜单显示在气泡左边
                        x = bubbleRect.left - menuWidth - 10;
                    } else {
                        // 客户消息在左边，菜单显示在气泡右边
                        x = bubbleRect.right + 10;
                    }

                    // 垂直位置以气泡顶部为准
                    y = bubbleRect.top;

                    // 检查边界并调整位置
                    if (x < 10) {
                        x = bubbleRect.right + 10; // 如果左边空间不够，显示在右边
                    }
                    if (x + menuWidth > window.innerWidth - 10) {
                        x = bubbleRect.left - menuWidth - 10; // 如果右边空间不够，显示在左边
                    }

                    // 检查下边界
                    if (y + menuHeight > window.innerHeight - 10) {
                        y = window.innerHeight - menuHeight - 10;
                    }

                    // 确保不超出上边界
                    y = Math.max(10, y);

                    contextMenu.value = {
                        visible: true,
                        x: x,
                        y: y,
                        message: message
                    };

                    // 添加高亮效果
                    messageBubble.classList.add('context-highlight');

                    // 3秒后移除高亮效果
                    setTimeout(() => {
                        messageBubble.classList.remove('context-highlight');
                    }, 3000);
                };

                const hideContextMenu = () => {
                    contextMenu.value.visible = false;
                    contextMenu.value.message = null;

                    // 移除所有高亮效果
                    document.querySelectorAll('.context-highlight').forEach(el => {
                        el.classList.remove('context-highlight');
                    });
                };

                const copyMessage = async () => {
                    if (!contextMenu.value.message) return;

                    try {
                        let textToCopy = '';
                        const message = contextMenu.value.message;

                        if (message.is_recalled) {
                            textToCopy = '[该消息已被撤回]';
                        } else if (message.message_type === 'image') {
                            textToCopy = '[图片消息]';
                        } else {
                            // 移除HTML标签，只保留纯文本
                            const tempDiv = document.createElement('div');
                            tempDiv.innerHTML = message.message;
                            textToCopy = tempDiv.textContent || tempDiv.innerText || '';
                        }

                        await navigator.clipboard.writeText(textToCopy);
                        ElementPlus.ElMessage.success('消息已复制到剪贴板');
                    } catch (error) {
                        // 如果现代API不可用，使用传统方法
                        const textArea = document.createElement('textarea');
                        textArea.value = contextMenu.value.message.message || '[该消息已被撤回]';
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);
                        ElementPlus.ElMessage.success('消息已复制到剪贴板');
                    }

                    hideContextMenu();
                };

                const confirmRecallMessage = async () => {
                    if (!contextMenu.value.message) return;

                    try {
                        await ElementPlus.ElMessageBox.confirm(
                            '撤回后，该消息将从聊天记录中删除，且无法恢复。确定要撤回这条消息吗？',
                            '确认撤回',
                            {
                                confirmButtonText: '确定撤回',
                                cancelButtonText: '取消',
                                type: 'warning',
                                confirmButtonClass: 'el-button--danger'
                            }
                        );

                        await recallMessage(contextMenu.value.message.id);
                        hideContextMenu();
                    } catch (error) {
                        // 用户取消撤回
                        if (error !== 'cancel') {
                            console.error('撤回确认失败:', error);
                        }
                        hideContextMenu();
                    }
                };

                // 加载预设问题和回复
                const loadPresetData = async () => {
                    try {
                        const response = await axios.post('/plugin/Customersystem/api/getParams');
                        if (response.data.code === 200) {
                            const data = response.data.data;


                            // 加载预设问题 - 从preset_questions.items中读取
                            if (data.preset_questions && Array.isArray(data.preset_questions.items) && data.preset_questions.items.length > 0) {
                                presetQuestions.value = data.preset_questions.items.map((item, index) => ({
                                    id: index + 1,
                                    question: typeof item === 'string' ? item : (item.question || item.label || item.title || `问题${index + 1}`)
                                }));
                            } else {
                                // 默认预设问题
                                presetQuestions.value = [
                                    { id: 1, question: '如何下单？' },
                                    { id: 2, question: '支付方式有哪些？' },
                                    { id: 3, question: '配送时间多久？' },
                                    { id: 4, question: '如何退换货？' },
                                    { id: 5, question: '有优惠活动吗？' }
                                ];
                            }

                            // 加载快速回复 - 从quick_replies.items中读取
                            if (data.quick_replies && data.quick_replies.enabled && Array.isArray(data.quick_replies.items) && data.quick_replies.items.length > 0) {
                                presetReplies.value = data.quick_replies.items
                                    .filter(item => item.label && item.content) // 过滤掉空的项目
                                    .map((item, index) => ({
                                        id: index + 1,
                                        title: item.label,
                                        content: item.content
                                    }));
                            } else if (data.preset_replies && data.preset_replies.enabled && Array.isArray(data.preset_replies.items) && data.preset_replies.items.length > 0) {
                                // 备用：从preset_replies.items中读取
                                presetReplies.value = data.preset_replies.items
                                    .filter(item => item.label && item.content) // 过滤掉空的项目
                                    .map((item, index) => ({
                                        id: index + 1,
                                        title: item.label,
                                        content: item.content
                                    }));
                            } else {
                                // 默认预设回复
                                presetReplies.value = [
                                    { id: 1, title: '您好', content: '您好！很高兴为您服务，请问有什么可以帮助您的吗？' },
                                    { id: 2, title: '稍等', content: '好的，请稍等片刻，我马上为您处理。' },
                                    { id: 3, title: '感谢', content: '感谢您的咨询，如有其他问题请随时联系我们。' },
                                    { id: 4, title: '抱歉', content: '非常抱歉给您带来不便，我们会尽快为您解决。' },
                                    { id: 5, title: '再见', content: '感谢您的咨询，祝您生活愉快！' }
                                ];
                            }


                        }
                    } catch (error) {

                        // 使用默认数据
                        presetQuestions.value = [
                            { id: 1, question: '如何下单？' },
                            { id: 2, question: '支付方式有哪些？' },
                            { id: 3, question: '配送时间多久？' },
                            { id: 4, question: '如何退换货？' },
                            { id: 5, question: '有优惠活动吗？' }
                        ];

                        presetReplies.value = [
                            { id: 1, title: '您好', content: '您好！很高兴为您服务，请问有什么可以帮助您的吗？' },
                            { id: 2, title: '稍等', content: '好的，请稍等片刻，我马上为您处理。' },
                            { id: 3, title: '感谢', content: '感谢您的咨询，如有其他问题请随时联系我们。' },
                            { id: 4, title: '抱歉', content: '非常抱歉给您带来不便，我们会尽快为您解决。' },
                            { id: 5, title: '再见', content: '感谢您的咨询，祝您生活愉快！' }
                        ];
                    }
                };

                // 使用预设回复
                const usePresetReply = (content) => {
                    inputMessage.value = content;
                    messageInput.value?.focus();
                    closeQuickReplyPanel();
                    ElementPlus.ElMessage.success('回复内容已填入输入框');
                };

                const goBack = () => {
                    window.location.href = '/plugin/Customersystem/api/index';
                };

                const toggleSessionDetails = () => {
                    showSessionDetails.value = !showSessionDetails.value;
                };

                const showContactInfo = () => {
                    // 显示联系人详情
                };
                
                const toggleSessionStatus = async () => {
                    if (!currentSession.value) return;

                    const newStatus = currentSession.value.status === 'open' ? 'closed' : 'open';
                    const actionText = newStatus === 'closed' ? '关闭' : '打开';

                    try {
                        const confirmed = await ElementPlus.ElMessageBox.confirm(
                            `确定要${actionText}此会话吗?`,
                            '提示',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning'
                            }
                        );

                        if (confirmed) {
                            const response = await axios.post('/plugin/Customersystem/api/updateSessionStatus', {
                                session_id: currentSession.value.id,
                                status: newStatus
                            });

                            if (response.data.code === 200) {
                                ElementPlus.ElMessage.success(`会话已${actionText}`);

                                // 更新当前会话状态
                                currentSession.value.status = newStatus;

                                // 更新会话列表中的状态
                                const index = sessions.value.findIndex(s => s.id === currentSession.value.id);
                                if (index !== -1) {
                                    sessions.value[index].status = newStatus;
                                }
                            } else {
                                ElementPlus.ElMessage.error(response.data.msg || '操作失败');
                            }
                        }
                    } catch (error) {
                        if (error !== 'cancel') {

                            ElementPlus.ElMessage.error('操作失败');
                        }
                    }
                };

                // 实时消息轮询功能
                const startRealTimePolling = () => {
                    if (realTimeUpdateTimer.value) {
                        clearInterval(realTimeUpdateTimer.value);
                    }

                    // 每2秒检查一次新消息和已读状态
                    realTimeUpdateTimer.value = setInterval(async () => {
                        if (!isPollingActive.value) return;

                        try {
                            await checkForNewMessages();
                        } catch (error) {
                            // 静默处理错误
                        }
                    }, 2000);
                };

                const stopRealTimePolling = () => {
                    if (realTimeUpdateTimer.value) {
                        clearInterval(realTimeUpdateTimer.value);
                        realTimeUpdateTimer.value = null;
                    }
                    isPollingActive.value = false;
                };

                const checkForNewMessages = async () => {
                    try {
                        // 如果当前有打开的会话，直接检查该会话的新消息
                        if (currentSessionId.value) {
                            await loadSessionRealTimeMessages();
                        }

                        // 更新会话列表
                        await loadSessions();
                    } catch (error) {
                        // 静默处理错误
                    }
                };

                const loadSessionRealTimeMessages = async () => {
                    if (!currentSessionId.value) return;

                    try {
                        // 获取会话的所有消息
                        const response = await axios.get('/plugin/Customersystem/api/getSessionDetail', {
                            params: {
                                id: currentSessionId.value
                            }
                        });

                        if (response.data.code === 200) {
                            const data = response.data.data;
                            const allMessages = data.messages || [];
                            const currentMessageCount = currentMessages.value.length;

                            // 检查IP是否发生变化
                            if (data.session && data.session.contact && currentSession.value && currentSession.value.contact) {
                                const newIP = data.session.contact.ip;
                                const oldIP = currentSession.value.contact.ip;

                                if (newIP && oldIP && newIP !== oldIP) {
                                    console.log('检测到IP变化:', {
                                        old: oldIP,
                                        new: newIP
                                    });

                                    // 更新当前会话信息中的IP
                                    currentSession.value.contact.ip = newIP;

                                    // 同时更新会话列表中对应会话的IP
                                    const sessionIndex = sessions.value.findIndex(s => s.id === currentSessionId.value);
                                    if (sessionIndex !== -1 && sessions.value[sessionIndex].contact) {
                                        sessions.value[sessionIndex].contact.ip = newIP;
                                    }

                                    // 添加视觉动画效果
                                    nextTick(() => {
                                        const ipElement = document.querySelector('.session-ip');
                                        const locationElement = document.querySelector('.session-location');

                                        if (ipElement) {
                                            ipElement.classList.add('ip-updated');
                                            setTimeout(() => {
                                                ipElement.classList.remove('ip-updated');
                                            }, 2000);
                                        }

                                        if (locationElement) {
                                            locationElement.classList.add('ip-updated');
                                            setTimeout(() => {
                                                locationElement.classList.remove('ip-updated');
                                            }, 2000);
                                        }
                                    });

                                    // 重新解析IP地址位置
                                    ipLocation.value = '解析中...';
                                    try {
                                        const location = await getLocationFromIP(newIP);
                                        ipLocation.value = location;

                                        // 显示IP变化提示
                                        ElementPlus.ElMessage({
                                            message: `客户IP已更新: ${oldIP} → ${newIP} (${location})`,
                                            type: 'info',
                                            duration: 5000,
                                            showClose: true
                                        });

                                        console.log('IP地址已更新:', {
                                            old: oldIP,
                                            new: newIP,
                                            location: location
                                        });
                                    } catch (error) {
                                        console.error('IP位置解析失败:', error);
                                        ipLocation.value = '未知地区';
                                        ElementPlus.ElMessage({
                                            message: `客户IP已更新: ${oldIP} → ${newIP}`,
                                            type: 'info',
                                            duration: 5000,
                                            showClose: true
                                        });
                                    }
                                }
                            }

                            // 检查是否有新消息或已读状态变化
                            let hasUpdates = false;
                            let hasNewMessages = false;

                            // 检查新消息
                            if (allMessages.length > currentMessageCount) {
                                hasUpdates = true;
                                hasNewMessages = true;
                            } else {
                                // 检查已读状态是否有变化
                                for (let i = 0; i < currentMessages.value.length; i++) {
                                    const currentMsg = currentMessages.value[i];
                                    const serverMsg = allMessages.find(msg => msg.id === currentMsg.id);

                                    if (serverMsg) {
                                        // 检查已读状态字段是否有变化
                                        if (currentMsg.customer_read !== serverMsg.customer_read ||
                                            currentMsg.merchant_read !== serverMsg.merchant_read ||
                                            currentMsg.staff_read !== serverMsg.staff_read ||
                                            currentMsg.is_read !== serverMsg.is_read) {
                                            hasUpdates = true;
                                            break;
                                        }
                                    }
                                }
                            }

                            // 如果有更新，刷新消息列表
                            if (hasUpdates) {
                                // 直接更新整个消息列表
                                currentMessages.value = allMessages;

                                // 更新最后消息ID
                                if (allMessages.length > 0) {
                                    const maxId = Math.max(...allMessages.map(msg => msg.id || 0));
                                    lastMessageId.value = maxId;
                                }

                                // 如果有新消息才滚动到底部和播放提示音
                                if (hasNewMessages) {
                                    nextTick(() => {
                                        scrollToBottom();
                                    });
                                    playNotificationSound();
                                }
                            }
                        }
                    } catch (error) {
                        // 静默处理错误
                    }
                };

                const playNotificationSound = () => {
                    try {
                        // 创建简单的提示音
                        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                        const oscillator = audioContext.createOscillator();
                        const gainNode = audioContext.createGain();

                        oscillator.connect(gainNode);
                        gainNode.connect(audioContext.destination);

                        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

                        oscillator.start(audioContext.currentTime);
                        oscillator.stop(audioContext.currentTime + 0.1);
                    } catch (error) {
                        // 如果音频播放失败，静默处理
                    }
                };

                const markCurrentSessionAsRead = async () => {
                    if (!currentSessionId.value) return;

                    try {
                        await axios.post('/plugin/Customersystem/api/markSessionAsRead', {
                            session_id: currentSessionId.value
                        });
                    } catch (error) {
                        // 静默处理错误
                    }
                };

                // 快速回复面板控制函数
                const toggleQuickReplyPanel = () => {
                    showQuickReplyPanel.value = !showQuickReplyPanel.value;
                };

                const closeQuickReplyPanel = () => {
                    showQuickReplyPanel.value = false;
                };

                // 使用预设问题
                const usePresetQuestion = (question) => {
                    inputMessage.value = question;
                    messageInput.value?.focus();
                    closeQuickReplyPanel();
                    ElementPlus.ElMessage.success('问题已填入输入框');
                };

                // 标记消息为已读
                const markMessageAsRead = async (messageId) => {
                    if (!messageId || !currentSessionId.value) return;

                    try {
                        await axios.post('/plugin/Customersystem/api/markMessageRead', {
                            message_id: messageId,
                            session_id: currentSessionId.value,
                            role_type: 'staff'
                        });

                        // 更新本地消息状态
                        const messageIndex = currentMessages.value.findIndex(msg => msg.id === messageId);
                        if (messageIndex !== -1) {
                            currentMessages.value[messageIndex].staff_read = 1;
                            currentMessages.value[messageIndex].staff_read_time = Math.floor(Date.now() / 1000);
                        }
                    } catch (error) {
                        // 静默处理错误
                    }
                };

                // 获取消息已读状态CSS类名
                const getReadStatusClass = (message) => {
                    // 客服端：只显示自己发送的消息的已读状态
                    if (message.role_type !== 'staff' && message.sender_type !== 'staff') {
                        return '';
                    }

                    // 检查客户和商家是否已读
                    const customerRead = message.customer_read === 1;
                    const merchantRead = message.merchant_read === 1;

                    if (customerRead && merchantRead) {
                        return 'all_read';
                    } else if (customerRead && !merchantRead) {
                        return 'customer_read';
                    } else if (!customerRead && merchantRead) {
                        return 'merchant_read';
                    } else {
                        return 'unread';
                    }
                };

                // 获取发送者名称
                const getSenderName = (message) => {
                    const roleType = message.role_type || message.sender_type;

                    switch (roleType) {
                        case 'customer':
                            return '客户';
                        case 'merchant':
                            return '商家';
                        case 'staff':
                            return '平台客服';
                        default:
                            return '未知';
                    }
                };

                // 获取发送者类型（用于CSS类名）
                const getSenderType = (message) => {
                    const roleType = message.role_type || message.sender_type;
                    return roleType || 'unknown';
                };

                // 获取消息已读状态文本
                const getReadStatusText = (message) => {
                    // 客服端：只显示自己发送的消息的已读状态
                    if (message.role_type !== 'staff' && message.sender_type !== 'staff') {
                        return '';
                    }

                    // 检查客户和商家是否已读
                    const customerRead = message.customer_read === 1;
                    const merchantRead = message.merchant_read === 1;

                    if (customerRead && merchantRead) {
                        return '已读';
                    } else if (customerRead && !merchantRead) {
                        return '客户已读';
                    } else if (!customerRead && merchantRead) {
                        return '商家已读';
                    } else {
                        return '未读';
                    }
                };

                // 批量标记会话消息为已读
                const markAllMessagesAsRead = async () => {
                    if (!currentSessionId.value) return;

                    try {
                        await axios.post('/plugin/Customersystem/api/markSessionMessagesRead', {
                            session_id: currentSessionId.value,
                            role_type: 'staff'
                        });

                        // 更新本地消息状态
                        currentMessages.value.forEach(msg => {
                            if (msg.role_type !== 'staff' && msg.sender_type !== 'staff') {
                                msg.staff_read = 1;
                                msg.staff_read_time = Math.floor(Date.now() / 1000);
                            }
                        });

                        // 静默标记已读，无需提示
                    } catch (error) {
                        // 静默处理错误，避免干扰用户
                    }
                };

                // 清空聊天记录
                const clearChatContent = async () => {
                    if (!currentSessionId.value) {
                        ElementPlus.ElMessage.warning('请先选择一个会话');
                        return;
                    }

                    try {
                        // 确认对话框
                        await ElementPlus.ElMessageBox.confirm(
                            '确定要清空当前会话的所有聊天记录吗？此操作不可恢复！',
                            '清空聊天记录',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning',
                                dangerouslyUseHTMLString: false
                            }
                        );

                        // 调用API清空聊天记录
                        const response = await axios.post('/plugin/Customersystem/api/clearChatMessages', {
                            session_id: currentSessionId.value
                        });

                        if (response.data.code === 200) {
                            // 清空本地消息列表
                            currentMessages.value = [];

                            // 更新会话列表中的最后消息
                            const sessionIndex = sessions.value.findIndex(s => s.id === currentSessionId.value);
                            if (sessionIndex !== -1) {
                                sessions.value[sessionIndex].last_message = '';
                                sessions.value[sessionIndex].last_time = Math.floor(Date.now() / 1000);
                            }

                            ElementPlus.ElMessage.success('聊天记录已清空');
                        } else {
                            ElementPlus.ElMessage.error(response.data.msg || '清空失败');
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            ElementPlus.ElMessage.error('清空聊天记录失败');
                        }
                    }
                };

                // 打开设置页面
                const openSettings = () => {
                    window.open('/plugin/Customersystem/api/settings', '_blank');
                };

                // 获取URL参数
                const getUrlParams = () => {
                    const urlParams = new URLSearchParams(window.location.search);
                    return {
                        session_id: urlParams.get('session_id')
                    };
                };

                // 初始化
                onMounted(() => {
                    const params = getUrlParams();

                    // 加载预设数据
                    loadPresetData();

                    // 加载会话列表
                    loadSessions().then(() => {
                        // 如果URL中有session_id参数，自动选择该会话
                        if (params.session_id) {
                            const targetSession = sessions.value.find(s => s.id == params.session_id);
                            if (targetSession) {
                                selectSession(targetSession);
                            }
                        } else if (sessions.value.length > 0) {
                            // 否则选择第一个会话
                            selectSession(sessions.value[0]);
                        }

                        // 启动实时消息轮询（在会话加载完成后启动）
                        startRealTimePolling();
                    });

                    // 点击外部关闭表情选择器和右键菜单
                    document.addEventListener('click', (event) => {
                        const emojiPicker = document.querySelector('.emoji-picker');
                        const emojiBtn = document.querySelector('.toolbar-btn');
                        if (emojiPicker && !emojiPicker.contains(event.target) && !emojiBtn?.contains(event.target)) {
                            showEmojiPicker.value = false;
                        }

                        // 点击其他地方隐藏右键菜单
                        const contextMenuEl = document.querySelector('.context-menu');
                        if (contextMenu.value.visible && contextMenuEl && !contextMenuEl.contains(event.target)) {
                            hideContextMenu();
                        }
                    });

                    // 监听键盘快捷键
                    document.addEventListener('keydown', (event) => {
                        if (event.key === 'Escape' && contextMenu.value.visible) {
                            hideContextMenu();
                        }

                        // Ctrl+C 复制当前选中的消息
                        if (event.ctrlKey && event.key === 'c' && contextMenu.value.visible && contextMenu.value.message) {
                            event.preventDefault();
                            copyMessage();
                        }
                    });

                    // 页面可见性变化时控制轮询
                    document.addEventListener('visibilitychange', () => {
                        if (document.hidden) {
                            // 页面隐藏时停止轮询以节省资源
                            isPollingActive.value = false;
                        } else {
                            // 页面显示时恢复轮询
                            isPollingActive.value = true;
                            // 立即检查一次新消息
                            checkForNewMessages();
                        }
                    });

                    // 页面卸载时清理定时器
                    window.addEventListener('beforeunload', () => {
                        stopRealTimePolling();
                    });
                });

                
                return {
                    sessions,
                    currentSessionId,
                    currentSession,
                    currentMessages,
                    inputMessage,
                    searchKeyword,
                    imagePreviewVisible,
                    previewImageUrl,
                    showSessionDetails,
                    ipLocation,
                    showEmojiPicker,
                    emojiList,
                    fileInput,
                    messageInput,
                    presetQuestions,
                    presetReplies,
                    // 快速回复面板相关
                    showQuickReplyPanel,
                    filteredSessions,
                    groupedMessages,
                    selectSession,
                    sendMessage,
                    formatTime,
                    formatMessageTime,
                    formatMessageDate,
                    formatSessionTime,
                    formatMessage,
                    getRoleDisplayName,
                    getSessionDisplayTitle,
                    isImageUrl,
                    previewImage,
                    getLocationFromIP,
                    toggleEmojiPicker,
                    insertEmoji,
                    selectImage,
                    handleFileSelect,
                    handlePaste,
                    canRecallMessage,
                    shouldShowRecallOption,
                    recallMessage,
                    // 右键菜单相关
                    contextMenu,
                    showContextMenu,
                    hideContextMenu,
                    copyMessage,
                    confirmRecallMessage,
                    loadPresetData,
                    usePresetReply,
                    usePresetQuestion,
                    // 快速回复面板控制函数
                    toggleQuickReplyPanel,
                    closeQuickReplyPanel,
                    // 设置页面
                    openSettings,
                    goBack,
                    toggleSessionDetails,
                    showContactInfo,
                    toggleSessionStatus,
                    // 实时消息轮询相关
                    startRealTimePolling,
                    stopRealTimePolling,
                    checkForNewMessages,
                    hasNewMessages,
                    isPollingActive,
                    // 已读状态相关
                    markMessageAsRead,
                    getReadStatusClass,
                    getReadStatusText,
                    markAllMessagesAsRead,
                    // 发送者信息
                    getSenderName,
                    getSenderType,
                    // 清空功能
                    clearChatContent
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
