<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <link href="/static/others/dist/css/style.css" rel="stylesheet">
    <title>商品图标设置</title>
    <style>
        /* 添加响应式样式 */
        @media screen and (max-width: 768px) {
            .el-form-item {
                margin-bottom: 15px;
            }
            .el-form {
                padding: 10px;
            }
            .el-form-item__label {
                width: auto !important;
                text-align: left;
                float: none;
                display: block;
                margin-bottom: 8px;
                padding: 0;
            }
            .el-form-item__content {
                margin-left: 0 !important;
            }
            .icon-item {
                margin: 15px 0 !important;
                padding: 15px 10px !important;
            }
            .preview-box {
                width: 150px !important;
                height: 150px !important;
            }
            .el-radio-group {
                display: flex;
                flex-direction: column;
            }
            .el-radio {
                margin: 5px 0;
            }
            .el-form-item-msg {
                font-size: 12px;
                line-height: 1.4;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <el-card shadow="never">
            <template #header>
                <div class="card-header">
                    <span>商品图标设置</span>
                </div>
            </template>
            <el-form :model="form" label-width="120px" class="form-container">
                <el-form-item label="图标开关：">
                    <el-switch v-model="form.status" :active-value="1" :inactive-value="0" />
                </el-form-item>

                <div v-for="(icon, index) in form.icons" :key="index" class="icon-item" style="margin-bottom: 20px; padding: 20px; border: 1px solid #EBEEF5; border-radius: 4px;">
                    <div class="icon-header" style="margin-bottom: 15px;">
                        <span>图标 {{index + 1}}</span>
                        <el-button type="danger" link @click="removeIcon(index)" style="float: right;">
                            删除
                        </el-button>
                    </div>

                    <el-form-item label="图标URL：">
                        <el-input v-model="icon.icon_url" placeholder="请输入图标URL地址">
                            <template #append>
                                <el-button @click="previewIcon(icon)">预览</el-button>
                            </template>
                        </el-input>
                        <div style="margin-top: 10px;">
                            <el-upload
                                action="/merchantApi/Upload/file"
                                :show-file-list="false"
                                :on-success="(res) => handleUploadSuccess(res, icon)"
                                :on-error="handleUploadError"
                                :before-upload="beforeUpload"
                                name="file"
                                accept="image/*">
                                <el-button type="primary">上传图标</el-button>
                            </el-upload>
                        </div>
                        <div class="el-form-item-msg">支持jpg、png、gif格式，大小不超过2MB</div>
                    </el-form-item>

                    <el-form-item label="匹配关键词：">
                        <el-input v-model="icon.keywords" 
                                  type="textarea" 
                                  :rows="2"
                                  placeholder="请输入商品名称关键词，多个关键词请用英文逗号分隔">
                        </el-input>
                        <div class="el-form-item-msg">
                            例如: 热销,新品,促销 (当商品名称包含这些关键词时会显示图标)
                        </div>
                    </el-form-item>

                    <el-form-item label="图标位置：">
                        <el-radio-group v-model="icon.position">
                            <el-radio label="right-top">右上角</el-radio>
                            <el-radio label="left-top">左上角</el-radio>
                            <el-radio label="right-bottom">右下角</el-radio>
                            <el-radio label="left-bottom">左下角</el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <el-form-item label="图标大小：">
                        <el-input-number 
                            v-model="icon.size" 
                            :min="20" 
                            :max="100" 
                            :step="5"
                            style="width: 180px">
                        </el-input-number>
                        <div class="el-form-item-msg">图标大小范围20-100像素</div>
                    </el-form-item>

                    <el-form-item label="预览效果：">
                        <div class="preview-box" style="width:200px;height:200px;border:1px solid #dcdfe6;position:relative;background:#f5f7fa;">
                            <div style="position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);color:#909399;font-size:14px;">
                                商品展示区域
                            </div>
                            <img :src="icon.icon_url" v-show="icon.icon_url" 
                                 :style="getPreviewStyle(icon)">
                        </div>
                        <div class="el-form-item-msg">预览区域显示实际大小效果</div>
                    </el-form-item>
                </div>

                <el-form-item>
                    <el-button type="primary" @click="addIcon" style="margin-right: 10px;">添加图标</el-button>
                    <el-button type="primary" :loading="loading" @click="save">保存设置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
    </div>

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/dist/index.js"></script>

    <script>
        const { createApp, ref, reactive, computed } = Vue;
        const { ElMessage } = ElementPlus;

        createApp({
            setup() {
                const loading = ref(false);
                const form = reactive({
                    status: 1,
                    icons: []
                });

                // 添加新图标
                const addIcon = () => {
                    form.icons.push({
                        icon_url: '',
                        keywords: '',
                        position: 'right-top',
                        size: 30
                    });
                };

                // 删除图标
                const removeIcon = (index) => {
                    form.icons.splice(index, 1);
                };

                // 获取预览样式
                const getPreviewStyle = (icon) => {
                    const style = {
                        position: 'absolute',
                        width: icon.size + 'px',
                        height: icon.size + 'px'
                    };

                    switch(icon.position) {
                        case 'right-top':
                            style.top = '5px';
                            style.right = '5px';
                            break;
                        case 'left-top':
                            style.top = '5px';
                            style.left = '5px';
                            break;
                        case 'right-bottom':
                            style.bottom = '5px';
                            style.right = '5px';
                            break;
                        case 'left-bottom':
                            style.bottom = '5px';
                            style.left = '5px';
                            break;
                    }
                    return style;
                };

                // 预览图标
                const previewIcon = (icon) => {
                    if (!icon.icon_url) {
                        ElMessage.warning('请先输入图标URL');
                        return;
                    }
                    window.open(icon.icon_url, '_blank');
                };

                // 获取数据
                const fetchData = async () => {
                    try {
                        const res = await axios.post("/plugin/Producticon/api/fetchConfig");
                        if (res.data?.code === 200) {
                            const data = res.data.data;
                            form.status = parseInt(data.status || 1);
                            // 确保icons是数组且至少有一个默认配置
                            if (!Array.isArray(data.icons) || data.icons.length === 0) {
                                form.icons = [{
                                    icon_url: '',
                                    keywords: '',
                                    position: 'right-top',
                                    size: 30
                                }];
                            } else {
                                form.icons = data.icons;
                            }
                        } else {
                            ElMessage.error(res.data?.msg || '获取数据失败');
                        }
                    } catch (error) {
                        console.error('获取数据失败:', error);
                        ElMessage.error('获取数据失败');
                    }
                };

                // 保存数据
                const save = async () => {
                    if (form.icons.length === 0) {
                        ElMessage.warning('请添加至少一个图标');
                        return;
                    }

                    loading.value = true;
                    try {
                        const res = await axios.post("/plugin/Producticon/api/save", form);
                        if (res.data?.code === 200) {
                            ElMessage.success('保存成功');
                            await fetchData();
                        } else {
                            ElMessage.error(res.data?.msg || '保存失败');
                        }
                    } catch (error) {
                        console.error('保存失败:', error);
                        ElMessage.error('保存失败');
                    } finally {
                        loading.value = false;
                    }
                };

                // 上传前验证
                const beforeUpload = (file) => {
                    const isImage = /^image\//.test(file.type);
                    if (!isImage) {
                        ElMessage.error('只能上传图片文件!');
                        return false;
                    }
                    const isLt2M = file.size / 1024 / 1024 < 2;
                    if (!isLt2M) {
                        ElMessage.error('图片大小不能超过 2MB!');
                        return false;
                    }
                    return true;
                };

                // 上传成功
                const handleUploadSuccess = (res, icon) => {
                    if (res.code === 1) {
                        icon.icon_url = res.data.url;
                        ElMessage.success('上传成功');
                    } else {
                        ElMessage.error(res.msg || '上传失败');
                    }
                };

                // 上传失败
                const handleUploadError = () => {
                    ElMessage.error('上传失败，请重试');
                };

                // 页面加载时获取数据
                fetchData();

                return {
                    loading,
                    form,
                    addIcon,
                    removeIcon,
                    getPreviewStyle,
                    previewIcon,
                    save,
                    beforeUpload,
                    handleUploadSuccess,
                    handleUploadError
                };
            }
        })
        .use(ElementPlus)
        .mount('#app');
    </script>
</body>
</html> 