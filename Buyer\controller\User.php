<?php

namespace plugin\Buyer\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Log;
use think\facade\Db;

class User extends BasePlugin {

    protected $scene = ['user'];  // 只允许普通用户访问
    
    protected $noNeedLogin = [];

    // 买家排行榜页面
    public function index() {
        return View::fetch();
    }

    // 获取买家排行榜数据
    public function getBuyerList() {
        try {
            // 检查功能和密码验证
            $configFile = app()->getRootPath() . 'plugin/Buyer/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            if (!($config['buyer_config']['status'] ?? true)) {
                return json(['code' => 403, 'msg' => '功能已关闭']);
            }
            
            if (($config['buyer_config']['need_password'] ?? false) && !session('buyer_rank_verified')) {
                return json(['code' => 401, 'msg' => '需要密码验证']);
            }

            $page = input('page/d', 1);
            $limit = input('limit/d', 10);
            $offset = ($page - 1) * $limit;
            $sort = input('sort/s', 'total_amount');
            $order = input('order/s', 'desc');
            $search = input('search/s', '');
            $dateRange = input('dateRange/a', []);

            // 首先获取所有与当前用户相关的商品ID（包括直接创建的和被其他商家对接的）
            $relatedGoodsIds = $this->getAllRelatedGoodsIds($this->user->id);

            // 获取当前用户自己的商品ID
            $myGoodsIds = Db::name('goods')
                ->where('user_id', $this->user->id)
                ->column('id');
            
            $myGoodsIdsStr = !empty($myGoodsIds) ? implode(',', $myGoodsIds) : '0';

            // 构建基础查询 - 基于所有相关商品ID
            $query = Db::name('order')
                ->alias('o')
                ->whereIn('o.goods_id', $relatedGoodsIds)
                ->where('o.status', 1);  // 只查询已完成订单

            // 添加时间范围筛选（如果有）
            if (!empty($dateRange) && count($dateRange) == 2) {
                $startDate = $dateRange[0] . ' 00:00:00';
                $endDate = $dateRange[1] . ' 23:59:59';
                $query->whereBetweenTime('o.create_time', $startDate, $endDate);
            }

            // 添加联系方式搜索（如果有）
            if (!empty($search)) {
                $query->where('o.contact', 'like', "%{$search}%");
            }

            // 分组统计，添加商家信息和自营/代理金额
            $query = $query->field([
                'o.contact',
                'o.user_id',
                'u.nickname',
                'COUNT(DISTINCT o.id) as total_orders',
                'SUM(JSON_LENGTH(l.cards)) as total_cards',
                'SUM(o.total_amount) as total_amount',
                "SUM(CASE WHEN o.goods_id IN ({$myGoodsIdsStr}) THEN o.total_amount ELSE 0 END) as self_amount",
                "SUM(CASE WHEN o.goods_id NOT IN ({$myGoodsIdsStr}) THEN o.total_amount ELSE 0 END) as agent_amount"
            ])
            ->join('order_card_log l', 'o.id = l.order_id', 'LEFT')
            ->join('user u', 'o.user_id = u.id', 'LEFT')
            ->group('o.contact');

            // 设置排序
            if (in_array($sort, ['total_orders', 'total_cards', 'total_amount', 'self_amount', 'agent_amount'])) {
                $query->order($sort, $order === 'ascending' ? 'asc' : 'desc');
            }

            // 获取总记录数和分页数据
            $total = Db::table($query->buildSql() . ' as temp')->count();
            $buyers = $query->limit($offset, $limit)->select()->toArray();

            // 处理数据格式
            foreach ($buyers as &$buyer) {
                // 保存原始联系方式，用于后续查询
                $buyer['raw_contact'] = $buyer['contact'];
                
                // 处理脱敏
                if ($config['buyer_config']['mask_contact'] ?? true) {
                    $buyer['contact'] = $this->maskContact($buyer['contact']);
                }
                
                // 确保数值类型正确
                $buyer['total_amount'] = floatval($buyer['total_amount']);
                $buyer['total_orders'] = intval($buyer['total_orders']);
                $buyer['total_cards'] = intval($buyer['total_cards']);
                $buyer['self_amount'] = floatval($buyer['self_amount']);
                $buyer['agent_amount'] = floatval($buyer['agent_amount']);
            }

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $buyers,
                'total' => $total
            ]);

        } catch (\Exception $e) {
            Log::error('获取买家排行榜失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取数据失败：' . $e->getMessage()]);
        }
    }

    /**
     * 递归获取所有与当前用户相关的商品ID
     * 包括当前用户创建的商品以及其他商家对接的商品
     */
    private function getAllRelatedGoodsIds($userId) {
        // 获取当前用户创建的所有商品
        $myGoodsIds = Db::name('goods')
            ->where('user_id', $userId)
            ->column('id');
        
        if (empty($myGoodsIds)) {
            return [];
        }

        // 找出所有对接了这些商品的下游商品（通过parent_id关联）
        $relatedIds = $myGoodsIds;
        $tempIds = $myGoodsIds;
        
        // 递归查找所有下游商品
        do {
            $nextLevelIds = Db::name('goods')
                ->whereIn('parent_id', $tempIds)
                ->column('id');
                
            if (empty($nextLevelIds)) {
                break;
            }
            
            $relatedIds = array_merge($relatedIds, $nextLevelIds);
            $tempIds = $nextLevelIds;
        } while (!empty($tempIds));
        
        return $relatedIds;
    }

    // 验证密码
    public function verifyPassword() {
        try {
            $password = input('password/s', '');
            $configFile = app()->getRootPath() . 'plugin/Buyer/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            if ($password === ($config['buyer_config']['password'] ?? '')) {
                session('buyer_rank_verified', true);
                return json(['code' => 200, 'msg' => '验证成功']);
            }
            
            return json(['code' => 403, 'msg' => '密码错误']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '验证失败：' . $e->getMessage()]);
        }
    }

    // 新增：检查验证状态
    public function checkVerified() {
        try {
            $configFile = app()->getRootPath() . 'plugin/Buyer/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            // 如果功能关闭
            if (!($config['buyer_config']['status'] ?? true)) {
                return json(['code' => 403, 'msg' => '功能已关闭']);
            }
            
            // 如果不需要密码或已验证
            if (!($config['buyer_config']['need_password'] ?? false) || session('buyer_rank_verified')) {
                return json(['code' => 200, 'msg' => '已验证']);
            }
            
            return json(['code' => 401, 'msg' => '需要密码验证']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '验证失败：' . $e->getMessage()]);
        }
    }

    /**
     * 联系方式脱敏处理
     */
    private function maskContact($contact) {
        if (empty($contact)) return '';
        
        $len = mb_strlen($contact);
        if ($len <= 2) return $contact;
        
        $showLen = floor($len / 3);
        $stars = str_repeat('*', $len - $showLen * 2);
        return mb_substr($contact, 0, $showLen) . $stars . mb_substr($contact, -$showLen);
    }
    
    /**
     * 获取特定联系方式的购买店铺记录
     */
    public function getContactStores() {
        try {
            // 检查功能和密码验证
            $configFile = app()->getRootPath() . 'plugin/Buyer/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            if (!($config['buyer_config']['status'] ?? true)) {
                return json(['code' => 403, 'msg' => '功能已关闭']);
            }
            
            if (($config['buyer_config']['need_password'] ?? false) && !session('buyer_rank_verified')) {
                return json(['code' => 401, 'msg' => '需要密码验证']);
            }

            // 获取原始联系方式（优先）或脱敏联系方式
            $rawContact = input('raw_contact/s', '');
            $contact = input('contact/s', '');
            $contactToUse = !empty($rawContact) ? $rawContact : $contact;
            
            if (empty($contactToUse)) {
                return json(['code' => 400, 'msg' => '联系方式不能为空']);
            }
            
            // 直接查询所有符合联系方式的订单，不限制于当前用户相关的商品
            // 这样可以找到所有店铺包括对接链中的所有相关店铺
            $ordersQuery = Db::name('order')
                ->alias('o')
                ->field([
                    'o.id', 'o.goods_id', 'o.goods_name', 'o.user_id', 'o.contact', 
                    'o.quantity', 'o.goods_price', 'o.total_amount', 'o.create_time',
                    'o.parent_id', 'o.parent_goods_id', 'o.parent_amount', 
                    'u.nickname as buyer_shop_name',  // 下单商家的昵称
                    'g.name as goods_real_name', 'g.user_id as goods_owner_id', 'g.parent_id as goods_parent_id',
                    'ou.nickname as goods_owner_name', // 商品所有者的昵称
                    'pg.name as parent_goods_name', 'pg.user_id as parent_goods_owner_id',
                    'pu.nickname as parent_goods_owner_name' // 上游商品所有者的昵称
                ])
                ->join('user u', 'o.user_id = u.id', 'LEFT') // 订单所属商家
                ->join('goods g', 'o.goods_id = g.id', 'LEFT') // 商品信息
                ->join('user ou', 'g.user_id = ou.id', 'LEFT') // 商品所有者
                ->join('goods pg', 'g.parent_id = pg.id', 'LEFT') // 上游商品信息
                ->join('user pu', 'pg.user_id = pu.id', 'LEFT') // 上游商品所有者
                ->where('o.contact', $contactToUse) // 使用原始联系方式
                ->where('o.status', 1)  // 只查询已完成订单
                ->order('o.create_time', 'desc');
            
            $orders = $ordersQuery->select()->toArray();
            
            // 获取所有与当前用户相关的商品ID，用于后续标记是否与当前用户相关
            $relatedGoodsIds = $this->getAllRelatedGoodsIds($this->user->id);
            
            // 处理数据
            $storeData = [];
            $storeMap = [];
            
            foreach ($orders as $order) {
                // 格式化数据
                $order['create_time'] = date('Y-m-d H:i:s', $order['create_time']);
                $order['total_amount'] = floatval($order['total_amount']);
                $order['parent_amount'] = floatval($order['parent_amount']);
                $order['goods_price'] = floatval($order['goods_price']);
                $order['quantity'] = intval($order['quantity']);
                
                // 判断商品链关系
                $hasParentGoods = !empty($order['goods_parent_id']);
                
                // 确定订单实际对应的店铺(优先使用商品所有者，因为可能是对接了别人的商品)
                $realShopId = $order['goods_owner_id'] ?? $order['user_id'];
                $realShopName = $order['goods_owner_name'] ?? $order['buyer_shop_name'];
                
                // 确定上游店铺信息(如果存在)
                $parentShopId = $hasParentGoods ? ($order['parent_goods_owner_id'] ?? 0) : 0;
                $parentShopName = $hasParentGoods ? ($order['parent_goods_owner_name'] ?? '未知') : '';
                
                // 标记是否与当前用户有关联
                $isRelatedToCurrentUser = in_array($order['goods_id'], $relatedGoodsIds);
                
                // 组织店铺数据结构(针对商品实际所属店铺)
                if (!isset($storeMap[$realShopId])) {
                    $storeMap[$realShopId] = count($storeData);
                    $storeData[] = [
                        'store_id' => $realShopId,
                        'store_name' => $realShopName,
                        'is_upstream' => false,  // 不是上游店铺
                        'has_parent' => $hasParentGoods,  // 是否有上游
                        'parent_id' => $parentShopId,     // 上游店铺ID
                        'parent_name' => $parentShopName, // 上游店铺名称
                        'is_related_to_user' => $isRelatedToCurrentUser, // 是否与当前用户有关
                        'orders' => [],
                        'total_amount' => 0,
                        'order_count' => 0
                    ];
                } else if (!$storeData[$storeMap[$realShopId]]['is_related_to_user'] && $isRelatedToCurrentUser) {
                    // 更新关联状态
                    $storeData[$storeMap[$realShopId]]['is_related_to_user'] = true;
                }
                
                // 添加订单到实际店铺
                $idx = $storeMap[$realShopId];
                $storeData[$idx]['orders'][] = [
                    'id' => $order['id'],
                    'goods_id' => $order['goods_id'],
                    'goods_name' => $order['goods_real_name'] ?? $order['goods_name'],
                    'shop_name' => $realShopName,
                    'quantity' => $order['quantity'],
                    'price' => $order['goods_price'],
                    'total_amount' => $order['total_amount'],
                    'create_time' => $order['create_time'],
                    'has_parent' => $hasParentGoods,
                    'parent_amount' => $order['parent_amount'],
                    'parent_shop_name' => $parentShopName,
                    'is_related_to_user' => $isRelatedToCurrentUser
                ];
                $storeData[$idx]['total_amount'] += $order['total_amount'];
                $storeData[$idx]['order_count']++;
                
                // 如果有上游店铺，也添加到上游店铺的记录中
                if ($hasParentGoods && $parentShopId > 0) {
                    // 检查上游店铺是否与当前用户相关
                    $isParentRelatedToUser = !empty($relatedGoodsIds) && 
                                             in_array($order['parent_goods_id'], $relatedGoodsIds);
                    
                    if (!isset($storeMap[$parentShopId])) {
                        $storeMap[$parentShopId] = count($storeData);
                        $storeData[] = [
                            'store_id' => $parentShopId,
                            'store_name' => $parentShopName,
                            'is_upstream' => true,   // 是上游店铺
                            'downstream_count' => 0, // 下游店铺数量
                            'orders' => [],
                            'total_amount' => 0,
                            'parent_amount' => 0,    // 上游获得的金额
                            'order_count' => 0,
                            'is_related_to_user' => $isParentRelatedToUser // 上游店铺是否与当前用户相关
                        ];
                    } else if (!$storeData[$storeMap[$parentShopId]]['is_related_to_user'] && $isParentRelatedToUser) {
                        // 更新关联状态
                        $storeData[$storeMap[$parentShopId]]['is_related_to_user'] = true;
                    }
                    
                    // 添加订单到上游店铺
                    $idx = $storeMap[$parentShopId];
                    $storeData[$idx]['orders'][] = [
                        'id' => $order['id'],
                        'goods_id' => $order['parent_goods_id'],
                        'goods_name' => $order['parent_goods_name'],
                        'shop_name' => $order['buyer_shop_name'], // 下单店铺名
                        'downstream_shop' => $realShopName,       // 下游店铺名
                        'quantity' => $order['quantity'],
                        'total_amount' => $order['total_amount'],  // 订单总金额
                        'parent_amount' => $order['parent_amount'], // 上游获得的金额
                        'create_time' => $order['create_time'],
                        'is_upstream_order' => true,
                        'is_related_to_user' => $isParentRelatedToUser
                    ];
                    $storeData[$idx]['total_amount'] += $order['total_amount'];
                    $storeData[$idx]['parent_amount'] += $order['parent_amount'];
                    $storeData[$idx]['order_count']++;
                    $storeData[$idx]['downstream_count'] = 1; // 至少有1个下游
                }
            }
            
            // 返回查询的联系方式（可能已脱敏）
            $displayContact = !empty($contact) ? $contact : $this->maskContact($contactToUse);
            
            // 按排序规则整理数据
            usort($storeData, function($a, $b) {
                // 首先按照是否与当前用户相关排序（相关的排在前面）
                if ($a['is_related_to_user'] !== $b['is_related_to_user']) {
                    return $a['is_related_to_user'] ? -1 : 1;
                }
                
                // 然后按照是否是上游店铺排序（上游店铺排在前面）
                if ($a['is_upstream'] !== $b['is_upstream']) {
                    return $a['is_upstream'] ? -1 : 1;
                }
                
                // 最后按总金额排序
                return $b['total_amount'] <=> $a['total_amount'];
            });
            
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $storeData,
                'total' => count($storeData),
                'display_contact' => $displayContact
            ]);
            
        } catch (\Exception $e) {
            Log::error('获取联系方式店铺记录失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取数据失败：' . $e->getMessage()]);
        }
    }
} 
