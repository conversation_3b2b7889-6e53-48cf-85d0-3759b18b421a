<?php

namespace plugin\Automaticloading\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;
use think\facade\Log;

class Api extends BasePlugin {

    protected $scene = ['admin'];
    
    protected $noNeedLogin = [];

    public function index() {
        return View::fetch();
    }

    // 获取配置
    public function getConfig() {
        try {
            $config = include app()->getRootPath() . 'plugin/Automaticloading/params.php';
            return json(['code' => 0, 'msg' => 'success', 'data' => $config]);
        } catch (\Exception $e) {
            Log::error('获取配置失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取配置失败']);
        }
    }

    // 获取商品表结构（用于调试）
    public function getGoodsTableInfo() {
        try {
            // 获取表前缀
            $prefix = config('database.connections.mysql.prefix');
            // 获取表结构
            $fields = Db::query("SHOW COLUMNS FROM {$prefix}goods");
            
            return json(['code' => 0, 'msg' => '成功', 'data' => $fields]);
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '获取表结构失败: ' . $e->getMessage()]);
        }
    }

    // 保存配置
    public function saveConfig() {
        try {
            $params = input('post.');
            $configFile = app()->getRootPath() . 'plugin/Automaticloading/params.php';
            
            // 如果文件不存在，创建默认配置
            if (!file_exists($configFile)) {
                $defaultConfig = [
                    'auto_loading_config' => [
                        'status' => 1,
                        'match_patterns' => [
                            '角色昵称' => '角色昵称[:|：](.*?)(?=\s|\n|$|\|)',
                            '等级' => '等级[:|：](.*?)(?=\s|\n|$|\|)',
                            '仓库价值' => '仓库价值[v|V|:|：](.*?)(?=\s|\n|$|\|)',
                            '道具价值' => '道具价值[:|：](.*?)(?=\s|\n|$|\|)',
                            '游戏区服' => '游戏区服[:|：](.*?)(?=\s|\n|$|\|)',
                            '今日登录' => '今日登录[:|：](.*?)(?=\s|\n|$|\|)',
                        ]
                    ]
                ];
                file_put_contents($configFile, '<?php return ' . var_export($defaultConfig, true) . ';');
            }
            
            // 获取现有配置
            $config = include $configFile;
            
            // 更新配置
            $config['auto_loading_config']['status'] = isset($params['status']) ? intval($params['status']) : 1;
            
            if (isset($params['match_patterns'])) {
                $config['auto_loading_config']['match_patterns'] = $params['match_patterns'];
            }
            
            // 保存配置
            file_put_contents($configFile, '<?php return ' . var_export($config, true) . ';');
            
            return json(['code' => 0, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            Log::error('保存配置失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    // 获取所有用户的数据处理历史记录
    public function getHistoryRecords() {
        try {
            $page = input('page/d', 1);
            $limit = input('limit/d', 10);
            $offset = ($page - 1) * $limit;
            $search = xss_safe(input('search/s', ''));
            $dateRange = input('dateRange/a', []);
            $userId = input('user_id/d', 0);
            
            // 收集所有分表的结果
            $allRecords = [];
            $totalCount = 0;
            
            // 查询所有可能的分表
            $tables = [];
            $tablePrefix = config('database.connections.mysql.prefix');
            $possibleTables = Db::query("SHOW TABLES LIKE '{$tablePrefix}goods_card_storage_%'");
            
            foreach ($possibleTables as $tableRow) {
                foreach ($tableRow as $tableName) {
                    $tables[] = str_replace($tablePrefix, '', $tableName);
                }
            }
            
            if (empty($tables)) {
                $tables[] = 'goods_card_storage_0';
            }
            
            // 对每个表进行查询并合并结果
            foreach ($tables as $tableName) {
                // 构建基础查询
                $query = Db::name($tableName)
                    ->alias('c')
                    ->join('goods g', 'c.goods_id = g.id')
                    ->join('user u', 'c.user_id = u.id');
                
                // 添加用户ID过滤
                if ($userId > 0) {
                    $query->where('c.user_id', $userId);
                }

                // 添加时间范围筛选
                if (!empty($dateRange) && count($dateRange) == 2) {
                    $startDate = strtotime($dateRange[0] . ' 00:00:00');
                    $endDate = strtotime($dateRange[1] . ' 23:59:59');
                    $query->whereBetweenTime('c.create_time', $startDate, $endDate);
                }

                // 添加卡密搜索
                if (!empty($search)) {
                    $query->where(function($q) use ($search) {
                        $q->where('c.secret', 'like', "%{$search}%")
                          ->whereOr('g.name', 'like', "%{$search}%")
                          ->whereOr('u.username', 'like', "%{$search}%");
                    });
                }

                // 先获取此表中的总记录数
                $tableTotal = $query->count();
                $totalCount += $tableTotal;
                
                // 如果此表中有数据，获取分页数据
                if ($tableTotal > 0) {
                    $tableRecords = $query->field([
                            'c.id',
                            'g.name as goods_name',
                            'g.image as goods_image',
                            'c.secret',
                            'c.create_time',
                            'c.status',
                            'c.goods_id',
                            'c.user_id',
                            'u.username as username'
                        ])
                        ->order('c.create_time', 'desc')
                        ->select()
                        ->toArray();
                    
                    $allRecords = array_merge($allRecords, $tableRecords);
                }
            }
            
            // 对合并后的结果进行排序（按创建时间降序）
            usort($allRecords, function($a, $b) {
                return $b['create_time'] - $a['create_time'];
            });
            
            // 手动分页
            $records = array_slice($allRecords, $offset, $limit);

            // 处理数据
            foreach ($records as &$record) {
                // 从secret中提取角色昵称
                $secretData = json_decode($record['secret'], true);
                $record['nickname'] = isset($secretData['角色昵称']) ? $secretData['角色昵称'] : '';
                
                // 提取仓库价值
                $record['warehouse_value'] = $this->extract_warehouse_value($secretData);
                if (is_float($record['warehouse_value']) && $record['warehouse_value'] >= 1000000) {
                    $record['warehouse_value'] = round($record['warehouse_value'] / 1000000, 2) . 'M';
                }
                
                // 格式化时间
                $record['create_time'] = date('Y-m-d H:i:s', $record['create_time']);
                
                // 状态文本
                $record['status_text'] = $record['status'] == 0 ? '未使用' : '已使用';
                
                // XSS防护
                $record['goods_name'] = xss_safe($record['goods_name']);
                $record['nickname'] = xss_safe($record['nickname']);
                $record['username'] = xss_safe($record['username']);
            }

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $records,
                'total' => $totalCount
            ]);
            
        } catch (\Exception $e) {
            Log::error('获取历史记录失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取数据失败：' . $e->getMessage()]);
        }
    }

    // 获取系统中的商品列表
    public function getGoodsList() {
        try {
            $page = input('page/d', 1);
            $limit = input('limit/d', 10);
            $search = xss_safe(input('search/s', ''));
            
            $query = Db::name('goods')->alias('g');
            
            if (!empty($search)) {
                $query->where('g.name', 'like', "%{$search}%");
            }
            
            $total = $query->count();
            
            $goods = $query->field([
                'g.id', 
                'g.name', 
                'g.image', 
                'g.price', 
                'g.status',
                'g.create_time'
            ])
            ->order('g.id', 'desc')
            ->page($page, $limit)
            ->select()
            ->toArray();
            
            foreach ($goods as &$item) {
                $item['create_time'] = date('Y-m-d H:i:s', $item['create_time']);
                $item['status_text'] = $item['status'] == 1 ? '上架' : '下架';
            }
            
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $goods,
                'total' => $total
            ]);
            
        } catch (\Exception $e) {
            Log::error('获取商品列表失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取数据失败：' . $e->getMessage()]);
        }
    }

    // 获取处理数据统计
    public function getStatistics() {
        try {
            // 获取今日处理数据总量
            $today = strtotime(date('Y-m-d'));
            $todayCount = 0;
            
            // 获取总处理数据量
            $totalCount = 0;
            
            // 获取未使用数据量
            $unusedCount = 0;
            
            // 获取已使用数据量
            $usedCount = 0;
            
            // 查询所有可能的分表
            $tables = [];
            $tablePrefix = config('database.connections.mysql.prefix');
            $possibleTables = Db::query("SHOW TABLES LIKE '{$tablePrefix}goods_card_storage_%'");
            
            foreach ($possibleTables as $tableRow) {
                foreach ($tableRow as $tableName) {
                    $tables[] = str_replace($tablePrefix, '', $tableName);
                }
            }
            
            if (empty($tables)) {
                $tables[] = 'goods_card_storage_0';
            }
            
            // 对每个表进行统计
            foreach ($tables as $tableName) {
                // 今日新增
                $todayCount += Db::name($tableName)->where('create_time', '>=', $today)->count();
                
                // 总量
                $tableTotal = Db::name($tableName)->count();
                $totalCount += $tableTotal;
                
                // 未使用
                $unusedCount += Db::name($tableName)->where('status', 0)->count();
                
                // 已使用
                $usedCount += Db::name($tableName)->where('status', 1)->count();
            }
            
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => [
                    'today' => $todayCount,
                    'total' => $totalCount,
                    'unused' => $unusedCount,
                    'used' => $usedCount
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('获取统计数据失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取数据失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 从解析后的数据中提取仓库价值
     */
    private function extract_warehouse_value($data) {
        // 尝试从"仓库价值v"中提取数值
        if (isset($data['仓库价值v'])) {
            $value = $data['仓库价值v'];
            // 如果格式是 "0.7M" 这种形式
            if (preg_match('/([0-9\.]+)M/i', $value, $matches)) {
                return floatval($matches[1]) * 1000000; // 转换为数值
            }
            return floatval($value);
        }
        
        // 尝试从"道具价值"获取
        if (isset($data['道具价值'])) {
            return floatval($data['道具价值']);
        }
        
        return false;
    }
    
    /**
     * 获取文件上传配置
     */
    public function getUploadConfig() {
        // 获取当前配置
        $config = include app()->getRootPath() . 'plugin/Automaticloading/params.php';
        
        // 如果没有上传配置，添加默认配置
        if (!isset($config['upload_config'])) {
            $config['upload_config'] = [
                'allowed_extensions' => ['txt'],
                'max_size' => 2048, // 2MB
                'mime_types' => [
                    'text/plain'
                ]
            ];
            
            // 保存配置
            file_put_contents(
                app()->getRootPath() . 'plugin/Automaticloading/params.php', 
                '<?php return ' . var_export($config, true) . ';'
            );
        }
        
        return json([
            'code' => 200, 
            'msg' => '获取成功', 
            'data' => $config['upload_config']
        ]);
    }
    
    /**
     * 更新文件上传配置
     */
    public function updateUploadConfig() {
        $params = input('post.');
        $configFile = app()->getRootPath() . 'plugin/Automaticloading/params.php';
        
        // 获取现有配置
        $config = include $configFile;
        
        // 更新上传配置
        if (isset($params['allowed_extensions'])) {
            $config['upload_config']['allowed_extensions'] = $params['allowed_extensions'];
        }
        
        if (isset($params['max_size'])) {
            $config['upload_config']['max_size'] = intval($params['max_size']);
        }
        
        if (isset($params['mime_types'])) {
            $config['upload_config']['mime_types'] = $params['mime_types'];
        }
        
        // 保存配置
        file_put_contents($configFile, '<?php return ' . var_export($config, true) . ';');
        
        return json(['code' => 200, 'msg' => '保存成功']);
    }
    
    /**
     * 文件上传处理
     */
    public function uploadFile() {
        // 获取配置
        $config = include app()->getRootPath() . 'plugin/Automaticloading/params.php';
        $uploadConfig = isset($config['upload_config']) ? $config['upload_config'] : [
            'allowed_extensions' => ['txt'],
            'max_size' => 2048,
            'mime_types' => ['text/plain']
        ];
        
        // 获取上传文件
        $file = request()->file('file');
        if (!$file) {
            return json(['code' => 400, 'msg' => '未检测到上传文件']);
        }
        
        // 验证文件 - ThinkPHP 8.0.3兼容性修改
        // 获取文件名和大小
        $originalName = $file->getOriginalName();
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        $size = $file->getSize();
        $mimeType = $file->getMime();
        
        // 检查扩展名
        if (!in_array(strtolower($extension), $uploadConfig['allowed_extensions'])) {
            return json(['code' => 400, 'msg' => '不支持的文件类型，仅支持: ' . implode(', ', $uploadConfig['allowed_extensions'])]);
        }
        
        // 检查大小
        if ($size > $uploadConfig['max_size'] * 1024) {
            return json(['code' => 400, 'msg' => '文件超过最大限制: ' . ($uploadConfig['max_size'] / 1024) . 'MB']);
        }
        
        try {
            // 上传路径
            $savename = \think\facade\Filesystem::disk('public')->putFile('uploads', $file);
            
            if ($savename) {
                $url = request()->domain() . '/storage/' . $savename;
                return json(['code' => 200, 'msg' => '上传成功', 'data' => ['url' => $url, 'path' => $savename]]);
            } else {
                return json(['code' => 500, 'msg' => '上传失败']);
            }
        } catch (\Exception $e) {
            Log::error('文件上传失败: ' . $e->getMessage());
            return json(['code' => 500, 'msg' => '上传失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 获取用户列表
     */
    public function getUserList() {
        try {
            $page = input('page/d', 1);
            $limit = input('limit/d', 10);
            $search = xss_safe(input('search/s', ''));
            
            $query = Db::name('user')->alias('u');
            
            if (!empty($search)) {
                $query->where('u.username|u.email|u.mobile', 'like', "%{$search}%");
            }
            
            $total = $query->count();
            
            $users = $query->field([
                'u.id', 
                'u.username', 
                'u.mobile', 
                'u.avatar', 
                'u.email',
                'u.status',
                'u.create_time'
            ])
            ->order('u.id', 'desc')
            ->page($page, $limit)
            ->select()
            ->toArray();
            
            foreach ($users as &$user) {
                $user['create_time'] = date('Y-m-d H:i:s', $user['create_time']);
                $user['status_text'] = $user['status'] ? '正常' : '禁用';
            }
            
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $users,
                'total' => $total
            ]);
            
        } catch (\Exception $e) {
            Log::error('获取用户列表失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取数据失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取数据列表
     */
    public function getData() {
        try {
            $page = input('page/d', 1);
            $limit = input('limit/d', 10);
            $search = xss_safe(input('search/s', ''));
            
            // 收集所有分表的结果
            $allRecords = [];
            $totalCount = 0;
            
            // 查询所有可能的分表
            $tables = [];
            $tablePrefix = config('database.connections.mysql.prefix');
            $possibleTables = Db::query("SHOW TABLES LIKE '{$tablePrefix}goods_card_storage_%'");
            
            foreach ($possibleTables as $tableRow) {
                foreach ($tableRow as $tableName) {
                    $tables[] = str_replace($tablePrefix, '', $tableName);
                }
            }
            
            if (empty($tables)) {
                $tables[] = 'goods_card_storage_0';
            }
            
            // 对每个表进行查询并合并结果
            foreach ($tables as $tableName) {
                // 构建基础查询
                $query = Db::name($tableName)
                    ->alias('c')
                    ->join('goods g', 'c.goods_id = g.id')
                    ->join('user u', 'c.user_id = u.id');
                
                // 添加时间范围筛选
                if (!empty($search)) {
                    $query->where(function($q) use ($search) {
                        $q->where('c.secret', 'like', "%{$search}%")
                          ->whereOr('g.name', 'like', "%{$search}%")
                          ->whereOr('u.username', 'like', "%{$search}%");
                    });
                }

                // 先获取此表中的总记录数
                $tableTotal = $query->count();
                $totalCount += $tableTotal;
                
                // 如果此表中有数据，获取分页数据
                if ($tableTotal > 0) {
                    $tableRecords = $query->field([
                            'c.id',
                            'g.name as goods_name',
                            'g.image as goods_image',
                            'c.secret',
                            'c.create_time',
                            'c.status',
                            'c.goods_id',
                            'c.user_id',
                            'u.username as username'
                        ])
                        ->order('c.create_time', 'desc')
                        ->select()
                        ->toArray();
                    
                    $allRecords = array_merge($allRecords, $tableRecords);
                }
            }
            
            // 对合并后的结果进行排序（按创建时间降序）
            usort($allRecords, function($a, $b) {
                return $b['create_time'] - $a['create_time'];
            });
            
            // 手动分页
            $offset = ($page - 1) * $limit;
            $records = array_slice($allRecords, $offset, $limit);

            // 处理数据
            foreach ($records as &$record) {
                // 根据 secret 解析内容
                $secretData = json_decode($record['secret'], true);
                $content = isset($secretData['内容']) ? $secretData['内容'] : '';
                $record['content'] = $content;
                
                // 格式化时间
                $record['create_time'] = date('Y-m-d H:i:s', $record['create_time']);
            }

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $records,
                'total' => $totalCount,
                'totalData' => $totalCount
            ]);
            
        } catch (\Exception $e) {
            Log::error('获取自动上货数据失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取数据失败：' . $e->getMessage()]);
        }
    }
} 