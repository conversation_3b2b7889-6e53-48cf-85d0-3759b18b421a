-- 抽奖插件数据库表结构
-- 创建时间: 2024-06-22
-- 版本: 1.0.0

-- 1. 抽奖配置表
CREATE TABLE IF NOT EXISTS `pt_plugin_lottery_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `config_key` varchar(50) NOT NULL COMMENT '配置键',
    `config_value` text COMMENT '配置值',
    `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='抽奖配置表';

-- 2. 奖品表
CREATE TABLE IF NOT EXISTS `pt_plugin_lottery_prizes` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL COMMENT '奖品名称',
    `type` varchar(20) NOT NULL COMMENT '奖品类型',
    `probability` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '中奖概率',
    `stock` int(11) NOT NULL DEFAULT '0' COMMENT '库存数量',
    `balance_amount` decimal(10,2) DEFAULT '0.00' COMMENT '余额金额',
    `description` text COMMENT '奖品描述',
    `image` varchar(255) DEFAULT NULL COMMENT '奖品图片',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 1启用 0禁用',
    `sort` int(11) DEFAULT '0' COMMENT '排序',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `status` (`status`),
    KEY `type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='抽奖奖品表';

-- 3. 抽奖记录表
CREATE TABLE IF NOT EXISTS `pt_plugin_lottery_records` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL COMMENT '用户ID',
    `merchant_id` int(11) NOT NULL COMMENT '商户ID',
    `prize_id` int(11) NOT NULL COMMENT '奖品ID',
    `prize_name` varchar(100) NOT NULL COMMENT '奖品名称',
    `prize_type` varchar(20) NOT NULL COMMENT '奖品类型',
    `balance_amount` decimal(10,2) DEFAULT '0.00' COMMENT '余额金额',
    `shipped` tinyint(1) DEFAULT '0' COMMENT '是否已发货',
    `ship_time` datetime DEFAULT NULL COMMENT '发货时间',
    `balance_sent` tinyint(1) DEFAULT '0' COMMENT '余额是否已发放',
    `auto_sent` tinyint(1) DEFAULT '0' COMMENT '是否自动发放',
    `is_virtual` tinyint(1) DEFAULT '0' COMMENT '是否虚拟记录',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `user_id` (`user_id`),
    KEY `merchant_id` (`merchant_id`),
    KEY `prize_id` (`prize_id`),
    KEY `create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='抽奖记录表';

-- 4. 商户抽奖限制表
CREATE TABLE IF NOT EXISTS `pt_plugin_lottery_merchant_limits` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `merchant_id` int(11) NOT NULL COMMENT '商户ID',
    `daily_limit` int(11) NOT NULL DEFAULT '3' COMMENT '每日抽奖次数限制',
    `used_count` int(11) NOT NULL DEFAULT '0' COMMENT '已使用次数',
    `last_reset_date` date NOT NULL COMMENT '最后重置日期',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `merchant_id` (`merchant_id`),
    KEY `last_reset_date` (`last_reset_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商户抽奖限制表';

-- 5. 流水规则表
CREATE TABLE IF NOT EXISTS `pt_plugin_lottery_turnover_rules` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `turnover_amount` decimal(10,2) NOT NULL COMMENT '流水金额',
    `draw_times` int(11) NOT NULL COMMENT '获得抽奖次数',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 1启用 0禁用',
    `sort` int(11) DEFAULT '0' COMMENT '排序',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `status` (`status`),
    KEY `turnover_amount` (`turnover_amount`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流水规则表';

-- 6. 流水领取记录表
CREATE TABLE IF NOT EXISTS `pt_plugin_lottery_turnover_claims` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `merchant_id` int(11) NOT NULL COMMENT '商户ID',
    `rule_id` int(11) NOT NULL COMMENT '规则ID',
    `claim_date` date NOT NULL COMMENT '领取日期',
    `draw_times_used` int(11) DEFAULT '0' COMMENT '已使用的抽奖次数',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `merchant_rule_date` (`merchant_id`, `rule_id`, `claim_date`),
    KEY `merchant_id` (`merchant_id`),
    KEY `claim_date` (`claim_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流水领取记录表';

-- 7. 奖品类型表
CREATE TABLE IF NOT EXISTS `pt_plugin_lottery_prize_types` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `value` varchar(20) NOT NULL COMMENT '类型值',
    `label` varchar(50) NOT NULL COMMENT '类型标签',
    `tag_type` varchar(20) DEFAULT 'info' COMMENT '标签类型',
    `tag_color` varchar(20) DEFAULT NULL COMMENT '自定义标签颜色',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 1启用 0禁用',
    `sort` int(11) DEFAULT '0' COMMENT '排序',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `value` (`value`),
    KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='奖品类型表';
