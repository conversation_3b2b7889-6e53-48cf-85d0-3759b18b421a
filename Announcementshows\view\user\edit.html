<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" />
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>广告内容编辑</title>
    <!-- 先加载Vue -->
    <script src="/static/others/vue/vue.global.prod.js"></script>
    <!-- 再加载Element Plus -->
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>
    <!-- 最后加载图标 -->
    <script src="/assets/plugin/Announcementshows/index.iife.min.js"></script>
    <!-- 预加载广告管理脚本 -->
    
    <style>
        body {
            background-color: #f5f7fa;
            margin: 0;
            padding: 0;
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
        }
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 15px;
            box-sizing: border-box;
        }
        .header {
            margin-bottom: 20px;
            padding: 10px 0;
            border-bottom: 1px solid #ebeef5;
        }
        .header h2 {
            margin: 0;
            color: #303133;
            font-weight: 600;
        }
        .preview-container {
            margin: 20px 0;
            padding: 15px;
            border: 1px dashed #dcdfe6;
            border-radius: 4px;
            background: #fff;
        }
        .preview-item {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .ad-card {
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.3s;
        }
        .ad-card:hover {
            box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.2);
            transform: translateY(-2px);
        }
        .ad-card-header {
            padding: 18px 20px;
            border-bottom: 1px solid #ebeef5;
            background: #f5f7fa;
        }
        .ad-card-body {
            padding: 20px;
            background: #fff;
        }
        .ad-upload {
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            text-align: center;
            padding: 20px;
            transition: all 0.3s;
        }
        .ad-upload:hover {
            border-color: #409EFF;
        }
        .uploaded-image {
            max-width: 100%;
            object-fit: contain;
            max-height: 100%;
            border-radius: 4px;
        }
        .form-tip {
            font-size: 12px;
            color: #909399;
            margin-top: 5px;
        }
        .status-tag {
            margin-left: 10px;
        }
        .action-footer {
            margin-top: 20px;
            display: flex;
            justify-content: flex-end;
        }
        .expired-icon {
            margin-left: 5px;
            color: #f56c6c;
        }
        .expiry-info {
            font-size: 13px;
            color: #606266;
            margin-top: 10px;
        }
        .remaining-days {
            color: #67c23a;
            font-weight: bold;
        }
        .color-preview {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 2px;
            vertical-align: middle;
            margin-left: 5px;
            border: 1px solid #dcdfe6;
        }
        .gradient-preview {
            display: inline-block;
            width: 60px;
            height: 20px;
            border-radius: 2px;
            vertical-align: middle;
            margin-left: 5px;
            border: 1px solid #dcdfe6;
        }
        .gradient-controls {
            margin-top: 10px;
        }
        .gradient-direction-selector {
            display: flex;
            gap: 10px;
            margin-top: 8px;
            flex-wrap: wrap;
        }
        .gradient-direction-item {
            display: flex;
            align-items: center;
            gap: 5px;
            padding: 5px 10px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
            background: #fff;
        }
        .gradient-direction-item:hover {
            border-color: #409eff;
            background: #ecf5ff;
        }
        .gradient-direction-item.active {
            border-color: #409eff;
            background: #409eff;
            color: #fff;
        }
        .gradient-direction-preview {
            width: 20px;
            height: 12px;
            border-radius: 2px;
            border: 1px solid rgba(0,0,0,0.1);
        }
        .image-close-btn {
            position: absolute;
            top: -10px;
            right: -10px;
            width: 24px;
            height: 24px;
            line-height: 20px;
            text-align: center;
            background: #f56c6c;
            color: white;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            z-index: 1;
        }
        .image-close-btn:hover {
            background: #f78989;
        }
        
        /* 移动端适配样式 */
        @media (max-width: 768px) {
            .container {
                padding: 0 10px;
                margin: 10px auto;
            }
            
            .ad-card-header {
                padding: 12px 15px;
            }
            
            .ad-card-body {
                padding: 15px;
            }
            
            .el-form-item {
                margin-bottom: 15px;
            }
            
            .el-form-item__label {
                padding: 0 0 8px;
                line-height: 1.2;
                text-align: left !important;
                float: none;
                display: block;
                width: 100% !important;
            }
            
            .el-form-item__content {
                margin-left: 0 !important;
            }
            
            .el-upload {
                width: 100%;
            }
            
            .ad-upload {
                padding: 10px;
            }
            
            .el-button {
                padding: 8px 15px;
                font-size: 14px;
            }
            
            .el-button + .el-button {
                margin-left: 5px;
            }
            
            .el-button-group {
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between;
                width: 100%;
            }
            
            .el-button-group .el-button {
                margin-bottom: 10px;
                flex: 1 1 auto;
                min-width: 120px;
                margin-left: 5px;
                margin-right: 5px;
            }
            
            .expiry-info {
                text-align: left;
                margin-top: 5px;
                margin-bottom: 10px;
            }
            
            .el-timeline-item__wrapper {
                padding-left: 18px;
            }
            
            .el-timeline-item__timestamp {
                font-size: 12px;
            }
            
            /* 标题的移动端样式 */
            .ad-card-header h3 {
                font-size: 16px;
                display: flex;
                flex-wrap: wrap;
                align-items: center;
            }
            
            .status-tag {
                margin-left: 5px;
                margin-top: 5px;
                font-size: 12px;
            }
        }
    </style>
</head>

<body>
    <div id="app">
        <div class="container">
            <el-card>
                <template #header>
                    <div class="card-header">
                        <span>我的广告位管理</span>
                    </div>
                </template>

                <el-empty
                    v-if="myAds.length === 0"
                    description="您还没有租用任何广告位"
                >
                </el-empty>

                <template v-else>
                    <el-timeline>
                        <el-timeline-item
                            v-for="(ad, index) in myAds"
                            :key="index"
                            :type="isExpired(ad) ? 'danger' : 'success'"
                            :timestamp="formatDate(ad.end_time)"
                        >
                            <el-card class="ad-card">
                                <template #header>
                                    <div class="ad-card-header">
                                        <el-row :gutter="20" type="flex" align="middle">
                                            <el-col :xs="24" :sm="12">
                                                <h3 style="margin: 0;">
                                                    广告位 #{{ ad.position }}
                                                    <el-tag class="status-tag" :type="isExpired(ad) ? 'danger' : 'success'">
                                                        {{ isExpired(ad) ? '已过期' : '使用中' }}
                                                    </el-tag>
                                                </h3>
                                            </el-col>
                                            <el-col :xs="24" :sm="12" style="text-align: right;">
                                                <div class="expiry-info">
                                                    <template v-if="!isExpired(ad)">
                                                        剩余时间: 
                                                        <el-tag type="success" effect="plain">
                                                            {{ getRemainingDays(ad.end_time) }} 天
                                                        </el-tag>
                                                    </template>
                                                </div>
                                            </el-col>
                                        </el-row>
                                    </div>
                                </template>

                                <el-form :model="ad" label-width="100px" label-position="top">
                                    <template v-if="isImageMode">
                                        <el-form-item label="广告图片">
                                            <el-upload
                                                class="ad-upload"
                                                action="/adminApi/Upload/file"
                                                :on-success="(res) => handleUploadSuccess(res, index)"
                                                :on-error="handleUploadError"
                                                :before-upload="beforeUpload"
                                                accept="image/*"
                                                :disabled="isExpired(ad)"
                                                :show-file-list="false"
                                                drag
                                            >
                                                <template v-if="ad.content && ad.content.startsWith('http')">
                                                    <el-image
                                                        :src="ad.content"
                                                        :style="{ height: config.height + 'px' }"
                                                        fit="contain"
                                                        :preview-src-list="[ad.content]"
                                                    >
                                                        <template #error>
                                                            <div class="image-error">
                                                                <i class="el-icon-picture"></i>
                                                                <span>图片加载失败</span>
                                                            </div>
                                                        </template>
                                                    </el-image>
                                                    <el-button
                                                        v-if="!isExpired(ad)"
                                                        type="danger"
                                                        circle
                                                        class="image-delete-btn"
                                                        @click.stop="clearImage(index)"
                                                    >
                                                        <i class="el-icon-delete"></i>
                                                    </el-button>
                                                </template>
                                                <template v-else>
                                                    <i class="el-icon-upload"></i>
                                                    <div class="el-upload__text">
                                                        <span>点击上传图片</span>
                                                        <span v-if="!isExpired(ad)" style="color: #a3a6ad; display: block; margin-top: 8px; font-size: 12px;">
                                                            或拖拽图片到此处
                                                        </span>
                                                    </div>
                                                    <el-button v-if="!isExpired(ad)" type="primary">选择图片</el-button>
                                                </template>
                                                <div class="form-tip" v-if="!isExpired(ad)">
                                                    <p>建议尺寸 {{ getRecommendedSize() }}</p>
                                                    <p>支持JPG、PNG格式，图片大小不超过2MB</p>
                                                </div>
                                            </el-upload>
                                        </el-form-item>
                                    </template>

                                    <template v-else>
                                        <el-form-item label="文字内容">
                                            <el-input
                                                v-model="ad.content"
                                                type="textarea"
                                                :rows="4"
                                                placeholder="请输入广告文字内容"
                                                :maxlength="500"
                                                show-word-limit
                                                :disabled="isExpired(ad)"
                                            ></el-input>
                                            <div class="form-tip">支持简单HTML标签，如&lt;br&gt;、&lt;b&gt;、&lt;i&gt;等</div>
                                        </el-form-item>
                                        <el-row :gutter="20">
                                            <el-col :xs="24" :sm="12">
                                                <el-form-item label="文字设置">
                                                    <el-switch
                                                        v-model="ad.useTextGradient"
                                                        :disabled="isExpired(ad)"
                                                        active-text="使用渐变文字"
                                                        inactive-text="使用纯色文字"
                                                        style="margin-bottom: 10px;"
                                                    ></el-switch>

                                                    <template v-if="!ad.useTextGradient">
                                                        <div>
                                                            <el-color-picker v-model="ad.textColor" :disabled="isExpired(ad)"></el-color-picker>
                                                            <span class="color-preview" :style="{backgroundColor: ad.textColor}"></span>
                                                        </div>
                                                    </template>

                                                    <template v-else>
                                                        <div class="gradient-controls">
                                                            <el-row :gutter="10">
                                                                <el-col :span="12">
                                                                    <div style="margin-bottom: 8px;">起始颜色</div>
                                                                    <el-color-picker v-model="ad.textGradientStartColor" :disabled="isExpired(ad)"></el-color-picker>
                                                                </el-col>
                                                                <el-col :span="12">
                                                                    <div style="margin-bottom: 8px;">结束颜色</div>
                                                                    <el-color-picker v-model="ad.textGradientEndColor" :disabled="isExpired(ad)"></el-color-picker>
                                                                </el-col>
                                                            </el-row>

                                                            <div style="margin-top: 10px;">
                                                                <div style="margin-bottom: 8px;">渐变方向</div>
                                                                <div class="gradient-direction-selector">
                                                                    <div
                                                                        v-for="direction in gradientDirections"
                                                                        :key="'text-' + direction.value"
                                                                        class="gradient-direction-item"
                                                                        :class="{ active: ad.textGradientDirection === direction.value }"
                                                                        @click="!isExpired(ad) && (ad.textGradientDirection = direction.value)"
                                                                    >
                                                                        <div
                                                                            class="gradient-direction-preview"
                                                                            :style="{ background: getDirectionPreview(direction.value, ad.textGradientStartColor, ad.textGradientEndColor) }"
                                                                        ></div>
                                                                        <span>{{ direction.label }}</span>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <div style="margin-top: 10px;">
                                                                <div style="margin-bottom: 8px;">文字预览效果</div>
                                                                <div
                                                                    style="
                                                                        font-size: 16px;
                                                                        font-weight: bold;
                                                                        padding: 10px;
                                                                        text-align: center;
                                                                        border-radius: 4px;
                                                                        border: 1px solid #dcdfe6;
                                                                        min-height: 40px;
                                                                        display: flex;
                                                                        align-items: center;
                                                                        justify-content: center;
                                                                        position: relative;
                                                                        overflow: hidden;
                                                                    "
                                                                    :style="ad.useTextGradient ? {
                                                                        background: ad.useGradient ? getGradientStyle(ad) : '#f5f5f5',
                                                                        color: 'transparent'
                                                                    } : {
                                                                        background: ad.useGradient ? getGradientStyle(ad) : '#f5f5f5',
                                                                        color: ad.textColor || '#333333'
                                                                    }"
                                                                >
                                                                    <span v-if="ad.useTextGradient"
                                                                          :style="{
                                                                              background: getTextGradientStyle(ad),
                                                                              WebkitBackgroundClip: 'text',
                                                                              WebkitTextFillColor: 'transparent',
                                                                              backgroundClip: 'text',
                                                                              color: 'transparent',
                                                                              position: 'absolute',
                                                                              top: '50%',
                                                                              left: '50%',
                                                                              transform: 'translate(-50%, -50%)',
                                                                              whiteSpace: 'nowrap'
                                                                          }">
                                                                        广告文字预览
                                                                    </span>
                                                                    <span v-else>广告文字预览</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </template>
                                                </el-form-item>
                                            </el-col>
                                            <el-col :xs="24" :sm="12">
                                                <el-form-item label="背景设置">
                                                    <el-switch
                                                        v-model="ad.useGradient"
                                                        :disabled="isExpired(ad)"
                                                        active-text="使用渐变背景"
                                                        inactive-text="使用纯色背景"
                                                        style="margin-bottom: 10px;"
                                                    ></el-switch>

                                                    <template v-if="!ad.useGradient">
                                                        <div>
                                                            <el-color-picker v-model="ad.backgroundColor" :disabled="isExpired(ad)"></el-color-picker>
                                                            <span class="color-preview" :style="{backgroundColor: ad.backgroundColor}"></span>
                                                        </div>
                                                    </template>

                                                    <template v-else>
                                                        <div class="gradient-controls">
                                                            <el-row :gutter="10">
                                                                <el-col :span="12">
                                                                    <div style="margin-bottom: 8px;">起始颜色</div>
                                                                    <el-color-picker v-model="ad.gradientStartColor" :disabled="isExpired(ad)"></el-color-picker>
                                                                </el-col>
                                                                <el-col :span="12">
                                                                    <div style="margin-bottom: 8px;">结束颜色</div>
                                                                    <el-color-picker v-model="ad.gradientEndColor" :disabled="isExpired(ad)"></el-color-picker>
                                                                </el-col>
                                                            </el-row>

                                                            <div style="margin-top: 10px;">
                                                                <div style="margin-bottom: 8px;">渐变方向</div>
                                                                <div class="gradient-direction-selector">
                                                                    <div
                                                                        v-for="direction in gradientDirections"
                                                                        :key="direction.value"
                                                                        class="gradient-direction-item"
                                                                        :class="{ active: ad.gradientDirection === direction.value }"
                                                                        @click="!isExpired(ad) && (ad.gradientDirection = direction.value)"
                                                                    >
                                                                        <div
                                                                            class="gradient-direction-preview"
                                                                            :style="{ background: getDirectionPreview(direction.value, ad.gradientStartColor, ad.gradientEndColor) }"
                                                                        ></div>
                                                                        <span>{{ direction.label }}</span>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <div style="margin-top: 10px;">
                                                                <div style="margin-bottom: 8px;">预览效果</div>
                                                                <div
                                                                    class="gradient-preview"
                                                                    :style="{ background: getGradientStyle(ad) }"
                                                                ></div>
                                                            </div>
                                                        </div>
                                                    </template>
                                                </el-form-item>
                                            </el-col>
                                        </el-row>
                                    </template>

                                    <el-form-item label="跳转链接">
                                        <el-input
                                            v-model="ad.link"
                                            placeholder="请输入点击广告后跳转的链接，例如：https://www.example.com"
                                            :disabled="isExpired(ad)"
                                        >
                                            <template #append>
                                                <el-button @click="previewLinkClick(ad.link)">
                                                    <i class="el-icon-link"></i>
                                                </el-button>
                                            </template>
                                        </el-input>
                                    </el-form-item>

                                    <el-form-item>
                                        <div class="mobile-button-group">
                                            <el-button 
                                                v-if="isExpired(ad)" 
                                                type="danger" 
                                                @click="showRenewDialog(ad.position)"
                                                style="width: 100%; margin-bottom: 10px;"
                                            >
                                                <i class="el-icon-refresh"></i>
                                                续费广告位
                                            </el-button>
                                            <el-button 
                                                v-else
                                                type="warning" 
                                                @click="showRenewDialog(ad.position)"
                                                style="width: 100%; margin-bottom: 10px;"
                                            >
                                                <i class="el-icon-refresh"></i>
                                                提前续费
                                            </el-button>
                                            <el-button 
                                                type="primary" 
                                                @click="saveAd(index)" 
                                                :disabled="isExpired(ad)"
                                                style="width: 100%;"
                                            >
                                                <i class="el-icon-check"></i>
                                                保存修改
                                            </el-button>
                                        </div>
                                    </el-form-item>
                                </el-form>
                            </el-card>
                        </el-timeline-item>
                    </el-timeline>
                </template>
            </el-card>
        </div>
    </div>

    <script>
        const { createApp, ref, reactive, computed, onMounted, h } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;
        
        // 使用Element Plus内置图标
        const icons = {
            UploadFilled: 'el-icon-upload',
            Delete: 'el-icon-delete',
            Picture: 'el-icon-picture',
            Link: 'el-icon-link',
            Refresh: 'el-icon-refresh',
            Check: 'el-icon-check'
        };

        createApp({
            setup() {
                const loading = ref(false);
                const myAds = ref([]);
                const config = reactive({
                    height: 60,
                    padding: 15,
                    background: '#ffffff',
                    text_color: '#333333',
                    display_mode: 0,
                    auto_scroll: 1,
                    scroll_interval: 5000
                });

                // 渐变方向选项
                const gradientDirections = ref([
                    { value: 'to right', label: '左到右' },
                    { value: 'to left', label: '右到左' },
                    { value: 'to bottom', label: '上到下' },
                    { value: 'to top', label: '下到上' },
                    { value: 'to bottom right', label: '左上到右下' },
                    { value: 'to bottom left', label: '右上到左下' },
                    { value: 'to top right', label: '左下到右上' },
                    { value: 'to top left', label: '右下到左上' }
                ]);
                
                const isImageMode = computed(() => {
                    if (typeof config.display_mode === 'number') {
                        return config.display_mode === 1;
                    } else if (typeof config.display_mode === 'string') {
                        return config.display_mode === '1';
                    }
                    return false;
                });

                const isMobileDevice = computed(() => {
                    return window.innerWidth < 768;
                });

                const fetchMyAds = async () => {
                    loading.value = true;
                    try {
                        // 尝试更多的API路径
                        const apiPaths = [
                            "/plugin/Announcementshows/User/getMyAds",
                            "/plugins/Announcementshows/User/getMyAds",
                            "/Announcementshows/User/getMyAds",
                            "/addons/Announcementshows/User/getMyAds",
                            "/static/plugins/Announcementshows/User/getMyAds",
                            "/static/Announcementshows/User/getMyAds",
                            "/Announcementshows/User/getMyAds",
                            "/plugin/Announcementshows/User/getMyAds",
                            "/plugins/Announcementshows/User/getMyAds"
                        ];
                        
                        let response = null;
                        let success = false;
                        let lastError = null;
                        
                        for (const path of apiPaths) {
                            try {
                                const result = await axios.post(path, {}, {
                                    timeout: 5000, // 设置超时时间
                                    headers: {
                                        'X-Requested-With': 'XMLHttpRequest'
                                    }
                                });
                                
                                if (result.data?.code === 200) {
                                    response = result.data;
                                    success = true;
                                    break;
                                } else {
                                    lastError = result.data?.msg || '未知错误';
                                }
                            } catch (err) {
                                lastError = err.message;
                            }
                        }
                        
                        if (success && response) {
                            const { ads, config: configData } = response.data;
                            
                            if (!ads || !Array.isArray(ads)) {
                                ElMessage.error('数据格式错误，请刷新页面重试');
                                return;
                            }
                            
                            Object.assign(config, {
                                height: parseInt(configData?.height) || 60,
                                padding: parseInt(configData?.padding) || 15,
                                background: configData?.background || '#ffffff',
                                text_color: configData?.text_color || '#333333',
                                display_mode: parseInt(configData?.display_mode) || 0,
                                auto_scroll: parseInt(configData?.auto_scroll) || 1,
                                scroll_interval: parseInt(configData?.scroll_interval) || 5000
                            });
                            
                            // 确保广告数据有默认值
                            myAds.value = ads.map(ad => ({
                                ...ad,
                                textColor: ad.textColor || '#000000',
                                backgroundColor: ad.backgroundColor || '#ffffff',
                                useGradient: ad.useGradient || false,
                                gradientStartColor: ad.gradientStartColor || '#409eff',
                                gradientEndColor: ad.gradientEndColor || '#67c23a',
                                gradientDirection: ad.gradientDirection || 'to right',
                                useTextGradient: ad.useTextGradient || false,
                                textGradientStartColor: ad.textGradientStartColor || '#f56c6c',
                                textGradientEndColor: ad.textGradientEndColor || '#e6a23c',
                                textGradientDirection: ad.textGradientDirection || 'to right',
                                content: ad.content || '',
                                link: ad.link || '',
                                position: ad.position || '',
                                end_time: ad.end_time || null
                            }));
                        } else {
                            ElMessage.error(`获取广告数据失败: ${lastError || '请检查网络连接'}`);
                        }
                    } catch (error) {
                        ElMessage.error('网络错误，请稍后重试');
                    } finally {
                        loading.value = false;
                    }
                };

                const saveAd = async (index) => {
                    const ad = myAds.value[index];
                    if (isExpired(ad)) {
                        ElMessage.warning('广告位已过期，请先续费');
                        return;
                    }

                    loading.value = true;
                    try {
                        const postData = {
                            position: ad.position,
                            content: ad.content,
                            is_image: isImageMode.value,
                            link: ad.link
                        };

                        if (!isImageMode.value) {
                            postData.textColor = ad.textColor || '#000000';
                            postData.backgroundColor = ad.backgroundColor || '#ffffff';
                            postData.useGradient = ad.useGradient || false;
                            postData.gradientStartColor = ad.gradientStartColor || '#409eff';
                            postData.gradientEndColor = ad.gradientEndColor || '#67c23a';
                            postData.gradientDirection = ad.gradientDirection || 'to right';
                            postData.useTextGradient = ad.useTextGradient || false;
                            postData.textGradientStartColor = ad.textGradientStartColor || '#f56c6c';
                            postData.textGradientEndColor = ad.textGradientEndColor || '#e6a23c';
                            postData.textGradientDirection = ad.textGradientDirection || 'to right';
                        }

                        // 尝试多个可能的API路径
                        const apiPaths = [
                            "/plugin/Announcementshows/User/editAdContent",
                            "/plugins/Announcementshows/User/editAdContent",
                            "/Announcementshows/User/editAdContent"
                        ];
                        
                        let response = null;
                        let success = false;
                        
                        for (const path of apiPaths) {
                            try {
                                const result = await axios.post(path, postData);
                                if (result.data?.code === 200) {
                                    response = result.data;
                                    success = true;
                                    break;
                                }
                            } catch (err) {
                                // 静默处理错误
                            }
                        }

                        if (success && response) {
                            ElMessage.success('广告内容保存成功');
                        } else {
                            ElMessage.error(response?.msg || '保存失败');
                        }
                    } catch (error) {
                        ElMessage.error('网络错误，请稍后重试');
                    } finally {
                        loading.value = false;
                    }
                };
                
                const handleUploadSuccess = (response, index) => {
                    if (response.code === 200) {
                        const oldContent = myAds.value[index].content;
                        myAds.value[index].content = response.data.url;
                        
                        // 提交到服务器保存更新
                        const ad = myAds.value[index];
                        axios.post("/plugin/Announcementshows/User/editAdContent", {
                            position: ad.position,
                            content: response.data.url,
                            is_image: true,
                            link: ad.link
                        }).then(result => {
                            if (result.data.code === 200) {
                                ElMessage.success('图片上传成功并已保存');
                            } else {
                                // 回滚本地数据
                                myAds.value[index].content = oldContent;
                                ElMessage.error(result.data.msg || '图片上传成功但保存失败');
                            }
                        }).catch(error => {
                            // 回滚本地数据
                            myAds.value[index].content = oldContent;
                            ElMessage.error('网络错误，图片上传成功但保存失败');
                        });
                    } else {
                        ElMessage.error(response.msg || '图片上传失败');
                    }
                };

                const handleUploadError = (error) => {
                    ElMessage.error('图片上传失败，请检查网络连接');
                };

                const beforeUpload = (file) => {
                    // 验证文件类型
                    const isImage = file.type.startsWith('image/');
                    if (!isImage) {
                        ElMessage.error('只能上传图片文件！');
                        return false;
                    }

                    // 验证文件大小（限制为2MB）
                    const isLt2M = file.size / 1024 / 1024 < 2;
                    if (!isLt2M) {
                        ElMessage.error('图片大小不能超过2MB！');
                        return false;
                    }

                    return true;
                };

                const formatDate = (dateString) => {
                    if (!dateString) return '永久';
                    const date = new Date(dateString);
                    return date.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                };

                const getRemainingDays = (dateString) => {
                    if (!dateString) return '∞';
                    const endDate = new Date(dateString);
                    const now = new Date();
                    const diffTime = endDate - now;
                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                    return diffDays > 0 ? diffDays : 0;
                };

                const isExpired = (ad) => {
                    if (!ad || !ad.end_time) return false;
                    const endDate = new Date(ad.end_time);
                    const now = new Date();
                    return endDate < now;
                };

                const showRenewDialog = (position) => {
                    try {
                        // 检查是否可以使用全局函数
                        if (typeof window.announcementShowsRentDialog === 'function') {
                            window.announcementShowsRentDialog(position, true); // 第二个参数true表示这是续费操作
                            return;
                        }
                        
                        // 如果全局函数不存在，创建内联的续费对话框
                        createInlineRenewDialog(position);
                    } catch (error) {
                        ElMessage.error('续费功能加载失败，请刷新页面重试');
                    }
                };
                
                // 内联实现的续费对话框
                const createInlineRenewDialog = async (position) => {
                    try {
                        // 首先获取价格配置
                        const { data: priceResponse } = await axios.post("/plugin/Announcementshows/User/getPriceConfig");
                        if (!priceResponse || priceResponse.code !== 200) {
                            throw new Error('获取价格配置失败');
                        }
                        
                        const priceConfig = priceResponse.data;
                        
                        // 使用Element Plus对话框
                        ElMessageBox({
                            title: '续费广告位',
                            message: h('div', null, [
                                h('p', null, `您正在为广告位 #${position} 续费`),
                                h('div', { class: 'renew-options' }, [
                                    h('select', { 
                                        id: 'renewal-duration',
                                        style: 'width: 100%; padding: 8px; margin-bottom: 15px; border-radius: 4px; border: 1px solid #dcdfe6;'
                                    }, [
                                        h('option', { value: '30' }, `月费 - ¥${priceConfig.renew_month_price || '19.90'}`),
                                        h('option', { value: '90' }, `季费 - ¥${priceConfig.renew_quarter_price || '49.90'}`),
                                        h('option', { value: '180' }, `半年费 - ¥${priceConfig.renew_halfyear_price || '89.90'}`),
                                        h('option', { value: '365' }, `年费 - ¥${priceConfig.renew_year_price || '169.90'}`)
                                    ]),
                                    h('p', null, [
                                        '账户余额: ',
                                        h('span', { style: 'font-weight: bold; color: #409EFF;' }, 
                                            `¥${priceConfig.user_money ? parseFloat(priceConfig.user_money).toFixed(2) : '加载中...'}`
                                        )
                                    ])
                                ])
                            ]),
                            showCancelButton: true,
                            confirmButtonText: '确认续费',
                            cancelButtonText: '取消',
                            beforeClose: (action, instance, done) => {
                                if (action === 'confirm') {
                                    instance.confirmButtonLoading = true;
                                    instance.confirmButtonText = '处理中...';
                                    
                                    // 获取选择的时长
                                    const durationSelect = document.getElementById('renewal-duration');
                                    const duration = durationSelect ? durationSelect.value : '30';
                                    
                                    // 获取对应的价格
                                    let price = 0;
                                    switch (parseInt(duration)) {
                                        case 30:
                                            price = priceConfig.renew_month_price || 19.9;
                                            break;
                                        case 90:
                                            price = priceConfig.renew_quarter_price || 49.9;
                                            break;
                                        case 180:
                                            price = priceConfig.renew_halfyear_price || 89.9;
                                            break;
                                        case 365:
                                            price = priceConfig.renew_year_price || 169.9;
                                            break;
                                    }
                                    
                                    // 提交续费请求
                                    axios.post("/plugin/Announcementshows/User/openAdSpace", {
                                        position: position,
                                        duration: duration,
                                        price: price,
                                        is_renew: true
                                    }).then(response => {
                                        instance.confirmButtonLoading = false;
                                        done();
                                        if (response.data.code === 200) {
                                            ElMessage.success('续费成功！');
                                            // 刷新数据而不是重载页面
                                            fetchMyAds();
                                        } else {
                                            ElMessage.error(response.data.msg || '续费失败');
                                        }
                                    }).catch(error => {
                                        instance.confirmButtonLoading = false;
                                        done();
                                        ElMessage.error('续费请求失败，请稍后重试');
                                    });
                                } else {
                                    done();
                                }
                            }
                        }).then(action => {
                            // 用户点击确认按钮后的处理
                        }).catch(() => {
                            // 用户取消操作
                        });
                        
                    } catch (error) {
                        ElMessage.error('无法加载续费功能，请刷新页面重试');
                    }
                };

                const getRecommendedSize = () => {
                    // 根据当前高度提供建议尺寸
                    const height = config.height;
                    const ratio = 3; // 建议宽高比
                    return `${height * ratio}×${height}px`;
                };

                const clearImage = (index) => {
                    const ad = myAds.value[index];
                    if (isExpired(ad)) {
                        ElMessage.warning('广告位已过期，无法修改');
                        return;
                    }
                    
                    // 先在本地设置为null
                    myAds.value[index].content = null;
                    
                    // 向服务器提交更新
                    axios.post("/plugin/Announcementshows/User/editAdContent", {
                        position: ad.position,
                        content: null,
                        is_image: isImageMode.value,
                        link: ad.link
                    }).then(response => {
                        if (response.data.code === 200) {
                            ElMessage.success('图片删除成功');
                        } else {
                            // 如果服务器更新失败，回滚本地数据
                            ElMessage.error(response.data.msg || '图片删除失败');
                            fetchMyAds(); // 重新加载数据
                        }
                    }).catch(error => {
                        ElMessage.error('网络错误，图片删除失败');
                        fetchMyAds(); // 重新加载数据
                    });
                };

                const handleImageLoadError = (index, event) => {
                    ElMessage.error(`广告位 #${myAds.value[index].position} 的图片加载失败，请检查图片URL是否正确`);
                };

                const handleImageLoadSuccess = (index) => {
                    // 图片加载成功不需要任何操作
                };

                const previewLinkClick = (link) => {
                    // 这里可以添加打开新窗口或新标签页的逻辑
                };

                const goToMarket = () => {
                    // 跳转到广告位市场
                    window.location.href = '/plugin/Announcementshows/User/market';
                };

                // 获取渐变样式
                const getGradientStyle = (ad) => {
                    if (!ad.useGradient) {
                        return ad.backgroundColor || '#ffffff';
                    }

                    const startColor = ad.gradientStartColor || '#409eff';
                    const endColor = ad.gradientEndColor || '#67c23a';
                    const direction = ad.gradientDirection || 'to right';

                    return `linear-gradient(${direction}, ${startColor}, ${endColor})`;
                };

                // 获取方向预览样式
                const getDirectionPreview = (direction, startColor, endColor) => {
                    const start = startColor || '#409eff';
                    const end = endColor || '#67c23a';
                    return `linear-gradient(${direction}, ${start}, ${end})`;
                };

                // 获取文字渐变样式
                const getTextGradientStyle = (ad) => {
                    if (!ad.useTextGradient) {
                        return ad.textColor || '#000000';
                    }

                    const startColor = ad.textGradientStartColor || '#f56c6c';
                    const endColor = ad.textGradientEndColor || '#e6a23c';
                    const direction = ad.textGradientDirection || 'to right';

                    return `linear-gradient(${direction}, ${startColor}, ${endColor})`;
                };

                onMounted(() => {
                    fetchMyAds();
                    
                    // 添加消息监听，用于从外部接收刷新数据的请求
                    window.addEventListener('message', (event) => {
                        if (event.data && event.data.type === 'REFRESH_ADS_DATA') {
                            fetchMyAds();
                            
                            if (event.data.position && myAds.value.length > 0) {
                                const targetIndex = myAds.value.findIndex(ad => ad.position === event.data.position);
                                if (targetIndex >= 0) {
                                    setTimeout(() => {
                                        const adElement = document.querySelector(`.ad-card:nth-child(${targetIndex + 1})`);
                                        if (adElement) {
                                            adElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
                                        }
                                    }, 300);
                                }
                            }
                        }
                    });
                    
                    // 监听窗口大小变化，适配移动端
                    window.addEventListener('resize', () => {
                        // 可以在这里添加一些动态的样式调整
                    });
                });

                return {
                    loading,
                    myAds,
                    config,
                    gradientDirections,
                    isImageMode,
                    isMobileDevice,
                    saveAd,
                    handleUploadSuccess,
                    handleUploadError,
                    beforeUpload,
                    formatDate,
                    getRemainingDays,
                    isExpired,
                    showRenewDialog,
                    getRecommendedSize,
                    clearImage,
                    handleImageLoadError,
                    handleImageLoadSuccess,
                    previewLinkClick,
                    goToMarket,
                    getGradientStyle,
                    getDirectionPreview,
                    getTextGradientStyle
                };
            }
        })
        .use(ElementPlus, {
            locale: ElementPlusLocaleZhCn
        })
        .mount('#app');
    </script>
</body>
</html> 