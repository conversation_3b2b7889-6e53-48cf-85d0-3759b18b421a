<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡密回收站</title>
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <style>
        body {
            margin: 0;
            background: #f5f7fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
            height: 100vh;
            overflow: hidden;
        }

        .main-container {
            height: 650px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
        }

        .cards-table {
            background: #fff;
            border-radius: 8px;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .filter-container {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .pagination-container {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            padding-bottom: 10px;
        }

        .el-pagination {
            justify-content: center !important;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="main-container">
            <div class="cards-table">
                <!-- 搜索和筛选区域 -->
                <div class="filter-container">
                    <el-row :gutter="20" style="margin-bottom: 20px;">
                        <el-col :span="8">
                            <el-input
                                v-model="searchKey"
                                placeholder="搜索卡密"
                                clearable
                                @keyup.enter="handleSearch"
                                @clear="handleSearch">
                                <template #append>
                                    <el-button @click="handleSearch">
                                        <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="16" height="16">
                                            <path fill="currentColor" d="M795.904 750.72l124.992 124.928a32 32 0 01-45.248 45.248L750.656 795.904a416 416 0 1145.248-45.248zM480 832a352 352 0 100-704 352 352 0 000 704z"/>
                                        </svg>
                                    </el-button>
                                </template>
                            </el-input>
                        </el-col>
                        <el-col :span="12">
                            <el-date-picker
                                v-model="dateRange"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="删除开始日期"
                                end-placeholder="删除结束日期"
                                value-format="YYYY-MM-DD"
                                :shortcuts="dateShortcuts"
                                @change="handleDateChange">
                            </el-date-picker>
                        </el-col>
                        <el-col :span="4">
                            <el-button type="danger" @click="handleCleanup" :loading="cleanupLoading">清空回收站</el-button>
                        </el-col>
                    </el-row>
                </div>

                <!-- 表格区域 -->
                <div class="table-container">
                    <el-table 
                        :data="cards" 
                        style="width: 100%" 
                        v-loading="loading"
                        @selection-change="handleSelectionChange">
                        <el-table-column
                            type="selection"
                            width="20">
                        </el-table-column>
                        <el-table-column 
                            prop="goods_name" 
                            label="商品名称" 
                            min-width="200"
                            align="center">
                            <template #default="scope">
                                <div style="display: flex; align-items: center; justify-content: center;">
                                    <el-image 
                                        style="width: 40px; height: 40px; margin-right: 10px;"
                                        :src="scope.row.goods_image"
                                        :preview-src-list="[scope.row.goods_image]"
                                        fit="cover">
                                        <template #error>
                                            <div class="image-slot">
                                                <el-icon><picture-filled /></el-icon>
                                            </div>
                                        </template>
                                    </el-image>
                                    <span>{{ scope.row.goods_name }}</span>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column 
                            prop="card_info" 
                            label="卡密" 
                            min-width="200"
                            align="center">
                        </el-table-column>
                        <el-table-column 
                            prop="delete_time" 
                            label="删除时间" 
                            min-width="180"
                            align="center">
                            <template #default="scope">
                                {{ formatTime(scope.row.delete_time) }}
                            </template>
                        </el-table-column>
                        <el-table-column 
                            prop="status_text" 
                            label="状态" 
                            min-width="100"
                            align="center">
                        </el-table-column>
                        <el-table-column 
                            label="操作" 
                            width="120"
                            align="center">
                            <template #default="scope">
                                <el-button
                                    type="danger"
                                    size="small"
                                    @click="handleDelete(scope.row)">
                                    删除
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 添加批量操作按钮 -->
                    <div style="margin-top: 20px;">
                        <el-button 
                            type="danger" 
                            :disabled="selectedCards.length === 0"
                            @click="handleBatchDelete">
                            批量删除
                        </el-button>
                    </div>
                </div>

                <!-- 分页区域 -->
                <div class="pagination-container">
                    <el-pagination
                        v-model:current-page="currentPage"
                        v-model:page-size="pageSize"
                        :page-sizes="[10]"
                        layout="total, prev, pager, next"
                        :total="total"
                        @current-change="handleCurrentChange">
                    </el-pagination>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>

    <script>
        const { createApp, ref, onMounted } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        createApp({
            setup() {
                const cards = ref([]);
                const currentPage = ref(1);
                const pageSize = ref(10);
                const total = ref(0);
                const loading = ref(false);
                const cleanupLoading = ref(false);
                const searchKey = ref('');
                const dateRange = ref([]);
                const selectedCards = ref([]);

                // 添加日期快捷选项
                const dateShortcuts = [
                    {
                        text: '最近一周',
                        value: () => {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                            return [start, end];
                        },
                    },
                    {
                        text: '最近一个月',
                        value: () => {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                            return [start, end];
                        },
                    },
                    {
                        text: '最近三个月',
                        value: () => {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                            return [start, end];
                        },
                    }
                ];

                // 加载数据
                const loadData = async () => {
                    try {
                        loading.value = true;
                        const res = await axios.get('/plugin/Merchantcleanup/user/getRecycledCards', {
                            params: {
                                page: currentPage.value,
                                limit: pageSize.value,
                                search: searchKey.value,
                                dateRange: dateRange.value || []
                            }
                        });

                        if (res.data.code === 200) {
                            cards.value = res.data.data;
                            total.value = res.data.total;
                        } else {
                            ElMessage.error(res.data.msg);
                        }
                    } catch (err) {
                        console.error('加载数据失败:', err);
                        ElMessage.error('数据加载失败');
                    } finally {
                        loading.value = false;
                    }
                };

                // 删除单个卡密
                const handleDelete = async (row) => {
                    try {
                        await ElMessageBox.confirm(
                            '确定要删除这个卡密吗？此操作不可恢复！',
                            '警告',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning',
                            }
                        );

                        const res = await axios.post('/plugin/Merchantcleanup/user/deleteCard', {
                            id: row.id,
                            goods_id: row.goods_id
                        });
                        
                        if (res.data.code === 200) {
                            ElMessage.success('删除成功');
                            loadData();
                        } else {
                            ElMessage.error(res.data.msg);
                        }
                    } catch (err) {
                        if (err !== 'cancel') {
                            console.error('删除失败:', err);
                            ElMessage.error('删除失败');
                        }
                    }
                };

                // 清空回收站
                const handleCleanup = async () => {
                    try {
                        await ElMessageBox.confirm(
                            '确定要清空回收站吗？此操作不可恢复！',
                            '警告',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning',
                            }
                        );

                        cleanupLoading.value = true;
                        const res = await axios.post('/plugin/Merchantcleanup/user/cleanupRecycled');
                        
                        if (res.data.code === 200) {
                            ElMessage.success(res.data.msg);
                            loadData();
                        } else {
                            ElMessage.error(res.data.msg);
                        }
                    } catch (err) {
                        if (err !== 'cancel') {
                            console.error('清空失败:', err);
                            ElMessage.error('清空失败');
                        }
                    } finally {
                        cleanupLoading.value = false;
                    }
                };

                const handleCurrentChange = (val) => {
                    currentPage.value = val;
                    loadData();
                };

                const handleSearch = () => {
                    currentPage.value = 1;
                    loadData();
                };

                const handleDateChange = () => {
                    currentPage.value = 1;
                    loadData();
                };

                // 添加时间格式化方法
                const formatTime = (timestamp) => {
                    if (!timestamp) return '';
                    const date = new Date(timestamp);
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    const hours = String(date.getHours()).padStart(2, '0');
                    const minutes = String(date.getMinutes()).padStart(2, '0');
                    const seconds = String(date.getSeconds()).padStart(2, '0');
                    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
                };

                // 选择变化处理
                const handleSelectionChange = (selection) => {
                    selectedCards.value = selection;
                };

                // 批量删除处理
                const handleBatchDelete = async () => {
                    if (selectedCards.value.length === 0) {
                        return;
                    }

                    try {
                        await ElMessageBox.confirm(
                            `确定要删除选中的 ${selectedCards.value.length} 个卡密吗？此操作不可恢复！`,
                            '警告',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning',
                            }
                        );

                        const res = await axios.post('/plugin/Merchantcleanup/user/batchDeleteCards', {
                            cardsData: selectedCards.value
                        });
                        
                        if (res.data.code === 200) {
                            ElMessage.success(res.data.msg);
                            loadData();
                        } else {
                            ElMessage.error(res.data.msg);
                        }
                    } catch (err) {
                        if (err !== 'cancel') {
                            console.error('批量删除失败:', err);
                            ElMessage.error('批量删除失败');
                        }
                    }
                };

                onMounted(() => {
                    loadData();
                });

                return {
                    cards,
                    currentPage,
                    pageSize,
                    total,
                    loading,
                    cleanupLoading,
                    searchKey,
                    dateRange,
                    handleCurrentChange,
                    handleSearch,
                    handleDateChange,
                    handleCleanup,
                    handleDelete,
                    formatTime,
                    dateShortcuts,
                    selectedCards,
                    handleSelectionChange,
                    handleBatchDelete,
                };
            }
        }).use(ElementPlus, {
            locale: ElementPlusLocaleZhCn
        }).mount('#app');
    </script>
</body>
</html>
