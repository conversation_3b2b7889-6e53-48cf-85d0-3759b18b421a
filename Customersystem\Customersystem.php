<?php

namespace plugin\Customersystem;

use app\common\library\Plugin;
use think\facade\Db;
use think\facade\Env;
use think\facade\Log;

class Customersystem extends Plugin {

    public function install() {
        // 使用Env获取数据库前缀
        $prefix = Env::get('DB_PREFIX');
        $tableContacts = $prefix . 'plugin_chat_contacts';
        $tableSessions = $prefix . 'plugin_chat_sessions';
        $tableMessages = $prefix . 'plugin_chat_messages';
        $tableSettings = $prefix . 'plugin_chat_settings';
        $tableJoinRequests = $prefix . 'plugin_chat_join_requests';
        $tableParticipants = $prefix . 'plugin_chat_session_participants';
        $tableSuppliers = $prefix . 'plugin_suppliers';
        $tablePresetReplies = $prefix . 'plugin_chat_preset_replies';
        $tableCustomerSettings = $prefix . 'plugin_chat_customer_settings';



        // 使用heredoc语法定义SQL - 聊天联系人表
        $sqlContacts = <<<SQL
CREATE TABLE IF NOT EXISTS `{$tableContacts}` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `name` varchar(100) NOT NULL COMMENT '姓名',
    `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
    `phone` varchar(20) DEFAULT NULL COMMENT '电话',
    `qq` varchar(20) DEFAULT NULL COMMENT 'QQ号码',
    `wechat` varchar(50) DEFAULT NULL COMMENT '微信号',
    `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
    `status` enum('active','blocked') NOT NULL DEFAULT 'active' COMMENT '状态',
    `last_active_time` int(11) DEFAULT NULL COMMENT '最后活跃时间',
    `tags` varchar(255) DEFAULT NULL COMMENT '标签',
    `notes` text COMMENT '客服备注信息',
    `create_time` int(11) NOT NULL COMMENT '创建时间',
    `update_time` int(11) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_user_id` (`user_id`),
    KEY `idx_email` (`email`),
    KEY `idx_phone` (`phone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='聊天联系人表';
SQL;

        // 使用heredoc语法定义SQL - 聊天会话表
        $sqlSessions = <<<SQL
CREATE TABLE IF NOT EXISTS `{$tableSessions}` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `contact_id` int(11) NOT NULL COMMENT '联系人ID',
    `staff_id` int(11) DEFAULT NULL COMMENT '客服ID',
    `title` varchar(255) NOT NULL COMMENT '会话标题',
    `status` enum('open','closed') NOT NULL DEFAULT 'open' COMMENT '会话状态',
    `source` enum('customer','merchant') DEFAULT 'customer' COMMENT '会话类型：customer=联系客服，merchant=联系商家',
    `merchant_id` int(11) DEFAULT NULL COMMENT '指定的商家ID，当source=merchant时使用',
    `supplier_id` int(11) DEFAULT NULL COMMENT '供货商ID',
    `original_merchant_id` int(11) DEFAULT NULL COMMENT '原始商家ID，供货商场景使用',
    `last_message` text COMMENT '最后一条消息',
    `last_time` int(11) DEFAULT NULL COMMENT '最后消息时间',
    `unread_count` int(11) DEFAULT '0' COMMENT '未读消息数',
    `customer_unread` INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '客户未读数',
    `merchant_unread` INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商家未读数',
    `staff_unread` INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '客服未读数',
    `create_time` int(11) NOT NULL COMMENT '创建时间',
    `update_time` int(11) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_contact_id` (`contact_id`),
    KEY `idx_staff_id` (`staff_id`),
    KEY `idx_merchant_id` (`merchant_id`),
    KEY `idx_supplier_id` (`supplier_id`),
    KEY `idx_original_merchant_id` (`original_merchant_id`),
    KEY `idx_source` (`source`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='聊天会话表';
SQL;

        // 使用heredoc语法定义SQL - 聊天消息表
        $sqlMessages = <<<SQL
CREATE TABLE IF NOT EXISTS `{$tableMessages}` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `session_id` int(11) NOT NULL COMMENT '会话ID',
    `sender_type` enum('staff','customer') NOT NULL COMMENT '发送者类型',
    `sender_id` int(11) NOT NULL COMMENT '发送者ID',
    `role_type` enum('merchant','staff','customer','supplier') DEFAULT NULL COMMENT '角色类型',
    `message` text COMMENT '消息内容',
    `message_type` enum('text','image','file') NOT NULL DEFAULT 'text' COMMENT '消息类型',
    `file_url` varchar(255) DEFAULT NULL COMMENT '文件URL',
    `is_read` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已读',
    `read_time` int(11) DEFAULT '0' COMMENT '已读时间戳',
    `customer_read` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '客户已读状态',
    `customer_read_time` INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '客户已读时间',
    `merchant_read` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '商家已读状态',
    `merchant_read_time` INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商家已读时间',
    `staff_read` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '客服已读状态',
    `staff_read_time` INT(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '客服已读时间',
    `is_system` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否系统消息',
    `is_recalled` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已撤回',
    `visible_to` text COMMENT '消息可见性(JSON格式,如["supplier_1","customer_5"])',
    `extra_data` text COMMENT '扩展数据(JSON格式)',
    `create_time` int(11) NOT NULL COMMENT '创建时间',
    `update_time` int(11) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_session_id` (`session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='聊天消息表';
SQL;

        // 使用heredoc语法定义SQL - 商家消息设置表
        $sqlSettings = <<<SQL
CREATE TABLE IF NOT EXISTS `{$tableSettings}` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `user_id` int(11) NOT NULL COMMENT '商家ID',
    `settings` text COMMENT '设置JSON',
    `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
    `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家消息设置表';
SQL;

        // 使用heredoc语法定义SQL - 聊天会话加入请求表
        $sqlJoinRequests = <<<SQL
CREATE TABLE IF NOT EXISTS `{$tableJoinRequests}` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `session_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '会话ID',
  `requester_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '请求方ID',
  `requester_type` varchar(20) NOT NULL DEFAULT '' COMMENT '请求方类型(supplier/customer)',
  `requester_name` varchar(100) NOT NULL DEFAULT '' COMMENT '请求方名称',
  `target_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '目标方ID',
  `target_type` varchar(20) NOT NULL DEFAULT '' COMMENT '目标方类型(supplier/customer)',
  `reason` varchar(255) NOT NULL DEFAULT '' COMMENT '申请/邀请原因',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '状态(pending/approved/rejected)',
  `is_invitation` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否是邀请(0=申请,1=邀请)',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_session` (`session_id`),
  KEY `idx_requester` (`requester_id`,`requester_type`),
  KEY `idx_target` (`target_id`,`target_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='聊天会话加入请求表';
SQL;

        // 使用heredoc语法定义SQL - 会话参与者表
        $sqlParticipants = <<<SQL
CREATE TABLE IF NOT EXISTS `{$tableParticipants}` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `session_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '会话ID',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `user_type` varchar(20) NOT NULL DEFAULT '' COMMENT '用户类型(supplier/customer/admin)',
  `join_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '加入时间',
  `is_active` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '是否活跃',
  `last_read_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '最后阅读时间',
  `invitation_id` int(11) unsigned DEFAULT NULL COMMENT '关联的邀请ID',
  `approved_time` int(11) unsigned DEFAULT NULL COMMENT '审核通过时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_session_user` (`session_id`,`user_id`,`user_type`),
  KEY `idx_user` (`user_id`,`user_type`),
  KEY `idx_invitation_id` (`invitation_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='会话参与者表';
SQL;

        // 使用heredoc语法定义SQL - 供应商表
        $sqlSuppliers = <<<SQL
CREATE TABLE IF NOT EXISTS `{$tableSuppliers}` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '供应商名称',
  `status` varchar(20) NOT NULL DEFAULT 'normal' COMMENT '状态(normal/disabled)',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='供应商表';
SQL;

        // 使用heredoc语法定义SQL - 预设回复表
        $sqlPresetReplies = <<<SQL
CREATE TABLE IF NOT EXISTS `{$tablePresetReplies}` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `merchant_id` int(11) NOT NULL DEFAULT '0' COMMENT '商家ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '回复内容',
  `tags` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '标签，逗号分隔',
  `is_quick` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为快捷回复，0=否，1=是',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `merchant_id` (`merchant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预设回复表';
SQL;

        // 使用heredoc语法定义SQL - 系统设置表
        $sqlCustomerSettings = <<<SQL
CREATE TABLE IF NOT EXISTS `{$tableCustomerSettings}` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `key` varchar(100) NOT NULL COMMENT '设置键名',
  `value` text COMMENT '设置值',
  `type` varchar(20) NOT NULL DEFAULT 'string' COMMENT '值类型',
  `description` varchar(255) DEFAULT NULL COMMENT '设置描述',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_key` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统设置表';
SQL;

        // 创建联系人表
        $this->createTable($tableContacts, $sqlContacts, '联系人表');

        // 创建会话表
        $this->createTable($tableSessions, $sqlSessions, '会话表');

        // 创建消息表
        $this->createTable($tableMessages, $sqlMessages, '消息表');

        // 创建设置表
        $this->createTable($tableSettings, $sqlSettings, '设置表');

        // 创建加入请求表
        $this->createTable($tableJoinRequests, $sqlJoinRequests, '加入请求表');

        // 创建参与者表
        $this->createTable($tableParticipants, $sqlParticipants, '参与者表');

        // 创建供应商表
        $this->createTable($tableSuppliers, $sqlSuppliers, '供应商表');

        // 创建预设回复表
        $this->createTable($tablePresetReplies, $sqlPresetReplies, '预设回复表');

        // 创建系统设置表
        $this->createTable($tableCustomerSettings, $sqlCustomerSettings, '系统设置表');

        // 插入默认系统设置
        try {
            $defaultSetting = "INSERT INTO `{$tableCustomerSettings}` (`key`, `value`, `type`, `description`, `create_time`, `update_time`)
            VALUES ('invitation_expiry_hours', '48', 'integer', '商家邀请超时时间(小时)', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())";
            Db::execute($defaultSetting);

        } catch (\Exception $e) {


        }


        return true;
    }

    public function uninstall() {
        // 使用Env获取数据库前缀
        $prefix = Env::get('DB_PREFIX');

        // 定义需要删除的表
        $tables = [
            $prefix . 'plugin_chat_contacts',
            $prefix . 'plugin_chat_sessions',
            $prefix . 'plugin_chat_messages',
            $prefix . 'plugin_chat_settings',
            $prefix . 'plugin_chat_join_requests',
            $prefix . 'plugin_chat_session_participants',
            $prefix . 'plugin_suppliers',
            $prefix . 'plugin_chat_preset_replies',
            $prefix . 'plugin_chat_customer_settings'
        ];

        try {
            foreach ($tables as $tableName) {
                Db::execute("DROP TABLE IF EXISTS `{$tableName}`");

            }
        } catch (\Exception $e) {

            Log::error('Customersystem插件卸载失败：' . $e->getMessage());
            return false;
        }

        return true;
    }

    public function upgrade() {
        try {
            // 使用Env获取数据库前缀
            $prefix = Env::get('DB_PREFIX');

            // 这里可以添加升级逻辑，比如检查字段是否存在并添加新字段
            // 类似于Waterchallenge插件的upgrade方法

            return true;
        } catch (\Exception $e) {
            Log::error('Customersystem插件升级失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * 创建数据表的通用方法
     */
    protected function createTable($tableName, $sql, $description) {
        // 检查表是否存在
        $has_tables = Db::query("SHOW TABLES LIKE '{$tableName}'");

        if (!empty($has_tables)) {

        } else {
            // 表不存在，创建表
            try {
                Db::execute($sql);

            } catch (\Exception $e) {


                return false;
            }
        }
        return true;
    }
}