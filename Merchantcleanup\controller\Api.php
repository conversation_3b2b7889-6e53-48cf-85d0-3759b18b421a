<?php

namespace plugin\Merchantcleanup\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Log;
use think\facade\Db;

class Api extends BasePlugin {

    protected $scene = ['admin'];  // 只允许管理员访问
    
    protected $noNeedLogin = [];

    // 管理面板首页
    public function index() {
        return View::fetch();
    }

    // 获取配置
    public function getConfig() {
        try {
            $configFile = app()->getRootPath() . 'plugin/Merchantcleanup/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            // 确保返回必要的配置项
            if (!isset($config['cleanup_config'])) {
                $config['cleanup_config'] = [
                    'status' => true,  // 回收站功能开关
                    'auto_clean_days' => 30,  // 自动清理天数
                    'enable_auto_clean' => false  // 是否开启自动清理
                ];
            }

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $config['cleanup_config']
            ]);
        } catch (\Exception $e) {
            Log::error('获取配置失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取配置失败：' . $e->getMessage()]);
        }
    }

    // 保存配置
    public function saveConfig() {
        try {
            $data = [
                'status' => input('status/b', true),
                'auto_clean_days' => input('auto_clean_days/d', 30),
                'enable_auto_clean' => input('enable_auto_clean/b', false)
            ];
            
            // 读取配置文件
            $configFile = app()->getRootPath() . 'plugin/Merchantcleanup/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            // 更新配置
            $config['cleanup_config'] = $data;
            
            // 保存配置
            if (!file_put_contents($configFile, "<?php\nreturn " . var_export($config, true) . ";\n")) {
                throw new \Exception('保存配置失败');
            }
            
            return json(['code' => 200, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            Log::error('保存配置失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }
}
