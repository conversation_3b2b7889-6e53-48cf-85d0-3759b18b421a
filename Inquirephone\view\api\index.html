<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <meta http-equiv="Cache-Control" content="max-age=31536000">
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>订单联系方式查询</title>
    <style>
        .table-cell {
            display: flex;
            align-items: center;
        }
        .table-cell span {
            margin-right: 10px;
        }
        .order-info {
            display: flex;
            gap: 15px;
            align-items: center;
        }
        .info-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .info-label {
            color: #909399;
            font-size: 13px;
        }
        .info-value {
            font-weight: 500;
        }
    </style>
</head>
<body>
<div id="app">
    <el-card shadow="never">
        <div style="margin-bottom: 20px">
            <el-input
                v-model="tradeNo"
                placeholder="请输入完整订单号查询"
                style="width: 300px"
                @keyup.enter="handleSearch"
            >
                <template #append>
                    <el-button @click="handleSearch">
                        <el-icon><Search /></el-icon>
                    </el-button>
                </template>
            </el-input>
            <div style="margin-top: 10px; color: #909399; font-size: 12px;">
                提示：请输入完整的订单号进行查询
            </div>
        </div>

        <el-table
            v-if="orderData"
            :data="[orderData]"
            border
            style="width: 100%"
            v-loading="loading"
        >
            <el-table-column label="订单信息" min-width="200">
                <template #default="scope">
                    <div class="order-info">
                        <div class="info-item">
                            <span class="info-label">商品：</span>
                            <span class="info-value">{{ scope.row.goods_name }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">订单号：</span>
                            <span class="info-value">{{ scope.row.trade_no }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">状态：</span>
                            <el-tag size="small" :type="getStatusType(scope.row.status)">
                                {{ getStatusText(scope.row.status) }}
                            </el-tag>
                        </div>
                        <div class="info-item">
                            <span class="info-label">联系方式：</span>
                            <span class="info-value">{{ scope.row.contact_info }}</span>
                        </div>
                    </div>
                </template>
            </el-table-column>
        </el-table>

        <div v-else style="text-align: center; padding: 20px; color: #909399;">
            <el-empty description="请输入完整订单号查询" />
        </div>
    </el-card>
</div>

<script src="/static/others/vue/vue.global.prod.js"></script>
<script src="/static/others/element-plus/index.full.min.js"></script>
<script src="/static/others/axios/axios.min.js"></script>

<script>
const { ref } = Vue;
const { ElMessage } = ElementPlus;

const app = Vue.createApp({
    setup() {
        const loading = ref(false);
        const orderData = ref(null);
        const tradeNo = ref('');

        const fetchData = async () => {
            if (!tradeNo.value) {
                orderData.value = null;
                return;
            }

            loading.value = true;
            try {
                const res = await axios.get('/plugin/Inquirephone/api/getOrders', {
                    params: {
                        trade_no: tradeNo.value
                    }
                });

                if (res.data?.code === 200) {
                    orderData.value = res.data.data;
                } else {
                    ElMessage.error(res.data?.msg || '未找到订单');
                    orderData.value = null;
                }
            } catch (error) {
                console.error('Error:', error);
                ElMessage.error('查询失败，请稍后重试');
                orderData.value = null;
            } finally {
                loading.value = false;
            }
        };

        const handleSearch = () => {
            fetchData();
        };

        const getStatusType = (status) => {
            const types = {
                0: 'info',
                1: 'success',
                2: 'warning',
                3: 'danger'
            };
            return types[status] || 'info';
        };

        const getStatusText = (status) => {
            const texts = {
                0: '未支付',
                1: '已支付',
                2: '已关闭',
                3: '已退款'
            };
            return texts[status] || '未知';
        };

        return {
            loading,
            orderData,
            tradeNo,
            handleSearch,
            getStatusType,
            getStatusText
        };
    }
});

app.use(ElementPlus);
app.mount('#app');
</script>
</body>
</html> 