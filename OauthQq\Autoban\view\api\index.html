<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <meta http-equiv="Cache-Control" content="max-age=31536000">
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>自动封禁设置</title>
    <style>
        /* 加载动画样式 */
        #loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #fff;
            z-index: 9999;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(0, 0, 0, 0.1);
            border-top-color: #3498db;
            border-radius: 50%;
            animation: spin 1s ease infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>

<body>
<!-- 加载动画容器 -->
<div id="loading">
    <div class="spinner"></div>
</div>

<!-- Vue 应用挂载点 -->
<div id="app" style="display: none">
    <el-card shadow="never">
        <el-form :model="form" label-width="auto">
            <el-form-item label="自动封禁功能：">
                <el-radio-group v-model="form.status">
                    <el-radio :value="0">关闭</el-radio>
                    <el-radio :value="1">开启</el-radio>
                </el-radio-group>
            </el-form-item>

            <el-form-item label="投诉率阈值：">
                <el-input-number 
                    v-model="form.threshold" 
                    :min="1" 
                    :max="100" 
                    :step="1">
                    <template #suffix>%</template>
                </el-input-number>
                <div class="el-form-item-description" style="font-size: 12px; color: #909399; margin-top: 4px;">
                    当用户投诉率超过此值时将自动封禁（默认50%）
                </div>
            </el-form-item>

            <el-form-item label="封禁提示内容：">
                <el-input 
                    v-model="form.banContent" 
                    type="textarea" 
                    :rows="3"
                    placeholder="请输入自动封禁时的提示内容">
                </el-input>
                <div class="el-form-item-description" style="font-size: 12px; color: #909399; margin-top: 4px;">
                    可使用 {threshold} 作为阈值占位符，例如：因投诉率超过{threshold}%，系统自动封禁
                </div>
            </el-form-item>
        </el-form>

        <p style="text-align: center;margin: 0 auto;margin-top: 16px;">
            <el-button type="primary" :loading="isLoading" @click="save">
                保存
            </el-button>
        </p>
    </el-card>
</div>

<script src="/static/others/vue/vue.global.prod.js"></script>
<script src="/static/others/element-plus/index.full.min.js"></script>
<script src="/static/others/axios/axios.min.js"></script>

<script type="module">
    const { ref, reactive } = Vue;
    const { ElMessage } = ElementPlus;

    const isLoading = ref(false);

    const form = reactive({
        status: 0,
        threshold: 50,
        banContent: '因投诉率超过{threshold}%，系统自动封禁'
    });

    const fetchData = async () => {
        isLoading.value = true;
        try {
            const res = await axios.post("/plugin/Autoban/api/fetchData");
            isLoading.value = false;
            if (res.data?.code == 200) {
                form.status = res.data?.data?.status ?? 0;
                form.threshold = res.data?.data?.threshold ?? 50;
                form.banContent = res.data?.data?.banContent ?? '因投诉率超过{threshold}%，系统自动封禁';
            } else {
                ElMessage.error(res.data?.msg);
            }
        } catch (error) {
            isLoading.value = false;
            ElMessage.error(error.message || '请求失败');
        }
    };

    fetchData();

    const save = async () => {
        isLoading.value = true;
        try {
            const res = await axios.post("/plugin/Autoban/api/save", form);
            isLoading.value = false;
            if (res.data?.code == 200) {
                ElMessage.success('保存成功');
            } else {
                ElMessage.error(res.data?.msg);
            }
        } catch (error) {
            isLoading.value = false;
            ElMessage.error(error.message || '请求失败');
        }
    };

    const app = Vue.createApp({
        setup() {
            return {
                isLoading,
                form,
                save,
            };
        },
    });

    app.use(ElementPlus);
    app.mount("#app");

    window.onload = () => {
        document.getElementById("loading").style.display = "none";
        document.getElementById("app").style.display = "block";
    };
</script>
</body>
</html>
