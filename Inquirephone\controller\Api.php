<?php
namespace plugin\Inquirephone\controller;

use app\common\controller\BasePlugin;
use think\facade\Db;
use think\facade\View;

class Api extends BasePlugin
{
    protected $scene = ['user'];  // 商家用户场景

    public function index()
    {
        return View::fetch();
    }

    /**
     * 过滤 SQL 注入和 XSS 攻击
     */
    protected function filterInput($input) 
    {
        if (empty($input)) {
            return '';
        }

        // 移除空白字符
        $input = preg_replace('/\s+/', '', $input);

        // 过滤常见的 SQL 注入和 XSS 攻击字符
        $blacklist = [
            // SQL 注入
            "'", '"', '`', ';', '\\', '=', 
            // 注释符
            '--', '#', '/*', '*/', '//',
            // 特殊字符
            '<', '>', '(', ')', '[', ']', '{', '}',
            // 危险函数
            'eval', 'exec', 'system', 'shell',
            // 编码函数
            'base64', 'decode', 'encode',
            // SQL 关键字
            'select', 'update', 'delete', 'drop', 'truncate',
            'union', 'insert', 'having', 'group by',
            // 逻辑运算符
            ' and ', ' or ', ' xor ', ' not ',
            // 特殊编码
            '&#', '0x', '%0', 'u00',
            // XSS
            'script', 'javascript', 'vbscript', 'expression',
            'applet', 'meta', 'xml', 'blink', 'link',
            'style', 'embed', 'object', 'iframe', 'frame',
            'layer', 'bgsound', 'title', 'base',
            // 事件
            'onabort', 'onactivate', 'onafterprint', 'onafterupdate',
            'onbeforeactivate', 'onbeforecopy', 'onbeforecut',
            'onbeforedeactivate', 'onbeforeeditfocus', 'onbeforepaste',
            'onbeforeprint', 'onbeforeunload', 'onbeforeupdate',
            'onblur', 'onbounce', 'oncellchange', 'onchange',
            'onclick', 'oncontextmenu', 'oncontrolselect',
            'oncopy', 'oncut', 'ondataavailable', 'ondatasetchanged',
            'ondatasetcomplete', 'ondblclick', 'ondeactivate',
            'ondrag', 'ondragend', 'ondragenter', 'ondragleave',
            'ondragover', 'ondragstart', 'ondrop', 'onerror',
            'onerrorupdate', 'onfilterchange', 'onfinish', 'onfocus',
            'onfocusin', 'onfocusout', 'onhelp', 'onkeydown',
            'onkeypress', 'onkeyup', 'onlayoutcomplete', 'onload',
            'onlosecapture', 'onmousedown', 'onmouseenter',
            'onmouseleave', 'onmousemove', 'onmouseout',
            'onmouseover', 'onmouseup', 'onmousewheel', 'onmove',
            'onmoveend', 'onmovestart', 'onpaste', 'onpropertychange',
            'onreadystatechange', 'onreset', 'onresize', 'onresizeend',
            'onresizestart', 'onrowenter', 'onrowexit', 'onrowsdelete',
            'onrowsinserted', 'onscroll', 'onselect', 'onselectionchange',
            'onselectstart', 'onstart', 'onstop', 'onsubmit', 'onunload'
        ];

        // 将所有危险字符/字符串替换为空
        $input = str_ireplace($blacklist, '', $input);

        // 过滤十六进制和unicode编码
        if (preg_match('/0x[0-9a-fA-F]+/', $input) || 
            preg_match('/\\\\[xuU][0-9a-fA-F]+/', $input)) {
            return '';
        }

        // 只允许字母和数字
        if (!preg_match('/^[A-Za-z0-9]+$/', $input)) {
            return '';
        }

        return $input;
    }

    // 获取订单列表
    public function getOrders()
    {
        try {
            $trade_no = trim($this->request->get('trade_no', ''));
            
            // 如果没有订单号，直接返回空数据
            if (empty($trade_no)) {
                return json([
                    'code' => 200,
                    'msg' => 'success',
                    'data' => null
                ]);
            }

            // 安全过滤
            $trade_no = $this->filterInput($trade_no);
            if (empty($trade_no)) {
                return json([
                    'code' => 400,
                    'msg' => '订单号格式不正确'
                ]);
            }

            // 精确查询订单
            $order = Db::name('order')
                ->alias('o')
                ->join('goods g', 'o.goods_id = g.id')
                ->where([
                    'o.user_id' => $this->user->id,
                    'o.trade_no' => $trade_no
                ])
                ->field([
                    'o.trade_no',
                    'o.status',
                    'o.contact as contact_info',
                    'g.name as goods_name'
                ])
                ->find();

            if (!$order) {
                return json([
                    'code' => 404,
                    'msg' => '未找到订单',
                    'data' => null
                ]);
            }

            // 安全处理输出数据
            $order['contact_info'] = htmlspecialchars($order['contact_info']);
            $order['goods_name'] = htmlspecialchars($order['goods_name']);
            
            return json([
                'code' => 200,
                'msg' => 'success',
                'data' => $order
            ]);

        } catch (\Exception $e) {
            \think\facade\Log::error('Order query error: ' . $e->getMessage());
            return json([
                'code' => 500,
                'msg' => '查询失败'
            ]);
        }
    }

    /**
     * 验证订单号格式
     */
    protected function validateOrderNo($orderNo)
    {
        if (empty($orderNo)) {
            return true;
        }

        // 严格限制只允许字母和数字，其他一律拦截
        if (!preg_match('/^[A-Za-z0-9]+$/', $orderNo) || 
            strlen($orderNo) > 32 || 
            preg_match('/0x[0-9a-fA-F]+/', $orderNo) ||  // 拦截十六进制
            preg_match('/\\\\[xuU][0-9a-fA-F]+/', $orderNo)  // 拦截 unicode 编码
        ) {
            return false;
        }

        return true;
    }
} 