<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单流水展示设置</title>
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <style>
        /* 基础样式 */
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #f5f7fa;
        }

        .container {
            padding: 20px;
        }

        .config-card {
            margin-bottom: 20px;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .el-form-item {
            margin-bottom: 22px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <el-card class="config-card">
                <template #header>
                    <div class="card-header">
                        <span>基础设置</span>
                    </div>
                </template>
                
                <el-form :model="config" label-width="120px" v-loading="loading">
                    <el-form-item label="启用状态">
                        <el-switch v-model="config.status" />
                    </el-form-item>
                    
                    <el-form-item label="需要密码访问">
                        <el-switch v-model="config.need_password" />
                    </el-form-item>
                    
                    <el-form-item label="访问密码" v-if="config.need_password">
                        <el-input v-model="config.password" placeholder="请输入访问密码" />
                    </el-form-item>

                    <el-form-item label="交易号脱敏">
                        <el-switch v-model="config.desensitization" />
                        <div class="el-form-item-desc" style="color: #909399; font-size: 12px; margin-top: 5px;">
                            开启后将对交易号进行部分隐藏处理
                        </div>
                    </el-form-item>
                    
                    <el-form-item>
                        <el-button type="primary" @click="saveConfig" :loading="saving">保存设置</el-button>
                    </el-form-item>
                </el-form>
            </el-card>
        </div>
    </div>

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>

    <script>
        const app = Vue.createApp({
            setup() {
                const config = Vue.ref({
                    status: true,
                    need_password: false,
                    password: '',
                    desensitization: false,
                });
                const loading = Vue.ref(false);
                const saving = Vue.ref(false);

                // 获取配置
                const getConfig = async () => {
                    loading.value = true;
                    try {
                        const res = await axios.get('/plugin/Orderdisplay/api/getConfig');
                        if (res.data.code === 200) {
                            Object.assign(config.value, res.data.data);
                        } else {
                            ElMessage.error(res.data.msg || '获取配置失败');
                        }
                    } catch (error) {
                        console.error('获取配置失败:', error);
                        ElMessage.error('获取配置失败：' + error.message);
                    } finally {
                        loading.value = false;
                    }
                };

                // 保存配置
                const saveConfig = async () => {
                    saving.value = true;
                    try {
                        const res = await axios.post('/plugin/Orderdisplay/api/saveConfig', config.value);
                        if (res.data.code === 200) {
                            ElMessage.success('保存成功');
                        } else {
                            ElMessage.error(res.data.msg || '保存失败');
                        }
                    } catch (error) {
                        console.error('保存配置失败:', error);
                        ElMessage.error('保存配置失败：' + error.message);
                    } finally {
                        saving.value = false;
                    }
                };

                // 页面加载时获取配置
                Vue.onMounted(() => {
                    getConfig();
                });

                return {
                    config,
                    loading,
                    saving,
                    saveConfig
                };
            }
        });

        app.use(ElementPlus, {
            locale: ElementPlusLocaleZhCn
        });
        
        app.mount('#app');
    </script>
</body>
</html> 