
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>卡密验证系统 - 仪表盘</title>
        <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        <script src="https://cdn.bootcdn.net/ajax/libs/Chart.js/3.7.1/chart.min.js"></script>
        <style>
            body {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            }
            .sidebar {
                background: linear-gradient(180deg, #4a00e0 0%, #8e2de2 100%);
                min-height: 100vh;
                padding-top: 20px;
                box-shadow: 4px 0 10px rgba(0, 0, 0, 0.1);
                z-index: 1000;
                position: fixed;
                width: inherit;
                max-width: inherit;
            }
            .sidebar-header {
                padding: 0 15px 20px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                margin-bottom: 20px;
            }
            .sidebar a {
                color: rgba(255, 255, 255, 0.8);
                display: block;
                padding: 12px 20px;
                text-decoration: none;
                transition: all 0.3s;
                margin-bottom: 5px;
                border-left: 3px solid transparent;
            }
            .sidebar a:hover {
                background-color: rgba(255, 255, 255, 0.1);
                color: #fff;
                border-left: 3px solid #fff;
            }
            .sidebar a.active {
                background-color: rgba(255, 255, 255, 0.2);
                color: #fff;
                border-left: 3px solid #fff;
            }
            .sidebar i {
                margin-right: 10px;
                width: 20px;
                text-align: center;
            }
            .main-content {
                margin-left: 16.666667%;
                padding: 30px;
            }
            .page-header {
                margin-bottom: 30px;
                border-bottom: 1px solid #dee2e6;
                padding-bottom: 15px;
            }
            .dashboard-card {
                background: #fff;
                border-radius: 15px;
                overflow: hidden;
                box-shadow: 0 10px 20px rgba(0,0,0,0.05);
                margin-bottom: 30px;
                transition: all 0.3s;
            }
            .dashboard-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 15px 30px rgba(0,0,0,0.1);
            }
            .card-header {
                padding: 20px;
                border-bottom: 1px solid #f1f1f1;
                font-weight: 600;
                background-color: #fff;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }
            .card-header .title {
                font-size: 18px;
                color: #333;
            }
            .card-body {
                padding: 20px;
            }
            .stat-card {
                background: linear-gradient(45deg, #8e2de2, #4a00e0);
                border-radius: 15px;
                padding: 25px 20px;
                box-shadow: 0 10px 20px rgba(142, 45, 226, 0.2);
                color: white;
                display: flex;
                align-items: center;
                transition: all 0.3s;
                margin-bottom: 30px;
            }
            .stat-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 15px 30px rgba(142, 45, 226, 0.3);
            }
            .stat-card .icon {
                background: rgba(255, 255, 255, 0.2);
                width: 65px;
                height: 65px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                margin-right: 20px;
                font-size: 24px;
            }
            .stat-card .stat-content h2 {
                font-size: 28px;
                font-weight: 700;
                margin-bottom: 5px;
                line-height: 1;
            }
            .stat-card .stat-content p {
                margin-bottom: 0;
                font-size: 14px;
                opacity: 0.8;
            }
            .stat-card.blue {
                background: linear-gradient(45deg, #4e54c8, #8f94fb);
            }
            .stat-card.green {
                background: linear-gradient(45deg, #11998e, #38ef7d);
            }
            .stat-card.orange {
                background: linear-gradient(45deg, #f46b45, #eea849);
            }
            .verification-stat {
                text-align: center;
                padding: 20px 15px;
                border-radius: 10px;
                background-color: #f8f9fa;
                transition: all 0.3s;
                margin-bottom: 20px;
            }
            .verification-stat:hover {
                background-color: #e9ecef;
                transform: translateY(-3px);
            }
            .verification-stat h3 {
                font-size: 24px;
                font-weight: 700;
                margin-bottom: 5px;
                color: #4a00e0;
            }
            .verification-stat p {
                margin-bottom: 0;
                color: #6c757d;
                font-size: 14px;
            }
            .table {
                margin-bottom: 0;
            }
            .table thead th {
                font-weight: 600;
                font-size: 14px;
                border-top: none;
                padding: 15px 12px;
            }
            .table tbody td {
                vertical-align: middle;
                padding: 12px;
                font-size: 14px;
            }
            .license-key {
                font-family: monospace;
                font-size: 13px;
                background-color: #f8f9fa;
                padding: 5px 8px;
                border-radius: 5px;
                cursor: pointer;
            }
            .license-key:hover {
                background-color: #e9ecef;
            }
            .card-status {
                font-weight: 500;
                padding: 5px 10px;
                border-radius: 20px;
                font-size: 12px;
                display: inline-block;
            }
            .status-active {
                background-color: rgba(25, 135, 84, 0.1);
                color: #198754;
            }
            .status-inactive {
                background-color: rgba(108, 117, 125, 0.1);
                color: #6c757d;
            }
            .status-expired {
                background-color: rgba(220, 53, 69, 0.1);
                color: #dc3545;
            }
            .empty-data {
                text-align: center;
                padding: 40px 20px;
                color: #6c757d;
            }
            .empty-data i {
                font-size: 48px;
                margin-bottom: 20px;
                opacity: 0.2;
            }
            .empty-data p {
                margin-bottom: 0;
                font-size: 16px;
            }
            .chart-container {
                height: 330px;
                width: 100%;
                margin-top: 10px;
            }
        </style>
    </head>
    <body>
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-2">
                    <div class="sidebar">
                        <div class="sidebar-header">
                            <h4 class="text-center text-white fw-bold mb-0">卡密验证系统</h4>
                            <p class="text-center text-white-50 mb-0">管理面板</p>
                        </div>
                        <a href="/dashboard" class="active"><i class="fas fa-tachometer-alt"></i> 仪表盘</a>
                        <a href="/licenses"><i class="fas fa-key"></i> 卡密管理</a>
                        <a href="/create_license"><i class="fas fa-plus-circle"></i> 生成卡密</a>
                        <a href="/access_logs"><i class="fas fa-history"></i> 访问日志</a>
                        <a href="/logout"><i class="fas fa-sign-out-alt"></i> 退出登录</a>
                    </div>
                </div>
                <div class="col-md-10 main-content">
                    <div class="page-header d-flex align-items-center justify-content-between">
                        <h2 class="mb-0">系统仪表盘</h2>
                        <div class="date">{{ current_date }}</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="stat-card blue">
                                <div class="icon">
                                    <i class="fas fa-key"></i>
                                </div>
                                <div class="stat-content">
                                    <h2>{{ total_licenses }}</h2>
                                    <p>卡密总数</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stat-card green">
                                <div class="icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="stat-content">
                                    <h2>{{ active_licenses }}</h2>
                                    <p>已激活卡密</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stat-card orange">
                                <div class="icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="stat-content">
                                    <h2>{{ expired_licenses }}</h2>
                                    <p>已过期卡密</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="dashboard-card">
                                <div class="card-header">
                                    <div class="title">
                                        <i class="fas fa-chart-line me-2"></i>验证趋势
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container">
                                        <canvas id="verificationsChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="dashboard-card">
                                <div class="card-header">
                                    <div class="title">
                                        <i class="fas fa-chart-bar me-2"></i>验证统计
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="verification-stat">
                                                <h3>{{ verification_stats.total }}</h3>
                                                <p>30天总验证</p>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="verification-stat">
                                                <h3>{{ verification_stats.today }}</h3>
                                                <p>今日验证</p>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="verification-stat">
                                                <h3>{{ verification_stats.yesterday }}</h3>
                                                <p>昨日验证</p>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="verification-stat">
                                                <h3>{{ verification_stats.this_week }}</h3>
                                                <p>本周验证</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="dashboard-card">
                                <div class="card-header">
                                    <div class="title">
                                        <i class="fas fa-key me-2"></i>最近创建的卡密
                                    </div>
                                    <a href="/licenses" class="btn btn-sm btn-outline-primary">查看全部</a>
                                </div>
                                <div class="card-body p-0">
                                    {% if recent_licenses %}
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>卡密</th>
                                                    <th>软件ID</th>
                                                    <th>创建日期</th>
                                                    <th>状态</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for license in recent_licenses %}
                                                <tr>
                                                    <td><span class="license-key" onclick="copyToClipboard('{{ license.license_key }}')">{{ license.license_key }}</span></td>
                                                    <td>{{ license.software_id }}</td>
                                                    <td>{{ license.created_at }}</td>
                                                    <td>
                                                        {% if license.valid_until and license.valid_until < current_date %}
                                                            <span class="card-status status-expired">已过期</span>
                                                        {% elif license.activated %}
                                                            <span class="card-status status-active">已激活</span>
                                                        {% else %}
                                                            <span class="card-status status-inactive">未激活</span>
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    {% else %}
                                    <div class="empty-data">
                                        <i class="fas fa-key"></i>
                                        <p>尚未创建任何卡密</p>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="dashboard-card">
                                <div class="card-header">
                                    <div class="title">
                                        <i class="fas fa-history me-2"></i>最近激活记录
                                    </div>
                                    <a href="/access_logs" class="btn btn-sm btn-outline-primary">查看全部日志</a>
                                </div>
                                <div class="card-body p-0">
                                    {% if recent_activations %}
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>卡密</th>
                                                    <th>设备ID</th>
                                                    <th>时间</th>
                                                    <th>状态</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for log in recent_activations %}
                                                <tr>
                                                    <td><span class="license-key">{{ log.license_key }}</span></td>
                                                    <td>{{ log.device_id }}</td>
                                                    <td>{{ log.timestamp }}</td>
                                                    <td>
                                                        <span class="card-status status-active">成功</span>
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    {% else %}
                                    <div class="empty-data">
                                        <i class="fas fa-history"></i>
                                        <p>尚无卡密激活记录</p>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <script>
            // 模拟过去30天的验证数据
            const labels = Array.from({length: 30}, (_, i) => {
                const date = new Date();
                date.setDate(date.getDate() - 29 + i);
                return `${date.getMonth()+1}/${date.getDate()}`;
            });
            
            // 随机生成样本数据
            const generateData = () => {
                return Array.from({length: 30}, () => Math.floor(Math.random() * 50));
            };
            
            const ctx = document.getElementById('verificationsChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '验证次数',
                        data: generateData(),
                        borderColor: '#8e2de2',
                        backgroundColor: 'rgba(142, 45, 226, 0.1)',
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: '#8e2de2',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 4,
                        pointHoverRadius: 6
                    }]
                },
                options: {
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                            backgroundColor: 'rgba(0, 0, 0, 0.7)',
                            titleColor: '#fff',
                            bodyColor: '#fff',
                            borderColor: '#8e2de2',
                            borderWidth: 1,
                            padding: 10,
                            displayColors: false
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        }
                    }
                }
            });
            
            function copyToClipboard(text) {
                navigator.clipboard.writeText(text).then(() => {
                    alert('卡密已复制到剪贴板');
                }).catch(err => {
                    console.error('复制失败:', err);
                });
            }
        </script>
    </body>
    </html>
    