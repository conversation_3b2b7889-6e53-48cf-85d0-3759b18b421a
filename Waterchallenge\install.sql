-- 流水挑战规则表
CREATE TABLE IF NOT EXISTS `pt_plugin_waterchallenge_rules` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `rule_id` varchar(32) NOT NULL COMMENT '规则ID',
  `name` varchar(255) NOT NULL COMMENT '规则名称',
  `turnover_amount` decimal(10,3) NOT NULL DEFAULT '0.000' COMMENT '要求流水',
  `reward_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '奖励金额',
  `challenge_duration` int(11) NOT NULL DEFAULT '7' COMMENT '挑战持续天数',
  `valid_days` int(11) NOT NULL DEFAULT '30' COMMENT '规则有效天数',
  `max_attempts` int(11) NOT NULL DEFAULT '0' COMMENT '最大参与次数，0表示无限制',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '规则状态：0禁用1启用',
  `start_time` datetime DEFAULT NULL COMMENT '生效开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '生效结束时间',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_rule_id` (`rule_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流水挑战规则';

-- 流水挑战用户记录表
CREATE TABLE IF NOT EXISTS `pt_plugin_waterchallenge_user` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `challenge_id` varchar(32) NOT NULL COMMENT '挑战记录ID',
  `rule_id` varchar(32) NOT NULL COMMENT '规则ID',
  `rule_name` varchar(255) NOT NULL COMMENT '规则名称',
  `current_amount` decimal(10,3) NOT NULL DEFAULT '0.000' COMMENT '当前流水',
  `base_amount` decimal(10,3) NOT NULL DEFAULT '0.000' COMMENT '基准流水',
  `target_amount` decimal(10,3) NOT NULL DEFAULT '0.000' COMMENT '目标流水',
  `status` varchar(20) NOT NULL DEFAULT 'ongoing' COMMENT '状态：ongoing|completed|failed',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `reward_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '奖励金额',
  `reward_sent` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已发放奖励：0否1是',
  `reward_time` datetime DEFAULT NULL COMMENT '奖励发放时间',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_challenge_id` (`challenge_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流水挑战用户记录'; 