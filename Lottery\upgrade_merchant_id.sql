-- 抽奖插件商户ID字段升级脚本
-- 修复 SQLSTATE[22003]: Numeric value out of range 错误
-- 将 merchant_id 相关字段从 int(11) 升级为 bigint(20)
-- 创建时间: 2025-07-02

-- 1. 升级抽奖记录表的用户ID和商户ID字段
ALTER TABLE `pt_plugin_lottery_records` 
MODIFY COLUMN `user_id` bigint(20) NOT NULL COMMENT '用户ID',
MODIFY COLUMN `merchant_id` bigint(20) NOT NULL COMMENT '商户ID';

-- 2. 升级商户抽奖限制表的商户ID字段
ALTER TABLE `pt_plugin_lottery_merchant_limits` 
MODIFY COLUMN `merchant_id` bigint(20) NOT NULL COMMENT '商户ID';

-- 3. 升级流水领取记录表的商户ID字段
ALTER TABLE `pt_plugin_lottery_turnover_claims` 
MODIFY COLUMN `merchant_id` bigint(20) NOT NULL COMMENT '商户ID';

-- 4. 升级分佣抽奖次数使用记录表的商户ID字段
ALTER TABLE `pt_plugin_lottery_commission_draws` 
MODIFY COLUMN `merchant_id` bigint(20) NOT NULL COMMENT '商户ID';

-- 5. 升级邀请关系表的用户ID字段
ALTER TABLE `pt_plugin_lottery_invitations` 
MODIFY COLUMN `inviter_id` bigint(20) NOT NULL COMMENT '邀请人ID',
MODIFY COLUMN `invitee_id` bigint(20) NOT NULL COMMENT '被邀请人ID';

-- 6. 升级邀请奖励记录表的用户ID字段
ALTER TABLE `pt_plugin_lottery_invitation_rewards` 
MODIFY COLUMN `inviter_id` bigint(20) NOT NULL COMMENT '邀请人ID',
MODIFY COLUMN `invitee_id` bigint(20) NOT NULL COMMENT '被邀请人ID';

-- 升级完成提示
SELECT 'Lottery plugin merchant_id fields upgraded successfully!' as message;
