<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服系统管理</title>
    <!-- 引入Element Plus -->
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <style>
        :root {
            --primary-color: #409EFF;
            --success-color: #67C23A;
            --warning-color: #E6A23C;
            --danger-color: #F56C6C;
            --info-color: #909399;
            --border-color: #EBEEF5;
            --bg-color: #F5F7FA;
            --text-primary: #303133;
            --text-regular: #606266;
            --text-secondary: #909399;
            --box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            --transition-time: 0.3s;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            color: var(--text-regular);
            overflow-x: hidden;
        }

        #app {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .app-container {
            padding: 5px;
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 100%;
            margin: 0;
            width: 100%;
            box-sizing: border-box;
            /* animation: fadeInUp 0.6s ease-out; 移除动画以提升性能 */
            /* 确保容器高度根据内容自适应，避免底部空白 */
            min-height: auto;
            gap: 5px;
        }

        .header {
            background: rgba(255, 255, 255, 0.98);
            /* backdrop-filter: blur(10px); 移除模糊效果以提升性能 */
            padding: 5px 5px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            margin-bottom: 5px;
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            letter-spacing: -0.5px;
        }

        .header-title:before {
            content: "";
            display: inline-block;
            width: 5px;
            height: 28px;
            background: linear-gradient(135deg, var(--primary-color), #66a6ff);
            margin-right: 16px;
            border-radius: 3px;
        }

        .header-controls {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .notification-switch {
            display: flex;
            align-items: center;
            background: rgba(240, 247, 255, 0.8);
            padding: 10px 16px;
            border-radius: 12px;
            border: 1px solid rgba(64, 158, 255, 0.2);
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .notification-switch:hover {
            background: rgba(240, 247, 255, 1);
            border-color: rgba(64, 158, 255, 0.4);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
        }
        
        .data-card {
            margin-bottom: 5px;
            cursor: pointer;
            transition: all 0.3s ease; /* 减少动画时间 */
            border-radius: 20px;
            overflow: hidden;
            border: none;
            background: rgba(255, 255, 255, 0.95);
            /* backdrop-filter: blur(10px); 移除模糊效果 */
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            position: relative;
            /* animation: fadeInUp 0.6s ease-out; 移除动画以提升性能 */
        }

        .data-card:nth-child(1) {
            animation-delay: 0.1s;
        }

        .data-card:nth-child(2) {
            animation-delay: 0.2s;
        }

        .data-card:nth-child(3) {
            animation-delay: 0.3s;
        }

        .data-card:nth-child(4) {
            animation-delay: 0.4s;
        }

        .data-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            border-radius: 20px;
            pointer-events: none;
        }

        .data-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
        }

        .data-card .el-card__body {
            padding: 5px 5px;
            position: relative;
            z-index: 1;
        }

        .data-value {
            font-size: 42px;
            font-weight: 800;
            color: var(--text-primary);
            margin: 16px 0;
            transition: all 0.4s ease;
            line-height: 1;
            letter-spacing: -1px;
        }

        .data-card:nth-child(1):hover .data-value {
            color: var(--primary-color);
            text-shadow: 0 0 20px rgba(64, 158, 255, 0.3);
        }

        .data-card:nth-child(2):hover .data-value {
            color: var(--success-color);
            text-shadow: 0 0 20px rgba(103, 194, 58, 0.3);
        }

        .data-card:nth-child(3):hover .data-value {
            color: var(--warning-color);
            text-shadow: 0 0 20px rgba(230, 162, 60, 0.3);
        }

        .data-card:nth-child(4):hover .data-value {
            color: var(--danger-color);
            text-shadow: 0 0 20px rgba(245, 108, 108, 0.3);
        }

        .data-title {
            font-size: 16px;
            color: var(--text-secondary);
            font-weight: 600;
            display: flex;
            align-items: center;
            letter-spacing: 0.5px;
        }

        .data-title:before {
            content: "";
            display: inline-block;
            width: 12px;
            height: 12px;
            background: linear-gradient(135deg, var(--primary-color), #66a6ff);
            margin-right: 12px;
            border-radius: 50%;
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
        }

        .data-card:nth-child(1) .data-title:before {
            background: linear-gradient(135deg, var(--primary-color), #66a6ff);
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
        }

        .data-card:nth-child(2) .data-title:before {
            background: linear-gradient(135deg, var(--success-color), #85d159);
            box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
        }

        .data-card:nth-child(3) .data-title:before {
            background: linear-gradient(135deg, var(--warning-color), #f0c674);
            box-shadow: 0 2px 8px rgba(230, 162, 60, 0.3);
        }

        .data-card:nth-child(4) .data-title:before {
            background: linear-gradient(135deg, var(--danger-color), #ff8583);
            box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
        }
        
        .status-tag-success {
            background-color: #f0f9eb;
            color: var(--success-color);
            border: 1px solid #e1f3d8;
        }
        
        .status-tag-warning {
            background-color: #fdf6ec;
            color: var(--warning-color);
            border: 1px solid #faecd8;
        }
        
        .status-tag-danger {
            background-color: #fef0f0;
            color: var(--danger-color);
            border: 1px solid #fde2e2;
        }
        
        .empty-data {
            color: var(--text-secondary);
            text-align: center;
            margin: 60px 0;
            font-size: 16px;
        }
        
        .empty-data:before {
            content: "📭";
            display: block;
            font-size: 36px;
            margin-bottom: 10px;
        }
        
        .session-item {
            transition: all 0.2s;
            border-radius: 4px;
        }
        
        .session-item:hover {
            background-color: #f5f7fa;
        }
        
        .session-title {
            font-weight: 600;
            margin-bottom: 6px;
            color: var(--text-primary);
        }
        
        .session-content {
            color: var(--text-regular);
            font-size: 13px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .session-time {
            font-size: 12px;
            color: var(--text-secondary);
        }
        
        .session-badge {
            margin-left: 10px;
        }
        
        /* 聊天消息样式 */
        .message {
            display: flex;
            margin-bottom: 12px;
            flex-direction: column;
            max-width: 80%;
        }
        
        .message.staff {
            align-self: flex-end;
            margin-left: auto;
        }
        
        .message.customer {
            align-self: flex-start;
            margin-right: auto;
        }
        
        .message-meta {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
            font-size: 12px;
            color: var(--text-secondary);
        }
        
        .message-content {
            padding: 10px 14px;
            border-radius: 12px;
            position: relative;
            word-break: break-word;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            line-height: 1.5;
        }
        
        .message.staff .message-content {
            background: linear-gradient(135deg, #4b93ff, #409EFF);
            color: white;
            text-align: left;
            border-top-right-radius: 2px;
        }
        
        .message.staff .message-content:after {
            content: "";
            position: absolute;
            top: 0;
            right: -8px;
            width: 0;
            height: 0;
            border-left: 10px solid #409EFF;
            border-top: 10px solid transparent;
        }
        
        .message.customer .message-content {
            background: white;
            border: 1px solid #eaedf3;
            text-align: left;
            border-top-left-radius: 2px;
        }
        
        .message.customer .message-content:after {
            content: "";
            position: absolute;
            top: 0;
            left: -8px;
            width: 0;
            height: 0;
            border-right: 10px solid white;
            border-top: 10px solid transparent;
        }
        
        /* 预设问题样式 */
        .preset-questions {
            background: #f8fafc;
            border-radius: 8px;
            padding: 12px 15px;
            margin: 10px 0;
            border: 1px solid #e2e8f0;
        }
        
        .preset-question-item {
            display: block;
            padding: 8px 12px;
            margin: 5px 0;
            background: #fff;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #333; /* 确保文字颜色为深色 */
        }
        
        .preset-question-item:hover {
            background: #f0f9ff;
            border-color: #bae6fd;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        
        /* 覆盖message.staff的白色文字颜色，确保预设问题中的文字是深色的 */
        .message.staff .message-content .preset-questions,
        .message.staff .message-content .preset-question-item {
            color: #333;
        }
        
        .message-sender {
            font-weight: bold;
        }
        
        .chat-messages-container {
            display: flex;
            flex-direction: column;
            background-color: #f8f9fc;
            padding: 20px;
            border-radius: 8px;
            height: auto;
            max-height: 90vh;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: rgba(0, 0, 0, 0.1) transparent;
        }
        
        .chat-messages-container::-webkit-scrollbar {
            width: 6px;
        }
        
        .chat-messages-container::-webkit-scrollbar-track {
            background: transparent;
        }
        
        .chat-messages-container::-webkit-scrollbar-thumb {
            background-color: rgba(0, 0, 0, 0.1);
            border-radius: 3px;
        }
        
        /* MessageBox 确认对话框样式 - 确保始终在屏幕中央 */
        .el-message-box {
            position: fixed !important;
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
            margin: 0 !important;
            z-index: 3000 !important;
            max-width: 420px !important;
            width: auto !important;
            border-radius: 16px !important;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
            backdrop-filter: blur(20px) !important;
            background: rgba(255, 255, 255, 0.98) !important;
            border: 1px solid rgba(255, 255, 255, 0.3) !important;
        }



        /* MessageBox 标题样式 */
        .el-message-box__title {
            color: #333333 !important;
            font-weight: 600 !important;
            font-size: 18px !important;
        }

        /* MessageBox 内容样式 */
        .el-message-box__content {
            color: #666666 !important;
            font-size: 16px !important;
            line-height: 1.5 !important;
        }

        /* MessageBox 按钮样式 */
        .el-message-box__btns {
            padding: 16px 20px !important;
            display: flex !important;
            justify-content: flex-end !important;
            gap: 12px !important;
        }

        .el-message-box__btns .el-button {
            border-radius: 12px !important;
            font-weight: 600 !important;
            padding: 10px 20px !important;
            font-size: 14px !important;
        }

        /* 移动端 MessageBox 适配 */
        @media screen and (max-width: 480px) {
            .el-message-box {
                max-width: 90% !important;
                width: 90% !important;
                margin: 0 5% !important;
                border-radius: 20px !important;
            }

            .el-message-box__content {
                font-size: 15px !important;
                padding: 20px !important;
            }

            .el-message-box__btns {
                padding: 16px 20px !important;
                flex-direction: row !important;
                justify-content: space-between !important;
            }

            .el-message-box__btns .el-button {
                flex: 1 !important;
                margin: 0 4px !important;
                padding: 12px 16px !important;
                font-size: 16px !important;
                border-radius: 16px !important;
            }
        }

        /* 对话框样式增强 */
        .el-dialog {
            display: flex;
            flex-direction: column;
            margin: 0 !important;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            max-height: 90vh;
            width: 70% !important;
            border-radius: 12px;
            overflow: hidden;
            z-index: 2001;
        }
        
        /* 防止对话框变成全屏模式 */
        .el-dialog.is-fullscreen {
            width: 70% !important;
            max-height: 90vh !important;
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
            margin: 0 !important;
        }
        
        /* 添加遮罩层的z-index确保覆盖其他元素 */
        .el-overlay {
            z-index: 2000;
        }

        /* 确保 MessageBox 遮罩层优先级最高 */
        .el-overlay.is-message-box {
            z-index: 2999 !important;
            backdrop-filter: blur(8px) !important;
            background-color: rgba(0, 0, 0, 0.4) !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
        }

        /* 确保 MessageBox 在所有设备上都居中 */
        @media screen and (min-width: 481px) {
            .el-message-box {
                position: fixed !important;
                top: 50vh !important;
                left: 50vw !important;
                transform: translate(-50%, -50%) !important;
                margin: 0 !important;
            }
        }

        /* 平板设备 MessageBox 适配 */
        @media screen and (min-width: 481px) and (max-width: 768px) {
            .el-message-box {
                max-width: 80% !important;
                width: 80% !important;
            }
        }
        
        .el-dialog__header {
            padding: 16px 20px;
            border-bottom: 1px solid var(--border-color);
            background-color: #fafbfc;
        }
        
        .el-dialog__title {
            font-weight: 600;
            font-size: 18px;
        }
        
        .el-dialog__body {
            overflow: auto;
            flex: 1;
            padding: 20px;
            max-height: calc(90vh - 100px);
        }
        
        .el-dialog__footer {
            padding: 14px 20px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            flex-wrap: wrap;
            background-color: #fafbfc;
        }
        
        /* 表格样式增强 - 性能优化，自适应高度 */
        .el-table {
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06); /* 减少阴影复杂度 */
            background: rgba(255, 255, 255, 0.98);
            /* backdrop-filter: blur(10px); 移除模糊效果 */
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #333333;
            /* 移除固定高度，让表格根据内容自适应 */
        }

        /* 表格行高度设置 */
        .el-table .el-table__row {
            height: 60px;
        }

        /* 表格头部高度 */
        .el-table .el-table__header-wrapper {
            height: 60px;
        }

        /* 表格体高度自适应 - 根据内容动态调整 */
        .el-table .el-table__body-wrapper {
            /* 移除固定最大高度，让内容自然展示 */
            overflow-y: visible;
        }

        /* 表格体自适应高度 */
        .el-table .el-table__body {
            min-height: auto;
        }

        /* 当数据较少时，减少底部空白 */
        .el-table--fit {
            height: auto !important;
        }

        /* 确保表格在没有数据时显示合适的高度 */
        .el-table .el-table__empty-block {
            min-height: 100px;
        }

        /* 表格滚动条样式 */
        .el-table .el-table__body-wrapper::-webkit-scrollbar {
            width: 8px;
        }

        .el-table .el-table__body-wrapper::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 4px;
        }

        .el-table .el-table__body-wrapper::-webkit-scrollbar-thumb {
            background: rgba(64, 158, 255, 0.3);
            border-radius: 4px;
            transition: background 0.3s ease;
        }

        .el-table .el-table__body-wrapper::-webkit-scrollbar-thumb:hover {
            background: rgba(64, 158, 255, 0.5);
        }

        /* 表格容器样式 - 自适应高度 */
        .table-container {
            position: relative;
            overflow: visible; /* 改为visible，让内容自然展示 */
            border-radius: 16px;
            /* 确保容器能够根据内容自适应高度 */
            height: auto;
            min-height: 150px; /* 设置最小高度 */
        }

        /* 空数据状态优化 */
        .el-table__empty-block {
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 表格加载状态优化 */
        .el-loading-mask {
            border-radius: 16px;
        }

        .el-table th {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0) !important;
            font-weight: 700;
            color: #333333 !important;
            border-bottom: 2px solid rgba(64, 158, 255, 0.1);
            letter-spacing: 0.5px;
        }

        .el-table td {
            color: #333333 !important;
        }

        .el-table tr {
            transition: all 0.3s ease;
        }

        .el-table tr:hover td {
            background: linear-gradient(135deg, #f0f7ff, #e6f4ff) !important;
            transform: scale(1.01);
            color: #333333 !important;
        }
        
        /* 按钮样式增强 */
        .el-button--primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            border-radius: 12px;
            font-weight: 600;
            letter-spacing: 0.5px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .el-button--primary:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
            transform: translateY(-3px) scale(1.05);
        }

        .el-button--danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            border: none;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
            border-radius: 12px;
            font-weight: 600;
            letter-spacing: 0.5px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .el-button--danger:hover {
            background: linear-gradient(135deg, #ee5a52 0%, #ff6b6b 100%);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.6);
            transform: translateY(-3px) scale(1.05);
        }

        .el-button--success {
            background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
            border: none;
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.4);
            border-radius: 12px;
            font-weight: 600;
            letter-spacing: 0.5px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .el-button--success:hover {
            background: linear-gradient(135deg, #44a08d 0%, #4ecdc4 100%);
            box-shadow: 0 8px 25px rgba(78, 205, 196, 0.6);
            transform: translateY(-3px) scale(1.05);
        }
        
        /* 标签页样式 */
        .el-tabs__item {
            font-weight: 500;
            padding: 0 20px;
            font-size: 15px;
        }
        
        .el-tabs__item.is-active {
            color: var(--primary-color);
            font-weight: 600;
        }
        
        .el-tabs__active-bar {
            height: 3px;
            border-radius: 3px;
        }
        
        /* 卡片样式 - 性能优化 */
        .el-card {
            border-radius: 20px;
            overflow: hidden;
            transition: all 0.3s ease; /* 简化动画 */
            border: none;
            background: rgba(255, 255, 255, 0.98);
            /* backdrop-filter: blur(10px); 移除模糊效果 */
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06); /* 减少阴影复杂度 */
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #333333;
        }

        .el-card:hover {
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
            transform: translateY(-4px);
        }

        .el-card__header {
            padding: 24px 28px;
            font-weight: 700;
            font-size: 18px;
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border-bottom: 2px solid rgba(64, 158, 255, 0.1);
            letter-spacing: 0.5px;
            color: #333333;
        }

        .el-card__body {
            color: #333333;
        }
        
        /* 分页样式 */
        .el-pagination.is-background .el-pager li:not(.is-disabled).is-active {
            background-color: var(--primary-color);
            box-shadow: 0 2px 10px rgba(64, 158, 255, 0.3);
        }
        
        /* 快速回复区域 */
        .quick-reply-section {
            margin-bottom: 5px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(240, 247, 255, 0.8));
            backdrop-filter: blur(10px);
            padding: 5px;
            border-radius: 20px;
            border: 1px solid rgba(64, 158, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            position: relative;
            overflow: hidden;
        }

        .quick-reply-section:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 6px;
            height: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 0 3px 3px 0;
        }

        .quick-reply-section h4 {
            margin-top: 0;
            margin-bottom: 5px;
            color: var(--text-primary);
            font-weight: 700;
            font-size: 18px;
            display: flex;
            align-items: center;
            letter-spacing: 0.5px;
        }

        .quick-reply-section h4:before {
            content: '';
            display: inline-block;
            width: 10px;
            height: 10px;
            background: linear-gradient(135deg, var(--primary-color), #66a6ff);
            margin-right: 12px;
            border-radius: 50%;
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
        }

        .quick-reply-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }

        .quick-reply-buttons .el-button {
            border-radius: 12px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(64, 158, 255, 0.2);
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(5px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            font-weight: 500;
            letter-spacing: 0.3px;
            color: #333333 !important;
        }

        .quick-reply-buttons .el-button:hover {
            transform: translateY(-3px) scale(1.05);
            background: linear-gradient(135deg, #f0f7ff, #e6f4ff);
            border-color: rgba(64, 158, 255, 0.4);
            box-shadow: 0 8px 25px rgba(64, 158, 255, 0.15);
            color: #333333 !important;
        }
        
        @media screen and (max-width: 767px) {
            .quick-reply-section {
                padding: 5px;
                margin-bottom: 5px;
            }

            .quick-reply-buttons {
                gap: 5px;
            }
            
            .quick-reply-buttons .el-button {
                margin-bottom: 5px;
            }
        }
        
        /* 回复输入区域 */
        .reply-input-container {
            display: flex;
            flex-direction: row;
            background: white;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
            gap: 12px;
        }
        
        @media screen and (max-width: 767px) {
            .reply-input-container {
                flex-direction: column;
            }
            
            .reply-input-container .el-button,
            .reply-input-container .el-upload {
                width: 100%;
                margin-top: 8px;
            }
            
            /* 上传按钮居中 */
            .reply-input-container .el-upload {
                text-align: center;
                display: block;
            }
        }
        
        /* 响应式设计 */
        /* 大屏设备 - 超过1400px */
        @media screen and (min-width: 1401px) {
            .app-container {
                padding: 5px;
                max-width: 100%;
            }

            .el-dialog {
                width: 60% !important;
                max-height: 85vh;
            }

            .data-value {
                font-size: 48px;
            }

            .header-title {
                font-size: 32px;
            }
        }

        /* 中等电脑屏幕 - 992px到1400px */
        @media screen and (min-width: 992px) and (max-width: 1400px) {
            .app-container {
                padding: 5px;
                max-width: 100%;
            }

            .el-dialog {
                width: 70% !important;
                max-height: 85vh;
            }

            .data-value {
                font-size: 38px;
            }
        }

        /* 平板和小型电脑屏幕 - 768px到991px */
        @media screen and (min-width: 768px) and (max-width: 991px) {
            .app-container {
                padding: 5px;
            }

            .el-dialog {
                width: 85% !important;
                max-height: 90vh;
            }

            .data-value {
                font-size: 36px;
            }

            .header-title {
                font-size: 24px;
            }
        }
        
        /* 平板设备 - 576px到767px */
        @media screen and (min-width: 576px) and (max-width: 767px) {
            .el-dialog {
                width: 95% !important;
                max-height: 95vh;
            }
            
            .el-col-6 {
                width: 50%;
            }
            
            .data-value {
                font-size: 28px;
            }
            
            .message {
                max-width: 90%;
            }
            
            .app-container {
                padding: 5px;
            }

            .header {
                padding: 5px;
                flex-direction: column;
                gap: 5px;
            }
            
            /* 调整表头和操作列的大小 */
            .el-table-column--selection {
                width: 45px !important;
            }
            
            .el-table__header th:last-child {
                width: 130px !important;
            }
            
            /* 优化表格在平板上的显示 */
            .el-table {
                font-size: 13px;
                /* 移除固定高度，让表格自适应 */
            }

            .el-table .el-table__row {
                height: 55px;
            }

            .el-table .el-table__header-wrapper {
                height: 55px;
            }

            .el-table .el-table__body-wrapper {
                /* 移除固定最大高度 */
                overflow-y: visible;
            }
        }
        
        /* 手机横屏 - 481px到575px */
        @media screen and (min-width: 481px) and (max-width: 575px) {
            .el-col-6 {
                width: 50%;
                margin-bottom: 12px;
            }
            
            .el-table__fixed-right {
                display: none !important;
            }
            
            .data-value {
                font-size: 26px;
            }
            
            .el-dialog__body {
                padding: 16px;
            }
            
            /* 优化对话框内的表单布局 */
            .el-descriptions {
                font-size: 13px;
            }
            
            .el-descriptions-item__label {
                width: 80px;
            }
            
            /* 操作区域优化 */
            .el-dialog__footer {
                display: flex;
                justify-content: space-between;
            }
            
            /* 头部调整为列布局 */
            .header {
                padding: 14px;
                flex-direction: column;
                gap: 12px;
            }
        }
        
        /* 手机竖屏 - 小于480px */
        @media screen and (max-width: 480px) {
            body {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            }

            .app-container {
                padding: 5px;
                gap: 5px;
            }

            .header {
                padding: 20px 16px;
                border-radius: 20px;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
                margin-bottom: 20px;
            }

            .header-title {
                font-size: 24px;
                font-weight: 800;
                background: linear-gradient(135deg, #667eea, #764ba2);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                text-align: center;
                margin-bottom: 16px;
            }

            .header-controls {
                width: 100%;
                justify-content: center;
            }

            .notification-switch {
                background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
                border: 1px solid rgba(102, 126, 234, 0.3);
                border-radius: 16px;
                padding: 12px 20px;
                backdrop-filter: blur(10px);
            }

            /* 数据卡片网格布局 - 优化小屏幕显示 */
            .el-row {
                display: grid !important;
                grid-template-columns: 1fr 1fr;
                gap: 5px;
                margin: 0 !important;
            }

            .el-col-6 {
                width: 100% !important;
                padding: 0 !important;
                margin: 0 !important;
            }

            .data-card {
                margin-bottom: 0;
                border-radius: 16px;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
                border: 1px solid rgba(255, 255, 255, 0.3);
                min-height: 100px;
                display: flex;
                align-items: center;
            }

            .data-card .el-card__body {
                padding: 5px 5px;
                text-align: center;
                width: 100%;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
            }

            .data-value {
                font-size: 28px;
                font-weight: 800;
                margin: 4px 0;
                background: linear-gradient(135deg, #667eea, #764ba2);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                line-height: 1;
            }

            .data-title {
                font-size: 12px;
                font-weight: 600;
                color: rgba(0, 0, 0, 0.7);
                justify-content: center;
                margin: 0;
                line-height: 1.2;
                text-align: center;
            }

            .data-title:before {
                display: none;
            }

            /* 客服系统状态卡片特殊处理 */
            .data-card:last-child .data-title {
                font-size: 11px;
                line-height: 1.1;
            }

            /* 表格容器优化 */
            .el-card {
                border-radius: 20px;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.3);
                margin-bottom: 20px;
                color: #333333;
            }

            .el-card__header {
                background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
                border-radius: 20px 20px 0 0;
                padding: 16px 20px;
                font-size: 18px;
                font-weight: 700;
                color: #333333 !important;
            }

            .el-card__body {
                padding: 16px;
                color: #333333;
            }

            /* 标签页优化 */
            .el-tabs__header {
                margin-bottom: 12px;
            }

            .el-tabs__nav-wrap {
                background: rgba(255, 255, 255, 0.9);
                border-radius: 12px;
                padding: 3px;
                backdrop-filter: blur(10px);
            }

            .el-tabs__item {
                padding: 8px 12px;
                font-size: 13px;
                font-weight: 600;
                border-radius: 10px;
                transition: all 0.3s ease;
                margin: 0 2px;
            }

            .el-tabs__item.is-active {
                background: linear-gradient(135deg, #667eea, #764ba2);
                color: white !important;
            }

            /* 标签页按钮优化 */
            .el-tabs__nav .el-button {
                padding: 6px 10px;
                font-size: 12px;
                border-radius: 10px;
                margin: 0 2px;
            }

            /* 表格优化 */
            .el-table {
                border-radius: 16px;
                font-size: 12px;
                background: transparent;
                color: #333333;
                /* 移除固定高度，让表格自适应 */
            }

            .el-table .el-table__row {
                height: 50px;
            }

            .el-table .el-table__header-wrapper {
                height: 50px;
            }

            .el-table .el-table__body-wrapper {
                /* 移除固定最大高度 */
                overflow-y: visible;
            }

            .el-table td, .el-table th {
                padding: 8px 4px;
                border: none;
                color: #333333 !important;
                font-size: 12px;
            }

            .el-table th {
                font-size: 13px;
                font-weight: 700;
            }

            .el-table tr:nth-child(even) {
                background: rgba(255, 255, 255, 0.5);
            }

            .el-table tr:nth-child(even) td {
                color: #333333 !important;
            }

            /* 表格按钮优化 */
            .el-table .el-button {
                padding: 4px 8px;
                font-size: 11px;
                border-radius: 8px;
                margin: 1px;
            }

            /* 状态标签优化 */
            .el-tag {
                font-size: 11px;
                padding: 2px 6px;
                border-radius: 8px;
            }

            /* 对话框优化 */
            .el-dialog {
                width: 95% !important;
                margin: 5vh auto !important;
                border-radius: 20px;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
                color: #333333;
            }

            .el-dialog__header {
                padding: 20px;
                background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
                border-radius: 20px 20px 0 0;
                color: #333333;
            }

            .el-dialog__body {
                padding: 20px;
                color: #333333;
            }

            .el-dialog__title {
                color: #333333 !important;
            }

            /* 聊天消息容器 */
            .chat-messages-container {
                padding: 16px;
                border-radius: 20px;
                background: rgba(255, 255, 255, 0.9);
                backdrop-filter: blur(20px);
                max-height: 60vh;
                color: #333333;
            }

            /* 快速回复区域 */
            .quick-reply-section {
                padding: 16px;
                border-radius: 20px;
                background: rgba(255, 255, 255, 0.9);
                backdrop-filter: blur(20px);
                margin-bottom: 16px;
                color: #333333;
            }

            .quick-reply-buttons {
                gap: 8px;
            }

            .quick-reply-buttons .el-button {
                border-radius: 16px;
                font-size: 13px;
                padding: 8px 16px;
                color: #333333 !important;
                background: rgba(255, 255, 255, 0.9) !important;
            }

            .quick-reply-buttons .el-button:hover {
                color: #333333 !important;
                background: rgba(240, 247, 255, 0.9) !important;
            }
        }

        /* 超小屏幕优化 - 小于400px */
        @media screen and (max-width: 400px) {
            .app-container {
                padding: 5px;
            }

            .header {
                padding: 16px 12px;
                margin-bottom: 16px;
            }

            .header-title {
                font-size: 20px;
            }

            /* 数据卡片单列显示 */
            .el-row {
                display: grid !important;
                grid-template-columns: 1fr 1fr;
                gap: 5px;
                margin: 0 !important;
            }

            .data-card {
                min-height: 85px;
                border-radius: 12px;
            }

            .data-card .el-card__body {
                padding: 5px 5px;
            }

            .data-value {
                font-size: 24px;
                margin: 2px 0;
            }

            .data-title {
                font-size: 11px;
                line-height: 1.1;
            }

            .data-card:last-child .data-title {
                font-size: 10px;
            }

            /* 表格和卡片间距优化 */
            .el-card {
                margin-bottom: 12px;
            }

            .el-card__header {
                padding: 12px 16px;
                font-size: 16px;
            }

            .el-card__body {
                padding: 12px;
            }
        }

        /* 手机横屏和小平板 - 481px到767px */
        @media screen and (min-width: 481px) and (max-width: 767px) {
            body {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            }

            .app-container {
                padding: 5px;
            }

            .header {
                padding: 24px;
                border-radius: 20px;
                flex-direction: row;
                align-items: center;
                justify-content: space-between;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
            }

            .header-title {
                font-size: 26px;
                margin-bottom: 0;
                background: linear-gradient(135deg, #667eea, #764ba2);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }

            .header-controls {
                width: auto;
                flex-direction: row;
            }

            /* 数据卡片2x2网格 */
            .el-row {
                display: grid !important;
                grid-template-columns: 1fr 1fr;
                gap: 5px;
                margin: 0 !important;
            }

            .el-col-6 {
                width: 100% !important;
                padding: 0 !important;
            }

            .data-card {
                margin-bottom: 0;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                border-radius: 20px;
            }

            .data-card .el-card__body {
                padding: 5px 5px;
                text-align: center;
            }

            .data-value {
                font-size: 36px;
                background: linear-gradient(135deg, #667eea, #764ba2);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }

            .data-title {
                font-size: 15px;
                justify-content: center;
            }

            .data-title:before {
                display: none;
            }

            .el-card {
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                border-radius: 20px;
            }

            .el-dialog {
                width: 85% !important;
                max-height: 85vh;
                border-radius: 20px;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
            }

            .chat-messages-container {
                padding: 20px;
                border-radius: 20px;
                background: rgba(255, 255, 255, 0.9);
                backdrop-filter: blur(20px);
            }

            .quick-reply-section {
                padding: 20px;
                border-radius: 20px;
                background: rgba(255, 255, 255, 0.9);
                backdrop-filter: blur(20px);
            }

            .quick-reply-buttons .el-button {
                color: #333333 !important;
                background: rgba(255, 255, 255, 0.9) !important;
            }

            .quick-reply-buttons .el-button:hover {
                color: #333333 !important;
                background: rgba(240, 247, 255, 0.9) !important;
            }

            .el-table {
                font-size: 14px;
                background: transparent;
                /* 移除固定高度，让表格自适应 */
            }

            .el-table .el-table__row {
                height: 52px;
            }

            .el-table .el-table__header-wrapper {
                height: 52px;
            }

            .el-table .el-table__body-wrapper {
                /* 移除固定最大高度 */
                overflow-y: visible;
            }

            .el-tabs__nav-wrap {
                background: rgba(255, 255, 255, 0.9);
                border-radius: 16px;
                padding: 4px;
                backdrop-filter: blur(10px);
            }

            .el-tabs__item.is-active {
                background: linear-gradient(135deg, #667eea, #764ba2);
                color: white !important;
                border-radius: 12px;
            }
        }

        /* 移动端触摸优化 */
        @media screen and (max-width: 767px) {
            /* 增大点击区域 */
            .el-button {
                min-height: 44px;
                padding: 12px 20px;
                font-size: 16px;
                border-radius: 16px;
                font-weight: 600;
            }

            /* 表格行点击区域优化 */
            .el-table__row {
                cursor: pointer;
            }

            .el-table__row:active {
                background: rgba(102, 126, 234, 0.1) !important;
            }

            /* 输入框优化 */
            .el-input__inner {
                min-height: 44px;
                font-size: 16px;
                border-radius: 12px;
            }

            /* 开关组件优化 */
            .el-switch {
                transform: scale(1.2);
            }

            /* 标签页触摸优化 */
            .el-tabs__item {
                min-height: 44px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            /* 分页组件优化 */
            .el-pagination {
                text-align: center;
                padding: 20px 0;
            }

            .el-pagination .el-pager li {
                min-width: 44px;
                min-height: 44px;
                line-height: 44px;
                margin: 0 4px;
                border-radius: 12px;
            }

            /* 消息气泡触摸反馈 */
            .message-content {
                transition: all 0.2s ease;
            }

            .message-content:active {
                transform: scale(0.98);
            }

            /* 卡片触摸反馈 */
            .data-card:active {
                transform: scale(0.98);
            }

            /* 防止文本选择 */
            .data-card, .el-button, .el-tabs__item {
                -webkit-user-select: none;
                -moz-user-select: none;
                -ms-user-select: none;
                user-select: none;
            }

            /* 滚动条在移动端隐藏 */
            ::-webkit-scrollbar {
                width: 0;
                height: 0;
            }

            /* iOS样式优化 */
            .el-input__inner, .el-textarea__inner {
                -webkit-appearance: none;
                border-radius: 12px;
            }

            /* 状态指示器优化 */
            .el-badge__content {
                font-size: 12px;
                min-width: 20px;
                height: 20px;
                line-height: 20px;
            }
        }
        
        /* 对话框内容样式 */
        .session-detail-container {
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        
        .contact-info-section {
            margin-bottom: 16px;
        }
        
        .message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .message-header h3 {
            margin: 0;
            font-weight: 600;
            color: var(--text-primary);
            font-size: 16px;
        }
        
        .message-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .reply-section {
            margin-top: 16px;
        }
        
        .reply-container {
            display: flex;
            flex-direction: column;
        }
        
        /* 确保图片弹窗居中且大小合适 */
        .el-dialog.image-preview-dialog {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .el-dialog.image-preview-dialog .el-dialog__body {
            padding: 10px;
            max-height: none;
        }
        
        /* 手机端适配 */
        @media screen and (max-width: 768px) {
            .message-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .message-actions {
                width: 100%;
                justify-content: space-between;
            }
            
            .contact-info-section .el-descriptions {
                width: 100%;
                overflow-x: auto;
            }
            
            .header-controls {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .notification-switch {
                width: 100%;
                justify-content: space-between;
                margin-bottom: 5px;
            }
            
            .header-controls .el-button {
                width: 100%;
            }
        }
        
        /* 以下是美化会话记录的新CSS样式 */
        /* 消息日期分组 */
        .message-date-divider {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
            position: relative;
            text-align: center;
            color: var(--text-secondary);
            font-size: 13px;
        }
        
        .message-date-divider:before, .message-date-divider:after {
            content: "";
            flex: 1;
            border-bottom: 1px solid #e0e7ff;
            margin: 0 10px;
        }
        
        .message-date-divider span {
            padding: 3px 10px;
            background-color: rgba(240, 247, 255, 0.8);
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            font-weight: 500;
        }
        
        /* 改进的消息气泡 */
        .message-content {
            transition: all 0.2s ease;
        }
        
        .message.staff .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            border-radius: 20px 20px 4px 20px;
            animation: fadeInRight 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .message.staff .message-content:hover {
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
            transform: translateY(-2px) scale(1.02);
        }

        .message.customer .message-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 0, 0, 0.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border-radius: 20px 20px 20px 4px;
            animation: fadeInLeft 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .message.customer .message-content:hover {
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px) scale(1.02);
        }
        
        /* 消息状态指示器 */
        .message-status {
            margin-left: 5px;
            font-size: 12px;
            display: inline-flex;
            align-items: center;
        }
        
        .message-status i {
            margin-right: 3px;
            font-size: 12px;
        }
        
        /* 消息时间美化 */
        .message-time {
            font-size: 11px;
            opacity: 0.8;
            display: flex;
            align-items: center;
        }
        
        .message-time i {
            margin-right: 3px;
            font-size: 12px;
        }
        
        /* 图片消息美化 */
        .image-message {
            position: relative;
            transition: all 0.3s ease;
            max-width: 240px;
        }
        
        .image-message:after {
            content: "查看大图";
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 8px;
            background: rgba(0, 0, 0, 0.6);
            color: white;
            font-size: 12px;
            text-align: center;
            transform: translateY(100%);
            transition: transform 0.2s ease;
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            z-index: 2;
        }
        
        .image-message:hover:after {
            transform: translateY(0);
        }
        
        /* 文件消息美化 */
        .file-message {
            display: flex;
            align-items: center;
            padding: 12px;
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid #e0e7ff;
            border-radius: 8px;
            transition: all 0.2s ease;
        }
        
        .message.staff .file-message {
            background: rgba(255, 255, 255, 0.9);
            color: #333;
        }
        
        .file-message:hover {
            background: #f5f9ff;
            border-color: #c0d8ff;
        }
        
        .file-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #e0ecff;
            border-radius: 8px;
            margin-right: 12px;
        }
        
        .file-info {
            flex: 1;
        }
        
        .file-name {
            font-weight: 500;
            margin-bottom: 3px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 160px;
        }
        
        .file-size {
            font-size: 11px;
            color: #666;
        }
        
        .file-download {
            padding: 4px 8px;
            background: #f0f7ff;
            border-radius: 4px;
            color: var(--primary-color);
            font-size: 12px;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        .file-download:hover {
            background: #e0ecff;
        }
        
        /* 动画效果 */
        @keyframes fadeInLeft {
            from {
                opacity: 0;
                transform: translateX(-40px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateX(0) scale(1);
            }
        }

        @keyframes fadeInRight {
            from {
                opacity: 0;
                transform: translateX(40px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateX(0) scale(1);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(40px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.4);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
            }
        }

        @keyframes shimmer {
            0% {
                background-position: -200px 0;
            }
            100% {
                background-position: calc(200px + 100%) 0;
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .shimmer {
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            background-size: 200px 100%;
            animation: shimmer 2s infinite;
        }

        /* 加载动画 */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% {
                transform: translate3d(0,0,0);
            }
            40%, 43% {
                transform: translate3d(0, -30px, 0);
            }
            70% {
                transform: translate3d(0, -15px, 0);
            }
            90% {
                transform: translate3d(0, -4px, 0);
            }
        }

        @keyframes glow {
            0% {
                box-shadow: 0 0 5px rgba(102, 126, 234, 0.5);
            }
            50% {
                box-shadow: 0 0 20px rgba(102, 126, 234, 0.8), 0 0 30px rgba(102, 126, 234, 0.6);
            }
            100% {
                box-shadow: 0 0 5px rgba(102, 126, 234, 0.5);
            }
        }

        /* 微交互效果 */
        .bounce-enter {
            animation: bounce 0.6s ease-out;
        }

        .glow-effect {
            animation: glow 2s ease-in-out infinite;
        }

        /* 现代化加载指示器 */
        .modern-loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(102, 126, 234, 0.3);
            border-radius: 50%;
            border-top-color: #667eea;
            animation: spin 1s ease-in-out infinite;
        }

        /* 成功状态动画 */
        @keyframes checkmark {
            0% {
                stroke-dashoffset: 100;
            }
            100% {
                stroke-dashoffset: 0;
            }
        }

        .success-checkmark {
            stroke-dasharray: 100;
            animation: checkmark 0.6s ease-in-out;
        }
        
        /* 会话消息容器优化 */
        .chat-messages-container {
            display: flex;
            flex-direction: column;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            background-image:
                radial-gradient(circle at 25px 25px, rgba(255, 255, 255, 0.3) 2px, transparent 2px),
                radial-gradient(circle at 75px 75px, rgba(64, 158, 255, 0.1) 1px, transparent 1px);
            background-size: 100px 100px;
            padding: 32px;
            border-radius: 20px;
            height: auto;
            max-height: 90vh;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: rgba(64, 158, 255, 0.3) transparent;
            box-shadow:
                inset 0 0 30px rgba(0, 0, 0, 0.05),
                0 8px 32px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }
        
        /* 添加消息操作按钮样式 */
        .message-actions-btn {
            opacity: 0;
            position: absolute;
            right: -30px;
            top: 0;
            transition: all 0.2s ease;
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .message.customer .message-actions-btn {
            left: -30px;
            right: auto;
        }
        
        .message-content {
            position: relative;
        }
        
        .message-content:hover .message-actions-btn {
            opacity: 1;
        }
        
        .message-action-icon {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: #666;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            font-size: 14px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .message-action-icon:hover {
            background: rgba(64, 158, 255, 0.1);
            color: #409EFF;
            transform: scale(1.15);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
        }

        .message-action-icon svg {
            transition: all 0.3s ease;
            opacity: 0.8;
        }

        .message-action-icon:hover svg {
            opacity: 1;
            transform: scale(1.1);
        }

        /* 复制按钮特殊样式 */
        .message-action-icon:first-child:hover {
            background: rgba(52, 199, 89, 0.1);
            color: #34c759;
            box-shadow: 0 4px 12px rgba(52, 199, 89, 0.2);
        }

        /* 撤回按钮特殊样式 */
        .message-action-icon:last-child:hover {
            background: rgba(255, 149, 0, 0.1);
            color: #ff9500;
            box-shadow: 0 4px 12px rgba(255, 149, 0, 0.2);
        }
        
        /* 已撤回消息样式 */
        .message-recalled {
            font-style: italic;
            color: #999;
            background: rgba(240, 240, 240, 0.5) !important;
            border: 1px dashed #ddd !important;
        }
        
        .message-recalled:after {
            display: none !important;
        }
        
        /* 撤回消息文字样式 */
        .recalled-message-text {
            padding: 10px;
            font-style: italic;
            color: #999;
            text-align: center;
        }
        
        /* 消息图片样式 */
        .message-image-container {
            width: 100%;
            max-width: 240px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            margin: 5px 0;
        }
        
        .message-image {
            width: 100%;
            height: auto;
            transition: transform 0.3s ease;
            cursor: pointer;
        }
        
        .message-image:hover {
            transform: scale(1.02);
        }
        
        /* 对话框最大化样式 */
        .el-dialog.is-maximized {
            width: 100% !important;
            height: 100% !important;
            margin: 0 !important;
            position: fixed;
            top: 0 !important;
            left: 0 !important;
            transform: none !important;
            max-height: 100vh !important;
            border-radius: 0;
        }
        
        .el-dialog.is-maximized .el-dialog__body {
            height: calc(100vh - 120px) !important;
            max-height: calc(100vh - 120px) !important;
        }
        
        .el-dialog.is-maximized .chat-messages-container {
            height: calc(100vh - 350px) !important;
            max-height: calc(100vh - 350px) !important;
        }
        
        /* 对话框最大化样式和按钮 */
        .el-dialog__header .maximize-btn {
            position: absolute;
            top: 20px;
            right: 50px;
            padding: 0;
            border: none;
            background: transparent;
            font-size: 16px;
            cursor: pointer;
            color: #909399;
        }
        
        .el-dialog__header .maximize-btn:hover {
            color: #409EFF;
        }
        
        /* 确保关闭按钮位置正确 */
        .el-dialog__header .el-dialog__close {
            font-size: 16px;
            margin-left: 15px;
        }
        
        /* 美化滚动条 */
        ::-webkit-scrollbar {
            width: 12px;
            height: 12px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            backdrop-filter: blur(5px);
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.6), rgba(118, 75, 162, 0.6));
            border-radius: 10px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.8), rgba(118, 75, 162, 0.8));
            transform: scale(1.1);
        }

        ::-webkit-scrollbar-corner {
            background: transparent;
        }
        
        /* 角色标签样式改进 */
        .message-sender-role {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 13px;
            font-weight: 500;
            line-height: 1.4;
        }
        
        .message-sender-role i {
            margin-right: 4px;
        }
        
        .customer-tag {
            background-color: #e6f4ff;
            color: #0958d9;
        }
        
        .merchant-tag {
            background-color: #fff7e6;
            color: #d46b08;
        }
        
        .staff-tag {
            background-color: #fff1f0;
            color: #cf1322;
        }
        
        /* 移动端适配 */
        @media (max-width: 768px) {
            .message-sender-role {
                font-size: 12px;
                padding: 2px 6px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="app-container">
            <div class="header">
                <div class="header-title">客服系统管理</div>
                <div class="header-controls">
                    <el-tooltip :content="notificationEnabled ? '桌面通知已启用' : '桌面通知已禁用'" placement="bottom">
                        <div class="notification-switch">
                            <span style="margin-right: 8px;">桌面通知</span>
                            <el-switch
                                v-model="notificationEnabled"
                                @change="val => {
                                    if(val && notificationPermission !== 'granted') {
                                        requestNotificationPermission();
                                    } else {
                                        saveNotificationSetting(val);
                                    }
                                }"
                                active-color="#67C23A">
                            </el-switch>
                        </div>
                    </el-tooltip>
                    <el-button type="success" size="default" @click="goToChatPage" style="margin-right: 10px;">
                        <i class="el-icon-chat-dot-round" style="margin-right: 5px;"></i>聊天页面
                    </el-button>
                    <el-button type="primary" size="default" @click="goToSettings">
                        <i class="el-icon-setting" style="margin-right: 5px;"></i>系统设置
                    </el-button>
                </div>
            </div>
            
            <el-row :gutter="20">
                <el-col :span="6">
                    <el-card class="data-card" @click="changeTabToSessions('all')">
                        <div class="data-value">{{ totalSessions }}</div>
                        <div class="data-title">总会话数</div>
                    </el-card>
                </el-col>
                <el-col :span="6">
                    <el-card class="data-card" @click="changeTabToSessions('open')">
                        <div class="data-value">{{ openSessions }}</div>
                        <div class="data-title">进行中会话</div>
                    </el-card>
                </el-col>
                <el-col :span="6">
                    <el-card class="data-card" @click="changeTabToUnread">
                        <div class="data-value">{{ unreadMessages }}</div>
                        <div class="data-title">未读消息</div>
                    </el-card>
                </el-col>
                <el-col :span="6">
                    <el-card class="data-card">
                        <div class="data-value">
                            <el-switch
                                v-model="chatEnabled"
                                active-color="#67C23A"
                                @change="toggleChatStatus">
                            </el-switch>
                        </div>
                        <div class="data-title">客服系统状态</div>
                    </el-card>
                </el-col>
            </el-row>
            
            <el-card style="margin-top: 5px;">
                <template #header>
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <el-tabs v-model="activeTab">
                            <el-tab-pane label="全部会话" name="all"></el-tab-pane>
                            <el-tab-pane label="进行中" name="open"></el-tab-pane>
                            <el-tab-pane label="已关闭" name="closed"></el-tab-pane>
                        </el-tabs>
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <el-button 
                                size="small" 
                                type="primary" 
                                @click="refreshSessions" 
                                :loading="loading"
                                title="刷新会话列表">
                                <i class="el-icon-refresh-right"></i> 刷新会话
                            </el-button>
                            <el-button 
                                size="small" 
                                type="success" 
                                @click="markAllSessionsAsRead" 
                                :loading="markingAsRead"
                                title="将所有未读消息标记为已读"
                                :disabled="sessions.length === 0">
                                <i class="el-icon-check"></i> 批量已读
                            </el-button>
                            <el-button 
                                size="small" 
                                type="danger" 
                                @click="toggleSessionSelection" 
                                :disabled="sessions.length === 0">
                                {{ isSelectingSessions ? '取消选择' : '批量操作' }}
                            </el-button>
                            <el-button 
                                size="small" 
                                type="danger" 
                                v-if="isSelectingSessions" 
                                @click="deleteSelectedSessions" 
                                :disabled="selectedSessions.length === 0">
                                删除所选({{ selectedSessions.length }})
                            </el-button>
                            <el-button 
                                size="small" 
                                type="warning" 
                                v-if="isSelectingSessions" 
                                @click="closeSelectedSessions" 
                                :disabled="selectedSessions.length === 0 || !hasOpenSessionsSelected">
                                关闭所选({{ openSelectedSessionsCount }})
                            </el-button>
                            <el-input
                                placeholder="搜索会话"
                                v-model="searchKeyword"
                                style="width: 200px;"
                                clearable
                                prefix-icon="el-icon-search">
                            </el-input>
                        </div>
                    </div>
                </template>
                
                <div class="table-container">
                    <el-table
                        :data="filteredSessions"
                        style="width: 100%"
                        v-loading="loading"
                        @row-click="handleSessionClick"
                        @selection-change="handleSessionSelectionChange"
                        :max-height="getMaxTableHeight()"
                        fit>
                    <el-table-column
                        type="selection"
                        width="55"
                        v-if="isSelectingSessions">
                    </el-table-column>
                    <el-table-column label="会话标题" prop="title" min-width="200">
                        <template #default="scope">
                            <div class="session-title">
                                <el-input
                                    v-if="scope.row.isEditingTitle"
                                    v-model="scope.row.editingTitle"
                                    size="small"
                                    @blur="saveSessionTitle(scope.row)"
                                    @keyup.enter="saveSessionTitle(scope.row)"
                                    placeholder="请输入会话标题"
                                    style="width: 200px;">
                                </el-input>
                                <template v-else>
                                    <span @dblclick="startEditTitle(scope.row)">
                                        {{ scope.row.title }}
                                        <el-badge v-if="scope.row.unread_count > 0" :value="scope.row.unread_count" class="session-badge" type="danger"></el-badge>
                                    </span>
                                    <el-button
                                        type="text"
                                        size="small"
                                        @click.stop="startEditTitle(scope.row)"
                                        style="margin-left: 5px;">
                                        <i class="el-icon-edit"></i>
                                    </el-button>
                                </template>
                            </div>
                            <div class="session-content">{{ scope.row.last_message }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="联系人" width="200">
                        <template #default="scope">
                            <div v-if="scope.row.contact">
                                <div>{{ scope.row.contact.name }}</div>
                                <div style="font-size: 12px; color: #909399;">{{ scope.row.contact.email }}</div>
                                <div v-if="scope.row.contact.ip" style="font-size: 11px; color: #67c23a;">
                                    <i class="el-icon-location-outline"></i> {{ scope.row.contact.ip }}
                                </div>
                            </div>
                            <div v-else>-</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="状态" width="100">
                        <template #default="scope">
                            <el-tag 
                                :type="scope.row.status === 'open' ? 'success' : 'info'"
                                effect="dark"
                                size="small">
                                {{ scope.row.status === 'open' ? '进行中' : '已关闭' }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="最后活动" width="180">
                        <template #default="scope">
                            <div class="session-time">{{ formatTime(scope.row.last_time) }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="210" fixed="right">
                        <template #default="scope">
                            <div style="display: flex; flex-direction: column; gap: 5px;">
                                <div style="display: flex; gap: 5px; justify-content: space-between;">
                                    <el-button 
                                        size="small" 
                                        type="primary" 
                                        @click.stop="handleViewSession(scope.row)"
                                        style="flex: 1;">
                                        <i class="el-icon-view"></i> 查看
                                    </el-button>
                                    <el-button 
                                        size="small" 
                                        :type="scope.row.status === 'open' ? 'danger' : 'success'"
                                        @click.stop="handleToggleSessionStatus(scope.row)"
                                        style="flex: 1;">
                                        <i :class="scope.row.status === 'open' ? 'el-icon-close' : 'el-icon-check'"></i>
                                        {{ scope.row.status === 'open' ? '关闭' : '打开' }}
                                    </el-button>
                                </div>
                                <div style="display: flex; gap: 5px; justify-content: space-between;">
                                    <el-button
                                        size="small"
                                        type="warning"
                                        @click.stop="handleSendEmail(scope.row)"
                                        style="flex: 1;">
                                        <i class="el-icon-message"></i> 客户通知
                                    </el-button>
                                    <el-button
                                        size="small"
                                        type="info"
                                        @click.stop="handleSendMerchantEmail(scope.row)"
                                        style="flex: 1;">
                                        <i class="el-icon-message"></i> 商家通知
                                    </el-button>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    </el-table>
                </div>

                <div class="empty-data" v-if="filteredSessions.length === 0 && !loading">
                    暂无会话数据
                </div>
                
                <div style="margin-top: 20px; text-align: right;">
                    <el-pagination
                        background
                        layout="prev, pager, next"
                        :total="total"
                        :page-size="limit"
                        v-model:current-page="currentPage"
                        @current-change="handlePageChange">
                    </el-pagination>
                </div>
            </el-card>
        </div>
        
        <!-- 会话详情对话框 -->
        <el-dialog 
            title="会话详情" 
            v-model="dialogVisible" 
            width="70%"
            top="5vh"
            :destroy-on-close="true"
            :close-on-click-modal="false"
            :before-close="handleCloseDialog"
            @opened="handleDialogOpened"
            :class="{'is-maximized': isDialogMaximized}">
            <div v-if="currentSession" class="session-detail-container">
                <div class="contact-info-section">
                    <el-descriptions :column="3" border size="small">
                        <el-descriptions-item label="联系人">
                            {{ currentSession.contact ? currentSession.contact.name : '-' }}
                        </el-descriptions-item>
                        <el-descriptions-item label="邮箱">
                            {{ currentSession.contact ? currentSession.contact.email : '-' }}
                        </el-descriptions-item>
                        <el-descriptions-item label="电话">
                            {{ currentSession.contact && currentSession.contact.phone ? currentSession.contact.phone : '-' }}
                        </el-descriptions-item>
                        <el-descriptions-item label="IP地址">
                            <span v-if="currentSession.contact && currentSession.contact.ip" style="color: #67c23a;">
                                <i class="el-icon-location-outline"></i> {{ currentSession.contact.ip }}
                            </span>
                            <span v-else>-</span>
                        </el-descriptions-item>
                        <el-descriptions-item label="会话状态">
                            <el-tag :type="currentSession.status === 'open' ? 'success' : 'info'" effect="dark">
                                {{ currentSession.status === 'open' ? '进行中' : '已关闭' }}
                            </el-tag>
                        </el-descriptions-item>
                        <el-descriptions-item label="创建时间">
                            {{ formatTime(currentSession.create_time) }}
                        </el-descriptions-item>
                        <el-descriptions-item label="最后活动">
                            {{ formatTime(currentSession.last_time) }}
                        </el-descriptions-item>
                    </el-descriptions>
                </div>
                
                <div class="message-header">
                    <h3>会话记录</h3>
                    <div class="message-actions">
                        <el-button size="small" type="primary" @click="markAllCustomerMessagesAsRead" :disabled="currentMessages.length === 0">
                            <i class="el-icon-check"></i> 全部已读
                        </el-button>
                        <el-button size="small" type="danger" @click="confirmClearMessages" :disabled="currentMessages.length === 0">
                            <i class="el-icon-delete"></i> 清空消息
                        </el-button>
                        <el-button size="small" type="danger" @click="toggleMessageSelection" :disabled="currentMessages.length === 0">
                            {{ isSelecting ? '取消选择' : '选择删除' }}
                        </el-button>
                        <el-button size="small" type="danger" v-if="isSelecting" @click="deleteSelectedMessages" :disabled="selectedMessages.length === 0">
                            删除所选({{ selectedMessages.length }})
                        </el-button>
                    </div>
                </div>

                <!-- 添加操作指引 -->
                <div class="message-guide" style="margin-bottom: 15px; padding: 10px; background: #f0f7ff; border-radius: 8px; color: #606266; font-size: 13px;">
                    <div style="margin-bottom: 5px; font-weight: bold;"><i class="el-icon-info-filled" style="margin-right: 5px; color: #409EFF;"></i>操作指引：</div>
                    <div>- 将鼠标悬停在任意消息上，右侧（客户消息为左侧）会显示操作按钮</div>
                    <div>- 点击 <svg width="14" height="14" viewBox="0 0 24 24" fill="#606266" style="vertical-align: middle; margin-right: 2px;"><path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/></svg> 可复制消息内容或图片链接</div>
                    <div>- 点击 <svg width="14" height="14" viewBox="0 0 24 24" fill="#606266" style="vertical-align: middle; margin-right: 2px;"><path d="M12.5 8c-2.65 0-5.05.99-6.9 2.6L2 7v9h9l-3.62-3.62c1.39-1.16 3.16-1.88 5.12-1.88 3.54 0 6.55 2.31 7.6 5.5l2.37-.78C21.08 11.03 17.15 8 12.5 8z"/></svg> 可撤回2分钟内由您发送的消息</div>
                </div>
                
                <div class="chat-messages-container">
                    <div v-for="(message, index) in currentMessages" :key="index" :class="['message', message.role_type === 'staff' ? 'staff' : 'customer']">
                        <!-- 添加日期分组分隔线 -->
                        <div class="message-date-divider" v-if="shouldShowDateDivider(message, index)">
                            <span>{{ formatMessageDate(message.create_time) }}</span>
                        </div>
                        <div class="message-meta">
                            <div class="message-sender">
                                <el-checkbox 
                                    v-if="isSelecting" 
                                    v-model="message.selected" 
                                    @change="updateSelectedMessages"
                                    style="margin-right: 5px;">
                                </el-checkbox>
                                <div class="message-sender-role" 
                                     :class="{
                                         'customer-tag': message.role_type === 'customer',
                                         'merchant-tag': message.role_type === 'merchant',
                                         'staff-tag': message.role_type === 'staff'
                                     }">
                                    <i :class="{
                                         'el-icon-user': message.role_type === 'customer',
                                         'el-icon-shop': message.role_type === 'merchant',
                                         'el-icon-service': message.role_type === 'staff'
                                     }"></i>
                                    {{ message.role_type === 'merchant' ? '商家' : (message.role_type === 'staff' ? '平台客服' : '客户') }}
                                </div>
                            </div>
                            <div class="message-time">
                                <i class="el-icon-time"></i>
                                {{ formatMessageTime(message.create_time) }}
                                <!-- 添加已读状态显示 -->
                                <span v-if="message.role_type === 'customer'" class="message-read-status" 
                                    :style="{
                                        color: getReadStatus(message) === true ? '#10b981' : 
                                              getReadStatus(message) === 'partial' ? '#f59e0b' : '#94a3b8', 
                                        marginLeft: '8px', 
                                        fontSize: '12px'
                                    }">
                                    <template v-if="getReadStatus(message) === true">
                                        已读<i class="el-icon-check" style="margin-left: 2px;"></i>
                                    </template>
                                    <template v-else-if="getReadStatus(message) === 'partial'">
                                        部分已读<i class="el-icon-warning" style="margin-left: 2px;"></i>
                                    </template>
                                    <template v-else>
                                        未读
                                    </template>
                                </span>
                            </div>
                        </div>
                        <div :class="['message-content', message.is_recalled ? 'message-recalled' : '']">
                            <div v-if="(message.message_type === 'text' || !message.message_type) && !message.is_recalled && !isImageUrl(message.message)" v-html="formatMessage(message.message)"></div>
                            <div v-else-if="(message.message_type === 'image' || isImageUrl(message.message)) && !message.is_recalled" class="image-message">
                                <div class="message-image-container">
                                    <img :src="message.file_url || message.message" 
                                         @click="previewImage(message.file_url || message.message)"
                                         @error="handleImageError($event, message)" 
                                         class="message-image"
                                         style="display: block;"
                                         alt="图片消息" />
                                </div>
                            </div>
                            <div v-else-if="message.message_type === 'file' && !message.is_recalled" class="file-message">
                                <div class="file-icon">
                                    <i class="el-icon-document" style="font-size: 20px; color: #409EFF;"></i>
                                </div>
                                <div class="file-info">
                                    <div class="file-name">{{ getFileName(message.file_url) }}</div>
                                    <div class="file-size">文件附件</div>
                                </div>
                                <a :href="message.file_url" target="_blank" class="file-download">
                                    <i class="el-icon-download"></i> 下载
                                </a>
                            </div>
                            <div v-if="message.is_recalled" class="recalled-message-text">
                                {{ message.message }}
                            </div>
                            
                            <!-- 消息操作按钮 -->
                            <div class="message-actions-btn" v-if="!message.is_recalled">
                                <div class="message-action-icon" @click="copyMessage(message)" title="复制">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                                    </svg>
                                </div>
                                <div
                                    class="message-action-icon"
                                    @click="recallMessage(message)"
                                    title="撤回"
                                    v-if="canRecallMessage(message)">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12.5 8c-2.65 0-5.05.99-6.9 2.6L2 7v9h9l-3.62-3.62c1.39-1.16 3.16-1.88 5.12-1.88 3.54 0 6.55 2.31 7.6 5.5l2.37-.78C21.08 11.03 17.15 8 12.5 8z"/>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div v-if="currentSession.status === 'open'" class="reply-section">
                    <div class="reply-container">
                        <!-- 预设回复按钮区域 - 已移除 -->
                        
                        <!-- 快速回复按钮区域 (客服专用) -->
                        <div v-if="params.quick_replies && params.quick_replies.enabled" class="quick-reply-section" style="margin-top: 12px; background: linear-gradient(135deg, #f8faff, #e9f2ff); border: 1px solid #d0e0ff; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
                            <h4 style="color: #333; font-weight: 600;">客服快速回复</h4>
                            <div class="quick-reply-buttons">
                                <el-button 
                                    v-for="(reply, index) in params.quick_replies.items" 
                                    :key="index" 
                                    size="small" 
                                    type="primary"
                                    @click="useQuickReply(reply)"
                                    style="margin: 4px; font-weight: 500; box-shadow: 0 2px 5px rgba(0,0,0,0.08);">
                                    {{ reply.label }}
                                </el-button>
                            </div>
                        </div>
                        
                        <div class="reply-input-container">
                            <el-input 
                                v-model="replyMessage" 
                                placeholder="输入回复内容..." 
                                type="textarea" 
                                :rows="3"
                                @keyup.enter="sendReply"
                                style="margin-right: 16px;"></el-input>
                            <div style="display: flex; flex-direction: column; justify-content: space-between;">
                                <el-button type="primary" @click="sendReply" :loading="sendingReply">发送</el-button>
                                <el-upload
                                    action="/adminApi/Upload/file"
                                    :show-file-list="false"
                                    :on-success="handleUploadSuccess"
                                    :before-upload="beforeUpload">
                                    <el-button type="info">上传文件</el-button>
                                </el-upload>
                                <div class="paste-tip" style="font-size: 12px; color: #909399; margin-top: 5px; text-align: center;">
                                    <i class="el-icon-info-circle"></i> 可直接粘贴图片
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <template #footer>
                <el-button @click="dialogVisible = false">关闭</el-button>
                <el-button 
                    :type="currentSession && currentSession.status === 'open' ? 'danger' : 'success'"
                    @click="handleToggleSessionStatus(currentSession)">
                    {{ currentSession && currentSession.status === 'open' ? '关闭会话' : '重新打开会话' }}
                </el-button>
            </template>
        </el-dialog>
        
        <!-- 图片预览 -->
        <el-dialog v-model="imagePreviewVisible" append-to-body class="image-preview-dialog" :close-on-click-modal="true">
            <img width="100%" :src="previewImageUrl" alt="预览图片" 
                 style="border-radius: 4px; max-height: 80vh; object-fit: contain;"
                 @error="previewImageUrl = previewImageUrl.replace(/^(https?:\/\/[^\/]+)?\/uploads\//, '/uploads/')">
        </el-dialog>
        
        <!-- 邮件发送对话框 - 已不再使用，使用直接发送模式 -->
        <!-- <el-dialog
            title="发送邮件通知"
            v-model="emailDialogVisible"
            width="50%"
            :destroy-on-close="true"
            :close-on-click-modal="false">
            <div v-if="currentSession && currentSession.contact">
                <el-form :model="emailForm" label-width="100px">
                    <el-form-item label="收件人">
                        <el-input v-model="emailForm.email" disabled></el-input>
                    </el-form-item>
                    <el-form-item label="邮件标题">
                        <el-input v-model="emailForm.subject" placeholder="请输入邮件标题"></el-input>
                    </el-form-item>
                    <el-form-item label="邮件内容">
                        <el-input v-model="emailForm.content" type="textarea" :rows="10" placeholder="请输入邮件内容"></el-input>
                    </el-form-item>
                </el-form>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="emailDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="sendEmailNotification" :loading="sendingEmail">发送</el-button>
                </span>
            </template>
        </el-dialog> -->
    </div>
    
    <!-- <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/element-ui@2.15.10/lib/index.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios@0.21.1/dist/axios.min.js"></script> -->
    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>
    <script>
        const { createApp, ref, computed, onMounted, onBeforeUnmount, nextTick, watch } = Vue;
        
        const App = {
            setup() {
                // 统计数据
                const totalSessions = ref(0);
                const openSessions = ref(0);
                const unreadMessages = ref(0);
                const chatEnabled = ref(false);
                
                // 列表数据
                const sessions = ref([]);
                const loading = ref(false);
                const currentPage = ref(1);
                const limit = ref(10);
                const total = ref(0);
                const activeTab = ref('all');
                const searchKeyword = ref('');
                
                // 会话详情
                const dialogVisible = ref(false);
                const currentSession = ref(null);
                const currentMessages = ref([]);
                const replyMessage = ref('');
                const sendingReply = ref(false);
                const uploadedFile = ref(null);
                
                // 图片预览
                const imagePreviewVisible = ref(false);
                const previewImageUrl = ref('');
                
                // 实时轮询设置 - 优化性能
                let pollingTimer = null;
                const pollingInterval = 5000; // 增加到5秒轮询一次，减少服务器压力
                const lastMessageId = ref(0);
                let pollingCount = 0; // 轮询计数器，用于控制频率
                
                // 参数
                const params = ref({});
                
                // 选择相关
                const isSelecting = ref(false);
                const selectedMessages = ref([]);
                const isSelectingSessions = ref(false);
                const selectedSessions = ref([]);
                
                // 对话框最大化状态
                const isDialogMaximized = ref(false);
                
                // 通知相关
                const notificationEnabled = ref(localStorage.getItem('notification_enabled') === 'true');
                const notificationPermission = ref('default');
                
                // 邮件发送相关
                const emailDialogVisible = ref(false);
                const emailForm = ref({
                    email: '',
                    subject: '客服系统消息通知',
                    content: '尊敬的用户，您好！\n\n您有新的客服消息，请登录系统查看您的在线客服内容。\n\n如有任何问题，请随时联系我们。\n\n此致\n客服团队'
                });
                const sendingEmail = ref(false);
                
                // 批量已读相关
                const markingAsRead = ref(false);
                
                // 缓存变量，避免重复计算
                let filteredSessionsCache = null;
                let lastFilterParams = { tab: '', keyword: '', sessionsLength: 0 };

                // 防抖函数，减少频繁调用
                const debounce = (func, wait) => {
                    let timeout;
                    return function executedFunction(...args) {
                        const later = () => {
                            clearTimeout(timeout);
                            func(...args);
                        };
                        clearTimeout(timeout);
                        timeout = setTimeout(later, wait);
                    };
                };

                // 计算属性 - 添加缓存优化和安全检查
                const filteredSessions = computed(() => {
                    // 安全检查，确保 sessions.value 存在
                    if (!sessions.value || !Array.isArray(sessions.value)) {
                        return [];
                    }

                    const currentParams = {
                        tab: activeTab.value,
                        keyword: searchKeyword.value,
                        sessionsLength: sessions.value.length
                    };

                    // 检查是否需要重新计算
                    if (filteredSessionsCache &&
                        lastFilterParams.tab === currentParams.tab &&
                        lastFilterParams.keyword === currentParams.keyword &&
                        lastFilterParams.sessionsLength === currentParams.sessionsLength) {
                        return filteredSessionsCache;
                    }

                    let result = sessions.value;

                    // 根据选项卡过滤
                    if (activeTab.value !== 'all') {
                        result = result.filter(session => session.status === activeTab.value);
                    }

                    // 根据搜索关键词过滤
                    if (searchKeyword.value) {
                        const keyword = searchKeyword.value.toLowerCase();
                        result = result.filter(session => {
                            return (
                                (session.title && session.title.toLowerCase().includes(keyword)) ||
                                (session.last_message && session.last_message.toLowerCase().includes(keyword)) ||
                                (session.contact && session.contact.name && session.contact.name.toLowerCase().includes(keyword)) ||
                                (session.contact && session.contact.email && session.contact.email.toLowerCase().includes(keyword))
                            );
                        });
                    }

                    // 更新缓存
                    filteredSessionsCache = result;
                    lastFilterParams = currentParams;

                    return result;
                });
                
                // 判断选中的会话中是否有打开状态的会话
                const hasOpenSessionsSelected = computed(() => {
                    if (!selectedSessions.value || !Array.isArray(selectedSessions.value)) {
                        return false;
                    }
                    return selectedSessions.value.some(session => session.status === 'open');
                });

                // 计算选中的处于打开状态的会话数量
                const openSelectedSessionsCount = computed(() => {
                    if (!selectedSessions.value || !Array.isArray(selectedSessions.value)) {
                        return 0;
                    }
                    return selectedSessions.value.filter(session => session.status === 'open').length;
                });
                
                // 请求通知权限
                const requestNotificationPermission = async () => {
                    try {
                        // 检查浏览器是否支持通知API
                        if (!("Notification" in window)) {
                            ElementPlus.ElMessage.warning('您的浏览器不支持桌面通知');
                            saveNotificationSetting(false);
                            return;
                        }
                        
                        // 获取当前权限状态
                        notificationPermission.value = Notification.permission;
                        
                        // 如果已经授权，直接返回
                        if (notificationPermission.value === "granted") {
                            saveNotificationSetting(true);
                            ElementPlus.ElMessage.success('通知权限已授权');
                            return;
                        }
                        
                        // 如果权限被拒绝，提示用户在浏览器设置中启用
                        if (notificationPermission.value === "denied") {
                            ElementPlus.ElMessage.warning('通知权限已被拒绝，请在浏览器设置中启用通知');
                            saveNotificationSetting(false);
                            return;
                        }
                        
                        // 请求权限
                        const permission = await Notification.requestPermission();
                        notificationPermission.value = permission;
                        
                        if (permission === "granted") {
                            saveNotificationSetting(true);
                            ElementPlus.ElMessage.success('通知权限已授权');
                            
                            // 显示一个测试通知
                            showNotification('通知测试', '客服系统通知功能已启用', null);
                        } else {
                            saveNotificationSetting(false);
                            ElementPlus.ElMessage.warning('通知权限被拒绝');
                        }
                    } catch (error) {
                        saveNotificationSetting(false);
                        ElementPlus.ElMessage.error('请求通知权限失败');
                    }
                };
                
                // 显示桌面通知
                const showNotification = (title, body, icon = null, sessionData = null) => {
                    // 检查通知权限和开关状态
                    if (notificationPermission.value !== "granted" || !notificationEnabled.value) {
                        return;
                    }
                    
                    // 如果对话框打开且是当前会话，且页面处于可见状态，则不发送通知
                    if (dialogVisible.value && currentSession.value && 
                        sessionData && sessionData.id === currentSession.value.id && 
                        document.visibilityState === 'visible') {
                        return;
                    }
                    
                    try {
                        // 创建通知
                        const notification = new Notification(title, {
                            body: body,
                            icon: icon || '/static/plugins/Customersystem/images/broken-image.png',
                            tag: sessionData ? `session-${sessionData.id}` : 'customer-system'
                        });
                        
                        // 添加点击事件，用户点击通知时打开相应会话
                        notification.onclick = function() {
                            // 聚焦窗口
                            window.focus();
                            
                            // 如果传入了会话数据且对话框未打开，打开相应会话
                            if (sessionData && !dialogVisible.value) {
                                handleViewSession(sessionData);
                            } else if (sessionData && dialogVisible.value && 
                                      currentSession.value && currentSession.value.id !== sessionData.id) {
                                // 如果对话框已打开但显示的是其他会话，则切换到该会话
                                handleViewSession(sessionData);
                            }
                            
                            // 关闭通知
                            this.close();
                        };
                        
                        // 设置通知自动关闭时间
                        setTimeout(() => {
                            notification.close();
                        }, 5000);
                    } catch (error) {
                        
                    }
                };
                
                // 添加保存通知设置的函数
                const saveNotificationSetting = (value) => {
                    notificationEnabled.value = value;
                    // 将设置保存到localStorage
                    localStorage.setItem('notification_enabled', value);
                };
                
                // 获取会话列表数据
                const fetchData = () => {
                    loading.value = true;
                    axios.get(`/plugin/Customersystem/api/getSessionList?page=${currentPage.value}&limit=${limit.value}&status=${activeTab.value === 'all' ? '' : activeTab.value}`)
                        .then(response => {
                            if (response.data.code === 200) {
                                const data = response.data.data;
                                sessions.value = data.data;
                                total.value = data.total;
                                
                                // 统计数据
                                countStatistics();
                            } else {
                                ElementPlus.ElMessage.error(response.data.msg || '获取数据失败');
                            }
                        })
                        .catch(error => {
                            ElementPlus.ElMessage.error('获取会话列表失败');
                        })
                        .finally(() => {
                            loading.value = false;
                        });
                };
                
                // 统计数据 - 性能优化，减少数据传输
                const countStatistics = () => {
                    // 减少请求的数据量，只获取必要的统计信息
                    axios.get('/plugin/Customersystem/api/getSessionList?limit=200&statistics_only=1')
                        .then(response => {
                            if (response.data.code === 200) {
                                const data = response.data.data;
                                const allSessions = data.data;

                                // 使用服务器返回的总数，避免客户端计算
                                totalSessions.value = data.total || allSessions.length;

                                // 优化计算，使用更高效的方法
                                let openCount = 0;
                                let unreadCount = 0;

                                for (let i = 0; i < allSessions.length; i++) {
                                    const session = allSessions[i];
                                    if (session.status === 'open') {
                                        openCount++;
                                    }
                                    if (session.unread_count > 0) {
                                        unreadCount += parseInt(session.unread_count);
                                    }
                                }

                                openSessions.value = openCount;
                                unreadMessages.value = unreadCount;
                            }
                        })
                        .catch(error => {
                            // 静默处理错误，避免控制台污染
                        });
                };

                // 防抖的统计数据更新 - 在 countStatistics 定义后创建
                const debouncedCountStatistics = debounce(countStatistics, 2000);
                
                // 获取聊天系统状态
                const fetchChatStatus = () => {
                    axios.get('/plugin/Customersystem/api/getParams')
                        .then(response => {
                            if (response.data.code === 200) {
                                const paramsData = response.data.data;
                                chatEnabled.value = paramsData.settings && paramsData.settings.chat_enabled === true;
                                
                                // 保存获取到的参数
                                if (response.data.data) {
                                    // 确保preset_replies对象存在
                                    if (!paramsData.preset_replies) {
                                        paramsData.preset_replies = {
                                            enabled: true,
                                            title: '以下是常见问题，您看下是否可以帮助到您呢？',
                                            items: []
                                        };
                                    } else if (!paramsData.preset_replies.items) {
                                        paramsData.preset_replies.items = [];
                                    }
                                    
                                    // 确保quick_replies对象存在
                                    if (!paramsData.quick_replies) {
                                        paramsData.quick_replies = {
                                            enabled: true,
                                            items: []
                                        };
                                    } else if (!paramsData.quick_replies.items) {
                                        paramsData.quick_replies.items = [];
                                    }
                                    
                                    params.value = paramsData;
                                }
                            }
                        })
                        .catch(error => {

                        });
                };
                
                // 判断消息的已读状态
                const getReadStatus = (message) => {
                    // 当前页面是客服管理界面，角色为staff
                    const currentRole = 'staff';
                    
                    // 根据消息发送者和当前角色判断应该检查哪个已读字段
                    if (message.role_type === 'customer') {
                        // 客户消息应该检查staff_read字段
                        return message.staff_read === 1;
                    } else if (message.role_type === 'merchant') {
                        // 商家消息应该检查staff_read字段
                        return message.staff_read === 1;
                    } else if (message.role_type === 'staff') {
                        // 客服消息应该检查customer_read和merchant_read字段
                        if (message.customer_read === 1 && message.merchant_read === 1) {
                            return true; // 全部已读
                        } else if (message.customer_read === 1 || message.merchant_read === 1) {
                            return 'partial'; // 部分已读
                        } else {
                            return false; // 全部未读
                        }
                    }
                    
                    // 默认未读
                    return false;
                }
                
                // 使用预设回复
                const usePresetReply = (reply) => {
                    if (reply && reply.content) {
                        replyMessage.value = reply.content;
                        ElementPlus.ElMessage.success('预设回复已插入');
                    }
                };
                
                // 使用快速回复
                const useQuickReply = (reply) => {
                    if (reply && reply.content) {
                        replyMessage.value = reply.content;
                        ElementPlus.ElMessage.success('快速回复已插入');
                    }
                };
                
                // 切换聊天系统状态
                const toggleChatStatus = (status) => {
                    axios.post('/plugin/Customersystem/api/toggleChatStatus', { enabled: status ? 1 : 0 })
                        .then(response => {
                            if (response.data.code === 200) {
                                ElementPlus.ElMessage.success(status ? '客服系统已启用' : '客服系统已禁用');
                            } else {
                                ElementPlus.ElMessage.error(response.data.msg || '操作失败');
                                chatEnabled.value = !status; // 恢复原状态
                            }
                        })
                        .catch(error => {

                            ElementPlus.ElMessage.error('操作失败');
                            chatEnabled.value = !status; // 恢复原状态
                        });
                };
                
                // 分页切换
                const handlePageChange = (page) => {
                    currentPage.value = page;
                    fetchData();
                };
                
                // 切换选项卡
                const changeTabToSessions = (tab) => {
                    activeTab.value = tab;
                    currentPage.value = 1;
                    fetchData();
                };
                
                // 切换到未读消息选项卡
                const changeTabToUnread = () => {
                    activeTab.value = 'all';
                    searchKeyword.value = '';
                    currentPage.value = 1;
                    
                    // 获取所有会话后按未读消息筛选
                    loading.value = true;
                    axios.get('/plugin/Customersystem/api/getSessionList?limit=1000')
                        .then(response => {
                            if (response.data.code === 200) {
                                const allSessions = response.data.data.data;
                                sessions.value = allSessions.filter(session => session.unread_count > 0);
                                total.value = sessions.value.length;
                            } else {
                                ElementPlus.ElMessage.error(response.data.msg || '获取数据失败');
                            }
                        })
                        .catch(error => {

                            ElementPlus.ElMessage.error('获取未读消息失败');
                        })
                        .finally(() => {
                            loading.value = false;
                        });
                };
                
                // 点击行查看会话详情
                const handleSessionClick = (row) => {
                    handleViewSession(row);
                };
                
                // 查看会话详情 - 跳转到聊天页面
                const handleViewSession = (session) => {
                    // 停止轮询
                    stopPolling();

                    // 跳转到聊天页面，并传递会话ID
                    window.location.href = `/plugin/Customersystem/api/chatPage?session_id=${session.id}`;
                };

                // 对话框打开时的处理
                const handleDialogOpened = () => {
                    // 开始轮询获取新消息
                    startPolling();
                    
                    // 调整聊天容器高度，使其能显示更多消息
                    nextTick(() => {
                        adjustChatContainerHeight();
                    });
                    
                    // 添加窗口大小变化的监听
                    window.addEventListener('resize', adjustChatContainerHeight);
                    
                    // 确保对话框不是全屏模式
                    const dialogEl = document.querySelector('.el-dialog');
                    if (dialogEl && dialogEl.classList.contains('is-fullscreen')) {
                        dialogEl.classList.remove('is-fullscreen');
                    }
                    
                    // 为对话框添加最大化按钮
                    nextTick(() => {
                        const dialogHeader = document.querySelector('.el-dialog__header');
                        if (dialogHeader && !dialogHeader.querySelector('.maximize-btn')) {
                            const maxBtn = document.createElement('button');
                            maxBtn.className = 'maximize-btn';
                            maxBtn.innerHTML = '<i class="el-icon-full-screen"></i>';
                            maxBtn.addEventListener('click', toggleDialogMaximize);
                            
                            // 在关闭按钮前插入最大化按钮
                            const closeBtn = dialogHeader.querySelector('.el-dialog__close');
                            if (closeBtn && closeBtn.parentNode) {
                                closeBtn.parentNode.insertBefore(maxBtn, closeBtn);
                            } else {
                                dialogHeader.appendChild(maxBtn);
                            }
                        }
                        
                        // 根据当前最大化状态更新按钮图标
                        updateMaximizeButtonIcon();
                    });

                    // 标记所有消息为已读
                    markMessagesReadOnDialogOpen();
                    
                    // 启动已读状态轮询
                    startReadStatusPolling();
                };
                
                // 调整聊天容器高度的函数
                const adjustChatContainerHeight = () => {
                    const chatContainer = document.querySelector('.chat-messages-container');
                    const dialogBody = document.querySelector('.el-dialog__body');
                    const contactSection = document.querySelector('.contact-info-section');
                    const messageHeader = document.querySelector('.message-header');
                    const replySection = document.querySelector('.reply-section');
                    
                    if (chatContainer && dialogBody && contactSection && messageHeader) {
                        // 如果对话框已最大化，使用特定的高度计算
                        if (isDialogMaximized.value) {
                            const dialogHeight = window.innerHeight;
                            const headerHeight = 60; // 估计的header高度
                            const footerHeight = 60; // 估计的footer高度
                            const otherElementsHeight = contactSection.offsetHeight + messageHeader.offsetHeight + 
                                                      (replySection ? replySection.offsetHeight : 0) + 40;
                            
                            const availableHeight = dialogHeight - headerHeight - footerHeight - otherElementsHeight;
                            
                            if (availableHeight > 200) { // 确保最小高度
                                chatContainer.style.height = availableHeight + 'px';
                                chatContainer.style.maxHeight = availableHeight + 'px';
                            }
                        } else {
                            // 计算其他元素占用的高度
                            const otherHeight = (contactSection.offsetHeight + messageHeader.offsetHeight + 
                                               (replySection ? replySection.offsetHeight : 0) + 40); // 40px 为额外边距
                            
                            // 设置聊天容器的高度为对话框body高度减去其他元素高度
                            const availableHeight = dialogBody.offsetHeight - otherHeight;
                            if (availableHeight > 200) { // 确保最小高度
                                chatContainer.style.height = availableHeight + 'px';
                                chatContainer.style.maxHeight = availableHeight + 'px';
                            }
                        }
                        
                        // 滚动到底部
                        chatContainer.scrollTop = chatContainer.scrollHeight;
                    }
                };
                
                // 停止轮询
                const stopPolling = () => {
                    if (pollingTimer) {
                        clearInterval(pollingTimer);
                        pollingTimer = null;
                    }
                    
                    // 同时停止已读状态轮询
                    stopReadStatusPolling();
                };
                
                // 关闭对话框
                const handleCloseDialog = () => {
                    dialogVisible.value = false;
                    currentSession.value = null;
                    currentMessages.value = [];
                    lastMessageId.value = 0;
                    
                    // 停止所有轮询
                    stopPolling();
                    
                    // 移除窗口大小变化的监听
                    window.removeEventListener('resize', adjustChatContainerHeight);
                };
                
                // 处理页面可见性变化
                const handleVisibilityChange = () => {
                    if (document.visibilityState === 'visible') {
                        // 页面可见时，如果有打开的会话，则标记消息为已读
                        if (dialogVisible.value && currentSession.value) {
                            markAllCustomerMessagesAsRead();
                            
                            // 重新获取一次实时消息
                            pollNewMessages();
                        }
                    }
                };
                
                // 切换会话状态
                const handleToggleSessionStatus = (session) => {
                    if (!session) return;
                    
                    const newStatus = session.status === 'open' ? 'closed' : 'open';
                    const actionText = newStatus === 'closed' ? '关闭' : '重新开启';
                    
                    ElementPlus.ElMessageBox.confirm(`确定要${actionText}此会话吗?`, '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        axios.post('/plugin/Customersystem/api/updateSessionStatus', {
                            session_id: session.id,
                            status: newStatus
                        })
                        .then(response => {
                            if (response.data.code === 200) {
                                ElementPlus.ElMessage.success(`会话已${actionText}`);
                                
                                // 更新本地会话状态
                                session.status = newStatus;
                                
                                // 如果当前正在查看此会话，同步更新详情中的状态
                                if (currentSession.value && currentSession.value.id === session.id) {
                                    currentSession.value.status = newStatus;
                                }
                                
                                // 刷新数据
                                fetchData();
                            } else {
                                ElementPlus.ElMessage.error(response.data.msg || '操作失败');
                            }
                        })
                        .catch(error => {
                            ElementPlus.ElMessage.error('操作失败');
                        });
                    }).catch(() => {
                        // 取消操作
                    });
                };
                
                // 发送回复
                const sendReply = () => {
                    if ((!replyMessage.value || replyMessage.value.trim() === '') && !uploadedFile.value) {
                        ElementPlus.ElMessage.warning('请输入回复内容或上传文件');
                        return;
                    }
                    
                    if (!currentSession.value) {
                        ElementPlus.ElMessage.error('会话信息不存在');
                        return;
                    }
                    
                    sendingReply.value = true;
                    
                    const postData = {
                        session_id: currentSession.value.id,
                        sender_type: 'staff',
                        sender_id: 0, // 系统或管理员ID
                        message: replyMessage.value,
                        message_type: 'text' // 添加默认的消息类型为文本
                    };
                    
                    // 如果有上传的文件
                    if (uploadedFile.value) {
                        postData.message_type = uploadedFile.value.type;
                        postData.file_url = uploadedFile.value.url;
                    }
                    
                    axios.post('/plugin/Customersystem/api/sendMessage', postData)
                        .then(response => {
                            if (response.data.code === 200) {
                                ElementPlus.ElMessage.success('回复已发送');
                                
                                // 清空输入
                                replyMessage.value = '';
                                uploadedFile.value = null;
                                
                                // 添加消息到列表
                                if (Array.isArray(response.data.data)) {
                                    // 如果返回多条消息（例如预设回复）
                                    response.data.data.forEach(msg => {
                                        currentMessages.value.push(msg);
                                    });
                                } else {
                                    // 单条消息
                                    currentMessages.value.push(response.data.data);
                                }
                                
                                // 更新最后消息ID，确保轮询能正确工作
                                lastMessageId.value = getLastMessageId();
                                
                                // 更新会话最后消息
                                if (currentSession.value) {
                                    currentSession.value.last_message = postData.message || '[文件]';
                                    currentSession.value.last_time = Math.floor(Date.now() / 1000);
                                }
                                
                                // 刷新会话列表
                                fetchData();
                                
                                // 重新调整聊天容器高度并滚动到底部
                                nextTick(() => {
                                    adjustChatContainerHeight();
                                });
                            } else {
                                ElementPlus.ElMessage.error(response.data.msg || '发送失败');
                            }
                        })
                        .catch(error => {

                            ElementPlus.ElMessage.error('发送回复失败');
                        })
                        .finally(() => {
                            sendingReply.value = false;
                        });
                };
                
                // 上传前检查
                const beforeUpload = (file) => {
                    const isLt5M = file.size / 1024 / 1024 < 5;
                    if (!isLt5M) {
                        ElementPlus.ElMessage.error('文件大小不能超过 5MB!');
                        return false;
                    }
                    return true;
                };
                
                // 上传成功回调
                const handleUploadSuccess = (response) => {
                    if (response.code === 200 || response.code === 1) {
                        uploadedFile.value = {
                            url: response.data.url,
                            type: response.data.type || 'image',
                            name: response.data.name || '附件'
                        };
                        ElementPlus.ElMessage.success('文件上传成功，正在发送...');
                        
                        // 添加自动发送功能
                        setTimeout(() => {
                            sendReply();
                        }, 300); // 短暂延迟确保状态已更新
                    } else {
                        ElementPlus.ElMessage.error(response.msg || '上传失败');
                    }
                };
                
                // 预览图片
                const previewImage = (url) => {
                    previewImageUrl.value = url;
                    imagePreviewVisible.value = true;
                };
                
                // 前往聊天页面
                const goToChatPage = () => {
                    // 在跳转之前清除定时器和状态
                    stopPolling();

                    window.location.href = '/plugin/Customersystem/api/chatPage';
                };

                // 前往设置页面
                const goToSettings = () => {
                    // 在跳转之前清除定时器和状态
                    stopPolling();

                    // 清除localStorage中可能保存的高度设置
                    try {
                        localStorage.removeItem('chat_container_height');
                    } catch (e) {

                    }

                    window.location.href = '/plugin/Customersystem/api/settings';
                };
                
                // 格式化时间
                const formatTime = (timestamp) => {
                    if (!timestamp) return '-';
                    
                    const date = new Date(timestamp * 1000);
                    const year = date.getFullYear();
                    const month = (date.getMonth() + 1).toString().padStart(2, '0');
                    const day = date.getDate().toString().padStart(2, '0');
                    const hours = date.getHours().toString().padStart(2, '0');
                    const minutes = date.getMinutes().toString().padStart(2, '0');
                    
                    return `${year}-${month}-${day} ${hours}:${minutes}`;
                };
                
                // 开始轮询 - 优化性能
                const startPolling = () => {
                    // 清除已有定时器
                    stopPolling();
                    pollingCount = 0;

                    // 设置定时器进行轮询
                    pollingTimer = setInterval(() => {
                        pollingCount++;

                        // 如果当前有打开的会话，则获取该会话的最新消息
                        if (dialogVisible.value && currentSession.value) {
                            pollNewMessages();

                            // 每3次轮询才检查一次已读状态更新（减少频率）
                            if (pollingCount % 3 === 0) {
                                checkAndUpdateReadStatus();
                            }
                        }

                        // 减少会话列表更新频率
                        if(dialogVisible.value) {
                            // 对话框打开时，每2次轮询更新一次列表
                            if (pollingCount % 2 === 0) {
                                updateSessionsList();
                            }
                        } else {
                            // 如果没有打开对话框，每4次轮询更新一次列表（约20秒）
                            if (pollingCount % 4 === 0) {
                                updateSessionsList();
                            }
                        }
                    }, pollingInterval);
                };
                
                // 获取最后一条消息的ID
                const getLastMessageId = () => {
                    if (!currentMessages.value || currentMessages.value.length === 0) {
                        return 0;
                    }
                    
                    // 找出最大的消息ID
                    return Math.max(...currentMessages.value.map(msg => parseInt(msg.id || 0)));
                };
                
                // 轮询获取新消息
                const pollNewMessages = () => {
                    if (!currentSession.value) return;
                    

                    
                    // 获取当前会话的最新消息
                    axios.post('/plugin/Customersystem/api/getRealtimeMessages', {
                        session_id: currentSession.value.id,
                        last_message_id: lastMessageId.value,
                        check_read_status: true // 添加检查已读状态参数
                    })
                    .then(response => {
                        if (response.data.code === 200) {
                            const data = response.data.data;
                            if (data && data.messages && data.messages.length > 0) {

                                
                                // 添加新消息到消息列表
                                let hasNewMessages = false;
                                const messagesToAdd = [];
                                const customerMessagesToMarkRead = []; // 收集客户消息ID用于标记已读
                                const customerNewMessages = []; // 收集新的客户消息用于通知
                                
                                // 优化消息去重逻辑，使用Set提升性能
                                const existingIds = new Set(currentMessages.value.map(msg => msg.id));

                                data.messages.forEach(message => {
                                    // 首先检查ID是否存在（最快的方式）
                                    if (!existingIds.has(message.id)) {
                                        // 只有ID不存在时才进行更复杂的检查
                                        const exists = currentMessages.value.some(msg =>
                                            // 检查发送时间相近且内容相同的消息
                                            Math.abs(msg.create_time - message.create_time) < 5 &&
                                            msg.sender_type === message.sender_type &&
                                            ((msg.message_type === 'image' && message.message_type === 'image' &&
                                              (msg.file_url === message.file_url || msg.message === message.message)) ||
                                             (msg.message === message.message))
                                        );

                                        if (!exists) {
                                            messagesToAdd.push(message);
                                            hasNewMessages = true;

                                            // 如果是客户消息，收集ID用于批量标记已读
                                            if (message.role_type === 'customer' || message.sender_type === 'customer') {
                                                customerMessagesToMarkRead.push(message.id);
                                                customerNewMessages.push(message); // 收集用于通知
                                            }
                                        }
                                    }
                                });
                                
                                // 添加消息到会话
                                if (messagesToAdd.length > 0) {
                                    // 添加消息
                                    messagesToAdd.forEach(message => {
                                        currentMessages.value.push(message);

                                        
                                        // 如果是客户发送的消息，播放通知声音
                                        if (message.sender_type === 'customer' || message.role_type === 'customer') {
                                            playNotificationSound();
                                            
                                            // 添加桌面通知功能
                                            if (notificationEnabled.value && notificationPermission.value === "granted") {
                                                // 获取会话标题和联系人信息
                                                const sessionTitle = currentSession.value.title || '新消息';
                                                const contactName = currentSession.value.contact ? currentSession.value.contact.name : '客户';
                                                
                                                // 处理消息内容（如果是HTML则提取纯文本）
                                                let messageText = message.message || '';
                                                if (messageText.startsWith('<div')) {
                                                    const tempDiv = document.createElement('div');
                                                    tempDiv.innerHTML = messageText;
                                                    messageText = tempDiv.textContent || tempDiv.innerText || messageText;
                                                }
                                                
                                                // 限制消息长度
                                                if (messageText.length > 50) {
                                                    messageText = messageText.substring(0, 50) + '...';
                                                }
                                                
                                                // 如果是图片或文件消息
                                                if (message.message_type === 'image') {
                                                    messageText = '[图片消息]';
                                                } else if (message.message_type === 'file') {
                                                    messageText = '[文件消息]';
                                                }
                                                
                                                const notificationBody = `${contactName}: ${messageText}`;
                                                
                                                // 显示桌面通知
                                                showNotification(sessionTitle, notificationBody, null, currentSession.value);
                                            }
                                        }
                                    });
                                    
                                    // 更新最后消息ID
                                    lastMessageId.value = getLastMessageId();
                                    
                                    // 更新会话的最后消息
                                    if (currentSession.value && data.messages.length > 0) {
                                        const lastMsg = data.messages[data.messages.length - 1];
                                        currentSession.value.last_message = lastMsg.message || '[文件]';
                                        currentSession.value.last_time = lastMsg.create_time;
                                    }
                                    
                                    // 滚动到底部
                                    nextTick(() => {
                                        const chatContainer = document.querySelector('.chat-messages-container');
                                        if (chatContainer) {
                                            chatContainer.scrollTop = chatContainer.scrollHeight;
                                        }
                                    });
                                    
                                    // 自动标记客户消息为已读（仅在对话框可见时）
                                    if (customerMessagesToMarkRead.length > 0 && document.visibilityState === 'visible') {
                                        // 对每个新消息单独标记为已读
                                        customerMessagesToMarkRead.forEach(messageId => {
                                            markMessageAsRead(messageId);
                                        });
                                        
                                        // 更新统计数据
                                        countStatistics();
                                    }
                                    
                                    // 不管是否标记为已读，都要确保更新统计数据
                                    countStatistics();
                                }
                            }
                            
                            // 处理已读状态变更的消息
                            if (data && data.read_status_changed && data.read_status_changed.length > 0) {
                                data.read_status_changed.forEach(updatedMsg => {
                                    const index = currentMessages.value.findIndex(msg => msg.id === updatedMsg.id);
                                    if (index !== -1) {
                                        currentMessages.value[index].is_read = updatedMsg.is_read;
                                        currentMessages.value[index].read_time = updatedMsg.read_time;
                                    }
                                });
                                
                                // 更新统计数据
                                countStatistics();
                            }
                            
                            // 更新会话中未读消息的总数
                            if (data && typeof data.unread_count !== 'undefined') {
                                if (currentSession.value) {
                                    currentSession.value.unread_count = data.unread_count;
                                    
                                    // 同时更新会话列表中的未读计数
                                    const sessionIndex = sessions.value.findIndex(s => s.id === currentSession.value.id);
                                    if (sessionIndex !== -1) {
                                        sessions.value[sessionIndex].unread_count = data.unread_count;
                                    }
                                }
                                // 强制更新统计数据
                                countStatistics();
                            }
                        }
                    })
                    .catch(error => {

                    });
                };
                
                // 更新会话列表
                const updateSessionsList = () => {
                    axios.get(`/plugin/Customersystem/api/getSessionList?page=${currentPage.value}&limit=${limit.value}&status=${activeTab.value === 'all' ? '' : activeTab.value}`)
                        .then(response => {
                            if (response.data.code === 200) {
                                const data = response.data.data;
                                
                                // 先检查是否有新消息
                                let hasNewUnread = false;
                                let totalNewUnread = 0;
                                let newCustomerMessages = [];
                                
                                data.data.forEach((newSession) => {
                                    const oldSession = sessions.value.find(s => s.id === newSession.id);
                                    if (!oldSession || newSession.unread_count > oldSession.unread_count) {
                                        hasNewUnread = true;
                                        // 计算新增的未读消息数量
                                        const newUnreadCount = !oldSession ? newSession.unread_count : 
                                                              (newSession.unread_count - oldSession.unread_count);
                                        totalNewUnread += newUnreadCount > 0 ? newUnreadCount : 0;
                                        
                                        // 添加到新消息列表，用于通知
                                        if (newUnreadCount > 0) {
                                            newCustomerMessages.push({
                                                session: newSession,
                                                unreadCount: newUnreadCount
                                            });
                                        }
                                    }
                                });
                                
                                // 更新会话列表
                                sessions.value = data.data;
                                total.value = data.total;
                                
                                // 如果有新消息，更新统计数据并播放通知声音
                                if (hasNewUnread) {

                                    // 统计数据
                                    countStatistics();
                                    
                                    // 如果有新消息，播放通知声音
                                    playNotificationSound();
                                    
                                    // 显示桌面通知
                                    if (notificationEnabled.value && notificationPermission.value === "granted") {
                                        newCustomerMessages.forEach(item => {
                                            const sessionTitle = item.session.title || '新消息';
                                            const contactName = item.session.contact ? item.session.contact.name : '客户';
                                            let notificationBody = `${contactName} 发来 ${item.unreadCount} 条新消息`;
                                            
                                            // 添加消息预览
                                            if (item.session.last_message) {
                                                // 如果是HTML内容，提取纯文本
                                                let messageText = item.session.last_message;
                                                if (messageText.startsWith('<div')) {
                                                    const tempDiv = document.createElement('div');
                                                    tempDiv.innerHTML = messageText;
                                                    messageText = tempDiv.textContent || tempDiv.innerText || messageText;
                                                }
                                                
                                                // 限制消息预览长度
                                                if (messageText.length > 50) {
                                                    messageText = messageText.substring(0, 50) + '...';
                                                }
                                                
                                                notificationBody += `\n${messageText}`;
                                            }
                                            
                                            // 显示通知
                                            showNotification(sessionTitle, notificationBody, null, item.session);
                                        });
                                    }
                                }
                            }
                        })
                        .catch(error => {

                        });
                };
                
                
                // 选择相关
                const toggleMessageSelection = () => {
                    isSelecting.value = !isSelecting.value;
                    selectedMessages.value = [];
                    
                    // 初始化所有消息的selected属性
                    if (isSelecting.value) {
                        currentMessages.value.forEach(message => {
                            message.selected = false;
                        });
                    }
                };
                
                const updateSelectedMessages = () => {
                    if (isSelecting.value) {
                        // 清空当前选中列表
                        selectedMessages.value = [];
                        
                        // 重新遍历选中状态
                        currentMessages.value.forEach(message => {
                            if (message.selected) {
                                selectedMessages.value.push(message);
                            }
                        });
                    }
                };
                
                const confirmClearMessages = () => {
                    ElementPlus.ElMessageBox.confirm('确定要清空所有消息吗?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        // 调用API清空消息
                        axios.post(`/plugin/Customersystem/api/clearMessages`, {
                            session_id: currentSession.value.id
                        })
                        .then(response => {
                            if (response.data.code === 200) {
                                // 重新获取会话详情
                                handleViewSession(currentSession.value);
                                ElementPlus.ElMessage.success('所有消息已清空');
                            } else {
                                ElementPlus.ElMessage.error(response.data.msg || '清空消息失败');
                            }
                        })
                        .catch(error => {

                            ElementPlus.ElMessage.error('清空消息失败，请稍后重试');
                        });
                    }).catch(() => {
                        // 取消操作
                    });
                };
                
                const deleteSelectedMessages = () => {
                    if (selectedMessages.value.length === 0) {
                        ElementPlus.ElMessage.warning('请先选择要删除的消息');
                        return;
                    }
                    
                    ElementPlus.ElMessageBox.confirm(`确定要删除已选择的 ${selectedMessages.value.length} 条消息吗?`, '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        // 从选中的消息对象中提取ID
                        const messageIds = selectedMessages.value.map(message => message.id);
                        
                        // 调用API删除消息
                        axios.post(`/plugin/Customersystem/api/deleteMessages`, {
                            session_id: currentSession.value.id,
                            message_ids: messageIds
                        })
                        .then(response => {
                            if (response.data.code === 200) {
                                // 重新获取会话详情
                                handleViewSession(currentSession.value);
                                // 退出选择模式
                                isSelecting.value = false;
                                selectedMessages.value = [];
                                ElementPlus.ElMessage.success('已删除所选消息');
                            } else {
                                ElementPlus.ElMessage.error(response.data.msg || '删除消息失败');
                            }
                        })
                        .catch(error => {

                            ElementPlus.ElMessage.error('删除消息失败，请稍后重试');
                        });
                    }).catch(() => {
                        // 取消操作
                    });
                };
                
                // 切换会话选择模式
                const toggleSessionSelection = () => {
                    isSelectingSessions.value = !isSelectingSessions.value;
                    selectedSessions.value = [];
                };
                
                // 处理会话选择变化
                const handleSessionSelectionChange = (selection) => {
                    selectedSessions.value = selection;
                };
                
                const deleteSelectedSessions = () => {
                    if (selectedSessions.value.length === 0) {
                        ElementPlus.ElMessage.warning('请先选择要删除的会话');
                        return;
                    }
                    
                    ElementPlus.ElMessageBox.confirm(`确定要删除已选择的 ${selectedSessions.value.length} 个会话吗？此操作将同时删除会话中的所有消息！`, '警告', {
                        confirmButtonText: '确定删除',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        // 获取选中的会话ID
                        const sessionIds = selectedSessions.value.map(session => session.id);
                        
                        // 调用API删除会话
                        axios.post('/plugin/Customersystem/api/deleteSessions', {
                            session_ids: sessionIds
                        })
                        .then(response => {
                            if (response.data.code === 200) {
                                ElementPlus.ElMessage.success('已删除选中的会话');
                                // 退出选择模式
                                isSelectingSessions.value = false;
                                selectedSessions.value = [];
                                // 重新加载数据
                                fetchData();
                            } else {
                                ElementPlus.ElMessage.error(response.data.msg || '删除会话失败');
                            }
                        })
                        .catch(error => {

                            ElementPlus.ElMessage.error('删除会话失败，请稍后重试');
                        });
                    }).catch(() => {
                        // 取消操作
                    });
                };
                
                // 开始编辑会话标题
                const startEditTitle = (session) => {
                    session.isEditingTitle = true;
                    session.editingTitle = session.title;
                };
                
                // 保存会话标题
                const saveSessionTitle = (session) => {
                    if (session.editingTitle) {
                        axios.post('/plugin/Customersystem/api/updateSessionTitle', {
                            session_id: session.id,
                            title: session.editingTitle
                        })
                        .then(response => {
                            if (response.data.code === 200) {
                                session.title = session.editingTitle;
                                session.isEditingTitle = false;
                                ElementPlus.ElMessage.success('会话标题已保存');
                                fetchData();
                            } else {
                                ElementPlus.ElMessage.error(response.data.msg || '保存会话标题失败');
                            }
                        })
                        .catch(error => {

                            ElementPlus.ElMessage.error('保存会话标题失败');
                        });
                    }
                };
                
                // 处理图片加载错误
                const handleImageError = (event, message) => {

                    
                    // 如果file_url加载失败，尝试使用message字段作为URL
                    if (event.target.src === message.file_url && message.message && message.message !== message.file_url) {

                        event.target.src = message.message;
                    } else {
                        // 显示默认错误图片
                        event.target.src = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJmZWF0aGVyIGZlYXRoZXItaW1hZ2UiPjxyZWN0IHg9IjMiIHk9IjMiIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgcng9IjIiIHJ5PSIyIj48L3JlY3Q+PGNpcmNsZSBjeD0iOC41IiBjeT0iOC41IiByPSIxLjUiPjwvY2lyY2xlPjxwb2x5bGluZSBwb2ludHM9IjIxIDE1IDE2IDEwIDUgMjEiPjwvcG9seWxpbmU+PC9zdmc+';
                        event.target.style.padding = '10px';
                        event.target.style.background = '#f5f5f5';
                        
                        // 确保容器有合适的尺寸显示错误图标
                        const container = event.target.closest('.message-image-container');
                        if (container) {
                            container.style.width = '120px';
                            container.style.height = '120px';
                            container.style.background = '#f5f5f5';
                            container.style.display = 'flex';
                            container.style.alignItems = 'center';
                            container.style.justifyContent = 'center';
                        }
                    }
                };
                
                // 格式化消息文本
                const formatMessage = (message) => {
                    if (!message) return '';
                    
                    // 检查是否是预设问题列表
                    if (message.startsWith('<div class="preset-questions">') && message.endsWith('</div>')) {
                        return message; // 直接返回预设问题HTML，不进行转义
                    }
                    
                    // 检查是否是<div class="preset-reply">格式的内容
                    if (message.startsWith('<div class="preset-reply">') && message.endsWith('</div>')) {
                        // 提取div中的内容，去掉首尾的div标签
                        message = message.substring('<div class="preset-reply">'.length, message.length - '</div>'.length);
                    }
                    
                    // 检查是否是图片URL，如果是，则返回特殊标记，在外部处理
                    if (isImageUrl(message)) {
                        return message; // 直接返回原始URL，不进行处理
                    }
                    
                    // 先处理HTML特殊字符防止XSS攻击
                    const safeMessage = message
                        .replace(/&/g, '&amp;')
                        .replace(/</g, '&lt;')
                        .replace(/>/g, '&gt;')
                        .replace(/"/g, '&quot;')
                        .replace(/'/g, '&#039;');
                    
                    // 将换行符转换为<br>标签
                    const withLineBreaks = safeMessage.replace(/\n/g, '<br>');
                    
                    // 检查是否有预设回复格式（多行内容）
                    if (withLineBreaks.includes('<br>')) {
                        // 内联样式实现与.preset-reply相同的效果
                        const presetStyles = 'background: #fff; border-radius: 8px; padding: 15px; margin: 10px 0; ' +
                            'box-shadow: 0 2px 12px rgba(0,0,0,0.1); border: 1px solid #e0e7ee; ' +
                            'position: relative; overflow: hidden; border-left: 4px solid var(--primary-color); ' +
                            'line-height: 1.6; color: #333;';
                        
                        // 将URL转换为可点击的链接，并添加内联样式
                        return `<div style="${presetStyles}">${withLineBreaks.replace(/(https?:\/\/[^\s<]+)/g, '<a href="$1" target="_blank" style="color: #3b82f6; text-decoration: underline;">$1</a>')}</div>`;
                    }
                    
                    // 将URL转换为可点击的链接
                    return withLineBreaks.replace(/(https?:\/\/[^\s<]+)/g, '<a href="$1" target="_blank" style="color: #3b82f6; text-decoration: underline;">$1</a>');
                };
                
                // 获取文件名
                const getFileName = (url) => {
                    if (!url) return '文件';
                    const parts = url.split('/');
                    return parts[parts.length - 1];
                };
                
                // 检测URL是否为图片链接
                const isImageUrl = (url) => {
                    if (!url || typeof url !== 'string') return false;
                    
                    // 检查是否是标准图片URL格式
                    if (url.match(/\.(jpeg|jpg|gif|png|webp|bmp|svg)(\?.*)?$/i)) return true;
                    
                    // 检查是否包含特定图片上传路径
                    if (url.includes('/upload/') && !url.includes('<') && !url.includes('>')) {
                        // 如果URL中包含upload且没有HTML标记，很可能是图片
                        return true;
                    }
                    
                    return false;
                };
                
                // 格式化消息日期（仅显示日期部分）
                const formatMessageDate = (timestamp) => {
                    if (!timestamp) return '-';
                    
                    const date = new Date(timestamp * 1000);
                    const today = new Date();
                    const yesterday = new Date(today);
                    yesterday.setDate(yesterday.getDate() - 1);
                    
                    // 检查是否是今天
                    if (date.toDateString() === today.toDateString()) {
                        return '今天';
                    }
                    
                    // 检查是否是昨天
                    if (date.toDateString() === yesterday.toDateString()) {
                        return '昨天';
                    }
                    
                    const year = date.getFullYear();
                    const month = (date.getMonth() + 1).toString().padStart(2, '0');
                    const day = date.getDate().toString().padStart(2, '0');
                    
                    return `${year}年${month}月${day}日`;
                };
                
                // 格式化消息时间（仅显示时间部分）
                const formatMessageTime = (timestamp) => {
                    if (!timestamp) return '';
                    
                    const date = new Date(timestamp * 1000);
                    const hours = date.getHours().toString().padStart(2, '0');
                    const minutes = date.getMinutes().toString().padStart(2, '0');
                    
                    return `${hours}:${minutes}`;
                };
                
                // 判断是否应该显示日期分隔线
                const shouldShowDateDivider = (message, index) => {
                    if (index === 0) return true;
                    
                    const currentDate = new Date(message.create_time * 1000).toDateString();
                    const prevDate = new Date(currentMessages.value[index - 1].create_time * 1000).toDateString();
                    
                    return currentDate !== prevDate;
                };
                
                // 检查消息是否可以撤回（2分钟内的消息且属于自己发送的）
                const canRecallMessage = (message) => {
                    // 只有客服/商家发送的消息可以撤回
                    if (message.role_type !== 'staff' && message.role_type !== 'merchant') {
                        return false;
                    }
                    
                    // 如果消息已经被撤回，不可再次撤回
                    if (message.is_recalled) {
                        return false;
                    }
                    
                    // 检查是否在2分钟内
                    const now = Math.floor(Date.now() / 1000);
                    const messageTime = message.create_time;
                    
                    return (now - messageTime) <= 120; // 2分钟 = 120秒
                };
                
                // 复制消息内容
                const copyMessage = (message) => {
                    // 获取纯文本内容，去除HTML标签
                    let textToCopy = '';
                    
                    if (message.message_type === 'text' || !message.message_type) {
                        // 创建临时元素来获取纯文本
                        const tempDiv = document.createElement('div');
                        tempDiv.innerHTML = message.message;
                        textToCopy = tempDiv.textContent || tempDiv.innerText || '';
                    } else if (message.message_type === 'file') {
                        textToCopy = message.file_url || '文件附件';
                    } else if (message.message_type === 'image') {
                        // 对于图片，复制图片URL
                        textToCopy = message.file_url || message.message || '';
                    }
                    
                    // 如果没有内容可复制
                    if (!textToCopy) {
                        ElementPlus.ElMessage.warning('无内容可复制');
                        return;
                    }
                    
                    // 使用现代剪贴板API复制文本
                    navigator.clipboard.writeText(textToCopy)
                        .then(() => {
                            ElementPlus.ElMessage.success('已复制到剪贴板');
                        })
                        .catch(err => {

                            ElementPlus.ElMessage.error('复制失败');
                            
                            // 尝试使用传统方法复制
                            fallbackCopy(textToCopy);
                        });
                };
                
                // 后备的复制方法
                const fallbackCopy = (text) => {
                    try {
                        const textarea = document.createElement('textarea');
                        textarea.value = text;
                        textarea.style.position = 'fixed';
                        textarea.style.opacity = '0';
                        document.body.appendChild(textarea);
                        textarea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textarea);
                        ElementPlus.ElMessage.success('已复制到剪贴板');
                    } catch (err) {
                        ElementPlus.ElMessage.error('复制失败，请手动复制');
                    }
                };
                
                // 撤回消息
                const recallMessage = (message) => {
                    ElementPlus.ElMessageBox.confirm('确定要撤回这条消息吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        axios.post('/plugin/Customersystem/api/recallMessage', {
                            message_id: message.id,
                            session_id: currentSession.value.id
                        })
                        .then(response => {
                            if (response.data.code === 200) {
                                ElementPlus.ElMessage.success('消息已撤回');
                                // 更新消息状态
                                message.message = '[该消息已撤回]';
                                message.is_recalled = 1;
                                
                                // 如果是图片或文件消息，清空file_url
                                if (message.message_type === 'image' || message.message_type === 'file') {
                                    message.file_url = '';
                                }
                                
                                // 更新会话的最后消息，如果该消息是最后一条
                                if (currentMessages.value[currentMessages.value.length - 1].id === message.id) {
                                    if (currentSession.value) {
                                        currentSession.value.last_message = '[该消息已撤回]';
                                    }
                                }
                            } else {
                                ElementPlus.ElMessage.error(response.data.msg || '撤回失败');
                            }
                        })
                        .catch(error => {

                            ElementPlus.ElMessage.error('撤回消息失败');
                        });
                    }).catch(() => {
                        // 用户取消操作
                    });
                };
                
                // 添加全局粘贴事件处理函数以及键盘处理函数
                // ... existing code ...
                // 处理全局粘贴事件
                const handleGlobalPaste = (e) => {
                    // 只有当对话框打开且输入框被聚焦时才处理粘贴
                    if (!dialogVisible.value) return;
                    
                    // 检查当前焦点是否在回复输入框内
                    const isInputFocused = document.activeElement.classList.contains('el-textarea__inner');
                    const isTextArea = document.activeElement.tagName === 'TEXTAREA';
                    
                    // 如果焦点不在输入框内则不处理
                    if (!isInputFocused && !isTextArea) return;
                    
                    const items = e.clipboardData && e.clipboardData.items;
                    let file = null;
                    
                    if (items && items.length) {
                        // 遍历剪贴板中的内容，查找图片
                        for (let i = 0; i < items.length; i++) {
                            if (items[i].type.indexOf('image') !== -1) {
                                file = items[i].getAsFile();
                                break;
                            }
                        }
                    }
                    
                    // 如果找到图片文件，则上传
                    if (file) {
                        // 阻止默认粘贴行为，防止图片URL被粘贴到输入框
                        e.preventDefault();
                        
                        // 检查文件大小
                        if (!beforeUpload(file)) {
                            return;
                        }
                        
                        // 开始上传
                        ElementPlus.ElMessage.info('正在上传剪贴板图片...');
                        
                        // 创建FormData对象
                        const formData = new FormData();
                        formData.append('file', file, 'clipboard_image.png');
                        
                        // 发送上传请求
                        axios.post('/adminApi/Upload/file', formData, {
                            headers: {
                                'Content-Type': 'multipart/form-data'
                            }
                        })
                        .then(response => {
                            // 处理上传成功结果
                            handleUploadSuccess(response.data);
                        })
                        .catch(error => {

                            ElementPlus.ElMessage.error('上传剪贴板图片失败');
                        });
                    }
                };
                
                // 处理图片预览键盘事件
                const handleImagePreviewKeydown = (e) => {
                    // 如果图片预览窗口打开
                    if (imagePreviewVisible.value) {
                        // Esc键关闭预览
                        if (e.key === 'Escape') {
                            imagePreviewVisible.value = false;
                        }
                    }
                };
                
                // 处理键盘快捷键
                const handleKeyDown = (e) => {
                    // 如果对话框打开
                    if (dialogVisible.value) {
                        // Ctrl+Enter 发送消息
                        if (e.ctrlKey && e.key === 'Enter') {
                            e.preventDefault();
                            sendReply();
                        }
                    }
                };
                // ... existing code ...
                
                onMounted(() => {
                    fetchData();
                    fetchChatStatus();
                    // 开始轮询获取新消息
                    startPolling();
                    
                    // 立即检查一次未读消息
                    countStatistics();

                    // 优化定时器，减少检查频率（每60秒）
                    const unreadCheckTimer = setInterval(() => {
                        // 只在页面可见时更新统计数据
                        if (document.visibilityState === 'visible') {
                            countStatistics();
                        }
                    }, 60000);
                    
                    // 设置快捷键监听
                    document.addEventListener('keydown', handleKeyDown);
                    
                    // 添加全局粘贴事件处理
                    document.addEventListener('paste', handleGlobalPaste);
                    
                    // 添加图片预览键盘支持
                    document.addEventListener('keydown', handleImagePreviewKeydown);
                    
                    // 调整初始高度
                    nextTick(() => {
                        adjustChatContainerHeight();
                    });
                    
                    // 监听对话框打开事件，添加最大化按钮
                    watch(dialogVisible, (newVal) => {
                        if (newVal) {
                            // 给对话框添加最大化按钮
                            nextTick(() => {
                                const dialogHeader = document.querySelector('.el-dialog__header');
                                if (dialogHeader && !dialogHeader.querySelector('.maximize-btn')) {
                                    const maxBtn = document.createElement('button');
                                    maxBtn.className = 'maximize-btn';
                                    maxBtn.innerHTML = '<i class="el-icon-full-screen"></i>';
                                    maxBtn.addEventListener('click', toggleDialogMaximize);
                                    
                                    // 在关闭按钮前插入最大化按钮
                                    const closeBtn = dialogHeader.querySelector('.el-dialog__close');
                                    if (closeBtn && closeBtn.parentNode) {
                                        closeBtn.parentNode.insertBefore(maxBtn, closeBtn);
                                    } else {
                                        dialogHeader.appendChild(maxBtn);
                                    }
                                }
                                
                                // 根据当前最大化状态更新按钮图标
                                updateMaximizeButtonIcon();
                            });
                        }
                    });
                    
                    // 添加页面可见性变化监听
                    document.addEventListener('visibilitychange', handleVisibilityChange);
                    
                    // 检查通知权限
                    if ("Notification" in window) {
                        notificationPermission.value = Notification.permission;
                        
                        // 从localStorage读取用户的通知偏好
                        const savedPreference = localStorage.getItem('notification_enabled');
                        
                        // 检查是否是安全上下文（https或localhost）
                        const isSecureContext = window.isSecureContext || 
                                              window.location.protocol === 'https:' || 
                                              window.location.hostname === 'localhost' ||
                                              window.location.hostname === '127.0.0.1';
                        
                        // 如果已有权限，且用户偏好为开启（或之前未设置但权限已授权）
                        if (notificationPermission.value === "granted" && 
                            (savedPreference === 'true' || savedPreference === null)) {
                            saveNotificationSetting(true);
                            
                            // 延迟5秒后发送一个测试通知（如果是非已打开会话）
                            setTimeout(() => {
                                if (!dialogVisible.value) {
                                    showNotification('客服系统已准备就绪', '您已启用桌面通知功能，有新消息时将在这里提醒您', null);
                                }
                            }, 5000);
                        } 
                        // 如果权限被拒绝，强制设置为关闭状态
                        else if (notificationPermission.value === "denied") {
                            saveNotificationSetting(false);
                        }
                        // 如果在安全上下文中且权限状态为default(未决定)
                        else if (notificationPermission.value === "default" && isSecureContext) {
                            // 如果用户之前选择过开启，则请求权限
                            if (savedPreference === 'true') {
                                // 延迟3秒后请求通知权限
                                setTimeout(() => {
                                    requestNotificationPermission();
                                }, 3000);
                            }
                        }
                        // 如果不是安全上下文，设置为关闭并显示提示
                        else if (!isSecureContext) {
                            saveNotificationSetting(false);
                            ElementPlus.ElMessage.warning({
                                message: '当前页面不在安全上下文（HTTPS），通知功能可能无法使用',
                                duration: 5000
                            });
                        }
                    }
                    
                    // 返回清除函数
                    return () => {
                        clearInterval(unreadCheckTimer);
                    };
                });
                
                onBeforeUnmount(() => {
                    // 确保清理所有定时器
                    stopPolling();
                    
                    // 移除事件监听器
                    document.removeEventListener('keydown', handleKeyDown);
                    document.removeEventListener('paste', handleGlobalPaste);
                    document.removeEventListener('keydown', handleImagePreviewKeydown);
                    
                    // 移除页面可见性变化监听
                    document.removeEventListener('visibilitychange', handleVisibilityChange);
                });
                
                // 切换对话框最大化状态
                const toggleDialogMaximize = () => {
                    isDialogMaximized.value = !isDialogMaximized.value;
                    // 更新按钮图标
                    updateMaximizeButtonIcon();
                    // 调整对话框内容高度
                    nextTick(() => {
                        adjustChatContainerHeight();
                    });
                };
                
                // 通知声音已被移除
                const playNotificationSound = () => {
                    // 空函数，通知声音功能已禁用
                    // 如需启用，请先上传声音文件到/static/plugins/Customersystem/sounds/目录
                };
                
                // 更新最大化按钮图标
                const updateMaximizeButtonIcon = () => {
                    const maxBtn = document.querySelector('.maximize-btn i');
                    if (maxBtn) {
                        if (isDialogMaximized.value) {
                            maxBtn.className = 'el-icon-minus';
                        } else {
                            maxBtn.className = 'el-icon-full-screen';
                        }
                    }
                };
                
                // 标记单条消息为已读
                const markMessageAsRead = (messageId) => {
                    if (!currentSession.value || !messageId) return;
                    
                    axios.post('/plugin/Customersystem/api/markMessageRead', {
                        message_id: messageId,
                        session_id: currentSession.value.id,
                        role_type: 'staff',  // 添加角色类型，这里是客服管理界面
                        viewer_role: 'staff'  // 明确表示客服视角
                    })
                    .then(response => {
                        if (response.data.code === 200) {
                            // 更新本地消息状态
                            const index = currentMessages.value.findIndex(msg => msg.id === messageId);
                            if (index !== -1) {
                                currentMessages.value[index].is_read = 1;
                                currentMessages.value[index].read_time = Math.floor(Date.now() / 1000);
                                // 更新角色特定的已读状态
                                currentMessages.value[index].staff_read = 1;
                                currentMessages.value[index].staff_read_time = Math.floor(Date.now() / 1000);
                            }
                            
                            // 减少会话的未读消息计数（如果当前为1）
                            if (currentSession.value && currentSession.value.unread_count > 0) {
                                // 不直接减1，而是交给后端准确计算
                                // 重新获取最新消息和未读计数
                                pollNewMessages();
                            }
                        }
                    })
                    .catch(error => {
                        // 静默处理错误，避免对用户体验造成干扰

                    });
                };
                
                // 批量标记会话中的所有消息为已读
                const markAllSessionMessagesAsRead = () => {
                    if (!currentSession.value) return;
                    
                    axios.post('/plugin/Customersystem/api/markSessionMessagesRead', {
                        session_id: currentSession.value.id,
                        role_type: 'staff', // 标记自己作为客服收到的客户消息
                        viewer_role: 'staff' // 添加明确的查看者角色参数，确保一致性
                    })
                    .then(response => {
                        if (response.data.code === 200) {
                            // 更新所有客户消息的已读状态
                            currentMessages.value.forEach(msg => {
                                if (msg.role_type === 'customer' && !msg.is_read) {
                                    msg.is_read = 1;
                                    msg.read_time = Math.floor(Date.now() / 1000);
                                    // 更新客服特定已读状态
                                    msg.staff_read = 1;
                                    msg.staff_read_time = Math.floor(Date.now() / 1000);
                                }
                            });
                            
                            // 更新会话的未读计数（从响应中获取）
                            if (response.data.data && typeof response.data.data.unread_count !== 'undefined') {
                                if (currentSession.value) {
                                    currentSession.value.unread_count = response.data.data.unread_count;
                                    
                                    // 更新会话列表中对应会话的未读计数
                                    const sessionIndex = sessions.value.findIndex(s => s.id === currentSession.value.id);
                                    if (sessionIndex !== -1) {
                                        sessions.value[sessionIndex].unread_count = response.data.data.unread_count;
                                    }
                                }
                            }
                            
                            // 更新未读消息数量
                            countStatistics();
                        }
                    })
                    .catch(error => {
                        // 静默处理错误

                    });
                };
                
                // 标记会话中所有非客服消息为已读
                const markAllCustomerMessagesAsRead = () => {
                    if (!currentSession.value) return;
                    
                    // 查找所有未读的客户消息
                    const customerMessages = currentMessages.value.filter(msg => 
                        msg.role_type === 'customer' && (!msg.staff_read)
                    );
                    
                    if (customerMessages.length > 0) {
                        axios.post('/plugin/Customersystem/api/markSessionMessagesRead', {
                            session_id: currentSession.value.id,
                            role_type: 'staff', // 标记客服收到的客户消息
                            viewer_role: 'staff', // 添加明确的查看者角色参数
                            is_visible: document.visibilityState === 'visible' ? 1 : 0 // 添加页面可见性参数
                        })
                        .then(response => {
                            if (response.data.code === 200) {
                                // 只有当请求成功并且页面明确可见时才更新本地消息状态
                                if (document.visibilityState === 'visible') {
                                    // 更新本地消息状态
                                    customerMessages.forEach(msg => {
                                        msg.staff_read = 1;
                                        // 兼容旧代码，但实际不再依赖
                                        msg.is_read = 1;
                                    });
                                    
                                    // 更新未读消息计数
                                    updateUnreadCount();
                                }
                            }
                        })
                        .catch(error => {

                        });
                    }
                };
                
                // 更新消息已读状态
                const checkAndUpdateReadStatus = () => {
                    if (!currentSession.value) return;
                    
                    // 获取所有未读的客户消息（对客服来说未读）
                    const pendingMessages = currentMessages.value.filter(msg => 
                        msg.role_type === 'customer' && (!msg.is_read || !msg.staff_read)
                    );
                    
                    // 使用最新消息ID查询，这样可以一次性获取所有可能的状态更新
                    if (pendingMessages.length > 0) {
                        axios.post('/plugin/Customersystem/api/getRealtimeMessages', {
                            session_id: currentSession.value.id,
                            last_message_id: 0, // 使用0代表只查询已读状态变更，不需要新消息
                            check_session_status: false,
                            check_recalled: false,
                            check_read_status: true,
                            sender_type: 'staff',
                            viewer_role: 'staff' // 添加查看者角色
                        })
                        .then(response => {
                            if (response.data.code === 200) {
                                const responseData = response.data.data;
                                const readStatusChangedMessages = responseData.read_status_changed || [];
                                
                                if (readStatusChangedMessages.length > 0) {
                                    // 更新消息已读状态
                                    readStatusChangedMessages.forEach(message => {
                                        const index = currentMessages.value.findIndex(msg => msg.id === message.id);
                                        if (index !== -1) {
                                            currentMessages.value[index].is_read = message.is_read;
                                            currentMessages.value[index].read_time = message.read_time;
                                            // 更新客服特定已读状态
                                            if (message.staff_read) {
                                                currentMessages.value[index].staff_read = message.staff_read;
                                                currentMessages.value[index].staff_read_time = message.staff_read_time;
                                            }
                                        }
                                    });
                                    

                                    
                                    // 更新未读消息统计
                                    countStatistics();
                                }
                            }
                        })
                        .catch(error => {

                        });
                    }
                };
                
                // 启动已读状态轮询 - 优化性能
                let readStatusPollingTimer = null;
                const readStatusPollingInterval = 15000; // 增加到15秒检查一次已读状态
                
                const startReadStatusPolling = () => {
                    // 如果已经有轮询，先停止
                    stopReadStatusPolling();
                    
                    // 启动轮询
                    readStatusPollingTimer = setInterval(() => {
                        if (dialogVisible.value && currentSession.value) {
                            // 只在有未读的客户消息时进行轮询（针对客服角色）
                            const pendingMessages = currentMessages.value.filter(msg => 
                                msg.role_type === 'customer' && (!msg.is_read || !msg.staff_read)
                            );
                            
                            if (pendingMessages.length > 0) {
                                // 静默查询，不显示加载状态
                                checkAndUpdateReadStatus();
                            }
                        }
                    }, readStatusPollingInterval);
                };
                
                // 停止已读状态轮询
                const stopReadStatusPolling = () => {
                    if (readStatusPollingTimer) {
                        clearInterval(readStatusPollingTimer);
                        readStatusPollingTimer = null;
                    }
                };
                
                // 在每次打开对话框时自动将所有消息标记为已读
                const markMessagesReadOnDialogOpen = () => {
                    if (currentSession.value) {
                        // 将所有客户消息标记为已读
                        markAllCustomerMessagesAsRead();
                        
                        // 更新当前会话的未读计数
                        if (currentSession.value.unread_count > 0) {
                            currentSession.value.unread_count = 0;
                            
                            // 刷新列表中的会话
                            const index = sessions.value.findIndex(s => s.id === currentSession.value.id);
                            if (index !== -1) {
                                sessions.value[index].unread_count = 0;
                            }
                            
                            // 强制更新统计数据
                            countStatistics();
                        }
                    }
                };
                
                // 创建右上角通知开关组件
                const headerComponent = computed(() => {
                    return {
                        render() {
                            return h('div', { style: 'display: flex; align-items: center;' }, [
                                h('div', { style: 'margin-right: 10px;' }, [
                                    h('el-tooltip', { 
                                        content: notificationEnabled.value ? '桌面通知已启用' : '桌面通知已禁用',
                                        placement: 'bottom' 
                                    }, [
                                        h('el-switch', {
                                            modelValue: notificationEnabled.value,
                                            'onUpdate:modelValue': (val) => {
                                                if (val && notificationPermission.value !== 'granted') {
                                                    requestNotificationPermission();
                                                } else {
                                                    saveNotificationSetting(val);
                                                }
                                            },
                                            activeColor: '#67C23A',
                                            activeText: '桌面通知',
                                            inactiveText: '桌面通知',
                                            style: 'margin-right: 15px;'
                                        })
                                    ])
                                ]),
                                h('el-button', {
                                    type: 'primary',
                                    size: 'default',
                                    onClick: goToSettings
                                }, [
                                    h('i', { class: 'el-icon-setting', style: 'margin-right: 5px;' }),
                                    '系统设置'
                                ])
                            ]);
                        }
                    };
                });
                
                // 发送邮件通知
                const sendEmailNotification = () => {

                    
                    if (!currentSession.value || !currentSession.value.contact) {
                        ElementPlus.ElMessage.error('无法发送邮件，会话信息不存在');
                        return;
                    }
                    
                    if (!emailForm.value.subject.trim()) {
                        ElementPlus.ElMessage.warning('请输入邮件标题');
                        return;
                    }
                    
                    if (!emailForm.value.content.trim()) {
                        ElementPlus.ElMessage.warning('请输入邮件内容');
                        return;
                    }
                    
                    sendingEmail.value = true;
                    
                    const postData = {
                        session_id: currentSession.value.id,
                        subject: emailForm.value.subject,
                        content: emailForm.value.content
                    };
                    

                    
                    // 发送邮件请求
                    axios.post('/plugin/Customersystem/api/sendNotificationEmail', postData)
                        .then(response => {

                            sendingEmail.value = false;
                            
                            if (response.data.code === 200) {
                                ElementPlus.ElMessage.success('邮件发送成功');
                                emailDialogVisible.value = false;
                                
                                // 重置表单
                                emailForm.value.subject = '客服系统消息通知';
                                emailForm.value.content = '尊敬的用户，您好！\n\n您有新的客服消息，请登录系统查看您的在线客服内容。\n\n如有任何问题，请随时联系我们。\n\n此致\n客服团队';
                                
                                // 添加到系统消息
                                const systemMessage = {
                                    id: Date.now(),
                                    session_id: currentSession.value.id,
                                    sender_type: 'staff',
                                    sender_id: 0,
                                    role_type: 'merchant',
                                    message: `已向客户发送邮件通知（${emailForm.value.subject}）`,
                                    message_type: 'text',
                                    is_read: 0,
                                    is_system: 1,
                                    create_time: Math.floor(Date.now() / 1000)
                                };
                                
                                // 添加系统消息到当前消息列表
                                if (dialogVisible.value && currentMessages.value) {
                                    currentMessages.value.push(systemMessage);
                                }
                                
                                // 如果当前有打开的会话详情，刷新消息列表
                                if (dialogVisible.value && currentSession.value) {
                                    setTimeout(() => {
                                        handleViewSession(currentSession.value);
                                    }, 1000);
                                }
                            } else {
                                const errorMsg = response.data.msg || '发送邮件失败';

                                ElementPlus.ElMessage.error(errorMsg);
                            }
                        })
                        .catch(error => {

                            sendingEmail.value = false;
                            
                            let errorMsg = '发送邮件失败';
                            if (error.response && error.response.data && error.response.data.msg) {
                                errorMsg += ': ' + error.response.data.msg;
                            } else if (error.message) {
                                errorMsg += ': ' + error.message;
                            }
                            
                            ElementPlus.ElMessage.error(errorMsg);
                        });
                };
                
                // 处理发送邮件给商家
                const handleSendMerchantEmail = (session) => {
                    if (!session) {
                        ElementPlus.ElMessage.error('会话信息为空，无法发送邮件');
                        return;
                    }
                    
                    currentSession.value = session;
                    
                    // 显示发送中提示
                    ElementPlus.ElMessage.info('正在查找商家信息并发送通知...');
                    
                    // 调用API获取商家信息并发送邮件
                    axios.post('/plugin/Customersystem/api/sendMerchantNotificationEmail', {
                        session_id: session.id
                    })
                    .then(response => {
                        if (response.data.code === 200) {
                            ElementPlus.ElMessage.success('已成功发送通知给商家');
                            
                            // 添加到系统消息
                            const systemMessage = {
                                id: Date.now(),
                                session_id: session.id,
                                sender_type: 'staff',
                                sender_id: 0,
                                role_type: 'staff',
                                message: `已向商家发送邮件通知`,
                                message_type: 'text',
                                is_read: 0,
                                is_system: 1,
                                create_time: Math.floor(Date.now() / 1000)
                            };
                            
                            // 添加系统消息到当前消息列表
                            if (dialogVisible.value && currentMessages.value) {
                                currentMessages.value.push(systemMessage);
                            }
                            
                            // 如果当前有打开的会话详情，刷新消息列表
                            if (dialogVisible.value && currentSession.value) {
                                setTimeout(() => {
                                    handleViewSession(currentSession.value);
                                }, 1000);
                            }
                        } else {
                            ElementPlus.ElMessage.error(response.data.msg || '发送通知失败');
                        }
                    })
                    .catch(error => {
                        let errorMsg = '发送通知失败';
                        if (error.response && error.response.data && error.response.data.msg) {
                            errorMsg += ': ' + error.response.data.msg;
                        } else if (error.message) {
                            errorMsg += ': ' + error.message;
                        }
                        
                        ElementPlus.ElMessage.error(errorMsg);
                    });
                };
                
                // 处理发送邮件
                const handleSendEmail = (session) => {

                    if (!session) {

                        ElementPlus.ElMessage.error('会话信息为空，无法发送邮件');
                        return;
                    }
                    
                    currentSession.value = session;
                    
                    // 获取会话关联的联系人信息

                    axios.get(`/plugin/Customersystem/api/getSessionDetail?id=${session.id}`)
                        .then(response => {

                            if (response.data.code === 200) {
                                const data = response.data.data;
                                currentSession.value = data.session;
                                currentSession.value.contact = data.contact;
                                
                                // 如果联系人没有邮箱，给出提示
                                if (!currentSession.value.contact || !currentSession.value.contact.email) {
                                    ElementPlus.ElMessage.error('该联系人没有邮箱信息，无法发送邮件');
                                    return;
                                }
                                
                                // 直接发送邮件，不打开对话框
                                const email = currentSession.value.contact.email;
                                const subject = '客服系统消息通知';
                                const content = '尊敬的用户，您好！\n\n您有新的客服消息，请登录系统查看您的在线客服内容。\n\n如有任何问题，请随时联系我们。\n\n此致\n客服团队';
                                
                                // 显示发送中提示
                                sendingEmail.value = true;
                                ElementPlus.ElMessage.info('正在发送邮件通知...');
                                
                                // 直接调用API发送邮件
                                const postData = {
                                    session_id: currentSession.value.id,
                                    subject: subject,
                                    content: content
                                };
                                

                                
                                // 发送邮件请求
                                axios.post('/plugin/Customersystem/api/sendNotificationEmail', postData)
                                    .then(response => {

                                        sendingEmail.value = false;
                                        
                                        if (response.data.code === 200) {
                                            ElementPlus.ElMessage.success('邮件发送成功');
                                            
                                            // 添加到系统消息
                                            const systemMessage = {
                                                id: Date.now(),
                                                session_id: currentSession.value.id,
                                                sender_type: 'staff',
                                                sender_id: 0,
                                                role_type: 'merchant',
                                                message: `已向客户发送邮件通知（${subject}）`,
                                                message_type: 'text',
                                                is_read: 0,
                                                is_system: 1,
                                                create_time: Math.floor(Date.now() / 1000)
                                            };
                                            
                                            // 添加系统消息到当前消息列表
                                            if (dialogVisible.value && currentMessages.value) {
                                                currentMessages.value.push(systemMessage);
                                            }
                                            
                                            // 如果当前有打开的会话详情，刷新消息列表
                                            if (dialogVisible.value && currentSession.value) {
                                                setTimeout(() => {
                                                    handleViewSession(currentSession.value);
                                                }, 1000);
                                            }
                                        } else {
                                            const errorMsg = response.data.msg || '发送邮件失败';

                                            ElementPlus.ElMessage.error(errorMsg);
                                        }
                                    })
                                    .catch(error => {

                                        sendingEmail.value = false;
                                        
                                        let errorMsg = '发送邮件失败';
                                        if (error.response && error.response.data && error.response.data.msg) {
                                            errorMsg += ': ' + error.response.data.msg;
                                        } else if (error.message) {
                                            errorMsg += ': ' + error.message;
                                        }
                                        
                                        ElementPlus.ElMessage.error(errorMsg);
                                    });
                            } else {
                                ElementPlus.ElMessage.error(response.data.msg || '获取联系人信息失败');
                            }
                        })
                        .catch(error => {

                            ElementPlus.ElMessage.error('获取联系人信息失败');
                        });
                };
                
                // 添加刷新会话的方法
                const refreshSessions = () => {
                    fetchData();
                    ElementPlus.ElMessage.success('会话列表已刷新');
                };
                
                // 批量标记所有可见会话为已读
                const markAllSessionsAsRead = () => {
                    if (filteredSessions.value.length === 0) {
                        ElementPlus.ElMessage.warning('没有可标记的会话');
                        return;
                    }
                    
                    markingAsRead.value = true;
                    ElementPlus.ElMessage.info('正在处理批量已读请求...');
                    
                    // 获取所有有未读消息的会话ID
                    const unreadSessionIds = filteredSessions.value
                        .filter(session => session.unread_count > 0)
                        .map(session => session.id);
                    
                    if (unreadSessionIds.length === 0) {
                        ElementPlus.ElMessage.info('所有会话都已读');
                        markingAsRead.value = false;
                        return;
                    }
                    
                    // 使用Promise.all并行处理所有标记请求
                    const markPromises = unreadSessionIds.map(sessionId => {
                        return axios.post('/plugin/Customersystem/api/markSessionMessagesRead', {
                            session_id: sessionId,
                            role_type: 'staff',
                            viewer_role: 'staff'
                        });
                    });
                    
                    Promise.all(markPromises)
                        .then(responses => {
                            // 检查是否所有请求都成功
                            const allSuccess = responses.every(response => response.data.code === 200);
                            
                            if (allSuccess) {
                                ElementPlus.ElMessage.success(`已将 ${unreadSessionIds.length} 个会话标记为已读`);
                                
                                // 更新本地会话状态
                                filteredSessions.value.forEach(session => {
                                    if (unreadSessionIds.includes(session.id)) {
                                        session.unread_count = 0;
                                    }
                                });
                                
                                // 刷新统计数据
                                countStatistics();
                            } else {
                                ElementPlus.ElMessage.warning('部分会话标记失败，请刷新后重试');
                            }
                        })
                        .catch(error => {
                            ElementPlus.ElMessage.error('批量标记已读失败');

                        })
                        .finally(() => {
                            markingAsRead.value = false;
                        });
                };
                
                // 批量关闭会话
                const closeSelectedSessions = () => {
                    if (selectedSessions.value.length === 0) {
                        ElementPlus.ElMessage.warning('请先选择要关闭的会话');
                        return;
                    }
                    
                    // 过滤出处于打开状态的会话
                    const openSessions = selectedSessions.value.filter(session => session.status === 'open');
                    
                    if (openSessions.length === 0) {
                        ElementPlus.ElMessage.warning('所选会话中没有处于打开状态的会话');
                        return;
                    }
                    
                    ElementPlus.ElMessageBox.confirm(`确定要关闭选中的 ${openSessions.length} 个会话吗？`, '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        // 使用Promise.all并行处理所有关闭请求
                        const closePromises = openSessions.map(session => {
                            return axios.post('/plugin/Customersystem/api/updateSessionStatus', {
                                session_id: session.id,
                                status: 'closed'
                            });
                        });
                        
                        Promise.all(closePromises)
                            .then(responses => {
                                // 检查是否所有请求都成功
                                const allSuccess = responses.every(response => response.data.code === 200);
                                
                                if (allSuccess) {
                                    ElementPlus.ElMessage.success(`已关闭 ${openSessions.length} 个会话`);
                                    
                                    // 更新本地会话状态
                                    openSessions.forEach(session => {
                                        session.status = 'closed';
                                    });
                                    
                                    // 如果当前有打开的会话详情且是被关闭的会话之一，更新其状态
                                    if (currentSession.value) {
                                        const closedSession = openSessions.find(s => s.id === currentSession.value.id);
                                        if (closedSession) {
                                            currentSession.value.status = 'closed';
                                        }
                                    }
                                    
                                    // 刷新数据
                                    fetchData();
                                    
                                    // 退出选择模式
                                    isSelectingSessions.value = false;
                                    selectedSessions.value = [];
                                } else {
                                    ElementPlus.ElMessage.warning('部分会话关闭失败，请刷新后重试');
                                    // 刷新数据
                                    fetchData();
                                }
                            })
                            .catch(error => {
                                ElementPlus.ElMessage.error('批量关闭会话失败');

                            });
                    }).catch(() => {
                        // 取消操作
                    });
                };

                // 计算表格高度 - 根据实际数据量自适应
                const getTableHeight = () => {
                    // 安全检查，确保 filteredSessions 存在
                    const dataLength = (filteredSessions.value && Array.isArray(filteredSessions.value))
                        ? filteredSessions.value.length
                        : 0;

                    // 如果没有数据，返回最小高度
                    if (dataLength === 0) {
                        return 150; // 无数据时显示较小高度
                    }

                    // 根据屏幕尺寸调整行高
                    let rowHeight = 60; // 默认桌面端行高
                    if (window.innerWidth <= 480) {
                        rowHeight = 50; // 手机端
                    } else if (window.innerWidth <= 768) {
                        rowHeight = 55; // 平板端
                    } else if (window.innerWidth <= 991) {
                        rowHeight = 52; // 手机横屏
                    }

                    const headerHeight = rowHeight;
                    // 根据实际数据量计算高度，不设置最大行数限制
                    const totalHeight = headerHeight + (dataLength * rowHeight);

                    return totalHeight;
                };

                // 计算表格最大高度 - 设置合理的最大高度限制
                const getMaxTableHeight = () => {
                    // 根据视窗高度设置最大高度，避免表格过高
                    const viewportHeight = window.innerHeight;
                    const maxTableHeight = Math.min(viewportHeight * 0.6, 800); // 最多占视窗60%或800px

                    return maxTableHeight;
                };

                return {
                    // 数据
                    totalSessions,
                    openSessions,
                    unreadMessages,
                    chatEnabled,
                    sessions,
                    loading,
                    currentPage,
                    limit,
                    total,
                    activeTab,
                    searchKeyword,
                    dialogVisible,
                    currentSession,
                    currentMessages,
                    replyMessage,
                    sendingReply,
                    uploadedFile,
                    imagePreviewVisible,
                    previewImageUrl,
                    params,
                    isSelectingSessions,
                    selectedSessions,
                    
                    // 计算属性
                    filteredSessions,
                    hasOpenSessionsSelected,
                    openSelectedSessionsCount,
                    
                    // 方法
                    fetchData,
                    refreshSessions,
                    markAllSessionsAsRead, // 添加批量已读方法
                    closeSelectedSessions,
                    toggleChatStatus,
                    handlePageChange,
                    changeTabToSessions,
                    changeTabToUnread,
                    handleSessionClick,
                    handleViewSession,
                    handleCloseDialog,
                    handleToggleSessionStatus,
                    sendReply,
                    beforeUpload,
                    handleUploadSuccess,
                    previewImage,
                    goToChatPage,
                    goToSettings,
                    formatTime,
                    usePresetReply,
                    useQuickReply,
                    toggleSessionSelection,
                    handleSessionSelectionChange,
                    deleteSelectedSessions,
                    
                    // 邮件发送相关方法
                    handleSendEmail,
                    handleSendMerchantEmail, // 添加商家通知方法
                    
                    // 选择相关
                    isSelecting,
                    selectedMessages,
                    markingAsRead, // 添加标记状态变量
                    toggleMessageSelection,
                    updateSelectedMessages,
                    confirmClearMessages,
                    deleteSelectedMessages,
                    
                    // 新方法
                    startEditTitle,
                    saveSessionTitle,
                    handleDialogOpened,
                    adjustChatContainerHeight,
                    handleImageError,
                    formatMessage,
                    // 会话记录美化相关方法
                    getFileName,
                    formatMessageDate,
                    formatMessageTime,
                    shouldShowDateDivider,
                    canRecallMessage,
                    copyMessage,
                    recallMessage,
                    isDialogMaximized,
                    toggleDialogMaximize,
                    markMessageAsRead,
                    markAllSessionMessagesAsRead,
                    markAllCustomerMessagesAsRead,
                    checkAndUpdateReadStatus,
                    startReadStatusPolling,
                    stopReadStatusPolling,
                    markMessagesReadOnDialogOpen,
                    handleVisibilityChange,
                    getReadStatus,
                    isImageUrl,
                    // 通知相关
                    notificationEnabled,
                    notificationPermission,
                    requestNotificationPermission,
                    saveNotificationSetting,

                    // 表格高度计算方法
                    getMaxTableHeight
                };
            }
        };
        
        const app = createApp(App);
        app.use(ElementPlus, {
            locale: ElementPlusLocaleZhCn
        });
        app.mount('#app');
    </script>
</body>
</html> 