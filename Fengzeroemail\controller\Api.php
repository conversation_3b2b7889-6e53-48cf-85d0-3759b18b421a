<?php

namespace plugin\Fengzeroemail\controller;

use app\common\controller\BasePlugin;
use think\facade\Db;
use think\facade\View;
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

class Api extends BasePlugin
{
    protected $scene = ['admin'];
    protected $noNeedLogin = [];

    public function index()
    {
        return View::fetch();
    }

    public function queryDomainStatus()
    {
        $url = $this->request->post('url', '');

        if (empty($url)) {
            return json(['code' => 0, 'msg' => '域名无效']);
        }

        $checkUrl = 'https://cgi.urlsec.qq.com/index.php?m=url&a=validUrl&url=' . urlencode($url);
        $ch = curl_init($checkUrl);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $response = curl_exec($ch);
        if ($response === false) {
            return json(['code' => 500, 'msg' => '请求接口发生错误: ' . curl_error($ch)]);
        }
        curl_close($ch);

        $data_msg = json_decode($response)->data;
        if ($data_msg == 'ok') {
            $status = '域名被拦截';
        } else {
            $status = '域名正常';
        }

        return json(['code' => 200, 'data' => ['status' => $status], 'msg' => '查询成功']);
    }

    public function sendEmail()
    {
        $url = $this->request->post('url', '');
        $status = $this->request->post('status', '');
        $recipientEmail = $this->request->post('recipient_email', '');

        if (empty($url) || empty($status) || empty($recipientEmail)) {
            return json(['code' => 0, 'msg' => '未输入收件箱或参数无效']);
        }

        $config = [
            'smtp_host' => Db::name('system_config')->where('name', 'smtp_host')->value('value'),
            'smtp_port' => Db::name('system_config')->where('name', 'smtp_port')->value('value'),
            'smtp_name' => Db::name('system_config')->where('name', 'smtp_name')->value('value'),
            'smtp_username' => Db::name('system_config')->where('name', 'smtp_username')->value('value'),
            'smtp_password' => Db::name('system_config')->where('name', 'smtp_password')->value('value'),
        ];

        if (empty($config['smtp_host']) || empty($config['smtp_port']) || empty($config['smtp_name']) || empty($config['smtp_username']) || empty($config['smtp_password'])) {
            return json(['code' => 0, 'msg' => '未找到邮箱配置']);
        }

        $mail = new PHPMailer(true);

        try {
            $mail->isSMTP();
            $mail->Host = $config['smtp_host'];
            $mail->SMTPAuth = true;
            $mail->Username = $config['smtp_username'];
            $mail->Password = $config['smtp_password'];
            $mail->SMTPSecure = 'ssl';
            $mail->Port = (int)$config['smtp_port'];

            $mail->setFrom($config['smtp_username'], $config['smtp_name']);
            $mail->addAddress($recipientEmail, 'Recipient Name');

            $mail->isHTML(true);
            $mail->Subject = '域名检测报告';
            $mail->Body = "域名: $url<br>状态: $status";

            $mail->send();
            return json(['code' => 200, 'msg' => '邮件发送成功']);
        } catch (Exception $e) {
            return json(['code' => 0, 'msg' => '邮件发送失败: ' . $e->getMessage()]);
        }
    }
}