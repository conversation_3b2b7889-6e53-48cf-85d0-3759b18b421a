<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>支付图标替换</title>
    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>
</head>
<body>

<div id="app">
    <el-card>
        <div class="title">支付图标列表</div>
        <div style="margin-bottom: 20px;">
            <el-input
                v-model="searchQuery"
                placeholder="搜索支付名称"
                style="width: 200px;"
                @keyup.enter="handleSearch"
            >
                <template #append>
                    <el-button @click="handleSearch">
                        搜索
                    </el-button>
                </template>
            </el-input>
        </div>

        <el-table :data="paymentIcons" style="width: 100%">
            <el-table-column prop="name" label="支付名称"></el-table-column>
            <el-table-column prop="icon" label="图标">
                <template #default="scope">
                    <img :src="scope.row.icon" alt="图标" style="width: 30px; height: 30px;">
                </template>
            </el-table-column>
            <el-table-column label="操作">
                <template #default="scope">
                    <el-button type="text" @click="editIcon(scope.row)">编辑</el-button>
                    <el-button type="text" @click="deleteIcon(scope.row.id)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <div style="margin-top: 20px; text-align: right;">
            <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[10, 20, 30, 50]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>

        <el-button type="primary" @click="showInsertDialog" style="margin-top: 20px;">添加支付图标</el-button>
    </el-card>

    <el-dialog v-model="dialogVisible" :title="form.id ? '编辑支付图标' : '添加支付图标'">
        <el-form :model="form">
            <el-form-item label="支付名称">
                <el-input v-model="form.name"></el-input>
            </el-form-item>
            <el-form-item label="上传图标">
                <div v-if="form.icon" class="current-icon" style="margin-bottom: 10px;">
                    <span>当前图标：</span>
                    <img :src="form.icon" alt="当前图标" style="width: 30px; height: 30px; vertical-align: middle;">
                </div>
                <el-upload
                    class="upload-demo"
                    drag
                    action="/adminApi/Upload/file"
                    name="file"
                    :on-success="handleUploadSuccess"
                    :before-upload="beforeUpload"
                    :show-file-list="false">
                    <i class="el-icon-upload"></i>
                    <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                </el-upload>
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="submitForm">确 定</el-button>
            </span>
        </template>
    </el-dialog>
</div>

<script type="module">
    const { ref, getCurrentInstance } = Vue;

    const app = Vue.createApp({
        setup() {
            const { proxy } = getCurrentInstance();
            const paymentIcons = ref([]);
            const dialogVisible = ref(false);
            const form = ref({ name: '', imageName: '', id: null, icon: '' });
            
            const currentPage = ref(1);
            const pageSize = ref(10);
            const total = ref(0);
            const searchQuery = ref('');

            const fetchPaymentIcons = async () => {
                try {
                    const res = await axios.get("/plugin/Paymenticon/Api/getPaymentIcons", {
                        params: {
                            page: currentPage.value,
                            limit: pageSize.value,
                            search: searchQuery.value
                        }
                    });
                    
                    if (res.data.code === 200) {
                        paymentIcons.value = res.data.data.list;
                        total.value = res.data.data.total;
                    } else {
                        console.error('获取支付图标失败:', res.data.msg);
                    }
                } catch (error) {
                    console.error('获取支付图标失败:', error);
                }
            };

            const handleSearch = () => {
                currentPage.value = 1;
                fetchPaymentIcons();
            };

            const handleCurrentChange = (val) => {
                currentPage.value = val;
                fetchPaymentIcons();
            };

            const handleSizeChange = (val) => {
                pageSize.value = val;
                currentPage.value = 1;
                fetchPaymentIcons();
            };

            const showInsertDialog = () => {
                form.value = { name: '', imageName: '', id: null, icon: '' };
                dialogVisible.value = true;
            };

            const handleUploadSuccess = (response, file) => {
                if (response.code === 200) {
                    form.value.icon = response.data.url;
                    proxy.$message.success('上传成功');
                } else {
                    proxy.$message.error(response.msg || '上传失败');
                }
            };

            const beforeUpload = (file) => {
                const isImage = file.type.startsWith('image/');
                if (!isImage) {
                    proxy.$message.error('上传文件必须是图片!');
                }
                return isImage;
            };

            const submitForm = async () => {
                const isEdit = form.value.id !== null;
                const url = isEdit ? "/plugin/Paymenticon/Api/editPaymentIcon" : "/plugin/Paymenticon/Api/insertPaymentIcon";
                const data = {
                    id: form.value.id,
                    name: form.value.name,
                    icon: form.value.icon
                };

                try {
                    const res = await axios.post(url, data);
                    if (res.data.code === 200) {
                        dialogVisible.value = false;
                        await fetchPaymentIcons();
                        proxy.$message.success(res.data.msg);
                    } else {
                        proxy.$message.error(res.data.msg);
                    }
                } catch (error) {
                    proxy.$message.error('操作失败：' + error.message);
                }
            };

            const editIcon = (icon) => {
                form.value = {
                    id: icon.id,
                    name: icon.name,
                    imageName: icon.icon.split('/').pop(),
                    icon: icon.icon
                };
                dialogVisible.value = true;
            };

            const deleteIcon = async (id) => {
                try {
                    const res = await axios.post("/plugin/Paymenticon/Api/deletePaymentIcon", { id });
                    if (res.data.code === 200) {
                        await fetchPaymentIcons();
                        proxy.$message.success(res.data.msg);
                    } else {
                        proxy.$message.error(res.data.msg);
                    }
                } catch (error) {
                    proxy.$message.error('删除失败：' + error.message);
                }
            };

            fetchPaymentIcons();

            return {
                paymentIcons,
                dialogVisible,
                form,
                currentPage,
                pageSize,
                total,
                searchQuery,
                showInsertDialog,
                handleUploadSuccess,
                beforeUpload,
                submitForm,
                editIcon,
                deleteIcon,
                handleSearch,
                handleCurrentChange,
                handleSizeChange
            };
        }
    });

    app.use(ElementPlus, {
        locale: ElementPlusLocaleZhCn,
    });
    app.mount("#app");
</script>
</body>
</html> 