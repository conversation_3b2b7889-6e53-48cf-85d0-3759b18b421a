#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
配置管理器
"""

import configparser
import os
from typing import Dict, Any

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.ini"):
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        if os.path.exists(self.config_file):
            try:
                self.config.read(self.config_file, encoding='utf-8')
            except Exception as e:
                print(f"加载配置文件失败: {e}")
                self.create_default_config()
        else:
            self.create_default_config()
    
    def create_default_config(self):
        """创建默认配置"""
        # 基本设置
        self.config['BASIC'] = {
            'domain': 'https://970faka.com',
            'username_length': '8',
            'password_length': '8',
            'username_prefix': '',
            'mobile_prefix': '138',
            'register_count': '10',
            'include_numbers': 'True',
            'include_letters': 'True'
        }
        
        # 注册设置
        self.config['REGISTER'] = {
            'delay_min': '1.0',
            'delay_max': '3.0',
            'max_retries': '3',
            'max_threads': '3',
            'enable_captcha': 'True',
            'captcha_mode': '自动识别',
            'enable_sms': 'False'
        }
        
        # 椰子云API设置
        self.config['YEZI_CLOUD'] = {
            'username': '',
            'password': '',
            'project_id': '',
            'domain': 'http://api.sqhyw.net:90',
            'backup_domain': 'http://api.nnanx.com:90'
        }

        # YesCaptcha商业验证码识别API设置
        self.config['YESCAPTCHA'] = {
            'enable': 'False',
            'client_key': '',
            'priority': 'high'  # high: 优先使用, low: 作为备选
        }
        
        # 导入设置
        self.config['IMPORT'] = {
            'mobile_file': '',
            'invite_code_file': '',
            'last_mobile_dir': '',
            'last_invite_dir': ''
        }
        
        # 界面设置
        self.config['UI'] = {
            'window_width': '1200',
            'window_height': '800',
            'window_x': '100',
            'window_y': '100',
            'theme': 'default'
        }
        
        self.save_config()
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                self.config.write(f)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def get(self, section: str, key: str, default: Any = None) -> str:
        """获取配置值"""
        try:
            return self.config.get(section, key)
        except (configparser.NoSectionError, configparser.NoOptionError):
            return str(default) if default is not None else ""
    
    def get_int(self, section: str, key: str, default: int = 0) -> int:
        """获取整数配置值"""
        try:
            return self.config.getint(section, key)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            return default
    
    def get_float(self, section: str, key: str, default: float = 0.0) -> float:
        """获取浮点数配置值"""
        try:
            return self.config.getfloat(section, key)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            return default
    
    def get_bool(self, section: str, key: str, default: bool = False) -> bool:
        """获取布尔配置值"""
        try:
            return self.config.getboolean(section, key)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            return default
    
    def set(self, section: str, key: str, value: Any):
        """设置配置值"""
        if not self.config.has_section(section):
            self.config.add_section(section)
        self.config.set(section, key, str(value))
    
    def get_section(self, section: str) -> Dict[str, str]:
        """获取整个配置节"""
        try:
            return dict(self.config.items(section))
        except configparser.NoSectionError:
            return {}
    
    def set_section(self, section: str, values: Dict[str, Any]):
        """设置整个配置节"""
        if not self.config.has_section(section):
            self.config.add_section(section)
        
        for key, value in values.items():
            self.config.set(section, key, str(value))
    
    def remove_section(self, section: str):
        """删除配置节"""
        if self.config.has_section(section):
            self.config.remove_section(section)
    
    def remove_option(self, section: str, key: str):
        """删除配置项"""
        if self.config.has_section(section):
            self.config.remove_option(section, key)
    
    def has_section(self, section: str) -> bool:
        """检查配置节是否存在"""
        return self.config.has_section(section)
    
    def has_option(self, section: str, key: str) -> bool:
        """检查配置项是否存在"""
        return self.config.has_option(section, key)
    
    def get_all_sections(self) -> list:
        """获取所有配置节名称"""
        return self.config.sections()
    
    def backup_config(self, backup_file: str = None):
        """备份配置文件"""
        if backup_file is None:
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = f"config_backup_{timestamp}.ini"
        
        try:
            import shutil
            shutil.copy2(self.config_file, backup_file)
            return True
        except Exception as e:
            print(f"备份配置文件失败: {e}")
            return False
    
    def restore_config(self, backup_file: str):
        """恢复配置文件"""
        try:
            import shutil
            shutil.copy2(backup_file, self.config_file)
            self.load_config()
            return True
        except Exception as e:
            print(f"恢复配置文件失败: {e}")
            return False
    
    def export_config(self, export_file: str):
        """导出配置到指定文件"""
        try:
            with open(export_file, 'w', encoding='utf-8') as f:
                self.config.write(f)
            return True
        except Exception as e:
            print(f"导出配置失败: {e}")
            return False
    
    def import_config(self, import_file: str):
        """从指定文件导入配置"""
        try:
            temp_config = configparser.ConfigParser()
            temp_config.read(import_file, encoding='utf-8')
            
            # 合并配置
            for section in temp_config.sections():
                if not self.config.has_section(section):
                    self.config.add_section(section)
                
                for key, value in temp_config.items(section):
                    self.config.set(section, key, value)
            
            self.save_config()
            return True
        except Exception as e:
            print(f"导入配置失败: {e}")
            return False

    def get_yezi_cloud_config(self) -> Dict[str, str]:
        """获取椰子云配置"""
        return self.get_section('YEZI_CLOUD')

    def is_yezi_cloud_configured(self) -> bool:
        """检查椰子云是否已配置"""
        config = self.get_yezi_cloud_config()
        return bool(config.get('username') and config.get('password') and config.get('project_id'))

    def update_yezi_cloud_config(self, username: str, password: str, project_id: str):
        """更新椰子云配置"""
        self.set('YEZI_CLOUD', 'username', username)
        self.set('YEZI_CLOUD', 'password', password)
        self.set('YEZI_CLOUD', 'project_id', project_id)
        self.save_config()

    def get_selenium_config(self) -> Dict[str, Any]:
        """获取Selenium配置"""
        return {
            'headless': self.get_bool('SELENIUM', 'headless', False),
            'window_width': self.get_int('SELENIUM', 'window_width', 1920),
            'window_height': self.get_int('SELENIUM', 'window_height', 1080),
            'wait_timeout': self.get_int('SELENIUM', 'wait_timeout', 20),
            'sms_wait_timeout': self.get_int('SELENIUM', 'sms_wait_timeout', 60),
            'auto_captcha': self.get_bool('SELENIUM', 'auto_captcha', True)
        }

    def get_register_config_enhanced(self) -> Dict[str, Any]:
        """获取增强的注册配置"""
        return {
            'delay_min': self.get_float('REGISTER', 'delay_min', 1.0),
            'delay_max': self.get_float('REGISTER', 'delay_max', 3.0),
            'max_retries': self.get_int('REGISTER', 'max_retries', 3),
            'max_threads': self.get_int('REGISTER', 'max_threads', 3),
            'enable_captcha': self.get_bool('REGISTER', 'enable_captcha', True),
            'captcha_mode': self.get('REGISTER', 'captcha_mode', 'Selenium自动化'),
            'enable_sms': self.get_bool('REGISTER', 'enable_sms', True)
        }

# 全局配置管理器实例
config_manager = ConfigManager()

def get_config() -> ConfigManager:
    """获取配置管理器实例"""
    return config_manager
