<?php
namespace plugin\Customersystem\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;
use think\facade\Log;
use think\facade\Session;
use think\facade\Cache;
use plugin\Customersystem\Hook;
use app\common\service\EmailService;

class Api extends BasePlugin
{
    protected $scene = [
        'admin',
    ];
    protected $noNeedLogin = ['chat', 'sendMessage', 'upload', 'getParams', 'createSession', 'showSuccess', 'queryHistory', 'captcha', 'getRealtimeMessages', 'getMerchantList', 'getMerchantByToken', 'getSessionsByContactId', 'toggleQiantaiJs', 'sendMerchantNotificationEmail', 'getCurrentIP', 'queryIPLocation', 'getSessionInfo'];
    protected $adminInfo = null;

    /**
     * 初始化方法
     */
    protected function initialize()
    {
        parent::initialize();
        
        // 初始化管理员信息
        if (Session::has('admin')) {
            $this->adminInfo = Session::get('admin');
        }
    }

    /**
     * 管理后台主页
     */
    public function index()
    {
        // 检查并确保客服导航菜单存在
        $this->ensureChatNavExists();

        return View::fetch();
    }

    /**
     * 聊天页面
     */
    public function chatPage()
    {
        return View::fetch('chat');
    }

    /**
     * 系统设置页面
     */
    public function settings()
    {
        try {
            // 获取当前参数设置
            $params = $this->getParamsData();
            
            // 传递参数到模板
            View::assign([
                'params' => json_encode($params, JSON_UNESCAPED_UNICODE),
            ]);
            
            return View::fetch('chat/settings');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 确保客服导航菜单存在
     */
    private function ensureChatNavExists()
    {
        try {
            $nav = Db::name('nav')
                ->where('href', '/plugin/Customersystem/api/chat')
                ->find();
            
            if (empty($nav)) {
                // 添加到导航
                Db::name('nav')->insert([
                    'pid' => 0,
                    'name' => '在线客服',
                    'href' => '/plugin/Customersystem/api/chat',
                    'target' => '_self',
                    'sort' => 99,
                    'status' => 1,
                    'create_time' => time(),
                    'update_time' => time(),
                ]);
            } else if ($nav['status'] == 0) {
                // 确保导航是启用状态
                Db::name('nav')
                    ->where('id', $nav['id'])
                    ->update([
                        'status' => 1,
                        'update_time' => time()
                    ]);
            }
        } catch (\Exception $e) {
            // 记录错误但继续执行
            error_log("确保客服导航菜单存在时出错: " . $e->getMessage());
        }
    }

    /**
     * 客服聊天入口页面
     */
    public function chat()
    {
        try {
            // 检查访问权限
            if (!$this->checkPageAccess('/plugin/Customersystem/api/chat')) {
                // 获取网站配置用于美化关闭页面
                $logo = Db::name('system_config')
                    ->where('type', 'website')
                    ->where('name', 'logo')
                    ->value('value');
                
                // 处理logo URL
                if (preg_match('/^https?:\/\/(oss\.|cos\.|[-\w]+\.qiniucdn\.|[-\w]+)/i', $logo)) {
                    $logo = $logo;
                } else if (strpos($logo, 'uploads/') === 0) {
                    $logo = request()->domain() . '/' . $logo;
                } else {
                    $logo = request()->domain() . '/' . ltrim($logo, '/');
                }
                
                $siteName = Db::name('system_config')
                    ->where('type', 'website')
                    ->where('name', 'app_name')
                    ->value('value');
                
                $favicon = Db::name('system_config')
                    ->where('type', 'website')
                    ->where('name', 'favicon')
                    ->value('value');
                
                // 处理 favicon URL
                if (preg_match('/^https?:\/\/(oss\.|cos\.|[-\w]+\.qiniucdn\.|[-\w]+)/i', $favicon)) {
                    $favicon = $favicon;
                } else if (strpos($favicon, 'uploads/') === 0) {
                    $favicon = request()->domain() . '/' . $favicon;
                } else {
                    $favicon = request()->domain() . '/' . ltrim($favicon, '/');
                }
                
                // 获取网站配置信息
                $icpNumber = Db::name('system_config')
                    ->where('type', 'website')
                    ->where('name', 'icp_number')
                    ->value('value');
                
                $gaNumber = Db::name('system_config')
                    ->where('type', 'website')
                    ->where('name', 'ga_number')
                    ->value('value');
                
                // 获取domain值
                $domain = Db::name('system_config')
                    ->where('type', 'domain')
                    ->where('name', 'shop_domain')
                    ->value('value');
                
                // 获取导航菜单数据，包含子菜单信息
                $navItems = Db::name('nav')
                    ->where('status', 1)
                    ->where('pid', 0)
                    ->field(['id', 'name', 'href', 'target', 'sort'])
                    ->order('sort desc')
                    ->select()
                    ->each(function($item) {
                        // 获取子菜单
                        $item['subMenus'] = Db::name('nav')
                            ->where('status', 1)
                            ->where('pid', $item['id'])
                            ->field(['id', 'name', 'href', 'target', 'sort'])
                            ->order('sort desc')
                            ->select()
                            ->toArray();
                        
                        // 判断当前页面是否为该菜单或其子菜单
                        $currentUrl = request()->url(true);
                        $item['active'] = ($currentUrl == $item['href']) 
                            || (strpos($currentUrl, $item['href']) === 0 && $item['href'] != '/');
                        
                        // 检查子菜单是否包含当前页
                        if (!$item['active'] && !empty($item['subMenus'])) {
                            foreach ($item['subMenus'] as $subItem) {
                                if ($currentUrl == $subItem['href'] 
                                    || (strpos($currentUrl, $subItem['href']) === 0 && $subItem['href'] != '/')) {
                                    $item['active'] = true;
                                    break;
                                }
                            }
                        }
                        
                        return $item;
                    })
                    ->toArray();
                
                // 传递数据到视图
                View::assign([
                    'logo' => $logo,
                    'siteName' => $siteName,
                    'favicon' => $favicon,
                    'icpNumber' => $icpNumber,
                    'gaNumber' => $gaNumber,
                    'domain' => $domain,
                    'navItems' => $navItems,
                ]);
                
                // 返回美化的功能关闭页面
                return View::fetch('chat/closed');
            }
            
            // 获取网站配置
            $logo = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'logo')
                ->value('value');
            
            // 处理logo URL
            if (preg_match('/^https?:\/\/(oss\.|cos\.|[-\w]+\.qiniucdn\.|[-\w]+)/i', $logo)) {
                $logo = $logo;
            } else if (strpos($logo, 'uploads/') === 0) {
                $logo = request()->domain() . '/' . $logo;
            } else {
                $logo = request()->domain() . '/' . ltrim($logo, '/');
            }
            
            $siteName = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'app_name')
                ->value('value');
            
            $favicon = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'favicon')
                ->value('value');

            $domain = Db::name('system_config')
                ->where('type', 'domain')
                ->where('name', 'shop_domain')
                ->value('value');
            
            // 处理 favicon URL
            if (preg_match('/^https?:\/\/(oss\.|cos\.|[-\w]+\.qiniucdn\.|[-\w]+)/i', $favicon)) {
                $favicon = $favicon;
            } else if (strpos($favicon, 'uploads/') === 0) {
                $favicon = request()->domain() . '/' . $favicon;
            } else {
                $favicon = request()->domain() . '/' . ltrim($favicon, '/');
            }
            
            // 获取导航菜单数据，包含子菜单信息
            $navItems = Db::name('nav')
                ->where('status', 1)
                ->where('pid', 0)
                ->field(['id', 'name', 'href', 'target', 'sort'])
                ->order('sort desc')
                ->select()
                ->each(function($item) {
                    // 获取子菜单
                    $item['subMenus'] = Db::name('nav')
                        ->where('status', 1)
                        ->where('pid', $item['id'])
                        ->field(['id', 'name', 'href', 'target', 'sort'])
                        ->order('sort desc')
                        ->select()
                        ->toArray();
                    
                    // 判断当前页面是否为该菜单或其子菜单
                    $currentUrl = request()->url(true);
                    $item['active'] = ($currentUrl == $item['href']) 
                        || (strpos($currentUrl, $item['href']) === 0 && $item['href'] != '/');
                    
                    // 检查子菜单是否包含当前页
                    if (!$item['active'] && !empty($item['subMenus'])) {
                        foreach ($item['subMenus'] as $subItem) {
                            if ($currentUrl == $subItem['href'] 
                                || (strpos($currentUrl, $subItem['href']) === 0 && $subItem['href'] != '/')) {
                                $item['active'] = true;
                                break;
                            }
                        }
                    }
                    
                    return $item;
                })
                ->toArray();
                
            // 获取网站配置信息
            $icpNumber = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'icp_number')
                ->value('value');
            
            $gaNumber = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'ga_number')
                ->value('value');
            
            $icpCert = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'icp_cert')
                ->value('value');

            // 获取前台JS脚本的启用状态
            $params = $this->getParamsData();
            $qiantaiJsEnabled = false;
            
            if (isset($params['settings']['qiantai_js_enabled'])) {
                $qiantaiJsEnabled = (bool)$params['settings']['qiantai_js_enabled'];
            }

            View::assign([
                'logo' => $logo,
                'siteName' => $siteName,
                'favicon' => $favicon,
                'navItems' => $navItems,
                'icpNumber' => $icpNumber,
                'gaNumber' => $gaNumber,
                'icpCert' => $icpCert,
                'qiantaiJsEnabled' => $qiantaiJsEnabled, // 传递JS启用状态到模板
                'domain' => $domain,
            ]);
            
            return View::fetch('chat/index');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 检查页面访问权限
     */
    private function checkPageAccess($path)
    {
        try {
            // 首先检查参数配置中客服功能是否启用
            $params = $this->getParamsData();
            if (isset($params['settings']['chat_enabled']) && $params['settings']['chat_enabled'] === false) {
                return false; // 如果配置中明确禁用了聊天功能，则不允许访问
            }
            
            // 如果配置中启用了聊天功能，则允许访问，无需检查导航菜单
            if (isset($params['settings']['chat_enabled']) && $params['settings']['chat_enabled'] === true) {
                return true;
            }
            
            // 检查是否存在对应的菜单(包括顶级菜单和子菜单)
            $menu = Db::name('nav')
                ->where('href', $path)
                ->where('status', 1)  // 确保菜单是启用状态
                ->find();
            
            // 如果找到启用的菜单项就允许访问
            return !empty($menu);
            
        } catch (\Exception $e) {
            // 记录错误日志但不暴露给用户
            error_log("检查页面访问权限出错: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 显示成功页面
     */
    public function showSuccess()
    {
        // 获取网站配置
        $logo = Db::name('system_config')
            ->where('type', 'website')
            ->where('name', 'logo')
            ->value('value');
        
        // 处理logo URL
        if (preg_match('/^https?:\/\/(oss\.|cos\.|[-\w]+\.qiniucdn\.|[-\w]+)/i', $logo)) {
            $logo = $logo;
        } else if (strpos($logo, 'uploads/') === 0) {
            $logo = request()->domain() . '/' . $logo;
        } else {
            $logo = request()->domain() . '/' . ltrim($logo, '/');
        }
        
        $siteName = Db::name('system_config')
            ->where('type', 'website')
            ->where('name', 'app_name')
            ->value('value');
        
        // 获取网站配置信息
        $icpNumber = Db::name('system_config')
            ->where('type', 'website')
            ->where('name', 'icp_number')
            ->value('value');
        
        $gaNumber = Db::name('system_config')
            ->where('type', 'website')
            ->where('name', 'ga_number')
            ->value('value');
        
        View::assign([
            'logo' => $logo,
            'siteName' => $siteName,
            'icpNumber' => $icpNumber,
            'gaNumber' => $gaNumber,
        ]);
        
        return View::fetch('chat/success');
    }

    /**
     * 获取插件参数
     */
    public function getParams()
    {
        try {
            // 获取参数
            $data = $this->getParamsData();
            
            // 确保settings对象存在
            if (!isset($data['settings']) || !is_array($data['settings'])) {
                $data['settings'] = [];
            }
            
            // 设置联系字段默认值
            if (!isset($data['settings']['contact_field_type'])) {
                $data['settings']['contact_field_type'] = 'phone'; // 默认显示电话输入框
            }
            
            // 确保布尔值被正确处理
            $this->convertBooleanValues($data);
            
            return $this->success('获取参数成功', $data);
        } catch (\Exception $e) {
            return $this->error('获取参数失败: ' . $e->getMessage());
        }
    }

    /**
     * 保存插件参数
     */
    public function saveParams()
    {
        try {
            $post = $this->request->post();
            
            // 验证提交数据
            if (empty($post)) {
                return $this->error('未接收到参数数据');
            }
            
            // 保存参数到params.php文件
            $paramsFile = __DIR__ . '/../params.php';
            
            // 对嵌套的布尔值进行处理
            $this->convertBooleanValues($post);
            
            // 确保settings中的所有开关都是布尔值
            if (isset($post['settings'])) {
                $booleanKeys = [
                    'chat_enabled',
                    'file_upload_enabled',
                    'notification_enabled',
                    'offline_message_enabled'
                ];
                
                foreach ($booleanKeys as $key) {
                    if (isset($post['settings'][$key])) {
                        $post['settings'][$key] = (bool)$post['settings'][$key];
                    }
                }
            }
            
            // 更新参数文件
            file_put_contents($paramsFile, "<?php\n\nreturn " . var_export($post, true) . ";\n");
            
            return $this->success('参数保存成功');
        } catch (\Exception $e) {
            return $this->error('保存参数失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 递归转换数组中的布尔值为整数
     * @param array &$data 要处理的数据
     */
    private function convertBooleanValues(&$data)
    {
        if (!is_array($data)) {
            return;
        }
        
        foreach ($data as $key => &$value) {
            if (is_bool($value)) {
                $value = $value ? true : false;  // 保持布尔值类型
            } else if ($value === 'true' || $value === '1' || $value === 1) {
                $value = true;
            } else if ($value === 'false' || $value === '0' || $value === 0) {
                $value = false;
            } else if (is_array($value)) {
                // 特殊处理某些已知的布尔值字段
                if ($key === 'preset_replies' && isset($value['enabled'])) {
                    $value['enabled'] = (bool)$value['enabled'];
                }
                if ($key === 'faq_config' && isset($value['enabled'])) {
                    $value['enabled'] = (bool)$value['enabled'];
                }
                if ($key === 'quick_replies' && isset($value['enabled'])) {
                    $value['enabled'] = (bool)$value['enabled'];
                }
                
                // 递归处理数组
                $this->convertBooleanValues($value);
            }
        }
    }
    
    /**
     * 创建新的客服会话
     */
    public function createSession()
    {
        if (!$this->request->isPost()) {
            return $this->error('非法请求');
        }

        // 接收参数
        $name = $this->request->post('name', '');
        $email = $this->request->post('email', '');
        $phone = $this->request->post('phone', '');
        $qq = $this->request->post('qq', ''); // 添加QQ字段
        $wechat = $this->request->post('wechat', ''); // 添加微信字段
        $message = $this->request->post('message', '');
        $merchantId = $this->request->post('merchant_id', 0, 'intval');

        // 设置默认发送者类型为'customer'和会话来源
        $source = $merchantId > 0 ? 'merchant' : 'customer';

        try {
            // 验证必须的字段
            if (empty($name)) {
                return $this->error('请填写您的姓名');
            }
            
            if (empty($email)) {
                return $this->error('请填写您的邮箱');
            }
            
            // 获取真实IP地址，兼容CDN情况
            $ip = real_ip();
            
            // 检查是否已达到限制数量 (查询活跃会话数量)
            // 从配置中获取限制数量
            $params = $this->getParamsData();
            $activeSessionLimit = isset($params['settings']['max_active_sessions']) ? 
                intval($params['settings']['max_active_sessions']) : 2; // 默认为2个
            
            // 检查邮箱对应的活跃会话数量
            $emailSessionCount = 0;
            if (!empty($email)) {
                // 首先查找与此邮箱关联的联系人
                $contactsByEmail = Db::name('plugin_chat_contacts')
                    ->where('email', $email)
                    ->select()
                    ->toArray();
                
                if (!empty($contactsByEmail)) {
                    // 获取这些联系人的ID列表
                    $contactIds = array_column($contactsByEmail, 'id');
                    
                    // 查询这些联系人关联的开放会话数量
                    $emailSessionCount = Db::name('plugin_chat_sessions')
                        ->where('contact_id', 'in', $contactIds)
                        ->where('status', 'open')
                        ->count();
                }
            }
            
            // 检查电话对应的活跃会话数量
            $phoneSessionCount = 0;
            if (!empty($phone)) {
                // 首先查找与此电话关联的联系人
                $contactsByPhone = Db::name('plugin_chat_contacts')
                    ->where('phone', $phone)
                    ->select()
                    ->toArray();
                
                if (!empty($contactsByPhone)) {
                    // 获取这些联系人的ID列表
                    $contactIds = array_column($contactsByPhone, 'id');
                    
                    // 查询这些联系人关联的开放会话数量
                    $phoneSessionCount = Db::name('plugin_chat_sessions')
                        ->where('contact_id', 'in', $contactIds)
                        ->where('status', 'open')
                        ->count();
                }
            }
            
            // 取两者中的较大值，作为此用户已有的活跃会话数
            $activeSessionCount = max($emailSessionCount, $phoneSessionCount);
            
            // 如果已达到最大限制，返回错误
            if ($activeSessionCount >= $activeSessionLimit) {
                return $this->error('您已有 ' . $activeSessionCount . ' 个进行中的会话，请先完成或关闭现有会话后再创建新会话');
            }
            
            // 检查是否已存在相同邮箱或电话的联系人
            $existingContact = null;
            
            // 优先根据邮箱查找联系人，不再要求名称匹配
            if (!empty($email)) {
                $existingContact = Db::name('plugin_chat_contacts')
                    ->where('email', $email)
                    ->order('id', 'asc') // 如果有多个，优先使用最早创建的联系人
                    ->find();
                

            }
            
            // 如果邮箱没找到且提供了电话，再根据电话查找
            if (empty($existingContact) && !empty($phone)) {
                $existingContact = Db::name('plugin_chat_contacts')
                    ->where('phone', $phone)
                    ->order('id', 'asc') // 如果有多个，优先使用最早创建的联系人
                    ->find();
                

            }
            
            // 如果找到现有联系人，使用现有ID并更新信息；否则创建新联系人
            if ($existingContact) {
                $contactId = $existingContact['id'];
                
                // 更新联系人信息，保持邮箱不变，可以更新姓名和电话
                $updateData = [
                    'update_time' => time()
                ];
                
                // 如果名称不同且不为空，更新名称
                if (!empty($name) && $existingContact['name'] != $name) {
                    $updateData['name'] = $name;
                }
                
                // 如果电话不同且不为空，更新电话
                if (!empty($phone) && $existingContact['phone'] != $phone) {
                    $updateData['phone'] = $phone;
                }
                
                // 如果QQ不同且不为空，更新QQ
                if (!empty($qq) && $existingContact['qq'] != $qq) {
                    $updateData['qq'] = $qq;
                }
                
                // 如果微信不同且不为空，更新微信
                if (!empty($wechat) && $existingContact['wechat'] != $wechat) {
                    $updateData['wechat'] = $wechat;
                }
                
                // 执行更新
                if (count($updateData) > 1) { // 确保有需要更新的字段
                    Db::name('plugin_chat_contacts')->where('id', $contactId)->update($updateData);
                }
                

            } else {
                // 创建新联系人记录
                $contactId = Db::name('plugin_chat_contacts')->insertGetId([
                    'name' => $name,
                    'email' => $email,
                    'phone' => $phone ?? '',
                    'qq' => $qq ?? '',      // 添加QQ字段
                    'wechat' => $wechat ?? '', // 添加微信字段
                    'ip' => $ip,            // 添加IP地址
                    'create_time' => time(),
                    'update_time' => time(),
                ]);
                

            }
            
            // 判断是与商家聊天还是与客服聊天
            $merchant_id = isset($merchantId) ? intval($merchantId) : 0;
            $source = $merchant_id > 0 ? 'merchant' : 'customer';
            
            // 如果是与商家聊天，检查商家是否存在
            if ($merchant_id > 0) {
                $merchant = Db::name('user')->where('id', $merchant_id)->find();
                if (empty($merchant)) {
                    return $this->error('商家不存在');
                }
                
                // 生成标题，加入问题内容摘要
                $messageExcerpt = !empty($message) ? ('【' . mb_substr($message, 0, 10, 'UTF-8') . (mb_strlen($message, 'UTF-8') > 10 ? '...' : '') . '】') : '';
                $merchant_name = $merchant_id;
                // 通过商家ID查询user表获取nickname
                if (!empty($merchant_id)) {
                    $merchant_info = \think\facade\Db::name('user')->where('id', $merchant_id)->field('nickname')->find();
                    if ($merchant_info && !empty($merchant_info['nickname'])) {
                        $merchant_name = $merchant_info['nickname'];
                    }
                }
                $title = '客户咨询: ' . $name . ' ' . $messageExcerpt . ' (联系商家:' . $merchant_name . ')';
            } else {
                // 生成标题，加入问题内容摘要
                $messageExcerpt = !empty($message) ? ('【' . mb_substr($message, 0, 10, 'UTF-8') . (mb_strlen($message, 'UTF-8') > 10 ? '...' : '') . '】') : '';
                $title = '客户咨询: ' . $name . ' ' . $messageExcerpt;
            }
            
            // 创建会话记录
            $sessionId = Db::name('plugin_chat_sessions')->insertGetId([
                'contact_id' => $contactId,
                'title' => $title,
                'status' => 'open',
                'source' => $source,
                'merchant_id' => $merchant_id,
                'last_time' => time(),
                'create_time' => time(),
                'update_time' => time(),
            ]);
            

            
            // 如果有初始消息，创建第一条消息
            if (!empty($message)) {
                Db::name('plugin_chat_messages')->insert([
                    'session_id' => $sessionId,
                    'sender_type' => 'customer',
                    'sender_id' => $contactId,
                    'message' => $message,
                    'message_type' => 'text',
                    'is_read' => 0,
                    'create_time' => time(),
                    'update_time' => time(),
                ]);
                
                // 更新会话的最后消息
                Db::name('plugin_chat_sessions')->where('id', $sessionId)->update([
                    'last_message' => $message,
                    'last_time' => time(),
                    'unread_count' => 1,
                    'update_time' => time(),
                ]);
            }
            
            // 获取参数配置
            $params = $this->getParamsData();
            
            // 检查是否启用了自动欢迎消息
            $autoResponseEnabled = isset($params['auto_response']['enabled']) ? (bool)$params['auto_response']['enabled'] : true;
            
            // 只有在启用自动欢迎消息时才发送
            if ($autoResponseEnabled) {
                // 获取欢迎消息，按优先级检查多个可能的字段
                $welcomeMessage = '您好，欢迎咨询客服，请问有什么可以帮助您？'; // 默认欢迎语
                
                // 检查settings中的chat_description字段(前端设置界面使用的字段)
                if (isset($params['chat_description']) && !empty($params['chat_description'])) {
                    $welcomeMessage = $params['chat_description'];
                }
                // 检查auto_response.welcome_message字段(原代码使用的字段)
                else if (isset($params['auto_response']['welcome_message']) && !empty($params['auto_response']['welcome_message'])) {
                    $welcomeMessage = $params['auto_response']['welcome_message'];
                }
                    
                // 添加客服的自动欢迎消息
                Db::name('plugin_chat_messages')->insert([
                    'session_id' => $sessionId,
                    'sender_type' => 'staff',
                    'sender_id' => 0, // 0表示系统/自动消息
                    'message' => $welcomeMessage,
                    'message_type' => 'text',
                    'is_read' => 1,
                    'create_time' => time(),
                    'update_time' => time(),
                ]);
            }

            // 根据会话来源(source)决定发送哪种预设问题
            if ($source == 'customer') {
                // 客服会话: 发送平台客服预设问题
                if (isset($params['preset_replies']) && $params['preset_replies']['enabled'] && !empty($params['preset_replies']['items'])) {
                    // 获取自定义标题和描述
                    $presetTitle = isset($params['preset_questions']['title']) && !empty($params['preset_questions']['title']) 
                        ? $params['preset_questions']['title'] 
                        : '常见问题';
                    
                    $presetDesc = isset($params['preset_questions']['description']) && !empty($params['preset_questions']['description']) 
                        ? $params['preset_questions']['description'] 
                        : '以下是用户经常咨询的问题，点击即可直接提问：';
                    
                    // 构建标题和描述HTML
                    $titleHtml = "<h4 style='margin-bottom: 10px; font-size: 16px; color: #333; font-weight: 600;'>{$presetTitle}</h4>";
                    $descHtml = "<p style='margin-bottom: 12px; font-size: 14px; color: #64748b;'>{$presetDesc}</p>";
                    
                    // 构建预设问题列表，添加可点击的样式
                    $questionList = '';
                    foreach ($params['preset_replies']['items'] as $index => $item) {
                        $number = $index + 1;
                        $questionList .= "<div class='preset-question-item' style='cursor:pointer;margin:5px 0;padding:8px 12px;background:#fff;border-radius:6px;border:1px solid #e2e8f0;transition:all 0.2s ease;'>{$number}. {$item['label']}</div>";
                    }

                    // 发送预设问题列表，包含标题和描述
                    Db::name('plugin_chat_messages')->insert([
                        'session_id' => $sessionId,
                        'sender_type' => 'staff',
                        'sender_id' => 0,
                        'message' => '<div class="preset-questions">' . $titleHtml . $descHtml . $questionList . '</div>',
                        'message_type' => 'text',
                        'role_type' => 'staff',  // 添加角色类型
                        'is_read' => 0,
                        'create_time' => time() + 2,
                        'update_time' => time() + 2,
                    ]);

                    // 更新会话的最后消息为预设问题列表
                    Db::name('plugin_chat_sessions')
                        ->where('id', $sessionId)
                        ->update([
                            'last_message' => '平台预设问题',
                            'last_time' => time() + 2,
                            'update_time' => time() + 2,
                            'unread_count' => Db::raw('unread_count + 2')  // 增加2条未读消息
                        ]);
                }
            } else if ($source == 'merchant' && $merchant_id > 0) {
                // 商家会话: 发送商家预设问题
                // 获取商家的预设问题设置
                $merchantSettings = Db::name('plugin_chat_settings')
                    ->where('user_id', $merchant_id)
                    ->find();
                    
                if (!empty($merchantSettings) && !empty($merchantSettings['settings'])) {
                    $settingsData = json_decode($merchantSettings['settings'], true) ?: [];
                    
                    // 检查是否开启了自动发送预设问题
                    $autoSendEnabled = isset($settingsData['autoSendPresetQuestions']) ? 
                        (bool)$settingsData['autoSendPresetQuestions'] : true;
                    
                    if ($autoSendEnabled && !empty($settingsData['presetQuestions'])) {
                        $presetQuestions = $settingsData['presetQuestions'];
                        
                        // 获取标题和描述
                        $title = $presetQuestions['title'] ?? 'Spikees云寄售';
                        $description = $presetQuestions['description'] ?? '有什么问题您下方留言，如果下面有您想问的问题点击即可解答！';
                        $questions = $presetQuestions['questions'] ?? [
                            '1. 平台安全吗',
                            '2. 如何注册商家？',
                            '3. 投诉订单什么时候到账？'
                        ];
                        
                        // 兼容新格式数据 - 如果questions是对象数组
                        if (!empty($questions) && is_array($questions) && isset($questions[0]['question'])) {
                            $formattedQuestions = [];
                            foreach ($questions as $item) {
                                $question = $item['question'] ?? '';
                                $answer = $item['answer'] ?? '';
                                $formattedQuestions[] = $question;
                            }
                            $questions = $formattedQuestions;
                        }
                        
                        // 构建预设问题HTML
                        $titleHtml = '<h4 style="margin-bottom: 10px; font-size: 16px; color: #333; font-weight: 600;">' . htmlspecialchars($title) . '</h4>';
                        $descHtml = '<p style="margin-bottom: 12px; font-size: 14px; color: #64748b;">' . nl2br(htmlspecialchars($description)) . '</p>';
                        
                        $questionList = '';
                        foreach ($questions as $index => $question) {
                            // 如果使用新格式，获取问题和答案
                            $questionText = is_array($question) ? ($question['question'] ?? '') : $question;
                            $answer = is_array($question) ? ($question['answer'] ?? '') : '';
                            
                            $dataAnswer = !empty($answer) ? ' data-answer="' . htmlspecialchars($answer) . '"' : '';
                            $questionList .= '<div class="preset-question-item"' . $dataAnswer . ' style="cursor:pointer;margin:5px 0;padding:8px 12px;background:#fff;border-radius:6px;border:1px solid #e2e8f0;transition:all 0.2s ease;">' . htmlspecialchars($questionText) . '</div>';
                        }
                        
                        // 发送预设问题列表，包含标题和描述
                        Db::name('plugin_chat_messages')->insert([
                            'session_id' => $sessionId,
                            'sender_type' => 'staff', // 修改为'staff'，避免数据截断错误
                            'sender_id' => $merchant_id, // 使用商家ID
                            'message' => '<div class="preset-questions">' . $titleHtml . $descHtml . $questionList . '</div>',
                            'message_type' => 'text',
                            'role_type' => 'merchant',  // 角色类型为商家
                            'is_read' => 0,
                            'create_time' => time() + 3, // 确保在客服消息之后显示
                            'update_time' => time() + 3,
                        ]);
                        
                        // 更新会话的最后消息为商家预设问题列表
                        Db::name('plugin_chat_sessions')
                            ->where('id', $sessionId)
                            ->update([
                                'last_message' => '商家预设问题',
                                'last_time' => time() + 3,
                                'update_time' => time() + 3,
                                'unread_count' => Db::raw('unread_count + 1')  // 增加1条未读消息
                            ]);
                    }
                }
            }

            // 根据咨询类型发送邮件通知
            $this->sendNotificationByType($source, $merchant_id, $name, $email, $message, $sessionId);

            // 返回会话信息
            $sessionData = [
                'session_id' => $sessionId,
                'contact_id' => $contactId,
                'messages' => Db::name('plugin_chat_messages')
                    ->where('session_id', $sessionId)
                    ->order('create_time asc')
                    ->select()
                    ->toArray()
            ];

            return $this->success('会话创建成功', $sessionData);
        } catch (\Exception $e) {
            return $this->error('创建会话失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取实时消息
     * 
     * @return \think\Response
     */
    public function getRealtimeMessages()
    {
        $sessionId = input('session_id', 0, 'intval');
        $lastMessageId = input('last_message_id', 0, 'intval');
        $checkSessionStatus = input('check_session_status', false, 'boolval');
        $checkRecalled = input('check_recalled', false, 'boolval');
        $checkReadStatus = input('check_read_status', false, 'boolval');
        $senderType = input('sender_type', '', 'trim'); // 发送者类型
        $messageIds = input('message_ids', [], 'json_decode'); // 需要查询已读状态的消息ID列表
        
        if (empty($sessionId)) {
            return $this->error('会话ID不能为空');
        }
        
        try {
            // 获取会话信息
            $session = Db::name('plugin_chat_sessions')->where('id', $sessionId)->find();
            if (!$session) {
                return $this->error('会话不存在');
            }
            
            // 构建基本返回数据
            $returnData = [
                'messages' => [],
                'session_status' => $session['status'],
                'is_closed' => $session['status'] === 'closed'
            ];
            
            // 如果提供了特定的消息ID列表，则查询这些消息的状态
            if ($checkReadStatus && !empty($messageIds) && is_array($messageIds)) {
                // 查询指定消息ID的消息
                $readStatusMessages = Db::name('plugin_chat_messages')
                    ->whereIn('id', $messageIds)
                    ->where('session_id', $sessionId)
                    ->select();
                
                if (!empty($readStatusMessages)) {
                    $returnData['read_status_changed'] = $readStatusMessages;
                }
            } 
            // 否则获取新消息
            else {
                // 获取新消息
                $newMessages = Db::name('plugin_chat_messages')
                    ->where('session_id', $sessionId)
                    ->where('id', '>', $lastMessageId)
                    ->order('id', 'asc')
                    ->select();
                
                $returnData['messages'] = $newMessages;
                
                // 如果需要检查已读状态变更，返回所有消息的最新已读状态
                if ($checkReadStatus) {
                    // 如果提供了特定的消息ID列表，只查询这些消息
                    if (!empty($messageIds)) {
                        $readStatusChangedMessages = Db::name('plugin_chat_messages')
                            ->where('session_id', $sessionId)
                            ->where('id', 'in', $messageIds)
                            ->select();
                    } else {
                        // 否则返回会话中所有消息的最新状态
                        $readStatusChangedMessages = Db::name('plugin_chat_messages')
                            ->where('session_id', $sessionId)
                            ->select();
                    }

                    if (!empty($readStatusChangedMessages)) {
                        $returnData['read_status_changed'] = $readStatusChangedMessages;
                    }
                }
                
                // 如果需要检查消息撤回状态
                if ($checkRecalled) {
                    // 获取被撤回的消息
                    $recalledMessages = Db::name('plugin_chat_messages')
                        ->where('session_id', $sessionId)
                        ->where('is_recalled', 1)
                        ->select();
                    
                    if (!empty($recalledMessages)) {
                        $returnData['recalled_messages'] = $recalledMessages;
                    }
                }
                
                // 如果当前是客户端，自动更新IP地址并标记消息为已读
                if ($senderType === 'customer') {
                    // 自动更新客户IP地址
                    $this->updateCustomerIP($session['contact_id']);

                    // 如果有新消息，标记这些消息为已读
                    if (!empty($newMessages)) {
                        $otherMessages = [];
                        foreach ($newMessages as $message) {
                            if ($message['sender_type'] !== 'customer' && $message['role_type'] !== 'customer') {
                                $otherMessages[] = $message['id'];
                            }
                        }

                        // 如果有非客户发送的消息，标记为已读
                        if (!empty($otherMessages)) {
                            foreach ($otherMessages as $msgId) {
                                // 标记消息已读
                                Db::name('plugin_chat_messages')
                                    ->where('id', $msgId)
                                    ->where('session_id', $sessionId)
                                    ->update([
                                        'customer_read' => 1,
                                        'customer_read_time' => time(),
                                        'update_time' => time()
                                    ]);
                            }

                            // 更新会话的未读消息计数
                            $this->updateSessionUnreadCountForRole($sessionId, 'customer');
                        }
                    }
                }
            }
            
            return $this->success('获取消息成功', $returnData);
        } catch (\Exception $e) {
            Log::error("获取消息失败: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return $this->error('获取消息失败: ' . $e->getMessage());
        }
    }

    /**
     * 发送消息
     */
    public function sendMessage()
    {
        try {
            $sessionId = $this->request->param('session_id/d', 0);
            $message = $this->request->param('message/s', '');
            $senderType = $this->request->param('sender_type/s', 'customer');
            $messageType = $this->request->param('message_type/s', 'text');
            $fileUrl = $this->request->param('file_url/s', '');
            $senderId = $this->request->param('sender_id/d', 0);
            
            // 简单验证
            if (empty($sessionId)) {
                return $this->error('会话ID不能为空');
            }
            
            if (empty($message) && empty($fileUrl)) {
                return $this->error('消息内容不能为空');
            }
            
            // 检查会话是否存在
            $session = Db::name('plugin_chat_sessions')
                ->where('id', $sessionId)
                ->find();

            if (empty($session)) {
                return $this->error('会话不存在');
            }

            // 如果会话已关闭且发送者是客户，则返回错误信息
            if ($session['status'] === 'closed' && $senderType === 'customer') {
                return $this->error('会话已关闭，无法发送消息');
            }

            // 如果是客户发送消息，自动获取并更新最新IP地址
            if ($senderType === 'customer') {
                $this->updateCustomerIP($session['contact_id']);
            }
            
            // 如果是客户发送的消息，检查是否匹配预设问题
            if ($senderType === 'customer' && $messageType === 'text') {
                // 获取参数配置
                $params = $this->getParamsData();
                
                // 检查是否启用了预设回复
                if (isset($params['preset_replies']) && $params['preset_replies']['enabled'] && !empty($params['preset_replies']['items'])) {
                    $matchIndex = null;
                    
                    // 1. 尝试匹配纯数字（预设问题编号）
                    if (preg_match('/^[1-9]\d*$/', trim($message))) {
                        $matchIndex = intval(trim($message)) - 1;
                    } 
                    // 2. 尝试匹配格式为"数字. 文本"的完整问题
                    else if (preg_match('/^([1-9]\d*)\.\s+(.+)$/', trim($message), $matches)) {
                        $matchIndex = intval($matches[1]) - 1;
                    }
                    
                    // 如果找到匹配的索引，处理预设回复
                    if ($matchIndex !== null && isset($params['preset_replies']['items'][$matchIndex])) {
                        // 找到匹配的预设回复
                        $preset = $params['preset_replies']['items'][$matchIndex];
                        
                        // 先发送客户的选择
                        $messageId = Db::name('plugin_chat_messages')->insertGetId([
                            'session_id' => $sessionId,
                            'sender_type' => $senderType,
                            'sender_id' => $session['contact_id'],
                            'message' => $matchIndex + 1 . '. ' . $preset['label'],  // 添加问题标签
                            'message_type' => $messageType,
                            'role_type' => 'customer',  // 添加角色类型
                            'is_read' => 0,
                            'create_time' => time(),
                            'update_time' => time()
                        ]);
                        
                        // 处理回复内容，如果有标题则添加标题
                        $replyContent = $preset['content'];
                        if (isset($preset['title']) && !empty($preset['title'])) {
                            $replyContent = '<div style="font-weight:bold;margin-bottom:8px;color:#4b5563;">' . $preset['title'] . '</div>' . $replyContent;
                        }
                        
                        // 自动发送预设回复
                        $replyMessage = Db::name('plugin_chat_messages')->insertGetId([
                            'session_id' => $sessionId,
                            'sender_type' => 'staff',
                            'sender_id' => 0,
                            'message' => $replyContent,
                            'message_type' => 'text',
                            'role_type' => 'staff',  // 添加角色类型
                            'is_read' => 0,
                            'create_time' => time() + 1,
                            'update_time' => time() + 1
                        ]);
                        
                        // 更新会话状态
                        Db::name('plugin_chat_sessions')
                            ->where('id', $sessionId)
                            ->update([
                                'last_message' => isset($preset['title']) && $preset['title'] ? $preset['title'] : $preset['label'],
                                'last_time' => time() + 1,
                                'update_time' => time() + 1,
                                'unread_count' => Db::raw('unread_count + 1')
                            ]);
                        
                        // 返回两条消息
                        $messages = Db::name('plugin_chat_messages')
                            ->whereIn('id', [$messageId, $replyMessage])
                            ->select()
                            ->toArray();
                        
                        return $this->success('发送成功', $messages);
                    }
                }
                
                // 检查是否匹配商家预设问题 (新增的处理逻辑)
                if ($session['source'] === 'merchant' && $session['merchant_id'] > 0) {
                    // 获取商家ID
                    $merchantId = $session['merchant_id'];
                    
                    // 获取商家的预设问题设置
                    $merchantSettings = Db::name('plugin_chat_settings')
                        ->where('user_id', $merchantId)
                        ->find();
                        
                    if (!empty($merchantSettings) && !empty($merchantSettings['settings'])) {
                        $settingsData = json_decode($merchantSettings['settings'], true) ?: [];
                        
                        // 检查是否有预设问题及答案配置
                        if (!empty($settingsData['presetQuestions']) && !empty($settingsData['presetQuestions']['questions'])) {
                            $questions = $settingsData['presetQuestions']['questions'];
                            
                            // 查找匹配的问题
                            $matchedQuestion = null;
                            $matchedAnswer = null;
                            $questionIndex = null;
                            
                            // 删除数字前缀和点，用于更精确匹配
                            $cleanMessage = preg_replace('/^\d+\.\s*/', '', trim($message));
                            
                            foreach ($questions as $index => $question) {
                                // 支持新旧两种数据格式
                                if (is_array($question)) {
                                    $questionText = $question['question'] ?? '';
                                    $answerText = $question['answer'] ?? '';
                                } else {
                                    $questionText = $question;
                                    $answerText = ''; // 旧格式没有答案
                                }
                                
                                // 清理问题文本，移除数字前缀
                                $cleanQuestion = preg_replace('/^\d+\.\s*/', '', trim($questionText));
                                
                                // 检查是否直接匹配或包含在消息中
                                if ($cleanMessage === $cleanQuestion || 
                                    strpos($cleanMessage, $cleanQuestion) !== false || 
                                    strpos($cleanQuestion, $cleanMessage) !== false) {
                                    $matchedQuestion = $questionText;
                                    $matchedAnswer = $answerText;
                                    $questionIndex = $index;
                                    break;
                                }
                            }
                            
                            // 如果找到匹配的问题和答案
                            if ($matchedQuestion && !empty($matchedAnswer)) {

                                
                                // 先发送客户的问题
                                $messageId = Db::name('plugin_chat_messages')->insertGetId([
                                    'session_id' => $sessionId,
                                    'sender_type' => $senderType,
                                    'sender_id' => $session['contact_id'],
                                    'message' => $message,  // 使用客户原始输入
                                    'message_type' => $messageType,
                                    'role_type' => 'customer',
                                    'is_read' => 0,
                                    'create_time' => time(),
                                    'update_time' => time()
                                ]);
                                
                                // 商家自动回复
                                $replyMessage = Db::name('plugin_chat_messages')->insertGetId([
                                    'session_id' => $sessionId,
                                    'sender_type' => 'staff',
                                    'sender_id' => $merchantId, // 使用商家ID
                                    'message' => $matchedAnswer,
                                    'message_type' => 'text',
                                    'role_type' => 'merchant', // 角色类型为商家
                                    'is_read' => 0,
                                    'create_time' => time() + 1,
                                    'update_time' => time() + 1
                                ]);
                                
                                // 更新会话状态
                                Db::name('plugin_chat_sessions')
                                    ->where('id', $sessionId)
                                    ->update([
                                        'last_message' => mb_substr($matchedAnswer, 0, 20, 'UTF-8') . (mb_strlen($matchedAnswer, 'UTF-8') > 20 ? '...' : ''),
                                        'last_time' => time() + 1,
                                        'update_time' => time() + 1,
                                        'unread_count' => Db::raw('unread_count + 1')
                                    ]);
                                
                                // 返回两条消息
                                $messages = Db::name('plugin_chat_messages')
                                    ->whereIn('id', [$messageId, $replyMessage])
                                    ->select()
                                    ->toArray();
                                
                                return $this->success('发送成功', $messages);
                            }
                        }
                    }
                }
            }
            
            // 在商家预设问题匹配逻辑之后添加问答回复匹配逻辑
            if (isset($params['qa_replies']) && $params['qa_replies']['enabled'] && !empty($params['qa_replies']['items'])) {
                $userQuestion = trim($message);
                if (!empty($userQuestion)) {

                    
                    // 获取相似度阈值，默认75%
                    $threshold = isset($params['qa_replies']['threshold']) ? intval($params['qa_replies']['threshold']) : 75;
                    $threshold = $threshold / 100; // 转换为0-1的范围
                    
                    $bestMatch = null;
                    $bestSimilarity = 0;
                    
                    // 遍历所有问答项，查找最匹配的
                    foreach ($params['qa_replies']['items'] as $qaItem) {
                        if (empty($qaItem['question']) || empty($qaItem['answer'])) {
                            continue;
                        }
                        
                        // 计算问题相似度
                        $similarity = $this->calculateSimilarity($userQuestion, $qaItem['question']);
                        
                        // 如果有关键词，提高匹配权重
                        if (!empty($qaItem['keywords'])) {
                            $keywords = array_map('trim', explode(',', $qaItem['keywords']));
                            foreach ($keywords as $keyword) {
                                if (!empty($keyword) && stripos($userQuestion, $keyword) !== false) {
                                    // 关键词匹配，增加相似度
                                    $similarity += 0.2;
                                    // 相似度不超过1
                                    $similarity = min(1, $similarity);
                                    break;
                                }
                            }
                        }
                        
                        // 如果相似度更高，更新最佳匹配
                        if ($similarity > $bestSimilarity) {
                            $bestSimilarity = $similarity;
                            $bestMatch = $qaItem;
                        }
                    }
                    
                    // 如果找到匹配的问答项且相似度超过阈值
                    if ($bestMatch !== null && $bestSimilarity >= $threshold) {

                        
                        // 先发送客户的问题
                        $messageId = Db::name('plugin_chat_messages')->insertGetId([
                            'session_id' => $sessionId,
                            'sender_type' => $senderType,
                            'sender_id' => $session['contact_id'],
                            'message' => $message,  // 使用客户原始输入
                            'message_type' => $messageType,
                            'role_type' => 'customer',  // 添加角色类型
                            'is_read' => 0,
                            'create_time' => time(),
                            'update_time' => time()
                        ]);
                        
                        // 自动发送回答
                        $replyMessage = Db::name('plugin_chat_messages')->insertGetId([
                            'session_id' => $sessionId,
                            'sender_type' => 'staff',
                            'sender_id' => 0, // 机器人回复
                            'message' => $bestMatch['answer'],
                            'message_type' => 'text',
                            'role_type' => 'staff',  // 添加角色类型
                            'is_read' => 0,
                            'create_time' => time() + 1,
                            'update_time' => time() + 1
                        ]);
                        
                        // 更新会话状态
                        Db::name('plugin_chat_sessions')
                            ->where('id', $sessionId)
                            ->update([
                                'last_message' => mb_substr($bestMatch['answer'], 0, 20, 'UTF-8') . (mb_strlen($bestMatch['answer'], 'UTF-8') > 20 ? '...' : ''),
                                'last_time' => time() + 1,
                                'update_time' => time() + 1,
                                'unread_count' => Db::raw('unread_count + 1')
                            ]);
                        
                        // 返回两条消息
                        $messages = Db::name('plugin_chat_messages')
                            ->whereIn('id', [$messageId, $replyMessage])
                            ->select()
                            ->toArray();
                        
                        return $this->success('发送成功', $messages);
                    }
                }
            }
            
            // 如果不是预设问题或没有匹配到，按普通消息处理
            // 获取管理员ID，如果是客服发送
            $staffId = 0;
            if ($senderType == 'staff') {
                // 尝试获取当前登录的管理员ID
                if ($this->adminInfo && isset($this->adminInfo['id'])) {
                    $staffId = $this->adminInfo['id'];
                } elseif (session('?admin')) {
                    $adminInfo = session('admin');
                    $staffId = $adminInfo['id'] ?? 0;
                }
                
                // 如果客户端提供了发送者ID，则使用客户端的值
                if ($senderId > 0) {
                    $staffId = $senderId;
                }
                
                // 如果仍然没有有效的staff_id，使用默认值1
                if ($staffId <= 0) {
                    $staffId = 1; // 默认管理员ID
                }
            }
            
            // 确定客户发送者ID
            $customerId = $session['contact_id'];
            // 如果是客户且提供了sender_id
            if ($senderType == 'customer' && $senderId > 0) {
                $customerId = $senderId;
            }
            
            // 确保消息类型与内容一致性
            if ($messageType != 'text' && empty($fileUrl)) {
                // 如果指定了非文本类型但没有文件URL，则默认为文本类型
                $messageType = 'text';
            } else if (!empty($fileUrl) && $messageType == 'text') {
                // 如果有文件URL但类型为文本，则根据URL后缀判断类型
                $extension = strtolower(pathinfo($fileUrl, PATHINFO_EXTENSION));
                $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
                
                if (in_array($extension, $imageExtensions)) {
                    $messageType = 'image';
                } else {
                    $messageType = 'file';
                }
            }
            
            // 检查消息是否是图片URL格式
            if ($messageType == 'text' && !empty($message)) {
                // 检查是否是标准的图片URL格式
                if (preg_match('/\.(jpeg|jpg|gif|png|webp|bmp)(\?.*)?$/i', $message) && filter_var($message, FILTER_VALIDATE_URL)) {
                    $messageType = 'image';
                    // 如果是图片URL，则将其设置为fileUrl
                    $fileUrl = $message;
                }
                // 检查是否是上传路径格式
                else if (strpos($message, '/upload/') !== false && !preg_match('/<[^>]*>/', $message) && filter_var($message, FILTER_VALIDATE_URL)) {
                    $messageType = 'image';
                    $fileUrl = $message;
                }
            }
            
            // 构建消息数据
            $data = [
                'session_id' => $sessionId,
                'sender_type' => $senderType,
                'sender_id' => $senderType == 'staff' ? $staffId : $customerId,
                'message_type' => $messageType,  // 确保每条消息都有消息类型
                'is_read' => 0,
                'create_time' => time(),
                'update_time' => time()
            ];
            
            // 处理图片或文件类型消息
            if ($messageType == 'image') {
                // 确保有图片URL
                $imgUrl = !empty($fileUrl) ? $fileUrl : $message;
                
                // 将图片URL包装成HTML格式
                $data['message'] = '<div><div class="message-image-container"><img src="' . $imgUrl . '" class="message-image" alt="图片消息" style="display: block;"></div></div>';
                $data['file_url'] = $imgUrl;
            } else if ($messageType == 'file') {
                // 文件类型消息
                $data['message'] = $message;
                $data['file_url'] = $fileUrl;
            } else {
                // 文本或其他类型消息
                $data['message'] = $message;
            }
            
            // 设置角色类型
            if ($senderType == 'staff') {
                // 所有管理员发送的消息都显示为客服，不再区分商家
                $data['role_type'] = 'staff';
                
                // 如果是系统消息，添加特殊样式
                if ($staffId == 0) {
                    $data['message'] = '<div class="system-message">' . $message . '</div>';
                }
            } else {
                $data['role_type'] = 'customer';
            }
            
            // 发送消息
            $messageId = Db::name('plugin_chat_messages')->insertGetId($data);
            
            if (!$messageId) {
                return $this->error('发送失败');
            }
            
            // 更新会话的最后消息和时间
            $updateData = [
                'last_message' => $messageType == 'text' ? $message : '[文件]',
                'last_time' => time(),
                'update_time' => time(),
            ];
            
            // 如果是客户发送消息，增加未读消息数
            if ($senderType == 'customer') {
                $updateData['unread_count'] = Db::raw('unread_count + 1');
            }
            
            Db::name('plugin_chat_sessions')
                ->where('id', $sessionId)
                ->update($updateData);

            // 获取新创建的消息，包含完整信息
            $newMessage = Db::name('plugin_chat_messages')
                ->where('id', $messageId)
                ->find();
                
            // 确保ID是整数
            if ($newMessage) {
                $newMessage['id'] = intval($newMessage['id']);
                
                // 确保时间戳是整数
                if (isset($newMessage['create_time'])) {
                    $newMessage['create_time'] = intval($newMessage['create_time']);
                }
                if (isset($newMessage['update_time'])) {
                    $newMessage['update_time'] = intval($newMessage['update_time']);
                }
                
                // 确保消息类型存在，默认为文本类型
                if (empty($newMessage['message_type'])) {
                    $newMessage['message_type'] = 'text';
                }
            }
            
            // 查询会话状态消息（如果会话状态在此过程中发生变化）
            $statusMessage = null;
            $session = Db::name('plugin_chat_sessions')
                ->where('id', $sessionId)
                ->find();
                
            // 如果是商家会话，检查是否需要通知商家
            if ($session && $session['source'] === 'merchant' && $senderType === 'customer') {
                // 从标题中提取商家ID
                $merchantIdMatch = [];
                if (preg_match('/联系商家ID:(\d+)/', $session['title'], $merchantIdMatch)) {
                    $merchantId = intval($merchantIdMatch[1]);
                    
                    // 这里可以添加通知商家的逻辑，例如发送邮件、短信或系统通知
                    // 例如：记录到商家的未读消息表中
                }
            }
            
            // 调用Hook进行消息发送后的处理
            try {
                $hook = new \plugin\Customersystem\Hook();
                $hook->afterMessageSent($sessionId, $newMessage);
                
                // 清除会话缓存，强制下次获取最新数据
                $hook->clearSessionCache($sessionId);
            } catch (\Exception $e) {
                Log::error("消息发送后Hook处理失败: " . $e->getMessage());
            }
            
            // 将结果作为数组返回，方便前端统一处理
            $responseMessages = [$newMessage];
            
            return $this->success('发送成功', $responseMessages);
        } catch (\Exception $e) {
            return $this->error('发送失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 查询历史消息
     */
    public function queryHistory()
    {
        try {
            $post = $this->request->post();
            
            // 验证必须的字段
            if (empty($post['email']) && empty($post['phone'])) {
                return $this->error('请提供邮箱地址或电话号码');
            }
            
            // 是否为静默查询，即不记录日志
            $isSilent = isset($post['silent']) && $post['silent'] == 1;
            
            if (!$isSilent) {
                // 记录查询日志
                $logInfo = "Customersystem查询历史消息: ";
                if (!empty($post['email'])) {
                    $logInfo .= "邮箱={$post['email']}";
                }
                if (!empty($post['phone'])) {
                    $logInfo .= (!empty($post['email']) ? ", " : "") . "电话={$post['phone']}";
                }
            }
            
            // 记录查询条件
            $email = !empty($post['email']) ? $post['email'] : '';
            $phone = !empty($post['phone']) ? $post['phone'] : '';
            

            
            // 第一步：查询plugin_chat_contacts表，获取匹配的联系人ID
            $contactIds = [];

            // 根据邮箱查询联系人ID
            if (!empty($email)) {
                $emailContactIds = Db::name('plugin_chat_contacts')
                    ->where('email', $email)
                    ->column('id');

                if (!empty($emailContactIds)) {
                    $contactIds = array_merge($contactIds, $emailContactIds);
                }
            }

            // 根据电话查询联系人ID
            if (!empty($phone)) {
                $phoneContactIds = Db::name('plugin_chat_contacts')
                    ->where('phone', $phone)
                    ->column('id');

                if (!empty($phoneContactIds)) {
                    $contactIds = array_merge($contactIds, $phoneContactIds);
                }
            }

            // 去重联系人ID
            $contactIds = array_unique($contactIds);
            


            
            if (empty($contactIds)) {
                return $this->success('未找到聊天记录', []);
            }

            // 记录找到的联系人ID
            Log::info("历史查询找到联系人ID", [
                'email' => $email,
                'phone' => $phone,
                'contact_ids' => $contactIds
            ]);

            // 第二步：根据联系人ID查询plugin_chat_sessions表中的所有相关会话
            $sessions = Db::name('plugin_chat_sessions')
                ->whereIn('contact_id', $contactIds)
                ->order('last_time desc')
                ->select()
                ->toArray();

            // 确保会话ID唯一性，去除可能的重复
            $uniqueSessions = [];
            $seenSessionIds = [];
            foreach ($sessions as $session) {
                if (!in_array($session['id'], $seenSessionIds)) {
                    $uniqueSessions[] = $session;
                    $seenSessionIds[] = $session['id'];
                }
            }
            $sessions = $uniqueSessions;


            
            if (empty($sessions)) {
                return $this->success('未找到聊天记录', []);
            }
            
            // 清除缓存，确保获取最新数据
            $hook = new \plugin\Customersystem\Hook();

            // 第四步：获取所有相关联系人的完整信息，创建映射
            $contacts = Db::name('plugin_chat_contacts')
                ->whereIn('id', $contactIds)
                ->select()
                ->toArray();

            // 创建联系人ID到联系人信息的映射
            $contactMap = [];
            foreach ($contacts as $contact) {
                $contactMap[$contact['id']] = $contact;
            }
            
            // 获取每个会话的最新消息
            foreach ($sessions as &$session) {
                // 清除此会话的缓存
                $hook->clearSessionCache($session['id']);

                // 获取此会话对应的联系人信息并添加到会话数据中
                if (isset($contactMap[$session['contact_id']])) {
                    $session['contact'] = $contactMap[$session['contact_id']];
                } else {
                    // 如果映射中没有找到，尝试从数据库中获取
                    $session['contact'] = Db::name('plugin_chat_contacts')
                        ->where('id', $session['contact_id'])
                        ->find();
                }
                
                // 从数据库获取最新消息
                $session['messages'] = Db::name('plugin_chat_messages')
                    ->where('session_id', $session['id'])
                    ->order('create_time asc') // 时间正序排列
                    ->select()
                    ->toArray();
                    
                // 确保消息ID和时间戳是整数
                foreach ($session['messages'] as &$message) {
                    if (isset($message['id'])) {
                        $message['id'] = intval($message['id']);
                    }
                    if (isset($message['create_time'])) {
                        $message['create_time'] = intval($message['create_time']);
                    }
                    if (isset($message['update_time'])) {
                        $message['update_time'] = intval($message['update_time']);
                    }
                }
                
                // 计算会话中未读的客服消息数量
                $unreadStaffMessages = Db::name('plugin_chat_messages')
                    ->where('session_id', $session['id'])
                    ->where('sender_type', 'staff')
                    ->where('is_read', 0)
                    ->count();
                    
                $session['unread_staff_count'] = $unreadStaffMessages;
                
                // 如果有未读消息，标记为未读状态，便于前端显示提示
                if ($unreadStaffMessages > 0) {
                    $session['has_unread'] = true;
                } else {
                    $session['has_unread'] = false;
                }
            }
            
            // 当非静默查询时更新会话已读状态
            if (!$isSilent) {
                
                // 将所有客服消息标记为已读
                foreach ($sessions as $session) {
                    Db::name('plugin_chat_messages')
                        ->where('session_id', $session['id'])
                        ->where('sender_type', 'staff')
                        ->where('is_read', 0)
                        ->update(['is_read' => 1, 'update_time' => time()]);
                }
            }
            
            return $this->success('查询成功', [
                'contacts' => $contacts,
                'sessions' => $sessions
            ]);
        } catch (\Exception $e) {
            Log::error("Customersystem查询历史消息失败: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return $this->error('查询失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 文件上传
     */
    public function upload()
    {
        try {
            // 获取参数配置
            $params = $this->getParamsData();
            $uploadConfig = $params['upload_config'] ?? [];
            
            // 获取上传文件
            $file = $this->request->file('file');
            
            // 验证文件
            if (empty($file)) {
                return $this->error('请选择文件');
            }
            
            // 记录上传来源信息
            $isClipboard = false;
            $filename = $file->getOriginalName();
            if ($filename === 'clipboard_image.png' || $filename === 'image.png') {
                $isClipboard = true;
            }
            
            // 验证文件大小
            $maxSize = $uploadConfig['max_file_size'] ?? 5242880; // 默认5MB
            if ($file->getSize() > $maxSize) {
                return $this->error('文件大小不能超过' . ($maxSize / 1024 / 1024) . 'MB');
            }
            
            // 验证文件类型
            $ext = $file->getOriginalExtension();
            $allowedExt = $uploadConfig['allowed_extensions'] ?? ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx'];
            if (!in_array(strtolower($ext), array_map('strtolower', $allowedExt))) {
                return $this->error('不支持的文件类型');
            }
            
            // 针对剪贴板图片生成更友好的文件名
            $savename = '';
            if ($isClipboard) {
                // 为剪贴板图片生成带时间戳的文件名
                $timestamp = date('YmdHis');
                $randStr = substr(md5(uniqid(mt_rand(), true)), 0, 8);
                $savename = \think\facade\Filesystem::disk('public')->putFileAs(
                    'chat', 
                    $file, 
                    "clipboard_{$timestamp}_{$randStr}.png"
                );
            } else {
                // 其他文件使用默认处理方式
                $savename = \think\facade\Filesystem::disk('public')->putFile('chat', $file);
            }
            
            // 文件URL
            $fileUrl = '/uploads/' . $savename;
            
            // 判断是否为图片类型
            $messageType = in_array(strtolower($ext), ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']) ? 'image' : 'file';
            
            return $this->success('上传成功', [
                'url' => $fileUrl,
                'type' => $messageType,
                'name' => $isClipboard ? '剪贴板图片' : $file->getOriginalName(),
                'size' => $file->getSize()
            ]);
        } catch (\Exception $e) {
            Log::error("文件上传失败: " . $e->getMessage());
            return $this->error('上传失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取所有会话列表 (管理员)
     */
    public function getSessionList()
    {
        try {
            $page = $this->request->param('page/d', 1);
            $limit = $this->request->param('limit/d', 20);
            $status = $this->request->param('status', '');
            
            // 查询条件
            $where = [];
            if ($status !== '') {
                $where[] = ['status', '=', $status];
            }
            
            // 判断当前用户是否为管理员
            $isAdmin = $this->adminInfo && isset($this->adminInfo['id']);
            
            // 如果是普通商家，只能看到与自己相关的会话
            if (!$isAdmin && session('?user')) {
                $currentUser = session('user');
                $merchantId = $currentUser['id'] ?? 0;
                
                // 商家只能看到两种会话：
                // 1. source=merchant且merchant_id等于自己ID的会话
                // 2. source=customer的会话（联系客服）
                $where[] = function ($query) use ($merchantId) {
                    $query->where('source', 'customer')
                          ->whereOr(function ($query) use ($merchantId) {
                              $query->where('source', 'merchant')
                                   ->where('merchant_id', $merchantId);
                          });
                };
            }
            
            // 查询会话列表
            $list = Db::name('plugin_chat_sessions')
                ->where($where)
                ->order('last_time desc')
                ->paginate([
                    'list_rows' => $limit,
                    'page' => $page,
                ])
                ->each(function($item) {
                    // 获取联系人信息
                    $contact = Db::name('plugin_chat_contacts')
                        ->where('id', $item['contact_id'])
                        ->find();
                    
                    if ($contact) {
                        $item['contact'] = $contact;
                    }
                    
                    return $item;
                });
            
            return $this->success('获取成功', $list);
        } catch (\Exception $e) {
            return $this->error('获取会话列表失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取会话详情 (管理员)
     */
    public function getSessionDetail()
    {
        try {
            $sessionId = $this->request->param('id/d');
            
            if (empty($sessionId)) {
                return $this->error('会话ID不能为空');
            }
            
            // 查询会话
            $session = Db::name('plugin_chat_sessions')
                ->where('id', $sessionId)
                ->find();
                
            if (empty($session)) {
                return $this->error('会话不存在');
            }
            
            // 判断当前用户是否为管理员
            $isAdmin = $this->adminInfo && isset($this->adminInfo['id']);
            
            // 如果不是管理员，检查是否有权限查看此会话
            if (!$isAdmin && session('?user')) {
                $currentUser = session('user');
                $merchantId = $currentUser['id'] ?? 0;
                
                // 检查会话类型和商家ID
                if ($session['source'] == 'merchant' && $session['merchant_id'] != $merchantId) {
                    return $this->error('您无权查看此会话');
                }
            }
            
            // 获取联系人信息
            $contact = Db::name('plugin_chat_contacts')
                ->where('id', $session['contact_id'])
                ->find();
                
            // 获取消息列表
            $messages = Db::name('plugin_chat_messages')
                ->where('session_id', $sessionId)
                ->order('create_time asc')
                ->select()
                ->toArray();
                
            // 更新已读状态
            Db::name('plugin_chat_sessions')
                ->where('id', $sessionId)
                ->update([
                    'unread_count' => 0,
                    'update_time' => time()
                ]);
                
            Db::name('plugin_chat_messages')
                ->where('session_id', $sessionId)
                ->where('is_read', 0)
                ->update([
                    'is_read' => 1,
                    'update_time' => time()
                ]);
                
            return $this->success('获取成功', [
                'session' => $session,
                'contact' => $contact,
                'messages' => $messages
            ]);
        } catch (\Exception $e) {
            return $this->error('获取会话详情失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 更新会话状态 (管理员)
     * 注意：当会话状态为closed时，客户将无法发送新消息
     */
    public function updateSessionStatus()
    {
        try {
            $post = $this->request->post();
            
            // 验证必须的字段
            if (empty($post['session_id'])) {
                return $this->error('会话ID不能为空');
            }
            
            if (empty($post['status']) || !in_array($post['status'], ['open', 'closed'])) {
                return $this->error('状态值无效');
            }
            
            // 验证会话是否存在
            $session = Db::name('plugin_chat_sessions')
                ->where('id', $post['session_id'])
                ->find();
                
            if (empty($session)) {
                return $this->error('会话不存在');
            }
            
            // 更新会话状态
            Db::name('plugin_chat_sessions')
                ->where('id', $post['session_id'])
                ->update([
                    'status' => $post['status'],
                    'update_time' => time()
                ]);
                
            // 如果关闭会话，添加一条系统消息
            if ($post['status'] === 'closed') {
                Db::name('plugin_chat_messages')->insert([
                    'session_id' => $post['session_id'],
                    'sender_type' => 'staff',
                    'sender_id' => 0, // 0表示系统消息
                    'message' => '会话已关闭',
                    'message_type' => 'text',
                    'is_read' => 0,
                    'create_time' => time(),
                    'update_time' => time(),
                ]);
            } else if ($post['status'] === 'open' && $session['status'] === 'closed') {
                // 如果重新打开会话，添加一条系统消息
                Db::name('plugin_chat_messages')->insert([
                    'session_id' => $post['session_id'],
                    'sender_type' => 'staff',
                    'sender_id' => 0, // 0表示系统消息
                    'message' => '会话已重新打开',
                    'message_type' => 'text',
                    'is_read' => 0,
                    'create_time' => time(),
                    'update_time' => time(),
                ]);
            }
                
            return $this->success('更新会话状态成功');
        } catch (\Exception $e) {
            return $this->error('更新会话状态失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取详细历史对话记录
     */
    public function getDetailedHistory()
    {
        try {
            $sessionId = $this->request->param('session_id/d');
            
            if (empty($sessionId)) {
                return $this->error('会话ID不能为空');
            }
            
            // 查询会话是否存在
            $session = Db::name('plugin_chat_sessions')
                ->where('id', $sessionId)
                ->find();
                
            if (empty($session)) {
                return $this->error('会话不存在');
            }
            
            // 获取联系人信息
            $contact = Db::name('plugin_chat_contacts')
                ->where('id', $session['contact_id'])
                ->find();
                
            // 获取全部历史消息，按时间排序
            $messages = Db::name('plugin_chat_messages')
                ->where('session_id', $sessionId)
                ->order('create_time asc')
                ->select()
                ->toArray();
                
            return $this->success('获取历史记录成功', [
                'session' => $session,
                'contact' => $contact,
                'messages' => $messages
            ]);
        } catch (\Exception $e) {
            return $this->error('获取历史记录失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取插件参数数据
     * @return array 插件参数数据
     * @throws \Exception 文件不存在时抛出异常
     */
    private function getParamsData()
    {
        $paramsFile = __DIR__ . '/../params.php';
        if (!file_exists($paramsFile)) {
            throw new \Exception('参数文件不存在');
        }
        
        $params = include $paramsFile;
        
        // 确保布尔值被正确处理
        $this->convertBooleanValues($params);
        
        // 确保返回的参数中包含必要的结构
        if (!isset($params['quick_replies'])) {
            $params['quick_replies'] = [
                'enabled' => true,
                'items' => [
                    [
                        'label' => '欢迎语',
                        'content' => '您好，很高兴为您服务，请问有什么可以帮到您？'
                    ],
                    [
                        'label' => '稍等回复',
                        'content' => '请您稍等，我正在查询相关信息，很快为您回复。'
                    ],
                    [
                        'label' => '感谢回复',
                        'content' => '感谢您的咨询，祝您使用愉快！如有其他问题，随时联系我们。'
                    ]
                ]
            ];
        }
        
        if (!isset($params['quick_replies']['items'])) {
            $params['quick_replies']['items'] = [
                [
                    'label' => '欢迎语',
                    'content' => '您好，很高兴为您服务，请问有什么可以帮到您？'
                ],
                [
                    'label' => '稍等回复',
                    'content' => '请您稍等，我正在查询相关信息，很快为您回复。'
                ],
                [
                    'label' => '感谢回复',
                    'content' => '感谢您的咨询，祝您使用愉快！如有其他问题，随时联系我们。'
                ]
            ];
        }
        
        // 确保问答回复结构存在
        if (!isset($params['qa_replies'])) {
            $params['qa_replies'] = [
                'enabled' => true,
                'title' => '智能问答',
                'description' => '输入您的问题，系统会智能匹配回复',
                'threshold' => 75,
                'items' => [
                    [
                        'question' => '如何联系客服？',
                        'answer' => '您可以通过网站右下角的在线客服按钮与我们联系，或者拨打服务热线：400-123-4567。',
                        'keywords' => '联系,客服,电话,服务'
                    ],
                    [
                        'question' => '忘记密码怎么办？',
                        'answer' => '您可以点击登录页面的"忘记密码"按钮，通过绑定的邮箱或手机号进行密码重置。',
                        'keywords' => '密码,忘记,重置,找回'
                    ]
                ]
            ];
        }
        
        if (!isset($params['qa_replies']['items'])) {
            $params['qa_replies']['items'] = [
                [
                    'question' => '如何联系客服？',
                    'answer' => '您可以通过网站右下角的在线客服按钮与我们联系，或者拨打服务热线：400-123-4567。',
                    'keywords' => '联系,客服,电话,服务'
                ],
                [
                    'question' => '忘记密码怎么办？',
                    'answer' => '您可以点击登录页面的"忘记密码"按钮，通过绑定的邮箱或手机号进行密码重置。',
                    'keywords' => '密码,忘记,重置,找回'
                ]
            ];
        }
        
        // 确保问答回复阈值存在
        if (!isset($params['qa_replies']['threshold'])) {
            $params['qa_replies']['threshold'] = 75;
        }
        
        return $params;
    }

    /**
     * 切换聊天系统开关状态
     */
    public function toggleChatStatus()
    {
        try {
            $enabled = $this->request->param('enabled/b', false);
            
            // 获取当前参数
            $params = $this->getParamsData();
            if (empty($params)) {
                return $this->error('获取参数失败');
            }
            
            // 只修改chat_enabled参数，不影响其他参数
            $params['settings']['chat_enabled'] = (bool)$enabled;  // 确保是布尔值
            
            // 保存参数到params.php文件
            $paramsFile = __DIR__ . '/../params.php';
            file_put_contents($paramsFile, "<?php\n\nreturn " . var_export($params, true) . ";\n");
            
            return $this->success('设置保存成功');
        } catch (\Exception $e) {
            return $this->error('设置保存失败: ' . $e->getMessage());
        }
    }

    /**
     * 标准响应：成功
     */
    public function success($msg = '', $data = null, $code = 200, $type = null, array $header = [])
    {
        return json([
            'code' => $code,
            'msg'  => $msg,
            'time' => time(),
            'data' => $data,
        ])->header($header);
    }

    /**
     * 标准响应：错误
     */
    public function error($msg = '', $data = null, $code = 0, $type = null, array $header = [])
    {
        return json([
            'code' => $code,
            'msg'  => $msg,
            'time' => time(),
            'data' => $data,
        ])->header($header);
    }

    /**
     * 检查导航状态
     */
    public function checkNav()
    {
        $nav = Db::name('nav')
            ->where('href', '/plugin/Customersystem/api/chat')
            ->where('status', 1)
            ->find();
        
        return json(['exists' => !empty($nav)]);
    }

    /**
     * 切换导航菜单
     */
    public function toggleNav()
    {
        try {
            $nav = Db::name('nav')
                ->where('href', '/plugin/Customersystem/api/chat')
                ->find();
            
            if (empty($nav)) {
                // 添加到导航
                Db::name('nav')->insert([
                    'pid' => 0,
                    'name' => '在线客服',
                    'href' => '/plugin/Customersystem/api/chat',
                    'target' => '_self',
                    'sort' => 99,
                    'status' => 1,
                    'create_time' => time(),
                    'update_time' => time(),
                ]);
                
                return json(['code' => 1, 'msg' => '成功添加到导航栏']);
            } else {
                // 从导航移除（修改状态而非删除）
                Db::name('nav')
                    ->where('id', $nav['id'])
                    ->update([
                        'status' => $nav['status'] == 1 ? 0 : 1,
                        'update_time' => time()
                    ]);
                
                return json(['code' => 1, 'msg' => $nav['status'] == 1 ? '已从导航栏移除' : '已添加到导航栏']);
            }
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '操作失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 清空会话消息
     */
    public function clearMessages()
    {
        try {
            $sessionId = $this->request->param('session_id/d');
            
            if (empty($sessionId)) {
                return $this->error('会话ID不能为空');
            }
            
            // 验证会话是否存在
            $session = Db::name('plugin_chat_sessions')
                ->where('id', $sessionId)
                ->find();
                
            if (empty($session)) {
                return $this->error('会话不存在');
            }
            
            // 删除所有消息
            Db::name('plugin_chat_messages')
                ->where('session_id', $sessionId)
                ->delete();
                
            // 更新会话信息
            Db::name('plugin_chat_sessions')
                ->where('id', $sessionId)
                ->update([
                    'last_message' => '所有消息已清空',
                    'last_time' => time(),
                    'unread_count' => 0,
                    'update_time' => time()
                ]);
                
            // 添加系统消息
            Db::name('plugin_chat_messages')->insert([
                'session_id' => $sessionId,
                'sender_type' => 'staff',
                'sender_id' => 0, // 0表示系统消息
                'message' => '所有历史消息已被清空',
                'message_type' => 'text',
                'is_read' => 0,
                'create_time' => time(),
                'update_time' => time(),
            ]);
            
            return $this->success('会话消息已清空');
        } catch (\Exception $e) {
            return $this->error('清空消息失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 批量删除消息
     */
    public function deleteMessages()
    {
        try {
            $post = $this->request->post();
            
            if (empty($post['message_ids']) || !is_array($post['message_ids'])) {
                return $this->error('消息ID不能为空');
            }
            
            $messageIds = $post['message_ids'];
            $sessionId = $post['session_id'] ?? 0;
            
            if (empty($sessionId)) {
                return $this->error('会话ID不能为空');
            }
            
            // 验证会话是否存在
            $session = Db::name('plugin_chat_sessions')
                ->where('id', $sessionId)
                ->find();
                
            if (empty($session)) {
                return $this->error('会话不存在');
            }
            
            // 批量删除消息
            Db::name('plugin_chat_messages')
                ->whereIn('id', $messageIds)
                ->where('session_id', $sessionId)
                ->delete();
                
            // 获取最新消息作为会话的最后消息
            $lastMessage = Db::name('plugin_chat_messages')
                ->where('session_id', $sessionId)
                ->order('create_time desc')
                ->find();
                
            if ($lastMessage) {
                // 更新会话的最后消息
                Db::name('plugin_chat_sessions')
                    ->where('id', $sessionId)
                    ->update([
                        'last_message' => $lastMessage['message_type'] == 'text' ? $lastMessage['message'] : '[文件]',
                        'last_time' => $lastMessage['create_time'],
                        'update_time' => time()
                    ]);
            } else {
                // 如果没有消息了，更新为默认值
                Db::name('plugin_chat_sessions')
                    ->where('id', $sessionId)
                    ->update([
                        'last_message' => '无消息',
                        'last_time' => time(),
                        'update_time' => time()
                    ]);
            }
            
            // 重新计算未读消息数
            $unreadCount = Db::name('plugin_chat_messages')
                ->where('session_id', $sessionId)
                ->where('is_read', 0)
                ->count();
                
            Db::name('plugin_chat_sessions')
                ->where('id', $sessionId)
                ->update([
                    'unread_count' => $unreadCount
                ]);
                
            return $this->success('消息已删除');
        } catch (\Exception $e) {
            return $this->error('删除消息失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量删除会话
     */
    public function deleteSessions()
    {
        try {
            $post = $this->request->post();
            
            if (empty($post['session_ids']) || !is_array($post['session_ids'])) {
                return $this->error('会话ID不能为空');
            }
            
            $sessionIds = $post['session_ids'];
            
            // 开启事务
            Db::startTrans();
            try {
                // 获取要删除的会话关联的联系人ID
                $contactIds = Db::name('plugin_chat_sessions')
                    ->whereIn('id', $sessionIds)
                    ->column('contact_id');
                
                // 删除会话消息
                Db::name('plugin_chat_messages')
                    ->whereIn('session_id', $sessionIds)
                    ->delete();
                
                // 删除会话
                Db::name('plugin_chat_sessions')
                    ->whereIn('id', $sessionIds)
                    ->delete();
                
                // 删除联系人（如果该联系人没有其他会话）
                if (!empty($contactIds)) {
                    foreach ($contactIds as $contactId) {
                        // 检查联系人是否还有其他会话
                        $hasOtherSessions = Db::name('plugin_chat_sessions')
                            ->where('contact_id', $contactId)
                            ->whereNotIn('id', $sessionIds)
                            ->count();
                        
                        if ($hasOtherSessions == 0) {
                            // 如果没有其他会话，删除联系人
                            Db::name('plugin_chat_contacts')
                                ->where('id', $contactId)
                                ->delete();
                        }
                    }
                }
                
                Db::commit();
                return $this->success('删除成功');
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            return $this->error('删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 清空所有会话
     */
    public function clearAllSessions()
    {
        try {
            // 开启事务
            Db::startTrans();
            try {
                // 删除所有会话消息
                Db::name('plugin_chat_messages')->delete(true);
                
                // 删除所有会话
                Db::name('plugin_chat_sessions')->delete(true);
                
                // 删除所有联系人
                Db::name('plugin_chat_contacts')->delete(true);
                
                Db::commit();
                return $this->success('清空成功');
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            return $this->error('清空失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新会话标题
     */
    public function updateSessionTitle()
    {
        try {
            $post = $this->request->post();
            
            if (empty($post['session_id'])) {
                return $this->error('会话ID不能为空');
            }
            
            if (empty($post['title'])) {
                return $this->error('会话标题不能为空');
            }
            
            // 验证会话是否存在
            $session = Db::name('plugin_chat_sessions')
                ->where('id', $post['session_id'])
                ->find();
                
            if (empty($session)) {
                return $this->error('会话不存在');
            }
            
            // 更新会话标题
            Db::name('plugin_chat_sessions')
                ->where('id', $post['session_id'])
                ->update([
                    'title' => $post['title'],
                    'update_time' => time()
                ]);
                
            // 添加系统消息记录标题修改
            Db::name('plugin_chat_messages')->insert([
                'session_id' => $post['session_id'],
                'sender_type' => 'staff',
                'sender_id' => 0, // 0表示系统消息
                'message' => '会话标题已更新为：' . $post['title'],
                'message_type' => 'text',
                'is_read' => 0,
                'create_time' => time(),
                'update_time' => time(),
            ]);
            
            return $this->success('会话标题已更新');
        } catch (\Exception $e) {
            return $this->error('更新会话标题失败: ' . $e->getMessage());
        }
    }

    /**
     * 撤回消息
     */
    public function recallMessage()
    {
        try {
            $post = $this->request->post();
            
            // 验证必须的字段
            if (empty($post['message_id'])) {
                return $this->error('消息ID不能为空');
            }
            
            if (empty($post['session_id'])) {
                return $this->error('会话ID不能为空');
            }
            
            // 获取消息信息
            $message = Db::name('plugin_chat_messages')
                ->where('id', $post['message_id'])
                ->where('session_id', $post['session_id'])
                ->find();
                
            if (empty($message)) {
                return $this->error('消息不存在');
            }
            
            // 检查消息发送时间是否超过2分钟，超过则不允许撤回
            $timeLimit = 120; // 2分钟 = 120秒
            $now = time();
            $messageTime = $message['create_time'];
            
            if (($now - $messageTime) > $timeLimit) {
                return $this->error('消息发送已超过2分钟，无法撤回');
            }
            
            // 构建更新数据
            $updateData = [
                'message' => '[该消息已撤回]',
                'is_recalled' => 1,
                'update_time' => time()
            ];
            
            // 如果是图片或文件类型消息，清空file_url字段并修改消息类型
            if ($message['message_type'] === 'image' || $message['message_type'] === 'file') {
                $updateData['file_url'] = '';
                // 将消息类型改为text，防止前端继续渲染为图片
                $updateData['message_type'] = 'text';
            }
            
            // 不删除原消息，而是更新为撤回状态
            Db::name('plugin_chat_messages')
                ->where('id', $post['message_id'])
                ->update($updateData);
                
            // 如果这是最后一条消息，更新会话的最后消息内容
            $lastMessage = Db::name('plugin_chat_messages')
                ->where('session_id', $post['session_id'])
                ->order('create_time desc')
                ->find();
                
            if ($lastMessage && $lastMessage['id'] == $post['message_id']) {
                Db::name('plugin_chat_sessions')
                    ->where('id', $post['session_id'])
                    ->update([
                        'last_message' => '[该消息已撤回]',
                        'update_time' => time()
                    ]);
            }
            
            // 调用Hook清除缓存，确保实时更新
            try {
                $hook = new \plugin\Customersystem\Hook();
                // 清除相关缓存
                $hook->clearSessionCache($post['session_id']);
                
                // 获取更新后的消息，传给afterMessageSent方法更新缓存
                $updatedMessage = Db::name('plugin_chat_messages')
                    ->where('id', $post['message_id'])
                    ->find();
                    
                if ($updatedMessage) {
                    $hook->afterMessageSent($post['session_id'], $updatedMessage);
                }
            } catch (\Exception $e) {
                Log::error("消息撤回后清除缓存失败: " . $e->getMessage());
            }
            
            return $this->success('消息已撤回');
        } catch (\Exception $e) {
            return $this->error('撤回消息失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取可聊天的商家列表
     */
    public function getMerchantList()
    {
        try {
            // 获取商家用户列表
            $merchants = Db::name('user')
                ->field('id, username, nickname, avatar, last_login_time')
                ->order('last_login_time desc')
                ->select()
                ->toArray();
            
            return $this->success('获取成功', [
                'merchants' => $merchants
            ]);
        } catch (\Exception $e) {
            return $this->error('获取商家列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 根据token获取商家信息
     */
    public function getMerchantByToken()
    {
        try {
            $token = $this->request->param('token', '');
            
            if(empty($token)) {
                return $this->error('商家代码不能为空');
            }
            
            // 查询匹配token的商家
            $merchant = Db::name('user')
                ->field('id, username, nickname, avatar')
                ->where('token', $token)
                ->find();
                
            if(!$merchant) {
                return $this->error('未找到对应的商家');
            }
            
            return $this->success('获取成功', [
                'merchant' => $merchant
            ]);
        } catch (\Exception $e) {
            return $this->error('获取商家信息失败: ' . $e->getMessage());
        }
    }

    /**
     * 根据联系人ID获取所有会话
     */
    public function getSessionsByContactId()
    {
        try {
            $contactId = $this->request->param('contact_id/d', 0);
            
            if (empty($contactId)) {
                return $this->error('联系人ID不能为空');
            }
            
            // 获取联系人信息
            $contact = Db::name('plugin_chat_contacts')
                ->where('id', $contactId)
                ->find();
                
            if (empty($contact)) {
                return $this->error('联系人不存在');
            }
            
            // 查询该联系人的所有会话
            $prefix = config('database.connections.mysql.prefix');
            $rawSql = "SELECT * FROM `{$prefix}plugin_chat_sessions` WHERE contact_id = {$contactId} ORDER BY last_time DESC";
            $sessions = Db::query($rawSql);
            
            // 格式化结果
            $result = [
                'contact' => $contact,
                'sessions' => $sessions
            ];
            
            return $this->success('获取成功', $result);
        } catch (\Exception $e) {
            Log::error("获取联系人会话失败: " . $e->getMessage());
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 标记消息为已读
     * 
     * @return \think\Response
     */
    public function markMessageRead()
    {
        $messageId = input('message_id', 0, 'intval');
        $sessionId = input('session_id', 0, 'intval');
        $roleType = input('role_type', 'staff', 'trim'); // 新增：用户角色，默认为staff(客服)
        
        if (empty($messageId) || empty($sessionId)) {
            return $this->error('参数错误');
        }
        
        try {
            // 获取会话信息，验证权限
            $session = Db::name('plugin_chat_sessions')->where('id', $sessionId)->find();
            if (!$session) {
                return $this->error('会话不存在');
            }
            
            // 根据角色确定更新的字段
            $updateData = [
                'is_read' => 1, // 保留原字段更新，向后兼容
                'read_time' => time(),
                'update_time' => time()
            ];
            
            // 根据角色添加特定字段
            switch ($roleType) {
                case 'customer':
                    $updateData['customer_read'] = 1;
                    $updateData['customer_read_time'] = time();
                    break;
                case 'merchant':
                    $updateData['merchant_read'] = 1;
                    $updateData['merchant_read_time'] = time();
                    break;
                case 'staff':
                default:
                    $updateData['staff_read'] = 1;
                    $updateData['staff_read_time'] = time();
                    break;
            }
            
            // 标记消息为已读
            $result = Db::name('plugin_chat_messages')
                ->where('id', $messageId)
                ->where('session_id', $sessionId)
                ->update($updateData);
            
            if ($result) {
                // 调用Hook更新缓存，确保实时性
                $hook = new \plugin\Customersystem\Hook();
                $hook->afterMessageRead($sessionId, $messageId);
                
                // 更新客户端消息列表显示
                $message = Db::name('plugin_chat_messages')->where('id', $messageId)->find();
                
                return $this->success('标记成功', [
                    'message_id' => $messageId,
                    'is_read' => 1,
                    'read_time' => $message ? $message['read_time'] : time(),
                    'role_type' => $roleType,
                    'customer_read' => $message ? $message['customer_read'] : 0,
                    'merchant_read' => $message ? $message['merchant_read'] : 0,
                    'staff_read' => $message ? $message['staff_read'] : 0
                ]);
            }
            
            return $this->error('标记失败，消息可能不存在');
        } catch (\Exception $e) {
            return $this->error('操作失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量标记消息为已读
     * 
     * @return \think\Response
     */
    public function markSessionMessagesRead()
    {
        $request = $this->request;
        $sessionId = $request->post('session_id/d', 0);
        $roleType = $request->post('role_type', ''); // 当前用户角色：customer, staff, merchant
        
        try {
            // 验证参数
            if (!$sessionId) {
                return $this->error('会话ID不能为空');
            }
            
            if (!$roleType) {
                return $this->error('角色类型不能为空');
            }
            
            // 获取会话信息
            $session = Db::name('plugin_chat_sessions')
                ->where('id', $sessionId)
                ->find();
                
            if (!$session) {
                return $this->error('会话不存在');
            }
            
            // 根据角色类型确定要更新的消息
            $where = [];
            $where[] = ['session_id', '=', $sessionId];
            
            // 设置更新数据和查询条件
            $updateData = [
                'is_read' => 1, 
                'read_time' => time(),
                'update_time' => time()
            ];

            switch ($roleType) {
                case 'customer':
                    // 客户查看，标记其他角色发送的消息为已读
                    $where[] = ['sender_type', '<>', 'customer'];
                    $updateData['customer_read'] = 1;
                    $updateData['customer_read_time'] = time();
                    break;
                    
                case 'merchant':
                    // 商家查看，标记非商家发送的消息为已读
                    $where[] = ['role_type', '<>', 'merchant'];
                    $updateData['merchant_read'] = 1;
                    $updateData['merchant_read_time'] = time();
                    break;
                    
                case 'staff':
                    // 客服查看，标记非客服发送的消息为已读
                    $where[] = ['role_type', '<>', 'staff'];
                    $updateData['staff_read'] = 1;
                    $updateData['staff_read_time'] = time();
                    break;
            }
            
            // 执行更新
            $affectedRows = Db::name('plugin_chat_messages')
                ->where($where)
                ->update($updateData);
            
            // 更新会话未读计数
            $this->updateSessionUnreadCountForRole($sessionId, $roleType);
            

            
            return $this->success('标记消息已读成功', [
                'affected_rows' => $affectedRows,
                'session_id' => $sessionId,
                'role_type' => $roleType
            ]);
        } catch (\Exception $e) {
            Log::error("标记消息已读失败: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return $this->error('标记消息已读失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新指定角色的会话未读计数
     * @param int $sessionId 会话ID
     * @param string $roleType 角色类型
     * @return bool 是否成功
     */
    protected function updateSessionUnreadCountForRole($sessionId, $roleType)
    {
        // 总未读计数
        $totalUnreadCount = Db::name('plugin_chat_messages')
            ->where('session_id', $sessionId)
            ->where('is_read', 0)
            ->count();
        
        // 针对角色的未读计数
        $roleSpecificField = '';
        $roleWhere = [];
        
        switch ($roleType) {
            case 'customer':
                $roleSpecificField = 'customer_unread';
                $roleWhere = [
                    ['session_id', '=', $sessionId],
                    ['sender_type', '<>', 'customer'],
                    ['customer_read', '=', 0]
                ];
                break;
                
            case 'merchant':
                $roleSpecificField = 'merchant_unread';
                $roleWhere = [
                    ['session_id', '=', $sessionId],
                    ['role_type', '<>', 'merchant'],
                    ['merchant_read', '=', 0]
                ];
                break;
                
            case 'staff':
                $roleSpecificField = 'staff_unread';
                $roleWhere = [
                    ['session_id', '=', $sessionId],
                    ['role_type', '<>', 'staff'],
                    ['staff_read', '=', 0]
                ];
                break;
        }
        
        // 计算特定角色的未读计数
        $roleUnreadCount = 0;
        if (!empty($roleWhere)) {
            $roleUnreadCount = Db::name('plugin_chat_messages')
                ->where($roleWhere)
                ->count();
        }
        
        // 更新会话未读计数
        $updateData = [
            'unread_count' => $totalUnreadCount,
            'update_time' => time()
        ];
        
        // 如果有角色特定字段，添加到更新数据中
        if (!empty($roleSpecificField)) {
            $updateData[$roleSpecificField] = $roleUnreadCount;
        }
        
        Db::name('plugin_chat_sessions')
            ->where('id', $sessionId)
            ->update($updateData);
        
        return true;
    }

    /**
     * 手动发送通知邮件
     */
    public function sendNotificationEmail()
    {
        try {
            // 获取请求参数（支持POST和GET）
            $post = $this->request->param();
            
            $sessionId = isset($post['session_id']) ? intval($post['session_id']) : 0;
            $subject = isset($post['subject']) ? $post['subject'] : '客服系统消息通知';
            $content = isset($post['content']) ? $post['content'] : '';
            

            
            if (empty($sessionId)) {
                return $this->error('会话ID不能为空');
            }
            
            // 获取会话和联系人信息
            $session = Db::name('plugin_chat_sessions')
                ->where('id', $sessionId)
                ->find();
                
            if (empty($session)) {
                return $this->error('会话不存在');
            }
            
            $contact = Db::name('plugin_chat_contacts')
                ->where('id', $session['contact_id'])
                ->find();
                
            if (empty($contact) || empty($contact['email'])) {
                return $this->error('联系人邮箱不存在');
            }
            
            $email = $contact['email'];
            
            // 如果内容为空，设置默认内容
            if (empty($content)) {
                $content = "尊敬的用户，您好！\n\n您有新的客服消息，请登录系统查看您的在线客服内容。\n\n如有任何问题，请随时联系我们。\n\n此致\n客服团队";
            }
            
            try {
                // 使用官方的EmailService发送邮件
                $service = new \app\common\service\EmailService();
                $res = $service->subject($subject)->message($content)->to($email)->send();
                
                if ($res) {
                    // 获取发送者ID
                    $senderId = 0; // 默认为系统
                    if ($this->adminInfo && isset($this->adminInfo['id'])) {
                        $senderId = $this->adminInfo['id'];
                    } elseif (session('?admin')) {
                        $adminInfo = session('admin');
                        $senderId = $adminInfo['id'] ?? 0;
                    }
                    
                    // 添加系统消息记录邮件发送
                    Db::name('plugin_chat_messages')->insert([
                        'session_id' => $sessionId,
                        'sender_type' => 'staff',
                        'sender_id' => $senderId,
                        'role_type' => 'staff', // 角色类型为客服
                        'message' => "已向客户发送邮件通知（{$subject}）",
                        'message_type' => 'text',
                        'is_read' => 0,
                        'create_time' => time(),
                        'update_time' => time(),
                    ]);
                    
                    return $this->success('邮件发送成功！');
                } else {
                    Log::error("邮件发送失败: " . $service->getError());
                    return $this->error($service->getError());
                }
            } catch (\Exception $e) {
                Log::error("发送邮件服务异常: " . $e->getMessage() . "\n" . $e->getTraceAsString());
                return $this->error('发送邮件异常: ' . $e->getMessage());
            }
        } catch (\Exception $e) {
            Log::error("发送邮件整体处理异常: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return $this->error('发送邮件失败: ' . $e->getMessage());
        }
    }

    /**
     * 给商家发送通知邮件
     */
    public function sendMerchantNotificationEmail()
    {
        try {
            // 获取请求参数
            $post = $this->request->param();
            $sessionId = isset($post['session_id']) ? intval($post['session_id']) : 0;
            

            
            if (empty($sessionId)) {
                return $this->error('会话ID不能为空');
            }
            
            // 获取会话信息
            $session = Db::name('plugin_chat_sessions')
                ->where('id', $sessionId)
                ->find();
                
            if (empty($session)) {
                return $this->error('会话不存在');
            }
            
            // 获取会话关联的商家ID
            $merchantId = $session['merchant_id'];
            if (empty($merchantId)) {
                return $this->error('该会话未关联商家');
            }
            
            // 从user_email_push表中查找商家的邮箱
            $merchantEmail = Db::name('user_email_push')
                ->where('user_id', $merchantId)
                ->value('email');
                
            if (empty($merchantEmail)) {
                return $this->error('商家未绑定通知邮箱');
            }

            // 获取商家名称
            $merchantName = Db::name('user')
                ->where('id', $merchantId)
                ->value('nickname');
            
            if (empty($merchantName)) {
                $merchantName = '商家#' . $merchantId;
            }
            
            // 获取联系人信息，用于邮件内容
            $contactInfo = '';
            if (!empty($session['contact_id'])) {
                $contact = Db::name('plugin_chat_contacts')
                    ->where('id', $session['contact_id'])
                    ->find();
                    
                if (!empty($contact)) {
                    $contactInfo = "联系人: {$contact['name']}";
                    if (!empty($contact['email'])) {
                        $contactInfo .= " / 邮箱: {$contact['email']}";
                    }
                    if (!empty($contact['phone'])) {
                        $contactInfo .= " / 电话: {$contact['phone']}";
                    }
                }
            }
            
            // 获取最新消息预览
            $latestMessage = Db::name('plugin_chat_messages')
                ->where('session_id', $sessionId)
                ->where('sender_type', 'customer')
                ->order('id desc')
                ->limit(1)
                ->value('message');
                
            $messageExcerpt = '';
            if (!empty($latestMessage)) {
                $plainMessage = strip_tags($latestMessage);
                $messageExcerpt = mb_substr($plainMessage, 0, 50, 'UTF-8');
                if (mb_strlen($plainMessage, 'UTF-8') > 50) {
                    $messageExcerpt .= '...';
                }
            }
            
            // 设置邮件标题和内容
            $title = '客户咨询: ' . ($contact['name'] ?? '访客') . ' ' . $messageExcerpt . ' (联系商家:' . $merchantName . ')';
            
            $content = "尊敬的{$merchantName}，您好！\n\n";
            $content .= "您有新的客户咨询，详情如下：\n\n";
            $content .= "会话ID: #{$sessionId}\n";
            $content .= "会话标题: {$session['title']}\n";
            $content .= "{$contactInfo}\n\n";
            
            if (!empty($messageExcerpt)) {
                $content .= "最新消息: {$messageExcerpt}\n\n";
            }
            
            $content .= "请登录系统查看并及时回复客户咨询。\n\n";
            $content .= "此致\n客服系统";
            
            try {
                // 使用官方的EmailService发送邮件
                $service = new \app\common\service\EmailService();
                $res = $service->subject($title)->message($content)->to($merchantEmail)->send();
                
                if ($res) {
                    // 获取发送者ID
                    $senderId = 0; // 默认为系统
                    if ($this->adminInfo && isset($this->adminInfo['id'])) {
                        $senderId = $this->adminInfo['id'];
                    } elseif (session('?admin')) {
                        $adminInfo = session('admin');
                        $senderId = $adminInfo['id'] ?? 0;
                    }
                    
                    // 添加系统消息记录邮件发送
                    Db::name('plugin_chat_messages')->insert([
                        'session_id' => $sessionId,
                        'sender_type' => 'staff',
                        'sender_id' => $senderId,
                        'role_type' => 'staff', // 角色类型为客服
                        'message' => "已向商家 {$merchantName} 发送邮件通知",
                        'message_type' => 'text',
                        'is_read' => 0,
                        'create_time' => time(),
                        'update_time' => time(),
                    ]);
                    
                    return $this->success('邮件发送成功！');
                } else {
                    Log::error("邮件发送失败: " . $service->getError());
                    return $this->error($service->getError());
                }
            } catch (\Exception $e) {
                Log::error("发送商家邮件服务异常: " . $e->getMessage() . "\n" . $e->getTraceAsString());
                return $this->error('发送邮件异常: ' . $e->getMessage());
            }
        } catch (\Exception $e) {
            Log::error("发送商家邮件整体处理异常: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return $this->error('发送邮件失败: ' . $e->getMessage());
        }
    }

    /**
     * 切换前台插件JS加载状态
     */
    public function toggleQiantaiJs()
    {
        try {
            $enabled = (bool)$this->request->post('enabled', false);
            
            // 目录和文件路径配置
            $sourceFile = root_path() . 'plugin/Customersystem/static/qiantai.js';
            $targetDir = root_path() . 'public/assets/plugin/Customersystem/';
            $targetFile = $targetDir . 'qiantai.js';
            
            if ($enabled) {
                // 创建目标目录（如果不存在）
                if (!is_dir($targetDir)) {
                    mkdir($targetDir, 0755, true);
                }
                
                // 检查源文件是否存在
                if (!file_exists($sourceFile)) {
                    return $this->error('源JS文件不存在: ' . $sourceFile);
                }
                
                // 复制JS文件
                if (!copy($sourceFile, $targetFile)) {
                    return $this->error('复制JS文件失败');
                }
                
                // 更新系统设置
                $params = $this->getParamsData();
                
                // 设置启用状态
                if (!isset($params['settings'])) {
                    $params['settings'] = [];
                }
                $params['settings']['qiantai_js_enabled'] = true;
                
                // 保存设置
                $paramsFile = __DIR__ . '/../params.php';
                file_put_contents($paramsFile, "<?php\n\nreturn " . var_export($params, true) . ";\n");
                
                return $this->success('JS功能已启用');
            } else {
                // 禁用功能
                
                // 删除JS文件
                if (file_exists($targetFile)) {
                    if (!unlink($targetFile)) {
                        return $this->error('删除JS文件失败');
                    }
                }
                
                // 更新系统设置
                $params = $this->getParamsData();
                
                // 设置禁用状态
                if (!isset($params['settings'])) {
                    $params['settings'] = [];
                }
                $params['settings']['qiantai_js_enabled'] = false;
                
                // 保存设置
                $paramsFile = __DIR__ . '/../params.php';
                file_put_contents($paramsFile, "<?php\n\nreturn " . var_export($params, true) . ";\n");
                
                return $this->success('JS功能已禁用');
            }
        } catch (\Exception $e) {
            return $this->error('操作失败: ' . $e->getMessage());
        }
    }

    /**
     * 计算两个字符串的相似度
     * 
     * @param string $str1 第一个字符串
     * @param string $str2 第二个字符串
     * @return float 相似度，范围0-1
     */
    private function calculateSimilarity($str1, $str2)
    {
        // 将字符串转换为小写并去除标点
        $str1 = $this->cleanString($str1);
        $str2 = $this->cleanString($str2);
        
        // 如果完全相同，返回1
        if ($str1 === $str2) {
            return 1.0;
        }
        
        // 如果一个为空，返回0
        if (empty($str1) || empty($str2)) {
            return 0.0;
        }
        
        // 计算最长公共子序列长度
        $len1 = mb_strlen($str1);
        $len2 = mb_strlen($str2);
        $lcs = $this->lcs($str1, $str2);
        
        // 相似度计算
        return (2 * $lcs) / ($len1 + $len2);
    }

    /**
     * 清理字符串，去除标点和多余空格，转换为小写
     * 
     * @param string $string 输入字符串
     * @return string 清理后的字符串
     */
    private function cleanString($string)
    {
        // 转为小写
        $string = mb_strtolower($string);
        
        // 去除标点符号
        $string = preg_replace('/[^\p{L}\p{N}\s]/u', '', $string);
        
        // 替换多个空格为单个空格
        $string = preg_replace('/\s+/', ' ', $string);
        
        // 去除首尾空格
        return trim($string);
    }

    /**
     * 计算最长公共子序列长度
     * 
     * @param string $str1 第一个字符串
     * @param string $str2 第二个字符串
     * @return int 最长公共子序列长度
     */
    private function lcs($str1, $str2)
    {
        $len1 = mb_strlen($str1);
        $len2 = mb_strlen($str2);
        
        $matrix = array();
        
        // 初始化第一行和第一列
        for ($i = 0; $i <= $len1; $i++) {
            $matrix[$i][0] = 0;
        }
        
        for ($j = 0; $j <= $len2; $j++) {
            $matrix[0][$j] = 0;
        }
        
        // 填充矩阵
        for ($i = 1; $i <= $len1; $i++) {
            for ($j = 1; $j <= $len2; $j++) {
                if (mb_substr($str1, $i - 1, 1) == mb_substr($str2, $j - 1, 1)) {
                    $matrix[$i][$j] = $matrix[$i - 1][$j - 1] + 1;
                } else {
                    $matrix[$i][$j] = max($matrix[$i - 1][$j], $matrix[$i][$j - 1]);
                }
            }
        }
        
        return $matrix[$len1][$len2];
    }

    /**
     * 根据咨询类型发送邮件通知
     * @param string $source 会话来源 ('customer' 或 'merchant')
     * @param int $merchantId 商家ID
     * @param string $customerName 客户姓名
     * @param string $customerEmail 客户邮箱
     * @param string $message 咨询消息
     * @param int $sessionId 会话ID
     */
    private function sendNotificationByType($source, $merchantId, $customerName, $customerEmail, $message, $sessionId)
    {
        try {
            if ($source === 'merchant' && $merchantId > 0) {
                // 咨询店铺商家 - 发送邮件给商家
                $this->sendMerchantEmailNotification($merchantId, $customerName, $customerEmail, $message, $sessionId);
            } else {
                // 咨询平台客服 - 发送邮件给平台客服
                $this->sendPlatformServiceEmailNotification($customerName, $customerEmail, $message, $sessionId);
            }
        } catch (\Exception $e) {
            Log::error('发送邮件通知异常: ' . $e->getMessage());
        }
    }

    /**
     * 发送商家邮件通知
     * @param int $merchantId 商家ID
     * @param string $customerName 客户姓名
     * @param string $customerEmail 客户邮箱
     * @param string $message 咨询消息
     * @param int $sessionId 会话ID
     */
    private function sendMerchantEmailNotification($merchantId, $customerName, $customerEmail, $message, $sessionId)
    {
        try {
            // 获取商家基本信息
            $merchant = Db::name('user')->where('id', $merchantId)->find();
            if (empty($merchant)) {
                return;
            }

            // 从user_email_push表中获取商家的邮箱地址
            $merchantEmail = Db::name('user_email_push')
                ->where('user_id', $merchantId)
                ->value('email');

            if (empty($merchantEmail)) {
                return;
            }

            // 构建邮件内容
            $subject = '新客户咨询通知';
            $content = "尊敬的 {$merchant['username']} 用户，您好！\n\n";
            $content .= "您有一位新客户开始咨询您的商品，详情如下：\n\n";
            $content .= "客户姓名：{$customerName}\n";
            $content .= "客户邮箱：{$customerEmail}\n";
            $content .= "会话ID：{$sessionId}\n";
            $content .= "咨询内容：{$message}\n\n";
            $content .= "请及时登录客服系统查看并回复客户的咨询。\n\n";
            $content .= "如有任何问题，请随时联系我们。\n\n";
            $content .= "此致\n客服系统";



            // 使用官方的EmailService发送邮件
            $service = new \app\common\service\EmailService();
            $res = $service->subject($subject)
                ->message($content)
                ->to($merchantEmail)
                ->send();

            if ($res) {

                // 添加系统消息记录邮件发送
                Db::name('plugin_chat_messages')->insert([
                    'session_id' => $sessionId,
                    'sender_type' => 'staff',
                    'sender_id' => 0,
                    'role_type' => 'staff',
                    'message' => "系统已向商家发送邮件通知（{$subject}）",
                    'message_type' => 'text',
                    'is_read' => 0,
                    'is_system' => 1,
                    'create_time' => time(),
                    'update_time' => time(),
                ]);
            } else {
                Log::error("商家邮件通知发送失败: " . $service->getError());
            }
        } catch (\Exception $e) {
            Log::error('发送商家邮件通知异常: ' . $e->getMessage());
        }
    }

    /**
     * 发送平台客服邮件通知
     * @param string $customerName 客户姓名
     * @param string $customerEmail 客户邮箱
     * @param string $message 咨询消息
     * @param int $sessionId 会话ID
     */
    private function sendPlatformServiceEmailNotification($customerName, $customerEmail, $message, $sessionId)
    {
        try {
            // 获取平台客服邮箱配置
            $params = $this->getParamsData();
            $platformEmail = $params['notification_config']['platform_service_email'] ?? '';

            if (empty($platformEmail)) {
                Log::info("平台客服邮箱未配置，跳过邮件通知");
                return;
            }

            // 构建邮件内容
            $subject = '新客户咨询平台客服通知';
            $content = "尊敬的平台客服，您好！\n\n";
            $content .= "有新客户咨询平台客服，详情如下：\n\n";
            $content .= "客户姓名：{$customerName}\n";
            $content .= "客户邮箱：{$customerEmail}\n";
            $content .= "会话ID：{$sessionId}\n";
            $content .= "咨询内容：{$message}\n\n";
            $content .= "请及时登录客服系统查看并回复客户的咨询。\n\n";
            $content .= "如有任何问题，请随时联系我们。\n\n";
            $content .= "此致\n客服系统";

            Log::info("准备向平台客服 ({$platformEmail}) 发送新客户咨询邮件通知");

            // 使用官方的EmailService发送邮件
            $service = new \app\common\service\EmailService();
            $res = $service->subject($subject)
                ->message($content)
                ->to($platformEmail)
                ->send();

            if ($res) {
                Log::info("平台客服邮件通知发送成功: {$platformEmail}");

                // 添加系统消息记录邮件发送
                Db::name('plugin_chat_messages')->insert([
                    'session_id' => $sessionId,
                    'sender_type' => 'staff',
                    'sender_id' => 0,
                    'role_type' => 'staff',
                    'message' => "系统已向平台客服发送邮件通知（{$subject}）",
                    'message_type' => 'text',
                    'is_read' => 0,
                    'is_system' => 1,
                    'create_time' => time(),
                    'update_time' => time(),
                ]);
            } else {
                Log::error("平台客服邮件通知发送失败: " . $service->getError());
            }
        } catch (\Exception $e) {
            Log::error('发送平台客服邮件通知异常: ' . $e->getMessage());
        }
    }

    /**
     * 获取真实IP地址，兼容CDN情况
     * @return string
     */
    private function real_ip()
    {
        // 优先级顺序的HTTP头列表
        $headers = [
            'HTTP_X_FORWARDED_FOR',     // 最常用的代理头
            'HTTP_X_REAL_IP',           // Nginx real_ip模块
            'HTTP_CLIENT_IP',           // 代理服务器发送的原始用户IP
            'HTTP_X_CLUSTER_CLIENT_IP', // 集群环境
            'HTTP_FORWARDED_FOR',       // RFC 7239标准
            'HTTP_FORWARDED',           // RFC 7239标准
            'HTTP_CF_CONNECTING_IP',    // Cloudflare
            'HTTP_X_ORIGINAL_FORWARDED_FOR', // 原始转发
        ];

        foreach ($headers as $header) {
            if (!empty($_SERVER[$header])) {
                $ips = explode(',', $_SERVER[$header]);
                $ip = trim($ips[0]); // 取第一个IP

                // 验证IP格式
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }

                // 如果第一个IP是私有IP，尝试其他IP
                foreach ($ips as $candidate) {
                    $candidate = trim($candidate);
                    if (filter_var($candidate, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                        return $candidate;
                    }
                }
            }
        }

        // 如果没有找到有效的公网IP，返回REMOTE_ADDR
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }

    /**
     * 获取实时消息更新
     * 供前端轮询调用，获取新消息和通知
     *
     * @return \think\Response
     */
    public function getRealTimeUpdates()
    {
        try {
            // 简化查询，避免JOIN可能的问题
            $activeSessionsCount = Db::name('plugin_chat_sessions')
                ->where('status', 1)
                ->count();

            // 检查最近5分钟内是否有新消息
            $recentTime = time() - 300;
            $hasNewMessages = false;

            try {
                $newMessageCount = Db::name('plugin_chat_messages')
                    ->where('create_time', '>', $recentTime)
                    ->count();
                $hasNewMessages = $newMessageCount > 0;
            } catch (\Exception $e) {
                // 如果消息表查询失败，默认为false
                $hasNewMessages = false;
            }

            $result = [
                'timestamp' => time(),
                'has_new_messages' => $hasNewMessages,
                'active_sessions_count' => $activeSessionsCount,
                'last_activity' => $hasNewMessages ? time() : 0,
                'notifications' => []
            ];

            return $this->success($result);

        } catch (\Exception $e) {
            // 返回基本的成功响应，避免500错误
            $result = [
                'timestamp' => time(),
                'has_new_messages' => false,
                'active_sessions_count' => 0,
                'last_activity' => 0,
                'notifications' => [],
                'error' => $e->getMessage()
            ];

            return $this->success($result);
        }
    }

    /**
     * 获取指定会话的实时消息
     *
     * @return \think\Response
     */
    public function getSessionRealTimeMessages()
    {
        $sessionId = input('session_id', 0);
        $lastMessageId = input('last_message_id', 0);

        if (!$sessionId) {
            return $this->error('会话ID不能为空');
        }

        try {
            // 直接查询数据库获取消息
            $query = Db::name('plugin_chat_messages')
                ->where('session_id', $sessionId)
                ->order('create_time', 'asc');

            // 如果指定了最后消息ID，只返回更新的消息
            if ($lastMessageId > 0) {
                $query->where('id', '>', $lastMessageId);
            }

            $messages = $query->select()->toArray();

            $result = [
                'session_id' => $sessionId,
                'messages' => $messages,
                'message_count' => count($messages),
                'timestamp' => time()
            ];

            return $this->success($result);

        } catch (\Exception $e) {
            Log::error("获取会话实时消息失败: " . $e->getMessage() . " 文件: " . $e->getFile() . " 行号: " . $e->getLine());
            return $this->error('获取会话实时消息失败: ' . $e->getMessage());
        }
    }

    /**
     * 标记会话消息为已读并清除通知
     *
     * @return \think\Response
     */
    public function markSessionAsRead()
    {
        $sessionId = input('session_id', 0);

        if (!$sessionId) {
            return $this->error('会话ID不能为空');
        }

        try {
            $hook = new \plugin\Customersystem\Hook();

            // 清除新消息通知
            $hook->clearNewMessageNotification($sessionId);

            // 批量标记消息为已读
            $hook->afterBatchMessageRead($sessionId, 'staff');

            return $this->success('标记成功');

        } catch (\Exception $e) {
            Log::error("标记会话已读失败: " . $e->getMessage());
            return $this->error('标记会话已读失败');
        }
    }

    /**
     * 获取客户端当前IP地址
     */
    public function getCurrentIP()
    {
        try {
            // 使用官方real_ip()方法获取真实IP，兼容CDN情况
            $clientIP = real_ip();

            return $this->success('获取IP成功', [
                'ip' => $clientIP,
                'user_agent' => $this->request->header('User-Agent', ''),
                'time' => date('Y-m-d H:i:s')
            ]);

        } catch (\Exception $e) {
            Log::error("获取客户端IP失败: " . $e->getMessage());
            return $this->error('获取IP地址失败: ' . $e->getMessage());
        }
    }

    /**
     * 查询IP地址位置信息
     */
    public function queryIPLocation()
    {
        try {
            $ip = $this->request->param('ip/s', '');

            if (empty($ip)) {
                return $this->error('IP地址不能为空');
            }

            // 验证IP格式
            if (!filter_var($ip, FILTER_VALIDATE_IP)) {
                return $this->error('IP地址格式不正确');
            }

            // 调用ai.cesu.net的IP查询接口
            $url = 'https://ai.cesu.net/api/query-ip';
            $data = json_encode(['ip' => $ip]);

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Accept: application/json',
                'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                Log::error("IP查询请求失败: " . $error);
                return $this->error('IP查询服务暂时不可用');
            }

            if ($httpCode !== 200) {
                Log::error("IP查询返回错误状态码: " . $httpCode);
                return $this->error('IP查询服务返回错误');
            }

            $result = json_decode($response, true);

            if (!$result) {
                Log::error("IP查询返回数据解析失败: " . $response);
                return $this->error('IP查询结果解析失败');
            }

            // 提取需要的字段
            $locationData = [
                'ip' => $result['ip'] ?? $ip,
                'country' => $result['country'] ?? '',
                'province' => $result['province'] ?? '',
                'city' => $result['city'] ?? '',
                'isp' => $result['isp'] ?? '',
                'location_string' => ''
            ];

            // 组合位置字符串
            $locationParts = array_filter([
                $locationData['country'],
                $locationData['province'],
                $locationData['city']
            ]);

            $locationData['location_string'] = implode(' ', $locationParts);

            if (empty($locationData['location_string'])) {
                $locationData['location_string'] = '未知地区';
            }

            return $this->success('IP位置查询成功', $locationData);

        } catch (\Exception $e) {
            Log::error("查询IP位置失败: " . $e->getMessage());
            return $this->error('查询IP位置失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新客户IP地址（私有方法）
     */
    private function updateCustomerIP($contactId)
    {
        try {
            // 获取当前真实IP
            $currentIP = real_ip();

            if (empty($currentIP)) {
                return false;
            }

            // 获取联系人当前IP
            $contact = Db::name('plugin_chat_contacts')
                ->where('id', $contactId)
                ->find();

            if (empty($contact)) {
                return false;
            }

            // 如果IP没有变化，不需要更新
            if ($contact['ip'] === $currentIP) {
                return true;
            }

            // 更新联系人IP地址
            $updateResult = Db::name('plugin_chat_contacts')
                ->where('id', $contactId)
                ->update([
                    'ip' => $currentIP,
                    'update_time' => time()
                ]);

            if ($updateResult) {
                // 记录IP变化日志
                Log::info("客户聊天时IP地址自动更新", [
                    'contact_id' => $contactId,
                    'old_ip' => $contact['ip'],
                    'new_ip' => $currentIP,
                    'user_agent' => $this->request->header('User-Agent', ''),
                    'time' => date('Y-m-d H:i:s'),
                    'trigger' => 'chat_message'
                ]);

                return true;
            }

            return false;

        } catch (\Exception $e) {
            Log::error("自动更新客户IP失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取会话基本信息（包括最新IP）
     */
    public function getSessionInfo()
    {
        try {
            $sessionId = $this->request->param('session_id/d', 0);

            if (empty($sessionId)) {
                return $this->error('会话ID不能为空');
            }

            // 获取会话信息
            $session = Db::name('plugin_chat_sessions')
                ->where('id', $sessionId)
                ->find();

            if (empty($session)) {
                return $this->error('会话不存在');
            }

            // 获取联系人信息
            $contact = Db::name('plugin_chat_contacts')
                ->where('id', $session['contact_id'])
                ->find();

            if (empty($contact)) {
                return $this->error('联系人不存在');
            }

            // 如果是客户端请求，自动更新IP
            $senderType = $this->request->param('sender_type/s', '');
            if ($senderType === 'customer') {
                $this->updateCustomerIP($session['contact_id']);

                // 重新获取更新后的联系人信息
                $contact = Db::name('plugin_chat_contacts')
                    ->where('id', $session['contact_id'])
                    ->find();
            }

            return $this->success('获取会话信息成功', [
                'session' => $session,
                'contact' => $contact,
                'ip_updated' => $senderType === 'customer'
            ]);

        } catch (\Exception $e) {
            Log::error("获取会话信息失败: " . $e->getMessage());
            return $this->error('获取会话信息失败: ' . $e->getMessage());
        }
    }

    /**
     * 清空会话聊天记录
     *
     * @return \think\Response
     */
    public function clearChatMessages()
    {
        $request = $this->request;
        $sessionId = $request->post('session_id/d', 0);

        try {
            // 验证参数
            if (!$sessionId) {
                return $this->error('会话ID不能为空');
            }

            // 验证会话是否存在
            $session = Db::name('plugin_chat_sessions')
                ->where('id', $sessionId)
                ->find();

            if (!$session) {
                return $this->error('会话不存在');
            }

            // 开启事务
            Db::startTrans();

            try {
                // 删除会话中的所有消息
                $deletedCount = Db::name('plugin_chat_messages')
                    ->where('session_id', $sessionId)
                    ->delete();

                // 更新会话信息
                Db::name('plugin_chat_sessions')
                    ->where('id', $sessionId)
                    ->update([
                        'last_message' => '',
                        'last_time' => time(),
                        'unread_count' => 0,
                        'customer_unread' => 0,
                        'merchant_unread' => 0,
                        'staff_unread' => 0,
                        'update_time' => time()
                    ]);

                // 提交事务
                Db::commit();

                // 调用Hook清理缓存
                $hook = new \plugin\Customersystem\Hook();
                $hook->afterClearMessages($sessionId);

                return $this->success('聊天记录已清空', [
                    'session_id' => $sessionId,
                    'deleted_count' => $deletedCount
                ]);

            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::error('清空聊天记录失败: ' . $e->getMessage());
            return $this->error('清空聊天记录失败: ' . $e->getMessage());
        }
    }
}