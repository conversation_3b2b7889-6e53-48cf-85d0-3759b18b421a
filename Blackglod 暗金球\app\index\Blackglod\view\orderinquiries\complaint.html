<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$title}</title>
    {if !empty($favicon)}
    <link rel="shortcut icon" href="{$favicon}" type="image/x-icon">
    {/if}
    <link rel="stylesheet" href="/assets/plugin/Blackglod/plugin/Blackglod/css/all.min.css">
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <style>
        /* 复用之前的基础样式 */
        :root {
            --primary: #C8A675;
            --primary-dark: #a88a5c;
            --primary-light: #e0c4a3;
            --dark: #000000;
            --light: #ffffff;
            --gray: #666666;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            font-family: 'Noto <PERSON> SC', sans-serif;
            background-color: rgba(0, 0, 0, 0.85);
            color: var(--light);
            line-height: 1.6;
        }

        .complaint-section {
            padding-top: 120px;
            min-height: 100vh;
        }

        .complaint-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
        }

        .complaint-box {
            background: rgba(0, 0, 0, 0.6);
            border-radius: 16px;
            padding: 30px;
            border: 1px solid rgba(200, 166, 117, 0.1);
            backdrop-filter: blur(10px);
        }

        .complaint-title {
            font-size: 1.8rem;
            color: var(--primary);
            margin-bottom: 2rem;
            text-align: center;
        }

        .order-info {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 2rem;
        }

        .order-info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(200, 166, 117, 0.1);
        }

        .order-info-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .label {
            color: var(--gray);
        }

        .value {
            color: var(--light);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            color: var(--primary);
            margin-bottom: 0.5rem;
        }

        .complaint-type {
            width: 100%;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(200, 166, 117, 0.2);
            border-radius: 8px;
            color: var(--light);
            transition: var(--transition);
        }

        .complaint-type:focus {
            border-color: var(--primary);
            outline: none;
        }

        .complaint-content {
            width: 100%;
            min-height: 150px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(200, 166, 117, 0.2);
            border-radius: 8px;
            color: var(--light);
            resize: vertical;
            transition: var(--transition);
        }

        .complaint-content:focus {
            border-color: var(--primary);
            outline: none;
        }

        .upload-section {
            margin-top: 1rem;
        }

        .upload-title {
            color: var(--gray);
            margin-bottom: 1rem;
        }

        .upload-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 1rem;
        }

        .upload-item {
            aspect-ratio: 1;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
            border: 1px dashed rgba(200, 166, 117, 0.2);
        }

        .upload-item:hover {
            border-color: var(--primary);
        }

        .submit-btn {
            width: 100%;
            padding: 12px;
            background: var(--primary);
            color: var(--dark);
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: var(--transition);
            margin-top: 2rem;
        }

        .submit-btn:hover {
            background: var(--primary-dark);
        }

        .notice {
            margin-top: 1rem;
            color: var(--gray);
            font-size: 0.9rem;
            text-align: center;
        }

        /* 下拉菜单样式 */
        .complaint-type-select {
            position: relative;
        }

        .type-options {
            position: absolute;
            top: 100%;
            left: 0;
            width: 100%;
            background: rgba(0, 0, 0, 0.9);
            border: 1px solid rgba(200, 166, 117, 0.2);
            border-radius: 8px;
            margin-top: 4px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .type-option {
            padding: 10px;
            cursor: pointer;
            transition: var(--transition);
        }

        .type-option:hover {
            background: rgba(200, 166, 117, 0.1);
            color: var(--primary);
        }

        .complaint-type-select.active .type-options {
            display: block;
        }

        /* 字数统计 */
        .word-count {
            text-align: right;
            color: var(--gray);
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }

        /* 返回按钮 */
        .back-btn {
            display: inline-flex;
            align-items: center;
            color: var(--primary);
            text-decoration: none;
            margin-bottom: 2rem;
            transition: var(--transition);
        }

        .back-btn:hover {
            color: var(--primary-light);
        }

        .back-btn i {
            margin-right: 0.5rem;
        }
    </style>
</head>
<body>
    <section class="complaint-section">
        <div class="complaint-container">
            <a href="javascript:history.back()" class="back-btn">
                <i class="fas fa-arrow-left"></i> 返回上一页
            </a>
            
            <div class="complaint-box">
                <h1 class="complaint-title">订单投诉</h1>
                
                <div class="order-info">
                    <div class="order-info-item">
                        <span class="label">订单号</span>
                        <span class="value">{$order.trade_no}</span>
                    </div>
                    <div class="order-info-item">
                        <span class="label">商品名称</span>
                        <span class="value">{$order.goods_name}</span>
                    </div>
                    <div class="order-info-item">
                        <span class="label">订单金额</span>
                        <span class="value">￥{$order.total_amount}</span>
                    </div>
                    <div class="order-info-item">
                        <span class="label">下单时间</span>
                        <span class="value">{:date('Y-m-d H:i:s', $order.create_time)}</span>
                    </div>
                </div>

                <form id="complaintForm" onsubmit="return false;">
                    <div class="form-group">
                        <label class="form-label">投诉类型</label>
                        <div class="complaint-type-select">
                            <input type="text" class="complaint-type" id="complaintType" readonly placeholder="请选择投诉类型" onclick="toggleTypeOptions()">
                            <div class="type-options">
                                {foreach $complaintTypes as $type}
                                <div class="type-option" onclick="selectType('{$type.value}')">{$type.label}</div>
                                {/foreach}
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">投诉说明</label>
                        <textarea class="complaint-content" id="complaintContent" placeholder="请详细描述您遇到的问题..." oninput="updateWordCount()"></textarea>
                        <div class="word-count">0/200</div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">上传凭证</label>
                        <div class="upload-container">
                            <div class="upload-item" onclick="triggerFileUpload()">
                                <i class="fas fa-plus"></i>
                            </div>
                        </div>
                        <input type="file" id="fileUpload" style="display: none" accept="image/*" multiple onchange="handleFileUpload(this)">
                        <div class="upload-title">最多上传3张图片，每张不超过2M</div>
                    </div>

                    <button class="submit-btn" onclick="submitComplaint()">提交投诉</button>

                    <div class="notice">
                        <i class="fas fa-info-circle"></i>
                        提交投诉后，客服将在24小时内处理您的问题
                    </div>
                </form>
            </div>
        </div>
    </section>

    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script>
        // 投诉类型选择
        function toggleTypeOptions() {
            document.querySelector('.complaint-type-select').classList.toggle('active');
        }

        function selectType(type) {
            document.getElementById('complaintType').value = type;
            document.querySelector('.complaint-type-select').classList.remove('active');
        }

        // 点击其他地方关闭下拉菜单
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.complaint-type-select')) {
                document.querySelector('.complaint-type-select').classList.remove('active');
            }
        });

        // 字数统计
        function updateWordCount() {
            const content = document.getElementById('complaintContent').value;
            const count = content.length;
            document.querySelector('.word-count').textContent = `${count}/200`;
        }

        // 文件上传
        function triggerFileUpload() {
            document.getElementById('fileUpload').click();
        }

        function handleFileUpload(input) {
            const files = input.files;
            const container = document.querySelector('.upload-container');
            const existingImages = container.querySelectorAll('img').length;

            if (existingImages + files.length > 3) {
                ElementPlus.ElMessage.warning('最多只能上传3张图片');
                return;
            }

            for (let file of files) {
                if (file.size > 2 * 1024 * 1024) {
                    ElementPlus.ElMessage.warning('图片大小不能超过2M');
                    continue;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    const div = document.createElement('div');
                    div.className = 'upload-item';
                    div.innerHTML = `
                        <img src="${e.target.result}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 8px;">
                        <span onclick="removeImage(this)" style="position: absolute; top: -8px; right: -8px; background: rgba(0,0,0,0.5); border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; cursor: pointer;">
                            <i class="fas fa-times"></i>
                        </span>
                    `;
                    container.insertBefore(div, container.firstChild);
                };
                reader.readAsDataURL(file);
            }
        }

        function removeImage(element) {
            element.parentElement.remove();
        }

        // 提交投诉
        function submitComplaint() {
            const type = document.getElementById('complaintType').value;
            const content = document.getElementById('complaintContent').value;
            const images = Array.from(document.querySelectorAll('.upload-container img')).map(img => img.src);

            if (!type) {
                ElementPlus.ElMessage.warning('请选择投诉类型');
                return;
            }

            if (!content) {
                ElementPlus.ElMessage.warning('请填写投诉说明');
                return;
            }

            if (content.length > 200) {
                ElementPlus.ElMessage.warning('投诉说明不能超过200字');
                return;
            }

            // 发送投诉请求
            fetch('/index/orderinquiries/submitComplaint', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    trade_no: '{$order.trade_no}',
                    type: type,
                    content: content,
                    images: images
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    ElementPlus.ElMessage.success('投诉提交成功');
                    setTimeout(() => {
                        window.location.href = '/orderinquiries';
                    }, 1500);
                } else {
                    ElementPlus.ElMessage.error(data.msg || '提交失败，请稍后重试');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                ElementPlus.ElMessage.error('系统错误，请稍后重试');
            });
        }
    </script>
</body>
</html> 