<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <meta http-equiv="Cache-Control" content="max-age=31536000">
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>开启弹窗</title>
    <style>
        /* 加载动画样式 */
        #loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #fff;
            z-index: 9999;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(0, 0, 0, 0.1);
            border-top-color: #3498db;
            border-radius: 50%;
            animation: spin 1s ease infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    </style>
</head>

<body>
<!-- 加载动画容器 -->
<div id="loading">
    <div class="spinner"></div>
</div>

<!-- Vue 应用挂载点 -->
<div id="app" style="display: none">

    <el-card shadow="never">

        <el-form :model="form" label-width="auto">
            <el-form-item label="是否要开启店铺弹窗">
                <el-radio-group v-model="form.status">
                    <el-radio :value="0">关闭</el-radio>
                    <el-radio :value="1">开启</el-radio>
                </el-radio-group>
            </el-form-item>
        </el-form>
    </el-card>

</div>

<script src="/static/others/vue/vue.global.prod.js"></script>
<script src="/static/others/element-plus/index.full.min.js"></script>
<script src="/static/others/axios/axios.min.js"></script>

<script type="module">
    const { ref, reactive } = Vue;
    const { ElMessage } = ElementPlus;

    const isLoading = ref(false);

    const form = reactive({
        status: 0,
    });

    // 获取数据
    const fetchData = async () => {
        isLoading.value = true;
        try {
            const res = await axios.post("/plugin/Shoppopup/api/fetchData");
            isLoading.value = false;
            if (res.data?.code == 200) {
                form.status = res.data?.data?.status ?? 0;
            } else {
                ElMessage.error(res.data?.msg);
            }
        } catch (error) {
            isLoading.value = false;
            ElMessage.error(error.message || '请求失败');
        }
    };

    fetchData();

    // 提交保存数据
    const save = async () => {
        isLoading.value = true;
        try {
            const res = await axios.post("/plugin/Shoppopup/api/save", form);
            isLoading.value = false;
            if (res.data?.code == 200) {
                ElMessage.success('保存成功');
            } else {
                ElMessage.error(res.data?.msg);
            }
        } catch (error) {
            isLoading.value = false;
            ElMessage.error(error.message || '请求失败');
        }
    };

    // Vue 应用
    const app = Vue.createApp({
        setup() {
            return {
                isLoading,
                form,
                save
            };
        },
    });

    app.use(ElementPlus);
    app.mount("#app");

    // 移除加载动画
    window.onload = () => {
        document.getElementById("loading").style.display = "none"; // 隐藏加载动画
        document.getElementById("app").style.display = "block"; // 显示 Vue 应用
    };
</script>
</body>
</html>
