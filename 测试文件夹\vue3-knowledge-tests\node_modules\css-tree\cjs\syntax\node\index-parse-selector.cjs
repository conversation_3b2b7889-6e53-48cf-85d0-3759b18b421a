'use strict';

const AnPlusB = require('./AnPlusB.cjs');
const AttributeSelector = require('./AttributeSelector.cjs');
const ClassSelector = require('./ClassSelector.cjs');
const Combinator = require('./Combinator.cjs');
const Identifier = require('./Identifier.cjs');
const IdSelector = require('./IdSelector.cjs');
const NestingSelector = require('./NestingSelector.cjs');
const Nth = require('./Nth.cjs');
const Percentage = require('./Percentage.cjs');
const PseudoClassSelector = require('./PseudoClassSelector.cjs');
const PseudoElementSelector = require('./PseudoElementSelector.cjs');
const Raw = require('./Raw.cjs');
const Selector = require('./Selector.cjs');
const SelectorList = require('./SelectorList.cjs');
const String = require('./String.cjs');
const TypeSelector = require('./TypeSelector.cjs');



exports.AnPlusB = AnPlusB.parse;
exports.AttributeSelector = AttributeSelector.parse;
exports.ClassSelector = ClassSelector.parse;
exports.Combinator = Combinator.parse;
exports.Identifier = Identifier.parse;
exports.IdSelector = IdSelector.parse;
exports.NestingSelector = NestingSelector.parse;
exports.Nth = Nth.parse;
exports.Percentage = Percentage.parse;
exports.PseudoClassSelector = PseudoClassSelector.parse;
exports.PseudoElementSelector = PseudoElementSelector.parse;
exports.Raw = Raw.parse;
exports.Selector = Selector.parse;
exports.SelectorList = SelectorList.parse;
exports.String = String.parse;
exports.TypeSelector = TypeSelector.parse;
