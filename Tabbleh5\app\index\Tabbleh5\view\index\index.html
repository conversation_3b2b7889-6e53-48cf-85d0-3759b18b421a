<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="description" content="{$siteSubtitle}">
    <meta name="keywords" content="卡密,查询,订单,服务,{$siteName}">
    <meta name="theme-color" content="#4361ee">
    <title>{$title}</title>
    
    {notempty name="favicon"}
    <link rel="icon" href="{$favicon}" type="image/x-icon">
    <link rel="shortcut icon" href="{$favicon}" type="image/x-icon">
    {/notempty}
    
    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://cdn.tailwindcss.com">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- 关键CSS先内联，避免渲染阻塞 -->
    <style>
        /* 最小化的初始样式，确保页面加载时有基本外观 */
        :root {
            --primary-color: #4361ee;
            --primary-dark: #3a5efd;
            --primary-light: #8b9cff;
            --secondary-color: #f44f61;
            --secondary-dark: #e63a4d;
            --accent-color: #00d2d3;
            --success-color: #0abf53;
            --warning-color: #f7b924;
            --dark-color: #1e293b;
            --light-color: #f8fafc;
            --bg-color: #f4f7fe;
            --merchant-color: #8b5cf6;
            --merchant-dark: #7c3aed;
        }
        
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--bg-color);
            -webkit-tap-highlight-color: transparent;
            overscroll-behavior-y: none;
        }
        .page-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--bg-color);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.5s ease, visibility 0.5s ease;
        }
        .spinner-container {
            position: relative;
            width: 80px;
            height: 80px;
        }
        .spinner {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: 4px solid rgba(67, 97, 238, 0.1);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1.2s cubic-bezier(0.68, -0.55, 0.27, 1.55) infinite;
        }
        .spinner:nth-child(2) {
            width: 70%;
            height: 70%;
            top: 15%;
            left: 15%;
            border-top-color: var(--secondary-color);
            animation-duration: 1.5s;
            animation-delay: 0.1s;
        }
        .spinner:nth-child(3) {
            width: 40%;
            height: 40%;
            top: 30%;
            left: 30%;
            border-top-color: var(--accent-color);
            animation-duration: 1.8s;
            animation-delay: 0.2s;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
    
    <!-- 延迟加载非关键CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" media="print" onload="this.media='all'">
    
    <!-- 预加载字体 -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript>
        <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    </noscript>
    
    <!-- Tailwind CDN - 考虑在生产环境中使用本地版本 -->
    <script src="https://cdn.tailwindcss.com" defer></script>
    
    <!-- 其余样式保持不变 -->
    <style>
        /* Custom styles */
        :root {
            --primary-color: #4361ee;
            --primary-dark: #3a5efd;
            --primary-light: #8b9cff;
            --secondary-color: #f44f61;
            --secondary-dark: #e63a4d;
            --accent-color: #00d2d3;
            --success-color: #0abf53;
            --warning-color: #f7b924;
            --dark-color: #1e293b;
            --light-color: #f8fafc;
            --bg-color: #f4f7fe;
        }
        
        body {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--bg-color);
            color: var(--dark-color);
            -webkit-tap-highlight-color: transparent;
            overscroll-behavior-y: none;
        }
        
        /* 页面初始加载遮罩 */
        .page-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--bg-color);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.6s ease, visibility 0.6s ease;
        }
        
        .page-loader.loaded {
            opacity: 0;
            visibility: hidden;
        }
        
        .loader-content {
            text-align: center;
        }
        
        .spinner {
            display: inline-block;
            width: 50px;
            height: 50px;
            border: 3px solid rgba(67, 97, 238, 0.3);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        /* 淡入效果 */
        .fade-in {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.8s ease, transform 0.8s ease;
        }
        
        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* 延迟加载效果 */
        .stagger-item {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.5s ease, transform 0.5s ease;
        }
        
        .stagger-item.visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* 渐变背景动画 */
        .animated-gradient {
            background: linear-gradient(135deg, #3a5efd 0%, #4c48ff 50%, #667eea 100%);
            background-size: 200% 200%;
            animation: gradientFlow 8s ease infinite;
        }
        
        @keyframes gradientFlow {
            0% { background-position: 0% 50% }
            50% { background-position: 100% 50% }
            100% { background-position: 0% 50% }
        }
        
        .primary-gradient {
            background: linear-gradient(135deg, #3a5efd 0%, #4c48ff 100%);
        }
        
        .header-wrapper {
            position: relative;
            overflow: hidden;
            border-radius: 0 0 28px 28px;
            box-shadow: 0 5px 15px rgba(58, 94, 253, 0.1);
        }
        
        .header-wrapper::before {
            content: '';
            position: absolute;
            top: -20px;
            right: -20px;
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            z-index: 1;
            animation: pulse 8s ease-in-out infinite alternate;
        }
        
        .header-wrapper::after {
            content: '';
            position: absolute;
            bottom: -30px;
            left: -20px;
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.08);
            z-index: 1;
            animation: pulse 8s ease-in-out infinite alternate-reverse;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); opacity: 0.8; }
            50% { transform: scale(1.15); opacity: 0.5; }
            100% { transform: scale(1); opacity: 0.8; }
        }
        
        .search-box {
            border-radius: 50px;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.3);
            height: 46px;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .search-box:focus-within {
            box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }
        
        .search-box input::placeholder {
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
        }
        
        .search-action {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
        
        .search-action:active {
            transform: scale(0.9);
            background: rgba(255, 255, 255, 0.3);
        }
        
        .search-history {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border-radius: 16px;
            margin-top: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease, opacity 0.3s ease;
            opacity: 0;
            z-index: 100;
        }
        
        .search-history.active {
            max-height: 300px;
            opacity: 1;
            padding: 10px 0;
        }
        
        .history-item {
            display: flex;
            align-items: center;
            padding: 8px 15px;
            transition: background-color 0.2s ease;
        }
        
        .history-item:active {
            background-color: rgba(67, 97, 238, 0.05);
        }
        
        .pulse-animation {
            animation: pulse-ring 1.5s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
        }
        
        @keyframes pulse-ring {
            0% {
                transform: scale(0.9);
            }
            50% {
                transform: scale(1.1);
            }
            100% {
                transform: scale(0.9);
            }
        }
        
        /* 语音搜索波纹效果 */
        .voice-wave {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            transform: scale(0);
            z-index: -1;
        }
        
        .voice-active .voice-wave {
            animation: voice-wave 2s infinite;
        }
        
        @keyframes voice-wave {
            0% {
                transform: scale(0);
                opacity: 1;
            }
            100% {
                transform: scale(2);
                opacity: 0;
            }
        }
        
        .card {
            background: #fff;
            border-radius: 22px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.03), 0 0 0 1px rgba(230, 230, 245, 0.6);
            overflow: hidden;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .card:active {
            transform: scale(0.98);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.06);
        }
        
        .main-card {
            height: 130px;
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .primary-card {
            background: linear-gradient(135deg, #4f6bf2 0%, #3a5efd 100%);
            color: white;
        }
        
        .secondary-card {
            background: linear-gradient(135deg, #f44f61 0%, #e63a4d 100%);
            color: white;
        }
        
        .main-card::before {
            content: '';
            position: absolute;
            right: -10px;
            bottom: -15px;
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            z-index: 1;
            animation: bubbleFloat 7s ease-in-out infinite alternate;
        }
        
        .main-card::after {
            content: '';
            position: absolute;
            right: -30px;
            top: -20px;
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            z-index: 1;
            animation: bubbleFloat 8s ease-in-out infinite alternate-reverse;
        }
        
        @keyframes bubbleFloat {
            0% { transform: translate(0, 0); }
            50% { transform: translate(-5px, 5px); }
            100% { transform: translate(5px, -5px); }
        }
        
        .main-card-content {
            position: relative;
            z-index: 2;
        }
        
        .icon-circle {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.07);
            background-color: #fff;
            margin-bottom: 6px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            z-index: 1;
        }
        
        .icon-circle:active {
            transform: scale(0.95);
        }
        
        .icon-circle svg {
            width: 24px;
            height: 24px;
            color: var(--primary-color);
            transition: transform 0.3s ease;
            position: relative;
            z-index: 2;
        }
        
        .icon-circle:hover svg {
            transform: scale(1.1);
        }
        
        .icon-circle::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle, rgba(67, 97, 238, 0.05) 0%, rgba(255, 255, 255, 0) 70%);
            z-index: 1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .icon-circle:hover::after {
            opacity: 1;
        }
        
        .service-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px 0;
            transition: transform 0.3s ease;
        }
        
        .service-item:active {
            transform: scale(0.95);
        }
        
        .service-label {
            font-size: 12px;
            font-weight: 500;
            color: #4b5563;
            margin-top: 2px;
        }
        
        .nav-wrapper {
            border-top: 1px solid rgba(229, 231, 235, 0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            background-color: rgba(255, 255, 255, 0.92);
            padding: 6px 0 5px;
            box-shadow: 0 -1px 10px rgba(0, 0, 0, 0.05);
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 50;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 0;
            min-width: 70px;
            position: relative;
            color: #9ca3af;
            font-weight: 500;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        
        .nav-item.active {
            color: var(--primary-color);
            transform: translateY(-2px);
        }
        
        .nav-item.active::after {
            content: '';
            position: absolute;
            bottom: -5px;
            width: 28px;
            height: 3px;
            background-color: var(--primary-color);
            border-radius: 3px;
            transform: scaleX(0);
            transition: transform 0.3s ease;
            animation: navIndicator 0.3s forwards;
        }
        
        @keyframes navIndicator {
            to { transform: scaleX(1); }
        }
        
        .nav-item svg {
            width: 22px;
            height: 22px;
            margin-bottom: 5px;
            transition: transform 0.3s ease;
        }
        
        .nav-item:hover svg {
            transform: translateY(-2px);
        }
        
        .nav-item span {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 80px;
            text-align: center;
        }
        
        /* 导航栏高亮特效 */
        .nav-item.active svg {
            filter: drop-shadow(0 0 3px rgba(67, 97, 238, 0.3));
        }
        
        /* 导航栏涟漪效果 */
        .nav-ripple {
            position: relative;
            overflow: hidden;
        }
        
        .nav-ripple::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 5px;
            height: 5px;
            background: rgba(67, 97, 238, 0.4);
            opacity: 0;
            border-radius: 100%;
            transform: scale(1, 1) translate(-50%, -50%);
            transform-origin: 50% 50%;
        }
        
        .nav-ripple:active::after {
            animation: ripple 0.6s ease-out;
        }
        
        @keyframes ripple {
            0% {
                transform: scale(0, 0) translate(-50%, -50%);
                opacity: 0.5;
            }
            100% {
                transform: scale(20, 20) translate(-50%, -50%);
                opacity: 0;
            }
        }
        
        .section-title {
            position: relative;
            font-weight: 700;
            color: #374151;
            padding-left: 12px;
            margin-bottom: 16px;
        }
        
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 18px;
            background: linear-gradient(to bottom, var(--primary-color), var(--primary-light));
            border-radius: 2px;
        }
        
        .notice-card {
            padding: 14px;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
            border-left: 3px solid #e5e7eb;
            transition: all 0.2s ease;
            border-radius: 12px;
        }
        
        .notice-card:active {
            border-left-color: var(--primary-color);
            background-color: #f9fafc;
        }
        
        .notice-tag {
            font-size: 11px;
            padding: 3px 8px;
            border-radius: 10px;
            background-color: #eef2ff;
            color: var(--primary-color);
        }
        
        .more-tag {
            font-size: 11px;
            font-weight: 500;
            color: var(--primary-color);
            transition: transform 0.3s ease;
        }
        
        .more-tag:active {
            transform: translateX(2px);
        }
        
        .platform-banner {
            position: relative;
            height: 120px;
            background: linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            transition: transform 0.3s ease;
        }
        
        .platform-banner:active {
            transform: scale(0.98);
        }
        
        .platform-banner::after {
            content: '';
            position: absolute;
            width: 140px;
            height: 140px;
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(79, 70, 229, 0.05) 100%);
            border-radius: 50%;
            bottom: -70px;
            right: -30px;
            animation: bannerBubble 10s infinite alternate;
        }
        
        .platform-banner::before {
            content: '';
            position: absolute;
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(79, 70, 229, 0.05) 100%);
            border-radius: 50%;
            top: -60px;
            left: -30px;
            animation: bannerBubble 12s infinite alternate-reverse;
        }
        
        @keyframes bannerBubble {
            0% { transform: translate(0, 0); }
            100% { transform: translate(10px, 10px); }
        }
        
        @keyframes floatAnimation {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
            100% { transform: translateY(0px); }
        }
        
        .float-icon {
            animation: floatAnimation 2.5s ease-in-out infinite;
        }
        
        .user-btn {
            border-radius: 50px;
            background-color: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 6px 12px;
            transition: all 0.3s ease;
        }
        
        .user-btn:active {
            background-color: rgba(255, 255, 255, 0.25);
            transform: scale(0.95);
        }
        
        /* 底部安全区 */
        .safe-area-bottom {
            height: env(safe-area-inset-bottom, 0px);
        }
        
        /* 骨架屏效果 */
        .skeleton {
            position: relative;
            overflow: hidden;
            background-color: #e5e7eb;
            border-radius: 4px;
        }
        
        .skeleton::after {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            transform: translateX(-100%);
            background-image: linear-gradient(
                90deg,
                rgba(255, 255, 255, 0) 0,
                rgba(255, 255, 255, 0.2) 20%,
                rgba(255, 255, 255, 0.5) 60%,
                rgba(255, 255, 255, 0)
            );
            animation: shimmer 2s infinite;
            content: '';
        }
        
        @keyframes shimmer {
            100% {
                transform: translateX(100%);
            }
        }
        
        /* 触摸反馈效果 */
        .touch-effect {
            position: relative;
            overflow: hidden;
        }
        
        .touch-effect::after {
            content: '';
            display: block;
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            pointer-events: none;
            background-image: radial-gradient(circle, rgba(255, 255, 255, 0.2) 10%, transparent 10.01%);
            background-repeat: no-repeat;
            background-position: 50%;
            transform: scale(10, 10);
            opacity: 0;
            transition: transform 0.5s, opacity 0.8s;
        }
        
        .touch-effect:active::after {
            transform: scale(0, 0);
            opacity: 0.3;
            transition: 0s;
        }
        
        /* 新增卡片样式 */
        .card-shadow-hover {
            transition: box-shadow 0.3s ease, transform 0.3s ease;
        }
        
        .card-shadow-hover:hover {
            box-shadow: 0 12px 25px rgba(0, 0, 0, 0.08);
            transform: translateY(-2px);
        }
        
        /* 光晕效果 */
        .glow-effect {
            position: relative;
            z-index: 1;
        }
        
        .glow-effect::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, var(--primary-color), var(--primary-dark), var(--primary-light), var(--primary-color));
            background-size: 400%;
            z-index: -1;
            border-radius: 24px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .glow-effect:hover::before {
            opacity: 0.7;
            animation: glowing 20s linear infinite;
        }
        
        @keyframes glowing {
            0% { background-position: 0 0; }
            50% { background-position: 400% 0; }
            100% { background-position: 0 0; }
        }
        
        /* 标签标记样式优化 */
        .label-tag {
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 500;
            background-color: rgba(67, 97, 238, 0.1);
            color: var(--primary-color);
            display: inline-block;
            margin-right: 5px;
        }
        
        /* 3D 卡片效果 */
        .card-3d-effect {
            transition: transform 0.5s ease, box-shadow 0.5s ease;
            transform-style: preserve-3d;
            perspective: 1000px;
        }
        
        .card-3d-effect:hover {
            transform: translateY(-5px) rotateX(5deg);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }
        
        /* 添加装饰图标样式 */
        .banner-icon {
            position: absolute;
            opacity: 0.15;
            z-index: 1;
        }
        
        .banner-icon-1 {
            top: 15px;
            right: 25px;
            transform: rotate(15deg);
            animation: floatAnimation 3s ease-in-out infinite;
        }
        
        .banner-icon-2 {
            bottom: 15px;
            left: 25px;
            transform: rotate(-10deg);
            animation: floatAnimation 2.5s ease-in-out infinite 0.5s;
        }
        
        .feature-icon-group {
            display: flex;
            justify-content: space-around;
            margin-top: 12px;
        }
        
        .feature-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: rgba(67, 97, 238, 0.1);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .feature-icon:hover {
            transform: scale(1.1);
            background-color: rgba(67, 97, 238, 0.15);
        }
        
        .feature-icon svg {
            width: 12px;
            height: 12px;
            color: var(--primary-color);
        }
        
        /* 卡片内装饰图案 */
        .card-decoration {
            position: absolute;
            z-index: 1;
            opacity: 0.2;
        }
        
        .card-decoration-1 {
            top: 10px;
            right: 10px;
            width: 40px;
            height: 40px;
        }
        
        .card-decoration-2 {
            bottom: 10px;
            left: 10px;
            width: 30px;
            height: 30px;
            transform: rotate(45deg);
        }
        
        /* 水平滚动容器 */
        .scroll-container {
            overflow-x: auto;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
            scroll-behavior: smooth;
            -webkit-overflow-scrolling: touch;
            padding-bottom: 8px;
        }
        
        .scroll-container::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
        }
        
        .scroll-content {
            display: flex;
            flex-wrap: nowrap;
            padding: 0 5px;
        }
        
        .scroll-content .service-item {
            flex: 0 0 auto;
            width: 80px;
            margin: 0 8px;
        }
        
        /* 滚动指示器 */
        .scroll-indicator {
            display: flex;
            justify-content: center;
            margin-top: 10px;
        }
        
        .scroll-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: #e5e7eb;
            margin: 0 3px;
            transition: all 0.3s ease;
        }
        
        .scroll-dot.active {
            background-color: var(--primary-color);
            width: 20px;
            border-radius: 3px;
        }
        
        /* 图标背景色 */
        .bg-blue-soft {
            background-color: rgba(67, 97, 238, 0.08);
        }
        
        .bg-indigo-soft {
            background-color: rgba(14, 165, 233, 0.08);
        }
        
        .bg-red-soft {
            background-color: rgba(244, 79, 97, 0.08);
        }
        
        .bg-green-soft {
            background-color: rgba(10, 191, 83, 0.08);
        }
        
        .bg-yellow-soft {
            background-color: rgba(247, 185, 36, 0.08);
        }
        
        .bg-purple-soft {
            background-color: rgba(139, 92, 246, 0.08);
        }
        
        /* 新增毛玻璃效果卡片 */
        .glassmorphism {
            background: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
        }
        
        /* 新增渐变边框效果 */
        .gradient-border {
            position: relative;
            border-radius: 22px;
            padding: 1px;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
        }
        
        .gradient-border-content {
            background: white;
            border-radius: 21px;
            padding: 14px;
            height: 100%;
        }
        
        /* 改进的富标签 */
        .rich-tag {
            display: inline-flex;
            align-items: center;
            padding: 3px 8px;
            border-radius: 6px;
            font-size: 11px;
            font-weight: 500;
            background-color: #f1f5ff;
            color: var(--primary-color);
            margin-right: 5px;
            box-shadow: 0 2px 5px rgba(67, 97, 238, 0.1);
        }
        
        .rich-tag svg {
            width: 12px;
            height: 12px;
            margin-right: 4px;
        }
        
        /* 改进的滚动效果 */
        .smooth-scroll {
            scroll-behavior: smooth;
            scroll-snap-type: x mandatory;
        }
        
        .smooth-scroll > * {
            scroll-snap-align: center;
        }
        
        /* 动感效果卡片 */
        .dynamic-card {
            transition: all 0.3s ease;
            overflow: hidden;
            position: relative;
        }
        
        .dynamic-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }
        
        .dynamic-card:hover::before {
            transform: translateX(100%);
        }
        
        /* 内容框 */
        .content-box {
            border-radius: 16px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: white;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.03);
            transition: all 0.3s ease;
        }
        
        .content-box:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.05);
            transform: translateY(-2px);
        }
        
        /* 动态下划线 */
        .dynamic-underline {
            position: relative;
            display: inline-block;
        }
        
        .dynamic-underline::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background-color: var(--primary-color);
            transition: width 0.3s ease;
        }
        
        .dynamic-underline:hover::after {
            width: 100%;
        }
        
        /* 新增悬浮按钮 */
        .floating-btn {
            position: fixed;
            bottom: 80px;
            right: 20px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
            z-index: 100;
            transform: scale(0);
            animation: popIn 0.3s ease forwards 3s;
        }
        
        @keyframes popIn {
            0% { transform: scale(0); }
            80% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .floating-btn svg {
            width: 24px;
            height: 24px;
        }
        
        /* 改进滚动条 */
        .custom-scrollbar {
            scrollbar-width: thin;
            scrollbar-color: var(--primary-light) #f1f5ff;
        }
        
        .custom-scrollbar::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5ff;
            border-radius: 10px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: var(--primary-light);
            border-radius: 10px;
        }
        
        /* 改进通知卡片 */
        .enhanced-notice {
            position: relative;
            overflow: hidden;
            border-radius: 14px;
            border-left: none;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }
        
        .enhanced-notice::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: linear-gradient(to bottom, var(--primary-color), var(--primary-light));
        }
        
        .enhanced-notice:hover {
            transform: translateX(3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        /* 改进的头像样式 */
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            background: linear-gradient(135deg, #4361ee, #3a5efd);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            box-shadow: 0 4px 10px rgba(67, 97, 238, 0.2);
            border: 2px solid white;
        }
        
        /* 徽章样式 */
        .badge {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: var(--secondary-color);
            color: white;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid white;
        }
        
        /* 子菜单样式 */
        .parent-menu {
            position: relative;
        }
        
        .submenu-wrapper {
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%) translateY(10px);
            width: max-content;
            min-width: 220px;
            max-width: 90vw;
            z-index: 50;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            pointer-events: none;
            max-height: 80vh; /* 限制最大高度 */
            overflow-y: auto; /* 内容超出时显示滚动条 */
        }
        
        .submenu-container {
            position: relative;
            border-radius: 12px;
            background: white;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(235, 238, 251, 0.9);
            overflow: hidden;
        }
        
        .has-children {
            position: relative;
        }
        
        .submenu-arrow {
            transition: transform 0.3s ease;
        }
        
        .submenu-arrow-up {
            position: absolute;
            top: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 16px;
            height: 16px;
            background-color: white;
            clip-path: polygon(50% 0%, 100% 100%, 0% 100%);
            transform-origin: center;
            z-index: 1;
            border-top: 1px solid rgba(235, 238, 251, 0.9);
            border-left: 1px solid rgba(235, 238, 251, 0.9);
        }
        
        /* 桌面端悬停显示子菜单 */
        @media (hover: hover) {
            .parent-menu:hover .submenu-wrapper,
            .parent-menu:focus-within .submenu-wrapper {
                opacity: 1;
                visibility: visible;
            }
            
            .parent-menu:hover .submenu-arrow,
            .parent-menu:focus-within .submenu-arrow {
                transform: rotate(180deg);
            }
        }
        
        /* 移动端点击显示子菜单 */
        .parent-menu.active .submenu-wrapper {
            opacity: 1;
            visibility: visible;
        }
        
        .parent-menu.active .submenu-arrow {
            transform: rotate(180deg);
        }
        
        /* 为图标添加小标记表示有子菜单 */
        .has-children::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 8px;
            height: 8px;
            background-color: var(--primary-color);
            border-radius: 50%;
            border: 2px solid white;
            display: none; /* 默认隐藏，仅在JS中根据需要显示 */
        }
        
        /* 改进子菜单网格布局 */
        .submenu-container .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(90px, 1fr));
            gap: 8px;
            padding: 8px;
        }
        
        .submenu-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 8px;
            border-radius: 8px;
            text-align: center;
            transition: all 0.2s ease;
        }
        
        .submenu-item:hover {
            background-color: rgba(67, 97, 238, 0.05);
            transform: translateY(-2px);
        }
        
        .submenu-item:hover svg {
            transform: translateX(2px);
            transition: transform 0.2s ease;
        }
        
        /* 为子菜单添加动画效果 */
        .submenu-wrapper {
            transform: translateX(-50%) translateY(10px);
            transition: opacity 0.3s ease, visibility 0.3s ease, transform 0.3s ease;
        }
        
        .parent-menu.active .submenu-wrapper,
        .parent-menu:hover .submenu-wrapper {
            transform: translateX(-50%) translateY(0);
        }
        
        .submenu-items {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
        }
        
        .submenu-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }
        
        .submenu-icon {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f3f4f6;
            margin-bottom: 6px;
        }
        
        .submenu-icon svg {
            width: 20px;
            height: 20px;
            color: var(--primary-color);
        }
        
        .submenu-label {
            font-size: 10px;
            color: #4b5563;
            max-width: 100%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        /* 底部导航指示器 */
        .bottom-nav-indicator {
            position: absolute;
            bottom: -20px;
            left: 0;
            right: 0;
            display: flex;
            justify-content: center;
            gap: 4px;
            padding: 4px;
        }
        
        .bottom-nav-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: #d1d5db;
            transition: all 0.3s ease;
        }
        
        .bottom-nav-dot.active {
            background-color: var(--primary-color);
            width: 20px;
            border-radius: 3px;
        }
        
        /* 底部导航栏商家入口 */
        .merchant-entry {
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            color: white;
            border-radius: 12px;
            padding: 8px 12px;
            box-shadow: 0 4px 10px rgba(139, 92, 246, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all 0.3s ease;
            transform-origin: center;
        }
        
        .merchant-entry:active {
            transform: scale(0.95);
        }
        
        .merchant-entry::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 60%);
            transform: rotate(0deg);
            transition: transform 1s linear;
        }
        
        .merchant-entry:hover::before {
            transform: rotate(180deg);
        }
        
        .merchant-glow {
            animation: merchantGlow 3s infinite alternate;
        }
        
        @keyframes merchantGlow {
            0% { box-shadow: 0 4px 10px rgba(139, 92, 246, 0.3); }
            100% { box-shadow: 0 6px 15px rgba(139, 92, 246, 0.6); }
        }

        /* 添加页面快速入场动画 */
        @keyframes quickFadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .quick-fade-in {
            animation: quickFadeInUp 0.4s ease forwards;
        }
        
        /* 添加波浪背景效果 */
        .wave-bg {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            overflow: hidden;
            line-height: 0;
            z-index: 0;
        }
        
        .wave-bg svg {
            display: block;
            width: calc(100% + 1.3px);
            height: 46px;
        }
        
        .wave-bg .shape-fill {
            fill: #FFFFFF;
            opacity: 0.2;
        }
        
        /* 商家按钮悬浮效果增强 */
        .merchant-btn {
            box-shadow: 0 4px 12px rgba(139, 92, 246, 0.25);
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }
        
        .merchant-btn:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 6px 15px rgba(139, 92, 246, 0.35);
        }
        
        /* 商家卡片入场动画 */
        .merchant-card-enter {
            animation: merchantCardEnter 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
        }
        
        @keyframes merchantCardEnter {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
        
        /* 添加脉冲动画 */
        @keyframes pulse-subtle {
            0% { opacity: 0.7; }
            50% { opacity: 1; }
            100% { opacity: 0.7; }
        }
        
        .animate-pulse-subtle {
            animation: pulse-subtle 1.5s ease-in-out infinite;
        }
        
        /* 添加缓慢ping动画 */
        @keyframes ping-slow {
            75%, 100% {
                transform: scale(1.5);
                opacity: 0;
            }
        }
        
        .animate-ping-slow {
            animation: ping-slow 2.5s cubic-bezier(0, 0, 0.2, 1) infinite;
        }
    </style>
    <!-- 底部导航栏样式增强 -->
    <style>
        /* 更新底部导航栏样式 */
        .nav-wrapper {
            border-top: 1px solid rgba(229, 231, 235, 0.8);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            background-color: rgba(255, 255, 255, 0.95);
            padding: 8px 0 5px;
            box-shadow: 0 -2px 15px rgba(0, 0, 0, 0.06);
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 50;
        }
        
        /* 调整底部子菜单样式 - 从底部向上展开 */
        .submenu-wrapper-bottom {
            position: absolute !important;
            bottom: 100% !important;
            top: auto !important;
            left: 50%;
            transform: translateX(-50%) translateY(15px);
            z-index: 100;
            filter: drop-shadow(0 -2px 10px rgba(0, 0, 0, 0.1));
        }
        
        /* 子菜单向上箭头调整 */
        .submenu-wrapper-bottom .submenu-arrow-up {
            top: auto !important;
            bottom: -8px !important;
            transform: translateX(-50%) rotate(180deg) !important;
            background: white;
            box-shadow: 0 2px 2px rgba(0, 0, 0, 0.05);
        }
        
        /* 子菜单展开动画调整 */
        .parent-menu.active .submenu-wrapper-bottom {
            transform: translateX(-50%) translateY(0);
        }
        
        @media (hover: hover) {
            .parent-menu:hover .submenu-wrapper-bottom,
            .parent-menu:focus-within .submenu-wrapper-bottom {
                transform: translateX(-50%) translateY(0);
            }
        }
        
        /* 底部导航栏内的服务项样式调整 */
        .nav-wrapper .service-item {
            padding: 5px 0;
            margin: 0 5px;
            position: relative;
        }
        
        .nav-wrapper .icon-circle {
            width: 48px;
            height: 48px;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }
        
        .nav-wrapper .service-item:active .icon-circle {
            transform: scale(0.92);
        }
        
        .nav-wrapper .scroll-container {
            overflow-x: auto;
            overflow-y: hidden;
            -webkit-overflow-scrolling: touch;
            scroll-behavior: smooth;
            scrollbar-width: none;
            -ms-overflow-style: none;
            padding: 0 10px;
        }
        
        .nav-wrapper .scroll-container::-webkit-scrollbar {
            display: none;
        }
        
        .nav-wrapper .scroll-content {
            display: flex;
            flex-wrap: nowrap;
            gap: 15px;
            padding: 5px 0;
        }
        
        .nav-wrapper .service-item {
            flex: 0 0 auto;
            width: 70px;
            position: relative;
        }
        
        /* 子菜单容器样式优化 */
        .submenu-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(235, 238, 251, 0.9);
            overflow: hidden;
            padding: 10px;
            min-width: 220px;
            max-width: 90vw;
        }
        
        /* 子菜单项样式改进 */
        .submenu-item {
            position: relative;
            overflow: hidden;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .submenu-item:hover {
            background-color: rgba(67, 97, 238, 0.05);
            transform: translateY(-2px);
        }
        
        .submenu-item:active {
            transform: scale(0.98);
        }
        
        /* 主页内容区域添加底部内边距，防止被导航栏遮挡 */
        .relative.min-h-screen {
            padding-bottom: 120px;
        }
        
        /* 改进滚动进度条样式，使其在底部导航栏中更美观 */
        .nav-wrapper .scroll-progress-container {
            height: 3px;
            background-color: rgba(229, 231, 235, 0.3);
            border-radius: 3px;
            margin: 5px 15px 8px;
        }
        
        .nav-wrapper .scroll-progress-bar {
            background: linear-gradient(to right, var(--primary-color), var(--primary-light));
            border-radius: 3px;
            position: relative;
            overflow: hidden;
        }
        
        .nav-wrapper .scroll-progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, 
                rgba(255, 255, 255, 0) 0%, 
                rgba(255, 255, 255, 0.4) 50%, 
                rgba(255, 255, 255, 0) 100%);
            animation: progressShimmer 1.5s infinite;
            transform: translateX(-100%);
        }
        
        /* 服务标签样式增强 */
        .service-label {
            font-size: 12px;
            font-weight: 500;
            color: #4b5563;
            margin-top: 6px;
            max-width: 100%;
            text-align: center;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        /* 菜单项激活状态 */
        .service-item.active .icon-circle {
            transform: translateY(-3px);
            box-shadow: 0 10px 15px rgba(67, 97, 238, 0.15);
        }
        
        .service-item.active .service-label {
            color: var(--primary-color);
            font-weight: 600;
        }
        
        /* 微光效果增强 */
        .glow-pulse {
            animation: glowPulse 2s ease-in-out infinite alternate;
        }
        
        @keyframes glowPulse {
            0% { opacity: 0; }
            50% { opacity: 0.7; }
            100% { opacity: 0; }
        }
    </style>
</head>
<body class="text-gray-800">
    <!-- 加载遮罩 -->
    <div class="page-loader">
        <div class="loader-content">
            <div class="spinner-container">
                <div class="spinner"></div>
                <div class="spinner"></div>
                <div class="spinner"></div>
            </div>
            <div class="text-[#4361ee] font-bold text-xl mt-6" style="animation: fadeInUp 0.6s 0.3s both;">{$siteName}</div>
            <p class="text-gray-500 text-sm mt-2" style="animation: fadeInUp 0.6s 0.5s both;">精彩体验即将开启...</p>
        </div>
    </div>

    <div class="relative min-h-screen pb-4">
        <!-- Header -->
        <div class="header-wrapper animated-gradient text-white fade-in">
            <div class="p-5 pb-6">
                <div class="flex justify-between items-center mb-5">
                    <div>
                        <h1 class="text-xl font-bold text-white flex items-center">
                            <span class="mr-2">{$siteName}</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-yellow-300 animate-pulse-subtle">
                                <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                            </svg>
                        </h1>
                        <p class="text-xs text-white opacity-90 mt-1">{$siteSubtitle}</p>
                    </div>
                    <a href="/merchant/login" class="user-btn merchant-btn group flex items-center shadow-lg hover:scale-105 transition-all duration-300 relative overflow-hidden">
                        <div class="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full"></div>
                        <div class="absolute inset-0 bg-gradient-to-r from-indigo-600/20 to-purple-600/20 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                        <div class="avatar flex items-center justify-center text-sm w-8 h-8 mr-1.5 relative overflow-hidden">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="group-hover:scale-110 transition-transform duration-300">
                                <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
                                <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
                                <path d="M9 14h.01"></path>
                                <path d="M13 14h2"></path>
                                <path d="M9 18h.01"></path>
                                <path d="M13 18h2"></path>
                            </svg>
                        </div>
                        <span class="ml-0.5 text-xs font-medium group-hover:font-semibold transition-all duration-300 relative z-10">商家中心</span>
                        <div class="absolute -bottom-1 left-1/2 w-0 h-0.5 bg-white transform -translate-x-1/2 group-hover:w-[90%] transition-all duration-300"></div>
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="px-4 -mt-6 pb-24">
            <!-- 主要服务区域 - 两列网格布局 -->
            <div class="flex flex-col gap-4 mb-5">
                <!-- 顶部双卡片区域 -->
                <div class="grid grid-cols-2 gap-3 fade-in">
                    <div class="card main-card primary-card p-4 stagger-item card-3d-effect dynamic-card relative overflow-hidden backdrop-blur-sm">
                        <!-- 添加背景装饰 -->
                        <div class="absolute -top-10 -right-10 w-32 h-32 rounded-full bg-white opacity-10 blur-xl"></div>
                        <div class="absolute -bottom-6 -left-6 w-24 h-24 rounded-full bg-white opacity-10 blur-md"></div>
                        
                        <div class="main-card-content flex flex-col h-full justify-between relative z-10">
                            <!-- 添加装饰图案 -->
                            <div class="card-decoration card-decoration-1">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <rect width="20" height="14" x="2" y="5" rx="2"></rect>
                                    <line x1="2" x2="22" y1="10" y2="10"></line>
                                </svg>
                            </div>
                            <div class="card-decoration card-decoration-2">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <circle cx="11" cy="11" r="8"></circle>
                                    <path d="m21 21-4.3-4.3"></path>
                                </svg>
                            </div>
                            
                            <div class="flex items-center">
                                <div class="h-10 w-10 rounded-full bg-white bg-opacity-20 flex items-center justify-center mr-3 shadow-lg">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <rect width="20" height="14" x="2" y="5" rx="2"></rect>
                                        <line x1="2" x2="22" y1="10" y2="10"></line>
                                    </svg>
                                </div>
                                <h2 class="font-bold text-base">鲸商城PRO查询</h2>
                            </div>
                            <div>
                                <p class="text-xs opacity-90 mb-3">一键查询卡片服务</p>
                                <div class="w-14 h-14 rounded-xl bg-white bg-opacity-15 float-icon flex items-center justify-center shadow-lg relative overflow-hidden">
                                    <!-- 添加波纹效果 -->
                                    <div class="absolute inset-0 bg-white opacity-20 scale-0 rounded-xl transform-origin-center animate-ping-slow"></div>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <circle cx="11" cy="11" r="8"></circle>
                                        <path d="m21 21-4.3-4.3"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card main-card secondary-card p-4 stagger-item card-3d-effect dynamic-card relative overflow-hidden backdrop-blur-sm group hover:shadow-2xl transition-all duration-500">
                        <!-- 添加背景装饰 -->
                        <div class="absolute -top-10 -right-10 w-32 h-32 rounded-full bg-white opacity-10 blur-xl group-hover:opacity-20 transition-opacity duration-500"></div>
                        <div class="absolute -bottom-6 -left-6 w-24 h-24 rounded-full bg-white opacity-10 blur-md group-hover:opacity-20 transition-opacity duration-500"></div>
                        
                        <!-- 添加热门标签 -->
                        <div class="absolute -top-1 -right-1 bg-white/20 text-white text-[10px] font-medium py-1 px-2 rounded-bl-lg rounded-tr-lg backdrop-blur-sm z-20 shadow-lg">
                            热门
                        </div>
                        
                        <div class="main-card-content flex flex-col h-full justify-between relative z-10">
                            <!-- 添加装饰图案 -->
                            <div class="card-decoration card-decoration-1">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path>
                                    <polyline points="14 2 14 8 20 8"></polyline>
                                </svg>
                            </div>
                            <div class="card-decoration card-decoration-2">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                                    <line x1="8" y1="21" x2="16" y2="21"></line>
                                    <line x1="12" y1="17" x2="12" y2="21"></line>
                                </svg>
                            </div>
                            
                            <div class="flex items-center">
                                <div class="h-10 w-10 rounded-full bg-white bg-opacity-20 flex items-center justify-center mr-3 shadow-lg group-hover:scale-110 transition-transform duration-500">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path>
                                        <polyline points="14 2 14 8 20 8"></polyline>
                                        <line x1="16" x2="8" y1="13" y2="13"></line>
                                        <line x1="16" x2="8" y1="17" y2="17"></line>
                                        <line x1="10" x2="8" y1="9" y2="9"></line>
                                    </svg>
                                </div>
                                <h2 class="font-bold text-base">订单查询</h2>
                            </div>
                            <div>
                                <p class="text-xs opacity-90 mb-3">快速查询订单信息</p>
                                <a href="/order" class="block group-hover:scale-105 transition-transform duration-500">
                                    <div class="w-14 h-14 rounded-xl bg-white bg-opacity-15 float-icon flex items-center justify-center shadow-lg relative overflow-hidden">
                                        <!-- 添加波纹效果 -->
                                        <div class="absolute inset-0 bg-white opacity-20 scale-0 rounded-xl transform-origin-center animate-ping-slow"></div>
                                        <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="group-hover:scale-110 transition-transform duration-300">
                                            <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                                            <line x1="8" y1="21" x2="16" y2="21"></line>
                                            <line x1="12" y1="17" x2="12" y2="21"></line>
                                        </svg>
                                    </div>
                                </a>
                            </div>
                        </div>
                        
                        <!-- 添加透明覆盖层实现整体可点击效果 -->
                        <a href="/order" class="absolute inset-0 z-30 opacity-0"></a>
                    </div>
                </div>

                <!-- 服务图标区 - 将从这里移除 -->
                <div class="card p-4 fade-in glassmorphism rounded-2xl relative" style="display:none;">
                    <!-- 已移至底部导航栏 -->
                </div>

                <!-- 二级内容区域 - 左右分栏 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- 左侧平台信息 - 添加商家入口卡片 -->
                    <div class="card mb-4 fade-in rounded-2xl">
                        <div class="p-4 pb-2 flex justify-between items-center">
                            <h3 class="section-title text-base">商家专区</h3>
                            <a href="/merchant/login" class="more-tag flex items-center">
                                立即进入
                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-1 animate-pulse-subtle">
                                    <polyline points="9 18 15 12 9 6"></polyline>
                                </svg>
                            </a>
                        </div>
                        <div class="px-4 pb-4">
                            <p class="text-sm text-[#8b5cf6] mb-3 font-medium">强大高效的商家管理平台</p>
                            <div class="platform-banner relative overflow-hidden shadow-md" style="background: linear-gradient(135deg, #f0ecff 0%, #e5dbff 100%);">
                                <!-- 添加装饰图标 -->
                                <div class="banner-icon banner-icon-1">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#8b5cf6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
                                        <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
                                    </svg>
                                </div>
                                <div class="banner-icon banner-icon-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#8b5cf6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                                        <line x1="8" y1="21" x2="16" y2="21"></line>
                                        <line x1="12" y1="17" x2="12" y2="21"></line>
                                    </svg>
                                </div>
                                
                                <!-- 添加光晕背景 -->
                                <div class="absolute -top-20 -right-20 w-40 h-40 rounded-full bg-purple-100 opacity-50 blur-xl"></div>
                                <div class="absolute -bottom-20 -left-20 w-40 h-40 rounded-full bg-indigo-100 opacity-50 blur-xl"></div>
                                
                                <div class="relative z-10 text-center py-5">
                                    <p class="font-bold text-gray-800 text-base">商家管理中心</p>
                                    <p class="text-[#8b5cf6] text-xs mt-1">订单管理 · 库存跟踪 · 数据分析</p>
                                    
                                    <!-- 添加功能图标列表 -->
                                    <div class="feature-icon-group mt-3">
                                        <div class="feature-icon hover:scale-110 transition-transform duration-300" style="background-color: rgba(139, 92, 246, 0.1);">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="#8b5cf6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                                                <line x1="8" y1="21" x2="16" y2="21"></line>
                                                <line x1="12" y1="17" x2="12" y2="21"></line>
                                            </svg>
                                        </div>
                                        <div class="feature-icon hover:scale-110 transition-transform duration-300" style="background-color: rgba(139, 92, 246, 0.1);">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="#8b5cf6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                                <polyline points="22 4 12 14.01 9 11.01"></polyline>
                                            </svg>
                                        </div>
                                        <div class="feature-icon hover:scale-110 transition-transform duration-300" style="background-color: rgba(139, 92, 246, 0.1);">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="#8b5cf6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <line x1="12" y1="20" x2="12" y2="10"></line>
                                                <line x1="18" y1="20" x2="18" y2="4"></line>
                                                <line x1="6" y1="20" x2="6" y2="16"></line>
                                            </svg>
                                        </div>
                                        <div class="feature-icon hover:scale-110 transition-transform duration-300" style="background-color: rgba(139, 92, 246, 0.1);">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="#8b5cf6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                                                <circle cx="12" cy="10" r="3"></circle>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧平台信息 -->
                    <div class="card mb-4 fade-in rounded-2xl">
                        <div class="p-4 pb-2 flex justify-between items-center">
                            <h3 class="section-title text-base">为客户提供简易</h3>
                            <span class="more-tag flex items-center">
                                了解更多
                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-1 animate-pulse-subtle">
                                    <polyline points="9 18 15 12 9 6"></polyline>
                                </svg>
                            </span>
                        </div>
                        <div class="px-4 pb-4">
                            <p class="text-sm text-[#4361ee] mb-3 font-medium">集约打造的自助服务平台</p>
                            <div class="platform-banner relative overflow-hidden shadow-md">
                                <!-- 添加装饰图标 -->
                                <div class="banner-icon banner-icon-1">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#4361ee" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                                    </svg>
                                </div>
                                <div class="banner-icon banner-icon-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#4361ee" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                                    </svg>
                                </div>
                                
                                <!-- 添加光晕背景 -->
                                <div class="absolute -top-20 -right-20 w-40 h-40 rounded-full bg-indigo-100 opacity-50 blur-xl"></div>
                                <div class="absolute -bottom-20 -left-20 w-40 h-40 rounded-full bg-blue-100 opacity-50 blur-xl"></div>
                                
                                <div class="relative z-10 text-center py-5">
                                    <p class="font-bold text-gray-800 text-base">一站式服务中心</p>
                                    <p class="text-[#4361ee] text-xs mt-1">简易 · 高效 · 安全</p>
                                    
                                    <!-- 添加功能图标列表 -->
                                    <div class="feature-icon-group mt-3">
                                        <div class="feature-icon hover:scale-110 transition-transform duration-300">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                                            </svg>
                                        </div>
                                        <div class="feature-icon hover:scale-110 transition-transform duration-300">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <circle cx="12" cy="12" r="10"></circle>
                                                <polyline points="12 6 12 12 16 14"></polyline>
                                            </svg>
                                        </div>
                                        <div class="feature-icon hover:scale-110 transition-transform duration-300">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                                <polyline points="22 4 12 14.01 9 11.01"></polyline>
                                            </svg>
                                        </div>
                                        <div class="feature-icon hover:scale-110 transition-transform duration-300">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M18 8h1a4 4 0 0 1 0 8h-1"></path>
                                                <path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z"></path>
                                                <line x1="6" y1="1" x2="6" y2="4"></line>
                                                <line x1="10" y1="1" x2="10" y2="4"></line>
                                                <line x1="14" y1="1" x2="14" y2="4"></line>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航栏 -->
        <div class="nav-wrapper">
            <!-- 滚动容器 -->
            <div class="scroll-container custom-scrollbar px-2">
                <div class="scroll-content py-1" id="nav-scroll-container">
                    {volist name="navItems" id="item"}
                    <!-- 服务菜单项 -->
                    <div class="service-item stagger-item relative {if isset($item.children) && count($item.children) > 0}parent-menu{/if}" data-submenu-id="{$item.id}">
                        <!-- 主菜单项 -->
                        <a href="{$item.href ?? 'javascript:;'}" class="flex flex-col items-center nav-main-item {if isset($item.children) && count($item.children) > 0}has-children{/if}" data-nav-id="{$item.id}">
                            <div class="icon-circle {eq name='key+1' value='1'}bg-blue-soft{/eq}{eq name='key+1' value='2'}bg-indigo-soft{/eq}{eq name='key+1' value='3'}bg-red-soft{/eq}{eq name='key+1' value='4'}bg-green-soft{/eq}{eq name='key+1' value='5'}bg-yellow-soft{/eq}{gt name='key+1' value='5'}bg-purple-soft{/gt} relative shadow-lg">
                                <!-- 微光效果 -->
                                <div class="absolute inset-0 rounded-full opacity-0 hover:opacity-70 bg-white transition-opacity duration-500 glow-pulse"></div>
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" 
                                stroke="{eq name='key+1' value='1'}#4361ee{/eq}{eq name='key+1' value='2'}#0ea5e9{/eq}{eq name='key+1' value='3'}#f44f61{/eq}{eq name='key+1' value='4'}#0abf53{/eq}{eq name='key+1' value='5'}#f7b924{/eq}{gt name='key+1' value='5'}#8b5cf6{/gt}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative z-10">
                                    {switch key+1}
                                    {case 1}
                                    <circle cx="11" cy="11" r="8"></circle>
                                    <path d="m21 21-4.3-4.3"></path>
                                    {/case}
                                    {case 2}
                                    <path d="M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z"></path>
                                    <path d="m3.3 7 8.7 5 8.7-5"></path>
                                    <path d="M12 22V12"></path>
                                    {/case}
                                    {case 3}
                                    <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                                    <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
                                    {/case}
                                    {case 4}
                                    <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
                                    {/case}
                                    {case 5}
                                    <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                                    <line x1="8" y1="21" x2="16" y2="21"></line>
                                    <line x1="12" y1="17" x2="12" y2="21"></line>
                                    {/case}
                                    {case 6}
                                    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                                    {/case}
                                    {default}
                                    <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                                    <polyline points="9 22 9 12 15 12 15 22"></polyline>
                                    {/default}
                                    {/switch}
                                </svg>
                            </div>
                            <div class="flex items-center">
                                <span class="service-label font-medium">{$item.name}</span>
                                <!-- 子菜单指示器 -->
                                {if isset($item.children) && count($item.children) > 0}
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="12" height="12" fill="none" stroke="#4b5563" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-1 submenu-arrow transition-transform duration-300">
                                    <polyline points="6 9 12 15 18 9"></polyline>
                                </svg>
                                {/if}
                            </div>
                        </a>
                    </div>
                    {/volist}
                </div>
            </div>
            
            <!-- 添加滚动进度条 -->
            <div class="scroll-progress-container mx-4 mt-1 mb-2">
                <div class="scroll-progress-bar" id="scrollProgressBar"></div>
            </div>
            
            <div class="safe-area-bottom"></div>
        </div>

        <!-- 子菜单容器移到外层 -->
        <div class="submenus-container">
            {volist name="navItems" id="item"}
            {if isset($item.children) && count($item.children) > 0}
            <div class="submenu-wrapper submenu-wrapper-bottom" data-for-menu="{$item.id}" style="display:none; visibility:visible;">
                <div class="submenu-container bg-white rounded-xl shadow-lg p-2 z-50 transition-all duration-300">
                    <div class="submenu-arrow-up"></div>
                    <div class="grid grid-cols-2 gap-2 relative z-10">
                        {volist name="item.children" id="child"}
                        <a href="{$child.href ?? '#'}" class="submenu-item p-2 rounded-lg hover:bg-gray-50 flex flex-col items-center transition-all duration-300">
                            <div class="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mb-1 transition-transform duration-300">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="{eq name='key+1' value='1'}#4361ee{/eq}{eq name='key+1' value='2'}#0ea5e9{/eq}{eq name='key+1' value='3'}#f44f61{/eq}{eq name='key+1' value='4'}#0abf53{/eq}{eq name='key+1' value='5'}#f7b924{/eq}{gt name='key+1' value='5'}#8b5cf6{/gt}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                                    <polyline points="9 18 15 12 9 6"></polyline>
                                </svg>
                            </div>
                            <span class="text-xs text-gray-700">{$child.name}</span>
                        </a>
                        {/volist}
                    </div>
                </div>
            </div>
            {/if}
            {/volist}
        </div>

        <!-- 添加新的子菜单容器样式 -->
        <style>
            /* 子菜单容器 */
            .submenus-container {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                pointer-events: none;
                z-index: 1000;
            }
            
            .submenus-container .submenu-wrapper {
                position: fixed !important;
                pointer-events: auto;
                opacity: 0;
                transition: opacity 0.3s ease, transform 0.3s ease;
            }
            
            .submenus-container .submenu-wrapper.active {
                display: block !important;
                opacity: 1;
                transform: translateX(-50%) translateY(0px);
            }
            
            /* 修复子菜单箭头样式 */
            .submenu-arrow-up {
                position: absolute;
                width: 16px;
                height: 16px;
                bottom: -8px;
                left: 50%;
                transform: translateX(-50%) rotate(180deg);
                background-color: white;
                clip-path: polygon(50% 0%, 100% 100%, 0% 100%);
                z-index: 1;
            }
        </style>

        <!-- 修改子菜单JavaScript逻辑 -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // 初始化滚动进度条
                initScrollProgressBar();
                
                // 初始化子菜单
                initSubmenus();
                
                // 添加调试信息
                console.log('DOM完全加载，初始化完成');
            });
            
            function initScrollProgressBar() {
                const scrollContainer = document.getElementById('nav-scroll-container');
                const progressBar = document.getElementById('scrollProgressBar');
                
                if (!scrollContainer || !progressBar) return;
                
                // 更新进度条宽度的函数
                const updateProgressBar = function() {
                    const scrollPosition = scrollContainer.scrollLeft;
                    const maxScroll = scrollContainer.scrollWidth - scrollContainer.clientWidth;
                    
                    // 计算滚动百分比
                    const scrollPercentage = maxScroll > 0 ? (scrollPosition / maxScroll) * 100 : 0;
                    
                    // 更新进度条宽度
                    progressBar.style.width = scrollPercentage + '%';
                };
                
                // 初始更新一次
                updateProgressBar();
                
                // 添加滚动事件监听
                scrollContainer.addEventListener('scroll', function() {
                    requestAnimationFrame(updateProgressBar);
                }, { passive: true });
                
                // 添加窗口大小变化事件
                window.addEventListener('resize', function() {
                    setTimeout(updateProgressBar, 100);
                });
                
                // 点击进度条跳转到相应位置
                const progressContainer = document.querySelector('.scroll-progress-container');
                if (progressContainer) {
                    progressContainer.addEventListener('click', function(e) {
                        const rect = this.getBoundingClientRect();
                        const clickPosition = (e.clientX - rect.left) / rect.width;
                        
                        // 计算滚动位置
                        const targetScrollPosition = clickPosition * (scrollContainer.scrollWidth - scrollContainer.clientWidth);
                        
                        // 平滑滚动到目标位置
                        scrollContainer.scrollTo({
                            left: targetScrollPosition,
                            behavior: 'smooth'
                        });
                    });
                }
            }
            
            function initSubmenus() {
                // 性能工具对象，包含防抖和节流功能
                const performanceUtils = {
                    // 防抖函数：延迟执行函数直到停止触发一段时间后
                    debounce: function(callback, delay) {
                        let timer;
                        return function() {
                            const context = this;
                            const args = arguments;
                            clearTimeout(timer);
                            timer = setTimeout(function() {
                                callback.apply(context, args);
                            }, delay);
                        };
                    },
                    
                    // 节流函数：限制函数的执行频率
                    throttle: function(callback, limit) {
                        let waiting = false;
                        return function() {
                            if (!waiting) {
                                callback.apply(this, arguments);
                                waiting = true;
                                setTimeout(function() {
                                    waiting = false;
                                }, limit);
                            }
                        };
                    }
                };
                
                // 获取所有带子菜单的菜单项
                const parentMenus = document.querySelectorAll('.parent-menu');
                console.log('找到父菜单项数量:', parentMenus.length);
                
                const submenus = document.querySelectorAll('.submenu-wrapper');
                console.log('找到子菜单数量:', submenus.length);
                
                // 定义全局状态变量，跟踪当前打开的子菜单
                let currentOpenMenu = null;
                let currentOpenSubmenu = null;
                
                parentMenus.forEach(menu => {
                    const mainItem = menu.querySelector('.nav-main-item');
                    const menuId = menu.getAttribute('data-submenu-id');
                    console.log('处理菜单项:', menuId, menu);
                    
                    const submenu = document.querySelector(`.submenu-wrapper[data-for-menu="${menuId}"]`);
                    
                    if (!mainItem || !submenu) {
                        console.warn('找不到主菜单项或子菜单:', menuId);
                        return;
                    }
                    
                    console.log('成功匹配菜单和子菜单:', menuId);
                    
                    // 为所有菜单项添加点击事件
                    mainItem.addEventListener('click', function(e) {
                        console.log('菜单点击:', menuId);
                        
                        // 阻止默认行为，防止导航
                        e.preventDefault();
                        e.stopPropagation();
                        
                        // 判断是否已经是当前打开的菜单
                        const isCurrentMenuOpen = menu.classList.contains('active');
                        console.log('当前菜单是否已打开:', isCurrentMenuOpen);
                        
                        // 关闭其他打开的子菜单
                        parentMenus.forEach(item => {
                            if (item !== menu) {
                                item.classList.remove('active');
                            }
                        });
                        
                        // 隐藏所有子菜单
                        submenus.forEach(sub => {
                            if (sub !== submenu) {
                                sub.style.display = 'none';
                                sub.classList.remove('active');
                            }
                        });
                        
                        // 切换当前菜单的打开状态
                        menu.classList.toggle('active');
                        
                        // 更新当前打开的菜单状态
                        if (menu.classList.contains('active')) {
                            console.log('激活菜单:', menuId);
                            currentOpenMenu = menu;
                            currentOpenSubmenu = submenu;
                            
                            // 显示当前子菜单
                            submenu.style.display = 'block';
                            
                            // 调整子菜单位置
                            adjustSubmenuPosition(menu, submenu);
                            
                            // 延迟添加active类，确保位置调整后再触发动画
                            setTimeout(() => {
                                submenu.classList.add('active');
                            }, 10);
                            
                            // 添加全局点击事件，点击外部区域关闭菜单
                            setTimeout(() => {
                                document.addEventListener('click', handleOutsideClick);
                            }, 10);
                        } else {
                            console.log('关闭菜单:', menuId);
                            currentOpenMenu = null;
                            currentOpenSubmenu = null;
                            submenu.classList.remove('active');
                            setTimeout(() => {
                                submenu.style.display = 'none';
                            }, 300); // 等待动画完成后隐藏
                        }
                        
                        // 如果不是第一次点击，并且是同一菜单项，允许导航
                        if (isCurrentMenuOpen) {
                            // 点击已打开的菜单项，跳转到其链接
                            const href = mainItem.getAttribute('href');
                            if (href && href !== 'javascript:;' && href !== '#') {
                                window.location.href = href;
                            }
                        }
                    });
                    
                    // 初始化时调整一次子菜单位置
                    setTimeout(() => {
                        submenu.style.display = 'none'; // 确保初始状态是隐藏的
                    }, 100);
                    
                    // 使用防抖函数优化窗口大小变化事件
                    window.addEventListener('resize', performanceUtils.debounce(() => {
                        if (menu.classList.contains('active') && currentOpenSubmenu === submenu) {
                            adjustSubmenuPosition(menu, submenu);
                        }
                    }, 200));
                });
                
                // 处理点击外部区域关闭子菜单
                function handleOutsideClick(e) {
                    console.log('外部点击');
                    if (currentOpenMenu && !currentOpenMenu.contains(e.target) && 
                        currentOpenSubmenu && !currentOpenSubmenu.contains(e.target)) {
                        console.log('关闭打开的菜单');
                        currentOpenMenu.classList.remove('active');
                        if (currentOpenSubmenu) {
                            currentOpenSubmenu.classList.remove('active');
                            setTimeout(() => {
                                currentOpenSubmenu.style.display = 'none';
                            }, 300); // 等待动画完成后隐藏
                        }
                        currentOpenMenu = null;
                        currentOpenSubmenu = null;
                        document.removeEventListener('click', handleOutsideClick);
                    }
                }
                
                // 调整子菜单位置，确保不超出屏幕边界
                function adjustSubmenuPosition(menu, submenu) {
                    if (!menu || !submenu) return;
                    console.log('调整子菜单位置');
                    
                    // 获取菜单项的位置信息
                    const menuRect = menu.getBoundingClientRect();
                    console.log('菜单位置:', menuRect);
                    
                    // 设置子菜单的初始位置 - 放在菜单项的正上方
                    submenu.style.position = 'fixed';
                    submenu.style.left = menuRect.left + (menuRect.width / 2) + 'px';
                    submenu.style.bottom = (window.innerHeight - menuRect.top) + 'px';
                    submenu.style.transform = 'translateX(-50%) translateY(15px)'; // 初始偏移，用于动画
                    
                    // 强制子菜单向上显示
                    const arrow = submenu.querySelector('.submenu-arrow-up');
                    if (arrow) {
                        arrow.style.top = 'auto';
                        arrow.style.bottom = '-8px';
                        arrow.style.left = '50%';
                        arrow.style.transform = 'translateX(-50%) rotate(180deg)';
                    }
                    
                    // 获取子菜单的尺寸
                    const submenuRect = submenu.getBoundingClientRect();
                    console.log('子菜单位置:', submenuRect);
                    
                    // 检查是否超出右侧边界
                    if (submenuRect.right > window.innerWidth - 10) {
                        const rightOverflow = submenuRect.right - (window.innerWidth - 10);
                        submenu.style.left = (parseFloat(submenu.style.left) - rightOverflow) + 'px';
                        console.log('右侧溢出调整:', rightOverflow);
                        
                        // 调整箭头位置
                        if (arrow) {
                            const arrowLeft = 50 + ((rightOverflow / submenuRect.width) * 100);
                            arrow.style.left = arrowLeft + '%';
                        }
                    }
                    
                    // 检查是否超出左侧边界
                    if (submenuRect.left < 10) {
                        const leftOverflow = 10 - submenuRect.left;
                        submenu.style.left = (parseFloat(submenu.style.left) + leftOverflow) + 'px';
                        console.log('左侧溢出调整:', leftOverflow);
                        
                        // 调整箭头位置
                        if (arrow) {
                            const arrowLeft = 50 - ((leftOverflow / submenuRect.width) * 100);
                            arrow.style.left = arrowLeft + '%';
                        }
                    }
                    
                    // 检查是否超出顶部边界
                    if (submenuRect.top < 10) {
                        // 如果上方空间不足，调整最大高度
                        const maxHeight = menuRect.top - 20;
                        submenu.style.maxHeight = maxHeight + 'px';
                        submenu.style.overflow = 'auto';
                        console.log('顶部溢出调整, 最大高度:', maxHeight);
                    }
                    
                    console.log('子菜单位置调整完成');
                }
            }
        </script>

        <!-- 添加悬浮按钮 -->
        <div class="floating-btn touch-effect">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="8" x2="12" y2="16"></line>
                <line x1="8" y1="12" x2="16" y2="12"></line>
            </svg>
        </div>
    </div>

    <script>
        // 使用即时执行函数避免全局命名空间污染
        (function() {
            // 定义性能优化的工具函数
            const performanceUtils = {
                // 节流函数 - 限制函数执行频率
                throttle: function(callback, delay) {
                    let lastTime = 0;
                    return function() {
                        const now = new Date().getTime();
                        if (now - lastTime >= delay) {
                            callback.apply(this, arguments);
                            lastTime = now;
                        }
                    };
                },
                
                // 防抖函数 - 延迟函数执行直到操作停止
                debounce: function(callback, delay) {
                    let timer;
                    return function() {
                        clearTimeout(timer);
                        timer = setTimeout(() => {
                            callback.apply(this, arguments);
                        }, delay);
                    };
                },
                
                // 懒加载函数 - 延迟加载非关键资源
                lazyLoad: function(selector, options = {}) {
                    const elements = document.querySelectorAll(selector);
                    
                    if ('IntersectionObserver' in window) {
                        const observer = new IntersectionObserver((entries) => {
                            entries.forEach(entry => {
                                if (entry.isIntersecting) {
                                    const element = entry.target;
                                    if (element.dataset.src) {
                                        element.src = element.dataset.src;
                                        element.removeAttribute('data-src');
                                    }
                                    observer.unobserve(element);
                                }
                            });
                        }, options);
                        
                        elements.forEach(element => {
                            observer.observe(element);
                        });
                    } else {
                        // 兼容不支持IntersectionObserver的浏览器
                        elements.forEach(element => {
                            if (element.dataset.src) {
                                element.src = element.dataset.src;
                                element.removeAttribute('data-src');
                            }
                        });
                    }
                }
            };
            
            // 页面加载优化
            function initApp() {
                // 减少初始加载时间
                setTimeout(function() {
                    document.querySelector('.page-loader').classList.add('loaded');
                }, 800); // 从1500ms减少到800ms
                
                // 使用requestAnimationFrame处理动画，提高性能
                requestAnimationFrame(() => {
                    // 启用淡入效果
                    setTimeout(function() {
                        document.querySelectorAll('.fade-in').forEach(function(element) {
                            element.classList.add('visible');
                        });
                    }, 1000); // 从1800ms减少到1000ms
                    
                    // 使用分批处理减少一次性操作过多元素导致的性能问题
                    const staggerItems = document.querySelectorAll('.stagger-item');
                    const batchSize = 5; // 每批处理5个元素
                    
                    for (let i = 0; i < staggerItems.length; i += batchSize) {
                        const batch = Array.from(staggerItems).slice(i, i + batchSize);
                        setTimeout(() => {
                            batch.forEach((element, index) => {
                                setTimeout(() => {
                                    element.classList.add('visible');
                                }, 50 * index);
                            });
                        }, 1200 + (i * 10)); // 从2000ms减少到1200ms起，并减少每批次间隔
                    }
                    
                    // 添加商家卡片入场动画
                    setTimeout(() => {
                        document.querySelector('.merchant-card-enter')?.classList.add('visible');
                    }, 1500);
                    
                    // 添加波浪动效
                    animateWaves();
                });
                
                // 初始化搜索框功能
                initSearchBox();
                
                // 初始化触感反馈
                initTouchFeedback();
                
                // 懒加载图片
                performanceUtils.lazyLoad('[data-src]');
                
                // 使用IntersectionObserver优化滚动检测
                initIntersectionObserver();
                
                // 其他初始化操作...
                initOtherFeatures();
                
                // 初始化导航栏和子菜单功能
                initNavigation();
                
                // 初始化子菜单功能
                initSubmenus();
                
                // 商家按钮特效
                initMerchantEffects();
            }
            
            // 初始化商家中心按钮特效
            function initMerchantEffects() {
                const merchantBtn = document.querySelector('.merchant-btn');
                if (merchantBtn) {
                    merchantBtn.addEventListener('mouseenter', () => {
                        merchantBtn.classList.add('pulse-animation');
                    });
                    
                    merchantBtn.addEventListener('mouseleave', () => {
                        setTimeout(() => {
                            merchantBtn.classList.remove('pulse-animation');
                        }, 300);
                    });
                }
                
                // 为商家入口添加动画
                const merchantEntry = document.querySelector('.merchant-entry');
                if (merchantEntry) {
                    merchantEntry.addEventListener('click', function(e) {
                        // 创建点击波纹效果
                        const circle = document.createElement('span');
                        const diameter = Math.max(this.clientWidth, this.clientHeight);
                        const radius = diameter / 2;
                        
                        circle.style.width = circle.style.height = `${diameter}px`;
                        circle.style.left = `${e.clientX - this.offsetLeft - radius}px`;
                        circle.style.top = `${e.clientY - this.offsetTop - radius}px`;
                        circle.classList.add('ripple');
                        
                        const ripple = this.querySelector('.ripple');
                        if (ripple) {
                            ripple.remove();
                        }
                        
                        this.appendChild(circle);
                    });
                }
            }
            
            // 波浪动画
            function animateWaves() {
                const waveBg = document.querySelector('.wave-bg');
                if (waveBg) {
                    waveBg.style.animation = 'waveAnimation 8s linear infinite';
                }
            }
            
            // 搜索框初始化
            function initSearchBox() {
                const searchInput = document.getElementById('searchInput');
                const searchHistory = document.getElementById('searchHistory');
                const voiceSearch = document.getElementById('voiceSearch');
                
                if (!searchInput || !searchHistory || !voiceSearch) return;
                
                // 点击搜索框显示历史记录
                searchInput.addEventListener('focus', function() {
                    searchHistory.classList.add('active');
                });
                
                // 点击外部区域隐藏历史记录 - 使用事件委托提高性能
                document.addEventListener('click', function(e) {
                    if (!searchInput.contains(e.target) && !searchHistory.contains(e.target)) {
                        searchHistory.classList.remove('active');
                    }
                });
                
                // 语音搜索功能
                let isRecording = false;
                
                voiceSearch.addEventListener('click', function() {
                    if (!isRecording) {
                        startVoiceSearch();
                    } else {
                        stopVoiceSearch();
                    }
                });
                
                function startVoiceSearch() {
                    // 检查浏览器是否支持语音识别
                    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                        isRecording = true;
                        voiceSearch.classList.add('voice-active');
                        voiceSearch.classList.add('pulse-animation');
                        
                        // 提示用户正在录音
                        searchInput.placeholder = "正在聆听...";
                        
                        // 实际语音识别代码将在这里实现
                        // 这里是模拟语音识别过程
                        setTimeout(function() {
                            stopVoiceSearch();
                            searchInput.value = "模拟语音识别结果";
                        }, 3000);
                    } else {
                        alert("您的浏览器不支持语音识别功能");
                    }
                }
                
                function stopVoiceSearch() {
                    isRecording = false;
                    voiceSearch.classList.remove('voice-active');
                    voiceSearch.classList.remove('pulse-animation');
                    searchInput.placeholder = "搜索卡片、订单、服务...";
                }
            
            // 搜索历史点击事件 - 使用事件委托减少事件监听器数量
            searchHistory.addEventListener('click', function(e) {
                const historyItem = e.target.closest('.history-item');
                if (historyItem) {
                    const text = historyItem.querySelector('span').textContent;
                    searchInput.value = text;
                    searchHistory.classList.remove('active');
                    // 这里可以触发搜索操作
                }
            });
        }
        
        // 触感反馈初始化 - 使用事件委托减少事件监听器数量
        function initTouchFeedback() {
            let hasInteracted = false;
            document.body.addEventListener('click', function() {
                hasInteracted = true;
            });
            
            // 使用事件委托代替多个单独的事件监听器
            document.body.addEventListener('touchstart', function(e) {
                if (!hasInteracted || !navigator.vibrate || typeof navigator.vibrate !== 'function') return;
                
                const target = e.target.closest('.card, .icon-circle, .nav-item, .user-btn');
                if (target) {
                    try {
                        navigator.vibrate(10); // 轻微振动10毫秒
                    } catch (e) {
                        console.log('振动API不可用');
                    }
                }
            }, { passive: true }); // 使用passive参数提高滚动性能
        }
        
        // 滚动优化 - 使用IntersectionObserver代替滚动事件监听
        function initIntersectionObserver() {
            if ('IntersectionObserver' in window) {
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.classList.add('visible');
                            observer.unobserve(entry.target);
                        }
                    });
                }, {
                    threshold: 0.1,
                    rootMargin: '20px'
                });
                
                document.querySelectorAll('.fade-in:not(.visible), .stagger-item:not(.visible)').forEach(element => {
                    observer.observe(element);
                });
            }
        }
        
        // 其他初始化函数
        function initOtherFeatures() {
            // 横向滚动导航指示器更新 - 使用节流函数优化滚动处理
            const scrollContainer = document.querySelector('.scroll-container');
            const scrollDots = document.querySelectorAll('.scroll-dot');
            
            if (scrollContainer && scrollDots.length > 0) {
                const updateScrollIndicator = performanceUtils.throttle(() => {
                    const scrollPosition = scrollContainer.scrollLeft;
                    const maxScroll = scrollContainer.scrollWidth - scrollContainer.clientWidth;
                    const scrollPercentage = scrollPosition / maxScroll;
                    
                    const activeDotIndex = Math.min(
                        Math.floor(scrollPercentage * scrollDots.length),
                        scrollDots.length - 1
                    );
                    
                    // 使用requestAnimationFrame确保平滑更新UI
                    requestAnimationFrame(() => {
                        scrollDots.forEach((dot, index) => {
                            dot.classList.toggle('active', index === activeDotIndex);
                        });
                    });
                }, 100); // 100ms节流
                
                scrollContainer.addEventListener('scroll', updateScrollIndicator, { passive: true });
                
                // 点击指示器滚动到对应位置 - 使用事件委托
                const dotContainer = scrollDots[0].parentElement;
                dotContainer.addEventListener('click', (e) => {
                    const dot = e.target.closest('.scroll-dot');
                    if (!dot) return;
                    
                    const index = Array.from(scrollDots).indexOf(dot);
                    if (index !== -1) {
                        const scrollPos = (index / (scrollDots.length - 1)) * (scrollContainer.scrollWidth - scrollContainer.clientWidth);
                        scrollContainer.scrollTo({ left: scrollPos, behavior: 'smooth' });
                    }
                });
            }
        }
        
        // 页面加载完成后初始化应用
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initApp);
        } else {
            initApp();
        }
        
        // 添加页面可见性API支持，在页面隐藏时暂停不必要的动画和操作
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                // 页面不可见时暂停不必要的动画和操作
                document.body.classList.add('page-hidden');
            } else {
                // 页面可见时恢复动画和操作
                document.body.classList.remove('page-hidden');
            }
        });
        
        // 监听网络状态，在网络不佳时调整加载策略
        if ('connection' in navigator) {
            const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
            if (connection) {
                function updateNetworkStatus() {
                    if (connection.saveData || connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
                        // 网络环境较差，减少加载内容和动画
                        document.body.classList.add('low-bandwidth');
                    } else {
                        document.body.classList.remove('low-bandwidth');
                    }
                }
                
                updateNetworkStatus();
                connection.addEventListener('change', updateNetworkStatus);
            }
        }
        
        // 初始化导航栏和子菜单功能
        function initNavigation() {
            const navItems = document.querySelectorAll('.nav-item');
            let activeSubmenu = null;
            let touchStartY = 0;
            
            // 处理导航项点击事件
            navItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    const navId = this.getAttribute('data-nav-id');
                    const submenu = document.getElementById(`submenu-${navId}`);
                    
                    // 如果没有子菜单，就直接激活该导航项
                    if (!submenu) {
                        activateNavItem(this);
                        return;
                    }
                    
                    // 如果子菜单已经激活，就关闭它
                    if (submenu.classList.contains('active')) {
                        submenu.classList.remove('active');
                        activeSubmenu = null;
                        return;
                    }
                    
                    // 关闭其他打开的子菜单
                    if (activeSubmenu) {
                        activeSubmenu.classList.remove('active');
                    }
                    
                    // 打开当前子菜单
                    submenu.classList.add('active');
                    activeSubmenu = submenu;
                    e.stopPropagation();
                });
            });
            
            // 点击页面其他区域关闭子菜单
            document.addEventListener('click', function(e) {
                if (activeSubmenu && !e.target.closest('.nav-submenu') && !e.target.closest('.nav-item')) {
                    activeSubmenu.classList.remove('active');
                    activeSubmenu = null;
                }
            });
            
            // 处理指示器点击事件
            const indicators = document.querySelectorAll('.bottom-nav-indicator');
            indicators.forEach(indicator => {
                const dots = indicator.querySelectorAll('.bottom-nav-dot');
                dots.forEach((dot, index) => {
                    dot.addEventListener('click', function(e) {
                        // 重置所有指示器
                        dots.forEach(d => d.classList.remove('active'));
                        // 激活当前指示器
                        this.classList.add('active');
                        e.stopPropagation();
                    });
                });
            });
            
            // 激活导航项
            function activateNavItem(item) {
                // 移除其他导航项的active类
                navItems.forEach(navItem => {
                    navItem.classList.remove('active');
                });
                
                // 添加active类到当前项
                item.classList.add('active');
            }
        }
    })();
</script>
</body>
</html>