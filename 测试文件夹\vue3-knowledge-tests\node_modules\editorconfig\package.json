{"name": "editorconfig", "version": "1.0.4", "description": "EditorConfig File Locator and Interpreter for Node.js", "keywords": ["editorconfig", "core"], "main": "./lib/index.js", "contributors": ["<PERSON> (topbug.net)", "<PERSON> (https://github.com/jedmao/)", "<PERSON> (http://treyhunner.com)", "<PERSON> (https://github.com/hildjj/)"], "directories": {"bin": "./bin", "lib": "./lib"}, "scripts": {"clean": "rimraf lib cmake_install.cmake CTestTestfile.cmake Makefile", "prebuild": "npm run clean", "build": "cmake . && tsc", "pretest": "npm run build && npm run lint", "test": "npm run test:all", "test:all": "mocha && ctest . --preset Test", "precoverage": "npm run build -- --inlineSourceMap", "coverage": "c8 npm run test:all", "postcoverage": "npm run build", "ci": "npm run coverage -- -- -VV --output-on-failure", "lint": "eslint . --ext ts", "prepub": "npm run lint && npm run build", "pub": "npm publish"}, "repository": {"type": "git", "url": "git://github.com/editorconfig/editorconfig-core-js.git"}, "bugs": "https://github.com/editorconfig/editorconfig-core-js/issues", "author": "EditorConfig Team", "license": "MIT", "dependencies": {"@one-ini/wasm": "0.1.1", "commander": "^10.0.0", "minimatch": "9.0.1", "semver": "^7.5.3"}, "devDependencies": {"@types/mocha": "^10.0.1", "@types/node": "^20.3.1", "@types/semver": "^7.5.0", "@typescript-eslint/eslint-plugin": "5.60.0", "@typescript-eslint/parser": "5.60.0", "c8": "8.0.0", "eslint": "8.43.0", "eslint-plugin-jsdoc": "46.2.6", "mocha": "^10.2.0", "rimraf": "^5.0.1", "should": "^13.2.3", "typescript": "^5.1.3"}, "engines": {"node": ">=14"}}