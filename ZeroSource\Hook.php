<?php

namespace plugin\ZeroSource;

use think\facade\Db;

class Hook
{
    public function handle()
    {
        $status = intval(plugconf("ZeroSource.status") ?? 0);
        $disableStatus = intval(plugconf("ZeroSource.disableStatus") ?? 0);

        // 当 status 为 1 时，执行隐藏处理
        if ($status == 1) {
            $this->executeHideProcess();
        }

        // 当 disableStatus 为 1 时，执行商品状态检查
        if ($disableStatus == 1) {
            $this->executeDisableStatusCheck();
        }
    }

    // 新增隐藏处理的公共方法
    private function handleHideStatus()
    {
        $prefix = config('database.connections.mysql.prefix');
        $userTable = $prefix . 'user';
        $goodsTable = $prefix . 'goods';
        $goodsPoolTable = $prefix . 'goods_pool';
        $goodsCardStorageTable = $prefix . 'goods_card_storage_0';

        // 获取所有用户
        $users = Db::table($userTable)->select();

        foreach ($users as $user) {
            $userId = $user['id'];
            $goods = Db::table($goodsTable)->where('user_id', $userId)->select();

            $allAgentStatusZero = true;
            $hasCard = false;
            $hasOtherType = false;
            $hasValidCardStorage = false;

            foreach ($goods as $item) {
                if ($item['agent_status'] == 0) {
                    if ($item['goods_type'] == 'card') {
                        $hasCard = true;
                        // 检查卡密库存
                        $cardStorageQuery = Db::table($goodsCardStorageTable)
                            ->where('goods_id', $item['id']);
                            
                        // 检查是否存在未删除且状态为0的记录
                        $hasActiveCard = $cardStorageQuery->where('delete_time', null)
                            ->where('status', 0)
                            ->find();
                            
                        if (!$hasActiveCard) {
                            // 如果没有未删除且状态为0的记录，则检查状态为1的记录
                            $hasValidCard = $cardStorageQuery->where('status', 1)->find();
                            if ($hasValidCard) {
                                $hasValidCardStorage = true;
                            }
                        } else {
                            $hasValidCardStorage = true;
                        }
                    } else {
                        $hasOtherType = true;
                    }
                } else {
                    $allAgentStatusZero = false;
                }
            }

            // 如果该用户没有商品或(所有商品的 agent_status 都为 0 且没有有效的卡密库存)，更新 status 为 0
            if (empty($goods) || ($allAgentStatusZero && !$hasValidCardStorage)) {
                Db::table($goodsPoolTable)->where('user_id', $userId)->update(['status' => 0]);
            }
        }
    }

    // 新增方法：执行隐藏处理
    private function executeHideProcess()
    {
        $hideTime = plugconf("ZeroSource.hideTime") ?? '00:00';

        if (date("H:i") == $hideTime) {
            $this->handleHideStatus();
        }
    }

    // 新增方法：执行商品状态检查
    private function executeDisableStatusCheck()
    {
        $hideTime = plugconf("ZeroSource.hideTime") ?? '00:00';

        if (date("H:i") == $hideTime) {
            $prefix = config('database.connections.mysql.prefix');
            $goodsTable = $prefix . 'goods';
            $goodsCardTable = $prefix . 'goods_card';
            $goodsCardStorageTable = $prefix . 'goods_card_storage_0';

            $goodsList = Db::table($goodsTable)->select();

            foreach ($goodsList as $goodsItem) {
                $goodsId = $goodsItem['id'];
                $userId = $goodsItem['user_id'];
                $goodsType = $goodsItem['goods_type'];

                // 仅对 card 类型商品进行操作
                if ($goodsType == 'card') {
                    $goodsCard = Db::table($goodsCardTable)->where('goods_id', $goodsId)->select();

                    if (!empty($goodsCard)) {
                        $user = Db::table($prefix . 'user')->where('id', $userId)->find();

                        if ($user) {
                            $cardStorageQuery = Db::table($goodsCardStorageTable)
                                ->where('user_id', $user['id'])
                                ->where('goods_id', $goodsId);
                                
                            // 检查是否存在未删除且状态为0的记录
                            $hasActiveCard = $cardStorageQuery->where('delete_time', null)
                                ->where('status', 0)
                                ->find();
                                
                            if (!$hasActiveCard) {
                                // 如果没有未删除且状态为0的记录，则检查状态为1的记录
                                $hasValidCard = $cardStorageQuery->where('status', 1)->find();
                                if (!$hasValidCard) {
                                    // 如果既没有未删除且状态为0的记录，也没有状态为1的记录，则更新agent_status
                                    Db::table($goodsTable)->where('id', $goodsId)->update(['agent_status' => 0]);
                                    // 重新执行隐藏处理
                                    $this->handleHideStatus();
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
