<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下载飞鸟VPN - 永远能连上的海外加速器</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .download-page {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .download-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 50px;
            max-width: 800px;
            width: 100%;
            text-align: center;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
        }
        
        .download-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #3742fa, #a55eea);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .download-subtitle {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 40px;
        }
        
        .platform-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }
        
        .platform-card {
            background: #fff;
            border-radius: 15px;
            padding: 30px 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .platform-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        .platform-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            color: #3742fa;
        }
        
        .platform-name {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }
        
        .platform-desc {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 20px;
        }
        
        .download-btn {
            background: linear-gradient(45deg, #3742fa, #a55eea);
            color: #fff;
            border: none;
            border-radius: 25px;
            padding: 12px 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(55, 66, 250, 0.3);
        }
        
        .qr-section {
            margin: 40px 0;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 15px;
        }
        
        .qr-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
        }
        
        .qr-codes {
            display: flex;
            justify-content: center;
            gap: 40px;
            flex-wrap: wrap;
        }
        
        .qr-item {
            text-align: center;
        }
        
        .qr-code {
            width: 120px;
            height: 120px;
            background: #fff;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            font-size: 3rem;
            color: #3742fa;
        }
        
        .qr-label {
            font-size: 0.9rem;
            color: #666;
            font-weight: 600;
        }
        
        .features-highlight {
            margin: 40px 0;
            text-align: left;
        }
        
        .features-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
            text-align: center;
        }
        
        .features-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        
        .feature-highlight {
            display: flex;
            align-items: center;
            padding: 15px;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
        
        .feature-highlight i {
            font-size: 1.5rem;
            color: #3742fa;
            margin-right: 15px;
        }
        
        .feature-text {
            font-size: 1rem;
            color: #333;
        }
        
        .back-link {
            margin-top: 30px;
        }
        
        .back-btn {
            background: transparent;
            color: #3742fa;
            border: 2px solid #3742fa;
            border-radius: 25px;
            padding: 10px 30px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }
        
        .back-btn:hover {
            background: #3742fa;
            color: #fff;
            transform: translateY(-2px);
        }

        /* Fallback styles for when Font Awesome fails to load */
        .fa-fallback {
            font-family: Arial, sans-serif;
            font-weight: bold;
        }

        /* Hide icons if Font Awesome doesn't load properly */
        @supports not (font-family: "Font Awesome 6 Free") {
            .fab, .fas, .fa {
                display: none;
            }
        }
        
        .download-stats {
            display: flex;
            justify-content: center;
            gap: 40px;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #3742fa;
            display: block;
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: #666;
        }
        
        @media (max-width: 768px) {
            .download-container {
                padding: 30px 20px;
                margin: 10px;
            }
            
            .download-title {
                font-size: 2rem;
            }
            
            .platform-grid {
                grid-template-columns: 1fr;
            }
            
            .qr-codes {
                gap: 20px;
            }
            
            .download-stats {
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="download-page">
        <div class="download-container">
            <h1 class="download-title">下载飞鸟VPN</h1>
            <p class="download-subtitle">选择适合您设备的版本，开始安全的网络体验</p>
            
            <div class="download-stats">
                <div class="stat-item">
                    <span class="stat-number">3000万+</span>
                    <span class="stat-label">全球用户</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">99.9%</span>
                    <span class="stat-label">连接成功率</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">24/7</span>
                    <span class="stat-label">技术支持</span>
                </div>
            </div>
            
            <div class="platform-grid">
                <div class="platform-card" onclick="downloadApp('windows')">
                    <div class="platform-icon">
                        <i class="fab fa-windows"></i>
                    </div>
                    <div class="platform-name">Windows</div>
                    <div class="platform-desc">支持 Windows 7/8/10/11</div>
                    <button class="download-btn">立即下载</button>
                </div>
                
                <div class="platform-card" onclick="downloadApp('mac')">
                    <div class="platform-icon">
                        <i class="fab fa-apple"></i>
                    </div>
                    <div class="platform-name">macOS</div>
                    <div class="platform-desc">支持 macOS 10.12 及以上</div>
                    <button class="download-btn">立即下载</button>
                </div>
                
                <div class="platform-card" onclick="downloadApp('android')">
                    <div class="platform-icon">
                        <i class="fab fa-android"></i>
                    </div>
                    <div class="platform-name">Android</div>
                    <div class="platform-desc">支持 Android 5.0 及以上</div>
                    <button class="download-btn">立即下载</button>
                </div>
                
                <div class="platform-card" onclick="downloadApp('ios')">
                    <div class="platform-icon">
                        <i class="fab fa-app-store-ios"></i>
                    </div>
                    <div class="platform-name">iOS</div>
                    <div class="platform-desc">支持 iOS 12.0 及以上</div>
                    <button class="download-btn">App Store</button>
                </div>
            </div>
            
            <div class="qr-section">
                <h3 class="qr-title">扫码下载移动版</h3>
                <div class="qr-codes">
                    <div class="qr-item">
                        <div class="qr-code">
                            <i class="fab fa-android"></i>
                        </div>
                        <div class="qr-label">Android版</div>
                    </div>
                    <div class="qr-item">
                        <div class="qr-code">
                            <i class="fab fa-apple"></i>
                        </div>
                        <div class="qr-label">iOS版</div>
                    </div>
                </div>
            </div>
            
            <div class="features-highlight">
                <h3 class="features-title">为什么选择飞鸟VPN？</h3>
                <div class="features-list">
                    <div class="feature-highlight">
                        <i class="fas fa-shield-alt"></i>
                        <span class="feature-text">军用级加密保护</span>
                    </div>
                    <div class="feature-highlight">
                        <i class="fas fa-bolt"></i>
                        <span class="feature-text">极速连接体验</span>
                    </div>
                    <div class="feature-highlight">
                        <i class="fas fa-globe"></i>
                        <span class="feature-text">全球服务器覆盖</span>
                    </div>
                    <div class="feature-highlight">
                        <i class="fas fa-eye-slash"></i>
                        <span class="feature-text">零日志政策</span>
                    </div>
                </div>
            </div>
            
            <div class="back-link">
                <a href="index.html" class="back-btn">
                    <i class="fas fa-arrow-left"></i>
                    返回首页
                </a>
            </div>
        </div>
    </div>

    <script>
        function downloadApp(platform) {
            const downloads = {
                windows: 'https://download.example.com/FeiNiaoVPN-Windows.exe',
                mac: 'https://download.example.com/FeiNiaoVPN-macOS.dmg',
                android: 'https://download.example.com/FeiNiaoVPN-Android.apk',
                ios: 'https://apps.apple.com/app/feiniao-vpn'
            };
            
            // 显示下载提示
            showDownloadToast(platform);
            
            // 模拟下载（实际项目中这里应该是真实的下载链接）
            console.log(`开始下载 ${platform} 版本`);
            
            // 统计下载次数
            trackDownload(platform);
        }
        
        function showDownloadToast(platform) {
            const platformNames = {
                windows: 'Windows',
                mac: 'macOS',
                android: 'Android',
                ios: 'iOS'
            };
            
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(45deg, #3742fa, #a55eea);
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                z-index: 10000;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
                box-shadow: 0 10px 30px rgba(55, 66, 250, 0.3);
            `;
            
            toast.innerHTML = `
                <div style="display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-download"></i>
                    <span>正在准备 ${platformNames[platform]} 版本下载...</span>
                </div>
            `;
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(0)';
            }, 100);
            
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 3000);
        }
        
        function trackDownload(platform) {
            // 这里可以添加下载统计代码
            console.log(`下载统计: ${platform}`);
        }
        
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.platform-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 200);
            });

            // 检查字体加载状态
            checkFontAwesome();
        });

        function checkFontAwesome() {
            // 检查Font Awesome是否正确加载
            const testElement = document.createElement('span');
            testElement.className = 'fas fa-home';
            testElement.style.position = 'absolute';
            testElement.style.left = '-9999px';
            document.body.appendChild(testElement);

            const computedStyle = window.getComputedStyle(testElement);
            const fontFamily = computedStyle.getPropertyValue('font-family');

            if (!fontFamily.includes('Font Awesome')) {
                console.warn('Font Awesome failed to load, using fallback icons');
                // 可以在这里添加替代图标的逻辑
                addFallbackIcons();
            }

            document.body.removeChild(testElement);
        }

        function addFallbackIcons() {
            // 为主要图标添加文字替代
            const iconMappings = {
                'fa-windows': '🪟',
                'fa-apple': '🍎',
                'fa-android': '🤖',
                'fa-app-store-ios': '📱',
                'fa-shield-alt': '🛡️',
                'fa-bolt': '⚡',
                'fa-globe': '🌍',
                'fa-eye-slash': '👁️',
                'fa-download': '⬇️',
                'fa-arrow-left': '←'
            };

            Object.keys(iconMappings).forEach(iconClass => {
                const elements = document.querySelectorAll(`.${iconClass}`);
                elements.forEach(element => {
                    element.textContent = iconMappings[iconClass];
                    element.style.fontFamily = 'Arial, sans-serif';
                });
            });
        }
    </script>
</body>
</html>
