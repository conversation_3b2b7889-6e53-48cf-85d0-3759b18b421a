(function () {
    // 动态调整弹窗字体大小
    function adjustPopupFontSize() {
        const screenWidth = window.innerWidth;
        const popupBody = document.querySelector('.html-popup-body');

        if (!popupBody) return;

        // 根据屏幕宽度动态计算字体大小（减少3px）
        let baseFontSize;
        if (screenWidth >= 1200) {
            baseFontSize = 13;
        } else if (screenWidth >= 901) {
            baseFontSize = 12;
        } else if (screenWidth >= 769) {
            baseFontSize = 11;
        } else if (screenWidth >= 481) {
            baseFontSize = 11;
        } else {
            baseFontSize = 10;
        }

        // 应用字体大小
        popupBody.style.fontSize = baseFontSize + 'px';

        // 调整内容元素的字体大小
        const elements = popupBody.querySelectorAll('p, div, span, a, button');
        elements.forEach(element => {
            if (!element.style.fontSize) {
                element.style.fontSize = baseFontSize + 'px';
            }
        });

        // 调整标题字体大小
        const headers = popupBody.querySelectorAll('h1, h2, h3, h4, h5, h6');
        headers.forEach(header => {
            if (!header.style.fontSize) {
                const headerSize = Math.max(baseFontSize + 2, baseFontSize * 1.2);
                header.style.fontSize = headerSize + 'px';
            }
        });
    }

    // 获取店铺信息
    function getShopInfo() {
        let shopName = '';
        let merchantId = '';
        
        // 1. 从 nickname 元素获取商家名称（优先级最高）
        const nicknameElement = document.querySelector('.nickname');
        if (nicknameElement) {
            shopName = nicknameElement.textContent.trim();
        }

        // 2. 从带有店铺名称class的元素获取
        if (!shopName) {
            const shopNameElements = document.querySelectorAll('.shop-name, .store-name, .merchant-name');
            for (const element of shopNameElements) {
                const text = element.textContent.trim();
                if (text) {
                    shopName = text;
                    break;
                }
            }
        }

        // 3. 从页面标题获取商家名称
        if (!shopName) {
            const pageTitle = document.title.trim();
            if (pageTitle) {
                const index = pageTitle.indexOf('的小店');
                if (index !== -1) {
                    shopName = pageTitle.substring(0, index);
                }
            }
        }

        // 4. 从商家ID属性元素获取
        if (!merchantId) {
            const merchantIdElement = document.querySelector('[data-merchant-id]');
            if (merchantIdElement) {
                merchantId = merchantIdElement.getAttribute('data-merchant-id');
            }
        }

        // 5. 从页面元素中获取商家信息
        if (!shopName || !merchantId) {
            const shopInfoScript = document.querySelector('script[data-shop-info]');
            if (shopInfoScript) {
                try {
                    const shopInfo = JSON.parse(shopInfoScript.textContent);
                    if (!shopName && shopInfo.shopName) {
                        shopName = shopInfo.shopName;
                    }
                    if (!merchantId && shopInfo.merchantId) {
                        merchantId = shopInfo.merchantId;
                    }
                } catch (e) {
                    // 解析商家信息失败，忽略错误
                }
            }
        }
        
        // 6. 从 meta 标签获取
        if (!shopName) {
            const metaTags = [
                document.querySelector('meta[name="shop-name"]'),
                document.querySelector('meta[property="og:site_name"]'),
                document.querySelector('meta[name="author"]')
            ];
            
            for (const tag of metaTags) {
                if (tag && tag.getAttribute('content')) {
                    shopName = tag.getAttribute('content');
                    break;
                }
            }
        }
        
        if (!merchantId) {
            const merchantIdMeta = document.querySelector('meta[name="merchant-id"]');
            if (merchantIdMeta) {
                merchantId = merchantIdMeta.getAttribute('content');
            }
        }
        
        // 7. 从 URL 参数获取（备用方式）
        if (!shopName || !merchantId) {
            const urlParams = new URLSearchParams(window.location.search);
            if (!shopName) {
                const urlShopName = urlParams.get('shop_name');
                if (urlShopName) shopName = urlShopName;
            }
            if (!merchantId) {
                const urlMerchantId = urlParams.get('merchant_id');
                if (urlMerchantId) merchantId = urlMerchantId;
            }
        }

        // 8. 从URL路径中提取可能的店铺名或ID
        if (!shopName || !merchantId) {
            const pathParts = window.location.pathname.split('/');
            for (const part of pathParts) {
                // 如果路径部分看起来像店铺名（包含字母或非ASCII字符）
                if (/[a-zA-Z\u00C0-\u00FF\u0100-\uFFFF]/.test(part) && part.length > 1 && !shopName) {
                    shopName = decodeURIComponent(part);
                }
                // 如果路径部分看起来像ID（纯数字）
                else if (/^\d+$/.test(part) && part.length > 0 && !merchantId) {
                    merchantId = part;
                }
            }
        }

        return { shopName, merchantId };
    }

    // 解码HTML内容（处理JSON转义字符，保持HTML结构）
    function decodeHtmlContent(content) {
        if (!content || typeof content !== 'string') {
            return content;
        }

        // 检查是否包含JSON转义字符
        if (content.includes('\\n') || content.includes('\\"') || content.includes('\\\\')) {
            try {
                // 解码JSON转义字符，保持HTML结构
                return content
                    .replace(/\\n/g, '\n')
                    .replace(/\\r/g, '\r')
                    .replace(/\\t/g, '\t')
                    .replace(/\\"/g, '"')
                    .replace(/\\\\/g, '\\');
            } catch (e) {
                return content;
            }
        }

        return content;
    }

    // 创建HTML弹窗
    function createHtmlPopup(config) {
        if (!config.content) return;

        // 检查是否已存在弹窗
        if (document.querySelector('.html-popup-modal')) {
            return;
        }

        const modal = document.createElement('div');
        modal.className = 'html-popup-modal';

        modal.innerHTML = `
            <div class="html-popup-content">
                <div class="html-popup-header">
                    <h3>${config.title}</h3>
                </div>
                <div class="html-popup-body">
                    ${config.content}
                </div>
                <div class="html-popup-footer">
                    <button class="popup-btn popup-btn-reject">拒绝</button>
                    <button class="popup-btn popup-btn-accept">同意</button>
                </div>
            </div>
        `;

        // 添加样式
        const style = document.createElement('style');
        style.textContent = getPopupStyles(config.enable_scrollbar);
        document.head.appendChild(style);

        // 关闭弹窗函数
        const closeModal = () => {
            modal.classList.remove('show');
            setTimeout(() => {
                modal.remove();
                style.remove();
            }, 300);
        };

        // 关闭按钮已移除

        // 绑定同意按钮事件（支持延迟功能）
        const acceptBtn = modal.querySelector('.popup-btn-accept');

        // 检查是否启用延迟功能
        if (config.enable_agree_delay && config.agree_delay_seconds > 0) {
            let remainingSeconds = config.agree_delay_seconds;

            // 初始状态：禁用按钮并显示倒计时
            acceptBtn.disabled = true;
            acceptBtn.style.opacity = '0.5';
            acceptBtn.style.cursor = 'not-allowed';
            acceptBtn.textContent = `同意 (${remainingSeconds}s)`;

            // 倒计时
            const countdown = setInterval(() => {
                remainingSeconds--;
                if (remainingSeconds > 0) {
                    acceptBtn.textContent = `同意 (${remainingSeconds}s)`;
                } else {
                    // 倒计时结束，启用按钮
                    clearInterval(countdown);
                    acceptBtn.disabled = false;
                    acceptBtn.style.opacity = '1';
                    acceptBtn.style.cursor = 'pointer';
                    acceptBtn.textContent = '同意';
                }
            }, 1000);
        }

        acceptBtn.onclick = () => {
            if (!acceptBtn.disabled) {
                closeModal();
            }
        };

        // 绑定拒绝按钮事件
        modal.querySelector('.popup-btn-reject').onclick = () => {
            closeModal();

            // 根据配置执行拒绝后的操作
            const rejectAction = config.reject_action || 'none';


            switch (rejectAction) {
                case 'redirect':
                    // 跳转到指定页面
                    const rejectUrl = config.reject_url;
                    if (rejectUrl) {

                        window.location.href = rejectUrl;
                    }
                    break;

                case 'close':
                    // 关闭当前网页
                    const closeConfirm = config.close_confirm;
                    if (closeConfirm) {
                        // 显示确认对话框
                        if (confirm('确定要关闭当前网页吗？')) {
                            window.close();
                        }
                    } else {
                        // 直接关闭
                        window.close();
                    }
                    break;

                case 'none':
                default:
                    // 无操作，只关闭弹窗

                    break;
            }
        };

        modal.onclick = (e) => {
            if (e.target === modal) {
                closeModal();
            }
        };

        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                closeModal();
            }
        });

        // 添加到页面并显示
        document.body.appendChild(modal);
        // 强制重绘
        modal.offsetHeight;
        modal.classList.add('show');

        // 调整字体大小
        setTimeout(() => {
            adjustPopupFontSize();
        }, 100);
    }

    // 获取弹窗样式
    function getPopupStyles(enableScrollbar = false) {
        return `
            /* 主容器样式 */
            .html-popup-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.65);
                display: flex;
                justify-content: center;
                ${enableScrollbar ? 'align-items: center;' : 'align-items: flex-start; overflow-y: auto; padding-top: 20px;'}
                z-index: 99999;
                opacity: 0;
                transition: all 0.35s ease;
                padding: 20px;
            }

            /* 内容框 */
            .html-popup-content {
                background: white;
                border-radius: 12px;
                width: auto;
                ${enableScrollbar ? 'height: 500px; max-height: 80vh;' : 'height: auto; max-height: none;'}
                max-width: 90vw;
                min-width: 360px;
                transform: scale(0.9);
                transition: all 0.35s ease;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
                overflow: hidden;
                display: flex;
                flex-direction: column;
            }

            /* 弹窗头部 */
            .html-popup-header {
                padding: 6px 15px;
                background: #f9fafb;
                border-bottom: 1px solid #edf2f7;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            /* 弹窗标题 */
            .html-popup-header h3 {
                margin: 0;
                font-size: 13px;
                color: #60a5fa;
                font-weight: 600;
            }

            /* 关闭按钮已移除 */

            /* 弹窗主体内容区 */
            .html-popup-body {
                padding: 15px;
                ${enableScrollbar ? 'overflow-y: auto; max-height: calc(80vh - 120px);' : 'overflow: visible; height: auto;'}
                ${enableScrollbar ? 'flex: 1;' : 'flex: none;'}
                line-height: 1.5;
                color: #4b5563;
                font-size: 11px;
            }

            /* 弹窗底部按钮区 */
            .html-popup-footer {
                padding: 12px 15px;
                background: #f9fafb;
                border-top: 1px solid #edf2f7;
                display: flex;
                justify-content: center;
                gap: 10px;
            }

            /* 弹窗按钮样式 */
            .popup-btn {
                padding: 8px 20px;
                border: none;
                border-radius: 6px;
                cursor: pointer;
                font-size: 14px;
                font-weight: 500;
                transition: all 0.2s ease;
                min-width: 80px;
            }

            /* 拒绝按钮 */
            .popup-btn-reject {
                background: #f3f4f6;
                color: #6b7280;
                border: 1px solid #d1d5db;
            }

            .popup-btn-reject:hover {
                background: #e5e7eb;
                color: #4b5563;
            }

            /* 同意按钮 */
            .popup-btn-accept {
                background: #3b82f6;
                color: white;
                transition: all 0.3s ease;
            }

            .popup-btn-accept:hover:not(:disabled) {
                background: #2563eb;
            }

            .popup-btn-accept:disabled {
                background: #9ca3af !important;
                color: #d1d5db !important;
                cursor: not-allowed !important;
                opacity: 0.5 !important;
            }

            /* 显示状态 */
            .html-popup-modal.show {
                opacity: 1;
            }
            .html-popup-modal.show .html-popup-content {
                transform: scale(1);
            }

            /* 响应式设计 - 大屏幕 (电脑端) */
            @media (min-width: 1200px) {
                .html-popup-content {
                    width: auto !important;
                    max-width: 80vw;
                    min-width: 500px;
                }
                .html-popup-header h3 {
                    font-size: 15px;
                }
                .html-popup-body {
                    font-size: 13px;
                    line-height: 1.6;
                    padding: 20px;
                }
                .popup-btn {
                    font-size: 16px;
                    padding: 10px 24px;
                }
            }

            /* 响应式设计 - 中等屏幕 (平板横屏) */
            @media (max-width: 1199px) and (min-width: 901px) {
                .html-popup-content {
                    width: auto !important;
                    max-width: 85vw;
                    min-width: 450px;
                }
                .html-popup-header h3 {
                    font-size: 13px;
                }
                .html-popup-body {
                    font-size: 12px;
                    line-height: 1.5;
                }
                .popup-btn {
                    font-size: 15px;
                }
            }

            /* 响应式设计 - 小屏幕 (平板竖屏) */
            @media (max-width: 900px) and (min-width: 769px) {
                .html-popup-content {
                    width: 90% !important;
                    margin: 15px;
                }
                .html-popup-header h3 {
                    font-size: 12px;
                }
                .html-popup-body {
                    font-size: 11px;
                    line-height: 1.5;
                    padding: 18px;
                }
                .popup-btn {
                    font-size: 14px;
                    padding: 9px 20px;
                }
            }

            /* 响应式设计 - 手机端 */
            @media (max-width: 768px) {
                .html-popup-content {
                    width: 95% !important;
                    margin: 10px;
                }
                .html-popup-header h3 {
                    font-size: 11px;
                }
                .html-popup-body {
                    font-size: 11px;
                    line-height: 1.5;
                    padding: 15px;
                }
                .html-popup-footer {
                    padding: 12px 15px;
                    gap: 8px;
                }
                .popup-btn {
                    padding: 10px 16px;
                    font-size: 14px;
                    min-width: 70px;
                }
            }

            /* 响应式设计 - 小手机端 */
            @media (max-width: 480px) {
                .html-popup-content {
                    width: 98% !important;
                    margin: 5px;
                }
                .html-popup-header h3 {
                    font-size: 10px;
                }
                .html-popup-body {
                    font-size: 10px;
                    line-height: 1.4;
                    padding: 12px;
                }
                .html-popup-footer {
                    padding: 10px 12px;
                    gap: 6px;
                }
                .popup-btn {
                    padding: 8px 12px;
                    font-size: 13px;
                    min-width: 60px;
                }
                /* 关闭按钮已移除 */
            }

            /* 弹窗内容响应式字体调整 */
            .html-popup-body {
                /* 基础字体设置 */
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
            }

            /* 内容区域的响应式字体调整 */
            @media (min-width: 1200px) {
                .html-popup-body h1, .html-popup-body h2, .html-popup-body h3 {
                    font-size: 1.3em !important;
                }
                .html-popup-body p, .html-popup-body div, .html-popup-body span {
                    font-size: 13px !important;
                }
                .html-popup-body small {
                    font-size: 11px !important;
                }
            }

            @media (max-width: 1199px) and (min-width: 901px) {
                .html-popup-body h1, .html-popup-body h2, .html-popup-body h3 {
                    font-size: 1.2em !important;
                }
                .html-popup-body p, .html-popup-body div, .html-popup-body span {
                    font-size: 12px !important;
                }
                .html-popup-body small {
                    font-size: 10px !important;
                }
            }

            @media (max-width: 900px) and (min-width: 769px) {
                .html-popup-body h1, .html-popup-body h2, .html-popup-body h3 {
                    font-size: 1.1em !important;
                }
                .html-popup-body p, .html-popup-body div, .html-popup-body span {
                    font-size: 11px !important;
                }
                .html-popup-body small {
                    font-size: 9px !important;
                }
            }

            @media (max-width: 768px) {
                .html-popup-body h1, .html-popup-body h2, .html-popup-body h3 {
                    font-size: 1.1em !important;
                }
                .html-popup-body p, .html-popup-body div, .html-popup-body span {
                    font-size: 11px !important;
                }
                .html-popup-body small {
                    font-size: 9px !important;
                }
            }

            @media (max-width: 480px) {
                .html-popup-body h1, .html-popup-body h2, .html-popup-body h3 {
                    font-size: 1.0em !important;
                }
                .html-popup-body p, .html-popup-body div, .html-popup-body span {
                    font-size: 10px !important;
                }
                .html-popup-body small {
                    font-size: 8px !important;
                }
            }

            /* 按钮和链接的响应式字体调整 */
            @media (min-width: 1200px) {
                .html-popup-body a, .html-popup-body button {
                    font-size: 13px !important;
                }
            }

            @media (max-width: 1199px) and (min-width: 901px) {
                .html-popup-body a, .html-popup-body button {
                    font-size: 12px !important;
                }
            }

            @media (max-width: 900px) and (min-width: 769px) {
                .html-popup-body a, .html-popup-body button {
                    font-size: 11px !important;
                }
            }

            @media (max-width: 768px) {
                .html-popup-body a, .html-popup-body button {
                    font-size: 11px !important;
                }
            }

            @media (max-width: 480px) {
                .html-popup-body a, .html-popup-body button {
                    font-size: 10px !important;
                }
            }

            /* 确保链接样式正常工作 */
            .html-popup-body a {
                cursor: pointer;
            }

            /* 确保按钮样式的链接正常显示 */
            .html-popup-body a[style*="background"] {
                text-decoration: none !important;
            }

            /* 确保居中布局正常工作 */
            .html-popup-body [style*="text-align: center"] {
                text-align: center !important;
            }

            /* 确保内联块元素正常显示 */
            .html-popup-body [style*="display: inline-block"] {
                display: inline-block !important;
            }

            /* 确保块级元素正常显示 */
            .html-popup-body [style*="display: block"] {
                display: block !important;
            }

            /* 确保分割线样式正常 */
            .html-popup-body hr {
                margin: 10px 0;
            }

            /* 确保强调文本正常显示 */
            .html-popup-body strong {
                font-weight: bold;
            }

            /* 确保段落间距正常 */
            .html-popup-body p {
                margin: 8px 0;
            }
        `;
    }

    // 获取并显示弹窗
    function showHtmlPopup() {
        const { shopName, merchantId } = getShopInfo();

        // 如果既没有获取到店铺名称也没有获取到商家ID，则不显示弹窗
        if (!shopName && !merchantId) {
            return;
        }

        // 检查显示频率
        const shouldShow = (popupData) => {
            // 使用商家ID和内容哈希值创建唯一存储键
            const contentHash = hashString(popupData.content);
            const storageKey = `html_popup_${shopName || merchantId}_${contentHash}`;
            const lastShow = localStorage.getItem(storageKey);
            const now = new Date().getTime();

            if (!lastShow) return true;

            const frequency = popupData.frequency || 'once';
            switch (frequency) {
                case 'once':
                    return false; // 已经显示过就不再显示
                case 'login':
                    return true; // 每次访问都显示
                case 'daily':
                    const oneDayMs = 24 * 60 * 60 * 1000;
                    return (now - parseInt(lastShow)) >= oneDayMs;
                case 'weekly':
                    const oneWeekMs = 7 * 24 * 60 * 60 * 1000;
                    return (now - parseInt(lastShow)) >= oneWeekMs;
                default:
                    return true;
            }
        };

        // 简单的字符串哈希函数
        function hashString(str) {
            if (!str) return '0';
            let hash = 0;
            for (let i = 0; i < str.length; i++) {
                const char = str.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // Convert to 32bit integer
            }
            return hash.toString();
        }



        // 构建请求参数
        const params = new URLSearchParams();
        if (shopName) params.append('shop_name', shopName);
        if (merchantId) params.append('merchant_id', merchantId);

        // 发送请求获取弹窗内容
        const baseUrl = window.location.protocol + '//' + window.location.host;
        fetch(baseUrl + '/plugin/Htmlpopup/api/fetchData?' + params.toString(), {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.code === 200 && data.data) {
                // 处理内容
                let content = data.data.content;

                // 如果是内置模板，使用服务器返回的模板内容
                if (data.data.content_type === 'builtin') {
                    // 使用服务器返回的内置模板内容
                    content = data.data.builtin_template_content || data.data.content || '';
                }

                // 解码HTML实体和JSON转义字符
                content = decodeHtmlContent(content);

                // 检查弹窗开关状态和内容
                if (data.data.status === 1 && content) {
                    // 更新数据对象的内容
                    const popupData = { ...data.data, content: content };

                    // 检查是否应该显示
                    if (shouldShow(popupData)) {
                        createHtmlPopup(popupData);

                        // 存储显示时间
                        const contentHash = hashString(content);
                        const storageKey = `html_popup_${shopName || merchantId}_${contentHash}`;
                        localStorage.setItem(storageKey, new Date().getTime().toString());
                    }
                }
            }
        })
        .catch(() => {
            // 错误处理，静默忽略
        });
    }

    // 监听窗口大小变化，重新调整字体大小
    let resizeTimer;
    window.addEventListener('resize', () => {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(() => {
            adjustPopupFontSize();
        }, 150);
    });

    // 监听设备方向变化
    window.addEventListener('orientationchange', () => {
        setTimeout(() => {
            adjustPopupFontSize();
        }, 300);
    });

    // 确保页面完全加载后再显示弹窗
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', showHtmlPopup);
    } else {
        showHtmlPopup();
    }
})();
