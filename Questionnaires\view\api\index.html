<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>问卷与举报管理</title>
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <link rel="icon" href="<?php echo isset($favicon) ? $favicon : '/favicon.ico'; ?>" type="image/x-icon">
    <style>
        body {
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
        }
        .container {
            padding: 20px;
            max-width: 1000px;
            margin: 0 auto;
        }
        .form-card {
            margin-bottom: 20px;
        }
        .form-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .question-type-item {
            margin-bottom: 10px;
            border: 1px solid #ebeef5;
            padding: 10px;
            border-radius: 4px;
        }
        .question-type-item .handle {
            cursor: move;
            margin-right: 10px;
        }
        /* 添加加载动画样式 */
        #loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #fff;
            z-index: 9999;
        }
        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(0, 0, 0, 0.1);
            border-top-color: #1890ff;
            border-radius: 50%;
            animation: spin 1s ease infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .tab-content {
            margin-top: 20px;
        }
        .proof-image {
            max-width: 100px;
            max-height: 100px;
            margin-right: 10px;
            cursor: pointer;
            border-radius: 4px;
            transition: transform 0.3s;
        }
        .proof-image:hover {
            transform: scale(1.05);
        }
        .proof-image-preview {
            max-width: 100%;
            max-height: 400px;
            border-radius: 4px;
        }
        .status-badge {
            padding: 6px 12px;
            border-radius: 4px;
            display: inline-flex;
            align-items: center;
            font-weight: 500;
            font-size: 13px;
        }
        /* 添加下拉菜单的自定义样式 */
        .expire-time-dropdown {
            max-height: 300px !important;
            overflow-y: auto !important;
        }
        .expire-time-dropdown .el-select-dropdown__item {
            height: 34px;
            line-height: 34px;
            padding: 0 15px;
            font-size: 14px;
        }
        .expire-time-dropdown .el-select-dropdown__item.selected {
            color: #409EFF;
            font-weight: bold;
        }
        .el-select-dropdown.expire-time-dropdown {
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }
        /* 添加选择器样式 */
        .el-select.el-select--large {
            width: 100%;
        }
        .el-select.el-select--large .el-input__wrapper {
            padding: 0 15px;
        }
        .el-select-dropdown__item {
            transition: all 0.3s;
        }
        .el-select-dropdown__item:hover {
            background-color: #f5f7fa;
        }
        .el-select-dropdown__item.selected {
            background-color: #ecf5ff;
        }
        .status-pending {
            background-color: #e6a23c;
            color: white;
        }
        .status-handled {
            background-color: #67c23a;
            color: white;
        }
        .status-rejected {
            background-color: #f56c6c;
            color: white;
        }
        /* 新增样式 */
        .report-search-box {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            background: #f9f9f9;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        }
        .report-search-item {
            margin-right: 15px;
        }
        .report-table {
            margin-top: 10px;
            border-radius: 8px;
            overflow: hidden;
        }
        /* 添加表格行样式 */
        .el-table .pending-row {
            --el-table-tr-bg-color: var(--el-color-warning-light-9);
        }
        .el-table .handled-row {
            --el-table-tr-bg-color: var(--el-color-success-light-9);
        }
        .operation-button {
            margin: 0 5px;
        }
        .report-header {
            font-size: 18px;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ebeef5;
            color: #333;
            display: flex;
            align-items: center;
        }
        .el-table {
            --el-table-row-hover-bg-color: #f5f7fa;
        }
        .el-table th {
            background-color: #f5f7fa;
            color: #333;
            font-weight: bold;
        }
        .el-table td {
            padding: 12px 0;
        }
        /* 添加表格内容自动换行样式 */
        .el-table .cell {
            white-space: pre-wrap !important;
            word-break: break-word !important;
            line-height: 1.5 !important;
        }
        .el-button {
            transition: all 0.3s;
        }
        .el-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <!-- 添加加载动画容器 -->
    <div id="loading">
        <div class="spinner"></div>
    </div>

    <div id="app" style="display: none">
        <div class="container">
            <div class="form-header">
                <h2>问卷与举报管理</h2>
            </div>
            
            <el-tabs v-model="activeTab">
                <el-tab-pane label="参数设置" name="settings">
                    <div v-if="loading" style="text-align: center; padding: 40px;">
                        <el-skeleton :rows="3" animated />
                    </div>
                    
                    <div v-else class="tab-content">
                        <el-form :model="params" label-width="150px">
                            <el-card class="form-card">
                                <template #header>
                                    <h3>基本设置</h3>
                                </template>
                                
                                <el-form-item label="默认问卷说明">
                                    <el-input type="textarea" :rows="4" v-model="params.questionnaire_description" />
                                </el-form-item>
                                
                                <el-form-item label="文件上传接口">
                                    <el-input v-model="params.upload_api" readonly disabled />
                                </el-form-item>
                                
                                <el-form-item label="默认模板类型">
                                    <el-select
                                        v-model="params.template_type"
                                        placeholder="请选择模板类型"
                                        size="large"
                                        style="width: 100%">
                                        <el-option
                                            v-for="item in templateOptions"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        />
                                    </el-select>
                                </el-form-item>
                                
                                <el-form-item label="邮箱提交次数限制">
                                    <el-input-number 
                                        v-model="params.email_submit_limit" 
                                        :min="1" 
                                        :max="20"
                                        placeholder="请输入限制次数"
                                        size="large"
                                        style="width: 100%" />
                                    <div class="form-tip" style="margin-top: 5px;">
                                        设置同一个邮箱最多可以提交多少次举报，默认为3次
                                    </div>
                                </el-form-item>
                                
                                <el-form-item label="IP邮箱数量限制">
                                    <el-input-number 
                                        v-model="params.ip_email_limit" 
                                        :min="1" 
                                        :max="20"
                                        placeholder="请输入限制数量"
                                        size="large"
                                        style="width: 100%" />
                                    <div class="form-tip" style="margin-top: 5px;">
                                        设置同一IP最多可以使用多少个不同邮箱提交举报，默认为5个
                                    </div>
                                </el-form-item>

                                <el-form-item label="导航菜单">
                                    <el-button 
                                        :type="navExists ? 'danger' : 'primary'"
                                        @click="toggleNav"
                                        :loading="navLoading">
                                        {{ navExists ? '从导航栏移除' : '添加到导航栏' }}
                                    </el-button>
                                    <div class="form-tip" style="margin-top: 10px;">
                                        {{ navExists ? '当前已添加到网站导航栏' : '当前未添加到网站导航栏' }}
                                    </div>
                                </el-form-item>
                            </el-card>
                            
                            <el-card class="form-card">
                                <template #header>
                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                        <h3>问题类型设置</h3>
                                        <el-button type="primary" size="small" @click="addQuestionType">添加类型</el-button>
                                    </div>
                                </template>
                                
                                <div v-if="params.question_types.length === 0" style="text-align: center; padding: 20px;">
                                    <p>暂无问题类型，请添加</p>
                                </div>
                                
                                <div>
                                    <div v-for="(type, index) in params.question_types" :key="index" class="question-type-item">
                                        <div style="display: flex; align-items: center;">
                                            <div style="flex: 1;">
                                                <el-row :gutter="20">
                                                    <el-col :span="6">
                                                        <el-form-item label="类型ID" label-width="60px">
                                                            <el-input v-model="type.id" placeholder="例如：text" />
                                                        </el-form-item>
                                                    </el-col>
                                                    <el-col :span="6">
                                                        <el-form-item label="名称" label-width="60px">
                                                            <el-input v-model="type.name" placeholder="例如：文本输入" />
                                                        </el-form-item>
                                                    </el-col>
                                                    <el-col :span="6">
                                                        <el-form-item label="组件" label-width="60px">
                                                            <el-select 
                                                                v-model="type.component" 
                                                                placeholder="选择组件类型" 
                                                                style="width: 100%"
                                                                filterable
                                                                :filter-method="() => true"
                                                                :default-first-option="true">
                                                                <el-option
                                                                    v-for="item in componentOptions"
                                                                    :key="item.value"
                                                                    :label="item.label"
                                                                    :value="item.value"
                                                                />
                                                            </el-select>
                                                        </el-form-item>
                                                    </el-col>
                                                    <el-col :span="6">
                                                        <el-form-item label="图标" label-width="60px">
                                                            <el-input v-model="type.icon" placeholder="例如：Edit" />
                                                        </el-form-item>
                                                    </el-col>
                                                </el-row>
                                                <el-row :gutter="20" v-if="['radio', 'checkbox', 'select', 'rate'].includes(type.component)">
                                                    <el-col :span="24">
                                                        <el-form-item label="选项" label-width="60px">
                                                            <div v-for="(option, optionIndex) in (type.options || [])" :key="optionIndex" style="display: flex; margin-bottom: 10px;">
                                                                <el-input v-model="type.options[optionIndex]" :placeholder="type.component === 'rate' ? '评分说明文字' : '选项内容'" style="margin-right: 10px;" />
                                                                <el-button type="danger" circle @click="removeOption(type, optionIndex)">
                                                                    <svg viewBox="0 0 1024 1024" width="1em" height="1em">
                                                                        <path fill="currentColor" d="M160 256H96a32 32 0 0 1 0-64h256V95.936a32 32 0 0 1 32-32h256a32 32 0 0 1 32 32V192h256a32 32 0 1 1 0 64h-64v672a32 32 0 0 1-32 32H192a32 32 0 0 1-32-32V256zm448-64v-64H416v64h192zM224 896h576V256H224v640zm192-128a32 32 0 0 1-32-32V416a32 32 0 0 1 64 0v320a32 32 0 0 1-32 32zm192 0a32 32 0 0 1-32-32V416a32 32 0 0 1 64 0v320a32 32 0 0 1-32 32z"/>
                                                                    </svg>
                                                                </el-button>
                                                            </div>
                                                            <el-button type="primary" size="small" @click="addOption(type)">{{ type.component === 'rate' ? '添加评分说明' : '添加选项' }}</el-button>
                                                        </el-form-item>
                                                    </el-col>
                                                </el-row>
                                                <el-row :gutter="20">
                                                    <el-col :span="24">
                                                        <el-form-item label="配置" label-width="60px">
                                                            <el-checkbox v-model="type.required" :true-label="true" :false-label="false">必填</el-checkbox>
                                                            <el-input v-if="['input', 'textarea'].includes(type.component)"
                                                                v-model="type.placeholder"
                                                                placeholder="输入提示文字"
                                                                style="width: 200px; margin-left: 10px;" />
                                                            <template v-if="type.component === 'upload'">
                                                                <el-input-number 
                                                                    v-model="type.maxFiles" 
                                                                    :min="1" 
                                                                    :max="10"
                                                                    placeholder="最大文件数"
                                                                    style="width: 150px; margin-left: 10px;" />
                                                                <el-select 
                                                                    v-model="type.fileTypes" 
                                                                    multiple 
                                                                    placeholder="允许的文件类型"
                                                                    style="width: 200px; margin-left: 10px;">
                                                                    <el-option label="图片" value="image" />
                                                                    <el-option label="文档" value="document" />
                                                                    <el-option label="视频" value="video" />
                                                                </el-select>
                                                            </template>
                                                            <template v-if="type.component === 'other'">
                                                                <div style="margin-top: 10px;">
                                                                    <el-input 
                                                                        v-model="type.fieldKey" 
                                                                        placeholder="字段键名(存储标识)"
                                                                        style="width: 200px; margin-right: 10px;" />
                                                                    <el-checkbox 
                                                                        v-model="type.autoCreate" 
                                                                        :true-label="true" 
                                                                        :false-label="false">
                                                                        自动创建字段
                                                                    </el-checkbox>
                                                                </div>
                                                                <div style="margin-top: 10px;">
                                                                    <el-select 
                                                                        v-model="type.dataType" 
                                                                        placeholder="数据类型"
                                                                        style="width: 150px; margin-right: 10px;">
                                                                        <el-option label="文本" value="string" />
                                                                        <el-option label="数字" value="number" />
                                                                        <el-option label="布尔值" value="boolean" />
                                                                        <el-option label="日期" value="date" />
                                                                        <el-option label="对象" value="object" />
                                                                        <el-option label="数组" value="array" />
                                                                    </el-select>
                                                                    <el-input 
                                                                        v-model="type.defaultValue" 
                                                                        placeholder="默认值"
                                                                        style="width: 200px;" />
                                                                </div>
                                                            </template>
                                                        </el-form-item>
                                                    </el-col>
                                                </el-row>
                                            </div>
                                            <div style="margin-left: 10px; display: flex;">
                                                <el-button 
                                                    type="primary" 
                                                    circle 
                                                    :disabled="index === 0"
                                                    @click="moveQuestionType(index, 'up')"
                                                    style="margin-right: 5px">
                                                    <svg viewBox="0 0 1024 1024" width="1em" height="1em">
                                                        <path fill="currentColor" d="M512 320L192 704h639.936z"/>
                                                    </svg>
                                                </el-button>
                                                <el-button 
                                                    type="primary" 
                                                    circle 
                                                    :disabled="index === params.question_types.length - 1"
                                                    @click="moveQuestionType(index, 'down')"
                                                    style="margin-right: 5px">
                                                    <svg viewBox="0 0 1024 1024" width="1em" height="1em">
                                                        <path fill="currentColor" d="M831.872 340.864L512 652.672 192.128 340.864a30.592 30.592 0 0 0-42.752 0 29.12 29.12 0 0 0 0 41.6L489.664 714.24a32 32 0 0 0 44.672 0l340.288-331.712a29.12 29.12 0 0 0 0-41.728 30.592 30.592 0 0 0-42.752 0z"/>
                                                    </svg>
                                                </el-button>
                                                <el-button 
                                                    type="danger" 
                                                    circle 
                                                    @click="removeQuestionType(index)">
                                                    <svg viewBox="0 0 1024 1024" width="1em" height="1em">
                                                        <path fill="currentColor" d="M160 256H96a32 32 0 0 1 0-64h256V95.936a32 32 0 0 1 32-32h256a32 32 0 0 1 32 32V192h256a32 32 0 1 1 0 64h-64v672a32 32 0 0 1-32 32H192a32 32 0 0 1-32-32V256zm448-64v-64H416v64h192zM224 896h576V256H224v640zm192-128a32 32 0 0 1-32-32V416a32 32 0 0 1 64 0v320a32 32 0 0 1-32 32zm192 0a32 32 0 0 1-32-32V416a32 32 0 0 1 64 0v320a32 32 0 0 1-32 32z"/>
                                                    </svg>
                                                </el-button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </el-card>
                            
                            <div style="text-align: center; margin-top: 20px;">
                                <el-button type="primary" @click="saveParams" :loading="saving">保存设置</el-button>
                            </div>
                        </el-form>
                    </div>
                </el-tab-pane>
                
                <el-tab-pane label="违规商品举报" name="reports">
                    <div class="tab-content">
                        <el-card>
                            <template #header>
                                <h3 class="report-header">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="20" height="20" style="margin-right: 8px; vertical-align: -4px;">
                                        <path fill="currentColor" d="M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896zm0 832a384 384 0 0 0 0-768 384 384 0 0 0 0 768zm48-176a48 48 0 1 1-96 0 48 48 0 0 1 96 0zm-48-464a32 32 0 0 1 32 32v288a32 32 0 0 1-64 0V288a32 32 0 0 1 32-32z"></path>
                                    </svg>
                                    举报列表
                                </h3>
                            </template>
                            
                            <div class="report-search-box">
                                <div class="report-search-item" style="flex: 1;">
                                    <el-input
                                        v-model="reportSearch.keyword"
                                        placeholder="搜索任意字段..."
                                        clearable
                                        prefix-icon="Search">
                                    </el-input>
                                </div>
                                <div class="report-search-item">
                                    <el-select 
                                        v-model="reportSearch.status" 
                                        placeholder="状态" 
                                        style="width: 120px;"
                                        clearable>
                                        <el-option label="全部" value="-1">
                                            <div style="display: flex; align-items: center;">
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="16" height="16" style="margin-right: 8px;">
                                                    <path fill="currentColor" d="M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896zm0 832a384 384 0 0 0 0-768 384 384 0 0 0 0 768z"></path>
                                                </svg>
                                                全部
                                            </div>
                                        </el-option>
                                        <el-option label="未处理" value="0">
                                            <div style="display: flex; align-items: center;">
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="16" height="16" style="margin-right: 8px; color: #e6a23c;">
                                                    <path fill="currentColor" d="M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896zm0 192a58.432 58.432 0 0 0-58.24 63.744l23.36 256.384a35.072 35.072 0 0 0 69.76 0l23.296-256.384A58.432 58.432 0 0 0 512 256zm0 512a51.2 51.2 0 1 0 0-102.4 51.2 51.2 0 0 0 0 102.4z"></path>
                                                </svg>
                                                未处理
                                            </div>
                                        </el-option>
                                        <el-option label="已处理" value="1">
                                            <div style="display: flex; align-items: center;">
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="16" height="16" style="margin-right: 8px; color: #67c23a;">
                                                    <path fill="currentColor" d="M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896zm-55.808 536.384-138.304-136.448a38.4 38.4 0 1 0-54.336 54.336l165.248 163.84a38.272 38.272 0 0 0 54.336 0l297.792-297.728a38.4 38.4 0 1 0-54.4-54.4L456.192 600.384z"></path>
                                                </svg>
                                                已处理
                                            </div>
                                        </el-option>
                                    </el-select>
                                </div>
                                <div>
                                    <el-button type="primary" @click="searchReports">搜索</el-button>
                                    <el-button @click="resetSearch">重置</el-button>
                                </div>
                            </div>
                            
                            <div class="report-table">
                                <div v-for="(report, index) in reports" :key="report.id" style="margin-bottom: 15px; border: 1px solid #EBEEF5; border-radius: 8px; overflow: hidden; background: #fff; box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05); transition: all 0.3s;">
                                    <div style="display: grid; grid-template-columns: 48px 1fr;">
                                        <div style="background-color: #f5f7fa; display: flex; justify-content: center; align-items: center; border-right: 1px solid #EBEEF5; grid-row: 1/8;">
                                            <el-checkbox v-model="report.selected" @change="handleItemSelect(report)"></el-checkbox>
                                        </div>
                                        
                                        <div style="display: grid; grid-template-columns: 120px 1fr;">
                                            <div style="background-color: #f5f7fa; padding: 12px; font-weight: bold; border-right: 1px solid #EBEEF5; border-bottom: 1px solid #EBEEF5;">
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="14" height="14" style="margin-right: 4px; vertical-align: -2px;">
                                                    <path fill="currentColor" d="M288 320a224 224 0 1 0 448 0 224 224 0 1 0-448 0zm544 608H160a32 32 0 0 1-32-32v-96a160 160 0 0 1 160-160h448a160 160 0 0 1 160 160v96a32 32 0 0 1-32 32z"></path>
                                                </svg>
                                                ID
                                            </div>
                                            <div style="padding: 12px 15px; border-bottom: 1px solid #EBEEF5;">
                                                {{ report.id }}
                                            </div>
                                            
                                            <div style="background-color: #f5f7fa; padding: 12px; font-weight: bold; border-right: 1px solid #EBEEF5; border-bottom: 1px solid #EBEEF5;">
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="14" height="14" style="margin-right: 4px; vertical-align: -2px;">
                                                    <path fill="currentColor" d="M128 352.576V352a288 288 0 0 1 491.072-204.224 192 192 0 0 1 274.24 204.48 64 64 0 0 1 57.216 74.24C921.6 600.512 850.048 710.656 736 756.992V800a96 96 0 0 1-96 96H384a96 96 0 0 1-96-96v-43.008c-114.048-46.336-185.6-156.48-214.528-330.496A64 64 0 0 1 128 352.64zm64-.576h64a160 160 0 0 1 320 0h64a224 224 0 0 0-448 0zm128 0h192a96 96 0 0 0-192 0zm439.424 0h68.544A128.256 128.256 0 0 0 704 192c-15.36 0-29.952 2.688-43.52 7.616 11.328 18.176 20.672 37.76 27.84 58.304A64.128 64.128 0 0 1 759.424 352zM672 768H352v32a32 32 0 0 0 32 32h256a32 32 0 0 0 32-32v-32zm-342.528-64h365.056c101.504-32.64 165.76-124.928 192.896-288H136.576c27.136 163.072 91.392 255.36 192.896 288z"></path>
                                                </svg>
                                                店铺链接
                                            </div>
                                            <div style="padding: 12px 15px; border-bottom: 1px solid #EBEEF5; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                                                {{ report.shop_link || '未提供' }}
                                            </div>
                                            
                                            <div style="background-color: #f5f7fa; padding: 12px; font-weight: bold; border-right: 1px solid #EBEEF5; border-bottom: 1px solid #EBEEF5;">
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="14" height="14" style="margin-right: 4px; vertical-align: -2px;">
                                                    <path fill="currentColor" d="M872 512h-80a248 248 0 0 0-248 248v80c0 8.8 7.2 16 16 16h544c8.8 0 16-7.2 16-16v-80c0-137-111-248-248-248zM640 568a40 40 0 1 1 80 0 40 40 0 0 1-80 0zm152-24a40 40 0 1 1 0 80 40 40 0 0 1 0-80zM232 704c39.8 0 72-32.2 72-72s-32.2-72-72-72-72 32.2-72 72 32.2 72 72 72zm0-112c22.1 0 40 17.9 40 40s-17.9 40-40 40-40-17.9-40-40 17.9-40 40-40zm248 304h-80a248 248 0 0 0-248-248h-80c-8.8 0-16 7.2-16 16v456c0 8.8 7.2 16 16 16h544c8.8 0 16-7.2 16-16v-80c0-137-111-248-248-248z"></path>
                                                </svg>
                                                店铺名称
                                            </div>
                                            <div style="padding: 12px 15px; border-bottom: 1px solid #EBEEF5; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                                                {{ report.shop_name || '未提供' }}
                                            </div>
                                            
                                            <div style="background-color: #f5f7fa; padding: 12px; font-weight: bold; border-right: 1px solid #EBEEF5; border-bottom: 1px solid #EBEEF5;">
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="14" height="14" style="margin-right: 4px; vertical-align: -2px;">
                                                    <path fill="currentColor" d="M128 224v512a64 64 0 0 0 64 64h640a64 64 0 0 0 64-64V224H128zm0-64h768a64 64 0 0 1 64 64v512a128 128 0 0 1-128 128H192A128 128 0 0 1 64 736V224a64 64 0 0 1 64-64z"></path>
                                                    <path fill="currentColor" d="M904 224 656.128 506.368 904 800H120L370.752 506.304 120 224h784zM120 160h784a64 64 0 0 1 64 64v512a64 64 0 0 1-64 64H120a64 64 0 0 1-64-64V224a64 64 0 0 1 64-64z"></path>
                                                </svg>
                                                联系邮箱
                                            </div>
                                            <div style="padding: 12px 15px; border-bottom: 1px solid #EBEEF5; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                                                {{ report.email || '未提供' }}
                                            </div>
                                            
                                            <div style="background-color: #f5f7fa; padding: 12px; font-weight: bold; border-right: 1px solid #EBEEF5; border-bottom: 1px solid #EBEEF5;">
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="14" height="14" style="margin-right: 4px; vertical-align: -2px;">
                                                    <path fill="currentColor" d="M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896zm0 832a384 384 0 0 0 0-768 384 384 0 0 0 0 768zm48-176a48 48 0 1 1-96 0 48 48 0 0 1 96 0zm-48-464a32 32 0 0 1 32 32v288a32 32 0 0 1-64 0V288a32 32 0 0 1 32-32z"></path>
                                                </svg>
                                                违规类型
                                            </div>
                                            <div style="padding: 12px 15px; border-bottom: 1px solid #EBEEF5;">
                                                <el-tag type="danger" v-if="report.violation_type === '著作权侵权'" effect="dark">著作权侵权</el-tag>
                                                <el-tag type="warning" v-else-if="report.violation_type === '隐私权侵权'" effect="dark">隐私权侵权</el-tag>
                                                <el-tag type="info" v-else-if="report.violation_type === '交易违规'" effect="dark">交易违规</el-tag>
                                                <el-tag v-else-if="report.violation_type === '商品违规'" effect="dark">商品违规</el-tag>
                                                <span v-else>{{ report.violation_type || '未提供' }}</span>
                                            </div>
                                            
                                            <div style="background-color: #f5f7fa; padding: 12px; font-weight: bold; border-right: 1px solid #EBEEF5; border-bottom: 1px solid #EBEEF5;">
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="14" height="14" style="margin-right: 4px; vertical-align: -2px;">
                                                    <path fill="currentColor" d="M512 896a384 384 0 1 0 0-768 384 384 0 0 0 0 768zm0 64a448 448 0 1 1 0-896 448 448 0 0 1 0 896z"></path>
                                                    <path fill="currentColor" d="M480 256a32 32 0 0 1 32 32v256a32 32 0 0 1-64 0V288a32 32 0 0 1 32-32z"></path>
                                                    <path fill="currentColor" d="M480 512h256a32 32 0 1 1 0 64H480a32 32 0 0 1 0-64z"></path>
                                                </svg>
                                                提交时间
                                            </div>
                                            <div style="padding: 12px 15px; border-bottom: 1px solid #EBEEF5;">
                                                {{ report.create_time_text || '-' }}
                                            </div>
                                            
                                            <div style="background-color: #f5f7fa; padding: 12px; font-weight: bold; border-right: 1px solid #EBEEF5; border-bottom: 1px solid #EBEEF5;">
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="14" height="14" style="margin-right: 4px; vertical-align: -2px;">
                                                    <path fill="currentColor" d="M600.704 64a32 32 0 0 1 30.464 22.208l35.2 109.376c14.784 7.232 28.928 15.36 42.432 24.512l112.384-24.192a32 32 0 0 1 34.432 15.36L944.32 364.8a32 32 0 0 1-4.032 37.504l-77.12 85.12a357.12 357.12 0 0 1 0 49.024l77.12 85.248a32 32 0 0 1 4.032 37.504l-88.704 153.6a32 32 0 0 1-34.432 15.296L708.8 803.904c-13.44 9.088-27.648 17.28-42.368 24.512l-35.264 109.376A32 32 0 0 1 600.704 960H423.296a32 32 0 0 1-30.464-22.208L357.696 828.48a351.616 351.616 0 0 1-42.56-24.64l-112.32 24.256a32 32 0 0 1-34.432-15.36L79.68 659.2a32 32 0 0 1 4.032-37.504l77.12-85.248a357.12 357.12 0 0 1 0-48.896l-77.12-85.248A32 32 0 0 1 79.68 364.8l88.704-153.6a32 32 0 0 1 34.432-15.296l112.32 24.256c13.568-9.152 27.776-17.408 42.56-24.64l35.2-109.312A32 32 0 0 1 423.232 64H600.64zm-23.424 64H446.72l-36.352 113.088-24.512 11.968a294.113 294.113 0 0 0-34.816 20.096l-22.656 15.36-116.224-25.088-65.28 113.152 79.68 88.192-1.92 27.136a293.12 293.12 0 0 0 0 40.192l1.92 27.136-79.808 88.192 65.344 113.152 116.224-25.024 22.656 15.296a294.113 294.113 0 0 0 34.816 20.096l24.512 11.968L446.72 896h130.688l36.48-113.152 24.448-11.904a288.282 288.282 0 0 0 34.752-20.096l22.592-15.296 116.288 25.024 65.28-113.152-79.744-88.192 1.92-27.136a293.12 293.12 0 0 0 0-40.256l-1.92-27.136 79.808-88.128-65.344-113.152-116.288 24.96-22.592-15.232a287.616 287.616 0 0 0-34.752-20.096l-24.448-11.904L577.344 128zM512 320a192 192 0 1 1 0 384 192 192 0 0 1 0-384zm0 64a128 128 0 1 0 0 256 128 128 0 0 0 0-256z"></path>
                                                </svg>
                                                操作
                                            </div>
                                            <div style="padding: 12px 15px;">
                                                <el-button size="small" @click="viewReport(report)">查看</el-button>
                                                <el-button size="small" type="success" v-if="report.status === 0" @click="markReportAsHandled(report)">标记已处理</el-button>
                                                <el-button size="small" type="warning" v-if="report.status === 0" @click="showRejectDialog(report)">驳回</el-button>
                                                <el-button size="small" type="danger" @click="deleteReport(report)">删除</el-button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div v-if="reports.length === 0" style="text-align: center; padding: 30px; background: #fff; border: 1px solid #EBEEF5; border-radius: 4px;">
                                    暂无数据
                                </div>
                            </div>
                            
                            <div style="margin-top: 20px; display: flex; justify-content: space-between;">
                                <div>
                                    <el-button @click="toggleSelection(selectedReports)" v-if="selectedReports.length > 0">
                                        取消选择
                                    </el-button>
                                    <el-button type="primary" @click="batchMarkAsHandled" v-if="selectedReports.length > 0">
                                        批量标记为已处理
                                    </el-button>
                                </div>
                                <el-pagination
                                    v-model:current-page="reportPagination.currentPage"
                                    v-model:page-size="reportPagination.pageSize"
                                    :page-sizes="[10, 20, 30, 50]"
                                    :total="reportPagination.total"
                                    @size-change="handleReportSizeChange"
                                    @current-change="handleReportCurrentChange"
                                    layout="total, sizes, prev, pager, next, jumper" />
                            </div>
                        </el-card>
                    </div>
                </el-tab-pane>
            </el-tabs>
            
            <!-- 查看举报详情的对话框 -->
            <el-dialog
                v-model="reportDetailVisible"
                title="举报详情"
                width="70%">
                <div v-if="currentReport">
                    <el-descriptions :column="2" border>
                        <el-descriptions-item label="举报ID">{{ currentReport.id }}</el-descriptions-item>
                        <el-descriptions-item label="状态">
                            <span class="status-badge" :class="currentReport.status === 0 ? 'status-pending' : 'status-handled'">
                                {{ currentReport.status_text }}
                            </span>
                        </el-descriptions-item>
                        <el-descriptions-item label="违规类型" :span="2">
                            <el-tag type="danger" v-if="currentReport.violation_type === '著作权侵权'">著作权侵权</el-tag>
                            <el-tag type="warning" v-else-if="currentReport.violation_type === '隐私权侵权'" effect="dark">隐私权侵权</el-tag>
                            <el-tag type="info" v-else-if="currentReport.violation_type === '交易违规'" effect="dark">交易违规</el-tag>
                            <el-tag v-else-if="currentReport.violation_type === '商品违规'" effect="dark">商品违规</el-tag>
                            <span v-else>{{ currentReport.violation_type || '未提供' }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item label="店铺链接" :span="2">{{ currentReport.shop_link || '未提供' }}</el-descriptions-item>
                        <el-descriptions-item label="店铺名称" :span="2">{{ currentReport.shop_name || '未提供' }}</el-descriptions-item>
                        <el-descriptions-item label="商品链接" :span="2">{{ currentReport.product_link || '未提供' }}</el-descriptions-item>
                        <el-descriptions-item label="举报人邮箱">{{ currentReport.email || '未提供' }}</el-descriptions-item>
                        <el-descriptions-item label="提交IP">{{ currentReport.ip || '未提供' }}</el-descriptions-item>
                        <el-descriptions-item label="提交时间">{{ currentReport.create_time_text || '-' }}</el-descriptions-item>
                        <el-descriptions-item label="处理时间">{{ currentReport.update_time_text || '-' }}</el-descriptions-item>
                        <el-descriptions-item label="备注说明" :span="2">{{ currentReport.remark || '无' }}</el-descriptions-item>
                        <el-descriptions-item v-if="currentReport.status === 2" label="驳回原因" :span="2">
                            {{ currentReport.reject_reason || '无' }}
                        </el-descriptions-item>
                    </el-descriptions>
                    
                    <div style="margin-top: 20px;">
                        <h4>证明材料：</h4>
                        <div style="display: flex; flex-wrap: wrap;">
                            <div v-for="(proof, index) in currentReport.proofs" :key="index" style="margin: 10px;">
                                <img :src="proof" class="proof-image" @click="previewImage(proof)" />
                            </div>
                            <div v-if="!currentReport.proofs || currentReport.proofs.length === 0" style="padding: 20px; color: #999;">
                                未提供证明材料
                            </div>
                        </div>
                    </div>
                    
                    <div v-if="currentReport?.status === 0" style="margin-top: 20px;">
                        <h4>处理设置：</h4>
                        <div style="padding: 10px 15px; color: #606266;">
                            请点击"标记为已处理"按钮进行处理。
                        </div>
                    </div>
                </div>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="reportDetailVisible = false">关闭</el-button>
                        <el-button 
                            type="success" 
                            v-if="currentReport?.status === 0"
                            @click="markReportAsHandled(currentReport)">标记为已处理</el-button>
                    </span>
                </template>
            </el-dialog>
            
            <!-- 图片预览 -->
            <el-dialog
                v-model="imagePreviewVisible"
                append-to-body
                title="图片预览">
                <div style="text-align: center;">
                    <img :src="previewImageUrl" class="proof-image-preview" />
                </div>
            </el-dialog>

            <!-- 驳回原因对话框 -->
            <el-dialog
                v-model="rejectDialogVisible"
                title="驳回举报"
                width="500px">
                <el-form :model="rejectForm">
                    <el-form-item label="驳回原因" :label-width="'100px'">
                        <el-input
                            v-model="rejectForm.reason"
                            type="textarea"
                            :rows="4"
                            placeholder="请输入驳回原因">
                        </el-input>
                    </el-form-item>
                </el-form>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="rejectDialogVisible = false">取消</el-button>
                        <el-button type="primary" @click="submitReject" :loading="rejectLoading">确定</el-button>
                    </span>
                </template>
            </el-dialog>

            <!-- 处理举报对话框 -->
            <el-dialog
                v-model="handleReportDialogVisible"
                :title="currentProcessingReport?.isBatch ? '批量处理举报' : '处理举报'"
                width="500px">
                <div style="margin-bottom: 20px;">
                    <p v-if="currentProcessingReport?.isBatch">
                        您正在处理 <span style="color: #409EFF; font-weight: bold;">{{ currentProcessingReport?.reports?.length || 0 }}</span> 条举报
                    </p>
                    <p v-else>
                        您正在处理举报 #{{ currentProcessingReport?.id }}
                    </p>
                    <p style="margin-top: 10px; color: #606266;">请设置风险记录的有效期：</p>
                </div>
                
                <el-form label-width="120px">
                    <el-form-item label="风险记录期限">
                        <el-select 
                            v-model="riskExpireTime" 
                            placeholder="请选择风险记录期限" 
                            style="width: 100%;"
                            popper-class="expire-time-dropdown"
                            size="large"
                            clearable
                            :teleported="true"
                            :fit-input-width="false"
                            :model-value="riskExpireTime"
                            @change="(val) => riskExpireTime = val">
                            <el-option 
                                v-for="item in expireTimeOptions" 
                                :key="item.value" 
                                :label="item.label" 
                                :value="item.value">
                                <span style="display: flex; align-items: center;">
                                    <svg v-if="item.label === '永久'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="16" height="16" style="margin-right: 8px; color: #409EFF;">
                                        <path fill="currentColor" d="M512 896a384 384 0 1 0 0-768 384 384 0 0 0 0 768zm0 64a448 448 0 1 1 0-896 448 448 0 0 1 0 896z"></path>
                                        <path fill="currentColor" d="M480 256a32 32 0 0 1 32 32v256a32 32 0 0 1-64 0V288a32 32 0 0 1 32-32z"></path>
                                        <path fill="currentColor" d="M480 512h256a32 32 0 1 1 0 64H480a32 32 0 0 1 0-64z"></path>
                                    </svg>
                                    {{ item.label }}
                                </span>
                            </el-option>
                        </el-select>
                        <div class="form-tip" style="margin-top: 5px; color: #999; font-size: 12px;">
                            设置风险记录的有效期，默认为永久。设置为"永久"表示风险记录不会过期。
                        </div>
                    </el-form-item>
                </el-form>
                
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="handleReportDialogVisible = false">取消</el-button>
                        <el-button type="success" @click="submitHandleReport" :loading="processLoading">
                            {{ currentProcessingReport?.isBatch ? '批量处理' : '标记已处理' }}
                        </el-button>
                    </span>
                </template>
            </el-dialog>
        </div>
    </div>

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>
    
    <script>
    const { createApp, ref, reactive, onMounted } = Vue;
    const { ElMessage, ElMessageBox } = ElementPlus;
    
    // 配置axios
    axios.defaults.timeout = 15000;
    axios.defaults.baseURL = '';

    // 请求拦截器
    axios.interceptors.request.use(
        config => config,
        error => {
            console.error('请求错误:', error);
            return Promise.reject(error);
        }
    );

    // 响应拦截器
    axios.interceptors.response.use(
        response => response,
        error => {
            console.error('响应错误:', error);
            return Promise.reject(error);
        }
    );

    // 封装API请求方法
    const api = {
        index: () => axios.get('./index'),
        getParams: () => axios.get('./getParams'),
        saveParams: (data) => axios.post('./saveParams', data),
        getReportList: (page, limit, keyword, status) => axios.get('./getReportList', {
            params: { page, limit, keyword, status }
        }),
        getReport: (id) => axios.get('./getReport', { params: { id } }),
        updateReportStatus: (id, status, expireTime) => axios.post('./updateReportStatus', { id, status, expireTime }),
        deleteReport: (id) => axios.post('./deleteReport', { id }),
        rejectReport: (id, reason) => axios.post('./rejectReport', { id, reason })
    };

    const app = createApp({
        setup() {
            // 状态定义
            const activeTab = ref('settings');
            const isMobile = ref(false);
            const templateOptions = [
                {
                    value: 'default',
                    label: '默认模板',
                },
                {
                    value: 'simple',
                    label: '简洁模板',
                },
                {
                    value: 'business',
                    label: '企业模板',
                }
            ];
            const componentOptions = [
                { value: 'input', label: '输入框' },
                { value: 'textarea', label: '文本域' },
                { value: 'radio', label: '单选框' },
                { value: 'checkbox', label: '多选框' },
                { value: 'select', label: '下拉框' },
                { value: 'datetime', label: '日期时间选择' },
                { value: 'upload', label: '图片上传' },
                { value: 'rate', label: '评分' },
                { value: 'slider', label: '滑块' },
                { value: 'other', label: '其他字段' }
            ];
            const params = reactive({
                questionnaire_description: '',
                question_types: [],
                field_mappings: {},
                upload_api: '/adminApi/Upload/file',
                template_type: 'default',
                email_submit_limit: 3,
                ip_email_limit: 5
            });
            const loading = ref(true);
            const saving = ref(false);
            
            // 举报相关数据
            const reports = ref([]);
            const reportsLoading = ref(false);
            const reportTableRef = ref();
            const selectedReports = ref([]);
            const reportSearch = reactive({
                keyword: '',
                status: '-1'
            });
            const reportPagination = reactive({
                currentPage: 1,
                pageSize: 10,
                total: 0
            });
            const currentReport = ref(null);
            const reportDetailVisible = ref(false);
            const imagePreviewVisible = ref(false);
            const previewImageUrl = ref('');
            const allSelected = ref(false);
            const navExists = ref(false);
            const navLoading = ref(false);
            const rejectDialogVisible = ref(false);
            const rejectLoading = ref(false);
            const rejectForm = reactive({
                id: null,
                reason: ''
            });
            
            // 定义过期时间选项
            const expireTimeOptions = ref([
                { label: '永久', value: 4102415999 },
                { label: '1天', value: 1*24*60*60 + Math.floor(Date.now()/1000) },
                { label: '3天', value: 3*24*60*60 + Math.floor(Date.now()/1000) },
                { label: '7天', value: 7*24*60*60 + Math.floor(Date.now()/1000) },
                { label: '14天', value: 14*24*60*60 + Math.floor(Date.now()/1000) },
                { label: '1个月(30天)', value: 30*24*60*60 + Math.floor(Date.now()/1000) },
                { label: '2个月(60天)', value: 60*24*60*60 + Math.floor(Date.now()/1000) },
                { label: '3个月(90天)', value: 90*24*60*60 + Math.floor(Date.now()/1000) }
            ]);
            
            // 设置默认值为永久
            const riskExpireTime = ref(expireTimeOptions.value[0].value);
            
            // 添加处理举报对话框相关变量
            const handleReportDialogVisible = ref(false);
            const currentProcessingReport = ref(null);
            const processLoading = ref(false);

            // 方法定义
            const initPage = () => {
                loading.value = true;
                getParams();
                getReports();
            };

            const getParams = async () => {
                try {
                    const response = await api.getParams();
                    if (response.data.code === 200) {
                        Object.assign(params, response.data.data);
                        
                        // 确保问题类型中的required属性是布尔值
                        if (params.question_types && params.question_types.length > 0) {
                            params.question_types.forEach(type => {
                                type.required = !!type.required;
                            });
                        }
                        
                        // 确保有默认的问题类型
                        setDefaultQuestionTypes();
                    } else {
                        ElMessage.error(response.data.msg || '获取参数失败');
                    }
                } catch (error) {
                    console.error('Error:', error);
                    ElMessage.error('获取参数失败');
                    
                    // 出错时也要确保有默认问题类型
                    setDefaultQuestionTypes();
                } finally {
                    loading.value = false;
                }
            };

            const addQuestionType = () => {
                params.question_types.push({
                    id: '',
                    name: '',
                    icon: 'Question',
                    component: 'input',
                    required: false,
                    placeholder: ''
                });
            };

            // 设置默认问题类型（如果为空）
            const setDefaultQuestionTypes = () => {
                if (!params.question_types || params.question_types.length === 0) {
                    params.question_types = [
                        {
                            id: 'shopLink',
                            name: '违规店铺链接',
                            component: 'input',
                            required: true,
                            placeholder: '请输入违规店铺链接',
                            icon: 'Link'
                        },
                        {
                            id: 'shopName',
                            name: '店铺名称',
                            component: 'input',
                            required: true,
                            placeholder: '请输入店铺名称',
                            icon: 'Shop'
                        },
                        {
                            id: 'productLink',
                            name: '违规商品链接',
                            component: 'input',
                            required: true,
                            placeholder: '请输入违规商品链接',
                            icon: 'Link'
                        },
                        {
                            id: 'violationType',
                            name: '违规类型',
                            component: 'radio',
                            required: true,
                            options: ['著作权侵权', '隐私权侵权', '交易违规', '商品违规'],
                            icon: 'Warning'
                        },
                        {
                            id: 'proofs',
                            name: '证明材料',
                            component: 'upload',
                            required: true,
                            maxFiles: 5,
                            fileTypes: ['image'],
                            placeholder: '请上传违规证明材料，最多5张图片',
                            icon: 'Upload'
                        },
                        {
                            id: 'email',
                            name: '联系邮箱',
                            component: 'input',
                            required: true,
                            placeholder: '请输入您的联系邮箱',
                            icon: 'Message'
                        },
                        {
                            id: 'remark',
                            name: '备注说明',
                            component: 'textarea',
                            required: false,
                            placeholder: '请输入补充说明（可选）',
                            icon: 'Document'
                        }
                    ];
                }
            };

            const removeQuestionType = (index) => {
                ElMessageBox.confirm('确定要删除此问题类型吗?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    params.question_types.splice(index, 1);
                    ElMessage.success('删除成功!');
                }).catch(() => {});
            };

            const addOption = (type) => {
                if (!type.options) {
                    type.options = [];
                }
                type.options.push('');
            };

            const removeOption = (type, optionIndex) => {
                type.options.splice(optionIndex, 1);
            };

            const saveParams = async () => {
                if (params.question_types.some(type => !type.id || !type.name)) {
                    ElMessage.warning('问题类型的ID和名称不能为空');
                    return;
                }

                // 验证模板类型
                const allowedTemplateTypes = ['default', 'simple', 'business'];
                if (!allowedTemplateTypes.includes(params.template_type)) {
                    ElMessage.warning('请选择有效的模板类型');
                    return;
                }
                
                // 验证邮箱提交次数限制
                if (!params.email_submit_limit || params.email_submit_limit < 1 || params.email_submit_limit > 20) {
                    ElMessage.warning('邮箱提交次数限制必须在1到20之间');
                    params.email_submit_limit = 3; // 设置回默认值
                    return;
                }
                
                // 验证IP邮箱数量限制
                if (!params.ip_email_limit || params.ip_email_limit < 1 || params.ip_email_limit > 20) {
                    ElMessage.warning('IP邮箱数量限制必须在1到20之间');
                    params.ip_email_limit = 5; // 设置回默认值
                    return;
                }
                
                // 验证选项类型的配置
                for (const type of params.question_types) {
                    if (['radio', 'checkbox', 'select'].includes(type.component)) {
                        if (!type.options || type.options.length === 0) {
                            ElMessage.warning(`问题类型"${type.name}"需要至少一个选项`);
                            return;
                        }
                        if (type.options.some(opt => !opt.trim())) {
                            ElMessage.warning(`问题类型"${type.name}"的选项内容不能为空`);
                            return;
                        }
                    }
                }
                
                // 确保基本必填字段存在
                const requiredFields = ['shop_link', 'product_link', 'violation_type','shop_name', 'proofs', 'email'];
                const fieldIds = params.question_types.map(type => type.id);
                const missingFields = requiredFields.filter(field => !fieldIds.includes(field));
                
                if (missingFields.length > 0) {
                    ElMessage.warning(`必须包含以下基本字段: ${missingFields.join(', ')}`);
                    return;
                }
                
                saving.value = true;
                try {
                    const response = await api.saveParams(params);
                    if (response.data.code === 200) {
                        ElMessage.success('保存成功');
                    } else {
                        ElMessage.error(response.data.msg || '保存失败');
                    }
                } catch (error) {
                    console.error('Error:', error);
                    ElMessage.error('保存失败');
                } finally {
                    saving.value = false;
                }
            };

            const getReports = async () => {
                reportsLoading.value = true;
                try {
                    const response = await api.getReportList(
                        reportPagination.currentPage,
                        reportPagination.pageSize,
                        reportSearch.keyword,
                        reportSearch.status
                    );
                    if (response.data.code === 200) {
                        const reportList = response.data.data.list;
                        // 确保每个report的proofs是数组格式
                        reportList.forEach(report => {
                            if (typeof report.proofs === 'string') {
                                try {
                                    report.proofs = JSON.parse(report.proofs);
                                } catch (e) {
                                    report.proofs = [];
                                    console.error('解析proofs失败:', e);
                                }
                            }
                            
                            if (!Array.isArray(report.proofs)) {
                                report.proofs = [];
                            }
                            
                            // 初始化选择状态
                            report.selected = false;
                        });
                        
                        reports.value = reportList;
                        reportPagination.total = response.data.data.total;
                    } else {
                        ElMessage.error(response.data.msg || '获取举报列表失败');
                    }
                } catch (error) {
                    console.error('Error:', error);
                    ElMessage.error('获取举报列表失败');
                } finally {
                    reportsLoading.value = false;
                    document.getElementById("loading").style.display = "none";
                    document.getElementById("app").style.display = "block";
                }
            };

            const searchReports = () => {
                reportPagination.currentPage = 1;
                getReports();
            };

            const resetSearch = () => {
                reportSearch.keyword = '';
                reportSearch.status = '-1';
                reportPagination.currentPage = 1;
                getReports();
            };

            const handleReportSizeChange = (val) => {
                reportPagination.pageSize = val;
                getReports();
            };

            const handleReportCurrentChange = (val) => {
                reportPagination.currentPage = val;
                getReports();
            };

            const viewReport = async (report) => {
                try {
                    const response = await api.getReport(report.id);
                    if (response.data.code === 200) {
                        const report = response.data.data;
                        // 确保report.proofs是数组
                        if (typeof report.proofs === 'string') {
                            try {
                                report.proofs = JSON.parse(report.proofs);
                            } catch (e) {
                                report.proofs = [];
                                console.error('解析详情proofs失败:', e);
                            }
                        }
                        
                        if (!Array.isArray(report.proofs)) {
                            report.proofs = [];
                        }
                        
                        currentReport.value = report;
                        reportDetailVisible.value = true;
                    } else {
                        ElMessage.error(response.data.msg || '获取详情失败');
                    }
                } catch (error) {
                    console.error('Error:', error);
                    ElMessage.error('获取详情失败');
                }
            };

            const markReportAsHandled = (report) => {
                // 使用对话框而不是简单的确认框
                handleReportDialogVisible.value = true;
                currentProcessingReport.value = report;
            };

            const previewImage = (url) => {
                previewImageUrl.value = url;
                imagePreviewVisible.value = true;
            };

            const moveQuestionType = (index, direction) => {
                if (direction === 'up' && index > 0) {
                    const temp = params.question_types[index];
                    params.question_types[index] = params.question_types[index - 1];
                    params.question_types[index - 1] = temp;
                } else if (direction === 'down' && index < params.question_types.length - 1) {
                    const temp = params.question_types[index];
                    params.question_types[index] = params.question_types[index + 1];
                    params.question_types[index + 1] = temp;
                }
            };

            const handleSelectionChange = (val) => {
                selectedReports.value = val;
            };
            
            const toggleSelection = (rows) => {
                if (rows && rows.length > 0) {
                    rows.forEach(row => {
                        reportTableRef.value?.toggleRowSelection(row, false);
                    });
                } else {
                    reportTableRef.value?.clearSelection();
                }
            };
            
            const batchMarkAsHandled = () => {
                if (selectedReports.value.length === 0) {
                    ElMessage.warning('请先选择要处理的举报');
                    return;
                }
                
                const pendingReports = selectedReports.value.filter(item => item.status === 0);
                if (pendingReports.length === 0) {
                    ElMessage.warning('您选择的举报已全部处理');
                    return;
                }
                
                // 使用对话框
                handleReportDialogVisible.value = true;
                currentProcessingReport.value = { isBatch: true, reports: pendingReports };
            };

            const tableRowClassName = ({ row }) => {
                if (row.status === 0) {
                    return 'pending-row';
                } else if (row.status === 1) {
                    return 'handled-row';
                }
                return '';
            };

            const selectAll = (val) => {
                reports.value.forEach(report => {
                    report.selected = val;
                });
                selectedReports.value = val ? [...reports.value] : [];
            };
            
            const handleItemSelect = (report) => {
                if (report.selected) {
                    selectedReports.value.push(report);
                } else {
                    selectedReports.value = selectedReports.value.filter(item => item.id !== report.id);
                }
                allSelected.value = reports.value.length > 0 && reports.value.every(item => item.selected);
            };

            // 检查导航状态
            const checkNavStatus = async () => {
                try {
                    const response = await axios.get('./checkNav');
                    navExists.value = response.data.exists;
                } catch (error) {
                    console.error('Error:', error);
                }
            };

            // 切换导航菜单
            const toggleNav = async () => {
                navLoading.value = true;
                try {
                    const response = await axios.get('./toggleNav');
                    if (response.data.code === 1) {
                        ElMessage.success(response.data.msg);
                        navExists.value = !navExists.value;
                    } else {
                        ElMessage.error(response.data.msg || '操作失败');
                    }
                } catch (error) {
                    console.error('Error:', error);
                    ElMessage.error('操作失败');
                } finally {
                    navLoading.value = false;
                }
            };

            const deleteReport = (report) => {
                ElMessageBox.confirm('确定要删除此举报吗? 此操作不可恢复', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    try {
                        const response = await api.deleteReport(report.id);
                        if (response.data.code === 200) {
                            ElMessage.success('删除成功');
                            getReports();
                        } else {
                            ElMessage.error(response.data.msg || '删除失败');
                        }
                    } catch (error) {
                        console.error('Error:', error);
                        ElMessage.error('删除失败');
                    }
                }).catch(() => {});
            };

            const showRejectDialog = (report) => {
                rejectForm.id = report.id;
                rejectForm.reason = '';
                rejectDialogVisible.value = true;
            };

            const submitReject = async () => {
                if (!rejectForm.reason.trim()) {
                    ElMessage.warning('请输入驳回原因');
                    return;
                }

                rejectLoading.value = true;
                try {
                    const response = await api.rejectReport(rejectForm.id, rejectForm.reason);

                    if (response.data.code === 200) {
                        ElMessage.success(response.data.msg);
                        rejectDialogVisible.value = false;
                        getReports();
                    } else {
                        ElMessage.error(response.data.msg || '驳回失败');
                    }
                } catch (error) {
                    console.error('Error:', error);
                    ElMessage.error('驳回失败');
                } finally {
                    rejectLoading.value = false;
                }
            };

            // 提交处理举报
            const submitHandleReport = async () => {
                if (!currentProcessingReport.value) return;
                
                processLoading.value = true;
                try {
                    // 处理批量情况
                    if (currentProcessingReport.value.isBatch) {
                        const pendingReports = currentProcessingReport.value.reports;
                        const promises = pendingReports.map(report => 
                            api.updateReportStatus(report.id, 1, riskExpireTime.value)
                        );
                        
                        await Promise.all(promises);
                        ElMessage.success(`成功处理 ${pendingReports.length} 条举报`);
                    } else {
                        // 处理单个举报
                        const response = await api.updateReportStatus(currentProcessingReport.value.id, 1, riskExpireTime.value);
                        if (response.data.code === 200) {
                            ElMessage.success('操作成功');
                            
                            // 如果是在详情页面处理的，更新详情数据
                            if (reportDetailVisible.value && currentReport.value && currentReport.value.id === currentProcessingReport.value.id) {
                                currentReport.value.status = 1;
                                currentReport.value.status_text = '已处理';
                                currentReport.value.update_time = Math.floor(Date.now() / 1000);
                                currentReport.value.update_time_text = new Date().toLocaleString();
                            }
                        } else {
                            ElMessage.error(response.data.msg || '操作失败');
                            return;
                        }
                    }
                    
                    // 关闭对话框并刷新列表
                    handleReportDialogVisible.value = false;
                    getReports();
                } catch (error) {
                    console.error('Error:', error);
                    ElMessage.error('操作失败');
                } finally {
                    processLoading.value = false;
                }
            };

            // 生命周期钩子
            onMounted(() => {
                // 检查设备是否为移动设备
                isMobile.value = window.innerWidth <= 768;
                // 添加窗口大小变化事件监听
                window.addEventListener('resize', () => {
                    isMobile.value = window.innerWidth <= 768;
                });
                
                initPage();
                checkNavStatus();
            });

            return {
                activeTab,
                params,
                loading,
                saving,
                reports,
                reportsLoading,
                reportTableRef,
                selectedReports,
                reportSearch,
                reportPagination,
                currentReport,
                reportDetailVisible,
                imagePreviewVisible,
                previewImageUrl,
                templateOptions,
                componentOptions,
                tableRowClassName,
                addQuestionType,
                removeQuestionType,
                addOption,
                removeOption,
                saveParams,
                searchReports,
                resetSearch,
                handleReportSizeChange,
                handleReportCurrentChange,
                viewReport,
                markReportAsHandled,
                batchMarkAsHandled,
                handleSelectionChange,
                toggleSelection,
                previewImage,
                moveQuestionType,
                allSelected,
                selectAll,
                handleItemSelect,
                isMobile,
                navExists,
                navLoading,
                toggleNav,
                rejectDialogVisible,
                rejectLoading,
                rejectForm,
                deleteReport,
                showRejectDialog,
                submitReject,
                riskExpireTime,
                expireTimeOptions,
                handleReportDialogVisible,
                currentProcessingReport,
                processLoading,
                submitHandleReport,
            };
        }
    });

    // 使用 Element Plus
    app.use(ElementPlus, {
        locale: ElementPlusLocaleZhCn
    });
    
    // 挂载应用
    app.mount('#app');
    </script>
</body>
</html>