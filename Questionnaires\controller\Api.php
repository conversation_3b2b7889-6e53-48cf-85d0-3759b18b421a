<?php
namespace plugin\Questionnaires\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;

class Api extends BasePlugin
{
    protected $scene = [
        'admin',
    ];
    protected $noNeedLogin = ['buyers', 'submitReport', 'upload','getParams','submitReport', 'showSuccess','queryReportsByEmail', 'captcha'];

    public function index()
    {
        return View::fetch();
    }

    // 添加买家举报入口页面
    public function buyers()
    {
        try {
            // 检查访问权限
            if (!$this->checkPageAccess('/plugin/Questionnaires/api/buyers')) {
                // 获取网站配置用于美化关闭页面
                $logo = Db::name('system_config')
                    ->where('type', 'website')
                    ->where('name', 'logo')
                    ->value('value');
                
                // 处理logo URL
                if (preg_match('/^https?:\/\/(oss\.|cos\.|[-\w]+\.qiniucdn\.|[-\w]+)/i', $logo)) {
                    $logo = $logo;
                } else if (strpos($logo, 'uploads/') === 0) {
                    $logo = request()->domain() . '/' . $logo;
                } else {
                    $logo = request()->domain() . '/' . ltrim($logo, '/');
                }
                
                $siteName = Db::name('system_config')
                    ->where('type', 'website')
                    ->where('name', 'app_name')
                    ->value('value');
                
                $favicon = Db::name('system_config')
                    ->where('type', 'website')
                    ->where('name', 'favicon')
                    ->value('value');
                
                // 处理 favicon URL
                if (preg_match('/^https?:\/\/(oss\.|cos\.|[-\w]+\.qiniucdn\.|[-\w]+)/i', $favicon)) {
                    $favicon = $favicon;
                } else if (strpos($favicon, 'uploads/') === 0) {
                    $favicon = request()->domain() . '/' . $favicon;
                } else {
                    $favicon = request()->domain() . '/' . ltrim($favicon, '/');
                }
                
                // 获取网站配置信息
                $icpNumber = Db::name('system_config')
                    ->where('type', 'website')
                    ->where('name', 'icp_number')
                    ->value('value');
                
                $gaNumber = Db::name('system_config')
                    ->where('type', 'website')
                    ->where('name', 'ga_number')
                    ->value('value');
                
                // 传递数据到视图
                View::assign([
                    'logo' => $logo,
                    'siteName' => $siteName,
                    'favicon' => $favicon,
                    'icpNumber' => $icpNumber,
                    'gaNumber' => $gaNumber,
                ]);
                
                // 返回美化的功能关闭页面
                return View::fetch('buyers/closed');
            }
            
            // 获取网站配置
            $logo = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'logo')
                ->value('value');
            
            // 处理logo URL
            if (preg_match('/^https?:\/\/(oss\.|cos\.|[-\w]+\.qiniucdn\.|[-\w]+)/i', $logo)) {
                $logo = $logo;
            } else if (strpos($logo, 'uploads/') === 0) {
                $logo = request()->domain() . '/' . $logo;
            } else {
                $logo = request()->domain() . '/' . ltrim($logo, '/');
            }
            
            $siteName = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'app_name')
                ->value('value');
            
            $favicon = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'favicon')
                ->value('value');
            
            // 处理 favicon URL
            if (preg_match('/^https?:\/\/(oss\.|cos\.|[-\w]+\.qiniucdn\.|[-\w]+)/i', $favicon)) {
                $favicon = $favicon;
            } else if (strpos($favicon, 'uploads/') === 0) {
                $favicon = request()->domain() . '/' . $favicon;
            } else {
                $favicon = request()->domain() . '/' . ltrim($favicon, '/');
            }
            
            // 获取导航菜单数据
            $navItems = Db::name('nav')
                ->where('status', 1)
                ->where('pid', 0)
                ->field(['id', 'name', 'href', 'target'])
                ->order('sort asc')
                ->select()
                ->each(function($item) {
                    // 获取子菜单
                    $item['children'] = Db::name('nav')
                        ->where('status', 1)
                        ->where('pid', $item['id'])
                        ->field(['name', 'href', 'target'])
                        ->order('sort asc')
                        ->select()
                        ->toArray();
                    return $item;
                })
                ->toArray();

            // 获取网站配置信息
            $icpNumber = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'icp_number')
                ->value('value');
            
            $gaNumber = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'ga_number')
                ->value('value');
            
            $icpCert = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'icp_cert')
                ->value('value');

            View::assign([
                'logo' => $logo,
                'siteName' => $siteName,
                'favicon' => $favicon,
                'navItems' => json_encode($navItems, JSON_UNESCAPED_UNICODE),
                'icpNumber' => $icpNumber,
                'gaNumber' => $gaNumber,
                'icpCert' => $icpCert,
            ]);
            
            return View::fetch('buyers/index');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 检查页面访问权限
     */
    private function checkPageAccess($path)
    {
        try {
            // 检查是否存在对应的菜单(包括顶级菜单和子菜单)
            $menu = Db::name('nav')
                ->where('href', $path)
                ->where('status', 1)  // 确保菜单是启用状态
                ->find();
            
            // 如果找到启用的菜单项就允许访问
            return !empty($menu);
            
        } catch (\Exception $e) {
            // 记录错误日志但不暴露给用户
            error_log("检查页面访问权限出错: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 显示成功页面
     */
    public function showSuccess()
    {
        try {
            // 获取网站配置
            $logo = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'logo')
                ->value('value');
            
            // 处理logo URL
            if (preg_match('/^https?:\/\/(oss\.|cos\.|[-\w]+\.qiniucdn\.|[-\w]+)/i', $logo)) {
                $logo = $logo;
            } else if (strpos($logo, 'uploads/') === 0) {
                $logo = request()->domain() . '/' . $logo;
            } else {
                $logo = request()->domain() . '/' . ltrim($logo, '/');
            }
            
            $siteName = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'app_name')
                ->value('value');
            
            $favicon = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'favicon')
                ->value('value');
            
            // 处理 favicon URL
            if (preg_match('/^https?:\/\/(oss\.|cos\.|[-\w]+\.qiniucdn\.|[-\w]+)/i', $favicon)) {
                $favicon = $favicon;
            } else if (strpos($favicon, 'uploads/') === 0) {
                $favicon = request()->domain() . '/' . $favicon;
            } else {
                $favicon = request()->domain() . '/' . ltrim($favicon, '/');
            }

            // 获取网站配置信息
            $icpNumber = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'icp_number')
                ->value('value');
            
            $gaNumber = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'ga_number')
                ->value('value');
            
            $icpCert = Db::name('system_config')
                ->where('type', 'website')
                ->where('name', 'icp_cert')
                ->value('value');

            View::assign([
                'logo' => $logo,
                'siteName' => $siteName,
                'favicon' => $favicon,
                'icpNumber' => $icpNumber,
                'gaNumber' => $gaNumber,
                'icpCert' => $icpCert,
            ]);
            
            return View::fetch('buyers/success');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 操作成功返回的数据
     * @param string $msg    提示信息
     * @param mixed  $data   要返回的数据
     * @param int    $code   错误码，默认为200
     * @param string $type   输出类型
     * @param array  $header 发送的 Header 信息
     */
    public function success($msg = '', $data = null, $code = 200, $type = null, array $header = [])
    {
        return parent::success($msg, $data, $code, $type, $header);
    }

    // 获取配置数据
    public function fetchData()
    {
        $data = [
            // 问卷基本配置
            'questionnaire_status' => intval(plugconf("Questionnaires.status") ?? 0),
            'allow_anonymous' => intval(plugconf("Questionnaires.allow_anonymous") ?? 0),
            'show_results' => intval(plugconf("Questionnaires.show_results") ?? 0),
            'expire_days' => intval(plugconf("Questionnaires.expire_days") ?? 30),
            'max_responses' => intval(plugconf("Questionnaires.max_responses") ?? 1000),
            // 通知配置
            'notify_status' => intval(plugconf("Questionnaires.notify_status") ?? 0),
            'notify_email' => plugconf("Questionnaires.notify_email") ?: '',
            'notify_admin' => intval(plugconf("Questionnaires.notify_admin") ?? 0),
            // 样式配置
            'theme_color' => plugconf("Questionnaires.theme_color") ?: '#1890ff',
            'custom_css' => plugconf("Questionnaires.custom_css") ?: '',
            // 权限配置
            'user_min_level' => intval(plugconf("Questionnaires.user_min_level") ?? 0),
            'admin_min_level' => intval(plugconf("Questionnaires.admin_min_level") ?? 99),
        ];

        $this->success('success', $data);
    }

    // 保存基本配置
    public function saveSettings()
    {
        $status = $this->request->post('status/d', 0);
        $allowAnonymous = $this->request->post('allow_anonymous/d', 0);
        $showResults = $this->request->post('show_results/d', 0);
        $expireDays = $this->request->post('expire_days/d', 30);
        $maxResponses = $this->request->post('max_responses/d', 1000);
        $userMinLevel = $this->request->post('user_min_level/d', 0);
        $adminMinLevel = $this->request->post('admin_min_level/d', 99);

        // 验证参数
        if ($expireDays < 1 || $expireDays > 365) {
            $this->error('过期天数必须在1-365之间');
        }
        
        if ($maxResponses < 10 || $maxResponses > 10000) {
            $this->error('最大回答数必须在10-10000之间');
        }

        // 保存配置
        plugconf("Questionnaires.status", $status);
        plugconf("Questionnaires.allow_anonymous", $allowAnonymous);
        plugconf("Questionnaires.show_results", $showResults);
        plugconf("Questionnaires.expire_days", $expireDays);
        plugconf("Questionnaires.max_responses", $maxResponses);
        plugconf("Questionnaires.user_min_level", $userMinLevel);
        plugconf("Questionnaires.admin_min_level", $adminMinLevel);

        $this->success('保存成功');
    }

    // 保存通知配置
    public function saveNotify()
    {
        $status = $this->request->post('status/d', 0);
        $email = $this->request->post('email/s', '');
        $notifyAdmin = $this->request->post('notify_admin/d', 0);

        // 验证邮箱
        if ($status && !empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $this->error('邮箱格式不正确');
        }

        plugconf("Questionnaires.notify_status", $status);
        plugconf("Questionnaires.notify_email", $email);
        plugconf("Questionnaires.notify_admin", $notifyAdmin);

        $this->success('保存成功');
    }

    // 保存样式配置
    public function saveTheme()
    {
        $themeColor = $this->request->post('theme_color/s', '#1890ff');
        $customCss = $this->request->post('custom_css/s', '');

        plugconf("Questionnaires.theme_color", $themeColor);
        plugconf("Questionnaires.custom_css", $customCss);

        $this->success('保存成功');
    }

    // 获取问卷列表
    public function getQuestionnaireList()
    {
        try {
            $page = $this->request->get('page/d', 1);
            $limit = $this->request->get('limit/d', 10);
            $keyword = $this->request->get('keyword/s', '');

            $query = Db::name('questionnaires');
            
            if (!empty($keyword)) {
                $query->where('title', 'like', "%{$keyword}%");
            }

            $total = $query->count();
            $list = $query->order('id', 'desc')
                ->page($page, $limit)
                ->select()
                ->toArray();

            // 处理问卷数据
            foreach ($list as &$item) {
                $item['create_time_text'] = date('Y-m-d H:i:s', $item['create_time']);
                $item['expire_time_text'] = $item['expire_time'] ? date('Y-m-d H:i:s', $item['expire_time']) : '永不过期';
                $item['response_count'] = Db::name('questionnaire_responses')
                    ->where('questionnaire_id', $item['id'])
                    ->count();
            }

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => [
                    'total' => $total,
                    'list' => $list
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '获取问卷列表失败：' . $e->getMessage()
            ]);
        }
    }

    // 获取单个问卷详情
    public function getQuestionnaire()
    {
        try {
            $id = $this->request->get('id/d', 0);
            
            if (empty($id)) {
                return json(['code' => 500, 'msg' => '问卷ID不能为空']);
            }

            $questionnaire = Db::name('questionnaires')->where('id', $id)->find();
            
            if (empty($questionnaire)) {
                return json(['code' => 404, 'msg' => '问卷不存在']);
            }

            // 获取问题列表
            $questions = Db::name('questionnaire_questions')
                ->where('questionnaire_id', $id)
                ->order('sort', 'asc')
                ->select()
                ->toArray();

            // 处理问题选项
            foreach ($questions as &$question) {
                if (in_array($question['type'], ['radio', 'checkbox', 'select'])) {
                    $question['options'] = json_decode($question['options'], true) ?: [];
                }
            }

            $questionnaire['questions'] = $questions;
            $questionnaire['response_count'] = Db::name('questionnaire_responses')
                ->where('questionnaire_id', $id)
                ->count();

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $questionnaire
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '获取问卷失败：' . $e->getMessage()
            ]);
        }
    }

    // 创建/更新问卷
    public function saveQuestionnaire()
    {
        try {
            $id = $this->request->post('id/d', 0);
            $title = $this->request->post('title/s', '');
            $description = $this->request->post('description/s', '');
            $status = $this->request->post('status/d', 0);
            $expireTime = $this->request->post('expire_time/s', '');
            $questions = $this->request->post('questions/a', []);

            // 验证数据
            if (empty($title)) {
                return json(['code' => 500, 'msg' => '问卷标题不能为空']);
            }

            if (empty($questions)) {
                return json(['code' => 500, 'msg' => '问卷至少需要包含一个问题']);
            }

            // 处理过期时间
            $expireTimestamp = empty($expireTime) ? 0 : strtotime($expireTime);

            Db::startTrans();
            try {
                $data = [
                    'title' => $title,
                    'description' => $description,
                    'status' => $status,
                    'expire_time' => $expireTimestamp,
                    'update_time' => time()
                ];

                if ($id) {
                    // 更新问卷
                    Db::name('questionnaires')->where('id', $id)->update($data);
                    // 删除旧问题
                    Db::name('questionnaire_questions')->where('questionnaire_id', $id)->delete();
                } else {
                    // 新增问卷
                    $data['create_time'] = time();
                    $id = Db::name('questionnaires')->insertGetId($data);
                }

                // 保存问题
                foreach ($questions as $index => $question) {
                    if (empty($question['title'])) {
                        continue;
                    }

                    $options = isset($question['options']) ? json_encode($question['options']) : '';
                    
                    Db::name('questionnaire_questions')->insert([
                        'questionnaire_id' => $id,
                        'title' => $question['title'],
                        'type' => $question['type'] ?? 'text',
                        'required' => isset($question['required']) ? intval($question['required']) : 0,
                        'options' => $options,
                        'sort' => $index + 1,
                        'create_time' => time()
                    ]);
                }

                Db::commit();
                return json(['code' => 200, 'msg' => '保存成功', 'data' => ['id' => $id]]);
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 500, 'msg' => '保存问卷失败：' . $e->getMessage()]);
            }
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '系统错误：' . $e->getMessage()]);
        }
    }

    // 删除问卷
    public function deleteQuestionnaire()
    {
        try {
            $id = $this->request->post('id/d', 0);
            
            if (empty($id)) {
                return json(['code' => 500, 'msg' => '问卷ID不能为空']);
            }

            Db::startTrans();
            try {
                // 删除问卷
                Db::name('questionnaires')->where('id', $id)->delete();
                // 删除问题
                Db::name('questionnaire_questions')->where('questionnaire_id', $id)->delete();
                // 删除回答
                Db::name('questionnaire_responses')->where('questionnaire_id', $id)->delete();
                
                Db::commit();
                return json(['code' => 200, 'msg' => '删除成功']);
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 500, 'msg' => '删除问卷失败：' . $e->getMessage()]);
            }
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '系统错误：' . $e->getMessage()]);
        }
    }

    // 提交问卷回答
    public function submitResponse()
    {
        try {
            $questionnaireId = $this->request->post('questionnaire_id/d', 0);
            $answers = $this->request->post('answers/a', []);
            $userId = $this->request->userId ?: 0;
            
            if (empty($questionnaireId)) {
                return json(['code' => 500, 'msg' => '问卷ID不能为空']);
            }

            if (empty($answers)) {
                return json(['code' => 500, 'msg' => '回答不能为空']);
            }

            // 检查问卷是否存在且有效
            $questionnaire = Db::name('questionnaires')
                ->where('id', $questionnaireId)
                ->where('status', 1)
                ->find();
                
            if (empty($questionnaire)) {
                return json(['code' => 404, 'msg' => '问卷不存在或已关闭']);
            }

            // 检查是否过期
            if ($questionnaire['expire_time'] > 0 && $questionnaire['expire_time'] < time()) {
                return json(['code' => 500, 'msg' => '问卷已过期']);
            }

            // 检查匿名提交权限
            $allowAnonymous = plugconf("Questionnaires.allow_anonymous") ?? 0;
            if (!$userId && !$allowAnonymous) {
                return json(['code' => 403, 'msg' => '请先登录后再提交']);
            }

            // 检查用户是否已提交过
            if ($userId) {
                $exists = Db::name('questionnaire_responses')
                    ->where('questionnaire_id', $questionnaireId)
                    ->where('user_id', $userId)
                    ->find();
                    
                if ($exists) {
                    return json(['code' => 500, 'msg' => '您已提交过该问卷']);
                }
            }

            // 获取问题列表
            $questions = Db::name('questionnaire_questions')
                ->where('questionnaire_id', $questionnaireId)
                ->select()
                ->toArray();

            // 验证必答题是否已回答
            $questionMap = [];
            foreach ($questions as $question) {
                $questionMap[$question['id']] = $question;
                if ($question['required'] && !isset($answers[$question['id']])) {
                    return json(['code' => 500, 'msg' => '问题"' . $question['title'] . '"为必答题']);
                }
            }

            // 处理各种答案类型
            $insertData = [];
            foreach ($answers as $questionId => $answer) {
                $question = $questionMap[$questionId] ?? null;
                
                // 跳过不存在的问题
                if (!$question) {
                    continue;
                }
                
                // 根据问题类型处理答案
                $processedAnswer = $answer;
                
                if (in_array($question['type'], ['checkbox', 'select'])) {
                    // 多选类型，确保是数组格式的JSON字符串
                    if (is_array($answer)) {
                        $processedAnswer = json_encode($answer);
                    } elseif (!is_string($answer) || !$this->isJson($answer)) {
                        $processedAnswer = '[]';
                    }
                } elseif (in_array($question['type'], ['rate', 'slider'])) {
                    // 评分和滑块类型，确保是数字
                    $processedAnswer = is_numeric($answer) ? (float)$answer : 0;
                }
                
                $insertData[] = [
                    'questionnaire_id' => $questionnaireId,
                    'question_id' => $questionId,
                    'answer' => is_array($processedAnswer) ? json_encode($processedAnswer) : $processedAnswer,
                    'user_id' => $userId,
                    'ip' => $this->request->ip(),
                    'create_time' => time()
                ];
            }

            // 保存回答
            $responseId = Db::name('questionnaire_responses')->insertGetId([
                'questionnaire_id' => $questionnaireId,
                'user_id' => $userId,
                'ip' => $this->request->ip(),
                'create_time' => time()
            ]);

            // 保存回答详情
            foreach ($insertData as $data) {
                $data['response_id'] = $responseId;
                Db::name('questionnaire_response_details')->insert($data);
            }

            // 处理通知
            $notifyStatus = plugconf("Questionnaires.notify_status") ?? 0;
            if ($notifyStatus) {
                $this->sendNotification($questionnaire, $userId);
            }

            return json(['code' => 200, 'msg' => '提交成功']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '提交失败：' . $e->getMessage()]);
        }
    }

    /**
     * 检查字符串是否为有效的JSON
     */
    private function isJson($string) {
        if (!is_string($string)) {
            return false;
        }
        json_decode($string);
        return (json_last_error() === JSON_ERROR_NONE);
    }

    // 获取问卷统计结果
    public function getStatistics()
    {
        try {
            $id = $this->request->get('id/d', 0);
            
            if (empty($id)) {
                return json(['code' => 500, 'msg' => '问卷ID不能为空']);
            }

            $questionnaire = Db::name('questionnaires')->where('id', $id)->find();
            
            if (empty($questionnaire)) {
                return json(['code' => 404, 'msg' => '问卷不存在']);
            }

            // 获取问题列表
            $questions = Db::name('questionnaire_questions')
                ->where('questionnaire_id', $id)
                ->order('sort', 'asc')
                ->select()
                ->toArray();

            // 获取回答总数
            $totalResponses = Db::name('questionnaire_responses')
                ->where('questionnaire_id', $id)
                ->count();

            $result = [
                'questionnaire' => $questionnaire,
                'total_responses' => $totalResponses,
                'questions' => []
            ];

            // 统计每个问题的回答
            foreach ($questions as $question) {
                $questionResult = [
                    'id' => $question['id'],
                    'title' => $question['title'],
                    'type' => $question['type']
                ];

                // 选择题统计
                if (in_array($question['type'], ['radio', 'checkbox', 'select'])) {
                    $options = json_decode($question['options'], true) ?: [];
                    $stats = [];
                    
                    // 初始化选项统计
                    foreach ($options as $option) {
                        $stats[$option] = 0;
                    }
                    
                    // 统计回答
                    $answers = Db::name('questionnaire_response_details')
                        ->alias('d')
                        ->join('questionnaire_responses r', 'd.response_id = r.id')
                        ->where('r.questionnaire_id', $id)
                        ->where('d.question_id', $question['id'])
                        ->field('d.answer')
                        ->select()
                        ->toArray();
                        
                    foreach ($answers as $answer) {
                        if ($question['type'] == 'checkbox') {
                            $selectedOptions = json_decode($answer['answer'], true) ?: [];
                            foreach ($selectedOptions as $selected) {
                                if (isset($stats[$selected])) {
                                    $stats[$selected]++;
                                }
                            }
                        } else {
                            if (isset($stats[$answer['answer']])) {
                                $stats[$answer['answer']]++;
                            }
                        }
                    }
                    
                    // 计算百分比
                    $optionStats = [];
                    foreach ($stats as $option => $count) {
                        $percent = $totalResponses > 0 ? round(($count / $totalResponses) * 100, 2) : 0;
                        $optionStats[] = [
                            'option' => $option,
                            'count' => $count,
                            'percent' => $percent
                        ];
                    }
                    
                    $questionResult['statistics'] = $optionStats;
                } 
                // 文本题列出回答
                else {
                    $answers = Db::name('questionnaire_response_details')
                        ->alias('d')
                        ->join('questionnaire_responses r', 'd.response_id = r.id')
                        ->where('r.questionnaire_id', $id)
                        ->where('d.question_id', $question['id'])
                        ->field('d.answer, r.user_id, r.create_time')
                        ->order('r.create_time', 'desc')
                        ->select()
                        ->toArray();
                        
                    $textAnswers = [];
                    foreach ($answers as $answer) {
                        $textAnswers[] = [
                            'user_id' => $answer['user_id'],
                            'answer' => $answer['answer'],
                            'create_time' => date('Y-m-d H:i:s', $answer['create_time'])
                        ];
                    }
                    
                    $questionResult['answers'] = $textAnswers;
                }
                
                $result['questions'][] = $questionResult;
            }

            return json(['code' => 200, 'msg' => '获取成功', 'data' => $result]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '获取统计失败：' . $e->getMessage()]);
        }
    }

    // 发送通知
    private function sendNotification($questionnaire, $userId)
    {
        try {
            $notifyEmail = plugconf("Questionnaires.notify_email");
            $notifyAdmin = plugconf("Questionnaires.notify_admin") ?? 0;
            
            if (empty($notifyEmail) && !$notifyAdmin) {
                return;
            }
            
            // 构建通知内容
            $subject = "问卷提交通知：{$questionnaire['title']}";
            $content = "有新的问卷提交：\n";
            $content .= "问卷标题：{$questionnaire['title']}\n";
            $content .= "提交时间：" . date('Y-m-d H:i:s') . "\n";
            
            if ($userId) {
                $user = Db::name('user')->where('id', $userId)->find();
                if ($user) {
                    $content .= "提交用户：{$user['username']}\n";
                }
            } else {
                $content .= "提交用户：匿名用户\n";
            }
            
            // 发送邮件通知
            if (!empty($notifyEmail)) {
                // 这里调用系统邮件发送功能
                // 根据实际系统替换成对应的邮件发送方法
                $service = new \app\common\service\EmailService();
                $res = $service->from($notifyEmail, '系统通知')
                    ->subject($subject)
                    ->message($content)
                    ->to($notifyEmail)
                    ->send();

                if (!$res) {
                    error_log("Questionnaires: 发送通知失败: " . $service->getError());
                }
            }
            
            // 发送管理员通知
            if ($notifyAdmin) {
                // 发送系统通知给管理员
                // 根据实际系统替换成对应的通知方法
                admin_notice($subject, $content);
            }
        } catch (\Exception $e) {
            error_log("Questionnaires: 发送通知失败: " . $e->getMessage());
        }
    }

    // 获取问卷参数设置
    public function getParams()
    {
        try {
            // 检查文件是否存在
            $paramsFile = app()->getRootPath() . 'plugin/Questionnaires/params.php';
            if (!file_exists($paramsFile)) {
                // 如果文件不存在，返回默认配置
                return json([
                    'code' => 200,
                    'msg' => '获取成功',
                    'data' => [
                        'questionnaire_description' => '',
                        'question_types' => [],
                        'field_mappings' => [],
                        'upload_api' => '',
                        'template_type' => 'default'
                    ]
                ]);
            }
            
            // 读取params.php文件内容
            $params = include $paramsFile;
            
            // 确保field_mappings字段存在
            if (!isset($params['field_mappings'])) {
                $params['field_mappings'] = [];
            }
            
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $params
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '获取参数失败：' . $e->getMessage()
            ]);
        }
    }

    // 保存问卷参数设置
    public function saveParams()
    {
        try {
            $input = input('post.');
            
            if (!isset($input['question_types']) || !is_array($input['question_types'])) {
                return json(['code' => 400, 'msg' => '问题类型参数错误']);
            }
            
            // 检查并确保必填的基本字段存在
            $requiredFields = ['shop_link', 'shop_name', 'product_link', 'violation_type', 'proofs', 'email'];
            $existingFields = array_column($input['question_types'], 'id');
            
            $missingFields = array_diff($requiredFields, $existingFields);
            if (!empty($missingFields)) {
                return json(['code' => 400, 'msg' => '缺少必要的字段: ' . implode(', ', $missingFields)]);
            }
            
            // 确保备注字段存在
            $remarkExists = false;
            foreach ($input['question_types'] as $type) {
                if ($type['id'] === 'remark') {
                    $remarkExists = true;
                    break;
                }
            }
            
            // 如果没有备注字段，添加一个默认的备注字段
            if (!$remarkExists) {
                $input['question_types'][] = [
                    'id' => 'remark',
                    'name' => '备注说明',
                    'component' => 'textarea',
                    'required' => false,
                    'placeholder' => '请输入补充说明（可选）',
                    'icon' => 'Document'
                ];
            }

            // 处理问题类型
            foreach ($input['question_types'] as &$type) {
                // 验证必要的字段
                if (empty($type['id']) || empty($type['name']) || empty($type['component'])) {
                    return json(['code' => 400, 'msg' => '问题类型的ID、名称和组件类型不能为空']);
                }
                
                // 确保required字段始终是布尔值
                $type['required'] = $type['required'] === true || $type['required'] === 'true' || $type['required'] === 1;
                
                // 处理options字段
                if (isset($type['options']) && !is_array($type['options'])) {
                    if (is_string($type['options'])) {
                        // 尝试解析JSON
                        $options = json_decode($type['options'], true);
                        if (is_array($options)) {
                            $type['options'] = $options;
                        } else {
                            // 如果不是有效的JSON，尝试按逗号分隔
                            $type['options'] = explode(',', $type['options']);
                        }
                    } else {
                        $type['options'] = [];
                    }
                }
            }
            
            $description = $this->request->post('questionnaire_description/s', '');
            $questionTypes = $this->request->post('question_types/a', []);
            $uploadApi = $this->request->post('upload_api/s', '');
            $templateType = $this->request->post('template_type/s', 'default');
            
            // 获取限制配置参数
            $emailSubmitLimit = $this->request->post('email_submit_limit/d', 3);
            $ipEmailLimit = $this->request->post('ip_email_limit/d', 5);
            
            // 保存到input数组中
            $input['email_submit_limit'] = $emailSubmitLimit;
            $input['ip_email_limit'] = $ipEmailLimit;
            
            // 获取原有的问题类型配置
            $paramsFile = app()->getRootPath() . 'plugin/Questionnaires/params.php';
            $oldParams = file_exists($paramsFile) ? include $paramsFile : ['question_types' => []];
            $oldQuestionTypes = array_column($oldParams['question_types'] ?? [], null, 'id');
            
            // 获取要删除的字段
            $newTypeIds = array_column($questionTypes, 'id');
            $deletedTypes = array_diff_key($oldQuestionTypes, array_flip($newTypeIds));
            
            // 处理删除的字段
            if (!empty($deletedTypes)) {
                try {
                    $tableName = Db::name('plugin_reports')->getTable();
                    foreach ($deletedTypes as $type) {
                        $fieldName = $type['id'];
                        
                        // 检查字段是否存在
                        $existingColumns = Db::query("SHOW COLUMNS FROM `{$tableName}` LIKE '{$fieldName}'");
                        if (!empty($existingColumns)) {
                            // 删除字段
                            $sql = "ALTER TABLE `{$tableName}` DROP COLUMN `{$fieldName}`";
                            Db::execute($sql);
                        }
                    }
                } catch (\Exception $e) {
                    return json(['code' => 500, 'msg' => '删除字段失败：' . $e->getMessage()]);
                }
            }
            
            // 验证问题类型
            foreach ($questionTypes as &$type) {
                if (empty($type['id']) || empty($type['name'])) {
                    return json(['code' => 400, 'msg' => '问题类型的ID和名称不能为空']);
                }
                
                // 验证ID格式
                if (!preg_match('/^[a-zA-Z0-9_]+$/', $type['id'])) {
                    return json(['code' => 400, 'msg' => '问题类型ID只能包含字母、数字和下划线']);
                }
                
                // XSS过滤
                $type['name'] = xss_safe($type['name']);
                if (!empty($type['placeholder'])) {
                    $type['placeholder'] = xss_safe($type['placeholder']);
                }
                
                // 确保required字段为布尔值
                $type['required'] = isset($type['required']) && ($type['required'] === true || $type['required'] === 'true' || $type['required'] === 1 || $type['required'] === '1');
                
                // 处理其他字段类型
                if (isset($type['component']) && $type['component'] === 'other') {
                    // 验证字段键名
                    if (empty($type['fieldKey'])) {
                        $type['fieldKey'] = $type['id'];
                    }
                    
                    // 处理自动创建字段
                    $type['autoCreate'] = true;
                    
                    // 设置默认数据类型为string
                    if (!isset($type['dataType'])) {
                        $type['dataType'] = 'string';
                    }
                    
                    // 验证数据类型
                    $allowedDataTypes = ['string', 'number', 'boolean', 'date', 'object', 'array'];
                    if (!in_array($type['dataType'], $allowedDataTypes)) {
                        $type['dataType'] = 'string';
                    }
                }
                
                // 对所有类型都尝试创建字段
                try {
                    $fieldName = $type['id'];
                    
                    // 使用 Db::name() 获取完整表名
                    $tableName = Db::name('plugin_reports')->getTable();
                    
                    // 检查字段是否存在
                    $existingColumns = Db::query("SHOW COLUMNS FROM `{$tableName}` LIKE '{$fieldName}'");
                    
                    if (empty($existingColumns)) {
                        // 根据数据类型生成SQL
                        $sqlType = $this->getSqlType($type['dataType'] ?? 'string');
                        $defaultValue = $this->getDefaultValue($type['dataType'] ?? 'string', $type['defaultValue'] ?? null);
                        
                        // 创建字段
                        $sql = "ALTER TABLE `{$tableName}` ADD COLUMN `{$fieldName}` {$sqlType}";
                        
                        // 设置默认值
                        if ($defaultValue !== null) {
                            $sql .= " DEFAULT {$defaultValue}";
                        } else {
                            // 为不同类型设置合适的默认值
                            switch ($sqlType) {
                                case 'VARCHAR(255)':
                                    $sql .= " DEFAULT ''";
                                    break;
                                case 'DECIMAL(10,2)':
                                    $sql .= " DEFAULT 0";
                                    break;
                                case 'TINYINT(1)':
                                    $sql .= " DEFAULT 0";
                                    break;
                                case 'DATETIME':
                                    $sql .= " DEFAULT CURRENT_TIMESTAMP";
                                    break;
                                case 'TEXT':
                                    $sql .= " DEFAULT NULL";
                                    break;
                                default:
                                    $sql .= " DEFAULT ''";
                            }
                        }
                        
                        // 添加字段注释
                        $sql .= " COMMENT '" . addslashes($type['name']) . "'";
                        
                        Db::execute($sql);
                    }
                } catch (\Exception $e) {
                    throw new \Exception("创建字段 {$fieldName} 失败：" . $e->getMessage());
                }
                
                // 保存字段映射
                $fieldMappings[$type['id']] = [
                    'id' => $type['id'],
                    'name' => $type['name'],
                    'dataType' => $type['dataType'] ?? 'string',
                    'defaultValue' => $type['defaultValue'] ?? ''
                ];
            }
            
            // 构建参数内容
            $content = "<?php\n\nreturn [\n";
            $content .= "    // 默认问卷说明\n";
            $content .= "    'questionnaire_description' => '" . str_replace("'", "\\'", $description) . "',\n";
            $content .= "    \n    // 默认问题类型\n";
            $content .= "    'question_types' => " . var_export($questionTypes, true) . ",\n";
            $content .= "    \n    // 字段映射\n";
            $content .= "    'field_mappings' => " . var_export($fieldMappings, true) . ",\n";
            $content .= "    \n    // 文件上传配置\n";
            $content .= "    'upload_api' => '" . str_replace("'", "\\'", $uploadApi) . "',\n";
            $content .= "    \n    // 默认模板类型\n";
            $content .= "    'template_type' => '" . $templateType . "',\n";
            $content .= "    \n    // 邮箱提交次数限制，默认3次\n";
            $content .= "    'email_submit_limit' => " . (int)($input['email_submit_limit'] ?? 3) . ",\n";
            $content .= "    \n    // IP提交邮箱数量限制，默认5个\n";
            $content .= "    'ip_email_limit' => " . (int)($input['ip_email_limit'] ?? 5) . "\n";
            $content .= "];\n";
            
            // 写入文件
            $paramsPath = app()->getRootPath() . 'plugin/Questionnaires/params.php';
            
            if (file_put_contents($paramsPath, $content) === false) {
                return json(['code' => 500, 'msg' => '保存失败：无法写入文件']);
            }
            
            return json(['code' => 200, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '保存参数失败：' . $e->getMessage()]);
        }
    }

    /**
     * 根据数据类型获取对应的SQL类型
     * @param string $dataType
     * @return string
     */
    private function getSqlType($dataType)
    {
        switch ($dataType) {
            case 'number':
                return 'DECIMAL(10,2)';
            case 'boolean':
                return 'TINYINT(1)';
            case 'date':
                return 'DATETIME';
            case 'object':
            case 'array':
                return 'TEXT';
            case 'string':
            default:
                return 'VARCHAR(255)';
        }
    }

    /**
     * 获取默认值的SQL表示
     * @param string $dataType
     * @param mixed $value
     * @return string|null
     */
    private function getDefaultValue($dataType, $value)
    {
        if ($value === null || $value === '') {
            return null;
        }

        switch ($dataType) {
            case 'number':
                return is_numeric($value) ? $value : 0;
            case 'boolean':
                return $value ? 1 : 0;
            case 'date':
                return "'0000-00-00 00:00:00'";
            case 'string':
                return "'" . addslashes($value) . "'";
            case 'object':
            case 'array':
                return "'" . addslashes(json_encode($value)) . "'";
            default:
                return null;
        }
    }

    // 处理违规商品举报提交
    public function submitReport()
    {
        try {
            // 获取参数
            $requestData = request()->getInput();
            $data = json_decode($requestData, true);
            
            if (empty($data)) {
                return json(['code' => 400, 'msg' => '提交数据不能为空']);
            }
            
            // 检查是否是更新操作
            $reportId = isset($data['reportId']) ? (int)$data['reportId'] : 0;
            
            // 如果有reportId，则使用updateReport方法
            if ($reportId > 0) {
                return $this->updateReport($reportId, $data);
            }
            
            // 获取问卷类型配置，确保字段ID正确
            $params = $this->getParamsData();
            $questionTypes = $params['question_types'] ?? [];
            
            // 创建字段映射关系
            $fieldMapping = [];
            foreach ($questionTypes as $type) {
                if (isset($type['id'])) {
                    $fieldId = $type['id'];
                    // 将前端字段ID (如shopLink) 映射到数据库字段名 (如shop_link)
                    $dbFieldName = $this->convertToDatabaseFieldName($fieldId);
                    $fieldMapping[$fieldId] = $dbFieldName;
                }
            }
            
            // 准备插入数据
            $insertData = [
                'status' => 0,  // 0: 未处理, 1: 已处理
                'create_time' => time(),
                'update_time' => time(),
                'ip' => request()->ip()
            ];
            
            // 验证邮箱格式
            if (isset($data['email']) && !empty($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                return json(['code' => 400, 'msg' => '邮箱格式不正确']);
            }
            
            // 获取配置参数
            $params = $this->getParamsData();
            $emailLimit = isset($params['email_submit_limit']) ? intval($params['email_submit_limit']) : 3; // 默认每个邮箱最多提交3次
            $ipEmailLimit = isset($params['ip_email_limit']) ? intval($params['ip_email_limit']) : 5; // 默认每个IP最多提交5个邮箱
            
            // 检查邮箱提交次数限制
            if (isset($data['email']) && !empty($data['email'])) {
                $email = $data['email'];
                $emailCount = Db::name('plugin_reports')
                    ->where('email', $email)
                    ->count();
                
                if ($emailCount >= $emailLimit) {
                    return json(['code' => 403, 'msg' => "该邮箱已达到最大提交次数({$emailLimit}次)，请使用其他邮箱"]);
                }
            }
            
            // 检查IP提交邮箱数量限制
            $ip = request()->ip();
            $ipEmailsCount = Db::name('plugin_reports')
                ->where('ip', $ip)
                ->distinct(true)
                ->field('email')
                ->count();

            if ($ipEmailsCount >= $ipEmailLimit) {
                return json(['code' => 403, 'msg' => "当前IP已达到最大提交邮箱数量({$ipEmailLimit}个)，请稍后再试"]);
            }
            
            // 将数据从前端字段映射到数据库字段
            foreach ($data as $fieldId => $value) {
                // 查找匹配的字段类型
                $fieldType = null;
                foreach ($questionTypes as $type) {
                    if ($type['id'] === $fieldId) {
                        $fieldType = $type;
                        break;
                    }
                }
                
                // 根据映射关系获取对应的数据库字段名
                $dbField = isset($fieldMapping[$fieldId]) ? $fieldMapping[$fieldId] : $fieldId;
                
                // 根据字段类型处理数据
                if ($fieldType && isset($fieldType['component'])) {
                    switch ($fieldType['component']) {
                        case 'upload':
                            // 处理上传数据为JSON格式
                            $insertData[$dbField] = is_array($value) ? json_encode($value) : $value;
                            break;
                        case 'checkbox':
                        case 'select':
                            // 处理多选数据为JSON格式
                            $insertData[$dbField] = is_array($value) ? json_encode($value) : $value;
                            break;
                        default:
                            $insertData[$dbField] = $value;
                    }
                } else {
                    // 如果找不到匹配的字段类型，直接使用映射后的字段名
                    $insertData[$dbField] = $value;
                }
            }
            
            // 确保表中有这些字段
            $tableColumns = Db::query("SHOW COLUMNS FROM `" . Db::name('plugin_reports')->getTable() . "`");
            $existingColumns = array_column($tableColumns, 'Field');
            
            // 过滤掉不存在的字段
            foreach ($insertData as $field => $value) {
                if (!in_array($field, $existingColumns)) {
                    unset($insertData[$field]);
                }
            }
            
            // 保存到数据库
            $reportId = Db::name('plugin_reports')->insertGetId($insertData);
            return json(['code' => 200, 'msg' => '举报提交成功', 'data' => ['id' => $reportId]]);
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '提交失败：' . $e->getMessage()]);
        }
    }

    /**
     * 更新已有举报信息
     */
    public function updateReport($reportId, $data)
    {
        try {
            // 检查举报ID是否存在
            $report = Db::name('plugin_reports')->where('id', $reportId)->find();
            if (empty($report)) {
                return json(['code' => 404, 'msg' => '未找到原举报记录']);
            }
            
            // 获取问卷类型配置，确保字段ID正确
            $params = $this->getParamsData();
            $questionTypes = $params['question_types'] ?? [];
            
            // 创建字段映射关系
            $fieldMapping = [];
            foreach ($questionTypes as $type) {
                if (isset($type['id'])) {
                    $fieldId = $type['id'];
                    // 将前端字段ID (如shopLink) 映射到数据库字段名 (如shop_link)
                    $dbFieldName = $this->convertToDatabaseFieldName($fieldId);
                    $fieldMapping[$fieldId] = $dbFieldName;
                }
            }
            
            // 准备更新数据
            $updateData = [
                'status' => 0,  // 重置为未处理状态，需要重新审核
                'update_time' => time(),
                'reject_reason' => '', // 清空驳回原因
            ];
            
            // 验证邮箱格式
            if (isset($data['email']) && !empty($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                return json(['code' => 400, 'msg' => '邮箱格式不正确']);
            }
            
            // 将数据从前端字段映射到数据库字段
            foreach ($data as $fieldId => $value) {
                // 跳过reportId字段
                if ($fieldId === 'reportId') {
                    continue;
                }
                
                // 查找匹配的字段类型
                $fieldType = null;
                foreach ($questionTypes as $type) {
                    if ($type['id'] === $fieldId) {
                        $fieldType = $type;
                        break;
                    }
                }
                
                // 根据映射关系获取对应的数据库字段名
                $dbField = isset($fieldMapping[$fieldId]) ? $fieldMapping[$fieldId] : $fieldId;
                
                // 根据字段类型处理数据
                if ($fieldType && isset($fieldType['component'])) {
                    switch ($fieldType['component']) {
                        case 'upload':
                            // 处理上传数据为JSON格式
                            $updateData[$dbField] = is_array($value) ? json_encode($value) : $value;
                            break;
                        case 'checkbox':
                        case 'select':
                            // 处理多选数据为JSON格式
                            $updateData[$dbField] = is_array($value) ? json_encode($value) : $value;
                            break;
                        default:
                            $updateData[$dbField] = $value;
                    }
                } else {
                    // 如果找不到匹配的字段类型，直接使用映射后的字段名
                    $updateData[$dbField] = $value;
                }
            }
            
            // 确保表中有这些字段
            $tableColumns = Db::query("SHOW COLUMNS FROM `" . Db::name('plugin_reports')->getTable() . "`");
            $existingColumns = array_column($tableColumns, 'Field');
            
            // 过滤掉不存在的字段
            foreach ($updateData as $field => $value) {
                if (!in_array($field, $existingColumns)) {
                    unset($updateData[$field]);
                }
            }
            
            // 更新数据库记录
            Db::name('plugin_reports')->where('id', $reportId)->update($updateData);
            
            return json([
                'code' => 200, 
                'msg' => '举报更新成功，已重新提交审核', 
                'data' => ['id' => $reportId]
            ]);
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '更新失败：' . $e->getMessage()]);
        }
    }

    /**
     * 将前端字段ID转换为数据库字段名
     * 例如: shopLink -> shop_link
     */
    private function convertToDatabaseFieldName($fieldId)
    {
        // 驼峰转下划线
        $dbField = preg_replace('/([a-z])([A-Z])/', '$1_$2', $fieldId);
        return strtolower($dbField);
    }

    // 获取举报列表
    public function getReportList()
    {
        try {
            $page = $this->request->get('page/d', 1);
            $limit = $this->request->get('limit/d', 10);
            $keyword = $this->request->get('keyword/s', '');
            $status = $this->request->get('status/d', -1);

            // 获取问卷参数配置，包括问题类型
            $params = $this->getParamsData();
            $questionTypes = $params['question_types'] ?? [];

            $query = Db::name('plugin_reports');
            
            // 获取所有可搜索字段
            $searchableFields = ['shop_link', 'shop_name', 'product_link', 'violation_type', 'email', 'remark']; 
            
            // 构建搜索条件
            if (!empty($keyword)) {
                $whereSql = [];
                foreach ($searchableFields as $field) {
                    // 确保字段存在于表结构中再进行搜索
                    $tableStructure = Db::query("SHOW COLUMNS FROM `" . Db::name('plugin_reports')->getTable() . "` LIKE '{$field}'");
                    if (!empty($tableStructure)) {
                        $whereSql[] = "`{$field}` LIKE :keyword";
                    }
                }
                if (!empty($whereSql)) {
                    $whereStr = implode(' OR ', $whereSql);
                    $query->whereRaw("({$whereStr})", ['keyword' => "%{$keyword}%"]);
                }
            }
            
            // 状态筛选
            if ($status >= 0) {
                $query->where('status', $status);
            }

            // 获取总数
            $total = $query->count();
            
            // 获取列表数据
            $list = $query->order('id', 'desc')
                ->page($page, $limit)
                ->select()
                ->toArray();

            // 获取表字段结构
            $tableColumns = Db::query("SHOW COLUMNS FROM `" . Db::name('plugin_reports')->getTable() . "`");
            $existingColumns = array_column($tableColumns, 'Field');

            // 处理数据
            foreach ($list as &$item) {
                $item['create_time_text'] = date('Y-m-d H:i:s', $item['create_time']);
                $item['update_time_text'] = $item['update_time'] ? date('Y-m-d H:i:s', $item['update_time']) : '-';
                
                // 更新状态文本
                switch ($item['status']) {
                    case 0:
                        $item['status_text'] = '未处理';
                        break;
                    case 1:
                        $item['status_text'] = '已处理';
                        break;
                    case 2:
                        $item['status_text'] = '已驳回';
                        break;
                    default:
                        $item['status_text'] = '未知状态';
                }
                
                // 确保proofs字段存在且为数组
                if (isset($item['proofs']) && !empty($item['proofs'])) {
                    if (is_string($item['proofs'])) {
                        try {
                            $item['proofs'] = json_decode($item['proofs'], true);
                        } catch (\Exception $e) {
                            $item['proofs'] = [];
                        }
                    }
                    
                    if (!is_array($item['proofs'])) {
                        $item['proofs'] = [];
                    }
                } else {
                    $item['proofs'] = [];
                }
                
                // 处理违规类型字段
                if (isset($item['violation_type']) && !empty($item['violation_type'])) {
                    if (is_string($item['violation_type']) && $this->isJson($item['violation_type'])) {
                        try {
                            $item['violation_type'] = json_decode($item['violation_type'], true);
                            // 如果是数组，转换为字符串显示
                            if (is_array($item['violation_type'])) {
                                $item['violation_type'] = implode(',', $item['violation_type']);
                            }
                        } catch (\Exception $e) {
                            // 如果解析失败，保持原样
                        }
                    }
                }
                
                // 确保必要字段存在
                $requiredFields = ['shop_link', 'shop_name', 'product_link', 'violation_type', 'email', 'remark', 'reject_reason'];
                foreach ($requiredFields as $field) {
                    if (!isset($item[$field])) {
                        $item[$field] = '';
                    }
                }
                
                // 处理动态字段的显示
                foreach ($questionTypes as $type) {
                    if (isset($type['id']) && in_array($type['id'], $existingColumns) && isset($item[$type['id']])) {
                        // 根据字段类型处理显示
                        switch ($type['component'] ?? '') {
                            case 'checkbox':
                            case 'select':
                            case 'radio':
                                if (is_string($item[$type['id']]) && $this->isJson($item[$type['id']])) {
                                    $decoded = json_decode($item[$type['id']], true);
                                    if (is_array($decoded)) {
                                        $item[$type['id']] = implode(',', $decoded);
                                    } else {
                                        $item[$type['id']] = $decoded;
                                    }
                                }
                                break;
                            case 'date':
                                $item[$type['id']] = !empty($item[$type['id']]) ? 
                                    date('Y-m-d H:i:s', strtotime($item[$type['id']])) : '';
                                break;
                            case 'boolean':
                                $item[$type['id']] = !empty($item[$type['id']]) ? '是' : '否';
                                break;
                        }
                    }
                }
            }

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => [
                    'total' => $total,
                    'list' => $list
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '获取举报列表失败：' . $e->getMessage()
            ]);
        }
    }

    // 获取举报详情
    public function getReport()
    {
        try {
            $id = $this->request->get('id/d', 0);
            
            if (empty($id)) {
                return json(['code' => 500, 'msg' => '举报ID不能为空']);
            }

            $report = Db::name('plugin_reports')->where('id', $id)->find();
            
            if (empty($report)) {
                return json(['code' => 404, 'msg' => '举报信息不存在']);
            }

            // 处理数据
            $report['create_time_text'] = date('Y-m-d H:i:s', $report['create_time']);
            $report['update_time_text'] = $report['update_time'] ? date('Y-m-d H:i:s', $report['update_time']) : '-';
            $report['status_text'] = $report['status'] == 0 ? '未处理' : '已处理';
            
            // 确保proofs字段是数组格式
            if (isset($report['proofs']) && !empty($report['proofs'])) {
                if (is_string($report['proofs'])) {
                    try {
                        $report['proofs'] = json_decode($report['proofs'], true);
                    } catch (\Exception $e) {
                        $report['proofs'] = [];
                    }
                }
                
                if (!is_array($report['proofs'])) {
                    $report['proofs'] = [];
                }
            } else {
                $report['proofs'] = [];
            }
            
            // 确保必要字段存在
            $requiredFields = ['shop_link', 'shop_name', 'product_link', 'violation_type', 'email', 'remark'];
            foreach ($requiredFields as $field) {
                if (!isset($report[$field])) {
                    $report[$field] = '';
                }
            }

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $report
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '获取举报详情失败：' . $e->getMessage()
            ]);
        }
    }

    // 更新举报状态
    public function updateReportStatus()
    {
        try {
            $id = $this->request->post('id/d', 0);
            $status = $this->request->post('status/d', 0);
            $expireTime = $this->request->post('expireTime/d', 4102415999); // 默认永久
            
            if (empty($id)) {
                return json(['code' => 500, 'msg' => '举报ID不能为空']);
            }

            $report = Db::name('plugin_reports')->where('id', $id)->find();
            
            if (empty($report)) {
                return json(['code' => 404, 'msg' => '举报信息不存在']);
            }

            // 更新状态
            Db::name('plugin_reports')->where('id', $id)->update([
                'status' => $status,
                'update_time' => time()
            ]);

            // 如果举报被标记为已处理，则创建用户风险记录
            if ($status == 1) {
                // 获取商店名称，检查可能的字段名
                $shopName = null;
                $possibleFields = ['shop_name', 'shopname', 'shopName'];
                foreach ($possibleFields as $field) {
                    if (isset($report[$field]) && !empty($report[$field])) {
                        $shopName = $report[$field];
                        break;
                    }
                }
                
                // 如果成功获取到店铺名称
                if (!empty($shopName)) {
                    // 根据店铺名称查找对应的用户信息（主要匹配nickname字段）
                    $user = Db::name('user')->where('nickname', $shopName)->field('id')->find();
                    
                    if ($user) {
                        // 构建风险内容
                        $content = "风控描述：";
                        
                        // 检查违规类型字段
                        $violationType = null;
                        $violationFields = ['violation_type', 'violationtype', 'violationType'];
                        foreach ($violationFields as $field) {
                            if (isset($report[$field]) && !empty($report[$field])) {
                                $violationType = $report[$field];
                                break;
                            }
                        }
                        
                        if (!empty($violationType)) {
                            $content .= "违规类型【{$violationType}】";
                        } else {
                            $content .= "违规类型【交易违规】";
                        }
                        
                        // 检查商品链接字段
                        $productLink = null;
                        $productFields = ['product_link', 'productlink', 'productLink'];
                        foreach ($productFields as $field) {
                            if (isset($report[$field]) && !empty($report[$field])) {
                                $productLink = $report[$field];
                                break;
                            }
                        }
                        
                        if (!empty($productLink)) {
                            $content .= "，用户售卖的商品链接：{$productLink}";
                        }
                        
                        // 检查备注字段
                        if (isset($report['remark']) && !empty($report['remark'])) {
                            $content .= "，举报说明：{$report['remark']}";
                        }

                        // 插入用户风险记录（固定risk_type=2 交易违规）
                        Db::name('user_risk')->insert([
                            'user_id' => $user['id'],
                            'risk_type' => 2,  // 固定为交易违规
                            'user_read' => 0,  // 默认未读
                            'create_time' => time(),
                            'content' => $content,
                            'expire_time'=> $expireTime
                        ]);
                    }
                }
            }

            return json(['code' => 200, 'msg' => '更新成功']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '更新状态失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取问卷参数配置
     * @return array 参数配置
     */
    private function getParamsData()
    {
        // 检查文件是否存在
        $paramsFile = app()->getRootPath() . 'plugin/Questionnaires/params.php';
        if (!file_exists($paramsFile)) {
            // 如果文件不存在，返回默认配置
            return [
                'questionnaire_description' => '',
                'question_types' => [
                    [
                        'id' => 'shopLink',
                        'name' => '违规店铺链接',
                        'component' => 'input',
                        'required' => true,
                        'placeholder' => '请输入违规店铺链接',
                        'icon' => 'Link'
                    ],
                    [
                        'id' => 'shopName',
                        'name' => '违规店铺名称',
                        'component' => 'input',
                        'required' => true,
                        'placeholder' => '请输入违规店铺名称',
                        'icon' => 'Text'
                    ],
                    [
                        'id' => 'productLink',
                        'name' => '违规商品链接',
                        'component' => 'input',
                        'required' => true,
                        'placeholder' => '请输入违规商品链接',
                        'icon' => 'Link'
                    ],
                    [
                        'id' => 'violationType',
                        'name' => '违规类型',
                        'component' => 'radio',
                        'required' => true,
                        'options' => ['著作权侵权', '隐私权侵权', '交易违规', '商品违规'],
                        'icon' => 'Warning'
                    ],
                    [
                        'id' => 'proofs',
                        'name' => '证明材料',
                        'component' => 'upload',
                        'required' => true,
                        'maxFiles' => 5,
                        'fileTypes' => ['image'],
                        'placeholder' => '请上传违规证明材料，最多5张图片',
                        'icon' => 'Upload'
                    ],
                    [
                        'id' => 'email',
                        'name' => '联系邮箱',
                        'component' => 'input',
                        'required' => true,
                        'placeholder' => '请输入您的联系邮箱',
                        'icon' => 'Message'
                    ],
                    [
                        'id' => 'remark',
                        'name' => '备注说明',
                        'component' => 'textarea',
                        'required' => false,
                        'placeholder' => '请输入补充说明（可选）',
                        'icon' => 'Document'
                    ]
                ],
                'field_mappings' => [],
                'upload_api' => '',
                'template_type' => 'default'
            ];
        }
        
        // 读取params.php文件内容
        $params = include $paramsFile;
        
        // 确保field_mappings字段存在
        if (!isset($params['field_mappings'])) {
            $params['field_mappings'] = [];
        }
        
        // 检查并确保必填的基本字段存在
        $requiredFields = ['shopLink', 'shopName', 'productLink', 'violationType', 'proofs', 'email'];
        $existingFields = array_column($params['question_types'] ?? [], 'id');
        
        foreach ($requiredFields as $field) {
            $fieldExists = false;
            foreach ($params['question_types'] as $type) {
                if ($type['id'] === $field) {
                    $fieldExists = true;
                    break;
                }
            }
            
            if (!$fieldExists) {
                // 添加默认字段配置
                switch ($field) {
                    case 'shopLink':
                        $params['question_types'][] = [
                            'id' => 'shopLink',
                            'name' => '违规店铺链接',
                            'component' => 'input',
                            'required' => true,
                            'placeholder' => '请输入违规店铺链接',
                            'icon' => 'Link'
                        ];
                        break;
                    case 'shopName':
                        $params['question_types'][] = [
                            'id' => 'shopName',
                            'name' => '违规店铺名称',
                            'component' => 'input',
                            'required' => true,
                            'placeholder' => '请输入违规店铺名称',
                            'icon' => 'Text'
                        ];
                        break;
                    case 'productLink':
                        $params['question_types'][] = [
                            'id' => 'productLink',
                            'name' => '违规商品链接',
                            'component' => 'input',
                            'required' => true,
                            'placeholder' => '请输入违规商品链接',
                            'icon' => 'Link'
                        ];
                        break;
                    case 'violationType':
                        $params['question_types'][] = [
                            'id' => 'violationType',
                            'name' => '违规类型',
                            'component' => 'radio',
                            'required' => true,
                            'options' => ['著作权侵权', '隐私权侵权', '交易违规', '商品违规'],
                            'icon' => 'Warning'
                        ];
                        break;
                    case 'proofs':
                        $params['question_types'][] = [
                            'id' => 'proofs',
                            'name' => '证明材料',
                            'component' => 'upload',
                            'required' => true,
                            'maxFiles' => 5,
                            'fileTypes' => ['image'],
                            'placeholder' => '请上传违规证明材料，最多5张图片',
                            'icon' => 'Upload'
                        ];
                        break;
                    case 'email':
                        $params['question_types'][] = [
                            'id' => 'email',
                            'name' => '联系邮箱',
                            'component' => 'input',
                            'required' => true,
                            'placeholder' => '请输入您的联系邮箱',
                            'icon' => 'Message'
                        ];
                        break;
                }
            }
        }
        
        // 确保备注字段存在
        $remarkExists = false;
        foreach ($params['question_types'] as $type) {
            if ($type['id'] === 'remark') {
                $remarkExists = true;
                break;
            }
        }
        
        // 如果没有备注字段，添加一个默认的备注字段
        if (!$remarkExists) {
            $params['question_types'][] = [
                'id' => 'remark',
                'name' => '备注说明',
                'component' => 'textarea',
                'required' => false,
                'placeholder' => '请输入补充说明（可选）',
                'icon' => 'Document'
            ];
        }
        
        return $params;
    }

    // 处理文件上传
    public function upload()
    {
        try {
            // 获取上传文件
            $file = $this->request->file('file');
            
            if (empty($file)) {
                return json(['code' => 400, 'msg' => '没有上传文件']);
            }
            
            // 文件大小限制(10MB)
            $maxSize = 10 * 1024 * 1024; 
            if ($file->getSize() > $maxSize) {
                return json(['code' => 400, 'msg' => '文件大小不能超过10MB']);
            }
            
            // 限制文件类型
            $allowMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp', 'application/pdf'];
            if (!in_array($file->getMime(), $allowMimeTypes)) {
                return json(['code' => 400, 'msg' => '只允许上传图片和PDF文件']);
            }
            
            // 使用系统上传目录
            $savePath = app()->getRootPath() . 'public/uploads/reports/';
            
            // 确保目录存在
            if (!is_dir($savePath)) {
                mkdir($savePath, 0755, true);
            }
            
            // 保存文件
            $info = $file->move($savePath);
            
            if ($info) {
                $saveName = $info->getSaveName();
                $url = '/uploads/reports/' . str_replace('\\', '/', $saveName);
                
                return json([
                    'code' => 200, 
                    'msg' => '上传成功', 
                    'data' => [
                        'url' => $url,
                        'name' => $info->getFilename()
                    ]
                ]);
            } else {
                return json(['code' => 500, 'msg' => '上传失败：' . $file->getError()]);
            }
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '上传失败：' . $e->getMessage()]);
        }
    }

    // 在 Api.php 中添加一个方法来删除这些字段
    public function removeDefaultFields()
    {
        try {
            $tableName = Db::name('plugin_reports')->getTable();
            $fieldsToRemove = ['shop_link', 'shop_name', 'product_link', 'violation_type'];
            
            foreach ($fieldsToRemove as $fieldName) {
                // 检查字段是否存在
                $existingColumns = Db::query("SHOW COLUMNS FROM `{$tableName}` LIKE '{$fieldName}'");
                if (!empty($existingColumns)) {
                    // 删除字段
                    $sql = "ALTER TABLE `{$tableName}` DROP COLUMN `{$fieldName}`";
                    Db::execute($sql);
                }
            }
            
            // 同时需要修改 params.php 中的配置
            $paramsFile = app()->getRootPath() . 'plugin/Questionnaires/params.php';
            if (file_exists($paramsFile)) {
                $params = include $paramsFile;
                
                // 过滤掉这些默认字段
                $params['question_types'] = array_filter($params['question_types'], function($type) {
                    return !in_array($type['id'], ['shopLink', 'shopName', 'productLink', 'violationType']);
                });
                
                // 重新写入配置文件
                $content = "<?php\n\nreturn " . var_export($params, true) . ";\n";
                file_put_contents($paramsFile, $content);
            }
            
            return json(['code' => 200, 'msg' => '默认字段删除成功']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '删除字段失败：' . $e->getMessage()]);
        }
    }

    /**
     * 切换导航菜单状态
     */
    public function toggleNav()
    {
        try {
            // 检查是否已存在该菜单
            $exists = Db::name('nav')->where('href', '/plugin/Questionnaires/api/buyers')->find();
            
            if ($exists) {
                // 如果存在则删除
                Db::name('nav')->where('href', '/plugin/Questionnaires/api/buyers')->delete();
                return json(['code' => 1, 'msg' => '已从导航栏移除']);
            } else {
                // 如果不存在则添加
                $maxId = Db::name('nav')->max('id') + 1;
                
                // 插入导航菜单
                Db::name('nav')->insert([
                    'id' => $maxId,
                    'pid' => 0,
                    'type' => 'C',
                    'name' => '违规举报',
                    'href' => '/plugin/Questionnaires/api/buyers',
                    'sort' => 0,
                    'target' => 0,
                    'status' => 1,
                    'create_time' => time(),
                    'update_time' => time()
                ]);
                return json(['code' => 1, 'msg' => '已添加到导航栏']);
            }
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '操作失败：' . $e->getMessage()]);
        }
    }

    /**
     * 检查导航菜单状态
     */
    public function checkNav()
    {
        $exists = Db::name('nav')->where('href', '/plugin/Questionnaires/api/buyers')->find();
        return json(['exists' => !empty($exists)]);
    }

    /**
     * 删除举报
     */
    public function deleteReport()
    {
        try {
            $id = $this->request->post('id/d', 0);
            
            if (empty($id)) {
                return json(['code' => 500, 'msg' => '举报ID不能为空']);
            }

            $report = Db::name('plugin_reports')->where('id', $id)->find();
            
            if (empty($report)) {
                return json(['code' => 404, 'msg' => '举报信息不存在']);
            }

            // 删除举报
            Db::name('plugin_reports')->where('id', $id)->delete();

            return json(['code' => 200, 'msg' => '删除成功']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }

    /**
     * 驳回举报
     */
    public function rejectReport()
    {
        try {
            $id = $this->request->post('id/d', 0);
            $reason = $this->request->post('reason/s', '');
            
            if (empty($id)) {
                return json(['code' => 500, 'msg' => '举报ID不能为空']);
            }

            if (empty($reason)) {
                return json(['code' => 500, 'msg' => '请填写驳回原因']);
            }

            // 检查字段是否存在，如果不存在则创建
            $tableName = Db::name('plugin_reports')->getTable();
            $existingColumns = Db::query("SHOW COLUMNS FROM `{$tableName}` LIKE 'reject_reason'");
            if (empty($existingColumns)) {
                // 添加驳回原因字段
                $sql = "ALTER TABLE `{$tableName}` ADD COLUMN `reject_reason` text COMMENT '驳回原因'";
                Db::execute($sql);
            }

            $report = Db::name('plugin_reports')->where('id', $id)->find();
            
            if (empty($report)) {
                return json(['code' => 404, 'msg' => '举报信息不存在']);
            }

            // 更新状态为已驳回（状态值为2）
            Db::name('plugin_reports')->where('id', $id)->update([
                'status' => 2,
                'reject_reason' => $reason,
                'update_time' => time()
            ]);

            // 发送邮件通知
            if (!empty($report['email'])) {
                $service = new \app\common\service\EmailService();
                $message = "您好，\n\n您提交的违规商品举报已被驳回。\n\n驳回原因：{$reason}\n\n如有疑问，请重新提交举报。";
                
                $res = $service->subject('违规举报驳回通知')
                    ->message($message)
                    ->to($report['email'])
                    ->send();

                if (!$res) {
                    return json(['code' => 200, 'msg' => '驳回成功，但邮件发送失败：' . $service->getError()]);
                }
            }

            return json(['code' => 200, 'msg' => '驳回成功']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '驳回失败：' . $e->getMessage()]);
        }
    }

    /**
     * 通过邮箱查询举报记录
     */
    public function queryReportsByEmail()
    {
        try {
            $email = $this->request->get('email/s', '');
            $captcha = $this->request->get('captcha/s', '');
            
            if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                return json(['code' => 400, 'msg' => '请输入有效的邮箱地址']);
            }
            
            // 验证码校验
            if (!captcha_check($captcha)) {
                return json(['code' => 400, 'msg' => '验证码错误']);
            }

            // 检查reject_reason字段是否存在
            $tableName = Db::name('plugin_reports')->getTable();
            $existingColumns = Db::query("SHOW COLUMNS FROM `{$tableName}` LIKE 'reject_reason'");
            $hasRejectReason = !empty($existingColumns);
            
            // 构建查询字段
            $fields = 'id, shop_link, shop_name, product_link, violation_type, status, create_time, update_time';
            if ($hasRejectReason) {
                $fields .= ', reject_reason';
            }

            $reports = Db::name('plugin_reports')
                ->where('email', $email)
                ->field($fields)
                ->order('create_time', 'desc')
                ->select()
                ->toArray();

            // 处理数据
            foreach ($reports as &$report) {
                $report['create_time_text'] = date('Y-m-d H:i:s', $report['create_time']);
                $report['update_time_text'] = $report['update_time'] ? date('Y-m-d H:i:s', $report['update_time']) : '-';
                
                // 更新状态文本
                switch ($report['status']) {
                    case 0:
                        $report['status_text'] = '未处理';
                        break;
                    case 1:
                        $report['status_text'] = '已处理';
                        break;
                    case 2:
                        $report['status_text'] = '已驳回';
                        break;
                    default:
                        $report['status_text'] = '未知状态';
                }
                
                // 确保reject_reason字段存在
                if (!$hasRejectReason && $report['status'] == 2) {
                    $report['reject_reason'] = '驳回原因未记录';
                }
            }

            return json([
                'code' => 200,
                'msg' => '查询成功',
                'data' => $reports
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '查询失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 生成验证码
     */
    public function captcha()
    {
        return captcha();
    }

}
