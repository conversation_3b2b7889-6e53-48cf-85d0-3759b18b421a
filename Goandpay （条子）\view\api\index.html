<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>去支付弹窗提示配置</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background-color: #f5f7fa;
            color: #333;
            min-height: 100vh;
        }
        
        .page-container {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }
        
        .page-header {
            background: linear-gradient(135deg, #409eff, #64b5f6);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            align-items: center;
        }
        
        .header-logo {
            width: 32px;
            height: 32px;
            margin-right: 12px;
        }
        
        .header-title {
            font-size: 20px;
            font-weight: 500;
            margin: 0;
        }
        
        .page-body {
            flex: 1;
            padding: 30px 20px;
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
            box-sizing: border-box;
        }
        
        .panel-container {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .form-panel {
            flex: 3;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }
        
        .preview-panel {
            flex: 2;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
            padding: 0;
            position: sticky;
            top: 20px;
            height: fit-content;
            max-height: calc(100vh - 40px);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        
        .preview-header {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            font-weight: 500;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .preview-body {
            padding: 20px;
            overflow-y: auto;
            flex: 1;
        }
        
        .mobile-preview {
            width: 320px;
            height: 580px;
            background-color: #f7f9fc;
            border: 10px solid #333;
            border-radius: 30px;
            margin: 0 auto;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        
        .mobile-status-bar {
            height: 30px;
            background-color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }
        
        .mobile-notch {
            width: 150px;
            height: 20px;
            background-color: #333;
            border-radius: 0 0 15px 15px;
            position: absolute;
            top: 0;
        }
        
        .mobile-screen {
            height: calc(100% - 30px);
            overflow-y: auto;
            background-color: #fff;
            position: relative;
        }
        
        .mobile-content {
            padding: 15px;
            min-height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .mobile-payment-popup {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }
        
        .mobile-payment-container {
            width: 85%;
            max-width: 280px;
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }
        
        .mobile-payment-header {
            padding: 15px;
            position: relative;
        }
        
        .mobile-payment-title {
            font-size: 16px;
            font-weight: 500;
            margin: 0;
        }
        
        .mobile-payment-line {
            height: 3px;
            width: 40px;
            background: linear-gradient(90deg, #409eff, #64b5f6);
            border-radius: 3px;
            margin-top: 8px;
        }
        
        .mobile-payment-content {
            padding: 10px 15px;
            max-height: 200px;
            overflow-y: auto;
            border-top: 1px solid #f5f5f5;
            border-bottom: 1px solid #f5f5f5;
            font-size: 13px;
        }
        
        .mobile-payment-footer {
            padding: 15px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .mobile-payment-agree {
            display: flex;
            align-items: center;
            font-size: 12px;
        }
        
        .mobile-payment-btn {
            background: linear-gradient(135deg, #409eff, #64b5f6);
            color: white;
            border: none;
            padding: 8px 0;
            border-radius: 5px;
            font-size: 14px;
            text-align: center;
            cursor: pointer;
        }
        
        .form-section {
            margin-bottom: 20px;
        }
        
        .panel-title {
            font-size: 16px;
            font-weight: 500;
            margin: 0 0 20px 0;
            padding-bottom: 10px;
            border-bottom: 1px solid #f0f0f0;
            color: #333;
        }
        
        .el-card {
            box-shadow: none !important;
            border: none !important;
        }
        
        .el-form-item {
            margin-bottom: 22px;
        }
        
        .el-form-item__label {
            font-weight: 500;
            color: #333;
        }
        
        .feature-icon {
            font-size: 18px;
            color: #409eff;
            margin-right: 5px;
            vertical-align: middle;
        }
        
        .el-form-item-msg {
            color: #909399 !important;
            font-size: 12px !important;
            margin-top: 5px !important;
            line-height: 1.4;
        }
        
        .action-bar {
            margin-top: 30px;
            display: flex;
            justify-content: flex-end;
            border-top: 1px solid #f0f0f0;
            padding-top: 20px;
        }
        
        .action-bar .el-button {
            padding: 10px 25px;
            font-size: 14px;
        }
        
        .editor-container {
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .w-e-toolbar {
            border-bottom: 1px solid #dcdfe6 !important;
            background-color: #f5f7fa !important;
        }
        
        .w-e-text-container {
            height: 350px !important;
            background-color: #fff;
        }
        
        .w-e-text-container [contenteditable=true] {
            color: #333;
            line-height: 1.6;
            font-size: 14px;
        }
        
        .template-bar {
            margin-bottom: 15px;
        }
        
        .template-title {
            font-size: 14px;
            margin-bottom: 10px;
            font-weight: 500;
        }
        
        /* 响应式样式调整 */
        @media screen and (max-width: 768px) {
            .page-body {
                padding: 20px 10px;
            }
            
            .panel-container {
                flex-direction: column;
            }
            
            .preview-panel {
                position: static;
                max-height: none;
            }
            
            .mobile-preview {
                width: 280px;
                height: 500px;
            }
            
            .form-panel {
                padding: 0;
            }
            
            .el-form-item {
                margin-bottom: 20px;
            }
            
            .el-form-item__label {
                width: auto !important;
                text-align: left;
                float: none;
                display: block;
                margin-bottom: 8px;
                padding: 0;
            }
            
            .el-form-item__content {
                margin-left: 0 !important;
            }
            
            .el-radio-group {
                display: flex;
                flex-direction: column;
                gap: 10px;
            }
            
            .el-radio {
                margin-right: 0;
                margin-bottom: 5px;
            }
            
            .w-e-toolbar {
                flex-wrap: wrap;
            }
            
            .w-e-text-container {
                height: 300px !important;
            }
            
            .editor-container {
                margin: 0;
            }
            
            .action-bar {
                flex-direction: column;
            }
            
            .action-bar .el-button {
                width: 100%;
                margin: 5px 0 !important;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="page-container">
            <header class="page-header">
                <div class="header-content">
                    <svg class="header-logo" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="32" height="32"><path fill="white" d="M848,160H480c-26.51,0-48,21.49-48,48V352H208c-26.51,0-48,21.49-48,48v352c0,26.51,21.49,48,48,48h368c26.51,0,48-21.49,48-48V600h224c26.51,0,48-21.49,48-48V208C896,181.49,874.51,160,848,160Z"/><path fill="#e6f1ff" d="M576,352V208c0-26.51-21.49-48-48-48H176c-26.51,0-48,21.49-48,48V560H352V400c0-26.51,21.49-48,48-48H576Z"/><circle fill="white" cx="392" cy="600" r="100"/><path fill="#409eff" d="M392, 546c0, 0 -20, 0 -20, 20v8h-8c-11, 0 -20, 9 -20, 20c0, 11 9, 20 20, 20h8v8c0, 20 20, 20 20, 20s20, 0 20, -20v-8h8c11, 0 20, -9 20, -20c0, -11 -9, -20 -20, -20h-8v-8c0, -20 -20, -20 -20, -20z"/></svg>
                    <h1 class="header-title">支付弹窗提示配置</h1>
                </div>
            </header>
            
            <div class="page-body">
                <div class="panel-container">
                    <div class="form-panel">
                        <el-card shadow="never">
                            <el-form :model="form" label-width="140px">
                                <h3 class="panel-title">基本配置</h3>
                                
                                <el-form-item label="弹窗开关：">
                                    <el-switch v-model="form.status" :active-value="1" :inactive-value="0" />
                                    <div class="el-form-item-msg">
                                        开启后支付过程中将显示弹窗提示，关闭则直接进入支付流程
                                    </div>
                                </el-form-item>

                                <el-form-item label="弹窗标题：">
                                    <el-input v-model="form.noticeTitle" placeholder="请输入弹窗标题" maxlength="20" show-word-limit style="width: 300px"></el-input>
                                    <div class="el-form-item-msg">
                                        自定义弹窗顶部显示的标题文字，最多20个字符
                                    </div>
                                </el-form-item>

                                <el-form-item label="弹出频率：">
                                    <el-radio-group v-model="form.frequency">
                                        <el-radio label="once">仅弹一次</el-radio>
                                        <el-radio label="login">每次访问</el-radio>
                                        <el-radio label="daily">每天一次</el-radio>
                                        <el-radio label="weekly">每周一次</el-radio>
                                    </el-radio-group>
                                    <div class="el-form-item-msg">
                                        控制向同一用户显示弹窗的频率，根据实际需求选择合适的频率
                                    </div>
                                </el-form-item>
                                
                                <h3 class="panel-title">内容设置</h3>
                                
                                <div class="template-bar">
                                    <div class="template-title">快速模板：</div>
                                    <el-button size="small" @click="useTemplate('protocol')">用户协议</el-button>
                                    <el-button size="small" @click="useTemplate('payment')">支付说明</el-button>
                                    <el-button size="small" @click="useTemplate('notice')">订单提醒</el-button>
                                </div>

                                <el-form-item label="提示内容：">
                                    <div class="editor-container">
                                        <div id="editor-toolbar" style="border-bottom: 1px solid #dcdfe6;"></div>
                                        <div id="editor-text-area" style="height: 300px"></div>
                                    </div>
                                    <div class="el-form-item-msg">
                                        支持HTML标签和CSS样式，可以设置文字颜色、大小、添加图片等
                                    </div>
                                </el-form-item>
                                
                                <h3 class="panel-title">高级功能</h3>
                                
                                <el-form-item label="语音朗读：">
                                    <el-switch v-model="form.readEnabled" :active-value="1" :inactive-value="0" />
                                    <div class="el-form-item-msg">
                                        开启后，弹窗内容将会被自动朗读给用户，增强内容传达效果
                                    </div>
                                </el-form-item>
                                
                                <el-form-item label="确认按钮倒计时：">
                                    <el-input-number v-model="form.closeDelay" :min="0" :max="60" :precision="0"></el-input-number>
                                    <span class="el-input-group__append">秒</span>
                                    <div class="el-form-item-msg">
                                        设置后，确认按钮将在倒计时结束后才可点击，设为0表示无需等待
                                    </div>
                                </el-form-item>
                                
                                <el-form-item label="联系方式检查：">
                                    <el-switch v-model="form.contactCheck" :active-value="1" :inactive-value="0" />
                                    <div class="el-form-item-msg">
                                        开启后，用户在支付前必须填写联系方式，否则将提示填写
                                    </div>
                                </el-form-item>

                                <div class="action-bar">
                                    <el-button type="primary" :loading="loading" @click="save">保存设置</el-button>
                                    <el-button @click="resetForm">重置</el-button>
                                </div>
                            </el-form>
                        </el-card>
                    </div>
                    
                    <div class="preview-panel">
                        <div class="preview-header">
                            <span>效果预览</span>
                            <el-button size="small" type="text" @click="refreshPreview">刷新预览</el-button>
                        </div>
                        <div class="preview-body">
                            <div class="mobile-preview">
                                <div class="mobile-status-bar">
                                    <div class="mobile-notch"></div>
                                </div>
                                <div class="mobile-screen">
                                    <div class="mobile-content">
                                        <div style="height: 50px; background-color: #f7f9fc; border-radius: 4px;"></div>
                                        <div style="height: 30px; margin-top: 10px; width: 70%; background-color: #f7f9fc; border-radius: 4px;"></div>
                                        <div style="height: 80px; margin-top: 20px; background-color: #f7f9fc; border-radius: 4px;"></div>
                                        <div style="display: flex; justify-content: space-between; margin-top: 15px;">
                                            <div style="height: 30px; width: 45%; background-color: #f7f9fc; border-radius: 4px;"></div>
                                            <div style="height: 30px; width: 45%; background-color: #409eff; border-radius: 4px;"></div>
                                        </div>
                                    </div>
                                    
                                    <div class="mobile-payment-popup" v-if="form.status === 1">
                                        <div class="mobile-payment-container">
                                            <div class="mobile-payment-header">
                                                <h3 class="mobile-payment-title">{{ form.noticeTitle }}</h3>
                                                <div class="mobile-payment-line"></div>
                                            </div>
                                            <div class="mobile-payment-content" v-html="previewContent"></div>
                                            <div class="mobile-payment-footer">
                                                <div class="mobile-payment-agree">
                                                    <input type="checkbox" style="margin-right: 5px;">
                                                    <span>我已阅读并同意上述内容</span>
                                                </div>
                                                <button class="mobile-payment-btn">同意支付</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/dist/index.min.js"></script>
    <link href="/static/others/dist/css/style.css" rel="stylesheet">

    <script>
        const { createApp, ref, reactive, onMounted, computed } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        createApp({
            setup() {
                const loading = ref(false);
                const form = reactive({
                    status: 1,
                    frequency: 'once',
                    readEnabled: 1,
                    closeDelay: 0,
                    noticeTitle: '支付前提示',
                    contactCheck: 1
                });
                
                // 预览内容
                const previewContent = ref('预览内容将显示在这里');
                
                let editor = null;

                onMounted(() => {
                    if (window.wangEditor) {
                        const E = window.wangEditor;
                        editor = E.createEditor({
                            selector: '#editor-text-area',
                            html: '',
                            config: {
                                placeholder: '请输入支付提示内容...',
                                html: true,
                                MENU_CONF: {
                                    uploadImage: {
                                        server: '/merchantApi/Upload/file',
                                        fieldName: 'file',
                                        maxFileSize: 2 * 1024 * 1024,
                                        maxNumberOfFiles: 10,
                                        allowedFileTypes: ['image/*'],
                                        meta: {},
                                        headers: {},
                                        onBeforeUpload(file) {
                                            return file // 返回 false 则终止上传
                                        },
                                        onProgress(progress) {
                                            
                                        },
                                        onSuccess(file, res) {
                                            
                                        },
                                        onFailed(file, res) {
                                            ElMessage.error('图片上传失败：' + (res?.msg || '未知错误'))
                                        },
                                        onError(file, err, res) {
                                            ElMessage.error('图片上传失败：' + (res?.msg || err.message || '未知错误'))
                                        },
                                        customInsert(res, insertFn) {
                                            if (res.code === 1) {
                                                insertFn(res.data.url, res.data.original_name || '', '')
                                            } else {
                                                ElMessage.error(res.msg || '上传失败')
                                            }
                                        }
                                    }
                                }
                            }
                        });

                        const toolbar = E.createToolbar({
                            editor,
                            selector: '#editor-toolbar',
                            config: {
                                excludeKeys: [],
                                toolbarKeys: [
                                    'headerSelect',
                                    'blockquote',
                                    '|',
                                    'bold',
                                    'underline',
                                    'italic',
                                    {
                                        key: 'group-more-style',
                                        title: '更多',
                                        menuKeys: ['through', 'code', 'sup', 'sub', 'clearStyle'],
                                    },
                                    '|',
                                    'color',
                                    'bgColor',
                                    '|',
                                    'fontSize',
                                    'fontFamily',
                                    'lineHeight',
                                    '|',
                                    'bulletedList',
                                    'numberedList',
                                    'todo',
                                    {
                                        key: 'group-justify',
                                        title: '对齐',
                                        menuKeys: ['justifyLeft', 'justifyCenter', 'justifyRight', 'justifyJustify'],
                                    },
                                    '|',
                                    'insertImage',
                                    '|',
                                    'insertTable',
                                    'undo',
                                    'redo',
                                    '|',
                                    'fullScreen'
                                ]
                            }
                        });
                        
                        // 监听编辑器内容变化，更新预览
                        editor.on('change', () => {
                            refreshPreview();
                        });
                    } else {
                        ElMessage.error('富文本编辑器加载失败，请刷新页面重试');
                    }

                    fetchData();
                });

                // 刷新预览
                const refreshPreview = () => {
                    if (editor) {
                        previewContent.value = editor.getHtml() || '请在编辑器中填写内容';
                    }
                };
                
                // 使用模板
                const useTemplate = (type) => {
                    if (!editor) return;
                    
                    let templateContent = '';
                    
                    switch(type) {
                        case 'protocol':
                            templateContent = '<h3 style="text-align: center; margin-bottom: 10px;">用户购买协议</h3><p>尊敬的用户，感谢您使用我们的产品和服务！</p><p>请您务必认真阅读以下条款，并同意本协议后再进行支付：</p><ol><li>本商品/服务一经购买，非质量问题不予退款</li><li>使用过程中遇到问题，可随时联系客服获取帮助</li><li>最终解释权归本公司所有</li></ol>';
                            break;
                        case 'payment':
                            templateContent = '<h3 style="text-align: center; color: #409eff; margin-bottom: 10px;">支付说明</h3><p>请在完成支付前确认您的订单信息：</p><p>1. 支付金额以订单显示为准</p><p>2. 支付成功后系统将自动为您发货/开通服务</p><p>3. 如有疑问请添加客服微信咨询</p><p style="color:#f56c6c; margin-top: 10px;">特别提醒：付款前请确认您的联系方式正确，以便我们及时与您取得联系</p>';
                            break;
                        case 'notice':
                            templateContent = '<div style="background-color: #f0f9eb; border-radius: 4px; padding: 10px; margin-bottom: 10px;"><p style="color: #67c23a; font-weight: bold;">重要提醒</p><p>您即将完成订单支付，请注意以下事项：</p></div><ul><li>订单支付后将立即处理，请确保信息准确</li><li>如需修改收货地址，请支付前联系客服</li><li>发货后可在订单页面查看物流信息</li></ul><p style="text-align: center; margin-top: 10px;">感谢您的购买！</p>';
                            break;
                    }
                    
                    if (templateContent) {
                        ElMessageBox.confirm('使用模板将覆盖当前内容，是否继续？', '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }).then(() => {
                            editor.setHtml(templateContent);
                            refreshPreview();
                            ElMessage.success('模板应用成功');
                        }).catch(() => {});
                    }
                };
                
                // 重置表单
                const resetForm = () => {
                    ElMessageBox.confirm('确定要重置所有设置吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        form.status = 1;
                        form.frequency = 'once';
                        form.readEnabled = 1;
                        form.closeDelay = 0;
                        form.noticeTitle = '支付前提示';
                        form.contactCheck = 1;
                        
                        if (editor) {
                            editor.setHtml('');
                        }
                        
                        refreshPreview();
                        ElMessage.success('重置成功');
                    }).catch(() => {});
                };

                // 获取数据
                const fetchData = async () => {
                    try {
                        const res = await axios.post("/plugin/Goandpay/api/fetchData");
                        if (res.data?.code === 200) {
                            const data = res.data.data;
                            form.status = parseInt(data.status);
                            form.frequency = data.frequency;
                            form.readEnabled = parseInt(data.read_enabled);
                            form.closeDelay = parseInt(data.close_delay) || 0;
                            form.noticeTitle = data.notice_title || '支付前提示';
                            form.contactCheck = parseInt(data.contact_check) || 1;
                            
                            if (editor && data.payment_notice) {
                                editor.setHtml(data.payment_notice);
                                refreshPreview();
                            }
                        } else {
                            ElMessage.error(res.data?.msg || '获取数据失败');
                        }
                    } catch (error) {
                        ElMessage.error('获取数据失败');
                    }
                };

                // 保存数据
                const save = async () => {
                    if (!editor) {
                        ElMessage.error('编辑器未初始化');
                        return;
                    }

                    const content = editor.getHtml();
                    if (!content.trim()) {
                        ElMessage.warning('请输入支付提示内容');
                        return;
                    }

                    loading.value = true;
                    try {
                        const formData = new FormData();
                        formData.append('status', form.status);
                        formData.append('frequency', form.frequency);
                        formData.append('read_enabled', form.readEnabled);
                        formData.append('close_delay', form.closeDelay);
                        formData.append('notice_title', form.noticeTitle);
                        formData.append('contact_check', form.contactCheck);
                        formData.append('payment_notice', content);
                        
                        const res = await axios.post("/plugin/Goandpay/api/save", formData);
                        if (res.data?.code === 200) {
                            ElMessage.success('保存成功');
                            await fetchData();
                        } else {
                            ElMessage.error(res.data?.msg || '保存失败');
                        }
                    } catch (error) {
                        ElMessage.error('保存失败');
                    } finally {
                        loading.value = false;
                    }
                };

                return {
                    loading,
                    form,
                    save,
                    previewContent,
                    refreshPreview,
                    useTemplate,
                    resetForm
                };
            }
        })
        .use(ElementPlus)
        .mount('#app');
    </script>
</body>
</html>