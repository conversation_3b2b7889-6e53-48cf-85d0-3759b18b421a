# 970faka.com 注册工具

一个功能完整的自动化注册工具，支持GUI界面和命令行操作。

## 🚀 快速启动

### 方式一：使用主启动文件（推荐）
```bash
# 启动菜单模式（默认）
python main.py

# 直接启动GUI界面
python main.py gui

# 启动Selenium自动化
python main.py selenium

# 启动批量注册
python main.py batch

# 配置管理
python main.py config
```

### 方式二：使用传统启动文件
```bash
# 启动菜单模式
python start.py

# 直接启动GUI
python gui/run_gui.py
```

### 方式三：使用可执行文件
```bash
cd dist
用户注册工具.exe
```

## 📁 项目结构

```
register/
├── 📂 gui/                    # GUI界面模块
│   ├── register_gui.py        # 主GUI界面
│   └── run_gui.py             # GUI启动入口
├── 📂 core/                   # 核心功能模块
│   ├── register.py            # 注册核心功能
│   ├── sms_api.py             # 椰子云短信API
│   ├── captcha_handler.py     # 验证码处理
│   └── batch_register.py      # 批量注册
├── 📂 browser_automation/     # 浏览器自动化模块
│   ├── selenium_register.py   # Selenium注册核心
│   ├── element_helper.py      # 页面元素操作
│   └── xpath_config.py        # XPath配置
├── 📂 config/                 # 配置管理模块
│   ├── config_manager.py      # 配置管理器
│   ├── config.ini             # 主配置文件
│   └── config_example.ini     # 配置示例
├── 📂 utils/                  # 工具模块
│   └── fix_chromedriver.py    # ChromeDriver修复
├── 📂 docs/                   # 文档模块
│   └── 文件说明.md            # 详细文档
├── 📂 dist/                   # 发布版本
│   └── 用户注册工具.exe       # 可执行程序
├── main.py                    # 🚀 主启动文件
├── start.py                   # 传统启动文件
└── requirements.txt           # 依赖列表
```

## ✨ 主要功能

### 🖥️ GUI界面
- 直观的图形界面操作
- 实时进度显示
- 配置管理
- 日志查看

### 🤖 自动化注册
- Selenium浏览器自动化
- 自动验证码识别（ddddocr）
- 椰子云短信验证
- 智能重试机制

### 📦 批量注册
- 多线程并发注册
- 自动用户数据生成
- 进度实时监控
- 错误处理和重试

### 📱 短信验证
- 椰子云API集成
- 自动获取手机号
- 自动接收验证码
- 智能等待机制

## ⚙️ 配置说明

主配置文件：`config/config.ini`

```ini
[BASIC]
domain = https://970faka.com    # 注册域名
username_length = 8             # 用户名长度
password_length = 8             # 密码长度

[SELENIUM]
headless = False                # 无头模式
window_width = 1920            # 窗口宽度
window_height = 1080           # 窗口高度
sms_wait_timeout = 180         # 短信等待超时（秒）

[YEZI_CLOUD]
username = your_username        # 椰子云用户名
password = your_password        # 椰子云密码
project_id = your_project_id    # 椰子云项目ID
```

## 📋 依赖要求

```bash
pip install -r requirements.txt
```

主要依赖：
- PyQt6 - GUI界面
- selenium - 浏览器自动化
- requests - HTTP请求
- ddddocr - 验证码识别
- webdriver-manager - ChromeDriver管理

## 🔧 安装说明

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd register
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **配置椰子云**（可选）
   ```bash
   python main.py config
   ```

4. **启动程序**
   ```bash
   python main.py gui
   ```

## 💡 使用建议

1. **首次使用**：建议先使用GUI界面熟悉功能
2. **椰子云配置**：配置后可实现完全自动化
3. **批量注册**：适合大量用户注册需求
4. **Selenium模式**：适合单个或少量用户注册

## 🐛 常见问题

1. **ChromeDriver问题**
   ```bash
   python main.py
   # 选择 "6. 🔧 修复ChromeDriver"
   ```

2. **椰子云连接失败**
   - 检查用户名密码是否正确
   - 检查项目ID是否有效
   - 检查网络连接

3. **验证码识别失败**
   - 确保已安装ddddocr
   - 可切换到手动输入模式

## 📞 技术支持

如有问题，请查看 `docs/文件说明.md` 获取详细文档。
