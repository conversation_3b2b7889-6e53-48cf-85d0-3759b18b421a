<?php

namespace plugin\AgisoStorage;

use app\common\library\Plugin;
use app\common\model\GoodsEquityApp as GoodsEquityAppModel;
 
class AgisoStorage extends Plugin {

    /**
     * 插件安装方法
     * @return bool
     */
    public function install() {
        $app = GoodsEquityAppModel::where(['code' => 'AgisoStorage'])->find();
        if (!$app) {
            $app = new GoodsEquityAppModel();
        }
        $app->code = "AgisoStorage";
        $app->name = "阿奇索91卡券仓库";
        $app->image = "https://www.jingsoft.com/upload/b1/57ce468248df5c3930250e3f282794.png";
        $app->sub_name = "调用阿奇索卡券仓库库存";
        $app->description = '<p>对接参考获取说明：</p><p>1、前往阿奇索开放平台： <a href=\"https://open.agiso.com/\" target=\"_blank\">https://open.agiso.com/</a> 注册账号，完成资质认证，添加一个【电商业务】应用</p><p>2、获取AppSecret秘钥：编辑刚刚添加的应用即可查看AppSecret秘钥</p><p>3、获取AccessToken：前往91卡券仓库 <a href=\"https://mai.91kami.com/#/open/authorize\" target=\"_blank\">https://mai.91kami.com/#/open/authorize</a> &nbsp;添加开放平台Appid进行应用关联授权，<span style=\"color: rgb(255, 77, 79);\">务必保存自动显示的AccessToken，</span><span style=\"color: rgb(225, 60, 57);\">并且给授权添加【提卡】接口权限</span></p><p>4、获取卡券ID：前往 <a href=\"https://mai.91kami.com/#/repertory/cardkind\" target=\"_blank\">https://mai.91kami.com/#/repertory/cardkind</a> &nbsp;点击管理卡券，在弹出窗口左上角即可看到“卡种ID”</p><p><br></p>';
        $app->system_field = [
            [
                'id' => 'app_secret',
                'name' => 'AppSecret',
                'remark' => '请输入AppSecret',
                'required' => true,
                'type' => 'input',
                'data' => [
                ],
                'accept' => '',
            ],
            [
                'id' => 'access_token',
                'name' => 'AccessToken',
                'remark' => '请输入AccessToken',
                'required' => true,
                'type' => 'input',
                'data' => [
                ],
                'accept' => '',
            ],
            [
                'id' => 'cpkId',
                'name' => '卡券ID',
                'remark' => '请输入卡券ID',
                'required' => true,
                'type' => 'input',
                'data' => [
                ],
                'accept' => '',
            ],
            [
                'id' => 'instructions',
                'name' => '使用说明',
                'remark' => '请输入使用说明，不填不显示',
                'required' => false,
                'type' => 'input',
                'textarea' => true,
                'data' => [
                ],
                'accept' => '',
            ],
        ];
        $app->buyer_field = [];
        $app->create_time = time();
        $app->save();

        return true;
    }

    /**
     * 插件卸载方法
     * @return bool
     */
    public function uninstall() {
        $app = GoodsEquityAppModel::where(['code' => 'AgisoStorage'])->find();
        if ($app) {
            $app->delete();
        }
        return true;
    }

    // 查询状态
    public function askStatus($biz_content) {
        return true;
    }

    // 订单处理

    public function orderDetail($biz_content) {
        $cardDetail = $this->request("http://gw.api.agiso.com/acpr/CardPwdKind/GetDetails", ['timestamp' => time(), 'cpkId' => $biz_content['system_value']['cpkId'] ?? ''], $biz_content['system_value']['app_secret'] ?? '', $biz_content['system_value']['access_token'] ?? '');
        $cardDetail = json_decode($cardDetail, true);
        if ($cardDetail['IsSuccess'] != true) {
            return ['status' => 1, 'msg' => $cardDetail['Error_Msg']];
        }

        if ($cardDetail['Data']['TotalCount'] - $cardDetail['Data']['UsedCount'] <= 0) {
            return ['status' => 1, 'msg' => '库存不足，请联系商家前往91仓库补货，商家如补货完成请重新查询！'];
        }

        $sendPick = $this->request("http://gw.api.agiso.com/acpr/CardPwd/HandPick",
                [
                    "timestamp" => time(),
                    "cpkId" => $biz_content['system_value']['cpkId'] ?? '',
                    "num" => $biz_content['quantity'] ?? 1,
                    "handPickOrderId" => $biz_content['trade_no'] ?? ''
                ], $biz_content['system_value']['app_secret'] ?? '', $biz_content['system_value']['access_token'] ?? '');

        $sendPick = json_decode($sendPick, true);

        if ($sendPick['IsSuccess'] != true) {
            return ['status' => 1, 'msg' => $sendPick['Error_Msg']];
        } else {
            $CardPwdArr = $sendPick['Data']['CardPwdArr'];
            $html_content = '';
            for ($i = 0; $i < count($CardPwdArr); $i++) {
                $j = $i + 1;
                $pwd = $CardPwdArr[$i]['p'] == '' ? '' : "<span>{$cardDetail['Data']['PrefixPwd']}</span><span>{$CardPwdArr[$i]['p']}</span>";
                $html_content .= "<p><b style='display: inline-flex;align-items: center;box-sizing: border-box;height: 24px;padding: 0 8px;border-radius:4px;font-size: 12px;line-height: 22px;color:rgb(0,180,42);background:rgb(232,255,234);margin-right:6px;'>第{$j}张</b><span>{$cardDetail['Data']['PrefixCardNo']}</span><span style='margin-right:16px'>{$CardPwdArr[$i]['c']}</span>{$pwd}</p>";
            }

            $instructions = ($biz_content['system_value']['instructions'] ?? '') == "" ? "" : "<div><span style='color:rgb(39, 46, 59);font-weight:600'>使用说明：</span><br><div style='margin-top:4px'>" . ($biz_content['system_value']['instructions'] ?? '') . "</div></div></div>";

            $html = "<div style='padding:8px'>{$html_content}{$instructions}</div>";

            return ['status' => 2, 'msg' => '处理成功', 'data' => $html];
        }
    }

    private function request($url, $params, $appSecret, $accessToken) {
        //业务参数
        $params['sign'] = $this->sign($params, $appSecret);

        //设置Header
        $headers[] = "Authorization: Bearer " . $accessToken;
        $headers[] = "ApiVersion: 1";

        $ci = curl_init();
        curl_setopt($ci, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_0);
        curl_setopt($ci, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($ci, CURLOPT_ENCODING, "");
        curl_setopt($ci, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ci, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ci, CURLOPT_HEADER, FALSE);
        curl_setopt($ci, CURLOPT_POST, TRUE);
        curl_setopt($ci, CURLOPT_POSTFIELDS, $params);
        curl_setopt($ci, CURLOPT_URL, $url);
        curl_setopt($ci, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ci, CURLINFO_HEADER_OUT, TRUE);
        $response = curl_exec($ci);
        curl_close($ci);
        return $response;
    }

    private function sign($args, $client_secret) {
        ksort($args);
        $str = '';
        foreach ($args as $key => $value) {
            $str .= ($key . $value);
        }
        //头尾加入AppSecret
        $str = $client_secret . $str . $client_secret;
        $encodeStr = md5($str);
        return $encodeStr;
    }
}
