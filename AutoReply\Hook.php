<?php
namespace plugin\AutoReply;

use think\facade\Db;

class Hook
{
    public function handle()
    {
        try {
            // 检查功能是否启用
            $status = intval(plugconf("AutoReply.reply_config.status") ?? 0);
            error_log("AutoReply: 当前功能状态: {$status}");
            
            if ($status !== 1) {
                error_log("AutoReply: 功能未启用");
                return;
            }

            // 获取检查间隔时间
            $checkInterval = intval(plugconf("AutoReply.reply_config.check_interval") ?? 60);
            $lastCheckTime = time() - $checkInterval;

            // 获取关键词规则
            $rules = plugconf("AutoReply.keyword_rules") ?: [];
            if (empty($rules)) {
                return;
            }

            // 查询需要处理的投诉消息
            $messages = Db::name('complaint_message')
                ->alias('cm')
                ->where('cm.create_time', '>', $lastCheckTime)
                ->where('cm.identity', 'buyer')
                ->select()
                ->toArray();

            if (empty($messages)) {
                return;
            }

            foreach ($messages as $message) {
                // 检查这个投诉是否有任何平台回复（包括历史回复）
                $hasAnyPlatformReply = Db::name('complaint_message')
                    ->where('complaint_id', $message['complaint_id'])
                    ->where('identity', 'platform')
                    ->find();

                // 如果从未回复过，可以触发无关键词规则
                $canTriggerNoKeyword = !$hasAnyPlatformReply;

                // 检查当前检查周期内是否已回复
                $hasAutoReplied = Db::name('complaint_message')
                    ->where('complaint_id', $message['complaint_id'])
                    ->where('identity', 'platform')
                    ->where('create_time', '>', $lastCheckTime)
                    ->find();
                
                if ($hasAutoReplied) {
                    continue; // 如果在当前周期已经回复过，跳过这条消息
                }

                $hasReplied = false;

                // 先处理有关键词的规则
                foreach ($rules as $rule) {
                    if (empty($rule['status']) || $hasReplied) {
                        continue;
                    }

                    // 跳过无关键词规则，优先处理有关键词的规则
                    if (empty($rule['keywords'])) {
                        continue;
                    }

                    // 处理有关键词的规则
                    foreach ($rule['keywords'] as $keyword) {
                        if (strpos($message['content'], $keyword) !== false) {
                            $this->handleReply($rule, $message, $keyword);
                            $hasReplied = true;
                            break;
                        }
                    }
                }

                // 如果没有匹配到关键词规则，且允许触发无关键词规则
                if (!$hasReplied && $canTriggerNoKeyword) {
                    foreach ($rules as $rule) {
                        if (!empty($rule['status']) && empty($rule['keywords'])) {
                            $this->handleReply($rule, $message);
                            break; // 只使用第一个无关键词规则
                        }
                    }
                }
            }

            // 新增用户自动回复处理
            $this->handleUserAutoReply();
            
        } catch (\Exception $e) {
            error_log("AutoReply Hook error: " . $e->getMessage());
        }
    }

    // 抽取回复处理逻辑为单独的方法
    private function handleReply($rule, $message, $keyword = '')
    {
        try {
            $currentTime = time();
            
            // 插入回复消息
            $insertResult = Db::name('complaint_message')->insert([
                'complaint_id' => $message['complaint_id'],
                'content_type' => 0,
                'content' => $rule['reply'],
                'identity' => 'platform',
                'create_time' => $currentTime
            ]);
            
            if ($insertResult) {
                // 处理邮件通知
                if (intval(plugconf("AutoReply.reply_config.email_notify") ?? 0) === 1) {
                    $notifyEmail = plugconf("AutoReply.reply_config.notify_email");
                    if ($notifyEmail) {
                        $this->sendNotifyEmail($message, $rule, $keyword, $notifyEmail);
                    }
                }
                
                error_log("AutoReply: 成功回复投诉 {$message['complaint_id']}");
                return true;
            }
        } catch (\Exception $e) {
            error_log("AutoReply: 回复投诉失败: " . $e->getMessage());
        }
        return false;
    }

    // 抽取邮件发送逻辑为单独的方法
    private function sendNotifyEmail($message, $rule, $keyword, $notifyEmail)
    {
        try {
            // 检查邮件发送频率
            $lastEmailTime = cache('auto_reply_last_email_time') ?: 0;
            $currentTime = time();
            
            // 限制每60秒只能发送一次邮件
            if ($currentTime - $lastEmailTime < 60) {
                error_log("AutoReply: 邮件发送太频繁，已跳过");
                return;
            }
            
            $service = new \app\common\service\EmailService();
            $emailContent = "系统自动回复了一条投诉消息：\n\n";
            $emailContent .= "回复时间：" . date('Y-m-d H:i:s', $currentTime) . "\n";
            $emailContent .= "投诉ID：{$message['complaint_id']}\n";
            $emailContent .= "买家消息：{$message['content']}\n";
            $emailContent .= "触发关键词：" . ($keyword ?: '未设置关键词') . "\n";
            $emailContent .= "自动回复：{$rule['reply']}\n";
            
            $res = $service->subject('投诉自动回复通知')
                         ->message($emailContent)
                         ->to($notifyEmail)
                         ->send();
            
            if ($res) {
                cache('auto_reply_last_email_time', $currentTime);
                error_log("AutoReply: 邮件发送成功");
            } else {
                error_log("AutoReply: 邮件发送失败: " . $service->getError());
            }
        } catch (\Exception $e) {
            error_log("AutoReply: 邮件发送异常: " . $e->getMessage());
        }
    }

    // 处理用户自动回复
    private function handleUserAutoReply()
    {
        try {
            // 获取所有用户
            $users = Db::name('user')->select()->toArray();

            foreach ($users as $user) {
                // 检查用户的自动回复功能是否启用
                $status = intval(plugconf("AutoReply.merchant_{$user['id']}.status") ?? 0);

                if ($status !== 1) {
                    continue;
                }

                // 获取检查间隔时间
                $checkInterval = intval(plugconf("AutoReply.merchant_{$user['id']}.check_interval") ?? 60);
                $lastCheckTime = time() - $checkInterval;

                // 获取用户的关键词规则
                $rules = plugconf("AutoReply.merchant_{$user['id']}.keyword_rules") ?: [];
                if (empty($rules)) {
                    continue;
                }

                // 查询需要处理的投诉消息 - 修复：只查询属于当前用户的投诉消息
                $messages = Db::name('complaint_message')
                    ->alias('cm')
                    ->join('complaint c', 'c.id = cm.complaint_id')
                    ->where('cm.create_time', '>', $lastCheckTime)
                    ->where('cm.identity', 'buyer')
                    ->where('c.user_id', $user['id'])  // 关键修复：只处理属于当前用户的投诉
                    ->field('cm.*')
                    ->select()
                    ->toArray();

                if (empty($messages)) {
                    continue;
                }

                foreach ($messages as $message) {
                    // 检查这个投诉是否已经回复过
                    $hasReplied = Db::name('complaint_message')
                        ->where('complaint_id', $message['complaint_id'])
                        ->where('identity', 'user')
                        ->where('create_time', '>', $lastCheckTime)
                        ->find();

                    if ($hasReplied) {
                        continue;
                    }

                    // 处理规则匹配
                    foreach ($rules as $rule) {
                        if (empty($rule['status'])) {
                            continue;
                        }

                        // 如果没有关键词，作为默认回复规则
                        if (empty($rule['keywords'])) {
                            $this->insertUserReply($message['complaint_id'], $rule['reply'], $user['id']);
                            break;
                        }

                        // 检查关键词匹配
                        foreach ($rule['keywords'] as $keyword) {
                            if (strpos($message['content'], $keyword) !== false) {
                                $this->insertUserReply($message['complaint_id'], $rule['reply'], $user['id']);
                                break 2;
                            }
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            error_log("AutoReply User Hook error: " . $e->getMessage());
        }
    }

    // 插入用户回复
    private function insertUserReply($complaintId, $content, $userId)
    {
        try {
            $currentTime = time();
            
            // 插入回复消息
            $insertResult = Db::name('complaint_message')->insert([
                'complaint_id' => $complaintId,
                'content_type' => 0,
                'content' => $content,
                'identity' => 'user',
                'create_time' => $currentTime
            ]);
            
            if ($insertResult) {
                // 处理邮件通知
                if (intval(plugconf("AutoReply.merchant_{$userId}.email_notify") ?? 0) === 1) {
                    $notifyEmail = plugconf("AutoReply.merchant_{$userId}.notify_email");
                    if ($notifyEmail) {
                        $this->sendUserAutoReplyEmail($complaintId, $content, $notifyEmail, $userId);
                    }
                }
                
                error_log("AutoReply: 用户 {$userId} 成功自动回复投诉 {$complaintId}");
                return true;
            }
        } catch (\Exception $e) {
            error_log("AutoReply: 用户 {$userId} 自动回复失败: " . $e->getMessage());
        }
        return false;
    }

    // 发送用户自动回复邮件通知
    private function sendUserAutoReplyEmail($complaintId, $content, $notifyEmail, $userId)
    {
        try {
            $lastEmailTime = cache("auto_reply_user_{$userId}_last_email_time") ?: 0;
            $currentTime = time();
            
            if ($currentTime - $lastEmailTime < 60) {
                return;
            }
            
            $service = new \app\common\service\EmailService();
            $emailContent = "系统已自动回复投诉消息：\n\n";
            $emailContent .= "回复时间：" . date('Y-m-d H:i:s', $currentTime) . "\n";
            $emailContent .= "投诉ID：{$complaintId}\n";
            $emailContent .= "回复内容：{$content}\n";
            
            $res = $service->subject('投诉自动回复通知')
                         ->message($emailContent)
                         ->to($notifyEmail)
                         ->send();
            
            if ($res) {
                cache("auto_reply_user_{$userId}_last_email_time", $currentTime);
                error_log("AutoReply: 用户 {$userId} 邮件通知发送成功");
            }
        } catch (\Exception $e) {
            error_log("AutoReply: 用户 {$userId} 邮件通知发送失败: " . $e->getMessage());
        }
    }
}
