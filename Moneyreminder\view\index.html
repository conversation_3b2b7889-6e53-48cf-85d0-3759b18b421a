<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>账户余额提醒</title>
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <link rel="icon" href="<?php echo isset($favicon) ? $favicon : '/favicon.ico'; ?>" type="image/x-icon">
    <style>
        body {
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
        }
        .container {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .settings-form {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        }
        .card-header {
            padding: 15px 20px;
            border-bottom: 1px solid #ebeef5;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .card-header h3 {
            margin: 0;
            font-size: 18px;
            color: #303133;
        }
        #loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #fff;
            z-index: 9999;
        }
        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(0, 0, 0, 0.1);
            border-top-color: #409EFF;
            border-radius: 50%;
            animation: spin 1s ease infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- 加载动画 -->
    <div id="loading">
        <div class="spinner"></div>
    </div>

    <div id="app" style="display: none">
        <div class="container">
            <el-card class="settings-form">
                <template #header>
                    <div class="card-header">
                        <h3>
                            <i class="el-icon-setting"></i>
                            配置设置
                        </h3>
                    </div>
                </template>
                <el-form :model="config" label-width="120px">
                    <el-form-item label="监控状态">
                        <el-switch v-model="config.monitor_status"></el-switch>
                    </el-form-item>

                    <el-form-item label="保证金阈值">
                        <el-input-number v-model="config.deposit_threshold" :min="0" :step="100" style="width: 200px;"></el-input-number>
                        <span class="el-form-item__description">元，低于此金额将发送提醒</span>
                    </el-form-item>

                    <el-form-item label="运营账户阈值">
                        <el-input-number v-model="config.operate_threshold" :min="0" :step="100" style="width: 200px;"></el-input-number>
                        <span class="el-form-item__description">元，低于此金额将发送提醒</span>
                    </el-form-item>

                    <el-form-item label="检查间隔">
                        <el-input-number v-model="config.check_interval" :min="1" :max="1440" style="width: 200px;"></el-input-number>
                        <span class="el-form-item__description">分钟</span>
                    </el-form-item>

                    <el-form-item label="通知方式">
                        <el-select v-model="config.notify_type" style="width: 200px;">
                            <el-option label="仅商户端(默认)" value="1"></el-option>
                            <el-option label="商户端+QQ" value="2"></el-option>
                            <el-option label="商户端+邮箱(需开启)" value="3"></el-option>
                            <el-option label="商户端+wxpusher" value="4"></el-option>
                            <el-option label="商户端+企业微信群" value="5"></el-option>
                            <el-option label="商户端+机器人" value="6"></el-option>
                        </el-select>
                        <span class="el-form-item__description">商户端通知为默认开启，其他通知方式为额外通知</span>
                    </el-form-item>

                    <el-form-item label="通知邮箱" v-if="config.notify_type === '3'">
                        <el-input v-model="config.notify_email" style="width: 300px;"></el-input>
                    </el-form-item>

                    <el-form-item label="通知QQ" v-if="config.notify_type === '2'">
                        <el-input v-model="config.notify_qq" style="width: 300px;"></el-input>
                    </el-form-item>

                    <el-form-item label="wxpusher配置" v-if="config.notify_type === '4'">
                        <el-input v-model="config.notify_wxpusher" style="width: 300px;"></el-input>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="saveConfig">保存配置</el-button>
                        <el-button type="success" @click="testNotify">测试通知</el-button>
                    </el-form-item>
                </el-form>
            </el-card>
        </div>
    </div>

    <!-- 在 script 标签之前添加 PHP 数据处理 -->
    <?php
    $configData = json_encode([
        'monitor_status' => isset($params['monitor_status']) ? (bool)$params['monitor_status'] : false,
        'deposit_threshold' => isset($params['deposit_threshold']) ? (float)$params['deposit_threshold'] : 1000.00,
        'operate_threshold' => isset($params['operate_threshold']) ? (float)$params['operate_threshold'] : 500.00,
        'check_interval' => isset($params['check_interval']) ? (int)$params['check_interval'] : 60,
        'notify_type' => isset($params['notify_type']) ? (string)$params['notify_type'] : '1',
        'notify_email' => isset($params['notify_email']) ? (string)$params['notify_email'] : '',
        'notify_qq' => isset($params['notify_qq']) ? (string)$params['notify_qq'] : '',
        'notify_wxpusher' => isset($params['notify_wxpusher']) ? (string)$params['notify_wxpusher'] : ''
    ], JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP | JSON_PRESERVE_ZERO_FRACTION);
    ?>
    <script>
    // 定义全局变量存储 PHP 数据
    window.INIT_DATA = {
        config: JSON.parse('<?= $configData ?>')
    };
    console.log('Initial config:', window.INIT_DATA.config); // 调试日志
    </script>

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>
    
    <script>
        const { createApp, ref, reactive, onMounted, watch } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        const app = createApp({
            setup() {
                console.log('Setup initial config:', window.INIT_DATA.config); // 调试日志

                // 使用全局变量中的数据初始化响应式对象
                const config = reactive({
                    monitor_status: Boolean(window.INIT_DATA.config.monitor_status),
                    deposit_threshold: parseFloat(window.INIT_DATA.config.deposit_threshold),
                    operate_threshold: parseFloat(window.INIT_DATA.config.operate_threshold),
                    check_interval: parseInt(window.INIT_DATA.config.check_interval),
                    notify_type: String(window.INIT_DATA.config.notify_type),
                    notify_email: String(window.INIT_DATA.config.notify_email || ''),
                    notify_qq: String(window.INIT_DATA.config.notify_qq || ''),
                    notify_wxpusher: String(window.INIT_DATA.config.notify_wxpusher || '')
                });

                console.log('Reactive config:', config); // 调试日志

                const saveConfig = async () => {
                    try {
                        // 转换数据类型
                        const postData = {
                            monitor_status: Number(config.monitor_status),
                            deposit_threshold: parseFloat(config.deposit_threshold),
                            operate_threshold: parseFloat(config.operate_threshold),
                            check_interval: parseInt(config.check_interval),
                            notify_type: String(config.notify_type),
                            notify_email: String(config.notify_email || ''),
                            notify_qq: String(config.notify_qq || ''),
                            notify_wxpusher: String(config.notify_wxpusher || '')
                        };

                        console.log('Saving config:', postData); // 调试日志

                        const response = await axios.post("{:plugin_url('Moneyreminder/Api/save')}", postData);
                        console.log('Save response:', response.data); // 调试日志
                        
                        if (response.data.code === 1) {
                            ElMessage.success('保存成功');
                            // 更新本地配置
                            const savedData = response.data.data;
                            Object.assign(config, {
                                monitor_status: Boolean(savedData.monitor_status),
                                deposit_threshold: parseFloat(savedData.deposit_threshold),
                                operate_threshold: parseFloat(savedData.operate_threshold),
                                check_interval: parseInt(savedData.check_interval),
                                notify_type: String(savedData.notify_type),
                                notify_email: String(savedData.notify_email || ''),
                                notify_qq: String(savedData.notify_qq || ''),
                                notify_wxpusher: String(savedData.notify_wxpusher || '')
                            });

                            console.log('Updated config:', config); // 调试日志
                        } else {
                            ElMessage.error(response.data.msg || '保存失败');
                        }
                    } catch (error) {
                        console.error('Save config error:', error);
                        ElMessage.error(error.response?.data?.msg || '保存失败');
                    }
                };

                const testNotify = () => {
                    // 检查是否启用监控
                    if (!config.monitor_status) {
                        ElMessage({
                            type: 'warning',
                            message: '请先启用监控状态',
                            duration: 5000
                        });
                        return;
                    }

                    // 检查当前通知方式是否配置完整
                    if (config.notify_type === '3' && !config.notify_email) {
                        ElMessage({
                            type: 'warning',
                            message: '请先配置通知邮箱',
                            duration: 5000
                        });
                        return;
                    }
                    if (config.notify_type === '2' && !config.notify_qq) {
                        ElMessage({
                            type: 'warning',
                            message: '请先配置通知QQ',
                            duration: 5000
                        });
                        return;
                    }
                    if (config.notify_type === '4' && !config.notify_wxpusher) {
                        ElMessage({
                            type: 'warning',
                            message: '请先配置WxPusher',
                            duration: 5000
                        });
                        return;
                    }

                    ElMessageBox.confirm(
                        `将发送测试通知，当前通知方式：${getNotifyTypeName(config.notify_type)}，确定继续吗？`,
                        '提示',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'info',
                            dangerouslyUseHTMLString: true,
                            message: `<p>将发送测试通知，当前配置:</p>
                                     <p>- 通知方式：${getNotifyTypeName(config.notify_type)}</p>
                                     <p>- 保证金阈值：${config.deposit_threshold}元</p>
                                     <p>- 运营钱包阈值：${config.operate_threshold}元</p>
                                     <p>确定继续吗？</p>`
                        }
                    ).then(async () => {
                        const loading = ElMessage({
                            type: 'info',
                            message: '正在发送测试通知...',
                            duration: 0
                        });

                        try {
                            console.log('发送测试通知请求');
                            const response = await axios.post(
                                "{:plugin_url('Moneyreminder/Api/testNotify')}",
                                {},
                                {
                                    timeout: 30000 // 设置30秒超时
                                }
                            );
                            
                            loading.close();
                            console.log('测试通知响应:', response.data);
                            
                            if (response.data.code === 1) {
                                // 成功
                                ElMessage({
                                    type: 'success',
                                    message: '测试通知发送成功！请查看对应的通知渠道',
                                    duration: 5000
                                });
                            } else {
                                // 有错误信息
                                let errorMsg = response.data.msg || '发送测试通知失败';
                                if (response.data.data && response.data.data.error) {
                                    errorMsg += '：' + response.data.data.error;
                                }
                                
                                // 显示详细错误信息
                                ElMessageBox.alert(
                                    errorMsg,
                                    '测试通知失败',
                                    {
                                        confirmButtonText: '确定',
                                        type: 'error'
                                    }
                                );
                                
                                console.error('测试通知失败:', response.data);
                            }
                        } catch (error) {
                            loading.close();
                            console.error('测试通知错误:', error);
                            let errorMsg = '发送测试通知失败';
                            
                            if (error.response) {
                                // 服务器返回错误
                                const responseData = error.response.data;
                                errorMsg += '：' + (responseData.msg || responseData.message || error.response.statusText);
                                if (responseData.data && responseData.data.error) {
                                    errorMsg += ' - ' + responseData.data.error;
                                }
                                console.error('服务器错误响应:', responseData);
                            } else if (error.request) {
                                // 请求发送成功但没有收到响应
                                errorMsg += '：服务器无响应，请检查服务器日志';
                                console.error('无服务器响应');
                            } else {
                                // 请求发送失败
                                errorMsg += '：' + error.message;
                                console.error('请求错误:', error.message);
                            }
                            
                            // 使用对话框显示详细错误
                            ElMessageBox.alert(
                                errorMsg,
                                '测试通知失败',
                                {
                                    confirmButtonText: '确定',
                                    type: 'error'
                                }
                            );
                        }
                    }).catch(() => {
                        console.log('用户取消发送测试通知');
                    });
                };

                // 获取通知方式名称
                const getNotifyTypeName = (type) => {
                    const types = {
                        '1': '仅商户端',
                        '2': '商户端+QQ',
                        '3': '商户端+邮箱',
                        '4': '商户端+wxpusher',
                        '5': '商户端+企业微信群',
                        '6': '商户端+机器人'
                    };
                    return types[type] || '未知';
                };

                // 生命周期钩子
                onMounted(() => {
                    // 隐藏加载动画，显示主内容
                    document.getElementById("loading").style.display = "none";
                    document.getElementById("app").style.display = "block";
                });

                return {
                    config,
                    saveConfig,
                    testNotify
                };
            }
        });

        // 使用 Element Plus
        app.use(ElementPlus, {
            locale: ElementPlusLocaleZhCn
        });

        // 挂载应用
        app.mount('#app');
    </script>
</body>
</html> 