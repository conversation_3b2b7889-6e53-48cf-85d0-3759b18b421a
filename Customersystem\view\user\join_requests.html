<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>商家邀请管理</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>
    <style>
        [v-cloak] { display: none; }
        
        :root {
            --primary-color: #6366f1;
            --primary-hover: #8b5cf6;
            --primary-light: #a5b4fc;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --text-color: #1f2937;
            --text-secondary: #4b5563;
            --text-light: #6b7280;
            --text-muted: #9ca3af;
            --border-color: #e5e7eb;
            --border-light: #f3f4f6;
            --bg-color: #f8fafc;
            --bg-secondary: #ffffff;
            --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --card-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --hover-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --transition-speed: 0.2s;
            --border-radius: 12px;
            --border-radius-lg: 16px;
            --spacing-xs: 4px;
            --spacing-sm: 8px;
            --spacing-md: 16px;
            --spacing-lg: 24px;
            --spacing-xl: 32px;
        }
        
        /* 现代化页面基础样式 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: var(--text-color);
            line-height: 1.6;
            opacity: 0;
            transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 14px;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        body.page-loaded {
            opacity: 1;
        }

        body.page-fully-loaded:after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-hover), var(--success-color));
            animation: loadingBar 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
            z-index: 2000;
            border-radius: 0 0 2px 2px;
        }
        
        @keyframes loadingBar {
            0% { width: 0; opacity: 1; }
            80% { width: 100%; opacity: 1; }
            100% { width: 100%; opacity: 0; }
        }
        
        .container {
            max-width: 1280px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background: var(--bg-secondary);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--card-shadow-lg);
            border: 1px solid var(--border-light);
            animation: fadeIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
            border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.98);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
        
        /* 标签切换动画 */
        .tab-changing {
            animation: tabChange 0.3s ease;
        }
        
        @keyframes tabChange {
            0% { opacity: 1; transform: translateY(0); }
            50% { opacity: 0.8; transform: translateY(5px); }
            100% { opacity: 1; transform: translateY(0); }
        }
        
        /* 页面切换动画 */
        .page-changing {
            animation: pageChange 0.3s ease;
        }
        
        @keyframes pageChange {
            0% { opacity: 1; transform: translateX(0); }
            50% { opacity: 0.8; transform: translateX(5px); }
            100% { opacity: 1; transform: translateX(0); }
        }
        
        /* 数据加载动画 */
        .loading-data {
            position: relative;
        }
        
        .loading-data:after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(to right, var(--primary-color), var(--success-color));
            animation: loadingProgress 1s infinite linear;
            z-index: 10;
            border-radius: 3px;
        }
        
        @keyframes loadingProgress {
            0% { width: 0; left: 0; }
            50% { width: 50%; left: 25%; }
            100% { width: 0; left: 100%; }
        }
        
        /* 卡片可见性动画 */
        .request-card, .session-card {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.5s ease, transform 0.5s ease;
        }
        
        .card-visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* 表格行动画 */
        .el-table__row {
            opacity: 0;
            transform: translateY(10px);
            transition: opacity 0.4s ease, transform 0.4s ease;
        }
        
        .row-visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* 按钮点击动画 */
        .btn-clicked {
            animation: btnClick 0.5s ease;
        }
        
        @keyframes btnClick {
            0% { transform: scale(1); }
            50% { transform: scale(0.95); }
            100% { transform: scale(1); }
        }
        
        /* 成功动画 */
        .success-animation {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80px;
            height: 80px;
            background-color: rgba(103, 194, 58, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 40px;
            z-index: 9999;
            animation: successPop 1.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            pointer-events: none;
            box-shadow: 0 10px 30px rgba(103, 194, 58, 0.4);
        }
        
        @keyframes successPop {
            0% { transform: translate(-50%, -50%) scale(0); opacity: 0; }
            25% { transform: translate(-50%, -50%) scale(1.2); opacity: 1; }
            50% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
            80% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
            100% { transform: translate(-50%, -50%) scale(1.5); opacity: 0; }
        }
        
        /* 自定义消息框样式 */
        .custom-message-box {
            border-radius: 12px !important;
            overflow: hidden !important;
        }
        
        .custom-message-box .el-message-box__header {
            background-color: rgba(245, 247, 250, 0.5) !important;
            padding: 20px !important;
        }
        
        .custom-message-box .el-message-box__title {
            font-size: 18px !important;
            font-weight: 600 !important;
        }
        
        .custom-message-box .el-message-box__content {
            padding: 25px 20px !important;
        }
        
        .custom-message-box .el-message-box__btns {
            padding: 15px 20px !important;
        }
        
        /* 修改消息框取消按钮样式 - 提高优先级 */
        .el-message-box__btns .el-button--default,
        .el-message-box__btns .el-button.el-button--default {
            background: #f0f2f5 !important;
            color: #606266 !important;
            border: none !important;
        }

        /* 确保取消按钮不会使用红色背景 - 多重选择器提高优先级 */
        .el-message-box__btns button:nth-child(1),
        .el-message-box__btns .el-button:nth-child(1),
        .el-message-box__btns button.el-button:nth-child(1) {
            background: #f0f2f5 !important;
            color: #606266 !important;
            border: none !important;
        }

        .el-message-box__btns .el-button--default:hover,
        .el-message-box__btns button:nth-child(1):hover,
        .el-message-box__btns .el-button:nth-child(1):hover,
        .el-message-box__btns button.el-button:nth-child(1):hover {
            background: #e6e8eb !important;
            color: #606266 !important;
            transform: translateY(-2px) !important;
        }
        
        /* 加强按钮脉动效果 */
        .btn-pulse {
            animation: strongPulse 2s infinite;
            position: relative;
        }
        
        .btn-pulse:after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 8px;
            animation: btnRipple 2s infinite;
        }
        
        @keyframes strongPulse {
            0% { transform: scale(1); }
            5% { transform: scale(1.05); }
            10% { transform: scale(1); }
            100% { transform: scale(1); }
        }
        
        @keyframes btnRipple {
            0% { box-shadow: 0 0 0 0 rgba(62, 120, 240, 0.5); }
            70% { box-shadow: 0 0 0 15px rgba(62, 120, 240, 0); }
            100% { box-shadow: 0 0 0 0 rgba(62, 120, 240, 0); }
        }
        
        /* 卡片悬停效果 */
        .hover-effect {
            position: relative;
            overflow: hidden;
        }
        
        .hover-effect:after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 50% 50%, rgba(62, 120, 240, 0.1), transparent 70%);
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }
        
        .hover-effect:hover:after {
            opacity: 1;
        }
        
        /* 表格悬停行效果 */
        .el-table__body tr:hover > td {
            position: relative;
            background-color: rgba(62, 120, 240, 0.06) !important;
            transform: translateY(-1px);
            transition: all 0.3s ease !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }
        
        /* 空状态美化加强 */
        .el-empty__image {
            opacity: 0.8;
            transition: all 0.3s ease;
        }
        
        .el-empty:hover .el-empty__image {
            transform: scale(1.05);
            opacity: 1;
        }
        
        /* 标签美化加强 */
        .el-tag {
            position: relative;
            overflow: hidden;
        }
        
        .el-tag:after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }
        
        .el-tag:hover:after {
            transform: translateX(100%);
        }
        
        .header {
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-xl) var(--spacing-xl);
            background: linear-gradient(135deg, var(--bg-secondary) 0%, #f1f5f9 100%);
            box-shadow: var(--card-shadow);
            border: 1px solid var(--border-light);
            border-radius: var(--border-radius-lg);
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            overflow: hidden;
            transition: all var(--transition-speed) cubic-bezier(0.4, 0, 0.2, 1);
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
            border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
        }

        .header:hover {
            transform: translateY(-2px);
            box-shadow: var(--hover-shadow);
        }

        .header::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 300px;
            height: 300px;
            background: radial-gradient(circle, rgba(99, 102, 241, 0.05) 0%, transparent 70%);
            z-index: 0;
            border-radius: 50%;
            transform: translate(100px, -100px);
        }

        .header-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--text-color);
            margin: 0;
            position: relative;
            z-index: 1;
            letter-spacing: -0.025em;
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .header-title::before {
            content: '📋';
            font-size: 32px;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }
        
        .empty-tip {
            text-align: center;
            padding: 50px;
            color: var(--text-light);
            background-color: #fff;
            border-radius: 12px;
            box-shadow: var(--card-shadow);
            margin: 30px 0;
        }
        
        .back-button {
            position: fixed;
            bottom: var(--spacing-xl);
            left: var(--spacing-xl);
            z-index: 1000;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            color: white !important;
            border: none;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: 50px;
            cursor: pointer;
            font-size: 15px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            box-shadow: var(--card-shadow-lg);
            transition: all var(--transition-speed) cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .back-button:hover {
            background: linear-gradient(135deg, var(--primary-hover), var(--primary-color));
            box-shadow: var(--hover-shadow);
            transform: translateY(-3px) scale(1.02);
        }

        .back-button:active {
            transform: translateY(-1px) scale(0.98);
        }

        .back-button i {
            font-size: 18px;
            transition: transform var(--transition-speed) ease;
        }

        .back-button:hover i {
            transform: translateX(-2px);
        }
        
        .tabs-container {
            background: var(--bg-secondary);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--card-shadow);
            padding: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
            overflow: hidden;
            transition: all var(--transition-speed) cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid var(--border-light);
            position: relative;
        }

        .tabs-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
            border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
        }

        .tabs-container:hover {
            box-shadow: var(--card-shadow-lg);
            transform: translateY(-1px);
        }
        
        /* 现代化卡片样式 */
        .el-card {
            border: 1px solid var(--border-light) !important;
            border-radius: var(--border-radius-lg) !important;
            box-shadow: var(--card-shadow) !important;
            transition: all var(--transition-speed) cubic-bezier(0.4, 0, 0.2, 1) !important;
            margin-bottom: var(--spacing-lg) !important;
            overflow: hidden !important;
            animation: cardFadeIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            animation-fill-mode: both;
            background: var(--bg-secondary) !important;
            position: relative;
        }

        .el-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
            opacity: 0;
            transition: opacity var(--transition-speed) ease;
        }

        .el-card:hover::before {
            opacity: 1;
        }

        @keyframes cardFadeIn {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.98);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .request-card {
            animation-delay: calc(0.1s * var(--card-index, 0));
        }

        .session-card {
            animation-delay: calc(0.1s * var(--card-index, 0));
        }

        .el-card:hover {
            transform: translateY(-3px) scale(1.01);
            box-shadow: var(--hover-shadow) !important;
        }

        .el-card__header {
            padding: var(--spacing-lg) var(--spacing-xl) !important;
            border-bottom: 1px solid var(--border-light) !important;
            font-weight: 600 !important;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
            position: relative;
            overflow: hidden;
        }

        .el-card__header::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, rgba(99, 102, 241, 0.05) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }

        .el-card__body {
            padding: var(--spacing-xl) !important;
            position: relative;
        }
        
        /* 现代化标签页样式 */
        .el-tabs__nav-wrap::after {
            height: 2px !important;
            background: linear-gradient(90deg, var(--border-light), transparent) !important;
        }

        .el-tabs__item {
            font-size: 16px !important;
            font-weight: 500 !important;
            height: 56px !important;
            line-height: 56px !important;
            color: var(--text-secondary) !important;
            transition: all var(--transition-speed) cubic-bezier(0.4, 0, 0.2, 1) !important;
            margin-right: var(--spacing-md) !important;
            padding: 0 var(--spacing-lg) !important;
            border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
            position: relative;
            overflow: hidden;
        }

        .el-tabs__item::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
            transform: scaleX(0);
            transition: transform var(--transition-speed) cubic-bezier(0.4, 0, 0.2, 1);
        }

        .el-tabs__item:hover {
            color: var(--primary-color) !important;
            background: rgba(99, 102, 241, 0.05) !important;
        }

        .el-tabs__item.is-active {
            font-weight: 600 !important;
            color: var(--primary-color) !important;
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.05) 100%) !important;
        }

        .el-tabs__item.is-active::before {
            transform: scaleX(1);
        }
        
        /* 现代化按钮样式 */
        .el-button:not(.el-message-box__btns .el-button) {
            border-radius: var(--border-radius) !important;
            font-weight: 600 !important;
            transition: all var(--transition-speed) cubic-bezier(0.4, 0, 0.2, 1) !important;
            padding: var(--spacing-md) var(--spacing-lg) !important;
            color: #ffffff !important;
            box-shadow: var(--card-shadow) !important;
            border: none !important;
            position: relative;
            overflow: hidden;
        }

        .el-button:not(.is-disabled):hover {
            transform: translateY(-2px) scale(1.02) !important;
            box-shadow: var(--card-shadow-lg) !important;
        }

        .el-button:not(.is-disabled):active {
            transform: translateY(0) scale(0.98) !important;
        }

        .el-button--primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover)) !important;
        }

        .el-button--primary:hover {
            background: linear-gradient(135deg, var(--primary-hover), var(--primary-light)) !important;
        }

        .el-button--success {
            background: linear-gradient(135deg, var(--success-color), #34d399) !important;
        }

        .el-button--warning {
            background: linear-gradient(135deg, var(--warning-color), #fbbf24) !important;
        }

        .el-button--danger:not(.el-message-box__btns .el-button) {
            background: linear-gradient(135deg, var(--danger-color), #f87171) !important;
        }

        .el-button--info {
            background: linear-gradient(135deg, var(--border-light), #e5e7eb) !important;
            color: var(--text-secondary) !important;
        }

        .el-button--default {
            background: linear-gradient(135deg, var(--bg-secondary), var(--border-light)) !important;
            color: var(--text-secondary) !important;
            border: 1px solid var(--border-color) !important;
        }

        .el-button--text {
            background: none !important;
            box-shadow: none !important;
            color: var(--primary-color) !important;
            padding: var(--spacing-sm) var(--spacing-md) !important;
        }

        .el-button--text:hover {
            background: rgba(99, 102, 241, 0.1) !important;
            transform: none !important;
        }
        
        /* 对话框美化 */
        .el-dialog {
            border-radius: 14px !important;
            overflow: hidden !important;
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15) !important;
        }
        
        .el-dialog__header {
            padding: 24px !important;
            border-bottom: 1px solid rgba(0,0,0,0.05) !important;
            margin: 0 !important;
            background-color: rgba(245, 247, 250, 0.5) !important;
        }
        
        .el-dialog__title {
            font-weight: 600 !important;
            font-size: 20px !important;
            color: var(--text-color) !important;
        }
        
        .el-dialog__body {
            padding: 30px !important;
        }
        
        /* 分页美化 */
        .el-pagination {
            text-align: center !important;
            margin-top: 30px !important;
            padding: 20px 0 !important;
        }
        
        .el-pagination .el-pagination__jump,
        .el-pagination button,
        .el-pagination .el-pager li {
            background-color: #fff !important;
            border-radius: 6px !important;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05) !important;
            transition: all 0.3s ease !important;
        }
        
        .el-pagination .el-pager li:hover,
        .el-pagination button:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1) !important;
        }
        
        .el-pagination .el-pager li.active {
            background-color: var(--primary-color) !important;
            color: white !important;
            font-weight: 600 !important;
        }
        
        /* 输入框美化 */
        .el-input__inner {
            border-radius: 8px !important;
            padding: 12px 15px !important;
            height: auto !important;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05) inset !important;
            border: 1px solid rgba(0, 0, 0, 0.08) !important;
            transition: all 0.3s ease !important;
        }
        
        .el-input__inner:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(62, 120, 240, 0.15) !important;
        }
        
        .el-textarea__inner {
            border-radius: 8px !important;
            padding: 12px 15px !important;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05) inset !important;
            border: 1px solid rgba(0, 0, 0, 0.08) !important;
            transition: all 0.3s ease !important;
        }
        
        .el-textarea__inner:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(62, 120, 240, 0.15) !important;
        }
        
        /* 加载态美化 */
        .skeleton-loading {
            animation: skeleton-loading 1.5s ease-in-out infinite;
            background: linear-gradient(90deg, #f0f2f5 25%, #e6e8eb 50%, #f0f2f5 75%) !important;
            background-size: 200% 100% !important;
            border-radius: 12px !important;
        }
        
        @keyframes skeleton-loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
        
        /* 布局优化 */
        .action-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 1px dashed rgba(0,0,0,0.08);
        }
        
        .info-row {
            margin-bottom: 15px;
            display: flex;
            flex-wrap: wrap;
            padding: 8px 0;
        }
        
        .info-label {
            font-weight: 600;
            margin-right: 15px;
            color: var(--text-color);
            min-width: 90px;
        }
        
        .info-value {
            color: var(--text-secondary);
            flex: 1;
        }
        
        /* 现代化状态标签样式 */
        .status-tag {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            margin-right: var(--spacing-sm);
            position: relative;
            overflow: hidden;
            transition: all var(--transition-speed) ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-tag::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.2) 0%, transparent 50%, rgba(255,255,255,0.2) 100%);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }

        .status-tag:hover::before {
            transform: translateX(100%);
        }

        .tag-pending {
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.15) 0%, rgba(251, 191, 36, 0.1) 100%);
            color: #92400e !important;
            border: 1px solid rgba(245, 158, 11, 0.3);
            box-shadow: 0 2px 4px rgba(245, 158, 11, 0.1);
        }

        .tag-pending::after {
            content: '⏳';
            margin-left: var(--spacing-xs);
        }

        .tag-approved {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.15) 0%, rgba(52, 211, 153, 0.1) 100%);
            color: #065f46 !important;
            border: 1px solid rgba(16, 185, 129, 0.3);
            box-shadow: 0 2px 4px rgba(16, 185, 129, 0.1);
        }

        .tag-approved::after {
            content: '✅';
            margin-left: var(--spacing-xs);
        }

        .tag-rejected {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.15) 0%, rgba(248, 113, 113, 0.1) 100%);
            color: #991b1b !important;
            border: 1px solid rgba(239, 68, 68, 0.3);
            box-shadow: 0 2px 4px rgba(239, 68, 68, 0.1);
        }

        .tag-rejected::after {
            content: '❌';
            margin-left: var(--spacing-xs);
        }

        .tag-success {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.15) 0%, rgba(52, 211, 153, 0.1) 100%);
            color: #065f46 !important;
            border: 1px solid rgba(16, 185, 129, 0.3);
            box-shadow: 0 2px 4px rgba(16, 185, 129, 0.1);
        }

        .tag-success::after {
            content: '🎉';
            margin-left: var(--spacing-xs);
        }
        
        /* 现代化移动端响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: var(--spacing-md);
                margin: var(--spacing-md);
                width: auto;
                border-radius: var(--border-radius-lg);
                background: var(--bg-secondary);
                box-shadow: var(--card-shadow-lg);
            }

            .header {
                padding: var(--spacing-lg) var(--spacing-md);
                flex-direction: column;
                align-items: stretch;
                gap: var(--spacing-md);
                border-radius: var(--border-radius-lg);
                background: linear-gradient(135deg, var(--bg-secondary) 0%, #f8fafc 100%);
                border: 1px solid var(--border-light);
                box-shadow: var(--card-shadow);
                position: relative;
                overflow: hidden;
            }

            .header::before {
                height: 3px;
                background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
            }

            .header-title {
                font-size: 24px;
                margin-bottom: 0;
                text-align: center;
                font-weight: 700;
                color: var(--text-color);
                letter-spacing: -0.025em;
                position: relative;
                z-index: 1;
            }

            .header .button-group {
                width: 100%;
                flex-direction: column;
                gap: 12px;
            }

            .header .el-button {
                width: 100%;
                justify-content: center;
                padding: 14px 20px !important;
                font-size: 15px !important;
                font-weight: 600 !important;
                border-radius: 12px !important;
                box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1) !important;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            }

            .tabs-container {
                padding: var(--spacing-md);
                border-radius: var(--border-radius-lg);
                background: var(--bg-secondary);
                border: 1px solid var(--border-light);
                box-shadow: var(--card-shadow);
                margin-bottom: var(--spacing-lg);
                position: relative;
            }

            .tabs-container::before {
                height: 2px;
                background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
            }

            .el-tabs__item {
                font-size: 15px !important;
                height: 54px !important;
                line-height: 54px !important;
                padding: 0 var(--spacing-lg) !important;
                border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
                margin-right: var(--spacing-sm) !important;
                font-weight: 600 !important;
                transition: all var(--transition-speed) cubic-bezier(0.4, 0, 0.2, 1) !important;
                position: relative;
                overflow: hidden;
            }

            .el-tabs__item.is-active {
                background: linear-gradient(135deg, var(--primary-color), var(--primary-hover)) !important;
                color: white !important;
                border-radius: var(--border-radius) !important;
                transform: translateY(-1px);
                box-shadow: var(--card-shadow-lg);
            }

            .el-tabs__item.is-active::before {
                transform: scaleX(1);
            }

            .el-card__header,
            .el-card__body {
                padding: 18px !important;
            }

            .el-card {
                border-radius: var(--border-radius-lg) !important;
                margin-bottom: var(--spacing-md) !important;
                box-shadow: var(--card-shadow) !important;
                border: 1px solid var(--border-light) !important;
                overflow: hidden !important;
                background: var(--bg-secondary) !important;
                position: relative;
            }

            .el-card::before {
                height: 3px;
                background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
                opacity: 1;
            }

            .action-row {
                flex-direction: column;
                align-items: stretch;
                gap: var(--spacing-md);
                padding: var(--spacing-md);
                background: var(--bg-secondary);
                border: 1px solid var(--border-light);
                border-radius: var(--border-radius);
                margin-bottom: var(--spacing-md);
                box-shadow: var(--card-shadow);
            }

            .action-row .button-group {
                flex-direction: column;
                gap: 10px;
            }

            .action-row .el-button {
                width: 100%;
                margin: 0;
                padding: 14px 20px !important;
                font-size: 15px !important;
                font-weight: 600 !important;
                justify-content: center;
                border-radius: 12px !important;
                box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1) !important;
            }

            .back-button {
                bottom: var(--spacing-lg);
                left: var(--spacing-lg);
                padding: var(--spacing-md) var(--spacing-xl);
                font-size: 15px;
                border-radius: 30px;
                background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
                box-shadow: var(--card-shadow-lg);
                backdrop-filter: blur(10px);
                font-weight: 600;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }

            .back-button:hover {
                transform: translateY(-2px) scale(1.02);
                box-shadow: var(--hover-shadow);
            }

            .back-button:active {
                transform: translateY(0) scale(0.98);
            }

            .back-button i {
                font-size: 18px;
                transition: transform var(--transition-speed) ease;
            }

            .back-button:hover i {
                transform: translateX(-2px);
            }

            /* 卡片内按钮组优化 */
            .el-card__header {
                background: #ffffff !important;
                border: 1px solid #e0e0e0 !important;
                border-bottom: 1px solid rgba(0, 0, 0, 0.06) !important;
            }

            .el-card__header .button-group {
                flex-direction: column;
                gap: 10px;
                width: 100%;
                margin-top: 12px;
            }

            .el-card__header .el-button {
                width: 100%;
                margin: 0;
                padding: 12px 18px !important;
                font-size: 15px !important;
                font-weight: 600 !important;
                border-radius: 10px !important;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
            }

            /* 信息行优化 */
            .info-row {
                flex-direction: row;
                align-items: flex-start;
                gap: 12px;
                padding: 12px 16px;
                background: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 10px;
                margin-bottom: 8px;
                border-left: 3px solid var(--primary-color);
            }

            .info-label {
                min-width: auto;
                font-size: 14px;
                font-weight: 700;
                color: var(--primary-color);
                display: flex;
                align-items: center;
                min-width: 90px;
            }

            .info-label i {
                margin-right: 6px;
                font-size: 16px;
            }

            .info-value {
                font-size: 14px;
                padding-left: 0;
                flex: 1;
                color: var(--text-color);
                font-weight: 500;
                line-height: 1.4;
            }

            .info-text {
                font-size: 12px;
                padding-left: 0;
                color: var(--text-light);
                font-style: italic;
            }

            /* 状态标签优化 */
            .status-tag {
                font-size: 13px;
                padding: 10px 16px;
                margin-bottom: 12px;
                border-radius: 20px;
                font-weight: 600;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                display: inline-flex;
                align-items: center;
                gap: 6px;
            }

            /* 表格隐藏，使用卡片布局 */
            .el-table {
                display: none;
            }

            .mobile-card-list {
                display: block;
            }
        }

        /* 响应式调整 - 平板端 */
        @media (min-width: 769px) and (max-width: 1024px) {
            .container {
                padding: 20px;
                margin: 20px auto;
                max-width: 900px;
            }

            .header {
                padding: 20px 25px;
                flex-direction: row;
                flex-wrap: wrap;
                gap: 15px;
            }

            .header-title {
                font-size: 22px;
                flex: 1;
                min-width: 200px;
            }

            .header .button-group {
                flex-direction: row;
                flex-wrap: wrap;
                gap: 10px;
            }

            .tabs-container {
                padding: 20px;
            }

            .el-tabs__item {
                font-size: 15px !important;
                height: 50px !important;
                line-height: 50px !important;
                padding: 0 20px !important;
            }

            .action-row {
                flex-direction: row;
                flex-wrap: wrap;
                align-items: center;
                gap: 15px;
            }

            .action-row .button-group {
                flex-direction: row;
                flex-wrap: wrap;
                gap: 10px;
            }

            .el-card__header .button-group {
                flex-direction: row;
                gap: 10px;
                margin-top: 0;
            }

            .info-row {
                flex-direction: row;
                align-items: center;
            }

            .info-label {
                min-width: 120px;
            }

            /* 表格列宽调整 */
            .el-table .el-table__cell {
                padding: 12px 8px !important;
            }

            .back-button {
                bottom: 25px;
                left: 25px;
            }
        }

        /* 响应式调整 - 大屏电脑端 */
        @media (min-width: 1025px) {
            .container {
                max-width: 1200px;
                margin: 30px auto;
                padding: 25px;
            }

            .header {
                padding: 25px 30px;
            }

            .header-title {
                font-size: 24px;
            }

            .tabs-container {
                padding: 25px;
            }

            .el-tabs__item {
                font-size: 16px !important;
                height: 54px !important;
                line-height: 54px !important;
            }

            .back-button {
                bottom: 30px;
                left: 30px;
            }

            /* 桌面端悬停效果增强 */
            .el-card:hover {
                transform: translateY(-6px);
                box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
            }

            .el-button:hover {
                transform: translateY(-2px);
            }
        }

        /* 移动端专用样式 */
        .mobile-card-list {
            display: none;
        }

        @media (max-width: 768px) {
            .mobile-card-list {
                display: block;
            }

            .mobile-processed-card {
                background: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 16px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
                margin-bottom: 16px;
                padding: 18px;
                border-left: 4px solid var(--success-color);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                position: relative;
                overflow: hidden;
            }

            .mobile-processed-card::before {
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                width: 100px;
                height: 100px;
                background: radial-gradient(circle, rgba(103, 194, 58, 0.1) 0%, transparent 70%);
                border-radius: 50%;
                transform: translate(30px, -30px);
            }

            .mobile-processed-card:hover {
                transform: translateY(-4px);
                box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
            }

            .mobile-card-header {
                display: flex;
                align-items: flex-start;
                justify-content: space-between;
                margin-bottom: 16px;
                padding-bottom: 12px;
                border-bottom: 2px dashed rgba(0,0,0,0.08);
                position: relative;
                z-index: 1;
            }

            .mobile-card-title {
                display: flex;
                align-items: center;
                flex: 1;
                font-weight: 700;
                font-size: 16px;
                color: var(--text-color);
                line-height: 1.3;
            }

            .mobile-card-title i {
                margin-right: 10px;
                color: var(--primary-color);
                font-size: 20px;
                background: rgba(62, 120, 240, 0.1);
                padding: 6px;
                border-radius: 8px;
            }

            .mobile-card-status {
                margin-left: 12px;
            }

            .mobile-card-body {
                margin-bottom: 16px;
                position: relative;
                z-index: 1;
            }

            .mobile-card-info {
                display: flex;
                align-items: center;
                margin-bottom: 10px;
                font-size: 14px;
                padding: 8px 12px;
                background: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                border-left: 3px solid var(--primary-color);
            }

            .mobile-card-info i {
                margin-right: 10px;
                color: var(--primary-color);
                width: 18px;
                text-align: center;
                font-size: 16px;
            }

            .mobile-card-label {
                font-weight: 600;
                margin-right: 10px;
                min-width: 80px;
                color: var(--primary-color);
            }

            .mobile-card-actions {
                display: flex;
                gap: 10px;
                flex-direction: column;
                position: relative;
                z-index: 1;
            }

            .mobile-card-actions .el-button {
                width: 100%;
                margin: 0;
                padding: 12px 18px !important;
                font-size: 15px !important;
                font-weight: 600 !important;
                justify-content: center;
                border-radius: 10px !important;
                box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1) !important;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            }

            /* 对话框移动端优化 */
            .el-dialog {
                width: 95% !important;
                margin: 10px auto !important;
                max-height: 90vh !important;
                border-radius: 20px !important;
                box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15) !important;
                backdrop-filter: blur(10px);
            }

            .el-dialog__header {
                padding: 24px 20px 16px 20px !important;
                background: #ffffff !important;
                border: 1px solid #e0e0e0 !important;
                border-radius: 20px 20px 0 0 !important;
                border-bottom: 2px solid rgba(0, 0, 0, 0.06) !important;
            }

            .el-dialog__title {
                font-size: 20px !important;
                font-weight: 700 !important;
                color: var(--text-color) !important;
                text-align: center;
            }

            .el-dialog__body {
                padding: 20px !important;
                max-height: 60vh;
                overflow-y: auto;
                -webkit-overflow-scrolling: touch;
            }

            .el-dialog__footer {
                padding: 16px 20px 20px 20px !important;
                background: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 0 0 20px 20px;
            }

            .dialog-footer {
                display: flex;
                gap: 12px;
            }

            .dialog-footer .el-button {
                flex: 1;
                margin: 0;
                padding: 14px 20px !important;
                font-size: 15px !important;
                font-weight: 600 !important;
                border-radius: 12px !important;
                box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1) !important;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            }

            /* 取消按钮样式优化 */
            .dialog-footer .el-button:not(.el-button--primary) {
                background: #ffffff !important;
                border: 2px solid #e0e0e0 !important;
                color: #000000 !important;
                font-weight: 600 !important;
            }

            .dialog-footer .el-button:not(.el-button--primary):hover {
                background: #f8f9fa !important;
                border-color: #adb5bd !important;
                color: #000000 !important;
                transform: translateY(-2px) !important;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15) !important;
            }

            .dialog-footer .el-button:not(.el-button--primary):active {
                transform: translateY(0) !important;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
            }

            /* 确认按钮样式增强 */
            .dialog-footer .el-button--primary {
                background: linear-gradient(135deg, var(--primary-color) 0%, #5a90ff 100%) !important;
                border: 2px solid var(--primary-color) !important;
                box-shadow: 0 4px 15px rgba(62, 120, 240, 0.3) !important;
            }

            .dialog-footer .el-button--primary:hover {
                background: linear-gradient(135deg, #5a90ff 0%, var(--primary-color) 100%) !important;
                transform: translateY(-2px) !important;
                box-shadow: 0 6px 20px rgba(62, 120, 240, 0.4) !important;
            }

            .dialog-footer .el-button--primary:active {
                transform: translateY(0) !important;
                box-shadow: 0 3px 12px rgba(62, 120, 240, 0.3) !important;
            }

            /* 确保所有对话框取消按钮都是黑色字体 */
            .el-dialog .el-button:not(.el-button--primary):not(.el-button--success):not(.el-button--warning):not(.el-button--danger):not(.el-button--info) {
                background: #ffffff !important;
                border: 2px solid #e0e0e0 !important;
                color: #000000 !important;
                font-weight: 600 !important;
            }

            .el-dialog .el-button:not(.el-button--primary):not(.el-button--success):not(.el-button--warning):not(.el-button--danger):not(.el-button--info):hover {
                background: #f8f9fa !important;
                border-color: #adb5bd !important;
                color: #000000 !important;
            }

            .el-dialog .el-button:not(.el-button--primary):not(.el-button--success):not(.el-button--warning):not(.el-button--danger):not(.el-button--info):active {
                color: #000000 !important;
            }

            /* 表单优化 */
            .settings-form {
                padding: 5px;
            }

            .el-form-item__label {
                font-size: 14px !important;
                line-height: 1.4 !important;
            }

            .el-input__inner,
            .el-select .el-input__inner {
                font-size: 16px !important;
                padding: 12px 15px !important;
            }

            .el-input-number {
                width: 100% !important;
            }

            .el-input-number .el-input__inner {
                text-align: left !important;
            }

            /* 分页器移动端优化 */
            .el-pagination {
                padding: 20px 0 !important;
                background: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 16px;
                margin-top: 16px;
            }

            .el-pagination .el-pager,
            .el-pagination .btn-prev,
            .el-pagination .btn-next {
                font-size: 15px !important;
                font-weight: 600 !important;
            }

            .el-pagination .el-pager li {
                min-width: 40px !important;
                height: 40px !important;
                line-height: 40px !important;
                border-radius: 10px !important;
                margin: 0 4px !important;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
            }

            .el-pagination .btn-prev,
            .el-pagination .btn-next {
                border-radius: 10px !important;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
            }

            /* 空状态优化 */
            .el-empty {
                padding: 60px 20px !important;
                background: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 16px;
                margin: 16px 0;
            }

            .el-empty__image {
                width: 140px !important;
                height: 140px !important;
                opacity: 0.8;
            }

            .el-empty__description {
                font-size: 16px !important;
                padding: 0 15px;
                color: var(--text-secondary) !important;
                font-weight: 500;
                line-height: 1.5;
            }

            /* 触摸反馈 */
            .touch-active {
                background-color: rgba(0, 0, 0, 0.05) !important;
                transform: scale(0.98) !important;
                transition: all 0.1s ease !important;
            }

            /* 移动端按钮触摸优化 */
            .el-button {
                -webkit-tap-highlight-color: transparent;
                -webkit-touch-callout: none;
                -webkit-user-select: none;
                user-select: none;
            }

            /* 移动端卡片触摸优化 */
            .mobile-processed-card,
            .el-card {
                -webkit-tap-highlight-color: transparent;
                -webkit-touch-callout: none;
            }

            /* 移动端表单元素优化 */
            .el-input__inner,
            .el-textarea__inner,
            .el-select .el-input__inner {
                -webkit-appearance: none;
                -webkit-tap-highlight-color: transparent;
            }

            /* 移动端滚动优化 */
            .el-dialog__body,
            .tabs-container {
                -webkit-overflow-scrolling: touch;
            }

            /* 移动端安全区域适配 */
            .back-button {
                bottom: calc(15px + env(safe-area-inset-bottom));
                left: calc(15px + env(safe-area-inset-left));
            }

            .container {
                padding-bottom: calc(20px + env(safe-area-inset-bottom));
            }

            /* 移动端字体大小优化 */
            .header-title {
                font-size: 22px !important;
                line-height: 1.2 !important;
            }

            .el-card__header {
                font-size: 16px !important;
                line-height: 1.4 !important;
            }

            .info-label {
                font-size: 14px !important;
                line-height: 1.3 !important;
            }

            .info-value {
                font-size: 14px !important;
                line-height: 1.4 !important;
            }

            /* 移动端间距优化 */
            .el-card {
                margin-bottom: 16px !important;
            }

            .info-row {
                margin-bottom: 8px !important;
                padding: 12px 16px !important;
            }

            /* 移动端加载状态优化 */
            .skeleton-loading {
                min-height: 240px;
                border-radius: 16px;
                background: #ffffff;
                border: 1px solid #e0e0e0;
            }

            /* 移动端分页器简化 */
            .el-pagination .el-pagination__sizes,
            .el-pagination .el-pagination__total,
            .el-pagination .el-pagination__jump {
                display: none !important;
            }

            /* 移动端标签简化 */
            .el-tag {
                font-size: 12px !important;
                padding: 0 12px !important;
                height: 28px !important;
                line-height: 26px !important;
                border-radius: 14px !important;
                font-weight: 600 !important;
            }

            /* 移动端倒计时文字优化 */
            .countdown-critical {
                font-size: 14px !important;
                font-weight: 700 !important;
                background: linear-gradient(135deg, #f56c6c, #ff8a8a);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                animation: pulse 1.5s infinite;
            }

            /* 移动端表单优化 */
            .settings-form .el-form-item__label {
                font-size: 15px !important;
                font-weight: 600 !important;
                color: var(--text-color) !important;
            }

            .settings-form .el-input__inner,
            .settings-form .el-select .el-input__inner {
                font-size: 16px !important;
                padding: 14px 16px !important;
                border-radius: 12px !important;
                border: 2px solid rgba(0, 0, 0, 0.08) !important;
            }

            .settings-form .el-input__inner:focus,
            .settings-form .el-select .el-input__inner:focus {
                border-color: var(--primary-color) !important;
                box-shadow: 0 0 0 4px rgba(62, 120, 240, 0.15) !important;
            }

            /* 移动端卡片标题区域美化 */
            .el-card__header {
                position: relative;
                overflow: hidden;
            }

            .el-card__header::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 2px;
                background: linear-gradient(90deg, var(--primary-color), #5a90ff);
            }

            /* 移动端按钮悬停效果增强 */
            .el-button:active {
                transform: scale(0.98) !important;
                transition: transform 0.1s ease !important;
            }

            /* 全局取消按钮样式统一 */
            .el-button:not(.el-button--primary):not(.el-button--success):not(.el-button--warning):not(.el-button--danger):not(.el-button--info) {
                background: #ffffff !important;
                border: 2px solid #e0e0e0 !important;
                color: #000000 !important;
                font-weight: 600 !important;
            }

            .el-button:not(.el-button--primary):not(.el-button--success):not(.el-button--warning):not(.el-button--danger):not(.el-button--info):hover {
                background: #f8f9fa !important;
                border-color: #adb5bd !important;
                color: #000000 !important;
                transform: translateY(-1px) !important;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12) !important;
            }

            /* 移动端卡片内容区域美化 */
            .el-card__body {
                background: #ffffff;
                border: 1px solid #e0e0e0;
                position: relative;
            }

            /* 移动端时间转换提示美化 */
            .time-conversion {
                background: linear-gradient(135deg, rgba(62, 120, 240, 0.1) 0%, rgba(90, 144, 255, 0.1) 100%) !important;
                border: 1px solid rgba(62, 120, 240, 0.2) !important;
                border-radius: 12px !important;
                padding: 16px 20px !important;
                margin-top: 16px !important;
                font-size: 15px !important;
                line-height: 1.5 !important;
            }

            .time-conversion strong {
                color: var(--primary-color) !important;
                font-weight: 700 !important;
            }

            /* 移动端提示文本美化 */
            .hint-text {
                color: var(--text-light) !important;
                font-size: 14px !important;
                padding: 12px 16px !important;
                background: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 10px;
                border-left: 3px solid var(--warning-color);
                margin: 12px 0;
                line-height: 1.4;
            }

            /* 移动端页面背景优化 */
            body.mobile {
                background: #ffffff;
                background-attachment: fixed;
            }

            /* 移动端滚动条隐藏 */
            .el-dialog__body::-webkit-scrollbar {
                width: 4px;
            }

            .el-dialog__body::-webkit-scrollbar-thumb {
                background-color: rgba(0, 0, 0, 0.2);
                border-radius: 2px;
            }
        }
        
        /* 动态提示标签 */
        .badge-dot {
            position: relative;
        }
        
        .badge-dot:after {
            content: '';
            position: absolute;
            top: 2px;
            right: -6px;
            width: 10px;
            height: 10px;
            background-color: var(--danger-color);
            border-radius: 50%;
            box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.8);
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(0.8); opacity: 0.8; }
            50% { transform: scale(1.2); opacity: 1; }
            100% { transform: scale(0.8); opacity: 0.8; }
        }
        
        /* 按钮脉冲效果 */
        .btn-pulse {
            animation: btnPulse 2s infinite;
        }
        
        @keyframes btnPulse {
            0% { box-shadow: 0 0 0 0 rgba(62, 120, 240, 0.4); }
            70% { box-shadow: 0 0 0 12px rgba(62, 120, 240, 0); }
            100% { box-shadow: 0 0 0 0 rgba(62, 120, 240, 0); }
        }
        
        /* 标签页下划线动画 */
        .el-tabs__active-bar {
            transition: all 0.4s cubic-bezier(0.645, 0.045, 0.355, 1) !important;
            height: 3px !important;
            border-radius: 3px !important;
        }
        
        .el-tabs__item.is-active {
            position: relative;
            overflow: hidden;
        }
        
        .el-tabs__item.is-active:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 40px;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), #5a90ff);
            border-radius: 3px;
            transform: translateX(-50%);
            animation: tabActive 0.4s forwards;
        }
        
        @keyframes tabActive {
            from { width: 0; opacity: 0; }
            to { width: 40px; opacity: 1; }
        }
        
        /* 滚动条美化 */
        ::-webkit-scrollbar {
            width: 10px;
            height: 10px;
        }
        
        ::-webkit-scrollbar-track {
            background-color: rgba(0, 0, 0, 0.03);
            border-radius: 6px;
        }
        
        ::-webkit-scrollbar-thumb {
            background-color: rgba(144, 147, 153, 0.3);
            border-radius: 6px;
            border: 3px solid rgba(255, 255, 255, 0.1);
            background-clip: padding-box;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background-color: rgba(144, 147, 153, 0.5);
        }
        
        /* 提示气泡 */
        .tooltip {
            position: relative;
            cursor: help;
        }
        
        .tooltip:hover:before {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            margin-bottom: 8px;
            padding: 8px 14px;
            background-color: rgba(40, 40, 40, 0.9);
            color: white;
            border-radius: 8px;
            font-size: 13px;
            white-space: nowrap;
            z-index: 10;
            pointer-events: none;
            opacity: 0;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            animation: fadeIn 0.3s forwards;
        }
        
        .tooltip:hover:after {
            content: '';
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 6px solid transparent;
            border-top-color: rgba(40, 40, 40, 0.9);
            z-index: 10;
            pointer-events: none;
            opacity: 0;
            animation: fadeIn 0.3s forwards;
        }
        
        /* 倒计时样式 */
        .countdown-critical {
            color: #f56c6c;
            font-weight: bold;
            animation: pulse 1.5s infinite;
        }
        
        /* 表格美化 */
        .el-table {
            border-radius: 12px !important;
            overflow: hidden !important;
            box-shadow: var(--card-shadow) !important;
            border: none !important;
        }
        
        .el-table th {
            background-color: rgba(245, 247, 250, 0.7) !important;
            padding: 15px 0 !important;
            font-weight: 600 !important;
            color: var(--text-color) !important;
        }
        
        .el-table td {
            padding: 16px 0 !important;
            color: var(--text-secondary) !important;
        }
        
        .el-table--enable-row-hover .el-table__body tr:hover>td {
            background-color: rgba(62, 120, 240, 0.05) !important;
        }
        
        /* 标签美化 */
        .el-tag {
            border-radius: 20px !important;
            padding: 0 12px !important;
            height: 28px !important;
            line-height: 26px !important;
            font-weight: 500 !important;
            border: none !important;
        }
        
        .el-tag--success {
            background-color: rgba(103, 194, 58, 0.15) !important;
            color: #3c8618 !important;
        }
        
        .el-tag--danger {
            background-color: rgba(245, 108, 108, 0.15) !important;
            color: #cf1322 !important;
        }
        
        /* 按钮组美化 */
        .button-group {
            display: flex;
            gap: 10px;
        }
        
        /* 空状态美化 */
        .el-empty {
            padding: 30px !important;
        }
        
        .el-empty__description {
            margin-top: 15px !important;
            font-size: 15px !important;
            color: var(--text-secondary) !important;
        }
        
        /* 确保页面上所有文本可见 */
        .el-empty__description p {
            color: var(--text-secondary) !important;
            margin: 8px 0 !important;
        }
        
        /* 设置表单样式 */
        .settings-form {
            padding: 10px;
        }
        
        .hint-text {
            color: var(--text-secondary);
            font-size: 13px;
            padding: 5px 0;
        }
        
        .custom-dialog .el-dialog__body {
            padding: 20px 30px !important;
        }
        
        /* 提示文本样式 */
        .time-conversion {
            background: rgba(62, 120, 240, 0.1);
            padding: 10px 15px;
            border-radius: 8px;
            margin-top: 15px;
            font-size: 14px;
            color: var(--text-color);
        }
        
        .time-conversion strong {
            color: var(--primary-color);
        }
        
        .info-text {
            margin-left: 8px;
            color: var(--text-secondary);
            font-size: 13px;
        }
    </style>
</head>
<body>
    <div id="app" v-cloak>
        <a href="/plugin/Customersystem/user/index" class="back-button">
            <i class="el-icon-arrow-left"></i>返回聊天列表
        </a>
        
        <div class="container">
            <div class="header">
                <div class="header-title">商家邀请管理</div>
                <div>
                    <el-button type="danger" size="small" plain @click="clearAllProcessedCache" style="margin-right: 10px;">
                        <i class="el-icon-delete"></i> 批量清除记录
                    </el-button>
                    <el-button type="primary" size="small" plain :loading="loading" @click="clearProcessedCache">
                        <i class="el-icon-refresh"></i> 刷新邀请列表
                    </el-button>
                </div>
            </div>

            <!-- 邀请过期时间设置对话框 -->
            <el-dialog
                title="邀请过期时间设置"
                v-model="invitationSettingsDialogVisible"
                width="500px"
                :before-close="handleCloseSettingsDialog"
                custom-class="custom-dialog">
                <div class="settings-form">
                    <el-form :model="invitationSettings" label-width="120px">
                        <el-form-item label="过期时间值">
                            <el-input-number v-model="invitationSettings.value" :min="1" :max="999" style="width: 100%;"></el-input-number>
                        </el-form-item>
                        <el-form-item label="时间单位">
                            <el-select v-model="invitationSettings.unit" style="width: 100%;">
                                <el-option label="分钟" value="minute"></el-option>
                                <el-option label="小时" value="hour"></el-option>
                                <el-option label="天" value="day"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                                                                 <div class="hint-text">超过此时间未处理的邀请请求将自动关闭</div>
                            <div class="time-conversion" v-if="invitationSettings.value > 0">
                                <template v-if="invitationSettings.unit === 'minute'">
                                    <strong>{{ invitationSettings.value }}</strong> 分钟 = <strong>{{ (invitationSettings.value / 60).toFixed(1) }}</strong> 小时 = <strong>{{ (invitationSettings.value / 1440).toFixed(2) }}</strong> 天
                                </template>
                                <template v-else-if="invitationSettings.unit === 'hour'">
                                    <strong>{{ invitationSettings.value }}</strong> 小时 = <strong>{{ (invitationSettings.value * 60).toFixed(0) }}</strong> 分钟 = <strong>{{ (invitationSettings.value / 24).toFixed(2) }}</strong> 天
                                </template>
                                <template v-else-if="invitationSettings.unit === 'day'">
                                    <strong>{{ invitationSettings.value }}</strong> 天 = <strong>{{ (invitationSettings.value * 24).toFixed(0) }}</strong> 小时 = <strong>{{ (invitationSettings.value * 1440).toFixed(0) }}</strong> 分钟
                                </template>
                            </div>
                        </el-form-item>
                    </el-form>
                </div>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="handleCloseSettingsDialog">取消</el-button>
                        <el-button type="primary" @click="saveInvitationSettings" :loading="saveSettingsLoading">保存设置</el-button>
                    </span>
                </template>
            </el-dialog>

            <div class="tabs-container">
                <el-tabs v-model="activeTab" @tab-click="handleTabChange">
                    <el-tab-pane label="待处理邀请请求" name="pending">
                        <div class="action-row">
                            <span v-if="requests.length > 0" class="status-tag tag-pending badge-dot">
                                <i class="el-icon-time" style="margin-right: 5px;"></i>
                                待处理商家邀请: {{ total }} 条
                            </span>
                            <span v-else></span>
                            <div class="button-group">
                                <el-button type="primary" size="small" @click="loadPendingRequests(1)" class="btn-pulse">
                                    <i class="el-icon-refresh"></i> 刷新列表
                                </el-button>
                                <el-button type="warning" size="small" @click="checkExpiredInvitations" :loading="checkingExpired">
                                    <i class="el-icon-time"></i> 检查过期邀请
                                </el-button>
                                <el-button type="success" size="small" @click="showInvitationSettingsDialog">
                                    <i class="el-icon-setting"></i> 过期时间设置
                                </el-button>
                            </div>
                        </div>
                        
                        <el-card v-if="loading" class="skeleton-loading" shadow="never">
                            <el-skeleton :rows="5" animated />
                        </el-card>
                        
                        <div v-else>
                            <el-empty v-if="requests.length === 0" description="暂无待处理的商家邀请" :image-size="180">
                                <span style="color: #606266; font-size: 15px;">当前没有需要处理的商家邀请请求</span>
                            </el-empty>
                            
                            <el-card v-for="(request, index) in requests" :key="request.id" class="request-card" :style="{'--card-index': index}">
                                <template #header>
                                    <div style="display: flex; align-items: center; justify-content: space-between;">
                                        <div style="display: flex; align-items: center;">
                                            <i class="el-icon-chat-dot-square" style="margin-right: 12px; color: var(--primary-color); font-size: 22px;"></i>
                                            <span style="font-weight: bold; font-size: 16px;">{{ request.session_title }}</span>
                                        </div>
                                        <div class="button-group">
                                            <el-button type="primary" size="small" @click="handleRequest(request.id, 'approve')">
                                                <i class="el-icon-check" style="margin-right: 5px;"></i>同意
                                            </el-button>
                                            <el-button type="danger" size="small" @click="confirmReject(request.id)">
                                                <i class="el-icon-close" style="margin-right: 5px;"></i>拒绝
                                            </el-button>
                                        </div>
                                    </div>
                                </template>
                                
                                <div>
                                    <div class="info-row">
                                        <span class="info-label"><i class="el-icon-user" style="margin-right: 5px; color: var(--primary-color);"></i>邀请你的商家:</span>
                                        <span class="info-value">{{ request.requester_name }}</span>
                                    </div>
                                    <div class="info-row">
                                        <span class="info-label"><i class="el-icon-document" style="margin-right: 5px; color: var(--primary-color);"></i>邀请原因:</span>
                                        <span class="info-value">{{ request.reason || '无' }}</span>
                                    </div>
                                    <div class="info-row">
                                        <span class="info-label"><i class="el-icon-date" style="margin-right: 5px; color: var(--primary-color);"></i>邀请时间:</span>
                                        <span class="info-value">{{ formatTime(request.create_time) }}</span>
                                    </div>
                                    <div class="info-row">
                                        <span class="info-label"><i class="el-icon-time" style="margin-right: 5px; color: var(--warning-color);"></i>过期时间:</span>
                                        <span class="info-value">{{ formatTime(request.create_time + invitationExpiryHours * 3600) }}</span>
                                        <span class="info-text" v-if="invitationSettings.unit === 'minute'">({{ invitationSettings.value }}分钟后过期)</span>
                                        <span class="info-text" v-else-if="invitationSettings.unit === 'hour'">({{ invitationSettings.value }}小时后过期)</span>
                                        <span class="info-text" v-else-if="invitationSettings.unit === 'day'">({{ invitationSettings.value }}天后过期)</span>
                                    </div>
                                    <div class="info-row">
                                        <span class="info-label"><i class="el-icon-alarm-clock" style="margin-right: 5px; color: var(--primary-color);"></i>剩余时间:</span>
                                        <span class="info-value">
                                            <span v-if="getRemainingTime(request.create_time).days > 0 || getRemainingTime(request.create_time).hours > 0" 
                                                  :class="{'countdown-critical': getRemainingTime(request.create_time).days === 0 && getRemainingTime(request.create_time).hours < 3}">
                                                {{ getRemainingTime(request.create_time).days > 0 ? getRemainingTime(request.create_time).days + ' 天 ' : '' }}
                                                {{ getRemainingTime(request.create_time).hours }} 小时 
                                                {{ getRemainingTime(request.create_time).minutes }} 分钟
                                            </span>
                                            <span v-else class="countdown-critical">
                                                {{ getRemainingTime(request.create_time).minutes }} 分钟 
                                                {{ getRemainingTime(request.create_time).seconds }} 秒
                                            </span>
                                        </span>
                                    </div>
                                </div>
                            </el-card>
                            
                            <el-pagination
                                v-if="total > 0"
                                background
                                layout="prev, pager, next"
                                :total="total"
                                :page-size="limit"
                                :current-page="currentPage"
                                @current-change="handlePageChange"
                            >
                            </el-pagination>
                        </div>
                    </el-tab-pane>
                    
                    <el-tab-pane label="已处理邀请请求" name="processed">
                        <div class="action-row">
                            <span v-if="processedRequests.length > 0" class="status-tag tag-success">
                                <i class="el-icon-checked" style="margin-right: 5px;"></i>
                                已处理邀请请求: {{ processedTotal }} 条
                            </span>
                            <span v-else></span>
                            <div class="button-group">
                                <el-button type="primary" size="small" @click="loadProcessedRequests(1)">
                                    <i class="el-icon-refresh"></i> 刷新列表
                                </el-button>
                                <el-button type="danger" size="small" v-if="selectedProcessedRequests.length > 0" @click="deleteSelectedProcessedRequests">
                                    <i class="el-icon-delete"></i> 删除选中 ({{ selectedProcessedRequests.length }})
                                </el-button>
                                <el-button type="danger" size="small" v-if="processedTotal > 0" @click="clearAllProcessedCache">
                                    <i class="el-icon-delete"></i> 清除所有记录
                                </el-button>
                            </div>
                        </div>
                        
                        <el-card v-if="loadingProcessed" class="skeleton-loading" shadow="never">
                            <el-skeleton :rows="5" animated />
                        </el-card>
                        
                        <div v-else>
                            <el-empty v-if="processedRequests.length === 0" description="暂无已处理的商家邀请请求" :image-size="180">
                                <span style="color: #606266; font-size: 15px;">当前没有已处理的商家邀请请求</span>
                            </el-empty>
                            
                            <el-table
                                v-if="processedRequests.length > 0"
                                ref="processedTable"
                                :data="processedRequests"
                                style="width: 100%"
                                @selection-change="handleProcessedSelectionChange"
                                border>
                                <el-table-column
                                    type="selection"
                                    width="55">
                                </el-table-column>
                                <el-table-column
                                    label="会话标题"
                                    prop="session_title">
                                    <template #default="scope">
                                        <div style="display: flex; align-items: center;">
                                            <i class="el-icon-chat-dot-square" style="margin-right: 10px; color: var(--primary-color); font-size: 20px;"></i>
                                            <span style="font-weight: 500;">{{ scope.row.session_title }}</span>
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                    label="邀请商家"
                                    prop="requester_name"
                                    width="150">
                                    <template #default="scope">
                                        <div style="display: flex; align-items: center;">
                                            <i class="el-icon-user" style="margin-right: 5px; color: var(--primary-color);"></i>
                                            <span>{{ scope.row.requester_name }}</span>
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                    label="状态"
                                    prop="status"
                                    width="100">
                                    <template #default="scope">
                                        <el-tag type="success" effect="dark" v-if="scope.row.status === 'approved'">已同意</el-tag>
                                        <el-tag type="danger" effect="dark" v-else-if="scope.row.status === 'rejected'">已拒绝</el-tag>
                                        <el-tag v-else>{{ scope.row.status }}</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                    label="处理时间"
                                    prop="update_time"
                                    width="180">
                                    <template #default="scope">
                                        <div style="display: flex; align-items: center;">
                                            <i class="el-icon-time" style="margin-right: 5px; color: var(--primary-color);"></i>
                                            <span>{{ formatTime(scope.row.update_time) }}</span>
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                    label="操作"
                                    width="200">
                                    <template #default="scope">
                                        <div class="button-group">
                                            <el-button
                                                type="primary"
                                                size="small"
                                                @click="viewSession(scope.row.session_id)"
                                                v-if="scope.row.status === 'approved'">
                                                <i class="el-icon-view" style="margin-right: 5px;"></i>查看会话
                                            </el-button>
                                            <el-button
                                                type="danger"
                                                size="small"
                                                @click="deleteProcessedRequest(scope.row.id)">
                                                <i class="el-icon-delete" style="margin-right: 5px;"></i>删除
                                            </el-button>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>

                            <!-- 移动端卡片布局 -->
                            <div class="mobile-card-list" v-if="processedRequests.length > 0">
                                <div v-for="(request, index) in processedRequests" :key="request.id" class="mobile-processed-card">
                                    <div class="mobile-card-header">
                                        <div class="mobile-card-title">
                                            <i class="el-icon-chat-dot-square"></i>
                                            {{ request.session_title }}
                                        </div>
                                        <div class="mobile-card-status">
                                            <el-tag type="success" effect="dark" size="small" v-if="request.status === 'approved'">已同意</el-tag>
                                            <el-tag type="danger" effect="dark" size="small" v-else-if="request.status === 'rejected'">已拒绝</el-tag>
                                            <el-tag size="small" v-else>{{ request.status }}</el-tag>
                                        </div>
                                    </div>

                                    <div class="mobile-card-body">
                                        <div class="mobile-card-info">
                                            <i class="el-icon-user"></i>
                                            <span class="mobile-card-label">邀请商家:</span>
                                            <span>{{ request.requester_name }}</span>
                                        </div>
                                        <div class="mobile-card-info">
                                            <i class="el-icon-time"></i>
                                            <span class="mobile-card-label">处理时间:</span>
                                            <span>{{ formatTime(request.update_time) }}</span>
                                        </div>
                                    </div>

                                    <div class="mobile-card-actions">
                                        <el-button
                                            type="primary"
                                            size="small"
                                            @click="viewSession(request.session_id)"
                                            v-if="request.status === 'approved'">
                                            <i class="el-icon-view" style="margin-right: 5px;"></i>查看会话
                                        </el-button>
                                        <el-button
                                            type="danger"
                                            size="small"
                                            @click="deleteProcessedRequest(request.id)">
                                            <i class="el-icon-delete" style="margin-right: 5px;"></i>删除记录
                                        </el-button>
                                    </div>
                                </div>
                            </div>

                            <el-pagination
                                v-if="processedTotal > 0"
                                background
                                layout="prev, pager, next"
                                :total="processedTotal"
                                :page-size="limit"
                                :current-page="processedPage"
                                @current-change="handleProcessedPageChange"
                            >
                            </el-pagination>
                        </div>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </div>
    </div>

    <!-- 错误处理脚本 -->
    <script src="/plugin/Customersystem/static/error-handler.js"></script>

    <script>
        const app = Vue.createApp({
            data() {
                return {
                    activeTab: 'pending',
                    requests: [],
                    processedRequests: [],
                    loading: false,
                    loadingProcessed: false,
                    selectedRequest: null,
                    dialogVisible: false,
                    rejectionReason: '',
                    isSupplier: false,
                    supplierInfo: null,
                    currentPage: 1,
                    limit: 10,
                    total: 0,
                    processedPage: 1,
                    processedTotal: 0,
                    selectedProcessedRequests: [],
                    invitationExpiryHours: 48, // 默认48小时
                    invitationSettingsDialogVisible: false,
                    saveSettingsLoading: false,
                    checkingExpired: false,
                    invitationSettings: {
                        value: 48,
                        unit: 'hour'
                    },
                    isMobile: false,
                    isTablet: false,
                    isDesktop: true
                };
            },
            computed: {
                deviceType() {
                    if (this.isMobile) return 'mobile';
                    if (this.isTablet) return 'tablet';
                    return 'desktop';
                }
            },
            mounted() {
                // 设备检测
                this.detectDevice();
                window.addEventListener('resize', this.detectDevice);

                // 添加页面载入动画
                document.body.classList.add('page-loaded');

                this.loadPendingRequests(1);
                
                // 应用卡片动画
                this.$nextTick(() => {
                    this.applyCardAnimations();
                });
                
                // 检查用户是否为供货商
                this.checkSupplierStatus();
                this.fetchRequests();
                this.fetchProcessedRequests();
                
                // 获取邀请过期时间设置
                this.getInvitationExpiryHours();
                
                // 启动定时器，每秒更新一次剩余时间
                this.countdownTimer = setInterval(() => {
                    // 此处不需要做任何事情，Vue会自动重新计算getRemainingTime的结果
                    // 仅在待处理邀请标签页才更新，减少不必要的计算
                    if (this.activeTab === 'pending' && this.requests.length > 0) {
                        this.$forceUpdate();
                    }
                }, 1000);
                
                // 启动定时器，每隔5分钟自动检查过期邀请
                this.checkExpiryTimer = setInterval(() => {

                    this.checkExpiredInvitations();
                }, 300000); // 5分钟 = 300000毫秒
                
                // 首次加载时也检查一次
                setTimeout(() => {
                    this.checkExpiredInvitations();
                }, 2000);
                
                // 添加首屏载入动画
                setTimeout(() => {
                    const cards = document.querySelectorAll('.el-card');
                    cards.forEach((card, index) => {
                        setTimeout(() => {
                            card.classList.add('animated');
                        }, index * 100);
                    });
                }, 300);
            },
            
            // 组件销毁时清除定时器和事件监听器
            unmounted() {
                if (this.countdownTimer) {
                    clearInterval(this.countdownTimer);
                }
                if (this.checkExpiryTimer) {
                    clearInterval(this.checkExpiryTimer);
                }
                window.removeEventListener('resize', this.detectDevice);
            },
            methods: {
                // 设备检测
                detectDevice() {
                    const width = window.innerWidth;
                    this.isMobile = width <= 768;
                    this.isTablet = width > 768 && width <= 1024;
                    this.isDesktop = width > 1024;

                    // 添加设备类型到body
                    document.body.classList.remove('mobile', 'tablet', 'desktop');
                    if (this.isMobile) {
                        document.body.classList.add('mobile');
                    } else if (this.isTablet) {
                        document.body.classList.add('tablet');
                    } else {
                        document.body.classList.add('desktop');
                    }
                },

                // 移动端触摸优化
                handleTouchStart(event) {
                    if (this.isMobile) {
                        event.target.classList.add('touch-active');
                    }
                },

                handleTouchEnd(event) {
                    if (this.isMobile) {
                        setTimeout(() => {
                            event.target.classList.remove('touch-active');
                        }, 150);
                    }
                },

                // 处理标签页切换
                handleTabChange(tab) {
                    // 添加标签切换动画
                    const container = document.querySelector('.tabs-container');
                    container.classList.add('tab-changing');
                    setTimeout(() => {
                        container.classList.remove('tab-changing');
                    }, 300);
                    
                    if (tab.props.name === 'processed' && this.processedRequests.length === 0) {
                        this.loadProcessedRequests(1);
                    }
                    
                    // 应用卡片动画
                    this.$nextTick(() => {
                        this.applyCardAnimations();
                    });
                },
                
                // 跳转到会话页面
                viewSession(sessionId) {
                    if (!sessionId) return;
                    
                    // 添加点击动画效果
                    const btnElement = event.currentTarget;
                    btnElement.classList.add('btn-clicked');
                    
                    localStorage.setItem('joinedSessionId', sessionId);
                    localStorage.setItem('fromJoinRequest', 'true');
                    
                    // 显示加载提示
                    const loadingInstance = ElementPlus.ElLoading.service({
                        text: '正在准备会话...',
                        background: 'rgba(0, 0, 0, 0.7)'
                    });
                    
                    // 先预加载会话列表，然后跳转
                    fetch('/plugin/Customersystem/user/getSessionList')
                        .then(response => response.json())
                        .then(data => {

                            setTimeout(() => {
                                loadingInstance.close();
                                window.location.href = '/plugin/Customersystem/user/index?from=joinRequests';
                            }, 800);
                        })
                        .catch(err => {

                            loadingInstance.close();
                            window.location.href = '/plugin/Customersystem/user/index?from=joinRequests';
                        });
                },
                
                // 应用卡片动画
                applyCardAnimations() {
                    // 为请求卡片添加级联动画索引
                    if (this.activeTab === 'pending') {
                        const cards = document.querySelectorAll('.request-card');
                        cards.forEach((card, index) => {
                            card.style.setProperty('--card-index', index);
                            // 添加悬停效果类
                            card.classList.add('hover-effect');
                        });
                    } else if (this.activeTab === 'processed') {
                        const cards = document.querySelectorAll('.request-card');
                        cards.forEach((card, index) => {
                            card.style.setProperty('--card-index', index);
                        });
                    }
                },
                
                loadPendingRequests(page) {
                    this.loading = true;
                    this.currentPage = page || 1;
                    
                    // 添加加载动画类
                    const container = document.querySelector('.tabs-container');
                    if (container) container.classList.add('loading-data');
                    
                    axios.get('/plugin/Customersystem/user/getPendingJoinRequests', {
                        params: {
                            page: this.currentPage,
                            limit: this.limit
                        }
                    })
                    .then(response => {
                        this.loading = false;
                        // 移除加载动画类
                        if (container) container.classList.remove('loading-data');
                        
                        if (response.data.code === 200) {
                            // 过滤掉已处理的请求
                            let requests = response.data.data.data || [];
                            requests = requests.filter(req => !this.isRequestProcessed(req.id));
                            
                            this.requests = requests;
                            this.total = response.data.data.total || 0;
                            
                            // 应用卡片动画
                            this.$nextTick(() => {
                                this.applyCardAnimations();
                                
                                // 添加新数据加载动画
                                const cards = document.querySelectorAll('.request-card');
                                cards.forEach((card, index) => {
                                    setTimeout(() => {
                                        card.classList.add('card-visible');
                                    }, index * 100);
                                });
                            });
                            
                            // 如果过滤后没有请求，但服务器返回有总数，说明有些请求被过滤掉了
                            if (this.requests.length === 0 && this.total > 0) {
                                // 如果当前不是第一页且服务器返回有数据，尝试加载上一页
                                if (this.currentPage > 1 && response.data.data.data && response.data.data.data.length > 0) {
                                    this.loadPendingRequests(this.currentPage - 1);
                                } else if (this.currentPage === 1) {
                                    // 第一页没有显示数据，但服务器返回有数据，尝试清除本地缓存并重新加载
                                    ElementPlus.ElMessageBox.confirm(
                                        '有些邀请已在本地标记为处理过，是否清除本地缓存并重新显示？',
                                        '提示',
                                        {
                                            confirmButtonText: '确定',
                                            cancelButtonText: '取消',
                                            type: 'info',
                                            customClass: 'custom-message-box',
                                            cancelButtonClass: 'el-button--info'
                                        }
                                    ).then(() => {
                                        localStorage.removeItem('processedChatRequests');
                                        
                                        // 添加成功反馈动画
                                        const notification = ElementPlus.ElNotification({
                                            title: '操作成功',
                                            message: '已清除本地缓存，正在重新加载邀请列表',
                                            type: 'success',
                                            duration: 2000
                                        });
                                        
                                        setTimeout(() => {
                                            this.loadPendingRequests(1);
                                        }, 500);
                                    }).catch(() => {});
                                }
                            }
                        } else {
                            ElementPlus.ElMessage.error(response.data.msg || '加载失败');
                        }
                    })
                    .catch(error => {
                        this.loading = false;
                        // 移除加载动画类
                        if (container) container.classList.remove('loading-data');
                        
                        ElementPlus.ElMessage.error('网络错误，请稍后重试');

                    });
                },
                
                // 加载已处理的邀请列表
                loadProcessedRequests(page) {
                    this.loadingProcessed = true;
                    this.processedPage = page || 1;
                    
                    // 添加加载动画类
                    const container = document.querySelector('.tabs-container');
                    if (container) container.classList.add('loading-data');
                    
                    axios.get('/plugin/Customersystem/user/getProcessedJoinRequests', {
                        params: {
                            page: this.processedPage,
                            limit: this.limit
                        }
                    })
                    .then(response => {
                        this.loadingProcessed = false;
                        // 移除加载动画类
                        if (container) container.classList.remove('loading-data');
                        
                        if (response.data.code === 200) {
                            this.processedRequests = response.data.data.data || [];
                            this.processedTotal = response.data.data.total || 0;
                            
                            // 应用卡片动画
                            this.$nextTick(() => {
                                this.applyCardAnimations();
                                
                                // 添加表格行动画
                                const rows = document.querySelectorAll('.el-table__row');
                                rows.forEach((row, index) => {
                                    setTimeout(() => {
                                        row.classList.add('row-visible');
                                    }, index * 80);
                                });
                            });
                        } else {
                            ElementPlus.ElMessage.error(response.data.msg || '加载失败');
                        }
                    })
                    .catch(error => {
                        this.loadingProcessed = false;
                        // 移除加载动画类
                        if (container) container.classList.remove('loading-data');
                        
                        ElementPlus.ElMessage.error('网络错误，请稍后重试');

                    });
                },
                
                handlePageChange(page) {
                    // 添加翻页动画
                    const container = document.querySelector('.tabs-container');
                    if (container) {
                        container.classList.add('page-changing');
                        setTimeout(() => {
                            container.classList.remove('page-changing');
                        }, 300);
                    }
                    
                    this.loadPendingRequests(page);
                },
                
                handleProcessedPageChange(page) {
                    // 添加翻页动画
                    const container = document.querySelector('.tabs-container');
                    if (container) {
                        container.classList.add('page-changing');
                        setTimeout(() => {
                            container.classList.remove('page-changing');
                        }, 300);
                    }
                    
                    this.loadProcessedRequests(page);
                },
                
                confirmReject(requestId) {
                    ElementPlus.ElMessageBox.confirm('确定拒绝此商家邀请吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning',
                        customClass: 'custom-message-box',
                        cancelButtonClass: 'el-button--info'
                    }).then(() => {
                        this.handleRequest(requestId, 'reject');
                    }).catch(() => {});
                },
                
                // 检查过期邀请请求
                checkExpiredInvitations() {
                    this.checkingExpired = true;
                    
                    axios.get('/plugin/Customersystem/user/checkExpiredInvitations')
                        .then(response => {
                            if (response.data.code === 200) {
                                ElementPlus.ElMessage.success(response.data.msg);
                                // 刷新邀请列表
                                this.loadPendingRequests(1);
                            } else {
                                ElementPlus.ElMessage.error(response.data.msg || '检查失败');
                            }
                        })
                        .catch(error => {
                            ElementPlus.ElMessage.error('检查过期邀请请求失败，请稍后重试');

                        })
                        .finally(() => {
                            this.checkingExpired = false;
                        });
                },
                
                handleRequest(requestId, action) {

                    
                    if (!requestId) {
                        ElementPlus.ElMessage.error('请求ID无效');
                        return;
                    }
                    
                    // 显示确认对话框 (只对同意操作显示确认对话框，拒绝操作已经在confirmReject中显示了确认对话框)
                    if (action === 'approve') {
                        ElementPlus.ElMessageBox.confirm(
                            '确定要同意此邀请吗？',
                            '确认操作',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'success',
                                cancelButtonClass: 'el-button--info'
                            }
                        ).then(() => {
                            this.processRequest(requestId, action);
                        }).catch(() => {
                            // 用户取消操作，不做任何处理
                        });
                    } else {
                        // 拒绝操作直接处理，不再显示确认对话框
                        this.processRequest(requestId, action);
                    }
                },
                
                // 新增方法，处理请求
                processRequest(requestId, action) {
                    // 显示加载提示
                    const loadingInstance = ElementPlus.ElLoading.service({
                        text: action === 'approve' ? '正在同意邀请...' : '正在拒绝邀请...',
                        background: 'rgba(0, 0, 0, 0.7)'
                    });
                    
                    // 创建FormData对象
                    const formData = new FormData();
                    formData.append('request_id', requestId);
                    formData.append('action', action);
                    
                    // 转换为URLSearchParams，确保服务器正确接收POST数据
                    const params = new URLSearchParams();
                    params.append('request_id', requestId);
                    params.append('action', action);
                    
                    // 设置超时时间和重试选项
                    axios.post('/plugin/Customersystem/user/handleJoinRequest', params, {
                        timeout: 30000, // 30秒超时
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded'
                        }
                    })
                    .then(response => {
                        loadingInstance.close();
                        if (response.data.code === 200) {
                            // 记录该请求已被处理，防止刷新页面后重复显示
                            this.markRequestAsProcessed(requestId, action);
                            
                            ElementPlus.ElMessage.success(response.data.msg || (action === 'approve' ? '已同意邀请' : '已拒绝邀请'));
                            // 从当前显示的请求列表中移除此请求
                            this.requests = this.requests.filter(req => req.id !== requestId);
                            
                            // 如果请求列表为空且当前不是第一页，则回到上一页
                            if (this.requests.length === 0 && this.currentPage > 1) {
                                this.loadPendingRequests(this.currentPage - 1);
                            } else if (this.requests.length === 0) {
                                // 如果当前是第一页且列表为空，则更新总数
                                this.total = 0;
                            } else {
                                // 如果列表不为空，更新总数
                                this.total--;
                            }
                            
                            // 处理成功后，刷新已处理列表
                            if (this.activeTab === 'processed') {
                                this.loadProcessedRequests(this.processedPage);
                            }
                            
                            // 如果是同意邀请，跳转到聊天页面
                            if (action === 'approve') {
                                // 更新供货商状态，标记用户为供货商
                                this.isSupplier = true;
                                
                                // 再次检查供货商状态，确保状态同步
                                setTimeout(() => {
                                    this.checkSupplierByProcessedInvites();
                                }, 1000);
                                
                                // 保存会话ID到localStorage
                                if (response.data.data && response.data.data.session_id) {
                                    localStorage.setItem('joinedSessionId', response.data.data.session_id);
                                }
                                
                                // 设置标记，表示是从申请页面处理完毕
                                localStorage.setItem('fromJoinRequest', 'true');
                                
                                // 询问用户是否立即前往聊天页面
                                ElementPlus.ElMessageBox.confirm(
                                    '邀请已同意，是否立即前往聊天页面？',
                                    '操作成功',
                                    {
                                        confirmButtonText: '立即前往',
                                        cancelButtonText: '稍后再说',
                                        type: 'success'
                                    }
                                ).then(() => {
                                    // 用户点击"立即前往"
                                    // 先请求一次会话列表接口，确保会话加载到列表中
                                    fetch('/plugin/Customersystem/user/getSessionList')
                                        .then(response => response.json())
                                        .then(data => {

                                            // 设置一个标记，表示是从邀请页面跳转过来的
                                            localStorage.setItem('fromJoinRequest', 'true');
                                            // 延迟一点再跳转，确保数据已加载
                                            setTimeout(() => {
                                                window.location.href = '/plugin/Customersystem/user/index?from=joinRequests';
                                            }, 500);
                                        })
                                        .catch(err => {

                                            // 即使失败也跳转
                                            window.location.href = '/plugin/Customersystem/user/index?from=joinRequests';
                                        });
                                }).catch(() => {
                                    // 用户点击"稍后再说"，不做任何操作
                                });
                            }
                        } else {
                            ElementPlus.ElMessage.error(response.data.msg || '操作失败');
                        }
                    })
                    .catch(error => {
                        loadingInstance.close();
                        let errorMsg = '网络错误，请稍后重试';
                        let isSuccessfulDespiteError = false;
                        
                        // 检查是否有详细错误信息
                        if (error.response && error.response.data) {
                            if (error.response.data.msg) {
                                errorMsg = error.response.data.msg;
                            } else if (typeof error.response.data === 'string') {
                                // 检查各种可能的错误情况
                                if (error.response.data.includes('Duplicate entry')) {
                                    errorMsg = '该用户已经是会话参与者，无需重复添加';
                                    isSuccessfulDespiteError = true;
                                    
                                    // 更新供货商状态，即使发生了唯一键约束错误
                                    if (action === 'approve') {
                                        // 标记用户为供货商
                                        this.isSupplier = true;
                                        
                                        // 再次检查供货商状态，确保状态同步
                                        setTimeout(() => {
                                            this.checkSupplierByProcessedInvites();
                                        }, 1000);
                                        
                                        // 即使出现唯一键约束错误，也视为成功，询问是否前往聊天页面
                                        setTimeout(() => {
                                            ElementPlus.ElMessageBox.confirm(
                                                '该用户已在会话中，是否立即前往聊天页面？',
                                                '操作成功',
                                                {
                                                    confirmButtonText: '立即前往',
                                                    cancelButtonText: '稍后再说',
                                                    type: 'success'
                                                }
                                            ).then(() => {
                                                window.location.href = '/plugin/Customersystem/user/index?from=joinRequests';
                                            }).catch(() => {});
                                        }, 500);
                                    } else if (error.response.data.includes('邀请不存在或已处理')) {
                                        errorMsg = '该邀请已被处理，请刷新页面';
                                        isSuccessfulDespiteError = true;
                                    }
                                }
                            }
                        } else if (error.message) {
                            errorMsg = error.message;
                            if (error.message.includes('timeout')) {
                                errorMsg = '请求超时，但操作可能已成功，请刷新页面查看最新状态';
                            }
                        }
                        
                        if (isSuccessfulDespiteError) {
                            ElementPlus.ElMessage.success(action === 'approve' ? '已同意邀请' : '已拒绝邀请');
                            // 记录该请求已被处理
                            this.markRequestAsProcessed(requestId, action);
                            // 移除该请求
                            this.requests = this.requests.filter(req => req.id !== requestId);
                            this.total--;
                            
                            // 处理成功后，刷新已处理列表
                            if (this.activeTab === 'processed') {
                                this.loadProcessedRequests(this.processedPage);
                            }
                        } else {
                            ElementPlus.ElMessage.error(errorMsg);
                        }
                        

                    });
                },
                
                // 标记请求为已处理，使用localStorage存储状态
                markRequestAsProcessed(requestId, action) {
                    try {
                        const processedRequests = JSON.parse(localStorage.getItem('processedChatRequests') || '{}');
                        processedRequests[requestId] = {
                            action: action,
                            timestamp: new Date().getTime()
                        };
                        localStorage.setItem('processedChatRequests', JSON.stringify(processedRequests));
                        
                        // 添加成功动画反馈
                        const successIcon = document.createElement('div');
                        successIcon.className = 'success-animation';
                        successIcon.innerHTML = '<i class="el-icon-check"></i>';
                        document.body.appendChild(successIcon);
                        
                        setTimeout(() => {
                            document.body.removeChild(successIcon);
                        }, 1500);
                    } catch (e) {

                    }
                },
                
                // 检查请求是否已被处理
                isRequestProcessed(requestId) {
                    try {
                        const processedRequests = JSON.parse(localStorage.getItem('processedChatRequests') || '{}');
                        return !!processedRequests[requestId];
                    } catch (e) {

                        return false;
                    }
                },
                
                formatTime(timestamp) {
                    if (!timestamp) return '未知时间';
                    
                    const date = new Date(timestamp * 1000);
                    const year = date.getFullYear();
                    const month = (date.getMonth() + 1).toString().padStart(2, '0');
                    const day = date.getDate().toString().padStart(2, '0');
                    const hours = date.getHours().toString().padStart(2, '0');
                    const minutes = date.getMinutes().toString().padStart(2, '0');
                    const seconds = date.getSeconds().toString().padStart(2, '0');
                    
                    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
                },
                
                clearProcessedCache() {
                    ElementPlus.ElMessageBox.confirm(
                        '确定要清除已处理的邀请缓存吗？这将重新显示可能已被处理过的记录。',
                        '清除确认',
                        {
                            confirmButtonText: '确定清除',
                            cancelButtonText: '取消',
                            type: 'warning',
                            cancelButtonClass: 'el-button--info'
                        }
                    ).then(() => {
                        // 显示加载提示
                        const loadingInstance = ElementPlus.ElLoading.service({
                            text: '正在清除缓存...',
                            background: 'rgba(0, 0, 0, 0.7)'
                        });
                        
                        // 执行清除本地缓存
                        localStorage.removeItem('processedChatRequests');
                        
                        // 清除数据库中的已处理邀请记录
                        axios.post('/plugin/Customersystem/user/clearProcessedJoinRequests')
                            .then(response => {
                                if (response.data.code === 200) {
                                    ElementPlus.ElMessage.success('已成功清除所有已处理的邀请记录');
                                } else {
                                    ElementPlus.ElMessage.warning('本地缓存已清除，但数据库记录清除失败: ' + response.data.msg);
                                }
                            })
                            .catch(error => {
                                ElementPlus.ElMessage.warning('本地缓存已清除，但数据库记录清除失败');

                            })
                            .finally(() => {
                                // 关闭加载提示
                                loadingInstance.close();
                                
                                // 刷新两个标签页的数据
                                this.loadProcessedRequests(1);
                                
                                // 如果在已处理标签页，也刷新已处理列表
                                if (this.activeTab === 'processed') {
                                    this.loadProcessedRequests(1);
                                }
                            });
                    }).catch(() => {
                        // 用户取消操作，不做任何处理
                    });
                },
                
                // 检查用户供货商状态
                checkSupplierStatus() {
                    axios.get('/plugin/Customersystem/user/checkSupplierStatus')
                        .then(response => {
                            if (response.data.code === 200) {
                                this.isSupplier = response.data.data.is_supplier || false;
                            }
                        })
                        .catch(error => {

                        })
                        .finally(() => {
                            // 使用增强的供货商检查方法（检查已处理邀请）
                            this.checkSupplierByProcessedInvites();
                        });
                },
                
                // 通过已处理邀请判断供货商身份
                checkSupplierByProcessedInvites() {
                    axios.get('/plugin/Customersystem/user/checkSupplierByProcessedInvites')
                        .then(response => {
                            if (response.data.code === 200) {
                                // 如果基本检查未识别为供货商，但通过邀请记录识别为供货商
                                if (!this.isSupplier && response.data.data.is_supplier) {
                                    this.isSupplier = true;

                                }
                                
                                // 如果有供货商信息，保存起来
                                if (response.data.data.supplier_info) {
                                    this.supplierInfo = response.data.data.supplier_info;
                                }
                            }
                        })
                        .catch(error => {

                        });
                },
                
                fetchRequests() {
                    this.loadPendingRequests(1);
                },
                
                fetchProcessedRequests() {
                    this.loadProcessedRequests(1);
                },
                
                clearAllProcessedCache() {
                    ElementPlus.ElMessageBox.confirm(
                        '确定要清除所有已处理的邀请缓存吗？这将重新显示可能已被处理过的记录。',
                        '批量清除确认',
                        {
                            confirmButtonText: '确定清除',
                            cancelButtonText: '取消',
                            type: 'warning',
                            distinguishCancelAndClose: true,
                            cancelButtonClass: 'el-button--info'
                        }
                    ).then(() => {
                        // 显示加载提示
                        const loadingInstance = ElementPlus.ElLoading.service({
                            text: '正在清除缓存...',
                            background: 'rgba(0, 0, 0, 0.7)'
                        });
                        
                        // 执行清除本地缓存
                        localStorage.removeItem('processedChatRequests');
                        
                        // 清除数据库中的已处理邀请记录
                        axios.post('/plugin/Customersystem/user/clearProcessedJoinRequests')
                            .then(response => {
                                if (response.data.code === 200) {
                                    ElementPlus.ElMessage.success('已成功清除所有已处理的邀请记录');
                                } else {
                                    ElementPlus.ElMessage.warning('本地缓存已清除，但数据库记录清除失败: ' + response.data.msg);
                                }
                            })
                            .catch(error => {
                                ElementPlus.ElMessage.warning('本地缓存已清除，但数据库记录清除失败');

                            })
                            .finally(() => {
                                // 关闭加载提示
                                loadingInstance.close();
                                
                                // 刷新两个标签页的数据
                                this.loadProcessedRequests(1);
                                this.loadPendingRequests(1);
                            });
                    }).catch(() => {
                        // 用户取消操作，不做任何处理
                    });
                },

                handleProcessedSelectionChange(selection) {
                    this.selectedProcessedRequests = selection.map(item => item.id);
                },

                deleteSelectedProcessedRequests() {
                    if (this.selectedProcessedRequests.length === 0) {
                        ElementPlus.ElMessage.warning('请选择要删除的记录');
                        return;
                    }

                    ElementPlus.ElMessageBox.confirm('确定要删除选中的记录吗？', '批量删除确认', {
                        confirmButtonText: '确定删除',
                        cancelButtonText: '取消',
                        type: 'warning',
                        cancelButtonClass: 'el-button--info'
                    }).then(() => {
                        this.deleteProcessedRequests(this.selectedProcessedRequests);
                    }).catch(() => {});
                },

                deleteProcessedRequests(requestIds) {
                    const loadingInstance = ElementPlus.ElLoading.service({
                        text: '正在删除记录...',
                        background: 'rgba(0, 0, 0, 0.7)'
                    });

                    // 创建表单数据
                    const formData = new FormData();
                    requestIds.forEach(id => {
                        formData.append('request_ids[]', id);
                    });

                    axios.post('/plugin/Customersystem/user/batchDeleteProcessedJoinRequests', formData)
                    .then(response => {
                        loadingInstance.close();
                        if (response.data.code === 200) {
                            ElementPlus.ElMessage.success('已成功删除选中的记录');
                            // 重新加载已处理列表
                            this.loadProcessedRequests(this.processedPage);
                            // 清空选中的记录
                            this.selectedProcessedRequests = [];
                        } else {
                            ElementPlus.ElMessage.error(response.data.msg || '删除记录失败');
                        }
                    })
                    .catch(error => {
                        loadingInstance.close();
                        ElementPlus.ElMessage.error('网络错误，请稍后重试');

                    });
                },

                deleteProcessedRequest(requestId) {
                    ElementPlus.ElMessageBox.confirm('确定要删除此记录吗？', '删除确认', {
                        confirmButtonText: '确定删除',
                        cancelButtonText: '取消',
                        type: 'warning',
                        cancelButtonClass: 'el-button--info'
                    }).then(() => {
                        this.deleteProcessedRequests([requestId]);
                    }).catch(() => {});
                },
                
                // 获取邀请过期时间设置
                getInvitationExpiryHours() {
                    axios.get('/plugin/Customersystem/user/getInvitationSettings')
                        .then(response => {
                            if (response.data.code === 200 && response.data.data) {
                                // 设置值和单位
                                this.invitationSettings.value = response.data.data.value;
                                this.invitationSettings.unit = response.data.data.unit;
                                
                                // 计算小时值用于倒计时显示
                                switch (response.data.data.unit) {
                                    case 'minute':
                                        this.invitationExpiryHours = response.data.data.value / 60;
                                        break;
                                    case 'hour':
                                        this.invitationExpiryHours = response.data.data.value;
                                        break;
                                    case 'day':
                                        this.invitationExpiryHours = response.data.data.value * 24;
                                        break;
                                    default:
                                        this.invitationExpiryHours = response.data.data.value;
                                }
                            }
                        })
                        .catch(error => {

                            
                            // 如果获取失败，使用默认值
                            this.invitationSettings.value = 48;
                            this.invitationSettings.unit = 'hour';
                            this.invitationExpiryHours = 48;
                        });
                },
                
                // 显示邀请设置对话框
                showInvitationSettingsDialog() {
                    // 刷新设置值
                    this.getInvitationExpiryHours();
                    // 显示对话框
                    this.invitationSettingsDialogVisible = true;
                },
                
                // 关闭设置对话框
                handleCloseSettingsDialog() {
                    this.invitationSettingsDialogVisible = false;
                },
                
                // 保存邀请过期时间设置
                saveInvitationSettings() {
                    this.saveSettingsLoading = true;
                    
                    const params = new URLSearchParams();
                    params.append('value', this.invitationSettings.value);
                    params.append('unit', this.invitationSettings.unit);
                    
                    axios.post('/plugin/Customersystem/user/saveInvitationSettings', params, {
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded'
                        }
                    })
                    .then(response => {
                        if (response.data.code === 200) {
                            ElementPlus.ElMessage.success('设置保存成功');
                            
                            // 更新小时值用于倒计时显示
                            switch (this.invitationSettings.unit) {
                                case 'minute':
                                    this.invitationExpiryHours = this.invitationSettings.value / 60;
                                    break;
                                case 'hour':
                                    this.invitationExpiryHours = this.invitationSettings.value;
                                    break;
                                case 'day':
                                    this.invitationExpiryHours = this.invitationSettings.value * 24;
                                    break;
                            }
                            
                            // 关闭对话框
                            this.invitationSettingsDialogVisible = false;
                        } else {
                            ElementPlus.ElMessage.error(response.data.msg || '保存设置失败');
                        }
                    })
                    .catch(error => {
                        ElementPlus.ElMessage.error('网络错误，请稍后重试');

                    })
                    .finally(() => {
                        this.saveSettingsLoading = false;
                    });
                },
                
                // 计算邀请的剩余时间
                getRemainingTime(createTime) {
                    // 计算过期时间
                    const expiryTime = createTime + (this.invitationExpiryHours * 3600);
                    const now = Math.floor(Date.now() / 1000); // 当前时间戳（秒）
                    
                    // 如果已过期，返回全零
                    if (now >= expiryTime) {
                        return { days: 0, hours: 0, minutes: 0, seconds: 0 };
                    }
                    
                    // 计算剩余秒数
                    let secondsLeft = expiryTime - now;
                    
                    // 转换为天、时、分、秒
                    const days = Math.floor(secondsLeft / 86400);
                    secondsLeft %= 86400;
                    
                    const hours = Math.floor(secondsLeft / 3600);
                    secondsLeft %= 3600;
                    
                    const minutes = Math.floor(secondsLeft / 60);
                    const seconds = secondsLeft % 60;
                    
                    return {
                        days,
                        hours,
                        minutes,
                        seconds
                    };
                }
            }
        });

        app.use(ElementPlus, {
            locale: ElementPlusLocaleZhCn
        });
        
        // 添加自定义指令，实现淡入动画
        app.directive('fade-in', {
            mounted(el, binding) {
                el.style.opacity = '0';
                el.style.transition = 'opacity 0.5s ease';
                
                setTimeout(() => {
                    el.style.opacity = '1';
                }, binding.value || 0);
            }
        });
        
        app.mount('#app');
        
        // 添加页面载入完成动画
        window.addEventListener('load', () => {
            document.body.classList.add('page-fully-loaded');
        });
    </script>
</body>
</html> 