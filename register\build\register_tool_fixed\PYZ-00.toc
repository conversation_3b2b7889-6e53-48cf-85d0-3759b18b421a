('D:\\编程\\插件开发\\plugin\\register\\build\\register_tool_fixed\\PYZ-00.pyz',
 [('IPython',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\__init__.py',
   'PYMODULE'),
  ('IPython.core',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\__init__.py',
   'PYMODULE'),
  ('IPython.core.alias',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\alias.py',
   'PYMODULE'),
  ('IPython.core.application',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\application.py',
   'PYMODULE'),
  ('IPython.core.async_helpers',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\async_helpers.py',
   'PYMODULE'),
  ('IPython.core.autocall',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\autocall.py',
   'PYMODULE'),
  ('IPython.core.builtin_trap',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\builtin_trap.py',
   'PYMODULE'),
  ('IPython.core.compilerop',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\compilerop.py',
   'PYMODULE'),
  ('IPython.core.completer',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\completer.py',
   'PYMODULE'),
  ('IPython.core.completerlib',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\completerlib.py',
   'PYMODULE'),
  ('IPython.core.crashhandler',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\crashhandler.py',
   'PYMODULE'),
  ('IPython.core.debugger',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\debugger.py',
   'PYMODULE'),
  ('IPython.core.display',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\display.py',
   'PYMODULE'),
  ('IPython.core.display_functions',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\display_functions.py',
   'PYMODULE'),
  ('IPython.core.display_trap',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\display_trap.py',
   'PYMODULE'),
  ('IPython.core.displayhook',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\displayhook.py',
   'PYMODULE'),
  ('IPython.core.displaypub',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\displaypub.py',
   'PYMODULE'),
  ('IPython.core.error',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\error.py',
   'PYMODULE'),
  ('IPython.core.events',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\events.py',
   'PYMODULE'),
  ('IPython.core.excolors',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\excolors.py',
   'PYMODULE'),
  ('IPython.core.extensions',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\extensions.py',
   'PYMODULE'),
  ('IPython.core.formatters',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\formatters.py',
   'PYMODULE'),
  ('IPython.core.getipython',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\getipython.py',
   'PYMODULE'),
  ('IPython.core.guarded_eval',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\guarded_eval.py',
   'PYMODULE'),
  ('IPython.core.history',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\history.py',
   'PYMODULE'),
  ('IPython.core.hooks',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\hooks.py',
   'PYMODULE'),
  ('IPython.core.inputtransformer2',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\inputtransformer2.py',
   'PYMODULE'),
  ('IPython.core.interactiveshell',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\interactiveshell.py',
   'PYMODULE'),
  ('IPython.core.latex_symbols',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\latex_symbols.py',
   'PYMODULE'),
  ('IPython.core.logger',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\logger.py',
   'PYMODULE'),
  ('IPython.core.macro',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\macro.py',
   'PYMODULE'),
  ('IPython.core.magic',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\magic.py',
   'PYMODULE'),
  ('IPython.core.magic_arguments',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\magic_arguments.py',
   'PYMODULE'),
  ('IPython.core.magics',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\magics\\__init__.py',
   'PYMODULE'),
  ('IPython.core.magics.ast_mod',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\magics\\ast_mod.py',
   'PYMODULE'),
  ('IPython.core.magics.auto',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\magics\\auto.py',
   'PYMODULE'),
  ('IPython.core.magics.basic',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\magics\\basic.py',
   'PYMODULE'),
  ('IPython.core.magics.code',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\magics\\code.py',
   'PYMODULE'),
  ('IPython.core.magics.config',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\magics\\config.py',
   'PYMODULE'),
  ('IPython.core.magics.display',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\magics\\display.py',
   'PYMODULE'),
  ('IPython.core.magics.execution',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\magics\\execution.py',
   'PYMODULE'),
  ('IPython.core.magics.extension',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\magics\\extension.py',
   'PYMODULE'),
  ('IPython.core.magics.history',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\magics\\history.py',
   'PYMODULE'),
  ('IPython.core.magics.logging',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\magics\\logging.py',
   'PYMODULE'),
  ('IPython.core.magics.namespace',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\magics\\namespace.py',
   'PYMODULE'),
  ('IPython.core.magics.osm',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\magics\\osm.py',
   'PYMODULE'),
  ('IPython.core.magics.packaging',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\magics\\packaging.py',
   'PYMODULE'),
  ('IPython.core.magics.pylab',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\magics\\pylab.py',
   'PYMODULE'),
  ('IPython.core.magics.script',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\magics\\script.py',
   'PYMODULE'),
  ('IPython.core.oinspect',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\oinspect.py',
   'PYMODULE'),
  ('IPython.core.page',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\page.py',
   'PYMODULE'),
  ('IPython.core.payload',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\payload.py',
   'PYMODULE'),
  ('IPython.core.payloadpage',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\payloadpage.py',
   'PYMODULE'),
  ('IPython.core.prefilter',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\prefilter.py',
   'PYMODULE'),
  ('IPython.core.profiledir',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\profiledir.py',
   'PYMODULE'),
  ('IPython.core.pylabtools',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\pylabtools.py',
   'PYMODULE'),
  ('IPython.core.release',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\release.py',
   'PYMODULE'),
  ('IPython.core.shellapp',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\shellapp.py',
   'PYMODULE'),
  ('IPython.core.splitinput',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\splitinput.py',
   'PYMODULE'),
  ('IPython.core.ultratb',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\ultratb.py',
   'PYMODULE'),
  ('IPython.core.usage',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\core\\usage.py',
   'PYMODULE'),
  ('IPython.display',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\display.py',
   'PYMODULE'),
  ('IPython.extensions',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\extensions\\__init__.py',
   'PYMODULE'),
  ('IPython.extensions.storemagic',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\extensions\\storemagic.py',
   'PYMODULE'),
  ('IPython.external',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\external\\__init__.py',
   'PYMODULE'),
  ('IPython.external.qt_for_kernel',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\external\\qt_for_kernel.py',
   'PYMODULE'),
  ('IPython.external.qt_loaders',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\external\\qt_loaders.py',
   'PYMODULE'),
  ('IPython.lib',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\lib\\__init__.py',
   'PYMODULE'),
  ('IPython.lib.clipboard',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\lib\\clipboard.py',
   'PYMODULE'),
  ('IPython.lib.display',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\lib\\display.py',
   'PYMODULE'),
  ('IPython.lib.guisupport',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\lib\\guisupport.py',
   'PYMODULE'),
  ('IPython.lib.pretty',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\lib\\pretty.py',
   'PYMODULE'),
  ('IPython.paths',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\paths.py',
   'PYMODULE'),
  ('IPython.terminal',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\terminal\\__init__.py',
   'PYMODULE'),
  ('IPython.terminal.debugger',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\terminal\\debugger.py',
   'PYMODULE'),
  ('IPython.terminal.embed',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\terminal\\embed.py',
   'PYMODULE'),
  ('IPython.terminal.interactiveshell',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\terminal\\interactiveshell.py',
   'PYMODULE'),
  ('IPython.terminal.ipapp',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\terminal\\ipapp.py',
   'PYMODULE'),
  ('IPython.terminal.magics',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\terminal\\magics.py',
   'PYMODULE'),
  ('IPython.terminal.prompts',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\terminal\\prompts.py',
   'PYMODULE'),
  ('IPython.terminal.pt_inputhooks',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\terminal\\pt_inputhooks\\__init__.py',
   'PYMODULE'),
  ('IPython.terminal.ptutils',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\terminal\\ptutils.py',
   'PYMODULE'),
  ('IPython.terminal.shortcuts',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\terminal\\shortcuts\\__init__.py',
   'PYMODULE'),
  ('IPython.terminal.shortcuts.auto_match',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\terminal\\shortcuts\\auto_match.py',
   'PYMODULE'),
  ('IPython.terminal.shortcuts.auto_suggest',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\terminal\\shortcuts\\auto_suggest.py',
   'PYMODULE'),
  ('IPython.terminal.shortcuts.filters',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\terminal\\shortcuts\\filters.py',
   'PYMODULE'),
  ('IPython.testing',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\testing\\__init__.py',
   'PYMODULE'),
  ('IPython.testing.skipdoctest',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\testing\\skipdoctest.py',
   'PYMODULE'),
  ('IPython.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\__init__.py',
   'PYMODULE'),
  ('IPython.utils.PyColorize',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\PyColorize.py',
   'PYMODULE'),
  ('IPython.utils._process_cli',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\_process_cli.py',
   'PYMODULE'),
  ('IPython.utils._process_common',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\_process_common.py',
   'PYMODULE'),
  ('IPython.utils._process_emscripten',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\_process_emscripten.py',
   'PYMODULE'),
  ('IPython.utils._process_posix',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\_process_posix.py',
   'PYMODULE'),
  ('IPython.utils._process_win32',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\_process_win32.py',
   'PYMODULE'),
  ('IPython.utils._sysinfo',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\_sysinfo.py',
   'PYMODULE'),
  ('IPython.utils.capture',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\capture.py',
   'PYMODULE'),
  ('IPython.utils.colorable',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\colorable.py',
   'PYMODULE'),
  ('IPython.utils.coloransi',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\coloransi.py',
   'PYMODULE'),
  ('IPython.utils.contexts',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\contexts.py',
   'PYMODULE'),
  ('IPython.utils.data',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\data.py',
   'PYMODULE'),
  ('IPython.utils.decorators',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\decorators.py',
   'PYMODULE'),
  ('IPython.utils.dir2',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\dir2.py',
   'PYMODULE'),
  ('IPython.utils.docs',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\docs.py',
   'PYMODULE'),
  ('IPython.utils.encoding',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\encoding.py',
   'PYMODULE'),
  ('IPython.utils.frame',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\frame.py',
   'PYMODULE'),
  ('IPython.utils.generics',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\generics.py',
   'PYMODULE'),
  ('IPython.utils.importstring',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\importstring.py',
   'PYMODULE'),
  ('IPython.utils.io',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\io.py',
   'PYMODULE'),
  ('IPython.utils.ipstruct',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\ipstruct.py',
   'PYMODULE'),
  ('IPython.utils.module_paths',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\module_paths.py',
   'PYMODULE'),
  ('IPython.utils.openpy',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\openpy.py',
   'PYMODULE'),
  ('IPython.utils.path',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\path.py',
   'PYMODULE'),
  ('IPython.utils.process',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\process.py',
   'PYMODULE'),
  ('IPython.utils.py3compat',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\py3compat.py',
   'PYMODULE'),
  ('IPython.utils.sentinel',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\sentinel.py',
   'PYMODULE'),
  ('IPython.utils.strdispatch',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\strdispatch.py',
   'PYMODULE'),
  ('IPython.utils.sysinfo',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\sysinfo.py',
   'PYMODULE'),
  ('IPython.utils.syspathcontext',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\syspathcontext.py',
   'PYMODULE'),
  ('IPython.utils.terminal',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\terminal.py',
   'PYMODULE'),
  ('IPython.utils.text',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\text.py',
   'PYMODULE'),
  ('IPython.utils.timing',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\timing.py',
   'PYMODULE'),
  ('IPython.utils.tokenutil',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\tokenutil.py',
   'PYMODULE'),
  ('IPython.utils.wildcard',
   'D:\\pyfile\\py\\Lib\\site-packages\\IPython\\utils\\wildcard.py',
   'PYMODULE'),
  ('OpenSSL',
   'D:\\pyfile\\py\\Lib\\site-packages\\OpenSSL\\__init__.py',
   'PYMODULE'),
  ('OpenSSL.SSL',
   'D:\\pyfile\\py\\Lib\\site-packages\\OpenSSL\\SSL.py',
   'PYMODULE'),
  ('OpenSSL._util',
   'D:\\pyfile\\py\\Lib\\site-packages\\OpenSSL\\_util.py',
   'PYMODULE'),
  ('OpenSSL.crypto',
   'D:\\pyfile\\py\\Lib\\site-packages\\OpenSSL\\crypto.py',
   'PYMODULE'),
  ('OpenSSL.version',
   'D:\\pyfile\\py\\Lib\\site-packages\\OpenSSL\\version.py',
   'PYMODULE'),
  ('PIL', 'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\__init__.py', 'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._util',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'D:\\pyfile\\py\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PyQt6',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyQt6\\__init__.py',
   'PYMODULE'),
  ('PyQt6.lupdate',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyQt6\\lupdate\\__init__.py',
   'PYMODULE'),
  ('PyQt6.lupdate.designer_source',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyQt6\\lupdate\\designer_source.py',
   'PYMODULE'),
  ('PyQt6.lupdate.lupdate',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyQt6\\lupdate\\lupdate.py',
   'PYMODULE'),
  ('PyQt6.lupdate.pylupdate',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyQt6\\lupdate\\pylupdate.py',
   'PYMODULE'),
  ('PyQt6.lupdate.python_source',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyQt6\\lupdate\\python_source.py',
   'PYMODULE'),
  ('PyQt6.lupdate.source_file',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyQt6\\lupdate\\source_file.py',
   'PYMODULE'),
  ('PyQt6.lupdate.translation_file',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyQt6\\lupdate\\translation_file.py',
   'PYMODULE'),
  ('PyQt6.lupdate.translations',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyQt6\\lupdate\\translations.py',
   'PYMODULE'),
  ('PyQt6.lupdate.user',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyQt6\\lupdate\\user.py',
   'PYMODULE'),
  ('PyQt6.uic',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyQt6\\uic\\__init__.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\__init__.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.as_string',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\as_string.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.compiler',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\compiler.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.indenter',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\indenter.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.misc',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\misc.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.proxy_metaclass',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\proxy_metaclass.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.qobjectcreator',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\qobjectcreator.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.qtproxies',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\qtproxies.py',
   'PYMODULE'),
  ('PyQt6.uic.Loader',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyQt6\\uic\\Loader\\__init__.py',
   'PYMODULE'),
  ('PyQt6.uic.Loader.loader',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyQt6\\uic\\Loader\\loader.py',
   'PYMODULE'),
  ('PyQt6.uic.Loader.qobjectcreator',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyQt6\\uic\\Loader\\qobjectcreator.py',
   'PYMODULE'),
  ('PyQt6.uic.compile_ui',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyQt6\\uic\\compile_ui.py',
   'PYMODULE'),
  ('PyQt6.uic.enum_map',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyQt6\\uic\\enum_map.py',
   'PYMODULE'),
  ('PyQt6.uic.exceptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyQt6\\uic\\exceptions.py',
   'PYMODULE'),
  ('PyQt6.uic.icon_cache',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyQt6\\uic\\icon_cache.py',
   'PYMODULE'),
  ('PyQt6.uic.load_ui',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyQt6\\uic\\load_ui.py',
   'PYMODULE'),
  ('PyQt6.uic.objcreator',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyQt6\\uic\\objcreator.py',
   'PYMODULE'),
  ('PyQt6.uic.properties',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyQt6\\uic\\properties.py',
   'PYMODULE'),
  ('PyQt6.uic.pyuic',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyQt6\\uic\\pyuic.py',
   'PYMODULE'),
  ('PyQt6.uic.ui_file',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyQt6\\uic\\ui_file.py',
   'PYMODULE'),
  ('PyQt6.uic.uiparser',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyQt6\\uic\\uiparser.py',
   'PYMODULE'),
  ('__future__', 'D:\\pyfile\\py\\Lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'D:\\pyfile\\py\\Lib\\_aix_support.py', 'PYMODULE'),
  ('_bootsubprocess', 'D:\\pyfile\\py\\Lib\\_bootsubprocess.py', 'PYMODULE'),
  ('_compat_pickle', 'D:\\pyfile\\py\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'D:\\pyfile\\py\\Lib\\_compression.py', 'PYMODULE'),
  ('_distutils_hack',
   'D:\\pyfile\\py\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'D:\\pyfile\\py\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_osx_support', 'D:\\pyfile\\py\\Lib\\_osx_support.py', 'PYMODULE'),
  ('_py_abc', 'D:\\pyfile\\py\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\pyfile\\py\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils._win32',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\_win32.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_pyi_rth_utils.tempfile',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\tempfile.py',
   'PYMODULE'),
  ('_sitebuiltins', 'D:\\pyfile\\py\\Lib\\_sitebuiltins.py', 'PYMODULE'),
  ('_strptime', 'D:\\pyfile\\py\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'D:\\pyfile\\py\\Lib\\_threading_local.py', 'PYMODULE'),
  ('argcomplete',
   'D:\\pyfile\\py\\Lib\\site-packages\\argcomplete\\__init__.py',
   'PYMODULE'),
  ('argcomplete.completers',
   'D:\\pyfile\\py\\Lib\\site-packages\\argcomplete\\completers.py',
   'PYMODULE'),
  ('argcomplete.exceptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\argcomplete\\exceptions.py',
   'PYMODULE'),
  ('argcomplete.finders',
   'D:\\pyfile\\py\\Lib\\site-packages\\argcomplete\\finders.py',
   'PYMODULE'),
  ('argcomplete.io',
   'D:\\pyfile\\py\\Lib\\site-packages\\argcomplete\\io.py',
   'PYMODULE'),
  ('argcomplete.lexers',
   'D:\\pyfile\\py\\Lib\\site-packages\\argcomplete\\lexers.py',
   'PYMODULE'),
  ('argcomplete.packages',
   'D:\\pyfile\\py\\Lib\\site-packages\\argcomplete\\packages\\__init__.py',
   'PYMODULE'),
  ('argcomplete.packages._argparse',
   'D:\\pyfile\\py\\Lib\\site-packages\\argcomplete\\packages\\_argparse.py',
   'PYMODULE'),
  ('argcomplete.packages._shlex',
   'D:\\pyfile\\py\\Lib\\site-packages\\argcomplete\\packages\\_shlex.py',
   'PYMODULE'),
  ('argcomplete.shell_integration',
   'D:\\pyfile\\py\\Lib\\site-packages\\argcomplete\\shell_integration.py',
   'PYMODULE'),
  ('argparse', 'D:\\pyfile\\py\\Lib\\argparse.py', 'PYMODULE'),
  ('arrow',
   'D:\\pyfile\\py\\Lib\\site-packages\\arrow\\__init__.py',
   'PYMODULE'),
  ('arrow._version',
   'D:\\pyfile\\py\\Lib\\site-packages\\arrow\\_version.py',
   'PYMODULE'),
  ('arrow.api',
   'D:\\pyfile\\py\\Lib\\site-packages\\arrow\\api.py',
   'PYMODULE'),
  ('arrow.arrow',
   'D:\\pyfile\\py\\Lib\\site-packages\\arrow\\arrow.py',
   'PYMODULE'),
  ('arrow.constants',
   'D:\\pyfile\\py\\Lib\\site-packages\\arrow\\constants.py',
   'PYMODULE'),
  ('arrow.factory',
   'D:\\pyfile\\py\\Lib\\site-packages\\arrow\\factory.py',
   'PYMODULE'),
  ('arrow.formatter',
   'D:\\pyfile\\py\\Lib\\site-packages\\arrow\\formatter.py',
   'PYMODULE'),
  ('arrow.locales',
   'D:\\pyfile\\py\\Lib\\site-packages\\arrow\\locales.py',
   'PYMODULE'),
  ('arrow.parser',
   'D:\\pyfile\\py\\Lib\\site-packages\\arrow\\parser.py',
   'PYMODULE'),
  ('arrow.util',
   'D:\\pyfile\\py\\Lib\\site-packages\\arrow\\util.py',
   'PYMODULE'),
  ('ast', 'D:\\pyfile\\py\\Lib\\ast.py', 'PYMODULE'),
  ('asttokens',
   'D:\\pyfile\\py\\Lib\\site-packages\\asttokens\\__init__.py',
   'PYMODULE'),
  ('asttokens.astroid_compat',
   'D:\\pyfile\\py\\Lib\\site-packages\\asttokens\\astroid_compat.py',
   'PYMODULE'),
  ('asttokens.asttokens',
   'D:\\pyfile\\py\\Lib\\site-packages\\asttokens\\asttokens.py',
   'PYMODULE'),
  ('asttokens.line_numbers',
   'D:\\pyfile\\py\\Lib\\site-packages\\asttokens\\line_numbers.py',
   'PYMODULE'),
  ('asttokens.mark_tokens',
   'D:\\pyfile\\py\\Lib\\site-packages\\asttokens\\mark_tokens.py',
   'PYMODULE'),
  ('asttokens.util',
   'D:\\pyfile\\py\\Lib\\site-packages\\asttokens\\util.py',
   'PYMODULE'),
  ('asyncio', 'D:\\pyfile\\py\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'D:\\pyfile\\py\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\pyfile\\py\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\pyfile\\py\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\pyfile\\py\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\pyfile\\py\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\pyfile\\py\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events', 'D:\\pyfile\\py\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\pyfile\\py\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\pyfile\\py\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures', 'D:\\pyfile\\py\\Lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.locks', 'D:\\pyfile\\py\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.log', 'D:\\pyfile\\py\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.mixins', 'D:\\pyfile\\py\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\pyfile\\py\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\pyfile\\py\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues', 'D:\\pyfile\\py\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners', 'D:\\pyfile\\py\\Lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\pyfile\\py\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto', 'D:\\pyfile\\py\\Lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.staggered',
   'D:\\pyfile\\py\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams', 'D:\\pyfile\\py\\Lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\pyfile\\py\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'D:\\pyfile\\py\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks', 'D:\\pyfile\\py\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.threads', 'D:\\pyfile\\py\\Lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.timeouts', 'D:\\pyfile\\py\\Lib\\asyncio\\timeouts.py', 'PYMODULE'),
  ('asyncio.transports',
   'D:\\pyfile\\py\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock', 'D:\\pyfile\\py\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\pyfile\\py\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\pyfile\\py\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\pyfile\\py\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('attr', 'D:\\pyfile\\py\\Lib\\site-packages\\attr\\__init__.py', 'PYMODULE'),
  ('attr._cmp',
   'D:\\pyfile\\py\\Lib\\site-packages\\attr\\_cmp.py',
   'PYMODULE'),
  ('attr._compat',
   'D:\\pyfile\\py\\Lib\\site-packages\\attr\\_compat.py',
   'PYMODULE'),
  ('attr._config',
   'D:\\pyfile\\py\\Lib\\site-packages\\attr\\_config.py',
   'PYMODULE'),
  ('attr._funcs',
   'D:\\pyfile\\py\\Lib\\site-packages\\attr\\_funcs.py',
   'PYMODULE'),
  ('attr._make',
   'D:\\pyfile\\py\\Lib\\site-packages\\attr\\_make.py',
   'PYMODULE'),
  ('attr._next_gen',
   'D:\\pyfile\\py\\Lib\\site-packages\\attr\\_next_gen.py',
   'PYMODULE'),
  ('attr._version_info',
   'D:\\pyfile\\py\\Lib\\site-packages\\attr\\_version_info.py',
   'PYMODULE'),
  ('attr.converters',
   'D:\\pyfile\\py\\Lib\\site-packages\\attr\\converters.py',
   'PYMODULE'),
  ('attr.exceptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\attr\\exceptions.py',
   'PYMODULE'),
  ('attr.filters',
   'D:\\pyfile\\py\\Lib\\site-packages\\attr\\filters.py',
   'PYMODULE'),
  ('attr.setters',
   'D:\\pyfile\\py\\Lib\\site-packages\\attr\\setters.py',
   'PYMODULE'),
  ('attr.validators',
   'D:\\pyfile\\py\\Lib\\site-packages\\attr\\validators.py',
   'PYMODULE'),
  ('attrs',
   'D:\\pyfile\\py\\Lib\\site-packages\\attrs\\__init__.py',
   'PYMODULE'),
  ('attrs.converters',
   'D:\\pyfile\\py\\Lib\\site-packages\\attrs\\converters.py',
   'PYMODULE'),
  ('attrs.exceptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\attrs\\exceptions.py',
   'PYMODULE'),
  ('attrs.filters',
   'D:\\pyfile\\py\\Lib\\site-packages\\attrs\\filters.py',
   'PYMODULE'),
  ('attrs.setters',
   'D:\\pyfile\\py\\Lib\\site-packages\\attrs\\setters.py',
   'PYMODULE'),
  ('attrs.validators',
   'D:\\pyfile\\py\\Lib\\site-packages\\attrs\\validators.py',
   'PYMODULE'),
  ('base64', 'D:\\pyfile\\py\\Lib\\base64.py', 'PYMODULE'),
  ('bcrypt',
   'D:\\pyfile\\py\\Lib\\site-packages\\bcrypt\\__init__.py',
   'PYMODULE'),
  ('bcrypt.__about__',
   'D:\\pyfile\\py\\Lib\\site-packages\\bcrypt\\__about__.py',
   'PYMODULE'),
  ('bdb', 'D:\\pyfile\\py\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'D:\\pyfile\\py\\Lib\\bisect.py', 'PYMODULE'),
  ('brotli', 'D:\\pyfile\\py\\Lib\\site-packages\\brotli.py', 'PYMODULE'),
  ('browser_automation',
   'D:\\编程\\插件开发\\plugin\\register\\browser_automation\\__init__.py',
   'PYMODULE'),
  ('browser_automation.element_helper',
   'D:\\编程\\插件开发\\plugin\\register\\browser_automation\\element_helper.py',
   'PYMODULE'),
  ('browser_automation.selenium_register',
   'D:\\编程\\插件开发\\plugin\\register\\browser_automation\\selenium_register.py',
   'PYMODULE'),
  ('browser_automation.xpath_config',
   'D:\\编程\\插件开发\\plugin\\register\\browser_automation\\xpath_config.py',
   'PYMODULE'),
  ('bz2', 'D:\\pyfile\\py\\Lib\\bz2.py', 'PYMODULE'),
  ('cProfile', 'D:\\pyfile\\py\\Lib\\cProfile.py', 'PYMODULE'),
  ('calendar', 'D:\\pyfile\\py\\Lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'D:\\pyfile\\py\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\pyfile\\py\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cffi', 'D:\\pyfile\\py\\Lib\\site-packages\\cffi\\__init__.py', 'PYMODULE'),
  ('cffi._imp_emulation',
   'D:\\pyfile\\py\\Lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.api', 'D:\\pyfile\\py\\Lib\\site-packages\\cffi\\api.py', 'PYMODULE'),
  ('cffi.cffi_opcode',
   'D:\\pyfile\\py\\Lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'D:\\pyfile\\py\\Lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'D:\\pyfile\\py\\Lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error',
   'D:\\pyfile\\py\\Lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'D:\\pyfile\\py\\Lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock',
   'D:\\pyfile\\py\\Lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.model',
   'D:\\pyfile\\py\\Lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'D:\\pyfile\\py\\Lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'D:\\pyfile\\py\\Lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'D:\\pyfile\\py\\Lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'D:\\pyfile\\py\\Lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'D:\\pyfile\\py\\Lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cgi', 'D:\\pyfile\\py\\Lib\\cgi.py', 'PYMODULE'),
  ('chardet',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.enums',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.escprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.johabfreq',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\johabfreq.py',
   'PYMODULE'),
  ('chardet.johabprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\johabprober.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langrussianmodel',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\langrussianmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.utf1632prober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\utf1632prober.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.version',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\pyfile\\py\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\pyfile\\py\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\pyfile\\py\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\pyfile\\py\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\pyfile\\py\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\pyfile\\py\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\pyfile\\py\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('clr', 'D:\\pyfile\\py\\Lib\\site-packages\\clr.py', 'PYMODULE'),
  ('clr_loader',
   'D:\\pyfile\\py\\Lib\\site-packages\\clr_loader\\__init__.py',
   'PYMODULE'),
  ('clr_loader.ffi',
   'D:\\pyfile\\py\\Lib\\site-packages\\clr_loader\\ffi\\__init__.py',
   'PYMODULE'),
  ('clr_loader.ffi.hostfxr',
   'D:\\pyfile\\py\\Lib\\site-packages\\clr_loader\\ffi\\hostfxr.py',
   'PYMODULE'),
  ('clr_loader.ffi.mono',
   'D:\\pyfile\\py\\Lib\\site-packages\\clr_loader\\ffi\\mono.py',
   'PYMODULE'),
  ('clr_loader.ffi.netfx',
   'D:\\pyfile\\py\\Lib\\site-packages\\clr_loader\\ffi\\netfx.py',
   'PYMODULE'),
  ('clr_loader.hostfxr',
   'D:\\pyfile\\py\\Lib\\site-packages\\clr_loader\\hostfxr.py',
   'PYMODULE'),
  ('clr_loader.mono',
   'D:\\pyfile\\py\\Lib\\site-packages\\clr_loader\\mono.py',
   'PYMODULE'),
  ('clr_loader.netfx',
   'D:\\pyfile\\py\\Lib\\site-packages\\clr_loader\\netfx.py',
   'PYMODULE'),
  ('clr_loader.types',
   'D:\\pyfile\\py\\Lib\\site-packages\\clr_loader\\types.py',
   'PYMODULE'),
  ('clr_loader.util',
   'D:\\pyfile\\py\\Lib\\site-packages\\clr_loader\\util\\__init__.py',
   'PYMODULE'),
  ('clr_loader.util.clr_error',
   'D:\\pyfile\\py\\Lib\\site-packages\\clr_loader\\util\\clr_error.py',
   'PYMODULE'),
  ('clr_loader.util.coreclr_errors',
   'D:\\pyfile\\py\\Lib\\site-packages\\clr_loader\\util\\coreclr_errors.py',
   'PYMODULE'),
  ('clr_loader.util.find',
   'D:\\pyfile\\py\\Lib\\site-packages\\clr_loader\\util\\find.py',
   'PYMODULE'),
  ('clr_loader.util.hostfxr_errors',
   'D:\\pyfile\\py\\Lib\\site-packages\\clr_loader\\util\\hostfxr_errors.py',
   'PYMODULE'),
  ('clr_loader.util.runtime_spec',
   'D:\\pyfile\\py\\Lib\\site-packages\\clr_loader\\util\\runtime_spec.py',
   'PYMODULE'),
  ('cmd', 'D:\\pyfile\\py\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'D:\\pyfile\\py\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\pyfile\\py\\Lib\\codeop.py', 'PYMODULE'),
  ('colorama',
   'D:\\pyfile\\py\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'D:\\pyfile\\py\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'D:\\pyfile\\py\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'D:\\pyfile\\py\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'D:\\pyfile\\py\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'D:\\pyfile\\py\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorsys', 'D:\\pyfile\\py\\Lib\\colorsys.py', 'PYMODULE'),
  ('comm', 'D:\\pyfile\\py\\Lib\\site-packages\\comm\\__init__.py', 'PYMODULE'),
  ('comm.base_comm',
   'D:\\pyfile\\py\\Lib\\site-packages\\comm\\base_comm.py',
   'PYMODULE'),
  ('commctrl',
   'D:\\pyfile\\py\\Lib\\site-packages\\win32\\lib\\commctrl.py',
   'PYMODULE'),
  ('concurrent', 'D:\\pyfile\\py\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('concurrent.futures',
   'D:\\pyfile\\py\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\pyfile\\py\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\pyfile\\py\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\pyfile\\py\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('config', 'D:\\编程\\插件开发\\plugin\\register\\config\\__init__.py', 'PYMODULE'),
  ('config.config_manager',
   'D:\\编程\\插件开发\\plugin\\register\\config\\config_manager.py',
   'PYMODULE'),
  ('configparser', 'D:\\pyfile\\py\\Lib\\configparser.py', 'PYMODULE'),
  ('contextlib', 'D:\\pyfile\\py\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'D:\\pyfile\\py\\Lib\\contextvars.py', 'PYMODULE'),
  ('contourpy',
   'D:\\pyfile\\py\\Lib\\site-packages\\contourpy\\__init__.py',
   'PYMODULE'),
  ('contourpy._version',
   'D:\\pyfile\\py\\Lib\\site-packages\\contourpy\\_version.py',
   'PYMODULE'),
  ('contourpy.array',
   'D:\\pyfile\\py\\Lib\\site-packages\\contourpy\\array.py',
   'PYMODULE'),
  ('contourpy.chunk',
   'D:\\pyfile\\py\\Lib\\site-packages\\contourpy\\chunk.py',
   'PYMODULE'),
  ('contourpy.convert',
   'D:\\pyfile\\py\\Lib\\site-packages\\contourpy\\convert.py',
   'PYMODULE'),
  ('contourpy.dechunk',
   'D:\\pyfile\\py\\Lib\\site-packages\\contourpy\\dechunk.py',
   'PYMODULE'),
  ('contourpy.enum_util',
   'D:\\pyfile\\py\\Lib\\site-packages\\contourpy\\enum_util.py',
   'PYMODULE'),
  ('contourpy.typecheck',
   'D:\\pyfile\\py\\Lib\\site-packages\\contourpy\\typecheck.py',
   'PYMODULE'),
  ('contourpy.types',
   'D:\\pyfile\\py\\Lib\\site-packages\\contourpy\\types.py',
   'PYMODULE'),
  ('copy', 'D:\\pyfile\\py\\Lib\\copy.py', 'PYMODULE'),
  ('core', 'D:\\编程\\插件开发\\plugin\\register\\core\\__init__.py', 'PYMODULE'),
  ('core.batch_register',
   'D:\\编程\\插件开发\\plugin\\register\\core\\batch_register.py',
   'PYMODULE'),
  ('core.captcha_handler',
   'D:\\编程\\插件开发\\plugin\\register\\core\\captcha_handler.py',
   'PYMODULE'),
  ('core.register',
   'D:\\编程\\插件开发\\plugin\\register\\core\\register.py',
   'PYMODULE'),
  ('core.sms_api',
   'D:\\编程\\插件开发\\plugin\\register\\core\\sms_api.py',
   'PYMODULE'),
  ('cryptography',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.aead',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ciphers',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ciphers.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.cmac',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\cmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.decode_asn1',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\decode_asn1.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ec',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.rsa',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.padding',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.pkcs12',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\pkcs12.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('csv', 'D:\\pyfile\\py\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'D:\\pyfile\\py\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix', 'D:\\pyfile\\py\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes._endian', 'D:\\pyfile\\py\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('ctypes.macholib',
   'D:\\pyfile\\py\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\pyfile\\py\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\pyfile\\py\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\pyfile\\py\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util', 'D:\\pyfile\\py\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes.wintypes', 'D:\\pyfile\\py\\Lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('curses', 'D:\\pyfile\\py\\Lib\\curses\\__init__.py', 'PYMODULE'),
  ('curses.has_key', 'D:\\pyfile\\py\\Lib\\curses\\has_key.py', 'PYMODULE'),
  ('cycler',
   'D:\\pyfile\\py\\Lib\\site-packages\\cycler\\__init__.py',
   'PYMODULE'),
  ('dataclasses', 'D:\\pyfile\\py\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'D:\\pyfile\\py\\Lib\\datetime.py', 'PYMODULE'),
  ('dateutil',
   'D:\\pyfile\\py\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'D:\\pyfile\\py\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'D:\\pyfile\\py\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'D:\\pyfile\\py\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'D:\\pyfile\\py\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'D:\\pyfile\\py\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'D:\\pyfile\\py\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'D:\\pyfile\\py\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'D:\\pyfile\\py\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'D:\\pyfile\\py\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'D:\\pyfile\\py\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'D:\\pyfile\\py\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'D:\\pyfile\\py\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'D:\\pyfile\\py\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'D:\\pyfile\\py\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('ddddocr',
   'D:\\pyfile\\py\\Lib\\site-packages\\ddddocr\\__init__.py',
   'PYMODULE'),
  ('debugpy',
   'D:\\pyfile\\py\\Lib\\site-packages\\debugpy\\__init__.py',
   'PYMODULE'),
  ('debugpy._vendored',
   'D:\\pyfile\\py\\Lib\\site-packages\\debugpy\\_vendored\\__init__.py',
   'PYMODULE'),
  ('debugpy._vendored._util',
   'D:\\pyfile\\py\\Lib\\site-packages\\debugpy\\_vendored\\_util.py',
   'PYMODULE'),
  ('debugpy._vendored.force_pydevd',
   'D:\\pyfile\\py\\Lib\\site-packages\\debugpy\\_vendored\\force_pydevd.py',
   'PYMODULE'),
  ('debugpy._version',
   'D:\\pyfile\\py\\Lib\\site-packages\\debugpy\\_version.py',
   'PYMODULE'),
  ('debugpy.adapter',
   'D:\\pyfile\\py\\Lib\\site-packages\\debugpy\\adapter\\__init__.py',
   'PYMODULE'),
  ('debugpy.common',
   'D:\\pyfile\\py\\Lib\\site-packages\\debugpy\\common\\__init__.py',
   'PYMODULE'),
  ('debugpy.common.json',
   'D:\\pyfile\\py\\Lib\\site-packages\\debugpy\\common\\json.py',
   'PYMODULE'),
  ('debugpy.common.log',
   'D:\\pyfile\\py\\Lib\\site-packages\\debugpy\\common\\log.py',
   'PYMODULE'),
  ('debugpy.common.sockets',
   'D:\\pyfile\\py\\Lib\\site-packages\\debugpy\\common\\sockets.py',
   'PYMODULE'),
  ('debugpy.common.timestamp',
   'D:\\pyfile\\py\\Lib\\site-packages\\debugpy\\common\\timestamp.py',
   'PYMODULE'),
  ('debugpy.common.util',
   'D:\\pyfile\\py\\Lib\\site-packages\\debugpy\\common\\util.py',
   'PYMODULE'),
  ('debugpy.public_api',
   'D:\\pyfile\\py\\Lib\\site-packages\\debugpy\\public_api.py',
   'PYMODULE'),
  ('debugpy.server',
   'D:\\pyfile\\py\\Lib\\site-packages\\debugpy\\server\\__init__.py',
   'PYMODULE'),
  ('debugpy.server.api',
   'D:\\pyfile\\py\\Lib\\site-packages\\debugpy\\server\\api.py',
   'PYMODULE'),
  ('decimal', 'D:\\pyfile\\py\\Lib\\decimal.py', 'PYMODULE'),
  ('decorator', 'D:\\pyfile\\py\\Lib\\site-packages\\decorator.py', 'PYMODULE'),
  ('defusedxml',
   'D:\\pyfile\\py\\Lib\\site-packages\\defusedxml\\__init__.py',
   'PYMODULE'),
  ('defusedxml.ElementTree',
   'D:\\pyfile\\py\\Lib\\site-packages\\defusedxml\\ElementTree.py',
   'PYMODULE'),
  ('defusedxml.cElementTree',
   'D:\\pyfile\\py\\Lib\\site-packages\\defusedxml\\cElementTree.py',
   'PYMODULE'),
  ('defusedxml.common',
   'D:\\pyfile\\py\\Lib\\site-packages\\defusedxml\\common.py',
   'PYMODULE'),
  ('defusedxml.expatbuilder',
   'D:\\pyfile\\py\\Lib\\site-packages\\defusedxml\\expatbuilder.py',
   'PYMODULE'),
  ('defusedxml.expatreader',
   'D:\\pyfile\\py\\Lib\\site-packages\\defusedxml\\expatreader.py',
   'PYMODULE'),
  ('defusedxml.minidom',
   'D:\\pyfile\\py\\Lib\\site-packages\\defusedxml\\minidom.py',
   'PYMODULE'),
  ('defusedxml.pulldom',
   'D:\\pyfile\\py\\Lib\\site-packages\\defusedxml\\pulldom.py',
   'PYMODULE'),
  ('defusedxml.sax',
   'D:\\pyfile\\py\\Lib\\site-packages\\defusedxml\\sax.py',
   'PYMODULE'),
  ('defusedxml.xmlrpc',
   'D:\\pyfile\\py\\Lib\\site-packages\\defusedxml\\xmlrpc.py',
   'PYMODULE'),
  ('difflib', 'D:\\pyfile\\py\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'D:\\pyfile\\py\\Lib\\dis.py', 'PYMODULE'),
  ('distutils', 'D:\\pyfile\\py\\Lib\\distutils\\__init__.py', 'PYMODULE'),
  ('distutils._msvccompiler',
   'D:\\pyfile\\py\\Lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'D:\\pyfile\\py\\Lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'D:\\pyfile\\py\\Lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.cmd', 'D:\\pyfile\\py\\Lib\\distutils\\cmd.py', 'PYMODULE'),
  ('distutils.command',
   'D:\\pyfile\\py\\Lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'D:\\pyfile\\py\\Lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('distutils.command.build',
   'D:\\pyfile\\py\\Lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'D:\\pyfile\\py\\Lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'D:\\pyfile\\py\\Lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('distutils.config', 'D:\\pyfile\\py\\Lib\\distutils\\config.py', 'PYMODULE'),
  ('distutils.core', 'D:\\pyfile\\py\\Lib\\distutils\\core.py', 'PYMODULE'),
  ('distutils.debug', 'D:\\pyfile\\py\\Lib\\distutils\\debug.py', 'PYMODULE'),
  ('distutils.dep_util',
   'D:\\pyfile\\py\\Lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'D:\\pyfile\\py\\Lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.dist', 'D:\\pyfile\\py\\Lib\\distutils\\dist.py', 'PYMODULE'),
  ('distutils.errors', 'D:\\pyfile\\py\\Lib\\distutils\\errors.py', 'PYMODULE'),
  ('distutils.extension',
   'D:\\pyfile\\py\\Lib\\distutils\\extension.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'D:\\pyfile\\py\\Lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.file_util',
   'D:\\pyfile\\py\\Lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'D:\\pyfile\\py\\Lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('distutils.log', 'D:\\pyfile\\py\\Lib\\distutils\\log.py', 'PYMODULE'),
  ('distutils.msvc9compiler',
   'D:\\pyfile\\py\\Lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.spawn', 'D:\\pyfile\\py\\Lib\\distutils\\spawn.py', 'PYMODULE'),
  ('distutils.sysconfig',
   'D:\\pyfile\\py\\Lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'D:\\pyfile\\py\\Lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('distutils.unixccompiler',
   'D:\\pyfile\\py\\Lib\\distutils\\unixccompiler.py',
   'PYMODULE'),
  ('distutils.util', 'D:\\pyfile\\py\\Lib\\distutils\\util.py', 'PYMODULE'),
  ('distutils.version',
   'D:\\pyfile\\py\\Lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'D:\\pyfile\\py\\Lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('dns', 'D:\\pyfile\\py\\Lib\\site-packages\\dns\\__init__.py', 'PYMODULE'),
  ('dns.rdtypes',
   'D:\\pyfile\\py\\Lib\\site-packages\\dns\\rdtypes\\__init__.py',
   'PYMODULE'),
  ('dns.version',
   'D:\\pyfile\\py\\Lib\\site-packages\\dns\\version.py',
   'PYMODULE'),
  ('doctest', 'D:\\pyfile\\py\\Lib\\doctest.py', 'PYMODULE'),
  ('dotenv',
   'D:\\pyfile\\py\\Lib\\site-packages\\dotenv\\__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   'D:\\pyfile\\py\\Lib\\site-packages\\dotenv\\ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   'D:\\pyfile\\py\\Lib\\site-packages\\dotenv\\main.py',
   'PYMODULE'),
  ('dotenv.parser',
   'D:\\pyfile\\py\\Lib\\site-packages\\dotenv\\parser.py',
   'PYMODULE'),
  ('dotenv.variables',
   'D:\\pyfile\\py\\Lib\\site-packages\\dotenv\\variables.py',
   'PYMODULE'),
  ('email', 'D:\\pyfile\\py\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\pyfile\\py\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\pyfile\\py\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'D:\\pyfile\\py\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase',
   'D:\\pyfile\\py\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime', 'D:\\pyfile\\py\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'D:\\pyfile\\py\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\pyfile\\py\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'D:\\pyfile\\py\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'D:\\pyfile\\py\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\pyfile\\py\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'D:\\pyfile\\py\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'D:\\pyfile\\py\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\pyfile\\py\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'D:\\pyfile\\py\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'D:\\pyfile\\py\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'D:\\pyfile\\py\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'D:\\pyfile\\py\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'D:\\pyfile\\py\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'D:\\pyfile\\py\\Lib\\email\\utils.py', 'PYMODULE'),
  ('executing',
   'D:\\pyfile\\py\\Lib\\site-packages\\executing\\__init__.py',
   'PYMODULE'),
  ('executing._exceptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\executing\\_exceptions.py',
   'PYMODULE'),
  ('executing._position_node_finder',
   'D:\\pyfile\\py\\Lib\\site-packages\\executing\\_position_node_finder.py',
   'PYMODULE'),
  ('executing.executing',
   'D:\\pyfile\\py\\Lib\\site-packages\\executing\\executing.py',
   'PYMODULE'),
  ('executing.version',
   'D:\\pyfile\\py\\Lib\\site-packages\\executing\\version.py',
   'PYMODULE'),
  ('fastjsonschema',
   'D:\\pyfile\\py\\Lib\\site-packages\\fastjsonschema\\__init__.py',
   'PYMODULE'),
  ('fastjsonschema.draft04',
   'D:\\pyfile\\py\\Lib\\site-packages\\fastjsonschema\\draft04.py',
   'PYMODULE'),
  ('fastjsonschema.draft06',
   'D:\\pyfile\\py\\Lib\\site-packages\\fastjsonschema\\draft06.py',
   'PYMODULE'),
  ('fastjsonschema.draft07',
   'D:\\pyfile\\py\\Lib\\site-packages\\fastjsonschema\\draft07.py',
   'PYMODULE'),
  ('fastjsonschema.exceptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\fastjsonschema\\exceptions.py',
   'PYMODULE'),
  ('fastjsonschema.generator',
   'D:\\pyfile\\py\\Lib\\site-packages\\fastjsonschema\\generator.py',
   'PYMODULE'),
  ('fastjsonschema.indent',
   'D:\\pyfile\\py\\Lib\\site-packages\\fastjsonschema\\indent.py',
   'PYMODULE'),
  ('fastjsonschema.ref_resolver',
   'D:\\pyfile\\py\\Lib\\site-packages\\fastjsonschema\\ref_resolver.py',
   'PYMODULE'),
  ('fastjsonschema.version',
   'D:\\pyfile\\py\\Lib\\site-packages\\fastjsonschema\\version.py',
   'PYMODULE'),
  ('filecmp', 'D:\\pyfile\\py\\Lib\\filecmp.py', 'PYMODULE'),
  ('fnmatch', 'D:\\pyfile\\py\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fqdn', 'D:\\pyfile\\py\\Lib\\site-packages\\fqdn\\__init__.py', 'PYMODULE'),
  ('fqdn._compat',
   'D:\\pyfile\\py\\Lib\\site-packages\\fqdn\\_compat.py',
   'PYMODULE'),
  ('fractions', 'D:\\pyfile\\py\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'D:\\pyfile\\py\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\pyfile\\py\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\pyfile\\py\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\pyfile\\py\\Lib\\gettext.py', 'PYMODULE'),
  ('gevent',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\__init__.py',
   'PYMODULE'),
  ('gevent._abstract_linkable',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\_abstract_linkable.py',
   'PYMODULE'),
  ('gevent._compat',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\_compat.py',
   'PYMODULE'),
  ('gevent._config',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\_config.py',
   'PYMODULE'),
  ('gevent._ffi',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\_ffi\\__init__.py',
   'PYMODULE'),
  ('gevent._ffi.callback',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\_ffi\\callback.py',
   'PYMODULE'),
  ('gevent._ffi.loop',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\_ffi\\loop.py',
   'PYMODULE'),
  ('gevent._ffi.watcher',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\_ffi\\watcher.py',
   'PYMODULE'),
  ('gevent._fileobjectcommon',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\_fileobjectcommon.py',
   'PYMODULE'),
  ('gevent._fileobjectposix',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\_fileobjectposix.py',
   'PYMODULE'),
  ('gevent._greenlet_primitives',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\_greenlet_primitives.py',
   'PYMODULE'),
  ('gevent._hub_local',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\_hub_local.py',
   'PYMODULE'),
  ('gevent._hub_primitives',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\_hub_primitives.py',
   'PYMODULE'),
  ('gevent._ident',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\_ident.py',
   'PYMODULE'),
  ('gevent._imap',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\_imap.py',
   'PYMODULE'),
  ('gevent._interfaces',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\_interfaces.py',
   'PYMODULE'),
  ('gevent._monitor',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\_monitor.py',
   'PYMODULE'),
  ('gevent._patcher',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\_patcher.py',
   'PYMODULE'),
  ('gevent._semaphore',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\_semaphore.py',
   'PYMODULE'),
  ('gevent._socket3',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\_socket3.py',
   'PYMODULE'),
  ('gevent._socketcommon',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\_socketcommon.py',
   'PYMODULE'),
  ('gevent._tblib',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\_tblib.py',
   'PYMODULE'),
  ('gevent._threading',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\_threading.py',
   'PYMODULE'),
  ('gevent._tracer',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\_tracer.py',
   'PYMODULE'),
  ('gevent._util',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\_util.py',
   'PYMODULE'),
  ('gevent._waiter',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\_waiter.py',
   'PYMODULE'),
  ('gevent.ares',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\ares.py',
   'PYMODULE'),
  ('gevent.backdoor',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\backdoor.py',
   'PYMODULE'),
  ('gevent.baseserver',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\baseserver.py',
   'PYMODULE'),
  ('gevent.builtins',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\builtins.py',
   'PYMODULE'),
  ('gevent.contextvars',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\contextvars.py',
   'PYMODULE'),
  ('gevent.core',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\core.py',
   'PYMODULE'),
  ('gevent.event',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\event.py',
   'PYMODULE'),
  ('gevent.events',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\events.py',
   'PYMODULE'),
  ('gevent.exceptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\exceptions.py',
   'PYMODULE'),
  ('gevent.fileobject',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\fileobject.py',
   'PYMODULE'),
  ('gevent.greenlet',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\greenlet.py',
   'PYMODULE'),
  ('gevent.hub',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\hub.py',
   'PYMODULE'),
  ('gevent.libev',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\libev\\__init__.py',
   'PYMODULE'),
  ('gevent.libev._corecffi_build',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\libev\\_corecffi_build.py',
   'PYMODULE'),
  ('gevent.libev.corecffi',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\libev\\corecffi.py',
   'PYMODULE'),
  ('gevent.libev.watcher',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\libev\\watcher.py',
   'PYMODULE'),
  ('gevent.libuv',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\libuv\\__init__.py',
   'PYMODULE'),
  ('gevent.libuv._corecffi_build',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\libuv\\_corecffi_build.py',
   'PYMODULE'),
  ('gevent.libuv.loop',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\libuv\\loop.py',
   'PYMODULE'),
  ('gevent.libuv.watcher',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\libuv\\watcher.py',
   'PYMODULE'),
  ('gevent.local',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\local.py',
   'PYMODULE'),
  ('gevent.lock',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\lock.py',
   'PYMODULE'),
  ('gevent.monkey',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\monkey\\__init__.py',
   'PYMODULE'),
  ('gevent.monkey.__main__',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\monkey\\__main__.py',
   'PYMODULE'),
  ('gevent.monkey._errors',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\monkey\\_errors.py',
   'PYMODULE'),
  ('gevent.monkey._main',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\monkey\\_main.py',
   'PYMODULE'),
  ('gevent.monkey._patch_thread_common',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\monkey\\_patch_thread_common.py',
   'PYMODULE'),
  ('gevent.monkey._patch_thread_gte313',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\monkey\\_patch_thread_gte313.py',
   'PYMODULE'),
  ('gevent.monkey._patch_thread_lt313',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\monkey\\_patch_thread_lt313.py',
   'PYMODULE'),
  ('gevent.monkey._state',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\monkey\\_state.py',
   'PYMODULE'),
  ('gevent.monkey._util',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\monkey\\_util.py',
   'PYMODULE'),
  ('gevent.monkey.api',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\monkey\\api.py',
   'PYMODULE'),
  ('gevent.os',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\os.py',
   'PYMODULE'),
  ('gevent.pool',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\pool.py',
   'PYMODULE'),
  ('gevent.pywsgi',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\pywsgi.py',
   'PYMODULE'),
  ('gevent.queue',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\queue.py',
   'PYMODULE'),
  ('gevent.resolver',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\resolver\\__init__.py',
   'PYMODULE'),
  ('gevent.resolver._addresses',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\resolver\\_addresses.py',
   'PYMODULE'),
  ('gevent.resolver._hostsfile',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\resolver\\_hostsfile.py',
   'PYMODULE'),
  ('gevent.resolver.ares',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\resolver\\ares.py',
   'PYMODULE'),
  ('gevent.resolver.blocking',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\resolver\\blocking.py',
   'PYMODULE'),
  ('gevent.resolver.dnspython',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\resolver\\dnspython.py',
   'PYMODULE'),
  ('gevent.resolver.thread',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\resolver\\thread.py',
   'PYMODULE'),
  ('gevent.resolver_ares',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\resolver_ares.py',
   'PYMODULE'),
  ('gevent.resolver_thread',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\resolver_thread.py',
   'PYMODULE'),
  ('gevent.select',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\select.py',
   'PYMODULE'),
  ('gevent.selectors',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\selectors.py',
   'PYMODULE'),
  ('gevent.server',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\server.py',
   'PYMODULE'),
  ('gevent.signal',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\signal.py',
   'PYMODULE'),
  ('gevent.socket',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\socket.py',
   'PYMODULE'),
  ('gevent.ssl',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\ssl.py',
   'PYMODULE'),
  ('gevent.subprocess',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\subprocess.py',
   'PYMODULE'),
  ('gevent.testing',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\testing\\__init__.py',
   'PYMODULE'),
  ('gevent.testing.errorhandler',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\testing\\errorhandler.py',
   'PYMODULE'),
  ('gevent.testing.exception',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\testing\\exception.py',
   'PYMODULE'),
  ('gevent.testing.flaky',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\testing\\flaky.py',
   'PYMODULE'),
  ('gevent.testing.hub',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\testing\\hub.py',
   'PYMODULE'),
  ('gevent.testing.leakcheck',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\testing\\leakcheck.py',
   'PYMODULE'),
  ('gevent.testing.modules',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\testing\\modules.py',
   'PYMODULE'),
  ('gevent.testing.monkey_test',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\testing\\monkey_test.py',
   'PYMODULE'),
  ('gevent.testing.openfiles',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\testing\\openfiles.py',
   'PYMODULE'),
  ('gevent.testing.params',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\testing\\params.py',
   'PYMODULE'),
  ('gevent.testing.patched_tests_setup',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\testing\\patched_tests_setup.py',
   'PYMODULE'),
  ('gevent.testing.resources',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\testing\\resources.py',
   'PYMODULE'),
  ('gevent.testing.six',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\testing\\six.py',
   'PYMODULE'),
  ('gevent.testing.skipping',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\testing\\skipping.py',
   'PYMODULE'),
  ('gevent.testing.sockets',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\testing\\sockets.py',
   'PYMODULE'),
  ('gevent.testing.support',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\testing\\support.py',
   'PYMODULE'),
  ('gevent.testing.switching',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\testing\\switching.py',
   'PYMODULE'),
  ('gevent.testing.sysinfo',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\testing\\sysinfo.py',
   'PYMODULE'),
  ('gevent.testing.testcase',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\testing\\testcase.py',
   'PYMODULE'),
  ('gevent.testing.testrunner',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\testing\\testrunner.py',
   'PYMODULE'),
  ('gevent.testing.timing',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\testing\\timing.py',
   'PYMODULE'),
  ('gevent.testing.travis',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\testing\\travis.py',
   'PYMODULE'),
  ('gevent.testing.util',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\testing\\util.py',
   'PYMODULE'),
  ('gevent.tests',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\__init__.py',
   'PYMODULE'),
  ('gevent.tests.__main__',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\__main__.py',
   'PYMODULE'),
  ('gevent.tests._blocks_at_top_level',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\_blocks_at_top_level.py',
   'PYMODULE'),
  ('gevent.tests._import_import_patch',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\_import_import_patch.py',
   'PYMODULE'),
  ('gevent.tests._import_patch',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\_import_patch.py',
   'PYMODULE'),
  ('gevent.tests._import_wait',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\_import_wait.py',
   'PYMODULE'),
  ('gevent.tests._imports_at_top_level',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\_imports_at_top_level.py',
   'PYMODULE'),
  ('gevent.tests._imports_imports_at_top_level',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\_imports_imports_at_top_level.py',
   'PYMODULE'),
  ('gevent.tests.getaddrinfo_module',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\getaddrinfo_module.py',
   'PYMODULE'),
  ('gevent.tests.known_failures',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\known_failures.py',
   'PYMODULE'),
  ('gevent.tests.lock_tests',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\lock_tests.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\monkey_package\\__init__.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.__main__',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\monkey_package\\__main__.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.issue1526_no_monkey',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\monkey_package\\issue1526_no_monkey.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.issue1526_with_monkey',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\monkey_package\\issue1526_with_monkey.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.issue302monkey',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\monkey_package\\issue302monkey.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.script',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\monkey_package\\script.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.threadpool_monkey_patches',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\monkey_package\\threadpool_monkey_patches.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.threadpool_no_monkey',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\monkey_package\\threadpool_no_monkey.py',
   'PYMODULE'),
  ('gevent.tests.test__GreenletExit',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__GreenletExit.py',
   'PYMODULE'),
  ('gevent.tests.test___config',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test___config.py',
   'PYMODULE'),
  ('gevent.tests.test___ident',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test___ident.py',
   'PYMODULE'),
  ('gevent.tests.test___monitor',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test___monitor.py',
   'PYMODULE'),
  ('gevent.tests.test___monkey_patching',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test___monkey_patching.py',
   'PYMODULE'),
  ('gevent.tests.test__all__',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__all__.py',
   'PYMODULE'),
  ('gevent.tests.test__api',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__api.py',
   'PYMODULE'),
  ('gevent.tests.test__api_timeout',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__api_timeout.py',
   'PYMODULE'),
  ('gevent.tests.test__ares_host_result',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__ares_host_result.py',
   'PYMODULE'),
  ('gevent.tests.test__ares_timeout',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__ares_timeout.py',
   'PYMODULE'),
  ('gevent.tests.test__backdoor',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__backdoor.py',
   'PYMODULE'),
  ('gevent.tests.test__close_backend_fd',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__close_backend_fd.py',
   'PYMODULE'),
  ('gevent.tests.test__compat',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__compat.py',
   'PYMODULE'),
  ('gevent.tests.test__contextvars',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__contextvars.py',
   'PYMODULE'),
  ('gevent.tests.test__core',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__core.py',
   'PYMODULE'),
  ('gevent.tests.test__core_async',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__core_async.py',
   'PYMODULE'),
  ('gevent.tests.test__core_callback',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__core_callback.py',
   'PYMODULE'),
  ('gevent.tests.test__core_fork',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__core_fork.py',
   'PYMODULE'),
  ('gevent.tests.test__core_loop_run',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__core_loop_run.py',
   'PYMODULE'),
  ('gevent.tests.test__core_stat',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__core_stat.py',
   'PYMODULE'),
  ('gevent.tests.test__core_timer',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__core_timer.py',
   'PYMODULE'),
  ('gevent.tests.test__core_watcher',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__core_watcher.py',
   'PYMODULE'),
  ('gevent.tests.test__destroy',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__destroy.py',
   'PYMODULE'),
  ('gevent.tests.test__destroy_default_loop',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__destroy_default_loop.py',
   'PYMODULE'),
  ('gevent.tests.test__doctests',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__doctests.py',
   'PYMODULE'),
  ('gevent.tests.test__environ',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__environ.py',
   'PYMODULE'),
  ('gevent.tests.test__event',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__event.py',
   'PYMODULE'),
  ('gevent.tests.test__events',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__events.py',
   'PYMODULE'),
  ('gevent.tests.test__example_echoserver',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__example_echoserver.py',
   'PYMODULE'),
  ('gevent.tests.test__example_portforwarder',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__example_portforwarder.py',
   'PYMODULE'),
  ('gevent.tests.test__example_udp_client',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__example_udp_client.py',
   'PYMODULE'),
  ('gevent.tests.test__example_udp_server',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__example_udp_server.py',
   'PYMODULE'),
  ('gevent.tests.test__example_webproxy',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__example_webproxy.py',
   'PYMODULE'),
  ('gevent.tests.test__example_wsgiserver',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__example_wsgiserver.py',
   'PYMODULE'),
  ('gevent.tests.test__example_wsgiserver_ssl',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__example_wsgiserver_ssl.py',
   'PYMODULE'),
  ('gevent.tests.test__examples',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__examples.py',
   'PYMODULE'),
  ('gevent.tests.test__exc_info',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__exc_info.py',
   'PYMODULE'),
  ('gevent.tests.test__execmodules',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__execmodules.py',
   'PYMODULE'),
  ('gevent.tests.test__fileobject',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__fileobject.py',
   'PYMODULE'),
  ('gevent.tests.test__getaddrinfo_import',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__getaddrinfo_import.py',
   'PYMODULE'),
  ('gevent.tests.test__greenio',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__greenio.py',
   'PYMODULE'),
  ('gevent.tests.test__greenlet',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__greenlet.py',
   'PYMODULE'),
  ('gevent.tests.test__greenletset',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__greenletset.py',
   'PYMODULE'),
  ('gevent.tests.test__greenness',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__greenness.py',
   'PYMODULE'),
  ('gevent.tests.test__hub',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__hub.py',
   'PYMODULE'),
  ('gevent.tests.test__hub_join',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__hub_join.py',
   'PYMODULE'),
  ('gevent.tests.test__hub_join_timeout',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__hub_join_timeout.py',
   'PYMODULE'),
  ('gevent.tests.test__import_blocking_in_greenlet',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__import_blocking_in_greenlet.py',
   'PYMODULE'),
  ('gevent.tests.test__import_wait',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__import_wait.py',
   'PYMODULE'),
  ('gevent.tests.test__issue112',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__issue112.py',
   'PYMODULE'),
  ('gevent.tests.test__issue1686',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__issue1686.py',
   'PYMODULE'),
  ('gevent.tests.test__issue1864',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__issue1864.py',
   'PYMODULE'),
  ('gevent.tests.test__issue230',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__issue230.py',
   'PYMODULE'),
  ('gevent.tests.test__issue330',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__issue330.py',
   'PYMODULE'),
  ('gevent.tests.test__issue467',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__issue467.py',
   'PYMODULE'),
  ('gevent.tests.test__issue6',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__issue6.py',
   'PYMODULE'),
  ('gevent.tests.test__issue600',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__issue600.py',
   'PYMODULE'),
  ('gevent.tests.test__issue607',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__issue607.py',
   'PYMODULE'),
  ('gevent.tests.test__issue639',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__issue639.py',
   'PYMODULE'),
  ('gevent.tests.test__issue_728',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__issue_728.py',
   'PYMODULE'),
  ('gevent.tests.test__issues461_471',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__issues461_471.py',
   'PYMODULE'),
  ('gevent.tests.test__iwait',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__iwait.py',
   'PYMODULE'),
  ('gevent.tests.test__joinall',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__joinall.py',
   'PYMODULE'),
  ('gevent.tests.test__local',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__local.py',
   'PYMODULE'),
  ('gevent.tests.test__lock',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__lock.py',
   'PYMODULE'),
  ('gevent.tests.test__loop_callback',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__loop_callback.py',
   'PYMODULE'),
  ('gevent.tests.test__makefile_ref',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__makefile_ref.py',
   'PYMODULE'),
  ('gevent.tests.test__memleak',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__memleak.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__monkey.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_builtins_future',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__monkey_builtins_future.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_hub_in_thread',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__monkey_hub_in_thread.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_logging',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__monkey_logging.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_module_run',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__monkey_module_run.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_multiple_imports',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__monkey_multiple_imports.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_queue',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__monkey_queue.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_select',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__monkey_select.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_selectors',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__monkey_selectors.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_sigchld',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__monkey_sigchld.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_sigchld_2',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__monkey_sigchld_2.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_sigchld_3',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__monkey_sigchld_3.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_ssl_warning',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__monkey_ssl_warning.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_ssl_warning2',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__monkey_ssl_warning2.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_ssl_warning3',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__monkey_ssl_warning3.py',
   'PYMODULE'),
  ('gevent.tests.test__nondefaultloop',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__nondefaultloop.py',
   'PYMODULE'),
  ('gevent.tests.test__order',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__order.py',
   'PYMODULE'),
  ('gevent.tests.test__os',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__os.py',
   'PYMODULE'),
  ('gevent.tests.test__pool',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__pool.py',
   'PYMODULE'),
  ('gevent.tests.test__pywsgi',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__pywsgi.py',
   'PYMODULE'),
  ('gevent.tests.test__queue',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__queue.py',
   'PYMODULE'),
  ('gevent.tests.test__real_greenlet',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__real_greenlet.py',
   'PYMODULE'),
  ('gevent.tests.test__refcount',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__refcount.py',
   'PYMODULE'),
  ('gevent.tests.test__refcount_core',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__refcount_core.py',
   'PYMODULE'),
  ('gevent.tests.test__resolver_dnspython',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__resolver_dnspython.py',
   'PYMODULE'),
  ('gevent.tests.test__select',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__select.py',
   'PYMODULE'),
  ('gevent.tests.test__selectors',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__selectors.py',
   'PYMODULE'),
  ('gevent.tests.test__semaphore',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__semaphore.py',
   'PYMODULE'),
  ('gevent.tests.test__server',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__server.py',
   'PYMODULE'),
  ('gevent.tests.test__server_pywsgi',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__server_pywsgi.py',
   'PYMODULE'),
  ('gevent.tests.test__signal',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__signal.py',
   'PYMODULE'),
  ('gevent.tests.test__sleep0',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__sleep0.py',
   'PYMODULE'),
  ('gevent.tests.test__socket',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__socket.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_close',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__socket_close.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_dns',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__socket_dns.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_dns6',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__socket_dns6.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_errors',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__socket_errors.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_ex',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__socket_ex.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_send_memoryview',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__socket_send_memoryview.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_ssl',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__socket_ssl.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_timeout',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__socket_timeout.py',
   'PYMODULE'),
  ('gevent.tests.test__socketpair',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__socketpair.py',
   'PYMODULE'),
  ('gevent.tests.test__ssl',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__ssl.py',
   'PYMODULE'),
  ('gevent.tests.test__subprocess',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__subprocess.py',
   'PYMODULE'),
  ('gevent.tests.test__subprocess_interrupted',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__subprocess_interrupted.py',
   'PYMODULE'),
  ('gevent.tests.test__subprocess_poll',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__subprocess_poll.py',
   'PYMODULE'),
  ('gevent.tests.test__systemerror',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__systemerror.py',
   'PYMODULE'),
  ('gevent.tests.test__thread',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__thread.py',
   'PYMODULE'),
  ('gevent.tests.test__threading',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__threading.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_2',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__threading_2.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_before_monkey',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__threading_before_monkey.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_fork_from_dummy',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__threading_fork_from_dummy.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_holding_lock_while_monkey',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__threading_holding_lock_while_monkey.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_monkey_in_thread',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__threading_monkey_in_thread.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_native_before_monkey',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__threading_native_before_monkey.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_no_monkey',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__threading_no_monkey.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_patched_local',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__threading_patched_local.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_vs_settrace',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__threading_vs_settrace.py',
   'PYMODULE'),
  ('gevent.tests.test__threadpool',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__threadpool.py',
   'PYMODULE'),
  ('gevent.tests.test__threadpool_executor_patched',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__threadpool_executor_patched.py',
   'PYMODULE'),
  ('gevent.tests.test__timeout',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__timeout.py',
   'PYMODULE'),
  ('gevent.tests.test__util',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\tests\\test__util.py',
   'PYMODULE'),
  ('gevent.thread',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\thread.py',
   'PYMODULE'),
  ('gevent.threading',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\threading.py',
   'PYMODULE'),
  ('gevent.threadpool',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\threadpool.py',
   'PYMODULE'),
  ('gevent.time',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\time.py',
   'PYMODULE'),
  ('gevent.timeout',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\timeout.py',
   'PYMODULE'),
  ('gevent.util',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\util.py',
   'PYMODULE'),
  ('gevent.win32util',
   'D:\\pyfile\\py\\Lib\\site-packages\\gevent\\win32util.py',
   'PYMODULE'),
  ('glob', 'D:\\pyfile\\py\\Lib\\glob.py', 'PYMODULE'),
  ('greenlet',
   'D:\\pyfile\\py\\Lib\\site-packages\\greenlet\\__init__.py',
   'PYMODULE'),
  ('gui', 'D:\\编程\\插件开发\\plugin\\register\\gui\\__init__.py', 'PYMODULE'),
  ('gui.register_gui',
   'D:\\编程\\插件开发\\plugin\\register\\gui\\register_gui.py',
   'PYMODULE'),
  ('gui.run_gui',
   'D:\\编程\\插件开发\\plugin\\register\\gui\\run_gui.py',
   'PYMODULE'),
  ('gzip', 'D:\\pyfile\\py\\Lib\\gzip.py', 'PYMODULE'),
  ('h11', 'D:\\pyfile\\py\\Lib\\site-packages\\h11\\__init__.py', 'PYMODULE'),
  ('h11._abnf',
   'D:\\pyfile\\py\\Lib\\site-packages\\h11\\_abnf.py',
   'PYMODULE'),
  ('h11._connection',
   'D:\\pyfile\\py\\Lib\\site-packages\\h11\\_connection.py',
   'PYMODULE'),
  ('h11._events',
   'D:\\pyfile\\py\\Lib\\site-packages\\h11\\_events.py',
   'PYMODULE'),
  ('h11._headers',
   'D:\\pyfile\\py\\Lib\\site-packages\\h11\\_headers.py',
   'PYMODULE'),
  ('h11._readers',
   'D:\\pyfile\\py\\Lib\\site-packages\\h11\\_readers.py',
   'PYMODULE'),
  ('h11._receivebuffer',
   'D:\\pyfile\\py\\Lib\\site-packages\\h11\\_receivebuffer.py',
   'PYMODULE'),
  ('h11._state',
   'D:\\pyfile\\py\\Lib\\site-packages\\h11\\_state.py',
   'PYMODULE'),
  ('h11._util',
   'D:\\pyfile\\py\\Lib\\site-packages\\h11\\_util.py',
   'PYMODULE'),
  ('h11._version',
   'D:\\pyfile\\py\\Lib\\site-packages\\h11\\_version.py',
   'PYMODULE'),
  ('h11._writers',
   'D:\\pyfile\\py\\Lib\\site-packages\\h11\\_writers.py',
   'PYMODULE'),
  ('hashlib', 'D:\\pyfile\\py\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\pyfile\\py\\Lib\\hmac.py', 'PYMODULE'),
  ('html', 'D:\\pyfile\\py\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'D:\\pyfile\\py\\Lib\\html\\entities.py', 'PYMODULE'),
  ('http', 'D:\\pyfile\\py\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'D:\\pyfile\\py\\Lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'D:\\pyfile\\py\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http.cookies', 'D:\\pyfile\\py\\Lib\\http\\cookies.py', 'PYMODULE'),
  ('http.server', 'D:\\pyfile\\py\\Lib\\http\\server.py', 'PYMODULE'),
  ('idna', 'D:\\pyfile\\py\\Lib\\site-packages\\idna\\__init__.py', 'PYMODULE'),
  ('idna.core',
   'D:\\pyfile\\py\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'D:\\pyfile\\py\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\pyfile\\py\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'D:\\pyfile\\py\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'D:\\pyfile\\py\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('imp', 'D:\\pyfile\\py\\Lib\\imp.py', 'PYMODULE'),
  ('importlib', 'D:\\pyfile\\py\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'D:\\pyfile\\py\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\pyfile\\py\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\pyfile\\py\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'D:\\pyfile\\py\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\pyfile\\py\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\pyfile\\py\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\pyfile\\py\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\pyfile\\py\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\pyfile\\py\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\pyfile\\py\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\pyfile\\py\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\pyfile\\py\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\pyfile\\py\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\pyfile\\py\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\pyfile\\py\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\pyfile\\py\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\pyfile\\py\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\pyfile\\py\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\pyfile\\py\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\pyfile\\py\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util', 'D:\\pyfile\\py\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('importlib_metadata',
   'D:\\pyfile\\py\\Lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'D:\\pyfile\\py\\Lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'D:\\pyfile\\py\\Lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'D:\\pyfile\\py\\Lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'D:\\pyfile\\py\\Lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'D:\\pyfile\\py\\Lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'D:\\pyfile\\py\\Lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'D:\\pyfile\\py\\Lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('importlib_metadata._typing',
   'D:\\pyfile\\py\\Lib\\site-packages\\importlib_metadata\\_typing.py',
   'PYMODULE'),
  ('importlib_metadata.compat',
   'D:\\pyfile\\py\\Lib\\site-packages\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py311',
   'D:\\pyfile\\py\\Lib\\site-packages\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py39',
   'D:\\pyfile\\py\\Lib\\site-packages\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('inspect', 'D:\\pyfile\\py\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\pyfile\\py\\Lib\\ipaddress.py', 'PYMODULE'),
  ('ipykernel',
   'D:\\pyfile\\py\\Lib\\site-packages\\ipykernel\\__init__.py',
   'PYMODULE'),
  ('ipykernel._eventloop_macos',
   'D:\\pyfile\\py\\Lib\\site-packages\\ipykernel\\_eventloop_macos.py',
   'PYMODULE'),
  ('ipykernel._version',
   'D:\\pyfile\\py\\Lib\\site-packages\\ipykernel\\_version.py',
   'PYMODULE'),
  ('ipykernel.comm',
   'D:\\pyfile\\py\\Lib\\site-packages\\ipykernel\\comm\\__init__.py',
   'PYMODULE'),
  ('ipykernel.comm.comm',
   'D:\\pyfile\\py\\Lib\\site-packages\\ipykernel\\comm\\comm.py',
   'PYMODULE'),
  ('ipykernel.comm.manager',
   'D:\\pyfile\\py\\Lib\\site-packages\\ipykernel\\comm\\manager.py',
   'PYMODULE'),
  ('ipykernel.compiler',
   'D:\\pyfile\\py\\Lib\\site-packages\\ipykernel\\compiler.py',
   'PYMODULE'),
  ('ipykernel.connect',
   'D:\\pyfile\\py\\Lib\\site-packages\\ipykernel\\connect.py',
   'PYMODULE'),
  ('ipykernel.control',
   'D:\\pyfile\\py\\Lib\\site-packages\\ipykernel\\control.py',
   'PYMODULE'),
  ('ipykernel.debugger',
   'D:\\pyfile\\py\\Lib\\site-packages\\ipykernel\\debugger.py',
   'PYMODULE'),
  ('ipykernel.displayhook',
   'D:\\pyfile\\py\\Lib\\site-packages\\ipykernel\\displayhook.py',
   'PYMODULE'),
  ('ipykernel.embed',
   'D:\\pyfile\\py\\Lib\\site-packages\\ipykernel\\embed.py',
   'PYMODULE'),
  ('ipykernel.eventloops',
   'D:\\pyfile\\py\\Lib\\site-packages\\ipykernel\\eventloops.py',
   'PYMODULE'),
  ('ipykernel.gui',
   'D:\\pyfile\\py\\Lib\\site-packages\\ipykernel\\gui\\__init__.py',
   'PYMODULE'),
  ('ipykernel.gui.gtk3embed',
   'D:\\pyfile\\py\\Lib\\site-packages\\ipykernel\\gui\\gtk3embed.py',
   'PYMODULE'),
  ('ipykernel.gui.gtkembed',
   'D:\\pyfile\\py\\Lib\\site-packages\\ipykernel\\gui\\gtkembed.py',
   'PYMODULE'),
  ('ipykernel.heartbeat',
   'D:\\pyfile\\py\\Lib\\site-packages\\ipykernel\\heartbeat.py',
   'PYMODULE'),
  ('ipykernel.iostream',
   'D:\\pyfile\\py\\Lib\\site-packages\\ipykernel\\iostream.py',
   'PYMODULE'),
  ('ipykernel.ipkernel',
   'D:\\pyfile\\py\\Lib\\site-packages\\ipykernel\\ipkernel.py',
   'PYMODULE'),
  ('ipykernel.jsonutil',
   'D:\\pyfile\\py\\Lib\\site-packages\\ipykernel\\jsonutil.py',
   'PYMODULE'),
  ('ipykernel.kernelapp',
   'D:\\pyfile\\py\\Lib\\site-packages\\ipykernel\\kernelapp.py',
   'PYMODULE'),
  ('ipykernel.kernelbase',
   'D:\\pyfile\\py\\Lib\\site-packages\\ipykernel\\kernelbase.py',
   'PYMODULE'),
  ('ipykernel.kernelspec',
   'D:\\pyfile\\py\\Lib\\site-packages\\ipykernel\\kernelspec.py',
   'PYMODULE'),
  ('ipykernel.parentpoller',
   'D:\\pyfile\\py\\Lib\\site-packages\\ipykernel\\parentpoller.py',
   'PYMODULE'),
  ('ipykernel.pickleutil',
   'D:\\pyfile\\py\\Lib\\site-packages\\ipykernel\\pickleutil.py',
   'PYMODULE'),
  ('ipykernel.serialize',
   'D:\\pyfile\\py\\Lib\\site-packages\\ipykernel\\serialize.py',
   'PYMODULE'),
  ('ipykernel.trio_runner',
   'D:\\pyfile\\py\\Lib\\site-packages\\ipykernel\\trio_runner.py',
   'PYMODULE'),
  ('ipykernel.zmqshell',
   'D:\\pyfile\\py\\Lib\\site-packages\\ipykernel\\zmqshell.py',
   'PYMODULE'),
  ('isoduration',
   'D:\\pyfile\\py\\Lib\\site-packages\\isoduration\\__init__.py',
   'PYMODULE'),
  ('isoduration.constants',
   'D:\\pyfile\\py\\Lib\\site-packages\\isoduration\\constants.py',
   'PYMODULE'),
  ('isoduration.formatter',
   'D:\\pyfile\\py\\Lib\\site-packages\\isoduration\\formatter\\__init__.py',
   'PYMODULE'),
  ('isoduration.formatter.checking',
   'D:\\pyfile\\py\\Lib\\site-packages\\isoduration\\formatter\\checking.py',
   'PYMODULE'),
  ('isoduration.formatter.exceptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\isoduration\\formatter\\exceptions.py',
   'PYMODULE'),
  ('isoduration.formatter.formatting',
   'D:\\pyfile\\py\\Lib\\site-packages\\isoduration\\formatter\\formatting.py',
   'PYMODULE'),
  ('isoduration.operations',
   'D:\\pyfile\\py\\Lib\\site-packages\\isoduration\\operations\\__init__.py',
   'PYMODULE'),
  ('isoduration.operations.util',
   'D:\\pyfile\\py\\Lib\\site-packages\\isoduration\\operations\\util.py',
   'PYMODULE'),
  ('isoduration.parser',
   'D:\\pyfile\\py\\Lib\\site-packages\\isoduration\\parser\\__init__.py',
   'PYMODULE'),
  ('isoduration.parser.exceptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\isoduration\\parser\\exceptions.py',
   'PYMODULE'),
  ('isoduration.parser.parsing',
   'D:\\pyfile\\py\\Lib\\site-packages\\isoduration\\parser\\parsing.py',
   'PYMODULE'),
  ('isoduration.parser.util',
   'D:\\pyfile\\py\\Lib\\site-packages\\isoduration\\parser\\util.py',
   'PYMODULE'),
  ('isoduration.types',
   'D:\\pyfile\\py\\Lib\\site-packages\\isoduration\\types.py',
   'PYMODULE'),
  ('jedi', 'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\__init__.py', 'PYMODULE'),
  ('jedi._compatibility',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\_compatibility.py',
   'PYMODULE'),
  ('jedi.api',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\api\\__init__.py',
   'PYMODULE'),
  ('jedi.api.classes',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\api\\classes.py',
   'PYMODULE'),
  ('jedi.api.completion',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\api\\completion.py',
   'PYMODULE'),
  ('jedi.api.completion_cache',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\api\\completion_cache.py',
   'PYMODULE'),
  ('jedi.api.environment',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\api\\environment.py',
   'PYMODULE'),
  ('jedi.api.errors',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\api\\errors.py',
   'PYMODULE'),
  ('jedi.api.exceptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\api\\exceptions.py',
   'PYMODULE'),
  ('jedi.api.file_name',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\api\\file_name.py',
   'PYMODULE'),
  ('jedi.api.helpers',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\api\\helpers.py',
   'PYMODULE'),
  ('jedi.api.interpreter',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\api\\interpreter.py',
   'PYMODULE'),
  ('jedi.api.keywords',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\api\\keywords.py',
   'PYMODULE'),
  ('jedi.api.project',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\api\\project.py',
   'PYMODULE'),
  ('jedi.api.refactoring',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\api\\refactoring\\__init__.py',
   'PYMODULE'),
  ('jedi.api.refactoring.extract',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\api\\refactoring\\extract.py',
   'PYMODULE'),
  ('jedi.api.strings',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\api\\strings.py',
   'PYMODULE'),
  ('jedi.cache',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\cache.py',
   'PYMODULE'),
  ('jedi.common',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\common.py',
   'PYMODULE'),
  ('jedi.debug',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\debug.py',
   'PYMODULE'),
  ('jedi.file_io',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\file_io.py',
   'PYMODULE'),
  ('jedi.inference',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.analysis',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\analysis.py',
   'PYMODULE'),
  ('jedi.inference.arguments',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\arguments.py',
   'PYMODULE'),
  ('jedi.inference.base_value',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\base_value.py',
   'PYMODULE'),
  ('jedi.inference.cache',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\cache.py',
   'PYMODULE'),
  ('jedi.inference.compiled',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\compiled\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.compiled.access',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\compiled\\access.py',
   'PYMODULE'),
  ('jedi.inference.compiled.getattr_static',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\compiled\\getattr_static.py',
   'PYMODULE'),
  ('jedi.inference.compiled.mixed',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\compiled\\mixed.py',
   'PYMODULE'),
  ('jedi.inference.compiled.subprocess',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\compiled\\subprocess\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.compiled.subprocess.functions',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\compiled\\subprocess\\functions.py',
   'PYMODULE'),
  ('jedi.inference.compiled.value',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\compiled\\value.py',
   'PYMODULE'),
  ('jedi.inference.context',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\context.py',
   'PYMODULE'),
  ('jedi.inference.docstring_utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\docstring_utils.py',
   'PYMODULE'),
  ('jedi.inference.docstrings',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\docstrings.py',
   'PYMODULE'),
  ('jedi.inference.dynamic_params',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\dynamic_params.py',
   'PYMODULE'),
  ('jedi.inference.filters',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\filters.py',
   'PYMODULE'),
  ('jedi.inference.finder',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\finder.py',
   'PYMODULE'),
  ('jedi.inference.flow_analysis',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\flow_analysis.py',
   'PYMODULE'),
  ('jedi.inference.gradual',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\gradual\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.gradual.annotation',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\gradual\\annotation.py',
   'PYMODULE'),
  ('jedi.inference.gradual.base',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\gradual\\base.py',
   'PYMODULE'),
  ('jedi.inference.gradual.conversion',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\gradual\\conversion.py',
   'PYMODULE'),
  ('jedi.inference.gradual.generics',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\gradual\\generics.py',
   'PYMODULE'),
  ('jedi.inference.gradual.stub_value',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\gradual\\stub_value.py',
   'PYMODULE'),
  ('jedi.inference.gradual.type_var',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\gradual\\type_var.py',
   'PYMODULE'),
  ('jedi.inference.gradual.typeshed',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\gradual\\typeshed.py',
   'PYMODULE'),
  ('jedi.inference.gradual.typing',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\gradual\\typing.py',
   'PYMODULE'),
  ('jedi.inference.gradual.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\gradual\\utils.py',
   'PYMODULE'),
  ('jedi.inference.helpers',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\helpers.py',
   'PYMODULE'),
  ('jedi.inference.imports',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\imports.py',
   'PYMODULE'),
  ('jedi.inference.lazy_value',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\lazy_value.py',
   'PYMODULE'),
  ('jedi.inference.names',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\names.py',
   'PYMODULE'),
  ('jedi.inference.param',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\param.py',
   'PYMODULE'),
  ('jedi.inference.parser_cache',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\parser_cache.py',
   'PYMODULE'),
  ('jedi.inference.recursion',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\recursion.py',
   'PYMODULE'),
  ('jedi.inference.references',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\references.py',
   'PYMODULE'),
  ('jedi.inference.signature',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\signature.py',
   'PYMODULE'),
  ('jedi.inference.star_args',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\star_args.py',
   'PYMODULE'),
  ('jedi.inference.syntax_tree',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\syntax_tree.py',
   'PYMODULE'),
  ('jedi.inference.sys_path',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\sys_path.py',
   'PYMODULE'),
  ('jedi.inference.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\utils.py',
   'PYMODULE'),
  ('jedi.inference.value',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\value\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.value.decorator',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\value\\decorator.py',
   'PYMODULE'),
  ('jedi.inference.value.dynamic_arrays',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\value\\dynamic_arrays.py',
   'PYMODULE'),
  ('jedi.inference.value.function',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\value\\function.py',
   'PYMODULE'),
  ('jedi.inference.value.instance',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\value\\instance.py',
   'PYMODULE'),
  ('jedi.inference.value.iterable',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\value\\iterable.py',
   'PYMODULE'),
  ('jedi.inference.value.klass',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\value\\klass.py',
   'PYMODULE'),
  ('jedi.inference.value.module',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\value\\module.py',
   'PYMODULE'),
  ('jedi.inference.value.namespace',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\inference\\value\\namespace.py',
   'PYMODULE'),
  ('jedi.parser_utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\parser_utils.py',
   'PYMODULE'),
  ('jedi.plugins',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\plugins\\__init__.py',
   'PYMODULE'),
  ('jedi.plugins.django',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\plugins\\django.py',
   'PYMODULE'),
  ('jedi.plugins.flask',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\plugins\\flask.py',
   'PYMODULE'),
  ('jedi.plugins.pytest',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\plugins\\pytest.py',
   'PYMODULE'),
  ('jedi.plugins.registry',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\plugins\\registry.py',
   'PYMODULE'),
  ('jedi.plugins.stdlib',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\plugins\\stdlib.py',
   'PYMODULE'),
  ('jedi.settings',
   'D:\\pyfile\\py\\Lib\\site-packages\\jedi\\settings.py',
   'PYMODULE'),
  ('jinja2',
   'D:\\pyfile\\py\\Lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'D:\\pyfile\\py\\Lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'D:\\pyfile\\py\\Lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'D:\\pyfile\\py\\Lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'D:\\pyfile\\py\\Lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'D:\\pyfile\\py\\Lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'D:\\pyfile\\py\\Lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'D:\\pyfile\\py\\Lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   'D:\\pyfile\\py\\Lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   'D:\\pyfile\\py\\Lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'D:\\pyfile\\py\\Lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'D:\\pyfile\\py\\Lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'D:\\pyfile\\py\\Lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'D:\\pyfile\\py\\Lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'D:\\pyfile\\py\\Lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'D:\\pyfile\\py\\Lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'D:\\pyfile\\py\\Lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'D:\\pyfile\\py\\Lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'D:\\pyfile\\py\\Lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'D:\\pyfile\\py\\Lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json', 'D:\\pyfile\\py\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'D:\\pyfile\\py\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'D:\\pyfile\\py\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\pyfile\\py\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('jsonpointer',
   'D:\\pyfile\\py\\Lib\\site-packages\\jsonpointer.py',
   'PYMODULE'),
  ('jsonschema',
   'D:\\pyfile\\py\\Lib\\site-packages\\jsonschema\\__init__.py',
   'PYMODULE'),
  ('jsonschema._format',
   'D:\\pyfile\\py\\Lib\\site-packages\\jsonschema\\_format.py',
   'PYMODULE'),
  ('jsonschema._keywords',
   'D:\\pyfile\\py\\Lib\\site-packages\\jsonschema\\_keywords.py',
   'PYMODULE'),
  ('jsonschema._legacy_keywords',
   'D:\\pyfile\\py\\Lib\\site-packages\\jsonschema\\_legacy_keywords.py',
   'PYMODULE'),
  ('jsonschema._types',
   'D:\\pyfile\\py\\Lib\\site-packages\\jsonschema\\_types.py',
   'PYMODULE'),
  ('jsonschema._typing',
   'D:\\pyfile\\py\\Lib\\site-packages\\jsonschema\\_typing.py',
   'PYMODULE'),
  ('jsonschema._utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\jsonschema\\_utils.py',
   'PYMODULE'),
  ('jsonschema.exceptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\jsonschema\\exceptions.py',
   'PYMODULE'),
  ('jsonschema.protocols',
   'D:\\pyfile\\py\\Lib\\site-packages\\jsonschema\\protocols.py',
   'PYMODULE'),
  ('jsonschema.validators',
   'D:\\pyfile\\py\\Lib\\site-packages\\jsonschema\\validators.py',
   'PYMODULE'),
  ('jsonschema_specifications',
   'D:\\pyfile\\py\\Lib\\site-packages\\jsonschema_specifications\\__init__.py',
   'PYMODULE'),
  ('jsonschema_specifications._core',
   'D:\\pyfile\\py\\Lib\\site-packages\\jsonschema_specifications\\_core.py',
   'PYMODULE'),
  ('jupyter_client',
   'D:\\pyfile\\py\\Lib\\site-packages\\jupyter_client\\__init__.py',
   'PYMODULE'),
  ('jupyter_client._version',
   'D:\\pyfile\\py\\Lib\\site-packages\\jupyter_client\\_version.py',
   'PYMODULE'),
  ('jupyter_client.adapter',
   'D:\\pyfile\\py\\Lib\\site-packages\\jupyter_client\\adapter.py',
   'PYMODULE'),
  ('jupyter_client.asynchronous',
   'D:\\pyfile\\py\\Lib\\site-packages\\jupyter_client\\asynchronous\\__init__.py',
   'PYMODULE'),
  ('jupyter_client.asynchronous.client',
   'D:\\pyfile\\py\\Lib\\site-packages\\jupyter_client\\asynchronous\\client.py',
   'PYMODULE'),
  ('jupyter_client.blocking',
   'D:\\pyfile\\py\\Lib\\site-packages\\jupyter_client\\blocking\\__init__.py',
   'PYMODULE'),
  ('jupyter_client.blocking.client',
   'D:\\pyfile\\py\\Lib\\site-packages\\jupyter_client\\blocking\\client.py',
   'PYMODULE'),
  ('jupyter_client.channels',
   'D:\\pyfile\\py\\Lib\\site-packages\\jupyter_client\\channels.py',
   'PYMODULE'),
  ('jupyter_client.channelsabc',
   'D:\\pyfile\\py\\Lib\\site-packages\\jupyter_client\\channelsabc.py',
   'PYMODULE'),
  ('jupyter_client.client',
   'D:\\pyfile\\py\\Lib\\site-packages\\jupyter_client\\client.py',
   'PYMODULE'),
  ('jupyter_client.clientabc',
   'D:\\pyfile\\py\\Lib\\site-packages\\jupyter_client\\clientabc.py',
   'PYMODULE'),
  ('jupyter_client.connect',
   'D:\\pyfile\\py\\Lib\\site-packages\\jupyter_client\\connect.py',
   'PYMODULE'),
  ('jupyter_client.jsonutil',
   'D:\\pyfile\\py\\Lib\\site-packages\\jupyter_client\\jsonutil.py',
   'PYMODULE'),
  ('jupyter_client.kernelspec',
   'D:\\pyfile\\py\\Lib\\site-packages\\jupyter_client\\kernelspec.py',
   'PYMODULE'),
  ('jupyter_client.launcher',
   'D:\\pyfile\\py\\Lib\\site-packages\\jupyter_client\\launcher.py',
   'PYMODULE'),
  ('jupyter_client.localinterfaces',
   'D:\\pyfile\\py\\Lib\\site-packages\\jupyter_client\\localinterfaces.py',
   'PYMODULE'),
  ('jupyter_client.manager',
   'D:\\pyfile\\py\\Lib\\site-packages\\jupyter_client\\manager.py',
   'PYMODULE'),
  ('jupyter_client.managerabc',
   'D:\\pyfile\\py\\Lib\\site-packages\\jupyter_client\\managerabc.py',
   'PYMODULE'),
  ('jupyter_client.multikernelmanager',
   'D:\\pyfile\\py\\Lib\\site-packages\\jupyter_client\\multikernelmanager.py',
   'PYMODULE'),
  ('jupyter_client.provisioning',
   'D:\\pyfile\\py\\Lib\\site-packages\\jupyter_client\\provisioning\\__init__.py',
   'PYMODULE'),
  ('jupyter_client.provisioning.factory',
   'D:\\pyfile\\py\\Lib\\site-packages\\jupyter_client\\provisioning\\factory.py',
   'PYMODULE'),
  ('jupyter_client.provisioning.local_provisioner',
   'D:\\pyfile\\py\\Lib\\site-packages\\jupyter_client\\provisioning\\local_provisioner.py',
   'PYMODULE'),
  ('jupyter_client.provisioning.provisioner_base',
   'D:\\pyfile\\py\\Lib\\site-packages\\jupyter_client\\provisioning\\provisioner_base.py',
   'PYMODULE'),
  ('jupyter_client.session',
   'D:\\pyfile\\py\\Lib\\site-packages\\jupyter_client\\session.py',
   'PYMODULE'),
  ('jupyter_client.ssh',
   'D:\\pyfile\\py\\Lib\\site-packages\\jupyter_client\\ssh\\__init__.py',
   'PYMODULE'),
  ('jupyter_client.ssh.forward',
   'D:\\pyfile\\py\\Lib\\site-packages\\jupyter_client\\ssh\\forward.py',
   'PYMODULE'),
  ('jupyter_client.ssh.tunnel',
   'D:\\pyfile\\py\\Lib\\site-packages\\jupyter_client\\ssh\\tunnel.py',
   'PYMODULE'),
  ('jupyter_client.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\jupyter_client\\utils.py',
   'PYMODULE'),
  ('jupyter_client.win_interrupt',
   'D:\\pyfile\\py\\Lib\\site-packages\\jupyter_client\\win_interrupt.py',
   'PYMODULE'),
  ('jupyter_core',
   'D:\\pyfile\\py\\Lib\\site-packages\\jupyter_core\\__init__.py',
   'PYMODULE'),
  ('jupyter_core.paths',
   'D:\\pyfile\\py\\Lib\\site-packages\\jupyter_core\\paths.py',
   'PYMODULE'),
  ('jupyter_core.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\jupyter_core\\utils\\__init__.py',
   'PYMODULE'),
  ('jupyter_core.version',
   'D:\\pyfile\\py\\Lib\\site-packages\\jupyter_core\\version.py',
   'PYMODULE'),
  ('kiwisolver',
   'D:\\pyfile\\py\\Lib\\site-packages\\kiwisolver\\__init__.py',
   'PYMODULE'),
  ('kiwisolver.exceptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\kiwisolver\\exceptions.py',
   'PYMODULE'),
  ('logging', 'D:\\pyfile\\py\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('logging.config', 'D:\\pyfile\\py\\Lib\\logging\\config.py', 'PYMODULE'),
  ('logging.handlers', 'D:\\pyfile\\py\\Lib\\logging\\handlers.py', 'PYMODULE'),
  ('lzma', 'D:\\pyfile\\py\\Lib\\lzma.py', 'PYMODULE'),
  ('markupsafe',
   'D:\\pyfile\\py\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'D:\\pyfile\\py\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('matplotlib',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\__init__.py',
   'PYMODULE'),
  ('matplotlib._afm',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\_afm.py',
   'PYMODULE'),
  ('matplotlib._api',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\_api\\__init__.py',
   'PYMODULE'),
  ('matplotlib._api.deprecation',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\_api\\deprecation.py',
   'PYMODULE'),
  ('matplotlib._blocking_input',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\_blocking_input.py',
   'PYMODULE'),
  ('matplotlib._cm',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\_cm.py',
   'PYMODULE'),
  ('matplotlib._cm_listed',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\_cm_listed.py',
   'PYMODULE'),
  ('matplotlib._color_data',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\_color_data.py',
   'PYMODULE'),
  ('matplotlib._constrained_layout',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\_constrained_layout.py',
   'PYMODULE'),
  ('matplotlib._docstring',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\_docstring.py',
   'PYMODULE'),
  ('matplotlib._enums',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\_enums.py',
   'PYMODULE'),
  ('matplotlib._fontconfig_pattern',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\_fontconfig_pattern.py',
   'PYMODULE'),
  ('matplotlib._layoutgrid',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\_layoutgrid.py',
   'PYMODULE'),
  ('matplotlib._mathtext',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\_mathtext.py',
   'PYMODULE'),
  ('matplotlib._mathtext_data',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\_mathtext_data.py',
   'PYMODULE'),
  ('matplotlib._pylab_helpers',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\_pylab_helpers.py',
   'PYMODULE'),
  ('matplotlib._text_helpers',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\_text_helpers.py',
   'PYMODULE'),
  ('matplotlib._tight_bbox',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\_tight_bbox.py',
   'PYMODULE'),
  ('matplotlib._tight_layout',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\_tight_layout.py',
   'PYMODULE'),
  ('matplotlib._version',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\_version.py',
   'PYMODULE'),
  ('matplotlib.artist',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\artist.py',
   'PYMODULE'),
  ('matplotlib.axes',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\axes\\__init__.py',
   'PYMODULE'),
  ('matplotlib.axes._axes',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\axes\\_axes.py',
   'PYMODULE'),
  ('matplotlib.axes._base',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\axes\\_base.py',
   'PYMODULE'),
  ('matplotlib.axes._secondary_axes',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\axes\\_secondary_axes.py',
   'PYMODULE'),
  ('matplotlib.axis',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\axis.py',
   'PYMODULE'),
  ('matplotlib.backend_bases',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\backend_bases.py',
   'PYMODULE'),
  ('matplotlib.backend_managers',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\backend_managers.py',
   'PYMODULE'),
  ('matplotlib.backend_tools',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\backend_tools.py',
   'PYMODULE'),
  ('matplotlib.backends',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\backends\\__init__.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_agg',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\backends\\backend_agg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_qt',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\backends\\backend_qt.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_qtagg',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\backends\\backend_qtagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\backends\\backend_webagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg_core',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\backends\\backend_webagg_core.py',
   'PYMODULE'),
  ('matplotlib.backends.qt_compat',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\backends\\qt_compat.py',
   'PYMODULE'),
  ('matplotlib.backends.qt_editor',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\backends\\qt_editor\\__init__.py',
   'PYMODULE'),
  ('matplotlib.backends.qt_editor._formlayout',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\backends\\qt_editor\\_formlayout.py',
   'PYMODULE'),
  ('matplotlib.backends.qt_editor.figureoptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\backends\\qt_editor\\figureoptions.py',
   'PYMODULE'),
  ('matplotlib.bezier',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\bezier.py',
   'PYMODULE'),
  ('matplotlib.category',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\category.py',
   'PYMODULE'),
  ('matplotlib.cbook',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\cbook.py',
   'PYMODULE'),
  ('matplotlib.cm',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\cm.py',
   'PYMODULE'),
  ('matplotlib.collections',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\collections.py',
   'PYMODULE'),
  ('matplotlib.colorbar',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\colorbar.py',
   'PYMODULE'),
  ('matplotlib.colors',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\colors.py',
   'PYMODULE'),
  ('matplotlib.container',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\container.py',
   'PYMODULE'),
  ('matplotlib.contour',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\contour.py',
   'PYMODULE'),
  ('matplotlib.dates',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\dates.py',
   'PYMODULE'),
  ('matplotlib.dviread',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\dviread.py',
   'PYMODULE'),
  ('matplotlib.figure',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\figure.py',
   'PYMODULE'),
  ('matplotlib.font_manager',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\font_manager.py',
   'PYMODULE'),
  ('matplotlib.gridspec',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\gridspec.py',
   'PYMODULE'),
  ('matplotlib.hatch',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\hatch.py',
   'PYMODULE'),
  ('matplotlib.image',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\image.py',
   'PYMODULE'),
  ('matplotlib.layout_engine',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\layout_engine.py',
   'PYMODULE'),
  ('matplotlib.legend',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\legend.py',
   'PYMODULE'),
  ('matplotlib.legend_handler',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\legend_handler.py',
   'PYMODULE'),
  ('matplotlib.lines',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\lines.py',
   'PYMODULE'),
  ('matplotlib.markers',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\markers.py',
   'PYMODULE'),
  ('matplotlib.mathtext',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\mathtext.py',
   'PYMODULE'),
  ('matplotlib.mlab',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\mlab.py',
   'PYMODULE'),
  ('matplotlib.offsetbox',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\offsetbox.py',
   'PYMODULE'),
  ('matplotlib.patches',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\patches.py',
   'PYMODULE'),
  ('matplotlib.path',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\path.py',
   'PYMODULE'),
  ('matplotlib.patheffects',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\patheffects.py',
   'PYMODULE'),
  ('matplotlib.projections',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\projections\\__init__.py',
   'PYMODULE'),
  ('matplotlib.projections.geo',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\projections\\geo.py',
   'PYMODULE'),
  ('matplotlib.projections.polar',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\projections\\polar.py',
   'PYMODULE'),
  ('matplotlib.pyplot',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\pyplot.py',
   'PYMODULE'),
  ('matplotlib.quiver',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\quiver.py',
   'PYMODULE'),
  ('matplotlib.rcsetup',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\rcsetup.py',
   'PYMODULE'),
  ('matplotlib.scale',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\scale.py',
   'PYMODULE'),
  ('matplotlib.spines',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\spines.py',
   'PYMODULE'),
  ('matplotlib.stackplot',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\stackplot.py',
   'PYMODULE'),
  ('matplotlib.streamplot',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\streamplot.py',
   'PYMODULE'),
  ('matplotlib.style',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\style\\__init__.py',
   'PYMODULE'),
  ('matplotlib.style.core',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\style\\core.py',
   'PYMODULE'),
  ('matplotlib.table',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\table.py',
   'PYMODULE'),
  ('matplotlib.texmanager',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\texmanager.py',
   'PYMODULE'),
  ('matplotlib.text',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\text.py',
   'PYMODULE'),
  ('matplotlib.textpath',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\textpath.py',
   'PYMODULE'),
  ('matplotlib.ticker',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\ticker.py',
   'PYMODULE'),
  ('matplotlib.transforms',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\transforms.py',
   'PYMODULE'),
  ('matplotlib.tri',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\tri\\__init__.py',
   'PYMODULE'),
  ('matplotlib.tri._triangulation',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\tri\\_triangulation.py',
   'PYMODULE'),
  ('matplotlib.tri._tricontour',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\tri\\_tricontour.py',
   'PYMODULE'),
  ('matplotlib.tri._trifinder',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\tri\\_trifinder.py',
   'PYMODULE'),
  ('matplotlib.tri._triinterpolate',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\tri\\_triinterpolate.py',
   'PYMODULE'),
  ('matplotlib.tri._tripcolor',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\tri\\_tripcolor.py',
   'PYMODULE'),
  ('matplotlib.tri._triplot',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\tri\\_triplot.py',
   'PYMODULE'),
  ('matplotlib.tri._trirefine',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\tri\\_trirefine.py',
   'PYMODULE'),
  ('matplotlib.tri._tritools',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\tri\\_tritools.py',
   'PYMODULE'),
  ('matplotlib.typing',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\typing.py',
   'PYMODULE'),
  ('matplotlib.units',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\units.py',
   'PYMODULE'),
  ('matplotlib.widgets',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib\\widgets.py',
   'PYMODULE'),
  ('matplotlib_inline',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib_inline\\__init__.py',
   'PYMODULE'),
  ('matplotlib_inline.backend_inline',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib_inline\\backend_inline.py',
   'PYMODULE'),
  ('matplotlib_inline.config',
   'D:\\pyfile\\py\\Lib\\site-packages\\matplotlib_inline\\config.py',
   'PYMODULE'),
  ('mimetypes', 'D:\\pyfile\\py\\Lib\\mimetypes.py', 'PYMODULE'),
  ('mpl_toolkits',
   'D:\\pyfile\\py\\Lib\\site-packages\\mpl_toolkits\\__init__.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d',
   'D:\\pyfile\\py\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\__init__.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.art3d',
   'D:\\pyfile\\py\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\art3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.axes3d',
   'D:\\pyfile\\py\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\axes3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.axis3d',
   'D:\\pyfile\\py\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\axis3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.proj3d',
   'D:\\pyfile\\py\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\proj3d.py',
   'PYMODULE'),
  ('multiprocessing',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('nacl', 'D:\\pyfile\\py\\Lib\\site-packages\\nacl\\__init__.py', 'PYMODULE'),
  ('nacl.bindings',
   'D:\\pyfile\\py\\Lib\\site-packages\\nacl\\bindings\\__init__.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_aead',
   'D:\\pyfile\\py\\Lib\\site-packages\\nacl\\bindings\\crypto_aead.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_box',
   'D:\\pyfile\\py\\Lib\\site-packages\\nacl\\bindings\\crypto_box.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_core',
   'D:\\pyfile\\py\\Lib\\site-packages\\nacl\\bindings\\crypto_core.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_generichash',
   'D:\\pyfile\\py\\Lib\\site-packages\\nacl\\bindings\\crypto_generichash.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_hash',
   'D:\\pyfile\\py\\Lib\\site-packages\\nacl\\bindings\\crypto_hash.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_kx',
   'D:\\pyfile\\py\\Lib\\site-packages\\nacl\\bindings\\crypto_kx.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_pwhash',
   'D:\\pyfile\\py\\Lib\\site-packages\\nacl\\bindings\\crypto_pwhash.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_scalarmult',
   'D:\\pyfile\\py\\Lib\\site-packages\\nacl\\bindings\\crypto_scalarmult.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_secretbox',
   'D:\\pyfile\\py\\Lib\\site-packages\\nacl\\bindings\\crypto_secretbox.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_secretstream',
   'D:\\pyfile\\py\\Lib\\site-packages\\nacl\\bindings\\crypto_secretstream.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_shorthash',
   'D:\\pyfile\\py\\Lib\\site-packages\\nacl\\bindings\\crypto_shorthash.py',
   'PYMODULE'),
  ('nacl.bindings.crypto_sign',
   'D:\\pyfile\\py\\Lib\\site-packages\\nacl\\bindings\\crypto_sign.py',
   'PYMODULE'),
  ('nacl.bindings.randombytes',
   'D:\\pyfile\\py\\Lib\\site-packages\\nacl\\bindings\\randombytes.py',
   'PYMODULE'),
  ('nacl.bindings.sodium_core',
   'D:\\pyfile\\py\\Lib\\site-packages\\nacl\\bindings\\sodium_core.py',
   'PYMODULE'),
  ('nacl.bindings.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\nacl\\bindings\\utils.py',
   'PYMODULE'),
  ('nacl.encoding',
   'D:\\pyfile\\py\\Lib\\site-packages\\nacl\\encoding.py',
   'PYMODULE'),
  ('nacl.exceptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\nacl\\exceptions.py',
   'PYMODULE'),
  ('nacl.public',
   'D:\\pyfile\\py\\Lib\\site-packages\\nacl\\public.py',
   'PYMODULE'),
  ('nacl.signing',
   'D:\\pyfile\\py\\Lib\\site-packages\\nacl\\signing.py',
   'PYMODULE'),
  ('nacl.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\nacl\\utils.py',
   'PYMODULE'),
  ('nbformat',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\__init__.py',
   'PYMODULE'),
  ('nbformat._imports',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\_imports.py',
   'PYMODULE'),
  ('nbformat._struct',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\_struct.py',
   'PYMODULE'),
  ('nbformat._version',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\_version.py',
   'PYMODULE'),
  ('nbformat.converter',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\converter.py',
   'PYMODULE'),
  ('nbformat.corpus',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\corpus\\__init__.py',
   'PYMODULE'),
  ('nbformat.corpus.words',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\corpus\\words.py',
   'PYMODULE'),
  ('nbformat.json_compat',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\json_compat.py',
   'PYMODULE'),
  ('nbformat.notebooknode',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\notebooknode.py',
   'PYMODULE'),
  ('nbformat.reader',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\reader.py',
   'PYMODULE'),
  ('nbformat.sentinel',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\sentinel.py',
   'PYMODULE'),
  ('nbformat.v1',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\v1\\__init__.py',
   'PYMODULE'),
  ('nbformat.v1.convert',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\v1\\convert.py',
   'PYMODULE'),
  ('nbformat.v1.nbbase',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\v1\\nbbase.py',
   'PYMODULE'),
  ('nbformat.v1.nbjson',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\v1\\nbjson.py',
   'PYMODULE'),
  ('nbformat.v1.rwbase',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\v1\\rwbase.py',
   'PYMODULE'),
  ('nbformat.v2',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\v2\\__init__.py',
   'PYMODULE'),
  ('nbformat.v2.convert',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\v2\\convert.py',
   'PYMODULE'),
  ('nbformat.v2.nbbase',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\v2\\nbbase.py',
   'PYMODULE'),
  ('nbformat.v2.nbjson',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\v2\\nbjson.py',
   'PYMODULE'),
  ('nbformat.v2.nbpy',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\v2\\nbpy.py',
   'PYMODULE'),
  ('nbformat.v2.nbxml',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\v2\\nbxml.py',
   'PYMODULE'),
  ('nbformat.v2.rwbase',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\v2\\rwbase.py',
   'PYMODULE'),
  ('nbformat.v3',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\v3\\__init__.py',
   'PYMODULE'),
  ('nbformat.v3.convert',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\v3\\convert.py',
   'PYMODULE'),
  ('nbformat.v3.nbbase',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\v3\\nbbase.py',
   'PYMODULE'),
  ('nbformat.v3.nbjson',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\v3\\nbjson.py',
   'PYMODULE'),
  ('nbformat.v3.nbpy',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\v3\\nbpy.py',
   'PYMODULE'),
  ('nbformat.v3.rwbase',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\v3\\rwbase.py',
   'PYMODULE'),
  ('nbformat.v4',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\v4\\__init__.py',
   'PYMODULE'),
  ('nbformat.v4.convert',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\v4\\convert.py',
   'PYMODULE'),
  ('nbformat.v4.nbbase',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\v4\\nbbase.py',
   'PYMODULE'),
  ('nbformat.v4.nbjson',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\v4\\nbjson.py',
   'PYMODULE'),
  ('nbformat.v4.rwbase',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\v4\\rwbase.py',
   'PYMODULE'),
  ('nbformat.validator',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\validator.py',
   'PYMODULE'),
  ('nbformat.warnings',
   'D:\\pyfile\\py\\Lib\\site-packages\\nbformat\\warnings.py',
   'PYMODULE'),
  ('nest_asyncio',
   'D:\\pyfile\\py\\Lib\\site-packages\\nest_asyncio.py',
   'PYMODULE'),
  ('netrc', 'D:\\pyfile\\py\\Lib\\netrc.py', 'PYMODULE'),
  ('ntsecuritycon',
   'D:\\pyfile\\py\\Lib\\site-packages\\win32\\lib\\ntsecuritycon.py',
   'PYMODULE'),
  ('nturl2path', 'D:\\pyfile\\py\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'D:\\pyfile\\py\\Lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.array_api',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._indexing_functions',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\array_api\\_indexing_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.compat',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.core.records',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.testing',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\pyfile\\py\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('onnxruntime',
   'D:\\pyfile\\py\\Lib\\site-packages\\onnxruntime\\__init__.py',
   'PYMODULE'),
  ('onnxruntime.capi',
   'D:\\pyfile\\py\\Lib\\site-packages\\onnxruntime\\capi\\__init__.py',
   'PYMODULE'),
  ('onnxruntime.capi._ld_preload',
   'D:\\pyfile\\py\\Lib\\site-packages\\onnxruntime\\capi\\_ld_preload.py',
   'PYMODULE'),
  ('onnxruntime.capi._pybind_state',
   'D:\\pyfile\\py\\Lib\\site-packages\\onnxruntime\\capi\\_pybind_state.py',
   'PYMODULE'),
  ('onnxruntime.capi.onnxruntime_collect_build_info',
   'D:\\pyfile\\py\\Lib\\site-packages\\onnxruntime\\capi\\onnxruntime_collect_build_info.py',
   'PYMODULE'),
  ('onnxruntime.capi.onnxruntime_inference_collection',
   'D:\\pyfile\\py\\Lib\\site-packages\\onnxruntime\\capi\\onnxruntime_inference_collection.py',
   'PYMODULE'),
  ('onnxruntime.capi.onnxruntime_validation',
   'D:\\pyfile\\py\\Lib\\site-packages\\onnxruntime\\capi\\onnxruntime_validation.py',
   'PYMODULE'),
  ('onnxruntime.capi.training',
   'D:\\pyfile\\py\\Lib\\site-packages\\onnxruntime\\capi\\training\\__init__.py',
   'PYMODULE'),
  ('onnxruntime.capi.version_info',
   'D:\\pyfile\\py\\Lib\\site-packages\\onnxruntime\\capi\\version_info.py',
   'PYMODULE'),
  ('opcode', 'D:\\pyfile\\py\\Lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'D:\\pyfile\\py\\Lib\\optparse.py', 'PYMODULE'),
  ('outcome',
   'D:\\pyfile\\py\\Lib\\site-packages\\outcome\\__init__.py',
   'PYMODULE'),
  ('outcome._impl',
   'D:\\pyfile\\py\\Lib\\site-packages\\outcome\\_impl.py',
   'PYMODULE'),
  ('outcome._util',
   'D:\\pyfile\\py\\Lib\\site-packages\\outcome\\_util.py',
   'PYMODULE'),
  ('outcome._version',
   'D:\\pyfile\\py\\Lib\\site-packages\\outcome\\_version.py',
   'PYMODULE'),
  ('packaging',
   'D:\\pyfile\\py\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\pyfile\\py\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\pyfile\\py\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'D:\\pyfile\\py\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\pyfile\\py\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\pyfile\\py\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\pyfile\\py\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'D:\\pyfile\\py\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'D:\\pyfile\\py\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'D:\\pyfile\\py\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'D:\\pyfile\\py\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\pyfile\\py\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'D:\\pyfile\\py\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\pyfile\\py\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\pyfile\\py\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('paramiko',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\__init__.py',
   'PYMODULE'),
  ('paramiko._version',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\_version.py',
   'PYMODULE'),
  ('paramiko._winapi',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\_winapi.py',
   'PYMODULE'),
  ('paramiko.agent',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\agent.py',
   'PYMODULE'),
  ('paramiko.auth_handler',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\auth_handler.py',
   'PYMODULE'),
  ('paramiko.auth_strategy',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\auth_strategy.py',
   'PYMODULE'),
  ('paramiko.ber',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\ber.py',
   'PYMODULE'),
  ('paramiko.buffered_pipe',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\buffered_pipe.py',
   'PYMODULE'),
  ('paramiko.channel',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\channel.py',
   'PYMODULE'),
  ('paramiko.client',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\client.py',
   'PYMODULE'),
  ('paramiko.common',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\common.py',
   'PYMODULE'),
  ('paramiko.compress',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\compress.py',
   'PYMODULE'),
  ('paramiko.config',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\config.py',
   'PYMODULE'),
  ('paramiko.dsskey',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\dsskey.py',
   'PYMODULE'),
  ('paramiko.ecdsakey',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\ecdsakey.py',
   'PYMODULE'),
  ('paramiko.ed25519key',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\ed25519key.py',
   'PYMODULE'),
  ('paramiko.file',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\file.py',
   'PYMODULE'),
  ('paramiko.hostkeys',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\hostkeys.py',
   'PYMODULE'),
  ('paramiko.kex_curve25519',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\kex_curve25519.py',
   'PYMODULE'),
  ('paramiko.kex_ecdh_nist',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\kex_ecdh_nist.py',
   'PYMODULE'),
  ('paramiko.kex_gex',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\kex_gex.py',
   'PYMODULE'),
  ('paramiko.kex_group1',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\kex_group1.py',
   'PYMODULE'),
  ('paramiko.kex_group14',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\kex_group14.py',
   'PYMODULE'),
  ('paramiko.kex_group16',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\kex_group16.py',
   'PYMODULE'),
  ('paramiko.kex_gss',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\kex_gss.py',
   'PYMODULE'),
  ('paramiko.message',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\message.py',
   'PYMODULE'),
  ('paramiko.packet',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\packet.py',
   'PYMODULE'),
  ('paramiko.pipe',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\pipe.py',
   'PYMODULE'),
  ('paramiko.pkey',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\pkey.py',
   'PYMODULE'),
  ('paramiko.primes',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\primes.py',
   'PYMODULE'),
  ('paramiko.proxy',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\proxy.py',
   'PYMODULE'),
  ('paramiko.rsakey',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\rsakey.py',
   'PYMODULE'),
  ('paramiko.server',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\server.py',
   'PYMODULE'),
  ('paramiko.sftp',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\sftp.py',
   'PYMODULE'),
  ('paramiko.sftp_attr',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\sftp_attr.py',
   'PYMODULE'),
  ('paramiko.sftp_client',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\sftp_client.py',
   'PYMODULE'),
  ('paramiko.sftp_file',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\sftp_file.py',
   'PYMODULE'),
  ('paramiko.sftp_handle',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\sftp_handle.py',
   'PYMODULE'),
  ('paramiko.sftp_server',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\sftp_server.py',
   'PYMODULE'),
  ('paramiko.sftp_si',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\sftp_si.py',
   'PYMODULE'),
  ('paramiko.ssh_exception',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\ssh_exception.py',
   'PYMODULE'),
  ('paramiko.ssh_gss',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\ssh_gss.py',
   'PYMODULE'),
  ('paramiko.transport',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\transport.py',
   'PYMODULE'),
  ('paramiko.util',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\util.py',
   'PYMODULE'),
  ('paramiko.win_openssh',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\win_openssh.py',
   'PYMODULE'),
  ('paramiko.win_pageant',
   'D:\\pyfile\\py\\Lib\\site-packages\\paramiko\\win_pageant.py',
   'PYMODULE'),
  ('parso',
   'D:\\pyfile\\py\\Lib\\site-packages\\parso\\__init__.py',
   'PYMODULE'),
  ('parso._compatibility',
   'D:\\pyfile\\py\\Lib\\site-packages\\parso\\_compatibility.py',
   'PYMODULE'),
  ('parso.cache',
   'D:\\pyfile\\py\\Lib\\site-packages\\parso\\cache.py',
   'PYMODULE'),
  ('parso.file_io',
   'D:\\pyfile\\py\\Lib\\site-packages\\parso\\file_io.py',
   'PYMODULE'),
  ('parso.grammar',
   'D:\\pyfile\\py\\Lib\\site-packages\\parso\\grammar.py',
   'PYMODULE'),
  ('parso.normalizer',
   'D:\\pyfile\\py\\Lib\\site-packages\\parso\\normalizer.py',
   'PYMODULE'),
  ('parso.parser',
   'D:\\pyfile\\py\\Lib\\site-packages\\parso\\parser.py',
   'PYMODULE'),
  ('parso.pgen2',
   'D:\\pyfile\\py\\Lib\\site-packages\\parso\\pgen2\\__init__.py',
   'PYMODULE'),
  ('parso.pgen2.generator',
   'D:\\pyfile\\py\\Lib\\site-packages\\parso\\pgen2\\generator.py',
   'PYMODULE'),
  ('parso.pgen2.grammar_parser',
   'D:\\pyfile\\py\\Lib\\site-packages\\parso\\pgen2\\grammar_parser.py',
   'PYMODULE'),
  ('parso.python',
   'D:\\pyfile\\py\\Lib\\site-packages\\parso\\python\\__init__.py',
   'PYMODULE'),
  ('parso.python.diff',
   'D:\\pyfile\\py\\Lib\\site-packages\\parso\\python\\diff.py',
   'PYMODULE'),
  ('parso.python.errors',
   'D:\\pyfile\\py\\Lib\\site-packages\\parso\\python\\errors.py',
   'PYMODULE'),
  ('parso.python.parser',
   'D:\\pyfile\\py\\Lib\\site-packages\\parso\\python\\parser.py',
   'PYMODULE'),
  ('parso.python.pep8',
   'D:\\pyfile\\py\\Lib\\site-packages\\parso\\python\\pep8.py',
   'PYMODULE'),
  ('parso.python.prefix',
   'D:\\pyfile\\py\\Lib\\site-packages\\parso\\python\\prefix.py',
   'PYMODULE'),
  ('parso.python.token',
   'D:\\pyfile\\py\\Lib\\site-packages\\parso\\python\\token.py',
   'PYMODULE'),
  ('parso.python.tokenize',
   'D:\\pyfile\\py\\Lib\\site-packages\\parso\\python\\tokenize.py',
   'PYMODULE'),
  ('parso.python.tree',
   'D:\\pyfile\\py\\Lib\\site-packages\\parso\\python\\tree.py',
   'PYMODULE'),
  ('parso.tree',
   'D:\\pyfile\\py\\Lib\\site-packages\\parso\\tree.py',
   'PYMODULE'),
  ('parso.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\parso\\utils.py',
   'PYMODULE'),
  ('pathlib', 'D:\\pyfile\\py\\Lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'D:\\pyfile\\py\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'D:\\pyfile\\py\\Lib\\pickle.py', 'PYMODULE'),
  ('pkg_resources',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.appdirs',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\appdirs.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._adapters',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._common',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._compat',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._itertools',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._legacy',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.abc',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.readers',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.simple',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.context',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.functools',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.text',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.more',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.recipes',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.__about__',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._manylinux',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._musllinux',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.actions',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.common',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.core',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\core.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.diagram',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.exceptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.helpers',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.results',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\results.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.testing',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.unicode',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.util',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\util.py',
   'PYMODULE'),
  ('pkg_resources._vendor.zipp',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\_vendor\\zipp.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'D:\\pyfile\\py\\Lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('pkgutil', 'D:\\pyfile\\py\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'D:\\pyfile\\py\\Lib\\platform.py', 'PYMODULE'),
  ('platformdirs',
   'D:\\pyfile\\py\\Lib\\site-packages\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('platformdirs.android',
   'D:\\pyfile\\py\\Lib\\site-packages\\platformdirs\\android.py',
   'PYMODULE'),
  ('platformdirs.api',
   'D:\\pyfile\\py\\Lib\\site-packages\\platformdirs\\api.py',
   'PYMODULE'),
  ('platformdirs.macos',
   'D:\\pyfile\\py\\Lib\\site-packages\\platformdirs\\macos.py',
   'PYMODULE'),
  ('platformdirs.unix',
   'D:\\pyfile\\py\\Lib\\site-packages\\platformdirs\\unix.py',
   'PYMODULE'),
  ('platformdirs.version',
   'D:\\pyfile\\py\\Lib\\site-packages\\platformdirs\\version.py',
   'PYMODULE'),
  ('platformdirs.windows',
   'D:\\pyfile\\py\\Lib\\site-packages\\platformdirs\\windows.py',
   'PYMODULE'),
  ('plistlib', 'D:\\pyfile\\py\\Lib\\plistlib.py', 'PYMODULE'),
  ('pprint', 'D:\\pyfile\\py\\Lib\\pprint.py', 'PYMODULE'),
  ('profile', 'D:\\pyfile\\py\\Lib\\profile.py', 'PYMODULE'),
  ('prompt_toolkit',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.application',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\application\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.application.application',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\application\\application.py',
   'PYMODULE'),
  ('prompt_toolkit.application.current',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\application\\current.py',
   'PYMODULE'),
  ('prompt_toolkit.application.dummy',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\application\\dummy.py',
   'PYMODULE'),
  ('prompt_toolkit.application.run_in_terminal',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\application\\run_in_terminal.py',
   'PYMODULE'),
  ('prompt_toolkit.auto_suggest',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\auto_suggest.py',
   'PYMODULE'),
  ('prompt_toolkit.buffer',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\buffer.py',
   'PYMODULE'),
  ('prompt_toolkit.cache',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\cache.py',
   'PYMODULE'),
  ('prompt_toolkit.clipboard',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\clipboard\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.clipboard.base',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\clipboard\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.clipboard.in_memory',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\clipboard\\in_memory.py',
   'PYMODULE'),
  ('prompt_toolkit.completion',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\completion\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.base',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\completion\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.deduplicate',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\completion\\deduplicate.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.filesystem',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\completion\\filesystem.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.fuzzy_completer',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\completion\\fuzzy_completer.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.nested',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\completion\\nested.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.word_completer',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\completion\\word_completer.py',
   'PYMODULE'),
  ('prompt_toolkit.cursor_shapes',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\cursor_shapes.py',
   'PYMODULE'),
  ('prompt_toolkit.data_structures',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\data_structures.py',
   'PYMODULE'),
  ('prompt_toolkit.document',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\document.py',
   'PYMODULE'),
  ('prompt_toolkit.enums',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\enums.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\eventloop\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.async_generator',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\eventloop\\async_generator.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.inputhook',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\eventloop\\inputhook.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\eventloop\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.win32',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\eventloop\\win32.py',
   'PYMODULE'),
  ('prompt_toolkit.filters',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\filters\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.app',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\filters\\app.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.base',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\filters\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.cli',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\filters\\cli.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\filters\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\formatted_text\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.ansi',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\formatted_text\\ansi.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.base',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\formatted_text\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.html',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\formatted_text\\html.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.pygments',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\formatted_text\\pygments.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\formatted_text\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.history',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\history.py',
   'PYMODULE'),
  ('prompt_toolkit.input',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\input\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.input.ansi_escape_sequences',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\input\\ansi_escape_sequences.py',
   'PYMODULE'),
  ('prompt_toolkit.input.base',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\input\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.input.defaults',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\input\\defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.input.posix_pipe',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\input\\posix_pipe.py',
   'PYMODULE'),
  ('prompt_toolkit.input.posix_utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\input\\posix_utils.py',
   'PYMODULE'),
  ('prompt_toolkit.input.typeahead',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\input\\typeahead.py',
   'PYMODULE'),
  ('prompt_toolkit.input.vt100',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\input\\vt100.py',
   'PYMODULE'),
  ('prompt_toolkit.input.vt100_parser',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\input\\vt100_parser.py',
   'PYMODULE'),
  ('prompt_toolkit.input.win32',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\input\\win32.py',
   'PYMODULE'),
  ('prompt_toolkit.input.win32_pipe',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\input\\win32_pipe.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\key_binding\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.auto_suggest',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\auto_suggest.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.basic',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\basic.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.completion',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\completion.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.cpr',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\cpr.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.emacs',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\emacs.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.focus',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\focus.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.mouse',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\mouse.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.named_commands',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\named_commands.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.open_in_editor',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\open_in_editor.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.page_navigation',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\page_navigation.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.scroll',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\scroll.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.search',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\search.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.vi',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\vi.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.defaults',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\key_binding\\defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.digraphs',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\key_binding\\digraphs.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.emacs_state',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\key_binding\\emacs_state.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.key_bindings',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\key_binding\\key_bindings.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.key_processor',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\key_binding\\key_processor.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.vi_state',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\key_binding\\vi_state.py',
   'PYMODULE'),
  ('prompt_toolkit.keys',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\keys.py',
   'PYMODULE'),
  ('prompt_toolkit.layout',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\layout\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.containers',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\layout\\containers.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.controls',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\layout\\controls.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.dimension',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\layout\\dimension.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.dummy',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\layout\\dummy.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.layout',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\layout\\layout.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.margins',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\layout\\margins.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.menus',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\layout\\menus.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.mouse_handlers',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\layout\\mouse_handlers.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.processors',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\layout\\processors.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.screen',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\layout\\screen.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.scrollable_pane',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\layout\\scrollable_pane.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\layout\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.lexers',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\lexers\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.lexers.base',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\lexers\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.lexers.pygments',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\lexers\\pygments.py',
   'PYMODULE'),
  ('prompt_toolkit.mouse_events',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\mouse_events.py',
   'PYMODULE'),
  ('prompt_toolkit.output',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\output\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.output.base',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\output\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.output.color_depth',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\output\\color_depth.py',
   'PYMODULE'),
  ('prompt_toolkit.output.conemu',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\output\\conemu.py',
   'PYMODULE'),
  ('prompt_toolkit.output.defaults',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\output\\defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.output.flush_stdout',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\output\\flush_stdout.py',
   'PYMODULE'),
  ('prompt_toolkit.output.plain_text',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\output\\plain_text.py',
   'PYMODULE'),
  ('prompt_toolkit.output.vt100',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\output\\vt100.py',
   'PYMODULE'),
  ('prompt_toolkit.output.win32',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\output\\win32.py',
   'PYMODULE'),
  ('prompt_toolkit.output.windows10',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\output\\windows10.py',
   'PYMODULE'),
  ('prompt_toolkit.patch_stdout',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\patch_stdout.py',
   'PYMODULE'),
  ('prompt_toolkit.renderer',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\renderer.py',
   'PYMODULE'),
  ('prompt_toolkit.search',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\search.py',
   'PYMODULE'),
  ('prompt_toolkit.selection',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\selection.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\shortcuts\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.dialogs',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\shortcuts\\dialogs.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.progress_bar',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\shortcuts\\progress_bar\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.progress_bar.base',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\shortcuts\\progress_bar\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.progress_bar.formatters',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\shortcuts\\progress_bar\\formatters.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.prompt',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\shortcuts\\prompt.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\shortcuts\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.styles',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\styles\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.base',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\styles\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.defaults',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\styles\\defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.named_colors',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\styles\\named_colors.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.pygments',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\styles\\pygments.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.style',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\styles\\style.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.style_transformation',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\styles\\style_transformation.py',
   'PYMODULE'),
  ('prompt_toolkit.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.validation',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\validation.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\widgets\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.base',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\widgets\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.dialogs',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\widgets\\dialogs.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.menus',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\widgets\\menus.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.toolbars',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\widgets\\toolbars.py',
   'PYMODULE'),
  ('prompt_toolkit.win32_types',
   'D:\\pyfile\\py\\Lib\\site-packages\\prompt_toolkit\\win32_types.py',
   'PYMODULE'),
  ('pstats', 'D:\\pyfile\\py\\Lib\\pstats.py', 'PYMODULE'),
  ('psutil',
   'D:\\pyfile\\py\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'D:\\pyfile\\py\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'D:\\pyfile\\py\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('pure_eval',
   'D:\\pyfile\\py\\Lib\\site-packages\\pure_eval\\__init__.py',
   'PYMODULE'),
  ('pure_eval.core',
   'D:\\pyfile\\py\\Lib\\site-packages\\pure_eval\\core.py',
   'PYMODULE'),
  ('pure_eval.my_getattr_static',
   'D:\\pyfile\\py\\Lib\\site-packages\\pure_eval\\my_getattr_static.py',
   'PYMODULE'),
  ('pure_eval.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\pure_eval\\utils.py',
   'PYMODULE'),
  ('pure_eval.version',
   'D:\\pyfile\\py\\Lib\\site-packages\\pure_eval\\version.py',
   'PYMODULE'),
  ('py_compile', 'D:\\pyfile\\py\\Lib\\py_compile.py', 'PYMODULE'),
  ('pyasn1',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyasn1\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyasn1\\codec\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.ber',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyasn1\\codec\\ber\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.ber.decoder',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyasn1\\codec\\ber\\decoder.py',
   'PYMODULE'),
  ('pyasn1.codec.ber.encoder',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyasn1\\codec\\ber\\encoder.py',
   'PYMODULE'),
  ('pyasn1.codec.ber.eoo',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyasn1\\codec\\ber\\eoo.py',
   'PYMODULE'),
  ('pyasn1.codec.cer',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyasn1\\codec\\cer\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.cer.decoder',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyasn1\\codec\\cer\\decoder.py',
   'PYMODULE'),
  ('pyasn1.codec.cer.encoder',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyasn1\\codec\\cer\\encoder.py',
   'PYMODULE'),
  ('pyasn1.codec.der',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyasn1\\codec\\der\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.der.decoder',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyasn1\\codec\\der\\decoder.py',
   'PYMODULE'),
  ('pyasn1.codec.der.encoder',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyasn1\\codec\\der\\encoder.py',
   'PYMODULE'),
  ('pyasn1.codec.streaming',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyasn1\\codec\\streaming.py',
   'PYMODULE'),
  ('pyasn1.compat',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyasn1\\compat\\__init__.py',
   'PYMODULE'),
  ('pyasn1.compat.integer',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyasn1\\compat\\integer.py',
   'PYMODULE'),
  ('pyasn1.compat.octets',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyasn1\\compat\\octets.py',
   'PYMODULE'),
  ('pyasn1.debug',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyasn1\\debug.py',
   'PYMODULE'),
  ('pyasn1.error',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyasn1\\error.py',
   'PYMODULE'),
  ('pyasn1.type',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyasn1\\type\\__init__.py',
   'PYMODULE'),
  ('pyasn1.type.base',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyasn1\\type\\base.py',
   'PYMODULE'),
  ('pyasn1.type.char',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyasn1\\type\\char.py',
   'PYMODULE'),
  ('pyasn1.type.constraint',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyasn1\\type\\constraint.py',
   'PYMODULE'),
  ('pyasn1.type.error',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyasn1\\type\\error.py',
   'PYMODULE'),
  ('pyasn1.type.namedtype',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyasn1\\type\\namedtype.py',
   'PYMODULE'),
  ('pyasn1.type.namedval',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyasn1\\type\\namedval.py',
   'PYMODULE'),
  ('pyasn1.type.tag',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyasn1\\type\\tag.py',
   'PYMODULE'),
  ('pyasn1.type.tagmap',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyasn1\\type\\tagmap.py',
   'PYMODULE'),
  ('pyasn1.type.univ',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyasn1\\type\\univ.py',
   'PYMODULE'),
  ('pyasn1.type.useful',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyasn1\\type\\useful.py',
   'PYMODULE'),
  ('pycparser',
   'D:\\pyfile\\py\\Lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'D:\\pyfile\\py\\Lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'D:\\pyfile\\py\\Lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'D:\\pyfile\\py\\Lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'D:\\pyfile\\py\\Lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'D:\\pyfile\\py\\Lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'D:\\pyfile\\py\\Lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'D:\\pyfile\\py\\Lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'D:\\pyfile\\py\\Lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'D:\\pyfile\\py\\Lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'D:\\pyfile\\py\\Lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pydoc', 'D:\\pyfile\\py\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data', 'D:\\pyfile\\py\\Lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\pyfile\\py\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pygments',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\__init__.py',
   'PYMODULE'),
  ('pygments.console',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\console.py',
   'PYMODULE'),
  ('pygments.filter',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\filter.py',
   'PYMODULE'),
  ('pygments.filters',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\filters\\__init__.py',
   'PYMODULE'),
  ('pygments.formatter',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\formatter.py',
   'PYMODULE'),
  ('pygments.formatters',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\formatters\\__init__.py',
   'PYMODULE'),
  ('pygments.formatters._mapping',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\formatters\\_mapping.py',
   'PYMODULE'),
  ('pygments.formatters.bbcode',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\formatters\\bbcode.py',
   'PYMODULE'),
  ('pygments.formatters.groff',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\formatters\\groff.py',
   'PYMODULE'),
  ('pygments.formatters.html',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\formatters\\html.py',
   'PYMODULE'),
  ('pygments.formatters.img',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\formatters\\img.py',
   'PYMODULE'),
  ('pygments.formatters.irc',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\formatters\\irc.py',
   'PYMODULE'),
  ('pygments.formatters.latex',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\formatters\\latex.py',
   'PYMODULE'),
  ('pygments.formatters.other',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\formatters\\other.py',
   'PYMODULE'),
  ('pygments.formatters.pangomarkup',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\formatters\\pangomarkup.py',
   'PYMODULE'),
  ('pygments.formatters.rtf',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\formatters\\rtf.py',
   'PYMODULE'),
  ('pygments.formatters.svg',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\formatters\\svg.py',
   'PYMODULE'),
  ('pygments.formatters.terminal',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\formatters\\terminal.py',
   'PYMODULE'),
  ('pygments.formatters.terminal256',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\formatters\\terminal256.py',
   'PYMODULE'),
  ('pygments.lexer',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexer.py',
   'PYMODULE'),
  ('pygments.lexers',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\__init__.py',
   'PYMODULE'),
  ('pygments.lexers._ada_builtins',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\_ada_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._asy_builtins',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\_asy_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._cl_builtins',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\_cl_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._cocoa_builtins',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\_cocoa_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._csound_builtins',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\_csound_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._css_builtins',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\_css_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._googlesql_builtins',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\_googlesql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._julia_builtins',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\_julia_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lasso_builtins',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\_lasso_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lilypond_builtins',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\_lilypond_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lua_builtins',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\_lua_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._luau_builtins',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\_luau_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._mapping',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\_mapping.py',
   'PYMODULE'),
  ('pygments.lexers._mql_builtins',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\_mql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._mysql_builtins',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\_mysql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._openedge_builtins',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\_openedge_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._php_builtins',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\_php_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._postgres_builtins',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\_postgres_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._qlik_builtins',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\_qlik_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._scheme_builtins',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\_scheme_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._scilab_builtins',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\_scilab_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._sourcemod_builtins',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\_sourcemod_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._sql_builtins',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\_sql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._stan_builtins',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\_stan_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._stata_builtins',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\_stata_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._tsql_builtins',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\_tsql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._usd_builtins',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\_usd_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._vbscript_builtins',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\_vbscript_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._vim_builtins',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\_vim_builtins.py',
   'PYMODULE'),
  ('pygments.lexers.actionscript',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\actionscript.py',
   'PYMODULE'),
  ('pygments.lexers.ada',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\ada.py',
   'PYMODULE'),
  ('pygments.lexers.agile',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\agile.py',
   'PYMODULE'),
  ('pygments.lexers.algebra',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\algebra.py',
   'PYMODULE'),
  ('pygments.lexers.ambient',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\ambient.py',
   'PYMODULE'),
  ('pygments.lexers.amdgpu',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\amdgpu.py',
   'PYMODULE'),
  ('pygments.lexers.ampl',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\ampl.py',
   'PYMODULE'),
  ('pygments.lexers.apdlexer',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\apdlexer.py',
   'PYMODULE'),
  ('pygments.lexers.apl',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\apl.py',
   'PYMODULE'),
  ('pygments.lexers.archetype',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\archetype.py',
   'PYMODULE'),
  ('pygments.lexers.arrow',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\arrow.py',
   'PYMODULE'),
  ('pygments.lexers.arturo',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\arturo.py',
   'PYMODULE'),
  ('pygments.lexers.asc',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\asc.py',
   'PYMODULE'),
  ('pygments.lexers.asm',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\asm.py',
   'PYMODULE'),
  ('pygments.lexers.asn1',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\asn1.py',
   'PYMODULE'),
  ('pygments.lexers.automation',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\automation.py',
   'PYMODULE'),
  ('pygments.lexers.bare',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\bare.py',
   'PYMODULE'),
  ('pygments.lexers.basic',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\basic.py',
   'PYMODULE'),
  ('pygments.lexers.bdd',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\bdd.py',
   'PYMODULE'),
  ('pygments.lexers.berry',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\berry.py',
   'PYMODULE'),
  ('pygments.lexers.bibtex',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\bibtex.py',
   'PYMODULE'),
  ('pygments.lexers.blueprint',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\blueprint.py',
   'PYMODULE'),
  ('pygments.lexers.boa',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\boa.py',
   'PYMODULE'),
  ('pygments.lexers.bqn',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\bqn.py',
   'PYMODULE'),
  ('pygments.lexers.business',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\business.py',
   'PYMODULE'),
  ('pygments.lexers.c_cpp',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\c_cpp.py',
   'PYMODULE'),
  ('pygments.lexers.c_like',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\c_like.py',
   'PYMODULE'),
  ('pygments.lexers.capnproto',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\capnproto.py',
   'PYMODULE'),
  ('pygments.lexers.carbon',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\carbon.py',
   'PYMODULE'),
  ('pygments.lexers.cddl',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\cddl.py',
   'PYMODULE'),
  ('pygments.lexers.chapel',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\chapel.py',
   'PYMODULE'),
  ('pygments.lexers.clean',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\clean.py',
   'PYMODULE'),
  ('pygments.lexers.codeql',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\codeql.py',
   'PYMODULE'),
  ('pygments.lexers.comal',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\comal.py',
   'PYMODULE'),
  ('pygments.lexers.compiled',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\compiled.py',
   'PYMODULE'),
  ('pygments.lexers.configs',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\configs.py',
   'PYMODULE'),
  ('pygments.lexers.console',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\console.py',
   'PYMODULE'),
  ('pygments.lexers.cplint',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\cplint.py',
   'PYMODULE'),
  ('pygments.lexers.crystal',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\crystal.py',
   'PYMODULE'),
  ('pygments.lexers.csound',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\csound.py',
   'PYMODULE'),
  ('pygments.lexers.css',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\css.py',
   'PYMODULE'),
  ('pygments.lexers.d',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\d.py',
   'PYMODULE'),
  ('pygments.lexers.dalvik',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\dalvik.py',
   'PYMODULE'),
  ('pygments.lexers.data',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\data.py',
   'PYMODULE'),
  ('pygments.lexers.dax',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\dax.py',
   'PYMODULE'),
  ('pygments.lexers.devicetree',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\devicetree.py',
   'PYMODULE'),
  ('pygments.lexers.diff',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\diff.py',
   'PYMODULE'),
  ('pygments.lexers.dns',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\dns.py',
   'PYMODULE'),
  ('pygments.lexers.dotnet',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\dotnet.py',
   'PYMODULE'),
  ('pygments.lexers.dsls',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\dsls.py',
   'PYMODULE'),
  ('pygments.lexers.dylan',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\dylan.py',
   'PYMODULE'),
  ('pygments.lexers.ecl',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\ecl.py',
   'PYMODULE'),
  ('pygments.lexers.eiffel',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\eiffel.py',
   'PYMODULE'),
  ('pygments.lexers.elm',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\elm.py',
   'PYMODULE'),
  ('pygments.lexers.elpi',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\elpi.py',
   'PYMODULE'),
  ('pygments.lexers.email',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\email.py',
   'PYMODULE'),
  ('pygments.lexers.erlang',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\erlang.py',
   'PYMODULE'),
  ('pygments.lexers.esoteric',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\esoteric.py',
   'PYMODULE'),
  ('pygments.lexers.ezhil',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\ezhil.py',
   'PYMODULE'),
  ('pygments.lexers.factor',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\factor.py',
   'PYMODULE'),
  ('pygments.lexers.fantom',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\fantom.py',
   'PYMODULE'),
  ('pygments.lexers.felix',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\felix.py',
   'PYMODULE'),
  ('pygments.lexers.fift',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\fift.py',
   'PYMODULE'),
  ('pygments.lexers.floscript',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\floscript.py',
   'PYMODULE'),
  ('pygments.lexers.forth',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\forth.py',
   'PYMODULE'),
  ('pygments.lexers.fortran',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\fortran.py',
   'PYMODULE'),
  ('pygments.lexers.foxpro',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\foxpro.py',
   'PYMODULE'),
  ('pygments.lexers.freefem',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\freefem.py',
   'PYMODULE'),
  ('pygments.lexers.func',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\func.py',
   'PYMODULE'),
  ('pygments.lexers.functional',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\functional.py',
   'PYMODULE'),
  ('pygments.lexers.futhark',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\futhark.py',
   'PYMODULE'),
  ('pygments.lexers.gcodelexer',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\gcodelexer.py',
   'PYMODULE'),
  ('pygments.lexers.gdscript',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\gdscript.py',
   'PYMODULE'),
  ('pygments.lexers.gleam',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\gleam.py',
   'PYMODULE'),
  ('pygments.lexers.go',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\go.py',
   'PYMODULE'),
  ('pygments.lexers.grammar_notation',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\grammar_notation.py',
   'PYMODULE'),
  ('pygments.lexers.graph',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\graph.py',
   'PYMODULE'),
  ('pygments.lexers.graphics',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\graphics.py',
   'PYMODULE'),
  ('pygments.lexers.graphql',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\graphql.py',
   'PYMODULE'),
  ('pygments.lexers.graphviz',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\graphviz.py',
   'PYMODULE'),
  ('pygments.lexers.gsql',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\gsql.py',
   'PYMODULE'),
  ('pygments.lexers.hare',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\hare.py',
   'PYMODULE'),
  ('pygments.lexers.haskell',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\haskell.py',
   'PYMODULE'),
  ('pygments.lexers.haxe',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\haxe.py',
   'PYMODULE'),
  ('pygments.lexers.hdl',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\hdl.py',
   'PYMODULE'),
  ('pygments.lexers.hexdump',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\hexdump.py',
   'PYMODULE'),
  ('pygments.lexers.html',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\html.py',
   'PYMODULE'),
  ('pygments.lexers.idl',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\idl.py',
   'PYMODULE'),
  ('pygments.lexers.igor',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\igor.py',
   'PYMODULE'),
  ('pygments.lexers.inferno',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\inferno.py',
   'PYMODULE'),
  ('pygments.lexers.installers',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\installers.py',
   'PYMODULE'),
  ('pygments.lexers.int_fiction',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\int_fiction.py',
   'PYMODULE'),
  ('pygments.lexers.iolang',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\iolang.py',
   'PYMODULE'),
  ('pygments.lexers.j',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\j.py',
   'PYMODULE'),
  ('pygments.lexers.javascript',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\javascript.py',
   'PYMODULE'),
  ('pygments.lexers.jmespath',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\jmespath.py',
   'PYMODULE'),
  ('pygments.lexers.jslt',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\jslt.py',
   'PYMODULE'),
  ('pygments.lexers.json5',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\json5.py',
   'PYMODULE'),
  ('pygments.lexers.jsonnet',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\jsonnet.py',
   'PYMODULE'),
  ('pygments.lexers.jsx',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\jsx.py',
   'PYMODULE'),
  ('pygments.lexers.julia',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\julia.py',
   'PYMODULE'),
  ('pygments.lexers.jvm',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\jvm.py',
   'PYMODULE'),
  ('pygments.lexers.kuin',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\kuin.py',
   'PYMODULE'),
  ('pygments.lexers.kusto',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\kusto.py',
   'PYMODULE'),
  ('pygments.lexers.ldap',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\ldap.py',
   'PYMODULE'),
  ('pygments.lexers.lean',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\lean.py',
   'PYMODULE'),
  ('pygments.lexers.lilypond',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\lilypond.py',
   'PYMODULE'),
  ('pygments.lexers.lisp',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\lisp.py',
   'PYMODULE'),
  ('pygments.lexers.macaulay2',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\macaulay2.py',
   'PYMODULE'),
  ('pygments.lexers.make',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\make.py',
   'PYMODULE'),
  ('pygments.lexers.maple',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\maple.py',
   'PYMODULE'),
  ('pygments.lexers.markup',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\markup.py',
   'PYMODULE'),
  ('pygments.lexers.math',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\math.py',
   'PYMODULE'),
  ('pygments.lexers.matlab',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\matlab.py',
   'PYMODULE'),
  ('pygments.lexers.maxima',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\maxima.py',
   'PYMODULE'),
  ('pygments.lexers.meson',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\meson.py',
   'PYMODULE'),
  ('pygments.lexers.mime',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\mime.py',
   'PYMODULE'),
  ('pygments.lexers.minecraft',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\minecraft.py',
   'PYMODULE'),
  ('pygments.lexers.mips',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\mips.py',
   'PYMODULE'),
  ('pygments.lexers.ml',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\ml.py',
   'PYMODULE'),
  ('pygments.lexers.modeling',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\modeling.py',
   'PYMODULE'),
  ('pygments.lexers.modula2',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\modula2.py',
   'PYMODULE'),
  ('pygments.lexers.mojo',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\mojo.py',
   'PYMODULE'),
  ('pygments.lexers.monte',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\monte.py',
   'PYMODULE'),
  ('pygments.lexers.mosel',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\mosel.py',
   'PYMODULE'),
  ('pygments.lexers.ncl',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\ncl.py',
   'PYMODULE'),
  ('pygments.lexers.nimrod',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\nimrod.py',
   'PYMODULE'),
  ('pygments.lexers.nit',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\nit.py',
   'PYMODULE'),
  ('pygments.lexers.nix',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\nix.py',
   'PYMODULE'),
  ('pygments.lexers.numbair',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\numbair.py',
   'PYMODULE'),
  ('pygments.lexers.oberon',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\oberon.py',
   'PYMODULE'),
  ('pygments.lexers.objective',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\objective.py',
   'PYMODULE'),
  ('pygments.lexers.ooc',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\ooc.py',
   'PYMODULE'),
  ('pygments.lexers.openscad',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\openscad.py',
   'PYMODULE'),
  ('pygments.lexers.other',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\other.py',
   'PYMODULE'),
  ('pygments.lexers.parasail',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\parasail.py',
   'PYMODULE'),
  ('pygments.lexers.parsers',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\parsers.py',
   'PYMODULE'),
  ('pygments.lexers.pascal',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\pascal.py',
   'PYMODULE'),
  ('pygments.lexers.pawn',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\pawn.py',
   'PYMODULE'),
  ('pygments.lexers.pddl',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\pddl.py',
   'PYMODULE'),
  ('pygments.lexers.perl',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\perl.py',
   'PYMODULE'),
  ('pygments.lexers.phix',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\phix.py',
   'PYMODULE'),
  ('pygments.lexers.php',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\php.py',
   'PYMODULE'),
  ('pygments.lexers.pointless',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\pointless.py',
   'PYMODULE'),
  ('pygments.lexers.pony',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\pony.py',
   'PYMODULE'),
  ('pygments.lexers.praat',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\praat.py',
   'PYMODULE'),
  ('pygments.lexers.procfile',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\procfile.py',
   'PYMODULE'),
  ('pygments.lexers.prolog',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\prolog.py',
   'PYMODULE'),
  ('pygments.lexers.promql',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\promql.py',
   'PYMODULE'),
  ('pygments.lexers.prql',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\prql.py',
   'PYMODULE'),
  ('pygments.lexers.ptx',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\ptx.py',
   'PYMODULE'),
  ('pygments.lexers.python',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\python.py',
   'PYMODULE'),
  ('pygments.lexers.q',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\q.py',
   'PYMODULE'),
  ('pygments.lexers.qlik',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\qlik.py',
   'PYMODULE'),
  ('pygments.lexers.qvt',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\qvt.py',
   'PYMODULE'),
  ('pygments.lexers.r',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\r.py',
   'PYMODULE'),
  ('pygments.lexers.rdf',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\rdf.py',
   'PYMODULE'),
  ('pygments.lexers.rebol',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\rebol.py',
   'PYMODULE'),
  ('pygments.lexers.rego',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\rego.py',
   'PYMODULE'),
  ('pygments.lexers.resource',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\resource.py',
   'PYMODULE'),
  ('pygments.lexers.ride',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\ride.py',
   'PYMODULE'),
  ('pygments.lexers.rita',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\rita.py',
   'PYMODULE'),
  ('pygments.lexers.rnc',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\rnc.py',
   'PYMODULE'),
  ('pygments.lexers.roboconf',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\roboconf.py',
   'PYMODULE'),
  ('pygments.lexers.robotframework',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\robotframework.py',
   'PYMODULE'),
  ('pygments.lexers.ruby',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\ruby.py',
   'PYMODULE'),
  ('pygments.lexers.rust',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\rust.py',
   'PYMODULE'),
  ('pygments.lexers.sas',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\sas.py',
   'PYMODULE'),
  ('pygments.lexers.savi',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\savi.py',
   'PYMODULE'),
  ('pygments.lexers.scdoc',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\scdoc.py',
   'PYMODULE'),
  ('pygments.lexers.scripting',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\scripting.py',
   'PYMODULE'),
  ('pygments.lexers.sgf',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\sgf.py',
   'PYMODULE'),
  ('pygments.lexers.shell',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\shell.py',
   'PYMODULE'),
  ('pygments.lexers.sieve',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\sieve.py',
   'PYMODULE'),
  ('pygments.lexers.slash',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\slash.py',
   'PYMODULE'),
  ('pygments.lexers.smalltalk',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\smalltalk.py',
   'PYMODULE'),
  ('pygments.lexers.smithy',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\smithy.py',
   'PYMODULE'),
  ('pygments.lexers.smv',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\smv.py',
   'PYMODULE'),
  ('pygments.lexers.snobol',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\snobol.py',
   'PYMODULE'),
  ('pygments.lexers.solidity',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\solidity.py',
   'PYMODULE'),
  ('pygments.lexers.soong',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\soong.py',
   'PYMODULE'),
  ('pygments.lexers.sophia',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\sophia.py',
   'PYMODULE'),
  ('pygments.lexers.special',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\special.py',
   'PYMODULE'),
  ('pygments.lexers.spice',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\spice.py',
   'PYMODULE'),
  ('pygments.lexers.sql',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\sql.py',
   'PYMODULE'),
  ('pygments.lexers.srcinfo',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\srcinfo.py',
   'PYMODULE'),
  ('pygments.lexers.stata',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\stata.py',
   'PYMODULE'),
  ('pygments.lexers.supercollider',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\supercollider.py',
   'PYMODULE'),
  ('pygments.lexers.tablegen',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\tablegen.py',
   'PYMODULE'),
  ('pygments.lexers.tact',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\tact.py',
   'PYMODULE'),
  ('pygments.lexers.tal',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\tal.py',
   'PYMODULE'),
  ('pygments.lexers.tcl',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\tcl.py',
   'PYMODULE'),
  ('pygments.lexers.teal',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\teal.py',
   'PYMODULE'),
  ('pygments.lexers.templates',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\templates.py',
   'PYMODULE'),
  ('pygments.lexers.teraterm',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\teraterm.py',
   'PYMODULE'),
  ('pygments.lexers.testing',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\testing.py',
   'PYMODULE'),
  ('pygments.lexers.text',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\text.py',
   'PYMODULE'),
  ('pygments.lexers.textedit',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\textedit.py',
   'PYMODULE'),
  ('pygments.lexers.textfmts',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\textfmts.py',
   'PYMODULE'),
  ('pygments.lexers.theorem',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\theorem.py',
   'PYMODULE'),
  ('pygments.lexers.thingsdb',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\thingsdb.py',
   'PYMODULE'),
  ('pygments.lexers.tlb',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\tlb.py',
   'PYMODULE'),
  ('pygments.lexers.tls',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\tls.py',
   'PYMODULE'),
  ('pygments.lexers.tnt',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\tnt.py',
   'PYMODULE'),
  ('pygments.lexers.trafficscript',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\trafficscript.py',
   'PYMODULE'),
  ('pygments.lexers.typoscript',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\typoscript.py',
   'PYMODULE'),
  ('pygments.lexers.typst',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\typst.py',
   'PYMODULE'),
  ('pygments.lexers.ul4',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\ul4.py',
   'PYMODULE'),
  ('pygments.lexers.unicon',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\unicon.py',
   'PYMODULE'),
  ('pygments.lexers.urbi',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\urbi.py',
   'PYMODULE'),
  ('pygments.lexers.usd',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\usd.py',
   'PYMODULE'),
  ('pygments.lexers.varnish',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\varnish.py',
   'PYMODULE'),
  ('pygments.lexers.verification',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\verification.py',
   'PYMODULE'),
  ('pygments.lexers.verifpal',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\verifpal.py',
   'PYMODULE'),
  ('pygments.lexers.vip',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\vip.py',
   'PYMODULE'),
  ('pygments.lexers.vyper',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\vyper.py',
   'PYMODULE'),
  ('pygments.lexers.web',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\web.py',
   'PYMODULE'),
  ('pygments.lexers.webassembly',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\webassembly.py',
   'PYMODULE'),
  ('pygments.lexers.webidl',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\webidl.py',
   'PYMODULE'),
  ('pygments.lexers.webmisc',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\webmisc.py',
   'PYMODULE'),
  ('pygments.lexers.wgsl',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\wgsl.py',
   'PYMODULE'),
  ('pygments.lexers.whiley',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\whiley.py',
   'PYMODULE'),
  ('pygments.lexers.wowtoc',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\wowtoc.py',
   'PYMODULE'),
  ('pygments.lexers.wren',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\wren.py',
   'PYMODULE'),
  ('pygments.lexers.x10',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\x10.py',
   'PYMODULE'),
  ('pygments.lexers.xorg',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\xorg.py',
   'PYMODULE'),
  ('pygments.lexers.yang',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\yang.py',
   'PYMODULE'),
  ('pygments.lexers.yara',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\yara.py',
   'PYMODULE'),
  ('pygments.lexers.zig',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\lexers\\zig.py',
   'PYMODULE'),
  ('pygments.modeline',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\modeline.py',
   'PYMODULE'),
  ('pygments.plugin',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\plugin.py',
   'PYMODULE'),
  ('pygments.regexopt',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\regexopt.py',
   'PYMODULE'),
  ('pygments.scanner',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\scanner.py',
   'PYMODULE'),
  ('pygments.style',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\style.py',
   'PYMODULE'),
  ('pygments.styles',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\__init__.py',
   'PYMODULE'),
  ('pygments.styles._mapping',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\_mapping.py',
   'PYMODULE'),
  ('pygments.styles.abap',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\abap.py',
   'PYMODULE'),
  ('pygments.styles.algol',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\algol.py',
   'PYMODULE'),
  ('pygments.styles.algol_nu',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\algol_nu.py',
   'PYMODULE'),
  ('pygments.styles.arduino',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\arduino.py',
   'PYMODULE'),
  ('pygments.styles.autumn',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\autumn.py',
   'PYMODULE'),
  ('pygments.styles.borland',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\borland.py',
   'PYMODULE'),
  ('pygments.styles.bw',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\bw.py',
   'PYMODULE'),
  ('pygments.styles.coffee',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\coffee.py',
   'PYMODULE'),
  ('pygments.styles.colorful',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\colorful.py',
   'PYMODULE'),
  ('pygments.styles.default',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\default.py',
   'PYMODULE'),
  ('pygments.styles.dracula',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\dracula.py',
   'PYMODULE'),
  ('pygments.styles.emacs',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\emacs.py',
   'PYMODULE'),
  ('pygments.styles.friendly',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\friendly.py',
   'PYMODULE'),
  ('pygments.styles.friendly_grayscale',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\friendly_grayscale.py',
   'PYMODULE'),
  ('pygments.styles.fruity',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\fruity.py',
   'PYMODULE'),
  ('pygments.styles.gh_dark',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\gh_dark.py',
   'PYMODULE'),
  ('pygments.styles.gruvbox',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\gruvbox.py',
   'PYMODULE'),
  ('pygments.styles.igor',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\igor.py',
   'PYMODULE'),
  ('pygments.styles.inkpot',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\inkpot.py',
   'PYMODULE'),
  ('pygments.styles.lightbulb',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\lightbulb.py',
   'PYMODULE'),
  ('pygments.styles.lilypond',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\lilypond.py',
   'PYMODULE'),
  ('pygments.styles.lovelace',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\lovelace.py',
   'PYMODULE'),
  ('pygments.styles.manni',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\manni.py',
   'PYMODULE'),
  ('pygments.styles.material',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\material.py',
   'PYMODULE'),
  ('pygments.styles.monokai',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\monokai.py',
   'PYMODULE'),
  ('pygments.styles.murphy',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\murphy.py',
   'PYMODULE'),
  ('pygments.styles.native',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\native.py',
   'PYMODULE'),
  ('pygments.styles.nord',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\nord.py',
   'PYMODULE'),
  ('pygments.styles.onedark',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\onedark.py',
   'PYMODULE'),
  ('pygments.styles.paraiso_dark',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\paraiso_dark.py',
   'PYMODULE'),
  ('pygments.styles.paraiso_light',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\paraiso_light.py',
   'PYMODULE'),
  ('pygments.styles.pastie',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\pastie.py',
   'PYMODULE'),
  ('pygments.styles.perldoc',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\perldoc.py',
   'PYMODULE'),
  ('pygments.styles.rainbow_dash',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\rainbow_dash.py',
   'PYMODULE'),
  ('pygments.styles.rrt',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\rrt.py',
   'PYMODULE'),
  ('pygments.styles.sas',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\sas.py',
   'PYMODULE'),
  ('pygments.styles.solarized',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\solarized.py',
   'PYMODULE'),
  ('pygments.styles.staroffice',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\staroffice.py',
   'PYMODULE'),
  ('pygments.styles.stata_dark',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\stata_dark.py',
   'PYMODULE'),
  ('pygments.styles.stata_light',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\stata_light.py',
   'PYMODULE'),
  ('pygments.styles.tango',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\tango.py',
   'PYMODULE'),
  ('pygments.styles.trac',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\trac.py',
   'PYMODULE'),
  ('pygments.styles.vim',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\vim.py',
   'PYMODULE'),
  ('pygments.styles.vs',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\vs.py',
   'PYMODULE'),
  ('pygments.styles.xcode',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\xcode.py',
   'PYMODULE'),
  ('pygments.styles.zenburn',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\styles\\zenburn.py',
   'PYMODULE'),
  ('pygments.token',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\token.py',
   'PYMODULE'),
  ('pygments.unistring',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\unistring.py',
   'PYMODULE'),
  ('pygments.util',
   'D:\\pyfile\\py\\Lib\\site-packages\\pygments\\util.py',
   'PYMODULE'),
  ('pyparsing',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pyparsing.actions',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pyparsing.common',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyparsing\\common.py',
   'PYMODULE'),
  ('pyparsing.core',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyparsing\\core.py',
   'PYMODULE'),
  ('pyparsing.diagram',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pyparsing.exceptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pyparsing.helpers',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pyparsing.results',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyparsing\\results.py',
   'PYMODULE'),
  ('pyparsing.testing',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pyparsing.unicode',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pyparsing.util',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyparsing\\util.py',
   'PYMODULE'),
  ('pyreadline3',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.ironpython_clipboard',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\clipboard\\ironpython_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.no_clipboard',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\clipboard\\no_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.win32_clipboard',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\clipboard\\win32_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.console',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\console\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.console.ansi',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\console\\ansi.py',
   'PYMODULE'),
  ('pyreadline3.console.console',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\console\\console.py',
   'PYMODULE'),
  ('pyreadline3.console.event',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\console\\event.py',
   'PYMODULE'),
  ('pyreadline3.console.ironpython_console',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\console\\ironpython_console.py',
   'PYMODULE'),
  ('pyreadline3.error',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\error.py',
   'PYMODULE'),
  ('pyreadline3.keysyms',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\keysyms\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.common',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\keysyms\\common.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.ironpython_keysyms',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\keysyms\\ironpython_keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.keysyms',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\keysyms\\keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.winconstants',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\keysyms\\winconstants.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\lineeditor\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.history',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\lineeditor\\history.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.lineobj',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\lineeditor\\lineobj.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.wordmatcher',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\lineeditor\\wordmatcher.py',
   'PYMODULE'),
  ('pyreadline3.logger',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\logger.py',
   'PYMODULE'),
  ('pyreadline3.modes',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\modes\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.modes.basemode',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\modes\\basemode.py',
   'PYMODULE'),
  ('pyreadline3.modes.emacs',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\modes\\emacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.notemacs',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\modes\\notemacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.vi',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\modes\\vi.py',
   'PYMODULE'),
  ('pyreadline3.py3k_compat',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\py3k_compat.py',
   'PYMODULE'),
  ('pyreadline3.release',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\release.py',
   'PYMODULE'),
  ('pyreadline3.rlmain',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\rlmain.py',
   'PYMODULE'),
  ('pyreadline3.unicode_helper',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\unicode_helper.py',
   'PYMODULE'),
  ('pythoncom', 'D:\\pyfile\\py\\Lib\\site-packages\\pythoncom.py', 'PYMODULE'),
  ('pythonnet',
   'D:\\pyfile\\py\\Lib\\site-packages\\pythonnet\\__init__.py',
   'PYMODULE'),
  ('pywin',
   'D:\\pyfile\\py\\Lib\\site-packages\\Pythonwin\\pywin\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs',
   'D:\\pyfile\\py\\Lib\\site-packages\\Pythonwin\\pywin\\dialogs\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs.list',
   'D:\\pyfile\\py\\Lib\\site-packages\\Pythonwin\\pywin\\dialogs\\list.py',
   'PYMODULE'),
  ('pywin.dialogs.status',
   'D:\\pyfile\\py\\Lib\\site-packages\\Pythonwin\\pywin\\dialogs\\status.py',
   'PYMODULE'),
  ('pywin.mfc',
   'D:\\pyfile\\py\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\__init__.py',
   'PYMODULE'),
  ('pywin.mfc.dialog',
   'D:\\pyfile\\py\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\dialog.py',
   'PYMODULE'),
  ('pywin.mfc.object',
   'D:\\pyfile\\py\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\object.py',
   'PYMODULE'),
  ('pywin.mfc.thread',
   'D:\\pyfile\\py\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\thread.py',
   'PYMODULE'),
  ('pywin.mfc.window',
   'D:\\pyfile\\py\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\window.py',
   'PYMODULE'),
  ('pywin32_system32', '-', 'PYMODULE'),
  ('pywintypes',
   'D:\\pyfile\\py\\Lib\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE'),
  ('queue', 'D:\\pyfile\\py\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\pyfile\\py\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\pyfile\\py\\Lib\\random.py', 'PYMODULE'),
  ('readline', 'D:\\pyfile\\py\\Lib\\site-packages\\readline.py', 'PYMODULE'),
  ('referencing',
   'D:\\pyfile\\py\\Lib\\site-packages\\referencing\\__init__.py',
   'PYMODULE'),
  ('referencing._attrs',
   'D:\\pyfile\\py\\Lib\\site-packages\\referencing\\_attrs.py',
   'PYMODULE'),
  ('referencing._core',
   'D:\\pyfile\\py\\Lib\\site-packages\\referencing\\_core.py',
   'PYMODULE'),
  ('referencing.exceptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\referencing\\exceptions.py',
   'PYMODULE'),
  ('referencing.jsonschema',
   'D:\\pyfile\\py\\Lib\\site-packages\\referencing\\jsonschema.py',
   'PYMODULE'),
  ('referencing.typing',
   'D:\\pyfile\\py\\Lib\\site-packages\\referencing\\typing.py',
   'PYMODULE'),
  ('requests',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('rfc3339_validator',
   'D:\\pyfile\\py\\Lib\\site-packages\\rfc3339_validator.py',
   'PYMODULE'),
  ('rfc3986_validator',
   'D:\\pyfile\\py\\Lib\\site-packages\\rfc3986_validator.py',
   'PYMODULE'),
  ('rlcompleter', 'D:\\pyfile\\py\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('rpds', 'D:\\pyfile\\py\\Lib\\site-packages\\rpds\\__init__.py', 'PYMODULE'),
  ('runpy', 'D:\\pyfile\\py\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'D:\\pyfile\\py\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'D:\\pyfile\\py\\Lib\\selectors.py', 'PYMODULE'),
  ('selenium',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\__init__.py',
   'PYMODULE'),
  ('selenium.common',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\common\\__init__.py',
   'PYMODULE'),
  ('selenium.common.exceptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\common\\exceptions.py',
   'PYMODULE'),
  ('selenium.types',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\types.py',
   'PYMODULE'),
  ('selenium.webdriver',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\chrome\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.options',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\chrome\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.remote_connection',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\chrome\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.service',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\chrome\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.webdriver',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\chrome\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\chromium\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.options',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\chromium\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.remote_connection',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\chromium\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.service',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\chromium\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.webdriver',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\chromium\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.common',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\common\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.action_chains',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\common\\action_chains.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.action_builder',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\action_builder.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.input_device',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\input_device.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.interaction',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\interaction.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.key_actions',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\key_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.key_input',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\key_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.mouse_button',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\mouse_button.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.pointer_actions',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\pointer_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.pointer_input',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\pointer_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.wheel_actions',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\wheel_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.wheel_input',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\wheel_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.alert',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\common\\alert.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\common\\bidi\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.cdp',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\common\\bidi\\cdp.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.console',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\common\\bidi\\console.py',
   'PYMODULE'),
  ('selenium.webdriver.common.by',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\common\\by.py',
   'PYMODULE'),
  ('selenium.webdriver.common.desired_capabilities',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\common\\desired_capabilities.py',
   'PYMODULE'),
  ('selenium.webdriver.common.driver_finder',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\common\\driver_finder.py',
   'PYMODULE'),
  ('selenium.webdriver.common.keys',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\common\\keys.py',
   'PYMODULE'),
  ('selenium.webdriver.common.log',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\common\\log.py',
   'PYMODULE'),
  ('selenium.webdriver.common.options',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\common\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.common.print_page_options',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\common\\print_page_options.py',
   'PYMODULE'),
  ('selenium.webdriver.common.proxy',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\common\\proxy.py',
   'PYMODULE'),
  ('selenium.webdriver.common.selenium_manager',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\common\\selenium_manager.py',
   'PYMODULE'),
  ('selenium.webdriver.common.service',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\common\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.common.timeouts',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\common\\timeouts.py',
   'PYMODULE'),
  ('selenium.webdriver.common.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\common\\utils.py',
   'PYMODULE'),
  ('selenium.webdriver.common.virtual_authenticator',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\common\\virtual_authenticator.py',
   'PYMODULE'),
  ('selenium.webdriver.common.window',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\common\\window.py',
   'PYMODULE'),
  ('selenium.webdriver.edge',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\edge\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.options',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\edge\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.remote_connection',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\edge\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.service',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\edge\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.webdriver',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\edge\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\firefox\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.firefox_binary',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\firefox\\firefox_binary.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.firefox_profile',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\firefox\\firefox_profile.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.options',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\firefox\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.remote_connection',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\firefox\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.service',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\firefox\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.webdriver',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\firefox\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.ie',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\ie\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.options',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\ie\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.service',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\ie\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.webdriver',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\ie\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.remote',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\remote\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.bidi_connection',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\remote\\bidi_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.command',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\remote\\command.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.errorhandler',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\remote\\errorhandler.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.file_detector',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\remote\\file_detector.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.mobile',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\remote\\mobile.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.remote_connection',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\remote\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.script_key',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\remote\\script_key.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.shadowroot',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\remote\\shadowroot.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.switch_to',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\remote\\switch_to.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\remote\\utils.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.webdriver',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\remote\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.webelement',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\remote\\webelement.py',
   'PYMODULE'),
  ('selenium.webdriver.safari',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\safari\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.options',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\safari\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.permissions',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\safari\\permissions.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.remote_connection',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\safari\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.service',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\safari\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.webdriver',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\safari\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.support',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\support\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.support.abstract_event_listener',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\support\\abstract_event_listener.py',
   'PYMODULE'),
  ('selenium.webdriver.support.color',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\support\\color.py',
   'PYMODULE'),
  ('selenium.webdriver.support.event_firing_webdriver',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\support\\event_firing_webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.support.events',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\support\\events.py',
   'PYMODULE'),
  ('selenium.webdriver.support.expected_conditions',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\support\\expected_conditions.py',
   'PYMODULE'),
  ('selenium.webdriver.support.relative_locator',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\support\\relative_locator.py',
   'PYMODULE'),
  ('selenium.webdriver.support.select',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\support\\select.py',
   'PYMODULE'),
  ('selenium.webdriver.support.ui',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\support\\ui.py',
   'PYMODULE'),
  ('selenium.webdriver.support.wait',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\support\\wait.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\webkitgtk\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.options',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\webkitgtk\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.service',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\webkitgtk\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.webdriver',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\webkitgtk\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\wpewebkit\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.options',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\wpewebkit\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.service',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\wpewebkit\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.webdriver',
   'D:\\pyfile\\py\\Lib\\site-packages\\selenium\\webdriver\\wpewebkit\\webdriver.py',
   'PYMODULE'),
  ('setuptools',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._deprecation_warning',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_deprecation_warning.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._collections',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\_collections.py',
   'PYMODULE'),
  ('setuptools._distutils._functools',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\_functools.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.bcppcompiler',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\bcppcompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.config',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.py37compat',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\command\\py37compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.register',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\command\\register.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.upload',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\command\\upload.py',
   'PYMODULE'),
  ('setuptools._distutils.config',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.msvc9compiler',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('setuptools._distutils.msvccompiler',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.py38compat',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\py38compat.py',
   'PYMODULE'),
  ('setuptools._distutils.py39compat',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\py39compat.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._path',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._vendor',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._compat',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._legacy',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.simple',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.__about__',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.actions',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\actions.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.common',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\common.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.core',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\core.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.diagram',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.exceptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.helpers',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.results',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\results.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.testing',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\testing.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.unicode',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.util',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\_vendor\\zipp.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.py36compat',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\command\\py36compat.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.config',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.extern',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\extern\\__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.py34compat',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\py34compat.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'D:\\pyfile\\py\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'D:\\pyfile\\py\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\pyfile\\py\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\pyfile\\py\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'D:\\pyfile\\py\\Lib\\site.py', 'PYMODULE'),
  ('six', 'D:\\pyfile\\py\\Lib\\site-packages\\six.py', 'PYMODULE'),
  ('smtplib', 'D:\\pyfile\\py\\Lib\\smtplib.py', 'PYMODULE'),
  ('sniffio',
   'D:\\pyfile\\py\\Lib\\site-packages\\sniffio\\__init__.py',
   'PYMODULE'),
  ('sniffio._impl',
   'D:\\pyfile\\py\\Lib\\site-packages\\sniffio\\_impl.py',
   'PYMODULE'),
  ('sniffio._version',
   'D:\\pyfile\\py\\Lib\\site-packages\\sniffio\\_version.py',
   'PYMODULE'),
  ('socket', 'D:\\pyfile\\py\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'D:\\pyfile\\py\\Lib\\socketserver.py', 'PYMODULE'),
  ('socks', 'D:\\pyfile\\py\\Lib\\site-packages\\socks.py', 'PYMODULE'),
  ('sortedcontainers',
   'D:\\pyfile\\py\\Lib\\site-packages\\sortedcontainers\\__init__.py',
   'PYMODULE'),
  ('sortedcontainers.sorteddict',
   'D:\\pyfile\\py\\Lib\\site-packages\\sortedcontainers\\sorteddict.py',
   'PYMODULE'),
  ('sortedcontainers.sortedlist',
   'D:\\pyfile\\py\\Lib\\site-packages\\sortedcontainers\\sortedlist.py',
   'PYMODULE'),
  ('sortedcontainers.sortedset',
   'D:\\pyfile\\py\\Lib\\site-packages\\sortedcontainers\\sortedset.py',
   'PYMODULE'),
  ('sqlite3', 'D:\\pyfile\\py\\Lib\\sqlite3\\__init__.py', 'PYMODULE'),
  ('sqlite3.dbapi2', 'D:\\pyfile\\py\\Lib\\sqlite3\\dbapi2.py', 'PYMODULE'),
  ('sqlite3.dump', 'D:\\pyfile\\py\\Lib\\sqlite3\\dump.py', 'PYMODULE'),
  ('ssl', 'D:\\pyfile\\py\\Lib\\ssl.py', 'PYMODULE'),
  ('sspi',
   'D:\\pyfile\\py\\Lib\\site-packages\\win32\\lib\\sspi.py',
   'PYMODULE'),
  ('sspicon',
   'D:\\pyfile\\py\\Lib\\site-packages\\win32\\lib\\sspicon.py',
   'PYMODULE'),
  ('stack_data',
   'D:\\pyfile\\py\\Lib\\site-packages\\stack_data\\__init__.py',
   'PYMODULE'),
  ('stack_data.core',
   'D:\\pyfile\\py\\Lib\\site-packages\\stack_data\\core.py',
   'PYMODULE'),
  ('stack_data.formatting',
   'D:\\pyfile\\py\\Lib\\site-packages\\stack_data\\formatting.py',
   'PYMODULE'),
  ('stack_data.serializing',
   'D:\\pyfile\\py\\Lib\\site-packages\\stack_data\\serializing.py',
   'PYMODULE'),
  ('stack_data.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\stack_data\\utils.py',
   'PYMODULE'),
  ('stack_data.version',
   'D:\\pyfile\\py\\Lib\\site-packages\\stack_data\\version.py',
   'PYMODULE'),
  ('start', 'D:\\编程\\插件开发\\plugin\\register\\start.py', 'PYMODULE'),
  ('statistics', 'D:\\pyfile\\py\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\pyfile\\py\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\pyfile\\py\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\pyfile\\py\\Lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'D:\\pyfile\\py\\Lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'D:\\pyfile\\py\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\pyfile\\py\\Lib\\tempfile.py', 'PYMODULE'),
  ('test', 'D:\\pyfile\\py\\Lib\\test\\__init__.py', 'PYMODULE'),
  ('test.libregrtest',
   'D:\\pyfile\\py\\Lib\\test\\libregrtest\\__init__.py',
   'PYMODULE'),
  ('test.libregrtest.cmdline',
   'D:\\pyfile\\py\\Lib\\test\\libregrtest\\cmdline.py',
   'PYMODULE'),
  ('test.libregrtest.main',
   'D:\\pyfile\\py\\Lib\\test\\libregrtest\\main.py',
   'PYMODULE'),
  ('test.libregrtest.pgo',
   'D:\\pyfile\\py\\Lib\\test\\libregrtest\\pgo.py',
   'PYMODULE'),
  ('test.libregrtest.refleak',
   'D:\\pyfile\\py\\Lib\\test\\libregrtest\\refleak.py',
   'PYMODULE'),
  ('test.libregrtest.runtest',
   'D:\\pyfile\\py\\Lib\\test\\libregrtest\\runtest.py',
   'PYMODULE'),
  ('test.libregrtest.runtest_mp',
   'D:\\pyfile\\py\\Lib\\test\\libregrtest\\runtest_mp.py',
   'PYMODULE'),
  ('test.libregrtest.save_env',
   'D:\\pyfile\\py\\Lib\\test\\libregrtest\\save_env.py',
   'PYMODULE'),
  ('test.libregrtest.setup',
   'D:\\pyfile\\py\\Lib\\test\\libregrtest\\setup.py',
   'PYMODULE'),
  ('test.libregrtest.utils',
   'D:\\pyfile\\py\\Lib\\test\\libregrtest\\utils.py',
   'PYMODULE'),
  ('test.libregrtest.win_utils',
   'D:\\pyfile\\py\\Lib\\test\\libregrtest\\win_utils.py',
   'PYMODULE'),
  ('test.lock_tests', 'D:\\pyfile\\py\\Lib\\test\\lock_tests.py', 'PYMODULE'),
  ('test.support',
   'D:\\pyfile\\py\\Lib\\test\\support\\__init__.py',
   'PYMODULE'),
  ('test.support.import_helper',
   'D:\\pyfile\\py\\Lib\\test\\support\\import_helper.py',
   'PYMODULE'),
  ('test.support.os_helper',
   'D:\\pyfile\\py\\Lib\\test\\support\\os_helper.py',
   'PYMODULE'),
  ('test.support.testresult',
   'D:\\pyfile\\py\\Lib\\test\\support\\testresult.py',
   'PYMODULE'),
  ('test.support.threading_helper',
   'D:\\pyfile\\py\\Lib\\test\\support\\threading_helper.py',
   'PYMODULE'),
  ('textwrap', 'D:\\pyfile\\py\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\pyfile\\py\\Lib\\threading.py', 'PYMODULE'),
  ('threadpoolctl',
   'D:\\pyfile\\py\\Lib\\site-packages\\threadpoolctl.py',
   'PYMODULE'),
  ('timeit', 'D:\\pyfile\\py\\Lib\\timeit.py', 'PYMODULE'),
  ('token', 'D:\\pyfile\\py\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\pyfile\\py\\Lib\\tokenize.py', 'PYMODULE'),
  ('tornado',
   'D:\\pyfile\\py\\Lib\\site-packages\\tornado\\__init__.py',
   'PYMODULE'),
  ('tornado._locale_data',
   'D:\\pyfile\\py\\Lib\\site-packages\\tornado\\_locale_data.py',
   'PYMODULE'),
  ('tornado.autoreload',
   'D:\\pyfile\\py\\Lib\\site-packages\\tornado\\autoreload.py',
   'PYMODULE'),
  ('tornado.concurrent',
   'D:\\pyfile\\py\\Lib\\site-packages\\tornado\\concurrent.py',
   'PYMODULE'),
  ('tornado.escape',
   'D:\\pyfile\\py\\Lib\\site-packages\\tornado\\escape.py',
   'PYMODULE'),
  ('tornado.gen',
   'D:\\pyfile\\py\\Lib\\site-packages\\tornado\\gen.py',
   'PYMODULE'),
  ('tornado.http1connection',
   'D:\\pyfile\\py\\Lib\\site-packages\\tornado\\http1connection.py',
   'PYMODULE'),
  ('tornado.httpclient',
   'D:\\pyfile\\py\\Lib\\site-packages\\tornado\\httpclient.py',
   'PYMODULE'),
  ('tornado.httpserver',
   'D:\\pyfile\\py\\Lib\\site-packages\\tornado\\httpserver.py',
   'PYMODULE'),
  ('tornado.httputil',
   'D:\\pyfile\\py\\Lib\\site-packages\\tornado\\httputil.py',
   'PYMODULE'),
  ('tornado.ioloop',
   'D:\\pyfile\\py\\Lib\\site-packages\\tornado\\ioloop.py',
   'PYMODULE'),
  ('tornado.iostream',
   'D:\\pyfile\\py\\Lib\\site-packages\\tornado\\iostream.py',
   'PYMODULE'),
  ('tornado.locale',
   'D:\\pyfile\\py\\Lib\\site-packages\\tornado\\locale.py',
   'PYMODULE'),
  ('tornado.locks',
   'D:\\pyfile\\py\\Lib\\site-packages\\tornado\\locks.py',
   'PYMODULE'),
  ('tornado.log',
   'D:\\pyfile\\py\\Lib\\site-packages\\tornado\\log.py',
   'PYMODULE'),
  ('tornado.netutil',
   'D:\\pyfile\\py\\Lib\\site-packages\\tornado\\netutil.py',
   'PYMODULE'),
  ('tornado.options',
   'D:\\pyfile\\py\\Lib\\site-packages\\tornado\\options.py',
   'PYMODULE'),
  ('tornado.platform',
   'D:\\pyfile\\py\\Lib\\site-packages\\tornado\\platform\\__init__.py',
   'PYMODULE'),
  ('tornado.platform.asyncio',
   'D:\\pyfile\\py\\Lib\\site-packages\\tornado\\platform\\asyncio.py',
   'PYMODULE'),
  ('tornado.process',
   'D:\\pyfile\\py\\Lib\\site-packages\\tornado\\process.py',
   'PYMODULE'),
  ('tornado.queues',
   'D:\\pyfile\\py\\Lib\\site-packages\\tornado\\queues.py',
   'PYMODULE'),
  ('tornado.routing',
   'D:\\pyfile\\py\\Lib\\site-packages\\tornado\\routing.py',
   'PYMODULE'),
  ('tornado.simple_httpclient',
   'D:\\pyfile\\py\\Lib\\site-packages\\tornado\\simple_httpclient.py',
   'PYMODULE'),
  ('tornado.tcpclient',
   'D:\\pyfile\\py\\Lib\\site-packages\\tornado\\tcpclient.py',
   'PYMODULE'),
  ('tornado.tcpserver',
   'D:\\pyfile\\py\\Lib\\site-packages\\tornado\\tcpserver.py',
   'PYMODULE'),
  ('tornado.template',
   'D:\\pyfile\\py\\Lib\\site-packages\\tornado\\template.py',
   'PYMODULE'),
  ('tornado.util',
   'D:\\pyfile\\py\\Lib\\site-packages\\tornado\\util.py',
   'PYMODULE'),
  ('tornado.web',
   'D:\\pyfile\\py\\Lib\\site-packages\\tornado\\web.py',
   'PYMODULE'),
  ('tornado.websocket',
   'D:\\pyfile\\py\\Lib\\site-packages\\tornado\\websocket.py',
   'PYMODULE'),
  ('trace', 'D:\\pyfile\\py\\Lib\\trace.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\pyfile\\py\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('traitlets',
   'D:\\pyfile\\py\\Lib\\site-packages\\traitlets\\__init__.py',
   'PYMODULE'),
  ('traitlets._version',
   'D:\\pyfile\\py\\Lib\\site-packages\\traitlets\\_version.py',
   'PYMODULE'),
  ('traitlets.config',
   'D:\\pyfile\\py\\Lib\\site-packages\\traitlets\\config\\__init__.py',
   'PYMODULE'),
  ('traitlets.config.application',
   'D:\\pyfile\\py\\Lib\\site-packages\\traitlets\\config\\application.py',
   'PYMODULE'),
  ('traitlets.config.argcomplete_config',
   'D:\\pyfile\\py\\Lib\\site-packages\\traitlets\\config\\argcomplete_config.py',
   'PYMODULE'),
  ('traitlets.config.configurable',
   'D:\\pyfile\\py\\Lib\\site-packages\\traitlets\\config\\configurable.py',
   'PYMODULE'),
  ('traitlets.config.loader',
   'D:\\pyfile\\py\\Lib\\site-packages\\traitlets\\config\\loader.py',
   'PYMODULE'),
  ('traitlets.log',
   'D:\\pyfile\\py\\Lib\\site-packages\\traitlets\\log.py',
   'PYMODULE'),
  ('traitlets.traitlets',
   'D:\\pyfile\\py\\Lib\\site-packages\\traitlets\\traitlets.py',
   'PYMODULE'),
  ('traitlets.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\traitlets\\utils\\__init__.py',
   'PYMODULE'),
  ('traitlets.utils.bunch',
   'D:\\pyfile\\py\\Lib\\site-packages\\traitlets\\utils\\bunch.py',
   'PYMODULE'),
  ('traitlets.utils.decorators',
   'D:\\pyfile\\py\\Lib\\site-packages\\traitlets\\utils\\decorators.py',
   'PYMODULE'),
  ('traitlets.utils.descriptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\traitlets\\utils\\descriptions.py',
   'PYMODULE'),
  ('traitlets.utils.getargspec',
   'D:\\pyfile\\py\\Lib\\site-packages\\traitlets\\utils\\getargspec.py',
   'PYMODULE'),
  ('traitlets.utils.importstring',
   'D:\\pyfile\\py\\Lib\\site-packages\\traitlets\\utils\\importstring.py',
   'PYMODULE'),
  ('traitlets.utils.nested_update',
   'D:\\pyfile\\py\\Lib\\site-packages\\traitlets\\utils\\nested_update.py',
   'PYMODULE'),
  ('traitlets.utils.sentinel',
   'D:\\pyfile\\py\\Lib\\site-packages\\traitlets\\utils\\sentinel.py',
   'PYMODULE'),
  ('traitlets.utils.text',
   'D:\\pyfile\\py\\Lib\\site-packages\\traitlets\\utils\\text.py',
   'PYMODULE'),
  ('traitlets.utils.warnings',
   'D:\\pyfile\\py\\Lib\\site-packages\\traitlets\\utils\\warnings.py',
   'PYMODULE'),
  ('trio', 'D:\\pyfile\\py\\Lib\\site-packages\\trio\\__init__.py', 'PYMODULE'),
  ('trio._abc',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_abc.py',
   'PYMODULE'),
  ('trio._channel',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_channel.py',
   'PYMODULE'),
  ('trio._core',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_core\\__init__.py',
   'PYMODULE'),
  ('trio._core._asyncgens',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_core\\_asyncgens.py',
   'PYMODULE'),
  ('trio._core._entry_queue',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_core\\_entry_queue.py',
   'PYMODULE'),
  ('trio._core._exceptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_core\\_exceptions.py',
   'PYMODULE'),
  ('trio._core._generated_instrumentation',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_core\\_generated_instrumentation.py',
   'PYMODULE'),
  ('trio._core._generated_io_epoll',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_core\\_generated_io_epoll.py',
   'PYMODULE'),
  ('trio._core._generated_io_kqueue',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_core\\_generated_io_kqueue.py',
   'PYMODULE'),
  ('trio._core._generated_io_windows',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_core\\_generated_io_windows.py',
   'PYMODULE'),
  ('trio._core._generated_run',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_core\\_generated_run.py',
   'PYMODULE'),
  ('trio._core._instrumentation',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_core\\_instrumentation.py',
   'PYMODULE'),
  ('trio._core._io_common',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_core\\_io_common.py',
   'PYMODULE'),
  ('trio._core._io_epoll',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_core\\_io_epoll.py',
   'PYMODULE'),
  ('trio._core._io_kqueue',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_core\\_io_kqueue.py',
   'PYMODULE'),
  ('trio._core._io_windows',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_core\\_io_windows.py',
   'PYMODULE'),
  ('trio._core._ki',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_core\\_ki.py',
   'PYMODULE'),
  ('trio._core._local',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_core\\_local.py',
   'PYMODULE'),
  ('trio._core._mock_clock',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_core\\_mock_clock.py',
   'PYMODULE'),
  ('trio._core._multierror',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_core\\_multierror.py',
   'PYMODULE'),
  ('trio._core._parking_lot',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_core\\_parking_lot.py',
   'PYMODULE'),
  ('trio._core._run',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_core\\_run.py',
   'PYMODULE'),
  ('trio._core._thread_cache',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_core\\_thread_cache.py',
   'PYMODULE'),
  ('trio._core._traps',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_core\\_traps.py',
   'PYMODULE'),
  ('trio._core._unbounded_queue',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_core\\_unbounded_queue.py',
   'PYMODULE'),
  ('trio._core._wakeup_socketpair',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_core\\_wakeup_socketpair.py',
   'PYMODULE'),
  ('trio._core._windows_cffi',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_core\\_windows_cffi.py',
   'PYMODULE'),
  ('trio._deprecate',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_deprecate.py',
   'PYMODULE'),
  ('trio._dtls',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_dtls.py',
   'PYMODULE'),
  ('trio._file_io',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_file_io.py',
   'PYMODULE'),
  ('trio._highlevel_generic',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_highlevel_generic.py',
   'PYMODULE'),
  ('trio._highlevel_open_tcp_listeners',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_highlevel_open_tcp_listeners.py',
   'PYMODULE'),
  ('trio._highlevel_open_tcp_stream',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_highlevel_open_tcp_stream.py',
   'PYMODULE'),
  ('trio._highlevel_open_unix_stream',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_highlevel_open_unix_stream.py',
   'PYMODULE'),
  ('trio._highlevel_serve_listeners',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_highlevel_serve_listeners.py',
   'PYMODULE'),
  ('trio._highlevel_socket',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_highlevel_socket.py',
   'PYMODULE'),
  ('trio._highlevel_ssl_helpers',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_highlevel_ssl_helpers.py',
   'PYMODULE'),
  ('trio._path',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_path.py',
   'PYMODULE'),
  ('trio._signals',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_signals.py',
   'PYMODULE'),
  ('trio._socket',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_socket.py',
   'PYMODULE'),
  ('trio._ssl',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_ssl.py',
   'PYMODULE'),
  ('trio._subprocess',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_subprocess.py',
   'PYMODULE'),
  ('trio._subprocess_platform',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_subprocess_platform\\__init__.py',
   'PYMODULE'),
  ('trio._subprocess_platform.kqueue',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_subprocess_platform\\kqueue.py',
   'PYMODULE'),
  ('trio._subprocess_platform.waitid',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_subprocess_platform\\waitid.py',
   'PYMODULE'),
  ('trio._subprocess_platform.windows',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_subprocess_platform\\windows.py',
   'PYMODULE'),
  ('trio._sync',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_sync.py',
   'PYMODULE'),
  ('trio._threads',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_threads.py',
   'PYMODULE'),
  ('trio._timeouts',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_timeouts.py',
   'PYMODULE'),
  ('trio._unix_pipes',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_unix_pipes.py',
   'PYMODULE'),
  ('trio._util',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_util.py',
   'PYMODULE'),
  ('trio._version',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_version.py',
   'PYMODULE'),
  ('trio._wait_for_object',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_wait_for_object.py',
   'PYMODULE'),
  ('trio._windows_pipes',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\_windows_pipes.py',
   'PYMODULE'),
  ('trio.abc', 'D:\\pyfile\\py\\Lib\\site-packages\\trio\\abc.py', 'PYMODULE'),
  ('trio.from_thread',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\from_thread.py',
   'PYMODULE'),
  ('trio.lowlevel',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\lowlevel.py',
   'PYMODULE'),
  ('trio.socket',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\socket.py',
   'PYMODULE'),
  ('trio.testing',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\testing\\__init__.py',
   'PYMODULE'),
  ('trio.testing._check_streams',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\testing\\_check_streams.py',
   'PYMODULE'),
  ('trio.testing._checkpoints',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\testing\\_checkpoints.py',
   'PYMODULE'),
  ('trio.testing._memory_streams',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\testing\\_memory_streams.py',
   'PYMODULE'),
  ('trio.testing._network',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\testing\\_network.py',
   'PYMODULE'),
  ('trio.testing._sequencer',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\testing\\_sequencer.py',
   'PYMODULE'),
  ('trio.testing._trio_test',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\testing\\_trio_test.py',
   'PYMODULE'),
  ('trio.to_thread',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio\\to_thread.py',
   'PYMODULE'),
  ('trio_websocket',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio_websocket\\__init__.py',
   'PYMODULE'),
  ('trio_websocket._impl',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio_websocket\\_impl.py',
   'PYMODULE'),
  ('trio_websocket._version',
   'D:\\pyfile\\py\\Lib\\site-packages\\trio_websocket\\_version.py',
   'PYMODULE'),
  ('tty', 'D:\\pyfile\\py\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\pyfile\\py\\Lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'D:\\pyfile\\py\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest', 'D:\\pyfile\\py\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest._log', 'D:\\pyfile\\py\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('unittest.async_case',
   'D:\\pyfile\\py\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case', 'D:\\pyfile\\py\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest.loader', 'D:\\pyfile\\py\\Lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.main', 'D:\\pyfile\\py\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.mock', 'D:\\pyfile\\py\\Lib\\unittest\\mock.py', 'PYMODULE'),
  ('unittest.result', 'D:\\pyfile\\py\\Lib\\unittest\\result.py', 'PYMODULE'),
  ('unittest.runner', 'D:\\pyfile\\py\\Lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.signals', 'D:\\pyfile\\py\\Lib\\unittest\\signals.py', 'PYMODULE'),
  ('unittest.suite', 'D:\\pyfile\\py\\Lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.util', 'D:\\pyfile\\py\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('uri_template',
   'D:\\pyfile\\py\\Lib\\site-packages\\uri_template\\__init__.py',
   'PYMODULE'),
  ('uri_template.charset',
   'D:\\pyfile\\py\\Lib\\site-packages\\uri_template\\charset.py',
   'PYMODULE'),
  ('uri_template.expansions',
   'D:\\pyfile\\py\\Lib\\site-packages\\uri_template\\expansions.py',
   'PYMODULE'),
  ('uri_template.uritemplate',
   'D:\\pyfile\\py\\Lib\\site-packages\\uri_template\\uritemplate.py',
   'PYMODULE'),
  ('uri_template.variable',
   'D:\\pyfile\\py\\Lib\\site-packages\\uri_template\\variable.py',
   'PYMODULE'),
  ('urllib', 'D:\\pyfile\\py\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'D:\\pyfile\\py\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\pyfile\\py\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request', 'D:\\pyfile\\py\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('urllib.response', 'D:\\pyfile\\py\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib3',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib._appengine_environ',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\contrib\\_appengine_environ.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.packages',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\packages\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.backports',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\packages\\backports\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.backports.makefile',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\packages\\backports\\makefile.py',
   'PYMODULE'),
  ('urllib3.packages.backports.weakref_finalize',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\packages\\backports\\weakref_finalize.py',
   'PYMODULE'),
  ('urllib3.packages.six',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\packages\\six.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.request',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\request.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.queue',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\queue.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('utils', 'D:\\编程\\插件开发\\plugin\\register\\utils\\__init__.py', 'PYMODULE'),
  ('utils.fix_chromedriver',
   'D:\\编程\\插件开发\\plugin\\register\\utils\\fix_chromedriver.py',
   'PYMODULE'),
  ('uuid', 'D:\\pyfile\\py\\Lib\\uuid.py', 'PYMODULE'),
  ('wave', 'D:\\pyfile\\py\\Lib\\wave.py', 'PYMODULE'),
  ('wcwidth',
   'D:\\pyfile\\py\\Lib\\site-packages\\wcwidth\\__init__.py',
   'PYMODULE'),
  ('wcwidth.table_vs16',
   'D:\\pyfile\\py\\Lib\\site-packages\\wcwidth\\table_vs16.py',
   'PYMODULE'),
  ('wcwidth.table_wide',
   'D:\\pyfile\\py\\Lib\\site-packages\\wcwidth\\table_wide.py',
   'PYMODULE'),
  ('wcwidth.table_zero',
   'D:\\pyfile\\py\\Lib\\site-packages\\wcwidth\\table_zero.py',
   'PYMODULE'),
  ('wcwidth.unicode_versions',
   'D:\\pyfile\\py\\Lib\\site-packages\\wcwidth\\unicode_versions.py',
   'PYMODULE'),
  ('wcwidth.wcwidth',
   'D:\\pyfile\\py\\Lib\\site-packages\\wcwidth\\wcwidth.py',
   'PYMODULE'),
  ('webbrowser', 'D:\\pyfile\\py\\Lib\\webbrowser.py', 'PYMODULE'),
  ('webcolors',
   'D:\\pyfile\\py\\Lib\\site-packages\\webcolors\\__init__.py',
   'PYMODULE'),
  ('webcolors._conversion',
   'D:\\pyfile\\py\\Lib\\site-packages\\webcolors\\_conversion.py',
   'PYMODULE'),
  ('webcolors._definitions',
   'D:\\pyfile\\py\\Lib\\site-packages\\webcolors\\_definitions.py',
   'PYMODULE'),
  ('webcolors._html5',
   'D:\\pyfile\\py\\Lib\\site-packages\\webcolors\\_html5.py',
   'PYMODULE'),
  ('webcolors._normalization',
   'D:\\pyfile\\py\\Lib\\site-packages\\webcolors\\_normalization.py',
   'PYMODULE'),
  ('webcolors._types',
   'D:\\pyfile\\py\\Lib\\site-packages\\webcolors\\_types.py',
   'PYMODULE'),
  ('webdriver_manager',
   'D:\\pyfile\\py\\Lib\\site-packages\\webdriver_manager\\__init__.py',
   'PYMODULE'),
  ('webdriver_manager.chrome',
   'D:\\pyfile\\py\\Lib\\site-packages\\webdriver_manager\\chrome.py',
   'PYMODULE'),
  ('webdriver_manager.core',
   'D:\\pyfile\\py\\Lib\\site-packages\\webdriver_manager\\core\\__init__.py',
   'PYMODULE'),
  ('webdriver_manager.core.archive',
   'D:\\pyfile\\py\\Lib\\site-packages\\webdriver_manager\\core\\archive.py',
   'PYMODULE'),
  ('webdriver_manager.core.config',
   'D:\\pyfile\\py\\Lib\\site-packages\\webdriver_manager\\core\\config.py',
   'PYMODULE'),
  ('webdriver_manager.core.constants',
   'D:\\pyfile\\py\\Lib\\site-packages\\webdriver_manager\\core\\constants.py',
   'PYMODULE'),
  ('webdriver_manager.core.download_manager',
   'D:\\pyfile\\py\\Lib\\site-packages\\webdriver_manager\\core\\download_manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.driver',
   'D:\\pyfile\\py\\Lib\\site-packages\\webdriver_manager\\core\\driver.py',
   'PYMODULE'),
  ('webdriver_manager.core.driver_cache',
   'D:\\pyfile\\py\\Lib\\site-packages\\webdriver_manager\\core\\driver_cache.py',
   'PYMODULE'),
  ('webdriver_manager.core.file_manager',
   'D:\\pyfile\\py\\Lib\\site-packages\\webdriver_manager\\core\\file_manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.http',
   'D:\\pyfile\\py\\Lib\\site-packages\\webdriver_manager\\core\\http.py',
   'PYMODULE'),
  ('webdriver_manager.core.logger',
   'D:\\pyfile\\py\\Lib\\site-packages\\webdriver_manager\\core\\logger.py',
   'PYMODULE'),
  ('webdriver_manager.core.manager',
   'D:\\pyfile\\py\\Lib\\site-packages\\webdriver_manager\\core\\manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.os_manager',
   'D:\\pyfile\\py\\Lib\\site-packages\\webdriver_manager\\core\\os_manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\webdriver_manager\\core\\utils.py',
   'PYMODULE'),
  ('webdriver_manager.drivers',
   'D:\\pyfile\\py\\Lib\\site-packages\\webdriver_manager\\drivers\\__init__.py',
   'PYMODULE'),
  ('webdriver_manager.drivers.chrome',
   'D:\\pyfile\\py\\Lib\\site-packages\\webdriver_manager\\drivers\\chrome.py',
   'PYMODULE'),
  ('win32com',
   'D:\\pyfile\\py\\Lib\\site-packages\\win32com\\__init__.py',
   'PYMODULE'),
  ('win32com.client',
   'D:\\pyfile\\py\\Lib\\site-packages\\win32com\\client\\__init__.py',
   'PYMODULE'),
  ('win32com.client.CLSIDToClass',
   'D:\\pyfile\\py\\Lib\\site-packages\\win32com\\client\\CLSIDToClass.py',
   'PYMODULE'),
  ('win32com.client.build',
   'D:\\pyfile\\py\\Lib\\site-packages\\win32com\\client\\build.py',
   'PYMODULE'),
  ('win32com.client.dynamic',
   'D:\\pyfile\\py\\Lib\\site-packages\\win32com\\client\\dynamic.py',
   'PYMODULE'),
  ('win32com.client.gencache',
   'D:\\pyfile\\py\\Lib\\site-packages\\win32com\\client\\gencache.py',
   'PYMODULE'),
  ('win32com.client.genpy',
   'D:\\pyfile\\py\\Lib\\site-packages\\win32com\\client\\genpy.py',
   'PYMODULE'),
  ('win32com.client.makepy',
   'D:\\pyfile\\py\\Lib\\site-packages\\win32com\\client\\makepy.py',
   'PYMODULE'),
  ('win32com.client.selecttlb',
   'D:\\pyfile\\py\\Lib\\site-packages\\win32com\\client\\selecttlb.py',
   'PYMODULE'),
  ('win32com.client.util',
   'D:\\pyfile\\py\\Lib\\site-packages\\win32com\\client\\util.py',
   'PYMODULE'),
  ('win32com.server',
   'D:\\pyfile\\py\\Lib\\site-packages\\win32com\\server\\__init__.py',
   'PYMODULE'),
  ('win32com.server.dispatcher',
   'D:\\pyfile\\py\\Lib\\site-packages\\win32com\\server\\dispatcher.py',
   'PYMODULE'),
  ('win32com.server.exception',
   'D:\\pyfile\\py\\Lib\\site-packages\\win32com\\server\\exception.py',
   'PYMODULE'),
  ('win32com.server.policy',
   'D:\\pyfile\\py\\Lib\\site-packages\\win32com\\server\\policy.py',
   'PYMODULE'),
  ('win32com.server.util',
   'D:\\pyfile\\py\\Lib\\site-packages\\win32com\\server\\util.py',
   'PYMODULE'),
  ('win32com.shell',
   'D:\\pyfile\\py\\Lib\\site-packages\\win32comext\\shell\\__init__.py',
   'PYMODULE'),
  ('win32com.shell.shellcon',
   'D:\\pyfile\\py\\Lib\\site-packages\\win32comext\\shell\\shellcon.py',
   'PYMODULE'),
  ('win32com.universal',
   'D:\\pyfile\\py\\Lib\\site-packages\\win32com\\universal.py',
   'PYMODULE'),
  ('win32com.util',
   'D:\\pyfile\\py\\Lib\\site-packages\\win32com\\util.py',
   'PYMODULE'),
  ('win32con',
   'D:\\pyfile\\py\\Lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32evtlogutil',
   'D:\\pyfile\\py\\Lib\\site-packages\\win32\\lib\\win32evtlogutil.py',
   'PYMODULE'),
  ('win32traceutil',
   'D:\\pyfile\\py\\Lib\\site-packages\\win32\\lib\\win32traceutil.py',
   'PYMODULE'),
  ('winerror',
   'D:\\pyfile\\py\\Lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('wsgiref', 'D:\\pyfile\\py\\Lib\\wsgiref\\__init__.py', 'PYMODULE'),
  ('wsgiref.validate', 'D:\\pyfile\\py\\Lib\\wsgiref\\validate.py', 'PYMODULE'),
  ('wsproto',
   'D:\\pyfile\\py\\Lib\\site-packages\\wsproto\\__init__.py',
   'PYMODULE'),
  ('wsproto.connection',
   'D:\\pyfile\\py\\Lib\\site-packages\\wsproto\\connection.py',
   'PYMODULE'),
  ('wsproto.events',
   'D:\\pyfile\\py\\Lib\\site-packages\\wsproto\\events.py',
   'PYMODULE'),
  ('wsproto.extensions',
   'D:\\pyfile\\py\\Lib\\site-packages\\wsproto\\extensions.py',
   'PYMODULE'),
  ('wsproto.frame_protocol',
   'D:\\pyfile\\py\\Lib\\site-packages\\wsproto\\frame_protocol.py',
   'PYMODULE'),
  ('wsproto.handshake',
   'D:\\pyfile\\py\\Lib\\site-packages\\wsproto\\handshake.py',
   'PYMODULE'),
  ('wsproto.typing',
   'D:\\pyfile\\py\\Lib\\site-packages\\wsproto\\typing.py',
   'PYMODULE'),
  ('wsproto.utilities',
   'D:\\pyfile\\py\\Lib\\site-packages\\wsproto\\utilities.py',
   'PYMODULE'),
  ('xml', 'D:\\pyfile\\py\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.dom', 'D:\\pyfile\\py\\Lib\\xml\\dom\\__init__.py', 'PYMODULE'),
  ('xml.dom.NodeFilter',
   'D:\\pyfile\\py\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg', 'D:\\pyfile\\py\\Lib\\xml\\dom\\domreg.py', 'PYMODULE'),
  ('xml.dom.expatbuilder',
   'D:\\pyfile\\py\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'D:\\pyfile\\py\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom', 'D:\\pyfile\\py\\Lib\\xml\\dom\\minidom.py', 'PYMODULE'),
  ('xml.dom.pulldom', 'D:\\pyfile\\py\\Lib\\xml\\dom\\pulldom.py', 'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'D:\\pyfile\\py\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree', 'D:\\pyfile\\py\\Lib\\xml\\etree\\__init__.py', 'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\pyfile\\py\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\pyfile\\py\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\pyfile\\py\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\pyfile\\py\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers', 'D:\\pyfile\\py\\Lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\pyfile\\py\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax', 'D:\\pyfile\\py\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\pyfile\\py\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\pyfile\\py\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler', 'D:\\pyfile\\py\\Lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\pyfile\\py\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\pyfile\\py\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc', 'D:\\pyfile\\py\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client', 'D:\\pyfile\\py\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('xmlrpc.server', 'D:\\pyfile\\py\\Lib\\xmlrpc\\server.py', 'PYMODULE'),
  ('yaml', 'D:\\pyfile\\py\\Lib\\site-packages\\yaml\\__init__.py', 'PYMODULE'),
  ('yaml.composer',
   'D:\\pyfile\\py\\Lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'D:\\pyfile\\py\\Lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'D:\\pyfile\\py\\Lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.dumper',
   'D:\\pyfile\\py\\Lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'D:\\pyfile\\py\\Lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.error',
   'D:\\pyfile\\py\\Lib\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('yaml.events',
   'D:\\pyfile\\py\\Lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.loader',
   'D:\\pyfile\\py\\Lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'D:\\pyfile\\py\\Lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.parser',
   'D:\\pyfile\\py\\Lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.reader',
   'D:\\pyfile\\py\\Lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.representer',
   'D:\\pyfile\\py\\Lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.resolver',
   'D:\\pyfile\\py\\Lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.scanner',
   'D:\\pyfile\\py\\Lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.serializer',
   'D:\\pyfile\\py\\Lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.tokens',
   'D:\\pyfile\\py\\Lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('zipfile', 'D:\\pyfile\\py\\Lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'D:\\pyfile\\py\\Lib\\zipimport.py', 'PYMODULE'),
  ('zipp', 'D:\\pyfile\\py\\Lib\\site-packages\\zipp\\__init__.py', 'PYMODULE'),
  ('zipp._functools',
   'D:\\pyfile\\py\\Lib\\site-packages\\zipp\\_functools.py',
   'PYMODULE'),
  ('zipp.compat',
   'D:\\pyfile\\py\\Lib\\site-packages\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('zipp.compat.overlay',
   'D:\\pyfile\\py\\Lib\\site-packages\\zipp\\compat\\overlay.py',
   'PYMODULE'),
  ('zipp.compat.py310',
   'D:\\pyfile\\py\\Lib\\site-packages\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('zipp.compat.py313',
   'D:\\pyfile\\py\\Lib\\site-packages\\zipp\\compat\\py313.py',
   'PYMODULE'),
  ('zipp.glob',
   'D:\\pyfile\\py\\Lib\\site-packages\\zipp\\glob.py',
   'PYMODULE'),
  ('zmq', 'D:\\pyfile\\py\\Lib\\site-packages\\zmq\\__init__.py', 'PYMODULE'),
  ('zmq._future',
   'D:\\pyfile\\py\\Lib\\site-packages\\zmq\\_future.py',
   'PYMODULE'),
  ('zmq._typing',
   'D:\\pyfile\\py\\Lib\\site-packages\\zmq\\_typing.py',
   'PYMODULE'),
  ('zmq.asyncio',
   'D:\\pyfile\\py\\Lib\\site-packages\\zmq\\asyncio.py',
   'PYMODULE'),
  ('zmq.backend',
   'D:\\pyfile\\py\\Lib\\site-packages\\zmq\\backend\\__init__.py',
   'PYMODULE'),
  ('zmq.backend.cython',
   'D:\\pyfile\\py\\Lib\\site-packages\\zmq\\backend\\cython\\__init__.py',
   'PYMODULE'),
  ('zmq.backend.select',
   'D:\\pyfile\\py\\Lib\\site-packages\\zmq\\backend\\select.py',
   'PYMODULE'),
  ('zmq.constants',
   'D:\\pyfile\\py\\Lib\\site-packages\\zmq\\constants.py',
   'PYMODULE'),
  ('zmq.error',
   'D:\\pyfile\\py\\Lib\\site-packages\\zmq\\error.py',
   'PYMODULE'),
  ('zmq.eventloop',
   'D:\\pyfile\\py\\Lib\\site-packages\\zmq\\eventloop\\__init__.py',
   'PYMODULE'),
  ('zmq.eventloop.zmqstream',
   'D:\\pyfile\\py\\Lib\\site-packages\\zmq\\eventloop\\zmqstream.py',
   'PYMODULE'),
  ('zmq.green',
   'D:\\pyfile\\py\\Lib\\site-packages\\zmq\\green\\__init__.py',
   'PYMODULE'),
  ('zmq.green.core',
   'D:\\pyfile\\py\\Lib\\site-packages\\zmq\\green\\core.py',
   'PYMODULE'),
  ('zmq.green.device',
   'D:\\pyfile\\py\\Lib\\site-packages\\zmq\\green\\device.py',
   'PYMODULE'),
  ('zmq.green.poll',
   'D:\\pyfile\\py\\Lib\\site-packages\\zmq\\green\\poll.py',
   'PYMODULE'),
  ('zmq.sugar',
   'D:\\pyfile\\py\\Lib\\site-packages\\zmq\\sugar\\__init__.py',
   'PYMODULE'),
  ('zmq.sugar.attrsettr',
   'D:\\pyfile\\py\\Lib\\site-packages\\zmq\\sugar\\attrsettr.py',
   'PYMODULE'),
  ('zmq.sugar.context',
   'D:\\pyfile\\py\\Lib\\site-packages\\zmq\\sugar\\context.py',
   'PYMODULE'),
  ('zmq.sugar.frame',
   'D:\\pyfile\\py\\Lib\\site-packages\\zmq\\sugar\\frame.py',
   'PYMODULE'),
  ('zmq.sugar.poll',
   'D:\\pyfile\\py\\Lib\\site-packages\\zmq\\sugar\\poll.py',
   'PYMODULE'),
  ('zmq.sugar.socket',
   'D:\\pyfile\\py\\Lib\\site-packages\\zmq\\sugar\\socket.py',
   'PYMODULE'),
  ('zmq.sugar.stopwatch',
   'D:\\pyfile\\py\\Lib\\site-packages\\zmq\\sugar\\stopwatch.py',
   'PYMODULE'),
  ('zmq.sugar.tracker',
   'D:\\pyfile\\py\\Lib\\site-packages\\zmq\\sugar\\tracker.py',
   'PYMODULE'),
  ('zmq.sugar.version',
   'D:\\pyfile\\py\\Lib\\site-packages\\zmq\\sugar\\version.py',
   'PYMODULE'),
  ('zmq.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\zmq\\utils\\__init__.py',
   'PYMODULE'),
  ('zmq.utils.garbage',
   'D:\\pyfile\\py\\Lib\\site-packages\\zmq\\utils\\garbage.py',
   'PYMODULE'),
  ('zmq.utils.interop',
   'D:\\pyfile\\py\\Lib\\site-packages\\zmq\\utils\\interop.py',
   'PYMODULE'),
  ('zmq.utils.jsonapi',
   'D:\\pyfile\\py\\Lib\\site-packages\\zmq\\utils\\jsonapi.py',
   'PYMODULE'),
  ('zope', '-', 'PYMODULE'),
  ('zope.event',
   'D:\\pyfile\\py\\Lib\\site-packages\\zope\\event\\__init__.py',
   'PYMODULE'),
  ('zope.interface',
   'D:\\pyfile\\py\\Lib\\site-packages\\zope\\interface\\__init__.py',
   'PYMODULE'),
  ('zope.interface._compat',
   'D:\\pyfile\\py\\Lib\\site-packages\\zope\\interface\\_compat.py',
   'PYMODULE'),
  ('zope.interface.declarations',
   'D:\\pyfile\\py\\Lib\\site-packages\\zope\\interface\\declarations.py',
   'PYMODULE'),
  ('zope.interface.exceptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\zope\\interface\\exceptions.py',
   'PYMODULE'),
  ('zope.interface.interface',
   'D:\\pyfile\\py\\Lib\\site-packages\\zope\\interface\\interface.py',
   'PYMODULE'),
  ('zope.interface.interfaces',
   'D:\\pyfile\\py\\Lib\\site-packages\\zope\\interface\\interfaces.py',
   'PYMODULE'),
  ('zope.interface.ro',
   'D:\\pyfile\\py\\Lib\\site-packages\\zope\\interface\\ro.py',
   'PYMODULE'),
  ('zope.interface.verify',
   'D:\\pyfile\\py\\Lib\\site-packages\\zope\\interface\\verify.py',
   'PYMODULE')])
