<?php

namespace plugin\Revisecreattime\controller;

use app\common\controller\BasePlugin;
use think\facade\Db;
use think\facade\View;

class Api extends BasePlugin {

    protected $scene = [
        'merchant',
        'user',
    ];
    
    protected $noNeedLogin = [];

    public function index() {
        return View::fetch();
    }


    /**
     * 获取用户创建时间
     */
    public function fetchData() {
        try {
            if (!$this->user) {
                return $this->error('用户未登录', null, 401);
            }

            trace('当前用户ID：' . $this->user->id);

            $userInfo = Db::name('user')
                ->where('id', $this->user->id)
                ->field('id, username, nickname, create_time')
                ->find();
            
            trace('查询结果：' . json_encode($userInfo));
            
            if (!$userInfo) {
                return $this->error('未找到用户信息');
            }
            
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => [
                    'id' => $userInfo['id'],
                    'username' => $userInfo['username'],
                    'nickname' => $userInfo['nickname'],
                    'create_time' => (int)$userInfo['create_time']
                ]
            ]);
        } catch (\Exception $e) {
            trace('错误信息：' . $e->getMessage());
            return $this->error('获取数据失败：' . $e->getMessage());
        }
    }

    /**
     * 保存新的创建时间
     */
    public function save() {
        if (!$this->user) {
            return $this->error('用户未登录', null, 401);
        }

        $create_time = $this->request->post('create_time');
        
        if (!$create_time || !($timestamp = strtotime($create_time))) {
            return $this->error('时间格式不正确');
        }

        try {
            $result = Db::name('user')
                ->where('id', $this->user->id)
                ->update(['create_time' => $timestamp]);
                
            return json([
                'code' => 200,
                'msg' => $result !== false ? '保存成功' : '保存失败',
                'data' => null
            ]);
        } catch (\Exception $e) {
            trace('保存错误：' . $e->getMessage());
            return $this->error('保存失败：' . $e->getMessage());
        }
    }
}
