<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>邀请码管理</title>
    <style>
        #app {
            padding: 20px;
        }
        .page-title {
            font-size: 16px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .table-header {
            background-color: #F5F7FA;
            padding: 12px 0;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }
        .table-header-item {
            flex: 1;
            text-align: left;
            padding-left: 20px;
            color: #606266;
            font-size: 14px;
        }
        .table-row {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #EBEEF5;
        }
        .table-cell {
            flex: 1;
            padding-left: 20px;
            color: #606266;
        }
        .empty-data {
            text-align: center;
            padding: 32px 0;
            color: #909399;
        }
        .success-rate {
            padding: 4px 8px;
            border-radius: 4px;
            color: white;
        }
        .success-rate-high {
            background-color: #67C23A;
        }
        .success-rate-medium {
            background-color: #E6A23C;
        }
        .success-rate-low {
            background-color: #F56C6C;
        }
        /* 添加下拉按钮样式 */
        .el-dropdown {
            margin-left: 10px;
        }
        
        .el-dropdown .el-button {
            display: flex;
            align-items: center;
        }
        
        /* 自定义下拉图标样式 */
        .dropdown-icon {
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid currentColor;
            margin-left: 5px;
            display: inline-block;
            vertical-align: middle;
        }
    </style>
</head>
<body>

<div id="app">
    <div class="page-title">
        <span>邀请码管理</span>
        <el-dropdown @command="handleSortChange">
            <el-button type="primary" size="small">
                {{ getSortLabel(sortType) }}
                <span class="dropdown-icon"></span>
            </el-button>
            <template #dropdown>
                <el-dropdown-menu>
                    <el-dropdown-item command="total_count">邀请码总数排序</el-dropdown-item>
                    <el-dropdown-item command="used_count">已使用数量排序</el-dropdown-item>
                    <el-dropdown-item command="unused_count">未使用数量排序</el-dropdown-item>
                    <el-dropdown-item command="success_rate">使用成功率排序</el-dropdown-item>
                </el-dropdown-menu>
            </template>
        </el-dropdown>
        <el-button type="primary" size="small" @click="refreshData">刷新数据</el-button>
    </div>

    <div v-loading="loading">
        <!-- 表头 -->
        <div class="table-header">
            <div class="table-header-item">商家账户</div>
            <div class="table-header-item">邀请码总数</div>
            <div class="table-header-item">未使用</div>
            <div class="table-header-item">已使用</div>
            <div class="table-header-item">使用成功率</div>
            <div class="table-header-item">操作</div>
        </div>

        <!-- 数据行 -->
        <div v-if="userStats.length > 0">
            <div class="table-row" v-for="item in userStats" :key="item.user_id">
                <div class="table-cell">{{ item.username }}</div>
                <div class="table-cell">{{ item.total_count }}</div>
                <div class="table-cell">{{ item.unused_count }}</div>
                <div class="table-cell">{{ item.used_count }}</div>
                <div class="table-cell">
                    <span :class="getSuccessRateClass(item.success_rate)" class="success-rate">
                        {{ item.success_rate }}%
                    </span>
                </div>
                <div class="table-cell">
                    <el-button 
                        type="danger" 
                        size="small" 
                        @click="handleDeleteUnused(item.user_id)"
                        :disabled="item.unused_count === 0">
                        删除未使用邀请码
                    </el-button>
                </div>
            </div>
        </div>

        <!-- 分页 -->
        <div class="pagination" style="margin-top: 20px; text-align: center;">
            <span>共 {{ total }} 条</span>
            <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[15, 30, 50, 100]"
                background
                layout="prev, pager, next, jumper"
                :total="total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
            <span>前往 <el-input v-model="jumpPage" style="width: 60px; margin: 0 5px;" size="small" /> 页</span>
        </div>
    </div>
</div>

<script src="/static/others/vue/vue.global.prod.js"></script>
<script src="/static/others/element-plus/index.full.min.js"></script>
<script src="/static/others/axios/axios.min.js"></script>
<script src="/static/others/element-plus/locale/zh-cn.min.js"></script>

<script>
const { ref } = Vue;
const { ElMessage, ElMessageBox } = ElementPlus;

const app = Vue.createApp({
    setup() {
        const userStats = ref([]);
        const loading = ref(false);
        const sortType = ref('total_count');
        const currentPage = ref(1);
        const pageSize = ref(15);
        const total = ref(0);
        const jumpPage = ref('');

        // 获取排序类型显示文本
        const getSortLabel = (type) => {
            const labels = {
                'total_count': '邀请码总数排序',
                'used_count': '已使用数量排序',
                'unused_count': '未使用数量排序',
                'success_rate': '使用成功率排序'
            };
            return labels[type] || '邀请码总数排序';
        };

        // 获取数据
        const fetchData = async () => {
            loading.value = true;
            try {
                const res = await axios.get("/plugin/Invitationcode/Api/getInviteCodes", {
                    params: {
                        sort_type: sortType.value,
                        page: currentPage.value,
                        page_size: pageSize.value
                    }
                });
                if (res.data.code === 200) {
                    userStats.value = res.data.data.list;
                    total.value = res.data.data.total;
                } else {
                    ElMessage.error(res.data.msg);
                }
            } catch (error) {
                console.error('Error:', error);
                ElMessage.error('获取数据失败');
            } finally {
                loading.value = false;
            }
        };

        // 处理排序变化
        const handleSortChange = (command) => {
            sortType.value = command;
            fetchData();
        };

        // 刷新数据
        const refreshData = () => {
            fetchData();
        };

        // 删除未使用邀请码
        const handleDeleteUnused = async (userId) => {
            try {
                await ElMessageBox.confirm(
                    '确定要删除该商家所有未使用的邀请码吗？',
                    '确认删除',
                    {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning',
                    }
                );

                const res = await axios.post("/plugin/Invitationcode/Api/deleteUnusedCodes", {
                    user_id: userId
                });

                if (res.data.code === 200) {
                    ElMessage.success('删除成功');
                    fetchData();
                } else {
                    ElMessage.error(res.data.msg);
                }
            } catch (error) {
                if (error !== 'cancel') {
                    ElMessage.error('删除失败');
                }
            }
        };

        // 处理页码变化
        const handleCurrentChange = (val) => {
            currentPage.value = val;
            fetchData();
        };

        // 处理每页条数变化
        const handleSizeChange = (val) => {
            pageSize.value = val;
            currentPage.value = 1;
            fetchData();
        };

        // 添加获取成功率样式类的函数
        const getSuccessRateClass = (rate) => {
            if (rate >= 70) {
                return 'success-rate-high';
            } else if (rate >= 30) {
                return 'success-rate-medium';
            } else {
                return 'success-rate-low';
            }
        };

        // 初始化时获取数据
        fetchData();

        return {
            userStats,
            loading,
            sortType,
            currentPage,
            pageSize,
            total,
            jumpPage,
            getSortLabel,
            refreshData,
            handleSortChange,
            handleDeleteUnused,
            handleCurrentChange,
            handleSizeChange,
            getSuccessRateClass
        };
    }
});

app.use(ElementPlus, {
    locale: ElementPlusLocaleZhCn
});
app.mount("#app");
</script>
</body>
</html>
