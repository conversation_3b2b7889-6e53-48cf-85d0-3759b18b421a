<?php

namespace plugin\Modificationinstructions\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Log;
use think\facade\Db;

class User extends BasePlugin {

    protected $scene = ['user'];  // 只允许普通用户访问
    
    protected $noNeedLogin = [];

    // 修改使用说明页面
    public function index() {
        return View::fetch();
    }

    // 获取配置
    public function getConfig() {
        try {
            $configFile = app()->getRootPath() . 'plugin/Modificationinstructions/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            // 确保返回必要的配置项
            if (!isset($config['instructions_config'])) {
                $config['instructions_config'] = [
                    'status' => true,
                    'allow_html' => false,
                    'need_approval' => false,  // 禁用审核功能
                    'max_length' => 2000
                ];
            }
            
            // 强制禁用审核功能，避免需要数据库表
            $config['instructions_config']['need_approval'] = false;

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $config['instructions_config']
            ]);
        } catch (\Exception $e) {
            Log::error('获取配置失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取配置失败：' . $e->getMessage()]);
        }
    }

    // 获取用户对接的上级商品列表
    public function getParentGoodsList() {
        try {
            // 检查功能开启状态
            $configFile = app()->getRootPath() . 'plugin/Modificationinstructions/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            if (!($config['instructions_config']['status'] ?? true)) {
                return json(['code' => 403, 'msg' => '功能已关闭']);
            }
            
            $page = input('page/d', 1);
            $limit = input('limit/d', 10);
            $offset = ($page - 1) * $limit;
            $search = input('search/s', '');
            
            // 获取当前用户的商品
            $query = Db::name('goods')
                ->alias('g')
                ->where('g.user_id', $this->user->id)
                ->where('g.parent_id', '>', 0)  // 只获取有上级商品的商品
                ->where('g.delete_time', null); // 排除已删除
                
            // 添加搜索条件
            if (!empty($search)) {
                $query->where('g.name|pg.name', 'like', "%{$search}%");
            }
            
            // 联表查询获取上级商品信息
            $query = $query->join('goods pg', 'g.parent_id = pg.id', 'LEFT')
                ->join('user pu', 'pg.user_id = pu.id', 'LEFT')
                ->join('goods_card gc', 'g.id = gc.goods_id', 'LEFT')
                ->field([
                    'g.id',
                    'g.name',
                    'g.parent_id',
                    'pg.name as parent_name',
                    'pu.username as parent_username',
                    'pu.nickname as parent_nickname',
                    'gc.instructions'
                ]);
                
            // 获取总记录数和分页数据
            $total = Db::table($query->buildSql() . ' as temp')->count();
            $goodsList = $query->limit($offset, $limit)->select()->toArray();
            
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $goodsList,
                'total' => $total
            ]);
            
        } catch (\Exception $e) {
            Log::error('获取商品列表失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取数据失败：' . $e->getMessage()]);
        }
    }
    
    // 获取上级商品的使用说明
    public function getParentInstructions() {
        try {
            $goodsId = input('goods_id/d', 0);
            
            if ($goodsId <= 0) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }
            
            // 验证商品是否属于当前用户且存在上级商品
            $goods = Db::name('goods')
                ->where('id', $goodsId)
                ->where('user_id', $this->user->id)
                ->where('parent_id', '>', 0)
                ->find();
                
            if (!$goods) {
                return json(['code' => 403, 'msg' => '无权操作或商品不存在']);
            }
            
            // 获取上级商品信息
            $parentGoods = Db::name('goods')
                ->alias('g')
                ->where('g.id', $goods['parent_id'])
                ->join('goods_card gc', 'g.id = gc.goods_id', 'LEFT')
                ->join('user u', 'g.user_id = u.id', 'LEFT')
                ->field([
                    'g.id',
                    'g.name',
                    'g.user_id',
                    'u.username',
                    'u.nickname',
                    'gc.instructions as parent_instructions'
                ])
                ->find();
                
            if (!$parentGoods) {
                return json(['code' => 404, 'msg' => '上级商品不存在']);
            }
            
            // 获取自己商品的使用说明
            $goodsCard = Db::name('goods_card')
                ->where('goods_id', $goodsId)
                ->field('instructions')
                ->find();
                
            $myInstructions = $goodsCard ? $goodsCard['instructions'] : '';
                
            $result = [
                'goods_id' => $goodsId,
                'goods_name' => $goods['name'],
                'parent_id' => $parentGoods['id'],
                'parent_name' => $parentGoods['name'],
                'parent_username' => $parentGoods['username'],
                'parent_nickname' => $parentGoods['nickname'],
                'parent_instructions' => $parentGoods['parent_instructions'] ?? '',
                'my_instructions' => $myInstructions
            ];
            
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $result
            ]);
            
        } catch (\Exception $e) {
            Log::error('获取使用说明失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取数据失败：' . $e->getMessage()]);
        }
    }
    
    // 修改商品使用说明
    public function updateInstructions() {
        try {
            $goodsId = input('goods_id/d', 0);
            $instructions = input('instructions/s', '');
            
            // 检查功能开启状态
            $configFile = app()->getRootPath() . 'plugin/Modificationinstructions/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            if (!($config['instructions_config']['status'] ?? true)) {
                return json(['code' => 403, 'msg' => '功能已关闭']);
            }
            
            // 验证内容长度
            $maxLength = $config['instructions_config']['max_length'] ?? 2000;
            if (mb_strlen($instructions) > $maxLength) {
                return json(['code' => 400, 'msg' => "内容超过最大长度限制({$maxLength}字符)"]);
            }
            
            // 如果不允许HTML，则清理HTML标签
            if (!($config['instructions_config']['allow_html'] ?? false)) {
                $instructions = strip_tags($instructions);
            }
            
            // 验证商品是否属于当前用户且存在上级商品
            $goods = Db::name('goods')
                ->where('id', $goodsId)
                ->where('user_id', $this->user->id)
                ->where('parent_id', '>', 0)
                ->find();
                
            if (!$goods) {
                return json(['code' => 403, 'msg' => '无权操作或商品不存在']);
            }
            
            Db::startTrans();
            
            try {
                // 直接更新使用说明
                $goodsCard = Db::name('goods_card')
                    ->where('goods_id', $goodsId)
                    ->find();
                    
                if ($goodsCard) {
                    // 更新已有记录
                    Db::name('goods_card')
                        ->where('goods_id', $goodsId)
                        ->update(['instructions' => $instructions]);
                } else {
                    // 创建新记录
                    Db::name('goods_card')->insert([
                        'goods_id' => $goodsId,
                        'instructions' => $instructions
                    ]);
                }
                
                Db::commit();
                
                return json([
                    'code' => 200,
                    'msg' => '修改成功'
                ]);
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
            
        } catch (\Exception $e) {
            Log::error('修改使用说明失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '修改失败：' . $e->getMessage()]);
        }
    }
    
    // 复制上级商品使用说明
    public function copyParentInstructions() {
        try {
            $goodsId = input('goods_id/d', 0);
            
            if ($goodsId <= 0) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }
            
            // 验证商品是否属于当前用户且存在上级商品
            $goods = Db::name('goods')
                ->where('id', $goodsId)
                ->where('user_id', $this->user->id)
                ->where('parent_id', '>', 0)
                ->find();
                
            if (!$goods) {
                return json(['code' => 403, 'msg' => '无权操作或商品不存在']);
            }
            
            // 获取上级商品使用说明
            $parentCard = Db::name('goods_card')
                ->where('goods_id', $goods['parent_id'])
                ->find();
                
            if (!$parentCard || empty($parentCard['instructions'])) {
                return json(['code' => 404, 'msg' => '上级商品没有使用说明']);
            }
            
            // 直接复制
            $goodsCard = Db::name('goods_card')
                ->where('goods_id', $goodsId)
                ->find();
                
            if ($goodsCard) {
                // 更新已有记录
                Db::name('goods_card')
                    ->where('goods_id', $goodsId)
                    ->update(['instructions' => $parentCard['instructions']]);
            } else {
                // 创建新记录
                Db::name('goods_card')->insert([
                    'goods_id' => $goodsId,
                    'instructions' => $parentCard['instructions']
                ]);
            }
            
            return json([
                'code' => 200,
                'msg' => '复制成功'
            ]);
            
        } catch (\Exception $e) {
            Log::error('复制上级使用说明失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '操作失败：' . $e->getMessage()]);
        }
    }
}