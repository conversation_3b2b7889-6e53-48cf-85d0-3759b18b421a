<?php

namespace plugin\Questionnaires;

use app\common\library\Plugin;
use think\facade\Db;
use think\facade\Env;

class Questionnaires extends Plugin {

    public function install() {
        // 使用Env获取数据库前缀
        $tableName = Env::get('DB_PREFIX') . 'plugin_reports';
        
        echo "开始安装问卷调查插件...\n";
        echo "正在创建数据表...\n";
        echo "数据库前缀: [" . Env::get('DB_PREFIX') . "]\n";
        echo "创建表: {$tableName}\n";
        
        // 使用heredoc语法定义SQL
        $sql = <<<SQL
CREATE TABLE IF NOT EXISTS `{$tableName}` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `shop_link` varchar(500) NOT NULL DEFAULT '' COMMENT '店铺链接',
    `shop_name` varchar(255) NOT NULL DEFAULT '' COMMENT '店铺名称',
    `product_link` varchar(500) NOT NULL DEFAULT '' COMMENT '商品链接',
    `violation_type` varchar(100) NOT NULL DEFAULT '' COMMENT '违规类型',
    `proofs` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '证明材料JSON',
    `remark` text COMMENT '备注说明',
    `email` varchar(100) NOT NULL DEFAULT '' COMMENT '联系邮箱',
    `ip` varchar(50) DEFAULT '' COMMENT '提交IP',
    `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态 0未处理 1已处理 2已驳回',
    `reject_reason` text COMMENT '驳回原因',
    `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_email` (`email`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='违规商品举报表';
SQL;

        // 检查表是否存在
        $has_tables = Db::query("SHOW TABLES LIKE '{$tableName}'");
        
        // 表已经存在
        if (!empty($has_tables)) {
            echo "表 {$tableName} 已存在，跳过创建！\n";
        } else {
            // 表不存在，创建表
            try {
                Db::execute($sql);
                echo "数据表创建完成！\n";
            } catch (\Exception $e) {
                echo "创建表失败: " . $e->getMessage() . "\n";
                // 记录错误日志
                file_put_contents(__DIR__ . '/install_error.log', date('Y-m-d H:i:s') . " - 创建表失败: " . $e->getMessage() . "\n", FILE_APPEND);
                return false;
            }
        }
        
        echo "插件安装成功！\n";
        return true;
    }

    public function uninstall() {
        // 使用Env获取数据库前缀
        $tableName = Env::get('DB_PREFIX') . 'plugin_reports';
        
        try {
            // 删除举报信息表
            Db::execute("DROP TABLE IF EXISTS `{$tableName}`");
            echo "表 {$tableName} 删除成功\n";
        } catch (\Exception $e) {
            echo "删除表失败: " . $e->getMessage() . "\n";
            return false;
        }
        
        return true;
    }
} 