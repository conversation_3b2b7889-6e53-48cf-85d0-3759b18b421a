<?php

return array (
  'chat_description' => '欢迎使用在线客服系统，请在下方输入您的问题，我们的客服人员将会尽快回复您。',
  'settings' => 
  array (
    'chat_enabled' => true,
    'file_upload_enabled' => true,
    'notification_enabled' => true,
    'auto_response_enabled' => true,
    'offline_message_enabled' => true,
  ),
  'emoji_enabled' => true,
  'upload_config' => 
  array (
    'max_file_size' => '5242880',
    'allowed_extensions' => 
    array (
      0 => 'jpg',
      1 => 'jpeg',
      2 => 'png',
      3 => 'gif',
      4 => 'pdf',
      5 => 'doc',
      6 => 'docx',
      7 => 'xls',
      8 => 'xlsx',
      9 => 'zip',
    ),
    'upload_api' => '/shopApi/Upload/file',
  ),
  'staff_config' => 
  array (
    'max_concurrent_chats' => '5',
    'auto_allocation' => '',
    'chat_timeout' => '1800',
  ),
  'session_config' => 
  array (
    'save_history' => true,
    'history_days' => '30',
  ),
  'visitor_info' => 
  array (
    'name_required' => true,
    'email_required' => true,
    'phone_required' => '',
    'question_required' => true,
  ),
  'ui_config' => 
  array (
    'theme_color' => '#41b883',
    'secondary_color' => '#348ceb',
    'chat_position' => 'right',
    'window_height' => '500px',
    'window_width' => '360px',
    'button_text' => '联系客服',
    'header_text' => '在线客服',
    'placeholder_text' => '请输入您的问题...',
    'send_button_text' => '发送',
    'timestamp_format' => 'Y-m-d H:i:s',
  ),
  'notification_config' =>
  array (
    'sound_enabled' => true,
    'browser_notification' => true,
    'email_notification' => true,
    'email_template' => '您有一条新的客服消息，请登录系统查看。',
    'new_message_sound' => '/static/plugins/Customersystem/sounds/notification.mp3',
    'platform_service_email' => '', // 平台客服邮箱
  ),
  'analytics_config' => 
  array (
    'track_chat_duration' => true,
    'track_response_time' => true,
    'track_satisfaction' => true,
    'satisfaction_options' => 
    array (
      0 => '非常满意',
      1 => '满意',
      2 => '一般',
      3 => '不满意',
      4 => '非常不满意',
    ),
  ),
  'faq_config' => 
  array (
    'enabled' => true,
    'categories' => 
    array (
      0 => 
      array (
        'id' => 'general',
        'name' => '常见问题',
        'questions' => 
        array (
          0 => 
          array (
            'question' => '如何联系客服？',
            'answer' => '您可以直接在当前页面与客服对话，或通过邮件联系我们。',
          ),
          1 => 
          array (
            'question' => '客服服务时间？',
            'answer' => '我们的客服服务时间为周一至周五 9:00-18:00。',
          ),
        ),
      ),
      1 => 
      array (
        'id' => 'technical',
        'name' => '技术支持',
        'questions' => 
        array (
          0 => 
          array (
            'question' => '如何重置密码？',
            'answer' => '您可以在登录页面点击&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot;忘记密码&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot;，按照提示操作即可重置密码。',
          ),
          1 => 
          array (
            'question' => '无法上传文件怎么办？',
            'answer' => '请确保您的文件大小不超过5MB，且格式为常见的图片或文档格式。',
          ),
        ),
      ),
    ),
  ),
  'features' => 
  array (
    'chat_enabled' => true,
    'file_upload_enabled' => true,
    'emoji_enabled' => true,
    'faq_enabled' => true,
    'rating_enabled' => true,
    'captcha_enabled' => '',
  ),
  'preset_replies' => 
  array (
    'enabled' => true,
    'title' => '以下是常见问题，您看下是否可以帮助到您呢？',
    'items' => 
    array (
      0 => 
      array (
        'label' => '平台安全吗',
        'content' => '平台运用先进的安全技术保护用户在平台账户中存储的个人信息、账户信息以及交易记录的安全。平台拥有完善的全监测系统，可以及时发现网站的非正常访问并做相应的安全响应。重金采用高防服务器。让您用着安全，放心。',
      ),
      1 => 
      array (
        'label' => '如何注册商家？',
        'content' => '您好您好，我来咯您有什么需要？您先发给我啦让我给您看看 帮您处理一下啦，如果我长时间未回复您的消息请您联系客服 工作时间：早9点至晚10点！',
      ),
      2 => 
      array (
        'label' => '投诉订单什么时候到账？',
        'content' => '抱歉抱歉，咱们平台的投诉处理方式是：您投诉后等待24小时商家处理期，如商家未回复或者同意退款后或者上方时间进入裁决期小梦才能为您退款哒~(商家同意退款/进入裁决期间24小时内小梦将会全力为您进行退款请您耐心等待一下哦~）

若您还想咨询其他问题，请点击选择',
      ),
    ),
  ),
  'preset_questions' => 
  array (
    'title' => '常见问题',
    'description' => '以下是用户经常咨询的问题，点击即可直接提问：',
  ),
);
