<?php

namespace app\index\Blackglod55\controller;

use app\common\controller\BaseIndex;
use app\common\model\Article as ArticleModel;

class Api extends BaseIndex {

    public function systemConfig() {
        $config = [
            'website' => [
                'title' => sysconf('website.title'),
                'app_name' => sysconf('website.app_name'),
                'logo' => sysconf('website.logo'),
                'favicon' => sysconf('website.favicon'),
                'description' => sysconf('website.description'),
                'keywords' => sysconf('website.keywords'),
                'site_status' => sysconf('website.site_status'),
                'site_open' => sysconf('website.site_open'),
                'site_close_tip' => sysconf('website.site_close_tip'),
                'copy_right' => sysconf('website.copy_right'),
                'icp_number' => sysconf('website.icp_number'),
                'ga_number' => sysconf('website.ga_number'),
                'tongji' => sysconf('website.tongji'),
                'icp_cert' => sysconf('website.icp_cert'),
            ],
            'kefu' => [
                'qq' => sysconf('kefu.qq'),
                'weixin' => sysconf('kefu.weixin'),
                'work_weixin' => sysconf('kefu.work_weixin'),
                'mobile' => sysconf('kefu.mobile'),
                'qrcode' => sysconf('kefu.qrcode'),
            ],
            'nav' => get_nav(),
            'theme' => [
                'code' => get_template_code(),
                'params' => get_template_params()
            ],
        ];

        $this->success('success', null, $config);
    }

    public function indexArticle() {
        $notice = ArticleModel::hasWhere("category", ['alias' => 'notice'])->where(['Article.status' => 1])->order("sort desc,id desc")->limit(2)->select();
        $faq = ArticleModel::hasWhere("category", ['alias' => 'faq'])->where(['Article.status' => 1])->order("sort desc,id desc")->limit(2)->select();
        $news = ArticleModel::hasWhere("category", ['alias' => 'news'])->where(['Article.status' => 1])->order("sort desc,id desc")->limit(2)->select();
        $settlement = ArticleModel::hasWhere("category", ['alias' => 'settlement'])->where(['Article.status' => 1])->order("sort desc,id desc")->limit(2)->select();
        $this->success('success', null, [
            'notice' => $notice,
            'faq' => $faq,
            'news' => $news,
            'settlement' => $settlement
        ]);
    }

    public function categoryArticles() {
        $alias = $this->request->post('alias/s', '');
        $current = $this->request->post('current/d', 0);
        $pageSize = $this->request->post('pageSize/d', 0);

        $model = ArticleModel::with(["category"])->hasWhere("category", ['alias' => $alias])->where(['Article.status' => 1]);
        $count = $model->count();
        $lists = $model->order('sort desc,id desc')->page($current, $pageSize)->select();
        $res = [
            'total' => $count,
            'list' => $lists
        ];

        $this->success("success", null, $res);
    }

    public function articleDetail() {
        $id = $this->request->post('id/d', 0);
        $alias = $this->request->post('alias/s', '');

        $where = [];
        if ($id > 0) {
            $where['id'] = $id;
        } elseif ($alias != '') {
            $where['alias'] = $alias;
        }

        $article = ArticleModel::with(["category"])->where($where)->find();
        if (!$article) {
            $this->error("文章不存在");
        }

        $this->success("success", null, $article);
    }
}
