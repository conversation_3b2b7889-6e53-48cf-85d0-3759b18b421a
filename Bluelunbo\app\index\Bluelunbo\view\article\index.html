<!doctype html>
<html lang="zh">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>{$title}</title>
        
        <link rel="shortcut icon" href="{$favicon}" type="image/x-icon">
        <!-- Font Awesome 图标库 -->
        <link rel="stylesheet" href="/assets/plugin/Bluelunbo/plugin/Bluelunbo/css/all.min.css">
        <style>
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
        }
        
        :root {
            --primary: #4169e1;
            --primary-light: #6a8bef;
            --primary-dark: #3a5ecc;
            --primary-bg: #f5f9ff;
            --secondary-bg: #f0f7ff;
            --card-bg: #f8faff;
            --text-primary: #333;
            --text-secondary: #666;
            --text-light: #999;
            --border-radius-sm: 8px;
            --border-radius: 15px;
            --border-radius-lg: 20px;
            --border-radius-xl: 30px;
            --shadow-sm: 0 4px 8px rgba(0, 0, 0, 0.05);
            --shadow: 0 10px 30px rgba(65, 105, 225, 0.1);
            --shadow-lg: 0 15px 40px rgba(65, 105, 225, 0.15);
            --transition: all 0.3s ease;
            
            /* 新增加的变量 */
            --gradient-primary: linear-gradient(135deg, var(--primary-light), var(--primary), var(--primary-dark));
            --gradient-overlay: linear-gradient(rgba(65, 105, 225, 0.05), rgba(65, 105, 225, 0.1));
            --glass-effect: rgba(255, 255, 255, 0.8);
            --box-shadow-hover: 0 15px 35px rgba(65, 105, 225, 0.2);
            --accent-color: #ff7b54;
            --success-color: #4CAF50;
            --warning-color: #FFC107;
            --error-color: #FF5252;
            
            /* 新增美化变量 */
            --card-hover-transform: translateY(-8px);
            --hover-transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
            --card-hover-shadow: 0 20px 40px rgba(65, 105, 225, 0.18);
            --subtle-bg-pattern: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%234169e1' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        
        body {
            background-color: var(--primary-bg);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden; /* 防止水平滚动条 */
            font-size: 16px;
            background-image: var(--gradient-overlay), var(--subtle-bg-pattern);
            background-attachment: fixed;
        }
        
        a {
            text-decoration: none;
            color: var(--text-primary);
            transition: var(--transition);
        }
        
        ul {
            list-style: none;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            position: relative;
            z-index: 1;
            text-align: center; /* 添加文本居中 */
        }
        
        /* 头部导航增强 */
        header {
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 15px 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            transition: all 0.3s ease;
        }
        
        /* 滚动后导航栏变小 */
        header.scrolled {
            padding: 10px 0;
            background-color: rgba(255, 255, 255, 0.98);
            box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
        }
        
        header.scrolled .logo img {
            height: 35px;
        }
        
        header.scrolled .logo-text {
            font-size: 20px;
        }
        
        header.scrolled .nav-links li a {
            font-size: 14px;
        }
        
        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
        }
        
        .logo img {
            height: 40px;
            margin-right: 10px;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
            transition: all 0.3s ease;
        }
        
        .logo-text {
            font-size: 22px;
            font-weight: bold;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }
        
        .nav-main {
            display: flex;
            align-items: center;
            margin-left: 20px; /* 添加左边距，使菜单更靠近logo */
        }
        
        .nav-links {
            display: flex;
            margin-right: 30px;
        }
        
        .nav-links li {
            display: inline-block;
            margin: 0 10px;
            position: relative;
        }
        
        .nav-links li a {
            color: var(--text-primary);
            font-weight: 500;
            transition: var(--transition);
            white-space: nowrap;
            position: relative;
            padding: 8px 0;
            display: inline-block;
        }
        
        .nav-links li a::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: 0;
            left: 0;
            background: var(--gradient-primary);
            border-radius: 2px;
            transition: width 0.3s ease;
        }
        
        .nav-links li a:hover::after {
            width: 100%;
        }
        
        /* 下拉箭头样式 */
        .dropdown-arrow {
            font-size: 8px; /* 箭头字体大小 */
            margin-left: 5px; /* 与文字的间距 */
            color: var(--primary); /* 箭头颜色 */
            display: inline-block;
            vertical-align: middle;
            transform: translateY(-1px); /* 微调箭头位置 */
        }
        
        .nav-links li.active a {
            color: var(--primary);
        }
        
        .nav-links li.active::after {
            content: "";
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: var(--primary);
        }
        
        /* 子菜单样式 */
        .submenu {
            position: absolute;
            top: 100%;
            left: 50%;  /* 改为50%使其可以水平居中 */
            width: 150px; /* 从200px减小到150px */
            background-color: #fff;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-radius: var(--border-radius-sm);
            padding: 10px 0;
            z-index: 100;
            opacity: 0;
            visibility: hidden;
            transform: translateX(-50%) translateY(10px); /* 添加translateX(-50%)实现水平居中 */
            transition: all 0.3s ease;
        }
        
        .nav-links li:hover .submenu {
            opacity: 1;
            visibility: visible;
            transform: translateX(-50%) translateY(0); /* 更新transform，保持水平居中 */
        }
        
        .submenu li {
            display: block;
            margin: 0;
            padding: 0;
            text-align: center; /* 添加文本居中对齐 */
        }
        
        .submenu li a {
            display: block;
            padding: 8px 15px;
            font-size: 14px;
            color: var(--text-secondary);
            text-align: center; /* 添加文本居中对齐 */
        }
        
        .submenu li a:hover {
            background-color: var(--primary-bg);
        }
        
        .auth-buttons {
            display: flex;
        }
        
        .btn {
            padding: 10px 24px;
            border-radius: 50px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
            outline: none;
            letter-spacing: 0.5px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
        }
        
        .btn::before {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: all 0.4s;
        }
        
        .btn:hover::before {
            left: 100%;
        }
        
        .btn-outline {
            border: 1.5px solid var(--primary);
            color: var(--primary);
            background-color: transparent;
            margin-right: 10px;
        }
        
        .btn-outline:hover {
            background-color: rgba(65, 105, 225, 0.05);
            transform: translateY(-2px);
            box-shadow: 0 6px 10px rgba(0, 0, 0, 0.08);
        }
        
        .btn-primary {
            background: var(--gradient-primary);
            background-size: 200% auto;
            color: white;
            display: flex;
            align-items: center;
        }
        
        .btn-primary:hover {
            background-position: right center;
            transform: translateY(-2px);
            box-shadow: 0 6px 10px rgba(65, 105, 225, 0.2);
        }
        
        /* 文章区域样式 */
        .article-section {
            padding: 30px 0;
            background-color: var(--primary-bg);
            position: relative;
            overflow: hidden; /* 防止装饰元素溢出 */
        }
        
        .article-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 200px;
            background: linear-gradient(180deg, rgba(65, 105, 225, 0.12), transparent);
            z-index: 0;
        }
        
        /* 添加额外的背景装饰 */
        .article-section::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 150px;
            background: linear-gradient(0deg, rgba(65, 105, 225, 0.08), transparent);
            z-index: 0;
        }
        
        /* 添加装饰圆形 */
        .article-section .article-container {
            position: relative;
            z-index: 2;
        }
        
        /* 添加背景装饰元素样式 */
        @keyframes float {
            0% { transform: translateY(0) rotate(0deg); }
            50% { transform: translateY(-15px) rotate(5deg); }
            100% { transform: translateY(0) rotate(0deg); }
        }
        
        @keyframes float-reverse {
            0% { transform: translateY(0) rotate(0deg); }
            50% { transform: translateY(15px) rotate(-5deg); }
            100% { transform: translateY(0) rotate(0deg); }
        }
        
        @keyframes pulse {
            0% { transform: scale(1); opacity: 0.5; }
            50% { transform: scale(1.05); opacity: 0.7; }
            100% { transform: scale(1); opacity: 0.5; }
        }
        
        /* 背景圆形装饰 */
        .bg-circle {
            position: absolute;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-light), var(--primary));
            opacity: 0.1;
            z-index: 1;
            pointer-events: none; /* 确保用户可以点击下方内容 */
            filter: blur(20px);
        }
        
        /* 背景装饰元素 */
        .bg-decoration {
            position: absolute;
            z-index: 1;
            pointer-events: none;
        }
        
        .bg-decoration.circle-1 {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-light), var(--primary));
            top: -50px;
            left: -50px;
            opacity: 0.1;
            filter: blur(30px);
            animation: pulse 15s infinite ease-in-out;
        }
        
        .bg-decoration.circle-2 {
            width: 300px;
            height: 300px;
            border-radius: 50%;
            background: linear-gradient(135deg, #8e9efc, var(--primary));
            bottom: 10%;
            right: -100px;
            opacity: 0.08;
            filter: blur(40px);
            animation: pulse 20s infinite ease-in-out reverse;
        }
        
        .bg-decoration.circle-3 {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary), #6a8bef);
            top: 40%;
            left: 10%;
            opacity: 0.07;
            filter: blur(25px);
            animation: float 25s infinite ease-in-out;
        }
        
        .bg-decoration.wave-1 {
            width: 100%;
            height: 100px;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z' opacity='.15' fill='%234169e1'%3E%3C/path%3E%3C/svg%3E");
            background-size: cover;
            position: absolute;
            bottom: 0;
            left: 0;
            opacity: 0.4;
            animation: float-reverse 15s infinite ease-in-out alternate;
        }
        
        .bg-decoration.wave-2 {
            width: 100%;
            height: 150px;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z' opacity='.1' fill='%234169e1'%3E%3C/path%3E%3C/svg%3E");
            background-size: cover;
            position: absolute;
            top: 0;
            left: 0;
            opacity: 0.3;
            animation: float 20s infinite ease-in-out alternate;
        }
        
        .article-container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            position: relative;
            z-index: 1;
        }
        
        .notice-item {
            background-color: #fff;
            border-radius: var(--border-radius-lg);
            padding: 30px;
            margin-bottom: 40px;
            box-shadow: var(--shadow-sm);
            transition: var(--hover-transition);
            animation: fadeIn 0.5s ease forwards;
            position: relative;
            border: 1px solid rgba(65, 105, 225, 0.08);
            overflow: hidden;
        }
        
        .notice-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: var(--gradient-primary);
            transition: var(--transition);
            transform: scaleX(0.7);
            transform-origin: left;
            opacity: 0.7;
        }
        
        .notice-item:hover {
            box-shadow: var(--card-hover-shadow);
            transform: var(--card-hover-transform);
            border-color: rgba(65, 105, 225, 0.15);
        }
        
        .notice-item:hover::before {
            transform: scaleX(1);
            opacity: 1;
        }
        
        .notice-title {
            font-size: 26px;
            color: var(--text-primary);
            margin-bottom: 20px;
            font-weight: 600;
            position: relative;
            padding-bottom: 12px;
            letter-spacing: 0.3px;
        }
        
        .notice-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 3px;
            background: var(--gradient-primary);
            border-radius: 3px;
            transition: width 0.3s ease;
        }
        
        .notice-item:hover .notice-title::after {
            width: 100px;
        }
        
        .notice-content {
            color: var(--text-secondary);
            font-size: 16px;
            line-height: 1.8;
            margin-bottom: 25px;
            text-align: justify;
            position: relative;
        }
        
        /* 添加四字一行的排版效果 */
        .notice-content p {
            display: inline;
            word-break: break-all;
            word-wrap: break-word;
            margin-bottom: 1em;
        }
        
        .notice-content p::after {
            content: "";
            display: block;
            margin-bottom: 12px;
        }
        
        /* 汉字四字一行处理 */
        .notice-content .char-group {
            display: inline-block;
            width: 4em;
            text-align: justify;
            text-align-last: justify;
            margin-right: 1.2em;
            line-height: 1.9;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .notice-content .char-group:hover {
            color: var(--primary);
            transform: translateY(-2px);
            text-shadow: 0 2px 4px rgba(65, 105, 225, 0.2);
        }
        
        .notice-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: var(--text-light);
            font-size: 14px;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            padding-top: 18px;
            margin-top: 5px;
        }
        
        .notice-time {
            display: flex;
            align-items: center;
            transition: var(--transition);
        }
        
        .notice-time:hover, .view-count:hover {
            color: var(--primary);
            transform: translateY(-2px);
        }
        
        .notice-time i, .view-count i {
            margin-right: 8px;
            color: var(--primary);
            transition: transform 0.3s ease;
        }
        
        .notice-time:hover i, .view-count:hover i {
            transform: scale(1.2) rotate(10deg);
        }
        
        .view-count {
            display: flex;
            align-items: center;
            transition: var(--transition);
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* 添加页面滚动动画 */
        @keyframes floatUp {
            0% {
                opacity: 0;
                transform: translateY(20px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* 页脚样式 */
        .footer {
            padding-top: 50px;
            padding-bottom: 20px;
            background: linear-gradient(to bottom, #2c3e50, #1a252f);
            color: #fff;
            position: relative;
            box-shadow: 0 -5px 15px rgba(0, 0, 0, 0.05);
        }
        
        .footer::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5z-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.03' fill-rule='evenodd'/%3E%3C/svg%3E");
            opacity: 0.3;
        }
        
        .footer-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            text-align: left;
            gap: 40px;
            position: relative;
            z-index: 1;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .footer-column {
            flex: 1;
            min-width: 230px;
            transition: all 0.3s ease;
        }
        
        .footer-column:hover {
            transform: translateY(-5px);
        }
        
        .footer-logo {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .footer-logo img {
            height: 42px;
            margin-right: 12px;
            filter: brightness(0) invert(1);
            transition: transform 0.3s ease;
        }
        
        .footer-logo:hover img {
            transform: scale(1.1);
        }
        
        .footer-description {
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.6;
            margin-bottom: 22px;
            font-size: 14px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }
        
        .footer-social {
            display: flex;
            gap: 12px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .footer-social-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 38px;
            height: 38px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transition: all 0.3s ease;
            font-size: 16px;
            backdrop-filter: blur(3px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .footer-social-icon:hover {
            background: var(--primary);
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            color: white;
        }
        
        .footer-social-icon i {
            transition: transform 0.3s ease;
        }
        
        .footer-social-icon:hover i {
            transform: scale(1.15);
        }
        
        .footer-title {
            color: white;
            font-size: 19px;
            font-weight: 600;
            margin-bottom: 18px;
            position: relative;
            padding-bottom: 10px;
            letter-spacing: 0.2px;
        }
        
        .footer-title::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-light), var(--primary));
            border-radius: 2px;
            transition: width 0.3s ease;
        }
        
        .footer-column:hover .footer-title::after {
            width: 70px;
        }
        
        .footer-links {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        
        .footer-links a {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
            position: relative;
            transition: all 0.3s ease;
            padding-left: 0;
            display: inline-block;
        }
        
        .footer-links a::before {
            content: "›";
            position: absolute;
            left: -15px;
            opacity: 0;
            transition: all 0.3s ease;
        }
        
        .footer-links a:hover {
            color: white;
            padding-left: 5px;
            transform: translateX(3px);
        }
        
        .footer-links a:hover::before {
            opacity: 1;
            left: -10px;
        }
        
        .footer-contact {
            display: flex;
            align-items: flex-start;
            margin-bottom: 14px;
            transition: all 0.3s ease;
        }
        
        .footer-contact:hover {
            transform: translateX(3px);
        }
        
        .footer-contact-icon {
            width: 24px;
            margin-right: 12px;
            margin-top: 3px;
            color: var(--primary-light);
            transition: transform 0.3s ease;
        }
        
        .footer-contact:hover .footer-contact-icon {
            transform: scale(1.2);
            color: white;
        }
        
        .footer-contact-text {
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.5;
            font-size: 14px;
            flex: 1;
            transition: color 0.3s ease;
        }
        
        .footer-contact:hover .footer-contact-text {
            color: white;
        }
        
        .footer-bottom {
            text-align: center;
            margin-top: 50px;
            padding-top: 25px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            z-index: 1;
        }
        
        .footer-bottom::before {
            content: "";
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-light), var(--primary));
            border-radius: 3px;
        }
        
        .footer-bottom p {
            color: rgba(255, 255, 255, 0.6);
            font-size: 13px;
        }
        
        /* 版权区域样式 */
        .copyright {
            width: 100%;
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .copyright-content {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
            gap: 30px;
        }
        
        .copyright-text {
            color: rgba(255, 255, 255, 0.6);
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 7px;
            transition: color 0.3s ease;
        }
        
        .copyright-text:hover {
            color: white;
        }
        
        .copyright-text i {
            color: var(--primary-light);
            font-size: 16px;
            transition: transform 0.3s ease;
        }
        
        .copyright-text:hover i {
            transform: rotate(360deg);
        }
        
        .beian-info {
            display: flex;
            align-items: center;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .beian-link {
            color: rgba(255, 255, 255, 0.6);
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 7px;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .beian-link::after {
            content: "";
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 0;
            height: 1px;
            background-color: white;
            transition: width 0.3s ease;
        }
        
        .beian-link:hover {
            color: #fff;
        }
        
        .beian-link:hover::after {
            width: 100%;
        }
        
        .beian-link i {
            font-size: 16px;
            color: var(--primary-light);
            transition: transform 0.3s ease;
        }
        
        .beian-link:hover i {
            transform: scale(1.2);
        }
        
        /* 响应式设计 */
        @media (max-width: 991px) {
            .footer-container {
                gap: 30px;
            }
            
            .footer-column {
                min-width: 45%;
            }
        }
        
        @media (max-width: 768px) {
            .footer {
                padding-top: 40px;
            }
            
            .footer-column {
                min-width: 100%;
                margin-bottom: 10px;
            }
            
            .footer-column:hover {
                transform: none;
            }
            
            .footer-bottom {
                margin-top: 30px;
            }
            
            .footer-social {
                justify-content: center;
            }
            
            .footer-title {
                text-align: center;
                margin-top: 15px;
                margin-bottom: 15px;
            }
            
            .footer-title::after {
                left: 50%;
                transform: translateX(-50%);
            }
            
            .footer-links {
                align-items: center;
            }
            
            .footer-links a:hover {
                transform: none;
            }
            
            .footer-contact {
                justify-content: center;
                margin-bottom: 10px;
            }
            
            .footer-contact:hover {
                transform: none;
            }
            
            .footer-contact-text {
                max-width: 70%;
            }
            
            .copyright-content {
                flex-direction: column;
                gap: 15px;
            }
            
            .beian-info {
                justify-content: center;
                gap: 12px;
            }
            
            .copyright-text:hover i {
                transform: none;
            }
            
            .beian-link:hover i {
                transform: none;
            }
        }
        </style>
    </head>
    <body>
        <header>
            <div class="container header-container">
                <div class="logo">
                    <img src="{$logo}" alt="{$siteName}Logo">
                </div>
                <div class="nav-main">
                    <ul class="nav-links">
                        {foreach $navItems as $nav}
                        <li class="{if (isset($currentPage) && $currentPage == 'prohibit_directory') || (strpos($nav.name, '禁售目录') !== false)}active{/if}">
                            <a href="{$nav.href}" {if $nav.target=='_blank'}target="_blank"{/if}>
                                {$nav.name}
                                {if !empty($nav.children)}
                                <span class="dropdown-arrow">▼</span>
                                {/if}
                            </a>
                            {if !empty($nav.children)}
                            <ul class="submenu">
                                {foreach $nav.children as $child}
                                <li><a href="{$child.href}" {if $child.target=='_blank'}target="_blank"{/if}>{$child.name}</a></li>
                                {/foreach}
                            </ul>
                            {/if}
                        </li>
                        {/foreach}
                    </ul>
                    <div class="auth-buttons">
                        <a href="/merchant/login" class="btn btn-outline"><i class="fas fa-user"></i> 商家登录</a>
                        <a href="/merchant/register" class="btn btn-primary"><i class="fas fa-user-plus"></i>&nbsp;&nbsp;商家注册</a>
                    </div>
                </div>
            </div>
        </header>
        
        <main class="article-section">
            <!-- 添加背景装饰元素 -->
            <div class="bg-decoration circle-1"></div>
            <div class="bg-decoration circle-2"></div>
            <div class="bg-decoration circle-3"></div>
            <div class="bg-decoration wave-1"></div>
            <div class="bg-decoration wave-2"></div>
            
            <div class="article-container">
                {foreach $notice as $article}
                    <article class="notice-item" data-article-id="{$article.id}">
                        <h2 class="notice-title">{$article.title}</h2>
                        <div class="notice-content">
                            {$article.content|raw}
                        </div>
                        <div class="notice-meta">
                            <span class="notice-time">
                                <i class="far fa-clock"></i> {$article.create_at}
                            </span>
                            <span class="view-count">
                                <i class="far fa-eye"></i> {$article.views ?? '0'} 次浏览
                            </span>
                        </div>
                    </article>
                {/foreach}
            </div>
        </main>

        <footer class="footer">
            <div class="footer-container">
                <div class="footer-column animate slide-up">
                    <div class="footer-logo">
                        <img src="{$logo}" alt="{$siteName}Logo">
                        <div class="footer-logo-text">{$siteName}</div>
                    </div>
                    <p class="footer-description">
                        {$footer_description}
                    </p>
                    <div class="footer-social">
                        <a href="javascript:;" class="footer-social-icon" title="手机"><i class="fas fa-mobile-alt"></i></a>
                        <a href="javascript:;" class="footer-social-icon" title="消息"><i class="fas fa-bell"></i></a>
                        <a href="javascript:;" class="footer-social-icon" title="邮箱"><i class="fas fa-envelope"></i></a>
                        <a href="javascript:;" class="footer-social-icon" title="微信"><i class="fab fa-weixin"></i></a>
                    </div>
                </div>
                
                {if $footer_service_show}
                <div class="footer-column animate slide-up delay-200">
                    <h3 class="footer-title">{$footer_quicklinks_title|default='服务中心'}</h3>
                    <div class="footer-links">
                        <a href="{$footer_service_1_link}">{$footer_service_1}</a>
                        <a href="{$footer_service_2_link}">{$footer_service_2}</a>
                        <a href="{$footer_service_3_link}">{$footer_service_3}</a>
                        <a href="{$footer_service_4_link}">{$footer_service_4}</a>
                    </div>
                </div>
                {/if}
                
                {if $footer_help_show}
                <div class="footer-column animate slide-up delay-300">
                    <h3 class="footer-title">{$footer_products_title|default='帮助中心'}</h3>
                    <div class="footer-links">
                        <a href="{$footer_help_1_link}">{$footer_help_1}</a>
                        <a href="{$footer_help_2_link}">{$footer_help_2}</a>
                        <a href="{$footer_help_3_link}">{$footer_help_3}</a>
                        <a href="{$footer_help_4_link}">{$footer_help_4}</a>
                    </div>
                </div>
                {/if}
                
                <div class="footer-column animate slide-up delay-400">
                    <h3 class="footer-title">{$footer_contact_title|default='联系我们'}</h3>
                    <div class="footer-contact">
                        <div class="footer-contact-icon"><i class="fas fa-map-marker-alt"></i></div>
                        <div class="footer-contact-text">{$footer_contact_address}</div>
                    </div>
                    <div class="footer-contact">
                        <div class="footer-contact-icon"><i class="fas fa-phone-alt"></i></div>
                        <div class="footer-contact-text">{$footer_contact_phone}</div>
                    </div>
                    <div class="footer-contact">
                        <div class="footer-contact-icon"><i class="fas fa-envelope"></i></div>
                        <div class="footer-contact-text">{$footer_contact_email}</div>
                    </div>
                    <div class="footer-contact">
                        <div class="footer-contact-icon"><i class="fas fa-clock"></i></div>
                        <div class="footer-contact-text">{$footer_contact_hours}</div>
                    </div>
                    <div class="footer-contact">
                        <div class="footer-contact-icon"><i class="fab fa-qq"></i></div>
                        <div class="footer-contact-text">QQ: {$contact_qq}</div>
                    </div>
                    <div class="footer-contact">
                        <div class="footer-contact-icon"><i class="fab fa-weixin"></i></div>
                        <div class="footer-contact-text">微信: {$contact_wx}</div>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <div class="copyright">
                    <div class="copyright-content">
                        <span class="copyright-text">
                            <i class="far fa-copyright"></i>
                            {$siteName} - 版权所有 © {$year}
                        </span>
                        <div class="beian-info">
                            {if !empty($icpNumber)}
                            <a href="https://beian.miit.gov.cn/" target="_blank" class="beian-link">
                                <i class="fas fa-shield-alt"></i>
                                <span>{$icpNumber}</span>
                            </a>
                            {/if}
                            {if !empty($gaNumber)}
                            <a href="{$icpCert}" target="_blank" class="beian-link">
                                <i class="fas fa-badge-check"></i>
                                <span>{$gaNumber}</span>
                            </a>
                            {/if}
                        </div>
                    </div>
                </div>
            </div>
        </footer>

        <script>
            // 页面加载动画
            document.addEventListener('DOMContentLoaded', function() {
                document.body.classList.add('loaded');
                
                // 直接在页面加载时添加scrolled类，使其始终保持图一中的样式
                const header = document.querySelector('header');
                header.classList.add('scrolled');
                
                // 删除滚动监听器，防止导航栏样式变化
                // 背景切换功能
                const body = document.body;
                
                // 创建背景装饰元素
                function createBackgroundDecoration() {
                    const articleSection = document.querySelector('.article-section');
                    
                    // 创建浮动圆
                    for (let i = 0; i < 6; i++) {
                        const circle = document.createElement('div');
                        circle.classList.add('bg-circle');
                        
                        // 随机大小和位置
                        const size = Math.random() * 150 + 50;
                        circle.style.width = `${size}px`;
                        circle.style.height = `${size}px`;
                        circle.style.left = `${Math.random() * 100}%`;
                        circle.style.top = `${Math.random() * 100}%`;
                        circle.style.opacity = (Math.random() * 0.15).toFixed(2);
                        
                        // 随机动画
                        const animationDuration = (Math.random() * 15 + 15).toFixed(1);
                        const animation = Math.random() > 0.5 ? 'float' : 'float-reverse';
                        circle.style.animation = `${animation} ${animationDuration}s infinite ease-in-out`;
                        
                        // 随机延迟
                        circle.style.animationDelay = `${Math.random() * 5}s`;
                        
                        articleSection.appendChild(circle);
                    }
                }
                
                // 调用创建背景装饰元素
                createBackgroundDecoration();
                
                // 检查bgToggle元素是否存在，避免null引用错误
                const bgToggle = document.querySelector('.bg-toggle');
                if (bgToggle) {
                    bgToggle.addEventListener('click', function() {
                        body.classList.toggle('no-bg');
                        const icon = bgToggle.querySelector('i');
                        
                        if (body.classList.contains('no-bg')) {
                            icon.classList.remove('fa-eye-slash');
                            icon.classList.add('fa-eye');
                        } else {
                            icon.classList.remove('fa-eye');
                            icon.classList.add('fa-eye-slash');
                        }
                    });
                }
                
                // 给文章添加动画效果
                const articles = document.querySelectorAll('.notice-item');
                articles.forEach((article, index) => {
                    article.style.animationDelay = `${index * 0.2}s`;
                });
                
                // 文本四字一行处理
                const noticeContents = document.querySelectorAll('.notice-content');
                noticeContents.forEach(content => {
                    // 保留原始HTML，处理后将重新插入
                    const originalHtml = content.innerHTML;
                    
                    // 创建临时元素以获取纯文本
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = originalHtml;
                    
                    // 获取所有文本节点并进行处理
                    const textNodes = getAllTextNodes(tempDiv);
                    
                    // 处理每个文本节点
                    textNodes.forEach(node => {
                        const text = node.nodeValue.trim();
                        if (text.length > 0) {
                            // 每四个字符一组
                            const groups = [];
                            for (let i = 0; i < text.length; i += 4) {
                                const group = text.substr(i, 4);
                                if (group.trim()) {
                                    groups.push(`<span class="char-group">${group}</span>`);
                                }
                            }
                            
                            // 创建一个包含分组的新元素
                            const newNode = document.createElement('span');
                            newNode.innerHTML = groups.join('');
                            
                            // 替换原始节点
                            if (node.parentNode) {
                                node.parentNode.replaceChild(newNode, node);
                            }
                        }
                    });
                });
                
                // 获取所有文本节点的辅助函数
                function getAllTextNodes(element) {
                    const textNodes = [];
                    
                    function getTextNodes(node) {
                        if (node.nodeType === 3) { // 文本节点
                            textNodes.push(node);
                        } else if (node.nodeType === 1) { // 元素节点
                            for (let i = 0; i < node.childNodes.length; i++) {
                                getTextNodes(node.childNodes[i]);
                            }
                        }
                    }
                    
                    getTextNodes(element);
                    return textNodes;
                }
            });
        </script>
    </body>
</html>