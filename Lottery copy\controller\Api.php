<?php

namespace plugin\Lottery\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Log;
use think\facade\Db;

class Api extends BasePlugin {

    protected $scene = ['admin'];  // 只允许管理员访问
    
    // 管理面板首页
    public function index() {
        return View::fetch();
    }
    
    // 获取抽奖配置
    public function getLotteryConfig() {
        try {
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            // 确保返回必要的配置项
            if (!isset($config['prize_types'])) {
                $config['prize_types'] = [];
            }
            
            if (!isset($config['hidden_prizes'])) {
                $config['hidden_prizes'] = [];
            }
            
            if (!isset($config['lottery_config'])) {
                $config['lottery_config'] = [
                    'status' => 1,
                    'daily_limit' => 3,
                    'start_hour' => '00:00',
                    'end_hour' => '23:59',
                    'show_probability' => 0
                ];
            }

            return json([
                'code' => 200, 
                'msg' => '获取成功', 
                'data' => [
                    'config' => $config['lottery_config'],
                    'prizeTypes' => $config['prize_types'],
                    'hiddenPrizes' => $config['hidden_prizes']
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取抽奖配置失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取配置失败：' . $e->getMessage()]);
        }
    }

    // 保存抽奖配置
    public function saveLotteryConfig() {
        try {
            $data = [
                'status' => input('status/d', 1),
                'daily_limit' => max(0, input('daily_limit/d', 3)),
                'start_hour' => input('start_hour/s', '00:00'),
                'end_hour' => input('end_hour/s', '23:59'),
                'show_probability' => input('show_probability/d', 0)
            ];

            // 验证每日抽奖次数
            if ($data['daily_limit'] < 0 || $data['daily_limit'] > 100) {
                return json(['code' => 400, 'msg' => '每日抽奖次数必须在1-100之间']);
            }

            // 读取配置文件
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            // 更新基础配置
            $config['lottery_config'] = $data;
            
            // 同步更新所有商户的每日限制
            if (isset($config['merchant_limits']) && is_array($config['merchant_limits'])) {
                foreach ($config['merchant_limits'] as &$merchantLimit) {
                    // 只更新当天的限制
                    if ($merchantLimit['last_reset_date'] === date('Y-m-d')) {
                        $merchantLimit['daily_limit'] = $data['daily_limit'];
                    }
                }
            }
            
            // 保存配置文件
            if (!file_put_contents($configFile, "<?php\nreturn " . var_export($config, true) . ";\n")) {
                throw new \Exception('保存配置失败');
            }

            return json(['code' => 200, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            Log::error('保存抽奖配置失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    // 获取奖品列表
    public function getPrizes() {
        try {
            // 读取配置文件
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            $prizes = isset($config['prizes']) ? $config['prizes'] : [];
            
            // 格式化返回数据
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => [
                    'list' => array_values($prizes),
                    'total' => count($prizes)
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取奖品列表失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    // 添加/编辑奖品
    public function savePrize() {
        try {
            $data = [
                'id' => input('id/d', 0),
                'name' => input('name/s', ''),
                'type' => input('type/s', ''),
                'probability' => input('probability/f', 0),
                'stock' => input('stock/d', 0),
                'status' => input('status/d', 0), // 修改这里，确保接收布尔值转换为0/1
                'balance_amount' => input('balance_amount/f', 0),
                'update_time' => time()
            ];
            
            // 验证数据
            if (empty($data['name'])) {
                return json(['code' => 400, 'msg' => '奖品名称不能为空']);
            }
            
            if (empty($data['type'])) {
                return json(['code' => 400, 'msg' => '奖品类型不能为空']);
            }
            
            if ($data['probability'] <= 0 || $data['probability'] > 100) {
                return json(['code' => 400, 'msg' => '概率必须在0-100之间']);
            }

            // 验证余额金额不能小于0
            if ($data['balance_amount'] < 0) {
                return json(['code' => 400, 'msg' => '余额金额不能小于0']);
            }
            
            // 确保状态值为 0 或 1
            $data['status'] = $data['status'] ? 1 : 0;
            
            // 读取配置文件
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            if (!isset($config['prizes'])) {
                $config['prizes'] = [];
            }

            // 如果是编辑现有奖品
            if ($data['id'] > 0) {
                // 检查奖品是否存在
                $found = false;
                foreach ($config['prizes'] as $key => $prize) {
                    if ($prize['id'] == $data['id']) {
                        $config['prizes'][$key] = array_merge($prize, $data);
                        $found = true;
                        break;
                    }
                }
                if (!$found) {
                    return json(['code' => 404, 'msg' => '奖品不存在']);
                }
            } else {
                // 添加新奖品
                $maxId = 0;
                foreach ($config['prizes'] as $prize) {
                    $maxId = max($maxId, intval($prize['id']));
                }
                $data['id'] = $maxId + 1;
                $data['create_time'] = date('Y-m-d H:i:s');
                $config['prizes'][] = $data;
            }
            
            // 保存配置
            if (!file_put_contents($configFile, "<?php\nreturn " . var_export($config, true) . ";\n")) {
                throw new \Exception('保存配置失败');
            }
            
            return json(['code' => 200, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            Log::error('保存奖品失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    // 删除奖品
    public function deletePrize() {
        try {
            $id = input('id/d', 0);
            if (empty($id)) {
                return json(['code' => 400, 'msg' => '奖品ID不能为空']);
            }

            // 读取配置文件
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            // 获取现有奖品列表
            $prizes = isset($config['prizes']) ? $config['prizes'] : [];
            
            // 查找要删除的奖品
            $found = false;
            $newPrizes = [];
            foreach ($prizes as $prize) {
                if ($prize['id'] == $id) {
                    $found = true;
                    continue;
                }
                $newPrizes[] = $prize;
            }
            
            if (!$found) {
                return json(['code' => 404, 'msg' => '奖品不存在']);
            }
            
            // 更新配置文件
            $config['prizes'] = $newPrizes;
            if (!file_put_contents($configFile, "<?php\nreturn " . var_export($config, true) . ";\n")) {
                throw new \Exception('保存配置失败');
            }
            
            return json(['code' => 200, 'msg' => '删除成功']);
        } catch (\Exception $e) {
            Log::error('删除奖品失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }

    // 更新奖品状态
    public function updatePrizeStatus() {
        try {
            $id = input('id/d', 0);
            $status = input('status/d', 0);

            // 读取配置
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = include $configFile;

            // 更新状态
            $updated = false;
            foreach ($config['prizes'] as &$prize) {
                if ($prize['id'] == $id) {
                    $prize['status'] = $status;
                    $prize['update_time'] = time();
                    $updated = true;
                    break;
                }
            }

            if (!$updated) {
                return json(['code' => 404, 'msg' => '奖品不存在']);
            }

            // 保存配置
            file_put_contents($configFile, "<?php\n\nreturn " . var_export($config, true) . ";\n");

            return json(['code' => 200, 'msg' => '更新成功']);
        } catch (\Exception $e) {
            Log::error('更新奖品状态失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '更新失败：' . $e->getMessage()]);
        }
    }

    // 获取抽奖记录
    public function getLotteryRecords() {
        try {
            $page = input('page/d', 1);
            $pageSize = input('pageSize/d', 20);
            $searchMerchantId = input('merchantId/s', '');
            $searchPrizeName = input('prizeName/s', '');
            $searchPrizeType = input('prizeType/s', '');
            
            // 读取配置文件
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            // 获取所有记录
            $records = isset($config['lottery_records']) ? $config['lottery_records'] : [];
            
            // 获取奖品类型列表
            $prizeTypes = isset($config['prize_types']) ? $config['prize_types'] : [];
            
            // 获取隐藏奖品配置
            $hiddenPrizes = isset($config['hidden_prizes']) ? $config['hidden_prizes'] : [];
            
            // 修改过滤逻辑，只对真实用户记录进行隐藏处理
            $filteredRecords = array_filter($records, function($record) use ($searchMerchantId, $searchPrizeName, $searchPrizeType, $hiddenPrizes) {
                // 检查是否为虚拟用户记录（通过商户ID是否为字符串类型判断）
                $isVirtualRecord = is_string($record['merchant_id']) && strpos($record['merchant_id'], '商家') === 0;
                
                // 如果是真实用户且奖品在隐藏列表中，则不显示
                if (!$isVirtualRecord && in_array($record['prize_id'], $hiddenPrizes)) {
                    return false;
                }
                
                // 应用其他搜索条件
                if ($searchMerchantId && $record['merchant_id'] != $searchMerchantId) {
                    return false;
                }
                if ($searchPrizeName && strpos($record['prize_name'], $searchPrizeName) === false) {
                    return false;
                }
                if ($searchPrizeType && $record['prize_type'] != $searchPrizeType) {
                    return false;
                }
                
                return true;
            });
            
            // 按时间倒序排序
            usort($filteredRecords, function($a, $b) {
                return strtotime($b['create_time']) - strtotime($a['create_time']);
            });
            
            // 获取所有商户ID
            $merchantIds = array_unique(array_column($filteredRecords, 'merchant_id'));
            
            // 从user表获取商户信息
            $merchantInfo = [];
            if (!empty($merchantIds)) {
                $users = Db::name('user')
                    ->whereIn('id', $merchantIds)
                    ->field('id, nickname')
                    ->select()
                    ->toArray();
                
                foreach ($users as $user) {
                    $merchantInfo[$user['id']] = $user['nickname'];
                }
            }
            
            // 计算分页
            $total = count($filteredRecords);
            $offset = ($page - 1) * $pageSize;
            $records = array_slice($filteredRecords, $offset, $pageSize);
            
            // 处理每条记录
            foreach ($records as &$record) {
                // 设置商户名称
                $record['merchant_name'] = isset($merchantInfo[$record['merchant_id']]) 
                    ? $merchantInfo[$record['merchant_id']] 
                    : '未知商户';
                    
                // 确保 prize_type 字段存在
                if (!isset($record['prize_type'])) {
                    // 从奖品配置中获取奖品类型
                    $prizeId = $record['prize_id'];
                    $record['prize_type'] = isset($config['prizes'][$prizeId]) ? 
                        $config['prizes'][$prizeId]['type'] : 
                        'virtual';
                }
                
                // 设置奖品类型文本和样式
                $record['prize_type_text'] = $this->getPrizeTypeText($record['prize_type']);
                $typeStyle = $this->getPrizeTypeStyle($record['prize_type']);
                
                // 添加奖品类型样式信息
                $record['prize_type_style'] = [
                    'type' => $typeStyle['type'] ?? 'info',
                    'color' => isset($typeStyle['style']) ? 
                        preg_replace('/background-color:\s*/', '', $typeStyle['style']) : 
                        null
                ];
                
                // 检查是否是自动发放的
                $isAutoSent = false;
                if (!empty($record['balance_sent'])) {
                    // 检查余额变动记录，只根据用户ID和原因判断
                    $moneyLog = Db::name('user_money_log')
                        ->where([
                            'user_id' => $record['merchant_id'],
                            'reason' => '抽奖奖品余额自动发放'
                        ])
                        ->find();
                    $isAutoSent = !empty($moneyLog);
                }
                
                // 添加自动发放标记
                $record['auto_sent'] = $isAutoSent;
                
                // 确保发货相关字段存在
                $record = array_merge([
                    'id' => $record['id'] ?? '',
                    'merchant_id' => $record['merchant_id'] ?? '',
                    'prize_name' => $record['prize_name'] ?? '',
                    'prize_type' => $record['prize_type'] ?? 'virtual',
                    'create_time' => $record['create_time'] ?? '',
                    'shipped' => $record['shipped'] ?? false,
                    'ship_time' => $record['ship_time'] ?? '',
                    'balance_sent' => $record['balance_sent'] ?? false,
                    'auto_sent' => $record['auto_sent'] ?? false
                ], $record);
                
                // 添加是否可发货的标志
                $record['can_ship'] = !$record['shipped'];
            }
            
            // 处理记录数据，确保包含图片信息
            $records = array_map(function($record) {
                // 如果记录中没有图片信息，可以从奖品配置中获取
                if (empty($record['prize_image']) && !empty($record['prize_id'])) {
                    $prize = $this->getPrizeById($record['prize_id']);
                    $record['prize_image'] = $prize['image'] ?? '';
                }
                return $record;
            }, $records);
            
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => [
                    'records' => $records,
                    'pagination' => [
                        'total' => $total,
                        'current' => $page,
                        'pageSize' => $pageSize,
                        'pages' => ceil($total / $pageSize)  // 添加总页数
                    ],
                    'prizeTypes' => $prizeTypes
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取抽奖记录失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取记录失败：' . $e->getMessage()]);
        }
    }

    // 根据奖品ID获取奖品信息
    private function getPrizeById($prizeId) {
        $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
        $config = file_exists($configFile) ? include $configFile : [];
        $prizes = isset($config['prizes']) ? $config['prizes'] : [];
        
        foreach ($prizes as $prize) {
            if ($prize['id'] == $prizeId) {
                return $prize;
            }
        }
        
        return null;
    }

    // 更新奖品
    public function updatePrize() {
        try {
            $id = input('id/d', 0);
            $data = [
                'name' => input('name/s', ''),
                'type' => input('type/s', ''),
                'probability' => input('probability/f', 0),
                'stock' => input('stock/d', 0),
                'status' => input('status/d', 1)
            ];
            
            // 验证数据
            if (empty($data['name'])) {
                return json(['code' => 400, 'msg' => '奖品名称不能为空']);
            }
            
            if (empty($data['type'])) {
                return json(['code' => 400, 'msg' => '奖品类型不能为空']);
            }
            
            if ($data['probability'] <= 0 || $data['probability'] > 100) {
                return json(['code' => 400, 'msg' => '概率必须在0-100之间']);
            }
            
            // 读取配置文件
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = include $configFile;
            
            // 更新奖品信息
            if (isset($config['prizes'][$id])) {
                $config['prizes'][$id] = array_merge($config['prizes'][$id], $data);
                $config['prizes'][$id]['update_time'] = time();
                
                if (!file_put_contents($configFile, "<?php\nreturn " . var_export($config, true) . ";\n")) {
                    throw new \Exception('保存配置失败');
                }
                
                return json(['code' => 200, 'msg' => '更新成功']);
            }
            
            return json(['code' => 404, 'msg' => '奖品不存在']);
        } catch (\Exception $e) {
            Log::error('更新奖品失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '更新失败：' . $e->getMessage()]);
        }
    }

    // 保存系统配置
    public function saveConfig() {
        try {
            $config = [
                'status' => input('status/d', 1),
                'max_draws_per_day' => input('max_draws_per_day/d', 3),
                'start_time' => input('start_time/s', '00:00'),
                'end_time' => input('end_time/s', '23:59')
            ];

            // 验证时间格式
            if (!preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $config['start_time']) ||
                !preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $config['end_time'])) {
                return json(['code' => 400, 'msg' => '时间格式不正确']);
            }

            // 保存配置
            set_plugin_config('Lottery', $config);

            return json(['code' => 0, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            Log::error('保存配置失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    // 添加/编辑通知
    public function saveNotification() {
        try {
            $id = input('id/d', 0);
            $data = [
                'title' => input('title/s', ''),
                'content' => input('content/s', ''),
                'status' => input('status/d', 1),
                'update_time' => time()
            ];

            if (empty($data['title']) || empty($data['content'])) {
                return json(['code' => 400, 'msg' => '标题和内容不能为空']);
            }

            if ($id) {
                Db::name('lottery_notifications')
                    ->where('id', $id)
                    ->update($data);
            } else {
                $data['create_time'] = time();
                Db::name('lottery_notifications')->insert($data);
            }

            return json(['code' => 0, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            Log::error('保存通知失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    // 删除通知
    public function deleteNotification() {
        try {
            $id = input('id/d', 0);
            if (!$id) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }

            Db::name('lottery_notifications')->where('id', $id)->delete();
            
            return json(['code' => 0, 'msg' => '删除成功']);
        } catch (\Exception $e) {
            Log::error('删除通知失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }

    // 获取通知列表
    public function getNotifications() {
        try {
            $page = input('page/d', 1);
            $limit = input('limit/d', 10);
            
            $notifications = Db::name('lottery_notifications')
                ->page($page, $limit)
                ->order('create_time', 'desc')
                ->select()
                ->toArray();
                
            $total = Db::name('lottery_notifications')->count();

            return json([
                'code' => 0,
                'msg' => '获取成功',
                'data' => [
                    'list' => $notifications,
                    'total' => $total
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取通知列表失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取通知失败：' . $e->getMessage()]);
        }
    }

    // 修改保存奖品类型方法
    public function savePrizeType() {
        try {
            $data = [
                'label' => input('label/s', ''),
                'value' => input('value/s', ''),
                'tagType' => input('tagType/s', ''),
                'tagColor' => input('tagColor/s', ''),
                'is_system' => input('is_system/d', 0)
            ];
            $isEdit = input('isEdit/b', false);

            if (empty($data['label']) || empty($data['value'])) {
                return json(['code' => 400, 'msg' => '类型名称和标识不能为空']);
            }

            // 读取配置文件
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = file_exists($configFile) ? include $configFile : [];

            if (!isset($config['prize_types'])) {
                $config['prize_types'] = [];
            }

            // 检查是否存在相同标识
            $existingIndex = -1;
            foreach ($config['prize_types'] as $index => $type) {
                if ($type['value'] === $data['value']) {
                    $existingIndex = $index;
                    break;
                }
            }

            // 如果是编辑现有类型
            if ($isEdit) {
                if ($existingIndex === -1) {
                    return json(['code' => 404, 'msg' => '类型不存在']);
                }

                // 保留原有的 is_system 值
                $data['is_system'] = $config['prize_types'][$existingIndex]['is_system'];
                
                // 如果是系统类型，只允许修改标签名称
                if ($data['is_system']) {
                    $config['prize_types'][$existingIndex]['label'] = $data['label'];
                } else {
                    // 非系统类型可以修改所有属性
                    $config['prize_types'][$existingIndex] = $data;
                }
            } else {
                // 添加新类型
                if ($existingIndex !== -1) {
                    return json(['code' => 400, 'msg' => '已存在相同标识的类型']);
                }
                $config['prize_types'][] = $data;
            }

            // 保存配置
            if (!file_put_contents($configFile, "<?php\nreturn " . var_export($config, true) . ";\n")) {
                throw new \Exception('保存配置失败');
            }

            // 返回更新后的类型列表
            return json([
                'code' => 200, 
                'msg' => $isEdit ? '修改成功' : '添加成功',
                'data' => $config['prize_types']
            ]);
        } catch (\Exception $e) {
            Log::error('保存奖品类型失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    // 修改重置抽奖次数方法
    public function resetDraws() {
        try {
            $merchantId = input('merchantId/s', '');
            
            if (empty($merchantId)) {
                return json(['code' => 400, 'msg' => '商户ID不能为空']);
            }

            // 读取配置文件获取每日抽奖次数
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            // 获取每日抽奖次数设置
            $dailyLimit = isset($config['lottery_config']['daily_limit']) ? 
                $config['lottery_config']['daily_limit'] : 3; // 默认3次
            
            // 更新商户限制
            $config['merchant_limits'][$merchantId] = [
                'daily_limit' => $dailyLimit,  // 使用每日抽奖次数设置
                'used_count' => 0,  // 重置已使用次数为0
                'update_time' => time(),
                'last_reset_date' => date('Y-m-d')
            ];

            // 保存配置
            if (!file_put_contents($configFile, "<?php\nreturn " . var_export($config, true) . ";\n")) {
                throw new \Exception('保存配置失败');
            }

            return json(['code' => 200, 'msg' => '重置成功']);
        } catch (\Exception $e) {
            Log::error('重置抽奖次数失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '重置失败：' . $e->getMessage()]);
        }
    }

    // 清空抽奖记录
    public function clearRecords() {
        try {
            // 读取配置文件
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            // 清空所有记录
            $config['lottery_records'] = [];
            
            // 更新配置文件
            if (!file_put_contents($configFile, "<?php\nreturn " . var_export($config, true) . ";\n")) {
                throw new \Exception('保存配置失败');
            }

            return json(['code' => 200, 'msg' => '清空成功']);
        } catch (\Exception $e) {
            Log::error('清空抽奖记录失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '清空失败：' . $e->getMessage()]);
        }
    }

    // 添加辅助方法：获取奖品类型文本
    private function getPrizeTypeText($type) {
        // 读取配置文件获取奖品类型定义
        $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
        $config = include $configFile;
        
        // 从配置中获取奖品类型定义
        $prizeTypes = isset($config['prize_types']) ? $config['prize_types'] : [];
        
        // 查找对应的类型定义
        foreach ($prizeTypes as $prizeType) {
            if ($prizeType['value'] === $type) {
                return $prizeType['label'];
            }
        }
        
        // 如果找不到对应的类型定义，使用默认的映射关系
        switch ($type) {
            case 'virtual':
                return '虚拟奖品';
            case 'physical':
                return '实物奖品';
            case 'cash':
                return '现金红包';
            default:
                return '未知类型';
        }
    }

    // 添加 getPrizeTypeStyle 方法
    private function getPrizeTypeStyle($type) {
        // 读取配置文件获取奖品类型定义
        $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
        $config = include $configFile;
        
        // 从配置中获取奖品类型定义
        $prizeTypes = isset($config['prize_types']) ? $config['prize_types'] : [];
        
        // 查找对应的类型定义
        foreach ($prizeTypes as $prizeType) {
            if ($prizeType['value'] === $type) {
                if ($prizeType['tagType'] === 'custom' && !empty($prizeType['tagColor'])) {
                    return [
                        'style' => [
                            'backgroundColor' => $prizeType['tagColor'],
                            'borderColor' => $prizeType['tagColor'],
                            'color' => '#ffffff'
                        ]
                    ];
                }
                return ['type' => $prizeType['tagType'] ?: 'info'];
            }
        }
        
        // 如果找不到对应的类型定义，使用默认的映射关系
        return ['type' => 'info'];
    }

    // 修改发放余额方法
    public function sendBalance() {
        try {
            $recordId = input('record_id/s', '');
            $merchantId = input('merchant_id/d', 0);
            $amount = input('amount/f', 0);
            $prizeName = input('prize_name/s', ''); // 添加奖品名称参数

            // 基础验证
            if (empty($recordId) || empty($merchantId) || $amount <= 0) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }

            // 开启事务
            Db::startTrans();
            
            // 获取用户当前余额
            $user = Db::name('user')->where('id', $merchantId)->find();
            if (!$user) {
                Db::rollback();
                return json(['code' => 404, 'msg' => '用户不存在']);
            }
            
            // 计算新余额
            $beforeBalance = $user['platform_money'];
            $afterBalance = bcadd($beforeBalance, $amount, 2);
            
            // 更新用户余额
            $result = Db::name('user')
                ->where('id', $merchantId)
                ->update(['platform_money' => $afterBalance]);
                
            if (!$result) {
                Db::rollback();
                return json(['code' => 500, 'msg' => '更新用户余额失败']);
            }
            
            // 读取配置文件
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            // 查找中奖记录
            $found = false;
            foreach ($config['lottery_records'] as &$record) {
                if ($record['id'] === $recordId) {
                    $found = true;
                    // 检查是否已发放或已发货
                    if (!empty($record['balance_sent']) || !empty($record['auto_sent']) || !empty($record['shipped'])) {
                        Db::rollback();
                        return json(['code' => 400, 'msg' => '该记录已处理过']);
                    }
                    
                    // 更新记录状态
                    $record['balance_sent'] = true;
                    $record['balance_amount'] = $amount;
                    $record['balance_time'] = date('Y-m-d H:i:s');
                    $record['shipped'] = true;
                    $record['ship_time'] = date('Y-m-d H:i:s');
                    break;
                }
            }

            if (!$found) {
                Db::rollback();
                return json(['code' => 404, 'msg' => '记录不存在']);
            }

            // 发放余额记录
            $result = Db::name('user_money_log')->insert([
                'user_id' => $merchantId,
                'change' => $amount,
                'reason' => '恭喜你获得' . $prizeName, // 修改reason描述
                'create_time' => time(),
                'source' => 'Platform'
            ]);

            if (!$result) {
                Db::rollback();
                return json(['code' => 500, 'msg' => '发放余额失败']);
            }

            // 保存配置文件
            if (!file_put_contents($configFile, "<?php\nreturn " . var_export($config, true) . ";\n")) {
                Db::rollback();
                return json(['code' => 500, 'msg' => '保存配置失败']);
            }

            // 提交事务
            Db::commit();
            return json(['code' => 200, 'msg' => '发放成功']);
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('发放余额失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '发放失败：' . $e->getMessage()]);
        }
    }

    // 获取自动发送余额配置
    public function getAutoSendConfig() {
        try {
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => [
                    'auto_send' => $config['auto_send_balance'] ?? false
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取自动发送配置失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取配置失败：' . $e->getMessage()]);
        }
    }

    // 保存自动发送余额配置
    public function saveAutoSendConfig() {
        try {
            $autoSend = input('auto_send/b', false);
            
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            $config['auto_send_balance'] = $autoSend;
            
            if (!file_put_contents($configFile, "<?php\nreturn " . var_export($config, true) . ";\n")) {
                throw new \Exception('保存配置失败');
            }
            
            return json(['code' => 200, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            Log::error('保存自动发送配置失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    // 修改发货方法
    public function handleShipment() {
        try {
            $recordId = input('post.record_id/s', '');
            
            if (empty($recordId)) {
                return json(['code' => 400, 'msg' => '记录ID不能为空']);
            }
            
            // 开启事务
            Db::startTrans();
            
            // 读取配置文件
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            // 查找并更新记录
            $found = false;
            $record = null;
            $balanceAmount = 0;

            foreach ($config['lottery_records'] as &$record) {
                if (isset($record['id']) && $record['id'] === $recordId) {
                    // 获取对应的奖品信息
                    $prize = null;
                    foreach ($config['prizes'] as $p) {
                        if ($p['id'] == $record['prize_id']) {
                            $prize = $p;
                            break;
                        }
                    }
                    
                    // 如果是余额奖品且未发放过,自动发放余额
                    if ($prize && isset($prize['balance_amount']) && $prize['balance_amount'] > 0 
                        && empty($record['balance_sent']) && empty($record['auto_sent'])) {
                        
                        // 获取用户当前余额
                        $user = Db::name('user')->where('id', $record['merchant_id'])->find();
                        if (!$user) {
                            Db::rollback();
                            return json(['code' => 404, 'msg' => '用户不存在']);
                        }
                        
                        $beforeBalance = $user['platform_money'];
                        $afterBalance = bcadd($beforeBalance, $prize['balance_amount'], 2);
                        
                        // 更新用户余额
                        $result = Db::name('user')
                            ->where('id', $record['merchant_id'])
                            ->update(['platform_money' => $afterBalance]);
                            
                        if (!$result) {
                            Db::rollback();
                            return json(['code' => 500, 'msg' => '更新用户余额失败']);
                        }
                        
                        // 添加余额变动记录
                        $moneyLogResult = Db::name('user_money_log')->insert([
                            'user_id' => $record['merchant_id'],
                            'change' => $prize['balance_amount'],
                            'reason' => '恭喜你获得' . $record['prize_name'], // 修改这里
                            'create_time' => time(),
                            'source' => 'Platform'
                        ]);
                        
                        if (!$moneyLogResult) {
                            Db::rollback();
                            return json(['code' => 500, 'msg' => '添加余额记录失败']);
                        }
                        
                        // 更新中奖记录状态
                        $record['balance_sent'] = true;
                        $record['auto_sent'] = true;
                        $record['balance_amount'] = $prize['balance_amount'];
                        $record['balance_time'] = date('Y-m-d H:i:s');
                        
                        $balanceAmount = $prize['balance_amount'];
                    }
                    
                    // 更新发货状态
                    $record['shipped'] = 1;
                    $record['ship_time'] = date('Y-m-d H:i:s');
                    $record['update_time'] = time();
                    
                    $found = true;
                    break;
                }
            }

            if (!$found) {
                throw new \Exception('未找到对应的记录');
            }
            
            // 保存配置文件
            if (!file_put_contents($configFile, "<?php\nreturn " . var_export($config, true) . ";\n")) {
                throw new \Exception('保存配置失败');
            }
            
            // 提交事务
            Db::commit();
            
            // 修改返回信息，添加余额显示
            $msg = '发货成功';
            if ($balanceAmount > 0) {
                $msg .= '，余额:' . $balanceAmount . '元';
            }
            
            return json(['code' => 200, 'msg' => $msg]);
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('发货失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '发货失败：' . $e->getMessage()]);
        }
    }

    // 更新奖品类型
    public function updatePrizeType() {
        try {
            $data = [
                'label' => input('label/s', ''),
                'value' => input('value/s', ''),
                'tagType' => input('tagType/s', ''),
                'tagColor' => input('tagColor/s', '')
            ];

            if (empty($data['label']) || empty($data['value'])) {
                return json(['code' => 400, 'msg' => '类型名称和标识不能为空']);
            }

            // 读取配置文件
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            $prizeTypes = isset($config['prize_types']) ? $config['prize_types'] : [];

            // 查找并更新类型
            $found = false;
            foreach ($prizeTypes as &$type) {
                if ($type['value'] === $data['value']) {
                    // 如果是系统类型，只允许修改标签名称
                    if (!empty($type['is_system'])) {
                        $type['label'] = $data['label'];
                    } else {
                        $type['label'] = $data['label'];
                        $type['tagType'] = $data['tagType'];
                        if ($data['tagType'] === 'custom') {
                            $type['tagColor'] = preg_replace('/^#([A-Fa-f0-9]{6}).*/', '#$1', $data['tagColor']);
                        }
                    }
                    $found = true;
                    break;
                }
            }

            if (!$found) {
                return json(['code' => 404, 'msg' => '类型不存在']);
            }

            // 更新配置文件
            $config['prize_types'] = array_values($prizeTypes);
            if (!file_put_contents($configFile, "<?php\nreturn " . var_export($config, true) . ";\n")) {
                throw new \Exception('保存配置失败');
            }

            return json(['code' => 200, 'msg' => '修改成功']);
        } catch (\Exception $e) {
            Log::error('修改奖品类型失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '修改失败：' . $e->getMessage()]);
        }
    }

    // 删除奖品类型
    public function deletePrizeType() {
        try {
            $value = input('value/s', '');
            if (empty($value)) {
                return json(['code' => 400, 'msg' => '类型标识不能为空']);
            }

            // 读取配置文件
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            // 获取现有奖品类型
            $prizeTypes = isset($config['prize_types']) ? $config['prize_types'] : [];

            // 检查是否为系统类型
            foreach ($prizeTypes as $type) {
                if ($type['value'] === $value && isset($type['is_system']) && $type['is_system']) {
                    return json(['code' => 400, 'msg' => '系统类型不能删除']);
                }
            }

            // 检查是否有奖品正在使用该类型
            if (isset($config['prizes'])) {
                foreach ($config['prizes'] as $prize) {
                    if ($prize['type'] === $value) {
                        return json(['code' => 400, 'msg' => '该类型正在被奖品使用，无法删除']);
                    }
                }
            }

            // 过滤掉要删除的类型
            $newTypes = array_filter($prizeTypes, function($type) use ($value) {
                return $type['value'] !== $value;
            });

            // 更新配置文件
            $config['prize_types'] = array_values($newTypes);
            if (!file_put_contents($configFile, "<?php\nreturn " . var_export($config, true) . ";\n")) {
                throw new \Exception('保存配置失败');
            }

            return json(['code' => 200, 'msg' => '删除成功']);
        } catch (\Exception $e) {
            Log::error('删除奖品类型失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }

    // 获取流水规则
    public function getTurnoverRules() {
        try {
            $page = input('page/d', 1);
            $pageSize = input('pageSize/d', 12); // 默认每页12条
            
            // 读取配置文件
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            // 获取流水规则
            $rules = isset($config['turnover_rules']) ? $config['turnover_rules'] : [];
            
            // 按流水金额排序
            usort($rules, function($a, $b) {
                return $a['turnover_amount'] - $b['turnover_amount'];
            });
            
            // 计算总数
            $total = count($rules);
            
            // 分页处理
            $offset = ($page - 1) * $pageSize;
            $rules = array_slice($rules, $offset, $pageSize);
            
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => [
                    'list' => $rules,
                    'total' => $total,
                    'page' => $page,
                    'pageSize' => $pageSize
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取流水规则失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取规则失败：' . $e->getMessage()]);
        }
    }

    // 保存流水规则
    public function saveTurnoverRule() {
        try {
            $data = [
                'id' => input('id/d', 0),
                'turnover_amount' => input('turnover_amount/f', 0),
                'draw_times' => input('draw_times/d', 0),
                'status' => input('status/d', 1)
            ];
            
            // 验证数据
            if ($data['turnover_amount'] <= 0) {
                return json(['code' => 400, 'msg' => '流水金额必须大于0']);
            }
            
            if ($data['draw_times'] <= 0) {
                return json(['code' => 400, 'msg' => '抽奖次数必须大于0']);
            }

            // 确保状态值为 0 或 1
            $data['status'] = $data['status'] ? 1 : 0;
            
            // 读取配置文件
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            if (!isset($config['turnover_rules'])) {
                $config['turnover_rules'] = [];
            }
            
            // 查找最大ID
            $maxId = 0;
            foreach ($config['turnover_rules'] as $rule) {
                if (isset($rule['id']) && $rule['id'] > $maxId) {
                    $maxId = $rule['id'];
                }
            }
            
            // 更新或添加规则
            $existingIndex = -1;
            foreach ($config['turnover_rules'] as $index => $rule) {
                if (isset($rule['id']) && $rule['id'] === $data['id']) {
                    $existingIndex = $index;
                    break;
                }
            }
            
            if ($existingIndex !== -1) {
                if (!isset($config['turnover_rules'][$existingIndex])) {
                    return json(['code' => 404, 'msg' => '规则不存在']);
                }
                $config['turnover_rules'][$existingIndex] = $data;
            } else {
                $data['id'] = $maxId + 1;
                $config['turnover_rules'][] = $data;
            }
            
            // 保存配置
            if (!file_put_contents($configFile, "<?php\nreturn " . var_export($config, true) . ";\n")) {
                throw new \Exception('保存配置失败');
            }
            
            // 仅在状态变更时返回成功消息
            return json([
                'code' => 200,
                'data' => $config['turnover_rules']
            ]);
        } catch (\Exception $e) {
            Log::error('保存流水规则失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    // 删除流水规则
    public function deleteTurnoverRule() {
        try {
            // 确保获取到正确的id参数
            $id = intval(input('post.id', 0));
            if ($id <= 0) {
                return json(['code' => 400, 'msg' => '无效的规则ID']);
            }
            
            // 读取配置文件
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            if (!isset($config['turnover_rules']) || !is_array($config['turnover_rules'])) {
                return json(['code' => 404, 'msg' => '规则不存在']);
            }
            
            // 查找并删除规则
            $found = false;
            $newRules = [];
            foreach ($config['turnover_rules'] as $rule) {
                if (isset($rule['id']) && intval($rule['id']) === $id) {
                    $found = true;
                    continue;
                }
                $newRules[] = $rule;
            }
            
            if (!$found) {
                return json(['code' => 404, 'msg' => '规则不存在']);
            }
            
            // 更新配置
            $config['turnover_rules'] = array_values($newRules);  // 重新索引数组
            
            // 保存配置文件
            if (!file_put_contents($configFile, "<?php\nreturn " . var_export($config, true) . ";\n")) {
                throw new \Exception('保存配置失败');
            }
            
            return json(['code' => 200, 'msg' => '删除成功']);
        } catch (\Exception $e) {
            Log::error('删除流水规则失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }

    // 获取Hook配置
    public function getHookConfig() {
        try {
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => [
                    'auto_update_status' => intval(plugconf('Lottery.auto_update_status') ?? 0),
                    'update_interval' => intval(plugconf('Lottery.update_interval') ?? 300),
                    'last_update' => intval(plugconf('Lottery.last_update') ?? 0)
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取Hook配置失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    // 保存Hook配置
    public function saveHookConfig() {
        try {
            $status = input('auto_update_status/d', 0);
            $interval = input('update_interval/d', 300);
            
            // 验证间隔时间
            if ($interval < 300 || $interval > 86400) {
                return json(['code' => 400, 'msg' => '更新间隔必须在5分钟到24小时之间']);
            }
            
            // 保存配置
            plugconf('Lottery.auto_update_status', strval($status));
            plugconf('Lottery.update_interval', strval($interval));
            
            return json(['code' => 200, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            Log::error('保存Hook配置失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    // 手动触发更新
    public function triggerUpdate() {
        try {
            $hook = new \plugin\Lottery\Hook();
            $hook->handle();
            return json(['code' => 200, 'msg' => '更新成功']);
        } catch (\Exception $e) {
            Log::error('手动触发更新失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '更新失败：' . $e->getMessage()]);
        }
    }

    // 获取流水配置
    public function getTurnoverConfig() {
        try {
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = include $configFile;
            
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => [
                    'reset_type' => $config['turnover_reset']['type'] ?? 'daily',
                    'last_reset' => $config['turnover_reset']['last_reset'] ?? 0,
                    'rules' => $config['turnover_rules'] ?? []
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取流水配置失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取配置失败：' . $e->getMessage()]);
        }
    }

    // 保存流水配置
    public function saveTurnoverConfig() {
        try {
            $resetType = input('reset_type/s', 'daily');
            
            // 验证重置类型
            if (!in_array($resetType, ['daily', 'weekly', 'monthly'])) {
                return json(['code' => 400, 'msg' => '无效的重置类型']);
            }
            
            // 读取配置文件
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            // 更新重置类型配置
            if (!isset($config['turnover_reset'])) {
                $config['turnover_reset'] = [];
            }
            $config['turnover_reset']['type'] = $resetType;
            
            // 保存配置文件
            if (!file_put_contents($configFile, "<?php\nreturn " . var_export($config, true) . ";\n")) {
                throw new \Exception('保存配置失败');
            }
            
            return json(['code' => 200, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            Log::error('保存流水配置失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    // 手动重置流水
    public function resetTurnover() {
        try {
            // 获取当前系统时间
            $currentTime = time();
            $currentDate = date('Y-m-d H:i:s', $currentTime);
            
            // 读取配置文件
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = include $configFile;
            
            // 更新配置文件中的重置时间和流水记录
            if (!isset($config['turnover_reset'])) {
                $config['turnover_reset'] = [];
            }
            
            $config['turnover_reset']['last_reset'] = $currentTime;
            $config['turnover_reset']['reset_time'] = $currentDate;
            
            // 清空重置时间点之前的流水记录
            if (isset($config['turnover_records'])) {
                // 只保留重置时间点之后的记录
                $config['turnover_records'] = array_filter($config['turnover_records'], function($record) use ($currentTime) {
                    return isset($record['time']) && $record['time'] > $currentTime;
                });
            } else {
                $config['turnover_records'] = [];
            }
            
            // 保存配置文件
            if (!file_put_contents($configFile, "<?php\nreturn " . var_export($config, true) . ";\n")) {
                throw new \Exception('更新重置时间失败');
            }
            
            return json([
                'code' => 200, 
                'msg' => '重置成功',
                'data' => [
                    'reset_time' => $currentDate
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('重置流水失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '重置失败：' . $e->getMessage()]);
        }
    }

    // 批量发货
    public function batchShipment() {
        try {
            $recordIds = input('post.record_ids/a', []);
            
            if (empty($recordIds)) {
                return json(['code' => 400, 'msg' => '请选择要发货的记录']);
            }
            
            // 开启事务
            Db::startTrans();
            
            // 读取配置文件
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            $successCount = 0;
            $failCount = 0;
            
            foreach ($config['lottery_records'] as &$record) {
                if (in_array($record['id'], $recordIds)) {
                    // 检查是否已发货
                    if (!empty($record['shipped'])) {
                        $failCount++;
                        continue;
                    }
                    
                    // 获取对应的奖品信息
                    $prize = null;
                    foreach ($config['prizes'] as $p) {
                        if ($p['id'] == $record['prize_id']) {
                            $prize = $p;
                            break;
                        }
                    }
                    
                    // 如果是余额奖品且未发放过,自动发放余额
                    if ($prize && isset($prize['balance_amount']) && $prize['balance_amount'] > 0 
                        && empty($record['balance_sent']) && empty($record['auto_sent'])) {
                        
                        // 获取用户当前余额
                        $user = Db::name('user')->where('id', $record['merchant_id'])->find();
                        if (!$user) {
                            $failCount++;
                            continue;
                        }
                        
                        $beforeBalance = $user['platform_money'];
                        $afterBalance = bcadd($beforeBalance, $prize['balance_amount'], 2);
                        
                        // 更新用户余额
                        $result = Db::name('user')
                            ->where('id', $record['merchant_id'])
                            ->update(['platform_money' => $afterBalance]);
                            
                        if (!$result) {
                            $failCount++;
                            continue;
                        }
                        
                        // 添加余额变动记录
                        $moneyLogResult = Db::name('user_money_log')->insert([
                            'user_id' => $record['merchant_id'],
                            'change' => $prize['balance_amount'],
                            'reason' => '恭喜你获得' . $record['prize_name'],
                            'create_time' => time(),
                            'source' => 'Platform'
                        ]);
                        
                        if (!$moneyLogResult) {
                            $failCount++;
                            continue;
                        }
                        
                        // 更新中奖记录状态
                        $record['balance_sent'] = true;
                        $record['auto_sent'] = true;
                        $record['balance_amount'] = $prize['balance_amount'];
                        $record['balance_time'] = date('Y-m-d H:i:s');
                    }
                    
                    // 更新发货状态
                    $record['shipped'] = 1;
                    $record['ship_time'] = date('Y-m-d H:i:s');
                    $record['update_time'] = time();
                    
                    $successCount++;
                }
            }
            
            // 保存配置文件
            if (!file_put_contents($configFile, "<?php\nreturn " . var_export($config, true) . ";\n")) {
                throw new \Exception('保存配置失败');
            }
            
            // 提交事务
            Db::commit();
            
            return json([
                'code' => 200, 
                'msg' => "批量发货完成，成功: {$successCount} 条，失败: {$failCount} 条"
            ]);
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('批量发货失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '批量发货失败：' . $e->getMessage()]);
        }
    }

    // 添加设置隐藏奖品配置
    public function setHiddenPrizes() {
        try {
            $hiddenPrizes = input('hiddenPrizes/a', []);
            
            // 读取配置文件
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            // 更新隐藏奖品配置
            $config['hidden_prizes'] = array_values(array_map('intval', array_unique($hiddenPrizes)));
            
            // 保存配置
            if (!file_put_contents($configFile, "<?php\nreturn " . var_export($config, true) . ";\n")) {
                throw new \Exception('保存配置失败');
            }
            
            return json(['code' => 200, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            Log::error('保存隐藏奖品设置失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    /**
     * 生成虚拟中奖记录
     */
    public function generateFakeRecords() {
        try {
            $count = input('count/d', 1);
            $prizeId = input('prize_id/d', 0);
            $startTime = input('start_time/s', '');
            $endTime = input('end_time/s', '');
            $merchantPrefix = input('merchant_prefix/s', '');
            $merchantIdStart = input('merchant_id_start/s', '');
            $merchantIdEnd = input('merchant_id_end/s', '');

            // 验证时间格式
            if (!empty($startTime) && !strtotime($startTime)) {
                return json(['code' => 400, 'msg' => '开始时间格式不正确']);
            }
            if (!empty($endTime) && !strtotime($endTime)) {
                return json(['code' => 400, 'msg' => '结束时间格式不正确']);
            }
            
            // 如果没有设置时间范围，默认为最近7天
            $startTimestamp = !empty($startTime) ? strtotime($startTime) : (time() - 7 * 24 * 3600);
            $endTimestamp = !empty($endTime) ? strtotime($endTime) : time();
            
            // 验证时间范围
            if ($endTimestamp <= $startTimestamp) {
                return json(['code' => 400, 'msg' => '结束时间必须大于开始时间']);
            }

            // 验证商家ID范围
            if (!empty($merchantIdStart) && !empty($merchantIdEnd)) {
                if (!is_numeric($merchantIdStart) || !is_numeric($merchantIdEnd)) {
                    return json(['code' => 400, 'msg' => '商家ID范围必须是数字']);
                }
                if ((int)$merchantIdStart >= (int)$merchantIdEnd) {
                    return json(['code' => 400, 'msg' => '商家ID结束值必须大于起始值']);
                }
            }

            // 读取配置文件
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            // 获取可用奖品列表
            $prizes = isset($config['prizes']) ? $config['prizes'] : [];
            $prizes = array_filter($prizes, function($prize) {
                return $prize['status'] == 1;
            });
            
            if (empty($prizes)) {
                return json(['code' => 400, 'msg' => '没有可用的奖品']);
            }

            // 处理商家ID前缀
            $merchantPrefixes = [];
            if (!empty($merchantPrefix)) {
                // 去除空格并分割
                $merchantPrefixes = array_map('trim', explode(',', $merchantPrefix));
                // 验证生成数量不能超过前缀数量
                if ($count > count($merchantPrefixes)) {
                    return json(['code' => 400, 'msg' => '生成数量不能超过商家ID数量']);
                }
            }

            // 生成虚拟记录
            $newRecords = [];
            for ($i = 0; $i < $count; $i++) {
                // 如果指定了奖品ID，就使用指定的奖品
                $prize = null;
                if ($prizeId) {
                    foreach ($prizes as $p) {
                        if ($p['id'] == $prizeId) {
                            $prize = $p;
                            break;
                        }
                    }
                    if (!$prize) {
                        return json(['code' => 404, 'msg' => '指定的奖品不存在']);
                    }
                } else {
                    // 随机选择一个奖品
                    $prize = $prizes[array_rand($prizes)];
                }
                
                // 生成随机时间（在指定范围内）
                $randomTime = rand($startTimestamp, $endTimestamp);
                
                // 生成商户ID
                if (!empty($merchantIdStart) && !empty($merchantIdEnd)) {
                    // 使用指定范围生成商户ID
                    $merchantId = rand((int)$merchantIdStart, (int)$merchantIdEnd);
                } elseif (!empty($merchantPrefixes)) {
                    // 按顺序使用前缀列表中的ID
                    $merchantId = $merchantPrefixes[$i];
                } else {
                    // 使用8位随机数
                    $merchantId = str_pad(rand(10000000, 99999999), 8, '0', STR_PAD_LEFT);
                }
                
                // 生成商户名称
                $merchantName = '商家' . $merchantId;
                
                // 创建记录
                $record = [
                    'id' => uniqid(),
                    'user_id' => $merchantId,
                    'merchant_id' => $merchantId,
                    'merchant_name' => $merchantName,
                    'prize_id' => $prize['id'],
                    'prize_name' => $prize['name'],
                    'prize_type' => $prize['type'],
                    'prize_image' => $prize['image'] ?? '',
                    'balance_amount' => isset($prize['balance_amount']) ? $prize['balance_amount'] : 0,
                    'create_time' => date('Y-m-d H:i:s', $randomTime),
                    'shipped' => true,
                    'ship_time' => date('Y-m-d H:i:s', $randomTime + rand(300, 3600)),  // 发货时间在中奖后5分钟到1小时内
                    'balance_sent' => true,
                    'auto_sent' => true,
                    'is_virtual' => true  // 标记为虚拟记录
                ];
                
                $newRecords[] = $record;
            }
            
            // 添加到现有记录中
            $config['lottery_records'] = array_merge(
                isset($config['lottery_records']) ? $config['lottery_records'] : [],
                $newRecords
            );
            
            // 保存配置
            if (!file_put_contents($configFile, "<?php\nreturn " . var_export($config, true) . ";\n")) {
                throw new \Exception('保存配置失败');
            }
            
            return json([
                'code' => 200, 
                'msg' => "成功生成 {$count} 条虚拟中奖记录",
                'data' => $newRecords
            ]);
            
        } catch (\Exception $e) {
            Log::error('生成虚拟中奖记录失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '生成失败：' . $e->getMessage()]);
        }
    }

    // 批量删除中奖记录
    public function batchDeleteRecords() {
        try {
            $recordIds = input('recordIds/a', []);
            if (empty($recordIds)) {
                return json(['code' => 400, 'msg' => '请选择要删除的记录']);
            }

            // 读取配置文件
            $configFile = app()->getRootPath() . 'plugin/Lottery/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            if (!isset($config['lottery_records'])) {
                return json(['code' => 404, 'msg' => '没有找到中奖记录']);
            }

            // 过滤保留未被选中删除的记录
            $newRecords = array_filter($config['lottery_records'], function($record) use ($recordIds) {
                return !in_array($record['id'], $recordIds);
            });

            // 更新配置文件
            $config['lottery_records'] = array_values($newRecords);
            if (!file_put_contents($configFile, "<?php\nreturn " . var_export($config, true) . ";\n")) {
                throw new \Exception('保存配置失败');
            }

            return json([
                'code' => 200,
                'msg' => '批量删除成功',
                'data' => [
                    'deletedCount' => count($recordIds)
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('批量删除中奖记录失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '批量删除失败：' . $e->getMessage()]);
        }
    }
}
