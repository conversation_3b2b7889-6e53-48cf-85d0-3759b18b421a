<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>飘窗默认设置</title>
    <style>
        /* 响应式设计增强 */
        /* 超小屏幕手机 */
        @media screen and (max-width: 320px) {
            body {
                padding: 8px;
                font-size: 14px;
            }
            .el-card {
                border-radius: 8px;
            }
            .card-header span {
                font-size: 16px;
            }
            .el-form-item__label {
                font-size: 13px;
            }
            .number-input-item {
                padding: 6px 8px;
            }
            .avatar-uploader, .avatar, .avatar-uploader-icon {
                width: 100px;
                height: 100px;
            }
        }

        /* 小屏幕手机 */
        @media screen and (min-width: 321px) and (max-width: 375px) {
            body {
                padding: 10px;
                font-size: 14px;
            }
            .card-header span {
                font-size: 17px;
            }
            .el-form-item__label {
                font-size: 14px;
            }
            .number-input-item {
                padding: 7px 10px;
            }
        }

        /* 中等屏幕手机 */
        @media screen and (min-width: 376px) and (max-width: 414px) {
            body {
                padding: 12px;
                font-size: 15px;
            }
            .card-header span {
                font-size: 18px;
            }
            .number-input-item {
                padding: 8px 12px;
            }
        }

        /* 大屏幕手机 */
        @media screen and (min-width: 415px) and (max-width: 768px) {
            body {
                padding: 14px;
                font-size: 16px;
            }
            .card-header span {
                font-size: 19px;
            }
            .number-input-item {
                padding: 10px 14px;
            }
            .avatar-uploader, .avatar, .avatar-uploader-icon {
                width: 140px;
                height: 140px;
            }
        }

        /* 横屏模式优化 */
        @media screen and (max-width: 768px) and (orientation: landscape) {
            .el-form {
                max-width: 600px;
                margin: 0 auto;
            }
            .number-input-group {
                flex-direction: row;
                flex-wrap: wrap;
            }
            .number-input-item {
                width: calc(50% - 10px);
            }
        }

        /* 高分辨率屏幕优化 */
        @media screen and (min-width: 769px) and (min-device-pixel-ratio: 2) {
            body {
                font-size: 16px;
            }
            .el-card {
                max-width: 800px;
                margin: 20px auto;
            }
            .number-input-group {
                justify-content: flex-start;
            }
        }

        /* 动态字体大小计算 */
        :root {
            --base-font-size: 16px;
            --min-font-size: 14px;
            --max-font-size: 18px;
            font-size: clamp(var(--min-font-size), 1vw + 1vh, var(--max-font-size));
        }

        /* 自适应间距 */
        .el-form-item {
            margin-bottom: clamp(20px, 4vw, 28px);
        }
        .number-input-group {
            gap: clamp(10px, 2vw, 20px);
        }

        /* 优化触摸区域 */
        .el-button, .el-switch, .action-icon {
            min-height: 44px; /* 符合可访问性标准的最小触摸区域 */
        }

        /* 优化图片预览 */
        .image-container {
            max-width: 100%;
            height: auto;
        }

        /* 优化输入框 */
        .el-input-number {
            width: 100%;
            max-width: 200px;
        }

        /* 优化开关组件 */
        .el-switch {
            transform-origin: left center;
            transform: scale(1.2);
        }

        @media (hover: hover) {
            /* 仅在支持悬停的设备上启用悬停效果 */
            .number-input-item:hover {
                background: #ecf5ff;
            }
            .action-icon:hover {
                transform: scale(1.1);
            }
        }

        /* 暗黑模式支持 */
        @media (prefers-color-scheme: dark) {
            body {
                background: #1a1a1a;
            }
            .el-card {
                background: #2b2b2b;
                border-color: #3a3a3a;
            }
            .number-input-item {
                background: #333;
            }
        }

        /* 添加触摸设备支持 */
        @media (hover: none) {
            .image-actions {
                display: flex;
                background: rgba(0,0,0,0.2);
            }
            .image-container:active .image-actions {
                display: flex;
            }
        }
    </style>
</head>

<body>
    <div id="app">
        <el-card shadow="never">
            <template #header>
                <div class="card-header">
                    <span>飘窗默认配置</span>
                    <div class="el-form-item-msg">此处设置的配置将作为商家未设置时的默认值</div>
                </div>
            </template>

            <el-form :model="form" label-width="90px">
                <!-- 飘窗开关 -->
                <el-form-item label="飘窗开关">
                    <el-switch 
                        v-model="form.status" 
                        :active-value="1" 
                        :inactive-value="0"
                        class="custom-switch" 
                    />
                </el-form-item>

                <!-- 图片上传 -->
                <el-form-item label="图片上传">
                    <div class="image-container">
                        <el-upload
                            class="avatar-uploader"
                            action="/adminApi/Upload/file"
                            :headers="uploadHeaders"
                            :show-file-list="false"
                            :on-success="handleUploadSuccess"
                            :on-error="onUploadError"
                            :before-upload="beforeUpload"
                            :with-credentials="true"
                            name="file">
                            <img v-if="form.image_url" :src="form.image_url" class="avatar">
                            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                        </el-upload>
                        
                        <div v-if="form.image_url" class="image-actions">
                            <el-icon class="action-icon" @click="previewImage"><ZoomIn /></el-icon>
                            <el-icon class="action-icon" @click="removeImage('main')"><Delete /></el-icon>
                        </div>
                    </div>
                    <div class="el-form-item-msg">悬浮在页面上显示的图片，建议比例相近，大小不超过2MB</div>
                </el-form-item>

                <!-- 弹窗图片 -->
                <el-form-item label="弹窗图片">
                    <div class="image-container">
                        <el-upload
                            class="avatar-uploader"
                            action="/adminApi/Upload/file"
                            :headers="uploadHeaders"
                            :show-file-list="false"
                            :on-success="handlePopupUploadSuccess"
                            :on-error="onUploadError"
                            :before-upload="beforeUpload"
                            :with-credentials="true"
                            name="file">
                            <img v-if="form.popup_image_url" :src="form.popup_image_url" class="avatar">
                            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                        </el-upload>
                        
                        <div v-if="form.popup_image_url" class="image-actions">
                            <el-icon class="action-icon" @click="previewPopupImage"><ZoomIn /></el-icon>
                            <el-icon class="action-icon" @click="removeImage('popup')"><Delete /></el-icon>
                        </div>
                    </div>
                    <div class="el-form-item-msg">点击悬浮框后显示的弹窗图片（如二维码），建议清晰可识别</div>
                </el-form-item>

                <!-- 跳转链接 -->
                <el-form-item label="跳转链接">
                    <el-input v-model="form.link_url" placeholder="点击图片跳转的链接地址" />
                    <div class="el-form-item-msg">请输入完整的URL地址，包含http://或https://</div>
                </el-form-item>

                <!-- 弹窗设置 -->
                <el-form-item label="弹窗设置">
                    <div style="margin-bottom: 15px;">
                        <div style="margin-bottom: 10px; font-weight: bold;">弹窗标题：</div>
                        <el-input v-model="form.popup_title" placeholder="输入弹窗标题文字" />
                    </div>
                    <div>
                        <div style="margin-bottom: 10px; font-weight: bold;">底部提示：</div>
                        <el-input v-model="form.popup_footer" placeholder="输入底部提示文字" />
                    </div>
                    <div class="el-form-item-msg">设置扫码弹窗的标题和底部提示文字</div>
                </el-form-item>

                <!-- 图片尺寸 -->
                <el-form-item label="图片尺寸">
                    <div class="number-input-group">
                        <div class="number-input-item">
                            <span>宽度：</span>
                            <el-input-number 
                                v-model="form.width" 
                                :min="50" 
                                :max="1200" 
                                :step="10"
                                controls-position="right"
                            />
                        </div>
                        <div class="number-input-item">
                            <span>高度：</span>
                            <el-input-number 
                                v-model="form.height" 
                                :min="50" 
                                :max="1200" 
                                :step="10"
                                controls-position="right"
                            />
                        </div>
                    </div>
                    <div class="el-form-item-msg">宽度和高度范围：50-1200像素</div>
                </el-form-item>

                <!-- 尺寸限制 -->
                <el-form-item label="尺寸限制">
                    <div class="number-input-group">
                        <div class="number-input-item">
                            <span>最小：</span>
                            <el-input-number 
                                v-model="form.min_size" 
                                :min="50" 
                                :max="300" 
                                :step="10"
                                controls-position="right"
                                @change="updateSizeLimit"
                            />
                        </div>
                        <div class="number-input-item">
                            <span>最大：</span>
                            <el-input-number 
                                v-model="form.max_size" 
                                :min="300" 
                                :max="800" 
                                :step="10"
                                controls-position="right"
                                @change="updateSizeLimit"
                            />
                        </div>
                    </div>
                    <div class="el-form-item-msg">最小尺寸范围：50-300像素，最大尺寸范围：300-800像素</div>
                </el-form-item>

                <!-- 显示位置 -->
                <el-form-item label="显示位置">
                    <div class="number-input-group">
                        <div class="number-input-item">
                            <span>底部：</span>
                            <el-input-number 
                                v-model="form.bottom" 
                                :min="0" 
                                :max="1000" 
                                controls-position="right"
                            />
                        </div>
                        <div class="number-input-item">
                            <span>右侧：</span>
                            <el-input-number 
                                v-model="form.right" 
                                :min="0" 
                                :max="1000" 
                                controls-position="right"
                            />
                        </div>
                    </div>
                    <div class="el-form-item-msg">距离底部和右侧的像素值</div>
                </el-form-item>

                <!-- 商家修改权限 -->
                <el-form-item label="商家权限">
                    <el-switch 
                        v-model="form.merchant_can_edit" 
                        :active-value="1" 
                        :inactive-value="0"
                        class="custom-switch" 
                    />
                    <div class="el-form-item-msg">开启后商家可以修改自己的飘窗设置，关闭后只能使用默认设置</div>
                </el-form-item>

                <!-- 提交按钮 -->
                <el-form-item>
                    <el-button 
                        type="primary" 
                        :loading="loading" 
                        @click="save"
                        class="save-btn"
                    >
                        保存设置
                    </el-button>
                </el-form-item>
            </el-form>
        </el-card>
    </div>

    <!-- 引入依赖库 -->
    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>

    <script>
        const { createApp, ref, nextTick, watch } = Vue;
        const app = createApp({
            setup() {
                // 状态定义
                const loading = ref(false);
                const form = ref({
                    status: 0,
                    image_url: '',
                    popup_image_url: '',
                    link_url: '',
                    width: 200,
                    height: 200,
                    bottom: 20,
                    right: 20,
                    min_size: 200,
                    max_size: 500,
                    popup_title: '扫描二维码',
                    popup_footer: '请使用扫码软件扫描',
                    merchant_can_edit: 1
                });

                // 获取配置
                const fetchData = async () => {
                    try {
                        const res = await axios.post('getConfig');
                        if (res.data?.code === 200 && res.data?.data) {
                            const data = res.data.data;
                            form.value = {
                                status: parseInt(data.status) || 0,
                                image_url: data.image_url || '',
                                popup_image_url: data.popup_image_url || '',
                                link_url: data.link_url || '',
                                width: parseInt(data.width) || 200,
                                height: parseInt(data.height) || 200,
                                bottom: parseInt(data.bottom) || 20,
                                right: parseInt(data.right) || 20,
                                min_size: parseInt(data.min_size) || 200,
                                max_size: parseInt(data.max_size) || 500,
                                popup_title: data.popup_title || '扫描二维码',
                                popup_footer: data.popup_footer || '请使用扫码软件扫描',
                                merchant_can_edit: data.merchant_can_edit !== undefined ? parseInt(data.merchant_can_edit) : 1
                            };
                        }
                    } catch (error) {
                        console.error('获取数据失败:', error);
                        ElementPlus.ElMessage.error('获取数据失败，请刷新页面重试');
                    }
                };

                // 保存配置
                const save = async () => {
                    try {
                        loading.value = true;
                        // 数据验证
                        if (form.value.status === 1 && !form.value.image_url) {
                            ElementPlus.ElMessage.error('请先上传图片');
                            return;
                        }

                        const res = await axios.post('saveConfig', form.value);
                        if (res.data?.code === 200) {
                            ElementPlus.ElMessage.success(res.data.msg || '保存成功');
                            // 使用返回的数据更新表单
                            if (res.data.data) {
                                const data = res.data.data;
                                // 先设置尺寸限制
                                form.value.min_size = parseInt(data.min_size);
                                form.value.max_size = parseInt(data.max_size);
                                
                                // 然后更新其他数据
                                form.value = {
                                    ...form.value,
                                    status: parseInt(data.status),
                                    image_url: data.image_url,
                                    popup_image_url: data.popup_image_url,
                                    link_url: data.link_url,
                                    width: parseInt(data.width),
                                    height: parseInt(data.height),
                                    bottom: parseInt(data.bottom),
                                    right: parseInt(data.right),
                                    popup_title: data.popup_title,
                                    popup_footer: data.popup_footer,
                                    merchant_can_edit: data.merchant_can_edit !== undefined ? parseInt(data.merchant_can_edit) : 1
                                };
                            }
                        } else {
                            ElementPlus.ElMessage.error(res.data?.msg || '保存失败');
                        }
                    } catch (error) {
                        console.error('保存失败:', error);
                        ElementPlus.ElMessage.error('保存失败，请稍后重试');
                    } finally {
                        loading.value = false;
                    }
                };

                // 处理图片上传成功
                const handleUploadSuccess = (res) => {
                    if (res.code === 200 && res.data?.url) {
                        const imageUrl = res.data.url;
                        // 检查URL是否是完整的http(s)地址
                        const fullUrl = imageUrl.startsWith('http') ? imageUrl : (window.location.origin + imageUrl);
                        
                        const img = new Image();
                        img.onload = () => {
                            form.value.width = Math.min(Math.max(img.width, 50), 1200);
                            form.value.height = Math.min(Math.max(img.height, 50), 1200);
                            form.value.image_url = fullUrl; // 使用完整URL
                            ElementPlus.ElMessage.success(res.msg || '上传成功');
                            save();
                        };
                        img.onerror = () => {
                            ElementPlus.ElMessage.error('图片加载失败');
                            form.value.image_url = '';
                        };
                        img.src = fullUrl;
                    } else {
                        form.value.image_url = '';
                        ElementPlus.ElMessage.error(res.msg || '上传失败');
                    }
                };

                // 处理弹窗图片上传成功
                const handlePopupUploadSuccess = (res) => {
                    if (res.code === 200 && res.data?.url) {
                        const imageUrl = res.data.url;
                        // 检查URL是否是完整的http(s)地址
                        const fullUrl = imageUrl.startsWith('http') ? imageUrl : (window.location.origin + imageUrl);
                        
                        form.value.popup_image_url = fullUrl; // 使用完整URL
                        ElementPlus.ElMessage.success('上传弹窗图片成功');
                        save();
                    } else {
                        form.value.popup_image_url = '';
                        ElementPlus.ElMessage.error(res.msg || '上传失败');
                    }
                };

                // 图片上传错误
                const onUploadError = (err) => {
                    console.error('上传错误:', err);
                    ElementPlus.ElMessage.error('上传失败：' + (err.message || '未知错误'));
                    form.value.image_url = '';
                };

                // 上传前验证
                const beforeUpload = (file) => {
                    const isImage = /^image\/(jpeg|png|gif)$/.test(file.type);
                    if (!isImage) {
                        showMessage('error', '只能上传jpg/png/gif格式的图片');
                        return false;
                    }

                    const isLt2M = file.size / 1024 / 1024 < 2;
                    if (!isLt2M) {
                        showMessage('error', '图片大小不能超过2MB');
                        return false;
                    }

                    return true; // 直接返回true，移除尺寸验证
                };

                // 预览图片
                const previewImage = () => {
                    if (form.value.image_url) {
                        ElementPlus.ElMessageBox.alert(
                            `<img src="${form.value.image_url}" style="max-width: 100%;">`,
                            '图片预览',
                            {
                                dangerouslyUseHTMLString: true,
                                showClose: true,
                                closeOnClickModal: true,
                                closeOnPressEscape: true,
                            }
                        );
                    }
                };

                // 预览弹窗图片
                const previewPopupImage = () => {
                    if (form.value.popup_image_url) {
                        ElementPlus.ElMessageBox.alert(
                            `<img src="${form.value.popup_image_url}" style="max-width: 100%;">`,
                            '弹窗图片预览',
                            {
                                dangerouslyUseHTMLString: true,
                                showClose: true,
                                closeOnClickModal: true,
                                closeOnPressEscape: true,
                            }
                        );
                    }
                };

                // 修改确认框方法
                const showConfirm = (message, title) => {
                    return ElementPlus.ElMessageBox.confirm(
                        message,
                        title,
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning',
                        }
                    );
                };

                // 删除图片
                const removeImage = (type = 'main') => {
                    const title = type === 'popup' ? '弹窗图片' : '悬浮框图片';
                    ElementPlus.ElMessageBox.confirm(
                        `确定要删除${title}吗？`,
                        '提示',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning',
                        }
                    ).then(async () => {
                        try {
                            const res = await axios.post('deleteImage', {
                                type: type
                            });
                            if (res.data?.code === 200) {
                                if (type === 'popup') {
                                    form.value.popup_image_url = '';
                                } else {
                                    form.value.image_url = '';
                                }
                                ElementPlus.ElMessage.success('删除成功');
                                save(); // 删除后保存配置
                            } else {
                                ElementPlus.ElMessage.error(res.data?.msg || '删除失败');
                            }
                        } catch (error) {
                            console.error('删除失败:', error);
                            ElementPlus.ElMessage.error('删除失败，请稍后重试');
                        }
                    }).catch(() => {});
                };

                // 修改消息提示方法
                const showMessage = (type, message) => {
                    ElementPlus.ElMessage({
                        type: type,
                        message: message
                    });
                };

                // 更新尺寸限制函数
                const updateSizeLimit = () => {
                    // 确保最小尺寸在有效范围内
                    form.value.min_size = Math.min(Math.max(form.value.min_size, 50), 300);
                    
                    // 确保最大尺寸在有效范围内
                    form.value.max_size = Math.min(Math.max(form.value.max_size, 300), 800);
                    
                    // 确保最大尺寸大于最小尺寸
                    if (form.value.max_size <= form.value.min_size) {
                        form.value.max_size = form.value.min_size + 100;
                    }
                    
                    // 确保当前宽高在新的范围内
                    form.value.width = Math.min(Math.max(form.value.width, form.value.min_size), form.value.max_size);
                    form.value.height = Math.min(Math.max(form.value.height, form.value.min_size), form.value.max_size);
                };

                // 监听尺寸变化
                watch(() => form.value.min_size, updateSizeLimit);
                watch(() => form.value.max_size, updateSizeLimit);

                // 初始化数据
                nextTick(() => {
                    fetchData();
                });

                // 返回数据和方法
                return {
                    loading,
                    form,
                    uploadHeaders: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Authorization': localStorage.getItem('token') || ''
                    },
                    handleUploadSuccess,
                    handlePopupUploadSuccess,
                    onUploadError,
                    beforeUpload,
                    previewImage,
                    previewPopupImage,
                    removeImage,
                    save
                };
            }
        });

        app.use(ElementPlus);
        app.mount('#app');
    </script>
</body>
</html>
