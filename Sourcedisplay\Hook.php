<?php

namespace plugin\Sourcedisplay;

use think\facade\Db;

class Hook {
    public function handle(&$js) {
        // 添加JS文件
        $js[] = plugstatic("Sourcedisplay", 'source.js');
    }

    public function getTopSources() {
        // 获取前三个最新刷新的货源
        return Db::name('goods_pool')
            ->field('id, title, user_id, refresh_time, create_time, tags, status')
            ->where('status', 1)
            ->order('refresh_time', 'desc')
            ->limit(3)
            ->select()
            ->toArray();
    }
} 