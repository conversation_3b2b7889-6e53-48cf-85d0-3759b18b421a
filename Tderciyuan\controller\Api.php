<?php

namespace plugin\Tderciyuan\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;

/**
 * 二次元Live2D API控制器
 */
class Api extends BasePlugin
{
    /**
     * 无需登录的方法，同时也就不需要鉴权了
     * @var array
     */
    protected $noNeedLogin = ['fetchData'];

    /**
     * 无需鉴权的方法,但需要登录
     * @var array
     */
    protected $noNeedRight = ['*'];

    /**
     * 添加场景配置
     * @var array
     */
    protected $scene = [
        'user'
    ];

    /**
     * API首页
     */
    public function index()
    {
        // 传递当前登录用户信息到模板
        View::assign([
            'merchant_id' => $this->user->id ?? 0,
            'shop_name' => $this->user->nickname ?? ''
        ]);
        return View::fetch();
    }

    /**
     * 获取Live2D配置数据
     */
    public function fetchData()
    {
        try {
            // 获取商家ID
            $merchant_id = $this->request->param('merchant_id', 0, 'intval');
            $shop_name = $this->request->param('shop_name', '', 'trim');
            
            // 如果没有merchant_id但有shop_name，通过shop_name查找merchant_id
            if (!$merchant_id && $shop_name) {
                try {
                    $user = \think\facade\Db::name('user')
                        ->where('nickname', $shop_name)
                        ->field('id')
                        ->find();

                    if ($user) {
                        $merchant_id = $user['id'];
                    }
                } catch (\Exception $e) {
                    \think\facade\Log::error('查找商家信息失败: ' . $e->getMessage());
                }
            }

            if (!$merchant_id) {
                // 如果没有商家信息，返回默认配置
                $data = [
                    'status' => intval(plugconf("Tderciyuan.status") ?? 1),
                    'model_type' => (string)(plugconf("Tderciyuan.model_type") ?? 'koharu'),
                    'custom_model_path' => (string)(plugconf("Tderciyuan.custom_model_path") ?? ''),
                    'position' => (string)(plugconf("Tderciyuan.position") ?? 'right'),
                    'width' => intval(plugconf("Tderciyuan.width") ?? 200),
                    'height' => intval(plugconf("Tderciyuan.height") ?? 300),
                    'merchant_can_edit' => intval(plugconf("Tderciyuan.merchant_can_edit") ?? 1)
                ];

                return json([
                    'code' => 200,
                    'msg' => 'success',
                    'data' => $data
                ]);
            }

            // 获取商家配置
            $merchant_status = merchant_plugconf($merchant_id, "Tderciyuan.status");
            
            // 获取商家权限设置 - 获取原始值然后明确转为整数
            $merchant_can_edit_raw = plugconf("Tderciyuan.merchant_can_edit");
            $merchant_can_edit = $merchant_can_edit_raw === null ? 1 : intval($merchant_can_edit_raw);
            
            // 如果商家未开启或未设置，则获取后台默认配置
            if ($merchant_status === null || intval($merchant_status) !== 1) {
                $data = [
                    'status' => intval(plugconf("Tderciyuan.status") ?? 1),
                    'model_type' => (string)(plugconf("Tderciyuan.model_type") ?? 'auto'),
                    'custom_model_path' => (string)(plugconf("Tderciyuan.custom_model_path") ?? ''),
                    'position' => (string)(plugconf("Tderciyuan.position") ?? 'right'),
                    'width' => intval(plugconf("Tderciyuan.width") ?? 200),
                    'height' => intval(plugconf("Tderciyuan.height") ?? 300),
                    'display_mode' => (string)(plugconf("Tderciyuan.display_mode") ?? 'normal'),
                    'merchant_can_edit' => $merchant_can_edit
                ];
            } else {
                // 使用商家自己的配置
                $data = [
                    'status' => intval($merchant_status),
                    'model_type' => (string)(merchant_plugconf($merchant_id, "Tderciyuan.model_type") ?? 'auto'),
                    'custom_model_path' => (string)(merchant_plugconf($merchant_id, "Tderciyuan.custom_model_path") ?? ''),
                    'position' => (string)(merchant_plugconf($merchant_id, "Tderciyuan.position") ?? 'right'),
                    'width' => intval(merchant_plugconf($merchant_id, "Tderciyuan.width") ?? 200),
                    'height' => intval(merchant_plugconf($merchant_id, "Tderciyuan.height") ?? 300),
                    'display_mode' => (string)(merchant_plugconf($merchant_id, "Tderciyuan.display_mode") ?? 'normal'),
                    'merchant_can_edit' => $merchant_can_edit
                ];
            }

            return json([
                'code' => 200,
                'msg' => 'success',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            \think\facade\Log::error('FetchData error: ' . $e->getMessage());
            return json(['code' => 0, 'msg' => '获取数据失败：' . $e->getMessage()]);
        }
    }

    public function save() {
        try {
            // 判断是否开启了商家修改权限
            $merchantCanEdit = plugconf("Tderciyuan.merchant_can_edit");
            $merchantCanEdit = $merchantCanEdit === null ? 1 : intval($merchantCanEdit);
            
            if ($merchantCanEdit !== 1) {
                return json(['code' => 403, 'msg' => '管理员已禁止商家修改看板娘设置']);
            }
            
            if (!$this->user || !$this->user->id) {
                return json(['code' => 403, 'msg' => '请先登录']);
            }

            $merchant_id = $this->user->id;
            
            // 获取并验证基础参数
            $params = $this->validateParams();
            if (!$params['success']) {
                return json(['code' => 0, 'msg' => $params['message']]);
            }

            // 保存配置
            $this->saveConfigurations($merchant_id, $params['data']);

            return json(['code' => 200, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            \think\facade\Log::error('Save error: ' . $e->getMessage());
            return json(['code' => 0, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    /**
     * 验证参数
     * @return array
     */
    private function validateParams() {
        // 判断是否开启了商家修改权限
        $merchantCanEdit = plugconf("Tderciyuan.merchant_can_edit");
        $merchantCanEdit = $merchantCanEdit === null ? 1 : intval($merchantCanEdit);
        
        if ($merchantCanEdit !== 1) {
            return ['success' => false, 'message' => '管理员已禁止商家修改看板娘设置'];
        }

        $params = [
            'status' => $this->request->post('status/d', 1),
            'model_type' => $this->request->post('model_type', 'auto', 'trim'),
            'custom_model_path' => $this->request->post('custom_model_path', '', 'trim'),
            'position' => $this->request->post('position', 'right', 'trim'),
            'width' => $this->request->post('width/d', 200),
            'height' => $this->request->post('height/d', 300),
            'display_mode' => $this->request->post('display_mode', 'normal', 'trim')
        ];

        // 验证必填项
        if (empty($params['model_type']) && $params['status'] == 1) {
            return ['success' => false, 'message' => '请选择模型类型'];
        }

        // 如果是自定义模型，需要验证模型路径
        if ($params['model_type'] === 'custom' && empty($params['custom_model_path'])) {
            return ['success' => false, 'message' => '请输入自定义模型路径'];
        }

        // 验证数值范围
        if (!$this->validateSizeRanges($params)) {
            return ['success' => false, 'message' => '参数范围验证失败'];
        }

        return ['success' => true, 'data' => $params];
    }

    /**
     * 验证尺寸范围
     * @param array $params
     * @return bool
     */
    private function validateSizeRanges($params) {
        $validations = [
            ['value' => $params['width'], 'min' => 100, 'max' => 400, 'name' => '宽度'],
            ['value' => $params['height'], 'min' => 100, 'max' => 600, 'name' => '高度']
        ];

        foreach ($validations as $validation) {
            if ($validation['value'] < $validation['min'] || $validation['value'] > $validation['max']) {
                return false;
            }
        }

        return true;
    }

    /**
     * 保存配置
     * @param int $merchant_id
     * @param array $data
     */
    private function saveConfigurations($merchant_id, $data) {
        merchant_plugconf($merchant_id, "Tderciyuan.status", $data['status']);
        merchant_plugconf($merchant_id, "Tderciyuan.model_type", $data['model_type']);
        merchant_plugconf($merchant_id, "Tderciyuan.custom_model_path", $data['custom_model_path']);
        merchant_plugconf($merchant_id, "Tderciyuan.position", $data['position']);
        merchant_plugconf($merchant_id, "Tderciyuan.width", $data['width']);
        merchant_plugconf($merchant_id, "Tderciyuan.height", $data['height']);
        merchant_plugconf($merchant_id, "Tderciyuan.display_mode", $data['display_mode']);
    }

    /**
     * 删除模型文件
     * @param string $modelUrl 模型URL
     * @return bool
     */
    private function deleteModelFile($modelUrl) {
        try {
            if (empty($modelUrl)) {
                return false;
            }

            // 将URL转换为服务器路径
            $filePath = public_path() . ltrim($modelUrl, '/');
            
            // 检查文件是否存在且在允许的目录内
            if (strpos($filePath, public_path() . 'upload/tderciyuan/') !== 0) {
                \think\facade\Log::error('Attempted to delete file outside of allowed directory: ' . $filePath);
                return false;
            }

            if (file_exists($filePath)) {
                if (@unlink($filePath)) {
                    \think\facade\Log::info('Model file deleted successfully: ' . $filePath);
                    
                    // 尝试删除空文件夹
                    $dir = dirname($filePath);
                    if (is_dir($dir) && count(scandir($dir)) <= 2) { // 只有 . 和 .. 时删除目录
                        @rmdir($dir);
                        \think\facade\Log::info('Empty directory removed: ' . $dir);
                    }
                    
                    return true;
                } else {
                    \think\facade\Log::error('Failed to delete model file: ' . $filePath);
                    return false;
                }
            }
            
            return false;
        } catch (\Exception $e) {
            \think\facade\Log::error('Delete model file error: ' . $e->getMessage());
            return false;
        }
    }

    public function upload() {
        try {
            // 检查用户登录状态
            if (!$this->user || !$this->user->id) {
                return json(['code' => 403, 'msg' => '请先登录']);
            }

            $file = $this->request->file('file');
            if (!$file) {
                return json(['code' => 0, 'msg' => '请选择要上传的文件']);
            }

            // 验证文件
            validate([
                'file' => [
                    'fileSize' => 2097152,
                    'fileExt' => 'json,png,jpg,jpeg,gif'
                ]
            ])->check(['file' => $file]);

            // 直接转发到商户端统一上传接口
            $response = $this->forwardToUploadApi($file);
            return json($response);

        } catch (\think\exception\ValidateException $e) {
            return json(['code' => 0, 'msg' => $e->getMessage()]);
        } catch (\Exception $e) {
            \think\facade\Log::error('Upload error: ' . $e->getMessage());
            return json(['code' => 0, 'msg' => '上传失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 转发到上传API
     * @param $file
     * @return array
     */
    private function forwardToUploadApi($file) {
        try {
            $savename = \think\facade\Filesystem::disk('public')->putFile('upload/tderciyuan', $file);
            $savename = str_replace('\\', '/', $savename);
            
            // 获取上传后的URL
            $url = '/storage/' . $savename;
            
            return [
                'code' => 200,
                'msg' => '上传成功',
                'data' => [
                    'url' => $url,
                    'path' => $savename
                ]
            ];
        } catch (\Exception $e) {
            \think\facade\Log::error('Forward upload error: ' . $e->getMessage());
            return [
                'code' => 0,
                'msg' => '上传处理失败：' . $e->getMessage()
            ];
        }
    }
} 