<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>HTML弹窗内置模板管理</title>
    <style>
        /* 响应式设计增强 */
        @media screen and (max-width: 768px) {
            body {
                padding: 8px;
                font-size: 14px;
            }
            .el-card {
                border-radius: 8px;
            }
            .card-header span {
                font-size: 16px;
            }
            .el-form-item__label {
                font-size: 13px;
            }
        }

        /* 自适应间距 */
        .el-form-item {
            margin-bottom: clamp(20px, 4vw, 28px);
        }

        /* 优化触摸区域 */
        .el-button {
            min-height: 44px;
        }

        /* 模板列表样式 */
        .template-item {
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            background: #fafafa;
        }

        .template-item:hover {
            background: #f0f9eb;
            border-color: #67c23a;
        }

        .template-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .template-name {
            font-weight: bold;
            color: #303133;
            font-size: 16px;
        }

        .template-key {
            color: #909399;
            font-size: 12px;
            background: #f4f4f5;
            padding: 2px 8px;
            border-radius: 4px;
        }

        .template-content {
            max-height: 200px;
            overflow-y: auto;
            background: #f8f9fa;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            padding: 12px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.5;
            white-space: pre-wrap;
            word-break: break-word;
            color: #2c3e50;
            position: relative;
        }

        .template-content::before {
            content: 'HTML';
            position: absolute;
            top: 4px;
            right: 8px;
            font-size: 10px;
            color: #909399;
            background: #e4e7ed;
            padding: 2px 6px;
            border-radius: 2px;
        }

        .template-actions {
            margin-top: 12px;
            text-align: right;
        }

        /* 编辑表单样式 */
        .edit-form {
            background: #f0f9eb;
            border: 1px solid #67c23a;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .edit-form .el-form-item__label {
            color: #67c23a;
            font-weight: bold;
        }

        /* 新增模板按钮 */
        .add-template-btn {
            width: 100%;
            height: 60px;
            border: 2px dashed #409eff;
            background: #ecf5ff;
            color: #409eff;
            font-size: 16px;
            margin-bottom: 20px;
        }

        .add-template-btn:hover {
            border-color: #66b1ff;
            background: #d9ecff;
        }

        /* 暗黑模式支持 */
        @media (prefers-color-scheme: dark) {
            body {
                background: #1a1a1a;
            }
            .el-card {
                background: #2b2b2b;
                border-color: #3a3a3a;
            }
            .template-item {
                background: #333;
                border-color: #444;
            }
            .template-content {
                background: #2b2b2b;
                border-color: #444;
                color: #e4e7ed;
            }
        }
    </style>
</head>

<body>
    <div id="app">
        <el-card shadow="never">
            <template #header>
                <div class="card-header">
                    <span>HTML弹窗内置模板管理</span>
                    <div style="margin-top: 8px; color: #909399; font-size: 12px;">
                        <i class="el-icon-info"></i> 管理HTML弹窗插件的内置模板，商家可以选择使用这些预设模板
                    </div>
                </div>
            </template>

            <!-- 全局控制开关 -->
            <div style="margin-bottom: 20px; padding: 16px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #409eff;">
                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 12px;">
                    <div>
                        <span style="font-weight: 600; color: #2c3e50;">弹窗总开关</span>
                        <div style="color: #909399; font-size: 12px; margin-top: 4px;">
                            控制整个弹窗系统的启用状态，关闭后所有商家的弹窗都不会显示
                        </div>
                    </div>
                    <el-switch
                        v-model="globalSettings.popupEnabled"
                        @change="saveGlobalSettings"
                        :loading="globalLoading"
                    ></el-switch>
                </div>


            </div>

            <!-- 弹窗全局配置 -->
            <div style="margin-bottom: 20px; padding: 16px; background: #f0f9eb; border-radius: 8px; border-left: 4px solid #67c23a;">
                <div style="margin-bottom: 16px;">
                    <span style="font-weight: 600; color: #2c3e50; font-size: 16px;">弹窗全局配置</span>
                    <div style="color: #909399; font-size: 12px; margin-top: 4px;">
                        <i class="el-icon-info"></i> 配置所有弹窗的默认行为和样式
                    </div>
                </div>

                <el-form :model="popupConfig" label-width="120px">
                    <el-form-item label="弹窗标题：">
                        <el-input
                            v-model="popupConfig.title"
                            placeholder="自定义弹窗标题，默认为'HTML弹窗'"
                            maxlength="20"
                            show-word-limit
                            style="max-width: 400px;"
                        />
                    </el-form-item>

                    <el-form-item label="弹出频率：">
                        <el-radio-group v-model="popupConfig.frequency">
                            <el-radio label="once">仅弹一次</el-radio>
                            <el-radio label="login">每次访问</el-radio>
                            <el-radio label="daily">每天一次</el-radio>
                            <el-radio label="weekly">每周一次</el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <el-form-item label="默认按钮颜色：">
                        <el-color-picker v-model="popupConfig.buttonColor" show-alpha></el-color-picker>
                    </el-form-item>

                    <el-form-item label="拒绝后操作：">
                        <el-radio-group v-model="popupConfig.rejectAction">
                            <el-radio label="none">无操作</el-radio>
                            <el-radio label="redirect">跳转到指定页面</el-radio>
                            <el-radio label="close">关闭当前网页</el-radio>
                        </el-radio-group>
                        <div style="margin-top: 8px; color: #909399; font-size: 12px;">
                            <i class="el-icon-info"></i> 设置用户点击"拒绝"按钮后的操作
                        </div>
                    </el-form-item>

                    <el-form-item label="跳转地址：" v-if="popupConfig.rejectAction === 'redirect'">
                        <el-input
                            v-model="popupConfig.rejectUrl"
                            placeholder="请输入跳转的URL地址，如：https://www.example.com"
                            style="max-width: 500px;"
                        ></el-input>
                        <div style="margin-top: 4px; color: #67c23a; font-size: 11px;">
                            💡 提示：可以是完整URL或相对路径，如 /page.html
                        </div>
                    </el-form-item>

                    <el-form-item label="关闭确认：" v-if="popupConfig.rejectAction === 'close'">
                        <el-switch v-model="popupConfig.closeConfirm"></el-switch>
                        <span style="margin-left: 10px; color: #606266; font-size: 13px;">
                            开启后会弹出确认对话框
                        </span>
                        <div style="margin-top: 4px; color: #e6a23c; font-size: 11px;">
                            ⚠️ 注意：关闭网页功能可能被浏览器阻止
                        </div>
                    </el-form-item>

                    <el-form-item label="默认内置模板：">
                        <el-select
                            v-model="popupConfig.defaultTemplate"
                            placeholder="选择默认内置模板"
                            style="width: 300px;"
                        >
                            <el-option
                                v-for="(template, key) in templates"
                                :key="key"
                                :label="template.name"
                                :value="key"
                            ></el-option>
                        </el-select>
                        <div style="margin-top: 8px; color: #909399; font-size: 12px;">
                            <i class="el-icon-info"></i> 当商家未选择模板时，系统将自动使用此默认模板
                        </div>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="savePopupConfig" :loading="configLoading">
                            保存配置
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 新增模板按钮 -->
            <el-button
                class="add-template-btn"
                @click="showAddForm = true"
                v-if="!showAddForm && !editingTemplate"
            >
                <i class="el-icon-plus"></i> 添加新的内置模板
            </el-button>

            <!-- 新增/编辑模板表单 -->
            <div v-if="showAddForm || editingTemplate" class="edit-form">
                <h3 style="color: #67c23a; margin-bottom: 20px;">
                    {{ editingTemplate ? '编辑模板' : '添加新模板' }}
                </h3>

                <el-form :model="templateForm" label-width="100px">
                    <el-form-item label="模板标识" required>
                        <el-input
                            v-model="templateForm.key"
                            placeholder="请输入模板标识，如：custom_template_1"
                            :disabled="!!editingTemplate"
                        />
                        <div style="margin-top: 4px; color: #909399; font-size: 12px;">
                            模板的唯一标识符，只能包含字母、数字和下划线
                        </div>
                    </el-form-item>

                    <el-form-item label="模板名称" required>
                        <el-input
                            v-model="templateForm.name"
                            placeholder="请输入模板显示名称"
                        />
                    </el-form-item>

                    <el-form-item label="模板内容" required>
                        <div style="margin-bottom: 10px;">
                            <el-button size="small" type="info" @click="showTemplateHelp = !showTemplateHelp">
                                {{ showTemplateHelp ? '隐藏' : '显示' }}模板制作指南
                            </el-button>
                            <el-button size="small" type="success" @click="insertBasicTemplate" style="margin-left: 8px;">
                                插入基础框架
                            </el-button>
                            <el-button size="small" type="warning" @click="insertCardTemplate" style="margin-left: 8px;">
                                插入卡片框架
                            </el-button>
                        </div>

                        <!-- 模板制作指南 -->
                        <div v-if="showTemplateHelp" style="background: #f0f9eb; border: 1px solid #67c23a; border-radius: 6px; padding: 15px; margin-bottom: 15px;">
                            <h4 style="color: #67c23a; margin: 0 0 12px 0; font-size: 14px;">📝 模板制作指南</h4>
                            <div style="color: #5a6c7d; font-size: 12px; line-height: 1.6;">
                                <p style="margin: 0 0 8px 0;"><strong>基础结构：</strong></p>
                                <code style="background: #f8f9fa; padding: 2px 4px; border-radius: 3px; font-size: 11px;">
                                    &lt;div style="max-width: 500px; margin: 0 auto;"&gt;内容&lt;/div&gt;
                                </code>

                                <p style="margin: 12px 0 8px 0;"><strong>常用样式：</strong></p>
                                <ul style="margin: 0; padding-left: 16px;">
                                    <li>背景渐变：<code style="background: #f8f9fa; padding: 1px 3px; font-size: 11px;">background: linear-gradient(135deg, #667eea 0%, #764ba2 100%)</code></li>
                                    <li>圆角边框：<code style="background: #f8f9fa; padding: 1px 3px; font-size: 11px;">border-radius: 12px</code></li>
                                    <li>阴影效果：<code style="background: #f8f9fa; padding: 1px 3px; font-size: 11px;">box-shadow: 0 4px 20px rgba(0,0,0,0.1)</code></li>
                                    <li>文字居中：<code style="background: #f8f9fa; padding: 1px 3px; font-size: 11px;">text-align: center</code></li>
                                </ul>

                                <p style="margin: 12px 0 8px 0;"><strong>推荐字体：</strong></p>
                                <code style="background: #f8f9fa; padding: 2px 4px; border-radius: 3px; font-size: 11px;">
                                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif
                                </code>

                                <p style="margin: 12px 0 4px 0;"><strong>💡 提示：</strong>参考现有模板样式，复制修改更简单！</p>
                            </div>
                        </div>

                        <el-input
                            v-model="templateForm.content"
                            type="textarea"
                            :rows="15"
                            placeholder="请输入HTML模板内容，支持所有HTML标签和CSS样式...&#10;&#10;示例框架：&#10;&lt;div style=&quot;max-width: 500px; margin: 0 auto; background: white; border-radius: 12px; padding: 25px; box-shadow: 0 4px 20px rgba(0,0,0,0.1);&quot;&gt;&#10;    &lt;h2 style=&quot;text-align: center; color: #2c3e50; margin-bottom: 20px;&quot;&gt;标题&lt;/h2&gt;&#10;    &lt;p style=&quot;color: #5a6c7d; line-height: 1.6;&quot;&gt;内容文本&lt;/p&gt;&#10;&lt;/div&gt;"
                            style="font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 13px;"
                        />
                        <div style="margin-top: 8px; color: #909399; font-size: 12px;">
                            <i class="el-icon-info"></i> 支持完整HTML代码，包括复杂布局、样式和结构，但不支持JavaScript
                        </div>
                    </el-form-item>

                    <el-form-item>
                        <el-button
                            type="primary"
                            :loading="loading"
                            @click="saveTemplate"
                        >
                            {{ editingTemplate ? '更新模板' : '保存模板' }}
                        </el-button>
                        <el-button @click="cancelEdit">取消</el-button>
                        <el-button
                            type="info"
                            @click="previewTemplate"
                            v-if="templateForm.content"
                        >
                            预览模板
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 模板列表 -->
            <div v-if="!showAddForm && !editingTemplate">
                <h3 style="margin-bottom: 20px; color: #303133;">
                    现有模板 ({{ Object.keys(templates).length }})
                </h3>

                <div v-if="Object.keys(templates).length === 0" style="text-align: center; padding: 40px; color: #909399;">
                    <i class="el-icon-document" style="font-size: 48px; margin-bottom: 16px;"></i>
                    <p>暂无内置模板</p>
                    <p style="font-size: 12px;">点击上方按钮添加第一个模板</p>
                </div>

                <div v-for="(template, key) in templates" :key="key" class="template-item">
                    <div class="template-header">
                        <div>
                            <div class="template-name">{{ template.name || '未命名模板' }}</div>
                            <div class="template-key">{{ key }}</div>
                        </div>
                        <div class="template-actions">
                            <el-button size="small" type="primary" @click="editTemplate(key, template)">
                                编辑
                            </el-button>
                            <el-button size="small" type="info" @click="previewTemplateContent(template.content)" :disabled="!template.content">
                                预览
                            </el-button>
                            <el-button size="small" type="danger" @click="confirmDelete(key, template.name || '未命名模板')">
                                删除
                            </el-button>
                        </div>
                    </div>
                    <div class="template-content">
                        <div v-if="!template.content" style="color: #f56c6c; font-style: italic;">
                            ⚠️ 模板内容为空
                        </div>
                        <div v-else-if="typeof template.content !== 'string'" style="color: #f56c6c; font-style: italic;">
                            ⚠️ 模板内容格式错误 (类型: {{ typeof template.content }})
                        </div>
                        <div v-else>{{ formatHtmlForDisplay(template.content) }}</div>
                    </div>
                </div>
            </div>

            <!-- 预览对话框 -->
            <el-dialog
                v-model="showPreview"
                title="模板预览"
                width="80%"
                :close-on-click-modal="false"
            >
                <div style="border: 1px solid #dcdfe6; border-radius: 4px; background: #fff; min-height: 200px;">
                    <div v-if="!previewContent" style="padding: 40px; text-align: center; color: #909399;">
                        <i class="el-icon-warning" style="font-size: 48px; margin-bottom: 16px;"></i>
                        <p>预览内容为空</p>
                    </div>
                    <div v-else style="padding: 20px;">
                        <div v-html="previewContent"></div>
                    </div>
                </div>
                <template #footer>
                    <div style="text-align: center;">
                        <el-button @click="showPreview = false">关闭预览</el-button>
                        <el-button type="info" @click="copyPreviewContent" v-if="previewContent">
                            复制HTML代码
                        </el-button>
                    </div>
                </template>
            </el-dialog>
        </el-card>
    </div>

    <!-- 引入依赖库 -->
    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>

    <script>
        // 全局错误处理
        window.addEventListener('unhandledrejection', function(event) {
            event.preventDefault();
        });

        const { createApp, ref, reactive, nextTick } = Vue;
        const app = createApp({
            setup() {
                // 状态定义
                const loading = ref(false);
                const showAddForm = ref(false);
                const editingTemplate = ref(null);
                const showPreview = ref(false);
                const previewContent = ref('');
                const templates = ref({});
                const showTemplateHelp = ref(false);
                const globalLoading = ref(false);
                const configLoading = ref(false);

                // 全局设置
                const globalSettings = reactive({
                    popupEnabled: true
                });

                // 弹窗配置
                const popupConfig = reactive({
                    frequency: 'once',
                    title: 'HTML弹窗',
                    buttonColor: '#409eff',
                    rejectAction: 'none',
                    rejectUrl: '',
                    closeConfirm: false,
                    defaultTemplate: 'purchase_agreement'
                });

                const templateForm = reactive({
                    key: '',
                    name: '',
                    content: ''
                });

                // 获取模板列表
                const fetchTemplates = async () => {
                    try {
                        loading.value = true;
                        const res = await axios.post('getTemplates');
                        if (res.data?.code === 200) {
                            const templateData = res.data.data || {};

                            // 验证和修复模板数据
                            const validatedTemplates = {};
                            for (const [key, template] of Object.entries(templateData)) {
                                if (template && typeof template === 'object') {
                                    // 确保模板有必要的字段
                                    validatedTemplates[key] = {
                                        name: template.name || '未命名模板',
                                        content: typeof template.content === 'string' ? template.content : ''
                                    };

                                    // 如果内容为空，跳过
                                    if (!validatedTemplates[key].content) {
                                        delete validatedTemplates[key];
                                    }
                                } else {
                                    // 数据格式不正确，跳过
                                }
                            }

                            templates.value = validatedTemplates;
                        } else {
                            ElementPlus.ElMessage.error(res.data?.msg || '获取模板列表失败');
                        }
                    } catch (error) {
                        ElementPlus.ElMessage.error('获取模板列表失败，请刷新页面重试');
                    } finally {
                        loading.value = false;
                    }
                };

                // 保存模板
                const saveTemplate = async () => {
                    try {
                        // 验证表单
                        if (!templateForm.key || !templateForm.name || !templateForm.content) {
                            ElementPlus.ElMessage.error('请填写完整的模板信息');
                            return;
                        }

                        // 验证模板标识格式
                        if (!/^[a-zA-Z0-9_]+$/.test(templateForm.key)) {
                            ElementPlus.ElMessage.error('模板标识只能包含字母、数字和下划线');
                            return;
                        }

                        // 检查是否重复（新增时）
                        if (!editingTemplate.value && templates.value[templateForm.key]) {
                            ElementPlus.ElMessage.error('模板标识已存在，请使用其他标识');
                            return;
                        }

                        loading.value = true;
                        const res = await axios.post('saveTemplate', {
                            templateKey: templateForm.key,
                            templateName: templateForm.name,
                            templateContent: templateForm.content
                        }, {
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });

                        if (res.data?.code === 200) {
                            ElementPlus.ElMessage.success(res.data.msg || '保存成功');
                            templates.value = res.data.data || {};
                            cancelEdit();
                        } else {
                            ElementPlus.ElMessage.error(res.data?.msg || '保存失败');
                        }
                    } catch (error) {
                        ElementPlus.ElMessage.error('保存失败，请稍后重试');
                    } finally {
                        loading.value = false;
                    }
                };

                // 编辑模板
                const editTemplate = (key, template) => {
                    editingTemplate.value = key;
                    templateForm.key = key;
                    templateForm.name = template.name;
                    templateForm.content = template.content;
                };

                // 取消编辑
                const cancelEdit = () => {
                    showAddForm.value = false;
                    editingTemplate.value = null;
                    templateForm.key = '';
                    templateForm.name = '';
                    templateForm.content = '';
                };

                // 预览模板
                const previewTemplate = () => {
                    if (!templateForm.content) {
                        ElementPlus.ElMessage.warning('请先输入模板内容');
                        return;
                    }
                    previewContent.value = templateForm.content;
                    showPreview.value = true;
                };

                // 预览模板内容
                const previewTemplateContent = (content) => {
                    if (!content || content.trim() === '') {
                        ElementPlus.ElMessage.warning('模板内容为空，无法预览');
                        return;
                    }

                    // 检查内容是否是有效的HTML
                    if (content.length < 10 || (!content.includes('<') && !content.includes('>'))) {
                        ElementPlus.ElMessage.warning('模板内容格式不正确，请检查HTML代码');
                        return;
                    }

                    previewContent.value = content;
                    showPreview.value = true;
                };

                // 复制预览内容
                const copyPreviewContent = async () => {
                    try {
                        await navigator.clipboard.writeText(previewContent.value);
                        ElementPlus.ElMessage.success('HTML代码已复制到剪贴板');
                    } catch (error) {
                        // 降级方案
                        const textArea = document.createElement('textarea');
                        textArea.value = previewContent.value;
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);
                        ElementPlus.ElMessage.success('HTML代码已复制到剪贴板');
                    }
                };

                // 确认删除
                const confirmDelete = (key, name) => {
                    ElementPlus.ElMessageBox.confirm(
                        `确定要删除模板 "${name}" 吗？此操作不可恢复。`,
                        '确认删除',
                        {
                            confirmButtonText: '确定删除',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    ).then(() => {
                        deleteTemplate(key);
                    }).catch(() => {});
                };

                // 删除模板
                const deleteTemplate = async (key) => {
                    try {
                        loading.value = true;
                        const res = await axios.post('deleteTemplate', {
                            templateKey: key
                        });

                        if (res.data?.code === 200) {
                            ElementPlus.ElMessage.success(res.data.msg || '删除成功');
                            templates.value = res.data.data || {};
                        } else {
                            ElementPlus.ElMessage.error(res.data?.msg || '删除失败');
                        }
                    } catch (error) {
                        ElementPlus.ElMessage.error('删除失败，请稍后重试');
                    } finally {
                        loading.value = false;
                    }
                };

                // 格式化HTML代码显示
                const formatHtmlForDisplay = (html) => {
                    if (!html) return '暂无内容';

                    // 确保html是字符串类型
                    const htmlStr = String(html);

                    // 如果内容太短，可能是错误信息，直接返回
                    if (htmlStr.length < 10) {
                        return htmlStr;
                    }

                    // 限制显示长度，避免过长的代码影响界面
                    if (htmlStr.length > 500) {
                        return htmlStr.substring(0, 500) + '\n\n... (内容已截断，点击预览查看完整内容)';
                    }

                    return htmlStr;
                };

                // 插入基础模板框架
                const insertBasicTemplate = () => {
                    // 检查是否正在编辑模板，如果是则询问用户
                    if (templateForm.content && templateForm.content.trim() !== '') {
                        ElementPlus.ElMessageBox.confirm(
                            '当前已有内容，插入模板将覆盖现有内容，是否继续？',
                            '确认操作',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning'
                            }
                        ).then(() => {
                            doInsertBasicTemplate();
                        }).catch(() => {
                            // 用户取消，不执行任何操作
                        });
                    } else {
                        doInsertBasicTemplate();
                    }
                };

                const doInsertBasicTemplate = () => {
                    const basicTemplate = `<div style="max-width: 500px; margin: 0 auto; background: white; border-radius: 12px; padding: 25px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
    <!-- 标题区域 -->
    <h2 style="text-align: center; color: #2c3e50; margin: 0 0 20px 0; font-size: 20px; font-weight: 600;">
        📢 这里是标题
    </h2>

    <!-- 内容区域 -->
    <div style="color: #5a6c7d; line-height: 1.6; margin-bottom: 20px;">
        <p style="margin: 0 0 12px 0;">这里是主要内容文本，您可以根据需要修改。</p>
        <p style="margin: 0;">支持多段落内容，保持良好的阅读体验。</p>
    </div>

    <!-- 提示框 -->
    <div style="background: #f0f9eb; border-left: 4px solid #67c23a; padding: 15px; border-radius: 0 6px 6px 0; margin: 15px 0;">
        <div style="color: #67c23a; font-weight: 600; margin-bottom: 8px;">💡 温馨提示</div>
        <div style="color: #495057; font-size: 14px;">
            这里可以放置重要提示信息
        </div>
    </div>

    <!-- 底部信息 -->
    <div style="text-align: center; margin-top: 20px; padding-top: 15px; border-top: 1px solid #e9ecef; color: #6c757d; font-size: 13px;">
        底部说明文字
    </div>
</div>`;
                    templateForm.content = basicTemplate;
                    ElementPlus.ElMessage.success('基础框架已插入，请根据需要修改内容');
                };

                // 插入卡片模板框架
                const insertCardTemplate = () => {
                    // 检查是否正在编辑模板，如果是则询问用户
                    if (templateForm.content && templateForm.content.trim() !== '') {
                        ElementPlus.ElMessageBox.confirm(
                            '当前已有内容，插入模板将覆盖现有内容，是否继续？',
                            '确认操作',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning'
                            }
                        ).then(() => {
                            doInsertCardTemplate();
                        }).catch(() => {
                            // 用户取消，不执行任何操作
                        });
                    } else {
                        doInsertCardTemplate();
                    }
                };

                const doInsertCardTemplate = () => {
                    const cardTemplate = `<div style="max-width: 450px; margin: 0 auto; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 16px; overflow: hidden; box-shadow: 0 8px 32px rgba(0,0,0,0.15); font-family: -apple-system, BlinkMacSystemFont, sans-serif;">
    <!-- 头部区域 -->
    <div style="background: rgba(255,255,255,0.1); color: white; padding: 25px 20px; text-align: center;">
        <div style="font-size: 32px; margin-bottom: 10px;">🎉</div>
        <h2 style="margin: 0 0 8px 0; font-size: 22px; font-weight: 600;">卡片标题</h2>
        <p style="margin: 0; font-size: 14px; opacity: 0.9;">副标题或简短描述</p>
    </div>

    <!-- 内容区域 -->
    <div style="background: white; padding: 25px 20px;">
        <div style="margin-bottom: 20px;">
            <h3 style="color: #2c3e50; margin: 0 0 12px 0; font-size: 16px;">📋 主要内容</h3>
            <p style="color: #5a6c7d; line-height: 1.6; margin: 0;">
                这里是卡片的主要内容区域，您可以添加任何需要展示的信息。
            </p>
        </div>

        <!-- 特色区域 -->
        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff;">
            <div style="color: #007bff; font-weight: 600; margin-bottom: 8px;">✨ 特色功能</div>
            <ul style="color: #495057; margin: 0; padding-left: 18px; line-height: 1.6;">
                <li>功能特点一</li>
                <li>功能特点二</li>
                <li>功能特点三</li>
            </ul>
        </div>

        <!-- 底部按钮区域 -->
        <div style="text-align: center; margin-top: 20px;">
            <div style="background: linear-gradient(90deg, #ff6b6b, #feca57); color: white; padding: 12px 24px; border-radius: 25px; display: inline-block; font-weight: 600; font-size: 14px;">
                立即体验
            </div>
        </div>
    </div>
</div>`;
                    templateForm.content = cardTemplate;
                    ElementPlus.ElMessage.success('卡片框架已插入，请根据需要修改内容');
                };



                // 获取全局设置
                const fetchGlobalSettings = async () => {
                    try {
                        const res = await axios.get('getGlobalSettings');
                        if (res.data?.code === 200) {
                            globalSettings.popupEnabled = res.data.data.popupEnabled ?? true;
                        }
                    } catch (error) {
                        // 获取全局设置失败，使用默认值
                    }
                };

                // 保存全局设置
                const saveGlobalSettings = async () => {
                    globalLoading.value = true;
                    try {
                        const res = await axios.post('saveGlobalSettings', {
                            popupEnabled: globalSettings.popupEnabled
                        });
                        if (res.data?.code === 200) {
                            ElementPlus.ElMessage.success('全局设置保存成功');
                        } else {
                            ElementPlus.ElMessage.error(res.data?.msg || '保存失败');
                        }
                    } catch (error) {
                        ElementPlus.ElMessage.error('保存失败');
                    } finally {
                        globalLoading.value = false;
                    }
                };

                // 获取弹窗配置
                const fetchPopupConfig = async () => {
                    try {
                        const res = await axios.get('getPopupConfig');
                        if (res.data?.code === 200) {
                            const data = res.data.data;
                            popupConfig.frequency = data.frequency || 'once';
                            popupConfig.title = data.title || 'HTML弹窗';

                            // 将预设颜色名转换为对应的十六进制值
                            const colorMap = {
                                'blue': '#409eff',
                                'red': '#F56C6C',
                                'green': '#67C23A',
                                'orange': '#E6A23C',
                                'teal': '#009688',
                                'purple': '#9C27B0',
                                'gray': '#909399'
                            };
                            const buttonColor = data.button_color || 'blue';
                            popupConfig.buttonColor = colorMap[buttonColor] || buttonColor;

                            popupConfig.rejectAction = data.reject_action || 'none';
                            popupConfig.rejectUrl = data.reject_url || '';
                            popupConfig.closeConfirm = data.close_confirm === 1 || data.close_confirm === true;
                            popupConfig.defaultTemplate = data.default_template || 'purchase_agreement';
                        }
                    } catch (error) {
                        // 获取弹窗配置失败，使用默认值
                    }
                };

                // 保存弹窗配置
                const savePopupConfig = async () => {
                    configLoading.value = true;
                    try {
                        const res = await axios.post('savePopupConfig', {
                            frequency: popupConfig.frequency,
                            title: popupConfig.title,
                            button_color: popupConfig.buttonColor,
                            reject_action: popupConfig.rejectAction,
                            reject_url: popupConfig.rejectUrl,
                            close_confirm: popupConfig.closeConfirm ? 1 : 0,
                            default_template: popupConfig.defaultTemplate
                        });
                        if (res.data?.code === 200) {
                            ElementPlus.ElMessage.success('弹窗配置保存成功');
                        } else {
                            ElementPlus.ElMessage.error(res.data?.msg || '保存失败');
                        }
                    } catch (error) {
                        ElementPlus.ElMessage.error('保存失败');
                    } finally {
                        configLoading.value = false;
                    }
                };

                // 初始化数据
                nextTick(() => {
                    fetchTemplates();
                    fetchGlobalSettings();
                    fetchPopupConfig();
                });

                // 返回数据和方法
                return {
                    loading,
                    showAddForm,
                    editingTemplate,
                    showPreview,
                    previewContent,
                    templates,
                    templateForm,
                    showTemplateHelp,
                    globalSettings,
                    globalLoading,
                    popupConfig,
                    configLoading,
                    saveTemplate,
                    editTemplate,
                    cancelEdit,
                    previewTemplate,
                    previewTemplateContent,
                    confirmDelete,
                    formatHtmlForDisplay,
                    insertBasicTemplate,
                    insertCardTemplate,
                    copyPreviewContent,
                    doInsertBasicTemplate,
                    doInsertCardTemplate,
                    saveGlobalSettings,
                    savePopupConfig
                };
            }
        });

        app.use(ElementPlus);
        app.mount('#app');
    </script>
</body>
</html>