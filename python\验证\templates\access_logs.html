
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>卡密验证系统 - 访问日志</title>
        <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        <style>
            body {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            }
            .sidebar {
                background: linear-gradient(180deg, #4a00e0 0%, #8e2de2 100%);
                min-height: 100vh;
                padding-top: 20px;
                box-shadow: 4px 0 10px rgba(0, 0, 0, 0.1);
                z-index: 1000;
                position: fixed;
                width: inherit;
                max-width: inherit;
            }
            .sidebar-header {
                padding: 0 15px 20px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                margin-bottom: 20px;
            }
            .sidebar a {
                color: rgba(255, 255, 255, 0.8);
                display: block;
                padding: 12px 20px;
                text-decoration: none;
                transition: all 0.3s;
                margin-bottom: 5px;
                border-left: 3px solid transparent;
            }
            .sidebar a:hover {
                background-color: rgba(255, 255, 255, 0.1);
                color: #fff;
                border-left: 3px solid #fff;
            }
            .sidebar a.active {
                background-color: rgba(255, 255, 255, 0.2);
                color: #fff;
                border-left: 3px solid #fff;
            }
            .sidebar i {
                margin-right: 10px;
                width: 20px;
                text-align: center;
            }
            .main-content {
                margin-left: 16.666667%;
                padding: 30px;
            }
            .page-header {
                margin-bottom: 30px;
                border-bottom: 1px solid #dee2e6;
                padding-bottom: 15px;
            }
            .dashboard-card {
                background: #fff;
                border-radius: 15px;
                overflow: hidden;
                box-shadow: 0 10px 20px rgba(0,0,0,0.05);
                margin-bottom: 30px;
                transition: all 0.3s;
            }
            .dashboard-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 15px 30px rgba(0,0,0,0.1);
            }
            .card-header {
                padding: 20px;
                border-bottom: 1px solid #f1f1f1;
                font-weight: 600;
                background-color: #fff;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }
            .card-header .title {
                font-size: 18px;
                color: #333;
            }
            .card-body {
                padding: 20px;
            }
            .status-card {
                border-radius: 15px;
                padding: 20px;
                box-shadow: 0 10px 20px rgba(0,0,0,0.05);
                transition: all 0.3s;
                display: flex;
                align-items: center;
                margin-bottom: 30px;
            }
            .status-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            }
            .status-card .icon {
                width: 60px;
                height: 60px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 10px;
                font-size: 24px;
                margin-right: 20px;
                color: white;
            }
            .stats-blue {
                background: linear-gradient(45deg, #4e54c8, #8f94fb);
            }
            .stats-green {
                background: linear-gradient(45deg, #11998e, #38ef7d);
            }
            .stats-red {
                background: linear-gradient(45deg, #ff416c, #ff4b2b);
            }
            .stats-info {
                flex-grow: 1;
            }
            .stats-info h3 {
                font-size: 24px;
                font-weight: 700;
                margin-bottom: 0;
            }
            .stats-info p {
                color: #6c757d;
                margin-bottom: 0;
            }
            .search-bar {
                padding: 15px 20px;
                margin-bottom: 20px;
                border-radius: 10px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.05);
                background-color: #fff;
            }
            .table-container {
                background-color: #fff;
                border-radius: 10px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.05);
                overflow: hidden;
            }
            .table {
                margin-bottom: 0;
            }
            .table th {
                background-color: #f8f9fa;
                border-top: none;
                font-weight: 600;
                font-size: 14px;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }
            .table td {
                vertical-align: middle;
                padding: 15px 12px;
                font-size: 14px;
            }
            .card-status {
                font-weight: 500;
                padding: 5px 10px;
                border-radius: 20px;
                font-size: 12px;
                display: inline-block;
            }
            .status-success {
                background-color: rgba(25, 135, 84, 0.1);
                color: #198754;
            }
            .status-danger {
                background-color: rgba(220, 53, 69, 0.1);
                color: #dc3545;
            }
            .action-badge {
                padding: 5px 10px;
                border-radius: 20px;
                font-size: 12px;
                font-weight: 500;
            }
            .action-verify {
                background-color: rgba(13, 110, 253, 0.1);
                color: #0d6efd;
            }
            .action-activate {
                background-color: rgba(25, 135, 84, 0.1);
                color: #198754;
            }
            .action-register {
                background-color: rgba(102, 16, 242, 0.1);
                color: #6610f2;
            }
            .pagination {
                margin-top: 20px;
                justify-content: center;
            }
            .pagination .page-item .page-link {
                color: #6c757d;
                border: none;
                margin: 0 5px;
                border-radius: 5px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            }
            .pagination .page-item.active .page-link {
                background: linear-gradient(45deg, #8e2de2, #4a00e0);
                color: white;
                box-shadow: 0 5px 10px rgba(142, 45, 226, 0.3);
            }
            .device-id {
                font-family: monospace;
                font-size: 13px;
                max-width: 150px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            .license-key {
                font-family: monospace;
                font-size: 13px;
                background-color: #f8f9fa;
                padding: 5px 10px;
                border-radius: 5px;
                cursor: pointer;
            }
            .license-key:hover {
                background-color: #e9ecef;
            }
            .empty-data {
                text-align: center;
                padding: 60px 20px;
                color: #6c757d;
            }
            .empty-data i {
                font-size: 48px;
                margin-bottom: 20px;
                opacity: 0.2;
            }
            .empty-data p {
                margin-bottom: 0;
                font-size: 16px;
            }
        </style>
    </head>
    <body>
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-2">
                    <div class="sidebar">
                        <div class="sidebar-header">
                            <h4 class="text-center text-white fw-bold mb-0">卡密验证系统</h4>
                            <p class="text-center text-white-50 mb-0">管理面板</p>
                        </div>
                        <a href="/dashboard"><i class="fas fa-tachometer-alt"></i> 仪表盘</a>
                        <a href="/licenses"><i class="fas fa-key"></i> 卡密管理</a>
                        <a href="/create_license"><i class="fas fa-plus-circle"></i> 生成卡密</a>
                        <a href="/access_logs" class="active"><i class="fas fa-history"></i> 访问日志</a>
                        <a href="/logout"><i class="fas fa-sign-out-alt"></i> 退出登录</a>
                    </div>
                </div>
                <div class="col-md-10 main-content">
                    <div class="page-header">
                        <h2 class="mb-0">访问日志</h2>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="status-card">
                                <div class="icon stats-blue">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div class="stats-info">
                                    <h3>{{ verify_count }}</h3>
                                    <p>验证请求</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="status-card">
                                <div class="icon stats-green">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="stats-info">
                                    <h3>{{ activate_count }}</h3>
                                    <p>激活请求</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="status-card">
                                <div class="icon stats-red">
                                    <i class="fas fa-times-circle"></i>
                                </div>
                                <div class="stats-info">
                                    <h3>{{ failed_count }}</h3>
                                    <p>失败请求</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="search-bar">
                        <form method="get" action="/access_logs" class="row">
                            <div class="col-md-6">
                                <input type="text" name="search" class="form-control" placeholder="搜索卡密、设备ID或IP地址..." value="{{ search_term }}">
                            </div>
                            <div class="col-md-4">
                                <select name="action" class="form-select">
                                    <option value="" {% if not filter_action %}selected{% endif %}>所有类型</option>
                                    <option value="verify" {% if filter_action == 'verify' %}selected{% endif %}>验证</option>
                                    <option value="activate" {% if filter_action == 'activate' %}selected{% endif %}>激活</option>
                                    <option value="register" {% if filter_action == 'register' %}selected{% endif %}>注册</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-2"></i>搜索
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <div class="table-container">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>卡密</th>
                                    <th>设备ID</th>
                                    <th>IP地址</th>
                                    <th>类型</th>
                                    <th>时间</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in logs %}
                                <tr>
                                    <td>{{ log.id }}</td>
                                    <td><span class="license-key" onclick="copyToClipboard('{{ log.license_key }}')">{{ log.license_key }}</span></td>
                                    <td><span class="device-id" title="{{ log.device_id }}">{{ log.device_id }}</span></td>
                                    <td>{{ log.ip_address }}</td>
                                    <td>
                                        {% if log.action == 'verify' %}
                                            <span class="action-badge action-verify">验证</span>
                                        {% elif log.action == 'activate' %}
                                            <span class="action-badge action-activate">激活</span>
                                        {% elif log.action == 'register' %}
                                            <span class="action-badge action-register">注册</span>
                                        {% else %}
                                            {{ log.action }}
                                        {% endif %}
                                    </td>
                                    <td>{{ log.timestamp }}</td>
                                    <td>
                                        {% if log.success %}
                                            <span class="card-status status-success">成功</span>
                                        {% else %}
                                            <span class="card-status status-danger">失败</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                                
                                {% if not logs %}
                                <tr>
                                    <td colspan="7">
                                        <div class="empty-data">
                                            <i class="fas fa-history"></i>
                                            <p>
                                                {% if search_term or filter_action %}
                                                    没有找到符合条件的访问记录
                                                {% else %}
                                                    尚未记录任何访问日志
                                                {% endif %}
                                            </p>
                                        </div>
                                    </td>
                                </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页控件 -->
                    {% if total > per_page %}
                    <nav>
                        <ul class="pagination">
                            {% for i in range(1, (total // per_page) + (1 if total % per_page > 0 else 0) + 1) %}
                            <li class="page-item {% if i == page %}active{% endif %}">
                                <a class="page-link" href="/access_logs?page={{ i }}{% if search_term %}&search={{ search_term }}{% endif %}{% if filter_action %}&action={{ filter_action }}{% endif %}">{{ i }}</a>
                            </li>
                            {% endfor %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <script>
            function copyToClipboard(text) {
                navigator.clipboard.writeText(text).then(() => {
                    alert('卡密已复制到剪贴板');
                }).catch(err => {
                    console.error('复制失败:', err);
                });
            }
        </script>
    </body>
    </html>
    