#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import argparse
import subprocess
import time
import webbrowser

def print_banner():
    """打印应用程序横幅"""
    banner = """
  ___________  _________    验证保护系统  _________  ___________
 /  _______  \/  _____  \  -------------  /  _____  \/  _______  \\
/  /       \  \  \    \  \ 版本: v1.0.0  /  /    \  \  \       \  \\
\  \_______\  \  \____\  \ 作者: Claude  \  \____\  \  \_______\  /
 \_________  /\_________/  -------------  \_________/\_________  /
          \  \                                                \  \\
           \__\                                                \__/
    """
    print(banner)

def run_command(command, wait=True):
    """运行命令"""
    try:
        process = subprocess.Popen(command, shell=True)
        if wait:
            process.wait()
        return process
    except Exception as e:
        print(f"错误: 运行命令 '{command}' 失败 - {str(e)}")
        return None

def check_dependencies():
    """检查依赖项"""
    missing_deps = []
    
    try:
        import flask
    except ImportError:
        missing_deps.append("flask")
    
    try:
        import requests
    except ImportError:
        missing_deps.append("requests")
    
    try:
        import tabulate
    except ImportError:
        missing_deps.append("tabulate")
    
    try:
        import PyQt5
    except ImportError:
        missing_deps.append("PyQt5")
    
    if missing_deps:
        print("缺少以下依赖项:")
        for dep in missing_deps:
            print(f"  - {dep}")
        
        install = input("是否自动安装这些依赖项? (y/n): ")
        if install.lower() == 'y':
            deps_str = " ".join(missing_deps)
            run_command(f"pip install {deps_str}")
            print("依赖项已安装")
        else:
            print("请手动安装依赖项")
            print("命令: pip install flask requests tabulate PyQt5")
            sys.exit(1)

def run_server(port=5000):
    """运行验证服务器"""
    server_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "server.py")
    
    # 检查服务器是否已经运行
    import socket
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    result = sock.connect_ex(('127.0.0.1', port))
    sock.close()
    
    if result == 0:  # 端口已被占用，服务器可能已运行
        print(f"服务器已在端口 {port} 上运行")
        return
    
    print(f"正在启动验证服务器，端口: {port}...")
    process = run_command(f"python {server_path}", wait=False)
    
    # 等待服务器启动
    import time
    time.sleep(2)
    
    # 检查服务器是否成功启动
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    result = sock.connect_ex(('127.0.0.1', port))
    sock.close()
    
    if result == 0:
        print("服务器已成功启动")
    else:
        print("服务器启动失败")
        if process:
            process.terminate()

def run_admin_web(port=5001):
    """运行Web管理界面"""
    admin_web_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "admin_web.py")
    
    # 检查Web界面是否已经运行
    import socket
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    result = sock.connect_ex(('127.0.0.1', port))
    sock.close()
    
    if result == 0:  # 端口已被占用，Web界面可能已运行
        print(f"Web管理界面已在端口 {port} 上运行")
        return
    
    print(f"正在启动Web管理界面，端口: {port}...")
    process = run_command(f"python {admin_web_path}", wait=False)
    
    # 等待Web界面启动
    import time
    time.sleep(2)
    
    # 检查Web界面是否成功启动
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    result = sock.connect_ex(('127.0.0.1', port))
    sock.close()
    
    if result == 0:
        print("Web管理界面已成功启动")
        # 打开浏览器
        webbrowser.open(f"http://localhost:{port}")
    else:
        print("Web管理界面启动失败")
        if process:
            process.terminate()

def run_client_gui():
    """运行客户端GUI"""
    client_gui_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "client_gui.py")
    print("正在启动客户端GUI...")
    run_command(f"python {client_gui_path}")

def run_admin_cli():
    """运行命令行管理工具"""
    admin_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "admin.py")
    
    # 显示帮助信息
    help_info = run_command(f"python {admin_path}", wait=True)
    
    # 等待用户输入命令
    while True:
        command = input("请输入admin.py命令 (输入'exit'退出): ")
        if command.lower() == 'exit':
            break
        
        run_command(f"python {admin_path} {command}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="EXE验证保护系统")
    
    # 添加子命令
    subparsers = parser.add_subparsers(dest="command", help="命令")
    
    # 服务器命令
    server_parser = subparsers.add_parser("server", help="启动验证服务器")
    server_parser.add_argument("--port", "-p", type=int, default=5000, help="服务器端口")
    
    # 管理Web界面命令
    admin_web_parser = subparsers.add_parser("admin-web", help="启动Web管理界面")
    admin_web_parser.add_argument("--port", "-p", type=int, default=5001, help="Web界面端口")
    
    # 客户端GUI命令
    subparsers.add_parser("client", help="启动客户端GUI")
    
    # 管理命令行工具
    subparsers.add_parser("admin", help="启动命令行管理工具")
    
    # 全功能模式
    subparsers.add_parser("all", help="启动所有组件")
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 打印横幅
    print_banner()
    
    # 检查依赖项
    check_dependencies()
    
    # 根据命令执行相应操作
    if args.command == "server":
        run_server(args.port)
    elif args.command == "admin-web":
        run_admin_web(args.port)
    elif args.command == "client":
        run_client_gui()
    elif args.command == "admin":
        run_admin_cli()
    elif args.command == "all":
        # 启动所有组件
        run_server()
        run_admin_web()
        run_client_gui()
    else:
        # 如果没有指定命令，显示交互菜单
        while True:
            print("\n请选择要启动的组件:")
            print("1. 验证服务器")
            print("2. Web管理界面")
            print("3. 客户端GUI")
            print("4. 命令行管理工具")
            print("5. 启动所有组件")
            print("0. 退出")
            
            choice = input("请输入选项 [0-5]: ")
            
            if choice == "1":
                run_server()
            elif choice == "2":
                run_admin_web()
            elif choice == "3":
                run_client_gui()
            elif choice == "4":
                run_admin_cli()
            elif choice == "5":
                run_server()
                run_admin_web()
                run_client_gui()
            elif choice == "0":
                break
            else:
                print("无效选项，请重新输入")

if __name__ == "__main__":
    main() 