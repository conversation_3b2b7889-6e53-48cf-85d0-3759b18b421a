import os
import sys
import sqlite3
import requests
import json
import argparse
import datetime
from tabulate import tabulate

class LicenseManager:
    def __init__(self, server_url, db_path="licenses.db"):
        self.server_url = server_url
        self.db_path = db_path
    
    def connect_db(self):
        """连接到数据库"""
        if not os.path.exists(self.db_path):
            print(f"错误: 数据库文件 {self.db_path} 不存在")
            return None
        
        try:
            return sqlite3.connect(self.db_path)
        except Exception as e:
            print(f"连接数据库出错: {str(e)}")
            return None
    
    def create_license(self, software_id, user_id, valid_days=30):
        """创建新的许可证"""
        try:
            response = requests.post(
                f"{self.server_url}/api/register",
                json={
                    "software_id": software_id,
                    "user_id": user_id,
                    "valid_days": valid_days
                },
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"创建许可证成功!")
                print(f"软件ID: {software_id}")
                print(f"用户ID: {user_id}")
                print(f"许可证密钥: {data.get('license_key')}")
                print(f"有效期至: {data.get('valid_until')}")
                return data.get('license_key')
            else:
                print(f"创建许可证失败: {response.status_code}")
                print(response.text)
                return None
        except Exception as e:
            print(f"请求服务器出错: {str(e)}")
            return None
    
    def get_software_ids(self):
        """获取所有现有的软件ID"""
        conn = self.connect_db()
        if not conn:
            return []
        
        try:
            c = conn.cursor()
            c.execute("SELECT DISTINCT software_id FROM licenses ORDER BY software_id")
            software_ids = [row[0] for row in c.fetchall()]
            return software_ids
        except Exception as e:
            print(f"获取软件ID列表出错: {str(e)}")
            return []
        finally:
            conn.close()
    
    def list_licenses(self, software_id=None, user_id=None, active_only=False):
        """列出所有许可证"""
        conn = self.connect_db()
        if not conn:
            return
        
        try:
            c = conn.cursor()
            query = "SELECT license_key, software_id, user_id, hwid, valid_until, is_active FROM licenses"
            params = []
            
            where_clauses = []
            if software_id:
                where_clauses.append("software_id = ?")
                params.append(software_id)
            if user_id:
                where_clauses.append("user_id = ?")
                params.append(user_id)
            if active_only:
                where_clauses.append("is_active = 1")
            
            if where_clauses:
                query += " WHERE " + " AND ".join(where_clauses)
            
            c.execute(query, params)
            licenses = c.fetchall()
            
            headers = ["许可证密钥", "软件ID", "用户ID", "硬件ID", "有效期至", "状态"]
            table_data = []
            
            for license in licenses:
                status = "有效" if license[5] == 1 else "无效"
                
                # 判断是否过期
                try:
                    valid_until = datetime.datetime.strptime(license[4], "%Y-%m-%d")
                    if valid_until < datetime.datetime.now():
                        status += " (已过期)"
                except:
                    pass
                
                row = [
                    license[0],  # 许可证密钥
                    license[1],  # 软件ID
                    license[2],  # 用户ID
                    license[3] if license[3] else "未绑定",  # 硬件ID
                    license[4],  # 有效期至
                    status       # 状态
                ]
                table_data.append(row)
            
            if not table_data:
                print("未找到符合条件的许可证")
            else:
                print(tabulate(table_data, headers=headers, tablefmt="grid"))
        except Exception as e:
            print(f"查询许可证出错: {str(e)}")
        finally:
            conn.close()
    
    def revoke_license(self, license_key):
        """吊销许可证"""
        conn = self.connect_db()
        if not conn:
            return False
        
        try:
            c = conn.cursor()
            c.execute("UPDATE licenses SET is_active = 0 WHERE license_key = ?", (license_key,))
            
            if c.rowcount > 0:
                conn.commit()
                print(f"已成功吊销许可证: {license_key}")
                return True
            else:
                print(f"未找到许可证: {license_key}")
                return False
        except Exception as e:
            print(f"吊销许可证出错: {str(e)}")
            return False
        finally:
            conn.close()
    
    def extend_license(self, license_key, days):
        """延长许可证有效期"""
        conn = self.connect_db()
        if not conn:
            return False
        
        try:
            c = conn.cursor()
            c.execute("SELECT valid_until FROM licenses WHERE license_key = ?", (license_key,))
            result = c.fetchone()
            
            if not result:
                print(f"未找到许可证: {license_key}")
                return False
            
            # 解析当前有效期并延长
            try:
                valid_until = datetime.datetime.strptime(result[0], "%Y-%m-%d")
                new_valid_until = valid_until + datetime.timedelta(days=days)
                new_valid_until_str = new_valid_until.strftime("%Y-%m-%d")
                
                c.execute("UPDATE licenses SET valid_until = ? WHERE license_key = ?", 
                         (new_valid_until_str, license_key))
                conn.commit()
                
                print(f"已成功延长许可证有效期:")
                print(f"许可证密钥: {license_key}")
                print(f"新有效期至: {new_valid_until_str}")
                return True
            except Exception as e:
                print(f"处理日期出错: {str(e)}")
                return False
        except Exception as e:
            print(f"延长许可证出错: {str(e)}")
            return False
        finally:
            conn.close()
    
    def view_access_logs(self, license_key=None, limit=50):
        """查看访问日志"""
        conn = self.connect_db()
        if not conn:
            return
        
        try:
            c = conn.cursor()
            query = "SELECT id, license_key, hwid, ip_address, access_time, is_valid FROM access_logs"
            params = []
            
            if license_key:
                query += " WHERE license_key = ?"
                params.append(license_key)
            
            query += " ORDER BY access_time DESC LIMIT ?"
            params.append(limit)
            
            c.execute(query, params)
            logs = c.fetchall()
            
            headers = ["ID", "许可证密钥", "硬件ID", "IP地址", "访问时间", "验证结果"]
            table_data = []
            
            for log in logs:
                status = "成功" if log[5] == 1 else "失败"
                
                row = [
                    log[0],      # ID
                    log[1],      # 许可证密钥
                    log[2],      # 硬件ID
                    log[3],      # IP地址
                    log[4],      # 访问时间
                    status       # 验证结果
                ]
                table_data.append(row)
            
            if not table_data:
                print("未找到符合条件的访问日志")
            else:
                print(tabulate(table_data, headers=headers, tablefmt="grid"))
        except Exception as e:
            print(f"查询访问日志出错: {str(e)}")
        finally:
            conn.close()

def main():
    parser = argparse.ArgumentParser(description="许可证管理工具")
    parser.add_argument("--server", "-s", default="http://localhost:5000", help="许可证服务器URL")
    parser.add_argument("--db", "-d", default="licenses.db", help="数据库文件路径")
    
    subparsers = parser.add_subparsers(dest="command", help="命令")
    
    # 创建许可证
    create_parser = subparsers.add_parser("create", help="创建新的许可证")
    create_parser.add_argument("--software", "-sw", help="软件ID")
    create_parser.add_argument("--user", "-u", required=True, help="用户ID")
    create_parser.add_argument("--days", "-d", type=int, default=30, help="有效天数")
    
    # 列出许可证
    list_parser = subparsers.add_parser("list", help="列出许可证")
    list_parser.add_argument("--software", "-sw", help="按软件ID筛选")
    list_parser.add_argument("--user", "-u", help="按用户ID筛选")
    list_parser.add_argument("--active", "-a", action="store_true", help="只显示有效的许可证")
    
    # 列出软件ID
    subparsers.add_parser("list-software", help="列出所有软件ID")
    
    # 吊销许可证
    revoke_parser = subparsers.add_parser("revoke", help="吊销许可证")
    revoke_parser.add_argument("license_key", help="要吊销的许可证密钥")
    
    # 延长许可证
    extend_parser = subparsers.add_parser("extend", help="延长许可证有效期")
    extend_parser.add_argument("license_key", help="要延长的许可证密钥")
    extend_parser.add_argument("days", type=int, help="延长的天数")
    
    # 查看访问日志
    logs_parser = subparsers.add_parser("logs", help="查看访问日志")
    logs_parser.add_argument("--license", "-l", help="按许可证密钥筛选")
    logs_parser.add_argument("--limit", "-n", type=int, default=50, help="显示的最大记录数")
    
    args = parser.parse_args()
    
    # 创建许可证管理器
    manager = LicenseManager(args.server, args.db)
    
    # 执行命令
    if args.command == "create":
        if not args.software:
            # 如果未提供软件ID，则显示可用的软件ID列表供选择
            software_ids = manager.get_software_ids()
            
            if not software_ids:
                print("目前没有软件ID记录。请手动输入新的软件ID:")
                software_id = input("> ")
            else:
                print("可用的软件ID:")
                for i, sw_id in enumerate(software_ids, 1):
                    print(f"{i}. {sw_id}")
                
                print("\n请选择软件ID序号，或输入新的软件ID:")
                choice = input("> ")
                
                try:
                    index = int(choice) - 1
                    if 0 <= index < len(software_ids):
                        software_id = software_ids[index]
                    else:
                        software_id = choice
                except ValueError:
                    software_id = choice
        else:
            software_id = args.software
            
        manager.create_license(software_id, args.user, args.days)
    elif args.command == "list":
        manager.list_licenses(args.software, args.user, args.active)
    elif args.command == "list-software":
        software_ids = manager.get_software_ids()
        if software_ids:
            print("可用的软件ID:")
            for i, sw_id in enumerate(software_ids, 1):
                print(f"{i}. {sw_id}")
        else:
            print("目前没有软件ID记录")
    elif args.command == "revoke":
        manager.revoke_license(args.license_key)
    elif args.command == "extend":
        manager.extend_license(args.license_key, args.days)
    elif args.command == "logs":
        manager.view_access_logs(args.license, args.limit)
    else:
        parser.print_help()

if __name__ == "__main__":
    main() 