<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="referrer" content="no-referrer">
    <meta name="robots" content="noarchive">
    <meta http-equiv="Cache-Control" content="no-store">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>{$title|default='Blackglod商城'}</title>
    <link rel="shortcut icon" href="/assets/plugin/Blackglod/plugin/Blackglod/images/favicon.ico" type="image/x-icon">
    {if !empty($favicon)}
    <link rel="shortcut icon" href="{$favicon}" type="image/x-icon">
    {/if}
    <link rel="stylesheet" href="/assets/plugin/Blackglod/plugin/Blackglod/css/all.min.css">
    <style>

        :root {
            --primary: #C8A675;
            --primary-dark: #a88a5c;
            --primary-light: #e0c4a3;
            --dark: #000000;
            --light: #ffffff;
            --gray: #666666;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: var(--dark);
            color: var(--light);
            line-height: 1.6;
            overflow-x: hidden;
            animation: pageTransition 0.8s ease-out;
        }

        .container {
            width: 90%;
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
        }

        /* 优化渐变背景动画 */
        .background-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            opacity: 0.1;
            background: 
                radial-gradient(circle at 20% 20%, rgba(200, 166, 117, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(168, 138, 92, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.95) 100%);
            animation: backgroundMove 30s ease-in-out infinite alternate;
            will-change: transform;
            transform: translateZ(0);
            contain: paint;
        }

        @keyframes backgroundMove {
            0% { background-position: 0% 0%, 0% 0%, 0% 0%; }
            50% { background-position: 20% 20%, -20% -20%, 0% 0%; }
            100% { background-position: -20% -20%, 20% 20%, 0% 0%; }
        }

        /* 增强的头部导航 */
        .header {
            padding: 1rem 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            background: linear-gradient(to bottom,
                rgba(0, 0, 0, 0.95) 0%,
                rgba(0, 0, 0, 0.8) 100%);
            backdrop-filter: blur(10px);
            transition: var(--transition);
            will-change: transform, opacity;
            transform: translateZ(0);
        }

        .header.scrolled {
            padding: 0.5rem 0;
            background: rgba(0, 0, 0, 0.95);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
        }

        .header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 2rem;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            color: var(--light);
            text-decoration: none;
            min-width: 200px;
        }

        .logo-img {
            height: 36px;
            width: auto;
        }

        .logo span {
            font-size: 1.4rem;
            font-weight: 500;
            background: linear-gradient(135deg, var(--light), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        /* 优化导航栏样式 */
        .nav-links {
            display: flex;
            gap: 1rem; /* 减小间距 */
            margin-left: auto;
            flex-wrap: nowrap; /* 防止换行 */
            align-items: center;
        }

        .nav-item {
            position: relative;
            white-space: nowrap; /* 防止文字换行 */
        }

        .nav-item > a {
            color: var(--light);
            text-decoration: none;
            font-size: 0.85rem; /* 稍微减小字体 */
            padding: 0.4rem 0.6rem; /* 减小内边距 */
            border-radius: 4px;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 2px;
        }

        .nav-item > a:hover {
            color: var(--primary);
            background: rgba(200, 166, 117, 0.1);
        }

        .dropdown-arrow {
            transition: var(--transition);
            margin-left: 2px;
        }

        .nav-item:hover .dropdown-arrow {
            transform: translateY(2px);
        }

        .submenu {
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translate3d(-50%, 10px, 0);
            background: rgba(0, 0, 0, 0.95);
            backdrop-filter: blur(10px);
            min-width: 160px;
            border-radius: 8px;
            padding: 0.5rem;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(200, 166, 117, 0.1);
            will-change: transform, opacity;
        }

        .nav-item:hover .submenu {
            opacity: 1;
            visibility: visible;
            transform: translate3d(-50%, 0, 0);
        }

        .submenu a {
            color: var(--light);
            text-decoration: none;
            padding: 0.6rem 1rem;
            display: block;
            font-size: 0.9rem;
            border-radius: 4px;
            transition: var(--transition);
        }

        .submenu a:hover {
            background: rgba(200, 166, 117, 0.1);
            color: var(--primary);
        }

        .auth-buttons {
            display: flex;
            gap: 1rem;
            margin-left: 2rem;
        }

        .auth-buttons .btn {
            padding: 0.5rem 1.2rem;
            border-radius: 4px;
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .auth-icon {
            transition: var(--transition);
        }

        .btn-login {
            color: var(--primary);
            border: 1px solid var(--primary);
            background: transparent;
        }

        .btn-login:hover {
            background: rgba(200, 166, 117, 0.1);
        }

        .btn-login:hover .auth-icon {
            transform: scale(1.1);
        }

        .btn-register {
            color: var(--dark);
            background: var(--primary);
            border: 1px solid var(--primary);
        }

        .btn-register .auth-icon {
            color: var(--dark);
        }

        .btn-register:hover {
            background: var(--primary-dark);
            border-color: var(--primary-dark);
        }

        .btn-register:hover .auth-icon {
            transform: scale(1.1);
        }

        @media (max-width: 768px) {
            .nav-links, .auth-buttons {
                display: none;
            }
        }

        /* 当导航项超过一定数量时进行响应式调整 */
        @media (max-width: 1200px) {
            .nav-item > a {
                font-size: 0.8rem;
                padding: 0.3rem 0.5rem;
            }
            
            .nav-links {
                gap: 0.5rem;
            }
        }

        /* 添加导航栏滚动功能 */
        @media (max-width: 1400px) {
            .nav-links {
                overflow-x: auto;
                scrollbar-width: none; /* Firefox */
                -ms-overflow-style: none; /* IE and Edge */
                padding-bottom: 5px; /* 为滚动条预留空间 */
            }
            
            .nav-links::-webkit-scrollbar {
                display: none; /* Chrome, Safari and Opera */
            }
        }

        /* 增强的英雄区域 */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
            padding: 6rem 0;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                linear-gradient(45deg, rgba(0,0,0,0.7), transparent),
                radial-gradient(circle at center, transparent, rgba(0,0,0,0.8));
            z-index: 1;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            text-align: center;
            animation: fadeInUp 1s ease-out;
        }

        .hero h1 {
            font-size: 4rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--light), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: gradientFlow 8s linear infinite;
            text-shadow: 
                0 0 10px rgba(200, 166, 117, 0.3),
                0 0 20px rgba(200, 166, 117, 0.2),
                0 0 30px rgba(200, 166, 117, 0.1);
        }

        @keyframes gradientFlow {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .hero h2 {
            font-size: 2.5rem;
            margin-bottom: 2rem;
            background: linear-gradient(135deg, var(--light) 10%, var(--primary) 50%, var(--light) 90%);
            background-size: 200% auto;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: shine 3s linear infinite;
            text-shadow: 
                0 0 10px rgba(200, 166, 117, 0.3),
                0 0 20px rgba(200, 166, 117, 0.2),
                0 0 30px rgba(200, 166, 117, 0.1);
            position: relative;
        }

        @keyframes shine {
            0% {
                background-position: 200% center;
            }
            100% {
                background-position: -200% center;
            }
        }

        .hero h2::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, 
                transparent,
                rgba(255, 255, 255, 0.4),
                transparent
            );
            animation: lightPass 2s ease-in-out infinite;
            transform: skewX(-20deg);
        }

        @keyframes lightPass {
            0% {
                transform: translateX(-100%) skewX(-20deg);
            }
            100% {
                transform: translateX(200%) skewX(-20deg);
            }
        }

        /* 增强的统计数字 */
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 4rem;
        }

        .stat-item {
            position: relative;
            transform: translateY(30px);
            opacity: 0;
            animation: statFadeIn 0.8s forwards;
            animation-delay: calc(var(--delay, 0) * 0.1s);
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .stat-item:nth-child(1) { --delay: 1; }
        .stat-item:nth-child(2) { --delay: 2; }
        .stat-item:nth-child(3) { --delay: 3; }
        .stat-item:nth-child(4) { --delay: 4; }

        @keyframes statFadeIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .stat-item-inner {
            background: linear-gradient(135deg, 
                rgba(200, 166, 117, 0.05) 0%,
                rgba(0, 0, 0, 0.3) 100%);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2.5rem 2rem;
            text-align: center;
            position: relative;
            overflow: hidden;
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(200, 166, 117, 0.1);
            min-height: 220px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .stat-item:hover .stat-item-inner {
            transform: translateY(-10px) scale(1.02);
            border-color: var(--primary);
            box-shadow: 
                0 20px 40px rgba(0, 0, 0, 0.3),
                0 0 20px rgba(200, 166, 117, 0.2);
        }

        /* 背景渐变效果 */
        .stat-item-inner::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(
                transparent, 
                rgba(200, 166, 117, 0.1), 
                transparent 30%
            );
            opacity: 0;
            transition: opacity 0.5s ease;
            z-index: 0;
        }

        .stat-item:hover .stat-item-inner::before {
            opacity: 1;
            animation: rotateGradient 4s linear infinite;
        }

        @keyframes rotateGradient {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        /* 图标样式 */
        .stat-icon-wrapper {
            position: relative;
            width: 70px;
            height: 70px;
            margin: 0 auto 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: rgba(25, 25, 25, 0.8);
            border: 1px solid rgba(200, 166, 117, 0.2);
            transition: all 0.4s ease;
            z-index: 2;
        }

        .stat-item:hover .stat-icon-wrapper {
            transform: scale(1.1);
            border-color: rgba(200, 166, 117, 0.4);
            box-shadow: 0 0 30px rgba(200, 166, 117, 0.3);
        }

        .stat-icon {
            font-size: 2rem;
            color: var(--primary);
            transition: all 0.5s ease;
            z-index: 2;
        }

        .stat-item:hover .stat-icon {
            transform: scale(1.2) rotate(10deg);
            color: var(--primary-light);
            text-shadow: 0 0 20px var(--primary);
        }

        .icon-glow {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: radial-gradient(
                circle at center,
                rgba(200, 166, 117, 0.5) 0%,
                transparent 70%
            );
            opacity: 0.3;
            filter: blur(10px);
            transition: all 0.5s ease;
            transform: scale(0.8);
            z-index: 1;
        }

        .stat-item:hover .icon-glow {
            opacity: 0.7;
            transform: scale(1.3);
        }

        /* 数字样式 */
        .stat-number {
            font-size: 2.8rem;
            font-weight: 700;
            background: linear-gradient(135deg, 
                var(--primary-light) 0%,
                var(--primary) 50%,
                var(--primary-dark) 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
            font-family: 'Arial', sans-serif;
            position: relative;
            display: inline-block;
            opacity: 0;
            transform: translateY(20px);
            animation: numberAppear 0.6s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
            animation-delay: calc(var(--delay, 0) * 0.1s + 0.3s);
        }

        @keyframes numberAppear {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .stat-number::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 2px;
            background: var(--primary);
            border-radius: 2px;
        }

        .stat-title {
            color: #999;
            font-size: 1.1rem;
            margin-top: 0.5rem;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            position: relative;
            z-index: 2;
        }

        .stat-item:hover .stat-title {
            color: var(--light);
        }

        /* 装饰元素 */
        .stat-decoration {
            position: absolute;
            bottom: 1.5rem;
            left: 0;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.5s ease;
            z-index: 2;
        }

        .stat-item:hover .stat-decoration {
            opacity: 0.7;
            transform: translateY(0);
        }

        .stat-line {
            height: 1px;
            width: 30px;
            background: linear-gradient(90deg, transparent, var(--primary), transparent);
        }

        .stat-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: var(--primary);
            margin: 0 0.5rem;
            box-shadow: 0 0 10px var(--primary);
        }

        /* 闪光效果 */
        .stat-item-inner::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            right: -50%;
            bottom: -50%;
            background: linear-gradient(
                to right,
                rgba(200, 166, 117, 0) 0%,
                rgba(200, 166, 117, 0.1) 50%,
                rgba(200, 166, 117, 0) 100%
            );
            transform: rotate(45deg) translate(-100%, -25%);
            opacity: 0;
            transition: all 0.7s ease;
            z-index: 1;
        }

        .stat-item:hover .stat-item-inner::after {
            transform: rotate(45deg) translate(100%, 25%);
            opacity: 1;
        }

        /* 数字滚动动画 */
        @keyframes countUp {
            from {
                opacity: 0.3;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .stat-number.visible {
            animation: countUp 1.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        }

        /* 响应式调整 */
        @media (max-width: 992px) {
            .stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 576px) {
            .stats {
                grid-template-columns: 1fr;
            }
            
            .stat-item-inner {
                padding: 2rem 1.5rem;
            }
            
            .stat-number {
                font-size: 2.2rem;
            }
            
            .stat-icon-wrapper {
                width: 60px;
                height: 60px;
            }
            
            .stat-icon {
                font-size: 1.6rem;
            }
        }

        /* 增强的特性区域 */
        .features {
            padding: 8rem 0;
            background: linear-gradient(to bottom, transparent, rgba(0,0,0,0.8));
            position: relative;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .feature-item {
            padding: 2.5rem;
            border-radius: 20px;
            background: linear-gradient(135deg, 
                rgba(200, 166, 117, 0.05) 0%,
                rgba(0, 0, 0, 0.3) 100%);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(200, 166, 117, 0.1);
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .feature-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, transparent, rgba(200, 166, 117, 0.1));
            opacity: 0;
            transition: var(--transition);
        }

        .feature-item:hover {
            transform: translateY(-10px) scale(1.02);
            border-color: var(--primary);
        }

        .feature-item:hover::before {
            opacity: 1;
        }

        .feature-icon {
            font-size: 3rem;
            color: var(--primary);
            margin-bottom: 1.5rem;
            transition: var(--transition);
        }

        .feature-item:hover .feature-icon {
            transform: scale(1.2) rotate(10deg);
        }

        /* 增强的步骤区域 */
        .steps {
            padding: 8rem 0;
            position: relative;
            background: linear-gradient(to top, transparent, rgba(0,0,0,0.8));
            overflow: hidden;
        }
        
        /* 优化网格背景 */
        .steps::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(30deg, #000000 12%, transparent 12.5%, transparent 87%, #000000 87.5%, #000000),
                linear-gradient(150deg, #000000 12%, transparent 12.5%, transparent 87%, #000000 87.5%, #000000),
                linear-gradient(30deg, #000000 12%, transparent 12.5%, transparent 87%, #000000 87.5%, #000000),
                linear-gradient(150deg, #000000 12%, transparent 12.5%, transparent 87%, #000000 87.5%, #000000),
                linear-gradient(60deg, rgba(200,166,117,0.1) 25%, transparent 25.5%, transparent 75%, rgba(200,166,117,0.1) 75%, rgba(200,166,117,0.1)),
                linear-gradient(60deg, rgba(200,166,117,0.1) 25%, transparent 25.5%, transparent 75%, rgba(200,166,117,0.1) 75%, rgba(200,166,117,0.1));
            background-size: 40px 70px;
            background-position: 0 0, 0 0, 20px 35px, 20px 35px, 0 0, 20px 35px;
            opacity: 0.1;
            z-index: 0;
        }
        
        /* 中央光晕效果 */
        .steps::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 50% 50%, 
                rgba(200,166,117,0.15),
                transparent 60%);
            animation: pulseGlow 6s ease-in-out infinite;
            z-index: 1;
        }
        
        @keyframes pulseGlow {
            0%, 100% {
                opacity: 0.3;
                transform: scale(1);
            }
            50% {
                opacity: 0.5;
                transform: scale(1.2);
            }
        }
        
        .steps .container {
            position: relative;
            z-index: 2;
        }
        
        /* 标题样式增强 */
        .steps .section-title {
            text-align: center;
            font-size: 2.8rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--primary-light), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            position: relative;
            animation: titleGlow 3s ease-in-out infinite alternate;
        }
        
        @keyframes titleGlow {
            0% {
                text-shadow: 0 0 5px rgba(200, 166, 117, 0.3);
            }
            100% {
                text-shadow: 0 0 15px rgba(200, 166, 117, 0.6);
            }
        }
        
        /* 副标题样式增强 */
        .steps .section-subtitle {
            text-align: center;
            color: var(--gray);
            margin-bottom: 5rem;
            font-size: 1.2rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            position: relative;
        }
        
        .steps .section-subtitle::after {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 2px;
            background: linear-gradient(90deg, transparent, var(--primary), transparent);
        }
        
        /* 步骤容器布局优化 */
        .steps-container {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 2rem;
            position: relative;
            margin: 0 auto;
            max-width: 1000px;
        }
        
        /* 步骤卡片样式 */
        .step {
            flex: 1;
            position: relative;
            transform: translateY(50px);
            opacity: 0;
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        /* 步骤卡片显示动画 */
        .step-visible {
            transform: translateY(0);
            opacity: 1;
        }
        
        .step-card {
            background: rgba(25, 25, 25, 0.6);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(200, 166, 117, 0.1);
            padding: 2.5rem 1.5rem;
            text-align: center;
            position: relative;
            overflow: hidden;
            transition: all 0.4s ease;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transform: translateZ(0);
        }
        
        /* 卡片内层 */
        .step-card-inner {
            position: relative;
            z-index: 2;
        }
        
        /* 卡片悬停效果 */
        .step-hover .step-card {
            border-color: rgba(200, 166, 117, 0.3);
            transform: translateY(-10px);
            box-shadow: 
                0 20px 40px rgba(0, 0, 0, 0.3),
                0 0 20px rgba(200, 166, 117, 0.2);
        }
        
        /* 背景渐变 */
        .step-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, 
                rgba(200, 166, 117, 0.05) 0%,
                rgba(0, 0, 0, 0.3) 100%);
            z-index: 1;
        }
        
        /* 闪光效果 */
        .step-card::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            right: -50%;
            bottom: -50%;
            background: linear-gradient(
                to right,
                rgba(200, 166, 117, 0) 0%,
                rgba(200, 166, 117, 0.1) 50%,
                rgba(200, 166, 117, 0) 100%
            );
            transform: rotate(45deg) translate(-100%, -25%);
            opacity: 0;
            transition: all 0.7s ease;
        }
        
        .step-hover .step-card::after {
            transform: rotate(45deg) translate(100%, 25%);
            opacity: 1;
        }
        
        /* 步骤圆圈样式 */
        .step-circle {
            width: 120px;
            height: 120px;
            margin: 0 auto 2rem;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* 数字样式 */
        .step-number {
            position: absolute;
            font-size: 2.2rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--light), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            z-index: 2;
            transition: var(--transition);
            opacity: 1;
        }
        
        /* 图标样式 */
        .step-icon {
            position: absolute;
            font-size: 2.8rem;
            color: var(--primary);
            z-index: 2;
            opacity: 0;
            transform: scale(0.5) rotate(-180deg);
            transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
        }
        
        /* 圆环进度条 */
        .progress-ring {
            position: absolute;
            transform: rotate(-90deg);
        }
        
        .progress-ring-circle {
            fill: transparent;
            stroke: var(--primary);
            stroke-width: 3;
            stroke-dasharray: 339.292;
            stroke-dashoffset: 339.292;
            transition: all 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
            filter: drop-shadow(0 0 5px rgba(200, 166, 117, 0.3));
        }
        
        /* 光晕效果 */
        .step-glow {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: radial-gradient(circle at center,
                rgba(200, 166, 117, 0.4) 0%,
                transparent 70%);
            opacity: 0;
            transition: all 0.5s ease;
            filter: blur(10px);
            transform: scale(0.8);
        }
        
        /* 悬停动画效果 */
        .step-hover .progress-ring-circle {
            stroke-dashoffset: 0;
            stroke-width: 4;
        }
        
        .step-hover .step-number {
            opacity: 0;
            transform: scale(0.5) translateY(-20px);
        }
        
        .step-hover .step-icon {
            opacity: 1;
            transform: scale(1) rotate(0deg);
        }
        
        .step-hover .step-glow {
            opacity: 0.8;
            transform: scale(1.2);
        }
        
        /* 标题样式 */
        .step h3 {
            color: var(--primary);
            font-size: 1.5rem;
            margin-bottom: 1rem;
            transition: var(--transition);
            position: relative;
            display: inline-block;
        }
        
        .step h3::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--primary);
            transition: all 0.4s ease;
        }
        
        .step-hover h3::after {
            width: 100%;
        }
        
        /* 描述文本样式 */
        .step p {
            color: var(--gray);
            font-size: 1rem;
            line-height: 1.6;
            transition: var(--transition);
            opacity: 0.7;
        }
        
        .step-hover p {
            color: var(--light);
            opacity: 1;
        }
        
        /* 装饰元素样式 */
        .step-decoration {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            pointer-events: none;
        }
        
        .dot {
            position: absolute;
            background: var(--primary);
            border-radius: 50%;
            opacity: 0;
            transform: scale(0);
            transition: all 0.5s ease;
        }
        
        .dot-1 {
            width: 8px;
            height: 8px;
            top: 20%;
            right: 15%;
        }
        
        .dot-2 {
            width: 5px;
            height: 5px;
            top: 70%;
            left: 20%;
        }
        
        .dot-3 {
            width: 6px;
            height: 6px;
            bottom: 10%;
            right: 30%;
        }
        
        .line {
            position: absolute;
            background: linear-gradient(90deg, transparent, var(--primary), transparent);
            height: 1px;
            opacity: 0;
            transform: scaleX(0);
            transition: all 0.5s ease;
        }
        
        .line-1 {
            width: 40px;
            top: 30%;
            left: 10%;
            transform-origin: left center;
        }
        
        .line-2 {
            width: 60px;
            bottom: 25%;
            right: 10%;
            transform-origin: right center;
        }
        
        .step-hover .dot {
            opacity: 0.7;
            transform: scale(1);
        }
        
        .step-hover .line {
            opacity: 0.3;
            transform: scaleX(1);
        }
        
        /* 连接线样式 */
        .step-connector {
            position: absolute;
            top: 50%;
            right: -1rem;
            width: 2rem;
            height: 2rem;
            transform: translateY(-50%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 3;
        }
        
        .connector-line {
            position: absolute;
            height: 2px;
            width: 100%;
            background: linear-gradient(to right, var(--primary), var(--primary-dark));
            transform: scaleX(0);
            transform-origin: left center;
            transition: transform 0.6s ease;
        }
        
        .connector-arrow {
            position: absolute;
            right: 0;
            color: var(--primary);
            opacity: 0;
            transform: translateX(-10px);
            transition: all 0.6s ease;
        }
        
        .step-visible .connector-line {
            transform: scaleX(1);
        }
        
        .step-visible .connector-arrow {
            opacity: 1;
            transform: translateX(0);
        }
        
        /* 隐藏最后一个步骤的连接器 */
        .step:last-child .step-connector {
            display: none;
        }
        
        /* 底部装饰 */
        .steps-decoration {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 4rem;
            opacity: 0.5;
        }
        
        .decoration-line {
            height: 1px;
            width: 100px;
            background: linear-gradient(90deg, transparent, var(--primary), transparent);
        }
        
        .decoration-circle {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: var(--primary);
            margin: 0 1rem;
            box-shadow: 0 0 10px var(--primary);
        }
        
        /* 响应式调整 */
        @media (max-width: 992px) {
            .steps-container {
                flex-direction: column;
                gap: 4rem;
            }
            
            .step {
                width: 100%;
                max-width: 350px;
            }
            
            .step-connector {
                top: auto;
                right: 50%;
                bottom: -2rem;
                transform: translateX(50%) rotate(90deg);
            }
        }
        
        @media (max-width: 768px) {
            .steps {
                padding: 5rem 0;
            }
            
            .steps .section-title {
                font-size: 2.2rem;
            }
            
            .steps .section-subtitle {
                font-size: 1rem;
                margin-bottom: 3rem;
            }
            
            .step-card {
                padding: 2rem 1rem;
            }
            
            .step-circle {
                width: 100px;
                height: 100px;
            }
            
            .progress-ring {
                width: 100px;
                height: 100px;
            }
            
            .progress-ring-circle {
                stroke-dasharray: 282.74;
                stroke-dashoffset: 282.74;
            }
            
            .step-number {
                font-size: 1.8rem;
            }
            
            .step-icon {
                font-size: 2.2rem;
            }
            
            .step h3 {
                font-size: 1.3rem;
            }
        }

        /* 优化页脚样式 */
        .footer {
            background-color: rgba(0, 0, 0, 0.95);
            padding: 3rem 0 1rem;
            position: relative;
            margin-top: 5rem;
            overflow: hidden;
        }
        
        /* 新的页脚布局 */
        .footer-new-layout {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            align-items: flex-start;
            gap: 2rem;
            padding-bottom: 1rem;
            margin-bottom: 0;
            border-bottom: 1px solid rgba(200, 166, 117, 0.1);
        }
        
        /* 删除footer-grid上边距，确保与footer-new-layout无间隙 */
        .footer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin: 0; /* 去除上边距 */
            padding-top: 1rem; /* 顶部添加少量内边距 */
            padding-bottom: 0; /* 去除底部内边距 */
        }

        /* 左侧Logo和标语 */
        .footer-logo-section {
            flex: 1;
            min-width: 200px;
            margin-right: 2rem;
        }

        .footer-logo {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .footer-logo .logo-img {
            height: 40px;
            margin-bottom: 0.5rem;
        }

        .footer-logo .slogan {
            font-size: 1rem;
            color: var(--primary);
            font-weight: 500;
            margin-top: 0.5rem;
        }

        .footer-address {
            color: var(--gray);
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }

        /* 中间快速通道 */
        .footer-quick-section {
            flex: 1;
            min-width: 180px;
            margin-right: 2rem;
        }

        .quick-title {
            color: var(--primary);
            font-size: 1.2rem;
            margin-bottom: 1.2rem;
            position: relative;
            padding-bottom: 0.5rem;
        }

        .quick-title::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 30px;
            height: 2px;
            background: var(--primary);
        }

        .quick-links {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .quick-links li {
            margin-bottom: 0.8rem;
        }

        .quick-links a {
            color: var(--gray);
            text-decoration: none;
            font-size: 0.95rem;
            transition: var(--transition);
            display: block;
        }

        .quick-links a:hover {
            color: var(--primary-light);
            transform: translateX(5px);
        }

        /* 右侧二维码区域 */
        .footer-qrcode-section {
            display: flex;
            gap: 2rem;
            flex: 2;
            justify-content: flex-end;
        }

        .qrcode-item {
            text-align: center;
            max-width: 150px;
        }

        .qrcode-title {
            color: var(--primary);
            font-size: 1.1rem;
            margin-bottom: 1rem;
        }

        .qrcode-img {
            width: 120px;
            height: 120px;
            margin: 0 auto 0.8rem;
            padding: 5px;
            background: #fff;
            border-radius: 4px;
        }

        .qrcode-img img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* 二维码占位符样式 */
        .qrcode-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(200, 166, 117, 0.1);
            color: var(--primary);
            font-size: 0.9rem;
            text-align: center;
            line-height: 1.4;
            border: 1px dashed var(--primary);
        }

        .qrcode-desc {
            color: var(--gray);
            font-size: 0.8rem;
            line-height: 1.4;
        }

        /* 响应式调整 */
        @media (max-width: 992px) {
            .footer-new-layout {
                flex-direction: column;
                gap: 2rem;
            }

            .footer-logo-section, 
            .footer-quick-section {
                margin-right: 0;
            }

            .footer-qrcode-section {
                justify-content: flex-start;
            }
        }

        @media (max-width: 576px) {
            .footer-qrcode-section {
                flex-direction: column;
                gap: 2rem;
                align-items: center;
            }

            .qrcode-item {
                max-width: 100%;
            }
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 3rem;
            margin-bottom: 3rem;
        }

        .footer-column {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .footer-column h3 {
            color: var(--primary);
            font-size: 1.2rem;
            margin-bottom: 1rem;
            position: relative;
            padding-bottom: 0.5rem;
        }

        .footer-column h3::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 30px;
            height: 2px;
            background: var(--primary);
            border-radius: 2px;
        }

        .footer-column a {
            color: var(--gray);
            text-decoration: none;
            transition: var(--transition);
            font-size: 0.95rem;
            display: inline-block;
            position: relative;
            padding-left: 1.2rem;
        }

        .footer-column a::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: var(--primary);
            transform: translateY(-50%) scale(0.6);
            opacity: 0.5;
            transition: var(--transition);
        }

        .footer-column a:hover {
            color: var(--primary-light);
            transform: translateX(5px);
        }

        .footer-column a:hover::before {
            transform: translateY(-50%) scale(1);
            opacity: 1;
        }

        .copyright {
            text-align: center;
            color: var(--gray);
            padding-top: 2rem;
            border-top: 1px solid rgba(200, 166, 117, 0.1);
            font-size: 0.9rem;
        }

        .copyright a {
            color: var(--primary);
            text-decoration: none;
            transition: var(--transition);
            margin: 0 0.5rem;
        }

        .copyright a:hover {
            color: var(--primary-light);
        }

        @media (max-width: 768px) {
            .footer {
                padding: 3rem 0 1.5rem;
            }

            .footer-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 2rem;
            }

            .copyright {
                flex-direction: column;
                gap: 0.5rem;
            }
        }

        @media (max-width: 480px) {
            .footer-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .hero h1 {
                font-size: 2.5rem;
            }

            .hero h2 {
                font-size: 2rem;
            }

            .steps-container {
                flex-direction: column;
                gap: 2rem;
            }

            .steps-container::before {
                display: none;
            }

            .auth-buttons {
                display: none;
            }

            .mobile-menu-btn {
                display: block;
            }
        }

        /* 移动端菜单按钮 */
        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            color: var(--light);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
        }

        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: block;
            }
        }

    /* 星星背景容器 */
    .stars-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        z-index: 0;
        pointer-events: none;
        background: radial-gradient(circle at center, rgba(0,0,0,0.8), rgba(0,0,0,0.95)); /* 添加暗色渐变背景 */
    }

    /* 星星样式 */
    .star {
        position: absolute;
        background: linear-gradient(135deg, #ffd700, #ffa500);  /* 更亮的金色渐变 */
        border-radius: 50%;
        filter: blur(1px);
        box-shadow: 
            0 0 4px #ffd700,
            0 0 8px #ffd700,
            0 0 12px #c8a675;  /* 三层光晕效果 */
        opacity: 0;
        animation: twinkle var(--duration) ease-in-out infinite;
        animation-delay: var(--delay);
        will-change: transform, opacity;
        transform: translateZ(0);
        transform: translate3d(0, 0, 0);
    }

    /* 星星闪烁动画 */
    @keyframes twinkle {
        0%, 100% {
            opacity: 0;
            transform: scale(0.3);
            filter: blur(1px);
        }
        50% {
            opacity: var(--opacity);
            transform: scale(1.2);  /* 更大的缩放效果 */
            filter: blur(0.5px);
        }
    }

    /* 流星效果 */
    .shooting-star {
        position: absolute;
        width: 200px;  /* 更长的流星 */
        height: 3px;   /* 更粗的流星 */
        background: linear-gradient(90deg, 
            rgba(255, 215, 0, 1), 
            rgba(255, 215, 0, 0.8),
            rgba(200, 166, 117, 0.4), 
            transparent
        );
        opacity: 0;
        filter: blur(0.5px);
        transform: rotate(-45deg);
        box-shadow: 
            0 0 20px rgba(255, 215, 0, 0.8),
            0 0 30px rgba(255, 215, 0, 0.6);  /* 更强的光晕效果 */
        animation: shoot 3s ease-in infinite;
        animation-delay: var(--delay);
        will-change: transform, opacity;
    }

    @keyframes shoot {
        0% {
            opacity: 0;
            transform: translateX(-100%) translateY(0) rotate(-45deg);
        }
        10% {
            opacity: 1;
        }
        20%, 100% {
            opacity: 0;
            transform: translateX(100vw) translateY(100vh) rotate(-45deg);
        }
    }

    /* 金额数字的响应式字体大小 */
    .stat-number.amount {
        font-size: 2.8rem; /* 默认大小 */
    }

    .stat-number.amount.length-9 {
        font-size: 2.6rem;
    }

    .stat-number.amount.length-10 {
        font-size: 2.4rem;
    }

    .stat-number.amount.length-11 {
        font-size: 2.2rem;
    }

    .stat-number.amount.length-12 {
        font-size: 2rem;
    }

    /* 移动端适配 */
    @media (max-width: 768px) {
        .stat-number.amount {
            font-size: 2.2rem;
        }
        
        .stat-number.amount.length-9 {
            font-size: 2rem;
        }
        
        .stat-number.amount.length-10 {
            font-size: 1.8rem;
        }
        
        .stat-number.amount.length-11 {
            font-size: 1.6rem;
        }
        
        .stat-number.amount.length-12 {
            font-size: 1.4rem;
        }
    }

    .payment-icons {
        padding: 6rem 0;
        background: rgba(0, 0, 0, 0.3);
        border-bottom: 1px solid rgba(200, 166, 117, 0.1);
        position: relative;
        overflow: hidden;
    }

    .payment-icons::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, 
            transparent,
            var(--primary),
            transparent
        );
    }

    .section-title {
        text-align: center;
        color: var(--primary);
        font-size: 2.2rem;
        margin-bottom: 1rem;
        background: linear-gradient(135deg, var(--primary-light), var(--primary));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .section-subtitle {
        text-align: center;
        color: var(--gray);
        margin-bottom: 3rem;
        font-size: 1.1rem;
    }

    .icons-grid {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: 3rem;
        align-items: center;
        justify-items: center;
        padding: 2rem 0;
    }

    .icon-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1.2rem;
        position: relative;
        transition: transform 0.3s ease;
        padding: 1.5rem;
        border-radius: 15px;
        background: rgba(0, 0, 0, 0.2);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(200, 166, 117, 0.1);
    }

    .icon-item:hover {
        transform: translateY(-10px);
        background: rgba(200, 166, 117, 0.1);
        border-color: rgba(200, 166, 117, 0.3);
        box-shadow: 
            0 10px 20px rgba(0, 0, 0, 0.2),
            0 0 15px rgba(200, 166, 117, 0.2),
            inset 0 0 20px rgba(200, 166, 117, 0.1);
    }

    .icon-wrapper {
        position: relative;
        padding: 1.5rem;
        border-radius: 50%;
        background: rgba(0, 0, 0, 0.3);
        overflow: hidden;
    }

    .icon-wrapper::before {
        content: '';
        position: absolute;
        inset: -50%;
        background: conic-gradient(
            from 0deg,
            transparent,
            var(--primary) 60%,
            transparent 80%
        );
        animation: rotateGlow 2s linear infinite;
    }

    .icon-wrapper::after {
        content: '';
        position: absolute;
        inset: 2px;
        background: rgba(0, 0, 0, 0.8);
        border-radius: 50%;
        z-index: 1;
    }

    .payment-icon {
        width: 70px;
        height: 70px;
        color: var(--primary);
        transition: all 0.3s ease;
        position: relative;
        z-index: 2;
        filter: drop-shadow(0 0 8px rgba(200, 166, 117, 0.3));
    }

    .icon-item:hover .payment-icon {
        transform: scale(1.1) rotate(5deg);
        filter: drop-shadow(0 0 12px rgba(200, 166, 117, 0.5));
    }

    .icon-item span {
        color: var(--gray);
        font-size: 1rem;
        font-weight: 500;
        transition: all 0.3s ease;
        text-shadow: 0 0 10px rgba(200, 166, 117, 0.3);
    }

    .icon-item:hover span {
        color: var(--primary);
        transform: scale(1.1);
        text-shadow: 0 0 15px rgba(200, 166, 117, 0.5);
    }

    @keyframes rotateGlow {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    /* 添加浮动粒子效果 */
    .icon-item::after {
        content: '';
        position: absolute;
        width: 4px;
        height: 4px;
        background: var(--primary);
        border-radius: 50%;
        filter: blur(1px);
        opacity: 0;
        transition: 0.5s;
        animation: particleFloat 2s ease-in-out infinite;
    }

    @keyframes particleFloat {
        0% {
            transform: translate(0, 0);
            opacity: 0;
        }
        50% {
            opacity: 1;
        }
        100% {
            transform: translate(var(--x, 20px), var(--y, -20px));
            opacity: 0;
        }
    }

    .icons-grid {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: 2rem;
        padding: 2rem;
        position: relative;
        z-index: 1;
    }

    .icon-glow {
        position: absolute;
        inset: 0;
        background: radial-gradient(circle at center, 
            rgba(200, 166, 117, 0.2),
            transparent 70%
        );
        opacity: 0;
        transition: var(--transition);
        filter: blur(10px);
    }

    .icon-item:hover .icon-glow {
        opacity: 1;
        transform: scale(1.2);
    }

    .icon-item:hover .payment-icon {
        transform: scale(1.1);
        filter: drop-shadow(0 0 10px rgba(200, 166, 117, 0.3));
    }

    .icon-item span {
        color: var(--gray);
        font-size: 1rem;
        font-weight: 500;
        transition: var(--transition);
    }

    .icon-item:hover span {
        color: var(--primary-light);
    }

    @media (max-width: 768px) {
        .payment-icons {
            padding: 4rem 0;
        }

        .icons-grid {
            grid-template-columns: repeat(3, 1fr);
            gap: 2rem;
        }
        
        .payment-icon {
            width: 60px;
            height: 60px;
        }

        .section-title {
            font-size: 1.8rem;
        }

        .section-subtitle {
            font-size: 1rem;
            margin-bottom: 2rem;
        }
    }

    /* 减少不必要的动画 */
    @media (prefers-reduced-motion: reduce) {
        .background-animation,
        .star,
        .shooting-star {
            animation: none;
        }
    }

    .nav-item > a.has-arrow {
        display: flex;
        align-items: center;
        gap: 8px;
        padding-right: 12px;
    }

    /* 确保箭头颜色为金色 */
    .dropdown-arrow path {
        stroke: var(--primary);
    }

    /* 悬停时箭头颜色加深 */
    .nav-item:hover .dropdown-arrow path {
        stroke: var(--primary-dark);
    }

    /* 优化按钮效果 */
    .btn {
        position: relative;
        overflow: hidden;
    }

    .btn::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 60%);
        transform: scale(0);
        transition: transform 0.6s ease-out;
    }

    .btn:hover::before {
        transform: scale(1);
    }

    /* 添加页面切换动画 */
    @keyframes pageTransition {
        0% { opacity: 0; transform: translateY(20px); }
        100% { opacity: 1; transform: translateY(0); }
    }

    /* 优化导航栏效果 */
    .header {
        background: linear-gradient(to bottom,
            rgba(0, 0, 0, 0.95) 0%,
            rgba(0, 0, 0, 0.8) 100%);
    }

    .nav-item > a::before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 0;
        height: 2px;
        background: var(--primary);
        transition: all 0.3s ease;
        transform: translateX(-50%);
    }

    .nav-item > a:hover::before {
        width: 100%;
    }

    /* 优化统计数字动画 */
    .stat-number {
        background: linear-gradient(135deg, 
            var(--primary-light) 0%,
            var(--primary) 50%,
            var(--primary-dark) 100%);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        animation: numberGlow 2s ease-in-out infinite alternate;
    }

    @keyframes numberGlow {
        0% { filter: drop-shadow(0 0 2px rgba(200, 166, 117, 0.3)); }
        100% { filter: drop-shadow(0 0 8px rgba(200, 166, 117, 0.6)); }
    }

    /* 优化滚动条样式 */
    ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }

    ::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.1);
    }

    ::-webkit-scrollbar-thumb {
        background: var(--primary);
        border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: var(--primary-dark);
    }

    /* 添加页面加载进度条 */
    .progress-bar {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background: linear-gradient(to right, var(--primary), var(--primary-light));
        transform-origin: 0 50%;
        transform: scaleX(0);
        transition: transform 0.3s ease;
        z-index: 10000;
    }

    /* 粒子容器 */
    .particles-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
        pointer-events: none;
        overflow: hidden;
    }

    /* 粒子样式 */
    .particle {
        position: absolute;
        background: radial-gradient(circle at center,
            rgba(200, 166, 117, 0.8) 0%,
            rgba(200, 166, 117, 0.4) 40%,
            transparent 70%
        );
        border-radius: 50%;
        filter: blur(1px);
        animation: particleFloat 8s infinite;
    }

    /* 粒子动画 */
    @keyframes particleFloat {
        0% {
            transform: translate(0, 0) rotate(0deg) scale(1);
            opacity: 0;
        }
        20% {
            opacity: var(--particle-opacity);
        }
        80% {
            opacity: var(--particle-opacity);
        }
        100% {
            transform: translate(
                var(--particle-translateX),
                var(--particle-translateY)
            ) 
            rotate(var(--particle-rotate)) 
            scale(var(--particle-scale));
            opacity: 0;
        }
    }

    /* 连线效果 */
    .particle-line {
        position: absolute;
        background: linear-gradient(90deg,
            transparent,
            rgba(200, 166, 117, 0.2),
            transparent
        );
        height: 1px;
        transform-origin: left center;
        animation: lineGrow 4s infinite;
    }

    @keyframes lineGrow {
        0% {
            transform: scaleX(0);
            opacity: 0;
        }
        50% {
            transform: scaleX(1);
            opacity: 1;
        }
        100% {
            transform: scaleX(0);
            opacity: 0;
        }
    }

    /* 星球容器 */
    .planets-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 0;
        pointer-events: none;
        overflow: hidden;
    }

    /* 星球基础样式 */
    .planet {
        position: absolute;
        border-radius: 50%;
        filter: blur(1px);
        animation: planetGlow 4s ease-in-out infinite alternate;
    }

    /* 主星球 */
    .planet-main {
        width: 300px;
        height: 300px;
        right: -100px;
        top: 20%;
        background: radial-gradient(circle at 30% 30%,
            rgba(200, 166, 117, 0.2),
            rgba(168, 138, 92, 0.1),
            rgba(0, 0, 0, 0)
        );
        box-shadow: 
            inset 10px 10px 30px rgba(200, 166, 117, 0.3),
            inset -10px -10px 30px rgba(0, 0, 0, 0.4);
    }

    /* 小星球1 */
    .planet-small-1 {
        width: 150px;
        height: 150px;
        left: 10%;
        top: 15%;
        background: radial-gradient(circle at 40% 40%,
            rgba(200, 166, 117, 0.15),
            rgba(168, 138, 92, 0.08),
            rgba(0, 0, 0, 0)
        );
        animation-delay: -2s;
    }

    /* 小星球2 */
    .planet-small-2 {
        width: 100px;
        height: 100px;
        left: 60%;
        bottom: 20%;
        background: radial-gradient(circle at 35% 35%,
            rgba(200, 166, 117, 0.12),
            rgba(168, 138, 92, 0.06),
            rgba(0, 0, 0, 0)
        );
        animation-delay: -1s;
    }

    /* 星球光晕动画 */
    @keyframes planetGlow {
        0% {
            transform: scale(1) rotate(0deg);
            opacity: 0.8;
        }
        100% {
            transform: scale(1.1) rotate(360deg);
            opacity: 1;
        }
    }

    /* 星球轨道 */
    .planet-orbit {
        position: absolute;
        border: 1px solid rgba(200, 166, 117, 0.1);
        border-radius: 50%;
        animation: orbitRotate 20s linear infinite;
    }

    .orbit-1 {
        width: 400px;
        height: 400px;
        right: -150px;
        top: 15%;
    }

    .orbit-2 {
        width: 200px;
        height: 200px;
        left: 8%;
        top: 12%;
    }

    /* 轨道旋转动画 */
    @keyframes orbitRotate {
        from {
            transform: rotate(0deg);
        }
        to {
            transform: rotate(360deg);
        }
    }

    /* 调整原有粒子容器的层级 */
    .particles-container {
        z-index: 1;
    }

    /* 左侧主星球 */
    .planet-main-left {
        width: 280px;
        height: 280px;
        left: -80px;
        top: 35%;
        background: radial-gradient(circle at 70% 30%,
            rgba(200, 166, 117, 0.2),
            rgba(168, 138, 92, 0.1),
            rgba(0, 0, 0, 0)
        );
        box-shadow: 
            inset -10px 10px 30px rgba(200, 166, 117, 0.3),
            inset 10px -10px 30px rgba(0, 0, 0, 0.4);
        animation-delay: -3s;
    }

    /* 添加新的轨道 */
    .orbit-3 {
        width: 380px;
        height: 380px;
        left: -130px;
        top: 30%;
        animation-direction: reverse;
    }

    /* 调整其他星球的位置以平衡布局 */
    .planet-small-1 {
        left: 15%;
        top: 20%;
    }

    .planet-small-2 {
        left: 65%;
        bottom: 25%;
    }

    /* 增强光晕效果 */
    .planet::after {
        content: '';
        position: absolute;
        inset: -20px;
        border-radius: 50%;
        background: radial-gradient(circle at center,
            rgba(200, 166, 117, 0.1),
            transparent 70%
        );
        filter: blur(10px);
        animation: glowPulse 4s ease-in-out infinite alternate;
    }

    @keyframes glowPulse {
        0% {
            opacity: 0.3;
            transform: scale(0.8);
        }
        100% {
            opacity: 0.7;
            transform: scale(1.2);
        }
    }

    /* 3D球体容器 */
    .sphere-container {
        position: relative;
        width: 150px;
        height: 150px;
        margin: 30px auto;
        perspective: 1200px;
    }

    /* 球体主体 */
    .sphere {
        position: relative;
        width: 100%;
        height: 100%;
        transform-style: preserve-3d;
        animation: sphereRotate 20s linear infinite;
    }

    /* 球体核心 */
    .sphere-core {
        position: absolute;
        width: 60px;
        height: 60px;
        left: 30px;
        top: 30px;
        border-radius: 50%;
        background: radial-gradient(circle at 30% 30%,
            var(--primary-light),
            var(--primary),
            var(--primary-dark)
        );
        box-shadow: 
            0 0 20px rgba(200, 166, 117, 0.4),
            inset 0 0 15px rgba(255, 255, 255, 0.6);
    }

    /* 球体环 */
    .sphere-ring {
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        border: 2px solid rgba(200, 166, 117, 0.3);
        box-shadow: 0 0 15px rgba(200, 166, 117, 0.2);
    }

    /* 设置三个环的旋转角度 */
    .sphere-ring:nth-child(1) {
        transform: rotateX(60deg);
    }

    .sphere-ring:nth-child(2) {
        transform: rotateY(60deg);
    }

    .sphere-ring:nth-child(3) {
        transform: rotateZ(60deg);
    }

    /* 球体旋转动画 */
    @keyframes sphereRotate {
        0% {
            transform: rotateX(-20deg) rotateY(0deg) rotateZ(45deg);
        }
        100% {
            transform: rotateX(-20deg) rotateY(360deg) rotateZ(45deg);
        }
    }

    /* 添加悬停效果 */
    .sphere-container:hover .sphere {
        animation-play-state: paused;
    }

    .sphere-container:hover .sphere-core {
        background: radial-gradient(circle at 30% 30%,
            var(--primary-light),
            var(--primary),
            var(--primary-dark)
        );
        box-shadow: 
            0 0 30px rgba(200, 166, 117, 0.6),
            inset 0 0 20px rgba(255, 255, 255, 0.8);
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
        .sphere-container {
            width: 100px;
            height: 100px;
        }
        
        .sphere-core {
            width: 40px;
            height: 40px;
            left: 20px;
            top: 20px;
        }
    }

    .sphere-grid {
        position: absolute;
        width: 100%;
        height: 100%;
        transform-style: preserve-3d;
        box-shadow: inset 0 0 20px rgba(200, 166, 117, 0.1);
        border-radius: 50%;
    }

    /* 经线 */
    .longitude-line {
        position: absolute;
        width: 100%;
        height: 100%;
        border: 1px solid rgba(200, 166, 117, 0.15);
        border-radius: 50%;
        transform: rotateY(var(--rotation));
    }

    /* 纬线 */
    .latitude-line {
        position: absolute;
        width: 100%;
        height: 100%;
        border: 1px solid rgba(200, 166, 117, 0.15);
        border-radius: 50%;
        transform: rotateX(var(--rotation)) scaleY(var(--scale));
    }

    .sphere-surface {
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: radial-gradient(circle at 30% 30%,
            rgba(200, 166, 117, 0.6),
            rgba(200, 166, 117, 0.3)
        );
        box-shadow: 
            inset 0 0 20px rgba(200, 166, 117, 0.4),
            0 0 15px rgba(200, 166, 117, 0.3);
    }

    @keyframes sphereRotate {
        0% {
            transform: rotateX(-20deg) rotateY(0deg) rotateZ(45deg);
        }
        100% {
            transform: rotateX(-20deg) rotateY(360deg) rotateZ(45deg);
        }
    }

    /* 悬停效果 */
    .sphere-container:hover .sphere {
        animation-play-state: paused;
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
        .sphere-container {
            width: 100px;
            height: 100px;
        }
    }

    /* 添加球体两侧的装饰元素样式 */
    .sphere-decorations {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        pointer-events: none;
    }

    .sphere-ring-decoration {
        position: absolute;
        border: 2px solid rgba(200, 166, 117, 0.1);
        border-radius: 50%;
        transform-style: preserve-3d;
    }

    /* 左侧装饰 */
    .decoration-left {
        left: -60px;
        top: 50%;
        width: 40px;
        height: 40px;
        animation: rotateLeft 8s linear infinite;
    }

    .decoration-left::before {
        content: '';
        position: absolute;
        width: 8px;
        height: 8px;
        background: var(--primary);
        border-radius: 50%;
        top: 50%;
        left: -4px;
        transform: translateY(-50%);
        box-shadow: 0 0 15px var(--primary);
    }

    /* 右侧装饰 */
    .decoration-right {
        right: -60px;
        top: 50%;
        width: 40px;
        height: 40px;
        animation: rotateRight 8s linear infinite;
    }

    .decoration-right::after {
        content: '';
        position: absolute;
        width: 8px;
        height: 8px;
        background: var(--primary);
        border-radius: 50%;
        top: 50%;
        right: -4px;
        transform: translateY(-50%);
        box-shadow: 0 0 15px var(--primary);
    }

    /* 装饰性光线 */
    .sphere-rays {
        position: absolute;
        width: 200%;
        height: 200%;
        top: -50%;
        left: -50%;
        background: radial-gradient(
            circle at center,
            rgba(200, 166, 117, 0.1) 0%,
            transparent 60%
        );
        animation: raysPulse 4s ease-in-out infinite;
    }

    /* 悬浮粒子 */
    .floating-particles {
        position: absolute;
        width: 100%;
        height: 100%;
    }

    .floating-particle {
        position: absolute;
        width: 4px;
        height: 4px;
        background: var(--primary);
        border-radius: 50%;
        opacity: 0.6;
        filter: blur(1px);
        animation: floatParticle 6s ease-in-out infinite;
    }

    @keyframes rotateLeft {
        from { transform: translateY(-50%) rotate(0deg); }
        to { transform: translateY(-50%) rotate(-360deg); }
    }

    @keyframes rotateRight {
        from { transform: translateY(-50%) rotate(0deg); }
        to { transform: translateY(-50%) rotate(360deg); }
    }

    @keyframes raysPulse {
        0%, 100% { transform: scale(1); opacity: 0.3; }
        50% { transform: scale(1.2); opacity: 0.5; }
    }

    @keyframes floatParticle {
        0%, 100% { transform: translate(0, 0); opacity: 0.6; }
        50% { transform: translate(var(--tx), var(--ty)); opacity: 0.9; }
    }

    /* 添加轨道效果 */
    .orbit {
        position: absolute;
        border: 1px solid rgba(200, 166, 117, 0.1);
        border-radius: 50%;
        transform-style: preserve-3d;
    }

    .orbit-1 {
        width: 200px;
        height: 200px;
        animation: orbitRotate 20s linear infinite;
    }

    .orbit-2 {
        width: 300px;
        height: 300px;
        animation: orbitRotate 30s linear infinite reverse;
    }

    /* 添加行星效果 */
    .planet {
        position: absolute;
        border-radius: 50%;
        transform-style: preserve-3d;
        animation: planetPulse 4s ease-in-out infinite;
    }

    .planet-small-1 {
        width: 20px;
        height: 20px;
        background: radial-gradient(circle at 30% 30%,
            var(--primary-light),
            var(--primary)
        );
        box-shadow: 0 0 15px var(--primary);
        left: 25%;
        top: 15%;
    }

    .planet-small-2 {
        width: 15px;
        height: 15px;
        background: radial-gradient(circle at 30% 30%,
            var(--primary-light),
            var(--primary)
        );
        box-shadow: 0 0 12px var(--primary);
        right: 20%;
        bottom: 30%;
    }

    /* 添加连接线效果 */
    .connection-line {
        position: absolute;
        height: 1px;
        background: linear-gradient(90deg,
            transparent,
            var(--primary),
            transparent
        );
        opacity: 0.3;
        transform-origin: left center;
        animation: lineGlow 3s ease-in-out infinite;
    }

    /* 添加数据流动画 */
    .data-stream {
        position: absolute;
        width: 2px;
        height: 2px;
        background: var(--primary);
        border-radius: 50%;
        filter: blur(1px);
        opacity: 0;
        animation: dataFlow 3s linear infinite;
    }

    /* 添加全息投影效果 */
    .hologram {
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: repeating-radial-gradient(
            circle at 50%,
            transparent 0,
            rgba(200, 166, 117, 0.1) 1px,
            transparent 2px
        );
        opacity: 0.5;
        animation: hologramScan 4s linear infinite;
    }

    @keyframes planetPulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
    }

    @keyframes lineGlow {
        0%, 100% { opacity: 0.3; }
        50% { opacity: 0.6; }
    }

    @keyframes dataFlow {
        0% {
            opacity: 0;
            transform: translate(0, 0) scale(1);
        }
        50% {
            opacity: 1;
            transform: translate(var(--tx), var(--ty)) scale(1.5);
        }
        100% {
            opacity: 0;
            transform: translate(calc(var(--tx) * 2), calc(var(--ty) * 2)) scale(1);
        }
    }

    @keyframes hologramScan {
        0% { transform: translateY(-50%) rotate(0deg); }
        100% { transform: translateY(50%) rotate(360deg); }
    }

    @keyframes orbitRotate {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    /* 添加能量波纹效果 */
    .energy-ripple {
        position: absolute;
        width: 100%;
        height: 100%;
        border: 2px solid var(--primary);
        border-radius: 50%;
        opacity: 0;
        transform: scale(0.8);
        animation: rippleEffect 3s ease-out infinite;
    }

    @keyframes rippleEffect {
        0% {
            opacity: 0.5;
            transform: scale(0.8);
        }
        100% {
            opacity: 0;
            transform: scale(1.5);
        }
    }

    /* 添加小星球环线效果 */
    .planet-ring {
        position: absolute;
        border-radius: 50%;
        border: 1px solid var(--primary);
        opacity: 0.4;
        transform: rotate3d(1, 0, 1, 75deg);
        transform-style: preserve-3d;
        animation: ringRotate 10s linear infinite;
    }

    .planet-small-1 .planet-ring {
        width: 30px;
        height: 30px;
        left: -5px;
        top: -5px;
    }

    .planet-small-1 .planet-ring:nth-child(2) {
        width: 35px;
        height: 35px;
        left: -7.5px;
        top: -7.5px;
        border-color: rgba(200, 166, 117, 0.3);
        animation-direction: reverse;
        animation-duration: 15s;
    }

    .planet-small-2 .planet-ring {
        width: 25px;
        height: 25px;
        left: -5px;
        top: -5px;
    }

    .planet-small-2 .planet-ring:nth-child(2) {
        width: 28px;
        height: 28px;
        left: -6.5px;
        top: -6.5px;
        border-color: rgba(200, 166, 117, 0.3);
        animation-direction: reverse;
        animation-duration: 12s;
    }

    /* 添加光点效果 */
    .ring-particle {
        position: absolute;
        width: 2px;
        height: 2px;
        background: var(--primary);
        border-radius: 50%;
        box-shadow: 0 0 4px var(--primary);
        animation: particleGlow 2s ease-in-out infinite;
    }

    @keyframes ringRotate {
        from { transform: rotate3d(1, 0, 1, 75deg) rotateZ(0deg); }
        to { transform: rotate3d(1, 0, 1, 75deg) rotateZ(360deg); }
    }

    @keyframes particleGlow {
        0%, 100% { opacity: 0.4; transform: scale(1); }
        50% { opacity: 1; transform: scale(1.5); }
    }

    /* 数据可视化区块样式 */
    .data-visualization {
        padding: 6rem 0;
        background: linear-gradient(to bottom, rgba(0,0,0,0.8), transparent);
    }

    .chart-container {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
        margin-top: 3rem;
    }

    .chart-item {
        position: relative;
        background: rgba(200, 166, 117, 0.05);
        border-radius: 20px;
        padding: 2rem;
        height: 300px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(200, 166, 117, 0.1);
        transition: transform 0.3s ease;
    }

    .chart-item:hover {
        transform: translateY(-5px);
    }

    .chart-overlay {
        position: absolute;
        top: 1rem;
        right: 1rem;
        padding: 1rem;
        background: rgba(0, 0, 0, 0.7);
        border-radius: 10px;
        backdrop-filter: blur(5px);
    }

    .chart-info {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .chart-label {
        color: var(--gray);
        font-size: 0.9rem;
    }

    .chart-value {
        color: var(--primary);
        font-size: 1.5rem;
        font-weight: 700;
    }

    /* 联系我们区块样式优化 */
    .contact-us {
        padding: 8rem 0;
        position: relative;
        background: linear-gradient(
            to bottom,
            transparent,
            rgba(0, 0, 0, 0.4)
        );
    }

    .contact-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 4rem;
        align-items: center;
    }

    .contact-info h2 {
        font-size: 2.8rem;
        margin-bottom: 1.5rem;
        background: linear-gradient(135deg, 
            var(--primary-light), 
            var(--primary)
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        position: relative;
    }

    .contact-info h2::after {
        content: '';
        position: absolute;
        left: 0;
        bottom: -10px;
        width: 60px;
        height: 3px;
        background: var(--primary);
        border-radius: 2px;
    }

    .contact-info p {
        color: var(--gray);
        font-size: 1.1rem;
        line-height: 1.6;
        margin-bottom: 2rem;
    }

    .contact-methods {
        display: grid;
        gap: 2rem;
    }

    .contact-item {
        display: flex;
        align-items: center;
        gap: 1.5rem;
        padding: 1.5rem;
        background: rgba(200, 166, 117, 0.05);
        border-radius: 15px;
        border: 1px solid rgba(200, 166, 117, 0.1);
        transition: all 0.3s ease;
    }

    .contact-item:hover {
        transform: translateX(10px);
        background: rgba(200, 166, 117, 0.1);
        border-color: rgba(200, 166, 117, 0.3);
    }

    .contact-item i {
        font-size: 1.8rem;
        color: var(--primary);
        transition: all 0.3s ease;
    }

    .contact-item:hover i {
        transform: scale(1.2);
        color: var(--primary-light);
    }

    .contact-item span {
        font-size: 1.1rem;
        color: var(--light);
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
        .contact-info h2 {
            font-size: 2.2rem;
        }
        
        .contact-item {
            padding: 1rem;
        }
    }

    /* 增强球体效果的CSS样式，添加在现有style标签内 */
    /* 球体容器 */
    .sphere-container {
        position: relative;
        width: 180px;
        height: 180px;
        margin: 30px auto;
        perspective: 1200px;
    }

    /* 主球体 */
    .sphere {
        position: relative;
        width: 100%;
        height: 100%;
        transform-style: preserve-3d;
        animation: sphereRotate 20s linear infinite;
    }

    /* 外层球体 */
    .sphere-outer {
        position: absolute;
        width: 100%;
        height: 100%;
        transform-style: preserve-3d;
        border-radius: 50%;
    }

    /* 内层球体 */
    .sphere-inner {
        position: absolute;
        width: 60%;
        height: 60%;
        top: 20%;
        left: 20%;
        transform-style: preserve-3d;
        border-radius: 50%;
        animation: innerSphereRotate 15s linear infinite reverse;
    }

    /* 外层经线 */
    .longitude-line {
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        border: 1px solid rgba(200, 166, 117, 0.2);
        transform-style: preserve-3d;
        transform: rotateY(var(--rotation));
        box-shadow: 0 0 10px rgba(200, 166, 117, 0.1);
    }

    /* 外层纬线 */
    .latitude-line {
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        border: 1px solid rgba(200, 166, 117, 0.15);
        transform-style: preserve-3d;
        transform: rotateX(var(--rotation)) scaleY(var(--scale));
    }

    /* 内层经线 */
    .inner-longitude-line {
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        border: 1px solid rgba(200, 166, 117, 0.3);
        transform-style: preserve-3d;
        transform: rotateY(var(--rotation));
        box-shadow: 0 0 5px rgba(200, 166, 117, 0.2);
    }

    /* 内层纬线 */
    .inner-latitude-line {
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        border: 1px solid rgba(200, 166, 117, 0.25);
        transform-style: preserve-3d;
        transform: rotateX(var(--rotation)) scaleY(var(--scale));
    }

    /* 球体核心 */
    .sphere-core {
        position: absolute;
        width: 40%;
        height: 40%;
        top: 30%;
        left: 30%;
        border-radius: 50%;
        background: radial-gradient(circle at 30% 30%,
            var(--primary-light),
            var(--primary),
            var(--primary-dark)
        );
        box-shadow: 
            0 0 20px rgba(200, 166, 117, 0.6),
            inset 0 0 15px rgba(255, 255, 255, 0.8);
        animation: coreGlow 4s ease-in-out infinite alternate;
    }

    /* 球体外发光 */
    .sphere-glow {
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: radial-gradient(circle at center,
            rgba(200, 166, 117, 0.3) 0%,
            transparent 70%
        );
        filter: blur(10px);
        animation: glowPulse 4s ease-in-out infinite alternate;
    }

    /* 球体粒子 */
    .sphere-particles {
        position: absolute;
        width: 100%;
        height: 100%;
        transform-style: preserve-3d;
    }

    /* 轨道样式增强 */
    .orbit {
        position: absolute;
        border: 1px solid rgba(200, 166, 117, 0.2);
        border-radius: 50%;
        transform-style: preserve-3d;
    }

    .orbit-1 {
        width: 120%;
        height: 120%;
        top: -10%;
        left: -10%;
        animation: orbitRotate 30s linear infinite;
    }

    .orbit-2 {
        width: 140%;
        height: 140%;
        top: -20%;
        left: -20%;
        animation: orbitRotate 40s linear infinite reverse;
    }

    /* 行星环 */
    .planet-ring {
        position: absolute;
        width: 130%;
        height: 30%;
        top: 35%;
        left: -15%;
        border-radius: 50%;
        border: 1px solid var(--primary);
        opacity: 0.4;
        transform: rotateX(75deg);
        animation: ringRotate 10s linear infinite;
    }

    /* 动画效果 */
    @keyframes sphereRotate {
        0% {
            transform: rotateX(20deg) rotateY(0deg) rotateZ(10deg);
        }
        100% {
            transform: rotateX(20deg) rotateY(360deg) rotateZ(10deg);
        }
    }

    @keyframes innerSphereRotate {
        0% {
            transform: rotateX(-10deg) rotateY(0deg) rotateZ(-5deg);
        }
        100% {
            transform: rotateX(-10deg) rotateY(360deg) rotateZ(-5deg);
        }
    }

    @keyframes coreGlow {
        0% {
            box-shadow: 0 0 15px rgba(200, 166, 117, 0.4);
            opacity: 0.8;
        }
        100% {
            box-shadow: 0 0 30px rgba(200, 166, 117, 0.8);
            opacity: 1;
        }
    }

    @keyframes glowPulse {
        0% {
            opacity: 0.2;
            transform: scale(0.8);
        }
        100% {
            opacity: 0.5;
            transform: scale(1.1);
        }
    }

    @keyframes ringRotate {
        from { transform: rotateX(75deg) rotateZ(0deg); }
        to { transform: rotateX(75deg) rotateZ(360deg); }
    }

    /* 光线效果 */
    .sphere-rays {
        position: absolute;
        width: 200%;
        height: 200%;
        top: -50%;
        left: -50%;
        background: radial-gradient(
            ellipse at center,
            rgba(200, 166, 117, 0.2) 0%,
            transparent 60%
        );
        animation: raysPulse 4s ease-in-out infinite alternate;
    }

    @keyframes raysPulse {
        0% {
            opacity: 0.2;
            transform: scale(0.8);
        }
        100% {
            opacity: 0.4;
            transform: scale(1.1);
        }
    }

    /* 能量波纹 */
    .energy-ripple {
        position: absolute;
        width: 100%;
        height: 100%;
        border: 2px solid var(--primary);
        border-radius: 50%;
        opacity: 0;
        transform: scale(0.8);
        animation: rippleEffect 3s ease-out infinite;
    }

    @keyframes rippleEffect {
        0% {
            opacity: 0.5;
            transform: scale(0.8);
            border-color: var(--primary);
        }
        100% {
            opacity: 0;
            transform: scale(1.5);
            border-color: transparent;
        }
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
        .sphere-container {
            width: 150px;
            height: 150px;
        }
    }

    /* 球体粒子样式 */
    .sphere-particle {
        position: absolute;
        background: var(--primary);
        border-radius: 50%;
        left: 50%;
        top: 50%;
        box-shadow: 0 0 8px var(--primary);
        animation: particlePulse 4s ease-in-out infinite alternate;
        will-change: transform, opacity;
    }

    @keyframes particlePulse {
        0% {
            opacity: 0.2;
            transform: translate3d(var(--x, 0), var(--y, 0), var(--z, 0)) scale(0.6);
            box-shadow: 0 0 5px var(--primary);
        }
        50% {
            opacity: 1;
            transform: translate3d(
                calc(var(--x, 0) + 5px), 
                calc(var(--y, 0) + 5px), 
                calc(var(--z, 0) + 5px)
            ) scale(1.2);
            box-shadow: 0 0 10px var(--primary), 0 0 20px var(--primary-light);
        }
        100% {
            opacity: 0.3;
            transform: translate3d(var(--x, 0), var(--y, 0), var(--z, 0)) scale(0.8);
            box-shadow: 0 0 6px var(--primary);
        }
    }

    /* 星球位置和样式优化 */
    .planet-small-1 {
        position: absolute;
        width: 15px;
        height: 15px;
        top: 25%;
        right: 15%;
        background: radial-gradient(circle at 30% 30%,
            var(--primary-light),
            var(--primary)
        );
        border-radius: 50%;
        box-shadow: 0 0 15px var(--primary-light);
        animation: planetHover 5s ease-in-out infinite alternate;
    }

    .planet-small-2 {
        position: absolute;
        width: 10px;
        height: 10px;
        bottom: 25%;
        left: 20%;
        background: radial-gradient(circle at 30% 30%,
            var(--primary-light),
            var(--primary)
        );
        border-radius: 50%;
        box-shadow: 0 0 12px var(--primary-light);
        animation: planetHover 7s ease-in-out infinite alternate-reverse;
    }

    @keyframes planetHover {
        0% {
            transform: translateY(0) scale(1);
            box-shadow: 0 0 12px var(--primary);
        }
        100% {
            transform: translateY(8px) scale(1.1);
            box-shadow: 0 0 20px var(--primary-light);
        }
    }

    /* 给整个球体添加悬停时的视觉反馈 */
    .sphere-container:hover .sphere-outer {
        filter: brightness(1.2);
    }

    .sphere-container:hover .sphere-core {
        box-shadow: 
            0 0 30px rgba(200, 166, 117, 0.8),
            inset 0 0 25px rgba(255, 255, 255, 0.9);
    }

    .sphere-container:hover .energy-ripple {
        animation-duration: 2s;
    }
    
    /* 左右两侧小球的样式 */
    .sphere-container {
        position: relative;
        width: 180px;
        height: 180px;
        margin: 30px auto;
        perspective: 1200px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .sphere-side {
        position: absolute;
        width: 70px;
        height: 70px;
        z-index: 2;
        perspective: 800px;
        animation: sideSphereFloat 6s ease-in-out infinite alternate;
    }

    .sphere-left {
        left: -100px;
        animation-delay: -2s;
    }

    .sphere-right {
        right: -100px;
        animation-delay: -4s;
    }

    @keyframes sideSphereFloat {
        0% {
            transform: translateY(-5px) translateZ(0);
        }
        100% {
            transform: translateY(5px) translateZ(0);
        }
    }

    .sphere-side-inner {
        position: relative;
        width: 100%;
        height: 100%;
        transform-style: preserve-3d;
        animation: sideSphereSpin 15s linear infinite;
    }

    .sphere-left .sphere-side-inner {
        animation-direction: reverse;
    }

    @keyframes sideSphereSpin {
        0% {
            transform: rotateX(20deg) rotateY(0deg) rotateZ(10deg);
        }
        100% {
            transform: rotateX(20deg) rotateY(360deg) rotateZ(10deg);
        }
    }

    .sphere-side-core {
        position: absolute;
        width: 70%;
        height: 70%;
        top: 15%;
        left: 15%;
        border-radius: 50%;
        background: radial-gradient(circle at 30% 30%,
            var(--primary-light),
            var(--primary) 60%,
            var(--primary-dark) 100%);
        box-shadow: 
            0 0 15px rgba(200, 166, 117, 0.6),
            inset 0 0 10px rgba(255, 255, 255, 0.8);
        animation: coreGlow 3s ease-in-out infinite alternate;
    }

    .sphere-side-ring {
        position: absolute;
        width: 120%;
        height: 120%;
        top: -10%;
        left: -10%;
        border-radius: 50%;
        border: 1px solid var(--primary);
        box-shadow: 0 0 8px rgba(200, 166, 117, 0.3);
        opacity: 0.6;
        animation: ringRotate 10s linear infinite;
    }

    .sphere-left .sphere-side-ring {
        animation-direction: reverse;
    }

    .sphere-side-glow {
        position: absolute;
        width: 140%;
        height: 140%;
        top: -20%;
        left: -20%;
        border-radius: 50%;
        background: radial-gradient(
            circle at center,
            rgba(200, 166, 117, 0.3) 0%,
            transparent 70%
        );
        filter: blur(8px);
        opacity: 0.5;
        animation: glowPulse 4s ease-in-out infinite alternate;
    }

    .sphere-side-particle {
        position: absolute;
        width: 4px;
        height: 4px;
        background: var(--primary-light);
        border-radius: 50%;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        filter: blur(1px);
        box-shadow: 0 0 5px var(--primary);
        animation: particleFloat 4s ease-in-out infinite alternate;
    }

    @keyframes particleFloat {
        0% {
            transform: translate(-50%, -50%) scale(0.8);
            opacity: 0.5;
        }
        100% {
            transform: translate(calc(-50% + var(--tx, 0)), calc(-50% + var(--ty, 0))) scale(1.2);
            opacity: 0.9;
            box-shadow: 0 0 8px var(--primary-light);
        }
    }

    /* 连接线样式 */
    .sphere-connector {
        position: absolute;
        height: 1px;
        background: linear-gradient(90deg,
            transparent 0%,
            var(--primary) 50%,
            transparent 100%);
        top: 50%;
        transform: translateY(-50%);
        opacity: 0.4;
        animation: connectorPulse 4s ease-in-out infinite alternate;
        z-index: 1;
    }

    .left-connector {
        left: -90px;
        width: 80px;
    }

    .right-connector {
        right: -90px;
        width: 80px;
    }

    @keyframes connectorPulse {
        0% {
            opacity: 0.2;
            box-shadow: 0 0 2px var(--primary);
        }
        100% {
            opacity: 0.6;
            box-shadow: 0 0 8px var(--primary);
        }
    }

    /* 悬停效果 */
    .sphere-container:hover .sphere-side-core {
        animation-duration: 1.5s;
        box-shadow: 0 0 20px rgba(200, 166, 117, 0.8);
    }

    .sphere-container:hover .sphere-side-ring {
        animation-duration: 5s;
        opacity: 0.8;
    }

    .sphere-container:hover .sphere-connector {
        animation-duration: 2s;
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
        .sphere-side {
            width: 50px;
            height: 50px;
        }
        
        .sphere-left {
            left: -70px;
        }
        
        .sphere-right {
            right: -70px;
        }
        
        .left-connector {
            left: -65px;
            width: 60px;
        }
        
        .right-connector {
            right: -65px;
            width: 60px;
        }
    }

    @media (max-width: 576px) {
        .sphere-side {
            display: none;
        }
        
        .sphere-connector {
            display: none;
        }
    }

    /* 更新左右两侧小球样式为虚线构成 */
    .sphere-side-core {
        display: none; /* 隐藏实心核心 */
    }

    .sphere-side-ring {
        display: none; /* 隐藏原有实心环 */
    }

    /* 添加虚线环样式 */
    .sphere-side-dotted-ring {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        border-radius: 50%;
        border: 1px dashed var(--primary);
        box-shadow: 0 0 6px rgba(200, 166, 117, 0.2);
        opacity: 0.7;
        animation: ringRotate 20s linear infinite;
    }

    /* 调整虚线环的样式使不同环有细微差异 */
    .sphere-side-dotted-ring:nth-child(1) {
        width: 90%;
        height: 90%;
        top: 5%;
        left: 5%;
        border: 1px dotted var(--primary-light);
        animation-duration: 15s;
    }

    .sphere-side-dotted-ring:nth-child(2) {
        width: 110%;
        height: 110%;
        top: -5%;
        left: -5%;
        border: 1px dashed var(--primary);
        animation-duration: 25s;
    }

    .sphere-side-dotted-ring:nth-child(3) {
        width: 70%;
        height: 70%;
        top: 15%;
        left: 15%;
        border: 1px dotted var(--primary-dark);
        animation-duration: 18s;
        animation-direction: reverse;
    }

    .sphere-side-dotted-ring:nth-child(4) {
        width: 130%;
        height: 130%;
        top: -15%;
        left: -15%;
        border: 1px dashed var(--primary-light);
        opacity: 0.4;
        animation-duration: 30s;
        animation-direction: reverse;
    }

    /* 更新光晕效果 */
    .sphere-side-glow {
        opacity: 0.3;
        filter: blur(10px);
    }

    /* 增强粒子效果 */
    .sphere-side-particle {
        background: var(--primary);
        width: 2px;
        height: 2px;
        opacity: 0.8;
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
        .sphere-side-dotted-ring:nth-child(4) {
            display: none; /* 在中等屏幕上隐藏最外层环 */
        }
    }

    @media (max-width: 576px) {
        .sphere-side {
            display: none;
        }
        
        .sphere-connector {
            display: none;
        }
    }

    /* 线条球体的样式 */
    .sphere-side-lines {
        position: absolute;
        width: 100%;
        height: 100%;
        transform-style: preserve-3d;
        border-radius: 50%;
    }

    .sphere-line {
        position: absolute;
        top: 50%;
        left: 50%;
        transform-origin: center;
        border-radius: 50px;
        background: linear-gradient(90deg, 
            rgba(200, 166, 117, 0.1), 
            rgba(200, 166, 117, 0.7) 50%, 
            rgba(200, 166, 117, 0.1)
        );
        box-shadow: 0 0 8px rgba(200, 166, 117, 0.4);
        animation: linePulse 4s ease-in-out infinite alternate;
    }

    .horizontal-line {
        width: 100%;
        height: 1px;
        margin-top: -0.5px;
        margin-left: -50%;
    }

    .vertical-line {
        width: 1px;
        height: 100%;
        margin-top: -50%;
        margin-left: -0.5px;
    }

    .diagonal-line, .cross-line {
        width: 140%;
        height: 1px;
        margin-top: -0.5px;
        margin-left: -70%;
    }

    .sphere-side-core-dot {
        position: absolute;
        width: 4px;
        height: 4px;
        background-color: var(--primary);
        border-radius: 50%;
        top: 50%;
        left: 50%;
        margin-top: -2px;
        margin-left: -2px;
        box-shadow: 0 0 10px var(--primary-light);
        animation: corePulse 2s ease-in-out infinite;
    }

    @keyframes linePulse {
        0% {
            opacity: 0.4;
        }
        50% {
            opacity: 0.8;
        }
        100% {
            opacity: 0.4;
        }
    }

    @keyframes corePulse {
        0%, 100% {
            transform: scale(1);
            box-shadow: 0 0 10px var(--primary-light);
        }
        50% {
            transform: scale(1.5);
            box-shadow: 0 0 15px var(--primary-light);
        }
    }

    /* 保留其他必要的样式 */
    .sphere-side {
        position: absolute;
        width: 70px;
        height: 70px;
        z-index: 2;
        perspective: 800px;
        animation: sideSphereFloat 6s ease-in-out infinite alternate;
    }

    .sphere-left {
        left: -100px;
        animation-delay: -2s;
    }

    .sphere-right {
        right: -100px;
        animation-delay: -4s;
    }

    .sphere-side-inner {
        position: relative;
        width: 100%;
        height: 100%;
        transform-style: preserve-3d;
        animation: sideSphereSpin 20s linear infinite;
    }

    .sphere-left .sphere-side-inner {
        animation-direction: reverse;
    }

    @keyframes sideSphereSpin {
        0% {
            transform: rotateX(20deg) rotateY(0deg) rotateZ(10deg);
        }
        100% {
            transform: rotateX(20deg) rotateY(360deg) rotateZ(10deg);
        }
    }

    .sphere-side-glow {
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: radial-gradient(circle at center,
            rgba(200, 166, 117, 0.2) 0%,
            transparent 70%
        );
        filter: blur(5px);
        animation: glowPulse 4s ease-in-out infinite alternate;
    }

    @keyframes sideSphereFloat {
        0% {
            transform: translateY(-5px) translateZ(0);
        }
        100% {
            transform: translateY(5px) translateZ(0);
        }
    }

    @media (max-width: 768px) {
        .sphere-side {
            width: 50px;
            height: 50px;
        }
        
        .sphere-left {
            left: -70px;
        }
        
        .sphere-right {
            right: -70px;
        }
    }

    @media (max-width: 576px) {
        .sphere-side {
            display: none;
        }
    }

    /* 卫星轨道样式 */
    .satellite-orbit {
        position: absolute;
        width: 150%;
        height: 150%;
        border: 1px dashed rgba(200, 166, 117, 0.2);
        border-radius: 50%;
        top: -25%;
        left: -25%;
        transform-style: preserve-3d;
        animation: orbitRotate 15s linear infinite;
    }

    .orbit-left-1 {
        transform: rotateX(70deg) rotateY(10deg);
        animation-direction: normal;
    }

    .orbit-left-2 {
        width: 130%;
        height: 130%;
        top: -15%;
        left: -15%;
        animation-duration: 12s;
        animation-direction: reverse;
    }

    .orbit-right-1 {
        transform: rotateX(70deg) rotateY(-10deg);
        animation-direction: reverse;
    }

    .orbit-right-2 {
        width: 130%;
        height: 130%;
        top: -15%;
        left: -15%;
        animation-duration: 12s;
        animation-direction: normal;
    }

    /* 卫星样式 */
    .satellite {
        position: absolute;
        width: 10px;
        height: 10px;
        background: radial-gradient(circle at center, var(--primary-light), var(--primary));
        border-radius: 50%;
        box-shadow: 0 0 10px var(--primary-light);
        transform-style: preserve-3d;
        transform-origin: center;
        animation: satellitePulse 3s ease-in-out infinite alternate;
    }

    .satellite::before {
        content: '';
        position: absolute;
        width: 14px;
        height: 2px;
        background: linear-gradient(90deg, transparent, var(--primary), transparent);
        top: 4px;
        left: -2px;
        box-shadow: 0 0 5px var(--primary);
        border-radius: 2px;
    }

    .satellite-left-1 {
        top: 0;
        left: 20%;
    }

    .satellite-left-2 {
        top: 70%;
        left: 10%;
    }

    .satellite-right-1 {
        top: 0;
        left: 80%;
    }

    .satellite-right-2 {
        top: 70%;
        left: 90%;
    }

    @keyframes orbitRotate {
        0% {
            transform: rotateZ(0deg);
        }
        100% {
            transform: rotateZ(360deg);
        }
    }

    @keyframes satellitePulse {
        0% {
            box-shadow: 0 0 5px var(--primary-light);
            transform: scale(0.9);
        }
        100% {
            box-shadow: 0 0 15px var(--primary-light);
            transform: scale(1.1);
        }
    }

    .orbit-left-1, .orbit-right-1 {
        opacity: 0.7;
    }

    .orbit-left-2, .orbit-right-2 {
        opacity: 0.5;
    }

    @media (max-width: 768px) {
        .satellite {
            width: 8px;
            height: 8px;
        }
        
        .satellite::before {
            width: 10px;
            height: 1.5px;
            top: 3.25px;
            left: -1px;
        }
    }

    @media (max-width: 576px) {
        .satellite-orbit {
            display: none;
        }
    }

    /* 联系我们部分的虚线球体样式 */
    .contact-us {
        position: relative;
        overflow: hidden;
    }
    
    /* 左侧球体 */
    .contact-sphere-left {
        position: absolute;
        left: -50px;
        top: 50%;
        transform: translateY(-50%);
        width: 150px;
        height: 150px;
        opacity: 0.3;
        z-index: 0;
        pointer-events: none;
    }
    
    /* 右侧球体 */
    .contact-sphere-right {
        position: absolute;
        right: -50px;
        top: 50%;
        transform: translateY(-50%);
        width: 150px;
        height: 150px;
        opacity: 0.3;
        z-index: 0;
        pointer-events: none;
    }
    
    /* 球体内部容器，添加3D旋转动画 */
    .contact-sphere-inner {
        position: relative;
        width: 100%;
        height: 100%;
        animation: contactSphereRotate 20s linear infinite;
        transform-style: preserve-3d;
    }
    
    .contact-sphere-left .contact-sphere-inner {
        animation-direction: reverse;
    }
    
    @keyframes contactSphereRotate {
        0% { transform: rotateX(20deg) rotateY(0deg) rotateZ(0deg); }
        100% { transform: rotateX(20deg) rotateY(360deg) rotateZ(0deg); }
    }
    
    /* 虚线圆环样式 */
    .contact-sphere-ring {
        position: absolute;
        border: 1px dashed var(--primary);
        border-radius: 50%;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
    }
    
    .contact-sphere-ring:nth-child(1) {
        transform: rotateX(60deg);
    }
    
    .contact-sphere-ring:nth-child(2) {
        transform: rotateY(60deg);
        width: 80%;
        height: 80%;
        top: 10%;
        left: 10%;
    }
    
    .contact-sphere-ring:nth-child(3) {
        transform: rotateZ(60deg);
        width: 60%;
        height: 60%;
        top: 20%;
        left: 20%;
    }
    
    /* 虚线经纬线样式 */
    .contact-sphere-line {
        position: absolute;
        background: none;
        border: 1px dashed var(--primary);
    }
    
    .contact-sphere-line.horizontal {
        width: 100%;
        height: 0;
        top: 50%;
        left: 0;
    }
    
    .contact-sphere-line.vertical {
        width: 0;
        height: 100%;
        top: 0;
        left: 50%;
    }
    
    .contact-sphere-line.diagonal-1 {
        width: 142%;
        height: 0;
        top: 50%;
        left: -21%;
        transform: rotate(45deg);
    }
    
    .contact-sphere-line.diagonal-2 {
        width: 142%;
        height: 0;
        top: 50%;
        left: -21%;
        transform: rotate(-45deg);
    }
    
    /* 中心点样式 */
    .contact-sphere-dot {
        position: absolute;
        width: 6px;
        height: 6px;
        background: var(--primary);
        border-radius: 50%;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        box-shadow: 0 0 10px var(--primary-light);
        animation: pulseDot 2s ease-in-out infinite alternate;
    }
    
    @keyframes pulseDot {
        from {
            transform: translate(-50%, -50%) scale(0.8);
            opacity: 0.6;
        }
        to {
            transform: translate(-50%, -50%) scale(1.2);
            opacity: 1;
        }
    }
    
    /* 响应式调整 */
    @media (max-width: 992px) {
        .contact-sphere-left, .contact-sphere-right {
            width: 120px;
            height: 120px;
        }
    }
    
    @media (max-width: 768px) {
        .contact-sphere-left {
            left: -80px;
        }
        
        .contact-sphere-right {
            right: -80px;
        }
    }
    
    @media (max-width: 576px) {
        .contact-sphere-left, .contact-sphere-right {
            display: none;
        }
    }

    /* 改进支付信息框样式 */
    .payment-info-box {
        margin-top: 3rem;
        padding: 2.5rem;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        overflow: visible;
    }
    
    .payment-info-box::before {
        content: '';
        position: absolute;
        top: 0;
        left: 10%;
        right: 10%;
        height: 1px;
        background: linear-gradient(90deg, 
            transparent,
            var(--primary),
            transparent
        );
    }
    
    .payment-info-box::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 10%;
        right: 10%;
        height: 1px;
        background: linear-gradient(90deg, 
            transparent,
            var(--primary),
            transparent
        );
    }
    
    /* 内容样式增强 */
    .info-content {
        position: relative;
        z-index: 2;
        padding: 2rem;
        background: rgba(0, 0, 0, 0.4);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        border: 1px solid rgba(200, 166, 117, 0.2);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        width: 100%;
        max-width: 600px;
    }
    
    .info-title {
        font-size: 2rem;
        color: var(--primary-light);
        margin-bottom: 1rem;
        text-align: center;
        text-shadow: 0 0 10px rgba(200, 166, 117, 0.5);
    }
    
    .info-text {
        color: var(--gray);
        font-size: 1.1rem;
        margin-bottom: 1.5rem;
        text-align: center;
    }
    
    .info-highlight {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 1.5rem;
    }
    
    .info-stat {
        font-size: 3rem;
        font-weight: 700;
        background: linear-gradient(135deg, var(--primary-light), var(--primary));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        text-shadow: 0 0 15px rgba(200, 166, 117, 0.8);
        animation: statPulse 2s ease-in-out infinite alternate;
    }
    
    @keyframes statPulse {
        from { transform: scale(1); text-shadow: 0 0 10px rgba(200, 166, 117, 0.5); }
        to { transform: scale(1.05); text-shadow: 0 0 20px rgba(200, 166, 117, 0.8); }
    }
    
    /* 添加安全图标组样式 */
    .security-icons {
        display: flex;
        justify-content: center;
        gap: 2rem;
        margin-top: 1rem;
    }
    
    .security-icon {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
    }
    
    .security-icon i {
        font-size: 1.5rem;
        color: var(--primary);
        background: rgba(0, 0, 0, 0.3);
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        border: 1px solid rgba(200, 166, 117, 0.3);
        transition: all 0.3s ease;
    }
    
    .security-icon:hover i {
        color: var(--primary-light);
        transform: scale(1.1);
        box-shadow: 0 0 15px rgba(200, 166, 117, 0.4);
    }
    
    .security-icon span {
        font-size: 0.9rem;
        color: var(--gray);
    }
    
    /* 左右虚线球体样式 */
    .info-sphere-left, .info-sphere-right {
        position: absolute;
        width: 80px;
        height: 80px;
        z-index: 1;
        opacity: 0.6;
        pointer-events: none;
    }
    
    .info-sphere-left {
        left: 10%;
        top: 50%;
        transform: translateY(-50%);
    }
    
    .info-sphere-right {
        right: 10%;
        top: 50%;
        transform: translateY(-50%);
    }
    
    .info-sphere-inner {
        position: relative;
        width: 100%;
        height: 100%;
        animation: infoSphereRotate 15s linear infinite;
        transform-style: preserve-3d;
    }
    
    .info-sphere-left .info-sphere-inner {
        animation-direction: reverse;
    }
    
    @keyframes infoSphereRotate {
        0% { transform: rotateX(20deg) rotateY(0deg) rotateZ(0deg); }
        100% { transform: rotateX(20deg) rotateY(360deg) rotateZ(0deg); }
    }
    
    /* 虚线样式 */
    .info-sphere-ring {
        position: absolute;
        border: 1px dashed var(--primary);
        border-radius: 50%;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
    }
    
    .info-sphere-ring:nth-child(1) {
        transform: rotateX(60deg);
    }
    
    .info-sphere-ring:nth-child(2) {
        transform: rotateY(60deg);
    }
    
    .info-sphere-line {
        position: absolute;
        background: none;
        border: 1px dashed var(--primary);
    }
    
    .info-sphere-line.horizontal {
        width: 100%;
        height: 0;
        top: 50%;
        left: 0;
    }
    
    .info-sphere-line.vertical {
        width: 0;
        height: 100%;
        top: 0;
        left: 50%;
    }
    
    .info-sphere-line.diagonal-1 {
        width: 140%;
        height: 0;
        top: 50%;
        left: -20%;
        transform: rotate(45deg);
    }
    
    .info-sphere-line.diagonal-2 {
        width: 140%;
        height: 0;
        top: 50%;
        left: -20%;
        transform: rotate(-45deg);
    }
    
    .info-sphere-dot {
        position: absolute;
        width: 4px;
        height: 4px;
        background: var(--primary);
        border-radius: 50%;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        box-shadow: 0 0 8px var(--primary-light);
        animation: dotPulse 2s ease-in-out infinite alternate;
    }
    
    @keyframes dotPulse {
        from { transform: translate(-50%, -50%) scale(0.8); opacity: 0.6; }
        to { transform: translate(-50%, -50%) scale(1.2); opacity: 1; }
    }
    
    /* 数据流线条装饰 */
    .info-data-lines {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 0;
    }
    
    .data-line {
        position: absolute;
        height: 1px;
        background: linear-gradient(90deg, 
            transparent,
            rgba(200, 166, 117, 0.3),
            transparent
        );
        opacity: 0;
        animation: dataLineFlow 4s ease-in-out infinite;
    }
    
    .line-1 {
        width: 40%;
        top: 30%;
        left: 20%;
        animation-delay: 0s;
    }
    
    .line-2 {
        width: 35%;
        top: 55%;
        right: 15%;
        animation-delay: 1.5s;
    }
    
    .line-3 {
        width: 50%;
        bottom: 25%;
        left: 25%;
        animation-delay: 3s;
    }
    
    @keyframes dataLineFlow {
        0% { transform: translateX(-100%); opacity: 0; }
        50% { opacity: 0.8; }
        100% { transform: translateX(100%); opacity: 0; }
    }
    
    .data-particle {
        position: absolute;
        width: 4px;
        height: 4px;
        background: var(--primary);
        border-radius: 50%;
        opacity: 0;
        box-shadow: 0 0 8px var(--primary-light);
        animation: particleMove 5s ease-in-out infinite;
    }
    
    .p-1 {
        top: 35%;
        left: 15%;
        animation-delay: 1s;
    }
    
    .p-2 {
        top: 65%;
        right: 20%;
        animation-delay: 2.5s;
    }
    
    .p-3 {
        bottom: 25%;
        left: 35%;
        animation-delay: 4s;
    }
    
    @keyframes particleMove {
        0% { transform: translate(0, 0); opacity: 0; }
        25% { opacity: 0.8; }
        50% { transform: translate(40px, -20px); opacity: 0.8; }
        75% { opacity: 0.4; }
        100% { transform: translate(80px, -40px); opacity: 0; }
    }
    
    /* 响应式调整 */
    @media (max-width: 992px) {
        .info-sphere-left, .info-sphere-right {
            width: 60px;
            height: 60px;
        }
        
        .info-title {
            font-size: 1.8rem;
        }
        
        .info-stat {
            font-size: 2.5rem;
        }
    }
    
    @media (max-width: 768px) {
        .info-sphere-left {
            left: 5%;
        }
        
        .info-sphere-right {
            right: 5%;
        }
        
        .security-icons {
            gap: 1rem;
        }
        
        .security-icon i {
            width: 40px;
            height: 40px;
            font-size: 1.2rem;
        }
    }
    
    @media (max-width: 576px) {
        .info-sphere-left, 
        .info-sphere-right {
            display: none;
        }
        
        .info-content {
            padding: 1.5rem;
        }
    }
    </style>
</head>
<body>
    <div class="stars-container"></div>
    <div class="background-animation"></div>
    
    <header class="header">
        <div class="container">
            <a href="/" class="logo">
                {if !empty($logo)}
                <img src="{$logo}" alt="{$siteName}" class="logo-img">
                {else}
                <i class="fas fa-credit-card"></i>
                {/if}
            </a>
            
            <nav class="nav-links">
                {foreach $navItems as $nav}
                <div class="nav-item">
                    <a href="{$nav.href}" class="nav-link {if !empty($nav.children)}has-arrow{/if}">
                        {$nav.name}
                        {if !empty($nav.children)}
                        <svg class="dropdown-arrow" width="8" height="6" viewBox="0 0 8 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M4 6L0 0L8 0L4 6Z" fill="var(--primary)"/>
                        </svg>
                        {/if}
                    </a>
                    {if !empty($nav.children)}
                    <div class="submenu">
                        {foreach $nav.children as $child}
                        <a href="{$child.href}">{$child.name}</a>
                        {/foreach}
                    </div>
                    {/if}
                </div>
                {/foreach}
            </nav>
            <div class="auth-buttons">
                <a href="/merchant/login" class="btn btn-login">
                    <svg class="auth-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M20 21V19C20 16.7909 18.2091 15 16 15H8C5.79086 15 4 16.7909 4 19V21" stroke="var(--primary)" stroke-width="2" stroke-linecap="round"/>
                        <circle cx="12" cy="7" r="4" stroke="var(--primary)" stroke-width="2"/>
                    </svg>
                    商户登录
                </a>
                <a href="/merchant/register" class="btn btn-register">
                    <svg class="auth-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M16 21V19C16 16.7909 14.2091 15 12 15H8C5.79086 15 4 16.7909 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        <circle cx="10" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                        <path d="M20 8V14M17 11H23" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                    </svg>
                    商户注册
                </a>
            </div>
            <button class="mobile-menu-btn">
                <i class="fas fa-bars"></i>
            </button>
        </div>
    </header>

    <section class="hero">
        <div class="planets-container">
            <div class="planet-orbit orbit-1"></div>
            <div class="planet-orbit orbit-2"></div>
            <div class="planet-orbit orbit-3"></div>
            <div class="planet planet-main"></div>
            <div class="planet planet-main-left"></div>
            <div class="planet planet-small-1"></div>
            <div class="planet planet-small-2"></div>
        </div>
        <div class="container hero-content">
            <h1>24H自动发货</h1>
            <h2>{$siteName}</h2>
            <p style="font-size: 1rem; color: #999; margin-top: 1rem;">服务器数据库集群化部署，金融级容灾方案，客户信息数据安全放在第一位</p>
            
            <!-- 添加3D球体容器 -->
            <div class="sphere-container">
                <!-- 左侧小球 - 改为线条结构 -->
                <div class="sphere-side sphere-left">
                    <div class="sphere-side-inner">
                        <!-- 线条构成的球体结构 -->
                        <div class="sphere-side-lines">
                            <!-- 水平线 -->
                            <div class="sphere-line horizontal-line" style="transform: rotateX(0deg) translateY(-40%) scale(0.8);"></div>
                            <div class="sphere-line horizontal-line" style="transform: rotateX(0deg) translateY(-20%) scale(0.9);"></div>
                            <div class="sphere-line horizontal-line" style="transform: rotateX(0deg) translateY(0%) scale(1);"></div>
                            <div class="sphere-line horizontal-line" style="transform: rotateX(0deg) translateY(20%) scale(0.9);"></div>
                            <div class="sphere-line horizontal-line" style="transform: rotateX(0deg) translateY(40%) scale(0.8);"></div>
                            
                            <!-- 垂直线 -->
                            <div class="sphere-line vertical-line" style="transform: rotateY(0deg) translateX(-40%) scale(0.8);"></div>
                            <div class="sphere-line vertical-line" style="transform: rotateY(0deg) translateX(-20%) scale(0.9);"></div>
                            <div class="sphere-line vertical-line" style="transform: rotateY(0deg) translateX(0%) scale(1);"></div>
                            <div class="sphere-line vertical-line" style="transform: rotateY(0deg) translateX(20%) scale(0.9);"></div>
                            <div class="sphere-line vertical-line" style="transform: rotateY(0deg) translateX(40%) scale(0.8);"></div>
                            
                            <!-- 对角线 -->
                            <div class="sphere-line diagonal-line" style="transform: rotateZ(45deg) scale(1);"></div>
                            <div class="sphere-line diagonal-line" style="transform: rotateZ(135deg) scale(1);"></div>
                            
                            <!-- 交叉线 -->
                            <div class="sphere-line cross-line" style="transform: rotateY(45deg) rotateX(45deg) scale(0.7);"></div>
                            <div class="sphere-line cross-line" style="transform: rotateY(-45deg) rotateX(-45deg) scale(0.7);"></div>
                            <div class="sphere-line cross-line" style="transform: rotateY(45deg) rotateX(-45deg) scale(0.7);"></div>
                            <div class="sphere-line cross-line" style="transform: rotateY(-45deg) rotateX(45deg) scale(0.7);"></div>
                            
                            <!-- 中心小光点 -->
                            <div class="sphere-side-core-dot"></div>
                            
                            <!-- 外部光晕 -->
                            <div class="sphere-side-glow"></div>
                        </div>
                        
                        <!-- 卫星轨道和卫星 -->
                        <div class="satellite-orbit orbit-left-1">
                            <div class="satellite satellite-left-1"></div>
                        </div>
                        <div class="satellite-orbit orbit-left-2" style="transform: rotateX(60deg) rotateY(30deg);">
                            <div class="satellite satellite-left-2"></div>
                        </div>
                    </div>
                </div>

                <!-- 中间球体保持不变 -->
                <div class="sphere">
                    <!-- 外层大球 -->
                    <div class="sphere-outer">
                        {for start="0" end="180" step="6"}
                        <div class="longitude-line" style="--rotation: {$i}deg"></div>
                        {/for}
                        {for start="10", end="170" step="10"}
                        <div class="latitude-line" style="--rotation: {$i}deg; --scale: {echo abs(cos($i * 3.14159 / 180))}"></div>
                        {/for}
                        <div class="sphere-glow"></div>
                    </div>
                    
                    <!-- 内层小球 -->
                    <div class="sphere-inner">
                        {for start="0" end="180" step="15"}
                        <div class="inner-longitude-line" style="--rotation: {$i}deg"></div>
                        {/for}
                        {for start="10" end="170" step="15"}
                        <div class="inner-latitude-line" style="--rotation: {$i}deg; --scale: {echo abs(cos($i * 3.14159 / 180))}"></div>
                        {/for}
                        <div class="sphere-core"></div>
                    </div>
                    
                    <!-- 装饰元素 -->
                    <div class="sphere-particles"></div>
                    <div class="sphere-rays"></div>
                    <div class="energy-ripple"></div>
                    <div class="energy-ripple" style="animation-delay: 1.5s;"></div>
                </div>
                
                <!-- 右侧小球 - 改为线条结构 -->
                <div class="sphere-side sphere-right">
                    <div class="sphere-side-inner">
                        <!-- 线条构成的球体结构 -->
                        <div class="sphere-side-lines">
                            <!-- 水平线 -->
                            <div class="sphere-line horizontal-line" style="transform: rotateX(0deg) translateY(-40%) scale(0.8);"></div>
                            <div class="sphere-line horizontal-line" style="transform: rotateX(0deg) translateY(-20%) scale(0.9);"></div>
                            <div class="sphere-line horizontal-line" style="transform: rotateX(0deg) translateY(0%) scale(1);"></div>
                            <div class="sphere-line horizontal-line" style="transform: rotateX(0deg) translateY(20%) scale(0.9);"></div>
                            <div class="sphere-line horizontal-line" style="transform: rotateX(0deg) translateY(40%) scale(0.8);"></div>
                            
                            <!-- 垂直线 -->
                            <div class="sphere-line vertical-line" style="transform: rotateY(0deg) translateX(-40%) scale(0.8);"></div>
                            <div class="sphere-line vertical-line" style="transform: rotateY(0deg) translateX(-20%) scale(0.9);"></div>
                            <div class="sphere-line vertical-line" style="transform: rotateY(0deg) translateX(0%) scale(1);"></div>
                            <div class="sphere-line vertical-line" style="transform: rotateY(0deg) translateX(20%) scale(0.9);"></div>
                            <div class="sphere-line vertical-line" style="transform: rotateY(0deg) translateX(40%) scale(0.8);"></div>
                            
                            <!-- 对角线 -->
                            <div class="sphere-line diagonal-line" style="transform: rotateZ(45deg) scale(1);"></div>
                            <div class="sphere-line diagonal-line" style="transform: rotateZ(135deg) scale(1);"></div>
                            
                            <!-- 交叉线 -->
                            <div class="sphere-line cross-line" style="transform: rotateY(45deg) rotateX(45deg) scale(0.7);"></div>
                            <div class="sphere-line cross-line" style="transform: rotateY(-45deg) rotateX(-45deg) scale(0.7);"></div>
                            <div class="sphere-line cross-line" style="transform: rotateY(45deg) rotateX(-45deg) scale(0.7);"></div>
                            <div class="sphere-line cross-line" style="transform: rotateY(-45deg) rotateX(45deg) scale(0.7);"></div>
                            
                            <!-- 中心小光点 -->
                            <div class="sphere-side-core-dot"></div>
                            
                            <!-- 外部光晕 -->
                            <div class="sphere-side-glow"></div>
                        </div>
                        
                        <!-- 卫星轨道和卫星 -->
                        <div class="satellite-orbit orbit-right-1">
                            <div class="satellite satellite-right-1"></div>
                        </div>
                        <div class="satellite-orbit orbit-right-2" style="transform: rotateX(60deg) rotateY(-30deg);">
                            <div class="satellite satellite-right-2"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 小球之间的连接线 -->
                <div class="sphere-connector left-connector"></div>
                <div class="sphere-connector right-connector"></div>

                <!-- 添加缺失的容器元素 -->
                <div class="sphere-decorations"></div>
                <div class="floating-particles"></div>
                <div class="sphere-grid" style="display: none;"></div>
            </div>

            <div class="stats">
                <div class="stat-item" data-aos="fade-up" data-aos-delay="100">
                    <div class="stat-item-inner">
                        <div class="stat-icon-wrapper">
                            <i class="fas fa-shopping-cart stat-icon"></i>
                            <div class="icon-glow"></div>
                        </div>
                        <div class="stat-number" data-value="{$stats_orders}">{$stats_orders}</div>
                        <div class="stat-title">完成订单</div>
                        <div class="stat-decoration">
                            <span class="stat-line"></span>
                            <span class="stat-dot"></span>
                            <span class="stat-line"></span>
                        </div>
                    </div>
                </div>
                <div class="stat-item" data-aos="fade-up" data-aos-delay="200">
                    <div class="stat-item-inner">
                        <div class="stat-icon-wrapper">
                            <i class="fas fa-credit-card stat-icon"></i>
                            <div class="icon-glow"></div>
                        </div>
                        <div class="stat-number" data-value="{$stats_cards}">{$stats_cards}</div>
                        <div class="stat-title">发卡次数</div>
                        <div class="stat-decoration">
                            <span class="stat-line"></span>
                            <span class="stat-dot"></span>
                            <span class="stat-line"></span>
                        </div>
                    </div>
                </div>
                <div class="stat-item" data-aos="fade-up" data-aos-delay="300">
                    <div class="stat-item-inner">
                        <div class="stat-icon-wrapper">
                            <i class="fas fa-store stat-icon"></i>
                            <div class="icon-glow"></div>
                        </div>
                        <div class="stat-number" data-value="{$stats_merchants}">{$stats_merchants}</div>
                        <div class="stat-title">商户累计</div>
                        <div class="stat-decoration">
                            <span class="stat-line"></span>
                            <span class="stat-dot"></span>
                            <span class="stat-line"></span>
                        </div>
                    </div>
                </div>
                <div class="stat-item" data-aos="fade-up" data-aos-delay="400">
                    <div class="stat-item-inner">
                        <div class="stat-icon-wrapper">
                            <i class="fas fa-coins stat-icon"></i>
                            <div class="icon-glow"></div>
                        </div>
                        <div class="stat-number" data-value="{$stats_amount}">{$stats_amount}</div>
                        <div class="stat-title">交易金额</div>
                        <div class="stat-decoration">
                            <span class="stat-line"></span>
                            <span class="stat-dot"></span>
                            <span class="stat-line"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="payment-icons">
        <div class="hexagon-bg"></div>
        <div class="glow-effect"></div>
        <div class="container">
            <h2 class="section-title">支付方式<span class="highlight">全面支持</span></h2>
            <p class="section-subtitle">多种支付渠道，<span class="highlight-text">安全便捷</span> 交易无忧</p>
            
            <div class="payment-container">
                <div class="icons-grid">
                    <div class="icon-item" data-name="QQ支付">
                        <div class="icon-wrapper">
                            <div class="icon-border"></div>
                            <svg viewBox="0 0 100 100" class="payment-icon">
                                <defs>
                                    <linearGradient id="iconGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color: var(--primary-light)"/>
                                        <stop offset="100%" style="stop-color: var(--primary-dark)"/>
                                    </linearGradient>
                                    <filter id="glow1">
                                        <feGaussianBlur stdDeviation="2" result="blur" />
                                        <feComposite in="SourceGraphic" in2="blur" operator="over" />
                                    </filter>
                                </defs>
                                <rect x="20" y="20" width="60" height="60" rx="10" fill="none" stroke="url(#iconGradient1)" stroke-width="4"/>
                                <!-- 企鹅图形 -->
                                <path d="
                                    M50 35 
                                    L42 45 
                                    L58 45
                                    L50 35
                                    M42 45
                                    L40 60
                                    L46 55
                                    L50 60
                                    L54 55
                                    L60 60
                                    L58 45
                                    Z" 
                                    fill="url(#iconGradient1)"
                                    filter="url(#glow1)"
                                />
                            </svg>
                            <div class="icon-glow"></div>
                            <div class="icon-particles">
                                <span class="particle"></span>
                                <span class="particle"></span>
                                <span class="particle"></span>
                            </div>
                        </div>
                        <div class="icon-text">
                            <span>QQ支付</span>
                            <div class="icon-line"></div>
                        </div>
                    </div>
                    <div class="icon-item" data-name="微信支付">
                        <div class="icon-wrapper">
                            <div class="icon-border"></div>
                            <svg viewBox="0 0 100 100" class="payment-icon">
                                <defs>
                                    <linearGradient id="iconGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color: var(--primary-light)"/>
                                        <stop offset="100%" style="stop-color: var(--primary-dark)"/>
                                    </linearGradient>
                                    <filter id="glow2">
                                        <feGaussianBlur stdDeviation="2" result="blur" />
                                        <feComposite in="SourceGraphic" in2="blur" operator="over" />
                                    </filter>
                                </defs>
                                <rect x="20" y="20" width="60" height="60" rx="10" fill="none" stroke="url(#iconGradient2)" stroke-width="4"/>
                                <!-- 对勾图形 -->
                                <path d="M35 50 L45 60 L65 40" 
                                      fill="none" 
                                      stroke="url(#iconGradient2)" 
                                      stroke-width="4" 
                                      stroke-linecap="round" 
                                      stroke-linejoin="round"
                                      filter="url(#glow2)"/>
                            </svg>
                            <div class="icon-glow"></div>
                            <div class="icon-particles">
                                <span class="particle"></span>
                                <span class="particle"></span>
                                <span class="particle"></span>
                            </div>
                        </div>
                        <div class="icon-text">
                            <span>微信支付</span>
                            <div class="icon-line"></div>
                        </div>
                    </div>
                    <div class="icon-item" data-name="支付宝">
                        <div class="icon-wrapper">
                            <div class="icon-border"></div>
                            <svg viewBox="0 0 100 100" class="payment-icon">
                                <defs>
                                    <linearGradient id="iconGradient3" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color: var(--primary-light)"/>
                                        <stop offset="100%" style="stop-color: var(--primary-dark)"/>
                                    </linearGradient>
                                    <filter id="glow3">
                                        <feGaussianBlur stdDeviation="2" result="blur" />
                                        <feComposite in="SourceGraphic" in2="blur" operator="over" />
                                    </filter>
                                </defs>
                                <rect x="20" y="20" width="60" height="60" rx="10" fill="none" stroke="url(#iconGradient3)" stroke-width="4"/>
                                <text x="50" y="60" text-anchor="middle" fill="url(#iconGradient3)" font-size="24" filter="url(#glow3)">支</text>
                            </svg>
                            <div class="icon-glow"></div>
                            <div class="icon-particles">
                                <span class="particle"></span>
                                <span class="particle"></span>
                                <span class="particle"></span>
                            </div>
                        </div>
                        <div class="icon-text">
                            <span>支付宝</span>
                            <div class="icon-line"></div>
                        </div>
                    </div>
                    <div class="icon-item" data-name="银联支付">
                        <div class="icon-wrapper">
                            <div class="icon-border"></div>
                            <svg viewBox="0 0 100 100" class="payment-icon">
                                <defs>
                                    <linearGradient id="iconGradient4" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color: var(--primary-light)"/>
                                        <stop offset="100%" style="stop-color: var(--primary-dark)"/>
                                    </linearGradient>
                                    <filter id="glow4">
                                        <feGaussianBlur stdDeviation="2" result="blur" />
                                        <feComposite in="SourceGraphic" in2="blur" operator="over" />
                                    </filter>
                                </defs>
                                <rect x="20" y="20" width="60" height="60" rx="10" fill="none" stroke="url(#iconGradient4)" stroke-width="4"/>
                                <text x="50" y="60" text-anchor="middle" fill="url(#iconGradient4)" font-size="24" filter="url(#glow4)">银</text>
                            </svg>
                            <div class="icon-glow"></div>
                            <div class="icon-particles">
                                <span class="particle"></span>
                                <span class="particle"></span>
                                <span class="particle"></span>
                            </div>
                        </div>
                        <div class="icon-text">
                            <span>银联支付</span>
                            <div class="icon-line"></div>
                        </div>
                    </div>
                    <div class="icon-item" data-name="京东支付">
                        <div class="icon-wrapper">
                            <div class="icon-border"></div>
                            <svg viewBox="0 0 100 100" class="payment-icon">
                                <defs>
                                    <linearGradient id="iconGradient5" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color: var(--primary-light)"/>
                                        <stop offset="100%" style="stop-color: var(--primary-dark)"/>
                                    </linearGradient>
                                    <filter id="glow5">
                                        <feGaussianBlur stdDeviation="2" result="blur" />
                                        <feComposite in="SourceGraphic" in2="blur" operator="over" />
                                    </filter>
                                </defs>
                                <rect x="20" y="20" width="60" height="60" rx="10" fill="none" stroke="url(#iconGradient5)" stroke-width="4"/>
                                <text x="50" y="60" text-anchor="middle" fill="url(#iconGradient5)" font-size="24" filter="url(#glow5)">京</text>
                            </svg>
                            <div class="icon-glow"></div>
                            <div class="icon-particles">
                                <span class="particle"></span>
                                <span class="particle"></span>
                                <span class="particle"></span>
                            </div>
                        </div>
                        <div class="icon-text">
                            <span>京东支付</span>
                            <div class="icon-line"></div>
                        </div>
                    </div>
                    <div class="icon-item" data-name="云闪付">
                        <div class="icon-wrapper">
                            <div class="icon-border"></div>
                            <svg viewBox="0 0 100 100" class="payment-icon">
                                <defs>
                                    <linearGradient id="iconGradient6" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color: var(--primary-light)"/>
                                        <stop offset="100%" style="stop-color: var(--primary-dark)"/>
                                    </linearGradient>
                                    <filter id="glow6">
                                        <feGaussianBlur stdDeviation="2" result="blur" />
                                        <feComposite in="SourceGraphic" in2="blur" operator="over" />
                                    </filter>
                                </defs>
                                <rect x="20" y="20" width="60" height="60" rx="10" fill="none" stroke="url(#iconGradient6)" stroke-width="4"/>
                                <text x="50" y="60" text-anchor="middle" fill="url(#iconGradient6)" font-size="24" filter="url(#glow6)">云</text>
                            </svg>
                            <div class="icon-glow"></div>
                            <div class="icon-particles">
                                <span class="particle"></span>
                                <span class="particle"></span>
                                <span class="particle"></span>
                            </div>
                        </div>
                        <div class="icon-text">
                            <span>云闪付</span>
                            <div class="icon-line"></div>
                        </div>
                    </div>
                </div>
                
                <div class="payment-info-box">
                    <!-- 添加左侧球体装饰 -->
                    <div class="info-sphere-left">
                        <div class="info-sphere-inner">
                            <div class="info-sphere-ring"></div>
                            <div class="info-sphere-ring"></div>
                            <div class="info-sphere-line horizontal"></div>
                            <div class="info-sphere-line vertical"></div>
                            <div class="info-sphere-line diagonal-1"></div>
                            <div class="info-sphere-line diagonal-2"></div>
                            <div class="info-sphere-dot"></div>
                        </div>
                    </div>
                    
                    <!-- 添加数据流线条装饰 -->
                    <div class="info-data-lines">
                        <div class="data-line line-1"></div>
                        <div class="data-line line-2"></div>
                        <div class="data-line line-3"></div>
                        <div class="data-particle p-1"></div>
                        <div class="data-particle p-2"></div>
                        <div class="data-particle p-3"></div>
                    </div>
                    
                    <div class="info-content">
                        <div class="info-title">安全支付保障</div>
                        <div class="info-text">多重数据加密，确保交易安全</div>
                        <div class="info-highlight">
                            <span class="info-stat">100%</span>
                            <span class="info-label">资金安全</span>
                        </div>
                        
                        <!-- 添加安全图标组 -->
                        <div class="security-icons">
                            <div class="security-icon">
                                <i class="fas fa-shield-alt"></i>
                                <span>防护</span>
                            </div>
                            <div class="security-icon">
                                <i class="fas fa-lock"></i>
                                <span>加密</span>
                            </div>
                            <div class="security-icon">
                                <i class="fas fa-check-circle"></i>
                                <span>认证</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 添加右侧球体装饰 -->
                    <div class="info-sphere-right">
                        <div class="info-sphere-inner">
                            <div class="info-sphere-ring"></div>
                            <div class="info-sphere-ring"></div>
                            <div class="info-sphere-line horizontal"></div>
                            <div class="info-sphere-line vertical"></div>
                            <div class="info-sphere-line diagonal-1"></div>
                            <div class="info-sphere-line diagonal-2"></div>
                            <div class="info-sphere-dot"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 添加支付框的扩展样式 -->
                <style>
                    /* 改进支付信息框样式 */
                    .payment-info-box {
                        margin-top: 3rem;
                        padding: 2.5rem;
                        position: relative;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        overflow: visible;
                    }
                    
                    .payment-info-box::before {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 10%;
                        right: 10%;
                        height: 1px;
                        background: linear-gradient(90deg, 
                            transparent,
                            var(--primary),
                            transparent
                        );
                    }
                    
                    .payment-info-box::after {
                        content: '';
                        position: absolute;
                        bottom: 0;
                        left: 10%;
                        right: 10%;
                        height: 1px;
                        background: linear-gradient(90deg, 
                            transparent,
                            var(--primary),
                            transparent
                        );
                    }
                    
                    /* 内容样式增强 */
                    .info-content {
                        position: relative;
                        z-index: 2;
                        padding: 2rem;
                        background: rgba(0, 0, 0, 0.4);
                        backdrop-filter: blur(10px);
                        border-radius: 20px;
                        border: 1px solid rgba(200, 166, 117, 0.2);
                        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                        width: 100%;
                        max-width: 600px;
                    }
                    
                    .info-title {
                        font-size: 2rem;
                        color: var(--primary-light);
                        margin-bottom: 1rem;
                        text-align: center;
                        text-shadow: 0 0 10px rgba(200, 166, 117, 0.5);
                    }
                    
                    .info-text {
                        color: var(--gray);
                        font-size: 1.1rem;
                        margin-bottom: 1.5rem;
                        text-align: center;
                    }
                    
                    .info-highlight {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        margin-bottom: 1.5rem;
                    }
                    
                    .info-stat {
                        font-size: 2.5rem;
                        font-weight: 700;
                        color: var(--primary-light);
                        text-shadow: 0 0 10px rgba(200, 166, 117, 0.5);
                        animation: numberPulse 2s ease-in-out infinite alternate;
                    }
                    
                    @keyframes numberPulse {
                        0% {
                            transform: scale(1);
                            text-shadow: 0 0 10px rgba(200, 166, 117, 0.3);
                        }
                        100% {
                            transform: scale(1.1);
                            text-shadow: 0 0 20px rgba(200, 166, 117, 0.6);
                        }
                    }
                    
                    .info-label {
                        font-size: 1.2rem;
                        color: var(--gray);
                    }
                    
                    /* 响应式调整 */
                    @media (max-width: 1200px) {
                        .icons-grid {
                            grid-template-columns: repeat(3, 1fr);
                        }
                        
                        .payment-icons .section-title {
                            font-size: 2.5rem;
                        }
                    }
                    
                    @media (max-width: 768px) {
                        .payment-icons {
                            padding: 5rem 0;
                        }
                        
                        .icons-grid {
                            grid-template-columns: repeat(2, 1fr);
                            gap: 1.5rem;
                        }
                        
                        .payment-icons .section-title {
                            font-size: 2.2rem;
                        }
                        
                        .payment-icons .section-subtitle {
                            font-size: 1rem;
                            margin-bottom: 3rem;
                        }
                        
                        .icon-wrapper {
                            width: 60px;
                            height: 60px;
                        }
                        
                        .info-title {
                            font-size: 1.5rem;
                        }
                        
                        .info-text {
                            font-size: 1rem;
                        }
                        
                        .info-stat {
                            font-size: 2rem;
                        }
                    }
                    
                    @media (max-width: 480px) {
                        .icons-grid {
                            grid-template-columns: repeat(1, 1fr);
                        }
                    }
                </style>
            </div>
        </div>
        
        <style>
        /* 支付方式区域样式增强 */
        .payment-icons {
            padding: 8rem 0;
            position: relative;
            overflow: hidden;
            background: linear-gradient(to bottom, 
                rgba(0, 0, 0, 0.9), 
                rgba(10, 10, 10, 0.95)
            );
            perspective: 1000px;
        }
        
        /* 六边形背景 */
        .hexagon-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
            opacity: 0.07;
            background-image: 
                repeating-linear-gradient(60deg, rgba(200, 166, 117, 0.3) 0, rgba(200, 166, 117, 0.3) 1px, transparent 1px, transparent 30px),
                repeating-linear-gradient(120deg, rgba(200, 166, 117, 0.3) 0, rgba(200, 166, 117, 0.3) 1px, transparent 1px, transparent 30px),
                repeating-linear-gradient(180deg, rgba(200, 166, 117, 0.3) 0, rgba(200, 166, 117, 0.3) 1px, transparent 1px, transparent 30px);
            animation: hexagonMove 60s linear infinite;
        }
        
        @keyframes hexagonMove {
            0% {
                background-position: 0 0, 0 0, 0 0;
            }
            100% {
                background-position: 100px 100px, 100px 100px, 100px 100px;
            }
        }
        
        /* 发光效果 */
        .glow-effect {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 70%;
            height: 70%;
            background: radial-gradient(
                ellipse at center,
                rgba(200, 166, 117, 0.15) 0%,
                transparent 70%
            );
            z-index: 1;
            filter: blur(60px);
            pointer-events: none;
            animation: glowPulse 6s ease-in-out infinite alternate;
        }
        
        @keyframes glowPulse {
            0% {
                opacity: 0.5;
                transform: translate(-50%, -50%) scale(0.8);
            }
            100% {
                opacity: 0.8;
                transform: translate(-50%, -50%) scale(1.2);
            }
        }
        
        .payment-icons .container {
            position: relative;
            z-index: 2;
        }
        
        /* 标题样式 */
        .payment-icons .section-title {
            text-align: center;
            font-size: 3rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--light), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            position: relative;
            letter-spacing: 1px;
            text-transform: uppercase;
            animation: titleFloat 3s ease-in-out infinite alternate;
        }
        
        .payment-icons .section-title .highlight {
            display: inline-block;
            background: linear-gradient(135deg, var(--primary-light), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: highlightGlow 2s ease-in-out infinite alternate;
            position: relative;
        }
        
        .payment-icons .section-title .highlight::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, transparent, var(--primary), transparent);
            opacity: 0.7;
        }
        
        @keyframes titleFloat {
            0% {
                transform: translateY(0);
                text-shadow: 0 0 10px rgba(200, 166, 117, 0.3);
            }
            100% {
                transform: translateY(-5px);
                text-shadow: 0 0 20px rgba(200, 166, 117, 0.5);
            }
        }
        
        @keyframes highlightGlow {
            0% {
                text-shadow: 0 0 5px rgba(200, 166, 117, 0.5);
            }
            100% {
                text-shadow: 0 0 15px rgba(200, 166, 117, 0.8);
            }
        }
        
        .payment-icons .section-subtitle {
            text-align: center;
            color: var(--gray);
            margin-bottom: 4rem;
            font-size: 1.2rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            position: relative;
        }
        
        .payment-icons .section-subtitle .highlight-text {
            color: var(--primary-light);
            font-weight: 500;
            position: relative;
        }
        
        .payment-icons .section-subtitle .highlight-text::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 1px;
            background: var(--primary);
        }
        
        .payment-container {
            display: flex;
            flex-direction: column;
            gap: 4rem;
        }
        
        /* 图标网格 */
        .icons-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 2rem;
            transform-style: preserve-3d;
            transform: perspective(1000px) rotateX(5deg);
            transition: all 0.5s ease;
            opacity: 0;
            animation: gridAppear 1s ease-out forwards;
        }
        
        @keyframes gridAppear {
            to {
                opacity: 1;
                transform: perspective(1000px) rotateX(0);
            }
        }
        
        /* 图标项目 */
        .icon-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1.5rem;
            position: relative;
            padding: 1.5rem;
            transform-style: preserve-3d;
            transform: translateZ(0px);
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            animation: iconFloat 7s ease-in-out infinite alternate;
            animation-delay: calc(var(--idx) * 0.1s);
        }
        
        .icon-item:nth-child(1) { --idx: 1; }
        .icon-item:nth-child(2) { --idx: 2; }
        .icon-item:nth-child(3) { --idx: 3; }
        .icon-item:nth-child(4) { --idx: 4; }
        .icon-item:nth-child(5) { --idx: 5; }
        .icon-item:nth-child(6) { --idx: 6; }
        
        @keyframes iconFloat {
            0%, 100% {
                transform: translateZ(0px) translateY(0);
            }
            50% {
                transform: translateZ(10px) translateY(-5px);
            }
        }
        
        .icon-item:hover {
            transform: translateZ(20px) translateY(-10px) scale(1.05);
            z-index: 3;
        }
        
        .icon-item::before {
            content: attr(data-name);
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%) translateY(-20px) scale(0.7);
            color: var(--primary);
            font-weight: 500;
            padding: 0.3rem 1rem;
            border-radius: 20px;
            background: rgba(0, 0, 0, 0.7);
            border: 1px solid rgba(200, 166, 117, 0.3);
            opacity: 0;
            transition: all 0.3s ease;
            pointer-events: none;
            white-space: nowrap;
            z-index: 10;
            box-shadow: 0 0 15px rgba(200, 166, 117, 0.2);
        }
        
        .icon-item:hover::before {
            opacity: 1;
            transform: translateX(-50%) translateY(0) scale(1);
        }
        
        /* 图标容器 */
        .icon-wrapper {
            position: relative;
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            transform-style: preserve-3d;
            perspective: 600px;
        }
        
        .icon-border {
            position: absolute;
            inset: -10px;
            border-radius: 50%;
            border: 1px solid rgba(200, 166, 117, 0.2);
            transform-style: preserve-3d;
            animation: rotate 10s linear infinite;
        }
        
        .icon-item:hover .icon-border {
            border-width: 2px;
            border-color: rgba(200, 166, 117, 0.4);
            animation-duration: 5s;
            box-shadow: 0 0 15px rgba(200, 166, 117, 0.2);
        }
        
        @keyframes rotate {
            0% { transform: rotateZ(0deg) rotateX(20deg) rotateY(0deg); }
            100% { transform: rotateZ(360deg) rotateX(20deg) rotateY(360deg); }
        }
        
        .payment-icon {
            width: 100%;
            height: 100%;
            color: var(--primary);
            transition: all 0.5s ease;
            position: relative;
            z-index: 2;
            filter: drop-shadow(0 0 8px rgba(200, 166, 117, 0.3));
            transform-style: preserve-3d;
            transform: translateZ(10px);
        }
        
        .icon-item:hover .payment-icon {
            transform: translateZ(20px) scale(1.1) rotate(5deg);
            filter: drop-shadow(0 0 15px rgba(200, 166, 117, 0.5));
        }
        
        .icon-glow {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: radial-gradient(
                circle at center,
                rgba(200, 166, 117, 0.5) 0%,
                transparent 70%
            );
            opacity: 0.3;
            filter: blur(10px);
            transition: all 0.5s ease;
            transform: scale(0.8) translateZ(5px);
            z-index: 1;
        }
        
        .icon-item:hover .icon-glow {
            opacity: 0.7;
            transform: scale(1.5) translateZ(5px);
        }
        
        /* 粒子效果 */
        .icon-particles {
            position: absolute;
            width: 100%;
            height: 100%;
            z-index: 3;
            pointer-events: none;
        }
        
        .icon-particles .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: var(--primary);
            border-radius: 50%;
            opacity: 0;
            filter: blur(1px);
        }
        
        .icon-particles .particle:nth-child(1) { top: 20%; left: 10%; }
        .icon-particles .particle:nth-child(2) { top: 70%; right: 20%; }
        .icon-particles .particle:nth-child(3) { bottom: 10%; left: 30%; }
        
        .icon-item:hover .icon-particles .particle {
            animation: particleFloat 2s ease-in-out infinite;
        }
        
        .icon-item:hover .icon-particles .particle:nth-child(1) { animation-delay: 0s; }
        .icon-item:hover .icon-particles .particle:nth-child(2) { animation-delay: 0.3s; }
        .icon-item:hover .icon-particles .particle:nth-child(3) { animation-delay: 0.6s; }
        
        @keyframes particleFloat {
            0% {
                opacity: 0;
                transform: translateY(0) scale(0.5);
            }
            50% {
                opacity: 1;
                transform: translateY(-15px) scale(1.5);
            }
            100% {
                opacity: 0;
                transform: translateY(-30px) scale(0.5);
            }
        }
        
        /* 文字效果 */
        .icon-text {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .icon-text span {
            color: var(--gray);
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .icon-item:hover .icon-text span {
            color: var(--primary-light);
            transform: scale(1.1);
        }
        
        .icon-line {
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, var(--primary), transparent);
            transition: all 0.3s ease;
        }
        
        .icon-item:hover .icon-line {
            width: 100%;
        }
        
        /* 支付信息框 */
        .payment-info-box {
            margin-top: 2rem;
            padding: 2rem;
            background: linear-gradient(135deg, 
                rgba(25, 25, 25, 0.5) 0%,
                rgba(10, 10, 10, 0.8) 100%);
            border-radius: 20px;
            border: 1px solid rgba(200, 166, 117, 0.1);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transform: translateY(30px);
            opacity: 0;
            animation: infoBoxAppear 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
            animation-delay: 0.5s;
            backdrop-filter: blur(10px);
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            position: relative;
            overflow: hidden;
        }
        
        .payment-info-box::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(
                transparent, 
                rgba(200, 166, 117, 0.1), 
                transparent 30%
            );
            opacity: 0.3;
            animation: rotateGradient 10s linear infinite;
            z-index: 1;
        }
        
        @keyframes infoBoxAppear {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .info-content {
            position: relative;
            z-index: 2;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1rem;
            text-align: center;
        }
        
        .info-title {
            font-size: 1.8rem;
            color: var(--primary);
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .info-text {
            color: var(--gray);
            font-size: 1.1rem;
            margin-bottom: 1.5rem;
        }
        
        .info-highlight {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
        }
        
        .info-stat {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-light);
            text-shadow: 0 0 10px rgba(200, 166, 117, 0.5);
            animation: numberPulse 2s ease-in-out infinite alternate;
        }
        
        @keyframes numberPulse {
            0% {
                transform: scale(1);
                text-shadow: 0 0 10px rgba(200, 166, 117, 0.3);
            }
            100% {
                transform: scale(1.1);
                text-shadow: 0 0 20px rgba(200, 166, 117, 0.6);
            }
        }
        
        .info-label {
            font-size: 1.2rem;
            color: var(--gray);
        }
        
        /* 响应式调整 */
        @media (max-width: 1200px) {
            .icons-grid {
                grid-template-columns: repeat(3, 1fr);
            }
            
            .payment-icons .section-title {
                font-size: 2.5rem;
            }
        }
        
        @media (max-width: 768px) {
            .payment-icons {
                padding: 5rem 0;
            }
            
            .icons-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1.5rem;
            }
            
            .payment-icons .section-title {
                font-size: 2.2rem;
            }
            
            .payment-icons .section-subtitle {
                font-size: 1rem;
                margin-bottom: 3rem;
            }
            
            .icon-wrapper {
                width: 60px;
                height: 60px;
            }
            
            .info-title {
                font-size: 1.5rem;
            }
            
            .info-text {
                font-size: 1rem;
            }
            
            .info-stat {
                font-size: 2rem;
            }
        }
        
        @media (max-width: 480px) {
            .icons-grid {
                grid-template-columns: repeat(1, 1fr);
            }
        }
        </style>
    </section>

    <section class="features">
        <div class="container">
            <h2 class="section-title">核心优势</h2>
            <p class="section-subtitle">为您提供最优质的服务体验</p>
            <div class="features-grid">
                <div class="feature-item">
                    <div class="feature-icon-wrapper">
                        <i class="fas fa-box-open feature-icon"></i>
                        <div class="icon-glow"></div>
                        <div class="icon-particles">
                            <span class="particle"></span>
                            <span class="particle"></span>
                            <span class="particle"></span>
                        </div>
                    </div>
                    <h3>丰富的产品选择</h3>
                    <p>我们提供一个虚拟数字商品平台，包含全网各类虚拟品，常用软件可以找到满足你需求的商品。</p>
                    <div class="feature-decoration">
                        <span class="decoration-line"></span>
                        <span class="decoration-circle"></span>
                    </div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon-wrapper">
                        <i class="fas fa-shield-alt feature-icon"></i>
                        <div class="icon-glow"></div>
                        <div class="icon-particles">
                            <span class="particle"></span>
                            <span class="particle"></span>
                            <span class="particle"></span>
                        </div>
                    </div>
                    <h3>高度安全的购物环境</h3>
                    <p>所有交易均走正规支付通道，拒绝使用易支付，三网免挂接口，让您可以安心购物。</p>
                    <div class="feature-decoration">
                        <span class="decoration-line"></span>
                        <span class="decoration-circle"></span>
                    </div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon-wrapper">
                        <i class="fas fa-headset feature-icon"></i>
                        <div class="icon-glow"></div>
                        <div class="icon-particles">
                            <span class="particle"></span>
                            <span class="particle"></span>
                            <span class="particle"></span>
                        </div>
                    </div>
                    <h3>专业的客户支持</h3>
                    <p>我们的客服团队由经验丰富的专业人员组成，确保商品销售之后处理，防止商品判定之类的难题。</p>
                    <div class="feature-decoration">
                        <span class="decoration-line"></span>
                        <span class="decoration-circle"></span>
                    </div>
                </div>
            </div>
        </div>
        
        <style>
        /* 增强特性区域样式 */
        .features {
            padding: 8rem 0;
            background: linear-gradient(to bottom, transparent, rgba(0,0,0,0.8));
            position: relative;
            overflow: hidden;
        }
        
        .features::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                radial-gradient(circle at 20% 30%, rgba(200, 166, 117, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(200, 166, 117, 0.1) 0%, transparent 50%);
            opacity: 0.6;
            z-index: 0;
        }
        
        .features .container {
            position: relative;
            z-index: 2;
        }
        
        .features .section-title {
            text-align: center;
            font-size: 2.8rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--primary-light), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            position: relative;
            animation: titleGlow 3s ease-in-out infinite alternate;
        }
        
        .features .section-subtitle {
            text-align: center;
            color: var(--gray);
            margin-bottom: 4rem;
            font-size: 1.2rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            position: relative;
        }
        
        .features .section-subtitle::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 2px;
            background: linear-gradient(90deg, transparent, var(--primary), transparent);
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 3rem;
        }
        
        .feature-item {
            padding: 3rem 2rem;
            border-radius: 20px;
            background: linear-gradient(135deg, 
                rgba(25, 25, 25, 0.8) 0%,
                rgba(10, 10, 10, 0.9) 100%);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(200, 166, 117, 0.1);
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            transform: translateY(50px);
            opacity: 0;
            animation: featureAppear 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
            animation-delay: calc(var(--order) * 0.2s);
        }
        
        .feature-item:nth-child(2) {
            --order: 2;
        }
        
        .feature-item:nth-child(3) {
            --order: 3;
        }
        
        @keyframes featureAppear {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .feature-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, 
                rgba(200, 166, 117, 0.1) 0%,
                transparent 60%);
            opacity: 0;
            transition: var(--transition);
            z-index: 1;
        }
        
        .feature-item::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(
                transparent, 
                rgba(200, 166, 117, 0.1), 
                transparent 30%
            );
            opacity: 0;
            transform: rotate(0deg);
            transition: opacity 0.5s ease;
            z-index: 0;
        }
        
        .feature-item:hover {
            transform: translateY(-10px) scale(1.02);
            border-color: rgba(200, 166, 117, 0.3);
            box-shadow: 
                0 20px 40px rgba(0, 0, 0, 0.3),
                0 0 30px rgba(200, 166, 117, 0.2);
        }
        
        .feature-item:hover::before {
            opacity: 1;
        }
        
        .feature-item:hover::after {
            opacity: 1;
            animation: rotateGradient 4s linear infinite;
        }
        
        @keyframes rotateGradient {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }
        
        .feature-icon-wrapper {
            position: relative;
            width: 80px;
            height: 80px;
            margin: 0 auto 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: rgba(25, 25, 25, 0.8);
            border: 1px solid rgba(200, 166, 117, 0.2);
            transition: all 0.4s ease;
            z-index: 2;
        }
        
        .feature-item:hover .feature-icon-wrapper {
            transform: scale(1.1);
            border-color: rgba(200, 166, 117, 0.4);
            box-shadow: 0 0 30px rgba(200, 166, 117, 0.3);
        }
        
        .feature-icon {
            font-size: 2.5rem;
            color: var(--primary);
            transition: all 0.5s ease;
            z-index: 2;
        }
        
        .feature-item:hover .feature-icon {
            transform: scale(1.2) rotate(10deg);
            color: var(--primary-light);
            text-shadow: 0 0 20px var(--primary);
        }
        
        .icon-glow {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: radial-gradient(
                circle at center,
                rgba(200, 166, 117, 0.5) 0%,
                transparent 70%
            );
            opacity: 0.3;
            filter: blur(10px);
            transition: all 0.5s ease;
            transform: scale(0.8);
            z-index: 1;
        }
        
        .feature-item:hover .icon-glow {
            opacity: 0.7;
            transform: scale(1.3);
        }
        
        .icon-particles {
            position: absolute;
            width: 100%;
            height: 100%;
            z-index: 1;
        }
        
        .icon-particles .particle {
            position: absolute;
            width: 6px;
            height: 6px;
            background: var(--primary);
            border-radius: 50%;
            opacity: 0;
            filter: blur(1px);
            transition: all 0.3s ease;
        }
        
        .icon-particles .particle:nth-child(1) {
            top: 20%;
            left: 10%;
        }
        
        .icon-particles .particle:nth-child(2) {
            top: 70%;
            right: 20%;
        }
        
        .icon-particles .particle:nth-child(3) {
            bottom: 10%;
            left: 30%;
        }
        
        .feature-item:hover .icon-particles .particle {
            animation: particlePulse 2s ease-in-out infinite;
        }
        
        .feature-item:hover .icon-particles .particle:nth-child(1) {
            animation-delay: 0s;
        }
        
        .feature-item:hover .icon-particles .particle:nth-child(2) {
            animation-delay: 0.3s;
        }
        
        .feature-item:hover .icon-particles .particle:nth-child(3) {
            animation-delay: 0.6s;
        }
        
        @keyframes particlePulse {
            0% {
                opacity: 0;
                transform: scale(0) translateY(0);
            }
            50% {
                opacity: 1;
                transform: scale(1) translateY(-10px);
            }
            100% {
                opacity: 0;
                transform: scale(0) translateY(-20px);
            }
        }
        
        .feature-item h3 {
            font-size: 1.6rem;
            color: var(--primary);
            margin-bottom: 1.2rem;
            text-align: center;
            position: relative;
            z-index: 2;
            transition: all 0.4s ease;
        }
        
        .feature-item:hover h3 {
            color: var(--primary-light);
            transform: scale(1.05);
            text-shadow: 0 0 10px rgba(200, 166, 117, 0.3);
        }
        
        .feature-item p {
            color: var(--gray);
            font-size: 1rem;
            line-height: 1.6;
            text-align: center;
            position: relative;
            z-index: 2;
            transition: all 0.4s ease;
        }
        
        .feature-item:hover p {
            color: var(--light);
        }
        
        .feature-decoration {
            position: absolute;
            bottom: 1.5rem;
            left: 0;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.5s ease;
            z-index: 2;
        }
        
        .feature-item:hover .feature-decoration {
            opacity: 0.7;
            transform: translateY(0);
        }
        
        .decoration-line {
            height: 1px;
            width: 30px;
            background: linear-gradient(90deg, transparent, var(--primary), transparent);
        }
        
        .decoration-circle {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: var(--primary);
            margin: 0 0.5rem;
            box-shadow: 0 0 10px var(--primary);
        }
        
        /* 响应式调整 */
        @media (max-width: 992px) {
            .features-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 2rem;
            }
            
            .feature-item {
                padding: 2.5rem 1.5rem;
            }
            
            .feature-icon-wrapper {
                width: 70px;
                height: 70px;
            }
            
            .feature-icon {
                font-size: 2rem;
            }
        }
        
        @media (max-width: 768px) {
            .features {
                padding: 5rem 0;
            }
            
            .features .section-title {
                font-size: 2.2rem;
            }
            
            .features .section-subtitle {
                font-size: 1rem;
                margin-bottom: 3rem;
            }
            
            .feature-item h3 {
                font-size: 1.4rem;
            }
        }
        </style>
    </section>

    <section class="steps">
        <div class="container">
            <h2 class="section-title">简单入驻 只需三步</h2>
            <p class="section-subtitle">这比你想象的更容易，遵循几个简单的步骤</p>
            
            <!-- 优化后的步骤容器 -->
            <div class="steps-container">
                <!-- 步骤1 -->
                <div class="step" data-aos="fade-right" data-aos-delay="100">
                    <div class="step-card">
                        <div class="step-card-inner">
                            <div class="step-circle">
                                <div class="step-number">01</div>
                                <div class="step-icon">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <svg class="progress-ring" width="120" height="120">
                                    <circle class="progress-ring-circle" cx="60" cy="60" r="54" />
                                </svg>
                                <!-- 添加光效 -->
                                <div class="step-glow"></div>
                            </div>
                            <h3>注册上架</h3>
                            <p>快速注册商户账号<br>轻松上架商品</p>
                            
                            <!-- 添加悬浮装饰元素 -->
                            <div class="step-decoration">
                                <span class="dot dot-1"></span>
                                <span class="dot dot-2"></span>
                                <span class="dot dot-3"></span>
                                <span class="line line-1"></span>
                                <span class="line line-2"></span>
                            </div>
                        </div>
                    </div>
                    <!-- 添加连接线 -->
                    <div class="step-connector">
                        <div class="connector-line"></div>
                        <div class="connector-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>
                
                <!-- 步骤2 -->
                <div class="step" data-aos="fade-up" data-aos-delay="300">
                    <div class="step-card">
                        <div class="step-card-inner">
                            <div class="step-circle">
                                <div class="step-number">02</div>
                                <div class="step-icon">
                                    <i class="fas fa-handshake"></i>
                                </div>
                                <svg class="progress-ring" width="120" height="120">
                                    <circle class="progress-ring-circle" cx="60" cy="60" r="54" />
                                </svg>
                                <!-- 添加光效 -->
                                <div class="step-glow"></div>
                            </div>
                            <h3>托管交易</h3>
                            <p>安全托管资金<br>保障双方权益</p>
                            
                            <!-- 添加悬浮装饰元素 -->
                            <div class="step-decoration">
                                <span class="dot dot-1"></span>
                                <span class="dot dot-2"></span>
                                <span class="dot dot-3"></span>
                                <span class="line line-1"></span>
                                <span class="line line-2"></span>
                            </div>
                        </div>
                    </div>
                    <!-- 添加连接线 -->
                    <div class="step-connector">
                        <div class="connector-line"></div>
                        <div class="connector-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>
                
                <!-- 步骤3 -->
                <div class="step" data-aos="fade-left" data-aos-delay="500">
                    <div class="step-card">
                        <div class="step-card-inner">
                            <div class="step-circle">
                                <div class="step-number">03</div>
                                <div class="step-icon">
                                    <i class="fas fa-wallet"></i>
                                </div>
                                <svg class="progress-ring" width="120" height="120">
                                    <circle class="progress-ring-circle" cx="60" cy="60" r="54" />
                                </svg>
                                <!-- 添加光效 -->
                                <div class="step-glow"></div>
                            </div>
                            <h3>即时到账</h3>
                            <p>交易完成后<br>资金秒速到账</p>
                            
                            <!-- 添加悬浮装饰元素 -->
                            <div class="step-decoration">
                                <span class="dot dot-1"></span>
                                <span class="dot dot-2"></span>
                                <span class="dot dot-3"></span>
                                <span class="line line-1"></span>
                                <span class="line line-2"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 添加底部装饰 -->
            <div class="steps-decoration">
                <div class="decoration-line"></div>
                <div class="decoration-circle"></div>
                <div class="decoration-line"></div>
            </div>
        </div>
        
        <!-- 添加JavaScript代码 -->
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 为步骤卡片添加悬停效果
            const steps = document.querySelectorAll('.step');
            
            steps.forEach(step => {
                step.addEventListener('mouseenter', function() {
                    this.classList.add('step-hover');
                });
                
                step.addEventListener('mouseleave', function() {
                    this.classList.remove('step-hover');
                });
            });
            
            // 添加滚动触发动画
            const handleIntersection = (entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('step-visible');
                        // 停止观察已显示的元素
                        observer.unobserve(entry.target);
                    }
                });
            };
            
            const observer = new IntersectionObserver(handleIntersection, {
                root: null,
                threshold: 0.2,
            });
            
            steps.forEach(step => {
                observer.observe(step);
            });
        });
        </script>
        
        <style>
        /* 增强步骤区域样式 */
        .steps {
            padding: 8rem 0;
            position: relative;
            background: linear-gradient(to top, transparent, rgba(0,0,0,0.8));
            overflow: hidden;
        }
        
        /* 优化网格背景 */
        .steps::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(30deg, #000000 12%, transparent 12.5%, transparent 87%, #000000 87.5%, #000000),
                linear-gradient(150deg, #000000 12%, transparent 12.5%, transparent 87%, #000000 87.5%, #000000),
                linear-gradient(30deg, #000000 12%, transparent 12.5%, transparent 87%, #000000 87.5%, #000000),
                linear-gradient(150deg, #000000 12%, transparent 12.5%, transparent 87%, #000000 87.5%, #000000),
                linear-gradient(60deg, rgba(200,166,117,0.1) 25%, transparent 25.5%, transparent 75%, rgba(200,166,117,0.1) 75%, rgba(200,166,117,0.1)),
                linear-gradient(60deg, rgba(200,166,117,0.1) 25%, transparent 25.5%, transparent 75%, rgba(200,166,117,0.1) 75%, rgba(200,166,117,0.1));
            background-size: 40px 70px;
            background-position: 0 0, 0 0, 20px 35px, 20px 35px, 0 0, 20px 35px;
            opacity: 0.1;
            z-index: 0;
        }
        
        /* 中央光晕效果 */
        .steps::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 50% 50%, 
                rgba(200,166,117,0.15),
                transparent 60%);
            animation: pulseGlow 6s ease-in-out infinite;
            z-index: 1;
        }
        
        @keyframes pulseGlow {
            0%, 100% {
                opacity: 0.3;
                transform: scale(1);
            }
            50% {
                opacity: 0.5;
                transform: scale(1.2);
            }
        }
        
        .steps .container {
            position: relative;
            z-index: 2;
        }
        
        /* 标题样式增强 */
        .steps .section-title {
            text-align: center;
            font-size: 2.8rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--primary-light), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            position: relative;
            animation: titleGlow 3s ease-in-out infinite alternate;
        }
        
        @keyframes titleGlow {
            0% {
                text-shadow: 0 0 5px rgba(200, 166, 117, 0.3);
            }
            100% {
                text-shadow: 0 0 15px rgba(200, 166, 117, 0.6);
            }
        }
        
        /* 副标题样式增强 */
        .steps .section-subtitle {
            text-align: center;
            color: var(--gray);
            margin-bottom: 5rem;
            font-size: 1.2rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            position: relative;
        }
        
        .steps .section-subtitle::after {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 2px;
            background: linear-gradient(90deg, transparent, var(--primary), transparent);
        }
        
        /* 步骤容器布局优化 */
        .steps-container {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 2rem;
            position: relative;
            margin: 0 auto;
            max-width: 1000px;
        }
        
        /* 步骤卡片样式 */
        .step {
            flex: 1;
            position: relative;
            transform: translateY(50px);
            opacity: 0;
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        /* 步骤卡片显示动画 */
        .step-visible {
            transform: translateY(0);
            opacity: 1;
        }
        
        /* 装饰元素样式 */
        .step-decoration {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            pointer-events: none;
        }
        
        .dot {
            position: absolute;
            background: var(--primary);
            border-radius: 50%;
            opacity: 0;
            transform: scale(0);
            transition: all 0.5s ease;
        }
        
        .dot-1 {
            width: 8px;
            height: 8px;
            top: 20%;
            right: 15%;
        }
        
        .dot-2 {
            width: 5px;
            height: 5px;
            top: 70%;
            left: 20%;
        }
        
        .dot-3 {
            width: 6px;
            height: 6px;
            bottom: 10%;
            right: 30%;
        }
        
        .line {
            position: absolute;
            background: linear-gradient(90deg, transparent, var(--primary), transparent);
            height: 1px;
            opacity: 0;
            transform: scaleX(0);
            transition: all 0.5s ease;
        }
        
        .line-1 {
            width: 40px;
            top: 30%;
            left: 10%;
            transform-origin: left center;
        }
        
        .line-2 {
            width: 60px;
            bottom: 25%;
            right: 10%;
            transform-origin: right center;
        }
        
        .step-hover .dot {
            opacity: 0.7;
            transform: scale(1);
        }
        
        .step-hover .line {
            opacity: 0.3;
            transform: scaleX(1);
        }
        
        /* 连接线样式 */
        .step-connector {
            position: absolute;
            top: 50%;
            right: -1rem;
            width: 2rem;
            height: 2rem;
            transform: translateY(-50%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 3;
        }
        
        .connector-line {
            position: absolute;
            height: 2px;
            width: 100%;
            background: linear-gradient(to right, var(--primary), var(--primary-dark));
            transform: scaleX(0);
            transform-origin: left center;
            transition: transform 0.6s ease;
        }
        
        .connector-arrow {
            position: absolute;
            right: 0;
            color: var(--primary);
            opacity: 0;
            transform: translateX(-10px);
            transition: all 0.6s ease;
        }
        
        .step-visible .connector-line {
            transform: scaleX(1);
        }
        
        .step-visible .connector-arrow {
            opacity: 1;
            transform: translateX(0);
        }
        
        /* 隐藏最后一个步骤的连接器 */
        .step:last-child .step-connector {
            display: none;
        }
        
        /* 底部装饰 */
        .steps-decoration {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 4rem;
            opacity: 0.5;
        }
        
        .decoration-line {
            height: 1px;
            width: 100px;
            background: linear-gradient(90deg, transparent, var(--primary), transparent);
        }
        
        .decoration-circle {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: var(--primary);
            margin: 0 1rem;
            box-shadow: 0 0 10px var(--primary);
        }
        
        /* 响应式调整 */
        @media (max-width: 992px) {
            .steps-container {
                flex-direction: column;
                gap: 4rem;
            }
            
            .step {
                width: 100%;
                max-width: 350px;
            }
            
            .step-connector {
                top: auto;
                right: 50%;
                bottom: -2rem;
                transform: translateX(50%) rotate(90deg);
            }
        }
        
        @media (max-width: 768px) {
            .steps {
                padding: 5rem 0;
            }
            
            .steps .section-title {
                font-size: 2.2rem;
            }
            
            .steps .section-subtitle {
                font-size: 1rem;
                margin-bottom: 3rem;
            }
            
            .step-card {
                padding: 2rem 1rem;
            }
            
            .step-circle {
                width: 100px;
                height: 100px;
            }
            
            .progress-ring {
                width: 100px;
                height: 100px;
            }
            
            .progress-ring-circle {
                stroke-dasharray: 282.74;
                stroke-dashoffset: 282.74;
            }
            
            .step-number {
                font-size: 1.8rem;
            }
            
            .step-icon {
                font-size: 2.2rem;
            }
            
            .step h3 {
                font-size: 1.3rem;
            }
        }
        </style>
    </section>

    <section class="contact-us">
        <div class="container">
            <!-- 添加左侧虚线球体 -->
            <div class="contact-sphere-left">
                <div class="contact-sphere-inner">
                    <!-- 虚线圆环 -->
                    <div class="contact-sphere-ring"></div>
                    <div class="contact-sphere-ring"></div>
                    <div class="contact-sphere-ring"></div>
                    <!-- 虚线经纬线 -->
                    <div class="contact-sphere-line horizontal"></div>
                    <div class="contact-sphere-line vertical"></div>
                    <div class="contact-sphere-line diagonal-1"></div>
                    <div class="contact-sphere-line diagonal-2"></div>
                    <!-- 中心点 -->
                    <div class="contact-sphere-dot"></div>
                </div>
            </div>
            
            <div class="contact-grid">
                <div class="contact-info">
                    <h2>联系我们</h2>
                    <p>如果您有任何问题，请随时与我们联系</p>
                    <div class="contact-methods">
                        <div class="contact-item">
                            <i class="fas fa-envelope"></i>
                            <span>{$contact_email}</span>
                        </div>
                        <div class="contact-item">
                            <i class="fab fa-qq"></i>
                            <span>QQ客服: {$contact_qq}</span>
                        </div>
                        <div class="contact-item">
                            <i class="fab fa-weixin"></i>
                            <span>微信客服: {$contact_wx}</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 添加右侧虚线球体 -->
            <div class="contact-sphere-right">
                <div class="contact-sphere-inner">
                    <!-- 虚线圆环 -->
                    <div class="contact-sphere-ring"></div>
                    <div class="contact-sphere-ring"></div>
                    <div class="contact-sphere-ring"></div>
                    <!-- 虚线经纬线 -->
                    <div class="contact-sphere-line horizontal"></div>
                    <div class="contact-sphere-line vertical"></div>
                    <div class="contact-sphere-line diagonal-1"></div>
                    <div class="contact-sphere-line diagonal-2"></div>
                    <!-- 中心点 -->
                    <div class="contact-sphere-dot"></div>
                </div>
            </div>
        </div>
        
        <!-- 添加虚线球体的CSS样式 -->
        <style>
            /* 联系我们部分的虚线球体样式 */
            .contact-us {
                position: relative;
                overflow: hidden;
            }
            
            /* 左侧球体 */
            .contact-sphere-left {
                position: absolute;
                left: -50px;
                top: 50%;
                transform: translateY(-50%);
                width: 150px;
                height: 150px;
                opacity: 0.3;
                z-index: 0;
                pointer-events: none;
            }
            
            /* 右侧球体 */
            .contact-sphere-right {
                position: absolute;
                right: -50px;
                top: 50%;
                transform: translateY(-50%);
                width: 150px;
                height: 150px;
                opacity: 0.3;
                z-index: 0;
                pointer-events: none;
            }
            
            /* 球体内部容器，添加3D旋转动画 */
            .contact-sphere-inner {
                position: relative;
                width: 100%;
                height: 100%;
                animation: contactSphereRotate 20s linear infinite;
                transform-style: preserve-3d;
            }
            
            .contact-sphere-left .contact-sphere-inner {
                animation-direction: reverse;
            }
            
            @keyframes contactSphereRotate {
                0% { transform: rotateX(20deg) rotateY(0deg) rotateZ(0deg); }
                100% { transform: rotateX(20deg) rotateY(360deg) rotateZ(0deg); }
            }
            
            /* 虚线圆环样式 */
            .contact-sphere-ring {
                position: absolute;
                border: 1px dashed var(--primary);
                border-radius: 50%;
                width: 100%;
                height: 100%;
                top: 0;
                left: 0;
            }
            
            .contact-sphere-ring:nth-child(1) {
                transform: rotateX(60deg);
            }
            
            .contact-sphere-ring:nth-child(2) {
                transform: rotateY(60deg);
                width: 80%;
                height: 80%;
                top: 10%;
                left: 10%;
            }
            
            .contact-sphere-ring:nth-child(3) {
                transform: rotateZ(60deg);
                width: 60%;
                height: 60%;
                top: 20%;
                left: 20%;
            }
            
            /* 虚线经纬线样式 */
            .contact-sphere-line {
                position: absolute;
                background: none;
                border: 1px dashed var(--primary);
            }
            
            .contact-sphere-line.horizontal {
                width: 100%;
                height: 0;
                top: 50%;
                left: 0;
            }
            
            .contact-sphere-line.vertical {
                width: 0;
                height: 100%;
                top: 0;
                left: 50%;
            }
            
            .contact-sphere-line.diagonal-1 {
                width: 142%;
                height: 0;
                top: 50%;
                left: -21%;
                transform: rotate(45deg);
            }
            
            .contact-sphere-line.diagonal-2 {
                width: 142%;
                height: 0;
                top: 50%;
                left: -21%;
                transform: rotate(-45deg);
            }
            
            /* 中心点样式 */
            .contact-sphere-dot {
                position: absolute;
                width: 6px;
                height: 6px;
                background: var(--primary);
                border-radius: 50%;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                box-shadow: 0 0 10px var(--primary-light);
                animation: pulseDot 2s ease-in-out infinite alternate;
            }
            
            @keyframes pulseDot {
                from {
                    transform: translate(-50%, -50%) scale(0.8);
                    opacity: 0.6;
                }
                to {
                    transform: translate(-50%, -50%) scale(1.2);
                    opacity: 1;
                }
            }
            
            /* 响应式调整 */
            @media (max-width: 992px) {
                .contact-sphere-left, .contact-sphere-right {
                    width: 120px;
                    height: 120px;
                }
            }
            
            @media (max-width: 768px) {
                .contact-sphere-left {
                    left: -80px;
                }
                
                .contact-sphere-right {
                    right: -80px;
                }
            }
            
            @media (max-width: 576px) {
                .contact-sphere-left, .contact-sphere-right {
                    display: none;
                }
            }
        </style>
    </section>

    <footer class="footer">
        <div class="container">
            <!-- 添加图片中显示的页脚布局 -->
            <div class="footer-new-layout">
                <!-- 左侧Logo和标语 -->
                <div class="footer-logo-section">
                    <div class="footer-logo">
                        <img src="{$footer_logo|default='/assets/plugin/Blackglod/plugin/Blackglod/images/logo.png'}" alt="{$siteName|default='Blackglod商城'}" class="logo-img">
                        <span class="slogan">{$footer_slogan|default='专业靠谱 · 不将就'}</span>
                    </div>
                    <div class="footer-address">{$footer_address|default='中国 · 广西'}</div>
                </div>
                {if $footer_service_show == 1}
                <div class="footer-column">
                    <h3>服务中心</h3>
                    <a href="{$footer_service_1_link|default='#'}">{$footer_service_1|default='卡密查询'}</a>
                    <a href="{$footer_service_2_link|default='#'}">{$footer_service_2|default='投诉中心'}</a>
                    <a href="{$footer_service_3_link|default='#'}">{$footer_service_3|default='卡密工具'}</a>
                    <a href="{$footer_service_4_link|default='#'}">{$footer_service_4|default='商户入驻'}</a>
                </div>
                {/if}
                
                {if $footer_help_show == 1}
                <div class="footer-column">
                    <h3>帮助中心</h3>
                    <a href="{$footer_help_1_link|default='#'}">{$footer_help_1|default='常见问题'}</a>
                    <a href="{$footer_help_2_link|default='#'}">{$footer_help_2|default='系统公告'}</a>
                    <a href="{$footer_help_3_link|default='#'}">{$footer_help_3|default='结算公告'}</a>
                    <a href="{$footer_help_4_link|default='#'}">{$footer_help_4|default='新闻动态'}</a>
                </div>
                {/if}
                
                
                {if $footer_legal_show == 1}
                <div class="footer-column">
                    <h3>法律责任</h3>
                    <a href="{$footer_legal_1_link|default='#'}">{$footer_legal_1|default='免责声明'}</a>
                    <a href="{$footer_legal_2_link|default='#'}">{$footer_legal_2|default='禁售商品'}</a>
                    <a href="{$footer_legal_3_link|default='#'}">{$footer_legal_3|default='服务协议'}</a>
                    <a href="{$footer_legal_4_link|default='#'}">{$footer_legal_4|default='隐私政策'}</a>
                </div>
                {/if}
                
                
                {if $footer_links_show == 1}
                <div class="footer-column">
                    <h3>友情链接</h3>
                    <a href="{$footer_links_1_link|default='#'}">{$footer_links_1|default='一意支付'}</a>
                    <a href="{$footer_links_2_link|default='#'}">{$footer_links_2|default='支付宝'}</a>
                    <a href="{$footer_links_3_link|default='#'}">{$footer_links_3|default='微信支付'}</a>
                    <a href="{$footer_links_4_link|default='#'}">{$footer_links_4|default='QQ钱包'}</a>
                </div>
                {/if}
                
                <!-- 右侧二维码区域 -->
                <div class="footer-qrcode-section">
                    <div class="qrcode-item">
                        <h3 class="qrcode-title">{$footer_wechat_title|default='官方公众号'}</h3>
                        <div class="qrcode-img">
                            {if !empty($footer_wechat_qrcode)}
                            <img src="{$footer_wechat_qrcode}" alt="{$footer_wechat_title|default='官方公众号'}">
                            {else}
                            <div class="qrcode-placeholder">请设置<br>公众号二维码链接</div>
                            {/if}
                        </div>
                        <div class="qrcode-desc">{$footer_wechat_desc|default='扫码关注微信公众号<br>实时获取订单及优惠'|raw}</div>
                    </div>
                    
                    <div class="qrcode-item">
                        <h3 class="qrcode-title">{$footer_service_title|default='官方客服'}</h3>
                        <div class="qrcode-img">
                            {if !empty($footer_service_qrcode)}
                            <img src="{$footer_service_qrcode}" alt="{$footer_service_title|default='官方客服'}">
                            {else}
                            <div class="qrcode-placeholder">请设置<br>客服二维码链接</div>
                            {/if}
                        </div>
                        <div class="qrcode-desc">{$footer_service_desc|default='扫码联系客服团队<br>实时解决订单问题'|raw}</div>
                    </div>
                </div>
            </div><!-- 第一部分结束 -->
            <div class="copyright">
                <span>{$siteName|default='Blackglod'} - 版权所有 © {$year|default='2022'}-至今</span>
                {if !empty($icpNumber)}
                <a href="https://beian.miit.gov.cn/" target="_blank">{$icpNumber}</a>
                {/if}
                {if !empty($gaNumber)}
                <a href="{$icpCert}" target="_blank">{$gaNumber}</a>
                {/if}
            </div>
        </div>
    </footer>

    <div class="particles-container"></div>
    
    <!-- 添加进度条元素 -->
    <div class="progress-bar"></div>

    <script>
        // 页面滚动时的头部效果
        window.addEventListener('scroll', () => {
            const header = document.querySelector('.header');
            if (window.scrollY > 50) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
        });

        // 调整金额字体大小
        function adjustAmountFontSize(element) {
            const amount = element.textContent.replace(/,/g, '');
            const length = amount.split('.')[0].length; // 只计算整数部分的长度
            
            // 移除所有长度相关的类
            element.classList.remove('length-9', 'length-10', 'length-11', 'length-12');
            
            // 添加对应长度的类
            if (length >= 9) {
                element.classList.add(`length-${length}`);
            }
        }

        // 修改原有的animateValue函数
        function animateValue(element, start, end, duration) {
            let startTimestamp = null;
            const decimal = String(end).includes('.') ? String(end).split('.')[1].length : 0;
            const isAmount = element.closest('.stat-item').querySelector('div:last-child').textContent === '交易金额';
            
            if (isAmount) {
                element.classList.add('amount');
            }
            
            // 确保元素可见
            element.style.opacity = "1";
            
            const step = (timestamp) => {
                if (!startTimestamp) startTimestamp = timestamp;
                const progress = Math.min((timestamp - startTimestamp) / duration, 1);
                const currentValue = progress * (end - start) + start;
                
                element.textContent = decimal ? 
                    currentValue.toFixed(decimal) : 
                    Math.floor(currentValue).toLocaleString();
                    
                if (isAmount) {
                    adjustAmountFontSize(element);
                }
            
                if (progress < 1) {
                    window.requestAnimationFrame(step);
                }
            };
            
            window.requestAnimationFrame(step);
        }

        // 观察者设置
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    // 立即设置可见性
                    entry.target.style.opacity = "1";
                    
                    if (!entry.target.classList.contains('animated')) {
                        entry.target.classList.add('visible', 'animated');
                        const finalValue = parseFloat(entry.target.textContent.replace(/,/g, ''));
                        animateValue(entry.target, 0, finalValue, 2000);
                    }
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '50px'
        });

        // 当页面加载完成后开始观察
        document.addEventListener('DOMContentLoaded', () => {
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(stat => {
                // 确保元素默认可见
                stat.style.opacity = "1";
                observer.observe(stat);
            });
        });

        // 移动端菜单
        const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
        const navLinks = document.querySelector('.nav-links');
        
        mobileMenuBtn.addEventListener('click', () => {
            navLinks.style.display = navLinks.style.display === 'flex' ? 'none' : 'flex';
        });

    // 创建星星
    function createStars() {
        const container = document.querySelector('.stars-container');
        const fragment = document.createDocumentFragment();
        const starCount = window.innerWidth > 768 ? 120 : 60;

        for (let i = 0; i < starCount; i++) {
            const star = document.createElement('div');
            star.className = 'star';
            
            const x = Math.random() * 100;
            const y = Math.random() * 100;
            const size = Math.random() * 6 + 2;
            const delay = Math.random() * 5;
            const duration = Math.random() * 3 + 2;
            const opacity = Math.random() * 0.8 + 0.4;

            star.style.cssText = `
                left: ${x}%;
                top: ${y}%;
                width: ${size}px;
                height: ${size}px;
                --delay: ${delay}s;
                --duration: ${duration}s;
                --opacity: ${opacity};
            `;
            
            fragment.appendChild(star);
        }
        
        container.appendChild(fragment);
    }

    // 页面加载完成后创建星星
    document.addEventListener('DOMContentLoaded', createStars);

    // 为星星添加视差效果
    let mouseMoveTimeout;
    document.addEventListener('mousemove', (e) => {
        if (!mouseMoveTimeout) {
            requestAnimationFrame(() => {
                const stars = document.querySelectorAll('.star');
                const mouseX = e.clientX / window.innerWidth - 0.5;
                const mouseY = e.clientY / window.innerHeight - 0.5;

                stars.forEach(star => {
                    const depth = Math.random() * 5;
                    star.style.transform = `translate3d(${mouseX * depth}px, ${mouseY * depth}px, 0)`;
                });
                mouseMoveTimeout = null;
            });
            mouseMoveTimeout = true;
        }
    });

    // 添加页面加载进度条动画
    document.addEventListener('DOMContentLoaded', () => {
        const progressBar = document.querySelector('.progress-bar');
        if (progressBar) { // 添加存在性检查
            progressBar.style.transform = 'scaleX(1)';
            setTimeout(() => {
                progressBar.style.opacity = '0';
            }, 1000);
        }
    });

    // 添加页面滚动时的平滑动画
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            document.querySelector(this.getAttribute('href')).scrollIntoView({
                behavior: 'smooth'
            });
        });
    });

    // 创建高级粒子动画
    function createParticles() {
        const container = document.querySelector('.particles-container');
        const particleCount = 50;
        
        function createParticle() {
            const particle = document.createElement('div');
            particle.className = 'particle';
            
            // 随机大小
            const size = Math.random() * 4 + 2;
            
            // 随机位置
            const x = Math.random() * window.innerWidth;
            const y = Math.random() * window.innerHeight;
            
            // 随机移动参数     
            const translateX = (Math.random() - 0.5) * 200;
            const translateY = (Math.random() - 0.5) * 200;
            const rotate = Math.random() * 360;
            const scale = Math.random() * 0.5 + 0.5;
            const opacity = Math.random() * 0.5 + 0.3;
            
            particle.style.cssText = `
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                --particle-translateX: ${translateX}px;
                --particle-translateY: ${translateY}px;
                --particle-rotate: ${rotate}deg;
                --particle-scale: ${scale};
                --particle-opacity: ${opacity};
            `;
            
            container.appendChild(particle);
            
            
            if (Math.random() > 0.7) {
                createLine(x, y, translateX, translateY);
            }
            
            
            particle.addEventListener('animationend', () => {
                particle.remove();
                createParticle();
            });
        }
        
        function createLine(x1, y1, x2, y2) {
            const line = document.createElement('div');
            line.className = 'particle-line';
            
            const length = Math.sqrt(Math.pow(x2, 2) + Math.pow(y2, 2));
            const angle = Math.atan2(y2, x2) * (180 / Math.PI);
            
            line.style.cssText = `
                left: ${x1}px;
                top: ${y1}px;
                width: ${length}px;
                transform: rotate(${angle}deg);
            `;
            
            container.appendChild(line);
            
            
            line.addEventListener('animationend', () => {
                line.remove();
            });
        }
        
        
        for (let i = 0; i < particleCount; i++) {
            createParticle();
        }
    }


    document.addEventListener('DOMContentLoaded', createParticles);

    
    document.addEventListener('DOMContentLoaded', () => {
        const sphereGrid = document.querySelector('.sphere-grid');
        
            
        for (let i = 0; i < 180; i += 10) {
            const line = document.createElement('div');
            line.className = 'longitude-line';
            line.style.setProperty('--rotation', `${i}deg`);
            sphereGrid.appendChild(line);
        }
        
            
        for (let i = 5; i < 175; i += 5) {
            const line = document.createElement('div');
            line.className = 'latitude-line';
            // 计算缩放比例，使用余弦函数创造球形效果
            const scale = Math.abs(Math.cos(i * Math.PI / 180));
            line.style.setProperty('--rotation', `${i}deg`);
            line.style.setProperty('--scale', scale);
            sphereGrid.appendChild(line);
        }
    });

    
    function addFloatingParticles() {
        const container = document.querySelector('.floating-particles');
        const particleCount = 8;
        
        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'floating-particle';
            
            // 随机位置和移动
            const x = Math.random() * 100;
            const y = Math.random() * 100;
            const tx = (Math.random() - 0.5) * 40;
            const ty = (Math.random() - 0.5) * 40;
            
            particle.style.cssText = `
                left: ${x}%;
                top: ${y}%;
                --tx: ${tx}px;
                --ty: ${ty}px;
                animation-delay: ${Math.random() * 2}s;
            `;
            
            container.appendChild(particle);
        }
    }

    document.addEventListener('DOMContentLoaded', () => {
        addFloatingParticles();
    });

    function createDataStreams() {
        const container = document.querySelector('.sphere-decorations');
        const streamCount = 12;
        
        for (let i = 0; i < streamCount; i++) {
            const stream = document.createElement('div');
            stream.className = 'data-stream';
            
            // 随机起点和方向
            const startX = Math.random() * 100;
            const startY = Math.random() * 100;
            const tx = (Math.random() - 0.5) * 100;
            const ty = (Math.random() - 0.5) * 100;
            
            stream.style.cssText = `
                left: ${startX}%;
                top: ${startY}%;
                --tx: ${tx}px;
                --ty: ${ty}px;
                animation-delay: ${Math.random() * 3}s;
            `;
            
            container.appendChild(stream);
        }
    }

    function createConnectionLines() {
        const container = document.querySelector('.sphere-decorations');
        const lineCount = 6;
        
        for (let i = 0; i < lineCount; i++) {
            const line = document.createElement('div');
            line.className = 'connection-line';
            
            const angle = (i / lineCount) * 360;
            const length = 40 + Math.random() * 40;
            
            line.style.cssText = `
                width: ${length}px;
                left: 50%;
                top: 50%;
                transform: rotate(${angle}deg) translateX(-50%);
                animation-delay: ${Math.random() * 3}s;
            `;
            
            container.appendChild(line);
        }
    }

    document.addEventListener('DOMContentLoaded', () => {
        addFloatingParticles();
        createDataStreams();
        createConnectionLines();
    });
    function initCharts() {
        // 添加检查确保元素存在
        const transactionChart = document.getElementById('transactionChart');
        const successRateChart = document.getElementById('successRateChart');
        
        // 只有当元素存在且Chart对象可用时才初始化图表
        if (transactionChart && typeof Chart !== 'undefined') {
            const transactionCtx = transactionChart.getContext('2d');
            new Chart(transactionCtx, {
                type: 'line',
                data: {
                    labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
                    datasets: [{
                        label: '交易量',
                        data: [65, 59, 80, 81, 56, 55],
                        borderColor: '#C8A675',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }
        
        if (successRateChart && typeof Chart !== 'undefined') {
            const successCtx = successRateChart.getContext('2d');
            new Chart(successCtx, {
                type: 'doughnut',
                data: {
                    labels: ['成功', '失败'],
                    datasets: [{
                        data: [99.9, 0.1],
                        backgroundColor: ['#C8A675', '#666666']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }
    }
    document.addEventListener('DOMContentLoaded', () => {
        // 只有在需要图表时调用初始化
        // 如果不需要图表功能，可以注释掉下面这行
        // initCharts();
    });

    // 添加球体粒子效果
    function createSphereParticles() {
        const container = document.querySelector('.sphere-particles');
        if (!container) return;
        
        // 清空现有粒子
        container.innerHTML = '';
        
        // 创建新粒子
        const particleCount = 15;
        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'sphere-particle';
            
            // 随机位置（球面上的点）
            const theta = Math.random() * Math.PI * 2; // 经度
            const phi = Math.acos(2 * Math.random() - 1); // 纬度
            const radius = 48; // 球半径
            
            const x = radius * Math.sin(phi) * Math.cos(theta);
            const y = radius * Math.sin(phi) * Math.sin(theta);
            const z = radius * Math.cos(phi);
            
            // 随机大小和动画延迟
            const size = Math.random() * 3 + 1;
            const delay = Math.random() * 4;
            
            particle.style.cssText = `
                width: ${size}px;
                height: ${size}px;
                transform: translate3d(${x}px, ${y}px, ${z}px);
                animation-delay: ${delay}s;
            `;
            
            container.appendChild(particle);
        }
    }
    
    // 添加球体的交互效果
    function addSphereInteraction() {
        const sphere = document.querySelector('.sphere');
        if (!sphere) return;
        
        sphere.addEventListener('mouseenter', () => {
            sphere.style.animationPlayState = 'paused';
            document.querySelector('.sphere-inner').style.animationPlayState = 'paused';
        });
        
        sphere.addEventListener('mouseleave', () => {
            sphere.style.animationPlayState = 'running';
            document.querySelector('.sphere-inner').style.animationPlayState = 'running';
        });
    }

    // 添加页面加载进度条动画
    document.addEventListener('DOMContentLoaded', () => {
        // 现有代码...
        
        // 添加球体效果
        createSphereParticles();
        addSphereInteraction();
        
        // 定期更新粒子以保持视觉效果
        setInterval(createSphereParticles, 10000);
    });

    // 修复粒子生成函数
    function addFixedFloatingParticles() {
        const container = document.querySelector('.floating-particles');
        if (!container) return; // 如果容器不存在则退出函数
        
        const particleCount = 8;
        
        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'floating-particle';
            
            // 随机位置和移动
            const x = Math.random() * 100;
            const y = Math.random() * 100;
            const tx = (Math.random() - 0.5) * 40;
            const ty = (Math.random() - 0.5) * 40;
            
            particle.style.cssText = `
                left: ${x}%;
                top: ${y}%;
                --tx: ${tx}px;
                --ty: ${ty}px;
                animation-delay: ${Math.random() * 2}s;
            `;
            
            container.appendChild(particle);
        }
    }

    // 修复数据流生成函数
    function createFixedDataStreams() {
        const container = document.querySelector('.sphere-decorations');
        if (!container) return; // 如果容器不存在则退出函数
        
        const streamCount = 12;
        
        for (let i = 0; i < streamCount; i++) {
            const stream = document.createElement('div');
            stream.className = 'data-stream';
            
            // 随机起点和方向
            const startX = Math.random() * 100;
            const startY = Math.random() * 100;
            const tx = (Math.random() - 0.5) * 100;
            const ty = (Math.random() - 0.5) * 100;
            
            stream.style.cssText = `
                left: ${startX}%;
                top: ${startY}%;
                --tx: ${tx}px;
                --ty: ${ty}px;
                animation-delay: ${Math.random() * 3}s;
            `;
            
            container.appendChild(stream);
        }
    }

    // 修复连接线生成函数
    function createFixedConnectionLines() {
        const container = document.querySelector('.sphere-decorations');
        if (!container) return; // 如果容器不存在则退出函数
        
        const lineCount = 6;
        
        for (let i = 0; i < lineCount; i++) {
            const line = document.createElement('div');
            line.className = 'connection-line';
            
            const angle = (i / lineCount) * 360;
            const length = 40 + Math.random() * 40;
            
            line.style.cssText = `
                width: ${length}px;
                left: 50%;
                top: 50%;
                transform: rotate(${angle}deg) translateX(-50%);
                animation-delay: ${Math.random() * 3}s;
            `;
            
            container.appendChild(line);
        }
    }

    // 替换原始的事件监听器
    document.addEventListener('DOMContentLoaded', function() {
        // 使用新的修复函数
        addFixedFloatingParticles();
        createFixedDataStreams();
        createFixedConnectionLines();
        
        // 其他现有初始化代码
        if (typeof createSphereParticles === 'function') {
            createSphereParticles();
        }
        
        if (typeof addSphereInteraction === 'function') {
            addSphereInteraction();
        }
    });
    </script>
</body>
</html>