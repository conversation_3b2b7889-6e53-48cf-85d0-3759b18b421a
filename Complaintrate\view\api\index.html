<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <title>投诉费率管理</title>
    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <style>
        .page-container {
            padding: 20px;
            min-height: 800px;
        }
        .button-container {
            margin-bottom: 20px;
        }
        .el-table {
            margin-top: 20px;
        }
        .warning-text {
            color: #E6A23C;
        }
        .warning-text.danger {
            color: #F56C6C;
        }
        .pagination-container {
            margin-top: 20px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="page-container">
            <el-tabs v-model="activeTab">
                <el-tab-pane label="等级管理" name="groups">
                    <div class="button-container">
                        <el-button type="primary" @click="showSettingDialog">时间设置</el-button>
                        <el-button type="success" @click="handleManualCalc">手动计算</el-button>
                    </div>

                    <el-table :data="channelGroups" border style="width: 100%">
                        <el-table-column prop="id" label="ID" width="60"></el-table-column>
                        <el-table-column prop="name" label="等级" min-width="120"></el-table-column>
                        <el-table-column label="投诉率/订单" min-width="180">
                            <template #default="scope">
                                <div>
                                    投诉率：{{ scope.row.complaint_rate }}%<br>
                                    投诉订单：{{ scope.row.complaint_count }}单
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="费用" min-width="280">
                            <template #default="scope">
                                <div>
                                    月费：<el-input-number 
                                        v-model="scope.row.price" 
                                        :min="0" 
                                        :precision="2"
                                        :step="100"
                                        size="small"
                                        style="width: 120px"
                                        @change="(val) => handlePriceChange(scope.row.id, 'price', val)"
                                    ></el-input-number>元<br>
                                    季费：<el-input-number 
                                        v-model="scope.row.quarterly_price" 
                                        :min="0" 
                                        :precision="2"
                                        :step="100"
                                        size="small"
                                        style="width: 120px"
                                        @change="(val) => handlePriceChange(scope.row.id, 'quarterly_price', val)"
                                    ></el-input-number>元<br>
                                    年费：<el-input-number 
                                        v-model="scope.row.yearly_price" 
                                        :min="0" 
                                        :precision="2"
                                        :step="100"
                                        size="small"
                                        style="width: 120px"
                                        @change="(val) => handlePriceChange(scope.row.id, 'yearly_price', val)"
                                    ></el-input-number>元
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="状态" width="100">
                            <template #default="scope">
                                <el-tag :type="scope.row.can_upgrade ? 'success' : 'info'">
                                    {{ scope.row.can_upgrade ? '可开通' : '不可开通' }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="150" align="center">
                            <template #default="scope">
                                <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-tab-pane>

                <!-- 投诉率列表 tab -->
                <el-tab-pane label="投诉率列表" name="complaints">
                    <el-table :data="complaintsList" border style="width: 100%">
                        <el-table-column label="投诉信息" min-width="800">
                            <template #default="scope">
                                <div style="display: flex; align-items: center; gap: 20px;">
                                    <span style="min-width: 150px;">
                                        账号：{{ scope.row.username }}
                                    </span>
                                    <span style="min-width: 150px;">
                                        昵称：{{ scope.row.nickname }}
                                    </span>
                                    <span style="display: flex; align-items: center; gap: 15px;">
                                        <span>
                                            投诉率：
                                            <span :class="{'warning-text': scope.row.complaint_rate > 3, 'danger': scope.row.complaint_rate > 5}">
                                                {{ scope.row.complaint_rate }}%
                                            </span>
                                        </span>
                                        <span>
                                            投诉订单：{{ scope.row.complaint_count }}单
                                        </span>
                                        <span>
                                            总订单：{{ scope.row.total_orders }}单
                                        </span>
                                    </span>
                                    <span>
                                        最近投诉：{{ formatDate(scope.row.latest_complaint_time) }}
                                    </span>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>

                    <div class="pagination-container">
                        <el-pagination
                            v-model:current-page="currentPage"
                            v-model:page-size="pageSize"
                            :page-sizes="[10, 20, 50, 100]"
                            :total="total"
                            layout="total, sizes, prev, pager, next"
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                        />
                    </div>
                </el-tab-pane>
            </el-tabs>

            <!-- 编辑对话框 -->
            <el-dialog
                :title="dialogTitle"
                v-model="dialogVisible"
                width="600px"
            >
                <el-form :model="form" label-width="120px">
                    <el-form-item label="等级名称">
                        <el-input v-model="form.name" disabled></el-input>
                    </el-form-item>

                    <el-form-item label="投诉率" required>
                        <el-input-number 
                            v-model="form.complaint_rate" 
                            :min="0" 
                            :max="100"
                            :precision="2"
                            :step="0.1"
                            controls-position="right">
                            <template #append>%</template>
                        </el-input-number>
                    </el-form-item>

                    <el-form-item label="投诉订单" required>
                        <el-input-number 
                            v-model="form.complaint_count" 
                            :min="0" 
                            :precision="0"
                            :step="1"
                            controls-position="right">
                            <template #append>单</template>
                        </el-input-number>
                    </el-form-item>

                    <el-form-item label="是否可开通">
                        <el-switch v-model="form.can_upgrade"></el-switch>
                    </el-form-item>
                </el-form>

                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="dialogVisible = false">取消</el-button>
                        <el-button type="primary" @click="handleSubmit">确定</el-button>
                    </span>
                </template>
            </el-dialog>

            <!-- 时间设置对话框 -->
            <el-dialog
                title="时间设置"
                v-model="settingDialogVisible"
                width="500px"
            >
                <el-form :model="settingForm" label-width="120px">
                    <el-form-item label="自动更新">
                        <el-switch v-model="settingForm.auto_update_status"></el-switch>
                    </el-form-item>

                    <el-form-item label="更新间隔">
                        <el-input-number 
                            v-model="settingForm.update_interval"
                            :min="5"
                            :max="86400"
                            :step="1"
                            controls-position="right">
                        </el-input-number>
                        <span class="ml-2">秒</span>
                    </el-form-item>

                    <el-form-item label="更新周期">
                        <el-select v-model="settingForm.update_cycle">
                            <el-option label="每月" value="monthly"></el-option>
                            <el-option label="每季度" value="quarterly"></el-option>
                            <el-option label="每年" value="yearly"></el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="计算模式">
                        <el-select v-model="settingForm.calc_mode">
                            <el-option label="实时计算" value="realtime"></el-option>
                            <el-option label="定时计算" value="scheduled"></el-option>
                        </el-select>
                    </el-form-item>
                </el-form>

                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="settingDialogVisible = false">取消</el-button>
                        <el-button type="primary" @click="handleSettingSubmit">确定</el-button>
                    </span>
                </template>
            </el-dialog>
        </div>
    </div>

    <script>
        const { createApp, ref, onMounted, watch } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        const app = createApp({
            setup() {
                const activeTab = ref('groups');
                const channelGroups = ref([]);
                const complaintsList = ref([]);
                const currentPage = ref(1);
                const pageSize = ref(10);
                const total = ref(0);
                const sortType = ref('latest_time');
                const dialogVisible = ref(false);
                const dialogTitle = ref('编辑等级');
                const settingDialogVisible = ref(false);
                const settingForm = ref({
                    auto_update_status: false,
                    update_interval: 5,
                    update_cycle: 'monthly',
                    calc_mode: 'realtime'
                });
                const rateGroups = ref([]);
                const form = ref({
                    id: '',
                    name: '',
                    complaint_rate: 0,
                    complaint_count: 0,
                    can_upgrade: true,
                    disabled_message: ''
                });

                // 获取基础设置
                const getSettings = async () => {
                    try {
                        const res = await axios.get('/plugin/Complaintrate/Api/getSettings');
                        if (res.data.code === 200) {
                            const data = res.data.data;
                            settingForm.value = {
                                auto_update_status: Boolean(data.auto_update_status),
                                update_interval: Math.max(5, parseInt(data.update_interval)),
                                update_cycle: data.update_cycle,
                                calc_mode: data.calc_mode
                            };
                        }
                    } catch (error) {
                        ElMessage.error('获取设置失败');
                    }
                };

                // 保存基础设置
                const saveSettings = async () => {
                    try {
                        const res = await axios.post('/plugin/Complaintrate/Api/saveSettings', settingForm.value);
                        if (res.data.code === 200) {
                            ElMessage.success('保存成功');
                        }
                    } catch (error) {
                        ElMessage.error('保存失败');
                    }
                };

                // 获取渠道组列表
                const getChannelGroups = async () => {
                    try {
                        const res = await axios.post('/plugin/Complaintrate/Api/adminChannelGroups');
                        if (res.data.code === 200) {
                            channelGroups.value = res.data.data;
                        }
                    } catch (error) {
                        ElMessage.error('获取列表失败');
                    }
                };

                // 获取投诉率列表
                const getComplaintsList = async () => {
                    try {
                        const res = await axios.post('/plugin/Complaintrate/Api/getComplaintsList', {
                            page: currentPage.value,
                            limit: pageSize.value,
                            sort_type: sortType.value
                        });
                        if (res.data.code === 200) {
                            complaintsList.value = res.data.data.list;
                            total.value = res.data.data.total;
                        }
                    } catch (error) {
                        ElMessage.error('获取列表失败');
                    }
                };

                // 获取费率组列表
                const getRateGroups = async () => {
                    try {
                        const res = await axios.post('/plugin/Complaintrate/Api/getRateGroups');
                        if (res.data.code === 200) {
                            rateGroups.value = res.data.data;
                        }
                    } catch (error) {
                        ElMessage.error('获取费率组失败');
                    }
                };

                // 格式化日期
                const formatDate = (timestamp) => {
                    if (!timestamp) return '';
                    const date = new Date(timestamp * 1000);
                    return date.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit'
                    });
                };

                // 处理分页大小变化
                const handleSizeChange = (val) => {
                    pageSize.value = val;
                    getComplaintsList();
                };

                // 处理页码变化
                const handleCurrentChange = (val) => {
                    currentPage.value = val;
                    getComplaintsList();
                };

                // 监听排序方式变化
                watch(sortType, () => {
                    currentPage.value = 1;
                    getComplaintsList();
                });

                // 编辑渠道组
                const handleEdit = (row) => {
                    dialogTitle.value = '编辑等级';
                    form.value = { ...row };
                    dialogVisible.value = true;
                };

                // 提交表单
                const handleSubmit = async () => {
                    try {
                        const res = await axios.post('/plugin/Complaintrate/Api/saveChannelGroup', form.value);
                        if (res.data.code === 200) {
                            ElMessage.success('保存成功');
                            dialogVisible.value = false;
                            getChannelGroups();
                        } else {
                            ElMessage.error(res.data.msg || '保存失败');
                        }
                    } catch (error) {
                        ElMessage.error('保存失败：' + (error.response?.data?.msg || error.message));
                    }
                };

                // 设置对话框提交
                const handleSettingSubmit = async () => {
                    if (settingForm.value.update_interval < 5) {
                        ElMessage.error('更新间隔不能小于5秒');
                        return;
                    }

                    try {
                        const res = await axios.post('/plugin/Complaintrate/Api/saveSettings', {
                            auto_update_status: Number(settingForm.value.auto_update_status),
                            update_interval: parseInt(settingForm.value.update_interval),
                            update_cycle: settingForm.value.update_cycle,
                            calc_mode: settingForm.value.calc_mode
                        });
                        
                        if (res.data.code === 200) {
                            ElMessage.success('保存成功');
                            settingDialogVisible.value = false;
                        } else {
                            ElMessage.error(res.data.msg || '保存失败');
                        }
                    } catch (error) {
                        ElMessage.error('保存失败：' + (error.response?.data?.msg || error.message));
                    }
                };

                // 监听 activeTab 变化
                watch(activeTab, (newVal) => {
                    if (newVal === 'groups') {
                        getChannelGroups();
                    }
                });

                // 手动计算
                const handleManualCalc = async () => {
                    try {
                        const res = await axios.post('/plugin/Complaintrate/Api/manualCalc');
                        if (res.data.code === 200) {
                            ElMessage.success('计算完成');
                            getChannelGroups();
                        } else {
                            ElMessage.error(res.data.msg || '计算失败');
                        }
                    } catch (error) {
                        ElMessage.error('计算失败：' + (error.response?.data?.msg || error.message));
                    }
                };

                // 显示设置对话框
                const showSettingDialog = () => {
                    getSettings();
                    settingDialogVisible.value = true;
                };

                // 处理价格变更
                const handlePriceChange = async (id, type, value) => {
                    try {
                        const res = await axios.post('/plugin/Complaintrate/Api/savePrice', {
                            id: id,
                            type: type,
                            value: value
                        });
                        
                        if (res.data.code === 200) {
                            ElMessage.success('保存成功');
                        } else {
                            ElMessage.error(res.data.msg || '保存失败');
                            // 刷新数据
                            getChannelGroups();
                        }
                    } catch (error) {
                        ElMessage.error('保存失败：' + (error.response?.data?.msg || error.message));
                        // 刷新数据
                        getChannelGroups();
                    }
                };

                onMounted(() => {
                    getRateGroups();
                    getSettings();
                    getChannelGroups();
                    getComplaintsList();
                });

                return {
                    activeTab,
                    channelGroups,
                    complaintsList,
                    currentPage,
                    pageSize,
                    total,
                    sortType,
                    dialogVisible,
                    dialogTitle,
                    settingDialogVisible,
                    settingForm,
                    rateGroups,
                    form,
                    handleEdit,
                    handleSubmit,
                    handleSettingSubmit,
                    handleSizeChange,
                    handleCurrentChange,
                    formatDate,
                    handleManualCalc,
                    showSettingDialog,
                    handlePriceChange
                };
            }
        });

        app.use(ElementPlus, {
            locale: ElementPlusLocaleZhCn,
        });
        
        app.mount('#app');
    </script>
</body>
</html> 