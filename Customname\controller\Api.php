<?php

namespace plugin\Customname\controller;

use app\common\controller\BasePlugin;
use think\facade\View;

class Api extends BasePlugin {

    protected $scene = [
        'admin'
    ];
    protected $noNeedLogin = [];

    public function index() {
        return View::fetch();
    }

    public function fetchData() {
        $params = [
            'status' => intval(plugconf("Customname.status") ?? 0),
            'merchantName' => plugconf("Customname.merchantName") ?? '',
        ];

        $this->success('success', $params);
    }

    public function save() {
        $status = $this->request->post('status/d', 0);
        $merchantName = $this->request->post('merchantName/s', '');

        plugconf("Customname.status", $status);;
        plugconf("Customname.merchantName", $merchantName);

        $this->success('success');
    }
}
