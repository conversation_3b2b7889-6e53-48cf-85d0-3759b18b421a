<?php

namespace plugin\AutoSettleArticle;
 
use app\common\model\Article as ArticleModel;
use app\common\model\ArticleCategory as ArticleCategoryModel;

class Hook {

    public function handle() {
        $status = intval(plugconf("AutoSettleArticle.status") ?? 0);
        if ($status == 1) {
            $article = ArticleModel::hasWhere('category', ['alias' => 'settlement'])->whereDay('Article.create_time')->find();
            if (!$article) {
                $time = plugconf("AutoSettleArticle.time") ?? '00:00';

                if (date("H:i") >= $time) {
                    $category = ArticleCategoryModel::where(['alias' => 'settlement'])->find();
                    if ($category) {
                        $values = [
                            'year' => date("Y", time()),
                            'month' => date("m", time()),
                            'day' => date("d", time()),
                        ];

                        $article = new ArticleModel();
                        $article->category_id = $category->id;
                        $article->title = replace_placeholders(base64_decode(plugconf("AutoSettleArticle.base64_title") ?? ''), $values);
                        $article->content = replace_placeholders($this->convertToHTML(base64_decode(plugconf("AutoSettleArticle.base64_content") ?? '')), $values);
                        $article->status = 1;
                        $article->create_time = time();
                        $article->save();
                    }
                }
            }
        }
    }

    private function convertToHTML($text) {
        return str_replace("\n", "</br>", $text);
    }
}
