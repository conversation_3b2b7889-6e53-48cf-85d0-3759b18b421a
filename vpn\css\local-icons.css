/* 本地SVG图标样式 */
.local-icon {
    display: inline-block;
    width: 1em;
    height: 1em;
    fill: currentColor;
    vertical-align: -0.125em;
    font-size: inherit;
}

/* 图标大小变体 */
.local-icon-xs { font-size: 0.75em; }
.local-icon-sm { font-size: 0.875em; }
.local-icon-lg { font-size: 1.25em; }
.local-icon-xl { font-size: 1.5em; }
.local-icon-2x { font-size: 2em; }
.local-icon-3x { font-size: 3em; }

/* 图标动画效果 */
.local-icon-spin {
    animation: local-icon-spin 2s infinite linear;
}

.local-icon-pulse {
    animation: local-icon-pulse 2s infinite;
}

.local-icon-bounce {
    animation: local-icon-bounce 1s infinite;
}

.local-icon-shake {
    animation: local-icon-shake 0.82s cubic-bezier(.36,.07,.19,.97) both;
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* 动画关键帧 */
@keyframes local-icon-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes local-icon-pulse {
    0%, 100% { 
        opacity: 1; 
        transform: scale(1);
    }
    50% { 
        opacity: 0.5; 
        transform: scale(1.1);
    }
}

@keyframes local-icon-bounce {
    0%, 20%, 53%, 80%, 100% {
        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
        transform: translate3d(0,0,0);
    }
    40%, 43% {
        animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
        transform: translate3d(0, -30px, 0);
    }
    70% {
        animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0,-4px,0);
    }
}

@keyframes local-icon-shake {
    10%, 90% {
        transform: translate3d(-1px, 0, 0);
    }
    20%, 80% {
        transform: translate3d(2px, 0, 0);
    }
    30%, 50%, 70% {
        transform: translate3d(-4px, 0, 0);
    }
    40%, 60% {
        transform: translate3d(4px, 0, 0);
    }
}

/* 图标颜色变体 */
.local-icon-primary { color: #6366f1; }
.local-icon-secondary { color: #06b6d4; }
.local-icon-success { color: #10b981; }
.local-icon-warning { color: #f59e0b; }
.local-icon-danger { color: #ef4444; }
.local-icon-info { color: #3b82f6; }
.local-icon-light { color: #f8fafc; }
.local-icon-dark { color: #1f2937; }
.local-icon-white { color: #ffffff; }

/* 悬停效果 */
.local-icon-hover:hover {
    transform: scale(1.1);
    transition: transform 0.2s ease;
}

.local-icon-glow:hover {
    filter: drop-shadow(0 0 8px currentColor);
    transition: filter 0.3s ease;
}

/* 响应式图标 */
@media (max-width: 768px) {
    .local-icon-responsive {
        font-size: 0.9em;
    }
}

@media (max-width: 480px) {
    .local-icon-responsive {
        font-size: 0.8em;
    }
}

/* 图标组合样式 */
.local-icon-stack {
    position: relative;
    display: inline-block;
    width: 2em;
    height: 2em;
    line-height: 2em;
    vertical-align: middle;
}

.local-icon-stack .local-icon {
    position: absolute;
    left: 0;
    width: 100%;
    text-align: center;
}

.local-icon-stack .local-icon-1x {
    line-height: inherit;
}

.local-icon-stack .local-icon-2x {
    font-size: 2em;
}

/* 图标边框 */
.local-icon-border {
    border: 0.08em solid;
    border-radius: 0.1em;
    padding: 0.2em 0.25em 0.15em;
}

/* 图标旋转 */
.local-icon-rotate-90 { transform: rotate(90deg); }
.local-icon-rotate-180 { transform: rotate(180deg); }
.local-icon-rotate-270 { transform: rotate(270deg); }

/* 图标翻转 */
.local-icon-flip-horizontal { transform: scaleX(-1); }
.local-icon-flip-vertical { transform: scaleY(-1); }
.local-icon-flip-both { transform: scale(-1); }

/* 固定宽度图标 */
.local-icon-fw {
    width: 1.25em;
    text-align: center;
}

/* 列表图标 */
.local-icon-ul {
    list-style-type: none;
    margin-left: 2.5em;
    padding-left: 0;
}

.local-icon-ul > li {
    position: relative;
}

.local-icon-li {
    position: absolute;
    left: -2em;
    width: 2em;
    top: 0.14em;
    text-align: center;
}

/* 图标按钮样式 */
.local-icon-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5em;
    border: none;
    background: transparent;
    cursor: pointer;
    border-radius: 0.25em;
    transition: all 0.2s ease;
}

.local-icon-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
}

.local-icon-btn:active {
    transform: translateY(0);
}

/* 图标徽章 */
.local-icon-badge {
    position: relative;
}

.local-icon-badge::after {
    content: '';
    position: absolute;
    top: -0.25em;
    right: -0.25em;
    width: 0.5em;
    height: 0.5em;
    background-color: #ef4444;
    border-radius: 50%;
    border: 2px solid #ffffff;
}

/* 图标加载状态 */
.local-icon-loading {
    opacity: 0.6;
    pointer-events: none;
}

/* 图标禁用状态 */
.local-icon-disabled {
    opacity: 0.4;
    cursor: not-allowed;
}

/* 图标组 */
.local-icon-group {
    display: inline-flex;
    align-items: center;
    gap: 0.5em;
}

.local-icon-group .local-icon {
    flex-shrink: 0;
}

/* 图标文本组合 */
.local-icon-text {
    display: inline-flex;
    align-items: center;
    gap: 0.5em;
}

.local-icon-text .local-icon {
    flex-shrink: 0;
}

/* 圆形图标背景 */
.local-icon-circle {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2em;
    height: 2em;
    border-radius: 50%;
    background-color: currentColor;
    color: white;
}

.local-icon-circle .local-icon {
    color: inherit;
}

/* 方形图标背景 */
.local-icon-square {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2em;
    height: 2em;
    border-radius: 0.25em;
    background-color: currentColor;
    color: white;
}

.local-icon-square .local-icon {
    color: inherit;
}
