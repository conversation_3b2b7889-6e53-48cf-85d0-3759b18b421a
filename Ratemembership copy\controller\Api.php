<?php
namespace plugin\Ratemembership\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;

class Api extends BasePlugin
{
    // 设置场景权限，支持管理员和商户
    protected $scene = ['admin'];  
    
    // 这些方法不需要登录检测，让商户可以直接访问
    protected $noNeedLogin = ['merchant', 'merchantChannelGroups', 'getMerchantData', 'upgradeLevel'];  

    // 管理端首页 - 只允许管理员访问
    public function index()
    {
        return View::fetch();
    }

    // 管理端获取渠道组列表
    public function adminChannelGroups()
    {
        try {
            // 获取渠道组基本信息
            $groups = Db::name('channel_group')
                ->alias('g')
                ->whereNull('g.delete_time')
                ->field([
                    'g.id',
                    'g.name'
                ])
                ->select()
                ->toArray();

            // 获取平台费率和商户费率
            foreach ($groups as &$group) {
                // 获取平台基础费率
                $platformRate = Db::name('channel_group_rule')
                    ->where([
                        'group_id' => $group['id'],
                        'channel_id' => 1  // 平台渠道
                    ])
                    ->value('rate');

                // 获取直清合规渠道费率
                $directRate = Db::name('channel_group_rule')
                    ->where([
                        'group_id' => $group['id'],
                        'channel_id' => 10  // 直清合规渠道
                    ])
                    ->value('rate');

                // 获取商户自定义渠道费率
                $merchantRate = Db::name('channel_group_rule')
                    ->where([
                        'group_id' => $group['id'],
                        'channel_id' => 14  // 商户自定义渠道
                    ])
                    ->value('rate');

                $group['platform_rate'] = floatval($platformRate ?? 6.00);
                $group['direct_rate'] = floatval($directRate ?? 6.00);
                $group['merchant_rate'] = floatval($merchantRate ?? 6.00);
            }

            // 获取流水配置
            $turnovers = plugconf('Ratemembership.turnovers') ? json_decode(plugconf('Ratemembership.turnovers'), true) : [];
            // 获取价格配置
            $prices = plugconf('Ratemembership.prices') ? json_decode(plugconf('Ratemembership.prices'), true) : [];
            // 获取季费和年费配置
            $quarterlyPrices = plugconf('Ratemembership.quarterly_prices') ? json_decode(plugconf('Ratemembership.quarterly_prices'), true) : [];
            $yearlyPrices = plugconf('Ratemembership.yearly_prices') ? json_decode(plugconf('Ratemembership.yearly_prices'), true) : [];
            
            foreach ($groups as &$group) {
                $group['turnover'] = $turnovers[$group['id']] ?? 0;
                $group['price'] = $prices[$group['id']] ?? 299.00;  // 默认299元
                $group['quarterly_price'] = $quarterlyPrices[$group['id']] ?? 0;  // 添加季费
                $group['yearly_price'] = $yearlyPrices[$group['id']] ?? 0;  // 添加年费
                $group['can_upgrade'] = $group['id'] == min(array_keys($turnovers)) ? 0 : 
                    (intval(plugconf("Ratemembership.can_upgrade.{$group['id']}") ?? 1));
                $group['disabled_message'] = plugconf("Ratemembership.disabled_message.{$group['id']}") ?? '该等级暂不开放';
            }

            return json(['code' => 200, 'msg' => '获取成功', 'data' => $groups]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '获取失败: ' . $e->getMessage()]);
        }
    }

    
    // 添加渠道组
    public function addChannelGroup()
    {
        Db::startTrans();
        try {
            $name = $this->request->post('name', '');
            $platformRate = $this->request->post('platform_rate', 6.00);
            $merchantRate = $this->request->post('merchant_rate', 6.00);

            // 插入渠道组 - 移除 create_time 字段
            $groupId = Db::name('channel_group')->insertGetId([
                'name' => $name
            ]);

            // 插入平台渠道费率规则 - 移除 create_time 字段
            $platformChannels = [1, 2, 3, 4, 5, 6, 7, 8, 9, 15];
            foreach ($platformChannels as $channelId) {
                Db::name('channel_group_rule')->insert([
                    'group_id' => $groupId,
                    'channel_id' => $channelId,
                    'rate' => $platformRate,
                    'status' => 1
                ]);
            }

            // 插入商户渠道费率规则 - 移除 create_time 字段
            $merchantChannels = [14, 12, 13, 16, 17, 18, 19, 20];
            foreach ($merchantChannels as $channelId) {
                Db::name('channel_group_rule')->insert([
                    'group_id' => $groupId,
                    'channel_id' => $channelId,
                    'rate' => $merchantRate,
                    'status' => 1
                ]);
            }

            // 获取并更新流水配置
            $turnovers = plugconf('Ratemembership.turnovers') ? json_decode(plugconf('Ratemembership.turnovers'), true) : [];
            $turnovers[$groupId] = 0;  // 默认流水要求为0
            plugconf('Ratemembership.turnovers', json_encode($turnovers));

            // 获取并更新价格配置
            $prices = plugconf('Ratemembership.prices') ? json_decode(plugconf('Ratemembership.prices'), true) : [];
            $prices[$groupId] = 299.00;  // 默认价格299元
            plugconf('Ratemembership.prices', json_encode($prices));

            Db::commit();
            return json(['code' => 200, 'msg' => '添加成功']);
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 500, 'msg' => '添加失败: ' . $e->getMessage()]);
        }
    }

    // 编辑渠道组
    public function editChannelGroup()
    {
        try {
            $data = $this->request->post();
            
            if (empty($data['id'])) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }

            // 检查渠道组是否存在
            $group = Db::name('channel_group')->where('id', $data['id'])->find();
            if (!$group) {
                return json(['code' => 400, 'msg' => '渠道组不存在']);
            }

            Db::startTrans();
            try {
                // 更新基本信息
                $groupData = [
                    'name' => $data['name']
                ];
                
                Db::name('channel_group')->where('id', $data['id'])->update($groupData);
                
                // 清除旧的费率规则
                Db::name('channel_group_rule')->where('group_id', $data['id'])->delete();
                
                if (!empty($data['use_custom_rates'])) {
                    // 使用自定义费率
                    // 保存平台渠道费率
                    foreach ($data['platform_rates'] as $rate) {
                        Db::name('channel_group_rule')->insert([
                            'group_id' => $data['id'],
                            'channel_id' => $rate['channel_id'],
                            'rate' => $rate['rate']
                        ]);
                    }
                    
                    // 保存商户渠道费率
                    foreach ($data['merchant_rates'] as $rate) {
                        Db::name('channel_group_rule')->insert([
                            'group_id' => $data['id'],
                            'channel_id' => $rate['channel_id'],
                            'rate' => $rate['rate']
                        ]);
                    }
                } else {
                    // 使用统一费率
                    // 保存平台渠道费率（除直付通外）
                    foreach ([1, 2, 3, 4, 5, 6, 7, 8, 9, 15] as $channelId) {
                        Db::name('channel_group_rule')->insert([
                            'group_id' => $data['id'],
                            'channel_id' => $channelId,
                            'rate' => $data['unified_platform_rate']
                        ]);
                    }

                    // 保存直付通渠道费率
                    foreach ([10, 11] as $channelId) {
                        Db::name('channel_group_rule')->insert([
                            'group_id' => $data['id'],
                            'channel_id' => $channelId,
                            'rate' => $data['unified_zft_rate'] ?? $data['unified_platform_rate']
                        ]);
                    }
                    
                    // 保存商户渠道费率
                    foreach ([14, 12, 13, 16, 17, 18, 19, 20] as $channelId) {
                        Db::name('channel_group_rule')->insert([
                            'group_id' => $data['id'],
                            'channel_id' => $channelId,
                            'rate' => $data['unified_merchant_rate']
                        ]);
                    }
                }
                
                // 保存其他配置
                $turnovers = plugconf('Ratemembership.turnovers') ? json_decode(plugconf('Ratemembership.turnovers'), true) : [];
                $prices = plugconf('Ratemembership.prices') ? json_decode(plugconf('Ratemembership.prices'), true) : [];
                $quarterlyPrices = plugconf('Ratemembership.quarterly_prices') ? json_decode(plugconf('Ratemembership.quarterly_prices'), true) : [];
                $yearlyPrices = plugconf('Ratemembership.yearly_prices') ? json_decode(plugconf('Ratemembership.yearly_prices'), true) : [];
                
                $turnovers[$data['id']] = floatval($data['turnover']);
                $prices[$data['id']] = floatval($data['price']);
                $quarterlyPrices[$data['id']] = floatval($data['quarterly_price'] ?? 0);
                $yearlyPrices[$data['id']] = floatval($data['yearly_price'] ?? 0);
                
                plugconf('Ratemembership.turnovers', json_encode($turnovers));
                plugconf('Ratemembership.prices', json_encode($prices));
                plugconf('Ratemembership.quarterly_prices', json_encode($quarterlyPrices));
                plugconf('Ratemembership.yearly_prices', json_encode($yearlyPrices));
                plugconf("Ratemembership.can_upgrade.{$data['id']}", $data['can_upgrade'] ? '1' : '0');
                plugconf("Ratemembership.disabled_message.{$data['id']}", $data['disabled_message']);

                Db::commit();
                return json(['code' => 200, 'msg' => '更新成功']);
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '更新失败：' . $e->getMessage()]);
        }
    }

    // 删除渠道组
    public function deleteChannelGroup()
    {
        try {
            $id = intval($this->request->post('id'));
            if (!$id) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }

            // 检查是否有用户使用该等级
            $userCount = Db::name('user')->where('channel_group_id', $id)->count();
            if ($userCount > 0) {
                return json(['code' => 400, 'msg' => '该等级下还有用户，无法删除']);
            }

            // 获取最小ID（默认等级）
            $minId = Db::name('channel_group')->min('id');
            if ($id == $minId) {
                return json(['code' => 400, 'msg' => '默认等级不能删除']);
            }

            Db::startTrans();
            try {
                // 删除渠道组
                Db::name('channel_group')->where('id', $id)->delete();
                // 删除费率规则
                Db::name('channel_group_rule')->where('group_id', $id)->delete();
                
                // 删除相关配置但保留其他等级的配置
                $configKeys = [
                    'Ratemembership.turnovers',
                    'Ratemembership.prices',
                    'Ratemembership.quarterly_prices',
                    'Ratemembership.yearly_prices',
                    'Ratemembership.can_upgrade',
                    'Ratemembership.disabled_message'
                ];

                foreach ($configKeys as $key) {
                    $config = plugconf($key);
                    if ($config) {
                        $data = json_decode($config, true);
                        if (isset($data[$id])) {
                            unset($data[$id]);
                            plugconf($key, json_encode($data));
                        }
                    }
                }

                Db::commit();
                return json(['code' => 200, 'msg' => '删除成功']);
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }

    // 获取时间设置
    public function getSettings()
    {
        try {
            $settings = [
                'update_interval' => intval(plugconf('Ratemembership.update_interval') ?? 3600),
                'auto_update_status' => intval(plugconf('Ratemembership.auto_update_status') ?? 0),
                'update_cycle' => plugconf('Ratemembership.update_cycle') ?? 'monthly',
                'calc_mode' => plugconf('Ratemembership.calc_mode') ?? 'realtime'  // 添加计算模式
            ];
            
            return json(['code' => 200, 'msg' => '获取成功', 'data' => $settings]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    // 保存时间设置
    public function saveSettings()
    {
        try {
            $updateInterval = intval($this->request->post('update_interval', 3600));
            $autoUpdateStatus = intval($this->request->post('auto_update_status', 0));
            $updateCycle = $this->request->post('update_cycle', 'monthly');
            $calcMode = $this->request->post('calc_mode', 'realtime');  // 添加计算模式

            // 验证参数
            if ($updateInterval < 60 || $updateInterval > 86400) {
                return json(['code' => 400, 'msg' => '检查间隔必须在1分钟到24小时之间']);
            }

            if (!in_array($updateCycle, ['monthly', 'quarterly', 'yearly'])) {
                return json(['code' => 400, 'msg' => '无效的更新周期']);
            }

            if (!in_array($calcMode, ['realtime', 'period'])) {
                return json(['code' => 400, 'msg' => '无效的计算模式']);
            }

            // 保存设置
            plugconf('Ratemembership.update_interval', strval($updateInterval));
            plugconf('Ratemembership.auto_update_status', strval($autoUpdateStatus));
            plugconf('Ratemembership.update_cycle', $updateCycle);
            plugconf('Ratemembership.calc_mode', $calcMode);  // 保存计算模式

            return json(['code' => 200, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    /**
     * 手动计算所有商户的等级
     */
    public function handleManualCalc()
    {
        try {
            // 获取所有商户
            $merchants = Db::name('user')
                ->field(['id', 'channel_group_id'])
                ->select()
                ->toArray();

            // 获取更新周期设置
            $updateCycle = plugconf('Ratemembership.update_cycle') ?? 'monthly';
            $updateCount = 0;
            $errors = [];

            foreach ($merchants as $merchant) {
                try {
                    // 计算统计周期的开始和结束时间
                    $endTime = time();
                    switch ($updateCycle) {
                        case 'monthly':
                            $startTime = strtotime('-30 days');
                            break;
                        case 'quarterly':
                            $startTime = strtotime('-90 days');
                            break;
                        case 'yearly':
                            $startTime = strtotime('-365 days');
                            break;
                        default:
                            $startTime = strtotime('-30 days');
                    }

                    // 计算商户流水 - 使用 total_amount 字段
                    $amount = Db::name('order')
                        ->where([
                            'user_id' => $merchant['id'],
                            'status' => 1, // 成功的订单
                        ])
                        ->whereTime('create_time', 'between', [$startTime, $endTime])
                        ->sum('total_amount'); // 使用 total_amount 字段

                    if ($amount === null) {
                        $amount = 0;
                    }

                    // 调试输出
                    trace("商户ID: {$merchant['id']}, 流水: {$amount}, 开始时间: " . date('Y-m-d H:i:s', $startTime) . ", 结束时间: " . date('Y-m-d H:i:s', $endTime));

                    // 获取流水阈值配置
                    $turnovers = plugconf('Ratemembership.turnovers');
                    $turnovers = $turnovers ? json_decode($turnovers, true) : [];
                    
                    if (empty($turnovers)) {
                        continue;
                    }

                    // 修改为相同的降级逻辑
                    $targetGroupId = min(array_keys($turnovers)); // 默认最低等级
                    asort($turnovers);
                    
                    foreach ($turnovers as $groupId => $threshold) {
                        $canUpgrade = intval(plugconf("Ratemembership.can_upgrade.{$groupId}") ?? 1);
                        if (!$canUpgrade) {
                            continue;
                        }
                        
                        if ($amount >= floatval($threshold)) {
                            $targetGroupId = $groupId;
                            break;
                        }
                    }

                    // 如果等级不同则更新
                    if ($targetGroupId != $merchant['channel_group_id']) {
                        Db::startTrans();
                        try {
                            // 更新商户等级
                            Db::name('user')
                                ->where('id', $merchant['id'])
                                ->update([
                                    'channel_group_id' => $targetGroupId,
                                    'update_time' => time()
                                ]);

                            // 设置到期时间
                            $expireTime = time();
                            switch ($updateCycle) {
                                case 'monthly':
                                    $expireTime += 30 * 24 * 3600;
                                    break;
                                case 'quarterly':
                                    $expireTime += 90 * 24 * 3600;
                                    break;
                                case 'yearly':
                                    $expireTime += 365 * 24 * 3600;
                                    break;
                                default:
                                    $expireTime += 30 * 24 * 3600;
                            }

                            $expireTimes = plugconf('Ratemembership.expire_times') ? json_decode(plugconf('Ratemembership.expire_times'), true) : [];
                            $expireTimes[$merchant['id']] = $expireTime;
                            plugconf('Ratemembership.expire_times', json_encode($expireTimes));

                            Db::commit();
                            $updateCount++;
                        } catch (\Exception $e) {
                            Db::rollback();
                            $errors[] = "商户ID {$merchant['id']} 更新失败: " . $e->getMessage();
                        }
                    }
                } catch (\Exception $e) {
                    $errors[] = "商户ID {$merchant['id']} 处理失败: " . $e->getMessage();
                }
            }

            // 更新最后执行时间
            plugconf('Ratemembership.last_update', strval(time()));

            $message = "成功更新 {$updateCount} 个商户等级";
            if (!empty($errors)) {
                $message .= "，但有以下错误：\n" . implode("\n", $errors);
            }

            return json(['code' => 200, 'msg' => $message]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '手动计算失败：' . $e->getMessage()]);
        }
    }

    // 添加新方法：开通等级
    public function upgradeLevel()
    {
        if (!$this->user) {
            return json(['code' => 403, 'msg' => '余额不足']);
        }

        try {
            $groupId = input('group_id/d', 0);
            $isRenew = input('is_renew/b', false);
            $cycle = input('cycle/s', 'monthly');
            $price = input('price/f', 0);

            // 验证价格
            if ($price <= 0) {
                throw new \Exception('价格无效');
            }

            // 检查余额是否足够
            $userMoney = Db::name('user')
                ->where('id', $this->user->id)
                ->value('operate_money');
            
            if (floatval($userMoney) < floatval($price)) {
                throw new \Exception('余额不足');
            }

            Db::startTrans();
            try {
                // 扣除用户余额
                $result = Db::name('user')
                    ->where('id', $this->user->id)
                    ->dec('operate_money', $price)
                    ->update();
                
                if (!$result) {
                    throw new \Exception('扣款失败');
                }

                // 记录资金变动到用户资金日志表
                Db::name('user_money_log')->insert([
                    'user_id' => $this->user->id,
                    'change' => -$price,
                    'reason' => ($isRenew ? '续费' : '开通') . '会员等级',
                    'create_time' => time(),
                    'source' => 'Platform'
                ]);

                // 计算到期时间
                $currentTime = time();
                $expireTime = $currentTime;
                switch ($cycle) {
                    case 'yearly':
                        $expireTime = $currentTime + (365 * 24 * 3600); // 一年
                        break;
                    case 'quarterly':
                        $expireTime = $currentTime + (90 * 24 * 3600);  // 90天
                        break;
                    default:
                        $expireTime = $currentTime + (30 * 24 * 3600);  // 30天
                }

                // 如果是续费，需要在原到期时间基础上增加时间
                if ($isRenew) {
                    $expireTimes = plugconf('Ratemembership.expire_times') ? 
                        json_decode(plugconf('Ratemembership.expire_times'), true) : [];
                    $oldExpireTime = $expireTimes[$this->user->id] ?? $currentTime;
                    if ($oldExpireTime > $currentTime) {
                        // 如果还未到期，在原到期时间基础上增加
                        $expireTime = $oldExpireTime;
                        switch ($cycle) {
                            case 'yearly':
                                $expireTime += 365 * 24 * 3600;
                                break;
                            case 'quarterly':
                                $expireTime += 90 * 24 * 3600;
                                break;
                            default:
                                $expireTime += 30 * 24 * 3600;
                        }
                    }
                }

                // 更新用户等级
                Db::name('user')
                    ->where('id', $this->user->id)
                    ->update([
                        'channel_group_id' => $groupId,
                        'update_time' => time()
                    ]);

                // 保存到期时间
                $expireTimes = plugconf('Ratemembership.expire_times') ? 
                    json_decode(plugconf('Ratemembership.expire_times'), true) : [];
                $expireTimes[$this->user->id] = $expireTime;
                plugconf('Ratemembership.expire_times', json_encode($expireTimes));

                Db::commit();
                return json(['code' => 200, 'msg' => ($isRenew ? '续费' : '开通') . '成功']);
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => ($isRenew ? '续费' : '开通') . '失败: ' . $e->getMessage()]);
        }
    }

    private function getCycleName($cycle)
    {
        $cycleNames = [
            'monthly' => '包月',
            'quarterly' => '包季',
            'yearly' => '包年'
        ];
        return $cycleNames[$cycle] ?? '包月';
    }

    // 获取会员列表
    public function getMemberList()
    {
        try {
            $page = $this->request->post('page/d', 1);
            $limit = $this->request->post('limit/d', 10);

            // 获取总记录数
            $total = Db::name('user')
                ->where('channel_group_id', '>', 0)
                ->count();

            // 获取分页数据
            $users = Db::name('user')
                ->alias('u')
                ->join('channel_group g', 'u.channel_group_id = g.id', 'left')
                ->where('u.channel_group_id', '>', 0)
                ->field([
                    'u.id',
                    'u.username',
                    'g.name as group_name',
                    'u.channel_group_id'
                ])
                ->page($page, $limit)
                ->select()
                ->toArray();

            // 获取到期时间配置
            $expireTimes = plugconf('Ratemembership.expire_times') ? json_decode(plugconf('Ratemembership.expire_times'), true) : [];

            // 添加到期时间
            foreach ($users as &$user) {
                $user['expire_time'] = $expireTimes[$user['id']] ?? 0;
            }

            return json(['code' => 200, 'msg' => '获取成功', 'data' => [
                'list' => $users,
                'total' => $total
            ]]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '获取失败: ' . $e->getMessage()]);
        }
    }

    // 更新会员到期时间
    public function updateExpireTime()
    {
        try {
            $userId = $this->request->post('user_id/d', 0);
            $expireTime = $this->request->post('expire_time/d', 0);

            if (empty($userId)) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }

            // 检查用户是否存在且不是默认等级
            $user = Db::name('user')
                ->where('id', $userId)
                ->where('channel_group_id', '>', 0)
                ->find();
            
            if (!$user) {
                return json(['code' => 400, 'msg' => '用户不存在或为默认等级']);
            }

            // 获取并更新到期时间配置
            $expireTimes = plugconf('Ratemembership.expire_times') ? json_decode(plugconf('Ratemembership.expire_times'), true) : [];
            
            if ($expireTime > 0) {
                $expireTimes[$userId] = $expireTime;
            } else {
                // 如果设置为0，则删除该用户的到期时间记录（永久有效）
                unset($expireTimes[$userId]);
            }
            
            plugconf('Ratemembership.expire_times', json_encode($expireTimes));

            return json(['code' => 200, 'msg' => '更新成功']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '更新失败: ' . $e->getMessage()]);
        }
    }

    // 修改会员等级方法
    public function updateMemberLevel()
    {
        try {
            $userId = $this->request->post('user_id/d', 0);
            $groupId = $this->request->post('group_id/d', 0);

            if (empty($userId) || empty($groupId)) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }

            // 更新用户等级
            Db::name('user')
                ->where('id', $userId)
                ->update([
                    'channel_group_id' => $groupId,
                    'update_time' => time()
                ]);

            return json(['code' => 200, 'msg' => '更新成功']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '更新失败: ' . $e->getMessage()]);
        }
    }

    // 添加获取渠道列表的方法
    public function getChannels()
    {

        try {
            // 获取平台渠道
            $platformChannels = Db::name('channel')
                ->where('id', 'in', [1, 2, 3, 4, 5, 6, 7, 8, 9, 15])
                ->field(['id', 'name'])
                ->select();
            
            // 获取商户渠道
            $merchantChannels = Db::name('channel')
                ->where('id', 'in', [14, 12, 13, 16, 17, 18, 19, 20])
                ->field(['id', 'name'])
                ->select();
            
            return json([
                'code' => 200, 
                'msg' => '获取成功', 
                'data' => [
                    'platform_channels' => $platformChannels,
                    'merchant_channels' => $merchantChannels
                ]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    // 修改保存等级信息的方法
    public function saveChannelGroup()
    {

        try {
            $data = $this->request->post();
            
            if (empty($data['name'])) {
                return json(['code' => 400, 'msg' => '等级名称不能为空']);
            }
            
            Db::startTrans();
            try {
                // 保存基本信息
                $groupId = $data['id'] ?? null;
                $groupData = [
                    'name' => $data['name']
                ];
                
                if ($groupId) {
                    // 更新现有等级
                    Db::name('channel_group')->where('id', $groupId)->update($groupData);
                } else {
                    // 添加新等级
                    $groupId = Db::name('channel_group')->insertGetId($groupData);
                }
                
                // 清除旧的费率规则
                Db::name('channel_group_rule')->where('group_id', $groupId)->delete();
                
                if (!empty($data['use_custom_rates'])) {
                    // 使用自定义费率
                    if (!empty($data['platform_rates'])) {
                        foreach ($data['platform_rates'] as $rate) {
                            if (!empty($rate['channel_id']) && isset($rate['rate'])) {
                                Db::name('channel_group_rule')->insert([
                                    'group_id' => $groupId,
                                    'channel_id' => $rate['channel_id'],
                                    'rate' => floatval($rate['rate'])
                                ]);
                            }
                        }
                    }
                    
                    if (!empty($data['merchant_rates'])) {
                        foreach ($data['merchant_rates'] as $rate) {
                            if (!empty($rate['channel_id']) && isset($rate['rate'])) {
                                Db::name('channel_group_rule')->insert([
                                    'group_id' => $groupId,
                                    'channel_id' => $rate['channel_id'],
                                    'rate' => floatval($rate['rate'])
                                ]);
                            }
                        }
                    }
                } else {
                    // 使用统一费率
                    // 平台渠道费率（除直付通外）
                    foreach ([1, 2, 3, 4, 5, 6, 7, 8, 9, 15] as $channelId) {
                        Db::name('channel_group_rule')->insert([
                            'group_id' => $groupId,
                            'channel_id' => $channelId,
                            'rate' => floatval($data['unified_platform_rate'])
                        ]);
                    }

                    // 直付通渠道费率
                    foreach ([10, 11] as $channelId) {
                        Db::name('channel_group_rule')->insert([
                            'group_id' => $groupId,
                            'channel_id' => $channelId,
                            'rate' => floatval($data['unified_zft_rate'] ?? $data['unified_platform_rate'])
                        ]);
                    }
                    
                    // 商户渠道费率
                    foreach ([14, 12, 13, 16, 17, 18, 19, 20] as $channelId) {
                        Db::name('channel_group_rule')->insert([
                            'group_id' => $groupId,
                            'channel_id' => $channelId,
                            'rate' => floatval($data['unified_merchant_rate'])
                        ]);
                    }
                }
                
                // 保存其他配置
                $turnovers = plugconf('Ratemembership.turnovers') ? json_decode(plugconf('Ratemembership.turnovers'), true) : [];
                $prices = plugconf('Ratemembership.prices') ? json_decode(plugconf('Ratemembership.prices'), true) : [];
                $quarterlyPrices = plugconf('Ratemembership.quarterly_prices') ? json_decode(plugconf('Ratemembership.quarterly_prices'), true) : [];
                $yearlyPrices = plugconf('Ratemembership.yearly_prices') ? json_decode(plugconf('Ratemembership.yearly_prices'), true) : [];
                
                $turnovers[$groupId] = floatval($data['turnover'] ?? 0);
                $prices[$groupId] = floatval($data['price'] ?? 299.00);
                $quarterlyPrices[$groupId] = floatval($data['quarterly_price'] ?? 0);
                $yearlyPrices[$groupId] = floatval($data['yearly_price'] ?? 0);
                
                plugconf('Ratemembership.turnovers', json_encode($turnovers));
                plugconf('Ratemembership.prices', json_encode($prices));
                plugconf('Ratemembership.quarterly_prices', json_encode($quarterlyPrices));
                plugconf('Ratemembership.yearly_prices', json_encode($yearlyPrices));
                plugconf("Ratemembership.can_upgrade.{$groupId}", $data['can_upgrade'] ? '1' : '0');
                plugconf("Ratemembership.disabled_message.{$groupId}", $data['disabled_message'] ?? '该等级暂不开放');
                
                Db::commit();
                return json(['code' => 200, 'msg' => '保存成功']);
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    // 清空会员列表
    public function clearMembers()
    {

        
        try {
            Db::startTrans();
            try {
                // 获取最低等级（默认等级）ID
                $turnovers = plugconf('Ratemembership.turnovers') ? json_decode(plugconf('Ratemembership.turnovers'), true) : [];
                $defaultGroupId = !empty($turnovers) ? min(array_keys($turnovers)) : null;
                
                if (!$defaultGroupId) {
                    throw new \Exception('未找到默认等级');
                }
                
                // 更新所有非默认等级用户为默认等级
                Db::name('user')
                    ->where('channel_group_id', '>', 0)
                    ->where('channel_group_id', '<>', $defaultGroupId)
                    ->update([
                        'channel_group_id' => $defaultGroupId,
                        'update_time' => time()
                    ]);
                
                // 清空到期时间配置
                plugconf('Ratemembership.expire_times', '{}');
                
                Db::commit();
                return json(['code' => 200, 'msg' => '清空成功']);
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '清空失败：' . $e->getMessage()]);
        }
    }

    // 删除商户
    public function deleteMerchant()
    {
        try {
            $userId = $this->request->post('user_id/d', 0);
            if (!$userId) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }

            // 检查用户是否存在
            $user = Db::name('user')
                ->where('id', $userId)
                ->where('channel_group_id', '>', 0)
                ->find();
            
            if (!$user) {
                return json(['code' => 400, 'msg' => '用户不存在或不是会员']);
            }

            // 获取最低等级（默认等级）ID
            $turnovers = plugconf('Ratemembership.turnovers') ? json_decode(plugconf('Ratemembership.turnovers'), true) : [];
            $defaultGroupId = !empty($turnovers) ? min(array_keys($turnovers)) : null;
            
            if (!$defaultGroupId) {
                throw new \Exception('未找到默认等级');
            }

            Db::startTrans();
            try {
                // 重置用户等级为默认等级
                Db::name('user')
                    ->where('id', $userId)
                    ->update([
                        'channel_group_id' => $defaultGroupId,
                        'update_time' => time()
                    ]);

                // 删除到期时间配置
                $expireTimes = plugconf('Ratemembership.expire_times') ? json_decode(plugconf('Ratemembership.expire_times'), true) : [];
                if (isset($expireTimes[$userId])) {
                    unset($expireTimes[$userId]);
                    plugconf('Ratemembership.expire_times', json_encode($expireTimes));
                }

                Db::commit();
                return json(['code' => 200, 'msg' => '删除成功']);
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }
}
