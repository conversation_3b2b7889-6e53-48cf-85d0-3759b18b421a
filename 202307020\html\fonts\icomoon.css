@font-face {
  font-family: 'icomoon';
  src:  url('fonts/icomoon.eot?j9p4q0');
  src:  url('fonts/icomoon.eot?j9p4q0#iefix') format('embedded-opentype'),
    url('fonts/icomoon.ttf?j9p4q0') format('truetype'),
    url('fonts/icomoon.woff?j9p4q0') format('woff'),
    url('fonts/icomoon.svg?j9p4q0#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-facebook:before {
  content: "\ea90";
}
.icon-twitter:before {
  content: "\ea96";
}
.icon-youtube:before {
  content: "\ea9d";
}
.icon-linkedin:before {
  content: "\eaca";
}
.icon-bars:before {
  content: "\f0c9";
}
.icon-instagram:before {
  content: "\f16d";
}
