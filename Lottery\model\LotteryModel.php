<?php

namespace plugin\Lottery\model;

use think\facade\Db;
use think\facade\Log;

class LotteryModel {

    /**
     * 获取配置
     */
    public static function getConfig() {
        try {
            $config = Db::name('plugin_lottery_config')->select()->toArray();

            // 设置默认值
            $defaults = [
                'status' => 1,
                'daily_limit' => 3,
                'start_hour' => '00:00',
                'end_hour' => '23:59',
                'show_probability' => 0,
                'commission_draw_enabled' => 0,
                'commission_draw_amount' => 1.00,
                'commission_draw_max' => 10
            ];

            $configData = $defaults;

            // 覆盖数据库中的配置并进行类型转换
            foreach ($config as $item) {
                $key = $item['config_key'];
                $value = $item['config_value'];

                // 根据配置键进行类型转换
                switch ($key) {
                    case 'status':
                    case 'daily_limit':
                    case 'show_probability':
                    case 'commission_draw_enabled':
                    case 'commission_draw_max':
                        $configData[$key] = intval($value);
                        break;
                    case 'commission_draw_amount':
                        $configData[$key] = floatval($value);
                        break;
                    case 'start_hour':
                    case 'end_hour':
                        $configData[$key] = $value;
                        break;
                    default:
                        $configData[$key] = $value;
                        break;
                }
            }

            return $configData;
        } catch (\Exception $e) {
            Log::error('获取配置失败：' . $e->getMessage());
            return [
                'status' => 1,
                'daily_limit' => 3,
                'start_hour' => '00:00',
                'end_hour' => '23:59',
                'show_probability' => 0
            ];
        }
    }

    /**
     * 获取商户限制
     */
    public static function getMerchantLimit($merchantId) {
        try {
            $today = date('Y-m-d');
            $limit = Db::name('plugin_lottery_merchant_limits')
                ->where('merchant_id', $merchantId)
                ->find();
            
            if (!$limit || $limit['last_reset_date'] !== $today) {
                // 获取配置中的每日限制
                $config = self::getConfig();
                $dailyLimit = intval($config['daily_limit'] ?? 3);
                
                // 创建或更新限制记录
                $data = [
                    'merchant_id' => $merchantId,
                    'daily_limit' => $dailyLimit,
                    'used_count' => 0,
                    'last_reset_date' => $today,
                    'update_time' => date('Y-m-d H:i:s')
                ];
                
                if ($limit) {
                    Db::name('plugin_lottery_merchant_limits')
                        ->where('merchant_id', $merchantId)
                        ->update($data);
                } else {
                    $data['create_time'] = date('Y-m-d H:i:s');
                    Db::name('plugin_lottery_merchant_limits')->insert($data);
                }
                
                return $data;
            }
            
            return $limit;
        } catch (\Exception $e) {
            Log::error('获取商户限制失败：' . $e->getMessage());
            return [
                'merchant_id' => $merchantId,
                'daily_limit' => 3,
                'used_count' => 0,
                'last_reset_date' => date('Y-m-d')
            ];
        }
    }

    /**
     * 更新商户已使用次数
     */
    public static function updateMerchantUsedCount($merchantId) {
        try {
            $today = date('Y-m-d');
            Db::name('plugin_lottery_merchant_limits')
                ->where('merchant_id', $merchantId)
                ->where('last_reset_date', $today)
                ->inc('used_count')
                ->update();
            return true;
        } catch (\Exception $e) {
            Log::error('更新商户使用次数失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取可用奖品
     */
    public static function getAvailablePrizes() {
        try {
            return Db::name('plugin_lottery_prizes')
                ->where('status', 1)
                ->where('stock', '>', 0)
                ->order('sort asc, id asc')
                ->select()
                ->toArray();
        } catch (\Exception $e) {
            Log::error('获取可用奖品失败：' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取所有奖品
     */
    public static function getPrizes() {
        try {
            return Db::name('plugin_lottery_prizes')
                ->order('sort asc, id asc')
                ->select()
                ->toArray();
        } catch (\Exception $e) {
            Log::error('获取奖品失败：' . $e->getMessage());
            return [];
        }
    }

    /**
     * 添加抽奖记录
     */
    public static function addRecord($record) {
        try {
            $record['create_time'] = date('Y-m-d H:i:s');
            $record['update_time'] = date('Y-m-d H:i:s');
            return Db::name('plugin_lottery_records')->insert($record);
        } catch (\Exception $e) {
            Log::error('添加抽奖记录失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * 减少奖品库存
     */
    public static function decreasePrizeStock($prizeId) {
        try {
            return Db::name('plugin_lottery_prizes')
                ->where('id', $prizeId)
                ->where('stock', '>', 0)
                ->dec('stock')
                ->update();
        } catch (\Exception $e) {
            Log::error('减少奖品库存失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取抽奖记录
     */
    public static function getRecords($where = [], $page = 1, $pageSize = 20, $hideHiddenPrizes = false) {
        try {
            $query = Db::name('plugin_lottery_records')
                ->alias('r')
                ->leftJoin('plugin_lottery_prizes p', 'r.prize_id = p.id')
                ->field('r.*, p.name as prize_name, p.type as prize_type');

            if (!empty($where)) {
                $query->where($where);
            }

            // 如果需要隐藏指定奖品
            if ($hideHiddenPrizes) {
                $hiddenPrizes = self::getHiddenPrizes();
                if (!empty($hiddenPrizes)) {
                    $query->whereNotIn('r.prize_id', $hiddenPrizes);
                }
            }

            $total = $query->count();
            $list = $query->order('r.create_time desc')
                ->page($page, $pageSize)
                ->select()
                ->toArray();

            return [
                'list' => $list,
                'total' => $total
            ];
        } catch (\Exception $e) {
            Log::error('获取抽奖记录失败：' . $e->getMessage());
            return [
                'list' => [],
                'total' => 0
            ];
        }
    }

    /**
     * 获取隐藏奖品列表
     */
    public static function getHiddenPrizes() {
        try {
            $config = Db::name('plugin_lottery_config')
                ->where('config_key', 'hidden_prizes')
                ->value('config_value');

            if ($config) {
                $hiddenPrizes = json_decode($config, true);
                return is_array($hiddenPrizes) ? array_map('intval', $hiddenPrizes) : [];
            }

            return [];
        } catch (\Exception $e) {
            Log::error('获取隐藏奖品列表失败：' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取流水规则
     */
    public static function getTurnoverRules() {
        try {
            return Db::name('plugin_lottery_turnover_rules')
                ->where('status', 1)
                ->order('turnover_amount asc')
                ->select()
                ->toArray();
        } catch (\Exception $e) {
            Log::error('获取流水规则失败：' . $e->getMessage());
            return [];
        }
    }

    /**
     * 计算流水抽奖次数（基于已领取的记录）
     */
    public static function calculateTurnoverDraws($merchantId) {
        try {
            // 获取今日已领取的流水奖励记录
            $claims = self::getTurnoverClaims($merchantId);

            // 计算总的可用抽奖次数
            $totalDraws = 0;
            foreach ($claims as $claim) {
                // 获取规则信息
                $rule = Db::name('plugin_lottery_turnover_rules')
                    ->where('id', $claim['rule_id'])
                    ->find();

                if ($rule) {
                    // 计算可用次数 = 规则给予的次数 - 已使用的次数
                    $availableDraws = $rule['draw_times'] - ($claim['draw_times_used'] ?? 0);
                    $totalDraws += max(0, $availableDraws);
                }
            }

            return $totalDraws;
        } catch (\Exception $e) {
            Log::error('计算流水抽奖次数失败：' . $e->getMessage());
            return 0;
        }
    }

    /**
     * 使用流水抽奖次数
     */
    public static function useTurnoverDraw($merchantId) {
        try {
            // 获取今日已领取的流水奖励记录
            $claims = self::getTurnoverClaims($merchantId);

            // 找到第一个还有可用次数的记录
            foreach ($claims as $claim) {
                $rule = Db::name('plugin_lottery_turnover_rules')
                    ->where('id', $claim['rule_id'])
                    ->find();

                if ($rule) {
                    $usedTimes = $claim['draw_times_used'] ?? 0;
                    $availableTimes = $rule['draw_times'] - $usedTimes;

                    if ($availableTimes > 0) {
                        // 更新已使用次数
                        Db::name('plugin_lottery_turnover_claims')
                            ->where('id', $claim['id'])
                            ->inc('draw_times_used')
                            ->update();

                        return true;
                    }
                }
            }

            return false;
        } catch (\Exception $e) {
            Log::error('使用流水抽奖次数失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取奖品类型
     */
    public static function getPrizeTypes() {
        try {
            return Db::name('plugin_lottery_prize_types')
                ->where('status', 1)
                ->order('sort asc, id asc')
                ->select()
                ->toArray();
        } catch (\Exception $e) {
            Log::error('获取奖品类型失败：' . $e->getMessage());
            return [];
        }
    }

    /**
     * 根据值获取奖品类型
     */
    public static function getPrizeTypeByValue($value) {
        try {
            return Db::name('plugin_lottery_prize_types')
                ->where('value', $value)
                ->where('status', 1)
                ->find();
        } catch (\Exception $e) {
            Log::error('获取奖品类型失败：' . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取今日流水
     */
    public static function getTodayTurnover($merchantId) {
        try {
            $today = date('Y-m-d');
            return Db::name('user_analysis')
                ->where([
                    ['user_id', '=', $merchantId],
                    ['date', '=', $today]
                ])
                ->sum('total_amount') ?: 0;
        } catch (\Exception $e) {
            Log::error('获取今日流水失败：' . $e->getMessage());
            return 0;
        }
    }

    /**
     * 获取本月流水
     */
    public static function getMonthTurnover($merchantId) {
        try {
            $monthStart = date('Y-m-01');
            $today = date('Y-m-d');
            return Db::name('user_analysis')
                ->where([
                    ['user_id', '=', $merchantId],
                    ['date', '>=', $monthStart],
                    ['date', '<=', $today]
                ])
                ->sum('total_amount') ?: 0;
        } catch (\Exception $e) {
            Log::error('获取本月流水失败：' . $e->getMessage());
            return 0;
        }
    }

    /**
     * 根据配置的重置周期获取流水
     */
    public static function getCurrentTurnover($merchantId) {
        try {
            // 获取配置的重置周期
            $config = self::getConfig();
            $resetPeriod = $config['turnover_reset_period'] ?? 'daily';

            $startDate = '';
            $endDate = date('Y-m-d');

            switch ($resetPeriod) {
                case 'daily':
                    $startDate = date('Y-m-d');
                    break;
                case 'weekly':
                    // 获取本周开始日期（周一）
                    $startDate = date('Y-m-d', strtotime('monday this week'));
                    break;
                case 'monthly':
                    // 获取本月开始日期
                    $startDate = date('Y-m-01');
                    break;
                default:
                    $startDate = date('Y-m-d');
                    break;
            }

            return Db::name('user_analysis')
                ->where([
                    ['user_id', '=', $merchantId],
                    ['date', '>=', $startDate],
                    ['date', '<=', $endDate]
                ])
                ->sum('total_amount') ?: 0;
        } catch (\Exception $e) {
            Log::error('获取流水失败：' . $e->getMessage());
            return 0;
        }
    }

    /**
     * 获取流水领取记录
     */
    public static function getTurnoverClaims($merchantId) {
        try {
            // 获取配置的重置周期
            $config = self::getConfig();
            $resetPeriod = $config['turnover_reset_period'] ?? 'daily';

            $query = Db::name('plugin_lottery_turnover_claims')
                ->where('merchant_id', $merchantId);

            // 根据重置周期过滤记录
            switch ($resetPeriod) {
                case 'daily':
                    $query->where('claim_date', date('Y-m-d'));
                    break;
                case 'weekly':
                    $weekStart = date('Y-m-d', strtotime('monday this week'));
                    $weekEnd = date('Y-m-d');
                    $query->where('claim_date', '>=', $weekStart)
                          ->where('claim_date', '<=', $weekEnd);
                    break;
                case 'monthly':
                    $monthStart = date('Y-m-01');
                    $monthEnd = date('Y-m-d');
                    $query->where('claim_date', '>=', $monthStart)
                          ->where('claim_date', '<=', $monthEnd);
                    break;
                default:
                    $query->where('claim_date', date('Y-m-d'));
                    break;
            }

            return $query->select()->toArray();
        } catch (\Exception $e) {
            Log::error('获取流水领取记录失败：' . $e->getMessage());
            return [];
        }
    }

    /**
     * 计算下级商家销售分佣获得的抽奖次数
     */
    public static function calculateCommissionDraws($merchantId) {
        try {
            $config = self::getConfig();

            // 检查是否启用分佣抽奖功能
            if (!$config['commission_draw_enabled']) {
                return 0;
            }

            $today = date('Y-m-d');
            $startTime = $today . ' 00:00:00';
            $endTime = $today . ' 23:59:59';

            // 查询今日下级商家销售分佣记录
            $commissionAmount = Db::name('user_money_log')
                ->where('user_id', $merchantId)
                ->where('reason', '下级商家销售分佣')
                ->where('create_time', '>=', strtotime($startTime))
                ->where('create_time', '<=', strtotime($endTime))
                ->sum('change') ?: 0;

            // 计算可获得的抽奖次数
            $totalDrawTimes = 0;
            if ($commissionAmount > 0 && $config['commission_draw_amount'] > 0) {
                $totalDrawTimes = floor($commissionAmount / $config['commission_draw_amount']);

                // 限制最大次数
                if ($config['commission_draw_max'] > 0) {
                    $totalDrawTimes = min($totalDrawTimes, $config['commission_draw_max']);
                }
            }

            // 获取已使用的次数
            $usedCount = self::getCommissionDrawUsedCount($merchantId);

            // 返回剩余可用次数
            return max(0, $totalDrawTimes - $usedCount);
        } catch (\Exception $e) {
            Log::error('计算分佣抽奖次数失败：' . $e->getMessage());
            return 0;
        }
    }

    /**
     * 获取今日分佣金额
     */
    public static function getTodayCommissionAmount($merchantId) {
        try {
            $today = date('Y-m-d');
            $startTime = $today . ' 00:00:00';
            $endTime = $today . ' 23:59:59';

            return Db::name('user_money_log')
                ->where('user_id', $merchantId)
                ->where('reason', '下级商家销售分佣')
                ->where('create_time', '>=', strtotime($startTime))
                ->where('create_time', '<=', strtotime($endTime))
                ->sum('change') ?: 0;
        } catch (\Exception $e) {
            Log::error('获取今日分佣金额失败：' . $e->getMessage());
            return 0;
        }
    }

    /**
     * 获取分佣抽奖次数已使用数量
     */
    public static function getCommissionDrawUsedCount($merchantId) {
        try {
            $today = date('Y-m-d');
            $record = Db::name('plugin_lottery_commission_draws')
                ->where('merchant_id', $merchantId)
                ->where('draw_date', $today)
                ->find();

            return $record ? intval($record['used_count']) : 0;
        } catch (\Exception $e) {
            Log::error('获取分佣抽奖已使用次数失败：' . $e->getMessage());
            return 0;
        }
    }

    /**
     * 使用分佣抽奖次数
     */
    public static function useCommissionDraw($merchantId) {
        try {
            $today = date('Y-m-d');

            // 检查是否有可用的分佣抽奖次数
            $availableDraws = self::calculateCommissionDraws($merchantId);
            if ($availableDraws <= 0) {
                return false;
            }

            // 查找或创建记录
            $record = Db::name('plugin_lottery_commission_draws')
                ->where('merchant_id', $merchantId)
                ->where('draw_date', $today)
                ->find();

            if ($record) {
                // 更新已使用次数
                Db::name('plugin_lottery_commission_draws')
                    ->where('id', $record['id'])
                    ->inc('used_count')
                    ->update();
            } else {
                // 创建新记录
                Db::name('plugin_lottery_commission_draws')->insert([
                    'merchant_id' => $merchantId,
                    'draw_date' => $today,
                    'used_count' => 1,
                    'create_time' => date('Y-m-d H:i:s'),
                    'update_time' => date('Y-m-d H:i:s')
                ]);
            }

            return true;
        } catch (\Exception $e) {
            Log::error('使用分佣抽奖次数失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * 创建邀请关系
     */
    public static function createInvitation($inviterId, $inviteeId, $invitationCode = null) {
        try {
            // 检查被邀请人是否已经有邀请关系
            $existing = Db::name('plugin_lottery_invitations')
                ->where('invitee_id', $inviteeId)
                ->find();

            if ($existing) {
                return false; // 已经被邀请过
            }

            // 获取用户信息
            $inviter = Db::name('user')->where('id', $inviterId)->find();
            $invitee = Db::name('user')->where('id', $inviteeId)->find();

            if (!$inviter || !$invitee) {
                return false;
            }

            // 创建邀请关系
            $invitationId = Db::name('plugin_lottery_invitations')->insertGetId([
                'inviter_id' => $inviterId,
                'inviter_name' => $inviter['username'],
                'invitee_id' => $inviteeId,
                'invitee_name' => $invitee['username'],
                'invitation_code' => $invitationCode,
                'status' => 1, // 直接确认
                'create_time' => date('Y-m-d H:i:s'),
                'confirm_time' => date('Y-m-d H:i:s')
            ]);

            // 检查是否启用邀请奖励
            $config = self::getConfig();
            if ($config['invitation_enabled'] && $config['invitation_reward_amount'] > 0) {
                // 创建奖励记录
                Db::name('plugin_lottery_invitation_rewards')->insert([
                    'invitation_id' => $invitationId,
                    'inviter_id' => $inviterId,
                    'inviter_name' => $inviter['username'],
                    'invitee_id' => $inviteeId,
                    'invitee_name' => $invitee['username'],
                    'reward_amount' => $config['invitation_reward_amount'],
                    'reward_status' => $config['invitation_auto_send'] ? 1 : 0,
                    'send_time' => $config['invitation_auto_send'] ? date('Y-m-d H:i:s') : null,
                    'create_time' => date('Y-m-d H:i:s')
                ]);

                // 如果自动发放奖励
                if ($config['invitation_auto_send']) {
                    self::sendInvitationReward($invitationId);
                }
            }

            return $invitationId;
        } catch (\Exception $e) {
            Log::error('创建邀请关系失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * 发放邀请奖励
     */
    public static function sendInvitationReward($invitationId) {
        try {
            $reward = Db::name('plugin_lottery_invitation_rewards')
                ->where('invitation_id', $invitationId)
                ->find();

            if (!$reward || $reward['reward_status'] == 1) {
                return false; // 奖励不存在或已发放
            }

            // 给邀请人发放奖励
            Db::startTrans();
            try {
                // 增加邀请人余额
                Db::name('user')
                    ->where('id', $reward['inviter_id'])
                    ->inc('operate_money', $reward['reward_amount'])
                    ->update();

                // 记录资金变动
                Db::name('user_money_log')->insert([
                    'user_id' => $reward['inviter_id'],
                    'change' => $reward['reward_amount'],
                    'reason' => '邀请奖励',
                    'memo' => '邀请用户【' . $reward['invitee_name'] . '】获得奖励',
                    'create_time' => time(),
                    'source' => 'Invitation'
                ]);

                // 更新奖励状态
                Db::name('plugin_lottery_invitation_rewards')
                    ->where('id', $reward['id'])
                    ->update([
                        'reward_status' => 1,
                        'send_time' => date('Y-m-d H:i:s')
                    ]);

                Db::commit();
                return true;
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('发放邀请奖励失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取邀请记录
     */
    public static function getInvitationRecords($page = 1, $pageSize = 20) {
        try {
            $query = Db::name('plugin_lottery_invitation_rewards')
                ->alias('r')
                ->leftJoin('plugin_lottery_invitations i', 'r.invitation_id = i.id')
                ->field('r.*, i.create_time as invitation_time');

            $total = $query->count();
            $list = $query->order('r.create_time desc')
                ->page($page, $pageSize)
                ->select()
                ->toArray();

            return [
                'list' => $list,
                'total' => $total
            ];
        } catch (\Exception $e) {
            Log::error('获取邀请记录失败：' . $e->getMessage());
            return [
                'list' => [],
                'total' => 0
            ];
        }
    }

    /**
     * 获取邀请统计
     */
    public static function getInvitationStats() {
        try {
            // 总邀请人数
            $totalInviters = Db::name('plugin_lottery_invitations')
                ->group('inviter_id')
                ->count();

            // 今日邀请数
            $todayInvitations = Db::name('plugin_lottery_invitations')
                ->whereTime('create_time', 'today')
                ->count();

            // 待发放奖励数
            $pendingRewards = Db::name('plugin_lottery_invitation_rewards')
                ->where('reward_status', 0)
                ->count();

            // 已发放奖励总金额
            $totalRewardAmount = Db::name('plugin_lottery_invitation_rewards')
                ->where('reward_status', 1)
                ->sum('reward_amount') ?: 0;

            return [
                'total_inviters' => $totalInviters,
                'today_invitations' => $todayInvitations,
                'pending_rewards' => $pendingRewards,
                'total_reward_amount' => $totalRewardAmount
            ];
        } catch (\Exception $e) {
            Log::error('获取邀请统计失败：' . $e->getMessage());
            return [
                'total_inviters' => 0,
                'today_invitations' => 0,
                'pending_rewards' => 0,
                'total_reward_amount' => 0
            ];
        }
    }
}
