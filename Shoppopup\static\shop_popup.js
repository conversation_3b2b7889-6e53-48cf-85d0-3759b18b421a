// shop_popup.js

// 引入 Vue 和 Element Plus（假设页面中未预加载）
const scriptVue = document.createElement('script');
scriptVue.src = 'https://unpkg.com/vue@3';
document.head.appendChild(scriptVue);

const scriptElementPlus = document.createElement('script');
scriptElementPlus.src = 'https://unpkg.com/element-plus';
document.head.appendChild(scriptElementPlus);

const linkElementPlus = document.createElement('link');
linkElementPlus.rel = 'stylesheet';
linkElementPlus.href = 'https://unpkg.com/element-plus/dist/index.css';
document.head.appendChild(linkElementPlus);

// 等待 Vue 和 Element Plus 加载完成
scriptElementPlus.onload = () => {
    const { createApp, ref, onMounted } = Vue;

    // 创建弹窗应用
    const app = createApp({
        setup() {
            const showDialog = ref(false);
            const user = ref(window.user || '未知商家'); // 从全局变量或服务端植入的 JS 获取商家信息

            // 页面加载完成后显示弹窗
            onMounted(() => {
                showDialog.value = true;
            });

            return {
                showDialog,
                user,
            };
        },
        template: `
            <el-dialog 
                v-model="showDialog" 
                title="欢迎访问商家店铺" 
                custom-class="custom-dialog"
                :close-on-click-modal="false"
                :append-to-body="true">
                <div>
                    <p>您好，您正在访问商家：{{ user }}</p>
                    <p>欢迎光临！</p>
                </div>
            </el-dialog>
        `,
    });

    // 创建挂载点
    const mountPoint = document.createElement('div');
    document.body.appendChild(mountPoint);

    // 注册 Element Plus 并挂载应用
    app.use(ElementPlus);
    app.mount(mountPoint);
};
