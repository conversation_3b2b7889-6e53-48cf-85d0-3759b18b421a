<?php
namespace plugin\Clearallinone;

use think\facade\Db;

class Hook
{
    public function handle()
    {
        try {
            // 检查是否启用订单清理
            if (intval(plugconf("Clearallinone.order_status") ?? 0) === 1) {
                $this->clearOrders();
            }
            
            // 检查是否启用商户通知自动清理
            if (intval(plugconf("Clearallinone.message_status") ?? 0) === 1) {
                $this->clearMessages();
            }

            // 检查是否启用卡密自动清理
            if (intval(plugconf("Clearallinone.card_status") ?? 0) === 1) {
                $this->clearDeletedCards();
            }
        } catch (\Exception $e) {
            error_log("Clearallinone: 清理任务失败: " . $e->getMessage());
        }
    }

    private function clearOrders()
    {
        try {
            // 检查是否启用自动清理
            $status = intval(plugconf("Clearallinone.order_status") ?? 0);
            if ($status !== 1) {
                return;
            }

            // 获取配置
            $days = intval(plugconf("Clearallinone.order_days") ?? 30);
            $executeTime = plugconf("Clearallinone.order_execute_time") ?? "00:00";
            $interval = intval(plugconf("Clearallinone.order_interval") ?? 1);
            $autoStatusList = explode(',', plugconf("Clearallinone.order_auto_status_list") ?? '1');
            
            // 验证执行时间格式
            if (!preg_match('/^([01][0-9]|2[0-3]):([0-5][0-9])$/', $executeTime)) {
                error_log("Clearallinone: 执行时间格式错误");
                return;
            }

            // 检查执行间隔
            $lastExecuteTime = intval(plugconf("Clearallinone.last_order_execute_time") ?? 0);
            $currentTime = time();
            
            // 如果设置了间隔且未达到间隔时间，则不执行
            if ($interval > 0 && ($currentTime - $lastExecuteTime) < ($interval * 86400)) {
                return;
            }

            // 检查是否到达执行时间
            $currentHour = date('H', $currentTime);
            $currentMinute = date('i', $currentTime);
            list($executeHour, $executeMinute) = explode(':', $executeTime);
            
            if ($currentHour != $executeHour || $currentMinute != $executeMinute) {
                return;
            }

            // 计算清理时间点
            $clearTime = $currentTime - ($days * 86400);
            
            // 获取今天的开始时间
            $todayStart = strtotime(date('Y-m-d 00:00:00', $currentTime));

            // 获取所有冻结中的订单ID
            $frozenOrderIds = Db::name('auto_unfreeze')
                ->column('order_id');

            // 构建查询条件
            $query = Db::name('order');

            // 添加时间条件
            $query->where(function ($query) use ($clearTime, $todayStart) {
                // 对于已完成订单，使用 success_time 判断
                $query->where(function ($q) use ($clearTime, $todayStart) {
                    $q->where('success_time', '<=', $clearTime)  // 完成时间早于清理时间点
                      ->where('success_time', '<', $todayStart)  // 不清理今天的订单
                      ->where('success_time', '>', 0);  // 确保是已完成订单
                });

                // 对于未完成订单，使用 create_time 判断
                $query->whereOr(function ($q) use ($clearTime, $todayStart) {
                    $q->where('success_time', 0)  // 未完成订单
                      ->where('create_time', '<=', $clearTime)  // 创建时间早于清理时间点
                      ->where('create_time', '<', $todayStart);  // 不清理今天的订单
                });
            });

            // 排除冻结中的订单
            if (!empty($frozenOrderIds)) {
                $query->whereNotIn('id', $frozenOrderIds);
            }

            // 添加状态条件
            if (!empty($autoStatusList)) {
                $query->whereIn('status', $autoStatusList);
            }

            // 获取商家销量统计
            $sellerStats = $query->where('status', 1)
                ->group('user_id')
                ->column('COUNT(*)', 'user_id');

            // 开始事务
            Db::startTrans();
            try {
                // 执行删除
                $deleted = $query->delete();

                // 更新商家销量统计
                foreach ($sellerStats as $sellerId => $count) {
                    Db::name('user')
                        ->where('id', $sellerId)
                        ->dec('sell_count', $count)
                        ->update();
                }

                // 更新最后执行时间
                plugconf("Clearallinone.last_order_execute_time", $currentTime);
                
                Db::commit();

                // 记录日志
                $logMessage = sprintf(
                    "自动清理订单完成，共清理 %d 条订单记录，更新了 %d 个商家的销量统计",
                    $deleted,
                    count($sellerStats)
                );
                error_log("Clearallinone: " . $logMessage);

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            error_log("Clearallinone: 自动清理订单失败: " . $e->getMessage());
            throw $e;
        }
    }

    // 添加商户通知自动清理方法
    private function clearMessages()
    {
        try {
            $status = intval(plugconf("Clearallinone.message_status") ?? 0);
            if ($status !== 1) {
                return;
            }

            $days = intval(plugconf("Clearallinone.message_days") ?? 1);
            $executeTime = plugconf("Clearallinone.message_execute_time") ?? "00:00";
            $interval = intval(plugconf("Clearallinone.message_interval") ?? 1);
            
            // 验证执行时间格式
            if (!preg_match('/^([01][0-9]|2[0-3]):([0-5][0-9])$/', $executeTime)) {
                error_log("Clearallinone: 执行时间格式错误，已重置为默认值 00:00");
                $executeTime = "00:00";
                plugconf("Clearallinone.message_execute_time", $executeTime);
            }

            // 获取当前时间和上次执行时间
            $currentTime = time();
            $lastExecuteTime = intval(plugconf("Clearallinone.last_message_execute_time") ?? 0);
            
            // 检查执行间隔
            if ($interval > 0 && ($currentTime - $lastExecuteTime) < ($interval * 86400)) {
                return;
            }

            // 获取今天的执行时间点
            list($executeHour, $executeMinute) = explode(':', $executeTime);
            $todayExecuteTime = strtotime(date('Y-m-d') . " {$executeHour}:{$executeMinute}:00");
            
            if ($currentTime < $todayExecuteTime || $lastExecuteTime >= $todayExecuteTime) {
                return;
            }

            // 计算清理时间点
            $clearTime = $currentTime - ($days * 86400);

            // 获取所有冻结中的订单ID
            $frozenOrderIds = Db::name('auto_unfreeze')
                ->where('status', 1)  // 1表示冻结中的状态
                ->column('order_id');

            // 构建查询条件
            $query = Db::name('user_message')
                ->where('user_read', 1);  // 只清理已读消息

            // 如果有冻结订单，则排除这些订单相关的消息
            if (!empty($frozenOrderIds)) {
                $query->whereNotIn('order_id', $frozenOrderIds);
            }

            // 添加时间条件
            $query->where('create_time', '<=', $clearTime);

            // 获取要删除的记录数
            $beforeCount = $query->count();

            if ($beforeCount === 0) {
                error_log("Clearallinone: 没有需要清理的商户通知");
                return;
            }

            // 获取要删除的消息列表
            $messagesToDelete = $query->field('id, user_id, create_time')->select()->toArray();
            $count = count($messagesToDelete);

            // 记录清理详情
            $logDetails = [];
            foreach ($messagesToDelete as $message) {
                $logDetails[] = sprintf(
                    "通知ID: %d, 用户ID: %d, 创建时间: %s",
                    $message['id'],
                    $message['user_id'],
                    date('Y-m-d H:i:s', $message['create_time'])
                );
            }

            // 执行删除
            $deleted = $query->delete();
            
            // 更新最后执行时间
            plugconf("Clearallinone.last_message_execute_time", $currentTime);
            
            // 记录详细日志
            $logMessage = sprintf(
                "自动清理商户通知完成，共清理 %d 条已读通知\n清理详情：\n%s",
                $deleted,
                implode("\n", $logDetails)
            );
            error_log("Clearallinone: " . $logMessage);

        } catch (\Exception $e) {
            error_log("Clearallinone: 自动清理商户通知失败: " . $e->getMessage());
            throw $e;
        }
    }

    // 添加自动清理已删除卡密方法
    private function clearDeletedCards()
    {
        try {
            // 检查是否启用自动清理
            $status = intval(plugconf("Clearallinone.card_status") ?? 0);
            if ($status !== 1) {
                return;
            }

            // 获取配置
            $executeTime = plugconf("Clearallinone.card_execute_time") ?? "00:00";
            $interval = intval(plugconf("Clearallinone.card_interval") ?? 0); // 默认值改为0
            
            // 验证执行时间格式
            if (!preg_match('/^([01][0-9]|2[0-3]):([0-5][0-9])$/', $executeTime)) {
                error_log("Clearallinone: 执行时间格式错误，已重置为默认值 00:00");
                $executeTime = "00:00";
                plugconf("Clearallinone.card_execute_time", $executeTime);
            }

            // 检查执行间隔
            $lastExecuteTime = intval(plugconf("Clearallinone.last_card_execute_time") ?? 0);
            $currentTime = time();
            
            // 修改间隔判断逻辑
            if ($interval > 0) {
                // 如果设置了大于0的间隔，才检查间隔时间
                if (($currentTime - $lastExecuteTime) < ($interval * 86400)) {
                    return;
                }
            }

            // 检查是否到达今天的执行时间点
            list($executeHour, $executeMinute) = explode(':', $executeTime);
            $todayExecuteTime = strtotime(date('Y-m-d') . " {$executeHour}:{$executeMinute}:00");
            
            // 如果未到执行时间或今天已执行过，则不执行
            if ($currentTime < $todayExecuteTime || $lastExecuteTime >= $todayExecuteTime) {
                return;
            }

            // 构建查询条件：查找所有已删除的卡密
            $query = Db::name('goods_card_storage_0')
                ->whereNotNull('delete_time');  // 确保只清理已删除的卡密

            // 获取要删除的记录数
            $beforeCount = $query->count();

            if ($beforeCount === 0) {
                error_log("Clearallinone: 没有需要清理的已删除卡密");
                return;
            }

            // 执行物理删除
            $deleted = $query->delete(true);  // true 表示物理删除
            
            // 更新最后执行时间
            plugconf("Clearallinone.last_card_execute_time", $currentTime);
            
            // 记录日志
            $logMessage = sprintf(
                "自动清理已删除卡密完成，共清理 %d 条记录",
                $deleted
            );
            error_log("Clearallinone: " . $logMessage);

        } catch (\Exception $e) {
            error_log("Clearallinone: 自动清理已删除卡密失败: " . $e->getMessage());
            throw $e;
        }
    }
}
