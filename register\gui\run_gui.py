#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
启动注册工具GUI（包含椰子云自动注册功能）
"""

import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

def main():
    """主函数"""
    try:
        from PyQt6.QtWidgets import QApplication
        from gui.register_gui import RegisterGUI
        
        # 创建应用
        app = QApplication(sys.argv)
        
        # 设置应用信息
        app.setApplicationName("用户注册工具")
        app.setApplicationVersion("1.0")
        app.setOrganizationName("Register Tool")
        
        # 创建主窗口
        window = RegisterGUI()
        window.show()
        
        print("[启动] 注册工具GUI已启动")
        print("[信息] 新功能: 椰子云自动注册")
        print("[提示] 使用方法:")
        print("   1. 配置椰子云API信息")
        print("   2. 点击'椰子云自动注册'按钮")
        print("   3. 享受一键式自动注册")
        
        # 运行应用
        sys.exit(app.exec())
        
    except ImportError as e:
        print(f"[失败] 导入错误: {e}")
        print("[提示] 请确保已安装所需依赖:")
        print("   pip install PyQt6 selenium requests ddddocr")
        
    except Exception as e:
        print(f"[失败] 启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
