(function() {
    // 创建状态标志，用于防止重复显示弹窗
    var surveyShown = false;
    var surveySubmitted = false;
    
    // 创建问卷弹窗容器
    var survey_wrapper = document.createElement('div');
    survey_wrapper.id = 'registration-survey';
    survey_wrapper.style.position = 'fixed';
    survey_wrapper.style.top = '50%';
    survey_wrapper.style.left = '50%';
    survey_wrapper.style.transform = 'translate(-50%, -50%)';
    survey_wrapper.style.zIndex = '999999';
    survey_wrapper.style.background = 'white';
    survey_wrapper.style.padding = '30px';
    survey_wrapper.style.borderRadius = '12px';
    survey_wrapper.style.boxShadow = '0 4px 20px 0 rgba(0, 0, 0, 0.15)';
    survey_wrapper.style.width = '90%';
    survey_wrapper.style.maxWidth = '600px';
    survey_wrapper.style.display = 'none';
    
    // 简单的Markdown转HTML函数
    function convertMarkdownToHtml(text) {
        if (!text) return '';
        
        try {
            console.log("处理Markdown文本，前40个字符:", text.substring(0, 40));
            
            // 检测乱码并尝试修复
            if (containsGarbledText(text)) {
                console.log("检测到可能的乱码，尝试修复...");
                text = tryFixGarbledText(text);
            }
            
            // 基本安全处理
            text = text.replace(/</g, '&lt;').replace(/>/g, '&gt;');
            
            // 替换标题 - 修复标题匹配，确保能匹配多行文本中的标题和没有空格的标题
            text = text.replace(/^###\s*(.*?)$/gm, '<h3>$1</h3>');
            text = text.replace(/^##\s*(.*?)$/gm, '<h2>$1</h2>');
            text = text.replace(/^#\s*(.*?)$/gm, '<h1>$1</h1>');
            
            // 替换粗体和斜体 - 使用非贪婪匹配确保嵌套格式正确
            text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
            text = text.replace(/\*(.*?)\*/g, '<em>$1</em>');
            
            // 替换链接
            text = text.replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2">$1</a>');
            
            // 添加代码块支持
            text = text.replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>');
            
            // 添加行内代码支持
            text = text.replace(/`([^`]+)`/g, '<code>$1</code>');
            
            // 替换列表 - 改进列表处理方式
            let lines = text.split('\n');
            let inList = false;
            let listType = '';
            let listContent = '';
            let result = [];
            
            for (let i = 0; i < lines.length; i++) {
                // 有序列表项 (匹配数字后面跟着点和空格)
                if (lines[i].match(/^\d+\.\s+(.*?)$/)) {
                    if (!inList || listType !== 'ol') {
                        // 如果已经在列表中但类型不同，结束当前列表
                        if (inList) {
                            result.push(listType === 'ul' ? '</ul>' : '</ol>');
                        }
                        result.push('<ol>');
                        inList = true;
                        listType = 'ol';
                    }
                    // 提取列表项内容
                    let content = lines[i].replace(/^\d+\.\s+(.*?)$/, '$1');
                    result.push('<li>' + content + '</li>');
                }
                // 无序列表项 (匹配破折号或星号后面跟着空格)
                else if (lines[i].match(/^[-*]\s+(.*?)$/)) {
                    if (!inList || listType !== 'ul') {
                        // 如果已经在列表中但类型不同，结束当前列表
                        if (inList) {
                            result.push(listType === 'ul' ? '</ul>' : '</ol>');
                        }
                        result.push('<ul>');
                        inList = true;
                        listType = 'ul';
                    }
                    // 提取列表项内容
                    let content = lines[i].replace(/^[-*]\s+(.*?)$/, '$1');
                    result.push('<li>' + content + '</li>');
                }
                // 如果不是列表项且当前在列表中，结束列表
                else if (inList) {
                    result.push(listType === 'ul' ? '</ul>' : '</ol>');
                    inList = false;
                    listType = '';
                    // 添加当前非列表行
                    result.push(lines[i]);
                }
                // 普通行
                else {
                    result.push(lines[i]);
                }
            }
            
            // 如果文档结束但列表还没结束
            if (inList) {
                result.push(listType === 'ul' ? '</ul>' : '</ol>');
            }
            
            text = result.join('\n');
            
            // 替换水平线
            text = text.replace(/^---$/gm, '<hr>');
            
            // 替换换行，但保留段落结构
            text = text.replace(/\n\n/g, '</p><p>');
            text = text.replace(/\n/g, '<br>');
            
            // 确保文本被段落包裹
            if (!text.startsWith('<h') && !text.startsWith('<p>') && 
                !text.startsWith('<ul') && !text.startsWith('<ol') && 
                !text.startsWith('<pre')) {
                text = '<p>' + text;
            }
            if (!text.endsWith('</p>') && !text.endsWith('</h1>') && 
                !text.endsWith('</h2>') && !text.endsWith('</h3>') && 
                !text.endsWith('</ul>') && !text.endsWith('</ol>') && 
                !text.endsWith('</pre>')) {
                text = text + '</p>';
            }
            
            // 最终检查 - 如果结果仍包含乱码，记录并尝试进一步修复
            if (containsGarbledText(text)) {
                console.warn("Markdown转换后仍检测到乱码");
            } else {
                console.log("Markdown转换成功");
            }
            
            return text;
        } catch (e) {
            console.error("Markdown转换失败:", e);
            return '<p>' + text + '</p>';
        }
    }
    
    // 检测文本是否包含乱码
    function containsGarbledText(text) {
        if (!text) return false;
        
        // 检测典型的UTF-8乱码特征
        const garbledPatterns = [/ç[^\w]/g, /æ[^\w]/g, /å[^\w]/g, /\&\#[0-9]+;/g, /\&[a-z]+;/g];
        for (let pattern of garbledPatterns) {
            if (pattern.test(text)) {
                return true;
            }
        }
        
        // 检测中文内容被破坏的特征
        if (text.includes('ç') || text.includes('æ') || text.includes('å') || 
            text.includes('&#') || (text.includes('&') && text.includes(';'))) {
            return true;
        }
        
        return false;
    }
    
    // 尝试修复乱码文本
    function tryFixGarbledText(text) {
        if (!text) return '';
        
        console.log("尝试修复乱码文本...");
        
        // 首先尝试HTML实体解码
        try {
            if (text.indexOf('&') !== -1 && text.indexOf(';') !== -1) {
                let div = document.createElement('div');
                div.innerHTML = text;
                let decoded = div.textContent;
                // 检查是否成功解码（判断是否含有更多中文字符）
                if (countChineseChars(decoded) > countChineseChars(text)) {
                    console.log("HTML实体解码成功");
                    return decoded;
                }
            }
        } catch (e) {
            console.log("HTML实体解码失败:", e.message);
        }
        
        // 方法1: 尝试通过escape/unescape修复双重编码
        try {
            let fixed = decodeURIComponent(escape(text));
            // 只有当结果看起来更好时才采用（含有更多中文字符）
            if (countChineseChars(fixed) > countChineseChars(text)) {
                console.log("方法1成功修复乱码");
                return fixed;
            }
        } catch (e) {
            console.log("方法1修复失败:", e.message);
        }
        
        // 方法2: URL解码尝试
        try {
            let urlDecoded = decodeURIComponent(text);
            if (countChineseChars(urlDecoded) > countChineseChars(text)) {
                console.log("URL解码成功修复乱码");
                return urlDecoded;
            }
        } catch (e) {
            console.log("URL解码失败:", e.message);
        }
        
        // 方法3: 替换常见乱码序列
        let replacements = {
            'ç': '中', 'æ': '文', 'å': '字', 
            '&quot;': '"', '&amp;': '&', '&lt;': '<', '&gt;': '>', '&nbsp;': ' ',
            '&#39;': "'", '&#38;': '&', '&#60;': '<', '&#62;': '>'
        };
        
        let result = text;
        for (let char in replacements) {
            result = result.replace(new RegExp(char, 'g'), replacements[char]);
        }
        
        // 检查替换后的文本是否有更多中文字符
        if (countChineseChars(result) > countChineseChars(text)) {
            console.log("常见乱码序列替换成功");
            return result;
        }
        
        // 如果经过所有修复仍无法修复，返回原始文本并提供警告
        console.warn("所有乱码修复方法均失败，使用原始文本");
        
        // 至少清理可能干扰显示的HTML实体
        return text.replace(/\&[a-z]+;/g, ' ').replace(/\&#[0-9]+;/g, ' ');
    }
    
    // 统计中文字符数量
    function countChineseChars(text) {
        if (!text) return 0;
        const chineseRegex = /[\u4e00-\u9fff]/g;
        const matches = text.match(chineseRegex);
        return matches ? matches.length : 0;
    }
    
    // 添加关闭按钮
    var closeButton = document.createElement('div');
    closeButton.innerHTML = '&times;';
    closeButton.style.position = 'absolute';
    closeButton.style.top = '10px';
    closeButton.style.right = '15px';
    closeButton.style.fontSize = '24px';
    closeButton.style.cursor = 'pointer';
    closeButton.style.color = '#999';
    
    // 添加一个标志来防止重复触发
    var isShowingDialog = false;
    
    // 记录上一个路径，用于检测路由变化
    var lastPathname = window.location.pathname;
    
    // 检查是否在注册页面并显示弹窗
    function checkPathAndShowSurvey(isRefresh = false) {
        if (isShowingDialog) {
            return; // 如果已经在显示中，则不重复触发
        }

        if (window.location.pathname === '/merchant/register' || window.location.pathname === '/register') {
            isShowingDialog = true;
            // 不再每次都重新初始化内容
            if (survey_wrapper.children.length === 0) {
                initSurveyContent().then(() => {
                    setTimeout(function() {
                        survey_wrapper.style.display = 'block';
                    }, isRefresh ? 0 : 500);
                });
            } else {
                setTimeout(function() {
                    survey_wrapper.style.display = 'block';
                }, isRefresh ? 0 : 500);
            }
        } else {
            survey_wrapper.style.display = 'none';
            isShowingDialog = false;
        }
    }
    
    // 定期检测URL变化
    setInterval(function() {
        if (window.location.pathname !== lastPathname) {
            lastPathname = window.location.pathname;
            checkPathAndShowSurvey(false); // 路由变化触发
        }
    }, 300);
    
    // 添加历史状态变化监听
    window.addEventListener('popstate', function() {
        checkPathAndShowSurvey(false);
    });
    
    // 添加路由变化监听
    window.addEventListener('pushstate', function() {
        checkPathAndShowSurvey(false);
    });
    
    // 添加replaceState监听
    window.addEventListener('replacestate', function() {
        checkPathAndShowSurvey(false);
    });
    
    // 修改DOM加载处理
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            if (!document.getElementById('registration-survey')) {
                document.body.appendChild(survey_wrapper);
                checkPathAndShowSurvey(true);
            }
        });
    } else {
        if (!document.getElementById('registration-survey')) {
            document.body.appendChild(survey_wrapper);
            checkPathAndShowSurvey(true);
        }
    }
    
    // 修改初始化问卷内容的函数
    async function initSurveyContent() {
        try {
            // 先检查是否已经有内容
            if (survey_wrapper.children.length > 0) {
                return; // 如果已经有内容，直接返回
            }
            
            // 清空现有内容以防万一
            survey_wrapper.innerHTML = '';
            
            const response = await fetch('/plugin/Registrationpage/api/getOptions');
            const result = await response.json();
            
            if (result.code !== 200) {
                return;
            }

            const { options, title, buttonText, customMessages, showCloseButton } = result.data;
            
            if (showCloseButton) {
                closeButton.innerHTML = '&times;';
                closeButton.style.position = 'absolute';
                closeButton.style.top = '10px';
                closeButton.style.right = '15px';
                closeButton.style.fontSize = '24px';
                closeButton.style.cursor = 'pointer';
                closeButton.style.color = '#333';
                survey_wrapper.appendChild(closeButton);
            }
            
            // 创建问卷标题
            var titleElement = document.createElement('h3');
            titleElement.style.marginTop = '0';
            titleElement.style.marginBottom = '30px';
            titleElement.style.fontSize = '22px';
            titleElement.style.fontWeight = '600';
            titleElement.style.textAlign = 'center';
            titleElement.style.color = '#333';
            titleElement.style.borderBottom = '1px solid #f0f2f5';
            titleElement.style.paddingBottom = '15px';
            titleElement.textContent = title;
            survey_wrapper.appendChild(titleElement);
            
            // 创建选项容器
            var optionsContainer = document.createElement('div');
            optionsContainer.className = 'survey-options';
            optionsContainer.style.marginBottom = '30px';
            
            // 添加选项
            options.sort((a, b) => a.sort - b.sort).forEach((option, index) => {
                var optionDiv = document.createElement('div');
                optionDiv.style.padding = '16px 20px';
                optionDiv.style.marginBottom = '12px';
                optionDiv.style.borderRadius = '8px';
                optionDiv.style.cursor = 'pointer';
                optionDiv.style.transition = 'all 0.3s';
                optionDiv.style.border = '1px solid #ebeef5';
                optionDiv.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.02)';
                optionDiv.style.fontSize = '16px';
                optionDiv.textContent = option.text;
                optionDiv.dataset.index = index;
                optionDiv.dataset.value = option.text;
                
                // 悬停效果
                optionDiv.onmouseover = function() {
                    if (this.style.backgroundColor !== 'rgb(64, 128, 255)') {
                        this.style.backgroundColor = '#f5f7fa';
                        this.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.05)';
                    }
                };
                
                optionDiv.onmouseout = function() {
                    if (this.style.backgroundColor !== 'rgb(64, 128, 255)') {
                        this.style.backgroundColor = 'transparent';
                        this.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.02)';
                    }
                };
                
                // 点击选项切换选中状态
                optionDiv.onclick = function() {
                    // 移除所有选项的选中状态
                    document.querySelectorAll('.survey-options > div').forEach(item => {
                        item.style.backgroundColor = 'transparent';
                        item.style.color = '#333';
                        item.style.border = '1px solid #ebeef5';
                        item.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.02)';
                    });
                    
                    // 设置当前选项为选中状态
                    this.style.backgroundColor = '#4080ff';
                    this.style.color = 'white';
                    this.style.border = '1px solid #4080ff';
                    this.style.boxShadow = '0 4px 12px rgba(64, 128, 255, 0.2)';
                };
                
                optionsContainer.appendChild(optionDiv);
            });
            
            survey_wrapper.appendChild(optionsContainer);
            
            // 创建提交按钮
            var submitButton = document.createElement('button');
            submitButton.textContent = buttonText || 'OK 我已选好';
            submitButton.style.backgroundColor = '#4080ff';
            submitButton.style.color = 'white';
            submitButton.style.border = 'none';
            submitButton.style.padding = '14px 20px';
            submitButton.style.borderRadius = '8px';
            submitButton.style.cursor = 'pointer';
            submitButton.style.width = '100%';
            submitButton.style.fontSize = '18px';
            submitButton.style.fontWeight = '500';
            submitButton.style.boxShadow = '0 4px 12px rgba(64, 128, 255, 0.2)';
            submitButton.style.transition = 'all 0.3s';
            
            submitButton.onclick = async function() {
                const selectedOption = document.querySelector('.survey-options > div[style*="background-color: rgb(64, 128, 255)"]');
                if (!selectedOption) {
                    showTipMessage('请选择一个选项');
                    return;
                }
                
                try {
                    const optionIndex = parseInt(selectedOption.dataset.index);
                    
                    const submitResponse = await fetch('/plugin/Registrationpage/api/saveAnswer', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            option: selectedOption.dataset.value,
                            optionIndex: optionIndex
                        })
                    });
                    
                    const submitResult = await submitResponse.json();
                    
                    if (submitResult.code === 200) {
                        survey_wrapper.style.display = 'none';
                        isShowingDialog = false;
                        
                        const responseData = submitResult.data;
                        
                        if (responseData && responseData.message) {
                            showCustomMessage(
                                responseData.message,
                                responseData.buttonText || '我知道了',
                                responseData.redirectUrl || '',
                                responseData.extraButtons || []
                            );
                            return;
                        }
                        
                        if (responseData.agreement) {
                            showAgreementDialog(responseData.agreement);
                            return;
                        }
                    } else {
                        showTipMessage('提交失败: ' + submitResult.msg);
                    }
                } catch (error) {
                    showTipMessage('提交失败，请稍后再试');
                }
            };
            
            survey_wrapper.appendChild(submitButton);
            
        } catch (error) {}
    }
    
    // 显示自定义提示信息
    function showCustomMessage(message, buttonText, redirectUrl, extraButtons) {
        var messageBox = document.createElement('div');
        messageBox.style.position = 'fixed';
        messageBox.style.top = '50%';
        messageBox.style.left = '50%';
        messageBox.style.transform = 'translate(-50%, -50%)';
        messageBox.style.backgroundColor = 'white';
        messageBox.style.padding = '30px';
        messageBox.style.borderRadius = '12px';
        messageBox.style.boxShadow = '0 4px 20px 0 rgba(0, 0, 0, 0.15)';
        messageBox.style.zIndex = '1000000';
        messageBox.style.maxWidth = '600px';
        messageBox.style.width = '90%';
        
        var messageText = document.createElement('p');
        messageText.textContent = message;
        messageText.style.margin = '0 0 30px 0';
        messageText.style.fontSize = '16px';
        messageText.style.lineHeight = '1.8';
        messageText.style.color = '#333';
        
        var closeBtn = document.createElement('button');
        closeBtn.textContent = buttonText || '我知道了';
        closeBtn.style.backgroundColor = '#409EFF';
        closeBtn.style.color = 'white';
        closeBtn.style.border = 'none';
        closeBtn.style.padding = '12px 20px';
        closeBtn.style.borderRadius = '8px';
        closeBtn.style.cursor = 'pointer';
        closeBtn.style.width = '100%';
        closeBtn.style.fontSize = '16px';
        closeBtn.style.fontWeight = '500';
        closeBtn.style.boxShadow = '0 4px 12px rgba(64, 158, 255, 0.2)';
        closeBtn.style.transition = 'all 0.3s';
        closeBtn.style.marginBottom = extraButtons && extraButtons.length > 0 ? '15px' : '0';
        
        // 添加悬停效果
        closeBtn.onmouseover = function() {
            this.style.backgroundColor = '#66b1ff';
        };
        closeBtn.onmouseout = function() {
            this.style.backgroundColor = '#409EFF';
        };
        
        closeBtn.onclick = function() {
            document.body.removeChild(messageBox);
            
            if (redirectUrl && redirectUrl.trim() !== '') {
                window.location.href = redirectUrl;
            }
        };
        
        messageBox.appendChild(messageText);
        messageBox.appendChild(closeBtn);
        
        // 添加额外的按钮
        if (extraButtons && extraButtons.length > 0) {
            var btnContainer = document.createElement('div');
            btnContainer.style.display = 'flex';
            btnContainer.style.flexWrap = 'wrap';
            btnContainer.style.gap = '15px';
            btnContainer.style.marginTop = '15px';
            
            extraButtons.forEach(function(btn) {
                var button = document.createElement('button');
                button.textContent = btn.text || '按钮';
                button.style.flexGrow = '1';
                button.style.minWidth = '150px';
                button.style.padding = '12px 20px';
                button.style.borderRadius = '8px';
                button.style.cursor = 'pointer';
                button.style.fontSize = '16px';
                button.style.fontWeight = '500';
                button.style.transition = 'all 0.3s';
                
                // 设置按钮样式
                switch (btn.type) {
                    case 'primary':
                        button.style.backgroundColor = '#409EFF';
                        button.style.color = 'white';
                        button.style.border = 'none';
                        break;
                    case 'success':
                        button.style.backgroundColor = '#67C23A';
                        button.style.color = 'white';
                        button.style.border = 'none';
                        break;
                    case 'warning':
                        button.style.backgroundColor = '#E6A23C';
                        button.style.color = 'white';
                        button.style.border = 'none';
                        break;
                    case 'danger':
                        button.style.backgroundColor = '#F56C6C';
                        button.style.color = 'white';
                        button.style.border = 'none';
                        break;
                    default: // default
                        button.style.backgroundColor = 'white';
                        button.style.color = '#606266';
                        button.style.border = '1px solid #DCDFE6';
                        break;
                }
                
                button.onclick = function() {
                    document.body.removeChild(messageBox);
                    
                    if (btn.url && btn.url.trim() !== '') {
                        window.location.href = btn.url;
                    }
                };
                
                btnContainer.appendChild(button);
            });
            
            messageBox.appendChild(btnContainer);
        }
        
        document.body.appendChild(messageBox);
    }
    
    // 显示用户协议对话框
    function showAgreementDialog(agreementText) {
        // 添加 Markdown 样式
        if (!document.getElementById('markdown-styles')) {
            const style = document.createElement('style');
            style.id = 'markdown-styles';
            style.textContent = `
                .markdown-content h1, .markdown-content h2, .markdown-content h3, 
                .markdown-content h4, .markdown-content h5, .markdown-content h6 {
                    margin-top: 1.5em;
                    margin-bottom: 0.5em;
                    font-weight: 600;
                    color: #1a1a1a;
                }
                .markdown-content h1 { font-size: 1.8em; }
                .markdown-content h2 { font-size: 1.6em; }
                .markdown-content h3 { font-size: 1.4em; }
                .markdown-content h4 { font-size: 1.2em; }
                .markdown-content p { margin: 0.8em 0; }
                .markdown-content ul, .markdown-content ol { padding-left: 2em; margin: 0.8em 0; }
                .markdown-content li { margin: 0.3em 0; }
                .markdown-content blockquote {
                    margin: 1em 0;
                    padding: 0.5em 1em;
                    border-left: 4px solid #ddd;
                    background: #f8f8f8;
                    color: #666;
                }
                .markdown-content code {
                    background: #f0f0f0;
                    padding: 0.2em 0.4em;
                    border-radius: 3px;
                    font-family: monospace;
                    font-size: 0.9em;
                }
                .markdown-content pre {
                    background: #f0f0f0;
                    padding: 1em;
                    border-radius: 5px;
                    overflow-x: auto;
                }
                .markdown-content pre code {
                    background: transparent;
                    padding: 0;
                    border-radius: 0;
                }
                .markdown-content a {
                    color: #0366d6;
                    text-decoration: none;
                }
                .markdown-content a:hover {
                    text-decoration: underline;
                }
                .markdown-content table {
                    border-collapse: collapse;
                    width: 100%;
                    margin: 1em 0;
                }
                .markdown-content th, .markdown-content td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: left;
                }
                .markdown-content tr:nth-child(even) {
                    background-color: #f2f2f2;
                }
            `;
            document.head.appendChild(style);
        }
        
        var agreementBox = document.createElement('div');
        agreementBox.style.position = 'fixed';
        agreementBox.style.top = '50%';
        agreementBox.style.left = '50%';
        agreementBox.style.transform = 'translate(-50%, -50%)';
        agreementBox.style.backgroundColor = 'white';
        agreementBox.style.padding = '30px';
        agreementBox.style.borderRadius = '12px';
        agreementBox.style.boxShadow = '0 4px 20px 0 rgba(0, 0, 0, 0.15)';
        agreementBox.style.zIndex = '1000000';
        agreementBox.style.maxWidth = '800px';
        agreementBox.style.width = '95%';
        agreementBox.style.maxHeight = '85vh';
        agreementBox.style.overflowY = 'auto';
        
        var agreementContent = document.createElement('div');
        agreementContent.classList.add('markdown-content');
        
        // 处理协议内容
        try {
            console.log("接收到的协议内容长度:", agreementText ? agreementText.length : 0);
            console.log("内容前40字符:", agreementText ? agreementText.substring(0, 40) : "空");
            
            // 先尝试HTML实体解码
            let decodedText = agreementText;
            if (agreementText && agreementText.indexOf('&') !== -1 && agreementText.indexOf(';') !== -1) {
                let div = document.createElement('div');
                div.innerHTML = agreementText;
                decodedText = div.textContent;
                console.log("HTML实体解码后前40字符:", decodedText.substring(0, 40));
            }
            
            // 检测常见乱码特征并尝试修复
            if (containsGarbledText(decodedText)) {
                console.log("检测到协议内容包含乱码，尝试修复...");
                decodedText = tryFixGarbledText(decodedText);
            }
            
            // 移除可能影响显示的特殊字符
            let cleanText = (decodedText || "").replace(/\n\n\n/g, '\n\n').trim();
            
            // 使用我们的内置 Markdown 解析器
            let htmlContent = convertMarkdownToHtml(cleanText);
            agreementContent.innerHTML = htmlContent;
            
            // 检查中文字符数量，如果太少可能是乱码没修复好
            let chineseCount = countChineseChars(htmlContent);
            console.log("处理后文本包含中文字符数:", chineseCount);
            
            // 如果中文字符太少（少于10个），或者仍有乱码特征，直接显示原始文本
            if (chineseCount < 10 || containsGarbledText(htmlContent)) {
                console.warn("处理后内容仍不正确，尝试显示纯文本");
                // 最后的尝试：直接显示文本，不做Markdown解析
                let finalText = decodedText || agreementText || "协议内容加载失败";
                agreementContent.innerHTML = '<pre style="white-space: pre-wrap; font-family: inherit;">' + finalText + '</pre>';
            }
        } catch (e) {
            console.error('处理协议内容出错:', e);
            // 如果处理失败，显示纯文本
            let finalText = agreementText || "协议内容加载失败";
            agreementContent.innerHTML = '<pre style="white-space: pre-wrap;">' + finalText + '</pre>';
        }
        
        agreementContent.style.marginBottom = '20px';
        agreementContent.style.lineHeight = '1.6';
        agreementContent.style.color = '#333';
        
        var buttonContainer = document.createElement('div');
        buttonContainer.style.display = 'flex';
        buttonContainer.style.justifyContent = 'space-between';
        
        var disagreeBtn = document.createElement('button');
        disagreeBtn.textContent = '不同意';
        disagreeBtn.style.backgroundColor = '#F56C6C';
        disagreeBtn.style.color = 'white';
        disagreeBtn.style.border = 'none';
        disagreeBtn.style.padding = '12px 20px';
        disagreeBtn.style.borderRadius = '8px';
        disagreeBtn.style.cursor = 'pointer';
        disagreeBtn.style.width = '48%';
        disagreeBtn.style.fontSize = '16px';
        disagreeBtn.style.fontWeight = '500';
        disagreeBtn.style.boxShadow = '0 4px 12px rgba(245, 108, 108, 0.2)';
        disagreeBtn.style.transition = 'all 0.3s';
        
        // 添加悬停效果
        disagreeBtn.onmouseover = function() {
            this.style.backgroundColor = '#f78989';
        };
        disagreeBtn.onmouseout = function() {
            this.style.backgroundColor = '#F56C6C';
        };
        
        disagreeBtn.onclick = function() {
            document.body.removeChild(agreementBox);
            isShowingDialog = false;
            window.location.href = '/';
        };
        
        var agreeBtn = document.createElement('button');
        agreeBtn.textContent = '我已阅读，同意协议';
        agreeBtn.style.backgroundColor = '#67C23A';
        agreeBtn.style.color = 'white';
        agreeBtn.style.border = 'none';
        agreeBtn.style.padding = '12px 20px';
        agreeBtn.style.borderRadius = '8px';
        agreeBtn.style.cursor = 'pointer';
        agreeBtn.style.width = '48%';
        agreeBtn.style.fontSize = '16px';
        agreeBtn.style.fontWeight = '500';
        agreeBtn.style.boxShadow = '0 4px 12px rgba(103, 194, 58, 0.2)';
        agreeBtn.style.transition = 'all 0.3s';
        
        // 添加悬停效果
        agreeBtn.onmouseover = function() {
            this.style.backgroundColor = '#85ce61';
        };
        agreeBtn.onmouseout = function() {
            this.style.backgroundColor = '#67C23A';
        };
        
        agreeBtn.onclick = function() {
            document.body.removeChild(agreementBox);
            isShowingDialog = false;
            if (typeof initMerchantRegister === 'function') {
                initMerchantRegister();
            }
        };
        
        buttonContainer.appendChild(disagreeBtn);
        buttonContainer.appendChild(agreeBtn);
        
        agreementBox.appendChild(agreementContent);
        agreementBox.appendChild(buttonContainer);
        
        document.body.appendChild(agreementBox);
    }
    
    // 添加一个新的简单提示弹窗函数
    function showTipMessage(message) {
        var tipBox = document.createElement('div');
        tipBox.style.position = 'fixed';
        tipBox.style.top = '50%';
        tipBox.style.left = '50%';
        tipBox.style.transform = 'translate(-50%, -50%)';
        tipBox.style.backgroundColor = 'white';
        tipBox.style.padding = '30px';
        tipBox.style.borderRadius = '12px';
        tipBox.style.boxShadow = '0 4px 20px 0 rgba(0, 0, 0, 0.15)';
        tipBox.style.zIndex = '1000000';
        tipBox.style.maxWidth = '500px';
        tipBox.style.width = '90%';
        tipBox.style.textAlign = 'center';
        
        // 添加图标
        var iconDiv = document.createElement('div');
        iconDiv.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="#E6A23C" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></svg>';
        iconDiv.style.marginBottom = '20px';
        
        var messageText = document.createElement('p');
        messageText.textContent = message;
        messageText.style.margin = '0 0 30px 0';
        messageText.style.fontSize = '18px';
        messageText.style.lineHeight = '1.6';
        messageText.style.color = '#333';
        messageText.style.fontWeight = '500';
        
        var closeBtn = document.createElement('button');
        closeBtn.textContent = '确定';
        closeBtn.style.backgroundColor = '#409EFF';
        closeBtn.style.color = 'white';
        closeBtn.style.border = 'none';
        closeBtn.style.padding = '12px 25px';
        closeBtn.style.borderRadius = '8px';
        closeBtn.style.cursor = 'pointer';
        closeBtn.style.width = '180px';
        closeBtn.style.fontSize = '16px';
        closeBtn.style.fontWeight = '500';
        closeBtn.style.boxShadow = '0 4px 12px rgba(64, 158, 255, 0.2)';
        closeBtn.style.transition = 'all 0.3s';
        
        // 添加悬停效果
        closeBtn.onmouseover = function() {
            this.style.backgroundColor = '#66b1ff';
            this.style.boxShadow = '0 6px 16px rgba(64, 158, 255, 0.3)';
        };
        closeBtn.onmouseout = function() {
            this.style.backgroundColor = '#409EFF';
            this.style.boxShadow = '0 4px 12px rgba(64, 158, 255, 0.2)';
        };
        
        closeBtn.onclick = function() {
            document.body.removeChild(tipBox);
        };
        
        tipBox.appendChild(iconDiv);
        tipBox.appendChild(messageText);
        tipBox.appendChild(closeBtn);
        
        document.body.appendChild(tipBox);
    }
})();