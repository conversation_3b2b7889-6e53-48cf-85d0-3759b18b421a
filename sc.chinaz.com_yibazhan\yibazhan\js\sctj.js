document.writeln('<script type="text\/javascript" src="\/\/s4.cnzz.com\/stat.php?id=300636&web_id=300636"><\/scr' + 'ipt>');
var _hmt = _hmt || [];
(function() {
    var hm = document.createElement("script");
    hm.src = "https://hm.baidu.com/hm.js?398913ed58c9e7dfe9695953fb7b6799";
    var s = document.getElementsByTagName("script")[0];
    s.parentNode.insertBefore(hm, s);
})();
(function() {
    var bp = document.createElement('script');
    var curProtocol = window.location.protocol.split(':')[0];
    if (curProtocol === 'https') {
        bp.src = 'https://zz.bdstatic.com/linksubmit/push.js';
    } else {
        bp.src = 'http://push.zhanzhang.baidu.com/push.js';
    }
    var s = document.getElementsByTagName("script")[0];
    s.parentNode.insertBefore(bp, s);
})();
var dlAdHtml = '<div class="dl-left-gg">'
dlAdHtml += '<div></div>'
dlAdHtml += '</div>'
$("body").append(dlAdHtml);


    (function(c,l,a,r,i,t,y){
        c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
        t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
        y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
    })(window, document, "clarity", "script", "kd8vw56cvm");

var vailtitle = document.title;

// 判断<title>是否包含 '776'
if (vailtitle.includes('狙击枪')) {
  // 跳转到百度
  window.location.href = 'https://sc.chinaz.com/404.html';
}

    // 使用 DOMContentLoaded 事件确保 DOM 加载完成后再执行脚本
    document.addEventListener('DOMContentLoaded', function() {
		updateCopyrightYear(".banquan p, #Foot .container p,.TPfooter .TPmain p")
    });
	
	
	function updateCopyrightYear(selectors) {
		// 获取所有匹配的元素
		const elements = document.querySelectorAll(selectors);

		// 获取当前年份
		const currentYear = new Date().getFullYear();

		// 遍历每个匹配的元素
		elements.forEach(element => {
			// 使用正则表达式替换“2002-2024”为“2002-当前年份”
			element.innerHTML = element.innerHTML.replace(/2002-\d{4}/g, `2002-${currentYear}`);
		});
	}