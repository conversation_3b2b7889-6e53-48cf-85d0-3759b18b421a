<?php

use think\facade\Route;

Route::rule('', "index/Index/index");
Route::rule('/', "index/Index/index");

Route::rule('/article', "index/Article/index");
Route::get('/company/faq', 'index/Article/faq');

// 修改订单查询路由
Route::get('/orderinquiries', "index/Orderinquiries/index");
Route::get('/order/card/:trade_no', 'index/Orderinquiries/getCardInfo');  // 修改获取卡密信息的路由
Route::get('/order/info/:trade_no', 'index/Orderinquiries/orderInfo');  // 添加订单详情路由

// 添加商户登录相关路由
Route::get('/merchant/login', 'index/Blackglod.Glodlogin/index');
Route::post('/merchant/login', 'index/Blackglod.Glodlogin/login');
