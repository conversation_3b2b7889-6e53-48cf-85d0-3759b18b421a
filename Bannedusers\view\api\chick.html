<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>违禁和投诉插件配置</title>
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <style>
        .page-container {
            display: flex;
            min-height: 100vh;
        }
        .sidebar {
            width: 200px;
            border-right: 1px solid #dcdfe6;
            padding: 20px 0;
        }
        .main-content {
            flex: 1;
            padding: 20px;
        }
        .el-menu {
            border-right: none;
        }
        .el-form-item-description {
            font-size: 12px;
            color: #909399;
            margin-top: 4px;
        }
        .html-editor {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .preview-content {
            padding: 10px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            min-height: 50px;
            background-color: #f5f7fa;
        }
        
        .preview-content a {
            color: #409EFF;
            text-decoration: none;
        }
        
        .preview-content a:hover {
            text-decoration: underline;
        }
    </style>
</head>

<body>
    <div id="app">
        <div class="page-container">
            <!-- 左侧导航菜单 -->
            <div class="sidebar">
                <el-menu
                    :default-active="activeMenu"
                    @select="handleMenuSelect">
                    <el-menu-item index="basic">
                        <el-icon><Setting /></el-icon>
                        <span>基础设置</span>
                    </el-menu-item>
                    <el-menu-item index="notice">
                        <el-icon><Document /></el-icon>
                        <span>风控公示设置</span>
                    </el-menu-item>
                    <el-menu-item index="risk-levels">
                        <el-icon><Warning /></el-icon>
                        <span>风控等级设置</span>
                    </el-menu-item>
                </el-menu>
            </div>

            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 基础设置 -->
                <el-card v-show="activeMenu === 'basic'" shadow="never">
                    <template #header>
                        <div class="card-header">
                            <span>插件配置</span>
                        </div>
                    </template>
                    <el-form>
                        <el-form-item label="页面模板">
                            <el-radio-group v-model="templateForm.type" @change="handleTemplateChange">
                                <el-radio label="default">默认模板</el-radio>
                                <el-radio label="risk">风控模板</el-radio>
                            </el-radio-group>
                            <div class="el-form-item-description">
                                切换后需要重新添加导航菜单才能生效
                            </div>
                        </el-form-item>

                        <el-form-item>
                            <el-tooltip
                                content="添加或移除导航栏中的'违禁商家'、'商家投诉率'和'买家黑名单'菜单"
                                placement="top">
                                <el-button 
                                    type="primary" 
                                    :loading="loading.nav"
                                    @click="toggleNav">
                                    {{ navExists ? '从导航栏移除' : '添加到导航栏' }}
                                </el-button>
                            </el-tooltip>
                        </el-form-item>
                        <el-form-item>
                            <el-button 
                                type="warning" 
                                :loading="loading.cache"
                                @click="confirmClearCache">
                                清除缓存
                            </el-button>
                        </el-form-item>
                        <el-form-item>
                            <el-button 
                                type="success" 
                                :loading="loading.install"
                                @click="installFloatMenu">
                                安装浮动菜单
                            </el-button>
                            <el-button 
                                type="danger" 
                                :loading="loading.uninstall"
                                @click="uninstallFloatMenu">
                                卸载浮动菜单
                            </el-button>
                        </el-form-item>
                    </el-form>
                </el-card>

                <!-- 风控公示设置 -->
                <el-card v-show="activeMenu === 'notice'" shadow="never">
                    <template #header>
                        <div class="card-header">
                            <span>风控公示内容设置</span>
                        </div>
                    </template>
                    <el-form :model="noticeForm">
                        <el-form-item label="公示内容">
                            <div class="html-editor">
                                <el-input
                                    v-model="noticeForm.content"
                                    type="textarea"
                                    :rows="6"
                                    placeholder="请输入风控公示内容">
                                </el-input>
                                <div class="preview-content" v-html="noticeForm.content"></div>
                            </div>
                            <div class="el-form-item-description">
                                支持HTML标签，可以添加链接和样式。下方为预览效果。
                            </div>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="saveNotice" :loading="loading.notice">
                                保存
                            </el-button>
                        </el-form-item>
                    </el-form>
                </el-card>

                <!-- 风控等级设置 -->
                <el-card v-show="activeMenu === 'risk-levels'" shadow="never">
                    <template #header>
                        <div class="card-header">
                            <span>风控等级设置</span>
                            <el-button type="primary" @click="addRiskLevel" size="small">
                                添加等级
                            </el-button>
                        </div>
                    </template>

                    <el-form>
                        <el-table :data="riskLevels" style="width: 100%">
                            <el-table-column label="等级名称" width="150">
                                <template #default="{ row }">
                                    <el-input v-model="row.name" placeholder="请输入等级名称" />
                                </template>
                            </el-table-column>

                            <el-table-column label="标签颜色" width="120">
                                <template #default="{ row }">
                                    <el-color-picker v-model="row.color" />
                                </template>
                            </el-table-column>

                            <!-- 新增违规内容列 -->
                            <el-table-column label="违规内容" min-width="300">
                                <template #default="{ row }">
                                    <el-input
                                        v-model="row.content"
                                        type="textarea"
                                        :rows="2"
                                        placeholder="请输入该等级对应的违规内容" />
                                </template>
                            </el-table-column>

                            <!-- 新增处罚依据列 -->
                            <el-table-column label="处罚依据" min-width="300">
                                <template #default="{ row }">
                                    <el-input
                                        v-model="row.punishment"
                                        type="textarea"
                                        :rows="2"
                                        placeholder="请输入该等级对应的处罚依据" />
                                </template>
                            </el-table-column>

                            <el-table-column label="预览效果" width="150">
                                <template #default="{ row }">
                                    <el-tag 
                                        :style="{ backgroundColor: row.color, color: '#fff', borderColor: row.color }"
                                        effect="plain">
                                        {{ row.name || '预览效果' }}
                                    </el-tag>
                                </template>
                            </el-table-column>

                            <el-table-column label="操作" width="100" align="center">
                                <template #default="{ $index }">
                                    <el-button 
                                        type="danger" 
                                        size="small" 
                                        @click="removeRiskLevel($index)"
                                        :disabled="riskLevels.length <= 1">
                                        删除
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>

                        <div style="margin-top: 20px;">
                            <el-button 
                                type="primary" 
                                :loading="loading.riskLevels"
                                @click="saveRiskLevels">
                                保存设置
                            </el-button>
                        </div>
                    </el-form>
                </el-card>
            </div>
        </div>
    </div>

    <!-- 引入依赖 -->
    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>

    <script>
        const { createApp, ref, reactive, onMounted } = Vue;
        const { ElMessageBox, ElMessage } = ElementPlus;

        createApp({
            setup() {
                const activeMenu = ref('basic');
                const navExists = ref(false);
                const loading = reactive({
                    nav: false,
                    cache: false,
                    notice: false,
                    riskLevels: false,
                    install: false,
                    uninstall: false
                });
                const noticeForm = reactive({
                    content: ''
                });
                const templateForm = reactive({
                    type: 'default'
                });

                const riskLevels = ref([
                    { 
                        name: '高风险', 
                        color: '#F56C6C',
                        content: '存在严重违规行为，已被系统永久封禁;发布违禁商品，已被系统永久封禁;存在欺诈行为，已被系统永久封禁'
                    },
                    { 
                        name: '中风险', 
                        color: '#E6A23C',
                        content: '存在违规行为，需要进行整改;发布不合规商品，需要整改;商品描述不实，需要整改'
                    },
                    { 
                        name: '低风险', 
                        color: '#67C23A',
                        content: '系统检测到轻微违规行为;商品信息不完整;商品价格异常'
                    }
                ]);

                // 处理菜单选择
                const handleMenuSelect = (index) => {
                    activeMenu.value = index;
                };

                const toggleNav = async () => {
                    loading.nav = true;
                    try {
                        const response = await axios.post('/plugin/Bannedusers/Api/toggleNav');
                        if (response.data.code === 1) {
                            ElMessage.success(response.data.msg);
                            navExists.value = response.data.exists;
                        } else {
                            ElMessage.error(response.data.msg);
                        }
                    } catch (error) {
                        ElMessage.error('操作失败：' + error.message);
                    }
                    loading.nav = false;
                };

                const confirmClearCache = () => {
                    ElMessageBox.confirm(
                        '确定要清除缓存吗？清除后将重新获取最新数据。',
                        '提示',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    ).then(async () => {
                        loading.cache = true;
                        try {
                            const response = await axios.post('/plugin/Bannedusers/Api/clearCache');
                            if (response.data.code === 1) {
                                ElMessage.success(response.data.msg);
                            } else {
                                ElMessage.error(response.data.msg);
                            }
                        } catch (error) {
                            ElMessage.error('清除缓存失败');
                        } finally {
                            loading.cache = false;
                        }
                    }).catch(() => {
                        // 用户点击取消，不做任何操作
                    });
                };

                const saveNotice = async () => {
                    loading.notice = true;
                    try {
                        const response = await axios.post('/plugin/Bannedusers/Api/saveNotice', {
                            content: noticeForm.content
                        });
                        
                        if (response.data.code === 1) {
                            ElMessage.success('保存成功');
                        } else {
                            ElMessage.error(response.data.msg || '保存失败');
                        }
                    } catch (error) {
                        ElMessage.error('保存失败：' + error.message);
                    } finally {
                        loading.notice = false;
                    }
                };

                const getNoticeContent = async () => {
                    try {
                        const response = await axios.get('/plugin/Bannedusers/Api/getNotice');
                        if (response.data.code === 1) {
                            noticeForm.content = response.data.content;
                        }
                    } catch (error) {
                        ElMessage.error('获取风控公示内容失败');
                    }
                };

                const getTemplateType = async () => {
                    try {
                        const response = await axios.get('/plugin/Bannedusers/Api/getTemplate');
                        if (response.data.code === 1) {
                            templateForm.type = response.data.type;
                        }
                    } catch (error) {
                        ElMessage.error('获取模板设置失败');
                    }
                };

                const handleTemplateChange = async (value) => {
                    try {
                        const response = await axios.post('/plugin/Bannedusers/Api/saveTemplate', {
                            type: value
                        });
                        if (response.data.code === 1) {
                            ElMessage.success('模板切换成功，请重新添加导航菜单');
                            // 如果当前有导航菜单，自动移除
                            if (navExists.value) {
                                await toggleNav();
                            }
                        } else {
                            ElMessage.error(response.data.msg);
                            // 如果保存失败，恢复之前的选择
                            templateForm.type = response.data.currentType;
                        }
                    } catch (error) {
                        ElMessage.error('保存失败');
                        // 发生错误时也恢复之前的选择
                        getTemplateType();
                    }
                };

                const addRiskLevel = () => {
                    riskLevels.value.push({
                        name: '',
                        color: '#409EFF',
                        content: ''
                    });
                };

                const removeRiskLevel = (index) => {
                    riskLevels.value.splice(index, 1);
                };

                const getRiskLevels = async () => {
                    try {
                        const response = await axios.get('/plugin/Bannedusers/Api/getRiskLevels');
                        if (response.data.code === 1) {
                            riskLevels.value = response.data.levels;
                        }
                    } catch (error) {
                        console.error('获取风控等级配置失败:', error);
                        ElementPlus.ElMessage.error('获取配置失败');
                    }
                };

                const saveRiskLevels = async () => {
                    loading.riskLevels = true;
                    try {
                        // 验证数据
                        if (riskLevels.value.some(level => !level.name.trim())) {
                            ElementPlus.ElMessage.error('等级名称不能为空');
                            return;
                        }

                        const response = await axios.post('/plugin/Bannedusers/Api/saveRiskLevels', {
                            levels: riskLevels.value
                        });

                        if (response.data.code === 1) {
                            ElementPlus.ElMessage.success('保存成功');
                        } else {
                            ElementPlus.ElMessage.error(response.data.msg || '保存失败');
                        }
                    } catch (error) {
                        console.error('保存风控等级配置失败:', error);
                        ElementPlus.ElMessage.error('保存失败');
                    } finally {
                        loading.riskLevels = false;
                    }
                };

                const checkNavStatus = async () => {
                    try {
                        const response = await axios.get('/plugin/Bannedusers/Api/checkNavStatus');
                        if (response.data.code === 1) {
                            navExists.value = response.data.exists;
                        }
                    } catch (error) {
                        console.error('获取导航状态失败:', error);
                    }
                };

                const installFloatMenu = async () => {
                    loading.install = true;
                    try {
                        const response = await axios.post('/plugin/Bannedusers/Api/installFloatMenu');
                        if (response.data.code === 1) {
                            ElMessage.success(response.data.msg);
                        } else {
                            ElMessage.error(response.data.msg);
                        }
                    } catch (error) {
                        ElMessage.error('安装失败：' + error.message);
                    } finally {
                        loading.install = false;
                    }
                };

                const uninstallFloatMenu = async () => {
                    loading.uninstall = true;
                    try {
                        const response = await axios.post('/plugin/Bannedusers/Api/uninstallFloatMenu');
                        if (response.data.code === 1) {
                            ElMessage.success(response.data.msg);
                        } else {
                            ElMessage.error(response.data.msg);
                        }
                    } catch (error) {
                        ElMessage.error('卸载失败：' + error.message);
                    } finally {
                        loading.uninstall = false;
                    }
                };

                onMounted(() => {
                    checkNavStatus();
                    getNoticeContent();
                    getTemplateType();
                    getRiskLevels();
                });

                return {
                    activeMenu,
                    navExists,
                    loading,
                    noticeForm,
                    handleMenuSelect,
                    toggleNav,
                    confirmClearCache,
                    saveNotice,
                    templateForm,
                    handleTemplateChange,
                    riskLevels,
                    addRiskLevel,
                    removeRiskLevel,
                    saveRiskLevels,
                    installFloatMenu,
                    uninstallFloatMenu
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
