<?php

namespace plugin\Buyer\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Log;
use think\facade\Db;

class Api extends BasePlugin {

    protected $scene = ['admin'];  // 只允许管理员访问
    
    protected $noNeedLogin = [];

    // 管理面板首页
    public function index() {
        return View::fetch();
    }

    // 获取配置
    public function getConfig() {
        try {
            $configFile = app()->getRootPath() . 'plugin/Buyer/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            // 确保返回必要的配置项
            if (!isset($config['buyer_config'])) {
                $config['buyer_config'] = [
                    'status' => true,
                    'need_password' => false,
                    'password' => '',
                    'mask_contact' => true  // 添加联系方式脱敏开关
                ];
            }

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $config['buyer_config']
            ]);
        } catch (\Exception $e) {
            Log::error('获取配置失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取配置失败：' . $e->getMessage()]);
        }
    }

    // 保存配置
    public function saveConfig() {
        try {
            $data = [
                'status' => input('status/b', true),
                'need_password' => input('need_password/b', false),
                'password' => input('password/s', ''),
                'mask_contact' => input('mask_contact/b', true)  // 添加联系方式脱敏开关
            ];
            
            // 读取配置文件
            $configFile = app()->getRootPath() . 'plugin/Buyer/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            // 更新配置
            $config['buyer_config'] = $data;
            
            // 保存配置
            if (!file_put_contents($configFile, "<?php\nreturn " . var_export($config, true) . ";\n")) {
                throw new \Exception('保存配置失败');
            }
            
            return json(['code' => 200, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            Log::error('保存配置失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }
} 