<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <meta http-equiv="Cache-Control" content="max-age=31536000">
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>自动封禁设置</title>
    <style>
        /* 加载动画样式 */
        #loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #fff;
            z-index: 9999;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(0, 0, 0, 0.1);
            border-top-color: #3498db;
            border-radius: 50%;
            animation: spin 1s ease infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .page-container {
            display: flex;
            min-height: 100vh;
        }
        .sidebar {
            width: 200px;
            border-right: 1px solid #dcdfe6;
            padding: 20px 0;
        }
        .main-content {
            flex: 1;
            padding: 20px;
        }

        .suggestion-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 0;
        }
    </style>
</head>

<body>
<!-- 加载动画容器 -->
<div id="loading">
    <div class="spinner"></div>
</div>

<!-- Vue 应用挂载点 -->
<div id="app" style="display: none">
    <div class="page-container">
        <!-- 左侧导航栏 -->
        <div class="sidebar">
            <el-menu
                :default-active="activeMenu"
                @select="handleMenuSelect">
                <el-menu-item index="complaint">
                    <span>投诉率封禁</span>
                </el-menu-item>
                <el-menu-item index="order">
                    <span>无流水封禁</span>
                </el-menu-item>
                <el-menu-item index="timeout">
                    <span>删除封禁超时用户</span>
                </el-menu-item>
                <el-menu-item index="whitelist">
                    <span>解封白名单</span>
                </el-menu-item>
                <el-menu-item index="unauth">
                    <span>未实名封禁</span>
                </el-menu-item>
                <el-menu-item index="scene">
                    <span>场景封禁</span>
                </el-menu-item>
            </el-menu>
        </div>

        <!-- 主要内容区 -->
        <div class="main-content">
            <!-- 投诉率封禁配置 -->
            <el-card v-show="activeMenu === 'complaint'" shadow="never">
                <el-form :model="complaintForm" label-width="120px">
                    <el-form-item label="投诉率封禁：">
                        <el-radio-group v-model="complaintForm.status">
                            <el-radio :label="0">关闭</el-radio>
                            <el-radio :label="1">开启</el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <el-form-item label="封禁类型：">
                        <el-radio-group v-model="complaintForm.type">
                            <el-radio label="rate">按投诉率</el-radio>
                            <el-radio label="count">按投诉数量</el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <!-- 投诉率设置 -->
                    <template v-if="complaintForm.type === 'rate'">
                        <el-form-item label="投诉率阈值：">
                            <el-input-number 
                                v-model="complaintForm.rateThreshold" 
                                :min="1" 
                                :max="100"
                                :step="1">
                                <template #suffix>%</template>
                            </el-input-number>
                            <div class="el-form-item-description">
                                当用户投诉率超过此值时将自动封禁（默认50%）
                            </div>
                        </el-form-item>

                        <!-- 新增：最小订单数设置 -->
                        <el-form-item label="最小订单数：">
                            <el-input-number 
                                v-model="complaintForm.minOrders" 
                                :min="1" 
                                :max="100"
                                :step="1">
                                <template #suffix>单</template>
                            </el-input-number>
                            <div class="el-form-item-description">
                                只有订单数达到此数量才会计算投诉率（默认5单）
                            </div>
                        </el-form-item>

                        <el-form-item label="(风控模板专属)封禁提示内容：">
                            <el-input 
                                v-model="complaintForm.rateContent" 
                                type="textarea" 
                                :rows="3"
                                placeholder="请输入按投诉率封禁时的提示内容">
                            </el-input>
                            <div class="el-form-item-description">
                                可使用 {threshold} 作为阈值占位符
                            </div>
                        </el-form-item>
                        
                        <el-form-item label="封禁时长：">
                            <el-select v-model="complaintForm.expireTime" placeholder="请选择封禁时长">
                                <el-option label="使用默认" :value="0"></el-option>
                                <el-option label="永久" :value="4102415999"></el-option>
                                <el-option label="1天" :value="86400"></el-option>
                                <el-option label="3天" :value="259200"></el-option>
                                <el-option label="7天" :value="604800"></el-option>
                                <el-option label="14天" :value="1209600"></el-option>
                                <el-option label="1个月(30天)" :value="2592000"></el-option>
                                <el-option label="2个月(60天)" :value="5184000"></el-option>
                                <el-option label="3个月(90天)" :value="7776000"></el-option>
                            </el-select>
                            <div class="el-form-item-description">
                                设置投诉率封禁时长，选择"使用默认"将使用全局设置的默认封禁时长
                            </div>
                        </el-form-item>
                    </template>

                    <!-- 投诉数量设置 -->
                    <template v-else>
                        <el-form-item label="投诉数量：">
                            <el-input-number 
                                v-model="complaintForm.countThreshold" 
                                :min="1" 
                                :max="999"
                                :step="1">
                                <template #suffix>次</template>
                            </el-input-number>
                            <div class="el-form-item-description">
                                当用户投诉次数超过此值时将自动封禁（默认5次）
                            </div>
                        </el-form-item>

                        <el-form-item label="(风控模板专属)封禁提示内容：">
                            <el-input 
                                v-model="complaintForm.countContent" 
                                type="textarea" 
                                :rows="3"
                                placeholder="请输入按投诉数量封禁时的提示内容">
                            </el-input>
                            <div class="el-form-item-description">
                                可使用 {threshold} 作为阈值占位符
                            </div>
                        </el-form-item>
                        
                        <el-form-item label="封禁时长：">
                            <el-select v-model="complaintForm.expireTime" placeholder="请选择封禁时长">
                                <el-option label="使用默认" :value="0"></el-option>
                                <el-option label="永久" :value="4102415999"></el-option>
                                <el-option label="1天" :value="86400"></el-option>
                                <el-option label="3天" :value="259200"></el-option>
                                <el-option label="7天" :value="604800"></el-option>
                                <el-option label="14天" :value="1209600"></el-option>
                                <el-option label="1个月(30天)" :value="2592000"></el-option>
                                <el-option label="2个月(60天)" :value="5184000"></el-option>
                                <el-option label="3个月(90天)" :value="7776000"></el-option>
                            </el-select>
                            <div class="el-form-item-description">
                                设置投诉数量封禁时长，选择"使用默认"将使用全局设置的默认封禁时长
                            </div>
                        </el-form-item>
                    </template>

                    <el-form-item>
                        <el-button type="primary" @click="saveComplaint" :loading="isLoading">
                            保存
                        </el-button>
                    </el-form-item>
                </el-form>
            </el-card>

            <!-- 无流水封禁配置 -->
            <el-card v-show="activeMenu === 'order'" shadow="never">
                <el-form :model="orderForm" label-width="120px">
                    <el-form-item label="无流水封禁：">
                        <el-radio-group v-model="orderForm.status">
                            <el-radio :label="0">关闭</el-radio>
                            <el-radio :label="1">开启</el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <el-form-item label="封禁类型：">
                        <el-checkbox-group v-model="orderForm.banTypes">
                            <el-checkbox label="no_order">无订单封禁</el-checkbox>
                            <el-checkbox label="no_success">无成功订单封禁</el-checkbox>
                        </el-checkbox-group>
                        <div class="el-form-item-description">
                            可同时选择多个封禁类型
                        </div>
                    </el-form-item>

                    <el-form-item label="时间范围类型">
                        <el-radio-group v-model="orderForm.timeRangeType">
                            <el-radio label="days">检查时间范围（天）</el-radio>
                            <el-radio label="exact">精确时间范围</el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <!-- 根据选择显示不同的时间范围输入 -->
                    <el-form-item v-if="orderForm.timeRangeType === 'days'" label="检查天数">
                        <el-input-number 
                            v-model="orderForm.days" 
                            :min="1" 
                            :max="365"
                            placeholder="请输入天数">
                        </el-input-number>
                        <span class="tip">1-365天</span>
                    </el-form-item>

                    <template v-if="orderForm.timeRangeType === 'exact'">
                        <el-form-item label="开始时间">
                            <el-date-picker
                                v-model="orderForm.startTime"
                                type="date"
                                placeholder="选择开始日期"
                                format="YYYY-MM-DD"
                                value-format="YYYY-MM-DD"
                                :disabled-date="disabledStartDate">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="结束时间">
                            <el-date-picker
                                v-model="orderForm.endTime"
                                type="date"
                                placeholder="选择结束日期"
                                format="YYYY-MM-DD"
                                value-format="YYYY-MM-DD"
                                :disabled-date="disabledEndDate">
                            </el-date-picker>
                        </el-form-item>
                    </template>

                    <el-form-item label="(风控模板专属)封禁提示内容：">
                        <el-input 
                            v-model="orderForm.banContent" 
                            type="textarea" 
                            :rows="3"
                            placeholder="请输入自动封禁时的提示内容">
                        </el-input>
                        <div class="el-form-item-description">
                            可使用 {days} 作为天数占位符
                        </div>
                    </el-form-item>

                    <el-form-item label="封禁时长：">
                        <el-select v-model="orderForm.expireTime" placeholder="请选择封禁时长">
                            <el-option label="使用默认" :value="0"></el-option>
                            <el-option label="永久" :value="4102415999"></el-option>
                            <el-option label="1天" :value="86400"></el-option>
                            <el-option label="3天" :value="259200"></el-option>
                            <el-option label="7天" :value="604800"></el-option>
                            <el-option label="14天" :value="1209600"></el-option>
                            <el-option label="1个月(30天)" :value="2592000"></el-option>
                            <el-option label="2个月(60天)" :value="5184000"></el-option>
                            <el-option label="3个月(90天)" :value="7776000"></el-option>
                        </el-select>
                        <div class="el-form-item-description">
                            设置无流水封禁时长，选择"使用默认"将使用全局设置的默认封禁时长
                        </div>
                    </el-form-item>

                    <el-form-item label="排除新用户天数：">
                        <el-input-number 
                            v-model="orderForm.excludeNewUserDays" 
                            :min="0" 
                            :max="365"
                            placeholder="请输入天数">
                        </el-input-number>
                        <div class="el-form-item-description">
                            注册天数少于此值的用户将不会被自动封禁（0-365天，默认7天）
                        </div>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="saveOrder" :loading="isLoading">
                            保存
                        </el-button>
                    </el-form-item>
                </el-form>
            </el-card>

            <!-- 删除封禁超时用户配置 -->
            <el-card v-show="activeMenu === 'timeout'" shadow="never">
                <el-form :model="timeoutForm" label-width="120px">
                    <el-form-item label="自动删除：">
                        <el-radio-group v-model="timeoutForm.status">
                            <el-radio :label="0">关闭</el-radio>
                            <el-radio :label="1">开启</el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <el-form-item label="封禁超时天数：">
                        <el-input-number 
                            v-model="timeoutForm.days" 
                            :min="1" 
                            :max="365"
                            placeholder="请输入天数">
                        </el-input-number>
                        <div class="el-form-item-description">
                            超过设定天数的封禁用户将被自动删除（1-365天）
                        </div>
                    </el-form-item>

                    <el-form-item label="批量处理：">
                        <el-switch v-model="timeoutForm.batchProcessing"></el-switch>
                        <div class="el-form-item-description">
                            开启后将分批处理用户数据，避免服务器过载
                        </div>
                    </el-form-item>

                    <el-form-item label="每批处理数量：" v-show="timeoutForm.batchProcessing">
                        <el-input-number 
                            v-model="timeoutForm.batchSize" 
                            :min="5" 
                            :max="500"
                            placeholder="请输入每批处理数量">
                        </el-input-number>
                        <div class="el-form-item-description">
                            每次处理的用户数量（5-500，建议100以内）
                        </div>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="saveTimeout" :loading="isLoading">
                            保存
                        </el-button>
                        <el-button type="danger" @click="handleManualDelete" :loading="isLoading">
                            立即执行删除
                        </el-button>
                    </el-form-item>
                </el-form>
            </el-card>

            <!-- 解封白名单配置 -->
            <el-card v-show="activeMenu === 'whitelist'" shadow="never">
                <el-form :model="whitelistForm" label-width="120px">
                    <el-form-item label="用户ID：">
                        <el-autocomplete
                            v-model="whitelistForm.user_id"
                            :fetch-suggestions="queryUserIds"
                            placeholder="请输入要解封的用户ID"
                            :trigger-on-focus="false"
                            @select="handleUserIdSelect"
                            clearable>
                            <template #default="{ item }">
                                <div class="suggestion-item">
                                    <span>ID: {{ item.id }}</span>
                                    <small style="color: #999">({{ item.username }})</small>
                                </div>
                            </template>
                        </el-autocomplete>
                    </el-form-item>

                    <el-form-item label="白名单天数：">
                        <el-input-number 
                            v-model="whitelistForm.whitelist_days" 
                            :min="1" 
                            :max="365"
                            placeholder="请输入天数">
                        </el-input-number>
                        <div class="el-form-item-description">
                            解封后将加入白名单的天数（1-365天）
                        </div>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="handleWhitelist" :loading="isLoading">
                            解封并加白名单
                        </el-button>
                    </el-form-item>
                </el-form>

                <!-- 添加封禁时间选择 -->
                <el-divider>封禁时间设置</el-divider>
                <el-form :model="banTimeForm" label-width="120px">
                    <el-form-item label="默认封禁时长：">
                        <el-select v-model="banTimeForm.expire_time" placeholder="请选择封禁时长">
                            <el-option label="永久" :value="4102415999"></el-option>
                            <el-option label="1天" :value="86400"></el-option>
                            <el-option label="3天" :value="259200"></el-option>
                            <el-option label="7天" :value="604800"></el-option>
                            <el-option label="14天" :value="1209600"></el-option>
                            <el-option label="1个月(30天)" :value="2592000"></el-option>
                            <el-option label="2个月(60天)" :value="5184000"></el-option>
                            <el-option label="3个月(90天)" :value="7776000"></el-option>
                        </el-select>
                        <div class="el-form-item-description">
                            设置新封禁用户的默认封禁时长
                        </div>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="saveBanTime" :loading="isLoading">
                            保存
                        </el-button>
                    </el-form-item>
                </el-form>

                <!-- 添加白名单列表 -->
                <el-divider>白名单列表</el-divider>
                <el-table :data="whitelistData" style="width: 100%" v-loading="tableLoading">
                    <el-table-column label="白名单信息" min-width="600">
                        <template #default="scope">
                            <div style="display: flex; align-items: center; gap: 20px;">
                                <el-tag type="info" effect="plain" style="min-width: 120px;">
                                    {{ scope.row.username }}
                                </el-tag>
                                <span style="color: #606266; min-width: 180px;">
                                    <i class="el-icon-time"></i>
                                    加入：{{ scope.row.create_time }}
                                </span>
                                <span style="color: #606266; min-width: 180px;">
                                    <i class="el-icon-timer"></i>
                                    过期：{{ scope.row.expire_time }}
                                </span>
                                <el-tag :type="scope.row.status === '有效' ? 'success' : 'danger'" 
                                        size="small"
                                        effect="dark">
                                    {{ scope.row.status }}
                                </el-tag>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="100" align="right">
                        <template #default="scope">
                            <el-button 
                                type="danger" 
                                size="small"
                                :icon="Delete"
                                circle
                                @click="handleRemoveWhitelist(scope.row)"
                                :disabled="scope.row.status === '已过期'">
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>

            <!-- 添加未实名封禁配置 -->
            <el-card v-show="activeMenu === 'unauth'" shadow="never">
                <el-form :model="unauthForm" label-width="120px">
                    <el-form-item label="未实名封禁：">
                        <el-radio-group v-model="unauthForm.status">
                            <el-radio :label="0">关闭</el-radio>
                            <el-radio :label="1">开启</el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <el-form-item label="无交易天数：">
                        <el-input-number 
                            v-model="unauthForm.days" 
                            :min="1" 
                            :max="365"
                            placeholder="请输入天数">
                        </el-input-number>
                        <div class="el-form-item-description">
                            超过设定天数无交易的未实名用户将被自动封禁（1-365天）
                        </div>
                    </el-form-item>

                    <el-form-item label="封禁提示内容：">
                        <el-input 
                            v-model="unauthForm.banContent" 
                            type="textarea" 
                            :rows="3"
                            placeholder="请输入未实名封禁时的提示内容">
                        </el-input>
                    </el-form-item>

                    <el-form-item label="封禁时长：">
                        <el-select v-model="unauthForm.expireTime" placeholder="请选择封禁时长">
                            <el-option label="使用默认" :value="0"></el-option>
                            <el-option label="永久" :value="4102415999"></el-option>
                            <el-option label="1天" :value="86400"></el-option>
                            <el-option label="3天" :value="259200"></el-option>
                            <el-option label="7天" :value="604800"></el-option>
                            <el-option label="14天" :value="1209600"></el-option>
                            <el-option label="1个月(30天)" :value="2592000"></el-option>
                            <el-option label="2个月(60天)" :value="5184000"></el-option>
                            <el-option label="3个月(90天)" :value="7776000"></el-option>
                        </el-select>
                        <div class="el-form-item-description">
                            设置未实名封禁时长，选择"使用默认"将使用全局设置的默认封禁时长
                        </div>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="saveUnauth" :loading="isLoading">
                            保存
                        </el-button>
                        <el-button type="danger" @click="handleManualUnauth" :loading="isLoading">
                            立即执行封禁
                        </el-button>
                    </el-form-item>
                </el-form>
            </el-card>

            <!-- 添加场景封禁配置卡片 -->
            <el-card v-show="activeMenu === 'scene'" shadow="never">
                <el-form :model="sceneForm" label-width="120px">
                    <el-form-item label="启用状态：">
                        <el-switch v-model="sceneForm.status"></el-switch>
                    </el-form-item>

                    <!-- 添加封禁开关 -->
                    <el-form-item label="启用封禁：">
                        <el-switch v-model="sceneForm.banStatus" :disabled="!sceneForm.status"></el-switch>
                        <div class="el-form-item-description">
                            开启后将自动封禁超时未认证用户，关闭则只进行检测
                        </div>
                    </el-form-item>

                    <!-- 添加封禁时间设置 -->
                    <el-form-item label="封禁时间：" v-show="sceneForm.banStatus && sceneForm.status">
                        <el-input-number 
                            v-model="sceneForm.banMinutes" 
                            :min="1" 
                            :max="365"
                            :disabled="!sceneForm.status"
                            placeholder="请输入天数">
                        </el-input-number>
                        <div class="el-form-item-description">
                            未认证用户超过设定天数将被自动封禁（1-365天）
                        </div>
                    </el-form-item>

                    <!-- 添加删除开关 -->
                    <el-form-item label="启用删除：">
                        <el-switch v-model="sceneForm.deleteStatus" :disabled="!sceneForm.status"></el-switch>
                        <div class="el-form-item-description">
                            开启后将自动删除超时未认证用户，关闭则只进行封禁
                        </div>
                    </el-form-item>

                    <el-form-item label="删除时间：" v-show="sceneForm.deleteStatus && sceneForm.status">
                        <el-input-number 
                            v-model="sceneForm.deleteMinutes" 
                            :min="1" 
                            :max="365"
                            :disabled="!sceneForm.status"
                            placeholder="请输入天数">
                        </el-input-number>
                        <div class="el-form-item-description">
                            未认证用户超过设定天数将被自动删除（1-365天）
                        </div>
                    </el-form-item>

                    <el-form-item label="场景内容匹配：">
                        <el-input
                            v-model="sceneForm.sceneContent"
                            type="textarea"
                            :rows="3"
                            placeholder="请输入需要匹配的场景内容，多个内容用|分隔">
                        </el-input>
                        <div class="el-form-item-description">
                            多个匹配内容请用|分隔，例如：场景1|场景2|场景3
                        </div>
                    </el-form-item>

                    <el-form-item label="场景网站匹配：">
                        <el-input
                            v-model="sceneForm.sceneWebsite"
                            type="textarea"
                            :rows="3"
                            placeholder="请输入需要匹配的场景网站，多个网站用|分隔">
                        </el-input>
                        <div class="el-form-item-description">
                            多个匹配网站请用|分隔，例如：网站1|网站2|网站3
                        </div>
                    </el-form-item>

                    <el-form-item label="封禁提示内容：">
                        <el-input 
                            v-model="sceneForm.banContent" 
                            type="textarea" 
                            :rows="3"
                            placeholder="请输入场景封禁时的提示内容">
                        </el-input>
                    </el-form-item>

                    <el-form-item label="封禁时长：">
                        <el-select v-model="sceneForm.expireTime" placeholder="请选择封禁时长">
                            <el-option label="使用默认" :value="0"></el-option>
                            <el-option label="永久" :value="4102415999"></el-option>
                            <el-option label="1天" :value="86400"></el-option>
                            <el-option label="3天" :value="259200"></el-option>
                            <el-option label="7天" :value="604800"></el-option>
                            <el-option label="14天" :value="1209600"></el-option>
                            <el-option label="1个月(30天)" :value="2592000"></el-option>
                            <el-option label="2个月(60天)" :value="5184000"></el-option>
                            <el-option label="3个月(90天)" :value="7776000"></el-option>
                        </el-select>
                        <div class="el-form-item-description">
                            设置该场景下封禁的时长，选择"使用默认"将使用全局设置的默认封禁时长
                        </div>
                    </el-form-item>

                    <el-form-item label="检查已实名用户：">
                        <el-switch v-model="sceneForm.checkAuthUsers"></el-switch>
                        <div class="el-form-item-description">
                            启用后，场景内容和网站检查将同时适用于已实名认证的用户
                        </div>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="saveScene" :loading="isLoading">
                            保存
                        </el-button>
                        <el-button type="danger" @click="handleManualScene" :loading="isLoading">
                            立即执行检查
                        </el-button>
                    </el-form-item>
                </el-form>
            </el-card>
        </div>
    </div>
</div>

<script src="/static/others/vue/vue.global.prod.js"></script>
<script src="/static/others/element-plus/index.full.min.js"></script>
<script src="/static/others/axios/axios.min.js"></script>

<script type="module">
    const { ref, reactive, onMounted, watch } = Vue;
    const { ElMessage, ElMessageBox } = ElementPlus;

    const app = Vue.createApp({
        setup() {
            const isLoading = ref(false);
            const activeMenu = ref('complaint'); // 默认显示投诉率封禁

            const complaintForm = reactive({
                status: 0,
                type: 'rate',
                rateThreshold: 50,
                countThreshold: 5,
                minOrders: 5,
                rateContent: '您的账号因投诉率超过{threshold}%，已被系统自动封禁。如有疑问，请联系客服处理。',
                countContent: '您的账号因投诉次数超过{threshold}次，已被系统自动封禁。如有疑问，请联系客服处理。',
                expireTime: 0
            });

            const orderForm = reactive({
                status: 0,
                timeRangeType: 'days', // 默认使用天数范围
                days: 30,
                startTime: '',
                endTime: '',
                banContent: '',
                banTypes: [],
                excludeNewUserDays: 7,  // 添加新字段，默认值为7天
                expireTime: 0
            });

            const timeoutForm = reactive({
                status: 0,
                days: 30,
                batchProcessing: false,
                batchSize: 100
            });

            const unbanForm = reactive({
                username: '',
                whitelist_days: 30
            });

            const whitelistForm = reactive({
                user_id: '',  // 改为 user_id
                whitelist_days: 30
            });

            const whitelistData = ref([]);
            const tableLoading = ref(false);

            const unauthForm = reactive({
                status: 0,
                days: 7,  // 默认7天
                banContent: '您的账号未完成实名认证且长期无交易，已被系统自动封禁。如有疑问，请联系客服处理。',
                expireTime: 0
            });

            const sceneForm = reactive({
                status: false,
                banStatus: false,
                deleteStatus: false,
                banMinutes: 30,  // 添加封禁时间字段
                deleteMinutes: 30,
                sceneContent: '',
                sceneWebsite: '',
                banContent: '您的账号因场景内容违规，已被系统自动封禁。',
                checkAuthUsers: false,
                expireTime: 0
            });

            // 添加封禁时间配置对象
            const banTimeForm = reactive({
                expire_time: 4102415999  // 默认永久封禁
            });

            // 添加对场景封禁总开关的监听
            watch(() => sceneForm.status, (newValue) => {
                if (!newValue) {
                    // 当总开关关闭时，自动关闭封禁和删除功能
                    sceneForm.banStatus = false;
                    sceneForm.deleteStatus = false;
                }
            });

            const shortcuts = [
                {
                    text: '最近一周',
                    value: () => {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        return [start, end];
                    }
                },
                {
                    text: '最近一个月',
                    value: () => {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        return [start, end];
                    }
                },
                {
                    text: '最近三个月',
                    value: () => {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        return [start, end];
                    }
                }
            ];

            const fetchData = async () => {
                try {
                    isLoading.value = true;
                    const res = await axios.post("/plugin/Autoban/api/fetchData");
                    if (res.data?.code === 200) {
                        const data = res.data.data;
                        
                        // 投诉率配置
                        complaintForm.status = data.complaint_status ?? 0;
                        complaintForm.type = data.complaint_type ?? 'rate';
                        complaintForm.rateThreshold = data.complaint_rate_threshold ?? 50;
                        complaintForm.countThreshold = data.complaint_count_threshold ?? 5;
                        complaintForm.minOrders = data.complaint_min_orders ?? 5;
                        complaintForm.rateContent = data.complaint_rate_content;
                        complaintForm.countContent = data.complaint_count_content;
                        complaintForm.expireTime = data.complaint_expire_time ?? 0;
                        // 无流水配置
                        orderForm.status = data.order_status ?? 0;
                        orderForm.days = data.order_days ?? 30;
                        orderForm.banContent = data.order_ban_content;
                        orderForm.banTypes = data.order_ban_types?.split(',').filter(Boolean) || [];
                        orderForm.startTime = data.order_start_time || '';
                        orderForm.endTime = data.order_end_time || '';
                        orderForm.excludeNewUserDays = data.exclude_new_user_days ?? 7;  // 添加新数据赋值
                        orderForm.expireTime = data.order_expire_time ?? 0;
                        // 删除封禁超时用户配置
                        timeoutForm.status = data.timeout_status ?? 0;
                        timeoutForm.days = data.timeout_days ?? 30;
                        timeoutForm.batchProcessing = Boolean(data.timeout_batch_processing ?? 0);
                        timeoutForm.batchSize = data.timeout_batch_size ?? 100;
                        // 未实名配置
                        unauthForm.status = data.unauth_status ?? 0;
                        unauthForm.days = data.unauth_days ?? 7;  // 确保正确加载未实名天数配置
                        unauthForm.banContent = data.unauth_ban_content ?? '您的账号未完成实名认证，已被系统自动封禁。';
                        unauthForm.expireTime = data.unauth_expire_time ?? 0;
                        // 场景封禁配置加载 - 确保转换为布尔值
                        Object.assign(sceneForm, {
                            status: Boolean(data.scene_status),
                            banStatus: Boolean(data.scene_ban_status),
                            deleteStatus: Boolean(data.scene_delete_status),
                            sceneContent: data.scene_content,
                            sceneWebsite: data.scene_website,
                            banContent: data.scene_ban_content,
                            deleteMinutes: data.scene_delete_minutes,
                            banMinutes: data.scene_ban_minutes,
                            checkAuthUsers: Boolean(data.scene_check_auth_users),
                            expireTime: data.scene_ban_expire_time ?? 0
                        });
                        
                        // 封禁时间配置
                        banTimeForm.expire_time = data.default_ban_expire_time ?? 4102415999;
                    }
                } catch (error) {
                    ElMessage.error('加载配置失败');
                } finally {
                    isLoading.value = false;
                }
            };

            const saveComplaint = async () => {
                isLoading.value = true;
                try {
                    const res = await axios.post("/plugin/Autoban/api/saveComplaint", complaintForm);
                    if (res.data?.code === 200) {
                        ElMessage.success('保存成功');
                    } else {
                        ElMessage.error(res.data?.msg || '保存失败');
                    }
                } catch (error) {
                    ElMessage.error(error.message || '保存失败');
                } finally {
                    isLoading.value = false;
                }
            };

            const saveOrder = async () => {
                isLoading.value = true;
                try {
                    if (orderForm.timeRangeType === 'exact') {
                        if (!orderForm.startTime || !orderForm.endTime) {
                            ElMessage.error('请选择完整的时间范围');
                            return;
                        }
                        if (new Date(orderForm.startTime) > new Date(orderForm.endTime)) {
                            ElMessage.error('开始时间不能大于结束时间');
                            return;
                        }
                        // 清空天数
                        orderForm.days = 0;
                    } else {
                        // 清空精确时间范围
                        orderForm.startTime = '';
                        orderForm.endTime = '';
                    }
                    const res = await axios.post("/plugin/Autoban/api/saveOrder", orderForm);
                    if (res.data?.code === 200) {
                        ElMessage.success('保存成功');
                    } else {
                        ElMessage.error(res.data?.msg || '保存失败');
                    }
                } catch (error) {
                    ElMessage.error(error.message || '保存失败');
                } finally {
                    isLoading.value = false;
                }
            };

            const saveTimeout = async () => {
                if (!timeoutForm.days || timeoutForm.days < 1 || timeoutForm.days > 365) {
                    ElMessage.error('天数必须在1-365之间');
                    return;
                }

                if (timeoutForm.batchProcessing && (timeoutForm.batchSize < 5 || timeoutForm.batchSize > 500)) {
                    ElMessage.error('每批处理数量必须在5-500之间');
                    return;
                }

                isLoading.value = true;
                try {
                    const res = await axios.post("/plugin/Autoban/api/saveTimeout", {
                        status: timeoutForm.status,
                        days: timeoutForm.days,
                        batchProcessing: timeoutForm.batchProcessing ? 1 : 0,
                        batchSize: timeoutForm.batchSize
                    });
                    
                    if (res.data?.code === 200) {
                        ElMessage.success('保存成功');
                    } else {
                        ElMessage.error(res.data?.msg || '保存失败');
                    }
                } catch (error) {
                    ElMessage.error(error.message || '保存失败');
                } finally {
                    isLoading.value = false;
                }
            };

            const handleManualDelete = async () => {
                try {
                    let warningMessage = '确定要立即执行删除操作吗？此操作将删除所有超时封禁用户。';
                    if (timeoutForm.batchProcessing) {
                        warningMessage += `\n\n已开启批量处理，每批将处理 ${timeoutForm.batchSize} 个用户。`;
                    }
                    
                    await ElMessageBox.confirm(
                        warningMessage,
                        '警告',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    );
                    
                    isLoading.value = true;
                    const res = await axios.post("/plugin/Autoban/api/deleteTimeoutUsers", {
                        batchProcessing: timeoutForm.batchProcessing ? 1 : 0,
                        batchSize: timeoutForm.batchSize
                    });
                    
                    // 输出详细信息到控制台
                    console.group('删除封禁超时用户操作结果');
                    console.log('状态码:', res.data?.code);
                    console.log('返回数据:', res.data);
                    console.groupEnd();

                    // 成功情况
                    if (res.data?.code === 200) {
                        // 使用 ElMessageBox 显示详细信息
                        await ElMessageBox.alert(
                            res.data.msg.replace(/\n/g, '<br>'),
                            '执行结果',
                            {
                                dangerouslyUseHTMLString: true,
                                confirmButtonText: '确定'
                            }
                        );
                    } else {
                        // 失败情况
                        console.error('操作失败:', res.data?.msg);
                        ElMessageBox.alert(
                            res.data?.msg || '删除失败，未知错误',
                            '错误',
                            {
                                type: 'error',
                                confirmButtonText: '确定'
                            }
                        );
                    }
                } catch (error) {
                    if (error?.name !== 'CancelError') {
                        // 输出错误详情到控制台
                        console.group('删除封禁超时用户出错');
                        console.error('错误对象:', error);
                        console.error('响应数据:', error.response?.data);
                        console.error('错误信息:', error.message);
                        console.groupEnd();

                        // 显示错误提示
                        ElMessageBox.alert(
                            `操作失败：${error.response?.data?.msg || error.message || '未知错误'}`,
                            '错误',
                            {
                                type: 'error',
                                confirmButtonText: '确定'
                            }
                        );
                    }
                } finally {
                    isLoading.value = false;
                }
            };

            const handleUnban = async () => {
                if (!unbanForm.username) {
                    ElMessage.error('请输入用户名');
                    return;
                }

                try {
                    isLoading.value = true;
                    const res = await axios.post("/plugin/Autoban/api/unbanAndWhitelist", unbanForm);
                    
                    if (res.data?.code === 200) {
                        ElMessage.success(`解封成功，用户 ${res.data.data.username} 已加入白名单至 ${res.data.data.whitelist_expire}`);
                        unbanForm.username = ''; // 清空输入
                    } else {
                        ElMessage.error(res.data?.msg || '操作失败');
                    }
                } catch (error) {
                    ElMessage.error(error.response?.data?.msg || '操作失败');
                } finally {
                    isLoading.value = false;
                }
            };

            // 获取白名单列表
            const fetchWhitelist = async () => {
                tableLoading.value = true;
                try {
                    const res = await axios.post("/plugin/Autoban/api/getWhitelist");
                    if (res.data?.code === 200) {
                        whitelistData.value = res.data.data;
                    }
                } catch (error) {
                    ElMessage.error('获取白名单列表失败');
                } finally {
                    tableLoading.value = false;
                }
            };

            // 处理解封并加入白名单
            const handleWhitelist = async () => {
                if (!whitelistForm.user_id || isNaN(whitelistForm.user_id)) {
                    ElMessage.error('请选择有效的用户ID');
                    return;
                }

                try {
                    isLoading.value = true;
                    const res = await axios.post("/plugin/Autoban/api/unbanAndWhitelist", {
                        user_id: parseInt(whitelistForm.user_id),
                        whitelist_days: whitelistForm.whitelist_days
                    });
                    
                    if (res.data?.code === 200) {
                        ElMessage.success(`解封成功，用户 ID: ${res.data.data.user_id} 已加入白名单至 ${res.data.data.whitelist_expire}`);
                        whitelistForm.user_id = ''; // 清空输入
                        fetchWhitelist(); // 刷新列表
                    } else {
                        ElMessage.error(res.data?.msg || '操作失败');
                    }
                } catch (error) {
                    ElMessage.error(error.response?.data?.msg || '操作失败');
                } finally {
                    isLoading.value = false;
                }
            };

            // 处理移除白名单
            const handleRemoveWhitelist = async (row) => {
                try {
                    await ElMessageBox.confirm(
                        `确定要将用户 ID: ${row.user_id} 从白名单中移除吗？`,
                        '警告',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    );

                    const res = await axios.post("/plugin/Autoban/api/removeWhitelist", {
                        user_id: row.user_id
                    });

                    if (res.data?.code === 200) {
                        ElMessage.success('移除成功');
                        fetchWhitelist(); // 刷新列表
                    } else {
                        ElMessage.error(res.data?.msg || '移除失败');
                    }
                } catch (error) {
                    if (error !== 'cancel') {
                        ElMessage.error('操作失败');
                    }
                }
            };

            // 处理菜单选择
            const handleMenuSelect = (index) => {
                activeMenu.value = index;
            };

            const disabledStartDate = (time) => {
                // 禁用未来日期
                return time.getTime() > Date.now();
            };

            const disabledEndDate = (time) => {
                // 禁用开始日期之前的日期和未来日期
                if (orderForm.startTime) {
                    const startTime = new Date(orderForm.startTime).getTime();
                    return time.getTime() < startTime || time.getTime() > Date.now();
                }
                return time.getTime() > Date.now();
            };

            // 修改搜索方法名称和实现
            const queryUserIds = async (queryString, callback) => {
                if (queryString.length < 1) {
                    callback([]);
                    return;
                }
                
                try {
                    const res = await axios.post("/plugin/Autoban/api/searchUsers", {
                        user_id: queryString
                    });
                    
                    if (res.data?.code === 200) {
                        // 确保返回的数据中 id 是数字类型
                        const suggestions = res.data.data.map(item => ({
                            ...item,
                            id: parseInt(item.id)
                        }));
                        callback(suggestions);
                    } else {
                        callback([]);
                    }
                } catch (error) {
                    console.error('搜索用户失败:', error);
                    callback([]);
                }
            };

            // 修改选择处理方法
            const handleUserIdSelect = (item) => {
                // 确保选择的是数字类型的 ID
                whitelistForm.user_id = parseInt(item.id);
            };

            const saveUnauth = async () => {
                isLoading.value = true;
                try {
                    const res = await axios.post("/plugin/Autoban/api/saveUnauth", unauthForm);
                    if (res.data?.code === 200) {
                        ElMessage.success('保存成功');
                        // 如果返回了配置数据，更新本地表单
                        if (res.data.data) {
                            const data = res.data.data;
                            unauthForm.status = data.unauth_status ?? unauthForm.status;
                            unauthForm.days = data.unauth_days ?? unauthForm.days;
                            unauthForm.banContent = data.unauth_ban_content ?? unauthForm.banContent;
                            unauthForm.expireTime = data.unauth_expire_time ?? unauthForm.expireTime;
                            console.log("Autoban: 未实名配置已更新 - 天数: " + unauthForm.days);
                        }
                    } else {
                        ElMessage.error(res.data?.msg || '保存失败');
                    }
                } catch (error) {
                    ElMessage.error(error.message || '保存失败');
                    console.error("保存未实名配置出错:", error);
                } finally {
                    isLoading.value = false;
                }
            };

            const handleManualUnauth = async () => {
                try {
                    await ElMessageBox.confirm(
                        '确定要立即执行未实名检测吗？此操作将封禁所有未实名用户。',
                        '警告',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    );
                    
                    isLoading.value = true;
                    const res = await axios.post("/plugin/Autoban/api/checkUnauth");
                    
                    if (res.data?.code === 200) {
                        await ElMessageBox.alert(
                            res.data.msg.replace(/\n/g, '<br>'),
                            '执行结果',
                            {
                                dangerouslyUseHTMLString: true,
                                confirmButtonText: '确定'
                            }
                        );
                    } else {
                        ElMessageBox.alert(
                            res.data?.msg || '检测失败，未知错误',
                            '错误',
                            {
                                type: 'error',
                                confirmButtonText: '确定'
                            }
                        );
                    }
                } catch (error) {
                    if (error?.name !== 'CancelError') {
                        ElMessage.error(error.response?.data?.msg || '操作失败');
                    }
                } finally {
                    isLoading.value = false;
                }
            };

            // 修改保存场景配置方法
            const saveScene = async () => {
                if (!sceneForm.banContent) {
                    ElMessage.error('封禁提示内容不能为空');
                    return;
                }

                isLoading.value = true;
                try {
                    const res = await axios.post("/plugin/Autoban/api/saveScene", {
                        status: Number(sceneForm.status),
                        banStatus: Number(sceneForm.banStatus),
                        deleteStatus: Number(sceneForm.deleteStatus),
                        sceneContent: sceneForm.sceneContent,
                        sceneWebsite: sceneForm.sceneWebsite,
                        banContent: sceneForm.banContent,
                        deleteMinutes: sceneForm.deleteMinutes,
                        banMinutes: sceneForm.banMinutes,
                        checkAuthUsers: Number(sceneForm.checkAuthUsers),
                        expireTime: sceneForm.expireTime
                    });
                    
                    if (res.data?.code === 200) {
                        // 使用服务器返回的数据更新表单状态
                        const data = res.data.data;
                        Object.assign(sceneForm, {
                            status: Boolean(data.scene_status),  // 确保转换为布尔值
                            banStatus: Boolean(data.scene_ban_status),  // 确保转换为布尔值
                            deleteStatus: Boolean(data.scene_delete_status),  // 确保转换为布尔值
                            sceneContent: data.scene_content,
                            sceneWebsite: data.scene_website,
                            banContent: data.scene_ban_content,
                            deleteMinutes: data.scene_delete_minutes,
                            banMinutes: data.scene_ban_minutes,
                            expireTime: data.scene_ban_expire_time ?? 0
                        });
                        
                        ElMessage.success('保存成功');
                    } else {
                        ElMessage.error(res.data?.msg || '保存失败');
                    }
                } catch (error) {
                    ElMessage.error(error.message || '保存失败');
                } finally {
                    isLoading.value = false;
                }
            };

            // 添加手动执行场景检查方法
            const handleManualScene = async () => {
                try {
                    await ElMessageBox.confirm(
                        '确定要立即执行场景检查吗？',
                        '提示',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    );
                    
                    isLoading.value = true;
                    const res = await axios.post("/plugin/Autoban/api/checkScene");
                    
                    if (res.data?.code === 200) {
                        await ElMessageBox.alert(
                            res.data.msg.replace(/\n/g, '<br>'),
                            '执行结果',
                            {
                                dangerouslyUseHTMLString: true,
                                confirmButtonText: '确定'
                            }
                        );
                    } else {
                        ElMessage.error(res.data?.msg || '执行失败');
                    }
                } catch (error) {
                    if (error?.name !== 'CancelError') {
                        ElMessage.error(error.message || '执行失败');
                    }
                } finally {
                    isLoading.value = false;
                }
            };

            const saveBanTime = async () => {
                isLoading.value = true;
                try {
                    const res = await axios.post("/plugin/Autoban/api/saveBanTime", banTimeForm);
                    if (res.data?.code === 200) {
                        ElMessage.success('保存成功');
                    } else {
                        ElMessage.error(res.data?.msg || '保存失败');
                    }
                } catch (error) {
                    ElMessage.error(error.message || '保存失败');
                } finally {
                    isLoading.value = false;
                }
            };

            onMounted(() => {
                fetchData();
                if (activeMenu.value === 'whitelist') {
                    fetchWhitelist();
                }
            });

            // 监听菜单切换，当切换到白名单时获取列表
            watch(activeMenu, (newVal) => {
                if (newVal === 'whitelist') {
                    fetchWhitelist();
                }
            });

            return {
                isLoading,
                activeMenu,
                complaintForm,
                orderForm,
                timeoutForm,
                unbanForm,
                whitelistForm,
                whitelistData,
                tableLoading,
                handleMenuSelect,
                saveComplaint,
                saveOrder,
                saveTimeout,
                handleManualDelete,
                handleUnban,
                handleWhitelist,
                handleRemoveWhitelist,
                shortcuts,
                disabledStartDate,
                disabledEndDate,
                queryUserIds,
                handleUserIdSelect,
                unauthForm,
                saveUnauth,
                handleManualUnauth,
                sceneForm,
                saveScene,
                handleManualScene,
                banTimeForm,
                saveBanTime,
            };
        }
    });

    app.use(ElementPlus);
    app.mount("#app");

    window.onload = () => {
        document.getElementById("loading").style.display = "none";
        document.getElementById("app").style.display = "block";
    };
</script>
</body>
</html>
