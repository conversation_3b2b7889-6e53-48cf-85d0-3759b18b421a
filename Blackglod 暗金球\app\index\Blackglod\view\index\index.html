<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="referrer" content="no-referrer">
    <meta name="robots" content="noarchive">
    <meta http-equiv="Cache-Control" content="no-store">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <script>
        document.oncontextmenu = function() { return false; };
        document.onselectstart = function() { return false; };
        document.oncopy = function() { return false; };
        document.onkeydown = function(e) {
            if (e.keyCode === 123 || 
               (e.ctrl<PERSON>ey && e.shiftKey && e.keyCode === 73) || 
               (e.ctrl<PERSON><PERSON> && e.shiftKey && e.keyCode === 67) || 
               (e.ctrl<PERSON><PERSON> && e.shift<PERSON><PERSON> && e.keyCode === 74) || 
               (e.ctrl<PERSON><PERSON> && e.keyCode === 85)) {
                return false;
            }
        };
    </script>
    <title>{$title|default='Blackglod商城'}</title>
    <link rel="shortcut icon" href="/assets/plugin/Blackglod/plugin/Blackglod/images/favicon.ico" type="image/x-icon">
    {if !empty($favicon)}
    <link rel="shortcut icon" href="{$favicon}" type="image/x-icon">
    {/if}
    <link rel="stylesheet" href="/assets/plugin/Blackglod/plugin/Blackglod/css/all.min.css">
    <style>
        @font-face {
            font-family: 'Noto Sans SC';
            src: url('/assets/plugin/Blackglod/plugin/Blackglod/fonts/k3kCo84MPvpLmixcA63oeAL7Iqp5IZJF9bmaG-3FnYw.ttf') format('truetype');
            font-weight: 400;
            font-style: normal;
        }
        @font-face {
            font-family: 'Noto Sans SC';
            src: url('/assets/plugin/Blackglod/plugin/Blackglod/fonts/k3kCo84MPvpLmixcA63oeAL7Iqp5IZJF9bmaG9_FnYw.ttf') format('truetype');
            font-weight: 500;
            font-style: normal;
        }
        @font-face {
            font-family: 'Noto Sans SC';
            src: url('/assets/plugin/Blackglod/plugin/Blackglod/fonts/k3kCo84MPvpLmixcA63oeAL7Iqp5IZJF9bmaGzjCnYw.ttf') format('truetype');
            font-weight: 700;
            font-style: normal;
        }

        :root {
            --primary: #C8A675;
            --primary-dark: #a88a5c;
            --primary-light: #e0c4a3;
            --dark: #000000;
            --light: #ffffff;
            --gray: #666666;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: var(--dark);
            color: var(--light);
            line-height: 1.6;
            overflow-x: hidden;
            animation: pageTransition 0.8s ease-out;
        }

        .container {
            width: 90%;
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
        }

        /* 优化渐变背景动画 */
        .background-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            opacity: 0.1;
            background: 
                radial-gradient(circle at 20% 20%, rgba(200, 166, 117, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(168, 138, 92, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.95) 100%);
            animation: backgroundMove 30s ease-in-out infinite alternate;
            will-change: transform;
            transform: translateZ(0);
            contain: paint;
        }

        @keyframes backgroundMove {
            0% { background-position: 0% 0%, 0% 0%, 0% 0%; }
            50% { background-position: 20% 20%, -20% -20%, 0% 0%; }
            100% { background-position: -20% -20%, 20% 20%, 0% 0%; }
        }

        /* 增强的头部导航 */
        .header {
            padding: 1rem 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            background: linear-gradient(to bottom,
                rgba(0, 0, 0, 0.95) 0%,
                rgba(0, 0, 0, 0.8) 100%);
            backdrop-filter: blur(10px);
            transition: var(--transition);
            will-change: transform, opacity;
            transform: translateZ(0);
        }

        .header.scrolled {
            padding: 0.5rem 0;
            background: rgba(0, 0, 0, 0.95);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
        }

        .header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 2rem;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            color: var(--light);
            text-decoration: none;
            min-width: 200px;
        }

        .logo-img {
            height: 36px;
            width: auto;
        }

        .logo span {
            font-size: 1.4rem;
            font-weight: 500;
            background: linear-gradient(135deg, var(--light), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        /* 优化导航栏样式 */
        .nav-links {
            display: flex;
            gap: 1rem; /* 减小间距 */
            margin-left: auto;
            flex-wrap: nowrap; /* 防止换行 */
            align-items: center;
        }

        .nav-item {
            position: relative;
            white-space: nowrap; /* 防止文字换行 */
        }

        .nav-item > a {
            color: var(--light);
            text-decoration: none;
            font-size: 0.85rem; /* 稍微减小字体 */
            padding: 0.4rem 0.6rem; /* 减小内边距 */
            border-radius: 4px;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 2px;
        }

        .nav-item > a:hover {
            color: var(--primary);
            background: rgba(200, 166, 117, 0.1);
        }

        .dropdown-arrow {
            transition: var(--transition);
            margin-left: 2px;
        }

        .nav-item:hover .dropdown-arrow {
            transform: translateY(2px);
        }

        .submenu {
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translate3d(-50%, 10px, 0);
            background: rgba(0, 0, 0, 0.95);
            backdrop-filter: blur(10px);
            min-width: 160px;
            border-radius: 8px;
            padding: 0.5rem;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(200, 166, 117, 0.1);
            will-change: transform, opacity;
        }

        .nav-item:hover .submenu {
            opacity: 1;
            visibility: visible;
            transform: translate3d(-50%, 0, 0);
        }

        .submenu a {
            color: var(--light);
            text-decoration: none;
            padding: 0.6rem 1rem;
            display: block;
            font-size: 0.9rem;
            border-radius: 4px;
            transition: var(--transition);
        }

        .submenu a:hover {
            background: rgba(200, 166, 117, 0.1);
            color: var(--primary);
        }

        .auth-buttons {
            display: flex;
            gap: 1rem;
            margin-left: 2rem;
        }

        .auth-buttons .btn {
            padding: 0.5rem 1.2rem;
            border-radius: 4px;
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .auth-icon {
            transition: var(--transition);
        }

        .btn-login {
            color: var(--primary);
            border: 1px solid var(--primary);
            background: transparent;
        }

        .btn-login:hover {
            background: rgba(200, 166, 117, 0.1);
        }

        .btn-login:hover .auth-icon {
            transform: scale(1.1);
        }

        .btn-register {
            color: var(--dark);
            background: var(--primary);
            border: 1px solid var(--primary);
        }

        .btn-register .auth-icon {
            color: var(--dark);
        }

        .btn-register:hover {
            background: var(--primary-dark);
            border-color: var(--primary-dark);
        }

        .btn-register:hover .auth-icon {
            transform: scale(1.1);
        }

        @media (max-width: 768px) {
            .nav-links, .auth-buttons {
                display: none;
            }
        }

        /* 当导航项超过一定数量时进行响应式调整 */
        @media (max-width: 1200px) {
            .nav-item > a {
                font-size: 0.8rem;
                padding: 0.3rem 0.5rem;
            }
            
            .nav-links {
                gap: 0.5rem;
            }
        }

        /* 添加导航栏滚动功能 */
        @media (max-width: 1400px) {
            .nav-links {
                overflow-x: auto;
                scrollbar-width: none; /* Firefox */
                -ms-overflow-style: none; /* IE and Edge */
                padding-bottom: 5px; /* 为滚动条预留空间 */
            }
            
            .nav-links::-webkit-scrollbar {
                display: none; /* Chrome, Safari and Opera */
            }
        }

        /* 增强的英雄区域 */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
            padding: 6rem 0;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                linear-gradient(45deg, rgba(0,0,0,0.7), transparent),
                radial-gradient(circle at center, transparent, rgba(0,0,0,0.8));
            z-index: 1;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            text-align: center;
            animation: fadeInUp 1s ease-out;
        }

        .hero h1 {
            font-size: 4rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--light), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: gradientFlow 8s linear infinite;
            text-shadow: 
                0 0 10px rgba(200, 166, 117, 0.3),
                0 0 20px rgba(200, 166, 117, 0.2),
                0 0 30px rgba(200, 166, 117, 0.1);
        }

        @keyframes gradientFlow {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .hero h2 {
            font-size: 2.5rem;
            margin-bottom: 2rem;
            background: linear-gradient(135deg, var(--light) 10%, var(--primary) 50%, var(--light) 90%);
            background-size: 200% auto;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: shine 3s linear infinite;
            text-shadow: 
                0 0 10px rgba(200, 166, 117, 0.3),
                0 0 20px rgba(200, 166, 117, 0.2),
                0 0 30px rgba(200, 166, 117, 0.1);
            position: relative;
        }

        @keyframes shine {
            0% {
                background-position: 200% center;
            }
            100% {
                background-position: -200% center;
            }
        }

        .hero h2::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, 
                transparent,
                rgba(255, 255, 255, 0.4),
                transparent
            );
            animation: lightPass 2s ease-in-out infinite;
            transform: skewX(-20deg);
        }

        @keyframes lightPass {
            0% {
                transform: translateX(-100%) skewX(-20deg);
            }
            100% {
                transform: translateX(200%) skewX(-20deg);
            }
        }

        /* 增强的统计数字 */
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 4rem;
        }

        .stat-item {
            background: linear-gradient(135deg, 
                rgba(200, 166, 117, 0.05) 0%,
                rgba(0, 0, 0, 0.3) 100%);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2.5rem 2rem;
            text-align: center;
            position: relative;
            overflow: hidden;
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .stat-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                90deg,
                transparent,
                rgba(200, 166, 117, 0.2),
                transparent
            );
            transition: 0.5s;
        }

        .stat-item:hover::before {
            left: 100%;
        }

        .stat-item:hover {
            transform: translateY(-10px) scale(1.02);
            border-color: var(--primary);
            box-shadow: 
                0 20px 40px rgba(0, 0, 0, 0.3),
                0 0 20px rgba(200, 166, 117, 0.2);
        }

        .stat-number {
            font-size: 2.8rem;
            font-weight: 700;
            background: linear-gradient(135deg, 
                var(--primary-light) 0%,
                var(--primary) 50%,
                var(--primary-dark) 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
            font-family: 'Arial', sans-serif;
            position: relative;
            display: inline-block;
            opacity: 1; /* 确保默认可见 */
            transform: translateY(0); /* 初始位置 */
            will-change: transform, opacity;
        }

        .stat-number::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 2px;
            background: var(--primary);
            border-radius: 2px;
        }

        .stat-item div:last-child {
            color: #999;
            font-size: 1.1rem;
            margin-top: 0.5rem;
            letter-spacing: 1px;
        }

        /* 数字滚动动画 */
        @keyframes countUp {
            from {
                opacity: 0.3; /* 提高初始透明度 */
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .stat-number.visible {
            animation: countUp 1.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        }

        /* 增强的特性区域 */
        .features {
            padding: 8rem 0;
            background: linear-gradient(to bottom, transparent, rgba(0,0,0,0.8));
            position: relative;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .feature-item {
            padding: 2.5rem;
            border-radius: 20px;
            background: linear-gradient(135deg, 
                rgba(200, 166, 117, 0.05) 0%,
                rgba(0, 0, 0, 0.3) 100%);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(200, 166, 117, 0.1);
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .feature-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, transparent, rgba(200, 166, 117, 0.1));
            opacity: 0;
            transition: var(--transition);
        }

        .feature-item:hover {
            transform: translateY(-10px) scale(1.02);
            border-color: var(--primary);
        }

        .feature-item:hover::before {
            opacity: 1;
        }

        .feature-icon {
            font-size: 3rem;
            color: var(--primary);
            margin-bottom: 1.5rem;
            transition: var(--transition);
        }

        .feature-item:hover .feature-icon {
            transform: scale(1.2) rotate(10deg);
        }

        /* 增强的步骤区域 */
        .steps {
            padding: 8rem 0;
            background: linear-gradient(to top, transparent, rgba(0,0,0,0.8));
            position: relative;
            overflow: hidden;
        }

        /* 添加六边形网格背景 */
        .steps::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(30deg, #000000 12%, transparent 12.5%, transparent 87%, #000000 87.5%, #000000),
                linear-gradient(150deg, #000000 12%, transparent 12.5%, transparent 87%, #000000 87.5%, #000000),
                linear-gradient(30deg, #000000 12%, transparent 12.5%, transparent 87%, #000000 87.5%, #000000),
                linear-gradient(150deg, #000000 12%, transparent 12.5%, transparent 87%, #000000 87.5%, #000000),
                linear-gradient(60deg, rgba(200,166,117,0.1) 25%, transparent 25.5%, transparent 75%, rgba(200,166,117,0.1) 75%, rgba(200,166,117,0.1)),
                linear-gradient(60deg, rgba(200,166,117,0.1) 25%, transparent 25.5%, transparent 75%, rgba(200,166,117,0.1) 75%, rgba(200,166,117,0.1));
            background-size: 40px 70px;
            background-position: 0 0, 0 0, 20px 35px, 20px 35px, 0 0, 20px 35px;
            opacity: 0.1;
            z-index: 0;
        }

        /* 添加发光动画效果 */
        .steps::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 50% 50%, 
                rgba(200,166,117,0.1),
                transparent 60%);
            animation: pulseGlow 4s ease-in-out infinite;
            z-index: 1;
        }

        @keyframes pulseGlow {
            0%, 100% {
                opacity: 0.3;
                transform: scale(1);
            }
            50% {
                opacity: 0.5;
                transform: scale(1.2);
            }
        }

        /* 确保内容在背景之上 */
        .steps .container {
            position: relative;
            z-index: 2;
        }

        .steps .section-title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--primary-light), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .steps .section-subtitle {
            text-align: center;
            color: var(--gray);
            margin-bottom: 4rem;
            font-size: 1.1rem;
        }

        .steps-container {
            display: flex;
            justify-content: center;
            gap: 4rem;
            position: relative;
        }

        .steps-container::before {
            content: '';
            position: absolute;
            top: 60px;
            left: calc(16.666% + 60px);
            right: calc(16.666% + 60px);
            height: 2px;
            background: linear-gradient(90deg,
                transparent,
                var(--primary) 20%,
                var(--primary) 80%,
                transparent
            );
            opacity: 0.3;
        }

        .step {
            flex: 1;
            max-width: 280px;
            text-align: center;
            position: relative;
        }

        .step-circle {
            width: 120px;
            height: 120px;
            margin: 0 auto 2rem;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .step-number {
            position: absolute;
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary);
            z-index: 2;
            transition: var(--transition);
            opacity: 1;
        }

        .step-icon {
            position: absolute;
            font-size: 2.5rem;
            color: var(--primary);
            z-index: 2;
            opacity: 0;
            transform: scale(0.5);
            transition: var(--transition);
        }

        .progress-ring {
            position: absolute;
            transform: rotate(-90deg);
        }

        .progress-ring-circle {
            fill: transparent;
            stroke: var(--primary);
            stroke-width: 2;
            stroke-dasharray: 339.292;
            stroke-dashoffset: 339.292;
            transition: var(--transition);
        }

        .step:hover .progress-ring-circle {
            stroke-dashoffset: 0;
        }

        .step:hover .step-number {
            opacity: 0;
            transform: scale(0.5);
        }

        .step:hover .step-icon {
            opacity: 1;
            transform: scale(1);
        }

        .step h3 {
            color: var(--primary);
            font-size: 1.3rem;
            margin-bottom: 1rem;
            transition: var(--transition);
        }

        .step p {
            color: var(--gray);
            font-size: 1rem;
            line-height: 1.6;
            transition: var(--transition);
        }

        .step:hover h3 {
            transform: translateY(-5px);
            color: var(--primary-light);
        }

        .step:hover p {
            color: var(--light);
        }

        @media (max-width: 768px) {
            .steps {
                padding: 4rem 0;
            }

            .steps-container {
                flex-direction: column;
                align-items: center;
                gap: 3rem;
            }

            .steps-container::before {
                display: none;
            }

            .step {
                max-width: 100%;
            }

            .steps .section-title {
                font-size: 2rem;
            }

            .steps .section-subtitle {
                font-size: 1rem;
                margin-bottom: 3rem;
            }
        }

        /* 优化页脚样式 */
        .footer {
            background: linear-gradient(to top, rgba(0, 0, 0, 0.95), rgba(0, 0, 0, 0.8));
            padding: 4rem 0 2rem;
            position: relative;
            border-top: 1px solid rgba(200, 166, 117, 0.1);
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, 
                transparent,
                var(--primary),
                transparent
            );
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 3rem;
            margin-bottom: 3rem;
        }

        .footer-column {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .footer-column h3 {
            color: var(--primary);
            font-size: 1.2rem;
            margin-bottom: 1rem;
            position: relative;
            padding-bottom: 0.5rem;
        }

        .footer-column h3::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 30px;
            height: 2px;
            background: var(--primary);
            border-radius: 2px;
        }

        .footer-column a {
            color: var(--gray);
            text-decoration: none;
            transition: var(--transition);
            font-size: 0.95rem;
            display: inline-block;
            position: relative;
            padding-left: 1.2rem;
        }

        .footer-column a::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: var(--primary);
            transform: translateY(-50%) scale(0.6);
            opacity: 0.5;
            transition: var(--transition);
        }

        .footer-column a:hover {
            color: var(--primary-light);
            transform: translateX(5px);
        }

        .footer-column a:hover::before {
            transform: translateY(-50%) scale(1);
            opacity: 1;
        }

        .copyright {
            text-align: center;
            color: var(--gray);
            padding-top: 2rem;
            border-top: 1px solid rgba(200, 166, 117, 0.1);
            font-size: 0.9rem;
        }

        .copyright a {
            color: var(--primary);
            text-decoration: none;
            transition: var(--transition);
            margin: 0 0.5rem;
        }

        .copyright a:hover {
            color: var(--primary-light);
        }

        @media (max-width: 768px) {
            .footer {
                padding: 3rem 0 1.5rem;
            }

            .footer-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 2rem;
            }

            .copyright {
                flex-direction: column;
                gap: 0.5rem;
            }
        }

        @media (max-width: 480px) {
            .footer-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .hero h1 {
                font-size: 2.5rem;
            }

            .hero h2 {
                font-size: 2rem;
            }

            .steps-container {
                flex-direction: column;
                gap: 2rem;
            }

            .steps-container::before {
                display: none;
            }

            .auth-buttons {
                display: none;
            }

            .mobile-menu-btn {
                display: block;
            }
        }

        /* 移动端菜单按钮 */
        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            color: var(--light);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
        }

        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: block;
            }
        }

    /* 星星背景容器 */
    .stars-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        z-index: 0;
        pointer-events: none;
        background: radial-gradient(circle at center, rgba(0,0,0,0.8), rgba(0,0,0,0.95)); /* 添加暗色渐变背景 */
    }

    /* 星星样式 */
    .star {
        position: absolute;
        background: linear-gradient(135deg, #ffd700, #ffa500);  /* 更亮的金色渐变 */
        border-radius: 50%;
        filter: blur(1px);
        box-shadow: 
            0 0 4px #ffd700,
            0 0 8px #ffd700,
            0 0 12px #c8a675;  /* 三层光晕效果 */
        opacity: 0;
        animation: twinkle var(--duration) ease-in-out infinite;
        animation-delay: var(--delay);
        will-change: transform, opacity;
        transform: translateZ(0);
        transform: translate3d(0, 0, 0);
    }

    /* 星星闪烁动画 */
    @keyframes twinkle {
        0%, 100% {
            opacity: 0;
            transform: scale(0.3);
            filter: blur(1px);
        }
        50% {
            opacity: var(--opacity);
            transform: scale(1.2);  /* 更大的缩放效果 */
            filter: blur(0.5px);
        }
    }

    /* 流星效果 */
    .shooting-star {
        position: absolute;
        width: 200px;  /* 更长的流星 */
        height: 3px;   /* 更粗的流星 */
        background: linear-gradient(90deg, 
            rgba(255, 215, 0, 1), 
            rgba(255, 215, 0, 0.8),
            rgba(200, 166, 117, 0.4), 
            transparent
        );
        opacity: 0;
        filter: blur(0.5px);
        transform: rotate(-45deg);
        box-shadow: 
            0 0 20px rgba(255, 215, 0, 0.8),
            0 0 30px rgba(255, 215, 0, 0.6);  /* 更强的光晕效果 */
        animation: shoot 3s ease-in infinite;
        animation-delay: var(--delay);
        will-change: transform, opacity;
    }

    @keyframes shoot {
        0% {
            opacity: 0;
            transform: translateX(-100%) translateY(0) rotate(-45deg);
        }
        10% {
            opacity: 1;
        }
        20%, 100% {
            opacity: 0;
            transform: translateX(100vw) translateY(100vh) rotate(-45deg);
        }
    }

    /* 金额数字的响应式字体大小 */
    .stat-number.amount {
        font-size: 2.8rem; /* 默认大小 */
    }

    .stat-number.amount.length-9 {
        font-size: 2.6rem;
    }

    .stat-number.amount.length-10 {
        font-size: 2.4rem;
    }

    .stat-number.amount.length-11 {
        font-size: 2.2rem;
    }

    .stat-number.amount.length-12 {
        font-size: 2rem;
    }

    /* 移动端适配 */
    @media (max-width: 768px) {
        .stat-number.amount {
            font-size: 2.2rem;
        }
        
        .stat-number.amount.length-9 {
            font-size: 2rem;
        }
        
        .stat-number.amount.length-10 {
            font-size: 1.8rem;
        }
        
        .stat-number.amount.length-11 {
            font-size: 1.6rem;
        }
        
        .stat-number.amount.length-12 {
            font-size: 1.4rem;
        }
    }

    .payment-icons {
        padding: 6rem 0;
        background: rgba(0, 0, 0, 0.3);
        border-bottom: 1px solid rgba(200, 166, 117, 0.1);
        position: relative;
        overflow: hidden;
    }

    .payment-icons::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, 
            transparent,
            var(--primary),
            transparent
        );
    }

    .section-title {
        text-align: center;
        color: var(--primary);
        font-size: 2.2rem;
        margin-bottom: 1rem;
        background: linear-gradient(135deg, var(--primary-light), var(--primary));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .section-subtitle {
        text-align: center;
        color: var(--gray);
        margin-bottom: 3rem;
        font-size: 1.1rem;
    }

    .icons-grid {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: 3rem;
        align-items: center;
        justify-items: center;
        padding: 2rem 0;
    }

    .icon-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1.2rem;
        position: relative;
        transition: transform 0.3s ease;
        padding: 1.5rem;
        border-radius: 15px;
        background: rgba(0, 0, 0, 0.2);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(200, 166, 117, 0.1);
    }

    .icon-item:hover {
        transform: translateY(-10px);
        background: rgba(200, 166, 117, 0.1);
        border-color: rgba(200, 166, 117, 0.3);
        box-shadow: 
            0 10px 20px rgba(0, 0, 0, 0.2),
            0 0 15px rgba(200, 166, 117, 0.2),
            inset 0 0 20px rgba(200, 166, 117, 0.1);
    }

    .icon-wrapper {
        position: relative;
        padding: 1.5rem;
        border-radius: 50%;
        background: rgba(0, 0, 0, 0.3);
        overflow: hidden;
    }

    .icon-wrapper::before {
        content: '';
        position: absolute;
        inset: -50%;
        background: conic-gradient(
            from 0deg,
            transparent,
            var(--primary) 60%,
            transparent 80%
        );
        animation: rotateGlow 2s linear infinite;
    }

    .icon-wrapper::after {
        content: '';
        position: absolute;
        inset: 2px;
        background: rgba(0, 0, 0, 0.8);
        border-radius: 50%;
        z-index: 1;
    }

    .payment-icon {
        width: 70px;
        height: 70px;
        color: var(--primary);
        transition: all 0.3s ease;
        position: relative;
        z-index: 2;
        filter: drop-shadow(0 0 8px rgba(200, 166, 117, 0.3));
    }

    .icon-item:hover .payment-icon {
        transform: scale(1.1) rotate(5deg);
        filter: drop-shadow(0 0 12px rgba(200, 166, 117, 0.5));
    }

    .icon-item span {
        color: var(--gray);
        font-size: 1rem;
        font-weight: 500;
        transition: all 0.3s ease;
        text-shadow: 0 0 10px rgba(200, 166, 117, 0.3);
    }

    .icon-item:hover span {
        color: var(--primary);
        transform: scale(1.1);
        text-shadow: 0 0 15px rgba(200, 166, 117, 0.5);
    }

    @keyframes rotateGlow {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    /* 添加浮动粒子效果 */
    .icon-item::after {
        content: '';
        position: absolute;
        width: 4px;
        height: 4px;
        background: var(--primary);
        border-radius: 50%;
        filter: blur(1px);
        opacity: 0;
        transition: 0.5s;
        animation: particleFloat 2s ease-in-out infinite;
    }

    @keyframes particleFloat {
        0% {
            transform: translate(0, 0);
            opacity: 0;
        }
        50% {
            opacity: 1;
        }
        100% {
            transform: translate(var(--x, 20px), var(--y, -20px));
            opacity: 0;
        }
    }

    .icons-grid {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: 2rem;
        padding: 2rem;
        position: relative;
        z-index: 1;
    }

    .icon-glow {
        position: absolute;
        inset: 0;
        background: radial-gradient(circle at center, 
            rgba(200, 166, 117, 0.2),
            transparent 70%
        );
        opacity: 0;
        transition: var(--transition);
        filter: blur(10px);
    }

    .icon-item:hover .icon-glow {
        opacity: 1;
        transform: scale(1.2);
    }

    .icon-item:hover .payment-icon {
        transform: scale(1.1);
        filter: drop-shadow(0 0 10px rgba(200, 166, 117, 0.3));
    }

    .icon-item span {
        color: var(--gray);
        font-size: 1rem;
        font-weight: 500;
        transition: var(--transition);
    }

    .icon-item:hover span {
        color: var(--primary-light);
    }

    @media (max-width: 768px) {
        .payment-icons {
            padding: 4rem 0;
        }

        .icons-grid {
            grid-template-columns: repeat(3, 1fr);
            gap: 2rem;
        }
        
        .payment-icon {
            width: 60px;
            height: 60px;
        }

        .section-title {
            font-size: 1.8rem;
        }

        .section-subtitle {
            font-size: 1rem;
            margin-bottom: 2rem;
        }
    }

    /* 减少不必要的动画 */
    @media (prefers-reduced-motion: reduce) {
        .background-animation,
        .star,
        .shooting-star {
            animation: none;
        }
    }

    .nav-item > a.has-arrow {
        display: flex;
        align-items: center;
        gap: 8px;
        padding-right: 12px;
    }

    /* 确保箭头颜色为金色 */
    .dropdown-arrow path {
        stroke: var(--primary);
    }

    /* 悬停时箭头颜色加深 */
    .nav-item:hover .dropdown-arrow path {
        stroke: var(--primary-dark);
    }

    /* 优化按钮效果 */
    .btn {
        position: relative;
        overflow: hidden;
    }

    .btn::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 60%);
        transform: scale(0);
        transition: transform 0.6s ease-out;
    }

    .btn:hover::before {
        transform: scale(1);
    }

    /* 添加页面切换动画 */
    @keyframes pageTransition {
        0% { opacity: 0; transform: translateY(20px); }
        100% { opacity: 1; transform: translateY(0); }
    }

    /* 优化导航栏效果 */
    .header {
        background: linear-gradient(to bottom,
            rgba(0, 0, 0, 0.95) 0%,
            rgba(0, 0, 0, 0.8) 100%);
    }

    .nav-item > a::before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 0;
        height: 2px;
        background: var(--primary);
        transition: all 0.3s ease;
        transform: translateX(-50%);
    }

    .nav-item > a:hover::before {
        width: 100%;
    }

    /* 优化统计数字动画 */
    .stat-number {
        background: linear-gradient(135deg, 
            var(--primary-light) 0%,
            var(--primary) 50%,
            var(--primary-dark) 100%);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        animation: numberGlow 2s ease-in-out infinite alternate;
    }

    @keyframes numberGlow {
        0% { filter: drop-shadow(0 0 2px rgba(200, 166, 117, 0.3)); }
        100% { filter: drop-shadow(0 0 8px rgba(200, 166, 117, 0.6)); }
    }

    /* 优化滚动条样式 */
    ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }

    ::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.1);
    }

    ::-webkit-scrollbar-thumb {
        background: var(--primary);
        border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: var(--primary-dark);
    }

    /* 添加页面加载进度条 */
    .progress-bar {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background: linear-gradient(to right, var(--primary), var(--primary-light));
        transform-origin: 0 50%;
        transform: scaleX(0);
        transition: transform 0.3s ease;
        z-index: 10000;
    }

    /* 粒子容器 */
    .particles-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
        pointer-events: none;
        overflow: hidden;
    }

    /* 粒子样式 */
    .particle {
        position: absolute;
        background: radial-gradient(circle at center,
            rgba(200, 166, 117, 0.8) 0%,
            rgba(200, 166, 117, 0.4) 40%,
            transparent 70%
        );
        border-radius: 50%;
        filter: blur(1px);
        animation: particleFloat 8s infinite;
    }

    /* 粒子动画 */
    @keyframes particleFloat {
        0% {
            transform: translate(0, 0) rotate(0deg) scale(1);
            opacity: 0;
        }
        20% {
            opacity: var(--particle-opacity);
        }
        80% {
            opacity: var(--particle-opacity);
        }
        100% {
            transform: translate(
                var(--particle-translateX),
                var(--particle-translateY)
            ) 
            rotate(var(--particle-rotate)) 
            scale(var(--particle-scale));
            opacity: 0;
        }
    }

    /* 连线效果 */
    .particle-line {
        position: absolute;
        background: linear-gradient(90deg,
            transparent,
            rgba(200, 166, 117, 0.2),
            transparent
        );
        height: 1px;
        transform-origin: left center;
        animation: lineGrow 4s infinite;
    }

    @keyframes lineGrow {
        0% {
            transform: scaleX(0);
            opacity: 0;
        }
        50% {
            transform: scaleX(1);
            opacity: 1;
        }
        100% {
            transform: scaleX(0);
            opacity: 0;
        }
    }

    /* 星球容器 */
    .planets-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 0;
        pointer-events: none;
        overflow: hidden;
    }

    /* 星球基础样式 */
    .planet {
        position: absolute;
        border-radius: 50%;
        filter: blur(1px);
        animation: planetGlow 4s ease-in-out infinite alternate;
    }

    /* 主星球 */
    .planet-main {
        width: 300px;
        height: 300px;
        right: -100px;
        top: 20%;
        background: radial-gradient(circle at 30% 30%,
            rgba(200, 166, 117, 0.2),
            rgba(168, 138, 92, 0.1),
            rgba(0, 0, 0, 0)
        );
        box-shadow: 
            inset 10px 10px 30px rgba(200, 166, 117, 0.3),
            inset -10px -10px 30px rgba(0, 0, 0, 0.4);
    }

    /* 小星球1 */
    .planet-small-1 {
        width: 150px;
        height: 150px;
        left: 10%;
        top: 15%;
        background: radial-gradient(circle at 40% 40%,
            rgba(200, 166, 117, 0.15),
            rgba(168, 138, 92, 0.08),
            rgba(0, 0, 0, 0)
        );
        animation-delay: -2s;
    }

    /* 小星球2 */
    .planet-small-2 {
        width: 100px;
        height: 100px;
        left: 60%;
        bottom: 20%;
        background: radial-gradient(circle at 35% 35%,
            rgba(200, 166, 117, 0.12),
            rgba(168, 138, 92, 0.06),
            rgba(0, 0, 0, 0)
        );
        animation-delay: -1s;
    }

    /* 星球光晕动画 */
    @keyframes planetGlow {
        0% {
            transform: scale(1) rotate(0deg);
            opacity: 0.8;
        }
        100% {
            transform: scale(1.1) rotate(360deg);
            opacity: 1;
        }
    }

    /* 星球轨道 */
    .planet-orbit {
        position: absolute;
        border: 1px solid rgba(200, 166, 117, 0.1);
        border-radius: 50%;
        animation: orbitRotate 20s linear infinite;
    }

    .orbit-1 {
        width: 400px;
        height: 400px;
        right: -150px;
        top: 15%;
    }

    .orbit-2 {
        width: 200px;
        height: 200px;
        left: 8%;
        top: 12%;
    }

    /* 轨道旋转动画 */
    @keyframes orbitRotate {
        from {
            transform: rotate(0deg);
        }
        to {
            transform: rotate(360deg);
        }
    }

    /* 调整原有粒子容器的层级 */
    .particles-container {
        z-index: 1;
    }

    /* 左侧主星球 */
    .planet-main-left {
        width: 280px;
        height: 280px;
        left: -80px;
        top: 35%;
        background: radial-gradient(circle at 70% 30%,
            rgba(200, 166, 117, 0.2),
            rgba(168, 138, 92, 0.1),
            rgba(0, 0, 0, 0)
        );
        box-shadow: 
            inset -10px 10px 30px rgba(200, 166, 117, 0.3),
            inset 10px -10px 30px rgba(0, 0, 0, 0.4);
        animation-delay: -3s;
    }

    /* 添加新的轨道 */
    .orbit-3 {
        width: 380px;
        height: 380px;
        left: -130px;
        top: 30%;
        animation-direction: reverse;
    }

    /* 调整其他星球的位置以平衡布局 */
    .planet-small-1 {
        left: 15%;
        top: 20%;
    }

    .planet-small-2 {
        left: 65%;
        bottom: 25%;
    }

    /* 增强光晕效果 */
    .planet::after {
        content: '';
        position: absolute;
        inset: -20px;
        border-radius: 50%;
        background: radial-gradient(circle at center,
            rgba(200, 166, 117, 0.1),
            transparent 70%
        );
        filter: blur(10px);
        animation: glowPulse 4s ease-in-out infinite alternate;
    }

    @keyframes glowPulse {
        0% {
            opacity: 0.3;
            transform: scale(0.8);
        }
        100% {
            opacity: 0.7;
            transform: scale(1.2);
        }
    }

    /* 3D球体容器 */
    .sphere-container {
        position: relative;
        width: 150px;
        height: 150px;
        margin: 30px auto;
        perspective: 1200px;
    }

    /* 球体主体 */
    .sphere {
        position: relative;
        width: 100%;
        height: 100%;
        transform-style: preserve-3d;
        animation: sphereRotate 20s linear infinite;
    }

    /* 球体核心 */
    .sphere-core {
        position: absolute;
        width: 60px;
        height: 60px;
        left: 30px;
        top: 30px;
        border-radius: 50%;
        background: radial-gradient(circle at 30% 30%,
            var(--primary-light),
            var(--primary),
            var(--primary-dark)
        );
        box-shadow: 
            0 0 20px rgba(200, 166, 117, 0.4),
            inset 0 0 15px rgba(255, 255, 255, 0.6);
    }

    /* 球体环 */
    .sphere-ring {
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        border: 2px solid rgba(200, 166, 117, 0.3);
        box-shadow: 0 0 15px rgba(200, 166, 117, 0.2);
    }

    /* 设置三个环的旋转角度 */
    .sphere-ring:nth-child(1) {
        transform: rotateX(60deg);
    }

    .sphere-ring:nth-child(2) {
        transform: rotateY(60deg);
    }

    .sphere-ring:nth-child(3) {
        transform: rotateZ(60deg);
    }

    /* 球体旋转动画 */
    @keyframes sphereRotate {
        0% {
            transform: rotateX(-20deg) rotateY(0deg) rotateZ(45deg);
        }
        100% {
            transform: rotateX(-20deg) rotateY(360deg) rotateZ(45deg);
        }
    }

    /* 添加悬停效果 */
    .sphere-container:hover .sphere {
        animation-play-state: paused;
    }

    .sphere-container:hover .sphere-core {
        background: radial-gradient(circle at 30% 30%,
            var(--primary-light),
            var(--primary),
            var(--primary-dark)
        );
        box-shadow: 
            0 0 30px rgba(200, 166, 117, 0.6),
            inset 0 0 20px rgba(255, 255, 255, 0.8);
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
        .sphere-container {
            width: 100px;
            height: 100px;
        }
        
        .sphere-core {
            width: 40px;
            height: 40px;
            left: 20px;
            top: 20px;
        }
    }

    .sphere-grid {
        position: absolute;
        width: 100%;
        height: 100%;
        transform-style: preserve-3d;
        box-shadow: inset 0 0 20px rgba(200, 166, 117, 0.1);
        border-radius: 50%;
    }

    /* 经线 */
    .longitude-line {
        position: absolute;
        width: 100%;
        height: 100%;
        border: 1px solid rgba(200, 166, 117, 0.15);
        border-radius: 50%;
        transform: rotateY(var(--rotation));
    }

    /* 纬线 */
    .latitude-line {
        position: absolute;
        width: 100%;
        height: 100%;
        border: 1px solid rgba(200, 166, 117, 0.15);
        border-radius: 50%;
        transform: rotateX(var(--rotation)) scaleY(var(--scale));
    }

    .sphere-surface {
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: radial-gradient(circle at 30% 30%,
            rgba(200, 166, 117, 0.6),
            rgba(200, 166, 117, 0.3)
        );
        box-shadow: 
            inset 0 0 20px rgba(200, 166, 117, 0.4),
            0 0 15px rgba(200, 166, 117, 0.3);
    }

    @keyframes sphereRotate {
        0% {
            transform: rotateX(-20deg) rotateY(0deg) rotateZ(45deg);
        }
        100% {
            transform: rotateX(-20deg) rotateY(360deg) rotateZ(45deg);
        }
    }

    /* 悬停效果 */
    .sphere-container:hover .sphere {
        animation-play-state: paused;
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
        .sphere-container {
            width: 100px;
            height: 100px;
        }
    }

    /* 添加球体两侧的装饰元素样式 */
    .sphere-decorations {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        pointer-events: none;
    }

    .sphere-ring-decoration {
        position: absolute;
        border: 2px solid rgba(200, 166, 117, 0.1);
        border-radius: 50%;
        transform-style: preserve-3d;
    }

    /* 左侧装饰 */
    .decoration-left {
        left: -60px;
        top: 50%;
        width: 40px;
        height: 40px;
        animation: rotateLeft 8s linear infinite;
    }

    .decoration-left::before {
        content: '';
        position: absolute;
        width: 8px;
        height: 8px;
        background: var(--primary);
        border-radius: 50%;
        top: 50%;
        left: -4px;
        transform: translateY(-50%);
        box-shadow: 0 0 15px var(--primary);
    }

    /* 右侧装饰 */
    .decoration-right {
        right: -60px;
        top: 50%;
        width: 40px;
        height: 40px;
        animation: rotateRight 8s linear infinite;
    }

    .decoration-right::after {
        content: '';
        position: absolute;
        width: 8px;
        height: 8px;
        background: var(--primary);
        border-radius: 50%;
        top: 50%;
        right: -4px;
        transform: translateY(-50%);
        box-shadow: 0 0 15px var(--primary);
    }

    /* 装饰性光线 */
    .sphere-rays {
        position: absolute;
        width: 200%;
        height: 200%;
        top: -50%;
        left: -50%;
        background: radial-gradient(
            circle at center,
            rgba(200, 166, 117, 0.1) 0%,
            transparent 60%
        );
        animation: raysPulse 4s ease-in-out infinite;
    }

    /* 悬浮粒子 */
    .floating-particles {
        position: absolute;
        width: 100%;
        height: 100%;
    }

    .floating-particle {
        position: absolute;
        width: 4px;
        height: 4px;
        background: var(--primary);
        border-radius: 50%;
        opacity: 0.6;
        filter: blur(1px);
        animation: floatParticle 6s ease-in-out infinite;
    }

    @keyframes rotateLeft {
        from { transform: translateY(-50%) rotate(0deg); }
        to { transform: translateY(-50%) rotate(-360deg); }
    }

    @keyframes rotateRight {
        from { transform: translateY(-50%) rotate(0deg); }
        to { transform: translateY(-50%) rotate(360deg); }
    }

    @keyframes raysPulse {
        0%, 100% { transform: scale(1); opacity: 0.3; }
        50% { transform: scale(1.2); opacity: 0.5; }
    }

    @keyframes floatParticle {
        0%, 100% { transform: translate(0, 0); opacity: 0.6; }
        50% { transform: translate(var(--tx), var(--ty)); opacity: 0.9; }
    }

    /* 添加轨道效果 */
    .orbit {
        position: absolute;
        border: 1px solid rgba(200, 166, 117, 0.1);
        border-radius: 50%;
        transform-style: preserve-3d;
    }

    .orbit-1 {
        width: 200px;
        height: 200px;
        animation: orbitRotate 20s linear infinite;
    }

    .orbit-2 {
        width: 300px;
        height: 300px;
        animation: orbitRotate 30s linear infinite reverse;
    }

    /* 添加行星效果 */
    .planet {
        position: absolute;
        border-radius: 50%;
        transform-style: preserve-3d;
        animation: planetPulse 4s ease-in-out infinite;
    }

    .planet-small-1 {
        width: 20px;
        height: 20px;
        background: radial-gradient(circle at 30% 30%,
            var(--primary-light),
            var(--primary)
        );
        box-shadow: 0 0 15px var(--primary);
        left: 25%;
        top: 15%;
    }

    .planet-small-2 {
        width: 15px;
        height: 15px;
        background: radial-gradient(circle at 30% 30%,
            var(--primary-light),
            var(--primary)
        );
        box-shadow: 0 0 12px var(--primary);
        right: 20%;
        bottom: 30%;
    }

    /* 添加连接线效果 */
    .connection-line {
        position: absolute;
        height: 1px;
        background: linear-gradient(90deg,
            transparent,
            var(--primary),
            transparent
        );
        opacity: 0.3;
        transform-origin: left center;
        animation: lineGlow 3s ease-in-out infinite;
    }

    /* 添加数据流动画 */
    .data-stream {
        position: absolute;
        width: 2px;
        height: 2px;
        background: var(--primary);
        border-radius: 50%;
        filter: blur(1px);
        opacity: 0;
        animation: dataFlow 3s linear infinite;
    }

    /* 添加全息投影效果 */
    .hologram {
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: repeating-radial-gradient(
            circle at 50%,
            transparent 0,
            rgba(200, 166, 117, 0.1) 1px,
            transparent 2px
        );
        opacity: 0.5;
        animation: hologramScan 4s linear infinite;
    }

    @keyframes planetPulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
    }

    @keyframes lineGlow {
        0%, 100% { opacity: 0.3; }
        50% { opacity: 0.6; }
    }

    @keyframes dataFlow {
        0% {
            opacity: 0;
            transform: translate(0, 0) scale(1);
        }
        50% {
            opacity: 1;
            transform: translate(var(--tx), var(--ty)) scale(1.5);
        }
        100% {
            opacity: 0;
            transform: translate(calc(var(--tx) * 2), calc(var(--ty) * 2)) scale(1);
        }
    }

    @keyframes hologramScan {
        0% { transform: translateY(-50%) rotate(0deg); }
        100% { transform: translateY(50%) rotate(360deg); }
    }

    @keyframes orbitRotate {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    /* 添加能量波纹效果 */
    .energy-ripple {
        position: absolute;
        width: 100%;
        height: 100%;
        border: 2px solid var(--primary);
        border-radius: 50%;
        opacity: 0;
        transform: scale(0.8);
        animation: rippleEffect 3s ease-out infinite;
    }

    @keyframes rippleEffect {
        0% {
            opacity: 0.5;
            transform: scale(0.8);
        }
        100% {
            opacity: 0;
            transform: scale(1.5);
        }
    }

    /* 添加小星球环线效果 */
    .planet-ring {
        position: absolute;
        border-radius: 50%;
        border: 1px solid var(--primary);
        opacity: 0.4;
        transform: rotate3d(1, 0, 1, 75deg);
        transform-style: preserve-3d;
        animation: ringRotate 10s linear infinite;
    }

    .planet-small-1 .planet-ring {
        width: 30px;
        height: 30px;
        left: -5px;
        top: -5px;
    }

    .planet-small-1 .planet-ring:nth-child(2) {
        width: 35px;
        height: 35px;
        left: -7.5px;
        top: -7.5px;
        border-color: rgba(200, 166, 117, 0.3);
        animation-direction: reverse;
        animation-duration: 15s;
    }

    .planet-small-2 .planet-ring {
        width: 25px;
        height: 25px;
        left: -5px;
        top: -5px;
    }

    .planet-small-2 .planet-ring:nth-child(2) {
        width: 28px;
        height: 28px;
        left: -6.5px;
        top: -6.5px;
        border-color: rgba(200, 166, 117, 0.3);
        animation-direction: reverse;
        animation-duration: 12s;
    }

    /* 添加光点效果 */
    .ring-particle {
        position: absolute;
        width: 2px;
        height: 2px;
        background: var(--primary);
        border-radius: 50%;
        box-shadow: 0 0 4px var(--primary);
        animation: particleGlow 2s ease-in-out infinite;
    }

    @keyframes ringRotate {
        from { transform: rotate3d(1, 0, 1, 75deg) rotateZ(0deg); }
        to { transform: rotate3d(1, 0, 1, 75deg) rotateZ(360deg); }
    }

    @keyframes particleGlow {
        0%, 100% { opacity: 0.4; transform: scale(1); }
        50% { opacity: 1; transform: scale(1.5); }
    }

    /* 数据可视化区块样式 */
    .data-visualization {
        padding: 6rem 0;
        background: linear-gradient(to bottom, rgba(0,0,0,0.8), transparent);
    }

    .chart-container {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
        margin-top: 3rem;
    }

    .chart-item {
        position: relative;
        background: rgba(200, 166, 117, 0.05);
        border-radius: 20px;
        padding: 2rem;
        height: 300px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(200, 166, 117, 0.1);
        transition: transform 0.3s ease;
    }

    .chart-item:hover {
        transform: translateY(-5px);
    }

    .chart-overlay {
        position: absolute;
        top: 1rem;
        right: 1rem;
        padding: 1rem;
        background: rgba(0, 0, 0, 0.7);
        border-radius: 10px;
        backdrop-filter: blur(5px);
    }

    .chart-info {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .chart-label {
        color: var(--gray);
        font-size: 0.9rem;
    }

    .chart-value {
        color: var(--primary);
        font-size: 1.5rem;
        font-weight: 700;
    }

    /* 联系我们区块样式优化 */
    .contact-us {
        padding: 8rem 0;
        position: relative;
        background: linear-gradient(
            to bottom,
            transparent,
            rgba(0, 0, 0, 0.4)
        );
    }

    .contact-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 4rem;
        align-items: center;
    }

    .contact-info h2 {
        font-size: 2.8rem;
        margin-bottom: 1.5rem;
        background: linear-gradient(135deg, 
            var(--primary-light), 
            var(--primary)
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        position: relative;
    }

    .contact-info h2::after {
        content: '';
        position: absolute;
        left: 0;
        bottom: -10px;
        width: 60px;
        height: 3px;
        background: var(--primary);
        border-radius: 2px;
    }

    .contact-info p {
        color: var(--gray);
        font-size: 1.1rem;
        line-height: 1.6;
        margin-bottom: 2rem;
    }

    .contact-methods {
        display: grid;
        gap: 2rem;
    }

    .contact-item {
        display: flex;
        align-items: center;
        gap: 1.5rem;
        padding: 1.5rem;
        background: rgba(200, 166, 117, 0.05);
        border-radius: 15px;
        border: 1px solid rgba(200, 166, 117, 0.1);
        transition: all 0.3s ease;
    }

    .contact-item:hover {
        transform: translateX(10px);
        background: rgba(200, 166, 117, 0.1);
        border-color: rgba(200, 166, 117, 0.3);
    }

    .contact-item i {
        font-size: 1.8rem;
        color: var(--primary);
        transition: all 0.3s ease;
    }

    .contact-item:hover i {
        transform: scale(1.2);
        color: var(--primary-light);
    }

    .contact-item span {
        font-size: 1.1rem;
        color: var(--light);
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
        .contact-info h2 {
            font-size: 2.2rem;
        }
        
        .contact-item {
            padding: 1rem;
        }
    }
</style>
</head>
<body>
    <!-- 在body开始处添加防扒站水印 -->
    <div id="watermark" style="display:none;position:fixed;top:0;left:0;width:100%;height:100%;pointer-events:none;z-index:9999;"></div>
    <script>
        // 添加随机水印
        function addWatermark() {
            const watermark = document.getElementById('watermark');
            const timestamp = new Date().getTime();
            const randomStr = Math.random().toString(36).substr(2);
            const text = `${timestamp}-${randomStr}`;
            
            watermark.style.background = `repeating-linear-gradient(45deg, rgba(200,166,117,0.05), rgba(200,166,117,0.05) 10px, transparent 10px, transparent 20px)`;
            watermark.setAttribute('data-watermark', text);
        }
        
        // 定期更新水印
        setInterval(addWatermark, 1000);
        
        // 防止元素审查
        setInterval(() => {
            if (window.outerWidth - window.innerWidth > 160 || 
                window.outerHeight - window.innerHeight > 160) {
                document.body.innerHTML = '检测到开发者工具，页面已被禁用';
            }
        }, 1000);
    </script>
    <div class="stars-container"></div>
    <div class="background-animation"></div>
    
    <header class="header">
        <div class="container">
            <a href="/" class="logo">
                {if !empty($logo)}
                <img src="{$logo}" alt="{$siteName}" class="logo-img">
                {else}
                <i class="fas fa-credit-card"></i>
                {/if}
            </a>
            
            <nav class="nav-links">
                {foreach $navItems as $nav}
                <div class="nav-item">
                    <a href="{$nav.href}" class="nav-link {if !empty($nav.children)}has-arrow{/if}">
                        {$nav.name}
                        {if !empty($nav.children)}
                        <svg class="dropdown-arrow" width="8" height="6" viewBox="0 0 8 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M4 6L0 0L8 0L4 6Z" fill="var(--primary)"/>
                        </svg>
                        {/if}
                    </a>
                    {if !empty($nav.children)}
                    <div class="submenu">
                        {foreach $nav.children as $child}
                        <a href="{$child.href}">{$child.name}</a>
                        {/foreach}
                    </div>
                    {/if}
                </div>
                {/foreach}
            </nav>
            <div class="auth-buttons">
                <a href="/merchant/login" class="btn btn-login">
                    <svg class="auth-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M20 21V19C20 16.7909 18.2091 15 16 15H8C5.79086 15 4 16.7909 4 19V21" stroke="var(--primary)" stroke-width="2" stroke-linecap="round"/>
                        <circle cx="12" cy="7" r="4" stroke="var(--primary)" stroke-width="2"/>
                    </svg>
                    商户登录
                </a>
                <a href="/merchant/register" class="btn btn-register">
                    <svg class="auth-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M16 21V19C16 16.7909 14.2091 15 12 15H8C5.79086 15 4 16.7909 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        <circle cx="10" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                        <path d="M20 8V14M17 11H23" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                    </svg>
                    商户注册
                </a>
            </div>
            <button class="mobile-menu-btn">
                <i class="fas fa-bars"></i>
            </button>
        </div>
    </header>

    <section class="hero">
        <div class="planets-container">
            <div class="planet-orbit orbit-1"></div>
            <div class="planet-orbit orbit-2"></div>
            <div class="planet-orbit orbit-3"></div>
            <div class="planet planet-main"></div>
            <div class="planet planet-main-left"></div>
            <div class="planet planet-small-1"></div>
            <div class="planet planet-small-2"></div>
        </div>
        <div class="container hero-content">
            <h1>24H自动发货</h1>
            <h2>{$siteName}</h2>
            <p style="font-size: 1rem; color: #999; margin-top: 1rem;">服务器数据库集群化部署，金融级容灾方案，客户信息数据安全放在第一位</p>
            
            <!-- 添加3D球体容器 -->
            <div class="sphere-container">
                <div class="orbit orbit-1"></div>
                <div class="orbit orbit-2"></div>
                <div class="planet planet-small-1"></div>
                <div class="planet planet-small-2"></div>
                <div class="sphere-decorations">
                    <div class="sphere-rays"></div>
                    <div class="sphere-ring-decoration decoration-left"></div>
                    <div class="sphere-ring-decoration decoration-right"></div>
                    <div class="floating-particles"></div>
                    <div class="hologram"></div>
                    <div class="energy-ripple"></div>
                    <div class="energy-ripple" style="animation-delay: 1s;"></div>
                    <div class="energy-ripple" style="animation-delay: 2s;"></div>
                </div>
                <div class="sphere">
                    <!-- 创建更密集的经纬线网格球体 -->
                    <div class="sphere-grid">
                        <!-- 增加更多经线 -->
                        {for start="0" end="180" step="10"}
                        <div class="longitude-line" style="--rotation: {$i}deg"></div>
                        {/for}
                        <!-- 增加更多纬线 -->
                        {for start="5" end="95" step="5"}
                        <div class="latitude-line" style="--position: {$i}%"></div>
                        {/for}
                    </div>
                </div>
            </div>

            <div class="stats">
                <div class="stat-item">
                    <div class="stat-number">{$stats_orders}</div>
                    <div>完成订单</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{$stats_cards}</div>
                    <div>发卡次数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{$stats_merchants}</div>
                    <div>商户累计</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{$stats_amount}</div>
                    <div>交易金额</div>
                </div>
            </div>
        </div>
    </section>

    <section class="payment-icons">
        <div class="container">
            <h2 class="section-title">支持支付方式</h2>
            <p class="section-subtitle">多种支付渠道，安全便捷</p>
            <div class="icons-grid">
                <div class="icon-item">
                    <div class="icon-wrapper">
                        <svg viewBox="0 0 100 100" class="payment-icon">
                            <defs>
                                <linearGradient id="iconGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color: var(--primary-light)"/>
                                    <stop offset="100%" style="stop-color: var(--primary-dark)"/>
                                </linearGradient>
                            </defs>
                            <rect x="20" y="20" width="60" height="60" rx="10" fill="none" stroke="url(#iconGradient1)" stroke-width="4"/>
                            <!-- 企鹅图形 -->
                            <path d="
                                M50 35 
                                L42 45 
                                L58 45
                                L50 35
                                M42 45
                                L40 60
                                L46 55
                                L50 60
                                L54 55
                                L60 60
                                L58 45
                                Z" 
                                fill="url(#iconGradient1)"
                            />
                        </svg>
                        <div class="icon-glow"></div>
                    </div>
                    <span>QQ支付</span>
                </div>
                <div class="icon-item">
                    <div class="icon-wrapper">
                        <svg viewBox="0 0 100 100" class="payment-icon">
                            <defs>
                                <linearGradient id="iconGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color: var(--primary-light)"/>
                                    <stop offset="100%" style="stop-color: var(--primary-dark)"/>
                                </linearGradient>
                            </defs>
                            <rect x="20" y="20" width="60" height="60" rx="10" fill="none" stroke="url(#iconGradient2)" stroke-width="4"/>
                            <!-- 对勾图形 -->
                            <path d="M35 50 L45 60 L65 40" 
                                  fill="none" 
                                  stroke="url(#iconGradient2)" 
                                  stroke-width="4" 
                                  stroke-linecap="round" 
                                  stroke-linejoin="round"/>
                        </svg>
                        <div class="icon-glow"></div>
                    </div>
                    <span>微信支付</span>
                </div>
                <div class="icon-item">
                    <div class="icon-wrapper">
                        <svg viewBox="0 0 100 100" class="payment-icon">
                            <defs>
                                <linearGradient id="iconGradient3" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color: var(--primary-light)"/>
                                    <stop offset="100%" style="stop-color: var(--primary-dark)"/>
                                </linearGradient>
                            </defs>
                            <rect x="20" y="20" width="60" height="60" rx="10" fill="none" stroke="url(#iconGradient3)" stroke-width="4"/>
                            <text x="50" y="60" text-anchor="middle" fill="url(#iconGradient3)" font-size="24">支</text>
                        </svg>
                        <div class="icon-glow"></div>
                    </div>
                    <span>支付宝</span>
                </div>
                <div class="icon-item">
                    <div class="icon-wrapper">
                        <svg viewBox="0 0 100 100" class="payment-icon">
                            <defs>
                                <linearGradient id="iconGradient4" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color: var(--primary-light)"/>
                                    <stop offset="100%" style="stop-color: var(--primary-dark)"/>
                                </linearGradient>
                            </defs>
                            <rect x="20" y="20" width="60" height="60" rx="10" fill="none" stroke="url(#iconGradient4)" stroke-width="4"/>
                            <text x="50" y="60" text-anchor="middle" fill="url(#iconGradient4)" font-size="24">银</text>
                        </svg>
                        <div class="icon-glow"></div>
                    </div>
                    <span>银联支付</span>
                </div>
                <div class="icon-item">
                    <div class="icon-wrapper">
                        <svg viewBox="0 0 100 100" class="payment-icon">
                            <defs>
                                <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color: var(--primary-light)"/>
                                    <stop offset="100%" style="stop-color: var(--primary-dark)"/>
                                </linearGradient>
                            </defs>
                            <rect x="20" y="20" width="60" height="60" rx="10" fill="none" stroke="url(#iconGradient)" stroke-width="4"/>
                            <text x="50" y="60" text-anchor="middle" fill="url(#iconGradient)" font-size="24">京</text>
                        </svg>
                        <div class="icon-glow"></div>
                    </div>
                    <span>京东支付</span>
                </div>
                <div class="icon-item">
                    <div class="icon-wrapper">
                        <svg viewBox="0 0 100 100" class="payment-icon">
                            <defs>
                                <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color: var(--primary-light)"/>
                                    <stop offset="100%" style="stop-color: var(--primary-dark)"/>
                                </linearGradient>
                            </defs>
                            <rect x="20" y="20" width="60" height="60" rx="10" fill="none" stroke="url(#iconGradient)" stroke-width="4"/>
                            <text x="50" y="60" text-anchor="middle" fill="url(#iconGradient)" font-size="24">云</text>
                        </svg>
                        <div class="icon-glow"></div>
                    </div>
                    <span>云闪付</span>
                </div>
            </div>
        </div>
    </section>

    <section class="features">
        <div class="container">
            <div class="features-grid">
                <div class="feature-item">
                    <i class="fas fa-box-open feature-icon"></i>
                    <h3>丰富的产品选择</h3>
                    <p>我们提供一个虚拟数字商品平台，包含全网各类虚拟品，常用软件可以找到满足你需求的商品。</p>
                </div>
                <div class="feature-item">
                    <i class="fas fa-shield-alt feature-icon"></i>
                    <h3>高度安全的购物环境</h3>
                    <p>所有交易均走正规支付通道，拒绝使用易支付，三网免挂接口，让您可以安心购物。</p>
                </div>
                <div class="feature-item">
                    <i class="fas fa-headset feature-icon"></i>
                    <h3>专业的客户支持</h3>
                    <p>我们的客服团队由经验丰富的专业人员组成，确保商品销售之后处理，防止商品判定之类的难题。</p>
                </div>
            </div>
        </div>
    </section>

    <section class="steps">
        <div class="container">
            <h2 class="section-title">简单入驻 只需三步</h2>
            <p class="section-subtitle">这比你想象的更容易，遵循几个简单的步骤</p>
            <div class="steps-container">
                <div class="step">
                    <div class="step-circle">
                        <div class="step-number">01</div>
                        <div class="step-icon">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <svg class="progress-ring" width="120" height="120">
                            <circle class="progress-ring-circle" cx="60" cy="60" r="54" />
                        </svg>
                    </div>
                    <h3>注册上架</h3>
                    <p>快速注册商户账号<br>轻松上架商品</p>
                </div>
                <div class="step">
                    <div class="step-circle">
                        <div class="step-number">02</div>
                        <div class="step-icon">
                            <i class="fas fa-handshake"></i>
                        </div>
                        <svg class="progress-ring" width="120" height="120">
                            <circle class="progress-ring-circle" cx="60" cy="60" r="54" />
                        </svg>
                    </div>
                    <h3>托管交易</h3>
                    <p>安全托管资金<br>保障双方权益</p>
                </div>
                <div class="step">
                    <div class="step-circle">
                        <div class="step-number">03</div>
                        <div class="step-icon">
                            <i class="fas fa-wallet"></i>
                        </div>
                        <svg class="progress-ring" width="120" height="120">
                            <circle class="progress-ring-circle" cx="60" cy="60" r="54" />
                        </svg>
                    </div>
                    <h3>即时到账</h3>
                    <p>交易完成后<br>资金秒速到账</p>
                </div>
            </div>
        </div>
    </section>

    <section class="contact-us">
        <div class="container">
            <div class="contact-grid">
                <div class="contact-info">
                    <h2>联系我们</h2>
                    <p>如果您有任何问题，请随时与我们联系</p>
                    <div class="contact-methods">
                        <div class="contact-item">
                            <i class="fas fa-envelope"></i>
                            <span>{$contact_email}</span>
                        </div>
                        <div class="contact-item">
                            <i class="fab fa-qq"></i>
                            <span>QQ客服: {$contact_qq}</span>
                        </div>
                        <div class="contact-item">
                            <i class="fab fa-weixin"></i>
                            <span>微信客服: {$contact_wx}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <footer class="footer">
        <div class="container">
            <div class="footer-grid">
                {if $footer_service_show == 1}
                <div class="footer-column">
                    <h3>服务中心</h3>
                    <a href="{$footer_service_1_link|default='#'}">{$footer_service_1|default='卡密查询'}</a>
                    <a href="{$footer_service_2_link|default='#'}">{$footer_service_2|default='投诉中心'}</a>
                    <a href="{$footer_service_3_link|default='#'}">{$footer_service_3|default='卡密工具'}</a>
                    <a href="{$footer_service_4_link|default='#'}">{$footer_service_4|default='商户入驻'}</a>
                </div>
                {/if}
                
                {if $footer_help_show == 1}
                <div class="footer-column">
                    <h3>帮助中心</h3>
                    <a href="{$footer_help_1_link|default='#'}">{$footer_help_1|default='常见问题'}</a>
                    <a href="{$footer_help_2_link|default='#'}">{$footer_help_2|default='系统公告'}</a>
                    <a href="{$footer_help_3_link|default='#'}">{$footer_help_3|default='结算公告'}</a>
                    <a href="{$footer_help_4_link|default='#'}">{$footer_help_4|default='新闻动态'}</a>
                </div>
                {/if}
                
                {if $footer_legal_show == 1}
                <div class="footer-column">
                    <h3>法律责任</h3>
                    <a href="{$footer_legal_1_link|default='#'}">{$footer_legal_1|default='免责声明'}</a>
                    <a href="{$footer_legal_2_link|default='#'}">{$footer_legal_2|default='禁售商品'}</a>
                    <a href="{$footer_legal_3_link|default='#'}">{$footer_legal_3|default='服务协议'}</a>
                    <a href="{$footer_legal_4_link|default='#'}">{$footer_legal_4|default='隐私政策'}</a>
                </div>
                {/if}
                
                {if $footer_links_show == 1}
                <div class="footer-column">
                    <h3>友情链接</h3>
                    <a href="{$footer_links_1_link|default='#'}">{$footer_links_1|default='一意支付'}</a>
                    <a href="{$footer_links_2_link|default='#'}">{$footer_links_2|default='支付宝'}</a>
                    <a href="{$footer_links_3_link|default='#'}">{$footer_links_3|default='微信支付'}</a>
                    <a href="{$footer_links_4_link|default='#'}">{$footer_links_4|default='QQ钱包'}</a>
                </div>
                {/if}
            </div>
            <div class="copyright">
                <span>{$siteName|default='Blackglod'} - 版权所有 © {$year|default='2022'}-至今</span>
                {if !empty($icpNumber)}
                <a href="https://beian.miit.gov.cn/" target="_blank">{$icpNumber}</a>
                {/if}
                {if !empty($gaNumber)}
                <a href="{$icpCert}" target="_blank">{$gaNumber}</a>
                {/if}
            </div>
        </div>
    </footer>

    <div class="particles-container"></div>

    <script>
        // 页面滚动时的头部效果
        window.addEventListener('scroll', () => {
            const header = document.querySelector('.header');
            if (window.scrollY > 50) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
        });

        // 调整金额字体大小
        function adjustAmountFontSize(element) {
            const amount = element.textContent.replace(/,/g, '');
            const length = amount.split('.')[0].length; // 只计算整数部分的长度
            
            // 移除所有长度相关的类
            element.classList.remove('length-9', 'length-10', 'length-11', 'length-12');
            
            // 添加对应长度的类
            if (length >= 9) {
                element.classList.add(`length-${length}`);
            }
        }

        // 修改原有的animateValue函数
        function animateValue(element, start, end, duration) {
            let startTimestamp = null;
            const decimal = String(end).includes('.') ? String(end).split('.')[1].length : 0;
            const isAmount = element.closest('.stat-item').querySelector('div:last-child').textContent === '交易金额';
            
            if (isAmount) {
                element.classList.add('amount');
            }
            
            // 确保元素可见
            element.style.opacity = "1";
            
            const step = (timestamp) => {
                if (!startTimestamp) startTimestamp = timestamp;
                const progress = Math.min((timestamp - startTimestamp) / duration, 1);
                const currentValue = progress * (end - start) + start;
                
                element.textContent = decimal ? 
                    currentValue.toFixed(decimal) : 
                    Math.floor(currentValue).toLocaleString();
                    
                if (isAmount) {
                    adjustAmountFontSize(element);
                }
            
                if (progress < 1) {
                    window.requestAnimationFrame(step);
                }
            };
            
            window.requestAnimationFrame(step);
        }

        // 观察者设置
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    // 立即设置可见性
                    entry.target.style.opacity = "1";
                    
                    if (!entry.target.classList.contains('animated')) {
                        entry.target.classList.add('visible', 'animated');
                        const finalValue = parseFloat(entry.target.textContent.replace(/,/g, ''));
                        animateValue(entry.target, 0, finalValue, 2000);
                    }
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '50px'
        });

        // 当页面加载完成后开始观察
        document.addEventListener('DOMContentLoaded', () => {
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(stat => {
                // 确保元素默认可见
                stat.style.opacity = "1";
                observer.observe(stat);
            });
        });

        // 移动端菜单
        const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
        const navLinks = document.querySelector('.nav-links');
        
        mobileMenuBtn.addEventListener('click', () => {
            navLinks.style.display = navLinks.style.display === 'flex' ? 'none' : 'flex';
        });

    // 创建星星
    function createStars() {
        const container = document.querySelector('.stars-container');
        const fragment = document.createDocumentFragment();
        const starCount = window.innerWidth > 768 ? 120 : 60;

        for (let i = 0; i < starCount; i++) {
            const star = document.createElement('div');
            star.className = 'star';
            
            const x = Math.random() * 100;
            const y = Math.random() * 100;
            const size = Math.random() * 6 + 2;
            const delay = Math.random() * 5;
            const duration = Math.random() * 3 + 2;
            const opacity = Math.random() * 0.8 + 0.4;

            star.style.cssText = `
                left: ${x}%;
                top: ${y}%;
                width: ${size}px;
                height: ${size}px;
                --delay: ${delay}s;
                --duration: ${duration}s;
                --opacity: ${opacity};
            `;
            
            fragment.appendChild(star);
        }
        
        container.appendChild(fragment);
    }

    // 页面加载完成后创建星星
    document.addEventListener('DOMContentLoaded', createStars);

    // 为星星添加视差效果
    let mouseMoveTimeout;
    document.addEventListener('mousemove', (e) => {
        if (!mouseMoveTimeout) {
            requestAnimationFrame(() => {
                const stars = document.querySelectorAll('.star');
                const mouseX = e.clientX / window.innerWidth - 0.5;
                const mouseY = e.clientY / window.innerHeight - 0.5;

                stars.forEach(star => {
                    const depth = Math.random() * 5;
                    star.style.transform = `translate3d(${mouseX * depth}px, ${mouseY * depth}px, 0)`;
                });
                mouseMoveTimeout = null;
            });
            mouseMoveTimeout = true;
        }
    });

    // 添加页面加载进度条动画
    document.addEventListener('DOMContentLoaded', () => {
        const progressBar = document.querySelector('.progress-bar');
        progressBar.style.transform = 'scaleX(1)';
        setTimeout(() => {
            progressBar.style.opacity = '0';
        }, 1000);
    });

    // 添加页面滚动时的平滑动画
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            document.querySelector(this.getAttribute('href')).scrollIntoView({
                behavior: 'smooth'
            });
        });
    });

    // 创建高级粒子动画
    function createParticles() {
        const container = document.querySelector('.particles-container');
        const particleCount = 50;
        
        function createParticle() {
            const particle = document.createElement('div');
            particle.className = 'particle';
            
            // 随机大小
            const size = Math.random() * 4 + 2;
            
            // 随机位置
            const x = Math.random() * window.innerWidth;
            const y = Math.random() * window.innerHeight;
            
            // 随机移动参数     
            const translateX = (Math.random() - 0.5) * 200;
            const translateY = (Math.random() - 0.5) * 200;
            const rotate = Math.random() * 360;
            const scale = Math.random() * 0.5 + 0.5;
            const opacity = Math.random() * 0.5 + 0.3;
            
            particle.style.cssText = `
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                --particle-translateX: ${translateX}px;
                --particle-translateY: ${translateY}px;
                --particle-rotate: ${rotate}deg;
                --particle-scale: ${scale};
                --particle-opacity: ${opacity};
            `;
            
            container.appendChild(particle);
            
            
            if (Math.random() > 0.7) {
                createLine(x, y, translateX, translateY);
            }
            
            
            particle.addEventListener('animationend', () => {
                particle.remove();
                createParticle();
            });
        }
        
        function createLine(x1, y1, x2, y2) {
            const line = document.createElement('div');
            line.className = 'particle-line';
            
            const length = Math.sqrt(Math.pow(x2, 2) + Math.pow(y2, 2));
            const angle = Math.atan2(y2, x2) * (180 / Math.PI);
            
            line.style.cssText = `
                left: ${x1}px;
                top: ${y1}px;
                width: ${length}px;
                transform: rotate(${angle}deg);
            `;
            
            container.appendChild(line);
            
            
            line.addEventListener('animationend', () => {
                line.remove();
            });
        }
        
        
        for (let i = 0; i < particleCount; i++) {
            createParticle();
        }
    }


    document.addEventListener('DOMContentLoaded', createParticles);

    
    document.addEventListener('DOMContentLoaded', () => {
        const sphereGrid = document.querySelector('.sphere-grid');
        
            
        for (let i = 0; i < 180; i += 10) {
            const line = document.createElement('div');
            line.className = 'longitude-line';
            line.style.setProperty('--rotation', `${i}deg`);
            sphereGrid.appendChild(line);
        }
        
            
        for (let i = 5; i < 175; i += 5) {
            const line = document.createElement('div');
            line.className = 'latitude-line';
            // 计算缩放比例，使用余弦函数创造球形效果
            const scale = Math.abs(Math.cos(i * Math.PI / 180));
            line.style.setProperty('--rotation', `${i}deg`);
            line.style.setProperty('--scale', scale);
            sphereGrid.appendChild(line);
        }
    });

    
    function addFloatingParticles() {
        const container = document.querySelector('.floating-particles');
        const particleCount = 8;
        
        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'floating-particle';
            
            // 随机位置和移动
            const x = Math.random() * 100;
            const y = Math.random() * 100;
            const tx = (Math.random() - 0.5) * 40;
            const ty = (Math.random() - 0.5) * 40;
            
            particle.style.cssText = `
                left: ${x}%;
                top: ${y}%;
                --tx: ${tx}px;
                --ty: ${ty}px;
                animation-delay: ${Math.random() * 2}s;
            `;
            
            container.appendChild(particle);
        }
    }

    document.addEventListener('DOMContentLoaded', () => {
        addFloatingParticles();
    });

    function createDataStreams() {
        const container = document.querySelector('.sphere-decorations');
        const streamCount = 12;
        
        for (let i = 0; i < streamCount; i++) {
            const stream = document.createElement('div');
            stream.className = 'data-stream';
            
            // 随机起点和方向
            const startX = Math.random() * 100;
            const startY = Math.random() * 100;
            const tx = (Math.random() - 0.5) * 100;
            const ty = (Math.random() - 0.5) * 100;
            
            stream.style.cssText = `
                left: ${startX}%;
                top: ${startY}%;
                --tx: ${tx}px;
                --ty: ${ty}px;
                animation-delay: ${Math.random() * 3}s;
            `;
            
            container.appendChild(stream);
        }
    }

    function createConnectionLines() {
        const container = document.querySelector('.sphere-decorations');
        const lineCount = 6;
        
        for (let i = 0; i < lineCount; i++) {
            const line = document.createElement('div');
            line.className = 'connection-line';
            
            const angle = (i / lineCount) * 360;
            const length = 40 + Math.random() * 40;
            
            line.style.cssText = `
                width: ${length}px;
                left: 50%;
                top: 50%;
                transform: rotate(${angle}deg) translateX(-50%);
                animation-delay: ${Math.random() * 3}s;
            `;
            
            container.appendChild(line);
        }
    }

    document.addEventListener('DOMContentLoaded', () => {
        addFloatingParticles();
        createDataStreams();
        createConnectionLines();
    });
    function initCharts() {
        const transactionCtx = document.getElementById('transactionChart').getContext('2d');
        new Chart(transactionCtx, {
            type: 'line',
            data: {
                labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
                datasets: [{
                    label: '交易量',
                    data: [65, 59, 80, 81, 56, 55],
                    borderColor: '#C8A675',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
        
        const successCtx = document.getElementById('successRateChart').getContext('2d');
        new Chart(successCtx, {
            type: 'doughnut',
            data: {
                labels: ['成功', '失败'],
                datasets: [{
                    data: [99.9, 0.1],
                    backgroundColor: ['#C8A675', '#666666']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    }
    document.addEventListener('DOMContentLoaded', () => {
        initCharts();
    });
    </script>
</body>
</html>