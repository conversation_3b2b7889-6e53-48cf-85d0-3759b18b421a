#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
基于Selenium的自动化注册器
使用浏览器自动化完成970faka.com注册流程
"""

import time
import random
import string
from typing import Dict, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.chrome.service import Service as ChromeService
from webdriver_manager.chrome import ChromeDriverManager
import sys
import os
# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from core.sms_api import YeziCloudAPI
from config.config_manager import get_config
from browser_automation.element_helper import ElementHelper
from browser_automation.xpath_config import XPathConfig

class SeleniumRegister:
    """Selenium自动化注册器"""
    
    def __init__(self, headless: bool = False, use_yezi_cloud: bool = True, base_url: str = "https://970faka.com"):
        """
        初始化Selenium注册器

        Args:
            headless: 是否无头模式运行
            use_yezi_cloud: 是否使用椰子云短信服务
            base_url: 注册网站的基础域名
        """
        self.driver = None
        self.headless = headless
        self.use_yezi_cloud = use_yezi_cloud
        self.base_url = base_url.rstrip('/')  # 移除末尾的斜杠
        self.yezi_api = None
        self.wait = None
        self.setup_driver()
    
    def setup_driver(self):
        """设置Chrome驱动"""
        try:
            print("[设置] 正在设置Chrome驱动...")

            chrome_options = Options()

            if self.headless:
                chrome_options.add_argument('--headless')
                print("[静音] 启用无头模式")

            # 基本设置
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_argument('--disable-web-security')
            chrome_options.add_argument('--allow-running-insecure-content')
            chrome_options.add_argument('--disable-extensions')

            # 反检测设置
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # 设置User-Agent
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.6778.86 Safari/537.36')

            # 使用webdriver_manager自动管理ChromeDriver
            print("[下载] 正在下载/更新ChromeDriver...")
            driver_created = False

            # 方法1: 尝试webdriver_manager
            try:
                service = ChromeService(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
                print("[成功] 使用webdriver_manager成功创建Chrome驱动")
                driver_created = True
            except Exception as wm_error:
                # 检查是否是网络连接问题
                error_msg = str(wm_error)
                if "Could not reach host" in error_msg or "Are you offline" in error_msg:
                    print("[警告] webdriver_manager网络连接失败，尝试使用本地驱动...")
                else:
                    print(f"[警告] webdriver_manager失败: {wm_error}")

            # 方法2: 尝试系统PATH中的ChromeDriver
            if not driver_created:
                try:
                    print("[重试] 尝试使用系统PATH中的ChromeDriver...")
                    self.driver = webdriver.Chrome(options=chrome_options)
                    print("[成功] 使用系统ChromeDriver成功")
                    driver_created = True
                except Exception as path_error:
                    print(f"[警告] 系统ChromeDriver失败: {path_error}")

            # 方法3: 尝试当前目录的ChromeDriver
            if not driver_created:
                try:
                    print("[重试] 尝试使用当前目录的ChromeDriver...")
                    import os
                    current_dir = os.getcwd()
                    chromedriver_path = os.path.join(current_dir, "chromedriver.exe" if os.name == 'nt' else "chromedriver")

                    if os.path.exists(chromedriver_path):
                        service = ChromeService(chromedriver_path)
                        self.driver = webdriver.Chrome(service=service, options=chrome_options)
                        print("[成功] 使用当前目录ChromeDriver成功")
                        driver_created = True
                    else:
                        print("[警告] 当前目录未找到ChromeDriver")
                except Exception as local_error:
                    print(f"[警告] 当前目录ChromeDriver失败: {local_error}")

            if not driver_created:
                raise Exception("所有ChromeDriver方法都失败了")

            # 执行反检测脚本
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            self.wait = WebDriverWait(self.driver, 20)

            # 初始化元素助手
            self.element_helper = ElementHelper(self.driver, default_timeout=20)

            print("[成功] Chrome驱动初始化成功")

        except Exception as e:
            print(f"[失败] Chrome驱动初始化失败: {e}")
            print("\n[设置] 解决方案:")
            print("1. 确保已安装Chrome浏览器")
            print("2. 运行修复工具: python fix_chromedriver.py")
            print("3. 或运行离线修复: python fix_chromedriver_offline.py")
            print("4. 手动下载ChromeDriver: https://googlechromelabs.github.io/chrome-for-testing/")
            print("5. 检查网络连接是否正常")
            print("\n[提示] 快速修复:")
            print("   python quick_start.py  # 自动检查和修复")
            raise
    
    def setup_yezi_cloud(self, username: str, password: str, project_id: str, special: str = "") -> bool:
        """
        设置椰子云API

        Args:
            username: 椰子云用户名
            password: 椰子云密码
            project_id: 椰子云项目ID
            special: 专属项目参数（如果是专属项目，填写"1"）

        Returns:
            bool: 设置是否成功
        """
        try:
            self.yezi_api = YeziCloudAPI(username=username, password=password)
            login_result = self.yezi_api.login()

            if login_result['success']:
                self.project_id = project_id
                self.special_param = special  # 保存special参数
                print(f"[成功] 椰子云登录成功，余额: {login_result.get('balance', '0')}")
                if special:
                    print(f"[密钥] 使用专属项目模式: special={special}")
                return True
            else:
                print(f"[失败] 椰子云登录失败: {login_result['error']}")
                return False

        except Exception as e:
            print(f"[失败] 椰子云设置失败: {e}")
            return False
    
    def generate_user_data(self) -> Dict[str, str]:
        """生成随机用户数据"""
        username = ''.join(random.choices(string.digits, k=10))
        password = username  # 使用相同的用户名和密码
        
        return {
            'username': username,
            'password': password
        }
    
    def get_yezi_mobile(self) -> Optional[str]:
        """从椰子云获取手机号"""
        if not self.yezi_api:
            return None

        try:
            print("[手机] 从椰子云获取手机号...")

            # 使用special参数（如果设置了）
            special_param = getattr(self, 'special_param', '')
            if special_param:
                print(f"[密钥] 使用专属项目参数: special={special_param}")
                mobile_result = self.yezi_api.get_mobile(
                    project_id=self.project_id,
                    special=special_param
                )
            else:
                mobile_result = self.yezi_api.get_mobile(project_id=self.project_id)

            if mobile_result['success']:
                mobile = mobile_result['mobile']
                print(f"[成功] 获得手机号: {mobile}")
                return mobile
            else:
                print(f"[失败] 获取手机号失败: {mobile_result['error']}")
                return None

        except Exception as e:
            print(f"[失败] 获取手机号异常: {e}")
            return None
    
    def get_sms_code(self, mobile: str, max_wait: int = 60) -> Optional[str]:
        """从椰子云获取短信验证码"""
        if not self.yezi_api:
            return None
        
        try:
            print(f"[短信] 等待短信验证码...")
            start_time = time.time()
            
            while time.time() - start_time < max_wait:
                # 使用special参数（如果设置了）
                special_param = getattr(self, 'special_param', '')
                if special_param:
                    sms_result = self.yezi_api.get_message(
                        project_id=self.project_id,
                        phone_num=mobile,
                        special=special_param
                    )
                else:
                    sms_result = self.yezi_api.get_message(
                        project_id=self.project_id,
                        phone_num=mobile
                    )
                
                if sms_result['success'] and sms_result.get('messages'):
                    # 使用兼容格式：检查messages字段
                    for message in sms_result['messages']:
                        content = message.get('content', '')
                        code = message.get('code', '')

                        # 如果直接有code字段，使用它
                        if code:
                            print(f"[成功] 获得短信验证码: {code}")

                            # 自动拉黑手机号
                            print("[禁止] 自动拉黑手机号...")
                            try:
                                blacklist_result = self.yezi_api.blacklist_mobile(
                                    project_id=self.project_id,
                                    phone_num=mobile
                                )
                                if blacklist_result['success']:
                                    print(f"[成功] 手机号已自动拉黑: {mobile}")
                                else:
                                    print(f"[警告] 拉黑失败: {blacklist_result.get('error', '未知错误')}")
                            except Exception as blacklist_error:
                                print(f"[警告] 拉黑异常: {blacklist_error}")

                            return code

                        # 如果没有直接的code字段，从content中提取
                        elif content:
                            import re
                            code_match = re.search(r'\b(\d{4,6})\b', content)
                            if code_match:
                                code = code_match.group(1)
                                print(f"[成功] 从内容中提取验证码: {code}")

                                # 自动拉黑手机号
                                print("[禁止] 自动拉黑手机号...")
                                try:
                                    blacklist_result = self.yezi_api.blacklist_mobile(
                                        project_id=self.project_id,
                                        phone_num=mobile
                                    )
                                    if blacklist_result['success']:
                                        print(f"[成功] 手机号已自动拉黑: {mobile}")
                                    else:
                                        print(f"[警告] 拉黑失败: {blacklist_result.get('error', '未知错误')}")
                                except Exception as blacklist_error:
                                    print(f"[警告] 拉黑异常: {blacklist_error}")

                                return code

                
                time.sleep(3)  # 每3秒检查一次
                print("⏳ 继续等待短信...")
            
            print(f"[失败] 等待短信超时（{max_wait}秒）")
            return None
            
        except Exception as e:
            print(f"[失败] 获取短信验证码异常: {e}")
            return None
    
    def register_user(self, username: str, password: str, mobile: str = None,
                     invite_code: str = '', invite_token: str = '', **kwargs) -> Dict:
        """
        执行用户注册

        Args:
            username: 用户名
            password: 密码
            mobile: 手机号（可选，如果不提供则从椰子云获取）
            invite_code: 邀请码（可选）
            invite_token: 邀请令牌（可选）
            **kwargs: 其他参数（为了兼容性）

        Returns:
            dict: 注册结果
        """
        try:
            print(f"[启动] 开始注册用户: {username}")
            
            # 如果没有提供手机号且启用了椰子云，则获取手机号
            if not mobile and self.use_yezi_cloud and self.yezi_api:
                mobile = self.get_yezi_mobile()
                if not mobile:
                    return {
                        'success': False,
                        'error': '无法获取手机号',
                        'step': 'get_mobile'
                    }
            
            if not mobile:
                return {
                    'success': False,
                    'error': '未提供手机号且椰子云未配置',
                    'step': 'mobile_required'
                }
            
            # 第一步：访问注册页面
            register_url = f"{self.base_url}/merchant/register"
            print(f"[网络] 访问注册页面: {register_url}")
            self.driver.get(register_url)
            
            # 等待页面加载
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            time.sleep(2)

            # 检查弹窗是否存在，如果存在则点击按钮
            popup_container_xpath = "/html/body/div[2]/div/div[2]/div"
            popup_button_xpath = "/html/body/div[2]/div/div[2]/div/div[3]/button"
            print(f"[检查] 检查页面是否有弹窗容器: {popup_container_xpath}")

            if self.element_helper.check_element_exists(popup_container_xpath, timeout=3):
                print("[成功] 发现弹窗容器，检查按钮...")
                if self.element_helper.check_element_exists(popup_button_xpath, timeout=2):
                    print("[成功] 发现弹窗按钮，尝试点击...")
                    if self.element_helper.safe_click(popup_button_xpath, "弹窗按钮", timeout=3):
                        print("[目标] 成功点击弹窗按钮")
                        time.sleep(2)  # 等待按钮点击后的响应
                    else:
                        print("[警告] 弹窗按钮点击失败")
                else:
                    print("[警告] 弹窗容器存在但未找到按钮")
            else:
                print("[信息] 未发现弹窗容器，跳过弹窗处理，继续正常流程")
            
            # 第二步：填写注册信息
            print("[编辑] 填写注册信息...")

            # 填写用户名
            username_xpath = XPathConfig.get_form_element('username')
            if self.element_helper.safe_input(username_xpath, username, "用户名输入框"):
                print(f"[成功] 用户名已填写: {username}")
            else:
                return {
                    'success': False,
                    'error': '无法填写用户名',
                    'step': 'username_input'
                }

            # 填写密码
            password_xpath = XPathConfig.get_form_element('password')
            if self.element_helper.safe_input(password_xpath, password, "密码输入框"):
                print(f"[成功] 密码已填写")
            else:
                return {
                    'success': False,
                    'error': '无法填写密码',
                    'step': 'password_input'
                }

            # 填写确认密码
            repeat_password_xpath = XPathConfig.get_form_element('repeat_password')
            if self.element_helper.safe_input(repeat_password_xpath, password, "确认密码输入框"):
                print(f"[成功] 确认密码已填写")
            else:
                return {
                    'success': False,
                    'error': '无法填写确认密码',
                    'step': 'repeat_password_input'
                }

            # 填写手机号
            mobile_xpath = XPathConfig.get_form_element('mobile')
            if self.element_helper.safe_input(mobile_xpath, mobile, "手机号输入框"):
                print(f"[成功] 手机号已填写: {mobile}")
            else:
                return {
                    'success': False,
                    'error': '无法填写手机号',
                    'step': 'mobile_input'
                }

            # 第三步：点击获取短信验证码（会触发验证码弹窗）
            print("[手机] 点击获取短信验证码...")
            sms_button_xpath = "//*[@id=\"mobile_code\"]/div/div/span/span[2]/button"

            # 等待按钮可点击
            print("⏳ 等待获取验证码按钮可点击...")
            if not self.element_helper.check_element_exists(sms_button_xpath, timeout=10):
                return {
                    'success': False,
                    'error': '获取验证码按钮不存在',
                    'step': 'sms_button_not_found'
                }

            # 尝试多种点击方式
            button_clicked = False

            # 方法1：普通点击
            if self.element_helper.safe_click(sms_button_xpath, "获取验证码按钮", timeout=5):
                print("[成功] 普通点击成功")
                button_clicked = True
            else:
                print("[警告] 普通点击失败，尝试JavaScript点击...")

                # 方法2：JavaScript点击
                try:
                    js_script = f"""
                    var button = document.evaluate('{sms_button_xpath}', document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                    if (button) {{
                        button.click();
                        return true;
                    }}
                    return false;
                    """
                    result = self.driver.execute_script(js_script)
                    if result:
                        print("[成功] JavaScript点击成功")
                        button_clicked = True
                    else:
                        print("[失败] JavaScript点击失败")
                except Exception as js_error:
                    print(f"[失败] JavaScript点击异常: {js_error}")

            if button_clicked:
                print("[成功] 获取验证码按钮点击成功")
                time.sleep(1)  # 短暂等待弹窗出现

                # 立即处理验证码弹窗
                captcha_result = self.handle_immediate_captcha_popup()
                if not captcha_result:
                    return {
                        'success': False,
                        'error': '验证码弹窗处理失败',
                        'step': 'sms_captcha_popup'
                    }

                print("[成功] 短信验证码发送成功")
                time.sleep(2)  # 等待短信发送
            else:
                return {
                    'success': False,
                    'error': '无法点击获取验证码按钮（所有方法都失败）',
                    'step': 'sms_button_click_failed'
                }
            
            # 第五步：获取短信验证码
            if self.use_yezi_cloud and self.yezi_api:
                sms_code = self.get_sms_code(mobile)
                if not sms_code:
                    return {
                        'success': False,
                        'error': '无法获取短信验证码',
                        'step': 'sms_code'
                    }
                
                # 填写短信验证码（使用指定的XPath）
                sms_input_xpath = "//*[@id=\"mobile_code\"]/div/div/span/input"
                if self.element_helper.safe_input(sms_input_xpath, sms_code, "短信验证码输入框"):
                    print(f"[成功] 短信验证码已填写: {sms_code}")

                    # 立即进行后续操作：点击协议复选框和注册按钮
                    if self.complete_registration_steps():
                        print("[成功] 注册流程完成")
                        return {
                            'success': True,
                            'message': '注册成功',
                            'username': username,
                            'password': password,
                            'mobile': mobile
                        }
                    else:
                        return {
                            'success': False,
                            'error': '注册最后步骤失败',
                            'step': 'final_steps'
                        }
                else:
                    return {
                        'success': False,
                        'error': '无法填写短信验证码',
                        'step': 'sms_code_input'
                    }
            else:
                # 手动输入短信验证码
                print("[短信] 请手动查看短信并输入验证码")
                sms_code = input("请输入短信验证码: ").strip()
                if not sms_code:
                    return {
                        'success': False,
                        'error': '未输入短信验证码',
                        'step': 'sms_input'
                    }

                sms_input_xpath = "//*[@id=\"mobile_code\"]/div/div/span/input"
                if self.element_helper.safe_input(sms_input_xpath, sms_code, "短信验证码输入框"):
                    print(f"[成功] 短信验证码已填写: {sms_code}")

                    # 立即进行后续操作：点击协议复选框和注册按钮
                    if self.complete_registration_steps():
                        print("[成功] 注册流程完成")
                        return {
                            'success': True,
                            'message': '注册成功',
                            'username': username,
                            'password': password,
                            'mobile': mobile
                        }
                    else:
                        return {
                            'success': False,
                            'error': '注册最后步骤失败',
                            'step': 'final_steps'
                        }
                else:
                    return {
                        'success': False,
                        'error': '无法填写短信验证码',
                        'step': 'sms_code_input'
                    }


            # 注册流程已在短信验证码输入后自动完成
            print("[成功] 注册流程已完成")

            # 释放椰子云手机号
            if self.use_yezi_cloud and self.yezi_api:
                try:
                    self.yezi_api.release_mobile(project_id=self.project_id, phone_num=mobile)
                    print("[手机] 椰子云手机号已释放")
                except:
                    pass

            return {
                'success': True,
                'username': username,
                'password': password,
                'mobile': mobile,
                'message': '注册流程已完成'
            }
            
        except Exception as e:
            print(f"[失败] 注册过程异常: {e}")
            import traceback
            traceback.print_exc()
            
            return {
                'success': False,
                'error': f'注册异常: {str(e)}',
                'step': 'exception'
            }
    
    def handle_captcha_advanced(self, max_retries: int = 3) -> bool:
        """高级图形验证码处理（使用改进的ddddocr识别）"""
        try:
            # 验证码图片XPath
            captcha_img_xpath = XPathConfig.get_captcha_element('captcha_image')
            # 验证码输入框XPath
            captcha_input_xpath = XPathConfig.get_captcha_element('captcha_input')

            print("[检查] 查找验证码...")

            # 检查验证码输入框是否存在
            if not self.element_helper.check_element_exists(captcha_input_xpath, timeout=5):
                print("[信息] 未找到验证码输入框，可能不需要验证码")
                return True

            print("[成功] 找到验证码，使用改进的ddddocr自动识别")

            # 使用改进的ddddocr识别
            return self.handle_captcha_with_retry(captcha_img_xpath, captcha_input_xpath, max_retries)

        except Exception as e:
            print(f"[失败] 处理验证码失败: {e}")
            return False

    def handle_captcha_with_retry(self, captcha_img_xpath: str, captcha_input_xpath: str, max_retries: int = 3) -> bool:
        """带重试机制的验证码处理（使用改进的ddddocr识别）"""
        print("🤖 检测到验证码，使用ddddocr自动识别...")

        # 等待验证码完全加载
        time.sleep(2)

        # 检查验证码输入框是否存在
        if not self.element_helper.check_element_exists(captcha_input_xpath, timeout=5):
            print("[失败] 验证码输入框不存在")
            return False

        for attempt in range(max_retries):
            print(f"🤖 第 {attempt + 1}/{max_retries} 次尝试自动识别验证码...")

            try:
                # 点击验证码图片刷新（如果存在）
                if self.element_helper.check_element_exists(captcha_img_xpath, timeout=2):
                    print("[重试] 尝试点击验证码图片刷新...")
                    self.element_helper.safe_click(captcha_img_xpath, "验证码图片", timeout=2)
                    time.sleep(1)

                # 使用改进的ddddocr识别
                captcha_code = self.recognize_captcha_with_ddddocr(captcha_img_xpath)

                if captcha_code:
                    print(f"[目标] 第 {attempt + 1} 次识别结果: {captcha_code}")

                    # 清空输入框并填写验证码
                    if self.clear_and_input_captcha(captcha_input_xpath, captcha_code):
                        print(f"[成功] 验证码已填写: {captcha_code}")
                        return True
                    else:
                        print(f"[失败] 第 {attempt + 1} 次填写验证码失败")
                else:
                    print(f"[失败] 第 {attempt + 1} 次识别失败，验证码为空")

                if attempt < max_retries - 1:
                    print("[重试] 准备重新尝试...")
                    time.sleep(1)

            except Exception as e:
                print(f"[失败] 第 {attempt + 1} 次验证码处理异常: {e}")
                if attempt < max_retries - 1:
                    print("[重试] 准备重新尝试...")
                    time.sleep(1)

        # 如果自动识别失败，回退到手动输入
        if not self.headless:
            print("[图像] 自动识别失败，回退到手动输入...")
            return self.handle_captcha_manual(captcha_img_xpath, captcha_input_xpath)
        else:
            print(f"[失败] 验证码自动识别失败，已尝试 {max_retries} 次")
            return False

    def clear_and_input_captcha(self, input_xpath: str, captcha_code: str) -> bool:
        """清空输入框并输入新的验证码（改进版）"""
        try:
            print(f"[检查] 尝试输入验证码: {captcha_code}")

            # 等待输入框可见和可交互
            wait = WebDriverWait(self.driver, 10)
            input_element = wait.until(EC.element_to_be_clickable(("xpath", input_xpath)))

            print("[成功] 验证码输入框已找到并可交互")

            # 滚动到元素位置，确保可见
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", input_element)
            time.sleep(0.5)

            # 点击输入框获得焦点
            try:
                input_element.click()
                print("[成功] 输入框已获得焦点")
            except Exception as e:
                print(f"[警告] 点击输入框失败，尝试JavaScript点击: {e}")
                self.driver.execute_script("arguments[0].click();", input_element)

            time.sleep(0.3)

            # 多种方式清空输入框
            try:
                # 方法1: 使用clear()
                input_element.clear()
                time.sleep(0.2)

                # 方法2: 使用JavaScript清空
                self.driver.execute_script("arguments[0].value = '';", input_element)
                time.sleep(0.2)

                # 方法3: 使用键盘快捷键清空
                from selenium.webdriver.common.keys import Keys
                input_element.send_keys(Keys.CONTROL + "a")
                time.sleep(0.1)
                input_element.send_keys(Keys.DELETE)
                time.sleep(0.2)

                print("[成功] 输入框已清空")

            except Exception as e:
                print(f"[警告] 清空输入框时出现警告: {e}")

            # 输入验证码
            try:
                # 方法1: 直接输入
                input_element.send_keys(captcha_code)
                time.sleep(0.3)

                # 验证输入是否成功
                current_value = input_element.get_attribute('value')
                if current_value == captcha_code:
                    print(f"[成功] 验证码输入成功: {captcha_code}")
                    return True
                else:
                    print(f"[警告] 直接输入不匹配，尝试JavaScript输入")
                    # 方法2: 使用JavaScript输入
                    self.driver.execute_script(f"arguments[0].value = '{captcha_code}';", input_element)
                    time.sleep(0.3)

                    # 触发input事件
                    self.driver.execute_script("arguments[0].dispatchEvent(new Event('input', {bubbles: true}));", input_element)
                    self.driver.execute_script("arguments[0].dispatchEvent(new Event('change', {bubbles: true}));", input_element)

                    # 再次验证
                    current_value = input_element.get_attribute('value')
                    if current_value == captcha_code:
                        print(f"[成功] JavaScript输入成功: {captcha_code}")
                        return True
                    else:
                        print(f"[失败] 验证码输入失败，期望: {captcha_code}, 实际: {current_value}")
                        return False

            except Exception as e:
                print(f"[失败] 输入验证码失败: {e}")
                return False

        except TimeoutException:
            print(f"[失败] 等待验证码输入框超时: {input_xpath}")
            return False
        except Exception as e:
            print(f"[失败] 清空并输入验证码失败: {e}")
            return False

    def input_captcha_in_popup(self, input_xpath: str, captcha_code: str) -> bool:
        """在弹窗中输入验证码（专用方法）"""
        try:
            print(f"[检查] 在弹窗中输入验证码: {captcha_code}")

            # 等待弹窗稳定
            time.sleep(1)

            # 多次尝试找到并操作输入框
            for attempt in range(3):
                try:
                    print(f"[重试] 第 {attempt + 1} 次尝试操作弹窗输入框...")

                    # 等待输入框出现
                    wait = WebDriverWait(self.driver, 5)
                    input_element = wait.until(EC.presence_of_element_located(("xpath", input_xpath)))

                    # 等待元素可交互
                    wait.until(EC.element_to_be_clickable(("xpath", input_xpath)))

                    print("[成功] 弹窗输入框已找到")

                    # 滚动到弹窗中心
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", input_element)
                    time.sleep(0.5)

                    # 强制获得焦点
                    self.driver.execute_script("arguments[0].focus();", input_element)
                    time.sleep(0.3)

                    # 清空输入框
                    self.driver.execute_script("arguments[0].value = '';", input_element)
                    time.sleep(0.2)

                    # 使用JavaScript输入验证码
                    self.driver.execute_script(f"arguments[0].value = '{captcha_code}';", input_element)
                    time.sleep(0.3)

                    # 触发必要的事件
                    self.driver.execute_script("""
                        arguments[0].dispatchEvent(new Event('input', {bubbles: true}));
                        arguments[0].dispatchEvent(new Event('change', {bubbles: true}));
                        arguments[0].dispatchEvent(new Event('keyup', {bubbles: true}));
                    """, input_element)

                    time.sleep(0.5)

                    # 验证输入是否成功
                    current_value = input_element.get_attribute('value')
                    if current_value == captcha_code:
                        print(f"[成功] 弹窗验证码输入成功: {captcha_code}")
                        return True
                    else:
                        print(f"[警告] 第 {attempt + 1} 次输入不匹配，期望: {captcha_code}, 实际: {current_value}")
                        if attempt < 2:  # 不是最后一次尝试
                            time.sleep(1)
                            continue
                        else:
                            return False

                except TimeoutException:
                    print(f"[警告] 第 {attempt + 1} 次等待弹窗输入框超时")
                    if attempt < 2:
                        time.sleep(1)
                        continue
                    else:
                        return False
                except Exception as e:
                    print(f"[警告] 第 {attempt + 1} 次操作弹窗输入框失败: {e}")
                    if attempt < 2:
                        time.sleep(1)
                        continue
                    else:
                        return False

            return False

        except Exception as e:
            print(f"[失败] 弹窗验证码输入失败: {e}")
            return False

    def clear_captcha_input_for_retry(self, input_xpath: str) -> bool:
        """为重试清空验证码输入框"""
        try:
            print("🧹 清空验证码输入框，准备重试...")
            input_element = self.driver.find_element("xpath", input_xpath)

            # 多种方式清空输入框
            input_element.clear()
            time.sleep(0.2)

            # 使用JavaScript确保完全清空
            self.driver.execute_script("arguments[0].value = '';", input_element)
            time.sleep(0.2)

            # 使用键盘快捷键清空
            from selenium.webdriver.common.keys import Keys
            input_element.send_keys(Keys.CONTROL + "a")
            time.sleep(0.1)
            input_element.send_keys(Keys.DELETE)
            time.sleep(0.2)

            # 验证是否清空成功
            actual_value = input_element.get_attribute('value')
            if not actual_value or actual_value.strip() == "":
                print("[成功] 验证码输入框已清空")
                return True
            else:
                print(f"[警告] 验证码输入框清空不完全，剩余内容: '{actual_value}'")
                return False

        except Exception as e:
            print(f"[失败] 清空验证码输入框失败: {e}")
            return False



    def handle_captcha_manual(self, captcha_img_xpath: str, captcha_input_xpath: str) -> bool:
        """手动输入验证码"""
        try:
            print("[图像] 请查看浏览器中的验证码图片")

            # 高亮验证码图片
            self.element_helper.highlight_element(captcha_img_xpath, duration=1.0)

            captcha_code = input("请输入验证码: ").strip()

            if captcha_code:
                if self.clear_and_input_captcha(captcha_input_xpath, captcha_code):
                    return True
                else:
                    print("[失败] 无法填写验证码")
                    return False
            else:
                print("[失败] 未输入验证码")
                return False

        except Exception as e:
            print(f"[失败] 手动验证码处理失败: {e}")
            return False

    def recognize_captcha_with_ddddocr(self, captcha_img_xpath: str) -> str:
        """使用ddddocr快速识别验证码"""
        try:
            import ddddocr
            from PIL import Image
            import io
            import base64

            print("🤖 快速识别验证码...")

            # 快速获取图片数据
            try:
                # 等待图片加载完成
                wait = WebDriverWait(self.driver, 3)
                img_element = wait.until(EC.presence_of_element_located(("xpath", captcha_img_xpath)))

                # 使用JavaScript快速获取图片数据
                js_script = f"""
                var img = document.evaluate('{captcha_img_xpath}', document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                if (img && img.complete && img.naturalWidth > 0) {{
                    var canvas = document.createElement('canvas');
                    canvas.width = img.naturalWidth;
                    canvas.height = img.naturalHeight;
                    var ctx = canvas.getContext('2d');
                    ctx.drawImage(img, 0, 0);
                    return canvas.toDataURL('image/png');
                }}
                return null;
                """

                img_data = self.driver.execute_script(js_script)

                if img_data and img_data.startswith('data:image'):
                    # 解码base64图片数据
                    img_data = img_data.split(',')[1]
                    img_bytes = base64.b64decode(img_data)

                    # 使用ddddocr识别
                    ocr = ddddocr.DdddOcr()
                    result = ocr.classification(img_bytes)

                    # 清理结果
                    result = result.strip().replace(' ', '').replace('\n', '')

                    if result:
                        print(f"[成功] JavaScript方法识别成功: {result}")
                        return result
                    else:
                        print("[警告] JavaScript方法识别结果为空")

            except Exception as js_error:
                print(f"[警告] JavaScript方法失败: {js_error}")

            # 方法2：尝试获取图片src属性
            try:
                captcha_img = self.driver.find_element("xpath", captcha_img_xpath)
                img_src = captcha_img.get_attribute('src')

                print(f"[检查] 验证码图片src: {img_src}")

                if img_src and img_src.startswith('data:image'):
                    # 处理base64图片
                    img_data = img_src.split(',')[1]
                    img_bytes = base64.b64decode(img_data)

                    ocr = ddddocr.DdddOcr()
                    result = ocr.classification(img_bytes)
                    result = result.strip().replace(' ', '').replace('\n', '')

                    if result:
                        print(f"[成功] src属性方法识别成功: {result}")
                        return result
                    else:
                        print("[警告] src属性方法识别结果为空")

                else:
                    print("[警告] 验证码图片src为空或格式不支持")

            except Exception as src_error:
                print(f"[警告] src属性方法失败: {src_error}")

            # 方法3：尝试传统截图方法（作为最后手段）
            try:
                captcha_img = self.driver.find_element("xpath", captcha_img_xpath)

                # 检查元素尺寸
                size = captcha_img.size
                if size['width'] > 0 and size['height'] > 0:
                    screenshot = captcha_img.screenshot_as_png

                    ocr = ddddocr.DdddOcr()
                    result = ocr.classification(screenshot)
                    result = result.strip().replace(' ', '').replace('\n', '')

                    if result:
                        print(f"[成功] 传统截图方法识别成功: {result}")
                        return result
                    else:
                        print("[警告] 传统截图方法识别结果为空")
                else:
                    print(f"[警告] 验证码图片尺寸异常: {size}")

            except Exception as screenshot_error:
                print(f"[警告] 传统截图方法失败: {screenshot_error}")

            print("[失败] 所有识别方法都失败了")
            return ""

        except ImportError:
            print("[警告] ddddocr未安装，无法自动识别验证码")
            return ""
        except Exception as e:
            print(f"[失败] ddddocr识别异常: {e}")
            return ""

    def handle_immediate_captcha_popup(self, max_retries: int = 3) -> bool:
        """立即处理验证码弹窗（支持重试）"""
        try:
            print("[检查] 立即处理验证码弹窗...")

            # 验证码弹窗元素XPath
            captcha_img_xpath = "/html/body/div[2]/div[2]/div/div[2]/div/div[1]/div/img"
            captcha_input_xpath = "/html/body/div[2]/div[2]/div/div[2]/div/div[2]/span/input"
            captcha_confirm_xpath = "/html/body/div[2]/div[2]/div/div[3]/button[2]"

            # 等待弹窗出现
            print("⏳ 等待验证码弹窗出现...")
            wait = WebDriverWait(self.driver, 5)

            try:
                # 等待验证码图片出现
                wait.until(EC.presence_of_element_located(("xpath", captcha_img_xpath)))
                print("[成功] 验证码弹窗已出现")
            except TimeoutException:
                print("[警告] 验证码弹窗未出现，可能不需要验证码")
                return True

            # 多次重试验证码识别和输入
            for attempt in range(max_retries):
                print(f"[重试] 第 {attempt + 1} 次尝试验证码识别...")

                # 重新获取验证码图片内容
                print("[图片] 重新获取验证码图片内容...")
                captcha_code = self.recognize_captcha_with_ddddocr(captcha_img_xpath)

                if captcha_code:
                    print(f"[目标] 验证码识别成功: {captcha_code}")

                    # 清空输入框并输入验证码
                    if self.retry_input_captcha(captcha_input_xpath, captcha_code):
                        # 点击确认
                        time.sleep(0.5)
                        if self.element_helper.safe_click(captcha_confirm_xpath, "验证码确认按钮"):
                            print("[成功] 验证码确认按钮已点击")

                            # 通过检查"请输入验证码"元素判断弹窗是否关闭
                            if self.check_captcha_popup_closed():
                                print("[成功] 验证码验证成功，弹窗已关闭")
                                return True
                            else:
                                print("[失败] 验证码验证失败，弹窗仍然存在")
                                if attempt < max_retries - 1:
                                    print("[重试] 准备重新识别验证码...")
                                    time.sleep(1)  # 等待一秒后重试
                                    continue
                                else:
                                    print("[失败] 已达到最大重试次数")
                                    return False
                        else:
                            print("[失败] 点击确认按钮失败")
                            if attempt < max_retries - 1:
                                continue
                            else:
                                return False
                    else:
                        print("[失败] 验证码输入失败")
                        if attempt < max_retries - 1:
                            continue
                        else:
                            return False
                else:
                    print("[失败] 验证码识别失败")
                    if attempt < max_retries - 1:
                        print("[重试] 重新识别验证码...")
                        time.sleep(1)
                        continue
                    else:
                        # 最后一次尝试手动输入
                        if not self.headless:
                            print("[手动] 自动识别失败，请手动输入验证码")
                            captcha_code = input("请输入验证码: ").strip()
                            if captcha_code:
                                if self.retry_input_captcha(captcha_input_xpath, captcha_code):
                                    time.sleep(0.5)
                                    if self.element_helper.safe_click(captcha_confirm_xpath, "验证码确认按钮"):
                                        if self.check_captcha_popup_closed():
                                            print("[成功] 手动验证码验证成功")
                                            return True
                        return False

        except Exception as e:
            print(f"[失败] 立即处理验证码弹窗失败: {e}")
            return False

    def retry_input_captcha(self, input_xpath: str, captcha_code: str) -> bool:
        """重试输入验证码（清空后重新输入）"""
        try:
            print(f"[重试] 重新输入验证码: {captcha_code}")

            # 等待输入框可交互
            wait = WebDriverWait(self.driver, 5)
            input_element = wait.until(EC.element_to_be_clickable(("xpath", input_xpath)))

            # 滚动到元素位置
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", input_element)
            time.sleep(0.3)

            # 强制获得焦点
            self.driver.execute_script("arguments[0].focus();", input_element)
            time.sleep(0.2)

            # 多种方式清空输入框
            try:
                # 全选并删除
                from selenium.webdriver.common.keys import Keys
                input_element.send_keys(Keys.CONTROL + "a")
                time.sleep(0.1)
                input_element.send_keys(Keys.DELETE)
                time.sleep(0.2)

                # JavaScript清空
                self.driver.execute_script("arguments[0].value = '';", input_element)
                time.sleep(0.2)

                # 再次清空确保完全清除
                input_element.clear()
                time.sleep(0.2)

                print("[成功] 输入框已完全清空")

            except Exception as e:
                print(f"[警告] 清空输入框时出现警告: {e}")

            # 使用JavaScript输入验证码
            self.driver.execute_script(f"arguments[0].value = '{captcha_code}';", input_element)
            time.sleep(0.3)

            # 触发输入事件
            self.driver.execute_script("""
                arguments[0].dispatchEvent(new Event('input', {bubbles: true}));
                arguments[0].dispatchEvent(new Event('change', {bubbles: true}));
                arguments[0].dispatchEvent(new Event('keyup', {bubbles: true}));
            """, input_element)

            time.sleep(0.3)

            # 验证输入是否成功
            current_value = input_element.get_attribute('value')
            if current_value == captcha_code:
                print(f"[成功] 验证码重新输入成功: {captcha_code}")
                return True
            else:
                print(f"[失败] 验证码重新输入失败，期望: {captcha_code}, 实际: {current_value}")
                return False

        except TimeoutException:
            print(f"[失败] 等待验证码输入框超时")
            return False
        except Exception as e:
            print(f"[失败] 重新输入验证码失败: {e}")
            return False

    def complete_registration_steps(self) -> bool:
        """完成注册的最后步骤：点击协议复选框和注册按钮"""
        try:
            print("[重试] 开始完成注册最后步骤...")

            # 第一步：点击协议复选框
            print("[信息] 第一步：点击协议复选框...")

            # 多个协议复选框选择器策略
            checkbox_selectors = [
                "//div[@class='register-form-agreement-actions']//label[@class='arco-checkbox']",
                "//label[@class='arco-checkbox']",
                "//input[@class='arco-checkbox-target']",
                "//div[@class='arco-checkbox-icon']",
                "//span[@class='arco-icon-hover arco-checkbox-icon-hover']",
                "//*[@id=\"app\"]/div/div[2]/div[1]/div/div[3]/div[2]/div/div/div/form/div[6]/div[1]/div/div/label",
                "//div[contains(@class, 'register-form-agreement')]//label",
                "//div[contains(text(), '已阅读并同意')]/..//label"
            ]

            # 等待页面加载
            wait = WebDriverWait(self.driver, 10)
            checkbox_clicked = False

            for i, selector in enumerate(checkbox_selectors):
                try:
                    print(f"[检查] 尝试协议复选框选择器 {i+1}: {selector}")

                    # 等待元素出现
                    checkbox_element = wait.until(EC.presence_of_element_located(("xpath", selector)))
                    print(f"[成功] 找到协议复选框元素")

                    # 滚动到元素位置
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", checkbox_element)
                    time.sleep(0.5)

                    # 尝试点击
                    try:
                        # 等待可点击
                        clickable_element = wait.until(EC.element_to_be_clickable(("xpath", selector)))
                        clickable_element.click()
                        print(f"[成功] 协议复选框已点击（选择器 {i+1}）")
                        checkbox_clicked = True
                        time.sleep(1)  # 等待状态更新
                        break
                    except Exception as click_error:
                        print(f"[警告] 选择器 {i+1} 点击失败: {click_error}")
                        # 尝试JavaScript点击
                        try:
                            self.driver.execute_script("arguments[0].click();", checkbox_element)
                            print(f"[成功] 协议复选框已点击（JS方式，选择器 {i+1}）")
                            checkbox_clicked = True
                            time.sleep(1)
                            break
                        except Exception as js_error:
                            print(f"[警告] 选择器 {i+1} JS点击也失败: {js_error}")
                            continue

                except TimeoutException:
                    print(f"[警告] 选择器 {i+1} 等待超时")
                    continue
                except Exception as e:
                    print(f"[警告] 选择器 {i+1} 异常: {e}")
                    continue

            if not checkbox_clicked:
                print("[失败] 所有协议复选框选择器都失败了")
                return False

            # 第二步：点击注册按钮
            print("[启动] 第二步：点击注册按钮...")

            # 多个注册按钮选择器策略
            button_selectors = [
                "//button[contains(text(), '立即注册')]",
                "//button[contains(text(), '注册')]",
                "//button[@type='submit']",
                "//form//button[last()]",
                "//*[@id=\"app\"]/div/div[2]/div[1]/div/div[3]/div[2]/div/div/div/form/div[6]/div[2]/button",
                "//div[contains(@class, 'register-form')]//button",
                "//button[contains(@class, 'arco-btn-primary')]"
            ]

            button_clicked = False

            for i, selector in enumerate(button_selectors):
                try:
                    print(f"[检查] 尝试注册按钮选择器 {i+1}: {selector}")

                    # 等待按钮出现
                    register_button = wait.until(EC.presence_of_element_located(("xpath", selector)))
                    print(f"[成功] 找到注册按钮")

                    # 滚动到按钮位置
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", register_button)
                    time.sleep(0.5)

                    # 尝试点击
                    try:
                        # 等待可点击
                        clickable_button = wait.until(EC.element_to_be_clickable(("xpath", selector)))
                        clickable_button.click()
                        print(f"[成功] 注册按钮已点击（选择器 {i+1}）")
                        button_clicked = True
                        time.sleep(3)  # 等待注册处理
                        break
                    except Exception as click_error:
                        print(f"[警告] 选择器 {i+1} 点击失败: {click_error}")
                        # 尝试JavaScript点击
                        try:
                            self.driver.execute_script("arguments[0].click();", register_button)
                            print(f"[成功] 注册按钮已点击（JS方式，选择器 {i+1}）")
                            button_clicked = True
                            time.sleep(3)
                            break
                        except Exception as js_error:
                            print(f"[警告] 选择器 {i+1} JS点击也失败: {js_error}")
                            continue

                except TimeoutException:
                    print(f"[警告] 选择器 {i+1} 等待超时")
                    continue
                except Exception as e:
                    print(f"[警告] 选择器 {i+1} 异常: {e}")
                    continue

            if not button_clicked:
                print("[失败] 所有注册按钮选择器都失败了")
                return False

            # 检查注册结果
            print("[检查] 检查注册结果...")
            time.sleep(2)  # 额外等待时间

            # 检查是否有成功提示或跳转
            current_url = self.driver.current_url
            print(f"[位置] 当前页面URL: {current_url}")

            # 检查页面标题变化
            page_title = self.driver.title
            print(f"[页面] 页面标题: {page_title}")

            # 简单判断：如果URL发生变化或包含成功相关关键词，认为注册成功
            if "register" not in current_url.lower() or "success" in current_url.lower() or "dashboard" in current_url.lower():
                print("[完成] 注册可能成功（页面已跳转）")
                return True
            else:
                print("[警告] 注册状态不明确，但已完成所有步骤")
                return True  # 假设成功，因为已完成所有必要步骤

        except Exception as e:
            print(f"[失败] 完成注册步骤失败: {e}")
            return False

    def auto_input_sms_code_to_element(self, code: str) -> bool:
        """自动将验证码输入到指定的input元素"""
        try:
            print(f"[手机] 自动输入验证码到页面元素: {code}")

            # 方法1：尝试通过class定位
            input_selectors = [
                "//input[@class='arco-input arco-input-size-large' and @placeholder='请输入验证码']",
                "//input[@placeholder='请输入验证码']",
                "//input[contains(@class, 'arco-input') and @placeholder='请输入验证码']",
                "//*[@id='mobile_code']/div/div/span/input",  # 备用选择器
                "//input[@type='text' and contains(@placeholder, '验证码')]"
            ]

            wait = WebDriverWait(self.driver, 10)

            for i, selector in enumerate(input_selectors):
                try:
                    print(f"[检查] 尝试选择器 {i+1}: {selector}")

                    # 等待元素出现
                    input_element = wait.until(EC.presence_of_element_located(("xpath", selector)))
                    print(f"[成功] 找到验证码输入框")

                    # 等待元素可交互
                    input_element = wait.until(EC.element_to_be_clickable(("xpath", selector)))
                    print(f"[成功] 验证码输入框可交互")

                    # 滚动到元素位置
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", input_element)
                    time.sleep(0.5)

                    # 获得焦点
                    input_element.click()
                    time.sleep(0.3)

                    # 清空输入框
                    input_element.clear()
                    time.sleep(0.2)

                    # 使用JavaScript清空
                    self.driver.execute_script("arguments[0].value = '';", input_element)
                    time.sleep(0.2)

                    # 输入验证码
                    input_element.send_keys(code)
                    time.sleep(0.3)

                    # 验证输入是否成功
                    current_value = input_element.get_attribute('value')
                    if current_value == code:
                        print(f"[成功] 验证码输入成功: {code}")

                        # 触发输入事件
                        self.driver.execute_script("""
                            arguments[0].dispatchEvent(new Event('input', {bubbles: true}));
                            arguments[0].dispatchEvent(new Event('change', {bubbles: true}));
                        """, input_element)

                        # 立即完成注册步骤
                        print("[启动] 验证码输入成功，立即完成注册...")
                        if self.complete_registration_steps():
                            print("[成功] 注册流程自动完成")

                        return True
                    else:
                        print(f"[警告] 验证码输入不匹配，期望: {code}, 实际: {current_value}")
                        continue

                except TimeoutException:
                    print(f"[警告] 选择器 {i+1} 超时")
                    continue
                except Exception as e:
                    print(f"[警告] 选择器 {i+1} 失败: {e}")
                    continue

            print("[失败] 所有选择器都失败了")
            return False

        except Exception as e:
            print(f"[失败] 自动输入验证码失败: {e}")
            return False

    def check_captcha_popup_closed(self, timeout: int = 5) -> bool:
        """通过检查'请输入验证码'元素判断弹窗是否关闭"""
        try:
            print("[检查] 检查验证码弹窗是否关闭...")

            # "请输入验证码"元素的XPath
            captcha_title_xpath = "/html/body/div[2]/div[2]/div/div[1]/div[1]"

            # 等待一段时间，检查元素是否消失
            start_time = time.time()
            while time.time() - start_time < timeout:
                # 检查"请输入验证码"元素是否还存在
                title_exists = self.element_helper.check_element_exists(captcha_title_xpath, timeout=0.5)

                if not title_exists:
                    print("[成功] '请输入验证码'元素已消失，弹窗已关闭")
                    return True

                # 检查元素内容是否包含"请输入验证码"
                try:
                    title_element = self.driver.find_element("xpath", captcha_title_xpath)
                    title_text = title_element.text.strip()

                    if "请输入验证码" not in title_text:
                        print(f"[成功] 标题文本已变化: '{title_text}'，弹窗已关闭")
                        return True

                except:
                    # 如果找不到元素，说明弹窗已关闭
                    print("[成功] 无法找到标题元素，弹窗已关闭")
                    return True

                elapsed = time.time() - start_time
                print(f"⏳ 弹窗仍然存在，继续等待... ({elapsed:.1f}/{timeout}秒)")
                time.sleep(0.5)

            # 超时后最终检查
            print("⏰ 等待超时，进行最终检查...")
            final_exists = self.element_helper.check_element_exists(captcha_title_xpath, timeout=1)

            if not final_exists:
                print("[成功] 最终检查：弹窗已关闭")
                return True
            else:
                print("[失败] 最终检查：弹窗仍然存在")
                return False

        except Exception as e:
            print(f"[失败] 检查弹窗状态失败: {e}")
            # 发生异常时，假设弹窗已关闭
            return True

    def handle_sms_captcha_popup(self, max_retries: int = 3) -> bool:
        """处理获取短信验证码时的验证码弹窗（使用Selenium方法）"""
        try:
            print("[检查] 等待验证码弹窗出现...")

            # 验证码图片XPath
            captcha_img_xpath = "/html/body/div[2]/div[2]/div/div[2]/div/div[1]/div/img"
            # 验证码输入框XPath
            captcha_input_xpath = "/html/body/div[2]/div[2]/div/div[2]/div/div[2]/span/input"
            # 确认按钮XPath
            captcha_confirm_xpath = "/html/body/div[2]/div[2]/div/div[3]/button[2]"

            # 等待验证码弹窗出现（等待更长时间）
            print("⏳ 等待验证码弹窗加载...")
            time.sleep(3)  # 给弹窗更多时间加载

            # 检查弹窗是否出现
            if not self.element_helper.check_element_exists(captcha_input_xpath, timeout=8):
                print("[警告] 验证码弹窗未出现，可能不需要验证码")
                return True

            print("[成功] 发现验证码弹窗")

            # 等待验证码图片加载
            print("⏳ 等待验证码图片加载...")
            time.sleep(2)

            # 使用Selenium方法处理验证码
            for attempt in range(max_retries):
                print(f"🤖 第 {attempt + 1}/{max_retries} 次尝试处理弹窗验证码...")

                try:
                    # 检查验证码图片是否存在
                    if self.element_helper.check_element_exists(captcha_img_xpath, timeout=3):
                        print("[成功] 找到验证码图片")

                        # 尝试点击图片刷新
                        print("[重试] 点击验证码图片刷新...")
                        self.element_helper.safe_click(captcha_img_xpath, "验证码图片", timeout=2)
                        time.sleep(2)  # 等待图片刷新

                        # 使用改进的ddddocr识别
                        captcha_code = self.recognize_captcha_with_ddddocr(captcha_img_xpath)

                        if captcha_code:
                            print(f"[目标] 识别结果: {captcha_code}")

                            # 输入验证码并确认
                            if self.input_captcha_in_popup(captcha_input_xpath, captcha_code):
                                time.sleep(1)
                                if self.element_helper.safe_click(captcha_confirm_xpath, "验证码确认按钮"):
                                    print("[成功] 验证码处理完成")
                                    time.sleep(2)  # 等待弹窗关闭
                                    return True
                                else:
                                    print(f"[失败] 第 {attempt + 1} 次点击确认按钮失败")
                            else:
                                print(f"[失败] 第 {attempt + 1} 次填写验证码失败")
                        else:
                            print(f"[失败] 第 {attempt + 1} 次识别失败，验证码为空")
                    else:
                        print("[警告] 验证码图片不存在，尝试直接手动输入")

                        # 如果图片不存在，直接手动输入
                        if not self.headless:
                            captcha_code = input("请输入弹窗中的验证码: ").strip()

                            if captcha_code:
                                if self.clear_and_input_captcha(captcha_input_xpath, captcha_code):
                                    time.sleep(1)
                                    if self.element_helper.safe_click(captcha_confirm_xpath, "验证码确认按钮"):
                                        print("[成功] 手动验证码处理完成")
                                        time.sleep(2)  # 等待弹窗关闭
                                        return True

                    if attempt < max_retries - 1:
                        print("[重试] 准备重新尝试...")
                        # 在重试前，确保清空输入框
                        self.clear_captcha_input_for_retry(captcha_input_xpath)
                        time.sleep(2)

                except Exception as e:
                    print(f"[失败] 第 {attempt + 1} 次弹窗验证码处理异常: {e}")
                    if attempt < max_retries - 1:
                        print("[重试] 准备重新尝试...")
                        time.sleep(2)

            print(f"[失败] 弹窗验证码处理失败，已尝试 {max_retries} 次")
            return False

        except Exception as e:
            print(f"[失败] 处理验证码弹窗失败: {e}")
            return False

    def handle_sms_captcha_with_retry(self, captcha_img_xpath: str, captcha_input_xpath: str, captcha_confirm_xpath: str, max_retries: int = 3) -> bool:
        """带重试机制的短信验证码弹窗处理（使用改进的ddddocr识别）"""
        print("🤖 检测到验证码弹窗，使用ddddocr自动识别...")

        # 等待弹窗完全加载
        time.sleep(2)

        # 检查弹窗是否存在
        if not self.element_helper.check_element_exists(captcha_input_xpath, timeout=5):
            print("[失败] 验证码输入框不存在")
            return False

        if not self.element_helper.check_element_exists(captcha_confirm_xpath, timeout=3):
            print("[失败] 验证码确认按钮不存在")
            return False

        for attempt in range(max_retries):
            print(f"🤖 第 {attempt + 1}/{max_retries} 次尝试自动识别弹窗验证码...")

            try:
                # 点击验证码图片刷新（如果存在）
                if self.element_helper.check_element_exists(captcha_img_xpath, timeout=2):
                    print("[重试] 尝试点击验证码图片刷新...")
                    self.element_helper.safe_click(captcha_img_xpath, "验证码图片", timeout=2)
                    time.sleep(1)

                # 使用改进的ddddocr识别
                captcha_code = self.recognize_captcha_with_ddddocr(captcha_img_xpath)

                if captcha_code:
                    print(f"[目标] 第 {attempt + 1} 次识别结果: {captcha_code}")

                    # 清空输入框并填写验证码
                    if self.clear_and_input_captcha(captcha_input_xpath, captcha_code):
                        print(f"[成功] 验证码已填写: {captcha_code}")

                        # 点击确认按钮
                        time.sleep(1)
                        if self.element_helper.safe_click(captcha_confirm_xpath, "验证码确认按钮"):
                            print("[成功] 验证码确认成功")
                            time.sleep(2)  # 等待弹窗关闭
                            return True
                        else:
                            print(f"[失败] 第 {attempt + 1} 次点击确认按钮失败")
                    else:
                        print(f"[失败] 第 {attempt + 1} 次填写验证码失败")
                else:
                    print(f"[失败] 第 {attempt + 1} 次识别失败，验证码为空")

                if attempt < max_retries - 1:
                    print("[重试] 准备重新尝试...")
                    time.sleep(1)

            except Exception as e:
                print(f"[失败] 第 {attempt + 1} 次弹窗验证码处理异常: {e}")
                if attempt < max_retries - 1:
                    print("[重试] 准备重新尝试...")
                    time.sleep(1)

        # 如果自动识别失败，回退到手动输入
        if not self.headless:
            print("[图像] 自动识别失败，回退到手动输入...")
            return self.handle_sms_captcha_manual(captcha_img_xpath, captcha_input_xpath, captcha_confirm_xpath)
        else:
            print(f"[失败] 弹窗验证码自动识别失败，已尝试 {max_retries} 次")
            return False

    def handle_sms_captcha_manual(self, captcha_img_xpath: str, captcha_input_xpath: str, captcha_confirm_xpath: str) -> bool:
        """手动输入短信验证码弹窗"""
        try:
            # 高亮验证码图片
            self.element_helper.highlight_element(captcha_img_xpath, duration=2.0)

            captcha_code = input("请输入弹窗中的验证码: ").strip()

            if captcha_code:
                if self.clear_and_input_captcha(captcha_input_xpath, captcha_code):
                    print(f"[成功] 验证码已填写: {captcha_code}")

                    # 点击确认按钮
                    time.sleep(1)
                    if self.element_helper.safe_click(captcha_confirm_xpath, "验证码确认按钮"):
                        print("[成功] 验证码确认成功")
                        time.sleep(2)  # 等待弹窗关闭
                        return True
                    else:
                        print("[失败] 无法点击验证码确认按钮")
                        return False
                else:
                    print("[失败] 无法填写验证码")
                    return False
            else:
                print("[失败] 未输入验证码")
                return False

        except Exception as e:
            print(f"[失败] 手动弹窗验证码处理失败: {e}")
            return False

    def handle_captcha(self) -> bool:
        """处理图形验证码（保持向后兼容）"""
        return self.handle_captcha_advanced()
    
    def check_and_click_element(self, xpath: str, description: str = "元素", timeout: int = 5) -> bool:
        """
        检查元素是否存在，如果存在则点击

        Args:
            xpath: 元素的XPath
            description: 元素描述（用于日志）
            timeout: 等待超时时间（秒）

        Returns:
            bool: 是否成功点击
        """
        try:
            print(f"[检查] 检查{description}: {xpath}")

            # 等待元素出现
            element = WebDriverWait(self.driver, timeout).until(
                EC.element_to_be_clickable((By.XPATH, xpath))
            )

            print(f"[成功] 找到{description}，准备点击")

            # 滚动到元素位置
            self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
            time.sleep(0.5)

            # 点击元素
            element.click()
            print(f"[成功] 成功点击{description}")

            return True

        except TimeoutException:
            print(f"[警告] {description}不存在或不可点击（超时{timeout}秒）")
            return False
        except Exception as e:
            print(f"[失败] 点击{description}失败: {e}")
            return False

    def mouse_move_and_click(self, xpath: str, description: str = "元素", timeout: int = 5) -> bool:
        """
        使用鼠标移动到元素位置然后左键点击

        Args:
            xpath: 元素的XPath
            description: 元素描述（用于日志）
            timeout: 等待超时时间（秒）

        Returns:
            bool: 是否成功点击
        """
        try:
            print(f"[鼠标] 使用鼠标移动+左键点击{description}: {xpath}")

            # 等待元素出现
            element = WebDriverWait(self.driver, timeout).until(
                EC.element_to_be_clickable((By.XPATH, xpath))
            )

            # 滚动到元素位置
            self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
            time.sleep(0.5)

            # 使用ActionChains进行鼠标移动和点击
            actions = ActionChains(self.driver)
            actions.move_to_element(element).click().perform()

            print(f"[成功] 成功使用鼠标移动+左键点击{description}")
            return True

        except TimeoutException:
            print(f"[警告] {description}不存在或不可点击（超时{timeout}秒）")
            return False
        except Exception as e:
            print(f"[失败] 鼠标移动+点击{description}失败: {e}")
            return False

    def check_element_exists(self, xpath: str, timeout: int = 5) -> bool:
        """
        检查元素是否存在

        Args:
            xpath: 元素的XPath
            timeout: 等待超时时间（秒）

        Returns:
            bool: 元素是否存在
        """
        try:
            WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((By.XPATH, xpath))
            )
            return True
        except TimeoutException:
            return False
        except Exception:
            return False

    def close(self):
        """关闭浏览器"""
        if self.driver:
            try:
                # 先关闭所有窗口
                self.driver.quit()
                print("[关闭] 浏览器已关闭")
            except Exception as e:
                print(f"[警告] 关闭浏览器时出现异常: {e}")
                try:
                    # 尝试强制关闭
                    self.driver.close()
                    print("[关闭] 浏览器已强制关闭")
                except:
                    print("[警告] 无法关闭浏览器，可能需要手动关闭")
            finally:
                self.driver = None

        # 额外的清理步骤
        try:
            import gc
            gc.collect()  # 强制垃圾回收
        except:
            pass

def main():
    """主函数 - 支持连续注册"""
    print("🤖 Selenium自动化注册器")
    print("=" * 50)

    # 从配置文件读取设置
    config = get_config()
    selenium_config = config.get_selenium_config()
    yezi_config = config.get_yezi_cloud_config()

    register = None
    try:
        # 获取注册数量
        try:
            count = int(input("请输入要注册的账号数量 (默认1个): ").strip() or "1")
            if count <= 0:
                print("[失败] 数量必须大于0")
                return
        except ValueError:
            print("[失败] 请输入有效的数字")
            return

        # 初始化注册器
        basic_config = config.get_basic_config()
        base_url = basic_config.get('domain', 'https://970faka.com')
        register = SeleniumRegister(
            headless=selenium_config['headless'],
            use_yezi_cloud=config.is_yezi_cloud_configured(),
            base_url=base_url
        )

        # 配置椰子云（如果已配置）
        if config.is_yezi_cloud_configured():
            if not register.setup_yezi_cloud(
                yezi_config['username'],
                yezi_config['password'],
                yezi_config['project_id']
            ):
                print("[失败] 椰子云配置失败，将使用手动输入模式")
                register.use_yezi_cloud = False
        else:
            print("[警告] 椰子云未配置，将使用手动输入模式")
            print("请在config.ini中配置椰子云信息")
            register.use_yezi_cloud = False

        # 连续注册 - 每次注册后重新打开浏览器
        successful_registrations = []
        failed_registrations = []

        print(f"\n[模式] 每个账号注册完成后将关闭并重新打开浏览器")
        print(f"[提示] 这样可以确保每次注册都是全新的浏览器环境")

        for i in range(count):
            print(f"\n{'='*60}")
            print(f"[进度] 开始注册第 {i+1}/{count} 个账号")
            print(f"{'='*60}")

            current_register = None
            try:
                # 为每个账号创建全新的注册器实例
                print(f"[浏览器] 创建新的浏览器实例...")

                current_register = SeleniumRegister(
                    headless=selenium_config['headless'],
                    use_yezi_cloud=config.is_yezi_cloud_configured(),
                    base_url=base_url
                )

                # 配置椰子云（如果已配置）
                if config.is_yezi_cloud_configured():
                    print(f"[配置] 设置椰子云配置...")
                    if not current_register.setup_yezi_cloud(
                        yezi_config['username'],
                        yezi_config['password'],
                        yezi_config['project_id']
                    ):
                        print("[失败] 椰子云配置失败，将使用手动输入模式")
                        current_register.use_yezi_cloud = False
                else:
                    print("[警告] 椰子云未配置，将使用手动输入模式")
                    current_register.use_yezi_cloud = False

                # 生成用户数据
                user_data = current_register.generate_user_data()
                print(f"[用户] 用户名: {user_data['username']}")
                print(f"[密码] 密码: {user_data['password']}")

                print(f"[注册] 开始注册流程...")

                # 执行注册
                result = current_register.register_user(
                    username=user_data['username'],
                    password=user_data['password']
                )

                # 处理结果
                if result['success']:
                    print(f"\n[成功] 第 {i+1} 个账号注册成功！")
                    print(f"[用户] 用户名: {result['username']}")
                    print(f"[密码] 密码: {result['password']}")
                    print(f"[手机] 手机号: {result['mobile']}")

                    successful_registrations.append({
                        'index': i+1,
                        'username': result['username'],
                        'password': result['password'],
                        'mobile': result['mobile']
                    })

                else:
                    print(f"\n[失败] 第 {i+1} 个账号注册失败: {result['error']}")
                    print(f"[步骤] 失败步骤: {result.get('step', '未知')}")

                    failed_registrations.append({
                        'index': i+1,
                        'username': user_data['username'],
                        'error': result['error'],
                        'step': result.get('step', '未知')
                    })

            except Exception as e:
                print(f"[异常] 第 {i+1} 个账号注册异常: {e}")
                failed_registrations.append({
                    'index': i+1,
                    'username': user_data.get('username', '未知') if 'user_data' in locals() else '未知',
                    'error': f'注册异常: {str(e)}',
                    'step': 'exception'
                })

            finally:
                # 每次注册完成后都关闭浏览器
                if current_register:
                    try:
                        print(f"[清理] 关闭第 {i+1} 个账号的浏览器实例...")
                        current_register.close()
                        print(f"[清理] 浏览器已关闭")
                    except Exception as e:
                        print(f"[警告] 关闭浏览器失败: {e}")

                # 如果还有更多账号要注册，询问是否继续并添加延迟
                if i < count - 1:
                    print(f"\n[提示] 第 {i+1} 个账号处理完成")

                    # 询问是否继续
                    continue_choice = input("是否继续注册下一个账号？(y/n，默认y): ").strip().lower()
                    if continue_choice == 'n':
                        print("[停止] 用户选择停止注册")
                        break

                    # 添加延迟，确保浏览器完全关闭
                    import time
                    delay = 5
                    print(f"[等待] {delay} 秒后开始下一个账号注册...")
                    time.sleep(delay)

        # 显示最终统计
        print(f"\n{'='*60}")
        print("[统计] 注册完成统计")
        print(f"{'='*60}")
        print(f"[总计] 计划注册: {count} 个")
        print(f"[成功] 成功注册: {len(successful_registrations)} 个")
        print(f"[失败] 失败注册: {len(failed_registrations)} 个")

        # 显示成功的账号
        if successful_registrations:
            print(f"\n[成功] 成功注册的账号:")
            for reg in successful_registrations:
                print(f"  {reg['index']}. 用户名: {reg['username']} | 密码: {reg['password']} | 手机: {reg['mobile']}")

        # 显示失败的账号
        if failed_registrations:
            print(f"\n[失败] 失败注册的账号:")
            for reg in failed_registrations:
                print(f"  {reg['index']}. 用户名: {reg['username']} | 错误: {reg['error']}")

        # 保存结果
        if successful_registrations or failed_registrations:
            save_choice = input("\n是否保存注册结果到文件？(y/n): ").strip().lower()
            if save_choice == 'y':
                save_registration_results(successful_registrations, failed_registrations)

    except Exception as e:
        print(f"[失败] 程序异常: {e}")
    finally:
        if register:
            register.close()

def save_registration_results(successful, failed):
    """保存注册结果到文件"""
    try:
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"registration_results_{timestamp}.txt"

        with open(filename, 'w', encoding='utf-8') as f:
            f.write("970faka.com 注册结果\n")
            f.write("=" * 50 + "\n")
            f.write(f"生成时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            f.write(f"成功注册: {len(successful)} 个\n")
            f.write(f"失败注册: {len(failed)} 个\n")
            f.write(f"总计: {len(successful) + len(failed)} 个\n\n")

            if successful:
                f.write("成功注册的账号:\n")
                f.write("-" * 30 + "\n")
                for reg in successful:
                    f.write(f"{reg['index']}. 用户名: {reg['username']}\n")
                    f.write(f"   密码: {reg['password']}\n")
                    f.write(f"   手机: {reg['mobile']}\n\n")

            if failed:
                f.write("失败注册的账号:\n")
                f.write("-" * 30 + "\n")
                for reg in failed:
                    f.write(f"{reg['index']}. 用户名: {reg['username']}\n")
                    f.write(f"   错误: {reg['error']}\n")
                    f.write(f"   步骤: {reg['step']}\n\n")

        print(f"[保存] 结果已保存到: {filename}")

    except Exception as e:
        print(f"[失败] 保存结果失败: {e}")

if __name__ == "__main__":
    main()
