#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
用户注册工具 - Qt6 GUI版本
"""

import sys
import os
# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)
import json
import csv
import time
import random
import string
import threading
from typing import List, Dict
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QLabel, QLineEdit, QPushButton, QTextEdit, QFileDialog,
    QSpinBox, QDoubleSpinBox, QProgressBar, QTableWidget, QTableWidgetItem,
    QMessageBox, QGroupBox, QFormLayout, QCheckBox, QComboBox,
    QSplitter, QHeaderView, QFrame
)
from PyQt6.QtCore import QThread, pyqtSignal, Qt, QTimer, QMutex
from PyQt6.QtGui import QFont, QIcon, QPixmap

# 导入注册核心功能
from core.register import UserRegister
from core.sms_api import YeziCloudAPI
from config.config_manager import config_manager
from browser_automation.selenium_register import SeleniumRegister

class YeziContinuousRegisterWorker(QThread):
    """椰子云连续注册工作线程 - 每次注册后重新打开浏览器"""
    progress_updated = pyqtSignal(int, int)  # 当前进度, 总数
    result_ready = pyqtSignal(dict)  # 注册结果

    def __init__(self, sms_api, project_id, register_count,
                 username_length, password_length, username_prefix="",
                 headless=False, base_url="https://970faka.com"):
        super().__init__()
        self.sms_api = sms_api
        self.project_id = project_id
        self.register_count = register_count
        self.username_length = username_length
        self.password_length = password_length
        self.username_prefix = username_prefix
        self.headless = headless
        self.base_url = base_url
        self.is_running = True
        self.results = []

    def run(self):
        """运行连续注册 - 每次注册后重新打开浏览器"""
        try:
            print(f"[启动] 开始椰子云连续注册，总数: {self.register_count}")
            print("[模式] 每个账号注册完成后将关闭并重新打开浏览器")

            for i in range(self.register_count):
                if not self.is_running:
                    break

                print(f"\n{'='*50}")
                print(f"[进度] 开始注册第 {i+1}/{self.register_count} 个账号")
                print(f"{'='*50}")

                current_selenium = None
                try:
                    # 生成用户数据
                    import random
                    import string

                    if self.username_prefix:
                        username = self.username_prefix + ''.join(random.choices(string.digits, k=self.username_length))
                    else:
                        username = 'user' + ''.join(random.choices(string.digits, k=self.username_length))

                    password = ''.join(random.choices(string.ascii_letters + string.digits, k=self.password_length))

                    print(f"[用户] 用户名: {username}")
                    print(f"[密码] 密码: {password}")

                    # 为每个账号创建全新的Selenium实例
                    print(f"[浏览器] 创建新的浏览器实例...")
                    from browser_automation.selenium_register import SeleniumRegister

                    current_selenium = SeleniumRegister(
                        headless=self.headless,
                        use_yezi_cloud=True,
                        base_url=self.base_url
                    )

                    # 配置椰子云
                    print(f"[配置] 设置椰子云配置...")
                    username_api = getattr(self.sms_api, 'username', '')
                    password_api = getattr(self.sms_api, 'password', '')
                    current_selenium.setup_yezi_cloud(username_api, password_api, self.project_id)

                    print(f"[注册] 开始注册流程...")

                    # 执行注册
                    result = current_selenium.register_user(
                        username=username,
                        password=password,
                        mobile='',  # 椰子云会自动获取
                        invite_code='',
                        invite_token='',
                        use_yezi_cloud=True,
                        yezi_api=self.sms_api,
                        project_id=self.project_id
                    )

                    # 记录结果
                    result['index'] = i + 1
                    result['username'] = username
                    result['password'] = password
                    self.results.append(result)

                    # 发送结果信号
                    self.result_ready.emit(result)

                    # 发送进度信号
                    self.progress_updated.emit(i + 1, self.register_count)

                    if result['success']:
                        print(f"[成功] 第 {i+1} 个账号注册成功: {username}")
                        print(f"[手机] 手机号: {result.get('mobile', '未知')}")
                    else:
                        print(f"[失败] 第 {i+1} 个账号注册失败: {result.get('error', '未知错误')}")
                        print(f"[步骤] 失败步骤: {result.get('step', '未知')}")

                except Exception as e:
                    print(f"[异常] 第 {i+1} 个账号注册异常: {e}")

                    error_result = {
                        'success': False,
                        'error': f'注册异常: {str(e)}',
                        'index': i + 1,
                        'username': username if 'username' in locals() else '未知',
                        'password': password if 'password' in locals() else '未知'
                    }

                    self.results.append(error_result)
                    self.result_ready.emit(error_result)
                    self.progress_updated.emit(i + 1, self.register_count)

                finally:
                    # 每次注册完成后都关闭浏览器
                    if current_selenium:
                        try:
                            print(f"[清理] 关闭第 {i+1} 个账号的浏览器实例...")
                            current_selenium.close()
                            print(f"[清理] 浏览器已关闭")
                        except Exception as e:
                            print(f"[警告] 关闭浏览器失败: {e}")

                    # 如果还有更多账号要注册，添加延迟
                    if i < self.register_count - 1 and self.is_running:
                        import time
                        delay = 5  # 增加延迟时间，确保浏览器完全关闭
                        print(f"[等待] {delay} 秒后开始下一个账号注册...")
                        time.sleep(delay)

            print(f"\n{'='*50}")
            print(f"[完成] 椰子云连续注册完成，总计: {len(self.results)}")

            # 统计结果
            success_count = len([r for r in self.results if r.get('success', False)])
            failed_count = len(self.results) - success_count
            print(f"[统计] 成功: {success_count} 个，失败: {failed_count} 个")
            print(f"{'='*50}")

        except Exception as e:
            print(f"[异常] 椰子云连续注册异常: {e}")

    def stop(self):
        """停止注册"""
        self.is_running = False


class YeziMultiThreadRegisterWorker(QThread):
    """椰子云多线程注册工作线程"""
    progress_updated = pyqtSignal(int, int)  # 当前进度, 总数
    result_ready = pyqtSignal(dict)  # 注册结果

    def __init__(self, users, delay_range, max_retries, max_threads=3,
                 sms_api=None, project_id="", enable_captcha=False,
                 captcha_mode="Selenium自动化", base_url="https://970faka.com", show_browser=True):
        super().__init__()
        self.users = users
        self.delay_range = delay_range
        self.max_retries = max_retries
        self.max_threads = max_threads
        self.sms_api = sms_api
        self.project_id = project_id
        self.enable_captcha = enable_captcha
        self.captcha_mode = captcha_mode
        self.base_url = base_url
        self.show_browser = show_browser
        self.is_running = True
        self.results = []
        self.mutex = QMutex()
        self.completed_count = 0

    def run(self):
        """运行多线程注册"""
        try:
            print(f"[启动] 开始椰子云多线程注册，用户数: {len(self.users)}, 线程数: {self.max_threads}")

            # 使用线程池执行注册任务
            with ThreadPoolExecutor(max_workers=self.max_threads) as executor:
                # 提交所有注册任务
                future_to_user = {
                    executor.submit(self.register_single_user, user): user
                    for user in self.users
                }

                # 处理完成的任务
                for future in as_completed(future_to_user):
                    if not self.is_running:
                        break

                    user = future_to_user[future]
                    try:
                        result = future.result()

                        # 线程安全地更新结果
                        self.mutex.lock()
                        self.results.append(result)
                        self.completed_count += 1
                        current_progress = self.completed_count
                        self.mutex.unlock()

                        # 发送信号
                        self.result_ready.emit(result)
                        self.progress_updated.emit(current_progress, len(self.users))

                        print(f"[进度] 进度: {current_progress}/{len(self.users)}")

                        # 添加延迟
                        if current_progress < len(self.users):
                            delay = random.uniform(self.delay_range[0], self.delay_range[1])
                            time.sleep(delay)

                    except Exception as e:
                        print(f"[失败] 处理注册结果异常: {e}")

                        # 记录失败结果
                        error_result = {
                            'success': False,
                            'username': user.get('username', '未知'),
                            'error': f"处理异常: {e}"
                        }

                        self.mutex.lock()
                        self.results.append(error_result)
                        self.completed_count += 1
                        current_progress = self.completed_count
                        self.mutex.unlock()

                        self.result_ready.emit(error_result)
                        self.progress_updated.emit(current_progress, len(self.users))

            print(f"[完成] 椰子云多线程注册完成，总计: {len(self.results)}")

        except Exception as e:
            print(f"[失败] 椰子云多线程注册异常: {e}")

    def register_single_user(self, user):
        """注册单个用户"""
        selenium_register = None
        try:
            print(f"[重试] 开始注册用户: {user['username']}")

            # 创建独立的Selenium注册器
            selenium_register = SeleniumRegister(
                headless=not self.show_browser,  # 根据配置决定是否显示浏览器
                use_yezi_cloud=True,
                base_url=self.base_url
            )

            # 配置椰子云
            if self.sms_api:
                # 从API实例获取用户名和密码
                username = getattr(self.sms_api, 'username', '')
                password = getattr(self.sms_api, 'password', '')
                selenium_register.setup_yezi_cloud(username, password, self.project_id)

            # 执行注册
            result = selenium_register.register_user(
                username=user['username'],
                password=user['password'],
                mobile=user.get('mobile', ''),
                invite_code=user.get('invite_code', ''),
                invite_token=user.get('invite_token', ''),
                use_yezi_cloud=True,
                yezi_api=self.sms_api,
                project_id=self.project_id
            )

            print(f"[成功] 用户 {user['username']} 注册完成: {result.get('success', False)}")
            return result

        except Exception as e:
            print(f"[失败] 用户 {user['username']} 注册异常: {e}")
            return {
                'success': False,
                'username': user['username'],
                'error': str(e)
            }
        finally:
            # 确保关闭Selenium
            if selenium_register:
                try:
                    selenium_register.close()
                except:
                    pass

    def stop(self):
        """停止注册"""
        self.is_running = False


class MultiThreadRegisterWorker(QThread):
    """多线程注册工作类"""
    progress_updated = pyqtSignal(int, int, str)  # current, total, message
    result_ready = pyqtSignal(dict)  # result
    finished_all = pyqtSignal(list)  # all results
    sms_code_needed = pyqtSignal(str, str)  # mobile, project_id

    def __init__(self, register_instance, users, delay_range, max_retries,
                 max_threads=3, sms_api=None, enable_sms=False, project_id="",
                 enable_captcha=False, captcha_mode="自动识别+手动确认", base_url="https://970faka.com"):
        super().__init__()
        self.register_instance = register_instance
        self.users = users
        self.delay_range = delay_range
        self.max_retries = max_retries
        self.max_threads = max_threads
        self.sms_api = sms_api
        self.enable_sms = enable_sms
        self.project_id = project_id
        self.enable_captcha = enable_captcha
        self.captcha_mode = captcha_mode
        self.base_url = base_url
        self.is_running = True
        self.results = []
        self.mutex = QMutex()
        self.completed_count = 0

        # Selenium注册器（延迟初始化）
        self.selenium_register = None

        # 创建注册日志文件
        import datetime
        log_time = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_file = f"registration_log_{log_time}.txt"

    def log_registration(self, message):
        """记录注册日志"""
        try:
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            log_message = f"[{timestamp}] {message}\n"

            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_message)
        except Exception as e:
            print(f"日志记录失败: {e}")

    def register_single_user(self, user_data, index):
        """注册单个用户"""
        if not self.is_running:
            return None

        # 设置当前处理的用户索引
        self.current_index = index
        user = user_data.copy()

        # 如果启用椰子云短信验证码功能，先获取手机号
        if self.enable_sms and self.sms_api and self.project_id:
            try:
                # 如果用户没有手机号，从椰子云获取
                if not user.get('mobile'):
                    self.progress_updated.emit(index, len(self.users),
                                             f"[手机] 椰子云获取手机号: {user['username']}")

                    mobile_result = self.sms_api.get_mobile(self.project_id)
                    if not mobile_result['success']:
                        self.progress_updated.emit(index, len(self.users),
                                                 f"[失败] 椰子云获取手机号失败: {mobile_result['error']}")
                        self.log_registration(f"椰子云获取手机号失败 - 用户: {user['username']}, 错误: {mobile_result['error']}")
                        return

                    user['mobile'] = mobile_result['mobile']
                    self.progress_updated.emit(index, len(self.users),
                                             f"[手机] 椰子云手机号: {user['mobile']} (用户: {user['username']})")
                    self.log_registration(f"椰子云获取手机号成功 - 用户: {user['username']}, 手机号: {user['mobile']}")

            except Exception as e:
                self.progress_updated.emit(index, len(self.users),
                                         f"[失败] 椰子云处理异常: {str(e)}")
                self.log_registration(f"椰子云处理异常 - 用户: {user['username']}, 异常: {str(e)}")
                return

        # 处理验证码
        captcha_code = ""
        if self.enable_captcha:
            try:
                from captcha_handler import CaptchaHandler
                captcha_handler = CaptchaHandler(self.register_instance)

                self.progress_updated.emit(index, len(self.users),
                                         f"[验证] 处理验证码: {user['username']}")

                if self.captcha_mode == "自动识别(ddddocr)":
                    # 完全自动识别
                    captcha_result = captcha_handler.auto_solve_captcha()
                    if captcha_result['success']:
                        captcha_code = captcha_result['captcha_code']
                        self.progress_updated.emit(index, len(self.users),
                                                 f"🤖 自动识别成功: {captcha_code}")
                    else:
                        self.progress_updated.emit(index, len(self.users),
                                                 f"[失败] 自动识别失败: {captcha_result.get('error', '')}")
                        return  # 识别失败，跳过此用户

                elif self.captcha_mode == "手动输入":
                    # 纯手动输入（在主线程中处理）
                    self.progress_updated.emit(index, len(self.users),
                                             f"⏳ 等待手动输入验证码: {user['username']}")
                    # 这里需要在主线程中处理，暂时跳过
                    pass

                elif self.captcha_mode == "Selenium自动化":
                    # 使用Selenium自动化处理整个注册流程
                    self.progress_updated.emit(index, len(self.users),
                                             f"🤖 Selenium自动化注册: {user['username']}")

                    # 使用Selenium自动化注册
                    result = self.selenium_register_user(user, index)

                    if result['success']:
                        self.progress_updated.emit(index, len(self.users),
                                                 f"[成功] Selenium注册成功: {user['username']}")
                    else:
                        self.progress_updated.emit(index, len(self.users),
                                                 f"[失败] Selenium注册失败: {result['error']}")
                    return  # Selenium模式直接返回，不需要后续处理

                else:  # 自动识别+手动确认
                    # 自动识别但允许手动修改（在主线程中处理）
                    self.progress_updated.emit(index, len(self.users),
                                             f"[检查] 自动识别+确认: {user['username']}")
                    # 这里需要在主线程中处理，暂时跳过
                    pass

            except Exception as e:
                self.progress_updated.emit(index, len(self.users),
                                         f"[失败] 验证码处理异常: {str(e)}")

        # 执行注册
        for attempt in range(self.max_retries):
            if not self.is_running:
                break

            self.progress_updated.emit(index, len(self.users),
                                     f"[启动] 正在注册: {user['username']} (尝试 {attempt + 1}/{self.max_retries})")

            # 记录注册尝试日志
            self.log_registration(f"注册尝试 {attempt + 1}/{self.max_retries} - 用户: {user['username']}, 手机号: {user['mobile']}, 验证码: {user.get('mobile_code', '无')}")

            # 根据是否启用短信验证选择注册方法
            if self.enable_sms and self.sms_api and self.project_id:
                # 使用完整的短信验证注册流程
                result = self.register_instance.register_with_sms_flow(
                    username=user['username'],
                    password=user['password'],
                    mobile=user.get('mobile', ''),
                    invite_code=user.get('invite_code', ''),
                    invite_token=user.get('invite_token', ''),
                    use_yezi_cloud=True,
                    yezi_api=self.sms_api,
                    project_id=self.project_id
                )
            elif self.enable_captcha:
                # 使用完整的注册流程（包含验证码）
                result = self.register_with_captcha_flow(user, captcha_code)
            else:
                # 使用简单的注册方法
                result = self.register_instance.register_user(
                    username=user['username'],
                    password=user['password'],
                    mobile=user['mobile'],
                    mobile_code=user.get('mobile_code', ''),
                    invite_code=user.get('invite_code', ''),
                    captcha_code=captcha_code
                )

            if result['success']:
                self.progress_updated.emit(index, len(self.users),
                                         f"[成功] 注册成功: {user['username']}")
                # 记录成功日志
                self.log_registration(f"注册成功 - 用户: {user['username']}, 手机号: {user['mobile']}, 响应: {result.get('message', '')}")
                break
            else:
                if attempt < self.max_retries - 1:
                    self.progress_updated.emit(index, len(self.users),
                                             f"[警告] 重试: {user['username']} - {result.get('error', '')}")
                    # 记录重试日志
                    self.log_registration(f"注册重试 - 用户: {user['username']}, 手机号: {user['mobile']}, 错误: {result.get('error', '')}")
                    time.sleep(random.uniform(0.5, 1.5))
                else:
                    self.progress_updated.emit(index, len(self.users),
                                             f"[失败] 注册失败: {user['username']} - {result.get('error', '')}")
                    # 记录失败日志
                    self.log_registration(f"注册失败 - 用户: {user['username']}, 手机号: {user['mobile']}, 最终错误: {result.get('error', '')}")

        # 确保结果中包含用户名和手机号
        result['username'] = user['username']
        result['mobile'] = user['mobile']

        # 线程安全地更新结果
        self.mutex.lock()
        try:
            self.results.append(result)
            self.completed_count += 1
            self.result_ready.emit(result)
        finally:
            self.mutex.unlock()

        # 释放手机号 (如果使用了短信API且不是完整流程)
        # 注意：如果使用register_with_sms_flow，手机号释放已经在流程中处理了
        if (self.enable_sms and self.sms_api and user.get('mobile') and
            not (self.enable_sms and self.sms_api and self.project_id)):
            try:
                time.sleep(5)  # 等待5秒再释放
                self.log_registration(f"准备释放椰子云手机号 - 用户: {user['username']}, 手机号: {user['mobile']}")
                release_result = self.sms_api.release_mobile(user['mobile'], self.project_id)
                if release_result.get('success'):
                    self.log_registration(f"椰子云手机号释放成功 - 用户: {user['username']}, 手机号: {user['mobile']}")
                else:
                    self.log_registration(f"椰子云手机号释放失败 - 用户: {user['username']}, 手机号: {user['mobile']}, 错误: {release_result.get('error', '')}")
            except Exception as e:
                self.log_registration(f"椰子云手机号释放异常 - 用户: {user['username']}, 手机号: {user['mobile']}, 异常: {str(e)}")

        return result

    def register_with_captcha_flow(self, user, captcha_code=""):
        """
        使用完整验证码流程注册（包含椰子云短信触发）

        Args:
            user: 用户数据
            captcha_code: 验证码（可选）

        Returns:
            dict: 注册结果
        """
        try:
            # 第一步：获取图形验证码
            self.progress_updated.emit(self.current_index, len(self.users),
                                     f"[检查] 获取验证码: {user['username']}")

            captcha_result = self.register_instance.get_captcha_start()
            if not captcha_result['success']:
                return {
                    'success': False,
                    'error': f"获取验证码失败: {captcha_result['error']}"
                }

            img_url = captcha_result['img_url']
            captcha_key = captcha_result['captcha_key']

            # 第二步：下载并识别验证码
            self.progress_updated.emit(self.current_index, len(self.users),
                                     f"🤖 识别验证码: {user['username']}")

            recognize_result = self.register_instance.download_and_recognize_captcha(img_url)
            if not recognize_result['success']:
                return {
                    'success': False,
                    'error': f"识别验证码失败: {recognize_result['error']}"
                }

            captcha_code = recognize_result.get('captcha_code', '')
            if not captcha_code:
                return {
                    'success': False,
                    'error': "验证码识别失败，无法获取验证码"
                }

            # 第三步：验证图形验证码
            self.progress_updated.emit(self.current_index, len(self.users),
                                     f"[成功] 验证验证码: {user['username']} ({captcha_code})")

            check_url = captcha_result['check_url']
            verify_result = self.register_instance.verify_captcha(captcha_code, check_url)
            if not verify_result['success']:
                return {
                    'success': False,
                    'error': f"验证码验证失败: {verify_result['error']}"
                }

            ticket = verify_result['ticket']

            # 第四步：发送短信验证码
            self.progress_updated.emit(self.current_index, len(self.users),
                                     f"[手机] 发送短信: {user['username']} ({user['mobile']})")

            sms_result = self.register_instance.send_sms(user['mobile'], ticket)
            if not sms_result['success']:
                return {
                    'success': False,
                    'error': f"发送短信失败: {sms_result['error']}"
                }

            # 第五步：从椰子云获取短信验证码
            if self.enable_sms and self.sms_api and user.get('mobile'):
                self.progress_updated.emit(self.current_index, len(self.users),
                                         f"[短信] 椰子云等待短信: {user['username']}")

                mobile_code = self.wait_for_yezi_sms(user['mobile'])
                if not mobile_code:
                    return {
                        'success': False,
                        'error': "椰子云获取短信验证码超时"
                    }

                user['mobile_code'] = mobile_code

            # 第六步：最终注册
            self.progress_updated.emit(self.current_index, len(self.users),
                                     f"[启动] 执行注册: {user['username']}")

            result = self.register_instance.register_user(
                username=user['username'],
                password=user['password'],
                mobile=user['mobile'],
                mobile_code=user.get('mobile_code', ''),
                invite_code=user.get('invite_code', ''),
                invite_token=user.get('invite_token', '')
            )

            return result

        except Exception as e:
            return {
                'success': False,
                'error': f'完整注册流程异常: {str(e)}'
            }

    def wait_for_yezi_sms(self, mobile: str, max_wait_time: int = 120) -> str:
        """
        等待椰子云短信验证码

        Args:
            mobile: 手机号
            max_wait_time: 最大等待时间

        Returns:
            str: 验证码，失败返回空字符串
        """
        try:
            start_time = time.time()
            check_interval = 3

            while time.time() - start_time < max_wait_time:
                if not self.is_running:
                    break

                # 检查是否收到短信
                sms_result = self.sms_api.get_sms(self.project_id, mobile)

                if sms_result['success'] and sms_result.get('received'):
                    verification_code = sms_result.get('code', '')
                    if verification_code:
                        self.progress_updated.emit(self.current_index, len(self.users),
                                                 f"[短信] 椰子云验证码: {verification_code}")
                        return verification_code

                # 等待一段时间后重试
                time.sleep(check_interval)

            return ""

        except Exception as e:
            self.progress_updated.emit(self.current_index, len(self.users),
                                     f"[失败] 椰子云异常: {str(e)}")
            return ""

    def run(self):
        """执行多线程批量注册"""
        self.results = []
        self.completed_count = 0
        total = len(self.users)

        # 使用线程池执行注册
        with ThreadPoolExecutor(max_workers=self.max_threads) as executor:
            # 提交所有任务
            futures = []
            for i, user in enumerate(self.users):
                if not self.is_running:
                    break
                future = executor.submit(self.register_single_user, user, i)
                futures.append(future)

                # 添加延迟避免同时启动过多任务
                if i < total - 1:
                    time.sleep(random.uniform(self.delay_range[0], self.delay_range[1]))

            # 等待所有任务完成
            for future in as_completed(futures):
                if not self.is_running:
                    break
                try:
                    future.result()
                except Exception as e:
                    self.progress_updated.emit(0, total, f"[失败] 线程异常: {str(e)}")

        self.finished_all.emit(self.results)

    def selenium_register_user(self, user: Dict, index: int) -> Dict:
        """
        使用Selenium自动化注册用户

        Args:
            user: 用户数据
            index: 用户索引

        Returns:
            注册结果
        """
        try:
            # 延迟初始化Selenium注册器
            if self.selenium_register is None:
                self.selenium_register = SeleniumRegister(
                    headless=True,  # GUI模式下使用无头模式
                    use_yezi_cloud=bool(self.sms_api),
                    base_url=self.base_url
                )

                # 如果有椰子云API，设置项目ID
                if self.sms_api:
                    self.selenium_register.yezi_api = self.sms_api
                    self.selenium_register.project_id = self.project_id

            # 执行Selenium自动化注册
            result = self.selenium_register.register_user(
                username=user['username'],
                password=user['password'],
                mobile=user.get('mobile', '')
            )

            return result

        except Exception as e:
            return {
                'success': False,
                'error': f'Selenium注册异常: {str(e)}',
                'username': user.get('username', ''),
                'mobile': user.get('mobile', '')
            }

    def stop(self):
        """停止注册"""
        self.is_running = False

        # 关闭Selenium注册器
        if self.selenium_register:
            try:
                self.selenium_register.close()
            except:
                pass

class RegisterGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.register_instance = None
        self.worker = None
        self.results = []
        self.users_data = []

        # 加载配置
        self.load_config()

        # 初始化界面
        self.init_ui()

        # 应用配置到界面（必须在init_ui之后）
        self.apply_config_to_ui()

        # 初始化注册实例
        self.init_register_instance()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("用户注册工具 v1.0")
        self.setGeometry(100, 100, 1000, 700)
        
        # 设置应用图标（如果有的话）
        # self.setWindowIcon(QIcon('icon.png'))
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # 创建各个选项卡
        self.create_config_tab()
        self.create_batch_register_tab()
        self.create_results_tab()
        
        # 状态栏
        self.statusBar().showMessage("就绪")
        
    def create_config_tab(self):
        """创建配置选项卡"""
        config_widget = QWidget()
        layout = QVBoxLayout(config_widget)
        
        # 基本配置组
        basic_group = QGroupBox("基本配置")
        basic_layout = QFormLayout(basic_group)
        
        self.base_url_edit = QLineEdit("https://spikeesoft.com")
        self.endpoint_edit = QLineEdit("/merchantApi/user/register")
        self.phpsessid_edit = QLineEdit()
        
        basic_layout.addRow("域名:", self.base_url_edit)
        basic_layout.addRow("注册接口:", self.endpoint_edit)
        basic_layout.addRow("PHPSESSID:", self.phpsessid_edit)
        
        # 验证配置组
        validation_group = QGroupBox("验证配置")
        validation_layout = QFormLayout(validation_group)
        
        self.min_username_spin = QSpinBox()
        self.min_username_spin.setRange(1, 50)
        self.min_username_spin.setValue(3)
        
        self.max_username_spin = QSpinBox()
        self.max_username_spin.setRange(1, 50)
        self.max_username_spin.setValue(20)
        
        self.min_password_spin = QSpinBox()
        self.min_password_spin.setRange(1, 50)
        self.min_password_spin.setValue(6)
        
        self.max_password_spin = QSpinBox()
        self.max_password_spin.setRange(1, 50)
        self.max_password_spin.setValue(20)
        
        validation_layout.addRow("用户名最小长度:", self.min_username_spin)
        validation_layout.addRow("用户名最大长度:", self.max_username_spin)
        validation_layout.addRow("密码最小长度:", self.min_password_spin)
        validation_layout.addRow("密码最大长度:", self.max_password_spin)
        
        # 随机生成配置组
        random_group = QGroupBox("随机生成配置")
        random_layout = QFormLayout(random_group)

        self.username_length_spin = QSpinBox()
        self.username_length_spin.setRange(3, 20)
        self.username_length_spin.setValue(8)

        self.password_length_spin = QSpinBox()
        self.password_length_spin.setRange(6, 20)
        self.password_length_spin.setValue(10)

        self.username_prefix_edit = QLineEdit("user")
        self.include_numbers_check = QCheckBox()
        self.include_numbers_check.setChecked(True)
        self.include_letters_check = QCheckBox()
        self.include_letters_check.setChecked(True)

        random_layout.addRow("用户名长度:", self.username_length_spin)
        random_layout.addRow("密码长度:", self.password_length_spin)
        random_layout.addRow("用户名前缀:", self.username_prefix_edit)
        random_layout.addRow("包含数字:", self.include_numbers_check)
        random_layout.addRow("包含字母:", self.include_letters_check)

        # 椰子云API配置组
        sms_group = QGroupBox("椰子云短信API配置")
        sms_layout = QFormLayout(sms_group)

        self.enable_sms_check = QCheckBox()
        self.enable_sms_check.setChecked(False)
        self.sms_username_edit = QLineEdit()
        self.sms_password_edit = QLineEdit()
        self.sms_password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.sms_project_id_edit = QLineEdit()

        sms_layout.addRow("启用短信验证码:", self.enable_sms_check)
        sms_layout.addRow("椰子云用户名:", self.sms_username_edit)
        sms_layout.addRow("椰子云密码:", self.sms_password_edit)
        sms_layout.addRow("项目ID:", self.sms_project_id_edit)

        # 批量注册配置组
        batch_group = QGroupBox("批量注册配置")
        batch_layout = QFormLayout(batch_group)

        self.delay_min_spin = QDoubleSpinBox()
        self.delay_min_spin.setRange(0.1, 60.0)
        self.delay_min_spin.setValue(1.0)
        self.delay_min_spin.setSuffix(" 秒")

        self.delay_max_spin = QDoubleSpinBox()
        self.delay_max_spin.setRange(0.1, 60.0)
        self.delay_max_spin.setValue(3.0)
        self.delay_max_spin.setSuffix(" 秒")

        self.max_retries_spin = QSpinBox()
        self.max_retries_spin.setRange(1, 10)
        self.max_retries_spin.setValue(3)

        self.max_threads_spin = QSpinBox()
        self.max_threads_spin.setRange(1, 10)
        self.max_threads_spin.setValue(3)

        batch_layout.addRow("最大线程数:", self.max_threads_spin)
        batch_layout.addRow("最小延迟:", self.delay_min_spin)
        batch_layout.addRow("最大延迟:", self.delay_max_spin)
        batch_layout.addRow("最大重试次数:", self.max_retries_spin)
        
        # 按钮
        button_layout = QHBoxLayout()
        self.apply_config_btn = QPushButton("应用配置")
        self.apply_config_btn.clicked.connect(self.apply_config)
        self.reset_config_btn = QPushButton("重置配置")
        self.reset_config_btn.clicked.connect(self.reset_config)
        self.yezi_register_btn = QPushButton("椰子云自动注册")
        self.yezi_register_btn.clicked.connect(self.start_yezi_register)
        self.yezi_register_btn.setStyleSheet("QPushButton { background-color: #2196F3; color: white; font-weight: bold; }")

        self.yezi_continuous_btn = QPushButton("椰子云连续注册")
        self.yezi_continuous_btn.clicked.connect(self.start_yezi_continuous_register)
        self.yezi_continuous_btn.setStyleSheet("QPushButton { background-color: #FF9800; color: white; font-weight: bold; }")

        self.yezi_multi_register_btn = QPushButton("椰子云多线程注册")
        self.yezi_multi_register_btn.clicked.connect(self.start_yezi_multi_register)
        self.yezi_multi_register_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")

        button_layout.addWidget(self.apply_config_btn)
        button_layout.addWidget(self.reset_config_btn)
        button_layout.addWidget(self.yezi_register_btn)
        button_layout.addWidget(self.yezi_continuous_btn)
        button_layout.addWidget(self.yezi_multi_register_btn)
        button_layout.addStretch()
        
        # 添加到布局
        layout.addWidget(basic_group)
        layout.addWidget(validation_group)
        layout.addWidget(random_group)
        layout.addWidget(sms_group)
        layout.addWidget(batch_group)
        layout.addLayout(button_layout)
        layout.addStretch()
        
        self.tab_widget.addTab(config_widget, "配置")

    def create_batch_register_tab(self):
        """创建批量注册选项卡"""
        batch_widget = QWidget()
        layout = QVBoxLayout(batch_widget)

        # 数据导入组
        import_group = QGroupBox("数据导入")
        import_layout = QHBoxLayout(import_group)

        self.csv_mode_btn = QPushButton("从CSV导入")
        self.csv_mode_btn.clicked.connect(self.browse_csv_file)
        self.txt_mobile_btn = QPushButton("导入手机号TXT")
        self.txt_mobile_btn.clicked.connect(self.import_mobile_txt)
        self.txt_invite_btn = QPushButton("导入邀请码TXT")
        self.txt_invite_btn.clicked.connect(self.import_invite_txt)
        self.create_sample_btn = QPushButton("创建示例CSV")
        self.create_sample_btn.clicked.connect(self.create_sample_csv)

        import_layout.addWidget(self.csv_mode_btn)
        import_layout.addWidget(self.txt_mobile_btn)
        import_layout.addWidget(self.txt_invite_btn)
        import_layout.addWidget(self.create_sample_btn)
        import_layout.addStretch()

        # 注册数量设置组
        count_group = QGroupBox("注册设置")
        count_layout = QFormLayout(count_group)

        self.register_count_spin = QSpinBox()
        self.register_count_spin.setRange(1, 10000)
        self.register_count_spin.setValue(10)
        self.register_count_spin.setSuffix(" 个用户")

        self.generate_users_btn = QPushButton("生成用户数据")
        self.generate_users_btn.clicked.connect(self.generate_users_for_register)

        count_layout.addRow("注册用户数量:", self.register_count_spin)
        count_layout.addRow("", self.generate_users_btn)

        # 手机号和邀请码配置组
        mobile_group = QGroupBox("手机号和邀请码配置")
        mobile_layout = QFormLayout(mobile_group)

        self.mobile_prefix_edit = QLineEdit("138")
        self.mobile_prefix_edit.setMaxLength(3)
        self.invite_code_edit = QLineEdit("ABC123")

        # 邀请码模式选择
        self.invite_mode_combo = QComboBox()
        self.invite_mode_combo.addItems(["统一邀请码", "唯一邀请码(从TXT导入)", "不使用邀请码"])
        self.invite_mode_combo.currentTextChanged.connect(self.on_invite_mode_changed)

        mobile_layout.addRow("手机号前缀:", self.mobile_prefix_edit)
        mobile_layout.addRow("邀请码模式:", self.invite_mode_combo)
        mobile_layout.addRow("统一邀请码:", self.invite_code_edit)

        # 验证码配置组
        captcha_group = QGroupBox("验证码配置")
        captcha_layout = QFormLayout(captcha_group)

        self.enable_captcha_check = QCheckBox()
        self.enable_captcha_check.setChecked(False)
        self.captcha_endpoint_edit = QLineEdit("/merchantApi/Common/captchaStart")

        # 验证码识别模式
        self.captcha_mode_combo = QComboBox()
        self.captcha_mode_combo.addItems(["自动识别(ddddocr)", "手动输入", "自动识别+手动确认", "Selenium自动化"])
        self.captcha_mode_combo.setCurrentText("Selenium自动化")

        # 显示浏览器选项
        self.show_browser_check = QCheckBox()
        self.show_browser_check.setChecked(True)  # 默认显示浏览器
        self.show_browser_check.setToolTip("勾选后可以看到浏览器自动化过程，取消勾选则在后台运行")

        captcha_layout.addRow("启用验证码:", self.enable_captcha_check)
        captcha_layout.addRow("验证码接口:", self.captcha_endpoint_edit)
        captcha_layout.addRow("识别模式:", self.captcha_mode_combo)
        captcha_layout.addRow("显示浏览器:", self.show_browser_check)

        # 控制按钮
        control_layout = QHBoxLayout()
        self.start_batch_btn = QPushButton("开始批量注册")
        self.start_batch_btn.clicked.connect(self.start_batch_register)
        self.start_batch_btn.setEnabled(False)
        self.stop_batch_btn = QPushButton("停止注册")
        self.stop_batch_btn.clicked.connect(self.stop_batch_register)
        self.stop_batch_btn.setEnabled(False)
        self.clear_users_btn = QPushButton("清空用户列表")
        self.clear_users_btn.clicked.connect(self.clear_users_list)

        control_layout.addWidget(self.start_batch_btn)
        control_layout.addWidget(self.stop_batch_btn)
        control_layout.addWidget(self.clear_users_btn)
        control_layout.addStretch()

        # 进度显示
        progress_group = QGroupBox("进度")
        progress_layout = QVBoxLayout(progress_group)

        self.progress_bar = QProgressBar()
        self.progress_label = QLabel("就绪")

        progress_layout.addWidget(self.progress_bar)
        progress_layout.addWidget(self.progress_label)

        # 用户列表预览
        preview_group = QGroupBox("用户列表预览")
        preview_layout = QVBoxLayout(preview_group)

        self.user_table = QTableWidget()
        self.user_table.setColumnCount(4)
        self.user_table.setHorizontalHeaderLabels(["用户名", "密码", "手机号", "邀请码"])
        self.user_table.horizontalHeader().setStretchLastSection(True)

        preview_layout.addWidget(self.user_table)

        # 添加到布局
        layout.addWidget(import_group)
        layout.addWidget(count_group)
        layout.addWidget(mobile_group)
        layout.addWidget(captcha_group)
        layout.addLayout(control_layout)
        layout.addWidget(progress_group)
        layout.addWidget(preview_group)

        self.tab_widget.addTab(batch_widget, "批量注册")

    def create_results_tab(self):
        """创建结果选项卡"""
        results_widget = QWidget()
        layout = QVBoxLayout(results_widget)

        # 统计信息
        stats_group = QGroupBox("统计信息")
        stats_layout = QFormLayout(stats_group)

        self.total_label = QLabel("0")
        self.success_label = QLabel("0")
        self.failed_label = QLabel("0")
        self.success_rate_label = QLabel("0%")

        stats_layout.addRow("总计:", self.total_label)
        stats_layout.addRow("成功:", self.success_label)
        stats_layout.addRow("失败:", self.failed_label)
        stats_layout.addRow("成功率:", self.success_rate_label)

        # 操作按钮
        button_layout = QHBoxLayout()
        self.export_results_btn = QPushButton("导出结果")
        self.export_results_btn.clicked.connect(self.export_results)
        self.clear_results_btn = QPushButton("清空结果")
        self.clear_results_btn.clicked.connect(self.clear_results)

        button_layout.addWidget(self.export_results_btn)
        button_layout.addWidget(self.clear_results_btn)
        button_layout.addStretch()

        # 结果表格
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(5)
        self.results_table.setHorizontalHeaderLabels(["用户名", "手机号", "状态", "错误信息", "状态码"])
        self.results_table.horizontalHeader().setStretchLastSection(True)

        # 添加到布局
        layout.addWidget(stats_group)
        layout.addLayout(button_layout)
        layout.addWidget(self.results_table)

        self.tab_widget.addTab(results_widget, "注册结果")

    def init_register_instance(self):
        """初始化注册实例"""
        self.apply_config()

    def apply_config(self):
        """应用配置"""
        try:
            self.register_instance = UserRegister(
                base_url=self.base_url_edit.text().strip(),
                register_endpoint=self.endpoint_edit.text().strip(),
                min_username_length=self.min_username_spin.value(),
                max_username_length=self.max_username_spin.value(),
                min_password_length=self.min_password_spin.value(),
                max_password_length=self.max_password_spin.value()
            )

            # 设置会话Cookie
            phpsessid = self.phpsessid_edit.text().strip()
            if phpsessid:
                self.register_instance.set_session_cookie(phpsessid)

            self.statusBar().showMessage("配置已应用")
            QMessageBox.information(self, "成功", "配置已成功应用！")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"应用配置失败: {str(e)}")

    def reset_config(self):
        """重置配置"""
        self.base_url_edit.setText("https://spikeesoft.com")
        self.endpoint_edit.setText("/merchantApi/user/register")
        self.phpsessid_edit.clear()
        self.min_username_spin.setValue(3)
        self.max_username_spin.setValue(20)
        self.min_password_spin.setValue(6)
        self.max_password_spin.setValue(20)

        # 重置随机生成配置
        self.username_length_spin.setValue(8)
        self.password_length_spin.setValue(10)
        self.username_prefix_edit.setText("user")
        self.include_numbers_check.setChecked(True)
        self.include_letters_check.setChecked(True)

        # 重置椰子云API配置
        self.enable_sms_check.setChecked(False)
        self.sms_username_edit.clear()
        self.sms_password_edit.clear()
        self.sms_project_id_edit.clear()

        # 重置批量注册配置
        self.register_count_spin.setValue(10)
        self.max_threads_spin.setValue(3)
        self.delay_min_spin.setValue(1.0)
        self.delay_max_spin.setValue(3.0)
        self.max_retries_spin.setValue(3)

        # 重置手机号和邀请码配置
        self.mobile_prefix_edit.setText("138")
        self.invite_code_edit.setText("ABC123")
        self.invite_mode_combo.setCurrentText("统一邀请码")

        # 重置验证码配置
        self.enable_captcha_check.setChecked(False)
        self.captcha_endpoint_edit.setText("/merchantApi/Common/captchaStart")
        self.captcha_mode_combo.setCurrentText("Selenium自动化")
        self.show_browser_check.setChecked(True)

        self.statusBar().showMessage("配置已重置")

    def start_yezi_register(self):
        """启动椰子云自动注册"""
        try:
            # 检查椰子云配置
            sms_username = self.sms_username_edit.text().strip()
            sms_password = self.sms_password_edit.text().strip()
            project_id = self.sms_project_id_edit.text().strip()

            if not all([sms_username, sms_password, project_id]):
                QMessageBox.warning(self, "警告", "请先配置椰子云API信息！")
                return

            # 确认对话框
            reply = QMessageBox.question(
                self, "确认",
                "椰子云自动注册将使用Selenium自动化完成整个注册流程，包括：\n"
                "• 自动获取椰子云手机号\n"
                "• 自动识别验证码\n"
                "• 自动填写表单\n"
                "• 自动获取短信验证码\n"
                "• 自动提交注册\n\n"
                "确认开始？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                self.execute_yezi_register()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动椰子云自动注册失败: {str(e)}")

    def execute_yezi_register(self):
        """执行椰子云自动注册"""
        try:
            # 创建椰子云API实例
            sms_username = self.sms_username_edit.text().strip()
            sms_password = self.sms_password_edit.text().strip()
            project_id = self.sms_project_id_edit.text().strip()

            sms_api = YeziCloudAPI(sms_username, sms_password)

            # 登录椰子云
            login_result = sms_api.login()
            if not login_result['success']:
                QMessageBox.critical(self, "错误", f"椰子云登录失败: {login_result['error']}")
                return

            # 检查余额
            balance_result = sms_api.get_balance()
            if balance_result['success']:
                balance_data = balance_result['data'][0] if balance_result['data'] else {}
                balance = balance_data.get('money', '0')
                QMessageBox.information(self, "椰子云API", f"登录成功！当前余额: {balance} 元")

            # 创建Selenium注册器
            selenium_register = SeleniumRegister(
                headless=False,  # 显示浏览器窗口
                use_yezi_cloud=True,
                base_url=self.base_url_edit.text().strip()  # 使用GUI中配置的域名
            )

            # 设置椰子云API
            selenium_register.yezi_api = sms_api
            selenium_register.project_id = project_id

            # 生成随机用户名和密码
            import random
            import string
            username = 'user' + ''.join(random.choices(string.digits, k=8))
            password = username  # 密码与用户名相同

            # 显示进度对话框
            from PyQt6.QtWidgets import QProgressDialog
            progress = QProgressDialog("正在执行椰子云自动注册...", "取消", 0, 0, self)
            progress.setWindowTitle("椰子云自动注册")
            progress.setWindowModality(Qt.WindowModality.WindowModal)
            progress.show()

            # 执行注册
            result = selenium_register.register_user(
                username=username,
                password=password,
                mobile='',  # 椰子云会自动获取
                invite_code='',
                invite_token='',
                use_yezi_cloud=True,
                yezi_api=sms_api,
                project_id=project_id
            )

            progress.close()

            # 显示结果
            if result['success']:
                QMessageBox.information(
                    self, "成功",
                    f"椰子云自动注册成功！\n"
                    f"用户名: {username}\n"
                    f"密码: {password}\n"
                    f"手机号: {result.get('mobile', '未知')}"
                )
            else:
                QMessageBox.warning(
                    self, "失败",
                    f"椰子云自动注册失败！\n"
                    f"错误: {result.get('error', '未知错误')}"
                )

            # 关闭Selenium
            selenium_register.close()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"椰子云自动注册异常: {str(e)}")

    def start_yezi_continuous_register(self):
        """启动椰子云连续注册"""
        try:
            # 检查椰子云配置
            sms_username = self.sms_username_edit.text().strip()
            sms_password = self.sms_password_edit.text().strip()
            project_id = self.sms_project_id_edit.text().strip()

            if not all([sms_username, sms_password, project_id]):
                QMessageBox.warning(self, "警告", "请先配置椰子云API信息！")
                return

            # 获取注册数量
            register_count = self.register_count_spin.value()

            # 确认对话框
            reply = QMessageBox.question(
                self, "确认",
                f"椰子云连续注册将依次注册 {register_count} 个账号。\n\n"
                "功能包括：\n"
                "• 自动获取椰子云手机号\n"
                "• 自动识别验证码\n"
                "• 自动填写表单\n"
                "• 自动获取短信验证码\n"
                "• 自动提交注册\n"
                "• 注册成功后自动继续下一个\n\n"
                "确认开始？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                self.execute_yezi_continuous_register()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动椰子云连续注册失败: {str(e)}")

    def execute_yezi_continuous_register(self):
        """执行椰子云连续注册"""
        try:
            # 创建椰子云API实例
            sms_username = self.sms_username_edit.text().strip()
            sms_password = self.sms_password_edit.text().strip()
            project_id = self.sms_project_id_edit.text().strip()

            sms_api = YeziCloudAPI(sms_username, sms_password)

            # 登录椰子云
            login_result = sms_api.login()
            if not login_result['success']:
                QMessageBox.critical(self, "错误", f"椰子云登录失败: {login_result['error']}")
                return

            # 检查余额
            balance_result = sms_api.get_balance()
            if not balance_result['success']:
                QMessageBox.warning(self, "警告", f"无法获取椰子云余额: {balance_result['error']}")
            else:
                # 正确从data字段中获取余额
                balance_data = balance_result.get('data', [])
                if balance_data and len(balance_data) > 0:
                    balance = float(balance_data[0].get('money', '0'))
                else:
                    balance = 0

                register_count = self.register_count_spin.value()
                print(f"[检查] 椰子云余额检查: 余额={balance}, 需要注册={register_count}")

                if balance < register_count:
                    reply = QMessageBox.question(
                        self, "余额不足",
                        f"椰子云余额: {balance}，需要注册: {register_count}\n"
                        "余额可能不足，是否继续？",
                        QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
                    )
                    if reply == QMessageBox.StandardButton.No:
                        return

            # 获取注册数量和配置
            register_count = self.register_count_spin.value()
            username_length = self.username_length_spin.value()
            password_length = self.password_length_spin.value()
            username_prefix = self.username_prefix_edit.text().strip()

            # 获取浏览器配置
            headless = not self.show_browser_check.isChecked()
            base_url = self.base_url_edit.text().strip()

            # 创建并启动连续注册工作线程
            self.continuous_worker = YeziContinuousRegisterWorker(
                sms_api,
                project_id,
                register_count,
                username_length,
                password_length,
                username_prefix,
                headless,
                base_url
            )

            # 连接信号
            self.continuous_worker.progress_updated.connect(self.update_progress)
            self.continuous_worker.result_ready.connect(self.handle_yezi_result)
            self.continuous_worker.finished.connect(self.on_continuous_register_finished)

            # 启动工作线程
            self.continuous_worker.start()

            # 更新UI状态
            self.yezi_continuous_btn.setEnabled(False)
            self.yezi_register_btn.setEnabled(False)
            self.yezi_multi_register_btn.setEnabled(False)
            self.start_batch_btn.setEnabled(False)
            self.statusBar().showMessage("椰子云连续注册进行中...")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"椰子云连续注册异常: {str(e)}")

    def on_continuous_register_finished(self):
        """椰子云连续注册完成"""
        try:
            # 恢复UI状态
            self.yezi_continuous_btn.setEnabled(True)
            self.yezi_register_btn.setEnabled(True)
            self.yezi_multi_register_btn.setEnabled(True)
            self.start_batch_btn.setEnabled(True)
            self.statusBar().showMessage("椰子云连续注册完成")

            # 显示完成消息
            if hasattr(self, 'continuous_worker') and self.continuous_worker:
                success_count = len([r for r in self.continuous_worker.results if r.get('success', False)])
                total_count = len(self.continuous_worker.results)

                QMessageBox.information(
                    self, "完成",
                    f"椰子云连续注册完成！\n"
                    f"成功: {success_count}/{total_count}\n"
                    f"失败: {total_count - success_count}/{total_count}"
                )

        except Exception as e:
            QMessageBox.critical(self, "错误", f"椰子云连续注册完成处理异常: {str(e)}")

    def start_yezi_multi_register(self):
        """启动椰子云多线程注册"""
        try:
            # 检查椰子云配置
            sms_username = self.sms_username_edit.text().strip()
            sms_password = self.sms_password_edit.text().strip()
            project_id = self.sms_project_id_edit.text().strip()

            if not all([sms_username, sms_password, project_id]):
                QMessageBox.warning(self, "警告", "请先配置椰子云API信息！")
                return

            # 获取注册数量和线程数
            register_count = self.register_count_spin.value()
            max_threads = self.max_threads_spin.value()

            # 确认对话框
            reply = QMessageBox.question(
                self, "确认",
                f"椰子云多线程注册将启动 {max_threads} 个线程，注册 {register_count} 个账号。\n\n"
                "功能包括：\n"
                "• 多线程并发注册\n"
                "• 自动获取椰子云手机号\n"
                "• 自动识别验证码\n"
                "• 自动填写表单\n"
                "• 自动获取短信验证码\n"
                "• 自动提交注册\n\n"
                "确认开始？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                self.execute_yezi_multi_register()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动椰子云多线程注册失败: {str(e)}")

    def execute_yezi_multi_register(self):
        """执行椰子云多线程注册"""
        try:
            # 创建椰子云API实例
            sms_username = self.sms_username_edit.text().strip()
            sms_password = self.sms_password_edit.text().strip()
            project_id = self.sms_project_id_edit.text().strip()

            sms_api = YeziCloudAPI(sms_username, sms_password)

            # 登录椰子云
            login_result = sms_api.login()
            if not login_result['success']:
                QMessageBox.critical(self, "错误", f"椰子云登录失败: {login_result['error']}")
                return

            # 检查余额
            balance_result = sms_api.get_balance()
            if not balance_result['success']:
                QMessageBox.warning(self, "警告", f"无法获取椰子云余额: {balance_result['error']}")
            else:
                # 正确从data字段中获取余额
                balance_data = balance_result.get('data', [])
                if balance_data and len(balance_data) > 0:
                    balance = float(balance_data[0].get('money', '0'))
                else:
                    balance = 0

                register_count = self.register_count_spin.value()
                print(f"[检查] 椰子云余额检查: 余额={balance}, 需要注册={register_count}")

                if balance < register_count:
                    reply = QMessageBox.question(
                        self, "余额不足",
                        f"椰子云余额: {balance}，需要注册: {register_count}\n"
                        "余额可能不足，是否继续？",
                        QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
                    )
                    if reply == QMessageBox.StandardButton.No:
                        return
                else:
                    print(f"[成功] 椰子云余额充足: {balance} >= {register_count}")

            # 生成用户数据
            register_count = self.register_count_spin.value()
            username_length = self.username_length_spin.value()
            password_length = self.password_length_spin.value()
            username_prefix = self.username_prefix_edit.text().strip()

            users_data = []
            for i in range(register_count):
                if username_prefix:
                    username = username_prefix + ''.join(random.choices(string.digits, k=username_length))
                else:
                    username = 'user' + ''.join(random.choices(string.digits, k=username_length))
                password = ''.join(random.choices(string.ascii_letters + string.digits, k=password_length))

                users_data.append({
                    'username': username,
                    'password': password,
                    'mobile': '',  # 椰子云会自动获取
                    'invite_code': '',
                    'invite_token': ''
                })

            # 获取其他配置
            delay_range = (self.delay_min_spin.value(), self.delay_max_spin.value())
            max_retries = self.max_retries_spin.value()
            max_threads = self.max_threads_spin.value()
            enable_captcha = self.enable_captcha_check.isChecked()
            captcha_mode = self.captcha_mode_combo.currentText()
            show_browser = self.show_browser_check.isChecked()

            # 创建并启动椰子云多线程工作线程
            self.yezi_worker = YeziMultiThreadRegisterWorker(
                users_data,
                delay_range,
                max_retries,
                max_threads,
                sms_api,
                project_id,
                enable_captcha,
                captcha_mode,
                self.base_url_edit.text().strip(),
                show_browser
            )

            # 连接信号
            self.yezi_worker.progress_updated.connect(self.update_progress)
            self.yezi_worker.result_ready.connect(self.handle_yezi_result)
            self.yezi_worker.finished.connect(self.on_yezi_register_finished)

            # 启动工作线程
            self.yezi_worker.start()

            # 更新UI状态
            self.yezi_multi_register_btn.setEnabled(False)
            self.yezi_register_btn.setEnabled(False)
            self.start_batch_btn.setEnabled(False)
            self.statusBar().showMessage("椰子云多线程注册进行中...")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"椰子云多线程注册异常: {str(e)}")

    def handle_yezi_result(self, result):
        """处理椰子云注册结果"""
        try:
            if result['success']:
                # 添加到结果表格
                row_position = self.results_table.rowCount()
                self.results_table.insertRow(row_position)

                self.results_table.setItem(row_position, 0, QTableWidgetItem(result['username']))
                self.results_table.setItem(row_position, 1, QTableWidgetItem(result['password']))
                self.results_table.setItem(row_position, 2, QTableWidgetItem(result.get('mobile', '未知')))
                self.results_table.setItem(row_position, 3, QTableWidgetItem("[成功] 椰子云注册成功"))

                print(f"[成功] 椰子云注册成功: {result['username']} | 密码: {result['password']} | 手机号: {result.get('mobile', '未知')}")
            else:
                # 添加失败结果到表格
                row_position = self.results_table.rowCount()
                self.results_table.insertRow(row_position)

                self.results_table.setItem(row_position, 0, QTableWidgetItem(result.get('username', '未知')))
                self.results_table.setItem(row_position, 1, QTableWidgetItem(''))
                self.results_table.setItem(row_position, 2, QTableWidgetItem(''))
                self.results_table.setItem(row_position, 3, QTableWidgetItem(f"[失败] {result.get('error', '未知错误')}"))

                print(f"[失败] 椰子云注册失败: {result.get('username', '未知')} | 错误: {result.get('error', '未知错误')}")
        except Exception as e:
            print(f"[失败] 处理椰子云注册结果异常: {e}")

    def on_yezi_register_finished(self):
        """椰子云注册完成"""
        try:
            # 恢复UI状态
            self.yezi_multi_register_btn.setEnabled(True)
            self.yezi_register_btn.setEnabled(True)
            self.start_batch_btn.setEnabled(True)
            self.statusBar().showMessage("椰子云多线程注册完成")

            # 显示完成消息
            if hasattr(self, 'yezi_worker') and self.yezi_worker:
                success_count = len([r for r in self.yezi_worker.results if r.get('success', False)])
                total_count = len(self.yezi_worker.results)

                QMessageBox.information(
                    self, "完成",
                    f"椰子云多线程注册完成！\n"
                    f"成功: {success_count}/{total_count}\n"
                    f"失败: {total_count - success_count}/{total_count}"
                )

        except Exception as e:
            QMessageBox.critical(self, "错误", f"椰子云注册完成处理异常: {str(e)}")

    def on_invite_mode_changed(self, mode: str):
        """邀请码模式变化处理"""
        if mode == "统一邀请码":
            self.invite_code_edit.setEnabled(True)
            self.invite_code_edit.setPlaceholderText("输入统一邀请码")
        elif mode == "唯一邀请码(从TXT导入)":
            self.invite_code_edit.setEnabled(False)
            self.invite_code_edit.setPlaceholderText("请导入邀请码TXT文件")
        else:  # 不使用邀请码
            self.invite_code_edit.setEnabled(False)
            self.invite_code_edit.setPlaceholderText("不使用邀请码")

    def generate_random_string(self, length: int, include_numbers: bool = True, include_letters: bool = True) -> str:
        """生成随机字符串"""
        chars = ""
        if include_letters:
            chars += string.ascii_lowercase
        if include_numbers:
            chars += string.digits

        if not chars:
            chars = string.ascii_lowercase + string.digits

        return ''.join(random.choice(chars) for _ in range(length))

    def generate_random_mobile(self, prefix: str = "138") -> str:
        """生成随机手机号"""
        if len(prefix) != 3:
            prefix = "138"

        # 生成后8位随机数字
        suffix = ''.join(random.choice(string.digits) for _ in range(8))
        return prefix + suffix

    def get_yezi_mobile_for_user(self, user_index):
        """为用户获取椰子云手机号"""
        try:
            # 获取椰子云API配置
            sms_username = self.sms_username_edit.text().strip()
            sms_password = self.sms_password_edit.text().strip()
            project_id = self.sms_project_id_edit.text().strip()

            if not all([sms_username, sms_password, project_id]):
                QMessageBox.warning(self, "警告", "椰子云API配置不完整！")
                return ""

            # 创建椰子云API实例
            sms_api = YeziCloudAPI(sms_username, sms_password)

            # 登录
            login_result = sms_api.login()
            if not login_result['success']:
                QMessageBox.critical(self, "错误", f"椰子云API登录失败: {login_result['error']}")
                return ""

            # 获取手机号
            mobile_result = sms_api.get_mobile(project_id)
            if mobile_result['success']:
                mobile = mobile_result['mobile']

                # 存储椰子云API实例和手机号，供后续使用
                if not hasattr(self, 'yezi_mobiles'):
                    self.yezi_mobiles = {}
                    self.yezi_api_instance = sms_api
                    self.yezi_project_id = project_id

                self.yezi_mobiles[user_index] = mobile

                # 更新状态栏
                self.statusBar().showMessage(f"已获取椰子云手机号 {user_index + 1}: {mobile}")

                return mobile
            else:
                QMessageBox.warning(self, "警告", f"获取椰子云手机号失败: {mobile_result.get('error', '')}")
                return ""

        except Exception as e:
            QMessageBox.critical(self, "错误", f"椰子云手机号获取异常: {str(e)}")
            return ""

    def generate_users_for_register(self):
        """根据设置的数量生成用户数据"""
        if not self.register_instance:
            QMessageBox.warning(self, "警告", "请先应用配置！")
            return

        try:
            # 获取配置
            user_count = self.register_count_spin.value()
            username_length = self.username_length_spin.value()
            password_length = self.password_length_spin.value()
            username_prefix = self.username_prefix_edit.text().strip()
            mobile_prefix = self.mobile_prefix_edit.text().strip()
            invite_code = self.invite_code_edit.text().strip()
            include_numbers = self.include_numbers_check.isChecked()
            include_letters = self.include_letters_check.isChecked()
            enable_sms = self.enable_sms_check.isChecked()

            # 如果启用椰子云短信，显示进度对话框
            progress_dialog = None
            if enable_sms:
                from PyQt6.QtWidgets import QProgressDialog
                progress_dialog = QProgressDialog("正在获取椰子云手机号...", "取消", 0, user_count, self)
                progress_dialog.setWindowTitle("椰子云手机号获取")
                progress_dialog.setWindowModality(Qt.WindowModality.WindowModal)
                progress_dialog.show()

            # 生成用户数据
            users = []
            for i in range(user_count):
                # 检查是否取消
                if progress_dialog and progress_dialog.wasCanceled():
                    break

                if progress_dialog:
                    progress_dialog.setValue(i)
                    progress_dialog.setLabelText(f"正在获取第 {i+1}/{user_count} 个椰子云手机号...")
                # 生成用户名
                if username_prefix:
                    random_part_length = max(1, username_length - len(username_prefix))
                    random_part = self.generate_random_string(random_part_length, include_numbers, include_letters)
                    username = username_prefix + random_part
                else:
                    username = self.generate_random_string(username_length, include_numbers, include_letters)

                # 生成密码
                password = self.generate_random_string(password_length, True, True)

                # 获取手机号 (根据椰子云配置决定)
                enable_sms = self.enable_sms_check.isChecked()

                if enable_sms:
                    # 启用椰子云短信时，预先获取手机号
                    mobile = self.get_yezi_mobile_for_user(i)
                elif hasattr(self, 'mobile_list') and self.mobile_list:
                    # 使用导入的手机号
                    if i < len(self.mobile_list):
                        mobile = self.mobile_list[i]
                    else:
                        # 如果导入的手机号不够，循环使用
                        mobile = self.mobile_list[i % len(self.mobile_list)]
                else:
                    # 生成随机手机号
                    mobile = self.generate_random_mobile(mobile_prefix)

                # 根据邀请码模式获取邀请码
                invite_mode = self.invite_mode_combo.currentText()
                if invite_mode == "唯一邀请码(从TXT导入)":
                    if hasattr(self, 'invite_code_list') and self.invite_code_list:
                        if i < len(self.invite_code_list):
                            user_invite_code = self.invite_code_list[i]
                        else:
                            # 如果导入的邀请码不够，循环使用
                            user_invite_code = self.invite_code_list[i % len(self.invite_code_list)]
                    else:
                        user_invite_code = ""  # 没有导入邀请码
                elif invite_mode == "统一邀请码":
                    user_invite_code = invite_code
                else:  # 不使用邀请码
                    user_invite_code = ""

                users.append({
                    'username': username,
                    'password': password,
                    'mobile': mobile,
                    'invite_code': user_invite_code
                })

            # 更新用户表格
            self.user_table.setRowCount(len(users))
            for i, user in enumerate(users):
                self.user_table.setItem(i, 0, QTableWidgetItem(user['username']))
                self.user_table.setItem(i, 1, QTableWidgetItem('*' * len(user['password'])))
                self.user_table.setItem(i, 2, QTableWidgetItem(user['mobile']))
                self.user_table.setItem(i, 3, QTableWidgetItem(user['invite_code']))

            self.users_data = users
            self.start_batch_btn.setEnabled(True)

            # 关闭进度对话框
            if progress_dialog:
                progress_dialog.setValue(user_count)
                progress_dialog.close()

            # 显示成功消息
            if enable_sms and hasattr(self, 'yezi_mobiles'):
                success_count = len(self.yezi_mobiles)
                QMessageBox.information(self, "成功",
                                      f"已生成 {len(users)} 个用户\n"
                                      f"椰子云手机号获取: {success_count}/{user_count}\n"
                                      f"可以开始注册")
            else:
                QMessageBox.information(self, "成功", f"已生成 {len(users)} 个用户，可以开始注册")

            self.statusBar().showMessage(f"已生成 {len(users)} 个用户，可以开始注册")

        except Exception as e:
            # 关闭进度对话框
            if 'progress_dialog' in locals() and progress_dialog:
                progress_dialog.close()
            QMessageBox.critical(self, "错误", f"生成用户数据失败: {str(e)}")

    def clear_yezi_mobiles(self):
        """清理椰子云手机号"""
        if hasattr(self, 'yezi_mobiles') and hasattr(self, 'yezi_api_instance'):
            try:
                # 释放所有椰子云手机号
                for user_index, mobile in self.yezi_mobiles.items():
                    try:
                        self.yezi_api_instance.release_mobile(mobile, self.yezi_project_id)
                    except:
                        pass  # 忽略释放失败

                # 清理相关属性
                delattr(self, 'yezi_mobiles')
                delattr(self, 'yezi_api_instance')
                delattr(self, 'yezi_project_id')

            except Exception as e:
                print(f"清理椰子云手机号失败: {e}")

    def clear_users_list(self):
        """清空用户列表"""
        reply = QMessageBox.question(
            self, "确认", "确认清空用户列表？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 清理椰子云手机号
            self.clear_yezi_mobiles()

            self.user_table.setRowCount(0)
            self.users_data = []
            self.start_batch_btn.setEnabled(False)
            self.statusBar().showMessage("用户列表已清空")



    def browse_csv_file(self):
        """浏览并加载CSV文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择CSV文件", "", "CSV文件 (*.csv);;所有文件 (*)"
        )
        if file_path:
            self.load_csv_file(file_path)

    def create_sample_csv(self):
        """创建示例CSV文件"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存示例CSV文件", "users_sample.csv", "CSV文件 (*.csv)"
        )
        if file_path:
            try:
                sample_data = [
                    ['username', 'password', 'mobile', 'invite_code'],
                    ['testuser001', '123456789', '13800138001', 'ABC123'],
                    ['testuser002', '123456789', '13800138002', 'ABC123'],
                    ['testuser003', '123456789', '13800138003', 'DEF456'],
                    ['testuser004', '123456789', '13800138004', ''],
                    ['testuser005', '123456789', '13800138005', 'GHI789'],
                ]

                with open(file_path, 'w', newline='', encoding='utf-8') as file:
                    writer = csv.writer(file)
                    writer.writerows(sample_data)

                # 自动加载创建的示例文件
                self.load_csv_file(file_path)
                QMessageBox.information(self, "成功", f"示例CSV文件已创建: {file_path}")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"创建示例文件失败: {str(e)}")

    def load_csv_file(self, csv_file: str = None):
        """加载CSV文件"""
        if not csv_file:
            QMessageBox.warning(self, "警告", "请选择CSV文件！")
            return

        if not os.path.exists(csv_file):
            QMessageBox.warning(self, "警告", "CSV文件不存在！")
            return

        try:
            if not self.register_instance:
                QMessageBox.warning(self, "警告", "请先应用配置！")
                return

            users = self.register_instance.load_users_from_csv(csv_file)
            if not users:
                QMessageBox.warning(self, "警告", "CSV文件中没有有效的用户数据！")
                return

            # 更新用户表格
            self.user_table.setRowCount(len(users))
            for i, user in enumerate(users):
                self.user_table.setItem(i, 0, QTableWidgetItem(user.get('username', '')))
                self.user_table.setItem(i, 1, QTableWidgetItem('*' * len(user.get('password', ''))))
                self.user_table.setItem(i, 2, QTableWidgetItem(user.get('mobile', '')))
                self.user_table.setItem(i, 3, QTableWidgetItem(user.get('invite_code', '')))

            self.users_data = users
            self.start_batch_btn.setEnabled(True)

            QMessageBox.information(self, "成功", f"已加载 {len(users)} 个用户")
            self.statusBar().showMessage(f"已加载 {len(users)} 个用户")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载CSV文件失败: {str(e)}")

    def import_mobile_txt(self):
        """导入手机号TXT文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择手机号TXT文件", "", "文本文件 (*.txt);;所有文件 (*)"
        )
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as file:
                    mobiles = [line.strip() for line in file if line.strip()]

                if not mobiles:
                    QMessageBox.warning(self, "警告", "TXT文件中没有有效的手机号！")
                    return

                # 存储手机号列表
                self.mobile_list = mobiles
                QMessageBox.information(self, "成功", f"已导入 {len(mobiles)} 个手机号")
                self.statusBar().showMessage(f"已导入 {len(mobiles)} 个手机号")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"导入手机号TXT文件失败: {str(e)}")

    def import_invite_txt(self):
        """导入邀请码TXT文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择邀请码TXT文件", "", "文本文件 (*.txt);;所有文件 (*)"
        )
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as file:
                    invite_codes = [line.strip() for line in file if line.strip()]

                if not invite_codes:
                    QMessageBox.warning(self, "警告", "TXT文件中没有有效的邀请码！")
                    return

                # 存储邀请码列表
                self.invite_code_list = invite_codes
                QMessageBox.information(self, "成功", f"已导入 {len(invite_codes)} 个邀请码")
                self.statusBar().showMessage(f"已导入 {len(invite_codes)} 个邀请码")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"导入邀请码TXT文件失败: {str(e)}")

    def start_batch_register(self):
        """开始批量注册"""
        if not hasattr(self, 'users_data') or not self.users_data:
            QMessageBox.warning(self, "警告", "请先加载CSV文件！")
            return

        if not self.register_instance:
            QMessageBox.warning(self, "警告", "请先应用配置！")
            return

        # 确认对话框
        reply = QMessageBox.question(
            self, "确认",
            f"确认开始批量注册 {len(self.users_data)} 个用户？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply != QMessageBox.StandardButton.Yes:
            return

        # 清空之前的结果
        self.results = []
        self.update_results_display()

        # 设置UI状态
        self.start_batch_btn.setEnabled(False)
        self.stop_batch_btn.setEnabled(True)
        self.progress_bar.setValue(0)
        self.progress_bar.setMaximum(len(self.users_data))

        # 获取配置
        delay_range = (self.delay_min_spin.value(), self.delay_max_spin.value())
        max_retries = self.max_retries_spin.value()
        max_threads = self.max_threads_spin.value()

        # 椰子云API配置
        sms_api = None
        enable_sms = self.enable_sms_check.isChecked()
        project_id = self.sms_project_id_edit.text().strip()

        if enable_sms:
            # 检查是否已经预先获取了椰子云手机号
            if hasattr(self, 'yezi_api_instance') and hasattr(self, 'yezi_mobiles'):
                # 使用预先创建的椰子云API实例
                sms_api = self.yezi_api_instance
                project_id = self.yezi_project_id

                # 检查余额
                balance_result = sms_api.get_balance()
                if balance_result['success']:
                    balance_data = balance_result['data'][0] if balance_result['data'] else {}
                    balance = balance_data.get('money', '0')
                    QMessageBox.information(self, "椰子云API", f"使用预获取手机号注册！当前余额: {balance} 元")
                else:
                    QMessageBox.warning(self, "警告", f"获取余额失败: {balance_result['error']}")
            else:
                # 如果没有预先获取，则提示用户先生成用户数据
                QMessageBox.warning(self, "警告", "启用椰子云短信时，请先点击'生成用户数据'预先获取手机号！")
                self.start_batch_btn.setEnabled(True)
                self.stop_batch_btn.setEnabled(False)
                return

        # 验证码配置
        enable_captcha = self.enable_captcha_check.isChecked()
        captcha_mode = self.captcha_mode_combo.currentText()

        # 创建并启动多线程工作线程
        self.worker = MultiThreadRegisterWorker(
            self.register_instance,
            self.users_data,
            delay_range,
            max_retries,
            max_threads,
            sms_api,
            enable_sms,
            project_id,
            enable_captcha,
            captcha_mode,
            self.base_url_edit.text().strip()  # 传递GUI中配置的域名
        )

        self.worker.progress_updated.connect(self.update_progress)
        self.worker.result_ready.connect(self.add_result)
        self.worker.finished_all.connect(self.batch_finished)
        self.worker.sms_code_needed.connect(self.handle_sms_code_needed)

        self.worker.start()
        self.statusBar().showMessage("批量注册进行中...")

    def stop_batch_register(self):
        """停止批量注册"""
        if self.worker and self.worker.isRunning():
            self.worker.stop()
            self.worker.wait()

        self.start_batch_btn.setEnabled(True)
        self.stop_batch_btn.setEnabled(False)
        self.statusBar().showMessage("批量注册已停止")

    def handle_sms_code_needed(self, mobile: str, project_id: str):
        """处理需要短信验证码的信号"""
        # 这里可以添加额外的处理逻辑
        # 比如显示获取验证码的状态
        self.statusBar().showMessage(f"正在为 {mobile} 获取验证码...")

    def update_progress(self, current, total, message):
        """更新进度"""
        self.progress_bar.setValue(current + 1)
        self.progress_label.setText(message)

    def add_result(self, result):
        """添加注册结果"""
        self.results.append(result)

        # 更新用户表格中的手机号（如果是椰子云获取的）
        if result.get('mobile') and hasattr(self, 'users_data'):
            for i, user in enumerate(self.users_data):
                if user['username'] == result.get('username'):
                    # 更新用户数据
                    user['mobile'] = result['mobile']
                    # 更新表格显示
                    self.user_table.setItem(i, 2, QTableWidgetItem(result['mobile']))
                    break

        self.update_results_display()

    def batch_finished(self, results):
        """批量注册完成"""
        self.results = results
        self.update_results_display()

        self.start_batch_btn.setEnabled(True)
        self.stop_batch_btn.setEnabled(False)

        # 显示完成消息
        total = len(results)
        success_count = sum(1 for r in results if r['success'])

        QMessageBox.information(
            self, "完成",
            f"批量注册完成！\n总计: {total}\n成功: {success_count}\n失败: {total - success_count}"
        )

        self.statusBar().showMessage(f"批量注册完成 - 成功: {success_count}/{total}")

    def update_results_display(self):
        """更新结果显示"""
        # 更新统计信息
        total = len(self.results)
        success_count = sum(1 for r in self.results if r['success'])
        failed_count = total - success_count
        success_rate = (success_count / total * 100) if total > 0 else 0

        self.total_label.setText(str(total))
        self.success_label.setText(str(success_count))
        self.failed_label.setText(str(failed_count))
        self.success_rate_label.setText(f"{success_rate:.1f}%")

        # 更新结果表格
        self.results_table.setRowCount(total)
        for i, result in enumerate(self.results):
            self.results_table.setItem(i, 0, QTableWidgetItem(result.get('username', '')))
            self.results_table.setItem(i, 1, QTableWidgetItem(result.get('mobile', '')))

            status_item = QTableWidgetItem("成功" if result['success'] else "失败")
            if result['success']:
                status_item.setBackground(Qt.GlobalColor.green)
            else:
                status_item.setBackground(Qt.GlobalColor.red)
            self.results_table.setItem(i, 2, status_item)

            self.results_table.setItem(i, 3, QTableWidgetItem(result.get('error', '')))
            self.results_table.setItem(i, 4, QTableWidgetItem(str(result.get('status_code', ''))))

    def export_results(self):
        """导出结果"""
        if not self.results:
            QMessageBox.warning(self, "警告", "没有结果可以导出！")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出结果", "register_results.csv", "CSV文件 (*.csv)"
        )

        if file_path:
            try:
                if self.register_instance:
                    self.register_instance.save_results_to_csv(self.results, file_path)
                    QMessageBox.information(self, "成功", f"结果已导出到: {file_path}")
                else:
                    QMessageBox.warning(self, "警告", "注册实例未初始化！")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")

    def clear_results(self):
        """清空结果"""
        reply = QMessageBox.question(
            self, "确认", "确认清空所有结果？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.results = []
            self.update_results_display()
            self.statusBar().showMessage("结果已清空")

    def load_config(self):
        """加载配置"""
        try:
            # 加载窗口配置
            self.window_config = {
                'width': config_manager.get_int('UI', 'window_width', 1200),
                'height': config_manager.get_int('UI', 'window_height', 800),
                'x': config_manager.get_int('UI', 'window_x', 100),
                'y': config_manager.get_int('UI', 'window_y', 100)
            }
        except Exception as e:
            print(f"加载配置失败: {e}")
            self.window_config = {'width': 1200, 'height': 800, 'x': 100, 'y': 100}

    def apply_config_to_ui(self):
        """应用配置到界面"""
        try:
            # 设置窗口大小和位置
            self.setGeometry(
                self.window_config['x'],
                self.window_config['y'],
                self.window_config['width'],
                self.window_config['height']
            )

            # 加载基本设置
            domain = config_manager.get('BASIC', 'domain', 'https://970faka.com')
            self.base_url_edit.setText(domain)

            username_length = config_manager.get_int('BASIC', 'username_length', 8)
            self.username_length_spin.setValue(username_length)

            password_length = config_manager.get_int('BASIC', 'password_length', 8)
            self.password_length_spin.setValue(password_length)

            username_prefix = config_manager.get('BASIC', 'username_prefix', '')
            self.username_prefix_edit.setText(username_prefix)

            mobile_prefix = config_manager.get('BASIC', 'mobile_prefix', '138')
            self.mobile_prefix_edit.setText(mobile_prefix)

            register_count = config_manager.get_int('BASIC', 'register_count', 10)
            self.register_count_spin.setValue(register_count)

            include_numbers = config_manager.get_bool('BASIC', 'include_numbers', True)
            self.include_numbers_check.setChecked(include_numbers)

            include_letters = config_manager.get_bool('BASIC', 'include_letters', True)
            self.include_letters_check.setChecked(include_letters)

            # 加载注册设置
            self.delay_min_spin.setValue(config_manager.get_float('REGISTER', 'delay_min', 1.0))
            self.delay_max_spin.setValue(config_manager.get_float('REGISTER', 'delay_max', 3.0))
            self.max_retries_spin.setValue(config_manager.get_int('REGISTER', 'max_retries', 3))
            self.max_threads_spin.setValue(config_manager.get_int('REGISTER', 'max_threads', 3))
            self.enable_captcha_check.setChecked(config_manager.get_bool('REGISTER', 'enable_captcha', True))

            captcha_mode = config_manager.get('REGISTER', 'captcha_mode', 'Selenium自动化')
            index = self.captcha_mode_combo.findText(captcha_mode)
            if index >= 0:
                self.captcha_mode_combo.setCurrentIndex(index)

            self.show_browser_check.setChecked(config_manager.get_bool('REGISTER', 'show_browser', True))

            enable_sms = config_manager.get_bool('REGISTER', 'enable_sms', False)
            self.enable_sms_check.setChecked(enable_sms)

            # 加载椰子云设置
            sms_username = config_manager.get('YEZI_CLOUD', 'username', '')
            self.sms_username_edit.setText(sms_username)

            sms_password = config_manager.get('YEZI_CLOUD', 'password', '')
            self.sms_password_edit.setText(sms_password)

            sms_project_id = config_manager.get('YEZI_CLOUD', 'project_id', '')
            self.sms_project_id_edit.setText(sms_project_id)

        except Exception as e:
            print(f"应用配置到界面失败: {e}")

    def save_config(self):
        """保存配置"""
        try:
            # 保存窗口配置
            geometry = self.geometry()
            config_manager.set('UI', 'window_width', geometry.width())
            config_manager.set('UI', 'window_height', geometry.height())
            config_manager.set('UI', 'window_x', geometry.x())
            config_manager.set('UI', 'window_y', geometry.y())

            # 保存基本设置
            domain = self.base_url_edit.text()
            config_manager.set('BASIC', 'domain', domain)

            username_length = self.username_length_spin.value()
            config_manager.set('BASIC', 'username_length', username_length)

            password_length = self.password_length_spin.value()
            config_manager.set('BASIC', 'password_length', password_length)

            username_prefix = self.username_prefix_edit.text()
            config_manager.set('BASIC', 'username_prefix', username_prefix)

            mobile_prefix = self.mobile_prefix_edit.text()
            config_manager.set('BASIC', 'mobile_prefix', mobile_prefix)

            register_count = self.register_count_spin.value()
            config_manager.set('BASIC', 'register_count', register_count)

            include_numbers = self.include_numbers_check.isChecked()
            config_manager.set('BASIC', 'include_numbers', include_numbers)

            include_letters = self.include_letters_check.isChecked()
            config_manager.set('BASIC', 'include_letters', include_letters)

            # 保存注册设置
            config_manager.set('REGISTER', 'delay_min', self.delay_min_spin.value())
            config_manager.set('REGISTER', 'delay_max', self.delay_max_spin.value())
            config_manager.set('REGISTER', 'max_retries', self.max_retries_spin.value())
            config_manager.set('REGISTER', 'max_threads', self.max_threads_spin.value())
            config_manager.set('REGISTER', 'enable_captcha', self.enable_captcha_check.isChecked())
            config_manager.set('REGISTER', 'captcha_mode', self.captcha_mode_combo.currentText())
            config_manager.set('REGISTER', 'enable_sms', self.enable_sms_check.isChecked())
            config_manager.set('REGISTER', 'show_browser', self.show_browser_check.isChecked())

            # 保存椰子云设置
            sms_username = self.sms_username_edit.text()
            config_manager.set('YEZI_CLOUD', 'username', sms_username)

            sms_password = self.sms_password_edit.text()
            config_manager.set('YEZI_CLOUD', 'password', sms_password)

            sms_project_id = self.sms_project_id_edit.text()
            config_manager.set('YEZI_CLOUD', 'project_id', sms_project_id)

            # 保存到文件
            config_manager.save_config()

        except Exception as e:
            print(f"保存配置失败: {e}")

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 保存配置
        self.save_config()

        # 停止工作线程
        if self.worker and self.worker.isRunning():
            self.worker.stop()
            self.worker.wait(3000)  # 等待3秒

        event.accept()

def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用信息
    app.setApplicationName("用户注册工具")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("Register Tool")

    # 设置样式
    app.setStyle('Fusion')

    # 创建主窗口
    window = RegisterGUI()
    window.show()

    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
