
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>卡密验证系统 - 登录</title>
        <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        <style>
            body { 
                background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
            }
            .login-card { 
                background-color: rgba(255, 255, 255, 0.9);
                border-radius: 15px;
                box-shadow: 0 15px 35px rgba(0,0,0,0.2);
                backdrop-filter: blur(10px);
                overflow: hidden;
            }
            .card-header { 
                background: linear-gradient(to right, #4a00e0, #8e2de2);
                color: white;
                padding: 25px 20px;
                text-align: center;
                border-bottom: none;
            }
            .card-body {
                padding: 40px 30px;
            }
            .btn-primary {
                background: linear-gradient(to right, #4a00e0, #8e2de2);
                border: none;
                transition: all 0.3s;
                padding: 12px;
                font-weight: 600;
                letter-spacing: 0.5px;
            }
            .btn-primary:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 20px rgba(74, 0, 224, 0.3);
            }
            .form-control {
                border-radius: 10px;
                padding: 12px 15px;
                background: rgba(255, 255, 255, 0.9);
                border: 1px solid #e0e0e0;
                font-size: 15px;
            }
            .form-control:focus {
                box-shadow: 0 0 0 3px rgba(78, 0, 224, 0.2);
                border-color: #4a00e0;
            }
            .login-icon-container {
                text-align: center;
                margin-bottom: 30px;
            }
            .login-icon {
                font-size: 48px;
                width: 100px;
                height: 100px;
                line-height: 100px;
                border-radius: 50%;
                background: linear-gradient(135deg, #4a00e0, #8e2de2);
                color: white;
                text-align: center;
                margin: 0 auto;
                box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            }
            .form-label {
                font-weight: 600;
                color: #333;
                margin-bottom: 8px;
            }
            .alert {
                border-radius: 10px;
                padding: 15px;
                margin-bottom: 25px;
            }
            .form-floating label {
                padding: 12px 15px;
            }
            .form-floating>.form-control {
                padding: 12px 15px;
                height: calc(3.5rem + 2px);
            }
            .input-group {
                margin-bottom: 20px;
            }
            .input-group-text {
                background-color: white;
                border-right: none;
                border-radius: 10px 0 0 10px;
            }
            .input-group .form-control {
                border-left: none;
                border-radius: 0 10px 10px 0;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-5">
                    <div class="text-center mb-4">
                        <h2 class="text-white fw-bold mb-1">卡密验证系统</h2>
                        <p class="text-white mb-0">管理员控制面板</p>
                    </div>
                    <div class="card login-card">
                        <div class="card-header">
                            <h3 class="mb-0">管理员登录</h3>
                        </div>
                        <div class="card-body">
                            <div class="login-icon-container">
                                <div class="login-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                            </div>
                            
                            {% with messages = get_flashed_messages(with_categories=true) %}
                                {% if messages %}
                                    {% for category, message in messages %}
                                        <div class="alert alert-{{ category if category != 'message' else 'warning' }} alert-dismissible fade show" role="alert">
                                            {{ message }}
                                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                        </div>
                                    {% endfor %}
                                {% endif %}
                            {% endwith %}
                            
                            <form method="post" action="/login">
                                <div class="input-group mb-3">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                    <input type="text" class="form-control" id="username" name="username" placeholder="用户名" required autofocus>
                                </div>
                                <div class="input-group mb-4">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input type="password" class="form-control" id="password" name="password" placeholder="密码" required>
                                </div>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-sign-in-alt me-2"></i>登录
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="text-center mt-4">
                        <p class="text-white">© 2023 卡密验证系统 - 版权所有</p>
                    </div>
                </div>
            </div>
        </div>
        
        <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    