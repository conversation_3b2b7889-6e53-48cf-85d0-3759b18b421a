<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单流水记录</title>
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <style>
        /* 基础样式 */
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #f5f7fa;
        }

        .container {
            padding: 15px;
        }

        .search-box {
            margin-bottom: 20px;
        }

        .pagination-container {
            margin-top: 20px;
            display: flex;
            justify-content: flex-end;
        }

        .order-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .order-info-item {
            display: flex;
            align-items: center;
            white-space: nowrap;
        }

        /* 添加响应式样式 */
        @media screen and (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .search-box .el-row {
                margin-bottom: -15px;
            }

            .search-box .el-col {
                margin-bottom: 15px;
            }

            .order-info {
                flex-wrap: wrap;
                gap: 10px;
            }

            .order-info-item {
                font-size: 13px;
            }

            .el-table {
                font-size: 13px;
            }

            .pagination-container {
                justify-content: center;
            }

            .el-pagination {
                padding: 0;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <el-card>
                <!-- 密码验证对话框 -->
                <el-dialog
                    v-model="dialogVisible"
                    title="密码验证"
                    width="30%"
                    :close-on-click-modal="false"
                >
                    <el-form :model="passwordForm">
                        <el-form-item label="访问密码">
                            <el-input
                                v-model="passwordForm.password"
                                type="password"
                                placeholder="请输入访问密码"
                                @keyup.enter="verifyPassword"
                            />
                        </el-form-item>
                    </el-form>
                    <template #footer>
                        <span class="dialog-footer">
                            <el-button @click="dialogVisible = false">取 消</el-button>
                            <el-button type="primary" @click="verifyPassword">确 定</el-button>
                        </span>
                    </template>
                </el-dialog>

                <!-- 搜索框和日期选择 -->
                <div class="search-box">
                    <el-row :gutter="20">
                        <el-col :xs="24" :sm="8">
                            <el-input
                                v-model="search"
                                placeholder="搜索交易号"
                                @keyup.enter="handleSearch"
                            >
                                <template #append>
                                    <el-button @click="handleSearch">搜索</el-button>
                                </template>
                            </el-input>
                        </el-col>
                        <el-col :xs="24" :sm="12">
                            <el-date-picker
                                v-model="dateRange"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                @change="handleSearch"
                                style="width: 100%"
                            />
                        </el-col>
                    </el-row>
                </div>

                <!-- 数据表格 -->
                <el-table
                    :data="tableData"
                    style="width: 100%"
                    v-loading="loading"
                    @sort-change="handleSortChange"
                >
                    <el-table-column label="订单信息" min-width="300">
                        <template #default="{ row }">
                            <div class="order-info">
                                <div class="order-info-item">
                                    <span style="color: #909399; margin-right: 4px;">交易号：</span>
                                    <el-tooltip
                                        v-if="row.trade_no.includes('*')"
                                        content="已开启交易号脱敏显示"
                                        placement="top"
                                    >
                                        <span style="color: #303133; font-weight: 500;">{{ row.trade_no }}</span>
                                    </el-tooltip>
                                    <span v-else style="color: #303133; font-weight: 500;">{{ row.trade_no }}</span>
                                </div>
                                <div class="order-info-item">
                                    <span style="color: #909399; margin-right: 4px;">成功时间：</span>
                                    <span style="color: #303133;">{{ row.success_time }}</span>
                                </div>
                                <div class="order-info-item">
                                    <span style="color: #909399; margin-right: 4px;">交易金额：</span>
                                    <span style="color: #409EFF; font-weight: 500;">{{ row.total_amount }} 元</span>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页 -->
                <div class="pagination-container">
                    <el-pagination
                        v-model:current-page="currentPage"
                        v-model:page-size="pageSize"
                        :page-sizes="[10, 20, 50, 100]"
                        :total="total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                    />
                </div>
            </el-card>
        </div>
    </div>

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>

    <script>
        const { ref, onMounted } = Vue;
        
        const app = Vue.createApp({
            setup() {
                const dialogVisible = ref(false);
                const passwordForm = ref({ password: '' });
                const tableData = ref([]);
                const loading = ref(false);
                const currentPage = ref(1);
                const pageSize = ref(10);
                const total = ref(0);
                const search = ref('');
                const dateRange = ref('');
                const sortBy = ref('success_time');
                const sortOrder = ref('descending');

                const checkVerified = async () => {
                    try {
                        const res = await axios.post('/plugin/Orderdisplay/user/checkVerified');
                        if (res.data.code === 401) {
                            dialogVisible.value = true;
                        } else if (res.data.code === 200) {
                            fetchData();
                        } else {
                            ElMessage.error(res.data.msg);
                        }
                    } catch (error) {
                        ElMessage.error('验证失败：' + error.message);
                    }
                };

                const verifyPassword = async () => {
                    try {
                        const res = await axios.post('/plugin/Orderdisplay/user/verifyPassword', passwordForm.value);
                        if (res.data.code === 200) {
                            dialogVisible.value = false;
                            fetchData();
                        } else {
                            ElMessage.error(res.data.msg);
                        }
                    } catch (error) {
                        ElMessage.error('验证失败：' + error.message);
                    }
                };

                const fetchData = async () => {
                    loading.value = true;
                    try {
                        const params = {
                            page: currentPage.value,
                            limit: pageSize.value,
                            search: search.value,
                            sort: sortBy.value,
                            order: sortOrder.value,
                            dateRange: dateRange.value
                        };
                        
                        const res = await axios.post('/plugin/Orderdisplay/user/getOrderList', params);
                        if (res.data.code === 200) {
                            tableData.value = res.data.data;
                            total.value = res.data.total;
                        } else {
                            ElMessage.error(res.data.msg);
                        }
                    } catch (error) {
                        ElMessage.error('获取数据失败：' + error.message);
                    } finally {
                        loading.value = false;
                    }
                };

                const handleSearch = () => {
                    currentPage.value = 1;
                    fetchData();
                };

                const handleSortChange = (val) => {
                    sortBy.value = val.prop || 'success_time';
                    sortOrder.value = val.order || 'descending';
                    fetchData();
                };

                const handleSizeChange = (val) => {
                    pageSize.value = val;
                    fetchData();
                };

                const handleCurrentChange = (val) => {
                    currentPage.value = val;
                    fetchData();
                };

                onMounted(() => {
                    checkVerified();
                });

                return {
                    dialogVisible,
                    passwordForm,
                    tableData,
                    loading,
                    currentPage,
                    pageSize,
                    total,
                    search,
                    dateRange,
                    verifyPassword,
                    handleSearch,
                    handleSortChange,
                    handleSizeChange,
                    handleCurrentChange
                };
            }
        });

        app.use(ElementPlus, {
            locale: ElementPlusLocaleZhCn
        });
        
        app.mount('#app');
    </script>
</body>
</html> 