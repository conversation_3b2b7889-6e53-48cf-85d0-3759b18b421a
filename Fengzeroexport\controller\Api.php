<?php

namespace plugin\Fengzeroexport\controller;

use app\common\controller\BasePlugin;
use think\facade\Db;
use think\facade\View;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class Api extends BasePlugin
{
    protected $scene = ['admin'];
    protected $noNeedLogin = [];

    public function index()
    {
        return View::fetch();
    }

    public function export()
    {
        $userId = $this->request->post('user_id', '');
        $parentId = $this->request->post('parent_id', '');
        $dateRange = $this->request->post('date_range', '');
        $keyword = $this->request->post('keyword', '');
        
        if (empty($userId)) {
            return json(['code' => 0, 'msg' => '请输入用户ID']);
        }
        
        try {
            $query = Db::name('order');
            
            if (!empty($userId)) {
                $query->where('user_id', $userId);
            }
            
            // 添加关键词搜索
            if (!empty($keyword)) {
                $query->where('goods_name', 'like', "%{$keyword}%");
            }
            
            // 如果指定了上级ID，只查询与该上级相关的订单
            if (!empty($parentId)) {
                $agentRelation = Db::name('agent_user')
                    ->where('user_id', $userId)
                    ->where('agent_id', $parentId)
                    ->find();
                    
                if (!$agentRelation) {
                    return json(['code' => 0, 'msg' => '未找到与该上级的代理关系']);
                }
                
                $query->where('create_time', '>=', $agentRelation['create_time']);
                $query->where('parent_id', $parentId);
            }
            
            // 只查询已支付的订单
            $query->where('status', 1);
            
            // 添加时间范围查询条件
            if (!empty($dateRange)) {
                list($start, $end) = explode(' - ', $dateRange);
                $startTime = strtotime($start);
                $endTime = strtotime($end) + 86399; // 23:59:59
                $query->whereBetween('create_time', [$startTime, $endTime]);
            }
            
            // 获取数据
            $data = $query->field([
                'id',
                'trade_no',
                'goods_name', 
                'create_time',
                'goods_price',
                'total_amount',
                'fee',
                'status'
            ])->order('create_time', 'desc')->select();

            if (empty($data)) {
                return json(['code' => 0, 'msg' => '没有查询到数据']);
            }

            // 获取卡密信息
            $orderIds = array_column($data->toArray(), 'id');
            $cardLogs = Db::name('order_card_log')
                ->whereIn('order_id', $orderIds)
                ->column('cards', 'order_id');

            // 创建Excel对象
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();

            // 设置表头
            $sheet->setCellValue('A1', '订单号');
            $sheet->setCellValue('B1', '商品名称');
            $sheet->setCellValue('C1', '卡密信息');
            $sheet->setCellValue('D1', '购买时间');
            $sheet->setCellValue('E1', '商品金额');
            $sheet->setCellValue('F1', '实付金额');
            $sheet->setCellValue('G1', '手续费');
            $sheet->setCellValue('H1', '状态');

            // 写入数据
            $row = 2;
            foreach ($data as $item) {
                // 获取卡密信息
                $cards = isset($cardLogs[$item['id']]) ? json_decode($cardLogs[$item['id']], true) : [];
                $cardInfo = !empty($cards) ? implode(',', $cards) : '无卡密信息';

                $sheet->setCellValue('A' . $row, $item['trade_no']);
                $sheet->setCellValue('B' . $row, $item['goods_name']); // 完整显示商品名
                $sheet->setCellValue('C' . $row, $cardInfo);
                $sheet->setCellValue('D' . $row, date('Y-m-d H:i:s', $item['create_time']));
                $sheet->setCellValue('E' . $row, $item['goods_price']);
                $sheet->setCellValue('F' . $row, $item['total_amount']);
                $sheet->setCellValue('G' . $row, $item['fee']);
                $sheet->setCellValue('H' . $row, '已售出');
                $row++;
            }

            // 设置列宽
            $sheet->getColumnDimension('A')->setWidth(20);
            $sheet->getColumnDimension('B')->setWidth(40);
            $sheet->getColumnDimension('C')->setWidth(30);
            $sheet->getColumnDimension('D')->setWidth(20);
            $sheet->getColumnDimension('E')->setWidth(15);
            $sheet->getColumnDimension('F')->setWidth(15);
            $sheet->getColumnDimension('G')->setWidth(15);
            $sheet->getColumnDimension('H')->setWidth(15);

            // 设置表头样式
            $sheet->getStyle('A1:H1')->getFont()->setBold(true);
            $sheet->getStyle('A1:H1')->getFill()
                ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                ->getStartColor()->setRGB('CCCCCC');

            // 修改输出部分
            ob_end_clean(); // 清除缓冲区
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="orders_' . date('YmdHis') . '.xlsx"');
            header('Cache-Control: max-age=0');
            header('Cache-Control: max-age=1');
            header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
            header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
            header('Cache-Control: cache, must-revalidate');
            header('Pragma: public');

            $writer = new Xlsx($spreadsheet);
            $writer->save('php://output');
            exit;

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '导出失败：' . $e->getMessage()]);
        }
    }

    // 查询订单数量
    public function query()
    {
        $userId = $this->request->post('user_id', '');
        $parentId = $this->request->post('parent_id', '');
        $dateRange = $this->request->post('date_range', '');
        $keyword = $this->request->post('keyword', '');
        
        if (empty($userId)) {
            return json(['code' => 0, 'msg' => '请输入用户ID']);
        }
        
        try {
            $query = Db::name('order');
            
            if (!empty($userId)) {
                $query->where('user_id', $userId);
            }
            
            // 添加关键词搜索
            if (!empty($keyword)) {
                $query->where('goods_name', 'like', "%{$keyword}%");
            }
            
            // 如果指定了上级ID，只查询与该上级相关的订单
            if (!empty($parentId)) {
                // 查询该用户与上级的代理关系
                $agentRelation = Db::name('agent_user')
                    ->where('user_id', $userId)
                    ->where('agent_id', $parentId)
                    ->find();
                    
                if (!$agentRelation) {
                    return json(['code' => 0, 'msg' => '未找到与该上级的代理关系']);
                }
                
                // 只查询代理关系建立后的订单
                $query->where('create_time', '>=', $agentRelation['create_time']);
                $query->where('parent_id', $parentId);
            }
            
            // 只统计已支付的订单
            $query->where('status', 1);
            
            if (!empty($dateRange)) {
                list($start, $end) = explode(' - ', $dateRange);
                $startTime = strtotime($start);
                $endTime = strtotime($end) + 86399; // 23:59:59
                $query->whereBetween('create_time', [$startTime, $endTime]);
            }

            // 使用单次查询获取所有金额数据
            $orders = $query->field([
                'count(*) as count',
                'sum(goods_price) as goods_amount',
                'sum(case when fee_payer = 0 then total_amount else 0 end) as no_fee_amount',
                'sum(case when fee_payer = 1 then total_amount else 0 end) as fee_amount',
                'sum(case 
                    when fee_payer = 0 then total_amount - fee 
                    when fee_payer = 1 then total_amount - fee
                    else 0 
                end) as merchant_amount'
            ])->find();

            if ($orders['count'] > 0) {
                return json([
                    'code' => 200, 
                    'msg' => '查询成功', 
                    'data' => [
                        'count' => $orders['count'],
                        'goodsAmount' => number_format($orders['goods_amount'], 2, '.', ''),
                        'actualAmount' => number_format($orders['no_fee_amount'] + $orders['fee_amount'], 2, '.', ''),
                        'merchantAmount' => number_format($orders['merchant_amount'], 2, '.', ''),
                        'hasFee' => $orders['fee_amount'] > 0 ? '(含手续费)' : ''
                    ]
                ]);
            } else {
                return json(['code' => 0, 'msg' => '没有查询到数据']);
            }

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '查询失败：' . $e->getMessage()]);
        }
    }
}