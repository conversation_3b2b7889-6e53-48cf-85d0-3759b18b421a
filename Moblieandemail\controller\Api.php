<?php
namespace plugin\Moblieandemail\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;
use think\facade\Request;
use think\response\Json;

class Api extends BasePlugin
{
    protected $scene = ['admin']; // 仅管理员可访问

    protected $noNeedLogin = ['index']; // index方法无需鉴权

    // 显示 API 页面
    public function index()
    {
       return View::fetch();
    }

    /**
     * 获取用户表名（模仿Clearallinone风格）
     */
    protected function getUserTableName(): ?string
    {
        $tables = Db::query('SHOW TABLES');
        $prefix = config('database.connections.mysql.prefix');

        foreach ($tables as $table) {
            $tableName = current($table);
            $realTableName = str_replace($prefix, '', $tableName);

            // 直接查找user表
            if ($realTableName === 'user') {
                return $realTableName;
            }
        }

        // 如果没找到user表，查找包含user但不包含email的表
        foreach ($tables as $table) {
            $tableName = current($table);
            $realTableName = str_replace($prefix, '', $tableName);

            if (strpos($realTableName, 'user') !== false &&
                strpos($realTableName, 'email') === false &&
                strpos($realTableName, 'log') === false &&
                strpos($realTableName, 'message') === false) {
                return $realTableName;
            }
        }

        return null;
    }

    /**
     * 获取用户邮箱推送表名（模仿Clearallinone风格）
     */
    protected function getUserEmailTableName(): ?string
    {
        $tables = Db::query('SHOW TABLES');
        $prefix = config('database.connections.mysql.prefix');

        foreach ($tables as $table) {
            $tableName = current($table);
            $realTableName = str_replace($prefix, '', $tableName);

            // 直接查找user_email_push表
            if ($realTableName === 'user_email_push') {
                return $realTableName;
            }
        }

        // 如果没找到确切的表名，查找包含这些关键字的表
        foreach ($tables as $table) {
            $tableName = current($table);
            $realTableName = str_replace($prefix, '', $tableName);

            if (strpos($realTableName, 'user_email_push') !== false) {
                return $realTableName;
            }
        }

        return null;
    }



    /**
     * 导出手机号数据（模仿Clearallinone风格）
     */
    public function exportMobile(): Json
    {
        try {
            // 使用Clearallinone风格的表名获取
            $tableName = $this->getUserTableName();

            if (!$tableName) {
                return json([
                    'code' => 404,
                    'msg' => '未找到用户表'
                ]);
            }

            // 获取表的字段信息
            $fields = Db::name($tableName)->getTableFields();

            // 查找手机号字段
            $mobileFields = ['mobile', 'phone', 'tel', 'telephone', 'mobile_phone'];
            $foundField = null;

            foreach ($mobileFields as $field) {
                if (in_array($field, $fields)) {
                    $foundField = $field;
                    break;
                }
            }

            if (!$foundField) {
                return json([
                    'code' => 404,
                    'msg' => "表 {$tableName} 中未找到手机号字段"
                ]);
            }

            // 获取所有非空的手机号
            $mobileData = Db::name($tableName)
                ->where($foundField, '<>', '')
                ->whereNotNull($foundField)
                ->field($foundField)
                ->select()
                ->toArray();

            if (empty($mobileData)) {
                return json([
                    'code' => 204,
                    'msg' => "表 {$tableName} 中未找到手机号数据"
                ]);
            }

            // 提取手机号并去重
            $mobiles = array_unique(array_column($mobileData, $foundField));
            // 过滤掉空值和无效值
            $mobiles = array_filter($mobiles, function($mobile) {
                return !empty(trim($mobile)) && strlen(trim($mobile)) >= 10;
            });

            return json([
                'code' => 200,
                'msg' => '手机号导出成功',
                'data' => array_values($mobiles),
                'count' => count($mobiles)
            ]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '导出失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 导出邮箱数据（模仿Clearallinone风格）
     */
    public function exportEmail(): Json
    {
        try {
            // 使用Clearallinone风格的表名获取
            $tableName = $this->getUserEmailTableName();

            if (!$tableName) {
                // 如果没找到专门的邮箱表，尝试从用户表中查找邮箱字段
                $userTableName = $this->getUserTableName();
                if ($userTableName) {
                    $fields = Db::name($userTableName)->getTableFields();
                    $emailFields = ['email', 'mail', 'email_address'];

                    foreach ($emailFields as $field) {
                        if (in_array($field, $fields)) {
                            return $this->exportEmailFromTable($userTableName, $field);
                        }
                    }
                }

                return json([
                    'code' => 404,
                    'msg' => '未找到邮箱相关表'
                ]);
            }

            return $this->exportEmailFromTable($tableName, 'email');

        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '导出失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 从指定表导出邮箱数据的辅助方法（模仿Clearallinone风格）
     */
    private function exportEmailFromTable($tableName, $fieldName): Json
    {
        try {
            // 获取表的字段信息
            $fields = Db::name($tableName)->getTableFields();

            if (!in_array($fieldName, $fields)) {
                return json([
                    'code' => 404,
                    'msg' => "表 {$tableName} 中未找到邮箱字段"
                ]);
            }

            // 获取所有非空的邮箱
            $emailData = Db::name($tableName)
                ->where($fieldName, '<>', '')
                ->whereNotNull($fieldName)
                ->field($fieldName)
                ->select()
                ->toArray();

            if (empty($emailData)) {
                return json([
                    'code' => 204,
                    'msg' => "表 {$tableName} 中未找到邮箱数据"
                ]);
            }

            // 提取邮箱并去重
            $emails = array_unique(array_column($emailData, $fieldName));
            // 过滤掉空值和无效邮箱
            $emails = array_filter($emails, function($email) {
                return !empty(trim($email)) && filter_var(trim($email), FILTER_VALIDATE_EMAIL);
            });

            return json([
                'code' => 200,
                'msg' => '邮箱导出成功',
                'data' => array_values($emails),
                'count' => count($emails)
            ]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '导出失败: ' . $e->getMessage()]);
        }
    }



    /**
     * 导出所有数据（手机号和邮箱）（模仿Clearallinone风格）
     */
    public function exportAll(): Json
    {
        try {
            $result = [
                'mobiles' => [],
                'emails' => [],
                'mobile_count' => 0,
                'email_count' => 0
            ];

            // 获取手机号数据
            $userTableName = $this->getUserTableName();
            if ($userTableName) {
                try {
                    $fields = Db::name($userTableName)->getTableFields();
                    $mobileFields = ['mobile', 'phone', 'tel', 'telephone', 'mobile_phone'];
                    $foundMobileField = null;

                    foreach ($mobileFields as $field) {
                        if (in_array($field, $fields)) {
                            $foundMobileField = $field;
                            break;
                        }
                    }

                    if ($foundMobileField) {
                        $mobileData = Db::name($userTableName)
                            ->where($foundMobileField, '<>', '')
                            ->whereNotNull($foundMobileField)
                            ->field($foundMobileField)
                            ->select()
                            ->toArray();

                        if (!empty($mobileData)) {
                            $mobiles = array_unique(array_column($mobileData, $foundMobileField));
                            $mobiles = array_filter($mobiles, function($mobile) {
                                return !empty(trim($mobile)) && strlen(trim($mobile)) >= 10;
                            });
                            $result['mobiles'] = array_values($mobiles);
                            $result['mobile_count'] = count($result['mobiles']);
                        }
                    }
                } catch (\Exception $e) {
                    // 忽略错误，继续处理邮箱
                }
            }

            // 获取邮箱数据
            $emailTableName = $this->getUserEmailTableName();
            if ($emailTableName) {
                try {
                    $fields = Db::name($emailTableName)->getTableFields();
                    $emailFields = ['email', 'mail', 'email_address'];
                    $foundEmailField = null;

                    foreach ($emailFields as $field) {
                        if (in_array($field, $fields)) {
                            $foundEmailField = $field;
                            break;
                        }
                    }

                    if ($foundEmailField) {
                        $emailData = Db::name($emailTableName)
                            ->where($foundEmailField, '<>', '')
                            ->whereNotNull($foundEmailField)
                            ->field($foundEmailField)
                            ->select()
                            ->toArray();

                        if (!empty($emailData)) {
                            $emails = array_unique(array_column($emailData, $foundEmailField));
                            $emails = array_filter($emails, function($email) {
                                return !empty(trim($email)) && filter_var(trim($email), FILTER_VALIDATE_EMAIL);
                            });
                            $result['emails'] = array_values($emails);
                            $result['email_count'] = count($result['emails']);
                        }
                    }
                } catch (\Exception $e) {
                    // 忽略错误
                }
            }

            if ($result['mobile_count'] === 0 && $result['email_count'] === 0) {
                return json(['code' => 204, 'msg' => '未找到任何数据']);
            }

            return json([
                'code' => 200,
                'msg' => '数据导出成功',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '导出失败: ' . $e->getMessage()]);
        }
    }
}
