(function() {
    // 定义菜单数据
    const menuItems = [
        {
            text: "风控管理",
            path: "/plugin/Bannedusers/Api/riskControl"  // 默认使用风控模板路径
        },
        {
            text: "投诉管理",
            path: "/plugin/Bannedusers/Api/complaints"
        },
        {
            text: "封禁记录",
            path: "/plugin/Bannedusers/Api/bannedRecords"
        }
    ];

    // 创建菜单HTML
    function createMenu() {
        // 检查是否已存在菜单
        if (document.querySelector('.ban-select-list')) {
            return;
        }

        // 获取当前模板类型
        fetch('/plugin/Bannedusers/Api/getTemplate')
            .then(response => response.json())
            .then(data => {
                if (data.code === 1) {
                    // 根据模板类型更新风控管理路径
                    menuItems[0].path = data.type === 'risk' 
                        ? '/plugin/Bannedusers/Api/riskControl'
                        : '/plugin/Bannedusers/Api/index';
                    
                    // 创建菜单容器
                    const container = document.createElement('div');
                    container.className = 'ban-select-list';
                    
                    // 添加收缩箭头
                    const arrow = document.createElement('div');
                    arrow.className = 'ban-arrow expanded';
                    arrow.innerHTML = '→';  // 默认展开时显示向右箭头
                    container.appendChild(arrow);

                    // 创建菜单内容容器
                    const menuContent = document.createElement('div');
                    menuContent.className = 'ban-menu-content';
                    container.appendChild(menuContent);

                    menuItems.forEach((item, index) => {
                        const div = document.createElement('div');
                        div.className = 'ban-item';
                        if (index === 0) div.classList.add('ban-active');
                        
                        div.textContent = item.text;
                        
                        div.addEventListener('click', () => {
                            menuContent.querySelectorAll('.ban-item').forEach(el => {
                                el.classList.remove('ban-active');
                            });
                            div.classList.add('ban-active');
                            window.location.href = item.path;
                        });

                        menuContent.appendChild(div);
                    });

                    // 添加箭头点击事件
                    arrow.addEventListener('click', () => {
                        const isExpanded = arrow.classList.contains('expanded');
                        if (isExpanded) {
                            container.classList.add('collapsed');
                            arrow.classList.remove('expanded');
                            arrow.innerHTML = '←';  // 收起时显示向左箭头
                        } else {
                            container.classList.remove('collapsed');
                            arrow.classList.add('expanded');
                            arrow.innerHTML = '→';  // 展开时显示向右箭头
                        }
                    });

                    document.body.appendChild(container);

                    // 添加样式
                    const style = document.createElement('style');
                    style.textContent = `
                        .ban-select-list {
                            display: flex;
                            gap: 10px;
                            padding: 12px;
                            background-color: #fff;
                            border-radius: 8px;
                            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
                            position: fixed;
                            top: 40%;
                            right: 20px;
                            transform: translateY(-50%);
                            z-index: 1000;
                            transition: all 0.3s ease;
                        }

                        .ban-arrow {
                            cursor: pointer;
                            user-select: none;
                            padding: 8px 4px;
                            color: #666;
                            transition: all 0.3s ease;
                        }

                        .ban-arrow:hover {
                            color: #4a90e2;
                        }

                        .ban-menu-content {
                            display: flex;
                            flex-direction: column;
                            gap: 10px;
                            width: 80px;
                        }

                        .ban-select-list.collapsed .ban-menu-content {
                            display: none;
                        }

                        .ban-select-list.collapsed {
                            padding: 12px 4px;
                        }

                        .ban-select-list .ban-item {
                            padding: 8px 16px;
                            border-radius: 4px;
                            font-size: 14px;
                            color: #666;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            user-select: none;
                            text-align: center;
                            width: 100%;          /* 占满容器宽度 */
                        }

                        .ban-select-list .ban-item:hover {
                            background-color: #f0f7ff;
                            color: #4a90e2;
                        }

                        .ban-select-list .ban-item.ban-active {
                            background-color: #4a90e2;
                            color: #fff;
                        }

                        /* 移动端适配 */
                        @media screen and (max-width: 768px) {
                            .ban-select-list {
                                right: 10px;
                            }
                            
                            .ban-menu-content {
                                width: 60px;
                            }

                            .ban-select-list.collapsed {
                                padding: 8px 4px;
                            }
                        }
                    `;
                    document.head.appendChild(style);
                }
            })
            .catch(error => console.error('获取模板类型失败:', error));
    }

    // 确保页面完全加载后再显示菜单
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', createMenu);
    } else {
        createMenu();
    }
})();
