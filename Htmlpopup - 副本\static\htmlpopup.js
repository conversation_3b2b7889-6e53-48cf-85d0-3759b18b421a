(function () {
    // 获取店铺信息
    function getShopInfo() {
        let shopName = '';
        let merchantId = '';
        
        // 1. 从 nickname 元素获取商家名称（优先级最高）
        const nicknameElement = document.querySelector('.nickname');
        if (nicknameElement) {
            shopName = nicknameElement.textContent.trim();
        }

        // 2. 从带有店铺名称class的元素获取
        if (!shopName) {
            const shopNameElements = document.querySelectorAll('.shop-name, .store-name, .merchant-name');
            for (const element of shopNameElements) {
                const text = element.textContent.trim();
                if (text) {
                    shopName = text;
                    break;
                }
            }
        }

        // 3. 从页面标题获取商家名称
        if (!shopName) {
            const pageTitle = document.title.trim();
            if (pageTitle) {
                const index = pageTitle.indexOf('的小店');
                if (index !== -1) {
                    shopName = pageTitle.substring(0, index);
                }
            }
        }

        // 4. 从商家ID属性元素获取
        if (!merchantId) {
            const merchantIdElement = document.querySelector('[data-merchant-id]');
            if (merchantIdElement) {
                merchantId = merchantIdElement.getAttribute('data-merchant-id');
            }
        }

        // 5. 从页面元素中获取商家信息
        if (!shopName || !merchantId) {
            const shopInfoScript = document.querySelector('script[data-shop-info]');
            if (shopInfoScript) {
                try {
                    const shopInfo = JSON.parse(shopInfoScript.textContent);
                    if (!shopName && shopInfo.shopName) {
                        shopName = shopInfo.shopName;
                    }
                    if (!merchantId && shopInfo.merchantId) {
                        merchantId = shopInfo.merchantId;
                    }
                } catch (e) {
                    // 解析商家信息失败，忽略错误
                }
            }
        }
        
        // 6. 从 meta 标签获取
        if (!shopName) {
            const metaTags = [
                document.querySelector('meta[name="shop-name"]'),
                document.querySelector('meta[property="og:site_name"]'),
                document.querySelector('meta[name="author"]')
            ];
            
            for (const tag of metaTags) {
                if (tag && tag.getAttribute('content')) {
                    shopName = tag.getAttribute('content');
                    break;
                }
            }
        }
        
        if (!merchantId) {
            const merchantIdMeta = document.querySelector('meta[name="merchant-id"]');
            if (merchantIdMeta) {
                merchantId = merchantIdMeta.getAttribute('content');
            }
        }
        
        // 7. 从 URL 参数获取（备用方式）
        if (!shopName || !merchantId) {
            const urlParams = new URLSearchParams(window.location.search);
            if (!shopName) {
                const urlShopName = urlParams.get('shop_name');
                if (urlShopName) shopName = urlShopName;
            }
            if (!merchantId) {
                const urlMerchantId = urlParams.get('merchant_id');
                if (urlMerchantId) merchantId = urlMerchantId;
            }
        }

        // 8. 从URL路径中提取可能的店铺名或ID
        if (!shopName || !merchantId) {
            const pathParts = window.location.pathname.split('/');
            for (const part of pathParts) {
                // 如果路径部分看起来像店铺名（包含字母或非ASCII字符）
                if (/[a-zA-Z\u00C0-\u00FF\u0100-\uFFFF]/.test(part) && part.length > 1 && !shopName) {
                    shopName = decodeURIComponent(part);
                }
                // 如果路径部分看起来像ID（纯数字）
                else if (/^\d+$/.test(part) && part.length > 0 && !merchantId) {
                    merchantId = part;
                }
            }
        }

        return { shopName, merchantId };
    }

    // 创建HTML弹窗
    function createHtmlPopup(config) {
        if (!config.content) return;

        // 检查是否已存在弹窗
        if (document.querySelector('.html-popup-modal')) {
            return;
        }

        const modal = document.createElement('div');
        modal.className = 'html-popup-modal';

        const closeButton = `
            <button class="popup-close-btn" title="关闭">×</button>
        `;

        modal.innerHTML = `
            <div class="html-popup-content">
                <div class="html-popup-header">
                    <h3>${config.title}</h3>
                    ${closeButton}
                </div>
                <div class="html-popup-body">
                    ${config.content}
                </div>
                <div class="html-popup-footer">
                    <button class="popup-btn popup-btn-reject">拒绝</button>
                    <button class="popup-btn popup-btn-accept">同意</button>
                </div>
            </div>
        `;

        // 添加样式
        const style = document.createElement('style');
        style.textContent = getPopupStyles();
        document.head.appendChild(style);

        // 关闭弹窗函数
        const closeModal = (action = 'close') => {
            modal.classList.remove('show');
            setTimeout(() => {
                modal.remove();
                style.remove();
            }, 300);
        };

        // 绑定关闭事件
        modal.querySelector('.popup-close-btn').onclick = () => closeModal('close');

        // 绑定同意按钮事件
        modal.querySelector('.popup-btn-accept').onclick = () => {
            closeModal('accept');
        };

        // 绑定拒绝按钮事件
        modal.querySelector('.popup-btn-reject').onclick = () => {
            closeModal('reject');

            // 根据配置执行拒绝后的操作
            const rejectAction = config.reject_action || 'none';


            switch (rejectAction) {
                case 'redirect':
                    // 跳转到指定页面
                    const rejectUrl = config.reject_url;
                    if (rejectUrl) {

                        window.location.href = rejectUrl;
                    }
                    break;

                case 'close':
                    // 关闭当前网页
                    const closeConfirm = config.close_confirm;
                    if (closeConfirm) {
                        // 显示确认对话框
                        if (confirm('确定要关闭当前网页吗？')) {
                            window.close();
                        }
                    } else {
                        // 直接关闭
                        window.close();
                    }
                    break;

                case 'none':
                default:
                    // 无操作，只关闭弹窗

                    break;
            }
        };

        modal.onclick = (e) => {
            if (e.target === modal) {
                closeModal('mask');
            }
        };

        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                closeModal('escape');
            }
        });

        // 添加到页面并显示
        document.body.appendChild(modal);
        // 强制重绘
        modal.offsetHeight;
        modal.classList.add('show');
    }

    // 获取弹窗样式
    function getPopupStyles() {
        return `
            /* 主容器样式 */
            .html-popup-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.65);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 99999;
                opacity: 0;
                transition: all 0.35s ease;
                padding: 20px;
            }

            /* 内容框 */
            .html-popup-content {
                background: white;
                border-radius: 12px;
                width: 360px;
                height: 500px;
                max-width: 90vw;
                max-height: 90vh;
                transform: scale(0.9);
                transition: all 0.35s ease;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
                overflow: hidden;
                display: flex;
                flex-direction: column;
            }

            /* 弹窗头部 */
            .html-popup-header {
                padding: 12px 15px;
                background: #f9fafb;
                border-bottom: 1px solid #edf2f7;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            /* 弹窗标题 */
            .html-popup-header h3 {
                margin: 0;
                font-size: 17px;
                color: #374151;
                font-weight: 600;
            }

            /* 关闭按钮 */
            .popup-close-btn {
                background: none;
                border: none;
                width: 32px;
                height: 32px;
                border-radius: 50%;
                cursor: pointer;
                color: #6b7280;
                font-size: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .popup-close-btn:hover {
                background: #f3f4f6;
                color: #1f2937;
            }

            /* 弹窗主体内容区 */
            .html-popup-body {
                padding: 15px;
                overflow-y: auto;
                flex: 1;
                line-height: 1.4;
                color: #4b5563;
                font-size: 13px;
            }

            /* 弹窗底部按钮区 */
            .html-popup-footer {
                padding: 12px 15px;
                background: #f9fafb;
                border-top: 1px solid #edf2f7;
                display: flex;
                justify-content: flex-end;
                gap: 10px;
            }

            /* 弹窗按钮样式 */
            .popup-btn {
                padding: 8px 20px;
                border: none;
                border-radius: 6px;
                cursor: pointer;
                font-size: 14px;
                font-weight: 500;
                transition: all 0.2s ease;
                min-width: 80px;
            }

            /* 拒绝按钮 */
            .popup-btn-reject {
                background: #f3f4f6;
                color: #6b7280;
                border: 1px solid #d1d5db;
            }

            .popup-btn-reject:hover {
                background: #e5e7eb;
                color: #4b5563;
            }

            /* 同意按钮 */
            .popup-btn-accept {
                background: #3b82f6;
                color: white;
            }

            .popup-btn-accept:hover {
                background: #2563eb;
            }

            /* 显示状态 */
            .html-popup-modal.show {
                opacity: 1;
            }
            .html-popup-modal.show .html-popup-content {
                transform: scale(1);
            }

            /* 响应式设计 */
            @media (max-width: 900px) {
                .html-popup-content {
                    width: 95% !important;
                    margin: 10px;
                }
            }

            @media (max-width: 768px) {
                .html-popup-content {
                    width: 95% !important;
                    margin: 10px;
                }
                .html-popup-body {
                    padding: 15px;
                }
                .html-popup-footer {
                    padding: 12px 15px;
                    gap: 8px;
                }
                .popup-btn {
                    padding: 10px 16px;
                    font-size: 14px;
                    min-width: 70px;
                }
            }

            /* 完全保持HTML内容的原始样式 */
            .html-popup-body {
                /* 重置弹窗容器的默认样式，让HTML内容完全控制显示 */
                font-family: inherit;
                font-size: inherit;
                line-height: inherit;
                color: inherit;
            }

            /* 不对任何HTML元素进行样式重置，完全保持原始样式 */
            .html-popup-body * {
                /* 保持所有原始样式，不进行任何重置 */
            }

            /* 确保内联样式具有最高优先级 */
            .html-popup-body [style] {
                /* 内联样式自动具有最高优先级 */
            }

            /* 确保链接样式正常工作 */
            .html-popup-body a {
                cursor: pointer;
            }

            /* 确保按钮样式的链接正常显示 */
            .html-popup-body a[style*="background"] {
                text-decoration: none !important;
            }

            /* 确保居中布局正常工作 */
            .html-popup-body [style*="text-align: center"] {
                text-align: center !important;
            }

            /* 确保内联块元素正常显示 */
            .html-popup-body [style*="display: inline-block"] {
                display: inline-block !important;
            }

            /* 确保块级元素正常显示 */
            .html-popup-body [style*="display: block"] {
                display: block !important;
            }

            /* 确保分割线样式正常 */
            .html-popup-body hr {
                margin: 10px 0;
            }

            /* 确保强调文本正常显示 */
            .html-popup-body strong {
                font-weight: bold;
            }

            /* 确保段落间距正常 */
            .html-popup-body p {
                margin: 8px 0;
            }
        `;
    }

    // 获取并显示弹窗
    function showHtmlPopup() {
        const { shopName, merchantId } = getShopInfo();

        // 如果既没有获取到店铺名称也没有获取到商家ID，则不显示弹窗
        if (!shopName && !merchantId) {
            return;
        }

        // 检查显示频率
        const shouldShow = (popupData) => {
            // 使用商家ID和内容哈希值创建唯一存储键
            const contentHash = hashString(popupData.content);
            const storageKey = `html_popup_${shopName || merchantId}_${contentHash}`;
            const lastShow = localStorage.getItem(storageKey);
            const now = new Date().getTime();

            if (!lastShow) return true;

            const frequency = popupData.frequency || 'once';
            switch (frequency) {
                case 'once':
                    return false; // 已经显示过就不再显示
                case 'login':
                    return true; // 每次访问都显示
                case 'daily':
                    const oneDayMs = 24 * 60 * 60 * 1000;
                    return (now - parseInt(lastShow)) >= oneDayMs;
                case 'weekly':
                    const oneWeekMs = 7 * 24 * 60 * 60 * 1000;
                    return (now - parseInt(lastShow)) >= oneWeekMs;
                default:
                    return true;
            }
        };

        // 简单的字符串哈希函数
        function hashString(str) {
            if (!str) return '0';
            let hash = 0;
            for (let i = 0; i < str.length; i++) {
                const char = str.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // Convert to 32bit integer
            }
            return hash.toString();
        }

        // 获取内置模板内容
        function getBuiltinTemplateContent(template, templateFieldsJson, customTemplateContent = null) {
            // 如果有自定义模板内容，直接返回
            if (customTemplateContent) {
                return customTemplateContent;
            }

            // 解析模板字段
            let fields = {};
            if (templateFieldsJson) {
                try {
                    fields = typeof templateFieldsJson === 'string' ? JSON.parse(templateFieldsJson) : templateFieldsJson;
                } catch (e) {
                    // 解析模板字段失败，使用默认值
                }
            }

            // 设置默认值（与管理后台保持一致）
            const defaultFields = {
                // 购买协议模板字段
                platformName: '小火羊云寄售官方频道',
                platformChannel: '@xhyfkw',
                warmTip: '本站不提供任何担保、私下交易被骗一律与本站无关。',
                shopLink: 'https://www.zzrongtong.cn/merchant/',
                serviceLink: 'https://work.weixin.qq.com/kfid/kfcf72f7282e80c5ad2',
                shopButtonText: '开店成为商家赚米',
                serviceButtonText: '联系平台微信客服',
                // 平台规则模板字段
                rulesTitle: '平台使用规则',
                userNotice: '1. 请遵守平台相关规定，文明使用平台服务\n2. 禁止发布违法违规内容，维护良好的平台环境\n3. 保护个人隐私信息，谨防诈骗',
                tradeRules: '1. 所有交易请通过平台正规渠道进行\n2. 如遇问题请及时联系客服处理\n3. 平台将保障用户合法权益',
                serviceNotice: '如有疑问，请联系平台客服'
            };

            // 合并字段
            const finalFields = { ...defaultFields, ...fields };

            if (template === 'purchase_agreement') {
                return `
                    <div id="buy-protocol">
                        <p><strong><span style="color:#303133;"><span style="font-size:18px;">${finalFields.platformName}${finalFields.platformChannel}</span></span></strong></p>
                        <p><strong><span style="color:#ff6600;"><span style="font-size:18px;">温馨提示：${finalFields.warmTip}</span></span></strong></p>
                        <p><span style="color:#ff00ff;">1、本平台仅提供自动发卡、寄售服务，本平台非售卖销售商，有问题联系店铺客服或者平台投诉订单处理，非商品卡密问题本站不予受理售后争议</span></p>
                        <p><span style="color:#27ae60;">2、平台提供卡密查询时间为1个月（购买后请自行保存）如遇假卡/欺诈/涉黄赌/等违规商品请当天23点50前去平台投诉订单处理，若是超过24点后，平台放款给商家，不再受理任何问题。</span></p>
                        <p><span style="color:#ae2760;">3、为了方便售后建议您的每次购物都录个视频，一直到商品正常使用没问题在取消录制哦！</span></p>
                        <p><span style="color:#ff0000;">若遇到任何无法处理的问题，请当天在平台投诉订单，平台官方客服介入处理</span></p>
                        <div style="text-align: center; white-space: nowrap; width: 100%; box-sizing: border-box; margin: 10px 0;">
                            <a href="${finalFields.shopLink}" style="display: inline-block; width: calc(50% - 5px); background-color: #6600ff; color: white; border: none; padding: 8px 4px; margin-right: 10px; border-radius: 5px; text-decoration: none; cursor: pointer; box-sizing: border-box; text-align: center; font-size: 12px;">${finalFields.shopButtonText}</a>
                            <a href="${finalFields.serviceLink}" style="display: inline-block; width: calc(50% - 5px); background-color: #6600ff; color: white; border: none; padding: 8px 4px; border-radius: 5px; text-decoration: none; cursor: pointer; box-sizing: border-box; text-align: center; font-size: 12px;">${finalFields.serviceButtonText}</a>
                        </div>
                        <hr style="border: none; border-top: 1px dashed #000; margin: 20px 0;">
                        <p><span style="font-size:16px;"><strong><span style="color:#ff0000;">防骗提醒</span></strong><span style="color:#ff0000;">：</span></span><span style="color:#6600ff;">1.卡密内容为"联系QQ取卡或者QQ群拿货" 2.以各种理由推脱到第二天发货 3.商品有问题，卖家不售后 4.承诺充值返现 5.购买的商品为实物，需要快递发货的。请及时联系我们平台客服</span></p>
                    </div>
                `;
            } else if (template === 'platform_rules') {
                // 处理用户须知和交易规则的换行
                const userNoticeLines = finalFields.userNotice.split('\n').map((line, index) =>
                    `<p>${index + 1}. ${line.replace(/^\d+\.\s*/, '')}</p>`
                ).join('');

                const tradeRulesLines = finalFields.tradeRules.split('\n').map((line, index) =>
                    `<p>${index + 1}. ${line.replace(/^\d+\.\s*/, '')}</p>`
                ).join('');

                return `
                    <div style="padding: 20px; line-height: 1.6;">
                        <h3 style="color: #333; text-align: center; margin-bottom: 20px;">${finalFields.rulesTitle}</h3>
                        <div style="background: #f0f8ff; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                            <h4 style="color: #1e90ff; margin-top: 0;">用户须知</h4>
                            ${userNoticeLines}
                        </div>
                        <div style="background: #fff5ee; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                            <h4 style="color: #ff6347; margin-top: 0;">交易规则</h4>
                            ${tradeRulesLines}
                        </div>
                        <div style="text-align: center; margin-top: 20px;">
                            <p style="color: #666;">${finalFields.serviceNotice}</p>
                        </div>
                    </div>
                `;
            }

            // 默认返回购买协议模板
            return getBuiltinTemplateContent('purchase_agreement', templateFieldsJson);
        }

        // 构建请求参数
        const params = new URLSearchParams();
        if (shopName) params.append('shop_name', shopName);
        if (merchantId) params.append('merchant_id', merchantId);

        // 发送请求获取弹窗内容
        const baseUrl = window.location.protocol + '//' + window.location.host;
        fetch(baseUrl + '/plugin/Htmlpopup/api/fetchData?' + params.toString(), {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200 && data.data) {
                // 处理内容
                let content = data.data.content;

                // 如果是内置模板，动态生成模板内容
                if (data.data.content_type === 'builtin') {
                    // 检查是否是管理员自定义模板（不是预设的两个可编辑模板）
                    if (!['purchase_agreement', 'platform_rules'].includes(data.data.builtin_template)) {
                        // 对于管理员自定义模板，使用服务器返回的content字段
                        content = data.data.builtin_template_content || data.data.content || '';
                    } else {
                        // 对于预设的可编辑模板，动态生成内容
                        content = getBuiltinTemplateContent(data.data.builtin_template, data.data.template_fields);
                    }
                }

                // 检查弹窗开关状态和内容
                if (data.data.status === 1 && content) {
                    // 更新数据对象的内容
                    const popupData = { ...data.data, content: content };

                    // 检查是否应该显示
                    if (shouldShow(popupData)) {
                        createHtmlPopup(popupData);

                        // 存储显示时间
                        const contentHash = hashString(content);
                        const storageKey = `html_popup_${shopName || merchantId}_${contentHash}`;
                        localStorage.setItem(storageKey, new Date().getTime().toString());
                    }
                }
            }
        })
        .catch(() => {
            // 错误处理，静默忽略
        });
    }

    // 确保页面完全加载后再显示弹窗
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', showHtmlPopup);
    } else {
        showHtmlPopup();
    }
})();
