<?php
namespace plugin\Clearallinone\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;

class Api extends BasePlugin
{
    protected $scene = [
        'admin'
    ];
    protected $noNeedLogin = [];

    public function index()
    {
        return View::fetch();
    }

    // 搜索用户接口
    public function searchUsers()
    {
        try {
            $keyword = $this->request->post('keyword', '');
            if (!is_numeric($keyword)) {
                return json(['code' => 200, 'data' => []]);
            }

            // 通过ID搜索用户，并获取相似ID的用户列表
            $users = Db::name('user')
                ->where('id', 'like', $keyword . '%')
                ->field(['id', 'username'])
                ->limit(10)
                ->select()
                ->toArray();

            if (!empty($users)) {
                return json([
                    'code' => 200, 
                    'msg' => '查询成功',
                    'data' => array_map(function($user) {
                        return [
                            'value' => $user['id'],
                            'label' => "ID: {$user['id']} ({$user['username']})"
                        ];
                    }, $users)
                ]);
            }

            return json(['code' => 200, 'msg' => '未找到用户', 'data' => []]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '搜索用户失败: ' . $e->getMessage()]);
        }
    }

    // 修改清理登录日志方法
    public function clearLoginLogs()
    {
        try {
            $userId = $this->request->post('user_id/d', 0);
            
            // 获取正确的表名（带前缀）
            $tableName = config('database.connections.mysql.prefix') . 'user_login_log';
            
            // 构建查询条件
            $query = Db::table($tableName);
            if ($userId > 0) {
                $query = $query->where('user_id', $userId);
                
                // 查询当前日志数量
                $beforeCount = $query->count();

                if ($beforeCount == 0) {
                    return json(['code' => 200, 'msg' => "用户ID {$userId} 暂无登录日志"]);
                }
                
                // 删除指定用户的记录
                $deleted = $query->delete();
                $logMessage = sprintf("成功清理用户ID %d 的登录日志，共删除 %d 条记录", $userId, $beforeCount);
            } else {
                // 清空整个表，使用完整的表名
                $deleted = Db::execute('TRUNCATE TABLE `' . $tableName . '`');
                $logMessage = "成功清空所有登录日志";
            }

            if ($deleted === false) {
                throw new \Exception('删除操作失败');
            }

            // 记录日志
            error_log("Clearallinone: " . $logMessage);

            return json([
                'code' => 200,
                'msg' => $logMessage
            ]);

        } catch (\Exception $e) {
            error_log("Clearallinone: 清理登录日志失败: " . $e->getMessage());
            return json(['code' => 500, 'msg' => '清理失败: ' . $e->getMessage()]);
        }
    }

    // 修改清理投诉图片方法
    public function clearComplaintImages()
    {
        try {
            $userId = $this->request->post('user_id/d', 0);  // 添加用户ID参数
            $startDate = $this->request->post('start_date', '');
            $endDate = $this->request->post('end_date', '');
            
            // 构建查询条件
            $query = Db::name('complaint')
                ->whereNotNull('images')
                ->where('images', '<>', '');
                
            // 添加用户ID条件
            if ($userId > 0) {
                $query = $query->where('user_id', $userId);
            }
            
            // 添加日期范围条件
            if (!empty($startDate)) {
                $query = $query->where('create_time', '>=', strtotime($startDate));
            }
            if (!empty($endDate)) {
                $query = $query->where('create_time', '<=', strtotime($endDate . ' 23:59:59'));
            }
            
            $complaintImages = $query->column('images');
            
            // 构建收藏图片查询
            $collectQuery = Db::name('complaint')
                ->whereNotNull('collect_image')
                ->where('collect_image', '<>', '');
                
            // 添加用户ID条件
            if ($userId > 0) {
                $collectQuery = $collectQuery->where('user_id', $userId);
            }
            
            // 添加日期范围条件
            if (!empty($startDate)) {
                $collectQuery = $collectQuery->where('create_time', '>=', strtotime($startDate));
            }
            if (!empty($endDate)) {
                $collectQuery = $collectQuery->where('create_time', '<=', strtotime($endDate . ' 23:59:59'));
            }
            
            $collectImages = $collectQuery->column('collect_image');

            if (empty($complaintImages) && empty($collectImages)) {
                return json(['code' => 200, 'msg' => $userId > 0 ? "该用户暂无投诉图片" : "暂无投诉图片"]);
            }

            $deletedFiles = 0;
            $totalFiles = 0;
            $failedFiles = [];

            // 处理投诉图片
            foreach ($complaintImages as $imageJson) {
                $imageArray = json_decode($imageJson, true);
                if (!empty($imageArray)) {
                    foreach ($imageArray as $image) {
                        if (!empty($image)) {
                            $totalFiles++;
                            $this->deleteImageFile($image, $deletedFiles, $failedFiles);
                        }
                    }
                }
            }

            // 处理收藏图片
            foreach ($collectImages as $image) {
                if (!empty($image)) {
                    $totalFiles++;
                    $this->deleteImageFile($image, $deletedFiles, $failedFiles);
                }
            }

            // 更新数据库记录
            if ($userId > 0) {
                // 只更新指定用户的记录
                Db::name('complaint')
                    ->where('user_id', $userId)
                    ->whereNotNull('images')
                    ->where('images', '<>', '')
                    ->update(['images' => '']);

                Db::name('complaint')
                    ->where('user_id', $userId)
                    ->whereNotNull('collect_image')
                    ->where('collect_image', '<>', '')
                    ->update(['collect_image' => '']);
            } else {
                // 更新所有记录
                Db::name('complaint')
                    ->whereNotNull('images')
                    ->where('images', '<>', '')
                    ->update(['images' => '']);

                Db::name('complaint')
                    ->whereNotNull('collect_image')
                    ->where('collect_image', '<>', '')
                    ->update(['collect_image' => '']);
            }

            $message = sprintf(
                "%s清理完成！共处理 %d 个文件，成功删除 %d 个文件",
                $userId > 0 ? "用户ID {$userId} 的投诉图片" : "所有投诉图片",
                $totalFiles,
                $deletedFiles
            );

            if (!empty($failedFiles)) {
                $message .= "\n以下文件未能成功删除：\n" . implode("\n", $failedFiles);
            }

            error_log("Clearallinone: " . $message);

            return json([
                'code' => 200,
                'msg' => $message,
                'data' => [
                    'total_files' => $totalFiles,
                    'deleted_files' => $deletedFiles,
                    'failed_files' => $failedFiles
                ]
            ]);

        } catch (\Exception $e) {
            error_log("Clearallinone: 清理图片失败: " . $e->getMessage());
            return json(['code' => 500, 'msg' => '清理失败: ' . $e->getMessage()]);
        }
    }

    // 修改文件删除方法
    private function deleteImageFile($image, &$deletedFiles, &$failedFiles)
    {
        try {
            // 从URL中提取相对路径
            $relativePath = parse_url($image, PHP_URL_PATH);
            if (empty($relativePath)) {
                $failedFiles[] = $image;
                error_log("Clearallinone: 无效的图片URL: " . $image);
                return;
            }

            $filePath = public_path() . ltrim($relativePath, '/');
            
            // 确保路径是文件而不是目录
            if (is_dir($filePath)) {
                $failedFiles[] = $image;
                error_log("Clearallinone: 路径指向目录而不是文件: " . $filePath);
                return;
            }

            if (file_exists($filePath) && is_file($filePath)) {
                if (@unlink($filePath)) {
                    $deletedFiles++;
                    error_log("Clearallinone: 成功删除文件: " . $filePath);
                    
                    // 尝试删除空文件夹
                    $dirPath = dirname($filePath);
                    if (is_dir($dirPath) && count(scandir($dirPath)) <= 2) {
                        @rmdir($dirPath);
                        error_log("Clearallinone: 删除空文件夹: " . $dirPath);
                    }
                } else {
                    $failedFiles[] = $image;
                    error_log("Clearallinone: 删除文件失败: " . $filePath);
                }
            } else {
                $failedFiles[] = $image;
                error_log("Clearallinone: 文件不存在或不是有效文件: " . $filePath);
            }
        } catch (\Exception $e) {
            $failedFiles[] = $image;
            error_log("Clearallinone: 删除文件时发生错误: " . $e->getMessage());
        }
    }

    // 清理用户投诉记录
    public function clearUserComplaints()
    {
        try {
            $userId = $this->request->post('user_id/d', 0);
            $startDate = $this->request->post('start_date', '');
            $endDate = $this->request->post('end_date', '');
            $currentTime = time();
            
            // 构建查询条件
            $query = Db::name('complaint')
                ->where('expire_time', '<=', $currentTime);
                
            if ($userId > 0) {
                $query = $query->where('user_id', $userId);
            }
            
            // 添加日期范围条件
            if (!empty($startDate)) {
                $query = $query->where('create_time', '>=', strtotime($startDate));
            }
            if (!empty($endDate)) {
                $query = $query->where('create_time', '<=', strtotime($endDate . ' 23:59:59'));
            }

            // 获取要清理的记录数
            $beforeCount = $query->count();

            if ($beforeCount == 0) {
                $msg = $userId > 0 
                    ? "该用户暂无可清理的过期投诉记录" 
                    : "暂无可清理的过期投诉记录";
                return json(['code' => 200, 'msg' => $msg]);
            }

            // 先获取所有相关图片
            $records = $query->select()->toArray();

            $deletedFiles = 0;
            $totalFiles = 0;
            $failedFiles = [];

            // 处理图片文件
            foreach ($records as $record) {
                // 处理投诉图片
                if (!empty($record['images'])) {
                    $imageArray = json_decode($record['images'], true);
                    if (!empty($imageArray)) {
                        foreach ($imageArray as $image) {
                            $totalFiles++;
                            $this->deleteImageFile($image, $deletedFiles, $failedFiles);
                        }
                    }
                }

                // 处理收藏图片
                if (!empty($record['collect_image'])) {
                    $totalFiles++;
                    $this->deleteImageFile($record['collect_image'], $deletedFiles, $failedFiles);
                }
            }

            // 删除记录
            $deleted = $query->delete();
            
            if ($deleted === false) {
                throw new \Exception('删除记录失败');
            }

            $message = sprintf(
                "%s清理完成！共删除 %d 条过期投诉记录，处理 %d 个文件，成功删除 %d 个文件",
                $userId > 0 ? "用户过期投诉" : "所有过期投诉",
                $beforeCount,
                $totalFiles,
                $deletedFiles
            );

            if (!empty($failedFiles)) {
                $message .= "\n以下文件未能成功删除：\n" . implode("\n", $failedFiles);
            }

            // 记录日志
            error_log("Clearallinone: " . $message);

            return json([
                'code' => 200,
                'msg' => $message,
                'data' => [
                    'deleted_count' => $beforeCount,
                    'total_files' => $totalFiles,
                    'deleted_files' => $deletedFiles,
                    'failed_files' => $failedFiles
                ]
            ]);

        } catch (\Exception $e) {
            error_log("Clearallinone: 清理投诉记录失败: " . $e->getMessage());
            return json(['code' => 500, 'msg' => '清理失败: ' . $e->getMessage()]);
        }
    }

    // 修改查询用户投诉图片统计方法
    public function getComplaintImageStats()
    {
        try {
            $userId = $this->request->post('user_id/d', 0);
            $startDate = $this->request->post('start_date', '');
            $endDate = $this->request->post('end_date', '');
            
            // 构建查询条件
            $query = Db::name('complaint')
                ->where(function ($query) {
                    $query->where(function ($subQuery) {
                        $subQuery->whereNotNull('images')
                                ->where('images', '<>', '');
                    })->whereOr(function ($subQuery) {
                        $subQuery->whereNotNull('collect_image')
                                ->where('collect_image', '<>', '');
                    });
                });
                
            // 如果指定了用户ID，则添加用户条件
            if ($userId > 0) {
                $query = $query->where('user_id', $userId);
            }
            
            // 添加日期范围条件
            if (!empty($startDate)) {
                $query = $query->where('create_time', '>=', strtotime($startDate));
            }
            if (!empty($endDate)) {
                $query = $query->where('create_time', '<=', strtotime($endDate . ' 23:59:59'));
            }

            $records = $query->select()->toArray();

            $totalFiles = 0;
            $totalSize = 0;
            $fileList = [];

            foreach ($records as $record) {
                // 处理投诉图片
                if (!empty($record['images'])) {
                    $imageArray = json_decode($record['images'], true);
                    if (!empty($imageArray)) {
                        foreach ($imageArray as $image) {
                            $filePath = $this->getLocalFilePath($image);
                            if (file_exists($filePath)) {
                                $totalFiles++;
                                $fileSize = filesize($filePath);
                                $totalSize += $fileSize;
                                $fileList[] = [
                                    'path' => $image,
                                    'size' => $fileSize
                                ];
                            }
                        }
                    }
                }

                // 处理收藏图片
                if (!empty($record['collect_image'])) {
                    $filePath = $this->getLocalFilePath($record['collect_image']);
                    if (file_exists($filePath)) {
                        $totalFiles++;
                        $fileSize = filesize($filePath);
                        $totalSize += $fileSize;
                        $fileList[] = [
                            'path' => $record['collect_image'],
                            'size' => $fileSize
                        ];
                    }
                }
            }

            // 格式化文件大小
            $formattedSize = $this->formatFileSize($totalSize);

            // 修改返回消息，添加日期范围信息
            $dateRangeMsg = '';
            if (!empty($startDate) && !empty($endDate)) {
                $dateRangeMsg = sprintf("（%s 至 %s）", $startDate, $endDate);
            } elseif (!empty($startDate)) {
                $dateRangeMsg = sprintf("（%s 之后）", $startDate);
            } elseif (!empty($endDate)) {
                $dateRangeMsg = sprintf("（%s 之前）", $endDate);
            }

            // 修改返回消息，根据是否有用户ID显示不同内容
            $userMsg = $userId > 0 ? "用户ID {$userId} " : "所有用户";
            return json([
                'code' => 200,
                'msg' => sprintf("%s%s共有 %d 个投诉相关图片文件，总大小 %s", 
                    $userMsg, $dateRangeMsg, $totalFiles, $formattedSize),
                'data' => [
                    'total_files' => $totalFiles,
                    'total_size' => $totalSize,
                    'formatted_size' => $formattedSize,
                    'files' => $fileList
                ]
            ]);

        } catch (\Exception $e) {
            error_log("Clearallinone: 查询用户图片统计失败: " . $e->getMessage());
            return json(['code' => 500, 'msg' => '查询失败: ' . $e->getMessage()]);
        }
    }

    // 添加辅助方法：获取本地文件路径
    private function getLocalFilePath($url)
    {
        $relativePath = parse_url($url, PHP_URL_PATH);
        return public_path() . ltrim($relativePath, '/');
    }

    // 添加辅助方法：格式化文件大小
    private function formatFileSize($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }

    // 修改清理商品方法，同时清理图片
    public function clearGoods()
    {
        try {
            $userId = $this->request->post('user_id/d', 0);
            
            // 构建查询条件
            $query = Db::name('goods');
            if ($userId > 0) {
                $query = $query->where('user_id', $userId);
            }

            // 先获取需要删除的商品图片
            $records = $query->whereNotNull('image')
                ->where('image', '<>', '')
                ->field(['id', 'image'])
                ->select()
                ->toArray();

            // 处理图片文件
            $deletedFiles = 0;
            $totalFiles = 0;
            $failedFiles = [];

            foreach ($records as $record) {
                if (!empty($record['image'])) {
                    $totalFiles++;
                    $this->deleteImageFile($record['image'], $deletedFiles, $failedFiles);
                }
            }

            // 获取商品总数
            $beforeCount = $query->count();

            if ($beforeCount == 0) {
                $msg = $userId > 0 
                    ? "用户ID {$userId} 暂无商品记录" 
                    : "暂无商品记录";
                return json(['code' => 200, 'msg' => $msg]);
            }

            // 删除商品记录
            $deleted = $query->delete(true);

            if ($deleted === false) {
                throw new \Exception('删除操作失败');
            }

            // 构建返回消息
            $logMessage = sprintf(
                "%s清理完成！删除 %d 条商品记录，处理 %d 个图片文件，成功删除 %d 个图片",
                $userId > 0 ? "用户ID {$userId} 的" : "所有",
                $beforeCount,
                $totalFiles,
                $deletedFiles
            );

            if (!empty($failedFiles)) {
                $logMessage .= "\n以下图片文件未能成功删除：\n" . implode("\n", $failedFiles);
            }

            // 记录日志
            error_log("Clearallinone: " . $logMessage);

            return json([
                'code' => 200,
                'msg' => $logMessage,
                'data' => [
                    'deleted_records' => $beforeCount,
                    'total_files' => $totalFiles,
                    'deleted_files' => $deletedFiles,
                    'failed_files' => $failedFiles
                ]
            ]);

        } catch (\Exception $e) {
            error_log("Clearallinone: 清理商品记录失败: " . $e->getMessage());
            return json(['code' => 500, 'msg' => '清理失败: ' . $e->getMessage()]);
        }
    }

    // 添加清理商品图片方法
    public function clearGoodsImages()
    {
        try {
            $userId = $this->request->post('user_id/d', 0);
            
            // 构建查询条件
            $query = Db::name('goods')
                ->whereNotNull('image')
                ->where('image', '<>', '');
                
            if ($userId > 0) {
                $query = $query->where('user_id', $userId);
            }

            $records = $query->select()->toArray();

            if (empty($records)) {
                $msg = $userId > 0 
                    ? "该用户暂无商品图片" 
                    : "暂无商品图片";
                return json(['code' => 200, 'msg' => $msg]);
            }

            $deletedFiles = 0;
            $totalFiles = 0;
            $failedFiles = [];

            // 处理图片文件
            foreach ($records as $record) {
                if (!empty($record['image'])) {
                    $totalFiles++;
                    $this->deleteImageFile($record['image'], $deletedFiles, $failedFiles);
                }
            }

            // 清空数据库中的图片记录
            $query->update(['image' => '']);

            $message = sprintf(
                "%s清理完成！处理 %d 个文件，成功删除 %d 个文件",
                $userId > 0 ? "用户商品图片" : "所有商品图片",
                $totalFiles,
                $deletedFiles
            );

            if (!empty($failedFiles)) {
                $message .= "\n以下文件未能成功删除：\n" . implode("\n", $failedFiles);
            }

            // 记录日志
            error_log("Clearallinone: " . $message);

            return json([
                'code' => 200,
                'msg' => $message,
                'data' => [
                    'total_files' => $totalFiles,
                    'deleted_files' => $deletedFiles,
                    'failed_files' => $failedFiles
                ]
            ]);

        } catch (\Exception $e) {
            error_log("Clearallinone: 清理商品图片失败: " . $e->getMessage());
            return json(['code' => 500, 'msg' => '清理失败: ' . $e->getMessage()]);
        }
    }

    // 更新配置
    public function updateConfig()
    {
        try {
            $key = $this->request->post('key', '');
            $value = $this->request->post('value');

            if (empty($key)) {
                throw new \Exception('参数错误');
            }

            // 更新配置
            plugconf_set("Clearallinone." . $key, $value);

            return json(['code' => 200, 'msg' => '更新成功']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '更新失败: ' . $e->getMessage()]);
        }
    }

    // 获取配置数据
    public function fetchData()
    {
        try {
            $data = [
                // 订单配置
                'order_status' => intval(plugconf("Clearallinone.order_status") ?? 0),
                'order_days' => intval(plugconf("Clearallinone.order_days") ?? 30),
                'order_execute_time' => plugconf("Clearallinone.order_execute_time") ?? '00:00',
                'order_interval' => intval(plugconf("Clearallinone.order_interval") ?? 1),
                'order_status_list' => plugconf("Clearallinone.order_auto_status_list") ?? '1',
                
                // 添加商户通知配置
                'message_status' => intval(plugconf("Clearallinone.message_status") ?? 0),
                'message_days' => intval(plugconf("Clearallinone.message_days") ?? 1),
                'message_execute_time' => plugconf("Clearallinone.message_execute_time") ?? '00:00',
                'message_interval' => intval(plugconf("Clearallinone.message_interval") ?? 1),
                
                // 卡密清理配置
                'card_status' => intval(plugconf("Clearallinone.card_status") ?? 0),
                'card_execute_time' => plugconf("Clearallinone.card_execute_time") ?? '00:00',
                'card_interval' => intval(plugconf("Clearallinone.card_interval") ?? 1),
            ];

            return json(['code' => 1, 'msg' => '获取成功', 'data' => $data]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取配置失败: ' . $e->getMessage()]);
        }
    }

    // 保存订单配置
    public function saveOrder()
    {
        try {
            $status = $this->request->post('status/d', 0);
            $days = $this->request->post('days/d', 30);
            $executeTime = $this->request->post('executeTime', '00:00');
            $interval = $this->request->post('interval/d', 1);
            $autoStatusList = $this->request->post('autoStatusList', '1');

            // 验证时间格式
            if (!preg_match('/^([01][0-9]|2[0-3]):([0-5][0-9])$/', $executeTime)) {
                return json(['code' => 0, 'msg' => '执行时间格式必须为 HH:mm']);
            }

            // 验证其他参数
            if ($days < 1 || $days > 365) {
                return json(['code' => 0, 'msg' => '清理天数必须在1-365之间']);
            }
            if ($interval < 0 || $interval > 30) {
                return json(['code' => 0, 'msg' => '执行间隔必须在0-30之间']);
            }

            // 保存配置
            plugconf("Clearallinone.order_status", $status);
            plugconf("Clearallinone.order_days", $days);
            plugconf("Clearallinone.order_execute_time", $executeTime);
            plugconf("Clearallinone.order_interval", $interval);
            plugconf("Clearallinone.order_auto_status_list", $autoStatusList);

            return json(['code' => 1, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '保存失败: ' . $e->getMessage()]);
        }
    }

    // 修改手动清理订单方法
    public function clearOrders()
    {
        try {
            $userId = $this->request->post('user_id/d', 0);
            $deleteAll = $this->request->post('delete_all/b', false);
            $startDate = $this->request->post('start_date', '');
            $endDate = $this->request->post('end_date', '');
            $statusList = $this->request->post('status_list/a', []); // 获取状态列表
            
            // 记录接收到的参数
            error_log("Clearallinone: 清理订单参数 - 用户ID: $userId, 删除全部: " . ($deleteAll ? '是' : '否') . 
                ", 开始日期: $startDate, 结束日期: $endDate, 状态列表: " . json_encode($statusList));

            // 安全检查：必须选择至少一种订单状态
            if (empty($statusList)) {
                return json([
                    'code' => 400,
                    'msg' => '请选择至少一种订单状态进行清理。'
                ]);
            }

            // 构建查询条件
            $query = Db::name('order');
            
            // 添加时间范围条件
            if (!empty($startDate)) {
                $query->where('create_time', '>=', strtotime($startDate));
            }
            if (!empty($endDate)) {
                $query->where('create_time', '<=', strtotime($endDate . ' 23:59:59'));
            }

            // 添加状态条件
            if (!empty($statusList)) {
                $query->whereIn('status', $statusList);
            }

            // 获取所有冻结中的订单ID
            $frozenOrderIds = Db::name('auto_unfreeze')
                ->column('order_id');

            // 排除冻结中的订单
            if (!empty($frozenOrderIds)) {
                $query->whereNotIn('id', $frozenOrderIds);
            }

            if (!$deleteAll && $userId > 0) {
                $query->where('user_id', $userId);
            }

            // 获取要删除的订单数量统计（按商家分组）
            $sellerStats = $query->group('user_id')
                ->column('COUNT(*)', 'user_id');

            // 获取要删除的记录数
            $beforeCount = $query->count();

            if ($beforeCount == 0) {
                $msg = $userId > 0 
                    ? "用户ID {$userId} 暂无订单记录" 
                    : "暂无订单记录";
                if (!empty($startDate) || !empty($endDate)) {
                    $msg .= "（在指定日期范围内）";
                }
                if (!empty($statusList)) {
                    $msg .= sprintf("（状态：%s）", implode('、', array_map(function($status) {
                        $statusMap = [0 => '未支付', 1 => '已支付', 2 => '已关闭', 3 => '已退款'];
                        return $statusMap[$status] ?? $status;
                    }, $statusList)));
                }
                return json(['code' => 200, 'msg' => $msg]);
            }

            // 开启事务
            Db::startTrans();
            try {
                // 执行清理
                $deleted = $query->delete();
                
                if ($deleted === false) {
                    throw new \Exception('删除操作失败');
                }

                // 更新商家销量统计（添加安全检查）
                foreach ($sellerStats as $sellerId => $count) {
                    // 先获取当前销量
                    $currentSellCount = Db::name('user')
                        ->where('id', $sellerId)
                        ->value('sell_count');
                    
                    // 确保减少后的值不会小于0
                    $newCount = max(0, intval($currentSellCount) - intval($count));
                    
                    // 使用新的安全值更新
                    Db::name('user')
                        ->where('id', $sellerId)
                        ->update(['sell_count' => $newCount]);
                }

                Db::commit();
                
                // 修改返回消息，使用实际删除的数量而非查询到的数量
                $message = sprintf(
                    "%s清理完成！共删除 %d 条订单记录",
                    $userId > 0 ? "用户ID {$userId} 的" : "所有",
                    $deleted
                );
                if (!empty($startDate) || !empty($endDate)) {
                    $message .= sprintf("（%s 至 %s）", $startDate, $endDate);
                }
                if (!empty($statusList)) {
                    $message .= sprintf("（状态：%s）", implode('、', array_map(function($status) {
                        $statusMap = [0 => '未支付', 1 => '已支付', 2 => '已关闭', 3 => '已退款'];
                        return $statusMap[$status] ?? $status;
                    }, $statusList)));
                }

                // 记录日志
                error_log("Clearallinone: " . $message);

                return json([
                    'code' => 200,
                    'msg' => $message,
                    'data' => [
                        'deleted_count' => $deleted,
                        'updated_sellers' => count($sellerStats)
                    ]
                ]);

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            error_log("Clearallinone: 清理订单失败: " . $e->getMessage());
            return json(['code' => 500, 'msg' => '清理失败: ' . $e->getMessage()]);
        }
    }

    // 添加新方法：同时清理投诉记录和图片
    public function clearUserComplaintsAndImages()
    {
        try {
            $userId = $this->request->post('user_id/d', 0);
            $startDate = $this->request->post('start_date', '');
            $endDate = $this->request->post('end_date', '');
            $currentTime = time();
            
            // 构建查询条件
            $query = Db::name('complaint')
                ->where('expire_time', '<=', $currentTime);
                
            if ($userId > 0) {
                $query = $query->where('user_id', $userId);
            }
            
            // 添加日期范围条件
            if (!empty($startDate)) {
                $query = $query->where('create_time', '>=', strtotime($startDate));
            }
            if (!empty($endDate)) {
                $query = $query->where('create_time', '<=', strtotime($endDate . ' 23:59:59'));
            }

            // 获取要清理的记录数
            $beforeCount = $query->count();

            if ($beforeCount == 0) {
                $msg = $userId > 0 
                    ? "该用户暂无可清理的过期投诉记录" 
                    : "暂无可清理的过期投诉记录";
                return json(['code' => 200, 'msg' => $msg]);
            }

            // 先获取所有相关图片
            $records = $query->select()->toArray();

            $deletedFiles = 0;
            $totalFiles = 0;
            $failedFiles = [];

            // 处理图片文件
            foreach ($records as $record) {
                // 处理投诉图片
                if (!empty($record['images'])) {
                    $imageArray = json_decode($record['images'], true);
                    if (!empty($imageArray)) {
                        foreach ($imageArray as $image) {
                            $totalFiles++;
                            $this->deleteImageFile($image, $deletedFiles, $failedFiles);
                        }
                    }
                }

                // 处理收藏图片
                if (!empty($record['collect_image'])) {
                    $totalFiles++;
                    $this->deleteImageFile($record['collect_image'], $deletedFiles, $failedFiles);
                }
            }

            // 删除记录
            $deleted = $query->delete();
            
            if ($deleted === false) {
                throw new \Exception('删除记录失败');
            }

            $message = sprintf(
                "%s清理完成！共删除 %d 条过期投诉记录，处理 %d 个文件，成功删除 %d 个文件",
                $userId > 0 ? "用户过期投诉" : "所有过期投诉",
                $beforeCount,
                $totalFiles,
                $deletedFiles
            );

            if (!empty($failedFiles)) {
                $message .= "\n以下文件未能成功删除：\n" . implode("\n", $failedFiles);
            }

            // 记录日志
            error_log("Clearallinone: " . $message);

            return json([
                'code' => 200,
                'msg' => $message,
                'data' => [
                    'deleted_count' => $beforeCount,
                    'total_files' => $totalFiles,
                    'deleted_files' => $deletedFiles,
                    'failed_files' => $failedFiles
                ]
            ]);

        } catch (\Exception $e) {
            error_log("Clearallinone: 清理投诉记录和图片失败: " . $e->getMessage());
            return json(['code' => 500, 'msg' => '清理失败: ' . $e->getMessage()]);
        }
    }

    // 添加手动清理商户通知的方法
    public function clearUserMessages()
    {
        try {
            $userId = $this->request->post('user_id/d', 0);
            $startDate = $this->request->post('start_date', '');
            $endDate = $this->request->post('end_date', '');
            
            // 构建查询条件
            $query = Db::name('user_message')
                ->where('user_read', 1);  // 只清理已读消息
                
            if ($userId > 0) {
                $query = $query->where('user_id', $userId);
            }
            
            // 添加日期范围条件
            if (!empty($startDate)) {
                $query = $query->where('create_time', '>=', strtotime($startDate . ' 00:00:00'));
            }
            if (!empty($endDate)) {
                $query = $query->where('create_time', '<=', strtotime($endDate . ' 23:59:59'));
            }

            // 获取要删除的记录数
            $beforeCount = $query->count();

            if ($beforeCount == 0) {
                $msg = $userId > 0 
                    ? "用户ID {$userId} 暂无已读通知" 
                    : "暂无已读通知";
                if (!empty($startDate) || !empty($endDate)) {
                    $msg .= "（在指定日期范围内）";
                }
                return json(['code' => 200, 'msg' => $msg]);
            }

            // 删除记录
            $deleted = $query->delete();
            
            if ($deleted === false) {
                throw new \Exception('删除操作失败');
            }

            $message = sprintf(
                "%s清理完成！共删除 %d 条已读通知",
                $userId > 0 ? "用户ID {$userId} 的" : "所有用户",
                $beforeCount
            );
            if (!empty($startDate) || !empty($endDate)) {
                $message .= "（在指定日期范围内）";
            }

            // 记录日志
            error_log("Clearallinone: " . $message);

            return json([
                'code' => 200,
                'msg' => $message,
                'data' => [
                    'deleted_count' => $beforeCount
                ]
            ]);

        } catch (\Exception $e) {
            error_log("Clearallinone: 清理商户通知失败: " . $e->getMessage());
            return json(['code' => 500, 'msg' => '清理失败: ' . $e->getMessage()]);
        }
    }

    // 保存商户通知自动清理配置
    public function saveMessageConfig()
    {
        try {
            $status = $this->request->post('status/d', 0);
            $executeTime = $this->request->post('executeTime', '00:00');
            $interval = $this->request->post('interval/d', 1);

            // 验证时间格式
            if (!preg_match('/^([01][0-9]|2[0-3]):([0-5][0-9])$/', $executeTime)) {
                return json(['code' => 0, 'msg' => '执行时间格式必须为 HH:mm']);
            }

            // 验证间隔参数
            if ($interval < 0 || $interval > 30) {
                return json(['code' => 0, 'msg' => '执行间隔必须在0-30之间']);
            }

            // 保存配置
            plugconf("Clearallinone.message_status", $status);
            plugconf("Clearallinone.message_execute_time", $executeTime);
            plugconf("Clearallinone.message_interval", $interval);

            return json(['code' => 1, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '保存失败: ' . $e->getMessage()]);
        }
    }

    // 添加商品图片统计方法
    public function getGoodsImageStats()
    {
        try {
            $userId = $this->request->post('user_id/d', 0);
            $startDate = $this->request->post('start_date', '');
            $endDate = $this->request->post('end_date', '');
            
            // 构建查询条件
            $query = Db::name('goods')
                ->whereNotNull('image')
                ->where('image', '<>', '');
                
            // 如果指定了用户ID，则添加用户条件
            if ($userId > 0) {
                $query = $query->where('user_id', $userId);
            }
            
            // 添加日期范围条件
            if (!empty($startDate)) {
                $query = $query->where('create_time', '>=', strtotime($startDate));
            }
            if (!empty($endDate)) {
                $query = $query->where('create_time', '<=', strtotime($endDate . ' 23:59:59'));
            }

            $records = $query->select()->toArray();

            $totalFiles = 0;
            $totalSize = 0;
            $fileList = [];

            foreach ($records as $record) {
                if (!empty($record['image'])) {
                    $filePath = $this->getLocalFilePath($record['image']);
                    if (file_exists($filePath)) {
                        $totalFiles++;
                        $fileSize = filesize($filePath);
                        $totalSize += $fileSize;
                        $fileList[] = [
                            'path' => $record['image'],
                            'size' => $fileSize
                        ];
                    }
                }
            }

            // 格式化文件大小
            $formattedSize = $this->formatFileSize($totalSize);

            // 修改返回消息，添加日期范围信息
            $dateRangeMsg = '';
            if (!empty($startDate) && !empty($endDate)) {
                $dateRangeMsg = sprintf("（%s 至 %s）", $startDate, $endDate);
            } elseif (!empty($startDate)) {
                $dateRangeMsg = sprintf("（%s 之后）", $startDate);
            } elseif (!empty($endDate)) {
                $dateRangeMsg = sprintf("（%s 之前）", $endDate);
            }

            // 修改返回消息，根据是否有用户ID显示不同内容
            $userMsg = $userId > 0 ? "用户ID {$userId} " : "所有用户";
            return json([
                'code' => 200,
                'msg' => sprintf("%s%s共有 %d 个商品图片文件，总大小 %s", 
                    $userMsg, $dateRangeMsg, $totalFiles, $formattedSize),
                'data' => [
                    'total_files' => $totalFiles,
                    'total_size' => $totalSize,
                    'formatted_size' => $formattedSize,
                    'files' => $fileList
                ]
            ]);

        } catch (\Exception $e) {
            error_log("Clearallinone: 查询商品图片统计失败: " . $e->getMessage());
            return json(['code' => 500, 'msg' => '查询失败: ' . $e->getMessage()]);
        }
    }

    // 修改备份数据库方法
    public function backupDatabase()
    {
        try {
            // 获取备份类型
            $type = $this->request->post('type', 'all'); // 'all' 或 'order'
            
            // 获取数据库配置
            $config = config('database.connections.mysql');
            
            // 设置备份文件名，根据类型区分
            $prefix = ($type === 'order') ? 'order_backup_' : 'full_backup_';
            $filename = $prefix . date('Y-m-d_His') . '.sql';
            $backupPath = root_path() . 'backup/';
            
            // 创建备份目录
            if (!is_dir($backupPath)) {
                mkdir($backupPath, 0755, true);
            }
            
            // 完整的备份文件路径
            $filePath = $backupPath . $filename;
            
            // 构建 mysqldump 命令
            if ($type === 'order') {
                // 只备份 order 表
                $tableName = $config['prefix'] . 'order';
                $command = sprintf(
                    'mysqldump -h %s -u %s -p%s %s %s > %s',
                    escapeshellarg($config['hostname']),
                    escapeshellarg($config['username']),
                    escapeshellarg($config['password']),
                    escapeshellarg($config['database']),
                    escapeshellarg($tableName),
                    escapeshellarg($filePath)
                );
            } else {
                // 备份整个数据库
                $command = sprintf(
                    'mysqldump -h %s -u %s -p%s %s > %s',
                    escapeshellarg($config['hostname']),
                    escapeshellarg($config['username']),
                    escapeshellarg($config['password']),
                    escapeshellarg($config['database']),
                    escapeshellarg($filePath)
                );
            }
            
            // 执行备份命令
            exec($command, $output, $returnVar);
            
            if ($returnVar !== 0) {
                throw new \Exception('数据库备份失败');
            }
            
            $successMsg = $type === 'order' ? '订单表备份成功' : '数据库完整备份成功';
            
            return json([
                'code' => 200,
                'msg' => $successMsg,
                'data' => [
                    'filename' => $filename,
                    'size' => $this->formatFileSize(filesize($filePath))
                ]
            ]);
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '备份失败: ' . $e->getMessage()]);
        }
    }

    // 修改获取备份文件列表方法，添加类型标识
    public function getBackupFiles()
    {
        try {
            $backupPath = root_path() . 'backup/';
            $files = [];
            
            if (is_dir($backupPath)) {
                $dirFiles = scandir($backupPath);
                foreach ($dirFiles as $file) {
                    if ($file != '.' && $file != '..' && pathinfo($file, PATHINFO_EXTENSION) == 'sql') {
                        $filePath = $backupPath . $file;
                        // 根据文件名前缀判断备份类型
                        $type = strpos($file, 'order_backup_') === 0 ? '订单表备份' : '完整备份';
                        $files[] = [
                            'filename' => $file,
                            'type' => $type,
                            'size' => $this->formatFileSize(filesize($filePath)),
                            'create_time' => date('Y-m-d H:i:s', filemtime($filePath))
                        ];
                    }
                }
                
                // 按创建时间倒序排序
                usort($files, function($a, $b) {
                    return strtotime($b['create_time']) - strtotime($a['create_time']);
                });
            }
            
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $files
            ]);
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '获取备份文件列表失败: ' . $e->getMessage()]);
        }
    }

    // 修改删除备份文件方法的文件名验证
    public function deleteBackupFile()
    {
        try {
            $filename = $this->request->post('filename', '');
            
            if (empty($filename) || !preg_match('/^(order|full)_backup_\d{4}-\d{2}-\d{2}_\d{6}\.sql$/', $filename)) {
                throw new \Exception('无效的文件名');
            }
            
            $filePath = root_path() . 'backup/' . $filename;
            
            if (!file_exists($filePath)) {
                throw new \Exception('文件不存在');
            }
            
            if (!unlink($filePath)) {
                throw new \Exception('文件删除失败');
            }
            
            return json([
                'code' => 200,
                'msg' => '删除成功'
            ]);
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '删除失败: ' . $e->getMessage()]);
        }
    }

    // 修改下载备份文件方法的文件名验证
    public function downloadBackupFile()
    {
        try {
            $filename = $this->request->get('filename', '');
            
            if (empty($filename) || !preg_match('/^(order|full)_backup_\d{4}-\d{2}-\d{2}_\d{6}\.sql$/', $filename)) {
                throw new \Exception('无效的文件名');
            }
            
            $filePath = root_path() . 'backup/' . $filename;
            
            if (!file_exists($filePath)) {
                throw new \Exception('文件不存在');
            }
            
            // 设置响应头
            header('Content-Type: application/octet-stream');
            header('Content-Disposition: attachment; filename=' . $filename);
            header('Content-Length: ' . filesize($filePath));
            
            // 读取文件并输出
            readfile($filePath);
            exit;
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '下载失败: ' . $e->getMessage()]);
        }
    }

    // 修改导入订单表方法
    public function importOrders()
    {
        try {
            if (!isset($_FILES['file'])) {
                throw new \Exception('请选择要导入的文件');
            }

            $file = $_FILES['file'];
            if ($file['error'] !== UPLOAD_ERR_OK) {
                throw new \Exception('文件上传失败');
            }

            // 验证文件类型
            if (pathinfo($file['name'], PATHINFO_EXTENSION) !== 'sql') {
                throw new \Exception('只能上传SQL文件');
            }

            // 读取SQL文件内容
            $sqlContent = file_get_contents($file['tmp_name']);
            if ($sqlContent === false) {
                throw new \Exception('读取文件失败');
            }

            // 解析INSERT语句，适配mysqldump格式
            if (!preg_match('/INSERT INTO [`\'"]?(?:[^`\'"\s]+_)?order[`\'"]?\s+(?:\([^)]+\)\s+)?VALUES\s*\((.*?)\);/is', $sqlContent, $matches)) {
                throw new \Exception('未找到有效的订单数据');
            }

            // 开始事务
            Db::startTrans();
            try {
                $addedCount = 0;
                $skippedCount = 0;
                $updatedCount = 0;

                // 分割多条记录
                $values = explode('),(', $matches[1]);
                
                foreach ($values as $value) {
                    // 清理括号和多余空格
                    $value = trim($value, '() ');
                    // 分割字段值
                    $fieldValues = $this->parseValues($value);
                    
                    if (empty($fieldValues)) {
                        error_log("Clearallinone: 导入订单 - 跳过空记录");
                        $skippedCount++;
                        continue;
                    }

                    // 记录一下特别的值，比如非数字的search_count
                    if (isset($fieldValues[30]) && !is_null($fieldValues[30]) && !is_numeric($fieldValues[30])) {
                        error_log("Clearallinone: 导入订单 - 发现非数字的search_count值: " . $fieldValues[30]);
                    }

                    try {
                        // 构建数据数组
                        $data = [
                            'id' => is_numeric($fieldValues[0]) ? intval($fieldValues[0]) : 0,
                            'trade_no' => $fieldValues[1],
                            'goods_id' => is_numeric($fieldValues[2]) ? intval($fieldValues[2]) : 0,
                            'goods_name' => $fieldValues[3],
                            'goods_price' => is_numeric($fieldValues[4]) ? floatval($fieldValues[4]) : 0,
                            'quantity' => is_numeric($fieldValues[5]) ? intval($fieldValues[5]) : 0,
                            'use_coupon' => is_numeric($fieldValues[6]) ? intval($fieldValues[6]) : 0,
                            'coupon_code' => $fieldValues[7],
                            'coupon_price' => is_numeric($fieldValues[8]) ? floatval($fieldValues[8]) : 0,
                            'original_amount' => is_numeric($fieldValues[9]) ? floatval($fieldValues[9]) : 0,
                            'total_amount' => is_numeric($fieldValues[10]) ? floatval($fieldValues[10]) : 0,
                            'refund_amount' => is_numeric($fieldValues[11]) ? floatval($fieldValues[11]) : 0,
                            'channel_id' => is_numeric($fieldValues[12]) ? intval($fieldValues[12]) : 0,
                            'channel_account_id' => is_numeric($fieldValues[13]) ? intval($fieldValues[13]) : 0,
                            'rate' => is_numeric($fieldValues[14]) ? floatval($fieldValues[14]) : 0,
                            'fee' => is_numeric($fieldValues[15]) ? floatval($fieldValues[15]) : 0,
                            'fee_payer' => is_numeric($fieldValues[16]) ? intval($fieldValues[16]) : 0,
                            'status' => is_numeric($fieldValues[17]) ? intval($fieldValues[17]) : 0,
                            'create_ip' => $fieldValues[18],
                            'create_time' => is_numeric($fieldValues[19]) ? intval($fieldValues[19]) : time(),
                            'transaction_id' => $fieldValues[20],
                            'user_id' => is_numeric($fieldValues[21]) ? intval($fieldValues[21]) : 0,
                            'success_time' => $fieldValues[22],
                            'sendout' => is_numeric($fieldValues[23]) ? intval($fieldValues[23]) : 0,
                            'reissue_sendout' => is_numeric($fieldValues[24]) ? intval($fieldValues[24]) : 0,
                            'buyer_value' => $fieldValues[25],
                            'parent_id' => is_numeric($fieldValues[26]) ? intval($fieldValues[26]) : 0,
                            'parent_amount' => is_numeric($fieldValues[27]) ? floatval($fieldValues[27]) : 0,
                            'parent_goods_id' => is_numeric($fieldValues[28]) ? intval($fieldValues[28]) : 0,
                            'contact' => $fieldValues[29],
                            'search_count' => is_numeric($fieldValues[30]) ? intval($fieldValues[30]) : 0,
                            'extend' => $fieldValues[31]
                        ];

                        // 查找是否存在相同订单
                        $existingOrder = Db::name('order')
                            ->where('id', $data['id'])
                            ->find();

                        if ($existingOrder) {
                            if ($data['create_time'] > $existingOrder['create_time']) {
                                Db::name('order')
                                    ->where('id', $data['id'])
                                    ->update($data);
                                $updatedCount++;
                                error_log("Clearallinone: 导入订单 - 更新订单ID: " . $data['id']);
                            } else {
                                $skippedCount++;
                                error_log("Clearallinone: 导入订单 - 跳过更早的订单ID: " . $data['id']);
                            }
                        } else {
                            Db::name('order')->insert($data);
                            $addedCount++;
                            error_log("Clearallinone: 导入订单 - 添加新订单ID: " . $data['id']);
                        }
                    } catch (\Exception $e) {
                        error_log("Clearallinone: 导入订单 - 处理记录错误: " . $e->getMessage());
                        $skippedCount++;
                        // 继续处理下一条记录，不中断导入
                        continue;
                    }
                }

                Db::commit();
                
                $message = sprintf(
                    "导入完成！新增 %d 条记录，更新 %d 条记录，跳过 %d 条重复记录",
                    $addedCount,
                    $updatedCount,
                    $skippedCount
                );

                return json([
                    'code' => 200,
                    'msg' => $message,
                    'data' => [
                        'added' => $addedCount,
                        'updated' => $updatedCount,
                        'skipped' => $skippedCount
                    ]
                ]);

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            error_log("Clearallinone: 导入备份文件失败: " . $e->getMessage());
            return json(['code' => 500, 'msg' => '导入失败: ' . $e->getMessage()]);
        }
    }

    // 添加解析VALUES的辅助方法
    private function parseValues($valuesString) 
    {
        $values = [];
        $current = '';
        $inString = false;
        $length = strlen($valuesString);
        
        for ($i = 0; $i < $length; $i++) {
            $char = $valuesString[$i];
            
            if ($char === '\'' && ($i === 0 || $valuesString[$i-1] !== '\\')) {
                $inString = !$inString;
                continue;
            }
            
            if ($char === ',' && !$inString) {
                // 检查并处理NULL值
                $trimmed = trim($current, '\'"');
                $values[] = ($trimmed === 'NULL') ? null : $trimmed;
                $current = '';
                continue;
            }
            
            $current .= $char;
        }
        
        // 添加最后一个值，同时处理NULL值
        $trimmed = trim($current, '\'"');
        $values[] = ($trimmed === 'NULL') ? null : $trimmed;
        
        return $values;
    }

    // 清理卡密方法
    public function clearCards()
    {
        try {
            $userId = $this->request->post('user_id/d', 0);
            $startDate = $this->request->post('start_date', '');
            $endDate = $this->request->post('end_date', '');
            
            // 清理所有表的计数器
            $totalDeleted = 0;
            $tablePrefix = 'goods_card_storage';
            
            // 获取最大的表后缀编号
            $tables = Db::query("SHOW TABLES LIKE '{$tablePrefix}_%'");
            $suffixes = [];
            
            foreach ($tables as $table) {
                $tableName = current($table);
                if (preg_match("/{$tablePrefix}_(\d+)$/", $tableName, $matches)) {
                    $suffixes[] = (int)$matches[1];
                }
            }
            
            // 如果没有找到任何表，至少处理_0表
            if (empty($suffixes)) {
                $suffixes = [0];
            }

            // 遍历所有卡密表进行清理
            foreach ($suffixes as $suffix) {
                $tableName = "{$tablePrefix}_{$suffix}";
                
                // 构建查询条件
                $query = Db::name($tableName);
                
                if ($userId > 0) {
                    $query = $query->where('user_id', $userId);
                }
                
                // 添加日期范围条件
                if (!empty($startDate)) {
                    $query = $query->where('create_time', '>=', strtotime($startDate));
                }
                if (!empty($endDate)) {
                    $query = $query->where('create_time', '<=', strtotime($endDate . ' 23:59:59'));
                }

                // 获取要删除的记录数
                $beforeCount = $query->count();
                $totalDeleted += $beforeCount;

                if ($beforeCount > 0) {
                    // 删除记录
                    $query->delete(true);
                }
            }

            if ($totalDeleted == 0) {
                $msg = $userId > 0 
                    ? "用户ID {$userId} 暂无卡密记录" 
                    : "暂无卡密记录";
                return json(['code' => 200, 'msg' => $msg]);
            }

            $message = sprintf(
                "%s清理完成！共删除 %d 条卡密记录",
                $userId > 0 ? "用户ID {$userId} 的" : "所有",
                $totalDeleted
            );

            // 记录日志
            error_log("Clearallinone: " . $message);

            return json([
                'code' => 200,
                'msg' => $message,
                'data' => [
                    'deleted_count' => $totalDeleted
                ]
            ]);

        } catch (\Exception $e) {
            error_log("Clearallinone: 清理卡密失败: " . $e->getMessage());
            return json(['code' => 500, 'msg' => '清理失败: ' . $e->getMessage()]);
        }
    }

    // 清理已删除卡密方法
    public function clearDeletedCards()
    {
        try {
            $userId = $this->request->post('user_id/d', 0);
            $startDate = $this->request->post('start_date', '');
            $endDate = $this->request->post('end_date', '');
            
            // 清理所有表的计数器
            $totalDeleted = 0;
            $tablePrefix = 'goods_card_storage';
            
            // 获取最大的表后缀编号
            $tables = Db::query("SHOW TABLES LIKE '{$tablePrefix}_%'");
            $suffixes = [];
            
            foreach ($tables as $table) {
                $tableName = current($table);
                if (preg_match("/{$tablePrefix}_(\d+)$/", $tableName, $matches)) {
                    $suffixes[] = (int)$matches[1];
                }
            }
            
            // 如果没有找到任何表，至少处理_0表
            if (empty($suffixes)) {
                $suffixes = [0];
            }

            // 遍历所有卡密表进行清理
            foreach ($suffixes as $suffix) {
                $tableName = "{$tablePrefix}_{$suffix}";
                
                // 构建查询条件
                $query = Db::name($tableName)
                    ->whereNotNull('delete_time');
                
                if ($userId > 0) {
                    $query = $query->where('user_id', $userId);
                }
                
                // 添加日期范围条件
                if (!empty($startDate)) {
                    $query = $query->where('create_time', '>=', strtotime($startDate));
                }
                if (!empty($endDate)) {
                    $query = $query->where('create_time', '<=', strtotime($endDate . ' 23:59:59'));
                }

                // 获取要删除的记录数
                $beforeCount = $query->count();
                $totalDeleted += $beforeCount;

                if ($beforeCount > 0) {
                    // 删除记录
                    $query->delete(true);
                }
            }

            if ($totalDeleted == 0) {
                $msg = $userId > 0 
                    ? "用户ID {$userId} 暂无已删除的卡密记录" 
                    : "暂无已删除的卡密记录";
                return json(['code' => 200, 'msg' => $msg]);
            }

            $message = sprintf(
                "%s清理完成！共删除 %d 条已删除的卡密记录",
                $userId > 0 ? "用户ID {$userId} 的" : "所有",
                $totalDeleted
            );

            // 记录日志
            error_log("Clearallinone: " . $message);

            return json([
                'code' => 200,
                'msg' => $message,
                'data' => [
                    'deleted_count' => $totalDeleted
                ]
            ]);

        } catch (\Exception $e) {
            error_log("Clearallinone: 清理已删除卡密失败: " . $e->getMessage());
            return json(['code' => 500, 'msg' => '清理失败: ' . $e->getMessage()]);
        }
    }

    // 保存卡密清理配置
    public function saveCardConfig()
    {
        try {
            $status = $this->request->post('status/d', 0);
            $executeTime = $this->request->post('executeTime', '00:00');
            $interval = $this->request->post('interval/d', 0); // 默认值改为0

            // 验证时间格式
            if (!preg_match('/^([01][0-9]|2[0-3]):([0-5][0-9])$/', $executeTime)) {
                return json(['code' => 0, 'msg' => '执行时间格式必须为 HH:mm']);
            }

            // 验证间隔参数
            if ($interval < 0 || $interval > 30) {
                return json(['code' => 0, 'msg' => '执行间隔必须在0-30天之间']);
            }

            // 使用 plugconf 保存配置
            try {
                plugconf("Clearallinone.card_status", intval($status));
                plugconf("Clearallinone.card_execute_time", strval($executeTime));
                plugconf("Clearallinone.card_interval", intval($interval)); // 确保保存原始值
            } catch (\Exception $e) {
                error_log("Clearallinone: 保存配置失败: " . $e->getMessage());
                throw new \Exception('配置保存失败');
            }

            return json(['code' => 1, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            error_log("Clearallinone: 保存卡密清理配置失败: " . $e->getMessage());
            return json(['code' => 0, 'msg' => '保存失败: ' . $e->getMessage()]);
        }
    }

    // 添加清理流水明细方法
    public function clearMoneyLogs()
    {
        try {
            $userId = $this->request->post('user_id/d', 0);
            $sourceTypes = $this->request->post('source_types/a', []);
            $startDate = $this->request->post('start_date', '');
            $endDate = $this->request->post('end_date', '');
            
            // 构建查询条件
            $query = Db::name('user_money_log');
            
            if ($userId > 0) {
                $query = $query->where('user_id', $userId);
            }
            
            // 添加流水类型条件
            if (!empty($sourceTypes)) {
                $query = $query->whereIn('source', $sourceTypes);
            }
            
            // 添加日期范围条件
            if (!empty($startDate)) {
                $query = $query->where('create_time', '>=', strtotime($startDate));
            }
            if (!empty($endDate)) {
                $query = $query->where('create_time', '<=', strtotime($endDate . ' 23:59:59'));
            }

            // 获取要删除的记录数
            $beforeCount = $query->count();

            if ($beforeCount == 0) {
                $msg = $userId > 0 
                    ? "用户ID {$userId} 暂无流水明细记录" 
                    : "暂无流水明细记录";
                if (!empty($sourceTypes)) {
                    $msg .= sprintf("（类型：%s）", implode('、', $sourceTypes));
                }
                if (!empty($startDate) || !empty($endDate)) {
                    $msg .= "（在指定日期范围内）";
                }
                return json(['code' => 200, 'msg' => $msg]);
            }

            // 删除记录
            $deleted = $query->delete();
            
            if ($deleted === false) {
                throw new \Exception('删除操作失败');
            }

            $message = sprintf(
                "%s清理完成！共删除 %d 条流水明细记录",
                $userId > 0 ? "用户ID {$userId} 的" : "所有用户",
                $beforeCount
            );
            if (!empty($sourceTypes)) {
                $message .= sprintf("（类型：%s）", implode('、', $sourceTypes));
            }
            if (!empty($startDate) || !empty($endDate)) {
                $message .= sprintf("（%s 至 %s）", $startDate ?: '开始', $endDate ?: '结束');
            }

            // 记录日志
            error_log("Clearallinone: " . $message);

            return json([
                'code' => 200,
                'msg' => $message,
                'data' => [
                    'deleted_count' => $beforeCount
                ]
            ]);

        } catch (\Exception $e) {
            error_log("Clearallinone: 清理流水明细失败: " . $e->getMessage());
            return json(['code' => 500, 'msg' => '清理失败: ' . $e->getMessage()]);
        }
    }

    // 清理商品销量方法
    public function clearGoodsSellCount()
    {
        try {
            $userId = $this->request->post('user_id/d', 0);
            
            // 构建查询条件
            $query = Db::name('goods')
                ->where('sell_count', '>', 0);
                
            if ($userId > 0) {
                $query = $query->where('user_id', $userId);
            }

            // 获取要重置的记录数
            $beforeCount = $query->count();

            if ($beforeCount == 0) {
                $msg = $userId > 0 
                    ? "用户ID {$userId} 暂无需要重置销量的商品" 
                    : "暂无需要重置销量的商品";
                return json(['code' => 200, 'msg' => $msg]);
            }

            // 重置销量为0
            $updated = $query->update(['sell_count' => 0]);
            
            if ($updated === false) {
                throw new \Exception('重置操作失败');
            }

            $message = sprintf(
                "%s销量重置完成！共重置 %d 个商品的销量",
                $userId > 0 ? "用户ID {$userId} 的" : "所有",
                $beforeCount
            );

            // 记录日志
            error_log("Clearallinone: " . $message);

            return json([
                'code' => 200,
                'msg' => $message,
                'data' => [
                    'reset_count' => $beforeCount
                ]
            ]);

        } catch (\Exception $e) {
            error_log("Clearallinone: 重置商品销量失败: " . $e->getMessage());
            return json(['code' => 500, 'msg' => '重置失败: ' . $e->getMessage()]);
        }
    }

    // 添加用户订单数清零方法
    public function clearUserSellCount()
    {
        try {
            $userId = $this->request->post('user_id/d', 0);
            
            // 构建查询条件
            $query = Db::name('user')
                ->where('sell_count', '>', 0);
                
            if ($userId > 0) {
                $query = $query->where('id', $userId);
            }

            // 获取要重置的记录数
            $beforeCount = $query->count();

            if ($beforeCount == 0) {
                $msg = $userId > 0 
                    ? "用户ID {$userId} 暂无需要重置订单数的记录" 
                    : "暂无需要重置订单数的用户";
                return json(['code' => 200, 'msg' => $msg]);
            }

            // 重置sell_count为0
            $updated = $query->update(['sell_count' => 0]);
            
            if ($updated === false) {
                throw new \Exception('重置操作失败');
            }

            $message = sprintf(
                "%s订单数重置完成！共重置 %d 个用户的订单数",
                $userId > 0 ? "用户ID {$userId} 的" : "所有",
                $beforeCount
            );

            // 记录日志
            error_log("Clearallinone: " . $message);

            return json([
                'code' => 200,
                'msg' => $message,
                'data' => [
                    'reset_count' => $beforeCount
                ]
            ]);

        } catch (\Exception $e) {
            error_log("Clearallinone: 重置用户订单数失败: " . $e->getMessage());
            return json(['code' => 500, 'msg' => '重置失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 获取数据库中所有与user_id相关的表
     * @return \think\response\Json
     */
    public function getUserDataTables()
    {
        try {
            // 获取当前数据库中的所有表
            $tables = Db::query('SHOW TABLES');
            $prefix = config('database.connections.mysql.prefix');
            $userTables = [];
            
            // 遍历所有表，寻找包含user_id字段的表
            foreach ($tables as $table) {
                $tableName = current($table);
                $realTableName = str_replace($prefix, '', $tableName);
                
                try {
                    // 获取表的字段信息
                    $fields = Db::name($realTableName)->getTableFields();
                    
                    // 检查是否包含user_id字段或id字段(对于user表)
                    if (in_array('user_id', $fields) || ($realTableName === 'user' && in_array('id', $fields))) {
                        // 获取表的记录数量
                        $count = Db::name($realTableName)->count();
                        
                        $fieldKey = in_array('user_id', $fields) ? 'user_id' : 'id';
                        
                        $userTables[] = [
                            'table_name' => $realTableName,
                            'real_table' => $tableName,
                            'field_key' => $fieldKey,
                            'record_count' => $count
                        ];
                    }
                } catch (\Exception $e) {
                    // 如果该表无法访问，跳过
                    continue;
                }
            }
            
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $userTables
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '获取数据表失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 清理指定用户的所有数据
     * @return \think\response\Json
     */
    public function cleanUserData()
    {
        try {
            $userId = $this->request->post('user_id/d', 0);
            $cleanTypes = $this->request->post('clean_types/a', []);
            $startDate = $this->request->post('start_date', '');
            $endDate = $this->request->post('end_date', '');
            
            // 如果没有提供用户ID，确认是否要处理所有用户
            if (empty($userId)) {
                return json([
                    'code' => 400,
                    'msg' => '未指定用户ID，不支持批量清理所有用户，请指定要清理的用户ID'
                ]);
            }
            
            // 检查用户是否存在 - 但即使不存在也继续清理
            $user = Db::name('user')->where('id', $userId)->find();
            $userExists = !empty($user);
            
            // 保存删除结果
            $deletedData = [];
            
            // 开始事务
            Db::startTrans();
            
            try {
                // 获取所有相关表
                $tables = $this->getUserRelatedTables();
                
                // 如果指定了清理类型，过滤表
                if (!empty($cleanTypes)) {
                    // 这里可以根据清理类型筛选出特定的表
                }
                
                // 循环删除数据
                foreach ($tables as $table) {
                    $tableName = $table['table_name'];
                    $fieldKey = $table['field_key'];
                    
                    // 构建查询条件
                    $query = Db::name($tableName)->where($fieldKey, $userId);
                    
                    // 如果有日期范围，添加日期条件
                    if (!empty($startDate) && !empty($endDate)) {
                        // 获取表的字段信息
                        $fields = Db::name($tableName)->getTableFields();
                        
                        // 检查表是否包含create_time字段
                        if (in_array('create_time', $fields)) {
                            $query->whereBetweenTime('create_time', $startDate, $endDate);
                        }
                    }
                    
                    // 执行删除操作
                    $count = $query->delete();
                    
                    // 记录删除的数据数量
                    if ($count > 0) {
                        $deletedData[$tableName] = $count;
                    }
                }
                
                // 提交事务
                Db::commit();
                
                // 构建返回消息
                $message = "成功清理用户 {$user['username']} (ID: {$userId}) 的数据。\n";
                $message .= "清理数据明细：\n";
                
                foreach ($deletedData as $table => $count) {
                    $message .= "- {$table}表: {$count}条记录\n";
                }
                
                return json([
                    'code' => 200,
                    'msg' => $message,
                    'data' => $deletedData
                ]);
                
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                throw $e;
            }
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '清理失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 获取与用户相关的所有表
     * @return array
     */
    private function getUserRelatedTables()
    {
        // 获取当前数据库中的所有表
        $tables = Db::query('SHOW TABLES');
        $prefix = config('database.connections.mysql.prefix');
        $userTables = [];
        
        // 遍历所有表，寻找包含user_id字段的表
        foreach ($tables as $table) {
            $tableName = current($table);
            $realTableName = str_replace($prefix, '', $tableName);
            
            try {
                // 获取表的字段信息
                $fields = Db::name($realTableName)->getTableFields();
                
                // 检查是否包含user_id字段或id字段(对于user表)
                if (in_array('user_id', $fields) || ($realTableName === 'user' && in_array('id', $fields))) {
                    $fieldKey = in_array('user_id', $fields) ? 'user_id' : 'id';
                    
                    $userTables[] = [
                        'table_name' => $realTableName,
                        'real_table' => $tableName,
                        'field_key' => $fieldKey
                    ];
                }
            } catch (\Exception $e) {
                // 如果该表无法访问，跳过
                continue;
            }
        }
        
        return $userTables;
    }
    
    /**
     * 查询所有表中与指定用户相关的数据
     * @return \think\response\Json
     */
    public function getUserRelatedData()
    {
        try {
            $userId = $this->request->post('user_id/d', 0);
            
            if (empty($userId)) {
                return json([
                    'code' => 400,
                    'msg' => '未指定用户ID'
                ]);
            }
            
            // 检查用户是否存在
            $user = Db::name('user')->where('id', $userId)->find();
            $formattedUser = [];
            
            if (!empty($user)) {
                // 格式化用户数据，确保所有必需字段都存在
                $formattedUser = [
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'regdate' => date('Y-m-d H:i:s', $user['create_time'] ?? time()),
                    'status' => $user['status'] ?? 0,
                    'create_time' => $user['create_time'] ?? time()
                ];
            } else {
                // 用户不存在，但仍然继续查询其他表
                $formattedUser = [
                    'id' => $userId,
                    'username' => '未知用户',
                    'regdate' => '未知',
                    'status' => -1,
                    'create_time' => 0,
                    'note' => '此用户已在user表中删除，但其他表中可能仍有关联数据'
                ];
            }
            
            $result = [];
            
            // 获取所有相关表
            $tables = $this->getUserRelatedTables();

            // 表名描述映射，添加表的中文名称
            $tableDesc = [
                'user' => '用户表',
                'user_login_log' => '登录日志',
                'user_money_log' => '资金流水',
                'user_message' => '用户消息',
                'order' => '订单表',
                'goods' => '商品表',
                'goods_card_storage_0' => '卡密存储0',
                'goods_card_storage_1' => '卡密存储1',
                'goods_card_storage_2' => '卡密存储2',
                'complaint' => '投诉表',
                'auto_unfreeze' => '自动解冻',
                'user_withdrawal' => '提现记录',
                'user_recharge' => '充值记录',
                'user_report' => '举报记录',
                'user_authentication' => '用户认证',
                'user_favorite' => '用户收藏',
                'user_balance_log' => '余额日志',
                'user_deposit_log' => '保证金日志',
                'user_operate_log' => '运营钱包日志',
                'goods_category' => '商品分类',
                'goods_stock_log' => '商品库存日志'
            ];
            
            // 查询每个表中的关联数据
            foreach ($tables as $table) {
                $tableName = $table['table_name'];
                $fieldKey = $table['field_key'];
                
                // 构建查询
                $count = Db::name($tableName)->where($fieldKey, $userId)->count();
                
                // 只记录有数据的表
                if ($count > 0) {
                    $result[$tableName] = [
                        'count' => $count,
                        'description' => isset($tableDesc[$tableName]) ? $tableDesc[$tableName] : ''
                    ];
                }
            }
            
            return json([
                'code' => 200,
                'msg' => empty($user) ? '用户在user表中不存在，但找到了关联数据' : '查询成功',
                'data' => [
                    'user' => $formattedUser,
                    'related_data' => $result,
                    'user_exists' => !empty($user)
                ]
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '查询失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 清理指定用户在单个表中的数据
     * @return \think\response\Json
     */
    public function cleanUserDataInTable()
    {
        try {
            $userId = $this->request->post('user_id/d', 0);
            $tableName = $this->request->post('table_name', '');
            
            if (empty($userId)) {
                return json([
                    'code' => 400,
                    'msg' => '未指定用户ID'
                ]);
            }
            
            if (empty($tableName)) {
                return json([
                    'code' => 400,
                    'msg' => '未指定要清理的表名'
                ]);
            }
            
            // 检查用户是否存在 - 但即使不存在也继续清理
            $user = Db::name('user')->where('id', $userId)->find();
            $userExists = !empty($user);
            
            // 检查表是否存在
            $prefix = config('database.connections.mysql.prefix');
            try {
                $fields = Db::name($tableName)->getTableFields();
            } catch (\Exception $e) {
                return json([
                    'code' => 404,
                    'msg' => '指定的表不存在或无法访问'
                ]);
            }
            
            // 确定关联字段
            $fieldKey = in_array('user_id', $fields) ? 'user_id' : (($tableName === 'user' && in_array('id', $fields)) ? 'id' : '');
            
            if (empty($fieldKey)) {
                return json([
                    'code' => 400,
                    'msg' => '指定的表中没有用户ID关联字段'
                ]);
            }
            
            // 查询要删除的记录数
            $count = Db::name($tableName)->where($fieldKey, $userId)->count();
            
            if ($count === 0) {
                return json([
                    'code' => 200,
                    'msg' => '没有找到需要清理的数据',
                    'data' => [
                        'table' => $tableName,
                        'deleted' => 0
                    ]
                ]);
            }
            
            // 执行删除操作
            $deleted = Db::name($tableName)->where($fieldKey, $userId)->delete();
            
            if ($deleted === false) {
                throw new \Exception('删除记录失败');
            }
            
            $message = sprintf(
                "成功从表 %s 中删除了 %d 条与用户ID %d 相关的数据",
                $tableName,
                $deleted,
                $userId
            );
            
            // 记录日志
            error_log("Clearallinone: " . $message);
            
            return json([
                'code' => 200,
                'msg' => $message,
                'data' => [
                    'table' => $tableName,
                    'deleted' => $deleted
                ]
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '清理失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 清理指定用户的所有数据
     * @return \think\response\Json
     */
    public function clearUserAllData()
    {
        try {
            $userId = $this->request->post('user_id/d', 0);
            
            if (empty($userId)) {
                return json([
                    'code' => 400,
                    'msg' => '未指定用户ID'
                ]);
            }
            
            // 检查用户是否存在 - 但即使不存在也继续清理
            $user = Db::name('user')->where('id', $userId)->find();
            $userExists = !empty($user);
            $userInfo = $userExists ? 
                "{$user['username']} (ID: {$userId})" : 
                "ID为 {$userId} 的用户(已从user表删除)";
            
            try {
                // 开始事务
                Db::startTrans();
                
                // 获取所有相关表
                $tables = $this->getUserRelatedTables();
                $deletedData = [];
                
                // 遍历表进行清理，按顺序处理以避免外键约束问题
                foreach ($tables as $table) {
                    $tableName = $table['table_name'];
                    $fieldKey = $table['field_key'];
                    
                    // 获取表中与该用户关联的记录数
                    $count = Db::name($tableName)->where($fieldKey, $userId)->count();
                    
                    if ($count > 0) {
                        // 执行删除
                        $deleted = Db::name($tableName)->where($fieldKey, $userId)->delete();
                        if ($deleted > 0) {
                            $deletedData[] = [
                                'table' => $tableName,
                                'count' => $deleted
                            ];
                        }
                    }
                }
                
                // 提交事务
                Db::commit();
                
                $totalDeleted = array_sum(array_column($deletedData, 'count'));
                $message = "成功清理 {$userInfo} 的所有数据，共删除 {$totalDeleted} 条记录";
                
                return json([
                    'code' => 200,
                    'msg' => $message,
                    'data' => $deletedData
                ]);
                
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                throw $e;
            }
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '清理失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 清理已删除用户关联数据
     * @return \think\response\Json
     */
    public function clearDeletedUserData()
    {
        try {
            $userId = $this->request->post('user_id/d', 0);
            
            if (empty($userId)) {
                return json([
                    'code' => 400,
                    'msg' => '未指定用户ID'
                ]);
            }
            
            // 检查用户是否存在 - 我们只处理不存在的用户
            $user = Db::name('user')->where('id', $userId)->find();
            
            // 如果用户存在，返回错误信息
            if (!empty($user)) {
                return json([
                    'code' => 400,
                    'msg' => '指定的用户ID在用户表中仍然存在，请使用普通的清理功能'
                ]);
            }
            
            try {
                // 开始事务
                Db::startTrans();
                
                // 获取所有相关表
                $tables = $this->getUserRelatedTables();
                $deletedData = [];
                $totalRecords = 0;
                
                // 遍历表进行清理，按顺序处理以避免外键约束问题
                foreach ($tables as $table) {
                    $tableName = $table['table_name'];
                    $fieldKey = $table['field_key'];
                    
                    // 跳过user表 - 因为我们只处理其他表中与已删除用户相关的数据
                    if ($tableName === 'user') {
                        continue;
                    }
                    
                    // 获取表中与该用户关联的记录数
                    $count = Db::name($tableName)->where($fieldKey, $userId)->count();
                    
                    if ($count > 0) {
                        // 执行删除
                        $deleted = Db::name($tableName)->where($fieldKey, $userId)->delete();
                        if ($deleted > 0) {
                            $deletedData[] = [
                                'table' => $tableName,
                                'count' => $deleted
                            ];
                            $totalRecords += $deleted;
                        }
                    }
                }
                
                // 提交事务
                Db::commit();
                
                if (empty($deletedData)) {
                    return json([
                        'code' => 200,
                        'msg' => "用户ID {$userId} 没有需要清理的关联数据",
                        'data' => []
                    ]);
                }
                
                return json([
                    'code' => 200,
                    'msg' => "成功清理用户ID {$userId} 的所有关联数据，共删除 {$totalRecords} 条记录",
                    'data' => $deletedData
                ]);
                
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                throw $e;
            }
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '清理失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 清理所有已删除用户的关联数据
     * @return \think\response\Json
     */
    public function clearAllDeletedUsersData()
    {
        try {
            try {
                // 开始事务
                Db::startTrans();
                
                // 获取所有相关表
                $tables = $this->getUserRelatedTables();
                $deletedData = [];
                $totalRecords = 0;
                $deletedUsers = [];
                
                // 首先找到所有可能存在"孤儿数据"的用户ID
                $orphanedUserIds = [];
                
                foreach ($tables as $table) {
                    $tableName = $table['table_name'];
                    $fieldKey = $table['field_key'];
                    
                    // 跳过user表
                    if ($tableName === 'user') {
                        continue;
                    }
                    
                    // 查询所有包含user_id的表
                    if ($fieldKey === 'user_id') {
                        // 查找该表中的所有不同用户ID
                        $userIds = Db::name($tableName)
                            ->field('DISTINCT user_id as id')
                            ->select()
                            ->toArray();
                        
                        if (!empty($userIds)) {
                            foreach ($userIds as $userIdObj) {
                                if (isset($userIdObj['id']) && !empty($userIdObj['id'])) {
                                    $orphanedUserIds[] = $userIdObj['id'];
                                }
                            }
                        }
                    }
                }
                
                // 数组去重
                $orphanedUserIds = array_unique($orphanedUserIds);
                
                if (empty($orphanedUserIds)) {
                    return json([
                        'code' => 200,
                        'msg' => '没有找到已删除用户的关联数据',
                        'data' => []
                    ]);
                }
                
                // 检查哪些用户ID在user表中不存在
                $existingUserIds = Db::name('user')
                    ->whereIn('id', $orphanedUserIds)
                    ->column('id');
                
                // 找出不存在于user表中的用户ID
                $deletedUserIds = array_diff($orphanedUserIds, $existingUserIds);
                
                if (empty($deletedUserIds)) {
                    return json([
                        'code' => 200,
                        'msg' => '没有找到已删除用户的关联数据',
                        'data' => []
                    ]);
                }
                
                // 清理每个已删除用户的关联数据
                foreach ($deletedUserIds as $userId) {
                    $userDeletedData = [];
                    $userTotalDeleted = 0;
                    
                    foreach ($tables as $table) {
                        $tableName = $table['table_name'];
                        $fieldKey = $table['field_key'];
                        
                        // 跳过user表
                        if ($tableName === 'user') {
                            continue;
                        }
                        
                        // 处理user_id字段
                        if ($fieldKey === 'user_id') {
                            $count = Db::name($tableName)->where('user_id', $userId)->count();
                            
                            if ($count > 0) {
                                $deleted = Db::name($tableName)->where('user_id', $userId)->delete();
                                if ($deleted > 0) {
                                    $userDeletedData[] = [
                                        'table' => $tableName,
                                        'count' => $deleted
                                    ];
                                    $userTotalDeleted += $deleted;
                                }
                            }
                        }
                    }
                    
                    if ($userTotalDeleted > 0) {
                        $deletedUsers[] = [
                            'user_id' => $userId,
                            'deleted_records' => $userTotalDeleted,
                            'tables' => $userDeletedData
                        ];
                        $totalRecords += $userTotalDeleted;
                    }
                }
                
                // 提交事务
                Db::commit();
                
                if (empty($deletedUsers)) {
                    return json([
                        'code' => 200,
                        'msg' => '没有找到需要清理的已删除用户数据',
                        'data' => []
                    ]);
                }
                
                return json([
                    'code' => 200,
                    'msg' => "成功清理了 " . count($deletedUsers) . " 个已删除用户的关联数据，共删除 {$totalRecords} 条记录",
                    'data' => [
                        'deleted_users_count' => count($deletedUsers),
                        'total_records' => $totalRecords,
                        'details' => $deletedUsers
                    ]
                ]);
                
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                throw $e;
            }
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '清理失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取所有已删除但其他表中有关联数据的用户ID列表
     * @return \think\response\Json
     */
    public function getDeletedUserIds()
    {
        try {
            // 获取所有相关表
            $tables = $this->getUserRelatedTables();
            
            // 首先找到所有可能存在"孤儿数据"的用户ID
            $orphanedUserIds = [];
            
            foreach ($tables as $table) {
                $tableName = $table['table_name'];
                $fieldKey = $table['field_key'];
                
                // 跳过user表
                if ($tableName === 'user') {
                    continue;
                }
                
                // 查询所有包含user_id的表
                if ($fieldKey === 'user_id') {
                    // 查找该表中的所有不同用户ID
                    $userIds = Db::name($tableName)
                        ->field('DISTINCT user_id as id')
                        ->select()
                        ->toArray();
                    
                    if (!empty($userIds)) {
                        foreach ($userIds as $userIdObj) {
                            if (isset($userIdObj['id']) && !empty($userIdObj['id'])) {
                                $orphanedUserIds[] = $userIdObj['id'];
                            }
                        }
                    }
                }
            }
            
            // 数组去重
            $orphanedUserIds = array_unique($orphanedUserIds);
            
            if (empty($orphanedUserIds)) {
                return json([
                    'code' => 200,
                    'msg' => '没有找到已删除用户的关联数据',
                    'data' => [
                        'userIds' => []
                    ]
                ]);
            }
            
            // 检查哪些用户ID在user表中不存在
            $existingUserIds = Db::name('user')
                ->whereIn('id', $orphanedUserIds)
                ->column('id');
            
            // 找出不存在于user表中的用户ID
            $deletedUserIds = array_diff($orphanedUserIds, $existingUserIds);
            
            if (empty($deletedUserIds)) {
                return json([
                    'code' => 200,
                    'msg' => '没有找到已删除用户的关联数据',
                    'data' => [
                        'userIds' => []
                    ]
                ]);
            }
            
            return json([
                'code' => 200,
                'msg' => '成功获取已删除用户ID列表',
                'data' => [
                    'total' => count($deletedUserIds),
                    'userIds' => array_values($deletedUserIds)
                ]
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '获取已删除用户ID失败: ' . $e->getMessage()
            ]);
        }
    }

    // 通过文件名直接导入备份文件
    public function importBackupFile()
    {
        try {
            $filename = $this->request->post('filename', '');
            
            if (empty($filename) || !preg_match('/^order_backup_\d{4}-\d{2}-\d{2}_\d{6}\.sql$/', $filename)) {
                throw new \Exception('无效的文件名或非订单备份文件');
            }
            
            $filePath = root_path() . 'backup/' . $filename;
            
            if (!file_exists($filePath)) {
                throw new \Exception('文件不存在');
            }

            // 读取SQL文件内容
            $sqlContent = file_get_contents($filePath);
            if ($sqlContent === false) {
                throw new \Exception('读取文件失败');
            }

            // 解析INSERT语句，适配mysqldump格式
            if (!preg_match('/INSERT INTO [`\'"]?(?:[^`\'"\s]+_)?order[`\'"]?\s+(?:\([^)]+\)\s+)?VALUES\s*\((.*?)\);/is', $sqlContent, $matches)) {
                throw new \Exception('未找到有效的订单数据');
            }

            // 开始事务
            Db::startTrans();
            try {
                $addedCount = 0;
                $skippedCount = 0;
                $updatedCount = 0;

                // 分割多条记录
                $values = explode('),(', $matches[1]);
                
                foreach ($values as $value) {
                    // 清理括号和多余空格
                    $value = trim($value, '() ');
                    // 分割字段值
                    $fieldValues = $this->parseValues($value);
                    
                    if (empty($fieldValues)) {
                        error_log("Clearallinone: 导入订单 - 跳过空记录");
                        $skippedCount++;
                        continue;
                    }

                    // 记录一下特别的值，比如非数字的search_count
                    if (isset($fieldValues[30]) && !is_null($fieldValues[30]) && !is_numeric($fieldValues[30])) {
                        error_log("Clearallinone: 导入订单 - 发现非数字的search_count值: " . $fieldValues[30]);
                    }

                    try {
                        // 构建数据数组
                        $data = [
                            'id' => is_numeric($fieldValues[0]) ? intval($fieldValues[0]) : 0,
                            'trade_no' => $fieldValues[1],
                            'goods_id' => is_numeric($fieldValues[2]) ? intval($fieldValues[2]) : 0,
                            'goods_name' => $fieldValues[3],
                            'goods_price' => is_numeric($fieldValues[4]) ? floatval($fieldValues[4]) : 0,
                            'quantity' => is_numeric($fieldValues[5]) ? intval($fieldValues[5]) : 0,
                            'use_coupon' => is_numeric($fieldValues[6]) ? intval($fieldValues[6]) : 0,
                            'coupon_code' => $fieldValues[7],
                            'coupon_price' => is_numeric($fieldValues[8]) ? floatval($fieldValues[8]) : 0,
                            'original_amount' => is_numeric($fieldValues[9]) ? floatval($fieldValues[9]) : 0,
                            'total_amount' => is_numeric($fieldValues[10]) ? floatval($fieldValues[10]) : 0,
                            'refund_amount' => is_numeric($fieldValues[11]) ? floatval($fieldValues[11]) : 0,
                            'channel_id' => is_numeric($fieldValues[12]) ? intval($fieldValues[12]) : 0,
                            'channel_account_id' => is_numeric($fieldValues[13]) ? intval($fieldValues[13]) : 0,
                            'rate' => is_numeric($fieldValues[14]) ? floatval($fieldValues[14]) : 0,
                            'fee' => is_numeric($fieldValues[15]) ? floatval($fieldValues[15]) : 0,
                            'fee_payer' => is_numeric($fieldValues[16]) ? intval($fieldValues[16]) : 0,
                            'status' => is_numeric($fieldValues[17]) ? intval($fieldValues[17]) : 0,
                            'create_ip' => $fieldValues[18],
                            'create_time' => is_numeric($fieldValues[19]) ? intval($fieldValues[19]) : time(),
                            'transaction_id' => $fieldValues[20],
                            'user_id' => is_numeric($fieldValues[21]) ? intval($fieldValues[21]) : 0,
                            'success_time' => $fieldValues[22],
                            'sendout' => is_numeric($fieldValues[23]) ? intval($fieldValues[23]) : 0,
                            'reissue_sendout' => is_numeric($fieldValues[24]) ? intval($fieldValues[24]) : 0,
                            'buyer_value' => $fieldValues[25],
                            'parent_id' => is_numeric($fieldValues[26]) ? intval($fieldValues[26]) : 0,
                            'parent_amount' => is_numeric($fieldValues[27]) ? floatval($fieldValues[27]) : 0,
                            'parent_goods_id' => is_numeric($fieldValues[28]) ? intval($fieldValues[28]) : 0,
                            'contact' => $fieldValues[29],
                            'search_count' => is_numeric($fieldValues[30]) ? intval($fieldValues[30]) : 0,
                            'extend' => $fieldValues[31]
                        ];

                        // 查找是否存在相同订单
                        $existingOrder = Db::name('order')
                            ->where('id', $data['id'])
                            ->find();

                        if ($existingOrder) {
                            if ($data['create_time'] > $existingOrder['create_time']) {
                                Db::name('order')
                                    ->where('id', $data['id'])
                                    ->update($data);
                                $updatedCount++;
                                error_log("Clearallinone: 导入订单 - 更新订单ID: " . $data['id']);
                            } else {
                                $skippedCount++;
                                error_log("Clearallinone: 导入订单 - 跳过更早的订单ID: " . $data['id']);
                            }
                        } else {
                            Db::name('order')->insert($data);
                            $addedCount++;
                            error_log("Clearallinone: 导入订单 - 添加新订单ID: " . $data['id']);
                        }
                    } catch (\Exception $e) {
                        error_log("Clearallinone: 导入订单 - 处理记录错误: " . $e->getMessage());
                        $skippedCount++;
                        // 继续处理下一条记录，不中断导入
                        continue;
                    }
                }

                Db::commit();
                
                $message = sprintf(
                    "导入成功！从文件 %s 中新增 %d 条记录，更新 %d 条记录，跳过 %d 条重复记录",
                    $filename,
                    $addedCount,
                    $updatedCount,
                    $skippedCount
                );

                error_log("Clearallinone: " . $message);

                return json([
                    'code' => 200,
                    'msg' => $message,
                    'data' => [
                        'added' => $addedCount,
                        'updated' => $updatedCount,
                        'skipped' => $skippedCount
                    ]
                ]);

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            error_log("Clearallinone: 导入备份文件失败: " . $e->getMessage());
            return json(['code' => 500, 'msg' => '导入失败: ' . $e->getMessage()]);
        }
    }
} 