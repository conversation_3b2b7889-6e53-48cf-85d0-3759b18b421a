<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>抽奖大转盘</title>
    <style>
        /* 修改背景为华丽的渐变 */
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            overflow-x: hidden;
            min-height: 450px;
            position: relative;
        }

        /* 背景渐变动画 */
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* 添加星空背景效果 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(2px 2px at 20px 30px, #eee, transparent),
                radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
                radial-gradient(1px 1px at 90px 40px, #fff, transparent),
                radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
                radial-gradient(2px 2px at 160px 30px, #fff, transparent);
            background-repeat: repeat;
            background-size: 200px 100px;
            animation: sparkle 20s linear infinite;
            z-index: -1;
            opacity: 0.6;
        }

        @keyframes sparkle {
            from { transform: translateX(0); }
            to { transform: translateX(200px); }
        }

        /* 加载动画 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.9), rgba(118, 75, 162, 0.9));
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.5s ease;
        }

        .loading-spinner {
            width: 80px;
            height: 80px;
            border: 8px solid rgba(255, 255, 255, 0.3);
            border-top: 8px solid #FFD700;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            color: white;
            font-size: 18px;
            margin-top: 20px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .sakura {
            position: fixed;
            pointer-events: none;
            z-index: 0;
        }

        /* 粒子效果样式 */
        .particle {
            position: fixed;
            pointer-events: none;
            z-index: 1;
            border-radius: 50%;
        }

        /* 烟花效果样式 */
        .firework {
            position: fixed;
            pointer-events: none;
            z-index: 5;
            border-radius: 50%;
        }

        /* 添加转盘旋转时的特效 */
        .turntable.spinning::before {
            content: '';
            position: absolute;
            top: -20px;
            left: -20px;
            right: -20px;
            bottom: -20px;
            border-radius: 50%;
            background: conic-gradient(
                from 0deg,
                transparent,
                rgba(255, 215, 0, 0.3),
                transparent,
                rgba(255, 165, 0, 0.3),
                transparent,
                rgba(255, 77, 77, 0.3),
                transparent
            );
            animation: spinGlow 1s linear infinite;
            z-index: -1;
        }

        @keyframes spinGlow {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* 修改卡片容器样式 */
        .lottery-container {
            background-color: transparent;
            box-shadow: none;
            max-width: 100%;
            padding: 10px 5px;  /* 减少左右内边距 */
            margin: 0;
        }

        /* 修改转盘容器样式 */
        .turntable {
            position: relative;
            width: 400px;
            height: 400px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85));
            border-radius: 50%;
            box-shadow:
                0 0 0 8px rgba(255, 215, 0, 0.3),
                0 0 0 16px rgba(255, 215, 0, 0.2),
                0 0 0 24px rgba(255, 215, 0, 0.1),
                0 20px 40px rgba(0, 0, 0, 0.2),
                inset 0 0 20px rgba(255, 215, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 4px solid #FFD700;
            padding: 0;
            transition: all 0.3s ease;
            animation: turntableGlow 3s ease-in-out infinite alternate;
            flex-shrink: 0;
        }

        /* 转盘发光动画 */
        @keyframes turntableGlow {
            0% {
                box-shadow:
                    0 0 0 8px rgba(255, 215, 0, 0.3),
                    0 0 0 16px rgba(255, 215, 0, 0.2),
                    0 0 0 24px rgba(255, 215, 0, 0.1),
                    0 20px 40px rgba(0, 0, 0, 0.2),
                    inset 0 0 20px rgba(255, 215, 0, 0.1);
            }
            100% {
                box-shadow:
                    0 0 0 12px rgba(255, 215, 0, 0.4),
                    0 0 0 20px rgba(255, 215, 0, 0.3),
                    0 0 0 28px rgba(255, 215, 0, 0.2),
                    0 25px 50px rgba(0, 0, 0, 0.3),
                    inset 0 0 30px rgba(255, 215, 0, 0.2);
            }
        }

        .turntable:hover {
            transform: scale(1.02);
        }

        /* 修改转盘画布样式 */
        .turntable-canvas {
            position: relative;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            transition: none;
            z-index: 1;
            filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.3));
        }

        .turntable-canvas.rotating {
            transition: transform 4s cubic-bezier(0.32, 0.96, 0.28, 1);
            filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.6));
        }

        /* 添加转盘边缘装饰 */
        .turntable::after {
            content: '';
            position: absolute;
            top: -8px;
            left: -8px;
            right: -8px;
            bottom: -8px;
            border-radius: 50%;
            background: conic-gradient(
                from 0deg,
                #FFD700,
                #FFA500,
                #FF6B6B,
                #FF4D4D,
                #FFD700
            );
            z-index: -1;
            animation: borderRotate 10s linear infinite;
        }

        @keyframes borderRotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* 修改指针容器和指针样式 */
        .pointer-container {
            position: absolute;
            top: -20px; /* 减小上边距 */
            left: 50%;
            transform: translateX(-50%);
            z-index: 3;
            width: 40px; /* 减小宽度 */
            height: 50px; /* 减小高度 */
        }

        /* 指针主体 */
        .pointer {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 50px;
            background: linear-gradient(135deg, #FFD700, #FFA500, #FF6B6B);
            clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
            box-shadow:
                0 4px 15px rgba(255, 77, 77, 0.4),
                0 0 20px rgba(255, 215, 0, 0.6),
                inset 0 2px 4px rgba(255, 255, 255, 0.3);
            opacity: 0.95;
            animation: pointerPulse 2s ease-in-out infinite;
        }

        /* 指针脉冲动画 */
        @keyframes pointerPulse {
            0%, 100% {
                transform: translateX(-50%) scale(1);
                filter: brightness(1);
            }
            50% {
                transform: translateX(-50%) scale(1.05);
                filter: brightness(1.2);
            }
        }

        /* 指针顶部装饰 */
        .pointer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 30px;
            background: linear-gradient(135deg, #FFD700, #FFA500);
            border-radius: 50%;
            box-shadow: 
                0 2px 4px rgba(0, 0, 0, 0.2),
                inset -2px -2px 4px rgba(0, 0, 0, 0.1),
                inset 2px 2px 4px rgba(255, 255, 255, 0.5);
        }

        /* 指针中心圆点 */
        .pointer::after {
            content: '';
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 12px;
            height: 12px;
            background: #fff;
            border-radius: 50%;
            box-shadow: 
                0 2px 4px rgba(0, 0, 0, 0.2),
                inset 0 0 2px rgba(0, 0, 0, 0.1);
        }

        /* 指针光泽效果 */
        .pointer-shine {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 100%;
            background: linear-gradient(
                90deg,
                transparent 0%,
                rgba(255, 255, 255, 0.2) 50%,
                transparent 100%
            );
        }

        /* 指针悬停效果 */
        .pointer:hover {
            filter: brightness(1.1);
        }

        /* 指针点击效果 */
        .pointer:active {
            transform: translateX(-50%) scale(0.98);
        }

        /* 转盘容器布局 */
        .turntable-container {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 40px;
            margin: 50px auto 20px;
            max-width: 800px;
        }

        /* 修改结果显示样式 - 移到转盘右边 */
        .result-display {
            position: relative;
            min-width: 200px;
            min-height: 100px;
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.95), rgba(255, 165, 0, 0.95));
            color: #FF4D4D;
            padding: 20px 30px;
            border-radius: 25px;
            font-size: 18px;
            font-weight: bold;
            box-shadow:
                0 0 0 3px rgba(255, 255, 255, 0.8),
                0 8px 25px rgba(255, 215, 0, 0.4),
                inset 0 2px 8px rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(15px);
            border: 3px solid rgba(255, 255, 255, 0.9);
            opacity: 0;
            transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            z-index: 10;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            animation: resultGlow 2s ease-in-out infinite alternate;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .result-display::before {
            content: '🎉 恭喜获得 🎉';
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 14px;
            color: #FFD700;
            background: rgba(255, 255, 255, 0.9);
            padding: 4px 12px;
            border-radius: 12px;
            white-space: nowrap;
        }

        /* 结果显示发光动画 */
        @keyframes resultGlow {
            0% {
                box-shadow:
                    0 0 0 3px rgba(255, 255, 255, 0.8),
                    0 8px 25px rgba(255, 215, 0, 0.4),
                    inset 0 2px 8px rgba(255, 255, 255, 0.3);
            }
            100% {
                box-shadow:
                    0 0 0 5px rgba(255, 255, 255, 0.9),
                    0 12px 35px rgba(255, 215, 0, 0.6),
                    inset 0 2px 12px rgba(255, 255, 255, 0.4);
            }
        }

        .result-display.show {
            opacity: 1;
            transform: scale(1.05);
            animation-duration: 1s;
        }

        /* 移动端适配结果显示 */
        @media screen and (max-width: 768px) {
            .turntable-container {
                flex-direction: column;
                gap: 20px;
                margin: 20px auto;
            }

            .result-display {
                min-width: 280px;
                min-height: 80px;
                padding: 15px 20px;
                font-size: 16px;
            }

            .result-display::before {
                font-size: 12px;
                padding: 3px 8px;
            }
        }

        /* 修改信息展示区域样式 */
        .lottery-info {
            text-align: center;
            color: #fff;
            margin-bottom: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .lottery-info > div {
            margin: 8px 0;
            font-size: 16px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            padding: 5px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .lottery-info > div:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .time-message {
            color: #ffeb3b;
        }

        .end-time {
            margin-left: 10px;
        }

        /* 修改按钮样式 */
        .lottery-btn .el-button--primary {
            background: linear-gradient(135deg, #FFD700, #FFA500, #FF6B6B);
            border: none;
            border-radius: 25px;
            padding: 15px 35px;
            font-size: 18px;
            color: #FFF;
            font-weight: bold;
            box-shadow:
                0 0 0 3px rgba(255, 255, 255, 0.3),
                0 8px 25px rgba(255, 77, 77, 0.4);
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .lottery-btn .el-button--primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .lottery-btn .el-button--primary:hover::before {
            left: 100%;
        }

        .lottery-btn .el-button--primary:hover {
            transform: translateY(-3px);
            box-shadow:
                0 0 0 4px rgba(255, 255, 255, 0.4),
                0 12px 35px rgba(255, 77, 77, 0.6);
        }

        /* 修改标签页样式 */
        .lottery-tabs .el-tabs__nav-wrap::after {
            display: none;
        }

        .lottery-tabs .el-tabs__item {
            color: rgba(255, 255, 255, 0.8);
        }

        .lottery-tabs .el-tabs__item.is-active {
            color: #fff;
        }

        .lottery-tabs .el-tabs__active-bar {
            background-color: #fff;
        }

        /* 修改奖品列表和记录样式 */
        .prize-list, .lottery-records {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85));
            border-radius: 20px;
            margin-top: 20px;
            box-shadow:
                0 0 0 2px rgba(255, 215, 0, 0.3),
                0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(15px);
            border: 2px solid rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }

        .prize-list:hover, .lottery-records:hover {
            transform: translateY(-5px);
            box-shadow:
                0 0 0 3px rgba(255, 215, 0, 0.4),
                0 15px 40px rgba(0, 0, 0, 0.15);
        }

        /* 信息卡片样式 */
        .lottery-info {
            margin-bottom: 20px;
            text-align: center;
        }

        /* 按钮容器样式 */
        .lottery-btn {
            display: none;
        }

        /* 奖品列表样式 */
        .prize-list {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .prize-list h3 {
            color: #333;
            font-size: 18px;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .prize-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            margin: 10px 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
            border-radius: 12px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 215, 0, 0.2);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .prize-item:hover {
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1));
            transform: translateX(10px);
            box-shadow: 0 6px 20px rgba(255, 215, 0, 0.2);
            border-color: rgba(255, 215, 0, 0.4);
        }

        .prize-name {
            color: #333;
            font-size: 16px;
        }

        .prize-probability {
            color: #909399;
            font-size: 14px;
        }

        /* 中奖记录样式 */
        .lottery-records {
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            margin-top: 20px;
            padding: 20px;
        }

        .records-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .record-item {
            padding: 15px;
            border-bottom: 1px solid #ebeef5;
        }

        .record-item:last-child {
            border-bottom: none;
        }

        .record-prize {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .prize-name {
            margin-right: 8px;
        }

        .record-time {
            font-size: 12px;
            color: #909399;
        }

        .no-records {
            text-align: center;
            color: #909399;
            padding: 20px;
        }

        /* 表单项提示信息 */
        .el-form-item-msg {
            margin-top: 8px;
            color: #666;
        }

        /* 导航菜单样式 */
        .lottery-tabs {
            margin-top: 20px;
        }

        .lottery-tabs .el-tabs__content {
            padding: 20px 0;
        }

        /* 调整奖品列表和记录的间距 */
        .prize-list,
        .lottery-records {
            margin-top: 0;
        }

        /* 适配不同尺寸的移动端屏幕 */
        @media screen and (max-width: 768px) {
            .turntable {
                width: 300px;
                height: 300px;
            }

            .lottery-info {
                margin: 10px;
                padding: 15px;
            }

            .lottery-info > div {
                font-size: 14px;
                margin: 6px 0;
            }

            /* 移动端转盘容器调整 */
            .turntable-container {
                flex-direction: column;
                gap: 20px;
                margin: 20px auto;
                padding: 0 10px;
            }

            .result-display {
                min-width: 250px;
                min-height: 70px;
                padding: 15px 20px;
                font-size: 16px;
            }

            .result-display::before {
                font-size: 12px;
                padding: 3px 8px;
                top: -12px;
            }

            /* 调整中心按钮大小 */
            .turntable-center {
                width: 50px;
                height: 50px;
            }

            .turntable-center-text {
                font-size: 14px;
            }

            /* 调整指针大小 */
            .pointer-container {
                top: -18px;
                width: 36px;
                height: 45px;
            }

            .pointer {
                width: 36px;
                height: 45px;
            }

            /* 调整结果显示位置 */
            .result-display {
                top: 10px;
                left: 10px;
                font-size: 12px;
                padding: 5px 14px;
            }
        }

        /* 针对中等尺寸手机屏幕 */
        @media screen and (min-width: 375px) and (max-width: 767px) {
            .turntable {
                width: 260px;
                height: 260px;
            }
        }

        /* 针对大尺寸手机屏幕 */
        @media screen and (min-width: 414px) and (max-width: 767px) {
            .turntable {
                width: 280px;
                height: 280px;
            }
        }

        /* 针对特小屏幕设备 */
        @media screen and (max-width: 320px) {
            .turntable {
                width: 220px;
                height: 220px;
            }

            .turntable-center {
                width: 45px;
                height: 45px;
            }

            .turntable-center-text {
                font-size: 12px;
            }
        }

        /* 优化樱花动画在移动端的表现 */
        @media screen and (max-width: 768px) {
            .sakura {
                opacity: 0.6;  /* 降低樱花透明度 */
            }
        }

        /* 修改中心按钮样式 */
        .turntable-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 90px;
            height: 90px;
            background: linear-gradient(135deg, #FFD700, #FFA500, #FF6B6B);
            border-radius: 50%;
            z-index: 2;
            cursor: pointer;
            box-shadow:
                0 0 0 4px rgba(255, 255, 255, 0.8),
                0 0 0 8px rgba(255, 215, 0, 0.6),
                0 8px 25px rgba(255, 77, 77, 0.4),
                inset 0 2px 8px rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border: 3px solid #FFF;
            animation: centerButtonGlow 2.5s ease-in-out infinite alternate;
        }

        /* 中心按钮发光动画 */
        @keyframes centerButtonGlow {
            0% {
                box-shadow:
                    0 0 0 4px rgba(255, 255, 255, 0.8),
                    0 0 0 8px rgba(255, 215, 0, 0.6),
                    0 8px 25px rgba(255, 77, 77, 0.4),
                    inset 0 2px 8px rgba(255, 255, 255, 0.3);
            }
            100% {
                box-shadow:
                    0 0 0 6px rgba(255, 255, 255, 0.9),
                    0 0 0 12px rgba(255, 215, 0, 0.8),
                    0 12px 35px rgba(255, 77, 77, 0.6),
                    inset 0 2px 12px rgba(255, 255, 255, 0.4);
            }
        }

        .turntable-center:hover {
            transform: translate(-50%, -50%) scale(1.08);
            box-shadow:
                0 0 0 6px rgba(255, 255, 255, 0.9),
                0 0 0 12px rgba(255, 215, 0, 0.8),
                0 12px 35px rgba(255, 77, 77, 0.6),
                inset 0 2px 12px rgba(255, 255, 255, 0.4);
            animation-duration: 1s;
        }

        .turntable-center:active {
            transform: translate(-50%, -50%);
        }

        .turntable-center-text {
            color: #FFF;
            font-size: 20px;
            font-weight: bold;
            text-align: center;
            line-height: 1.2;
            text-shadow:
                0 2px 4px rgba(0, 0, 0, 0.3),
                0 0 10px rgba(255, 215, 0, 0.5);
            animation: textShimmer 3s ease-in-out infinite;
        }

        /* 文字闪烁动画 */
        @keyframes textShimmer {
            0%, 100% {
                text-shadow:
                    0 2px 4px rgba(0, 0, 0, 0.3),
                    0 0 10px rgba(255, 215, 0, 0.5);
            }
            50% {
                text-shadow:
                    0 2px 4px rgba(0, 0, 0, 0.3),
                    0 0 20px rgba(255, 215, 0, 0.8),
                    0 0 30px rgba(255, 255, 255, 0.3);
            }
        }

        /* 添加禁用状态样式 */
        .turntable-center.disabled {
            opacity: 0.6;
            cursor: not-allowed;
            background: #ccc;
        }

        .turntable-center.disabled:hover {
            transform: translate(-50%, -50%);
            box-shadow: 0 4px 8px rgba(255, 77, 77, 0.3);
        }

        .turntable-center.disabled:active {
            transform: translate(-50%, -50%);
        }

        /* 流水统计样式 */
        .turnover-stats {
            padding: 20px;
            margin: 0 auto;
            max-width: 1000px;
        }

        .stat-card {
            background: linear-gradient(135deg,
                rgba(255, 107, 107, 0.95) 0%,
                rgba(255, 77, 77, 0.95) 25%,
                rgba(240, 147, 251, 0.95) 50%,
                rgba(245, 87, 108, 0.95) 75%,
                rgba(79, 172, 254, 0.95) 100%);
            background-size: 400% 400%;
            animation: statCardGradient 8s ease infinite;
            color: #fff;
            border: none;
            border-radius: 25px;
            box-shadow:
                0 0 0 3px rgba(255, 255, 255, 0.3),
                0 15px 35px rgba(255, 77, 77, 0.4),
                inset 0 2px 15px rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(15px);
            border: 2px solid rgba(255, 255, 255, 0.4);
            position: relative;
            overflow: hidden;
        }

        /* 流水统计卡片渐变动画 */
        @keyframes statCardGradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* 添加卡片光泽效果 */
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(255, 255, 255, 0.2),
                transparent);
            transition: left 0.8s ease;
            z-index: 1;
        }

        .stat-card:hover::before {
            left: 100%;
        }

        .stat-card .el-card__header {
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.1);
            border-radius: 23px 23px 0 0;
            position: relative;
            z-index: 2;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 18px;
            font-weight: bold;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .card-header .el-button {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: #fff;
            border-radius: 15px;
            transition: all 0.3s ease;
        }

        .card-header .el-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
        }

        .stat-content {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
            padding: 30px 20px;
            position: relative;
            z-index: 2;
        }

        .stat-item {
            text-align: center;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 20px;
            padding: 25px 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg,
                rgba(255, 255, 255, 0.1),
                rgba(255, 255, 255, 0.05));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .stat-item:hover::before {
            opacity: 1;
        }

        .stat-item:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow:
                0 0 0 2px rgba(255, 255, 255, 0.4),
                0 10px 25px rgba(255, 255, 255, 0.2);
        }

        .stat-label {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 12px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
            font-weight: 500;
        }

        .stat-value {
            font-size: 32px;
            font-weight: bold;
            text-shadow:
                0 2px 4px rgba(0, 0, 0, 0.3),
                0 0 15px rgba(255, 255, 255, 0.5);
            animation: valueGlow 3s ease-in-out infinite alternate;
        }

        /* 数值发光动画 */
        @keyframes valueGlow {
            0% {
                text-shadow:
                    0 2px 4px rgba(0, 0, 0, 0.3),
                    0 0 15px rgba(255, 255, 255, 0.5);
            }
            100% {
                text-shadow:
                    0 2px 4px rgba(0, 0, 0, 0.3),
                    0 0 25px rgba(255, 255, 255, 0.8),
                    0 0 35px rgba(255, 215, 0, 0.3);
            }
        }

        /* 流水规则卡片样式 */
        .rules-card {
            margin-top: 30px;
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.95) 0%,
                rgba(255, 255, 255, 0.85) 100%);
            border: none;
            border-radius: 25px;
            box-shadow:
                0 0 0 2px rgba(255, 215, 0, 0.3),
                0 15px 35px rgba(0, 0, 0, 0.1),
                inset 0 2px 15px rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(15px);
            border: 2px solid rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .rules-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(255, 215, 0, 0.1),
                transparent);
            transition: left 0.8s ease;
            z-index: 1;
        }

        .rules-card:hover::before {
            left: 100%;
        }

        .rules-card:hover {
            transform: translateY(-5px);
            box-shadow:
                0 0 0 3px rgba(255, 215, 0, 0.4),
                0 20px 45px rgba(0, 0, 0, 0.15),
                inset 0 2px 20px rgba(255, 255, 255, 0.4);
        }

        .rules-card .el-card__header {
            border-bottom: 1px solid rgba(255, 215, 0, 0.2);
            background: linear-gradient(135deg,
                rgba(255, 215, 0, 0.1),
                rgba(255, 165, 0, 0.1));
            border-radius: 23px 23px 0 0;
            position: relative;
            z-index: 2;
        }

        .rules-content {
            padding: 20px 0;
            position: relative;
            z-index: 2;
        }

        /* 表格样式美化 */
        .rules-content .el-table {
            background: transparent;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .rules-content .el-table th {
            background: linear-gradient(135deg, #f5f7fa, #e8ecf0);
            color: #606266;
            font-weight: bold;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
            border-bottom: 2px solid rgba(255, 215, 0, 0.3);
        }

        .rules-content .el-table td {
            color: #606266;
            background: rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
        }

        .rules-content .el-table tbody tr:hover td {
            background: linear-gradient(135deg,
                rgba(255, 215, 0, 0.1),
                rgba(255, 165, 0, 0.05));
            transform: scale(1.01);
        }

        .rules-content .el-table .el-button {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            border: none;
            color: #fff;
            font-weight: bold;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(255, 165, 0, 0.3);
            transition: all 0.3s ease;
        }

        .rules-content .el-table .el-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 165, 0, 0.5);
        }

        /* 流水规则表格中的文本样式 */
        .text-gray {
            color: #909399;
        }

        .text-success {
            color: #67c23a;
        }

        /* 添加分页样式 */
        .pagination {
            margin-top: 20px;
            text-align: center;
        }

        .pagination .el-pagination {
            justify-content: center;
        }

        .pagination .el-pagination.is-background .el-pager li:not(.disabled).active {
            background-color: #FF4D4D;
        }

        /* 添加或修改样式 */
        .pagination-container {
            margin-top: 20px;
            display: flex;
            justify-content: center;
        }

        .el-pagination.is-background .el-pager li:not(.disabled).active {
            background-color: #ff4d4d;
            color: #fff;
        }

        .el-pagination.is-background .el-pager li:not(.disabled):hover {
            color: #ff4d4d;
        }

        .records-list {
            margin-bottom: 20px;
        }

        .record-item {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 10px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .record-prize {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .prize-name {
            font-weight: bold;
            color: #333;
        }

        .record-time {
            font-size: 12px;
            color: #999;
            margin-top: 8px;
        }

        /* 中奖用户列表样式 */
        .winners-list {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 20px;
            margin-top: 15px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .winners-list .record-item {
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 8px;
            background: #fff;
            transition: all 0.3s ease;
        }

        .winners-list .record-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .winners-list .merchant-name {
            color: #409EFF;
            font-weight: bold;
            margin-right: 8px;
        }

        .winners-list .prize-name {
            color: #FF4D4D;
            font-weight: 500;
        }

        .winners-list .record-time {
            color: #909399;
            font-size: 12px;
            margin-top: 8px;
        }

        .balance-amount {
            color: #F56C6C;
            font-weight: bold;
            margin-left: 8px;
            font-size: 14px;
        }

        .prize-info {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 8px;
        }

        /* 中奖记录表格头部样式 */
        .winners-header {
            background: #f5f7fa;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 15px;
            font-weight: bold;
            color: #606266;
        }

        .winners-header .el-row {
            align-items: center;
        }

        /* 记录内容样式 */
        .record-content {
            display: flex;
            align-items: center;
            padding: 12px;
        }

        .winners-list .record-item {
            margin-bottom: 10px;
            padding: 0;
            border-radius: 8px;
            background: #fff;
            transition: all 0.3s ease;
        }

        .winners-list .record-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .winners-list .merchant-name {
            color: #409EFF;
            font-weight: bold;
        }

        .winners-list .prize-name {
            color: #FF4D4D;
            font-weight: 500;
        }

        .winners-list .balance-amount {
            color: #F56C6C;
            font-weight: bold;
            font-size: 14px;
        }

        .winners-list .record-time {
            color: #909399;
            font-size: 13px;
        }

        /* 确保列对齐 */
        .el-row {
            width: 100%;
            margin: 0 !important;
        }

        .el-col {
            display: flex;
            align-items: center;
        }

        /* 添加或修改样式 */
        .winners-list .merchant-name {
            font-weight: 500;
            display: inline-block;
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* 中奖用户列表的移动端适配 */
        @media screen and (max-width: 768px) {
            .winners-list {
                padding: 10px;
                
                .winners-header {
                    display: none; /* 在移动端隐藏表头 */
                }
                
                .record-item {
                    margin-bottom: 10px;
                    background: #fff;
                    border-radius: 8px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                }
                
                .record-content {
                    padding: 12px;
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 8px;
                }
                
                /* 重置列宽度为100% */
                .el-col {
                    width: 100% !important;
                    max-width: 100%;
                    margin-left: 0 !important;
                    margin-right: 0 !important;
                    padding: 4px 0;
                }
                
                /* 添加标签说明 */
                .el-col:before {
                    content: attr(data-label);
                    font-size: 12px;
                    color: #909399;
                    margin-right: 8px;
                }
                
                /* 商家名称样式 */
                .el-col:nth-child(1):before {
                    content: "商家：";
                }
                
                /* 奖品名称样式 */
                .el-col:nth-child(2):before {
                    content: "奖品：";
                }
                
                /* 奖品类型样式 */
                .el-col:nth-child(3):before {
                    content: "类型：";
                }
                
                /* 奖金样式 */
                .el-col:nth-child(4):before {
                    content: "奖金：";
                }
                
                /* 修改时间显示样式 */
                .el-col:nth-child(5) {
                    font-size: 13px; /* 减小字体大小 */
                    
                    &:before {
                        content: "时间：";
                        font-size: 12px;
                        color: #909399;
                        white-space: nowrap; /* 防止标签换行 */
                    }
                    
                    span {
                        white-space: nowrap; /* 防止时间换行 */
                        font-size: 13px; /* 统一字体大小 */
                    }
                }
                
                /* 调整时间行的内边距 */
                .el-col:last-child {
                    padding: 2px 0; /* 减少上下内边距 */
                    display: flex;
                    align-items: center;
                    flex-wrap: nowrap; /* 防止内容换行 */
                }
                
                .merchant-name {
                    display: inline;
                    word-break: break-all;
                    line-height: 1.4;
                }
                
                .prize-name {
                    display: inline;
                }
                
                /* 调整分页器样式 */
                .pagination-container {
                    margin-top: 15px;
                    
                    .el-pagination {
                        justify-content: center;
                        flex-wrap: wrap;
                        padding: 0 5px;
                    }
                    
                    .el-pagination .el-select {
                        margin-bottom: 8px;
                    }
                }
            }
        }

        /* 流水统计移动端适配 */
        @media screen and (max-width: 768px) {
            .turnover-stats {
                padding: 15px;
            }

            .stat-card {
                border-radius: 20px;
                margin: 0 5px;
            }

            .stat-content {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px 15px;
            }

            .stat-item {
                padding: 20px 15px;
                border-radius: 15px;
            }

            .stat-label {
                font-size: 14px;
                margin-bottom: 10px;
            }

            .stat-value {
                font-size: 28px;
            }

            .card-header {
                font-size: 16px;
            }

            .rules-card {
                margin: 20px 5px 0;
                border-radius: 20px;
            }

            .rules-content {
                padding: 15px 0;
            }

            .rules-content .el-table {
                font-size: 14px;
            }

            .rules-content .el-table th,
            .rules-content .el-table td {
                padding: 8px 4px;
            }
        }

        /* 针对超小屏幕的额外优化 */
        @media screen and (max-width: 320px) {
            .turnover-stats {
                padding: 10px;
            }

            .stat-card {
                margin: 0;
                border-radius: 15px;
            }

            .stat-content {
                gap: 15px;
                padding: 15px 10px;
            }

            .stat-item {
                padding: 15px 10px;
                border-radius: 12px;
            }

            .stat-label {
                font-size: 13px;
            }

            .stat-value {
                font-size: 24px;
            }

            .card-header {
                font-size: 15px;
            }

            .rules-card {
                margin: 15px 0 0;
                border-radius: 15px;
            }

            .winners-list {
                padding: 8px;

                .record-item {
                    margin-bottom: 8px;
                }

                .record-content {
                    padding: 8px;
                }

                .el-tag {
                    margin: 2px 0;
                }
            }
        }
    </style>
</head>
<body>
    <!-- 添加特效容器 -->
    <div id="sakura-container"></div>
    <div id="particles-container"></div>
    <div id="fireworks-container"></div>

    <!-- 加载动画 -->
    <div id="loading-overlay" class="loading-overlay">
        <div style="text-align: center;">
            <div class="loading-spinner"></div>
            <div class="loading-text">正在加载抽奖大转盘...</div>
        </div>
    </div>
    <div id="app">
        <el-card shadow="never" class="lottery-container">
            <!-- 信息展示区域 -->
            <div class="lottery-info">
                <div>今日基础抽奖次数: {{ remainingBaseDraws }}/{{ config?.daily_limit || 0 }}</div>
                <div>流水获得抽奖次数: {{ turnoverDraws }}/{{ turnoverStats.today_draws || 0 }}</div>
                <div>今日已抽奖次数: {{ totalDraws }}</div>
                <div class="time-message">{{ timeMessage }}</div>
                <div v-if="drawMessage" style="color: #ff4d4d;">{{ drawMessage }}</div>
            </div>

            <!-- 添加导航菜单 -->
            <el-tabs v-model="activeTab" class="lottery-tabs">
                <el-tab-pane label="抽奖大转盘" name="lottery">
                    <!-- 转盘区域 -->
                    <div class="turntable-container">
                        <div class="turntable">
                            <canvas ref="turntableCanvas" class="turntable-canvas" :class="{ rotating: isRotating }" width="300" height="300"></canvas>
                            <div class="turntable-center" @click="startLottery" :class="{ 'disabled': !canDraw || isRotating }">
                                <div class="turntable-center-text">点击</div>
                                <div class="turntable-center-text">抽奖</div>
                            </div>
                            <div class="pointer-container">
                                <div class="pointer">
                                    <div class="pointer-shine"></div>
                                </div>
                            </div>
                        </div>
                        <!-- 结果显示区域 -->
                        <div class="result-display" :class="{ show: showResult }">
                            {{ currentPrize }}
                        </div>
                    </div>
                </el-tab-pane>

                <el-tab-pane label="奖品信息" name="info">
                    <!-- 奖品列表 -->
                    <div class="prize-list">
                        <h3>奖品列表</h3>
                        <div class="prize-item" v-for="prize in prizes" :key="prize.id">
                            <span class="prize-name">{{ prize.name }}</span>
                            <span v-if="config && config.show_probability" class="prize-probability">
                                概率: {{ prize.probability }}%
                            </span>
                        </div>
                    </div>

                    <!-- 中奖记录 -->
                    <div class="lottery-records">
                        <h3>我的中奖记录</h3>
                        <div class="records-list">
                            <div v-if="records.length === 0" class="no-records">
                                暂无中奖记录
                            </div>
                            <div v-else v-for="record in records" :key="record.id" class="record-item">
                                <div class="record-prize">
                                    <span class="prize-name">{{ record.prize_name }}</span>
                                    <el-tag 
                                        :type="record.prize_type_style.type"
                                        :style="record.prize_type_style.style"
                                        size="small"
                                    >
                                        {{ record.prize_type_text }}
                                    </el-tag>
                                    <span v-if="record.balance_amount > 0" class="balance-amount">
                                        {{ record.balance_amount }}元
                                    </span>
                                    <el-tag 
                                        size="small" 
                                        :type="record.shipped ? 'success' : 'warning'"
                                        style="margin-left: 8px"
                                    >
                                        {{ record.shipped ? '已发货' : '未发货' }}
                                    </el-tag>
                                </div>
                                <div class="record-time">{{ record.create_time }}</div>
                            </div>
                        </div>
                        
                        <!-- 修改分页组件 -->
                        <div class="pagination-container" v-if="total > 0">
                            <el-pagination
                                v-model:current-page="currentPage"
                                v-model:page-size="pageSize"
                                :page-sizes="[10, 20, 50]"
                                :total="total"
                                @size-change="handleSizeChange"
                                @current-change="handleCurrentChange"
                                layout="total, sizes, prev, pager, next"
                                background
                            />
                        </div>
                    </div>
                </el-tab-pane>

                <!-- 添加流水统计标签页 -->
                <el-tab-pane label="流水统计" name="turnover">
                    <div class="turnover-stats">
                        <el-card class="stat-card">
                            <template #header>
                                <div class="card-header">
                                    <span>流水统计</span>
                                    <el-button type="primary" link @click="refreshTurnoverStats">
                                        <el-icon><Refresh /></el-icon>
                                        刷新
                                    </el-button>
                                </div>
                            </template>
                            <div class="stat-content">
                                <div class="stat-item">
                                    <div class="stat-label">今日流水</div>
                                    <div class="stat-value">{{ turnoverStats.today_amount || 0 }}元</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">本月流水</div>
                                    <div class="stat-value">{{ turnoverStats.month_amount || 0 }}元</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">已获得抽奖次数</div>
                                    <div class="stat-value">{{ turnoverStats.today_draws || 0 }}</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">下次抽奖需要</div>
                                    <div class="stat-value">{{ turnoverStats.next_required || 0 }}元</div>
                                </div>
                            </div>
                        </el-card>

                        <!-- 流水规则说明 -->
                        <el-card class="rules-card" style="margin-top: 20px;">
                            <template #header>
                                <div class="card-header">
                                    <span>流水规则说明</span>
                                </div>
                            </template>
                            <div class="rules-content">
                                <el-table :data="turnoverRules" style="width: 100%">
                                    <el-table-column prop="turnover_amount" label="流水金额">
                                        <template #default="scope">
                                            {{ scope.row.turnover_amount }}元
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="draw_times" label="获得抽奖次数">
                                        <template #default="scope">
                                            {{ scope.row.draw_times }}次
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="状态">
                                        <template #default="scope">
                                            <el-tag v-if="!scope.row.status" type="info">
                                                未开启
                                            </el-tag>
                                            <el-tag v-else :type="scope.row.claimed ? 'success' : 'warning'">
                                                {{ scope.row.claimed ? '已领取' : '未领取' }}
                                            </el-tag>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="操作" width="120">
                                        <template #default="scope">
                                            <el-button
                                                v-if="scope.row.status && !scope.row.claimed && scope.row.can_claim"
                                                type="primary"
                                                size="small"
                                                @click="claimTurnoverReward(scope.$index)"
                                                :loading="scope.row.claiming"
                                            >
                                                领取
                                            </el-button>
                                            <span v-else-if="!scope.row.status" class="text-gray">未开启</span>
                                            <span v-else-if="scope.row.claimed" class="text-success">已领取</span>
                                            <span v-else class="text-gray">未达成</span>
                                        </template>
                                    </el-table-column>
                                </el-table>
                                <el-empty v-if="!turnoverRules || turnoverRules.length === 0" description="暂无流水规则" />
                            </div>
                        </el-card>
                    </div>
                </el-tab-pane>

                <!-- 中奖用户标签页 -->
                <el-tab-pane label="中奖用户" name="winners">
                    <div class="winners-list">
                        <!-- 添加表头 -->
                        <div class="winners-header">
                            <el-row :gutter="20">
                                <el-col :span="4">商家</el-col>
                                <el-col :span="4">奖品名称</el-col>
                                <el-col :span="4">奖品类型</el-col>
                                <el-col :span="4">奖金</el-col>
                                <el-col :span="8">时间</el-col>
                            </el-row>
                        </div>

                        <div v-if="allRecords.length === 0" class="no-records">
                            暂无中奖记录
                        </div>
                        <div v-else v-for="record in allRecords" :key="record.id" class="record-item">
                            <el-row :gutter="20" class="record-content">
                                <el-col :span="4">
                                    <span class="merchant-name" :style="record.merchant_name.startsWith('VIP') ? 'color: #67C23A;' : ''">
                                        {{ record.merchant_name || record.merchant_id }}
                                    </span>
                                </el-col>
                                <el-col :span="4">
                                    <span class="prize-name">{{ record.prize_name }}</span>
                                </el-col>
                                <el-col :span="4">
                                    <el-tag 
                                        :type="record.prize_type_style.type"
                                        :style="record.prize_type_style.style"
                                    >
                                        {{ record.prize_type_text }}
                                    </el-tag>
                                </el-col>
                                <el-col :span="4">
                                    <span v-if="record.balance_amount > 0">¥{{ record.balance_amount }}</span>
                                    <span v-else>-</span>
                                </el-col>
                                <el-col :span="8">
                                    <span>{{ record.create_time }}</span>
                                </el-col>
                            </el-row>
                        </div>
                        
                        <!-- 分页组件 -->
                        <div class="pagination-container" v-if="winnersTotal > 0">
                            <el-pagination
                                v-model:current-page="winnersCurrentPage"
                                v-model:page-size="winnersPageSize"
                                :page-sizes="[10, 20, 50]"
                                :total="winnersTotal"
                                @size-change="handleWinnersSizeChange"
                                @current-change="handleWinnersCurrentChange"
                                layout="total, sizes, prev, pager, next"
                                background
                            />
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </el-card>
    </div>

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>

    <script>
    const { ref, reactive, onMounted, computed, watch, nextTick } = Vue;
    const { ElMessage } = ElementPlus;

    const app = Vue.createApp({
        setup() {
            const turntableCanvas = ref(null);
            const isRotating = ref(false);
            const prizes = ref([]);
            const remainingBaseDraws = ref(0);
            const turnoverDraws = ref(0);
            const totalDraws = ref(0); // 已使用的总抽奖次数
            const config = ref({
                status: 1,
                daily_limit: 0,
                start_hour: '00:00',
                end_hour: '23:59',
                show_probability: 0
            });
            const myRecords = ref([]);
            const showRecords = ref(true);

            // 添加导航菜单激活状态
            const activeTab = ref('lottery');

            // 检查是否在抽奖时间范围内
            const isInDrawTime = computed(() => {
                if (!config.value) return false;
                const now = new Date();
                const currentTime = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;
                return currentTime >= config.value.start_hour && currentTime <= config.value.end_hour;
            });

            // 显示时间消息
            const timeMessage = computed(() => {
                if (!config.value) return '';
                return `抽奖时间：${config.value.start_hour} - ${config.value.end_hour}`;
            });

            // 检查是否可以抽奖
            const canDraw = computed(() => {
                if (!config.value.status) return false;
                if (remainingBaseDraws.value <= 0 && turnoverDraws.value <= 0) return false;
                
                const now = new Date();
                const currentTime = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;
                
                return currentTime >= config.value.start_hour && currentTime <= config.value.end_hour;
            });

            // 抽奖提示信息
            const drawMessage = computed(() => {
                if (!config.value.status) return '抽奖活动已关闭';
                if (remainingBaseDraws.value <= 0 && turnoverDraws.value <= 0) return '今日抽奖次数已用完';
                
                const now = new Date();
                const currentTime = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;
                
                if (currentTime < config.value.start_hour) {
                    return `抽奖未开始，开始时间：${config.value.start_hour}`;
                } else if (currentTime > config.value.end_hour) {
                    return `抽奖已结束，结束时间：${config.value.end_hour}`;
                }
                return '';
            });

            // 在 data 中添加新的响应式数据
            const currentPrize = ref('');
            const showResult = ref(false);

            // 添加一个存储转盘奖品位置信息的数组
            const prizePositions = ref([]);

            // 添加 merchantLimit 响应式变量
            const merchantLimit = ref({
                daily_limit: 0,
                used_count: 0,
                update_time: 0,
                last_reset_date: ''
            });

            // 修改初始化转盘函数
            const initTurntable = () => {
                const canvas = turntableCanvas.value;
                if (!canvas || !prizes.value || prizes.value.length === 0) return;
                
                const ctx = canvas.getContext('2d');
                const width = canvas.width;
                const height = canvas.height;
                const centerX = width / 2;
                const centerY = height / 2;
                const radius = Math.min(width, height) / 2 - 20;
                
                ctx.clearRect(0, 0, width, height);
                
                const activePrizes = prizes.value.filter(prize => prize.status === 1 && prize.stock > 0);
                if (activePrizes.length === 0) return;
                
                const sliceAngle = (Math.PI * 2) / activePrizes.length;
                
                // 清空奖品位置数组
                prizePositions.value = [];
                
                activePrizes.forEach((prize, index) => {
                    // 调整起始角度，使第一个奖品位于12点钟方向
                    const startAngle = index * sliceAngle - Math.PI / 2;
                    const endAngle = startAngle + sliceAngle;
                    
                    // 存储奖品位置信息
                    prizePositions.value.push({
                        index,
                        id: prize.id,
                        name: prize.name,
                        startAngle,
                        endAngle,
                        // 转换为度数并确保在0-360范围内
                        startDegree: ((startAngle * 180 / Math.PI + 360) % 360),
                        endDegree: ((endAngle * 180 / Math.PI + 360) % 360)
                    });
                    
                    // 绘制扇形
                    ctx.beginPath();
                    ctx.moveTo(centerX, centerY);
                    ctx.arc(centerX, centerY, radius, startAngle, endAngle);
                    ctx.closePath();

                    // 创建渐变背景
                    const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);
                    const colors = ['#FFF8DC', '#FFE4B5', '#FFEAA7', '#FDCB6E'];
                    const color = colors[index % colors.length];
                    gradient.addColorStop(0, '#FFFFFF');
                    gradient.addColorStop(0.7, color);
                    gradient.addColorStop(1, '#FFD700');

                    ctx.fillStyle = gradient;
                    ctx.fill();

                    // 添加内阴影效果
                    ctx.save();
                    ctx.clip();
                    ctx.shadowColor = 'rgba(0, 0, 0, 0.1)';
                    ctx.shadowBlur = 10;
                    ctx.shadowOffsetX = 2;
                    ctx.shadowOffsetY = 2;
                    ctx.fillStyle = 'rgba(255, 215, 0, 0.1)';
                    ctx.fill();
                    ctx.restore();
                    
                    // 绘制分割线
                    ctx.beginPath();
                    ctx.moveTo(centerX, centerY);
                    ctx.lineTo(
                        centerX + Math.cos(startAngle) * radius,
                        centerY + Math.sin(startAngle) * radius
                    );
                    ctx.strokeStyle = '#FFD700';
                    ctx.lineWidth = 3;
                    ctx.shadowColor = 'rgba(255, 215, 0, 0.5)';
                    ctx.shadowBlur = 5;
                    ctx.stroke();
                    ctx.shadowColor = 'transparent';
                    ctx.shadowBlur = 0;
                    
                    // 绘制奖品文字和图标
                    ctx.save();
                    ctx.translate(centerX, centerY);
                    ctx.rotate(startAngle + sliceAngle / 2);

                    // 计算文字位置和大小
                    const textRadius = radius * 0.7;
                    const maxTextWidth = radius * 0.4; // 最大文字宽度

                    // 根据奖品数量调整字体大小
                    let fontSize = activePrizes.length > 8 ? 12 : activePrizes.length > 6 ? 14 : 16;
                    ctx.font = `bold ${fontSize}px "Microsoft YaHei", Arial`;

                    // 处理长文字，自动换行
                    const prizeName = prize.name;
                    const words = prizeName.split('');
                    const lines = [];
                    let currentLine = '';

                    // 测量文字宽度并分行
                    for (let i = 0; i < words.length; i++) {
                        const testLine = currentLine + words[i];
                        const metrics = ctx.measureText(testLine);

                        if (metrics.width > maxTextWidth && currentLine !== '') {
                            lines.push(currentLine);
                            currentLine = words[i];
                        } else {
                            currentLine = testLine;
                        }
                    }
                    if (currentLine) {
                        lines.push(currentLine);
                    }

                    // 限制最多显示2行
                    if (lines.length > 2) {
                        lines[1] = lines[1].substring(0, lines[1].length - 1) + '...';
                        lines.splice(2);
                    }

                    // 绘制文字
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';

                    const lineHeight = fontSize + 2;
                    const totalHeight = lines.length * lineHeight;
                    const startY = -totalHeight / 2 + lineHeight / 2;

                    lines.forEach((line, lineIndex) => {
                        const y = startY + lineIndex * lineHeight;

                        ctx.save();
                        ctx.translate(textRadius, y);
                        ctx.rotate(Math.PI / 2);

                        // 绘制文字描边
                        ctx.strokeStyle = '#FFD700';
                        ctx.lineWidth = 2;
                        ctx.strokeText(line, 0, 0);

                        // 绘制文字填充
                        ctx.fillStyle = '#8B4513';
                        ctx.shadowColor = 'rgba(255, 255, 255, 0.8)';
                        ctx.shadowBlur = 1;
                        ctx.shadowOffsetX = 1;
                        ctx.shadowOffsetY = 1;
                        ctx.fillText(line, 0, 0);

                        ctx.restore();
                    });

                    // 如果是现金奖励，在外圈添加金额标识
                    if (prize.type === 'cash' && prize.amount) {
                        ctx.save();
                        ctx.translate(radius * 0.85, 0);
                        ctx.rotate(Math.PI / 2);

                        // 绘制金额背景（圆角矩形）
                        ctx.fillStyle = '#FF4D4D';
                        ctx.beginPath();
                        const rectX = -15, rectY = -8, rectW = 30, rectH = 16, radius = 8;
                        ctx.moveTo(rectX + radius, rectY);
                        ctx.lineTo(rectX + rectW - radius, rectY);
                        ctx.quadraticCurveTo(rectX + rectW, rectY, rectX + rectW, rectY + radius);
                        ctx.lineTo(rectX + rectW, rectY + rectH - radius);
                        ctx.quadraticCurveTo(rectX + rectW, rectY + rectH, rectX + rectW - radius, rectY + rectH);
                        ctx.lineTo(rectX + radius, rectY + rectH);
                        ctx.quadraticCurveTo(rectX, rectY + rectH, rectX, rectY + rectH - radius);
                        ctx.lineTo(rectX, rectY + radius);
                        ctx.quadraticCurveTo(rectX, rectY, rectX + radius, rectY);
                        ctx.closePath();
                        ctx.fill();

                        // 绘制金额文字
                        ctx.fillStyle = '#FFD700';
                        ctx.font = 'bold 10px Arial';
                        ctx.textAlign = 'center';
                        ctx.textBaseline = 'middle';
                        ctx.fillText('¥' + prize.amount, 0, 0);

                        ctx.restore();
                    }

                    // 重置阴影
                    ctx.shadowColor = 'transparent';
                    ctx.shadowBlur = 0;
                    
                    ctx.restore();
                });
                
                // 绘制中心圆
                const centerRadius = radius * 0.2;

                // 创建中心圆渐变
                const centerGradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, centerRadius);
                centerGradient.addColorStop(0, '#FFFFFF');
                centerGradient.addColorStop(0.7, '#FFD700');
                centerGradient.addColorStop(1, '#FFA500');

                ctx.beginPath();
                ctx.arc(centerX, centerY, centerRadius, 0, Math.PI * 2);
                ctx.fillStyle = centerGradient;
                ctx.fill();

                // 绘制中心圆边框
                ctx.strokeStyle = '#FFD700';
                ctx.lineWidth = 4;
                ctx.shadowColor = 'rgba(255, 215, 0, 0.5)';
                ctx.shadowBlur = 8;
                ctx.stroke();

                // 重置阴影
                ctx.shadowColor = 'transparent';
                ctx.shadowBlur = 0;
            };

            // 修改获取数据的方法
            const fetchData = async () => {
                try {
                    const res = await axios.get('/plugin/Lottery/user/getLotteryConfig');
                    if (res.data.code === 200) {
                        config.value = res.data.data.config;
                        remainingBaseDraws.value = res.data.data.remainingBaseDraws;
                        turnoverDraws.value = res.data.data.turnoverDraws;
                        totalDraws.value = res.data.data.totalDraws; // 这里获取的是已使用的总次数
                        prizes.value = res.data.data.prizes;
                        merchantLimit.value = res.data.data.merchantLimit || {
                            daily_limit: config.value.daily_limit,
                            used_count: 0,
                            update_time: 0,
                            last_reset_date: ''
                        };
                        
                        // 同时更新流水统计
                        await fetchTurnoverStats();
                        
                        nextTick(() => {
                            initTurntable();
                        });
                    }
                } catch (error) {
                    console.error('获取配置失败:', error);
                    ElMessage.error('获取配置失败：' + (error.response?.data?.msg || error.message));
                }
            };

            // 修改获取当前指向奖品的方法
            const getCurrentPrize = () => {
                if (!prizePositions.value.length) return '';
                
                const canvas = turntableCanvas.value;
                if (!canvas) return '';

                // 获取当前转盘旋转角度
                const style = window.getComputedStyle(canvas);
                const matrix = new WebKitCSSMatrix(style.transform);
                let angle = Math.round(Math.atan2(matrix.m12, matrix.m11) * (180/Math.PI));
                
                // 将角度转换为 0-360 范围内的正值
                angle = ((angle % 360) + 360) % 360;
                
                // 计算指针实际指向的角度（12点方向为0度）
                const pointerAngle = (360 - angle + 270) % 360;
                
                // 查找当前指向的奖品
                const currentPrize = prizePositions.value.find(prize => {
                    let start = prize.startDegree;
                    let end = prize.endDegree;
                    
                    // 处理跨越360度的情况
                    if (end < start) {
                        end += 360;
                    }
                    
                    let adjustedPointerAngle = pointerAngle;
                    if (pointerAngle < start) {
                        adjustedPointerAngle += 360;
                    }
                    
                    return adjustedPointerAngle >= start && adjustedPointerAngle < end;
                });

                return currentPrize?.name || '';
            };

            // 添加动画帧更新方法
            const updateCurrentPrize = () => {
                if (isRotating.value) {
                    currentPrize.value = getCurrentPrize();
                    requestAnimationFrame(updateCurrentPrize);
                }
            };

            // 修改抽奖函数
            const startLottery = async () => {
                if (!canDraw.value || isRotating.value) return;
                
                try {
                    isRotating.value = true;
                    showResult.value = false;
                    
                    const res = await axios.post("/plugin/Lottery/user/doLottery");
                    
                    if (res.data?.code === 200) {
                        const { prize, angle, remainingBaseDraws: newBaseDraws, turnoverDraws: newTurnoverDraws, totalDraws: newTotalDraws } = res.data.data;
                        
                        // 应用旋转动画
                        const canvas = turntableCanvas.value;
                        const turntable = canvas.parentElement;

                        // 添加旋转特效类
                        turntable.classList.add('spinning');

                        canvas.style.transition = 'none';
                        canvas.style.transform = 'rotate(0deg)';
                        canvas.offsetHeight; // 强制重排

                        // 设置动画
                        canvas.style.transition = 'transform 4s cubic-bezier(0.25, 0.1, 0.25, 1)';
                        canvas.style.transform = `rotate(${angle}deg)`;

                        // 启动粒子效果
                        startParticleEffect();
                        
                        // 显示结果
                        setTimeout(async () => {
                            // 移除旋转特效类
                            const turntable = turntableCanvas.value.parentElement;
                            turntable.classList.remove('spinning');

                            currentPrize.value = prize.name;
                            showResult.value = true;

                            // 启动烟花效果
                            startFireworksEffect();

                            // 更新剩余次数
                            remainingBaseDraws.value = newBaseDraws;
                            turnoverDraws.value = newTurnoverDraws;
                            totalDraws.value = newTotalDraws;

                            // 重新获取流水统计
                            await fetchTurnoverStats();

                            ElMessage({
                                message: `恭喜获得：${prize.name}`,
                                type: 'success',
                                duration: 3000
                            });

                            isRotating.value = false;
                        }, 4000);
                        
                    } else {
                        ElMessage.error(res.data?.msg || '抽奖失败');
                        isRotating.value = false;
                    }
                } catch (error) {
                    console.error('抽奖失败:', error);
                    ElMessage.error('抽奖失败：' + (error.response?.data?.msg || error.message));
                    isRotating.value = false;
                }
            };

            // 获取当前转盘旋转角度的辅助函数
            const getCurrentRotation = (element) => {
                const style = window.getComputedStyle(element);
                const matrix = new WebKitCSSMatrix(style.transform);
                const angle = Math.round(Math.atan2(matrix.m12, matrix.m11) * (180/Math.PI));
                return ((angle % 360) + 360) % 360; // 确保返回0-360之间的值
            };

            // 监听奖品数组变化，重新初始化转盘
            watch(() => prizes.value, () => {
                nextTick(() => {
                    initTurntable();
                });
            }, { deep: true });

            // 添加分页相关的响应式数据
            const currentPage = ref(1);
            const pageSize = ref(10);
            const total = ref(0);
            const records = ref([]);

            // 获取中奖记录
            const fetchLotteryRecords = async () => {
                try {
                    const res = await axios.get("/plugin/Lottery/user/getLotteryRecords", {
                        params: {
                            page: currentPage.value,
                            pageSize: pageSize.value
                        }
                    });
                    
                    if (res.data?.code === 200) {
                        records.value = res.data.data.list;
                        total.value = res.data.data.total;
                        console.log('获取中奖记录成功:', res.data.data);
                    } else {
                        console.error('获取记录失败:', res.data);
                        ElMessage.error(res.data?.msg || '获取记录失败');
                    }
                } catch (error) {
                    console.error('获取中奖记录失败:', error);
                    ElMessage.error('获取记录失败');
                }
            };

            // 处理页码变化
            const handleCurrentChange = (val) => {
                currentPage.value = val;
                fetchLotteryRecords();
            };

            // 获取奖品类型对应的标签类型
            const getPrizeTypeTag = (type) => {
                switch (type) {
                    case 'cash':
                        return 'danger';
                    case 'virtual':
                        return 'success';
                    case 'physical':
                        return 'warning';
                    default:
                        return 'info';
                }
            };

            // 检查凌晨重置
            const checkReset = () => {
                const now = new Date();
                const tomorrow = new Date(now);
                tomorrow.setDate(tomorrow.getDate() + 1);
                tomorrow.setHours(0, 0, 0, 0);
                
                const timeToReset = tomorrow - now;
                
                setTimeout(() => {
                    fetchData(); // 重新获取配置和次数
                    checkReset(); // 设置下一天的重置
                }, timeToReset);
            };

            // 添加一个用于调试的方法（可选）
            const debugPrizePosition = () => {
                const currentRotation = getCurrentRotation(turntableCanvas.value);
                console.log('当前转盘旋转角度:', currentRotation);
                console.log('当前指向奖品:', getCurrentPrize());
                console.log('所有奖品位置:', prizePositions.value);
            };

            // 添加定时刷新
            const refreshConfig = () => {
                Promise.all([
                    fetchData(),
                    fetchTurnoverStats()
                ]).catch(error => {
                    console.error('刷新数据失败:', error);
                });
                setTimeout(refreshConfig, 60000); // 每分钟刷新一次
            };

            // 流水统计数据
            const turnoverStats = ref({
                today_amount: 0,
                today_draws: 0,
                next_required: 0
            });

            const turnoverRules = ref([]);

            // 获取流水统计
            const fetchTurnoverStats = async () => {
                try {
                    const res = await axios.post("/plugin/Lottery/user/getTurnoverStats");
                    if (res.data?.code === 200) {
                        turnoverStats.value = res.data.data.stats;
                        turnoverRules.value = res.data.data.rules;
                        
                        // 如果配置已更新，重新获取抽奖配置
                        if (res.data.data.config_updated) {
                            await fetchData();
                            ElMessage.success('已获得新的抽奖次数！');
                        }
                    }
                } catch (error) {
                    console.error('获取流水统计失败:', error);
                    ElMessage.error('获取流水统计失败');
                }
            };

            // 刷新流水统计
            const refreshTurnoverStats = () => {
                fetchTurnoverStats();
                ElMessage.success('刷新成功');
            };

            // 手动领取流水奖励
            const claimTurnoverReward = async (index) => {
                try {
                    // 设置领取状态
                    turnoverRules.value[index].claiming = true;

                    const res = await axios.post("/plugin/Lottery/user/claimTurnoverReward", {
                        rule_index: index
                    });

                    if (res.data?.code === 200) {
                        ElMessage.success('领取成功！获得 ' + turnoverRules.value[index].draw_times + ' 次抽奖机会');

                        // 更新本地状态
                        turnoverRules.value[index].claimed = true;
                        turnoverRules.value[index].can_claim = false;

                        // 刷新抽奖配置以更新抽奖次数
                        await fetchData();
                        await fetchTurnoverStats();
                    } else {
                        ElMessage.error(res.data?.msg || '领取失败');
                    }
                } catch (error) {
                    console.error('领取流水奖励失败:', error);
                    ElMessage.error('领取失败：' + (error.response?.data?.msg || error.message));
                } finally {
                    // 清除领取状态
                    turnoverRules.value[index].claiming = false;
                }
            };

            // 监听标签页切换
            watch(activeTab, (newVal) => {
                if (newVal === 'turnover') {
                    fetchTurnoverStats();
                }
            });

            // 修改配置监听
            watch(() => config.value, (newConfig) => {
                if (newConfig && newConfig.daily_limit) {
                    remainingBaseDraws.value = Math.max(0, newConfig.daily_limit - (merchantLimit.value?.used_count || 0));
                }
            }, { deep: true });

            // 在 setup 函数中添加 handleSizeChange 方法
            const handleSizeChange = (val) => {
                pageSize.value = val;
                currentPage.value = 1; // 切换每页显示数量时重置为第一页
                fetchLotteryRecords();
            };

            // 添加中奖用户相关的响应式数据
            const allRecords = ref([]);
            const winnersCurrentPage = ref(1);
            const winnersPageSize = ref(10);
            const winnersTotal = ref(0);

            // 获取所有中奖记录
            const fetchAllWinners = async () => {
                try {
                    const res = await axios.get('/plugin/Lottery/user/getLotteryRecords', {
                        params: {
                            page: winnersCurrentPage.value,
                            pageSize: winnersPageSize.value,
                            type: 'winners' // 添加type参数，用于标识是获取中奖用户列表
                        }
                    });
                    
                    if (res.data.code === 200) {
                        allRecords.value = res.data.data.list;
                        winnersTotal.value = res.data.data.total;
                    }
                } catch (error) {
                    console.error('获取中奖记录失败:', error);
                    ElMessage.error('获取中奖记录失败');
                }
            };

            // 处理中奖用户分页大小变化
            const handleWinnersSizeChange = (val) => {
                winnersPageSize.value = val;
                winnersCurrentPage.value = 1;
                fetchAllWinners();
            };

            // 处理中奖用户页码变化
            const handleWinnersCurrentChange = (val) => {
                winnersCurrentPage.value = val;
                fetchAllWinners();
            };

            // 监听标签页切换
            watch(activeTab, (newVal) => {
                if (newVal === 'winners') {
                    fetchAllWinners();
                }
            });

            // 添加奖品类型样式函数
            const getPrizeTypeStyle = (type) => {
                switch (type) {
                    case 'physical':
                        return 'warning';
                    case 'virtual':
                        return 'success';
                    case 'cash':
                        return 'danger';
                    default:
                        return 'info';
                }
            };

            // 添加奖品类型文本转换函数
            const getPrizeTypeText = (type) => {
                switch (type) {
                    case 'physical':
                        return '实物';
                    case 'virtual':
                        return '虚拟';
                    case 'cash':
                        return '现金';
                    default:
                        return '未知';
                }
            };

            onMounted(async () => {
                try {
                    // 设置canvas尺寸
                    const canvas = turntableCanvas.value;
                    const dpr = window.devicePixelRatio || 1;
                    const rect = canvas.getBoundingClientRect();
                    canvas.width = rect.width * dpr;
                    canvas.height = rect.height * dpr;

                    // 获取数据并初始化转盘
                    await fetchData();
                    await fetchLotteryRecords();
                    checkReset(); // 添加重置检查
                    refreshConfig();
                    if (activeTab.value === 'turnover') {
                        await fetchTurnoverStats();
                    }

                    // 隐藏加载动画
                    setTimeout(() => {
                        const loadingOverlay = document.getElementById('loading-overlay');
                        if (loadingOverlay) {
                            loadingOverlay.style.opacity = '0';
                            setTimeout(() => {
                                loadingOverlay.style.display = 'none';
                            }, 500);
                        }
                    }, 1000);
                } catch (error) {
                    console.error('初始化失败:', error);
                    // 即使出错也要隐藏加载动画
                    const loadingOverlay = document.getElementById('loading-overlay');
                    if (loadingOverlay) {
                        loadingOverlay.style.display = 'none';
                    }
                }
            });

            return {
                turntableCanvas,
                isRotating,
                remainingBaseDraws,
                turnoverDraws,
                totalDraws,
                canDraw,
                drawMessage,
                startLottery,
                prizes,
                myRecords,
                showRecords,
                getPrizeTypeTag,
                activeTab,
                isInDrawTime,
                timeMessage,
                records,
                currentPage,
                pageSize,
                total,
                handleCurrentChange,
                currentPrize,
                showResult,
                turnoverStats,
                turnoverRules,
                refreshTurnoverStats,
                claimTurnoverReward,
                merchantLimit,
                handleSizeChange,
                config,
                allRecords,
                winnersCurrentPage,
                winnersPageSize,
                winnersTotal,
                handleWinnersSizeChange,
                handleWinnersCurrentChange,
                getPrizeTypeStyle,
                getPrizeTypeText
            };
        }
    });

    app.use(ElementPlus, {
        locale: ElementPlusLocaleZhCn
    });

    app.mount("#app");

    // 添加樱花动画相关代码
    function createSakura() {
        const sakuraContainer = document.getElementById('sakura-container');
        const sakura = document.createElement('span');
        const size = Math.random() * 20 + 10;
        
        sakura.className = 'sakura';
        sakura.style.width = `${size}px`;
        sakura.style.height = `${size}px`;
        sakura.style.left = Math.random() * window.innerWidth + 'px';
        sakura.style.top = '-50px';
        sakura.style.background = `rgba(255, ${Math.random() * 100 + 155}, ${Math.random() * 100 + 155}, 0.8)`;
        sakura.style.borderRadius = '50%';
        sakura.style.filter = 'blur(1px)';
        
        const animation = sakura.animate([
            {
                transform: `translate(0, 0) rotate(0deg)`,
                opacity: 1
            },
            {
                transform: `translate(${Math.random() * 200 - 100}px, ${window.innerHeight + 50}px) rotate(${Math.random() * 720}deg)`,
                opacity: 0
            }
        ], {
            duration: Math.random() * 4000 + 5000,
            easing: 'linear'
        });
        
        sakuraContainer.appendChild(sakura);
        
        animation.onfinish = () => {
            sakura.remove();
        };
    }

    // 定期创建樱花
    function startSakuraAnimation() {
        setInterval(createSakura, 300);
    }

    // 粒子效果函数
    function createParticle() {
        const particlesContainer = document.getElementById('particles-container');
        const particle = document.createElement('div');
        const size = Math.random() * 6 + 2;
        const colors = ['#FFD700', '#FFA500', '#FF6B6B', '#FF4D4D', '#FFFFFF'];

        particle.className = 'particle';
        particle.style.width = `${size}px`;
        particle.style.height = `${size}px`;
        particle.style.left = Math.random() * window.innerWidth + 'px';
        particle.style.top = Math.random() * window.innerHeight + 'px';
        particle.style.background = colors[Math.floor(Math.random() * colors.length)];
        particle.style.boxShadow = `0 0 ${size * 2}px ${particle.style.background}`;

        const animation = particle.animate([
            {
                transform: 'translate(0, 0) scale(0)',
                opacity: 0
            },
            {
                transform: `translate(${Math.random() * 200 - 100}px, ${Math.random() * 200 - 100}px) scale(1)`,
                opacity: 1
            },
            {
                transform: `translate(${Math.random() * 400 - 200}px, ${Math.random() * 400 - 200}px) scale(0)`,
                opacity: 0
            }
        ], {
            duration: Math.random() * 2000 + 1000,
            easing: 'ease-out'
        });

        particlesContainer.appendChild(particle);

        animation.onfinish = () => {
            particle.remove();
        };
    }

    // 启动粒子效果
    function startParticleEffect() {
        let particleCount = 0;
        const maxParticles = 30;

        const particleInterval = setInterval(() => {
            if (particleCount < maxParticles) {
                createParticle();
                particleCount++;
            } else {
                clearInterval(particleInterval);
            }
        }, 100);
    }

    // 烟花效果函数
    function createFirework(x, y) {
        const fireworksContainer = document.getElementById('fireworks-container');
        const colors = ['#FFD700', '#FFA500', '#FF6B6B', '#FF4D4D', '#FFFFFF', '#00FF00', '#0080FF'];

        for (let i = 0; i < 12; i++) {
            const firework = document.createElement('div');
            const size = Math.random() * 8 + 4;

            firework.className = 'firework';
            firework.style.width = `${size}px`;
            firework.style.height = `${size}px`;
            firework.style.left = x + 'px';
            firework.style.top = y + 'px';
            firework.style.background = colors[Math.floor(Math.random() * colors.length)];
            firework.style.boxShadow = `0 0 ${size * 3}px ${firework.style.background}`;

            const angle = (i / 12) * Math.PI * 2;
            const distance = Math.random() * 150 + 50;
            const endX = Math.cos(angle) * distance;
            const endY = Math.sin(angle) * distance;

            const animation = firework.animate([
                {
                    transform: 'translate(0, 0) scale(0)',
                    opacity: 1
                },
                {
                    transform: `translate(${endX}px, ${endY}px) scale(1)`,
                    opacity: 1
                },
                {
                    transform: `translate(${endX * 1.5}px, ${endY * 1.5}px) scale(0)`,
                    opacity: 0
                }
            ], {
                duration: 1500,
                easing: 'ease-out'
            });

            fireworksContainer.appendChild(firework);

            animation.onfinish = () => {
                firework.remove();
            };
        }
    }

    // 启动烟花效果
    function startFireworksEffect() {
        const centerX = window.innerWidth / 2;
        const centerY = window.innerHeight / 2;

        // 中心烟花
        createFirework(centerX, centerY);

        // 随机位置烟花
        setTimeout(() => createFirework(centerX - 100, centerY - 50), 300);
        setTimeout(() => createFirework(centerX + 100, centerY - 50), 600);
        setTimeout(() => createFirework(centerX - 50, centerY + 80), 900);
        setTimeout(() => createFirework(centerX + 50, centerY + 80), 1200);
    }

    // 在页面加载完成后启动樱花动画
    window.addEventListener('load', () => {
        startSakuraAnimation();
    });
    </script>
</body>
</html>
