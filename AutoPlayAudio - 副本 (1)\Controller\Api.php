<?php

namespace plugin\AutoPlayAudio\Controller;

use app\common\controller\BasePlugin;
use think\Request;

class Api extends BasePlugin
{
    public function uploadForm()
    {
        return $this->fetch('upload_form');
    }

    public function uploadAudio(Request $request)
    {
        $file = $request->file('audio');
        if ($file) {
            $info = $file->move('plugin/AutoPlayAudio/static/audio/');
            if ($info) {
                // 更新音频文件路径
                plugin_set_param('AutoPlayAudio', 'audio_file', '/plugin/AutoPlayAudio/static/audio/' . $info->getSaveName());
                return json(['status' => 'success', 'file' => $info->getSaveName()]);
            }
        }
        return json(['status' => 'failure', 'error' => 'File upload failed']);
    }
}
