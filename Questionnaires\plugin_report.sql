CREATE TABLE IF NOT EXISTS `pt_plugin_reports` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `shop_link` varchar(500) NOT NULL DEFAULT '' COMMENT '店铺链接',
    `shop_name` varchar(255) NOT NULL DEFAULT '' COMMENT '店铺名称',
    `product_link` varchar(500) NOT NULL DEFAULT '' COMMENT '商品链接',
    `violation_type` varchar(100) NOT NULL DEFAULT '' COMMENT '违规类型',
    `proofs` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '证明材料JSON',
    `remark` text COMMENT '备注说明',
    `email` varchar(100) NOT NULL DEFAULT '' COMMENT '联系邮箱',
    `ip` varchar(50) DEFAULT '' COMMENT '提交IP',
    `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态 0未处理 1已处理 2已驳回',
    `reject_reason` text COMMENT '驳回原因',
    `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_email` (`email`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='违规商品举报表';