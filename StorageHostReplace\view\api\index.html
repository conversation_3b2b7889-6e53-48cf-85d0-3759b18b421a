<html>
 
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1.0" />
  <meta http-equiv="Cache-Control" content="max-age=31536000">

  <link rel="stylesheet" href="/static/others/element-plus/index.css">

  <title>数据迁移</title>
  <style>
    /* 加载动画样式 */
    #loading {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #fff;
      z-index: 9999;
    }

    .spinner {
      width: 50px;
      height: 50px;
      border: 5px solid rgba(0, 0, 0, 0.1);
      border-top-color: #3498db;
      border-radius: 50%;
      animation: spin 1s ease infinite;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }
  </style>
  <style>
    .progress-panel {
      background: #393d49;
      position: relative;
      min-height: 100px;
      line-height: 20px;
      resize: none;
      overflow: hidden;
      font-size: 12px;
      padding: 6px 10px;
      color: #fff;
      border-radius: 4px;
    }

    .title {
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .actions {
      margin-top: 16px;
    }

    .tips {
      font-size: 12px;
      color: #888;
    }
  </style>
</head>

<body>
  <!-- 加载动画容器 -->
  <div id="loading">
    <div class="spinner"></div>
  </div>

  <!-- Vue 应用挂载点 -->
  <div id="app" style="display: none">

    <el-card v-if="step== 1" shadow="never">

      <el-form :model="form" label-width="auto">
        <el-form-item label="替换后域名：">
          <el-input v-model="form.host" placeholder="请输入当前新域名" />
        </el-form-item>

      </el-form>


      <p style="text-align: center;margin: 0 auto;margin-top: 16px;">
        <el-button type="primary" :loading="isLoading" @click="searchHost">
          下一步
        </el-button>
      </p>
    </el-card>

    <el-card v-if="step== 2" shadow="never">
      <template #header>
        请勾选要替换的域名【请选择曾经使用过的域名】
      </template>
      <el-table :data="tableData" :height="400" style="width: 100%" @selection-change="handleSelectionChange">
        <el-table-column type="selection" align="center"></el-table-column>
        <el-table-column label="待替换域名">
          <template #default="{ row }">
            <span style="font-size: 12px;">{{row.host}}</span>
          </template>
        </el-table-column>
        <el-table-column label="替换后域名">
          <template #default="{ row }">
            <span style="font-size: 12px;">{{form.host}}</span>
          </template>
        </el-table-column>

        <el-table-column label="需替换数量" align="center">
          <template #default="{ row }">
            <span style="font-size: 12px;">{{row.count}}处</span>
          </template>
        </el-table-column>
      </el-table>
      <p style="text-align: center;margin: 0 auto;margin-top: 16px;">
        <el-button type="primary" :loading="isLoading" @click="startReplace">
          确定替换
        </el-button>
      </p>
    </el-card>
  </div>

  <script src="/static/others/vue/vue.global.prod.js"></script>
  <script src="/static/others/element-plus/index.full.min.js"></script>
  <script src="/static/others/axios/axios.min.js"></script>

  <script type="module">
    const { ref, reactive, watch } = Vue;
    const { ElMessage, ElMessageBox } = ElementPlus;
    const { Loading } = ElementPlus;

    const step = ref(1);

    const isLoading = ref(false);

    const { protocol, hostname, port } = window.location;


    const form = reactive({
      host: `${protocol}//${hostname}${port ? `:${port}` : ''}`,
    });


    const tableData = ref([]);
    const searchHost = async () => {
      isLoading.value = true;
      const res = await axios.post(
        "/plugin/StorageHostReplace/api/searchHost",
        form
      );
      isLoading.value = false;
      if (res.data?.code == 200) {
        tableData.value = res.data?.data;
        step.value = 2;
      } else {
        ElMessage.error(res.data?.msg);
      }
    }

    const multipleSelection = ref([])
    const handleSelectionChange = (val) => {
      multipleSelection.value = val
    }

    const startReplace = async () => {

      const hosts = multipleSelection.value.map((obj) => obj.host)
      if (hosts.length == 0) {
        ElMessage.error('请勾选条目');
        return false;
      }

      isLoading.value = true;
      const res = await axios.post(
        "/plugin/StorageHostReplace/api/startReplace",
        {
          host: form.host,
          target: hosts,
        }
      );
      isLoading.value = false;
      if (res.data?.code == 200) {
        ElMessage.success('替换成功');
        searchHost();
      } else {
        ElMessage.error(res.data?.msg);
      }
    }

    // Vue 应用
    const app = Vue.createApp({
      setup() {
        return {
          isLoading,
          searchHost,
          form,
          step,
          tableData,
          handleSelectionChange,
          startReplace,
        };
      },
    });

    app.use(ElementPlus);
    app.mount("#app");

    // 移除加载动画
    window.onload = () => {
      document.getElementById("loading").style.display = "none"; // 隐藏加载动画
      document.getElementById("app").style.display = "block"; // 显示 Vue 应用
    };
  </script>
</body>

</html>