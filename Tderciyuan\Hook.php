<?php
namespace plugin\Tderciyuan;

class Hook {

    public function handle(&$array) {
        try {
            // 读取状态配置
            $status = intval(plugconf("Tderciyuan.status") ?? 1);

            // 当status=1时才加载js
            if ($status == 1) {
                $user = $array[0];
                $array[1][] = plugstatic("Tderciyuan", 'live2d.js');   // 通过plugstatic函数获取插件内的js地址
            }
        } catch (\Exception $e) {
            // 静默处理错误，避免影响页面正常加载
            \think\facade\Log::error('Tderciyuan Hook error: ' . $e->getMessage());
        }
    }
} 