/*
- Jarallax
- Bootstrap Grid
- Scroll Spy
- <PERSON><PERSON>t
*/

/*--------------------------------------------------------------
/** Jarallax
--------------------------------------------------------------*/
.jarallax {
  position: relative;
  z-index: 0;
  min-height: 520px;
}
.jarallax > .jarallax-img {
  position: absolute;
  object-fit: cover;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}
.jarallax-keep-img {
  position: relative;
  z-index: 0;
}
.jarallax-keep-img > .jarallax-img {
    position: relative;
    display: block;
    max-width: 100%;
    height: auto;
    z-index: -100;
}


/*--------------------------------------------------------------
/** Bootstrap
--------------------------------------------------------------*/

/* scroll spy */

/* .scrollspy-nav {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.scrollspy-nav .scrollspy-link {
  transition: 0.3s ease;
  margin: 0px 10px;
  padding: 0px 15px;
  cursor: pointer;
}

.scrollspy-nav .scrollspy-link:hover {
  color: #C5A992;
}

.scrollspy-nav .scrollspy-indicator {
  position: absolute;
  height: 3px;
  width: 0;
  background: #C5A992;
  bottom: 0;
  left: 0;
  transition: width 0.3s, left 0.1s;
} */


/* tab-content*/
/* .tab-content {
    position: relative;
}
.tab-content .grid {
    flex-wrap: wrap;
    justify-content: flex-start;
}
[data-tab-content] {
    opacity: 0;
    visibility: hidden;
    height: 0;
}
.active[data-tab-content] {
    opacity: 1;
    visibility: visible;
    height: auto;
}
.tabs {
    display: flex;
    flex-wrap: wrap;
}
.tabs .tab {
    font-weight: 400;
    color: #053634;
    cursor: pointer;
}
.tabs .tab.active,
.tabs .tab:hover {
    color: #053634;
} */


/* Chocolat Lightbox */

.chocolat-zoomable.chocolat-zoomed {
    cursor: zoom-out;
}
.chocolat-open {
    overflow: hidden;
}
.chocolat-overlay {
    transition: opacity 0.4s ease, visibility 0s 0.4s ease;
    height: 100%;
    width: 100%;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 10;
    background-color: #000;
    visibility: hidden;
    opacity: 0;
}
.chocolat-overlay.chocolat-visible {
    transition: opacity 0.4s, visibility 0s;
    visibility: visible;
    opacity: 0.8;
}

.chocolat-wrapper {
    transition: opacity 0.4s ease, visibility 0s 0.4s ease;
    width: 100%;
    height: 100%;
    position: fixed;
    opacity: 0;
    left: 0;
    top: 0;
    z-index: 16;
    color: #fff;
    visibility: hidden;
}
.chocolat-wrapper.chocolat-visible {
    transition: opacity 0.4s, visibility 0s;
    opacity: 1;
    visibility: visible;
}

.chocolat-zoomable .chocolat-img {
    cursor: zoom-in;
}
.chocolat-loader {
    transition: opacity 0.3s;
    height: 32px;
    width: 32px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -16px;
    margin-top: -16px;
    z-index: 11;
    background: url(../images/chocolat/loader.gif);
    opacity: 0;
}
.chocolat-loader.chocolat-visible {
    opacity: 1;
}

.chocolat-image-wrapper {
    position: fixed;
    width: 0px;
    height: 0px;
    left: 50%;
    top: 50%;
    z-index: 14;
    text-align: left;
    transform: translate(-50%, -50%);
}

.chocolat-image-wrapper .chocolat-img {
    position: absolute;
    width: 100%;
    height: 100%;
}
.chocolat-wrapper .chocolat-left {
    width: 50px;
    height: 100px;
    cursor: pointer;
    background: url(../images/chocolat/left.png) 50% 50% no-repeat;
    z-index: 17;
    visibility: hidden;
}

.chocolat-layout {
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
}
.chocolat-image-canvas {
    transition: opacity .2s;
    opacity: 0;
    flex-grow: 1;
    align-self: stretch;
}
.chocolat-image-canvas.chocolat-visible {
    opacity: 1;
}
.chocolat-center {
    flex-grow: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    user-select: none;
}

.chocolat-wrapper .chocolat-right {
    width: 50px;
    height: 100px;
    cursor: pointer;
    background: url(../images/chocolat/right.png) 50% 50% no-repeat;
    z-index: 17;
    visibility: hidden;
}
.chocolat-wrapper .chocolat-right.active {
    visibility: visible;
}
.chocolat-wrapper .chocolat-left.active {
    visibility: visible;
}
.chocolat-wrapper .chocolat-top {
    height: 50px;
    overflow: hidden;
    z-index: 17;
    flex-shrink: 0;
}
.chocolat-wrapper .chocolat-close {
    width: 50px;
    height: 50px;
    cursor: pointer;
    position: absolute;
    top: 0;
    right: 0;
    background: url(../images/chocolat/close.png) 50% 50% no-repeat;
}
.chocolat-wrapper .chocolat-bottom {
    height: 40px;
    font-size: 12px;
    z-index: 17;
    padding-left: 15px;
    padding-right: 15px;
    background: rgba(0, 0, 0, 0.2);
    flex-shrink: 0;
    display: flex;
    align-items: center;

}
.chocolat-wrapper .chocolat-set-title {
    display: inline-block;
    padding-right: 15px;
    line-height: 1;
    border-right: 1px solid rgba(255, 255, 255, 0.3);
}
.chocolat-wrapper .chocolat-pagination {
    float: right;
    display: inline-block;
    padding-left: 15px;
    padding-right: 15px;
    margin-right: 15px;
    /*border-right: 1px solid rgba(255, 255, 255, 0.2);*/
}
.chocolat-wrapper .chocolat-fullscreen {
    width: 16px;
    height: 40px;
    background: url(../images/chocolat/fullscreen.png) 50% 50% no-repeat;
    display: block;
    cursor: pointer;
    float: right;
}
.chocolat-wrapper .chocolat-description {
    display: inline-block;
    flex-grow: 1;
    text-align: left;
}

/* no container mode*/
body.chocolat-open > .chocolat-overlay {
    z-index: 15;
}
body.chocolat-open > .chocolat-loader {
    z-index: 15;
}
body.chocolat-open > .chocolat-image-wrapper {
    z-index: 17;
}

/* container mode*/
.chocolat-in-container .chocolat-wrapper,
.chocolat-in-container .chocolat-image-wrapper,
.chocolat-in-container .chocolat-overlay {
    position: absolute;
}
.chocolat-in-container {
    position: relative;
}

.chocolat-zoomable.chocolat-zooming-in .chocolat-image-wrapper,
.chocolat-zoomable.chocolat-zooming-out .chocolat-image-wrapper {
    transition: width .2s ease, height .2s ease;
}
.chocolat-zoomable.chocolat-zooming-in .chocolat-img,
.chocolat-zoomable.chocolat-zooming-out .chocolat-img {
    transition: margin .2s ease;
}


/* Stellar Nav - jQuery Menu */

.stellarnav,
.stellarnav li{
  position:relative;line-height:normal;}
.stellarnav{
  width:100%;z-index:10;}
.stellarnav a{
  color:var(--accent-color);}
.stellarnav ul{
  margin:0;padding:0;text-align:right;}
.stellarnav li{
  list-style:none;display:block;margin:0;padding:0;vertical-align:middle;}
.stellarnav li a{
  padding:15px;display:block;text-decoration:none;color:var(--dark-color);font-size:inherit;font-family:inherit;box-sizing:border-box;-webkit-transition:all .3s ease-out;-moz-transition:all .3s ease-out;transition:all .3s ease-out;}
.stellarnav.light a,
.stellarnav.light li a{
  color:var(--light-color);}
.stellarnav>ul>li{
  display:inline-block;}
.stellarnav>ul>li>a{
  padding:10px 40px;}
.stellarnav ul ul{
  top:auto;width:220px;position:absolute;z-index:9900;text-align:left;display:none;background:var(--primary-color);}
.stellarnav.light ul ul{
  background:rgba(255,255,255,1);}
.stellarnav li li{
  display:block;}
.stellarnav ul ul ul{
  top:0;left:220px;}
.stellarnav>ul>li:hover>ul>li:hover>ul{
  opacity:1;visibility:visible;top:0;}
.stellarnav>ul>li.drop-left>ul{
  right:0;}
.stellarnav li.drop-left ul ul{
  left:auto;right:220px;}
.stellarnav.dark,
.stellarnav.dark ul ul{
  background:rgba(0,0,0,1);}
.stellarnav.dark a,
.stellarnav.dark li a{
  color:var(--light-color);}
.stellarnav.fixed{
  position:fixed;width:100%;top:0;left:0;z-index:11;background: #f3f2ec;padding: 10px 0;border-bottom: 1px solid #edebe4;}
body.stellarnav-noscroll-x{
  overflow-x:hidden;}
.stellarnav li.has-sub>a:after{
  content:'';margin-left:10px;border-left:6px solid transparent;border-right:6px solid transparent;border-top:6px solid #777;display:inline-block;}
.stellarnav .dd-toggle,
.stellarnav li.call-btn-mobile,
.stellarnav li.location-btn-mobile,
.stellarnav.hide-arrows li li.has-sub>a:after,
.stellarnav.hide-arrows li.drop-left li.has-sub>a:after,
.stellarnav.hide-arrows li.has-sub>a:after{
  display:none;}
.stellarnav li li.has-sub>a:after{
  margin-left:10px;float:right;border-top:6px solid transparent;border-bottom:6px solid transparent;border-left:6px solid #FFF;position:relative;top:4px;}
.stellarnav li.drop-left li.has-sub>a:after{
  float:left;margin-right:10px;border-left:0;border-top:6px solid transparent;border-bottom:6px solid transparent;border-right:6px solid #FFF;}
.stellarnav .call-btn-mobile,
.stellarnav .close-menu,
.stellarnav .location-btn-mobile,
.stellarnav .menu-toggle{
  display:none;text-transform:uppercase;text-decoration:none;}
.stellarnav .dd-toggle{
  position:absolute;top:0;right:0;padding:0;width:48px;height:48px;text-align:center;z-index:9999;border:0;}
.stellarnav.desktop li.has-sub a{
  padding-right:5px;}
.stellarnav.desktop.hide-arrows li.has-sub a{
  padding-right:15px;}
.stellarnav.mobile>ul>li>a.dd-toggle{
  padding:0;}
.stellarnav svg{
  fill:currentColor;width:1em;height:1em;position:relative;top:2px;}
.stellarnav a.dd-toggle .icon-plus{
  box-sizing:border-box;transition:transform .3s;width:12px;height:100%;position:relative;vertical-align:middle;display:inline-block;}
.stellarnav a.dd-toggle .icon-plus:after,
.stellarnav a.dd-toggle .icon-plus:before{
  content:'';display:block;height:0;top:50%;border-bottom:solid 3px #777;position:absolute;width:12px;}
.stellarnav a.dd-toggle .icon-plus:before{
  transform:rotate(90deg);transition:width .3s;}
.stellarnav li.open>a.dd-toggle .icon-plus{
  -webkit-transform:rotate(135deg);-ms-transform:rotate(135deg);-o-transform:rotate(135deg);transform:rotate(135deg);}
.stellarnav.light a.dd-toggle .icon-plus:after,
.stellarnav.light a.dd-toggle .icon-plus:before{
  border-color:var(--dark-color);}
.stellarnav.dark a.dd-toggle .icon-plus:after,
.stellarnav.dark a.dd-toggle .icon-plus:before{border-color:var(--light-color);}
.stellarnav .icon-close{
  box-sizing:border-box;width:12px;height:12px;position:relative;display:inline-block;}
.stellarnav .icon-close:after,
.stellarnav .icon-close:before{
  content:'';display:block;width:12px;height:0;top:50%;border-bottom:solid 3px var(--dark-grey-color);position:absolute;}
.stellarnav .icon-close:before{
  transform:rotate(45deg);}
.stellarnav .icon-close:after{
  transform:rotate(-45deg);}
.stellarnav.light .icon-close:after,
.stellarnav.light .icon-close:before{
  border-color:var(--dark-color);}
.stellarnav.dark .icon-close:after,
.stellarnav.dark .icon-close:before{
  border-color:var(--light-color);}
.stellarnav .call-btn-mobile,
.stellarnav .close-menu,
.stellarnav .location-btn-mobile,
.stellarnav .menu-toggle{
  padding:15px;box-sizing:border-box;}
.stellarnav .menu-toggle span.bars{
  display:inline-block;margin-right:7px;position:relative;top:3px;}
.stellarnav .menu-toggle span.bars span{
  display:block;width:15px;height:2px;border-radius:6px;background:#777;margin:0 0 3px;}
  .stellarnav .full{width:100%}.stellarnav .half{width:50%}.stellarnav .third{width:33%;text-align:center;}
.stellarnav .location-btn-mobile.third{
  text-align:center;}
.stellarnav .location-btn-mobile.half{
  text-align:right;}
.stellarnav.light .half,
.stellarnav.light .third{
  border-left:1px solid rgba(0,0,0,.15);}
.stellarnav.light.left .half,
.stellarnav.light.left .third,
.stellarnav.light.right .half,
.stellarnav.light.right .third{
  border-bottom:1px solid rgba(0,0,0,.15);}
.stellarnav.light .half:first-child,
.stellarnav.light .third:first-child{
  border-left:0;}
.stellarnav.dark .half,
.stellarnav.dark .third{
  border-left:1px solid rgba(255,255,255,.15);}
.stellarnav.dark.left .half,
.stellarnav.dark.left .third,
.stellarnav.dark.right .half,
.stellarnav.dark.right .third{
  border-bottom:1px solid rgba(255,255,255,.15);}
.stellarnav.dark.left .menu-toggle,
.stellarnav.dark.right .menu-toggle,
.stellarnav.light.left .menu-toggle,
.stellarnav.light.right .menu-toggle{
  border-bottom:0;}
.stellarnav.dark .half:first-child,
.stellarnav.dark .third:first-child{
  border-left:0;}
.stellarnav.light .menu-toggle span.bars span{
  background:var(--dark-color);}
.stellarnav.dark .menu-toggle span.bars span{
  background:var(--light-color);}
.stellarnav.mobile,
.stellarnav.mobile.fixed{
  position:static;}
.stellarnav.mobile ul{
  position:relative;display:none;text-align:left;background:rgba(221,221,221,1);}
.stellarnav.mobile.active>ul,
.stellarnav.mobile>ul>li{
  display:block;}
.stellarnav.mobile.active{
  padding-bottom:0;}
.stellarnav.mobile>ul>li>a{
  padding:15px;}
.stellarnav.mobile ul ul{
  position:relative;opacity:1;visibility:visible;width:auto;display:none;-moz-transition:none;-webkit-transition:none;-o-transition:color 0 ease-in;transition:none;}
.stellarnav.mobile ul ul ul{
  left:auto;top:auto;}
.stellarnav.mobile li.drop-left ul ul{
  right:auto;}
.stellarnav.mobile li a{
  border-bottom:1px solid rgba(255,255,255,.15);}
.stellarnav.mobile>ul{
  border-top:1px solid rgba(255,255,255,.15);}
.stellarnav.mobile.light li a{
  border-bottom:1px solid rgba(0,0,0,.15);}
.stellarnav.mobile.light>ul{
  border-top:1px solid rgba(0,0,0,.15);}
.stellarnav.mobile li a.dd-toggle,
.stellarnav.mobile.light li a.dd-toggle{
  border:0;}

.stellarnav.desktop li.mega ul ul{
  background:0 0;width:auto;}
.stellarnav.desktop li.mega li{
  display:inline-block;vertical-align:top;margin-left:-4px;}
.stellarnav.desktop li.mega li li{
  display:block;position:relative;left:4px;}
.stellarnav.desktop>ul>li.mega{
  position:inherit;}
.stellarnav.desktop>ul>li.mega>ul{
  width:100%;}
.stellarnav.desktop>ul>li.mega>ul li.has-sub ul{
  display:block;position:relative;left:auto;}
.stellarnav.desktop>ul>li.mega>ul>li{
  padding-bottom:15px;box-sizing:border-box;}
.stellarnav.desktop li.mega li li a{
  padding:5px 15px;}
.stellarnav.desktop li.mega li.has-sub a:after{
  display:none;}
.stellarnav.desktop>ul>li.mega>ul>li>a{
  color:#ff0;}

@media only screen and (max-width :768px){
  .stellarnav{overflow:hidden;display:block;}
  .stellarnav ul{position:relative;display:none;}
}

@media only screen and (max-width :420px){
  .stellarnav.mobile .call-btn-mobile.third span,
  .stellarnav.mobile .location-btn-mobile.third span{display:none;}
}


/*--------------------------------------------------------------
/** - Magnific Popup Css
--------------------------------------------------------------*/

.mfp-bg {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1042;
  overflow: hidden;
  position: fixed;
  background: #0b0b0b;
  opacity: 0.8; }

.mfp-wrap {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1043;
  position: fixed;
  outline: none !important;
  -webkit-backface-visibility: hidden; }

.mfp-container {
  text-align: center;
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  padding: 0 8px;
  box-sizing: border-box; }

.mfp-container:before {
  content: '';
  display: inline-block;
  height: 100%;
  vertical-align: middle; }

.mfp-align-top .mfp-container:before {
  display: none; }

.mfp-content {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  margin: 0 auto;
  text-align: left;
  z-index: 1045; }

.mfp-inline-holder .mfp-content,
.mfp-ajax-holder .mfp-content {
  width: 100%;
  cursor: auto; }

.mfp-ajax-cur {
  cursor: progress; }

.mfp-zoom-out-cur, .mfp-zoom-out-cur .mfp-image-holder .mfp-close {
  cursor: -moz-zoom-out;
  cursor: -webkit-zoom-out;
  cursor: zoom-out; }

.mfp-zoom {
  cursor: pointer;
  cursor: -webkit-zoom-in;
  cursor: -moz-zoom-in;
  cursor: zoom-in; }

.mfp-auto-cursor .mfp-content {
  cursor: auto; }

.mfp-close,
.mfp-arrow,
.mfp-preloader,
.mfp-counter {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none; }

.mfp-loading.mfp-figure {
  display: none; }

.mfp-hide {
  display: none !important; }

.mfp-preloader {
  color: #CCC;
  position: absolute;
  top: 50%;
  width: auto;
  text-align: center;
  margin-top: -0.8em;
  left: 8px;
  right: 8px;
  z-index: 1044; }
  .mfp-preloader a {
    color: #CCC; }
    .mfp-preloader a:hover {
      color: #FFF; }

.mfp-s-ready .mfp-preloader {
  display: none; }

.mfp-s-error .mfp-content {
  display: none; }

button.mfp-close,
button.mfp-arrow {
  overflow: visible;
  cursor: pointer;
  background: transparent;
  border: 0;
  -webkit-appearance: none;
  display: block;
  outline: none;
  padding: 0;
  z-index: 1046;
  box-shadow: none;
  touch-action: manipulation; }

button::-moz-focus-inner {
  padding: 0;
  border: 0; }

.mfp-close {
  width: 44px;
  height: 44px;
  line-height: 44px;
  position: absolute;
  right: 0;
  top: 0;
  text-decoration: none;
  text-align: center;
  opacity: 0.65;
  padding: 0 0 18px 10px;
  color: #FFF;
  font-style: normal;
  font-size: 28px;
  font-family: Arial, Baskerville, monospace; }
  .mfp-close:hover,
  .mfp-close:focus {
    opacity: 1; }
  .mfp-close:active {
    top: 1px; }

.mfp-close-btn-in .mfp-close {
  color: #333; }

.mfp-image-holder .mfp-close,
.mfp-iframe-holder .mfp-close {
  color: #FFF;
  right: 0px;
  text-align: right;
  padding-right: 20px;
  width: 100%;
  top: 40px;
  background: #020000;
  opacity: 0.7;
  font-size: 2.5em;
}

.mfp-counter {
  position: absolute;
  top: 0;
  right: 0;
  color: #CCC;
  font-size: 12px;
  line-height: 18px;
  white-space: nowrap; }

.mfp-arrow {
  position: absolute;
  opacity: 0.65;
  margin: 0;
  top: 50%;
  margin-top: -55px;
  padding: 0;
  width: 90px;
  height: 110px;
  -webkit-tap-highlight-color: transparent; }
  .mfp-arrow:active {
    margin-top: -54px; }
  .mfp-arrow:hover,
  .mfp-arrow:focus {
    opacity: 1; }
  .mfp-arrow:before,
  .mfp-arrow:after {
    content: '';
    display: block;
    width: 0;
    height: 0;
    position: absolute;
    left: 0;
    top: 0;
    margin-top: 35px;
    margin-left: 35px;
    border: medium inset transparent; }
  .mfp-arrow:after {
    border-top-width: 13px;
    border-bottom-width: 13px;
    top: 8px; }
  .mfp-arrow:before {
    border-top-width: 21px;
    border-bottom-width: 21px;
    opacity: 0.7; }

.mfp-arrow-left {
  left: 0; }
  .mfp-arrow-left:after {
    border-right: 17px solid #FFF;
    margin-left: 31px; }
  .mfp-arrow-left:before {
    margin-left: 25px;
    border-right: 27px solid #3F3F3F; }

.mfp-arrow-right {
  right: 0; }
  .mfp-arrow-right:after {
    border-left: 17px solid #FFF;
    margin-left: 39px; }
  .mfp-arrow-right:before {
    border-left: 27px solid #3F3F3F; }

.mfp-iframe-holder {
  padding-top: 40px;
  padding-bottom: 40px; }
  .mfp-iframe-holder .mfp-content {
    line-height: 0;
    width: 100%;
    max-width: 900px; }
  .mfp-iframe-holder .mfp-close {
    top: -40px; }

.mfp-iframe-scaler {
  width: 100%;
  height: 0;
  overflow: hidden;
  padding-top: 56.25%; }
  .mfp-iframe-scaler iframe {
    position: absolute;
    display: block;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.6);
    background: #000; }

/* Main image in popup */
img.mfp-img {
  width: auto;
  max-width: 100%;
  height: auto;
  display: block;
  line-height: 0;
  box-sizing: border-box;
  padding: 40px 0 40px;
  margin: 0 auto; }

/* The shadow behind the image */
.mfp-figure {
  line-height: 0; }
  .mfp-figure:after {
    content: '';
    position: absolute;
    left: 0;
    top: 40px;
    bottom: 40px;
    display: block;
    right: 0;
    width: auto;
    height: auto;
    z-index: -1;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.6);
    background: #444; }
  .mfp-figure small {
    color: #BDBDBD;
    display: block;
    font-size: 12px;
    line-height: 14px; }
  .mfp-figure figure {
    margin: 0; }

.mfp-bottom-bar {
  margin-top: -36px;
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  cursor: auto; }

.mfp-title {
  text-align: left;
  line-height: 18px;
  color: #F3F3F3;
  word-wrap: break-word;
  padding-right: 36px; }

.mfp-image-holder .mfp-content {
  max-width: 100%; }

.mfp-gallery .mfp-image-holder .mfp-figure {
  cursor: pointer; }

@media screen and (max-width: 800px) and (orientation: landscape), screen and (max-height: 300px) {
  /**
       * Remove all paddings around the image on small screen
       */
  .mfp-img-mobile .mfp-image-holder {
    padding-left: 0;
    padding-right: 0; }
  .mfp-img-mobile img.mfp-img {
    padding: 0; }
  .mfp-img-mobile .mfp-figure:after {
    top: 0;
    bottom: 0; }
  .mfp-img-mobile .mfp-figure small {
    display: inline;
    margin-left: 5px; }
  .mfp-img-mobile .mfp-bottom-bar {
    background: rgba(0, 0, 0, 0.6);
    bottom: 0;
    margin: 0;
    top: auto;
    padding: 3px 5px;
    position: fixed;
    box-sizing: border-box; }
    .mfp-img-mobile .mfp-bottom-bar:empty {
      padding: 0; }
  .mfp-img-mobile .mfp-counter {
    right: 5px;
    top: 3px; }
  .mfp-img-mobile .mfp-close {
    top: 0;
    right: 0;
    width: 35px;
    height: 35px;
    line-height: 35px;
    background: rgba(0, 0, 0, 0.6);
    position: fixed;
    text-align: center;
    padding: 0; } }

@media all and (max-width: 900px) {
  .mfp-arrow {
    -webkit-transform: scale(0.75);
    transform: scale(0.75); }
  .mfp-arrow-left {
    -webkit-transform-origin: 0;
    transform-origin: 0; }
  .mfp-arrow-right {
    -webkit-transform-origin: 100%;
    transform-origin: 100%; }
  .mfp-container {
    padding-left: 6px;
    padding-right: 6px; } }

