<?php

namespace app\index\test\controller;

use app\common\controller\BaseIndex;

class Index extends BaseIndex {

    public function index() {

        //获取菜单：get_nav();
        //获取当前模板编码：get_template_code();
        //读取当前模板自定义的配置：$params = get_template_params();      echo $params->url;

        return view('', [
            'title' => sysconf("website.title"),
            'params' => (array)get_template_params(),
        ]);
    }
}
