<?php

namespace plugin\Orderdisplay\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Log;
use think\facade\Db;

class Api extends BasePlugin {

    protected $scene = ['admin'];  // 只允许管理员访问
    
    protected $noNeedLogin = [];

    public function index() {
        return View::fetch();
    }

    // 获取配置
    public function getConfig() {
        try {
            $configFile = app()->getRootPath() . 'plugin/Orderdisplay/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            if (!isset($config['orderdisplay_config'])) {
                $config['orderdisplay_config'] = [
                    'status' => true,
                    'need_password' => false,
                    'password' => '',
                    'desensitization' => false,
                ];
            }

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $config['orderdisplay_config']
            ]);
        } catch (\Exception $e) {
            Log::error('获取配置失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取配置失败：' . $e->getMessage()]);
        }
    }

    // 保存配置
    public function saveConfig() {
        try {
            $data = [
                'status' => input('status/b', true),
                'need_password' => input('need_password/b', false),
                'password' => input('password/s', ''),
                'desensitization' => input('desensitization/b', false),
            ];
            
            $configFile = app()->getRootPath() . 'plugin/Orderdisplay/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            $config['orderdisplay_config'] = $data;
            
            if (!file_put_contents($configFile, "<?php\nreturn " . var_export($config, true) . ";\n")) {
                throw new \Exception('保存配置失败');
            }
            
            return json(['code' => 200, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            Log::error('保存配置失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }
} 