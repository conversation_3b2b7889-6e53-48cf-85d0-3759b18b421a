// 创建容器div
var container = document.createElement('div');
container.id = 'balanceInquiryContainer';
container.style.position = 'fixed';
container.style.top = '0';
container.style.left = '0';
container.style.width = '100%';
container.style.height = '100%';
container.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
container.style.zIndex = '9999';
container.style.display = 'none';

// 创建内部容器
var innerContainer = document.createElement('div');
innerContainer.style.position = 'relative';
innerContainer.style.width = '80%';
innerContainer.style.maxWidth = '1200px';
innerContainer.style.margin = '20px auto';
innerContainer.style.backgroundColor = '#fff';
innerContainer.style.borderRadius = '4px';
innerContainer.style.boxShadow = '0 2px 12px 0 rgba(0, 0, 0, 0.1)';
innerContainer.style.height = 'calc(100vh - 40px)';

// 创建标题栏
var header = document.createElement('div');
header.style.padding = '15px 20px';
header.style.borderBottom = '1px solid #ebeef5';
header.style.display = 'flex';
header.style.justifyContent = 'space-between';
header.style.alignItems = 'center';

var title = document.createElement('div');
title.textContent = '余额明细';
title.style.fontSize = '18px';
title.style.color = '#303133';

var closeButton = document.createElement('button');
closeButton.innerHTML = '×';
closeButton.style.border = 'none';
closeButton.style.background = 'none';
closeButton.style.fontSize = '20px';
closeButton.style.cursor = 'pointer';
closeButton.style.color = '#909399';
closeButton.onclick = function() {
    container.style.display = 'none';
};

header.appendChild(title);
header.appendChild(closeButton);

// 创建内容区域
var content = document.createElement('div');
content.style.padding = '0';
content.style.height = 'calc(100vh - 40px - 51px)';
content.style.overflow = 'hidden';

// 创建 iframe 来加载内容
var iframe = document.createElement('iframe');
iframe.style.width = '100%';
iframe.style.height = '100%';
iframe.style.border = 'none';
iframe.style.overflow = 'hidden';
iframe.src = '/plugin/Balanceinquiry/api/index';

// 将 iframe 添加到内容区域
content.appendChild(iframe);

// 组装对话框
innerContainer.appendChild(header);
innerContainer.appendChild(content);
container.appendChild(innerContainer);

// 将容器添加到页面
document.body.appendChild(container);

// 修改插入按钮的函数和观察逻辑
function initBalanceInquiry() {
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            insertBalanceButton();
            observeDOMChanges();
        });
    } else {
        insertBalanceButton();
        observeDOMChanges();
    }
}

function insertBalanceButton() {
    try {
        // 检查按钮是否已存在，避免重复插入
        if (document.querySelector('.balance-inquiry-btn')) {
            return;
        }

        const targetSpan = document.querySelector('span[class="el-text el-text--small"]');
        if (targetSpan) {
            const button = document.createElement('button');
            button.className = 'el-button el-button--small balance-inquiry-btn'; // 添加唯一类名
            button.style.marginRight = '10px';
            button.style.backgroundColor = '#ecf5ff';
            button.style.color = '#409eff';
            button.style.border = '1px solid #b3d8ff';
            button.innerHTML = '余额查询';
            button.onclick = function() {
                container.style.display = 'block';
            };
            
            button.onmouseover = function() {
                this.style.backgroundColor = '#dcecff';
                this.style.borderColor = '#409eff';
            };
            button.onmouseout = function() {
                this.style.backgroundColor = '#ecf5ff';
                this.style.borderColor = '#b3d8ff';
            };
            
            targetSpan.parentNode.insertBefore(button, targetSpan);
        }
    } catch (error) {
        console.error('插入余额查询按钮失败:', error);
    }
}

function observeDOMChanges() {
    const pageObserver = new MutationObserver((mutations) => {
        // 检查当前路径是否包含 finance/cash
        if (window.location.hash.includes('/finance/cash')) {
            const targetSpan = document.querySelector('span[class="el-text el-text--small"]');
            const existingButton = document.querySelector('.balance-inquiry-btn');
            
            if (targetSpan && !existingButton) {
                insertBalanceButton();
            }
        }
    });

    pageObserver.observe(document.body, {
        childList: true,
        subtree: true
    });

    // 监听 hash 变化
    window.addEventListener('hashchange', () => {
        if (window.location.hash.includes('/finance/cash')) {
            setTimeout(() => {
                insertBalanceButton();
            }, 500); // 给DOM一些渲染时间
        }
    });
}

// 初始化函数调用
initBalanceInquiry();
