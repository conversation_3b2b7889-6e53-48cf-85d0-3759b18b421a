<?php

namespace plugin\Riskcontrol\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;
use AlibabaCloud\SDK\Green\Green;
use AlibabaCloud\SDK\Green\Models\TextScanRequest;
use AlibabaCloud\SDK\Green\Models\TextScanResponse;
use AlibabaCloud\SDK\Green\Models\TextModerationRequest;
use AlibabaCloud\SDK\Green\Models\TextModerationResponse;
use AlibabaCloud\SDK\Green\Models\ClientProfile;
use AlibabaCloud\SDK\Green\Models\HttpProfile;
use AlibabaCloud\SDK\Green\Models\Credential;
use AlibabaCloud\SDK\Green\Models\TmsClient;
use Baidu\Aip\AipContentCensor;
use TencentCloud\Common\Credential as TencentCredential;
use TencentCloud\Common\Profile\ClientProfile as TencentClientProfile;
use TencentCloud\Common\Profile\HttpProfile as TencentHttpProfile;
use TencentCloud\Tms\*********\TmsClient as TencentTmsClient;
use TencentCloud\Tms\*********\Models\TextModerationRequest as TencentTextModerationRequest;

class Api extends BasePlugin
{
    protected $scene = ['admin'];
    protected $noNeedLogin = [];

    public function index()
    {
        return View::fetch();
    }

    // 获取配置
    public function fetchData()
    {
        $data = [
            'status' => intval(plugconf("Riskcontrol.status") ?? 0),
            
            // 阿里云配置
            'aliyun_status' => intval(plugconf("Riskcontrol.aliyun_status") ?? 0),
            'aliyun_access_key' => plugconf("Riskcontrol.aliyun_access_key"),
            'aliyun_access_secret' => plugconf("Riskcontrol.aliyun_access_secret"),
            'aliyun_region' => plugconf("Riskcontrol.aliyun_region"),
            
            // 腾讯云配置
            'tencent_status' => intval(plugconf("Riskcontrol.tencent_status") ?? 0),
            'tencent_secret_id' => plugconf("Riskcontrol.tencent_secret_id"),
            'tencent_secret_key' => plugconf("Riskcontrol.tencent_secret_key"),
            'tencent_region' => plugconf("Riskcontrol.tencent_region"),
            
            // 百度云配置
            'baidu_status' => intval(plugconf("Riskcontrol.baidu_status") ?? 0),
            'baidu_app_id' => plugconf("Riskcontrol.baidu_app_id"),
            'baidu_api_key' => plugconf("Riskcontrol.baidu_api_key"),
            'baidu_secret_key' => plugconf("Riskcontrol.baidu_secret_key"),
            
            // 处理配置
            'action_type' => plugconf("Riskcontrol.action_type") ?? 'mark',
            'notify_admin' => intval(plugconf("Riskcontrol.notify_admin") ?? 1),
            'notify_content' => plugconf("Riskcontrol.notify_content")
        ];
        
        $this->success('success', $data);
    }

    // 保存配置
    public function saveConfig()
    {
        $status = $this->request->post('status/d', 0);
        
        // 保存基础配置
        plugconf("Riskcontrol.status", $status);
        
        // 保存云服务配置
        $this->saveCloudConfig('aliyun');
        $this->saveCloudConfig('tencent'); 
        $this->saveCloudConfig('baidu');
        
        // 保存处理配置
        $actionType = $this->request->post('action_type/s', 'mark');
        $notifyAdmin = $this->request->post('notify_admin/d', 1);
        $notifyContent = $this->request->post('notify_content/s', '');
        
        plugconf("Riskcontrol.action_type", $actionType);
        plugconf("Riskcontrol.notify_admin", $notifyAdmin);
        plugconf("Riskcontrol.notify_content", $notifyContent);
        
        $this->success('保存成功');
    }

    // 手动检测
    public function manualCheck() 
    {
        try {
            $hook = new \plugin\Riskcontrol\Hook();
            $result = $hook->checkGoods();
            
            return json([
                'code' => 200,
                'msg' => "检测完成\n" . $result
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '检测失败:' . $e->getMessage()
            ]);
        }
    }

    private function saveCloudConfig($platform)
    {
        $status = $this->request->post("{$platform}_status/d", 0);
        plugconf("Riskcontrol.{$platform}_status", $status);
        
        if ($status) {
            // 保存密钥配置
            if ($platform == 'aliyun') {
                plugconf("Riskcontrol.{$platform}_access_key", $this->request->post('aliyun_access_key'));
                plugconf("Riskcontrol.{$platform}_access_secret", $this->request->post('aliyun_access_secret'));
                plugconf("Riskcontrol.{$platform}_region", $this->request->post('aliyun_region'));
            } else if ($platform == 'tencent') {
                plugconf("Riskcontrol.{$platform}_secret_id", $this->request->post('tencent_secret_id'));
                plugconf("Riskcontrol.{$platform}_secret_key", $this->request->post('tencent_secret_key')); 
                plugconf("Riskcontrol.{$platform}_region", $this->request->post('tencent_region'));
            } else if ($platform == 'baidu') {
                plugconf("Riskcontrol.{$platform}_app_id", $this->request->post('baidu_app_id'));
                plugconf("Riskcontrol.{$platform}_api_key", $this->request->post('baidu_api_key'));
                plugconf("Riskcontrol.{$platform}_secret_key", $this->request->post('baidu_secret_key'));
            }
        }
    }

    // 阿里云文本检测
    public function aliyunCheck($text)
    {
        try {
            $accessKeyId = plugconf("Riskcontrol.aliyun_access_key");
            $accessSecret = plugconf("Riskcontrol.aliyun_access_secret");
            $region = plugconf("Riskcontrol.aliyun_region");

            if (empty($accessKeyId) || empty($accessSecret)) {
                return $this->error('阿里云配置不完整');
            }

            AlibabaCloud::accessKeyClient($accessKeyId, $accessSecret)
                ->regionId($region)
                ->asDefaultClient();

            $result = AlibabaCloud::green()
                ->v20180509()
                ->textScan()
                ->body([
                    'tasks' => [
                        [
                            'content' => $text
                        ]
                    ],
                    'scenes' => ['antispam']
                ])
                ->request();

            if (!empty($result['data'])) {
                foreach ($result['data'] as $task) {
                    if ($task['results'][0]['suggestion'] !== 'pass') {
                        return $this->success('检测完成', [
                            'violation' => true,
                            'message' => $task['results'][0]['label']
                        ]);
                    }
                }
            }
            
            return $this->success('检测通过', [
                'violation' => false
            ]);

        } catch (\Exception $e) {
            return $this->error('阿里云检测失败: ' . $e->getMessage());
        }
    }

    // 腾讯云文本检测
    public function tencentCheck($text)
    {
        try {
            $secretId = plugconf("Riskcontrol.tencent_secret_id");
            $secretKey = plugconf("Riskcontrol.tencent_secret_key");
            $region = plugconf("Riskcontrol.tencent_region");

            if (empty($secretId) || empty($secretKey)) {
                return $this->error('腾讯云配置不完整');
            }

            // 引入腾讯云SDK
            require_once root_path() . 'vendor/tencentcloud/tencentcloud-sdk-php/TCloudAutoLoader.php';

            $cred = new TencentCredential($secretId, $secretKey);
            $httpProfile = new TencentHttpProfile();
            $httpProfile->setEndpoint("tms.tencentcloudapi.com");

            $clientProfile = new TencentClientProfile();
            $clientProfile->setHttpProfile($httpProfile);
            $clientProfile->setSignMethod("TC3-HMAC-SHA256");  // 设置签名方法
            
            $client = new TencentTmsClient($cred, $region, $clientProfile);
            
            $req = new TencentTextModerationRequest();
            $params = [
                "Content" => base64_encode($text),
                "Device" => [
                    "IP" => $_SERVER['REMOTE_ADDR'] ?? "127.0.0.1"
                ]
            ];
            $req->fromJsonString(json_encode($params));
            
            $resp = $client->TextModeration($req);
            $respData = json_decode($resp->toJsonString(), true);
            
            if (isset($respData['Suggestion']) && $respData['Suggestion'] !== "Pass") {
                return $this->success('检测完成', [
                    'violation' => true,
                    'message' => $respData['Label'] ?? '内容违规'
                ]);
            }
            
            return $this->success('检测通过', [
                'violation' => false
            ]);

        } catch (\Exception $e) {
            return $this->error('腾讯云检测失败: ' . $e->getMessage());
        }
    }

    // 百度云文本检测
    public function baiduCheck($text)
    {
        try {
            $appId = plugconf("Riskcontrol.baidu_app_id");
            $apiKey = plugconf("Riskcontrol.baidu_api_key");
            $secretKey = plugconf("Riskcontrol.baidu_secret_key");

            if (empty($appId) || empty($apiKey) || empty($secretKey)) {
                return $this->error('百度云配置不完整');
            }

            $client = new AipContentCensor($appId, $apiKey, $secretKey);
            $result = $client->textCensorUserDefined($text);

            if (isset($result['conclusionType']) && $result['conclusionType'] !== 1) {
                $message = !empty($result['data']) ? $result['data'][0]['msg'] : '内容违规';
                return $this->success('检测完成', [
                    'violation' => true,
                    'message' => $message
                ]);
            }
            
            return $this->success('检测通过', [
                'violation' => false
            ]);

        } catch (\Exception $e) {
            return $this->error('百度云检测失败: ' . $e->getMessage());
        }
    }

    // 测试文本检测接口
    public function testCheck()
    {
        $platform = $this->request->post('platform');
        $text = $this->request->post('text');

        if (empty($text)) {
            return $this->error('请输入测试文本');
        }

        switch ($platform) {
            case 'aliyun':
                return $this->aliyunCheck($text);
            case 'tencent':
                return $this->tencentCheck($text);
            case 'baidu':
                return $this->baiduCheck($text);
            default:
                return $this->error('不支持的平台');
        }
    }
} 