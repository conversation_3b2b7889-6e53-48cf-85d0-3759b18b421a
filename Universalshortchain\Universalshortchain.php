<?php

namespace plugin\Universalshortchain;
 
use app\common\library\Plugin;
use app\common\model\DwzApi as DwzApiModel;
use app\common\service\HttpService;

class Universalshortchain extends Plugin {

    /**
     * 插件安装方法
     * @return bool
     */
    public function install() {
        try {
            $model = DwzApiModel::where(['code' => 'Universalshortchain'])->find();
            if (!$model) {
                $model = new DwzApiModel();
            }
            $model->code = 'Universalshortchain';
            $model->name = '博天通用短链接';
            $model->tips = '';
            $model->website = 'https://t.aojiasuq.top/';
            
            // 保存数据并记录结果
            $result = $model->save();
            
            // 检查保存结果
            if ($result) {
                trace("Universalshortchain插件安装成功: 数据已保存", 'info');
                return true;
            } else {
                trace("Universalshortchain插件安装失败: 数据保存返回false", 'error');
                return false;
            }
        } catch (\Exception $e) {
            trace("Universalshortchain插件安装错误: " . $e->getMessage(), 'error');
            return false;
        }
    }

    /**
     * 插件卸载方法
     * @return bool
     */
    public function uninstall() {
        try {
            $model = DwzApiModel::where(['code' => 'Universalshortchain'])->find();
            if ($model) {
                $model->delete();
            }
            return true;
        } catch (\Exception $e) {
            // 记录错误但仍返回成功以允许卸载继续
            trace("Universalshortchain卸载错误: " . $e->getMessage(), 'error');
            return true;
        }
    }

    /**
     * 生成短链接
     * @param string $url 需要缩短的长网址
     * @return string|false
     */
    public function create($url) {
        $token = plugconf('Universalshortchain.token');
        $domain = plugconf('Universalshortchain.domain');
        if (empty($token) || empty($domain)) {
            return false;
        }

        $params = [
            'type' => plugconf('Universalshortchain.type'),
            'pattern' => plugconf('Universalshortchain.pattern'),
            'token' => $token,
            'url' => $url
        ];

        $request_url = rtrim($domain, '/') . '/api/url.php?type=' . $params['type'] 
                    . '&pattern=' . $params['pattern']
                    . '&token=' . $params['token']
                    . '&url=' . urlencode($params['url']);
        
        $res = HttpService::get($request_url);
        
        if ($res === false) {
            return false;
        }
        
        $json = json_decode($res, true);
        
        if (!$json) {
            return false;
        }

        if (isset($json['code']) && $json['code'] != 200) {
            return false;
        }

        if (!isset($json['dwz'])) {
            return false;
        }

        return $json['dwz'];
    }
} 