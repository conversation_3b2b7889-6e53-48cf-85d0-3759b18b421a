<?php

namespace plugin\Goandpay;

class Hook
{
    /**
     * ShopSettingBefore钩子函数 - 商户后台设置店铺信息前
     * 
     * @param \app\common\model\User $user 用户模型，包含店铺信息
     */
    public function ShopSettingBefore($user)
    {
        // 在店铺设置前获取店铺信息，可以在这里进行处理
    }
    
    /**
     * ShopSettingAfter钩子函数 - 商户后台设置店铺信息后
     * 
     * @param \app\common\model\User $user 用户模型，包含店铺信息
     */
    public function ShopSettingAfter($user)
    {
        // 在店铺设置后获取店铺信息，可以在这里进行处理
    }
    
    /**
     * 处理ShopJs钩子
     * 
     * @param array $array 包含用户信息和JS列表的数组
     */
    public function handle(&$array) {
        $user = $array[0];   
        $array[1][] = plugstatic("Goandpay", 'payment.js');   // 通过plugstatic函数获取插件内的js地址
    }
} 