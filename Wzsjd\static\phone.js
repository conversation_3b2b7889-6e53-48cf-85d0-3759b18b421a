(function() {
    // 添加判断是否为移动设备的函数
    function isMobileDevice() {
        return (window.innerWidth <= 768) || 
               /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    function initNavigation() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                createNavigation();
                setupRefreshListener();
            });
        } else {
            createNavigation();
            setupRefreshListener();
        }
    }

    // 添加刷新监听器
    function setupRefreshListener() {
        // 检查是否有刷新信号
        function checkForRefresh() {
            try {
                const refreshData = localStorage.getItem('navigation_refresh');
                if (refreshData) {
                    const data = JSON.parse(refreshData);
                    const now = new Date().getTime();
                    
                    // 如果刷新信号是最近5分钟内的
                    if (data.type === 'REFRESH_NAVIGATION' && (now - data.timestamp < 300000)) {
                        // 清除信号
                        localStorage.removeItem('navigation_refresh');
                        
                        // 刷新导航
                        initNavigation();
                    }
                }
            } catch (e) {
                // 静默处理错误
            }
        }
        
        // 立即检查一次
        checkForRefresh();
        
        // 每分钟检查一次
        setInterval(checkForRefresh, 60000);
        
        // 监听storage事件
        window.addEventListener('storage', (event) => {
            if (event.key === 'navigation_refresh') {
                checkForRefresh();
            }
        });
    }

    async function createNavigation() {
        try {
            const response = await fetch('/plugin/Wzsjd/Api/fetchData', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            const data = await response.json();
            if (!data || typeof data !== 'object') {
                return;
            }
            
            if (data.code === 200 && data.data) {
                // 确保使用严格比较，并且检查status是否为1
                const status = parseInt(data.data.status);
                
                if (status === 1) {
                    showNavigation(data.data);
                }
            }
        } catch (error) {
            // 静默处理错误
        }
    }

    function showNavigation(config) {
        try {
            if (!config || typeof config !== 'object') {
                return;
            }

            let icons = [];
            try {
                icons = JSON.parse(config.icons || '[]');
            } catch (e) {
                icons = [];
            }

            if (icons.length === 0) return;
            
            // 检测是否为移动设备
            const isMobile = isMobileDevice();
            
            // 根据设备和配置决定显示方式
            const navigationBar = isMobile ? 
                createMobileNavigationBar(icons, config) :
                createDesktopNavigationBar(icons, config);
                
            insertNavigation(navigationBar, isMobile);
            observeDOMChanges(navigationBar);
        } catch (error) {
            // 静默处理错误
        }
    }

    // 创建移动设备导航栏
    function createMobileNavigationBar(icons, config) {
        // 直接使用与订单管理区块完全相同的HTML结构
        const navigationContainer = document.createElement('div');
        navigationContainer.className = 'router-container mt-8';
        navigationContainer.setAttribute('data-v-5d7f7bec', '');
        navigationContainer.setAttribute('data-nav', 'wzsjd-mobile');
        
        // 添加标题头部
        const headerContainer = document.createElement('div');
        headerContainer.className = 'router-container-header';
        headerContainer.setAttribute('data-v-5d7f7bec', '');
        headerContainer.textContent = config.nav_title || '快捷导航';
        navigationContainer.appendChild(headerContainer);
        
        // 为每个图标创建元素，完全匹配示例中的DOM结构
        icons.slice(0, config.max_icons || 5).forEach(item => {
            const iconItem = document.createElement('div');
            iconItem.className = 'item';
            iconItem.setAttribute('data-v-5d7f7bec', '');
            
            // 插入SVG图标
            const iconSvg = document.createElement('div');
            iconSvg.innerHTML = item.icon;
            // 确保SVG有正确的属性
            const svg = iconSvg.querySelector('svg');
            if (svg) {
                svg.classList.add('icon');
                svg.setAttribute('data-v-5d7f7bec', '');
            }
            
            // 添加图标
            iconItem.appendChild(iconSvg.firstChild);
            
            // 添加文本，支持|符号换行
            const textContent = item.name.replace(/\|/g, '\n');
            const textNode = document.createElement('span');
            textNode.style.whiteSpace = 'pre-line'; // 支持换行显示
            textNode.textContent = ' ' + textContent + ' ';
            iconItem.appendChild(textNode);
            
            // 添加链接点击事件
            if (item.link) {
                iconItem.addEventListener('click', () => {
                    window.location.href = item.link;
                });
            }
            
            // 将图标项添加到导航容器
            navigationContainer.appendChild(iconItem);
        });
        
        return navigationContainer;
    }

    // 创建桌面设备导航栏（与移动设备完全相同，只是可能显示更多图标）
    function createDesktopNavigationBar(icons, config) {
        // 直接使用与订单管理区块完全相同的HTML结构
        const navigationContainer = document.createElement('div');
        navigationContainer.className = 'router-container mt-8';
        navigationContainer.setAttribute('data-v-5d7f7bec', '');
        navigationContainer.setAttribute('data-nav', 'wzsjd');
        
        // 添加标题头部
        const headerContainer = document.createElement('div');
        headerContainer.className = 'router-container-header';
        headerContainer.setAttribute('data-v-5d7f7bec', '');
        headerContainer.textContent = config.nav_title || '快捷导航';
        navigationContainer.appendChild(headerContainer);
        
        // 为每个图标创建元素，完全匹配示例中的DOM结构
        icons.slice(0, config.max_icons || 10).forEach(item => {
            const iconItem = document.createElement('div');
            iconItem.className = 'item';
            iconItem.setAttribute('data-v-5d7f7bec', '');
            
            // 插入SVG图标
            const iconSvg = document.createElement('div');
            iconSvg.innerHTML = item.icon;
            // 确保SVG有正确的属性
            const svg = iconSvg.querySelector('svg');
            if (svg) {
                svg.classList.add('icon');
                svg.setAttribute('data-v-5d7f7bec', '');
            }
            
            // 添加图标
            iconItem.appendChild(iconSvg.firstChild);
            
            // 添加文本，支持|符号换行
            const textContent = item.name.replace(/\|/g, '\n');
            const textNode = document.createElement('span');
            textNode.style.whiteSpace = 'pre-line'; // 支持换行显示
            textNode.textContent = ' ' + textContent + ' ';
            iconItem.appendChild(textNode);
            
            // 添加链接点击事件
            if (item.link) {
                iconItem.addEventListener('click', () => {
                    window.location.href = item.link;
                });
            }
            
            // 将图标项添加到导航容器
            navigationContainer.appendChild(iconItem);
        });
        
        return navigationContainer;
    }

    // 不再需要自定义样式表，因为我们直接使用了页面的样式类
    function addStyleSheet(config) {
        // 样式已经由页面的CSS提供，不需要额外添加
        return;
    }

    function insertNavigation(navigation, isMobile) {
        // 找到layout-container作为插入目标
        const layoutContainer = document.querySelector('div[data-v-5d7f7bec][data-v-68e8e1da].layout-container');
        
        if (!layoutContainer) {
            // 静默处理错误
            return;
        }
        
        // 首先检查是否已经存在导航元素，防止重复添加
        if (isMobile) {
            const existingNav = document.querySelector('[data-nav="wzsjd-mobile"]');
            if (existingNav) {
                existingNav.remove();
            }
        } else {
            const existingNav = document.querySelector('[data-nav="wzsjd"]');
            if (existingNav) {
                existingNav.remove();
            }
        }
        
        // 插入到layout-container的末尾
        layoutContainer.appendChild(navigation);
    }

    function findTargetElement() {
        // 1. 首先尝试匹配用户提供的特定结构：带有data-v-5d7f7bec属性的.router-container.mt-8
        const routerContainers = document.querySelectorAll('.router-container.mt-8[data-v-5d7f7bec]');
        for (const container of routerContainers) {
            const header = container.querySelector('.router-container-header');
            if (header && header.textContent.trim() === '运营策略') {
                return container;
            }
        }
        
        // 2. 查找所有带有data-v-5d7f7bec属性的.router-container元素
        const dataVContainers = document.querySelectorAll('.router-container[data-v-5d7f7bec]');
        for (const container of dataVContainers) {
            const header = container.querySelector('.router-container-header');
            if (header && header.textContent.includes('运营策略')) {
                return container;
            }
        }
        
        // 3. 常规查找包含"运营策略"的容器（原有逻辑）
        const headers = document.querySelectorAll('.router-container-header');
        for (const header of headers) {
            if (header.textContent.trim() === '运营策略') {
                const parentContainer = header.closest('.router-container');
                if (parentContainer) return parentContainer;
            }
        }
        
        // 4. 不精确匹配，查找包含"运营策略"的标题
        for (const header of headers) {
            if (header.textContent.includes('运营策略')) {
                const parentContainer = header.closest('.router-container');
                if (parentContainer) return parentContainer;
            }
        }
        
        // 5. 查找任何router-container元素
        const elements = document.querySelectorAll('.router-container');
        for (const element of elements) {
            if (element.textContent.includes('运营策略')) {
                return element;
            }
        }

        return null;
    }

    function observeDOMChanges(navigation) {
        // 创建一个MutationObserver实例，用于监听DOM变化
        const observer = new MutationObserver(() => {
            // 检查导航元素是否还存在于文档中
            if (!document.contains(navigation)) {
                // 如果导航元素不再存在于文档中
                const isMobile = navigation.hasAttribute('data-nav') && 
                                 navigation.getAttribute('data-nav') === 'wzsjd-mobile';
                // 重新插入它
                insertNavigation(navigation, isMobile);
            }
        });

        // 配置观察者
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // 初始化导航
    initNavigation();
})();
