import os
import sys
import requests
import json
import uuid
import subprocess
import time
import hashlib
import platform
import winreg
import ctypes
from threading import Thread
import traceback
import tkinter as tk
from tkinter import messagebox, simpledialog, ttk

# 尝试导入encodings模块，但在导入失败时不会崩溃
try:
    import encodings
    # 尝试导入可能需要的编码模块
    encoding_modules = ['utf_8', 'locale', 'aliases', 'ascii', 'idna']
    for module in encoding_modules:
        try:
            __import__(f'encodings.{module}')
        except ImportError:
            pass
except ImportError:
    pass

# 显示错误消息的函数
def show_error(title, message):
    # 使用tkinter显示错误消息，避免使用input()
    try:
        # 创建隐藏的根窗口
        root = tk.Tk()
        root.withdraw()
        # 显示错误消息
        messagebox.showerror(title, message)
        root.destroy()
    except:
        # 如果tkinter失败，尝试使用控制台输出
        print(f"{title}: {message}")
        # 不再使用input，而是简单地等待几秒
        time.sleep(5)

class LicenseVerifier:
    def __init__(self, server_url, software_id, license_file="license.dat", original_exe=None):
        self.server_url = server_url
        self.software_id = software_id
        self.license_file = license_file
        self.original_exe = original_exe
        self.is_authorized = False
        self.verification_thread = None
        self.process = None
        
    def get_hwid(self):
        """获取设备的唯一硬件ID"""
        # 获取CPU信息、主板序列号、BIOS信息等
        system_info = platform.uname()
        if platform.system() == "Windows":
            try:
                # 获取主板序列号
                reg = winreg.ConnectRegistry(None, winreg.HKEY_LOCAL_MACHINE)
                key = winreg.OpenKey(reg, r"HARDWARE\\DESCRIPTION\\System\\BIOS")
                motherboard = winreg.QueryValueEx(key, "BaseBoardProduct")[0]
                bios = winreg.QueryValueEx(key, "BIOSVendor")[0]
                # 获取CPU ID
                key = winreg.OpenKey(reg, r"HARDWARE\\DESCRIPTION\\System\\CentralProcessor\\0")
                processor_id = winreg.QueryValueEx(key, "ProcessorNameString")[0]
            except:
                motherboard = "Unknown"
                bios = "Unknown"
                processor_id = system_info.processor
            
            # 获取磁盘序列号
            try:
                result = subprocess.check_output("wmic diskdrive get serialnumber", shell=True)
                disk_serial = result.decode().strip().split('\n')[1].strip()
            except:
                disk_serial = "Unknown"
        else:
            # 针对Linux和MacOS的硬件识别，这里简化处理
            motherboard = "Unknown"
            bios = system_info.version
            processor_id = system_info.processor
            disk_serial = "Unknown"
            
        # 构建硬件ID
        hwid_str = f"{system_info.node}:{motherboard}:{processor_id}:{disk_serial}:{bios}"
        hwid = hashlib.sha256(hwid_str.encode()).hexdigest()
        return hwid
    
    def read_license(self):
        """从许可证文件读取许可证密钥"""
        if os.path.exists(self.license_file):
            try:
                with open(self.license_file, "r") as f:
                    return f.read().strip()
            except:
                return None
        return None
    
    def save_license(self, license_key):
        """保存许可证密钥到文件"""
        try:
            with open(self.license_file, "w") as f:
                f.write(license_key)
            return True
        except:
            return False
    
    def verify_license(self):
        """验证许可证"""
        license_key = self.read_license()
        if not license_key:
            print("未找到许可证密钥")
            return False, "未找到许可证密钥"
        
        hwid = self.get_hwid()
        print(f"当前硬件ID: {hwid}")
        print(f"验证服务器: {self.server_url}")
        print(f"软件ID: {self.software_id}")
        
        try:
            print("正在发送验证请求...")
            response = requests.post(
                f"{self.server_url}/api/verify",
                json={
                    "license_key": license_key,
                    "software_id": self.software_id,
                    "hwid": hwid
                },
                timeout=10
            )
            
            print(f"服务器响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"验证响应: {data}")
                    if data.get("valid"):
                        print("验证成功")
                        return True, data.get("message", "验证成功")
                    else:
                        print(f"验证失败: {data.get('message', '许可证无效')}")
                        return False, data.get("message", "许可证无效")
                except Exception as e:
                    print(f"解析JSON响应时出错: {str(e)}")
                    return False, f"解析响应时出错: {str(e)}"
            else:
                print(f"服务器错误: {response.status_code}")
                try:
                    print(f"错误响应内容: {response.text}")
                except:
                    pass
                return False, f"服务器错误: {response.status_code}"
        except Exception as e:
            print(f"验证请求出错: {str(e)}")
            # 如果网络连接失败，允许一定的宽限期继续使用
            if os.path.exists("last_verification.json"):
                try:
                    with open("last_verification.json", "r") as f:
                        last_data = json.load(f)
                        # 检查上次验证是否在宽限期内（7天）
                        remaining_time = 7 * 24 * 3600 - (time.time() - last_data.get("timestamp", 0))
                        if remaining_time > 0:
                            days = int(remaining_time / (24 * 3600))
                            hours = int((remaining_time % (24 * 3600)) / 3600)
                            print(f"使用离线缓存验证, 宽限期还剩: {days}天{hours}小时")
                            return True, f"使用离线缓存验证 (剩余{days}天{hours}小时)"
                        else:
                            print("离线宽限期已过")
                except Exception as cache_error:
                    print(f"读取缓存验证时出错: {str(cache_error)}")
            return False, f"验证服务器连接失败: {str(e)}"
    
    def save_verification_result(self, success):
        """保存验证结果到本地缓存"""
        try:
            with open("last_verification.json", "w") as f:
                json.dump({
                    "timestamp": time.time(),
                    "success": success
                }, f)
        except:
            pass
    
    def activate_with_key(self, license_key):
        """使用许可证密钥激活软件"""
        if self.save_license(license_key):
            is_valid, message = self.verify_license()
            if is_valid:
                self.is_authorized = True
                self.save_verification_result(True)
            return is_valid, message
        return False, "保存许可证失败"
    
    def periodic_verification(self, interval=3600):
        """定期验证许可证的后台线程"""
        while True:
            try:
                is_valid, message = self.verify_license()
                self.is_authorized = is_valid
                self.save_verification_result(is_valid)
                
                if not is_valid:
                    # 如果验证失败，终止应用程序
                    if self.process and self.process.poll() is None:
                        self.process.terminate()
                    return
            except Exception as e:
                # 出现异常时记录但不终止应用程序
                traceback.print_exc()
            
            # 间隔一段时间再次验证
            time.sleep(interval)
    
    def start_verification_thread(self, interval=3600):
        """启动定期验证的后台线程"""
        self.verification_thread = Thread(
            target=self.periodic_verification,
            args=(interval,),
            daemon=True
        )
        self.verification_thread.start()
    
    def run_protected_application(self):
        """运行受保护的应用程序"""
        if not self.original_exe or not os.path.exists(self.original_exe):
            return False, "找不到原始应用程序"
        
        is_valid, message = self.verify_license()
        if not is_valid:
            return False, message
        
        # 启动验证线程
        self.start_verification_thread()
        
        # 运行原始应用程序
        try:
            self.process = subprocess.Popen(self.original_exe)
            self.process.wait()  # 等待原始程序结束
            return True, "应用程序已正常退出"
        except Exception as e:
            error_message = f"启动应用程序失败: {str(e)}"
            return False, error_message

def wrap_exe_with_verification(exe_path, server_url, software_id, output_path=None):
    """将验证逻辑打包成一个新的exe，包装原始exe"""
    # 这里使用PyInstaller等工具将验证程序和原始exe打包成一个新的可执行文件
    # 简化起见，这里只是创建一个简单的启动器脚本
    if not output_path:
        output_path = f"{os.path.splitext(exe_path)[0]}_protected.py"
    
    # 获取当前模块的代码，以便集成到生成的启动器中
    with open(__file__, 'r', encoding='utf-8') as f:
        current_module_code = f.read()
    
    # 提取LicenseVerifier类的代码
    class_start = current_module_code.find("class LicenseVerifier:")
    class_end = current_module_code.find("def wrap_exe_with_verification", class_start)
    verifier_class_code = current_module_code[class_start:class_end].strip()
    
    # 提取show_error函数的代码
    show_error_start = current_module_code.find("def show_error")
    show_error_end = current_module_code.find("class LicenseVerifier")
    show_error_code = current_module_code[show_error_start:show_error_end].strip()
    
    # 处理Windows路径中的反斜杠
    safe_exe_path = exe_path.replace('\\', '/')
    
    # 生成启动器代码，确保show_error函数不会使用input()
    launcher_code = '''# -*- coding: utf-8 -*-
import os
import sys
import subprocess
import requests
import json
import time
import hashlib
import platform
import winreg
from threading import Thread
import tkinter as tk
from tkinter import messagebox, simpledialog, ttk
import traceback

# 尝试导入encodings模块，但在导入失败时不会崩溃
try:
    import encodings
    # 尝试导入可能需要的编码模块
    encoding_modules = ['utf_8', 'locale', 'aliases', 'ascii', 'idna']
    for module in encoding_modules:
        try:
            __import__(f'encodings.{module}')
        except ImportError:
            pass
except ImportError:
    pass

# 配置验证器
SERVER_URL = "''' + server_url + '''"
SOFTWARE_ID = "''' + software_id + '''"
ORIGINAL_EXE = "''' + safe_exe_path + '''"

# 从client.py模块集成的代码
''' + show_error_code + '''

''' + verifier_class_code + '''

# 卡密验证界面
class ActivationDialog:
    def __init__(self, verifier):
        self.verifier = verifier
        self.activation_data = None
        
        # 卡密激活窗口
        self.root = tk.Tk()
        self.root.title("软件激活")
        self.root.geometry("400x300")
        self.root.resizable(False, False)
        
        # 窗口居中
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - 400) // 2
        y = (screen_height - 300) // 2
        self.root.geometry(f"400x300+{x}+{y}")
        
        # 设置窗口图标（如果有）
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
        
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20 20 20 20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题标签
        title_label = ttk.Label(main_frame, text="软件激活", font=("微软雅黑", 16, "bold"))
        title_label.pack(pady=10)
        
        # 说明文本
        desc_label = ttk.Label(main_frame, text="请输入您的激活码以继续使用软件", font=("微软雅黑", 10))
        desc_label.pack(pady=10)
        
        # 卡密输入框
        input_frame = ttk.Frame(main_frame)
        input_frame.pack(fill=tk.X, pady=10)
        
        key_label = ttk.Label(input_frame, text="激活码:", font=("微软雅黑", 10))
        key_label.pack(side=tk.LEFT, padx=5)
        
        self.key_entry = ttk.Entry(input_frame, width=30)
        self.key_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=20)
        
        self.activate_button = ttk.Button(button_frame, text="激活", width=10, command=self.activate)
        self.activate_button.pack(side=tk.LEFT, padx=10)
        
        self.exit_button = ttk.Button(button_frame, text="退出", width=10, command=self.exit)
        self.exit_button.pack(side=tk.LEFT, padx=10)
        
        # 状态标签
        self.status_label = ttk.Label(main_frame, text="", font=("微软雅黑", 10))
        self.status_label.pack(pady=10)
        
        # 绑定回车键
        self.root.bind("<Return>", lambda event: self.activate())
        
        # 检查是否已有有效的许可证
        self.check_existing_license()
    
    def check_existing_license(self):
        """检查是否已有有效的许可证"""
        try:
            # 安全地处理返回值，确保message变量定义
            result = self.verifier.verify_license()
            if isinstance(result, tuple) and len(result) >= 2:
                is_valid, verification_message = result
                if is_valid:
                    self.status_label.config(text="已激活: " + str(verification_message), foreground="green")
                    self.root.after(1500, self.success)
                    return True
            else:
                print("验证许可证返回值格式不正确")
        except Exception as e:
            print("验证许可证时出错: " + str(e))
        return False
    
    def activate(self):
        """激活软件"""
        license_key = self.key_entry.get().strip()
        if not license_key:
            self.status_label.config(text="请输入激活码", foreground="red")
            return
        
        # 禁用激活按钮
        self.activate_button.config(state=tk.DISABLED)
        self.status_label.config(text="正在验证激活码...", foreground="blue")
        self.root.update()
        
        try:
            # 调用验证方法
            result = self.verifier.activate_with_key(license_key)
            if isinstance(result, tuple) and len(result) >= 2:
                success, activation_message = result
                
                if success:
                    self.status_label.config(text="激活成功: " + str(activation_message), foreground="green")
                    self.root.after(1500, self.success)
                else:
                    self.status_label.config(text="激活失败: " + str(activation_message), foreground="red")
                    self.activate_button.config(state=tk.NORMAL)
            else:
                self.status_label.config(text="验证返回值格式不正确", foreground="red")
                self.activate_button.config(state=tk.NORMAL)
        except Exception as e:
            self.status_label.config(text="激活出错: " + str(e), foreground="red")
            self.activate_button.config(state=tk.NORMAL)
    
    def success(self):
        """激活成功后关闭窗口"""
        self.activation_data = {"success": True}
        self.root.destroy()
    
    def exit(self):
        """用户退出"""
        self.activation_data = {"success": False, "message": "用户取消激活"}
        self.root.destroy()
    
    def run(self):
        """运行对话框，返回激活结果"""
        self.root.protocol("WM_DELETE_WINDOW", self.exit)  # 处理窗口关闭事件
        self.root.mainloop()
        return self.activation_data


# 主程序
if __name__ == "__main__":
    # 处理未捕获的异常
    try:
        # 添加调试信息
        print("启动验证保护程序...")
        print(f"验证服务器: {SERVER_URL}")
        print(f"软件ID: {SOFTWARE_ID}")
        print(f"原始程序: {ORIGINAL_EXE}")
        
        verifier = LicenseVerifier(SERVER_URL, SOFTWARE_ID, original_exe=ORIGINAL_EXE)
        
        # 首先尝试验证现有许可证
        try:
            print("检查现有许可证...")
            is_valid, message = verifier.verify_license()
            if is_valid:
                print(f"许可证有效: {message}")
                print("正在启动受保护的应用程序...")
                success, app_message = verifier.run_protected_application()
                if success:
                    print("应用程序已正常退出")
                    sys.exit(0)
                else:
                    print(f"启动应用程序失败: {app_message}")
                    show_error("启动失败", app_message)
                    sys.exit(1)
            else:
                print(f"许可证无效或不存在: {message}")
        except Exception as e:
            print(f"验证现有许可证时出错: {str(e)}")
        
        # 许可证无效或不存在，显示激活界面
        print("显示激活界面...")
        activation_dialog = ActivationDialog(verifier)
        result = activation_dialog.run()
        
        if not result or not result.get("success", False):
            error_message = "激活取消"
            if result and "message" in result:
                error_message = result["message"]
            print(f"激活失败: {error_message}")
            show_error("激活失败", error_message)
            sys.exit(1)
        
        # 激活成功，运行受保护的应用
        try:
            print("激活成功，启动应用程序...")
            run_result = verifier.run_protected_application()
            if isinstance(run_result, tuple) and len(run_result) >= 2:
                success, app_message = run_result
                if not success:
                    print(f"启动失败: {app_message}")
                    show_error("验证失败", "错误: " + str(app_message))
                    sys.exit(1)
                else:
                    print("应用程序已正常退出")
            else:
                print("验证过程返回了无效的结果")
                show_error("验证失败", "验证过程返回了无效的结果")
                sys.exit(1)
        except Exception as e:
            error_message = str(e)
            print("错误: " + error_message)
            traceback.print_exc()
            show_error("程序错误", "发生未知错误: " + error_message)
            sys.exit(1)
    except Exception as e:
        error_message = str(e)
        print("错误: " + error_message)
        traceback.print_exc()
        show_error("程序错误", "发生未知错误: " + error_message)
        sys.exit(1)
'''
    
    # 使用UTF-8编码写入文件，确保中文字符正确显示
    with open(output_path, "w", encoding="utf-8") as f:
        f.write(launcher_code)
    
    print(f"已创建启动器脚本: {output_path}")
    print("您可以使用PyInstaller将其打包成exe：")
    print(f"pyinstaller --onefile --noconsole {output_path}")
    
    # 创建一个打包批处理文件
    batch_path = f"{os.path.splitext(output_path)[0]}_打包.bat"
    
    # 处理路径，避免在f-string中使用反斜杠
    dist_path = os.path.join("%cd%", "dist", os.path.basename(os.path.splitext(output_path)[0]) + ".exe")
    dist_path = dist_path.replace("\\", "\\\\")
    
    with open(batch_path, "w", encoding="gb2312") as f:
        f.write(f'''@echo off
echo 正在打包您的程序，请稍等...
echo.

REM 检测Python环境
where python >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo 错误: 未找到Python环境
    echo 请确保已安装Python并添加到PATH环境变量中
    pause
    exit /b 1
)

REM 检测PyInstaller
python -c "import PyInstaller" >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo 正在安装PyInstaller...
    pip install pyinstaller
    if %ERRORLEVEL% NEQ 0 (
        echo 安装PyInstaller失败，请手动安装: pip install pyinstaller
        pause
        exit /b 1
    )
)

REM 准备打包环境
echo 正在准备打包环境...
if not exist build mkdir build
if not exist dist mkdir dist

REM 执行打包命令
echo 正在执行打包...
python -m PyInstaller --onefile --noconsole --clean --exclude-module encodings.locale "{output_path}"

if %ERRORLEVEL% NEQ 0 (
    echo 打包失败，请检查错误信息
) else (
    echo.
    echo 打包完成！
    echo EXE文件位于dist目录中: {dist_path}
)

pause
''')
    print(f"已创建打包批处理文件: {batch_path}")
    
    return output_path

# 示例用法
if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("用法: python client.py [激活模式] 或 python client.py [原始exe路径]")
        print("   激活模式: activate [许可证密钥]")
        print("   包装exe: wrap [原始exe路径] [服务器URL] [软件ID]")
        sys.exit(1)
    
    mode = sys.argv[1]
    
    if mode == "activate":
        if len(sys.argv) < 3:
            print("错误: 缺少许可证密钥")
            sys.exit(1)
        
        license_key = sys.argv[2]
        verifier = LicenseVerifier("http://localhost:5000", "your_software_id")
        success, message = verifier.activate_with_key(license_key)
        
        print(f"激活结果: {message}")
        sys.exit(0 if success else 1)
    
    elif mode == "wrap":
        if len(sys.argv) < 5:
            print("错误: 缺少参数")
            print("用法: python client.py wrap [原始exe路径] [服务器URL] [软件ID]")
            sys.exit(1)
        
        exe_path = sys.argv[2]
        server_url = sys.argv[3]
        software_id = sys.argv[4]
        
        output_path = wrap_exe_with_verification(exe_path, server_url, software_id)
        print(f"已创建保护启动器: {output_path}")
    
    else:
        # 假设传入的是要保护的原始exe路径
        exe_path = sys.argv[1]
        server_url = "http://localhost:5000"  # 默认值
        software_id = "default_software"  # 默认值
        
        if len(sys.argv) > 2:
            server_url = sys.argv[2]
        if len(sys.argv) > 3:
            software_id = sys.argv[3]
        
        output_path = wrap_exe_with_verification(exe_path, server_url, software_id)
        print(f"已创建保护启动器: {output_path}") 