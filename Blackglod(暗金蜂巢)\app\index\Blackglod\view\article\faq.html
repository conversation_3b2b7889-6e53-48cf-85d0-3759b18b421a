<!doctype html>
<html lang="zh">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>{$title}</title>
        
        <link rel="shortcut icon" href="{$favicon}" type="image/x-icon">
        <link rel="stylesheet" href="/assets/plugin/Blackglod/plugin/Blackglod/css/all.min.css">
        <link rel="stylesheet" href="/assets/plugin/Blackglod/plugin/Blackglod/css/all.min1.css">
        
<style>
            /* 全局样式 */
            :root {
                --primary-gold: #B39B77;     /* 更深沉的金色 */
                --dark-gold: #8B7355;        /* 暗金色 */
                --light-gold: #D4B78F;       /* 浅金色 */
                --bg-dark: #151515;          /* 更深的背景色 */
                --bg-darker: #0A0A0A;        /* 最深的背景色 */
                --text-gold: #B39B77;        /* 文字金色 */
                --text-light: #E5E5E5;       /* 浅色文字 */
            }

            /* 背景样式优化 */
            body {
                background: linear-gradient(135deg, 
                    var(--bg-darker) 0%, 
                    var(--bg-dark) 50%,
                    var(--bg-darker) 100%
                );
                color: var(--text-light);
                margin: 0;
                font-family: 'Arial', sans-serif;
                min-height: 100vh;
                position: relative;
                overflow-x: hidden;
            }

            /* 背景装饰效果 */
            body::before {
                content: '';
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: 
                    radial-gradient(circle at 20% 30%, 
                        rgba(179, 155, 119, 0.05) 0%, 
                        transparent 50%),
                    radial-gradient(circle at 80% 70%, 
                        rgba(179, 155, 119, 0.05) 0%, 
                        transparent 50%);
                pointer-events: none;
                z-index: 0;
            }

            /* 动态光效 */
            .light-effect {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: 
                    linear-gradient(45deg, 
                        transparent 0%, 
                        rgba(179, 155, 119, 0.02) 50%, 
                        transparent 100%);
                animation: lightMove 8s ease-in-out infinite;
                pointer-events: none;
                z-index: 1;
            }

            /* 网格背景 */
            .grid-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-image: 
                    linear-gradient(rgba(179, 155, 119, 0.05) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(179, 155, 119, 0.05) 1px, transparent 1px);
                background-size: 50px 50px;
                pointer-events: none;
                z-index: 1;
                opacity: 0.3;
            }

            /* 装饰线条 */
            .decorative-lines {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: 
                    linear-gradient(45deg, transparent 48%, 
                        rgba(179, 155, 119, 0.1) 49%, 
                        rgba(179, 155, 119, 0.1) 51%, 
                        transparent 52%);
                background-size: 100px 100px;
                pointer-events: none;
                z-index: 1;
                opacity: 0.2;
            }

            @keyframes lightMove {
                0% {
                    transform: translateX(-100%) rotate(-45deg);
                }
                50% {
                    transform: translateX(100%) rotate(-45deg);
                }
                100% {
                    transform: translateX(-100%) rotate(-45deg);
                }
            }

            /* 确保内容在装饰层之上 */
            .header,
            .article-section,
            .hex-decoration {
                position: relative;
                z-index: 2;
            }

            /* 调整文章容器背景 */
            .article-container {
                background: rgba(0, 0, 0, 0.4);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(179, 155, 119, 0.1);
                box-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
            }

            .notice-item {
                background: rgba(255, 255, 255, 0.03);
                backdrop-filter: blur(5px);
                border: 1px solid rgba(179, 155, 119, 0.1);
                transition: all 0.3s ease;
            }

            .notice-item:hover {
                transform: translateY(-2px);
                box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
                border-color: rgba(179, 155, 119, 0.2);
            }

            /* 导航栏样式优化 */
            .header {
                background: rgba(10, 10, 10, 0.95);
                border-bottom: 1px solid rgba(179, 155, 119, 0.1);
                backdrop-filter: blur(10px);
                padding: 0;
                position: fixed;
                width: 100%;
                top: 0;
                z-index: 1000;
                height: 70px;
            }

            .nav-container {
                display: flex;
                justify-content: space-between;
                align-items: center;
                max-width: 1400px;
                margin: 0 auto;
                padding: 0 2rem;
                height: 100%;
            }

            .logo {
                display: flex;
                align-items: center;
                gap: 1rem;
                text-decoration: none;
            }

            .logo-img {
                height: 35px;
                transition: transform 0.3s;
            }

            .nav-links {
                display: flex;
                align-items: center;
                gap: 2.5rem;
                margin: 0;
                padding: 0;
                list-style: none;
            }

            .nav-item {
                position: relative;
            }

            .nav-link {
                color: #999;
                text-decoration: none;
                font-size: 0.95rem;
                padding: 0.5rem 0;
                transition: all 0.3s ease;
                position: relative;
                display: flex;
                align-items: center;
            }
            
            .nav-link .dropdown-icon {
                margin-left: 5px;
                font-size: 0.7rem;
                transition: transform 0.3s ease;
            }
            
            .nav-item:hover .dropdown-icon {
                transform: rotate(180deg);
            }

            .nav-link::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 50%;
                width: 0;
                height: 2px;
                background: linear-gradient(90deg, var(--primary-gold), var(--light-gold));
                transition: all 0.3s ease;
                transform: translateX(-50%);
            }

            .nav-link:hover {
                color: var(--primary-gold);
            }

            .nav-link:hover::after {
                width: 100%;
            }

            /* 子菜单样式 */
            .submenu {
                position: absolute;
                top: 100%;
                left: 50%;
                transform: translateX(-50%) translateY(10px);
                min-width: 180px;
                background: rgba(15, 15, 15, 0.95);
                border: 1px solid rgba(179, 155, 119, 0.15);
                border-radius: 4px;
                padding: 0.5rem 0;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
                z-index: 1001;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
                backdrop-filter: blur(10px);
            }
            
            .nav-item:hover .submenu {
                opacity: 1;
                visibility: visible;
                transform: translateX(-50%) translateY(0);
            }
            
            .submenu::before {
                content: '';
                position: absolute;
                top: -6px;
                left: 50%;
                transform: translateX(-50%);
                width: 12px;
                height: 12px;
                background: rgba(15, 15, 15, 0.95);
                border-left: 1px solid rgba(179, 155, 119, 0.15);
                border-top: 1px solid rgba(179, 155, 119, 0.15);
                transform: translateX(-50%) rotate(45deg);
            }
            
            .submenu-item {
                display: block;
                padding: 0.6rem 1.2rem;
                color: #999;
                text-decoration: none;
                font-size: 0.9rem;
                transition: all 0.2s ease;
                position: relative;
                white-space: nowrap;
            }
            
            .submenu-item:hover {
                color: var(--primary-gold);
                background: rgba(179, 155, 119, 0.05);
                padding-left: 1.5rem;
            }
            
            .submenu-item::before {
                content: '';
                position: absolute;
                left: 0.8rem;
                top: 50%;
                width: 0;
                height: 1px;
                background: var(--primary-gold);
                transition: all 0.2s ease;
                transform: translateY(-50%);
            }
            
            .submenu-item:hover::before {
                width: 0.4rem;
            }

            /* 按钮样式优化 */
            .auth-buttons {
                display: flex;
                gap: 1rem;
            }

            .btn-login, .btn-register {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                font-size: 0.9rem;
                transition: all 0.3s;
                text-decoration: none;
            }

            .btn-login {
                background: transparent;
                border: 1px solid var(--primary-gold);
                color: var(--primary-gold);
                padding: 0.6rem 1.5rem;
                border-radius: 4px;
            }

            .btn-register {
                background: linear-gradient(135deg, var(--primary-gold), var(--dark-gold));
                border: none;
                color: #000;
                padding: 0.6rem 1.5rem;
                border-radius: 4px;
                font-weight: 500;
            }

            .btn-login:hover, .btn-register:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(179, 155, 119, 0.2);
            }

            /* 响应式设计 */
            @media (max-width: 768px) {
                .nav-container {
                    padding: 0 1rem;
                }

                .nav-links {
                    gap: 1rem;
                }

                .auth-buttons {
                    gap: 0.5rem;
                }

                .btn-login, .btn-register {
                    padding: 0.4rem 0.8rem;
                    font-size: 0.85rem;
                }
            }

            /* 文章列表样式优化 */
            .article-section {
                padding-top: 100px;
                position: relative;
                z-index: 2;
            }

            .article-container {
                max-width: 1200px;
                margin: 0 auto;
                padding: 2rem;
                background: rgba(0, 0, 0, 0.3);
                border-radius: 20px;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(179, 155, 119, 0.1);
            }

            .notice-item {
                background: rgba(255, 255, 255, 0.03);
                border: 1px solid rgba(200, 166, 117, 0.1);
                border-radius: 15px;
                padding: 2rem;
                margin-bottom: 1.5rem;
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
            }

            .notice-item:hover {
                transform: translateY(-3px);
                border-color: rgba(200, 166, 117, 0.3);
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            }

            .notice-item::before {
                content: '';
                position: absolute;
                top: -50%;
                left: -50%;
                width: 200%;
                height: 200%;
                background: linear-gradient(
                    45deg,
                    transparent,
                    rgba(200,166,117,0.1),
                    transparent
                );
                transform: rotate(45deg);
                animation: shimmer 3s infinite linear;
                pointer-events: none;
            }

            .notice-title {
                font-size: 1.5rem;
                color: var(--primary-gold);
                margin-bottom: 1rem;
                position: relative;
                display: inline-block;
            }

            .notice-content {
                color: var(--text-light);
                line-height: 1.8;
                margin-bottom: 1.5rem;
            }

            .notice-meta {
                display: flex;
                align-items: center;
                gap: 20px;
                margin-top: 15px;
                font-size: 14px;
                color: #666;
            }

            .notice-meta span {
                display: flex;
                align-items: center;
                gap: 5px;
            }

            .notice-meta i {
                font-size: 16px;
                color: #888;
            }

            /* 浏览次数图标特殊样式 */
            .view-count {
                opacity: 0.7;
            }

            .view-count i {
                color: #999;
            }

            /* 动画效果 */
            @keyframes shimmer {
                0% {
                    transform: translateX(-100%) rotate(45deg);
                }
                100% {
                    transform: translateX(100%) rotate(45deg);
                }
            }

            /* 响应式设计 */
            @media (max-width: 768px) {
                .container {
                    padding: 0 1rem;
                }

                .article-container {
                    padding: 1rem;
                    margin: 0 1rem;
                }

                .notice-item {
                    padding: 1.5rem;
                }

                .notice-title {
                    font-size: 1.3rem;
                }
            }

            /* 页脚样式 */
            .footer {
                background: rgba(10, 10, 10, 0.95);
                border-top: 1px solid rgba(179, 155, 119, 0.1);
                padding: 4rem 0 2rem;
                margin-top: 4rem;
            }

            .footer-container {
                max-width: 1200px;
                margin: 0 auto;
                padding: 0 2rem;
                display: grid;
                grid-template-columns: repeat(4, 1fr);
                gap: 3rem;
            }

            .footer-section h3 {
                color: var(--primary-gold);
                font-size: 1.2rem;
                margin-bottom: 1.5rem;
            }

            .footer-section ul {
                list-style: none;
                padding: 0;
                margin: 0;
            }

            .footer-section li {
                margin-bottom: 0.8rem;
            }

            .footer-section a {
                color: var(--text-light);
                text-decoration: none;
                transition: color 0.3s;
            }

            .footer-section a:hover {
                color: var(--primary-gold);
            }

            .footer-bottom {
                margin-top: 3rem;
                padding-top: 2rem;
                border-top: 1px solid rgba(179, 155, 119, 0.1);
                text-align: center;
                color: #666;
            }

            .footer-bottom a {
                color: #888;
                text-decoration: none;
            }

            .footer-bottom a:hover {
                color: var(--primary-gold);
            }

            /* 3D 蜂巢装饰样式 */
            .hex-decoration {
                position: fixed;
                top: 50%;
                transform: translateY(-50%);
                width: 300px;
                height: 600px;
                pointer-events: none;
                z-index: 0;
            }

            .hex-left {
                left: 0;
            }

            .hex-right {
                right: 0;
            }

            .hex-container {
                position: relative;
                width: 100%;
                height: 100%;
                display: grid;
                grid-template-columns: repeat(4, 1fr);
                gap: 10px;
                padding: 20px;
                transform-style: preserve-3d;
                transform: perspective(1000px) rotateX(15deg) rotateY(5deg);
                animation: containerFloat 15s infinite ease-in-out;
            }
            
            .hex-right .hex-container {
                transform: perspective(1000px) rotateX(15deg) rotateY(-5deg);
                animation: containerFloatReverse 15s infinite ease-in-out;
            }
            
            @keyframes containerFloat {
                0%, 100% { transform: perspective(1000px) rotateX(15deg) rotateY(5deg); }
                50% { transform: perspective(1000px) rotateX(12deg) rotateY(8deg); }
            }
            
            @keyframes containerFloatReverse {
                0%, 100% { transform: perspective(1000px) rotateX(15deg) rotateY(-5deg); }
                50% { transform: perspective(1000px) rotateX(12deg) rotateY(-8deg); }
            }

            .hex {
                width: 60px;
                height: 60px;
                background: rgba(179, 155, 119, 0.05);
                clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
                position: relative;
                transition: all 0.3s ease;
                transform-style: preserve-3d;
                transform: translateZ(0);
                box-shadow: 0 5px 15px rgba(0,0,0,0.3);
                border: 1px solid rgba(179, 155, 119, 0.2);
                overflow: hidden;
                backdrop-filter: blur(5px);
            }

            .hex::before {
                content: '';
                position: absolute;
                width: 100%;
                height: 100%;
                background: rgba(179, 155, 119, 0.03);
                clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
                transform: translateZ(-5px);
                border: 1px solid rgba(179, 155, 119, 0.1);
            }

            .hex::after {
                content: '';
                position: absolute;
                width: 100%;
                height: 100%;
                background: linear-gradient(135deg, rgba(179, 155, 119, 0.2), transparent);
                transform: translateZ(5px);
                clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
                opacity: 0.3;
                filter: blur(1px);
            }

            .hex:nth-child(even) {
                transform: translateZ(10px);
            }
            
            .hex:nth-child(3n) {
                transform: translateZ(20px);
            }
            
            .hex:nth-child(5n) {
                transform: translateZ(-10px);
            }

            /* 蜂巢内部光效 */
            .hex-light {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: radial-gradient(circle at center, rgba(255, 215, 128, 0.2) 0%, transparent 70%);
                opacity: 0;
                transition: opacity 0.5s ease;
                pointer-events: none;
                clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
                z-index: 2;
                animation: pulseLight var(--pulse-duration, 4s) infinite ease-in-out;
                animation-delay: calc(var(--delay) * 0.5s);
            }
            
            @keyframes pulseLight {
                0%, 100% { opacity: 0; }
                50% { opacity: 0.6; }
            }

            /* 动画效果 */
            @keyframes hexFloat {
                0%, 100% {
                    transform: translateY(0) scale(1);
                }
                50% {
                    transform: translateY(-10px) scale(1.05);
                }
            }

            .hex {
                animation: hexFloat 4s ease-in-out infinite;
                animation-delay: calc(var(--delay) * 0.3s);
                --pulse-duration: calc(3s + (var(--delay) * 0.5s));
            }
            
            /* 额外的蜂巢点缀 */
            .hex-small-container {
                position: fixed;
                width: 100%;
                height: 100%;
                top: 0;
                left: 0;
                pointer-events: none;
                z-index: 0;
                opacity: 0.5;
            }
            
            .hex-small {
                position: absolute;
                width: 20px;
                height: 20px;
                background: rgba(179, 155, 119, 0.1);
                clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
                border: 1px solid rgba(179, 155, 119, 0.2);
                animation: smallHexFloat 10s linear infinite;
                opacity: 0.3;
            }
            
            @keyframes smallHexFloat {
                0% {
                    transform: translateY(100vh) rotate(0deg);
                    opacity: 0;
                }
                10% {
                    opacity: 0.3;
                }
                90% {
                    opacity: 0.3;
                }
                100% {
                    transform: translateY(-100px) rotate(360deg);
                    opacity: 0;
                }
            }

            /* 响应式调整 */
            @media (max-width: 1600px) {
                .hex-decoration {
                    width: 200px;
                }
                .hex {
                    width: 40px;
                    height: 40px;
                }
            }

            @media (max-width: 1200px) {
                .hex-decoration {
                    width: 150px;
                }
                .hex {
                    width: 30px;
                    height: 30px;
                }
            }

            @media (max-width: 768px) {
                .hex-decoration {
                    display: none;
                }
                
                .hex-small-container {
                    opacity: 0.2;
                }
            }

            /* 移动设备菜单按钮 */
            .mobile-menu-toggle {
                display: none;
                cursor: pointer;
                background: none;
                border: none;
                font-size: 1.5rem;
                color: var(--primary-gold);
                padding: 0;
            }
            
            @media (max-width: 576px) {
                .mobile-menu-toggle {
                    display: block;
                    margin-right: 10px;
                }
                
                .auth-buttons {
                    justify-content: flex-end;
                    flex: 1;
                }
                
                .btn-login {
                    display: none;
                }
                
                .btn-register {
                    padding: 0.4rem 0.8rem;
                    font-size: 0.85rem;
                }
                
                .nav-links {
                    position: fixed;
                    top: 70px;
                    left: 0;
                    width: 100%;
                    flex-direction: column;
                    background: rgba(10, 10, 10, 0.98);
                    padding: 1rem 0;
                    transform: translateY(-150%);
                    opacity: 0;
                    visibility: hidden;
                    transition: all 0.3s ease;
                    z-index: 1000;
                    border-bottom: 1px solid rgba(179, 155, 119, 0.1);
                    backdrop-filter: blur(10px);
                    align-items: flex-start;
                    gap: 0;
                    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
                }
                
                .nav-links.active {
                    transform: translateY(0);
                    opacity: 1;
                    visibility: visible;
                }
                
                .nav-item {
                    width: 100%;
                    border-bottom: 1px solid rgba(179, 155, 119, 0.05);
                }
                
                .nav-item:last-child {
                    border-bottom: none;
                }
                
                .nav-link {
                    padding: 0.8rem 1.5rem;
                    width: 100%;
                    justify-content: space-between;
                }
                
                .nav-link::after {
                    display: none;
                }
                
                .submenu {
                    position: static;
                    background: rgba(20, 20, 20, 0.95);
                    transform: none;
                    box-shadow: none;
                    border-radius: 0;
                    border: none;
                    border-left: 2px solid var(--primary-gold);
                    max-height: 0;
                    overflow: hidden;
                    opacity: 1;
                    visibility: visible;
                    margin-left: 1.5rem;
                    width: calc(100% - 3rem);
                    transition: max-height 0.3s ease, padding 0.3s ease;
                }
                
                .nav-item.open .submenu {
                    max-height: 500px;
                    padding: 0.5rem 0;
                }
                
                .submenu::before {
                    display: none;
                }
                
                .submenu-item {
                    padding: 0.6rem 1rem;
                    border-bottom: 1px solid rgba(179, 155, 119, 0.05);
                }
                
                .submenu-item:last-child {
                    border-bottom: none;
                }
                
                /* 移动端登录/注册按钮 */
                .mobile-auth {
                    display: block;
                    width: 100%;
                    padding: 1rem 1.5rem;
                    background: rgba(179, 155, 119, 0.05);
                    margin-top: 0.5rem;
                }
                
                .mobile-auth .btn-login {
                    display: inline-block;
                    margin-right: 1rem;
                }
            }

            /* FAQ项样式优化 - 改为手风琴样式 */
            .faq-item {
                background: rgba(255, 255, 255, 0.03);
                backdrop-filter: blur(5px);
                border: 1px solid rgba(179, 155, 119, 0.1);
                border-radius: 12px;
                margin-bottom: 1.2rem;
                overflow: hidden;
                transition: all 0.3s ease, transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
                position: relative;
            }

            .faq-item:hover {
                border-color: rgba(179, 155, 119, 0.3);
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            }

            .faq-header {
                padding: 1.5rem 2rem;
                cursor: pointer;
                display: flex;
                justify-content: space-between;
                align-items: center;
                position: relative;
                z-index: 2;
                transition: all 0.3s ease;
            }

            .faq-header:hover {
                background: rgba(179, 155, 119, 0.05);
            }

            .faq-title {
                color: var(--primary-gold);
                margin: 0;
                font-size: 1.3rem;
                font-weight: 500;
                transition: all 0.3s ease;
                flex: 1;
            }

            .faq-icon {
                color: var(--primary-gold);
                font-size: 1.2rem;
                transition: all 0.3s ease;
                margin-left: 1rem;
                width: 24px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                background: rgba(179, 155, 119, 0.1);
            }

            .faq-content {
                max-height: 0;
                overflow: hidden;
                transition: max-height 0.5s cubic-bezier(0, 1, 0, 1);
                background: rgba(0, 0, 0, 0.2);
                border-top: 1px solid rgba(179, 155, 119, 0.05);
            }

            .faq-content-inner {
                padding: 0 2rem 2rem;
                color: var(--text-light);
                line-height: 1.8;
            }

            .faq-item.active .faq-content {
                max-height: 2000px;
                transition: max-height 1s ease-in-out;
            }

            .faq-item.active .faq-icon {
                transform: rotate(180deg);
                background: var(--primary-gold);
                color: #000;
            }

            .faq-meta {
                display: flex;
                align-items: center;
                gap: 20px;
                margin-top: 15px;
                padding-top: 15px;
                border-top: 1px solid rgba(179, 155, 119, 0.1);
                font-size: 14px;
                color: #666;
            }

            .faq-meta span {
                display: flex;
                align-items: center;
                gap: 5px;
            }

            .faq-meta i {
                font-size: 16px;
                color: #888;
            }

            /* 添加FAQ项的微妙动画效果 */
            .faq-item {
                transform: translateY(20px);
                opacity: 0;
                animation: fadeInUp 0.6s forwards;
            }

            @keyframes fadeInUp {
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .faq-item:nth-child(1) { animation-delay: 0.1s; }
            .faq-item:nth-child(2) { animation-delay: 0.2s; }
            .faq-item:nth-child(3) { animation-delay: 0.3s; }
            .faq-item:nth-child(4) { animation-delay: 0.4s; }
            .faq-item:nth-child(5) { animation-delay: 0.5s; }
            .faq-item:nth-child(6) { animation-delay: 0.6s; }
            .faq-item:nth-child(7) { animation-delay: 0.7s; }
            .faq-item:nth-child(8) { animation-delay: 0.8s; }
            .faq-item:nth-child(9) { animation-delay: 0.9s; }
            .faq-item:nth-child(10) { animation-delay: 1.0s; }

            /* 添加闪光效果 */
            .faq-item::before {
                content: '';
                position: absolute;
                top: -50%;
                left: -50%;
                width: 200%;
                height: 200%;
                background: linear-gradient(
                    45deg,
                    transparent,
                    rgba(200,166,117,0.08),
                    transparent
                );
                transform: rotate(45deg);
                animation: shimmer 3s infinite linear;
                pointer-events: none;
                z-index: 1;
            }

            .faq-shine {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                overflow: hidden;
                pointer-events: none;
                z-index: 1;
            }

            .faq-shine::after {
                content: '';
                position: absolute;
                top: -100%;
                left: -100%;
                width: 50px;
                height: 300%;
                background: linear-gradient(
                    90deg,
                    transparent,
                    rgba(255, 255, 255, 0.1),
                    transparent
                );
                transform: rotate(15deg);
                animation: shineAnimation 6s infinite linear;
                pointer-events: none;
            }

            @keyframes shineAnimation {
                0% {
                    left: -100%;
                }
                20%, 100% {
                    left: 150%;
                }
            }

            /* FAQ页面标题 */
            .page-title-container {
                text-align: center;
                margin-bottom: 3rem;
                position: relative;
            }

            .page-title {
                color: var(--primary-gold);
                font-size: 2.5rem;
                font-weight: 600;
                margin-bottom: 1rem;
                position: relative;
                display: inline-block;
            }

            .page-title::after {
                content: '';
                position: absolute;
                bottom: -10px;
                left: 50%;
                width: 80px;
                height: 3px;
                background: linear-gradient(90deg, transparent, var(--primary-gold), transparent);
                transform: translateX(-50%);
            }

            .page-subtitle {
                color: var(--text-light);
                font-size: 1.1rem;
                opacity: 0.7;
                max-width: 700px;
                margin: 0 auto;
                line-height: 1.6;
            }

            /* 搜索框样式 */
            .faq-search-container {
                margin-bottom: 2.5rem;
                position: relative;
                max-width: 600px;
                margin-left: auto;
                margin-right: auto;
            }

            .faq-search {
                width: 100%;
                padding: 1rem 1.5rem;
                background: rgba(0, 0, 0, 0.3);
                border: 1px solid rgba(179, 155, 119, 0.2);
                border-radius: 50px;
                color: var(--text-light);
                font-size: 1rem;
                outline: none;
                transition: all 0.3s ease;
                padding-left: 3rem;
            }

            .faq-search:focus {
                border-color: var(--primary-gold);
                box-shadow: 0 0 15px rgba(179, 155, 119, 0.2);
            }

            .search-icon {
                position: absolute;
                left: 1.2rem;
                top: 50%;
                transform: translateY(-50%);
                color: var(--primary-gold);
                font-size: 1rem;
                pointer-events: none;
            }

            /* 响应式设计优化 */
            @media (max-width: 768px) {
                .page-title {
                    font-size: 2rem;
                }

                .faq-header {
                    padding: 1.2rem 1.5rem;
                }

                .faq-title {
                    font-size: 1.1rem;
                }

                .faq-content-inner {
                    padding: 0 1.5rem 1.5rem;
                }
            }

            /* 滚动条样式 */
            ::-webkit-scrollbar {
                width: 10px;
                background: var(--bg-darker);
            }

            ::-webkit-scrollbar-thumb {
                background: var(--dark-gold);
                border-radius: 5px;
            }

            ::-webkit-scrollbar-thumb:hover {
                background: var(--primary-gold);
            }
        </style>
    </head>
    <body>
        <!-- 背景效果层 -->
        <div class="light-effect"></div>
        <div class="grid-overlay"></div>
        <div class="decorative-lines"></div>

        <!-- 左侧蜂巢装饰 -->
        <div class="hex-decoration hex-left">
            <div class="hex-container">
                {for start="1" end="21"}
                <div class="hex" style="--delay: {$i}">
                    <div class="hex-light"></div>
                </div>
                {/for}
            </div>
        </div>

        <!-- 右侧蜂巢装饰 -->
        <div class="hex-decoration hex-right">
            <div class="hex-container">
                {for start="1" end="21"}
                <div class="hex" style="--delay: {$i}">
                    <div class="hex-light"></div>
                </div>
                {/for}
            </div>
        </div>
        
        <!-- 背景浮动蜂巢 -->
        <div class="hex-small-container">
            <div class="hex-small" style="left: 5%; top: 20%; animation-duration: 15s; animation-delay: 0s;"></div>
            <div class="hex-small" style="left: 15%; top: 50%; animation-duration: 18s; animation-delay: 2s;"></div>
            <div class="hex-small" style="left: 25%; top: 80%; animation-duration: 20s; animation-delay: 5s;"></div>
            <div class="hex-small" style="left: 35%; top: 10%; animation-duration: 22s; animation-delay: 3s;"></div>
            <div class="hex-small" style="left: 45%; top: 40%; animation-duration: 25s; animation-delay: 7s;"></div>
            <div class="hex-small" style="left: 55%; top: 70%; animation-duration: 19s; animation-delay: 4s;"></div>
            <div class="hex-small" style="left: 65%; top: 30%; animation-duration: 17s; animation-delay: 6s;"></div>
            <div class="hex-small" style="left: 75%; top: 60%; animation-duration: 23s; animation-delay: 1s;"></div>
            <div class="hex-small" style="left: 85%; top: 90%; animation-duration: 21s; animation-delay: 8s;"></div>
            <div class="hex-small" style="left: 95%; top: 15%; animation-duration: 24s; animation-delay: 9s;"></div>
            <div class="hex-small" style="left: 10%; top: 45%; animation-duration: 26s; animation-delay: 10s;"></div>
            <div class="hex-small" style="left: 30%; top: 75%; animation-duration: 16s; animation-delay: 0s;"></div>
            <div class="hex-small" style="left: 50%; top: 25%; animation-duration: 27s; animation-delay: 3s;"></div>
            <div class="hex-small" style="left: 70%; top: 55%; animation-duration: 28s; animation-delay: 7s;"></div>
            <div class="hex-small" style="left: 90%; top: 85%; animation-duration: 30s; animation-delay: 5s;"></div>
        </div>

        <!-- 页面内容 -->
        <header class="header">
            <div class="nav-container">
                <a href="/" class="logo">
                    {if !empty($logo)}
                    <img src="{$logo}" alt="{$siteName}" class="logo-img">
                    {else}
                    <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                        <path d="M16 4L28 12V24L16 32L4 24V12L16 4Z" fill="#D4B78F"/>
                    </svg>
                    {/if}
                </a>
                
                <nav class="nav-links">
                    {foreach $navItems as $nav}
                    <div class="nav-item">
                        <a href="{$nav.href}" class="nav-link" {if $nav.target eq '_blank'}target="_blank"{/if}>
                            {$nav.name}
                        </a>
                        {if !empty($nav.children)}
                        <div class="submenu">
                            {foreach $nav.children as $child}
                            <a href="{$child.href}" class="submenu-item" {if $child.target eq '_blank'}target="_blank"{/if}>
                                {$child.name}
                            </a>
                            {/foreach}
                        </div>
                        {/if}
                    </div>
                    {/foreach}
                </nav>
                
                <div class="auth-buttons">
                    <a href="/merchant/login" class="btn-login">
                        <i class="fas fa-sign-in-alt"></i>
                        商户登录
                    </a>
                    <a href="/merchant/register" class="btn-register">
                        <i class="fas fa-user-plus"></i>
                        商户注册
                    </a>
                </div>
            </div>
        </header>

        <main class="article-section">
            <div class="article-container">
                <div class="page-title-container">
                    <h1 class="page-title">常见问题解答</h1>
                    <p class="page-subtitle">我们整理了用户最常提问的问题，希望能帮助您快速找到所需的信息</p>
                </div>

                <div class="faq-search-container">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="faq-search" placeholder="搜索您的问题..." id="faqSearch">
                </div>

                <div class="faq-list">
                    {foreach $faqList as $faq}
                    <div class="faq-item" data-article-id="{$faq.id}">
                        <div class="faq-shine"></div>
                        <div class="faq-header">
                            <h2 class="faq-title">{$faq.title}</h2>
                            <div class="faq-icon">
                                <i class="fas fa-chevron-down"></i>
                            </div>
                        </div>
                        <div class="faq-content">
                            <div class="faq-content-inner">
                                {$faq.content|raw}
                                <div class="faq-meta">
                                    <span><i class="far fa-clock"></i> {$faq.create_at}</span>
                                    <span class="view-count">
                                        <i class="far fa-eye"></i> 
                                        {$faq.views ?? '0'} 次浏览
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    {/foreach}
                </div>
            </div>
        </main>

        <footer class="footer">
            <div class="footer-container">
                {if $footer_service_show}
                <div class="footer-section">
                    <h3>服务中心</h3>
                    <ul>
                        <li><a href="{$footer_service_1_link}">{$footer_service_1}</a></li>
                        <li><a href="{$footer_service_2_link}">{$footer_service_2}</a></li>
                        <li><a href="{$footer_service_3_link}">{$footer_service_3}</a></li>
                        <li><a href="{$footer_service_4_link}">{$footer_service_4}</a></li>
                    </ul>
                </div>
                {/if}

                {if $footer_help_show}
                <div class="footer-section">
                    <h3>帮助中心</h3>
                    <ul>
                        <li><a href="{$footer_help_1_link}">{$footer_help_1}</a></li>
                        <li><a href="{$footer_help_2_link}">{$footer_help_2}</a></li>
                        <li><a href="{$footer_help_3_link}">{$footer_help_3}</a></li>
                        <li><a href="{$footer_help_4_link}">{$footer_help_4}</a></li>
                    </ul>
                </div>
                {/if}

                {if $footer_legal_show}
                <div class="footer-section">
                    <h3>法律责任</h3>
                    <ul>
                        <li><a href="{$footer_legal_1_link}">{$footer_legal_1}</a></li>
                        <li><a href="{$footer_legal_2_link}">{$footer_legal_2}</a></li>
                        <li><a href="{$footer_legal_3_link}">{$footer_legal_3}</a></li>
                        <li><a href="{$footer_legal_4_link}">{$footer_legal_4}</a></li>
                    </ul>
                </div>
                {/if}

                {if $footer_links_show}
                <div class="footer-section">
                    <h3>友情链接</h3>
                    <ul>
                        <li><a href="{$footer_links_1_link}">{$footer_links_1}</a></li>
                        <li><a href="{$footer_links_2_link}">{$footer_links_2}</a></li>
                        <li><a href="{$footer_links_3_link}">{$footer_links_3}</a></li>
                        <li><a href="{$footer_links_4_link}">{$footer_links_4}</a></li>
                    </ul>
                </div>
                {/if}
            </div>

            <div class="footer-bottom">
                {if !empty($icpNumber)}
                <a href="https://beian.miit.gov.cn/" target="_blank">{$icpNumber}</a>
                {/if}
                {if !empty($gaNumber)}
                <a href="{$icpCert}" target="_blank">{$gaNumber}</a>
                {/if}
            </div>
        </footer>

        <button class="mobile-menu-toggle">
            <i class="fas fa-bars"></i>
        </button>

        <script>
            // 添加3D效果和交互
            document.querySelectorAll('.hex').forEach((hex, index) => {
                hex.style.setProperty('--delay', index);
                
                // 随机化蜂巢的位置和大小，增加层次感
                const randomZ = Math.floor(Math.random() * 30) - 15;
                const randomScale = 0.8 + Math.random() * 0.4;
                hex.style.transform = `translateZ(${randomZ}px) scale(${randomScale})`;
                
                // 随机化动画延迟
                const randomDelay = Math.random() * 5;
                hex.style.animationDelay = `${randomDelay}s`;
                
                // 随机化脉冲光效时长
                const pulseDuration = 3 + Math.random() * 4;
                hex.style.setProperty('--pulse-duration', `${pulseDuration}s`);
                
                hex.addEventListener('mouseover', () => {
                    hex.style.transform = `translateZ(40px) scale(1.1)`;
                    const hexLight = hex.querySelector('.hex-light');
                    if (hexLight) {
                        hexLight.style.opacity = '0.8';
                    }
                });
                
                hex.addEventListener('mouseout', () => {
                    hex.style.transform = `translateZ(${randomZ}px) scale(${randomScale})`;
                    const hexLight = hex.querySelector('.hex-light');
                    if (hexLight) {
                        hexLight.style.opacity = '';
                    }
                });
            });

            document.addEventListener('DOMContentLoaded', function() {
                // FAQ 手风琴效果
                const faqItems = document.querySelectorAll('.faq-item');
                
                faqItems.forEach(item => {
                    const header = item.querySelector('.faq-header');
                    
                    header.addEventListener('click', () => {
                        // 关闭其他打开的 FAQ 项
                        faqItems.forEach(otherItem => {
                            if (otherItem !== item && otherItem.classList.contains('active')) {
                                otherItem.classList.remove('active');
                            }
                        });
                        
                        // 切换当前 FAQ 项
                        item.classList.toggle('active');
                    });
                    
                    // 获取文章ID并更新访问数
                    const articleId = item.getAttribute('data-article-id');
                    if (articleId) {
                        // 发送异步请求更新访问量
                        fetch(`/article/updateViews?id=${articleId}`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.code === 1) {
                                const viewCountElement = item.querySelector('.view-count');
                                if (viewCountElement) {
                                    viewCountElement.innerHTML = `<i class="far fa-eye"></i> ${data.views} 次浏览`;
                                }
                            }
                        })
                        .catch(error => console.error('Error:', error));
                    }
                });
                
                // FAQ 搜索功能
                const searchInput = document.getElementById('faqSearch');
                
                if (searchInput) {
                    searchInput.addEventListener('input', function() {
                        const searchTerm = this.value.toLowerCase();
                        
                        faqItems.forEach(item => {
                            const title = item.querySelector('.faq-title').textContent.toLowerCase();
                            const content = item.querySelector('.faq-content-inner').textContent.toLowerCase();
                            
                            if (title.includes(searchTerm) || content.includes(searchTerm)) {
                                item.style.display = 'block';
                            } else {
                                item.style.display = 'none';
                            }
                        });
                    });
                }

                // 移动端菜单交互
                const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
                const navLinks = document.querySelector('.nav-links');
                const navItems = document.querySelectorAll('.nav-item');
                
                if (mobileMenuToggle) {
                    mobileMenuToggle.addEventListener('click', function() {
                        navLinks.classList.toggle('active');
                        this.innerHTML = navLinks.classList.contains('active') 
                            ? '<i class="fas fa-times"></i>' 
                            : '<i class="fas fa-bars"></i>';
                            
                        // 添加移动端登录/注册按钮
                        if (navLinks.classList.contains('active') && !document.querySelector('.mobile-auth')) {
                            const mobileAuth = document.createElement('div');
                            mobileAuth.className = 'mobile-auth';
                            mobileAuth.innerHTML = `
                                <a href="/merchant/login" class="btn-login">
                                    <i class="fas fa-sign-in-alt"></i> 商户登录
                                </a>
                                <a href="/merchant/register" class="btn-register">
                                    <i class="fas fa-user-plus"></i> 商户注册
                                </a>
                            `;
                            navLinks.appendChild(mobileAuth);
                        }
                    });
                }
                
                // 移动端子菜单交互
                const handleSubMenu = () => {
                    if (window.innerWidth <= 576) {
                        navItems.forEach(item => {
                            const link = item.querySelector('.nav-link');
                            const subMenu = item.querySelector('.submenu');
                            
                            if (subMenu) {
                                link.addEventListener('click', function(e) {
                                    e.preventDefault();
                                    
                                    // 关闭其他打开的子菜单
                                    navItems.forEach(otherItem => {
                                        if (otherItem !== item && otherItem.classList.contains('open')) {
                                            otherItem.classList.remove('open');
                                            const otherIcon = otherItem.querySelector('.dropdown-icon');
                                            if (otherIcon) {
                                                otherIcon.style.transform = 'rotate(0)';
                                            }
                                        }
                                    });
                                    
                                    // 切换当前子菜单
                                    item.classList.toggle('open');
                                    const dropdownIcon = link.querySelector('.dropdown-icon');
                                    if (dropdownIcon) {
                                        dropdownIcon.style.transform = item.classList.contains('open') 
                                            ? 'rotate(180deg)' 
                                            : 'rotate(0)';
                                    }
                                });
                            }
                        });
                    }
                };
                
                handleSubMenu();
                
                // 窗口大小变化时重新处理
                window.addEventListener('resize', handleSubMenu);
            });
        </script>
    </body>
</html> 