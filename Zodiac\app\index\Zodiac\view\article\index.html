<!doctype html>
<html lang="en">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>{$title}</title>
        
        <link rel="shortcut icon" href="{$favicon}" type="image/x-icon">
        
        <!-- CSS 文件 -->
        <link href="/static/yibazhan/css/bootstrap.min.css" rel="stylesheet">
        <link href="/static/yibazhan/css/font-awesome.min.css" rel="stylesheet">
        <link href="/static/yibazhan/css/aos.css" rel="stylesheet">
        <link href="/static/yibazhan/css/css2.css" rel="stylesheet">
        <link href="/static/yibazhan/css/all.min.css" rel="stylesheet">
        <link href="/static/yibazhan/css/style.min.css" rel="stylesheet">
        <link href="/static/yibazhan/css/owl.carousel.min.css" rel="stylesheet">
        <link href="/static/yibazhan/css/owl.theme.default.min.css" rel="stylesheet">
        <link href="/static/yibazhan/css/jquery-ui.css" rel="stylesheet">
        <link href="/static/yibazhan/css/jquery-ui1.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
        
        <style>
            /* 导航样式 */
            .navbar-brand img {
                max-width: 150px;
                height: auto;
            }
            
            /* 文章列表样式 */
            .article-section {
                padding: 80px 0;
                background: var(--bg-gradient);
            }
            
            .article-container {
                max-width: 1000px;
                margin: 0 auto;
            }

            .notice-box {
                margin-top: 32px;
            }

            .notice-item {
                background-color: rgba(255, 255, 255, 0.1);
                padding: 25px;
                border-radius: 12px;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.1);
                margin-bottom: 25px;
                transition: all 0.3s ease;
            }

            .notice-item:hover {
                transform: translateY(-5px);
                background-color: rgba(255, 255, 255, 0.15);
                box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
            }

            .notice-item h3 {
                font-size: 20px;
                font-weight: 600;
                margin-bottom: 15px;
            }

            .notice-item h3 a {
                color: #fff;
                text-decoration: none;
                transition: color 0.3s ease;
            }

            .notice-item h3 a:hover {
                color: #007bff;
            }

            .notice-item p {
                color: rgba(255, 255, 255, 0.8);
                font-size: 15px;
                line-height: 1.6;
                margin-bottom: 15px;
            }

            .notice-item .date {
                color: rgba(255, 255, 255, 0.6);
                font-size: 13px;
                font-style: italic;
            }

            .page-title {
                text-align: center;
                color: #fff;
                margin-bottom: 40px;
            }

            .page-title h1 {
                font-size: 36px;
                font-weight: 600;
                margin-bottom: 15px;
            }

            .page-title p {
                color: rgba(255, 255, 255, 0.8);
                font-size: 16px;
            }

            /* 移动端适配 */
            @media (max-width: 768px) {
                .navbar-brand img {
                    max-width: 120px;
                }
                
                .navbar-nav {
                    margin-top: 1rem;
                    text-align: center;
                }
                
                .nav-item {
                    padding: 0.5rem 0;
                }
                
                .page-title h1 {
                    font-size: 28px;
                }
                
                .notice-item {
                    padding: 20px;
                }
            }

            .dropdown-arrow {
                margin-left: 4px;
                transition: transform 0.3s ease;
            }
            
            .dropdown.show .dropdown-arrow {
                transform: rotate(180deg);
            }
            
            .nav-link {
                display: flex;
                align-items: center;
            }
            
            /* 移除默认的下拉箭头 */
            .dropdown-toggle::after {
                display: none !important;
            }
            
            @media (max-width: 768px) {
                .dropdown-arrow {
                    margin-left: 8px;
                }
            }
        </style>
    </head>
    <body>
        <!-- 导航栏 -->
        <header class="float-start w-100">
            <nav class="navbar navbar-expand-lg navbar-light">
                <div class="container">
                    <a class="navbar-brand" href="/">
                        <img alt="logo" src="{$logo}" class="img-fluid">
                    </a>
                    <button class="navbar-toggler" type="button" data-bs-toggle="offcanvas" data-bs-target="#mobile-menu">
                        <span><i class="fas fa-bars"></i></span>
                    </button>
                    <div class="collapse navbar-collapse" id="navbarSupportedContent">
                        <ul class="navbar-nav mx-auto mb-2 mb-lg-0">
                            {foreach $navItems as $item}
                            <li class="nav-item {if !empty($item.children)}dropdown{/if}">
                                <a class="nav-link {if !empty($item.children)}dropdown-toggle{/if}" 
                                   href="{$item.href}" 
                                   {if $item.target}target="_blank"{/if}
                                   {if !empty($item.children)}data-bs-toggle="dropdown"{/if}>
                                    {$item.name}
                                    {if !empty($item.children)}
                                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor" class="dropdown-arrow" viewBox="0 0 16 16">
                                        <path d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z"/>
                                    </svg>
                                    {/if}
                                </a>
                                {if !empty($item.children)}
                                <ul class="dropdown-menu">
                                    {foreach $item.children as $child}
                                    <li>
                                        <a class="dropdown-item" 
                                           href="{$child.href}" 
                                           {if $child.target}target="_blank"{/if}>
                                            {$child.name}
                                        </a>
                                    </li>
                                    {/foreach}
                                </ul>
                                {/if}
                            </li>
                            {/foreach}
                        </ul>
                    </div>
                    <div class="d-none d-lg-block">
                        <div class="right-menui">
                            <ul>
                                <li>
                                    <a href="/merchant/login" class="btn consult-btn">商家中心</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </nav>
        </header>

        <!-- 文章列表部分 -->
        <section class="article-section">
            <div class="container article-container">
                <div class="page-title">
                    <h1>公告中心</h1>
                    <p>了解最新商城动态与重要通知</p>
                </div>
                
                <div class="notice-box">
                    {foreach $notice as $item}
                    <div class="notice-item" data-aos="fade-up">
                        <h3><a href="{$item.url}">{$item.title}</a></h3>
                        <p>{$item.content|raw}</p>
                        <span class="date">{$item.create_time|date="Y-m-d H:i"}</span>
                    </div>
                    {/foreach}
                </div>
            </div>
        </section>

        <!-- 页脚 -->
        <footer class="float-start w-100 pt-5">
            <div class="container">
                <div class="row row-cols-1 row-cols-sm-2 row-cols-lg-4 align-content-center">
                    <div class="col">
                        <a href="/">
                            <img alt="logo" src="{$logo}" class="img-fluid">
                        </a>
                        <p class="text-white mt-3 col-lg-10">我们致力于为您提供优质的商品和服务,让您享受愉快的购物体验</p>
                    </div>
                    <div class="col">
                        <div class="colm-footer">
                            <h5>联系我们</h5>
                            <ul>
                                <li><i class="fab fa-whatsapp"></i> ************</li>
                                <li><i class="fas fa-paper-plane"></i> <EMAIL></li>
                                <li><i class="fas fa-phone-alt"></i> ************</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col">
                        <div class="colm-footer">
                            <h5>我们的服务</h5>
                            <ul>
                                <li><a href="#">商品分类</a></li>
                                <li><a href="#">促销活动</a></li>
                                <li><a href="#">会员服务</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="col">
                        <div class="colm-footer">
                            <h5>快速链接</h5>
                            <ul>
                                <li><a href="#">关于我们</a></li>
                                <li><a href="#">商城资讯</a></li>
                                <li><a href="#">联系我们</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <hr class="my-4">
                <div class="d-md-flex align-items-center justify-content-between">
                    <p>Copyright &copy; {:date('Y')} {$siteName} All rights reserved.</p>
                    {if !empty($icpNumber) || !empty($gaNumber)}
                    <div class="beian-info">
                        {if !empty($icpNumber)}
                        <a href="https://beian.miit.gov.cn" target="_blank">{$icpNumber}</a>
                        {/if}
                        {if !empty($gaNumber)}
                        <span class="mx-2">|</span>
                        <a href="http://www.beian.gov.cn" target="_blank">
                            <img src="/assets/template/default/assets/wangan.a20583c8.png" alt="公安备案图标" style="vertical-align: -2px; margin-right: 3px; width: 16px; height: 16px;">
                            {$gaNumber}
                        </a>
                        {/if}
                        {if !empty($icpCert)}
                        <span class="mx-2">|</span>
                        <span>{$icpCert}</span>
                        {/if}
                    </div>
                    {/if}
                </div>
            </div>
        </footer>

        <!-- 移动端菜单 -->
        <div class="offcanvas offcanvas-end mobile-menu-div" id="mobile-menu">
            <div class="offcanvas-header">
                <button type="button" class="close-menu" data-bs-dismiss="offcanvas">
                    <span>关闭</span> <i class="fas fa-long-arrow-alt-right"></i>
                </button>
            </div>
            <div class="offcanvas-body">
                <div class="head-contact">
                    <a href="/" class="logo-side">
                        <img src="{$logo}" alt="logo">
                    </a>
                    <div class="mobile-menu-sec mt-5">
                        <nav class="navbar navbar-expand navbar-light">
                            <div class="collapse navbar-collapse">
                                <ul class="navbar-nav">
                                    {foreach $navItems as $item}
                                    <li class="nav-item {if !empty($item.children)}dropdown{/if}">
                                        <a class="nav-link {if !empty($item.children)}dropdown-toggle{/if}" 
                                           href="{$item.href}"
                                           {if $item.target}target="_blank"{/if}
                                           {if !empty($item.children)}data-bs-toggle="dropdown"{/if}>
                                            {$item.name}
                                            {if !empty($item.children)}
                                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor" class="dropdown-arrow" viewBox="0 0 16 16">
                                                <path d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z"/>
                                            </svg>
                                            {/if}
                                        </a>
                                        {if !empty($item.children)}
                                        <ul class="dropdown-menu">
                                            {foreach $item.children as $child}
                                            <li>
                                                <a class="dropdown-item" 
                                                   href="{$child.href}"
                                                   {if $child.target}target="_blank"{/if}>
                                                    {$child.name}
                                                </a>
                                            </li>
                                            {/foreach}
                                        </ul>
                                        {/if}
                                    </li>
                                    {/foreach}
                                </ul>
                            </div>
                        </nav>
                    </div>
                </div>
            </div>
        </div>

        <!-- JS文件 -->
        <script src="/static/yibazhan/js/jquery.min.js"></script>
        <script src="/static/yibazhan/js/bootstrap.bundle.min.js"></script>
        <script src="/static/yibazhan/js/custom.js"></script>
        <script src="/static/yibazhan/js/aos.js"></script>
        <script src="/static/yibazhan/js/owl.carousel.min.js"></script>
        <script src="/static/yibazhan/js/jquery-ui.min.js"></script>
        <script>
            AOS.init({
                offset: 100,
                easing: 'ease',
                delay: 0,
                once: true,
                duration: 800,
            });

            // 禁用右键菜单
            document.addEventListener('contextmenu', function(e) {
                e.preventDefault();
                return false;
            });

            // 禁用F12、Ctrl+Shift+I、Ctrl+Shift+J、Ctrl+Shift+C、Ctrl+U
            document.addEventListener('keydown', function(e) {
                if (
                    e.keyCode === 123 || // F12
                    (e.ctrlKey && e.shiftKey && e.keyCode === 73) || // Ctrl+Shift+I
                    (e.ctrlKey && e.shiftKey && e.keyCode === 74) || // Ctrl+Shift+J
                    (e.ctrlKey && e.shiftKey && e.keyCode === 67) || // Ctrl+Shift+C
                    (e.ctrlKey && e.keyCode === 85) // Ctrl+U
                ) {
                    e.preventDefault();
                    return false;
                }
            });

            // 禁用开发者工具快捷键
            window.addEventListener('keydown', function(e) {
                if (e.defaultPrevented) {
                    return;
                }
                if (e.key === 'F12' || 
                    (e.ctrlKey && e.key === 'I') ||
                    (e.ctrlKey && e.key === 'J') ||
                    (e.ctrlKey && e.key === 'C') ||
                    (e.ctrlKey && e.key === 'U')) {
                    e.preventDefault();
                }
            });

            // 禁用控制台
            setInterval(function() {
                debugger;
            }, 100);
        </script>
    </body>
</html>
