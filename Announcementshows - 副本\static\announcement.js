(function() {
    // 添加文字渐变相关的CSS样式
    function addTextGradientStyles() {
        const styleId = 'text-gradient-styles';
        if (document.getElementById(styleId)) {
            return; // 样式已存在
        }

        const style = document.createElement('style');
        style.id = styleId;
        style.textContent = `
            /* 文字渐变背景支持 */
            .has-background-gradient::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: var(--bg-gradient);
                z-index: -1;
                border-radius: inherit;
            }

            /* 确保文字渐变元素的层级 */
            .has-background-gradient {
                position: relative;
                z-index: 1;
            }
        `;
        document.head.appendChild(style);
    }

    // 立即添加样式
    addTextGradientStyles();

    // 添加辅助函数，生成安全的SVG字符串，避免NaN错误
    function createSafeSvg(type, options = {}) {
        let svgString = '';
        
        try {
            switch(type) {
                case 'rightArrow':
                    // 使用固定数值的路径字符串
                    svgString = '<svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="currentColor" class="arco-icon arco-icon-right" stroke-width="4" stroke-linecap="butt" stroke-linejoin="miter"><path d="M16 39.513L31.556 23.956L16 8.4"></path></svg>';
                    break;
                case 'smallRightArrow':
                    // 使用固定数值的路径字符串
                    svgString = '<svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"><polyline points="9 18, 15 12, 9 6"></polyline></svg>';
                    break;
                default:
                    console.warn('未知的SVG类型:', type);
                    // 提供一个简单的替代方案
                    svgString = '<svg viewBox="0 0 10 10" width="10" height="10"></svg>';
            }
        } catch (err) {
            console.error('创建SVG时出错:', err);
            // 出错时返回一个空的SVG
            svgString = '<svg width="0" height="0"></svg>';
        }
        
        return svgString;
    }
    
    // 将函数挂载到window对象上，使其在整个文件中可用
    window.createSafeSvg = createSafeSvg;
    
    // 添加用户广告管理脚本加载
    function loadUserAdsScript() {
        // 直接使用相对路径，避免多次尝试
        const script = document.createElement('script');
        script.src = './static/user-ads.js';
        script.async = true;
    }

    // 确保脚本加载
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', loadUserAdsScript);
    } else {
        loadUserAdsScript();
    }

    // 添加刷新监听器
    function setupRefreshListener() {
        // 检查是否有刷新信号
        function checkForRefresh() {
            try {
                const refreshData = localStorage.getItem('announcement_refresh');
                if (refreshData) {
                    const data = JSON.parse(refreshData);
                    const now = new Date().getTime();
                    
                    // 如果刷新信号是最近5分钟内的
                    if (data.type === 'REFRESH_ANNOUNCEMENTS' && (now - data.timestamp < 300000)) {
                        // 清除信号
                        localStorage.removeItem('announcement_refresh');
                        
                        // 刷新广告
                        // console.log('检测到广告更新，正在刷新...');
                        initAnnouncement();
                    }
                }
            } catch (e) {
                // console.error('检查刷新信号出错:', e);
            }
        }
        
        // 立即检查一次
        checkForRefresh();
        
        // 每分钟检查一次
        setInterval(checkForRefresh, 60000);
        
        // 监听storage事件
        window.addEventListener('storage', (event) => {
            if (event.key === 'announcement_refresh') {
                checkForRefresh();
            }
        });
    }

    // 添加判断是否为移动设备的函数
    function isMobileDevice() {
        return (window.innerWidth <= 768) || 
               /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    function initAnnouncement() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                createAnnouncement();
                setupRefreshListener();
            });
        } else {
            createAnnouncement();
            setupRefreshListener();
        }
    }

    async function createAnnouncement() {
        try {
            const response = await fetch('/plugin/Announcementshows/Api/fetchData', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            const data = await response.json();
            if (!data || typeof data !== 'object') {
                // console.error('获取公告数据格式错误');
                return;
            }
            
            if (data.code === 200 && data.data && data.data.status === 1) {
                showAnnouncement(data.data);
            } else {
                // console.error('获取公告数据状态错误:', data);
            }
        } catch (error) {
            // console.error('获取公告失败:', error);
        }
    }

    function showAnnouncement(config) {
        try {
            if (!config || typeof config !== 'object') {
                // console.error('公告配置数据无效');
                return;
            }

            if (config.empty_ad_image) {
                // console.log('配置中的未租用图片:', config.empty_ad_image);
            }

            let announcements = [];
            try {
                announcements = JSON.parse(config.announcements || '[]');
            } catch (e) {
                // console.error('解析公告数据失败:', e);
                announcements = [];
            }

            const now = new Date().getTime();
            
            // 处理空广告位和过期广告位
            const processedAnnouncements = processEmptyAds(announcements, config);
            
            // 获取有效的广告位
            const validAnnouncements = processedAnnouncements.filter(item => {
                if (!item || typeof item !== 'object') return false;
                return true; // 保留所有广告位，因为我们已经处理了空和过期的广告位
            }).map(item => {
                // 统一链接字段，确保兼容性
                if (item.link_url && !item.link) {
                    item.link = item.link_url;
                } else if (item.link && !item.link_url) {
                    item.link_url = item.link;
                }
                return item;
            });
            
            if (validAnnouncements.length === 0) return;
            
            // 检测是否为移动设备
            const isMobile = isMobileDevice();
            
            // 根据设备类型和配置选择模板
            let announcement;
            
            if (isMobile && config.mobile_enabled) {
                // 手机端模板 - 根据设置的当前模板显示
                switch(config.mobile_current_template) {
                    case 1:
                        // 模板1 - Banner轮播样式
                        announcement = createMobileTemplate1(validAnnouncements, config);
                        break;
                    case 2:
                        // 模板2 - 走马灯样式
                        announcement = createMobileTemplate2(validAnnouncements, config);
                        break;
                    case 3:
                        // 模板3 - 彩色卡片样式
                        announcement = createMobileTemplate3(validAnnouncements, config);
                        break;
                    default:
                        // 默认使用垂直彩色卡片列表模板
                        announcement = createMobileCardList(validAnnouncements, config);
                }
            } else {
                // PC端模板 - 改为使用网格布局代替原来的轮播图
                announcement = config.display_mode === 1 
                    ? createGridLayout(validAnnouncements, config) // 图片模式使用网格布局
                    : createTextAnnouncement(validAnnouncements, config);
            }
                
            insertAnnouncement(announcement);
            observeDOMChanges(announcement);
            
            // 将完整的公告数据（包括颜色信息）保存到 contentWrapper 的 dataset 中
            const contentWrapper = document.querySelector('.announcement-content');
            if (contentWrapper) {
                contentWrapper.dataset.announcements = JSON.stringify(validAnnouncements);
            }
            
            // 添加用户信息显示的悬浮提示（如果有）
            validAnnouncements.forEach((item, index) => {
                if (item.username && contentWrapper) {
                    const adItem = contentWrapper.querySelectorAll('.announcement-item')[index];
                    if (adItem) {
                        const tooltip = document.createElement('div');
                        tooltip.className = 'ad-user-tooltip';
                        tooltip.innerHTML = `租用用户: ${item.nickname || item.username}`;
                        tooltip.style.display = 'none';
                        adItem.appendChild(tooltip);
                        
                        adItem.addEventListener('mouseenter', () => {
                            tooltip.style.display = 'block';
                        });
                        adItem.addEventListener('mouseleave', () => {
                            tooltip.style.display = 'none';
                        });
                    }
                }
            });
            
        } catch (error) {
            // console.error('显示公告失败:', error);
        }
    }

    // 处理空广告位显示
    function processEmptyAds(announcements, config) {
        // 填充空广告位，确保至少有一个广告位
        if (!announcements || announcements.length === 0) {
            const emptyAdCount = 1;  // 默认创建1个广告位
            const emptyAds = [];

            for (let i = 0; i < emptyAdCount; i++) {
                let adText = config.empty_ad_text || '广告位还没出租，点击立即租用';
                
                // 替换占位符
                adText = adText.replace('{position}', (i + 1));
                
                emptyAds.push({
                    content: config.display_mode === 1 ? config.empty_ad_image : adText,
                    isEmpty: true,
                    link: config.rent_link || '',
                    image_url: config.empty_ad_image || '',
                    backgroundColor: config.background,
                    textColor: config.text_color
                });
            }
            
            return emptyAds;
        }
        
        // 检查并更新广告位状态
        announcements.forEach((announcement, index) => {
            const now = new Date().getTime();
            const endTime = announcement.end_time ? new Date(announcement.end_time).getTime() : 0;
            
            // 判断是否过期
            if (endTime && now > endTime) {
                announcement.isExpired = true;
                // 不替换为空内容，保留原内容，只标记为过期
            }
            
            // 确保有广告内容
            if (!announcement.content && config.display_mode === 0) {
                let adText = config.empty_ad_text || '广告位还没出租，点击立即租用';
                adText = adText.replace('{position}', (index + 1));
                announcement.content = adText;
                announcement.isEmpty = true;
            } else if (!announcement.content && config.display_mode === 1) {
                // 图片模式下，使用默认空广告图片
                announcement.content = config.empty_ad_image || '';
                announcement.image_url = config.empty_ad_image || '';
                announcement.isEmpty = true;
            }
            
            // 确保颜色配置
            if (!announcement.backgroundColor) {
                announcement.backgroundColor = config.background;
            }
            if (!announcement.textColor) {
                announcement.textColor = config.text_color;
            }
        });
        
        return announcements;
    }

    // 添加网格布局广告展示函数
    function createGridLayout(announcements, config) {
        // 设置基本容器
        const gridContainer = document.createElement('div');
        gridContainer.className = 'announcement-grid-container';
        gridContainer.style.width = '100%';
        gridContainer.style.maxWidth = '1200px';
        gridContainer.style.margin = '0 auto 15px auto';
        gridContainer.style.display = 'flex';
        gridContainer.style.flexWrap = 'wrap';
        gridContainer.style.gap = '15px';
        gridContainer.style.padding = '10px';
        gridContainer.style.boxSizing = 'border-box';
        gridContainer.style.overflow = 'auto';
        gridContainer.style.maxHeight = '700px'; // 设置最大高度,超出时显示滚动条

        // 处理每个广告项
        announcements.forEach((announcement, index) => {
            // 创建单个广告项容器
            const adItem = document.createElement('div');
            adItem.className = 'announcement-grid-item';
            adItem.style.width = 'calc(50% - 8px)'; // 两列布局,减去间隔的一半
            adItem.style.position = 'relative';
            adItem.style.borderRadius = '8px';
            adItem.style.overflow = 'hidden';
            adItem.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
            adItem.style.backgroundColor = '#f5f5f5'; // 添加背景色，使图片与容器边界更清晰

            // 创建图片容器
            const imgContainer = document.createElement('div');
            imgContainer.className = 'announcement-grid-img-container';
            imgContainer.style.width = '100%';
            imgContainer.style.height = '180px'; // 增加高度，给图片更多显示空间
            imgContainer.style.display = 'flex';
            imgContainer.style.alignItems = 'center';
            imgContainer.style.justifyContent = 'center';
            imgContainer.style.overflow = 'hidden';
            imgContainer.style.backgroundColor = '#ffffff'; // 给图片容器添加背景色

            // 创建图片元素
            const img = document.createElement('img');
            img.className = 'announcement-grid-img';
            img.style.maxWidth = '100%';
            img.style.maxHeight = '100%';
            img.style.objectFit = 'contain'; // 改为contain，确保图片完整显示而不被裁剪
            img.style.display = 'block';
            img.alt = `公告图片 ${index + 1}`;
            
            // 处理图片路径
            let imgSrc = announcement.content;
            // 确保图片路径正确
            if (imgSrc && !imgSrc.match(/^(https?:\/\/|\/)/i)) {
                imgSrc = '/' + imgSrc;
            }
            
            // 如果有image_url字段则优先使用
            if (announcement.image_url) {
                imgSrc = announcement.image_url;
            }

            img.src = imgSrc;
            
            // 图片加载错误处理
            img.onerror = function() {
                // 使用空图片占位符
                if (config.empty_ad_image) {
                    img.src = config.empty_ad_image;
                }
            };

            // 将图片添加到图片容器
            imgContainer.appendChild(img);

            // 检查广告位状态
            const now = new Date().getTime();
            const endTime = announcement.end_time ? new Date(announcement.end_time).getTime() : 0;
            const isExpired = endTime && now > endTime;
            const isEmpty = !announcement.content || (announcement.isEmpty === true);

            // 如果有链接,创建链接包装器
            if (announcement.link_url) {
                const linkWrapper = document.createElement('a');
                linkWrapper.href = announcement.link_url;
                linkWrapper.target = '_blank';
                linkWrapper.style.display = 'block';
                linkWrapper.style.width = '100%';
                linkWrapper.style.height = '100%';
                linkWrapper.style.textDecoration = 'none';
                linkWrapper.appendChild(imgContainer); // 使用imgContainer而不是直接使用img
                adItem.appendChild(linkWrapper);
            } else {
                adItem.appendChild(imgContainer); // 使用imgContainer而不是直接使用img
                
                // 如果是空广告位、过期广告位或未租用广告位，添加点击弹出购买窗口
                if (isEmpty || isExpired || !announcement.user_id) {
                    adItem.style.cursor = 'pointer';
                    adItem.addEventListener('click', function() {
                        showRentDialog(index + 1, config);
                    });
                }
            }

            // 添加广告信息覆盖层
            const infoOverlay = document.createElement('div');
            infoOverlay.className = 'announcement-grid-overlay';
            infoOverlay.style.position = 'absolute';
            infoOverlay.style.bottom = '0';
            infoOverlay.style.left = '0';
            infoOverlay.style.right = '0';
            infoOverlay.style.padding = '8px 12px';
            infoOverlay.style.background = 'rgba(0,0,0,0.6)';
            infoOverlay.style.color = '#fff';
            infoOverlay.style.fontSize = '12px';

            // 显示广告位信息或状态
            if (isEmpty) {
                infoOverlay.textContent = `广告位 ${index + 1} (未租用)`;
            } else if (isExpired) {
                infoOverlay.textContent = `广告位 ${index + 1} (已过期)`;
            } else if (announcement.user_id) {
                infoOverlay.textContent = announcement.username ? 
                    `广告位 ${index + 1} (${announcement.username})` : 
                    `广告位 ${index + 1} (已租用)`;
            } else {
                infoOverlay.textContent = `广告位 ${index + 1}`;
            }

            adItem.appendChild(infoOverlay);
            
            // 如果是已租用状态,添加操作按钮
            if (announcement.user_id && !isEmpty && !isExpired) {
                const actionButton = document.createElement('div');
                actionButton.className = 'announcement-grid-action';
                actionButton.style.position = 'absolute';
                actionButton.style.top = '8px';
                actionButton.style.right = '8px';
                actionButton.style.background = 'rgba(0,0,0,0.6)';
                actionButton.style.color = '#fff';
                actionButton.style.borderRadius = '4px';
                actionButton.style.padding = '4px 8px';
                actionButton.style.fontSize = '12px';
                actionButton.style.cursor = 'pointer';
                
                const days = Math.ceil((endTime - now) / (24 * 60 * 60 * 1000));
                actionButton.textContent = endTime ? `剩余 ${days} 天` : '已租用';
                
                adItem.appendChild(actionButton);
            }

            gridContainer.appendChild(adItem);
        });

        // 添加CSS样式
        const gridStyles = document.createElement('style');
        gridStyles.textContent = `
            .announcement-grid-container::-webkit-scrollbar {
                width: 6px;
                height: 6px;
            }
            .announcement-grid-container::-webkit-scrollbar-track {
                background: #f1f1f1;
                border-radius: 3px;
            }
            .announcement-grid-container::-webkit-scrollbar-thumb {
                background: #888;
                border-radius: 3px;
            }
            .announcement-grid-container::-webkit-scrollbar-thumb:hover {
                background: #555;
            }
            .announcement-grid-item {
                transition: transform 0.3s, box-shadow 0.3s;
            }
            .announcement-grid-item:hover {
                transform: translateY(-3px);
                box-shadow: 0 5px 15px rgba(0,0,0,0.15);
            }
            .announcement-grid-img {
                transition: transform 0.3s;
            }
            .announcement-grid-item:hover .announcement-grid-img {
                transform: scale(1.05);
            }
        `;
        document.head.appendChild(gridStyles);
        
        return gridContainer;
    }

    function createImageCarousel(announcements, config) {
        // 设置固定尺寸模式
        config.fixed_size = true;
        
        // 创建轮播容器
        const carouselContainer = document.createElement('div');
        carouselContainer.className = 'announcement-carousel-container';
        carouselContainer.style.width = '1168px'; // 固定宽度为1168px
        carouselContainer.style.maxWidth = '100%'; // 但在小屏幕上不超过父容器
        
        // 使用后台设置的高度值
        const height = parseInt(config.height) || 120;
        carouselContainer.style.height = height + 'px';
        
        carouselContainer.style.position = 'relative';
        carouselContainer.style.overflow = 'hidden';
        carouselContainer.style.background = config.background || '#f5f7fa';
        carouselContainer.style.borderRadius = '4px';
        carouselContainer.style.boxShadow = '0 1px 5px rgba(0,0,0,0.05)';
        carouselContainer.style.margin = '0 auto'; // 居中显示
        
        // 创建轮播项目容器
        const carouselItemsContainer = document.createElement('div');
        carouselItemsContainer.className = 'announcement-carousel-items';
        carouselItemsContainer.style.display = 'flex';
        carouselItemsContainer.style.width = '100%';
        carouselItemsContainer.style.height = '100%';
        carouselItemsContainer.style.transition = 'transform 0.5s ease';
        carouselContainer.appendChild(carouselItemsContainer);
        
        // 轮播图片数组
        const slides = [];
        
        // 为每个公告创建图片轮播项
        const validAnnouncements = announcements.filter(item => item && (item.content || item.image_url));
        validAnnouncements.forEach((announcement, index) => {
            // 创建轮播项
            const carouselItem = document.createElement('div');
            carouselItem.className = 'announcement-carousel-item';
            carouselItem.dataset.index = index;
            carouselItem.style.width = '100%';
            carouselItem.style.height = '100%';
            carouselItem.style.flexShrink = '0';
            carouselItem.style.display = 'flex';
            carouselItem.style.justifyContent = 'center';
            carouselItem.style.alignItems = 'center';
            carouselItem.style.position = 'relative';
            
            // 创建图片元素
            const img = document.createElement('img');
            img.className = 'announcement-carousel-img';
            
            // 尺寸控制，根据后台设置的高度
            if (config.fixed_size) {
                // 使用固定尺寸模式
                img.style.width = '1168px';
                img.style.height = height + 'px'; // 使用后台设置的高度
                img.style.objectFit = 'fill'; // 拉伸填满固定尺寸
            } else {
                // 使用自适应模式
                img.style.width = '100%';
                img.style.height = '100%';
                img.style.objectFit = 'cover'; // 覆盖整个区域，可能会裁剪
            }
            
            img.style.objectPosition = 'center'; // 居中显示
            img.style.display = 'block'; // 防止底部留白
            img.alt = `公告图片 ${index + 1}`;
            
            // 处理图片路径
            let imgSrc = announcement.content;
            // 确保图片路径正确（如果不是http开头或者已经有/开头，则加上/）
            if (imgSrc && !imgSrc.match(/^(https?:\/\/|\/)/i)) {
                imgSrc = '/' + imgSrc;
            }
            
            // 如果有image_url字段则优先使用
            if (announcement.image_url) {
                imgSrc = announcement.image_url;
            }
            
            // 添加参数强制图片服务器压缩图片尺寸
            if (imgSrc) {
                // 添加时间戳防止缓存
                const timestamp = new Date().getTime();
                // 添加尺寸参数，获取指定尺寸的图片
                const separator = imgSrc.includes('?') ? '&' : '?';
                imgSrc = `${imgSrc}${separator}t=${timestamp}&w=1168&h=${height}`; // 使用后台设置的高度
            }
            
            img.src = imgSrc;
            
            // 图片加载错误处理
            img.onerror = function() {
                // console.error(`公告图片加载失败: ${imgSrc}`);
                
                // 尝试使用备用URL
                if (announcement.image_url && announcement.image_url !== imgSrc) {
                    // console.log(`尝试备用图片URL: ${announcement.image_url}`);
                    img.src = announcement.image_url;
                } else {
                    // 使用空图片占位符
                    if (config.empty_ad_image) {
                        img.src = config.empty_ad_image;
                    }
                }
            };
            
            // 如果有链接，创建链接包装器
            if (announcement.link_url) {
                const linkWrapper = document.createElement('a');
                linkWrapper.href = announcement.link_url;
                linkWrapper.target = '_blank';
                linkWrapper.style.display = 'block';
                linkWrapper.style.width = '100%';
                linkWrapper.style.height = '100%';
                linkWrapper.style.textDecoration = 'none';
                linkWrapper.appendChild(img);
                carouselItem.appendChild(linkWrapper);
            } else {
                carouselItem.appendChild(img);
                
                // 如果是空广告位、过期广告位或未租用广告位，添加点击弹出购买窗口
                if (isEmpty || isExpired || !announcement.user_id) {
                    carouselItem.style.cursor = 'pointer';
                    carouselItem.addEventListener('click', function() {
                        showRentDialog(index + 1, config);
                    });
                }
            }
            
            // 为轮播项添加文字覆盖层（如果有）
            if (announcement.overlay_text) {
                const textOverlay = document.createElement('div');
                textOverlay.className = 'carousel-text-overlay';
                textOverlay.style.position = 'absolute';
                textOverlay.style.bottom = '20px';
                textOverlay.style.left = '20px';
                textOverlay.style.right = '20px';
                textOverlay.style.padding = '10px 15px';
                textOverlay.style.backgroundColor = 'rgba(0,0,0,0.5)';
                textOverlay.style.color = '#fff';
                textOverlay.style.borderRadius = '4px';
                textOverlay.style.fontSize = '14px';
                textOverlay.style.fontWeight = 'bold';
                textOverlay.textContent = announcement.overlay_text;
                
                // 添加白光闪烁效果
                if (announcement.textColor) {
                    textOverlay.classList.add('white-glow-text');
                    textOverlay.style.color = announcement.textColor;
                }
                
                carouselItem.appendChild(textOverlay);
            }
            
            carouselItemsContainer.appendChild(carouselItem);
            slides.push(carouselItem);
        });
        
        // 如果没有有效的轮播项，返回空容器
        if (slides.length === 0) {
            const emptyContainer = document.createElement('div');
            emptyContainer.style.width = '100%';
            emptyContainer.style.height = height + 'px';
            emptyContainer.style.display = 'flex';
            emptyContainer.style.justifyContent = 'center';
            emptyContainer.style.alignItems = 'center';
            emptyContainer.style.background = config.background || '#f5f7fa';
            emptyContainer.style.color = config.text_color || '#909399';
            emptyContainer.textContent = '暂无广告内容';
            // 为默认文字也添加闪烁效果
            emptyContainer.classList.add('white-glow-text');
            return emptyContainer;
        }
        
        // 添加操作按钮容器 (仅对第一个轮播项)
        if (validAnnouncements.length > 0) {
            const actionButtonsContainer = document.createElement('div');
            actionButtonsContainer.className = 'announcement-action-buttons';
            actionButtonsContainer.style.position = 'absolute';
            actionButtonsContainer.style.top = '5px';
            actionButtonsContainer.style.right = '5px';
            actionButtonsContainer.style.zIndex = '10';
            
            const announcement = validAnnouncements[0];
            const now = new Date().getTime();
            const endTime = announcement.end_time ? new Date(announcement.end_time).getTime() : 0;
            const isExpired = endTime && now > endTime;
            const isEmpty = !announcement.content || (announcement.isEmpty === true);
            
            if (isEmpty) {
                // 空广告位显示租用按钮
                const rentButton = document.createElement('button');
                rentButton.className = 'announcement-action-button rent-button';
                rentButton.textContent = '租用广告位';
                rentButton.style.padding = '4px 8px';
                rentButton.style.backgroundColor = '#409eff';
                rentButton.style.color = 'white';
                rentButton.style.border = 'none';
                rentButton.style.borderRadius = '4px';
                rentButton.style.cursor = 'pointer';
                rentButton.style.fontSize = '12px';
                rentButton.style.marginLeft = '5px';
                
                rentButton.onclick = (e) => {
                    e.stopPropagation(); // 阻止事件冒泡
                    showRentDialog(1, config);
                };
                
                actionButtonsContainer.appendChild(rentButton);
            } else if (isExpired) {
                // 如果到期，显示续费按钮
                const rentButton = document.createElement('button');
                rentButton.className = 'announcement-action-button renew-button';
                rentButton.textContent = '续费广告位';
                rentButton.style.padding = '4px 8px';
                rentButton.style.backgroundColor = '#67c23a';
                rentButton.style.color = 'white';
                rentButton.style.border = 'none';
                rentButton.style.borderRadius = '4px';
                rentButton.style.cursor = 'pointer';
                rentButton.style.fontSize = '12px';
                rentButton.style.marginLeft = '5px';
                
                rentButton.onclick = (e) => {
                    e.stopPropagation();
                    showRenewDialog(1, config);
                };
                
                actionButtonsContainer.appendChild(rentButton);
            } else if (announcement.user_id) {
                // 有用户ID表示有人租用，显示编辑按钮
                const expiryButton = document.createElement('button');
                expiryButton.className = 'announcement-action-button expiry-button';
                expiryButton.style.padding = '4px 8px';
                expiryButton.style.backgroundColor = '#f56c6c';
                expiryButton.style.color = 'white';
                expiryButton.style.border = 'none';
                expiryButton.style.borderRadius = '4px';
                expiryButton.style.cursor = 'default';
                expiryButton.style.fontSize = '12px';
                
                const days = Math.ceil((endTime - now) / (24 * 60 * 60 * 1000));
                expiryButton.textContent = `剩余 ${days} 天`;
                expiryButton.title = '到期时间: ' + (announcement.end_time || '未知');
                actionButtonsContainer.appendChild(expiryButton);
                
                // 添加编辑按钮
                if (announcement.user_id === getCurrentUserId()) {
                    const editButton = document.createElement('button');
                    editButton.textContent = '编辑广告';
                    editButton.className = 'announcement-action-button edit-button';
                    editButton.style.padding = '4px 8px';
                    editButton.style.backgroundColor = '#909399';
                    editButton.style.color = 'white';
                    editButton.style.border = 'none';
                    editButton.style.borderRadius = '4px';
                    editButton.style.cursor = 'pointer';
                    editButton.style.fontSize = '12px';
                    editButton.style.marginLeft = '5px';
                    
                    editButton.onclick = (e) => {
                        e.stopPropagation();
                        // 调用编辑函数
                        showEditDialog(1);
                    };
                    actionButtonsContainer.appendChild(editButton);
                }
            }
            
            slides[0].appendChild(actionButtonsContainer);
        }
        
        // 添加指示器
        if (slides.length > 1) {
            const indicators = document.createElement('div');
            indicators.className = 'announcement-carousel-indicators';
            indicators.style.position = 'absolute';
            indicators.style.bottom = '5px';
            indicators.style.left = '50%';
            indicators.style.transform = 'translateX(-50%)';
            indicators.style.display = 'flex';
            indicators.style.gap = '5px';
            
            slides.forEach((_, i) => {
                const indicator = document.createElement('span');
                indicator.className = 'announcement-carousel-indicator';
                indicator.style.width = '8px';
                indicator.style.height = '8px';
                indicator.style.borderRadius = '50%';
                indicator.style.backgroundColor = i === 0 ? '#409eff' : '#dcdfe6';
                indicator.style.cursor = 'pointer';
                indicator.onclick = () => showSlide(i);
                
                indicators.appendChild(indicator);
            });
            
            carouselContainer.appendChild(indicators);
        }
        
        // 添加轮播控制
        let currentSlide = 0;
        let autoScrollInterval;
        
        // 显示指定幻灯片
        function showSlide(n) {
            const slideCount = slides.length;
            if (slideCount === 0) return;
            
            // 处理循环
            currentSlide = (n + slideCount) % slideCount;
            
            // 更新轮播位置
            carouselItemsContainer.style.transform = `translateX(-${currentSlide * 100}%)`;
            
            // 更新指示器状态
            const indicators = carouselContainer.querySelectorAll('.announcement-carousel-indicator');
            indicators.forEach((indicator, i) => {
                indicator.style.backgroundColor = i === currentSlide ? '#409eff' : '#dcdfe6';
            });
            
            // 重置自动滚动计时器
            resetAutoScroll();
        }
        
        // 如果有多个广告，添加前后按钮
        if (slides.length > 1) {
            const prevBtn = document.createElement('button');
            prevBtn.className = 'announcement-carousel-prev';
            prevBtn.innerHTML = '&lt;';
            prevBtn.style.position = 'absolute';
            prevBtn.style.top = '50%';
            prevBtn.style.left = '10px';
            prevBtn.style.transform = 'translateY(-50%)';
            prevBtn.style.backgroundColor = 'rgba(0, 0, 0, 0.3)';
            prevBtn.style.color = 'white';
            prevBtn.style.border = 'none';
            prevBtn.style.width = '30px';
            prevBtn.style.height = '30px';
            prevBtn.style.borderRadius = '50%';
            prevBtn.style.cursor = 'pointer';
            prevBtn.style.zIndex = '2';
            prevBtn.style.fontSize = '16px';
            prevBtn.style.display = 'flex';
            prevBtn.style.alignItems = 'center';
            prevBtn.style.justifyContent = 'center';
            prevBtn.onclick = () => showSlide(currentSlide - 1);
            
            const nextBtn = document.createElement('button');
            nextBtn.className = 'announcement-carousel-next';
            nextBtn.innerHTML = '&gt;';
            nextBtn.style.position = 'absolute';
            nextBtn.style.top = '50%';
            nextBtn.style.right = '10px';
            nextBtn.style.transform = 'translateY(-50%)';
            nextBtn.style.backgroundColor = 'rgba(0, 0, 0, 0.3)';
            nextBtn.style.color = 'white';
            nextBtn.style.border = 'none';
            nextBtn.style.width = '30px';
            nextBtn.style.height = '30px';
            nextBtn.style.borderRadius = '50%';
            nextBtn.style.cursor = 'pointer';
            nextBtn.style.zIndex = '2';
            nextBtn.style.fontSize = '16px';
            nextBtn.style.display = 'flex';
            nextBtn.style.alignItems = 'center';
            nextBtn.style.justifyContent = 'center';
            nextBtn.onclick = () => showSlide(currentSlide + 1);
            
            carouselContainer.appendChild(prevBtn);
            carouselContainer.appendChild(nextBtn);
        }
        
        // 自动滚动功能
        function startAutoScroll() {
            if (slides.length <= 1) return;
            
            autoScrollInterval = setInterval(() => {
                showSlide(currentSlide + 1);
            }, config.scroll_interval || 5000);
        }
        
        // 重置自动滚动
        function resetAutoScroll() {
            if (autoScrollInterval) {
                clearInterval(autoScrollInterval);
            }
            if (config.auto_scroll && slides.length > 1) {
                startAutoScroll();
            }
        }
        
        // 鼠标悬停时暂停自动滚动
        carouselContainer.addEventListener('mouseenter', () => {
            if (autoScrollInterval) {
                clearInterval(autoScrollInterval);
            }
        });
        
        carouselContainer.addEventListener('mouseleave', () => {
            if (config.auto_scroll) {
                startAutoScroll();
            }
        });
        
        // 如果配置了自动滚动，启动它
        if (config.auto_scroll) {
            startAutoScroll();
        }
        
        return carouselContainer;
    }

    function createTextAnnouncement(validAnnouncements, config) {
        // 直接使用后台设置的高度值
        const height = parseInt(config.height) || 120;
        
        const announcement = document.createElement('div');
        announcement.className = 'announcement-container';
        announcement.setAttribute('data-v-260b1a98', '');

        const routerContainer = document.createElement('div');
        routerContainer.className = 'router-container';
        routerContainer.setAttribute('data-v-260b1a98', '');
        routerContainer.style.cssText = `
            background: #fff;
            border-radius: 12px;
            margin: 0 0 8px 0;
            padding: 16px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.03);
        `;

        const contentWrapper = document.createElement('div');
        contentWrapper.className = 'announcement-content';

        // 移动端添加标题
        if (window.innerWidth <= 767) {
            const titleDiv = document.createElement('div');
            titleDiv.className = 'router-container-header';
            titleDiv.textContent = '广告合作';
            routerContainer.appendChild(titleDiv);
        }

        const isMobile = window.innerWidth <= 768;
        if (isMobile) {
            contentWrapper.style.cssText = `
                display: flex;
                flex-wrap: wrap;
                gap: 10px;
                min-height: ${height}px;
                max-height: 500px;
                background: ${config.background};
                color: ${config.text_color};
                margin: 0 16px;
            `;
        } else {
            contentWrapper.style.cssText = `
                display: flex;
                flex-wrap: wrap;
                gap: 10px;
                row-gap: 10px;
                padding: ${config.padding}px;
                min-height: ${height}px;
                max-height: 500px;
                background: ${config.background};
                color: ${config.text_color};
                border-radius: 4px;
            `;
        }

        validAnnouncements.forEach((item, index) => {
            const contentDiv = document.createElement('div');
            contentDiv.className = 'announcement-item'; // 添加类名以便于应用样式
            contentDiv.style.flex = '0 0 auto';
            contentDiv.style.width = isMobile ? 'calc((100% - 30px) / 4)' : 'calc((100% - 70px) / 8)';
            contentDiv.style.textAlign = 'center';
            contentDiv.style.whiteSpace = config.wrap ? 'normal' : 'nowrap';
            contentDiv.style.overflow = config.wrap ? 'auto' : 'hidden';
            contentDiv.style.textOverflow = config.wrap ? 'clip' : 'ellipsis';
            contentDiv.style.fontSize = '12px';
            contentDiv.style.cursor = 'pointer';
            contentDiv.style.padding = '8px';
            contentDiv.style.transition = 'all 0.3s ease';
            
            // 处理文字渐变和背景样式
            // 更健壮的条件判断，支持多种数据类型
            const useTextGradient = item.useTextGradient === true || item.useTextGradient === 'true' || item.useTextGradient === 1 || item.useTextGradient === '1';
            const hasTextGradientColor = item.textGradientColor && item.textGradientColor.trim() !== '';

            console.log('文字渐变调试:', {
                useTextGradient: item.useTextGradient,
                useTextGradientParsed: useTextGradient,
                textGradientColor: item.textGradientColor,
                hasTextGradientColor: hasTextGradientColor,
                content: item.content
            });

            if (useTextGradient && hasTextGradientColor) {
                // 使用文字渐变 - 最终解决方案：使用span包装
                // 清除可能存在的覆盖层
                const existingGradientText = contentDiv.querySelector('.gradient-text-overlay');
                if (existingGradientText) {
                    existingGradientText.remove();
                }

                // 创建一个span元素来包装文字，实现渐变效果
                const textSpan = document.createElement('span');
                textSpan.textContent = item.content;
                textSpan.style.cssText = `
                    background: ${item.textGradientColor};
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                    color: transparent;
                    display: inline-block;
                    width: 100%;
                `;

                // 清空原内容并添加span
                contentDiv.innerHTML = '';
                contentDiv.appendChild(textSpan);

                // 设置容器背景（如果有背景渐变）
                if (item.useGradient && item.gradientColor) {
                    // 确保背景渐变能够正确显示
                    contentDiv.style.background = item.gradientColor;
                    contentDiv.style.backgroundColor = '';
                    // 添加重要性声明确保不被覆盖
                    contentDiv.style.setProperty('background', item.gradientColor, 'important');
                } else {
                    contentDiv.style.background = '';
                    contentDiv.style.backgroundColor = item.backgroundColor || config.background;
                    contentDiv.style.removeProperty('background');
                }

                // 添加特殊类来标识文字渐变元素
                contentDiv.classList.add('text-gradient-element');

                // 确保不设置color样式，避免覆盖文字渐变效果
                contentDiv.style.color = '';
            } else {
                // 使用普通文字颜色
                contentDiv.style.color = item.textColor || config.text_color;

                // 清除文字渐变相关样式
                contentDiv.style.background = '';
                contentDiv.style.webkitBackgroundClip = '';
                contentDiv.style.webkitTextFillColor = '';
                contentDiv.style.backgroundClip = '';
                contentDiv.style.position = '';
                contentDiv.style.zIndex = '';
                contentDiv.classList.remove('has-background-gradient');
                contentDiv.classList.remove('text-gradient-element');
                contentDiv.style.removeProperty('--bg-gradient');

                // 设置背景样式
                if (item.useGradient && item.gradientColor) {
                    // 确保背景渐变能够正确显示
                    contentDiv.style.background = item.gradientColor;
                    contentDiv.style.backgroundColor = '';
                    // 添加重要性声明确保不被覆盖
                    contentDiv.style.setProperty('background', item.gradientColor, 'important');
                } else {
                    contentDiv.style.background = '';
                    contentDiv.style.backgroundColor = item.backgroundColor || config.background;
                    contentDiv.style.removeProperty('background');
                }

                // 移除可能存在的渐变文字覆盖层
                const existingGradientText = contentDiv.querySelector('.gradient-text-overlay');
                if (existingGradientText) {
                    existingGradientText.remove();
                }
            }

            // 添加白光闪烁效果 - 但对文字渐变元素使用特殊处理
            if (useTextGradient && hasTextGradientColor) {
                // 对文字渐变元素使用特殊的闪烁效果，避免遮挡
                contentDiv.classList.add('text-gradient-glow');
                // 确保不添加white-glow-text类，避免样式冲突
                contentDiv.classList.remove('white-glow-text');
            } else if (item.textColor) {
                // 普通文字使用标准闪烁效果
                contentDiv.classList.add('white-glow-text');
            }

            // 如果有链接，添加悬停效果
            if (item.link && item.link.trim() !== '') {
                contentDiv.dataset.hasLink = 'true';
                contentDiv.addEventListener('mouseenter', () => {
                    contentDiv.style.opacity = '0.8';
                    contentDiv.style.transform = 'translateY(-2px)';
                });
                contentDiv.addEventListener('mouseleave', () => {
                    contentDiv.style.opacity = '1';
                    contentDiv.style.transform = 'translateY(0)';
                });
            }

            // 设置内容 - 只在非文字渐变时设置，避免覆盖span元素
            if (!(useTextGradient && hasTextGradientColor)) {
                contentDiv.textContent = item.content;
            }

            // 为所有链接添加新窗口打开和阻止冒泡
            const links = contentDiv.getElementsByTagName('a');
            for (let link of links) {
                link.setAttribute('target', '_blank');
                link.addEventListener('click', (e) => {
                    e.stopPropagation();
                });
            }

            // 检查广告位状态
            const now = new Date().getTime();
            const endTime = item.end_time ? new Date(item.end_time).getTime() : 0;
            const isExpired = endTime && now > endTime;
            const isEmpty = !item.content || (item.isEmpty === true);

            // 添加点击事件
            contentDiv.addEventListener('click', () => {
                // 优先检查是否有跳转链接（无论广告状态如何，有链接就直接跳转）
                if (item.link && item.link.trim() !== '') {
                    window.open(item.link, '_blank');
                    return;
                }

                // 如果没有跳转链接，再判断广告状态
                if (isExpired || isEmpty || !item.user_id) {
                    // 判断是否有设置租用跳转链接
                    if (config.rent_link && config.rent_link.trim() !== '') {
                        // 有租用链接则直接跳转
                        window.open(config.rent_link, '_blank');
                    } else {
                        // 直接显示租用对话框
                        showRentDialog(index + 1, config);
                    }
                } else if (config.allow_merchant_edit === 1) {
                    // 检查内容是否为默认内容或者空内容
                    const isDefaultContent =
                        !item.content ||
                        item.content.trim() === '' ||
                        item.content === config.empty_ad_text?.replace('{position}', (index + 1).toString()) ||
                        (typeof item.isEmpty === 'boolean' && item.isEmpty);

                    // 只有在允许商户编辑且是默认内容时才显示编辑对话框
                    if (isDefaultContent) {
                        try {
                            // 调用user-ads.js中的函数
                            if (typeof window.announcementShowsEditPage === 'function') {
                                window.announcementShowsEditPage(index + 1);
                            } else {
                                // console.error('编辑功能未加载');
                                showTip('编辑功能未加载，请刷新页面重试', 'error');
                            }
                        } catch (error) {
                            // console.error('显示编辑对话框失败:', error);
                            showTip('编辑功能加载失败，请刷新页面重试', 'error');
                        }
                    } else {
                        // 如果有内容但没有链接，点击时提示设置链接
                        showTip('点击"我的广告位管理"可设置广告链接', 'info');
                    }
                }
            });

            // 如果广告位已过期，添加过期提示
            if (isExpired) {
                const expiredTip = document.createElement('div');
                expiredTip.style.color = '#f56c6c';
                expiredTip.style.fontSize = '12px';
                expiredTip.style.marginTop = '5px';
                expiredTip.textContent = '广告位已过期';
                contentDiv.appendChild(expiredTip);
            }

            contentWrapper.appendChild(contentDiv);
        });

        routerContainer.appendChild(contentWrapper);
        announcement.appendChild(routerContainer);
        addStyleSheet(config);

        return announcement;
    }

    function applyStyles(announcement, contentWrapper, config) {
        // 直接使用后台设置的高度值
        const height = parseInt(config.height) || 120;
        
        Object.assign(announcement.style, {
            width: '100%',
            boxSizing: 'border-box',
            display: 'block',
            marginBottom: '8px'
        });

        Object.assign(contentWrapper.style, {
            padding: `${config.padding}px`,
            minHeight: `${height}px`,
            background: config.background,
            color: config.text_color,
            borderRadius: '4px',
            wordBreak: 'break-word'
        });
    }

    function addStyleSheet(config) {
        const styleSheet = document.createElement('style');
        styleSheet.textContent = `
            .announcement-container {
                width: 100%;
                overflow: hidden;
                position: relative;
                margin-bottom: 15px;
                box-sizing: border-box;
            }
            
            .announcement-content {
                display: flex;
                align-items: center;
                overflow-x: auto;
                scrollbar-width: none;
                -ms-overflow-style: none;
                box-sizing: border-box;
            }
            
            .announcement-content::-webkit-scrollbar {
                display: none;
            }
            
            .announcement-item {
                padding: 8px 15px;
                white-space: nowrap;
                flex: 0 0 auto;
                cursor: pointer;
                box-sizing: border-box;
                margin-right: 10px;
                border-radius: 4px;
                position: relative;
            }
            
            .announcement-item:last-child {
                margin-right: 0;
            }
            
            /* 添加白光闪烁动画 */
            @keyframes whiteGlow {
                0% {
                    text-shadow: 0 0 5px rgba(255, 255, 255, 0.1);
                }
                25% {
                    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5), 0 0 20px rgba(255, 255, 255, 0.3);
                }
                50% {
                    text-shadow: 0 0 15px rgba(255, 255, 255, 0.8), 0 0 30px rgba(255, 255, 255, 0.5), 0 0 40px rgba(255, 255, 255, 0.3);
                }
                75% {
                    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5), 0 0 20px rgba(255, 255, 255, 0.3);
                }
                100% {
                    text-shadow: 0 0 5px rgba(255, 255, 255, 0.1);
                }
            }
            
            /* 白光闪烁类 */
            .white-glow-text {
                animation: whiteGlow 2s infinite;
            }

            /* 文字渐变专用闪烁效果 - 不使用text-shadow避免遮挡 */
            @keyframes textGradientGlow {
                0% {
                    filter: brightness(1) saturate(1);
                }
                25% {
                    filter: brightness(1.2) saturate(1.3);
                }
                50% {
                    filter: brightness(1.4) saturate(1.5);
                }
                75% {
                    filter: brightness(1.2) saturate(1.3);
                }
                100% {
                    filter: brightness(1) saturate(1);
                }
            }

            .text-gradient-glow {
                animation: textGradientGlow 2s infinite;
            }

            /* 确保文字渐变元素的正确显示 */
            .text-gradient-element {
                -webkit-background-clip: text !important;
                -webkit-text-fill-color: transparent !important;
                background-clip: text !important;
                color: transparent !important;
            }
            
            .announcement-carousel {
                position: relative;
                overflow: hidden;
            }
            
            .carousel-wrapper {
                display: flex;
                width: 100%;
                transition: transform 0.5s ease;
            }
            
            .carousel-slide {
                flex: 0 0 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                position: relative;
            }
            
            .carousel-dots {
                display: flex;
                justify-content: center;
                margin-top: 5px;
            }
            
            .carousel-dot {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                background-color: #ccc;
                margin: 0 4px;
                cursor: pointer;
                transition: background-color 0.3s;
            }
            
            .carousel-dot.active {
                background-color: #666;
            }
            
            .carousel-controls {
                position: absolute;
                top: 50%;
                left: 0;
                right: 0;
                transform: translateY(-50%);
                display: flex;
                justify-content: space-between;
                padding: 0 10px;
                pointer-events: none;
            }
            
            .carousel-arrow {
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: rgba(255, 255, 255, 0.7);
                border-radius: 50%;
                cursor: pointer;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
                pointer-events: auto;
            }
            
            .rent-dialog {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 9999;
            }
            
            .dialog-content {
                background-color: white;
                padding: 20px;
                border-radius: 8px;
                width: 90%;
                max-width: 500px;
                max-height: 90vh;
                overflow-y: auto;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            }
            
            .dialog-content h3 {
                margin-top: 0;
                color: #333;
            }
            
            .dialog-content .form-group {
                margin-bottom: 15px;
            }
            
            .dialog-content label {
                display: block;
                margin-bottom: 5px;
                font-weight: bold;
            }
            
            .dialog-content select,
            .dialog-content button {
                width: 100%;
                padding: 8px 12px;
                border-radius: 4px;
                border: 1px solid #ddd;
            }
            
            .dialog-content button {
                background-color: #1890ff;
                color: white;
                border: none;
                cursor: pointer;
                margin-top: 10px;
            }
            
            .dialog-content button:hover {
                background-color: #40a9ff;
            }
            
            .dialog-content .cancel-button {
                background-color: #f5f5f5;
                color: #333;
                margin-top: 5px;
            }
            
            .dialog-content .cancel-button:hover {
                background-color: #e8e8e8;
            }
            
            .ad-preview {
                margin: 15px 0;
                padding: 10px;
                border: 1px dashed #ddd;
                border-radius: 4px;
                text-align: center;
            }
            
            .preview-text {
                margin-top: 5px;
                color: #666;
                font-size: 12px;
            }
            
            .price-display {
                text-align: right;
                font-weight: bold;
                margin: 15px 0;
            }
            
            .action-buttons {
                position: absolute;
                top: 5px;
                right: 5px;
                display: flex;
                gap: 5px;
            }
            
            .action-btn {
                font-size: 12px;
                padding: 2px 5px;
                background: rgba(0, 0, 0, 0.6);
                color: white;
                border: none;
                border-radius: 3px;
                cursor: pointer;
            }
            
            .action-btn:hover {
                background: rgba(0, 0, 0, 0.8);
            }
            
            .expiry-btn {
                background: rgba(24, 144, 255, 0.6);
            }
            
            .expiry-btn:hover {
                background: rgba(24, 144, 255, 0.8);
            }
            
            .announcement-content a {
                color: inherit;
                text-decoration: none;
            }
            
            .announcement-content a:hover {
                opacity: 0.8;
            }
            
            .ad-user-tooltip {
                position: absolute;
                top: -30px;
                left: 50%;
                transform: translateX(-50%);
                background: rgba(0, 0, 0, 0.7);
                color: white;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                white-space: nowrap;
                z-index: 10;
                pointer-events: none;
            }
            
            .ad-user-tooltip:after {
                content: '';
                position: absolute;
                top: 100%;
                left: 50%;
                margin-left: -5px;
                border-width: 5px;
                border-style: solid;
                border-color: rgba(0, 0, 0, 0.7) transparent transparent transparent;
            }
        `;
        document.head.appendChild(styleSheet);
    }

    function insertAnnouncement(announcement) {
        const isMobile = window.innerWidth <= 768;
        if (isMobile) {
            const routerHeader = document.querySelector('.router-container-header');
            if (routerHeader?.textContent.includes('商品管理') || routerHeader?.textContent.includes('合作管理')) {
                routerHeader.parentNode.parentNode.insertBefore(announcement, routerHeader.parentNode);
            }
        } else {
            const targetElement = document.querySelector('.card-title');
            if (targetElement?.textContent.includes('对接货源商')) {
                targetElement.parentNode.insertBefore(announcement, targetElement.nextElementSibling);
            }
        }
    }

    function observeDOMChanges(announcement) {
        const observer = new MutationObserver(() => {
            if (!document.contains(announcement)) {
                insertAnnouncement(announcement);
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    function showRentDialog(position, config, isRenewal = false, userId = null) {
        if (typeof window.announcementShowsRentDialog === 'function') {
            // 使用user-ads.js中的函数
            window.announcementShowsRentDialog(position, isRenewal);
        } else {
            // 作为备用，使用原始的租用对话框
            // 检查是否为图片模式
            const isImageMode = config.display_mode === 1;
            
            const dialog = document.createElement('div');
            dialog.className = 'rent-dialog';
            dialog.innerHTML = `
                <div class="dialog-content">
                    <h3>${isRenewal ? '续费广告位' : '开通广告位'}</h3>
                    <div class="ad-preview">
                        ${isImageMode ? `
                            <div class="image-preview">
                                ${config.empty_ad_image ? 
                                    `<img src="${config.empty_ad_image}" alt="广告位预览" style="max-width: 100%; height: ${config.height}px; object-fit: contain;">` : 
                                    `<div style="padding: 20px; text-align: center; color: #909399; background: #f5f7fa; border-radius: 4px; border: 1px dashed #dcdfe6;">广告位${position}</div>`
                                }
                                <div class="preview-text">广告位 ${position} 预览效果</div>
                            </div>
                        ` : `
                            <div class="text-preview" style="padding: 10px; background: ${config.background}; color: ${config.text_color};">
                                ${config.empty_ad_text.replace('{position}', position)}
                            </div>
                        `}
                    </div>
                    <div class="rent-options">
                        <div class="option-item" data-duration="30" data-price="${config.month_price}">
                            <h4>月租</h4>
                            <div class="price">￥${config.month_price}</div>
                            <div class="duration">30天</div>
                        </div>
                        <div class="option-item" data-duration="90" data-price="${config.quarter_price}">
                            <h4>季租</h4>
                            <div class="price">￥${config.quarter_price}</div>
                            <div class="duration">90天</div>
                        </div>
                        <div class="option-item" data-duration="365" data-price="${config.year_price}">
                            <h4>年租</h4>
                            <div class="price">￥${config.year_price}</div>
                            <div class="duration">365天</div>
                        </div>
                    </div>
                    <div class="dialog-buttons">
                        <button class="cancel">取消</button>
                        <button class="confirm" disabled>确认${isRenewal ? '续费' : '开通'}</button>
                    </div>
                </div>
            `;

            // 添加新的样式
            const style = document.createElement('style');
            style.textContent = `
                .rent-dialog {
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(0, 0, 0, 0.5);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 9999;
                }
                .dialog-content {
                    background: #fff;
                    padding: 20px;
                    border-radius: 8px;
                    width: 90%;
                    max-width: 500px;
                }
                .rent-options {
                    display: flex;
                    gap: 15px;
                    margin: 20px 0;
                }
                .option-item {
                    flex: 1;
                    padding: 15px;
                    border: 2px solid #eee;
                    border-radius: 8px;
                    text-align: center;
                    cursor: pointer;
                    transition: all 0.3s;
                }
                .option-item.selected {
                    border-color: #409EFF;
                    background: #ecf5ff;
                }
                .option-item h4 {
                    margin: 0 0 10px 0;
                    color: #333;
                }
                .option-item .price {
                    font-size: 20px;
                    color: #409EFF;
                    margin-bottom: 5px;
                }
                .option-item .duration {
                    color: #666;
                    font-size: 14px;
                }
                .dialog-buttons {
                    display: flex;
                    justify-content: flex-end;
                    gap: 10px;
                }
                .dialog-buttons button {
                    padding: 8px 20px;
                    border-radius: 4px;
                    cursor: pointer;
                    border: none;
                }
                .dialog-buttons .cancel {
                    background: #f5f5f5;
                    color: #666;
                }
                .dialog-buttons .confirm {
                    background: #409EFF;
                    color: #fff;
                }
                .dialog-buttons .confirm:disabled {
                    background: #a0cfff;
                    cursor: not-allowed;
                }
                .ad-preview {
                    margin: 15px 0;
                    border: 1px solid #eee;
                    border-radius: 4px;
                    overflow: hidden;
                }
                
                .image-preview {
                    text-align: center;
                    padding: 10px;
                    background: #f5f7fa;
                }
                
                .preview-text {
                    margin-top: 8px;
                    color: #909399;
                    font-size: 12px;
                }
                
                .text-preview {
                    padding: 15px;
                    text-align: center;
                    border-radius: 4px;
                    font-size: 14px;
                }
            `;

            document.head.appendChild(style);

            // 选择处理
            let selectedOption = null;
            const options = dialog.querySelectorAll('.option-item');
            const confirmBtn = dialog.querySelector('.confirm');

            options.forEach(option => {
                option.addEventListener('click', () => {
                    options.forEach(opt => opt.classList.remove('selected'));
                    option.classList.add('selected');
                    selectedOption = {
                        duration: parseInt(option.dataset.duration),
                        price: parseFloat(option.dataset.price)
                    };
                    confirmBtn.disabled = false;
                });
            });

            // 确认按钮
            confirmBtn.addEventListener('click', async () => {
                if (!selectedOption) return;

                try {
                    const response = await fetch('/plugin/Announcementshows/User/openAdSpace', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify({
                            position: position,
                            duration: selectedOption.duration,
                            price: selectedOption.price,
                            is_renewal: isRenewal,
                            original_user_id: userId
                        })
                    });

                    const result = await response.json();
                    if (result.code === 200) {
                        handleSuccess('开通成功');
                        // 修改：不再跳转，而是显示弹窗编辑页面
                        setTimeout(() => {
                            // 调用user-ads.js中的函数
                            if (typeof window.announcementShowsEditPage === 'function') {
                                window.announcementShowsEditPage(position);
                            } else {
                                // console.error('编辑功能未加载');
                                showTip('编辑功能未加载，请刷新页面重试', 'error');
                            }
                        }, 1000);
                    } else {
                        showTip(result.msg || (isRenewal ? '续费失败' : '开通失败'), 'error');
                    }
                } catch (error) {
                    handleError(error, isRenewal ? '续费' : '开通');
                }

                dialog.remove();
            });

            // 取消按钮
            dialog.querySelector('.cancel').addEventListener('click', () => {
                dialog.remove();
            });

            // 添加到页面
            document.body.appendChild(dialog);
        }
    }

    // 添加提示组件
    function showTip(message, type = 'info') {
        const tip = document.createElement('div');
        tip.className = 'announcement-tip';
        tip.innerHTML = message;
        
        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .announcement-tip {
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                padding: 10px 20px;
                border-radius: 4px;
                background: #fff;
                box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
                z-index: 9999;
                animation: tipSlideIn 0.3s ease;
            }
            .announcement-tip.success {
                background: #f0f9eb;
                color: #67c23a;
                border: 1px solid #e1f3d8;
            }
            .announcement-tip.error {
                background: #fef0f0;
                color: #f56c6c;
                border: 1px solid #fde2e2;
            }
            .announcement-tip.info {
                background: #f4f4f5;
                color: #909399;
                border: 1px solid #e9e9eb;
            }
            @keyframes tipSlideIn {
                from {
                    transform: translate(-50%, -20px);
                    opacity: 0;
                }
                to {
                    transform: translate(-50%, 0);
                    opacity: 1;
                }
            }
        `;
        document.head.appendChild(style);
        
        // 添加类型样式
        tip.classList.add(type);
        
        // 添加到页面
        document.body.appendChild(tip);
        
        // 3秒后自动移除
        setTimeout(() => {
            tip.style.animation = 'tipSlideOut 0.3s ease forwards';
            setTimeout(() => tip.remove(), 300);
        }, 3000);
    }

    // 全局函数，用于从外部调用广告编辑功能
    window.showAnnouncementEditDialog = function(position) {
        // 调用user-ads.js中的函数
        if (typeof window.announcementShowsEditPage === 'function') {
            window.announcementShowsEditPage(position);
        } else {
            // 没有加载user-ads.js时，尝试加载并等待
            const possiblePaths = [
                // 相对路径 - 基于当前脚本位置
                './user-ads.js',
                '../static/user-ads.js',
                'user-ads.js',
                // 绝对路径 - 从网站根目录开始
                '/Announcementshows/static/user-ads.js',
                '/static/Announcementshows/user-ads.js',
                '/addons/Announcementshows/static/user-ads.js',
                '/plugins/Announcementshows/static/user-ads.js',
                '/plugin/Announcementshows/static/user-ads.js'
            ];
            
            // 尝试加载每一个可能的路径
            function tryLoadScript(paths, index) {
                if (index >= paths.length) {
                    showTip('无法加载编辑功能，请刷新页面重试', 'error');
                    return;
                }
                
                const script = document.createElement('script');
                script.src = paths[index];
                script.async = true;
                
                script.onerror = function() {
                    console.warn('路径加载失败: ' + paths[index] + '，尝试下一个路径');
                    this.remove();
                    tryLoadScript(paths, index + 1);
                };
                
                script.onload = function() {
                    if (typeof window.announcementShowsEditPage === 'function') {
                        window.announcementShowsEditPage(position);
                    } else {
                        showTip('无法加载编辑功能，请刷新页面重试', 'error');
                    }
                };
                
                document.head.appendChild(script);
            }
            
            // 开始尝试加载
            tryLoadScript(possiblePaths, 0);
        }
    };

    // 修改原有的错误处理
    function handleError(error, action = '') {
        showTip(`操作失败，请稍后重试`, 'error');
        // console.error(`${action}失败:`, error);
    }

    // 修改原有的成功处理
    function handleSuccess(message) {
        showTip(message, 'success');
        setTimeout(() => location.reload(), 1500);
    }

    // 修改价格计算函数
    function calculatePrice(duration, isRenew, config) {
        let price = 0;
        switch (duration) {
            case 30:
                price = isRenew ? config.renew_month_price : config.month_price;
                break;
            case 90:
                price = isRenew ? config.renew_quarter_price : config.quarter_price;
                break;
            case 180: // 添加半年选项
                price = isRenew ? config.renew_halfyear_price : config.halfyear_price;
                break;
            case 365:
                price = isRenew ? config.renew_year_price : config.year_price;
                break;
        }
        return parseFloat(price).toFixed(2);
    }

    // 修改续费对话框函数
    function showRenewDialog(position, config) {
        const dialog = document.createElement('div');
        dialog.className = 'edit-dialog';
        dialog.innerHTML = `
            <div class="dialog-content">
                <h3>续费广告位</h3>
                <div class="renew-form">
                    <div class="form-item">
                        <label>续费时长：</label>
                        <select class="duration-select">
                            <option value="30">30天</option>
                            <option value="90">90天</option>
                            <option value="180">180天</option>
                            <option value="365">365天</option>
                        </select>
                    </div>
                    <div class="form-item">
                        <label>续费金额：</label>
                        <span class="price-display">计算中...</span>
                    </div>
                </div>
                <div class="dialog-buttons">
                    <button class="cancel">取消</button>
                    <button class="confirm">确认续费</button>
                </div>
            </div>
        `;

        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .renew-form {
                padding: 20px 0;
            }
            .form-item {
                margin-bottom: 15px;
                display: flex;
                align-items: center;
            }
            .form-item label {
                width: 80px;
                color: #606266;
            }
            .duration-select {
                padding: 8px;
                border: 1px solid #dcdfe6;
                border-radius: 4px;
                width: 120px;
            }
            .price-display {
                color: #f56c6c;
                font-weight: bold;
            }
        `;
        document.head.appendChild(style);

        // 计算价格函数
        function updatePrice() {
            const duration = parseInt(durationSelect.value);
            const price = calculatePrice(duration, true, config);
            priceDisplay.textContent = `￥${price}`;
        }

        const durationSelect = dialog.querySelector('.duration-select');
        const priceDisplay = dialog.querySelector('.price-display');
        
        durationSelect.addEventListener('change', updatePrice);
        updatePrice(); // 初始化价格显示

        // 确认续费
        dialog.querySelector('.confirm').addEventListener('click', async () => {
            const duration = parseInt(durationSelect.value);
            const price = parseFloat(calculatePrice(duration, true, config));

            try {
                const response = await fetch('/plugin/Announcementshows/User/openAdSpace', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        position: position,
                        duration: duration,
                        price: price,
                        is_renew: true
                    })
                });

                const result = await response.json();
                if (result.code === 200) {
                    handleSuccess('续费成功');
                } else {
                    showTip(result.msg || '续费失败', 'error');
                }
            } catch (error) {
                handleError(error, '续费');
            }
            dialog.remove();
        });

        // 取消按钮
        dialog.querySelector('.cancel').addEventListener('click', () => {
            dialog.remove();
        });

        document.body.appendChild(dialog);
    }

    // 修改对话框创建函数中的时长选项
    function createDurationOptions() {
        return [
            { value: 30, text: '1个月', price: 0 },
            { value: 90, text: '3个月', price: 0 },
            { value: 180, text: '6个月', price: 0 }, // 添加半年选项
            { value: 365, text: '12个月', price: 0 }
        ];
    }

    // 获取当前用户ID
    function getCurrentUserId() {
        try {
            // 从全局变量中获取
            if (window.app && window.app.user && window.app.user.id) {
                return window.app.user.id;
            }
            
            // 从页面元素中查找
            const userIdElement = document.querySelector('meta[name="user-id"]');
            if (userIdElement && userIdElement.content) {
                return parseInt(userIdElement.content);
            }
            
            // 尝试从登录状态中获取
            if (window.user && window.user.id) {
                return window.user.id;
            }
            
            return null;
        } catch (e) {
            // console.error('获取用户ID失败:', e);
            return null;
        }
    }
    
    // 显示编辑对话框
    function showEditDialog(position) {
        try {
            // 优先使用全局函数
            if (typeof window.announcementShowsEditPage === 'function') {
                window.announcementShowsEditPage(position);
                return;
            }
            
            // 如果全局函数不存在，直接跳转到编辑页面
            const editUrl = `/plugin/Announcementshows/User/edit?position=${position}`;
            window.open(editUrl, '_blank');
        } catch (e) {
            // console.error('打开编辑页面失败:', e);
            showTip('打开编辑页面失败，请刷新重试', 'error');
        }
    }

    // 添加手机端彩色卡片列表模板
    function createMobileCardList(announcements, config) {
        // 检查是否启用渐变色卡片模式
        if (config.gradient_cards === 1) {
            return createGradientCards(announcements, config);
        }
        
        const containerDiv = document.createElement('div');
        containerDiv.className = 'announcement-container mobile-card-list';
        containerDiv.setAttribute('data-template', 'mobile-cards');
        containerDiv.style.marginBottom = '8px';
        
        // 创建内容容器
        const contentWrapper = document.createElement('div');
        contentWrapper.className = 'announcement-content mobile-content';
        contentWrapper.style.display = 'flex';
        contentWrapper.style.flexDirection = 'column';
        contentWrapper.style.gap = '8px';
        contentWrapper.style.padding = '8px';
        contentWrapper.style.background = '#f5f7fa';
        
        // 不再使用彩色卡片，统一使用白色卡片风格
        announcements.forEach((item, index) => {
            // 创建系统公告容器
            const systemNotice = document.createElement('div');
            systemNotice.className = 'system-notice announcement-item';
            
            // 设置背景颜色，支持渐变色
            if (item.useGradient && item.gradientColor) {
                systemNotice.style.background = item.gradientColor;
                // 确保文字在渐变背景上清晰可见
                systemNotice.style.color = item.textColor || '#ffffff';
            } else {
                systemNotice.style.backgroundColor = item.backgroundColor || '#fff';
                systemNotice.style.color = item.textColor || '#333';
                systemNotice.style.border = '1px solid #ebeef5'; // 添加外边框
            }
            
            systemNotice.style.borderRadius = '8px';
            systemNotice.style.boxShadow = 'rgba(0, 0, 0, 0.05) 0px 2px 6px';
            systemNotice.style.marginBottom = '8px';
            
            // 如果有链接，添加鼠标指针样式
            if (item.link) {
                systemNotice.style.cursor = 'pointer';
            }
            
            // 创建内容容器
            const noticeContent = document.createElement('div');
            noticeContent.className = 'system-notice-content';
            noticeContent.style.display = 'flex';
            noticeContent.style.alignItems = 'center';
            noticeContent.style.padding = '12px 16px';
            
            // 创建标题
            const title = document.createElement('div');
            title.className = 'title';
            title.style.fontWeight = 'bold';
            title.style.fontSize = '14px';
            // 应用文字颜色，确保在渐变背景上可见
            title.style.color = item.useGradient ? 
                (item.textColor || '#ffffff') : 
                (item.textColor || '#333');
            title.style.marginRight = '15px';
            title.style.whiteSpace = 'nowrap';
            
            // 如果设置了文字颜色，添加闪烁效果
            if (item.textColor) {
                title.classList.add('white-glow-text');
            }
            
            // 创建内容
            const content = document.createElement('div');
            content.className = 'content line1';
            content.style.flex = '1';
            content.style.fontSize = '14px';
            // 应用文字颜色，确保在渐变背景上可见
            content.style.color = item.useGradient ? 
                (item.textColor || '#ffffff') : 
                (item.textColor || '#666');
            content.style.overflow = 'hidden';
            content.style.textOverflow = 'ellipsis';
            content.style.whiteSpace = 'nowrap';
            
            // 如果设置了文字颜色，添加闪烁效果
            if (item.textColor) {
                content.classList.add('white-glow-text');
            }
            
            // 创建右侧箭头
            const right = document.createElement('div');
            right.className = 'right';
            right.style.marginLeft = '10px';
            right.style.color = item.useGradient ? '#ffffff' : '#c2c2c2';
            // 使用安全的SVG生成函数
            right.innerHTML = createSafeSvg('rightArrow');
            
            // 设置SVG图标大小
            const svg = right.querySelector('svg');
            svg.style.width = '14px';
            svg.style.height = '14px';
            
            // 设置内容
            let mainText = item.content || '';
            let subText = '';
            
            // 如果内容包含分隔符，分割为主要文本和次要文本
            if (mainText.includes('|')) {
                const parts = mainText.split('|');
                mainText = parts[0].trim();
                subText = parts[1].trim();
            } else if (mainText.includes('：')) {
                const parts = mainText.split('：');
                mainText = parts[0].trim();
                subText = parts[1].trim();
            } else if (mainText.includes(':')) {
                const parts = mainText.split(':');
                mainText = parts[0].trim();
                subText = parts[1].trim();
            }
            
            title.textContent = mainText || '平台公告';
            content.textContent = subText || '未出租';
            
            // 组装公告项
            noticeContent.appendChild(title);
            noticeContent.appendChild(content);
            noticeContent.appendChild(right);
            systemNotice.appendChild(noticeContent);
            
            // 检查广告位状态
            const now = new Date().getTime();
            const endTime = item.end_time ? new Date(item.end_time).getTime() : 0;
            const isExpired = endTime && now > endTime;
            const isEmpty = !item.content || (item.isEmpty === true);
            
            // 添加链接或点击购买功能
            if (item.link) {
                systemNotice.onclick = function(e) {
                    e.preventDefault();
                    window.open(item.link, '_blank');
                };
            } else if (isEmpty || isExpired || !item.user_id) {
                // 如果是空广告位、过期广告位或未租用广告位，添加点击弹出购买窗口
                systemNotice.style.cursor = 'pointer';
                systemNotice.onclick = function(e) {
                    e.preventDefault();
                    showRentDialog(index + 1, config);
                };
            }
            
            contentWrapper.appendChild(systemNotice);
        });
        
        containerDiv.appendChild(contentWrapper);
        
        // 添加样式表，使用与页面相同的样式
        const styleSheet = document.createElement('style');
        styleSheet.textContent = `
            .announcement-container {
                width: 100%;
                box-sizing: border-box;
            }
            
            .system-notice {
                border-radius: 8px;
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
                margin-bottom: 8px;
                transition: all 0.3s;
            }
            
            .system-notice:hover {
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }
            
            .system-notice-content {
                display: flex;
                align-items: center;
                padding: 12px 16px;
            }
            
            .system-notice .title {
                font-weight: bold;
                font-size: 14px;
                margin-right: 15px;
                white-space: nowrap;
            }
            
            .system-notice .content {
                flex: 1;
                font-size: 14px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            
            .system-notice .right {
                margin-left: 10px;
                display: flex;
                align-items: center;
            }
            
            .system-notice .right svg {
                width: 14px;
                height: 14px;
            }
            
            .line1 {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            
            .announcement-content.mobile-content {
                padding: 8px;
                background: #f5f7fa;
            }
            
            /* 适配页面样式 */
            .mobile-card-list {
                margin-bottom: 8px;
            }
        `;
        document.head.appendChild(styleSheet);
        
        return containerDiv;
    }
    
    // 新增渐变色卡片模板
    function createGradientCards(announcements, config) {
        // 渐变色配置
        const gradientColors = [
            'linear-gradient(135deg, rgb(255, 123, 37), rgb(255, 94, 98))', // 橙红色
            'linear-gradient(135deg, rgb(255, 215, 0), rgb(255, 165, 0))',  // 金黄色
            'linear-gradient(135deg, rgb(0, 206, 209), rgb(32, 178, 170))', // 青绿色
            'linear-gradient(135deg, rgb(50, 205, 50), rgb(34, 139, 34))',  // 绿色
            'linear-gradient(135deg, rgb(65, 105, 225), rgb(30, 144, 255))' // 蓝色
        ];

        // 创建主容器
        const container = document.createElement('div');
        container.className = 'mobile-color-cards-container';
        container.style.padding = '10px';
        container.style.margin = '0 0 8px 0';
        
        // 创建卡片列表
        announcements.forEach((item, index) => {
            const card = document.createElement('div');
            card.className = 'color-card';
            card.style.borderRadius = '10px';
            card.style.overflow = 'hidden';
            card.style.marginBottom = '10px';
            card.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
            
            // 应用渐变背景或自定义背景
            card.style.background = item.useGradient && item.gradientColor ? 
                item.gradientColor : gradientColors[index % gradientColors.length];
            
            // 创建卡片内容
            const cardContent = document.createElement('div');
            cardContent.className = 'card-content';
            cardContent.style.padding = '15px';
            cardContent.style.display = 'flex';
            cardContent.style.justifyContent = 'space-between';
            cardContent.style.alignItems = 'center';
            
            // 解析内容
            let title = item.content;
            let desc = '';
            
            if (title.includes('|')) {
                const parts = title.split('|');
                title = parts[0].trim();
                desc = parts.length > 1 ? parts[1].trim() : '';
            }
            
            // 添加左侧内容
            const leftContent = document.createElement('div');
            leftContent.className = 'card-left-content';
            
            const titleEl = document.createElement('div');
            titleEl.className = 'card-title white-glow-text';
            titleEl.style.color = '#fff';
            titleEl.style.fontWeight = 'bold';
            titleEl.style.fontSize = '16px';
            titleEl.textContent = title;
            leftContent.appendChild(titleEl);
            
            if (desc) {
                const descEl = document.createElement('div');
                descEl.className = 'card-desc white-glow-text';
                descEl.style.color = 'rgba(255,255,255,0.9)';
                descEl.style.fontSize = '14px';
                descEl.style.marginTop = '5px';
                descEl.textContent = desc;
                leftContent.appendChild(descEl);
            }
            
            // 添加右侧箭头
            const rightContent = document.createElement('div');
            rightContent.className = 'card-right-content';
            // 使用安全的SVG生成函数
            rightContent.innerHTML = createSafeSvg('smallRightArrow');
            rightContent.style.color = '#fff';
            
            // 组装卡片内容
            cardContent.appendChild(leftContent);
            cardContent.appendChild(rightContent);
            card.appendChild(cardContent);
            
            // 检查广告位状态
            const now = new Date().getTime();
            const endTime = item.end_time ? new Date(item.end_time).getTime() : 0;
            const isExpired = endTime && now > endTime;
            const isEmpty = !item.content || (item.isEmpty === true);
            
            // 添加点击事件
            if (item.link) {
                card.style.cursor = 'pointer';
                card.addEventListener('click', () => {
                    window.open(item.link, '_blank');
                });
            } else if (isEmpty || isExpired || !item.user_id) {
                // 如果是空广告位、过期广告位或未租用广告位，添加点击弹出购买窗口
                card.style.cursor = 'pointer';
                card.addEventListener('click', () => {
                    showRentDialog(index + 1, config);
                });
            }
            
            container.appendChild(card);
        });
        
        return container;
    }

    // 模板1 - Banner轮播样式
    function createMobileTemplate1(announcements, config) {
        switch (config.mobile_template1_style) {
            case 'card':
                return createMobileBannerCard(announcements, config);
            case 'simple':
                return createMobileBannerSimple(announcements, config);
            case 'banner':
            default:
                return createMobileBanner(announcements, config);
        }
    }

    // 模板1样式1 - 图片Banner轮播
    function createMobileBanner(announcements, config) {
        const height = parseInt(config.mobile_height) || 120;
        
        // 创建轮播容器
        const carouselContainer = document.createElement('div');
        carouselContainer.className = 'mobile-announcement-carousel';
        carouselContainer.style.width = '100%';
        carouselContainer.style.height = height + 'px';
        carouselContainer.style.position = 'relative';
        carouselContainer.style.overflow = 'hidden';
        carouselContainer.style.borderRadius = '8px';
        carouselContainer.style.margin = '0 8px 8px 8px';
        
        // 创建轮播项目容器
        const carouselItemsContainer = document.createElement('div');
        carouselItemsContainer.className = 'carousel-items';
        carouselItemsContainer.style.display = 'flex';
        carouselItemsContainer.style.height = '100%';
        carouselItemsContainer.style.transition = 'transform 0.5s ease';
        
        // 添加轮播项
        announcements.forEach((item, index) => {
            const carouselItem = document.createElement('div');
            carouselItem.className = 'carousel-item';
            carouselItem.style.flex = '0 0 100%';
            carouselItem.style.height = '100%';
            carouselItem.style.position = 'relative';
            
            // 使用内容作为背景图片或使用背景色
            if (item.image_url || (config.display_mode === 1 && item.content)) {
                const img = document.createElement('img');
                img.src = item.image_url || item.content;
                img.style.width = '100%';
                img.style.height = '100%';
                img.style.objectFit = 'cover';
                carouselItem.appendChild(img);
            } else {
                carouselItem.style.backgroundColor = item.useGradient && item.gradientColor ? 
                    item.gradientColor : item.backgroundColor;
                
                // 添加文本内容
                const textContent = document.createElement('div');
                textContent.style.padding = '15px';
                textContent.style.color = item.textColor;
                textContent.style.fontSize = '14px';
                textContent.style.fontWeight = 'bold';
                textContent.className = 'white-glow-text';
                textContent.textContent = item.content;
                carouselItem.appendChild(textContent);
            }
            
            // 检查广告位状态
            const now = new Date().getTime();
            const endTime = item.end_time ? new Date(item.end_time).getTime() : 0;
            const isExpired = endTime && now > endTime;
            const isEmpty = !item.content || (item.isEmpty === true);
            
            // 如果有链接，添加点击事件
            if (item.link) {
                carouselItem.style.cursor = 'pointer';
                carouselItem.addEventListener('click', () => {
                    window.open(item.link, '_blank');
                });
            } else if (isEmpty || isExpired || !item.user_id) {
                // 如果是空广告位、过期广告位或未租用广告位，添加点击弹出购买窗口
                carouselItem.style.cursor = 'pointer';
                carouselItem.addEventListener('click', () => {
                    showRentDialog(index + 1, config);
                });
            }
            
            carouselItemsContainer.appendChild(carouselItem);
        });
        
        carouselContainer.appendChild(carouselItemsContainer);
        
        // 添加指示器
        if (announcements.length > 1) {
            const indicators = document.createElement('div');
            indicators.className = 'carousel-indicators';
            indicators.style.position = 'absolute';
            indicators.style.bottom = '10px';
            indicators.style.left = '0';
            indicators.style.right = '0';
            indicators.style.display = 'flex';
            indicators.style.justifyContent = 'center';
            indicators.style.gap = '8px';
            
            announcements.forEach((_, i) => {
                const indicator = document.createElement('span');
                indicator.style.width = '6px';
                indicator.style.height = '6px';
                indicator.style.borderRadius = '50%';
                indicator.style.backgroundColor = i === 0 ? '#fff' : 'rgba(255,255,255,0.5)';
                indicators.appendChild(indicator);
            });
            
            carouselContainer.appendChild(indicators);
            
            // 添加轮播功能
            let currentSlide = 0;
            
            function nextSlide() {
                currentSlide = (currentSlide + 1) % announcements.length;
                carouselItemsContainer.style.transform = `translateX(-${currentSlide * 100}%)`;
                updateIndicators();
            }
            
            function updateIndicators() {
                const dots = indicators.querySelectorAll('span');
                dots.forEach((dot, i) => {
                    dot.style.backgroundColor = i === currentSlide ? '#fff' : 'rgba(255,255,255,0.5)';
                });
            }
            
            // 自动轮播
            setInterval(nextSlide, 3000);
        }
        
        return carouselContainer;
    }
    
    // 模板1样式2 - 卡片式轮播
    function createMobileBannerCard(announcements, config) {
        // 实现卡片式轮播
        return createMobileBanner(announcements, config);
    }
    
    // 模板1样式3 - 简约轮播
    function createMobileBannerSimple(announcements, config) {
        // 实现简约轮播
        return createMobileBanner(announcements, config);
    }
    
    // 模板2 - 走马灯样式
    function createMobileTemplate2(announcements, config) {
        switch (config.mobile_template2_style) {
            case 'fade':
                return createMobileMarqueeFade(announcements, config);
            case 'slide':
                return createMobileMarqueeSlide(announcements, config);
            case 'marquee':
            default:
                return createMobileMarquee(announcements, config);
        }
    }
    
    // 模板2样式1 - 走马灯滚动
    function createMobileMarquee(announcements, config) {
        const container = document.createElement('div');
        container.className = 'mobile-marquee-container';
        container.style.width = '100%';
        container.style.overflow = 'hidden';
        container.style.backgroundColor = config.mobile_background;
        container.style.padding = '10px 0';
        container.style.margin = '0 0 8px 0';
        
        const marquee = document.createElement('div');
        marquee.className = 'marquee-content';
        marquee.style.display = 'flex';
        marquee.style.animation = 'marquee 20s linear infinite';
        marquee.style.whiteSpace = 'nowrap';
        
        announcements.forEach((item, index) => {
            const itemElement = document.createElement('div');
            itemElement.className = 'marquee-item';
            itemElement.style.padding = '0 20px';
            
            // 处理链接
            if (item.link) {
                const link = document.createElement('a');
                link.href = item.link;
                link.target = '_blank';
                link.style.textDecoration = 'none';
                link.style.color = item.textColor || config.mobile_text_color;
                link.textContent = item.content;
                itemElement.appendChild(link);
            } else {
                itemElement.style.color = item.textColor || config.mobile_text_color;
                itemElement.textContent = item.content;
                
                // 检查广告位状态
                const now = new Date().getTime();
                const endTime = item.end_time ? new Date(item.end_time).getTime() : 0;
                const isExpired = endTime && now > endTime;
                const isEmpty = !item.content || (item.isEmpty === true);
                
                // 如果是空广告位、过期广告位或未租用广告位，添加点击弹出购买窗口
                if (isEmpty || isExpired || !item.user_id) {
                    itemElement.style.cursor = 'pointer';
                    itemElement.addEventListener('click', () => {
                        showRentDialog(index + 1, config);
                    });
                }
            }
            
            marquee.appendChild(itemElement);
        });
        
        container.appendChild(marquee);
        
        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes marquee {
                0% { transform: translateX(100%); }
                100% { transform: translateX(-100%); }
            }
            
            .mobile-marquee-container {
                border-radius: 4px;
            }
            
            .marquee-item {
                font-size: 14px;
            }
        `;
        document.head.appendChild(style);
        
        return container;
    }
    
    // 模板2样式2 - 淡入淡出效果
    function createMobileMarqueeFade(announcements, config) {
        const container = document.createElement('div');
        container.className = 'mobile-fade-container';
        container.style.width = '100%';
        container.style.overflow = 'hidden';
        container.style.backgroundColor = config.mobile_background;
        container.style.padding = '10px';
        container.style.margin = '0 0 8px 0';
        container.style.position = 'relative';
        container.style.minHeight = '40px';
        
        announcements.forEach((item, index) => {
            const itemElement = document.createElement('div');
            itemElement.className = 'fade-item';
            itemElement.style.position = 'absolute';
            itemElement.style.top = '10px';
            itemElement.style.left = '10px';
            itemElement.style.right = '10px';
            itemElement.style.opacity = index === 0 ? '1' : '0';
            itemElement.style.transition = 'opacity 1s ease';
            itemElement.style.color = item.textColor || config.mobile_text_color;
            itemElement.textContent = item.content;
            
            // 处理链接
            if (item.link) {
                itemElement.style.cursor = 'pointer';
                itemElement.addEventListener('click', () => {
                    window.open(item.link, '_blank');
                });
            } else {
                // 检查广告位状态
                const now = new Date().getTime();
                const endTime = item.end_time ? new Date(item.end_time).getTime() : 0;
                const isExpired = endTime && now > endTime;
                const isEmpty = !item.content || (item.isEmpty === true);
                
                // 如果是空广告位、过期广告位或未租用广告位，添加点击弹出购买窗口
                if (isEmpty || isExpired || !item.user_id) {
                    itemElement.style.cursor = 'pointer';
                    itemElement.addEventListener('click', () => {
                        showRentDialog(index + 1, config);
                    });
                }
            }
            
            container.appendChild(itemElement);
        });
        
        // 淡入淡出动画
        if (announcements.length > 1) {
            let currentItem = 0;
            
            setInterval(() => {
                const items = container.querySelectorAll('.fade-item');
                items[currentItem].style.opacity = '0';
                currentItem = (currentItem + 1) % items.length;
                items[currentItem].style.opacity = '1';
            }, 3000);
        }
        
        return container;
    }
    
    // 模板2样式3 - 滑动效果
    function createMobileMarqueeSlide(announcements, config) {
        // 实现滑动效果
        return createMobileMarqueeFade(announcements, config);
    }
    
    // 模板3 - 彩色卡片样式
    function createMobileTemplate3(announcements, config) {
        switch (config.mobile_template3_style) {
            case 'dark_mode':
                return createMobileColorCardsDark(announcements, config);
            case 'light_mode':
                return createMobileColorCardsLight(announcements, config);
            case 'color_cards':
            default:
                return createMobileColorCards(announcements, config);
        }
    }
    
    // 模板3样式1 - 彩色卡片
    function createMobileColorCards(announcements, config) {
        // 渐变色配置
        const gradientColors = [
            'linear-gradient(135deg, rgb(255, 123, 37), rgb(255, 94, 98))', // 橙红色
            'linear-gradient(135deg, rgb(255, 215, 0), rgb(255, 165, 0))',  // 金黄色
            'linear-gradient(135deg, rgb(0, 206, 209), rgb(32, 178, 170))', // 青绿色
            'linear-gradient(135deg, rgb(50, 205, 50), rgb(34, 139, 34))',  // 绿色
            'linear-gradient(135deg, rgb(65, 105, 225), rgb(30, 144, 255))' // 蓝色
        ];

        // 创建主容器
        const container = document.createElement('div');
        container.className = 'mobile-color-cards-container';
        container.style.padding = '10px';
        container.style.margin = '0 0 8px 0';
        
        // 创建卡片列表
        announcements.forEach((item, index) => {
            const card = document.createElement('div');
            card.className = 'color-card';
            card.style.borderRadius = '10px';
            card.style.overflow = 'hidden';
            card.style.marginBottom = '10px';
            card.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
            
            // 应用渐变背景或自定义背景
            card.style.background = item.useGradient && item.gradientColor ? 
                item.gradientColor : gradientColors[index % gradientColors.length];
            
            // 创建卡片内容
            const cardContent = document.createElement('div');
            cardContent.className = 'card-content';
            cardContent.style.padding = '15px';
            cardContent.style.display = 'flex';
            cardContent.style.justifyContent = 'space-between';
            cardContent.style.alignItems = 'center';
            
            // 解析内容
            let title = item.content;
            let desc = '';
            
            if (title.includes('|')) {
                const parts = title.split('|');
                title = parts[0].trim();
                desc = parts.length > 1 ? parts[1].trim() : '';
            }
            
            // 添加左侧内容
            const leftContent = document.createElement('div');
            leftContent.className = 'card-left-content';
            
            const titleEl = document.createElement('div');
            titleEl.className = 'card-title white-glow-text';
            titleEl.style.color = '#fff';
            titleEl.style.fontWeight = 'bold';
            titleEl.style.fontSize = '16px';
            titleEl.textContent = title;
            leftContent.appendChild(titleEl);
            
            if (desc) {
                const descEl = document.createElement('div');
                descEl.className = 'card-desc white-glow-text';
                descEl.style.color = 'rgba(255,255,255,0.9)';
                descEl.style.fontSize = '14px';
                descEl.style.marginTop = '5px';
                descEl.textContent = desc;
                leftContent.appendChild(descEl);
            }
            
            // 添加右侧箭头
            const rightContent = document.createElement('div');
            rightContent.className = 'card-right-content';
            // 使用安全的SVG生成函数
            rightContent.innerHTML = createSafeSvg('smallRightArrow');
            rightContent.style.color = '#fff';
            
            // 组装卡片内容
            cardContent.appendChild(leftContent);
            cardContent.appendChild(rightContent);
            card.appendChild(cardContent);
            
            // 检查广告位状态
            const now = new Date().getTime();
            const endTime = item.end_time ? new Date(item.end_time).getTime() : 0;
            const isExpired = endTime && now > endTime;
            const isEmpty = !item.content || (item.isEmpty === true);
            
            // 添加点击事件
            if (item.link) {
                card.style.cursor = 'pointer';
                card.addEventListener('click', () => {
                    window.open(item.link, '_blank');
                });
            } else if (isEmpty || isExpired || !item.user_id) {
                // 如果是空广告位、过期广告位或未租用广告位，添加点击弹出购买窗口
                card.style.cursor = 'pointer';
                card.addEventListener('click', () => {
                    showRentDialog(index + 1, config);
                });
            }
            
            container.appendChild(card);
        });
        
        return container;
    }
    
    // 模板3样式2 - 暗色卡片
    function createMobileColorCardsDark(announcements, config) {
        const container = createMobileColorCards(announcements, config);
        
        // 给每个卡片应用暗色主题样式
        const cards = container.querySelectorAll('.color-card');
        cards.forEach((card) => {
            card.style.background = '#222';
            
            // 调整文字颜色
            const title = card.querySelector('.card-title');
            if (title) title.style.color = '#fff';
            
            const desc = card.querySelector('.card-desc');
            if (desc) desc.style.color = 'rgba(255,255,255,0.7)';
        });
        
        return container;
    }
    
    // 模板3样式3 - 亮色卡片
    function createMobileColorCardsLight(announcements, config) {
        const container = createMobileColorCards(announcements, config);
        
        // 给每个卡片应用亮色主题样式
        const cards = container.querySelectorAll('.color-card');
        cards.forEach((card) => {
            card.style.background = '#fff';
            card.style.border = '1px solid #eee';
            
            // 调整文字颜色
            const title = card.querySelector('.card-title');
            if (title) title.style.color = '#333';
            
            const desc = card.querySelector('.card-desc');
            if (desc) desc.style.color = '#666';
            
            // 调整右侧箭头颜色
            const rightContent = card.querySelector('.card-right-content');
            if (rightContent) rightContent.style.color = '#999';
        });
        
        return container;
    }

    initAnnouncement();
})();   