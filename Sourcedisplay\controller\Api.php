<?php

namespace plugin\Sourcedisplay\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;

class Api extends BasePlugin {
    protected $scene = ['admin'];
    protected $noNeedLogin = ['fetchData'];

    public function index() {
        return View::fetch();
    }

    // 获取配置
    public function fetchData() {
        header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
        header('Cache-Control: post-check=0, pre-check=0', false);
        header('Pragma: no-cache');

        try {
            // 修改数据结构，确保 rank_icons 正确传递
            $params = [
                'enabled' => intval(plugconf("Sourcedisplay.enabled") ?? 1),
                'rank_icons' => [
                    'rank1' => plugconf("Sourcedisplay.rank1_icon") ?? '',
                    'rank2' => plugconf("Sourcedisplay.rank2_icon") ?? '',
                    'rank3' => plugconf("Sourcedisplay.rank3_icon") ?? ''
                ],
                'title_wrap' => true
            ];

            // 获取前三货源数据
            $topSources = Db::name('goods_pool')
                ->alias('g')
                ->join('user u', 'g.user_id = u.id')
                ->field('g.id, g.title, g.user_id, g.refresh_time, g.create_time, g.tags, g.status, u.nickname, u.agent_key')
                ->where('g.status', 1)
                ->order('g.refresh_time', 'desc')
                ->limit(3)
                ->select()
                ->toArray();

            $params['sources'] = $topSources;

            return json(['code' => 200, 'msg' => 'success', 'data' => $params]);
        } catch (\Exception $e) {
            \think\facade\Log::record("FetchData error: " . $e->getMessage(), 'error');
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    // 保存配置
    public function save() {
        if (!$this->request->isPost()) {
            return json(['code' => 405, 'msg' => '请求方法不允许']);
        }

        try {
            $enabled = $this->request->post('enabled/d', 1);
            $rank1_icon = $this->request->post('rank1_icon', '');
            $rank2_icon = $this->request->post('rank2_icon', '');
            $rank3_icon = $this->request->post('rank3_icon', '');

            // 更新配置，包括空值
            plugconf("Sourcedisplay.rank1_icon", $rank1_icon);
            plugconf("Sourcedisplay.rank2_icon", $rank2_icon);
            plugconf("Sourcedisplay.rank3_icon", $rank3_icon);
            plugconf("Sourcedisplay.enabled", $enabled);

            return json(['code' => 200, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            \think\facade\Log::record("Save error: " . $e->getMessage(), 'error');
            return json(['code' => 500, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }
} 