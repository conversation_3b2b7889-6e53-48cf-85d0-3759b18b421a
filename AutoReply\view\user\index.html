<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>自动回复配置</title>
    <style>
        .page-container {
            padding: 20px;
        }
        .rule-card {
            margin-bottom: 20px;
        }
        .keyword-tag {
            margin-right: 5px;
            margin-bottom: 5px;
        }
        .test-panel {
            margin-top: 20px;
            padding: 15px;
            background: #f5f7fa;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div id="app" class="page-container">
        <el-card>
            <template #header>
                <div class="card-header">
                    <span>自动回复规则配置</span>
                </div>
            </template>

            <div v-for="(rule, index) in rules" :key="index" class="rule-card">
                <el-card>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <span>规则 #{{index + 1}}</span>
                        <el-switch v-model="rule.status" :active-value="1" :inactive-value="0"/>
                    </div>

                    <el-form label-width="120px">
                        <el-form-item label="触发关键词：">
                            <el-tag
                                v-for="(keyword, kidx) in rule.keywords"
                                :key="kidx"
                                closable
                                class="keyword-tag"
                                @close="removeKeyword(index, kidx)">
                                {{keyword}}
                            </el-tag>
                            
                            <el-input
                                v-if="inputVisible[index]"
                                ref="inputRefs"
                                v-model="inputValue[index]"
                                size="small"
                                style="width: 100px"
                                @keyup.enter="addKeyword(index)"
                                @blur="addKeyword(index)">
                            </el-input>
                            
                            <el-button v-else size="small" @click="showInput(index)">
                                + 添加关键词
                            </el-button>
                        </el-form-item>

                        <el-form-item label="回复内容：">
                            <el-input 
                                type="textarea" 
                                v-model="rule.reply" 
                                :rows="3" 
                                placeholder="请输入自动回复内容">
                            </el-input>
                        </el-form-item>
                    </el-form>

                    <div style="text-align: right; margin-top: 10px;">
                        <el-button type="danger" @click="removeRule(index)" size="small">删除规则</el-button>
                    </div>
                </el-card>
            </div>

            <div style="margin-top: 20px">
                <el-button type="primary" @click="addRule">添加规则</el-button>
            </div>

            <el-card style="margin-top: 20px;">
                <el-form :model="config" label-width="120px">
                    <el-form-item>
                        <div style="display: flex; align-items: center; gap: 30px;">
                            <div style="display: flex; align-items: center;">
                                <span style="margin-right: 10px;">功能状态：</span>
                                <el-switch v-model="config.status" :active-value="1" :inactive-value="0"/>
                            </div>

                            <div style="display: flex; align-items: center;">
                                <span style="margin-right: 10px;">自动发送：</span>
                                <el-switch v-model="config.auto_send" :active-value="1" :inactive-value="0"/>
                            </div>

                            <div style="display: flex; align-items: center;">
                                <span style="margin-right: 10px;">邮件通知：</span>
                                <template v-if="config.allow_merchant_email">
                                    <el-switch 
                                        v-model="config.email_notify" 
                                        :active-value="1" 
                                        :inactive-value="0"/>
                                </template>
                                <template v-else>
                                    <span class="el-text-small" style="color: #909399;">
                                        站长已关闭
                                    </span>
                                </template>
                            </div>
                        </div>
                    </el-form-item>

                    <el-form-item label="检查间隔：">
                        <el-input-number 
                            v-model="config.check_interval" 
                            :min="5"
                            :max="3600"
                            :step="5"
                            style="width: 180px">
                        </el-input-number>
                        <span class="el-text-small" style="margin-left: 10px;color: #909399;">
                            自动检查新消息的时间间隔（秒）
                        </span>
                    </el-form-item>

                    <el-form-item 
                        label="通知邮箱：" 
                        v-if="config.allow_merchant_email && config.email_notify === 1">
                        <el-input 
                            v-model="config.notify_email" 
                            placeholder="请输入接收通知的邮箱地址">
                        </el-input>
                    </el-form-item>

                    <el-form-item>
                        <el-button 
                            type="primary" 
                            @click="startAutoCheck" 
                            :loading="checking"
                            :disabled="!config.status || !config.auto_send">
                            开始自动检查
                        </el-button>
                        <span v-if="nextCheckTime" class="el-text-small" style="margin-left: 10px;color: #909399;">
                            下次检查时间：{{ nextCheckTime }}
                        </span>
                    </el-form-item>
                </el-form>
            </el-card>

            <div style="margin-top: 20px; text-align: center;">
                <el-button type="success" :loading="loading" @click="saveRules">保存配置</el-button>
            </div>
        </el-card>
    </div>

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>

    <script>
        const { createApp, ref, onMounted, nextTick, onUnmounted, watch } = Vue
        const { ElMessage, ElMessageBox } = ElementPlus

        const app = createApp({
            setup() {
                const rules = ref([])
                const loading = ref(false)
                const testing = ref(false)
                const inputVisible = ref([])
                const inputValue = ref([])
                const inputRefs = ref([])
                const testForm = ref({
                    content: '',
                    complaint_id: 1,
                    auto_send: false
                })
                const config = ref({
                    status: 0,
                    email_notify: 0,
                    notify_email: '',
                    check_interval: 60,
                    auto_send: false,
                    allow_merchant_email: 1,
                    keyword_rules: []
                })
                const checking = ref(false)
                const checkInterval = ref(null)
                const nextCheckTime = ref('')

                // 获取规则
                const getRules = async () => {
                    try {
                        const res = await axios.post('getRules')
                        if (res.data.code === 200) {
                            console.log('获取到的规则数据:', res.data.data)
                            config.value = res.data.data
                            // 直接使用后端返回的规则
                            rules.value = res.data.data.keyword_rules || []
                            
                            // 初始化输入状态数组
                            inputVisible.value = new Array(rules.value.length).fill(false)
                            inputValue.value = new Array(rules.value.length).fill('')
                            inputRefs.value = new Array(rules.value.length).fill(null)
                            
                            console.log('处理后的规则数据:', rules.value)
                        } else {
                            throw new Error(res.data.msg)
                        }
                    } catch (error) {
                        ElMessage.error(error.message || '获取规则失败')
                    }
                }

                // 保存规则
                const saveRules = async () => {
                    if (loading.value) return
                    loading.value = true
                    try {
                        const data = {
                            rules: rules.value,
                            config: {
                                ...config.value,
                            }
                        }
                        const res = await axios.post('saveRule', data)
                        if (res.data.code === 200) {
                            ElMessage.success('保存成功')
                            // 使用返回的数据更新本地状态
                            if (res.data.data) {
                                config.value = {
                                    status: res.data.data.status,
                                    email_notify: res.data.data.email_notify,
                                    notify_email: res.data.data.notify_email,
                                    check_interval: res.data.data.check_interval,
                                    auto_send: res.data.data.auto_send,
                                    allow_merchant_email: res.data.data.allow_merchant_email,
                                    keyword_rules: res.data.data.keyword_rules
                                }
                                rules.value = res.data.data.keyword_rules
                                
                                // 重新初始化输入状态数组
                                inputVisible.value = new Array(rules.value.length).fill(false)
                                inputValue.value = new Array(rules.value.length).fill('')
                                inputRefs.value = new Array(rules.value.length).fill(null)
                            }
                        } else {
                            throw new Error(res.data.msg)
                        }
                    } catch (error) {
                        ElMessage.error(error.message || '保存失败')
                    } finally {
                        loading.value = false
                    }
                }

                // 测试规则
                const testRule = async () => {
                    if (testing.value) return
                    if (!testForm.value.content) {
                        ElMessage.warning('请输入测试内容')
                        return
                    }
                    if (testForm.value.auto_send && !testForm.value.complaint_id) {
                        ElMessage.warning('启用自动发送时需要填写投诉单ID')
                        return
                    }

                    testing.value = true
                    try {
                        const res = await axios.post('testRule', testForm.value)
                        if (res.data.code === 200) {
                            if (res.data.data) {
                                let message = `匹配关键词: ${res.data.data.keyword}\n将回复: ${res.data.data.reply}`
                                if (res.data.data.sent) {
                                    message += '\n已自动发送回复'
                                }
                                ElMessage.success(message)
                            } else {
                                ElMessage.info('未匹配到任何规则')
                            }
                        } else {
                            throw new Error(res.data.msg || '测试失败')
                        }
                    } catch (error) {
                        ElMessage.error(error.message || '测试失败')
                    } finally {
                        testing.value = false
                    }
                }

                // 添加规则
                const addRule = async () => {
                    try {
                        await ElMessageBox.confirm('添加新的规则？', '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'info'
                        })
                        
                        rules.value.push({
                            status: 1,
                            keywords: [],
                            reply: ''
                        })
                        inputVisible.value.push(false)
                        inputValue.value.push('')
                        
                        ElMessage.success('添加规则成功')
                    } catch (error) {
                        if (error !== 'cancel') {
                            ElMessage.error(error.message || '添加规则失败')
                        }
                    }
                }

                // 删除规则
                const removeRule = async (index) => {
                    try {
                        await ElMessageBox.confirm('确定要删除这条规则吗？', '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        })
                        
                        const res = await axios.post('deleteRule', { index: index })
                        if (res.data.code === 200) {
                            // 先更新本地数据
                            rules.value.splice(index, 1)
                            inputVisible.value.splice(index, 1)
                            inputValue.value.splice(index, 1)
                            // 重新获取规则列表以确保同步
                            await getRules()
                            ElMessage.success('删除成功')
                        } else {
                            throw new Error(res.data.msg)
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            ElMessage.error(error.message || '删除失败')
                        }
                    }
                }

                // 显示输入框
                const showInput = (index) => {
                    inputVisible.value[index] = true
                    nextTick(() => {
                        inputRefs.value[index]?.focus()
                    })
                }

                // 添加关键词
                const addKeyword = (index) => {
                    const value = inputValue.value[index]
                    if (value) {
                        if (!rules.value[index].keywords) {
                            rules.value[index].keywords = []
                        }
                        // 检查关键词是否重复
                        if (rules.value[index].keywords.includes(value)) {
                            ElMessage.warning('该关键词已存在')
                            inputValue.value[index] = ''
                            return
                        }
                        rules.value[index].keywords.push(value)
                    }
                    inputVisible.value[index] = false
                    inputValue.value[index] = ''
                }

                // 删除关键词
                const removeKeyword = (ruleIndex, keywordIndex) => {
                    rules.value[ruleIndex].keywords.splice(keywordIndex, 1)
                }

                // 开始自动检查
                const startAutoCheck = async () => {
                    if (checking.value) return
                    checking.value = true
                    
                    try {
                        const res = await axios.post('autoCheck')
                        if (res.data.code === 200) {
                            ElMessage.success(res.data.msg)
                            // 如果功能启用，设置定时器
                            if (config.value.status && config.value.auto_send) {
                                setupAutoCheck()
                            }
                        } else {
                            throw new Error(res.data.msg)
                        }
                    } catch (error) {
                        ElMessage.error(error.message || '自动检查失败')
                    } finally {
                        checking.value = false
                    }
                }

                // 设置自动检查定时器
                const setupAutoCheck = () => {
                    // 清除现有定时器
                    if (checkInterval.value) {
                        clearInterval(checkInterval.value)
                    }
                    
                    // 设置新定时器
                    const interval = config.value.check_interval * 1000 // 转换为毫秒
                    checkInterval.value = setInterval(() => {
                        startAutoCheck()
                    }, interval)

                    // 更新下次检查时间
                    updateNextCheckTime()
                }

                // 更新下次检查时间显示
                const updateNextCheckTime = () => {
                    const next = new Date(Date.now() + config.value.check_interval * 1000)
                    nextCheckTime.value = next.toLocaleTimeString()
                }

                // 在组件卸载时清理定时器
                onUnmounted(() => {
                    if (checkInterval.value) {
                        clearInterval(checkInterval.value)
                    }
                })

                // 监听配置变化
                watch([() => config.value.status, () => config.value.auto_send], ([newStatus, newAutoSend]) => {
                    if (newStatus && newAutoSend) {
                        setupAutoCheck()
                    } else if (checkInterval.value) {
                        clearInterval(checkInterval.value)
                        checkInterval.value = null
                        nextCheckTime.value = ''
                    }
                })

                // 监听商家邮件开关状态
                watch(() => config.value.allow_merchant_email, (newValue) => {
                    if (!newValue) {
                        // 如果站长关闭了商家邮件功能，自动关闭邮件通知并清空邮箱
                        config.value.email_notify = 0;
                        config.value.notify_email = '';
                    }
                });

                // 初始化
                onMounted(() => {
                    getRules()
                })

                return {
                    rules,
                    loading,
                    testing,
                    inputVisible,
                    inputValue,
                    inputRefs,
                    testForm,
                    config,
                    saveRules,
                    testRule,
                    addRule,
                    removeRule,
                    showInput,
                    addKeyword,
                    removeKeyword,
                    checking,
                    nextCheckTime,
                    startAutoCheck
                }
            }
        })

        app.use(ElementPlus)
        app.mount('#app')
    </script>
</body>
</html> 