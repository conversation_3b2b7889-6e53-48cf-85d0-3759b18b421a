<?php

namespace plugin\Autoban;

use think\facade\Db;

class Hook
{
    public function handle()
    {
        $status = intval(plugconf("Autoban.status") ?? 0);
        
        // 当 status 为 1 时，执行自动封禁检查
        if ($status == 1) {
            $this->checkAndBanHighComplaintUsers();
        }
    }

    private function checkAndBanHighComplaintUsers()
    {
        try {
            $threshold = floatval(plugconf("Autoban.threshold") ?? 50);
            $banContent = plugconf("Autoban.banContent") ?? '因投诉率超过{threshold}%，系统自动封禁';
            $banContent = str_replace('{threshold}', $threshold, $banContent);

            // 获取投诉率超过阈值的用户
            $subQuery = Db::name('user')
                ->alias('u')
                ->leftJoin('complaint c', 'u.id = c.user_id')
                ->leftJoin('order o', 'u.id = o.user_id AND o.status = 1')
                ->field([
                    'u.id',
                    'COUNT(DISTINCT c.id) as complaint_count',
                    'COUNT(DISTINCT o.id) as total_orders'
                ])
                ->group('u.id')
                ->having('complaint_count > 0')
                ->buildSql();

            $highComplaintUsers = Db::table($subQuery . ' a')
                ->field([
                    'id as user_id',
                    'complaint_count',
                    'total_orders',
                    'ROUND((complaint_count / total_orders * 100), 2) as complaint_rate'
                ])
                ->having('complaint_rate >= ' . $threshold)
                ->select()
                ->toArray();

            $currentTime = time();

            foreach ($highComplaintUsers as $user) {
                // 检查是否已被封禁
                $existingBan = Db::name('user_risk')
                    ->where('user_id', $user['user_id'])
                    ->where('risk_type', 2)
                    ->find();

                if (!$existingBan) {
                    // 获取默认封禁时间
                    $expireTime = intval(plugconf("Autoban.default_ban_expire_time") ?? 4102415999);
                    
                    // 添加封禁记录
                    Db::name('user_risk')->insert([
                        'user_id' => $user['user_id'],
                        'risk_type' => 2,
                        'user_read' => 0,
                        'content' => $banContent,
                        'create_time' => $currentTime,
                        'expire_time' => $expireTime
                    ]);

                    // 更新用户状态为封禁
                    Db::name('user')
                        ->where('id', $user['user_id'])
                        ->update([
                            'rules' => json_encode(['CloseUser']),
                            'update_time' => $currentTime
                        ]);
                }
            }
        } catch (\Exception $e) {
            error_log("自动封禁失败: " . $e->getMessage());
        }
    }
} 