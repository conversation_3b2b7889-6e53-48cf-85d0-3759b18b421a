<?php

namespace app\index\Blackglod\controller;

use app\common\controller\BaseIndex;
use think\facade\Db;
use think\facade\Session;
use think\facade\Validate;

class Orderinquiries extends BaseIndex {

    public function index() {
        // 获取导航菜单数据(包含子菜单)
        $navItems = Db::name('nav')
            ->where('status', 1)
            ->where('pid', 0)
            ->field(['id', 'name', 'href', 'target'])
            ->order('sort asc')
            ->select()
            ->each(function($item) {
                $item['children'] = Db::name('nav')
                    ->where('status', 1)
                    ->where('pid', $item['id'])
                    ->field(['name', 'href', 'target'])
                    ->order('sort asc')
                    ->select()
                    ->toArray();
                return $item;
            })
            ->toArray();

        // 获取网站配置信息
        $logo = $this->formatImageUrl(sysconf('website.logo'));
        $siteName = sysconf('website.app_name');
        $favicon = $this->formatImageUrl(sysconf('website.favicon'));
        $icpNumber = sysconf('website.icp_number');
        $gaNumber = sysconf('website.ga_number');
        $icpCert = sysconf('website.icp_cert');

        // 获取页脚配置
        $params = get_template_params();

        // 修改获取查询参数的方式
        $keywords = input('keywords', '');
        $ticket = input('ticket', '');
        $status = input('status', '');
        
        $orderList = [];
        if (!empty($keywords)) {
            // 修改验证码校验逻辑
            if(!$this->checkTicket($ticket)) {
                return view('orderinquiries/index', [
                    'title' => '订单查询 - ' . $siteName,
                    'error' => '验证票据无效',
                    'logo' => $logo,
                    'siteName' => $siteName,
                    'favicon' => $favicon,
                    'navItems' => $navItems,
                    'icpNumber' => $icpNumber,
                    'gaNumber' => $gaNumber,
                    'icpCert' => $icpCert,
                    'keywords' => $keywords,
                    'status' => $status,
                    'orderList' => $orderList,

                    // 服务中心配置
                    'footer_service_show' => $params->footer_service_show ?? 1,
                    'footer_service_1' => $params->footer_service_1 ?? '卡密查询',
                    'footer_service_1_link' => $params->footer_service_1_link ?? '#',
                    'footer_service_2' => $params->footer_service_2 ?? '投诉中心',
                    'footer_service_2_link' => $params->footer_service_2_link ?? '#',
                    'footer_service_3' => $params->footer_service_3 ?? '卡密工具',
                    'footer_service_3_link' => $params->footer_service_3_link ?? '#',
                    'footer_service_4' => $params->footer_service_4 ?? '商户入驻',
                    'footer_service_4_link' => $params->footer_service_4_link ?? '#',
                    
                    // 帮助中心配置
                    'footer_help_show' => $params->footer_help_show ?? 1,
                    'footer_help_1' => $params->footer_help_1 ?? '常见问题',
                    'footer_help_1_link' => $params->footer_help_1_link ?? '#',
                    'footer_help_2' => $params->footer_help_2 ?? '系统公告',
                    'footer_help_2_link' => $params->footer_help_2_link ?? '#',
                    'footer_help_3' => $params->footer_help_3 ?? '结算公告',
                    'footer_help_3_link' => $params->footer_help_3_link ?? '#',
                    'footer_help_4' => $params->footer_help_4 ?? '新闻动态',
                    'footer_help_4_link' => $params->footer_help_4_link ?? '#',
                    
                    // 法律责任配置
                    'footer_legal_show' => $params->footer_legal_show ?? 1,
                    'footer_legal_1' => $params->footer_legal_1 ?? '免责声明',
                    'footer_legal_1_link' => $params->footer_legal_1_link ?? '#',
                    'footer_legal_2' => $params->footer_legal_2 ?? '禁售商品',
                    'footer_legal_2_link' => $params->footer_legal_2_link ?? '#',
                    'footer_legal_3' => $params->footer_legal_3 ?? '服务协议',
                    'footer_legal_3_link' => $params->footer_legal_3_link ?? '#',
                    'footer_legal_4' => $params->footer_legal_4 ?? '隐私政策',
                    'footer_legal_4_link' => $params->footer_legal_4_link ?? '#',
                    
                    // 友情链接配置
                    'footer_links_show' => $params->footer_links_show ?? 1,
                    'footer_links_1' => $params->footer_links_1 ?? '一意支付',
                    'footer_links_1_link' => $params->footer_links_1_link ?? '#',
                    'footer_links_2' => $params->footer_links_2 ?? '支付宝',
                    'footer_links_2_link' => $params->footer_links_2_link ?? '#',
                    'footer_links_3' => $params->footer_links_3 ?? '微信支付',
                    'footer_links_3_link' => $params->footer_links_3_link ?? '#',
                    'footer_links_4' => $params->footer_links_4 ?? 'QQ钱包',
                    'footer_links_4_link' => $params->footer_links_4_link ?? '#'
                ]);
            }

            // 构建查询条件
            $where = ['contact' => $keywords];
            if($status !== '') {
                $where['status'] = intval($status);
            }

            // 修改为分页查询
            $pageSize = 10; // 每页显示10条
            $orderList = Db::name('order')
                ->where($where)
                ->field([
                    'id',
                    'trade_no',
                    'goods_id',
                    'goods_name',
                    'total_amount',
                    'status',
                    'create_time',
                    'quantity',
                    'contact',
                    'transaction_id',
                    'success_time'
                ])
                ->order('create_time desc')
                ->paginate([
                    'list_rows' => $pageSize,
                    'query' => request()->param()
                ]);

            // 处理数据
            $orderList->each(function($item) {
                $item['status_text'] = $this->getStatusText($item['status']);
                $item['amount_text'] = "¥{$item['total_amount']}\n共{$item['quantity']}件";
                $item['create_time'] = date('Y-m-d H:i:s', $item['create_time']);
                
                // 获取商品类型
                $goods = Db::name('goods')
                    ->where('id', $item['goods_id'])
                    ->field(['goods_type'])
                    ->find();
                
                $item['goods_type'] = $goods ? $goods['goods_type'] : 'card';
                
                return $item;
            });
        }

        return view('orderinquiries/index', [
            'title' => '订单查询 - ' . $siteName,
            'logo' => $logo,
            'siteName' => $siteName,
            'favicon' => $favicon,
            'navItems' => $navItems,
            'icpNumber' => $icpNumber,
            'gaNumber' => $gaNumber,
            'icpCert' => $icpCert,
            'keywords' => $keywords,
            'status' => $status,
            'orderList' => $orderList,
            'error' => '',
            'ticket' => $ticket,

            // 服务中心配置
            'footer_service_show' => $params->footer_service_show ?? 1,
            'footer_service_1' => $params->footer_service_1 ?? '卡密查询',
            'footer_service_1_link' => $params->footer_service_1_link ?? '#',
            'footer_service_2' => $params->footer_service_2 ?? '投诉中心',
            'footer_service_2_link' => $params->footer_service_2_link ?? '#',
            'footer_service_3' => $params->footer_service_3 ?? '卡密工具',
            'footer_service_3_link' => $params->footer_service_3_link ?? '#',
            'footer_service_4' => $params->footer_service_4 ?? '商户入驻',
            'footer_service_4_link' => $params->footer_service_4_link ?? '#',
            
            // 帮助中心配置
            'footer_help_show' => $params->footer_help_show ?? 1,
            'footer_help_1' => $params->footer_help_1 ?? '常见问题',
            'footer_help_1_link' => $params->footer_help_1_link ?? '#',
            'footer_help_2' => $params->footer_help_2 ?? '系统公告',
            'footer_help_2_link' => $params->footer_help_2_link ?? '#',
            'footer_help_3' => $params->footer_help_3 ?? '结算公告',
            'footer_help_3_link' => $params->footer_help_3_link ?? '#',
            'footer_help_4' => $params->footer_help_4 ?? '新闻动态',
            'footer_help_4_link' => $params->footer_help_4_link ?? '#',
            
            // 法律责任配置
            'footer_legal_show' => $params->footer_legal_show ?? 1,
            'footer_legal_1' => $params->footer_legal_1 ?? '免责声明',
            'footer_legal_1_link' => $params->footer_legal_1_link ?? '#',
            'footer_legal_2' => $params->footer_legal_2 ?? '禁售商品',
            'footer_legal_2_link' => $params->footer_legal_2_link ?? '#',
            'footer_legal_3' => $params->footer_legal_3 ?? '服务协议',
            'footer_legal_3_link' => $params->footer_legal_3_link ?? '#',
            'footer_legal_4' => $params->footer_legal_4 ?? '隐私政策',
            'footer_legal_4_link' => $params->footer_legal_4_link ?? '#',
            
            // 友情链接配置
            'footer_links_show' => $params->footer_links_show ?? 1,
            'footer_links_1' => $params->footer_links_1 ?? '一意支付',
            'footer_links_1_link' => $params->footer_links_1_link ?? '#',
            'footer_links_2' => $params->footer_links_2 ?? '支付宝',
            'footer_links_2_link' => $params->footer_links_2_link ?? '#',
            'footer_links_3' => $params->footer_links_3 ?? '微信支付',
            'footer_links_3_link' => $params->footer_links_3_link ?? '#',
            'footer_links_4' => $params->footer_links_4 ?? 'QQ钱包',
            'footer_links_4_link' => $params->footer_links_4_link ?? '#'
        ]);
    }

    /**
     * 获取订单状态文本
     */
    private function getStatusText($status) {
        $statusMap = [
            0 => '待付款',
            1 => '已付款',
            2 => '已关闭',
            3 => '已退款'
        ];
        return isset($statusMap[$status]) ? $statusMap[$status] : '未知状态';
    }

    /**
     * 格式化图片URL
     */
    private function formatImageUrl($url) {
        if (empty($url)) {
            return '';
        }
        if (preg_match('/^https?:\/\/(oss\.|cos\.|[-\w]+\.qiniucdn\.|[-\w]+)/i', $url)) {
            return $url;
        }
        if (strpos($url, 'uploads/') === 0) {
            return request()->domain() . '/' . $url;
        }
        return request()->domain() . '/' . ltrim($url, '/');
    }

    /**
     * 验证ticket
     */
    private function checkTicket($ticket) {
        if(empty($ticket)) {
            return false;
        }
        // 这里可以添加更多的ticket验证逻辑
        return true;
    }

    /**
     * 获取卡密信息
     */
    public function getOrderCard()
    {
        try {
            $trade_no = input('trade_no', '');
            if (empty($trade_no)) {
                return json(['code' => 1, 'msg' => '订单号不能为空']);
            }

            // 获取订单信息和对应的商品ID
            $order = Db::name('order')
                ->where('trade_no', $trade_no)
                ->where('status', 1)  // 确保订单已支付
                ->field(['id', 'goods_id'])
                ->find();

            if (!$order) {
                return json(['code' => 1, 'msg' => '订单不存在或未支付']);
            }

            // 获取商品信息
            $goods = Db::name('goods')
                ->where('id', $order['goods_id'])
                ->field(['goods_type', 'goods_key'])
                ->find();

            // 根据商品类型处理
            if ($goods['goods_type'] == 'article' || $goods['goods_type'] == 'resource') {
                return json([
                    'code' => 2,
                    'msg' => 'redirect',
                    'data' => [
                        'url' => '/item/' . $goods['goods_key'] . '/' . $trade_no
                    ]
                ]);
            }

            // 获取卡密信息
            $cardInfo = Db::name('order_card_log')
                ->where('order_id', $order['id'])
                ->value('cards');

            if (!$cardInfo) {
                return json(['code' => 1, 'msg' => '未找到卡密信息']);
            }

            // 获取商品使用说明
            $instructions = Db::name('goods_card')
                ->where('goods_id', $order['goods_id'])
                ->value('instructions');

            // 处理卡密字符串
            $cardInfo = trim($cardInfo, '[]"');

            return json([
                'code' => 0,
                'msg' => 'success',
                'data' => $cardInfo,
                'instructions' => $instructions ?? ''
            ]);

        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '系统错误：' . $e->getMessage()]);
        }
    }

    /**
     * 显示卡密详情页面
     */
    public function showCard($trade_no = '')
    {
        try {
            if (empty($trade_no)) {
                return $this->error('订单号不能为空');
            }

            // 获取订单信息
            $order = Db::name('order')
                ->where('trade_no', $trade_no)
                ->where('status', 1)  // 确保订单已支付
                ->field(['id', 'quantity', 'goods_name', 'total_amount'])
                ->find();

            if (!$order) {
                return $this->error('订单不存在或未支付');
            }

            // 获取卡密信息
            $cardInfo = Db::name('order_card_log')
                ->where('order_id', $order['id'])
                ->field(['cards'])
                ->find();

            if (!$cardInfo) {
                return $this->error('未找到卡密信息');
            }

            // 解析卡密数组
            $cards = json_decode($cardInfo['cards'], true);
            if (empty($cards)) {
                return $this->error('卡密数据格式错误');
            }

            // 获取网站配置信息
            $siteName = sysconf('website.app_name');
            $favicon = $this->formatImageUrl(sysconf('website.favicon'));
            $logo = $this->formatImageUrl(sysconf('website.logo'));

            // 明确指定视图路径
            return $this->fetch('orderinquiries/card', [
                'title' => '卡密详情 - ' . $siteName,
                'favicon' => $favicon,
                'logo' => $logo,
                'siteName' => $siteName,
                'order' => $order,
                'cards' => $cards
            ]);
        } catch (\Exception $e) {
            return $this->error('系统错误：' . $e->getMessage());
        }
    }

    /**
     * 获取订单详情
     */
    public function getOrderDetail()
    {
        try {
            $trade_no = input('trade_no', '');
            if (empty($trade_no)) {
                return json(['code' => 1, 'msg' => '订单号不能为空']);
            }

            // 获取订单详细信息
            $order = Db::name('order')
                ->where('trade_no', $trade_no)
                ->field([
                    'trade_no',
                    'goods_name',
                    'goods_price',
                    'quantity',
                    'total_amount',
                    'transaction_id',
                    'status',
                    'create_time',
                    'success_time',
                    'contact'
                ])
                ->find();

            if (!$order) {
                return json(['code' => 1, 'msg' => '订单不存在']);
            }

            return json([
                'code' => 0,
                'msg' => 'success',
                'data' => $order
            ]);

        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '系统错误：' . $e->getMessage()]);
        }
    }
} 