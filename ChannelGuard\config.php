<?php

return [
    "version" => "1.0.0",
    "min_system_version" => "2.12.0",
    "category_name" => "功能插件",
    "name" => "渠道账户守护",
    "description" => "自动检测连续失败订单并关闭对应渠道账户",
    "logo" => "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjMDA3YWZmIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCI+PHBhdGggZD0iTTEyIDIycy04LTQtOC0xMFY1bDgtM2w4IDN2N2MwIDYtOCAxMC04IDEweiIvPjxwYXRoIGQ9Ik05LjUgMTJsMyAzbDQtNCIvPjwvc3ZnPg==",
    "menu" => [
        [
            'tag' => 'iframe',
            'name' => '操作面板',
            'src' => (string)plugin_url("ChannelGuard/Api/index", [], false, true),
        ]
    ],
    "hook" => [
        'SimpleCommand' => 'plugin\ChannelGuard\Hook::handle',
    ],
]; 