<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>二次元看板娘设置</title>
    <style>
        /* 响应式设计增强 */
        /* 超小屏幕手机 */
        @media screen and (max-width: 320px) {
            body {
                padding: 8px;
                font-size: 14px;
            }
            .el-card {
                border-radius: 8px;
            }
            .card-header span {
                font-size: 16px;
            }
            .el-form-item__label {
                font-size: 13px;
            }
            .number-input-item {
                padding: 6px 8px;
            }
        }

        /* 小屏幕手机 */
        @media screen and (min-width: 321px) and (max-width: 375px) {
            body {
                padding: 10px;
                font-size: 14px;
            }
            .card-header span {
                font-size: 17px;
            }
            .el-form-item__label {
                font-size: 14px;
            }
            .number-input-item {
                padding: 7px 10px;
            }
        }

        /* 中等屏幕手机 */
        @media screen and (min-width: 376px) and (max-width: 414px) {
            body {
                padding: 12px;
                font-size: 15px;
            }
            .card-header span {
                font-size: 18px;
            }
            .number-input-item {
                padding: 8px 12px;
            }
        }

        /* 动态字体大小计算 */
        :root {
            --base-font-size: 16px;
            --min-font-size: 14px;
            --max-font-size: 18px;
            font-size: clamp(var(--min-font-size), 1vw + 1vh, var(--max-font-size));
        }

        /* 自适应间距 */
        .el-form-item {
            margin-bottom: clamp(20px, 4vw, 28px);
        }
        .number-input-group {
            gap: clamp(10px, 2vw, 20px);
            display: flex;
            flex-wrap: wrap;
        }

        /* 优化触摸区域 */
        .el-button, .el-switch, .action-icon {
            min-height: 44px; /* 符合可访问性标准的最小触摸区域 */
        }

        /* 优化输入框 */
        .el-input-number {
            width: 100%;
            max-width: 200px;
        }

        /* 优化开关组件 */
        .el-switch {
            transform-origin: left center;
            transform: scale(1.2);
        }

        .number-input-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            background: #f5f7fa;
            border-radius: 4px;
            margin-bottom: 10px;
            width: 45%;
            min-width: 180px;
        }

        .number-input-item span {
            margin-right: 10px;
            white-space: nowrap;
        }

        .el-form-item-msg {
            font-size: 12px;
            color: #909399;
            margin-top: 5px;
            line-height: 1.4;
        }

        .position-selector {
            margin: 10px 0;
        }

        /* 自定义模型输入框 */
        .custom-model-input {
            margin-top: 10px;
            padding: 10px;
            background: #f0f9eb;
            border-radius: 4px;
            border-left: 3px solid #67c23a;
        }

        @media (hover: hover) {
            /* 仅在支持悬停的设备上启用悬停效果 */
            .number-input-item:hover {
                background: #ecf5ff;
            }
        }

        /* 暗黑模式支持 */
        @media (prefers-color-scheme: dark) {
            body {
                background: #1a1a1a;
            }
            .el-card {
                background: #2b2b2b;
                border-color: #3a3a3a;
            }
            .number-input-item {
                background: #333;
            }
        }
    </style>
</head>

<body>
    <div id="app">
        <el-card shadow="never">
            <template #header>
                <div class="card-header">
                    <span>二次元看板娘设置</span>
                    <div class="el-form-item-msg">此处设置的配置将作为网站默认配置</div>
                </div>
            </template>

            <el-form :model="form" label-width="90px">
                <!-- 看板娘开关 -->
                <el-form-item label="看板娘开关">
                    <el-switch 
                        v-model="form.status" 
                        :active-value="1" 
                        :inactive-value="0"
                        class="custom-switch" 
                    />
                </el-form-item>

                <!-- 模型选择 -->
                <el-form-item label="模型选择">
                    <el-radio-group v-model="form.model_type" @change="handleModelChange">
                        <el-radio label="auto">自动加载（推荐）</el-radio>
                        <el-radio label="koharu">Koharu (默认)</el-radio>
                        <el-radio label="shizuku">Shizuku</el-radio>
                        <el-radio label="custom">自定义模型</el-radio>
                    </el-radio-group>

                    <!-- 模型说明 -->
                    <div style="margin-top: 8px; font-size: 12px; color: #909399;">
                        <div v-if="form.model_type === 'auto'">
                            <i class="el-icon-info"></i>
                            使用 live2d-widgets 自动加载，支持多种随机模型，如果加载失败会自动降级到自定义看板娘
                        </div>
                        <div v-else-if="form.model_type === 'koharu'">
                            <i class="el-icon-info"></i>
                            粉色主题的可爱看板娘
                        </div>
                        <div v-else-if="form.model_type === 'shizuku'">
                            <i class="el-icon-info"></i>
                            蓝绿主题的清新看板娘
                        </div>
                        <div v-else-if="form.model_type === 'custom'">
                            <i class="el-icon-info"></i>
                            使用您指定的自定义模型路径
                        </div>
                    </div>
                    
                    <!-- 自定义模型输入框 -->
                    <div v-if="form.model_type === 'custom'" class="custom-model-input">
                        <el-input 
                            v-model="form.custom_model_path" 
                            placeholder="请输入自定义Live2D模型的JSON文件URL"
                            clearable
                        />
                        <div class="el-form-item-msg">例如: https://example.com/live2d-models/mymodel/mymodel.model.json</div>
                    </div>
                </el-form-item>

                <!-- 显示位置 -->
                <el-form-item label="显示位置" class="position-selector">
                    <el-radio-group v-model="form.position">
                        <el-radio label="left">左侧</el-radio>
                        <el-radio label="right">右侧</el-radio>
                    </el-radio-group>
                </el-form-item>

                <!-- 尺寸设置 -->
                <el-form-item label="尺寸设置">
                    <div class="number-input-group">
                        <div class="number-input-item">
                            <span>宽度：</span>
                            <el-input-number 
                                v-model="form.width" 
                                :min="100" 
                                :max="400" 
                                :step="10"
                                controls-position="right"
                            />
                        </div>
                        <div class="number-input-item">
                            <span>高度：</span>
                            <el-input-number 
                                v-model="form.height" 
                                :min="100" 
                                :max="600" 
                                :step="10"
                                controls-position="right"
                            />
                        </div>
                    </div>
                    <div class="el-form-item-msg">模型展示区域大小，调整后会影响看板娘显示的大小</div>
                </el-form-item>

                <!-- 显示模式 -->
                <el-form-item label="显示模式">
                    <el-radio-group v-model="form.display_mode">
                        <el-radio label="normal">标准模式</el-radio>
                        <el-radio label="pure">纯净模式</el-radio>
                    </el-radio-group>
                    <div style="margin-top: 8px; font-size: 12px; color: #909399;">
                        <div v-if="form.display_mode === 'normal'">
                            <i class="el-icon-info"></i>
                            显示看板娘和控制按钮，用户可以关闭和互动
                        </div>
                        <div v-else-if="form.display_mode === 'pure'">
                            <i class="el-icon-info"></i>
                            只显示看板娘模型，无控制按钮，纯净展示
                        </div>
                    </div>
                </el-form-item>

                <!-- 商家修改权限 -->
                <el-form-item label="商家权限">
                    <el-switch 
                        v-model="form.merchant_can_edit" 
                        :active-value="1" 
                        :inactive-value="0"
                        class="custom-switch" 
                    />
                    <div class="el-form-item-msg">开启后商家可以修改自己店铺的看板娘设置，关闭后只能使用默认设置</div>
                </el-form-item>



                <!-- 操作按钮 -->
                <el-form-item style="margin-top: 20px;">
                    <el-button 
                        type="primary" 
                        :loading="loading" 
                        @click="save"
                    >
                        保存设置
                    </el-button>
                    <el-button 
                        type="info" 
                        @click="reset"
                    >
                        重置为默认
                    </el-button>
                </el-form-item>
            </el-form>
        </el-card>
    </div>

    <!-- 引入依赖库 -->
    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>

    <script>
        // 全局错误处理
        window.addEventListener('unhandledrejection', function(event) {
            console.error('未处理的Promise拒绝:', event.reason);
            event.preventDefault(); // 阻止默认的错误处理
        });

        const { createApp, ref, nextTick, watch } = Vue;
        const app = createApp({
            setup() {
                // 状态定义
                const loading = ref(false);
                const form = ref({
                    status: 1,
                    model_type: 'auto',
                    custom_model_path: '',
                    position: 'right',
                    width: 200,
                    height: 300,
                    display_mode: 'normal',
                    merchant_can_edit: 1
                });
                


                // 获取配置
                const fetchData = async () => {
                    try {
                        loading.value = true;
                        const res = await axios.post('getConfig');
                        if (res.data?.code === 200 && res.data?.data) {
                            const data = res.data.data;
                            form.value = {
                                status: parseInt(data.status) || 1,
                                model_type: data.model_type || 'auto',
                                custom_model_path: data.custom_model_path || '',
                                position: data.position || 'right',
                                width: parseInt(data.width) || 200,
                                height: parseInt(data.height) || 300,
                                display_mode: data.display_mode || 'normal',
                                merchant_can_edit: data.merchant_can_edit !== undefined ? parseInt(data.merchant_can_edit) : 1
                            };


                        } else {
                            console.warn('获取配置返回异常数据:', res.data);
                            ElementPlus.ElMessage.warning('获取配置数据异常，使用默认配置');
                        }
                    } catch (error) {
                        console.error('获取数据失败:', error);
                        ElementPlus.ElMessage.error('获取数据失败，请刷新页面重试');
                    } finally {
                        loading.value = false;
                    }
                };

                // 保存配置
                const save = async () => {
                    try {
                        loading.value = true;
                        // 数据验证
                        if (form.value.model_type === 'custom' && !form.value.custom_model_path) {
                            ElementPlus.ElMessage.error('请输入自定义模型路径');
                            return;
                        }

                        const res = await axios.post('saveConfig', form.value);
                        if (res.data?.code === 200) {
                            ElementPlus.ElMessage.success(res.data.msg || '保存成功');
                            // 使用返回的数据更新表单
                            if (res.data.data) {
                                const data = res.data.data;
                                form.value = {
                                    status: parseInt(data.status),
                                    model_type: data.model_type,
                                    custom_model_path: data.custom_model_path,
                                    position: data.position,
                                    width: parseInt(data.width),
                                    height: parseInt(data.height),
                                    display_mode: data.display_mode || 'normal',
                                    merchant_can_edit: data.merchant_can_edit !== undefined ? parseInt(data.merchant_can_edit) : 1
                                };
                            }
                        } else {
                            ElementPlus.ElMessage.error(res.data?.msg || '保存失败');
                        }
                    } catch (error) {
                        console.error('保存失败:', error);
                        ElementPlus.ElMessage.error('保存失败，请稍后重试');
                    } finally {
                        loading.value = false;
                    }
                };
                
                // 重置为默认配置
                const reset = async () => {
                    try {
                        ElementPlus.ElMessageBox.confirm('确定要重置为默认配置吗？', '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }).then(async () => {
                            loading.value = true;
                            const res = await axios.post('resetConfig');
                            if (res.data?.code === 200) {
                                ElementPlus.ElMessage.success('重置成功');
                                // 重新获取配置
                                fetchData();
                            } else {
                                ElementPlus.ElMessage.error(res.data?.msg || '重置失败');
                            }
                            loading.value = false;
                        }).catch(() => {});
                    } catch (error) {
                        console.error('重置失败:', error);
                        ElementPlus.ElMessage.error('重置失败，请稍后重试');
                        loading.value = false;
                    }
                };
                
                // 处理模型变更
                const handleModelChange = () => {
                    // 如果切换到非自定义模型，清空自定义模型路径
                    if (form.value.model_type !== 'custom') {
                        form.value.custom_model_path = '';
                    }
                };



                // 初始化数据
                nextTick(() => {
                    fetchData();
                });

                // 返回数据和方法
                return {
                    loading,
                    form,
                    handleModelChange,
                    save,
                    reset
                };
            }
        });

        app.use(ElementPlus);
        app.mount('#app');
    </script>
</body>
</html> 