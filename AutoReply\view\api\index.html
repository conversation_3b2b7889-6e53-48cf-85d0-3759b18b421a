<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>投诉自动回复配置</title>
    <style>
        .page-container {
            padding: 20px;
            max-width: 1200px;  /* 限制最大宽度 */
            margin: 0 auto;     /* 居中显示 */
        }
        .rule-card {
            margin-bottom: 20px;
            border-radius: 8px;  /* 圆角更柔和 */
        }
        .keyword-tag {
            margin-right: 8px;
            margin-bottom: 8px;
        }
        .test-panel {
            margin-top: 30px;
            padding: 20px;
            background: #f5f7fa;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);  /* 添加轻微阴影 */
        }
        /* 添加新的样式 */
        .section-title {
            margin: 20px 0;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
            color: #333;
        }
        .form-actions {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            text-align: center;
        }
        .rule-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .help-text {
            color: #909399;
            font-size: 13px;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div id="app" class="page-container">
        <el-card>
            <el-form :model="config" label-width="120px">
                <el-form-item label="功能状态：">
                    <el-switch v-model="config.status" :active-value="1" :inactive-value="0"/>
                    <span class="el-text-small" style="margin-left: 10px;color: #909399;">
                        开启后将自动回复买家投诉消息
                    </span>
                </el-form-item>
                
                <el-form-item label="邮件通知：">
                    <el-switch v-model="config.email_notify" :active-value="1" :inactive-value="0"/>
                    <span class="el-text-small" style="margin-left: 10px;color: #909399;">
                        开启后将发送邮件通知
                    </span>
                </el-form-item>

                <el-form-item label="通知邮箱：" v-if="config.email_notify === 1">
                    <el-input v-model="config.notify_email" placeholder="请输入接收通知的邮箱地址"></el-input>
                </el-form-item>

                <el-form-item label="检查间隔：">
                    <el-input-number 
                        v-model="config.check_interval"
                        :min="10"
                        :max="3600"
                        :step="10">
                        <template #suffix>秒</template>
                    </el-input-number>
                    <span class="el-text-small" style="margin-left: 10px;color: #909399;">
                        每隔多少秒检查一次新投诉
                    </span>
                </el-form-item>

                <el-form-item label="邮件检查间隔：" v-if="config.email_notify === 1">
                    <el-input-number 
                        v-model="config.email_check_interval"
                        :min="10"
                        :max="3600"
                        :step="10">
                        <template #suffix>秒</template>
                    </el-input-number>
                    <span class="el-text-small" style="margin-left: 10px;color: #909399;">
                        每隔多少秒检查一次并发送邮件通知（建议设置大于60秒）
                    </span>
                </el-form-item>

                <el-form-item label="商家邮件通知：">
                    <el-switch 
                        v-model="config.allow_merchant_email" 
                        :active-value="1" 
                        :inactive-value="0"
                        @change="handleMerchantEmailChange"/>
                    <span class="el-text-small" style="margin-left: 10px;color: #909399;">
                        开启后商家可以使用邮件通知功能，关闭后将清除所有商家的邮件配置
                    </span>
                </el-form-item>

                <el-divider>回复规则</el-divider>

                <div v-for="(rule, index) in config.keyword_rules" :key="index" class="rule-card">
                    <el-card>
                        <template #header>
                            <div style="display: flex; justify-content: space-between; align-items: center">
                                <span>规则 #{{index + 1}}</span>
                                <el-switch v-model="rule.status" :active-value="1" :inactive-value="0"/>
                            </div>
                        </template>

                        <el-form-item label="触发关键词：">
                            <el-tag
                                v-for="(keyword, kidx) in rule.keywords"
                                :key="kidx"
                                closable
                                class="keyword-tag"
                                @close="removeKeyword(index, kidx)">
                                {{keyword}}
                            </el-tag>
                            <el-input
                                v-if="inputVisible[index]"
                                :class="`keyword-input-${index}`"
                                v-model="inputValue[index]"
                                class="keyword-input"
                                size="small"
                                @keyup.enter="addKeyword(index)"
                                @blur="addKeyword(index)">
                            </el-input>
                            <el-button v-else size="small" @click="showInput(index)">
                                + 添加关键词
                            </el-button>
                        </el-form-item>

                        <el-form-item label="回复内容：">
                            <el-input 
                                v-model="rule.reply"
                                type="textarea"
                                :rows="3"
                                placeholder="请输入自动回复内容">
                            </el-input>
                        </el-form-item>

                        <el-button type="danger" @click="removeRule(index)">删除规则</el-button>
                    </el-card>
                </div>

                <div class="form-actions">
                    <el-button type="primary" @click="addRule" style="margin-right: 10px;">添加规则</el-button>
                    <el-button type="primary" @click="saveConfig" :loading="loading">保存配置</el-button>
                </div>

                <div class="test-panel">
                    <el-form-item label="测试内容：">
                        <el-input 
                            v-model="testContent"
                            type="textarea"
                            :rows="3"
                            placeholder="请输入测试内容,用于测试关键词匹配">
                        </el-input>
                    </el-form-item>
                    <el-form-item label="投诉单ID：">
                        <el-input-number 
                            v-model="testComplaintId"
                            :min="1"
                            placeholder="请输入投诉单ID">
                        </el-input-number>
                        <el-switch
                            v-model="autoSend"
                            style="margin-left: 15px"
                            active-text="自动发送"
                            inactive-text="仅测试">
                        </el-switch>
                    </el-form-item>
                    <el-button type="success" @click="testRule" :loading="testing">
                        测试规则
                    </el-button>
                </div>
            </el-form>
        </el-card>
    </div>

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>

    <script>
        const { createApp, ref, reactive, onMounted } = Vue
        const app = createApp({
            setup() {
                const config = reactive({
                    status: 0,
                    email_notify: 0,
                    notify_email: '',
                    check_interval: 60,
                    allow_merchant_email: 0,
                    keyword_rules: []
                })

                const loading = ref(false)
                const testing = ref(false)
                const inputVisible = ref([])
                const inputValue = ref([])
                const testContent = ref('')
                const testComplaintId = ref('')
                const autoSend = ref(false)

                // 获取配置
                const getConfig = async () => {
                    try {
                        const res = await axios.post('getConfig')
                        if (res.data?.code === 200 && res.data.data) {
                            const data = res.data.data;
                            config.status = parseInt(data.status || 0);
                            config.email_notify = parseInt(data.email_notify || 0);
                            config.notify_email = data.notify_email || '';
                            config.check_interval = parseInt(data.check_interval || 60);
                            config.allow_merchant_email = parseInt(data.allow_merchant_email || 0);
                            
                            // 处理规则数据
                            config.keyword_rules = (data.keyword_rules || []).map(rule => ({
                                status: parseInt(rule.status || 1),
                                keywords: Array.isArray(rule.keywords) ? [...rule.keywords] : [],
                                reply: String(rule.reply || '')
                            }));
                            
                            // 重置输入状态
                            inputVisible.value = new Array(config.keyword_rules.length).fill(false);
                            inputValue.value = new Array(config.keyword_rules.length).fill('');
                        }
                    } catch (error) {
                        console.error('Get config error:', error);
                        ElementPlus.ElMessage.error('获取配置失败');
                    }
                }

                // 保存配置
                const saveConfig = async () => {
                    loading.value = true
                    try {
                        // 验证规则
                        if (config.keyword_rules.length > 0) {
                            for (const rule of config.keyword_rules) {
                                if (!rule.reply) {
                                    ElementPlus.ElMessage.error('请为每个规则填写回复内容');
                                    return;
                                }
                            }
                        }

                        // 准备提交数据
                        const postData = {
                            status: parseInt(config.status || 0),
                            email_notify: parseInt(config.email_notify || 0),
                            notify_email: config.notify_email,
                            check_interval: parseInt(config.check_interval || 60),
                            allow_merchant_email: parseInt(config.allow_merchant_email || 0),
                            keyword_rules: config.keyword_rules.map(rule => ({
                                status: parseInt(rule.status || 1),
                                keywords: Array.isArray(rule.keywords) ? [...rule.keywords] : [],
                                reply: String(rule.reply || '')
                            }))
                        };

                        const res = await axios.post('saveConfig', postData);
                        
                        if (res.data.code === 200) {
                            ElementPlus.ElMessage.success('保存成功');
                        } else {
                            throw new Error(res.data.msg || '保存失败');
                        }
                    } catch (error) {
                        ElementPlus.ElMessage.error(error.message || '保存失败，请稍后重试');
                        // 如果保存失败，重新获取配置
                        await getConfig();
                    } finally {
                        loading.value = false;
                    }
                }

                // 测试规则
                const testRule = async () => {
                    if (!testContent.value) {
                        ElementPlus.ElMessage.warning('请输入测试内容');
                        return;
                    }

                    if (autoSend.value && !testComplaintId.value) {
                        ElementPlus.ElMessage.warning('启用自动发送时需要填写投诉单ID');
                        return;
                    }

                    testing.value = true
                    try {
                        const res = await axios.post('testRule', {
                            content: testContent.value,
                            complaint_id: testComplaintId.value,
                            auto_send: autoSend.value
                        });
                        
                        if (res.data.code === 200) {
                            if (res.data.data) {
                                let message = `匹配关键词: ${res.data.data.keyword}\n将回复: ${res.data.data.reply}`;
                                if (res.data.data.sent) {
                                    message += '\n已自动发送回复';
                                }
                                ElementPlus.ElMessage({
                                    type: 'success',
                                    message: message,
                                    duration: 5000
                                });
                            } else {
                                ElementPlus.ElMessage({
                                    type: 'info',
                                    message: '未匹配到任何规则',
                                    duration: 3000
                                });
                            }
                        } else {
                            throw new Error(res.data.msg || '测试失败');
                        }
                    } catch (error) {
                        ElementPlus.ElMessage.error(error.message || '测试失败，请稍后重试');
                    } finally {
                        testing.value = false;
                    }
                }

                // 添加规则
                const addRule = () => {
                    config.keyword_rules.push({
                        status: 1,
                        keywords: [],
                        reply: ''
                    })
                    inputVisible.value.push(false)
                    inputValue.value.push('')
                }

                // 删除规则
                const removeRule = (index) => {
                    config.keyword_rules.splice(index, 1)
                    inputVisible.value.splice(index, 1)
                    inputValue.value.splice(index, 1)
                }

                // 显示关键词输入框
                const showInput = (index) => {
                    inputVisible.value[index] = true
                    setTimeout(() => {
                        const input = document.querySelector(`.keyword-input-${index}`)
                        if (input) {
                            input.focus()
                        }
                    }, 10)
                }

                // 添加关键词
                const addKeyword = (index) => {
                    const value = inputValue.value[index]
                    if (value) {
                        config.keyword_rules[index].keywords.push(value)
                    }
                    inputVisible.value[index] = false
                    inputValue.value[index] = ''
                }

                // 删除关键词
                const removeKeyword = (ruleIndex, keywordIndex) => {
                    config.keyword_rules[ruleIndex].keywords.splice(keywordIndex, 1)
                }

                // 处理商家邮件开关变化
                const handleMerchantEmailChange = (value) => {
                    if (value === 0) {
                        ElementPlus.ElMessageBox.confirm(
                            '关闭商家邮件通知功能将清除所有商家的邮件配置，是否继续？',
                            '警告',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning'
                            }
                        ).then(async () => {
                            // 用户确认关闭，立即保存配置
                            try {
                                const res = await axios.post('saveConfig', {
                                    ...config,
                                    allow_merchant_email: 0
                                });
                                if (res.data.code === 200) {
                                    ElementPlus.ElMessage.success('保存成功');
                                } else {
                                    throw new Error(res.data.msg || '保存失败');
                                }
                            } catch (error) {
                                ElementPlus.ElMessage.error(error.message || '保存失败，请稍后重试');
                                // 如果保存失败，恢复开关状态
                                config.allow_merchant_email = 1;
                            }
                        }).catch(() => {
                            // 用户取消，恢复开关状态
                            config.allow_merchant_email = 1;
                        });
                    } else {
                        // 开启时直接保存配置
                        saveConfig();
                    }
                };

                onMounted(() => {
                    getConfig()
                })

                return {
                    config,
                    loading,
                    testing,
                    inputVisible,
                    inputValue,
                    testContent,
                    testComplaintId,
                    autoSend,
                    saveConfig,
                    testRule,
                    addRule,
                    removeRule,
                    showInput,
                    addKeyword,
                    removeKeyword,
                    handleMerchantEmailChange
                }
            }
        })

        app.use(ElementPlus)
        app.mount('#app')
    </script>
</body>
</html>
