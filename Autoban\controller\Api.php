<?php
namespace plugin\Autoban\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;

class Api extends BasePlugin
{
    protected $scene = [
        'admin'
    ];
    protected $noNeedLogin = [];

    public function index()
    {
        return View::fetch();
    }

    // 获取配置数据
    public function fetchData()
    {
        $data = [
            // 投诉率配置
            'complaint_status' => intval(plugconf("Autoban.complaint_status") ?? 0),
            'complaint_type' => plugconf("Autoban.complaint_type") ?? 'rate',
            'complaint_rate_threshold' => floatval(plugconf("Autoban.complaint_rate_threshold") ?? 50),
            'complaint_count_threshold' => intval(plugconf("Autoban.complaint_count_threshold") ?? 5),
            'complaint_min_orders' => intval(plugconf("Autoban.complaint_min_orders") ?? 5),
            'complaint_rate_content' => plugconf("Autoban.complaint_rate_content"),
            'complaint_count_content' => plugconf("Autoban.complaint_count_content"),
            'complaint_expire_time' => intval(plugconf("Autoban.complaint_expire_time") ?? 0),
            // 无流水配置
            'order_status' => intval(plugconf("Autoban.order_status") ?? 0),
            'order_days' => intval(plugconf("Autoban.order_days") ?? 30),
            'order_start_time' => plugconf("Autoban.order_start_time") ?: '',
            'order_end_time' => plugconf("Autoban.order_end_time") ?: '',
            'order_ban_content' => plugconf("Autoban.order_ban_content"),
            'order_ban_types' => plugconf("Autoban.order_ban_types"),
            'order_expire_time' => intval(plugconf("Autoban.order_expire_time") ?? 0),
            // 新增：排除新用户天数配置
            'exclude_new_user_days' => intval(plugconf("Autoban.exclude_new_user_days") ?? 7),
            // 添加超时配置
            'timeout_status' => intval(plugconf("Autoban.timeout_status") ?? 0),
            'timeout_days' => intval(plugconf("Autoban.timeout_days") ?? 30),
            'timeout_batch_processing' => intval(plugconf("Autoban.timeout_batch_processing") ?? 0),
            'timeout_batch_size' => intval(plugconf("Autoban.timeout_batch_size") ?? 100),
            // 未实名配置
            'unauth_status' => intval(plugconf("Autoban.unauth_status") ?? 0),
            'unauth_days' => intval(plugconf("Autoban.unauth_days") ?? 7), // 确保加载未实名天数配置
            'unauth_ban_content' => plugconf("Autoban.unauth_ban_content") ?? '您的账号未完成实名认证，已被系统自动封禁。',
            'unauth_expire_time' => intval(plugconf("Autoban.unauth_expire_time") ?? 0),
            // 添加场景封禁配置
            'scene_status' => intval(plugconf("Autoban.scene_status") ?? 0),
            'scene_ban_status' => intval(plugconf("Autoban.scene_ban_status") ?? 0),
            'scene_ban_minutes' => intval(plugconf("Autoban.scene_ban_minutes") ?? 30),
            'scene_delete_status' => intval(plugconf("Autoban.scene_delete_status") ?? 0),
            'scene_content' => plugconf("Autoban.scene_content") ?: '',
            'scene_website' => plugconf("Autoban.scene_website") ?: '',
            'scene_ban_content' => plugconf("Autoban.scene_ban_content") ?: '',
            'scene_delete_minutes' => intval(plugconf("Autoban.scene_delete_minutes") ?? 30),
            'scene_check_auth_users' => intval(plugconf("Autoban.scene_check_auth_users") ?? 0),
            // 添加封禁时间配置
            'default_ban_expire_time' => intval(plugconf("Autoban.default_ban_expire_time") ?? 4102415999),
            // 添加场景封禁时间
            'scene_ban_expire_time' => intval(plugconf("Autoban.scene_ban_expire_time") ?? 0),
        ];

        $this->success('success', $data);
    }

    // 保存投诉率配置
    public function saveComplaint()
    {
        $status = $this->request->post('status/d', 0);
        $type = $this->request->post('type/s', 'rate');
        $rateThreshold = $this->request->post('rateThreshold/f', 50);
        $countThreshold = $this->request->post('countThreshold/d', 5);
        $minOrders = $this->request->post('minOrders/d', 5);
        $rateContent = $this->request->post('rateContent/s', '');
        $countContent = $this->request->post('countContent/s', '');
        $expireTime = $this->request->post('expireTime/d', 0);

        // 验证阈值
        if ($type === 'rate') {
            if ($rateThreshold < 1 || $rateThreshold > 100) {
                $this->error('投诉率必须在1-100之间');
            }
            if ($minOrders < 1 || $minOrders > 100) {
                $this->error('最小订单数必须在1-100之间');
            }
            if (empty($rateContent)) {
                $this->error('投诉率封禁提示内容不能为空');
            }
        } else {
            if ($countThreshold < 1 || $countThreshold > 999) {
                $this->error('投诉数量必须在1-999之间');
            }
            if (empty($countContent)) {
                $this->error('投诉数量封禁提示内容不能为空');
            }
        }

        plugconf("Autoban.complaint_status", $status);
        plugconf("Autoban.complaint_type", $type);
        plugconf("Autoban.complaint_rate_threshold", $rateThreshold);
        plugconf("Autoban.complaint_count_threshold", $countThreshold);
        plugconf("Autoban.complaint_min_orders", $minOrders);
        plugconf("Autoban.complaint_rate_content", $rateContent);
        plugconf("Autoban.complaint_count_content", $countContent);
        plugconf("Autoban.complaint_expire_time", $expireTime);

        $this->success('保存成功');
    }

    // 保存无流水配置
    public function saveOrder()
    {
        $status = $this->request->post('status/d', 0);
        $banTypes = $this->request->post('banTypes/a', []);
        $timeRangeType = $this->request->post('timeRangeType/s', 'days');
        $days = $this->request->post('days/d', 0);
        $startTime = $this->request->post('startTime/s', '');
        $endTime = $this->request->post('endTime/s', '');
        $banContent = $this->request->post('banContent/s', '');
        // 新增：获取排除新用户天数
        $excludeNewUserDays = $this->request->post('excludeNewUserDays/d', 7);
        // 新增：获取封禁时长
        $expireTime = $this->request->post('expireTime/d', 0);

        // 验证封禁内容和类型
        if (empty($banContent)) {
            $this->error('封禁提示内容不能为空');
        }
        if (empty($banTypes)) {
            $this->error('请至少选择一种封禁类型');
        }

        // 验证排除新用户天数
        if ($excludeNewUserDays < 0 || $excludeNewUserDays > 365) {
            $this->error('排除新用户天数必须在0-365之间');
        }

        // 验证时间范围
        if ($timeRangeType === 'days') {
            if ($days < 1 || $days > 365) {
                $this->error('天数必须在1-365之间');
            }
            // 清空精确时间范围
            $startTime = '';
            $endTime = '';
        } else {
            if (empty($startTime) || empty($endTime)) {
                $this->error('请选择完整的时间范围');
            }
            if (strtotime($startTime) > strtotime($endTime)) {
                $this->error('开始时间不能大于结束时间');
            }
            // 清空天数
            $days = 0;
        }

        // 保存配置
        plugconf("Autoban.order_status", $status);
        plugconf("Autoban.order_ban_types", implode(',', $banTypes));
        plugconf("Autoban.order_days", $days);
        plugconf("Autoban.order_start_time", $startTime);
        plugconf("Autoban.order_end_time", $endTime);
        plugconf("Autoban.order_ban_content", $banContent);
        // 确保保存排除新用户天数
        plugconf("Autoban.exclude_new_user_days", $excludeNewUserDays);
        // 保存封禁时长
        plugconf("Autoban.order_expire_time", $expireTime);

        $this->success('保存成功');
    }

    // 保存超时配置
    public function saveTimeout()
    {
        $status = $this->request->post('status/d', 0);
        $days = $this->request->post('days/d', 30);
        $batchProcessing = $this->request->post('batchProcessing/d', 0);
        $batchSize = $this->request->post('batchSize/d', 100);

        if ($days < 1 || $days > 365) {
            $this->error('天数必须在1-365之间');
        }

        if ($batchProcessing && ($batchSize < 5 || $batchSize > 500)) {
            $this->error('每批处理数量必须在5-500之间');
        }

        plugconf("Autoban.timeout_status", $status);
        plugconf("Autoban.timeout_days", $days);
        plugconf("Autoban.timeout_batch_processing", $batchProcessing);
        plugconf("Autoban.timeout_batch_size", $batchSize);

        $this->success('保存成功');
    }

    // 修改删除超时用户方法，添加批量处理功能
    public function deleteTimeoutUsers()
    {
        try {
            // 检查是否是取消操作
            $canceled = $this->request->post('canceled', 0);
            if ($canceled == 1) {
                return json(['code' => 200, 'msg' => '操作已取消']);
            }
            
            // 获取批量处理参数
            $batchProcessing = $this->request->post('batchProcessing/d', 0);
            $batchSize = $this->request->post('batchSize/d', 100);
            
            // 如果未通过参数传递，则读取配置
            if (!$batchProcessing) {
                $batchProcessing = intval(plugconf("Autoban.timeout_batch_processing") ?? 0);
                $batchSize = intval(plugconf("Autoban.timeout_batch_size") ?? 100);
            }
            
            // 验证批量处理参数
            if ($batchProcessing && ($batchSize < 5 || $batchSize > 500)) {
                return json(['code' => 500, 'msg' => '配置错误：每批处理数量必须在5-500之间']);
            }
            
            $days = intval(plugconf("Autoban.timeout_days") ?? 30);
            if ($days <= 0) {
                return json(['code' => 500, 'msg' => '配置错误：删除天数必须大于0']);
            }
            
            $timeLimit = time() - ($days * 86400);

            // 先获取所有封禁用户，不带时间限制
            try {
                $allBannedUsers = Db::name('user_risk')
                    ->alias('ur')
                    ->join('user u', 'ur.user_id = u.id')
                    ->where('ur.risk_type', 2)
                    ->field([
                        'u.id', 
                        'u.username', 
                        'ur.user_id',
                        'ur.create_time',
                        'FROM_UNIXTIME(ur.create_time) as ban_time'
                    ])
                    ->select()
                    ->toArray();
            } catch (\Exception $e) {
                return json([
                    'code' => 500, 
                    'msg' => '查询封禁用户失败: ' . $e->getMessage() . "\nSQL: " . Db::getLastSql()
                ]);
            }

            if (empty($allBannedUsers)) {
                return json(['code' => 200, 'msg' => '当前没有任何封禁用户']);
            }

            // 分类处理
            $timeoutUsers = [];
            $activeUsers = [];
            foreach ($allBannedUsers as $user) {
                if ($user['create_time'] < $timeLimit) {
                    $timeoutUsers[] = $user;
                } else {
                    $remainDays = ceil(($user['create_time'] - $timeLimit) / 86400);
                    $user['remain_days'] = $remainDays;
                    $activeUsers[] = $user;
                }
            }

            // 如果有超时用户，执行删除
            if (!empty($timeoutUsers)) {
                $userIds = array_column($timeoutUsers, 'user_id');
                $totalCount = count($userIds);
                
                // 是否需要批量处理
                if ($batchProcessing && $totalCount > $batchSize) {
                    // 创建批次任务
                    $batches = array_chunk($userIds, $batchSize);
                    $batchCount = count($batches);
                    $totalDeleted = 0;
                    $deletedCounts = [];
                    
                    try {
                        $message = "开始批量处理 {$totalCount} 个超时用户，共 {$batchCount} 个批次，每批 {$batchSize} 个用户。\n\n";
                        
                        foreach ($batches as $index => $batchUserIds) {
                            Db::startTrans();
                            
                            // 使用 Hook 类的删除方法删除与用户相关的所有数据
                            $hookObj = new \plugin\Autoban\Hook();
                            $batchDeletedCounts = $hookObj->deleteUserRelatedData($batchUserIds);
                            
                            Db::commit();
                            
                            // 合并统计数据
                            foreach ($batchDeletedCounts as $table => $count) {
                                if (!isset($deletedCounts[$table])) {
                                    $deletedCounts[$table] = 0;
                                }
                                $deletedCounts[$table] += $count;
                            }
                            
                            $batchSize = count($batchUserIds);
                            $totalDeleted += $batchSize;
                            $progressPercent = round(($index + 1) / $batchCount * 100, 2);
                            
                            $message .= "已完成第 " . ($index + 1) . " 批 ({$batchSize} 个用户)，总进度 {$progressPercent}%\n";
                            
                            // 休眠一小段时间，避免服务器过载
                            usleep(200000); // 休眠0.2秒
                        }
                        
                        $message .= "\n总共删除 {$totalDeleted} 个超时用户。\n";
                        $message .= "删除数据明细：\n";
                        
                        foreach ($deletedCounts as $table => $count) {
                            if ($count > 0) {
                                $message .= "- {$table}表: {$count}条记录\n";
                            }
                        }
                        
                        if (!empty($activeUsers)) {
                            $message .= "\n当前封禁用户状态：\n";
                            foreach ($activeUsers as $user) {
                                $message .= "用户 {$user['username']} 还剩 {$user['remain_days']} 天\n";
                            }
                        }
                        
                        return json(['code' => 200, 'msg' => $message]);
                    } catch (\Exception $e) {
                        Db::rollback();
                        return json([
                            'code' => 500,
                            'msg' => sprintf(
                                "批量删除失败：%s\n文件：%s\n行号：%d",
                                $e->getMessage(),
                                $e->getFile(),
                                $e->getLine()
                            )
                        ]);
                    }
                } else {
                    // 普通处理（不分批）
                    try {
                        Db::startTrans();
                        
                        // 使用 Hook 类的删除方法删除与用户相关的所有数据
                        $hookObj = new \plugin\Autoban\Hook();
                        $deletedCounts = $hookObj->deleteUserRelatedData($userIds);
                        
                        Db::commit();
                        
                        // 构建返回消息
                        $message = "成功删除 " . count($userIds) . " 个超时用户。\n";
                        $message .= "删除数据明细：\n";
                        
                        foreach ($deletedCounts as $table => $count) {
                            if ($count > 0) {
                                $message .= "- {$table}表: {$count}条记录\n";
                            }
                        }
                        
                        if (!empty($activeUsers)) {
                            $message .= "\n当前封禁用户状态：\n";
                            foreach ($activeUsers as $user) {
                                $message .= "用户 {$user['username']} 还剩 {$user['remain_days']} 天\n";
                            }
                        }
                        
                        return json(['code' => 200, 'msg' => $message]);
                        
                    } catch (\Exception $e) {
                        Db::rollback();
                        return json([
                            'code' => 500,
                            'msg' => sprintf(
                                "删除失败：%s\n文件：%s\n行号：%d",
                                $e->getMessage(),
                                $e->getFile(),
                                $e->getLine()
                            )
                        ]);
                    }
                }
            } else {
                // 只返回当前封禁用户状态
                $message = "当前封禁用户状态：\n";
                foreach ($activeUsers as $user) {
                    $message .= "用户 {$user['username']} 还剩 {$user['remain_days']} 天\n";
                }
                return json(['code' => 200, 'msg' => $message]);
            }
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => sprintf(
                    "错误信息：%s\n文件：%s\n行号：%d",
                    $e->getMessage(),
                    $e->getFile(),
                    $e->getLine()
                )
            ]);
        }
    }

    // 修改手动解封并加白名单方法
    public function unbanAndWhitelist()
    {
        try {
            $userId = $this->request->post('user_id/d', 0);
            $days = $this->request->post('whitelist_days/d', 30);
            
            if (empty($userId)) {
                return json(['code' => 500, 'msg' => '用户ID不能为空']);
            }
            
            if ($days < 1 || $days > 365) {
                return json(['code' => 500, 'msg' => '白名单天数必须在1-365天之间']);
            }

            // 查询用户信息
            $user = Db::name('user')
                ->where('id', $userId)
                ->field('id,username')
                ->find();

            if (!$user) {
                return json(['code' => 500, 'msg' => '用户不存在']);
            }

            // 获取当前白名单
            $whitelist = plugconf("Autoban.whitelist") ? json_decode(plugconf("Autoban.whitelist"), true) : [];
            
            // 检查用户是否已在白名单中且未过期
            foreach ($whitelist as $item) {
                if ($item['user_id'] == $user['id'] && $item['expire_time'] > time()) {
                    return json([
                        'code' => 500, 
                        'msg' => sprintf(
                            '该用户已在白名单中，将在 %s 过期',
                            date('Y-m-d H:i:s', $item['expire_time'])
                        )
                    ]);
                }
            }

            try {
                Db::startTrans();
                
                // 1. 删除封禁记录
                Db::name('user_risk')
                    ->where('user_id', $user['id'])
                    ->where('risk_type', 2)
                    ->delete();

                // 2. 更新用户状态
                Db::name('user')
                    ->where('id', $user['id'])
                    ->update([
                        'rules' => '[]',
                        'update_time' => time()
                    ]);

                // 3. 移除已过期或相同用户的记录
                $whitelist = array_filter($whitelist ?: [], function($item) {
                    return $item['expire_time'] > time();
                });
                
                $expireTime = time() + ($days * 86400);
                
                // 添加新记录
                $whitelist[] = [
                    'user_id' => $user['id'],
                    'username' => $user['username'],
                    'expire_time' => $expireTime,
                    'create_time' => time()
                ];
                
                // 保存白名单
                plugconf("Autoban.whitelist", json_encode(array_values($whitelist)));
                
                Db::commit();
                
                return json([
                    'code' => 200, 
                    'msg' => '操作成功',
                    'data' => [
                        'user_id' => $user['id'],
                        'username' => $user['username'],
                        'whitelist_expire' => date('Y-m-d H:i:s', $expireTime)
                    ]
                ]);
                
            } catch (\Exception $e) {
                Db::rollback();
                return json([
                    'code' => 500,
                    'msg' => '操作失败：' . $e->getMessage()
                ]);
            }
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '系统错误：' . $e->getMessage()
            ]);
        }
    }

    // 获取白名单列表
    public function getWhitelist()
    {
        try {
            $whitelist = plugconf("Autoban.whitelist") ? json_decode(plugconf("Autoban.whitelist"), true) : [];
            
            // 过滤和格式化列表
            $list = array_map(function($item) {
                return [
                    'user_id' => $item['user_id'],
                    'username' => $item['username'],
                    'create_time' => date('Y-m-d H:i:s', $item['create_time']),
                    'expire_time' => date('Y-m-d H:i:s', $item['expire_time']),
                    'status' => $item['expire_time'] > time() ? '有效' : '已过期'
                ];
            }, $whitelist ?: []);
            
            // 只返回未过期的记录
            $list = array_filter($list, function($item) {
                return $item['status'] === '有效';
            });
            
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => array_values($list)
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '获取白名单失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    // 移除白名单
    public function removeWhitelist()
    {
        try {
            $userId = $this->request->post('user_id/d', 0);
            
            if (empty($userId)) {
                return json(['code' => 500, 'msg' => '参数错误：用户ID不能为空']);
            }

            // 先获取当前白名单
            $whitelist = plugconf("Autoban.whitelist");
            if ($whitelist === false) {
                return json(['code' => 500, 'msg' => '获取白名单配置失败']);
            }

            $whitelist = json_decode($whitelist, true) ?: [];
            
            // 检查用户是否在白名单中
            $found = false;
            foreach ($whitelist as $item) {
                if ($item['user_id'] == $userId) {
                    $found = true;
                    break;
                }
            }
            
            if (!$found) {
                return json(['code' => 500, 'msg' => '用户不在白名单中']);
            }

            // 移除指定用户
            $whitelist = array_filter($whitelist, function($item) use ($userId) {
                return $item['user_id'] != $userId;
            });
            
            // 保存更新后的白名单
            $result = plugconf("Autoban.whitelist", json_encode(array_values($whitelist)));
            if ($result === false) {
                return json(['code' => 500, 'msg' => '保存白名单失败']);
            }
            
            // 记录操作日志
            error_log("Autoban: 成功从白名单移除用户ID: {$userId}");
            
            return json(['code' => 200, 'msg' => '移除成功']);
        } catch (\Exception $e) {
            error_log("Autoban: 移除白名单失败: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return json([
                'code' => 500, 
                'msg' => '移除白名单失败：' . $e->getMessage()
            ]);
        }
    }

    // 修改用户搜索接口
    public function searchUsers()
    {
        try {
            $userId = $this->request->post('user_id', '');
            if (empty($userId)) {
                return json(['code' => 200, 'data' => []]);
            }

            // 搜索用户
            $users = Db::name('user')
                ->where('id', 'like', "%{$userId}%")
                ->field(['id', 'username'])
                ->limit(10)
                ->select()
                ->toArray();

            return json(['code' => 200, 'data' => $users]);
        } catch (\Exception $e) {
            error_log("Autoban: 搜索用户失败: " . $e->getMessage());
            return json(['code' => 500, 'msg' => '搜索用户失败']);
        }
    }

    // 保存未实名配置
    public function saveUnauth()
    {
        $status = $this->request->post('status/d', 0);
        $days = $this->request->post('days/d', 7);  // 获取天数参数
        $banContent = $this->request->post('banContent/s', '');
        $expireTime = $this->request->post('expireTime/d', 0);

        if (empty($banContent)) {
            $this->error('封禁提示内容不能为空');
        }

        if ($days < 1 || $days > 365) {
            $this->error('天数必须在1-365之间');
        }
        
        // 记录保存的参数，便于调试
        error_log("Autoban: 保存未实名配置 - status={$status}, days={$days}, expireTime={$expireTime}");

        plugconf("Autoban.unauth_status", $status);
        plugconf("Autoban.unauth_days", $days);  // 确保保存天数配置
        plugconf("Autoban.unauth_ban_content", $banContent);
        plugconf("Autoban.unauth_expire_time", $expireTime);
        
        // 返回完整的配置数据
        $data = [
            'unauth_status' => $status,
            'unauth_days' => $days,
            'unauth_ban_content' => $banContent,
            'unauth_expire_time' => $expireTime
        ];

        $this->success('保存成功', $data);
    }

    // 检查未实名用户
    public function checkUnauth()
    {
        try {
            // 获取未实名封禁提示内容
            $banContent = plugconf("Autoban.unauth_ban_content") ?? '您的账号未完成实名认证，已被系统自动封禁。';
            // 获取未实名天数配置
            $days = intval(plugconf("Autoban.unauth_days") ?? 7);
            $timeLimit = time() - ($days * 86400);
            
            // 获取默认封禁时间
            $expireTime = intval(plugconf("Autoban.default_ban_expire_time") ?? 4102415999);
            
            // 如果设置了未实名特定的封禁时间，则使用未实名封禁时间
            $unauthExpireTime = intval(plugconf("Autoban.unauth_expire_time") ?? 0);
            if ($unauthExpireTime > 0) {
                $expireTime = $unauthExpireTime;
            }
            
            // 记录执行参数
            error_log("Autoban: 执行未实名检查 - 天数={$days}, 封禁时长={$expireTime}");
            
            // 查找未实名用户（user_auth表中不存在或params为null的用户）
            $unauthUsers = Db::name('user')
                ->alias('u')
                ->where('u.rules', 'not like', '%CloseUser%')
                ->where('u.create_time', '<', $timeLimit) // 添加时间限制，只检查超过指定天数的用户
                ->where(function ($query) {
                    $query->whereNotExists(function ($query) {
                        $query->name('user_auth')
                            ->whereColumn('user_id', 'u.id')
                            ->where('params', 'not null');
                    });
                })
                ->where(function ($query) use ($timeLimit) {
                    $query->whereNotExists(function ($query) use ($timeLimit) {
                        $query->name('order')
                            ->whereColumn('user_id', 'u.id')
                            ->where('create_time', '>', $timeLimit);
                    });
                })
                ->field(['u.id', 'u.username'])  // 添加 username 字段
                ->select()
                ->toArray();

            if (empty($unauthUsers)) {
                return json(['code' => 200, 'msg' => "执行结果：\n没有发现未实名用户"]);
            }

            // 过滤白名单用户
            $unauthUsers = array_filter($unauthUsers, function($user) {
                return !$this->isInWhitelist($user['id']);
            });

            if (empty($unauthUsers)) {
                return json(['code' => 200, 'msg' => "执行结果：\n发现的用户都在白名单中，无需处理"]);
            }

            // 执行封禁
            $bannedCount = 0;
            $bannedUsers = [];  // 记录被封禁的用户信息
            foreach ($unauthUsers as $user) {
                try {
                    // 检查是否已被封禁
                    $existingBan = Db::name('user_risk')
                        ->where('user_id', $user['id'])
                        ->where('risk_type', 2)
                        ->find();

                    if (!$existingBan) {
                        // 添加封禁记录
                        Db::name('user_risk')->insert([
                            'user_id' => $user['id'],
                            'risk_type' => 2,
                            'user_read' => 0,
                            'content' => $banContent . " (超过{$days}天无交易)",
                            'create_time' => time(),
                            'expire_time' => $expireTime
                        ]);

                        // 更新用户状态
                        Db::name('user')
                            ->where('id', $user['id'])
                            ->update([
                                'rules' => json_encode(['CloseUser']),
                                'update_time' => time()
                            ]);

                        $bannedCount++;
                        $bannedUsers[] = $user['username'];  // 记录被封禁的用户名
                    }
                } catch (\Exception $e) {
                    error_log("Autoban: 封禁未实名用户 {$user['id']} 失败: " . $e->getMessage());
                }
            }

            $message = "执行结果：\n";
            $message .= "共发现 " . count($unauthUsers) . " 个未实名用户\n";
            $message .= "成功封禁 {$bannedCount} 个用户\n";
            if (!empty($bannedUsers)) {
                $message .= "\n被封禁用户：\n" . implode("\n", $bannedUsers);
            }

            return json(['code' => 200, 'msg' => $message]);

        } catch (\Exception $e) {
            error_log("Autoban: 检查未实名用户失败: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return json(['code' => 500, 'msg' => '检测执行失败：' . $e->getMessage()]);
        }
    }

    // 检查用户是否在白名单中
    private function isInWhitelist($userId)
    {
        try {
            $whitelist = plugconf("Autoban.whitelist") ? json_decode(plugconf("Autoban.whitelist"), true) : [];
            if (empty($whitelist)) return false;
            
            foreach ($whitelist as $item) {
                if ($item['user_id'] == $userId && $item['expire_time'] > time()) {
                    return true;
                }
            }
            return false;
        } catch (\Exception $e) {
            error_log("Autoban: 检查白名单异常: " . $e->getMessage());
            return false;
        }
    }

    // 修改保存场景配置方法
    public function saveScene()
    {
        $status = $this->request->post('status/d', 0);
        $banStatus = $this->request->post('banStatus/d', 0);
        $banMinutes = $this->request->post('banMinutes/d', 30);
        $deleteStatus = $this->request->post('deleteStatus/d', 0);
        $deleteMinutes = $this->request->post('deleteMinutes/d', 30);
        $sceneContent = $this->request->post('sceneContent/s', '');
        $sceneWebsite = $this->request->post('sceneWebsite/s', '');
        $banContent = $this->request->post('banContent/s', '');
        $checkAuthUsers = $this->request->post('checkAuthUsers/d', 0);
        $expireTime = $this->request->post('expireTime/d', 0);

        // 如果总开关关闭，强制关闭封禁和删除功能
        if ($status == 0) {
            $banStatus = 0;
            $deleteStatus = 0;
        }

        if (empty($banContent)) {
            $this->error('封禁提示内容不能为空');
        }

        // 验证封禁时间
        if ($banMinutes < 1 || $banMinutes > 365) {
            $this->error('封禁时间必须在1-365天之间');
        }

        // 验证删除时间
        if ($deleteMinutes < 1 || $deleteMinutes > 365) {
            $this->error('删除时间必须在1-365天之间');
        }

        // 保存所有配置
        plugconf("Autoban.scene_status", $status);
        plugconf("Autoban.scene_ban_status", $banStatus);
        plugconf("Autoban.scene_ban_minutes", $banMinutes);
        plugconf("Autoban.scene_delete_status", $deleteStatus);
        plugconf("Autoban.scene_content", $sceneContent);
        plugconf("Autoban.scene_website", $sceneWebsite);
        plugconf("Autoban.scene_ban_content", $banContent);
        plugconf("Autoban.scene_delete_minutes", $deleteMinutes);
        plugconf("Autoban.scene_check_auth_users", $checkAuthUsers);
        plugconf("Autoban.scene_ban_expire_time", $expireTime);

        // 返回最新的配置数据
        $data = [
            'scene_status' => $status,
            'scene_ban_status' => $banStatus,
            'scene_ban_minutes' => $banMinutes,
            'scene_delete_status' => $deleteStatus,
            'scene_content' => $sceneContent,
            'scene_website' => $sceneWebsite,
            'scene_ban_content' => $banContent,
            'scene_delete_minutes' => $deleteMinutes,
            'scene_check_auth_users' => $checkAuthUsers,
            'scene_ban_expire_time' => $expireTime
        ];

        $this->success('保存成功', $data);
    }

    // 添加场景检查方法
    public function checkScene()
    {
        try {
            // 获取配置
            $sceneContent = plugconf("Autoban.scene_content");
            $sceneWebsite = plugconf("Autoban.scene_website");
            $banContent = plugconf("Autoban.scene_ban_content") ?? '您的账号因场景内容违规，已被系统自动封禁。';
            $checkAuthUsers = (bool)plugconf("Autoban.scene_check_auth_users", 0);

            if (empty($sceneContent) && empty($sceneWebsite)) {
                return json(['code' => 200, 'msg' => "执行结果：\n未配置任何匹配规则"]);
            }

            $matchedUsers = [];
            
            // 检查场景内容
            if (!empty($sceneContent)) {
                $contentPatterns = explode('|', $sceneContent);
                foreach ($contentPatterns as $pattern) {
                    if (empty($pattern)) continue;
                    
                    // 创建基础查询
                    $query = Db::name('user_auth')
                        ->alias('ua')
                        ->join('user u', 'ua.user_id = u.id')
                        ->where('u.rules', 'not like', '%CloseUser%')
                        ->where('ua.scene_content', 'like', "%{$pattern}%");
                    
                    // 根据是否检查已实名用户决定过滤条件
                    if (!$checkAuthUsers) {
                        // 只检查未实名用户（params为null或为空字符串）
                        $query->where(function($q) {
                            $q->whereNull('ua.params')->whereOr('ua.params', '');
                        });
                    }
                    
                    $users = $query->field(['u.id', 'u.username', 'ua.scene_content'])
                        ->select()
                        ->toArray();
                    
                    foreach ($users as $user) {
                        $matchedUsers[$user['id']] = [
                            'username' => $user['username'],
                            'reason' => "场景内容包含：{$pattern}"
                        ];
                    }
                }
            }

            // 检查场景网站
            if (!empty($sceneWebsite)) {
                $websitePatterns = explode('|', $sceneWebsite);
                foreach ($websitePatterns as $pattern) {
                    if (empty($pattern)) continue;
                    
                    // 创建基础查询
                    $query = Db::name('user_auth')
                        ->alias('ua')
                        ->join('user u', 'ua.user_id = u.id')
                        ->where('u.rules', 'not like', '%CloseUser%')
                        ->where('ua.scene_website', 'like', "%{$pattern}%");
                    
                    // 根据是否检查已实名用户决定过滤条件
                    if (!$checkAuthUsers) {
                        // 只检查未实名用户（params为null或为空字符串）
                        $query->where(function($q) {
                            $q->whereNull('ua.params')->whereOr('ua.params', '');
                        });
                    }
                    
                    $users = $query->field(['u.id', 'u.username', 'ua.scene_website'])
                        ->select()
                        ->toArray();
                    
                    foreach ($users as $user) {
                        if (!isset($matchedUsers[$user['id']])) {
                            $matchedUsers[$user['id']] = [
                                'username' => $user['username'],
                                'reason' => "场景网站包含：{$pattern}"
                            ];
                        }
                    }
                }
            }

            if (empty($matchedUsers)) {
                return json(['code' => 200, 'msg' => "执行结果：\n未发现违规场景用户"]);
            }

            // 过滤白名单用户
            $matchedUsers = array_filter($matchedUsers, function($user, $userId) {
                return !$this->isInWhitelist($userId);
            }, ARRAY_FILTER_USE_BOTH);

            if (empty($matchedUsers)) {
                return json(['code' => 200, 'msg' => "执行结果：\n发现的用户都在白名单中，无需处理"]);
            }

            // 执行封禁
            $bannedCount = 0;
            $message = "执行结果：\n";
            
            foreach ($matchedUsers as $userId => $user) {
                try {
                    // 检查是否已被封禁
                    $existingBan = Db::name('user_risk')
                        ->where('user_id', $userId)
                        ->where('risk_type', 2)
                        ->find();

                    if (!$existingBan) {
                        // 获取默认封禁时间
                        $expireTime = intval(plugconf("Autoban.default_ban_expire_time") ?? 4102415999);
                        
                        // 如果设置了场景特定的封禁时间，则使用场景封禁时间
                        $sceneBanExpireTime = intval(plugconf("Autoban.scene_ban_expire_time") ?? 0);
                        if ($sceneBanExpireTime > 0) {
                            $expireTime = $sceneBanExpireTime;
                        }
                        
                        // 添加封禁记录
                        Db::name('user_risk')->insert([
                            'user_id' => $userId,
                            'risk_type' => 2,
                            'user_read' => 0,
                            'content' => $banContent . "（{$user['reason']}）",
                            'create_time' => time(),
                            'expire_time' => $expireTime
                        ]);

                        // 更新用户状态
                        Db::name('user')
                            ->where('id', $userId)
                            ->update([
                                'rules' => json_encode(['CloseUser']),
                                'update_time' => time()
                            ]);

                        $bannedCount++;
                        $message .= "用户 {$user['username']} - {$user['reason']}\n";
                    }
                } catch (\Exception $e) {
                    error_log("Autoban: 封禁场景违规用户 {$userId} 失败: " . $e->getMessage());
                }
            }

            $message .= "\n共封禁 {$bannedCount} 个用户";
            return json(['code' => 200, 'msg' => $message]);

        } catch (\Exception $e) {
            error_log("Autoban: 检查场景违规失败: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return json(['code' => 500, 'msg' => '检测执行失败：' . $e->getMessage()]);
        }
    }

    // 添加手动删除用户及所有关联数据的方法
    public function deleteUser()
    {
        try {
            // 检查是否是取消操作
            $canceled = $this->request->post('canceled', 0);
            if ($canceled == 1) {
                return json(['code' => 200, 'msg' => '操作已取消']);
            }
            
            $userId = $this->request->post('user_id/d', 0);
            
            if (empty($userId)) {
                return json(['code' => 500, 'msg' => '参数错误：用户ID不能为空']);
            }

            // 检查用户是否存在
            $user = Db::name('user')
                ->where('id', $userId)
                ->field(['id', 'username'])
                ->find();
            
            if (empty($user)) {
                return json(['code' => 500, 'msg' => '用户不存在']);
            }
            
            try {
                Db::startTrans();
                
                // 使用 Hook 类删除该用户的所有关联数据
                $hookObj = new \plugin\Autoban\Hook();
                $deletedCounts = $hookObj->deleteUserRelatedData([$userId]);
                
                Db::commit();
                
                // 构建返回消息
                $message = "成功删除用户 {$user['username']} (ID: {$userId}) 及其所有关联数据。\n";
                $message .= "删除数据明细：\n";
                
                foreach ($deletedCounts as $table => $count) {
                    if ($count > 0) {
                        $message .= "- {$table}表: {$count}条记录\n";
                    }
                }
                
                return json(['code' => 200, 'msg' => $message]);
                
            } catch (\Exception $e) {
                Db::rollback();
                return json([
                    'code' => 500,
                    'msg' => sprintf(
                        "删除失败：%s\n文件：%s\n行号：%d",
                        $e->getMessage(),
                        $e->getFile(),
                        $e->getLine()
                    )
                ]);
            }
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => sprintf(
                    "错误信息：%s\n文件：%s\n行号：%d",
                    $e->getMessage(),
                    $e->getFile(),
                    $e->getLine()
                )
            ]);
        }
    }

    // 添加批量删除用户及所有关联数据的方法
    public function batchDeleteUsers()
    {
        try {
            // 检查是否是取消操作
            $canceled = $this->request->post('canceled', 0);
            if ($canceled == 1) {
                return json(['code' => 200, 'msg' => '操作已取消']);
            }
            
            $userIds = $this->request->post('user_ids/a', []);
            
            if (empty($userIds)) {
                return json(['code' => 500, 'msg' => '参数错误：用户ID列表不能为空']);
            }

            // 检查用户是否存在
            $users = Db::name('user')
                ->whereIn('id', $userIds)
                ->field(['id', 'username'])
                ->select()
                ->toArray();
                
            if (empty($users)) {
                return json(['code' => 500, 'msg' => '未找到任何用户']);
            }
            
            $foundUserIds = array_column($users, 'id');
            
            try {
                Db::startTrans();
                
                // 使用 Hook 类删除这些用户的所有关联数据
                $hookObj = new \plugin\Autoban\Hook();
                $deletedCounts = $hookObj->deleteUserRelatedData($foundUserIds);
                
                Db::commit();
                
                // 构建返回消息
                $usernames = implode('、', array_column($users, 'username'));
                $message = "成功删除以下用户及其所有关联数据：\n";
                $message .= $usernames . "\n\n";
                $message .= "删除数据明细：\n";
                
                foreach ($deletedCounts as $table => $count) {
                    if ($count > 0) {
                        $message .= "- {$table}表: {$count}条记录\n";
                    }
                }
                
                return json(['code' => 200, 'msg' => $message]);
                
            } catch (\Exception $e) {
                Db::rollback();
                return json([
                    'code' => 500,
                    'msg' => sprintf(
                        "删除失败：%s\n文件：%s\n行号：%d",
                        $e->getMessage(),
                        $e->getFile(),
                        $e->getLine()
                    )
                ]);
            }
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => sprintf(
                    "错误信息：%s\n文件：%s\n行号：%d",
                    $e->getMessage(),
                    $e->getFile(),
                    $e->getLine()
                )
            ]);
        }
    }

    // 保存默认封禁时间
    public function saveBanTime()
    {
        $expireTime = $this->request->post('expire_time/d', 4102415999);
        
        // 验证封禁时间
        if ($expireTime <= 0) {
            $this->error('封禁时间不能为负数或0');
        }
        
        // 保存配置
        plugconf("Autoban.default_ban_expire_time", $expireTime);
        
        $this->success('保存成功');
    }
} 