<?php

namespace plugin\Fengzeroquery\controller;

use app\common\controller\BasePlugin;
use think\facade\Db;
use think\facade\View;
use think\facade\Log;

class Api extends BasePlugin
{
    protected $scene = ['user'];

    public function index()
    {
        return View::fetch();
    }

    protected function filterInput($input)
    {
        if (empty($input)) {
            return '';
        }

        $input = preg_replace('/\s+/', '', $input);

        if (!preg_match('/^[A-Za-z0-9@\.]+$/', $input)) {
            return '';
        }

        return $input;
    }

    public function getOrders()
    {
        try {
            // 验证商家用户权限
            if (!$this->user || !$this->user->id) {
                return json(['code' => 403, 'msg' => '无权限访问']);
            }

            $query = trim($this->request->get('query', ''));
            $page = (int)$this->request->get('page', 1);
            $sort = $this->request->get('sort', 'desc');
            $status = $this->request->get('status', 'all');
            $limit = 3;
            
            if (empty($query)) {
                return json(['code' => 200, 'msg' => 'success', 'data' => [
                    'list' => [],
                    'total' => 0,
                    'page' => 1,
                    'limit' => $limit
                ]]);
            }

            $query = $this->filterInput($query);
            if (empty($query)) {
                return json(['code' => 400, 'msg' => '查询内容格式不正确']);
            }

            // 先获取商家旗下的用户ID列表
            $userIds = Db::name('user')
                ->where('parent_id', $this->user->id)
                ->column('id');
            
            // 添加商家自己的ID
            $userIds[] = $this->user->id;

            // 查询订单及关联信息
            $ordersQuery = Db::name('order')
                ->alias('o')
                ->join('goods g', 'o.goods_id = g.id')
                ->leftJoin('goods p', 'g.parent_id = p.id')
                ->leftJoin('user u', 'p.user_id = u.id AND p.user_id > 0')
                ->whereIn('g.user_id', $userIds)  // 只查询商家及其下级用户的订单
                ->where(function ($query_where) use ($query, $status) {
                    $query_where->whereOr([
                        'o.trade_no' => $query,
                        'o.contact' => $query
                    ]);

                    if ($status === 'paid') {
                        $query_where->where('o.status', 1);
                    }
                })
                ->field([
                    'o.trade_no',
                    'o.status',
                    'o.contact',
                    'o.create_time',
                    'g.name as goods_name',
                    'p.id as parent_id',
                    'p.name as parent_name',
                    'u.contact_qq',
                    'u.contact_mobile',
                    'u.contact_wechat'
                ]);

            // 修改排序逻辑，默认按时间倒序
            $ordersQuery->order('o.create_time ' . ($sort === 'asc' ? 'asc' : 'desc'));

            // 获取总记录数
            $total = $ordersQuery->count();
            
            // 获取分页数据
            $orders = $ordersQuery->page($page, $limit)->select()->toArray();

            if (empty($orders)) {
                return json(['code' => 404, 'msg' => '未找到订单']);
            }

            // 安全处理
            foreach ($orders as &$order) {
                $order['goods_name'] = htmlspecialchars($order['goods_name']);
                $order['contact'] = htmlspecialchars($order['contact']);
                $order['parent_name'] = $order['parent_name'] ? htmlspecialchars($order['parent_name']) : '';
                
                // 只有当上级产品存在时才显示联系方式
                if (!empty($order['parent_name'])) {
                    $order['contact_qq'] = $order['contact_qq'] ? htmlspecialchars($order['contact_qq']) : '';
                    $order['contact_mobile'] = $order['contact_mobile'] ? htmlspecialchars($order['contact_mobile']) : '';
                    $order['contact_wechat'] = $order['contact_wechat'] ? htmlspecialchars($order['contact_wechat']) : '';
                } else {
                    $order['contact_qq'] = '';
                    $order['contact_mobile'] = '';
                    $order['contact_wechat'] = '';
                }
            }
            
            return json([
                'code' => 200, 
                'msg' => 'success', 
                'data' => [
                    'list' => $orders,
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Order query error: ' . $e->getMessage());
            return json(['code' => 500, 'msg' => '查询失败']);
        }
    }
}