<?php

namespace plugin\Tddisbug;

class Hook
{
    public function handle(&$array)
    {
        // $array[0] 是 $user，$array[1] 是 $js
        $status = intval(plugconf("Tddisbug.status") ?? 0);  // 读取状态配置

        // 当status=1时候引入js
        if ($status == 1) {
            // 获取调试禁用级别
            $debug_level = plugconf("Tddisbug.debug_level") ?? 'all';

            // 使用极端反调试保护JS文件
            $js_url = plugstatic('Tddisbug', 'anti-debug.js');

            // 设置调试级别的全局变量
            $level_script = "<script>window.debugProtectionLevel = '{$debug_level}';</script>";

            // 将级别配置和JS文件添加到页面
            $array[1][] = $level_script;
            $array[1][] = $js_url;
        }
    }
} 