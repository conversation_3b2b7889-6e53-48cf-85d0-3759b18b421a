<?php
namespace plugin\Autoban\controller;

use app\common\controller\BasePlugin;
use think\facade\View;

class Api extends BasePlugin
{
    protected $scene = [
        'admin'
    ];
    protected $noNeedLogin = [];

    public function index()
    {
        return View::fetch();
    }

    // 获取配置数据
    public function fetchData()
    {
        $params = [
            'status' => intval(plugconf("Autoban.status") ?? 0),
            'threshold' => floatval(plugconf("Autoban.threshold") ?? 50),
            'banContent' => plugconf("Autoban.banContent") ?? '因投诉率超过{threshold}%，系统自动封禁'
        ];

        $this->success('success', $params);
    }

    // 保存配置数据
    public function save()
    {
        $status = $this->request->post('status/d', 0);
        $threshold = $this->request->post('threshold/f', 50);
        $banContent = $this->request->post('banContent/s', '');

        // 验证阈值范围
        if ($threshold < 1 || $threshold > 100) {
            $this->error('阈值必须在1-100之间');
        }

        // 验证封禁内容
        if (empty($banContent)) {
            $this->error('封禁提示内容不能为空');
        }

        plugconf("Autoban.status", $status);
        plugconf("Autoban.threshold", $threshold);
        plugconf("Autoban.banContent", $banContent);

        $this->success('success');
    }
} 