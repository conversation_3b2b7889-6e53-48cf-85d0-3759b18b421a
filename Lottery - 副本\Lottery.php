<?php

namespace plugin\Lottery;

use app\common\library\Plugin;
use think\facade\Db;
use think\facade\Env;
use think\facade\Log;

class Lottery extends Plugin {

    public function install() {
        // 使用Env获取数据库前缀
        $prefix = Env::get('DB_PREFIX');

        echo "开始安装抽奖插件...\n";
        echo "正在创建数据表...\n";
        echo "数据库前缀: [" . $prefix . "]\n";

        try {
            // 创建所有必要的表
            $this->createConfigTable($prefix);
            $this->createPrizesTable($prefix);
            $this->createPrizeTypesTable($prefix);
            $this->createRecordsTable($prefix);
            $this->createMerchantLimitsTable($prefix);
            $this->createTurnoverRulesTable($prefix);
            $this->createTurnoverClaimsTable($prefix);

            // 插入默认数据
            $this->insertDefaultData($prefix);

            echo "抽奖插件安装成功！\n";
            return true;
        } catch (\Exception $e) {
            echo "安装失败: " . $e->getMessage() . "\n";
            Log::error('抽奖插件安装失败: ' . $e->getMessage());
            // 记录错误日志到文件
            file_put_contents(__DIR__ . '/install_error.log', date('Y-m-d H:i:s') . " - 安装失败: " . $e->getMessage() . "\n", FILE_APPEND);
            return false;
        }
    }

    /**
     * 创建配置表
     */
    private function createConfigTable($prefix) {
        $tableName = $prefix . 'plugin_lottery_config';

        // 检查表是否存在
        $hasTable = Db::query("SHOW TABLES LIKE '{$tableName}'");
        if (!empty($hasTable)) {
            echo "表 {$tableName} 已存在，跳过创建！\n";
            return;
        }

        $sql = <<<SQL
CREATE TABLE IF NOT EXISTS `{$tableName}` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `config_key` varchar(50) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='抽奖配置表';
SQL;

        Db::execute($sql);
        echo "配置表创建完成！\n";
    }

    /**
     * 创建奖品表
     */
    private function createPrizesTable($prefix) {
        $tableName = $prefix . 'plugin_lottery_prizes';

        // 检查表是否存在
        $hasTable = Db::query("SHOW TABLES LIKE '{$tableName}'");
        if (!empty($hasTable)) {
            echo "表 {$tableName} 已存在，跳过创建！\n";
            return;
        }

        $sql = <<<SQL
CREATE TABLE IF NOT EXISTS `{$tableName}` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '奖品名称',
  `type` varchar(50) NOT NULL COMMENT '奖品类型',
  `probability` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '中奖概率',
  `stock` int(11) NOT NULL DEFAULT '0' COMMENT '库存数量',
  `balance_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '余额奖励金额',
  `description` text COMMENT '奖品描述',
  `image` varchar(255) DEFAULT NULL COMMENT '奖品图片',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用1启用',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='抽奖奖品表';
SQL;

        Db::execute($sql);
        echo "奖品表创建完成！\n";
    }

    /**
     * 创建奖品类型表
     */
    private function createPrizeTypesTable($prefix) {
        $tableName = $prefix . 'plugin_lottery_prize_types';

        // 检查表是否存在
        $hasTable = Db::query("SHOW TABLES LIKE '{$tableName}'");
        if (!empty($hasTable)) {
            echo "表 {$tableName} 已存在，跳过创建！\n";
            return;
        }

        $sql = <<<SQL
CREATE TABLE IF NOT EXISTS `{$tableName}` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `value` varchar(50) NOT NULL COMMENT '类型值',
  `label` varchar(100) NOT NULL COMMENT '类型标签',
  `tag_type` varchar(20) DEFAULT 'info' COMMENT '标签类型',
  `tag_color` varchar(20) DEFAULT NULL COMMENT '标签颜色',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用1启用',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `is_system` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否系统类型：0否1是',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_value` (`value`),
  KEY `idx_status` (`status`),
  KEY `idx_sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='抽奖奖品类型表';
SQL;

        Db::execute($sql);
        echo "奖品类型表创建完成！\n";
    }

    /**
     * 创建抽奖记录表
     */
    private function createRecordsTable($prefix) {
        $tableName = $prefix . 'plugin_lottery_records';

        // 检查表是否存在
        $hasTable = Db::query("SHOW TABLES LIKE '{$tableName}'");
        if (!empty($hasTable)) {
            echo "表 {$tableName} 已存在，跳过创建！\n";
            return;
        }

        $sql = <<<SQL
CREATE TABLE IF NOT EXISTS `{$tableName}` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `prize_id` int(11) unsigned NOT NULL COMMENT '奖品ID',
  `prize_name` varchar(100) NOT NULL COMMENT '奖品名称',
  `prize_type` varchar(50) NOT NULL COMMENT '奖品类型',
  `balance_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '余额奖励金额',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '发货状态：0未发货1已发货',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_prize_id` (`prize_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='抽奖记录表';
SQL;

        Db::execute($sql);
        echo "抽奖记录表创建完成！\n";
    }

    /**
     * 创建商户限制表
     */
    private function createMerchantLimitsTable($prefix) {
        $tableName = $prefix . 'plugin_lottery_merchant_limits';

        // 检查表是否存在
        $hasTable = Db::query("SHOW TABLES LIKE '{$tableName}'");
        if (!empty($hasTable)) {
            echo "表 {$tableName} 已存在，跳过创建！\n";
            return;
        }

        $sql = <<<SQL
CREATE TABLE IF NOT EXISTS `{$tableName}` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `merchant_id` int(11) unsigned NOT NULL COMMENT '商户ID',
  `daily_limit` int(11) NOT NULL DEFAULT '3' COMMENT '每日抽奖次数限制',
  `used_count` int(11) NOT NULL DEFAULT '0' COMMENT '已使用次数',
  `last_reset_date` date NOT NULL COMMENT '最后重置日期',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_last_reset_date` (`last_reset_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商户抽奖限制表';
SQL;

        Db::execute($sql);
        echo "商户限制表创建完成！\n";
    }

    /**
     * 创建流水规则表
     */
    private function createTurnoverRulesTable($prefix) {
        $tableName = $prefix . 'plugin_lottery_turnover_rules';

        // 检查表是否存在
        $hasTable = Db::query("SHOW TABLES LIKE '{$tableName}'");
        if (!empty($hasTable)) {
            echo "表 {$tableName} 已存在，跳过创建！\n";
            return;
        }

        $sql = <<<SQL
CREATE TABLE IF NOT EXISTS `{$tableName}` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `turnover_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '流水金额',
  `draw_times` int(11) NOT NULL DEFAULT '1' COMMENT '获得抽奖次数',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用1启用',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort` (`sort`),
  KEY `idx_turnover_amount` (`turnover_amount`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流水抽奖规则表';
SQL;

        Db::execute($sql);
        echo "流水规则表创建完成！\n";
    }

    /**
     * 创建流水领取记录表
     */
    private function createTurnoverClaimsTable($prefix) {
        $tableName = $prefix . 'plugin_lottery_turnover_claims';

        // 检查表是否存在
        $hasTable = Db::query("SHOW TABLES LIKE '{$tableName}'");
        if (!empty($hasTable)) {
            echo "表 {$tableName} 已存在，跳过创建！\n";
            return;
        }

        $sql = <<<SQL
CREATE TABLE IF NOT EXISTS `{$tableName}` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `merchant_id` int(11) unsigned NOT NULL COMMENT '商户ID',
  `rule_id` int(11) unsigned NOT NULL COMMENT '规则ID',
  `claim_date` date NOT NULL COMMENT '领取日期',
  `draw_times_used` int(11) NOT NULL DEFAULT '0' COMMENT '已使用抽奖次数',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_merchant_rule_date` (`merchant_id`, `rule_id`, `claim_date`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_rule_id` (`rule_id`),
  KEY `idx_claim_date` (`claim_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流水抽奖领取记录表';
SQL;

        Db::execute($sql);
        echo "流水领取记录表创建完成！\n";
    }

    /**
     * 插入默认数据
     */
    private function insertDefaultData($prefix) {
        echo "正在插入默认数据...\n";

        // 插入默认配置数据
        $this->insertDefaultConfig($prefix);

        // 插入默认奖品类型
        $this->insertDefaultPrizeTypes($prefix);

        // 插入默认奖品数据
        $this->insertDefaultPrizes($prefix);

        // 插入默认流水规则
        $this->insertDefaultTurnoverRules($prefix);

        echo "默认数据插入完成！\n";
    }

    /**
     * 插入默认配置
     */
    private function insertDefaultConfig($prefix) {
        $tableName = $prefix . 'plugin_lottery_config';

        // 检查是否已有配置数据
        $count = Db::name('plugin_lottery_config')->count();
        if ($count > 0) {
            echo "配置数据已存在，跳过插入！\n";
            return;
        }

        $configs = [
            ['config_key' => 'status', 'config_value' => '1', 'description' => '抽奖活动状态'],
            ['config_key' => 'daily_limit', 'config_value' => '3', 'description' => '每日基础抽奖次数'],
            ['config_key' => 'start_hour', 'config_value' => '00:00', 'description' => '抽奖开始时间'],
            ['config_key' => 'end_hour', 'config_value' => '23:59', 'description' => '抽奖结束时间'],
            ['config_key' => 'show_probability', 'config_value' => '0', 'description' => '是否显示中奖概率']
        ];

        foreach ($configs as $config) {
            Db::name('plugin_lottery_config')->insert($config);
        }

        echo "默认配置插入完成！\n";
    }

    /**
     * 插入默认奖品类型
     */
    private function insertDefaultPrizeTypes($prefix) {
        $tableName = $prefix . 'plugin_lottery_prize_types';

        // 检查是否已有类型数据
        $count = Db::name('plugin_lottery_prize_types')->count();
        if ($count > 0) {
            echo "奖品类型数据已存在，跳过插入！\n";
            return;
        }

        $types = [
            ['value' => 'cash', 'label' => '现金', 'tag_type' => 'danger', 'tag_color' => null, 'status' => 1, 'sort' => 1],
            ['value' => 'virtual', 'label' => '虚拟', 'tag_type' => 'success', 'tag_color' => null, 'status' => 1, 'sort' => 2],
            ['value' => 'physical', 'label' => '实物', 'tag_type' => 'warning', 'tag_color' => null, 'status' => 1, 'sort' => 3]
        ];

        foreach ($types as $type) {
            Db::name('plugin_lottery_prize_types')->insert($type);
        }

        echo "默认奖品类型插入完成！\n";
    }

    /**
     * 插入默认奖品
     */
    private function insertDefaultPrizes($prefix) {
        $tableName = $prefix . 'plugin_lottery_prizes';

        // 检查是否已有奖品数据
        $count = Db::name('plugin_lottery_prizes')->count();
        if ($count > 0) {
            echo "奖品数据已存在，跳过插入！\n";
            return;
        }

        $prizes = [
            ['name' => '三等奖', 'type' => 'virtual', 'probability' => 30.00, 'stock' => 100, 'balance_amount' => 0.00, 'description' => '虚拟奖品', 'image' => '', 'status' => 1, 'sort' => 1],
            ['name' => '二等奖', 'type' => 'virtual', 'probability' => 20.00, 'stock' => 50, 'balance_amount' => 0.00, 'description' => '虚拟奖品', 'image' => '', 'status' => 1, 'sort' => 2],
            ['name' => '一等奖', 'type' => 'cash', 'probability' => 10.00, 'stock' => 20, 'balance_amount' => 10.00, 'description' => '现金奖励', 'image' => '', 'status' => 1, 'sort' => 3],
            ['name' => 'iPhone14 Pro Max', 'type' => 'physical', 'probability' => 5.00, 'stock' => 5, 'balance_amount' => 0.00, 'description' => '实物奖品', 'image' => '', 'status' => 1, 'sort' => 4],
            ['name' => '谢谢参与', 'type' => 'virtual', 'probability' => 35.00, 'stock' => 1000, 'balance_amount' => 0.00, 'description' => '谢谢参与', 'image' => '', 'status' => 1, 'sort' => 5]
        ];

        foreach ($prizes as $prize) {
            Db::name('plugin_lottery_prizes')->insert($prize);
        }

        echo "默认奖品插入完成！\n";
    }

    /**
     * 插入默认流水规则
     */
    private function insertDefaultTurnoverRules($prefix) {
        $tableName = $prefix . 'plugin_lottery_turnover_rules';

        // 检查是否已有规则数据
        $count = Db::name('plugin_lottery_turnover_rules')->count();
        if ($count > 0) {
            echo "流水规则数据已存在，跳过插入！\n";
            return;
        }

        $rules = [
            ['turnover_amount' => 100.00, 'draw_times' => 1, 'status' => 1, 'sort' => 1],
            ['turnover_amount' => 500.00, 'draw_times' => 2, 'status' => 1, 'sort' => 2],
            ['turnover_amount' => 1000.00, 'draw_times' => 3, 'status' => 1, 'sort' => 3],
            ['turnover_amount' => 2000.00, 'draw_times' => 5, 'status' => 1, 'sort' => 4],
            ['turnover_amount' => 5000.00, 'draw_times' => 10, 'status' => 1, 'sort' => 5]
        ];

        foreach ($rules as $rule) {
            Db::name('plugin_lottery_turnover_rules')->insert($rule);
        }

        echo "默认流水规则插入完成！\n";
    }

    public function uninstall() {
        // 使用Env获取数据库前缀
        $prefix = Env::get('DB_PREFIX');

        echo "开始卸载抽奖插件...\n";

        // 删除所有相关表
        $tables = [
            $prefix . 'plugin_lottery_config',
            $prefix . 'plugin_lottery_prizes',
            $prefix . 'plugin_lottery_records',
            $prefix . 'plugin_lottery_merchant_limits',
            $prefix . 'plugin_lottery_turnover_rules',
            $prefix . 'plugin_lottery_turnover_claims',
            $prefix . 'plugin_lottery_prize_types'
        ];

        try {
            foreach ($tables as $table) {
                Db::execute("DROP TABLE IF EXISTS `{$table}`");
                echo "表 {$table} 删除成功\n";
            }

            echo "抽奖插件卸载成功！\n";
            return true;
        } catch (\Exception $e) {
            echo "卸载失败: " . $e->getMessage() . "\n";
            Log::error('抽奖插件卸载失败：' . $e->getMessage());
            return false;
        }
    }
}
