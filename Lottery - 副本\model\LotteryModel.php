<?php

namespace plugin\Lottery\model;

use think\facade\Db;
use think\facade\Log;

class LotteryModel {

    /**
     * 获取配置
     */
    public static function getConfig() {
        try {
            $config = Db::name('plugin_lottery_config')->select()->toArray();

            // 设置默认值
            $defaults = [
                'status' => 1,
                'daily_limit' => 3,
                'start_hour' => '00:00',
                'end_hour' => '23:59',
                'show_probability' => 0
            ];

            $configData = $defaults;

            // 覆盖数据库中的配置并进行类型转换
            foreach ($config as $item) {
                $key = $item['config_key'];
                $value = $item['config_value'];

                // 根据配置键进行类型转换
                switch ($key) {
                    case 'status':
                    case 'daily_limit':
                    case 'show_probability':
                        $configData[$key] = intval($value);
                        break;
                    case 'start_hour':
                    case 'end_hour':
                        $configData[$key] = $value;
                        break;
                    default:
                        $configData[$key] = $value;
                        break;
                }
            }

            return $configData;
        } catch (\Exception $e) {
            Log::error('获取配置失败：' . $e->getMessage());
            return [
                'status' => 1,
                'daily_limit' => 3,
                'start_hour' => '00:00',
                'end_hour' => '23:59',
                'show_probability' => 0
            ];
        }
    }

    /**
     * 获取商户限制
     */
    public static function getMerchantLimit($merchantId) {
        try {
            $today = date('Y-m-d');
            $limit = Db::name('plugin_lottery_merchant_limits')
                ->where('merchant_id', $merchantId)
                ->find();
            
            if (!$limit || $limit['last_reset_date'] !== $today) {
                // 获取配置中的每日限制
                $config = self::getConfig();
                $dailyLimit = intval($config['daily_limit'] ?? 3);
                
                // 创建或更新限制记录
                $data = [
                    'merchant_id' => $merchantId,
                    'daily_limit' => $dailyLimit,
                    'used_count' => 0,
                    'last_reset_date' => $today,
                    'update_time' => date('Y-m-d H:i:s')
                ];
                
                if ($limit) {
                    Db::name('plugin_lottery_merchant_limits')
                        ->where('merchant_id', $merchantId)
                        ->update($data);
                } else {
                    $data['create_time'] = date('Y-m-d H:i:s');
                    Db::name('plugin_lottery_merchant_limits')->insert($data);
                }
                
                return $data;
            }
            
            return $limit;
        } catch (\Exception $e) {
            Log::error('获取商户限制失败：' . $e->getMessage());
            return [
                'merchant_id' => $merchantId,
                'daily_limit' => 3,
                'used_count' => 0,
                'last_reset_date' => date('Y-m-d')
            ];
        }
    }

    /**
     * 更新商户已使用次数
     */
    public static function updateMerchantUsedCount($merchantId) {
        try {
            $today = date('Y-m-d');
            Db::name('plugin_lottery_merchant_limits')
                ->where('merchant_id', $merchantId)
                ->where('last_reset_date', $today)
                ->inc('used_count')
                ->update();
            return true;
        } catch (\Exception $e) {
            Log::error('更新商户使用次数失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取可用奖品
     */
    public static function getAvailablePrizes() {
        try {
            return Db::name('plugin_lottery_prizes')
                ->where('status', 1)
                ->where('stock', '>', 0)
                ->order('sort asc, id asc')
                ->select()
                ->toArray();
        } catch (\Exception $e) {
            Log::error('获取可用奖品失败：' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取所有奖品
     */
    public static function getPrizes() {
        try {
            return Db::name('plugin_lottery_prizes')
                ->order('sort asc, id asc')
                ->select()
                ->toArray();
        } catch (\Exception $e) {
            Log::error('获取奖品失败：' . $e->getMessage());
            return [];
        }
    }

    /**
     * 添加抽奖记录
     */
    public static function addRecord($record) {
        try {
            $record['create_time'] = date('Y-m-d H:i:s');
            $record['update_time'] = date('Y-m-d H:i:s');
            return Db::name('plugin_lottery_records')->insert($record);
        } catch (\Exception $e) {
            Log::error('添加抽奖记录失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * 减少奖品库存
     */
    public static function decreasePrizeStock($prizeId) {
        try {
            return Db::name('plugin_lottery_prizes')
                ->where('id', $prizeId)
                ->where('stock', '>', 0)
                ->dec('stock')
                ->update();
        } catch (\Exception $e) {
            Log::error('减少奖品库存失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取抽奖记录
     */
    public static function getRecords($where = [], $page = 1, $pageSize = 20) {
        try {
            $query = Db::name('plugin_lottery_records')
                ->alias('r')
                ->leftJoin('plugin_lottery_prizes p', 'r.prize_id = p.id')
                ->field('r.*, p.name as prize_name, p.type as prize_type');
            
            if (!empty($where)) {
                $query->where($where);
            }
            
            $total = $query->count();
            $list = $query->order('r.create_time desc')
                ->page($page, $pageSize)
                ->select()
                ->toArray();
            
            return [
                'list' => $list,
                'total' => $total
            ];
        } catch (\Exception $e) {
            Log::error('获取抽奖记录失败：' . $e->getMessage());
            return [
                'list' => [],
                'total' => 0
            ];
        }
    }

    /**
     * 获取流水规则
     */
    public static function getTurnoverRules() {
        try {
            return Db::name('plugin_lottery_turnover_rules')
                ->where('status', 1)
                ->order('turnover_amount asc')
                ->select()
                ->toArray();
        } catch (\Exception $e) {
            Log::error('获取流水规则失败：' . $e->getMessage());
            return [];
        }
    }

    /**
     * 计算流水抽奖次数（基于已领取的记录）
     */
    public static function calculateTurnoverDraws($merchantId) {
        try {
            // 获取今日已领取的流水奖励记录
            $claims = self::getTurnoverClaims($merchantId);

            // 计算总的可用抽奖次数
            $totalDraws = 0;
            foreach ($claims as $claim) {
                // 获取规则信息
                $rule = Db::name('plugin_lottery_turnover_rules')
                    ->where('id', $claim['rule_id'])
                    ->find();

                if ($rule) {
                    // 计算可用次数 = 规则给予的次数 - 已使用的次数
                    $availableDraws = $rule['draw_times'] - ($claim['draw_times_used'] ?? 0);
                    $totalDraws += max(0, $availableDraws);
                }
            }

            return $totalDraws;
        } catch (\Exception $e) {
            Log::error('计算流水抽奖次数失败：' . $e->getMessage());
            return 0;
        }
    }

    /**
     * 使用流水抽奖次数
     */
    public static function useTurnoverDraw($merchantId) {
        try {
            // 获取今日已领取的流水奖励记录
            $claims = self::getTurnoverClaims($merchantId);

            // 找到第一个还有可用次数的记录
            foreach ($claims as $claim) {
                $rule = Db::name('plugin_lottery_turnover_rules')
                    ->where('id', $claim['rule_id'])
                    ->find();

                if ($rule) {
                    $usedTimes = $claim['draw_times_used'] ?? 0;
                    $availableTimes = $rule['draw_times'] - $usedTimes;

                    if ($availableTimes > 0) {
                        // 更新已使用次数
                        Db::name('plugin_lottery_turnover_claims')
                            ->where('id', $claim['id'])
                            ->inc('draw_times_used')
                            ->update();

                        return true;
                    }
                }
            }

            return false;
        } catch (\Exception $e) {
            Log::error('使用流水抽奖次数失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取奖品类型
     */
    public static function getPrizeTypes() {
        try {
            return Db::name('plugin_lottery_prize_types')
                ->where('status', 1)
                ->order('sort asc, id asc')
                ->select()
                ->toArray();
        } catch (\Exception $e) {
            Log::error('获取奖品类型失败：' . $e->getMessage());
            return [];
        }
    }

    /**
     * 根据值获取奖品类型
     */
    public static function getPrizeTypeByValue($value) {
        try {
            return Db::name('plugin_lottery_prize_types')
                ->where('value', $value)
                ->where('status', 1)
                ->find();
        } catch (\Exception $e) {
            Log::error('获取奖品类型失败：' . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取今日流水
     */
    public static function getTodayTurnover($merchantId) {
        try {
            $today = date('Y-m-d');
            return Db::name('user_analysis')
                ->where([
                    ['user_id', '=', $merchantId],
                    ['date', '=', $today]
                ])
                ->sum('total_amount') ?: 0;
        } catch (\Exception $e) {
            Log::error('获取今日流水失败：' . $e->getMessage());
            return 0;
        }
    }

    /**
     * 获取本月流水
     */
    public static function getMonthTurnover($merchantId) {
        try {
            $monthStart = date('Y-m-01');
            $today = date('Y-m-d');
            return Db::name('user_analysis')
                ->where([
                    ['user_id', '=', $merchantId],
                    ['date', '>=', $monthStart],
                    ['date', '<=', $today]
                ])
                ->sum('total_amount') ?: 0;
        } catch (\Exception $e) {
            Log::error('获取本月流水失败：' . $e->getMessage());
            return 0;
        }
    }

    /**
     * 获取流水领取记录
     */
    public static function getTurnoverClaims($merchantId) {
        try {
            return Db::name('plugin_lottery_turnover_claims')
                ->where('merchant_id', $merchantId)
                ->where('claim_date', date('Y-m-d'))
                ->select()
                ->toArray();
        } catch (\Exception $e) {
            Log::error('获取流水领取记录失败：' . $e->getMessage());
            return [];
        }
    }
}
