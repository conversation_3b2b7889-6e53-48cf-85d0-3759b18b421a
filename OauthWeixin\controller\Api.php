<?php

namespace plugin\OauthWeixin\controller;

use app\common\controller\BasePlugin;
use app\common\library\WxTool;
use app\common\library\RandomExtend;
use app\common\model\User as UserModel;

class Api extends BasePlugin {

    protected $scene = [
        'user'
    ];
    protected $noNeedLogin = [
        'start',
        'login',
        'query',
    ];

    public function start() {
        if (plugconf("OauthWeixin.status") == 1) {
            $scene = input('scene/s', '');
            $is_mobile = request()->isMobile();
            $platform_code = RandomExtend::random(10, 3);

            $url = (string) plugin_url('OauthWeixin/Api/login', ['scene' => $scene, 'platform_code' => $platform_code, 'is_mobile' => $is_mobile ? 1 : 0], false, true);
            $this->success('success', [
                "redirect_url" => $url,
                "platform_code" => $platform_code,
                    ], 1);
        } else {
            return $this->error("抱歉！未开启商户微信登录", null, 0);
        }
    }

    public function login() {
        $is_mobile = input('is_mobile/d', 0);
        $scene = input('scene/s', '');
        $platform_code = input('platform_code/s', '');

        $wxTool = new WxTool(['appid' => plugconf('OauthWeixin.appid'), "secret" => plugconf('OauthWeixin.app_secret')]);
        $openid = $wxTool->getOpenid();
        if ($openid == "") {
            $this->error("抱歉！获取openid失败！", null, 500, 'html');
        }

        cache("oauth_" . $platform_code, ['from' => 'weixin', 'openid' => $openid], 600);

        if ($scene == "bind" && UserModel::where(['weixin_openid' => $openid])->find()) {
            return $this->success("当前微信已绑定其他账号", null, 500, 'html');
        }


        if ($scene == "login") {
            if ($is_mobile == 1) {
                return redirect("/merchant/login?from=weixin&platform_code=" . $platform_code);
            } else {
                return $this->success("授权成功", null, 200, 'html');
            }
        } elseif ($scene == "bind") {
            if ($is_mobile == 1) {
                return redirect("/merchant/user/setting?from=weixin&platform_code=" . $platform_code);
            } else {
                return $this->success("授权成功", null, 200, 'html');
            }
        }
    }

    public function query() {
        $platform_code = input('platform_code/s', '');
        $scene = input('scene/s', '');
        $cache = cache("oauth_" . $platform_code);
        if ($cache != null && isset($cache['openid'])) {
            if ($scene == "bind") {
                $user = $this->user->getUser();
                $user->weixin_openid = $cache['openid'];
                $user->save();
                $this->success("绑定成功", null, 1);
            } elseif ($scene == "login") {
                $this->success("授权成功", null, 1);
            }
        } else {
            $this->error("未登录", null, 0);
        }
    }

    public function unbind() {
        $user = $this->user->getUser();
        $user->weixin_openid = "";
        $user->save();
        $this->success("解绑成功", null, 1);
    }
}
