<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>飞鸟VPN - 永远能连上的海外加速器</title>
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="css/element-plus.css">
    <!-- Font Awesome -->
    <link href="css/font-awesome.css" rel="stylesheet">
    <!-- Local Icons -->
    <link href="css/local-icons.css" rel="stylesheet">
    <meta name="description" content="专业的VPN服务提供商，提供军用级加密、全球服务器、零日志政策，保护您的网络隐私和安全。">
    <meta name="keywords" content="VPN, 网络安全, 隐私保护, 翻墙, 代理服务器">
    <meta property="og:title" content="飞鸟VPN - 永远能连上的海外加速器">
    <meta property="og:description" content="专业的VPN服务提供商，保护您的网络隐私和安全">
    <meta property="og:type" content="website">
    <meta name="twitter:card" content="summary_large_image">
    <style>
        :root {
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --primary-light: #8b5cf6;
            --secondary-color: #06b6d4;
            --accent-color: #f59e0b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --text-light: #ffffff;
            --text-dark: #1f2937;
            --text-gray: #6b7280;
            --text-muted: #9ca3af;
            --bg-light: #f9fafb;
            --bg-dark: #111827;
            --bg-card: #ffffff;
            --bg-glass: rgba(255, 255, 255, 0.1);
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --border-radius-sm: 8px;
            --border-radius: 12px;
            --border-radius-lg: 16px;
            --border-radius-xl: 24px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-fast: all 0.15s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            overflow-x: hidden;
            scroll-behavior: smooth;
            line-height: 1.6;
            color: var(--text-dark);
            background: var(--bg-light);
        }

        /* 现代化滚动条 */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: transparent;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-radius: 3px;
            transition: var(--transition);
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
            transform: scaleY(1.1);
        }

        .hero-section {
            min-height: 100vh;
            background: linear-gradient(135deg,
                #0f0f23 0%,
                #1a1a2e 25%,
                #16213e 50%,
                #1a1a2e 75%,
                #0f0f23 100%);
            background-size: 400% 400%;
            animation: gradient-shift 12s ease infinite;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 30%, rgba(99, 102, 241, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(6, 182, 212, 0.08) 0%, transparent 50%);
            z-index: 1;
        }

        .gradient-blob {
            position: absolute;
            border-radius: 50%;
            filter: blur(60px);
            opacity: 0.6;
            animation: blob-float 15s ease-in-out infinite;
        }

        .blob-1 {
            width: 500px;
            height: 500px;
            background: linear-gradient(45deg, var(--primary-color), var(--primary-light));
            top: 5%;
            right: 5%;
        }

        .blob-2 {
            width: 350px;
            height: 350px;
            background: linear-gradient(45deg, var(--secondary-color), var(--primary-color));
            top: 35%;
            right: 25%;
            animation-delay: -5s;
        }

        .blob-3 {
            width: 250px;
            height: 250px;
            background: linear-gradient(45deg, var(--accent-color), var(--success-color));
            bottom: 15%;
            right: 15%;
            animation-delay: -10s;
        }

        .blob-4 {
            width: 180px;
            height: 180px;
            background: linear-gradient(45deg, var(--primary-light), var(--secondary-color));
            top: 60%;
            left: 10%;
            animation-delay: -7s;
        }

        @keyframes blob-float {
            0%, 100% {
                transform: translate(0, 0) scale(1) rotate(0deg);
                opacity: 0.6;
            }
            25% {
                transform: translate(40px, -40px) scale(1.15) rotate(90deg);
                opacity: 0.4;
            }
            50% {
                transform: translate(-30px, 30px) scale(0.85) rotate(180deg);
                opacity: 0.8;
            }
            75% {
                transform: translate(30px, 40px) scale(1.05) rotate(270deg);
                opacity: 0.5;
            }
        }

        @keyframes gradient-shift {
            0%, 100% { background-position: 0% 50%; }
            25% { background-position: 100% 25%; }
            50% { background-position: 100% 50%; }
            75% { background-position: 0% 75%; }
        }

        @keyframes float-up {
            from {
                opacity: 0;
                transform: translateY(40px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes slide-in-left {
            from {
                opacity: 0;
                transform: translateX(-60px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateX(0) scale(1);
            }
        }

        @keyframes slide-in-right {
            from {
                opacity: 0;
                transform: translateX(60px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateX(0) scale(1);
            }
        }

        @keyframes glow {
            0%, 100% {
                box-shadow: 0 0 30px rgba(99, 102, 241, 0.4),
                           0 0 60px rgba(99, 102, 241, 0.2);
            }
            50% {
                box-shadow: 0 0 50px rgba(99, 102, 241, 0.6),
                           0 0 100px rgba(99, 102, 241, 0.3);
            }
        }

        @keyframes pulse-glow {
            0%, 100% {
                box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
                transform: scale(1);
            }
            50% {
                box-shadow: 0 0 40px rgba(99, 102, 241, 0.6);
                transform: scale(1.05);
            }
        }

        /* 现代化卡片悬停效果 */
        @keyframes card-hover {
            0% {
                transform: translateY(0) scale(1);
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            }
            100% {
                transform: translateY(-8px) scale(1.02);
                box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            }
        }

        /* 现代化工具类 */
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px) saturate(180%);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .gradient-border {
            position: relative;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            padding: 2px;
            border-radius: var(--border-radius-lg);
        }

        .gradient-border::before {
            content: '';
            position: absolute;
            inset: 2px;
            background: var(--bg-card);
            border-radius: calc(var(--border-radius-lg) - 2px);
            z-index: -1;
        }

        .text-gradient {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hover-lift {
            transition: var(--transition);
        }

        .hover-lift:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: var(--shadow-xl);
        }

        /* 渐变文字动画 */
        @keyframes gradient-text {
            0%, 100% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
        }

        /* 光线扫描效果 */
        @keyframes shine {
            0% {
                transform: translateX(-100%);
            }
            100% {
                transform: translateX(100%);
            }
        }

        /* 脉冲呼吸效果 */
        @keyframes breathe {
            0%, 100% {
                transform: scale(1);
                opacity: 0.8;
            }
            50% {
                transform: scale(1.05);
                opacity: 1;
            }
        }

        /* 现代化加载动画 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg,
                var(--bg-dark) 0%,
                #1f2937 25%,
                #374151 50%,
                #1f2937 75%,
                var(--bg-dark) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1),
                       visibility 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(20px);
        }

        .loading-overlay.hidden {
            opacity: 0;
            visibility: hidden;
        }

        .loading-spinner {
            width: 80px;
            height: 80px;
            border: 3px solid rgba(99, 102, 241, 0.2);
            border-top: 3px solid var(--primary-color);
            border-right: 3px solid var(--primary-light);
            border-radius: 50%;
            animation: spin 1.2s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite;
            position: relative;
        }

        .loading-spinner::before {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            border: 2px solid transparent;
            border-top: 2px solid var(--secondary-color);
            border-radius: 50%;
            animation: spin 2s linear infinite reverse;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            color: var(--text-light);
            font-size: 1.4rem;
            font-weight: 700;
            margin-top: 30px;
            animation: pulse-text 2s ease infinite;
            background: linear-gradient(45deg, var(--primary-color), var(--primary-light));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        @keyframes pulse-text {
            0%, 100% {
                opacity: 0.8;
                transform: scale(1);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
            }
        }

        /* 价格卡片样式 */
        .pricing-card {
            transform-style: preserve-3d;
        }

        .pricing-card.featured {
            background: rgba(255, 255, 255, 0.98) !important;
            box-shadow: 0 25px 50px rgba(79, 195, 247, 0.2),
                        0 0 0 1px rgba(79, 195, 247, 0.1) !important;
        }

        .pricing-card:hover {
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15) !important;
        }

        .pricing-card.featured:hover {
            box-shadow: 0 35px 70px rgba(79, 195, 247, 0.3),
                        0 0 0 1px rgba(79, 195, 247, 0.2) !important;
        }

        /* 价格数字动画 */
        @keyframes price-glow {
            0%, 100% {
                text-shadow: 0 0 20px rgba(79, 195, 247, 0.3);
            }
            50% {
                text-shadow: 0 0 30px rgba(79, 195, 247, 0.6);
            }
        }

        .pricing-card.featured .price-number {
            animation: price-glow 2s ease infinite;
        }

        /* 背景浮动动画 */
        @keyframes float-background {
            0%, 100% {
                transform: translateY(0px) scale(1);
                opacity: 0.1;
            }
            50% {
                transform: translateY(-20px) scale(1.1);
                opacity: 0.2;
            }
        }

        /* 价格方案区域的特殊样式 */
        #pricing {
            background-attachment: fixed;
        }

        #pricing::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
            animation: gradient-shift 10s ease infinite;
        }

        /* 服务器节点动画 */
        @keyframes server-pulse {
            0%, 100% {
                transform: scale(1);
                opacity: 0.6;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.3;
            }
        }

        /* 服务器悬停效果 */
        .server-node:hover .server-tooltip {
            opacity: 1 !important;
            visibility: visible !important;
            transform: translateX(-50%) translateY(-5px) !important;
        }

        .server-node:hover .server-core {
            transform: scale(1.3) !important;
            box-shadow:
                0 0 30px rgba(79, 195, 247, 0.8),
                0 4px 15px rgba(0, 0, 0, 0.3) !important;
        }

        /* 统计卡片动画 */
        .stats-card {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .stats-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(79, 195, 247, 0.15) !important;
        }

        /* 地图容器特效 */
        .world-map-container {
            background:
                radial-gradient(circle at 20% 30%, rgba(79, 195, 247, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(41, 182, 246, 0.03) 0%, transparent 50%),
                linear-gradient(135deg, #ffffff 0%, #f7fafc 100%);
        }

        .world-map-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 30% 20%, rgba(79, 195, 247, 0.02) 0%, transparent 40%),
                radial-gradient(circle at 70% 80%, rgba(41, 182, 246, 0.02) 0%, transparent 40%);
            animation: map-glow 8s ease-in-out infinite;
            z-index: 0;
        }

        @keyframes map-glow {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }

        .navbar {
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            background: rgba(15, 15, 35, 0.85);
            backdrop-filter: blur(25px) saturate(180%);
            border-bottom: 1px solid rgba(99, 102, 241, 0.2);
            transition: var(--transition);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .navbar:hover {
            background: rgba(15, 15, 35, 0.95);
            border-bottom: 1px solid rgba(99, 102, 241, 0.4);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        }

        .hero-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
            min-height: 100vh;
            padding: 100px 20px 50px;
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
            z-index: 2;
        }

        .hero-content {
            color: #fff;
        }

        .brand-logo {
            display: flex;
            align-items: center;
            font-size: 2rem;
            font-weight: 800;
            margin-bottom: 40px;
            color: var(--text-light);
            animation: float-up 1.2s ease;
            letter-spacing: -0.02em;
        }

        .brand-logo i {
            margin-right: 12px;
            color: var(--primary-color);
            font-size: 2.4rem;
            animation: pulse-glow 3s ease infinite;
            filter: drop-shadow(0 0 15px rgba(99, 102, 241, 0.6));
            transition: var(--transition);
        }

        .brand-logo:hover i {
            transform: scale(1.1) rotate(5deg);
            color: var(--primary-light);
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 900;
            line-height: 1.1;
            margin-bottom: 50px;
            color: var(--text-light);
            background: linear-gradient(135deg,
                var(--text-light) 0%,
                var(--primary-color) 25%,
                var(--primary-light) 50%,
                var(--secondary-color) 75%,
                var(--text-light) 100%);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradient-shift 4s ease infinite, slide-in-left 1.2s ease;
            text-shadow: 0 0 40px rgba(255, 255, 255, 0.2);
            letter-spacing: -0.03em;
        }

        .feature-list {
            margin-bottom: 40px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            font-size: 1.1rem;
            color: rgba(255, 255, 255, 0.9);
        }

        .feature-icon {
            width: 55px;
            height: 55px;
            background: linear-gradient(135deg,
                var(--primary-color) 0%,
                var(--primary-light) 50%,
                var(--secondary-color) 100%);
            background-size: 200% 200%;
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            color: var(--text-light);
            font-size: 1.3rem;
            animation: gradient-shift 4s ease infinite, pulse-glow 3s ease infinite;
            box-shadow: var(--shadow-lg), 0 0 20px rgba(99, 102, 241, 0.3);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .feature-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: var(--transition);
        }

        .feature-icon:hover {
            transform: scale(1.15) rotate(5deg);
            box-shadow: var(--shadow-xl), 0 0 30px rgba(99, 102, 241, 0.5);
        }

        .feature-icon:hover::before {
            left: 100%;
        }

        .device-showcase {
            position: relative;
            height: 500px;
        }

        .laptop-device {
            position: absolute;
            top: 0;
            right: 0;
            width: 450px;
            height: 280px;
            background: linear-gradient(145deg, #f8fafc, #e2e8f0);
            border-radius: var(--border-radius-lg) var(--border-radius-lg) var(--border-radius-sm) var(--border-radius-sm);
            box-shadow: var(--shadow-2xl),
                        0 0 0 1px rgba(255, 255, 255, 0.8),
                        inset 0 1px 0 rgba(255, 255, 255, 0.9);
            z-index: 2;
            animation: slide-in-right 1.4s cubic-bezier(0.4, 0, 0.2, 1);
            transition: var(--transition);
            backdrop-filter: blur(10px);
        }

        .laptop-device:hover {
            transform: translateY(-8px) scale(1.03);
            box-shadow: var(--shadow-2xl),
                        0 40px 80px rgba(0, 0, 0, 0.15),
                        0 0 0 1px rgba(255, 255, 255, 0.9),
                        inset 0 1px 0 rgba(255, 255, 255, 1);
        }

        .laptop-screen {
            width: 100%;
            height: 85%;
            background: #fff;
            border-radius: 10px 10px 0 0;
            border: 8px solid #333;
            overflow: hidden;
        }

        .browser-bar {
            height: 30px;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            padding: 0 10px;
            border-bottom: 1px solid #ddd;
        }

        .browser-buttons {
            display: flex;
            gap: 5px;
            margin-right: 15px;
        }

        .browser-buttons span {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .btn-close { background: #ff5f57; }
        .btn-minimize { background: #ffbd2e; }
        .btn-maximize { background: #28ca42; }

        .address-bar {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 15px;
            padding: 5px 15px;
            font-size: 12px;
            color: #666;
            flex: 1;
        }

        .screen-content {
            padding: 40px 20px;
            text-align: center;
        }

        .google-logo {
            font-size: 48px;
            font-weight: 400;
            color: #4285f4;
            margin-bottom: 20px;
        }

        .search-bar {
            width: 80%;
            height: 35px;
            border: 1px solid #ddd;
            border-radius: 25px;
            margin: 0 auto 20px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .mobile-device {
            position: absolute;
            bottom: 50px;
            left: 50px;
            width: 200px;
            height: 360px;
            background: linear-gradient(145deg, #1f2937, #111827);
            border-radius: var(--border-radius-xl);
            padding: 10px;
            box-shadow: var(--shadow-2xl),
                        0 0 0 2px rgba(99, 102, 241, 0.2),
                        inset 0 1px 0 rgba(255, 255, 255, 0.1);
            z-index: 3;
            animation: slide-in-left 1.6s cubic-bezier(0.4, 0, 0.2, 1);
            transition: var(--transition);
            backdrop-filter: blur(10px);
        }

        .mobile-device:hover {
            transform: translateY(-5px) scale(1.06);
            box-shadow: var(--shadow-2xl),
                        0 30px 60px rgba(0, 0, 0, 0.3),
                        0 0 0 2px rgba(99, 102, 241, 0.4),
                        inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .mobile-screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 20px;
            overflow: hidden;
        }

        .mobile-header {
            height: 25px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 15px;
            font-size: 10px;
            font-weight: 600;
        }

        .app-interface {
            padding: 20px 15px;
        }

        .app-title {
            font-size: 16px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 20px;
            color: #333;
        }

        .connection-status {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            font-size: 12px;
            color: #28a745;
            font-weight: 600;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
            background: #28a745;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7),
                           0 0 10px rgba(40, 167, 69, 0.3);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(40, 167, 69, 0),
                           0 0 20px rgba(40, 167, 69, 0.1);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(40, 167, 69, 0),
                           0 0 10px rgba(40, 167, 69, 0.3);
            }
        }

        .server-list {
            font-size: 11px;
        }

        .server-item {
            padding: 8px 10px;
            margin-bottom: 5px;
            border-radius: 8px;
            background: #f8f9fa;
            color: #666;
        }

        .server-item.active {
            background: #e3f2fd;
            color: #1976d2;
            font-weight: 600;
        }



        /* 现代化响应式设计 */
        @media (max-width: 1024px) {
            .hero-title {
                font-size: 3rem;
            }

            .laptop-device {
                width: 380px;
                height: 240px;
            }

            .mobile-device {
                width: 160px;
                height: 300px;
            }
        }

        @media (max-width: 768px) {
            .hero-container {
                grid-template-columns: 1fr;
                gap: 50px;
                text-align: center;
                padding: 100px 20px 60px;
            }

            .hero-title {
                font-size: 2.5rem;
                margin-bottom: 40px;
            }

            .brand-logo {
                font-size: 1.8rem;
                margin-bottom: 30px;
            }

            .device-showcase {
                height: 350px;
                order: -1;
            }

            .laptop-device {
                width: 320px;
                height: 200px;
                position: relative;
                margin: 0 auto;
                top: 20px;
            }

            .mobile-device {
                width: 140px;
                height: 260px;
                position: absolute;
                bottom: 30px;
                left: 30px;
            }

            .feature-item {
                font-size: 1rem;
                margin-bottom: 25px;
            }

            .feature-icon {
                width: 50px;
                height: 50px;
                font-size: 1.2rem;
            }
        }

        @media (max-width: 480px) {
            .hero-title {
                font-size: 2rem;
                line-height: 1.2;
            }

            .brand-logo {
                font-size: 1.6rem;
            }

            .laptop-device {
                width: 280px;
                height: 170px;
            }

            .mobile-device {
                width: 120px;
                height: 220px;
                bottom: 20px;
                left: 20px;
            }

            .navbar {
                padding: 0 15px;
            }

            h2 {
                font-size: 2.2rem !important;
            }

            .loading-text {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <!-- SVG图标定义 -->
    <svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
        <!-- 火箭图标 -->
        <symbol id="rocket" viewBox="0 0 512 512">
            <path d="M156.6 384.9L125.7 354c-8.5-8.5-11.5-20.8-7.7-32.2c3-8.9 7-20.5 11.8-33.8L24 288c-8.6 0-16.6-4.6-20.9-12.1s-4.2-16.7 .2-24.1l52.5-88.5c13-21.9 36.5-35.3 61.9-35.3l82.3 0c2.4-4 4.8-7.7 7.2-11.3C289.1-4.1 411.1-8.1 483.9 5.3c11.6 2.1 20.6 11.2 22.8 22.8c13.4 72.9 9.3 194.8-111.4 277.7c-3.5 2.4-7.3 4.8-11.3 7.2v82.3c0 25.4-13.4 49-35.3 61.9l-88.5 52.5c-7.4 4.4-16.6 4.7-24.1 .2s-12.1-12.2-12.1-20.9V380.8c-14.1 4.9-26.4 8.9-35.7 11.9c-11.2 3.6-23.4 .5-31.8-7.8zM384 168a40 40 0 1 0 0-80 40 40 0 1 0 0 80z"/>
        </symbol>

        <!-- 盾牌图标 -->
        <symbol id="shield" viewBox="0 0 512 512">
            <path d="M256 0c4.6 0 9.2 1 13.4 2.9L457.7 82.8c22 9.3 38.4 31 38.3 57.2c-.5 99.2-41.3 280.7-213.6 363.2c-16.7 8-36.1 8-52.8 0C57.3 420.7 16.5 239.2 16 140c-.1-26.2 16.3-47.9 38.3-57.2L242.7 2.9C246.8 1 251.4 0 256 0zm0 66.8V444.8C394 378 431.1 230.1 432 141.4L256 66.8l0 0z"/>
        </symbol>

        <!-- 闪电图标 -->
        <symbol id="bolt" viewBox="0 0 448 512">
            <path d="M349.4 44.6c5.9-13.7 1.5-29.7-10.6-38.5s-28.6-8-39.9 1.8l-256 224c-10 8.8-13.6 22.9-8.9 35.3S50.7 288 64 288H175.5L98.6 467.4c-5.9 13.7-1.5 29.7 10.6 38.5s28.6 8 39.9-1.8l256-224c10-8.8 13.6-22.9 8.9-35.3s-16.6-20.7-30-20.7H272.5L349.4 44.6z"/>
        </symbol>

        <!-- 锁图标 -->
        <symbol id="lock" viewBox="0 0 448 512">
            <path d="M144 144v48H304V144c0-44.2-35.8-80-80-80s-80 35.8-80 80zM80 192V144C80 64.5 144.5 0 224 0s144 64.5 144 144v48h16c35.3 0 64 28.7 64 64V448c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64V256c0-35.3 28.7-64 64-64H80z"/>
        </symbol>

        <!-- 眼睛斜杠图标 -->
        <symbol id="eye-slash" viewBox="0 0 640 512">
            <path d="M38.8 5.1C28.4-3.1 13.3-1.2 5.1 9.2S-1.2 34.7 9.2 42.9l592 464c10.4 8.2 25.5 6.3 33.7-4.1s6.3-25.5-4.1-33.7L525.6 386.7c39.6-40.6 66.4-86.1 79.9-118.4c3.3-7.9 3.3-16.7 0-24.6c-14.9-35.7-46.2-87.7-93-131.1C465.5 68.8 400.8 32 320 32c-68.2 0-125 26.3-169.3 60.8L38.8 5.1zM223.1 149.5C248.6 126.2 282.7 112 320 112c79.5 0 144 64.5 144 144c0 24.9-6.3 48.3-17.4 68.7L408 294.5c8.4-19.3 10.6-41.4 4.8-63.3c-11.1-41.5-47.8-69.4-88.6-71.1c-5.8-.2-9.2 6.1-7.4 11.7c2.1 6.4 3.3 13.2 3.3 20.3c0 10.2-2.4 19.8-6.6 28.3l-90.3-70.8zM373 389.9c-16.4 6.5-34.3 10.1-53 10.1c-79.5 0-144-64.5-144-144c0-6.9 .5-13.6 1.4-20.2L83.1 161.5C60.3 191.2 44 220.8 34.5 243.7c-3.3 7.9-3.3 16.7 0 24.6c14.9 35.7 46.2 87.7 93 131.1C174.5 443.2 239.2 480 320 480c47.8 0 89.9-12.9 126.2-32.5L373 389.9z"/>
        </symbol>

        <!-- 地球图标 -->
        <symbol id="globe" viewBox="0 0 512 512">
            <path d="M352 256c0 22.2-1.2 43.6-3.3 64H163.3c-2.2-20.4-3.3-41.8-3.3-64s1.2-43.6 3.3-64H348.7c2.2 20.4 3.3 41.8 3.3 64zm28.8-64H503.9c5.3 20.5 8.1 41.9 8.1 64s-2.8 43.5-8.1 64H380.8c2.1-20.6 3.2-42 3.2-64s-1.1-43.4-3.2-64zm112.6-32H376.7c-10-63.9-29.8-117.4-55.3-151.6c78.3 20.7 142 77.5 171.9 151.6zm-149.1 0H167.7c6.1-36.4 15.5-68.6 27-94.7c10.5-23.6 22.2-40.7 33.5-51.5C239.4 3.2 248.7 0 256 0s16.6 3.2 27.8 13.8c11.3 10.8 23 27.9 33.5 51.5c11.6 26 20.9 58.2 27 94.7zm-209 0H8.1C38 85.6 101.7 28.8 180 8.1C154.5 42.3 134.7 95.8 124.7 160h0zM8.1 192H131.2c-2.1 20.6-3.2 42-3.2 64s1.1 43.4 3.2 64H8.1C2.8 299.5 0 278.1 0 256s2.8-43.5 8.1-64zM167.7 352c-6.1 36.4-15.5 68.6-27 94.7c-10.5 23.6-22.2 40.7-33.5 51.5C96 509.8 86.7 512 79.4 512s-16.6-2.2-27.8-12.8c-11.3-10.8-23-27.9-33.5-51.5c-11.6-26-20.9-58.2-27-94.7h209zm149.1 0c10 63.9 29.8 117.4 55.3 151.6C293.8 483.9 230.1 427.1 200.2 352H316.8zm112.6 32H503.9c-30 74.1-93.6 130.9-171.9 151.6c25.5-34.2 45.2-87.7 55.3-151.6z"/>
        </symbol>

        <!-- 手机图标 -->
        <symbol id="mobile" viewBox="0 0 384 512">
            <path d="M16 64C16 28.7 44.7 0 80 0H304c35.3 0 64 28.7 64 64V448c0 35.3-28.7 64-64 64H80c-35.3 0-64-28.7-64-64V64zM144 448c0 8.8 7.2 16 16 16h64c8.8 0 16-7.2 16-16s-7.2-16-16-16H160c-8.8 0-16 7.2-16 16zM304 64H80V384H304V64z"/>
        </symbol>

        <!-- 耳机图标 -->
        <symbol id="headset" viewBox="0 0 512 512">
            <path d="M256 48C141.1 48 48 141.1 48 256v40c0 13.3-10.7 24-24 24s-24-10.7-24-24V256C0 114.6 114.6 0 256 0S512 114.6 512 256V400.1c0 48.6-39.4 88-88.1 88L313.6 488c-8.3 14.3-23.8 24-41.6 24H240c-26.5 0-48-21.5-48-48s21.5-48 48-48h32c17.8 0 33.3 9.7 41.6 24l110.4 .1c22.1 0 40-17.9 40-40V256C464 141.1 370.9 48 256 48zM128 208c0-8.8 7.2-16 16-16h64c8.8 0 16 7.2 16 16v96c0 8.8-7.2 16-16 16H144c-8.8 0-16-7.2-16-16V208zm256 0c0-8.8 7.2-16 16-16h64c8.8 0 16 7.2 16 16v96c0 8.8-7.2 16-16 16H400c-8.8 0-16-7.2-16-16V208z"/>
        </symbol>

        <!-- Windows图标 -->
        <symbol id="windows" viewBox="0 0 448 512">
            <path d="M0 93.7l183.6-25.3v177.4H0V93.7zm0 324.6l183.6 25.3V268.4H0v149.9zm203.8 28L448 480V268.4H203.8v177.9zm0-380.6v180.1H448V32L203.8 65.7z"/>
        </symbol>

        <!-- 检查图标 -->
        <symbol id="check" viewBox="0 0 448 512">
            <path d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z"/>
        </symbol>

        <!-- 服务器图标 -->
        <symbol id="server" viewBox="0 0 512 512">
            <path d="M64 32C28.7 32 0 60.7 0 96v64c0 35.3 28.7 64 64 64H448c35.3 0 64-28.7 64-64V96c0-35.3-28.7-64-64-64H64zm280 72a24 24 0 1 1 0 48 24 24 0 1 1 0-48zm48 24a24 24 0 1 1 48 0 24 24 0 1 1 -48 0zM64 288c-35.3 0-64 28.7-64 64v64c0 35.3 28.7 64 64 64H448c35.3 0 64-28.7 64-64V352c0-35.3-28.7-64-64-64H64zm280 72a24 24 0 1 1 0 48 24 24 0 1 1 0-48zm48 24a24 24 0 1 1 48 0 24 24 0 1 1 -48 0z"/>
        </symbol>

        <!-- 地球美洲图标 -->
        <symbol id="globe-americas" viewBox="0 0 512 512">
            <path d="M57.7 193l9.4 16.4c8.3 14.5 21.9 25.2 38 29.8L163 255.7c17.2 4.9 29 20.6 29 38.5v39.9c0 11 6.2 21 16 25.9s16 14.9 16 25.9v39c0 15.6 14.9 26.9 29.9 22.6c16.1-4.6 28.6-17.5 32.7-33.8l2.8-11.2c4.2-16.9 15.2-31.4 30.3-40l8.1-4.6c15-8.5 24.2-24.5 24.2-41.7v-8.3c0-12.7-5.1-24.9-14.1-33.9l-3.9-3.9c-9-9-21.2-14.1-33.9-14.1H257c-11.1 0-22.1-2.9-31.8-8.4l-34.5-19.7c-4.3-2.5-7.6-6.5-9.2-11.2c-3.2-9.6 1.1-20 10.2-24.5l5.9-3c6.6-3.3 14.3-3.9 21.3-1.5l23.2 7.7c8.2 2.7 17.2-.4 21.9-7.5c4.7-7 4.2-16.3-1.2-22.8l-13.6-16.3c-10-12-9.9-29.5 .3-41.3l15.7-18.3c8.8-10.3 10.2-25 3.5-36.7l-2.4-4.2c-3.5-.2-6.9-.3-10.4-.3C163.1 48 84.4 108.9 57.7 193zM464 256c0-36.8-9.6-71.4-26.4-101.5L412 164.8c-15.7 6.3-23.8 23.8-18.5 39.8l16.9 50.7c3.5 10.4 12 18.3 22.6 20.9l29.1 7.3c1.2-9 1.8-18.2 1.8-27.5zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256z"/>
        </symbol>

        <!-- 速度计图标 -->
        <symbol id="tachometer" viewBox="0 0 576 512">
            <path d="M288 32C128.9 32 0 160.9 0 320c0 52.8 14.3 102.3 39.1 144.8c5.6 9.6 16.3 15.2 27.4 15.2h443c11.1 0 21.8-5.6 27.4-15.2C561.7 422.3 576 372.8 576 320c0-159.1-128.9-288-288-288zM288 368c-26.5 0-48-21.5-48-48s21.5-48 48-48s48 21.5 48 48s-21.5 48-48 48zm-48-240a48 48 0 1 1 0 96 48 48 0 1 1 0-96zm128 48a48 48 0 1 1 96 0 48 48 0 1 1 -96 0zM96 208a48 48 0 1 1 96 0 48 48 0 1 1 -96 0zm336 48a48 48 0 1 1 0 96 48 48 0 1 1 0-96z"/>
        </symbol>

        <!-- 信封图标 -->
        <symbol id="envelope" viewBox="0 0 512 512">
            <path d="M48 64C21.5 64 0 85.5 0 112c0 15.1 7.1 29.3 19.2 38.4L236.8 313.6c11.4 8.5 27 8.5 38.4 0L492.8 150.4c12.1-9.1 19.2-23.3 19.2-38.4c0-26.5-21.5-48-48-48H48zM0 176V384c0 35.3 28.7 64 64 64H448c35.3 0 64-28.7 64-64V176L294.4 339.2c-22.8 17.1-54 17.1-76.8 0L0 176z"/>
        </symbol>

        <!-- 电话图标 -->
        <symbol id="phone" viewBox="0 0 512 512">
            <path d="M164.9 24.6c-7.7-18.6-28-28.5-47.4-23.2l-88 24C12.1 30.2 0 46 0 64C0 311.4 200.6 512 448 512c18 0 33.8-12.1 38.6-29.5l24-88c5.3-19.4-4.6-39.7-23.2-47.4l-96-40c-16.3-6.8-35.2-2.1-46.3 11.6L304.7 368C234.3 334.7 177.3 277.7 144 207.3L193.3 167c13.7-11.2 18.4-30 11.6-46.3l-40-96z"/>
        </symbol>

        <!-- 时钟图标 -->
        <symbol id="clock" viewBox="0 0 512 512">
            <path d="M256 0a256 256 0 1 1 0 512A256 256 0 1 1 256 0zM232 120V256c0 8 4 15.5 10.7 20l96 64c11 7.4 25.9 4.4 33.3-6.7s4.4-25.9-6.7-33.3L280 243.2V120c0-13.3-10.7-24-24-24s-24 10.7-24 24z"/>
        </symbol>

        <!-- 鸽子图标 -->
        <symbol id="dove" viewBox="0 0 512 512">
            <path d="M160 120c0-35.3 28.7-64 64-64s64 28.7 64 64v8c0 17.7-14.3 32-32 32s-32-14.3-32-32v-8c0-8.8-7.2-16-16-16s-16 7.2-16 16v8c0 1.7 .1 3.3 .3 4.8L192 128c0-2.7 0-5.4 0-8zm-32 32v8c0 35.3-28.7 64-64 64H32c-17.7 0-32-14.3-32-32s14.3-32 32-32H64c8.8 0 16-7.2 16-16v-8c0-17.7 14.3-32 32-32s32 14.3 32 32zm224 0v8c0 17.7 14.3 32 32 32s32-14.3 32-32v-8c0-35.3-28.7-64-64-64s-64 28.7-64 64zm-64 64c-35.3 0-64 28.7-64 64v64c0 35.3 28.7 64 64 64h64c35.3 0 64-28.7 64-64V280c0-35.3-28.7-64-64-64H288z"/>
        </symbol>

        <!-- 网络图标 -->
        <symbol id="network-wired" viewBox="0 0 640 512">
            <path d="M256 64H384v64H256V64zM240 0c-26.5 0-48 21.5-48 48v96c0 26.5 21.5 48 48 48h160c26.5 0 48-21.5 48-48V48c0-26.5-21.5-48-48-48H240zM64 256H192v64H64V256zM48 192c-26.5 0-48 21.5-48 48v96c0 26.5 21.5 48 48 48H208c26.5 0 48-21.5 48-48V240c0-26.5-21.5-48-48-48H48zM448 256H576v64H448V256zM432 192c-26.5 0-48 21.5-48 48v96c0 26.5 21.5 48 48 48H592c26.5 0 48-21.5 48-48V240c0-26.5-21.5-48-48-48H432zM288 384H352v64H288V384zM272 320c-26.5 0-48 21.5-48 48v96c0 26.5 21.5 48 48 48h128c26.5 0 48-21.5 48-48V368c0-26.5-21.5-48-48-48H272z"/>
        </symbol>

        <!-- 用户图标 -->
        <symbol id="users" viewBox="0 0 640 512">
            <path d="M144 0a80 80 0 1 1 0 160A80 80 0 1 1 144 0zM512 0a80 80 0 1 1 0 160A80 80 0 1 1 512 0zM0 298.7C0 239.8 47.8 192 106.7 192h42.7c15.9 0 31 3.5 44.6 9.7c-1.3 7.2-1.9 14.7-1.9 22.3c0 38.2 16.8 72.5 43.3 96c-.2 0-.4 0-.7 0H21.3C9.6 320 0 310.4 0 298.7zM405.3 320c-.2 0-.4 0-.7 0c26.6-23.5 43.3-57.8 43.3-96c0-7.6-.7-15-1.9-22.3c13.6-6.3 28.7-9.7 44.6-9.7h42.7C592.2 192 640 239.8 640 298.7c0 11.8-9.6 21.3-21.3 21.3H405.3zM224 224a96 96 0 1 1 192 0 96 96 0 1 1 -192 0zM128 485.3C128 411.7 187.7 352 261.3 352H378.7C452.3 352 512 411.7 512 485.3c0 14.7-11.9 26.7-26.7 26.7H154.7c-14.7 0-26.7-11.9-26.7-26.7z"/>
        </symbol>

        <!-- Twitter图标 -->
        <symbol id="twitter" viewBox="0 0 512 512">
            <path d="M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253z"/>
        </symbol>

        <!-- Facebook图标 -->
        <symbol id="facebook" viewBox="0 0 512 512">
            <path d="M504 256C504 119 393 8 256 8S8 119 8 256c0 123.78 90.69 226.38 209.25 245V327.69h-63V256h63v-54.64c0-62.15 37-96.48 93.67-96.48 27.14 0 55.52 4.84 55.52 4.84v61h-31.28c-30.8 0-40.41 19.12-40.41 38.73V256h68.78l-11 71.69h-57.78V501C413.31 482.38 504 379.78 504 256z"/>
        </symbol>

        <!-- Instagram图标 -->
        <symbol id="instagram" viewBox="0 0 448 512">
            <path d="M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z"/>
        </symbol>

        <!-- LinkedIn图标 -->
        <symbol id="linkedin" viewBox="0 0 448 512">
            <path d="M416 32H31.9C14.3 32 0 46.5 0 64.3v383.4C0 465.5 14.3 480 31.9 480H416c17.6 0 32-14.5 32-32.3V64.3c0-17.8-14.4-32.3-32-32.3zM135.4 416H69V202.2h66.5V416zm-33.2-243c-21.3 0-38.5-17.3-38.5-38.5S80.9 96 102.2 96c21.2 0 38.5 17.3 38.5 38.5 0 21.3-17.2 38.5-38.5 38.5zm282.1 243h-66.4V312c0-24.8-.5-56.7-34.5-56.7-34.6 0-39.9 27-39.9 54.9V416h-66.4V202.2h63.7v29.2h.9c8.9-16.8 30.6-34.5 62.9-34.5 67.2 0 79.7 44.3 79.7 101.9V416z"/>
        </symbol>
    </svg>

    <!-- 加载动画 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div style="text-align: center;">
            <div class="loading-spinner"></div>
            <div class="loading-text">飞鸟VPN 正在加载...</div>
        </div>
    </div>

    <div id="app">
        <!-- 导航栏 -->
        <el-affix :offset="0">
            <el-menu
                mode="horizontal"
                :default-active="activeIndex"
                class="navbar"
                background-color="rgba(0, 0, 0, 0.9)"
                text-color="#fff"
                active-text-color="#00d4ff"
                @select="handleSelect"
            >
                <div style="display: flex; align-items: center; padding: 0 30px;">
                    <div class="brand-logo" style="margin-right: auto; margin-bottom: 0; font-size: 1.6rem;">
                        <svg class="local-icon local-icon-lg" style="margin-right: 8px; font-size: 1.8rem; color: #6366f1; filter: drop-shadow(0 0 15px rgba(99, 102, 241, 0.6));">
                            <use href="#rocket"></use>
                        </svg>
                        <span style="background: linear-gradient(45deg, #6366f1, #8b5cf6); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">飞鸟VPN</span>
                    </div>
                    <el-menu-item index="home" style="font-weight: 600;">首页</el-menu-item>
                    <el-menu-item index="features" style="font-weight: 600;">功能</el-menu-item>
                    <el-menu-item index="pricing" style="font-weight: 600;">价格</el-menu-item>
                    <el-menu-item index="servers" style="font-weight: 600;">服务器</el-menu-item>
                    <el-menu-item index="contact" style="font-weight: 600;">联系</el-menu-item>
                </div>
            </el-menu>
        </el-affix>

        <!-- 主页面 -->
        <section class="hero-section">
            <!-- 背景装饰 -->
            <div class="gradient-blob blob-1"></div>
            <div class="gradient-blob blob-2"></div>
            <div class="gradient-blob blob-3"></div>
            <div class="gradient-blob blob-4"></div>

            <div class="hero-container">
                <div class="hero-content">
                    <div class="brand-logo">
                        <svg class="local-icon local-icon-2x local-icon-pulse" style="color: var(--primary-color); filter: drop-shadow(0 0 15px rgba(99, 102, 241, 0.6));">
                            <use href="#rocket"></use>
                        </svg>
                        <span>飞鸟VPN</span>
                    </div>
                    <h1 class="hero-title">{{ heroTitle }}</h1>

                    <div class="feature-list">
                        <div
                            v-for="(feature, index) in features"
                            :key="index"
                            class="feature-item"
                            v-motion
                            :initial="{ opacity: 0, x: -50 }"
                            :enter="{ opacity: 1, x: 0, transition: { delay: index * 200 } }"
                        >
                            <div class="feature-icon">
                                <svg class="local-icon local-icon-lg">
                                    <use :href="'#' + feature.icon"></use>
                                </svg>
                            </div>
                            <span>{{ feature.text }}</span>
                        </div>
                    </div>

                    <div class="download-section">
                        <el-button
                            type="primary"
                            size="large"
                            @click="handleDownload"
                            style="
                                background: linear-gradient(135deg, #6366f1, #8b5cf6);
                                border: none;
                                padding: 18px 40px;
                                font-size: 18px;
                                font-weight: 700;
                                border-radius: 16px;
                                box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4), 0 4px 12px rgba(0, 0, 0, 0.15);
                                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                                position: relative;
                                overflow: hidden;
                            "
                            @mouseenter="$event.target.style.transform = 'translateY(-2px) scale(1.05)'; $event.target.style.boxShadow = '0 12px 35px rgba(99, 102, 241, 0.5), 0 6px 18px rgba(0, 0, 0, 0.2)'"
                            @mouseleave="$event.target.style.transform = 'translateY(0) scale(1)'; $event.target.style.boxShadow = '0 8px 25px rgba(99, 102, 241, 0.4), 0 4px 12px rgba(0, 0, 0, 0.15)'"
                        >
                            <svg class="local-icon local-icon-lg" style="margin-right: 10px; font-size: 20px;">
                                <use href="#windows"></use>
                            </svg>
                            立即下载
                        </el-button>
                        <div style="margin-top: 20px;">
                            <span style="
                                font-size: 20px;
                                font-weight: 700;
                                color: rgba(255, 255, 255, 0.9);
                                background: linear-gradient(45deg, #fff, #6366f1, #8b5cf6);
                                -webkit-background-clip: text;
                                -webkit-text-fill-color: transparent;
                                background-clip: text;
                            ">免费试用 · 无需注册</span>
                        </div>
                    </div>
                </div>

                <div class="hero-devices">
                    <div class="device-showcase">
                        <!-- 笔记本电脑 -->
                        <div class="laptop-device">
                            <div class="laptop-screen">
                                <div class="browser-bar">
                                    <div class="browser-buttons">
                                        <span class="btn-close"></span>
                                        <span class="btn-minimize"></span>
                                        <span class="btn-maximize"></span>
                                    </div>
                                    <div class="address-bar">google.com</div>
                                </div>
                                <div class="screen-content">
                                    <div class="google-logo">Google</div>
                                    <div class="search-bar"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 手机 -->
                        <div class="mobile-device">
                            <div class="mobile-screen">
                                <div class="mobile-header">
                                    <div>9:41</div>
                                    <div>100%</div>
                                </div>
                                <div class="app-interface">
                                    <div class="app-title">飞鸟VPN</div>
                                    <div class="connection-status">
                                        <div class="status-indicator"></div>
                                        <span>已连接 - {{ currentServer }}</span>
                                    </div>
                                    <div class="server-list">
                                        <div
                                            v-for="server in servers"
                                            :key="server.id"
                                            :class="['server-item', { active: server.id === activeServerId }]"
                                            @click="switchServer(server)"
                                        >
                                            {{ server.flag }} {{ server.name }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 功能特性 -->
        <section id="features" style="
            padding: 140px 0;
            background: linear-gradient(135deg,
                #ffffff 0%,
                #f8fafc 20%,
                #f1f5f9 40%,
                #e2e8f0 60%,
                #f1f5f9 80%,
                #ffffff 100%);
            position: relative;
            overflow: hidden;
        ">
            <!-- 现代化背景装饰 -->
            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; opacity: 0.04;">
                <div style="position: absolute; top: 10%; left: 5%; width: 300px; height: 300px; background: radial-gradient(circle, #6366f1 0%, transparent 70%); border-radius: 50%; animation: float-background 25s ease-in-out infinite;"></div>
                <div style="position: absolute; bottom: 15%; right: 8%; width: 250px; height: 250px; background: radial-gradient(circle, #8b5cf6 0%, transparent 70%); border-radius: 50%; animation: float-background 30s ease-in-out infinite reverse;"></div>
                <div style="position: absolute; top: 35%; right: 20%; width: 180px; height: 180px; background: radial-gradient(circle, #06b6d4 0%, transparent 70%); border-radius: 50%; animation: float-background 22s ease-in-out infinite;"></div>
                <div style="position: absolute; bottom: 40%; left: 15%; width: 120px; height: 120px; background: radial-gradient(circle, #f59e0b 0%, transparent 70%); border-radius: 50%; animation: float-background 28s ease-in-out infinite reverse;"></div>
            </div>

            <!-- 网格背景 -->
            <div style="
                position: absolute; top: 0; left: 0; right: 0; bottom: 0; opacity: 0.02;
                background-image:
                    linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
                background-size: 60px 60px;
            "></div>

            <div style="max-width: 1200px; margin: 0 auto; padding: 0 20px; position: relative; z-index: 2;">
                <h2 style="
                    text-align: center;
                    font-size: 3.2rem;
                    font-weight: 900;
                    margin-bottom: 25px;
                    color: #1f2937;
                    background: linear-gradient(135deg,
                        #1f2937 0%,
                        #6366f1 25%,
                        #8b5cf6 50%,
                        #06b6d4 75%,
                        #1f2937 100%);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                    text-shadow: 0 6px 12px rgba(0,0,0,0.1);
                    letter-spacing: -0.02em;
                    position: relative;
                ">
                    为什么选择我们？
                    <div style="position: absolute; bottom: -20px; left: 50%; transform: translateX(-50%); width: 120px; height: 6px; background: linear-gradient(45deg, #6366f1, #8b5cf6); border-radius: 3px; box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);"></div>
                </h2>
                <div style="
                    text-align: center;
                    margin-bottom: 80px;
                    color: #64748b;
                    font-size: 1.2rem;
                    font-weight: 500;
                    max-width: 600px;
                    margin-left: auto;
                    margin-right: auto;
                ">
                    专业级VPN解决方案，为您的数字生活提供全方位保护
                </div>
                <el-row :gutter="30">
                    <el-col
                        v-for="(feature, index) in detailedFeatures"
                        :key="index"
                        :xs="24" :sm="12" :md="8"
                        style="margin-bottom: 30px;"
                    >
                        <el-card
                            shadow="hover"
                            style="
                                height: 100%;
                                text-align: center;
                                border-radius: 24px;
                                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                                background: rgba(255, 255, 255, 0.95);
                                backdrop-filter: blur(20px);
                                border: 1px solid rgba(59, 130, 246, 0.1);
                                box-shadow:
                                    0 10px 30px rgba(0, 0, 0, 0.08),
                                    0 1px 8px rgba(0, 0, 0, 0.06),
                                    inset 0 1px 0 rgba(255, 255, 255, 0.8);
                            "
                            :body-style="{ padding: '50px 35px' }"
                        >
                            <div style="
                                width: 90px;
                                height: 90px;
                                margin: 0 auto 30px;
                                background: linear-gradient(135deg, #3b82f6 0%, #1e40af 50%, #1d4ed8 100%);
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                font-size: 2.2rem;
                                color: #fff;
                                box-shadow:
                                    0 8px 25px rgba(59, 130, 246, 0.3),
                                    0 3px 10px rgba(0, 0, 0, 0.2),
                                    inset 0 1px 0 rgba(255, 255, 255, 0.2);
                                position: relative;
                            ">
                                <svg class="local-icon local-icon-2x">
                                    <use :href="'#' + feature.icon"></use>
                                </svg>
                                <!-- 光晕效果 -->
                                <div style="
                                    position: absolute;
                                    top: -5px; left: -5px; right: -5px; bottom: -5px;
                                    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(30, 64, 175, 0.1));
                                    border-radius: 50%;
                                    z-index: -1;
                                    animation: glow 3s ease-in-out infinite;
                                "></div>
                            </div>
                            <h3 style="
                                font-size: 1.6rem;
                                font-weight: 700;
                                margin-bottom: 20px;
                                color: #1e293b;
                                background: linear-gradient(135deg, #1e293b 0%, #3b82f6 100%);
                                -webkit-background-clip: text;
                                -webkit-text-fill-color: transparent;
                                background-clip: text;
                            ">
                                {{ feature.title }}
                            </h3>
                            <p style="
                                color: #64748b;
                                line-height: 1.7;
                                font-size: 1rem;
                                font-weight: 500;
                            ">
                                {{ feature.description }}
                            </p>
                        </el-card>
                    </el-col>
                </el-row>
            </div>
        </section>

        <!-- 价格方案 -->
        <section id="pricing" style="
            padding: 140px 0;
            background: linear-gradient(135deg,
                #0f0f23 0%,
                #1a1a2e 20%,
                #16213e 40%,
                #0e1b2e 60%,
                #1a1a2e 80%,
                #0f0f23 100%);
            position: relative;
            overflow: hidden;
        ">
            <!-- 现代化背景装饰 -->
            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; opacity: 0.12; z-index: 1;">
                <div style="position: absolute; top: 12%; left: 6%; width: 350px; height: 350px; background: radial-gradient(circle, rgba(99, 102, 241, 0.4) 0%, transparent 70%); border-radius: 50%; animation: float-background 18s ease-in-out infinite;"></div>
                <div style="position: absolute; bottom: 12%; right: 6%; width: 280px; height: 280px; background: radial-gradient(circle, rgba(139, 92, 246, 0.3) 0%, transparent 70%); border-radius: 50%; animation: float-background 22s ease-in-out infinite reverse;"></div>
                <div style="position: absolute; top: 35%; left: 50%; width: 220px; height: 220px; background: radial-gradient(circle, rgba(6, 182, 212, 0.25) 0%, transparent 70%); border-radius: 50%; animation: float-background 25s ease-in-out infinite; transform: translate(-50%, -50%);"></div>
                <div style="position: absolute; top: 20%; right: 15%; width: 180px; height: 180px; background: radial-gradient(circle, rgba(245, 158, 11, 0.2) 0%, transparent 70%); border-radius: 50%; animation: float-background 20s ease-in-out infinite;"></div>
                <div style="position: absolute; bottom: 35%; left: 20%; width: 160px; height: 160px; background: radial-gradient(circle, rgba(16, 185, 129, 0.2) 0%, transparent 70%); border-radius: 50%; animation: float-background 24s ease-in-out infinite reverse;"></div>
            </div>

            <!-- 网格背景 -->
            <div style="
                position: absolute; top: 0; left: 0; right: 0; bottom: 0; opacity: 0.03;
                background-image:
                    linear-gradient(rgba(59, 130, 246, 0.2) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(59, 130, 246, 0.2) 1px, transparent 1px);
                background-size: 80px 80px;
                z-index: 1;
            "></div>

            <!-- 光线效果 -->
            <div style="
                position: absolute; top: 0; left: 0; right: 0; bottom: 0; z-index: 1;
                background:
                    radial-gradient(ellipse at 20% 30%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                    radial-gradient(ellipse at 80% 70%, rgba(30, 64, 175, 0.08) 0%, transparent 50%),
                    linear-gradient(135deg, transparent 0%, rgba(59, 130, 246, 0.05) 50%, transparent 100%);
            "></div>

            <div style="max-width: 1200px; margin: 0 auto; padding: 0 20px; position: relative; z-index: 2;">
                <h2 style="
                    text-align: center;
                    font-size: 3.4rem;
                    font-weight: 900;
                    margin-bottom: 25px;
                    color: #ffffff;
                    text-shadow: 0 6px 25px rgba(0,0,0,0.4);
                    background: linear-gradient(135deg,
                        #ffffff 0%,
                        #6366f1 20%,
                        #8b5cf6 40%,
                        #06b6d4 60%,
                        #f59e0b 80%,
                        #ffffff 100%);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                    letter-spacing: -0.02em;
                    position: relative;
                ">
                    选择您的方案
                    <div style="position: absolute; bottom: -20px; left: 50%; transform: translateX(-50%); width: 140px; height: 6px; background: linear-gradient(45deg, #6366f1, #8b5cf6, #06b6d4); border-radius: 3px; box-shadow: 0 4px 15px rgba(99, 102, 241, 0.5);"></div>
                </h2>
                <div style="
                    text-align: center;
                    margin-bottom: 80px;
                    color: #cbd5e1;
                    font-size: 1.2rem;
                    font-weight: 500;
                    max-width: 600px;
                    margin-left: auto;
                    margin-right: auto;
                ">
                    灵活的定价方案，满足个人和企业的不同需求
                </div>
                <el-row :gutter="30" justify="center">
                    <el-col
                        v-for="(plan, index) in pricingPlans"
                        :key="index"
                        :xs="24" :sm="12" :md="8"
                        style="margin-bottom: 30px;"
                    >
                        <div
                            :class="['pricing-card', { 'featured': plan.popular }]"
                            style="
                                background: rgba(255, 255, 255, 0.95);
                                backdrop-filter: blur(20px);
                                border-radius: 24px;
                                padding: 40px 30px;
                                text-align: center;
                                position: relative;
                                height: 100%;
                                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                                border: 1px solid rgba(255, 255, 255, 0.2);
                                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                                cursor: pointer;
                            "
                            @mouseenter="handleCardHover($event, true)"
                            @mouseleave="handleCardHover($event, false)"
                        >
                            <!-- 推荐标签 -->
                            <div
                                v-if="plan.popular"
                                style="
                                    position: absolute;
                                    top: -12px;
                                    left: 50%;
                                    transform: translateX(-50%);
                                    background: linear-gradient(45deg, #4FC3F7, #29B6F6);
                                    color: white;
                                    padding: 8px 24px;
                                    border-radius: 20px;
                                    font-size: 14px;
                                    font-weight: 600;
                                    box-shadow: 0 4px 12px rgba(79, 195, 247, 0.4);
                                    animation: glow 2s ease infinite;
                                "
                            >
                                推荐
                            </div>

                            <!-- 方案名称 -->
                            <h3 style="
                                font-size: 1.8rem;
                                font-weight: 700;
                                margin-bottom: 20px;
                                color: #2D3748;
                                margin-top: 10px;
                            ">
                                {{ plan.name }}
                            </h3>

                            <!-- 价格 -->
                            <div style="margin-bottom: 35px;">
                                <div style="display: flex; align-items: baseline; justify-content: center; gap: 4px;">
                                    <span style="font-size: 1.2rem; color: #4FC3F7; font-weight: 600;">¥</span>
                                    <span
                                        :class="{ 'price-number': plan.popular }"
                                        style="
                                            font-size: 3.5rem;
                                            font-weight: 800;
                                            color: #4FC3F7;
                                            line-height: 1;
                                            background: linear-gradient(45deg, #4FC3F7, #29B6F6);
                                            -webkit-background-clip: text;
                                            -webkit-text-fill-color: transparent;
                                            background-clip: text;
                                        "
                                    >{{ plan.price }}</span>
                                    <span style="font-size: 1.1rem; color: #718096; font-weight: 500;">/月</span>
                                </div>
                            </div>

                            <!-- 功能列表 -->
                            <div style="margin-bottom: 35px;">
                                <div
                                    v-for="feature in plan.features"
                                    :key="feature"
                                    style="
                                        padding: 12px 0;
                                        color: #4A5568;
                                        display: flex;
                                        align-items: center;
                                        font-size: 15px;
                                        line-height: 1.5;
                                    "
                                >
                                    <div style="
                                        width: 20px;
                                        height: 20px;
                                        background: linear-gradient(45deg, #48BB78, #38A169);
                                        border-radius: 50%;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        margin-right: 12px;
                                        flex-shrink: 0;
                                    ">
                                        <svg class="local-icon" style="color: white; font-size: 10px;">
                                            <use href="#check"></use>
                                        </svg>
                                    </div>
                                    {{ feature }}
                                </div>
                            </div>

                            <!-- 选择按钮 -->
                            <el-button
                                :type="plan.popular ? 'primary' : ''"
                                size="large"
                                :style="{
                                    width: '100%',
                                    borderRadius: '16px',
                                    padding: '16px 24px',
                                    fontSize: '16px',
                                    fontWeight: '600',
                                    background: plan.popular ? 'linear-gradient(45deg, #4FC3F7, #29B6F6)' : 'transparent',
                                    border: plan.popular ? 'none' : '2px solid #E2E8F0',
                                    color: plan.popular ? 'white' : '#4A5568',
                                    boxShadow: plan.popular ? '0 8px 20px rgba(79, 195, 247, 0.3)' : 'none',
                                    transition: 'all 0.3s ease'
                                }"
                                @click="selectPlan(plan)"
                                @mouseenter="plan.popular ? null : ($event.target.style.borderColor = '#4FC3F7', $event.target.style.color = '#4FC3F7')"
                                @mouseleave="plan.popular ? null : ($event.target.style.borderColor = '#E2E8F0', $event.target.style.color = '#4A5568')"
                            >
                                选择方案
                            </el-button>
                        </div>
                    </el-col>
                </el-row>
            </div>
        </section>

        <!-- 服务器地图 -->
        <section id="servers" style="
            padding: 140px 0;
            background: linear-gradient(135deg,
                #ffffff 0%,
                #f8fafc 15%,
                #f1f5f9 30%,
                #e2e8f0 45%,
                #f1f5f9 60%,
                #f8fafc 75%,
                #ffffff 90%,
                #f9fafb 100%);
            position: relative;
            overflow: hidden;
        ">
            <!-- 商业化背景装饰 -->
            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; opacity: 0.04;">
                <div style="position: absolute; top: 12%; left: 6%; width: 350px; height: 350px; background: radial-gradient(circle, #3b82f6 0%, transparent 70%); border-radius: 50%; animation: float-background 25s ease-in-out infinite;"></div>
                <div style="position: absolute; bottom: 12%; right: 6%; width: 280px; height: 280px; background: radial-gradient(circle, #1e40af 0%, transparent 70%); border-radius: 50%; animation: float-background 30s ease-in-out infinite reverse;"></div>
                <div style="position: absolute; top: 35%; left: 50%; width: 200px; height: 200px; background: radial-gradient(circle, #2563eb 0%, transparent 70%); border-radius: 50%; animation: float-background 20s ease-in-out infinite; transform: translateX(-50%);"></div>
            </div>

            <!-- 六边形网格背景 -->
            <div style="
                position: absolute; top: 0; left: 0; right: 0; bottom: 0; opacity: 0.02;
                background-image:
                    radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 2px, transparent 2px),
                    radial-gradient(circle at 75% 75%, rgba(30, 64, 175, 0.08) 1px, transparent 1px);
                background-size: 100px 100px, 150px 150px;
                z-index: 1;
            "></div>

            <!-- 渐变光效 -->
            <div style="
                position: absolute; top: 0; left: 0; right: 0; bottom: 0; z-index: 1;
                background:
                    radial-gradient(ellipse at 30% 20%, rgba(59, 130, 246, 0.05) 0%, transparent 60%),
                    radial-gradient(ellipse at 70% 80%, rgba(30, 64, 175, 0.03) 0%, transparent 60%);
            "></div>

            <div style="max-width: 1200px; margin: 0 auto; padding: 0 20px; position: relative; z-index: 2;">
                <h2 style="
                    text-align: center;
                    font-size: 3.2rem;
                    font-weight: 900;
                    margin-bottom: 25px;
                    color: #1f2937;
                    position: relative;
                    background: linear-gradient(135deg,
                        #1f2937 0%,
                        #6366f1 25%,
                        #8b5cf6 50%,
                        #06b6d4 75%,
                        #1f2937 100%);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                    letter-spacing: -0.02em;
                ">
                    全球服务器网络
                    <div style="position: absolute; bottom: -20px; left: 50%; transform: translateX(-50%); width: 140px; height: 6px; background: linear-gradient(45deg, #6366f1, #8b5cf6, #06b6d4); border-radius: 3px; box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);"></div>
                </h2>
                <div style="
                    text-align: center;
                    margin-bottom: 80px;
                    color: #64748b;
                    font-size: 1.2rem;
                    font-weight: 500;
                    max-width: 700px;
                    margin-left: auto;
                    margin-right: auto;
                ">
                    遍布全球的高性能服务器节点，为您提供稳定快速的连接体验
                </div>

                <!-- 世界地图容器 -->
                <div class="world-map-container" style="
                    position: relative;
                    height: 550px;
                    border-radius: 40px;
                    margin: 100px 0;
                    overflow: hidden;
                    background: linear-gradient(135deg,
                        #ffffff 0%,
                        #f8fafc 20%,
                        #f1f5f9 40%,
                        #e2e8f0 60%,
                        #f1f5f9 80%,
                        #ffffff 100%);
                    box-shadow:
                        0 30px 60px rgba(0, 0, 0, 0.12),
                        0 15px 35px rgba(99, 102, 241, 0.1),
                        0 0 0 2px rgba(99, 102, 241, 0.15),
                        inset 0 2px 0 rgba(255, 255, 255, 0.95),
                        inset 0 -2px 0 rgba(226, 232, 240, 0.6);
                    border: 3px solid rgba(226, 232, 240, 0.7);
                    backdrop-filter: blur(25px) saturate(120%);
                    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                ">
                    <!-- 全球地图SVG -->
                    <svg style="
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        opacity: 0.08;
                        z-index: 1;
                    " viewBox="0 0 1000 500" preserveAspectRatio="xMidYMid meet">
                        <defs>
                            <linearGradient id="mapGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#4FC3F7;stop-opacity:0.6" />
                                <stop offset="100%" style="stop-color:#29B6F6;stop-opacity:0.3" />
                            </linearGradient>
                        </defs>

                        <!-- 北美洲 -->
                        <path d="M 50 80 Q 80 60 120 70 Q 160 65 200 80 Q 240 90 280 110 Q 300 130 290 160 Q 280 190 260 210 Q 240 230 200 240 Q 160 245 120 240 Q 80 235 50 220 Q 30 200 35 180 Q 40 160 45 140 Q 48 120 50 100 Z"
                              fill="url(#mapGradient)"
                              stroke="#4FC3F7"
                              stroke-width="1"
                              opacity="0.7">
                            <animate attributeName="opacity" values="0.5;0.8;0.5" dur="4s" repeatCount="indefinite"/>
                        </path>

                        <!-- 南美洲 -->
                        <path d="M 180 260 Q 200 250 220 270 Q 240 290 250 320 Q 260 350 250 380 Q 240 410 220 430 Q 200 440 180 435 Q 160 430 150 410 Q 140 390 145 370 Q 150 350 155 330 Q 160 310 165 290 Q 170 275 180 260 Z"
                              fill="url(#mapGradient)"
                              stroke="#4FC3F7"
                              stroke-width="1"
                              opacity="0.6">
                            <animate attributeName="opacity" values="0.4;0.7;0.4" dur="5s" repeatCount="indefinite"/>
                        </path>

                        <!-- 欧洲 -->
                        <path d="M 420 90 Q 450 80 480 85 Q 510 90 530 100 Q 540 110 535 125 Q 530 140 520 150 Q 510 160 490 165 Q 470 170 450 165 Q 430 160 420 150 Q 410 140 415 125 Q 420 110 420 95 Z"
                              fill="url(#mapGradient)"
                              stroke="#4FC3F7"
                              stroke-width="1"
                              opacity="0.8">
                            <animate attributeName="opacity" values="0.6;0.9;0.6" dur="3s" repeatCount="indefinite"/>
                        </path>

                        <!-- 非洲 -->
                        <path d="M 450 180 Q 480 170 510 180 Q 530 190 540 210 Q 550 230 545 250 Q 540 270 530 290 Q 520 310 500 330 Q 480 350 460 360 Q 440 365 420 360 Q 400 355 390 340 Q 380 325 385 305 Q 390 285 395 265 Q 400 245 410 225 Q 420 205 435 190 Q 445 185 450 180 Z"
                              fill="url(#mapGradient)"
                              stroke="#4FC3F7"
                              stroke-width="1"
                              opacity="0.7">
                            <animate attributeName="opacity" values="0.5;0.8;0.5" dur="6s" repeatCount="indefinite"/>
                        </path>

                        <!-- 亚洲 -->
                        <path d="M 550 70 Q 600 60 650 70 Q 700 80 750 90 Q 800 100 830 120 Q 850 140 845 160 Q 840 180 825 200 Q 810 220 790 235 Q 770 250 745 260 Q 720 270 695 265 Q 670 260 645 250 Q 620 240 600 225 Q 580 210 570 190 Q 560 170 565 150 Q 570 130 575 110 Q 580 90 585 80 Q 590 75 595 72 Q 600 70 605 69 Z"
                              fill="url(#mapGradient)"
                              stroke="#4FC3F7"
                              stroke-width="1"
                              opacity="0.9">
                            <animate attributeName="opacity" values="0.7;1.0;0.7" dur="4s" repeatCount="indefinite"/>
                        </path>

                        <!-- 澳洲 -->
                        <path d="M 720 320 Q 750 310 780 320 Q 800 330 810 350 Q 815 370 805 385 Q 795 400 780 405 Q 765 410 750 405 Q 735 400 725 385 Q 715 370 720 355 Q 725 340 720 325 Z"
                              fill="url(#mapGradient)"
                              stroke="#4FC3F7"
                              stroke-width="1"
                              opacity="0.6">
                            <animate attributeName="opacity" values="0.4;0.7;0.4" dur="5s" repeatCount="indefinite"/>
                        </path>

                        <!-- 格陵兰 -->
                        <path d="M 320 30 Q 340 25 360 35 Q 375 45 370 60 Q 365 75 350 80 Q 335 85 320 80 Q 305 75 300 60 Q 295 45 305 35 Q 315 30 320 30 Z"
                              fill="url(#mapGradient)"
                              stroke="#4FC3F7"
                              stroke-width="1"
                              opacity="0.5">
                            <animate attributeName="opacity" values="0.3;0.6;0.3" dur="7s" repeatCount="indefinite"/>
                        </path>

                        <!-- 连接线网络 -->
                        <g opacity="0.3">
                            <line x1="150" y1="150" x2="470" y2="120" stroke="#4FC3F7" stroke-width="1" stroke-dasharray="5,5">
                                <animate attributeName="stroke-dashoffset" values="0;-10" dur="2s" repeatCount="indefinite"/>
                            </line>
                            <line x1="470" y1="120" x2="650" y2="140" stroke="#4FC3F7" stroke-width="1" stroke-dasharray="5,5">
                                <animate attributeName="stroke-dashoffset" values="0;-10" dur="2.5s" repeatCount="indefinite"/>
                            </line>
                            <line x1="200" y1="350" x2="480" y2="280" stroke="#4FC3F7" stroke-width="1" stroke-dasharray="5,5">
                                <animate attributeName="stroke-dashoffset" values="0;-10" dur="3s" repeatCount="indefinite"/>
                            </line>
                            <line x1="650" y1="140" x2="760" y2="360" stroke="#4FC3F7" stroke-width="1" stroke-dasharray="5,5">
                                <animate attributeName="stroke-dashoffset" values="0;-10" dur="2.8s" repeatCount="indefinite"/>
                            </line>
                        </g>
                    </svg>

                    <!-- 地图背景网格 -->
                    <div style="
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background-image:
                            linear-gradient(rgba(79, 195, 247, 0.02) 1px, transparent 1px),
                            linear-gradient(90deg, rgba(79, 195, 247, 0.02) 1px, transparent 1px);
                        background-size: 50px 50px;
                        opacity: 0.3;
                        z-index: 2;
                    "></div>

                    <!-- 服务器节点 -->
                    <div
                        v-for="(server, index) in globalServers"
                        :key="server.id"
                        :style="{
                            position: 'absolute',
                            top: server.position.top,
                            left: server.position.left,
                            cursor: 'pointer',
                            zIndex: 15
                        }"
                        @click="showServerInfo(server)"
                        @mouseenter="handleServerHover($event, server, true)"
                        @mouseleave="handleServerHover($event, server, false)"
                    >
                        <!-- 外圈脉冲动画 -->
                        <div style="
                            position: absolute;
                            top: -15px;
                            left: -15px;
                            width: 30px;
                            height: 30px;
                            border: 2px solid #4FC3F7;
                            border-radius: 50%;
                            animation: server-pulse 2s ease-in-out infinite;
                            opacity: 0.6;
                        "></div>

                        <!-- 中圈 -->
                        <div style="
                            position: absolute;
                            top: -10px;
                            left: -10px;
                            width: 20px;
                            height: 20px;
                            background: rgba(79, 195, 247, 0.2);
                            border-radius: 50%;
                            animation: server-pulse 2s ease-in-out infinite;
                            animation-delay: 0.5s;
                        "></div>

                        <!-- 核心点 -->
                        <div class="server-core" style="
                            width: 12px;
                            height: 12px;
                            background: linear-gradient(45deg, #4FC3F7, #29B6F6);
                            border-radius: 50%;
                            box-shadow:
                                0 0 20px rgba(79, 195, 247, 0.6),
                                0 2px 10px rgba(0, 0, 0, 0.2);
                            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                            position: relative;
                            z-index: 2;
                        "></div>

                        <!-- 悬停提示 -->
                        <div
                            class="server-tooltip"
                            style="
                                position: absolute;
                                bottom: 25px;
                                left: 50%;
                                transform: translateX(-50%);
                                background: rgba(45, 55, 72, 0.95);
                                color: white;
                                padding: 8px 12px;
                                border-radius: 8px;
                                font-size: 12px;
                                font-weight: 600;
                                white-space: nowrap;
                                opacity: 0;
                                visibility: hidden;
                                transition: all 0.3s ease;
                                backdrop-filter: blur(10px);
                                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                                z-index: 20;
                            "
                        >
                            {{ server.country }}
                            <div style="
                                position: absolute;
                                top: 100%;
                                left: 50%;
                                transform: translateX(-50%);
                                width: 0;
                                height: 0;
                                border-left: 4px solid transparent;
                                border-right: 4px solid transparent;
                                border-top: 4px solid rgba(45, 55, 72, 0.95);
                            "></div>
                        </div>
                    </div>

                    <!-- 连接线动画 -->
                    <svg style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none; z-index: 10;">
                        <defs>
                            <linearGradient id="connectionGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#4FC3F7;stop-opacity:0" />
                                <stop offset="50%" style="stop-color:#4FC3F7;stop-opacity:0.6" />
                                <stop offset="100%" style="stop-color:#29B6F6;stop-opacity:0" />
                            </linearGradient>
                        </defs>
                        <!-- 示例连接线 -->
                        <path d="M 150 100 Q 400 50 650 150" stroke="url(#connectionGradient)" stroke-width="2" fill="none" opacity="0.4">
                            <animate attributeName="stroke-dasharray" values="0,1000;1000,1000" dur="3s" repeatCount="indefinite"/>
                        </path>
                        <path d="M 200 200 Q 500 150 750 250" stroke="url(#connectionGradient)" stroke-width="2" fill="none" opacity="0.3">
                            <animate attributeName="stroke-dasharray" values="0,1000;1000,1000" dur="4s" repeatCount="indefinite"/>
                        </path>
                    </svg>
                </div>

                <!-- 统计数据 -->
                <el-row :gutter="60" justify="center" style="margin-top: 50px;">
                    <el-col :xs="24" :sm="8" style="margin-bottom: 30px;">
                        <div class="stats-card" style="
                            text-align: center;
                            background: rgba(255, 255, 255, 0.95);
                            backdrop-filter: blur(25px);
                            border-radius: 24px;
                            padding: 40px 25px;
                            box-shadow:
                                0 15px 35px rgba(0, 0, 0, 0.1),
                                0 5px 15px rgba(59, 130, 246, 0.08),
                                inset 0 1px 0 rgba(255, 255, 255, 0.9);
                            border: 1px solid rgba(59, 130, 246, 0.15);
                            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                        "
                        >
                            <div style="
                                width: 70px;
                                height: 70px;
                                background: linear-gradient(135deg, #3b82f6 0%, #1e40af 50%, #1d4ed8 100%);
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                margin: 0 auto 25px;
                                box-shadow:
                                    0 10px 25px rgba(59, 130, 246, 0.3),
                                    0 4px 12px rgba(0, 0, 0, 0.2),
                                    inset 0 1px 0 rgba(255, 255, 255, 0.2);
                                position: relative;
                            ">
                                <svg class="local-icon local-icon-xl" style="color: white; font-size: 1.6rem;">
                                    <use href="#server"></use>
                                </svg>
                                <!-- 光晕效果 -->
                                <div style="
                                    position: absolute;
                                    top: -3px; left: -3px; right: -3px; bottom: -3px;
                                    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(30, 64, 175, 0.1));
                                    border-radius: 50%;
                                    z-index: -1;
                                    animation: glow 3s ease-in-out infinite;
                                "></div>
                            </div>
                            <div style="
                                font-size: 2.2rem;
                                font-weight: 800;
                                color: #3b82f6;
                                margin-bottom: 10px;
                                background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
                                -webkit-background-clip: text;
                                -webkit-text-fill-color: transparent;
                                background-clip: text;
                            ">{{ stats.servers }}+</div>
                            <div style="font-size: 1.1rem; color: #64748b; font-weight: 600;">服务器</div>
                        </div>
                    </el-col>
                    <el-col :xs="24" :sm="8" style="margin-bottom: 30px;">
                        <div class="stats-card" style="
                            text-align: center;
                            background: rgba(255, 255, 255, 0.95);
                            backdrop-filter: blur(25px);
                            border-radius: 24px;
                            padding: 40px 25px;
                            box-shadow:
                                0 15px 35px rgba(0, 0, 0, 0.1),
                                0 5px 15px rgba(59, 130, 246, 0.08),
                                inset 0 1px 0 rgba(255, 255, 255, 0.9);
                            border: 1px solid rgba(59, 130, 246, 0.15);
                            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                        "
                        >
                            <div style="
                                width: 70px;
                                height: 70px;
                                background: linear-gradient(135deg, #3b82f6 0%, #1e40af 50%, #1d4ed8 100%);
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                margin: 0 auto 25px;
                                box-shadow:
                                    0 10px 25px rgba(59, 130, 246, 0.3),
                                    0 4px 12px rgba(0, 0, 0, 0.2),
                                    inset 0 1px 0 rgba(255, 255, 255, 0.2);
                                position: relative;
                            ">
                                <svg class="local-icon local-icon-xl" style="color: white; font-size: 1.6rem;">
                                    <use href="#globe-americas"></use>
                                </svg>
                                <!-- 光晕效果 -->
                                <div style="
                                    position: absolute;
                                    top: -3px; left: -3px; right: -3px; bottom: -3px;
                                    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(30, 64, 175, 0.1));
                                    border-radius: 50%;
                                    z-index: -1;
                                    animation: glow 3s ease-in-out infinite;
                                "></div>
                            </div>
                            <div style="
                                font-size: 2.2rem;
                                font-weight: 800;
                                color: #3b82f6;
                                margin-bottom: 10px;
                                background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
                                -webkit-background-clip: text;
                                -webkit-text-fill-color: transparent;
                                background-clip: text;
                            ">{{ stats.countries }}+</div>
                            <div style="font-size: 1.1rem; color: #64748b; font-weight: 600;">国家</div>
                        </div>
                    </el-col>
                    <el-col :xs="24" :sm="8" style="margin-bottom: 30px;">
                        <div class="stats-card" style="
                            text-align: center;
                            background: rgba(255, 255, 255, 0.95);
                            backdrop-filter: blur(25px);
                            border-radius: 24px;
                            padding: 40px 25px;
                            box-shadow:
                                0 15px 35px rgba(0, 0, 0, 0.1),
                                0 5px 15px rgba(59, 130, 246, 0.08),
                                inset 0 1px 0 rgba(255, 255, 255, 0.9);
                            border: 1px solid rgba(59, 130, 246, 0.15);
                            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                        "
                        >
                            <div style="
                                width: 70px;
                                height: 70px;
                                background: linear-gradient(135deg, #3b82f6 0%, #1e40af 50%, #1d4ed8 100%);
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                margin: 0 auto 25px;
                                box-shadow:
                                    0 10px 25px rgba(59, 130, 246, 0.3),
                                    0 4px 12px rgba(0, 0, 0, 0.2),
                                    inset 0 1px 0 rgba(255, 255, 255, 0.2);
                                position: relative;
                            ">
                                <svg class="local-icon local-icon-xl" style="color: white; font-size: 1.6rem;">
                                    <use href="#tachometer"></use>
                                </svg>
                                <!-- 光晕效果 -->
                                <div style="
                                    position: absolute;
                                    top: -3px; left: -3px; right: -3px; bottom: -3px;
                                    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(30, 64, 175, 0.1));
                                    border-radius: 50%;
                                    z-index: -1;
                                    animation: glow 3s ease-in-out infinite;
                                "></div>
                            </div>
                            <div style="
                                font-size: 2.2rem;
                                font-weight: 800;
                                color: #3b82f6;
                                margin-bottom: 10px;
                                background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
                                -webkit-background-clip: text;
                                -webkit-text-fill-color: transparent;
                                background-clip: text;
                            ">{{ stats.uptime }}%</div>
                            <div style="font-size: 1.1rem; color: #64748b; font-weight: 600;">在线率</div>
                        </div>
                    </el-col>
                </el-row>
            </div>
        </section>

        <!-- 联系我们 -->
        <section id="contact" style="
            padding: 120px 0;
            background: linear-gradient(135deg, #1e293b 0%, #334155 25%, #475569 50%, #334155 75%, #1e293b 100%);
            position: relative;
            overflow: hidden;
        ">
            <!-- 商业化背景装饰 -->
            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; opacity: 0.08;">
                <div style="position: absolute; top: 20%; left: 10%; width: 250px; height: 250px; background: radial-gradient(circle, rgba(59, 130, 246, 0.4) 0%, transparent 70%); border-radius: 50%; animation: float-background 18s ease-in-out infinite;"></div>
                <div style="position: absolute; bottom: 20%; right: 10%; width: 200px; height: 200px; background: radial-gradient(circle, rgba(30, 64, 175, 0.3) 0%, transparent 70%); border-radius: 50%; animation: float-background 22s ease-in-out infinite reverse;"></div>
                <div style="position: absolute; top: 50%; left: 50%; width: 150px; height: 150px; background: radial-gradient(circle, rgba(37, 99, 235, 0.2) 0%, transparent 70%); border-radius: 50%; animation: float-background 25s ease-in-out infinite; transform: translate(-50%, -50%);"></div>
            </div>

            <!-- 网格背景 -->
            <div style="
                position: absolute; top: 0; left: 0; right: 0; bottom: 0; opacity: 0.02;
                background-image:
                    linear-gradient(rgba(59, 130, 246, 0.3) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(59, 130, 246, 0.3) 1px, transparent 1px);
                background-size: 70px 70px;
            "></div>

            <!-- 光线效果 -->
            <div style="
                position: absolute; top: 0; left: 0; right: 0; bottom: 0;
                background:
                    radial-gradient(ellipse at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                    radial-gradient(ellipse at 75% 75%, rgba(30, 64, 175, 0.08) 0%, transparent 50%);
            "></div>

            <div style="max-width: 1000px; margin: 0 auto; padding: 0 20px; position: relative; z-index: 2;">
                <h2 style="
                    text-align: center;
                    font-size: 2.8rem;
                    font-weight: 800;
                    margin-bottom: 20px;
                    color: #fff;
                    text-shadow: 0 4px 20px rgba(0,0,0,0.3);
                    background: linear-gradient(135deg, #fff 0%, #3b82f6 50%, #fff 100%);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                ">
                    联系我们
                </h2>
                <div style="
                    text-align: center;
                    margin-bottom: 80px;
                    color: #cbd5e1;
                    font-size: 1.2rem;
                    font-weight: 500;
                    max-width: 600px;
                    margin-left: auto;
                    margin-right: auto;
                ">
                    专业的技术支持团队，为您提供7x24小时优质服务
                </div>
                <el-row :gutter="60">
                    <el-col :xs="24" :md="12">
                        <div style="color: #fff;">
                            <h3 style="font-size: 1.8rem; font-weight: 600; margin-bottom: 20px;">获取帮助</h3>
                            <p style="color: rgba(255, 255, 255, 0.8); margin-bottom: 30px;">我们的专业团队随时为您提供支持</p>

                            <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 20px;">
                                <svg class="local-icon local-icon-lg" style="color: #00d4ff; font-size: 1.2rem;">
                                    <use href="#envelope"></use>
                                </svg>
                                <span>{{ contactInfo.email }}</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 20px;">
                                <svg class="local-icon local-icon-lg" style="color: #00d4ff; font-size: 1.2rem;">
                                    <use href="#phone"></use>
                                </svg>
                                <span>{{ contactInfo.phone }}</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <svg class="local-icon local-icon-lg" style="color: #00d4ff; font-size: 1.2rem;">
                                    <use href="#clock"></use>
                                </svg>
                                <span>{{ contactInfo.hours }}</span>
                            </div>
                        </div>
                    </el-col>
                    <el-col :xs="24" :md="12">
                        <el-form
                            ref="contactForm"
                            :model="contactForm"
                            :rules="contactRules"
                            label-position="top"
                            style="
                                background: rgba(255, 255, 255, 0.15);
                                padding: 40px;
                                border-radius: 24px;
                                backdrop-filter: blur(20px);
                                border: 1px solid rgba(255, 255, 255, 0.2);
                                box-shadow:
                                    0 20px 40px rgba(0, 0, 0, 0.2),
                                    inset 0 1px 0 rgba(255, 255, 255, 0.3);
                            "
                        >
                            <el-form-item label="您的姓名" prop="name">
                                <el-input
                                    v-model="contactForm.name"
                                    placeholder="请输入您的姓名"
                                    style="--el-input-bg-color: rgba(255, 255, 255, 0.9);"
                                />
                            </el-form-item>
                            <el-form-item label="您的邮箱" prop="email">
                                <el-input
                                    v-model="contactForm.email"
                                    placeholder="请输入您的邮箱"
                                    style="--el-input-bg-color: rgba(255, 255, 255, 0.9);"
                                />
                            </el-form-item>
                            <el-form-item label="您的消息" prop="message">
                                <el-input
                                    v-model="contactForm.message"
                                    type="textarea"
                                    :rows="5"
                                    placeholder="请输入您的消息"
                                    style="--el-input-bg-color: rgba(255, 255, 255, 0.9);"
                                />
                            </el-form-item>
                            <el-form-item>
                                <el-button
                                    type="primary"
                                    size="large"
                                    style="
                                        width: 100%;
                                        background: linear-gradient(135deg, #3b82f6 0%, #1e40af 50%, #1d4ed8 100%);
                                        border: none;
                                        border-radius: 16px;
                                        padding: 16px 24px;
                                        font-size: 16px;
                                        font-weight: 600;
                                        box-shadow:
                                            0 8px 20px rgba(59, 130, 246, 0.3),
                                            0 3px 10px rgba(0, 0, 0, 0.2);
                                        transition: all 0.3s ease;
                                    "
                                    @click="submitContactForm"
                                    :loading="submitting"
                                >
                                    发送消息
                                </el-button>
                            </el-form-item>
                        </el-form>
                    </el-col>
                </el-row>
            </div>
        </section>

        <!-- 页脚 -->
        <footer style="
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
            color: #fff;
            padding: 80px 0 30px;
            position: relative;
            overflow: hidden;
        ">
            <!-- 商业化背景装饰 -->
            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; opacity: 0.05;">
                <div style="position: absolute; top: 20%; left: 15%; width: 200px; height: 200px; background: radial-gradient(circle, rgba(59, 130, 246, 0.3) 0%, transparent 70%); border-radius: 50%; animation: float-background 30s ease-in-out infinite;"></div>
                <div style="position: absolute; bottom: 20%; right: 15%; width: 150px; height: 150px; background: radial-gradient(circle, rgba(30, 64, 175, 0.2) 0%, transparent 70%); border-radius: 50%; animation: float-background 35s ease-in-out infinite reverse;"></div>
            </div>

            <!-- 顶部分割线 -->
            <div style="
                position: absolute; top: 0; left: 0; right: 0; height: 1px;
                background: linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.3) 50%, transparent 100%);
            "></div>
            <div style="max-width: 1200px; margin: 0 auto; padding: 0 20px; position: relative; z-index: 2;">
                <el-row :gutter="50" style="margin-bottom: 50px;">
                    <el-col :xs="24" :sm="12" :md="6">
                        <div class="brand-logo" style="margin-bottom: 20px;">
                            <svg class="local-icon local-icon-2x" style="
                                color: #3b82f6;
                                filter: drop-shadow(0 0 10px rgba(59, 130, 246, 0.5));
                            ">
                                <use href="#dove"></use>
                            </svg>
                            <span style="
                                background: linear-gradient(135deg, #fff 0%, #3b82f6 100%);
                                -webkit-background-clip: text;
                                -webkit-text-fill-color: transparent;
                                background-clip: text;
                            ">飞鸟VPN</span>
                        </div>
                        <p style="
                            color: rgba(255, 255, 255, 0.8);
                            font-size: 1rem;
                            line-height: 1.6;
                            font-weight: 500;
                        ">保护您的隐私，连接全世界</p>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="6">
                        <h4 style="
                            font-size: 1.3rem;
                            font-weight: 700;
                            margin-bottom: 25px;
                            color: #3b82f6;
                            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
                            -webkit-background-clip: text;
                            -webkit-text-fill-color: transparent;
                            background-clip: text;
                        ">产品</h4>
                        <ul style="list-style: none;">
                            <li style="margin-bottom: 10px;">
                                <a href="#" style="color: rgba(255, 255, 255, 0.7); text-decoration: none; transition: color 0.3s ease;">VPN应用</a>
                            </li>
                            <li style="margin-bottom: 10px;">
                                <a href="#" style="color: rgba(255, 255, 255, 0.7); text-decoration: none; transition: color 0.3s ease;">企业解决方案</a>
                            </li>
                            <li style="margin-bottom: 10px;">
                                <a href="#" style="color: rgba(255, 255, 255, 0.7); text-decoration: none; transition: color 0.3s ease;">API接口</a>
                            </li>
                        </ul>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="6">
                        <h4 style="font-size: 1.2rem; font-weight: 600; margin-bottom: 20px; color: #00d4ff;">支持</h4>
                        <ul style="list-style: none;">
                            <li style="margin-bottom: 10px;">
                                <a href="#" style="color: rgba(255, 255, 255, 0.7); text-decoration: none; transition: color 0.3s ease;">帮助中心</a>
                            </li>
                            <li style="margin-bottom: 10px;">
                                <a href="#" style="color: rgba(255, 255, 255, 0.7); text-decoration: none; transition: color 0.3s ease;">联系客服</a>
                            </li>
                            <li style="margin-bottom: 10px;">
                                <a href="#" style="color: rgba(255, 255, 255, 0.7); text-decoration: none; transition: color 0.3s ease;">故障报告</a>
                            </li>
                        </ul>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="6">
                        <h4 style="font-size: 1.2rem; font-weight: 600; margin-bottom: 20px; color: #00d4ff;">关注我们</h4>
                        <div style="display: flex; gap: 15px;">
                            <a
                                v-for="social in socialLinks"
                                :key="social.name"
                                :href="social.url"
                                style="width: 40px; height: 40px; background: rgba(255, 255, 255, 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: #fff; text-decoration: none; transition: all 0.3s ease;"
                                @mouseenter="$event.target.style.background = '#6366f1'"
                                @mouseleave="$event.target.style.background = 'rgba(255, 255, 255, 0.1)'"
                            >
                                <i :class="social.icon"></i>
                            </a>
                        </div>
                    </el-col>
                </el-row>
                <div style="text-align: center; padding-top: 20px; border-top: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.7);">
                    <p>&copy; 2024 飞鸟VPN. 保留所有权利。</p>
                </div>
            </div>
        </footer>


    </div>

    <!-- Vue 3 -->
    <script src="js/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="js/element-plus.js"></script>
    <!-- Element Plus 中文语言包 -->
    <script src="js/element-plus-locale-zh-cn.js"></script>

    <script>
        const { createApp, ref, reactive, onMounted, nextTick } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        createApp({
            setup() {
                // 响应式数据
                const activeIndex = ref('home');
                const heroTitle = ref('飞鸟加速器，永远能连上的海外加速器');
                const currentServer = ref('美国');
                const activeServerId = ref(1);
                const submitting = ref(false);

                // 功能特性数据
                const features = reactive([
                    { icon: 'shield', text: '自主研发协议，全新协议，告别服务器操作' },
                    { icon: 'network-wired', text: '智能连接64个从未中断过' },
                    { icon: 'bolt', text: '无需流量，秒开海外高清视频' },
                    { icon: 'users', text: '累计超过3000万用户' }
                ]);

                // 详细功能特性
                const detailedFeatures = reactive([
                    {
                        icon: 'lock',
                        title: '军用级加密',
                        description: '采用AES-256加密技术，确保您的数据绝对安全'
                    },
                    {
                        icon: 'bolt',
                        title: '极速连接',
                        description: '全球优化服务器，提供最快的连接速度'
                    },
                    {
                        icon: 'eye-slash',
                        title: '零日志政策',
                        description: '严格的无日志政策，保护您的隐私不被追踪'
                    },
                    {
                        icon: 'globe',
                        title: '全球服务器',
                        description: '覆盖50+国家，100+服务器节点'
                    },
                    {
                        icon: 'mobile',
                        title: '多平台支持',
                        description: '支持Windows、Mac、iOS、Android等所有平台'
                    },
                    {
                        icon: 'headset',
                        title: '24/7客服',
                        description: '全天候专业客服支持，随时为您解决问题'
                    }
                ]);

                // 服务器数据
                const servers = reactive([
                    { id: 1, name: '美国 - 洛杉矶', flag: '🇺🇸' },
                    { id: 2, name: '日本 - 东京', flag: '🇯🇵' },
                    { id: 3, name: '新加坡', flag: '🇸🇬' }
                ]);

                // 全球服务器 - 对应地图位置
                const globalServers = reactive([
                    { id: 1, country: '美国', position: { top: '30%', left: '18%' } },
                    { id: 2, country: '德国', position: { top: '22%', left: '47%' } },
                    { id: 3, country: '日本', position: { top: '28%', left: '82%' } },
                    { id: 4, country: '新加坡', position: { top: '48%', left: '70%' } },
                    { id: 5, country: '澳大利亚', position: { top: '72%', left: '77%' } },
                    { id: 6, country: '巴西', position: { top: '58%', left: '22%' } },
                    { id: 7, country: '南非', position: { top: '65%', left: '48%' } },
                    { id: 8, country: '印度', position: { top: '42%', left: '62%' } }
                ]);

                // 价格方案
                const pricingPlans = reactive([
                    {
                        name: '基础版',
                        price: 29,
                        popular: false,
                        features: [
                            '1个设备同时连接',
                            '基础服务器访问',
                            '标准加密',
                            '邮件支持'
                        ]
                    },
                    {
                        name: '专业版',
                        price: 59,
                        popular: true,
                        features: [
                            '5个设备同时连接',
                            '全部服务器访问',
                            '军用级加密',
                            '24/7客服支持',
                            '无限带宽'
                        ]
                    },
                    {
                        name: '企业版',
                        price: 99,
                        popular: false,
                        features: [
                            '无限设备连接',
                            '专用服务器',
                            '高级安全功能',
                            '优先客服支持',
                            '团队管理'
                        ]
                    }
                ]);

                // 统计数据
                const stats = reactive({
                    servers: 100,
                    countries: 50,
                    uptime: 99.9
                });

                // 联系信息
                const contactInfo = reactive({
                    email: '<EMAIL>',
                    phone: '+86 ************',
                    hours: '24/7 在线支持'
                });

                // 社交媒体链接
                const socialLinks = reactive([
                    { name: 'Twitter', icon: 'twitter', url: '#' },
                    { name: 'Facebook', icon: 'facebook', url: '#' },
                    { name: 'Instagram', icon: 'instagram', url: '#' },
                    { name: 'LinkedIn', icon: 'linkedin', url: '#' }
                ]);

                // 联系表单
                const contactForm = reactive({
                    name: '',
                    email: '',
                    message: ''
                });

                // 表单验证规则
                const contactRules = reactive({
                    name: [
                        { required: true, message: '请输入您的姓名', trigger: 'blur' }
                    ],
                    email: [
                        { required: true, message: '请输入邮箱地址', trigger: 'blur' },
                        { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
                    ],
                    message: [
                        { required: true, message: '请输入您的消息', trigger: 'blur' }
                    ]
                });



                // 方法
                const handleSelect = (key) => {
                    activeIndex.value = key;
                    // 平滑滚动到对应区域
                    const element = document.getElementById(key);
                    if (element) {
                        element.scrollIntoView({ behavior: 'smooth' });
                    }
                };

                const handleDownload = () => {
                    window.open('download.html', '_blank');
                };

                const switchServer = (server) => {
                    activeServerId.value = server.id;
                    currentServer.value = server.name.split(' - ')[0];
                    ElMessage.success(`已切换到${server.name}`);
                };

                const selectPlan = (plan) => {
                    ElMessageBox.confirm(
                        `您选择了${plan.name}，价格为¥${plan.price}/月，是否继续？`,
                        '确认选择',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'info'
                        }
                    ).then(() => {
                        ElMessage.success('感谢您的选择！我们会尽快联系您。');
                    }).catch(() => {
                        ElMessage.info('已取消选择');
                    });
                };

                const showServerInfo = (server) => {
                    ElMessage.info(`${server.country}服务器 - 延迟: 低 | 负载: 正常`);
                };

                const submitContactForm = () => {
                    // 这里应该有表单验证逻辑
                    submitting.value = true;

                    // 模拟提交
                    setTimeout(() => {
                        submitting.value = false;
                        ElMessage.success('消息已发送，我们会尽快回复您！');
                        // 重置表单
                        Object.keys(contactForm).forEach(key => {
                            contactForm[key] = '';
                        });
                    }, 2000);
                };

                const handleCardHover = (event, isEntering) => {
                    const card = event.target;
                    if (isEntering) {
                        card.style.transform = 'translateY(-10px) scale(1.02)';
                        card.style.boxShadow = '0 30px 60px rgba(0, 0, 0, 0.15)';

                        // 如果是推荐卡片，添加特殊效果
                        if (card.classList.contains('featured')) {
                            card.style.boxShadow = '0 35px 70px rgba(99, 102, 241, 0.3), 0 0 0 1px rgba(99, 102, 241, 0.2)';
                        }

                        // 添加轻微的旋转效果
                        card.style.transformStyle = 'preserve-3d';
                        card.style.transform += ' rotateX(2deg)';
                    } else {
                        card.style.transform = 'translateY(0) scale(1)';
                        card.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.1)';

                        if (card.classList.contains('featured')) {
                            card.style.boxShadow = '0 25px 50px rgba(99, 102, 241, 0.2), 0 0 0 1px rgba(99, 102, 241, 0.1)';
                        }
                    }
                };

                const handleServerHover = (event, server, isEntering) => {
                    const serverNode = event.currentTarget;
                    const tooltip = serverNode.querySelector('.server-tooltip');
                    const core = serverNode.querySelector('.server-core') || serverNode.children[serverNode.children.length - 1];

                    if (isEntering) {
                        // 显示提示框
                        if (tooltip) {
                            tooltip.style.opacity = '1';
                            tooltip.style.visibility = 'visible';
                            tooltip.style.transform = 'translateX(-50%) translateY(-5px)';
                        }

                        // 放大核心点
                        if (core) {
                            core.style.transform = 'scale(1.3)';
                            core.style.boxShadow = '0 0 30px rgba(99, 102, 241, 0.8), 0 4px 15px rgba(0, 0, 0, 0.3)';
                        }

                        // 添加类名用于CSS动画
                        serverNode.classList.add('server-node');
                    } else {
                        // 隐藏提示框
                        if (tooltip) {
                            tooltip.style.opacity = '0';
                            tooltip.style.visibility = 'hidden';
                            tooltip.style.transform = 'translateX(-50%) translateY(0)';
                        }

                        // 恢复核心点
                        if (core) {
                            core.style.transform = 'scale(1)';
                            core.style.boxShadow = '0 0 20px rgba(99, 102, 241, 0.6), 0 2px 10px rgba(0, 0, 0, 0.2)';
                        }

                        serverNode.classList.remove('server-node');
                    }
                };



                // 生命周期
                onMounted(() => {
                    // 页面加载动画等初始化逻辑
                    console.log('飞鸟VPN应用已加载');

                    // 隐藏加载动画
                    setTimeout(() => {
                        const loadingOverlay = document.getElementById('loadingOverlay');
                        if (loadingOverlay) {
                            loadingOverlay.classList.add('hidden');
                        }
                    }, 1500);

                    // 添加滚动视差效果
                    window.addEventListener('scroll', () => {
                        const scrolled = window.pageYOffset;
                        const parallax = document.querySelectorAll('.gradient-blob');
                        const speed = 0.5;

                        parallax.forEach((element, index) => {
                            const yPos = -(scrolled * speed * (index + 1) * 0.3);
                            element.style.transform = `translate3d(0, ${yPos}px, 0)`;
                        });
                    });

                    // 添加元素进入视口动画
                    const observerOptions = {
                        threshold: 0.1,
                        rootMargin: '0px 0px -50px 0px'
                    };

                    const observer = new IntersectionObserver((entries) => {
                        entries.forEach(entry => {
                            if (entry.isIntersecting) {
                                entry.target.style.opacity = '1';
                                entry.target.style.transform = 'translateY(0)';
                            }
                        });
                    }, observerOptions);

                    // 观察所有卡片元素
                    nextTick(() => {
                        const cards = document.querySelectorAll('.el-card');
                        cards.forEach(card => {
                            card.style.opacity = '0';
                            card.style.transform = 'translateY(30px)';
                            card.style.transition = 'all 0.6s ease';
                            observer.observe(card);
                        });
                    });

                    // 添加鼠标跟随光标效果
                    const cursor = document.createElement('div');
                    cursor.className = 'cursor-glow';
                    cursor.style.cssText = `
                        position: fixed;
                        width: 20px;
                        height: 20px;
                        background: radial-gradient(circle, rgba(99, 102, 241, 0.3) 0%, transparent 70%);
                        border-radius: 50%;
                        pointer-events: none;
                        z-index: 9998;
                        transition: transform 0.1s ease;
                        mix-blend-mode: screen;
                    `;
                    document.body.appendChild(cursor);

                    document.addEventListener('mousemove', (e) => {
                        cursor.style.left = e.clientX - 10 + 'px';
                        cursor.style.top = e.clientY - 10 + 'px';
                    });

                    // 鼠标悬停在可点击元素上时放大光标
                    const clickableElements = document.querySelectorAll('button, a, .el-button, .el-card');
                    clickableElements.forEach(element => {
                        element.addEventListener('mouseenter', () => {
                            cursor.style.transform = 'scale(2)';
                            cursor.style.background = 'radial-gradient(circle, rgba(99, 102, 241, 0.5) 0%, transparent 70%)';
                        });
                        element.addEventListener('mouseleave', () => {
                            cursor.style.transform = 'scale(1)';
                            cursor.style.background = 'radial-gradient(circle, rgba(99, 102, 241, 0.3) 0%, transparent 70%)';
                        });
                    });
                });

                return {
                    activeIndex,
                    heroTitle,
                    currentServer,
                    activeServerId,
                    submitting,
                    features,
                    detailedFeatures,
                    servers,
                    globalServers,
                    pricingPlans,
                    stats,
                    contactInfo,
                    socialLinks,
                    contactForm,
                    contactRules,
                    handleSelect,
                    handleDownload,
                    switchServer,
                    selectPlan,
                    showServerInfo,
                    submitContactForm,
                    handleCardHover,
                    handleServerHover
                };
            }
        }).use(ElementPlus, {
            locale: ElementPlusLocaleZhCn
        }).mount('#app');
    </script>
</body>
</html>
