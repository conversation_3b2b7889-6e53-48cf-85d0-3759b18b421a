<?php

namespace plugin\Invitationcode\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;
use think\facade\Request;
use think\response\Json;

class Api extends BasePlugin
{
    protected $scene = ['admin']; // 仅管理员可访问
    
    protected $noNeedLogin = ['index']; // index方法无需鉴权

    // 显示页面
    public function index()
    {
        return View::fetch();
    }

    // 获取商家邀请码统计数据
    public function getInviteCodes(): Json
    {
        try {
            $sortType = Request::param('sort_type', 'total_count');
            $page = Request::param('page', 1);
            $pageSize = Request::param('page_size', 15);
            
            // 验证排序字段
            if (!in_array($sortType, ['total_count', 'used_count', 'unused_count', 'success_rate'])) {
                $sortType = 'total_count';
            }

            // 获取统计数据
            $query = Db::name('user_invite_code')
                ->alias('i')
                ->join('user u', 'u.id = i.user_id', 'LEFT')
                ->field([
                    'i.user_id',
                    'u.username',
                    'COUNT(*) as total_count',
                    'SUM(CASE WHEN i.status = 0 THEN 1 ELSE 0 END) as unused_count',
                    'SUM(CASE WHEN i.status = 1 THEN 1 ELSE 0 END) as used_count'
                ])
                ->group('i.user_id');

            // 执行查询
            $statsData = $query->select()->each(function($item) {
                if ($item['user_id'] == 0) {
                    $item['username'] = '管理员';
                } elseif (empty($item['username'])) {
                    $item['username'] = '未知用户';
                }
                
                // 数据类型转换
                $item['total_count'] = (int)$item['total_count'];
                $item['unused_count'] = (int)$item['unused_count'];
                $item['used_count'] = (int)$item['used_count'];
                
                // 计算成功率
                $item['success_rate'] = $item['total_count'] > 0 
                    ? round(($item['used_count'] / $item['total_count']) * 100, 2)
                    : 0;
                    
                return $item;
            })->toArray();

            // 根据选择的字段排序
            usort($statsData, function($a, $b) use ($sortType) {
                if ($sortType === 'success_rate') {
                    return $b['success_rate'] <=> $a['success_rate'];
                }
                return $b[$sortType] <=> $a[$sortType];
            });

            // 分页处理
            $total = count($statsData);
            $offset = ($page - 1) * $pageSize;
            $list = array_slice($statsData, $offset, $pageSize);

            // 调试输出
            trace('Query result: ' . json_encode([
                'total' => $total,
                'list' => $list
            ]), 'debug');
            
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => [
                    'list' => $list,
                    'total' => $total
                ]
            ]);
        } catch (\Exception $e) {
            trace('Error: ' . $e->getMessage(), 'error');
            return json(['code' => 500, 'msg' => '错误: ' . $e->getMessage()]);
        }
    }

    // 删除指定商家的未使用邀请码
    public function deleteUnusedCodes(): Json
    {
        if (!Request::isPost()) {
            return json(['code' => 405, 'msg' => '请求方式无效']);
        }

        try {
            $userId = Request::param('user_id');
            
            if (!is_numeric($userId)) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }

            // 删除未使用的邀请码
            $result = Db::name('user_invite_code')
                ->where('user_id', $userId)
                ->where('status', 0)  // 未使用状态
                ->delete();

            return json([
                'code' => 200,
                'msg' => '删除成功',
                'data' => ['affected_rows' => $result]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '错误: ' . $e->getMessage()]);
        }
    }
}
