<?php

namespace plugin\Imagesxuanfu\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;

class Admin extends BasePlugin {
    
    // 指定只有管理员可以访问
    protected $scene = ['admin'];
    
    // 指定不需要登录验证的方法
    protected $noNeedLogin = [];

    public function index() {
        return View::fetch();
    }

    // 获取默认配置
    public function getConfig() {
        try {
            $merchant_can_edit_raw = plugconf("Imagesxuanfu.merchant_can_edit");
            $merchant_can_edit = $merchant_can_edit_raw === null ? 1 : intval($merchant_can_edit_raw);
            
            $data = [
                'status' => intval(plugconf("Imagesxuanfu.default_status") ?? 0),
                'image_url' => (string)(plugconf("Imagesxuanfu.default_image_url") ?? ''),
                'popup_image_url' => (string)(plugconf("Imagesxuanfu.default_popup_image_url") ?? ''),
                'link_url' => (string)(plugconf("Imagesxuanfu.default_link_url") ?? ''),
                'width' => intval(plugconf("Imagesxuanfu.default_width") ?? 200),
                'height' => intval(plugconf("Imagesxuanfu.default_height") ?? 200),
                'bottom' => intval(plugconf("Imagesxuanfu.default_bottom") ?? 20),
                'right' => intval(plugconf("Imagesxuanfu.default_right") ?? 20),
                'min_size' => intval(plugconf("Imagesxuanfu.default_min_size") ?? 200),
                'max_size' => intval(plugconf("Imagesxuanfu.default_max_size") ?? 500),
                'popup_title' => (string)(plugconf("Imagesxuanfu.default_popup_title") ?? '扫描二维码'),
                'popup_footer' => (string)(plugconf("Imagesxuanfu.default_popup_footer") ?? '请使用扫码软件扫描'),
                'merchant_can_edit' => $merchant_can_edit
            ];

            // 如果没有设置弹窗图片，默认使用悬浮框图片
            if (empty($data['popup_image_url'])) {
                $data['popup_image_url'] = $data['image_url'];
            }

            return json(['code' => 200, 'msg' => '获取成功', 'data' => $data]);
        } catch (\Exception $e) {
            \think\facade\Log::error('GetConfig error: ' . $e->getMessage());
            return json(['code' => 0, 'msg' => '获取数据失败：' . $e->getMessage()]);
        }
    }

    // 保存默认配置
    public function saveConfig() {
        try {
            // 获取表单数据并确保类型正确
            $status = intval($this->request->post('status') ?? 0);
            $image_url = trim((string)$this->request->post('image_url'));
            $popup_image_url = trim((string)$this->request->post('popup_image_url'));
            $link_url = trim((string)$this->request->post('link_url'));
            $popup_title = trim((string)$this->request->post('popup_title', '扫描二维码'));
            $popup_footer = trim((string)$this->request->post('popup_footer', '请使用扫码软件扫描'));
            
            // 确保merchant_can_edit值为0或1
            $merchant_can_edit = $this->request->post('merchant_can_edit');
            $merchant_can_edit = $merchant_can_edit === null ? 1 : intval($merchant_can_edit);
            $merchant_can_edit = $merchant_can_edit == 1 ? 1 : 0; // 强制转为0或1
            
            // 获取并验证尺寸数据
            $min_size = intval($this->request->post('min_size') ?? 200);
            $max_size = intval($this->request->post('max_size') ?? 500);
            
            // 验证尺寸范围
            if ($min_size < 50 || $min_size > 300) {
                return json(['code' => 0, 'msg' => '最小尺寸必须在50-300像素之间']);
            }
            if ($max_size < 300 || $max_size > 800) {
                return json(['code' => 0, 'msg' => '最大尺寸必须在300-800像素之间']);
            }
            if ($max_size <= $min_size) {
                return json(['code' => 0, 'msg' => '最大尺寸必须大于最小尺寸']);
            }

            // 获取并验证宽高
            $width = min(max(intval($this->request->post('width') ?? 200), $min_size), $max_size);
            $height = min(max(intval($this->request->post('height') ?? 200), $min_size), $max_size);
            
            // 获取位置数据
            $bottom = min(max(intval($this->request->post('bottom') ?? 20), 0), 1000);
            $right = min(max(intval($this->request->post('right') ?? 20), 0), 1000);

            // 验证必填项
            if (empty($image_url) && $status == 1) {
                return json(['code' => 0, 'msg' => '请先上传悬浮框图片']);
            }

            // 如果弹窗图片为空，使用悬浮框图片
            if (empty($popup_image_url)) {
                $popup_image_url = $image_url;
            }

            // 保存所有配置
            plugconf("Imagesxuanfu.default_status", $status);
            plugconf("Imagesxuanfu.default_image_url", $image_url);
            plugconf("Imagesxuanfu.default_popup_image_url", $popup_image_url);
            plugconf("Imagesxuanfu.default_link_url", $link_url);
            plugconf("Imagesxuanfu.default_width", $width);
            plugconf("Imagesxuanfu.default_height", $height);
            plugconf("Imagesxuanfu.default_bottom", $bottom);
            plugconf("Imagesxuanfu.default_right", $right);
            plugconf("Imagesxuanfu.default_min_size", $min_size);
            plugconf("Imagesxuanfu.default_max_size", $max_size);
            plugconf("Imagesxuanfu.default_popup_title", $popup_title);
            plugconf("Imagesxuanfu.default_popup_footer", $popup_footer);
            plugconf("Imagesxuanfu.merchant_can_edit", $merchant_can_edit);

            // 返回完整的配置数据
            return json([
                'code' => 200, 
                'msg' => '保存成功',
                'data' => [
                    'status' => $status,
                    'image_url' => $image_url,
                    'popup_image_url' => $popup_image_url,
                    'link_url' => $link_url,
                    'width' => $width,
                    'height' => $height,
                    'bottom' => $bottom,
                    'right' => $right,
                    'min_size' => $min_size,
                    'max_size' => $max_size,
                    'popup_title' => $popup_title,
                    'popup_footer' => $popup_footer,
                    'merchant_can_edit' => $merchant_can_edit
                ]
            ]);
        } catch (\Exception $e) {
            \think\facade\Log::error('SaveConfig error: ' . $e->getMessage());
            return json(['code' => 0, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    // 删除图片
    public function deleteImage() {
        try {
            $type = $this->request->post('type', 'main');
            
            if ($type == 'popup') {
                plugconf("Imagesxuanfu.default_popup_image_url", '');
                $msg = '删除弹窗图片成功';
            } else {
                plugconf("Imagesxuanfu.default_image_url", '');
                $msg = '删除悬浮框图片成功';
            }
            
            return json(['code' => 200, 'msg' => $msg]);
        } catch (\Exception $e) {
            \think\facade\Log::error('DeleteImage error: ' . $e->getMessage());
            return json(['code' => 0, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }
}
