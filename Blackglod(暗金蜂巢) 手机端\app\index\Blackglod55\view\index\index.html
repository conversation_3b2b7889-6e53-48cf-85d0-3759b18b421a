<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="referrer" content="no-referrer">
    <meta name="robots" content="noarchive">
    <meta http-equiv="Cache-Control" content="no-store">
    <title>{$title|default='Blackglod商城'}</title>
    <link rel="shortcut icon" href="/assets/plugin/Blackglod/plugin/Blackglod/images/favicon.ico" type="image/x-icon">
    {if !empty($favicon)}
    <link rel="shortcut icon" href="{$favicon}" type="image/x-icon">
    {/if}
    <link rel="stylesheet" href="/assets/plugin/Blackglod/plugin/Blackglod/css/all.min.css">
    <link rel="stylesheet" href="/assets/plugin/Blackglod/plugin/Blackglod/css/all.min1.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.css" />
    <style>
        /* 全局样式 */
        :root {
            --primary-gold: #B39B77;     /* 更深沉的金色 */
            --dark-gold: #8B7355;        /* 暗金色 */
            --light-gold: #D4B78F;       /* 浅金色 */
            --bg-dark: #151515;          /* 更深的背景色 */
            --bg-darker: #0A0A0A;        /* 最深的背景色 */
            --text-gold: #B39B77;        /* 文字金色 */
            --text-light: #E5E5E5;       /* 浅色文字 */
            --nav-font-size: 1rem;       /* 保持不变 */
        }

        body {
            background: linear-gradient(135deg, var(--bg-darker) 0%, var(--bg-dark) 100%);
            color: var(--text-light);
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            overflow-x: hidden;
        }

        /* 统一背景色样式 */
        .hero-section, .features-section, .stats-section, .contact-section, .footer {
            background: linear-gradient(135deg, var(--bg-darker) 0%, var(--bg-dark) 100%);
        }

        .stats-section {
            position: relative;
            padding: 3rem 0;
            overflow: hidden;
            width: 100%;
            margin: 0;
            border-top: none;
            border-bottom: none;
        }

        /* 导航栏样式优化 */
        .header {
            background: rgba(10, 10, 10, 0.85);
            border-bottom: 1px solid rgba(179, 155, 119, 0.2);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            padding: 0;
            position: fixed;
            width: 100%;
            top: 0;
            left: 0;
            z-index: 1000;
            height: 70px;
            margin: 0;
            transition: all 0.3s ease;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
        }
        
        /* 头部滚动效果 */
        .header-scrolled {
            height: 60px;
            background: rgba(10, 10, 10, 0.95);
            box-shadow: 0 5px 25px rgba(0, 0, 0, 0.4);
        }
        
        .header-scrolled .nav-link {
            font-size: 0.9rem;
        }
        
        .header-scrolled .logo-img {
            height: 30px;
        }
        
        .header-scrolled .btn-login, 
        .header-scrolled .btn-register {
            padding: 0.45rem 1.1rem;
            font-size: 0.9rem;
        }
        
        .header-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, rgba(10, 10, 10, 0.95), rgba(15, 15, 15, 0.95));
            z-index: -1;
        }
        
        .header-decorative-lines {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, 
                transparent 0%, 
                var(--primary-gold) 20%, 
                var(--light-gold) 50%, 
                var(--primary-gold) 80%, 
                transparent 100%);
            opacity: 0.6;
            z-index: 1;
        }

        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
            height: 100%;
            position: relative;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
            text-decoration: none;
            position: relative;
            z-index: 2;
        }

        .logo-img {
            height: 35px;
            transition: all 0.3s ease;
            filter: drop-shadow(0 0 5px rgba(212, 183, 143, 0.5));
        }
        
        .logo:hover .logo-img {
            transform: scale(1.05);
            filter: drop-shadow(0 0 8px rgba(212, 183, 143, 0.7));
        }

        .nav-links {
            display: flex;
            align-items: center;
            gap: 2.5rem;
            margin: 0;
            padding: 0;
            list-style: none;
            position: relative;
            z-index: 2;
        }

        .nav-item {
            position: relative;
        }
        
        .nav-link {
            color: #bbb;
            text-decoration: none;
            font-size: 0.95rem;
            font-weight: 500;
            letter-spacing: 0.5px;
            padding: 0.5rem 0;
            transition: all 0.3s ease;
            position: relative;
            display: flex;
            align-items: center;
        }
        
        .nav-link .dropdown-icon {
            margin-left: 5px;
            font-size: 0.7rem;
            transition: transform 0.3s ease;
            color: var(--primary-gold);
        }
        
        .nav-item:hover .dropdown-icon {
            transform: rotate(180deg);
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 50%;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-gold), var(--light-gold));
            transition: all 0.3s ease;
            transform: translateX(-50%);
            opacity: 0;
            border-radius: 2px;
            box-shadow: 0 0 8px rgba(212, 183, 143, 0.6);
        }

        .nav-link:hover {
            color: var(--light-gold);
            text-shadow: 0 0 10px rgba(212, 183, 143, 0.3);
        }

        .nav-link:hover::after {
            width: 100%;
            opacity: 1;
        }
        
        /* 子菜单样式优化 */
        .submenu {
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%) translateY(15px);
            min-width: 200px;
            background: rgba(15, 15, 15, 0.95);
            border: 1px solid rgba(179, 155, 119, 0.2);
            border-radius: 8px;
            padding: 0.75rem 0;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1001;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3), 
                        0 0 15px rgba(179, 155, 119, 0.1);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            overflow: hidden;
        }
        
        .submenu::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, 
                rgba(179, 155, 119, 0.05) 0%, 
                rgba(15, 15, 15, 0) 100%);
            z-index: -1;
        }
        
        .nav-item:hover .submenu {
            opacity: 1;
            visibility: visible;
            transform: translateX(-50%) translateY(5px);
        }
        
        .submenu::after {
            content: '';
            position: absolute;
            top: -6px;
            left: 50%;
            transform: translateX(-50%);
            width: 12px;
            height: 12px;
            background: rgba(15, 15, 15, 0.95);
            border-left: 1px solid rgba(179, 155, 119, 0.2);
            border-top: 1px solid rgba(179, 155, 119, 0.2);
            transform: translateX(-50%) rotate(45deg);
            z-index: -1;
        }
        
        .submenu-item {
            display: block;
            padding: 0.75rem 1.5rem;
            color: #aaa;
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            position: relative;
            white-space: nowrap;
            overflow: hidden;
        }
        
        .submenu-item:hover {
            color: var(--light-gold);
            background: rgba(179, 155, 119, 0.08);
            padding-left: 1.75rem;
        }
        
        .submenu-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 60%;
            background: linear-gradient(90deg, var(--primary-gold), transparent);
            opacity: 0.3;
            transition: all 0.3s ease;
        }
        
        .submenu-item:hover::before {
            width: 5px;
        }

        /* 认证按钮区域优化 */
        .auth-buttons {
            display: flex;
            align-items: center;
            gap: 1rem;
            position: relative;
            z-index: 2;
        }

        .mobile-menu-toggle {
            display: none;
            background: transparent;
            border: none;
            color: var(--text-light);
            font-size: 1.2rem;
            cursor: pointer;
            padding: 0.5rem;
            transition: all 0.3s ease;
        }

        .mobile-menu-toggle:hover {
            color: var(--primary-gold);
        }

        .btn-login, .btn-register {
            padding: 0.5rem 1.25rem;
            font-size: 0.95rem;
            text-decoration: none;
            border-radius: 6px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
            letter-spacing: 0.5px;
            position: relative;
            overflow: hidden;
        }

        .btn-login {
            border: 1.5px solid var(--primary-gold);
            color: var(--primary-gold);
            background: transparent;
        }

        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, 
                rgba(179, 155, 119, 0.1), 
                rgba(179, 155, 119, 0.2), 
                rgba(179, 155, 119, 0.1));
            transition: all 0.6s ease;
            z-index: -1;
        }

        .btn-login:hover {
            color: var(--light-gold);
            border-color: var(--light-gold);
            box-shadow: 0 0 15px rgba(179, 155, 119, 0.3);
            transform: translateY(-2px);
        }

        .btn-login:hover::before {
            left: 100%;
        }

        .btn-register {
            background: linear-gradient(135deg, var(--primary-gold), var(--dark-gold));
            border: none;
            color: #111;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            position: relative;
            z-index: 1;
        }

        .btn-register::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, var(--dark-gold), var(--primary-gold));
            opacity: 0;
            transition: all 0.3s ease;
            z-index: -1;
            border-radius: 6px;
        }

        .btn-register:hover {
            color: #000;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
        }

        .btn-register:hover::before {
            opacity: 1;
        }

        /* 响应式调整 */
        @media (max-width: 992px) {
            .nav-links {
                display: none;
                position: absolute;
                top: 70px;
                left: 0;
                width: 100%;
                background: rgba(10, 10, 10, 0.95);
                flex-direction: column;
                gap: 0;
                padding: 1rem 0;
                border-bottom: 1px solid rgba(179, 155, 119, 0.1);
                backdrop-filter: blur(15px);
                -webkit-backdrop-filter: blur(15px);
                box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
                z-index: 999;
            }
            
            .nav-links.show {
                display: flex;
            }
            
            .nav-item {
                width: 100%;
            }
            
            .nav-link {
                padding: 1rem 2rem;
                width: 100%;
                justify-content: space-between;
                border-bottom: 1px solid rgba(179, 155, 119, 0.1);
            }
            
            .nav-link::after {
                display: none;
            }
            
            .submenu {
                position: relative;
                top: auto;
                left: auto;
                transform: none;
                width: 100%;
                min-width: auto;
                border-radius: 0;
                box-shadow: none;
                border: none;
                border-top: 1px solid rgba(179, 155, 119, 0.1);
                padding: 0;
                max-height: 0;
                overflow: hidden;
                visibility: visible;
                opacity: 1;
                background: rgba(0, 0, 0, 0.2);
            }
            
            .submenu::before, .submenu::after {
                display: none;
            }
            
            .nav-item.active .submenu {
                max-height: 300px;
                padding: 0.5rem 0;
            }
            
            .submenu-item {
                padding: 0.75rem 3rem;
            }
            
            .mobile-menu-toggle {
                display: flex;
            }
            
            .auth-buttons {
                gap: 0.75rem;
            }
            
            .btn-login, .btn-register {
                padding: 0.5rem 1rem;
                font-size: 0.85rem;
            }
            
            .btn-login i, .btn-register i {
                display: none;
            }
        }
        
        @media (max-width: 576px) {
            .nav-container {
                padding: 0 1rem;
            }
            
            .btn-login, .btn-register {
                padding: 0.5rem 0.75rem;
                font-size: 0.8rem;
            }
        }

        /* 首屏区域样式优化 */
        .hero-section {
            position: relative;
            min-height: calc(100vh - 70px); /* 减去header高度 */
            display: flex;
            align-items: center;
            padding: 0 2rem;
            background: linear-gradient(135deg, var(--bg-darker) 0%, var(--bg-dark) 100%);
            overflow: hidden;
            margin: 0;
        }

        .hero-content {
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
            z-index: 2;
        }

        .hero-title {
            position: relative;
            font-size: 4rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-gold), var(--light-gold));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            margin-bottom: 1.5rem;
            overflow: hidden;  /* 确保闪光不会溢出 */
        }

        /* 更新闪光效果 */
        .hero-title::before {
            content: '';
            position: absolute;
            top: -100%;
            left: -100%;
            width: 120%;
            height: 300%;
            background: linear-gradient(
                45deg,
                transparent 0%,
                transparent 30%,
                rgba(255, 255, 255, 0.8) 50%,
                transparent 70%,
                transparent 100%
            );
            transform: rotate(45deg);
            animation: logoShine 4s ease-in-out infinite;
        }

        @keyframes logoShine {
            0% {
                transform: translateX(-200%) rotate(45deg);
            }
            15%, 100% {
                transform: translateX(100%) rotate(45deg);
            }
        }

        .hero-subtitle {
            font-size: 1.2rem;
            color: #999;
            max-width: 600px;
            line-height: 1.8;
            margin-bottom: 2rem;
            animation: subtitleFade 1s ease-out 0.3s forwards;
            opacity: 0;
        }

        .hero-buttons {
            display: flex;
            gap: 1.5rem;
            animation: buttonsFade 1s ease-out 0.6s forwards;
            opacity: 0;
        }

        /* 动画效果 */
        @keyframes titleFade {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes subtitleFade {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes buttonsFade {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 蜂窝背景优化 */
        .honeycomb-bg {
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 600px;
            height: 600px;
            opacity: 0.1;
            pointer-events: none;
        }

        .hex {
            position: absolute;
            background: linear-gradient(135deg, var(--primary-gold), var(--dark-gold));
            clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);
        }

        .decorative-lines {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(90deg, rgba(179, 155, 119, 0.03) 1px, transparent 1px),
                linear-gradient(0deg, rgba(179, 155, 119, 0.03) 1px, transparent 1px);
            background-size: 50px 50px;
            z-index: 1;
        }

        .container {
            position: relative;
            z-index: 2;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            margin-top: -80px;
        }

        .hero-title {
            font-size: 4.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            line-height: 1.2;
            background: linear-gradient(135deg, var(--primary-gold), var(--light-gold));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            text-shadow: 0 0 30px rgba(179, 155, 119, 0.2);
        }

        .hero-subtitle {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #999;
            margin-bottom: 2rem;
            max-width: 580px;
        }

        .hero-buttons {
            display: flex;
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .hero-buttons .btn-register,
        .hero-buttons .btn-login {
            padding: 0.8rem 2rem;
            border-radius: 4px;
            font-size: 1rem;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .hero-buttons .btn-register {
            background: #C8A675;
            color: #000;
            border: none;
        }

        .hero-buttons .btn-login {
            background: transparent;
            color: #C8A675;
            border: 1px solid #C8A675;
        }

        .hero-buttons .btn-register:hover,
        .hero-buttons .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(200,166,117,0.2);
        }

        .hex-sphere-container {
            position: absolute;
            right: 10%;
            top: 50%;
            transform: translateY(-50%);
            width: 300px;
            height: 300px;
            perspective: 1200px;
            z-index: 3;
            transform-style: preserve-3d;
        }

        .honeycomb {
            position: relative;
            width: 100%;
            height: 100%;
            display: grid;
            grid-template-columns: repeat(4, 60px);
            grid-gap: 8px;
            transform: rotate(30deg) rotateX(10deg) rotateY(10deg);
            transform-style: preserve-3d;
            animation: honeyRotate 20s infinite linear;
        }

        @keyframes honeyRotate {
            0% {
                transform: rotate(30deg) rotateX(10deg) rotateY(10deg);
            }
            25% {
                transform: rotate(32deg) rotateX(12deg) rotateY(12deg);
            }
            50% {
                transform: rotate(30deg) rotateX(10deg) rotateY(14deg);
            }
            75% {
                transform: rotate(28deg) rotateX(8deg) rotateY(12deg);
            }
            100% {
                transform: rotate(30deg) rotateX(10deg) rotateY(10deg);
            }
        }

        .hex::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(135deg, 
                rgba(212,183,143,0.2) 0%, 
                rgba(212,183,143,0.1) 50%,
                rgba(212,183,143,0.2) 100%);
            clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .hex:hover::before {
            opacity: 1;
        }
        
        /* 光线效果 */
        .hex::after {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            background: radial-gradient(circle at center, 
                rgba(212,183,143,0.3) 0%, 
                transparent 70%);
            opacity: 0;
            transition: opacity 0.4s ease;
            pointer-events: none;
        }
        
        .hex:hover::after {
            opacity: 1;
        }
        
        .hex {
            position: relative;
            width: 60px;
            height: 52px;
            background: linear-gradient(
                135deg,
                rgba(28, 28, 30, 0.95) 0%,
                rgba(20, 20, 22, 0.98) 100%
            );
            clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);
            border: 1px solid rgba(212,183,143,0.25);
            transition: all 0.4s ease;
            transform-style: preserve-3d;
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
            backface-visibility: hidden;
        }

        .hex:hover {
            background: linear-gradient(
                135deg,
                rgba(40, 40, 44, 0.95) 0%,
                rgba(30, 30, 33, 0.98) 100%
            );
            border-color: rgba(212,183,143,0.6);
            transform: scale(1.1) translateZ(15px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.3), 0 0 10px rgba(212,183,143,0.3);
            z-index: 10;
        }
        
        .hex-icon {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            z-index: 2;
        }
        
        .hex-icon i {
            font-size: 1.4rem;
            color: rgba(212,183,143,0.8);
            transition: all 0.3s ease;
            text-shadow: 0 0 5px rgba(212,183,143,0.2);
        }
        
        .hex:hover .hex-icon i {
            color: rgba(212,183,143,1);
            text-shadow: 0 0 15px rgba(212,183,143,0.5);
            transform: scale(1.2);
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: scale(0.5) translateY(20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        @keyframes hexFloat {
            0% {
                transform: translateY(0) translateZ(0) rotate(0deg);
            }
            50% {
                transform: translateY(-5px) translateZ(5px) rotate(1deg);
            }
            100% {
                transform: translateY(0) translateZ(0) rotate(0deg);
            }
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(-20px);
            }
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .hero-title {
                font-size: 3.5rem;
            }
            
            .hex-sphere-container {
                width: 400px;
                height: 400px;
                opacity: 0.6;
            }
        }

        @media (max-width: 768px) {
            .container {
                margin-top: -40px;
                align-items: center;
                text-align: center;
            }

            .hero-title {
                font-size: 3rem;
            }

            .hero-subtitle {
                font-size: 1rem;
                padding: 0 1rem;
                text-align: center;
            }

            .hero-buttons {
                justify-content: center;
            }

            .hex-sphere-container {
                display: none;
            }
        }

        .stats-section {
            position: relative;
            padding: 3rem 0;
            overflow: hidden;
            width: 100%;
            margin: 0;
            border-top: none;
            border-bottom: none;
        }

        .container {
            width: 100%;
            max-width: 1200px; /* 减小最大宽度 */
            margin: 0 auto;
            padding: 0; /* 移除容器的内边距 */
        }

        .stats-grid {
            display: flex;
            justify-content: center; /* 改回居中对齐 */
            align-items: center;
            width: 100%;
            padding: 0 40px;
            gap: 4rem; /* 增加卡片之间的间距 */
        }

        .stat-card {
            flex: 0 1 240px; /* 稍微减小卡片宽度，为间距留出空间 */
            position: relative;
            background: rgba(10, 10, 10, 0.8);
            border: 1px solid rgba(179, 155, 119, 0.1);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            padding: 2rem 1.5rem;
            border-radius: 15px;
            text-align: center;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            overflow: hidden;
            margin: 0;
        }

        .stat-icon {
            position: relative;
            z-index: 2;
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .stat-icon i {
            background: linear-gradient(135deg, var(--primary-gold), var(--light-gold));
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            filter: drop-shadow(0 0 10px rgba(179, 155, 119, 0.3));
        }

        .stat-content {
            position: relative;
            z-index: 2;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-gold), var(--light-gold));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            margin-bottom: 0.5rem;
            white-space: nowrap;
        }

        .stat-label {
            color: var(--text-light);
            font-size: 1rem;
            opacity: 0.9;
        }

        /* 响应式设计调整 */
        @media (max-width: 1400px) {
            .stats-grid {
                gap: 3rem; /* 较大屏幕稍微减小间距 */
            }
        }

        @media (max-width: 1200px) {
            .stats-grid {
                gap: 2.5rem;
            }
            
            .stat-card {
                flex: 0 1 220px;
            }
        }

        @media (max-width: 992px) {
            .stats-grid {
                flex-wrap: wrap;
                justify-content: center;
                gap: 2rem;
                padding: 0 20px;
            }

            .stat-card {
                flex: 0 1 calc(50% - 2rem);
            }
        }

        @media (max-width: 576px) {
            .stats-grid {
                flex-direction: column;
                padding: 0 15px;
            }

            .stat-card {
                flex: 0 1 100%;
                width: 100%;
            }
        }

        /* 保持其他样式不变 */
        .honeycomb-bg,
        .hero-bg,
        .decorative-lines {
            /* 保持原有样式 */
        }

        /* 悬浮效果 */
        .stat-card:hover {
            transform: translateY(-5px);
            border-color: rgba(179, 155, 119, 0.3);
            box-shadow: 
                0 20px 40px rgba(0, 0, 0, 0.3),
                0 0 30px rgba(179, 155, 119, 0.2);
        }

        .stat-card:hover .stat-icon i {
            transform: scale(1.1);
        }

        /* 添加分隔线 */
        @media (min-width: 993px) {
            .stat-card:not(:last-child)::after {
                content: '';
                position: absolute;
                right: -1rem;
                top: 50%;
                transform: translateY(-50%);
                height: 60%;
                width: 1px;
                background: linear-gradient(
                    to bottom,
                    transparent,
                    rgba(179, 155, 119, 0.1),
                    transparent
                );
            }
        }

        .features-section {
            position: relative;
            padding: 6rem 0;
            background: linear-gradient(135deg, var(--bg-darker) 0%, var(--bg-dark) 100%);
            overflow: hidden;
        }

        .section-header {
            text-align: center;
            margin-bottom: 2rem; /* 减小下边距，从5rem改为2rem */
            position: relative;
        }

        .section-title {
            font-size: 2.2rem; /* 减小字体大小，从3rem改为2.2rem */
            margin-bottom: 0.5rem; /* 减小下边距，从1rem改为0.5rem */
            position: relative;
            display: inline-block;
            background: linear-gradient(135deg, var(--primary-gold), var(--light-gold));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            text-shadow: 0 0 30px rgba(179, 155, 119, 0.2);
            animation: titlePulse 3s ease-in-out infinite;
        }

        .section-subtitle {
            font-size: 1rem; /* 减小字体大小，从1.2rem改为1rem */
            color: rgba(179, 155, 119, 0.8);
            margin-bottom: 1rem; /* 减小下边距，从2rem改为1rem */
        }

        .title-decoration {
            position: absolute;
            bottom: -10px; /* 减小距离，从-20px改为-10px */
            left: 50%;
            transform: translateX(-50%);
            width: 60px; /* 减小宽度，从100px改为60px */
            height: 2px; /* 减小高度，从3px改为2px */
            background: linear-gradient(90deg, 
                transparent,
                var(--primary-gold),
                transparent
            );
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 3rem;
            padding: 0 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .feature-card {
            background: rgba(10, 10, 10, 0.8);
            border: 1px solid rgba(179, 155, 119, 0.1);
            border-radius: 20px;
            padding: 3rem 2rem;
            text-align: center;
            position: relative;
            backdrop-filter: blur(10px);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            overflow: hidden;
            transform-style: preserve-3d;
            perspective: 1000px;
        }

        .feature-icon-wrapper {
            position: relative;
            width: 100px;
            height: 100px;
            margin: 0 auto 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .feature-icon {
            position: relative;
            width: 70px;
            height: 70px;
            background: rgba(200,166,117,0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2;
            transition: all 0.4s ease;
        }

        .icon-ring {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 90px;
            height: 90px;
            border: 1px solid rgba(200,166,117,0.2);
            border-radius: 50%;
            animation: ringPulse 2s ease-out infinite;
        }

        .icon-ring::before {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            border: 1px solid rgba(200,166,117,0.1);
            border-radius: 50%;
        }

        .feature-icon i {
            font-size: 2rem;
            background: linear-gradient(135deg, var(--primary-gold), var(--light-gold));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            transition: all 0.4s ease;
        }

        .feature-title {
            font-size: 1.5rem;
            color: var(--primary-gold);
            margin-bottom: 1rem;
            position: relative;
        }

        .feature-desc {
            color: rgba(255,255,255,0.8);
            line-height: 1.6;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }

        .feature-hover-effect {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(
                circle at center,
                rgba(200,166,117,0.1) 0%,
                transparent 70%
            );
            opacity: 0;
            transition: opacity 0.4s ease;
        }

        /* 悬浮效果 */
        .feature-card:hover {
            transform: translateY(-10px);
            border-color: rgba(179, 155, 119, 0.3);
            box-shadow: 
                0 20px 40px rgba(0, 0, 0, 0.3),
                0 0 30px rgba(179, 155, 119, 0.2);
        }

        .feature-card:hover .feature-icon {
            transform: scale(1.1);
            background: rgba(200,166,117,0.15);
        }

        .feature-card:hover .feature-title {
            transform: translateZ(15px);
        }

        .feature-card:hover .feature-desc {
            transform: translateZ(10px);
        }

        .feature-card:hover .feature-hover-effect {
            opacity: 1;
        }

        .feature-card:hover .icon-ring {
            border-color: rgba(200,166,117,0.3);
        }

        .feature-card:hover .feature-icon i {
            transform: scale(1.1);
            filter: drop-shadow(0 0 10px rgba(200,166,117,0.3));
        }

        /* 动画效果 */
        @keyframes titlePulse {
            0%, 100% {
                opacity: 1;
                filter: drop-shadow(0 0 10px rgba(179, 155, 119, 0.3));
            }
            50% {
                opacity: 0.8;
                filter: drop-shadow(0 0 20px rgba(179, 155, 119, 0.5));
            }
        }

        @keyframes ringPulse {
            0% {
                transform: translate(-50%, -50%) scale(0.95);
                opacity: 0.5;
            }
            50% {
                transform: translate(-50%, -50%) scale(1.05);
                opacity: 0.3;
            }
            100% {
                transform: translate(-50%, -50%) scale(0.95);
                opacity: 0.5;
            }
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .features-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 2rem;
            }
        }

        @media (max-width: 768px) {
            .features-grid {
                grid-template-columns: 1fr;
                padding: 0 1rem;
            }
            
            .section-title {
                font-size: 2.5rem;
            }
            
            .feature-card {
                padding: 2rem;
            }
        }

        .footer {
            background: linear-gradient(to bottom, var(--bg-darker), rgba(0,0,0,0.98));
            border-top: 1px solid rgba(179, 155, 119, 0.1);
            padding: 6rem 0 2rem;
            position: relative;
            overflow: hidden;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: radial-gradient(
                var(--primary-gold) 1px,
                transparent 1px
            );
            background-size: 50px 50px;
            opacity: 0.03;
            pointer-events: none;
        }

        .footer-waves {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            overflow: hidden;
            line-height: 0;
        }

        .waves {
            position: relative;
            width: 100%;
            height: 50px;
        }

        .parallax > use {
            animation: wave-move 25s cubic-bezier(.55,.5,.45,.5) infinite;
        }

        .parallax > use:nth-child(1) {
            animation-delay: -2s;
            animation-duration: 7s;
        }

        .parallax > use:nth-child(2) {
            animation-delay: -3s;
            animation-duration: 10s;
        }

        .parallax > use:nth-child(3) {
            animation-delay: -4s;
            animation-duration: 13s;
        }

        @keyframes wave-move {
            0% {
                transform: translate3d(-90px,0,0);
            }
            100% { 
                transform: translate3d(85px,0,0);
            }
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 3rem;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            position: relative;
        }

        .footer-column {
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.6s ease forwards;
        }

        .footer-title {
            color: var(--primary-gold);
            font-size: 1.2rem;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.8rem;
            position: relative;
            padding-bottom: 0.5rem;
            text-shadow: 0 0 10px rgba(179, 155, 119, 0.2);
        }

        .footer-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 40px;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-gold), transparent);
        }

        .footer-title i {
            font-size: 1.1em;
            background: linear-gradient(135deg, var(--primary-gold), var(--light-gold));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }

        .footer-links {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .footer-link {
            position: relative;
            padding-left: 0;
            transition: all 0.3s ease;
            color: var(--text-light);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            opacity: 0.8;
        }

        .footer-link:hover {
            padding-left: 10px;
            opacity: 1;
            color: var(--primary-gold);
        }

        .footer-link .link-icon {
            transition: transform 0.3s ease;
        }

        .footer-link:hover .link-icon {
            transform: translateX(5px);
        }

        .footer-divider {
            width: 100%;
            height: 1px;
            background: linear-gradient(90deg, 
                transparent,
                rgba(179, 155, 119, 0.2),
                transparent
            );
            margin: 3rem 0;
        }

        .copyright {
            text-align: center;
            padding: 2rem 0;
            width: 100%;
        }

        .copyright-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1rem;
        }

        .copyright-text {
            color: var(--text-light);
            opacity: 0.8;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            font-size: 0.95rem;
            letter-spacing: 0.5px;
        }

        .beian-info {
            display: flex;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .beian-link {
            color: #666;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            opacity: 0.7;
        }

        .beian-link:hover {
            opacity: 1;
            transform: translateY(-2px);
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .beian-info {
                flex-direction: column;
                gap: 1rem;
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .footer-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 2rem;
            }
        }

        @media (max-width: 768px) {
            .footer {
                padding: 4rem 0 2rem;
            }

            .footer-grid {
                grid-template-columns: 1fr;
                text-align: center;
            }

            .footer-title {
                justify-content: center;
            }

            .footer-title::after {
                left: 50%;
                transform: translateX(-50%);
            }

            .footer-link {
                justify-content: center;
            }

            .beian-info {
                flex-direction: column;
                gap: 1rem;
            }

            .beian-link {
                justify-content: center;
            }
        }

        /* 联系区域整体样式优化 */
        .contact-section {
            padding: 2rem 0; /* 减小内边距，从6rem改为2rem */
            background: linear-gradient(135deg, var(--bg-darker) 0%, var(--bg-dark) 100%);
            position: relative;
            overflow: hidden;
            text-align: center; /* 添加居中对齐 */
            margin: 0;
            border-top: none;
        }
        
        /* 联系区域背景美化 */
        .contact-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 20%, rgba(179, 155, 119, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(179, 155, 119, 0.05) 0%, transparent 50%);
            z-index: 0;
        }
        
        /* 蜂窝背景图案 */
        .contact-section::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='28' height='49' viewBox='0 0 28 49'%3E%3Cg fill-rule='evenodd'%3E%3Cg id='hexagons' fill='%23B39B77' fill-opacity='0.04' fill-rule='nonzero'%3E%3Cpath d='M13.99 9.25l13 7.5v15l-13 7.5L1 31.75v-15l12.99-7.5zM3 17.9v12.7l10.99 6.34 11-6.35V17.9l-11-6.34L3 17.9zM0 15l12.98-7.5V0h-2v6.35L0 12.69v2.3zm0 18.5L12.98 41v8h-2v-6.85L0 35.81v-2.3zM15 0v7.5L27.99 15H28v-2.31h-.01L17 6.35V0h-2zm0 49v-8l12.99-7.5H28v2.31h-.01L17 42.15V49h-2z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            opacity: 0.25;
            z-index: 0;
            pointer-events: none;
            animation: backgroundShift 60s linear infinite;
        }

        /* 光晕效果 */
        .contact-glow {
            position: absolute;
            width: 150%;
            height: 150%;
            top: -25%;
            left: -25%;
            background: 
                radial-gradient(circle at 30% 30%, rgba(179, 155, 119, 0.05) 0%, transparent 40%),
                radial-gradient(circle at 70% 70%, rgba(179, 155, 119, 0.05) 0%, transparent 40%);
            filter: blur(50px);
            z-index: 1;
            pointer-events: none;
            animation: glowPulse 15s ease-in-out infinite alternate;
        }
        
        /* 浮动蜂窝装饰 */
        .hex-decoration {
            position: absolute;
            width: 60px;
            height: 52px;
            background: rgba(179, 155, 119, 0.03);
            clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);
            opacity: 0.2;
            pointer-events: none;
            z-index: 1;
        }
        
        .hex-decoration:nth-child(1) {
            top: 10%;
            left: 5%;
            animation: floatHex 8s ease-in-out infinite;
            animation-delay: 0s;
        }
        
        .hex-decoration:nth-child(2) {
            top: 70%;
            left: 10%;
            animation: floatHex 12s ease-in-out infinite;
            animation-delay: 1s;
        }
        
        .hex-decoration:nth-child(3) {
            top: 20%;
            right: 5%;
            animation: floatHex 10s ease-in-out infinite;
            animation-delay: 2s;
        }
        
        .hex-decoration:nth-child(4) {
            top: 60%;
            right: 10%;
            animation: floatHex 9s ease-in-out infinite; 
            animation-delay: 3s;
        }
        
        /* 背景移动动画 */
        @keyframes backgroundShift {
            0% {
                background-position: 0 0;
            }
            100% {
                background-position: 100px 100px;
            }
        }
        
        /* 光晕脉冲动画 */
        @keyframes glowPulse {
            0% {
                opacity: 0.4;
                transform: scale(1);
            }
            50% {
                opacity: 0.5;
                transform: scale(1.05);
            }
            100% {
                opacity: 0.4;
                transform: scale(1);
            }
        }
        
        /* 蜂窝浮动动画 */
        @keyframes floatHex {
            0%, 100% {
                transform: translateY(0) rotate(0deg);
            }
            50% {
                transform: translateY(-15px) rotate(5deg);
            }
        }

        /* 确保内容在背景之上 */
        .contact-section .container {
            position: relative;
            z-index: 2;
        }

        /* 工作时间样式优化 */
        .working-hours {
            max-width: 600px;
            margin: 0 auto 1.5rem; /* 减小底部外边距，从4rem改为1.5rem */
            padding: 0.8rem; /* 减小内边距，从1.5rem改为0.8rem */
            background: rgba(15, 15, 15, 0.6); /* 更深色背景 */
            border-radius: 15px;
            border: 1px solid rgba(200,166,117,0.15); /* 加强边框色 */
            position: relative;
            overflow: hidden;
            display: flex; /* 修改为flex布局 */
            justify-content: center; /* 水平居中 */
            align-items: center; /* 垂直居中 */
            box-shadow: 0 5px 15px rgba(0,0,0,0.2), 0 0 10px rgba(179, 155, 119, 0.1); /* 添加阴影 */
            backdrop-filter: blur(10px);
        }

        /* 联系卡片网格布局优化 */
        .contact-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem; /* 减小间距，从2rem改为1rem */
            margin: 0 auto 1.5rem; /* 减小底部外边距，从4rem改为1.5rem */
            max-width: 1200px; /* 添加最大宽度 */
            padding: 0 2rem; /* 添加内边距 */
        }

        /* 联系卡片样式优化 */
        .contact-item {
            background: rgba(15, 15, 15, 0.6); /* 更深色背景 */
            border: 1px solid rgba(200,166,117,0.15); /* 加强边框色 */
            border-radius: 15px;
            padding: 1.5rem 1.2rem; /* 减小内边距，从2.5rem 2rem改为1.5rem 1.2rem */
            text-align: center;
            backdrop-filter: blur(10px);
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2), 0 0 10px rgba(179, 155, 119, 0.1); /* 添加阴影 */
        }

        .contact-item-inner {
            position: relative;
            z-index: 2;
        }

        .contact-info:hover {
            background: rgba(200,166,117,0.2);
            transform: translateY(-2px);
        }

        .contact-title {
            font-size: 1.2rem; /* 减小字体大小，从1.4rem改为1.2rem */
            color: var(--primary-gold);
            margin: 0.8rem 0 0.6rem; /* 减少外边距，从1.5rem 0 1rem改为0.8rem 0 0.6rem */
            font-weight: 500;
        }

        .contact-info {
            color: rgba(255,255,255,0.9);
            font-size: 1rem; /* 减小字体大小，从1.1rem改为1rem */
            margin-bottom: 0.7rem; /* 减少底部外边距，从1rem改为0.7rem */
            padding: 0.4rem 0.8rem; /* 减少内边距，从0.5rem 1rem改为0.4rem 0.8rem */
            background: rgba(200,166,117,0.1);
            border-radius: 8px;
            display: inline-block;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        /* 紧急联系提示样式优化 */
        .emergency-contact {
            max-width: 600px;
            margin: 0 auto; /* 修改为居中 */
            padding: 0.6rem; /* 减小内边距，从0.8rem改为0.6rem */
            background: rgba(20, 10, 10, 0.6); /* 更深色带红色调背景 */
            border-radius: 10px; /* 减小圆角，从12px改为10px */
            border: 1px solid rgba(255,87,34,0.25); /* 加强边框色 */
            position: relative;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2), 0 0 10px rgba(255, 87, 34, 0.1); /* 添加阴影 */
            backdrop-filter: blur(10px);
        }

        .emergency-content {
            display: flex;
            align-items: center;
            justify-content: center; /* 内容居中 */
            gap: 0.6rem; /* 减小间距，从1rem改为0.6rem */
            position: relative;
            z-index: 1;
        }

        .emergency-icon {
            color: #FF5722;
            font-size: 1.2rem; /* 减小图标大小，从1.5rem改为1.2rem */
        }

        .emergency-contact p {
            color: rgba(255,255,255,0.8);
            font-size: 0.9rem; /* 减小字体大小 */
            margin: 0; /* 移除默认边距 */
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .contact-grid {
                grid-template-columns: 1fr;
                padding: 0 1rem;
            }
            
            .working-hours,
            .emergency-contact {
                margin-left: auto; /* 确保在小屏幕上也保持居中 */
                margin-right: auto;
                width: calc(100% - 2rem); /* 添加合适的宽度 */
            }
        }

        /* 响应时间样式优化 */
        .response-time {
            font-size: 0.9rem;
            color: rgba(255,255,255,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .response-time i {
            color: var(--primary-gold);
            opacity: 0.8;
        }

        /* 在线状态样式优化 */
        .online-status {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.3rem;
            margin-top: 0.5rem;
            padding: 0.3rem 0.8rem;
            background: rgba(76,175,80,0.1);
            border-radius: 12px;
            width: fit-content;
            margin: 0.5rem auto 0;
        }

        .status-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #4CAF50;
            animation: pulse 2s infinite;
        }

        /* 二维码按钮样式优化 */
        .qr-button {
            margin-top: 0.5rem;
            padding: 0.4rem 0.8rem;
            background: transparent;
            border: 1px solid var(--primary-gold);
            color: var(--primary-gold);
            border-radius: 15px;
            font-size: 0.85rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.3rem;
        }

        .qr-button:hover {
            background: var(--primary-gold);
            color: var(--bg-darker);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(200,166,117,0.2);
        }

        /* 紧急联系提示样式优化 */
        .emergency-contact {
            max-width: 600px;
            margin: 0 auto; /* 修改为居中 */
            padding: 0.6rem; /* 减小内边距，从0.8rem改为0.6rem */
            background: rgba(20, 10, 10, 0.6); /* 更深色带红色调背景 */
            border-radius: 10px; /* 减小圆角，从12px改为10px */
            border: 1px solid rgba(255,87,34,0.25); /* 加强边框色 */
            position: relative;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2), 0 0 10px rgba(255, 87, 34, 0.1); /* 添加阴影 */
            backdrop-filter: blur(10px);
        }

        .emergency-content {
            display: flex;
            align-items: center;
            justify-content: center; /* 内容居中 */
            gap: 0.6rem; /* 减小间距，从1rem改为0.6rem */
            position: relative;
            z-index: 1;
        }

        .emergency-icon {
            color: #FF5722;
            font-size: 1.2rem; /* 减小图标大小，从1.5rem改为1.2rem */
        }

        .emergency-contact p {
            color: rgba(255,255,255,0.8);
            font-size: 0.9rem; /* 减小字体大小 */
            margin: 0; /* 移除默认边距 */
        }

        /* 悬浮效果优化 */
        .contact-item:hover {
            transform: translateY(-5px);
            border-color: rgba(200,166,117,0.3);
            box-shadow: 0 8px 25px rgba(0,0,0,0.25), 0 0 15px rgba(179, 155, 119, 0.15);
        }

        /* 响应式优化 */
        @media (max-width: 1024px) {
            .contact-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1.5rem;
            }
        }

        @media (max-width: 768px) {
            .contact-grid {
                grid-template-columns: 1fr;
                padding: 0 1rem;
            }
            
            .hours-content {
                flex-direction: column;
                text-align: center;
            }
            
            .emergency-content {
                flex-direction: column;
                text-align: center;
            }
            
            .working-hours,
            .emergency-contact {
                margin-left: auto; /* 确保在小屏幕上也保持居中 */
                margin-right: auto;
                width: calc(100% - 2rem); /* 添加合适的宽度 */
            }
        }

        /* 动画效果 */
        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.7;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        /* 页面加载动画 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--bg-darker);
            z-index: 9999;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: opacity 0.5s;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(179, 155, 119, 0.1);
            border-top-color: var(--primary-gold);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            box-shadow: 0 0 20px rgba(179, 155, 119, 0.1);
        }

        /* 动画关键帧 */
        @keyframes spin {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }

        /* 优化粒子容器样式 */
        .particles-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
            overflow: hidden;
        }

        /* 优化粒子样式 */
        .particle {
            position: absolute;
            background: linear-gradient(135deg, var(--primary-gold), var(--light-gold));
            opacity: 0.2;
            will-change: transform, opacity;
            transform: translateZ(0);
        }

        /* 粒子动画 */
        @keyframes float {
            0% {
                transform: translateY(0) rotate(0deg);
                opacity: 0.3;
            }
            50% {
                opacity: 0.6;
            }
            100% {
                transform: translateY(-100vh) rotate(360deg);
                opacity: 0;
            }
        }

        /* 添加光晕效果 */
        .hero-glow {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            background: 
                radial-gradient(circle at 20% 30%, rgba(179, 155, 119, 0.05) 0%, transparent 70%),
                radial-gradient(circle at 80% 70%, rgba(179, 155, 119, 0.05) 0%, transparent 70%);
            filter: blur(50px);
            opacity: 0.5;
        }

        /* 优化统计卡片样式 */
        .stat-card {
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(
                45deg,
                transparent,
                rgba(200,166,117,0.1),
                transparent
            );
            transform: rotate(45deg);
            animation: shimmer 3s infinite linear;
            pointer-events: none;
        }

        @keyframes shimmer {
            0% {
                transform: translateX(-100%) rotate(45deg);
            }
            100% {
                transform: translateX(100%) rotate(45deg);
            }
        }

        /* 优化页脚背景样式 */
        .footer {
            background: linear-gradient(to bottom, var(--bg-darker), rgba(0,0,0,0.98));
            border-top: 1px solid rgba(179, 155, 119, 0.1);
            padding: 6rem 0 2rem;
            position: relative;
            overflow: hidden;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: radial-gradient(
                var(--primary-gold) 1px,
                transparent 1px
            );
            background-size: 50px 50px;
            opacity: 0.03;
            pointer-events: none;
        }

        .footer-waves {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            overflow: hidden;
            line-height: 0;
        }

        .waves {
            position: relative;
            width: 100%;
            height: 50px;
        }

        .parallax > use {
            animation: wave-move 25s cubic-bezier(.55,.5,.45,.5) infinite;
        }

        .parallax > use:nth-child(1) {
            animation-delay: -2s;
            animation-duration: 7s;
        }

        .parallax > use:nth-child(2) {
            animation-delay: -3s;
            animation-duration: 10s;
        }

        .parallax > use:nth-child(3) {
            animation-delay: -4s;
            animation-duration: 13s;
        }

        @keyframes wave-move {
            0% {
                transform: translate3d(-90px,0,0);
            }
            100% {
                transform: translate3d(85px,0,0);
            }
        }

        /* 3D蜂窝球体 */
        .hex-sphere {
            position: absolute;
            right: 10%;
            top: 50%;
            transform: translateY(-50%);
            width: 300px;
            height: 300px;
            perspective: 1000px;
        }

        .hex-face {
            position: absolute;
            width: 100%;
            height: 100%;
            backface-visibility: hidden;
            transform-style: preserve-3d;
        }

        .hex-face::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, 
                rgba(200,166,117,0.1),
                rgba(200,166,117,0.05)
            );
            clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);
            border: 1px solid rgba(200,166,117,0.2);
        }

        .hex-face::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, 
                rgba(200,166,117,0.1),
                rgba(200,166,117,0.05)
            );
            clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);
            border: 1px solid rgba(200,166,117,0.2);
        }

        .hex-face.front {
            transform: rotateY(0deg) translateZ(150px);
        }

        .hex-face.back {
            transform: rotateY(180deg) translateZ(150px);
        }

        .hex-face.left {
            transform: rotateY(-90deg) translateZ(150px);
        }

        .hex-face.right {
            transform: rotateY(90deg) translateZ(150px);
        }

        .hex-face.top {
            transform: rotateX(90deg) translateZ(150px);
        }

        .hex-face.bottom {
            transform: rotateX(-90deg) translateZ(150px);
        }

        /* 添加鼠标交互 */
        .hex-sphere:hover .hex-face {
            transform: rotateY(180deg) rotateX(360deg) rotateZ(360deg);
        }

        .hex-sphere:hover .hex-face.front {
            transform: rotateY(0deg) translateZ(150px);
        }

        .hex-sphere:hover .hex-face.back {
            transform: rotateY(180deg) translateZ(150px);
        }

        .hex-sphere:hover .hex-face.left {
            transform: rotateY(-90deg) translateZ(150px);
        }

        .hex-sphere:hover .hex-face.right {
            transform: rotateY(90deg) translateZ(150px);
        }

        .hex-sphere:hover .hex-face.top {
            transform: rotateX(90deg) translateZ(150px);
        }

        .hex-sphere:hover .hex-face.bottom {
            transform: rotateX(-90deg) translateZ(150px);
        }

        /* 工作时间样式 */
        .working-hours {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 3rem;
            padding: 1rem;
            background: rgba(200,166,117,0.05);
            border-radius: 8px;
            border: 1px solid rgba(200,166,117,0.1);
        }

        .hours-icon {
            font-size: 2rem;
            color: var(--primary-gold);
        }

        .hours-info h4 {
            color: var(--primary-gold);
            margin-bottom: 0.5rem;
        }

        .hours-info p {
            color: rgba(255,255,255,0.8);
        }

        /* 响应时间样式 */
        .response-time {
            margin-top: 1rem;
            font-size: 0.9rem;
            color: rgba(255,255,255,0.6);
        }

        /* 在线状态样式 */
        .online-status {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.3rem;
            margin-top: 0.5rem;
            padding: 0.3rem 0.8rem;
            background: rgba(76,175,80,0.1);
            border-radius: 12px;
            width: fit-content;
            margin: 0.5rem auto 0;
        }

        .status-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #4CAF50;
            animation: pulse 2s infinite;
        }

        .status-text {
            font-size: 0.9rem;
            color: #4CAF50;
        }

        /* 二维码按钮样式 */
        .qr-button {
            margin-top: 0.5rem;
            padding: 0.4rem 0.8rem;
            background: transparent;
            border: 1px solid var(--primary-gold);
            color: var(--primary-gold);
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .qr-button:hover {
            background: var(--primary-gold);
            color: var(--bg-darker);
        }

        /* 紧急联系提示样式 */
        .emergency-contact {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            margin-top: 3rem;
            padding: 1rem;
            background: rgba(255,87,34,0.1);
            border-radius: 8px;
            border: 1px solid rgba(255,87,34,0.2);
        }

        .emergency-icon {
            color: #FF5722;
            font-size: 1.5rem;
        }

        .emergency-contact p {
            color: rgba(255,255,255,0.8);
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.7;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .working-hours {
                flex-direction: column;
                text-align: center;
            }
            
            .emergency-contact {
                flex-direction: column;
            text-align: center;
            }
        }

        /* 二维码弹窗样式 */
        .qr-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            opacity: 0;
            animation: fadeIn 0.3s ease forwards;
        }

        .qr-modal-content {
            background: var(--bg-dark);
            padding: 2rem;
            border-radius: 15px;
            border: 1px solid var(--primary-gold);
            text-align: center;
            position: relative;
            max-width: 90%;
            width: 320px;
            transform: scale(0.9);
            opacity: 0;
            animation: scaleIn 0.3s ease 0.1s forwards;
        }

        .qr-modal-content img {
            width: 200px;
            height: 200px;
            margin-bottom: 1rem;
            border-radius: 10px;
            border: 2px solid var(--primary-gold);
        }

        .qr-modal-content p {
            color: var(--text-light);
            margin-bottom: 1.5rem;
            font-size: 1rem;
        }

        .close-modal {
            background: var(--primary-gold);
            color: var(--bg-darker);
            border: none;
            padding: 0.8rem 2rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .close-modal:hover {
            background: var(--light-gold);
            transform: translateY(-2px);
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        @keyframes scaleIn {
            from {
                transform: scale(0.9);
                opacity: 0;
            }
            to {
                transform: scale(1);
                opacity: 1;
            }
        }

        /* 响应式调整 */
        @media (max-width: 480px) {
            .qr-modal-content {
                width: 280px;
                padding: 1.5rem;
            }

            .qr-modal-content img {
                width: 180px;
                height: 180px;
            }
        }

        /* 新增动画和效果样式 */
        .main-content {
            position: relative;
            overflow: hidden;
            margin-top: 70px; /* 添加顶部边距防止被header覆盖 */
        }

        /* 粒子容器 */
        .particles-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        /* 英雄区域高亮文字 */
        .hero-title[data-text] {
            position: relative;
            text-shadow: 0 0 15px rgba(179, 155, 119, 0.3);
        }

        .hero-title[data-text]::before {
            content: attr(data-text);
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            color: transparent;
            -webkit-text-stroke: 1px rgba(179, 155, 119, 0.6);
            z-index: -1;
            animation: textPulse 2s ease-in-out infinite;
        }

        .highlight {
            color: var(--primary-gold);
            font-weight: 500;
            position: relative;
        }

        .highlight::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: -2px;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, transparent, var(--primary-gold), transparent);
        }

        /* 按钮悬停效果 */
        .btn-hover-effect {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at center, rgba(179, 155, 119, 0.4) 0%, transparent 70%);
            opacity: 0;
            transition: opacity 0.5s;
            z-index: -1;
            border-radius: inherit;
        }

        .btn-register:hover .btn-hover-effect,
        .btn-login:hover .btn-hover-effect,
        .qr-button:hover .btn-hover-effect {
            opacity: 1;
            animation: pulseEffect 1.5s infinite;
        }

        /* 滚动指示器 */
        .scroll-indicator {
            position: absolute;
            bottom: 40px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            flex-direction: column;
            align-items: center;
            z-index: 10;
            opacity: 0.8;
            transition: opacity 0.3s;
        }

        .scroll-indicator:hover {
            opacity: 1;
        }

        .mouse {
            width: 26px;
            height: 40px;
            border: 2px solid var(--primary-gold);
            border-radius: 20px;
            display: flex;
            justify-content: center;
            padding-top: 8px;
            box-sizing: border-box;
        }

        .wheel {
            width: 4px;
            height: 10px;
            background-color: var(--primary-gold);
            border-radius: 2px;
            animation: scrollWheel 2s ease infinite;
        }

        .scroll-indicator p {
            margin-top: 10px;
            font-size: 0.8rem;
            color: var(--text-light);
            opacity: 0.7;
        }

        /* 统计卡片动画 */
        .stat-progress {
            width: 60px;
            height: 60px;
            position: absolute;
            top: 10px;
            right: 15px;
            transform: rotate(-90deg);
        }

        .stat-circle-bg {
            fill: none;
            stroke: rgba(179, 155, 119, 0.1);
            stroke-width: 2;
        }

        .stat-circle {
            fill: none;
            stroke: var(--primary-gold);
            stroke-width: 2.5;
            stroke-linecap: round;
            stroke-dashoffset: 0;
            transition: stroke-dasharray 1s ease;
        }

        .stat-card {
            overflow: hidden;
            position: relative;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle at center, rgba(179, 155, 119, 0.1) 0%, transparent 60%);
            opacity: 0;
            transition: opacity 0.5s;
            pointer-events: none;
        }

        .stat-card:hover::before {
            opacity: 1;
        }

        /* 特性卡片链接 */
        .feature-link {
            display: inline-flex;
            align-items: center;
            color: var(--primary-gold);
            font-size: 0.9rem;
            margin-top: 15px;
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .feature-link i {
            margin-left: 5px;
            transition: transform 0.3s ease;
        }

        .feature-card:hover .feature-link {
            opacity: 1;
            transform: translateY(0);
        }

        .feature-card:hover .feature-link i {
            transform: translateX(5px);
        }

        /* 标题装饰 */
        .title-decoration {
            position: relative;
            width: 100px;
            height: 2px;
            background: rgba(179, 155, 119, 0.3);
            margin: 20px auto 40px;
            overflow: hidden;
        }

        .title-decoration span {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--primary-gold);
            animation: titleDecoration 2s ease-in-out infinite;
        }

        /* 工作时间高亮 */
        .time-highlight {
            color: var(--primary-gold);
            font-weight: 500;
        }

        /* 响应时间高亮 */
        .response-highlight {
            color: var(--primary-gold);
            font-weight: 500;
        }

        /* 在线状态动画 */
        .status-dot.pulse {
            animation: statusPulse 1.5s infinite;
        }

        /* 紧急联系高亮 */
        .emergency-highlight {
            color: var(--primary-gold);
            font-weight: 500;
        }

        .emergency-icon.pulse {
            animation: emergencyPulse 2s infinite;
        }

        /* 复制提示样式 */
        .copy-tooltip {
            position: fixed;
            background-color: rgba(15, 15, 15, 0.9);
            color: var(--text-light);
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 0.8rem;
            z-index: 9999;
            pointer-events: none;
            opacity: 1;
            transition: opacity 0.3s;
            border: 1px solid var(--primary-gold);
        }

        /* 回到顶部按钮 */
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 45px;
            height: 45px;
            background-color: rgba(15, 15, 15, 0.8);
            color: var(--primary-gold);
            border: 1px solid rgba(179, 155, 119, 0.3);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        .back-to-top.show {
            opacity: 1;
            visibility: visible;
        }

        .back-to-top:hover {
            background-color: var(--primary-gold);
            color: var(--bg-darker);
            transform: translateY(-5px);
        }

        /* 动画关键帧 */
        @keyframes textPulse {
            0%, 100% { opacity: 0.2; }
            50% { opacity: 0.6; }
        }

        @keyframes pulseEffect {
            0% { transform: scale(1); opacity: 0.7; }
            50% { transform: scale(1.05); opacity: 0.4; }
            100% { transform: scale(1); opacity: 0.7; }
        }

        @keyframes scrollWheel {
            0% { transform: translateY(0); opacity: 1; }
            100% { transform: translateY(12px); opacity: 0; }
        }

        @keyframes titleDecoration {
            0%, 100% { transform: translateX(-100%); }
            50% { transform: translateX(100%); }
        }

        @keyframes statusPulse {
            0% { box-shadow: 0 0 0 0 rgba(46, 204, 113, 0.6); }
            70% { box-shadow: 0 0 0 6px rgba(46, 204, 113, 0); }
            100% { box-shadow: 0 0 0 0 rgba(46, 204, 113, 0); }
        }

        @keyframes emergencyPulse {
            0% { transform: scale(1); }
            10% { transform: scale(1.2); }
            20% { transform: scale(1); }
            100% { transform: scale(1); }
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .scroll-indicator {
                display: none;
            }
            
            .stat-progress {
                display: none;
            }
            
            .back-to-top {
                bottom: 15px;
                right: 15px;
                width: 40px;
                height: 40px;
            }
        }

        .hex-icon {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            z-index: 2;
        }
        
        .hex-icon i {
            font-size: 1.5rem;
            color: rgba(212,183,143,0.7);
            transition: all 0.3s ease;
        }
        
        .hex:hover .hex-icon i {
            color: rgba(212,183,143,1);
            text-shadow: 0 0 10px rgba(212,183,143,0.5);
            transform: scale(1.2);
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: scale(0.8);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        /* 页脚蜂窝装饰样式 */
        .footer {
            position: relative;
            overflow: hidden;
        }
        
        .footer-hex-left,
        .footer-hex-right {
            position: absolute;
            width: 200px;
            height: 300px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 1;
            opacity: 0.15;
            pointer-events: none;
        }
        
        .footer-hex-left {
            left: 0;
        }
        
        .footer-hex-right {
            right: 0;
        }
        
        .footer-hex {
            position: absolute;
            width: 80px;
            height: 70px;
            background: linear-gradient(
                135deg,
                rgba(28, 28, 30, 0.95) 0%,
                rgba(20, 20, 22, 0.98) 100%
            );
            clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);
            border: 1px solid rgba(212,183,143,0.3);
            animation: footerHexFloat 3s infinite ease-in-out;
        }
        
        .footer-hex-left .footer-hex:nth-child(1) {
            top: 0;
            left: 20px;
            animation-delay: 0.2s;
        }
        
        .footer-hex-left .footer-hex:nth-child(2) {
            top: 80px;
            left: 70px;
            animation-delay: 0.5s;
        }
        
        .footer-hex-left .footer-hex:nth-child(3) {
            top: 160px;
            left: 30px;
            animation-delay: 0.3s;
        }
        
        .footer-hex-left .footer-hex:nth-child(4) {
            top: 220px;
            left: 90px;
            animation-delay: 0.7s;
        }
        
        .footer-hex-right .footer-hex:nth-child(1) {
            top: 20px;
            right: 50px;
            animation-delay: 0.6s;
        }
        
        .footer-hex-right .footer-hex:nth-child(2) {
            top: 100px;
            right: 30px;
            animation-delay: 0.1s;
        }
        
        .footer-hex-right .footer-hex:nth-child(3) {
            top: 180px;
            right: 70px;
            animation-delay: 0.4s;
        }
        
        .footer-hex-right .footer-hex:nth-child(4) {
            top: 230px;
            right: 10px;
            animation-delay: 0.8s;
        }
        
        @keyframes footerHexFloat {
            0% {
                transform: translateY(0) rotate(0deg);
            }
            50% {
                transform: translateY(-8px) rotate(2deg);
            }
            100% {
                transform: translateY(0) rotate(0deg);
            }
        }
        
        /* 响应式调整 */
        @media (max-width: 768px) {
            .footer-hex-left,
            .footer-hex-right {
                width: 120px;
            }
            
            .footer-hex {
                width: 50px;
                height: 45px;
            }
        }

        /* 页脚美化样式 */
        .footer {
            position: relative;
            background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
            padding: 60px 0 30px;
            color: #d4b78f;
            overflow: hidden;
        }

        .footer-decorations {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            pointer-events: none;
            overflow: hidden;
        }

        .footer-hex-left, .footer-hex-right {
            position: absolute;
            top: 10%;
            z-index: 1;
        }

        .footer-hex-left {
            left: 0;
        }

        .footer-hex-right {
            right: 0;
        }

        .footer-hex {
            width: 40px;
            height: 40px;
            margin: 8px;
            background: rgba(212, 183, 143, 0.05);
            clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
            transition: all 0.3s ease;
        }

        .hex-animate {
            animation: pulse-hex 3s infinite ease-in-out;
        }

        @keyframes pulse-hex {
            0% { transform: scale(1); background: rgba(212, 183, 143, 0.05); }
            50% { transform: scale(1.1); background: rgba(212, 183, 143, 0.1); }
            100% { transform: scale(1); background: rgba(212, 183, 143, 0.05); }
        }

        .footer-waves {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100px;
            z-index: 5;
        }

        .waves {
            width: 100%;
            height: 100px;
        }

        .parallax > use {
            animation: wave-animation 25s cubic-bezier(.55, .5, .45, .5) infinite;
        }

        .parallax > use:nth-child(1) {
            animation-delay: -2s;
            animation-duration: 7s;
        }

        .parallax > use:nth-child(2) {
            animation-delay: -3s;
            animation-duration: 10s;
        }

        .parallax > use:nth-child(3) {
            animation-delay: -4s;
            animation-duration: 13s;
        }

        @keyframes wave-animation {
            0% { transform: translateX(-90px); }
            100% { transform: translateX(85px); }
        }

        .footer-container {
            position: relative;
            z-index: 10;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .footer-column {
            transition: transform 0.3s ease;
        }

        .footer-column:hover {
            transform: translateY(-5px);
        }

        .footer-title {
            font-size: 1.2rem;
            margin-bottom: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            color: #d4b78f;
            text-shadow: 0 0 10px rgba(212, 183, 143, 0.3);
        }

        .title-icon {
            margin-right: 10px;
            font-size: 1.1em;
            background: linear-gradient(135deg, #d4b78f, #a67c3d);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .footer-links {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .footer-link {
            color: #aaa;
            text-decoration: none;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
            font-size: 0.95rem;
        }

        .hover-effect:hover {
            color: #d4b78f;
            transform: translateX(5px);
        }

        .hover-effect:hover .link-icon {
            transform: rotate(90deg);
            color: #d4b78f;
        }

        .link-icon {
            margin-right: 8px;
            transition: all 0.3s ease;
            font-size: 0.8em;
            color: #888;
        }

        .footer-divider {
            height: 1px;
            background: linear-gradient(to right, transparent, rgba(212, 183, 143, 0.3), transparent);
            margin: 20px 0;
        }

        .copyright {
            text-align: center;
            padding: 2rem 0;
        }

        .copyright-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
        }

        .copyright-text {
            color: #888;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .beian-info {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .beian-link {
            color: #888;
            text-decoration: none;
            font-size: 0.85rem;
            display: flex;
            align-items: center;
            gap: 5px;
            transition: color 0.3s ease;
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 0.5; }
            50% { opacity: 1; }
            100% { opacity: 0.5; }
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .footer-grid {
                grid-template-columns: 1fr 1fr;
            }
            
            .footer-hex {
                width: 30px;
                height: 30px;
            }
        }

        @media (max-width: 480px) {
            .footer-grid {
                grid-template-columns: 1fr;
            }
            
            .copyright-content {
                flex-direction: column;
            }
            
            .beian-info {
                flex-direction: column;
                gap: 10px;
            }
        }

        /* 修改联系区域的图标样式 */
        .contact-section .feature-icon-wrapper {
            margin-bottom: 0.5rem; /* 添加较小的底部外边距 */
        }
        
        .contact-section .feature-icon {
            width: 50px; /* 减小图标尺寸，从原有尺寸改为50px */
            height: 50px; /* 减小图标尺寸，从原有尺寸改为50px */
            font-size: 1.2rem; /* 减小字体大小 */
        }
        
        .contact-section .icon-ring {
            width: 60px; /* 减小图标环尺寸 */
            height: 60px; /* 减小图标环尺寸 */
        }
        
        /* 减小AOS动画的高度影响 */
        .contact-section [data-aos] {
            min-height: 0 !important;
            height: auto !important;
            transition-property: transform, opacity !important;
        }

        /* 美化统计区域样式 */
        .stats-section {
            position: relative;
            padding: 80px 0;
            overflow: hidden;
            background-color: #0a0a0a;
        }

        .stats-section .hero-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(20, 20, 20, 0.9), rgba(10, 10, 10, 0.95));
            z-index: -2;
        }

        .stats-section .honeycomb-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><path d="M50 37.5l21.7 12.5v25L50 87.5 28.3 75V50L50 37.5z" fill="none" stroke="rgba(212, 175, 55, 0.1)" stroke-width="1"/></svg>');
            background-size: 100px 100px;
            opacity: 0.4;
            z-index: -1;
        }

        .section-title {
            text-align: center;
            margin-bottom: 50px;
            font-size: 2.5rem;
            font-weight: 700;
            color: #fff;
            text-shadow: 0 0 10px rgba(212, 175, 55, 0.5);
        }

        .section-title .highlight {
            color: #d4af37;
            margin-right: 10px;
            position: relative;
        }

        .section-title .highlight:after {
            content: "";
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, transparent, #d4af37, transparent);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .stat-card {
            position: relative;
            background: linear-gradient(145deg, #111111, #1a1a1a);
            border-radius: 15px;
            padding: 30px 20px;
            text-align: center;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3), 
                        0 0 0 1px rgba(212, 175, 55, 0.1),
                        0 0 0 4px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            overflow: hidden;
            z-index: 1;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4), 
                        0 0 0 1px rgba(212, 175, 55, 0.2),
                        0 0 0 4px rgba(0, 0, 0, 0.2);
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: #d4af37;
            text-shadow: 0 0 10px rgba(212, 175, 55, 0.4);
        }

        .stat-content {
            position: relative;
            z-index: 2;
        }

        .stat-number {
            font-size: 2.8rem;
            font-weight: 700;
            margin-bottom: 10px;
            background: linear-gradient(to right, #d4af37, #f5e8a9);
            -webkit-background-clip: text;
            color: transparent;
        }

        .stat-label {
            font-size: 1rem;
            color: #aaa;
            letter-spacing: 1px;
        }

        .stat-progress {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-90deg);
            width: 120%;
            height: 120%;
            opacity: 0.2;
            z-index: 0;
        }

        .stat-circle-bg {
            fill: none;
            stroke: rgba(80, 80, 80, 0.1);
            stroke-width: 2;
        }

        .stat-circle {
            fill: none;
            stroke: url(#gold-gradient);
            stroke-width: 2.5;
            stroke-linecap: round;
            transition: all 1s ease-in-out;
        }

        .card-glow {
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(212, 175, 55, 0.1) 0%, rgba(0, 0, 0, 0) 70%);
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
            z-index: 0;
        }

        .stat-card:hover .card-glow {
            opacity: 1;
        }

        .stats-footnote {
            text-align: center;
            color: #777;
            font-size: 0.9rem;
            margin-top: 20px;
        }

        /* 添加SVG渐变定义 */
        .stats-section:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.5), transparent);
        }

        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
            
            .section-title {
                font-size: 2rem;
            }
            
            .stat-number {
                font-size: 2.2rem;
            }
        }

        /* 美化蜂窝球体容器 */
        .hex-sphere-container {
            position: absolute;
            right: 10%;
            top: 50%;
            transform: translateY(-50%) scale(1.15);
            width: 300px;
            height: 300px;
            perspective: 1500px;
            z-index: 3;
            transform-style: preserve-3d;
            filter: drop-shadow(0 0 15px rgba(212,183,143,0.2));
            transition: all 0.5s ease-in-out;
        }

        .hex-sphere-container:hover {
            transform: translateY(-52%) scale(1.2);
            filter: drop-shadow(0 0 25px rgba(212,183,143,0.3));
        }

        .honeycomb {
            position: relative;
            width: 100%;
            height: 100%;
            display: grid;
            grid-template-columns: repeat(4, 60px);
            grid-gap: 8px;
            transform: rotate(30deg) rotateX(10deg) rotateY(10deg);
            transform-style: preserve-3d;
            animation: honeyRotate 20s infinite ease-in-out;
            transform-origin: center center;
        }

        @keyframes honeyRotate {
            0% {
                transform: rotate(30deg) rotateX(10deg) rotateY(10deg);
            }
            25% {
                transform: rotate(32deg) rotateX(14deg) rotateY(15deg);
            }
            50% {
                transform: rotate(30deg) rotateX(12deg) rotateY(20deg);
            }
            75% {
                transform: rotate(28deg) rotateX(10deg) rotateY(15deg);
            }
            100% {
                transform: rotate(30deg) rotateX(10deg) rotateY(10deg);
            }
        }

        .hex {
            position: relative;
            width: 60px;
            height: 52px;
            background: linear-gradient(
                135deg,
                rgba(28, 28, 30, 0.95) 0%,
                rgba(20, 20, 22, 0.98) 100%
            );
            clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);
            border: 1px solid rgba(212,183,143,0.25);
            transition: all 0.5s ease;
            transform-style: preserve-3d;
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
            backface-visibility: hidden;
            opacity: 0.85;
            transform: translateZ(0);
        }

        .hex:hover {
            background: linear-gradient(
                135deg,
                rgba(40, 40, 44, 0.95) 0%,
                rgba(30, 30, 33, 0.98) 100%
            );
            border-color: rgba(212,183,143,0.6);
            transform: scale(1.1) translateZ(20px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.3), 0 0 15px rgba(212,183,143,0.35);
            z-index: 10;
            opacity: 1;
        }

        .hex::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(135deg, 
                rgba(212,183,143,0.25) 0%, 
                rgba(212,183,143,0.15) 50%,
                rgba(212,183,143,0.25) 100%);
            clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .hex:hover::before {
            opacity: 1;
            animation: glowPulse 2s infinite alternate;
        }

        @keyframes glowPulse {
            0% {
                opacity: 0.6;
                filter: blur(1px);
            }
            100% {
                opacity: 1;
                filter: blur(2px);
            }
        }

        /* 优化光线效果 */
        .hex::after {
            content: '';
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            background: radial-gradient(circle at center, 
                rgba(212,183,143,0.35) 0%, 
                transparent 70%);
            opacity: 0;
            transition: opacity 0.4s ease;
            pointer-events: none;
            filter: blur(5px);
        }

        .hex:hover::after {
            opacity: 1;
        }

        /* 增强图标样式 */
        .hex-icon {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            z-index: 2;
            transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .hex-icon i {
            font-size: 1.4rem;
            color: rgba(212,183,143,0.8);
            transition: all 0.4s ease;
            text-shadow: 0 0 8px rgba(212,183,143,0.2);
            transform: translateZ(0);
        }

        .hex:hover .hex-icon i {
            color: rgba(212,183,143,1);
            text-shadow: 0 0 15px rgba(212,183,143,0.6);
            transform: scale(1.2) translateZ(10px);
            animation: iconFloat 1.5s ease-in-out infinite alternate;
        }

        @keyframes iconFloat {
            0% {
                transform: scale(1.2) translateZ(10px);
            }
            100% {
                transform: scale(1.35) translateZ(15px);
            }
        }

        /* 添加渐变流光效果 */
        .hex.active::before {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            background: linear-gradient(45deg, 
                rgba(212,183,143,0) 0%, 
                rgba(212,183,143,0.8) 50%, 
                rgba(212,183,143,0) 100%);
            filter: blur(5px);
            clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);
            animation: gradientFlow 2s linear infinite;
            z-index: -1;
        }

        @keyframes gradientFlow {
            0% {
                background-position: -200% 0;
            }
            100% {
                background-position: 200% 0;
            }
        }

        /* 添加3D阴影效果 */
        .hex {
            box-shadow: 
                inset 0 0 10px rgba(0,0,0,0.2),
                0 5px 10px rgba(0,0,0,0.2),
                0 10px 20px -5px rgba(0,0,0,0.1);
        }

        .hex:hover {
            box-shadow: 
                inset 0 0 15px rgba(0,0,0,0.3),
                0 8px 15px rgba(0,0,0,0.3),
                0 15px 30px -8px rgba(0,0,0,0.2),
                0 0 10px rgba(212,183,143,0.3);
        }

        /* 响应式调整 */
        @media (max-width: 1200px) {
            .hex-sphere-container {
                transform: translateY(-50%) scale(1);
                right: 7%;
            }
            
            .hex-sphere-container:hover {
                transform: translateY(-52%) scale(1.05);
            }
        }

        @media (max-width: 992px) {
            .hex-sphere-container {
                transform: translateY(-50%) scale(0.85);
                right: 5%;
            }
            
            .hex-sphere-container:hover {
                transform: translateY(-52%) scale(0.9);
            }
        }

        @media (max-width: 768px) {
            .hex-sphere-container {
                opacity: 0.5;
                transform: translateY(-50%) scale(0.7);
                right: 2%;
            }
        }
    </style>
</head>
<body>

    <header class="header">
        <div class="header-bg"></div>
        <div class="header-decorative-lines"></div>
        <div class="nav-container">
            <a href="/" class="logo">
                {if !empty($logo)}
                <img src="{$logo}" alt="{$siteName}" class="logo-img">
                {else}
                <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                    <path d="M16 4L28 12V24L16 32L4 24V12L16 4Z" fill="#D4B78F"/>
                </svg>
                {/if}
            </a>
            
            <nav class="nav-links">
                {foreach $navItems as $nav}
                <div class="nav-item">
                    <a href="{$nav.href}" class="nav-link" {if $nav.target eq '_blank'}target="_blank"{/if}>
                        {$nav.name}
                        {if !empty($nav.children)}
                        <i class="fas fa-chevron-down dropdown-icon"></i>
                        {/if}
                    </a>
                    {if !empty($nav.children)}
                    <div class="submenu">
                        {foreach $nav.children as $child}
                        <a href="{$child.href}" class="submenu-item" {if $child.target eq '_blank'}target="_blank"{/if}>
                            {$child.name}
                        </a>
                        {/foreach}
                    </div>
                    {/if}
                </div>
                {/foreach}
            </nav>

            <div class="auth-buttons">
                <button class="mobile-menu-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                <a href="/merchant/login" class="btn-login">
                    <i class="fas fa-sign-in-alt"></i> 商户登录
                </a>
                <a href="/merchant/register" class="btn-register">
                    <i class="fas fa-user-plus"></i> 商户注册
                </a>
            </div>
        </div>
    </header>

    <main class="main-content">
        <!-- 英雄区域 -->
        <section class="hero-section">
            <div class="hero-bg"></div>
            <div class="decorative-lines"></div>
            <div class="particles-container" id="particles-js"></div>
            <div class="container">
                <h1 class="hero-title" data-text="{$siteName}">{$siteName}</h1>
                <p class="hero-subtitle">
                    <span class="highlight">安全可靠</span>的支付解决方案，为您的业务保驾护航。
                    提供<span class="highlight">全方位</span>的支付服务，让您的交易更加便捷。
                </p>
                <div class="hero-buttons">
                    <a href="/merchant/register" class="btn-register">
                        <i class="fas fa-user-plus"></i> 立即注册
                        <span class="btn-hover-effect"></span>
                    </a>
                    <a href="/merchant/login" class="btn-login">
                        <i class="fas fa-sign-in-alt"></i> 商户登录
                        <span class="btn-hover-effect"></span>
                    </a>
                </div>
            </div>
            <div class="hex-sphere-container">
                <div class="honeycomb">
                    <!-- JavaScript将动态生成蜂巢单元格 -->
                </div>
            </div>
        </section>

        <!-- 统计数据区域 -->
        <section class="stats-section" data-aos="fade-up">
            <div class="hero-bg"></div>
            <div class="decorative-lines"></div>
            <div class="honeycomb-bg"></div>
            <div class="container">
                <h2 class="section-title" data-aos="fade-down">
                    <span class="highlight">平台数据</span>实时统计
                </h2>
                <div class="stats-grid">
                    <div class="stat-card" data-aos="zoom-in" data-aos-delay="100">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number counter" data-count="{$stats_orders}">1.2K+</div>
                            <div class="stat-label">完成订单数</div>
                        </div>
                        <svg class="stat-progress" viewBox="0 0 36 36">
                            <path class="stat-circle-bg" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" />
                            <path class="stat-circle" stroke-dasharray="85, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" />
                        </svg>
                        <div class="card-glow"></div>
                    </div>
                    <div class="stat-card" data-aos="zoom-in" data-aos-delay="200">
                        <div class="stat-icon">
                            <i class="fas fa-credit-card"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number counter" data-count="{$stats_cards}">0</div>
                            <div class="stat-label">发卡次数</div>
                        </div>
                        <svg class="stat-progress" viewBox="0 0 36 36">
                            <path class="stat-circle-bg" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" />
                            <path class="stat-circle" stroke-dasharray="70, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" />
                        </svg>
                        <div class="card-glow"></div>
                    </div>
                    <div class="stat-card" data-aos="zoom-in" data-aos-delay="300">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number counter" data-count="{$stats_merchants}">0</div>
                            <div class="stat-label">商户累计</div>
                        </div>
                        <svg class="stat-progress" viewBox="0 0 36 36">
                            <path class="stat-circle-bg" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" />
                            <path class="stat-circle" stroke-dasharray="65, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" />
                        </svg>
                        <div class="card-glow"></div>
                    </div>
                    <div class="stat-card" data-aos="zoom-in" data-aos-delay="400">
                        <div class="stat-icon">
                            <i class="fas fa-yen-sign"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number counter" data-count="{$stats_amount}">0</div>
                            <div class="stat-label">交易金额</div>
                        </div>
                        <svg class="stat-progress" viewBox="0 0 36 36">
                            <path class="stat-circle-bg" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" />
                            <path class="stat-circle" stroke-dasharray="75, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" />
                        </svg>
                        <div class="card-glow"></div>
                    </div>
                </div>
                <div class="stats-footnote" data-aos="fade-up" data-aos-delay="500">
                    <p>数据实时更新，展示平台真实运营状况</p>
                </div>
            </div>
        </section>

        <!-- 服务特点区域 -->
        <section class="features-section" data-aos="fade-up">
            <div class="hero-bg parallax-bg"></div>
            <div class="decorative-lines animated"></div>
            <div class="honeycomb-container animated"></div>
            <div class="container">
                <div class="section-header text-center">
                    <h2 class="section-title gradient-text" data-text="我们的优势">我们的优势</h2>
                    <p class="section-subtitle">为什么选择我们的服务</p>
                    <div class="title-decoration"><span class="pulse-animation"></span></div>
                </div>
                <div class="features-grid">
                    <div class="feature-card glass-effect" data-aos="fade-up" data-aos-delay="100">
                        <div class="feature-icon-wrapper">
                            <div class="feature-icon rotating">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="icon-ring pulsing"></div>
                        </div>
                        <h3 class="feature-title">安全可靠</h3>
                        <p class="feature-desc">采用先进的加密技术，确保交易安全</p>
                        <div class="feature-hover-effect gradient-hover"></div>
                        <a class="feature-link btn-hover">了解更多 <i class="fas fa-arrow-right icon-animation"></i></a>
                    </div>
                    <div class="feature-card glass-effect" data-aos="fade-up" data-aos-delay="200">
                        <div class="feature-icon-wrapper">
                            <div class="feature-icon rotating">
                                <i class="fas fa-bolt"></i>
                            </div>
                            <div class="icon-ring pulsing"></div>
                        </div>
                        <h3 class="feature-title">快速响应</h3>
                        <p class="feature-desc">毫秒级响应，支持高并发交易</p>
                        <div class="feature-hover-effect gradient-hover"></div>
                        <a class="feature-link btn-hover">了解更多 <i class="fas fa-arrow-right icon-animation"></i></a>
                    </div>
                    <div class="feature-card glass-effect" data-aos="fade-up" data-aos-delay="300">
                        <div class="feature-icon-wrapper">
                            <div class="feature-icon rotating">
                                <i class="fas fa-headset"></i>
                            </div>
                            <div class="icon-ring pulsing"></div>
                        </div>
                        <h3 class="feature-title">专业支持</h3>
                        <p class="feature-desc">7*24小时技术支持服务</p>
                        <div class="feature-hover-effect gradient-hover"></div>
                        <a class="feature-link btn-hover">了解更多 <i class="fas fa-arrow-right icon-animation"></i></a>
                    </div>
                    <!-- 新增卡片 -->
                    <div class="feature-card glass-effect" data-aos="fade-up" data-aos-delay="400">
                        <div class="feature-icon-wrapper">
                            <div class="feature-icon rotating">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="icon-ring pulsing"></div>
                        </div>
                        <h3 class="feature-title">数据分析</h3>
                        <p class="feature-desc">实时交易数据统计与分析报表</p>
                        <div class="feature-hover-effect gradient-hover"></div>
                        <a class="feature-link btn-hover">了解更多 <i class="fas fa-arrow-right icon-animation"></i></a>
                    </div>
                    <div class="feature-card glass-effect" data-aos="fade-up" data-aos-delay="500">
                        <div class="feature-icon-wrapper">
                            <div class="feature-icon rotating">
                                <i class="fas fa-wallet"></i>
                            </div>
                            <div class="icon-ring pulsing"></div>
                        </div>
                        <h3 class="feature-title">资金安全</h3>
                        <p class="feature-desc">多重签名，资金安全有保障</p>
                        <div class="feature-hover-effect gradient-hover"></div>
                        <a class="feature-link btn-hover">了解更多 <i class="fas fa-arrow-right icon-animation"></i></a>
                    </div>
                    <div class="feature-card glass-effect" data-aos="fade-up" data-aos-delay="600">
                        <div class="feature-icon-wrapper">
                            <div class="feature-icon rotating">
                                <i class="fas fa-code"></i>
                            </div>
                            <div class="icon-ring pulsing"></div>
                        </div>
                        <h3 class="feature-title">简单接入</h3>
                        <p class="feature-desc">完善的API文档，快速对接系统</p>
                        <div class="feature-hover-effect gradient-hover"></div>
                        <a class="feature-link btn-hover">了解更多 <i class="fas fa-arrow-right icon-animation"></i></a>
                    </div>
                </div>
            </div>
        </section>

        <!-- 联系方式区域 -->
        <section class="contact-section" data-aos="fade">
            <div class="contact-glow"></div>
            <div class="hex-decoration"></div>
            <div class="hex-decoration"></div>
            <div class="hex-decoration"></div>
            <div class="hex-decoration"></div>
            <div class="container">
                <div class="section-header" data-aos="zoom-in">
                    <h2 class="section-title gradient-text" data-text="联系我们">联系我们</h2>
                    <p class="section-subtitle">随时为您提供专业支持</p>
                    <div class="title-decoration">
                        <span class="pulse-animation"></span>
                        <span class="pulse-animation delay-1"></span>
                        <span class="pulse-animation delay-2"></span>
                    </div>
                </div>
                
                <!-- 优化工作时间信息 -->
                <div class="working-hours glass-card" data-aos="fade-up" data-aos-delay="100">
                    <div class="hours-content">
                        <div class="hours-icon pulse-animation">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="hours-info">
                            <h4>客服工作时间</h4>
                            <p>周一至周日 <span class="time-highlight gradient-text">9:00-22:00</span></p>
                        </div>
                    </div>
                    <div class="hours-decoration hexagon-pattern"></div>
                </div>

                <div class="contact-grid">
                    <div class="contact-item glass-effect hover-scale" data-aos="fade-right" data-aos-delay="150">
                        <div class="contact-item-inner">
                            <div class="feature-icon-wrapper">
                                <div class="feature-icon rotating glow-effect">
                                    <i class="fas fa-envelope contact-icon"></i>
                                </div>
                                <div class="icon-ring pulsing"></div>
                            </div>
                            <h3 class="contact-title">电子邮件</h3>
                            <p class="contact-info copy-text" data-tooltip="点击复制">{$contact_email}</p>
                            <p class="response-time">
                                <i class="fas fa-history"></i>
                                预计响应时间: <span class="response-highlight gradient-text">24小时内</span>
                            </p>
                            <div class="floating-particles"></div>
                        </div>
                        <div class="feature-hover-effect gradient-hover"></div>
                    </div>
                    
                    <div class="contact-item glass-effect hover-scale" data-aos="fade-up" data-aos-delay="200">
                        <div class="contact-item-inner">
                            <div class="feature-icon-wrapper">
                                <div class="feature-icon rotating glow-effect">
                                    <i class="fab fa-qq contact-icon"></i>
                                </div>
                                <div class="icon-ring pulsing"></div>
                            </div>
                            <h3 class="contact-title">QQ客服</h3>
                            <p class="contact-info copy-text" data-tooltip="点击复制">{$contact_qq}</p>
                            <div class="online-status">
                                <span class="status-dot pulse"></span>
                                <span class="status-text">在线</span>
                            </div>
                            <div class="floating-particles"></div>
                        </div>
                        <div class="feature-hover-effect gradient-hover"></div>
                    </div>
                    
                    <div class="contact-item glass-effect hover-scale" data-aos="fade-left" data-aos-delay="250">
                        <div class="contact-item-inner">
                            <div class="feature-icon-wrapper">
                                <div class="feature-icon rotating glow-effect">
                                    <i class="fab fa-weixin contact-icon"></i>
                                </div>
                                <div class="icon-ring pulsing"></div>
                            </div>
                            <h3 class="contact-title">微信客服</h3>
                            <p class="contact-info copy-text" data-tooltip="点击复制">{$contact_wx}</p>
                            <button class="qr-button glow-effect">
                                <i class="fas fa-qrcode"></i>
                                扫码添加
                                <span class="btn-hover-effect"></span>
                            </button>
                            <div class="floating-particles"></div>
                        </div>
                        <div class="feature-hover-effect gradient-hover"></div>
                    </div>
                </div>

                <!-- 优化紧急联系提示 -->
                <div class="emergency-contact glass-card" data-aos="fade-up" data-aos-delay="300">
                    <div class="emergency-content">
                        <div class="emergency-icon pulse">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <p>如遇<span class="emergency-highlight gradient-text">紧急情况</span>，请优先通过QQ或微信联系我们</p>
                    </div>
                    <div class="emergency-decoration"></div>
                </div>
            </div>
        </section>
        
        <!-- 添加回到顶部按钮 -->
        <button id="back-to-top" class="back-to-top">
            <i class="fas fa-chevron-up"></i>
        </button>
    </main>

    <footer class="footer">
        <!-- 页脚蜂窝装饰 -->
        <div class="footer-decorations">
            <div class="footer-hex-left">
                <div class="footer-hex hex-animate"></div>
                <div class="footer-hex hex-animate" style="animation-delay: 0.3s"></div>
                <div class="footer-hex hex-animate" style="animation-delay: 0.6s"></div>
                <div class="footer-hex hex-animate" style="animation-delay: 0.9s"></div>
            </div>
            
            <div class="footer-hex-right">
                <div class="footer-hex hex-animate"></div>
                <div class="footer-hex hex-animate" style="animation-delay: 0.4s"></div>
                <div class="footer-hex hex-animate" style="animation-delay: 0.8s"></div>
                <div class="footer-hex hex-animate" style="animation-delay: 1.2s"></div>
            </div>
        </div>
        
        <!-- 波浪动画效果 -->
        <div class="footer-waves">
            <svg class="waves" xmlns="http://www.w3.org/2000/svg" viewBox="0 24 150 28" preserveAspectRatio="none">
                <defs>
                    <path id="wave" d="M-160 44c30 0 58-18 88-18s 58 18 88 18 58-18 88-18 58 18 88 18 v44h-352z" />
                </defs>
                <g class="parallax">
                    <use href="#wave" x="48" y="0" fill="rgba(212,183,143,0.08)"/>
                    <use href="#wave" x="48" y="3" fill="rgba(212,183,143,0.05)"/>
                    <use href="#wave" x="48" y="5" fill="rgba(212,183,143,0.02)"/>
                </g>
            </svg>
        </div>

        <div class="container footer-container">
            <div class="footer-grid">
                {if $footer_service_show == 1}
                <div class="footer-column" data-aos="fade-up" data-aos-delay="100">
                    <h3 class="footer-title">
                        <i class="fas fa-concierge-bell title-icon"></i>
                        <span>服务中心</span>
                    </h3>
                    <div class="footer-links">
                        <a href="{$footer_service_1_link|default='#'}" class="footer-link hover-effect">
                            <span class="link-icon"><i class="fas fa-angle-right"></i></span>
                            <span class="link-text">{$footer_service_1|default='卡密查询'}</span>
                        </a>
                        <a href="{$footer_service_2_link|default='#'}" class="footer-link hover-effect">
                            <span class="link-icon"><i class="fas fa-angle-right"></i></span>
                            <span class="link-text">{$footer_service_2|default='投诉中心'}</span>
                        </a>
                        <a href="{$footer_service_3_link|default='#'}" class="footer-link hover-effect">
                            <span class="link-icon"><i class="fas fa-angle-right"></i></span>
                            <span class="link-text">{$footer_service_3|default='卡密工具'}</span>
                        </a>
                        <a href="{$footer_service_4_link|default='#'}" class="footer-link hover-effect">
                            <span class="link-icon"><i class="fas fa-angle-right"></i></span>
                            <span class="link-text">{$footer_service_4|default='商户入驻'}</span>
                        </a>
                    </div>
                </div>
                {/if}
                
                {if $footer_help_show == 1}
                <div class="footer-column" data-aos="fade-up" data-aos-delay="200">
                    <h3 class="footer-title">
                        <i class="fas fa-question-circle title-icon"></i>
                        <span>帮助中心</span>
                    </h3>
                    <div class="footer-links">
                        <a href="{$footer_help_1_link|default='#'}" class="footer-link hover-effect">
                            <span class="link-icon"><i class="fas fa-angle-right"></i></span>
                            <span class="link-text">{$footer_help_1|default='常见问题'}</span>
                        </a>
                        <a href="{$footer_help_2_link|default='#'}" class="footer-link hover-effect">
                            <span class="link-icon"><i class="fas fa-angle-right"></i></span>
                            <span class="link-text">{$footer_help_2|default='系统公告'}</span>
                        </a>
                        <a href="{$footer_help_3_link|default='#'}" class="footer-link hover-effect">
                            <span class="link-icon"><i class="fas fa-angle-right"></i></span>
                            <span class="link-text">{$footer_help_3|default='结算公告'}</span>
                        </a>
                        <a href="{$footer_help_4_link|default='#'}" class="footer-link hover-effect">
                            <span class="link-icon"><i class="fas fa-angle-right"></i></span>
                            <span class="link-text">{$footer_help_4|default='新闻动态'}</span>
                        </a>
                    </div>
                </div>
                {/if}
                
                {if $footer_legal_show == 1}
                <div class="footer-column" data-aos="fade-up" data-aos-delay="300">
                    <h3 class="footer-title">
                        <i class="fas fa-gavel title-icon"></i>
                        <span>法律责任</span>
                    </h3>
                    <div class="footer-links">
                        <a href="{$footer_legal_1_link|default='#'}" class="footer-link hover-effect">
                            <span class="link-icon"><i class="fas fa-angle-right"></i></span>
                            <span class="link-text">{$footer_legal_1|default='免责声明'}</span>
                        </a>
                        <a href="{$footer_legal_2_link|default='#'}" class="footer-link hover-effect">
                            <span class="link-icon"><i class="fas fa-angle-right"></i></span>
                            <span class="link-text">{$footer_legal_2|default='禁售商品'}</span>
                        </a>
                        <a href="{$footer_legal_3_link|default='#'}" class="footer-link hover-effect">
                            <span class="link-icon"><i class="fas fa-angle-right"></i></span>
                            <span class="link-text">{$footer_legal_3|default='服务协议'}</span>
                        </a>
                        <a href="{$footer_legal_4_link|default='#'}" class="footer-link hover-effect">
                            <span class="link-icon"><i class="fas fa-angle-right"></i></span>
                            <span class="link-text">{$footer_legal_4|default='隐私政策'}</span>
                        </a>
                    </div>
                </div>
                {/if}
                
                {if $footer_links_show == 1}
                <div class="footer-column" data-aos="fade-up" data-aos-delay="400">
                    <h3 class="footer-title">
                        <i class="fas fa-link title-icon"></i>
                        <span>友情链接</span>
                    </h3>
                    <div class="footer-links">
                        <a href="{$footer_links_1_link|default='#'}" class="footer-link hover-effect">
                            <span class="link-icon"><i class="fas fa-angle-right"></i></span>
                            <span class="link-text">{$footer_links_1|default='一意支付'}</span>
                        </a>
                        <a href="{$footer_links_2_link|default='#'}" class="footer-link hover-effect">
                            <span class="link-icon"><i class="fas fa-angle-right"></i></span>
                            <span class="link-text">{$footer_links_2|default='支付宝'}</span>
                        </a>
                        <a href="{$footer_links_3_link|default='#'}" class="footer-link hover-effect">
                            <span class="link-icon"><i class="fas fa-angle-right"></i></span>
                            <span class="link-text">{$footer_links_3|default='微信支付'}</span>
                        </a>
                        <a href="{$footer_links_4_link|default='#'}" class="footer-link hover-effect">
                            <span class="link-icon"><i class="fas fa-angle-right"></i></span>
                            <span class="link-text">{$footer_links_4|default='QQ钱包'}</span>
                        </a>
                    </div>
                </div>
                {/if}
            </div>
            
            <div class="footer-divider"></div>
            
            <div class="copyright">
                <div class="copyright-content">
                    <span class="copyright-text">
                        <i class="far fa-copyright pulse"></i>
                        <span>{$siteName|default='Blackglod'} - 版权所有 © {$year|default='2022'}-至今</span>
                    </span>
                    <div class="beian-info">
                        {if !empty($icpNumber)}
                        <a href="https://beian.miit.gov.cn/" target="_blank" class="beian-link hover-effect">
                            <i class="fas fa-shield-alt"></i>
                            <span>{$icpNumber}</span>
                        </a>
                        {/if}
                        {if !empty($gaNumber)}
                        <a href="{$icpCert}" target="_blank" class="beian-link hover-effect">
                            <i class="fas fa-badge-check"></i>
                            <span>{$gaNumber}</span>
                        </a>
                        {/if}
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
    window.addEventListener('load', function() {
        const overlay = document.querySelector('.loading-overlay');
        if (overlay) {
            overlay.style.opacity = '0';
            setTimeout(() => overlay.style.display = 'none', 500);
        }
        
        // 移动端菜单交互
        const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
        const navLinks = document.querySelector('.nav-links');
        const navItems = document.querySelectorAll('.nav-item');
        
        if (mobileMenuToggle) {
            mobileMenuToggle.addEventListener('click', function() {
                navLinks.classList.toggle('active');
                this.innerHTML = navLinks.classList.contains('active') 
                    ? '<i class="fas fa-times"></i>' 
                    : '<i class="fas fa-bars"></i>';
                    
                // 添加移动端登录/注册按钮
                if (navLinks.classList.contains('active') && !document.querySelector('.mobile-auth')) {
                    const mobileAuth = document.createElement('div');
                    mobileAuth.className = 'mobile-auth';
                    mobileAuth.innerHTML = `
                        <a href="/merchant/login" class="btn-login">
                            <i class="fas fa-sign-in-alt"></i> 商户登录
                        </a>
                        <a href="/merchant/register" class="btn-register">
                            <i class="fas fa-user-plus"></i> 商户注册
                        </a>
                    `;
                    navLinks.appendChild(mobileAuth);
                }
            });
        }
        
        // 移动端子菜单交互
        const handleSubMenu = () => {
            if (window.innerWidth <= 576) {
                navItems.forEach(item => {
                    const link = item.querySelector('.nav-link');
                    const subMenu = item.querySelector('.submenu');
                    
                    if (subMenu) {
                        link.addEventListener('click', function(e) {
                            e.preventDefault();
                            
                            // 关闭其他打开的子菜单
                            navItems.forEach(otherItem => {
                                if (otherItem !== item && otherItem.classList.contains('open')) {
                                    otherItem.classList.remove('open');
                                    const otherIcon = otherItem.querySelector('.dropdown-icon');
                                    if (otherIcon) {
                                        otherIcon.style.transform = 'rotate(0)';
                                    }
                                }
                            });
                            
                            // 切换当前子菜单
                            item.classList.toggle('open');
                            const dropdownIcon = link.querySelector('.dropdown-icon');
                            if (dropdownIcon) {
                                dropdownIcon.style.transform = item.classList.contains('open') 
                                    ? 'rotate(180deg)' 
                                    : 'rotate(0)';
                            }
                        });
                    }
                });
            }
        };
        
        handleSubMenu();
        
        // 窗口大小变化时重新处理
        window.addEventListener('resize', function() {
            if (window.innerWidth > 576 && navLinks.classList.contains('active')) {
                navLinks.classList.remove('active');
                mobileMenuToggle.innerHTML = '<i class="fas fa-bars"></i>';
                const mobileAuth = document.querySelector('.mobile-auth');
                if (mobileAuth) {
                    mobileAuth.remove();
                }
            }
        });
    });
    </script>

    <script>
    window.addEventListener('load', function() {
        const overlay = document.querySelector('.loading-overlay');
        if (overlay) {
            overlay.style.opacity = '0';
            setTimeout(() => overlay.style.display = 'none', 500);
        }
    });
    </script>

    <div class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <script>
    function adjustNavFontSize() {
        const navContainer = document.querySelector('.nav-container');
        const navLinks = document.querySelector('.nav-links');
        const navItems = navLinks.querySelectorAll('.nav-item');
        
        const containerWidth = navLinks.offsetWidth;
        
        let totalWidth = 0;
        navItems.forEach(item => {
            totalWidth += item.offsetWidth;
        });
        
        let fontSize = 1;
        
        if (totalWidth > containerWidth) {
            while (totalWidth > containerWidth && fontSize > 0.7) {
                fontSize -= 0.05;
                navContainer.style.setProperty('--nav-font-size', `${fontSize}rem`);
                
                totalWidth = 0;
                navItems.forEach(item => {
                    totalWidth += item.offsetWidth;
                });
            }
        } else {
            while (totalWidth < containerWidth && fontSize < 1) {
                fontSize += 0.05;
                navContainer.style.setProperty('--nav-font-size', `${fontSize}rem`);
                
                totalWidth = 0;
                navItems.forEach(item => {
                    totalWidth += item.offsetWidth;
                });
            }
        }
    }

    window.addEventListener('load', adjustNavFontSize);
    window.addEventListener('resize', adjustNavFontSize);

    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    window.addEventListener('resize', debounce(adjustNavFontSize, 250));

    </script>

    <!-- 添加粒子容器 -->
    <div class="particles-container"></div>
    <div class="hero-glow"></div>

    <!-- 添加粒子效果的JavaScript -->
    <script>
    // 粒子系统
    function initParticles() {
        const container = document.querySelector('.particles-container');
        if (!container) return;

        const particleCount = 15; // 减少粒子数量
        const particles = [];
        let frameId;

        function createParticle() {
            const particle = document.createElement('div');
            particle.className = 'particle';
            
            // 随机大小 (2-4px)
            const size = 2 + Math.random() * 2;
            particle.style.width = `${size}px`;
            particle.style.height = `${size}px`;
            
            // 初始位置
            const x = Math.random() * 100;
            particle.style.left = `${x}%`;
            particle.style.top = '100%';
            
            // 动画参数
            const duration = 15 + Math.random() * 10;
            const delay = Math.random() * 5;
            
            // 使用transform代替top属性动画
            particle.animate([
                { transform: 'translateY(0)', opacity: 0.3 },
                { transform: 'translateY(-100vh)', opacity: 0 }
            ], {
                duration: duration * 1000,
                delay: delay * 1000,
                easing: 'linear',
                fill: 'forwards'
            });

            container.appendChild(particle);
            
            // 动画结束后移除粒子
            setTimeout(() => {
                particle.remove();
                particles.splice(particles.indexOf(particle), 1);
                if (particles.length < particleCount) {
                    particles.push(createParticle());
                }
            }, (duration + delay) * 1000);

            return particle;
        }

        // 初始化粒子
        for (let i = 0; i < particleCount; i++) {
            particles.push(createParticle());
        }

        // 清理函数
        return () => {
            if (frameId) {
                cancelAnimationFrame(frameId);
            }
            particles.forEach(particle => particle.remove());
            particles.length = 0;
        };
    }

    // 页面加载和可见性变化时初始化粒子
    let cleanup;
    document.addEventListener('DOMContentLoaded', () => {
        cleanup = initParticles();
    });

    // 优化页面可见性处理
    document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
            if (cleanup) {
                cleanup();
                cleanup = null;
            }
        } else {
            if (!cleanup) {
                cleanup = initParticles();
            }
        }
    });

    // 优化窗口大小改变处理
    let resizeTimeout;
    window.addEventListener('resize', () => {
        if (resizeTimeout) {
            clearTimeout(resizeTimeout);
        }
        resizeTimeout = setTimeout(() => {
            if (cleanup) {
                cleanup();
            }
            cleanup = initParticles();
        }, 250);
    });
    </script>

    <!-- 创建蜂巢结构 -->
    <script>
    function createHoneycomb() {
        const container = document.querySelector('.honeycomb');
        if (!container) return;
        
        // 清空容器
        container.innerHTML = '';
        
        const rows = 5;
        const cols = 4;
        
        // SVG图标列表
        const icons = [
            'shield-alt', 'credit-card', 'chart-line', 'bolt', 
            'headset', 'wallet', 'code', 'server', 
            'lock', 'users', 'cog', 'globe'
        ];
        
        // 创建蜂窝结构
        for(let i = 0; i < rows; i++) {
            for(let j = 0; j < cols; j++) {
                const hex = document.createElement('div');
                hex.className = 'hex';
                hex.setAttribute('data-row', i);
                hex.setAttribute('data-col', j);
                
                // 偶数行偏移
                if(i % 2 === 0) {
                    hex.style.transform = 'translateX(30px)';
                }
                
                // 添加动画延迟
                const animDelay = (i * cols + j) * 0.1;
                const floatDelay = (Math.random() * 2).toFixed(2);
                const floatDuration = (2 + Math.random() * 2).toFixed(2);
                
                hex.style.animationName = 'fadeIn, hexFloat';
                hex.style.animationDuration = `0.5s, ${floatDuration}s`;
                hex.style.animationDelay = `${animDelay}s, ${floatDelay}s`;
                hex.style.animationIterationCount = '1, infinite';
                hex.style.animationTimingFunction = 'ease-out, ease-in-out';
                hex.style.animationFillMode = 'forwards, none';
                
                // 添加深度感
                const zIndex = Math.floor(Math.random() * 10) - 5;
                hex.style.zIndex = zIndex;
                
                // 添加SVG图标
                const iconIndex = (i * cols + j) % icons.length;
                const iconName = icons[iconIndex];
                
                // 创建图标容器
                const iconContainer = document.createElement('div');
                iconContainer.className = 'hex-icon';
                
                // 添加FontAwesome图标
                const icon = document.createElement('i');
                icon.className = `fas fa-${iconName}`;
                iconContainer.appendChild(icon);
                
                hex.appendChild(iconContainer);
                container.appendChild(hex);
            }
        }
        
        // 添加交互效果
        const allHexes = container.querySelectorAll('.hex');
        
        allHexes.forEach(hex => {
            hex.addEventListener('mouseover', function() {
                // 为当前六边形添加活跃状态
                this.classList.add('active');
                
                // 获取相邻六边形
                const row = parseInt(this.getAttribute('data-row'));
                const col = parseInt(this.getAttribute('data-col'));
                const neighbors = getNeighbors(row, col, rows, cols);
                
                // 应用效果到相邻六边形
                neighbors.forEach(pos => {
                    const selector = `.hex[data-row="${pos.row}"][data-col="${pos.col}"]`;
                    const neighborHex = container.querySelector(selector);
                    if (neighborHex) {
                        neighborHex.style.transform = `scale(1.05) translateZ(5px) ${row % 2 === 0 ? 'translateX(30px)' : ''}`;
                        neighborHex.style.borderColor = 'rgba(212,183,143,0.4)';
                        neighborHex.style.zIndex = '5';
                        neighborHex.style.animation = 'none';
                    }
                });
                
                // 移动整个蜂窝容器以创建3D效果
                const honeyContainer = document.querySelector('.hex-sphere-container');
                if (honeyContainer) {
                    const xPos = (this.offsetLeft / container.offsetWidth) - 0.5;
                    const yPos = (this.offsetTop / container.offsetHeight) - 0.5;
                    honeyContainer.style.transform = `translateY(-50%) scale(1.15) rotateX(${-yPos * 10}deg) rotateY(${xPos * 10}deg)`;
                }
            });
            
            hex.addEventListener('mouseout', function() {
                // 移除活跃状态
                this.classList.remove('active');
                
                // 重置相邻六边形
                const row = parseInt(this.getAttribute('data-row'));
                const col = parseInt(this.getAttribute('data-col'));
                const neighbors = getNeighbors(row, col, rows, cols);
                
                neighbors.forEach(pos => {
                    const selector = `.hex[data-row="${pos.row}"][data-col="${pos.col}"]`;
                    const neighborHex = container.querySelector(selector);
                    if (neighborHex) {
                        // 恢复原始样式
                        const isEvenRow = pos.row % 2 === 0;
                        neighborHex.style.transform = isEvenRow ? 'translateX(30px)' : '';
                        neighborHex.style.borderColor = 'rgba(212,183,143,0.25)';
                        neighborHex.style.zIndex = '';
                        
                        // 重新应用动画
                        const floatDelay = (Math.random() * 2).toFixed(2);
                        const floatDuration = (2 + Math.random() * 2).toFixed(2);
                        neighborHex.style.animation = `hexFloat ${floatDuration}s ${floatDelay}s infinite ease-in-out`;
                    }
                });
                
                // 恢复容器样式
                const honeyContainer = document.querySelector('.hex-sphere-container');
                if (honeyContainer) {
                    honeyContainer.style.transform = 'translateY(-50%) scale(1.15)';
                }
            });
        });
        
        // 鼠标移动时添加3D视差效果
        const sphereContainer = document.querySelector('.hex-sphere-container');
        if (sphereContainer) {
            document.addEventListener('mousemove', function(e) {
                if (!sphereContainer.matches(':hover')) {
                    const xPos = (e.clientX / window.innerWidth) - 0.5;
                    const yPos = (e.clientY / window.innerHeight) - 0.5;
                    
                    sphereContainer.style.transform = `translateY(-50%) scale(1.15) rotateX(${-yPos * 5}deg) rotateY(${xPos * 5}deg)`;
                }
            });
            
            // 重置位置当鼠标离开窗口
            document.addEventListener('mouseleave', function() {
                sphereContainer.style.transform = 'translateY(-50%) scale(1.15)';
            });
        }
    }

    // 获取相邻六边形
    function getNeighbors(row, col, maxRows, maxCols) {
        const neighbors = [];
        const isEvenRow = row % 2 === 0;
        
        // 相邻的六个方向
        const directions = [
            {row: -1, col: isEvenRow ? -1 : 0},  // 左上
            {row: -1, col: isEvenRow ? 0 : 1},   // 右上
            {row: 0, col: -1},                   // 左
            {row: 0, col: 1},                    // 右
            {row: 1, col: isEvenRow ? -1 : 0},   // 左下
            {row: 1, col: isEvenRow ? 0 : 1}     // 右下
        ];
        
        directions.forEach(dir => {
            const newRow = row + dir.row;
            const newCol = col + dir.col;
            
            // 检查边界
            if (newRow >= 0 && newRow < maxRows && newCol >= 0 && newCol < maxCols) {
                neighbors.push({row: newRow, col: newCol});
            }
        });
        
        return neighbors;
    }

    // 修改漂浮动画
    const floatStyle = document.createElement('style');
    floatStyle.textContent = `
        @keyframes hexFloat {
            0%, 100% {
                transform: translateY(0) translateZ(0) rotate(0deg);
            }
            25% {
                transform: translateY(-5px) translateZ(5px) rotate(0.5deg);
            }
            50% {
                transform: translateY(-10px) translateZ(10px) rotate(1deg);
            }
            75% {
                transform: translateY(-5px) translateZ(5px) rotate(0.5deg);
            }
        }
    `;
    document.head.appendChild(floatStyle);

    // 页面加载时创建蜂窝
    window.addEventListener('load', createHoneycomb);
    
    // 窗口大小改变时重新创建
    window.addEventListener('resize', debounce(createHoneycomb, 250));
    </script>

    <script>
    // 添加页面滚动动画
    const animateOnScroll = () => {
        const elements = document.querySelectorAll('.feature-card, .stat-card, .contact-item');
        
        elements.forEach(element => {
            const elementTop = element.getBoundingClientRect().top;
            const elementBottom = element.getBoundingClientRect().bottom;
            
            if (elementTop < window.innerHeight && elementBottom > 0) {
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
            }
        });
    };

    window.addEventListener('scroll', animateOnScroll);
    window.addEventListener('load', animateOnScroll);
    </script>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const numberElements = document.querySelectorAll('.stat-number');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const element = entry.target;
                    const finalValue = parseFloat(element.getAttribute('data-count'));
                    
                    animateValue(element, 0, finalValue, 2000);
                    observer.unobserve(element);
                }
            });
        }, {
            threshold: 0.1
        });

        numberElements.forEach(el => observer.observe(el));
    });

    function animateValue(element, start, end, duration) {
        let startTimestamp = null;
        const step = (timestamp) => {
            if (!startTimestamp) startTimestamp = timestamp;
            const progress = Math.min((timestamp - startTimestamp) / duration, 1);
            
            let currentValue = Math.floor(progress * (end - start) + start);
            
            // 格式化数字显示
            if (currentValue >= 1000000) {
                currentValue = (currentValue / 1000000).toFixed(1) + 'M+';
            } else if (currentValue >= 1000) {
                currentValue = (currentValue / 1000).toFixed(1) + 'K+';
            }
            
            element.textContent = currentValue;

            if (progress < 1) {
                window.requestAnimationFrame(step);
            }
        };
        
        window.requestAnimationFrame(step);
    }
    </script>

    <!-- 创建动态蜂巢背景 -->
    <script>
    function createHoneycombBackground() {
        const container = document.querySelector('.honeycomb-container');
        const hexCount = 30; // 调整蜂巢数量

        for (let i = 0; i < hexCount; i++) {
            const hex = document.createElement('div');
            hex.className = 'floating-hex';
            
            // 随机大小
            const size = Math.random() * 80 + 40;
            hex.style.width = `${size}px`;
            hex.style.height = `${size}px`;
            
            // 随机位置
            hex.style.left = `${Math.random() * 100}%`;
            hex.style.top = `${Math.random() * 100}%`;
            
            // 随机动画延迟和持续时间
            hex.style.animationDelay = `${Math.random() * 5}s`;
            hex.style.animationDuration = `${Math.random() * 15 + 15}s`;
            
            container.appendChild(hex);
        }
    }

    // 添加蜂巢样式
    const style = document.createElement('style');
    style.textContent = `
        .floating-hex {
            position: absolute;
            background: rgba(212,183,143,0.03);
            clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);
            pointer-events: none;
            animation: floatHex linear infinite;
        }

        @keyframes floatHex {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            20% {
                opacity: 0.2;
            }
            80% {
                opacity: 0.2;
            }
            100% {
                transform: translateY(-100vh) rotate(360deg);
                opacity: 0;
            }
        }

        /* 添加发光效果 */
        .floating-hex::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: inherit;
            filter: blur(4px);
            opacity: 0.5;
        }

        /* 添加渐变边框效果 */
        .floating-hex::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, 
                rgba(212,183,143,0.1),
                rgba(212,183,143,0.05),
                rgba(212,183,143,0.1)
            );
            clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);
        }
    `;
    document.head.appendChild(style);

    // 页面加载时创建蜂巢背景
    document.addEventListener('DOMContentLoaded', createHoneycombBackground);

    // 创建观察器实例
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'childList') {
                createHoneycombBackground();
            }
        });
    });

    // 配置观察选项
    const config = {
        childList: true,      // 观察目标子节点的变化
        subtree: true,        // 观察后代节点
        attributes: true      // 观察属性变化
    };

    // 页面加载时创建蜂巢背景并启动观察器
    document.addEventListener('DOMContentLoaded', () => {
        const container = document.querySelector('.honeycomb-container');
        if (container) {
            createHoneycombBackground();
            observer.observe(container, config);
        }
    });
    </script>

    <!-- 添加复制功能 -->
    <script>
    // 添加复制功能
    document.querySelectorAll('.contact-info').forEach(info => {
        info.addEventListener('click', function() {
            const text = this.textContent;
            navigator.clipboard.writeText(text).then(() => {
                // 创建提示元素
                const tooltip = document.createElement('div');
                tooltip.className = 'copy-tooltip';
                tooltip.textContent = '已复制!';
                tooltip.style.cssText = `
                    position: fixed;
                    background: var(--primary-gold);
                    color: var(--bg-darker);
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-size: 14px;
                    z-index: 1000;
                    opacity: 0;
                    transition: opacity 0.3s;
                `;
                
                // 定位到点击位置
                const rect = this.getBoundingClientRect();
                tooltip.style.left = rect.left + (rect.width - tooltip.offsetWidth) / 2 + 'px';
                tooltip.style.top = rect.bottom + 10 + 'px';
                
                document.body.appendChild(tooltip);
                
                // 显示动画
                requestAnimationFrame(() => {
                    tooltip.style.opacity = '1';
                    setTimeout(() => {
                        tooltip.style.opacity = '0';
                        setTimeout(() => tooltip.remove(), 300);
                    }, 1500);
                });
            });
        });
    });
    </script>

    <!-- 3D蜂窝球体 -->
    <script>
    function createHexSphere() {
        const container = document.querySelector('.hex-sphere');
        if (!container) return;

        const radius = 250; // 球体半径
        const rows = 15;    // 纬度划分
        const cols = 20;    // 经度划分

        // 创建球面蜂窝
        for (let i = 0; i < rows; i++) {
            const lat = (Math.PI / rows) * i;
            for (let j = 0; j < cols; j++) {
                const lon = ((2 * Math.PI) / cols) * j;

                // 计算球面坐标
                const x = radius * Math.sin(lat) * Math.cos(lon);
                const y = radius * Math.sin(lat) * Math.sin(lon);
                const z = radius * Math.cos(lat);

                // 创建蜂窝面
                const hex = document.createElement('div');
                hex.className = 'hex-face';

                // 计算旋转角度
                const rotX = (Math.atan2(y, z) * 180) / Math.PI;
                const rotY = (Math.atan2(x, z) * 180) / Math.PI;
                const rotZ = (Math.atan2(y, x) * 180) / Math.PI;

                // 应用变换
                hex.style.transform = `
                    translate3d(${x}px, ${y}px, ${z}px)
                    rotateX(${rotX}deg)
                    rotateY(${rotY}deg)
                    rotateZ(${rotZ}deg)
                `;

                // 根据位置设置透明度
                const opacity = Math.max(0.3, Math.abs(z) / radius);
                hex.style.opacity = opacity;

                container.appendChild(hex);
            }
        }

        // 添加鼠标交互
        let isDragging = false;
        let previousX = 0;
        let previousY = 0;
        let rotationX = 0;
        let rotationY = 0;

        container.addEventListener('mousedown', (e) => {
            isDragging = true;
            previousX = e.clientX;
            previousY = e.clientY;
            container.style.animation = 'none';
        });

        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;

            const deltaX = e.clientX - previousX;
            const deltaY = e.clientY - previousY;

            rotationY += deltaX * 0.5;
            rotationX -= deltaY * 0.5;

            container.style.transform = `
                rotateX(${rotationX}deg)
                rotateY(${rotationY}deg)
            `;

            previousX = e.clientX;
            previousY = e.clientY;
        });

        document.addEventListener('mouseup', () => {
            isDragging = false;
            setTimeout(() => {
                container.style.animation = 'rotateSphere 30s linear infinite';
            }, 1000);
        });
    }

    // 页面加载时初始化
    document.addEventListener('DOMContentLoaded', () => {
        createHexSphere();
    });

    // 优化性能
    window.addEventListener('resize', debounce(() => {
        const container = document.querySelector('.hex-sphere');
        if (container) {
            container.innerHTML = '';
            createHexSphere();
        }
    }, 250));
    </script>

    <script>
    // 添加二维码弹窗功能
    document.querySelectorAll('.qr-button').forEach(button => {
        button.addEventListener('click', function() {
            // 创建模态框
            const modal = document.createElement('div');
            modal.className = 'qr-modal';
            modal.innerHTML = `
                <div class="qr-modal-content">
                    <img src="{$contact_qrcode|default='/static/images/contact-qr.png'}" alt="联系方式二维码">
                    <p>扫描二维码添加客服微信</p>
                    <button class="close-modal">关闭</button>
                </div>
            `;
            
            // 添加到页面
            document.body.appendChild(modal);
            
            // 禁止背景滚动
            document.body.style.overflow = 'hidden';
            
            // 关闭按钮事件
            const closeModal = () => {
                modal.style.opacity = '0';
                setTimeout(() => {
                    modal.remove();
                    document.body.style.overflow = '';
                }, 300);
            };
            
            // 绑定关闭事件
            modal.querySelector('.close-modal').onclick = closeModal;
            
            // 点击背景关闭
            modal.onclick = (e) => {
                if (e.target === modal) closeModal();
            };
            
            // ESC键关闭
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') closeModal();
            }, { once: true });
        });
    });

    // 动态更新在线状态
    function updateOnlineStatus() {
        const now = new Date();
        const hour = now.getHours();
        const isWorkingHours = hour >= 9 && hour < 22;
        
        const statusDot = document.querySelector('.status-dot');
        const statusText = document.querySelector('.status-text');
        
        if (isWorkingHours) {
            statusDot.style.background = '#4CAF50';
            statusText.textContent = '在线';
            statusText.style.color = '#4CAF50';
        } else {
            statusDot.style.background = '#FFA000';
            statusText.textContent = '离线';
            statusText.style.color = '#FFA000';
        }
    }

    // 初始化并定期更新在线状态
    updateOnlineStatus();
    setInterval(updateOnlineStatus, 60000);
    </script>

    <script>
        // 处理动态效果和交互
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化AOS动画
            AOS.init({
                duration: 800,
                easing: 'ease-in-out',
                once: true,
                mirror: false,
                offset: 0,
                disable: 'mobile',
                startEvent: 'DOMContentLoaded',
                disableMutationObserver: false,
                throttleDelay: 99,
                debounceDelay: 50,
                anchorPlacement: 'top-bottom',
                useClassNames: true,
                initClassName: 'aos-init',
                animatedClassName: 'aos-animate'
            });

            // 数字计数器动画
            const counters = document.querySelectorAll('.counter');
            counters.forEach(counter => {
                const target = +counter.getAttribute('data-count');
                const duration = 2000; // 动画持续时间（毫秒）
                let startTime = null;
                const formatValue = (value) => {
                    if (value >= 1000) {
                        return (value / 1000).toFixed(1) + 'K';
                    }
                    return value;
                };

                function updateCounter(timestamp) {
                    if (!startTime) startTime = timestamp;
                    const progress = timestamp - startTime;
                    let currentValue = Math.min(Math.floor((progress / duration) * target), target);
                    
                    counter.textContent = formatValue(currentValue);
                    
                    if (progress < duration && currentValue < target) {
                        requestAnimationFrame(updateCounter);
                    } else {
                        counter.textContent = formatValue(target);
                    }
                }
                
                requestAnimationFrame(updateCounter);
            });

            // 点击复制功能
            const copyElements = document.querySelectorAll('.copy-text');
            copyElements.forEach(el => {
                el.addEventListener('click', function() {
                    const textToCopy = this.textContent;
                    navigator.clipboard.writeText(textToCopy).then(() => {
                        // 显示复制成功提示
                        const tooltip = document.createElement('div');
                        tooltip.className = 'copy-tooltip';
                        tooltip.textContent = '已复制!';
                        document.body.appendChild(tooltip);
                        
                        const rect = this.getBoundingClientRect();
                        tooltip.style.top = `${rect.top - 30}px`;
                        tooltip.style.left = `${rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2)}px`;
                        
                        setTimeout(() => {
                            tooltip.style.opacity = '0';
                            setTimeout(() => {
                                document.body.removeChild(tooltip);
                            }, 300);
                        }, 1500);
                    });
                });
            });

            // 回到顶部按钮
            const backToTopBtn = document.getElementById('back-to-top');
            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) {
                    backToTopBtn.classList.add('show');
                } else {
                    backToTopBtn.classList.remove('show');
                }
            });
            
            backToTopBtn.addEventListener('click', function() {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });

            // 移动端导航菜单
            const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
            const navLinks = document.querySelector('.nav-links');
            const navItems = document.querySelectorAll('.nav-item');

            if (mobileMenuToggle) {
                mobileMenuToggle.addEventListener('click', function() {
                    navLinks.classList.toggle('show');
                    this.innerHTML = navLinks.classList.contains('show') 
                        ? '<i class="fas fa-times"></i>' 
                        : '<i class="fas fa-bars"></i>';
                });
            }

            // 处理子菜单点击
            navItems.forEach(item => {
                const hasSubmenu = item.querySelector('.submenu');
                if (hasSubmenu && window.innerWidth <= 992) {
                    const link = item.querySelector('.nav-link');
                    link.addEventListener('click', function(e) {
                        if (window.innerWidth <= 992) {
                            e.preventDefault();
                            item.classList.toggle('active');
                            navItems.forEach(otherItem => {
                                if (otherItem !== item) {
                                    otherItem.classList.remove('active');
                                }
                            });
                        }
                    });
                }
            });
            
            // 处理窗口大小变化
            window.addEventListener('resize', function() {
                if (window.innerWidth > 992) {
                    navLinks.classList.remove('show');
                    mobileMenuToggle.innerHTML = '<i class="fas fa-bars"></i>';
                    navItems.forEach(item => {
                        item.classList.remove('active');
                    });
                }
            });

            // 添加滚动效果 - 头部缩小
            const header = document.querySelector('.header');
            let lastScrollTop = 0;
            
            window.addEventListener('scroll', function() {
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                
                // 滚动超过 100px 时添加压缩效果
                if (scrollTop > 100) {
                    header.classList.add('header-scrolled');
                } else {
                    header.classList.remove('header-scrolled');
                }
                
                lastScrollTop = scrollTop;
            });

            // 粒子效果初始化
            if (typeof particlesJS !== 'undefined') {
                particlesJS('particles-js', {
                    particles: {
                        number: { value: 80, density: { enable: true, value_area: 800 } },
                        color: { value: '#b39b77' },
                        shape: { type: 'polygon', polygon: { nb_sides: 6 } },
                        opacity: { value: 0.2, random: true },
                        size: { value: 3, random: true },
                        line_linked: {
                            enable: true,
                            distance: 150,
                            color: '#b39b77',
                            opacity: 0.1,
                            width: 1
                        },
                        move: {
                            enable: true,
                            speed: 2,
                            direction: 'none',
                            random: true,
                            out_mode: 'out'
                        }
                    },
                    interactivity: {
                        detect_on: 'canvas',
                        events: {
                            onhover: { enable: true, mode: 'grab' },
                            onclick: { enable: true, mode: 'push' }
                        },
                        modes: {
                            grab: { distance: 140, line_linked: { opacity: 0.3 } },
                            push: { particles_nb: 3 }
                        }
                    }
                });
            }
        });
    </script>
    <script src="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>

    <!-- 添加平滑的3D视差效果 -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const sphereContainer = document.querySelector('.hex-sphere-container');
        if (!sphereContainer) return;
        
        // 定义动画变量
        let currentX = 0, currentY = 0;
        let targetX = 0, targetY = 0;
        let speedX = 0.05, speedY = 0.05;
        
        // 平滑动画函数
        function animateContainer() {
            // 平滑动画逻辑
            currentX += (targetX - currentX) * speedX;
            currentY += (targetY - currentY) * speedY;
            
            // 设置变换
            sphereContainer.style.transform = `
                translateY(-50%) 
                scale(1.15) 
                rotateX(${-currentY * 8}deg) 
                rotateY(${currentX * 8}deg)
            `;
            
            // 继续动画循环
            requestAnimationFrame(animateContainer);
        }
        
        // 开始动画
        animateContainer();
        
        // 跟踪鼠标位置
        document.addEventListener('mousemove', function(e) {
            // 计算鼠标位置，值在 -0.5 到 0.5 之间
            targetX = (e.clientX / window.innerWidth) - 0.5;
            targetY = (e.clientY / window.innerHeight) - 0.5;
        });
        
        // 当鼠标离开窗口时，逐渐回到中心位置
        document.addEventListener('mouseleave', function() {
            targetX = 0;
            targetY = 0;
        });
        
        // 当用户滚动页面时，动态调整容器位置
        window.addEventListener('scroll', function() {
            // 检查容器是否在可视区域内
            const rect = sphereContainer.getBoundingClientRect();
            const isVisible = 
                rect.bottom > 0 &&
                rect.top < window.innerHeight;
            
            if (isVisible) {
                // 计算容器中心到屏幕中心的偏移量
                const centerOffsetY = ((rect.top + rect.bottom) / 2 - window.innerHeight / 2) / window.innerHeight;
                
                // 添加微小的倾斜效果，随着滚动改变
                targetY = centerOffsetY * 0.3;
            }
        });
        
        // 添加触摸设备支持
        document.addEventListener('touchmove', function(e) {
            if (e.touches.length > 0) {
                const touch = e.touches[0];
                targetX = (touch.clientX / window.innerWidth) - 0.5;
                targetY = (touch.clientY / window.innerHeight) - 0.5;
            }
        }, { passive: true });
        
        // 为蜂窝单元添加闪光效果
        function addGlowEffect() {
            const hexElements = document.querySelectorAll('.hex');
            if (!hexElements.length) return;
            
            // 添加随机闪光效果
            setInterval(() => {
                // 随机选择一个蜂窝单元
                const randomIndex = Math.floor(Math.random() * hexElements.length);
                const randomHex = hexElements[randomIndex];
                
                // 添加活跃类
                randomHex.classList.add('active');
                
                // 2秒后移除
                setTimeout(() => {
                    randomHex.classList.remove('active');
                }, 2000);
            }, 1000);
        }
        
        // 初始化闪光效果
        addGlowEffect();
    });
    </script>

</body>
</html>