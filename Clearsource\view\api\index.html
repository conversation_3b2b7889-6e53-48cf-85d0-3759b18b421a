<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>清空货源</title>
    <style>
        /* 将卡片居中 */
        #app {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #f0f2f5;
        }

        /* 进度面板样式 */
        .progress-panel {
            background: #393d49;
            position: relative;
            min-height: 160px;
            line-height: 20px;
            font-size: 12px;
            padding: 16px;
            color: #fff;
            border-radius: 4px;
            margin-top: 16px;
            width: 300px;
        }

        .title {
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 14px;
            font-weight: bold;
            color: #fff;
        }

        .actions {
            margin-top: 16px;
        }

        .tips {
            font-size: 12px;
            color: #888;
        }
    </style>
</head>
<body>

<div id="app">
    <el-card class="progress-panel">
        <template #header>
            <div class="title">清空货源</div>
        </template>
        <div>此操作将会清空所有货源，确认是否继续？</div>

        <!-- 新增自定义关键字输入框 -->
        <el-input 
            v-model="customKeyword" 
            placeholder="请输入关键字" 
            class="mt-2" 
            clearable>
        </el-input>

        <div class="actions">
            <el-button type="primary" @click="confirmClear">清空所有货源</el-button>
            <el-button type="danger" @click="confirmClearCustom">按关键字清空</el-button>
        </div>
        <div class="tips">提示：清空操作不可撤销，请谨慎操作。</div>
    </el-card>
</div>

<script src="/static/others/vue/vue.global.prod.js"></script>
<script src="/static/others/element-plus/index.full.min.js"></script>
<script src="/static/others/axios/axios.min.js"></script>

<script type="module">
    const { ref } = Vue;
    const { ElMessage, ElMessageBox } = ElementPlus;

    const app = Vue.createApp({
        setup() {
            const customKeyword = ref('');

            const confirmClear = async () => {
                try {
                    // 弹出确认对话框
                    await ElMessageBox.confirm(
                        '确定要清空所有货源吗？此操作不可撤销！',
                        '确认清空',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning',
                        }
                    );

                    // 执行清理操作
                    const res = await axios.post("/plugin/Clearsource/Api/clearSourceData");

                    if (res.data.code === 200) {
                        ElMessage.success('清空成功');
                    } else {
                        ElMessage.error(res.data.msg);
                    }
                } catch (error) {
                    if (error !== 'cancel') {
                        ElMessage.error('操作失败: ' + error.message);
                    }
                }
            };

            const confirmClearCustom = async () => {
                if (!customKeyword.value) {
                    return ElMessage.warning('请输入关键字');
                }

                try {
                    // 弹出确认对话框
                    await ElMessageBox.confirm(
                        `确定要清空包含关键字 "${customKeyword.value}" 的货源吗？此操作不可撤销！`,
                        '确认清空',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning',
                        }
                    );

                    // 发送关键字到后端清理
                    const res = await axios.post("/plugin/Clearsource/Api/clearSourceData", {
                        keyword: customKeyword.value
                    });

                    if (res.data.code === 200) {
                        ElMessage.success('按关键字清空成功');
                    } else {
                        ElMessage.error(res.data.msg);
                    }
                } catch (error) {
                    if (error !== 'cancel') {
                        ElMessage.error('操作失败: ' + error.message);
                    }
                }
            };

            return { customKeyword, confirmClear, confirmClearCustom };
        }
    });

    app.use(ElementPlus);
    app.mount("#app");
</script>
</body>
</html>
