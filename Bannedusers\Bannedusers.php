<?php

namespace plugin\Bannedusers;

use app\common\library\Plugin;
use think\facade\Db;

class Bannedusers extends Plugin {

    public function install() {
        return true;
    }

    public function uninstall() {
        // 查找黑名单查询菜单
        $parentMenu = Db::name('nav')
            ->where('name', '黑名单查询')
            ->where('type', 'M')
            ->find();
            
        if ($parentMenu) {
            // 删除所有子菜单
            Db::name('nav')->where('pid', $parentMenu['id'])->delete();
            // 删除父菜单
            Db::name('nav')->where('id', $parentMenu['id'])->delete();
        }
        
        return true;
    }
}
