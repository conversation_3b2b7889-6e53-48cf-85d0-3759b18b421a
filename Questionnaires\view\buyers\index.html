<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $siteName; ?>违规举报入口</title>
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <link rel="icon" href="<?php echo $favicon; ?>" type="image/x-icon">
    <style>
        body {
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: #333;
        }
        .container {
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        .page-header {
            text-align: center;
            margin-bottom: 30px;
            background: #fff;
            padding: 30px 20px;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,.05);
            position: relative;
            overflow: hidden;
        }
        .page-header::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, #1989fa, #5cadff);
        }
        .report-title {
            text-align: center;
            margin-bottom: 15px;
            font-size: 24px;
            font-weight: bold;
            color: #303133;
        }
        .report-desc {
            text-align: center;
            color: #606266;
            margin-bottom: 5px;
            line-height: 1.6;
            font-size: 14px;
        }
        .form-item {
            margin-bottom: 25px;
            background: #fff;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,.05);
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        .form-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px 0 rgba(0,0,0,.1);
            border-left: 3px solid #1989fa;
        }
        .form-title {
            font-size: 16px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            font-weight: 500;
            color: #303133;
        }
        .required-mark {
            color: #f56c6c;
            margin-right: 5px;
        }
        .form-question-number {
            display: inline-flex;
            justify-content: center;
            align-items: center;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: #1989fa;
            color: white;
            font-size: 14px;
            margin-right: 8px;
        }
        .submit-btn {
            text-align: center;
            margin-top: 30px;
            margin-bottom: 50px;
        }
        .upload-demo {
            max-width: 400px;
        }
        /* 加载动画样式 */
        #loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #fff;
            z-index: 9999;
        }
        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(0, 0, 0, 0.1);
            border-top-color: #1890ff;
            border-radius: 50%;
            animation: spin 1s ease infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        /* 添加响应式样式 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            .form-item {
                padding: 15px;
                margin-bottom: 15px;
            }
        }
        /* 自定义表单组件样式 */
        .el-radio {
            margin-right: 15px;
            margin-bottom: 10px;
        }
        .el-checkbox {
            margin-right: 15px;
            margin-bottom: 10px;
        }
        .el-input, .el-select {
            margin-bottom: 5px;
        }
        .el-textarea__inner {
            font-family: inherit;
        }
        .el-upload {
            width: 100%;
        }
        .el-upload-dragger {
            width: 100%;
        }
        .el-button--primary {
            padding: 12px 30px;
            font-size: 16px;
            transition: all 0.3s;
        }
        .el-button--primary:hover {
            transform: none;
            box-shadow: none;
        }
        .form-tip {
            color: #909399;
            font-size: 12px;
            margin-top: 5px;
            line-height: 1.4;
        }
        /* 上传组件美化 */
        .el-upload-list__item {
            transition: all 0.3s;
        }
        .el-upload-list__item:hover {
            transform: translateY(-2px);
        }
        .upload-icon {
            margin-right: 5px;
            vertical-align: middle;
        }
        /* 美化表单项图标 */
        .form-icon {
            margin-right: 6px;
            color: #909399;
        }
        /* 导航菜单样式 */
        .header {
            background: #fff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 100;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            position: relative;
        }

        .nav-left {
            display: flex;
            align-items: center;
        }

        .logo-container {
            margin-right: 40px;
        }

        .logo-img {
            height: 32px;
            width: auto;
        }

        .nav-menu {
            display: flex;
            gap: 24px;
            margin-left: 20px;
            align-items: center;
        }

        .nav-item {
            color: #606266;
            text-decoration: none;
            font-size: 14px;
            position: relative;
            padding: 20px 0;
            white-space: nowrap;
            display: inline-block;
        }

        .nav-item:hover {
            color: #409EFF;
        }

        .nav-item.active {
            color: #409EFF;
        }

        .nav-item.active::after {
            content: '';
            position: absolute;
            bottom: 15px;
            left: 0;
            width: 100%;
            height: 2px;
            background: #409EFF;
        }
        
        /* 添加子菜单样式 */
        .nav-item-with-children {
            position: relative;
            display: inline-flex;
            align-items: center;
        }
        
        .nav-item-with-children .nav-item {
            padding: 20px 0;
        }
        
        .nav-item-with-children:hover .sub-menu {
            display: block;
        }
        
        .sub-menu {
            display: none;
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: white;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            border-radius: 4px;
            padding: 5px 0;
            min-width: 140px;
            z-index: 10;
            text-align: center;
        }
        
        .sub-menu-item {
            display: block;
            padding: 8px 15px;
            color: #606266;
            text-decoration: none;
            font-size: 14px;
            white-space: nowrap;
            transition: all 0.3s;
        }
        
        .sub-menu-item:hover {
            background: #f0f7ff;
            color: #409EFF;
        }
        
        /* 移动端菜单按钮 */
        .mobile-menu-toggle {
            display: none;
            cursor: pointer;
            width: 30px;
            height: 30px;
            position: relative;
            z-index: 102;
        }
        
        .mobile-menu-line {
            display: block;
            width: 100%;
            height: 2px;
            background: #606266;
            margin: 6px 0;
            transition: all 0.3s;
        }
        
        .mobile-menu-toggle.active .mobile-menu-line:nth-child(1) {
            transform: translateY(8px) rotate(45deg);
        }
        
        .mobile-menu-toggle.active .mobile-menu-line:nth-child(2) {
            opacity: 0;
        }
        
        .mobile-menu-toggle.active .mobile-menu-line:nth-child(3) {
            transform: translateY(-8px) rotate(-45deg);
        }

        /* 页脚样式 */
        .footer {
            background: #000;
            padding: 15px 0;
            text-align: center;
            position: relative;
            margin-top: auto;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            color: rgba(255, 255, 255, 0.45);
            font-size: 12px;
        }

        .footer-link {
            color: rgba(255, 255, 255, 0.45);
            text-decoration: none;
            transition: color 0.3s;
            display: flex;
            align-items: center;
        }

        .footer-link:hover {
            color: rgba(255, 255, 255, 0.75);
        }

        .footer-divider {
            color: rgba(255, 255, 255, 0.45);
            margin: 0 10px;
        }

        .police-icon {
            width: 14px;
            height: 14px;
            margin-right: 5px;
            filter: opacity(0.45);
        }

        /* 主容器样式调整 */
        #app {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            padding-top: 60px; /* 为固定导航栏留出空间 */
        }

        .container {
            flex: 1;
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
            box-sizing: border-box;
        }

        /* 响应式样式 */
        @media (max-width: 768px) {
            .nav-container {
                height: auto;
                flex-direction: column;
                padding: 10px;
            }

            .nav-left {
                width: 100%;
                justify-content: space-between;
                margin-bottom: 10px;
            }

            .logo-container {
                margin-right: 0;
            }
            
            .mobile-menu-toggle {
                display: block;
            }
            
            .nav-menu {
                position: fixed;
                top: 60px;
                left: 0;
                width: 100%;
                height: 0;
                overflow: hidden;
                flex-direction: column;
                background: white;
                gap: 0;
                z-index: 100;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                transition: height 0.3s ease-in-out;
                margin-left: 0;
            }
            
            .nav-menu.active {
                height: auto;
                max-height: calc(100vh - 60px);
                overflow-y: auto;
            }

            .nav-item {
                padding: 12px 20px;
                width: 100%;
                box-sizing: border-box;
                border-bottom: 1px solid #f0f0f0;
            }
            
            .nav-item.active::after {
                display: none;
            }
            
            .nav-item-with-children {
                position: relative;
            }
            
            .sub-menu-toggle {
                position: absolute;
                right: 15px;
                top: 10px;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
            }
            
            .sub-menu {
                display: none;
                position: static;
                box-shadow: none;
                background: #f9f9f9;
                padding: 0;
                border-radius: 0;
                transform: none;
                min-width: 100%;
                text-align: left;
            }
            
            .sub-menu.active {
                display: block;
            }
            
            .sub-menu-item {
                padding: 12px 20px 12px 40px;
                border-bottom: 1px solid #f0f0f0;
                white-space: normal;
            }

            .footer-content {
                flex-wrap: wrap;
                gap: 10px;
            }
        }

        /* 修改商家中心按钮样式 */
        .merchant-btn {
            background: #4e6ef2 !important;
            border-color: #4e6ef2 !important;
            border-radius: 100px !important;
            padding: 8px 20px !important;
            font-size: 14px !important;
            height: 36px !important;
            font-weight: normal !important;
            box-shadow: none !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            gap: 6px !important;
            line-height: 1 !important;
            transition: all 0.3s ease !important;
        }

        .merchant-btn:hover {
            background: #4662d9 !important;
            border-color: #4662d9 !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 2px 8px rgba(78, 110, 242, 0.25) !important;
        }

        .merchant-btn:active {
            background: #3d53b7 !important;
            border-color: #3d53b7 !important;
            transform: translateY(0) !important;
            box-shadow: none !important;
        }

        .merchant-btn .icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 16px;
            height: 16px;
            margin-right: 2px;
        }

        .merchant-btn .text {
            margin-top: 1px;
            font-weight: 400;
        }

        /* 查询面板基础样式 */
        .query-panel {
            position: fixed;
            background: #fff;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        /* 桌面端样式 (宽度 >= 1200px) */
        @media screen and (min-width: 1200px) {
            .query-panel {
                top: 80px;
                right: 0;
                width: 360px;
                height: calc(100vh - 80px);
                transform: translateX(0);
                border-radius: 8px 0 0 8px;
            }
            
            .query-panel.collapsed {
                transform: translateX(360px);
            }
            
            .query-toggle {
                position: absolute;
                left: -20px;
                top: 50%;
                transform: translateY(-50%);
                width: 20px;
                height: 40px;
                background: #409eff;
                color: #fff;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                border-radius: 4px 0 0 4px;
            }
        }

        /* 平板端样式 (768px <= 宽度 < 1200px) */
        @media screen and (min-width: 768px) and (max-width: 1199px) {
            .query-panel {
                bottom: 0;
                left: 50%;
                transform: translateX(-50%) translateY(0);
                width: 80%;
                max-width: 600px;
                max-height: 80vh;
                border-radius: 8px 8px 0 0;
            }
            
            .query-panel.collapsed {
                transform: translateX(-50%) translateY(100%);
            }
            
            .query-toggle {
                position: absolute;
                top: -20px;
                left: 50%;
                transform: translateX(-50%);
                width: 40px;
                height: 20px;
                background: #409eff;
                color: #fff;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                border-radius: 4px 4px 0 0;
            }
            
            .query-toggle svg {
                transform: rotate(-90deg);
            }
            
            .query-panel.collapsed .query-toggle svg {
                transform: rotate(90deg);
            }
        }

        /* 移动端样式 (宽度 < 768px) */
        @media screen and (max-width: 767px) {
            .query-panel {
                bottom: 0;
                left: 0;
                width: 100%;
                max-height: 70vh;
                transform: translateY(0);
                border-radius: 8px 8px 0 0;
            }
            
            .query-panel.collapsed {
                transform: translateY(100%);
            }
            
            .query-toggle {
                position: absolute;
                top: -20px;
                left: 50%;
                transform: translateX(-50%);
                width: 40px;
                height: 20px;
                background: #409eff;
                color: #fff;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                border-radius: 4px 4px 0 0;
            }
            
            .query-toggle svg {
                transform: rotate(-90deg);
            }
            
            .query-panel.collapsed .query-toggle svg {
                transform: rotate(90deg);
            }
        }

        /* 查询面板内容样式 */
        .query-content {
            padding: 20px;
            height: 100%;
            overflow-y: auto;
        }

        .query-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
            color: #303133;
        }

        .report-item {
            border: 1px solid #ebeef5;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .report-status {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 13px;
            margin-bottom: 10px;
        }

        .status-pending {
            background-color: #e6a23c1a;
            color: #e6a23c;
        }

        .status-handled {
            background-color: #67c23a1a;
            color: #67c23a;
        }

        .status-rejected {
            background-color: #f56c6c1a;
            color: #f56c6c;
        }

        .report-time {
            font-size: 13px;
            color: #909399;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }

        .report-detail {
            font-size: 14px;
            color: #606266;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }

        .report-detail:last-child {
            margin-bottom: 0;
        }

        @media screen and (max-width: 767px) {
            .query-content {
                padding: 15px;
            }
            
            .report-item {
                padding: 12px;
            }
        }

        .report-actions {
            margin-top: 12px;
            display: flex;
            justify-content: flex-end;
            gap: 8px;
        }
        
        .report-actions .el-button {
            padding: 6px 12px;
            font-size: 13px;
        }
        
        .report-actions .el-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.25);
        }
        
        /* 验证码样式 */
        .captcha-container {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .captcha-input {
            flex: 1;
            margin-right: 10px;
        }
        
        .captcha-image {
            height: 38px;
            cursor: pointer;
            border-radius: 4px;
            border: 1px solid #dcdfe6;
        }
        
        .captcha-image:hover {
            border-color: #409eff;
        }

        /* 添加查询按钮样式 */
        .report-query-btn {
            position: absolute;
            right: 20px;
            top: 20px;
        }

        @media (max-width: 768px) {
            .report-query-btn {
                position: static;
                text-align: center;
                margin-top: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- 添加加载动画容器 -->
    <div id="loading">
        <div class="spinner"></div>
    </div>

    <div id="app" style="display: none">
        <!-- 头部导航 -->
        <div class="header">
            <div class="nav-container">
                <div class="nav-left">
                    <div class="logo-container">
                        <img :src="logo" class="logo-img" alt="Logo">
                    </div>
                    <!-- 添加移动菜单按钮 -->
                    <div class="mobile-menu-toggle" :class="{ active: mobileMenuActive }" @click="toggleMobileMenu">
                        <span class="mobile-menu-line"></span>
                        <span class="mobile-menu-line"></span>
                        <span class="mobile-menu-line"></span>
                    </div>
                    <div class="nav-menu" :class="{ active: mobileMenuActive }">
                        <template v-for="(item, index) in navItems" :key="index">
                            <!-- 有子菜单的导航项 -->
                            <div v-if="item.children && item.children.length > 0" class="nav-item-with-children">
                                <a :href="item.href" 
                                   class="nav-item" 
                                   :class="{ active: isCurrentPage(item.href) }">
                                    {{ item.name }}
                                </a>
                                <!-- 添加移动端子菜单切换按钮 -->
                                <div class="sub-menu-toggle" @click="toggleSubMenu($event)">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16">
                                        <path fill="currentColor" d="M16.59 8.59 12 13.17 7.41 8.59 6 10l6 6 6-6-1.41-1.41z"/>
                                    </svg>
                                </div>
                                <div class="sub-menu">
                                    <a v-for="(subItem, subIndex) in item.children" 
                                       :key="subIndex"
                                       :href="subItem.href"
                                       class="sub-menu-item">
                                        {{ subItem.name }}
                                    </a>
                                </div>
                            </div>
                            <!-- 没有子菜单的导航项 -->
                            <a v-else 
                               :href="item.href" 
                               class="nav-item" 
                               :class="{ active: isCurrentPage(item.href) }">
                                {{ item.name }}
                            </a>
                        </template>
                    </div>
                </div>
                <el-button 
                    type="primary" 
                    @click="goToMerchant" 
                    class="merchant-btn">
                    <span class="icon">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16">
                            <path fill="currentColor" d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                        </svg>
                    </span>
                    <span class="text">商家中心</span>
                </el-button>
            </div>
        </div>

        <div class="container">
            <div class="page-header">
                <h1 class="report-title">{{ siteName }}违规商品举报入口</h1>
                <div class="report-desc">{{ params.questionnaire_description || '请您根据举报页面提示，提交证明材料并填写举报说明，快发卡平台将会在7个工作日内处理您的举报信息。' }}</div>
                <!-- 添加查询按钮 -->
                <div class="report-query-btn">
                    <el-button type="primary" plain size="small" @click="openQueryDialog">
                        <svg viewBox="0 0 1024 1024" width="14" height="14" style="margin-right: 4px; vertical-align: -2px;">
                            <path fill="currentColor" d="M771.776 950.72L373.76 552.704l-2.048-2.048c-48.128-51.712-74.752-118.784-74.752-188.928 0-152.576 124.416-277.504 277.504-277.504 70.144 0 137.216 26.624 188.928 74.752l2.048 2.048 398.016 398.016-52.224 52.224-398.016-398.016c-36.864-33.28-85.504-51.712-136.704-51.712-110.592 0-200.704 90.112-200.704 200.704 0 51.2 18.432 99.84 51.712 136.704l398.016 398.016-52.224 52.224z"/>
                        </svg>
                        查询举报
                    </el-button>
                </div>
            </div>
            
            <el-form :model="formData" ref="reportForm" label-position="top">
                <div v-for="(question, index) in params.question_types" :key="index" class="form-item">
                    <div class="form-title">
                        <span class="form-question-number">{{ index + 1 }}</span>
                        <span class="required-mark" v-if="question.required">*</span>
                        <span>{{ question.name }}</span>
                    </div>
                    
                    <!-- 输入框 -->
                    <template v-if="question.component === 'input'">
                        <el-input 
                            :placeholder="question.placeholder" 
                            v-model="formData[question.id]">
                            <template #prefix>
                                <svg v-if="question.id === 'shopLink'" class="form-icon" viewBox="0 0 1024 1024" width="16" height="16">
                                    <path fill="currentColor" d="M192 128v704h640V128H192zm-32-64h704a32 32 0 0 1 32 32v768a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V96a32 32 0 0 1 32-32z"></path>
                                    <path fill="currentColor" d="M544 224h96a32 32 0 0 1 0 64h-96v96a32 32 0 0 1-64 0v-96h-96a32 32 0 0 1 0-64h96v-96a32 32 0 0 1 64 0v96zM352 576h320q32 0 32 32t-32 32H352q-32 0-32-32t32-32zm0 128h320q32 0 32 32t-32 32H352q-32 0-32-32t32-32z"></path>
                                </svg>
                                <svg v-else-if="question.id === 'email'" class="form-icon" viewBox="0 0 1024 1024" width="16" height="16">
                                    <path fill="currentColor" d="M128 224v512a64 64 0 0 0 64 64h640a64 64 0 0 0 64-64V224H128zm0-64h768a64 64 0 0 1 64 64v512a128 128 0 0 1-128 128H192A128 128 0 0 1 64 736V224a64 64 0 0 1 64-64z"></path>
                                    <path fill="currentColor" d="M904 224 656.128 506.368 904 800H120L370.752 506.304 120 224h784zM120 160h784a64 64 0 0 1 64 64v512a64 64 0 0 1-64 64H120a64 64 0 0 1-64-64V224a64 64 0 0 1 64-64z"></path>
                                </svg>
                                <svg v-else-if="question.id === 'productLink'" class="form-icon" viewBox="0 0 1024 1024" width="16" height="16">
                                    <path fill="currentColor" d="M715.648 625.152 670.4 579.904l90.496-90.56c75.008-74.944 85.12-186.368 22.656-248.896-62.528-62.464-173.952-52.352-248.96 22.656L444.16 353.6l-45.248-45.248 90.496-90.496c100.032-99.968 251.968-110.08 339.456-22.656 87.488 87.488 77.312 239.424-22.656 339.456l-90.496 90.496zm-90.496 90.496-90.496 90.496C434.624 906.112 282.688 916.224 195.2 828.8c-87.488-87.488-77.312-239.424 22.656-339.456l90.496-90.496 45.248 45.248-90.496 90.56c-75.008 74.944-85.12 186.368-22.656 248.896 62.464 62.464 173.952 52.352 248.96-22.656l90.496-90.496 45.248 45.248zm0-362.048 45.248 45.248L398.848 670.4 353.6 625.152 625.152 353.6z"></path>
                                </svg>
                            </template>
                        </el-input>
                    </template>
                    
                    <!-- 文本域 -->
                    <template v-if="question.component === 'textarea'">
                        <el-input 
                            type="textarea" 
                            :rows="4" 
                            :placeholder="question.placeholder" 
                            v-model="formData[question.id]">
                        </el-input>
                    </template>
                    
                    <!-- 单选框 -->
                    <template v-if="question.component === 'radio'">
                        <el-radio-group v-model="formData[question.id]">
                            <el-radio 
                                v-for="(option, optionIndex) in question.options" 
                                :key="optionIndex" 
                                :label="option">
                                {{ option }}
                            </el-radio>
                        </el-radio-group>
                    </template>
                    
                    <!-- 多选框 -->
                    <template v-if="question.component === 'checkbox'">
                        <el-checkbox-group v-model="formData[question.id]">
                            <el-checkbox 
                                v-for="(option, optionIndex) in question.options" 
                                :key="optionIndex" 
                                :label="option">
                                {{ option }}
                            </el-checkbox>
                        </el-checkbox-group>
                    </template>
                    
                    <!-- 下拉框 -->
                    <template v-if="question.component === 'select'">
                        <el-select 
                            v-model="formData[question.id]" 
                            :placeholder="question.placeholder || '请选择'" 
                            style="width: 100%"
                            :multiple="!!question.multiple"
                            collapse-tags
                            collapse-tags-tooltip
                            :max-collapse-tags="3"
                            :multiple-limit="question.maxSelect || 0">
                            <el-option 
                                v-for="(option, optionIndex) in question.options" 
                                :key="optionIndex"
                                :label="option"
                                :value="option">
                            </el-option>
                        </el-select>
                    </template>
                    
                    <!-- 日期选择 -->
                    <template v-if="question.component === 'date'">
                        <el-date-picker
                            v-model="formData[question.id]"
                            type="date"
                            :placeholder="question.placeholder"
                            style="width: 100%">
                        </el-date-picker>
                    </template>
                    
                    <!-- 时间选择 -->
                    <template v-if="question.component === 'time'">
                        <el-time-picker
                            v-model="formData[question.id]"
                            :placeholder="question.placeholder"
                            style="width: 100%">
                        </el-time-picker>
                    </template>
                    
                    <!-- 图片上传 -->
                    <template v-if="question.component === 'upload'">
                        <el-upload
                            class="upload-demo"
                            :action="params.upload_api || '/api/upload'"
                            :on-success="(response, file, files) => handleUploadSuccess(response, file, files, question.id)"
                            :on-remove="(file, files) => handleFileRemove(file, files, question.id)"
                            :before-upload="(file) => beforeFileUpload(file, question)"
                            :file-list="fileList[question.id] || []"
                            multiple
                            :limit="question.maxFiles || 5"
                            drag>
                            <div style="padding: 20px 0;">
                                <svg class="upload-icon" viewBox="0 0 1024 1024" width="30" height="30">
                                    <path fill="#909399" d="M544 864V672h128L512 480 352 672h128v192H320v-1.6c-5.376.32-10.496 1.6-16 1.6A240 240 0 0 1 64 624c0-123.136 93.12-223.488 212.608-237.248A239.808 239.808 0 0 1 512 192a239.872 239.872 0 0 1 235.456 194.752C866.912 400.512 960 500.864 960 624a240 240 0 0 1-240 240h-16v-1.6H544z"></path>
                                </svg>
                                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                            </div>
                            <div class="form-tip">{{ question.placeholder || '最多上传5张图片，单张图片10MB以内' }}</div>
                        </el-upload>
                    </template>
                    
                    <!-- 评分 -->
                    <template v-if="question.component === 'rate'">
                        <el-rate v-model="formData[question.id]" :show-text="question.options && question.options.length > 0" :texts="question.options || []"></el-rate>
                    </template>
                    
                    <!-- 滑块 -->
                    <template v-if="question.component === 'slider'">
                        <el-slider v-model="formData[question.id]" :min="0" :max="100" :step="1" :show-tooltip="true"></el-slider>
                    </template>
                </div>
                
                <div class="submit-btn">
                    <el-button type="primary" :loading="submitting" @click="submitForm" size="large">
                        <svg viewBox="0 0 1024 1024" width="16" height="16" style="margin-right: 8px; vertical-align: -2px;">
                            <path fill="currentColor" d="M512 64a32 32 0 0 1 32 32v192a32 32 0 0 1-64 0V96a32 32 0 0 1 32-32zm0 640a32 32 0 0 1 32 32v192a32 32 0 1 1-64 0V736a32 32 0 0 1 32-32zm448-192a32 32 0 0 1-32 32H736a32 32 0 1 1 0-64h192a32 32 0 0 1 32 32zm-640 0a32 32 0 0 1-32 32H96a32 32 0 0 1 0-64h192a32 32 0 0 1 32 32zM195.2 195.2a32 32 0 0 1 45.248 0L376.32 331.008a32 32 0 0 1-45.248 45.248L195.2 240.448a32 32 0 0 1 0-45.248zm452.544 452.544a32 32 0 0 1 45.248 0L828.8 783.552a32 32 0 0 1-45.248 45.248L647.744 692.992a32 32 0 0 1 0-45.248zM828.8 195.264a32 32 0 0 1 0 45.184L692.992 376.32a32 32 0 0 1-45.248-45.248l135.808-135.808a32 32 0 0 1 45.248 0zm-452.544 452.48a32 32 0 0 1 0 45.248L240.448 828.8a32 32 0 0 1-45.248-45.248l135.808-135.808a32 32 0 0 1 45.248 0z"/>
                        </svg>
                        提交举报
                    </el-button>
                </div>
            </el-form>
        </div>

        <!-- 添加查询弹窗 -->
        <el-dialog
            v-model="queryDialogVisible"
            title="举报查询"
            width="400px"
            :close-on-click-modal="false"
            center>
            <el-form @submit.prevent="queryReports">
                <el-form-item>
                    <el-input
                        v-model="queryEmail"
                        placeholder="请输入举报时使用的邮箱"
                        clearable>
                        <template #prefix>
                            <svg class="form-icon" viewBox="0 0 1024 1024" width="16" height="16">
                                <path fill="currentColor" d="M128 224v512a64 64 0 0 0 64 64h640a64 64 0 0 0 64-64V224H128zm0-64h768a64 64 0 0 1 64 64v512a128 128 0 0 1-128 128H192A128 128 0 0 1 64 736V224a64 64 0 0 1 64-64z"/>
                                <path fill="currentColor" d="M904 224 656.128 506.368 904 800H120L370.752 506.304 120 224h784zM120 160h784a64 64 0 0 1 64 64v512a64 64 0 0 1-64 64H120a64 64 0 0 1-64-64V224a64 64 0 0 1 64-64z"/>
                            </svg>
                        </template>
                    </el-input>
                </el-form-item>
                
                <el-form-item>
                    <div class="captcha-container">
                        <el-input
                            v-model="captchaCode"
                            placeholder="请输入验证码"
                            class="captcha-input">
                            <template #prefix>
                                <svg class="form-icon" viewBox="0 0 1024 1024" width="16" height="16">
                                    <path fill="currentColor" d="M704 576h160a32 32 0 1 1 0 64H704v160a32 32 0 1 1-64 0V640H480a32 32 0 0 1 0-64h160zm-320-32a32 32 0 0 1 0 64H192a32 32 0 0 1-32-32V192a32 32 0 0 1 32-32h320a32 32 0 0 1 0 64H224v320h160z"></path>
                                </svg>
                            </template>
                        </el-input>
                        <img :src="captchaUrl" class="captcha-image" @click="refreshCaptcha" alt="验证码" title="点击刷新">
                    </div>
                </el-form-item>
                
                <el-form-item>
                    <el-button type="primary" @click="queryReports" :loading="querying" style="width: 100%">
                        <svg viewBox="0 0 1024 1024" width="16" height="16" style="margin-right: 4px; vertical-align: -2px;">
                            <path fill="currentColor" d="M771.776 950.72L373.76 552.704l-2.048-2.048c-48.128-51.712-74.752-118.784-74.752-188.928 0-152.576 124.416-277.504 277.504-277.504 70.144 0 137.216 26.624 188.928 74.752l2.048 2.048 398.016 398.016-52.224 52.224-398.016-398.016c-36.864-33.28-85.504-51.712-136.704-51.712-110.592 0-200.704 90.112-200.704 200.704 0 51.2 18.432 99.84 51.712 136.704l398.016 398.016-52.224 52.224z"/>
                        </svg>
                        查询
                    </el-button>
                </el-form-item>
            </el-form>
            
            <div v-if="queryResults.length > 0" class="query-result">
                <div v-for="(report, index) in queryResults" :key="index" class="report-item">
                    <div class="report-status" :class="{
                        'status-pending': report.status === 0,
                        'status-handled': report.status === 1,
                        'status-rejected': report.status === 2
                    }">
                        <svg v-if="report.status === 0" viewBox="0 0 1024 1024" width="14" height="14" style="margin-right: 4px; vertical-align: -2px;">
                            <path fill="currentColor" d="M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896zm0 192a58.432 58.432 0 0 0-58.24 63.744l23.36 256.384a35.072 35.072 0 0 0 69.76 0l23.296-256.384A58.432 58.432 0 0 0 512 256zm0 512a51.2 51.2 0 1 0 0-102.4 51.2 51.2 0 0 0 0 102.4z"/>
                        </svg>
                        <svg v-else-if="report.status === 1" viewBox="0 0 1024 1024" width="14" height="14" style="margin-right: 4px; vertical-align: -2px;">
                            <path fill="currentColor" d="M512 64a448 448 0 1 1 0 896zm-55.808 536.384-138.304-136.448a38.4 38.4 0 1 0-54.336 54.336l165.248 163.84a38.272 38.272 0 0 0 54.336 0l297.792-297.728a38.4 38.4 0 1 0-54.4-54.4L456.192 600.384z"/>
                        </svg>
                        <svg v-else viewBox="0 0 1024 1024" width="14" height="14" style="margin-right: 4px; vertical-align: -2px;">
                            <path fill="currentColor" d="M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896zm0 393.664L407.936 353.6a38.4 38.4 0 1 0-54.336 54.336L457.664 512 353.6 616.064a38.4 38.4 0 1 0 54.336 54.336L512 566.336 616.064 670.4a38.4 38.4 0 1 0 54.336-54.336L566.336 512 670.4 407.936a38.4 38.4 0 1 0-54.336-54.336L512 457.664z"/>
                        </svg>
                        {{ report.status_text }}
                    </div>
                    <div class="report-time">
                        <svg viewBox="0 0 1024 1024" width="14" height="14" style="margin-right: 4px; vertical-align: -2px;">
                            <path fill="currentColor" d="M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896zm0 832a384 384 0 1 0 0-768 384 384 0 0 0 0 768zm32-384v-96a32 32 0 0 0-64 0v96h-96a32 32 0 0 0 0 64h96v96a32 32 0 1 0 64 0v-96h96a32 32 0 1 0 0-64h-96z"/>
                        </svg>
                        {{ report.create_time_text }}
                    </div>
                    <div class="report-detail">
                        <svg viewBox="0 0 1024 1024" width="14" height="14" style="margin-right: 4px; vertical-align: -2px;">
                            <path fill="currentColor" d="M288 320a224 224 0 1 0 448 0 224 224 0 1 0-448 0zm544 608H160a32 32 0 0 1-32-32v-96a160 160 0 0 1 160-160h448a160 160 0 0 1 160 160v96a32 32 0 0 1-32 32z"/>
                        </svg>
                        店铺：{{ report.shop_name }}
                    </div>
                    <div class="report-detail">
                        <svg viewBox="0 0 1024 1024" width="14" height="14" style="margin-right: 4px; vertical-align: -2px;">
                            <path fill="currentColor" d="M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896zm0 192a58.432 58.432 0 0 0-58.24 63.744l23.36 256.384a35.072 35.072 0 0 0 69.76 0l23.296-256.384A58.432 58.432 0 0 0 512 256zm0 512a51.2 51.2 0 1 0 0-102.4 51.2 51.2 0 0 0 0 102.4z"/>
                        </svg>
                        违规类型：{{ report.violation_type }}
                    </div>
                    <div v-if="report.status === 2" class="report-detail" style="color: #f56c6c;">
                        <svg viewBox="0 0 1024 1024" width="14" height="14" style="margin-right: 4px; vertical-align: -2px;">
                            <path fill="currentColor" d="M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896zm0 393.664L407.936 353.6a38.4 38.4 0 1 0-54.336 54.336L457.664 512 353.6 616.064a38.4 38.4 0 1 0 54.336 54.336L512 566.336 616.064 670.4a38.4 38.4 0 1 0 54.336-54.336L566.336 512 670.4 407.936a38.4 38.4 0 1 0-54.336-54.336L512 457.664z"/>
                        </svg>
                        驳回原因：{{ report.reject_reason }}
                    </div>
                    <!-- 添加重新编辑按钮 -->
                    <div v-if="report.status === 2" class="report-actions">
                        <el-button type="primary" size="small" @click="editReport(report)" :loading="report.editing">
                            <svg viewBox="0 0 1024 1024" width="14" height="14" style="margin-right: 4px; vertical-align: -2px;">
                                <path fill="currentColor" d="M832 512a32 32 0 1 1 64 0v352a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32h352a32 32 0 0 1 0 64H192v640z"/>
                                <path fill="currentColor" d="m469.952 554.24 52.8-7.552L847.104 222.4a32 32 0 1 0-45.248-45.248L477.44 501.44l-7.552 52.8zm422.4-422.4a96 96 0 0 1 0 135.808l-331.84 331.84a32 32 0 0 1-18.112 9.088L436.8 623.68a32 32 0 0 1-36.224-36.224l15.104-105.6a32 32 0 0 1 9.024-18.112l331.904-331.84a96 96 0 0 1 135.744 0z"/>
                            </svg>
                            重新编辑
                        </el-button>
                    </div>
                </div>
            </div>
            <div v-else-if="hasQueried" style="text-align: center; color: #909399; padding: 20px 0;">
                <svg viewBox="0 0 1024 1024" width="64" height="64" style="margin-bottom: 8px;">
                    <path fill="#909399" d="M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896zm0 192a58.432 58.432 0 0 0-58.24 63.744l23.36 256.384a35.072 35.072 0 0 0 69.76 0l23.296-256.384A58.432 58.432 0 0 0 512 256zm0 512a51.2 51.2 0 1 0 0-102.4 51.2 51.2 0 0 0 0 102.4z"/>
                </svg>
                <div>暂无举报记录</div>
            </div>
        </el-dialog>

        <!-- 页脚 -->
        <footer class="footer">
            <div class="footer-content">
                <span>Powered by {{ siteName }}</span>
                <span class="footer-divider">|</span>
                <span>{{ icpNumber }}</span>
                <span class="footer-divider">|</span>
                <a :href="'https://beian.miit.gov.cn'" target="_blank" class="footer-link">
                    增值电信经营许可证(ICP/EDI):{{ icpCert }}
                </a>
                <span class="footer-divider">|</span>
                <a :href="'http://www.beian.gov.cn'" target="_blank" class="footer-link">
                    <img src="/assets/template/default/assets/wangan.a20583c8.png" class="police-icon" alt="公安备案图标">
                    {{ gaNumber }}
                </a>
            </div>
        </footer>
    </div>

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>
    
    <script>
    const { createApp, ref, reactive, onMounted } = Vue;
    const { ElMessage } = ElementPlus;
    
    // 配置axios
    axios.defaults.timeout = 15000;
    axios.defaults.baseURL = '';

    // 请求拦截器
    axios.interceptors.request.use(
        config => config,
        error => {
            console.error('请求错误:', error);
            return Promise.reject(error);
        }
    );

    // 响应拦截器
    axios.interceptors.response.use(
        response => response,
        error => {
            console.error('响应错误:', error);
            return Promise.reject(error);
        }
    );

    const app = createApp({
        setup() {
            const formData = reactive({
                shopLink: '',
                productLink: '',
                violationType: '',
                proofs: [],
                email: '',
                remark: '',
                reportId: 0
            });
            const fileList = reactive({});
            const submitting = ref(false);
            
            // 添加 params 的定义
            const params = reactive({
                questionnaire_description: '',
                question_types: [],
                upload_api: '',
                template_type: 'default'
            });
            
            // 添加查询相关的数据
            const showQueryPanel = ref(true);
            const queryEmail = ref('');
            const queryResults = ref([]);
            const querying = ref(false);
            const hasQueried = ref(false);
            const captchaCode = ref('');
            const captchaUrl = ref('/plugin/Questionnaires/api/captcha');
            
            // 移动菜单状态
            const mobileMenuActive = ref(false);
            
            // 添加导航菜单和页脚相关的数据
            const logo = ref('<?php echo addslashes($logo); ?>');
            const siteName = ref('<?php echo addslashes($siteName); ?>');
            const icpNumber = ref('<?php echo addslashes($icpNumber); ?>');
            const gaNumber = ref('<?php echo addslashes($gaNumber); ?>');
            const icpCert = ref('<?php echo addslashes($icpCert); ?>');
            const navItems = ref(JSON.parse('<?php echo $navItems; ?>'));
            
            // 判断当前页面是否激活
            const isCurrentPage = (href) => {
                return window.location.pathname === href;
            };
            
            // 判断父菜单是否包含当前活跃页面
            const isActiveParent = (item) => {
                if (isCurrentPage(item.href)) return true;
                if (item.children && item.children.length > 0) {
                    return item.children.some(child => isCurrentPage(child.href));
                }
                return false;
            };
            
            // 跳转到商家中心
            const goToMerchant = () => {
                window.location.href = '/merchant';
            };
            
            // 切换移动菜单显示状态
            const toggleMobileMenu = () => {
                mobileMenuActive.value = !mobileMenuActive.value;
            };
            
            // 切换子菜单显示状态
            const toggleSubMenu = (event) => {
                const subMenu = event.target.closest('.nav-item-with-children').querySelector('.sub-menu');
                if (subMenu) {
                    subMenu.classList.toggle('active');
                }
            };

            // 初始化表单数据
            const initFormData = () => {
                // 确保基础字段存在
                formData.shopLink = '';
                formData.productLink = '';
                formData.violationType = '';
                formData.proofs = [];
                formData.email = '';
                formData.remark = '';
                formData.reportId = 0;
                
                // 清空文件列表
                for (const key in fileList) {
                    delete fileList[key];
                }
                fileList.proofs = [];
                
                // 重新初始化问卷字段
                params.question_types.forEach(question => {
                    switch (question.component) {
                        case 'checkbox':
                            formData[question.id] = [];
                            break;
                        case 'radio':
                            formData[question.id] = '';
                            break;
                        case 'select':
                            formData[question.id] = question.multiple ? [] : '';
                            break;
                        case 'upload':
                            // 确保proofs字段已初始化为数组
                            if (question.id === 'proofs') {
                                formData.proofs = [];
                            } else {
                                formData[question.id] = [];
                            }
                            fileList[question.id] = [];
                            break;
                        case 'rate':
                            formData[question.id] = 0; // 评分默认0分
                            break;
                        case 'slider':
                            formData[question.id] = 50; // 滑块默认50
                            break;
                        default:
                            formData[question.id] = '';
                            break;
                    }
                });
            };

            // 获取问卷参数
            const getParams = async () => {
                try {
                    const response = await axios.get('/plugin/Questionnaires/api/getParams');
                    if (response.data.code === 200) {
                        Object.assign(params, response.data.data);
                        // 初始化表单数据
                        initFormData();
                        // 隐藏加载动画并显示主内容
                        document.getElementById("loading").style.display = "none";
                        document.getElementById("app").style.display = "block";
                    } else {
                        ElMessage.error(response.data.msg || '获取配置失败');
                    }
                } catch (error) {
                    console.error('Error:', error);
                    ElMessage.error('获取配置失败');
                }
            };

            const handleUploadSuccess = (response, file, files, questionId) => {
                console.log("文件上传响应:", response);
                
                // 检查各种可能的成功状态码，适配官方API响应格式
                const isSuccess = response.code === 200 || response.code === 1 || response.code === '200' || response.code === '1' || response.status === 'success';
                
                if (isSuccess) {
                    // 确保字段存在并且是数组
                    if (!formData[questionId]) {
                        if (questionId === 'proofs') {
                            formData.proofs = [];
                        } else {
                            formData[questionId] = [];
                        }
                    }
                    
                    // 确保formData[questionId]是数组
                    if (!Array.isArray(formData[questionId])) {
                        formData[questionId] = [];
                    }
                    
                    // 根据官方API格式获取URL
                    let fileUrl = '';
                    
                    if (response.data && response.data.url) {
                        fileUrl = response.data.url;
                    } else if (response.data && response.data.path) {
                        fileUrl = response.data.path;
                    } else if (response.data && response.data.src) {  // 官方API通常返回src字段
                        fileUrl = response.data.src;
                    } else if (response.url) {
                        fileUrl = response.url;
                    } else if (response.path) {
                        fileUrl = response.path;
                    } else if (response.src) {
                        fileUrl = response.src;
                    } else if (typeof response === 'string') {
                        // 如果响应直接是字符串
                        fileUrl = response;
                    }
                    
                    if (fileUrl) {
                        // 确保URL是绝对路径
                        if (fileUrl.indexOf('http') !== 0 && fileUrl.indexOf('/') !== 0) {
                            fileUrl = '/' + fileUrl;
                        }
                        
                        // 避免重复添加相同URL
                        if (!formData[questionId].includes(fileUrl)) {
                            formData[questionId].push(fileUrl);
                        }
                        console.log(`文件上传成功，当前${questionId}字段内容:`, formData[questionId]);
                    } else {
                        console.error("无法获取文件URL，原始响应:", response);
                    }
                    
                    // 更新文件列表，设置URL属性
                    file.url = fileUrl; // 确保file对象有url属性
                    fileList[questionId] = files;
                    ElMessage.success('上传成功');
                } else {
                    console.error("文件上传失败:", response);
                    ElMessage.error(response.msg || '上传失败');
                }
            };

            const handleFileRemove = (file, files, questionId) => {
                // 从表单数据中移除对应文件URL
                if (formData[questionId] && Array.isArray(formData[questionId])) {
                    // 尝试多种可能的URL来源
                    const fileUrl = file.url || 
                                   (file.response && file.response.data && file.response.data.url) || 
                                   (file.response && file.response.data && file.response.data.src) || 
                                   '';
                    
                    if (fileUrl) {
                        const index = formData[questionId].indexOf(fileUrl);
                        if (index !== -1) {
                            formData[questionId].splice(index, 1);
                            console.log(`已从formData中移除文件: ${fileUrl}`, formData[questionId]);
                        }
                    }
                }
                // 更新fileList
                fileList[questionId] = files;
            };

            const beforeFileUpload = (file, question) => {
                // 实现文件上传前的逻辑
                console.log(`即将上传文件: ${file.name}`, question);
                return true; // 返回true表示允许上传
            };

            const validateForm = () => {
                // 先处理违规类型的映射
                const violationTypeMap = {
                    'copyright': '著作权侵权',
                    'privacy': '隐私权侵权',
                    'transaction': '交易违规',
                    'product': '商品违规'
                };
                
                // 如果存在violationType字段且需要映射
                if (formData.violationType && violationTypeMap[formData.violationType]) {
                    formData.violationType = violationTypeMap[formData.violationType];
                }
                
                // 检查所有字段，仅验证后台标记为必填的字段
                for (const question of params.question_types) {
                    if (question.required) {
                        const value = formData[question.id];
                        
                        // 检查不同类型的输入
                        if (question.component === 'upload') {
                            if (!Array.isArray(value) || value.length === 0) {
                                ElMessage.error(`请上传${question.name}的文件`);
                                return false;
                            }
                        } else if (question.component === 'checkbox') {
                            if (!Array.isArray(value) || value.length === 0) {
                                ElMessage.error(`请选择${question.name}`);
                                return false;
                            }
                        } else if (question.component === 'rate') {
                            if (typeof value !== 'number' || value === 0) {
                                ElMessage.error(`请对${question.name}进行评分`);
                                return false;
                            }
                        } else if (question.component === 'slider') {
                            if (typeof value !== 'number') {
                                ElMessage.error(`请设置${question.name}的值`);
                                return false;
                            }
                        } else if (['date', 'time'].includes(question.component)) {
                            if (!value) {
                                ElMessage.error(`请选择${question.name}`);
                                return false;
                            }
                        } else if (['input', 'textarea'].includes(question.component)) {
                            if (!value || !value.trim()) {
                                ElMessage.error(`请填写${question.name}`);
                                return false;
                            }
                        } else if (['radio', 'select'].includes(question.component)) {
                            if (!value || (Array.isArray(value) && value.length === 0)) {
                                ElMessage.error(`请选择${question.name}`);
                                return false;
                            }
                        }
                    }
                }
                
                // 邮箱格式验证（如果邮箱字段已填写）
                if (formData.email && formData.email.trim() && !/^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$/.test(formData.email)) {
                    ElMessage.error('邮箱格式不正确，请重新填写');
                    return false;
                }
                
                console.log('表单验证通过，待提交数据:', formData);
                return true;
            };

            const submitForm = async () => {
                if (!validateForm()) {
                    return;
                }
                
                submitting.value = true;
                try {
                    // 深拷贝提交数据，避免修改原始数据
                    const submitData = JSON.parse(JSON.stringify(formData));
                    
                    // 确保proofs字段正确传递
                    if (Array.isArray(submitData.proofs) && submitData.proofs.length > 0) {
                        console.log("提交前的proofs数据:", submitData.proofs);
                    }
                    
                    // 检查是否是更新举报（有reportId表示更新）
                    const isUpdate = submitData.reportId > 0;
                    const actionText = isUpdate ? '更新' : '提交';
                    
                    // 直接使用application/json格式提交
                    const response = await axios.post('/plugin/Questionnaires/api/submitReport', submitData, {
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    if (response.data.code === 200) {
                        ElMessage.success(`举报${actionText}成功`);
                        // 跳转到成功页面
                        window.location.href = '/plugin/Questionnaires/api/showSuccess';
                    } else {
                        ElMessage.error(response.data.msg || `${actionText}失败`);
                    }
                } catch (error) {
                    console.error('Error:', error);
                    if (error.response) {
                        console.error('Response Error:', error.response.data);
                        ElMessage.error(error.response.data.msg || '提交失败，请稍后重试');
                    } else {
                        ElMessage.error('提交失败，请稍后重试');
                    }
                } finally {
                    submitting.value = false;
                }
            };

            // 刷新验证码
            const refreshCaptcha = () => {
                captchaUrl.value = `/plugin/Questionnaires/api/captcha?t=${new Date().getTime()}`;
                captchaCode.value = '';
            };
            
            // 添加查询相关的方法
            const toggleQueryPanel = () => {
                showQueryPanel.value = !showQueryPanel.value;
            };
            
            const queryReports = async () => {
                if (!queryEmail.value) {
                    ElMessage.warning('请输入邮箱地址');
                    return;
                }
                
                if (!/^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$/.test(queryEmail.value)) {
                    ElMessage.warning('请输入有效的邮箱地址');
                    return;
                }
                
                if (!captchaCode.value) {
                    ElMessage.warning('请输入验证码');
                    return;
                }
                
                querying.value = true;
                try {
                    const response = await axios.get('/plugin/Questionnaires/api/queryReportsByEmail', {
                        params: { 
                            email: queryEmail.value,
                            captcha: captchaCode.value
                        }
                    });
                    
                    if (response.data.code === 200) {
                        queryResults.value = response.data.data;
                        hasQueried.value = true;
                    } else {
                        ElMessage.error(response.data.msg || '查询失败');
                        refreshCaptcha(); // 查询失败刷新验证码
                    }
                } catch (error) {
                    console.error('Error:', error);
                    ElMessage.error('查询失败，请稍后重试');
                    refreshCaptcha(); // 查询出错刷新验证码
                } finally {
                    querying.value = false;
                }
            };

            const editReport = (report) => {
                // 防止重复点击
                if (report.editing) return;
                
                // 设置编辑状态
                report.editing = true;
                
                // 创建字段映射关系（驼峰命名转换）
                const fieldMapping = {
                    'shop_link': 'shopLink',
                    'shop_name': 'shopName',
                    'product_link': 'productLink',
                    'violation_type': 'violationType',
                    'reject_reason': 'rejectReason',
                    'create_time': 'createTime',
                    'update_time': 'updateTime'
                };
                
                // 清空表单数据
                initFormData();
                
                // 查询完整的举报信息，包括所有字段
                axios.get('/plugin/Questionnaires/api/getReport', {
                    params: { id: report.id }
                }).then(response => {
                    if (response.data.code === 200) {
                        const fullReport = response.data.data;
                        console.log('获取到完整的举报信息:', fullReport);
                        
                        // 存储reportId用于提交时更新而不是新建
                        formData.reportId = fullReport.id;
                        
                        // 填充表单数据
                        params.question_types.forEach(question => {
                            // 获取数据库字段名
                            const dbField = question.id.replace(/([A-Z])/g, '_$1').toLowerCase();
                            
                            // 检查报告中是否存在该字段数据
                            if (fullReport[dbField] !== undefined) {
                                // 根据组件类型处理数据
                                switch(question.component) {
                                    case 'upload':
                                        try {
                                            // 处理上传文件
                                            let fileUrls = [];
                                            if (typeof fullReport[dbField] === 'string') {
                                                // 尝试解析JSON字符串
                                                try {
                                                    fileUrls = JSON.parse(fullReport[dbField]);
                                                } catch (e) {
                                                    // 如果不是JSON格式，可能是单个URL
                                                    fileUrls = fullReport[dbField] ? [fullReport[dbField]] : [];
                                                }
                                            } else if (Array.isArray(fullReport[dbField])) {
                                                fileUrls = fullReport[dbField];
                                            }
                                            
                                            // 设置表单数据
                                            formData[question.id] = fileUrls;
                                            
                                            // 更新文件列表
                                            fileList[question.id] = fileUrls.map(url => ({
                                                name: url.split('/').pop(),
                                                url: url
                                            }));
                                            
                                            console.log(`设置上传字段 ${question.id}:`, formData[question.id]);
                                        } catch (e) {
                                            console.error(`处理上传字段 ${question.id} 出错:`, e);
                                            formData[question.id] = [];
                                            fileList[question.id] = [];
                                        }
                                        break;
                                        
                                    case 'checkbox':
                                    case 'select':
                                        // 处理多选类型数据
                                        if (question.multiple || question.component === 'checkbox') {
                                            try {
                                                if (typeof fullReport[dbField] === 'string') {
                                                    formData[question.id] = fullReport[dbField] ? JSON.parse(fullReport[dbField]) : [];
                                                } else if (Array.isArray(fullReport[dbField])) {
                                                    formData[question.id] = fullReport[dbField];
                                                } else {
                                                    formData[question.id] = [];
                                                }
                                            } catch (e) {
                                                console.error(`处理多选字段 ${question.id} 出错:`, e);
                                                formData[question.id] = [];
                                            }
                                        } else {
                                            // 单选下拉框
                                            formData[question.id] = fullReport[dbField] || '';
                                        }
                                        break;
                                        
                                    case 'rate':
                                    case 'slider':
                                        // 处理数值类型
                                        formData[question.id] = parseFloat(fullReport[dbField]) || 0;
                                        break;
                                        
                                    case 'date':
                                    case 'time':
                                        // 处理日期时间类型
                                        if (fullReport[dbField]) {
                                            try {
                                                const timestamp = parseInt(fullReport[dbField]) * 1000;
                                                formData[question.id] = new Date(timestamp);
                                            } catch (e) {
                                                formData[question.id] = '';
                                            }
                                        } else {
                                            formData[question.id] = '';
                                        }
                                        break;
                                        
                                    default:
                                        // 处理普通文本字段
                                        formData[question.id] = fullReport[dbField] || '';
                                }
                            }
                        });
                        
                        // 特殊处理一些字段，确保前端显示正确
                        if (fullReport.proofs) {
                            try {
                                const proofs = typeof fullReport.proofs === 'string' ? 
                                    JSON.parse(fullReport.proofs) : fullReport.proofs;
                                    
                                formData.proofs = Array.isArray(proofs) ? proofs : [];
                                
                                // 更新文件列表
                                fileList.proofs = formData.proofs.map(url => ({
                                    name: url.split('/').pop(),
                                    url: url
                                }));
                            } catch (e) {
                                console.error('处理proofs字段出错:', e);
                                formData.proofs = [];
                                fileList.proofs = [];
                            }
                        }
                        
                        // 滚动到表单顶部
                        window.scrollTo({
                            top: 0,
                            behavior: 'smooth'
                        });
                        
                        // 关闭查询面板
                        showQueryPanel.value = false;
                        
                        // 显示提示消息
                        ElMessage({
                            type: 'success',
                            message: '已加载举报信息，您可以修改后重新提交',
                            duration: 3000
                        });
                    } else {
                        ElMessage.error(response.data.msg || '加载举报信息失败');
                    }
                }).catch(error => {
                    console.error('获取举报详情失败:', error);
                    ElMessage.error('加载举报信息失败，请稍后重试');
                }).finally(() => {
                    // 清除编辑状态
                    report.editing = false;
                });
            };

            // 页面加载完成后初始化
            onMounted(() => {
                getParams();
                // 初始化验证码
                refreshCaptcha();
            });

            // 添加查询弹窗相关的数据
            const queryDialogVisible = ref(false);

            // 打开查询弹窗
            const openQueryDialog = () => {
                queryDialogVisible.value = true;
                refreshCaptcha();
            };

            return {
                formData,
                fileList,
                submitting,
                params,
                logo,
                siteName,
                icpNumber,
                gaNumber,
                icpCert,
                navItems,
                isCurrentPage,
                isActiveParent,
                goToMerchant,
                submitForm,
                handleUploadSuccess,
                handleFileRemove,
                beforeFileUpload,
                queryDialogVisible,
                queryEmail,
                queryResults,
                querying,
                hasQueried,
                captchaCode,
                captchaUrl,
                openQueryDialog,
                queryReports,
                editReport,
                refreshCaptcha,
                mobileMenuActive,
                toggleMobileMenu,
                toggleSubMenu
            };
        }
    });

    // 使用 Element Plus
    app.use(ElementPlus, {
        locale: ElementPlusLocaleZhCn
    });
    
    // 挂载应用
    app.mount('#app');
    </script>
</body>
</html> 