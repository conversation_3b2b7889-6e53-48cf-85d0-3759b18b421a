<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <meta http-equiv="Cache-Control" content="max-age=31536000">
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>查询客户联系方式/订单号</title>
    <style>
        .query-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 15px;
        }

        .query-input {
            display: flex;
            gap: 8px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .query-result {
            min-height: 200px;
        }

        .order-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .order-section.paid {
            background: #f0f9eb;
        }

        .order-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 12px;
            color: #303133;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .order-time {
            font-size: 14px;
            color: #909399;
            font-weight: normal;
        }

        .info-row {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 12px;
        }

        .info-item {
            display: flex;
            align-items: center;
            flex: 1;
            min-width: 250px;
        }

        .info-label {
            color: #606266;
            margin-right: 8px;
            white-space: nowrap;
        }

        .info-value {
            color: #303133;
            word-break: break-all;
        }

        .order-divider {
            height: 1px;
            background: #dcdfe6;
            margin: 15px 0;
        }

        .pagination-container {
            margin-top: 20px;
            display: flex;
            justify-content: center;
        }

        /* 移动端适配 */
        @media screen and (max-width: 768px) {
            .query-container {
                margin: 10px auto;
                padding: 0 10px;
            }

            .query-input {
                flex-direction: column;
            }

            .query-input .el-input {
                margin-right: 0;
                margin-bottom: 8px;
            }

            .query-input .el-radio-group {
                margin-right: 0;
                margin-bottom: 8px;
                width: 100%;
                display: flex;
            }

            .query-input .el-radio-group .el-radio-button {
                flex: 1;
            }

            .query-input .el-button {
                width: 100%;
            }

            .info-item {
                min-width: 100%;
            }

            .order-section {
                padding: 12px;
            }

            .order-title {
                flex-direction: column;
                align-items: flex-start;
                gap: 4px;
            }
        }
    </style>
</head>
<body>
<div id="app" class="query-container">
    <el-card shadow="never">
        <div class="query-input">
            <el-input
                v-model="query"
                placeholder="请输入订单号或联系方式"
                style="flex: 1; margin-right: 8px;"
                @keyup.enter="handleSearch"
            ></el-input>

            <el-radio-group 
                v-model="sortType" 
                size="default" 
                style="margin-right: 8px;"
            >
                <el-radio-button label="all">全部订单</el-radio-button>
                <el-radio-button label="paid">已支付订单</el-radio-button>
            </el-radio-group>

            <el-button class="query-button" @click="handleSearch">
                <el-icon><Search /></el-icon> 查询
            </el-button>
        </div>

        <div class="query-result" v-loading="loading">
            <div v-if="!orderData || !orderData.list || orderData.list.length === 0">
                <div class="info-row">
                    <div class="info-item">
                        <span class="info-label">商品：</span>
                    </div>
                </div>
                <div class="info-row">
                    <div class="info-item">
                        <span class="info-label">订单号：</span>
                    </div>
                </div>
                <div class="info-row">
                    <div class="info-item">
                        <span class="info-label">状态：</span>
                    </div>
                </div>
                <div class="info-row">
                    <div class="info-item">
                        <span class="info-label">联系方式：</span>
                    </div>
                </div>
            </div>
            <div v-else>
                <div v-for="(order, index) in orderData.list" 
                     :key="order.trade_no" 
                     class="order-section"
                     :class="{ 'paid': order.status === 1 }">
                    <div class="order-title">
                        订单 {{(currentPage - 1) * orderData.limit + index + 1}}
                        <span class="order-time">{{ formatTime(order.create_time) }}</span>
                    </div>
                    <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">商品：</span>
                            <span class="info-value">{{ order.goods_name }}</span>
                        </div>
                        <div class="info-item" v-if="order.parent_name">
                            <span class="info-label">上级商品：</span>
                            <span class="info-value">{{ order.parent_name }}</span>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">订单号：</span>
                            <span class="info-value">{{ order.trade_no }}</span>
                        </div>
                        <div class="info-item" v-if="order.contact_qq">
                            <span class="info-label">QQ：</span>
                            <span class="info-value">{{ order.contact_qq }}</span>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">状态：</span>
                            <el-tag size="small" :type="getStatusType(order.status)">
                                {{ getStatusText(order.status) }}
                            </el-tag>
                        </div>
                        <div class="info-item" v-if="order.contact_mobile">
                            <span class="info-label">手机：</span>
                            <span class="info-value">{{ order.contact_mobile }}</span>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-item">
                            <span class="info-label">联系方式：</span>
                            <span class="info-value">{{ order.contact }}</span>
                        </div>
                        <div class="info-item" v-if="order.contact_wechat">
                            <span class="info-label">微信：</span>
                            <span class="info-value">{{ order.contact_wechat }}</span>
                        </div>
                    </div>
                    <div class="order-divider" v-if="index < orderData.list.length - 1"></div>
                </div>
                
                <div class="pagination-container" v-if="orderData.total > orderData.limit">
                    <el-pagination
                        v-model:current-page="currentPage"
                        :page-size="orderData.limit"
                        :total="orderData.total"
                        @current-change="handlePageChange"
                        layout="prev, pager, next"
                    />
                </div>
            </div>
        </div>
    </el-card>
</div>

<script src="/static/others/vue/vue.global.prod.js"></script>
<script src="/static/others/element-plus/index.full.min.js"></script>
<script src="/static/others/axios/axios.min.js"></script>

<script>
const { ref, watch, onMounted } = Vue;
const { ElMessage } = ElementPlus;

const app = Vue.createApp({
    setup() {
        const loading = ref(false);
        const orderData = ref({
            list: [],
            total: 0,
            page: 1,
            limit: 3
        });
        const query = ref('');
        const currentPage = ref(1);
        const sortType = ref('desc');

        const formatTime = (timestamp) => {
            if (!timestamp) return '';
            const date = new Date(timestamp * 1000);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        };

        const fetchData = async (page = 1) => {
            loading.value = true;
            try {
                const params = {
                    query: query.value,
                    page: page,
                    sort: ['desc', 'asc'].includes(sortType.value) ? sortType.value : 'desc',
                    status: sortType.value === 'paid' ? 'paid' : 'all'
                };

                // 使用统一的查询接口
                const url = '/plugin/Fengzeroquery/api/getOrders';

                const result = await axios.get(url, { params });

                if (result?.data?.code === 200) {
                    orderData.value = result.data.data;
                } else if (result?.data?.code === 404) {
                    orderData.value = {
                        list: [],
                        total: 0,
                        page: 1,
                        limit: 3
                    };
                    ElMessage.warning('未找到订单');
                } else if (result?.data?.code === 403) {
                    ElMessage.error(result.data.msg || '无权限访问');
                } else {
                    ElMessage.error(result.data.msg || '查询失败');
                }
            } catch (error) {
                console.error('Error:', error);
                ElMessage.error('查询失败，请稍后重试');
            } finally {
                loading.value = false;
            }
        };

        const handleSearch = () => {
            currentPage.value = 1;
            fetchData(1);
        };

        const handlePageChange = (page) => {
            fetchData(page);
        };

        const validateQuery = (rule, value, callback) => {
            const filteredQuery = value.replace(/\s+/g, '');
            if (!filteredQuery) {
                callback(new Error('查询内容不能为空'));
                return;
            }

            if (!/^[A-Za-z0-9@\.]+$/.test(filteredQuery)) {
                callback(new Error('查询内容格式不正确'));
                return;
            }

            callback();
        };

        const getStatusType = (status) => {
            const types = {
                0: 'info',
                1: 'success',
                2: 'warning',
                3: 'danger'
            };
            return types[status] || 'info';
        };

        const getStatusText = (status) => {
            const texts = {
                0: '未支付',
                1: '已支付',
                2: '已关闭',
                3: '已退款'
            };
            return texts[status] || '未知';
        };

        return {
            loading,
            orderData,
            query,
            sortType,
            currentPage,
            handleSearch,
            handlePageChange,
            validateQuery,
            getStatusType,
            getStatusText,
            formatTime,
        };
    }
});

app.use(ElementPlus);
app.mount('#app');
</script>
</body>
</html>