<html>

<head> 
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1.0" />
  <meta http-equiv="Cache-Control" content="max-age=31536000">
  <link rel="stylesheet" href="/static/others/element-plus/index.css">
  <title></title>
  <style>
    /* 加载动画样式 */
    #loading {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #fff;
      z-index: 9999;
    }

    .spinner {
      width: 50px;
      height: 50px;
      border: 5px solid rgba(0, 0, 0, 0.1);
      border-top-color: #3498db;
      border-radius: 50%;
      animation: spin 1s ease infinite;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }
  </style>
  <style>
    .progress-panel {
      background: #393d49;
      position: relative;
      min-height: 100px;
      line-height: 20px;
      resize: none;
      overflow: hidden;
      font-size: 12px;
      padding: 6px 10px;
      color: #fff;
      border-radius: 4px;
    }

    .title {
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .actions {
      margin-top: 16px;
    }

    .tips {
      font-size: 12px;
      color: #888;
    }
  </style>
</head>

<body>
  <!-- 加载动画容器 -->
  <div id="loading">
    <div class="spinner"></div>
  </div>

  <!-- Vue 应用挂载点 -->
  <div id="app" style="display: none">

    <el-card shadow="never">

      <el-form :model="form" label-width="auto">
        <el-form-item label="APPID：">
          <el-input v-model="form.app_id" type="text" placeholder="请输入APPID" />
        </el-form-item>
        <el-form-item label="平台密钥：">
          <el-input v-model="form.app_secret" type="text" placeholder="请输入平台密钥" />
        </el-form-item>
        <el-form-item label="商品分类：">
            <el-select
                v-model="category_list"
                multiple
                collapse-tags
                style="margin-left: 20px;"
                placeholder="请选择">
                <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
            </el-select>
        </el-form-item>
      <p style="text-align: center;margin: 0 auto;margin-top: 16px;">
        <el-button type="primary" :loading="isLoading" @click="save">
          保存
        </el-button>
      </p>

      <el-alert style="margin-top: 8px;" :closable="false">
        <div style="font-size: 12px;">
          可用变量<br>年份：${year}<br>月份：${month}<br>日期：${day}<br>插件会自动判断当日是否已发布结算公告(包括手动)，如已发布过当日不再发布</div>
      </el-alert>
    </el-card>

  </div>

  <script src="/static/others/vue/vue.global.prod.js"></script>
  <script src="/static/others/element-plus/index.full.min.js"></script>
  <script src="/static/others/axios/axios.min.js"></script>

  <script type="module">
    const { ref, reactive, watch } = Vue;
    const { ElMessage, ElMessageBox } = ElementPlus;
    const { Loading } = ElementPlus;

    const isLoading = ref(false);

    const form = reactive({
      app_id: '',
      app_secret: '',
      category_list: [],
      options: [{
          value: '选项1',
          label: '黄金糕'
        }, {
          value: '选项2',
          label: '双皮奶'
        }, {
          value: '选项3',
          label: '蚵仔煎'
        }, {
          value: '选项4',
          label: '龙须面'
        }, {
          value: '选项5',
          label: '北京烤鸭'
        }]
    });


    const fetchData = async () => {
      isLoading.value = true;
      const res = await axios.post(
        "/plugin/KsushouClone/api/fetchData");
      isLoading.value = false;
      if (res.data?.code == 200) {
        form.title = res.data?.data?.app_id ?? '';
        form.content = res.data?.data?.app_secret ?? '';
      } else {
        ElMessage.error(res.data?.msg);
      }
    }

    fetchData();


    const save = async () => {
      isLoading.value = true;
      const res = await axios.post(
        "/plugin/KsushouClone/api/save",
        form
      );
      isLoading.value = false;
      if (res.data?.code == 200) {
        ElMessage.success('保存成功');
        searchHost();
      } else {
        ElMessage.error(res.data?.msg);
      }
    }

    // Vue 应用
    const app = Vue.createApp({
      setup() {
        return {
          isLoading,
          form,
          save,
        };
      },
    });

    app.use(ElementPlus);
    app.mount("#app");

    // 移除加载动画
    window.onload = () => {
      document.getElementById("loading").style.display = "none"; // 隐藏加载动画
      document.getElementById("app").style.display = "block"; // 显示 Vue 应用
    };
  </script>
</body>

</html>