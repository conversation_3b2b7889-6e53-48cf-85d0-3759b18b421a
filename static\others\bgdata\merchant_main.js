$(window).load(function () {
    $(".loading").fadeOut();
});
$(function () {
    function load()
    {
        $.ajax({
            url: ajax_url(),
            type: 'post',
            success: function (res) {
                console.log(res);
                a(res.data.channelStatis);
                b(res.data.monthStatis);
                c(res.data.map_data, res.data.map_geo);
                d(res.data.yearStatis);


                $("#month_top_statis").empty();
                var month_top_statis_content = '<tr><th scope="col">排名</th><th scope="col">买家信息</th><th scope="col">金额</th></tr>';
                $.each(res.data.monthTopStatis, function (i, val) {
                    month_top_statis_content = month_top_statis_content + '<tr><td><span>' + (i + 1) + '</span></td> <td>' + val.contact + '</td> <td>' + val.transaction_money + '<br></td></tr>';
                });
                $("#month_top_statis").html(month_top_statis_content);


                $("#all_top_statis").empty();
                var all_top_statis_content = '<tr><th scope="col">排名</th><th scope="col">买家信息</th><th scope="col">金额</th></tr>';
                $.each(res.data.allTopStatis, function (i, val) {
                    all_top_statis_content = all_top_statis_content + '<tr><td><span>' + (i + 1) + '</span></td> <td>' + val.contact + '</td> <td>' + val.transaction_money + '<br></td></tr>';
                });
                $("#all_top_statis").html(all_top_statis_content);

                //////
                if (Number($("#all_sum").text()) !== res.data.incomeStatis.all_sum)
                {
                    $('#all_sum').lemCounter({
                        value_from: Number($("#all_sum").text()),
                        value_to: res.data.incomeStatis.all_sum,
                        animate_duration: 2,
                    });
                }
                if (Number($("#today_sum").text()) !== res.data.incomeStatis.today_sum)
                {
                    $('#today_sum').lemCounter({
                        value_from: Number($("#today_sum").text()),
                        value_to: res.data.incomeStatis.today_sum,
                        animate_duration: 2,
                    });
                }

                if (Number($("#yester_sum").text()) !== res.data.incomeStatis.yester_sum)
                {
                    $('#yester_sum').lemCounter({
                        value_from: Number($("#yester_sum").text()),
                        value_to: res.data.incomeStatis.yester_sum,
                        animate_duration: 2,
                    });
                }


                if (Number($("#today_paid").text()) !== res.data.orderStatis.today_paid)
                {
                    $('#today_paid').lemCounter({
                        value_from: Number($("#today_paid").text()),
                        value_to: res.data.orderStatis.today_paid,
                        animate_duration: 2,
                    });
                }

                if (Number($("#yester_paid").text()) !== res.data.orderStatis.yester_paid)
                {
                    $('#yester_paid').lemCounter({
                        value_from: Number($("#yester_paid").text()),
                        value_to: res.data.orderStatis.yester_paid,
                        animate_duration: 2,
                    });
                }



                if (Number($("#today_cashed").text()) !== res.data.cashStatis.today_cashed)
                {
                    $('#today_cashed').lemCounter({
                        value_from: Number($("#today_cashed").text()),
                        value_to: res.data.cashStatis.today_cashed,
                        animate_duration: 2,
                    });
                }

                if (Number($("#yester_cashed").text()) !== res.data.cashStatis.yester_cashed)
                {
                    $('#yester_cashed').lemCounter({
                        value_from: Number($("#yester_cashed").text()),
                        value_to: res.data.cashStatis.yester_cashed,
                        animate_duration: 2,
                    });
                }
                if (Number($("#all_order").text()) !== res.data.orderStatis.all_order)
                {
                    $('#all_order').lemCounter({
                        value_from: Number($("#all_order").text()),
                        value_to: res.data.orderStatis.all_order,
                        animate_duration: 2,
                    });
                }

            }
        });

    }

    var e1 = echarts.init(document.getElementById("echart1"));
    var e2 = echarts.init(document.getElementById("echart2"));
    var e3 = echarts.init(document.getElementById("echart4"));
    var h = echarts.init(document.getElementById("echart3"));
    load();
    var time = 5;
    setInterval(function () {
        $('#time_out').text("距下次自动刷新剩余" + time + "秒");
        if (time == 0)
        {
            load();
            time = 5;
        } else {
            time--;
        }
    }, 1000)

    function a(res_data) {
        option = {
            tooltip: {
                trigger: "item",
                formatter: "{a} <br/>{b} : {c}元 ({d}%)"
            },
            series: [{
                    name: "占比情况",
                    type: "pie",
                    radius: "50%",
                    center: ["50%", "50%"],
                    clockwise: false,
                    data: res_data,
                    label: {
                        normal: {
                            textStyle: {
                                color: "rgba(255,255,255,.6)",
                                fontSize: 14,
                            }
                        }
                    },
                    labelLine: {
                        normal: {
                            show: false
                        }
                    },
                    itemStyle: {
                        normal: {},
                        emphasis: {
                            borderWidth: 0,
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: "rgba(0, 0, 0, 0.5)"
                        }
                    }
                }],
            color: ["#62c98d", "#2f89cf", "#4cb9cf"],
        };
        e1.setOption(option);
        window.addEventListener("resize",
                function () {
                    e1.resize()
                })
    }
    function b(res_data) {
        var h = res_data.title;
        var i = ["交易金额"];
        option = {
            legend: {
                data: i,
                type: "scroll",
                textStyle: {
                    color: "#fff"
                },
                top: "0"
            },
            tooltip: {
                trigger: "axis",
                axisPointer: {
                    type: "shadow"
                }
            },
            color: ["#62c98d", "#2f89cf"],
            grid: {
                top: "14%",
                left: "15",
                right: "35",
                bottom: "12%",
                containLabel: true
            },
            xAxis: [{
                    type: "category",
                    axisLabel: {
                        textStyle: {
                            color: "rgba(255,255,255,.6)",
                        }
                    },
                    axisLine: {
                        lineStyle: {
                            color: "rgba(255,255,255,.1)"
                        }
                    },
                    data: h,
                }],
            yAxis: [{
                    name: "",
                    type: "value",
                    axisTick: {
                        show: false
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: "#2f2a7a"
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: "rgba(255,255,255,.6)"
                        }
                    },
                    axisLine: {
                        lineStyle: {
                            color: "rgba(255,255,255,.1)"
                        }
                    },
                }],
            dataZoom: [{
                    show: true,
                    height: 12,
                    xAxisIndex: [0],
                    bottom: 5,
                    start: 10,
                    end: 80,
                    handleIcon: "path://M306.1,413c0,2.2-1.8,4-4,4h-59.8c-2.2,0-4-1.8-4-4V200.8c0-2.2,1.8-4,4-4h59.8c2.2,0,4,1.8,4,4V413z",
                    handleSize: "110%",
                    handleStyle: {
                        color: "#d3dee5",
                    },
                    textStyle: {
                        color: "#fff"
                    },
                    borderColor: "rgba(255,255,255,.3)"
                }],
            series: {
                name: "交易金额",
                type: "bar",
                itemStyle: {
                    normal: {
                        color: ""
                    }
                },
                barWidth: "30",
                data: res_data.transaction_money
            }
        };
        e2.setOption(option);
        window.addEventListener("resize",
                function () {
                    e2.resize()
                })
    }
    function c(map_data, map_geo) {

        var f = map_data;
        var g = map_geo;
        var geoCoordMap = g;

        var data = f;
        var convertData = function (data) {
            var res = [];
            for (var i = 0; i < data.length; i++) {
                var geoCoord = geoCoordMap[data[i].name];
                if (geoCoord) {
                    res.push({
                        name: data[i].name,
                        value: geoCoord.concat(data[i].value)
                    });
                }
            }
            return res;
        };

        var findmax = f;
        var maxs = [];
        for (var i = 0; i < findmax.length; i++) {
            maxs.push(parseFloat(findmax[i].value));
        }

        maxs.sort(function(a, b){return b - a});

        var ratio = maxs[0] / 25;

        var convertedData = [
            convertData(data.sort(function (a, b) {
                return b.value - a.value;
            }).slice(0, 6))
        ];
        var convertedData0 = [
            convertData(data)
        ];

        data.sort(function (a, b) {
            return    a.value - b.value;
        });


        option = {
            title: {
                text: '今日全国各城市交易额大数据',
                subtext: 'Transaction big data',
                left: 'center',
                textStyle: {
                    color: '#fff'
                }
            },
            tooltip: {
                trigger: "item",
                formatter: function (c) {
                    if (typeof (c.value)[2] == "undefined") {
                        return c.name + " : " + c.value
                    } else {
                        return c.name + " : " + c.value[2]
                    }
                }
            },
            geo: {
                map: "china",
                label: {
                    emphasis: {
                        show: false
                    }
                },
                roam: false,
                itemStyle: {
                    normal: {
                        areaColor: "#0f47da",
                        borderColor: "#002097"
                    },
                    emphasis: {
                        areaColor: "#293fff"
                    }
                }
            },
            series: [{
                    name: "消费金额",
                    type: "scatter",
                    coordinateSystem: "geo",
                    data: convertedData0[0],
                    symbolSize: function (val) {
                        return Math.max((val[2] / ratio), 8);
                    },
                    label: {
                        normal: {
                            formatter: "{b}",
                            position: "right",
                            show: false
                        },
                        emphasis: {
                            show: true
                        }
                    },
                    itemStyle: {
                        normal: {
                            color: "#ffeb7b"
                        }
                    }
                }, {
                    name: 'Top 5',
                    type: 'effectScatter',
                    coordinateSystem: 'geo',
                    data: convertedData[0],
                    symbolSize: function (val) {
                        return Math.max((val[2] / ratio), 8);
                    },
                    showEffectOn: 'render',
                    rippleEffect: {
                        brushType: 'stroke'
                    },
                    hoverAnimation: true,
                    label: {
                        normal: {
                            formatter: '{b}',
                            position: 'right',
                            show: true
                        }
                    },
                    itemStyle: {
                        normal: {
                            color: '#f4e925',
                            shadowBlur: 10,
                            shadowColor: '#333',
                        }
                    },
                    zlevel: 1
                }]
        };
        h.setOption(option);
        window.addEventListener("resize",
                function () {
                    h.resize()
                })
    }
    function d(res_data) {
        option = {
            tooltip: {
                trigger: "axis",
                axisPointer: {
                    lineStyle: {
                        color: "#57617B"
                    }
                },
                formatter: "{b}:<br/> 交易额{c}元"
            },
            grid: {
                left: "0",
                right: "20",
                top: "10",
                bottom: "20",
                containLabel: true
            },
            xAxis: [{
                    type: "category",
                    boundaryGap: false,
                    axisLabel: {
                        show: true,
                        textStyle: {
                            color: "rgba(255,255,255,.6)"
                        }
                    },
                    axisLine: {
                        lineStyle: {
                            color: "rgba(255,255,255,.1)"
                        }
                    },
                    data: res_data.title
                }],
            yAxis: [{
                    axisLabel: {
                        show: true,
                        textStyle: {
                            color: "rgba(255,255,255,.6)"
                        }
                    },
                    axisLine: {
                        lineStyle: {
                            color: "rgba(255,255,255,.1)"
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: "rgba(255,255,255,.1)"
                        }
                    }
                }],
            series: [{
                    name: "交易额",
                    type: "line",
                    smooth: true,
                    symbol: "circle",
                    symbolSize: 5,
                    showSymbol: false,
                    lineStyle: {
                        normal: {
                            width: 3
                        }
                    },
                    areaStyle: {
                        normal: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                    offset: 0,
                                    color: "rgba(98, 201, 141, 0.5)"
                                },
                                {
                                    offset: 1,
                                    color: "rgba(98, 201, 141, 0.1)"
                                }], false),
                            shadowColor: "rgba(0, 0, 0, 0.1)",
                            shadowBlur: 10
                        }
                    },
                    itemStyle: {
                        normal: {
                            color: "#4cb9cf",
                            borderColor: "rgba(98, 201, 141,0.27)",
                            borderWidth: 12
                        }
                    },
                    data: res_data.transaction_money
                }]
        };
        e3.setOption(option);
        window.addEventListener("resize",
                function () {
                    e3.resize()
                })
    }
});
