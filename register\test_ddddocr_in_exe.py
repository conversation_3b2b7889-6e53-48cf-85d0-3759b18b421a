#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试打包后的exe中ddddocr是否正常工作
"""

import os
import sys

def test_ddddocr_import():
    """测试ddddocr导入"""
    print("[测试] 测试ddddocr导入...")
    
    try:
        import ddddocr
        print("[成功] ddddocr导入成功")
        return True
    except ImportError as e:
        print(f"[失败] ddddocr导入失败: {e}")
        return False
    except Exception as e:
        print(f"[失败] ddddocr导入异常: {e}")
        return False

def test_ddddocr_init():
    """测试ddddocr初始化"""
    print("[测试] 测试ddddocr初始化...")
    
    try:
        import ddddocr
        ocr = ddddocr.DdddOcr()
        print("[成功] ddddocr初始化成功")
        return True
    except Exception as e:
        print(f"[失败] ddddocr初始化失败: {e}")
        return False

def test_model_files():
    """测试模型文件是否存在"""
    print("[测试] 检查模型文件...")
    
    try:
        import ddddocr
        import os
        from pathlib import Path
        
        # 检查是否在打包环境中
        if hasattr(sys, '_MEIPASS'):
            # 在打包的exe中
            base_path = Path(sys._MEIPASS)
            print(f"[信息] 运行在打包环境中: {base_path}")
            
            # 查找ddddocr模型文件
            ddddocr_path = base_path / 'ddddocr'
            if ddddocr_path.exists():
                print(f"[成功] 找到ddddocr目录: {ddddocr_path}")
                
                # 列出模型文件
                model_files = list(ddddocr_path.glob('**/*.onnx'))
                print(f"[信息] 找到模型文件: {len(model_files)} 个")
                for model_file in model_files:
                    print(f"  - {model_file}")
                
                return len(model_files) > 0
            else:
                print(f"[失败] 未找到ddddocr目录")
                return False
        else:
            # 在开发环境中
            ddddocr_path = Path(ddddocr.__file__).parent
            print(f"[信息] 运行在开发环境中: {ddddocr_path}")
            
            model_files = list(ddddocr_path.glob('*.onnx'))
            print(f"[信息] 找到模型文件: {len(model_files)} 个")
            for model_file in model_files:
                print(f"  - {model_file}")
            
            return len(model_files) > 0
            
    except Exception as e:
        print(f"[失败] 检查模型文件异常: {e}")
        return False

def test_simple_ocr():
    """测试简单OCR功能"""
    print("[测试] 测试简单OCR功能...")
    
    try:
        import ddddocr
        from PIL import Image
        import io
        
        # 创建一个简单的测试图片
        img = Image.new('RGB', (100, 30), color='white')
        
        # 转换为字节
        img_bytes = io.BytesIO()
        img.save(img_bytes, format='PNG')
        img_bytes = img_bytes.getvalue()
        
        # 初始化OCR
        ocr = ddddocr.DdddOcr()
        
        # 尝试识别（可能会失败，但不应该崩溃）
        try:
            result = ocr.classification(img_bytes)
            print(f"[成功] OCR识别完成: '{result}'")
            return True
        except Exception as ocr_e:
            print(f"[信息] OCR识别失败（正常，因为是空白图片）: {ocr_e}")
            return True  # 能到这里说明模型加载成功
            
    except Exception as e:
        print(f"[失败] OCR测试异常: {e}")
        return False

def main():
    """主函数"""
    print("[启动] ddddocr打包测试")
    print("=" * 50)
    
    # 显示运行环境信息
    if hasattr(sys, '_MEIPASS'):
        print(f"[环境] 打包环境: {sys._MEIPASS}")
    else:
        print(f"[环境] 开发环境: {os.getcwd()}")
    
    print(f"[环境] Python版本: {sys.version}")
    print()
    
    # 执行测试
    tests = [
        ("导入测试", test_ddddocr_import),
        ("模型文件检查", test_model_files),
        ("初始化测试", test_ddddocr_init),
        ("OCR功能测试", test_simple_ocr),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        result = test_func()
        results.append((test_name, result))
        print()
    
    # 显示结果汇总
    print("=" * 50)
    print("[结果] 测试结果汇总:")
    
    all_passed = True
    for test_name, result in results:
        status = "[成功]" if result else "[失败]"
        print(f"  {status} {test_name}")
        if not result:
            all_passed = False
    
    print()
    if all_passed:
        print("[完成] 所有测试通过！ddddocr在打包环境中正常工作")
    else:
        print("[警告] 部分测试失败，ddddocr可能无法正常工作")
    
    print()
    print("[说明] 如果在exe中运行此测试：")
    print("  - 所有测试通过：ddddocr打包成功")
    print("  - 初始化失败：模型文件未正确打包")
    print("  - 导入失败：ddddocr库未正确打包")

if __name__ == "__main__":
    main()
