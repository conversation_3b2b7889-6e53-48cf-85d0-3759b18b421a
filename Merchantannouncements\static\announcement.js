(function() {
    // 添加检查函数，确保在DOM加载完成后执行
    function initAnnouncement() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                // 监听 XMLHttpRequest 请求
                monitorXHRRequests();
                // 检查是否是刷新页面
                checkRefresh();
            });
        } else {
            monitorXHRRequests();
            checkRefresh();
        }
    }

    // 检查是否是刷新页面
    function checkRefresh() {
        const currentPath = window.location.pathname;
        // 如果不是登录页面且有token，说明是刷新页面
        if (!currentPath.includes('/login') && !currentPath.includes('/signin') && localStorage.getItem('merchant_token')) {
            window.announcementLoaded = false;
            loadAnnouncement();
        }
    }

    // 监听 XMLHttpRequest 请求
    function monitorXHRRequests() {
        const originalXHR = window.XMLHttpRequest;
        window.XMLHttpRequest = function() {
            const xhr = new originalXHR();
            const originalOpen = xhr.open;
            const originalSend = xhr.send;

            xhr.open = function() {
                this.addEventListener('load', function() {
                    try {
                        const response = JSON.parse(this.responseText);
                        if (response.code === 1 && response.msg === "登录成功" && response.data?.merchant_token) {
                            localStorage.setItem('merchant_token', response.data.merchant_token);
                            localStorage.setItem('login_time', new Date().getTime());
                            // 清除公告显示缓存
                            clearAnnouncementCache();
                            window.announcementLoaded = false;
                            setTimeout(loadAnnouncement, 500);
                        }
                    } catch (e) {
                        // 忽略非 JSON 响应
                    }
                });
                return originalOpen.apply(this, arguments);
            };

            xhr.send = function() {
                return originalSend.apply(this, arguments);
            };

            return xhr;
        };
    }

    // 检查是否应该显示公告
    function shouldShowAnnouncement(frequency) {
        const storageKey = 'announcement_shown_time';
        const now = new Date();
        const today = now.setHours(0, 0, 0, 0);
        
        // 获取上次显示时间
        const lastShownTime = localStorage.getItem(storageKey);
        const lastDate = lastShownTime ? new Date(parseInt(lastShownTime)).setHours(0, 0, 0, 0) : null;
        
        switch (frequency) {
            case 'once': // 仅弹一次（永久记住）
                // 如果有显示记录，就永远不再显示
                return !lastShownTime;
                
            case 'login': // 每次登录
                // 检查是否是登录后的显示
                const loginTime = localStorage.getItem('login_time');
                if (!loginTime) return false;
                
                // 如果最后显示时间在最后登录时间之后，说明这次登录已经显示过了
                if (lastShownTime && parseInt(lastShownTime) > parseInt(loginTime)) {
                    return false;
                }
                return true;
                
            case 'daily': // 每天一次
                // 如果今天已经显示过了，就不再显示
                return !lastShownTime || today !== lastDate;
                
            case 'weekly': // 每周一次
                if (!lastShownTime) return true;
                
                const lastWeek = new Date(parseInt(lastShownTime));
                const weekStart = new Date(now);
                weekStart.setHours(0, 0, 0, 0);
                weekStart.setDate(weekStart.getDate() - weekStart.getDay()); // 设置到本周一
                const lastWeekStart = new Date(lastWeek);
                lastWeekStart.setHours(0, 0, 0, 0);
                lastWeekStart.setDate(lastWeekStart.getDate() - lastWeekStart.getDay()); // 设置到显示时的那周的周一
                return weekStart.getTime() > lastWeekStart.getTime();
                
            default:
                return false;
        }
    }

    // 清除缓存的函数（在登录成功后调用）
    function clearAnnouncementCache() {
        // 不清除"仅弹一次"模式的显示记录
        const frequency = localStorage.getItem('announcement_frequency');
        if (frequency !== 'once') {
            localStorage.removeItem('announcement_shown_time');
        }
    }

    // 记录显示时间
    function recordShowTime() {
        localStorage.setItem('announcement_shown_time', new Date().getTime().toString());
    }

    // 主要的公告加载函数
    function loadAnnouncement() {
        if (window.announcementLoaded) {
            return;
        }
        
        window.announcementLoaded = true;

        fetch('/plugin/Merchantannouncements/api/fetchData', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache'
            },
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            if (data.code == 200 && data.data.status == 1 && data.data.announcement) {
                const { frequency, read_enabled, close_delay } = data.data;
                if (shouldShowAnnouncement(frequency)) {
                    showAnnouncement(
                        data.data.announcement,
                        parseInt(read_enabled),
                        parseInt(close_delay) || 0
                    );
                    recordShowTime();
                }
            }
        })
        .catch(error => console.error('获取公告失败:', error));
    }

    // 创建并显示弹窗
    function showAnnouncement(content, readEnabled, closeDelay) {
        // 创建遮罩层
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            z-index: 9998;
            backdrop-filter: blur(3px);
            transition: opacity 0.3s ease;
        `;

        // 创建弹窗容器
        const dialog = document.createElement('div');
        dialog.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 4px 24px rgba(0, 0, 0, 0.15);
            z-index: 9999;
            max-width: 600px;
            width: 90%;
            animation: slideIn 0.3s ease;
        `;

        // 添加动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from {
                    opacity: 0;
                    transform: translate(-50%, -48%);
                }
                to {
                    opacity: 1;
                    transform: translate(-50%, -50%);
                }
            }
        `;
        document.head.appendChild(style);

        // 创建标题
        const title = document.createElement('div');
        title.style.cssText = `
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #edf2f7;
            display: flex;
            align-items: center;
        `;

        // 添加图标到标题
        const icon = document.createElement('span');
        icon.innerHTML = '📢';
        icon.style.marginRight = '8px';
        title.appendChild(icon);
        
        const titleText = document.createElement('span');
        titleText.textContent = '系统公告';
        title.appendChild(titleText);

        // 创建内容
        const contentDiv = document.createElement('div');
        contentDiv.style.cssText = `
            max-height: 400px;
            overflow-y: auto;
            margin-bottom: 24px;
            line-height: 1.8;
            color: #4a5568;
            font-size: 16px;
            padding-right: 10px;
            scrollbar-width: thin;
            scrollbar-color: #cbd5e0 #f7fafc;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
        `;
        
        contentDiv.className = 'announcement-content w-e-text';
        // 直接使用处理好的内容
        contentDiv.innerHTML = content;

        // 添加自定义内容样式
        const contentStyle = document.createElement('style');
        contentStyle.textContent = `
            .announcement-content {
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
                font-size: 16px;
                line-height: 1.8;
                color: #4a5568;
            }
            /* 基础文本样式 */
            .announcement-content p {
                margin: 0 0 1em;
                line-height: 1.8;
            }
            /* 标题样式 */
            .announcement-content h1, 
            .announcement-content h2, 
            .announcement-content h3, 
            .announcement-content h4, 
            .announcement-content h5, 
            .announcement-content h6 {
                font-weight: 600;
                line-height: 1.4;
                margin: 1em 0 0.5em;
            }
            /* 富文本格式样式 */
            .announcement-content strong,
            .announcement-content b {
                font-weight: bold;
            }
            .announcement-content em,
            .announcement-content i {
                font-style: italic;
            }
            .announcement-content u {
                text-decoration: underline;
            }
            .announcement-content strike,
            .announcement-content del {
                text-decoration: line-through;
            }
            /* 列表样式 */
            .announcement-content ul,
            .announcement-content ol {
                padding-left: 2em;
                margin: 1em 0;
            }
            .announcement-content ul li {
                list-style: disc;
            }
            .announcement-content ol li {
                list-style: decimal;
            }
            /* 对齐方式 */
            .announcement-content .text-left {
                text-align: left;
            }
            .announcement-content .text-center {
                text-align: center;
            }
            .announcement-content .text-right {
                text-align: right;
            }
            .announcement-content .text-justify {
                text-align: justify;
            }
            /* 链接样式 */
            .announcement-content a {
                color: #4299e1;
                text-decoration: none;
            }
            .announcement-content a:hover {
                text-decoration: underline;
            }
            /* 图片样式 */
            .announcement-content img {
                max-width: 100%;
                height: auto;
                margin: 1em 0;
            }
            /* 表格样式 */
            .announcement-content table {
                width: 100%;
                border-collapse: collapse;
                margin: 1em 0;
            }
            .announcement-content table td,
            .announcement-content table th {
                border: 1px solid #e2e8f0;
                padding: 8px;
            }
            /* 引用样式 */
            .announcement-content blockquote {
                margin: 1em 0;
                padding-left: 1em;
                border-left: 4px solid #e2e8f0;
                color: #718096;
            }
        `;
        document.head.appendChild(contentStyle);

        // 添加编辑器样式
        const editorStyle = document.createElement('link');
        editorStyle.rel = 'stylesheet';
        editorStyle.href = 'https://unpkg.com/@wangeditor/editor@latest/dist/css/style.css';
        document.head.appendChild(editorStyle);

        // 自定义滚动条样式
        const scrollbarStyle = document.createElement('style');
        scrollbarStyle.textContent = `
            .announcement-content::-webkit-scrollbar {
                width: 6px;
            }
            .announcement-content::-webkit-scrollbar-track {
                background: #f7fafc;
                border-radius: 3px;
            }
            .announcement-content::-webkit-scrollbar-thumb {
                background: #cbd5e0;
                border-radius: 3px;
            }
        `;
        document.head.appendChild(scrollbarStyle);

        // 创建按钮容器
        const buttonContainer = document.createElement('div');
        buttonContainer.style.cssText = `
            text-align: right;
            margin-top: 20px;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        `;

        let isReading = false;
        
        // 修改朗读按钮的创建逻辑
        if (parseInt(readEnabled) === 1) {
            // 创建朗读按钮
            const readButton = document.createElement('button');
            readButton.style.cssText = `
                background: #4CAF50;
                color: white;
                border: none;
                padding: 10px 24px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 15px;
                font-weight: 500;
                transition: all 0.2s ease;
                box-shadow: 0 2px 6px rgba(76, 175, 80, 0.2);
                display: flex;
                align-items: center;
            `;
            
            // 添加朗读图标
            const speakerIcon = document.createElement('span');
            speakerIcon.innerHTML = '🔊';
            speakerIcon.style.marginRight = '8px';
            readButton.appendChild(speakerIcon);
            
            const readText = document.createElement('span');
            readText.textContent = '朗读';
            readButton.appendChild(readText);

            // 朗读功能
            readButton.onclick = () => {
                if (!isReading) {
                    // 开始朗读
                    const utterance = new SpeechSynthesisUtterance();
                    utterance.text = contentDiv.textContent;
                    utterance.lang = 'zh-CN';
                    utterance.rate = 1.0;
                    utterance.pitch = 1.0;
                    
                    utterance.onend = () => {
                        isReading = false;
                        readText.textContent = '朗读';
                        speakerIcon.innerHTML = '🔊';
                        readButton.style.background = '#4CAF50';
                    };

                    window.speechSynthesis.speak(utterance);
                    isReading = true;
                    readText.textContent = '停止';
                    speakerIcon.innerHTML = '⏹️';
                    readButton.style.background = '#f44336';
                } else {
                    window.speechSynthesis.cancel();
                    isReading = false;
                    readText.textContent = '朗读';
                    speakerIcon.innerHTML = '🔊';
                    readButton.style.background = '#4CAF50';
                }
            };

            buttonContainer.appendChild(readButton);
        }

        // 创建关闭按钮
        const closeButton = document.createElement('button');
        closeButton.style.cssText = `
            background: #4299e1;
            color: white;
            border: none;
            padding: 10px 24px;
            border-radius: 6px;
            cursor: not-allowed;
            font-size: 15px;
            font-weight: 500;
            transition: all 0.2s ease;
            box-shadow: 0 2px 6px rgba(66, 153, 225, 0.2);
            opacity: 0.7;
        `;
        
        // 处理倒计时
        let remainingTime = closeDelay;
        if (remainingTime > 0) {
            closeButton.textContent = `请等待 ${remainingTime} 秒`;
            closeButton.disabled = true;
            
            const timer = setInterval(() => {
                remainingTime--;
                if (remainingTime > 0) {
                    closeButton.textContent = `请等待 ${remainingTime} 秒`;
                } else {
                    clearInterval(timer);
                    closeButton.textContent = '我知道了';
                    closeButton.disabled = false;
                    closeButton.style.cursor = 'pointer';
                    closeButton.style.opacity = '1';
                    
                    // 添加鼠标悬停效果
                    closeButton.onmouseover = () => {
                        closeButton.style.background = '#3182ce';
                        closeButton.style.transform = 'translateY(-1px)';
                        closeButton.style.boxShadow = '0 4px 12px rgba(66, 153, 225, 0.3)';
                    };
                    closeButton.onmouseout = () => {
                        closeButton.style.background = '#4299e1';
                        closeButton.style.transform = 'translateY(0)';
                        closeButton.style.boxShadow = '0 2px 6px rgba(66, 153, 225, 0.2)';
                    };
                }
            }, 1000);
        } else {
            closeButton.textContent = '我知道了';
            closeButton.style.cursor = 'pointer';
            closeButton.style.opacity = '1';
            closeButton.disabled = false;
            
            // 添加鼠标悬停效果
            closeButton.onmouseover = () => {
                closeButton.style.background = '#3182ce';
                closeButton.style.transform = 'translateY(-1px)';
                closeButton.style.boxShadow = '0 4px 12px rgba(66, 153, 225, 0.3)';
            };
            closeButton.onmouseout = () => {
                closeButton.style.background = '#4299e1';
                closeButton.style.transform = 'translateY(0)';
                closeButton.style.boxShadow = '0 2px 6px rgba(66, 153, 225, 0.2)';
            };
        }
        
        // 组装弹窗
        dialog.appendChild(title);
        dialog.appendChild(contentDiv);
        buttonContainer.appendChild(closeButton);
        dialog.appendChild(buttonContainer);

        // 添加到页面
        document.body.appendChild(overlay);
        document.body.appendChild(dialog);

        // 修改关闭事件
        const closeDialog = () => {
            if (isReading) {
                window.speechSynthesis.cancel();
            }
            overlay.style.opacity = '0';
            dialog.style.opacity = '0';
            dialog.style.transform = 'translate(-50%, -48%)';
            setTimeout(() => {
                document.body.removeChild(overlay);
                document.body.removeChild(dialog);
                document.head.removeChild(style);
                document.head.removeChild(scrollbarStyle);
                document.head.removeChild(editorStyle);
                document.head.removeChild(contentStyle);
            }, 300);
        };

        closeButton.onclick = closeDialog;
        overlay.onclick = closeDialog;
    }

    // 启动初始化
    initAnnouncement();
})(); 