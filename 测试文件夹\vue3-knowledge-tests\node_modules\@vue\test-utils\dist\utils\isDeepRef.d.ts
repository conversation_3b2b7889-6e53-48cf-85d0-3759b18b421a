import type { DeepRef } from '../types';
/**
 * Checks if the given value is a DeepRef.
 *
 * For both arrays and objects, it will recursively check
 * if any of their values is a Ref.
 *
 * @param {DeepRef<T> | unknown} r - The value to check.
 * @returns {boolean} Returns true if the value is a DeepRef, false otherwise.
 */
export declare const isDeepRef: <T>(r: DeepRef<T> | unknown) => r is DeepRef<T>;
