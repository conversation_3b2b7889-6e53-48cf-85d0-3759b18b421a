<?php

return [
    "version" => "1.0.0",
    "min_system_version" => "2.12.0",
    "category_name" => "功能插件",
    "name" => "刷新点排行榜",
    "description" => "刷新点排行榜刷新之后就会显示到卡片里面",
    "logo" => 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgICA8cmVjdCB4PSI0MCIgeT0iMTIwIiB3aWR0aD0iMzAiIGhlaWdodD0iNDAiIGZpbGw9IiNGRjZCNkIiLz4KICAgIDxyZWN0IHg9Ijg1IiB5PSI4MCIgd2lkdGg9IjMwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjNEVDREM0Ii8+CiAgICA8cmVjdCB4PSIxMzAiIHk9IjEwMCIgd2lkdGg9IjMwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjNDVCN0QxIi8+CiAgICA8dGV4dCB4PSI0NSIgeT0iMTc1IiBmb250LXNpemU9IjE2IiBmaWxsPSIjNjY2Ij4zPC90ZXh0PgogICAgPHRleHQgeD0iOTAiIHk9IjE3NSIgZm9udC1zaXplPSIxNiIgZmlsbD0iIzY2NiI+MTwvdGV4dD4KICAgIDx0ZXh0IHg9IjEzNSIgeT0iMTc1IiBmb250LXNpemU9IjE2IiBmaWxsPSIjNjY2Ij4yPC90ZXh0PgogICAgPHBhdGggZD0iTTQwIDYwIEwxNjAgNjAiIHN0cm9rZT0iIzY2NiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtZGFzaGFycmF5PSI0Ii8+CiAgICA8Y2lyY2xlIGN4PSIxMDAiIGN5PSIzMCIgcj0iMTUiIGZpbGw9IiNGRkQ5M0QiLz4KICAgIDxwYXRoIGQ9Ik0xMDAgMjAgTDEwMCA0MCBNOTAgMzAgTDExMCAzMCIgc3Ryb2tlPSIjZmZmIiBzdHJva2Utd2lkdGg9IjMiLz4KICAgIDwvc3ZnPg==',
    "menu" => [
        [
            'tag' => 'iframe',
            'name' => '显示设置',
            'src' => (string)plugin_url("Sourcedisplay/Api/index", [], false, true),
        ]
    ],
    "hook" => [
        'MerchantJs' => 'plugin\Sourcedisplay\Hook::handle',
        'SimpleCommand' => 'plugin\Sourcedisplay\Hook::getTopSources',
    ],
]; 