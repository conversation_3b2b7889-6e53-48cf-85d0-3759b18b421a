<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>系统清理工具</title>
    <style>
        .page-container {
            display: flex;
            min-height: 100vh;
        }
        .sidebar {
            width: 160px;
            border-right: 1px solid #dcdfe6;
            padding: 20px 0;
            background-color: #fff;
        }
        .main-content {
            flex: 1;
            padding: 20px 30px;
        }
        .suggestion-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 0;
        }
        .stats-info {
            margin-top: 10px;
        }
        .button-group {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .el-form-item-description {
            color: #606266;
            font-size: 12px;
            line-height: 1.5;
        }
        .el-form-item-description ul {
            margin: 5px 0;
        }
        .el-form-item-description li {
            margin: 3px 0;
        }
        .el-menu {
            border-right: none !important;
        }
        .el-menu-item {
            height: 50px;
            line-height: 50px;
            padding: 0 15px !important;
        }
        .el-menu-item .el-icon {
            margin-right: 8px;
            width: 20px;
            text-align: center;
            font-size: 16px;
            vertical-align: middle;
        }
        .el-menu-item span {
            font-size: 13px;
            color: #303133;
        }
        .el-menu-item.is-active {
            background-color: #ecf5ff;
        }
        .el-menu-item.is-active span {
            color: #409eff;
        }
        .el-menu-item:hover {
            background-color: #f5f7fa;
        }
        .upload-button {
            display: inline-block;
        }
        .upload-button .el-button {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .upload-button .el-icon {
            margin-right: 2px;
        }
        .backup-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .backup-actions {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .backup-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            gap: 20px;
            padding-right: 20px;
        }
        .backup-info .filename {
            flex: 1;
            font-size: 14px;
            color: #303133;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            min-width: 0;
        }
        .backup-info .file-meta {
            flex-shrink: 0;
            display: flex;
            align-items: center;
            gap: 15px;
            color: #909399;
            font-size: 13px;
        }
        .backup-info .file-size {
            white-space: nowrap;
        }
        .backup-info .file-time {
            white-space: nowrap;
        }
        .el-table {
            --el-table-border-color: #EBEEF5;
            --el-table-header-background-color: #F5F7FA;
        }
        .el-table th {
            background-color: var(--el-table-header-background-color);
            color: #606266;
            font-weight: 500;
        }
        .el-table td {
            padding: 12px 0;
        }
        .el-table .el-button--link {
            padding: 4px 8px;
            font-size: 13px;
        }
        .el-tag {
            margin: 0 4px;
        }
        .el-table tbody tr:hover td {
            background-color: #F5F7FA;
        }
        .el-popover {
            max-width: 300px;
            font-size: 13px;
            line-height: 1.5;
            color: #606266;
        }
        .el-message-box {
            width: 400px;
        }
        .el-message {
            width: auto;
            min-width: 300px;
            max-width: 500px;
        }
        .upload-button .icon {
            margin-right: 4px;
            vertical-align: middle;
        }
        .upload-button .el-button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }
        .el-button svg {
            margin-top: -2px;
        }
        .el-form-item-description {
            font-size: 12px;
            color: #909399;
            line-height: 1.5;
            margin-top: 4px;
            padding-left: 1px;
            display: block !important;
        }
        .progress-container {
            width: 100%;
        }
        .progress-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .progress-status p {
            margin: 0;
        }
        .el-progress {
            width: 100%;
        }
    </style>
</head>

<body>
<div id="app">
    <div class="page-container">
        <!-- 左侧导航栏 -->
        <div class="sidebar">
            <el-menu
                :default-active="activeMenu"
                @select="handleMenuSelect"
                style="border-right: none">
                <el-menu-item index="login">
                    <el-icon>
                        <svg viewBox="0 0 1024 1024" width="24" height="24">
                            <path d="M832 64H192c-35.3 0-64 28.7-64 64v768c0 35.3 28.7 64 64 64h640c35.3 0 64-28.7 64-64V128c0-35.3-28.7-64-64-64zM384 192h256c17.7 0 32 14.3 32 32s-14.3 32-32 32H384c-17.7 0-32-14.3-32-32s14.3-32 32-32zm384 640H256V256h512v576z" fill="currentColor"/>
                            <path d="M320 384h384v32H320zm0 128h384v32H320zm0 128h384v32H320z" fill="currentColor"/>
                        </svg>
                    </el-icon>
                    <span>登录日志清理</span>
                </el-menu-item>
                <el-menu-item index="complaint">
                    <el-icon>
                        <svg viewBox="0 0 1024 1024" width="24" height="24">
                            <path d="M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 632H136V232h752v560z" fill="currentColor"/>
                            <path d="M424 352c48.6 0 88 39.4 88 88s-39.4 88-88 88-88-39.4-88-88 39.4-88 88-88zm224 400H376c-4.4 0-8-3.6-8-8v-48c0-4.4 3.6-8 8-8h272c4.4 0 8 3.6 8 8v48c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V391zm-88 236c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v128c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V627zm264-236c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v364c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V391z" fill="currentColor"/>
                        </svg>
                    </el-icon>
                    <span>投诉图片清理</span>
                </el-menu-item>
                <el-menu-item index="goods">
                    <el-icon>
                        <svg viewBox="0 0 1024 1024" width="24" height="24">
                            <path d="M832 312H696v-16c0-101.6-82.4-184-184-184s-184 82.4-184 184v16H192c-17.7 0-32 14.3-32 32v536c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V344c0-17.7-14.3-32-32-32zm-432-16c0-61.9 50.1-112 112-112s112 50.1 112 112v16H400v-16zm392 544H232V384h96v88c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-88h224v88c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-88h96v456z" fill="currentColor"/>
                        </svg>
                    </el-icon>
                    <span>商品清理</span>
                </el-menu-item>
                <el-menu-item index="order">
                    <el-icon>
                        <svg viewBox="0 0 1024 1024" width="24" height="24">
                            <path d="M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v584z" fill="currentColor"/>
                            <path d="M304 420h416v32H304zm0 120h416v32H304zm0 120h416v32H304z" fill="currentColor"/>
                        </svg>
                    </el-icon>
                    <span>订单清理</span>
                </el-menu-item>
                <el-menu-item index="message">
                    <el-icon>
                        <svg viewBox="0 0 1024 1024" width="24" height="24">
                            <path d="M464 512a48 48 0 1 0 96 0 48 48 0 1 0-96 0zm200 0a48 48 0 1 0 96 0 48 48 0 1 0-96 0zm-400 0a48 48 0 1 0 96 0 48 48 0 1 0-96 0zm661.2-173.6c-22.6-53.7-55-101.9-96.3-143.3a444.35 444.35 0 0 0-143.3-96.3C630.6 75.7 572.2 64 512 64h-2c-60.6.3-119.3 12.3-174.5 35.9a445.35 445.35 0 0 0-142 96.5c-40.9 41.3-73 89.3-95.2 142.8-23 55.4-34.6 114.3-34.3 174.9A449.4 449.4 0 0 0 112 714v152a46 46 0 0 0 46 46h152.1A449.4 449.4 0 0 0 510 960h2.1c59.9 0 118-11.6 172.7-34.3a444.48 444.48 0 0 0 142.8-95.2c41.3-40.9 73.8-88.7 96.5-142 23.6-55.2 35.6-113.9 35.9-174.5.3-60.9-11.5-120-34.8-175.6zm-151.1 438C704 845.8 611 884 512 884h-1.7c-60.3-.3-120.2-15.3-173.1-43.5l-8.4-4.5H188V695.2l-4.5-8.4C155.3 633.9 140.3 574 140 513.7c-.4-99.7 37.7-193.3 107.6-263.8 69.8-70.5 163.1-109.5 262.8-109.9h1.7c50 0 98.5 9.7 144.2 28.9 44.6 18.7 84.6 45.6 119 80 34.3 34.3 61.3 74.4 80 119 19.4 46.2 29.1 95.2 28.9 145.8-.6 99.6-39.7 192.9-110.1 262.7z" fill="currentColor"/>
                        </svg>
                    </el-icon>
                    <span>商户通知清理</span>
                </el-menu-item>
                <el-menu-item index="backup">
                    <el-icon>
                        <svg viewBox="0 0 1024 1024" width="24" height="24">
                            <path d="M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-40 824H232V136h560v752zM484 391c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v364c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V391zm-88 236c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v128c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V627zm264-236c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v364c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V391z" fill="currentColor"/>
                        </svg>
                    </el-icon>
                    <span>数据备份</span>
                </el-menu-item>
                <el-menu-item index="card">
                    <el-icon>
                        <svg viewBox="0 0 1024 1024" width="24" height="24">
                            <path d="M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 632H136V232h752v560zM610.3 476h123.4c1.3 0 2.3-3.6 2.3-8v-48c0-4.4-1-8-2.3-8H610.3c-1.3 0-2.3 3.6-2.3 8v48c0 4.4 1 8 2.3 8zm4.8 144h185.7c3.9 0 7.1-3.6 7.1-8v-48c0-4.4-3.2-8-7.1-8H615.1c-3.9 0-7.1 3.6-7.1 8v48c0 4.4 3.2 8 7.1 8zM224 673h43.9c4.2 0 7.6-3.3 7.9-7.5 3.8-50.5 46-90.5 97.2-90.5s93.4 40 97.2 90.5c0.3 4.2 3.7 7.5 7.9 7.5H522a8 8 0 0 0 8-8.4c-2.8-53.3-32-99.7-74.6-126.1a111.8 111.8 0 0 0 29.1-75.5c0-61.9-49.9-112-111.4-112s-111.4 50.1-111.4 112c0 29.1 11 55.5 29.1 75.5a158.09 158.09 0 0 0-74.6 126.1c-0.4 4.6 3.2 8.4 7.8 8.4z" fill="currentColor"/>
                        </svg>
                    </el-icon>
                    <span>卡密清理</span>
                </el-menu-item>
                <el-menu-item index="moneyLog">
                    <el-icon>
                        <svg viewBox="0 0 1024 1024" width="24" height="24">
                            <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm47.7-420.7c-25.7-4.9-45.8-9.1-45.8-22.3 0-12.1 15.4-20 37.7-20 24.3 0 41.1 7.9 47.7 21.7 1.8 3.7 5.5 6 9.6 6h32.2c6.6 0 11.5-6.3 9.7-12.6-7.8-26.7-33.8-45.1-74.2-45.1v-32c0-4.4-3.6-8-8-8h-32c-4.4 0-8 3.6-8 8v32c-46.9 0-81.4 23.7-81.4 59.3 0 36.1 29.5 47.8 89.1 57.3 35.7 5.7 45.8 12.2 45.8 24.7 0 13.6-17.3 22.3-43.1 22.3-28.7 0-47.9-9.7-54.3-25.3-1.6-3.9-5.4-6.4-9.6-6.4h-33.2c-6.6 0-11.5 6.4-9.6 12.7 9.5 29.9 39.6 48.9 82.7 48.9V704c0 4.4 3.6 8 8 8h32c4.4 0 8-3.6 8-8v-32.2c50.8 0 83.2-24.8 83.2-63.3 0-37.3-29.6-49.2-85.5-58.2z" fill="currentColor"/>
                        </svg>
                    </el-icon>
                    <span>流水明细清理</span>
                </el-menu-item>
                <el-menu-item index="userClean">
                    <el-icon>
                        <svg viewBox="0 0 1024 1024" width="24" height="24">
                            <path d="M832 64H192c-35.3 0-64 28.7-64 64v768c0 35.3 28.7 64 64 64h640c35.3 0 64-28.7 64-64V128c0-35.3-28.7-64-64-64zM384 192h256c17.7 0 32 14.3 32 32s-14.3 32-32 32H384c-17.7 0-32-14.3-32-32s14.3-32 32-32zm384 640H256V256h512v576z" fill="currentColor"/>
                            <path d="M320 384h384v32H320zm0 128h384v32H320zm0 128h384v32H320z" fill="currentColor"/>
                        </svg>
                    </el-icon>
                    <span>用户数据清理</span>
                </el-menu-item>
            </el-menu>
        </div>

        <!-- 主要内容区 -->
        <div class="main-content">
            <!-- 登录日志清理 -->
            <el-card v-show="activeMenu === 'login'" shadow="never">
                <template #header>
                    <div class="card-header">
                        <h3>登录日志清理</h3>
                    </div>
                </template>

                <el-form :model="form" label-width="120px">
                    <el-form-item label="用户ID：">
                        <el-autocomplete
                            v-model="form.username"
                            :fetch-suggestions="queryUsernames"
                            placeholder="请输入用户ID（不输入则清理所有）"
                            style="width: 100%"
                            clearable
                            @select="handleUsernameSelect"
                            @clear="handleUsernameClear">
                            <template #default="{ item }">
                                <div>{{ item.label }}</div>
                            </template>
                        </el-autocomplete>
                    </el-form-item>

                    <el-form-item>
                        <el-button 
                            type="danger" 
                            @click="handleClearLogs" 
                            :loading="isLoading">
                            清理登录日志
                        </el-button>
                    </el-form-item>
                </el-form>
            </el-card>

            <!-- 投诉图片清理 -->
            <el-card v-show="activeMenu === 'complaint'" shadow="never">
                <template #header>
                    <div class="card-header">
                        <h3>投诉图片清理</h3>
                    </div>
                </template>

                <el-form :model="complaintForm" label-width="120px">
                    <el-form-item label="用户ID：">
                        <el-autocomplete
                            v-model="complaintForm.userId"
                            :fetch-suggestions="queryUsernames"
                            placeholder="请输入用户ID（不输入则查询所有）"
                            style="width: 100%"
                            clearable
                            @select="handleComplaintUserSelect"
                            @clear="handleComplaintUserClear">
                            <template #default="{ item }">
                                <div>{{ item.label }}</div>
                            </template>
                        </el-autocomplete>
                    </el-form-item>

                    <el-form-item label="日期范围：">
                        <el-date-picker
                            v-model="complaintForm.dateRange"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            value-format="YYYY-MM-DD"
                            :clearable="true"
                            @change="handleDateRangeChange"
                            style="width: 100%">
                        </el-date-picker>
                        <div class="el-form-item-description">
                            可选择日期范围，不选择则统计所有时间范围的投诉图片
                        </div>
                    </el-form-item>

                    <el-form-item>
                        <el-button 
                            type="primary"
                            @click="searchComplaintStats"
                            :loading="isSearching">
                            查询统计
                        </el-button>
                    </el-form-item>

                    <!-- 显示统计结果 -->
                    <div v-if="complaintStats && hasSearched" class="stats-info">
                        <el-alert
                            :title="complaintStats.msg"
                            type="info"
                            :closable="false">
                        </el-alert>
                    </div>

                    <el-divider>手动清理</el-divider>
                    <el-form-item>
                        <div class="button-group">
                            <el-button 
                                type="danger" 
                                @click="handleClearComplaintImages" 
                                :loading="isLoading">
                                清理所有投诉相关图片
                            </el-button>
                            <el-button 
                                type="danger" 
                                @click="handleClearUserComplaints" 
                                :loading="isLoading">
                                清理过期投诉记录和图片
                            </el-button>
                        </div>
                        <div class="el-form-item-description">
                            此操作将清理所有投诉相关的图片文件（包括投诉图片和买家收款码），请谨慎操作
                        </div>
                    </el-form-item>
                </el-form>
            </el-card>

            <!-- 商品清理 -->
            <el-card v-show="activeMenu === 'goods'" shadow="never">
                <template #header>
                    <div class="card-header">
                        <h3>商品清理</h3>
                    </div>
                </template>

                <el-form :model="goodsForm" label-width="120px">
                    <el-form-item label="用户ID：">
                        <el-autocomplete
                            v-model="goodsForm.userId"
                            :fetch-suggestions="queryUsernames"
                            placeholder="请输入用户ID（不输入则查询所有）"
                            style="width: 100%"
                            clearable
                            @select="handleGoodsUserSelect"
                            @clear="handleGoodsUserClear">
                            <template #default="{ item }">
                                <div>{{ item.label }}</div>
                            </template>
                        </el-autocomplete>
                    </el-form-item>

                    <el-form-item label="日期范围：">
                        <el-date-picker
                            v-model="goodsForm.dateRange"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            value-format="YYYY-MM-DD"
                            :clearable="true"
                            style="width: 100%">
                        </el-date-picker>
                        <div class="el-form-item-description">
                            可选择日期范围，不选择则统计所有时间范围的商品图片
                        </div>
                    </el-form-item>

                    <el-form-item>
                        <el-button 
                            type="primary" 
                            @click="searchGoodsStats" 
                            :loading="isSearching">
                            查询统计
                        </el-button>
                    </el-form-item>

                    <!-- 显示统计结果 -->
                    <div v-if="goodsStats" class="stats-info">
                        <el-alert
                            :title="goodsStats.msg"
                            type="info"
                            :closable="false">
                        </el-alert>
                    </div>

                    <el-divider>手动清理</el-divider>
                    <el-form-item>
                        <div class="button-group">
                            <el-button 
                                type="danger" 
                                @click="handleClearGoodsImages" 
                                :loading="isLoading">
                                清理商品图片
                            </el-button>
                            <el-button 
                                type="danger" 
                                @click="handleClearGoods" 
                                :loading="isLoading">
                                清理商品图片和数据
                            </el-button>
                            <el-button 
                                type="warning" 
                                @click="handleClearGoodsSellCount" 
                                :loading="isLoading">
                                重置商品销量
                            </el-button>
                        </div>
                        <div class="el-form-item-description">
                            此操作将清理商品记录、相关图片文件或重置销量数据，请谨慎操作
                        </div>
                    </el-form-item>
                </el-form>
            </el-card>

            <!-- 订单清理 -->
            <el-card v-show="activeMenu === 'order'" shadow="never">
                <template #header>
                    <div class="card-header">
                        <h3>订单清理</h3>
                    </div>
                </template>

                <el-form :model="orderForm" label-width="120px">
                    <el-form-item label="用户ID：">
                        <el-autocomplete
                            v-model="orderForm.userId"
                            :fetch-suggestions="queryUsernames"
                            placeholder="请输入用户ID（不输入则清理所有）"
                            style="width: 100%"
                            clearable
                            @select="handleOrderUserSelect"
                            @clear="handleOrderUserClear">
                            <template #default="{ item }">
                                <div>{{ item.label }}</div>
                            </template>
                        </el-autocomplete>
                    </el-form-item>

                    <!-- 添加订单状态选择 -->
                    <el-form-item label="订单状态：">
                        <el-checkbox-group v-model="orderForm.statusList">
                            <el-checkbox :label="0">未支付</el-checkbox>
                            <el-checkbox :label="1">已支付</el-checkbox>
                            <el-checkbox :label="2">已关闭</el-checkbox>
                            <el-checkbox :label="3">已退款</el-checkbox>
                        </el-checkbox-group>
                        <div class="el-form-item-description">
                            选择需要清理的订单状态，不选择则清理所有状态
                        </div>
                    </el-form-item>

                    <el-form-item label="日期范围：">
                        <el-date-picker
                            v-model="orderForm.dateRange"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            value-format="YYYY-MM-DD"
                            :disabled="orderForm.deleteAll"
                            @change="handleDateRangeChange">
                        </el-date-picker>
                        <div class="el-form-item-description">
                            选择的时间范围是闭区间，例如：选择2024-03-25将会包含整个25号（00:00:00至23:59:59）的订单。
                            <ul style="margin: 5px 0; padding-left: 20px;">
                                <li>已完成订单：根据成功时间判断</li>
                                <li>未完成订单：根据创建时间判断</li>
                                <li>冻结订单：不会被清理</li>
                                <li>当天订单：不会被清理</li>
                            </ul>
                        </div>
                    </el-form-item>

                    <el-form-item>
                        <div class="button-group">
                            <el-button 
                                type="danger" 
                                @click="handleClearOrders" 
                                :loading="isOrderCleaning">
                                清理订单
                            </el-button>
                            <el-button 
                                type="warning" 
                                @click="handleClearUserSellCount" 
                                :loading="isOrderCleaning">
                                重置用户订单数
                            </el-button>
                        </div>
                        <div class="el-form-item-description">
                            此操作将清理订单记录或重置用户销量数据，请谨慎操作
                        </div>
                    </el-form-item>
                </el-form>

                <el-divider>自动清理配置</el-divider>

                <el-form :model="orderForm" label-width="120px">
                    <el-form-item label="自动清理：">
                        <el-radio-group v-model="orderForm.status">
                            <el-radio :label="0">关闭</el-radio>
                            <el-radio :label="1">开启</el-radio>
                        </el-radio-group>
                        <div class="el-form-item-description">
                            开启后系统将按照以下配置自动清理订单，关闭后不会执行自动清理
                        </div>
                    </el-form-item>

                    <el-form-item label="自动清理状态：">
                        <el-checkbox-group v-model="orderForm.autoStatusList">
                            <el-checkbox :label="0">未支付</el-checkbox>
                            <el-checkbox :label="1">已支付</el-checkbox>
                            <el-checkbox :label="2">已关闭</el-checkbox>
                            <el-checkbox :label="3">已退款</el-checkbox>
                        </el-checkbox-group>
                        <div class="el-form-item-description">
                            设置清理时间阈值（单位：天）：
                            <ul style="margin: 5px 0; padding-left: 20px;">
                                <li>已完成订单：完成时间超过指定天数且不是当天的订单将被清理</li>
                                <li>未完成订单：创建时间超过指定天数且不是当天的订单将被清理</li>
                                <li>冻结订单：无论时间如何都不会被清理</li>
                            </ul>
                        </div>
                    </el-form-item>

                    <el-form-item label="清理时间：">
                        <el-input-number 
                            v-model="orderForm.days" 
                            :min="1" 
                            :max="365"
                            :step="1">
                        </el-input-number>
                        <div class="el-form-item-description">
                            设置清理时间阈值（单位：天）：
                            <ul style="margin: 5px 0; padding-left: 20px;">
                                <li>已完成订单：完成时间超过指定天数且不是当天的订单将被清理</li>
                                <li>未完成订单：创建时间超过指定天数且不是当天的订单将被清理</li>
                                <li>冻结订单：无论时间如何都不会被清理</li>
                            </ul>
                        </div>
                    </el-form-item>

                    <el-form-item label="执行时间：">
                        <el-time-picker
                            v-model="orderForm.executeTime"
                            format="HH:mm"
                            value-format="HH:mm"
                            :disabled="!orderForm.status"
                            placeholder="选择时间">
                        </el-time-picker>
                        <div class="el-form-item-description">
                            设置每天执行清理的具体时间点（24小时制，例如：00:00）。系统将在到达该时间点时检查并执行清理任务
                        </div>
                    </el-form-item>

                    <el-form-item label="执行间隔：">
                        <el-input-number 
                            v-model="orderForm.interval" 
                            :min="0" 
                            :max="30"
                            :step="1">
                        </el-input-number>
                        <div class="el-form-item-description">
                            设置清理任务的执行间隔（单位：天）：
                            - 设置为0：每天在指定时间执行一次
                            - 设置为1-30：每隔指定天数在指定时间执行一次
                            注意：即使到达执行时间，系统也会检查是否满足间隔天数才执行清理
                        </div>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="saveOrder" :loading="isConfigSaving">
                            保存配置
                        </el-button>
                    </el-form-item>
                </el-form>
            </el-card>

            <!-- 商户通知清理 -->
            <el-card v-show="activeMenu === 'message'" shadow="never">
                <template #header>
                    <div class="card-header">
                        <h3>商户通知清理</h3>
                    </div>
                </template>

                <el-divider>手动清理</el-divider>
                <el-form :model="messageForm" label-width="120px">
                    <el-form-item label="用户ID：">
                        <el-autocomplete
                            v-model="messageForm.userId"
                            :fetch-suggestions="queryUsernames"
                            placeholder="请输入用户ID（不输入则清理所有）"
                            style="width: 100%"
                            clearable
                            @select="handleMessageUserSelect"
                            @clear="handleMessageUserClear">
                            <template #default="{ item }">
                                <div>{{ item.label }}</div>
                            </template>
                        </el-autocomplete>
                    </el-form-item>

                    <el-form-item label="日期范围：">
                        <el-date-picker
                            v-model="messageForm.dateRange"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            value-format="YYYY-MM-DD"
                            :clearable="true"
                            :editable="false"
                            style="width: 100%">
                        </el-date-picker>
                        <div class="el-form-item-description">
                            可选择日期范围，不选择则清理所有时间范围的已读通知
                        </div>
                    </el-form-item>

                    <el-form-item>
                        <el-button 
                            type="danger" 
                            @click="handleClearMessages" 
                            :loading="isLoading">
                            清理已读通知
                        </el-button>
                        <div class="el-form-item-description">
                            此操作将清理已读的商户通知消息，请谨慎操作
                        </div>
                    </el-form-item>
                </el-form>

                <el-divider>自动清理配置</el-divider>
                <el-form :model="messageForm" label-width="120px">
                    <el-form-item label="自动清理：">
                        <el-radio-group v-model="messageForm.status">
                            <el-radio :label="0">关闭</el-radio>
                            <el-radio :label="1">开启</el-radio>
                        </el-radio-group>
                        <div class="el-form-item-description">
                            开启后系统将自动清理已读的商户通知消息
                        </div>
                    </el-form-item>

                    <el-form-item label="清理时间：">
                        <el-input-number 
                            v-model="messageForm.days" 
                            :min="1" 
                            :max="365"
                            :step="1"
                            style="width: 180px"
                        />
                        <span class="el-form-item-append">天</span>
                        <div class="el-form-item-description">
                            设置清理时间阈值（单位：天），已读消息在创建时间超过指定天数后将被自动清理
                        </div>
                    </el-form-item>

                    <el-form-item label="执行时间：">
                        <el-time-select
                            v-model="messageForm.executeTime"
                            start="00:00"
                            step="00:01"
                            end="23:30"
                            placeholder="选择时间"
                            style="width: 180px"
                        />
                        <div class="el-form-item-description">
                            设置每天执行自动清理的时间点
                        </div>
                    </el-form-item>

                    <el-form-item label="执行间隔：">
                        <el-input-number 
                            v-model="messageForm.interval" 
                            :min="0" 
                            :max="30"
                            :step="1"
                            style="width: 180px"
                        />
                        <span class="el-form-item-append">天</span>
                        <div class="el-form-item-description">
                            设置两次自动清理之间的最小间隔天数，0表示每天都执行
                        </div>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="saveMessageConfig">
                            保存配置
                        </el-button>
                    </el-form-item>
                </el-form>
            </el-card>

            <!-- 添加数据库备份卡片 -->
            <el-card v-show="activeMenu === 'backup'" shadow="never">
                <template #header>
                    <div class="card-header">
                        <h3>数据库备份</h3>
                    </div>
                </template>
                
                <div class="backup-container">
                    <!-- 备份操作区域 -->
                    <div class="backup-actions">
                        <el-button 
                            type="primary" 
                            :loading="isBackingUp"
                            @click="() => handleBackupDatabase('order')">
                            订单表备份
                        </el-button>
                        <el-button 
                            type="primary" 
                            :loading="isBackingUp"
                            @click="() => handleBackupDatabase('all')">
                            完整备份
                        </el-button>
                        <el-popover
                            placement="bottom"
                            :width="300"
                            trigger="hover">
                            <template #default>
                                <div>
                                    <p>支持导入 .sql 格式的订单备份文件，导入时会智能处理：</p>
                                    <ul>
                                        <li>自动跳过重复数据</li>
                                        <li>自动转换无效的数值字段</li>
                                        <li>自动处理NULL值为默认值</li>
                                        <li>可导入从本系统备份的订单数据</li>
                                    </ul>
                                </div>
                            </template>
                            <template #reference>
                                <el-upload
                                    class="upload-button"
                                    :action="'/plugin/Clearallinone/api/importOrders'"
                                    :show-file-list="false"
                                    :before-upload="beforeOrderImport"
                                    :on-success="handleImportSuccess"
                                    :on-error="handleImportError"
                                    accept=".sql">
                                    <el-button type="success" :loading="isImporting">
                                        <svg class="icon" viewBox="0 0 1024 1024" width="16" height="16">
                                            <path d="M544 672V448H704L512 256 320 448h160v224h64z m-64 96v64H192V384h192l64-64H192c-35.3 0-64 28.7-64 64v448c0 35.3 28.7 64 64 64h384c35.3 0 64-28.7 64-64v-64h-64z" fill="currentColor"></path>
                                        </svg>
                                        导入订单
                                    </el-button>
                                </el-upload>
                            </template>
                        </el-popover>
                    </div>

                    <!-- 备份文件列表 -->
                    <el-table :data="backupFiles" style="width: 100%">
                        <el-table-column label="备份类型" width="120">
                            <template #default="scope">
                                <el-tag 
                                    :type="scope.row.type === 'order' ? 'success' : 'primary'"
                                    size="small">
                                    {{ scope.row.type === 'order' ? '订单备份' : '完整备份' }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="备份信息" min-width="400">
                            <template #default="scope">
                                <div class="backup-info">
                                    <span class="filename">{{ scope.row.filename }}</span>
                                    <span class="file-meta">
                                        <span class="file-size">({{ scope.row.size }})</span>
                                        <span class="file-time">{{ scope.row.create_time }}</span>
                                    </span>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column width="150" align="right">
                            <template #default="scope">
                                <el-button 
                                    type="primary" 
                                    link 
                                    @click="downloadBackup(scope.row)">
                                    下载
                                </el-button>
                                <el-button 
                                    v-if="scope.row.filename.startsWith('order_backup_')"
                                    type="success" 
                                    link 
                                    @click="importFromBackup(scope.row)"
                                    :loading="scope.row.importing">
                                    导入
                                </el-button>
                                <el-button 
                                    type="danger" 
                                    link 
                                    @click="deleteBackup(scope.row)">
                                    删除
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </el-card>

            <!-- 卡密清理 -->
            <el-card v-show="activeMenu === 'card'" shadow="never">
                <template #header>
                    <div class="card-header">
                        <h3>卡密清理</h3>
                    </div>
                </template>

                <el-form :model="cardForm" label-width="120px">
                    <el-form-item label="用户ID：">
                        <el-autocomplete
                            v-model="cardForm.userId"
                            :fetch-suggestions="queryUsernames"
                            placeholder="请输入用户ID（不输入则清理所有）"
                            style="width: 100%"
                            clearable
                            @select="handleCardUserSelect"
                            @clear="handleCardUserClear">
                            <template #default="{ item }">
                                <div>{{ item.label }}</div>
                            </template>
                        </el-autocomplete>
                    </el-form-item>

                    <el-form-item label="日期范围：">
                        <el-date-picker
                            v-model="cardForm.dateRange"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            value-format="YYYY-MM-DD"
                            :clearable="true"
                            style="width: 100%">
                        </el-date-picker>
                    </el-form-item>

                    <el-form-item>
                        <div class="button-group">
                            <el-button 
                                type="danger" 
                                @click="handleClearCards" 
                                :loading="isCardCleaning">
                                清理所有卡密
                            </el-button>
                            <el-button 
                                type="danger" 
                                @click="handleClearDeletedCards" 
                                :loading="isCardCleaning">
                                清理回收站卡密
                            </el-button>
                        </div>
                        <div class="el-form-item-description">
                            <ul>
                                <li>清理所有卡密：将删除指定条件下的所有卡密记录</li>
                                <li>清理回收站卡密：将删除指定条件下的所有回收站卡密记录</li>
                            </ul>
                        </div>
                    </el-form-item>
                </el-form>

                <!-- 自动清理配置部分 -->
                <el-divider content-position="left">自动清理配置</el-divider>
                <el-form :model="cardConfig" label-width="120px">
                    <el-form-item label="自动清理：">
                        <el-switch v-model="cardConfig.status" />

                    </el-form-item>
                                            <p class="el-form-item-description" style="display: block !important;">
                            启用后，系统将按照设定的时间自动清理回收站中的所有卡密记录
                        </p>

                    <el-form-item label="执行时间：">
                        <el-time-picker
                            v-model="cardConfig.executeTime"
                            format="HH:mm"
                            value-format="HH:mm"
                            placeholder="选择时间"
                            :disabled="!cardConfig.status"
                            style="width: 120px;" />
                    </el-form-item>
                       <p class="el-form-item-description" style="display: block !important;">
                            每天自动执行清理的时间点，建议选择系统负载较低的时间
                        </p>

                    <el-form-item label="执行间隔：">
                        <el-input-number 
                            v-model="cardConfig.interval" 
                            :min="0" 
                            :max="30"
                            :disabled="!cardConfig.status" />

                    </el-form-item>
                        <p class="el-form-item-description" style="display: block !important;">
                            两次自动清理之间的最小间隔天数（0表示每天执行）
                        </p>

                    <el-form-item>
                        <el-button 
                            type="primary" 
                            @click="saveCardConfig" 
                            :loading="isConfigSaving">
                            保存配置
                        </el-button>
                    </el-form-item>
                </el-form>
            </el-card>

            <!-- 流水明细清理 -->
            <el-card v-show="activeMenu === 'moneyLog'" shadow="never">
                <template #header>
                    <div class="card-header">
                        <h3>流水明细清理</h3>
                    </div>
                </template>

                <el-form :model="moneyLogForm" label-width="120px">
                    <el-form-item label="用户ID：">
                        <el-autocomplete
                            v-model="moneyLogForm.userId"
                            :fetch-suggestions="queryUsernames"
                            placeholder="请输入用户ID（不输入则清理所有）"
                            style="width: 100%"
                            clearable
                            @select="handleMoneyLogUserSelect"
                            @clear="handleMoneyLogUserClear">
                            <template #default="{ item }">
                                <div>{{ item.label }}</div>
                            </template>
                        </el-autocomplete>
                    </el-form-item>

                    <el-form-item label="流水类型：">
                        <el-checkbox-group v-model="moneyLogForm.sourceTypes">
                            <el-checkbox label="Platform">平台账户流水明细(包结算记录)</el-checkbox>
                            <el-checkbox label="Deposit"> 保证金账户</el-checkbox>
                            <el-checkbox label="DepositLimit">最低保证金流水记录</el-checkbox>
                            <el-checkbox label="Operate">运营钱包流水明细</el-checkbox>
                        </el-checkbox-group>
                        <div class="el-form-item-description">
                            选择需要清理的流水类型，不选择则清理所有类型
                        </div>
                    </el-form-item>

                    <el-form-item label="日期范围：">
                        <el-date-picker
                            v-model="moneyLogForm.dateRange"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            value-format="YYYY-MM-DD"
                            :clearable="true"
                            style="width: 100%">
                        </el-date-picker>
                        <div class="el-form-item-description">
                            选择需要清理的日期范围，不选择则清理所有时间范围
                        </div>
                    </el-form-item>

                    <el-form-item>
                        <el-button 
                            type="danger" 
                            @click="handleClearMoneyLogs" 
                            :loading="isMoneyLogCleaning">
                            清理流水明细
                        </el-button>
                    </el-form-item>
                </el-form>
            </el-card>

            <!-- 用户数据清理 -->
            <el-card v-show="activeMenu === 'userClean'" shadow="never">
                <template #header>
                    <div class="card-header">
                        <h3>用户数据清理</h3>
                    </div>
                </template>

                <el-form :model="userCleanForm" label-width="120px">
                    <el-form-item label="用户ID：">
                        <el-autocomplete
                            v-model="userCleanForm.userId"
                            :fetch-suggestions="queryUsernames"
                            placeholder="请输入用户ID或用户名进行搜索"
                            style="width: 100%"
                            clearable
                            @select="handleUserCleanSelect"
                            @clear="handleUserCleanClear"
                            @input="handleUserIdInput">
                            <template #default="{ item }">
                                <div>{{ item.label }}</div>
                            </template>
                        </el-autocomplete>
                    </el-form-item>

                    <el-form-item>
                        <div class="button-group">
                            <el-button 
                                type="primary" 
                                @click="handleQueryUserData" 
                                :loading="isQuerying">
                                查询用户数据
                            </el-button>
                            <el-button 
                                type="danger" 
                                @click="handleUserClean" 
                                :loading="isUserCleaning"
                                :disabled="!userCleanForm.user_id">
                                清理用户数据
                            </el-button>
                            <el-button 
                                type="warning" 
                                @click="handleCleanAllDeletedUsers" 
                                :loading="isCleaningAllDeleted">
                                清理所有已删除用户
                            </el-button>
                        </div>
                    </el-form-item>
                </el-form>

                <!-- 显示查询结果 -->
                <div v-if="userDataResult && userDataResult.user">
                    <el-divider content-position="left">用户信息</el-divider>
                    <el-descriptions :column="2" border>
                        <el-descriptions-item label="用户ID">{{ userDataResult.user.id }}</el-descriptions-item>
                        <el-descriptions-item label="用户名">{{ userDataResult.user.username }}</el-descriptions-item>
                        <el-descriptions-item label="注册时间">{{ userDataResult.user.regdate }}</el-descriptions-item>
                        <el-descriptions-item label="状态">
                            <el-tag v-if="userDataResult.user_exists !== false" type="success">
                                用户存在
                            </el-tag>
                            <el-tag v-else type="warning">用户已删除</el-tag>
                        </el-descriptions-item>
                        <el-descriptions-item v-if="userDataResult.user.note" :span="2" label="备注">
                            <el-tag type="warning">{{ userDataResult.user.note }}</el-tag>
                        </el-descriptions-item>
                    </el-descriptions>

                    <el-divider content-position="left">关联数据</el-divider>
                    <div class="data-header" style="margin-bottom: 15px; display: flex; justify-content: space-between; align-items: center;">
                        <div v-if="userDataResult.user_exists === false" class="warning-message" style="margin-bottom: 10px; display: flex; align-items: center;">
                            <el-alert
                                title="用户已从user表中删除，但在其他表中还存在关联数据"
                                type="warning"
                                :closable="false"
                                style="margin-right: 10px; flex: 1;">
                            </el-alert>
                            <el-button 
                                type="danger" 
                                @click="handleCleanDeletedUserData" 
                                :loading="isUserCleaning">
                                清理已删除用户关联数据
                            </el-button>
                        </div>
                    </div>
                    <el-table :data="userDataTableList" stripe style="width: 100%">
                        <el-table-column prop="table_name" label="数据表" width="180">
                            <template #default="scope">
                                <el-tooltip :content="scope.row.table_name" placement="top">
                                    <div>
                                        <span>{{ scope.row.table_name }}</span>
                                        <el-tag 
                                            size="small" 
                                            type="info" 
                                            style="margin-left: 5px;">
                                            {{ scope.row.display_name }}
                                        </el-tag>
                                    </div>
                                </el-tooltip>
                            </template>
                        </el-table-column>
                        <el-table-column prop="count" label="记录数量" width="120">
                            <template #default="scope">
                                <el-tag>{{ scope.row.count }}</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="120">
                            <template #default="scope">
                                <el-button 
                                    type="danger" 
                                    size="small" 
                                    @click="handleCleanSingleTable(scope.row)">
                                    清理
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </el-card>

            <!-- 显示清理进度 -->
            <div v-if="showCleaningProgress" class="progress-container" style="margin-top: 20px;">
                <el-card shadow="never">
                    <template #header>
                        <div class="card-header">
                            <h3>正在清理已删除用户数据</h3>
                        </div>
                    </template>
                    <div class="progress-status" style="margin-bottom: 15px;">
                        <div v-if="cleaningStats.currentUserId">
                            <p>正在处理: 用户ID {{ cleaningStats.currentUserId }}</p>
                            <p>进度: {{ cleaningStats.processedCount }} / {{ cleaningStats.totalCount }}</p>
                            <p>已删除记录: {{ cleaningStats.deletedRecords }} 条</p>
                        </div>
                    </div>
                    <el-progress :percentage="cleaningProgress" :status="isCleaningAllDeleted ? '' : 'success'" :stroke-width="20"></el-progress>
                </el-card>
            </div>
        </div>
    </div>
</div>

<script src="/static/others/vue/vue.global.prod.js"></script>
<script src="/static/others/element-plus/index.full.min.js"></script>
<script src="/static/others/element-plus/locale/zh-cn.min.js"></script>
<script src="/static/others/axios/axios.min.js"></script>

<script>
    const { createApp, ref, reactive, onMounted } = Vue;
    const { ElMessage, ElMessageBox } = ElementPlus;

    const app = createApp({
        setup() {
            const isLoading = ref(false);
            const activeMenu = ref('login');
            const form = reactive({
                username: '',
                user_id: null,
                userInfo: ''
            });

            const complaintForm = reactive({
                userId: '',
                user_id: null,
                userInfo: '',
                dateRange: null
            });

            const hasSearched = ref(false);
            const complaintStats = ref(null);
            const isSearching = ref(false);

            const goodsForm = reactive({
                userId: '',
                user_id: null,
                dateRange: null
            });

            const orderForm = reactive({
                status: 0,
                days: 30,
                executeTime: '00:00',
                interval: 1,
                statusList: [],
                autoStatusList: [1],
                user_id: null
            });

            const isOrderCleaning = ref(false);
            const isConfigSaving = ref(false);

            // 添加商户通知表单数据
            const messageForm = reactive({
                userId: '',
                user_id: null,
                dateRange: null,
                status: 0,
                days: 30,
                executeTime: '00:00',
                interval: 1
            });

            const goodsStats = ref(null);

            const isBackingUp = ref(false);
            const backupFiles = ref([]);

            // 添加导入相关的响应式变量
            const isImporting = ref(false);

            // 在 setup() 中添加
            const cardForm = reactive({
                userId: '',
                user_id: null,
                dateRange: null
            });

            const isCardCleaning = ref(false);

            const cardConfig = reactive({
                status: false,
                executeTime: '00:00',
                interval: 0  // 默认值改为0
            });

            // 在 setup() 中添加
            const moneyLogForm = reactive({
                userId: '',
                user_id: null,
                sourceTypes: [],
                dateRange: null
            });

            const isMoneyLogCleaning = ref(false);

            // 添加用户数据清理相关的变量
            const userCleanForm = reactive({
                userId: '',
                user_id: null,
                cleanTypes: [], // 清理类型
                dateRange: null,
                days: 30,  // 添加默认值
                executeTime: '00:00',  // 添加默认值
                interval: 1  // 添加默认值
            });

            const userDataResult = ref(null);
            const userDataTableList = ref([]);
            const userDataTables = ref([]);
            const isQuerying = ref(false);
            const isUserCleaning = ref(false);
            const isCleaningAllDeleted = ref(false);
            const cleaningProgress = ref(0);
            const showCleaningProgress = ref(false);
            const cleaningStats = ref({
                processedCount: 0,
                totalCount: 0,
                currentUserId: null,
                deletedRecords: 0
            });
            
            // 添加数据表名称的中文翻译映射
            const tableNameMap = {
                'user': '用户表',
                'user_login_log': '登录日志',
                'user_money_log': '资金流水',
                'user_message': '用户消息',
                'order': '订单表',
                'goods': '商品表',
                'goods_card_storage_0': '卡密存储0',
                'goods_card_storage_1': '卡密存储1',
                'goods_card_storage_2': '卡密存储2',
                'complaint': '投诉表',
                'auto_unfreeze': '自动解冻',
                'user_withdrawal': '提现记录',
                'user_recharge': '充值记录',
                'user_report': '举报记录',
                'user_authentication': '用户认证',
                'user_favorite': '用户收藏',
                'user_balance_log': '余额日志',
                'user_deposit_log': '保证金日志',
                'user_operate_log': '运营钱包日志',
                'goods_category': '商品分类',
                'goods_stock_log': '商品库存日志'
            };

            // 处理菜单选择
            const handleMenuSelect = (index) => {
                activeMenu.value = index;
                // 如果切换到用户数据清理页面，初始化数据
                if (index === 'userClean') {
                    queryUserDataTables();
                }
            };

            // 查询用户名建议
            const queryUsernames = async (queryString, cb) => {
                if (!queryString) {
                    cb([]);
                    return;
                }

                try {
                    const res = await axios.post("/plugin/Clearallinone/api/searchUsers", {
                        keyword: queryString
                    });
                    
                    if (res.data?.code === 200) {
                        // 修改显示格式
                        const suggestions = res.data.data.map(item => ({
                            value: item.value,
                            label: `ID: ${item.value} (${item.label.split('(')[1].split(')')[0]})`  // 转换格式
                        }));
                        cb(suggestions);
                    } else {
                        cb([]);
                    }
                } catch (error) {
                    console.error('搜索用户失败:', error);
                    cb([]);
                }
            };

            // 处理用户选择
            const handleUsernameSelect = (item) => {
                form.user_id = item.value;
            };

            // 处理清理日志
            const handleClearLogs = async () => {
                try {
                    const confirmMessage = form.user_id 
                        ? `确定要清理用户ID ${form.user_id} 的所有登录日志吗？`
                        : '确定要清理所有用户的登录日志吗？此操作不可恢复！';

                    await ElMessageBox.confirm(
                        confirmMessage,
                        '警告',
                        {
                            confirmButtonText: '确定清理',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    );

                    isLoading.value = true;
                    const res = await axios.post("/plugin/Clearallinone/api/clearLoginLogs", {
                        user_id: form.user_id || 0  // 确保发送 0 表示清理所有
                    });

                    if (res.data?.code === 200) {
                        ElMessage.success(res.data.msg);
                        form.username = '';
                        form.user_id = null;
                    } else {
                        ElMessage.error(res.data?.msg || '清理失败');
                    }
                } catch (error) {
                    if (error !== 'cancel') {
                        ElMessage.error(error.response?.data?.msg || '操作失败');
                    }
                } finally {
                    isLoading.value = false;
                }
            };

            // 修改清理投诉图片方法
            const handleClearComplaintImages = async () => {
                try {
                    // 添加用户ID验证
                    if (complaintForm.userId && !complaintForm.user_id) {
                        ElMessage.warning('请选择有效的用户ID');
                        return;
                    }

                    const confirmMessage = complaintForm.user_id 
                        ? `确定要清理用户ID ${complaintForm.user_id} 的投诉相关图片吗？`
                        : '确定要清理所有用户的投诉相关图片吗？此操作不可恢复！';

                    await ElMessageBox.confirm(
                        confirmMessage,
                        '警告',
                        {
                            confirmButtonText: '确定清理',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    );

                    isLoading.value = true;
                    const params = {
                        user_id: complaintForm.user_id || 0,
                        start_date: complaintForm.dateRange ? complaintForm.dateRange[0] : '',
                        end_date: complaintForm.dateRange ? complaintForm.dateRange[1] : ''
                    };
                    
                    const res = await axios.post("/plugin/Clearallinone/api/clearComplaintImages", params);
                    
                    if (res.data?.code === 200) {
                        ElMessage.success(res.data.msg);
                        // 清理成功后重置表单
                        complaintForm.userId = '';
                        complaintForm.user_id = null;
                        complaintForm.dateRange = null;
                        complaintStats.value = null;
                        hasSearched.value = false;
                    } else {
                        ElMessage.error(res.data?.msg || '清理失败');
                    }
                } catch (error) {
                    if (error !== 'cancel') {
                        ElMessage.error(error.response?.data?.msg || '操作失败');
                    }
                } finally {
                    isLoading.value = false;
                }
            };

            // 修改用户选择处理函数
            const handleComplaintUserSelect = (item) => {
                complaintForm.userId = item.value;
                complaintForm.user_id = item.value;  // 确保 user_id 也被更新
                // 移除自动查询
                // searchComplaintStats();
            };

            // 修改用户清除处理函数
            const handleComplaintUserClear = () => {
                complaintForm.userId = '';
                complaintForm.user_id = null;  // 确保清除时也清除 user_id
                complaintStats.value = null;
                hasSearched.value = false;
            };

            // 处理日期范围变化
            const handleDateRangeChange = async () => {
                // 移除自动查询
                // if (complaintForm.user_id) {
                //     await updateComplaintStats();
                // }
            };

            // 添加更新统计信息的函数
            const updateComplaintStats = async () => {
                try {
                    const res = await axios.post("/plugin/Clearallinone/api/getComplaintImageStats", {
                        user_id: complaintForm.user_id,
                        start_date: complaintForm.dateRange ? complaintForm.dateRange[0] : '',
                        end_date: complaintForm.dateRange ? complaintForm.dateRange[1] : ''
                    });
                    if (res.data?.code === 200) {
                        complaintStats.value = res.data;
                    }
                } catch (error) {
                    ElMessage.error('获取用户图片统计失败');
                }
            };

            // 修改清理用户投诉记录方法
            const handleClearUserComplaints = async () => {
                try {
                    // 添加用户ID验证
                    if (complaintForm.userId && !complaintForm.user_id) {
                        ElMessage.warning('请选择有效的用户ID');
                        return;
                    }

                    const confirmMessage = complaintForm.user_id 
                        ? `确定要清理用户ID ${complaintForm.user_id} 的过期投诉记录和相关图片吗？`
                        : '确定要清理所有用户的过期投诉记录和相关图片吗？此操作不可恢复！';

                    await ElMessageBox.confirm(
                        confirmMessage,
                        '警告',
                        {
                            confirmButtonText: '确定清理',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    );

                    isLoading.value = true;
                    const params = {
                        user_id: complaintForm.user_id || 0,
                        start_date: complaintForm.dateRange ? complaintForm.dateRange[0] : '',
                        end_date: complaintForm.dateRange ? complaintForm.dateRange[1] : ''
                    };
                    
                    const res = await axios.post("/plugin/Clearallinone/api/clearUserComplaintsAndImages", params);
                    
                    if (res.data?.code === 200) {
                        ElMessage.success(res.data.msg);
                        // 清理成功后重置表单
                        complaintForm.userId = '';
                        complaintForm.user_id = null;
                        complaintForm.dateRange = null;
                        complaintStats.value = null;
                        hasSearched.value = false;
                    } else {
                        ElMessage.error(res.data?.msg || '清理失败');
                    }
                } catch (error) {
                    if (error !== 'cancel') {
                        ElMessage.error(error.response?.data?.msg || '操作失败');
                    }
                } finally {
                    isLoading.value = false;
                }
            };

            // 处理清除
            const handleUsernameClear = () => {
                form.username = '';
                form.user_id = null;
            };

            // 修改搜索方法
            const searchComplaintStats = async () => {
                try {
                    isSearching.value = true;
                    hasSearched.value = true;  // 设置已搜索状态

                    // 移除必须输入用户ID的验证
                    let userId = 0;  // 默认为0，表示查询所有用户
                    
                    // 如果输入了用户ID，则验证其有效性
                    if (complaintForm.userId) {
                        userId = parseInt(complaintForm.userId);
                        if (isNaN(userId)) {
                            ElMessage.warning('请输入有效的数字ID');
                            hasSearched.value = false;
                            isSearching.value = false;
                            return;
                        }
                    }

                    const res = await axios.post("/plugin/Clearallinone/api/getComplaintImageStats", {
                        user_id: userId,  // 使用转换后的数字ID，0表示所有用户
                        start_date: complaintForm.dateRange ? complaintForm.dateRange[0] : '',
                        end_date: complaintForm.dateRange ? complaintForm.dateRange[1] : ''
                    });
                    
                    if (res.data?.code === 200) {
                        complaintStats.value = res.data;
                    } else {
                        ElMessage.warning(res.data?.msg || '获取统计信息失败');
                        complaintStats.value = null;
                        hasSearched.value = false;
                    }
                } catch (error) {
                    console.error('搜索失败:', error);
                    ElMessage.error('搜索失败');
                    complaintStats.value = null;
                    hasSearched.value = false;
                } finally {
                    isSearching.value = false;
                }
            };

            // 修改商品相关的处理函数
            const handleGoodsUserSelect = (item) => {
                goodsForm.userId = item.value;
                goodsForm.user_id = item.value;
            };

            const handleGoodsUserClear = () => {
                goodsForm.userId = '';
                goodsForm.user_id = null;
                goodsForm.dateRange = null;
            };

            // 处理清理商品
            const handleClearGoods = async () => {
                try {
                    const confirmMessage = goodsForm.user_id 
                        ? `确定要清理用户ID ${goodsForm.user_id} 的所有商品记录吗？`
                        : '确定要清理所有用户的商品记录吗？此操作不可恢复！';

                    await ElMessageBox.confirm(
                        confirmMessage,
                        '警告',
                        {
                            confirmButtonText: '确定清理',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    );

                    isLoading.value = true;
                    const res = await axios.post("/plugin/Clearallinone/api/clearGoods", {
                        user_id: goodsForm.user_id || 0
                    });

                    if (res.data?.code === 200) {
                        ElMessage.success(res.data.msg);
                        goodsForm.userId = '';
                        goodsForm.user_id = null;
                    } else {
                        ElMessage.error(res.data?.msg || '清理失败');
                    }
                } catch (error) {
                    if (error !== 'cancel') {
                        console.error('清理商品失败:', error);
                        ElMessage.error('清理失败: ' + (error.message || '未知错误'));
                    }
                } finally {
                    isLoading.value = false;
                }
            };

            // 处理清理商品图片
            const handleClearGoodsImages = async () => {
                try {
                    const confirmMessage = goodsForm.user_id 
                        ? `确定要清理用户ID ${goodsForm.user_id} 的所有商品图片吗？`
                        : '确定要清理所有用户的商品图片吗？此操作不可恢复！';

                    await ElMessageBox.confirm(
                        confirmMessage,
                        '警告',
                        {
                            confirmButtonText: '确定清理',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    );

                    isLoading.value = true;
                    const res = await axios.post("/plugin/Clearallinone/api/clearGoodsImages", {
                        user_id: goodsForm.user_id || 0
                    });

                    if (res.data?.code === 200) {
                        ElMessage.success(res.data.msg);
                        goodsForm.userId = '';
                        goodsForm.user_id = null;
                    } else {
                        ElMessage.error(res.data?.msg || '清理失败');
                    }
                } catch (error) {
                    if (error !== 'cancel') {
                        console.error('清理商品图片失败:', error);
                        ElMessage.error('清理失败: ' + (error.message || '未知错误'));
                    }
                } finally {
                    isLoading.value = false;
                }
            };

            // 处理重置商品销量
            const handleClearGoodsSellCount = async () => {
                try {
                    const confirmMessage = goodsForm.user_id 
                        ? `确定要重置用户ID ${goodsForm.user_id} 的所有商品销量吗？`
                        : '确定要重置所有用户的商品销量吗？此操作不可恢复！';

                    await ElMessageBox.confirm(
                        confirmMessage,
                        '警告',
                        {
                            confirmButtonText: '确定重置',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    );

                    isLoading.value = true;
                    const res = await axios.post("/plugin/Clearallinone/api/clearGoodsSellCount", {
                        user_id: goodsForm.user_id || 0
                    });

                    if (res.data?.code === 200) {
                        ElMessage.success(res.data.msg);
                        // 可以选择是否重置表单
                        // goodsForm.userId = '';
                        // goodsForm.user_id = null;
                    } else {
                        ElMessage.error(res.data?.msg || '重置失败');
                    }
                } catch (error) {
                    if (error !== 'cancel') {
                        console.error('重置商品销量失败:', error);
                        ElMessage.error('重置失败: ' + (error.message || '未知错误'));
                    }
                } finally {
                    isLoading.value = false;
                }
            };

            // 获取配置数据
            const fetchData = async () => {
                try {
                    const res = await axios.post("/plugin/Clearallinone/api/fetchData");
                    if (res.data?.code === 1) {
                        const data = res.data.data || {};
                        
                        // 添加安全检查，设置默认值防止undefined错误
                        orderForm.status = data.order_status !== undefined ? data.order_status : 0;
                        orderForm.days = data.order_days !== undefined ? data.order_days : 30;
                        orderForm.executeTime = data.order_execute_time || '00:00';
                        orderForm.interval = data.order_interval !== undefined ? data.order_interval : 1;
                        orderForm.autoStatusList = data.order_status_list 
                            ? data.order_status_list.split(',').map(Number)
                            : [1];
                            
                        // 添加对messageForm的安全检查
                        messageForm.status = data.message_status !== undefined ? data.message_status : 0;
                        messageForm.days = data.message_days !== undefined ? data.message_days : 30;
                        messageForm.executeTime = data.message_execute_time || '00:00';
                        messageForm.interval = data.message_interval !== undefined ? data.message_interval : 1;
                        
                        // 添加对cardConfig的安全检查
                        cardConfig.status = Boolean(data.card_status);
                        cardConfig.executeTime = data.card_execute_time || '00:00';
                        cardConfig.interval = parseInt(data.card_interval || '0');
                    }
                } catch (error) {
                    console.error('获取配置失败:', error);
                }
            };

            // 保存订单配置
            const saveOrder = async () => {
                try {
                    isConfigSaving.value = true;
                    const params = {
                        status: orderForm.status,
                        days: orderForm.days,
                        executeTime: orderForm.executeTime,
                        interval: orderForm.interval,
                        autoStatusList: orderForm.autoStatusList.join(',')
                    };
                    const res = await axios.post("/plugin/Clearallinone/api/saveOrder", params);
                    
                    if (res.data?.code === 1) {
                        ElMessage.success(res.data.msg);
                    } else {
                        throw new Error(res.data?.msg || '保存失败');
                    }
                } catch (error) {
                    ElMessage.error(error.message);
                } finally {
                    isConfigSaving.value = false;
                }
            };

            // 商户通知相关方法
            const handleMessageUserSelect = (item) => {
                messageForm.userId = item.value;
                messageForm.user_id = item.value;
            };

            const handleMessageUserClear = () => {
                messageForm.userId = '';
                messageForm.user_id = null;
            };

            const handleClearMessages = async () => {
                try {
                    // 构建确认消息
                    let confirmMessage = messageForm.userId 
                        ? `确定要清理用户ID ${messageForm.userId} 的已读通知吗？`
                        : '确定要清理所有已读通知吗？';
                    
                    // 添加日期范围信息到确认消息
                    if (messageForm.dateRange && messageForm.dateRange.length === 2) {
                        confirmMessage += `\n时间范围：${messageForm.dateRange[0]} 至 ${messageForm.dateRange[1]}`;
                    }

                    await ElMessageBox.confirm(
                        confirmMessage,
                        '警告',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    );

                    isLoading.value = true;

                    // 准备请求参数
                    const params = {
                        user_id: messageForm.userId || 0
                    };

                    // 只有当选择了日期范围时才添加日期参数
                    if (messageForm.dateRange && messageForm.dateRange.length === 2) {
                        params.start_date = messageForm.dateRange[0];
                        params.end_date = messageForm.dateRange[1];
                    }

                    console.log('清理参数:', params); // 调试日志

                    const res = await axios.post("/plugin/Clearallinone/api/clearUserMessages", params);

                    if (res.data?.code === 200) {
                        ElMessage.success(res.data.msg);
                        // 清理成功后重置表单
                        messageForm.userId = '';
                        messageForm.user_id = null;
                        messageForm.dateRange = null;
                    } else {
                        throw new Error(res.data?.msg || '清理失败');
                    }
                } catch (error) {
                    if (error !== 'cancel') {
                        console.error('清理失败:', error); // 添加错误日志
                        ElMessage.error(error.message || '操作失败');
                    }
                } finally {
                    isLoading.value = false;
                }
            };

            // 订单相关处理函数
            const handleOrderUserSelect = (item) => {
                orderForm.user_id = item.value;
            };

            const handleOrderUserClear = () => {
                orderForm.userId = '';
                orderForm.user_id = null;
                orderForm.dateRange = null;
            };

            // 处理清理订单
            const handleClearOrders = async () => {
                try {
                    // 构建确认消息
                    let confirmMessage = orderForm.userId 
                        ? `确定要清理用户ID ${orderForm.userId} 的订单记录吗？`
                        : '确定要清理所有订单记录吗？';
                        
                    // 添加日期范围信息到确认消息
                    if (orderForm.dateRange && orderForm.dateRange.length === 2) {
                        confirmMessage += `\n时间范围：${orderForm.dateRange[0]} 至 ${orderForm.dateRange[1]}`;
                    }
                    
                    // 添加状态信息到确认消息
                    if (orderForm.statusList && orderForm.statusList.length > 0) {
                        const statusNames = orderForm.statusList.map(status => {
                            const statusMap = {0: '未支付', 1: '已支付', 2: '已关闭', 3: '已退款'};
                            return statusMap[status] || status;
                        });
                        confirmMessage += `\n订单状态：${statusNames.join('、')}`;
                    }

                    await ElMessageBox.confirm(
                        confirmMessage,
                        '警告',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    );
                    
                    isOrderCleaning.value = true;
                    
                    // 准备请求参数
                    const params = {
                        user_id: orderForm.userId || 0,
                        delete_all: !orderForm.userId
                    };

                    // 添加日期范围参数
                    if (orderForm.dateRange && orderForm.dateRange.length === 2) {
                        params.start_date = orderForm.dateRange[0];
                        params.end_date = orderForm.dateRange[1];
                    }
                    
                    // 添加状态列表参数
                    if (orderForm.statusList && orderForm.statusList.length > 0) {
                        params.status_list = orderForm.statusList;
                    }

                    const res = await axios.post("/plugin/Clearallinone/api/clearOrders", params);
                    if (res.data.code === 200) {
                        ElMessage.success(res.data.msg);
                        // 清理成功后重置表单
                        orderForm.userId = '';
                        orderForm.user_id = null;
                        orderForm.dateRange = null;
                    } else {
                        ElMessage.error(res.data.msg || '清理失败');
                    }
                } catch (error) {
                    if (error !== 'cancel') {
                        console.error('清理订单错误:', error);
                        ElMessage.error('清理失败: ' + (error.message || '未知错误'));
                    }
                } finally {
                    isOrderCleaning.value = false;
                }
            };

            // 添加日期范围变化处理函数
            const handleOrderDateRangeChange = (dates) => {
                // 移除了 console.log
            };

            // 保存商户通知配置
            const saveMessageConfig = async () => {
                try {
                    const res = await axios.post('saveMessageConfig', {
                        status: messageForm.status,
                        days: messageForm.days,
                        executeTime: messageForm.executeTime,
                        interval: messageForm.interval
                    });
                    
                    if (res.data?.code === 1) {
                        ElMessage.success(res.data.msg);
                    } else {
                        throw new Error(res.data?.msg || '保存失败');
                    }
                } catch (error) {
                    ElMessage.error(error.message);
                }
            };

            // 添加商品图片统计查询方法
            const searchGoodsStats = async () => {
                try {
                    isSearching.value = true;
                    hasSearched.value = true;

                    const params = {
                        user_id: goodsForm.userId || 0,
                        start_date: goodsForm.dateRange ? goodsForm.dateRange[0] : '',
                        end_date: goodsForm.dateRange ? goodsForm.dateRange[1] : ''
                    };

                    const res = await axios.post("/plugin/Clearallinone/api/getGoodsImageStats", params);

                    if (res.data?.code === 200) {
                        goodsStats.value = res.data;
                    } else {
                        throw new Error(res.data?.msg || '查询失败');
                    }
                } catch (error) {
                    console.error('查询失败:', error);
                    ElMessage.error(error.message || '查询失败');
                    goodsStats.value = null;
                    hasSearched.value = false;
                } finally {
                    isSearching.value = false;
                }
            };

            // 修改创建备份函数
            const handleBackupDatabase = async (type) => {
                try {
                    isBackingUp.value = true;
                    const res = await axios.post("/plugin/Clearallinone/api/backupDatabase", {
                        type: type // 添加备份类型参数
                    });
                    
                    if (res.data?.code === 200) {
                        ElMessage.success(type === 'order' ? '订单表备份成功' : '完整备份成功');
                        // 刷新备份文件列表
                        getBackupFiles();
                    } else {
                        throw new Error(res.data?.msg || '备份失败');
                    }
                } catch (error) {
                    ElMessage.error(error.message || '备份失败');
                } finally {
                    isBackingUp.value = false;
                }
            };

            // 获取备份文件列表
            const getBackupFiles = async () => {
                try {
                    const res = await axios.post("/plugin/Clearallinone/api/getBackupFiles");
                    if (res.data?.code === 200) {
                        // 为每个文件添加importing属性
                        backupFiles.value = res.data.data.map(file => ({
                            ...file,
                            importing: false,
                            type: file.type || (file.filename.startsWith('order_backup_') ? '订单表备份' : '完整备份')
                        }));
                    }
                } catch (error) {
                    console.error('获取备份文件列表失败:', error);
                }
            };

            // 下载备份
            const downloadBackup = (file) => {
                window.location.href = `/plugin/Clearallinone/api/downloadBackupFile?filename=${file.filename}`;
            };

            // 从备份文件导入
            const importFromBackup = async (file) => {
                try {
                    await ElMessageBox.confirm(
                        `确定要导入备份文件 ${file.filename} 吗？`,
                        '导入确认',
                        {
                            confirmButtonText: '确认导入',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    );
                    
                    // 设置当前文件的导入状态
                    file.importing = true;
                    
                    const res = await axios.post("/plugin/Clearallinone/api/importBackupFile", {
                        filename: file.filename
                    });
                    
                    if (res.data?.code === 200) {
                        ElMessage({
                            type: 'success',
                            message: res.data.msg,
                            duration: 5000,
                            showClose: true,
                            grouping: true
                        });
                    } else {
                        throw new Error(res.data?.msg || '导入失败');
                    }
                } catch (error) {
                    if (error !== 'cancel') {
                        ElMessage.error(error.message || '导入失败');
                    }
                } finally {
                    file.importing = false;
                }
            };

            // 删除备份
            const deleteBackup = async (file) => {
                try {
                    await ElMessageBox.confirm(
                        `确定要删除备份文件 ${file.filename} 吗？`,
                        '警告',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    );

                    const res = await axios.post("/plugin/Clearallinone/api/deleteBackupFile", {
                        filename: file.filename
                    });

                    if (res.data?.code === 200) {
                        ElMessage.success('删除成功');
                        // 刷新备份文件列表
                        getBackupFiles();
                    } else {
                        throw new Error(res.data?.msg || '删除失败');
                    }
                } catch (error) {
                    if (error !== 'cancel') {
                        ElMessage.error(error.message || '删除失败');
                    }
                }
            };

            // 修改导入相关的处理函数
            const beforeOrderImport = (file) => {
                // 文件类型验证
                if (!file.name.endsWith('.sql')) {
                    ElMessage.error('请选择 .sql 格式的备份文件！');
                    return false;
                }
                
                // 显示确认对话框
                return ElMessageBox.confirm(
                    '确认导入此备份文件吗？导入时会智能处理重复数据。',
                    '导入确认',
                    {
                        confirmButtonText: '确认导入',
                        cancelButtonText: '取消',
                        type: 'warning',
                    }
                )
                .then(() => {
                    isImporting.value = true;
                    return true;
                })
                .catch(() => {
                    return false;
                });
            };

            const handleImportSuccess = (response) => {
                isImporting.value = false;
                if (response.code === 200) {
                    ElMessage({
                        type: 'success',
                        message: response.msg,
                        duration: 5000,
                        showClose: true,
                        grouping: true,
                    });
                    // 刷新备份文件列表
                    getBackupFiles();
                } else {
                    ElMessage({
                        type: 'error',
                        message: response.msg || '导入失败',
                        duration: 5000,
                        showClose: true,
                        grouping: true,
                    });
                }
            };

            const handleImportError = (error) => {
                isImporting.value = false;
                ElMessage({
                    type: 'error',
                    message: '导入失败：' + (error.message || '请确保文件格式正确且包含有效的订单数据'),
                    duration: 5000,
                    showClose: true,
                    grouping: true,
                });
            };

            // 清理所有卡密
            const handleClearCards = async () => {
                try {
                    const result = await ElMessageBox.confirm(
                        '确定要清理卡密记录吗？此操作不可恢复！',
                        '警告',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    );

                    if (result === 'confirm') {
                        isCardCleaning.value = true;
                        const params = {
                            user_id: cardForm.user_id || 0,
                            start_date: cardForm.dateRange ? cardForm.dateRange[0] : '',
                            end_date: cardForm.dateRange ? cardForm.dateRange[1] : ''
                        };

                        const res = await axios.post("/plugin/Clearallinone/api/clearCards", params);

                        if (res.data?.code === 200) {
                            ElMessage.success(res.data.msg);
                            cardForm.userId = '';
                            cardForm.user_id = null;
                            cardForm.dateRange = null;
                        } else {
                            throw new Error(res.data?.msg || '清理失败');
                        }
                    }
                } catch (error) {
                    if (error !== 'cancel') {
                        console.error('清理卡密错误:', error);
                        ElMessage.error(error.message || '清理失败');
                    }
                } finally {
                    isCardCleaning.value = false;
                }
            };

            // 清理已删除卡密
            const handleClearDeletedCards = async () => {
                try {
                    const result = await ElMessageBox.confirm(
                        '确定要清理已删除的卡密记录吗？此操作不可恢复！',
                        '警告',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    );

                    if (result === 'confirm') {
                        isCardCleaning.value = true;
                        const params = {
                            user_id: cardForm.user_id || 0,
                            start_date: cardForm.dateRange ? cardForm.dateRange[0] : '',
                            end_date: cardForm.dateRange ? cardForm.dateRange[1] : ''
                        };

                        const res = await axios.post("/plugin/Clearallinone/api/clearDeletedCards", params);

                        if (res.data?.code === 200) {
                            ElMessage.success(res.data.msg);
                            cardForm.userId = '';
                            cardForm.user_id = null;
                            cardForm.dateRange = null;
                        } else {
                            throw new Error(res.data?.msg || '清理失败');
                        }
                    }
                } catch (error) {
                    if (error !== 'cancel') {
                        console.error('清理已删除卡密错误:', error);
                        ElMessage.error(error.message || '清理失败');
                    }
                } finally {
                    isCardCleaning.value = false;
                }
            };

            // 用户选择处理函数
            const handleCardUserSelect = (item) => {
                cardForm.user_id = item.value;
            };

            const handleCardUserClear = () => {
                cardForm.user_id = null;
            };

            // 保存卡密清理配置
            const saveCardConfig = async () => {
                try {
                    isConfigSaving.value = true;
                    const res = await axios.post("/plugin/Clearallinone/api/saveCardConfig", cardConfig);
                    if (res.data?.code === 1) {
                        ElMessage.success(res.data.msg);
                        // 重新获取配置以确保数据同步
                        fetchData();
                    } else {
                        throw new Error(res.data?.msg || '保存失败');
                    }
                } catch (error) {
                    console.error('保存卡密清理配置失败:', error);
                    ElMessage.error(error.message || '保存失败');
                } finally {
                    isConfigSaving.value = false;
                }
            };

            // 初始化时获取配置
            onMounted(() => {
                // 确保页面加载时默认选中第一个菜单
                activeMenu.value = 'login';
                fetchData();
                getBackupFiles();
            });

            // 添加处理函数
            const handleMoneyLogUserSelect = (item) => {
                moneyLogForm.user_id = item.value;
            };

            const handleMoneyLogUserClear = () => {
                moneyLogForm.userId = '';
                moneyLogForm.user_id = null;
            };

            const handleClearMoneyLogs = async () => {
                try {
                    // 构建确认消息
                    let confirmMessage = moneyLogForm.user_id 
                        ? `确定要清理用户ID ${moneyLogForm.user_id} 的流水明细吗？`
                        : '确定要清理所有流水明细吗？';
                        
                    // 添加类型信息
                    if (moneyLogForm.sourceTypes.length > 0) {
                        confirmMessage += `\n选中的类型：${moneyLogForm.sourceTypes.join('、')}`;
                    }
                    
                    // 添加日期范围信息
                    if (moneyLogForm.dateRange && moneyLogForm.dateRange.length === 2) {
                        confirmMessage += `\n时间范围：${moneyLogForm.dateRange[0]} 至 ${moneyLogForm.dateRange[1]}`;
                    }

                    await ElMessageBox.confirm(
                        confirmMessage,
                        '警告',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    );

                    isMoneyLogCleaning.value = true;

                    const res = await axios.post("/plugin/Clearallinone/api/clearMoneyLogs", {
                        user_id: moneyLogForm.user_id || 0,
                        source_types: moneyLogForm.sourceTypes,
                        start_date: moneyLogForm.dateRange ? moneyLogForm.dateRange[0] : '',
                        end_date: moneyLogForm.dateRange ? moneyLogForm.dateRange[1] : ''
                    });

                    if (res.data?.code === 200) {
                        ElMessage.success(res.data.msg);
                        moneyLogForm.userId = '';
                        moneyLogForm.user_id = null;
                        moneyLogForm.sourceTypes = [];
                        moneyLogForm.dateRange = null;
                    } else {
                        throw new Error(res.data?.msg || '清理失败');
                    }
                } catch (error) {
                    if (error !== 'cancel') {
                        ElMessage.error(error.message || '清理失败');
                    }
                } finally {
                    isMoneyLogCleaning.value = false;
                }
            };

            // 处理重置用户订单数
            const handleClearUserSellCount = async () => {
                try {
                    const confirmMessage = orderForm.user_id 
                        ? `确定要重置用户ID ${orderForm.user_id} 的订单数吗？`
                        : '确定要重置所有用户的订单数吗？此操作不可恢复！';

                    await ElMessageBox.confirm(
                        confirmMessage,
                        '警告',
                        {
                            confirmButtonText: '确定重置',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    );

                    isOrderCleaning.value = true;
                    const res = await axios.post("/plugin/Clearallinone/api/clearUserSellCount", {
                        user_id: orderForm.user_id || 0
                    });

                    if (res.data?.code === 200) {
                        ElMessage.success(res.data.msg);
                        // 可以选择是否重置表单
                        // orderForm.userId = '';
                        // orderForm.user_id = null;
                    } else {
                        ElMessage.error(res.data?.msg || '重置失败');
                    }
                } catch (error) {
                    if (error !== 'cancel') {
                        console.error('重置用户订单数失败:', error);
                        ElMessage.error('重置失败: ' + (error.message || '未知错误'));
                    }
                } finally {
                    isOrderCleaning.value = false;
                }
            };

            // 用户数据清理相关方法
            const handleUserCleanSelect = (item) => {
                // 确保选中的值覆盖自动输入处理的结果
                userCleanForm.userId = item.value;
                userCleanForm.user_id = parseInt(item.value);
                // 清空之前的查询结果
                userDataResult.value = null;
                userDataTableList.value = [];
            };

            const handleUserCleanClear = () => {
                userCleanForm.userId = '';
                userCleanForm.user_id = null;
                userCleanForm.cleanTypes = [];
                userCleanForm.dateRange = null;
                // 清空查询结果
                userDataResult.value = null;
                userDataTableList.value = [];
            };

            // 查询用户数据
            const handleQueryUserData = async () => {
                try {
                    // 判断是否有有效的用户ID
                    if (!userCleanForm.user_id) {
                        // 如果没有有效的user_id，但有输入userId，尝试将其转换为用户ID
                        if (userCleanForm.userId && /^\d+$/.test(userCleanForm.userId)) {
                            userCleanForm.user_id = parseInt(userCleanForm.userId);
                        } else {
                            ElMessage.warning('请输入有效的用户ID');
                            return;
                        }
                    }

                    isQuerying.value = true;
                    const res = await axios.post("/plugin/Clearallinone/api/getUserRelatedData", {
                        user_id: userCleanForm.user_id
                    });

                    if (res.data?.code === 200) {
                        userDataResult.value = res.data.data;
                        // 转换数据格式以适应表格，并处理新的API返回格式
                        userDataTableList.value = Object.entries(res.data.data.related_data).map(([tableName, data]) => {
                            // 处理新旧API格式兼容
                            const count = typeof data === 'object' ? data.count : data;
                            const description = typeof data === 'object' ? data.description : (tableNameMap[tableName] || '');
                            
                            return {
                                table_name: tableName,
                                count: count,
                                display_name: description || tableNameMap[tableName] || tableName
                            };
                        });
                    } else {
                        throw new Error(res.data?.msg || '查询失败');
                    }
                } catch (error) {
                    console.error('查询用户数据失败:', error);
                    ElMessage.error(error.message || '查询失败');
                } finally {
                    isQuerying.value = false;
                }
            };

            // 清理单个表的数据
            const handleCleanSingleTable = async (row) => {
                try {
                    // 确保有有效的用户ID
                    if (!userCleanForm.user_id) {
                        if (userCleanForm.userId && /^\d+$/.test(userCleanForm.userId)) {
                            userCleanForm.user_id = parseInt(userCleanForm.userId);
                        } else {
                            ElMessage.warning('无效的用户ID');
                            return;
                        }
                    }
                    
                    await ElMessageBox.confirm(
                        `确定要清理用户ID: ${userCleanForm.user_id} 在表 ${row.table_name} 中的 ${row.count} 条数据吗？`,
                        '警告',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    );

                    const res = await axios.post("/plugin/Clearallinone/api/cleanUserDataInTable", {
                        user_id: userCleanForm.user_id,
                        table_name: row.table_name
                    });

                    if (res.data?.code === 200) {
                        ElMessage.success(res.data.msg);
                        // 重新查询数据
                        await handleQueryUserData();
                    } else {
                        throw new Error(res.data?.msg || '清理失败');
                    }
                } catch (error) {
                    if (error !== 'cancel') {
                        console.error('清理表数据失败:', error);
                        ElMessage.error(error.message || '清理失败');
                    }
                }
            };

            // 查询所有相关数据表
            const queryUserDataTables = async () => {
                try {
                    const res = await axios.post("/plugin/Clearallinone/api/getUserDataTables");
                    if (res.data?.code === 200) {
                        userDataTables.value = res.data.data;
                    }
                } catch (error) {
                    console.error('获取用户数据表失败:', error);
                    ElMessage.error('获取用户数据表失败');
                }
            };

            // 清理用户数据
            const handleUserClean = async () => {
                try {
                    // 判断是否有有效的用户ID
                    if (!userCleanForm.user_id) {
                        // 如果没有有效的user_id，但有输入userId，尝试将其转换为用户ID
                        if (userCleanForm.userId && /^\d+$/.test(userCleanForm.userId)) {
                            userCleanForm.user_id = parseInt(userCleanForm.userId);
                        } else {
                            ElMessage.warning('请输入有效的用户ID');
                            return;
                        }
                    }

                    const confirmMessage = userCleanForm.user_id 
                        ? `确定要清理用户ID ${userCleanForm.user_id} 的所有数据吗？此操作不可恢复！`
                        : '确定要清理所有用户的所有数据吗？此操作不可恢复！';

                    await ElMessageBox.confirm(
                        confirmMessage,
                        '警告',
                        {
                            confirmButtonText: '确定清理',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    );

                    isUserCleaning.value = true;
                    const res = await axios.post("/plugin/Clearallinone/api/clearUserAllData", {
                        user_id: userCleanForm.user_id
                    });

                    if (res.data?.code === 200) {
                        ElMessage.success(res.data.msg);
                        // 清理成功后重置表单
                        userCleanForm.userId = '';
                        userCleanForm.user_id = null;
                        userCleanForm.cleanTypes = [];
                        userCleanForm.dateRange = null;
                        // 清空查询结果
                        userDataResult.value = null;
                        userDataTableList.value = [];
                    } else {
                        throw new Error(res.data?.msg || '清理失败');
                    }
                } catch (error) {
                    if (error !== 'cancel') {
                        console.error('清理用户数据失败:', error);
                        ElMessage.error(error.message || '清理失败');
                    }
                } finally {
                    isUserCleaning.value = false;
                }
            };

            // 清理已删除用户关联数据
            const handleCleanDeletedUserData = async () => {
                try {
                    const confirmMessage = userCleanForm.user_id 
                        ? `确定要清理用户ID ${userCleanForm.user_id} 的已删除用户关联数据吗？此操作不可恢复！`
                        : '确定要清理所有用户的已删除用户关联数据吗？此操作不可恢复！';

                    await ElMessageBox.confirm(
                        confirmMessage,
                        '警告',
                        {
                            confirmButtonText: '确定清理',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    );

                    isUserCleaning.value = true;
                    const res = await axios.post("/plugin/Clearallinone/api/clearDeletedUserData", {
                        user_id: userCleanForm.user_id
                    });

                    if (res.data?.code === 200) {
                        ElMessage.success(res.data.msg);
                        // 清理成功后重置表单
                        userCleanForm.userId = '';
                        userCleanForm.user_id = null;
                        userCleanForm.cleanTypes = [];
                        userCleanForm.dateRange = null;
                        // 清空查询结果
                        userDataResult.value = null;
                        userDataTableList.value = [];
                    } else {
                        throw new Error(res.data?.msg || '清理失败');
                    }
                } catch (error) {
                    if (error !== 'cancel') {
                        console.error('清理已删除用户关联数据失败:', error);
                        ElMessage.error(error.message || '清理失败');
                    }
                } finally {
                    isUserCleaning.value = false;
                }
            };

            // 清理所有已删除用户的关联数据
            const handleCleanAllDeletedUsers = async () => {
                try {
                    await ElMessageBox.confirm(
                        '确定要清理所有已从用户表删除但在其他表中仍有关联数据的用户记录吗？此操作不可恢复！',
                        '警告',
                        {
                            confirmButtonText: '确定清理',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    );

                    isCleaningAllDeleted.value = true;
                    showCleaningProgress.value = true;
                    cleaningProgress.value = 0;
                    
                    // 清理开始时重置表单和结果
                    userCleanForm.userId = '';
                    userCleanForm.user_id = null;
                    userCleanForm.cleanTypes = [];
                    userCleanForm.dateRange = null;
                    userDataResult.value = null;
                    userDataTableList.value = [];
                    
                    // 重置清理统计
                    cleaningStats.value = {
                        processedCount: 0,
                        totalCount: 0,
                        currentUserId: null,
                        deletedRecords: 0
                    };
                    
                    // 第一步：获取所有需要清理的已删除用户ID
                    const idsRes = await axios.post("/plugin/Clearallinone/api/getDeletedUserIds");
                    
                    if (idsRes.data?.code !== 200 || !idsRes.data?.data?.userIds || idsRes.data?.data?.userIds.length === 0) {
                        ElMessage.info(idsRes.data?.msg || '没有找到需要清理的已删除用户数据');
                        showCleaningProgress.value = false;
                        isCleaningAllDeleted.value = false;
                        return;
                    }
                    
                    const userIds = idsRes.data.data.userIds;
                    cleaningStats.value.totalCount = userIds.length;
                    
                    // 第二步：逐个处理每个用户ID
                    for (let i = 0; i < userIds.length; i++) {
                        const userId = userIds[i];
                        cleaningStats.value.currentUserId = userId;
                        cleaningStats.value.processedCount = i + 1;
                        
                        // 更新进度
                        cleaningProgress.value = Math.floor((i + 1) / userIds.length * 100);
                        
                        try {
                            // 清理单个用户数据
                            const res = await axios.post("/plugin/Clearallinone/api/clearDeletedUserData", {
                                user_id: userId
                            });
                            
                            if (res.data?.code === 200 && res.data?.data) {
                                // 计算删除的记录数
                                let deletedCount = 0;
                                res.data.data.forEach(item => {
                                    deletedCount += item.count;
                                });
                                cleaningStats.value.deletedRecords += deletedCount;
                            }
                            
                            // 添加一点延迟，减轻服务器压力
                            await new Promise(resolve => setTimeout(resolve, 100));
                            
                        } catch (err) {
                            console.error(`清理用户ID ${userId} 失败:`, err);
                            // 继续处理下一个用户，不中断整体流程
                        }
                    }
                    
                    ElMessage.success(`清理完成，共处理 ${cleaningStats.value.processedCount} 个已删除用户，删除 ${cleaningStats.value.deletedRecords} 条记录`);
                    
                } catch (error) {
                    if (error !== 'cancel') {
                        console.error('清理所有已删除用户关联数据失败:', error);
                        ElMessage.error(error.message || '清理失败');
                    }
                } finally {
                    isCleaningAllDeleted.value = false;
                    // 3秒后隐藏进度条
                    setTimeout(() => {
                        showCleaningProgress.value = false;
                    }, 3000);
                }
            };

            const handleUserIdInput = (value) => {
                userCleanForm.userId = value;
                // 如果输入的是纯数字，则直接设置为user_id
                if (value && /^\d+$/.test(value)) {
                    userCleanForm.user_id = parseInt(value);
                } else {
                    // 如果不是纯数字且不是通过选择得到的值，则清空user_id
                    // 但保留输入框中的内容，让用户可以继续输入或修改
                    userCleanForm.user_id = null;
                }
            };

            return {
                form,
                isLoading,
                activeMenu,
                queryUsernames,
                handleUsernameSelect,
                handleClearLogs,
                handleMenuSelect,
                handleClearComplaintImages,
                complaintForm,
                handleComplaintUserSelect,
                handleClearUserComplaints,
                handleComplaintUserClear,
                handleUsernameClear,
                complaintStats,
                handleDateRangeChange,
                isSearching,
                searchComplaintStats,
                goodsForm,
                handleGoodsUserSelect,
                handleGoodsUserClear,
                handleClearGoods,
                handleClearGoodsImages,
                orderForm,
                saveOrder,
                handleOrderUserSelect,
                handleOrderUserClear,
                handleClearOrders,
                isOrderCleaning,
                isConfigSaving,
                hasSearched,
                messageForm,
                handleMessageUserSelect,
                handleMessageUserClear,
                handleClearMessages,
                saveMessageConfig,
                goodsStats,
                searchGoodsStats,
                isBackingUp,
                backupFiles,
                handleBackupDatabase,
                downloadBackup,
                deleteBackup,
                isImporting,
                beforeOrderImport,
                handleImportSuccess,
                handleImportError,
                cardForm,
                isCardCleaning,
                handleClearCards,
                handleClearDeletedCards,
                handleCardUserSelect,
                handleCardUserClear,
                cardConfig,
                saveCardConfig,
                moneyLogForm,
                isMoneyLogCleaning,
                handleMoneyLogUserSelect,
                handleMoneyLogUserClear,
                handleClearMoneyLogs,
                handleClearGoodsSellCount,
                handleClearUserSellCount,
                userCleanForm,
                isUserCleaning,
                userDataTables,
                userDataResult,
                userDataTableList,
                isQuerying,
                handleUserCleanSelect,
                handleUserCleanClear,
                handleUserClean,
                handleQueryUserData,
                handleCleanSingleTable,
                queryUserDataTables,
                handleCleanDeletedUserData,
                handleCleanAllDeletedUsers,
                isCleaningAllDeleted,
                cleaningProgress,
                showCleaningProgress,
                cleaningStats,
                handleUserIdInput,
                importFromBackup,
            };
        }
    });

    app.use(ElementPlus, {
        locale: ElementPlusLocaleZhCn
    });
    app.mount("#app");
</script>
</body>
</html> 