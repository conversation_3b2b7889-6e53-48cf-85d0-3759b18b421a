<?php

namespace app\index\test\controller;

use app\common\controller\BaseIndex;
use app\common\model\Article as ArticleModel;

class Article extends BaseIndex {

    public function index() {

        $notice = ArticleModel::hasWhere("category", ['alias' => 'notice'])->where(['Article.status' => 1])->order("sort desc,id desc")->limit(5)->select();

        return view('', [
            'title' => sysconf("website.title"),
            'notice' => $notice,
        ]);
    }
}
