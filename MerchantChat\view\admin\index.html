<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>商家聊天配置</title>
    <style>
        /* 基础样式 */
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f5f7fa;
            color: #303133;
        }
        
        #app {
            padding: 15px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .el-card {
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05) !important;
            margin-bottom: 20px;
        }
        
        .el-card__header {
            padding: 18px 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        .card-header {
            display: flex;
            flex-direction: column;
        }
        
        .card-header span {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 8px;
        }
        
        .el-form {
            padding: 20px 0 5px;
        }
        
        /* 表单样式优化 */
        .el-form-item {
            margin-bottom: 22px;
            padding: 0 5px;
        }
        
        .el-form-item__label {
            font-weight: 500;
            color: #606266;
        }
        
        .el-input,
        .el-select {
            width: 100%;
            max-width: 400px;
        }
        
        .el-input__inner {
            border-radius: 4px;
        }
        
        .el-form-item-msg {
            font-size: 12px;
            color: #909399;
            line-height: 1.5;
            margin-top: 6px;
            padding-left: 1px;
        }
        
        /* 按钮样式优化 */
        .el-button {
            border-radius: 4px;
            padding: 10px 20px;
            transition: all 0.3s;
        }
        
        .el-button--primary {
            background-color: #409eff;
        }
        
        .el-button--primary:hover {
            background-color: #66b1ff;
        }
        
        .save-btn {
            min-width: 120px;
        }
        
        /* 开关组件优化 */
        .el-switch {
            margin-right: 8px;
        }
        
        /* 分隔线样式 */
        .form-divider {
            margin: 30px 0;
            border-top: 1px dashed #dcdfe6;
            position: relative;
        }
        
        .form-divider-text {
            position: absolute;
            left: 50%;
            top: -10px;
            transform: translateX(-50%);
            background-color: white;
            padding: 0 15px;
            color: #606266;
            font-size: 14px;
            font-weight: 500;
        }
        
        /* 响应式设计 - 简化版 */
        @media screen and (max-width: 767px) {
            #app {
                padding: 10px;
            }
            
            .el-card {
                margin: 10px 0;
                border-radius: 6px;
            }
            
            .el-card__header {
                padding: 15px;
            }
            
            .el-form {
                padding: 15px 0 5px;
            }
            
            .card-header span {
                font-size: 16px;
            }
            
            .el-form-item {
                margin-bottom: 18px;
            }
            
            .el-form-item__label {
                font-size: 14px;
            }
            
            .el-button {
                padding: 9px 15px;
            }
        }
        
        @media screen and (max-width: 480px) {
            #app {
                padding: 8px;
            }
            
            .el-card__header {
                padding: 12px;
            }
            
            .el-form-item__label {
                font-size: 13px;
            }
            
            .el-form-item {
                margin-bottom: 15px;
            }
            
            .card-header span {
                font-size: 15px;
            }
            
            .el-button {
                padding: 8px 12px;
                font-size: 13px;
            }
            
            .el-form-item-msg {
                font-size: 11px;
                line-height: 1.4;
            }
        }
        
        /* 横屏优化 */
        @media screen and (max-width: 767px) and (orientation: landscape) {
            .el-form {
                max-width: 600px;
                margin: 0 auto;
            }
        }
        
        /* 深色模式支持 */
        @media (prefers-color-scheme: dark) {
            .form-divider-text {
                background-color: #1e1e1e;
                color: #dcdfe6;
            }
        }
    </style>
</head>

<body>
    <div id="app">
        <el-card shadow="hover">
            <template #header>
                <div class="card-header">
                    <span>商家聊天默认配置</span>
                    <div class="el-form-item-msg">此处设置的配置将作为商家未设置时的默认值</div>
                </div>
            </template>

            <el-form :model="form" label-width="140px">
                <!-- 全局启用 -->
                <el-form-item label="开启默认模板的客服聊天">
                    <el-switch 
                        v-model="form.global_enabled" 
                        :active-value="1" 
                        :inactive-value="0"
                        class="custom-switch" 
                    />
                </el-form-item>
                
                <!-- 简约模板启用 -->
                <el-form-item label="开启简约模板的客服聊天">
                    <el-switch 
                        v-model="form.simple_enabled" 
                        :active-value="1" 
                        :inactive-value="0"
                        class="custom-switch" 
                    />
                </el-form-item>
                
                <!-- 默认手机模板启用 -->
                <el-form-item label="开启手机模板的客服聊天">
                    <el-switch 
                        v-model="form.defaulth5_enabled" 
                        :active-value="1" 
                        :inactive-value="0"
                        class="custom-switch" 
                    />
                </el-form-item>
                
                <div class="form-divider">
                    <span class="form-divider-text">管理员配置</span>
                </div>
                
                <!-- 聊天开关 -->
                <el-form-item label="聊天开关">
                    <el-switch 
                        v-model="form.status" 
                        :active-value="1" 
                        :inactive-value="0"
                        class="custom-switch" 
                    />
                </el-form-item>

                <!-- business_id -->
                <el-form-item label="business_id">
                    <template #label>
                        business_id
                        <el-tooltip content="此处设置的business_id将显示在嵌入到网站的脚本中，如:<script>var ymwl={business_id:193,...}</script>" placement="top">
                            <span style="display:inline-block;width:16px;height:16px;line-height:16px;text-align:center;background-color:#909399;color:white;border-radius:50%;font-size:12px;margin-left:5px;cursor:pointer;">?</span>
                        </el-tooltip>
                    </template>
                    <el-input v-model="form.business_id" placeholder="请输入business_id" />
                    <div class="el-form-item-msg">聊天脚本中的 business_id 参数值</div>
                </el-form-item>

                <!-- Token -->
                <el-form-item label="Token">
                    <template #label>
                        Token
                        <el-tooltip content="此处设置的Token将显示在嵌入到网站的脚本中，如:<script>var ymwl={token:'afd4d8896d3bd733a2ebd12d788c62d0',...}</script>" placement="top">
                            <span style="display:inline-block;width:16px;height:16px;line-height:16px;text-align:center;background-color:#909399;color:white;border-radius:50%;font-size:12px;margin-left:5px;cursor:pointer;">?</span>
                        </el-tooltip>
                    </template>
                    <el-input v-model="form.token" placeholder="请输入Token" />
                    <div class="el-form-item-msg">聊天脚本中的 token 参数值</div>
                </el-form-item>

                <!-- 脚本URL -->
                <el-form-item label="脚本URL">
                    <template #label>
                        脚本URL
                        <el-tooltip content="此URL将作为脚本src的值在网站中引用，如:<script src='https://kf.y1yun.top/assets/front/ymwl_online.js?v=1749626195'></script>" placement="top">
                            <span style="display:inline-block;width:16px;height:16px;line-height:16px;text-align:center;background-color:#909399;color:white;border-radius:50%;font-size:12px;margin-left:5px;cursor:pointer;">?</span>
                        </el-tooltip>
                    </template>
                    <el-input v-model="form.script_url" placeholder="请输入聊天脚本URL" />
                    <div class="el-form-item-msg">聊天脚本URL，默认为: https://kf.y1yun.top/assets/front/ymwl_online.js</div>
                </el-form-item>
                
                <!-- 脚本版本号 -->
                <el-form-item label="版本号">
                    <template #label>
                        版本号
                        <el-tooltip content="此版本号将作为脚本src的URL参数v的值，如:ymwl_online.js?v=1749626195" placement="top">
                            <span style="display:inline-block;width:16px;height:16px;line-height:16px;text-align:center;background-color:#909399;color:white;border-radius:50%;font-size:12px;margin-left:5px;cursor:pointer;">?</span>
                        </el-tooltip>
                    </template>
                    <el-input v-model="form.script_version" placeholder="请输入脚本版本号" />
                    <div class="el-form-item-msg">聊天脚本版本号，用于URL参数v的值，留空则使用随机时间戳</div>
                </el-form-item>

                <!-- 商家修改权限 -->
                <el-form-item label="商家权限">
                    <el-switch 
                        v-model="form.merchant_can_edit" 
                        :active-value="1" 
                        :inactive-value="0"
                        class="custom-switch" 
                    />
                    <div class="el-form-item-msg">开启后商家可以修改自己的聊天配置，关闭后只能使用默认设置</div>
                </el-form-item>

                <!-- 提交按钮 -->
                <el-form-item>
                    <el-button 
                        type="primary" 
                        :loading="loading" 
                        @click="save"
                        class="save-btn"
                    >
                        保存设置
                    </el-button>
                </el-form-item>
            </el-form>
        </el-card>
    </div>

    <!-- 引入依赖库 -->
    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>

    <script>
        const { createApp, ref, reactive, onMounted } = Vue;
        const app = createApp({
            setup() {
                // 状态定义
                const loading = ref(false);
                const form = reactive({
                    global_enabled: 0,
                    simple_enabled: 0,
                    defaulth5_enabled: 0,
                    status: 0,
                    business_id: '',
                    token: '',
                    script_url: 'https://kf.y1yun.top/assets/front/ymwl_online.js',
                    script_version: '',
                    merchant_can_edit: 1
                });

                // 获取默认配置
                const fetchConfig = async () => {
                    try {
                        const res = await axios.post('getConfig');
                        if (res.data?.code === 200 && res.data?.data) {
                            const data = res.data.data;
                            form.global_enabled = parseInt(data.global_enabled) || 0;
                            form.simple_enabled = parseInt(data.simple_enabled) || 0;
                            form.defaulth5_enabled = parseInt(data.defaulth5_enabled) || 0;
                            form.status = parseInt(data.status) || 0;
                            form.business_id = data.business_id || '';
                            form.token = data.token || '';
                            form.script_url = data.script_url || 'https://kf.y1yun.top/assets/front/ymwl_online.js';
                            form.script_version = data.script_version || '';
                            form.merchant_can_edit = data.merchant_can_edit !== undefined ? parseInt(data.merchant_can_edit) : 1;
                        }
                    } catch (error) {
                        console.error('获取数据失败:', error);
                        ElementPlus.ElMessage.error('获取数据失败，请刷新页面重试');
                    }
                };

                // 保存默认配置
                const save = async () => {
                    try {
                        loading.value = true;
                        // 数据验证
                        if (form.status === 1) {
                            if (!form.business_id) {
                                ElementPlus.ElMessage.error('请输入业务ID');
                                return;
                            }
                            if (!form.token) {
                                ElementPlus.ElMessage.error('请输入Token');
                                return;
                            }
                            if (!form.script_url) {
                                ElementPlus.ElMessage.error('请输入脚本URL');
                                return;
                            }
                        }

                        const res = await axios.post('saveConfig', form);
                        if (res.data?.code === 200) {
                            ElementPlus.ElMessage.success(res.data.msg || '保存成功');
                            
                            // 使用返回的数据更新表单
                            if (res.data.data) {
                                const data = res.data.data;
                                form.global_enabled = parseInt(data.global_enabled) || 0;
                                form.simple_enabled = parseInt(data.simple_enabled) || 0;
                                form.defaulth5_enabled = parseInt(data.defaulth5_enabled) || 0;
                                form.status = parseInt(data.status);
                                form.business_id = data.business_id;
                                form.token = data.token;
                                form.script_url = data.script_url;
                                form.script_version = data.script_version;
                                form.merchant_can_edit = data.merchant_can_edit !== undefined ? parseInt(data.merchant_can_edit) : 1;
                            }
                        } else {
                            ElementPlus.ElMessage.error(res.data?.msg || '保存失败');
                        }
                    } catch (error) {
                        console.error('保存失败:', error);
                        ElementPlus.ElMessage.error('保存失败，请稍后重试');
                    } finally {
                        loading.value = false;
                    }
                };

                // 初始化
                onMounted(() => {
                    fetchConfig();
                });

                return {
                    loading,
                    form,
                    save
                };
            }
        });

        app.use(ElementPlus);
        app.mount('#app');
    </script>
</body>
</html> 