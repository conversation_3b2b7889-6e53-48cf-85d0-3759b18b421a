<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <meta http-equiv="Cache-Control" content="max-age=31536000">
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>渠道账户守护设置</title>
    <style>
        /* 加载动画样式 */
        #loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #fff;
            z-index: 9999;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(0, 0, 0, 0.1);
            border-top-color: #3498db;
            border-radius: 50%;
            animation: spin 1s ease infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .page-container {
            padding: 20px;
        }

        .el-form-item-description {
            font-size: 12px;
            color: #909399;
            margin-top: 4px;
        }
    </style>
</head>

<body>
<!-- 加载动画容器 -->
<div id="loading">
    <div class="spinner"></div>
</div>

<!-- Vue 应用挂载点 -->
<div id="app" style="display: none">
    <div class="page-container">
        <el-card shadow="never">
            <el-form :model="configForm" label-width="120px">
                <el-form-item label="监控状态：">
                    <el-radio-group v-model="configForm.status">
                        <el-radio :label="0">关闭</el-radio>
                        <el-radio :label="1">开启</el-radio>
                    </el-radio-group>
                </el-form-item>

                <el-form-item label="失败阈值：">
                    <el-input-number 
                        v-model="configForm.fail_threshold" 
                        :min="1" 
                        :max="100"
                        :step="1">
                        <template #suffix>笔</template>
                    </el-input-number>
                    <div class="el-form-item-description">
                        连续失败订单数达到此值时将自动关闭渠道账户（1-100笔）
                    </div>
                </el-form-item>

                <el-form-item label="检查间隔：">
                    <el-input-number 
                        v-model="configForm.check_interval" 
                        :min="5" 
                        :max="3600"
                        :step="1">
                        <template #suffix>秒</template>
                    </el-input-number>
                    <div class="el-form-item-description">
                        系统自动检查的时间间隔（最少5秒）
                    </div>
                </el-form-item>

                <el-form-item label="检测时间：">
                    <el-input-number 
                        v-model="configForm.check_time" 
                        :min="1" 
                        :max="300"
                        :step="1">
                        <template #suffix>分钟</template>
                    </el-input-number>
                    <div class="el-form-item-description">
                        检测订单未支付的等待时间（1-300分钟）
                    </div>
                </el-form-item>

                <el-form-item label="通知方式：">
                    <el-radio-group v-model="configForm.notify_type">
                        <el-radio :label="1">邮箱通知</el-radio>
                        <el-radio :label="2">短信通知</el-radio>
                    </el-radio-group>
                </el-form-item>

                <el-form-item label="通知邮箱：" v-if="configForm.notify_type === 1">
                    <el-input 
                        v-model="configForm.notify_email" 
                        placeholder="请输入通知邮箱地址">
                    </el-input>
                    <div class="el-form-item-description">
                        渠道账户自动关闭时将发送通知到此邮箱
                    </div>
                </el-form-item>

                <el-form-item label="通知手机：" v-if="configForm.notify_type === 2">
                    <el-input 
                        v-model="configForm.notify_mobile" 
                        placeholder="请输入通知手机号">
                    </el-input>
                    <div class="el-form-item-description">
                        渠道账户自动关闭时将发送短信通知到此手机号
                        <el-button 
                            type="primary" 
                            link 
                            :loading="smsTemplateLoading"
                            @click="insertSmsTemplate">
                            添加短信模板
                        </el-button>
                    </div>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="saveConfig" :loading="isLoading">
                        保存
                    </el-button>
                </el-form-item>
            </el-form>
        </el-card>
    </div>
</div>

<script src="/static/others/vue/vue.global.prod.js"></script>
<script src="/static/others/element-plus/index.full.min.js"></script>
<script src="/static/others/axios/axios.min.js"></script>

<script>
const { createApp, ref, reactive } = Vue;

const app = createApp({
    setup() {
        const isLoading = ref(false);
        const smsTemplateLoading = ref(false);
        const configForm = reactive({
            status: 0,
            fail_threshold: 6,
            check_interval: 5,
            check_time: 2,
            notify_type: 1,    // 1:邮箱 2:短信
            notify_email: '',
            notify_mobile: ''
        });

        // 添加请求拦截器处理401错误
        axios.interceptors.response.use(
            response => response,
            error => {
                if (error.response?.status === 401 || error.response?.data?.code === 401) {
                    ElementPlus.ElMessage.error('登录已过期，请重新登录');
                    // 跳转到登录页面
                    window.location.href = '/admin/login/index';
                    return Promise.reject(error);
                }
                return Promise.reject(error);
            }
        );

        // 加载配置
        const loadConfig = async () => {
            try {
                const res = await axios.post('/plugin/ChannelGuard/api/fetchData');
                if (res.data?.code === 200) {
                    const data = res.data.data;
                    configForm.status = data.monitor_status ?? 0;
                    configForm.fail_threshold = data.fail_threshold ?? 6;
                    configForm.check_interval = data.check_interval ?? 5;
                    configForm.check_time = data.check_time ?? 2;
                    configForm.notify_type = data.notify_type ?? 1;
                    configForm.notify_email = data.notify_email ?? '';
                    configForm.notify_mobile = data.notify_mobile ?? '';
                } else {
                    ElementPlus.ElMessage.error(res.data?.msg || '加载配置失败');
                }
            } catch (error) {
                if (!error.response?.status === 401) {  // 401错误已在拦截器中处理
                    ElementPlus.ElMessage.error('加载配置失败：' + error.message);
                }
            }
        };

        // 保存配置
        const saveConfig = async () => {
            isLoading.value = true;
            try {
                const res = await axios.post('/plugin/ChannelGuard/api/saveConfig', configForm);
                if (res.data?.code === 200) {
                    ElementPlus.ElMessage.success('保存成功');
                } else {
                    ElementPlus.ElMessage.error(res.data?.msg || '保存失败');
                }
            } catch (error) {
                if (!error.response?.status === 401) {  // 401错误已在拦截器中处理
                    ElementPlus.ElMessage.error('保存失败：' + error.message);
                }
            } finally {
                isLoading.value = false;
            }
        };

        // 插入短信模板
        const insertSmsTemplate = async () => {
            smsTemplateLoading.value = true;
            try {
                const res = await axios.post('/plugin/ChannelGuard/api/insertSmsTemplate');
                if (res.data?.code === 200) {
                    ElementPlus.ElMessage({
                        message: '短信模板添加成功',
                        type: 'success',
                        duration: 2000
                    });
                } else {
                    ElementPlus.ElMessage({
                        message: res.data?.msg || '添加失败',
                        type: 'error',
                        duration: 2000
                    });
                }
            } catch (error) {
                ElementPlus.ElMessage({
                    message: error.response?.data?.msg || '添加失败',
                    type: 'error',
                    duration: 2000
                });
            } finally {
                smsTemplateLoading.value = false;
            }
        };

        // 页面加载完成后执行
        Vue.onMounted(() => {
            loadConfig();
            document.getElementById('loading').style.display = 'none';
            document.getElementById('app').style.display = 'block';
        });

        return {
            isLoading,
            smsTemplateLoading,
            configForm,
            saveConfig,
            insertSmsTemplate
        };
    }
});

app.use(ElementPlus);
app.mount('#app');
</script>
</body>
</html> 