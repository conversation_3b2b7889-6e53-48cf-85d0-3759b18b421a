<?php
namespace plugin\Clearmp4\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;

class Api extends BasePlugin
{
    protected $scene = [
        'admin'
    ];
    protected $noNeedLogin = [];

    public function index()
    {
        return View::fetch();
    }

    // 搜索用户接口
    public function searchUsers()
    {
        try {
            $keyword = $this->request->post('keyword', '');
            if (!is_numeric($keyword)) {
                return json(['code' => 200, 'data' => []]);
            }

            // 通过ID搜索用户，并获取相似ID的用户列表
            $users = Db::name('user')
                ->where('id', 'like', $keyword . '%')
                ->field(['id', 'username'])
                ->limit(10)
                ->select()
                ->toArray();

            if (!empty($users)) {
                return json([
                    'code' => 200, 
                    'msg' => '查询成功',
                    'data' => array_map(function($user) {
                        return [
                            'value' => $user['id'],
                            'label' => "ID: {$user['id']} ({$user['username']})"
                        ];
                    }, $users)
                ]);
            }

            return json(['code' => 200, 'msg' => '未找到用户', 'data' => []]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '搜索用户失败: ' . $e->getMessage()]);
        }
    }

    // 清理商品描述中的视频文件
    public function clearGoodsDescriptionVideos()
    {
        try {
            $userId = $this->request->post('user_id/d', 0);
            $startDate = $this->request->post('start_date', '');
            $endDate = $this->request->post('end_date', '');
            $extensions = $this->request->post('extensions', '');
            $maxSize = $this->request->post('max_size/f', 0); // MB
            $minSize = $this->request->post('min_size/f', 0); // MB

            // 如果没有指定扩展名，使用默认配置
            if (empty($extensions)) {
                $extensions = plugconf("Clearmp4.video_extensions") ?? "mp4,avi,mov,wmv,flv,mkv,webm,m4v";
            }

            // 构建查询条件
            $query = Db::name('goods')
                ->whereNotNull('description')
                ->where('description', '<>', '')
                ->where('description', 'like', '%<video%');

            // 添加用户ID条件
            if ($userId > 0) {
                $query = $query->where('user_id', $userId);
            }

            // 添加日期范围条件
            if (!empty($startDate)) {
                $query = $query->where('create_time', '>=', strtotime($startDate));
            }
            if (!empty($endDate)) {
                $query = $query->where('create_time', '<=', strtotime($endDate . ' 23:59:59'));
            }

            $records = $query->select()->toArray();

            if (empty($records)) {
                return json(['code' => 200, 'msg' => $userId > 0 ? "该用户暂无商品描述视频文件" : "暂无商品描述视频文件"]);
            }

            $deletedFiles = 0;
            $totalFiles = 0;
            $failedFiles = [];
            $skippedFiles = [];
            $updatedRecords = [];

            // 处理视频文件
            foreach ($records as $record) {
                $originalDescription = $record['description'];
                $newDescription = $this->processRichTextVideos($originalDescription, $extensions, $deletedFiles, $totalFiles, $failedFiles, $skippedFiles, $minSize, $maxSize, $startDate, $endDate);

                // 如果描述内容有变化，记录需要更新的记录
                if ($newDescription !== $originalDescription) {
                    $updatedRecords[] = [
                        'id' => $record['id'],
                        'description' => $newDescription
                    ];
                }
            }

            // 批量更新数据库记录
            foreach ($updatedRecords as $updateRecord) {
                Db::name('goods')
                    ->where('id', $updateRecord['id'])
                    ->update(['description' => $updateRecord['description']]);
            }

            $message = sprintf(
                "%s清理完成！共处理 %d 个视频文件，成功删除 %d 个文件，更新 %d 条商品记录",
                $userId > 0 ? "用户ID {$userId} 的商品描述视频" : "所有商品描述视频",
                $totalFiles,
                $deletedFiles,
                count($updatedRecords)
            );

            if (!empty($skippedFiles)) {
                $message .= "\n跳过的文件：\n" . implode("\n", $skippedFiles);
            }

            if (!empty($failedFiles)) {
                $message .= "\n删除失败的文件：\n" . implode("\n", $failedFiles);
            }

            error_log("Clearmp4: " . $message);

            return json([
                'code' => 200,
                'msg' => $message,
                'data' => [
                    'total_files' => $totalFiles,
                    'deleted_files' => $deletedFiles,
                    'failed_files' => $failedFiles,
                    'skipped_files' => $skippedFiles,
                    'updated_records' => count($updatedRecords)
                ]
            ]);

        } catch (\Exception $e) {
            error_log("Clearmp4: 清理商品描述视频失败: " . $e->getMessage());
            return json(['code' => 500, 'msg' => '清理失败: ' . $e->getMessage()]);
        }
    }

    // 获取商品描述视频统计信息
    public function getGoodsDescriptionVideoStats()
    {
        try {
            $userId = $this->request->post('user_id/d', 0);
            $startDate = $this->request->post('start_date', '');
            $endDate = $this->request->post('end_date', '');
            $extensions = $this->request->post('extensions', '');

            // 如果没有指定扩展名，使用默认配置
            if (empty($extensions)) {
                $extensions = plugconf("Clearmp4.video_extensions") ?? "mp4,avi,mov,wmv,flv,mkv,webm,m4v";
            }

            // 构建查询条件
            $query = Db::name('goods')
                ->whereNotNull('description')
                ->where('description', '<>', '')
                ->where('description', 'like', '%<video%');

            // 如果指定了用户ID，则添加用户条件
            if ($userId > 0) {
                $query = $query->where('user_id', $userId);
            }

            // 注意：这里不再使用商品创建时间过滤，而是在文件级别进行时间过滤
            // 因为我们要根据视频文件的实际上传时间来过滤，而不是商品创建时间
            // 如果需要提高性能，可以保留商品创建时间的粗略过滤

            $records = $query->select()->toArray();

            $totalFiles = 0;
            $totalSize = 0;
            $fileList = [];

            foreach ($records as $record) {
                // 从富文本描述中提取视频文件，传递日期参数进行文件级别的时间过滤
                $this->extractVideosFromDescription($record['description'], $extensions, $totalFiles, $totalSize, $fileList, $startDate, $endDate);
            }

            // 格式化文件大小
            $formattedSize = $this->formatFileSize($totalSize);

            // 修改返回消息，添加日期范围信息
            $dateRangeMsg = '';
            if (!empty($startDate) && !empty($endDate)) {
                $dateRangeMsg = sprintf("（%s 至 %s）", $startDate, $endDate);
            } elseif (!empty($startDate)) {
                $dateRangeMsg = sprintf("（%s 之后）", $startDate);
            } elseif (!empty($endDate)) {
                $dateRangeMsg = sprintf("（%s 之前）", $endDate);
            }

            // 修改返回消息，根据是否有用户ID显示不同内容
            $userMsg = $userId > 0 ? "用户ID {$userId} " : "所有用户";
            return json([
                'code' => 200,
                'msg' => sprintf("%s%s共有 %d 个商品描述视频文件，总大小 %s",
                    $userMsg, $dateRangeMsg, $totalFiles, $formattedSize),
                'data' => [
                    'total_files' => $totalFiles,
                    'total_size' => $totalSize,
                    'formatted_size' => $formattedSize,
                    'files' => $fileList
                ]
            ]);

        } catch (\Exception $e) {
            error_log("Clearmp4: 查询商品描述视频统计失败: " . $e->getMessage());
            return json(['code' => 500, 'msg' => '查询失败: ' . $e->getMessage()]);
        }
    }

    // 从富文本描述中提取视频文件信息
    private function extractVideosFromDescription($description, $extensions, &$totalFiles, &$totalSize, &$fileList, $startDate = '', $endDate = '')
    {
        // 匹配富文本编辑器中的视频标签
        $pattern = '/<div[^>]*data-w-e-type="video"[^>]*>.*?<video[^>]*>.*?<source[^>]*src="([^"]+)"[^>]*\/>.*?<\/video>.*?<\/div>/is';

        preg_match_all($pattern, $description, $matches);

        if (!empty($matches[1])) {
            foreach ($matches[1] as $videoUrl) {
                if ($this->isVideoFile($videoUrl, $extensions)) {
                    $filePath = $this->getLocalFilePath($videoUrl);
                    if (file_exists($filePath)) {
                        // 获取文件的上传时间（文件创建时间）
                        $fileUploadTime = filectime($filePath);

                        // 如果指定了日期范围，检查文件上传时间是否在范围内
                        if (!empty($startDate) && $fileUploadTime < strtotime($startDate)) {
                            continue;
                        }
                        if (!empty($endDate) && $fileUploadTime > strtotime($endDate)) {
                            continue;
                        }

                        $totalFiles++;
                        $fileSize = filesize($filePath);
                        $totalSize += $fileSize;
                        $fileList[] = [
                            'path' => $videoUrl,
                            'size' => $fileSize,
                            'formatted_size' => $this->formatFileSize($fileSize),
                            'upload_time' => $fileUploadTime,
                            'upload_time_formatted' => date('Y-m-d H:i:s', $fileUploadTime)
                        ];
                    }
                }
            }
        }
    }

    // 处理富文本中的视频内容
    private function processRichTextVideos($description, $extensions, &$deletedFiles, &$totalFiles, &$failedFiles, &$skippedFiles, $minSize, $maxSize, $startDate = '', $endDate = '')
    {
        // 匹配富文本编辑器中的视频标签
        $pattern = '/<div[^>]*data-w-e-type="video"[^>]*>.*?<video[^>]*>.*?<source[^>]*src="([^"]+)"[^>]*\/>.*?<\/video>.*?<\/div>/is';

        $newDescription = preg_replace_callback($pattern, function($matches) use ($extensions, &$deletedFiles, &$totalFiles, &$failedFiles, &$skippedFiles, $minSize, $maxSize, $startDate, $endDate) {
            $videoUrl = $matches[1];
            $totalFiles++;

            if ($this->isVideoFile($videoUrl, $extensions)) {
                $filePath = $this->getLocalFilePath($videoUrl);

                // 检查文件是否存在
                if (file_exists($filePath)) {
                    // 检查文件上传时间是否在指定范围内
                    if (!empty($startDate) || !empty($endDate)) {
                        $fileUploadTime = filectime($filePath);

                        if (!empty($startDate) && $fileUploadTime < strtotime($startDate)) {
                            $skippedFiles[] = $videoUrl . " (上传时间早于指定开始时间)";
                            return $matches[0]; // 保留原内容
                        }
                        if (!empty($endDate) && $fileUploadTime > strtotime($endDate)) {
                            $skippedFiles[] = $videoUrl . " (上传时间晚于指定结束时间)";
                            return $matches[0]; // 保留原内容
                        }
                    }

                    // 检查文件大小
                    if ($this->checkFileSize($videoUrl, $minSize, $maxSize)) {
                        // 尝试删除视频文件
                        if ($this->deleteVideoFile($videoUrl, $deletedFiles, $failedFiles)) {
                            // 如果删除成功，返回空字符串（移除整个视频标签）
                            return '';
                        }
                    } else {
                        $skippedFiles[] = $videoUrl . " (文件大小不符合条件)";
                    }
                } else {
                    $skippedFiles[] = $videoUrl . " (文件不存在)";
                }
            }

            // 如果删除失败或不是目标视频文件，保留原内容
            return $matches[0];
        }, $description);

        // 清理可能产生的多余空行和段落
        $newDescription = preg_replace('/<p><br><\/p>\s*<p><br><\/p>/', '<p><br></p>', $newDescription);
        $newDescription = preg_replace('/(<p><br><\/p>\s*){2,}/', '<p><br></p>', $newDescription);

        return $newDescription;
    }

    // 检查是否为视频文件
    private function isVideoFile($filePath, $extensions)
    {
        $extArray = explode(',', $extensions);
        $fileExt = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));

        foreach ($extArray as $ext) {
            if (trim(strtolower($ext)) === $fileExt) {
                return true;
            }
        }

        return false;
    }

    // 检查文件大小是否符合条件
    private function checkFileSize($filePath, $minSize, $maxSize)
    {
        $localPath = $this->getLocalFilePath($filePath);
        if (!file_exists($localPath)) {
            return false;
        }

        $fileSize = filesize($localPath) / (1024 * 1024); // 转换为MB

        // 检查最小大小
        if ($minSize > 0 && $fileSize < $minSize) {
            return false;
        }

        // 检查最大大小
        if ($maxSize > 0 && $fileSize > $maxSize) {
            return false;
        }

        return true;
    }

    // 删除视频文件
    private function deleteVideoFile($videoPath, &$deletedFiles, &$failedFiles, $recordId = null)
    {
        try {
            // 从URL中提取相对路径
            $relativePath = parse_url($videoPath, PHP_URL_PATH);
            if (empty($relativePath)) {
                $failedFiles[] = $videoPath;
                error_log("Clearmp4: 无效的视频URL: " . $videoPath);
                return false;
            }

            $filePath = public_path() . ltrim($relativePath, '/');

            // 确保路径是文件而不是目录
            if (is_dir($filePath)) {
                $failedFiles[] = $videoPath;
                error_log("Clearmp4: 路径指向目录而不是文件: " . $filePath);
                return false;
            }

            if (file_exists($filePath) && is_file($filePath)) {
                if (@unlink($filePath)) {
                    $deletedFiles++;
                    error_log("Clearmp4: 成功删除视频文件: " . $filePath . ($recordId ? " (记录ID: $recordId)" : ""));

                    // 尝试删除空文件夹
                    $dirPath = dirname($filePath);
                    if (is_dir($dirPath) && count(scandir($dirPath)) <= 2) {
                        @rmdir($dirPath);
                        error_log("Clearmp4: 删除空文件夹: " . $dirPath);
                    }
                    return true;
                } else {
                    $failedFiles[] = $videoPath;
                    error_log("Clearmp4: 删除视频文件失败: " . $filePath);
                    return false;
                }
            } else {
                $failedFiles[] = $videoPath;
                error_log("Clearmp4: 视频文件不存在或不是有效文件: " . $filePath);
                return false;
            }
        } catch (\Exception $e) {
            $failedFiles[] = $videoPath;
            error_log("Clearmp4: 删除视频文件时发生错误: " . $e->getMessage());
            return false;
        }
    }

    // 更新数据库记录，清空已删除的视频文件路径
    private function updateDatabaseRecords($records, $extensions)
    {
        foreach ($records as $record) {
            $updateData = [];

            // 处理投诉视频字段
            if (!empty($record['images'])) {
                $imageArray = json_decode($record['images'], true);
                if (!empty($imageArray)) {
                    $filteredImages = [];
                    foreach ($imageArray as $file) {
                        // 如果不是视频文件或文件仍然存在，则保留
                        if (!$this->isVideoFile($file, $extensions) || file_exists($this->getLocalFilePath($file))) {
                            $filteredImages[] = $file;
                        }
                    }
                    $updateData['images'] = empty($filteredImages) ? '' : json_encode($filteredImages);
                }
            }

            // 处理收藏视频字段
            if (!empty($record['collect_image']) && $this->isVideoFile($record['collect_image'], $extensions)) {
                // 如果文件已被删除，清空字段
                if (!file_exists($this->getLocalFilePath($record['collect_image']))) {
                    $updateData['collect_image'] = '';
                }
            }

            // 如果有需要更新的数据，则更新记录
            if (!empty($updateData)) {
                Db::name('complaint')->where('id', $record['id'])->update($updateData);
            }
        }
    }

    // 获取本地文件路径
    private function getLocalFilePath($url)
    {
        $relativePath = parse_url($url, PHP_URL_PATH);
        return public_path() . ltrim($relativePath, '/');
    }

    // 格式化文件大小
    private function formatFileSize($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }

    // 获取配置数据
    public function fetchData()
    {
        try {
            $data = [
                'video_status' => intval(plugconf("Clearmp4.video_status") ?? 0),
                'video_days' => intval(plugconf("Clearmp4.video_days") ?? 30),
                'video_execute_time' => plugconf("Clearmp4.video_execute_time") ?? '00:00',
                'video_interval' => intval(plugconf("Clearmp4.video_interval") ?? 1),
                'video_extensions' => plugconf("Clearmp4.video_extensions") ?? 'mp4,avi,mov,wmv,flv,mkv,webm,m4v',
                'video_max_size' => floatval(plugconf("Clearmp4.video_max_size") ?? 100),
                'video_min_size' => floatval(plugconf("Clearmp4.video_min_size") ?? 0),
            ];

            return json(['code' => 1, 'msg' => '获取成功', 'data' => $data]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取配置失败: ' . $e->getMessage()]);
        }
    }

    // 保存视频清理配置
    public function saveVideoConfig()
    {
        try {
            $status = $this->request->post('status/d', 0);
            $days = $this->request->post('days/d', 30);
            $executeTime = $this->request->post('executeTime', '00:00');
            $interval = $this->request->post('interval/d', 1);
            $extensions = $this->request->post('extensions', 'mp4,avi,mov,wmv,flv,mkv,webm,m4v');
            $maxSize = $this->request->post('maxSize/f', 100);
            $minSize = $this->request->post('minSize/f', 0);

            // 验证时间格式
            if (!preg_match('/^([01][0-9]|2[0-3]):([0-5][0-9])$/', $executeTime)) {
                return json(['code' => 0, 'msg' => '执行时间格式必须为 HH:mm']);
            }

            // 验证其他参数
            if ($days < 1 || $days > 365) {
                return json(['code' => 0, 'msg' => '清理天数必须在1-365之间']);
            }
            if ($interval < 0 || $interval > 30) {
                return json(['code' => 0, 'msg' => '执行间隔必须在0-30之间']);
            }
            if ($maxSize < 0 || $maxSize > 10240) {
                return json(['code' => 0, 'msg' => '最大文件大小必须在0-10240MB之间']);
            }
            if ($minSize < 0 || $minSize >= $maxSize) {
                return json(['code' => 0, 'msg' => '最小文件大小必须小于最大文件大小']);
            }

            // 验证扩展名格式
            if (empty($extensions)) {
                return json(['code' => 0, 'msg' => '视频扩展名不能为空']);
            }

            // 保存配置
            plugconf_set("Clearmp4.video_status", $status);
            plugconf_set("Clearmp4.video_days", $days);
            plugconf_set("Clearmp4.video_execute_time", $executeTime);
            plugconf_set("Clearmp4.video_interval", $interval);
            plugconf_set("Clearmp4.video_extensions", $extensions);
            plugconf_set("Clearmp4.video_max_size", $maxSize);
            plugconf_set("Clearmp4.video_min_size", $minSize);

            return json(['code' => 1, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '保存失败: ' . $e->getMessage()]);
        }
    }

    // 清理过期投诉记录及其视频文件
    public function clearExpiredComplaints()
    {
        try {
            $userId = $this->request->post('user_id/d', 0);
            $startDate = $this->request->post('start_date', '');
            $endDate = $this->request->post('end_date', '');
            $extensions = $this->request->post('extensions', '');
            $currentTime = time();

            // 如果没有指定扩展名，使用默认配置
            if (empty($extensions)) {
                $extensions = plugconf("Clearmp4.video_extensions") ?? "mp4,avi,mov,wmv,flv,mkv,webm,m4v";
            }

            // 构建查询条件
            $query = Db::name('complaint')
                ->where('expire_time', '<=', $currentTime);

            if ($userId > 0) {
                $query = $query->where('user_id', $userId);
            }

            // 添加日期范围条件
            if (!empty($startDate)) {
                $query = $query->where('create_time', '>=', strtotime($startDate));
            }
            if (!empty($endDate)) {
                $query = $query->where('create_time', '<=', strtotime($endDate . ' 23:59:59'));
            }

            // 获取要清理的记录数
            $beforeCount = $query->count();

            if ($beforeCount == 0) {
                $msg = $userId > 0
                    ? "该用户暂无可清理的过期投诉记录"
                    : "暂无可清理的过期投诉记录";
                return json(['code' => 200, 'msg' => $msg]);
            }

            // 先获取所有相关视频文件
            $records = $query->select()->toArray();

            $deletedFiles = 0;
            $totalFiles = 0;
            $failedFiles = [];

            // 处理视频文件
            foreach ($records as $record) {
                // 处理投诉视频
                if (!empty($record['images'])) {
                    $imageArray = json_decode($record['images'], true);
                    if (!empty($imageArray)) {
                        foreach ($imageArray as $file) {
                            if ($this->isVideoFile($file, $extensions)) {
                                $totalFiles++;
                                $this->deleteVideoFile($file, $deletedFiles, $failedFiles, $record['id']);
                            }
                        }
                    }
                }

                // 处理收藏视频
                if (!empty($record['collect_image']) && $this->isVideoFile($record['collect_image'], $extensions)) {
                    $totalFiles++;
                    $this->deleteVideoFile($record['collect_image'], $deletedFiles, $failedFiles, $record['id']);
                }
            }

            // 删除记录
            $deleted = $query->delete();

            if ($deleted === false) {
                throw new \Exception('删除记录失败');
            }

            $message = sprintf(
                "%s清理完成！共删除 %d 条过期投诉记录，处理 %d 个视频文件，成功删除 %d 个视频文件",
                $userId > 0 ? "用户过期投诉" : "所有过期投诉",
                $beforeCount,
                $totalFiles,
                $deletedFiles
            );

            if (!empty($failedFiles)) {
                $message .= "\n以下视频文件未能成功删除：\n" . implode("\n", $failedFiles);
            }

            // 记录日志
            error_log("Clearmp4: " . $message);

            return json([
                'code' => 200,
                'msg' => $message,
                'data' => [
                    'deleted_count' => $beforeCount,
                    'total_files' => $totalFiles,
                    'deleted_files' => $deletedFiles,
                    'failed_files' => $failedFiles
                ]
            ]);

        } catch (\Exception $e) {
            error_log("Clearmp4: 清理过期投诉记录失败: " . $e->getMessage());
            return json(['code' => 500, 'msg' => '清理失败: ' . $e->getMessage()]);
        }
    }
}
