<?php
namespace plugin\Paymenticon\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;
use think\facade\Request;
use think\response\Json;

class Api extends BasePlugin
{
    protected $scene = ['admin']; // 仅管理员可访问

    protected $noNeedLogin = []; // index方法无需鉴权

    // 显示 API 页面
    public function index()
    {
        return View::fetch();
    }

    // 查询 channel_paytype 表
    public function getPaymentIcons(): Json
    {
        try {
            $page = request()->param('page', 1);
            $limit = request()->param('limit', 10);
            $search = request()->param('search', '');
            
            $query = Db::name('channel_paytype');
            
            // 如果有搜索关键词
            if (!empty($search)) {
                $query->where('name', 'like', "%{$search}%");
            }
            
            // 获取总数
            $total = $query->count();
            
            // 获取分页数据
            $list = $query->page($page, $limit)
                ->order('id', 'desc')
                ->select()
                ->each(function($item) {
                    // 确保图标路径是完整的URL
                    $item['icon'] = $item['icon'];
                    return $item;
                });

            return json([
                'code' => 200, 
                'msg' => '获取成功',
                'data' => [
                    'list' => $list,
                    'total' => $total
                ]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '错误: ' . $e->getMessage()]);
        }
    }

    // 插入支付图标
    public function insertPaymentIcon(): Json
    {
        try {
            $params = request()->post();
            
            // 参数验证
            if (empty($params['name']) || empty($params['icon'])) {
                return json(['code' => 400, 'msg' => '支付名称和图标不能为空']);
            }

            // 插入数据
            $result = Db::name('channel_paytype')->insert([
                'name' => $params['name'],
                'icon' => $params['icon']  // 直接使用完整的图片URL
            ]);

            if ($result) {
                return json(['code' => 200, 'msg' => '添加成功']);
            }
            return json(['code' => 500, 'msg' => '添加失败']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '操作失败：' . $e->getMessage()]);
        }
    }

    // 编辑支付图标
    public function editPaymentIcon(): Json
    {
        try {
            $params = request()->post();
            
            if (empty($params['id']) || empty($params['name']) || empty($params['icon'])) {
                return json(['code' => 400, 'msg' => '参数不完整']);
            }

            $result = Db::name('channel_paytype')
                ->where('id', $params['id'])
                ->update([
                    'name' => $params['name'],
                    'icon' => $params['icon']  // 直接使用完整的图片URL
                ]);

            if ($result !== false) {
                return json(['code' => 200, 'msg' => '更新成功']);
            }
            return json(['code' => 500, 'msg' => '更新失败']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '操作失败：' . $e->getMessage()]);
        }
    }

    // 删除支付图标
    public function deletePaymentIcon(): Json
    {
        try {
            $id = request()->post('id');
            
            if (empty($id)) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }

            // 查找记录
            $icon = Db::name('channel_paytype')->where('id', $id)->find();
            if (!$icon) {
                return json(['code' => 404, 'msg' => '记录不存在']);
            }

            // 执行删除
            $result = Db::name('channel_paytype')->where('id', $id)->delete();
            
            if ($result) {
                return json(['code' => 200, 'msg' => '删除成功']);
            }
            return json(['code' => 500, 'msg' => '删除失败']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }
} 