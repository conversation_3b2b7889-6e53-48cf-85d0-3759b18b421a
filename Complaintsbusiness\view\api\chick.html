<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>插件配置</title>
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <style>
        .container {
            padding: 20px;
        }
    </style>
</head>

<body>
    <div id="app" class="container">
        <el-form label-width="120px">
            <el-form-item label="基础设置">
                <div style="display: flex; gap: 12px;">
                    <el-button 
                        type="primary" 
                        :loading="loading.nav"
                        @click="toggleNav">
                        {{ navExists ? '从导航栏移除' : '添加到导航栏' }}
                    </el-button>
                    
                    <el-button 
                        type="warning" 
                        :loading="loading.cache"
                        @click="clearCache">
                        清除缓存
                    </el-button>
                </div>
            </el-form-item>
        </el-form>
    </div>

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>

    <script>
        const { createApp, ref, reactive, onMounted } = Vue;

        createApp({
            setup() {
                const navExists = ref(false);
                const loading = reactive({
                    nav: false,
                    cache: false
                });

                const checkNavStatus = async () => {
                    try {
                        const response = await axios.get('/plugin/Complaintsbusiness/Api/checkNav');
                        navExists.value = response.data.exists;
                    } catch (error) {
                        ElMessage.error('获取导航状态失败');
                    }
                };

                const toggleNav = async () => {
                    loading.nav = true;
                    try {
                        const response = await axios.post('/plugin/Complaintsbusiness/Api/toggleNav');
                        if (response.data.code === 1) {
                            navExists.value = !navExists.value;
                            ElMessage.success(response.data.msg);
                        } else {
                            ElMessage.error(response.data.msg);
                        }
                    } catch (error) {
                        ElMessage.error('操作失败');
                    } finally {
                        loading.nav = false;
                    }
                };

                const clearCache = async () => {
                    loading.cache = true;
                    try {
                        const response = await axios.post('/plugin/Complaintsbusiness/Api/clearCache');
                        if (response.data.code === 1) {
                            ElMessage.success(response.data.msg);
                        } else {
                            ElMessage.error(response.data.msg);
                        }
                    } catch (error) {
                        ElMessage.error('清除缓存失败');
                    } finally {
                        loading.cache = false;
                    }
                };

                onMounted(() => {
                    checkNavStatus();
                });

                return {
                    navExists,
                    loading,
                    toggleNav,
                    clearCache
                };
            }
        }).use(ElementPlus).mount("#app");
    </script>
</body>
</html>
