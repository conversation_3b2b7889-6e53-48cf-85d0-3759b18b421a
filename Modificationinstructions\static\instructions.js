(function() {
    'use strict';

    // 获取当前商店信息
    function getShopInfo() {
        let shopName = '';
        let merchantId = '';
        
        // 尝试从URL中获取商店信息
        const pathSegments = window.location.pathname.split('/');
        if (pathSegments.length > 2) {
            shopName = pathSegments[1];
        }
        
        // 尝试从cookie中获取商户ID
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.startsWith('user_id=')) {
                merchantId = cookie.substring('user_id='.length);
                break;
            }
        }
        
        return { shopName, merchantId };
    }

    // 查找商品使用说明元素
    function findInstructionsElement() {
        // 查找包含使用说明的div容器
        const containers = document.querySelectorAll('.content[data-v-6fe57b98]');
        
        for (const container of containers) {
            // 检查是否包含w-e-text-container和内部的p标签
            const textContainer = container.querySelector('.w-e-text-container[data-slate-editor="true"]');
            if (textContainer) {
                return textContainer;
            }
        }
        
        return null;
    }

    // 获取上级商品使用说明
    function fetchParentInstructions() {
        const { shopName, merchantId } = getShopInfo();
        const timestamp = new Date().getTime(); // 防止缓存
        
        // 从URL中获取当前的goods_id
        let goodsId = null;
        const urlParams = new URLSearchParams(window.location.search);
        goodsId = urlParams.get('id') || urlParams.get('goods_id');
        
        // 如果没有找到商品ID，尝试从路径获取
        if (!goodsId) {
            const pathMatch = window.location.pathname.match(/\/product\/(\d+)/);
            if (pathMatch && pathMatch[1]) {
                goodsId = pathMatch[1];
            }
        }
        
        if (!goodsId) {
            console.error('未找到商品ID，无法获取上级使用说明');
            return Promise.reject('未找到商品ID');
        }
        
        // 构建API请求URL
        let apiUrl = '/plugin/Modificationinstructions/Api/getGoodsInstructions';
        
        // 添加参数
        const params = new URLSearchParams();
        params.append('goods_id', goodsId);
        if (shopName) params.append('shop_name', shopName);
        if (merchantId) params.append('merchant_id', merchantId);
        params.append('t', timestamp);
        
        // 组合最终URL
        apiUrl = apiUrl + '?' + params.toString();
        
        // 发送请求
        return fetch(apiUrl)
            .then(response => response.json())
            .then(data => {
                if (data.code === 200 && data.data) {
                    return data.data.instructions || '';
                }
                return '';
            })
            .catch(error => {
                console.error('获取上级商品使用说明失败:', error);
                return '';
            });
    }

    // 替换使用说明内容
    function replaceInstructions(container, instructions) {
        if (!container || !instructions) return;
        
        // 如果是纯文本，用p标签包装
        if (!instructions.trim().startsWith('<')) {
            instructions = `<p>${instructions}</p>`;
        }
        
        // 设置内容
        container.innerHTML = instructions;
    }

    // 初始化函数
    function init() {
        // 查找使用说明容器
        const instructionsElement = findInstructionsElement();
        if (!instructionsElement) {
            console.log('未找到使用说明容器');
            
            // 设置定时检查，因为可能容器是动态加载的
            setTimeout(init, 1000);
            return;
        }
        
        // 获取上级商品使用说明
        fetchParentInstructions().then(instructions => {
            if (instructions) {
                // 替换内容
                replaceInstructions(instructionsElement, instructions);
            }
        });
        
        // 监听DOM变化，处理动态加载的内容
        const observer = new MutationObserver((mutations) => {
            for (const mutation of mutations) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // 如果页面中新增了节点，尝试再次查找使用说明容器
                    const container = findInstructionsElement();
                    if (container) {
                        fetchParentInstructions().then(instructions => {
                            if (instructions) {
                                replaceInstructions(container, instructions);
                            }
                        });
                    }
                }
            }
        });
        
        // 开始观察整个文档的变化
        observer.observe(document.body, { childList: true, subtree: true });
    }

    // 当DOM加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
})(); 