(function() {
    // 更强的防重复执行机制
    const PLUGIN_ID = 'tderciyuan_live2d_' + Date.now();
    if (window.tderciyuanLive2DLoaded) {
        console.log('Tderciyuan Live2D已加载，跳过重复执行');
        return;
    }
    window.tderciyuanLive2DLoaded = true;
    window.tderciyuanPluginId = PLUGIN_ID;

    // 定义全局baseUrl变量
    const baseUrl = window.location.protocol + '//' + window.location.host;

    // 增强的清理Live2D相关元素的函数
    function cleanupLive2D() {
        try {
            console.log('开始清理Live2D元素...');

            // 清理所有可能的Live2D元素
            const selectors = [
                '.live2d-container',
                '#live2dcanvas',
                '#live2d-widget',
                '.live2d-widget',
                '[id*="live2d"]',
                '[class*="live2d"]',
                '.waifu',
                '.waifu-tips',
                '.waifu-custom',
                '.live2d-tip',
                '.live2d-tool',
                '.live2d-toggle'
            ];

            let removedCount = 0;
            selectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    if (element && element.parentNode) {
                        try {
                            // 检查是否是我们创建的元素
                            if (!element.dataset.tderciyuanSetup || element.dataset.tderciyuanSetup === 'true') {
                                element.parentNode.removeChild(element);
                                removedCount++;
                            }
                        } catch (e) {
                            // 静默处理移除错误
                        }
                    }
                });
            });

            console.log(`清理了 ${removedCount} 个Live2D相关元素`);

            // 清理全局变量
            if (window.L2Dwidget) {
                try {
                    if (typeof window.L2Dwidget.clear === 'function') {
                        window.L2Dwidget.clear();
                        console.log('已清理L2Dwidget');
                    }
                    if (typeof window.L2Dwidget.init === 'function') {
                        // 重置初始化状态
                        window.L2Dwidget._initialized = false;
                    }
                } catch (e) {
                    console.log('清理L2Dwidget时出错:', e);
                }
            }

            // 清理更多可能的全局变量
            const globalVarsToClean = [
                'live2d_settings',
                'live2d_path',
                'waifu_tips',
                'live2dWidget',
                'Live2D'
            ];

            globalVarsToClean.forEach(varName => {
                if (window[varName]) {
                    try {
                        delete window[varName];
                        console.log(`已清理全局变量: ${varName}`);
                    } catch (e) {
                        window[varName] = undefined;
                    }
                }
            });

            // 重置我们的全局标志
            window.live2dRunning = false;
            window.tderciyuanWaifuCreated = false;

            console.log('Live2D清理完成');

        } catch (e) {
            console.error('清理Live2D元素时出错:', e);
        }
    }

    function initLive2D() {
        // 获取当前页面的商家信息
        let shopName = '';
        let merchantId = '';

        // 从各种可能的位置获取商家信息
        const nicknameElement = document.querySelector('.nickname');
        if (nicknameElement) {
            shopName = nicknameElement.textContent.trim();
        }

        if (!shopName) {
            const pageTitle = document.title.trim();
            if (pageTitle) {
                const index = pageTitle.indexOf('的小店');
                if (index !== -1) {
                    shopName = pageTitle.substring(0, index);
                }
            }
        }

        if (!shopName) {
            const shopInfoScript = document.querySelector('script[data-shop-info]');
            if (shopInfoScript) {
                try {
                    const shopInfo = JSON.parse(shopInfoScript.textContent);
                    shopName = shopInfo.shopName;
                    merchantId = shopInfo.merchantId;
                } catch (e) {
                    console.error('解析商家信息失败:', e);
                }
            }
        }

        // 如果获取不到商家信息，仍然尝试获取默认配置
        if (!shopName && !merchantId) {
            console.log('未找到商家信息，尝试获取默认配置');
        }

        // 构建请求参数
        const params = new URLSearchParams();
        if (shopName) params.append('shop_name', shopName);
        if (merchantId) params.append('merchant_id', merchantId);

        // 如果没有任何参数，添加一个默认参数以确保请求正常
        if (!shopName && !merchantId) {
            params.append('use_default', '1');
        }

        // 发送请求获取Live2D配置
        fetch(baseUrl + '/plugin/Tderciyuan/api/fetchData?' + params.toString(), {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.code === 200 && data.data && data.data.status === 1) {
                createLive2D(data.data);
            } else {
                console.log('Live2D未启用或配置无效');
                // 不创建Live2D，因为状态为0或配置无效
            }
        })
        .catch(error => {
            console.error('获取Live2D配置失败:', error);
            // 不创建Live2D，避免在错误情况下显示
            console.log('由于配置获取失败，不显示Live2D');
        });
    }

    function createLive2D(config) {
        try {
            console.log('开始创建Live2D，插件ID:', window.tderciyuanPluginId);

            // 检查是否已经有Live2D在运行
            if (window.live2dRunning) {
                console.log('Live2D已在运行，跳过重复初始化');
                return;
            }

            // 彻底清理所有Live2D相关元素
            cleanupLive2D();

            // 等待一小段时间确保清理完成
            setTimeout(() => {
                continueCreateLive2D(config);
            }, 100);

        } catch (error) {
            console.error('创建Live2D失败:', error);
            window.live2dRunning = false;
            window.tderciyuanWaifuCreated = false;
        }
    }

    function continueCreateLive2D(config) {
        try {
            // 再次检查页面是否已经有看板娘元素
            const existingWaifu = document.querySelector('.waifu') ||
                                document.querySelector('.waifu-custom') ||
                                document.querySelector('#live2d-widget') ||
                                document.querySelector('.live2d-widget');

            if (existingWaifu && !existingWaifu.dataset.tderciyuanSetup) {
                console.log('检测到页面已有看板娘，为其添加控制功能');
                // 如果是 .waifu 元素，说明是 live2d-widgets
                if (existingWaifu.classList.contains('waifu')) {
                    setupLive2DWidgetsControls(existingWaifu, config);
                }
                return;
            }

            // 标记Live2D正在运行
            window.live2dRunning = true;

            // 防止重复创建的全局标志
            window.tderciyuanWaifuCreated = false;

            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

            // 获取配置
            let { model_type = 'auto', position = 'right', width = 200, height = 300, display_mode = 'normal' } = config;

            console.log('Live2D配置 - 模型类型:', model_type, '位置:', position, '尺寸:', width + 'x' + height, '显示模式:', display_mode);

            // 如果是 auto 模式，优先使用 live2d-widgets
            if (model_type === 'auto') {
                console.log('使用自动加载模式，优先尝试 live2d-widgets');
            }

            // 根据模型类型选择加载方式
            if (model_type === 'auto') {
                // auto 模式：使用 live2d-widgets
                console.log('auto模式：使用live2d-widgets自动加载');

                // 检查是否已经有加载中的脚本
                const existingScript = document.querySelector('script[src*="live2d-widgets"]');
                if (existingScript) {
                    console.log('检测到已有live2d-widgets脚本，等待其加载完成');
                    waitForExistingLive2D(config);
                    return;
                }

                // 尝试加载 live2d-widgets 自动加载脚本
                loadLive2DWidgets(config);
            } else {
                // 其他模式：直接使用自定义看板娘
                console.log('使用自定义看板娘模式，模型类型:', model_type);
                createCustomWaifu(config);
            }

        } catch (error) {
            console.error('继续创建Live2D失败:', error);
            window.live2dRunning = false;
            window.tderciyuanWaifuCreated = false;
        }
    }

    // 等待现有的live2d-widgets脚本加载
    function waitForExistingLive2D(config) {
        console.log('等待现有live2d-widgets脚本加载...');

        // 使用MutationObserver监听.waifu元素的出现
        let isDetected = false;
        const observer = new MutationObserver((mutations) => {
            if (isDetected) return;

            for (let mutation of mutations) {
                if (mutation.type === 'childList') {
                    for (let node of mutation.addedNodes) {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            const waifuElement = node.classList?.contains('waifu') ? node : node.querySelector?.('.waifu');
                            if (waifuElement) {
                                console.log('检测到live2d-widgets看板娘已加载');
                                isDetected = true;
                                observer.disconnect();
                                setupLive2DWidgetsControls(waifuElement, config);
                                return;
                            }
                        }
                    }
                }
            }
        });

        observer.observe(document.body, { childList: true, subtree: true });

        // 10秒后如果还没检测到，使用自定义看板娘
        setTimeout(() => {
            if (!isDetected) {
                console.log('live2d-widgets加载超时，使用自定义看板娘');
                observer.disconnect();
                createCustomWaifu(config);
            }
        }, 10000);
    }

    // 加载live2d-widgets脚本
    function loadLive2DWidgets(config) {
        // 检查是否已经有相同的脚本在加载
        const existingScript = document.querySelector('script[src*="live2d-widgets"]');
        if (existingScript) {
            console.log('检测到已有live2d-widgets脚本，等待其加载完成');
            waitForExistingLive2D(config);
            return;
        }

        const script = document.createElement('script');
        script.src = 'https://fastly.jsdelivr.net/npm/live2d-widgets@1.0.0-rc.4/dist/autoload.js';
        script.dataset.tderciyuanScript = 'true'; // 标记这是我们加载的脚本

        let isLoaded = false;

        script.onload = function() {
            console.log('live2d-widgets脚本加载成功');
            isLoaded = true;

            // 等待看板娘出现
            waitForLive2DWidgets(config);
        };

        script.onerror = function() {
            console.log('live2d-widgets脚本加载失败，使用自定义看板娘');
            if (!isLoaded) {
                createCustomWaifu(config);
            }
        };

        document.head.appendChild(script);

        // 15秒超时保护
        setTimeout(() => {
            if (!isLoaded) {
                console.log('live2d-widgets脚本加载超时，使用自定义看板娘');
                createCustomWaifu(config);
            }
        }, 15000);
    }

    // 等待live2d-widgets看板娘出现
    function waitForLive2DWidgets(config) {
        let checkCount = 0;
        const maxChecks = 10;
        let isSetupComplete = false;

        const checkForWaifu = () => {
            // 防止重复执行
            if (isSetupComplete) {
                console.log('看板娘已设置完成，跳过重复检查');
                return;
            }

            checkCount++;
            console.log(`第${checkCount}次检查live2d-widgets看板娘...`);

            // 检查是否已经有任何看板娘元素
            const existingWaifu = document.querySelector('.waifu') ||
                                document.querySelector('.waifu-custom') ||
                                document.querySelector('#live2d-widget') ||
                                document.querySelector('.live2d-widget');

            if (existingWaifu) {
                isSetupComplete = true;
                if (existingWaifu.classList.contains('waifu')) {
                    console.log('live2d-widgets看板娘已成功加载');
                    setupLive2DWidgetsControls(existingWaifu, config);
                } else {
                    console.log('检测到其他看板娘元素，跳过设置');
                }
            } else if (checkCount < maxChecks) {
                setTimeout(checkForWaifu, 2000);
            } else {
                console.log('live2d-widgets看板娘未能加载，使用自定义看板娘');
                isSetupComplete = true;
                createCustomWaifu(config);
            }
        };

        // 2秒后开始检查
        setTimeout(checkForWaifu, 2000);
    }

    // 为live2d-widgets看板娘设置控制功能
    function setupLive2DWidgetsControls(waifuElement, config) {
        const { position = 'right', width = 200, height = 300, display_mode = 'normal' } = config;
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

        console.log('为live2d-widgets看板娘设置控制功能，显示模式:', display_mode);

        // 检查全局标志，防止重复设置
        if (window.tderciyuanWaifuCreated) {
            console.log('看板娘已创建，跳过重复设置');
            return;
        }

        // 检查是否已经设置过控制功能
        if (waifuElement.dataset.tderciyuanSetup === 'true') {
            console.log('看板娘控制功能已设置，跳过重复设置');
            return;
        }

        // 检查元素是否还在DOM中
        if (!document.contains(waifuElement)) {
            console.log('看板娘元素已不在DOM中，跳过设置');
            return;
        }

        // 标记已设置
        waifuElement.dataset.tderciyuanSetup = 'true';
        waifuElement.dataset.tderciyuanPluginId = window.tderciyuanPluginId;
        window.tderciyuanWaifuCreated = true;

        // 调整位置
        waifuElement.style.cssText = `
            position: fixed !important;
            ${position === 'right' ? 'right' : 'left'}: 20px !important;
            bottom: 20px !important;
            z-index: 999 !important;
        `;

        // 如果是纯净模式，只调整位置，不添加控制按钮
        if (display_mode === 'pure') {
            console.log('纯净模式：只显示看板娘，不添加控制按钮');
            return;
        }

        // 创建关闭按钮
        const closeBtn = document.createElement('div');
        closeBtn.innerHTML = '×';
        closeBtn.style.cssText = `
            position: fixed;
            ${position === 'right' ? 'right' : 'left'}: 30px;
            bottom: ${height + 40}px;
            width: ${isMobile ? '32px' : '28px'};
            height: ${isMobile ? '32px' : '28px'};
            background: rgba(0,0,0,0.6);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: ${isMobile ? '24px' : '20px'};
            z-index: 10000;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.2s ease, visibility 0.2s ease;
            pointer-events: auto;
        `;

        document.body.appendChild(closeBtn);

        // 添加悬停效果
        waifuElement.addEventListener('mouseenter', () => {
            closeBtn.style.opacity = '1';
            closeBtn.style.visibility = 'visible';
        });

        waifuElement.addEventListener('mouseleave', () => {
            closeBtn.style.opacity = '0';
            closeBtn.style.visibility = 'hidden';
        });

        // 关闭按钮事件
        closeBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            waifuElement.style.display = 'none';
            closeBtn.style.display = 'none';
            localStorage.setItem('live2d_closed', Date.now());
            window.live2dRunning = false;
            window.tderciyuanWaifuCreated = false;
            console.log('live2d-widgets看板娘已关闭');
        });

        // 移动设备支持
        if (isMobile) {
            waifuElement.addEventListener('touchstart', () => {
                closeBtn.style.opacity = '1';
                closeBtn.style.visibility = 'visible';
                setTimeout(() => {
                    closeBtn.style.opacity = '0';
                    closeBtn.style.visibility = 'hidden';
                }, 3000);
            });
        }
    }

    // 创建自定义看板娘
    function createCustomWaifu(config) {
        const { model_type = 'auto', position = 'right', width = 200, height = 300, display_mode = 'normal' } = config;
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

        console.log('正在创建自定义看板娘，显示模式:', display_mode);

        // 检查全局标志，防止重复创建
        if (window.tderciyuanWaifuCreated) {
            console.log('看板娘已创建，跳过重复创建');
            return;
        }

        // 检查是否已经有我们创建的看板娘元素
        const existingTderciyuanWaifu = document.querySelector(`[data-tderciyuan-plugin-id="${window.tderciyuanPluginId}"]`);
        if (existingTderciyuanWaifu) {
            console.log('检测到已有本插件创建的看板娘，跳过重复创建');
            window.tderciyuanWaifuCreated = true;
            return;
        }

        // 检查是否已经有其他看板娘元素
        const existingWaifu = document.querySelector('.waifu') ||
                            document.querySelector('.waifu-custom') ||
                            document.querySelector('#live2d-widget') ||
                            document.querySelector('.live2d-widget');

        if (existingWaifu && !existingWaifu.dataset.tderciyuanSetup) {
            console.log('检测到页面已有其他看板娘元素，跳过创建自定义看板娘');
            window.tderciyuanWaifuCreated = true;
            return;
        }

        // 标记正在创建
        window.tderciyuanWaifuCreated = true;

        // 如果是纯净模式，不创建关闭按钮
        let closeBtn = null;
        if (display_mode !== 'pure') {

            // 创建关闭按钮
            closeBtn = document.createElement('div');
            closeBtn.innerHTML = '×';
            closeBtn.style.cssText = `
                position: fixed;
                ${position === 'right' ? 'right' : 'left'}: 30px;
                bottom: ${height + 40}px;
                width: ${isMobile ? '32px' : '28px'};
                height: ${isMobile ? '32px' : '28px'};
                background: rgba(0,0,0,0.6);
                color: white;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                font-size: ${isMobile ? '24px' : '20px'};
                z-index: 10000;
                opacity: 0;
                visibility: hidden;
                transition: opacity 0.2s ease, visibility 0.2s ease;
                pointer-events: auto;
            `;

            document.body.appendChild(closeBtn);
        }

        // 创建美观的自定义看板娘
        const customWaifu = document.createElement('div');
        customWaifu.className = 'waifu-custom';
        customWaifu.dataset.tderciyuanSetup = 'true';
        customWaifu.dataset.tderciyuanPluginId = window.tderciyuanPluginId;

        // 根据模型类型选择不同的样式和图标
        let waifuContent = '';
        let bgGradient = '';
        let icon = '';

        switch (model_type) {
            case 'koharu':
                bgGradient = 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%)';
                icon = '🌸';
                waifuContent = 'Koharu';
                break;
            case 'shizuku':
                bgGradient = 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)';
                icon = '💧';
                waifuContent = 'Shizuku';
                break;
            case 'custom':
                bgGradient = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                icon = '⭐';
                waifuContent = '自定义';
                break;
            default:
                bgGradient = 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)';
                icon = '🌸';
                waifuContent = '看板娘';
        }

        // 根据显示模式决定是否显示互动提示
        const interactionHint = display_mode === 'pure' ? '' : '<div style="font-size: 12px; opacity: 0.9;">点击互动</div>';
        const cursor = display_mode === 'pure' ? 'default' : 'pointer';

        customWaifu.innerHTML = `
            <div style="
                width: ${width}px;
                height: ${height}px;
                background: ${bgGradient};
                border-radius: 15px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 16px;
                text-align: center;
                box-shadow: 0 8px 32px rgba(0,0,0,0.1);
                cursor: ${cursor};
                transition: all 0.3s ease;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255,255,255,0.2);
            ">
                <div>
                    <div style="font-size: 48px; margin-bottom: 12px; animation: float 3s ease-in-out infinite;">${icon}</div>
                    <div style="font-weight: bold; margin-bottom: 8px;">${waifuContent}</div>
                    ${interactionHint}
                </div>
            </div>
        `;

        customWaifu.style.cssText = `
            position: fixed;
            ${position === 'right' ? 'right' : 'left'}: 20px;
            bottom: 20px;
            z-index: 999;
            pointer-events: auto;
        `;

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes float {
                0%, 100% { transform: translateY(0px); }
                50% { transform: translateY(-10px); }
            }
            .waifu-custom:hover > div {
                transform: scale(1.05);
            }
        `;
        document.head.appendChild(style);

        // 只在标准模式下添加交互效果
        if (display_mode !== 'pure') {
            let clickCount = 0;
            const messages = [
                '你好呀！ヾ(≧▽≦*)o',
                '今天过得怎么样？(｡◕‿◕｡)',
                '要一起玩吗？(＾◡＾)',
                '你真可爱！(´∀｀)♡',
                '再点我试试看～(￣▽￣)~*'
            ];

            customWaifu.addEventListener('click', () => {
                const messageDiv = document.createElement('div');
                messageDiv.textContent = messages[clickCount % messages.length];
                messageDiv.style.cssText = `
                    position: absolute;
                    top: -50px;
                    left: 50%;
                    transform: translateX(-50%);
                    background: rgba(0,0,0,0.8);
                    color: white;
                    padding: 8px 12px;
                    border-radius: 20px;
                    font-size: 14px;
                    white-space: nowrap;
                    animation: fadeInOut 2s ease-in-out;
                    pointer-events: none;
                    z-index: 1000;
                `;

                // 添加消息动画
                const messageStyle = document.createElement('style');
                messageStyle.textContent = `
                    @keyframes fadeInOut {
                        0% { opacity: 0; transform: translateX(-50%) translateY(10px); }
                        20%, 80% { opacity: 1; transform: translateX(-50%) translateY(0px); }
                        100% { opacity: 0; transform: translateX(-50%) translateY(-10px); }
                    }
                `;
                document.head.appendChild(messageStyle);

                customWaifu.appendChild(messageDiv);
                clickCount++;

                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.parentNode.removeChild(messageDiv);
                    }
                }, 2000);
            });

            // 添加悬停效果（显示关闭按钮）
            customWaifu.addEventListener('mouseenter', () => {
                if (closeBtn) {
                    closeBtn.style.opacity = '1';
                    closeBtn.style.visibility = 'visible';
                }
            });

            customWaifu.addEventListener('mouseleave', () => {
                if (closeBtn) {
                    closeBtn.style.opacity = '0';
                    closeBtn.style.visibility = 'hidden';
                }
            });
        }

        // 只在有关闭按钮时添加关闭事件
        if (closeBtn) {
            closeBtn.addEventListener('click', (e) => {
                e.stopPropagation();

                // 查找并隐藏看板娘元素
                const waifuElement = document.querySelector('.waifu-custom');
                if (waifuElement) {
                    waifuElement.style.display = 'none';
                }

                closeBtn.style.display = 'none';
                localStorage.setItem('live2d_closed', Date.now());
                window.live2dRunning = false;
                window.tderciyuanWaifuCreated = false;

                console.log('看板娘已关闭');
            });
        }

        document.body.appendChild(customWaifu);
        console.log('自定义看板娘已创建完成，插件ID:', window.tderciyuanPluginId);
    }

    // 检查用户是否在24小时内关闭过
    const closedTime = localStorage.getItem('live2d_closed');
    if (closedTime && (Date.now() - parseInt(closedTime)) < 24 * 60 * 60 * 1000) {
        console.log('用户24小时内关闭过Live2D，不再显示');
        return;
    }

    // 页面卸载时清理
    window.addEventListener('beforeunload', function() {
        console.log('页面卸载，清理Live2D资源');
        window.live2dRunning = false;
        window.tderciyuanWaifuCreated = false;
        window.tderciyuanLive2DLoaded = false;
        cleanupLive2D();
    });

    // 页面隐藏时也进行清理（处理单页应用的情况）
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            console.log('页面隐藏，重置Live2D状态');
            window.live2dRunning = false;
            window.tderciyuanWaifuCreated = false;
        }
    });

    // 等待页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(initLive2D, 500); // 延迟500ms确保其他脚本加载完成
        });
    } else {
        setTimeout(initLive2D, 500); // 延迟500ms确保其他脚本加载完成
    }

    console.log('Tderciyuan Live2D插件已加载，插件ID:', PLUGIN_ID);
})();