import requests
import time
import random
import string
import json
from urllib.parse import urlparse, parse_qs
import uuid
import datetime
import os
import sys
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                           QLabel, QLineEdit, QPushButton, QTextEdit, QSpinBox, 
                           QGroupBox, QFormLayout, QProgressBar, QMessageBox, QFileDialog,
                           QTabWidget, QCheckBox, QDateEdit, QComboBox)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer, QDate, QEventLoop
import threading

class StoveAccountRegistration:
    def __init__(self, proxy=None, config=None):
        """
        初始化注册类
        proxy格式: "*******************:port" 或 "http://ip:port"
        config: 可选的配置字典，包含自定义设置
        """
        self.proxy = proxy
        self.proxies = {"http": self.proxy, "https": self.proxy} if proxy else None
        self.session = requests.Session()
        self.session.proxies = self.proxies
        
        # 优化网络参数，加快访问速度并防止资源耗尽
        self.session.timeout = (1.5, 5)  # 大幅减少超时时间：连接超时1.5秒，读取超时5秒
        
        # 优化连接池配置，防止资源耗尽
        adapter = requests.adapters.HTTPAdapter(
            max_retries=0,  # 不重试，直接返回结果
            pool_connections=5,  # 降低每个线程的连接池连接数，减少资源占用
            pool_maxsize=20,  # 降低最大连接数，防止资源耗尽
            pool_block=True  # 阻塞等待连接池，防止创建过多连接
        )
        self.session.mount('http://', adapter)
        self.session.mount('https://', adapter)
        
        # 禁用内容压缩以减少处理时间
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36",
            "Accept-Language": "zh-CN,zh;q=0.9",
            "Accept": "application/json, text/plain, */*",
            "Accept-Encoding": "identity",  # 禁用内容压缩
        }
        self.session.headers.update(self.headers)
        # 生成唯一ID
        self.uuid = str(uuid.uuid4())
        # 保存配置
        self.config = config or {}
        # 增强安全特性 - 添加DNS缓存
        self.dns_cache = {}
        
    def get_random_string(self, length=10, chars=None):
        """
        生成随机字符串
        chars参数可以指定字符集，默认使用小写字母和数字
        """
        if chars is None:
            chars = string.ascii_lowercase + string.digits
        return ''.join(random.choice(chars) for _ in range(length))
    
    def generate_password(self, min_length=8, max_length=24):
        """
        生成符合要求的密码：8-24个字符，包含大小写字母和数字
        """
        # 随机密码长度
        length = random.randint(min_length, max_length)
        
        # 确保包含至少一个大写字母、一个小写字母和一个数字
        password = [
            random.choice(string.ascii_uppercase),  # 一个大写字母
            random.choice(string.ascii_lowercase),  # 一个小写字母
            random.choice(string.digits)            # 一个数字
        ]
        
        # 填充剩余长度
        remaining_length = length - len(password)
        chars = string.ascii_uppercase + string.ascii_lowercase + string.digits
        password.extend(random.choice(chars) for _ in range(remaining_length))
        
        # 打乱顺序
        random.shuffle(password)
        
        return ''.join(password)
    
    def generate_question_answer(self, min_length=3, max_length=20):
        """
        生成符合要求的安全问题答案：3-20个字符，可使用大小写字母和数字
        """
        # 随机答案长度
        length = random.randint(min_length, max_length)
        
        # 使用大小写字母和数字
        chars = string.ascii_uppercase + string.ascii_lowercase + string.digits
        return ''.join(random.choice(chars) for _ in range(length))
        
    def extract_state(self, url):
        """从URL中提取state参数"""
        parsed_url = urlparse(url)
        query_params = parse_qs(parsed_url.query)
        return query_params.get('state', [None])[0]
        
    def generate_user_data(self, state):
        """生成用户注册数据"""
        # 检查是否使用自定义设置
        if self.config.get('use_custom_user_id_length', False):
            min_length = self.config.get('min_user_id_length', 6)
            max_length = self.config.get('max_user_id_length', 15)
            user_id_length = random.randint(min_length, max_length)
        else:
            user_id_length = random.randint(6, 15)
            
        # 确保用户ID中至少包含一个字母和一个数字
        letters = random.sample(string.ascii_lowercase, min(3, user_id_length // 2))
        digits = random.sample(string.digits, min(2, user_id_length // 3))
        
        # 填充剩余长度
        remaining_length = user_id_length - len(letters) - len(digits)
        remaining_chars = random.choices(string.ascii_lowercase + string.digits, k=remaining_length)
        
        # 合并并打乱顺序
        all_chars = letters + digits + remaining_chars
        random.shuffle(all_chars)
        user_id = ''.join(all_chars)
        
        # 检查是否使用固定密码
        if self.config.get('use_fixed_password', False):
            password = self.config.get('fixed_password', '')
        else:
            # 生成随机密码
            if self.config.get('use_custom_password_length', False):
                min_length = self.config.get('min_password_length', 8)
                max_length = self.config.get('max_password_length', 24)
                password = self.generate_password(min_length, max_length)
            else:
                password = self.generate_password()
        
        # 检查是否使用固定安全问题答案
        if self.config.get('use_fixed_question_answer', False):
            question_answer = self.config.get('fixed_question_answer', '')
        else:
            # 检查是否自定义安全问题答案长度
            if self.config.get('use_custom_question_answer_length', False):
                min_length = self.config.get('min_question_answer_length', 3)
                max_length = self.config.get('max_question_answer_length', 20)
                question_answer = self.generate_question_answer(min_length, max_length)
            else:
                question_answer = self.generate_question_answer()
        
        email = f"{self.get_random_string(8)}@newbt.net"
        
        # 检查是否使用固定出生日期
        if self.config.get('use_fixed_birth_dt', False):
            birth_dt = self.config.get('fixed_birth_dt', '')
        else:
            birth_dt = f"{random.randint(1970, 2000)}-{random.randint(1, 12):02d}-{random.randint(1, 28):02d}"
        
        return {
            "state": state,
            "user_id": user_id,
            "user_password": password,
            "email": email,
            "first_name": self.get_random_string(5),
            "last_name": self.get_random_string(5),
            "birth_dt": birth_dt,
            "question_code": 1,
            "question_answer": question_answer
        } 

    def accept_terms(self, log_callback=None):
        """同意条款并获取state参数"""
        # 步骤1：发送同意条款请求
        terms_url = "https://api.onstove.com/sim/v1/crossfire/save/terms"
        
        terms_headers = {
            "caller-detail": self.uuid,
            "caller-id": "sim-front",
            "X-Lang": "zh-cn",
            "X-Nation": "PH",
            "Content-Type": "application/json",
            "Origin": "https://accounts.onstove.com",
            "Referer": "https://accounts.onstove.com/",
        }
        
        terms_data = {
            "type": "SIGN_UP",
            "service_id": "10",
            "viewarea_id": "STC_REWE",
            "game_service_id": "CF_PH",
            "game_viewarea_id": "SVC_AG",
            "gds_info": {
                "is_default": False,
                "nation": "PH",
                "regulation": "ETC",
                "timezone": "Asia/Manila",
                "utc_offset": 480,
                "lang": "zh-cn",
                "ip": ""
            }
        }
        
        self.session.headers.update(terms_headers)
        
        return_msg = "正在获取条款参数..."
        
        # 使用更短的超时时间，并禁用内容验证以提高速度
        try:
            terms_response = self.session.post(terms_url, json=terms_data, timeout=(1.5, 5), verify=False)
        
            if terms_response.status_code == 200:
                try:
                    result = terms_response.json()
                    if result.get("code") == 0:
                        state = result['value']['state']
                        return_msg += f"\n获取state参数成功"
                        return state, return_msg
                    else:
                        return_msg += f"\n获取state参数失败"
                except Exception:
                    return_msg += f"\n解析响应失败"
            else:
                return_msg += f"\n同意条款请求失败"
        except Exception:
            return_msg += f"\n连接服务器失败"
        
        return None, return_msg
        
    def register(self, log_callback=None):
        """执行注册流程"""
        if log_callback is None:
            log_callback = lambda x: None
            
        try:
            # 极简化日志，减少CPU开销
            log_callback("注册中...")
            
            # 步骤1: 访问初始URL - 使用极短的超时时间
            initial_url = "https://accounts.onstove.com/signup/cf/agree?redirect_url=https%3A%2F%2Fcfph.onstove.com%2F&style_type=cf"
            
            try:
                response = self.session.get(initial_url, timeout=(0.8, 3), stream=True, allow_redirects=True, verify=False)
                
                if response.status_code != 200:
                    return None, "访问URL失败"
            except requests.exceptions.RequestException:
                # 捕获所有请求相关异常
                return None, "访问URL超时或错误"
            except Exception:
                # 捕获任何其他异常
                return None, "访问URL时发生未知错误"
            
            # 步骤2: 同意条款并获取state参数 - 内联实现而不是调用方法
            terms_url = "https://api.onstove.com/sim/v1/crossfire/save/terms"
            
            terms_headers = {
                "caller-detail": self.uuid,
                "caller-id": "sim-front",
                "X-Lang": "zh-cn",
                "X-Nation": "PH",
                "Content-Type": "application/json",
                "Origin": "https://accounts.onstove.com",
                "Referer": "https://accounts.onstove.com/",
            }
            
            terms_data = {
                "type": "SIGN_UP",
                "service_id": "10",
                "viewarea_id": "STC_REWE",
                "game_service_id": "CF_PH",
                "game_viewarea_id": "SVC_AG",
                "gds_info": {
                    "is_default": False,
                    "nation": "PH",
                    "regulation": "ETC",
                    "timezone": "Asia/Manila",
                    "utc_offset": 480,
                    "lang": "zh-cn",
                    "ip": ""
                }
            }
            
            self.session.headers.update(terms_headers)
            
            # 使用极短的超时时间
            state = None
            try:
                terms_response = self.session.post(terms_url, json=terms_data, timeout=(0.8, 3), verify=False)
                
                if terms_response.status_code == 200:
                    try:
                        result = terms_response.json()
                        if result.get("code") == 0:
                            state = result['value']['state']
                    except:
                        pass
            except requests.exceptions.RequestException:
                return None, "获取state超时或请求错误"
            except Exception:
                return None, "获取state时发生未知错误"
            
            if not state:
                return None, "无法获取state"
                
            # 步骤3: 发送注册请求
            register_url = "https://api.onstove.com/sim/v1/crossfire/register"
            user_data = self.generate_user_data(state)
            
            # 设置特定的请求头
            register_headers = {
                "caller-detail": self.uuid,
                "caller-id": "sim-front",
                "X-Lang": "zh-cn",
                "X-Nation": "PH",
                "Content-Type": "application/json",
                "Origin": "https://accounts.onstove.com",
                "Referer": "https://accounts.onstove.com/",
            }
            
            self.session.headers.update(register_headers)
            
            # 使用极短的超时时间
            try:
                register_response = self.session.post(register_url, json=user_data, timeout=(0.8, 3), verify=False)
                
                if register_response.status_code == 200:
                    try:
                        result = register_response.json()
                        if result.get("code") == 0:
                            log_callback("注册成功")
                            
                            user_data["register_time"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                            return {
                                "member_no": result['value']['member_no'],
                                "access_token": result['value'].get('access_token', ''),
                                "refresh_token": result['value'].get('refresh_token', ''),
                                "nickname": result['value'].get('nickname', ''),
                                "user_data": user_data
                            }, "注册成功"
                    except KeyError:
                        # 具体处理键错误
                        return None, "注册响应缺少必要数据"
                    except json.JSONDecodeError:
                        # 处理JSON解析错误
                        return None, "注册响应格式错误"
                    except Exception as e:
                        # 处理其他异常
                        return None, f"注册处理响应时发生错误: {type(e).__name__}"
                elif register_response.status_code == 429:
                    # 特殊处理请求限制
                    return None, "请求过于频繁，服务器限制访问"
                elif register_response.status_code >= 500:
                    # 服务器错误
                    return None, "服务器内部错误"
                else:
                    # 其他HTTP错误
                    return None, f"注册请求失败，状态码: {register_response.status_code}"
            except requests.exceptions.Timeout:
                return None, "注册请求超时"
            except requests.exceptions.ConnectionError:
                return None, "注册请求连接错误"
            except requests.exceptions.RequestException:
                return None, "注册请求网络错误"
            except Exception as e:
                return None, f"注册请求发生未知错误: {type(e).__name__}"
            
            return None, "注册失败"
            
        except Exception as e:
            return None, f"注册过程发生异常: {type(e).__name__}"
        finally:
            # 清理会话资源
            try:
                self.session.close()
            except:
                pass

class RegisterWorker(QThread):
    update_signal = pyqtSignal(str)
    progress_signal = pyqtSignal(int)
    result_signal = pyqtSignal(list)
    finished_signal = pyqtSignal(int, int, int)  # 成功数, 失败数, 总尝试次数
    error_signal = pyqtSignal(str)  # 添加错误信号
    
    def __init__(self, proxy, count, config=None):
        super().__init__()
        self.proxy = proxy
        self.count = count
        self.accounts = []
        self.is_running = True
        self.config = config or {}
        self.progress = 0  # 存储当前进度
        self.last_gc_time = 0  # 上次垃圾回收时间
        # 添加错误计数器
        self.consecutive_errors = 0
        self.max_consecutive_errors = 5
        # 添加暂停机制
        self.error_pause_time = 1.0  # 初始暂停时间
        # 无限注册模式
        self.infinite_registration = self.config.get('infinite_registration', False)
        # 添加线程性能监控
        self.last_success_time = time.time()
        self.success_count_period = 0
        # 添加自动退出标记
        self.finished_normally = False
        # 添加内存监控
        self.last_memory_check = time.time()
        # 添加线程ID，用于标识
        self.thread_id = random.randint(1000, 9999)
        # 添加异常恢复机制
        self.recovery_attempts = 0
        self.max_recovery_attempts = 3
        # 添加线程健康状态
        self.health_status = "normal"  # normal, warning, critical
        # 添加循环控制，避免CPU占用过高
        self.loop_start_time = 0
        
    def run(self):
        successful = 0
        failed = 0
        total_attempts = 0
        
        # 添加额外的异常处理
        try:
            self.update_signal.emit(f"线程{self.thread_id}开始注册{self.count if not self.infinite_registration else '无限'}个账号...")
            
            # 持续尝试直到成功数量达到目标或者被手动停止
            while (self.infinite_registration or successful < self.count) and self.is_running:
                # 记录循环开始时间
                self.loop_start_time = time.time()
                
                # 定期执行垃圾回收，防止内存泄漏
                current_time = time.time()
                if current_time - self.last_gc_time > 30:  # 每30秒执行一次垃圾回收
                    import gc
                    gc.collect()
                    self.last_gc_time = current_time
                
                # 检查内存使用情况
                if current_time - self.last_memory_check > 60:  # 每60秒检查一次内存
                    try:
                        import psutil
                        process = psutil.Process()
                        memory_info = process.memory_info()
                        memory_percent = process.memory_percent()
                        
                        # 如果内存使用率过高，主动释放内存
                        if memory_percent > 85:
                            self.health_status = "critical"
                            self.update_signal.emit(f"线程{self.thread_id}内存使用率过高({memory_percent:.1f}%)，正在释放内存...")
                            # 清理部分账号记录，只保留最新的100个
                            if len(self.accounts) > 100:
                                self.accounts = self.accounts[-100:]
                            # 强制垃圾回收
                            gc.collect()
                            # 暂停一段时间让系统恢复
                            time.sleep(2.0)
                        elif memory_percent > 70:
                            self.health_status = "warning"
                            # 减少日志输出频率，节省资源
                            if total_attempts % 10 == 0:
                                self.update_signal.emit(f"线程{self.thread_id}内存使用率较高({memory_percent:.1f}%)，减少日志输出")
                        else:
                            self.health_status = "normal"
                            
                        self.last_memory_check = current_time
                    except Exception:
                        pass  # 忽略内存检查错误
                
                # 检查线程性能，自动调整等待时间
                if current_time - self.last_success_time > 60:  # 每分钟检查一次
                    success_rate = self.success_count_period / 60.0  # 每秒成功率
                    # 根据成功率自动调整线程行为
                    if success_rate < 0.05 and self.consecutive_errors > 3:  # 成功率过低且有连续错误
                        wait_time = min(10.0, self.error_pause_time * 2)  # 增加等待时间
                        self.update_signal.emit(f"线程{self.thread_id}检测到性能低下，暂停{wait_time:.1f}秒后继续...")
                        time.sleep(wait_time)
                    # 重置计数器
                    self.last_success_time = current_time
                    self.success_count_period = 0
                
                # 检查连续错误次数，如果太多则暂停一会儿
                if self.consecutive_errors >= self.max_consecutive_errors:
                    wait_time = min(10.0, self.error_pause_time * 1.5)
                    self.update_signal.emit(f"线程{self.thread_id}检测到连续{self.consecutive_errors}次错误，暂停{wait_time:.1f}秒后继续...")
                    time.sleep(wait_time)
                    # 增加暂停时间，最大10秒
                    self.error_pause_time = min(10.0, self.error_pause_time * 1.5)
                    # 重置连续错误计数
                    self.consecutive_errors = 0
                
                total_attempts += 1
                    
                # 精简日志，减少信号发送次数
                if (total_attempts % 5 == 0 or successful == 0) and self.health_status != "warning":  # 每5次或首次显示进度
                    self.update_signal.emit(f"线程{self.thread_id} - 尝试: {total_attempts}, 成功: {successful}/{self.count if not self.infinite_registration else '∞'}")
                    
                # 计算和更新进度
                self.progress = int((successful / max(1, self.count)) * 100) if not self.infinite_registration else min(99, int(successful / 10))
                # 降低进度信号发送频率
                if total_attempts % 3 == 0:
                    self.progress_signal.emit(self.progress)
                
                try:
                    # 创建注册对象并执行注册
                    registration = StoveAccountRegistration(self.proxy, self.config)
                    result, error_msg = registration.register(log_callback=lambda x: None)  # 不输出详细日志
                    
                    if result:
                        # 重置连续错误计数和暂停时间
                        self.consecutive_errors = 0
                        self.error_pause_time = 1.0
                        # 增加成功计数
                        self.success_count_period += 1
                        # 重置恢复尝试计数
                        self.recovery_attempts = 0
                        
                        # user_id----user_password----birth_dt----question_answer----注册时间
                        account_line = f"{result['user_data']['user_id']}----{result['user_data']['user_password']}----{result['user_data']['birth_dt']}----{result['user_data']['question_answer']}----{result['user_data']['register_time']}"
                        
                        # 检查账号是否已存在于文件中
                        is_duplicate = self.check_account_exists(account_line)
                        
                        if not is_duplicate:
                            successful += 1
                            
                            # 只在开始、结束或每5个成功时更新日志
                            log_interval = 10 if self.health_status == "warning" else 5
                            if successful == 1 or successful % log_interval == 0 or successful == self.count:
                                self.update_signal.emit(f"线程{self.thread_id} - 成功: {successful}/{self.count if not self.infinite_registration else '∞'} - 最新账号: {result['user_data']['user_id']}")
                            
                            # 将账号添加到列表
                            self.accounts.append(account_line)
                            # 只发送当前新注册的账号，而不是整个账号列表
                            self.result_signal.emit([account_line])
                            
                            # 更新进度
                            self.progress = int((successful / max(1, self.count)) * 100) if not self.infinite_registration else min(99, int(successful / 10))
                            # 降低进度信号发送频率
                            if successful % 5 == 0:
                                self.progress_signal.emit(self.progress)
                        else:
                            # 账号已存在于文件中，记录日志但不计入成功数
                            if self.health_status != "warning":  # 减少日志
                                self.update_signal.emit(f"线程{self.thread_id} - 账号 {result['user_data']['user_id']} 已存在于记事本中，跳过")
                        
                        # 几乎不等待，直接继续下一个注册
                        # 如果需要，可以保留极短的等待时间以避免请求过于集中
                        time.sleep(0.05)
                    else:
                        failed += 1
                        # 增加连续错误计数
                        self.consecutive_errors += 1
                        
                        if failed % 5 == 0 and self.health_status != "warning":  # 仅在失败次数为5的倍数时输出日志
                            self.update_signal.emit(f"线程{self.thread_id} - 失败: {failed}次 - {error_msg}")
                        
                        # 根据错误次数动态调整等待时间
                        wait_time = min(2.0, 0.1 * self.consecutive_errors)
                        # 如果是频率限制错误，等待时间更长
                        if "频繁" in error_msg or "限制" in error_msg:
                            wait_time = max(wait_time, 3.0)
                            
                        time.sleep(wait_time)
                except Exception as e:
                    # 捕获注册过程中的异常，避免线程崩溃
                    failed += 1
                    # 增加连续错误计数
                    self.consecutive_errors += 1
                    self.error_signal.emit(f"线程{self.thread_id} - 注册异常: {str(e)}")
                    
                    # 如果是严重异常，尝试恢复
                    self.recovery_attempts += 1
                    if self.recovery_attempts > self.max_recovery_attempts:
                        # 如果恢复尝试次数过多，重置会话
                        self.update_signal.emit(f"线程{self.thread_id} - 频繁异常，尝试重置...")
                        # 稍长等待，避免继续尝试导致更多错误
                        time.sleep(random.uniform(3.0, 7.0))
                        self.recovery_attempts = 0
                    
                    # 根据连续错误次数动态调整等待时间
                    wait_time = min(3.0, 0.5 * self.consecutive_errors)
                    time.sleep(wait_time)
                
                # 控制循环速度，避免CPU占用过高
                elapsed = time.time() - self.loop_start_time
                if elapsed < 0.05:  # 确保每次循环至少花费50毫秒
                    time.sleep(0.05 - elapsed)
                
            # 标记正常完成
            self.finished_normally = True
            self.progress = 100
            self.progress_signal.emit(self.progress)
            self.update_signal.emit(f"线程{self.thread_id}完成! 成功: {successful}, 失败: {failed}, 总尝试: {total_attempts}")
            
        except Exception as e:
            # 捕获线程主循环中的异常
            self.error_signal.emit(f"线程{self.thread_id}运行异常: {str(e)}")
        finally:
            # 确保在任何情况下都发送完成信号
            self.finished_signal.emit(successful, failed, total_attempts)
        
    def stop(self):
        self.is_running = False

    def check_account_exists(self, account_line):
        """检查账号是否已存在于自动保存文件中"""
        try:
            if not hasattr(self, 'config') or not self.config:
                return False
                
            # 获取自动保存路径
            file_path = self.config.get('auto_save_path', 'stove_accounts_auto.txt').strip()
            if not file_path:
                file_path = 'stove_accounts_auto.txt'
                
            # 检查文件是否存在
            if not os.path.exists(file_path):
                return False
                
            # 读取文件内容
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    existing_accounts = [line.strip() for line in f if line.strip()]
                    
                # 从account_line中提取user_id
                parts = account_line.split('----')
                if len(parts) >= 1:
                    user_id = parts[0]
                    
                    # 检查是否有相同的user_id
                    for existing in existing_accounts:
                        if existing.startswith(user_id + '----'):
                            return True
            except Exception:
                pass
                
            return False
        except Exception:
            # 如果出现任何错误，默认返回False（不视为重复）
            return False

class StoveRegistrationApp(QMainWindow):
    # 添加信号，用于线程安全地更新UI
    update_signal = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("CF菲律宾账号注册工具")
        self.setMinimumSize(900, 700)
        
        self.init_ui()
        self.worker = None
        self.workers = []  # 存储多个工作线程
        self.accounts = []
        self.config = {}
        self.config_file = "stove_config.json"
        # 添加统计变量
        self.total_successful = 0
        self.total_failed = 0
        self.total_attempts = 0
        # 添加完成消息弹窗标志位，防止重复弹窗
        self.completion_message_shown = False
        # 记录主线程ID
        self.main_thread_id = threading.get_ident()
        # 添加账号缓存字典，用于快速查找
        self.account_cache = set()
        # 添加已检查过的文件账号缓存
        self.checked_file_accounts = set()
        # 上次检查文件的时间
        self.last_file_check_time = 0
        # 在StoveRegistrationApp类的__init__方法中添加
        self.global_file_lock = threading.Lock()  # 全局文件操作锁
        # 添加无限注册模式标志
        self.infinite_registration = False
        # 添加自动调整线程数标志
        self.auto_adjust_threads = False
        # 线程性能数据
        self.thread_performance = {}
        # 上次性能检查时间
        self.last_performance_check = time.time()
        # 添加线程管理计时器
        self.thread_manager_timer = QTimer(self)
        self.thread_manager_timer.timeout.connect(self.manage_threads)
        self.thread_manager_timer.start(30000)  # 每30秒执行一次
        
        # 连接信号
        self.update_signal.connect(self.log)
        
        # 尝试加载上次保存的配置（如果存在）
        self.auto_load_config()
        # 预加载已有账号到缓存
        self.preload_existing_accounts()
        
    def init_ui(self):
        # 创建中央部件和主布局
        central_widget = QWidget()
        main_layout = QVBoxLayout(central_widget)
        
        # 创建标签页控件
        tab_widget = QTabWidget()
        
        # 主标签页
        main_tab = QWidget()
        main_tab_layout = QVBoxLayout(main_tab)
        
        # 代理设置部分
        proxy_group = QGroupBox("代理设置")
        proxy_layout = QFormLayout()
        
        self.proxy_input = QLineEdit()
        self.proxy_input.setPlaceholderText("*******************:port 或 http://ip:port")
        self.proxy_input.textChanged.connect(self.on_proxy_changed)
        proxy_layout.addRow("代理地址:", self.proxy_input)
        
        proxy_group.setLayout(proxy_layout)
        main_tab_layout.addWidget(proxy_group)
        
        # 注册设置部分
        register_group = QGroupBox("注册设置")
        register_layout = QFormLayout()
        
        # 添加无限注册选项
        registration_layout = QHBoxLayout()
        self.infinite_checkbox = QCheckBox("无限注册")
        self.infinite_checkbox.stateChanged.connect(self.toggle_infinite_registration)
        registration_layout.addWidget(self.infinite_checkbox)
        
        self.count_input = QSpinBox()
        self.count_input.setRange(1, 2147483647)  # 设置为最大整数
        self.count_input.setValue(10)
        self.count_input.valueChanged.connect(self.on_count_changed)
        registration_layout.addWidget(self.count_input)
        
        register_layout.addRow("注册数量:", registration_layout)
        
        # 添加线程数量设置
        thread_layout = QHBoxLayout()
        self.thread_count_input = QSpinBox()
        self.thread_count_input.setRange(1, 9999)  # 提高线程数量上限，几乎无限制
        self.thread_count_input.setValue(1)
        self.thread_count_input.valueChanged.connect(self.on_thread_count_changed)
        thread_layout.addWidget(self.thread_count_input)
        
        # 添加自动调整线程数选项
        self.auto_adjust_threads_checkbox = QCheckBox("自动调整线程数")
        self.auto_adjust_threads_checkbox.setChecked(False)
        self.auto_adjust_threads_checkbox.stateChanged.connect(self.toggle_auto_adjust_threads)
        thread_layout.addWidget(self.auto_adjust_threads_checkbox)
        
        register_layout.addRow("线程数量:", thread_layout)
        
        # 添加高级选项
        advanced_options_layout = QHBoxLayout()
        
        # 添加分批启动选项
        self.batch_start_checkbox = QCheckBox("分批启动线程")
        self.batch_start_checkbox.setChecked(True)
        self.batch_start_checkbox.stateChanged.connect(self.on_batch_start_changed)
        advanced_options_layout.addWidget(self.batch_start_checkbox)
        
        # 添加批次大小设置
        self.batch_size_label = QLabel("批次大小:")
        advanced_options_layout.addWidget(self.batch_size_label)
        
        self.batch_size_input = QSpinBox()
        self.batch_size_input.setRange(1, 100)
        self.batch_size_input.setValue(5)
        self.batch_size_input.valueChanged.connect(self.on_batch_size_changed)
        advanced_options_layout.addWidget(self.batch_size_input)
        
        # 添加批次等待时间设置
        self.batch_wait_label = QLabel("批次间隔(秒):")
        advanced_options_layout.addWidget(self.batch_wait_label)
        
        self.batch_wait_input = QSpinBox()
        self.batch_wait_input.setRange(1, 30)
        self.batch_wait_input.setValue(2)
        self.batch_wait_input.valueChanged.connect(self.on_batch_wait_changed)
        advanced_options_layout.addWidget(self.batch_wait_input)
        
        register_layout.addRow("高级选项:", advanced_options_layout)
        
        # 添加自动保存设置
        auto_save_layout = QHBoxLayout()
        self.auto_save_checkbox = QCheckBox("注册成功后自动保存账号")
        self.auto_save_checkbox.setChecked(True)
        self.auto_save_checkbox.stateChanged.connect(self.on_auto_save_changed)
        
        self.auto_save_path_input = QLineEdit()
        self.auto_save_path_input.setPlaceholderText("自动保存文件路径 (默认为stove_accounts_auto.txt)")
        self.auto_save_path_input.setText("stove_accounts_auto.txt")
        self.auto_save_path_input.textChanged.connect(self.on_auto_save_path_changed)
        
        auto_save_btn = QPushButton("选择路径")
        auto_save_btn.clicked.connect(self.select_auto_save_path)
        
        auto_save_layout.addWidget(self.auto_save_checkbox)
        auto_save_layout.addWidget(self.auto_save_path_input)
        auto_save_layout.addWidget(auto_save_btn)
        
        register_layout.addRow("自动保存:", auto_save_layout)
        
        register_group.setLayout(register_layout)
        main_tab_layout.addWidget(register_group)
        
        # 操作按钮
        buttons_layout = QHBoxLayout()
        
        self.start_button = QPushButton("开始注册")
        self.start_button.clicked.connect(self.start_registration)
        
        self.stop_button = QPushButton("停止注册")
        self.stop_button.clicked.connect(self.stop_registration)
        self.stop_button.setEnabled(False)
        
        self.save_button = QPushButton("保存账号")
        self.save_button.clicked.connect(self.save_accounts)
        
        self.save_config_button = QPushButton("保存配置")
        self.save_config_button.clicked.connect(self.save_config)
        
        buttons_layout.addWidget(self.start_button)
        buttons_layout.addWidget(self.stop_button)
        buttons_layout.addWidget(self.save_button)
        buttons_layout.addWidget(self.save_config_button)
        
        main_tab_layout.addLayout(buttons_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        main_tab_layout.addWidget(self.progress_bar)
        
        # 日志显示区域
        log_group = QGroupBox("日志")
        log_layout = QVBoxLayout()
        
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        log_layout.addWidget(self.log_display)
        
        log_group.setLayout(log_layout)
        main_tab_layout.addWidget(log_group, 1)  # 日志区域可拉伸
        
        # 注册结果显示区域
        results_group = QGroupBox("注册结果")
        results_layout = QVBoxLayout()
        
        self.results_display = QTextEdit()
        self.results_display.setReadOnly(True)
        results_layout.addWidget(self.results_display)
        
        results_group.setLayout(results_layout)
        main_tab_layout.addWidget(results_group, 1)  # 结果区域可拉伸
        
        # 高级设置标签页
        advanced_tab = QWidget()
        advanced_tab_layout = QVBoxLayout(advanced_tab)
        
        # 用户ID设置
        user_id_group = QGroupBox("用户ID设置")
        user_id_layout = QVBoxLayout()
        
        self.use_custom_user_id_length = QCheckBox("自定义用户ID长度")
        user_id_layout.addWidget(self.use_custom_user_id_length)
        
        user_id_length_layout = QHBoxLayout()
        self.min_user_id_length = QSpinBox()
        self.min_user_id_length.setRange(6, 14)
        self.min_user_id_length.setValue(6)
        self.max_user_id_length = QSpinBox()
        self.max_user_id_length.setRange(7, 15)
        self.max_user_id_length.setValue(15)
        
        user_id_length_layout.addWidget(QLabel("最小长度:"))
        user_id_length_layout.addWidget(self.min_user_id_length)
        user_id_length_layout.addWidget(QLabel("最大长度:"))
        user_id_length_layout.addWidget(self.max_user_id_length)
        user_id_length_layout.addStretch()
        
        user_id_layout.addLayout(user_id_length_layout)
        user_id_group.setLayout(user_id_layout)
        advanced_tab_layout.addWidget(user_id_group)
        
        # 密码设置
        password_group = QGroupBox("密码设置")
        password_layout = QVBoxLayout()
        
        self.use_fixed_password = QCheckBox("使用固定密码")
        password_layout.addWidget(self.use_fixed_password)
        
        fixed_password_layout = QHBoxLayout()
        fixed_password_layout.addWidget(QLabel("固定密码:"))
        self.fixed_password = QLineEdit()
        self.fixed_password.setPlaceholderText("密码长度为8-24位，必须包含大小写字母和数字")
        fixed_password_layout.addWidget(self.fixed_password)
        
        password_layout.addLayout(fixed_password_layout)
        
        self.use_custom_password_length = QCheckBox("自定义随机密码长度")
        password_layout.addWidget(self.use_custom_password_length)
        
        password_length_layout = QHBoxLayout()
        self.min_password_length = QSpinBox()
        self.min_password_length.setRange(8, 23)
        self.min_password_length.setValue(8)
        self.max_password_length = QSpinBox()
        self.max_password_length.setRange(9, 24)
        self.max_password_length.setValue(24)
        
        password_length_layout.addWidget(QLabel("最小长度:"))
        password_length_layout.addWidget(self.min_password_length)
        password_length_layout.addWidget(QLabel("最大长度:"))
        password_length_layout.addWidget(self.max_password_length)
        password_length_layout.addStretch()
        
        password_layout.addLayout(password_length_layout)
        password_group.setLayout(password_layout)
        advanced_tab_layout.addWidget(password_group)
        
        # 出生日期设置
        birth_dt_group = QGroupBox("出生日期设置")
        birth_dt_layout = QVBoxLayout()
        
        self.use_fixed_birth_dt = QCheckBox("使用固定出生日期")
        birth_dt_layout.addWidget(self.use_fixed_birth_dt)
        
        fixed_birth_dt_layout = QHBoxLayout()
        fixed_birth_dt_layout.addWidget(QLabel("固定出生日期:"))
        self.fixed_birth_dt = QDateEdit()
        self.fixed_birth_dt.setDisplayFormat("yyyy-MM-dd")
        self.fixed_birth_dt.setDate(QDate(1990, 1, 1))
        fixed_birth_dt_layout.addWidget(self.fixed_birth_dt)
        fixed_birth_dt_layout.addStretch()
        
        birth_dt_layout.addLayout(fixed_birth_dt_layout)
        birth_dt_group.setLayout(birth_dt_layout)
        advanced_tab_layout.addWidget(birth_dt_group)
        
        # 安全问题答案设置
        question_answer_group = QGroupBox("安全问题答案设置")
        question_answer_layout = QVBoxLayout()
        
        self.use_fixed_question_answer = QCheckBox("使用固定安全问题答案")
        question_answer_layout.addWidget(self.use_fixed_question_answer)
        
        fixed_question_answer_layout = QHBoxLayout()
        fixed_question_answer_layout.addWidget(QLabel("固定安全问题答案:"))
        self.fixed_question_answer = QLineEdit()
        self.fixed_question_answer.setPlaceholderText("答案长度为3-20位，可以包含大小写字母和数字")
        fixed_question_answer_layout.addWidget(self.fixed_question_answer)
        
        question_answer_layout.addLayout(fixed_question_answer_layout)
        
        # 添加自定义安全问题答案长度设置
        self.use_custom_question_answer_length = QCheckBox("自定义安全问题答案长度")
        question_answer_layout.addWidget(self.use_custom_question_answer_length)
        
        question_answer_length_layout = QHBoxLayout()
        self.min_question_answer_length = QSpinBox()
        self.min_question_answer_length.setRange(3, 19)
        self.min_question_answer_length.setValue(3)
        self.max_question_answer_length = QSpinBox()
        self.max_question_answer_length.setRange(4, 20)
        self.max_question_answer_length.setValue(20)
        
        question_answer_length_layout.addWidget(QLabel("最小长度:"))
        question_answer_length_layout.addWidget(self.min_question_answer_length)
        question_answer_length_layout.addWidget(QLabel("最大长度:"))
        question_answer_length_layout.addWidget(self.max_question_answer_length)
        question_answer_length_layout.addStretch()
        
        question_answer_layout.addLayout(question_answer_length_layout)
        
        question_answer_group.setLayout(question_answer_layout)
        advanced_tab_layout.addWidget(question_answer_group)
        
        # 添加一个占位符以便布局更美观
        advanced_tab_layout.addStretch(1)
        
        # 将标签页添加到标签页控件
        tab_widget.addTab(main_tab, "主界面")
        tab_widget.addTab(advanced_tab, "高级设置")
        
        main_layout.addWidget(tab_widget)
        
        self.setCentralWidget(central_widget)
        
        # 连接信号和槽
        self.use_fixed_password.toggled.connect(self.toggle_fixed_password)
        self.use_custom_user_id_length.toggled.connect(self.toggle_custom_user_id_length)
        self.use_custom_password_length.toggled.connect(self.toggle_custom_password_length)
        self.use_fixed_birth_dt.toggled.connect(self.toggle_fixed_birth_dt)
        self.use_fixed_question_answer.toggled.connect(self.toggle_fixed_question_answer)
        self.use_custom_question_answer_length.toggled.connect(self.toggle_custom_question_answer_length)
        
    def auto_load_config(self):
        """自动加载上次保存的配置（如果存在）"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, "r", encoding="utf-8") as f:
                    config = json.load(f)
                self.apply_config(config)
                self.config = config
                self.log(f"已自动加载上次保存的配置")
            except Exception as e:
                self.log(f"加载配置失败: {e}")
                
    def save_config(self):
        """保存配置到文件"""
        config = self.get_config()
        
        # 打开文件对话框
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存配置", "stove_config.json", "JSON文件 (*.json)"
        )
        
        if not file_path:
            return
            
        try:
            # 打开文件并写入配置信息
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
                    
            QMessageBox.information(self, "保存成功", f"配置已保存到 {file_path}")
            self.config = config
        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存配置时出错: {e}")
            
    def auto_save_config(self):
        """自动保存当前配置"""
        config = self.get_config()
        try:
            with open(self.config_file, "w", encoding="utf-8") as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            self.config = config
            self.log("配置已自动保存")
        except Exception as e:
            self.log(f"自动保存配置失败: {e}")
        
    def start_registration(self):
        """开始注册流程"""
        # 更新配置
        self.config = self.get_config()
        # 自动保存配置
        self.auto_save_config()
        
        proxy = self.config['proxy']
        count = self.config['count']
        thread_count = self.config['thread_count']
        
        # 大规模任务安全检查
        is_large_task = count > 1000 or thread_count > 30
        if is_large_task:
            # 确认大规模任务
            result = QMessageBox.warning(
                self, 
                "大规模任务警告", 
                f"您正在启动一个大规模任务 ({count}个账号，{thread_count}个线程)。\n\n"
                f"建议：\n"
                f"1. 对于{count}个账号，建议线程数不超过{min(50, count//100 + 5)}个\n"
                f"2. 启用自动调整线程功能以优化性能\n"
                f"3. 使用分批启动功能，批次大小建议为5\n"
                f"4. 批次等待时间建议为3-5秒\n\n"
                f"是否继续，或返回调整设置？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            if result == QMessageBox.StandardButton.No:
                return
        
        # 防止使用过多线程导致崩溃，添加更严格的安全限制
        memory_limit_threads = 50  # 默认内存能支持的线程数
        
        try:
            # 尝试基于系统内存估算安全线程数
            import psutil
            # 获取可用内存(GB)
            available_memory_gb = psutil.virtual_memory().available / (1024 * 1024 * 1024)
            # 估算每个线程大约需要20-30MB内存
            memory_limit_threads = int(available_memory_gb * 1024 / 30)
            # 限制在合理范围内
            memory_limit_threads = max(10, min(200, memory_limit_threads))
        except:
            pass
            
        max_safe_threads = min(memory_limit_threads, os.cpu_count() * 10 if os.cpu_count() else 50)
        if thread_count > max_safe_threads:
            thread_count = max_safe_threads
            self.log(f"警告: 线程数被限制为最大安全值{max_safe_threads}，以防止系统崩溃")
            self.thread_count_input.setValue(thread_count)
        
        # 高线程数量警告 (可选，但不会阻止用户使用高线程数)
        if thread_count > os.cpu_count() * 4 if os.cpu_count() else 16:
            result = QMessageBox.warning(self, "线程数量警告", 
                              f"您设置了 {thread_count} 个线程，大量线程可能会导致系统资源紧张或请求被封锁。\n\n"
                              f"建议：\n"
                              f"1. 使用分批启动功能\n"
                              f"2. 增加批次等待时间到3-5秒\n"
                              f"3. 启用自动调整线程功能\n\n"
                              f"确定要继续吗？", 
                              QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            if result == QMessageBox.StandardButton.No:
                return
        
        # 清空以前的账号记录和线程
        self.accounts = []
        
        # 停止所有现有线程
        self.stop_registration()
        self.workers = []
        
        # 重置统计计数
        self.total_successful = 0
        self.total_failed = 0
        self.total_attempts = 0
        # 重置完成消息弹窗标志位
        self.completion_message_shown = False
        
        # 切换按钮状态
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        
        # 清空日志和进度条
        self.log_display.clear()
        self.progress_bar.setValue(0)
        
        # 在大型任务之前执行一次垃圾回收，清理内存
        import gc
        gc.collect()
        
        # 为大型任务优化内存
        if is_large_task:
            self.log("正在优化内存使用，准备大规模任务...")
            # 清理不必要的缓存
            self.account_cache.clear()
            self.checked_file_accounts.clear()
            # 如果有大量线程，预先加载最常用的模块
            import json, datetime, random, string, threading
            # 再次执行垃圾回收
            gc.collect()
            # 设置主窗口优先级更高
            try:
                import win32process, win32api, win32con
                win32process.SetPriorityClass(win32api.GetCurrentProcess(), win32process.ABOVE_NORMAL_PRIORITY_CLASS)
            except:
                pass
        
        # 计算每个线程需要注册的账号数量并尽量平均分配
        if not self.infinite_registration and count < thread_count:
            thread_count = count  # 减少不必要的线程数
            self.log(f"注意: 线程数已调整为与注册数量相同: {thread_count}")
            
        # 优化分配算法，让每个线程的任务量尽量接近
        base_count = count // thread_count
        remainder = count % thread_count
        
        self.log(f"启动 {thread_count} 个线程同时注册，总计 {count if not self.infinite_registration else '无限'} 个账号")
        
        # 创建并启动多个工作线程
        thread_counts = []
        for i in range(thread_count):
            # 计算当前线程要注册的账号数，保证分配均匀
            if self.infinite_registration:
                thread_count_value = 999999  # 无限模式下设置一个很大的数
            else:
                thread_count_value = base_count
                if i < remainder:
                    thread_count_value += 1
                    
            thread_counts.append(thread_count_value)
            
        self.log(f"线程账号分配: {[tc if tc < 999999 else '∞' for tc in thread_counts]}")
        
        # 预创建线程锁，以减少动态创建锁的次数
        if not hasattr(self, '_accounts_lock'):
            self._accounts_lock = threading.Lock()
        if not hasattr(self, '_stats_lock'):
            self._stats_lock = threading.Lock()
        
        # 执行一次垃圾回收，以确保内存状态良好
        gc.collect()
        
        # 创建并启动线程
        for i in range(thread_count):
            if thread_counts[i] <= 0:
                continue
                
            worker = RegisterWorker(proxy, thread_counts[i], self.config)
            worker.update_signal.connect(self.log)
            worker.progress_signal.connect(lambda v, tid=i, total=thread_count: self.update_thread_progress(v, tid, total))
            worker.result_signal.connect(self.add_accounts)
            worker.finished_signal.connect(lambda s, f, ta, tid=i, total=thread_count: self.thread_finished(s, f, ta, tid, total))
            worker.error_signal.connect(self.handle_worker_error)  # 连接错误信号
            
            self.workers.append(worker)
            
        # 分批启动线程，避免同时启动太多线程导致系统负担过重
        if self.batch_start_checkbox.isChecked():
            batch_size = self.batch_size_input.value()
            batch_wait = self.batch_wait_input.value()
            
            # 对于大型任务，强制使用更保守的批次设置
            if is_large_task and batch_size > 10:
                batch_size = 5
                self.log(f"大型任务自动调整批次大小为 {batch_size}")
            if is_large_task and batch_wait < 2:
                batch_wait = 3
                self.log(f"大型任务自动调整批次等待时间为 {batch_wait}秒")
                
            self.log(f"使用分批启动模式，批次大小: {batch_size}，等待时间: {batch_wait}秒")
        else:
            batch_size = len(self.workers)  # 一次性启动所有线程
            batch_wait = 0.5
            
            # 如果是大型任务，强制使用分批启动
            if is_large_task:
                batch_size = 5
                batch_wait = 3
                self.log(f"警告：大型任务强制启用分批启动，批次大小: {batch_size}，等待时间: {batch_wait}秒")
            
        # 添加恢复点，如果启动过程中出现问题，可以恢复
        try:
            # 使用异常处理框架保护线程启动过程
            for i in range(0, len(self.workers), batch_size):
                batch_end = min(i + batch_size, len(self.workers))
                self.log(f"启动线程批次 {i//batch_size + 1}，线程 {i+1} 至 {batch_end}")
                
                for j in range(i, batch_end):
                    try:
                        # 每个单独的线程启动也用异常处理保护
                        self.workers[j].start()
                    except Exception as e:
                        # 单个线程启动失败不影响整体
                        self.log(f"线程 {j+1} 启动失败: {str(e)}")
                    
                # 每批次间隔，给系统资源释放的时间
                if i + batch_size < len(self.workers):  # 如果不是最后一批
                    self.log(f"等待 {batch_wait} 秒后启动下一批...")
                    # 分段等待，确保UI响应
                    for _ in range(int(batch_wait * 2)):
                        time.sleep(0.5)
                        QApplication.processEvents()  # 确保UI响应
        except Exception as e:
            # 如果启动过程中出现异常，记录并尝试继续
            self.log(f"启动线程过程中发生错误: {str(e)}")
            self.log("尝试继续运行已启动的线程...")
            
        # 启动线程监控定时器
        if not hasattr(self, 'monitor_timer'):
            self.monitor_timer = QTimer(self)
            self.monitor_timer.timeout.connect(self.check_threads_health)
            self.monitor_timer.start(15000)  # 每15秒检查一次
        
        # 启动自动保存定时器
        if self.auto_save_checkbox.isChecked() and not hasattr(self, 'auto_save_timer'):
            self.auto_save_timer = QTimer(self)
            self.auto_save_timer.timeout.connect(self.scheduled_auto_save)
            self.auto_save_timer.start(60000)  # 每分钟自动保存一次
            
    def check_threads_health(self):
        """定期检查线程健康状态"""
        if not self.workers:
            return
            
        active_threads = sum(1 for w in self.workers if w and w.isRunning())
        if active_threads == 0:
            # 所有线程都已停止
            self.log("所有线程已停止，检查是否需要重新启动...")
            if not self.completion_message_shown and self.total_successful < self.config.get('count', 10):
                # 任务未完成但所有线程已停止，可能是崩溃
                self.log("检测到程序可能异常终止，尝试恢复...")
                QTimer.singleShot(1000, self.restart_registration)
                
    def restart_registration(self):
        """尝试重新启动注册流程，从上次状态恢复"""
        try:
            # 检查是否有用户交互导致的停止
            if not self.stop_button.isEnabled():
                self.log("检测到用户已停止注册，不自动重启")
                return
                
            # 获取剩余需要注册的数量
            remaining = max(0, self.config.get('count', 10) - self.total_successful)
            if remaining <= 0 and not self.infinite_registration:
                self.log("注册任务已完成，不需要重新启动")
                return
                
            # 保存已有账号
            if self.accounts:
                self.auto_save_accounts()
                self.log(f"已保存 {len(self.accounts)} 个账号")
                
            # 重新设置注册数量为剩余数量
            if not self.infinite_registration:
                self.count_input.setValue(remaining)
                self.log(f"重新启动注册，剩余 {remaining} 个账号")
            else:
                self.log("重新启动无限注册任务")
                
            # 允许线程数量少一些，避免再次崩溃
            thread_count = self.thread_count_input.value()
            if thread_count > 20:
                new_thread_count = max(10, thread_count // 2)
                self.thread_count_input.setValue(new_thread_count)
                self.log(f"自动调整线程数从 {thread_count} 到 {new_thread_count}，降低系统负担")
                
            # 确保分批启动
            self.batch_start_checkbox.setChecked(True)
            # 更保守的批次设置
            self.batch_size_input.setValue(3)
            self.batch_wait_input.setValue(5)
            
            # 启动注册
            QTimer.singleShot(2000, self.start_registration)
        except Exception as e:
            self.log(f"恢复过程中发生错误: {str(e)}")
            
    def scheduled_auto_save(self):
        """定时自动保存账号"""
        if self.auto_save_checkbox.isChecked() and self.accounts:
            # 获取当前账号总数
            count = len(self.accounts)
            self.auto_save_accounts()
            self.log(f"定时自动保存 - 已保存 {count} 个账号")
    
    def handle_worker_error(self, error_msg):
        """处理工作线程的错误"""
        self.log(f"线程错误: {error_msg}")
        
        # 如果发生严重错误，可以考虑停止所有线程
        if any(critical in error_msg.lower() for critical in 
              ["连接失败", "内存不足", "out of memory", "too many", "resource", "崩溃", 
               "crash", "拒绝", "refuse", "denied", "reset", "closed", "timeout"]):
            
            self.log("检测到严重错误，正在停止所有线程...")
            # 在单独的定时器中停止线程，避免UI卡死
            QTimer.singleShot(10, self.stop_registration)
        
    def stop_registration(self):
        """停止所有注册线程"""
        if not self.workers:
            return
            
        self.log("正在停止所有注册线程...")
        
        # 标记所有线程为停止状态
        for worker in self.workers:
            if worker and worker.isRunning():
                worker.stop()
                
        # 设置停止超时，防止无限等待
        stop_timeout = time.time() + 5  # 最多等待5秒
                
        # 等待所有线程停止，并设置超时
        while time.time() < stop_timeout:
            all_stopped = True
            for worker in self.workers:
                if worker and worker.isRunning():
                    all_stopped = False
                    break
                    
            if all_stopped:
                break
                
            # 等待一小段时间
            time.sleep(0.1)
            QApplication.processEvents()  # 确保UI响应
        
        # 如果仍有线程在运行，强制终止
        running_threads = 0
        for worker in self.workers:
            if worker and worker.isRunning():
                running_threads += 1
                try:
                    worker.terminate()  # 强制终止线程（不推荐但在这种情况下需要）
                except:
                    pass
                
        if running_threads > 0:
            self.log(f"警告: {running_threads}个线程无法正常停止，已强制终止")
                
        # 清理线程资源
        for worker in self.workers:
            if worker:
                try:
                    worker.disconnect()  # 断开所有信号连接
                    worker.deleteLater()  # 标记线程对象为可删除
                except:
                    pass
                
        self.workers = []
        
        # 主动执行一次垃圾回收
        import gc
        gc.collect()
        
        # 恢复界面状态
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.log("所有线程已停止")
                
    def update_thread_progress(self, value, thread_id, total_threads):
        """更新总体进度条，根据所有线程的进度"""
        if not self.workers:
            return
            
        # 减少进度条更新频率，提高性能
        current_time = time.time()
        if not hasattr(self, 'last_progress_time'):
            self.last_progress_time = 0
            
        # 如果距离上次更新不到0.5秒，则跳过更新 (增加间隔时间)
        if current_time - self.last_progress_time < 0.5:
            return
            
        # 在单独的低优先级定时器中处理进度更新
        QTimer.singleShot(1, lambda: self._update_progress_bar())
        self.last_progress_time = current_time
    
    def _update_progress_bar(self):
        """分离的进度条更新方法，减轻主线程负担"""
        try:
            # 计算总体进度百分比
            total_progress = 0
            count = 0
            for worker in self.workers:
                if hasattr(worker, 'progress'):
                    total_progress += worker.progress
                    count += 1
                    
            # 更新进度条
            if count > 0:
                overall_progress = int(total_progress / count)
                self.progress_bar.setValue(overall_progress)
                
            # 处理事件，确保UI响应
            QApplication.processEvents(QEventLoop.ProcessEventsFlag.ExcludeUserInputEvents)
        except Exception:
            pass  # 忽略进度更新错误
    
    def add_accounts(self, accounts):
        """添加新注册的账号到总账号列表，并自动保存"""
        # 使用线程锁来保护关键区域，防止多线程同时修改数据导致重复
        if not hasattr(self, '_accounts_lock'):
            self._accounts_lock = threading.Lock()
            
        new_accounts_added = 0  # 改为计数而非布尔值
        
        # 使用线程锁保护共享资源访问
        with self._accounts_lock:
            # 使用列表复制以避免线程间的数据竞争
            accounts_copy = accounts.copy() if isinstance(accounts, list) else []
            
            for account in accounts_copy:
                # 检查是否为重复账号（使用缓存加速）
                if account not in self.account_cache:
                    # 再检查文件中是否存在（根据user_id）
                    parts = account.split('----')
                    if len(parts) >= 1:
                        user_id = parts[0]
                        if not self.check_account_exists_in_file(user_id):
                            # 账号在内存和文件中都不存在，添加到列表
                            self.accounts.append(account)
                            # 添加到缓存集合中用于快速查找
                            self.account_cache.add(account)
                            # 添加到已检查文件缓存
                            self.checked_file_accounts.add(user_id)
                            new_accounts_added += 1
                        else:
                            # 账号在文件中已存在但不在内存中，记录日志
                            self.log(f"账号 {user_id} 已存在于记事本文件中，不重复添加")
                    else:
                        # 异常情况（账号格式错误），仍然添加
                        self.accounts.append(account)
                        self.account_cache.add(account)
                        new_accounts_added += 1
                            
            # 只有在有新账号时才更新成功计数和结果显示
            if new_accounts_added > 0:
                # 更新成功计数
                self.total_successful += new_accounts_added
        
        # 锁外执行UI更新，减少锁持有时间
        if new_accounts_added > 0:
            # 在低优先级定时器中更新结果显示，避免UI阻塞
            QTimer.singleShot(1, lambda: self._update_results_display())
            
            # 记录日志
            self.log(f"新增 {new_accounts_added} 个账号，当前总成功数: {self.total_successful}")
            
            # 检查是否达到目标注册数量（非无限模式下）
            if not self.infinite_registration:
                count = self.config.get('count', 10)
                if self.total_successful >= count:
                    self.log(f"已达到目标注册数量 {count}！当前成功注册数: {self.total_successful}")
                    self.stop_registration()
                    
                    # 确保按钮状态正确更新
                    QTimer.singleShot(100, lambda: self.update_buttons_after_completion())
                    # 显示完成通知
                    QTimer.singleShot(200, lambda: self.show_completion_notification())
            
            # 如果启用了自动保存，则自动保存账号
            if self.auto_save_checkbox.isChecked():
                # 在低优先级任务中执行保存
                QTimer.singleShot(50, self.auto_save_accounts)
    
    def _update_results_display(self):
        """分离的结果显示更新方法，减轻主线程负担"""
        try:
            self.results_display.clear()
            
            # 使用线程锁保护账号列表的访问
            if not hasattr(self, '_accounts_lock'):
                self._accounts_lock = threading.Lock()
                
            # 在锁保护下获取账号列表副本
            with self._accounts_lock:
                # 只显示最新的100个账号，避免界面卡顿
                accounts_to_display = self.accounts[-100:].copy() if self.accounts else []
            
            # 锁外更新UI，减少锁持有时间
            for account in accounts_to_display:
                self.results_display.append(account)
            
            # 处理事件，确保UI响应
            QApplication.processEvents(QEventLoop.ProcessEventsFlag.ExcludeUserInputEvents)
        except Exception as e:
            # 添加更详细的错误日志
            self.log(f"更新显示错误: {str(e)}")
            pass
            
    def update_buttons_after_completion(self):
        """确保线程完成后按钮状态正确"""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        # 强制更新UI
        QApplication.processEvents()
        
    def save_accounts(self):
        """保存账号信息到文件"""
        if not self.accounts:
            QMessageBox.warning(self, "保存失败", "没有可保存的账号信息")
            return
            
        # 打开文件对话框
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存账号信息", "stove_accounts.txt", "文本文件 (*.txt)"
        )
        
        if not file_path:
            return
            
        try:
            # 先读取文件中已有的账号，避免重复
            existing_accounts = []
            if os.path.exists(file_path):
                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        existing_accounts = [line.strip() for line in f if line.strip()]
                except Exception:
                    pass
            
            # 找出新账号（不在现有文件中的账号）
            new_accounts = []
            for account in self.accounts:
                if account not in existing_accounts:
                    new_accounts.append(account)
                    
            if not new_accounts:
                QMessageBox.information(self, "保存信息", "所有账号已存在于文件中，文件保持不变")
                return
                
            # 如果有新账号，追加到文件末尾
            with open(file_path, "a", encoding="utf-8") as f:
                for account in new_accounts:
                    f.write(account + "\n")
                    
            QMessageBox.information(self, "保存成功", f"已追加 {len(new_accounts)} 个新账号到文件 {file_path}")
        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存账号信息时出错: {e}")

    def on_proxy_changed(self):
        """当代理地址变更时自动保存配置"""
        self.auto_save_config()
        
    def on_count_changed(self, value):
        """当注册数量变更时自动保存配置"""
        self.auto_save_config()

    def on_thread_count_changed(self, value):
        """当线程数量变更时自动保存配置"""
        self.auto_save_config()
        
    def on_auto_adjust_threads(self, state):
        """切换自动调整线程数模式"""
        self.auto_adjust_threads = state == Qt.CheckState.Checked.value
        self.thread_count_input.setEnabled(not self.auto_adjust_threads)
        self.auto_save_config()
    
    def on_batch_start_changed(self, state):
        """批量启动选项变更"""
        batch_start = state == Qt.CheckState.Checked.value
        self.batch_size_input.setEnabled(batch_start)
        self.batch_wait_input.setEnabled(batch_start)
        self.batch_size_label.setEnabled(batch_start)
        self.batch_wait_label.setEnabled(batch_start)
        self.auto_save_config()
        
    def on_batch_size_changed(self, value):
        """批次大小变更"""
        self.auto_save_config()
        
    def on_batch_wait_changed(self, value):
        """批次等待时间变更"""
        self.auto_save_config()
        
    def toggle_infinite_registration(self, state):
        """切换无限注册模式"""
        self.infinite_registration = state == Qt.CheckState.Checked.value
        self.count_input.setEnabled(not self.infinite_registration)
        self.auto_save_config()
        
    def log(self, message):
        """添加日志信息到日志显示区域，优化界面更新性能"""
        # 限制日志更新频率，避免界面过于频繁刷新消耗性能
        current_time = time.time()
        if not hasattr(self, 'last_log_time'):
            self.last_log_time = 0
            self.log_buffer = []
            
        # 添加到缓冲区
        self.log_buffer.append(message)
        
        # 如果距离上次更新不到0.5秒且不是强制更新，则先缓存 (提高间隔时间)
        if current_time - self.last_log_time < 0.5 and not message.startswith("完成!") and not message.startswith("成功:"):
            return
            
        # 更新界面
        if self.log_buffer:
            try:
                # 防止日志处理阻塞UI
                QTimer.singleShot(1, lambda: self.update_log_display())
            except Exception:
                pass  # 忽略日志更新错误，确保不影响主流程
    
    def update_log_display(self):
        """单独的方法处理日志显示更新，减轻主线程负担"""
        try:
            # 限制显示最新的100行日志，防止日志过长导致性能问题
            current_text = self.log_display.toPlainText()
            lines = current_text.split("\n")
            if len(lines) > 100:
                self.log_display.clear()
                self.log_display.append("\n".join(lines[-50:]))  # 只保留最新的50行
            
            # 添加新日志
            self.log_display.append("\n".join(self.log_buffer[-50:]))  # 最多显示最新的50条
            
            # 自动滚动到底部
            scrollbar = self.log_display.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())
            
            # 清空缓冲区
            self.log_buffer = []
            self.last_log_time = time.time()
            
            # 处理事件，确保UI响应
            QApplication.processEvents(QEventLoop.ProcessEventsFlag.ExcludeUserInputEvents)
        except Exception:
            pass  # 忽略日志更新错误
    
    def get_config(self):
        """获取当前配置"""
        config = {
            'proxy': self.proxy_input.text().strip(),
            'count': self.count_input.value(),
            'thread_count': self.thread_count_input.value(),
            'auto_save_enabled': self.auto_save_checkbox.isChecked(),
            'auto_save_path': self.auto_save_path_input.text().strip(),
            'infinite_registration': self.infinite_registration,
            'auto_adjust_threads': self.auto_adjust_threads,
            'batch_start': self.batch_start_checkbox.isChecked(),
            'batch_size': self.batch_size_input.value(),
            'batch_wait': self.batch_wait_input.value(),
            
            'use_custom_user_id_length': self.use_custom_user_id_length.isChecked(),
            'min_user_id_length': self.min_user_id_length.value(),
            'max_user_id_length': self.max_user_id_length.value(),
            
            'use_fixed_password': self.use_fixed_password.isChecked(),
            'fixed_password': self.fixed_password.text(),
            'use_custom_password_length': self.use_custom_password_length.isChecked(),
            'min_password_length': self.min_password_length.value(),
            'max_password_length': self.max_password_length.value(),
            
            'use_fixed_birth_dt': self.use_fixed_birth_dt.isChecked(),
            'fixed_birth_dt': self.fixed_birth_dt.date().toString("yyyy-MM-dd"),
            
            'use_fixed_question_answer': self.use_fixed_question_answer.isChecked(),
            'fixed_question_answer': self.fixed_question_answer.text(),
            
            'use_custom_question_answer_length': self.use_custom_question_answer_length.isChecked(),
            'min_question_answer_length': self.min_question_answer_length.value(),
            'max_question_answer_length': self.max_question_answer_length.value()
        }
        return config
        
    def apply_config(self, config):
        """应用配置到UI界面"""
        if not config:
            return
            
        self.proxy_input.setText(config.get('proxy', ''))
        self.count_input.setValue(config.get('count', 10))
        self.thread_count_input.setValue(config.get('thread_count', 1))
        
        # 无限注册设置
        self.infinite_registration = config.get('infinite_registration', False)
        self.infinite_checkbox.setChecked(self.infinite_registration)
        self.count_input.setEnabled(not self.infinite_registration)
        
        # 自动调整线程数设置
        self.auto_adjust_threads = config.get('auto_adjust_threads', False)
        self.auto_adjust_threads_checkbox.setChecked(self.auto_adjust_threads)
        self.thread_count_input.setEnabled(not self.auto_adjust_threads)
        
        # 分批启动设置
        self.batch_start_checkbox.setChecked(config.get('batch_start', True))
        self.batch_size_input.setValue(config.get('batch_size', 5))
        self.batch_wait_input.setValue(config.get('batch_wait', 2))
        self.on_batch_start_changed(self.batch_start_checkbox.isChecked())
        
        # 自动保存设置
        self.auto_save_checkbox.setChecked(config.get('auto_save_enabled', True))
        self.auto_save_path_input.setText(config.get('auto_save_path', 'stove_accounts_auto.txt'))
        
        # 用户ID长度设置
        self.use_custom_user_id_length.setChecked(config.get('use_custom_user_id_length', False))
        self.min_user_id_length.setValue(config.get('min_user_id_length', 6))
        self.max_user_id_length.setValue(config.get('max_user_id_length', 15))
        self.use_custom_user_id_length.setEnabled(config.get('use_custom_user_id_length', False))
        
        # 密码设置
        self.use_fixed_password.setChecked(config.get('use_fixed_password', False))
        self.fixed_password.setText(config.get('fixed_password', ''))
        self.use_custom_password_length.setEnabled(not config.get('use_fixed_password', False))
        
        self.use_fixed_birth_dt.setChecked(config.get('use_fixed_birth_dt', False))
        if 'fixed_birth_dt' in config:
            try:
                date_parts = config['fixed_birth_dt'].split('-')
                date = QDate(int(date_parts[0]), int(date_parts[1]), int(date_parts[2]))
                self.fixed_birth_dt.setDate(date)
            except (IndexError, ValueError):
                pass
        self.use_fixed_birth_dt.setEnabled(config.get('use_fixed_birth_dt', False))
        
        # 安全问题答案设置
        self.use_fixed_question_answer.setChecked(config.get('use_fixed_question_answer', False))
        self.fixed_question_answer.setText(config.get('fixed_question_answer', ''))
        self.use_custom_question_answer_length.setEnabled(not config.get('use_fixed_question_answer', False))
        
        # 自定义安全问题答案长度设置
        self.use_custom_question_answer_length.setChecked(config.get('use_custom_question_answer_length', False))
        self.min_question_answer_length.setValue(config.get('min_question_answer_length', 3))
        self.max_question_answer_length.setValue(config.get('max_question_answer_length', 20))
        self.use_custom_question_answer_length.setEnabled(config.get('use_custom_question_answer_length', False))
        
    def thread_finished(self, successful, failed, total_attempts, thread_id, total_threads):
        """单个线程完成时的处理"""
        # 使用线程锁保护统计数据的更新
        if not hasattr(self, '_stats_lock'):
            self._stats_lock = threading.Lock()
            
        with self._stats_lock:
            # 累加统计数据 (只累加失败数和尝试次数，成功数已在add_accounts中处理)
            self.total_failed += failed
            self.total_attempts += total_attempts
            
            # 检查是否所有线程都已完成
            all_finished = True
            normal_finish = True  # 检查是否正常完成
            for worker in self.workers:
                if worker and worker.isRunning():
                    all_finished = False
                    break
                elif worker and not hasattr(worker, 'finished_normally'):
                    normal_finish = False  # 至少有一个线程非正常结束
                elif worker and not worker.finished_normally:
                    normal_finish = False  # 至少有一个线程非正常结束
        
        # 在低优先级任务中更新日志，避免UI阻塞
        QTimer.singleShot(1, lambda: self.log(f"线程{thread_id+1}完成 - 当前统计 - 成功: {self.total_successful}，失败: {self.total_failed}，总尝试: {self.total_attempts}"))
        
        # 如果不是无限模式且所有线程都已完成，执行收尾工作
        if all_finished and len(self.workers) > 0:  # 确保有线程运行过
            # 使用定时器延迟执行，避免UI阻塞
            QTimer.singleShot(1, lambda: self.log(f"所有线程已完成！总计 - 成功: {self.total_successful}，失败: {self.total_failed}，总尝试次数: {self.total_attempts}"))
            # 延迟停止所有线程
            QTimer.singleShot(10, self.stop_registration)
            # 确保按钮状态正确更新
            QTimer.singleShot(100, self.update_buttons_after_completion)
            
            # 如果是非无限模式且正常完成了全部任务，显示完成消息
            if not self.infinite_registration and normal_finish and self.total_successful >= self.config.get('count', 10):
                QTimer.singleShot(200, lambda: self.show_completion_notification(True))
            else:
                # 显示普通的完成消息
                QTimer.singleShot(200, lambda: self.show_completion_notification(False))
                
    def show_completion_notification(self, auto_close=False):
        """显示完成消息，避免UI阻塞"""
        # 检查是否已经显示过完成消息
        if self.completion_message_shown:
            return
            
        try:
            if auto_close:
                # 显示自动关闭的完成消息
                reply = QMessageBox.information(self, "注册完成", 
                                       f"注册任务已完成!\n成功注册账号数: {self.total_successful}\n注册失败次数: {self.total_failed}\n总尝试次数: {self.total_attempts}\n\n程序将在30秒后自动关闭。",
                                       QMessageBox.StandardButton.Ok | QMessageBox.StandardButton.Cancel)
                
                # 如果用户点击了取消，则不自动关闭
                if reply == QMessageBox.StandardButton.Ok:
                    # 创建一个定时器，延迟30秒关闭程序
                    QTimer.singleShot(30000, lambda: self.auto_close_application())
            else:
                # 普通的完成消息
                QMessageBox.information(self, "注册完成", 
                                       f"注册任务已完成!\n成功注册账号数: {self.total_successful}\n注册失败次数: {self.total_failed}\n总尝试次数: {self.total_attempts}")
                
            # 设置标志位，防止重复弹窗
            self.completion_message_shown = True
        except Exception:
            self.log("注册任务已完成")
            
    def auto_close_application(self):
        """自动关闭应用程序"""
        try:
            # 确认保存账号
            if self.accounts and self.auto_save_checkbox.isChecked():
                self.log("正在保存账号后退出...")
                self.auto_save_accounts()
                # 等待2秒确保保存完成
                loop = QEventLoop()
                QTimer.singleShot(2000, loop.quit)
                loop.exec()
                
            self.log("任务已完成，程序正在关闭...")
            # 延迟1秒后关闭，确保日志显示
            QTimer.singleShot(1000, lambda: QApplication.instance().quit())
        except Exception as e:
            self.log(f"自动关闭时发生错误: {e}")

    def preload_existing_accounts(self):
        """预加载已有账号到缓存中，提高查重性能"""
        try:
            file_path = self.auto_save_path_input.text().strip()
            if not file_path:
                file_path = "stove_accounts_auto.txt"
                
            if os.path.exists(file_path):
                with open(file_path, "r", encoding="utf-8") as f:
                    for line in f:
                        line = line.strip()
                        if line:
                            # 添加整行到缓存
                            self.account_cache.add(line)
                            # 提取user_id添加到已检查缓存
                            parts = line.split('----')
                            if len(parts) >= 1:
                                self.checked_file_accounts.add(parts[0])
                                
                self.log(f"已预加载 {len(self.account_cache)} 个已有账号到缓存")
                # 更新检查时间
                self.last_file_check_time = time.time()
            
            # 添加自动去重功能
            self.clean_duplicate_accounts(file_path)
        except Exception as e:
            self.log(f"预加载账号缓存失败: {e}")
            
    def check_account_exists_in_file(self, user_id):
        """检查账号是否已存在于文件中（使用缓存提高性能）"""
        try:
            # 获取自动保存路径
            file_path = self.auto_save_path_input.text().strip()
            if not file_path:
                file_path = "stove_accounts_auto.txt"
                
            # 检查是否需要重新加载文件缓存（每5秒检查一次）
            current_time = time.time()
            if current_time - self.last_file_check_time > 5:  # 每5秒检查一次
                # 重新加载文件内容到缓存
                self.checked_file_accounts.clear()
                if os.path.exists(file_path):
                    try:
                        with open(file_path, "r", encoding="utf-8") as f:
                            for line in f:
                                line_parts = line.strip().split('----')
                                if line_parts and len(line_parts) >= 1:
                                    self.checked_file_accounts.add(line_parts[0])
                    except Exception:
                        pass
                self.last_file_check_time = current_time
                
            # 从缓存中检查
            return user_id in self.checked_file_accounts
        except Exception:
            return False

    def clean_duplicate_accounts(self, file_path):
        """清理文件中的重复账号，只保留每个用户ID的第一次出现"""
        if not os.path.exists(file_path):
            return
        
        with self.global_file_lock:
            try:
                # 读取所有账号
                with open(file_path, "r", encoding="utf-8") as f:
                    lines = [line.strip() for line in f if line.strip()]
                    
                # 使用字典去重，保留每个ID的第一次出现
                unique_accounts = {}
                
                for line in lines:
                    parts = line.split('----')
                    if len(parts) >= 1:
                        user_id = parts[0]
                        if user_id not in unique_accounts:
                            unique_accounts[user_id] = line
                
                # 清空文件并写入唯一账号
                with open(file_path, "w", encoding="utf-8") as f:
                    for account in unique_accounts.values():
                        f.write(account + "\n")
                
                self.log(f"已清理重复账号，保留 {len(unique_accounts)} 个唯一账号")
            except Exception as e:
                self.log(f"清理重复账号失败: {e}")

    def manage_threads(self):
        """定期管理线程性能和资源使用"""
        if not self.workers:
            return
            
        current_time = time.time()
        # 如果距离上次检查不到30秒，则跳过
        if current_time - self.last_performance_check < 30:
            return
            
        self.last_performance_check = current_time
        
        try:
            # 检查系统资源使用情况
            import psutil
            
            # 获取CPU和内存使用率
            cpu_percent = psutil.cpu_percent()
            mem_percent = psutil.virtual_memory().percent
            
            # 记录资源状态
            if self.workers and len(self.workers) > 10:  # 只在线程数较多时记录
                self.log(f"系统状态 - CPU: {cpu_percent}%, 内存: {mem_percent}%, 活跃线程: {len(self.workers)}")
            
            # 根据系统资源状态调整线程数
            if cpu_percent > 85 or mem_percent > 90:  # 系统负载过高
                # 减少线程数
                if len(self.workers) > 2:
                    to_stop = max(1, len(self.workers) // 5)  # 减少约20%的线程
                    self.log(f"系统资源紧张 (CPU: {cpu_percent}%, 内存: {mem_percent}%), 自动减少{to_stop}个线程")
                    self._adjust_thread_count(-to_stop)
            elif (cpu_percent > 75 or mem_percent > 80) and len(self.workers) > 30:
                # 中度资源紧张，减少少量线程
                to_stop = max(1, len(self.workers) // 10)  # 减少约10%的线程
                self.log(f"系统资源开始紧张 (CPU: {cpu_percent}%, 内存: {mem_percent}%), 减少{to_stop}个线程")
                self._adjust_thread_count(-to_stop)
            elif cpu_percent < 40 and mem_percent < 70 and self.infinite_registration and self.auto_adjust_threads:
                # 系统资源充足且为无限注册模式，可增加线程
                current_count = len(self.workers)
                if current_count < 50:  # 设置上限，防止无限增长
                    to_add = max(1, current_count // 10)  # 增加约10%的线程
                    self.log(f"系统资源充足 (CPU: {cpu_percent}%, 内存: {mem_percent}%), 自动增加{to_add}个线程")
                    self._adjust_thread_count(to_add)
        except Exception as e:
            # 忽略资源检查错误
            pass
            
        # 执行崩溃保护检查 - 如果内存使用过高，主动释放资源
        try:
            import psutil
            process = psutil.Process()
            
            # 如果内存使用率超过90%，进行紧急措施
            if process.memory_percent() > 90:
                self.log("警告：内存使用率过高，执行紧急内存释放...")
                # 立即停止一半的线程
                if len(self.workers) > 1:
                    to_stop = max(1, len(self.workers) // 2)
                    self._adjust_thread_count(-to_stop)
                # 手动清理缓存
                self.account_cache.clear()
                self.checked_file_accounts.clear()
                # 强制垃圾回收
                import gc
                gc.collect()
                time.sleep(1)
                gc.collect()
                self.log("紧急内存释放完成")
        except:
            pass
            
        # 检查线程运行状况，移除卡死的线程
        inactive_threads = 0
        for i in range(len(self.workers) - 1, -1, -1):
            if i >= len(self.workers):
                continue
                
            worker = self.workers[i]
            if not worker or not worker.isRunning():
                continue
                
            # 检查线程是否卡住（如果最后成功时间超过5分钟）
            if hasattr(worker, 'last_success_time') and current_time - worker.last_success_time > 300:
                inactive_threads += 1
                self.log(f"检测到线程{i+1}已卡住超过5分钟，正在重启...")
                
                try:
                    # 安全停止并移除线程
                    worker.stop()
                    # 等待最多1秒
                    if worker.wait(1000):
                        self.log(f"线程{i+1}已正常停止")
                    else:
                        worker.terminate()  # 强制终止
                        self.log(f"线程{i+1}已强制终止")
                        
                    # 断开信号连接
                    try:
                        worker.disconnect()
                    except:
                        pass
                    
                    # 从线程列表中移除
                    self.workers.pop(i)
                    
                    # 只在不超过50个非活跃线程时重新添加线程
                    if inactive_threads < 50:
                        # 重新添加一个线程
                        self._add_single_thread()
                except Exception as e:
                    self.log(f"重启线程时出错: {str(e)}")
                    
        # 主动执行一次垃圾回收
        import gc
        gc.collect()
        
    def _adjust_thread_count(self, delta):
        """调整线程数量"""
        if delta == 0:
            return
            
        if delta > 0:
            # 增加线程
            for _ in range(delta):
                self._add_single_thread()
        else:
            # 减少线程
            for _ in range(-delta):
                if self.workers:
                    # 移除最后一个线程
                    worker = self.workers.pop()
                    if worker:
                        worker.stop()
                        # 异步等待线程结束
                        QTimer.singleShot(1000, lambda w=worker: self._cleanup_thread(w))
                        
    def _cleanup_thread(self, worker):
        """清理不再使用的线程"""
        if not worker:
            return
            
        try:
            if worker.isRunning():
                worker.terminate()
                
            worker.disconnect()
            worker.deleteLater()
        except:
            pass
    
    def _add_single_thread(self):
        """添加单个新线程"""
        if not hasattr(self, 'config'):
            return
            
        # 获取当前配置
        proxy = self.config.get('proxy', '')
        count = self.config.get('count', 10)
        
        # 计算线程要处理的账号数
        thread_count = count / (len(self.workers) + 1)
        if self.infinite_registration:
            thread_count = 999999  # 无限模式下设置一个很大的数
            
        # 创建新线程
        worker = RegisterWorker(proxy, thread_count, self.config)
        worker.update_signal.connect(self.log)
        worker.progress_signal.connect(lambda v, tid=len(self.workers), total=len(self.workers)+1: 
                                     self.update_thread_progress(v, tid, total))
        worker.result_signal.connect(self.add_accounts)
        worker.finished_signal.connect(lambda s, f, ta, tid=len(self.workers), total=len(self.workers)+1: 
                                     self.thread_finished(s, f, ta, tid, total))
        worker.error_signal.connect(self.handle_worker_error)
        
        self.workers.append(worker)
        
        worker.start()
        
        self.log(f"添加了新线程，当前线程总数: {len(self.workers)}")

    def on_auto_save_changed(self, state):
        """当自动保存选项变更时自动保存配置"""
        self.auto_save_config()
        
    def on_auto_save_path_changed(self):
        """当自动保存路径变更时自动保存配置"""
        self.auto_save_config()
        
    def select_auto_save_path(self):
        """选择自动保存文件路径"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "选择自动保存文件", self.auto_save_path_input.text(), "文本文件 (*.txt)"
        )
        
        if file_path:
            self.auto_save_path_input.setText(file_path)
            self.auto_save_config()
    
    def auto_save_accounts(self):
        """自动保存账号信息到设置的文件路径"""
        if not self.accounts:
            return
            
        file_path = self.auto_save_path_input.text().strip()
        if not file_path:
            file_path = "stove_accounts_auto.txt"
        
        # 创建单独的线程来处理文件操作，避免阻塞UI
        accounts_copy = self.accounts.copy()  # 复制一份账号列表
        save_thread = threading.Thread(
            target=self._async_save_accounts,
            args=(accounts_copy, file_path),
            daemon=True  # 设为守护线程，程序退出时自动结束
        )
        save_thread.start()
    
    def _async_save_accounts(self, accounts, file_path):
        """在单独的线程中异步保存账号"""
        try:
            # 获取全局文件锁，确保同一时间只有一个线程操作文件
            with self.global_file_lock:
                # 每次都重新读取文件，确保获取最新内容
                existing_accounts = {}  # 用字典存储，键为user_id，值为完整账号行
                if os.path.exists(file_path):
                    try:
                        with open(file_path, "r", encoding="utf-8") as f:
                            for line in f:
                                line = line.strip()
                                if line:
                                    parts = line.split('----')
                                    if parts and len(parts) >= 1:
                                        existing_accounts[parts[0]] = line
                    except Exception as e:
                        self.update_signal.emit(f"读取文件时出错: {e}")
                
                # 找出新账号
                new_accounts = []
                for account in accounts:
                    parts = account.split('----')
                    if parts and len(parts) >= 1:
                        user_id = parts[0]
                        if user_id not in existing_accounts:
                            new_accounts.append(account)
                            existing_accounts[user_id] = account  # 更新字典防止本批次内重复
                
                # 如果有新账号，追加到文件末尾
                if new_accounts:
                    with open(file_path, "a", encoding="utf-8") as f:
                        for account in new_accounts:
                            f.write(account + "\n")
                    
                    # 同时更新内存缓存
                    with self._accounts_lock:
                        for user_id in existing_accounts:
                            self.checked_file_accounts.add(user_id)
                    
                    self.update_signal.emit(f"已自动追加 {len(new_accounts)} 个新账号到文件")
                else:
                    self.update_signal.emit(f"没有新账号需要保存")
        except Exception as e:
            self.update_signal.emit(f"保存账号失败: {e}")
    
    def toggle_fixed_password(self, checked):
        self.fixed_password.setEnabled(checked)
        self.use_custom_password_length.setEnabled(not checked)
        if checked:
            self.use_custom_password_length.setChecked(False)
        # 设置改变时自动保存
        self.auto_save_config()
            
    def toggle_custom_password_length(self, checked):
        self.min_password_length.setEnabled(checked)
        self.max_password_length.setEnabled(checked)
        # 设置改变时自动保存
        self.auto_save_config()
        
    def toggle_custom_user_id_length(self, checked):
        self.min_user_id_length.setEnabled(checked)
        self.max_user_id_length.setEnabled(checked)
        # 设置改变时自动保存
        self.auto_save_config()
        
    def toggle_fixed_birth_dt(self, checked):
        self.fixed_birth_dt.setEnabled(checked)
        # 设置改变时自动保存
        self.auto_save_config()
        
    def toggle_fixed_question_answer(self, checked):
        self.fixed_question_answer.setEnabled(checked)
        self.use_custom_question_answer_length.setEnabled(not checked)
        if checked:
            self.use_custom_question_answer_length.setChecked(False)
        # 设置改变时自动保存
        self.auto_save_config()
        
    def toggle_custom_question_answer_length(self, checked):
        self.min_question_answer_length.setEnabled(checked)
        self.max_question_answer_length.setEnabled(checked)
        # 设置改变时自动保存
        self.auto_save_config()
        
    def toggle_auto_adjust_threads(self, state):
        """切换自动调整线程数模式"""
        self.auto_adjust_threads = state == Qt.CheckState.Checked.value
        self.thread_count_input.setEnabled(not self.auto_adjust_threads)
        self.auto_save_config()

def main():
    # 添加全局异常处理钩子
    def global_exception_handler(exctype, value, traceback):
        print(f"未捕获的异常: {exctype.__name__}: {value}")
        sys.__excepthook__(exctype, value, traceback)  # 仍然调用原始处理程序以获取详细信息
        
    sys.excepthook = global_exception_handler
    
    # 设置线程异常处理
    old_thread_run = threading.Thread.run
    def new_thread_run(*args, **kwargs):
        try:
            old_thread_run(*args, **kwargs)
        except Exception as e:
            print(f"线程异常: {str(e)}")
    threading.Thread.run = new_thread_run
    
    app = QApplication(sys.argv)
    window = StoveRegistrationApp()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main() 