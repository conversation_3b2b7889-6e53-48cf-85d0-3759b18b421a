# -*- mode: python ; coding: utf-8 -*-

import sys
import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# 收集数据文件
datas = []

# 添加配置文件
datas += [('config/config_example.ini', 'config/')]

# 添加文档（如果存在）
if os.path.exists('docs'):
    datas += [('docs/', 'docs/')]

# 手动添加ddddocr模型文件

# ddddocr模型文件路径
ddddocr_path = r"D:\pyfile\py\Lib\site-packages\ddddocr"
datas += [(r"D:\pyfile\py\Lib\site-packages\ddddocr\common.onnx", "ddddocr/.")]
datas += [(r"D:\pyfile\py\Lib\site-packages\ddddocr\common_det.onnx", "ddddocr/.")]
datas += [(r"D:\pyfile\py\Lib\site-packages\ddddocr\common_old.onnx", "ddddocr/.")]
datas += [(r"D:\pyfile\py\Lib\site-packages\ddddocr\common.onnx", "ddddocr/.")]
datas += [(r"D:\pyfile\py\Lib\site-packages\ddddocr\common_det.onnx", "ddddocr/.")]
datas += [(r"D:\pyfile\py\Lib\site-packages\ddddocr\common_old.onnx", "ddddocr/.")]

# 尝试自动收集ddddocr数据文件
try:
    ddddocr_datas = collect_data_files('ddddocr')
    datas += ddddocr_datas
    print(f"[成功] 自动收集ddddocr文件: {len(ddddocr_datas)} 个")
except Exception as e:
    print(f"[警告] 自动收集ddddocr失败: {e}")

# 收集PyQt6数据文件
try:
    pyqt6_datas = collect_data_files('PyQt6')
    datas += pyqt6_datas
except Exception as e:
    print(f"[警告] 收集PyQt6失败: {e}")

# 隐藏导入
hiddenimports = []

# ddddocr相关
try:
    hiddenimports += collect_submodules('ddddocr')
    hiddenimports += ['onnxruntime', 'numpy', 'opencv-python', 'pillow']
except:
    pass

# selenium相关
try:
    hiddenimports += collect_submodules('selenium')
except:
    pass

# PyQt6相关
try:
    hiddenimports += collect_submodules('PyQt6')
except:
    pass

# 项目模块
hiddenimports += [
    'core.register',
    'core.sms_api', 
    'core.captcha_handler',
    'core.batch_register',
    'gui.register_gui',
    'browser_automation.selenium_register',
    'browser_automation.element_helper',
    'browser_automation.xpath_config',
    'config.config_manager',
    'utils.fix_chromedriver'
]

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['PyQt5', 'PyQt5.QtCore', 'PyQt5.QtGui', 'PyQt5.QtWidgets', 'tkinter'],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='用户注册工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
