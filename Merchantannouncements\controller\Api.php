<?php

namespace plugin\Merchantannouncements\controller;

use app\common\controller\BasePlugin;
use think\facade\View;

class Api extends BasePlugin {

    protected $scene = ['admin', 'merchant'];
    protected $noNeedLogin = [
        'fetchData',
    ];

    public function index() {
        // 添加编辑器配置
        $editorConfig = [
            'toolbarKeys' => [
                [
                    'key' => 'headerSelect',
                    'title' => '正文'
                ],
                'quote',
                'bold',
                'underline',
                'italic',
                'through',
                [
                    'key' => 'fontSize',
                    'title' => '默认字号'
                ],
                [
                    'key' => 'fontFamily',
                    'title' => '默认字体'
                ],
                [
                    'key' => 'lineHeight',
                    'title' => '默认行高'
                ],
                'bulletedList',
                'numberedList',
                'todo',
                [
                    'key' => 'group-justify',
                    'title' => '对齐',
                    'menuKeys' => ['justifyLeft', 'justifyCenter', 'justifyRight']
                ]
            ],
            'editorConfig' => [
                'placeholder' => '请输入内容...',
                'autoFocus' => false,
                'scroll' => true,
                'height' => 300,
                'minHeight' => 150,
                'maxHeight' => 500,
                'fontSizes' => [
                    '12px', '13px', '14px', '15px', '16px', '19px', '22px', '24px', '29px', '32px', '40px'
                ],
                'fontFamilies' => [
                    ['name' => '宋体', 'value' => 'SimSun'],
                    ['name' => '黑体', 'value' => 'SimHei'],
                    ['name' => '微软雅黑', 'value' => 'Microsoft YaHei'],
                    ['name' => '楷体', 'value' => 'KaiTi'],
                    ['name' => '仿宋', 'value' => 'FangSong'],
                    ['name' => 'Arial', 'value' => 'Arial, Helvetica, sans-serif']
                ],
                'lineHeights' => ['1', '1.15', '1.6', '2', '2.5', '3']
            ],
            'uploadConfig' => [
                'server' => '/ajax/upload',
                'fieldName' => 'file'
            ]
        ];
        
        View::assign('editorConfig', json_encode($editorConfig));
        return View::fetch();
    }

    // 获取公告配置
    public function fetchData() {
        // 添加防止缓存的响应头
        header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
        header('Cache-Control: post-check=0, pre-check=0', false);
        header('Pragma: no-cache');
        
        // 如果是商家端访问，只返回启用状态的公告
        if ($this->scene == 'merchant') {
            $status = intval(plugconf("Merchantannouncements.status") ?? 1);
            if ($status != 1) {
                $this->success('success', ['status' => 0, 'announcement' => '']);
                return;
            }
        }

        $params = [
            'status' => intval(plugconf("Merchantannouncements.status") ?? 1),
            'announcement' => plugconf("Merchantannouncements.announcement") ?? '',
            'frequency' => plugconf("Merchantannouncements.frequency") ?? 'once',
            'read_enabled' => intval(plugconf("Merchantannouncements.read_enabled") ?? 1),
            'close_delay' => intval(plugconf("Merchantannouncements.close_delay") ?? 0),
            'edit_mode' => plugconf("Merchantannouncements.edit_mode") ?? 'rich'
        ];

        $this->success('success', $params);
    }

    // 保存公告配置
    public function save() {
        $status = $this->request->post('status/d', 1);
        $announcement = $this->request->post('announcement', '', 'trim');
        $frequency = $this->request->post('frequency', 'once', 'trim');
        $read_enabled = $this->request->post('read_enabled/d', 1);
        $close_delay = $this->request->post('close_delay/d', 0);
        $edit_mode = $this->request->post('edit_mode', 'rich', 'trim');

        // 验证 read_enabled 值
        if (!in_array($read_enabled, [0, 1])) {
            $read_enabled = 1; // 默认开启
        }

        // 验证frequency值
        if (!in_array($frequency, ['once', 'login', 'daily', 'weekly'])) {
            $this->error('弹出频率设置不正确');
        }

        // 验证倒计时值
        if ($close_delay < 0 || $close_delay > 60) {
            $this->error('倒计时设置不正确');
        }

        // 验证编辑模式
        if (!in_array($edit_mode, ['rich', 'html'])) {
            $edit_mode = 'rich'; // 默认富文本模式
        }

        // 内容安全检查
        if ($announcement !== '' && !is_string($announcement)) {
            $this->error('公告内容格式不正确');
        }

        // 如果是HTML模式，进行额外的安全过滤
        if ($edit_mode === 'html' && $announcement) {
            $announcement = $this->safeHtmlFilter($announcement);
        }

        // 保存配置
        plugconf("Merchantannouncements.status", $status);
        plugconf("Merchantannouncements.announcement", $announcement);
        plugconf("Merchantannouncements.frequency", $frequency);
        plugconf("Merchantannouncements.read_enabled", intval($read_enabled));
        plugconf("Merchantannouncements.close_delay", $close_delay);
        plugconf("Merchantannouncements.edit_mode", $edit_mode);

        $this->success('保存成功');
    }

    /**
     * 宽松的HTML过滤函数，支持复杂HTML布局，只移除最危险的JavaScript内容
     * 模仿Htmlpopup插件的安全过滤实现
     */
    private function safeHtmlFilter($html) {
        // 移除script标签
        $html = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $html);

        // 移除javascript:协议
        $html = preg_replace('/javascript:/i', '', $html);

        // 移除vbscript:协议
        $html = preg_replace('/vbscript:/i', '', $html);

        // 只移除明显的事件属性，保留其他所有属性
        $html = preg_replace('/\s*on(click|load|mouseover|mouseout|focus|blur|change|submit)\s*=\s*["\'][^"\']*["\']/i', '', $html);

        // 移除style属性中的expression (IE特有的危险功能)
        $html = preg_replace('/expression\s*\([^)]*\)/i', '', $html);

        return $html;
    }
}
