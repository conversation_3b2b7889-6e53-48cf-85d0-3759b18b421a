<?php

namespace plugin\Bulkregistration\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;

class Api extends BasePlugin {

    protected $scene = [
        'admin'
    ];
    protected $noNeedLogin = [];

    /**
     * 显示首页
     */
    public function index() {
        return View::fetch();
    }

    /**
     * 生成 bcrypt hash
     */
    protected function generateBcryptHash($password) {
        $options = [
            'cost' => 13
        ];
        return password_hash($password, PASSWORD_BCRYPT, $options);
    }

    /**
     * 
     * @param string 
     * @param string 
     * @return bool 
     */
    protected function verifyBcryptHash($password, $hash) {
        return password_verify($password, $hash);
    }

    /**
     * 批量注册用户
     */
    public function register() {
        try {
            $data = $this->request->post();
            \think\facade\Log::info('接收到的数据：' . json_encode($data, JSON_UNESCAPED_UNICODE));
            
            if (empty($data['users'])) {
                throw new \Exception('用户数据不能为空');
            }
            
            $users = $data['users'];
            if (!is_array($users)) {
                $users = json_decode($data['users'], true);
            }
            
            if (empty($users)) {
                throw new \Exception('解析用户数据失败');
            }

            $results = [
                'success' => 0,
                'skipped' => 0,
                'success_users' => [],
                'skipped_users' => [],
                'total' => count($users)
            ];

            Db::startTrans();
            try {
                foreach ($users as $index => $user) {
                    if (empty($user['username'])) {
                        throw new \Exception("第" . ($index + 1) . "行账号为空");
                    }
                    
                    $exists = Db::name('user')
                        ->where('username', $user['username'])
                        ->find();
                    
                    if ($exists) {
                        $results['skipped']++;
                        $results['skipped_users'][] = [
                            'username' => $user['username'],
                            'reason' => '账号已存在'
                        ];
                        continue;
                    }

                    $insertData = [
                        'username' => $user['username'],
                        'nickname' => $user['username'],
                        'password' => password_hash($user['password'], PASSWORD_DEFAULT),
                        'mobile' => $user['mobile'],
                        'create_time' => time(),
                        'update_time' => time()
                    ];
                    
                    Db::name('user')->insert($insertData);
                    $results['success']++;
                    $results['success_users'][] = $user['username'];
                }
                
                Db::commit();
                return json([
                    'code' => 200,
                    'msg' => sprintf('注册完成：成功 %d 个，已存在 %d 个', 
                        $results['success'], 
                        $results['skipped']
                    ),
                    'data' => $results
                ]);
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            \think\facade\Log::error('注册失败：' . $e->getMessage());
            return json([
                'code' => 500,
                'msg' => '注册失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 验证用户密码
     */
    public function verifyPassword() {
        try {
            $username = $this->request->post('username');
            $password = $this->request->post('password');

            $user = Db::name('user')
                ->where('username', $username)
                ->find();

            if (!$user) {
                throw new \Exception('用户不存在');
            }

            $isValid = $this->verifyBcryptHash($password, $user['password']);

            return json([
                'code' => 200,
                'data' => [
                    'valid' => $isValid
                ],
                'msg' => $isValid ? '密码正确' : '密码错误'
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '验证失败：' . $e->getMessage()
            ]);
        }
    }
} 