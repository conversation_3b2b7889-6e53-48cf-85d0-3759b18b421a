<html>
    <head>
        <title>我是文章页</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            .notice-box{
                margin-top: 32px;
            }

            .notice-item {
                background-color: #fff; /* 白色背景 */
                padding: 20px;
                border-radius: 8px; /* 圆角 */
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); /* 阴影效果 */
                margin-bottom: 20px;
                transition: transform 0.3s ease, box-shadow 0.3s ease; /* 动画效果 */
            }

            .notice-item:hover {
                transform: translateY(-5px); /* 鼠标悬浮时上移 */
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2); /* 鼠标悬浮时加重阴影 */
            }

            .notice-item h3 {
                font-size: 18px;
                font-weight: bold;
                color: #333; /* 文章标题颜色 */
                margin-bottom: 10px;
            }

            .notice-item h3 a {
                text-decoration: none; /* 去除链接下划线 */
                color: #007BFF; /* 标题链接颜色 */
                transition: color 0.3s ease; /* 动画效果 */
            }

            .notice-item h3 a:hover {
                color: #0056b3; /* 悬浮时颜色变深 */
            }

            .notice-item p {
                font-size: 14px;
                color: #666; /* 描述颜色 */
                line-height: 1.6;
                margin-bottom: 10px;
            }

            .notice-item .date {
                font-size: 12px;
                color: #999; /* 日期颜色 */
                font-style: italic;
            }

        </style>
    </head>
    <body>
        <div>{$title}【我是文章页】</div>
        <div class="notice-box">
            {foreach $notice as $item}
            <div class="notice-item">
                <h3><a href="{$item.url}">{$item.title}</a></h3>
                <p>{$item.content|raw}</p>
                <span class="date">{$item.create_time|date="Y-m-d H:i"}</span>
            </div>
            {/foreach}
        </div>
    </body>
</html>
