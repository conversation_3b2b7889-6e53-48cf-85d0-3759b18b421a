<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <title>投诉率详情</title>
    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <style>
        /* 基础布局 */
        .page-container {
            padding: 20px;
        }

        /* 页面头部 */
        .page-header {
            margin-bottom: 20px;
            padding: 10px 0;
            border-bottom: 1px solid #EBEEF5;
            display: flex;
            align-items: center;
        }

        .page-title {
            margin: 0;
            font-size: 20px;
            font-weight: 500;
            color: #303133;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        /* 卡片样式 */
        .info-card {
            margin-top: 20px;
            border-radius: 4px;
            border: 1px solid #EBEEF5;
            max-width: 900px;
            margin: 20px auto;
        }

        .info-card .el-card__header {
            padding: 12px 20px;
            border-bottom: 1px solid #EBEEF5;
            background-color: #f5f7fa;
        }

        /* 文本样式 */
        .rate-text {
            font-size: 24px;
            font-weight: bold;
            color: #409EFF;
        }

        .warning-text {
            color: #E6A23C;
        }

        .danger-text {
            color: #F56C6C;
        }

        .disabled-text {
            color: #909399;
            font-size: 14px;
        }

        .small-text {
            font-size: 12px;
            color: #909399;
        }

        .link-text {
            color: #409EFF;
            text-decoration: none;
            cursor: pointer;
        }

        .link-text:hover {
            color: #66b1ff;
        }

        /* 表格样式 */
        .el-table {
            margin-bottom: 20px;
        }

        .el-table th {
            background-color: #f5f7fa;
            color: #606266;
            font-weight: 500;
            padding: 8px 0;
        }

        .expire-time {
            font-size: 13px;
            color: #606266;
            margin-bottom: 5px;
        }

        .expire-warning {
            color: #F56C6C;
            font-weight: bold;
        }

        .level-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .highest-level-text {
            color: #67C23A;
            font-weight: bold;
        }

        .member-text {
            color: #67C23A;
            font-weight: bold;
        }

        .expire-time {
            font-size: 12px;
            color: #909399;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="page-container">
            <div class="page-header">
                <h2 class="page-title">
                    投诉率/费率公示
                    <span class="wallet-tip">
                        提前开通指定等级，您需要前往<a href="/merchant/user/wallet" class="wallet-link" target="_blank">【运营钱包】</a>充值对应金额
                    </span>
                </h2>
            </div>

            <!-- 投诉率信息卡片 -->
            <el-card class="info-card">
                <template #header>
                    <div class="card-header">
                        <span>我的投诉率信息</span>
                        <el-tag v-if="merchantData.is_member" type="success">会员特权</el-tag>
                    </div>
                </template>
                
                <el-descriptions :column="2" border>
                    <el-descriptions-item label="当前状态">
                        <template v-if="merchantData.is_member">
                            <span class="member-text">会员特权(无需计算投诉率)</span>
                            <div class="expire-time">
                                到期时间：{{ formatDate(merchantData.member_expire_time) }}
                            </div>
                        </template>
                        <template v-else>
                            <span :class="['rate-text', 
                                merchantData.complaint_rate > 10 ? 'danger-text' : 
                                merchantData.complaint_rate > 5 ? 'warning-text' : '']">
                                投诉率：{{ merchantData.complaint_rate || '0.00' }}%
                            </span>
                        </template>
                    </el-descriptions-item>
                    <el-descriptions-item label="投诉订单" v-if="!merchantData.is_member">
                        {{ merchantData.complaint_count || 0 }}/{{ merchantData.total_orders || 0 }}
                    </el-descriptions-item>
                    <el-descriptions-item label="当前等级">
                        {{ merchantData.current_group || '默认分组' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="到期时间">
                        {{ formatDate(merchantData.expire_time) }}
                    </el-descriptions-item>
                </el-descriptions>
            </el-card>

            <!-- 等级列表 -->
            <el-table :data="channelGroups" border style="width: 100%; margin-top: 20px;">
                <el-table-column prop="name" label="等级" width="120"></el-table-column>
                <el-table-column label="投诉率/订单" min-width="180">
                    <template #default="scope">
                        投诉率：{{ scope.row.complaint_rate }}%<br>
                        投诉订单：{{ scope.row.complaint_count }}单
                    </template>
                </el-table-column>
                <el-table-column label="费用" min-width="200">
                    <template #default="scope">
                        <template v-if="scope.row.id === 1">
                            <div>免费</div>
                        </template>
                        <template v-else>
                            <div>月费：{{ formatAmount(scope.row.price) }}元</div>
                            <div>季费：{{ formatAmount(scope.row.quarterly_price) }}元</div>
                            <div>年费：{{ formatAmount(scope.row.yearly_price) }}元</div>
                        </template>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="180" align="center">
                    <template #default="scope">
                        <template v-if="scope.row.id === 1">
                            <span class="default-group-text">默认分组</span>
                        </template>
                        <template v-else>
                            <template v-if="!scope.row.can_upgrade">
                                <el-tooltip effect="dark" :content="scope.row.disabled_message" placement="top">
                                    <span class="disabled-text">暂不开放</span>
                                </el-tooltip>
                            </template>
                            <template v-else>
                                <div v-if="scope.row.is_current" class="expire-time" :class="{ 'expire-warning': isExpireSoon(scope.row.expire_time) }">
                                    {{ formatDate(scope.row.expire_time) }}
                                </div>
                                <el-select v-model="scope.row.selected_cycle" size="small" style="margin-bottom: 8px;">
                                    <el-option label="包月" value="monthly" :title="`${formatAmount(scope.row.price)}元/月`"></el-option>
                                    <el-option label="包季" value="quarterly" :title="`${formatAmount(scope.row.quarterly_price)}元/季`"></el-option>
                                    <el-option label="包年" value="yearly" :title="`${formatAmount(scope.row.yearly_price)}元/年`"></el-option>
                                </el-select>
                                <el-button 
                                    type="primary" 
                                    size="small" 
                                    @click="handleUpgrade(scope.row.id, scope.row.selected_cycle)"
                                    :disabled="!scope.row.can_pay">
                                    {{ getButtonText(scope.row) }}
                                </el-button>
                            </template>
                        </template>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>

    <script>
        const app = Vue.createApp({
            setup() {
                const { ref, onMounted } = Vue;
                const merchantData = ref({});
                const channelGroups = ref([]);

                // 获取商户数据
                const getMerchantData = async () => {
                    try {
                        const res = await axios.post('/plugin/Complaintrate/merchant/getMerchantData');
                        if (res.data.code === 200) {
                            merchantData.value = res.data.data;
                        }
                    } catch (error) {
                        console.error('获取商户数据失败:', error);
                    }
                };

                // 获取等级数据
                const getChannelGroups = async () => {
                    try {
                        const res = await axios.post('/plugin/Complaintrate/merchant/channelGroups');
                        if (res.data.code === 200) {
                            channelGroups.value = res.data.data;
                        }
                    } catch (error) {
                        console.error('获取等级数据失败:', error);
                    }
                };

                onMounted(() => {
                    getMerchantData();
                    getChannelGroups();
                });

                const formatAmount = (amount) => {
                    return Number(amount || 0).toFixed(2);
                };

                const formatDate = (timestamp) => {
                    if (!timestamp) return '永久';
                    const date = new Date(timestamp * 1000);
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    const hour = String(date.getHours()).padStart(2, '0');
                    const minute = String(date.getMinutes()).padStart(2, '0');
                    const second = String(date.getSeconds()).padStart(2, '0');
                    return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
                };

                const getButtonText = (row) => {
                    if (!row.can_upgrade) {
                        return row.disabled_message || '不可开通';
                    }
                    if (row.is_current) {
                        return '续费';
                    }
                    return '开通';
                };

                const handleUpgrade = async (groupId, cycle) => {
                    try {
                        const res = await axios.post('/plugin/Complaintrate/merchant/upgradeLevel', {
                            group_id: groupId,
                            cycle: cycle
                        });

                        if (res.data.code === 200) {
                            ElementPlus.ElMessage.success(res.data.msg);
                            await Promise.all([
                                getMerchantData(),
                                getChannelGroups()
                            ]);
                        } else {
                            throw new Error(res.data.msg);
                        }
                    } catch (error) {
                        if (error.toString().includes('cancel')) {
                            return;
                        }
                        ElementPlus.ElMessage.error('操作失败: ' + error.message);
                    }
                };

                const isExpireSoon = (expireTime) => {
                    const currentTime = new Date().getTime() / 1000;
                    const expireTimestamp = new Date(expireTime * 1000).getTime() / 1000;
                    const timeUntilExpire = expireTimestamp - currentTime;
                    return timeUntilExpire < 86400; // 1 day in seconds
                };

                return {
                    merchantData,
                    channelGroups,
                    formatAmount,
                    formatDate,
                    getButtonText,
                    handleUpgrade,
                    isExpireSoon
                };
            }
        });

        app.use(ElementPlus);
        app.mount('#app');
    </script>
</body>
</html> 