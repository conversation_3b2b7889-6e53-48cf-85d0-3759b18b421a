<?php

namespace plugin\Waterchallenge\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Log;
use think\facade\Db;

class Api extends BasePlugin {

    protected $scene = ['admin'];  // 只允许管理员访问
    
    // 管理面板首页
    public function index() {
        return View::fetch('api/index');  // 修改这里，指定正确的模板路径
    }
    
    // 获取挑战配置
    public function getChallengeConfig() {
        try {
            // 从数据库获取挑战规则
            $rules = Db::name('plugin_waterchallenge_rules')
                ->select()
                ->toArray();
            
            // 确保规则中的数值字段是数字类型
            if (!empty($rules)) {
                $now = time();
                foreach ($rules as &$rule) {
                    $rule['turnover_amount'] = floatval($rule['turnover_amount']);
                    $rule['reward_amount'] = floatval($rule['reward_amount']);
                    $rule['valid_days'] = intval($rule['valid_days']);
                    $rule['challenge_duration'] = intval($rule['challenge_duration']);
                    $rule['max_attempts'] = isset($rule['max_attempts']) ? intval($rule['max_attempts']) : 0; // 确保最大尝试次数是整数，0表示无限制
                    
                    // 计算规则有效期
                    $createTime = strtotime($rule['create_time']);
                    $endTime = $createTime + ($rule['valid_days'] * 86400);
                    $remainingDays = ceil(($endTime - $now) / 86400);
                    
                    // 添加额外信息
                    $rule['remaining_days'] = max(0, $remainingDays);
                    $rule['is_expired'] = ($now >= $endTime) ? 1 : 0;
                    $rule['end_date'] = date('Y-m-d H:i:s', $endTime);
                }
            }
            
            return json([
                'code' => 200, 
                'msg' => '获取成功', 
                'data' => [
                    'rules' => $rules
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取挑战配置失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取配置失败：' . $e->getMessage()]);
        }
    }

    // 保存挑战规则
    public function saveRule() {
        try {
            $data = input('post.');
            
            // 添加调试日志
            Log::info('保存规则数据：' . json_encode($data));
            
            // 验证数据
            if (empty($data['name'])) {
                return json(['code' => 400, 'msg' => '规则名称不能为空']);
            }
            
            if (empty($data['turnover_amount']) || $data['turnover_amount'] <= 0) {
                return json(['code' => 400, 'msg' => '目标流水必须大于0']);
            }
            
            if (empty($data['reward_amount']) || $data['reward_amount'] <= 0) {
                return json(['code' => 400, 'msg' => '奖励金额必须大于0']);
            }

            if (empty($data['valid_days']) || $data['valid_days'] <= 0) {
                return json(['code' => 400, 'msg' => '请设置有效天数']);
            }

            if (empty($data['challenge_duration']) || $data['challenge_duration'] <= 0) {
                return json(['code' => 400, 'msg' => '请设置有效的挑战时长']);
            }

            // 处理数据
            $saveData = [
                'name' => $data['name'],
                'turnover_amount' => floatval($data['turnover_amount']),
                'reward_amount' => floatval($data['reward_amount']),
                'valid_days' => intval($data['valid_days']),
                'challenge_duration' => intval($data['challenge_duration']),
                'max_attempts' => isset($data['max_attempts']) ? intval($data['max_attempts']) : 0, // 添加最大参与次数字段，0表示无限制
                'status' => isset($data['status']) ? intval($data['status']) : 1,
                'update_time' => date('Y-m-d H:i:s')
            ];

            // 如果是编辑
            if (!empty($data['id'])) {
                // 更新数据库记录
                $result = Db::name('plugin_waterchallenge_rules')
                    ->where('id', $data['id'])
                    ->update($saveData);
                    
                if (!$result) {
                    return json(['code' => 500, 'msg' => '更新规则失败']);
                }
            } else {
                // 添加新规则
                $saveData['rule_id'] = uniqid('rule_');
                $saveData['create_time'] = date('Y-m-d H:i:s');
                
                $result = Db::name('plugin_waterchallenge_rules')
                    ->insert($saveData);
                    
                if (!$result) {
                    return json(['code' => 500, 'msg' => '添加规则失败']);
                }
            }

            return json(['code' => 200, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            Log::error('保存挑战规则失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    // 删除挑战规则
    public function deleteRule() {
        try {
            $id = input('id/d', 0);
            
            if ($id <= 0) {
                return json(['code' => 400, 'msg' => '无效的规则ID']);
            }

            // 从数据库中删除规则
            $result = Db::name('plugin_waterchallenge_rules')
                ->where('id', $id)
                ->delete();
                
            if (!$result) {
                return json(['code' => 500, 'msg' => '删除规则失败']);
            }

            return json(['code' => 200, 'msg' => '删除成功']);
        } catch (\Exception $e) {
            Log::error('删除挑战规则失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }

    // 获取统计数据
    public function getData() {
        try {
            // 从数据库获取规则列表
            $rules = Db::name('plugin_waterchallenge_rules')
                ->select()
                ->toArray();
                
            // 计算规则有效期
            $now = time();
            foreach ($rules as &$rule) {
                $rule['turnover_amount'] = floatval($rule['turnover_amount']);
                $rule['reward_amount'] = floatval($rule['reward_amount']);
                $rule['valid_days'] = intval($rule['valid_days']);
                $rule['challenge_duration'] = intval($rule['challenge_duration']);
                
                // 计算规则有效期
                $createTime = strtotime($rule['create_time']);
                $endTime = $createTime + ($rule['valid_days'] * 86400);
                $remainingDays = ceil(($endTime - $now) / 86400);
                
                // 添加额外信息
                $rule['remaining_days'] = max(0, $remainingDays);
                $rule['is_expired'] = ($now >= $endTime) ? 1 : 0;
                $rule['end_date'] = date('Y-m-d H:i:s', $endTime);
            }
            
            // 获取搜索参数
            $merchant = input('merchant', '');
            $status = input('status', '');
            
            // 构建查询条件
            $where = [];
            if (!empty($merchant)) {
                // 先根据用户名查找用户ID
                $userIds = Db::name('user')
                    ->whereLike('username', "%{$merchant}%")
                    ->column('id');
                
                if (!empty($userIds)) {
                    $where[] = ['user_id', 'in', $userIds];
                } else {
                    // 如果没有匹配的用户，返回空结果
                    $where[] = ['user_id', '=', 0];
                }
            }
            
            if (!empty($status)) {
                $where[] = ['status', '=', $status];
            }
            
            // 从数据库获取挑战记录
            $records = Db::name('plugin_waterchallenge_user')
                ->where($where)
                ->select()
                ->toArray();
                
            // 添加用户名称信息并转换数值型字段
            if (!empty($records)) {
                $userIds = array_column($records, 'user_id');
                $userMap = Db::name('user')
                    ->whereIn('id', $userIds)
                    ->column('username', 'id');

                foreach ($records as &$record) {
                    $record['merchant_name'] = $userMap[$record['user_id']] ?? '未知用户';
                    // 确保数值字段为数字类型
                    $record['target_amount'] = floatval($record['target_amount']);
                    $record['reward_amount'] = floatval($record['reward_amount']);
                    $record['base_amount'] = floatval($record['base_amount']);

                    // 对于进行中的挑战，实时计算当前流水
                    if ($record['status'] === 'ongoing') {
                        // 获取用户当前总流水
                        $currentTotalAmount = floatval(Db::name('user_analysis')
                            ->where('user_id', $record['user_id'])
                            ->sum('total_amount')) ?: 0;

                        // 计算挑战流水（当前总流水 - 基准流水）
                        $record['current_amount'] = round(max(0, $currentTotalAmount - $record['base_amount']), 3);

                        // 同时更新数据库中的值，保持数据一致性
                        Db::name('plugin_waterchallenge_user')
                            ->where('id', $record['id'])
                            ->update([
                                'current_amount' => $record['current_amount'],
                                'update_time' => date('Y-m-d H:i:s')
                            ]);
                    } else {
                        // 对于已完成或失败的挑战，使用数据库中的值
                        $record['current_amount'] = floatval($record['current_amount']);
                    }

                    // 计算完成百分比
                    $record['progress_percentage'] = $record['target_amount'] > 0
                        ? min(100, round(($record['current_amount'] / $record['target_amount']) * 100, 1))
                        : 0;
                }
            }
            
            // 统计数据
            $stats = [
                'totalChallenges' => 0,
                'activeChallenges' => 0,
                'completedChallenges' => 0,
                'totalRewards' => 0
            ];
            
            // 计算统计数据
            foreach ($records as $record) {
                $stats['totalChallenges']++;
                if ($record['status'] === 'completed') {
                    $stats['completedChallenges']++;
                    if ($record['reward_sent']) {
                        $stats['totalRewards'] += $record['reward_amount'];
                    }
                } elseif ($record['status'] === 'ongoing') {
                    $stats['activeChallenges']++;
                }
            }

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => [
                    'stats' => $stats,
                    'rules' => $rules,
                    'records' => $records,
                    'total' => count($records)
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取数据失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取数据失败：' . $e->getMessage()]);
        }
    }

    // 更新规则状态
    public function updateStatus() {
        try {
            $id = input('id/d', 0);
            $status = input('status/d', 0);
            
            if ($id <= 0) {
                return json(['code' => 400, 'msg' => '无效的规则ID']);
            }

            // 更新数据库中的规则状态
            $result = Db::name('plugin_waterchallenge_rules')
                ->where('id', $id)
                ->update([
                    'status' => $status,
                    'update_time' => date('Y-m-d H:i:s')
                ]);
                
            if (!$result) {
                return json(['code' => 500, 'msg' => '更新规则状态失败']);
            }

            return json(['code' => 200, 'msg' => '状态更新成功']);
        } catch (\Exception $e) {
            Log::error('更新规则状态失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '更新状态失败：' . $e->getMessage()]);
        }
    }

    // 清空挑战记录
    public function clearRecords() {
        try {
            // 清空数据表中的挑战记录
            Db::name('plugin_waterchallenge_user')->delete(true);

            return json(['code' => 200, 'msg' => '清空记录成功']);
        } catch (\Exception $e) {
            Log::error('清空挑战记录失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '清空记录失败：' . $e->getMessage()]);
        }
    }
}