<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>平台数据管理</title>
</head>
<body>
<div id="app">
    <el-card shadow="never">
        <el-form :inline="true" class="demo-form-inline" style="margin-bottom: 20px;">
            <el-form-item label="日期范围">
                <el-date-picker
                    v-model="dateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :shortcuts="dateShortcuts"
                    format="YYYY年MM月DD日"
                    value-format="YYYY-MM-DD">
                </el-date-picker>
            </el-form-item>
            <el-form-item label="商户ID">
                <el-input v-model="merchantId" placeholder="请输入商户ID"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="handleSearch">查询</el-button>
                <el-button @click="handleReset">重置</el-button>
            </el-form-item>
        </el-form>

        <div style="text-align: center; margin: 16px 0;">
            <el-button type="danger" :loading="isClearing" @click="confirmClearData">
                {{ hasFilter ? '清空筛选数据' : '清空全部数据' }}
            </el-button>
            
            <el-popover
                placement="bottom"
                width="300"
                trigger="click">
                <template #reference>
                    <el-button type="primary" style="margin-left: 10px">
                        自动清除设置
                    </el-button>
                </template>
                <div>
                    <el-form label-width="80px">
                        <el-form-item label="自动清除">
                            <el-switch
                                v-model="autoConfig.auto_clear"
                                :active-value="1"
                                :inactive-value="0"
                                @change="saveConfig">
                            </el-switch>
                        </el-form-item>
                        <el-form-item label="清除周期">
                            <el-select 
                                v-model="autoConfig.clear_type" 
                                placeholder="选择清除周期"
                                @change="saveConfig">
                                <el-option label="每天" value="daily"></el-option>
                                <el-option label="每周" value="weekly"></el-option>
                                <el-option label="每月" value="monthly"></el-option>
                                <el-option label="每季度" value="quarterly"></el-option>
                                <el-option label="每年" value="yearly"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="清除时间" v-if="autoConfig.clear_type === 'daily'">
                            <el-time-select
                                v-model="autoConfig.clear_time"
                                start="00:00"
                                step="00:01"
                                end="23:59"
                                placeholder="选择时间"
                                @change="saveConfig">
                            </el-time-select>
                        </el-form-item>
                        <el-form-item label="执行日期" v-if="autoConfig.clear_type === 'weekly'">
                            <el-select 
                                v-model="autoConfig.clear_day" 
                                placeholder="选择星期"
                                @change="saveConfig">
                                <el-option label="星期一" value="1"></el-option>
                                <el-option label="星期二" value="2"></el-option>
                                <el-option label="星期三" value="3"></el-option>
                                <el-option label="星期四" value="4"></el-option>
                                <el-option label="星期五" value="5"></el-option>
                                <el-option label="星期六" value="6"></el-option>
                                <el-option label="星期日" value="0"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="执行日期" v-if="autoConfig.clear_type === 'monthly'">
                            <el-input-number 
                                v-model="autoConfig.clear_day" 
                                :min="1" 
                                :max="31"
                                placeholder="日期"
                                @change="saveConfig">
                            </el-input-number>
                        </el-form-item>
                        <el-form-item label="执行月份" v-if="autoConfig.clear_type === 'quarterly'">
                            <el-select 
                                v-model="autoConfig.clear_month" 
                                placeholder="选择月份"
                                @change="saveConfig">
                                <el-option label="3月" value="3"></el-option>
                                <el-option label="6月" value="6"></el-option>
                                <el-option label="9月" value="9"></el-option>
                                <el-option label="12月" value="12"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="执行月份" v-if="autoConfig.clear_type === 'yearly'">
                            <el-input-number 
                                v-model="autoConfig.clear_month" 
                                :min="1" 
                                :max="12"
                                placeholder="月份"
                                @change="saveConfig">
                            </el-input-number>
                        </el-form-item>
                    </el-form>
                </div>
            </el-popover>
        </div>

        <el-table :data="tableData" style="width: 100%; margin-top: 20px;">
            <el-table-column prop="id" label="ID" width="50"></el-table-column>
            <el-table-column prop="user_id" label="用户ID"></el-table-column>
            <el-table-column prop="date" label="时间" :formatter="formatDate"></el-table-column>
            <el-table-column prop="order_count" label="订单数"></el-table-column>
            <el-table-column prop="total_amount" label="总金额"></el-table-column>
            <el-table-column prop="fee" label="手续费"></el-table-column>
            <el-table-column prop="cost_amount" label="成本金额"></el-table-column>
        </el-table>

        <el-pagination
            style="margin-top: 20px; text-align: center;"
            :current-page="pagination.page"
            :page-size="pagination.pageSize"
            :total="pagination.total"
            @current-change="handlePageChange">
        </el-pagination>
    </el-card>
</div>

<script src="/static/others/vue/vue.global.prod.js"></script>
<script src="/static/others/element-plus/index.full.min.js"></script>
<script src="/static/others/axios/axios.min.js"></script>
<script src="/static/others/element-plus/locale/zh-cn.min.js"></script>

<script>
const { ref, reactive, computed } = Vue;
const { ElMessage, ElMessageBox } = ElementPlus;

const app = Vue.createApp({
    setup() {
        const isLoading = ref(false);
        const isClearing = ref(false);
        const tableData = ref([]);
        const dateRange = ref([]);
        const merchantId = ref('');
        const pagination = reactive({
            total: 0,
            page: 1,
            pageSize: 10,
            totalPages: 0,
        });
        const autoConfig = reactive({
            auto_clear: 0,
            clear_type: 'daily',
            clear_time: '00:00',
            clear_day: 1,
            clear_month: 1
        });

        const hasFilter = computed(() => {
            return dateRange.value?.length > 0 || merchantId.value !== '';
        });

        const fetchData = async (page = 1) => {
            isLoading.value = true;
            try {
                const params = {
                    page,
                    startDate: dateRange.value?.[0] || '',
                    endDate: dateRange.value?.[1] || '',
                    merchantId: merchantId.value
                };
                
                const res = await axios.post("/plugin/Zeroallsum/api/fetchData", params);
                if (res.data?.code === 200) {
                    tableData.value = res.data.data.tableData || [];
                    Object.assign(pagination, res.data.data.pagination || {});
                } else {
                    ElMessage.error(res.data?.msg || '获取数据失败');
                }
            } catch (error) {
                ElMessage.error(error.message || '请求失败');
            } finally {
                isLoading.value = false;
            }
        };

        const clearData = async () => {
            isClearing.value = true;
            try {
                const params = {
                    startDate: dateRange.value?.[0] || '',
                    endDate: dateRange.value?.[1] || '',
                    merchantId: merchantId.value
                };
                
                const res = await axios.post("/plugin/Zeroallsum/api/clearData", params);
                if (res.data?.code === 200) {
                    ElMessage.success('清空成功');
                    fetchData();
                } else {
                    ElMessage.error(res.data?.msg || '清空失败');
                }
            } catch (error) {
                ElMessage.error(error.message || '请求失败');
            } finally {
                isClearing.value = false;
            }
        };

        const confirmClearData = () => {
            if (tableData.value.length === 0) {
                ElMessage.warning('没有数据可清空');
                return;
            }

            const message = hasFilter.value 
                ? '确定要清空筛选出的数据吗？' 
                : '确定要清空所有数据吗？';

            ElMessageBox.confirm(message, '警告', {
                confirmButtonText: '清空',
                cancelButtonText: '取消',
                type: 'warning',
            })
            .then(clearData)
            .catch(() => {
                ElMessage.info('已取消清空数据');
            });
        };

        const getConfig = async () => {
            try {
                const res = await axios.post("/plugin/Zeroallsum/api/getAutoConfig");
                if (res.data?.code === 1) {
                    Object.assign(autoConfig, {
                        auto_clear: res.data.data.auto_clear,
                        clear_type: res.data.data.clear_type,
                        clear_time: res.data.data.clear_time,
                        clear_day: res.data.data.clear_day,
                        clear_month: res.data.data.clear_month
                    });
                } else {
                    throw new Error(res.data?.msg || '获取配置失败');
                }
            } catch (error) {
                console.error('获取配置错误:', error);
                ElMessage.error(error.response?.data?.msg || error.message || '获取配置失败');
            }
        };

        const saveConfig = async () => {
            try {
                const res = await axios.post("/plugin/Zeroallsum/api/save", autoConfig);
                if (res.data?.code === 1) {
                    ElMessage.success('保存成功');
                } else {
                    throw new Error(res.data?.msg || '保存失败');
                }
            } catch (error) {
                console.error('保存配置错误:', error);
                ElMessage.error(error.response?.data?.msg || error.message || '保存失败');
            }
        };

        const handlePageChange = (newPage) => {
            pagination.page = newPage;
            fetchData(newPage);
        };

        const formatDate = (row, column, cellValue) => {
            if (!cellValue) return '';
            const date = new Date(cellValue);
            return date.toLocaleDateString();
        };

        const handleSearch = () => {
            pagination.page = 1;
            fetchData();
        };

        const handleReset = () => {
            dateRange.value = [];
            merchantId.value = '';
            pagination.page = 1;
            fetchData();
        };

        const dateShortcuts = [
            {
                text: '最近一周',
                value: () => {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                    return [start, end];
                },
            },
            {
                text: '最近一个月',
                value: () => {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                    return [start, end];
                },
            },
            {
                text: '最近三个月',
                value: () => {
                    const end = new Date();
                    const start = new Date();
                    start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                    return [start, end];
                },
            },
            {
                text: '本月',
                value: () => {
                    const end = new Date();
                    const start = new Date();
                    start.setDate(1);
                    return [start, end];
                },
            },
            {
                text: '上个月',
                value: () => {
                    const end = new Date();
                    const start = new Date();
                    start.setMonth(start.getMonth() - 1);
                    start.setDate(1);
                    end.setDate(0);
                    return [start, end];
                },
            }
        ];

        fetchData();
        getConfig();

        return {
            isLoading,
            isClearing,
            tableData,
            pagination,
            dateRange,
            merchantId,
            autoConfig,
            hasFilter,
            fetchData,
            clearData,
            confirmClearData,
            handlePageChange,
            formatDate,
            handleSearch,
            handleReset,
            dateShortcuts,
            saveConfig
        };
    }
});

app.use(ElementPlus, {
    locale: ElementPlusLocaleZhCn
});

app.mount("#app");
</script>
</body>
</html>
