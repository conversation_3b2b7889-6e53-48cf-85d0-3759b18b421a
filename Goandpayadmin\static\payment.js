(function () {
    // 获取店铺信息
    function getShopInfo() {
        let shopName = '';
        let merchantId = '';
        
        // 从用户模型获取店铺信息（通过ShopSettingBefore和ShopSettingAfter钩子函数提供）
        const shopInfoScript = document.querySelector('script[data-shop-info]');
        if (shopInfoScript) {
            try {
                const shopInfo = JSON.parse(shopInfoScript.textContent);
                if (shopInfo.shopName) {
                    shopName = shopInfo.shopName;
                }
                if (shopInfo.merchantId) {
                    merchantId = shopInfo.merchantId;
                }
            } catch (e) {
                console.error('解析商家信息失败:', e);
            }
        }
        
        // 如果无法通过钩子函数获取，则尝试其他方式
        if (!shopName) {
            // 1. 从 nickname 元素获取商家名称
            const nicknameElement = document.querySelector('.nickname');
            if (nicknameElement) {
                shopName = nicknameElement.textContent.trim();
            }
        }

        // 2. 从带有店铺名称class的元素获取
        if (!shopName) {
            const shopNameElements = document.querySelectorAll('.shop-name, .store-name, .merchant-name');
            for (const element of shopNameElements) {
                const text = element.textContent.trim();
                if (text) {
                    shopName = text;
                    break;
                }
            }
        }

        // 3. 从页面标题获取商家名称
        if (!shopName) {
            const pageTitle = document.title.trim();
            if (pageTitle) {
                const index = pageTitle.indexOf('的小店');
                if (index !== -1) {
                    shopName = pageTitle.substring(0, index);
                }
            }
        }

        // 4. 从商家ID属性元素获取
        if (!merchantId) {
            const merchantIdElement = document.querySelector('[data-merchant-id]');
            if (merchantIdElement) {
                merchantId = merchantIdElement.getAttribute('data-merchant-id');
            }
        }
        
        // 5. 从 meta 标签获取
        if (!shopName) {
            const metaTags = [
                document.querySelector('meta[name="shop-name"]'),
                document.querySelector('meta[property="og:site_name"]'),
                document.querySelector('meta[name="author"]')
            ];
            
            for (const tag of metaTags) {
                if (tag && tag.getAttribute('content')) {
                    shopName = tag.getAttribute('content');
                    break;
                }
            }
        }
        
        if (!merchantId) {
            const merchantIdMeta = document.querySelector('meta[name="merchant-id"]');
            if (merchantIdMeta) {
                merchantId = merchantIdMeta.getAttribute('content');
            }
        }
        
        // 6. 从 URL 参数获取（备用方式）
        if (!shopName || !merchantId) {
            const urlParams = new URLSearchParams(window.location.search);
            if (!shopName) {
                const urlShopName = urlParams.get('shop_name');
                if (urlShopName) shopName = urlShopName;
            }
            if (!merchantId) {
                const urlMerchantId = urlParams.get('merchant_id');
                if (urlMerchantId) merchantId = urlMerchantId;
            }
        }

        console.log('获取到店铺信息:', { shopName, merchantId });
        return { shopName, merchantId };
    }

    // 查找去支付按钮
    function findPayButton() {
        // 获取所有按钮
        const allButtons = document.querySelectorAll('button, .arco-btn, .el-button');
        // 查找包含"去支付"文本的按钮
        for (const button of allButtons) {
            if (button.textContent && button.textContent.trim().includes('去支付')) {
                return button;
            }
        }
        return null;
    }

    // 添加新的去支付按钮
    function addNewPayButton(originalButton, config) {
        if (!originalButton || !config || !config.payment_notice) return;
        
        // 检查是否已经添加了新按钮
        if (document.querySelector('.goandpay-new-button')) {
            return;
        }
        
        // 创建新按钮，与原按钮样式相同
        const newButton = document.createElement('button');
        newButton.textContent = '去支付';
        newButton.className = originalButton.className + ' goandpay-new-button';
        
        // 复制原按钮的样式
        const computedStyle = window.getComputedStyle(originalButton);
        newButton.style.cssText = computedStyle.cssText;
        
        // 确保新按钮样式与原按钮一致
        newButton.style.backgroundColor = '#4080ff';
        newButton.style.color = 'white';
        newButton.style.border = 'none';
        newButton.style.borderRadius = '4px';
        newButton.style.padding = '8px 16px';
        newButton.style.cursor = 'pointer';
        newButton.style.fontSize = '14px';
        newButton.style.margin = '0 0 10px 0'; // 在底部添加间距
        newButton.style.width = computedStyle.width;
        newButton.style.height = computedStyle.height;
        
        // 添加点击事件
        newButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // 显示配置的内容（如果有）
            if (config.payment_notice) {
                // 创建内容弹窗
                const notification = document.createElement('div');
                notification.className = 'goandpay-notification';
                
                // 获取倒计时时间，默认为3秒
                let countdown = parseInt(config.close_delay) || 3;
                
                notification.innerHTML = `
                    <div class="goandpay-notice-header">支付前提示</div>
                    <div class="goandpay-notice-content">${config.payment_notice}</div>
                    <div class="goandpay-notice-footer">
                        <div class="goandpay-countdown">${countdown}秒后按钮可点击</div>
                        <button class="goandpay-submit-btn" ${countdown > 0 ? 'disabled' : ''}>立即前往支付</button>
                    </div>
                `;
                
                // 添加样式
                const style = document.createElement('style');
                style.textContent = `
                    .goandpay-notification {
                        position: fixed;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        background: white;
                        border-radius: 8px;
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                        z-index: 9999;
                        width: 380px;
                        max-width: 90%;
                        overflow: hidden;
                        display: flex;
                        flex-direction: column;
                    }
                    .goandpay-notice-header {
                        font-size: 16px;
                        font-weight: bold;
                        padding: 15px 20px;
                        border-bottom: 1px solid #f0f0f0;
                    }
                    .goandpay-notice-content {
                        padding: 20px;
                        color: #333;
                        line-height: 1.6;
                        min-height: 60px;
                        max-height: 60vh;
                        overflow-y: auto;
                        flex: 1;
                    }
                    /* 富文本内容样式 - 与编辑器样式一致 */
                    .goandpay-notice-content p {
                        margin: 10px 0;
                        line-height: 1.6;
                    }
                    .goandpay-notice-content img {
                        max-width: 100%;
                        height: auto;
                    }
                    .goandpay-notice-content a {
                        color: #4080ff;
                        text-decoration: none;
                    }
                    .goandpay-notice-content a:hover {
                        text-decoration: underline;
                    }
                    .goandpay-notice-content blockquote {
                        padding: 10px 15px;
                        margin: 10px 0;
                        border-left: 4px solid #e2e2e2;
                        background-color: #f5f5f5;
                    }
                    .goandpay-notice-content h1, 
                    .goandpay-notice-content h2, 
                    .goandpay-notice-content h3, 
                    .goandpay-notice-content h4, 
                    .goandpay-notice-content h5, 
                    .goandpay-notice-content h6 {
                        margin: 15px 0 10px;
                        font-weight: bold;
                        line-height: 1.4;
                    }
                    .goandpay-notice-content h1 {
                        font-size: 20px;
                    }
                    .goandpay-notice-content h2 {
                        font-size: 18px;
                    }
                    .goandpay-notice-content h3 {
                        font-size: 16px;
                    }
                    .goandpay-notice-content table {
                        border-collapse: collapse;
                        margin: 10px 0;
                        width: 100%;
                    }
                    .goandpay-notice-content table td,
                    .goandpay-notice-content table th {
                        border: 1px solid #ddd;
                        padding: 8px;
                    }
                    .goandpay-notice-content ul, 
                    .goandpay-notice-content ol {
                        padding-left: 20px;
                        margin: 10px 0;
                    }
                    .goandpay-notice-content code {
                        background-color: #f0f0f0;
                        padding: 2px 4px;
                        border-radius: 3px;
                        font-family: monospace;
                    }
                    .goandpay-notice-footer {
                        padding: 15px 20px;
                        border-top: 1px solid #f0f0f0;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    }
                    .goandpay-countdown {
                        color: #666;
                        font-size: 14px;
                    }
                    .goandpay-submit-btn {
                        background-color: #4080ff;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        padding: 8px 16px;
                        cursor: pointer;
                        font-size: 14px;
                    }
                    .goandpay-submit-btn:not([disabled]):hover {
                        background-color: #3070ee;
                    }
                    .goandpay-submit-btn[disabled] {
                        background-color: #a0bfff;
                        cursor: not-allowed;
                    }
                `;
                document.head.appendChild(style);
                document.body.appendChild(notification);
                
                // 获取立即前往支付按钮
                const submitBtn = notification.querySelector('.goandpay-submit-btn');
                
                // 如果倒计时大于0，则禁用按钮并开始倒计时
                if (countdown > 0) {
                    const countdownInterval = setInterval(() => {
                        countdown--;
                        const countdownEl = notification.querySelector('.goandpay-countdown');
                        if (countdownEl) {
                            countdownEl.textContent = `${countdown}秒后按钮可点击`;
                        }
                        
                        if (countdown <= 0) {
                            clearInterval(countdownInterval);
                            // 启用按钮
                            if (submitBtn) {
                                submitBtn.removeAttribute('disabled');
                                const countdownEl = notification.querySelector('.goandpay-countdown');
                                if (countdownEl) {
                                    countdownEl.textContent = `请点击按钮前往支付`;
                                }
                            }
                            // 不再自动跳转，等待用户点击
                        }
                    }, 1000);
                }
                
                // 添加按钮点击事件
                if (submitBtn) {
                    submitBtn.addEventListener('click', () => {
                        notification.remove();
                        newButton.remove();
                        originalButton.click();
                    });
                }
                
                // 如果启用了语音朗读
                if (config.read_enabled && parseInt(config.read_enabled) === 1) {
                    try {
                        // 提取纯文本
                        const tempDiv = document.createElement('div');
                        tempDiv.innerHTML = config.payment_notice;
                        const plainText = tempDiv.textContent || tempDiv.innerText || '';
                        
                        if (plainText && window.speechSynthesis) {
                            const utterance = new SpeechSynthesisUtterance(plainText);
                            // 设置中文语音
                            utterance.lang = 'zh-CN';
                            utterance.rate = 1.0; // 语速
                            window.speechSynthesis.speak(utterance);
                            
                            // 弹窗关闭时停止语音
                            const stopSpeech = () => {
                                window.speechSynthesis.cancel();
                            };
                            submitBtn.addEventListener('click', stopSpeech);
                        }
                    } catch (e) {
                        console.error('语音朗读失败:', e);
                    }
                }
            } else {
                // 如果没有配置内容，直接触发原始按钮
                newButton.remove();
                originalButton.click();
            }
        });
        
        // 将新按钮添加到原按钮之前
        originalButton.parentNode.insertBefore(newButton, originalButton);
        
        // 隐藏原始按钮
        originalButton.style.visibility = 'hidden';
        originalButton.style.position = 'absolute';
        originalButton.style.pointerEvents = 'none';
    }

    // 获取弹窗配置
    function fetchPaymentNoticeConfig() {
        const { shopName, merchantId } = getShopInfo();
        const timestamp = new Date().getTime(); // 防止缓存
        
        // 构建API请求URL
        let apiUrl = '/plugin/Goandpay/Api/fetchData';
        
        // 添加参数
        const params = new URLSearchParams();
        if (shopName) params.append('shop_name', shopName);
        if (merchantId) params.append('merchant_id', merchantId);
        params.append('t', timestamp);
        
        // 组合最终URL
        apiUrl = apiUrl + '?' + params.toString();
        
        // 发送请求
        return fetch(apiUrl)
            .then(response => response.json())
            .then(data => {
                if (data.code === 200 && data.data) {
                    // 保存配置到全局变量
                    window.paymentNoticeConfig = data.data;
                    return data.data;
                }
                return null;
            })
            .catch(error => {
                console.error('获取支付弹窗配置失败:', error);
                return null;
            });
    }

    // 初始化函数
    function init() {
        // 获取配置
        fetchPaymentNoticeConfig().then(config => {
            if (!config || !config.status) return;
            
            // 查找页面中的去支付按钮
            const payButton = findPayButton();
            if (payButton) {
                // 添加新的去支付按钮
                addNewPayButton(payButton, config);
            }
            
            // 监听DOM变化，处理动态加载的按钮
            const observer = new MutationObserver((mutations) => {
                for (const mutation of mutations) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        // 如果页面中新增了节点，尝试再次查找去支付按钮
                        const currentPayButton = findPayButton();
                        if (currentPayButton && !document.querySelector('.goandpay-new-button')) {
                            addNewPayButton(currentPayButton, config);
                            // 找到按钮后可以断开观察
                            break;
                        }
                    }
                }
            });
            
            // 开始观察整个文档的变化
            observer.observe(document.body, { childList: true, subtree: true });
        });
    }

    // 当DOM加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
})(); 