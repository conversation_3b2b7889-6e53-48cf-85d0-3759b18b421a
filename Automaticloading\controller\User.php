<?php
/**
 * 用户数据处理控制器
 * 处理用户上传的数据并自动匹配商品
 */

namespace plugin\Automaticloading\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Log;
use think\facade\Db;
use think\response\Json;
use PDO;

class User extends BasePlugin {
    
    protected $scene = ['user'];  // 只允许普通用户访问
    
    protected $noNeedLogin = ['process_data','getHistory','clearRoleCache','clearAllRoleCache'];  // 空数组表示所有方法都需要登录


    // 显示上传页面
    public function index() {
        $config = include app()->getRootPath() . 'plugin/Automaticloading/params.php';
        if (!isset($config['auto_loading_config']['status']) || !$config['auto_loading_config']['status']) {
            return $this->error('自动匹配功能已关闭');
        }
        return View::fetch();
    }
    
    /**
     * 处理接收到的用户数据
     */
    public function process_data() {
        try {
            // 改进请求方法检查，更灵活地处理请求方法
            $requestMethod = isset($_SERVER['REQUEST_METHOD']) ? $_SERVER['REQUEST_METHOD'] : 'UNKNOWN';
            $contentType = isset($_SERVER['CONTENT_TYPE']) ? $_SERVER['CONTENT_TYPE'] : '';
            
            Log::info('请求方法: ' . $requestMethod . ', Content-Type: ' . $contentType);
            
            // 获取并验证用户数据，增加多种获取方式，同时兼容GET和POST请求
            $userData = '';
            
            // 尝试从POST数据获取
            if (input('post.userData', '', 'trim')) {
                $userData = input('post.userData', '', 'trim');
            } elseif (request()->post('userData')) {
                $userData = request()->post('userData');
            } 
            // 尝试从GET参数获取
            elseif (input('get.userData', '', 'trim')) {
                $userData = input('get.userData', '', 'trim');
            } elseif (request()->get('userData')) {
                $userData = request()->get('userData');
            }
            // 尝试从原始输入流获取数据
            else {
                $rawInput = file_get_contents('php://input');
                $jsonData = json_decode($rawInput, true);
                if (is_array($jsonData) && isset($jsonData['userData'])) {
                    $userData = $jsonData['userData'];
                }
            }
            
            // 服务器端防重复提交检查
            $userId = $this->user->id;
            $userDataHash = md5($userData . $userId);
            $cacheKey = 'data_process_' . $userDataHash;
            
            // 获取skipPreviousSuccess参数
            $skipPreviousSuccess = false;
            
            // 尝试从POST、GET或JSON数据中获取skipPreviousSuccess参数
            if (input('post.skipPreviousSuccess') !== null) {
                $skipPreviousSuccess = (bool)input('post.skipPreviousSuccess');
            } elseif (input('get.skipPreviousSuccess') !== null) {
                $skipPreviousSuccess = (bool)input('get.skipPreviousSuccess');
            } else {
                $rawInput = file_get_contents('php://input');
                $jsonData = json_decode($rawInput, true);
                if (is_array($jsonData) && isset($jsonData['skipPreviousSuccess'])) {
                    $skipPreviousSuccess = (bool)$jsonData['skipPreviousSuccess'];
                }
            }
            
            if ($skipPreviousSuccess) {
                Log::info('启用跳过之前成功处理的数据');
            }
            
            // 检查是否批量重复提交相同数据
            $lastProcessInfo = cache($cacheKey);
            if ($lastProcessInfo) {
                $lastTime = $lastProcessInfo['time'];
                $currentTime = time();
                $timeDiff = $currentTime - $lastTime;
                
                // 如果用户未选择过滤已处理数据，则提示批量重复提交
                if (!$skipPreviousSuccess) {
                    $lastProcessTime = date('Y-m-d H:i:s', $lastTime);
                    Log::warning("用户 {$userId} 重复提交相同批次数据，但未启用自动过滤功能");
                    
                    // 返回特殊消息，提示用户这是重复提交并建议开启过滤
                    return json([
                        'status' => 'warning',
                        'message' => "检测到您在" . round($timeDiff/3600, 1) . "小时前提交过相同数据，建议开启\"自动过滤之前成功处理的数据\"选项",
                        'details' => [
                            [
                                'nickname' => '系统提示',
                                'status' => 'warning',
                                'message' => '已检测到重复提交，建议开启过滤选项避免重复处理',
                                'warehouse_value' => '不适用',
                                'is_duplicate' => true
                            ]
                        ],
                        'summary' => [
                            'total' => 1,
                            'success' => 0,
                            'failed' => 0,
                            'duplicate' => 1
                        ],
                        'duplicate_time' => $lastProcessTime,
                        'is_batch_duplicate' => true
                    ]);
                }
                // 如果启用了过滤功能，则记录日志但继续处理
                else {
                    Log::info("用户 {$userId} 重复提交相同批次数据，但已启用自动过滤功能，将继续处理");
                }
            }
            
            // 获取请求ID
            $requestId = '';
            $jsonData = json_decode(file_get_contents('php://input'), true);
            if (is_array($jsonData) && isset($jsonData['requestId'])) {
                $requestId = $jsonData['requestId'];
            }
            
            // 如果提供了请求ID，使用请求ID加强防重复提交检查
            if (!empty($requestId)) {
                $requestCacheKey = 'req_id_' . $requestId;
                $requestCache = cache($requestCacheKey);
                
                if ($requestCache) {
                    // 如果该请求ID已经处理过，直接返回之前的结果
                    Log::warning("检测到重复请求ID: {$requestId}, 用户ID: {$userId}");
                    return json([
                        'status' => 'warning',
                        'message' => '请求已处理，请勿重复提交',
                        'details' => [
                            [
                                'nickname' => '系统提示',
                                'status' => 'warning',
                                'message' => '该请求已经处理过，请勿重复提交',
                                'warehouse_value' => '未知',
                                'level' => 0
                            ]
                        ]
                    ]);
                }
                
                // 记录当前请求ID已被处理
                cache($requestCacheKey, ['time' => time()], 300); // 缓存5分钟
            }
            
            // 使用缓存检查是否最近处理过相同的数据
            $cache = cache($cacheKey);
            if ($cache) {
                $lastProcessTime = $cache['time'];
                $timeDiff = time() - $lastProcessTime;
                
                // 如果在5秒内处理过相同数据，拒绝处理
                if ($timeDiff < 5) {
                    Log::warning("检测到重复提交，用户ID: {$userId}, 时间间隔: {$timeDiff}秒");
                    return json([
                        'status' => 'warning',
                        'message' => '请勿频繁提交相同数据',
                        'details' => [
                            [
                                'nickname' => '系统提示',
                                'status' => 'warning',
                                'message' => '请勿在短时间内重复提交相同数据',
                                'warehouse_value' => '未知',
                                'level' => 0
                            ]
                        ]
                    ]);
                }
            }
            
            // 记录当前处理时间
            cache($cacheKey, ['time' => time(), 'data' => $userDataHash], 60); // 缓存1分钟
            
            // 获取仓库价值范围处理参数
            $enableWarehouseValueProcess = false;
            $warehouseValueGoodsId = 0;
            
            // 尝试从请求中获取仓库价值范围处理参数
            if (input('post.enableWarehouseValueProcess') !== null) {
                $enableWarehouseValueProcess = (bool)input('post.enableWarehouseValueProcess');
                if ($enableWarehouseValueProcess) {
                    $warehouseValueGoodsId = input('post.warehouseValueGoodsId/d', 0);
                }
            } elseif (input('get.enableWarehouseValueProcess') !== null) {
                $enableWarehouseValueProcess = (bool)input('get.enableWarehouseValueProcess');
                if ($enableWarehouseValueProcess) {
                    $warehouseValueGoodsId = input('get.warehouseValueGoodsId/d', 0);
                }
            } else {
                // 尝试从JSON数据中获取
                $rawInput = file_get_contents('php://input');
                $jsonData = json_decode($rawInput, true);
                if (is_array($jsonData)) {
                    if (isset($jsonData['enableWarehouseValueProcess'])) {
                        $enableWarehouseValueProcess = (bool)$jsonData['enableWarehouseValueProcess'];
                        if ($enableWarehouseValueProcess && isset($jsonData['warehouseValueGoodsId'])) {
                            $warehouseValueGoodsId = intval($jsonData['warehouseValueGoodsId']);
                        }
                    }
                }
            }
            
            if (empty($userData)) {
                Log::error('用户数据为空');
                return json(['status' => 'error', 'message' => '请提供用户数据']);
            }
            
            Log::info('接收到用户数据，长度: ' . strlen($userData));
            
            // 标准化数据格式
            $userData = str_replace(["\r\n", "\r"], "\n", $userData);
            $userData = trim($userData);
            
            // 先进行预处理数据校验，检查是否有有效的角色数据
            $preCheckArray = $this->parse_multi_roles_data($userData);
            if (empty($preCheckArray)) {
                Log::error('数据格式错误，无法解析任何角色数据');
                return json(['status' => 'error', 'message' => '数据格式错误，无法解析任何角色数据']);
            }
            
            // 检查是否有角色昵称
            $hasValidNickname = false;
            foreach ($preCheckArray as $item) {
                if (isset($item['parsed_data']['角色昵称']) && !empty(trim($item['parsed_data']['角色昵称']))) {
                    $hasValidNickname = true;
                    break;
                }
            }
            
            if (!$hasValidNickname) {
                Log::error('数据中不包含有效的角色昵称');
                return json(['status' => 'error', 'message' => '无法识别角色昵称，请检查数据格式']);
            }
            
            // 获取数据类型（机密/绝密）
            $dataType = '';
            
            // 尝试从POST数据获取
            if (input('post.dataType') !== null) {
                $dataType = input('post.dataType', '', 'trim');
            } 
            // 尝试从GET参数获取
            elseif (input('get.dataType') !== null) {
                $dataType = input('get.dataType', '', 'trim');
            }
            // 尝试从原始输入流获取
            else {
                $rawInput = file_get_contents('php://input');
                $jsonData = json_decode($rawInput, true);
                if (is_array($jsonData) && isset($jsonData['dataType'])) {
                    $dataType = $jsonData['dataType'];
                }
            }
            
            Log::info('数据类型: ' . $dataType);
            
            // 标准化换行符
            $userData = str_replace("\r\n", "\n", $userData);
            $userData = str_replace("\r", "\n", $userData);
            
            // 修复数据格式，确保如果存在_Callback部分，它直接跟在用户数据后面
            if (preg_match('/\n_Callback\((.*?)\);/s', $userData, $matches)) {
                $callbackPart = '_Callback(' . $matches[1] . ');';
                // 移除原始回调部分
                $userData = preg_replace('/\n_Callback\((.*?)\);/s', '', $userData);
                // 添加回调部分到用户数据后，用空格连接
                $userData = rtrim($userData) . " " . $callbackPart;
                Log::info("修复了用户数据格式，将_Callback部分移动到用户数据后面");
            }
            
            // 确保价值字段中的"M"后面有空格
            $userData = preg_replace('/([0-9\.]+[Mm])(?!\s)/m', '$1 ', $userData);
            Log::info("确保价值字段中的M后面有空格");
            
            // 获取过滤字段设置
            $enableFilter = false;
            $filterFields = [];
            
            // 尝试从POST数据获取
            if (input('post.enableFilter') !== null) {
                $enableFilter = (bool)input('post.enableFilter');
                if ($enableFilter && input('post.filterFields')) {
                    $filterFields = input('post.filterFields/a', []);
                }
            } 
            // 尝试从GET参数获取
            elseif (input('get.enableFilter') !== null) {
                $enableFilter = (bool)input('get.enableFilter');
                if ($enableFilter && input('get.filterFields')) {
                    $filterFields = input('get.filterFields/a', []);
                }
            }
            // 尝试从原始输入流获取
            else {
                $rawInput = file_get_contents('php://input');
                $jsonData = json_decode($rawInput, true);
                if (is_array($jsonData)) {
                    if (isset($jsonData['enableFilter'])) {
                        $enableFilter = (bool)$jsonData['enableFilter'];
                    }
                    if ($enableFilter && isset($jsonData['filterFields']) && is_array($jsonData['filterFields'])) {
                        $filterFields = $jsonData['filterFields'];
                    }
                }
            }
            
            Log::info('过滤设置: ' . ($enableFilter ? '启用' : '禁用') . ', 过滤字段: ' . json_encode($filterFields, JSON_UNESCAPED_UNICODE));
            
            // 解析多角色数据
            $roleDataArray = $this->parse_multi_roles_data($userData);
            if (empty($roleDataArray)) {
                Log::error('数据格式错误，无法解析任何角色数据');
                return json(['status' => 'error', 'message' => '数据格式错误，无法解析任何角色数据']);
            }
            
            // 处理结果
            $results = [];
            $successCount = 0;
            $failCount = 0;
            $duplicateCount = 0;
            
            // 记录已处理过的角色昵称，避免在同一批次中重复处理
            $processedNicknames = [];
            
            // 记录已处理过的原始数据，避免在同一批次中处理完全相同的数据
            $processedData = [];
            
            // 收集失败的原始数据
            $failedData = [];
            
            // 逐个处理每个角色的数据
            foreach ($roleDataArray as $roleData) {
                // 获取角色昵称用于日志记录
                $nickname = isset($roleData['parsed_data']['角色昵称']) ? $roleData['parsed_data']['角色昵称'] : '未知角色';
                
                // 跳过空昵称或无法识别的角色
                if (empty($nickname) || $nickname === '未知角色') {
                    $failCount++;
                    $results[] = [
                        'nickname' => $nickname,
                        'status' => 'error',
                        'message' => '无法识别角色昵称',
                        'warehouse_value' => '未知'
                    ];
                    continue;
                }
                
                // 标准化当前记录的原始数据，用于检测完全相同的数据
                $normalizedRawData = preg_replace('/\s+/', ' ', trim($roleData['raw_data']));
                
                // 检查当前批次中是否有完全相同的数据
                if (in_array($normalizedRawData, $processedData)) {
                    $duplicateCount++;
                    $results[] = [
                        'nickname' => $nickname,
                        'status' => 'warning',
                        'message' => '当前批次中存在完全相同的数据，已跳过',
                        'warehouse_value' => $this->extract_warehouse_value($roleData['parsed_data']) ?: '未知',
                        'level' => $this->extract_level($roleData['parsed_data']) ?: 0,
                        'is_duplicate' => true // 明确标记为重复数据
                    ];
                    Log::warning("当前批次中存在完全相同的数据: {$nickname}，已跳过");
                    continue;
                }
                
                // 记录已处理的数据
                $processedData[] = $normalizedRawData;
                
                // 检查当前批次中是否已处理过相同昵称（不区分大小写）
                if (in_array(strtolower($nickname), array_map('strtolower', $processedNicknames))) {
                    $duplicateCount++;
                    $results[] = [
                        'nickname' => $nickname,
                        'status' => 'warning',
                        'message' => '当前批次中存在重复角色昵称，已跳过',
                        'warehouse_value' => $this->extract_warehouse_value($roleData['parsed_data']) ?: '未知',
                        'level' => $this->extract_level($roleData['parsed_data']) ?: 0,
                        'is_duplicate' => true // 明确标记为重复数据
                    ];
                    continue;
                }
                
                // 记录已处理的昵称
                $processedNicknames[] = $nickname;
                
                // 检查是否曾经成功处理过此角色（如果启用了该选项）
                if ($skipPreviousSuccess) {
                    $previousSuccess = $this->checkIfPreviouslySucceeded($roleData['parsed_data'], $userId);
                    if ($previousSuccess) {
                        $duplicateCount++;
                        $warehouseValue = $this->extract_warehouse_value($roleData['parsed_data']) ?: '未知';
                        $level = $this->extract_level($roleData['parsed_data']) ?: 0;
                        
                        // 计算成功时间与当前时间的差距
                        $dayDiff = round((time() - $previousSuccess['timestamp']) / 86400, 1); // 精确到0.1天
                        
                        $results[] = [
                            'nickname' => $nickname,
                            'status' => 'warning',
                            'message' => "该角色已于{$dayDiff}天前成功处理，已自动跳过",
                            'warehouse_value' => $warehouseValue,
                            'level' => $level,
                            'previous_success' => true, // 标记为之前成功处理的数据
                            'previous_timestamp' => date('Y-m-d H:i:s', $previousSuccess['timestamp'])
                        ];
                        
                        Log::info("检测到角色 {$nickname} 已于{$dayDiff}天前({$previousSuccess['timestamp']})成功处理，跳过");
                        continue;
                    }
                }
                
                // 检查数据库中是否存在（单条记录级别的检查）
                // 如果存在则跳过，不存在则处理并添加到数据库
                $duplicateCheck = $this->check_duplicate_data($nickname, $roleData['raw_data']);
                if ($duplicateCheck) {
                    $duplicateCount++;
                    // 提取仓库价值和等级，确保在结果中显示
                    $warehouseValue = $this->extract_warehouse_value($roleData['parsed_data']) ?: '未知';
                    $level = $this->extract_level($roleData['parsed_data']) ?: 0;
                    
                    // 获取更详细的重复信息，如果check_duplicate_data返回数组
                    $duplicateReason = '数据库中已存在该角色数据';
                    if (is_array($duplicateCheck) && isset($duplicateCheck['message'])) {
                        $duplicateReason = $duplicateCheck['message'];
                    }
                    
                    $results[] = [
                        'nickname' => $nickname,
                        'status' => 'warning',
                        'message' => $duplicateReason,
                        'warehouse_value' => $warehouseValue,
                        'level' => $level,
                        'is_duplicate' => true // 明确标记为重复数据
                    ];
                    
                    // 记录跳过的角色
                    Log::info("角色 {$nickname} 在数据库中已存在，已跳过处理");
                    continue;
                }
                
                Log::info("处理角色数据: " . $nickname);
                
                // 处理用户数据，应用过滤器
                if ($enableFilter && !empty($filterFields)) {
                    $roleData['raw_data'] = $this->filter_user_data($roleData['raw_data'], $filterFields);
                    // 重新解析过滤后的数据
                    $roleData['parsed_data'] = $this->parse_user_data($roleData['raw_data']) ?: [];
                    Log::info("应用过滤后的数据: " . $roleData['raw_data']);
                }

                // 处理数据并存储
                try {
                    // 提取角色等级
                    $level = $this->extract_level($roleData['parsed_data']) ?: 0;
                    Log::info("角色 {$nickname} 等级: {$level}");
                    
                    // 提取仓库价值
                    $warehouseValue = $this->extract_warehouse_value($roleData['parsed_data']) ?: '';
                    Log::info("角色 {$nickname} 仓库价值: {$warehouseValue}");
                    
                    // 匹配商品
                    $goodsId = null;
                    $goodsName = "";
                    
                    // 低等级角色处理逻辑
                    $enableLowLevelProcess = false;
                    $lowLevelGoodsId = 0;
                    
                    // 尝试从JSON数据获取低等级角色处理参数
                    $rawInput = file_get_contents('php://input');
                    $jsonData = json_decode($rawInput, true);
                    if (is_array($jsonData)) {
                        if (isset($jsonData['enableLowLevelProcess'])) {
                            $enableLowLevelProcess = (bool)$jsonData['enableLowLevelProcess'];
                            if ($enableLowLevelProcess && isset($jsonData['lowLevelGoodsId'])) {
                                $lowLevelGoodsId = intval($jsonData['lowLevelGoodsId']);
                            }
                        }
                    }
                    
                    // 如果启用了低等级处理且当前角色等级在12以下
                    if ($enableLowLevelProcess && $level > 0 && $level < 12 && $lowLevelGoodsId > 0) {
                        Log::info("使用低等级处理逻辑，强制匹配商品ID: {$lowLevelGoodsId}");
                        $goodsId = $lowLevelGoodsId;
                        // 获取商品名称
                        $goods = Db::name('goods')->where('id', $goodsId)->find();
                        $goodsName = $goods ? $goods['name'] : "商品ID: {$goodsId}";
                    } 
                    // 如果启用了仓库价值范围处理，且仓库价值在范围内
                    else if ($enableWarehouseValueProcess && $warehouseValueGoodsId > 0 && $warehouseValue) {
                        // 标准化仓库价值为数值
                        $normalizedValue = $this->normalize_warehouse_value($warehouseValue);
                        
                        // 检查是否在0-1.9M范围内
                        if ($normalizedValue >= 0 && $normalizedValue < 1900000) {
                            Log::info("使用仓库价值范围处理逻辑，仓库价值 {$warehouseValue} 在范围内，强制匹配商品ID: {$warehouseValueGoodsId}");
                            $goodsId = $warehouseValueGoodsId;
                            // 获取商品名称
                            $goods = Db::name('goods')->where('id', $goodsId)->find();
                            $goodsName = $goods ? $goods['name'] : "商品ID: {$goodsId}";
                        } else {
                            Log::info("仓库价值 {$warehouseValue} 不在指定范围内，使用常规匹配");
                            // 常规的商品匹配逻辑
                            $goodsId = $this->match_goods_by_level_and_value($level, $warehouseValue, $roleData['parsed_data']);
                            
                            // 获取商品名称
                            if ($goodsId) {
                                $goods = Db::name('goods')->where('id', $goodsId)->find();
                                $goodsName = $goods ? $goods['name'] : "商品ID: {$goodsId}";
                            }
                        }
                    } else {
                        // 常规的商品匹配逻辑
                        $goodsId = $this->match_goods_by_level_and_value($level, $warehouseValue, $roleData['parsed_data']);
                        
                        // 获取商品名称
                        if ($goodsId) {
                            $goods = Db::name('goods')->where('id', $goodsId)->find();
                            $goodsName = $goods ? $goods['name'] : "商品ID: {$goodsId}";
                        }
                    }
                    
                    if ($goodsId) {
                        // 存储数据
                        $storageId = $this->store_user_data($roleData, $goodsId);
                        
                        if ($storageId) {
                            $successCount++;
                            $results[] = [
                                'nickname' => $nickname,
                                'status' => 'success',
                                'message' => '处理成功',
                                'goods_id' => $goodsId,
                                'goods_name' => $goodsName,
                                'warehouse_value' => $warehouseValue ?: '未知',
                                'level' => $level
                            ];
                        } else {
                            $failCount++;
                            $results[] = [
                                'nickname' => $nickname,
                                'status' => 'error',
                                'message' => '存储数据失败',
                                'warehouse_value' => $warehouseValue ?: '未知',
                                'level' => $level
                            ];
                            // 收集失败的原始数据
                            $failedData[] = [
                                'nickname' => $nickname,
                                'raw_data' => $roleData['raw_data'],
                                'reason' => '存储数据失败',
                                'warehouse_value' => $warehouseValue ?: '未知',
                                'level' => $level
                            ];
                        }
                    } else {
                        $failCount++;
                        $results[] = [
                            'nickname' => $nickname,
                            'status' => 'error',
                            'message' => '未找到匹配的商品',
                            'warehouse_value' => $warehouseValue ?: '未知',
                            'level' => $level
                        ];
                        // 收集失败的原始数据
                        $failedData[] = [
                            'nickname' => $nickname,
                            'raw_data' => $roleData['raw_data'],
                            'reason' => '未找到匹配的商品',
                            'warehouse_value' => $warehouseValue ?: '未知',
                            'level' => $level
                        ];
                    }
                } catch (\Exception $e) {
                    $failCount++;
                    $errorMsg = $e->getMessage();
                    Log::error("处理角色 {$nickname} 数据时发生错误: " . $errorMsg);
                    $results[] = [
                        'nickname' => $nickname,
                        'status' => 'error',
                        'message' => '处理异常: ' . $errorMsg,
                        'warehouse_value' => isset($roleData['parsed_data']) ? ($this->extract_warehouse_value($roleData['parsed_data']) ?: '未知') : '未知',
                        'level' => isset($roleData['parsed_data']) ? ($this->extract_level($roleData['parsed_data']) ?: 0) : 0
                    ];
                    // 收集失败的原始数据
                    $failedData[] = [
                        'nickname' => $nickname,
                        'raw_data' => $roleData['raw_data'],
                        'reason' => '处理异常: ' . $errorMsg,
                        'warehouse_value' => isset($roleData['parsed_data']) ? ($this->extract_warehouse_value($roleData['parsed_data']) ?: '未知') : '未知',
                        'level' => isset($roleData['parsed_data']) ? ($this->extract_level($roleData['parsed_data']) ?: 0) : 0
                    ];
                }
            }
            
            // 如果有失败的数据，将其保存到缓存中，以便导出
            if (!empty($failedData)) {
                // 确保失败数据是数组格式
                if (!is_array($failedData)) {
                    Log::error("失败数据不是有效的数组，转换为数组格式");
                    $failedData = [['nickname' => '系统错误', 'raw_data' => '数据格式错误', 'reason' => '系统内部错误']];
                }
                
                $failedCacheKey = 'failed_data_' . $userId . '_' . time();
                cache($failedCacheKey, $failedData, 3600); // 缓存1小时
                $cacheId = base64_encode($failedCacheKey);
                Log::info("保存失败数据到缓存，ID: {$cacheId}, 数据条数: " . count($failedData));
                
                // 设置响应状态和消息
                if ($successCount > 0) {
                    $status = 'success';
                    $message = "成功处理 {$successCount} 条数据";
                    if ($failCount > 0) {
                        $message .= "，失败 {$failCount} 条";
                    }
                    if ($duplicateCount > 0) {
                        $message .= "，已自动过滤 {$duplicateCount} 条重复数据";
                    }
                    
                    // 保存成功处理的数据到用户缓存，用于下次过滤
                    $this->cacheSuccessData($results, $userId);
                } else if ($duplicateCount > 0) {
                    $status = 'warning';
                    $message = "所有数据均为重复数据，已自动过滤 {$duplicateCount} 条";
                } else {
                    $status = 'error';
                    $message = "处理失败，请检查数据格式";
                }
                
                // 添加导出链接到结果
                $response = [
                    'status' => $status,
                    'message' => $message,
                    'details' => $results,
                    'summary' => [
                        'total' => count($roleDataArray),
                        'success' => $successCount,
                        'failed' => $failCount,
                        'duplicate' => $duplicateCount
                    ],
                    'failed_data_id' => $cacheId,
                    // 添加用户ID到响应中
                    'user_id' => $userId
                ];
            } else {
                // 设置响应状态和消息
                if ($successCount > 0) {
                    $status = 'success';
                    $message = "成功处理 {$successCount} 条数据";
                    if ($duplicateCount > 0) {
                        $message .= "，已自动过滤 {$duplicateCount} 条重复数据";
                    }
                    
                    // 保存成功处理的数据到用户缓存，用于下次过滤
                    $this->cacheSuccessData($results, $userId);
                } else if ($duplicateCount > 0) {
                    $status = 'warning';
                    $message = "所有数据均为重复数据，已自动过滤 {$duplicateCount} 条";
                } else {
                    $status = 'error';
                    $message = "处理失败，请检查数据格式";
                }
                
                $response = [
                    'status' => $status,
                    'message' => $message,
                    'details' => $results,
                    'summary' => [
                        'total' => count($roleDataArray),
                        'success' => $successCount,
                        'failed' => $failCount,
                        'duplicate' => $duplicateCount
                    ],
                    // 添加用户ID到响应中
                    'user_id' => $userId
                ];
            }
            
            // 处理完成后，记录处理结果
            $processCacheKey = 'process_result_' . $userId . '_' . time();
            cache($processCacheKey, [
                'time' => time(),
                'result' => [
                    'success' => $successCount,
                    'failed' => $failCount,
                    'duplicate' => $duplicateCount
                ]
            ], 3600); // 缓存1小时
            
            return json($response);
            
        } catch (\Exception $e) {
            // 记录详细的错误信息
            Log::error('处理用户数据失败，详细信息：');
            Log::error('错误消息: ' . $e->getMessage());
            Log::error('错误文件: ' . $e->getFile());
            Log::error('错误行号: ' . $e->getLine());
            Log::error('错误追踪: ' . $e->getTraceAsString());
            
            // 如果是开发环境，返回详细错误信息
            if (config('app.debug')) {
                return json([
                    'status' => 'error',
                    'message' => '处理数据失败：' . $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
            
            // 生产环境只返回基本错误信息
            return json(['status' => 'error', 'message' => '处理数据失败，请稍后重试']);
        }
    }
    
    /**
     * 检查是否存在重复数据
     * @param string $nickname 角色昵称
     * @param string $raw_data 原始数据
     * @return bool|array 如果重复返回包含详细信息的数组，否则返回false
     */
    private function check_duplicate_data($nickname, $raw_data) {
        try {
            if (empty($nickname)) {
                Log::error("检查重复数据失败: 角色昵称为空");
                return false;
            }
            
            // 记录开始检查的日志
            Log::info("开始检查角色数据是否重复: {$nickname}");
            
            // 获取商户ID
            $merchantId = $this->merchant_id;
            if (!$merchantId) {
                Log::error("检查重复数据失败: 无法获取商户ID");
                return false;
            }
            
            // 标准化昵称和原始数据
            $nickname = trim($nickname);
            $raw_data = trim($raw_data);
            
            // 标准化原始数据（移除所有空白符的差异）
            $normalized_raw_data = preg_replace('/\s+/', ' ', $raw_data);
            
            // 从原始数据中提取仓库价值
            $warehouseValue = $this->extract_warehouse_value_from_raw($raw_data);
            Log::info("当前数据仓库价值: " . ($warehouseValue ?: '未知'));
            
            // 获取所有商品卡密存储表
            $tables = $this->get_storage_tables();
            
            // 添加优化：解析当前提交的数据，以便后续比对
            $parsedData = $this->parse_user_data($raw_data);
            if (!$parsedData) {
                Log::warning("解析用户数据失败，无法进行详细比对");
            }
            
            // 使用PDO预处理语句提高安全性和性能
            $db = $this->db;
            
            foreach ($tables as $table) {
                Log::info("检查表 {$table} 中是否存在重复角色");
                
                // 使用预处理语句查询数据 - 首先基于昵称进行过滤
                $stmt = $db->prepare("SELECT id, secret, create_time FROM {$table} WHERE merchant_id = ? AND secret LIKE ? ORDER BY id DESC LIMIT 50");
                // 使用模糊匹配查找包含此角色昵称的记录（不区分大小写）
                $search_term = '%角色昵称%' . $nickname . '%';
                $stmt->execute([$merchantId, $search_term]);
                
                $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
                if (empty($records)) {
                    Log::info("表 {$table} 中未找到包含该角色昵称的记录");
                    continue;
                }
                
                Log::info("在表 {$table} 中找到 " . count($records) . " 条可能匹配的记录");
                
                foreach ($records as $record) {
                    // 获取记录中的secret字段
                    $storedData = $record['secret'];
                    
                    // 检查是否完全相同的数据
                    $normalized_stored_data = preg_replace('/\s+/', ' ', $storedData);
                    
                    // 如果数据完全相同（忽略空白符差异），则判定为重复
                    if ($normalized_raw_data === $normalized_stored_data) {
                        $createTime = strtotime($record['create_time']);
                        $createTimeFormatted = date('Y-m-d H:i:s', $createTime);
                        $daysDiff = (time() - $createTime) / (60 * 60 * 24);
                        $daysDiffFormatted = round($daysDiff, 1);
                        
                        Log::warning("发现完全相同的数据记录: 角色 {$nickname}, 记录ID: {$record['id']}, 表: {$table}, 创建时间: {$createTimeFormatted}");
                        return [
                            'message' => "数据库中已存在完全相同的数据（{$daysDiffFormatted}天前提交）",
                            'record_id' => $record['id'],
                            'table' => $table,
                            'create_time' => $createTimeFormatted,
                            'days_diff' => $daysDiffFormatted
                        ];
                    }
                    
                    // 验证是否真的匹配（避免部分匹配）
                    $nicknamePattern = '/角色昵称[:|：]\s*' . preg_quote($nickname, '/') . '\s*([|]|$)/i';
                    if (!preg_match($nicknamePattern, $storedData)) {
                        // 如果不是精确匹配，尝试通过解析提取更准确的昵称进行比较
                        $storedParsedData = $this->parse_user_data($storedData);
                        if (!$storedParsedData || !isset($storedParsedData['角色昵称'])) {
                            // 无法解析存储的数据，跳过这条记录
                            continue;
                        }
                        
                        $storedNickname = trim($storedParsedData['角色昵称']);
                        // 不区分大小写比较角色昵称
                        if (strcasecmp($storedNickname, $nickname) !== 0) {
                            // 昵称不匹配，跳过
                            continue;
                        }
                    }
                    
                    // 如果角色昵称匹配，还要检查仓库价值是否一致
                    if ($parsedData && $warehouseValue) {
                        // 从存储的数据中提取仓库价值
                        $storedWarehouseValue = $this->extract_warehouse_value_from_raw($storedData);
                        
                        // 如果仓库价值相差超过20%，可能是不同角色，继续检查下一条
                        if ($storedWarehouseValue && $warehouseValue) {
                            // 将存储的仓库价值标准化为浮点数（处理带单位的情况，如"1.5M"）
                            $storedValueNumber = $this->normalize_warehouse_value($storedWarehouseValue);
                            $currentValueNumber = $this->normalize_warehouse_value($warehouseValue);
                            
                            if ($storedValueNumber > 0 && $currentValueNumber > 0) {
                                $diff = abs($storedValueNumber - $currentValueNumber) / max($storedValueNumber, $currentValueNumber);
                                if ($diff > 0.2) { // 差异超过20%
                                    Log::info("角色昵称匹配但仓库价值差异大: {$storedWarehouseValue} vs {$warehouseValue}，继续检查");
                                    continue;
                                }
                            }
                        }
                    }
                    
                    // 计算记录的创建时间（防止比较太旧的数据）
                    $createTime = strtotime($record['create_time']);
                    $currentTime = time();
                    $daysDiff = ($currentTime - $createTime) / (60 * 60 * 24);
                    $daysDiffFormatted = round($daysDiff, 1);
                    $createTimeFormatted = date('Y-m-d H:i:s', $createTime);
                    
                    // 如果记录超过30天，可能是重复使用的昵称，降低匹配敏感度
                    if ($daysDiff > 30) {
                        // 对旧记录进行更严格的比较
                        // 必须有更多字段匹配才认为是重复
                        if ($parsedData) {
                            $storedParsedData = $this->parse_user_data($storedData);
                            if ($storedParsedData) {
                                // 检查关键字段是否匹配
                                $criticalFields = ['等级', '哈夫币', '道具价值', '仓库价值'];
                                $matchedFields = 0;
                                $matchedFieldNames = [];
                                
                                foreach ($criticalFields as $field) {
                                    if (isset($parsedData[$field]) && isset($storedParsedData[$field]) &&
                                        trim($parsedData[$field]) === trim($storedParsedData[$field])) {
                                        $matchedFields++;
                                        $matchedFieldNames[] = $field;
                                    }
                                }
                                
                                // 如果匹配字段太少，认为不是同一条数据
                                if ($matchedFields < 2) {
                                    Log::info("记录较旧且关键字段匹配较少，继续检查");
                                    continue;
                                }
                                
                                $matchedInfo = implode('、', $matchedFieldNames);
                                Log::warning("发现角色 {$nickname} 的相似记录，匹配字段: {$matchedInfo}，时间: {$createTimeFormatted}");
                                return [
                                    'message' => "发现角色 {$nickname} 相似记录（{$daysDiffFormatted}天前，{$matchedInfo}相同）",
                                    'record_id' => $record['id'],
                                    'table' => $table,
                                    'create_time' => $createTimeFormatted,
                                    'days_diff' => $daysDiffFormatted,
                                    'matched_fields' => $matchedFieldNames
                                ];
                            }
                        }
                    }
                    
                    // 数据匹配，发现重复
                    Log::warning("发现角色 {$nickname} 的重复数据，商户ID:{$merchantId}，记录ID:{$record['id']}，表:{$table}，时间:{$createTimeFormatted}");
                    return [
                        'message' => "数据库中已存在该角色数据（{$daysDiffFormatted}天前提交）",
                        'record_id' => $record['id'],
                        'table' => $table,
                        'create_time' => $createTimeFormatted,
                        'days_diff' => $daysDiffFormatted
                    ];
                }
            }
            
            // 所有表都检查完毕，未发现重复
            Log::info("角色 {$nickname} 未发现重复数据");
            return false;
        } catch (\Exception $e) {
            Log::error("检查重复数据异常: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return false;
        }
    }
    
    /**
     * 从原始数据中提取仓库价值
     * @param string $raw_data 原始数据
     * @return string|null 仓库价值
     */
    private function extract_warehouse_value_from_raw($raw_data) {
        if (empty($raw_data)) {
            return null;
        }
        
        // 尝试多种格式匹配仓库价值
        $patterns = [
            '/仓库价值[:|：]\s*([0-9.]+\s*[MmKk]?)\s*([|]|$)/i',  // 匹配 "仓库价值：1.5M" 或 "仓库价值:1.5k"
            '/道具价值[:|：]\s*([0-9.]+\s*[MmKk]?)\s*([|]|$)/i',  // 匹配 "道具价值：1.5M" 或 "道具价值:1.5k"
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $raw_data, $matches)) {
                return trim($matches[1]);
            }
        }
        
        return null;
    }
    
    /**
     * 标准化仓库价值为浮点数（单位：元）
     * @param string $value 仓库价值，如 "1.5M" 或 "1500K"
     * @return float 标准化后的数值
     */
    private function normalize_warehouse_value($value) {
        if (empty($value)) {
            return 0;
        }
        
        // 确保value是字符串类型，防止在是数字时导致类型错误
        $value = (string)$value;
        
        // 移除所有空格
        $value = str_replace(' ', '', $value);
        
        // 将值转换为小写以统一处理
        $lowerValue = strtolower($value);
        
        // 处理带单位的数值
        if (preg_match('/^([0-9.]+)m$/', $lowerValue, $matches)) {
            // 将 "1.5m" 转换为 1500000
            return floatval($matches[1]) * 1000000;
        } else if (preg_match('/^([0-9.]+)k$/', $lowerValue, $matches)) {
            // 将 "1500k" 转换为 1500000
            return floatval($matches[1]) * 1000;
        } else {
            // 尝试直接转换为浮点数
            return floatval($value);
        }
    }
    
    /**
     * 比较两条数据的关键字段
     * 
     * @param array $existingData 已存在的数据
     * @param string $newDataRaw 新数据的原始内容
     * @return bool 是否为重复数据
     */
    private function compare_critical_fields($existingData, $newDataRaw) {
        // 如果没有提供新数据，不进行比较
        if (empty($newDataRaw)) {
            return false;
        }
        
        // 解析新数据
        $newData = $this->parse_user_data($newDataRaw);
        if ($newData === false) {
            return false;
        }
        
        // 1. 比较仓库价值
        $existingValue = false;
        $newValue = false;
        
        // 尝试获取仓库价值
        foreach (['仓库价值', '道具价值', '价值', '物品价值', '账号价值', '总价值'] as $valueKey) {
            if (isset($existingData[$valueKey])) {
                $existingValue = trim($existingData[$valueKey]);
            }
            
            if (isset($newData[$valueKey])) {
                $newValue = trim($newData[$valueKey]);
            }
            
            // 如果已找到两者的价值，中断循环
            if ($existingValue !== false && $newValue !== false) {
                break;
            }
        }
        
        // 如果两者都有价值数据且相同，认为是重复数据
        if ($existingValue !== false && $newValue !== false) {
            // 对比前先标准化价值表示
            $existingValue = preg_replace('/\s+/', ' ', $existingValue);
            $newValue = preg_replace('/\s+/', ' ', $newValue);
            
            if (strcasecmp($existingValue, $newValue) === 0) {
                return true;
            }
        }
        
        // 2. 比较等级
        $existingLevel = $this->extract_level($existingData);
        $newLevel = $this->extract_level($newData);
        
        if ($existingLevel > 0 && $newLevel > 0 && $existingLevel === $newLevel) {
            return true;
        }
        
        // 3. 检查更多关键字段...
        // 可以添加其他关键字段的比较
        
        return false;
    }
    
    /**
     * 处理数据前检查重复
     */
    private function check_duplicates_before_process($userData) {
        try {
            // 解析多角色数据
            $roleDataArray = $this->parse_multi_roles_data($userData);
            if (empty($roleDataArray)) {
                return false;
            }

            $duplicates = [];
            $processedNicknames = []; // 用于跟踪当前批次中已处理过的昵称
            
            // 1. 先检查当前批次内是否有重复角色
            foreach ($roleDataArray as $roleData) {
                if (isset($roleData['parsed_data']['角色昵称'])) {
                    $nickname = trim($roleData['parsed_data']['角色昵称']);
                    if (empty($nickname)) {
                        continue;
                    }
                    
                    // 检查当前批次中是否已存在此昵称（避免同一批次重复提交）
                    // 使用不区分大小写的比较
                    $lowerNickname = strtolower($nickname);
                    $isInCurrentBatch = false;
                    
                    foreach ($processedNicknames as $existingNickname) {
                        if (strtolower($existingNickname) === $lowerNickname) {
                            $isInCurrentBatch = true;
                            break;
                        }
                    }
                    
                    if ($isInCurrentBatch) {
                        $duplicates[] = [
                            'nickname' => $nickname,
                            'raw_data' => $roleData['raw_data'],
                            'message' => '当前批次中存在重复角色昵称',
                            'warehouse_value' => $this->extract_warehouse_value($roleData['parsed_data']) ?: '未知'
                        ];
                        continue;
                    }
                    
                    // 记录已处理的昵称
                    $processedNicknames[] = $nickname;
                }
            }
            
            // 2. 检查数据库中是否有重复数据
            foreach ($roleDataArray as $roleData) {
                if (isset($roleData['parsed_data']['角色昵称'])) {
                    $nickname = trim($roleData['parsed_data']['角色昵称']);
                    if (empty($nickname)) {
                        continue;
                    }
                    
                    // 已经在当前批次中标记为重复的不需要再检查
                    $isDuplicate = false;
                    foreach ($duplicates as $dup) {
                        if (strcasecmp($dup['nickname'], $nickname) === 0) {
                            $isDuplicate = true;
                            break;
                        }
                    }
                    
                    if ($isDuplicate) {
                        continue;
                    }
                    
                    // 从解析数据中获取仓库价值
                    $warehouseValue = $this->extract_warehouse_value($roleData['parsed_data']);
                    
                    // 检查数据库中是否已存在此角色
                    if ($this->check_duplicate_data($nickname, $roleData['raw_data'])) {
                        Log::info("角色 {$nickname} 在数据库中已存在，标记为重复");
                        $duplicates[] = [
                            'nickname' => $nickname,
                            'raw_data' => $roleData['raw_data'],
                            'message' => '数据库中已存在该角色数据',
                            'warehouse_value' => $warehouseValue ?: '未知'
                        ];
                    }
                }
            }

            if (!empty($duplicates)) {
                Log::warning("发现重复角色数据: " . count($duplicates) . " 条");
                
                // 尝试从解析的数据中获取更多信息（等级等）
                foreach ($duplicates as &$dup) {
                    // 解析这条数据
                    $parsedData = $this->parse_user_data($dup['raw_data']);
                    if ($parsedData !== false) {
                        // 尝试获取等级
                        $dup['level'] = $this->extract_level($parsedData);
                    }
                    // 如果没有设置level，添加默认值
                    if (!isset($dup['level']) || $dup['level'] === 0) {
                        $dup['level'] = 0;
                    }
                }
                
                return $duplicates;
            }

            return false;
        } catch (\Exception $e) {
            Log::error("检查重复数据失败: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return false;
        }
    }
    
    /**
     * 解析多角色用户数据
     * 将包含多个角色的数据分割成独立的角色数据数组
     */
    private function parse_multi_roles_data($userData) {
        if (empty($userData)) {
            return [];
        }

        // 标准化换行符和空格
        $userData = str_replace(["\r\n", "\r"], "\n", $userData);
        $userData = trim($userData);
        
        // 按换行符分割多个角色数据
        $lines = explode("\n", $userData);
        $roles = [];
        
        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) continue;
            
            // 检查是否包含角色昵称
            if (preg_match('/(.*?)(角色昵称[:|：])/i', $line) || 
                strpos(strtolower($line), '角色昵称') === 0) {
                
                $parsedData = $this->parse_user_data($line);
                if ($parsedData !== false) {
                    $roles[] = [
                        'raw_data' => $line,
                        'parsed_data' => $parsedData
                    ];
                }
            }
        }
        
        // 如果没有找到任何角色数据，尝试解析整个数据
        if (empty($roles) && !empty($userData)) {
            $parsedData = $this->parse_user_data($userData);
            if ($parsedData !== false) {
                $roles[] = [
                    'raw_data' => $userData,
                    'parsed_data' => $parsedData
                ];
            }
        }
        
        return $roles;
    }
    
    /**
     * 将多行文本转换为管道分隔格式
     */
    private function convert_to_pipe_format($text) {
        if (empty($text)) return '';
        
        // 按行分割
        $lines = explode("\n", $text);
        $result = [];
        $currentLine = '';
        
        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) continue;
            
            // 检查是否是新角色的开始（包含"角色昵称"）
            if (preg_match('/(角色昵称[:|：])/', $line)) {
                if (!empty($currentLine)) {
                    $result[] = $currentLine;
                }
                $currentLine = $line;
            } 
            // 检查是否是回调数据（_Callback部分）
            else if (strpos($line, '_Callback') !== false) {
                // 直接添加回调数据到当前行，用空格连接
                if (!empty($currentLine)) {
                    // 确保M单位后面有空格
                    if (preg_match('/([0-9\.]+[Mm])$/i', $currentLine)) {
                        $currentLine .= ' ';
                    }
                    $currentLine .= " " . $line;
                } else {
                    $currentLine = $line;
                }
            }
            // 检查是否是键值对
            else if (strpos($line, ':') !== false || strpos($line, '：') !== false) {
                // 如果是键值对，使用管道符连接
                if (!empty($currentLine)) {
                    $currentLine .= "|" . $line;
                } else {
                    $currentLine = $line;
                }
            }
            // 其他内容（可能是上一行的继续）
            else {
                if (!empty($currentLine)) {
                    // 直接追加内容，使用空格连接
                    $currentLine .= " " . $line;
                } else {
                    $currentLine = $line;
                }
            }
        }
        
        // 添加最后一行
        if (!empty($currentLine)) {
            $result[] = $currentLine;
        }
        
        return implode("\n", $result);
    }
    
    /**
     * 解析单行数据
     */
    private function parse_line_data($line) {
        $result = [];
        
        // 标准化空格
        $line = preg_replace('/\s+/', ' ', $line);
        $line = trim($line);
        
        // 检查是否包含分隔符
        if (strpos($line, '：') !== false) {
            list($key, $value) = explode('：', $line, 2);
            $key = trim($key);
            $value = trim($value);
            $result[$key] = $value;
        } elseif (strpos($line, ':') !== false) {
            list($key, $value) = explode(':', $line, 2);
            $key = trim($key);
            $value = trim($value);
            $result[$key] = $value;
        }
        
        return $result;
    }
    
    /**
     * 确保M单位后面有空格
     */
    private function ensureMHasSpace($value) {
        if (empty($value)) return $value;
        
        // 检查是否包含M单位
        if (preg_match('/([0-9\.]+[Mm])(?!\s)/i', $value)) {
            // 添加空格
            $value = preg_replace('/([0-9\.]+[Mm])(?!\s)/i', '$1 ', $value);
            Log::info("为价值字段添加M后空格: {$value}");
        }
        
        return $value;
    }
    
    /**
     * 解析用户数据字符串到数组
     * @param string $userData 用户数据字符串
     * @return array|false 解析后的数据数组或失败时返回false
     */
    private function parse_user_data($userData) {
        if (empty($userData)) {
            return false;
        }

        try {
            // 标准化换行符和空格
            $userData = str_replace(["\r\n", "\r"], "\n", $userData);
            $userData = trim($userData);
            
            // 分割数据和_Callback部分
            $mainData = $userData;
            $callbackData = '';
            if (strpos($userData, '_Callback') !== false) {
                list($mainData, $callbackData) = explode('_Callback', $userData, 2);
                $mainData = trim($mainData);
                $callbackData = '_Callback' . $callbackData;
            }
            
            // 按管道符分割主数据
            $fields = explode('|', $mainData);
            $result = [];
            
            foreach ($fields as $field) {
                $field = trim($field);
                if (empty($field)) continue;
                
                // 处理只有键没有值的情况（如"禁言"）
                if (!strpos($field, '：') && !strpos($field, ':')) {
                    $result[$field] = '';
                    continue;
                }
                
                // 处理键值对
                if (strpos($field, '：') !== false) {
                    list($key, $value) = explode('：', $field, 2);
                } else if (strpos($field, ':') !== false) {
                    list($key, $value) = explode(':', $field, 2);
                } else {
                    continue;
                }
                
                $key = trim($key);
                $value = trim($value);
                
                if (!empty($key)) {
                    // 如果是最后一个字段且有_Callback数据，将其附加到值后面
                    if (!empty($callbackData) && $key === array_key_last(array_filter(explode('|', $mainData)))) {
                        $value .= ' ' . $callbackData;
                    }
                    $result[$key] = $value;
                }
            }
            
            return $result;
        } catch (\Exception $e) {
            Log::error("解析用户数据失败: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 从解析后的数据中提取仓库价值
     */
    private function extract_warehouse_value($data) {
        // 记录一下解析到的数据字段用于调试
        Log::info("解析到的数据字段: " . json_encode(array_keys($data), JSON_UNESCAPED_UNICODE));
        
        // 可能的仓库价值字段名列表
        $possibleKeys = [
            '仓库价值v', '仓库价值', '道具价值', '价值', '物品价值', '道具总价值',
            '背包价值', '账号价值', '总价值', '角色价值'
        ];
        
        // 遍历所有可能的字段名
        foreach ($possibleKeys as $key) {
            if (isset($data[$key])) {
                $value = $data[$key];
                Log::info("从字段 '{$key}' 中找到价值: {$value}");
                
                // 清理可能包含的_Callback数据
                if (strpos($value, '_Callback') !== false) {
                    // 提取价值部分，去掉_Callback部分
                    $parts = explode('_Callback', $value);
                    $value = trim($parts[0]);
                    Log::info("从混合值中提取纯价值: {$value}");
                }
                
                // 如果格式是 "0.7M" 这种形式
                if (preg_match('/([0-9\.]+)[Mm]/', $value, $matches)) {
                    // 确保M后面有空格
                    // 定义回调的函数以使内联过滤更简单
                    $ensureSpace = function($v) {
                        if (preg_match('/([0-9\.]+[Mm])$/', $v)) {
                            return $v . ' '; // 如果以M结尾且无空格，则添加空格
                        } elseif (preg_match('/([0-9\.]+[Mm]) +$/', $v)) {
                            return $v; // 如果已有空格，保持不变
                        } else {
                            return $v; // 其他情况保持不变
                        }
                    };
                    
                    // 修正值中的空格
                    $value = $ensureSpace($value);
                    Log::info("修正后的值(添加空格): {$value}");
                    
                    $convertedValue = floatval($matches[1]) * 1000000;
                    Log::info("转换带M单位的价值: {$matches[1]}M -> {$convertedValue}");
                    return $convertedValue;
                }
                return floatval($value);
            }
        }
        
        // 未找到任何价值字段
        Log::error("在数据中未找到任何价值字段");
        return false;
    }
    
    /**
     * 根据仓库价值匹配对应的商品ID
     * @param float $value 仓库价值
     * @param array $roleData 角色数据（包含等级等信息）
     * @return int|bool 商品ID或false
     */
    private function match_goods_by_value($value, $roleData = null) {
        try {
            // 获取当前商户ID
            $userId = $this->user->id;
            if (!$userId) {
                Log::error("无法获取商户ID");
                return false;
            }
            
            Log::info("商户ID: {$userId}, 匹配价值: {$value}");
            
            // 获取等级
            $level = 0;
            if ($roleData && isset($roleData['parsed_data'])) {
                // 识别等级字段
                $levelFields = ['等级', 'lv', 'Lv', 'LV', '级别', '人物等级', '角色等级'];
                foreach ($levelFields as $field) {
                    if (isset($roleData['parsed_data'][$field])) {
                        if (preg_match('/(\d+)/', $roleData['parsed_data'][$field], $matches)) {
                            $level = intval($matches[1]);
                            break;
                        }
                    }
                }
            }
            
            Log::info("角色等级: {$level}");
            
            // 获取当前商户的所有上架商品
            $query = Db::name('goods')
                ->field(['id', 'name', 'price'])
                ->where('status', 1)
                ->where('user_id', $userId)
                ->order('id', 'asc')
                ->select()
                ->toArray();
            
            if (empty($query)) {
                Log::warning("商户 {$userId} 没有上架的商品");
                return false;
            }
            
            // 根据等级和价值范围匹配商品
            $matchedGoods = [];
            
            foreach ($query as $goods) {
                $name = $goods['name'];
                $isTypeMatch = false;
                
                // 根据等级判断类型
                if ($level >= 30 && stripos($name, '绝密') !== false) {
                    $isTypeMatch = true;
                } else if ($level >= 12 && $level < 30 && stripos($name, '机密') !== false) {
                    $isTypeMatch = true;
                }
                
                if (!$isTypeMatch) {
                    continue;
                }
                
                // 解析商品名称中的价值范围
                if (preg_match('/(\d+(?:\.\d+)?)-(\d+(?:\.\d+)?)\s*[Mm]?/i', $name, $matches) ||
                    preg_match('/(\d+(?:\.\d+)?)[Mm]?-(\d+(?:\.\d+)?)[Mm]/i', $name, $matches)) {
                    
                    $minValue = floatval($matches[1]);
                    $maxValue = floatval($matches[2]);
                    
                    // 检查是否包含M单位
                    $multiplier = (stripos($name, 'M') !== false) ? 1000000 : 1;
                    $minValue *= $multiplier;
                    $maxValue *= $multiplier;
                    
                    Log::info("商品 {$name} 价值范围: {$minValue}-{$maxValue}");
                    
                    // 严格检查价值是否在范围内
                    if ($value >= $minValue && $value <= $maxValue) {
                        Log::info("找到精确范围匹配的商品: {$name}");
                        return $goods['id']; // 直接返回第一个匹配的商品
                    }
                }
                // 处理单一价值（如 "0.5M"）- 仅作为完全相等的匹配
                else if (preg_match('/(\d+(?:\.\d+)?)[Mm]/i', $name, $matches)) {
                    $targetValue = floatval($matches[1]) * 1000000;
                    
                    if ($value == $targetValue) {
                        Log::info("找到精确值匹配的商品: {$name}");
                        return $goods['id'];
                    }
                }
            }
            
            Log::warning("未找到匹配的商品");
            return false;
            
        } catch (\Exception $e) {
            Log::error("商品匹配错误: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 将用户数据存储到对应的商品记录中
     */
    private function store_user_data($roleData, $goodsId) {
        try {
            // 确保商品ID是整数
            $goodsId = intval($goodsId);
            
            if (!$goodsId) {
                Log::error("无法存储用户数据，商品ID为空或无效");
                return false;
            }
            
            // 获取角色昵称（如果存在），用于日志记录
            $nickname = isset($roleData['parsed_data']['角色昵称']) ? $roleData['parsed_data']['角色昵称'] : '未知角色';
            
            Log::info("开始存储用户数据，角色: {$nickname}, 商品ID: {$goodsId}");
            
            // 检查商品是否存在
            $goods = Db::name('goods')->where('id', $goodsId)->find();
            if (!$goods) {
                Log::error("无法存储用户数据，商品不存在，ID: {$goodsId}");
                return false;
            }
            
            // 记录商品名称，方便调试
            Log::info("匹配商品: {$goods['name']} (ID: {$goodsId})");
            
            // 获取用户ID，这里使用当前登录用户
            $userId = $this->user->id;
            
            if (!$userId) {
                Log::error("无法确定用户ID");
                return false;
            }
            
            // 检查raw_data是否存在
            if (!isset($roleData['raw_data']) || empty($roleData['raw_data'])) {
                Log::error("角色 {$nickname} 的原始数据(raw_data)为空或不存在");
                return false;
            }
            
            // 获取原始数据
            $secret = $roleData['raw_data'];
            
            // 标准化换行符
            $secret = str_replace("\r\n", "\n", $secret);
            $secret = str_replace("\r", "\n", $secret);
            
            // 清理数据，只处理多余的空格和换行
            $secret = preg_replace('/\s+/', ' ', $secret);
            $secret = trim($secret);
            
            // 确保仓库价值字段后面有空格（如果后面没有其他数据）
            if (!preg_match('/[仓库价值v|仓库价值|道具价值|价值|物品价值][：:]\s*\d+(?:\.\d+)?[Mm]\s+(?:_Callback|\S)/u', $secret)) {
                $secret = preg_replace('/([仓库价值v|仓库价值|道具价值|价值|物品价值][：:]\s*\d+(?:\.\d+)?[Mm])(?!\s)/u', '$1 ', $secret);
            }
            
            Log::info("最终处理后的数据格式: " . $secret);
            
            // 首先检查默认表是否存在，确保即使其他表出问题也能使用默认表
            $defaultTableExists = false;
            $prefix = config('database.connections.mysql.prefix');
            $defaultTableName = "goods_card_storage_0";
            $fullDefaultTableName = $prefix . $defaultTableName;
            
            try {
                $tableCheck = Db::query("SHOW TABLES LIKE '{$fullDefaultTableName}'");
                $defaultTableExists = !empty($tableCheck);
                
                if (!$defaultTableExists) {
                    // 创建默认表
                    Log::info("默认表 {$defaultTableName} 不存在，尝试创建");
                    $createTableSql = $this->generateStorageTableSql($defaultTableName);
                    Db::execute($createTableSql);
                    Log::info("创建默认表 {$defaultTableName} 成功");
                    $defaultTableExists = true;
                } else {
                    Log::info("默认表 {$defaultTableName} 已存在");
                }
            } catch (\Exception $e) {
                Log::error("检查或创建默认表失败: " . $e->getMessage());
            }
            
            // 确定基于商品ID的存储表名
            $tableSuffix = $this->goods_card_storage_suffix($goodsId);
            $tableName = "goods_card_storage" . $tableSuffix;
            
            // 如果是默认表或分表与默认表相同，直接使用默认表
            if ($tableName === $defaultTableName || !$this->should_use_separate_table($goodsId)) {
                $tableName = $defaultTableName;
            } else {
                // 检查分表是否存在
                $fullTableName = $prefix . $tableName;
                $tableExists = false;
                
                try {
                    $tableCheck = Db::query("SHOW TABLES LIKE '{$fullTableName}'");
                    $tableExists = !empty($tableCheck);
                } catch (\Exception $e) {
                    Log::error("检查表 {$tableName} 失败: " . $e->getMessage());
                }
                
                // 如果分表不存在，尝试创建
                if (!$tableExists) {
                    Log::info("表 {$tableName} 不存在，尝试创建");
                    try {
                        $createTableSql = $this->generateStorageTableSql($tableName);
                        Db::execute($createTableSql);
                        Log::info("创建表 {$tableName} 成功");
                    } catch (\Exception $e) {
                        Log::error("创建表 {$tableName} 失败: " . $e->getMessage());
                        // 如果创建分表失败但默认表存在，使用默认表
                        if ($defaultTableExists) {
                            $tableName = $defaultTableName;
                            Log::info("使用默认表 {$defaultTableName} 存储数据");
                        } else {
                            Log::error("默认表和分表都不可用，无法存储数据");
                            return false;
                        }
                    }
                }
            }
            
            // 插入数据
            try {
                Log::info("尝试将数据插入到表 {$tableName}");
                Log::info("插入的数据: user_id={$userId}, goods_id={$goodsId}, secret长度=" . strlen($secret));
                
                $data = [
                    'user_id' => $userId,
                    'goods_id' => $goodsId,
                    'secret' => $secret,
                    'status' => 0,
                    'create_time' => time(),
                    'unfreeze_time' => 0,
                    'first' => 0
                ];
                
                $id = Db::name($tableName)->insertGetId($data);
                
                Log::info("数据成功存储到表 {$tableName}, ID: {$id}");
                return $id;
            } catch (\Exception $e) {
                Log::error("插入数据到 {$tableName} 失败: " . $e->getMessage() . ", SQL: " . Db::getLastSql());
                Log::error("失败的数据: user_id={$userId}, goods_id={$goodsId}, secret长度=" . strlen($secret));
                Log::error("详细错误信息: " . print_r($e, true));
                
                // 如果不是默认表，尝试插入到默认表
                if ($tableName != $defaultTableName && $defaultTableExists) {
                    Log::info("尝试插入到默认表 {$defaultTableName}");
                    try {
                        $id = Db::name($defaultTableName)->insertGetId([
                            'user_id' => $userId,
                            'goods_id' => $goodsId,
                            'secret' => $secret,
                            'status' => 0,
                            'create_time' => time(),
                            'unfreeze_time' => 0,
                            'first' => 0
                        ]);
                        
                        Log::info("数据成功存储到默认表 {$defaultTableName}, ID: {$id}");
                        return $id;
                    } catch (\Exception $e2) {
                        Log::error("插入数据到默认表失败: " . $e2->getMessage() . ", SQL: " . Db::getLastSql());
                        Log::error("详细错误信息: " . print_r($e2, true));
                    }
                }
                return false;
            }
        } catch (\Exception $e) {
            Log::error("数据存储错误: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 判断是否应该使用单独的分表
     * 如果数据量小可以全部存入默认表
     */
    private function should_use_separate_table($goodsId) {
        // 这里可以根据实际情况设置条件
        // 例如：只有商品ID大于某个值时才使用分表
        // 或者通过配置参数控制是否使用分表
        
        // 默认使用分表
        return true;
    }
    
    /**
     * 生成存储表的建表SQL
     */
    private function generateStorageTableSql($tableName) {
        $prefix = config('database.connections.mysql.prefix');
        $fullTableName = $prefix . $tableName;
        
        return "CREATE TABLE IF NOT EXISTS `{$fullTableName}` (
          `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
          `user_id` bigint(20) NOT NULL,
          `goods_id` bigint(20) NOT NULL,
          `secret` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
          `status` tinyint(3) UNSIGNED NOT NULL DEFAULT '0' COMMENT '0未使用 1已使用',
          `first` tinyint(3) UNSIGNED NOT NULL DEFAULT '0' COMMENT '0默认 1优先销售',
          `create_time` int(10) UNSIGNED NOT NULL,
          `unfreeze_time` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '解冻时间',
          `sell_time` int(10) UNSIGNED DEFAULT NULL,
          `delete_time` int(11) DEFAULT NULL,
          PRIMARY KEY (`id`),
          KEY `user_id` (`user_id`),
          KEY `goods_id` (`goods_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    }
    
    /**
     * 根据商品ID计算存储表后缀
     */
    protected function goods_card_storage_suffix($goods_id) {
        return "_" . (intval($goods_id / 4000));
    }
    
    /**
     * 获取处理历史记录
     */
    public function getHistory() {
        try {
            // 获取当前用户ID
            $userId = $this->user->id;
            if (!$userId) {
                return json(['code' => 400, 'msg' => '获取用户信息失败']);
            }
            
            // 处理搜索参数
            $page = input('page/d', 1);
            $limit = input('limit/d', 10);
            $search = input('search/s', '');
            $dateRange = input('dateRange/a', []);
            
            // 先获取当前用户的商品ID列表
            $userGoodsIds = Db::name('goods')
                ->where('user_id', $userId)
                ->where('status', 1)  // 只获取上架的商品
                ->column('id');
            
            if (empty($userGoodsIds)) {
                return json(['code' => 200, 'msg' => '获取成功', 'data' => [], 'total' => 0]);
            }
            
            // 基础查询条件
            $where = [
                ['s.user_id', '=', $userId],  // 只查询当前用户的卡密数据
                ['s.goods_id', 'in', $userGoodsIds],  // 只查询属于当前用户商品的卡密
                ['s.delete_time', 'null', null]  // 排除已删除的记录
            ];
            
            // 添加搜索条件
            if (!empty($search)) {
                $where[] = ['s.secret', 'like', '%' . $search . '%'];
            }
            
            // 添加日期范围条件
            if (!empty($dateRange) && count($dateRange) === 2) {
                $startDate = $dateRange[0];
                $endDate = $dateRange[1];
                if ($startDate && $endDate) {
                    $where[] = ['s.create_time', 'between', [strtotime($startDate . ' 00:00:00'), strtotime($endDate . ' 23:59:59')]];
                }
            }

            // 获取所有可能的存储表
            $tables = ['goods_card_storage_0'];
            $prefix = config('database.connections.mysql.prefix');
            
            // 查找所有分表
            for ($i = 1; $i <= 10; $i++) {
                $tableName = 'goods_card_storage_' . $i;
                if (Db::query("SHOW TABLES LIKE '{$prefix}{$tableName}'")) {
                    $tables[] = $tableName;
                }
            }
            
            // 存储所有记录
            $allRecords = [];
            $totalCount = 0;
            
            // 遍历所有表查询数据
            foreach ($tables as $tableName) {
                try {
                    // 确保商品表关联是基于用户的商品
                    $query = Db::name($tableName)
                        ->alias('s')
                        ->join(['goods' => 'g'], 'g.id = s.goods_id AND g.user_id = ' . $userId)
                        ->where($where)
                        ->field([
                            's.id',
                            's.user_id',
                            's.goods_id',
                            's.secret',
                            's.status',
                            's.first',
                            's.create_time',
                            's.unfreeze_time',
                            's.sell_time',
                            'g.name as goods_name',
                            'g.image as goods_image'
                        ]);
                    
                    // 获取此表的记录数
                    $tableCount = $query->count();
                    
                    if ($tableCount > 0) {
                        // 获取此表的记录
                        $tableRecords = $query->select()->toArray();
                        
                        // 再次验证每条记录的所属权
                        foreach ($tableRecords as $key => $record) {
                            if ($record['user_id'] != $userId || !in_array($record['goods_id'], $userGoodsIds)) {
                                unset($tableRecords[$key]);
                                $tableCount--;
                                continue;
                            }
                        }
                        
                        if (!empty($tableRecords)) {
                            $allRecords = array_merge($allRecords, array_values($tableRecords));
                            $totalCount += $tableCount;
                        }
                    }
                } catch (\Exception $e) {
                    Log::error("查询表 {$tableName} 失败: " . $e->getMessage());
                    continue;
                }
            }
            
            // 对所有记录按创建时间排序
            usort($allRecords, function($a, $b) {
                return $b['create_time'] - $a['create_time'];
            });
            
            // 手动分页
            $offset = ($page - 1) * $limit;
            $records = array_slice($allRecords, $offset, $limit);
            
            // 处理记录数据
            foreach ($records as &$record) {
                // 格式化时间
                $record['create_time'] = date('Y-m-d H:i:s', $record['create_time']);
                $record['unfreeze_time'] = $record['unfreeze_time'] ? date('Y-m-d H:i:s', $record['unfreeze_time']) : '';
                $record['sell_time'] = $record['sell_time'] ? date('Y-m-d H:i:s', $record['sell_time']) : '';
                $record['status_text'] = $record['status'] == 0 ? '未使用' : '已使用';
                $record['first_text'] = $record['first'] == 0 ? '默认' : '优先销售';
                
                // 处理敏感信息
                $record['secret'] = $this->maskSensitiveInfo($record['secret']);
            }

            return json([
                'code' => 200, 
                'msg' => '获取成功', 
                'data' => $records, 
                'total' => $totalCount
            ]);

        } catch (\Exception $e) {
            Log::error('获取历史记录失败: ' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取历史记录失败']);
        }
    }
    
    /**
     * 处理敏感信息
     */
    private function maskSensitiveInfo($secret) {
        try {
            // 解析数据
            $data = $this->parse_user_data($secret);
            if ($data === false) {
                return $secret;
            }
            
            // 需要掩码的敏感字段
            $sensitiveFields = [
                '角色昵称', '账号', '用户名', '密码', 'QQ', '手机号', '邮箱',
                'access_token', 'token', 'key', 'secret'
            ];
            
            foreach ($data as $key => $value) {
                if (in_array($key, $sensitiveFields)) {
                    // 根据字段类型选择合适的掩码方式
                    if (strpos(strtolower($key), 'token') !== false || 
                        strpos(strtolower($key), 'key') !== false || 
                        strpos(strtolower($key), 'secret') !== false) {
                        $data[$key] = '******';
                    } else {
                        // 保留前两个字符和后两个字符
                        $len = mb_strlen($value);
                        if ($len > 4) {
                            $data[$key] = mb_substr($value, 0, 2) . str_repeat('*', $len - 4) . mb_substr($value, -2);
                        } else if ($len > 0) {
                            $data[$key] = mb_substr($value, 0, 1) . str_repeat('*', $len - 1);
                        }
                    }
                }
            }
            
            // 重新组合数据
            $maskedSecret = '';
            foreach ($data as $key => $value) {
                if ($maskedSecret !== '') {
                    $maskedSecret .= '|';
                }
                $maskedSecret .= $key . ':' . $value;
            }
            
            return $maskedSecret;
            
        } catch (\Exception $e) {
            Log::error('处理敏感信息失败: ' . $e->getMessage());
            return $secret;
        }
    }
    
    /**
     * 获取商品列表
     * 用于选择低等级角色的匹配商品
     */
    public function getGoodsList() {
        try {
            // 获取当前用户ID
            $userId = $this->user->id;
            if (!$userId) {
                return json(['code' => 400, 'msg' => '获取用户信息失败']);
            }
            
            // 获取当前商户的所有上架商品
            $goods = Db::name('goods')
                ->field(['id', 'name', 'price', 'image'])
                ->where('status', 1) // 只返回上架商品
                ->where('user_id', $userId) // 只返回当前商户的商品
                ->order('id', 'asc')
                ->select()
                ->toArray();
            
            return json(['code' => 200, 'msg' => '获取成功', 'data' => $goods]);
        } catch (\Exception $e) {
            Log::error('获取商品列表失败: ' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取商品列表失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 格式化仓库价值显示
     */
    private function format_warehouse_value($value) {
        // 如果数值较大，使用M单位显示
        if (is_numeric($value) && $value >= 1000000) {
            return round($value / 1000000, 2) . 'M ';
        }
        return $value;
    }

    /**
     * 过滤用户数据中的指定字段
     * 
     * @param string $userData 原始用户数据
     * @param array $filterFields 要过滤的字段列表
     * @return string 过滤后的用户数据
     */
    private function filter_user_data($userData, $filterFields) {
        if (empty($filterFields) || empty($userData)) {
            return $userData;
        }
        
        // 记录要过滤的字段和原始数据
        Log::info("开始过滤，字段列表: " . json_encode($filterFields, JSON_UNESCAPED_UNICODE));
        
        // 检查是否包含_Callback或access_token数据
        $hasCallback = strpos($userData, '_Callback') !== false;
        $hasAccessToken = preg_match('/access_token=[^|]+/i', $userData);
        
        // 如果数据中包含_Callback或access_token，暂时保存它们
        $callbackMatch = '';
        $accessTokenMatch = '';
        
        if ($hasCallback) {
            preg_match('/_Callback\(.*?\}\);/s', $userData, $matches);
            if (!empty($matches[0])) {
                $callbackMatch = $matches[0];
                $userData = str_replace($callbackMatch, '', $userData);
            }
        }
        
        if ($hasAccessToken) {
            preg_match('/\s*(?:access_token=[^|&\s]+(?:&[^|&\s]+)*)/i', $userData, $matches);
            if (!empty($matches[0])) {
                $accessTokenMatch = $matches[0];
                $userData = str_replace($accessTokenMatch, '', $userData);
            }
        }
        
        $userData = trim($userData);
        
        // 分割数据为字段
        $fields = explode('|', $userData);
        $filteredFields = [];
        
        foreach ($fields as $field) {
            $field = trim($field);
            if (empty($field)) continue;
            
            $shouldKeep = true;
            
            // 检查是否应该过滤这个字段
            foreach ($filterFields as $filterField) {
                // 检查字段名是否匹配过滤条件
                if (strpos($field, $filterField . '：') === 0 || 
                    strpos($field, $filterField . ':') === 0 || 
                    $field === $filterField) {
                    $shouldKeep = false;
                    Log::info("过滤掉字段: " . $field);
                    break;
                }
            }
            
            if ($shouldKeep) {
                $filteredFields[] = $field;
            }
        }
        
        // 重新组合过滤后的字段
        $filteredData = implode('|', $filteredFields);
        
        // 如果原始数据中有_Callback或access_token，按原样添加回去
        if ($hasAccessToken) {
            if (!preg_match('/\s$/', $filteredData)) {
                $filteredData .= ' ';
            }
            $filteredData .= $accessTokenMatch;
        }
        
        if ($hasCallback) {
            if (!preg_match('/\s$/', $filteredData)) {
                $filteredData .= ' ';
            }
            $filteredData .= $callbackMatch;
        }
        
        Log::info("过滤后的数据: " . $filteredData);
        
        return $filteredData;
    }

    /**
     * 根据指定类型强制匹配商品，并且考虑价值范围
     * @param string $type 类型：confidential(机密)或topsecret(绝密)
     * @param float $value 仓库价值
     * @return int|bool 商品ID或false
     */
    private function force_match_goods_type($type, $value = 0) {
        try {
            // 获取当前商户ID
            $userId = $this->user->id;
            if (!$userId) {
                Log::error("无法获取商户ID");
                return false;
            }
            
            Log::info("强制匹配商品类型: {$type}, 价值: {$value}");
            
            // 获取当前商户的所有上架商品
            $query = Db::name('goods')
                ->field(['id', 'name', 'price'])
                ->where('status', 1) // 只匹配上架商品
                ->where('user_id', $userId) // 只匹配当前商户的商品
                ->order('id', 'asc')
                ->select();
            
            if ($query->isEmpty()) {
                Log::warning("商户 {$userId} 没有上架的商品");
                return false;
            }
            
            // 记录所有查询到的商品
            $allGoodsNames = [];
            foreach ($query as $goods) {
                $allGoodsNames[] = $goods['id'] . ': ' . $goods['name'];
            }
            Log::info("商户所有上架商品: " . implode(', ', $allGoodsNames));
            
            // 商品分类存储
            $rangeMatchedGoods = []; // 存储带数字范围的商品
            $typeMatchedGoods = []; // 存储仅匹配类型的商品
            
            foreach ($query as $goods) {
                $name = $goods['name'];
                $isTypeMatched = false;
                
                // 首先根据类型筛选商品
                if ($type === 'confidential') {
                    // 匹配机密类型
                    if (stripos($name, '机密') !== false) {
                        $isTypeMatched = true;
                        Log::info("匹配到机密类型商品: {$name}");
                    }
                } else if ($type === 'topsecret') {
                    // 匹配绝密类型
                    if (stripos($name, '绝密') !== false) {
                        $isTypeMatched = true;
                        Log::info("匹配到绝密类型商品: {$name}");
                    }
                }
                
                // 如果类型匹配，再检查是否有数字范围
                if ($isTypeMatched) {
                    // 检查商品名称是否包含数字范围（如"1-10"、"0.1-1.5"等）
                    if (preg_match('/(\d+)-(\d+)/', $name) || 
                        preg_match('/(\d+\.\d+)-(\d+\.\d+)/', $name, $matches) ||
                        preg_match('/(\d+)-(\d+\.\d+)/', $name) ||
                        preg_match('/(\d+\.\d+)-(\d+)/', $name)) {
                        // 如果有数字范围，放入rangeMatchedGoods
                        $rangeMatchedGoods[] = $goods;
                        Log::info("匹配到带数字范围的{$type}类型商品: {$name}, ID: {$goods['id']}");
                    } else {
                        // 如果没有数字范围，放入typeMatchedGoods
                        $typeMatchedGoods[] = $goods;
                        Log::info("匹配到普通{$type}类型商品: {$name}, ID: {$goods['id']}");
                    }
                }
            }
            
            // 优先使用带数字范围的商品
            $matchedGoods = empty($rangeMatchedGoods) ? $typeMatchedGoods : $rangeMatchedGoods;
            
            // 如果没有匹配到商品，返回false
            if (empty($matchedGoods)) {
                Log::warning("未找到匹配的商品，类型: {$type}, 价值: {$value}");
                return false;
            }
            
            // 如果仓库价值为0或没有提供，直接返回第一个匹配的商品
            if ($value <= 0) {
                Log::info("无有效价值信息，返回第一个匹配的商品: {$matchedGoods[0]['name']}, ID: {$matchedGoods[0]['id']}");
                return $matchedGoods[0]['id'];
            }
            
            // 按价值范围对匹配的商品进行筛选
            $bestMatch = null;
            $bestMatchDiff = PHP_FLOAT_MAX;
            $anyInRange = false; // 标记是否有任何一个商品在范围内
            
            foreach ($matchedGoods as $goods) {
                $name = $goods['name'];
                
                // 解析价值范围
                $minValue = 0;
                $maxValue = PHP_FLOAT_MAX;
                $hasRange = false;
                
                // 尝试解析价值范围格式（如 "0.1-1"、"1M-5M"等）
                if (preg_match('/(\d+\.\d+)-(\d+\.?\d*)/', $name, $matches) || 
                    preg_match('/(\d+)-(\d+\.\d+)/', $name, $matches) || 
                    preg_match('/(\d+\.\d+)-(\d+\.\d+)/', $name, $matches) ||
                    preg_match('/(\d+)-(\d+)/', $name, $matches)) {
                    
                    $hasRange = true;
                    $minValue = floatval($matches[1]);
                    $maxValue = floatval($matches[2]);
                    
                    // 检查是否包含M单位
                    $multiplier = 1;
                    if (stripos($name, 'M') !== false) {
                        $multiplier = 1000000; // 如果包含M，数值代表百万
                    }
                    
                    $minValue = $minValue * $multiplier;
                    $maxValue = $maxValue * $multiplier;
                    
                    Log::info("解析价值范围: 原始范围={$matches[1]}-{$matches[2]}, 转换后范围={$minValue}-{$maxValue}");
                    
                    // 检查价值是否在范围内
                    if (floatval($value) >= $minValue && floatval($value) <= $maxValue) {
                        $anyInRange = true;
                        Log::info("价值 {$value} 在商品 {$name} 的范围 {$minValue}-{$maxValue} 内，直接匹配");
                        return $goods['id'];
                    }
                    
                    // 即使价值不在范围内，也记录差距以便找出最接近的商品
                    $diff = 0;
                    if ($value < $minValue) {
                        $diff = $minValue - floatval($value);
                    } else if ($value > $maxValue) {
                        $diff = floatval($value) - $maxValue;
                    }
                    
                    // 如果这个差距比之前找到的更小，更新最佳匹配
                    if ($diff < $bestMatchDiff) {
                        $bestMatchDiff = $diff;
                        $bestMatch = $goods;
                        Log::info("更新最佳匹配: {$name}, 差距: {$diff}，但不在范围内");
                    }
                    
                    // 添加到带范围的商品列表
                    $rangeMatchedGoods[] = [
                        'goods' => $goods,
                        'min' => $minValue,
                        'max' => $maxValue
                    ];
                } else {
                    $normalGoods[] = $goods;
                }
            }
            
            // 如果没有找到严格在范围内的匹配
            if (!$anyInRange) {
                // 提供更详细的日志，说明为什么没有匹配到
                if ($bestMatch !== null) {
                    Log::warning("价值 {$value} 不在任何商品范围内，最接近的是 {$bestMatch['name']}，差距: {$bestMatchDiff}");
                    
                    // 返回一个标记，表示未找到匹配
                    return false;
                } else {
                    Log::warning("未找到任何匹配的商品，价值: {$value}");
                    return false;
                }
            }
            
            // 理论上不应该到达这里，因为如果在范围内已经提前返回了
            Log::error("匹配逻辑错误，不应该到达这里: 价值={$value}");
            return false;
            
        } catch (\Exception $e) {
            Log::error("强制匹配商品错误: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 先根据等级再根据价值范围匹配商品
     * @param int $level 角色等级
     * @param float $value 仓库价值
     * @param array $roleData 角色数据
     * @return int|bool 商品ID或false
     */
    private function match_goods_by_level_and_value($level, $value, $roleData = null) {
        try {
            // 获取当前商户ID
            $userId = $this->user->id;
            if (!$userId) {
                Log::error("无法获取商户ID");
                return false;
            }
            
            // 转换价值为浮点数
            $floatValue = floatval($value);
            $originalValue = $value; // 保存原始值用于日志
            
            Log::info("开始匹配商品: 等级={$level}, 价值={$floatValue} (原始值: {$originalValue})");
            
            // 1. 首先确定角色等级类型
            $levelType = '';
            if ($level >= 30) {
                $levelType = '绝密';
            } elseif ($level >= 12) {
                $levelType = '机密';
            } else {
                Log::info("等级 {$level} 小于12级，需要手动指定商品");
                return false;
            }
            
            Log::info("根据等级 {$level} 确定类型: {$levelType}");
            
            // 2. 获取当前商户的所有上架商品
            $query = Db::name('goods')
                ->field(['id', 'name', 'price'])
                ->where('status', 1)
                ->where('user_id', $userId)
                ->order('id', 'asc')
                ->select()
                ->toArray();
            
            if (empty($query)) {
                Log::warning("商户 {$userId} 没有上架的商品");
                return false;
            }
            
            // 记录所有查询到的商品名
            Log::info("商户所有商品: " . implode(', ', array_column($query, 'name')));
            
            // 3. 筛选符合等级类型的商品
            $levelMatchedGoods = [];
            foreach ($query as $goods) {
                if (stripos($goods['name'], $levelType) !== false) {
                    $levelMatchedGoods[] = $goods;
                }
            }
            
            if (empty($levelMatchedGoods)) {
                Log::warning("未找到任何包含 {$levelType} 的商品");
                return false;
            }
            
            Log::info("符合 {$levelType} 的商品: " . implode(', ', array_column($levelMatchedGoods, 'name')));
            
            // 4. 在符合等级的商品中，筛选价值范围匹配的商品
            $inRangeMatches = [];
            $allPriceRanges = [];
            
            foreach ($levelMatchedGoods as $goods) {
                $name = $goods['name'];
                
                // 解析商品名称中的价值范围
                $valueRange = $this->parse_value_range($name);
                if (!$valueRange) {
                    Log::info("商品 {$name} 未包含可识别的价值范围");
                    continue;
                }
                
                $minValue = $valueRange['min'];
                $maxValue = $valueRange['max'];
                
                // 记录所有价值范围，便于日志输出
                $minValueM = $minValue / 1000000;
                $maxValueM = $maxValue / 1000000;
                $allPriceRanges[] = "{$name}: {$minValueM}-{$maxValueM}M";
                
                Log::info("商品 {$name} 价值范围: {$minValue}-{$maxValue}, 当前角色价值: {$floatValue}");
                
                // 检查角色价值是否在商品价值范围内
                if ($floatValue >= $minValue && $floatValue <= $maxValue) {
                    Log::info("价值 {$floatValue} 在商品 {$name} 的范围内，精确匹配成功");
                    $inRangeMatches[] = $goods;
                }
            }
            
            // 5. 返回结果处理
            // 如果有精确范围匹配，使用第一个
            if (!empty($inRangeMatches)) {
                Log::info("使用精确匹配商品: {$inRangeMatches[0]['name']}");
                return $inRangeMatches[0]['id'];
            }
            
            // 如果没有精确匹配，返回无法匹配
            $formattedValue = $floatValue >= 1000000 ? ($floatValue / 1000000) . "M" : $floatValue;
            Log::warning("无法匹配价值: {$formattedValue}，可用范围: " . implode(', ', $allPriceRanges));
            return false;
            
        } catch (\Exception $e) {
            Log::error("匹配商品失败: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return false;
        }
    }
    
    /**
     * 解析商品名称中的价值范围
     * @param string $name 商品名称
     * @return array|false 解析结果或false
     */
    private function parse_value_range($name) {
        try {
            // 记录原始商品名用于调试
            $originalName = $name;
            
            // 支持的格式模式
            $patterns = [
                // 标准范围格式：10-20M, 1.5-2.5M, 0.5M-1.5M
                '/(\d+(?:\.\d+)?)\s*[Mm]?\s*-\s*(\d+(?:\.\d+)?)\s*[Mm]/',
                // 单一值格式：10M, 1.5M
                '/(\d+(?:\.\d+)?)\s*[Mm](?!\s*-)/i',
                // K单位范围：100K-500K
                '/(\d+(?:\.\d+)?)\s*[Kk]\s*-\s*(\d+(?:\.\d+)?)\s*[Kk]/',
                // 纯数字大范围：1000000-2000000
                '/(\d{7,})\s*-\s*(\d{7,})/'
            ];
            
            foreach ($patterns as $index => $pattern) {
                if (preg_match($pattern, $name, $matches)) {
                    // 记录匹配到的模式
                    Log::info("商品 {$name} 匹配到价值模式 #{$index}: " . json_encode($matches));
                    
                    // 处理单一值格式 (如: 10M)
                    if (count($matches) == 2) {
                        $value = floatval($matches[1]);
                        // 处理单位
                        if (stripos($name, 'M') !== false) {
                            $value *= 1000000;
                        } elseif (stripos($name, 'K') !== false) {
                            $value *= 1000;
                        }
                        
                        // 允许15%的浮动范围
                        $result = [
                            'min' => $value * 0.85,
                            'max' => $value * 1.15,
                            'original' => $originalName,
                            'match_text' => $matches[0]
                        ];
                        
                        Log::info("解析单值结果: " . json_encode($result));
                        return $result;
                    }
                    // 处理范围格式 (如: 10-20M)
                    else if (count($matches) >= 3) {
                        $minValue = floatval($matches[1]);
                        $maxValue = floatval($matches[2]);
                        
                        // 处理单位
                        if (stripos($name, 'M') !== false) {
                            $minValue *= 1000000;
                            $maxValue *= 1000000;
                        } elseif (stripos($name, 'K') !== false) {
                            $minValue *= 1000;
                            $maxValue *= 1000;
                        }
                        
                        $result = [
                            'min' => $minValue,
                            'max' => $maxValue,
                            'original' => $originalName,
                            'match_text' => $matches[0]
                        ];
                        
                        Log::info("解析范围结果: " . json_encode($result));
                        return $result;
                    }
                }
            }
            
            Log::info("商品 {$name} 无法解析价值范围");
            return false;
            
        } catch (\Exception $e) {
            Log::error("解析价值范围失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 从解析数据中提取角色等级
     * @param array $parsedData 解析后的角色数据
     * @return int 角色等级，如果无法提取则返回0
     */
    private function extract_level($parsedData) {
        if (empty($parsedData)) {
            return 0;
        }
        
        // 可能的等级字段名列表
        $levelFieldNames = ['等级', 'lv', 'Lv', 'LV', '级别', '人物等级', '角色等级', '级', 'level', 'Level'];
        
        // 首先尝试直接匹配完整的等级字段
        foreach ($levelFieldNames as $fieldName) {
            if (isset($parsedData[$fieldName])) {
                // 提取纯数字
                if (preg_match('/(\d+)/', $parsedData[$fieldName], $matches)) {
                    return intval($matches[1]);
                }
            }
        }
        
        // 如果没找到，尝试在所有字段中查找匹配等级的模式
        foreach ($parsedData as $fieldName => $fieldValue) {
            // 检查字段名是否包含"等级"或"lv"相关关键字
            if (stripos($fieldName, '等级') !== false || stripos($fieldName, 'lv') !== false || 
                stripos($fieldName, '级') !== false || stripos($fieldName, 'level') !== false) {
                // 尝试从字段值中提取数字
                if (preg_match('/(\d+)/', $fieldValue, $matches)) {
                    return intval($matches[1]);
                }
            }
            
            // 尝试从字段名和值中匹配"XX级"或"LvXX"的模式
            if (preg_match('/(\d+)级/u', $fieldName . $fieldValue, $matches) || 
                preg_match('/[Ll][Vv](\d+)/i', $fieldName . $fieldValue, $matches)) {
                return intval($matches[1]);
            }
        }
        
        return 0;
    }

    /**
     * 获取所有商品卡密存储表
     * @return array 表名数组
     */
    private function get_storage_tables() {
        try {
            // 查询所有商品卡密存储表
            $tables = Db::query('SHOW TABLES LIKE ?', [config('database.connections.mysql.prefix') . 'goods_card_storage_%']);
            if (empty($tables)) {
                Log::info("没有找到任何商品卡密存储表");
                return [];
            }
            
            // 提取完整表名
            $tableNames = [];
            foreach ($tables as $table) {
                $tableName = current($table);
                if (!empty($tableName)) {
                    $tableNames[] = $tableName;
                }
            }
            
            Log::info("找到 " . count($tableNames) . " 个商品卡密存储表");
            return $tableNames;
        } catch (\Exception $e) {
            Log::error("获取商品卡密存储表失败: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 导出失败的原始数据
     * @return \think\Response
     */
    public function exportFailedData()
    {
        try {
            // 获取缓存ID
            $cacheId = input('cache_id', '', 'trim');
            if (empty($cacheId)) {
                return json(['status' => 'error', 'message' => '缺少必要参数']);
            }
            
            // 解码缓存ID
            $cacheKey = base64_decode($cacheId);
            Log::info("导出失败数据，缓存键: {$cacheKey}");
            
            // 从缓存中获取失败数据
            $failedData = cache($cacheKey);
            if (empty($failedData)) {
                Log::error("未找到失败数据或数据已过期，缓存键: {$cacheKey}");
                return json(['status' => 'error', 'message' => '未找到失败数据或数据已过期']);
            }
            
            // 确保失败数据是数组
            if (!is_array($failedData)) {
                Log::error("缓存中的失败数据格式错误: " . gettype($failedData));
                return json(['status' => 'error', 'message' => '导出失败: 缓存数据格式错误']);
            }
            
            // 记录导出的数据条数
            Log::info("导出失败数据，数据条数: " . count($failedData));
            
            // 调试数据的结构
            Log::info("失败数据的第一条结构: " . (isset($failedData[0]) ? json_encode($failedData[0], JSON_UNESCAPED_UNICODE) : '无数据'));
            
            // 仅导出原始数据，完全按照导入时的格式
            $content = "";
            $dataCount = 0;
            
            foreach ($failedData as $data) {
                // 只检查raw_data字段是否存在
                if (isset($data['raw_data']) && !empty($data['raw_data'])) {
                    // 直接添加原始数据，不做任何修改
                    $content .= $data['raw_data'] . "\n";
                    $dataCount++;
                } else {
                    Log::warning("发现缺少raw_data的失败数据项: " . json_encode($data, JSON_UNESCAPED_UNICODE));
                }
            }
            
            // 如果没有有效数据，返回错误
            if ($dataCount == 0) {
                Log::error("没有有效的失败数据可以导出");
                return json(['status' => 'error', 'message' => '没有有效的失败数据可以导出']);
            }
            
            Log::info("成功导出 {$dataCount} 条失败数据");
            
            // 设置响应头，保持原始编码
            return response($content, 200, [
                'Content-Type' => 'text/plain; charset=UTF-8',
                'Content-Disposition' => 'attachment; filename="failed_data_' . date('YmdHis') . '.txt"'
            ]);
        } catch (\Exception $e) {
            Log::error("导出失败数据异常: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return json(['status' => 'error', 'message' => '导出失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 缓存成功处理的数据，用于下次提交时过滤
     * @param array $results 处理结果
     * @param int $userId 用户ID
     */
    private function cacheSuccessData($results, $userId) {
        if (empty($results) || !$userId) {
            return;
        }
        
        // 只保存成功处理的数据
        $successData = [];
        foreach ($results as $result) {
            if ($result['status'] === 'success') {
                // 提取关键信息用于后续匹配
                $successData[] = [
                    'nickname' => $result['nickname'],
                    'level' => $result['level'],
                    'warehouse_value' => $result['warehouse_value'],
                    'timestamp' => time()
                ];
            }
        }
        
        if (empty($successData)) {
            return;
        }
        
        // 获取已有的缓存数据
        $cacheKey = 'success_data_' . $userId;
        $existingData = cache($cacheKey) ?: [];
        
        // 合并数据，确保不重复
        $mergedData = $this->mergeSuccessData($existingData, $successData);
        
        // 缓存合并后的数据（有效期30天）
        cache($cacheKey, $mergedData, 30 * 24 * 3600); // 30天缓存，而不是永久
        Log::info("成功缓存用户 {$userId} 的 " . count($successData) . " 条成功数据，总缓存数: " . count($mergedData));
    }
    
    /**
     * 清除指定角色的成功缓存记录
     * @param string $nickname 角色昵称
     * @param int $userId 用户ID
     * @return bool 是否成功
     */
    private function clearRoleSuccessCache($nickname, $userId) {
        if (empty($nickname) || !$userId) {
            return false;
        }
        
        // 标准化昵称
        $nickname = trim($nickname);
        if (empty($nickname)) {
            return false;
        }
        
        // 获取已有的缓存数据
        $cacheKey = 'success_data_' . $userId;
        $existingData = cache($cacheKey) ?: [];
        
        if (empty($existingData)) {
            return false;
        }
        
        // 移除匹配的角色
        $newData = [];
        $found = false;
        
        foreach ($existingData as $item) {
            // 不区分大小写比较角色昵称
            if (isset($item['nickname']) && strcasecmp($item['nickname'], $nickname) !== 0) {
                $newData[] = $item;
            } else {
                $found = true;
                Log::info("已从缓存中移除角色: {$nickname}");
            }
        }
        
        // 如果找到并移除了数据，更新缓存
        if ($found) {
            cache($cacheKey, $newData, 30 * 24 * 3600); // 30天缓存
            return true;
        }
        
        return false;
    }
    
    /**
     * 合并成功数据，避免重复
     * @param array $existing 已存在的数据
     * @param array $new 新数据
     * @return array 合并后的数据
     */
    private function mergeSuccessData($existing, $new) {
        if (empty($existing)) {
            return $new;
        }
        
        if (empty($new)) {
            return $existing;
        }
        
        // 使用角色昵称作为唯一标识
        $nicknameMap = [];
        foreach ($existing as $item) {
            if (isset($item['nickname'])) {
                $nicknameMap[strtolower($item['nickname'])] = $item;
            }
        }
        
        // 添加新数据，相同昵称的更新
        foreach ($new as $item) {
            if (isset($item['nickname'])) {
                $key = strtolower($item['nickname']);
                $nicknameMap[$key] = $item; // 无论是否存在都更新为最新的
            }
        }
        
        // 返回所有数据，不设置时间限制
        return array_values($nicknameMap);
    }
    
    /**
     * 检查当前数据是否与之前成功处理的数据匹配
     * @param array $parsedData 解析后的数据
     * @param int $userId 用户ID
     * @return bool 是否匹配
     */
    private function checkIfPreviouslySucceeded($parsedData, $userId) {
        if (empty($parsedData) || !$userId || !isset($parsedData['角色昵称'])) {
            return false;
        }
        
        $nickname = trim($parsedData['角色昵称']);
        if (empty($nickname)) {
            return false;
        }
        
        // 获取已有的缓存数据
        $cacheKey = 'success_data_' . $userId;
        $existingData = cache($cacheKey) ?: [];
        
        if (empty($existingData)) {
            return false;
        }
        
        // 查找匹配的昵称
        foreach ($existingData as $item) {
            if (strcasecmp($item['nickname'], $nickname) === 0) {
                return $item; // 返回匹配的缓存数据
            }
        }
        
        return false;
    }

    /**
     * 清除指定角色的缓存成功记录
     * @return \think\Response
     */
    public function clearRoleCache()
    {
        try {
            // 获取当前用户ID (从不同来源尝试获取)
            $userId = 0;
            
            // 记录调试信息
            $debugInfo = ['method' => 'clearRoleCache'];
            
            // 方法1: 从请求参数merchant_id获取（最优先）
            $userId = input('merchant_id/d', 0);
            $debugInfo['merchant_id_param'] = $userId;
            
            if (!$userId) {
                // 方法2: 从$this->merchant_id获取
                if (isset($this->merchant_id) && $this->merchant_id) {
                    $userId = $this->merchant_id;
                    $debugInfo['merchant_id_property'] = $userId;
                } 
                
                // 方法3: 从user对象获取
                if (!$userId && isset($this->user) && $this->user && isset($this->user->id)) {
                    $userId = $this->user->id;
                    $debugInfo['user_id_property'] = $userId;
                }
            }
            
            // 记录所有尝试获取用户ID的方法
            Log::info("尝试获取用户ID的方法: " . json_encode($debugInfo, JSON_UNESCAPED_UNICODE));
            
            if (!$userId) {
                return json(['status' => 'error', 'message' => '未能获取用户ID，请确保URL中包含merchant_id参数']);
            }
            
            // 获取要清除的角色昵称
            $nickname = input('nickname', '', 'trim');
            if (empty($nickname)) {
                return json(['status' => 'error', 'message' => '请提供角色昵称']);
            }
            
            // 记录操作日志
            Log::info("用户 {$userId} 请求清除角色缓存: {$nickname}");
            
            // 调用清除缓存的方法
            $result = $this->clearRoleSuccessCache($nickname, $userId);
            
            if ($result) {
                return json(['status' => 'success', 'message' => "已清除角色「{$nickname}」的缓存记录"]);
            } else {
                return json(['status' => 'warning', 'message' => "未找到角色「{$nickname}」的缓存记录"]);
            }
        } catch (\Exception $e) {
            Log::error("清除角色缓存失败: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return json(['status' => 'error', 'message' => '操作失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 清除当前用户的所有缓存成功记录
     * @return \think\Response
     */
    public function clearAllRoleCache()
    {
        try {
            // 获取当前用户ID (从不同来源尝试获取)
            $userId = 0;
            
            // 记录调试信息
            $debugInfo = ['method' => 'clearAllRoleCache'];
            
            // 方法1: 从请求参数merchant_id获取（最优先）
            $userId = input('merchant_id/d', 0);
            $debugInfo['merchant_id_param'] = $userId;
            
            if (!$userId) {
                // 方法2: 从$this->merchant_id获取
                if (isset($this->merchant_id) && $this->merchant_id) {
                    $userId = $this->merchant_id;
                    $debugInfo['merchant_id_property'] = $userId;
                } 
                
                // 方法3: 从user对象获取
                if (!$userId && isset($this->user) && $this->user && isset($this->user->id)) {
                    $userId = $this->user->id;
                    $debugInfo['user_id_property'] = $userId;
                }
            }
            
            // 记录所有尝试获取用户ID的方法
            Log::info("尝试获取用户ID的方法: " . json_encode($debugInfo, JSON_UNESCAPED_UNICODE));
            
            if (!$userId) {
                return json(['status' => 'error', 'message' => '未能获取用户ID，请确保URL中包含merchant_id参数']);
            }
            
            // 记录操作日志
            Log::info("用户 {$userId} 请求清除所有角色缓存");
            
            // 清除所有缓存
            $cacheKey = 'success_data_' . $userId;
            cache($cacheKey, null); // 设置为null表示删除缓存
            
            return json(['status' => 'success', 'message' => '已清除所有角色的缓存记录']);
        } catch (\Exception $e) {
            Log::error("清除所有角色缓存失败: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return json(['status' => 'error', 'message' => '操作失败: ' . $e->getMessage()]);
        }
    }

}