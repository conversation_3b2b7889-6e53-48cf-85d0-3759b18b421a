<?php

# 配置文件

return [

    // 网站基本信息
    [
        'id' => 'website_url',
        'name' => '网站官方网址',
        'placeholder' => 'https://www.example.com',
        'type' => 'input',
    ],

    // 页脚描述信息
    [
        'id' => 'footer_description',
        'name' => '页脚描述信息',
        'placeholder' => '鲸商pro是一家专业的电商支付解决方案提供商，致力于为商家提供安全、便捷、稳定的虚拟商品自动交易服务。',
        'type' => 'input',
    ],
    
    // 页脚快速链接
    [
        'id' => 'footer_quicklinks_title',
        'name' => '快速链接标题',
        'placeholder' => '快速链接',
        'type' => 'input',
    ],
    [
        'id' => 'footer_quicklink_1',
        'name' => '快速链接1名称',
        'placeholder' => '首页',
        'type' => 'input',
    ],
    [
        'id' => 'footer_quicklink_1_link',
        'name' => '快速链接1地址',
        'placeholder' => '#',
        'type' => 'input',
    ],
    [
        'id' => 'footer_quicklink_2',
        'name' => '快速链接2名称',
        'placeholder' => '平台优势',
        'type' => 'input',
    ],
    [
        'id' => 'footer_quicklink_2_link',
        'name' => '快速链接2地址',
        'placeholder' => '#',
        'type' => 'input',
    ],
    [
        'id' => 'footer_quicklink_3',
        'name' => '快速链接3名称',
        'placeholder' => '解决方案',
        'type' => 'input',
    ],
    [
        'id' => 'footer_quicklink_3_link',
        'name' => '快速链接3地址',
        'placeholder' => '#',
        'type' => 'input',
    ],
    [
        'id' => 'footer_quicklink_4',
        'name' => '快速链接4名称',
        'placeholder' => '帮助中心',
        'type' => 'input',
    ],
    [
        'id' => 'footer_quicklink_4_link',
        'name' => '快速链接4地址',
        'placeholder' => '#',
        'type' => 'input',
    ],
    [
        'id' => 'footer_quicklink_5',
        'name' => '快速链接5名称',
        'placeholder' => '服务协议',
        'type' => 'input',
    ],
    [
        'id' => 'footer_quicklink_5_link',
        'name' => '快速链接5地址',
        'placeholder' => '#',
        'type' => 'input',
    ],
    
    // 页脚产品服务
    [
        'id' => 'footer_products_title',
        'name' => '产品服务标题',
        'placeholder' => '产品服务',
        'type' => 'input',
    ],
    [
        'id' => 'footer_product_1',
        'name' => '产品服务1名称',
        'placeholder' => '支付渠道',
        'type' => 'input',
    ],
    [
        'id' => 'footer_product_1_link',
        'name' => '产品服务1地址',
        'placeholder' => '#',
        'type' => 'input',
    ],
    [
        'id' => 'footer_product_2',
        'name' => '产品服务2名称',
        'placeholder' => '商品管理',
        'type' => 'input',
    ],
    [
        'id' => 'footer_product_2_link',
        'name' => '产品服务2地址',
        'placeholder' => '#',
        'type' => 'input',
    ],
    [
        'id' => 'footer_product_3',
        'name' => '产品服务3名称',
        'placeholder' => '订单管理',
        'type' => 'input',
    ],
    [
        'id' => 'footer_product_3_link',
        'name' => '产品服务3地址',
        'placeholder' => '#',
        'type' => 'input',
    ],
    [
        'id' => 'footer_product_4',
        'name' => '产品服务4名称',
        'placeholder' => '用户管理',
        'type' => 'input',
    ],
    [
        'id' => 'footer_product_4_link',
        'name' => '产品服务4地址',
        'placeholder' => '#',
        'type' => 'input',
    ],
    [
        'id' => 'footer_product_5',
        'name' => '产品服务5名称',
        'placeholder' => '分销系统',
        'type' => 'input',
    ],
    [
        'id' => 'footer_product_5_link',
        'name' => '产品服务5地址',
        'placeholder' => '#',
        'type' => 'input',
    ],
    
    // 页脚联系信息
    [
        'id' => 'footer_contact_title',
        'name' => '联系信息标题',
        'placeholder' => '联系我们',
        'type' => 'input',
    ],
    [
        'id' => 'footer_contact_address',
        'name' => '联系地址',
        'placeholder' => '北京市朝阳区科技园区88号',
        'type' => 'input',
    ],
    [
        'id' => 'footer_contact_phone',
        'name' => '联系电话',
        'placeholder' => '************',
        'type' => 'input',
    ],
    [
        'id' => 'footer_contact_email',
        'name' => '联系邮箱',
        'placeholder' => '<EMAIL>',
        'type' => 'input',
    ],
    [
        'id' => 'footer_contact_hours',
        'name' => '营业时间',
        'placeholder' => '周一至周日 9:00-18:00',
        'type' => 'input',
    ],
    [
        'id' => 'contact_wx',
        'name' => '微信',
        'placeholder' => '微信',
        'type' => 'input',
    ],
    [
        'id' => 'contact_qq',
        'name' => 'QQ',
        'placeholder' => 'qq',
        'type' => 'input',
    ],
    
    // 页脚版权信息
    [
        'id' => 'footer_copyright',
        'name' => '版权信息',
        'placeholder' => '© 2023 鲸商pro 版权所有 | 京ICP备12345678号-1 | 京公网安备11010502030123号',
        'type' => 'textarea',
    ],
];
