<?php

namespace plugin\Customname;

use think\facade\Db;

class Hook
{
    public function handle()
    {
        $status = intval(plugconf("Customname.status") ?? 0);
        $merchantName = plugconf("Customname.merchantName") ?? '';

        if ($merchantName && $status === 1) {
            try {
                $merchants = Db::name('user')
                    ->where('nickname', 'like', '商家%')
                    ->order('id', 'desc')
                    ->select();

                if ($merchants->isEmpty()) {
                    trace("没有找到需要更新的商家昵称", 'notice');
                    return;
                }
                foreach ($merchants as $merchant) {
                    $newNickname = $merchantName . mt_rand(1000, 9999);
                
                    Db::name('user')
                        ->where('id', $merchant['id'])
                        ->update([
                            'nickname' => $newNickname,
                            'update_time' => time()
                        ]);
                }
                trace("成功更新商家昵称", 'info');
            } catch (\Exception $e) {
                trace("更新商家昵称失败：" . $e->getMessage(), 'error');
            }
        }
    }
}
