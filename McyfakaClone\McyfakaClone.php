<?php

namespace plugin\McyfakaClone;

use app\common\library\Plugin;
use app\common\model\GoodsEquityApp as GoodsEquityAppModel;

class McyfakaClone extends Plugin {

    /**
     * 插件安装方法
     * @return bool
     */
    public function install() {
        $app = GoodsEquityAppModel::where(['code' => 'McyfakaClone'])->find();
        if (!$app) {
            $app = new GoodsEquityAppModel();
        }
        $app->code = "McyfakaClone";
        $app->name = "异次元3.0商品克隆";
        $app->image = "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjNDA5RUZGIiBzdHJva2Utd2lkdGg9IjEuNSI+PHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMCA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDQgOUwxMC45MSA4LjI2TDEyIDJaIiBzdHJva2U9IiM0MDlFRkYiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPjwvc3ZnPg==";
        $app->sub_name = "从异次元3.0系统批量克隆商品";
        $app->description = '<p>对接参考获取说明：</p><p>1、前往异次元3.0系统后台，进入API管理页面</p><p>2、创建API应用，获取API Key和API Secret</p><p>3、确保API应用有商品相关权限</p><p>4、在插件配置中填写API地址、API Key和API Secret</p><p>5、选择要克隆的商品分类和目标分类</p><p>6、批量克隆商品到本地系统</p>';
        $app->system_field = [
            [
                'id' => 'api_url',
                'name' => 'API地址',
                'remark' => '请输入异次元3.0系统的API地址，如：https://your-domain.com',
                'required' => true,
                'type' => 'input',
                'data' => [],
                'accept' => '',
            ],
            [
                'id' => 'api_key',
                'name' => 'API Key',
                'remark' => '请输入异次元3.0系统提供的API Key',
                'required' => true,
                'type' => 'input',
                'data' => [],
                'accept' => '',
            ],
            [
                'id' => 'api_secret',
                'name' => 'API Secret',
                'remark' => '请输入异次元3.0系统提供的API Secret',
                'required' => true,
                'type' => 'input',
                'data' => [],
                'accept' => '',
            ],
            [
                'id' => 'source_category_id',
                'name' => '源分类ID',
                'remark' => '请输入要克隆的异次元3.0商品分类ID',
                'required' => true,
                'type' => 'input',
                'data' => [],
                'accept' => '',
            ],
            [
                'id' => 'target_category_id',
                'name' => '目标分类ID',
                'remark' => '请输入本地系统的目标分类ID',
                'required' => true,
                'type' => 'input',
                'data' => [],
                'accept' => '',
            ],
            [
                'id' => 'clone_images',
                'name' => '克隆图片',
                'remark' => '是否下载并保存商品图片到本地',
                'required' => false,
                'type' => 'select',
                'data' => [
                    ['value' => '1', 'name' => '是'],
                    ['value' => '0', 'name' => '否']
                ],
                'accept' => '',
            ],
        ];
        $app->buyer_field = [];
        $app->create_time = time();
        $app->save();

        return true;
    }

    /**
     * 插件卸载方法
     * @return bool
     */
    public function uninstall() {
        $app = GoodsEquityAppModel::where(['code' => 'McyfakaClone'])->find();
        if ($app) {
            $app->delete();
        }
        return true;
    }

    // 查询状态
    public function askStatus($biz_content) {
        return true;
    }

    // 订单处理 - 克隆商品
    public function orderDetail($biz_content) {
        try {
            $api_url = $biz_content['system_value']['api_url'] ?? '';
            $api_key = $biz_content['system_value']['api_key'] ?? '';
            $api_secret = $biz_content['system_value']['api_secret'] ?? '';
            $source_category_id = $biz_content['system_value']['source_category_id'] ?? '';
            $target_category_id = $biz_content['system_value']['target_category_id'] ?? '';
            $clone_images = $biz_content['system_value']['clone_images'] ?? '0';
            $quantity = $biz_content['quantity'] ?? 1;

            if (empty($api_url) || empty($api_key) || empty($api_secret)) {
                return ['status' => 1, 'msg' => '请先配置API参数'];
            }

            if (empty($source_category_id) || empty($target_category_id)) {
                return ['status' => 1, 'msg' => '请配置源分类ID和目标分类ID'];
            }

            // 获取异次元3.0商品列表
            $goods_list = $this->getAcgGoods($api_url, $api_key, $api_secret, $source_category_id, $quantity);

            if (empty($goods_list)) {
                return ['status' => 1, 'msg' => '未找到可克隆的商品'];
            }

            $success_count = 0;
            $error_messages = [];

            foreach ($goods_list as $goods) {
                try {
                    $this->cloneGoodsToLocal($goods, $target_category_id, $clone_images == '1');
                    $success_count++;
                } catch (\Exception $e) {
                    $error_messages[] = "商品 {$goods['name']} 克隆失败：" . $e->getMessage();
                }
            }

            $html_content = "<div style='padding:8px'>";
            $html_content .= "<p><b style='color:green'>成功克隆 {$success_count} 个商品</b></p>";

            if (!empty($error_messages)) {
                $html_content .= "<p><b style='color:red'>错误信息：</b></p>";
                foreach ($error_messages as $error) {
                    $html_content .= "<p style='color:red'>• {$error}</p>";
                }
            }

            $html_content .= "</div>";

            return ['status' => 2, 'msg' => '克隆完成', 'data' => $html_content];

        } catch (\Exception $e) {
            return ['status' => 1, 'msg' => '克隆失败：' . $e->getMessage()];
        }
    }

    /**
     * 获取异次元3.0商品列表
     */
    private function getAcgGoods($api_url, $api_key, $api_secret, $category_id, $limit = 10) {
        $params = [
            'category_id' => $category_id,
            'page' => 1,
            'limit' => $limit,
            'timestamp' => time(),
            'nonce' => uniqid()
        ];

        $response = $this->requestAcgApi($api_url, '/api/goods/list', $api_key, $api_secret, $params);
        return $response['list'] ?? [];
    }

    /**
     * 克隆商品到本地
     */
    private function cloneGoodsToLocal($goods_data, $target_category_id, $clone_images = false) {
        // 检查商品是否已存在
        $existing = \app\common\model\Goods::where('name', $goods_data['name'])
            ->where('description', $goods_data['description'] ?? '')
            ->find();

        if ($existing) {
            throw new \Exception('商品已存在');
        }

        // 创建商品
        $goods = new \app\common\model\Goods();
        $goods->user_id = 1; // 这里需要根据实际情况设置用户ID
        $goods->category_id = $target_category_id;
        $goods->name = $goods_data['name'];
        $goods->description = $goods_data['description'] ?? '';
        $goods->price = $goods_data['price'] ?? 0;
        $goods->cost_price = $goods_data['cost_price'] ?? 0;
        $goods->market_price = $goods_data['market_price'] ?? 0;
        $goods->stock = $goods_data['stock'] ?? 0;
        $goods->sales = 0;
        $goods->status = $goods_data['status'] ?? 1;
        $goods->sort = $goods_data['sort'] ?? 0;
        $goods->is_show = $goods_data['is_show'] ?? 1;
        $goods->delivery_way = 1; // 自动发货
        $goods->delivery_message = $goods_data['delivery_message'] ?? '';

        // 处理商品图片
        if ($clone_images && !empty($goods_data['image'])) {
            $goods->image = $this->downloadImage($goods_data['image']);
        }

        $goods->save();

        return $goods;
    }

    /**
     * 下载图片到本地
     */
    private function downloadImage($image_url) {
        try {
            if (empty($image_url) || !filter_var($image_url, FILTER_VALIDATE_URL)) {
                return '';
            }

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $image_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

            $image_content = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($image_content === false || $http_code !== 200) {
                return '';
            }

            // 生成文件名
            $extension = pathinfo(parse_url($image_url, PHP_URL_PATH), PATHINFO_EXTENSION) ?: 'jpg';
            $filename = 'mcyfaka_' . date('Ymd') . '_' . uniqid() . '.' . $extension;
            $upload_path = '/uploads/goods/' . date('Y/m/d') . '/';
            $full_path = public_path() . $upload_path;

            // 创建目录
            if (!is_dir($full_path)) {
                mkdir($full_path, 0755, true);
            }

            // 保存文件
            $file_path = $full_path . $filename;
            if (file_put_contents($file_path, $image_content)) {
                return $upload_path . $filename;
            }

            return '';
        } catch (\Exception $e) {
            return '';
        }
    }

    /**
     * 请求异次元3.0 API
     */
    private function requestAcgApi($api_url, $endpoint, $api_key, $api_secret, $params = []) {
        $url = rtrim($api_url, '/') . $endpoint;

        // 构建签名数据
        $sign_data = array_merge([
            'api_key' => $api_key,
            'timestamp' => time(),
            'nonce' => uniqid()
        ], $params);

        // 按键名排序
        ksort($sign_data);

        // 构建签名字符串
        $sign_string = '';
        foreach ($sign_data as $key => $value) {
            if (is_array($value)) {
                $value = json_encode($value);
            }
            $sign_string .= $key . '=' . $value . '&';
        }
        $sign_string .= 'api_secret=' . $api_secret;

        // 生成签名
        $signature = md5($sign_string);

        // 构建请求数据
        $post_data = array_merge($sign_data, ['signature' => $signature]);

        // 发送请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($post_data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'McyfakaClone/1.0');

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($response === false || $http_code !== 200) {
            throw new \Exception('API请求失败');
        }

        $result = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception('API响应格式错误');
        }

        if (!isset($result['code']) || ($result['code'] !== 200 && $result['code'] !== 1)) {
            throw new \Exception($result['message'] ?? $result['msg'] ?? 'API错误');
        }

        return $result['data'] ?? $result;
    }
}
