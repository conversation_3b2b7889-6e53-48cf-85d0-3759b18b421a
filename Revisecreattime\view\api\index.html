<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <meta http-equiv="Cache-Control" content="max-age=31536000">
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>商家自定义修改注册时间</title>
    <style>
        #loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #fff;
            z-index: 9999;
        }
        .spinner {
            width: 50px;
            height: 100px;
            border: 5px solid rgba(0, 0, 0, 0.1);
            border-top-color: #3498db;
            border-radius: 50%;
            animation: spin 1s ease infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
<div id="loading">
    <div class="spinner"></div>
</div>

<div id="app" style="display: none">
    <el-card shadow="never">
        <el-form :model="form" label-width="120">
            <el-form-item label="当前商家账户">
                <span>{{userInfo.nickname || userInfo.username}}</span>
            </el-form-item>
            
            <el-form-item label="当前注册时间">
                <span>{{formatTime(userInfo.create_time) || '暂无'}}</span>
            </el-form-item>


            <el-form-item label="需要修改时间">
                <el-date-picker
                    v-model="form.create_time"
                    type="datetime"
                    placeholder="选择日期时间"
                    :disabled-date="disabledDate"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss">
                </el-date-picker>
            </el-form-item>

            <el-form-item>
                <el-button type="primary" :loading="isLoading" @click="saveData">
                    确认修改
                </el-button>
            </el-form-item>
        </el-form>
    </el-card>
</div>

<script src="/static/others/vue/vue.global.prod.js"></script>
<script src="/static/others/element-plus/index.full.min.js"></script>
<script src="/static/others/axios/axios.min.js"></script>
<script src="/static/others/element-plus/locale/zh-cn.min.js"></script>

<script type="module">
    const { ref, reactive } = Vue;
    const { ElMessage } = ElementPlus;

    const app = Vue.createApp({
        setup() {
            const isLoading = ref(false);
            const userInfo = ref({});
            const form = ref({
                create_time: ''
            });

            const disabledDate = (time) => {
                return time.getTime() > Date.now();
            };

            const formatTime = (timestamp) => {
                if (!timestamp) return '';
                const date = new Date(timestamp * 1000);
                return date.toLocaleString();
            };
            
            const fetchData = async () => {
                try {
                    const res = await axios.post("/plugin/Revisecreattime/api/fetchData");
                    console.log('接口返回数据：', res.data);
                    if (res.data?.code === 200) {
                        userInfo.value = res.data.data;
                    } else {
                        ElMessage.error(res.data?.msg || '获取数据失败');
                    }
                } catch (error) {
                    console.error('请求错误：', error);
                    ElMessage.error(error.message || '获取数据失败');
                }
            };
            
            const saveData = async () => {
                if (!form.value.create_time) {
                    return ElMessage.warning('请选择新的创建时间');
                }
                
                isLoading.value = true;
                try {
                    const res = await axios.post("/plugin/Revisecreattime/api/save", form.value);
                    console.log('保存返回数据：', res.data);
                    if (res.data?.code === 200) {
                        ElMessage.success('保存成功');
                        fetchData();
                        form.value.create_time = '';
                    } else {
                        ElMessage.error(res.data?.msg || '保存失败');
                    }
                } catch (error) {
                    console.error('保存错误：', error);
                    ElMessage.error(error.message || '保存失败');
                } finally {
                    isLoading.value = false;
                }
            };

            // 初始化时获取数据
            fetchData();

            return {
                isLoading,
                userInfo,
                form,
                disabledDate,
                formatTime,
                saveData
            };
        }
    });

    app.use(ElementPlus, {
        locale: ElementPlusLocaleZhCn
    });
    app.mount("#app");

    window.onload = () => {
        document.getElementById("loading").style.display = "none";
        document.getElementById("app").style.display = "block";
    };
</script>
</body>
</html>
