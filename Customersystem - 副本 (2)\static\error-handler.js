/**
 * 全局错误处理器
 * 用于捕获和处理浏览器扩展相关的错误
 */
(function() {
    'use strict';
    
    // 错误消息模式匹配
    const ERROR_PATTERNS = [
        /message channel closed/i,
        /listener indicated an asynchronous response/i,
        /Extension context invalidated/i,
        /Could not establish connection/i,
        /The message port closed before a response was received/i
    ];
    
    // 检查是否为需要忽略的错误
    function shouldIgnoreError(error) {
        if (!error) return false;
        
        const message = error.message || error.toString();
        return ERROR_PATTERNS.some(pattern => pattern.test(message));
    }
    
    // 全局错误处理器
    window.addEventListener('error', function(event) {
        if (shouldIgnoreError(event.error)) {
            console.warn('[ErrorHandler] 已忽略浏览器扩展相关错误:', event.error.message);
            event.preventDefault();
            event.stopPropagation();
            return false;
        }
    }, true);
    
    // 未处理的Promise拒绝处理器
    window.addEventListener('unhandledrejection', function(event) {
        if (shouldIgnoreError(event.reason)) {
            console.warn('[ErrorHandler] 已忽略未处理的Promise拒绝:', event.reason.message || event.reason);
            event.preventDefault();
            return false;
        }
    });
    
    // 控制台错误拦截（可选）
    const originalConsoleError = console.error;
    console.error = function(...args) {
        const message = args.join(' ');
        if (ERROR_PATTERNS.some(pattern => pattern.test(message))) {
            console.warn('[ErrorHandler] 已拦截控制台错误:', message);
            return;
        }
        originalConsoleError.apply(console, args);
    };
    
    // 添加调试信息
    console.log('[ErrorHandler] 全局错误处理器已加载');
    
    // 导出到全局作用域（如果需要）
    window.CustomersystemErrorHandler = {
        shouldIgnoreError: shouldIgnoreError,
        patterns: ERROR_PATTERNS
    };
})();
