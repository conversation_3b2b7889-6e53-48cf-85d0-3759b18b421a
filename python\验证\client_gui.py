import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QLabel, QLineEdit, QPushButton, 
                             QMessageBox, QFileDialog, QTabWidget, QFormLayout,
                             QGroupBox, QStatusBar, QSpinBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QIcon, QFont, QPixmap
import requests
import platform
import winreg
import subprocess
import hashlib
import json
import time
import tkinter as tk
from tkinter import messagebox

class HardwareInfo:
    @staticmethod
    def get_hwid():
        """获取设备的唯一硬件ID"""
        system_info = platform.uname()
        if platform.system() == "Windows":
            try:
                # 获取主板序列号
                reg = winreg.ConnectRegistry(None, winreg.HKEY_LOCAL_MACHINE)
                key = winreg.OpenKey(reg, r"HARDWARE\\DESCRIPTION\\System\\BIOS")
                motherboard = winreg.QueryValueEx(key, "BaseBoardProduct")[0]
                bios = winreg.QueryValueEx(key, "BIOSVendor")[0]
                # 获取CPU ID
                key = winreg.OpenKey(reg, r"HARDWARE\\DESCRIPTION\\System\\CentralProcessor\\0")
                processor_id = winreg.QueryValueEx(key, "ProcessorNameString")[0]
            except:
                motherboard = "Unknown"
                bios = "Unknown"
                processor_id = system_info.processor
            
            # 获取磁盘序列号
            try:
                result = subprocess.check_output("wmic diskdrive get serialnumber", shell=True)
                disk_serial = result.decode().strip().split('\\n')[1].strip()
            except:
                disk_serial = "Unknown"
        else:
            # 针对Linux和MacOS的硬件识别，这里简化处理
            motherboard = "Unknown"
            bios = system_info.version
            processor_id = system_info.processor
            disk_serial = "Unknown"
            
        # 构建硬件ID
        hwid_str = f"{system_info.node}:{motherboard}:{processor_id}:{disk_serial}:{bios}"
        hwid = hashlib.sha256(hwid_str.encode()).hexdigest()
        return hwid

class LicenseAPI:
    @staticmethod
    def verify_license(server_url, license_key, software_id, hwid):
        try:
            response = requests.post(
                f"{server_url}/api/verify",
                json={
                    "license_key": license_key,
                    "software_id": software_id,
                    "hwid": hwid
                },
                timeout=10
            )
            return response
        except Exception as e:
            return None

class VerifyThread(QThread):
    result_signal = pyqtSignal(dict)
    
    def __init__(self, server_url, license_key, software_id, hwid):
        super().__init__()
        self.server_url = server_url
        self.license_key = license_key
        self.software_id = software_id
        self.hwid = hwid
        
    def run(self):
        try:
            response = LicenseAPI.verify_license(
                self.server_url, 
                self.license_key,
                self.software_id,
                self.hwid
            )
            
            if response is None:
                self.result_signal.emit({
                    "success": False,
                    "message": "连接服务器失败"
                })
                return
                
            if response.status_code == 200:
                data = response.json()
                self.result_signal.emit({
                    "success": data.get("valid", False),
                    "message": data.get("message", ""),
                    "status_code": response.status_code,
                    "raw_data": data
                })
            else:
                self.result_signal.emit({
                    "success": False,
                    "message": f"服务器错误: {response.status_code}",
                    "status_code": response.status_code
                })
        except Exception as e:
            self.result_signal.emit({
                "success": False,
                "message": f"验证过程出错: {str(e)}"
            })

class ActivationTab(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.initUI()
        
    def initUI(self):
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # 标题
        title_label = QLabel("软件许可证激活")
        title_label.setStyleSheet("font-size: 20px; font-weight: bold;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 表单区域
        form_group = QGroupBox("激活信息")
        form_layout = QFormLayout()
        
        self.license_input = QLineEdit()
        self.license_input.setPlaceholderText("请输入您的许可证密钥")
        form_layout.addRow("许可证密钥:", self.license_input)
        
        self.software_input = QLineEdit("default_software")
        form_layout.addRow("软件ID:", self.software_input)
        
        self.server_input = QLineEdit("http://localhost:5000")
        form_layout.addRow("服务器地址:", self.server_input)
        
        self.hwid_display = QLineEdit(HardwareInfo.get_hwid())
        self.hwid_display.setReadOnly(True)
        form_layout.addRow("硬件ID:", self.hwid_display)
        
        # 测试连接按钮
        server_test_layout = QHBoxLayout()
        server_test_layout.addWidget(self.server_input)
        test_conn_btn = QPushButton("测试连接")
        test_conn_btn.clicked.connect(self.test_server_connection)
        server_test_layout.addWidget(test_conn_btn)
        form_layout.addRow("验证服务器:", server_test_layout)
        
        form_group.setLayout(form_layout)
        layout.addWidget(form_group)
        
        # 激活按钮
        self.activate_btn = QPushButton("激活")
        self.activate_btn.setStyleSheet("font-size: 16px; padding: 8px; background-color: #4CAF50; color: white;")
        self.activate_btn.clicked.connect(self.activate_license)
        layout.addWidget(self.activate_btn)
        
        # 状态区域
        status_group = QGroupBox("激活状态")
        status_layout = QVBoxLayout()
        
        self.status_label = QLabel("等待激活...")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("padding: 10px;")
        status_layout.addWidget(self.status_label)
        
        self.expiry_label = QLabel("")
        self.expiry_label.setAlignment(Qt.AlignCenter)
        status_layout.addWidget(self.expiry_label)
        
        status_group.setLayout(status_layout)
        layout.addWidget(status_group)
        
        # 添加一些空间
        layout.addStretch()
    
    def activate_license(self):
        license_key = self.license_input.text().strip()
        software_id = self.software_input.text().strip()
        server_url = self.server_input.text().strip()
        hwid = self.hwid_display.text()
        
        if not license_key:
            QMessageBox.warning(self, "错误", "请输入许可证密钥")
            return
        
        # 保存许可证密钥到文件
        try:
            with open('license.dat', 'w') as f:
                f.write(license_key)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存许可证密钥失败: {str(e)}")
            return
        
        # 显示正在验证...
        self.status_label.setText("正在验证许可证...")
        self.status_label.setStyleSheet("padding: 10px; color: blue;")
        
        # 启动验证线程
        self.verify_thread = VerifyThread(server_url, license_key, software_id, hwid)
        self.verify_thread.result_signal.connect(self.handle_verification_result)
        self.verify_thread.start()
    
    def handle_verification_result(self, result):
        if result["success"]:
            # 保存验证结果到本地缓存
            try:
                with open("last_verification.json", "w") as f:
                    json.dump({
                        "timestamp": time.time(),
                        "success": True,
                        "license_key": self.license_input.text().strip(),
                        "software_id": self.software_input.text().strip()
                    }, f)
            except:
                pass
            
            self.status_label.setText(f"激活成功: {result['message']}")
            self.status_label.setStyleSheet("padding: 10px; color: green; font-weight: bold;")
            
            if "raw_data" in result and "valid_until" in result["raw_data"]:
                self.expiry_label.setText(f"有效期至: {result['raw_data']['valid_until']}")
            
            # 根据消息内容确定是首次激活还是常规验证
            if "首次激活" in result['message'] or "绑定" in result['message']:
                QMessageBox.information(self, "成功", 
                    f"软件已成功激活并绑定到当前设备！\n\n"
                    f"消息: {result['message']}\n\n"
                    f"硬件ID: {self.hwid_display.text()}\n\n"
                    f"此许可证现已与当前设备绑定，不可在其他设备上使用。如需迁移请联系管理员解绑。")
            else:
                QMessageBox.information(self, "成功", "软件已成功激活！")
        else:
            self.status_label.setText(f"激活失败: {result['message']}")
            self.status_label.setStyleSheet("padding: 10px; color: red; font-weight: bold;")
            
            # 为不同的错误提供更具体的提示
            if "硬件ID不匹配" in result['message']:
                QMessageBox.warning(self, "失败", 
                    f"激活失败: {result['message']}\n\n"
                    f"此许可证已经绑定到另一台设备。\n"
                    f"如需在此设备上使用，请联系管理员解绑当前许可证。")
            else:
                QMessageBox.warning(self, "失败", f"激活失败: {result['message']}")

    def test_server_connection(self):
        """测试与验证服务器的连接"""
        server_url = self.server_input.text().strip()
        if not server_url:
            QMessageBox.warning(self, "错误", "请输入验证服务器地址")
            return
            
        try:
            QMessageBox.information(self, "测试中", "正在测试与验证服务器的连接，请稍等...")
            
            response = requests.get(
                f"{server_url}/",
                timeout=5
            )
            
            if response.status_code == 200:
                QMessageBox.information(self, "连接成功", 
                    f"服务器连接成功!\n\n"
                    f"服务器响应: {response.status_code}")
            else:
                QMessageBox.warning(self, "连接异常", 
                    f"服务器返回了非预期状态码: {response.status_code}\n\n"
                    f"响应内容: {response.text[:100]}")
        except Exception as e:
            QMessageBox.critical(self, "连接失败", 
                f"连接验证服务器失败: {str(e)}\n\n"
                f"请确认服务器地址正确且服务器正在运行。")

class ProtectionTab(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.exe_path = ""
        self.initUI()
        
    def initUI(self):
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # 标题
        title_label = QLabel("EXE保护工具")
        title_label.setStyleSheet("font-size: 20px; font-weight: bold;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 表单区域
        form_group = QGroupBox("EXE保护设置")
        form_layout = QFormLayout()
        
        # EXE路径选择
        exe_layout = QHBoxLayout()
        self.exe_input = QLineEdit()
        self.exe_input.setReadOnly(True)
        self.exe_input.setPlaceholderText("选择需要保护的EXE文件")
        browse_btn = QPushButton("浏览...")
        browse_btn.clicked.connect(self.browse_exe)
        exe_layout.addWidget(self.exe_input)
        exe_layout.addWidget(browse_btn)
        form_layout.addRow("EXE文件:", exe_layout)
        
        # 服务器和软件ID
        server_test_layout = QHBoxLayout()
        self.server_input = QLineEdit("http://localhost:5000")
        server_test_layout.addWidget(self.server_input)
        test_conn_btn = QPushButton("测试连接")
        test_conn_btn.clicked.connect(self.test_server_connection)
        server_test_layout.addWidget(test_conn_btn)
        form_layout.addRow("验证服务器:", server_test_layout)
        
        self.software_input = QLineEdit("default_software")
        form_layout.addRow("软件ID:", self.software_input)
        
        form_group.setLayout(form_layout)
        layout.addWidget(form_group)
        
        # 保护设置
        settings_group = QGroupBox("保护设置")
        settings_layout = QFormLayout()
        
        self.verify_interval = QSpinBox()
        self.verify_interval.setRange(5, 1440)
        self.verify_interval.setValue(60)
        self.verify_interval.setSuffix(" 分钟")
        settings_layout.addRow("验证间隔:", self.verify_interval)
        
        self.offline_grace = QSpinBox()
        self.offline_grace.setRange(1, 30)
        self.offline_grace.setValue(7)
        self.offline_grace.setSuffix(" 天")
        settings_layout.addRow("离线宽限期:", self.offline_grace)
        
        settings_group.setLayout(settings_layout)
        layout.addWidget(settings_group)
        
        # 生成按钮
        self.protect_btn = QPushButton("创建保护版本")
        self.protect_btn.setStyleSheet("font-size: 16px; padding: 8px; background-color: #2196F3; color: white;")
        self.protect_btn.clicked.connect(self.create_protected_version)
        layout.addWidget(self.protect_btn)
        
        # 状态
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.status_label)
        
        # 添加一些空间
        layout.addStretch()
    
    def browse_exe(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "选择EXE文件", "", "EXE文件 (*.exe)")
        if file_path:
            self.exe_path = file_path
            self.exe_input.setText(file_path)
    
    def create_protected_version(self):
        if not self.exe_path:
            QMessageBox.warning(self, "错误", "请先选择一个EXE文件")
            return
        
        software_id = self.software_input.text().strip()
        server_url = self.server_input.text().strip()
        
        try:
            # 导入client模块中的包装函数
            from client import wrap_exe_with_verification
            
            # 使用client中的包装函数创建保护启动器
            # 统一使用正斜杠路径，避免Windows路径问题
            clean_exe_path = self.exe_path.replace('\\', '/')
            output_path = wrap_exe_with_verification(
                exe_path=clean_exe_path,
                server_url=server_url,
                software_id=software_id
            )
            
            # 确保显示的路径也是正确格式
            self.status_label.setText(f"已创建保护启动器: {output_path}")
            self.status_label.setStyleSheet("color: green;")
            
            # 处理批处理文件路径，使用正确的斜杠格式
            batch_path = f"{os.path.splitext(output_path)[0]}_打包.bat"
            
            QMessageBox.information(self, "成功", 
                f"已创建保护启动器: {output_path}\n\n"
                f"同时创建了打包脚本: {batch_path}\n"
                f"双击该脚本即可自动打包成EXE文件。")
        
        except Exception as e:
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "错误", f"创建保护启动器失败: {str(e)}")

    def test_server_connection(self):
        """测试与验证服务器的连接"""
        server_url = self.server_input.text().strip()
        if not server_url:
            QMessageBox.warning(self, "错误", "请输入验证服务器地址")
            return
            
        try:
            QMessageBox.information(self, "测试中", "正在测试与验证服务器的连接，请稍等...")
            
            response = requests.get(
                f"{server_url}/",
                timeout=5
            )
            
            if response.status_code == 200:
                QMessageBox.information(self, "连接成功", 
                    f"服务器连接成功!\n\n"
                    f"服务器响应: {response.status_code}")
            else:
                QMessageBox.warning(self, "连接异常", 
                    f"服务器返回了非预期状态码: {response.status_code}\n\n"
                    f"响应内容: {response.text[:100]}")
        except Exception as e:
            QMessageBox.critical(self, "连接失败", 
                f"连接验证服务器失败: {str(e)}\n\n"
                f"请确认服务器地址正确且服务器正在运行。")

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.initUI()
        
    def initUI(self):
        self.setWindowTitle('EXE验证保护系统')
        self.setGeometry(100, 100, 800, 600)
        
        # 中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 选项卡
        tab_widget = QTabWidget()
        
        # 添加激活标签页
        activation_tab = ActivationTab()
        tab_widget.addTab(activation_tab, "激活软件")
        
        # 添加保护工具标签页
        protection_tab = ProtectionTab()
        tab_widget.addTab(protection_tab, "EXE保护工具")
        
        main_layout.addWidget(tab_widget)
        
        # 状态栏
        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)
        self.statusBar.showMessage('就绪')

if __name__ == '__main__':
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec_()) 