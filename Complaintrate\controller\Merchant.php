<?php
namespace plugin\Complaintrate\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;

class Merchant extends BasePlugin
{
    protected $scene = ['user'];
    protected $noNeedLogin = ['index', 'getMerchantData', 'channelGroups', 'upgradeLevel'];

    public function index()
    {
        return View::fetch('merchant/index');
    }

    // 获取商户数据
    public function getMerchantData()
    {
        try {
            if (!$this->user) {
                return json(['code' => 403, 'msg' => '请先登录']);
            }

            $merchantId = $this->user->id;
            $cacheKey = "complaintrate_merchant_{$merchantId}";
            
            // 从配置获取最新计算时间和计算模式
            $lastCalcTime = intval(plugconf('Complaintrate.last_update_time') ?? 0);  // 确保是整数
            $calcMode = plugconf('Complaintrate.calc_mode') ?? 'realtime';
            
            // 获取缓存的数据
            $cachedData = cache($cacheKey);
            if ($cachedData && isset($cachedData['calc_time']) && $cachedData['calc_time'] >= $lastCalcTime) {
                return json(['code' => 200, 'msg' => '获取成功', 'data' => $cachedData]);
            }

            // 获取投诉和订单数据
            $complaintData = Db::name('user')
                ->alias('u')
                ->leftJoin('complaint c', 'u.id = c.user_id')
                ->leftJoin('order o', 'u.id = o.user_id AND o.status = 1')
                ->where('u.id', $merchantId)
                ->field([
                    'COUNT(DISTINCT CASE WHEN c.id IS NOT NULL THEN c.id END) as complaint_count',
                    'COUNT(DISTINCT CASE WHEN o.id IS NOT NULL THEN o.id END) as total_orders',
                    'MAX(c.create_time) as latest_complaint_time'
                ])
                ->find();

            // 获取当前等级信息
            $config = $this->getParamsConfig();
            $merchantData = $config['merchant_data'][$merchantId] ?? [];
            $currentGroup = $merchantData['group_name'] ?? (
                Db::name('channel_group')
                    ->where('id', $merchantData['group_id'] ?? 1)
                    ->value('name') ?? '默认等级'
            );
            $expireTime = intval($merchantData['expire_time'] ?? 0);

            // 计算投诉率
            $complaintRate = 0;
            if ($complaintData['total_orders'] > 0) {
                $complaintRate = round(($complaintData['complaint_count'] / $complaintData['total_orders']) * 100, 2);
            }

            // 确保所有时间戳都是整数
            $latestComplaintTime = intval($complaintData['latest_complaint_time'] ?? 0);
            $currentTime = time();

            $result = [
                'complaint_rate' => $complaintRate,
                'complaint_count' => intval($complaintData['complaint_count']),
                'total_orders' => intval($complaintData['total_orders']),
                'latest_complaint_time' => $latestComplaintTime ? date('Y-m-d H:i:s', $latestComplaintTime) : '',
                'current_group' => $currentGroup,
                'expire_time' => $expireTime,
                'calc_start_time' => date('Y-m-d H:i:s', $currentTime - 30*86400),
                'calc_end_time' => date('Y-m-d H:i:s', $currentTime),
                'last_update_time' => date('Y-m-d H:i:s', $lastCalcTime),
                'calc_time' => $currentTime,
                'calc_mode' => $calcMode
            ];

            // 缓存数据
            cache($cacheKey, $result, 3600);

            return json(['code' => 200, 'msg' => '获取成功', 'data' => $result]);
        } catch (\Exception $e) {
            \think\facade\Log::error('获取商户数据失败: ' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取数据失败: ' . $e->getMessage()]);
        }
    }

    // 获取渠道组列表
    public function channelGroups()
    {
        try {
            // 获取渠道组基本信息
            $groups = Db::name('channel_group')
                ->whereNull('delete_time')
                ->field(['id', 'name'])
                ->select()
                ->toArray();

            // 获取当前用户配置
            $config = $this->getParamsConfig();
            $merchantData = $config['merchant_data'][$this->user->id] ?? [];
            $currentGroupId = $merchantData['group_id'] ?? 1;
            $expireTime = $merchantData['expire_time'] ?? 0;

            // 获取价格配置
            $prices = json_decode(plugconf('Complaintrate.prices'), true) ?? [];
            $quarterlyPrices = json_decode(plugconf('Complaintrate.quarterly_prices'), true) ?? [];
            $yearlyPrices = json_decode(plugconf('Complaintrate.yearly_prices'), true) ?? [];
            $complaintRates = json_decode(plugconf('Complaintrate.complaint_rates'), true) ?? [];
            $complaintCounts = json_decode(plugconf('Complaintrate.complaint_counts'), true) ?? [];

            // 获取用户余额
            $userMoney = Db::name('user')
                ->where('id', $this->user->id)
                ->value('operate_money');

            foreach ($groups as &$group) {
                $group['complaint_rate'] = $complaintRates[$group['id']] ?? 0;
                $group['complaint_count'] = $complaintCounts[$group['id']] ?? 0;
                $group['price'] = $prices[$group['id']] ?? 30;  // 修改默认价格
                $group['quarterly_price'] = $quarterlyPrices[$group['id']] ?? 40;  // 修改默认价格
                $group['yearly_price'] = $yearlyPrices[$group['id']] ?? 50;  // 修改默认价格
                $group['can_upgrade'] = intval(plugconf("Complaintrate.can_upgrade.{$group['id']}") ?? 1);
                $group['disabled_message'] = plugconf("Complaintrate.disabled_message.{$group['id']}") ?? '该等级暂不开放';
                $group['is_current'] = ($group['id'] == $currentGroupId);
                $group['expire_time'] = $group['is_current'] ? $expireTime : 0;
                $group['selected_cycle'] = 'monthly';
                
                // 检查是否可以支付
                $minPrice = min(
                    $group['price'],
                    $group['quarterly_price'] > 0 ? $group['quarterly_price'] : PHP_FLOAT_MAX,
                    $group['yearly_price'] > 0 ? $group['yearly_price'] : PHP_FLOAT_MAX
                );
                $group['can_pay'] = $userMoney >= $minPrice;
            }

            return json(['code' => 200, 'msg' => '获取成功', 'data' => $groups]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    // 升级等级
    public function upgradeLevel()
    {
        if (!$this->user) {
            return json(['code' => 403, 'msg' => '请先登录']);
        }

        try {
            $groupId = input('group_id/d', 0);
            $cycle = input('cycle/s', 'monthly');

            if (!$groupId) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }

            // 获取当前用户配置
            $config = $this->getParamsConfig();
            $merchantData = $config['merchant_data'][$this->user->id] ?? [];
            $currentGroupId = $merchantData['group_id'] ?? 1;
            
            // 判断是否为续费
            $isRenew = ($currentGroupId == $groupId && !empty($merchantData['expire_time']));

            // 获取价格配置
            $prices = json_decode(plugconf('Complaintrate.prices'), true) ?? [];
            $quarterlyPrices = json_decode(plugconf('Complaintrate.quarterly_prices'), true) ?? [];
            $yearlyPrices = json_decode(plugconf('Complaintrate.yearly_prices'), true) ?? [];

            // 根据周期获取价格
            switch ($cycle) {
                case 'yearly':
                    $price = $yearlyPrices[$groupId] ?? 2999;
                    break;
                case 'quarterly':
                    $price = $quarterlyPrices[$groupId] ?? 799;
                    break;
                default:
                    $price = $prices[$groupId] ?? 299;
            }

            // 检查余额
            $userMoney = Db::name('user')
                ->where('id', $this->user->id)
                ->value('operate_money');

            if ($userMoney < $price) {
                return json(['code' => 400, 'msg' => '余额不足']);
            }

            Db::startTrans();
            try {
                // 扣款
                $result = Db::name('user')
                    ->where('id', $this->user->id)
                    ->dec('operate_money', $price)
                    ->update();

                if (!$result) {
                    throw new \Exception('扣款失败');
                }

                // 记录资金变动
                Db::name('user_money_log')->insert([
                    'user_id' => $this->user->id,
                    'change' => -$price,
                    'reason' => ($isRenew ? '续费' : '开通') . $this->getCycleName($cycle) . '投诉率等级',
                    'create_time' => time(),
                    'source' => 'Operate'
                ]);

                // 计算到期时间
                $currentTime = time();
                $expireTime = $currentTime;

                // 如果是续费且未过期，在原到期时间基础上增加
                if ($isRenew && isset($merchantData['expire_time']) && $merchantData['expire_time'] > $currentTime) {
                    $expireTime = $merchantData['expire_time'];
                }

                // 增加时间
                switch ($cycle) {
                    case 'yearly':
                        $expireTime += 365 * 24 * 3600;
                        break;
                    case 'quarterly':
                        $expireTime += 90 * 24 * 3600;
                        break;
                    default:
                        $expireTime += 30 * 24 * 3600;
                }

                // 更新商户数据
                $group = Db::name('channel_group')->where('id', $groupId)->find();
                if (!$group) {
                    throw new \Exception('渠道组不存在');
                }

                // 更新用户表中的 channel_group_id
                $updateResult = Db::name('user')
                    ->where('id', $this->user->id)
                    ->update([
                        'channel_group_id' => $groupId,
                        'update_time' => time()
                    ]);

                if (!$updateResult) {
                    throw new \Exception('更新用户等级失败');
                }

                // 更新配置文件中的商户数据
                $config['merchant_data'][$this->user->id] = [
                    'group_id' => $groupId,
                    'group_name' => $group['name'],
                    'expire_time' => $expireTime
                ];

                // 保存配置
                file_put_contents(
                    app()->getRootPath() . 'plugin/Complaintrate/params.php',
                    "<?php\n\nreturn " . var_export($config, true) . ";\n"
                );

                Db::commit();
                return json(['code' => 200, 'msg' => ($isRenew ? '续费' : '开通') . '成功']);
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '操作失败: ' . $e->getMessage()]);
        }
    }

    private function getCycleName($cycle)
    {
        $cycleNames = [
            'monthly' => '包月',
            'quarterly' => '包季',
            'yearly' => '包年'
        ];
        return $cycleNames[$cycle] ?? '包月';
    }

    private function getParamsConfig()
    {
        try {
            $configFile = app()->getRootPath() . 'plugin/Complaintrate/params.php';
            if (!file_exists($configFile)) {
                $defaultConfig = [
                    'merchant_data' => [],
                    'last_update' => time(),
                    'default_group_id' => 1
                ];
                file_put_contents($configFile, "<?php\n\nreturn " . var_export($defaultConfig, true) . ";\n");
                return $defaultConfig;
            }
            return include $configFile;
        } catch (\Exception $e) {
            \think\facade\Log::error('读取配置文件失败: ' . $e->getMessage());
            return [
                'merchant_data' => [],
                'last_update' => time(),
                'default_group_id' => 1
            ];
        }
    }
}