
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>卡密验证系统 - 登录</title>
        <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        <style>
            body { 
                background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
            }
            .login-card { 
                background-color: rgba(255, 255, 255, 0.9);
                border-radius: 10px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                backdrop-filter: blur(10px);
            }
            .card-header { 
                background: linear-gradient(to right, #4a00e0, #8e2de2);
                color: white;
                border-radius: 10px 10px 0 0 !important;
                padding: 20px;
            }
            .btn-primary {
                background: linear-gradient(to right, #4a00e0, #8e2de2);
                border: none;
                transition: all 0.3s;
            }
            .btn-primary:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            }
            .form-control {
                border-radius: 7px;
                padding: 12px 15px;
            }
            .form-control:focus {
                box-shadow: 0 0 0 3px rgba(78, 0, 224, 0.2);
                border-color: #4a00e0;
            }
            .login-icon {
                font-size: 3em;
                color: #4a00e0;
                margin-bottom: 20px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-5">
                    <div class="text-center mb-4">
                        <i class="fas fa-key login-icon"></i>
                        <h2 class="text-white fw-bold">卡密验证系统</h2>
                        <p class="text-white">管理员登录</p>
                    </div>
                    <div class="card login-card">
                        <div class="card-header">
                            <h3 class="text-center mb-0">管理员登录</h3>
                        </div>
                        <div class="card-body p-4">
                            {% if get_flashed_messages() %}
                            <div class="alert alert-danger">
                                {% for message in get_flashed_messages() %}
                                {{ message }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            <form method="post" action="/login">
                                <div class="mb-3">
                                    <label class="form-label">用户名</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                                        <input type="text" class="form-control" name="username" required>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">密码</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                        <input type="password" class="form-control" name="password" required>
                                    </div>
                                </div>
                                <div class="d-grid mt-4">
                                    <button type="submit" class="btn btn-primary btn-lg">登录</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    