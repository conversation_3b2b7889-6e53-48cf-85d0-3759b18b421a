<?php
 
return [
    "version" => "1.0.3",
    "min_system_version" => "2.11.0",
    "category_name" => "功能插件",
    "logo" => "data:image/png;base64,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",
    "name" => "敏感词检测",
    "description" => "敏感词检测",
    "form_fields" => [
        [
            "id" => "status",
            "name" => "开启状态",
            "required" => true,
            "type" => "radio",
            "data" => [
                [
                    "name" => "关闭",
                    "value" => 0
                ],
                [
                    "name" => "开启",
                    "value" => 1
                ]
            ]
        ],
        [
            "id" => "wordfilter_danger",
            "name" => "敏感词过滤,使用“|”分隔",
            "placeholder" => "请输入规则",
            "type" => "input",
            "textarea" => true
        ]
    ],
    "hook" => [
        'MerchantGoodsUpdateBefore' => 'plugin\CheckWordfilter\Hook',
    ],
];
