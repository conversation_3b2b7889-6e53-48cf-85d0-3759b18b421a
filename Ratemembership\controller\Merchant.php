<?php
namespace plugin\Ratemembership\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;

class Merchant extends BasePlugin
{
    protected $scene = ['user'];

    // 这些方法不需要登录检测
    protected $noNeedLogin = ['index', 'getMerchantData', 'channelGroups', 'upgradeLevel'];

    // 商户端首页
    public function index()
    {
        return View::fetch('merchant/index');
    }

    // 商家端获取渠道组列表
    public function channelGroups()
    {
        try {
            // 获取渠道组基本信息
            $groups = Db::name('channel_group')
                ->alias('g')
                ->whereNull('g.delete_time')
                ->field([
                    'g.id',
                    'g.name'
                ])
                ->select()
                ->toArray();

            // 获取插件配置中的等级名称
            $levelNames = plugconf('Ratemembership.level_names') ? json_decode(plugconf('Ratemembership.level_names'), true) : [];
            
            // 获取隐藏等级配置
            $hiddenGroups = plugconf('Ratemembership.is_hidden') ? json_decode(plugconf('Ratemembership.is_hidden'), true) : [];
            
            // 过滤掉隐藏等级，但保留当前用户使用的等级
            $filteredGroups = [];
            foreach ($groups as $group) {
                // 检查是否是隐藏等级
                $isHidden = isset($hiddenGroups[$group['id']]) && $hiddenGroups[$group['id']] == '1';
                
                // 如果是当前用户的等级，或者不是隐藏等级，则添加到显示列表
                if ($group['id'] == $this->user->channel_group_id || !$isHidden) {
                    $filteredGroups[] = $group;
                }
            }
            $groups = $filteredGroups;

            // 获取平台费率和商户费率
            foreach ($groups as &$group) {
                // 使用显示名称替换基础名称，如果没有显示名称则使用基础名称
                $group['name'] = !empty($levelNames[$group['id']]) ? $levelNames[$group['id']] : $group['name'];

                // 获取平台基础费率
                $platformRate = Db::name('channel_group_rule')
                    ->where([
                        'group_id' => $group['id'],
                        'channel_id' => 1  // 平台渠道
                    ])
                    ->value('rate');

                // 获取直清合规渠道费率
                $directRate = Db::name('channel_group_rule')
                    ->where([
                        'group_id' => $group['id'],
                        'channel_id' => 10  // 直清合规渠道
                    ])
                    ->value('rate');

                // 获取商户自定义渠道费率
                $merchantRate = Db::name('channel_group_rule')
                    ->where([
                        'group_id' => $group['id'],
                        'channel_id' => 14  // 商户自定义渠道
                    ])
                    ->value('rate');

                $group['platform_rate'] = floatval($platformRate ?? 6.00);
                $group['direct_rate'] = floatval($directRate ?? 6.00);
                $group['merchant_rate'] = floatval($merchantRate ?? 6.00);
            }

            // 获取流水配置
            $turnovers = plugconf('Ratemembership.turnovers') ? json_decode(plugconf('Ratemembership.turnovers'), true) : [];
            // 获取价格配置
            $prices = plugconf('Ratemembership.prices') ? json_decode(plugconf('Ratemembership.prices'), true) : [];
            // 获取季费和年费配置
            $quarterlyPrices = plugconf('Ratemembership.quarterly_prices') ? json_decode(plugconf('Ratemembership.quarterly_prices'), true) : [];
            $yearlyPrices = plugconf('Ratemembership.yearly_prices') ? json_decode(plugconf('Ratemembership.yearly_prices'), true) : [];
            
            // 获取默认等级ID（最小ID）
            $defaultGroupId = min(array_column($groups, 'id'));
            
            foreach ($groups as &$group) {
                $group['turnover'] = $turnovers[$group['id']] ?? 0;
                $group['price'] = $prices[$group['id']] ?? ($group['id'] == $defaultGroupId ? 0 : 299.00);  // 默认等级价格为0，其他299元
                $group['quarterly_price'] = $quarterlyPrices[$group['id']] ?? 0;
                $group['yearly_price'] = $yearlyPrices[$group['id']] ?? 0;
                
                // 修改这部分逻辑：不管是否是当前等级，只要设置了不开放就不允许操作
                $group['can_upgrade'] = $group['id'] == $defaultGroupId ? 0 : 
                    (intval(plugconf("Ratemembership.can_upgrade.{$group['id']}") ?? 1));
                
                // 默认等级显示"默认等级"，其他等级显示配置的消息
                $group['disabled_message'] = $group['id'] == $defaultGroupId ? '默认等级' : 
                    (plugconf("Ratemembership.disabled_message.{$group['id']}") ?? '该等级暂不开放');

                // 添加额外字段
                $group['selected_cycle'] = 'monthly';  // 默认选择包月
                $group['is_current'] = $group['id'] == $this->user->channel_group_id;
                
                // 修改支付检查逻辑：需要同时满足可升级和余额充足
                if ($group['id'] == $defaultGroupId) {
                    $group['can_pay'] = true;
                } else {
                    $userMoney = Db::name('user')
                        ->where('id', $this->user->id)
                        ->value('operate_money');
                    // 只有当等级开放时才检查余额
                    $group['can_pay'] = $group['can_upgrade'] && (floatval($userMoney) >= floatval($group['price']));
                }

                // 获取到期时间（默认等级永久有效）
                $expireTimes = plugconf('Ratemembership.expire_times') ? json_decode(plugconf('Ratemembership.expire_times'), true) : [];
                $group['expire_time'] = $group['id'] == $defaultGroupId ? 0 : 
                    ($group['is_current'] ? ($expireTimes[$this->user->id] ?? 0) : 0);
            }

            // 重新排序，将默认等级放在第一位
            usort($groups, function($a, $b) use ($defaultGroupId) {
                if ($a['id'] == $defaultGroupId) return -1;
                if ($b['id'] == $defaultGroupId) return 1;
                return $a['id'] - $b['id'];
            });

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $groups,
                'default_group_id' => $defaultGroupId
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    // 获取商家流水信息
    public function getMerchantData()
    {
        try {
            // 获取本月和上月的时间范围
            $thisMonthStart = date('Y-m-01');
            $thisMonthEnd = date('Y-m-t');
            $lastMonthStart = date('Y-m-01', strtotime('-1 month'));
            $lastMonthEnd = date('Y-m-t', strtotime('-1 month'));

            // 计算本月流水
            $thisMonthAmount = Db::name('user_analysis')
                ->where([
                    ['user_id', '=', $this->user->id],
                    ['date', '>=', $thisMonthStart],
                    ['date', '<=', $thisMonthEnd]
                ])
                ->sum('total_amount');

            // 计算上月流水
            $lastMonthAmount = Db::name('user_analysis')
                ->where([
                    ['user_id', '=', $this->user->id],
                    ['date', '>=', $lastMonthStart],
                    ['date', '<=', $lastMonthEnd]
                ])
                ->sum('total_amount');

            // 获取当前等级信息
            $currentGroup = Db::name('channel_group')
                ->where('id', $this->user->channel_group_id)
                ->find();

            // 获取显示名称
            $levelNames = plugconf('Ratemembership.level_names') ? json_decode(plugconf('Ratemembership.level_names'), true) : [];
            $currentGroupName = !empty($levelNames[$this->user->channel_group_id]) ? 
                $levelNames[$this->user->channel_group_id] : 
                ($currentGroup['name'] ?? '默认等级');

            // 获取平台费率和商户费率
            $platformRate = Db::name('channel_group_rule')
                ->where([
                    'group_id' => $currentGroup['id'],
                    'channel_id' => 1  // 平台渠道
                ])
                ->value('rate');

            $merchantRate = Db::name('channel_group_rule')
                ->where([
                    'group_id' => $currentGroup['id'],
                    'channel_id' => 14  // 商户自定义渠道
                ])
                ->value('rate');

            // 计算下一等级所需流水
            $turnovers = plugconf('Ratemembership.turnovers') ? json_decode(plugconf('Ratemembership.turnovers'), true) : [];
            $currentTurnover = $turnovers[$this->user->channel_group_id] ?? 0;
            $nextLevelAmount = 0;
            $isHighestLevel = true;
            
            // 检查是否有可升级的等级
            $hasUpgradableLevel = false;
            foreach ($turnovers as $groupId => $turnover) {
                if ($groupId != $this->user->channel_group_id) {
                    $canUpgrade = intval(plugconf("Ratemembership.can_upgrade.{$groupId}") ?? 1);
                    if ($canUpgrade) {
                        $hasUpgradableLevel = true;
                        if ($turnover > $currentTurnover) {
                            $nextLevelAmount = $turnover - $thisMonthAmount;
                            $isHighestLevel = false;
                            break;
                        }
                    }
                }
            }

            // 如果没有可升级的等级，设置特殊消息
            if (!$hasUpgradableLevel) {
                $nextLevelAmount = -5000;  // 使用特殊值表示暂未开放升级
                $isHighestLevel = false;
            }

            // 获取计算模式
            $calcMode = plugconf('Ratemembership.calc_mode') ?? 'realtime';
            
            // 获取更新周期
            $updateCycle = plugconf('Ratemembership.update_cycle') ?? 'monthly';
            
            // 获取下次更新时间提示
            $nextUpdateTime = '';
            if ($calcMode === 'period') {
                switch ($updateCycle) {
                    case 'daily':
                        $nextUpdateTime = date('Y-m-d', strtotime('+1 day')) . ' 00:00:00';
                        break;
                    case 'monthly':
                        $nextUpdateTime = date('Y-m-01', strtotime('+1 month'));
                        break;
                    case 'quarterly':
                        $currentMonth = date('n');
                        $currentQuarter = ceil($currentMonth / 3);
                        $nextQuarter = $currentQuarter + 1;
                        if ($nextQuarter > 4) {
                            $nextQuarter = 1;
                            $year = date('Y') + 1;
                        } else {
                            $year = date('Y');
                        }
                        $nextMonth = ($nextQuarter - 1) * 3 + 1;
                        $nextUpdateTime = date("$year-" . str_pad($nextMonth, 2, '0', STR_PAD_LEFT) . '-01');
                        break;
                    case 'yearly':
                        $nextUpdateTime = date('Y-01-01', strtotime('+1 year'));
                        break;
                }
            }

            return json([
                'code' => 200,
                'data' => [
                    'this_month_amount' => floatval($thisMonthAmount),
                    'last_month_amount' => floatval($lastMonthAmount),
                    'current_group' => $currentGroupName,
                    'current_group_id' => $this->user->channel_group_id,
                    'platform_rate' => $platformRate,
                    'merchant_rate' => $merchantRate,
                    'next_level_amount' => $nextLevelAmount,
                    'calc_start_time' => $thisMonthStart,
                    'calc_end_time' => $thisMonthEnd,
                    'last_month_start' => $lastMonthStart,
                    'last_month_end' => $lastMonthEnd,
                    'last_update_time' => date('Y-m-d H:i:s'),
                    'is_highest_level' => $isHighestLevel,
                    'turnover_hash' => md5($thisMonthAmount . $lastMonthAmount),
                    'calc_mode' => $calcMode,  // 添加计算模式
                    'next_update_time' => $nextUpdateTime  // 添加下次更新时间
                ]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '获取失败: ' . $e->getMessage()]);
        }
    }

    // 开通等级
    public function upgradeLevel()
    {
        if (!$this->user) {
            return json(['code' => 403, 'msg' => '请先登录']);
        }

        try {
            $groupId = input('group_id/d', 0);
            $isRenew = input('is_renew/b', false);
            $cycle = input('cycle/s', 'monthly');

            // 获取默认等级ID
            $defaultGroupId = Db::name('channel_group')
                ->whereNull('delete_time')
                ->min('id');

            // 如果是默认等级，直接更新用户等级
            if ($groupId == $defaultGroupId) {
                Db::startTrans();
                try {
                    // 更新用户等级
                    Db::name('user')
                        ->where('id', $this->user->id)
                        ->update([
                            'channel_group_id' => $groupId,
                            'update_time' => time()
                        ]);

                    // 清除之前的到期时间
                    $expireTimes = plugconf('Ratemembership.expire_times') ? 
                        json_decode(plugconf('Ratemembership.expire_times'), true) : [];
                    unset($expireTimes[$this->user->id]);
                    plugconf('Ratemembership.expire_times', json_encode($expireTimes));

                    Db::commit();
                    return json(['code' => 200, 'msg' => '已切换至默认等级']);
                } catch (\Exception $e) {
                    Db::rollback();
                    throw $e;
                }
            }

            // 获取对应周期的价格配置
            $prices = [];
            switch ($cycle) {
                case 'yearly':
                    $prices = plugconf('Ratemembership.yearly_prices') ? json_decode(plugconf('Ratemembership.yearly_prices'), true) : [];
                    break;
                case 'quarterly':
                    $prices = plugconf('Ratemembership.quarterly_prices') ? json_decode(plugconf('Ratemembership.quarterly_prices'), true) : [];
                    break;
                default:
                    $prices = plugconf('Ratemembership.prices') ? json_decode(plugconf('Ratemembership.prices'), true) : [];
                    break;
            }

            // 获取对应等级的价格
            $price = $prices[$groupId] ?? 0;

            // 验证价格
            if ($price <= 0) {
                throw new \Exception('该等级' . $this->getCycleName($cycle) . '价格未设置');
            }

            // 检查余额是否足够
            $userMoney = Db::name('user')
                ->where('id', $this->user->id)
                ->value('operate_money');
            
            if (floatval($userMoney) < floatval($price)) {
                throw new \Exception('余额不足');
            }

            Db::startTrans();
            try {
                // 扣除用户余额
                $result = Db::name('user')
                    ->where('id', $this->user->id)
                    ->dec('operate_money', $price)
                    ->update();
                
                if (!$result) {
                    throw new \Exception('扣款失败');
                }

                // 记录资金变动到用户资金日志表
                Db::name('user_money_log')->insert([
                    'user_id' => $this->user->id,
                    'change' => -$price,
                    'reason' => ($isRenew ? '续费' : '开通') . $this->getCycleName($cycle) . '会员等级',
                    'create_time' => time(),
                    'source' => 'Operate'
                ]);

                // 计算到期时间
                $currentTime = time();
                $expireTime = $currentTime;
                switch ($cycle) {
                    case 'yearly':
                        $expireTime = $currentTime + (365 * 24 * 3600);
                        break;
                    case 'quarterly':
                        $expireTime = $currentTime + (90 * 24 * 3600);
                        break;
                    default:
                        $expireTime = $currentTime + (30 * 24 * 3600);
                }

                // 如果是续费，需要在原到期时间基础上增加时间
                if ($isRenew) {
                    $expireTimes = plugconf('Ratemembership.expire_times') ? 
                        json_decode(plugconf('Ratemembership.expire_times'), true) : [];
                    $oldExpireTime = $expireTimes[$this->user->id] ?? $currentTime;
                    if ($oldExpireTime > $currentTime) {
                        $expireTime = $oldExpireTime;
                        switch ($cycle) {
                            case 'yearly':
                                $expireTime += 365 * 24 * 3600;
                                break;
                            case 'quarterly':
                                $expireTime += 90 * 24 * 3600;
                                break;
                            default:
                                $expireTime += 30 * 24 * 3600;
                        }
                    }
                }

                // 更新用户等级
                Db::name('user')
                    ->where('id', $this->user->id)
                    ->update([
                        'channel_group_id' => $groupId,
                        'update_time' => time()
                    ]);

                // 保存到期时间
                $expireTimes = plugconf('Ratemembership.expire_times') ? 
                    json_decode(plugconf('Ratemembership.expire_times'), true) : [];
                $expireTimes[$this->user->id] = $expireTime;
                plugconf('Ratemembership.expire_times', json_encode($expireTimes));

                Db::commit();
                return json(['code' => 200, 'msg' => ($isRenew ? '续费' : '开通') . '成功']);
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => ($isRenew ? '续费' : '开通') . '失败: ' . $e->getMessage()]);
        }
    }

    private function getCycleName($cycle)
    {
        $cycleNames = [
            'monthly' => '包月',
            'quarterly' => '包季',
            'yearly' => '包年'
        ];
        return $cycleNames[$cycle] ?? '包月';
    }
}
