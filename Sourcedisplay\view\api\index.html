<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>货源排行显示设置</title>
    <style>
    body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
        color: #333;
        line-height: 1.6;
        background-color: #f5f7fa;
        margin: 0;
        padding: 0;
    }
    
    #app {
        max-width: 800px;
        margin: 20px auto;
        padding: 0 15px;
    }
    
    .header {
        margin-bottom: 20px;
        text-align: center;
    }
    
    .header h1 {
        font-size: 24px;
        color: #409EFF;
    }
    
    .card-container {
        transition: all 0.3s ease;
        border-radius: 12px;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.05);
        overflow: hidden;
    }
    
    .avatar-container {
        position: relative;
        display: inline-block;
        margin-bottom: 10px;
    }

    .delete-icon {
        position: absolute;
        top: -10px;
        right: -10px;
        padding: 4px 8px;
        border-radius: 6px;
        transition: all 0.2s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    .delete-icon:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .avatar-uploader .avatar {
        width: 100px;
        height: 100px;
        display: block;
        border-radius: 10px;
        transition: all 0.3s ease;
        object-fit: contain;
        background: #f7f7f7;
    }
    
    .avatar-uploader .el-upload {
        border: 2px dashed #d9d9d9;
        border-radius: 10px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
        width: 100px;
        height: 100px;
    }
    
    .avatar-uploader .el-upload:hover {
        border-color: #409EFF;
        background-color: rgba(64, 158, 255, 0.05);
    }
    
    .avatar-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 100px;
        height: 100px;
        text-align: center;
        line-height: 100px;
    }
    
    .el-form-item {
        margin-bottom: 22px;
    }
    
    .el-card {
        border-radius: 12px !important;
        transition: all 0.3s ease;
    }
    
    .save-btn {
        width: 100%;
        font-weight: 500;
        letter-spacing: 1px;
        height: 44px;
    }
    
    .icon-title {
        display: block;
        margin-bottom: 8px;
        color: #606266;
        font-size: 14px;
    }
    
    .form-title {
        font-size: 18px;
        margin-bottom: 20px;
        color: #409EFF;
        font-weight: 500;
        display: flex;
        align-items: center;
    }
    
    .form-title::before {
        content: '';
        display: inline-block;
        width: 4px;
        height: 18px;
        background: #409EFF;
        margin-right: 8px;
        border-radius: 4px;
    }
    
    @media (max-width: 768px) {
        #app {
            padding: 0 10px;
            margin: 10px auto;
        }
        
        .el-form-item {
            margin-bottom: 15px;
        }
    }
    </style>
</head>
<body>
    <div id="app">
        <div class="header">
            <h1>货源排行显示设置</h1>
        </div>
        
        <el-card shadow="hover" class="card-container">
            <div class="form-title">基本设置</div>
            <el-form :model="form" label-width="120px">
                <el-form-item label="启用插件：">
                    <el-switch
                        v-model="form.enabled"
                        active-color="#409EFF"
                        inactive-color="#DCDFE6"
                    />
                </el-form-item>

                <div class="form-title">图标设置</div>
                <el-form-item label="排名1图标：">
                    <div class="avatar-container">
                        <el-upload
                            class="avatar-uploader"
                            action="/adminApi/Upload/file"
                            :show-file-list="false"
                            :on-success="(res) => handleUploadSuccess(res, 'rank1_icon')"
                        >
                            <img v-if="form.rank1_icon" :src="form.rank1_icon" class="avatar">
                            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                        </el-upload>
                        <el-button 
                            v-if="form.rank1_icon !== DEFAULT_ICONS.rank1_icon" 
                            type="danger" 
                            size="small" 
                            class="delete-icon"
                            @click="handleDeleteIcon('rank1_icon')"
                        >
                            <el-icon><Delete /></el-icon>
                        </el-button>
                        <span class="icon-title">第一名</span>
                    </div>
                </el-form-item>

                <el-form-item label="排名2图标：">
                    <div class="avatar-container">
                        <el-upload
                            class="avatar-uploader"
                            action="/adminApi/Upload/file"
                            :show-file-list="false"
                            :on-success="(res) => handleUploadSuccess(res, 'rank2_icon')"
                        >
                            <img v-if="form.rank2_icon" :src="form.rank2_icon" class="avatar">
                            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                        </el-upload>
                        <el-button 
                            v-if="form.rank2_icon !== DEFAULT_ICONS.rank2_icon" 
                            type="danger" 
                            size="small" 
                            class="delete-icon"
                            @click="handleDeleteIcon('rank2_icon')"
                        >
                            <el-icon><Delete /></el-icon>
                        </el-button>
                        <span class="icon-title">第二名</span>
                    </div>
                </el-form-item>

                <el-form-item label="排名3图标：">
                    <div class="avatar-container">
                        <el-upload
                            class="avatar-uploader"
                            action="/adminApi/Upload/file"
                            :show-file-list="false"
                            :on-success="(res) => handleUploadSuccess(res, 'rank3_icon')"
                        >
                            <img v-if="form.rank3_icon" :src="form.rank3_icon" class="avatar">
                            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                        </el-upload>
                        <el-button 
                            v-if="form.rank3_icon !== DEFAULT_ICONS.rank3_icon" 
                            type="danger" 
                            size="small" 
                            class="delete-icon"
                            @click="handleDeleteIcon('rank3_icon')"
                        >
                            <el-icon><Delete /></el-icon>
                        </el-button>
                        <span class="icon-title">第三名</span>
                    </div>
                </el-form-item>

                <el-form-item>
                    <el-button 
                        type="primary" 
                        :loading="loading" 
                        @click="save"
                        class="save-btn"
                        :disabled="!form.enabled && !hasCustomIcons"
                    >
                        保存设置
                    </el-button>
                </el-form-item>
            </el-form>
        </el-card>
    </div>

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>

    <script>
        const { createApp, ref, reactive, computed } = Vue;
        const { ElMessage, ElNotification } = ElementPlus;

        // 添加默认图标常量
        const DEFAULT_ICONS = {
            rank1_icon: 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMjAgMTIwIiB3aWR0aD0iMjQiIGhlaWdodD0iMjQiPjxwYXRoIGQ9Ik02MCAxMEwxMDUgMzVWODVMNjAgMTEwTDE1IDg1VjM1TDYwIDEwWiIgZmlsbD0iI0ZGQjAyMCIgc3Ryb2tlPSIjRkY5NTAwIiBzdHJva2Utd2lkdGg9IjIiLz48Y2lyY2xlIGN4PSI2MCIgY3k9IjYwIiByPSIyMCIgZmlsbD0iI0ZGRDcwMCIgc3Ryb2tlPSIjRkZDNzAwIiBzdHJva2Utd2lkdGg9IjIiLz48cGF0aCBkPSJNNjAgNDVMNjQgNTVINzVMNjYgNjJMNzAgNzJMNjAgNjVMNTAgNzJMNTQgNjJMNDUgNTVINTZMNjAgNDVaIiBmaWxsPSIjRkZGRkZGIi8+PHBhdGggZD0iTTM1IDUwQzI1IDU1IDIwIDY1IDI1IDcwQzM1IDY1IDQwIDU1IDM1IDUwWiIgZmlsbD0iIzQxNjlFMSIgb3BhY2l0eT0iMC45Ii8+PHBhdGggZD0iTTg1IDUwQzk1IDU1IDEwMCA2NSA5NSA3MEM4NSA2NSA4MCA1NSA4NSA1MFoiIGZpbGw9IiM0MTY5RTEiIG9wYWNpdHk9IjAuOSIvPjx0ZXh0IHg9IjYwIiB5PSI4NSIgZm9udC1mYW1pbHk9IkFyaWFsIEJsYWNrLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjI0IiBmb250LXdlaWdodD0iYm9sZCIgZmlsbD0iI0ZGRkZGRiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+Tk8uMTwvdGV4dD48L3N2Zz4=',
            rank2_icon: 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMjAgMTIwIiB3aWR0aD0iMjQiIGhlaWdodD0iMjQiPjxwYXRoIGQ9Ik02MCAxMEwxMDUgMzVWODVMNjAgMTEwTDE1IDg1VjM1TDYwIDEwWiIgZmlsbD0iI0ZGNzQ1MiIgc3Ryb2tlPSIjRkY1QzNEIiBzdHJva2Utd2lkdGg9IjIiLz48Y2lyY2xlIGN4PSI2MCIgY3k9IjYwIiByPSIyMCIgZmlsbD0iI0ZGRDcwMCIgc3Ryb2tlPSIjRkZDNzAwIiBzdHJva2Utd2lkdGg9IjIiLz48cGF0aCBkPSJNNjAgNDVMNjQgNTVINzVMNjYgNjJMNzAgNzJMNjAgNjVMNTAgNzJMNTQgNjJMNDUgNTVINTZMNjAgNDVaIiBmaWxsPSIjRkZGRkZGIi8+PHBhdGggZD0iTTM1IDUwQzI1IDU1IDIwIDY1IDI1IDcwQzM1IDY1IDQwIDU1IDM1IDUwWiIgZmlsbD0iIzQxNjlFMSIgb3BhY2l0eT0iMC45Ii8+PHBhdGggZD0iTTg1IDUwQzk1IDU1IDEwMCA2NSA5NSA3MEM4NSA2NSA4MCA1NSA4NSA1MFoiIGZpbGw9IjM0MTY5RTEiIG9wYWNpdHk9IjAuOSIvPjx0ZXh0IHg9IjYwIiB5PSI4NSIgZm9udC1mYW1pbHk9IkFyaWFsIEJsYWNrLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjI0IiBmb250LXdlaWdodD0iYm9sZCIgZmlsbD0iI0ZGRkZGRiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+Tk8uMjwvdGV4dD48L3N2Zz4=',
            rank3_icon: 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMjAgMTIwIiB3aWR0aD0iMjQiIGhlaWdodD0iMjQiPjxwYXRoIGQ9Ik02MCAxMEwxMDUgMzVWODVMNjAgMTEwTDE1IDg1VjM1TDYwIDEwWiIgZmlsbD0iIzQwQjg4MyIgc3Ryb2tlPSIjMzY5QjZGIiBzdHJva2Utd2lkdGg9IjIiLz48Y2lyY2xlIGN4PSI2MCIgY3k9IjYwIiByPSIyMCIgZmlsbD0iI0ZGRDcwMCIgc3Ryb2tlPSIjRkZDNzAwIiBzdHJva2Utd2lkdGg9IjIiLz48cGF0aCBkPSJNNjAgNDVMNjQgNTVINzVMNjYgNjJMNzAgNzJMNjAgNjVMNTAgNzJMNTQgNjJMNDUgNTVINTZMNjAgNDVaIiBmaWxsPSIjRkZGRkZGIi8+PHBhdGggZD0iTTM1IDUwQzI1IDU1IDIwIDY1IDI1IDcwQzM1IDY1IDQwIDU1IDM1IDUwWiIgZmlsbD0iIzQxNjlFMSIgb3BhY2l0eT0iMC45Ii8+PHBhdGggZD0iTTg1IDUwQzk1IDU1IDEwMCA2NSA5NSA3MEM4NSA2NSA4MCA1NSA4NSA1MFoiIGZpbGw9IiM0MTY5RTEiIG9wYWNpdHk9IjAuOSIvPjx0ZXh0IHg9IjYwIiB5PSI4NSIgZm9udC1mYW1pbHk9IkFyaWFsIEJsYWNrLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjI0IiBmb250LXdlaWdodD0iYm9sZCIgZmlsbD0iI0ZGRkZGRiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+Tk8uMzwvdGV4dD48L3N2Zz4='
        };

        const useSourceDisplay = () => {
            const loading = ref(false);
            
            const form = reactive({
                enabled: true,
                rank1_icon: '',
                rank2_icon: '',
                rank3_icon: ''
            });
            
            // 计算属性：是否有自定义图标
            const hasCustomIcons = computed(() => {
                return form.rank1_icon !== DEFAULT_ICONS.rank1_icon || 
                       form.rank2_icon !== DEFAULT_ICONS.rank2_icon ||
                       form.rank3_icon !== DEFAULT_ICONS.rank3_icon;
            });

            const fetchData = async () => {
                loading.value = true;
                try {
                    const { data: response } = await axios.post("/plugin/Sourcedisplay/api/fetchData");
                    if (response?.code === 200) {
                        const { data } = response;
                        form.enabled = !!data.enabled;
                        
                        // 确保只在有值时才更新表单
                        if (data.rank_icons) {
                            form.rank1_icon = data.rank_icons.rank1 || DEFAULT_ICONS.rank1_icon;
                            form.rank2_icon = data.rank_icons.rank2 || DEFAULT_ICONS.rank2_icon;
                            form.rank3_icon = data.rank_icons.rank3 || DEFAULT_ICONS.rank3_icon;
                        } else {
                            // 如果没有配置数据，使用默认图标
                            form.rank1_icon = DEFAULT_ICONS.rank1_icon;
                            form.rank2_icon = DEFAULT_ICONS.rank2_icon;
                            form.rank3_icon = DEFAULT_ICONS.rank3_icon;
                        }
                    } else {
                        ElMessage.error('获取数据失败：' + (response?.msg || '未知错误'));
                    }
                } catch (error) {
                    console.error('获取数据失败:', error);
                    ElMessage.error('获取数据失败：' + (error.message || '网络错误'));
                } finally {
                    loading.value = false;
                }
            };

            const save = async () => {
                loading.value = true;
                try {
                    const { data: response } = await axios.post("/plugin/Sourcedisplay/api/save", form);
                    if (response?.code === 200) {
                        ElNotification({
                            title: '成功',
                            message: '设置已保存',
                            type: 'success',
                            duration: 2000
                        });
                    } else {
                        ElMessage.error(response?.msg || '保存失败');
                    }
                } catch (error) {
                    console.error('保存失败:', error);
                    ElMessage.error('保存失败：' + (error.message || '网络错误'));
                } finally {
                    loading.value = false;
                }
            };

            const handleUploadSuccess = (response, field) => {
                if (response.code === 200) {
                    form[field] = response.data.url;
                    ElMessage.success('上传成功');
                } else {
                    ElMessage.error('上传失败：' + response.msg);
                }
            };

            const handleDeleteIcon = async (field) => {
                try {
                    const { data: response } = await axios.post("/plugin/Sourcedisplay/api/save", {
                        ...form,
                        [field]: '' // 发送空字符串来清除图标
                    });
                    
                    if (response?.code === 200) {
                        form[field] = DEFAULT_ICONS[field]; // 重置为默认图标
                        ElMessage.success('删除成功');
                    } else {
                        ElMessage.error(response?.msg || '删除失败');
                    }
                } catch (error) {
                    console.error('删除失败:', error);
                    ElMessage.error('删除失败：' + (error.message || '网络错误'));
                }
            };

            // 初始化数据
            fetchData();

            return {
                loading,
                form,
                save,
                handleUploadSuccess,
                handleDeleteIcon,
                DEFAULT_ICONS, // 暴露默认图标常量
                hasCustomIcons
            };
        };

        createApp({
            setup() {
                return useSourceDisplay();
            }
        })
        .use(ElementPlus, {
            locale: ElementPlusLocaleZhCn
        })
        .mount('#app');
    </script>
</body>
</html> 