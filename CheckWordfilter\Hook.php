<?php

namespace plugin\CheckWordfilter;
 
use app\common\model\Goods as GoodsModel;

class Hook
{

    public function handle($goods)
    {
        if (plugconf('CheckWordfilter.status') == 1) {
            $finish_goods = GoodsModel::with('extend')->where(['id' => $goods->id])->find();

            $check_res = $this->check_wordfilter(json_encode($finish_goods->toArray(), JSON_UNESCAPED_UNICODE) . json_encode($finish_goods->parent_id == 0 ? $finish_goods->extend->toArray() : [], JSON_UNESCAPED_UNICODE));
            if ($check_res !== true) {
                throw new \Exception("检测到敏感词“" . $check_res . "”，请遵守《商品发布条例》否则您的账号将会受到限制！");
            }
        }
    }

    /**
     * 字词过滤
     *
     * @param string $str 待检测字符串
     *
     * @return bool/string FALSE/敏感词汇
     */
    private function check_wordfilter($str)
    {
        if ($str !== '') {
            $words = explode('|', trim(plugconf('CheckWordfilter.wordfilter_danger'), '|'));
            foreach ($words as $word) {
                if ($word && $word != "") {
                    if (strpos($str, $word) !== false) {
                        return $word;
                    }
                }
            }
        }
        return true;
    }
}
