<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>飘窗设置</title>
    <script>
        // 这里可以从后端模板变量中获取
        window.merchant_id = '{$merchant_id|default=0}'; // ThinkPHP 模板语法
        window.shop_name = '{$shop_name|default=""}';
    </script>
    <style>
        /* 基础样式 */
        body {
            background: #f5f7fa;
            margin: 0;
            padding: 16px;
            min-height: 500px;
            box-sizing: border-box;
        }

        .el-card {
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05) !important;
            margin: 0 auto;
            max-width: 900px;
        }

        /* 响应式布局 */
        @media screen and (max-width: 768px) {
            body {
                padding: 10px;
            }

            .el-card {
                margin: 0;
                border-radius: 8px;
            }

            .el-form {
                padding: 10px;
            }

            .el-form-item {
                margin-bottom: 24px;
            }

            /* 调整标签宽度 */
            .el-form {
                --el-form-label-width: 90px !important;
            }

            .el-form-item__label {
                float: none;
                display: block;
                text-align: left;
                padding: 0 0 8px 0;
                line-height: 1.4;
                white-space: normal;
            }

            .el-form-item__content {
                margin-left: 0 !important;
            }

            /* 调整数字输入框组的布局 */
            div[style*="display: flex; align-items: center;"] {
                flex-wrap: wrap;
                gap: 8px;
            }

            div[style*="display: flex; align-items: center;"] > span {
                min-width: 60px;
            }

            .el-input-number {
                width: calc(50% - 50px) !important;
                margin: 0 !important;
            }

            /* 优化图片上传区域 */
            .image-container {
                width: 100%;
                max-width: 300px;
                margin: 0 auto;
            }

            .avatar-uploader {
                width: 100% !important;
                min-width: unset;
            }

            /* 确保上传图标居中显示 */
            .avatar-uploader-icon {
                width: 100%;
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 28px;
                color: #8c939d;
            }

            /* 优化消息提示文本 */
            .el-form-item-msg {
                margin-top: 8px;
                line-height: 1.4;
            }

            /* 输入框组样式优化 */
            .number-input-group {
                flex-direction: column;
                gap: 12px;
            }

            .number-input-item {
                width: 100%;
                box-sizing: border-box;
                justify-content: space-between;
            }

            /* 图片上传区域优化 */
            .image-container {
                width: 100%;
                max-width: 300px;
                margin: 0 auto;
            }

            .avatar-uploader {
                width: 100% !important;
                min-width: unset;
            }

            /* 链接输入框 */
            .el-input {
                width: 100%;
            }

            /* 尺寸和位置输入组 */
            div[style*="display: flex; align-items: center;"] {
                flex-direction: column;
                align-items: stretch !important;
                gap: 12px;
            }

            .el-input-number {
                width: 100% !important;
                margin: 0 !important;
            }

            /* 保存按钮 */
            .el-button {
                width: 100%;
                margin-top: 20px;
                height: 44px;
            }

            /* 调整表单项布局为垂直排列 */
            .el-form-item {
                margin-bottom: 24px;
                display: flex;
                flex-direction: column;
            }

            /* 标签样式调整 */
            .el-form-item__label {
                width: 100% !important;
                text-align: left;
                padding: 0 0 12px 0 !important;
                margin-bottom: 8px;
                line-height: 1.4;
                white-space: normal;
            }

            /* 内容区域样式调整 */
            .el-form-item__content {
                margin-left: 0 !important;
                width: 100%;
            }

            /* 重置表单标签宽度 */
            .el-form {
                --el-form-label-width: auto !important;
            }

            /* 修改显示位置输入框组的样式 */
            .position-inputs {
                flex-direction: column;
                align-items: stretch !important;
            }

            .position-input-group {
                width: 100%;
                justify-content: space-between;
            }

            .position-input-group .el-input-number {
                width: 70% !important;
            }
        }

        /* 超小屏幕优化 */
        @media screen and (max-width: 375px) {
            body {
                padding: 8px;
            }
            .el-form-item__label {
                font-size: 14px;
            }
            .el-form-item-msg {
                font-size: 11px;
            }
            div[style*="display: flex; align-items: center;"] {
                flex-direction: column;
                align-items: flex-start !important;
            }
            .el-input-number {
                width: 100% !important;
            }
        }

        /* 暗黑模式支持 */
        @media (prefers-color-scheme: dark) {
            body {
                background: #1a1a1a;
            }
            .el-card {
                background: #2b2b2b;
            }
            .number-input-item {
                background: rgba(255,255,255,0.05);
            }
        }

        /* 触摸优化 */
        @media (hover: none) {
            .el-button,
            .el-switch,
            .action-icon {
                min-height: 44px;
            }
            .number-input-item {
                padding: 12px;
            }
            .el-input-number__decrease,
            .el-input-number__increase {
                height: 32px;
                width: 32px;
            }
        }

        /* 横屏模式优化 */
        @media screen and (max-width: 768px) and (orientation: landscape) {
            .el-form {
                max-width: 600px;
                margin: 0 auto;
            }
            .number-input-group {
                flex-direction: row;
                flex-wrap: wrap;
            }
            .number-input-item {
                width: calc(50% - 6px);
            }
        }

        /* 高分辨率屏幕优化 */
        @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
            .el-button,
            .el-input,
            .el-switch {
                transform: translateZ(0);
            }
        }

        /* 图片尺寸输入框组样式 */
        .size-inputs {
            display: flex !important;
            flex-wrap: wrap;
            gap: 8px;
            align-items: center !important;
            width: 100%;
        }
        .size-input-group {
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;
            min-width: 200px;
        }
        .size-input-group span {
            min-width: 45px;
        }
        .size-input-group .el-input-number {
            flex: 1;
            max-width: 160px;
        }

        @media screen and (max-width: 768px) {
            .size-inputs {
                flex-direction: column;
                align-items: stretch !important;
            }
            .size-input-group {
                width: 100%;
                justify-content: space-between;
            }
            .size-input-group .el-input-number {
                width: 70% !important;
            }
        }

        /* 尺寸限制输入框组样式 */
        .size-limit-inputs {
            width: 100%;
        }
        .size-limit-group {
            flex: 1;
            min-width: 200px;
        }

        @media screen and (max-width: 768px) {
            .size-limit-inputs {
                flex-direction: column;
                gap: 12px;
            }
            .size-limit-group {
                width: 100%;
                justify-content: space-between;
            }
            .size-limit-group .el-input-number {
                flex: 1;
                margin-left: 8px;
            }
        }

        /* 尺寸限制相关样式 */
        .size-limit-container {
            width: 100%;
            margin-bottom: 8px;
        }
        .size-limit-row {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }
        .size-limit-item {
            display: flex;
            align-items: center;
            flex: 1;
            min-width: 240px;
        }
        .size-label {
            min-width: 80px;
            margin-right: 10px;
            color: var(--el-text-color-regular);
            font-size: 14px;
            white-space: nowrap;
        }
        .el-input-number {
            width: 140px !important;
        }

        /* 移动端适配 */
        @media screen and (max-width: 768px) {
            .size-limit-row {
                flex-direction: column;
                gap: 12px;
            }
            .size-limit-item {
                width: 100%;
                justify-content: space-between;
            }
            .size-label {
                min-width: auto;
            }
            .el-input-number {
                width: 160px !important;
            }
        }

        /* 暗黑模式适配 */
        @media (prefers-color-scheme: dark) {
            .size-label {
                color: var(--el-text-color-regular);
            }
        }

        /* 高分屏适配 */
        @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
            .size-limit-item {
                transform: translateZ(0);
            }
        }

        /* 卡片样式 */
        .el-card {
            margin: 20px;
        }
        
        /* 上传组件样式 */
        .avatar-uploader {
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            aspect-ratio: 1;  /* 保持正方形比例 */
            max-width: 300px;
            margin: 0 auto;
        }
        .avatar-uploader:hover {
            border-color: #409EFF;
        }
        .avatar-uploader-icon {
            font-size: 28px;
            color: #8c939d;
            width: 178px;
            height: 178px;
            text-align: center;
            line-height: 178px;
        }
        .avatar {
            width: 100% !important;
            height: 100% !important;
            object-fit: contain;  /* 确保图片完整显示 */
            background: #f5f7fa;
        }
        
        /* 表单提示信息 */
        .el-form-item-msg {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        
        /* 图片容器样式 */
        .image-container {
            position: relative;
            display: inline-block;
            width: auto;    /* 改为自适应宽度 */
            height: auto;   /* 改为自适应高度 */
            min-width: 178px;
            min-height: 178px;
        }
        .image-actions {
            position: absolute;
            top: 8px;
            right: 8px;
            display: flex;
            gap: 8px;
            background: rgba(0, 0, 0, 0.6);
            padding: 4px 8px;
            border-radius: 4px;
            opacity: 0;
            transition: opacity 0.3s;
        }
        .image-container:hover .image-actions {
            opacity: 1;
        }
        .action-icon {
            color: #fff;
            font-size: 20px;
            cursor: pointer;
        }
        .action-icon:hover {
            color: #409EFF;
        }
        
        /* 表单控件样式 */
        .el-input-number {
            width: 120px;
        }
        .el-form {
            max-width: 800px;
            margin: 0 auto;
        }
        .el-form-item:last-child {
            margin-bottom: 0;
        }
        
        /* 输入框样式优化 */
        .el-input__inner {
            text-align: left;
        }
        
        /* 表单项间距 */
        .el-form-item {
            margin-bottom: 22px;
        }
        
        /* 链接输入框宽度 */
        .el-form-item__content .el-input {
            max-width: 500px;
        }

        /* 图片预览相关样式 */
        .el-message-box__wrapper {
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .el-message-box {
            max-width: 90%;
            max-height: 90vh;
            overflow: auto;
        }
    </style>
</head>

<body>
    <div id="app">
        <el-card shadow="never">
            <el-form :model="form" label-width="120px">
                <!-- 飘窗开关 -->
                <el-form-item label="飘窗开关：">
                    <div style="display: flex; align-items: center;">
                        <el-switch v-model="form.status" :active-value="1" :inactive-value="0" />
                    </div>
                    <div class="el-form-item-msg">请修改商家昵称如果默认飘窗显示不了</div>
                </el-form-item>

                <!-- 图片上传 -->
                <el-form-item label="图片上传：">
                    <div class="image-container">
                        <el-upload
                            class="avatar-uploader"
                            :action="uploadUrl"
                            :headers="uploadHeaders"
                            :show-file-list="false"
                            :on-success="handleUploadSuccess"
                            :on-error="onUploadError"
                            :before-upload="beforeUpload"
                            :with-credentials="true"
                            name="file">
                            <img v-if="form.image_url" :src="form.image_url" class="avatar">
                            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                        </el-upload>
                        
                        <div v-if="form.image_url" class="image-actions">
                            <el-icon class="action-icon" @click="previewImage"><ZoomIn /></el-icon>
                            <el-icon class="action-icon" @click="removeImage"><Delete /></el-icon>
                        </div>
                    </div>
                    <div class="el-form-item-msg">悬浮在页面上显示的图片，建议比例相近，大小不超过2MB</div>
                </el-form-item>

                <!-- 弹窗图片 -->
                <el-form-item label="弹窗图片：">
                    <div class="image-container">
                        <el-upload
                            class="avatar-uploader"
                            :action="uploadUrl"
                            :headers="uploadHeaders"
                            :show-file-list="false"
                            :on-success="handlePopupUploadSuccess"
                            :on-error="onUploadError"
                            :before-upload="beforeUpload"
                            :with-credentials="true"
                            name="file">
                            <img v-if="form.popup_image_url" :src="form.popup_image_url" class="avatar">
                            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                        </el-upload>
                        
                        <div v-if="form.popup_image_url" class="image-actions">
                            <el-icon class="action-icon" @click="previewPopupImage"><ZoomIn /></el-icon>
                            <el-icon class="action-icon" @click="removePopupImage"><Delete /></el-icon>
                        </div>
                    </div>
                    <div class="el-form-item-msg">点击悬浮框后显示的弹窗图片（如二维码），建议清晰可识别</div>
                </el-form-item>

                <!-- 跳转链接 -->
                <el-form-item label="跳转链接：">
                    <el-input v-model="form.link_url" placeholder="点击图片跳转的链接地址" />
                </el-form-item>

                <!-- 弹窗设置 -->
                <el-form-item label="弹窗设置：">
                    <div style="margin-bottom: 15px;">
                        <div style="margin-bottom: 10px;">标题文本：</div>
                        <el-input v-model="form.popup_title" placeholder="弹窗标题" />
                    </div>
                    <div>
                        <div style="margin-bottom: 10px;">底部文本：</div>
                        <el-input v-model="form.popup_footer" placeholder="弹窗底部提示文字" />
                    </div>
                    <div class="el-form-item-msg">自定义弹窗中显示的标题和底部提示文字</div>
                </el-form-item>

                <!-- 图片尺寸 -->
                <el-form-item label="图片尺寸：">
                    <div class="size-inputs">
                        <div class="size-input-group">
                            <span>宽度：</span>
                            <el-input-number 
                                v-model="form.width" 
                                :min="50" 
                                :max="1200" 
                                :step="10"
                                controls-position="right"
                                @change="updateImageSize"
                            /> 
                        </div>
                        <div class="size-input-group">
                            <span>高度：</span>
                            <el-input-number 
                                v-model="form.height" 
                                :min="50" 
                                :max="1200" 
                                :step="10"
                                controls-position="right"
                                @change="updateImageSize"
                            />
                        </div>
                    </div>
                    <div class="el-form-item-msg">宽度和高度范围：50-1200像素</div>
                </el-form-item>

                <!-- 尺寸限制 -->
                <el-form-item label="尺寸限制：">
                    <div class="size-limit-container">
                        <div class="size-limit-row">
                            <div class="size-limit-item">
                                <span class="size-label">最小尺寸：</span>
                                <el-input-number 
                                    v-model="form.min_size" 
                                    :min="50" 
                                    :max="300" 
                                    :step="10"
                                    controls-position="right"
                                    @change="updateSizeLimit"
                                />
                            </div>
                            <div class="size-limit-item">
                                <span class="size-label">最大尺寸：</span>
                                <el-input-number 
                                    v-model="form.max_size" 
                                    :min="300" 
                                    :max="800" 
                                    :step="10"
                                    controls-position="right"
                                    @change="updateSizeLimit"
                                />
                            </div>
                        </div>
                    </div>
                    <div class="el-form-item-msg">最小尺寸范围：50-300像素，最大尺寸范围：300-800像素</div>
                </el-form-item>

                <!-- 显示位置 -->
                <el-form-item label="显示位置：">
                    <div class="position-inputs">
                        <div class="position-input-group">
                            <span>距底部：</span>
                            <el-input-number 
                                v-model="form.bottom" 
                                :min="0" 
                                :max="1000" 
                                controls-position="right"
                                style="width: 120px;" 
                            />
                        </div>
                        <div class="position-input-group">
                            <span>距右侧：</span>
                            <el-input-number 
                                v-model="form.right" 
                                :min="0" 
                                :max="1000" 
                                controls-position="right"
                                style="width: 120px;" 
                            />
                        </div>
                    </div>
                    <div class="el-form-item-msg">距离底部和右侧的像素值</div>
                </el-form-item>

                <!-- 保存按钮 -->
                <el-form-item>
                    <el-button 
                        type="primary" 
                        :loading="loading" 
                        @click="save"
                        :disabled="!canEdit"
                    >
                        保存设置
                    </el-button>
                    <div v-if="!canEdit" class="el-form-item-msg" style="color: #f56c6c; margin-top: 10px;">
                        管理员已禁止商家修改飘窗设置，如需更改请联系管理员
                    </div>
                </el-form-item>
            </el-form>
        </el-card>
    </div>

    <!-- 引入依赖库 -->
    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>

    <script>
        const { createApp, ref, reactive, onMounted, watch, nextTick } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        // 配置 axios
        axios.defaults.withCredentials = true;
        axios.defaults.validateStatus = function (status) {
            return status >= 200 && status < 300; // 默认值
        };

        createApp({
            setup() {
                // 状态定义
                const loading = ref(false);
                const previewVisible = ref(false);
                const canEdit = ref(true); // 是否允许商家编辑
                const uploadUrl = '/merchantApi/Upload/file';
                
                const form = reactive({
                    status: 0,
                    image_url: '',
                    popup_image_url: '',
                    link_url: '',
                    width: 200,
                    height: 200,
                    bottom: 20,
                    right: 20,
                    min_size: 200,
                    max_size: 500,
                    popup_title: '扫描二维码',
                    popup_footer: '请使用扫码软件扫描'
                });

                // 修改 validateSize 函数，移除旧的尺寸限制
                const validateSize = (value, min = 50, max = 1200) => {
                    if (value < min) return min;
                    if (value > max) return max;
                    return value;
                };

                // 修改图片预览容器的样式
                const updateImageSize = () => {
                    // 确保值在有效范围内
                    form.width = validateSize(form.width);
                    form.height = validateSize(form.height);

                    const imgElement = document.querySelector('.avatar');
                    const container = document.querySelector('.image-container');
                    const uploader = document.querySelector('.avatar-uploader');
                    
                    if (imgElement && form.image_url) {
                        // 设置实际尺寸
                        imgElement.style.width = `${form.width}px`;
                        imgElement.style.height = `${form.height}px`;
                        
                        // 同时更新容器尺寸
                        container.style.width = `${form.width}px`;
                        container.style.height = `${form.height}px`;
                        uploader.style.width = `${form.width}px`;
                        uploader.style.height = `${form.height}px`;
                    }
                };

                // 监听尺寸变化
                watch(() => form.width, (newVal) => {
                    form.width = validateSize(newVal);
                    updateImageSize();
                });

                watch(() => form.height, (newVal) => {
                    form.height = validateSize(newVal);
                    updateImageSize();
                });

                onMounted(() => {
                    let retryCount = 0;
                    const maxRetries = 3;
                    
                    const tryFetchData = async () => {
                        try {
                            await fetchData();
                        } catch (error) {
                            if (retryCount < maxRetries) {
                                retryCount++;
                                console.log(`获取数据失败，第 ${retryCount} 次重试...`);
                                setTimeout(tryFetchData, 1000 * retryCount); // 递增重试延迟
                            } else {
                                console.error('多次重试后仍然失败');
                                ElMessage.error('加载数据失败，请刷新页面重试');
                            }
                        }
                    };

                    tryFetchData();
                });

                axios.defaults.withCredentials = true;
                
                const fetchData = async () => {
                    try {
                        // 获取当前登录用户的数据
                        const res = await axios.post("/plugin/Imagesxuanfu/api/fetchData", {
                            merchant_id: window.merchant_id, // 如果页面中有商户ID
                            shop_name: window.shop_name // 如果页面中有商家名称
                        });
                        
                        if (res.data?.code === 200 && res.data.data) {
                            const data = res.data.data;
                            // 使用解构赋值并设置默认值
                            const {
                                status = 1,
                                image_url = '',
                                popup_image_url = '',
                                link_url = '',
                                width = 200,
                                height = 200,
                                bottom = 20,
                                right = 20,
                                min_size = 200,
                                max_size = 500,
                                popup_title = '扫描二维码',
                                popup_footer = '请使用扫码软件扫描',
                                merchant_can_edit = 1
                            } = data;

                            // 设置是否可编辑
                            canEdit.value = parseInt(merchant_can_edit) === 1;
                            
                            // 禁用编辑时给用户一个提示
                            if (!canEdit.value) {
                                ElMessage.warning('管理员已禁止商家修改飘窗设置');
                                
                                // 禁用所有输入元素
                                nextTick(() => {
                                    const inputs = document.querySelectorAll('input, .el-input-number, .el-switch, .avatar-uploader');
                                    inputs.forEach(input => {
                                        input.setAttribute('disabled', 'disabled');
                                    });
                                });
                            }

                            // 更新表单数据
                            Object.assign(form, {
                                status: parseInt(status),
                                image_url,
                                popup_image_url,
                                link_url,
                                width: parseInt(width),
                                height: parseInt(height),
                                bottom: parseInt(bottom),
                                right: parseInt(right),
                                min_size: parseInt(min_size),
                                max_size: parseInt(max_size),
                                popup_title,
                                popup_footer
                            });

                            // 确保加载的数据也在有效范围内
                            form.width = validateSize(form.width);
                            form.height = validateSize(form.height);

                            nextTick(() => {
                                updateImageSize();
                            });
                        } else if (res.data?.code === 403) {
                            ElMessage.error('请先登录');
                            // 可以在这里添加重定向到登录页面的逻辑
                            // window.location.href = '/login';
                        } else {
                            ElMessage.error(res.data?.msg || '获取数据失败');
                            console.error('获取数据失败:', res.data);
                        }
                    } catch (error) {
                        console.error('获取数据失败:', error);
                        ElMessage.error('获取数据失败，请检查网络连接');
                    }
                };

                const save = async () => {
                    // 检查是否有权限保存
                    if (!canEdit.value) {
                        ElMessage.error('管理员已禁止商家修改飘窗设置');
                        return;
                    }
                    
                    if (!form.image_url && form.status === 1) {
                        ElMessage.error('请先上传图片');
                        return;
                    }

                    try {
                        loading.value = true;
                        // 保存前验证尺寸
                        form.width = validateSize(form.width);
                        form.height = validateSize(form.height);

                        const res = await axios.post("/plugin/Imagesxuanfu/api/save", form);
                        if (res.data?.code === 200) {
                            ElMessage.success('保存成功');
                            await fetchData();
                        } else {
                            ElMessage.error(res.data?.msg || '保存失败');
                        }
                    } catch (error) {
                        console.error('保存失败:', error);
                        ElMessage.error('保存失败，请稍后重试');
                    } finally {
                        loading.value = false;
                    }
                };

                const handleUploadSuccess = (res) => {
                    // 检查是否有权限上传
                    if (!canEdit.value) {
                        ElMessage.error('管理员已禁止商家修改飘窗设置');
                        return;
                    }
                    
                    if (res.code === 1 && res.data?.url) {
                        form.image_url = res.data.url;
                        ElMessage.success('上传成功');
                        nextTick(() => {
                            updateImageSize();
                        });
                        save();
                    } else {
                        form.image_url = '';
                        ElMessage.error(res.msg || '上传失败');
                    }
                };

                const handlePopupUploadSuccess = (res) => {
                    // 检查是否有权限上传
                    if (!canEdit.value) {
                        ElMessage.error('管理员已禁止商家修改飘窗设置');
                        return;
                    }
                    
                    if (res.code === 1 && res.data?.url) {
                        form.popup_image_url = res.data.url;
                        ElMessage.success('上传弹窗图片成功');
                        save();
                    } else {
                        form.popup_image_url = '';
                        ElMessage.error(res.msg || '上传失败');
                    }
                };

                const beforeUpload = (file) => {
                    // 检查是否有权限上传
                    if (!canEdit.value) {
                        ElMessage.error('管理员已禁止商家修改飘窗设置');
                        return false;
                    }
                    
                    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
                    if (!allowedTypes.includes(file.type)) {
                        ElMessage.error('只能上传 JPG、PNG、GIF 格式的图片！');
                        return false;
                    }
                    
                    const isLt2M = file.size / 1024 / 1024 < 2;
                    if (!isLt2M) {
                        ElMessage.error('图片大小不能超过 2MB！');
                        return false;
                    }

                    return new Promise((resolve, reject) => {
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            const img = new Image();
                            img.onload = () => {
                                if (img.width < 50 || img.height < 50) {
                                    ElMessage.error('图片尺寸不能小于 50x50 像素');
                                    reject();
                                } else if (img.width > 3000 || img.height > 3000) {
                                    ElMessage.error('图片尺寸不能大于 3000x3000 像素');
                                    reject();
                                } else {
                                    resolve(true);
                                }
                            };
                            img.onerror = () => {
                                ElMessage.error('图片文件已损坏，请重新选择');
                                reject();
                            };
                            img.src = e.target.result;
                        };
                        reader.onerror = () => {
                            ElMessage.error('图片读取失败，请重新选择');
                            reject();
                        };
                        reader.readAsDataURL(file);
                    });
                };

                const onUploadError = (err) => {
                    console.error('上传错误:', err);
                    ElMessage.error('上传失败：' + (err.message || '未知错误'));
                    form.image_url = '';
                };

                const previewImage = () => {
                    if (form.image_url) {
                        ElMessageBox.alert(
                            `<img src="${form.image_url}" style="max-width: 100%;">`,
                            '图片预览',
                            {
                                dangerouslyUseHTMLString: true,
                                showClose: true,
                                closeOnClickModal: true,
                                closeOnPressEscape: true,
                            }
                        );
                    }
                };
                
                const previewPopupImage = () => {
                    if (form.popup_image_url) {
                        ElMessageBox.alert(
                            `<img src="${form.popup_image_url}" style="max-width: 100%;">`,
                            '弹窗图片预览',
                            {
                                dangerouslyUseHTMLString: true,
                                showClose: true,
                                closeOnClickModal: true,
                                closeOnPressEscape: true,
                            }
                        );
                    }
                };
                
                const removeImage = () => {
                    // 检查是否有权限删除
                    if (!canEdit.value) {
                        ElMessage.error('管理员已禁止商家修改飘窗设置');
                        return;
                    }
                    
                    ElMessageBox.confirm('确定要删除悬浮框图片吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        form.image_url = '';
                        ElMessage.success('图片已删除');
                        save();
                    }).catch(() => {});
                };
                
                const removePopupImage = () => {
                    // 检查是否有权限删除
                    if (!canEdit.value) {
                        ElMessage.error('管理员已禁止商家修改飘窗设置');
                        return;
                    }
                    
                    ElMessageBox.confirm('确定要删除弹窗图片吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        form.popup_image_url = '';
                        ElMessage.success('弹窗图片已删除');
                        save();
                    }).catch(() => {});
                };

                const uploadHeaders = {
                    'X-Requested-With': 'XMLHttpRequest'
                };

                // 更新尺寸限制函数
                const updateSizeLimit = () => {
                    // 确保最大尺寸不小于最小尺寸
                    if (form.max_size <= form.min_size) {
                        form.max_size = form.min_size + 100;
                    }
                    
                    // 确保当前宽高在新的范围内
                    form.width = Math.min(Math.max(form.width, form.min_size), form.max_size);
                    form.height = Math.min(Math.max(form.height, form.min_size), form.max_size);
                    
                    // 更新预览
                    updateImageSize();
                };

                // 监听尺寸变化
                watch(() => form.min_size, updateSizeLimit);
                watch(() => form.max_size, updateSizeLimit);

                // 返回数据和方法
                return {
                    loading,
                    form,
                    canEdit,
                    uploadUrl,
                    uploadHeaders,
                    handleUploadSuccess,
                    handlePopupUploadSuccess,
                    beforeUpload,
                    onUploadError,
                    save,
                    previewVisible,
                    previewImage,
                    previewPopupImage,
                    removeImage,
                    removePopupImage,
                    updateImageSize
                };
            }
        })
        .use(ElementPlus)
        .mount('#app');
    </script>
</body>
</html> 