import requests
import ddddocr
import re
import os
from urllib.parse import urlparse, parse_qs
import random
import string
import time
import threading
import queue
from concurrent.futures import ThreadPoolExecutor, as_completed
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                           QLabel, QLineEdit, QPushButton, QTextEdit, QSpinBox, 
                           QProgressBar, QGroupBox, QGridLayout, QCheckBox)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
import sys
import json
from datetime import datetime, timedelta

# 用户代理列表，随机轮换使用
USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.82 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.81 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:93.0) Gecko/20100101 Firefox/93.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Safari/605.1.15',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.6778.86 Safari/537.36',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.81 Safari/537.36',
    'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
    'Mozilla/5.0 (iPad; CPU OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36'
]

# 全局变量，用于追踪最后一次请求的时间
last_request_time = 0
request_lock = threading.Lock()

# IP代理管理类
class ProxyManager:
    def __init__(self, api_url="", log_callback=None):
        self.api_url = api_url
        self.current_proxy = None
        self.proxy_list = []
        self.failed_proxies = set()  # 记录失败的代理
        self.last_update_time = None
        self.update_interval = timedelta(minutes=30)  # 30分钟更新一次
        self.lock = threading.Lock()
        self.log_callback = log_callback
        self.direct_mode = False  # 是否使用直连模式
        
    def log(self, message):
        """记录日志"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)
    
    def fetch_proxy_list(self):
        """从API获取代理IP列表"""
        try:
            # 直接使用GET请求获取代理IP
            response = requests.get(self.api_url, timeout=10)
            
            if response.status_code == 200:
                content = response.text.strip()
                if content:
                    # 简单处理返回的文本，分割成IP列表
                    ip_list = [ip.strip() for ip in content.split('\n') if ip.strip()]
                    if ip_list:
                        self.log(f"成功获取 {len(ip_list)} 个代理IP")
                        
                        # 从列表中排除已知失败的代理
                        ip_list = [ip for ip in ip_list if ip not in self.failed_proxies]
                        self.log(f"排除失败代理后剩余 {len(ip_list)} 个代理IP")
                        
                        return ip_list
                    else:
                        self.log("API返回的IP为空")
                else:
                    self.log("API返回的内容为空")
            else:
                self.log(f"获取代理IP失败，状态码: {response.status_code}")
                
        except Exception as e:
            self.log(f"获取代理IP出错: {e}")
            
        return []
    
    def set_api_url(self, api_url):
        """设置API URL"""
        if api_url and api_url.strip():
            self.api_url = api_url.strip()
            self.log(f"已设置代理API URL: {self.api_url}")
            # 重置失败代理记录
            self.failed_proxies = set()
            return True
        return False
    
    def mark_proxy_failed(self, proxy):
        """标记代理为失败状态"""
        if proxy:
            with self.lock:
                self.failed_proxies.add(proxy)
                if proxy == self.current_proxy:
                    self.current_proxy = None
                self.log(f"已将代理 {proxy} 标记为失败")
    
    def test_proxy(self, proxy):
        """测试代理是否可用"""
        try:
            test_url = "http://www.newbt.net:8888/mail/new_user.php"
            proxies = {
                'http': f'http://{proxy}',
                'https': f'http://{proxy}'
            }
            
            self.log(f"正在测试代理 {proxy} 的连接...")
            response = requests.get(test_url, proxies=proxies, timeout=5)
            
            if response.status_code == 200:
                self.log(f"代理 {proxy} 测试成功")
                return True
            else:
                self.log(f"代理 {proxy} 测试失败，状态码: {response.status_code}")
                # 自动标记为失败状态
                self.mark_proxy_failed(proxy)
        except Exception as e:
            self.log(f"代理 {proxy} 测试出错: {e}")
            # 自动标记为失败状态
            self.mark_proxy_failed(proxy)
        
        return False
    
    def toggle_direct_mode(self, use_direct=None):
        """切换直连模式"""
        with self.lock:
            if use_direct is not None:
                self.direct_mode = use_direct
            else:
                self.direct_mode = not self.direct_mode
                
            if self.direct_mode:
                self.log("已切换到直连模式")
            else:
                self.log("已切换到代理模式")
            
            return self.direct_mode
    
    def get_proxy(self, force_update=False):
        """获取一个代理IP，如果需要或强制更新则从API获取新列表"""
        if self.direct_mode:
            return None  # 直连模式返回None
            
        with self.lock:
            current_time = datetime.now()
            
            # 检查是否需要更新代理列表
            need_update = (force_update or 
                not self.last_update_time or 
                not self.proxy_list or 
                current_time - self.last_update_time > self.update_interval)
                
            if need_update:
                self.log("更新代理IP列表...")
                self.proxy_list = self.fetch_proxy_list()
                self.last_update_time = current_time
                
                if not self.proxy_list:
                    self.log("无法获取代理IP，将使用直连")
                    self.current_proxy = None
                    self.direct_mode = True
                    return None
            
            # 如果有可用的代理列表，随机选择一个
            if self.proxy_list:
                # 确保不重复选择当前正在使用的代理
                available_proxies = [p for p in self.proxy_list if p != self.current_proxy]
                
                if not available_proxies and self.current_proxy:
                    # 如果只有一个代理且正在使用，则继续使用
                    self.log(f"只有一个可用代理: {self.current_proxy}，继续使用")
                    return self.current_proxy
                elif available_proxies:
                    # 随机选择一个不同的代理
                    self.current_proxy = random.choice(available_proxies)
                    self.log(f"已选择代理IP: {self.current_proxy}")
                    return self.current_proxy
            
            self.log("无可用代理IP，将使用直连")
            self.direct_mode = True
            self.current_proxy = None
            return None
    
    def get_proxy_dict(self):
        """获取用于requests的代理字典格式"""
        if self.direct_mode:
            return None
            
        proxy = self.get_proxy()
        if proxy:
            return {
                'http': f'http://{proxy}',
                'https': f'http://{proxy}'
            }
        return None

# 创建全局代理管理器实例
proxy_manager = None

def setup_proxy_manager(log_callback=None):
    """设置全局代理管理器"""
    global proxy_manager
    if not proxy_manager:
        proxy_manager = ProxyManager(log_callback=log_callback)
    return proxy_manager

def throttled_request(session, method, url, min_interval=1.0, **kwargs):
    """
    限制请求频率的请求函数，支持代理
    
    Args:
        session: requests会话
        method: 'get' 或 'post'
        url: 请求的URL
        min_interval: 两次请求之间的最小间隔时间（秒）
        kwargs: 传递给requests的其他参数
    
    Returns:
        requests.Response对象
    """
    global last_request_time, proxy_manager
    
    # 随机选择一个用户代理
    if 'headers' not in kwargs:
        kwargs['headers'] = {}
    kwargs['headers']['User-Agent'] = random.choice(USER_AGENTS)
    
    # 添加代理设置（如果启用）
    if proxy_manager:
        proxy_dict = proxy_manager.get_proxy_dict()
        if proxy_dict:
            kwargs['proxies'] = proxy_dict
    
    # 使用锁确保线程安全
    with request_lock:
        current_time = time.time()
        elapsed = current_time - last_request_time
        
        # 如果距离上次请求时间不足最小间隔，则等待
        if elapsed < min_interval:
            sleep_time = min_interval - elapsed + random.uniform(0.1, 0.5)
            time.sleep(sleep_time)
        
        # 执行请求
        if method.lower() == 'get':
            response = session.get(url, **kwargs)
        elif method.lower() == 'post':
            response = session.post(url, **kwargs)
        else:
            raise ValueError(f"不支持的HTTP方法: {method}")
        
        # 更新最后请求时间
        last_request_time = time.time()
        
        return response


def get_sessionid():
    """访问 new_user.php 获取 sessionid"""
    url = "http://www.newbt.net:8888/mail/new_user.php"
    session = requests.Session()

    try:
        # 使用限流请求
        response = throttled_request(
            session, 
            'get', 
            url, 
            min_interval=1.0,  # 减少间隔到1秒
            timeout=10
        )

        # 获取重定向后的 URL
        if response.status_code == 200:
            # 解析 URL 获取 sessionid
            parsed_url = urlparse(response.url)
            sessionid = parse_qs(parsed_url.query).get('sessionid', [None])[0]
            if sessionid:
                print(f"获取到的 sessionid: {sessionid}")
                return sessionid, session
            else:
                print("未能从 URL 中提取 sessionid")
                # 如果是代理模式失败，尝试标记代理失败
                if proxy_manager and proxy_manager.current_proxy and not proxy_manager.direct_mode:
                    proxy_manager.mark_proxy_failed(proxy_manager.current_proxy)
                return None, None
        else:
            print(f"访问 new_user.php 失败，状态码: {response.status_code}")
            # 如果是代理模式失败，尝试标记代理失败
            if proxy_manager and proxy_manager.current_proxy and not proxy_manager.direct_mode:
                proxy_manager.mark_proxy_failed(proxy_manager.current_proxy)
            return None, None
    except requests.exceptions.RequestException as e:
        print(f"请求发生错误: {e}")
        # 如果是代理模式失败，尝试标记代理失败
        if proxy_manager and proxy_manager.current_proxy and not proxy_manager.direct_mode:
            proxy_manager.mark_proxy_failed(proxy_manager.current_proxy)
        return None, None


def get_captcha(session, sessionid):
    """获取验证码并识别"""
    try:
        # 直接构建完整的验证码URL
        captcha_url = f"http://www.newbt.net:8888/mail/http_session_image.php?sessionid={sessionid}"

        # 设置请求头
        headers = {
            'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Referer': f'http://www.newbt.net:8888/mail/new_user.php?sessionid={sessionid}'
        }

        # 获取验证码数据，使用限流请求
        response = throttled_request(
            session, 
            'get', 
            captcha_url, 
            min_interval=3.0,
            headers=headers, 
            timeout=15
        )

        if response.status_code != 200:
            print(f"验证码请求失败，状态码: {response.status_code}")
            return None

        # 不再保存验证码图片到本地
        
        # 使用ddddocr识别
        ocr = ddddocr.DdddOcr(show_ad=False)  # 创建OCR实例
        ocr_rs = ocr.classification(response.content)

        # 清理识别结果，只保留字母和数字
        ocr_rs = re.sub(r'[^a-zA-Z0-9]', '', ocr_rs)
        
        # 验证识别结果是否合理
        if len(ocr_rs) < 4 or len(ocr_rs) > 6:
            print(f"验证码识别结果异常: {ocr_rs}，长度: {len(ocr_rs)}")
            return None

        print(f"识别出的验证码: {ocr_rs}")
        return ocr_rs

    except Exception as e:
        print(f"验证码处理过程出错: {e}")
        return None


def generate_random_credentials(length=8):
    """生成随机账号密码，包含英文字母和数字"""
    # 字符集：小写字母 + 数字
    chars = string.ascii_lowercase + string.digits
    # 确保至少包含一个字母和一个数字
    password = [
        random.choice(string.ascii_lowercase),  # 确保有一个字母
        random.choice(string.digits),  # 确保有一个数字
    ]
    # 剩余长度随机生成
    remaining_length = length - 2
    password.extend(random.choices(chars, k=remaining_length))
    # 打乱顺序
    random.shuffle(password)
    return ''.join(password)


def save_credentials(username, password, result):
    """保存账号密码到文件"""
    if "成功" in result:  # 只在注册成功时保存
        with threading.Lock():  # 添加线程锁保护文件写入
            current_time = time.strftime('%Y-%m-%d %H:%M:%S')
            with open('registered_accounts.txt', 'a', encoding='utf-8') as f:
                # 使用新的格式保存: 邮箱地址@newbt.net----密码----时间
                f.write(f"{username}@newbt.net----{password}----{current_time}\n")
            print(f"账号密码已保存到 registered_accounts.txt")


def register_user(username_length=8, password_length=10, log_callback=None, check_running=None):
    """注册单个用户"""
    # 生成随机账号密码
    random_username = generate_random_credentials(length=username_length)
    random_password = generate_random_credentials(length=password_length)

    # 使用回调函数或打印到控制台
    log_message = f"\n尝试注册新账号:\n账号: {random_username}\n密码: {random_password}"
    if log_callback:
        log_callback(log_message)
    else:
        with threading.Lock():
            print(log_message)

    # 检查是否应该继续运行
    if check_running and not check_running():
        if log_callback:
            log_callback("收到停止信号，中止注册流程")
        return False

    # 获取新的 sessionid，使用简化的重试策略
    sessionid = None
    session = None
    max_retries = 5  # 减少重试次数
    
    # 显示当前使用的代理状态
    if proxy_manager:
        if proxy_manager.direct_mode:
            if log_callback:
                log_callback("当前使用直连模式")
            else:
                print("当前使用直连模式")
        elif proxy_manager.current_proxy:
            if log_callback:
                log_callback(f"当前使用代理: {proxy_manager.current_proxy}")
            else:
                print(f"当前使用代理: {proxy_manager.current_proxy}")
    
    # 在所有重试失败后尝试切换到直连模式
    proxy_failures = 0
    for attempt in range(max_retries):
        # 检查是否应该继续运行
        if check_running and not check_running():
            if log_callback:
                log_callback("收到停止信号，中止获取sessionid")
            return False
            
        sessionid, session = get_sessionid()
        if sessionid:
            break
        
        proxy_failures += 1
        # 如果连续失败且使用代理，尝试切换到直连模式
        if proxy_failures >= 3 and proxy_manager and not proxy_manager.direct_mode:
            if log_callback:
                log_callback("多次获取 sessionid 失败，尝试切换到直连模式")
            else:
                print("多次获取 sessionid 失败，尝试切换到直连模式")
            proxy_manager.toggle_direct_mode(True)
            
        # 使用简单的等待时间
        wait_time = 1.0 + random.uniform(0.5, 1.0) * attempt
        
        log_message = f"获取 sessionid 失败，尝试第 {attempt+1}/{max_retries} 次，等待 {wait_time:.1f} 秒"
        if log_callback:
            log_callback(log_message)
        else:
            print(log_message)
        time.sleep(wait_time)

    if not sessionid:
        message = "获取 sessionid 全部尝试失败"
        if log_callback:
            log_callback(message)
        else:
            print(message)
        return False

    # 检查是否应该继续运行
    if check_running and not check_running():
        if log_callback:
            log_callback("收到停止信号，中止获取验证码")
        return False

    # 获取验证码，简化重试
    captcha_code = None
    max_captcha_retries = 5  # 减少验证码重试次数
    
    for attempt in range(max_captcha_retries):
        # 检查是否应该继续运行
        if check_running and not check_running():
            if log_callback:
                log_callback("收到停止信号，中止验证码处理")
            return False
            
        captcha_code = get_captcha(session, sessionid)
        if captcha_code and len(captcha_code) >= 4:  # 确保验证码长度合理
            break
            
        # 简化等待时间
        wait_time = 1.0 + random.uniform(0.5, 1.0) * attempt
        
        log_message = f"验证码获取或识别失败，尝试第 {attempt+1}/{max_captcha_retries} 次，等待 {wait_time:.1f} 秒"
        if log_callback:
            log_callback(log_message)
        else:
            print(log_message)
        time.sleep(wait_time)

    if not captcha_code or len(captcha_code) < 4:
        message = "验证码获取全部尝试失败"
        if log_callback:
            log_callback(message)
        else:
            print(message)
        return False

    # 检查是否应该继续运行
    if check_running and not check_running():
        if log_callback:
            log_callback("收到停止信号，中止发送注册请求")
        return False

    # 发送注册请求
    url = "http://www.newbt.net:8888/mail/new_user.php"

    data = {
        'user_name': random_username,
        'user_pass': random_password,
        'user_pass2': random_password,  # 确保密码一致
        'num': captcha_code,
        'button': '注册',
        'action': 'add'
    }
    
    log_message = f"步骤3 - 准备发送的数据: {data}"
    if log_callback:
        log_callback(log_message)
    else:
        print(log_message)

    headers = {
        'Host': 'www.newbt.net:8888',
        'Content-Type': 'application/x-www-form-urlencoded',
        'Cache-Control': 'max-age=0',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Origin': 'http://www.newbt.net:8888',
        'Upgrade-Insecure-Requests': '1',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Referer': f'http://www.newbt.net:8888/mail/new_user.php?sessionid={sessionid}',
        'Connection': 'keep-alive'
    }

    try:
        log_message = f"步骤4 - 发送POST请求到: {url}?sessionid={sessionid}"
        if log_callback:
            log_callback(log_message)
        else:
            print(log_message)
            
        # 使用限流请求，但减少间隔
        response = throttled_request(
            session, 
            'post', 
            f"{url}?sessionid={sessionid}",
            min_interval=1.0,  # 减少间隔
            data=data,
            headers=headers,
            timeout=10
        )

        response_text = response.text

        # 检查注册结果
        if "注册成功" in response_text:
            message = f"账号 {random_username} 注册成功!"
            if log_callback:
                log_callback(message)
            else:
                with threading.Lock():
                    print(message)
            save_credentials(random_username, random_password, "注册成功")
            return True
        else:
            message = f"账号 {random_username} 注册失败: {response_text[:100]}"
            if log_callback:
                log_callback(message)
            else:
                with threading.Lock():
                    print(message)
            return False

    except Exception as e:
        message = f"注册过程出错: {e}"
        if log_callback:
            log_callback(message)
        else:
            with threading.Lock():
                print(message)
        return False


class RegisterThread(QThread):
    """注册线程"""
    update_signal = pyqtSignal(str)  # 用于发送日志信息
    progress_signal = pyqtSignal(int, int)  # 用于更新进度(当前完成数, 成功数)
    finished_signal = pyqtSignal()  # 完成信号
    
    def __init__(self, thread_count, total_accounts, username_length, password_length):
        super().__init__()
        self.thread_count = thread_count
        self.total_accounts = total_accounts
        self.username_length = username_length
        self.password_length = password_length
        self.is_running = True
        self.task_queue = queue.Queue()  # 添加任务队列
        self.completed = 0
        self.successful = 0
        self.lock = threading.Lock()  # 添加线程锁
        self.worker_threads = []  # 存储工作线程引用
        self.last_progress_time = 0  # 记录上次进度更新时间
        
    def run(self):
        self.multi_register()
        self.finished_signal.emit()
        
    def stop(self):
        """停止所有注册任务"""
        self.log_callback("正在停止所有注册任务...")
        self.is_running = False
        
        # 清空队列中的所有任务
        with self.lock:
            try:
                while not self.task_queue.empty():
                    try:
                        self.task_queue.get_nowait()
                        self.task_queue.task_done()
                    except queue.Empty:
                        break
            except Exception as e:
                self.log_callback(f"清空队列时发生错误: {str(e)}")
                
        # 向队列中放入终止信号
        for _ in range(len(self.worker_threads) + 5):  # 额外添加终止信号确保所有线程收到
            try:
                self.task_queue.put(None, block=False)
            except:
                pass
                
        self.log_callback("已发送停止信号给所有工作线程")
        
    def log_callback(self, message):
        """日志回调函数"""
        try:
            self.update_signal.emit(message)
        except RuntimeError:
            # 处理Qt对象已销毁的情况
            print(f"日志回调失败: {message}")
        
    def update_progress(self, success=False):
        """更新进度，线程安全"""
        with self.lock:
            self.completed += 1
            if success:
                self.successful += 1
                
            # 控制进度更新频率，避免UI更新过于频繁
            current_time = time.time()
            if (current_time - self.last_progress_time >= 0.5) or (self.completed >= self.total_accounts):
                self.last_progress_time = current_time
                percentage = (self.completed / self.total_accounts) * 100 if self.total_accounts > 0 else 0
                self.log_callback(f"进度: {self.completed}/{self.total_accounts} ({percentage:.1f}%) (成功: {self.successful})")
                try:
                    self.progress_signal.emit(self.completed, self.successful)
                except RuntimeError:
                    # 处理Qt对象已销毁的情况
                    pass
        
    def worker_thread(self, worker_id):
        """工作线程函数，处理多个任务"""
        self.log_callback(f"工作线程 {worker_id} 启动")
        
        # 错开线程启动时间，避免请求拥塞
        time.sleep(random.uniform(0.2 * worker_id, 0.5 * worker_id + 0.3))
        
        consecutive_errors = 0  # 连续错误计数
        consecutive_success = 0  # 连续成功计数
        
        # 循环从队列中获取任务
        while self.is_running:
            task_id = None
            try:
                # 获取任务，设置超时避免无限等待
                task_id = self.task_queue.get(block=True, timeout=1)
                
                # 检查是否是终止信号或者程序已停止
                if task_id is None or not self.is_running:
                    self.task_queue.task_done()  # 确保标记任务完成
                    break
                
                self.log_callback(f"工作线程 {worker_id} 开始处理任务 {task_id}")
                
                try:
                    # 再次检查是否应该继续执行
                    if not self.is_running:
                        self.task_queue.task_done()
                        break
                        
                    # 创建一个检查函数用于传递给register_user
                    def check_running():
                        return self.is_running
                        
                    # 执行注册
                    success = register_user(
                        self.username_length, 
                        self.password_length, 
                        self.log_callback,
                        check_running  # 传递检查函数
                    )
                    
                    # 根据结果更新连续状态计数
                    if success:
                        consecutive_errors = 0
                        consecutive_success += 1
                    else:
                        consecutive_success = 0
                        consecutive_errors += 1
                    
                    # 更新进度
                    self.update_progress(success)
                    
                    # 标记任务完成
                    self.task_queue.task_done()
                    
                except Exception as e:
                    consecutive_errors += 1
                    consecutive_success = 0
                    self.log_callback(f"工作线程 {worker_id} 处理任务 {task_id} 时出错: {str(e)}")
                    self.update_progress(False)
                    self.task_queue.task_done()
                
                # 智能调整等待时间
                if self.is_running:
                    if consecutive_errors >= 3:
                        # 连续多次失败，延长等待时间
                        delay = random.uniform(3.0, 5.0)
                        self.log_callback(f"工作线程 {worker_id} 连续失败 {consecutive_errors} 次，等待 {delay:.1f} 秒")
                    elif consecutive_errors > 0:
                        # 有失败记录，适当延长等待
                        delay = random.uniform(1.5, 3.0)
                    elif consecutive_success > 5:
                        # 连续多次成功，可以加快速度
                        delay = random.uniform(0.3, 0.8)
                    else:
                        # 正常情况下的等待时间
                        delay = random.uniform(0.5, 1.5)
                    
                    time.sleep(delay)
                    
            except queue.Empty:
                # 队列为空，检查是否应该退出
                if self.completed >= self.total_accounts or not self.is_running:
                    break
                # 队列暂时为空，继续等待
                continue
            except Exception as e:
                self.log_callback(f"工作线程 {worker_id} 遇到未处理异常: {str(e)}")
                # 发生异常，暂停一会
                time.sleep(2)
                # 如果有任务ID，确保标记完成
                if task_id is not None:
                    try:
                        self.task_queue.task_done()
                    except:
                        pass
        
        self.log_callback(f"工作线程 {worker_id} 结束")
                
    def multi_register(self):
        """多线程注册 - 优化版本，提高效率和稳定性"""
        self.log_callback(f"开始多线程注册，线程数：{self.thread_count}，计划注册账号数：{self.total_accounts}")
        
        # 使用所有指定的线程，但不超过任务数
        actual_threads = min(self.thread_count, self.total_accounts)
        self.log_callback(f"实际使用线程数：{actual_threads}")
        
        # 重置状态
        self.is_running = True
        self.completed = 0
        self.successful = 0
        self.last_progress_time = time.time()
        self.worker_threads = []
        
        try:
            # 清空原有队列
            while not self.task_queue.empty():
                try:
                    self.task_queue.get_nowait()
                    self.task_queue.task_done()
                except:
                    break
                    
            # 分批添加任务到队列
            if self.is_running:
                batch_size = min(100, self.total_accounts)  # 批处理大小，避免一次性添加过多任务
                self.log_callback(f"开始添加注册任务到队列，批次大小: {batch_size}")
                
                for i in range(0, self.total_accounts, batch_size):
                    end = min(i + batch_size, self.total_accounts)
                    for j in range(i, end):
                        self.task_queue.put(j)
                    
                    # 检查是否应该继续
                    if not self.is_running:
                        self.log_callback("收到停止信号，中止添加任务")
                        break
                        
                    # 在添加大量任务时，分批添加并短暂暂停，减轻系统压力
                    if self.total_accounts > 100 and end < self.total_accounts:
                        time.sleep(0.1)
                    
                self.log_callback(f"已将 {self.total_accounts} 个注册任务加入队列")
            
            # 创建工作线程
            for i in range(actual_threads):
                if not self.is_running:
                    break
                    
                t = threading.Thread(target=self.worker_thread, args=(i,))
                t.daemon = True
                self.worker_threads.append(t)
                t.start()
                
                # 错开线程启动时间
                if i < actual_threads - 1:
                    time.sleep(0.2)
            
            # 等待所有任务完成或停止信号
            last_monitor_time = time.time()
            last_queue_size = self.task_queue.qsize()
            stalled_count = 0
            
            while not self.task_queue.empty() and self.is_running and self.completed < self.total_accounts:
                # 定期检查状态
                time.sleep(0.5)
                
                # 每15秒监控一次进度，检测是否卡住
                current_time = time.time()
                if current_time - last_monitor_time >= 15:
                    current_queue_size = self.task_queue.qsize()
                    remaining = self.total_accounts - self.completed
                    
                    # 输出当前状态
                    self.log_callback(f"监控: 已完成 {self.completed}/{self.total_accounts}, 成功 {self.successful}, 队列剩余 {current_queue_size}, 待处理 {remaining}")
                    
                    # 检测是否卡住（队列大小长时间不变但仍有任务）
                    if current_queue_size == last_queue_size and current_queue_size > 0:
                        stalled_count += 1
                        if stalled_count >= 2:  # 连续多次检测到卡住
                            self.log_callback(f"警告: 检测到队列处理可能卡住，尝试恢复")
                            
                            # 尝试添加额外工作线程解决卡顿
                            if len(self.worker_threads) < actual_threads + 3:  # 最多添加3个额外线程
                                new_worker_id = len(self.worker_threads)
                                self.log_callback(f"添加额外工作线程 {new_worker_id} 以解决卡顿")
                                
                                t = threading.Thread(target=self.worker_thread, args=(new_worker_id,))
                                t.daemon = True
                                self.worker_threads.append(t)
                                t.start()
                            
                            stalled_count = 0  # 重置卡住计数
                    else:
                        stalled_count = 0  # 队列大小有变化，重置卡住计数
                    
                    last_queue_size = current_queue_size
                    last_monitor_time = current_time
                
            # 检查完成情况
            if self.completed >= self.total_accounts:
                self.log_callback("所有注册任务已完成")
            elif not self.is_running:
                self.log_callback("注册过程被手动停止")
                
        except Exception as e:
            self.log_callback(f"多线程注册过程中发生错误: {str(e)}")
        finally:
            # 设置停止标志
            self.is_running = False
            
            # 清空剩余任务队列
            remaining_tasks = 0
            try:
                while not self.task_queue.empty():
                    try:
                        self.task_queue.get_nowait()
                        self.task_queue.task_done()
                        remaining_tasks += 1
                    except:
                        break
            except:
                pass
                
            if remaining_tasks > 0:
                self.log_callback(f"已清除剩余 {remaining_tasks} 个未处理的任务")
            
            # 添加终止信号让所有工作线程退出
            for _ in range(len(self.worker_threads) + 5):
                try:
                    self.task_queue.put(None, block=False)
                except:
                    break
                    
            # 等待所有线程完成
            for t in self.worker_threads:
                if t.is_alive():
                    t.join(0.5)  # 等待最多0.5秒
            
            # 检查未正常结束的线程
            active_threads = [t for t in self.worker_threads if t.is_alive()]
            if active_threads:
                self.log_callback(f"有 {len(active_threads)} 个工作线程未正常结束，将在后台结束")
            
            success_rate = (self.successful / self.completed * 100) if self.completed > 0 else 0
            self.log_callback(f"注册过程完成，总计: {self.total_accounts}，已完成: {self.completed}，成功: {self.successful}，成功率: {success_rate:.1f}%")


class EmailRegistrationApp(QMainWindow):
    """邮箱注册应用界面"""
    
    def __init__(self):
        super().__init__()
        self.register_thread = None
        self.proxy_timer = None
        self.initUI()
        # 初始化代理管理器但不启用
        self.proxy_manager = None
        
    def initUI(self):
        """初始化界面"""
        self.setWindowTitle('邮箱自动注册工具')
        self.setGeometry(100, 100, 800, 600)
        
        # 主布局
        main_widget = QWidget()
        main_layout = QVBoxLayout()
        main_widget.setLayout(main_layout)
        self.setCentralWidget(main_widget)
        
        # 参数设置区域
        param_group = QGroupBox('参数设置')
        param_layout = QGridLayout()
        param_group.setLayout(param_layout)
        
        # 线程数
        param_layout.addWidget(QLabel('线程数:'), 0, 0)
        self.thread_spin = QSpinBox()
        self.thread_spin.setRange(1, 20)
        self.thread_spin.setValue(5)
        param_layout.addWidget(self.thread_spin, 0, 1)
        
        # 注册数量
        param_layout.addWidget(QLabel('注册数量:'), 0, 2)
        self.accounts_spin = QSpinBox()
        self.accounts_spin.setRange(1, 1000)
        self.accounts_spin.setValue(5)  # 默认设置为5，与线程数相同
        param_layout.addWidget(self.accounts_spin, 0, 3)
        
        # 用户名长度
        param_layout.addWidget(QLabel('用户名长度:'), 1, 0)
        self.username_spin = QSpinBox()
        self.username_spin.setRange(5, 16)
        self.username_spin.setValue(8)
        param_layout.addWidget(self.username_spin, 1, 1)
        
        # 密码长度
        param_layout.addWidget(QLabel('密码长度:'), 1, 2)
        self.password_spin = QSpinBox()
        self.password_spin.setRange(6, 20)
        self.password_spin.setValue(10)
        param_layout.addWidget(self.password_spin, 1, 3)
        
        # 代理设置
        param_layout.addWidget(QLabel('使用代理:'), 2, 0)
        self.proxy_checkbox = QCheckBox()
        self.proxy_checkbox.setChecked(False)
        self.proxy_checkbox.toggled.connect(self.toggle_proxy)
        param_layout.addWidget(self.proxy_checkbox, 2, 1)
        
        # 直连模式选项
        self.direct_checkbox = QCheckBox("直连模式")
        self.direct_checkbox.setChecked(False)
        self.direct_checkbox.toggled.connect(self.toggle_direct_mode)
        self.direct_checkbox.setEnabled(False)
        param_layout.addWidget(self.direct_checkbox, 2, 2)
        
        # 代理API URL输入框
        param_layout.addWidget(QLabel('代理API:'), 3, 0)
        self.proxy_api_input = QLineEdit("")
        param_layout.addWidget(self.proxy_api_input, 3, 1, 1, 2)
        
        # 刷新代理按钮
        self.refresh_proxy_button = QPushButton('刷新代理IP')
        self.refresh_proxy_button.clicked.connect(self.refresh_proxy)
        self.refresh_proxy_button.setEnabled(False)
        param_layout.addWidget(self.refresh_proxy_button, 3, 3)
        
        # 测试代理按钮
        self.test_proxy_button = QPushButton('测试代理')
        self.test_proxy_button.clicked.connect(self.test_proxy)
        self.test_proxy_button.setEnabled(False)
        param_layout.addWidget(self.test_proxy_button, 2, 3)
        
        main_layout.addWidget(param_group)
        
        # 操作按钮区域
        button_layout = QHBoxLayout()
        
        # 开始按钮
        self.start_button = QPushButton('开始注册')
        self.start_button.clicked.connect(self.start_registration)
        button_layout.addWidget(self.start_button)
        
        # 停止按钮
        self.stop_button = QPushButton('停止注册')
        self.stop_button.clicked.connect(self.stop_registration)
        self.stop_button.setEnabled(False)
        button_layout.addWidget(self.stop_button)
        
        # 清空日志按钮
        self.clear_button = QPushButton('清空日志')
        self.clear_button.clicked.connect(self.clear_log)
        button_layout.addWidget(self.clear_button)
        
        main_layout.addLayout(button_layout)
        
        # 进度条
        progress_layout = QHBoxLayout()
        progress_layout.addWidget(QLabel('进度:'))
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        progress_layout.addWidget(self.progress_bar)
        
        # 成功数显示
        self.success_label = QLabel('成功: 0')
        progress_layout.addWidget(self.success_label)
        
        main_layout.addLayout(progress_layout)
        
        # 日志区域
        log_group = QGroupBox('注册日志')
        log_layout = QVBoxLayout()
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        log_group.setLayout(log_layout)
        main_layout.addWidget(log_group)
        
    def toggle_proxy(self, is_checked):
        """切换代理设置"""
        global proxy_manager
        
        self.refresh_proxy_button.setEnabled(is_checked)
        self.test_proxy_button.setEnabled(is_checked)
        self.direct_checkbox.setEnabled(is_checked)
        
        if is_checked:
            # 获取用户提供的API URL
            api_url = self.proxy_api_input.text().strip()
            if not api_url:
                api_url = ""
                self.proxy_api_input.setText(api_url)
                
            # 启用代理
            self.update_log(f"正在启用代理功能，API: {api_url}")
            if not proxy_manager:
                proxy_manager = setup_proxy_manager(self.update_log)
                
            # 设置API URL
            proxy_manager.set_api_url(api_url)
            
            # 重置直连模式状态
            proxy_manager.toggle_direct_mode(False)
            self.direct_checkbox.setChecked(False)
            
            # 立即获取代理
            proxy = proxy_manager.get_proxy(force_update=True)
            if proxy:
                self.update_log(f"代理功能已启用，当前IP: {proxy}，将每30分钟自动更换")
            else:
                self.update_log("代理功能已启用，但当前无可用IP，将使用直连")
                self.direct_checkbox.setChecked(True)
            
            # 设置定时器每30分钟更新一次代理
            if not self.proxy_timer:
                self.proxy_timer = QTimer(self)
                self.proxy_timer.timeout.connect(self.refresh_proxy)
            
            self.proxy_timer.start(30 * 60 * 1000)  # 30分钟 = 1800000毫秒
        else:
            # 禁用代理
            proxy_manager = None
            
            # 停止定时器
            if self.proxy_timer and self.proxy_timer.isActive():
                self.proxy_timer.stop()
            
            self.update_log("代理功能已禁用")
            self.direct_checkbox.setChecked(False)
    
    def toggle_direct_mode(self, is_direct):
        """切换直连模式"""
        global proxy_manager
        
        if proxy_manager:
            proxy_manager.toggle_direct_mode(is_direct)
            
            if is_direct:
                self.update_log("已切换到直连模式，不使用代理IP")
            else:
                self.update_log("已切换到代理模式，将尝试使用代理IP")
                # 刷新代理
                self.refresh_proxy()
    
    def test_proxy(self):
        """测试当前代理"""
        global proxy_manager
        
        if not proxy_manager:
            self.update_log("代理功能未启用，无法测试")
            return
            
        if proxy_manager.direct_mode:
            self.update_log("当前为直连模式，无需测试代理")
            return
            
        proxy = proxy_manager.current_proxy
        if not proxy:
            self.update_log("当前无可用代理，请先刷新代理IP")
            return
            
        self.update_log(f"正在测试代理 {proxy} 连接性...")
        if proxy_manager.test_proxy(proxy):
            self.update_log(f"代理 {proxy} 测试成功，可正常连接")
        else:
            self.update_log(f"代理 {proxy} 测试失败，已标记为失败状态")
            # 自动尝试获取新的代理
            self.update_log("尝试获取新的代理IP...")
            self.refresh_proxy()
            
    def refresh_proxy(self):
        """手动刷新代理IP"""
        global proxy_manager
        
        if proxy_manager:
            # 获取最新的API URL
            api_url = self.proxy_api_input.text().strip()
            if api_url:
                proxy_manager.set_api_url(api_url)
            
            # 如果是直连模式，询问是否切换回代理模式
            if proxy_manager.direct_mode:
                self.update_log("当前为直连模式，将尝试切换回代理模式")
                proxy_manager.toggle_direct_mode(False)
                self.direct_checkbox.setChecked(False)
                
            self.update_log("手动刷新代理IP...")
            
            # 尝试获取有效代理，最多尝试3次
            max_attempts = 3
            for attempt in range(max_attempts):
                proxy = proxy_manager.get_proxy(force_update=True)
                if not proxy:
                    self.update_log("获取代理IP失败，已切换到直连模式")
                    self.direct_checkbox.setChecked(True)
                    break
                    
                self.update_log(f"成功获取新代理IP: {proxy}")
                # 测试新代理
                if proxy_manager.test_proxy(proxy):
                    self.update_log(f"代理 {proxy} 测试成功，可正常连接")
                    break
                else:
                    # 如果不是最后一次尝试，则继续
                    if attempt < max_attempts - 1:
                        self.update_log(f"代理 {proxy} 测试失败，尝试获取新代理...")
                    else:
                        self.update_log(f"连续 {max_attempts} 次代理测试失败，切换到直连模式")
                        proxy_manager.toggle_direct_mode(True)
                        self.direct_checkbox.setChecked(True)
    
    def update_log(self, message):
        """更新日志"""
        self.log_text.append(message)
        # 滚动到底部
        self.log_text.verticalScrollBar().setValue(
            self.log_text.verticalScrollBar().maximum()
        )
        
    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        
    def update_progress(self, completed, successful):
        """更新进度"""
        total = self.accounts_spin.value()
        percentage = int((completed / total) * 100) if total > 0 else 0
        self.progress_bar.setValue(percentage)
        self.success_label.setText(f'成功: {successful}')
        
    def start_registration(self):
        """开始注册"""
        if self.register_thread and self.register_thread.isRunning():
            return
            
        # 获取参数
        thread_count = self.thread_spin.value()
        total_accounts = self.accounts_spin.value()
        
        # 移除自动调整注册数量的限制
        # if total_accounts != thread_count:
        #     self.accounts_spin.setValue(thread_count)
        #     total_accounts = thread_count
        #     self.update_log(f"已将注册数量自动调整为与线程数相同: {thread_count}")
            
        username_length = self.username_spin.value()
        password_length = self.password_spin.value()
        
        # 创建并启动线程
        self.register_thread = RegisterThread(
            thread_count,
            total_accounts,
            username_length,
            password_length
        )
        
        # 连接信号槽
        self.register_thread.update_signal.connect(self.update_log)
        self.register_thread.progress_signal.connect(self.update_progress)
        self.register_thread.finished_signal.connect(self.registration_finished)
        
        # 开始线程
        self.register_thread.start()
        
        # 更新UI状态
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.progress_bar.setValue(0)  # 重置进度条
        self.success_label.setText('成功: 0')  # 重置成功数量显示
        
        # 显示注册任务信息
        self.update_log(f"开始注册任务: 线程数={thread_count}, 总数量={total_accounts}, 用户名长度={username_length}, 密码长度={password_length}")
        
    def stop_registration(self):
        """停止注册"""
        if self.register_thread and self.register_thread.isRunning():
            self.update_log("正在停止注册过程...")
            self.stop_button.setEnabled(False)
            
            # 停止注册线程
            self.register_thread.stop()
            
            # 创建一个计时器来检查线程是否已经结束
            self.stop_timer = QTimer(self)
            self.stop_timer.timeout.connect(self.check_thread_stopped)
            self.stop_timer.start(500)  # 每500ms检查一次
            
    def check_thread_stopped(self):
        """检查线程是否已经停止"""
        if not self.register_thread.isRunning():
            self.stop_timer.stop()
            self.registration_finished()
            self.update_log("注册过程已停止")
        elif hasattr(self, 'stop_count'):
            self.stop_count += 1
            # 如果超过10秒(20次检查)线程仍未结束，尝试终止线程
            if self.stop_count > 20:
                self.stop_timer.stop()
                self.register_thread.terminate()
                self.registration_finished()
                self.update_log("强制终止注册过程")
        else:
            self.stop_count = 1
        
    def registration_finished(self):
        """注册完成"""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.update_log("注册过程完成")


# 运行示例
if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = EmailRegistrationApp()
    window.show()
    sys.exit(app.exec())
