<!DOCTYPE html>
<html lang="zh">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{$title}</title>
        {if !empty($favicon)}
        <link rel="shortcut icon" href="{$favicon}" type="image/x-icon">
        {/if}
        <link rel="stylesheet" href="/assets/plugin/Blackglod/plugin/Blackglod/css/all.min.css">
        <link rel="stylesheet" href="/static/others/element-plus/index.css">
        <style>
        @font-face {
            font-family: 'Noto Sans SC';
            src: url('/assets/plugin/Blackglod/plugin/Blackglod/fonts/k3kCo84MPvpLmixcA63oeAL7Iqp5IZJF9bmaG-3FnYw.ttf') format('truetype');
            font-weight: 400;
            font-style: normal;
        }
        @font-face {
            font-family: 'Noto Sans SC';
            src: url('/assets/plugin/Blackglod/plugin/Blackglod/fonts/k3kCo84MPvpLmixcA63oeAL7Iqp5IZJF9bmaG9_FnYw.ttf') format('truetype');
            font-weight: 500;
            font-style: normal;
        }
        @font-face {
            font-family: 'Noto Sans SC';
            src: url('/assets/plugin/Blackglod/plugin/Blackglod/fonts/k3kCo84MPvpLmixcA63oeAL7Iqp5IZJF9bmaGzjCnYw.ttf') format('truetype');
            font-weight: 700;
            font-style: normal;
        }

        :root {
            --primary: #C8A675;
            --primary-dark: #a88a5c;
            --primary-light: #e0c4a3;
            --dark: #000000;
            --light: #ffffff;
            --gray: #666666;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: rgba(0, 0, 0, 0.85);
            color: var(--light);
            line-height: 1.6;
            overflow-x: hidden;
            min-height: 100vh;
            position: relative;
        }

        .background-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
            opacity: 0.1;
            pointer-events: none;
        }

        .stars-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
            pointer-events: none;
        }

        .container {
            width: 90%;
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
            z-index: 2;
        }

        .header {
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            transition: var(--transition);
            will-change: transform, opacity;
            transform: translateZ(0);
        }

        .search-section {
            padding-top: 120px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
            z-index: 2;
        }

        .search-container {
            position: relative;
            z-index: 2;
            padding: 2rem 0;
        }

        .search-box {
            background: rgba(0, 0, 0, 0.6);
            border-radius: 16px;
            padding: 30px;
            margin-top: 2rem;
            border: 1px solid rgba(200, 166, 117, 0.1);
            backdrop-filter: blur(10px);
            position: relative;
            z-index: 2;
        }

        .search-title {
            font-size: 2.8rem;
            color: var(--primary);
            margin-bottom: 1rem;
            text-align: center;
            position: relative;
            z-index: 2;
        }

        .search-subtitle {
            font-size: 1.2rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 2rem;
            position: relative;
            z-index: 2;
        }

        .order-list {
            margin-top: 2rem;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 16px;
            padding: 20px;
            border: 1px solid rgba(200, 166, 117, 0.1);
            position: relative;
            z-index: 2;
        }

        .footer {
            position: relative;
            z-index: 2;
            margin-top: 4rem;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
        }

        @media (max-width: 768px) {
            .search-section {
                padding-top: 80px;
            }

            .search-title {
                font-size: 2rem;
            }

            .search-subtitle {
                font-size: 1rem;
            }

            .search-box {
                padding: 20px;
            }
        }

        .modal {
            position: fixed;
            z-index: 1100;
        }

        .input-group {
            position: relative;
            z-index: 2;
            display: flex;
            gap: 1rem;
            background: rgba(255, 255, 255, 0.05);
            padding: 10px;
            border-radius: 8px;
            border: 1px solid rgba(200, 166, 117, 0.2);
        }

        .search-input {
            flex: 1;
            background: transparent;
            border: none;
            color: var(--light);
            padding: 10px;
            font-size: 1rem;
        }

        .search-btn {
            background: var(--primary);
            color: var(--dark);
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            transition: var(--transition);
        }

        .search-btn:hover {
            background: var(--primary-dark);
        }

        .notice-text {
            margin-top: 1rem;
            color: var(--gray);
            font-size: 0.9rem;
            text-align: center;
            position: relative;
            z-index: 2;
        }

        .header {
            padding: 1rem 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            transition: var(--transition);
            will-change: transform, opacity;
            transform: translateZ(0);
        }

        .header.scrolled {
            padding: 0.5rem 0;
            background: rgba(0, 0, 0, 0.95);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
        }

        .header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 2rem;
            position: relative;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            color: var(--light);
            text-decoration: none;
            min-width: 200px;
        }

        .logo-img {
            height: 36px;
            width: auto;
        }

        .logo span {
            font-size: 1.4rem;
            font-weight: 500;
            background: linear-gradient(135deg, var(--light), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .nav-links {
            display: flex;
            gap: 1rem; /* 减小间距 */
            margin-left: auto;
            flex-wrap: nowrap; /* 防止换行 */
            align-items: center;
        }

        .nav-item {
            position: relative;
            white-space: nowrap; /* 防止文字换行 */
        }

        .nav-item > a {
            color: var(--light);
            text-decoration: none;
            font-size: 0.85rem; /* 稍微减小字体 */
            padding: 0.4rem 0.6rem; /* 减小内边距 */
            border-radius: 4px;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 2px;
        }

        .nav-item > a:hover {
            color: var(--primary);
            background: rgba(200, 166, 117, 0.1);
        }

        .dropdown-arrow {
            transition: var(--transition);
            margin-left: 2px;
        }

        .nav-item:hover .dropdown-arrow {
            transform: translateY(2px);
        }

        .submenu {
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translate3d(-50%, 10px, 0);
            background: rgba(0, 0, 0, 0.95);
            backdrop-filter: blur(10px);
            min-width: 160px;
            border-radius: 8px;
            padding: 0.5rem;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(200, 166, 117, 0.1);
            will-change: transform, opacity;
        }

        .nav-item:hover .submenu {
            opacity: 1;
            visibility: visible;
            transform: translate3d(-50%, 0, 0);
        }

        .submenu a {
            color: var(--light);
            text-decoration: none;
            padding: 0.6rem 1rem;
            display: block;
            font-size: 0.9rem;
            border-radius: 4px;
            transition: var(--transition);
        }

        .submenu a:hover {
            background: rgba(200, 166, 117, 0.1);
            color: var(--primary);
        }

        .auth-buttons {
            display: flex;
            gap: 1rem;
            margin-left: 2rem;
        }

        .auth-buttons .btn {
            padding: 0.5rem 1.2rem;
            border-radius: 4px;
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .auth-icon {
            transition: var(--transition);
        }

        .btn-login {
            color: var(--primary);
            border: 1px solid var(--primary);
            background: transparent;
        }

        .btn-login:hover {
            background: rgba(200, 166, 117, 0.1);
        }

        .btn-login:hover .auth-icon {
            transform: scale(1.1);
        }

        .btn-register {
            color: var(--dark);
            background: var(--primary);
            border: 1px solid var(--primary);
        }

        .btn-register .auth-icon {
            color: var(--dark);
        }

        .btn-register:hover {
            background: var(--primary-dark);
            border-color: var(--primary-dark);
        }

        .btn-register:hover .auth-icon {
            transform: scale(1.1);
        }

        @media (max-width: 768px) {
            .nav-links, .auth-buttons {
                display: none;
            }
        }

        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
            padding: 6rem 0;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                linear-gradient(45deg, rgba(0,0,0,0.7), transparent),
                radial-gradient(circle at center, transparent, rgba(0,0,0,0.8));
            z-index: 1;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            text-align: center;
            animation: fadeInUp 1s ease-out;
        }

        .hero h1 {
            font-size: 4rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--light), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: gradientFlow 8s linear infinite;
        }

        @keyframes gradientFlow {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .hero h2 {
            font-size: 2.5rem;
            margin-bottom: 2rem;
            background: linear-gradient(135deg, var(--light) 10%, var(--primary) 50%, var(--light) 90%);
            background-size: 200% auto;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: shine 3s linear infinite;
            text-shadow: 0 0 10px rgba(200, 166, 117, 0.3);
            position: relative;
        }

        @keyframes shine {
            0% {
                background-position: 200% center;
            }
            100% {
                background-position: -200% center;
            }
        }

        .hero h2::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, 
                transparent,
                rgba(255, 255, 255, 0.4),
                transparent
            );
            animation: lightPass 2s ease-in-out infinite;
            transform: skewX(-20deg);
        }

        @keyframes lightPass {
            0% {
                transform: translateX(-100%) skewX(-20deg);
            }
            100% {
                transform: translateX(200%) skewX(-20deg);
            }
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 4rem;
        }

        .stat-item {
            background: rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(200, 166, 117, 0.2);
            border-radius: 20px;
            padding: 2.5rem 2rem;
            text-align: center;
            position: relative;
            overflow: hidden;
            transition: all 0.4s ease;
            backdrop-filter: blur(10px);
        }

        .stat-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                90deg,
                transparent,
                rgba(200, 166, 117, 0.2),
                transparent
            );
            transition: 0.5s;
        }

        .stat-item:hover::before {
            left: 100%;
        }

        .stat-item:hover {
            transform: translateY(-10px);
            border-color: var(--primary);
            box-shadow: 0 10px 30px rgba(200, 166, 117, 0.1);
        }

        .stat-number {
            font-size: 2.8rem;
            font-weight: 700;
            background: linear-gradient(135deg, #ffd700, #c8a675);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
            font-family: 'Arial', sans-serif;
            position: relative;
            display: inline-block;
        }

        .stat-number::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 2px;
            background: var(--primary);
            border-radius: 2px;
        }

        .stat-item div:last-child {
            color: #999;
            font-size: 1.1rem;
            margin-top: 0.5rem;
            letter-spacing: 1px;
        }

        @keyframes countUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .stat-number.visible {
            animation: countUp 1.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        }

        .features {
            padding: 8rem 0;
            background: linear-gradient(to bottom, transparent, rgba(0,0,0,0.8));
            position: relative;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .feature-item {
            padding: 2.5rem;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(200, 166, 117, 0.1);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .feature-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, transparent, rgba(200, 166, 117, 0.1));
            opacity: 0;
            transition: var(--transition);
        }

        .feature-item:hover {
            transform: translateY(-10px);
            border-color: var(--primary);
        }

        .feature-item:hover::before {
            opacity: 1;
        }

        .feature-icon {
            font-size: 3rem;
            color: var(--primary);
            margin-bottom: 1.5rem;
            transition: var(--transition);
        }

        .feature-item:hover .feature-icon {
            transform: scale(1.2) rotate(10deg);
        }

        .steps {
            padding: 8rem 0;
            background: linear-gradient(to top, transparent, rgba(0,0,0,0.8));
            position: relative;
            overflow: hidden;
        }

        .steps::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(30deg, #000000 12%, transparent 12.5%, transparent 87%, #000000 87.5%, #000000),
                linear-gradient(150deg, #000000 12%, transparent 12.5%, transparent 87%, #000000 87.5%, #000000),
                linear-gradient(30deg, #000000 12%, transparent 12.5%, transparent 87%, #000000 87.5%, #000000),
                linear-gradient(150deg, #000000 12%, transparent 12.5%, transparent 87%, #000000 87.5%, #000000),
                linear-gradient(60deg, rgba(200,166,117,0.1) 25%, transparent 25.5%, transparent 75%, rgba(200,166,117,0.1) 75%, rgba(200,166,117,0.1)),
                linear-gradient(60deg, rgba(200,166,117,0.1) 25%, transparent 25.5%, transparent 75%, rgba(200,166,117,0.1) 75%, rgba(200,166,117,0.1));
            background-size: 40px 70px;
            background-position: 0 0, 0 0, 20px 35px, 20px 35px, 0 0, 20px 35px;
            opacity: 0.1;
            z-index: 0;
        }

        .steps::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 50% 50%, 
                rgba(200,166,117,0.1),
                transparent 60%);
            animation: pulseGlow 4s ease-in-out infinite;
            z-index: 1;
        }

        @keyframes pulseGlow {
            0%, 100% {
                opacity: 0.3;
                transform: scale(1);
            }
            50% {
                opacity: 0.5;
                transform: scale(1.2);
            }
        }

        .steps .container {
            position: relative;
            z-index: 2;
        }

        .steps .section-title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--primary-light), var(--primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .steps .section-subtitle {
            text-align: center;
            color: var(--gray);
            margin-bottom: 4rem;
            font-size: 1.1rem;
        }

        .steps-container {
            display: flex;
            justify-content: center;
            gap: 4rem;
            position: relative;
        }

        .steps-container::before {
            content: '';
            position: absolute;
            top: 60px;
            left: calc(16.666% + 60px);
            right: calc(16.666% + 60px);
            height: 2px;
            background: linear-gradient(90deg,
                transparent,
                var(--primary) 20%,
                var(--primary) 80%,
                transparent
            );
            opacity: 0.3;
        }

        .step {
            flex: 1;
            max-width: 280px;
            text-align: center;
            position: relative;
        }

        .step-circle {
            width: 120px;
            height: 120px;
            margin: 0 auto 2rem;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .step-number {
            position: absolute;
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary);
            z-index: 2;
            transition: var(--transition);
            opacity: 1;
        }

        .step-icon {
            position: absolute;
            font-size: 2.5rem;
            color: var(--primary);
            z-index: 2;
            opacity: 0;
            transform: scale(0.5);
            transition: var(--transition);
        }

        .progress-ring {
            position: absolute;
            transform: rotate(-90deg);
        }

        .progress-ring-circle {
            fill: transparent;
            stroke: var(--primary);
            stroke-width: 2;
            stroke-dasharray: 339.292;
            stroke-dashoffset: 339.292;
            transition: var(--transition);
        }

        .step:hover .progress-ring-circle {
            stroke-dashoffset: 0;
        }

        .step:hover .step-number {
            opacity: 0;
            transform: scale(0.5);
        }

        .step:hover .step-icon {
            opacity: 1;
            transform: scale(1);
        }

        .step h3 {
            color: var(--primary);
            font-size: 1.3rem;
            margin-bottom: 1rem;
            transition: var(--transition);
        }

        .step p {
            color: var(--gray);
            font-size: 1rem;
            line-height: 1.6;
            transition: var(--transition);
        }

        .step:hover h3 {
            transform: translateY(-5px);
            color: var(--primary-light);
        }

        .step:hover p {
            color: var(--light);
        }

        @media (max-width: 768px) {
            .steps {
                padding: 4rem 0;
            }

            .steps-container {
                flex-direction: column;
                align-items: center;
                gap: 3rem;
            }

            .steps-container::before {
                display: none;
            }

            .step {
                max-width: 100%;
            }

            .steps .section-title {
                font-size: 2rem;
            }

            .steps .section-subtitle {
                font-size: 1rem;
                margin-bottom: 3rem;
            }
        }

        .footer {
            background: linear-gradient(to top, rgba(0, 0, 0, 0.95), rgba(0, 0, 0, 0.8));
            padding: 4rem 0 2rem;
            position: relative;
            border-top: 1px solid rgba(200, 166, 117, 0.1);
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, 
                transparent,
                var(--primary),
                transparent
            );
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 3rem;
            margin-bottom: 3rem;
        }

        .footer-column {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .footer-column h3 {
            color: var(--primary);
            font-size: 1.2rem;
            margin-bottom: 1rem;
            position: relative;
            padding-bottom: 0.5rem;
        }

        .footer-column h3::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 30px;
            height: 2px;
            background: var(--primary);
            border-radius: 2px;
        }

        .footer-column a {
            color: var(--gray);
            text-decoration: none;
            transition: var(--transition);
            font-size: 0.95rem;
            display: inline-block;
            position: relative;
            padding-left: 1.2rem;
        }

        .footer-column a::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: var(--primary);
            transform: translateY(-50%) scale(0.6);
            opacity: 0.5;
            transition: var(--transition);
        }

        .footer-column a:hover {
            color: var(--primary-light);
            transform: translateX(5px);
        }

        .footer-column a:hover::before {
            transform: translateY(-50%) scale(1);
            opacity: 1;
        }

        .copyright {
            text-align: center;
            color: var(--gray);
            padding-top: 2rem;
            border-top: 1px solid rgba(200, 166, 117, 0.1);
            font-size: 0.9rem;
        }

        .copyright a {
            color: var(--primary);
            text-decoration: none;
            transition: var(--transition);
            margin: 0 0.5rem;
        }

        .copyright a:hover {
            color: var(--primary-light);
        }

        @media (max-width: 768px) {
            .footer {
                padding: 3rem 0 1.5rem;
            }

            .footer-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 2rem;
            }

            .copyright {
                flex-direction: column;
                gap: 0.5rem;
            }
        }

        @media (max-width: 480px) {
            .footer-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 移动端菜单按钮 */
        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            color: var(--light);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
        }

        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: block;
            }
        }

    /* 增强星星背景效果 */
    .stars-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        z-index: -1;
        pointer-events: none;
        background: radial-gradient(circle at center, rgba(0,0,0,0.5), rgba(0,0,0,0.8));
    }

    /* 金色星星样式 */
    .star {
        position: absolute;
        background: linear-gradient(135deg, #ffd700, #c8a675);
        border-radius: 50%;
        filter: blur(1px);
        box-shadow: 
            0 0 4px #ffd700,
            0 0 8px #ffd700,
            0 0 12px #c8a675;
        opacity: 0;
        animation: twinkle var(--duration) ease-in-out infinite;
        animation-delay: var(--delay);
        will-change: transform, opacity;
        z-index: 1;
    }

    /* 星星闪烁动画 */
    @keyframes twinkle {
        0%, 100% {
            opacity: 0;
            transform: scale(0.3);
            filter: blur(1px);
        }
        50% {
            opacity: var(--opacity);
            transform: scale(1.2);
            filter: blur(0.5px);
        }
    }

    /* 流星效果 */
    .shooting-star {
        position: absolute;
        width: 200px;
        height: 3px;
        background: linear-gradient(90deg, 
            rgba(255, 215, 0, 1), 
            rgba(255, 215, 0, 0.8),
            rgba(200, 166, 117, 0.4), 
            transparent
        );
        opacity: 0;
        filter: blur(0.5px);
        transform: rotate(-45deg);
        box-shadow: 
            0 0 20px rgba(255, 215, 0, 0.8),
            0 0 30px rgba(255, 215, 0, 0.6);
        animation: shoot 3s ease-in infinite;
        animation-delay: var(--delay);
        will-change: transform, opacity;
    }

    @keyframes shoot {
        0% {
            opacity: 0;
            transform: translateX(-100%) translateY(0) rotate(-45deg);
        }
        10% {
            opacity: 1;
        }
        20%, 100% {
            opacity: 0;
            transform: translateX(100vw) translateY(100vh) rotate(-45deg);
        }
    }

    /* 金额数字的响应式字体大小 */
    .stat-number.amount {
        font-size: 2.8rem;
    }

    .stat-number.amount.length-9 {
        font-size: 2.6rem;
    }

    .stat-number.amount.length-10 {
        font-size: 2.4rem;
    }

    .stat-number.amount.length-11 {
        font-size: 2.2rem;
    }

    .stat-number.amount.length-12 {
        font-size: 2rem;
    }

    /* 移动端适配 */
    @media (max-width: 768px) {
        .stat-number.amount {
            font-size: 2.2rem;
        }
        
        .stat-number.amount.length-9 {
            font-size: 2rem;
        }
        
        .stat-number.amount.length-10 {
            font-size: 1.8rem;
        }
        
        .stat-number.amount.length-11 {
            font-size: 1.6rem;
        }
        
        .stat-number.amount.length-12 {
            font-size: 1.4rem;
        }
    }

    .payment-icons {
        padding: 6rem 0;
        background: rgba(0, 0, 0, 0.3);
        border-bottom: 1px solid rgba(200, 166, 117, 0.1);
        position: relative;
        overflow: hidden;
    }

    .payment-icons::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, 
            transparent,
            var(--primary),
            transparent
        );
    }

    .section-title {
        text-align: center;
        color: var(--primary);
        font-size: 2.2rem;
        margin-bottom: 1rem;
        background: linear-gradient(135deg, var(--primary-light), var(--primary));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .section-subtitle {
        text-align: center;
        color: var(--gray);
        margin-bottom: 3rem;
        font-size: 1.1rem;
    }

    .icons-grid {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: 3rem;
        align-items: center;
        justify-items: center;
        padding: 2rem 0;
    }

    .icon-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1.2rem;
        position: relative;
    }

    .icon-wrapper {
        position: relative;
        padding: 1rem;
    }

    .payment-icon {
        width: 70px;
        height: 70px;
        color: var(--primary);
        transition: var(--transition);
        position: relative;
        z-index: 2;
    }

    .icon-glow {
        position: absolute;
        inset: 0;
        background: radial-gradient(circle at center, 
            rgba(200, 166, 117, 0.2),
            transparent 70%
        );
        opacity: 0;
        transition: var(--transition);
        filter: blur(10px);
    }

    .icon-item:hover .icon-glow {
        opacity: 1;
        transform: scale(1.2);
    }

    .icon-item:hover .payment-icon {
        transform: scale(1.1);
        filter: drop-shadow(0 0 10px rgba(200, 166, 117, 0.3));
    }

    .icon-item span {
        color: var(--gray);
        font-size: 1rem;
        font-weight: 500;
        transition: var(--transition);
    }

    .icon-item:hover span {
        color: var(--primary-light);
    }

    @media (max-width: 768px) {
        .payment-icons {
            padding: 4rem 0;
        }

        .icons-grid {
            grid-template-columns: repeat(3, 1fr);
            gap: 2rem;
        }
        
        .payment-icon {
            width: 60px;
            height: 60px;
        }

        .section-title {
            font-size: 1.8rem;
        }

        .section-subtitle {
            font-size: 1rem;
            margin-bottom: 2rem;
        }
    }

    /* 减少不必要的动画 */
    @media (prefers-reduced-motion: reduce) {
        .background-animation,
        .star,
        .shooting-star {
            animation: none;
        }
    }

    .nav-item > a.has-arrow {
        display: flex;
        align-items: center;
        gap: 8px;
        padding-right: 12px;
    }

    /* 确保箭头颜色为金色 */
    .dropdown-arrow path {
        stroke: var(--primary);
    }

    /* 悬停时箭头颜色加深 */
    .nav-item:hover .dropdown-arrow path {
        stroke: var(--primary-dark);
    }

    /* 添加订单状态样式 */
    .order-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid rgba(200, 166, 117, 0.1);
    }

    .order-status {
        padding: 4px 12px;
        border-radius: 4px;
        font-size: 14px;
    }

    .status-0 { background: rgba(255, 152, 0, 0.1); color: #ff9800; }
    .status-1 { background: rgba(76, 175, 80, 0.1); color: #4caf50; }
    .status-2 { background: rgba(33, 150, 243, 0.1); color: #2196f3; }
    .status-3 { background: rgba(244, 67, 54, 0.1); color: #f44336; }
    .status-4 { background: rgba(158, 158, 158, 0.1); color: #9e9e9e; }

    .info-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15px;
    }

    .info-item {
        flex: 1;
        margin-right: 20px;
    }

    .info-item:last-child {
        margin-right: 0;
    }

    .notice-text {
        padding: 15px;
        background: rgba(200, 166, 117, 0.05);
        border-radius: 8px;
        margin: 20px 0;
        font-size: 14px;
        color: var(--gray);
    }

    .notice-text i {
        color: var(--primary);
        margin-right: 8px;
    }

    .no-results {
        text-align: center;
        padding: 40px 0;
    }

    .no-results i {
        font-size: 48px;
        color: var(--primary);
        margin-bottom: 15px;
    }

    /* 搜索框样式优化 */
    .search-form {
        margin-bottom: 20px;
    }

    .input-group {
        display: flex;
        gap: 10px;
        background: rgba(255, 255, 255, 0.05);
        padding: 5px;
        border-radius: 8px;
        border: 1px solid rgba(200, 166, 117, 0.2);
    }

    .search-input {
        flex: 1;
        padding: 15px 20px;
        background: transparent;
        border: none;
        color: var(--light);
        font-size: 16px;
        outline: none;
    }

    .search-input::placeholder {
        color: rgba(255, 255, 255, 0.5);
    }

    .search-btn {
        padding: 12px 30px;
        background: var(--primary);
        color: var(--dark);
        border: none;
        border-radius: 6px;
        font-size: 16px;
        cursor: pointer;
        transition: var(--transition);
    }

    .search-btn:hover {
        background: var(--primary-dark);
    }

    /* 标题样式优化 */
    .search-title {
        font-size: 2.2rem;
        margin-bottom: 1rem;
        text-align: center;
        background: linear-gradient(135deg, var(--primary-light), var(--primary));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        padding: 0 20px;
    }

    .search-subtitle {
        text-align: center;
        color: var(--gray);
        margin-bottom: 2rem;
        padding: 0 20px;
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
        .input-group {
            flex-direction: column;
            gap: 10px;
            padding: 10px;
        }

        .search-btn {
            width: 100%;
        }

        .search-title {
            font-size: 1.8rem;
        }
    }

    .captcha-group {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .captcha-input {
        width: 120px;
        padding: 15px 20px;
        background: transparent;
        border: none;
        color: var(--light);
        font-size: 16px;
        outline: none;
        border-left: 1px solid rgba(200, 166, 117, 0.2);
    }

    .captcha-input::placeholder {
        color: rgba(255, 255, 255, 0.5);
    }

    .captcha-img {
        height: 38px;
        border-radius: 4px;
        cursor: pointer;
        transition: var(--transition);
    }

    .captcha-img:hover {
        opacity: 0.8;
    }

    .error-message {
        margin-top: 10px;
        padding: 10px;
        background: rgba(244, 67, 54, 0.1);
        color: #f44336;
        border-radius: 4px;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    /* 移动端适配 */
    @media (max-width: 768px) {
        .captcha-group {
            width: 100%;
        }
        
        .captcha-input {
            flex: 1;
        }
    }

    /* 弹窗样式 */
    .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        z-index: 1000;
        backdrop-filter: blur(5px);
    }

    .modal-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: var(--dark);
        border: 1px solid rgba(200, 166, 117, 0.2);
        border-radius: 10px;
        padding: 20px;
        width: 90%;
        max-width: 400px;
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid rgba(200, 166, 117, 0.1);
    }

    .modal-header h3 {
        color: var(--primary);
        margin: 0;
    }

    .close {
        color: var(--gray);
        font-size: 24px;
        cursor: pointer;
        transition: var(--transition);
    }

    .close:hover {
        color: var(--primary);
    }

    .captcha-container {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;
    }

    .modal-footer {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        margin-top: 20px;
        padding-top: 20px;
        border-top: 1px solid rgba(200, 166, 117, 0.1);
    }

    .cancel-btn, .confirm-btn {
        padding: 8px 20px;
        border-radius: 5px;
        cursor: pointer;
        transition: var(--transition);
    }

    .cancel-btn {
        background: transparent;
        border: 1px solid var(--primary);
        color: var(--primary);
    }

    .confirm-btn {
        background: var(--primary);
        border: 1px solid var(--primary);
        color: var(--dark);
    }

    .cancel-btn:hover {
        background: rgba(200, 166, 117, 0.1);
    }

    .confirm-btn:hover {
        background: var(--primary-dark);
    }

    .order-item {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        border: 1px solid rgba(200, 166, 117, 0.2);
    }

    .order-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 15px;
        padding-bottom: 15px;
        border-bottom: 1px solid rgba(200, 166, 117, 0.1);
    }

    .order-title {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .product-name {
        font-size: 16px;
        color: var(--light);
    }

    .order-time {
        font-size: 14px;
        color: var(--gray);
    }

    .order-no {
        color: var(--primary);
        font-size: 14px;
    }

    .order-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
    }

    .info-item {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .info-item label {
        color: var(--gray);
    }

    .amount {
        color: var(--primary);
        font-weight: 500;
    }

    .status {
        padding: 4px 12px;
        border-radius: 4px;
        font-size: 14px;
    }

    .order-actions {
        display: flex;
        justify-content: flex-end;
        gap: 15px;
    }

    .btn-detail,
    .btn-pay {
        padding: 6px 16px;
        border-radius: 4px;
        font-size: 14px;
        text-decoration: none;
        transition: var(--transition);
    }

    .btn-detail {
        color: var(--primary);
        border: 1px solid var(--primary);
    }

    .btn-detail:hover {
        background: rgba(200, 166, 117, 0.1);
    }

    .btn-pay {
        background: var(--primary);
        color: var(--dark);
    }

    .btn-pay:hover {
        background: var(--primary-dark);
    }

    /* 订单列表样式 */
    .order-tabs {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
        border-bottom: 1px solid rgba(200, 166, 117, 0.1);
        padding: 10px 0;
    }

    .tab-item {
        color: var(--gray);
        text-decoration: none;
        padding: 5px 0;
        position: relative;
        transition: var(--transition);
    }

    .tab-item:hover {
        color: var(--primary);
    }

    .tab-item.active {
        color: var(--primary);
    }

    .tab-item.active:after {
        content: '';
        position: absolute;
        bottom: -11px;
        left: 0;
        width: 100%;
        height: 2px;
        background: var(--primary);
    }

    /* 确保状态标签在有验证码的情况下也能正常工作 */
    .tab-item {
        cursor: pointer;
    }

    /* 添加一个隐藏的表单用于保持验证码状态 */
    .hidden-form {
        display: none;
    }

    .order-table {
        background: rgba(255, 255, 255, 0.02);
        border-radius: 8px;
        overflow: hidden;
    }

    .table-header {
        display: grid;
        grid-template-columns: 2fr 1.5fr 1fr 0.8fr 0.8fr 1fr;
        padding: 15px;
        background: rgba(255, 255, 255, 0.05);
        color: var(--gray);
        font-size: 14px;
    }

    .table-row {
        display: grid;
        grid-template-columns: 2fr 1.5fr 1fr 0.8fr 0.8fr 1fr;
        padding: 15px;
        border-bottom: 1px solid rgba(200, 166, 117, 0.1);
        align-items: center;
    }

    .product-info {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .product-name {
        color: var(--light);
    }

    .create-time {
        color: var(--gray);
        font-size: 14px;
    }

    .amount-info {
        display: flex;
        flex-direction: column;
        gap: 2px;
    }

    .price {
        color: #ff4d4f;
    }

    .quantity {
        color: var(--gray);
        font-size: 12px;
    }

    .status-tag {
        padding: 4px 12px;
        border-radius: 4px;
        font-size: 14px;
    }

    .status-0 { background: rgba(255, 152, 0, 0.1); color: #ff9800; }
    .status-1 { background: rgba(76, 175, 80, 0.1); color: #4caf50; }
    .status-2 { background: rgba(158, 158, 158, 0.1); color: #9e9e9e; }
    .status-3 { background: rgba(244, 67, 54, 0.1); color: #f44336; }

    .btn-get-card {
        padding: 4px 12px;
        border: 1px solid var(--primary);
        background: transparent;
        color: var(--primary);
        border-radius: 4px;
        cursor: pointer;
        transition: var(--transition);
    }

    .btn-get-card:hover {
        background: rgba(200, 166, 117, 0.1);
    }

    .btn-detail,
    .btn-refund {
        color: var(--primary);
        text-decoration: none;
        font-size: 13px;
        padding: 4px 8px;
        border-radius: 4px;
        transition: var(--transition);
    }

    .btn-detail:hover,
    .btn-refund:hover {
        color: var(--primary-light);
        background: rgba(200, 166, 117, 0.1);
    }

    .col-actions {
        display: flex;
        gap: 8px;
        align-items: center;
        white-space: nowrap;
    }

    @media (max-width: 768px) {
        .table-header,
        .table-row {
            grid-template-columns: 1fr;
            gap: 10px;
        }
        
        .table-header {
            display: none;
        }
        
        .col-name,
        .col-trade-no,
        .col-amount,
        .col-status,
        .col-profit,
        .col-actions {
            padding: 5px 0;
        }
    }

    .card-info {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .card-count {
        color: var(--gray);
        margin-bottom: 15px;
    }

    .card-count span {
        color: var(--primary);
        font-weight: 500;
    }

    .card-list {
        display: flex;
        flex-direction: column;
        gap: 10px;
        max-height: 300px;
        overflow-y: auto;
    }

    .card-item {
        display: flex;
        align-items: center;
        gap: 15px;
        padding: 10px;
        background: rgba(200, 166, 117, 0.1);
        border-radius: 6px;
    }

    .card-number {
        color: var(--gray);
        font-size: 14px;
        min-width: 40px;
    }

    .card-code {
        flex: 1;
        color: var(--primary);
        font-family: monospace;
        font-size: 16px;
        word-break: break-all;
    }

    .copy-btn {
        padding: 4px 12px;
        background: var(--primary);
        color: var(--dark);
        border: none;
        border-radius: 4px;
        cursor: pointer;
        transition: var(--transition);
    }

    .copy-btn:hover {
        background: var(--primary-dark);
    }

    .help-text {
        text-align: center;
        margin-top: 20px;
    }

    .help-text h4 {
        color: var(--gray);
        margin-bottom: 10px;
    }

    .contact-link {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        color: var(--primary);
        text-decoration: none;
        padding: 8px 16px;
        border: 1px solid var(--primary);
        border-radius: 4px;
        transition: var(--transition);
    }

    .contact-link:hover {
        background: rgba(200, 166, 117, 0.1);
    }

    /* 分页容器 */
    .pagination-container {
        margin-top: 2rem;
        padding: 1rem;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    /* 分页样式 */
    .pagination {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }

    .pagination li {
        list-style: none;
    }

    .pagination a,
    .pagination span {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 32px;
        height: 32px;
        padding: 0 10px;
        border-radius: 4px;
        background: rgba(200, 166, 117, 0.1);
        color: var(--primary);
        text-decoration: none;
        transition: var(--transition);
        border: 1px solid rgba(200, 166, 117, 0.2);
    }

    .pagination a:hover {
        background: rgba(200, 166, 117, 0.2);
        border-color: var(--primary);
    }

    .pagination .active span {
        background: var(--primary);
        color: var(--dark);
        border-color: var(--primary);
    }

    .pagination .disabled span {
        background: rgba(255, 255, 255, 0.05);
        color: var(--gray);
        cursor: not-allowed;
        border-color: transparent;
    }

    /* 移动端适配 */
    @media (max-width: 768px) {
        .pagination {
            flex-wrap: wrap;
            justify-content: center;
            gap: 0.5rem;
        }

        .pagination a,
        .pagination span {
            min-width: 28px;
            height: 28px;
            padding: 0 8px;
            font-size: 0.9rem;
        }
    }

    /* 添加样式 */
    .info-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        margin-top: 15px;
    }

    .info-item {
        padding: 10px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 5px;
    }

    .info-item .label {
        color: var(--primary);
        margin-right: 8px;
    }

    /* 修改按钮样式，使其与原链接样式一致 */
    .btn-detail {
        color: var(--primary);
        text-decoration: none;
        font-size: 13px;
        padding: 4px 8px;
        border-radius: 4px;
        transition: var(--transition);
        background: transparent;
        border: none;
        cursor: pointer;
    }

    .btn-detail:hover {
        color: var(--primary-light);
        background: rgba(200, 166, 117, 0.1);
    }

    /* 订单详情弹窗样式 */
    .order-detail-modal {
        max-width: 800px;
        background: var(--dark);
        border: 1px solid var(--primary);
    }

    .detail-section {
        margin-bottom: 24px;
    }

    .detail-section h4 {
        color: var(--primary);
        font-size: 16px;
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 1px solid rgba(200, 166, 117, 0.2);
    }

    .detail-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .detail-item {
        display: flex;
        align-items: center;
        padding: 12px;
        background: rgba(255, 255, 255, 0.03);
        border-radius: 6px;
        transition: background 0.3s;
    }

    .detail-item:hover {
        background: rgba(255, 255, 255, 0.05);
    }

    .detail-item .label {
        color: var(--gray);
        min-width: 90px;
        font-size: 14px;
    }

    .detail-item .value {
        color: var(--light);
        flex: 1;
        font-size: 14px;
    }

    /* 状态标签样式 */
    .status-tag {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
    }

    .status-pending {
        background: rgba(255, 152, 0, 0.1);
        color: #ff9800;
    }

    .status-success {
        background: rgba(76, 175, 80, 0.1);
        color: #4caf50;
    }

    .status-closed {
        background: rgba(158, 158, 158, 0.1);
        color: #9e9e9e;
    }

    .status-refunded {
        background: rgba(244, 67, 54, 0.1);
        color: #f44336;
    }

    /* 优化搜索区域样式 */
    .search-section {
        position: relative;
        z-index: 1;
        background: transparent;
    }

    .search-title {
        font-size: 2.8rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        text-shadow: 0 2px 10px rgba(200, 166, 117, 0.3);
        letter-spacing: 1px;
    }

    .search-subtitle {
        font-size: 1.2rem;
        color: var(--gray);
        margin-bottom: 3rem;
        letter-spacing: 0.5px;
    }

    .search-box {
        background: rgba(255, 255, 255, 0.03);
        border-radius: 16px;
        padding: 30px;
        border: 1px solid rgba(200, 166, 117, 0.1);
        backdrop-filter: blur(10px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    }

    .input-group {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(200, 166, 117, 0.2);
        border-radius: 12px;
        padding: 6px;
        transition: all 0.3s ease;
    }

    .input-group:hover {
        border-color: var(--primary);
        box-shadow: 0 0 15px rgba(200, 166, 117, 0.1);
    }

    .search-input {
        height: 48px;
        font-size: 1.1rem;
        letter-spacing: 0.5px;
    }

    .search-btn {
        min-width: 120px;
        height: 48px;
        font-weight: 500;
        letter-spacing: 1px;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    /* 优化订单列表样式 */
    .order-table {
        margin-top: 30px;
        border: 1px solid rgba(200, 166, 117, 0.1);
        border-radius: 12px;
        overflow: hidden;
        backdrop-filter: blur(10px);
    }

    .table-header {
        background: rgba(200, 166, 117, 0.05);
        padding: 20px;
        font-weight: 500;
        letter-spacing: 0.5px;
    }

    .table-row {
        padding: 20px;
        transition: all 0.3s ease;
        border-bottom: 1px solid rgba(200, 166, 117, 0.05);
    }

    .table-row:hover {
        background: rgba(200, 166, 117, 0.03);
    }

    .product-name {
        font-size: 1.1rem;
        font-weight: 500;
        margin-bottom: 6px;
    }

    .create-time {
        font-size: 0.9rem;
        color: var(--gray);
    }

    /* 优化状态标签样式 */
    .status-tag {
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 0.9rem;
        font-weight: 500;
        letter-spacing: 0.5px;
        text-transform: uppercase;
    }

    /* 优化按钮样式 */
    .btn-get-card,
    .btn-detail,
    .btn-refund {
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: 500;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
    }

    .btn-get-card {
        background: rgba(200, 166, 117, 0.1);
        border: 1px solid var(--primary);
    }

    .btn-get-card:hover {
        background: var(--primary);
        color: var(--dark);
    }

    /* 优化分页样式 */
    .pagination-container {
        margin-top: 40px;
        padding: 20px;
    }

    .pagination a,
    .pagination span {
        min-width: 40px;
        height: 40px;
        font-weight: 500;
        letter-spacing: 0.5px;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    /* 优化弹窗样式 */
    .modal-content {
        background: rgba(0, 0, 0, 0.95);
        border-radius: 16px;
        border: 1px solid rgba(200, 166, 117, 0.2);
        backdrop-filter: blur(20px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
    }

    .modal-header {
        padding: 20px 25px;
        border-bottom: 1px solid rgba(200, 166, 117, 0.1);
    }

    .modal-header h3 {
        font-size: 1.4rem;
        font-weight: 600;
        letter-spacing: 0.5px;
    }

    .modal-body {
        padding: 25px;
    }

    /* 当导航项超过一定数量时进行响应式调整 */
    @media (max-width: 1200px) {
        .nav-item > a {
            font-size: 0.8rem;
            padding: 0.3rem 0.5rem;
        }
        
        .nav-links {
            gap: 0.5rem;
        }
    }

    /* 添加导航栏滚动功能 */
    @media (max-width: 1400px) {
        .nav-links {
            overflow-x: auto;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
            padding-bottom: 5px; /* 为滚动条预留空间 */
        }
        
        .nav-links::-webkit-scrollbar {
            display: none; /* Chrome, Safari and Opera */
        }
    }

    /* 添加导航栏装饰星星样式 */
    .header .container {
        position: relative;
    }

    .header .container::before,
    .header .container::after {
        content: '';
        position: absolute;
        top: 50%;
        width: 20px;
        height: 20px;
        background: linear-gradient(135deg, var(--primary-light), var(--primary));
        clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
        animation: starTwinkle 2s ease-in-out infinite;
    }

    .header .container::before {
        left: -30px;
        transform: translateY(-50%) rotate(-15deg);
        animation-delay: 0s;
    }

    .header .container::after {
        right: -30px; 
        transform: translateY(-50%) rotate(15deg);
        animation-delay: 1s;
    }

    @keyframes starTwinkle {
        0%, 100% {
            opacity: 0.3;
            transform: translateY(-50%) scale(0.8) rotate(-15deg);
        }
        50% {
            opacity: 1;
            transform: translateY(-50%) scale(1.2) rotate(-15deg);
        }
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
        .header .container::before,
        .header .container::after {
            width: 15px;
            height: 15px;
        }
        
        .header .container::before {
            left: -20px;
        }
        
        .header .container::after {
            right: -20px;
        }
    }

    .instructions-section {
        margin-top: 20px;
        padding: 15px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        border: 1px solid rgba(200, 166, 117, 0.2);
    }

    .instructions-section h4 {
        color: var(--primary);
        margin-bottom: 10px;
        font-size: 1.1rem;
    }

    .instructions-content {
        color: var(--light);
        font-size: 0.9rem;
        line-height: 1.6;
    }

    .instructions-content p {
        margin: 0;
    }

    .instructions-content span {
        color: rgb(78, 89, 105);
        background-color: rgb(255, 255, 255);
        font-size: 14px;
    }

    /* 添加左右旋转行星样式 */
    .search-section {
        position: relative;
    }

    .planet {
        position: absolute;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        pointer-events: none;
    }

    .planet-left {
        left: 5%;
        top: 30%;
        background: linear-gradient(135deg, var(--primary-light), var(--primary));
        box-shadow: 
            0 0 20px rgba(200, 166, 117, 0.3),
            0 0 40px rgba(200, 166, 117, 0.2),
            inset 0 0 20px rgba(255, 255, 255, 0.2);
        animation: rotatePlanetLeft 20s linear infinite;
    }

    .planet-right {
        right: 5%;
        top: 30%;
        background: linear-gradient(225deg, var(--primary-light), var(--primary));
        box-shadow: 
            0 0 20px rgba(200, 166, 117, 0.3),
            0 0 40px rgba(200, 166, 117, 0.2),
            inset 0 0 20px rgba(255, 255, 255, 0.2);
        animation: rotatePlanetRight 20s linear infinite;
    }

    .planet::before {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: radial-gradient(circle at 30% 30%, 
            rgba(255, 255, 255, 0.4),
            transparent 50%);
    }

    .planet::after {
        content: '';
        position: absolute;
        width: 120%;
        height: 120%;
        top: -10%;
        left: -10%;
        border-radius: 50%;
        border: 2px solid rgba(200, 166, 117, 0.3);
        animation: rotateOrbit 10s linear infinite;
    }

    @keyframes rotatePlanetLeft {
        from {
            transform: rotate(0deg) translateX(20px) rotate(0deg);
        }
        to {
            transform: rotate(360deg) translateX(20px) rotate(-360deg);
        }
    }

    @keyframes rotatePlanetRight {
        from {
            transform: rotate(0deg) translateX(-20px) rotate(0deg);
        }
        to {
            transform: rotate(-360deg) translateX(-20px) rotate(360deg);
        }
    }

    @keyframes rotateOrbit {
        from {
            transform: rotate(0deg);
        }
        to {
            transform: rotate(360deg);
        }
    }

    /* 响应式调整 */
    @media (max-width: 1200px) {
        .planet {
            width: 40px;
            height: 40px;
        }
    }

    @media (max-width: 768px) {
        .planet {
            display: none;
        }
    }

    /* 行星装饰 */
    .planet {
        position: fixed;
        width: 300px;
        height: 300px;
        border-radius: 50%;
        pointer-events: none;
        z-index: 0;
    }

    .planet-left {
        left: -150px;
        top: 20%;
        background: radial-gradient(circle at 70% 50%,
            rgba(200, 166, 117, 0.15),
            rgba(200, 166, 117, 0.1),
            rgba(0, 0, 0, 0)
        );
        box-shadow: 
            inset -20px -20px 50px rgba(200, 166, 117, 0.2),
            0 0 50px rgba(200, 166, 117, 0.1);
        animation: planetPulseLeft 8s ease-in-out infinite;
    }

    .planet-right {
        right: -150px;
        bottom: 20%;
        background: radial-gradient(circle at 30% 50%,
            rgba(200, 166, 117, 0.15),
            rgba(200, 166, 117, 0.1),
            rgba(0, 0, 0, 0)
        );
        box-shadow: 
            inset 20px -20px 50px rgba(200, 166, 117, 0.2),
            0 0 50px rgba(200, 166, 117, 0.1);
        animation: planetPulseRight 8s ease-in-out infinite;
    }

    /* 行星环 */
    .planet-ring {
        position: absolute;
        width: 400px;
        height: 80px;
        border-radius: 50%;
        border: 2px solid rgba(200, 166, 117, 0.1);
        transform: rotate(-20deg);
    }

    .planet-left .planet-ring {
        top: 110px;
        left: -50px;
    }

    .planet-right .planet-ring {
        top: 110px;
        left: -50px;
        transform: rotate(20deg);
    }

    /* 行星表面纹理 */
    .planet-texture {
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: repeating-linear-gradient(
            45deg,
            rgba(200, 166, 117, 0.05) 0px,
            transparent 5px,
            transparent 10px
        );
        animation: textureRotate 20s linear infinite;
    }

    /* 行星光晕动画 */
    @keyframes planetPulseLeft {
        0%, 100% {
            transform: scale(1) translateX(0);
            opacity: 0.8;
        }
        50% {
            transform: scale(1.1) translateX(20px);
            opacity: 1;
        }
    }

    @keyframes planetPulseRight {
        0%, 100% {
            transform: scale(1) translateX(0);
            opacity: 0.8;
        }
        50% {
            transform: scale(1.1) translateX(-20px);
            opacity: 1;
        }
    }

    @keyframes textureRotate {
        from {
            transform: rotate(0deg);
        }
        to {
            transform: rotate(360deg);
        }
    }

    /* 响应式调整 */
    @media (max-width: 1400px) {
        .planet {
            width: 200px;
            height: 200px;
        }
        .planet-left {
            left: -100px;
        }
        .planet-right {
            right: -100px;
        }
        .planet-ring {
            width: 300px;
            height: 60px;
            top: 70px;
            left: -50px;
        }
    }

    @media (max-width: 768px) {
        .planet {
            display: none;
        }
    }

    /* 添加页脚两列布局样式 */
    .footer-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
        padding: 2rem 0;
    }

    .footer-column {
        display: flex;
        flex-direction: column;
        gap: 0.8rem;
    }

    .footer-column h3 {
        color: var(--primary);
        font-size: 1.2rem;
        margin-bottom: 1rem;
        font-weight: 500;
    }

    .footer-column a {
        color: var(--light);
        text-decoration: none;
        font-size: 0.9rem;
        transition: var(--transition);
        opacity: 0.8;
    }

    .footer-column a:hover {
        color: var(--primary);
        opacity: 1;
    }

    @media (max-width: 768px) {
        .footer-grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }
    }

    /* 修改移动端菜单样式 */
    .mobile-menu {
        position: fixed;
        top: 0;
        left: -100%;  /* 改为从左侧滑出 */
        width: 80%;
        height: 100vh;
        background: rgba(0, 0, 0, 0.95);
        backdrop-filter: blur(10px);
        z-index: 1000;
        transition: var(--transition);
        padding: 2rem 1rem;
        overflow-y: auto;
    }

    .mobile-menu.active {
        left: 0;  /* 改为从左侧滑出 */
    }

    /* 修改移动导航链接样式 */
    .mobile-nav-links {
        margin-top: 1rem;
    }

    .mobile-nav-item {
        margin-bottom: 0.5rem;
    }

    .mobile-nav-link {
        color: var(--light);
        text-decoration: none;
        font-size: 1rem;
        display: block;
        padding: 0.8rem 1rem;
        background: rgba(0, 0, 0, 0.3);
        border-radius: 4px;
        transition: var(--transition);
    }

    .mobile-nav-link:hover {
        background: rgba(200, 166, 117, 0.1);
        color: var(--primary);
    }

    /* 修改移动端认证按钮样式 */
    .mobile-auth-buttons {
        margin-top: 1rem;
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .mobile-auth-buttons .btn {
        width: 100%;
        padding: 0.8rem;
        text-align: center;
        font-size: 1rem;
    }

    .mobile-auth-buttons .btn-login {
        background: rgba(0, 0, 0, 0.3);
        border: 1px solid var(--primary);
    }

    .mobile-auth-buttons .btn-register {
        background: var(--primary);
        color: var(--dark);
    }

    /* 修改星星和背景动画的媒体查询 */
    @media (max-width: 768px) {
        .stars-container,
        .background-animation,
        .planet,
        .particles-container {
            display: none; /* 在移动端隐藏所有动画背景元素 */
        }
        
        body {
            background: var(--dark); /* 使用纯色背景 */
        }
    }

    /* 订单表格响应式优化 */
    @media (max-width: 768px) {
        .order-table .table-header {
            display: none; /* 隐藏表头 */
        }

        .table-row {
            display: flex;
            flex-direction: column;
            padding: 1rem;
            margin-bottom: 1rem;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            border: 1px solid rgba(200, 166, 117, 0.1);
        }

        .table-row > div {
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(200, 166, 117, 0.1);
            width: 100% !important; /* 覆盖原有宽度 */
        }

        .table-row > div:last-child {
            border-bottom: none;
        }

        /* 为每个字段添加标签 */
        .col-name::before {
            content: "商品名称：";
            color: var(--gray);
        }

        .col-trade-no::before {
            content: "订单号：";
            color: var(--gray);
        }

        .col-amount::before {
            content: "订单金额：";
            color: var(--gray);
        }

        .col-status::before {
            content: "订单状态：";
            color: var(--gray);
        }

        /* 操作按钮优化 */
        .col-actions {
            display: flex;
            gap: 0.8rem;
            margin-top: 0.5rem;
        }

        .btn-detail,
        .btn-refund {
            flex: 1;
            padding: 0.8rem;
            text-align: center;
            font-size: 0.9rem;
        }

        /* 订单详情弹窗优化 */
        .order-detail-modal {
            width: 95%;
            margin: 1rem auto;
        }

        .detail-grid {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
            gap: 0.3rem;
        }

        .detail-item .label {
            color: var(--gray);
            font-size: 0.9rem;
        }

        .detail-item .value {
            font-size: 1rem;
            word-break: break-all;
        }

        /* 状态标签优化 */
        .status-tag {
            display: inline-block;
            padding: 0.3rem 0.8rem;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        /* 商品信息布局优化 */
        .product-info {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .product-name {
            font-size: 1rem;
            font-weight: 500;
        }

        .create-time {
            font-size: 0.8rem;
            color: var(--gray);
        }

        /* 金额信息布局优化 */
        .amount-info {
            display: flex;
            flex-direction: column;
            gap: 0.3rem;
        }

        .price {
            font-size: 1.1rem;
            color: var(--primary);
        }

        .quantity {
            font-size: 0.8rem;
            color: var(--gray);
        }

        /* 获取卡密按钮优化 */
        .btn-get-card {
            display: inline-block;
            padding: 0.5rem 1rem;
            background: var(--primary);
            color: var(--dark);
            border-radius: 4px;
            text-decoration: none;
            text-align: center;
            width: 100%;
        }
    }

    /* 弹窗响应式优化 */
    @media (max-width: 768px) {
        .modal-content {
            width: 95%;
            margin: 1rem;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            padding: 1rem;
        }

        .modal-body {
            padding: 1rem;
        }

        .modal-footer {
            padding: 1rem;
            flex-direction: column;
            gap: 0.8rem;
        }

        .modal-footer button {
            width: 100%;
            padding: 0.8rem;
        }
    }
        </style>
    </head>
    <body>
        <div class="stars-container"></div>
        <div class="background-animation"></div>

        <!-- 添加行星元素 -->
        <div class="planet planet-left">
            <div class="planet-texture"></div>
            <div class="planet-ring"></div>
        </div>
        <div class="planet planet-right">
            <div class="planet-texture"></div>
            <div class="planet-ring"></div>
        </div>

        <!-- 复用首页的导航结构 -->
        <header class="header">
            <div class="container">
                <a href="/" class="logo">
                    {if !empty($logo)}
                    <img src="{$logo}" alt="{$siteName}" class="logo-img">
                    {else}
                    <i class="fas fa-credit-card"></i>
                    {/if}
                </a>
                
                <nav class="nav-links">
                    {foreach $navItems as $nav}
                    <div class="nav-item">
                        <a href="{$nav.href}" class="nav-link {if !empty($nav.children)}has-arrow{/if}">
                            {$nav.name}
                            {if !empty($nav.children)}
                            <svg class="dropdown-arrow" width="8" height="6" viewBox="0 0 8 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M4 6L0 0L8 0L4 6Z" fill="var(--primary)"/>
                            </svg>
                            {/if}
                        </a>
                        {if !empty($nav.children)}
                        <div class="submenu">
                            {foreach $nav.children as $child}
                            <a href="{$child.href}">{$child.name}</a>
                            {/foreach}
                        </div>
                        {/if}
                    </div>
                    {/foreach}
                </nav>
                <div class="auth-buttons">
                    <a href="/merchant/login" class="btn btn-login">
                        <svg class="auth-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M20 21V19C20 16.7909 18.2091 15 16 15H8C5.79086 15 4 16.7909 4 19V21" stroke="var(--primary)" stroke-width="2" stroke-linecap="round"/>
                            <circle cx="12" cy="7" r="4" stroke="var(--primary)" stroke-width="2"/>
                        </svg>
                        商户登录
                    </a>
                    <a href="/merchant/register" class="btn btn-register">
                        <svg class="auth-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M16 21V19C16 16.7909 14.2091 15 12 15H8C5.79086 15 4 16.7909 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            <circle cx="10" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                            <path d="M20 8V14M17 11H23" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                        商户注册
                    </a>
                </div>
                <button class="mobile-menu-btn">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </header>

        <!-- 移动端菜单 -->
        <div class="mobile-menu">
            <button class="mobile-menu-close">
                <i class="fas fa-times"></i>
            </button>
            
            <nav class="mobile-nav-links">
                {foreach $navItems as $nav}
                <div class="mobile-nav-item">
                    <a href="{$nav.href}" class="mobile-nav-link">
                        {$nav.name}
                        {if !empty($nav.children)}
                        <i class="fas fa-chevron-down"></i>
                        {/if}
                    </a>
                    {if !empty($nav.children)}
                    <div class="mobile-submenu">
                        {foreach $nav.children as $child}
                        <a href="{$child.href}" class="mobile-nav-link">{$child.name}</a>
                        {/foreach}
                    </div>
                    {/if}
                </div>
                {/foreach}
            </nav>
            
            <div class="mobile-auth-buttons">
                <a href="/merchant/login" class="btn btn-login">
                    <i class="fas fa-user"></i>
                    商户登录
                </a>
                <a href="/merchant/register" class="btn btn-register">
                    <i class="fas fa-user-plus"></i>
                    商户注册
                </a>
            </div>
        </div>

        <style>
            /* 移动端菜单样式 */
            .mobile-menu {
                position: fixed;
                top: 0;
                right: -100%;
                width: 80%;
                max-width: 300px;
                height: 100vh;
                background: rgba(0, 0, 0, 0.95);
                backdrop-filter: blur(10px);
                z-index: 2000;
                padding: 2rem;
                transition: right 0.3s ease;
                overflow-y: auto;
            }

            .mobile-menu.active {
                right: 0;
            }

            .mobile-menu-close {
                position: absolute;
                top: 1rem;
                right: 1rem;
                background: transparent;
                border: none;
                color: var(--light);
                font-size: 1.5rem;
                cursor: pointer;
                padding: 0.5rem;
                transition: var(--transition);
            }

            .mobile-menu-close:hover {
                color: var(--primary);
            }

            .mobile-nav-links {
                margin-top: 2rem;
            }

            .mobile-nav-item {
                margin-bottom: 1rem;
            }

            .mobile-nav-link {
                color: var(--light);
                text-decoration: none;
                font-size: 1.1rem;
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 0.8rem 0;
                transition: var(--transition);
            }

            .mobile-nav-link:hover {
                color: var(--primary);
            }

            .mobile-submenu {
                padding-left: 1rem;
                display: none;
            }

            .mobile-nav-item.active .mobile-submenu {
                display: block;
            }

            .mobile-auth-buttons {
                margin-top: 2rem;
                display: flex;
                flex-direction: column;
                gap: 1rem;
            }

            .mobile-auth-buttons .btn {
                width: 100%;
                padding: 0.8rem;
                text-align: center;
                border-radius: 8px;
                transition: var(--transition);
            }

            .btn-login {
                background: transparent;
                border: 1px solid var(--primary);
                color: var(--primary);
            }

            .btn-register {
                background: var(--primary);
                border: 1px solid var(--primary);
                color: var(--dark);
            }

            .btn-login:hover {
                background: rgba(200, 166, 117, 0.1);
            }

            .btn-register:hover {
                background: var(--primary-dark);
                border-color: var(--primary-dark);
            }

            @media (min-width: 769px) {
                .mobile-menu-btn {
                    display: none;
                }
            }

            @media (max-width: 768px) {
                .nav-links,
                .auth-buttons {
                    display: none;
                }

                .mobile-menu-btn {
                    display: block;
                }
            }
        </style>

        <script>
            // 移动端菜单优化
            const menuBtn = document.querySelector('.mobile-menu-btn');
            const closeBtn = document.querySelector('.mobile-menu-close');
            const mobileMenu = document.querySelector('.mobile-menu');
            const mobileNavItems = document.querySelectorAll('.mobile-nav-item');
            
            menuBtn.addEventListener('click', () => {
                mobileMenu.classList.add('active');
                document.body.style.overflow = 'hidden';
            });
            
            closeBtn.addEventListener('click', () => {
                mobileMenu.classList.remove('active');
                document.body.style.overflow = '';
            });
            
            // 子菜单切换
            mobileNavItems.forEach(item => {
                const link = item.querySelector('.mobile-nav-link');
                const submenu = item.querySelector('.mobile-submenu');
                
                if (submenu) {
                    link.addEventListener('click', (e) => {
                        e.preventDefault();
                        submenu.classList.toggle('active');
                        link.querySelector('i').classList.toggle('fa-chevron-up');
                        link.querySelector('i').classList.toggle('fa-chevron-down');
                    });
                }
            });
            
            // 点击菜单外部关闭菜单
            document.addEventListener('click', (e) => {
                if (!mobileMenu.contains(e.target) && !menuBtn.contains(e.target)) {
                    mobileMenu.classList.remove('active');
                    document.body.style.overflow = '';
                }
            });
        </script>

        <!-- 订单查询区域 -->
        <section class="search-section">
            <div class="background-animation"></div>
            <div class="stars-container"></div>
            
            <div class="container search-container">
                <h1 class="search-title">轻松查询订单，即刻享受自动交易</h1>
                <p class="search-subtitle">在此输入您的预留联系方式，如通讯联系平台答复时间</p>
                
                <div class="search-box">
                    <form method="get" action="/orderinquiries" class="search-form" id="searchForm">
                        <div class="input-group">
                            <input type="text" 
                                   name="keywords" 
                                   value="{$keywords}" 
                                   class="search-input" 
                                   placeholder="请输入订单联系方式或买家信息">
                            <input type="hidden" name="ticket" id="ticketInput" value="{$ticket}">
                            <button type="button" class="search-btn" onclick="showCaptchaModal()">查询订单</button>
                        </div>
                    </form>

                    <div class="notice-text">
                        <i class="fas fa-info-circle"></i>
                        尊敬用户：实际支付日期在本平台申请接收，如出现延迟订单付款存在延迟，请在24小时内的平台客服反馈，逾期将自动执行协商解决。
                    </div>

                    <!-- 添加条件判断,只有当keywords不为空时才显示订单列表 -->
                    {if !empty($keywords)}
                    <div class="order-list">
                        <!-- 订单状态标签 -->
                        <div class="order-tabs">
                            <a href="/orderinquiries?keywords={$keywords}&ticket={$ticket}" class="tab-item {if $status === ''}active{/if}">全部</a>
                            <a href="/orderinquiries?keywords={$keywords}&ticket={$ticket}&status=0" class="tab-item {if $status === '0'}active{/if}">待付款</a>
                            <a href="/orderinquiries?keywords={$keywords}&ticket={$ticket}&status=1" class="tab-item {if $status === '1'}active{/if}">已支付</a>
                            <a href="/orderinquiries?keywords={$keywords}&ticket={$ticket}&status=3" class="tab-item {if $status === '3'}active{/if}">已退款</a>
                        </div>

                        <!-- 订单表格头部 -->
                        <div class="order-table">
                            <div class="table-header">
                                <div class="col-name">商品名</div>
                                <div class="col-trade-no">订单号</div>
                                <div class="col-amount">金额</div>
                                <div class="col-status">状态</div>
                                <div class="col-profit">查看权益</div>
                                <div class="col-actions">操作</div>
                            </div>

                            {if !empty($orderList)}
                                {foreach $orderList as $order}
                                <div class="table-row">
                                    <div class="col-name">
                                        <div class="product-info">
                                            <div class="product-name">{$order.goods_name}</div>
                                            <div class="create-time">{$order.create_time}</div>
                                        </div>
                                    </div>
                                    <div class="col-trade-no">{$order.trade_no}</div>
                                    <div class="col-amount">
                                        <div class="amount-info">
                                            <div class="price">¥{$order.total_amount}</div>
                                            <div class="quantity">共{$order.quantity}件</div>
                                        </div>
                                    </div>
                                    <div class="col-status">
                                        <span class="status-tag status-{$order.status}">{$order.status_text}</span>
                                    </div>
                                    <div class="col-profit">
                                        {if $order.status == 1}
                                        <a href="javascript:void(0)" class="btn-get-card">
                                            {if $order.goods_type == 'card'}
                                                获取卡密
                                            {else}
                                                查看文章
                                            {/if}
                                        </a>
                                        {/if}
                                    </div>
                                    <div class="col-actions">
                                        <!-- 将链接改为按钮，并绑定点击事件 -->
                                        <button class="btn-detail" onclick="showOrderDetail('{$order.trade_no}')">订单详情</button>
                                        {if $order.status == 1}
                                        <a href="/order/complaint/apply/{$order.trade_no}" class="btn-refund">申请售后</a>
                                        {/if}
                                    </div>
                                </div>
                                {/foreach}
                                
                                <!-- 分页组件 -->
                                <div class="pagination-container">
                                    {$orderList|raw}
                                </div>
                            {else}
                                <div class="no-results">
                                    <i class="fas fa-search"></i>
                                    <p>未找到相关订单信息</p>
                                </div>
                            {/if}
                        </div>
                    </div>
                    {/if}
                </div>
            </div>
        </section>

        <!-- 验证码弹窗 -->
        <div class="modal" id="captchaModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>请输入验证码</h3>
                    <span class="close" onclick="closeCaptchaModal()">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="captcha-container">
                        <input type="text" 
                               id="captchaCode" 
                               class="captcha-input" 
                               placeholder="请输入验证码">
                        <img src="{:captcha_src()}" 
                             class="captcha-img" 
                             id="captchaImg"
                             alt="验证码" 
                             onclick="refreshCaptcha()">
                    </div>
                    {if !empty($error)}
                    <div class="error-message">
                        <i class="fas fa-exclamation-circle"></i>
                        {$error}
                    </div>
                    {/if}
                </div>
                <div class="modal-footer">
                    <button class="cancel-btn" onclick="closeCaptchaModal()">取消</button>
                    <button class="confirm-btn" onclick="submitForm()">确定</button>
                </div>
            </div>
        </div>

        <!-- 修改卡密弹窗结构 -->
        <div class="modal" id="cardModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>卡密信息</h3>
                    <span class="close" onclick="closeCardModal()">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="card-info">
                        <div class="card-count">共 <span id="cardCount">0</span> 张卡密</div>
                        <div class="card-list" id="cardList">
                            <!-- 卡密列表将通过 JavaScript 动态添加 -->
                        </div>
                    </div>
                    
                    <!-- 添加使用说明区域 -->
                    <div class="instructions-section">
                        <h4>使用说明</h4>
                        <div class="instructions-content" id="instructionsContent">
                            <!-- 使用说明将通过 JavaScript 动态添加 -->
                        </div>
                    </div>
                    
                    <div class="help-text">
                        <h4>遇到问题？</h4>
                        <a href="/contact" class="contact-link">
                            <i class="fas fa-headset"></i>
                            联系客服
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加订单详情对话框 -->
        <div id="orderDetailModal" class="modal">
            <div class="modal-content order-detail-modal">
                <div class="modal-header">
                    <h3>订单详情</h3>
                    <span class="close" onclick="closeOrderDetailModal()">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="detail-section">
                        <h4>基本信息</h4>
                        <div class="detail-grid">
                            <div class="detail-item">
                                <span class="label">订单号</span>
                                <span id="detail-trade-no" class="value"></span>
                            </div>
                            <div class="detail-item">
                                <span class="label">渠道流水号</span>
                                <span id="detail-transaction-id" class="value"></span>
                            </div>
                            <div class="detail-item">
                                <span class="label">商品名称</span>
                                <span id="detail-goods-name" class="value"></span>
                            </div>
                            <div class="detail-item">
                                <span class="label">商品单价</span>
                                <span id="detail-goods-price" class="value"></span>
                            </div>
                        </div>
                    </div>

                    <div class="detail-section">
                        <h4>交易信息</h4>
                        <div class="detail-grid">
                            <div class="detail-item">
                                <span class="label">购买数量</span>
                                <span id="detail-quantity" class="value"></span>
                            </div>
                            <div class="detail-item">
                                <span class="label">订单总额</span>
                                <span id="detail-total-amount" class="value"></span>
                            </div>
                            <div class="detail-item">
                                <span class="label">订单状态</span>
                                <span id="detail-status" class="value"></span>
                            </div>
                            <div class="detail-item">
                                <span class="label">联系方式</span>
                                <span id="detail-contact" class="value"></span>
                            </div>
                        </div>
                    </div>

                    <div class="detail-section">
                        <h4>时间信息</h4>
                        <div class="detail-grid">
                            <div class="detail-item">
                                <span class="label">下单时间</span>
                                <span id="detail-create-time" class="value"></span>
                            </div>
                            <div class="detail-item">
                                <span class="label">支付时间</span>
                                <span id="detail-success-time" class="value"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 替换页脚部分 -->
        <footer class="footer">
            <div class="container">
                <div class="footer-grid">
                    <div class="footer-column">
                        {if $footer_service_show == 1}
                        <div class="footer-section">
                            <h3>服务中心</h3>
                            <a href="{$footer_service_1_link|default='#'}">{$footer_service_1|default='卡密查询'}</a>
                            <a href="{$footer_service_2_link|default='#'}">{$footer_service_2|default='投诉中心'}</a>
                            <a href="{$footer_service_3_link|default='#'}">{$footer_service_3|default='卡密工具'}</a>
                            <a href="{$footer_service_4_link|default='#'}">{$footer_service_4|default='商户入驻'}</a>
                        </div>
                        {/if}
                        
                        {if $footer_help_show == 1}
                        <div class="footer-section">
                            <h3>帮助中心</h3>
                            <a href="{$footer_help_1_link|default='#'}">{$footer_help_1|default='常见问题'}</a>
                            <a href="{$footer_help_2_link|default='#'}">{$footer_help_2|default='系统公告'}</a>
                            <a href="{$footer_help_3_link|default='#'}">{$footer_help_3|default='结算公告'}</a>
                            <a href="{$footer_help_4_link|default='#'}">{$footer_help_4|default='新闻动态'}</a>
                        </div>
                        {/if}
                    </div>
                    
                    <div class="footer-column">
                        {if $footer_legal_show == 1}
                        <div class="footer-section">
                            <h3>法律责任</h3>
                            <a href="{$footer_legal_1_link|default='#'}">{$footer_legal_1|default='免责声明'}</a>
                            <a href="{$footer_legal_2_link|default='#'}">{$footer_legal_2|default='禁售商品'}</a>
                            <a href="{$footer_legal_3_link|default='#'}">{$footer_legal_3|default='服务协议'}</a>
                            <a href="{$footer_legal_4_link|default='#'}">{$footer_legal_4|default='隐私政策'}</a>
                        </div>
                        {/if}
                        
                        {if $footer_links_show == 1}
                        <div class="footer-section">
                            <h3>友情链接</h3>
                            <a href="{$footer_links_1_link|default='#'}">{$footer_links_1|default='一意支付'}</a>
                            <a href="{$footer_links_2_link|default='#'}">{$footer_links_2|default='支付宝'}</a>
                            <a href="{$footer_links_3_link|default='#'}">{$footer_links_3|default='微信支付'}</a>
                            <a href="{$footer_links_4_link|default='#'}">{$footer_links_4|default='QQ钱包'}</a>
                        </div>
                        {/if}
                    </div>
                </div>
                
                <div class="copyright">
                    {$siteName} - 版权所有 © 2022-至今 
                    {if !empty($icpNumber)}
                    <a href="https://beian.miit.gov.cn/" target="_blank">{$icpNumber}</a>
                    {/if}
                    {if !empty($gaNumber)}
                    <a href="{$icpCert}" target="_blank">{$gaNumber}</a>
                    {/if}
                </div>
            </div>
        </footer>

        <script src="/static/others/vue/vue.global.prod.js"></script>
        <script src="/static/others/element-plus/index.full.min.js"></script>
        <script src="/static/others/axios/axios.min.js"></script>
        <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>

        <script>
            // 页面滚动效果
            window.addEventListener('scroll', () => {
                const header = document.querySelector('.header');
                if (window.scrollY > 50) {
                    header.classList.add('scrolled');
                } else {
                    header.classList.remove('scrolled');
                }
            });

            // 创建星星背景
            function createStars() {
                const starsContainer = document.querySelector('.stars-container');
                const starCount = 150; // 增加星星数量
                const shootingStarCount = 5; // 添加流星

                // 创建普通星星
                for (let i = 0; i < starCount; i++) {
                    const star = document.createElement('div');
                    star.className = 'star';
                    
                    // 随机位置
                    star.style.left = `${Math.random() * 100}%`;
                    star.style.top = `${Math.random() * 100}%`;
                    
                    // 随机大小 (稍微增大)
                    const size = Math.random() * 4 + 1;
                    star.style.width = `${size}px`;
                    star.style.height = `${size}px`;
                    
                    // 随机动画参数
                    star.style.setProperty('--delay', `${Math.random() * 5}s`);
                    star.style.setProperty('--duration', `${Math.random() * 3 + 2}s`);
                    star.style.setProperty('--opacity', Math.random() * 0.7 + 0.3);
                    
                    starsContainer.appendChild(star);
                }

                // 创建流星
                for (let i = 0; i < shootingStarCount; i++) {
                    const shootingStar = document.createElement('div');
                    shootingStar.className = 'shooting-star';
                    
                    // 随机位置和延迟
                    shootingStar.style.left = `${Math.random() * 100}%`;
                    shootingStar.style.top = `${Math.random() * 50}%`;
                    shootingStar.style.setProperty('--delay', `${Math.random() * 15}s`);
                    
                    starsContainer.appendChild(shootingStar);
                }
            }

            createStars();

            function showCaptchaModal() {
                document.getElementById('captchaModal').style.display = 'block';
            }

            function closeCaptchaModal() {
                document.getElementById('captchaModal').style.display = 'none';
            }

            function refreshCaptcha() {
                document.getElementById('captchaImg').src = '{:captcha_src()}?t=' + Math.random();
            }

            function submitForm() {
                const captchaCode = document.getElementById('captchaCode').value;
                if (!captchaCode) {
                    alert('请输入验证码');
                    return;
                }
                // 生成ticket
                const ticket = generateTicket(captchaCode);
                document.getElementById('ticketInput').value = ticket;
                document.getElementById('searchForm').submit();
            }

            function generateTicket(captchaCode) {
                // 生成一个32位的随机字符串作为ticket
                return Array.from(crypto.getRandomValues(new Uint8Array(16)))
                    .map(b => b.toString(16).padStart(2, '0'))
                    .join('');
            }

            // 点击模态框外部关闭
            window.onclick = function(event) {
                const modal = document.getElementById('captchaModal');
                if (event.target == modal) {
                    closeCaptchaModal();
                }
            }

            // ESC键关闭模态框
            document.addEventListener('keydown', function(event) {
                if (event.key === 'Escape') {
                    closeCaptchaModal();
                }
            });

            document.addEventListener('DOMContentLoaded', function() {
                // 如果已经通过验证，为所有状态标签添加ticket参数
                const ticketInput = document.getElementById('ticketInput');
                if (ticketInput && ticketInput.value) {
                    const tabs = document.querySelectorAll('.tab-item');
                    tabs.forEach(tab => {
                        const url = new URL(tab.href);
                        url.searchParams.set('ticket', ticketInput.value);
                        tab.href = url.toString();
                    });
                }
            });

            // 显示卡密弹窗
            function showCardModal(tradeNo, event) {
                if (event) {
                    event.preventDefault();
                    event.stopPropagation();
                }

                fetch('/index/orderinquiries/getOrderCard', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: 'trade_no=' + tradeNo
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        // 显示卡密弹窗
                        document.getElementById('cardModal').style.display = 'block';
                        
                        // 清空之前的内容
                        document.getElementById('cardList').innerHTML = '';
                        document.getElementById('cardCount').textContent = '0';
                        document.getElementById('instructionsContent').innerHTML = '';
                        
                        // 更新卡密数量
                        document.getElementById('cardCount').textContent = '1';
                        
                        // 创建卡密显示元素
                        const cardItem = document.createElement('div');
                        cardItem.className = 'card-item';
                        cardItem.innerHTML = `
                            <span class="card-number">#1</span>
                            <span class="card-code">${data.data}</span>
                            <button class="copy-btn" onclick="copyCardCode('${data.data}')">
                                复制
                            </button>
                        `;
                        document.getElementById('cardList').appendChild(cardItem);

                        // 添加使用说明
                        if (data.instructions) {
                            document.getElementById('instructionsContent').innerHTML = data.instructions;
                        } else {
                            document.getElementById('instructionsContent').innerHTML = '<p>暂无使用说明</p>';
                        }
                    } else if (data.code === 2) {
                        // 跳转到文章页面
                        window.location.href = data.data.url;
                    } else {
                        ElementPlus.ElMessage.error(data.msg || '获取信息失败');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    ElementPlus.ElMessage.error('获取信息失败，请稍后重试');
                });
            }

            // 关闭卡密弹窗
            function closeCardModal() {
                document.getElementById('cardModal').style.display = 'none';
            }

            // 复制卡密
            function copyCardCode(code) {
                navigator.clipboard.writeText(code).then(() => {
                    ElementPlus.ElMessage({
                        message: '复制成功',
                        type: 'success',
                        duration: 2000
                    });
                }).catch(err => {
                    console.error('复制失败:', err);
                    ElementPlus.ElMessage.error('复制失败，请手动复制');
                });
            }

            // 修改表格行的事件绑定
            document.querySelectorAll('.table-row').forEach(row => {
                // 移除行的点击事件
                row.onclick = null;
                
                // 只给获取卡密按钮添加点击事件
                const cardBtn = row.querySelector('.btn-get-card');
                if (cardBtn) {
                    cardBtn.onclick = function(e) {
                        const tradeNo = row.querySelector('.col-trade-no').textContent;
                        showCardModal(tradeNo, e);
                    };
                }
            });

            // 获取状态文本和样式类名
            function getStatusInfo(status) {
                const statusMap = {
                    0: { text: '未支付', class: 'status-pending' },
                    1: { text: '已支付', class: 'status-success' },
                    2: { text: '已关闭', class: 'status-closed' },
                    3: { text: '已退款', class: 'status-refunded' }
                };
                return statusMap[status] || { text: '未知', class: '' };
            }

            // 显示订单详情
            function showOrderDetail(tradeNo) {
                fetch('/index/orderinquiries/getOrderDetail', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: 'trade_no=' + tradeNo
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        const order = data.data;
                        const statusInfo = getStatusInfo(order.status);
                        
                        // 填充订单详情
                        document.getElementById('detail-trade-no').textContent = order.trade_no;
                        document.getElementById('detail-transaction-id').textContent = order.transaction_id || '暂无';
                        document.getElementById('detail-goods-name').textContent = order.goods_name;
                        document.getElementById('detail-goods-price').textContent = '￥' + order.goods_price;
                        document.getElementById('detail-quantity').textContent = order.quantity + ' 件';
                        document.getElementById('detail-total-amount').textContent = '￥' + order.total_amount;
                        document.getElementById('detail-status').innerHTML = `<span class="status-tag ${statusInfo.class}">${statusInfo.text}</span>`;
                        document.getElementById('detail-contact').textContent = order.contact;
                        document.getElementById('detail-create-time').textContent = formatTimestamp(order.create_time);
                        document.getElementById('detail-success-time').textContent = order.success_time ? formatTimestamp(order.success_time) : '暂无';
                        
                        // 显示弹窗
                        document.getElementById('orderDetailModal').style.display = 'block';
                    } else {
                        ElementPlus.ElMessage.error(data.msg || '获取订单详情失败');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    ElementPlus.ElMessage.error('获取订单详情失败，请稍后重试');
                });
            }

            // 关闭订单详情弹窗
            function closeOrderDetailModal() {
                document.getElementById('orderDetailModal').style.display = 'none';
            }

            // 格式化时间戳
            function formatTimestamp(timestamp) {
                return new Date(timestamp * 1000).toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                });
            }
        </script>
    </body>
</html> 