('D:\\编程\\插件开发\\plugin\\build\\迁移工具\\PYZ-00.pyz',
 [('OpenSSL',
   'D:\\pyfile\\py\\Lib\\site-packages\\OpenSSL\\__init__.py',
   'PYMODULE'),
  ('OpenSSL.SSL',
   'D:\\pyfile\\py\\Lib\\site-packages\\OpenSSL\\SSL.py',
   'PYMODULE'),
  ('OpenSSL._util',
   'D:\\pyfile\\py\\Lib\\site-packages\\OpenSSL\\_util.py',
   'PYMODULE'),
  ('OpenSSL.crypto',
   'D:\\pyfile\\py\\Lib\\site-packages\\OpenSSL\\crypto.py',
   'PYMODULE'),
  ('OpenSSL.version',
   'D:\\pyfile\\py\\Lib\\site-packages\\OpenSSL\\version.py',
   'PYMODULE'),
  ('PyQt5',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('__future__', 'D:\\pyfile\\py\\Lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'D:\\pyfile\\py\\Lib\\_aix_support.py', 'PYMODULE'),
  ('_bootsubprocess', 'D:\\pyfile\\py\\Lib\\_bootsubprocess.py', 'PYMODULE'),
  ('_compat_pickle', 'D:\\pyfile\\py\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'D:\\pyfile\\py\\Lib\\_compression.py', 'PYMODULE'),
  ('_markupbase', 'D:\\pyfile\\py\\Lib\\_markupbase.py', 'PYMODULE'),
  ('_py_abc', 'D:\\pyfile\\py\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\pyfile\\py\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'D:\\pyfile\\py\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_strptime', 'D:\\pyfile\\py\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'D:\\pyfile\\py\\Lib\\_threading_local.py', 'PYMODULE'),
  ('argparse', 'D:\\pyfile\\py\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\pyfile\\py\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio', 'D:\\pyfile\\py\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'D:\\pyfile\\py\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\pyfile\\py\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\pyfile\\py\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\pyfile\\py\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\pyfile\\py\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\pyfile\\py\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events', 'D:\\pyfile\\py\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\pyfile\\py\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\pyfile\\py\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures', 'D:\\pyfile\\py\\Lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.locks', 'D:\\pyfile\\py\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.log', 'D:\\pyfile\\py\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.mixins', 'D:\\pyfile\\py\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\pyfile\\py\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\pyfile\\py\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues', 'D:\\pyfile\\py\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners', 'D:\\pyfile\\py\\Lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\pyfile\\py\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto', 'D:\\pyfile\\py\\Lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.staggered',
   'D:\\pyfile\\py\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams', 'D:\\pyfile\\py\\Lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\pyfile\\py\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'D:\\pyfile\\py\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks', 'D:\\pyfile\\py\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.threads', 'D:\\pyfile\\py\\Lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.timeouts', 'D:\\pyfile\\py\\Lib\\asyncio\\timeouts.py', 'PYMODULE'),
  ('asyncio.transports',
   'D:\\pyfile\\py\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock', 'D:\\pyfile\\py\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\pyfile\\py\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\pyfile\\py\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\pyfile\\py\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64', 'D:\\pyfile\\py\\Lib\\base64.py', 'PYMODULE'),
  ('bcrypt',
   'D:\\pyfile\\py\\Lib\\site-packages\\bcrypt\\__init__.py',
   'PYMODULE'),
  ('bdb', 'D:\\pyfile\\py\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'D:\\pyfile\\py\\Lib\\bisect.py', 'PYMODULE'),
  ('brotli', 'D:\\pyfile\\py\\Lib\\site-packages\\brotli.py', 'PYMODULE'),
  ('bs4', 'D:\\pyfile\\py\\Lib\\site-packages\\bs4\\__init__.py', 'PYMODULE'),
  ('bs4.builder',
   'D:\\pyfile\\py\\Lib\\site-packages\\bs4\\builder\\__init__.py',
   'PYMODULE'),
  ('bs4.builder._html5lib',
   'D:\\pyfile\\py\\Lib\\site-packages\\bs4\\builder\\_html5lib.py',
   'PYMODULE'),
  ('bs4.builder._htmlparser',
   'D:\\pyfile\\py\\Lib\\site-packages\\bs4\\builder\\_htmlparser.py',
   'PYMODULE'),
  ('bs4.builder._lxml',
   'D:\\pyfile\\py\\Lib\\site-packages\\bs4\\builder\\_lxml.py',
   'PYMODULE'),
  ('bs4.dammit',
   'D:\\pyfile\\py\\Lib\\site-packages\\bs4\\dammit.py',
   'PYMODULE'),
  ('bs4.element',
   'D:\\pyfile\\py\\Lib\\site-packages\\bs4\\element.py',
   'PYMODULE'),
  ('bs4.formatter',
   'D:\\pyfile\\py\\Lib\\site-packages\\bs4\\formatter.py',
   'PYMODULE'),
  ('bz2', 'D:\\pyfile\\py\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\pyfile\\py\\Lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'D:\\pyfile\\py\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\pyfile\\py\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cgi', 'D:\\pyfile\\py\\Lib\\cgi.py', 'PYMODULE'),
  ('chardet',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.enums',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.escprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.johabfreq',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\johabfreq.py',
   'PYMODULE'),
  ('chardet.johabprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\johabprober.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langrussianmodel',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\langrussianmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.utf1632prober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\utf1632prober.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.version',
   'D:\\pyfile\\py\\Lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\pyfile\\py\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\pyfile\\py\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.assets',
   'D:\\pyfile\\py\\Lib\\site-packages\\charset_normalizer\\assets\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\pyfile\\py\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\pyfile\\py\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\pyfile\\py\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.md',
   'D:\\pyfile\\py\\Lib\\site-packages\\charset_normalizer\\md.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\pyfile\\py\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\pyfile\\py\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('cmd', 'D:\\pyfile\\py\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'D:\\pyfile\\py\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\pyfile\\py\\Lib\\codeop.py', 'PYMODULE'),
  ('concurrent', 'D:\\pyfile\\py\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('concurrent.futures',
   'D:\\pyfile\\py\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\pyfile\\py\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\pyfile\\py\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\pyfile\\py\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('contextlib', 'D:\\pyfile\\py\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'D:\\pyfile\\py\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'D:\\pyfile\\py\\Lib\\copy.py', 'PYMODULE'),
  ('cryptography',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.aead',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ciphers',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ciphers.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.cmac',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\cmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.decode_asn1',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\decode_asn1.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ec',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.rsa',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.pkcs12',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\pkcs12.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'D:\\pyfile\\py\\Lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cssselect',
   'D:\\pyfile\\py\\Lib\\site-packages\\cssselect\\__init__.py',
   'PYMODULE'),
  ('cssselect.parser',
   'D:\\pyfile\\py\\Lib\\site-packages\\cssselect\\parser.py',
   'PYMODULE'),
  ('cssselect.xpath',
   'D:\\pyfile\\py\\Lib\\site-packages\\cssselect\\xpath.py',
   'PYMODULE'),
  ('csv', 'D:\\pyfile\\py\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'D:\\pyfile\\py\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix', 'D:\\pyfile\\py\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes._endian', 'D:\\pyfile\\py\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('ctypes.macholib',
   'D:\\pyfile\\py\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\pyfile\\py\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\pyfile\\py\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\pyfile\\py\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util', 'D:\\pyfile\\py\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes.wintypes', 'D:\\pyfile\\py\\Lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('dataclasses', 'D:\\pyfile\\py\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'D:\\pyfile\\py\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'D:\\pyfile\\py\\Lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'D:\\pyfile\\py\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'D:\\pyfile\\py\\Lib\\dis.py', 'PYMODULE'),
  ('doctest', 'D:\\pyfile\\py\\Lib\\doctest.py', 'PYMODULE'),
  ('email', 'D:\\pyfile\\py\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\pyfile\\py\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\pyfile\\py\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'D:\\pyfile\\py\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase',
   'D:\\pyfile\\py\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime', 'D:\\pyfile\\py\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'D:\\pyfile\\py\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\pyfile\\py\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'D:\\pyfile\\py\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'D:\\pyfile\\py\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\pyfile\\py\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'D:\\pyfile\\py\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'D:\\pyfile\\py\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\pyfile\\py\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'D:\\pyfile\\py\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'D:\\pyfile\\py\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'D:\\pyfile\\py\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'D:\\pyfile\\py\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'D:\\pyfile\\py\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'D:\\pyfile\\py\\Lib\\email\\utils.py', 'PYMODULE'),
  ('fnmatch', 'D:\\pyfile\\py\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\pyfile\\py\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'D:\\pyfile\\py\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\pyfile\\py\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\pyfile\\py\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\pyfile\\py\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\pyfile\\py\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'D:\\pyfile\\py\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\pyfile\\py\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\pyfile\\py\\Lib\\hmac.py', 'PYMODULE'),
  ('html', 'D:\\pyfile\\py\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'D:\\pyfile\\py\\Lib\\html\\entities.py', 'PYMODULE'),
  ('html.parser', 'D:\\pyfile\\py\\Lib\\html\\parser.py', 'PYMODULE'),
  ('http', 'D:\\pyfile\\py\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'D:\\pyfile\\py\\Lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'D:\\pyfile\\py\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http.cookies', 'D:\\pyfile\\py\\Lib\\http\\cookies.py', 'PYMODULE'),
  ('http.server', 'D:\\pyfile\\py\\Lib\\http\\server.py', 'PYMODULE'),
  ('idna', 'D:\\pyfile\\py\\Lib\\site-packages\\idna\\__init__.py', 'PYMODULE'),
  ('idna.core',
   'D:\\pyfile\\py\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'D:\\pyfile\\py\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\pyfile\\py\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'D:\\pyfile\\py\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'D:\\pyfile\\py\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib', 'D:\\pyfile\\py\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'D:\\pyfile\\py\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\pyfile\\py\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\pyfile\\py\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'D:\\pyfile\\py\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\pyfile\\py\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\pyfile\\py\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\pyfile\\py\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\pyfile\\py\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\pyfile\\py\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\pyfile\\py\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\pyfile\\py\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\pyfile\\py\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\pyfile\\py\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\pyfile\\py\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\pyfile\\py\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\pyfile\\py\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\pyfile\\py\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\pyfile\\py\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\pyfile\\py\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\pyfile\\py\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util', 'D:\\pyfile\\py\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'D:\\pyfile\\py\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\pyfile\\py\\Lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'D:\\pyfile\\py\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'D:\\pyfile\\py\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'D:\\pyfile\\py\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\pyfile\\py\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'D:\\pyfile\\py\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('logging.handlers', 'D:\\pyfile\\py\\Lib\\logging\\handlers.py', 'PYMODULE'),
  ('lxml', 'D:\\pyfile\\py\\Lib\\site-packages\\lxml\\__init__.py', 'PYMODULE'),
  ('lxml.ElementInclude',
   'D:\\pyfile\\py\\Lib\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('lxml.cssselect',
   'D:\\pyfile\\py\\Lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('lxml.doctestcompare',
   'D:\\pyfile\\py\\Lib\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('lxml.html',
   'D:\\pyfile\\py\\Lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('lxml.html.ElementSoup',
   'D:\\pyfile\\py\\Lib\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'D:\\pyfile\\py\\Lib\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'D:\\pyfile\\py\\Lib\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'D:\\pyfile\\py\\Lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'D:\\pyfile\\py\\Lib\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'D:\\pyfile\\py\\Lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'D:\\pyfile\\py\\Lib\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'D:\\pyfile\\py\\Lib\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'D:\\pyfile\\py\\Lib\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'D:\\pyfile\\py\\Lib\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.includes',
   'D:\\pyfile\\py\\Lib\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   'D:\\pyfile\\py\\Lib\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   'D:\\pyfile\\py\\Lib\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   'D:\\pyfile\\py\\Lib\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   'D:\\pyfile\\py\\Lib\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'D:\\pyfile\\py\\Lib\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'D:\\pyfile\\py\\Lib\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'D:\\pyfile\\py\\Lib\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lzma', 'D:\\pyfile\\py\\Lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'D:\\pyfile\\py\\Lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\pyfile\\py\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'D:\\pyfile\\py\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'D:\\pyfile\\py\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'D:\\pyfile\\py\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'D:\\pyfile\\py\\Lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'D:\\pyfile\\py\\Lib\\optparse.py', 'PYMODULE'),
  ('pathlib', 'D:\\pyfile\\py\\Lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'D:\\pyfile\\py\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'D:\\pyfile\\py\\Lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'D:\\pyfile\\py\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'D:\\pyfile\\py\\Lib\\platform.py', 'PYMODULE'),
  ('pprint', 'D:\\pyfile\\py\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'D:\\pyfile\\py\\Lib\\py_compile.py', 'PYMODULE'),
  ('pydoc', 'D:\\pyfile\\py\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data', 'D:\\pyfile\\py\\Lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\pyfile\\py\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pyreadline3',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.ironpython_clipboard',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\clipboard\\ironpython_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.no_clipboard',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\clipboard\\no_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.win32_clipboard',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\clipboard\\win32_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.console',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\console\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.console.ansi',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\console\\ansi.py',
   'PYMODULE'),
  ('pyreadline3.console.console',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\console\\console.py',
   'PYMODULE'),
  ('pyreadline3.console.event',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\console\\event.py',
   'PYMODULE'),
  ('pyreadline3.console.ironpython_console',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\console\\ironpython_console.py',
   'PYMODULE'),
  ('pyreadline3.error',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\error.py',
   'PYMODULE'),
  ('pyreadline3.keysyms',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\keysyms\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.common',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\keysyms\\common.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.ironpython_keysyms',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\keysyms\\ironpython_keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.keysyms',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\keysyms\\keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.winconstants',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\keysyms\\winconstants.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\lineeditor\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.history',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\lineeditor\\history.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.lineobj',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\lineeditor\\lineobj.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.wordmatcher',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\lineeditor\\wordmatcher.py',
   'PYMODULE'),
  ('pyreadline3.logger',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\logger.py',
   'PYMODULE'),
  ('pyreadline3.modes',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\modes\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.modes.basemode',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\modes\\basemode.py',
   'PYMODULE'),
  ('pyreadline3.modes.emacs',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\modes\\emacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.notemacs',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\modes\\notemacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.vi',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\modes\\vi.py',
   'PYMODULE'),
  ('pyreadline3.py3k_compat',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\py3k_compat.py',
   'PYMODULE'),
  ('pyreadline3.release',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\release.py',
   'PYMODULE'),
  ('pyreadline3.rlmain',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\rlmain.py',
   'PYMODULE'),
  ('pyreadline3.unicode_helper',
   'D:\\pyfile\\py\\Lib\\site-packages\\pyreadline3\\unicode_helper.py',
   'PYMODULE'),
  ('queue', 'D:\\pyfile\\py\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\pyfile\\py\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\pyfile\\py\\Lib\\random.py', 'PYMODULE'),
  ('readline', 'D:\\pyfile\\py\\Lib\\site-packages\\readline.py', 'PYMODULE'),
  ('requests',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\pyfile\\py\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('runpy', 'D:\\pyfile\\py\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'D:\\pyfile\\py\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'D:\\pyfile\\py\\Lib\\selectors.py', 'PYMODULE'),
  ('shlex', 'D:\\pyfile\\py\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\pyfile\\py\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\pyfile\\py\\Lib\\signal.py', 'PYMODULE'),
  ('smtplib', 'D:\\pyfile\\py\\Lib\\smtplib.py', 'PYMODULE'),
  ('socket', 'D:\\pyfile\\py\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'D:\\pyfile\\py\\Lib\\socketserver.py', 'PYMODULE'),
  ('socks', 'D:\\pyfile\\py\\Lib\\site-packages\\socks.py', 'PYMODULE'),
  ('soupsieve',
   'D:\\pyfile\\py\\Lib\\site-packages\\soupsieve\\__init__.py',
   'PYMODULE'),
  ('soupsieve.__meta__',
   'D:\\pyfile\\py\\Lib\\site-packages\\soupsieve\\__meta__.py',
   'PYMODULE'),
  ('soupsieve.css_match',
   'D:\\pyfile\\py\\Lib\\site-packages\\soupsieve\\css_match.py',
   'PYMODULE'),
  ('soupsieve.css_parser',
   'D:\\pyfile\\py\\Lib\\site-packages\\soupsieve\\css_parser.py',
   'PYMODULE'),
  ('soupsieve.css_types',
   'D:\\pyfile\\py\\Lib\\site-packages\\soupsieve\\css_types.py',
   'PYMODULE'),
  ('soupsieve.pretty',
   'D:\\pyfile\\py\\Lib\\site-packages\\soupsieve\\pretty.py',
   'PYMODULE'),
  ('soupsieve.util',
   'D:\\pyfile\\py\\Lib\\site-packages\\soupsieve\\util.py',
   'PYMODULE'),
  ('ssl', 'D:\\pyfile\\py\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'D:\\pyfile\\py\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\pyfile\\py\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\pyfile\\py\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\pyfile\\py\\Lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'D:\\pyfile\\py\\Lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'D:\\pyfile\\py\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\pyfile\\py\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\pyfile\\py\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\pyfile\\py\\Lib\\threading.py', 'PYMODULE'),
  ('token', 'D:\\pyfile\\py\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\pyfile\\py\\Lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\pyfile\\py\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('tty', 'D:\\pyfile\\py\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\pyfile\\py\\Lib\\typing.py', 'PYMODULE'),
  ('unittest', 'D:\\pyfile\\py\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest._log', 'D:\\pyfile\\py\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('unittest.async_case',
   'D:\\pyfile\\py\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case', 'D:\\pyfile\\py\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest.loader', 'D:\\pyfile\\py\\Lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.main', 'D:\\pyfile\\py\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.result', 'D:\\pyfile\\py\\Lib\\unittest\\result.py', 'PYMODULE'),
  ('unittest.runner', 'D:\\pyfile\\py\\Lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.signals', 'D:\\pyfile\\py\\Lib\\unittest\\signals.py', 'PYMODULE'),
  ('unittest.suite', 'D:\\pyfile\\py\\Lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.util', 'D:\\pyfile\\py\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('urllib', 'D:\\pyfile\\py\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'D:\\pyfile\\py\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\pyfile\\py\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request', 'D:\\pyfile\\py\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('urllib.response', 'D:\\pyfile\\py\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib3',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib._appengine_environ',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\contrib\\_appengine_environ.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.packages',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\packages\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.backports',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\packages\\backports\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.backports.makefile',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\packages\\backports\\makefile.py',
   'PYMODULE'),
  ('urllib3.packages.six',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\packages\\six.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.request',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\request.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.queue',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\queue.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\pyfile\\py\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('webbrowser', 'D:\\pyfile\\py\\Lib\\webbrowser.py', 'PYMODULE'),
  ('win32con',
   'D:\\pyfile\\py\\Lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32evtlogutil',
   'D:\\pyfile\\py\\Lib\\site-packages\\win32\\lib\\win32evtlogutil.py',
   'PYMODULE'),
  ('winerror',
   'D:\\pyfile\\py\\Lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('xml', 'D:\\pyfile\\py\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.parsers', 'D:\\pyfile\\py\\Lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\pyfile\\py\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax', 'D:\\pyfile\\py\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\pyfile\\py\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\pyfile\\py\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler', 'D:\\pyfile\\py\\Lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\pyfile\\py\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\pyfile\\py\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc', 'D:\\pyfile\\py\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client', 'D:\\pyfile\\py\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('zipfile', 'D:\\pyfile\\py\\Lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'D:\\pyfile\\py\\Lib\\zipimport.py', 'PYMODULE')])
