-- 抽奖插件数据库表结构
-- 创建时间: 2024-06-22
-- 版本: 1.0.0

-- 1. 抽奖配置表
CREATE TABLE IF NOT EXISTS `pt_plugin_lottery_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `config_key` varchar(50) NOT NULL COMMENT '配置键',
    `config_value` text COMMENT '配置值',
    `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='抽奖配置表';

-- 2. 奖品表
CREATE TABLE IF NOT EXISTS `pt_plugin_lottery_prizes` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL COMMENT '奖品名称',
    `type` varchar(20) NOT NULL COMMENT '奖品类型',
    `probability` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '中奖概率',
    `stock` int(11) NOT NULL DEFAULT '0' COMMENT '库存数量',
    `balance_amount` decimal(10,2) DEFAULT '0.00' COMMENT '余额金额',
    `description` text COMMENT '奖品描述',
    `image` varchar(255) DEFAULT NULL COMMENT '奖品图片',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 1启用 0禁用',
    `sort` int(11) DEFAULT '0' COMMENT '排序',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `status` (`status`),
    KEY `type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='抽奖奖品表';

-- 3. 抽奖记录表
CREATE TABLE IF NOT EXISTS `pt_plugin_lottery_records` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` bigint(20) NOT NULL COMMENT '用户ID',
    `merchant_id` bigint(20) NOT NULL COMMENT '商户ID',
    `prize_id` int(11) NOT NULL COMMENT '奖品ID',
    `prize_name` varchar(100) NOT NULL COMMENT '奖品名称',
    `prize_type` varchar(20) NOT NULL COMMENT '奖品类型',
    `balance_amount` decimal(10,2) DEFAULT '0.00' COMMENT '余额金额',
    `shipped` tinyint(1) DEFAULT '0' COMMENT '是否已发货',
    `ship_time` datetime DEFAULT NULL COMMENT '发货时间',
    `balance_sent` tinyint(1) DEFAULT '0' COMMENT '余额是否已发放',
    `auto_sent` tinyint(1) DEFAULT '0' COMMENT '是否自动发放',
    `is_virtual` tinyint(1) DEFAULT '0' COMMENT '是否虚拟记录',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `user_id` (`user_id`),
    KEY `merchant_id` (`merchant_id`),
    KEY `prize_id` (`prize_id`),
    KEY `create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='抽奖记录表';

-- 4. 商户抽奖限制表
CREATE TABLE IF NOT EXISTS `pt_plugin_lottery_merchant_limits` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `merchant_id` bigint(20) NOT NULL COMMENT '商户ID',
    `daily_limit` int(11) NOT NULL DEFAULT '3' COMMENT '每日抽奖次数限制',
    `used_count` int(11) NOT NULL DEFAULT '0' COMMENT '已使用次数',
    `last_reset_date` date NOT NULL COMMENT '最后重置日期',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `merchant_id` (`merchant_id`),
    KEY `last_reset_date` (`last_reset_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商户抽奖限制表';

-- 5. 流水规则表
CREATE TABLE IF NOT EXISTS `pt_plugin_lottery_turnover_rules` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `turnover_amount` decimal(10,2) NOT NULL COMMENT '流水金额',
    `draw_times` int(11) NOT NULL COMMENT '获得抽奖次数',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 1启用 0禁用',
    `sort` int(11) DEFAULT '0' COMMENT '排序',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `status` (`status`),
    KEY `turnover_amount` (`turnover_amount`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流水规则表';

-- 6. 流水领取记录表
CREATE TABLE IF NOT EXISTS `pt_plugin_lottery_turnover_claims` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `merchant_id` bigint(20) NOT NULL COMMENT '商户ID',
    `rule_id` int(11) NOT NULL COMMENT '规则ID',
    `claim_date` date NOT NULL COMMENT '领取日期',
    `draw_times_used` int(11) DEFAULT '0' COMMENT '已使用的抽奖次数',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `merchant_rule_date` (`merchant_id`, `rule_id`, `claim_date`),
    KEY `merchant_id` (`merchant_id`),
    KEY `claim_date` (`claim_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流水领取记录表';

-- 7. 奖品类型表
CREATE TABLE IF NOT EXISTS `pt_plugin_lottery_prize_types` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `value` varchar(20) NOT NULL COMMENT '类型值',
    `label` varchar(50) NOT NULL COMMENT '类型标签',
    `tag_type` varchar(20) DEFAULT 'info' COMMENT '标签类型',
    `tag_color` varchar(20) DEFAULT NULL COMMENT '自定义标签颜色',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 1启用 0禁用',
    `sort` int(11) DEFAULT '0' COMMENT '排序',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `value` (`value`),
    KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='奖品类型表';

-- 8. 分佣抽奖次数使用记录表
CREATE TABLE IF NOT EXISTS `pt_plugin_lottery_commission_draws` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `merchant_id` bigint(20) NOT NULL COMMENT '商户ID',
    `draw_date` date NOT NULL COMMENT '抽奖日期',
    `used_count` int(11) NOT NULL DEFAULT '0' COMMENT '已使用的分佣抽奖次数',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `merchant_date` (`merchant_id`, `draw_date`),
    KEY `merchant_id` (`merchant_id`),
    KEY `draw_date` (`draw_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分佣抽奖次数使用记录表';

-- 9. 邀请关系表
CREATE TABLE IF NOT EXISTS `pt_plugin_lottery_invitations` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `inviter_id` bigint(20) NOT NULL COMMENT '邀请人ID',
    `inviter_name` varchar(50) NOT NULL COMMENT '邀请人用户名',
    `invitee_id` bigint(20) NOT NULL COMMENT '被邀请人ID',
    `invitee_name` varchar(50) NOT NULL COMMENT '被邀请人用户名',
    `invitation_code` varchar(100) DEFAULT NULL COMMENT '邀请码',
    `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0=待确认，1=已确认',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
    `confirm_time` datetime DEFAULT NULL COMMENT '确认时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `invitee_id` (`invitee_id`),
    KEY `inviter_id` (`inviter_id`),
    KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邀请关系表';

-- 10. 邀请奖励记录表
CREATE TABLE IF NOT EXISTS `pt_plugin_lottery_invitation_rewards` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `invitation_id` int(11) NOT NULL COMMENT '邀请关系ID',
    `inviter_id` bigint(20) NOT NULL COMMENT '邀请人ID',
    `inviter_name` varchar(50) NOT NULL COMMENT '邀请人用户名',
    `invitee_id` bigint(20) NOT NULL COMMENT '被邀请人ID',
    `invitee_name` varchar(50) NOT NULL COMMENT '被邀请人用户名',
    `reward_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '奖励金额',
    `reward_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '奖励状态：0=未发放，1=已发放',
    `send_time` datetime DEFAULT NULL COMMENT '发放时间',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `invitation_id` (`invitation_id`),
    KEY `inviter_id` (`inviter_id`),
    KEY `reward_status` (`reward_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邀请奖励记录表';

-- ========================================
-- 初始化配置数据
-- ========================================

-- 插入基础配置
INSERT IGNORE INTO `pt_plugin_lottery_config` (`config_key`, `config_value`, `description`) VALUES
('status', '1', '抽奖状态：1=开启，0=关闭'),
('daily_limit', '3', '每日抽奖次数限制'),
('start_hour', '00:00', '抽奖开始时间'),
('end_hour', '23:59', '抽奖结束时间'),
('show_probability', '0', '是否显示中奖概率：1=显示，0=不显示'),
('auto_send_balance', '1', '是否自动发放余额：1=自动，0=手动'),
('commission_draw_enabled', '0', '是否启用下级商家销售分佣获取抽奖次数'),
('commission_draw_amount', '1.00', '每多少分佣金额获得1次抽奖'),
('commission_draw_max', '10', '每日通过分佣最多获得抽奖次数'),
('turnover_reset_period', 'daily', '流水统计重置周期：daily=每日，weekly=每周，monthly=每月'),
('invitation_enabled', '0', '是否启用邀请奖励功能'),
('invitation_reward_amount', '10.00', '邀请奖励金额'),
('invitation_auto_send', '1', '是否自动发放邀请奖励'),
('hidden_prizes', '[]', '隐藏奖品列表'),
('auto_update_status', '0', 'Hook自动更新状态：1=开启，0=关闭'),
('update_interval', '3600', 'Hook更新间隔（秒）'),
('last_update', '0', '最后更新时间戳');

-- 插入默认奖品类型
INSERT IGNORE INTO `pt_plugin_lottery_prize_types` (`value`, `label`, `tag_type`, `tag_color`, `status`, `sort`) VALUES
('physical', '实物奖品', 'warning', NULL, 1, 1),
('virtual', '虚拟奖品', 'success', NULL, 1, 2),
('cash', '现金奖励', 'danger', NULL, 1, 3),
('coupon', '优惠券', 'info', NULL, 1, 4),
('points', '积分奖励', 'primary', NULL, 1, 5),
('thanks', '谢谢参与', 'info', '#909399', 1, 99);

-- 插入示例奖品
INSERT IGNORE INTO `pt_plugin_lottery_prizes` (`name`, `type`, `probability`, `stock`, `balance_amount`, `description`, `status`, `sort`) VALUES
('谢谢参与', 'thanks', 50.00, 999999, 0.00, '感谢您的参与！', 1, 99),
('1元现金', 'cash', 20.00, 100, 1.00, '获得1元现金奖励', 1, 1),
('5元现金', 'cash', 15.00, 50, 5.00, '获得5元现金奖励', 1, 2),
('10元现金', 'cash', 10.00, 20, 10.00, '获得10元现金奖励', 1, 3),
('优惠券', 'coupon', 3.00, 30, 0.00, '获得优惠券一张', 1, 4),
('积分奖励', 'points', 2.00, 50, 0.00, '获得100积分', 1, 5);

-- 插入示例流水规则
INSERT IGNORE INTO `pt_plugin_lottery_turnover_rules` (`turnover_amount`, `draw_times`, `status`, `sort`) VALUES
(100.00, 1, 1, 1),
(500.00, 3, 1, 2),
(1000.00, 5, 1, 3),
(2000.00, 2, 1, 4),
(5000.00, 5, 1, 5),
(10000.00, 10, 1, 6);
