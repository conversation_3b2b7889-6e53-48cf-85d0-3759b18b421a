(function () {
    // 添加全局错误处理器
    window.addEventListener('error', function(event) {
        if (event.error && event.error.message &&
            event.error.message.includes('message channel closed')) {
            console.warn('捕获到消息通道关闭错误，已忽略:', event.error.message);
            event.preventDefault();
            return false;
        }
    });

    // 添加未处理的Promise拒绝处理器
    window.addEventListener('unhandledrejection', function(event) {
        if (event.reason && event.reason.message &&
            event.reason.message.includes('message channel closed')) {
            console.warn('捕获到未处理的Promise拒绝（消息通道关闭），已忽略:', event.reason.message);
            event.preventDefault();
            return false;
        }
    });

    // 获取店铺信息
    function getShopInfo() {
        let shopName = '';
        let merchantId = '';
        let shopIdentifier = ''; // 存储店铺标识符
        
        // 从URL中提取店铺标识符 (如 /shop/XH1ASH6I 中的 XH1ASH6I)
        const currentUrl = window.location.href;
        const shopUrlMatch = currentUrl.match(/\/shop\/([A-Z0-9]+)/i);
        if (shopUrlMatch && shopUrlMatch[1]) {
            shopIdentifier = shopUrlMatch[1];
        }
        
        // 从用户模型获取店铺信息
        const shopInfoScript = document.querySelector('script[data-shop-info]');
        if (shopInfoScript) {
            try {
                const shopInfo = JSON.parse(shopInfoScript.textContent);
                if (shopInfo.shopName) {
                    shopName = shopInfo.shopName;
                }
                if (shopInfo.merchantId) {
                    merchantId = shopInfo.merchantId;
                }
            } catch (e) {
                // 错误处理已移除
            }
        }
        
        // 如果无法通过上面方式获取，尝试其他方式
        if (!shopName) {
            const nicknameElement = document.querySelector('.nickname');
            if (nicknameElement) {
                shopName = nicknameElement.textContent.trim();
            }
        }

        if (!merchantId) {
            const merchantIdElement = document.querySelector('[data-merchant-id]');
            if (merchantIdElement) {
                merchantId = merchantIdElement.getAttribute('data-merchant-id');
            }
        }
        
        // 调试输出已移除
        return { shopName, merchantId, shopIdentifier };
    }

    // 创建客服按钮组
    function createCustomerServiceButtons() {
        const buttonGroup = document.createElement('div');
        buttonGroup.className = 'arco-space arco-space-horizontal arco-space-align-center customer-service-buttons';
        buttonGroup.style.position = 'absolute';
        buttonGroup.style.top = '50%';
        buttonGroup.style.left = '50%';
        buttonGroup.style.transform = 'translate(-50%, -50%)';
        buttonGroup.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';
        buttonGroup.style.borderRadius = '20px';
        buttonGroup.style.padding = '5px 10px';
        buttonGroup.style.zIndex = '999';
        buttonGroup.style.backdropFilter = 'blur(5px)';
        buttonGroup.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.15)';
        
        // 私信按钮
        const messageButton = createButton('私信', 'message', true);
        messageButton.style.marginRight = '13px';
        
        // 在线客服按钮（新增）
        const customerServiceButton = createButton('在线客服', 'message', true);
        customerServiceButton.style.marginRight = '13px';
        // 为在线客服按钮添加特殊点击事件
        customerServiceButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation(); // 阻止事件冒泡
            // 直接打开指定路径
            window.open('/plugin/Customersystem/api/chat', '_blank');
        });
        
        // 分享按钮
        const shareButton = createButton('分享', 'share-external', true);
        shareButton.style.marginRight = '13px';
        
        // 更多按钮
        const moreButton = createButton('', 'more', false);
        
        // 添加按钮到组
        buttonGroup.appendChild(messageButton);
        buttonGroup.appendChild(customerServiceButton); // 添加在线客服按钮
        buttonGroup.appendChild(shareButton);
        buttonGroup.appendChild(moreButton);
        
        return buttonGroup;
    }
    
    // 创建按钮
    function createButton(text, iconName, hasText) {
        const button = document.createElement('button');
        button.className = `arco-btn arco-btn-${hasText ? 'dashed' : 'text'} arco-btn-shape-round arco-btn-size-mini arco-btn-status-normal ${!hasText ? 'arco-btn-only-icon' : ''}`;
        button.type = 'button';
        
        const iconSvg = getIconSvg(iconName);
        
        button.innerHTML = `<span class="arco-btn-icon">${iconSvg}</span> ${hasText ? text : ''}`;
        
        // 添加事件监听
        button.addEventListener('click', function(e) {
            e.preventDefault();
            handleButtonClick(iconName);
        });
        
        return button;
    }
    
    // 获取图标SVG
    function getIconSvg(iconName) {
        const icons = {
            'message': '<svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="currentColor" class="arco-icon arco-icon-message" stroke-width="4" stroke-linecap="butt" stroke-linejoin="miter"><path d="M15 20h18m-18 9h9M7 41h17.63C33.67 41 41 33.67 41 24.63V24c0-9.389-7.611-17-17-17S7 14.611 7 24v17Z"></path></svg>',
            'share-external': '<svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="currentColor" class="arco-icon arco-icon-share-external" stroke-width="4" stroke-linecap="butt" stroke-linejoin="miter"><path d="M18 20h-7a1 1 0 0 0-1 1v20a1 1 0 0 0 1 1h26a1 1 0 0 0 1-1V21a1 1 0 0 0-1-1h-7m2.368-5.636L24.004 6l-8.364 8.364M24.003 28V6.604" stroke-miterlimit="16"></path></svg>',
            'more': '<svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="currentColor" class="arco-icon arco-icon-more" stroke-width="4" stroke-linecap="butt" stroke-linejoin="miter"><path d="M38 25v-2h2v2h-2ZM23 25v-2h2v2h-2ZM8 25v-2h2v2H8Z" fill="currentColor" stroke="none"></path><path d="M38 25v-2h2v2h-2ZM23 25v-2h2v2h-2ZM8 25v-2h2v2H8Z"></path></svg>'
        };
        
        return icons[iconName] || '';
    }
    
    // 处理按钮点击
    function handleButtonClick(buttonType) {
        const { shopName, merchantId } = getShopInfo();
        
        switch (buttonType) {
            case 'message':
                // 处理私信功能
                openCustomerService(shopName, merchantId);
                break;
            case 'share-external':
                // 处理分享功能
                shareContent(shopName);
                break;
            case 'more':
                // 处理更多功能
                showMoreOptions();
                break;
        }
    }
    
    // 打开客服窗口
    function openCustomerService(shopName, merchantId) {
        // 构建客服URL
        let serviceUrl = '/plugin/Customersystem/Api/chat';
        const params = new URLSearchParams();
        if (shopName) params.append('shop_name', shopName);
        if (merchantId) params.append('merchant_id', merchantId);
        
        // 获取完整店铺信息，包括新增的shopIdentifier
        const { shopIdentifier } = getShopInfo();
        
        // 添加店铺标识参数
        if (shopIdentifier) {
            params.append('shop_id', shopIdentifier);
            
            // 存储店铺标识到localStorage以便在客服页面使用
            localStorage.setItem('customer_shop_identifier', shopIdentifier);
        }
        
        // 提取店铺尾号信息
        let shopToken = '';
        
        // 方法1: 从URL中提取店铺尾号
        const urlParams = new URLSearchParams(window.location.search);
        const tokenFromUrl = urlParams.get('token') || '';
        if (tokenFromUrl) {
            shopToken = tokenFromUrl;
        }
        
        // 方法2: 尝试从页面元素中查找店铺尾号
        if (!shopToken) {
            const tokenEl = document.querySelector('[data-shop-token]');
            if (tokenEl) {
                shopToken = tokenEl.getAttribute('data-shop-token') || '';
            }
        }
        
        // 方法3: 尝试从店铺ID中提取后8位作为尾号
        if (!shopToken && merchantId && merchantId.length > 8) {
            shopToken = merchantId.slice(-8);
        }
        
        // 添加店铺尾号参数
        if (shopToken) {
            params.append('shop_token', shopToken);
        }
        
        serviceUrl = serviceUrl + '?' + params.toString();
        
        // 直接跳转到客服页面，而不是创建小窗口
        window.open(serviceUrl, '_blank');
    }
    
    // 分享内容
    function shareContent(shopName) {
        // 获取当前页面信息
        const title = document.title || shopName || '商城';
        const url = window.location.href;
        
        // 创建分享弹窗
        const shareWindow = document.createElement('div');
        shareWindow.className = 'share-window';
        shareWindow.innerHTML = `<div class="share-header"> <span>分享给朋友</span> <button class="close-btn">&times;</button> </div> <div class="share-content"> <div class="share-item" data-type="wechat"> <div class="share-icon wechat"></div> <div>微信</div> </div> <div class="share-item" data-type="qq"> <div class="share-icon qq"></div> <div>QQ</div> </div> <div class="share-item" data-type="weibo"> <div class="share-icon weibo"></div> <div>微博</div> </div> <div class="share-item" data-type="link"> <div class="share-icon link"></div> <div>复制链接</div> </div> </div>`;
        
        // 添加样式
        const style = document.createElement('style');
        style.textContent = `.share-window { position: fixed; bottom: 0; left: 0; width: 100%; background: white; border-radius: 8px 8px 0 0; box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1); z-index: 99999; } .share-header { display: flex; justify-content: space-between; align-items: center; padding: 15px; border-bottom: 1px solid #f0f0f0; } .close-btn { background: none; border: none; font-size: 20px; cursor: pointer; } .share-content { display: flex; padding: 20px; justify-content: space-around; } .share-item { display: flex; flex-direction: column; align-items: center; cursor: pointer; } .share-icon { width: 40px; height: 40px; border-radius: 50%; margin-bottom: 8px; background-color: #f0f0f0; }`;
        
        document.head.appendChild(style);
        document.body.appendChild(shareWindow);
        
        // 添加关闭按钮事件
        const closeBtn = shareWindow.querySelector('.close-btn');
        if (closeBtn) {
            closeBtn.addEventListener('click', function() {
                shareWindow.remove();
            });
        }
        
        // 添加分享项点击事件
        const shareItems = shareWindow.querySelectorAll('.share-item');
        shareItems.forEach(item => {
            item.addEventListener('click', function() {
                const type = this.getAttribute('data-type');
                if (type === 'link') {
                    // 复制链接到剪贴板
                    navigator.clipboard.writeText(url)
                        .then(() => {
                            alert('链接已复制到剪贴板');
                            shareWindow.remove();
                        })
                        .catch(err => {
                            // 错误处理已移除
                        });
                } else {
                    // 其他分享类型可以根据需要实现
                    alert(`分享到${type}功能正在开发中`);
                    shareWindow.remove();
                }
            });
        });
    }
    
    // 显示更多选项
    function showMoreOptions() {
        // 实现更多选项菜单
        const moreMenu = document.createElement('div');
        moreMenu.className = 'more-options-menu';
        moreMenu.innerHTML = `<div class="menu-item">收藏</div> <div class="menu-item">举报</div> <div class="menu-item">不感兴趣</div>`;
        
        // 添加样式
        const style = document.createElement('style');
        style.textContent = `.more-options-menu { position: absolute; background: white; border-radius: 4px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); z-index: 1000; padding: 5px 0; min-width: 100px; } .menu-item { padding: 8px 12px; cursor: pointer; } .menu-item:hover { background-color: #f5f5f5; }`;
        
        document.head.appendChild(style);
        
        // 获取更多按钮位置
        const moreButton = document.querySelector('.arco-btn-only-icon');
        if (moreButton) {
            const rect = moreButton.getBoundingClientRect();
            moreMenu.style.top = `${rect.bottom + window.scrollY + 5}px`;
            moreMenu.style.left = `${rect.left + window.scrollX - 70}px`;
            
            document.body.appendChild(moreMenu);
            
            // 点击其他区域关闭菜单
            document.addEventListener('click', function closeMenu(e) {
                if (!moreMenu.contains(e.target) && e.target !== moreButton) {
                    moreMenu.remove();
                    document.removeEventListener('click', closeMenu);
                }
            });
            
            // 菜单项点击事件
            const menuItems = moreMenu.querySelectorAll('.menu-item');
            menuItems.forEach(item => {
                item.addEventListener('click', function() {
                    alert(`${this.textContent}功能正在开发中`);
                    moreMenu.remove();
                });
            });
        }
    }

    // 查找产品图片并添加客服按钮
    function findProductImagesAndAddButtons() {
        // 不再向图片添加按钮
        return;
    }
    
    // 为图片添加客服按钮
    function addButtonsToImage(imgElement) {
        // 不再向图片添加按钮
        return;
    }

    // 新增：直接在页面上插入在线客服按钮
    function insertCustomerServiceButton() {
        // 先检查是否已经存在客服按钮
        const existingButton = document.querySelector('.customer-service-unique-btn');
        if (existingButton) {
            // 如果已存在按钮，则不再添加
            return;
        }
        
        // 查找分享按钮（同时查找圆形和方形按钮）
        const shareButtons = document.querySelectorAll('.arco-btn-shape-round, .arco-btn-shape-square');
        let buttonAdded = false; // 添加标志，确保只添加一次按钮
        
        shareButtons.forEach(button => {
            if (buttonAdded) return; // 如果已经添加了按钮，就跳过
            
            // 检查是否为分享按钮
            if (button.textContent.trim().includes('分享')) {
                const parentItem = button.closest('.arco-space-item');
                if (parentItem) {
                    // 检查是否已经添加了客服按钮
                    const nextItem = parentItem.nextElementSibling;
                    if (nextItem && nextItem.textContent.trim().includes('在线客服')) {
                        // 已存在客服按钮，不再添加
                        buttonAdded = true;
                        return;
                    }
                    
                    // 创建新的客服按钮
                    const newItem = document.createElement('div');
                    newItem.className = 'arco-space-item customer-service-unique-btn';
                    // 复制父元素的样式
                    if (parentItem.hasAttribute('style')) {
                        newItem.setAttribute('style', parentItem.getAttribute('style'));
                    }
                    
                    // 创建按钮 - 完全复制原始分享按钮的样式
                    const serviceButton = document.createElement('button');
                    
                    // 复制原始按钮的所有类名
                    serviceButton.className = button.className;
                    
                    // 复制原始按钮的内联样式
                    if (button.hasAttribute('style')) {
                        serviceButton.setAttribute('style', button.getAttribute('style'));
                    }
                    
                    // 复制原始按钮的所有自定义数据属性
                    Array.from(button.attributes).forEach(attr => {
                        if (attr.name.startsWith('data-')) {
                            serviceButton.setAttribute(attr.name, attr.value);
                        }
                    });
                    
                    // 设置按钮内容 - 使用与原按钮相同的图标结构
                    const btnIcon = button.querySelector('.arco-btn-icon');
                    if (btnIcon) {
                        // 复制原始按钮的图标结构，但替换成客服的图标
                        serviceButton.innerHTML = `
                            <span class="arco-btn-icon">
                                <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="currentColor" class="arco-icon arco-icon-message" stroke-width="4" stroke-linecap="butt" stroke-linejoin="miter">
                                    <path d="M15 20h18m-18 9h9M7 41h17.63C33.67 41 41 33.67 41 24.63V24c0-9.389-7.611-17-17-17S7 14.611 7 24v17Z"></path>
                                </svg>
                            </span>
                            在线客服
                        `;
                    } else {
                        // 如果原始按钮没有图标，则只添加文本
                        serviceButton.textContent = '在线客服';
                    }
                    
                    // 添加点击事件
                    serviceButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        // 获取店铺信息
                        const { shopName, merchantId } = getShopInfo();
                        // 使用店铺信息打开客服窗口
                        openCustomerService(shopName, merchantId);
                    });
                    
                    // 将按钮添加到新元素中
                    newItem.appendChild(serviceButton);
                    
                    // 将新元素插入到分享按钮后面
                    if (parentItem.nextSibling) {
                        parentItem.parentNode.insertBefore(newItem, parentItem.nextSibling);
                    } else {
                        parentItem.parentNode.appendChild(newItem);
                    }
                    
                    buttonAdded = true; // 标记已添加按钮
                }
            }
        });
        
        // 如果没有找到分享按钮，就使用原来的逻辑继续查找其他位置
        if (!buttonAdded) {
            // 查找私信按钮的父元素
            const buttons = document.querySelectorAll('.arco-space-item button');
            
            // 查找包含"分享此商品"的元素
            const shareElements = document.querySelectorAll('.arco-space');
            
            shareElements.forEach(element => {
                if (buttonAdded) return; // 如果已经添加了按钮，就跳过
                
                if (element.textContent.trim().includes('分享此商品')) {
                    const parentElement = element.closest('.goods_share');
                    if (parentElement) {
                        // 检查该父元素后面是否已经有客服按钮（防止重复添加）
                        const nextElement = parentElement.nextElementSibling;
                        if (nextElement && 
                            nextElement.textContent.trim().includes('在线客服') && 
                            nextElement.classList.contains('arco-space')) {
                            // 已经存在客服按钮，不再添加
                            buttonAdded = true;
                            return;
                        }
                        
                        // 创建在线客服按钮
                        const customerServiceBtn = document.createElement('div');
                        customerServiceBtn.className = 'arco-space arco-space-horizontal arco-space-align-center customer-service-unique-btn';
                        customerServiceBtn.style.marginTop = '10px';
                        customerServiceBtn.style.display = 'flex';
                        customerServiceBtn.style.alignItems = 'center';
                        customerServiceBtn.style.justifyContent = 'center'; // 添加水平居中
                        customerServiceBtn.style.color = '#f56a00'; // 使用橙色，与图片中的其他元素颜色相协调
                        customerServiceBtn.style.borderRadius = '4px';
                        customerServiceBtn.style.padding = '8px 12px';
                        customerServiceBtn.style.border = '1px solid #f0f0f0';
                        customerServiceBtn.style.backgroundColor = '#fff';
                        customerServiceBtn.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.05)';
                        customerServiceBtn.style.animation = 'pulse 2s infinite';
                        customerServiceBtn.style.width = '100%'; // 设置宽度为100%以便居中效果更明显
                        
                        // 添加闪烁动画的关键帧
                        if (!document.getElementById('customer-service-animation')) {
                            const style = document.createElement('style');
                            style.id = 'customer-service-animation';
                            style.textContent = `
                                @keyframes pulse {
                                    0% {
                                        color: #f56a00;
                                        border-color: #f56a00;
                                        transform: scale(1);
                                    }
                                    50% {
                                        color: #ff8c00;
                                        border-color: #ff8c00;
                                        box-shadow: 0 0 10px rgba(255, 140, 0, 0.4);
                                        transform: scale(1.03);
                                    }
                                    100% {
                                        color: #f56a00;
                                        border-color: #f56a00;
                                        transform: scale(1);
                                    }
                                }
                            `;
                            document.head.appendChild(style);
                        }
                        
                        customerServiceBtn.innerHTML = `
                            <div class="arco-space-item" style="margin-right: 8px; display: flex; align-items: center;">
                                <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="currentColor" class="arco-icon arco-icon-message" stroke-width="4" stroke-linecap="butt" stroke-linejoin="miter" style="width: 18px; height: 18px; color: #f56a00;">
                                    <path d="M15 20h18m-18 9h9M7 41h17.63C33.67 41 41 33.67 41 24.63V24c0-9.389-7.611-17-17-17S7 14.611 7 24v17Z"></path>
                                </svg>
                            </div>
                            <div class="arco-space-item" style="display: flex; align-items: center;">在线客服</div>
                        `;
                        
                        // 添加点击事件
                        customerServiceBtn.addEventListener('click', function(e) {
                            e.preventDefault();
                            // 获取店铺信息
                            const { shopName, merchantId } = getShopInfo();
                            // 使用店铺信息打开客服窗口
                            openCustomerService(shopName, merchantId);
                        });
                        
                        // 添加鼠标样式
                        customerServiceBtn.style.cursor = 'pointer';
                        
                        // 添加到"分享此商品"按钮下方
                        parentElement.insertAdjacentElement('afterend', customerServiceBtn);
                        buttonAdded = true; // 标记已添加按钮
                    }
                }
            });
            
            // 只有在还没有添加按钮的情况下，才继续检查私信按钮
            if (!buttonAdded) {
                buttons.forEach(button => {
                    // 查找包含"私信"文本的按钮
                    if (buttonAdded) return; // 如果已经添加了按钮，就跳过
                    
                    if (button.textContent.trim().includes('私信')) {
                        // 获取父级的.arco-space-item元素
                        const parentItem = button.closest('.arco-space-item');
                        if (parentItem) {
                            // 检查是否已经添加了客服按钮
                            const nextItem = parentItem.nextElementSibling;
                            if (nextItem && nextItem.textContent.trim().includes('在线客服')) {
                                // 已存在客服按钮，不再添加
                                buttonAdded = true;
                                return;
                            }
                            
                            // 克隆私信按钮的父元素
                            const newItem = parentItem.cloneNode(true);
                            // 修改克隆元素内部按钮的文本
                            const newButton = newItem.querySelector('button');
                            if (newButton) {
                                // 保留SVG图标，只修改文本部分
                                const btnIcon = newButton.querySelector('.arco-btn-icon');
                                if (btnIcon) {
                                    // 清除按钮内容，保留图标
                                    newButton.innerHTML = '';
                                    newButton.appendChild(btnIcon);
                                    newButton.appendChild(document.createTextNode(' 在线客服 '));
                                    
                                    // 添加点击事件
                                    newButton.addEventListener('click', function(e) {
                                        e.preventDefault();
                                        // 获取店铺信息
                                        const { shopName, merchantId } = getShopInfo();
                                        // 使用店铺信息打开客服窗口
                                        openCustomerService(shopName, merchantId);
                                    });
                                }
                            }
                            
                            // 给克隆的按钮添加唯一标识类
                            newItem.classList.add('customer-service-unique-btn');
                            
                            // 插入到私信按钮后面
                            if (parentItem.nextSibling) {
                                parentItem.parentNode.insertBefore(newItem, parentItem.nextSibling);
                            } else {
                                parentItem.parentNode.appendChild(newItem);
                            }
                            
                            buttonAdded = true; // 标记已添加按钮
                        }
                        
                        // 找到第一个私信按钮后就退出
                        return;
                    }
                });
            }
        }
    }

    // 初始化函数
    function init() {
        // 清除所有可能的旧按钮
        document.querySelectorAll('.customer-service-buttons').forEach(btn => {
            btn.remove();
        });
        
        // 不再添加图片上的客服按钮
        // findProductImagesAndAddButtons();
        
        // 只保留：直接插入在线客服按钮
        insertCustomerServiceButton();
        
        // 添加"联系TA"左侧的在线客服按钮
        addCustomerServiceNextToContact();
        
        // 检查是否在客服页面，如果是则填充店铺标识
        autoFillShopIdentifier();
        
        // 防止重复观察
        if (window.customerServiceObserver) {
            window.customerServiceObserver.disconnect();
        }
        
        // 监听DOM变化，只处理客服按钮
        const observer = new MutationObserver((mutations) => {
            try {
                // 使用节流函数减少处理频率
                if (window.csThrottleTimer) {
                    clearTimeout(window.csThrottleTimer);
                }

                window.csThrottleTimer = setTimeout(() => {
                    try {
                        // 不再检查新图片
                        // findProductImagesAndAddButtons();

                        // 改进检查逻辑：先检查所有已存在的客服按钮
                        const existingButtons = document.querySelectorAll('.customer-service-unique-btn');

                        // 如果没有找到任何客服按钮，才添加新按钮
                        if (existingButtons.length === 0) {
                            insertCustomerServiceButton();
                        }

                        // 检查"联系TA"左侧的客服按钮
                        const existingContactButtons = document.querySelectorAll('.cs-contact-button');
                        if (existingContactButtons.length === 0) {
                            addCustomerServiceNextToContact();
                        }
                    } catch (error) {
                        console.error('客服按钮处理错误:', error);
                    }
                }, 500); // 延迟一点时间确保元素完全加载
            } catch (error) {
                console.error('MutationObserver 错误:', error);
            }
        });
        
        // 保存观察器引用
        window.customerServiceObserver = observer;
        
        // 开始观察整个文档的变化
        observer.observe(document.body, { childList: true, subtree: true });
    }

    // 新增：在"联系TA"按钮左侧添加客服按钮
    function addCustomerServiceNextToContact() {
        // 查找所有"联系TA"按钮
        const contactButtons = document.querySelectorAll('.contact');
        
        contactButtons.forEach(contactBtn => {
            // 检查是否已经添加了客服按钮
            if (contactBtn.previousElementSibling && contactBtn.previousElementSibling.classList.contains('cs-contact-button')) {
                return; // 已存在客服按钮，跳过
            }
            
            // 检查是否包含"联系TA"文本
            if (contactBtn.textContent.trim().includes('联系TA')) {
                // 创建在线客服按钮，完全复制联系TA的样式
                const csButton = document.createElement('div');
                
                // 直接复制联系TA的所有class
                csButton.className = contactBtn.className;
                // 添加标识类，但不影响样式
                csButton.classList.add('cs-contact-button');
                
                // 复制所有的data属性
                Array.from(contactBtn.attributes).forEach(attr => {
                    if (attr.name.startsWith('data-')) {
                        csButton.setAttribute(attr.name, attr.value);
                    }
                });
                
                // 复制联系TA按钮的样式
                if (contactBtn.hasAttribute('style')) {
                    csButton.setAttribute('style', contactBtn.getAttribute('style'));
                }
                
                // 确保按钮间有合适的间距
                csButton.style.marginRight = '10px';
                
                // 使用与联系TA完全相同的SVG结构，但使用客服图标
                csButton.innerHTML = `<svg style="margin-right:2px;" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="15" height="15" ${contactBtn.querySelector('svg').hasAttribute('data-v-27fed50e') ? 'data-v-27fed50e=""' : ''}>
                    <path d="M811 116H221c-86.156 0-156 69.844-156 156v409l0.02 2.58C66.4 768.546 135.706 837 221 837h175.344l88.255 82.53 0.532 0.49c17.664 16.013 44.703 15.885 62.218-0.408L636.154 837H811c86.156 0 156-69.844 156-156V272c0-86.156-69.844-156-156-156z m0 72c46.392 0 84 37.608 84 84v409c0 46.392-37.608 84-84 84H622l-0.535 0.004a36 36 0 0 0-23.985 9.637l-81.416 75.737-80.921-75.672a36 36 0 0 0-24.59-9.706H221c-46.392 0-84-37.608-84-84V272c0-46.392 37.608-84 84-84h590z" fill="currentColor" ${contactBtn.querySelector('path').hasAttribute('data-v-27fed50e') ? 'data-v-27fed50e=""' : ''}></path>
                    <path d="M380 420h270M380 500h180M380 580h90" fill="none" stroke="currentColor" stroke-width="45" stroke-linecap="round" ${contactBtn.querySelector('path').hasAttribute('data-v-27fed50e') ? 'data-v-27fed50e=""' : ''}></path>
                </svg> 在线客服`;
                
                // 添加点击事件 - 修改为使用openCustomerService
                csButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // 获取店铺信息
                    const { shopName, merchantId } = getShopInfo();
                    // 使用店铺信息打开客服窗口
                    openCustomerService(shopName, merchantId);
                });
                
                // 添加鼠标样式
                csButton.style.cursor = 'pointer';
                
                // 插入到联系TA按钮前面
                contactBtn.parentNode.insertBefore(csButton, contactBtn);
            }
        });
    }

    // 新增：自动填充店铺标识的函数
    function autoFillShopIdentifier() {
        try {
            // 检查当前页面是否为客服页面
            if (window.location.pathname.includes('/plugin/Customersystem/api/chat') ||
                window.location.pathname.includes('/plugin/Customersystem/Api/chat')) {

                // 延迟执行以确保DOM完全加载
                setTimeout(function() {
                    try {
                        // 从localStorage获取存储的店铺标识
                        const shopIdentifier = localStorage.getItem('customer_shop_identifier');

                        if (shopIdentifier) {
                            // 查找输入框 - 尝试多种可能的选择器
                            const inputElements = [
                                document.querySelector('input[placeholder*="765U90CA"]'),
                                document.querySelector('#el-id-545-13'),
                                document.querySelector('.el-input__inner[placeholder*="例如"]'),
                                document.querySelector('.el-input__inner[id*="el-id"]')
                            ].filter(Boolean); // 过滤掉null或undefined

                            if (inputElements.length > 0) {
                                // 找到第一个匹配的输入框
                                const inputElement = inputElements[0];

                                // 填充店铺标识
                                inputElement.value = shopIdentifier;

                                // 触发输入事件，确保框架能正确响应
                                const inputEvent = new Event('input', { bubbles: true });
                                inputElement.dispatchEvent(inputEvent);

                                const changeEvent = new Event('change', { bubbles: true });
                                inputElement.dispatchEvent(changeEvent);

                                // 可以尝试触发Vue/Element UI的v-model更新
                                if (window.__vue__) {
                                    try {
                                        // 尝试获取Vue实例并更新模型
                                        const vnode = inputElement.__vnode || inputElement._vnode;
                                        if (vnode && vnode.componentInstance) {
                                            vnode.componentInstance.$emit('input', shopIdentifier);
                                        }
                                    } catch (e) {
                                        console.warn('Vue实例更新失败:', e);
                                    }
                                }

                                console.log('已自动填充店铺标识:', shopIdentifier);
                            } else {
                                // 如果找不到输入框，再次尝试（可能DOM还在加载中）
                                // 限制重试次数，避免无限递归
                                if (!window.autoFillRetryCount) {
                                    window.autoFillRetryCount = 0;
                                }
                                if (window.autoFillRetryCount < 5) {
                                    window.autoFillRetryCount++;
                                    setTimeout(autoFillShopIdentifier, 500);
                                }
                            }
                        }
                    } catch (error) {
                        console.error('自动填充店铺标识错误:', error);
                    }
                }, 800); // 延迟800ms执行
            }
        } catch (error) {
            console.error('autoFillShopIdentifier 函数错误:', error);
        }
    }

    // 当DOM加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
})();