<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <title>HTML弹窗模板管理</title>
    <style>
        /* 响应式设计增强 */
        @media screen and (max-width: 768px) {
            body {
                padding: 8px;
                font-size: 14px;
            }
            .el-card {
                border-radius: 8px;
            }
            .card-header span {
                font-size: 16px;
            }
            .el-form-item__label {
                font-size: 13px;
            }
        }

        /* 自适应间距 */
        .el-form-item {
            margin-bottom: clamp(20px, 4vw, 28px);
        }

        /* 优化触摸区域 */
        .el-button {
            min-height: 44px;
        }

        /* 模板预览样式 */
        .template-preview {
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            background: #fafafa;
        }

        .template-preview:hover {
            background: #f0f9eb;
            border-color: #67c23a;
        }

        .template-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .template-name {
            font-weight: bold;
            color: #303133;
            font-size: 16px;
        }

        .template-actions {
            margin-top: 12px;
            text-align: right;
        }

        /* 编辑表单样式 */
        .edit-form {
            background: #f0f9eb;
            border: 1px solid #67c23a;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .edit-form .el-form-item__label {
            color: #67c23a;
            font-weight: bold;
        }

        /* 暗黑模式支持 */
        @media (prefers-color-scheme: dark) {
            body {
                background: #1a1a1a;
            }
            .el-card {
                background: #2b2b2b;
                border-color: #3a3a3a;
            }
            .template-preview {
                background: #333;
                border-color: #444;
            }
        }

        /* 模板预览iframe样式 */
        .template-iframe {
            width: 100%;
            height: 400px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            background: white;
        }
    </style>
</head>

<body>
    <div id="app">
        <el-card shadow="never">
            <template #header>
                <div class="card-header">
                    <span>HTML弹窗模板管理</span>
                    <div style="margin-top: 8px; color: #909399; font-size: 12px;">
                        <i class="el-icon-info"></i> 管理HTML弹窗模板的文本内容，样式已固定无需修改
                    </div>
                </div>
            </template>

            <!-- 全局控制开关 -->
            <div style="margin-bottom: 20px; padding: 16px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #409eff;">
                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 12px;">
                    <div>
                        <span style="font-weight: 600; color: #2c3e50;">弹窗总开关</span>
                        <div style="color: #909399; font-size: 12px; margin-top: 4px;">
                            控制整个弹窗系统的启用状态，关闭后所有商家的弹窗都不会显示
                        </div>
                    </div>
                    <el-switch
                        v-model="globalSettings.popupEnabled"
                        @change="saveGlobalSettings"
                        :loading="globalLoading"
                    ></el-switch>
                </div>
            </div>

            <!-- 弹窗全局配置 -->
            <div style="margin-bottom: 20px; padding: 16px; background: #f0f9eb; border-radius: 8px; border-left: 4px solid #67c23a;">
                <div style="margin-bottom: 16px;">
                    <span style="font-weight: 600; color: #2c3e50; font-size: 16px;">弹窗全局配置</span>
                    <div style="color: #909399; font-size: 12px; margin-top: 4px;">
                        <i class="el-icon-info"></i> 配置所有弹窗的默认行为和样式
                    </div>
                </div>

                <el-form :model="popupConfig" label-width="120px">
                    <el-form-item label="弹窗标题：">
                        <el-input
                            v-model="popupConfig.title"
                            placeholder="自定义弹窗标题，默认为'HTML弹窗'"
                            maxlength="20"
                            show-word-limit
                            style="max-width: 400px;"
                        />
                    </el-form-item>

                    <el-form-item label="弹出频率：">
                        <el-radio-group v-model="popupConfig.frequency">
                            <el-radio label="once">仅弹一次</el-radio>
                            <el-radio label="login">每次访问</el-radio>
                            <el-radio label="daily">每天一次</el-radio>
                            <el-radio label="weekly">每周一次</el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <el-form-item label="弹窗滚动条：">
                        <el-switch
                            v-model="popupConfig.enable_scrollbar"
                            active-text="开启滚动条"
                            inactive-text="显示全部内容"
                            style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
                        />
                        <div style="color: #909399; font-size: 12px; margin-top: 4px;">
                            <i class="el-icon-info"></i> 开启后弹窗内容超出高度时显示滚动条，关闭后显示全部内容无需滚动
                        </div>
                    </el-form-item>

                    <el-form-item label="同意按钮延迟：">
                        <div>
                            <!-- 开关控制 -->
                            <div style="margin-bottom: 15px;">
                                <el-switch
                                    v-model="popupConfig.enable_agree_delay"
                                    active-text="启用延迟"
                                    inactive-text="立即可点击"
                                    style="--el-switch-on-color: #13ce66; --el-switch-off-color: #909399"
                                />
                            </div>

                            <!-- 时间设置区域 -->
                            <div v-if="popupConfig.enable_agree_delay" style="padding: 15px; background: #f0f9eb; border: 1px solid #b3d8b3; border-radius: 8px;">
                                <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                                    <span style="color: #67c23a; font-weight: 600; font-size: 14px;">⏰ 延迟时间：</span>
                                    <input
                                        v-model.number="popupConfig.agree_delay_seconds"
                                        type="number"
                                        min="1"
                                        max="60"
                                        style="width: 100px; padding: 10px; border: 2px solid #67c23a; border-radius: 6px; text-align: center; font-size: 16px; font-weight: bold;"
                                        placeholder="5"
                                    />
                                    <span style="color: #67c23a; font-weight: 600; font-size: 14px;">秒</span>
                                </div>
                                <div style="color: #67c23a; font-size: 12px;">
                                    💡 设置范围：1-60秒，同意按钮将显示倒计时后才能点击
                                </div>
                            </div>
                        </div>
                        <div style="color: #909399; font-size: 12px; margin-top: 8px;">
                            <i class="el-icon-info"></i> 启用后同意按钮将在指定秒数后才能点击，防止用户误操作
                        </div>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="savePopupConfig" :loading="configLoading">
                            保存配置
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 模板文本内容管理 -->
            <div style="margin-bottom: 20px; padding: 16px; background: #fff7e6; border-radius: 8px; border-left: 4px solid #fa8c16;">
                <div style="margin-bottom: 16px;">
                    <span style="font-weight: 600; color: #2c3e50; font-size: 16px;">模板文本内容管理</span>
                    <div style="color: #909399; font-size: 12px; margin-top: 4px;">
                        <i class="el-icon-info"></i> 只需修改文本内容，HTML和CSS样式已固定
                    </div>
                </div>

                <el-form :model="templateTexts" label-width="140px">
                    <el-form-item label="Logo图片链接：">
                        <el-input
                            v-model="templateTexts.logoUrl"
                            placeholder="如：https://shop.xhyfaka.com/xhylogo.gif"
                            style="max-width: 500px;"
                        />
                    </el-form-item>

                    <el-form-item label="平台名称：">
                        <el-input
                            v-model="templateTexts.platformName"
                            placeholder="如：小火羊云寄售官方频道"
                            style="max-width: 400px;"
                        />
                    </el-form-item>

                    <el-form-item label="平台频道：">
                        <el-input
                            v-model="templateTexts.platformChannel"
                            placeholder="如：@xhyfkw"
                            style="max-width: 400px;"
                        />
                    </el-form-item>

                    <el-form-item label="温馨提示：">
                        <el-input
                            v-model="templateTexts.warmTip"
                            type="textarea"
                            :rows="2"
                            placeholder="如：本站不提供任何担保，私下交易被骗一律与本站无关"
                            style="max-width: 500px;"
                        />
                    </el-form-item>

                    <el-form-item label="商家注册链接：">
                        <el-input
                            v-model="templateTexts.shopLink"
                            placeholder="如：https://shop.xhyfaka.com/merchant/"
                            style="max-width: 500px;"
                        />
                    </el-form-item>

                    <el-form-item label="商家注册按钮文字：">
                        <el-input
                            v-model="templateTexts.shopButtonText"
                            placeholder="如：注册商家同款店铺"
                            style="max-width: 400px;"
                        />
                    </el-form-item>

                    <el-form-item label="商家群链接：">
                        <el-input
                            v-model="templateTexts.groupLink"
                            placeholder="如：https://shop.xhyfaka.com/35c378d8-590b-45a1-8767-18e34b083da5.png"
                            style="max-width: 500px;"
                        />
                    </el-form-item>

                    <el-form-item label="商家群按钮文字：">
                        <el-input
                            v-model="templateTexts.groupButtonText"
                            placeholder="如：本平台商家通知群"
                            style="max-width: 400px;"
                        />
                    </el-form-item>

                    <el-form-item label="客服链接：">
                        <el-input
                            v-model="templateTexts.serviceLink"
                            placeholder="如：https://shop.xhyfaka.com/kf.html"
                            style="max-width: 500px;"
                        />
                    </el-form-item>

                    <el-form-item label="客服按钮文字：">
                        <el-input
                            v-model="templateTexts.serviceButtonText"
                            placeholder="如：联系平台在线客服"
                            style="max-width: 400px;"
                        />
                    </el-form-item>

                    <el-form-item label="客服提醒：">
                        <el-input
                            v-model="templateTexts.customerServiceNotice"
                            type="textarea"
                            :rows="2"
                            placeholder="如：注意：平台客服不等于商家客服，有任何问题请优先联系商家客服"
                            style="max-width: 500px;"
                        />
                    </el-form-item>

                    <el-form-item label="服务条款1：">
                        <el-input
                            v-model="templateTexts.notice1"
                            type="textarea"
                            :rows="2"
                            placeholder="如：1. 本平台仅提供发卡服务，本平台非销售商，非卡密问题本站不予受理售后争议。"
                            style="max-width: 500px;"
                        />
                    </el-form-item>

                    <el-form-item label="服务条款2：">
                        <el-input
                            v-model="templateTexts.notice2"
                            type="textarea"
                            :rows="3"
                            placeholder="如：2. 平台提供卡密查询时间为10天（购买后请自行保存），如遇违反中华人民共和国相关法律的违规商品，请当天24点前与我们平台客服联系举报。"
                            style="max-width: 500px;"
                        />
                    </el-form-item>

                    <el-form-item label="服务条款3：">
                        <el-input
                            v-model="templateTexts.notice3"
                            type="textarea"
                            :rows="2"
                            placeholder="如：3. 退货退款：虚拟产品具有可复制性，付款后不能正常使用时,不支持退货退款。您可与商家进行咨询"
                            style="max-width: 500px;"
                        />
                    </el-form-item>

                    <el-form-item label="防骗提醒标题：">
                        <el-input
                            v-model="templateTexts.warningTitle"
                            placeholder="如：防骗提醒"
                            style="max-width: 400px;"
                        />
                    </el-form-item>

                    <el-form-item label="防骗提醒1：">
                        <el-input
                            v-model="templateTexts.warning1"
                            type="textarea"
                            :rows="2"
                            placeholder="如：警惕脱离平台交易：遇到商品提示'联系QQ取卡'、'TG群拿货'等脱离正规交易平台的信息，切勿轻信。"
                            style="max-width: 500px;"
                        />
                    </el-form-item>

                    <el-form-item label="防骗提醒2：">
                        <el-input
                            v-model="templateTexts.warning2"
                            type="textarea"
                            :rows="2"
                            placeholder="如：留意发货拖延借口：若卖家以各种理由推脱，声称要到第二天发货，需提高警惕。"
                            style="max-width: 500px;"
                        />
                    </el-form-item>

                    <el-form-item label="防骗提醒3：">
                        <el-input
                            v-model="templateTexts.warning3"
                            type="textarea"
                            :rows="2"
                            placeholder="如：关注售后保障问题：当商品出现问题，卖家却拒绝提供售后服务，不处理退换货等事宜，此情况可疑。"
                            style="max-width: 500px;"
                        />
                    </el-form-item>

                    <el-form-item label="防骗提醒4：">
                        <el-input
                            v-model="templateTexts.warning4"
                            type="textarea"
                            :rows="2"
                            placeholder="如：小心充值返现陷阱：对于承诺充值返现的情况，不要被高额返利诱惑。"
                            style="max-width: 500px;"
                        />
                    </el-form-item>

                    <el-form-item label="防骗提醒5：">
                        <el-input
                            v-model="templateTexts.warning5"
                            type="textarea"
                            :rows="2"
                            placeholder="如：核实实物快递情况：购买实物商品时，若未按正常流程安排快递发货，应及时联系平台客服投诉。"
                            style="max-width: 500px;"
                        />
                    </el-form-item>

                    <el-form-item label="底部提示文字：">
                        <el-input
                            v-model="templateTexts.footerText"
                            placeholder="如：请谨慎交易，保护好自己的财产安全"
                            style="max-width: 400px;"
                        />
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="saveTemplateTexts" :loading="textLoading">
                            保存文本内容
                        </el-button>
                        <el-button type="success" @click="previewTemplate" style="margin-left: 10px;">
                            预览模板效果
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 模板预览 -->
            <div v-if="showPreview" style="margin-bottom: 20px;">
                <h3 style="margin-bottom: 16px; color: #303133;">模板预览</h3>
                <div style="border: 1px solid #dcdfe6; border-radius: 8px; overflow: hidden;">
                    <iframe
                        ref="previewFrame"
                        class="template-iframe"
                        :srcdoc="previewContent"
                        frameborder="0"
                    ></iframe>
                </div>
            </div>
        </el-card>
    </div>
        </el-card>
    </div>

    <!-- 引入依赖库 -->
    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>

    <script>
        const { createApp, ref, reactive, nextTick } = Vue;
        const app = createApp({
            setup() {
                // 状态定义
                const loading = ref(false);
                const showPreview = ref(false);
                const previewContent = ref('');
                const globalLoading = ref(false);
                const configLoading = ref(false);
                const textLoading = ref(false);

                // 全局设置
                const globalSettings = reactive({
                    popupEnabled: true
                });

                // 弹窗配置
                const popupConfig = reactive({
                    frequency: 'once',
                    title: 'HTML弹窗',
                    enable_scrollbar: false,
                    enable_agree_delay: false,
                    agree_delay_seconds: 5
                });

                // 模板文本内容
                const templateTexts = reactive({
                    logoUrl: 'https://shop.xhyfaka.com/xhylogo.gif',
                    platformName: '小火羊云寄售官方频道',
                    platformChannel: '@xhyfkw',
                    warmTip: '本站不提供任何担保，私下交易被骗一律与本站无关',
                    shopLink: 'https://shop.xhyfaka.com/merchant/',
                    shopButtonText: '注册商家同款店铺',
                    groupLink: 'https://shop.xhyfaka.com/35c378d8-590b-45a1-8767-18e34b083da5.png',
                    groupButtonText: '本平台商家通知群',
                    serviceLink: 'https://shop.xhyfaka.com/kf.html',
                    serviceButtonText: '联系平台在线客服',
                    customerServiceNotice: '注意：平台客服不等于商家客服，有任何问题请优先联系商家客服',
                    notice1: '1. 本平台仅提供发卡服务，本平台非销售商，非卡密问题本站不予受理售后争议。',
                    notice2: '2. 平台提供卡密查询时间为10天（购买后请自行保存），如遇违反中华人民共和国相关法律的违规商品，请当天24点前与我们平台客服联系举报。',
                    notice3: '3. 退货退款：虚拟产品具有可复制性，付款后不能正常使用时,不支持退货退款。您可与商家进行咨询',
                    warningTitle: '防骗提醒',
                    warning1: '警惕脱离平台交易：遇到商品提示"联系QQ取卡"、"TG群拿货"等脱离正规交易平台的信息，切勿轻信。',
                    warning2: '留意发货拖延借口：若卖家以各种理由推脱，声称要到第二天发货，需提高警惕。',
                    warning3: '关注售后保障问题：当商品出现问题，卖家却拒绝提供售后服务，不处理退换货等事宜，此情况可疑。',
                    warning4: '小心充值返现陷阱：对于承诺充值返现的情况，不要被高额返利诱惑。',
                    warning5: '核实实物快递情况：购买实物商品时，若未按正常流程安排快递发货，应及时联系平台客服投诉。',
                    footerText: '请谨慎交易，保护好自己的财产安全'
                });

                // 生成模板HTML内容
                // HTML转义函数 - 只转义必要的字符，保留引号
                const escapeHtml = (text) => {
                    if (!text) return '';
                    return text
                        .replace(/&/g, '&amp;')
                        .replace(/</g, '&lt;')
                        .replace(/>/g, '&gt;');
                        // 不转义引号，让它们正常显示
                };

                const generateTemplateHtml = () => {
                    return '<div style="font-family:Microsoft YaHei,sans-serif;max-width:600px;margin:0 auto;background:white;border-radius:12px;overflow:hidden;box-shadow:0 6px 18px rgba(0,0,0,0.08)">' +
                        '<div style="background:linear-gradient(135deg,#6e8efb,#a777e3);color:white;padding:20px;text-align:center">' +
                        '<img src="' + escapeHtml(templateTexts.logoUrl) + '" alt="平台Logo" style="width:180px;height:60px;">' +
                        '</div>' +
                        '<div style="padding:20px">' +
                        '<div style="background-color:#fff8e1;border-left:4px solid #ffc107;padding:12px;margin-bottom:20px;border-radius:0 4px 4px 0;font-weight:500;color:#e65100">' +
                        escapeHtml(templateTexts.warmTip) + ' 频道：' + escapeHtml(templateTexts.platformChannel) +
                        '</div>' +
                        '<a href="' + escapeHtml(templateTexts.shopLink) + '" style="display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,#6e8efb,#a777e3);color:white;border:none;padding:12px;margin:12px 0;border-radius:8px;text-decoration:none;font-weight:500;transition:all 0.3s ease;box-shadow:0 2px 5px rgba(0,0,0,0.1)"><svg t="1744595050613" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1316" width="24" height="24" style="margin-right:8px;"><path d="M670.6176 715.2128m-179.0976 0a179.0976 179.0976 0 1 0 358.1952 0 179.0976 179.0976 0 1 0-358.1952 0Z" fill="#FFBE0A" p-id="1317"></path><path d="M695.1424 311.7056a219.7504 219.7504 0 1 0-356.1472 172.0832 362.8032 362.8032 0 0 0-232.8064 338.2272 75.9808 75.9808 0 0 0 75.9296 75.9296h260.5568a35.84 35.84 0 0 0 0-71.68H182.1184a4.2496 4.2496 0 0 1-4.2496-4.2496 290.9184 290.9184 0 0 1 290.56-290.56h13.9776a29.8496 29.8496 0 0 0 4.3008-0.3072 220.16 220.16 0 0 0 208.4352-219.4432zM475.4432 459.776a148.0704 148.0704 0 1 1 148.0192-148.0704A148.224 148.224 0 0 1 475.4432 459.776zM714.2912 833.2288a35.84 35.84 0 0 1-24.5248-9.728L603.4944 742.4a35.84 35.84 0 1 1 49.1008-52.224l58.1632 54.6304L845.312 578.56a35.84 35.84 0 0 1 55.808 45.312l-158.72 196.096a35.84 35.84 0 0 1-25.6 13.1584 19.712 19.712 0 0 1-2.5088 0.1024z" fill="#34332E" p-id="1318"></path></svg>' + escapeHtml(templateTexts.shopButtonText) + '</a>' +
                        '<a href="' + escapeHtml(templateTexts.groupLink) + '" style="display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,#6e8efb,#a777e3);color:white;border:none;padding:12px;margin:12px 0;border-radius:8px;text-decoration:none;font-weight:500;transition:all 0.3s ease;box-shadow:0 2px 5px rgba(0,0,0,0.1)"><svg t="1744595015666" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1163" width="24" height="24" style="margin-right:8px;"><path d="M608.256 565.3504m-179.0976 0a179.0976 179.0976 0 1 0 358.1952 0 179.0976 179.0976 0 1 0-358.1952 0Z" fill="#FFBE0A" p-id="1164"></path><path d="M635.5968 921.6a132.5568 132.5568 0 0 1-117.4016-69.9392l-116.3264-210.4832-231.1168-103.1168a119.6544 119.6544 0 0 1 7.1168-221.44l552.96-205.3632a135.3216 135.3216 0 0 1 178.176 160.5632l-141.1584 547.84a132.8128 132.8128 0 0 1-113.9712 100.4544 144.6912 144.6912 0 0 1-18.2784 1.4848zM202.8544 384a47.9744 47.9744 0 0 0-2.8672 88.7808l242.0736 108.032a35.84 35.84 0 0 1 16.7424 15.36l122.112 220.8768a63.6928 63.6928 0 0 0 117.3504-14.9504l141.1072-547.84a63.6416 63.6416 0 0 0-83.8144-75.52L202.8032 384z" fill="#34332E" p-id="1165"></path><path d="M532.48 529.6128a35.84 35.84 0 0 1-25.6-60.9792l152.064-154.4704a35.84 35.84 0 1 1 51.2 50.2784L558.08 518.912a35.84 35.84 0 0 1-25.6 10.7008z" fill="#34332E" p-id="1166"></path></svg>' + escapeHtml(templateTexts.groupButtonText) + '</a>' +
                        '<a href="' + escapeHtml(templateTexts.serviceLink) + '" style="display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,#6e8efb,#a777e3);color:white;border:none;padding:12px;margin:12px 0;border-radius:8px;text-decoration:none;font-weight:500;transition:all 0.3s ease;box-shadow:0 2px 5px rgba(0,0,0,0.1)"><svg t="1744594983943" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1010" width="24" height="24" style="margin-right:8px;"><path d="M512 533.1968m-184.2176 0a184.2176 184.2176 0 1 0 368.4352 0 184.2176 184.2176 0 1 0-368.4352 0Z" fill="#FFBE0A" p-id="1011"></path><path d="M914.1248 754.0736A249.2928 249.2928 0 0 0 832.4608 445.44a34.048 34.048 0 0 0-2.8672-1.8432 366.6944 366.6944 0 0 0-377.088-333.8752 366.7456 366.7456 0 0 0-317.44 528.0256c-14.3872 53.4528-30.2592 106.5984-41.4208 142.9504a67.328 67.328 0 0 0 83.3024 84.224l156.5696-46.08a367.0016 367.0016 0 0 0 192.1536 18.8416A249.0368 249.0368 0 0 0 773.12 883.2l79.5648 23.3984a66.56 66.56 0 0 0 18.6368 2.7136 65.9968 65.9968 0 0 0 62.976-84.992c-7.5776-25.1392-14.336-48.7424-20.1728-70.2464z m-443.904 17.1008a290.6624 290.6624 0 0 1-120.832-23.1936 36.1472 36.1472 0 0 0-24.1152-1.3824L164.6592 793.6c12.1344-39.68 28.7232-95.8464 43.264-151.04a35.84 35.84 0 0 0-3.1232-26.2144 295.168 295.168 0 0 1 249.9584-435.2 295.0144 295.0144 0 1 1 15.36 589.824z m374.2208-38.0928a35.84 35.84 0 0 0-3.1232 26.2144c6.0416 22.9376 13.2096 48.2816 21.2992 75.5712l-81.2544-23.8592a35.84 35.84 0 0 0-24.1152 1.3824 176.9984 176.9984 0 0 1-142.4896-1.9456A369.3056 369.3056 0 0 0 721.92 736.8704a365.4144 365.4144 0 0 0 104.3456-199.68 176.9984 176.9984 0 0 1 18.432 196.1472z" fill="#34332E" p-id="1012"></path><path d="M331.1616 512.7168a35.84 35.84 0 0 1-35.84-35.84v-42.1376a35.84 35.84 0 1 1 71.68 0v42.1376a35.84 35.84 0 0 1-35.84 35.84zM569.7536 512.7168a35.84 35.84 0 0 1-35.84-35.84v-42.1376a35.84 35.84 0 0 1 71.68 0v42.1376a35.84 35.84 0 0 1-35.84 35.84z" fill="#34332E" p-id="1013"></path></svg>' + escapeHtml(templateTexts.serviceButtonText) + '</a>' +
                        '<div style="background-color:#ffebee;border-left:4px solid #f44336;padding:12px;margin:15px 0;border-radius:0 4px 4px 0;font-size:15px;line-height:1.5">' + escapeHtml(templateTexts.customerServiceNotice) + '</div>' +
                        '<div style="background-color:#f3e5f5;border-left:4px solid #9c27b0;padding:12px;margin:15px 0;border-radius:0 4px 4px 0;font-size:15px;line-height:1.5">' + escapeHtml(templateTexts.notice1) + '</div>' +
                        '<div style="background-color:#f3e5f5;border-left:4px solid #9c27b0;padding:12px;margin:15px 0;border-radius:0 4px 4px 0;font-size:15px;line-height:1.5">' + escapeHtml(templateTexts.notice2) + '</div>' +
                        '<div style="background-color:#f3e5f5;border-left:4px solid #9c27b0;padding:12px;margin:15px 0;border-radius:0 4px 4px 0;font-size:15px;line-height:1.5">' + escapeHtml(templateTexts.notice3) + '</div>' +
                        '<div style="background-color:#ffebee;border-radius:8px;padding:15px;margin-top:20px">' +
                        '<div style="color:#d32f2f;font-weight:600;margin-bottom:10px;display:flex;align-items:center">⚠️ ' + escapeHtml(templateTexts.warningTitle) + '</div>' +
                        '<div style="margin-bottom:10px;font-size:14px;line-height:1.5;position:relative;padding-left:20px"><span style="color:#d32f2f;font-weight:bold;position:absolute;left:0">•</span>' + escapeHtml(templateTexts.warning1) + '</div>' +
                        '<div style="margin-bottom:10px;font-size:14px;line-height:1.5;position:relative;padding-left:20px"><span style="color:#d32f2f;font-weight:bold;position:absolute;left:0">•</span>' + escapeHtml(templateTexts.warning2) + '</div>' +
                        '<div style="margin-bottom:10px;font-size:14px;line-height:1.5;position:relative;padding-left:20px"><span style="color:#d32f2f;font-weight:bold;position:absolute;left:0">•</span>' + escapeHtml(templateTexts.warning3) + '</div>' +
                        '<div style="margin-bottom:10px;font-size:14px;line-height:1.5;position:relative;padding-left:20px"><span style="color:#d32f2f;font-weight:bold;position:absolute;left:0">•</span>' + escapeHtml(templateTexts.warning4) + '</div>' +
                        '<div style="margin-bottom:10px;font-size:14px;line-height:1.5;position:relative;padding-left:20px"><span style="color:#d32f2f;font-weight:bold;position:absolute;left:0">•</span>' + escapeHtml(templateTexts.warning5) + '</div>' +
                        '</div>' +
                        '</div>' +
                        '<div style="background:#f5f5f5;padding:15px;text-align:center;font-size:12px;color:#666">' + escapeHtml(templateTexts.footerText) + '</div>' +
                        '</div>';
                };



                // 预览模板
                const previewTemplate = () => {
                    previewContent.value = generateTemplateHtml();
                    showPreview.value = true;
                };

                // 验证延迟秒数
                const validateDelaySeconds = () => {
                    let seconds = parseInt(popupConfig.agree_delay_seconds) || 5;
                    if (seconds < 1) seconds = 1;
                    if (seconds > 60) seconds = 60;
                    popupConfig.agree_delay_seconds = seconds;
                };

                // 延迟开关切换事件
                const onDelayToggle = (value) => {
                    console.log('延迟开关状态:', value);
                    console.log('当前配置:', popupConfig);
                };

                // 保存全局设置
                const saveGlobalSettings = async () => {
                    try {
                        globalLoading.value = true;
                        const res = await axios.post('saveGlobalSettings', {
                            popupEnabled: globalSettings.popupEnabled
                        });
                        if (res.data?.code === 200) {
                            ElementPlus.ElMessage.success('全局设置保存成功');
                        } else {
                            ElementPlus.ElMessage.error(res.data?.msg || '保存失败');
                        }
                    } catch (error) {
                        ElementPlus.ElMessage.error('保存失败，请重试');
                    } finally {
                        globalLoading.value = false;
                    }
                };

                // 保存弹窗配置
                const savePopupConfig = async () => {
                    try {
                        configLoading.value = true;

                        // 验证延迟秒数
                        let delaySeconds = parseInt(popupConfig.agree_delay_seconds) || 5;
                        if (delaySeconds < 1) delaySeconds = 1;
                        if (delaySeconds > 60) delaySeconds = 60;
                        popupConfig.agree_delay_seconds = delaySeconds;

                        const res = await axios.post('savePopupConfig', {
                            frequency: popupConfig.frequency,
                            title: popupConfig.title,
                            enable_scrollbar: popupConfig.enable_scrollbar,
                            enable_agree_delay: popupConfig.enable_agree_delay,
                            agree_delay_seconds: popupConfig.agree_delay_seconds
                        });
                        if (res.data?.code === 200) {
                            ElementPlus.ElMessage.success('弹窗配置保存成功');
                        } else {
                            ElementPlus.ElMessage.error(res.data?.msg || '保存失败');
                        }
                    } catch (error) {
                        ElementPlus.ElMessage.error('保存失败，请重试');
                    } finally {
                        configLoading.value = false;
                    }
                };

                // 处理文本内容，确保特殊字符正确处理
                const processTextContent = (text) => {
                    if (!text || typeof text !== 'string') return text;

                    // 确保文本内容正确处理，不进行额外的编码
                    return text.trim();
                };

                // 保存模板文本内容
                const saveTemplateTexts = async () => {
                    try {
                        textLoading.value = true;

                        // 处理所有文本内容，确保特殊字符正确
                        const processedTexts = {
                            logoUrl: processTextContent(templateTexts.logoUrl),
                            platformName: processTextContent(templateTexts.platformName),
                            platformChannel: processTextContent(templateTexts.platformChannel),
                            warmTip: processTextContent(templateTexts.warmTip),
                            shopLink: processTextContent(templateTexts.shopLink),
                            shopButtonText: processTextContent(templateTexts.shopButtonText),
                            groupLink: processTextContent(templateTexts.groupLink),
                            groupButtonText: processTextContent(templateTexts.groupButtonText),
                            serviceLink: processTextContent(templateTexts.serviceLink),
                            serviceButtonText: processTextContent(templateTexts.serviceButtonText),
                            customerServiceNotice: processTextContent(templateTexts.customerServiceNotice),
                            notice1: processTextContent(templateTexts.notice1),
                            notice2: processTextContent(templateTexts.notice2),
                            notice3: processTextContent(templateTexts.notice3),
                            warningTitle: processTextContent(templateTexts.warningTitle),
                            warning1: processTextContent(templateTexts.warning1),
                            warning2: processTextContent(templateTexts.warning2),
                            warning3: processTextContent(templateTexts.warning3),
                            warning4: processTextContent(templateTexts.warning4),
                            warning5: processTextContent(templateTexts.warning5),
                            footerText: processTextContent(templateTexts.footerText)
                        };

                        // 发送处理后的数据
                        const res = await axios.post('saveTemplateTexts', processedTexts);
                        if (res.data?.code === 200) {
                            ElementPlus.ElMessage.success('模板文本内容保存成功');
                        } else {
                            ElementPlus.ElMessage.error(res.data?.msg || '保存失败');
                        }
                    } catch (error) {
                        ElementPlus.ElMessage.error('保存失败，请重试');
                    } finally {
                        textLoading.value = false;
                    }
                };

                // 加载配置数据
                const loadConfig = async () => {
                    try {
                        const res = await axios.post('getConfig');

                        if (res.data?.code === 200) {
                            const config = res.data.data || {};

                            // 加载全局设置
                            if (config.globalSettings) {
                                Object.assign(globalSettings, config.globalSettings);
                            }

                            // 加载弹窗配置
                            if (config.popupConfig) {
                                Object.assign(popupConfig, config.popupConfig);
                            }

                            // 加载模板文本
                            if (config.templateTexts) {
                                Object.assign(templateTexts, config.templateTexts);
                            }
                        } else {
                            ElementPlus.ElMessage.error(res.data?.msg || '配置加载失败');
                        }
                    } catch (error) {
                        ElementPlus.ElMessage.error('配置加载失败，请刷新页面重试');
                    }
                };

                // 初始化
                const init = async () => {
                    await loadConfig();
                };

                // 页面加载时初始化
                init();

                return {
                    loading,
                    showPreview,
                    previewContent,
                    globalLoading,
                    configLoading,
                    textLoading,
                    globalSettings,
                    popupConfig,
                    templateTexts,
                    saveGlobalSettings,
                    savePopupConfig,
                    saveTemplateTexts,
                    previewTemplate,
                    validateDelaySeconds,
                    onDelayToggle
                };
            }
        });

        app.use(ElementPlus);
        app.mount('#app');
    </script>
</body>
</html>