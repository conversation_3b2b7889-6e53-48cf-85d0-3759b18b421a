<?php

namespace plugin\N5cduanlian;
 
use app\common\library\Plugin;
use app\common\model\DwzApi as DwzApiModel;
use app\common\service\HttpService;

class N5cduanlian extends Plugin {

    /**
     * 插件安装方法
     * @return bool
     */
    public function install() {
        $model = DwzApiModel::where(['code' => 'N5cduanlian'])->find();
        if (!$model) {
            $model = new DwzApiModel();
        }
        $model->code = 'N5cduanlian';
        $model->name = 'N5c短链接';
        $model->tips = '';
        $model->website = 'https://www.n5c.cn/';
        $model->save();

        return true;
    }

    /**
     * 插件卸载方法
     * @return bool
     */
    public function uninstall() {
        $model = DwzApiModel::where(['code' => 'N5cduanlian'])->find();
        if ($model) {
            $model->delete();
        }
        return true;
    }

    /**
     * 生成短链接
     * @param string $url 需要缩短的长网址
     * @return string|false
     */
    public function create($url) {
        $token = plugconf('N5cduanlian.token');
        if (empty($token)) {
            return false;
        }

        $params = [
            'type' => plugconf('N5cduanlian.type'),
            'pattern' => plugconf('N5cduanlian.pattern'),
            'token' => $token,
            'url' => $url
        ];

        $request_url = 'https://www.n5c.cn/api/url.php?type=' . $params['type'] 
                    . '&pattern=' . $params['pattern']
                    . '&token=' . $params['token']
                    . '&url=' . urlencode($params['url']);
        
        $res = HttpService::get($request_url);
        
        if ($res === false) {
            return false;
        }

        $json = json_decode($res, true);
        
        if (!$json) {
            return false;
        }

        if (isset($json['code']) && $json['code'] != 200) {
            return false;
        }

        if (!isset($json['dwz'])) {
            return false;
        }

        return $json['dwz'];
    }
} 