<?php

namespace plugin\Storeannouncements\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;

class Api extends BasePlugin {

    protected $noNeedLogin = ['fetchData'];
    
    // 添加场景配置
    protected $scene = [
        'user'
    ];

    public function index() {
        // 删除所有编辑器配置，直接返回视图
        return View::fetch();
    }

    public function fetchData() {
        try {
            // 添加防止缓存的响应头
            header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
            header('Cache-Control: post-check=0, pre-check=0', false);
            header('Pragma: no-cache');

            // 获取请求参数中的商家信息
            $shop_name = $this->request->param('shop_name', '', 'trim');
            $request_merchant_id = $this->request->param('merchant_id', 0, 'intval');
            $merchant_id = 0;
            
            // 优先使用URL请求参数中的商家ID
            if ($request_merchant_id > 0) {
                $merchant_id = $request_merchant_id;
            }
            // 其次通过店铺名称查找商家ID
            else if (!empty($shop_name)) {
                $merchant_id = $this->getMerchantIdByShopName($shop_name);
            }
            // 最后才使用当前登录用户的ID
            else if ($this->user && $this->user->id) {
                $merchant_id = $this->user->id;
            }
            
            if (!$merchant_id) {
                return json(['code' => 0, 'msg' => '未找到商家信息']);
            }

            // 确保获取的是指定商家的配置
            $button_color = merchant_plugconf($merchant_id, "Storeannouncements.button_color") ?? 'blue';
            
            // 记录获取到的颜色值
            \think\facade\Log::info('获取按钮颜色: '.$button_color.' 商家ID: '.$merchant_id);
            
            $params = [
                'status' => intval(merchant_plugconf($merchant_id, "Storeannouncements.status") ?? 1),
                'announcement' => merchant_plugconf($merchant_id, "Storeannouncements.announcement") ?? '',
                'frequency' => merchant_plugconf($merchant_id, "Storeannouncements.frequency") ?? 'once',
                'read_enabled' => intval(merchant_plugconf($merchant_id, "Storeannouncements.read_enabled") ?? 1),
                'close_delay' => intval(merchant_plugconf($merchant_id, "Storeannouncements.close_delay") ?? 0),
                'title' => merchant_plugconf($merchant_id, "Storeannouncements.title") ?? '店铺公告',
                'button_color' => $button_color
            ];

            return json(['code' => 200, 'msg' => 'success', 'data' => $params]);
        } catch (\Exception $e) {
            \think\facade\Log::error('获取公告失败：' . $e->getMessage());
            return json(['code' => 0, 'msg' => '获取数据失败']);
        }
    }

    // 添加通过店铺名称查找商家ID的方法
    protected function getMerchantIdByShopName($shop_name) {
        if (empty($shop_name)) {
            return 0;
        }
        
        try {
            // 使用与 Imagesxuanfu 相同的查询逻辑
            $merchant = \think\facade\Db::name('user')
                ->where('nickname', $shop_name)
                ->value('id');
            
            return intval($merchant);
        } catch (\Exception $e) {
            \think\facade\Log::error('通过店铺名称查找商家失败：' . $e->getMessage());
            return 0;
        }
    }

    public function save() {
        try {
            if (!$this->user || !$this->user->id) {
                return json(['code' => 403, 'msg' => '请先登录']);
            }
            
            // 获取要保存配置的商家ID
            $merchant_id = $this->request->post('merchant_id', 0, 'intval');
            
            // 如果没有指定商家ID，使用当前登录用户的ID
            if (!$merchant_id) {
                $merchant_id = $this->user->id;
            }
            
            // 权限检查：只允许用户修改自己的设置，管理员除外
            if ($merchant_id != $this->user->id && !$this->user->isAdmin) {
                return json(['code' => 403, 'msg' => '您无权修改其他商家的设置']);
            }

            $status = $this->request->post('status/d', 1);
            $announcement = $this->request->post('announcement', '', 'trim');
            $frequency = $this->request->post('frequency', 'once', 'trim');
            $read_enabled = $this->request->post('read_enabled/d', 1);
            $close_delay = $this->request->post('close_delay/d', 0);
            $title = $this->request->post('title', '店铺公告', 'trim');
            $button_color = $this->request->post('button_color', 'blue', 'trim');
            
            // 验证 read_enabled 值
            if (!in_array($read_enabled, [0, 1])) {
                $read_enabled = 1; // 默认开启
            }
            
            // 验证frequency值
            if (!in_array($frequency, ['once', 'login', 'daily', 'weekly'])) {
                return json(['code' => 0, 'msg' => '弹出频率设置不正确']);
            }
            
            // 验证倒计时值
            if ($close_delay < 0 || $close_delay > 60) {
                return json(['code' => 0, 'msg' => '倒计时设置不正确']);
            }
            
            // 验证标题
            if (empty($title)) {
                $title = '店铺公告'; // 默认标题
            } else if (mb_strlen($title) > 20) {
                $title = mb_substr($title, 0, 20); // 限制长度
            }
            
            // 验证按钮颜色格式
            if (empty($button_color)) {
                $button_color = '#409eff'; // 默认蓝色
            } else if (!preg_match('/^(#[0-9a-f]{3,8}|rgba?\(.*\))$/i', $button_color)) {
                // 如果不是有效的颜色格式，使用默认值
                $button_color = '#409eff';
            }
            
            // 内容安全检查
            if ($announcement !== '' && !is_string($announcement)) {
                return json(['code' => 0, 'msg' => '公告内容格式不正确']);
            }

            // 对公告内容进行XSS过滤
            if ($announcement) {
                $announcement = xss_safe($announcement);
            }

            // 保存配置到指定商家ID
            merchant_plugconf($merchant_id, "Storeannouncements.status", $status);
            merchant_plugconf($merchant_id, "Storeannouncements.announcement", $announcement);
            merchant_plugconf($merchant_id, "Storeannouncements.frequency", $frequency);
            merchant_plugconf($merchant_id, "Storeannouncements.read_enabled", intval($read_enabled));
            merchant_plugconf($merchant_id, "Storeannouncements.close_delay", $close_delay);
            merchant_plugconf($merchant_id, "Storeannouncements.title", $title);
            // 记录保存的颜色值
            \think\facade\Log::info('保存按钮颜色: '.$button_color.' 商家ID: '.$merchant_id);
            merchant_plugconf($merchant_id, "Storeannouncements.button_color", $button_color);

            \think\facade\Log::info('商家'.$merchant_id.'保存公告成功');
            return json(['code' => 200, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            \think\facade\Log::error('保存公告失败：' . $e->getMessage());
            return json(['code' => 0, 'msg' => '保存失败']);
        }
    }
} 