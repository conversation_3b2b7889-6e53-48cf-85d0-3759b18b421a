#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
椰子云短信验证码API接口
"""

import requests
import json
import time
from typing import Dict, Optional

class YeziCloudAPI:
    """椰子云API接口类"""
    
    def __init__(self, username: str = "", password: str = ""):
        """
        初始化椰子云API
        
        Args:
            username: 用户名
            password: 密码
        """
        self.username = username
        self.password = password
        self.token = ""
        self.base_url = "http://api.sqhyw.net:90"
        self.backup_url = "http://api.nnanx.com:90"
        self.current_url = self.base_url
        
        # 请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate',
        }
    
    def switch_to_backup_url(self):
        """切换到备用域名"""
        self.current_url = self.backup_url
    
    def login(self) -> Dict:
        """
        用户登录
        
        Returns:
            dict: 登录结果
        """
        if not self.username or not self.password:
            return {
                'success': False,
                'error': '用户名或密码不能为空'
            }
        
        # 根据椰子云文档，使用正确的API端点
        url = f"{self.current_url}/api/logins"
        params = {
            'username': self.username,
            'password': self.password
        }

        # 添加必要的请求头
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
        }
        
        try:
            response = requests.get(url, params=params, headers=headers, timeout=15)
            response.raise_for_status()

            data = response.json()

            if 'token' in data:
                self.token = data['token']
                user_data = data.get('data', [{}])[0] if data.get('data') else {}

                return {
                    'success': True,
                    'token': self.token,
                    'balance': user_data.get('money', '0'),
                    'balance_1': user_data.get('money_1', '0.0000'),
                    'user_id': user_data.get('id', ''),
                    'user_type': user_data.get('leixing', ''),
                    'message': '登录成功',
                    'data': data.get('data', [])
                }
            else:
                return {
                    'success': False,
                    'error': f'登录失败，未返回token。响应数据: {data}'
                }
                
        except requests.exceptions.RequestException as e:
            # 尝试备用域名
            if self.current_url == self.base_url:
                self.switch_to_backup_url()
                return self.login()
            else:
                return {
                    'success': False,
                    'error': f'登录请求失败: {str(e)}'
                }
        except json.JSONDecodeError as e:
            return {
                'success': False,
                'error': f'响应解析失败，可能不是JSON格式: {str(e)}'
            }
        except Exception as e:
            return {
                'success': False,
                'error': f'登录异常: {str(e)}'
            }
    
    def get_balance(self) -> Dict:
        """
        获取账户余额
        
        Returns:
            dict: 余额信息
        """
        if not self.token:
            return {
                'success': False,
                'error': '请先登录获取token'
            }
        
        url = f"{self.current_url}/api/get_myinfo"
        params = {
            'token': self.token
        }
        
        try:
            response = requests.get(url, params=params, headers=self.headers, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('message') == 'ok':
                return {
                    'success': True,
                    'data': data.get('data', [])
                }
            else:
                return {
                    'success': False,
                    'error': data.get('message', '获取余额失败')
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f'获取余额失败: {str(e)}'
            }
    
    def get_mobile(self, project_id: str, special: str = "", loop: str = "",
                   operator: str = "", phone_num: str = "", scope: str = "",
                   address: str = "", api_id: str = "", scope_black: str = "",
                   creat_time: str = "", designatedID: str = "") -> Dict:
        """
        获取手机号

        Args:
            project_id: 项目ID (必选) - 用于接收短信的项目ID
            special: 从专属取卡 special=1，不加这个参数取普通项目的卡 (可选)
            loop: 是否过滤项目 1过滤 2不过滤 默认不过滤 (可选)
            operator: 运营商 (0=默认 1=移动 2=联通 3=电信 4=实卡 5=虚卡) (可选)
            phone_num: 指定取号的手机号 (可选)
            scope: 指定号段 最多支持号码前5位 例如165或16511 (可选)
            address: 归属地选择 例如 湖北 甘肃 不需要带省字 (可选)
            api_id: 开发者用户ID (可选)
            scope_black: 排除号段 最长支持7位 多个用逗号分隔 (可选)
            creat_time: 过滤上线时间 单位/天 范围1-60 (可选)
            designatedID: 指定卡商id (可选)

        Returns:
            dict: 取卡结果
        """
        if not self.token:
            return {
                'success': False,
                'error': '请先登录获取token'
            }

        print(f"[手机] 椰子云获取手机号 - 项目ID: {project_id}, Special: {special}")

        url = f"{self.current_url}/api/get_mobile"
        params = {
            'token': self.token,
            'project_id': project_id
        }

        # 添加可选参数
        optional_params = {
            'special': special,
            'loop': loop,
            'operator': operator,
            'phone_num': phone_num,
            'scope': scope,
            'address': address,
            'api_id': api_id,
            'scope_black': scope_black,
            'creat_time': creat_time,
            'designatedID': designatedID
        }

        for key, value in optional_params.items():
            if value:  # 只添加非空参数
                params[key] = value

        try:
            print(f"📡 请求椰子云取卡API: {url}")
            print(f"[信息] 请求参数: {params}")

            response = requests.get(url, params=params, headers=self.headers, timeout=15)
            response.raise_for_status()

            data = response.json()
            print(f"[手机] 椰子云取卡API响应: {data}")

            if data.get('message') == 'ok' and 'mobile' in data:
                mobile = data['mobile']
                remaining = data.get('1分钟内剩余取卡数', '0')
                print(f"[成功] 椰子云获取手机号成功: {mobile} (剩余: {remaining})")
                return {
                    'success': True,
                    'mobile': mobile,
                    'remaining': remaining,
                    'project_id': project_id,  # 记录项目ID，确保一致性
                    'special': special  # 记录special参数，确保一致性
                }
            else:
                error_msg = data.get('message', '获取手机号失败')
                print(f"[失败] 椰子云获取手机号失败: {error_msg}")
                return {
                    'success': False,
                    'error': error_msg
                }

        except requests.exceptions.SSLError as e:
            print(f"[关闭] SSL握手失败，尝试备用域名: {str(e)}")
            # SSL错误时尝试备用域名
            if self.current_url == self.base_url:
                self.switch_to_backup_url()
                print(f"[重试] 切换到备用域名: {self.current_url}")
                return self.get_mobile(project_id, special, loop, operator, phone_num,
                                     scope, address, api_id, scope_black, creat_time, designatedID)
            else:
                return {
                    'success': False,
                    'error': f'SSL握手失败，请检查网络连接: {str(e)}'
                }
        except requests.exceptions.RequestException as e:
            print(f"[网络] 网络请求失败: {str(e)}")
            # 尝试备用域名
            if self.current_url == self.base_url:
                self.switch_to_backup_url()
                print(f"[重试] 切换到备用域名: {self.current_url}")
                return self.get_mobile(project_id, special, loop, operator, phone_num,
                                     scope, address, api_id, scope_black, creat_time, designatedID)
            else:
                return {
                    'success': False,
                    'error': f'获取手机号失败: {str(e)}'
                }
        except json.JSONDecodeError as e:
            print(f"[页面] JSON解析失败: {str(e)}")
            return {
                'success': False,
                'error': f'获取手机号响应解析失败: {str(e)}'
            }
    
    def get_sms(self, project_id: str, phone_num: str, special: str = "") -> Dict:
        """
        获取短信验证码

        Args:
            project_id: 项目ID (必选) - 必须与get_mobile使用的项目ID相同
            phone_num: 手机号 - get_mobile取卡接口返回的手机号 (必选)
            special: 如果取卡时调用了此参数，这里必须要填 special=1，否则获取不到短信 (可选)

        Returns:
            dict: 短信结果
            - 短信未到达: {'success': True, 'received': False, 'message': '短信尚未到达'}
            - 短信已到达: {'success': True, 'received': True, 'code': '807272', 'content': '【酷狗音乐】您的登录验证码807272...', 'data': [...]}
        """
        if not self.token:
            return {
                'success': False,
                'error': '请先登录获取token'
            }

        print(f"[检查] 椰子云获取短信 - 项目ID: {project_id}, 手机号: {phone_num}, Special: {special}")

        # 构建请求URL和参数
        url = f"{self.current_url}/api/get_message"
        params = {
            'token': self.token,
            'project_id': project_id,
            'phone_num': phone_num
        }

        # 如果取卡时使用了special参数，这里也必须填写
        if special:
            params['special'] = special

        try:
            print(f"📡 请求椰子云短信API: {url}")
            print(f"[信息] 请求参数: {params}")

            response = requests.get(url, params=params, headers=self.headers, timeout=15)
            response.raise_for_status()

            data = response.json()
            print(f"[短信] 椰子云短信API响应: {data}")

            # 检查响应状态
            message = data.get('message', '')

            if message == 'ok':
                # 检查是否收到短信
                if 'code' in data and data['code']:
                    # 短信已到达
                    sms_data = data.get('data', [])
                    sms_content = ""

                    if sms_data and len(sms_data) > 0:
                        sms_content = sms_data[0].get('modle', '')

                    print(f"[成功] 椰子云短信已到达 - 验证码: {data['code']}")
                    return {
                        'success': True,
                        'received': True,
                        'code': data['code'],
                        'content': sms_content,
                        'data': sms_data,
                        'phone': phone_num,
                        'project_id': project_id
                    }
                else:
                    # 短信尚未到达
                    print(f"⏳ 椰子云短信尚未到达，继续等待...")
                    return {
                        'success': True,
                        'received': False,
                        'message': '短信尚未到达，请继续等待',
                        'data': data.get('data', [])
                    }
            elif message in ['短信还未到达,请继续获取', '短信还未到达，请继续获取', '短信尚未到达']:
                # 椰子云特殊的"短信未到达"消息，这不是错误
                print(f"⏳ 椰子云短信尚未到达，继续等待...")
                return {
                    'success': True,
                    'received': False,
                    'message': '短信尚未到达，请继续等待',
                    'data': data.get('data', [])
                }
            else:
                error_msg = data.get('message', '未知错误')
                print(f"[失败] 椰子云短信API错误: {error_msg}")
                return {
                    'success': False,
                    'error': f"获取短信失败: {error_msg}"
                }

        except requests.exceptions.SSLError as e:
            print(f"[关闭] SSL握手失败，尝试备用域名: {str(e)}")
            # SSL错误时尝试备用域名
            if self.current_url == self.base_url:
                self.switch_to_backup_url()
                print(f"[重试] 切换到备用域名: {self.current_url}")
                return self.get_sms(project_id, phone_num, special)
            else:
                return {
                    'success': False,
                    'error': f'SSL握手失败，请检查网络连接: {str(e)}'
                }
        except requests.exceptions.RequestException as e:
            print(f"[网络] 网络请求失败: {str(e)}")
            # 尝试备用域名
            if self.current_url == self.base_url:
                self.switch_to_backup_url()
                print(f"[重试] 切换到备用域名: {self.current_url}")
                return self.get_sms(project_id, phone_num, special)
            else:
                return {
                    'success': False,
                    'error': f'获取短信请求失败: {str(e)}'
                }
        except json.JSONDecodeError as e:
            print(f"[页面] JSON解析失败: {str(e)}")
            return {
                'success': False,
                'error': f'获取短信响应解析失败: {str(e)}'
            }

    def get_message(self, project_id: str, phone_num: str, special: str = "") -> Dict:
        """
        获取短信验证码（get_sms的别名，保持兼容性）

        Args:
            project_id: 项目ID
            phone_num: 手机号
            special: 特殊参数

        Returns:
            dict: 短信结果，格式兼容旧版本
        """
        try:
            # 调用get_sms方法
            result = self.get_sms(project_id, phone_num, special)

            if result['success'] and result.get('received', False):
                # 转换为兼容格式
                return {
                    'success': True,
                    'messages': [{
                        'content': result.get('content', ''),
                        'code': result.get('code', '')
                    }],
                    'data': result.get('data', [])
                }
            elif result['success'] and not result.get('received', False):
                # 短信未到达
                return {
                    'success': True,
                    'messages': [],
                    'data': result.get('data', [])
                }
            else:
                # 错误情况
                return result

        except Exception as e:
            return {
                'success': False,
                'error': f'获取短信异常: {str(e)}'
            }
    
    def release_mobile(self, phone_num: str = "", project_id: str = "", special: str = "") -> Dict:
        """
        释放手机号
        
        Args:
            phone_num: 手机号 (空则释放所有)
            project_id: 项目ID
            special: 是否专属 (1)
            
        Returns:
            dict: 释放结果
        """
        if not self.token:
            return {
                'success': False,
                'error': '请先登录获取token'
            }
        
        url = f"{self.current_url}/api/free_mobile"
        params = {
            'token': self.token
        }
        
        if phone_num:
            params['phone_num'] = phone_num
        if project_id:
            params['project_id'] = project_id
        if special:
            params['special'] = special
        
        try:
            response = requests.get(url, params=params, headers=self.headers, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('message') == 'ok':
                return {
                    'success': True,
                    'message': '释放成功'
                }
            else:
                return {
                    'success': False,
                    'error': data.get('message', '释放失败')
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f'释放手机号失败: {str(e)}'
            }
    
    def blacklist_mobile(self, project_id: str, phone_num: str, special: str = "") -> Dict:
        """
        拉黑手机号
        
        Args:
            project_id: 项目ID
            phone_num: 手机号
            special: 是否专属 (1)
            
        Returns:
            dict: 拉黑结果
        """
        if not self.token:
            return {
                'success': False,
                'error': '请先登录获取token'
            }
        
        url = f"{self.current_url}/api/add_blacklist"
        params = {
            'token': self.token,
            'project_id': project_id,
            'phone_num': phone_num
        }
        
        if special:
            params['special'] = special
        
        try:
            response = requests.get(url, params=params, headers=self.headers, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('message') == 'ok':
                return {
                    'success': True,
                    'message': '拉黑成功'
                }
            else:
                return {
                    'success': False,
                    'error': data.get('message', '拉黑失败')
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f'拉黑手机号失败: {str(e)}'
            }

    def get_mobile_and_wait_sms(self, project_id: str, max_wait_time: int = 120) -> Dict:
        """
        获取手机号并等待短信验证码（专用于注册流程）

        Args:
            project_id: 项目ID
            max_wait_time: 最大等待时间（秒）

        Returns:
            dict: 包含手机号和验证码的结果
        """
        mobile = None
        try:
            # 1. 获取手机号
            mobile_result = self.get_mobile(project_id)
            if not mobile_result['success']:
                return {
                    'success': False,
                    'error': f"获取手机号失败: {mobile_result['error']}"
                }

            mobile = mobile_result['mobile']

            # 2. 等待短信验证码
            start_time = time.time()
            check_interval = 3  # 每3秒检查一次

            while time.time() - start_time < max_wait_time:
                # 检查是否收到短信
                sms_result = self.get_sms(project_id, mobile)

                if sms_result['success'] and sms_result.get('received'):
                    # 提取验证码
                    verification_code = sms_result.get('code', '')

                    if verification_code:
                        return {
                            'success': True,
                            'mobile': mobile,
                            'mobile_code': verification_code,  # 用于注册接口
                            'verification_code': verification_code,  # 保持兼容性
                            'sms_content': sms_result.get('content', ''),
                            'wait_time': int(time.time() - start_time)
                        }

                # 等待一段时间后重试
                time.sleep(check_interval)

            # 超时处理
            return {
                'success': False,
                'error': f'等待短信验证码超时（{max_wait_time}秒）',
                'mobile': mobile,
                'timeout': True
            }

        except Exception as e:
            error_msg = f'获取手机号和验证码异常: {str(e)}'

            # 如果有手机号，尝试释放
            if mobile:
                try:
                    self.release_mobile(mobile)
                except:
                    pass

            return {
                'success': False,
                'error': error_msg
            }
