<?php

return [
    "version" => "1.0.0",
    "min_system_version" => "2.12.0",
    "category_name" => "功能插件",
    "logo" => "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMgAAADICAYAAACtWK6eAAAVL0lEQVR4Xu1dTZYUxxHOBj0LeYVPwHACYCWBFsycQHACZk4gzQlsTuDhBB5O4OYEahYWeOXmBG6dwM3GkvwE7YjKrqn/qvyJzIrMinqvX8N0Vf5Exlfxm5ErJVdYCrxTJ9DBiTqou2oFH/zG65a6d+wY/1//7OH/+MFLf6/Urvj+pD7Cv/fQxr74VvD3x8ffws5isa2vFjtzyon/CAz+BwDBCj631QNg4IcFKPRHAyLstS7AhED6pD5A/zv1rdqG7XIZrQtAbNe5AsMpSIHvakCwbSnG/Sh5tuqzegug2Qho7EkuADGh2T9AIqxUCQiUDjGkgsnIbO9BtWwjgDEnmwCkj1YoJb5U5yAhHsDPzxIGxBQnIGDWIF3egi1zPXXzEn8XgJSrjqC4U0iK7+FPpxmDYozP1yBdXqsnABq5CgoIQN4BGFaFLXG+UFAMQeEKDP7XS7dblgkQdL3eUi9AtUD1CW0KuYYpsIEXyEb9V71SZzfu58XQa1kA0dJiySqUL2NfwUvl1ZJiL8sAiAbGn4+2hS+TyPPasL9cAlDyBogGxl9FjQqG6TXYKS9ztlPyBIgAIxgiBhregER5naOrOC+ACDBiA6Pd3xYkykVOEiUPgGAM46vCxvhhbg6R/gsKXKlvwEbJ4EobIDq49+xoZ6Sa/pEBG/VOAbOO0ZC/TnmC6QJE1KlU+G4LQHmeqscrPYBU6tQ5cIhIjVRgkqjalRZAtNT4uwAjHVS0RopqF0qTTSozSAMgYoSnwk9m41ypv6ivIX6SwMUfILgX43YR7DtNgJ4yRHMKbNUvkN3FPL+LN0BEpTJntzTvZK9y8QSIqFRpsrvrqBmrXPwAIiqVK5ul/hxLlYsXQDQ4fhQvVeq87jz+PaSqnHFKVeEDELE3nLkqswdZgYQHQH6CHKpbRS6VBP4y43bn6RxAkjCIl8wPEA0OdOPKJRRoUuAAmcEz53LNC5D3BTAkA1eAMUyBz5Dw+ASyg2e65gGIduMiOM5nmrd0mxIFZnQDxweIBsffYH2woohcQgFTCmxgj8mZ6c1U98UHyPsCHCI5qFZwWe1E34gVFyBicyyLnUPMNrLhHg8g4q0KwS7LbDMiSOIARMCxTEYOOetIcZLwAHkH9oauTSVBwJAMs8S2P6lHodNSwgJEcquWyLYx5xw8LSUcQAQcMRllyX3hnpJHoYpChAGIjnVgVq5UTl8y68abe7AYSRiAiDs3HmtIT5oCgVJS6AEiHith2bkoEMCzRQsQsTvmYg3pV1OA3B6hA4jYHcKkPChAao/QAUTsDh7sIaMgtUdoACJ2h7AlNwoQ2SP+AMEDMVfqXxIp58Yhix/PHtLj/+RLBX+AvC9q5creDt+VkOdDUOAaQHLh07AfQH4CYNwqACKXUIAnBTzztdwBIl4rngwho2pTYAtS5JErWdwB8k8o03OAKt1yCQW4U8Bj/4gbQMQw584SMr4mBfZQSf6+SyV5N4CIYS4MmB4FnAx2e4As0zDfAz/gZ1d8VvD5pD7CN6Y27I/fekPYCjKZb6l7xb8P4AJXxQcvzGyWTWNzAsvBYLcDyHIM8xIMG2DyN+pXtXURzx1eQNUUDwJaqQfFt2wHiA2XHRjs9206tQOI3j6LZXtyvLYwqQ2kTb9Vv8F3rJOPUCLfBsAcigqTImFCc9ZnOCPxiVqbdmMOkDylB0qKNahLr0LvbTZaEK2+fgf3nhvdLze5UMDK7WsOkLxsD7QbrkBavA61VdNl5W6eQVXsUIDle/gbqmVyUVLAwhYxB8j7It8q9S20OwDFK1ChrqOpUL4Lq9VaPBpCgOJLy+p5YyliBpD0pccegPEyKWC0mUEypunggS0ZShEzgLwvCjCc0o4wWmvoicJzJnbRegzVkQ7QojQRG8WfxmvwaD2famYaIOlKDy01ZjxbYor4zr/rrc2YJCpqlzMR4cEDuHwnXpzTAElTemxAhF6y8Ez5LODUs5LRMEWhqd8no+vjANFvKjTO07mw/EtKRrgvZSVp1I+CE1JkHCCpEd8ja9OPyjM/rU8IRjtRLlsKTNTTGgaIDgyi9EhDzw1UOMyW3rPdL3aJK+lHt+YOAyQl43zGM+xcVyXIc6m91IIQwaHRETVrGCCpGIACjiZHaFfwvx3YZMmPDBrr/QDRbyIkMvfkuehn1iXBRWKT2C7ToJrVD5A0orYCjjE2SElFtmXnEPcPOHj6AcI/9uG8hTIEbdm2KScK2yxNb2S9C5AUYh+U7lxUJ+9A5uxKvQBqlsmYeqPUY0hqDHVhv3+EbN3DzcYpTL3fFhnGFvsVJoeXii05OZEIN/QY612A8I99GOXQGJFTG7R4fuJQ4bvJSKtRP+2b9EsIN54NZUfTqY9itJsvUU+ooAsQ3m8c2jPpzOZKB0hcKu0AwTyq09GVo4zriKplCpLOWjcBwt17RenStdk+bJDUZroCao5+JT5iujwdb1YTILw9H1uobXRGttHJ5q1KCUwzqaUXlFKK2ADTlJ1yvK/1MmwChLP9QWmY48K+L+I8J4ZrTKdmzdWvnnMOu0INl8zxttZLqQkQvu5dereu3VzpjHU7gNAZ68gvIkVMUNN4GVYA0Xrqf0xaiH4PpYpTDt7mRCxK6WXTr2WJmsl14bzGk4OPdkPDDqkAwtn+IDotqEFic/cnrfQyTwPZgc31iMzmql4M6F4+j8ZuKXZUs0MqgPC1P6yr4RmviYnKQSk9bKQXtfQo+zaZszEBM72xZodUALHTjWNShlYPb498mGFwT/sFaVS73vdwvhtG03EvvXH1P6vFEDXLhFw3dkgdIAeTJ6PfE+pN2p5IWQIU/47lR6nq8Y4RrExzKYtdx+rXxsUdfcFZdHhjh2iAcM6/ogzSsaB9gEEg0L4stt3ehdyusrr8CfSE2xXwg/8uK9TjN/4fP3INUeAXOAAU6jNrgPA10ElOKs2OC7TkeQilSZ8WJUrTr3jJb4mOheVKgPwAxMakPW6XcYlIbgMnH08pJXRxawTF2Ga28vgGHMbu+EHV8efiLJPu+SUiUdoLdjTUNUD46qR0EWxyjo3UoAbG+UQha310A8VZJlqbeArtnU+AMBIBZurmGHsrAcK1tChdBHsmOjt3Ow4MlBAaEJ9hD8m38AlxoW26AttGA2ZoS0CInjm0WbycS4DY5CXFHHxYF2/MmZj2NQ6M+arTV+PiqIqbUtfmvsL+XUF+DucqGMsCiI5RYKS7/bbGk6/esKkYqWM4uZ9dcgMQvlX5QuRg2bxDYt6rU1AQHGgwlxeqUS9h6+8m5lCM+8odKBBiWIGLl6sHC9dpGTaIXgM81qD0TKV1nglfJ48x1ntvhBxA7gDJ24vVr1JtoTL9RTDD249lhp/OMccLXL0rxTdJERdjA4bSWag1nbXd/r3p15DBe0mewRtroqb77WONx7efAiC8xWOekfSu5AibGOnLKLbP837pms8GbGDuADE6Bch8xkzubG6aygscJYlzAMkRIFiChm8QKMR+jDlx0nSK7MFLdRm0QN2cc00fJNcoQbhG0fXS5uTqbXsMcwN/HxjTBskaAcK90kW4HYUx3646IIu01q5cypI+Mefh0le6ICkAwjXNpFqKHPaENJ0hy8oQwJW0qQfmAsIwz2zSAIhSaTNUc79NmGIMYRiErlXuVTv7Z7pFgGCpH+4H5aTr7m3HBpZgdwzBiu/GvKER71IBCOrsz4MVMqB7T3ZbahrmdJkBOncL01OwQrw+OkF7xHbe09GgxrZPj+1jmzj2S++201K1EgII7oz7Rt33XqCYDTTVCrrK9GNGr++LRDsT0LN50kMq/x2eaVVVKVQs7l6surF+kVTMoC49qNzV02oKxlYeOUuS6Te8vxRMJ2+rMNLTAQiqErCJJaYQ8OqrzmxUnrhpBnZ3IZtWtzlW/HCmTTpSpHDz8g4UdlchDY9WU73yf+uWdDBzy7v1Z/pmpygFyzsHsKR2EUnnnWrS95qiWCDn15/hg3VViDIoaAYQt5fItPqmJ08hDU37MiR3oNsKgKRYzJi/qlW9ePxsgvbKm6yXqyvZTPWho70Z2APxvlGzVwgQ3IT/g9HtvG7iu1cklHqF9J+uIeB3EtcUP9BKQ968d8zm5T3IcVC6qRKhgV5XH1zf5mNj1DEQVI3bAV7cw46evp3XFPtBorcBP4GsBqrL1ClA1Z9tOwnsKJyeUggGnO51/I66ezeUvVSV4XkAg0E17gMU3F6T7UZE5v0CqjgeirjITv0OVVWo62+ZqXS+q+H+PPAW9z3pZpPjBpK6neDrEjWjQLp3cbZDIOiKAMFSkyiu074odWNfSlSu8/Si/75zt32esxe1KPvDXczZEJwqWm3TZ9+91VvRPzXDdyzcn59yCsw5fpD+ZenRFDJ6TUk1fy2tCiB8PW2m1Ax9H1+AFC+3EiAppZuYLBlmtj739uaY9NQvQcoXjltE27XfFJ/ju9uweLlxP/7AZ8nRs4Mg2fg04vRstcdGJMgUAU0Cn1NthPm9CCGUAEk5FjJOnjk8XFUCqNggU8zL1Ug/8g33I9imyDv9u+/+iOkeundUAKFLy3AZRwrPcAXIkW/4H+Lpt8h7KOV5nyx4ZjqWeoa0xEHGqcY1DnJMyNQAycnV21yOeYzkumfmeBikKbYWdR/fVJMbyV8/J51/+R9b7plDvcIx1vdVcApg2tIv9P2m+09Cj6Pb/o3tWAdIbob6POpVVyLPI8XiM5V9j1ztj1qZqQoguaScVMtkzpha1L+AR8/hs4EPZq5+gCS9rbObuF5OiWKDkT378X6Cc52smuZRASQ3O8REvWoCY6w2GJ4iuy2yWvGscayw8iv8/6wot9O92rWwRM3q0ojvyWYNzaMCCE6Bq0fB/l04rl7pTUd4CCVKDIqieQgU/JRttduUpMX2GvItFtII7rYBkosd0q9e0QPDHLqiZlW04qzOtxJemwDhPHBzVuxWYZwTGOW4uWQa29AxxL1a/cTcv5MQzXu32VLNmwDJww6p1CsOwKivmEgRxfxMzI5q3gRIHnbIuqhRS2tjeL+Yjg0sO3lRO0WwDhuF3Ue1JvV2Oqp5H0BSt0PQ44Tim+cizJE8GYKVbNvU2gmCA4tt87x61OAuQHTFDJyIXGEoQFsnK8wY6VvluzGqmmuPCtwFSB5qFv0C07ZoHsSk7Xee1vjGPOr06FV/hwCSupo1DyPY9LoUr5bOt0J+4qnylms2EMztB0ge3iwbdp3n3twj7PyN8nLdBwPL/QARNSseYHI12oerP8ajrXlPgyrvMED4piKbTzuVO3MDibY58Ag33mpVpV4NHu83DBBRs+LCKxd1i2+VkqH1HM3bGwaIVrNSPBohLmPT9raBLcLPo28RppiDfqGiMX5O0VzENkYLoI8DJJfcrIjUJugK96JcJHWirwYHvkyfEcw/bhMTxcXHASLGetzFavbG82iHPoqk+yKdTP2ZBogY63OCBCtEvmJ/sm+q9qrBprppgIgUmRMgZd87CLZdq6/hABuuV3qb7SalB5LaDCAiRTix5dVRquw4DYp5GnuXVAbSwxwg3De5sOKUaINZF0UlPqu3zoUlxoaKa34HMm9vqafFPvzHIMGm7v9KYdHuFC7jkrBmEgSnnJ5/O4WFohzjugDLAaqyuByVhpvLDsV+jQfwjd6oE/jUA31oD12OgjEVNcsiMGsOEM5lWijZLJ+2ykISqIrpf2NVlk/qI0iFe8dp4t4MBEEbDGNU0BvS+g4KTSGlHSvS/KIemcaazAEiUiQf6NDMpGsLpeDutcxYsANIqu48GoaQVvop0IzX1Avm8aOYlfTA4dsBBJ9IY/MLv6XJe0S4SxLVrmvW6UkWtke5XPYAwSf5Fv3Kmw35z05XoOSZj+W0i9MNILJvnT+rygjrFNiDc+LMxbvnBhAtRfBs9fSS04RxlkcBj+3N7gARg315jJbmjK0N8/o03QGCrUgKSposs6RRG6aUDJHEDyBisC+J1VKcq5NhTidBsCW+58yluKAyZjoKOBvmtADRUkTqaNEtrLREQQHLiHk4FatsuX70McUEpQ2hgDsFrtU3sG2Z4PK3QcpBSDIjwXJIEwQU8PJatfunAwi2LAFEgvWVJjwogCkvzyn3x9ACROwRj7WVR70pQGR30Bvp7ZlJrpb3WksD1hQgszvCA0TsEevVlQe8KEBqd4QHiNgjXqstD1tRgNzuiAMQDRI8GwIr7sklFAhBgeBVKOmN9K49IkHEEKwhbSrwWF1MVlvxpFN4gGjPlhTB9lwoebxDgSilWeMARINE9o8Il1NRIAo4cLDxAKJBgqfnnlJRSdpZJAWuoGzPS9OyPb4UiguQFM7K9qWoPB+SAmsAx0UscMSXINijgCQkA+Xc9hbAcRYTHPMARECSMxOHmttsJ2/FVbHq5NOSBA13sUlCsVUe7V6D5LiMLTlK0s0HkHIE4t3Kg43DzCKqQd43hfkBor1bEkwMw2DptupRqody0jwAgjOS4xUo1zXltqoypgxmwQcgAhIG7DD7EFiBYz4v1tg6yK7E2bl0pgEETzx0mRcvCVLOQGIlLmuZ8jOzuXGniMYTIJWHS4z3qRVM+3eUGi/VE3XFdRq8AYJUE5WLK+/4jmsH4LgEcKx9Gwr5PH+A4OxF5QrJA3O0zValahMjDYCIyjUHE4fok71KlTZAcPS6FjCmqJyEWEFpMxgFNnCIzaXLITbBRmTQcFoSpD4hOSvRYHlZ3JKc1KhTLV2AVLYJerrOWbCCDKJOATybHfdvzJZoSLEcaQOkpICoXRS8QNlGkupUHwHyAEg5M1G7KJncpS2tTv0Gx0GfKZQgyV95AUSAMhdDZgeMkpB5AkSAEgso2QJjGQBpAuV7+O9JLM7JvB+Mgr/KSZUaWq+8JUh71tpGEaC4o3cDwHizBGAsS4J0gfIMgPId/PncnVcW8yQa29siyPc/tcvF+DZdvWVJkD6qaKnyAn56aEq0hdy3OGnRt64CkJIqGEv5AqTKAQCjoOLKMq8d1Nq8Vr+DGvUtSA25IpceTYXgPylUwZ4eVbDcwYJAwMDeawFFl0FFgkyBFlPt7wBgVgVgnmUgXTQgDuoDGNxbAcU4AwhApgDS/v0duIoPNxLmNAHAoJGNgHgjgLBd7NjV3e3Hx/8JDZiHIGHuglp2D/6NoDk5fmKNH0Gwu/l8Vj/DODZL9DpRE1wkCDVF6+2V4LmtHhR/PtwEKhFAeKF9U9o45d/w72UeE37XP+VvGMH+GUC5he+9ACHcIv4fun+z+brp8FUAAAAASUVORK5CYII=",
    "name" => "微信登录",
    "description" => "微信登录",
    "form_fields" => [
        [
            'id' => 'status',
            'name' => '开启状态',
            'required' => true,
            'type' => 'radio',
            'data' => [
                [
                    'name' => '关闭',
                    'value' => 0,
                ],
                [
                    'name' => '开启',
                    'value' => 1,
                ]
            ],
        ],
        [
            'id' => 'appid',
            'name' => '公众号APPID',
            'placeholder' => '请输入APPID',
            'required' => true,
            'type' => 'input',
        ],
        [
            'id' => 'app_secret',
            'name' => '公众号Secret',
            'placeholder' => '请输入公众号Secret',
            'required' => true,
            'type' => 'input',
        ],
    ],
    "hook" => [
        'MerchantSystemConfigAfter' => 'plugin\OauthWeixin\Hook',
    ],
];
