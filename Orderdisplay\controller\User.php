<?php

namespace plugin\Orderdisplay\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Log;
use think\facade\Db;

class User extends BasePlugin {

    protected $scene = ['user'];  // 只允许普通用户访问
    
    protected $noNeedLogin = [];

    public function index() {
        return View::fetch();
    }

    // 获取订单列表
    public function getOrderList() {
        try {
            // 检查功能和密码验证
            $configFile = app()->getRootPath() . 'plugin/Orderdisplay/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            if (!($config['orderdisplay_config']['status'] ?? true)) {
                return json(['code' => 403, 'msg' => '功能已关闭']);
            }
            
            if (($config['orderdisplay_config']['need_password'] ?? false) && !session('order_display_verified')) {
                return json(['code' => 401, 'msg' => '需要密码验证']);
            }

            $page = input('page/d', 1);
            $limit = input('limit/d', 10);
            $offset = ($page - 1) * $limit;
            $sort = input('sort/s', 'success_time');
            $order = input('order/s', 'desc');
            $search = input('search/s', '');
            $dateRange = input('dateRange/a', []);

            // 构建基础查询
            $query = Db::name('order')
                ->where('user_id', $this->user->id)
                ->where('status', 1);  // 只查询成功的订单

            // 添加时间范围筛选
            if (!empty($dateRange) && count($dateRange) == 2) {
                $startDate = $dateRange[0] . ' 00:00:00';
                $endDate = $dateRange[1] . ' 23:59:59';
                $query->whereBetweenTime('success_time', $startDate, $endDate);
            }

            // 添加交易号搜索
            if (!empty($search)) {
                $query->where('trade_no', 'like', "%{$search}%");
            }

            // 查询字段
            $query->field([
                'trade_no',
                "DATE_FORMAT(FROM_UNIXTIME(success_time), '%H:%i %Y/%m/%d') as success_time",
                'total_amount'
            ]);

            // 设置排序
            if (in_array($sort, ['success_time', 'total_amount'])) {
                $query->order($sort, $order === 'ascending' ? 'asc' : 'desc');
            }

            // 获取总记录数和分页数据
            $total = $query->count();
            $orders = $query->limit($offset, $limit)->select()->toArray();

            // 处理脱敏
            if ($config['orderdisplay_config']['desensitization'] ?? false) {
                foreach ($orders as &$order) {
                    // 保留前4位和后4位，中间用星号代替
                    $length = strlen($order['trade_no']);
                    if ($length > 8) {
                        $stars = str_repeat('*', $length - 8);
                        $order['trade_no'] = substr($order['trade_no'], 0, 4) . $stars . substr($order['trade_no'], -4);
                    }
                }
                unset($order);
            }

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $orders,
                'total' => $total
            ]);

        } catch (\Exception $e) {
            Log::error('获取订单列表失败：' . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取数据失败：' . $e->getMessage()]);
        }
    }

    // 验证密码
    public function verifyPassword() {
        try {
            $password = input('password/s', '');
            $configFile = app()->getRootPath() . 'plugin/Orderdisplay/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            if ($password === ($config['orderdisplay_config']['password'] ?? '')) {
                session('order_display_verified', true);
                return json(['code' => 200, 'msg' => '验证成功']);
            }
            
            return json(['code' => 403, 'msg' => '密码错误']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '验证失败：' . $e->getMessage()]);
        }
    }

    // 检查验证状态
    public function checkVerified() {
        try {
            $configFile = app()->getRootPath() . 'plugin/Orderdisplay/params.php';
            $config = file_exists($configFile) ? include $configFile : [];
            
            if (!($config['orderdisplay_config']['status'] ?? true)) {
                return json(['code' => 403, 'msg' => '功能已关闭']);
            }
            
            if (!($config['orderdisplay_config']['need_password'] ?? false) || session('order_display_verified')) {
                return json(['code' => 200, 'msg' => '已验证']);
            }
            
            return json(['code' => 401, 'msg' => '需要密码验证']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '验证失败：' . $e->getMessage()]);
        }
    }
} 