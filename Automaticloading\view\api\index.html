<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动上货管理系统</title>
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <style>
        body {
            margin: 0;
            background: #f5f7fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
        }

        .container {
            padding: 20px;
            min-height: calc(100vh - 40px);
            box-sizing: border-box;
            overflow-y: auto;
        }

        h1 {
            text-align: center;
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            margin: 0 0 20px;
            padding: 20px 0;
        }

        /* 仪表盘样式 */
        .dashboard {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        }

        .stat-title {
            font-size: 14px;
            color: #909399;
            margin-bottom: 10px;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #303133;
        }

        /* 主内容区域 */
        .main-content {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
            overflow: visible;
            height: auto;
        }

        /* 搜索框样式 */
        .search-box {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        }

        /* 表格容器 */
        .table-container {
            margin: 20px;
        }

        .table-container .el-table {
            border-radius: 8px;
            overflow: hidden;
        }

        .table-container .el-table th {
            background-color: #f5f7fa !important;
            font-weight: bold;
            color: #606266;
            font-size: 15px;
        }

        /* 详情对话框样式 */
        .el-dialog {
            border-radius: 8px;
        }

        .el-dialog__header {
            margin: 0;
            padding: 20px 24px;
            border-bottom: 1px solid #dcdfe6;
        }

        .el-dialog__body {
            padding: 24px;
        }

        .el-dialog__footer {
            padding: 16px 24px;
            border-top: 1px solid #dcdfe6;
        }

        /* 表单样式 */
        .config-form {
            padding: 20px;
        }

        .pattern-form {
            background: #f9fafc;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #ebeef5;
        }

        .pattern-item {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            align-items: center;
        }

        /* 美化滚动条 */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        ::-webkit-scrollbar-thumb {
            border-radius: 3px;
            background: #dcdfe6;
        }

        ::-webkit-scrollbar-track {
            border-radius: 3px;
            background: #f5f7fa;
        }

        /* 响应式设计 */
        @media screen and (max-width: 768px) {
            .dashboard {
                grid-template-columns: repeat(2, 1fr);
            }

            .pattern-item {
                flex-direction: column;
            }

            .container {
                padding: 10px;
                height: auto;
            }

            .search-box {
                padding: 15px;
            }

            .el-table {
                font-size: 13px;
            }
        }

        @media screen and (max-width: 576px) {
            .dashboard {
                grid-template-columns: 1fr;
            }

            h1 {
                font-size: 20px;
                padding: 15px 0;
            }

            .stat-card {
                padding: 15px;
            }
        }

        /* 动画效果 */
        .el-button {
            transition: all 0.3s ease;
        }

        .el-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        /* 美化JSON显示区域 */
        pre {
            background: #f5f7fa;
            padding: 15px;
            border-radius: 8px;
            white-space: pre-wrap;
            overflow-y: auto;
            color: #333;
            font-family: 'Courier New', Courier, monospace;
            line-height: 1.6;
            border: 1px solid #ebeef5;
        }

        /* 分页器样式 */
        .pagination-container {
            padding: 20px;
            text-align: right;
        }

        /* 用户信息样式 */
        .user-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .username-cell {
            font-weight: 500;
            color: #303133;
        }

        .balance-value {
            color: #67c23a;
            font-weight: 600;
        }

        /* 标签样式 */
        .el-tag--info {
            background-color: #f4f4f5;
            border-color: #e9e9eb;
            color: #909399;
        }
    </style>
</head>
<body>
    <div id="app" class="container">
        <h1>自动上货管理系统</h1>
        
        <!-- 统计信息面板 -->
        <div class="dashboard">
            <div class="stat-card">
                <div class="stat-title">今日处理数据</div>
                <div class="stat-number">{{ statistics.today }}</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">总处理数据</div>
                <div class="stat-number">{{ statistics.total }}</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">未使用数据</div>
                <div class="stat-number">{{ statistics.unused }}</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">已使用数据</div>
                <div class="stat-number">{{ statistics.used }}</div>
            </div>
        </div>
        
        <!-- 主要内容区域 -->
        <div class="main-content">
            <el-tabs v-model="activeTab" @tab-click="handleTabClick">
                <!-- 配置管理标签页 -->
                <el-tab-pane label="配置管理" name="config">
                    <div class="config-form">
                        <el-form :model="form" label-width="120px">
                            <el-form-item label="功能状态">
                                <el-switch v-model="form.status" active-text="开启" inactive-text="关闭" />
                                <div style="color: #909399; font-size: 12px; margin-top: 5px;">
                                    是否启用自动上货功能
                                </div>
                            </el-form-item>
                            
                            <div class="pattern-form">
                                <h4 style="margin-top: 0">匹配规则配置</h4>
                                <div style="color: #909399; font-size: 12px; margin-bottom: 15px;">
                                    配置数据匹配规则，键为字段名称，值为正则表达式
                                </div>
                                
                                <div v-for="(pattern, index) in patterns" :key="index" class="pattern-item">
                                    <el-input v-model="pattern.key" placeholder="字段名称" style="width: 200px" />
                                    <el-input v-model="pattern.value" placeholder="正则表达式" />
                                    <el-button type="danger" @click="removePattern(index)">删除</el-button>
                                </div>
                                
                                <el-button type="primary" @click="addPattern" style="margin-top: 10px">
                                    添加规则
                                </el-button>
                            </div>
                            
                            <div class="pattern-form">
                                <h4 style="margin-top: 0">文件上传配置</h4>
                                <div style="color: #909399; font-size: 12px; margin-bottom: 15px;">
                                    配置允许的文件类型和大小限制
                                </div>
                                
                                <el-form-item label="允许的扩展名">
                                    <el-select v-model="uploadConfig.allowed_extensions" multiple placeholder="请选择文件类型" style="width: 100%">
                                        <el-option label="TXT文本文件" value="txt" />
                                        <el-option label="CSV表格文件" value="csv" />
                                        <el-option label="Excel文件" value="xlsx" />
                                    </el-select>
                                </el-form-item>
                                
                                <el-form-item label="最大文件大小">
                                    <el-input-number 
                                        v-model="uploadConfig.max_size"
                                        :min="1"
                                        :max="10240"
                                        :step="1024"
                                    />
                                    <span style="margin-left: 10px; color: #909399;">
                                        KB ({{ (uploadConfig.max_size/1024).toFixed(2) }}MB)
                                    </span>
                                </el-form-item>
                            </div>
                            
                            <el-form-item>
                                <el-button type="primary" @click="saveConfig" :loading="saveLoading">
                                    保存配置
                                </el-button>
                            </el-form-item>
                        </el-form>
                    </div>
                </el-tab-pane>
                
                <!-- 历史记录标签页 -->
                <el-tab-pane label="处理历史" name="history">
                    <div class="search-box">
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-input 
                                    v-model="historySearch" 
                                    placeholder="搜索用户、商品或数据内容" 
                                    clearable
                                    @keyup.enter="searchHistory"
                                    @clear="searchHistory">
                                    <template #append>
                                        <el-button @click="searchHistory">
                                            <el-icon><Search /></el-icon>
                                        </el-button>
                                    </template>
                                </el-input>
                            </el-col>
                            <el-col :span="10">
                                <el-date-picker
                                    v-model="historyDateRange"
                                    type="daterange"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    :shortcuts="dateShortcuts"
                                    @change="searchHistory"
                                />
                            </el-col>
                        </el-row>
                    </div>
                    
                    <div class="table-container">
                        <el-table 
                            :data="historyRecords" 
                            v-loading="historyLoading"
                            border>
                            <el-table-column prop="username" label="用户" width="120" align="center" />
                            <el-table-column label="匹配商品" min-width="180" align="center">
                                <template #default="scope">
                                    <div class="user-info">
                                        <el-image 
                                            style="width: 40px; height: 40px;"
                                            :src="scope.row.goods_image"
                                            fit="cover">
                                            <template #error>
                                                <div class="image-slot">
                                                    <el-icon><Picture /></el-icon>
                                                </div>
                                            </template>
                                        </el-image>
                                        <span>{{ scope.row.goods_name }}</span>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column prop="create_time" label="创建时间" width="160" align="center" />
                            <el-table-column label="状态" width="100" align="center">
                                <template #default="scope">
                                    <el-tag :type="scope.row.status === 0 ? 'info' : 'success'">
                                        {{ scope.row.status === 0 ? '未使用' : '已使用' }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" width="120" align="center">
                                <template #default="scope">
                                    <el-button
                                        type="primary"
                                        link
                                        @click="viewRecordDetails(scope.row)">
                                        查看详情
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                        
                        <div class="pagination-container">
                            <el-pagination
                                v-model:current-page="historyPage"
                                v-model:page-size="historyLimit"
                                :page-sizes="[10, 20, 50, 100]"
                                :total="historyTotal"
                                layout="total, sizes, prev, pager, next"
                                @size-change="handleHistorySizeChange"
                                @current-change="handleHistoryPageChange"
                            />
                        </div>
                    </div>
                </el-tab-pane>
                
                <!-- 商品管理标签页 -->
                <el-tab-pane label="商品匹配" name="goods">
                    <div class="search-box">
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-input 
                                    v-model="goodsSearch" 
                                    placeholder="搜索商品名称" 
                                    clearable
                                    @keyup.enter="searchGoods"
                                    @clear="searchGoods">
                                    <template #append>
                                        <el-button @click="searchGoods">
                                            <el-icon><Search /></el-icon>
                                        </el-button>
                                    </template>
                                </el-input>
                            </el-col>
                        </el-row>
                    </div>
                    
                    <div class="table-container">
                        <el-table 
                            :data="goodsList" 
                            v-loading="goodsLoading"
                            border>
                            <el-table-column prop="id" label="ID" width="80" align="center" />
                            <el-table-column label="商品名称" min-width="200" align="center">
                                <template #default="scope">
                                    <div class="user-info">
                                        <el-image 
                                            style="width: 40px; height: 40px;"
                                            :src="scope.row.image"
                                            fit="cover">
                                            <template #error>
                                                <div class="image-slot">
                                                    <el-icon><Picture /></el-icon>
                                                </div>
                                            </template>
                                        </el-image>
                                        <span>{{ scope.row.name }}</span>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column prop="price" label="价格" width="120" align="center">
                                <template #default="scope">
                                    {{ scope.row.price }}元
                                </template>
                            </el-table-column>
                            <el-table-column prop="create_time" label="创建时间" width="160" align="center" />
                            <el-table-column label="状态" width="100" align="center">
                                <template #default="scope">
                                    <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
                                        {{ scope.row.status_text }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" width="120" align="center">
                                <template #default="scope">
                                    <el-button
                                        type="primary"
                                        link
                                        @click="viewGoodsHistory(scope.row)">
                                        匹配历史
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                        
                        <div class="pagination-container">
                            <el-pagination
                                v-model:current-page="goodsPage"
                                v-model:page-size="goodsLimit"
                                :page-sizes="[10, 20, 50, 100]"
                                :total="goodsTotal"
                                layout="total, sizes, prev, pager, next"
                                @size-change="handleGoodsSizeChange"
                                @current-change="handleGoodsPageChange"
                            />
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </div>
        
        <!-- 记录详情对话框 -->
        <el-dialog
            v-model="detailsDialogVisible"
            title="数据详情"
            width="70%">
            <div v-if="currentRecord" style="max-height: 70vh; overflow: auto;">
                <pre>{{ formatJson(currentRecord.secret) }}</pre>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="detailsDialogVisible = false">关闭</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
    
    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>
    
    <script>
    const { createApp, ref, reactive, onMounted, watch, onUnmounted } = Vue;
    const { ElMessage, ElMessageBox } = ElementPlus;
    
    createApp({
        setup() {
            // 当前选中的标签页
            const activeTab = ref('config');
            
            // 统计数据
            const statistics = ref({
                today: 0,
                total: 0,
                unused: 0,
                used: 0
            });
            
            // ===== 配置管理相关 =====
            const form = reactive({
                status: true
            });
            
            const patterns = ref([]);
            const saveLoading = ref(false);
            
            // 文件上传配置
            const uploadConfig = reactive({
                allowed_extensions: ['txt'],
                max_size: 2048, // 2MB in KB
                mime_types: ['text/plain']
            });
            
            // 添加MIME类型
            const addMimeType = () => {
                uploadConfig.mime_types.push('');
            };
            
            // 移除MIME类型
            const removeMimeType = (index) => {
                uploadConfig.mime_types.splice(index, 1);
            };
            
            // 添加匹配规则
            const addPattern = () => {
                patterns.value.push({
                    key: '',
                    value: ''
                });
            };
            
            // 移除规则项
            const removePattern = (index) => {
                patterns.value.splice(index, 1);
            };
            
            // 获取配置
            const getConfig = async () => {
                try {
                    const res = await axios.get('/plugin/Automaticloading/Api/getConfig');
                    if (res.data.code === 0 && res.data.data) {
                        const config = res.data.data.auto_loading_config || {};
                        
                        // 设置开关状态
                        form.status = config.status == 1;
                        
                        // 添加匹配规则
                        if (config.match_patterns) {
                            patterns.value = [];
                            for (const key in config.match_patterns) {
                                patterns.value.push({
                                    key: key,
                                    value: config.match_patterns[key]
                                });
                            }
                        }
                        
                        // 获取上传配置
                        getUploadConfig();
                    }
                } catch (error) {
                    console.error(error);
                    ElMessage.error('获取配置失败');
                }
            };
            
            // 获取上传配置
            const getUploadConfig = async () => {
                try {
                    const res = await axios.get('/plugin/Automaticloading/Api/getUploadConfig');
                    if (res.data.code === 200 && res.data.data) {
                        const config = res.data.data;
                        uploadConfig.allowed_extensions = config.allowed_extensions || ['txt'];
                        uploadConfig.max_size = config.max_size || 2048;
                        uploadConfig.mime_types = config.mime_types || ['text/plain'];
                    }
                } catch (error) {
                    console.error(error);
                    ElMessage.error('获取上传配置失败');
                }
            };
            
            // 保存配置
            const saveConfig = async () => {
                try {
                    saveLoading.value = true;
                    // 获取所有的匹配规则
                    const matchPatterns = {};
                    patterns.value.forEach(pattern => {
                        if (pattern.key && pattern.value) {
                            matchPatterns[pattern.key] = pattern.value;
                        }
                    });
                    
                    const res = await axios.post('/plugin/Automaticloading/Api/saveConfig', {
                        status: form.status ? 1 : 0,
                        match_patterns: matchPatterns
                    });
                    
                    if (res.data.code === 0) {
                        // 保存文件上传配置
                        await saveUploadConfig();
                        ElMessage.success('保存成功');
                    } else {
                        ElMessage.error(res.data.msg || '保存失败');
                    }
                } catch (error) {
                    console.error(error);
                    ElMessage.error('保存失败');
                } finally {
                    saveLoading.value = false;
                }
            };
            
            // 保存上传配置
            const saveUploadConfig = async () => {
                try {
                    const res = await axios.post('/plugin/Automaticloading/Api/updateUploadConfig', {
                        allowed_extensions: uploadConfig.allowed_extensions,
                        max_size: uploadConfig.max_size,
                        mime_types: uploadConfig.mime_types.filter(mime => mime.trim() !== '')
                    });
                    
                    if (res.data.code !== 200) {
                        ElMessage.error(res.data.msg || '保存上传配置失败');
                    }
                } catch (error) {
                    console.error(error);
                    ElMessage.error('保存上传配置失败');
                }
            };
            
            // ===== 历史记录相关 =====
            const historyRecords = ref([]);
            const historyPage = ref(1);
            const historyLimit = ref(10);
            const historyTotal = ref(0);
            const historyLoading = ref(false);
            const historySearch = ref('');
            const historyDateRange = ref([]);
            
            // 用户选择相关
            const selectedUserId = ref('');
            const userOptions = ref([]);
            const userSelectLoading = ref(false);
            
            // 搜索用户
            const searchUsers = async (query) => {
                if (query) {
                    userSelectLoading.value = true;
                    try {
                        const res = await axios.get('/plugin/Automaticloading/Api/getUserList', {
                            params: {
                                search: query,
                                limit: 10
                            }
                        });
                        
                        if (res.data.code === 200) {
                            userOptions.value = res.data.data;
                        } else {
                            ElMessage.error(res.data.msg || '获取用户列表失败');
                        }
                    } catch (error) {
                        console.error(error);
                        ElMessage.error('获取用户列表失败');
                    } finally {
                        userSelectLoading.value = false;
                    }
                } else {
                    userOptions.value = [];
                }
            };
            
            // 日期快捷选项
            const dateShortcuts = [
                {
                    text: '最近一周',
                    value: () => {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        return [start, end];
                    },
                },
                {
                    text: '最近一个月',
                    value: () => {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        return [start, end];
                    },
                },
                {
                    text: '最近三个月',
                    value: () => {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        return [start, end];
                    },
                }
            ];
            
            // 获取历史记录
            const getHistoryRecords = async () => {
                try {
                    historyLoading.value = true;
                    
                    const res = await axios.get('/plugin/Automaticloading/Api/getHistoryRecords', {
                        params: {
                            page: historyPage.value,
                            limit: historyLimit.value,
                            search: historySearch.value,
                            dateRange: historyDateRange.value || [],
                            user_id: selectedUserId.value || 0
                        }
                    });
                    
                    if (res.data.code === 200) {
                        historyRecords.value = res.data.data;
                        historyTotal.value = res.data.total;
                    } else {
                        ElMessage.error(res.data.msg || '获取历史记录失败');
                    }
                } catch (error) {
                    console.error(error);
                    ElMessage.error('获取历史记录失败');
                } finally {
                    historyLoading.value = false;
                }
            };
            
            // 搜索历史记录
            const searchHistory = () => {
                historyPage.value = 1;
                getHistoryRecords();
            };
            
            // 处理分页大小变化
            const handleHistorySizeChange = (size) => {
                historyLimit.value = size;
                getHistoryRecords();
            };
            
            // 处理页码变化
            const handleHistoryPageChange = (page) => {
                historyPage.value = page;
                getHistoryRecords();
            };
            
            // ===== 商品管理相关 =====
            const goodsList = ref([]);
            const goodsPage = ref(1);
            const goodsLimit = ref(10);
            const goodsTotal = ref(0);
            const goodsLoading = ref(false);
            const goodsSearch = ref('');
            
            // 获取商品列表
            const getGoodsList = async () => {
                try {
                    goodsLoading.value = true;
                    
                    const res = await axios.get('/plugin/Automaticloading/Api/getGoodsList', {
                        params: {
                            page: goodsPage.value,
                            limit: goodsLimit.value,
                            search: goodsSearch.value
                        }
                    });
                    
                    if (res.data.code === 200) {
                        goodsList.value = res.data.data;
                        goodsTotal.value = res.data.total;
                    } else {
                        ElMessage.error(res.data.msg || '获取商品列表失败');
                    }
                } catch (error) {
                    console.error(error);
                    ElMessage.error('获取商品列表失败');
                } finally {
                    goodsLoading.value = false;
                }
            };
            
            // 搜索商品
            const searchGoods = () => {
                goodsPage.value = 1;
                getGoodsList();
            };
            
            // 处理分页大小变化
            const handleGoodsSizeChange = (size) => {
                goodsLimit.value = size;
                getGoodsList();
            };
            
            // 处理页码变化
            const handleGoodsPageChange = (page) => {
                goodsPage.value = page;
                getGoodsList();
            };
            
            // 查看特定商品的匹配历史
            const viewGoodsHistory = (goodsId) => {
                activeTab.value = 'history';
                historySearch.value = '';
                historyPage.value = 1;
                // 查询特定商品ID
                setTimeout(() => {
                    getHistoryRecords();
                }, 100);
            };
            
            // ===== 获取统计数据 =====
            const getStatistics = async () => {
                try {
                    const res = await axios.get('/plugin/Automaticloading/Api/getStatistics');
                    if (res.data.code === 200) {
                        statistics.value = res.data.data;
                    }
                } catch (error) {
                    console.error(error);
                }
            };
            
            // ===== 详情对话框相关 =====
            const detailsDialogVisible = ref(false);
            const currentRecord = ref(null);
            
            // 查看详情
            const viewRecordDetails = (record) => {
                currentRecord.value = record;
                detailsDialogVisible.value = true;
            };
            
            // 格式化JSON
            const formatJson = (jsonStr) => {
                try {
                    if (typeof jsonStr === 'string') {
                        const obj = JSON.parse(jsonStr);
                        return JSON.stringify(obj, null, 2);
                    } else {
                        return JSON.stringify(jsonStr, null, 2);
                    }
                } catch (e) {
                    return jsonStr;
                }
            };
            
            // 处理标签切换
            const handleTabClick = (tab) => {
                if (tab.props.name === 'history') {
                    getHistoryRecords();
                } else if (tab.props.name === 'goods') {
                    getGoodsList();
                }
            };
            
            // 初始化
            onMounted(() => {
                getConfig();
                getStatistics();
            });
            
            // 每60秒刷新一次统计数据
            let statsTimer = null;
            onMounted(() => {
                statsTimer = setInterval(() => {
                    getStatistics();
                }, 60000);
            });
            
            // 清理定时器
            onUnmounted(() => {
                if (statsTimer) clearInterval(statsTimer);
            });
            
            return {
                activeTab,
                statistics,
                
                // 配置相关
                form,
                patterns,
                saveLoading,
                addPattern,
                removePattern,
                saveConfig,
                
                // 上传配置相关
                uploadConfig,
                addMimeType,
                removeMimeType,
                
                // 历史记录相关
                historyRecords,
                historyPage,
                historyLimit,
                historyTotal,
                historyLoading,
                historySearch,
                historyDateRange,
                dateShortcuts,
                searchHistory,
                handleHistorySizeChange,
                handleHistoryPageChange,
                
                // 商品相关
                goodsList,
                goodsPage,
                goodsLimit,
                goodsTotal,
                goodsLoading,
                goodsSearch,
                searchGoods,
                handleGoodsSizeChange,
                handleGoodsPageChange,
                viewGoodsHistory,
                
                // 详情相关
                detailsDialogVisible,
                currentRecord,
                viewRecordDetails,
                formatJson,
                
                // 其他
                handleTabClick,
                
                // 用户选择相关
                selectedUserId,
                userOptions,
                userSelectLoading,
                searchUsers
            };
        }
    }).use(ElementPlus, {
        locale: ElementPlusLocaleZhCn
    }).mount('#app');
    </script>
</body>
</html> 