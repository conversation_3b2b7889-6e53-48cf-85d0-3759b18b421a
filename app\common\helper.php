<?php
/**
 * 通用辅助函数
 */

use app\common\service\Config as ConfigService;
use think\facade\Config;
use think\facade\Db;

// ... 其他辅助函数 ...

/**
 * 获取插件配置参数
 * @param string $name 配置名称
 * @param mixed $default 默认值
 * @return mixed
 */
function plugin_get_params($name, $default = null)
{
    // 解析参数名称
    $parts = explode('.', $name);
    if (count($parts) < 2) {
        return $default;
    }
    
    $plugin = $parts[0];
    $key = $parts[1];
    
    // 从数据库中读取插件参数
    $params = Db::name('plugin_params')
        ->where('plugin', $plugin)
        ->where('name', $key)
        ->value('value');
    
    if ($params === null) {
        return $default;
    }
    
    // 反序列化参数
    $value = @json_decode($params, true);
    return $value === null ? $params : $value;
}

/**
 * 设置插件配置参数
 * @param string $name 配置名称
 * @param mixed $value 配置值
 * @return bool
 */
function plugin_set_params($name, $value)
{
    // 解析参数名称
    $parts = explode('.', $name);
    if (count($parts) < 2) {
        return false;
    }

    $plugin = $parts[0];
    $key = $parts[1];

    // 序列化值
    $params = is_array($value) || is_object($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;

    try {
        // 查询是否存在
        $exists = Db::name('plugin_params')
            ->where('plugin', $plugin)
            ->where('name', $key)
            ->find();

        // 更新或插入记录
        if ($exists) {
            return Db::name('plugin_params')
                ->where('plugin', $plugin)
                ->where('name', $key)
                ->update(['value' => $params, 'update_time' => time()]);
        } else {
            return Db::name('plugin_params')->insert([
                'plugin' => $plugin,
                'name' => $key,
                'value' => $params,
                'create_time' => time(),
                'update_time' => time()
            ]);
        }
    } catch (\Exception $e) {
        // 记录错误日志
        \think\facade\Log::error('插件配置保存失败: ' . $e->getMessage());
        return false;
    }
}

/**
 * 插件配置函数 - 支持读取和设置
 * @param string $name 配置名称
 * @param mixed $value 配置值（如果提供则为设置操作，否则为读取操作）
 * @param mixed $default 默认值（仅在读取时使用）
 * @return mixed
 */
function plugconf($name, $value = null, $default = null)
{
    // 如果提供了value参数，则为设置操作
    if (func_num_args() >= 2) {
        return plugin_set_params($name, $value);
    }

    // 否则为读取操作
    return plugin_get_params($name, $default);
}