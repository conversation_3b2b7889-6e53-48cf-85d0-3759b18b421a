<?php

namespace plugin\ChannelGuard\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;
use app\common\service\EmailService;
use app\common\service\SmsService;

class Api extends BasePlugin
{
    protected $scene = ['admin'];
    protected $noNeedLogin = [];

    public function index()
    {
        return View::fetch();
    }

    // 获取配置数据
    public function fetchData()
    {
        $data = [
            'monitor_status' => intval(plugconf("ChannelGuard.monitor_status") ?? 0),
            'fail_threshold' => intval(plugconf("ChannelGuard.fail_threshold") ?? 6),
            'check_interval' => intval(plugconf("ChannelGuard.check_interval") ?? 5),
            'check_time' => intval(plugconf("ChannelGuard.check_time") ?? 2),
            'notify_type' => intval(plugconf("ChannelGuard.notify_type") ?? 1),
            'notify_email' => plugconf("ChannelGuard.notify_email") ?? '',
            'notify_mobile' => plugconf("ChannelGuard.notify_mobile") ?? ''
        ];

        $this->success('success', $data);
    }

    // 保存配置
    public function saveConfig()
    {
        $status = $this->request->post('status/d', 0);
        $fail_threshold = $this->request->post('fail_threshold/d', 6);
        $check_interval = $this->request->post('check_interval/d', 5);
        $check_time = $this->request->post('check_time/d', 2);
        $notify_type = $this->request->post('notify_type/d', 1);
        $notify_email = $this->request->post('notify_email/s', '');
        $notify_mobile = $this->request->post('notify_mobile/s', '');

        // 验证参数
        if ($fail_threshold < 1 || $fail_threshold > 100) {
            $this->error('连续失败订单阈值必须在1-100之间');
        }
        if ($check_interval < 5) {
            $this->error('检查间隔不能小于5秒');
        }
        if ($check_time < 1 || $check_time > 300) {
            $this->error('检测时间必须在1-300分钟之间');
        }
        if ($notify_type === 1 && !empty($notify_email) && !filter_var($notify_email, FILTER_VALIDATE_EMAIL)) {
            $this->error('请输入有效的邮箱地址');
        }
        if ($notify_type === 2 && !empty($notify_mobile) && !preg_match('/^1[3-9]\d{9}$/', $notify_mobile)) {
            $this->error('请输入有效的手机号码');
        }

        // 保存配置
        plugconf("ChannelGuard.monitor_status", $status);
        plugconf("ChannelGuard.fail_threshold", $fail_threshold);
        plugconf("ChannelGuard.check_interval", $check_interval);
        plugconf("ChannelGuard.check_time", $check_time);
        plugconf("ChannelGuard.notify_type", $notify_type);
        plugconf("ChannelGuard.notify_email", $notify_email);
        plugconf("ChannelGuard.notify_mobile", $notify_mobile);

        $this->success('保存成功');
    }

    // 插入短信模板
    public function insertSmsTemplate()
    {
        // 检查模板是否已存在
        $exists = Db::name('sms_template')->where([
            ['name', '=', '账号切换通知']
        ])->find();
        
        if ($exists) {
            $this->error('短信模板已存在');
        }

        // 插入新模板
        $template = [
            'name' => '账号切换通知',
            'content' => '渠道账户${account}(ID:${old_id})已切换到新账号(ID:${new_id})',
            'event' => 'account_switch',
            'create_time' => time()
        ];

        $result = Db::name('sms_template')->insert($template);
        
        if ($result) {
            $this->success('短信模板添加成功');
        } else {
            $this->error('短信模板添加失败');
        }
    }
} 