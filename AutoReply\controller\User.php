<?php

namespace plugin\AutoReply\controller;

use app\common\controller\BasePlugin;
use think\facade\View;
use think\facade\Db;

class User extends BasePlugin {

    protected $scene = ['user'];

    protected $noNeedLogin = ['autoCheck','insertUserReply','sendUserAutoReplyEmail'];

    // 自动回复管理页面
    public function index() {
        return View::fetch();
    }

    // 获取商家规则
    public function getRules() {
        try {
            $merchantId = $this->user->id;
            
            // 检查站长是否允许商家使用邮件通知
            $allowMerchantEmail = intval(plugconf("AutoReply.reply_config.allow_merchant_email") ?? 1);
            
            error_log("AutoReply: 开始获取商家 {$merchantId} 的规则配置");
            
            // 获取基础配置
            $config = [
                'status' => intval(plugconf("AutoReply.merchant_{$merchantId}.status") ?? 0),
                'email_notify' => $allowMerchantEmail ? intval(plugconf("AutoReply.merchant_{$merchantId}.email_notify") ?? 0) : 0,
                'notify_email' => $allowMerchantEmail ? strval(plugconf("AutoReply.merchant_{$merchantId}.notify_email") ?? '') : '',
                'check_interval' => intval(plugconf("AutoReply.merchant_{$merchantId}.check_interval") ?? 60),
                'auto_send' => intval(plugconf("AutoReply.merchant_{$merchantId}.auto_send") ?? 0),
                'allow_merchant_email' => $allowMerchantEmail, // 添加商家邮件开关状态
                'keyword_rules' => []
            ];
            
            // 获取商家特定的规则数据
            $merchantRules = plugconf("AutoReply.merchant_{$merchantId}.keyword_rules");
            if (is_array($merchantRules) && !empty($merchantRules)) {
                $config['keyword_rules'] = array_map(function($rule) {
                    return [
                        'status' => isset($rule['status']) ? intval($rule['status']) : 1,
                        'keywords' => isset($rule['keywords']) && is_array($rule['keywords']) ? $rule['keywords'] : [],
                        'reply' => isset($rule['reply']) ? strval($rule['reply']) : ''
                    ];
                }, $merchantRules);
            } else {
                // 修复：商家没有规则时，不使用站长的默认规则，而是返回空规则
                // 这样可以避免商家回复站长设定的内容
                $config['keyword_rules'] = [];
                error_log("AutoReply: 商家 {$merchantId} 没有配置自己的规则，返回空规则避免使用站长默认规则");
            }
            
            error_log("AutoReply: 商家 {$merchantId} 获取规则成功，规则数：" . count($config['keyword_rules']));
            return json(['code' => 200, 'msg' => '获取成功', 'data' => $config]);
            
        } catch (\Exception $e) {
            error_log("AutoReply: 获取规则失败: " . $e->getMessage());
            return json(['code' => 500, 'msg' => '获取规则失败：' . $e->getMessage()]);
        }
    }

    // 保存规则
    public function saveRule() {
        try {
            $merchantId = $this->user->id;
            $data = $this->request->post();
            
            // 检查站长是否允许商家使用邮件通知
            $allowMerchantEmail = intval(plugconf("AutoReply.reply_config.allow_merchant_email") ?? 1);
            
            // 如果站长禁用了商家邮件通知，强制关闭商家的邮件通知设置
            if (!$allowMerchantEmail) {
                $data['config']['email_notify'] = 0;
                $data['config']['notify_email'] = '';
            }
            
            // 验证数据
            if (!isset($data['rules']) || !is_array($data['rules'])) {
                return json(['code' => 400, 'msg' => '规则数据格式错误']);
            }
            
            if (!isset($data['config']) || !is_array($data['config'])) {
                return json(['code' => 400, 'msg' => '配置数据格式错误']);
            }

            // 保存基础配置
            $config = [
                'status' => isset($data['config']['status']) ? intval($data['config']['status']) : 0,
                'email_notify' => $allowMerchantEmail ? intval($data['config']['email_notify']) : 0,
                'notify_email' => $allowMerchantEmail ? strval($data['config']['notify_email'] ?? '') : '',
                'check_interval' => isset($data['config']['check_interval']) ? intval($data['config']['check_interval']) : 60,
                'auto_send' => isset($data['config']['auto_send']) ? intval($data['config']['auto_send']) : 0
            ];

            // 验证检查间隔时间
            if ($config['check_interval'] < 5 || $config['check_interval'] > 3600) {
                return json(['code' => 400, 'msg' => '检查间隔时间必须在5-3600秒之间']);
            }

            // 验证邮箱格式
            if ($config['email_notify'] === 1 && !filter_var($config['notify_email'], FILTER_VALIDATE_EMAIL)) {
                return json(['code' => 400, 'msg' => '请输入有效的邮箱地址']);
            }

            // 保存所有配置
            foreach ($config as $key => $value) {
                if (!plugconf("AutoReply.merchant_{$merchantId}.{$key}", $value)) {
                    throw new \Exception("保存配置 {$key} 失败");
                }
            }

            // 处理规则数据
            $saveRules = [];
            foreach ($data['rules'] as $rule) {
                // 验证规则数据
                if (!isset($rule['reply'])) {
                    continue;
                }
                
                $reply = trim(strval($rule['reply']));
                if ($reply === '') {
                    continue;
                }
                
                // 确保关键词是数组
                $keywords = [];
                if (isset($rule['keywords']) && is_array($rule['keywords'])) {
                    $keywords = array_values(array_filter($rule['keywords'], function($keyword) {
                        return trim($keyword) !== '';
                    }));
                }
                
                $saveRules[] = [
                    'status' => isset($rule['status']) ? intval($rule['status']) : 1,
                    'keywords' => $keywords,
                    'reply' => $reply
                ];
            }

            // 保存规则
            if (!plugconf("AutoReply.merchant_{$merchantId}.keyword_rules", $saveRules)) {
                throw new \Exception('保存规则失败');
            }
            
            error_log("AutoReply: 商家 {$merchantId} 保存配置成功，状态：{$config['status']}，规则数：" . count($saveRules));
            
            // 返回更新后的完整配置
            $updatedConfig = [
                'status' => $config['status'],
                'email_notify' => $config['email_notify'],
                'notify_email' => $config['notify_email'],
                'check_interval' => $config['check_interval'],
                'auto_send' => $config['auto_send'],
                'keyword_rules' => $saveRules
            ];
            
            return json([
                'code' => 200, 
                'msg' => '保存成功',
                'data' => $updatedConfig
            ]);
        } catch (\Exception $e) {
            error_log("AutoReply: 商家 {$merchantId} 保存配置失败: " . $e->getMessage());
            return json(['code' => 500, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    // 删除规则
    public function deleteRule() {
        try {
            $merchantId = $this->user->id;
            $index = $this->request->post('index/d');
            
            // 不允许删除第一条规则
            if ($index === 0) {
                return json(['code' => 400, 'msg' => '默认规则不能删除']);
            }
            
            // 获取当前规则
            $rules = plugconf("AutoReply.merchant_{$merchantId}.keyword_rules");
            if (!is_array($rules)) {
                $rules = [];
            }
            
            // 检查索引是否有效
            if ($index < 0 || $index >= count($rules)) {
                return json(['code' => 400, 'msg' => '规则索引无效']);
            }
            
            // 删除规则
            array_splice($rules, $index, 1);
            
            // 保存更新后的规则
            if (!plugconf("AutoReply.merchant_{$merchantId}.keyword_rules", $rules)) {
                throw new \Exception('删除规则失败');
            }
            
            error_log("AutoReply: 商家 {$merchantId} 删除规则成功，剩余 " . count($rules) . " 条规则");
            return json(['code' => 200, 'msg' => '删除成功']);
        } catch (\Exception $e) {
            error_log("AutoReply: 商家 {$merchantId} 删除规则失败: " . $e->getMessage());
            return json(['code' => 500, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }

    // 测试规则
    public function testRule() {
        try {
            $merchantId = $this->user->id;
            $content = $this->request->post('content/s', '');
            $complaintId = $this->request->post('complaint_id/d', 0);
            $autoSend = $this->request->post('auto_send/b', false);
            
            if (empty($content)) {
                return json(['code' => 400, 'msg' => '请输入测试内容']);
            }
            
            // 检查商家的自动回复功能是否启用
            $merchantStatus = intval(plugconf("AutoReply.merchant_{$merchantId}.status") ?? 0);
            if ($merchantStatus !== 1) {
                return json(['code' => 200, 'msg' => '自动回复功能未启用']);
            }
            
            // 获取商家规则
            $rules = plugconf("AutoReply.merchant_{$merchantId}.keyword_rules") ?: [];
            
            foreach ($rules as $rule) {
                if (empty($rule['status'])) {
                    continue;
                }

                // 如果规则没有关键词，直接使用回复内容
                if (empty($rule['keywords'])) {
                    if ($autoSend && $complaintId) {
                        try {
                            // 插入消息记录
                            Db::name('complaint_message')->insert([
                                'complaint_id' => $complaintId,
                                'content_type' => 0,
                                'content' => $rule['reply'],
                                'identity' => 'user',
                                'create_time' => time()
                            ]);

                            return json([
                                'code' => 200,
                                'msg' => '匹配成功并已自动回复',
                                'data' => [
                                    'keyword' => '默认回复',
                                    'reply' => $rule['reply'],
                                    'sent' => true
                                ]
                            ]);
                        } catch (\Exception $e) {
                            return json(['code' => 500, 'msg' => '自动回复失败：' . $e->getMessage()]);
                        }
                    }

                    return json([
                        'code' => 200,
                        'msg' => '匹配成功',
                        'data' => [
                            'keyword' => '默认回复',
                            'reply' => $rule['reply'],
                            'sent' => false
                        ]
                    ]);
                }

                // 有关键词的规则匹配
                foreach ($rule['keywords'] as $keyword) {
                    if (stripos($content, $keyword) !== false) {
                        if ($autoSend && $complaintId) {
                            try {
                                // 插入消息记录
                                Db::name('complaint_message')->insert([
                                    'complaint_id' => $complaintId,
                                    'content_type' => 0,
                                    'content' => $rule['reply'],
                                    'identity' => 'user',
                                    'create_time' => time()
                                ]);

                                return json([
                                    'code' => 200,
                                    'msg' => '匹配成功并已自动回复',
                                    'data' => [
                                        'keyword' => $keyword,
                                        'reply' => $rule['reply'],
                                        'sent' => true
                                    ]
                                ]);
                            } catch (\Exception $e) {
                                return json(['code' => 500, 'msg' => '自动回复失败：' . $e->getMessage()]);
                            }
                        }

                        return json([
                            'code' => 200,
                            'msg' => '匹配成功',
                            'data' => [
                                'keyword' => $keyword,
                                'reply' => $rule['reply'],
                                'sent' => false
                            ]
                        ]);
                    }
                }
            }
            
            return json(['code' => 200, 'msg' => '未匹配到规则']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '测试失败：' . $e->getMessage()]);
        }
    }

    // 自动检测并回复消息
    public function autoCheck()
    {
        try {
            $userId = $this->user->id;
            error_log("AutoReply: 开始自动检查，用户ID：{$userId}");
            
            // 检查功能是否启用
            $status = intval(plugconf("AutoReply.merchant_{$userId}.status") ?? 0);
            $autoSend = intval(plugconf("AutoReply.merchant_{$userId}.auto_send") ?? 0);
            
            error_log("AutoReply: 功能状态：{$status}，自动发送：{$autoSend}");
            
            if ($status !== 1 || $autoSend !== 1) {
                return json(['code' => 200, 'msg' => '自动回复功能未启用']);
            }

            // 获取检查间隔时间
            $checkInterval = intval(plugconf("AutoReply.merchant_{$userId}.check_interval") ?? 60);
            $lastCheckTime = time() - $checkInterval;
            
            error_log("AutoReply: 检查间隔：{$checkInterval}秒，上次检查时间：" . date('Y-m-d H:i:s', $lastCheckTime));

            // 获取规则
            $rules = plugconf("AutoReply.merchant_{$userId}.keyword_rules") ?: [];
            if (empty($rules)) {
                error_log("AutoReply: 未配置规则");
                return json(['code' => 200, 'msg' => '未配置规则']);
            }

            // 查询需要处理的投诉消息
            $messages = Db::name('complaint_message')
                ->alias('cm')
                ->join('complaint c', 'c.id = cm.complaint_id')
                ->where('cm.create_time', '>', $lastCheckTime)
                ->where('cm.identity', 'buyer')
                ->where('c.user_id', $userId)  // 只查询当前用户的投诉消息
                ->field('cm.*')
                ->select()
                ->toArray();

            error_log("AutoReply: 找到 " . count($messages) . " 条新消息");

            if (empty($messages)) {
                return json(['code' => 200, 'msg' => '没有新消息']);
            }

            $repliedCount = 0;
            foreach ($messages as $message) {
                try {
                    // 检查是否已回复
                    $hasReplied = Db::name('complaint_message')
                        ->where('complaint_id', $message['complaint_id'])
                        ->where('identity', 'user')
                        ->where('create_time', '>', $lastCheckTime)
                        ->find();

                    if ($hasReplied) {
                        error_log("AutoReply: 投诉 {$message['complaint_id']} 已回复，跳过");
                        continue;
                    }

                    // 处理规则匹配
                    foreach ($rules as $rule) {
                        if (empty($rule['status'])) {
                            continue;
                        }

                        // 处理无关键词规则（默认回复）
                        if (empty($rule['keywords'])) {
                            if ($this->insertUserReply($message['complaint_id'], $rule['reply'], $userId)) {
                                $repliedCount++;
                                error_log("AutoReply: 投诉 {$message['complaint_id']} 使用默认规则回复成功");
                            }
                            break;
                        }

                        // 处理关键词匹配
                        foreach ($rule['keywords'] as $keyword) {
                            if (strpos($message['content'], $keyword) !== false) {
                                if ($this->insertUserReply($message['complaint_id'], $rule['reply'], $userId)) {
                                    $repliedCount++;
                                    error_log("AutoReply: 投诉 {$message['complaint_id']} 匹配关键词 {$keyword} 回复成功");
                                }
                                break 2;
                            }
                        }
                    }
                } catch (\Exception $e) {
                    error_log("AutoReply: 处理投诉 {$message['complaint_id']} 失败: " . $e->getMessage());
                    continue;
                }
            }

            error_log("AutoReply: 自动检查完成，成功回复 {$repliedCount} 条消息");
            return json([
                'code' => 200, 
                'msg' => "检查完成，自动回复了 {$repliedCount} 条消息",
                'data' => ['replied_count' => $repliedCount]
            ]);

        } catch (\Exception $e) {
            $errorMsg = "AutoReply: 自动检查失败: " . $e->getMessage();
            error_log($errorMsg);
            error_log($e->getTraceAsString());  // 记录完整的错误堆栈
            return json(['code' => 500, 'msg' => '自动检查失败：' . $e->getMessage()]);
        }
    }

    // 插入用户回复
    private function insertUserReply($complaintId, $content, $userId)
    {
        try {
            $currentTime = time();
            
            // 插入回复消息
            $insertResult = Db::name('complaint_message')->insert([
                'complaint_id' => $complaintId,
                'content_type' => 0,
                'content' => $content,
                'identity' => 'user',
                'create_time' => $currentTime
            ]);
            
            if ($insertResult) {
                // 处理邮件通知
                if (intval(plugconf("AutoReply.merchant_{$userId}.email_notify") ?? 0) === 1) {
                    $notifyEmail = plugconf("AutoReply.merchant_{$userId}.notify_email");
                    if ($notifyEmail) {
                        $this->sendUserAutoReplyEmail($complaintId, $content, $notifyEmail, $userId);
                    }
                }
                
                error_log("AutoReply: 用户 {$userId} 成功自动回复投诉 {$complaintId}");
                return true;
            }
        } catch (\Exception $e) {
            error_log("AutoReply: 用户 {$userId} 自动回复失败: " . $e->getMessage());
        }
        return false;
    }

    // 发送用户自动回复邮件通知
    private function sendUserAutoReplyEmail($complaintId, $content, $notifyEmail, $userId)
    {
        try {
            $lastEmailTime = cache("auto_reply_user_{$userId}_last_email_time") ?: 0;
            $currentTime = time();
            
            // 使用站长设置的邮件检查间隔
            $emailCheckInterval = intval(plugconf("AutoReply.reply_config.email_check_interval") ?? 60);
            
            if ($currentTime - $lastEmailTime < $emailCheckInterval) {
                return;
            }
            
            $service = new \app\common\service\EmailService();
            $emailContent = "系统已自动回复投诉消息：\n\n";
            $emailContent .= "回复时间：" . date('Y-m-d H:i:s', $currentTime) . "\n";
            $emailContent .= "投诉ID：{$complaintId}\n";
            $emailContent .= "回复内容：{$content}\n";
            
            $res = $service->subject('投诉自动回复通知')
                         ->message($emailContent)
                         ->to($notifyEmail)
                         ->send();
            
            if ($res) {
                cache("auto_reply_user_{$userId}_last_email_time", $currentTime);
                error_log("AutoReply: 用户 {$userId} 邮件通知发送成功");
            }
        } catch (\Exception $e) {
            error_log("AutoReply: 用户 {$userId} 邮件通知发送失败: " . $e->getMessage());
        }
    }
} 