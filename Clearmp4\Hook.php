<?php
namespace plugin\Clearmp4;

use think\facade\Db;

class Hook
{
    public function handle()
    {
        try {
            // 检查是否启用视频清理
            if (intval(plugconf("Clearmp4.video_status") ?? 0) === 1) {
                $this->clearGoodsDescriptionVideos();
            }
        } catch (\Exception $e) {
            error_log("Clearmp4: 清理任务失败: " . $e->getMessage());
        }
    }

    private function clearGoodsDescriptionVideos()
    {
        try {
            // 检查是否启用自动清理
            $status = intval(plugconf("Clearmp4.video_status") ?? 0);
            if ($status !== 1) {
                return;
            }

            // 获取配置
            $days = intval(plugconf("Clearmp4.video_days") ?? 30);
            $executeTime = plugconf("Clearmp4.video_execute_time") ?? "00:00";
            $interval = intval(plugconf("Clearmp4.video_interval") ?? 1);
            $extensions = plugconf("Clearmp4.video_extensions") ?? "mp4,avi,mov,wmv,flv,mkv,webm,m4v";

            // 验证执行时间格式
            if (!preg_match('/^([01][0-9]|2[0-3]):([0-5][0-9])$/', $executeTime)) {
                error_log("Clearmp4: 执行时间格式错误");
                return;
            }

            // 检查执行间隔
            $lastExecuteTime = intval(plugconf("Clearmp4.last_video_execute_time") ?? 0);
            $currentTime = time();

            // 如果设置了间隔且未达到间隔时间，则不执行
            if ($interval > 0 && ($currentTime - $lastExecuteTime) < ($interval * 86400)) {
                return;
            }

            // 检查是否到达执行时间
            $currentHour = date('H', $currentTime);
            $currentMinute = date('i', $currentTime);
            list($executeHour, $executeMinute) = explode(':', $executeTime);

            if ($currentHour != $executeHour || $currentMinute != $executeMinute) {
                return;
            }

            // 计算清理时间点
            $clearTime = $currentTime - ($days * 86400);

            // 获取今天的开始时间
            $todayStart = strtotime(date('Y-m-d 00:00:00', $currentTime));

            // 构建查询条件，查找商品表中包含视频的description字段
            $query = Db::name('goods')
                ->where('create_time', '<=', $clearTime)
                ->where('create_time', '<', $todayStart)
                ->whereNotNull('description')
                ->where('description', '<>', '')
                ->where('description', 'like', '%<video%');

            $records = $query->select()->toArray();

            if (empty($records)) {
                error_log("Clearmp4: 没有需要清理的商品描述视频文件");
                return;
            }

            $deletedFiles = 0;
            $totalFiles = 0;
            $failedFiles = [];
            $updatedRecords = [];

            // 处理视频文件
            foreach ($records as $record) {
                $originalDescription = $record['description'];
                $newDescription = $this->processRichTextVideos($originalDescription, $extensions, $deletedFiles, $totalFiles, $failedFiles);

                // 如果描述内容有变化，记录需要更新的记录
                if ($newDescription !== $originalDescription) {
                    $updatedRecords[] = [
                        'id' => $record['id'],
                        'description' => $newDescription
                    ];
                }
            }

            // 批量更新数据库记录
            foreach ($updatedRecords as $updateRecord) {
                Db::name('goods')
                    ->where('id', $updateRecord['id'])
                    ->update(['description' => $updateRecord['description']]);
            }

            // 更新最后执行时间
            plugconf_set("Clearmp4.last_video_execute_time", $currentTime);

            // 记录日志
            $logMessage = sprintf(
                "自动清理商品描述视频完成，共处理 %d 个视频文件，成功删除 %d 个文件，更新 %d 条商品记录",
                $totalFiles,
                $deletedFiles,
                count($updatedRecords)
            );

            if (!empty($failedFiles)) {
                $logMessage .= "\n删除失败的文件：\n" . implode("\n", $failedFiles);
            }

            error_log("Clearmp4: " . $logMessage);

        } catch (\Exception $e) {
            error_log("Clearmp4: 自动清理商品描述视频失败: " . $e->getMessage());
            throw $e;
        }
    }

    // 处理富文本中的视频内容
    private function processRichTextVideos($description, $extensions, &$deletedFiles, &$totalFiles, &$failedFiles)
    {
        // 匹配富文本编辑器中的视频标签
        $pattern = '/<div[^>]*data-w-e-type="video"[^>]*>.*?<video[^>]*>.*?<source[^>]*src="([^"]+)"[^>]*\/>.*?<\/video>.*?<\/div>/is';

        $newDescription = preg_replace_callback($pattern, function($matches) use ($extensions, &$deletedFiles, &$totalFiles, &$failedFiles) {
            $videoUrl = $matches[1];
            $totalFiles++;

            if ($this->isVideoFile($videoUrl, $extensions)) {
                // 尝试删除视频文件
                if ($this->deleteVideoFile($videoUrl, $deletedFiles, $failedFiles)) {
                    // 如果删除成功，返回空字符串（移除整个视频标签）
                    return '';
                }
            }

            // 如果删除失败或不是目标视频文件，保留原内容
            return $matches[0];
        }, $description);

        // 清理可能产生的多余空行和段落
        $newDescription = preg_replace('/<p><br><\/p>\s*<p><br><\/p>/', '<p><br></p>', $newDescription);
        $newDescription = preg_replace('/(<p><br><\/p>\s*){2,}/', '<p><br></p>', $newDescription);

        return $newDescription;
    }

    // 检查是否为视频文件
    private function isVideoFile($filePath, $extensions)
    {
        $extArray = explode(',', $extensions);
        $fileExt = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        
        foreach ($extArray as $ext) {
            if (trim(strtolower($ext)) === $fileExt) {
                return true;
            }
        }
        
        return false;
    }

    // 删除视频文件
    private function deleteVideoFile($videoPath, &$deletedFiles, &$failedFiles)
    {
        try {
            // 从URL中提取相对路径
            $relativePath = parse_url($videoPath, PHP_URL_PATH);
            if (empty($relativePath)) {
                $failedFiles[] = $videoPath;
                error_log("Clearmp4: 无效的视频URL: " . $videoPath);
                return false;
            }

            $filePath = public_path() . ltrim($relativePath, '/');

            // 确保路径是文件而不是目录
            if (is_dir($filePath)) {
                $failedFiles[] = $videoPath;
                error_log("Clearmp4: 路径指向目录而不是文件: " . $filePath);
                return false;
            }

            if (file_exists($filePath) && is_file($filePath)) {
                if (@unlink($filePath)) {
                    $deletedFiles++;
                    error_log("Clearmp4: 成功删除视频文件: " . $filePath);

                    // 尝试删除空文件夹
                    $dirPath = dirname($filePath);
                    if (is_dir($dirPath) && count(scandir($dirPath)) <= 2) {
                        @rmdir($dirPath);
                        error_log("Clearmp4: 删除空文件夹: " . $dirPath);
                    }
                    return true;
                } else {
                    $failedFiles[] = $videoPath;
                    error_log("Clearmp4: 删除视频文件失败: " . $filePath);
                    return false;
                }
            } else {
                $failedFiles[] = $videoPath;
                error_log("Clearmp4: 视频文件不存在或不是有效文件: " . $filePath);
                return false;
            }
        } catch (\Exception $e) {
            $failedFiles[] = $videoPath;
            error_log("Clearmp4: 删除视频文件时发生错误: " . $e->getMessage());
            return false;
        }
    }
}
